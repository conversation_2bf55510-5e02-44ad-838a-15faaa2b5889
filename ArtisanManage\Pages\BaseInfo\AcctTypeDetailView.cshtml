﻿@page
@model ArtisanManage.Pages.BaseInfo.AcctTypeDetailViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        var frame = "AcctTypeDetailEdit";

        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var RowIndex = -1;

        window.addEventListener('message', function (rs) {
            $("#popItem").jqxWindow('close');
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "AcctTypeDetailEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()

                    }
                    else {
                        var row = rs.data.record;
                        var rows = window.gridData_gridItems.localRows;
                    
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }

                        rows[0] = row;
                        rows[0].i = row.acct_way_id
                        debugger
                        rows[0].acct_type = row.acct_type_name
                    
                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                   
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "AcctTypeDetail_name", rs.data.record.AcctTypeDetail_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "AcctTypeDetail_note", rs.data.record.AcctTypeDetail_note);
                }
                $("#popItem").jqxWindow('close');
            };

        });
        var m_db_id = "10";

        var newCount = 1;
        /*
        function renderStockQty(row, stock_qty, son_stock_qty) {
            var stockLabel = `<label style="cursor:pointer;margin-left:4px;color:#000;margin-right:2px">${stock_qty}</label>`
            var sonStockLabel = ''
            if (son_stock_qty) {
                sonStockLabel = `<label style="cursor:pointer;margin-left:4px;color:#777;font-size:6px;margin-right:2px">${son_stock_qty}</label>`
            }
            debugger
            return `<div onclick='onGridRowEdit(${row})' style="height:100%;display:flex;align-items:center;justify-content:flex-start;" >${stockLabel}${sonStockLabel}</div>`

        }
        function AcctTypeRenderer(row, column, value, p4, p5, rowData) {
            return renderStockQty(row, rowData.stock_qty_unit, rowData.son_stock_qty)
        }
        */
        function btnAddItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="${frame}?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

        }
        function onGridRowEdit(rowIndex) {
            var acct_way_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'i');
            $('#popItem').jqxWindow('open');

            $("#popItem").jqxWindow('setContent', '<iframe src="AcctTypeDetailEdit?operKey=' + g_operKey + '&acct_way_id=' + acct_way_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }
        var itemSource = {};
        $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                    console.log(args);
                    if (args.datafield == "acct_way_name") {
                        if (args.originalEvent.button == 2) return;
                        var acct_way_id = args.row.bounddata.i;
                        debugger
                        RowIndex = args.rowindex;
                        debugger
                        if (ForSelect) {
                            var acct_way_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                            var acct_way_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "acct_way_name");
                            var msg = {
                                msgHead: 'AcctTypeDetailView', action: 'select', acct_way_id: acct_way_id, acct_way_name: acct_way_name
                            };
                            window.parent.postMessage(msg, '*');
                        }
                        else {
                            onGridRowEdit(args.rowindex);
                        }
                    }
                });
            $("#Cancel").on('click', function () {
                for (var i = 0; i < 10; i++) {
                    $('#jqxgrid').jqxGrid('deleterow', i);
                    $('#jqxgrid').jqxGrid('addrow', i, {})
                }
            });

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });
            QueryData();
        });
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
    </style>
</head>


<body>

    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;">
        <div><input id="searchString" class="margin" placeholder="请输入简拼/名称" /><button onclick="QueryData()" class="margin">查询</button></div>
        <div><button onclick="btnAddItem_click()" class="margin">新增明细</button></div>
    </div>

    <div id="gridItems" style="margin-top:10px;width:calc(100% - 20px);height:100%;margin-bottom:10px; "></div>


    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">结算明细</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>