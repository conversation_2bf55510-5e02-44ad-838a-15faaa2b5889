@page
@model ArtisanManage.Pages.BaseInfo.ClientArrearsModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head> 
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>

    <style>
        #div_arrearsAge{
            padding-left:20px;
        }
        #div_arrearsAge label:first-child{
            width:100px;
        }
    </style>

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
           
    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                    var supcust_id = args.row.bounddata.supcust_id;
                    var sup_name = args.row.bounddata.sup_name;
                    var seller_id = $('#seller_id').val().value;
                    var seller_name = $('#seller_id').val().label;
                    var senders_id = $('#senders_id').val().value;
                    var senders_name = $('#senders_id').val().label;
                    var startDay = $('#startDay').jqxDateTimeInput('val');
                    var endDay = $('#endDay').jqxDateTimeInput('val');
                    var arrears_order_status_l = $("#arrears_order_status").val().label
                    var arrears_order_status_v = $("#arrears_order_status").val().value
                    if (args.datafield == "sup_name") {
                        var url = `Report/AccountHistory?supcust_id=${supcust_id}&sup_name=${encodeURIComponent(sup_name)}&startDay=${startDay}&endDay=${endDay}`;
                        if (seller_name) url += `&seller_id=${seller_id}&seller_name=${seller_name}`;
                        window.parent.newTabPage('客户往来账', url);
                    }
                    if (args.datafield == "balance") {//总欠款：跳转收款单不带时间
                        var url = `Sheets/GetArrearsSheet?supcust_id=${supcust_id}&sup_name=${encodeURIComponent(sup_name)}`;
                        if (seller_name) url += `&seller_id=${seller_id}&seller_name=${seller_name}`;
                        if (senders_name) url += `&senders_id=${senders_id}&senders_name=${senders_name}`;
                        window.parent.newTabPage('收款单', url);
                    }
                    if (args.datafield == "left_amount") {//(期间)尚欠：跳转收款单带时间
                        var url = `Sheets/GetArrearsSheet?supcust_id=${supcust_id}&sup_name=${encodeURIComponent(sup_name)}&startDay=${startDay}&endDay=${endDay}`;
                        if (seller_name) url += `&seller_id=${seller_id}&seller_name=${seller_name}`;
                        if (senders_name) url += `&senders_id=${senders_id}&senders_name=${senders_name}`;
                        window.parent.newTabPage('收款单', url);
                    }
                    if ( args.datafield === "qk_add") {//期间欠款
                        var sub_type = "QK"                        
                        var url = `Report/ClientArrearsDetail?operKey=${g_operKey}&supcust_id=${supcust_id}&sup_name=${encodeURIComponent(sup_name)}&sub_type=${sub_type}&startDay=${startDay}&endDay=${endDay}`;
                        if (seller_name) url += `&seller_id=${seller_id}&seller_name=${seller_name}`;
                        if (senders_name) url += `&senders_id=${senders_id}&senders_name=${senders_name}`;
                        if (arrears_order_status_l) url += `&arrears_order_status_v=${arrears_order_status_l}&arrears_order_status=${arrears_order_status_v}`
                        window.parent.newTabPage('应收款明细', url);
                    }
                    if (args.datafield == "sk_add") {
                        var url = `Report/ArrearsSheetDetail?supcust_id=${supcust_id}&sup_name=${encodeURIComponent(sup_name)}&startDay=${endDay}&mm_startDay=${startDay}&mm_endDay=${endDay}`;
                        if (seller_name) url += `&seller_id=${seller_id}&seller_name=${seller_name}`;
                        if (senders_name) url += `&senders_id=${senders_id}&senders_name=${senders_name}`;
                        window.parent.newTabPage('收款单明细', url);
                    }
                });               
                QueryData();
                $('#supcust_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popClient').jqxWindow('open');
                        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
                $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });



    	 });
         window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                    //$.ajax({
                    //    url: '/api/SaleSheet/GetItemInfo',
                    //    type: 'GET',
                    //    contentType: 'application/json',
                    //    data: { operKey: g_operKey, item_id: null },
                    //    success: function(data) {
                    //        if (data.result === 'OK') {
                    //            if (!window.g_queriedItems) window.g_queriedItems = {};
                    //            window.g_queriedItems[item_id] = data.item;
                    //        }
                    //    }
                    //});

                }
                $('#popClient').jqxWindow('close');
            }
            

        });   

    </script>
</head>

<body>
  
    <div style="display:flex;margin-top:20px;align-items:center;">
         
        <div id="divHead" class="headtail" style="width: calc(100% - 110px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()">查询</button>
        <button id="btnExport" onclick="ExportExcel()">导出</button>
    </div>
    
     <div id="gridItems"></div>  
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div> 
        
    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
   

</body>
</html>