@page
@model ArtisanManage.Pages.BaseInfo.SalesSummaryBySenderModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var m_db_id = "10";

    	    var newCount = 1;


    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
                $("#popoverQuery").jqxPopover({ showArrow: false, autoClose: true, offset: { left: 0, top: -10 }, position: "bottom", title: "", showCloseButton: false, selector: $("#btnQueryChooseSheets") });
             //   $("#gridItems").on("cellclick", function (event) {
               //     var args = event.args;
                //});
                $("#gridItems").on("cellclick", function (event) {

                    var args = event.args;
                    var oper_id = args.row.bounddata.oper_id;
                    var oper_name = args.row.bounddata.oper_name;
                    var startDay = $('#startDay').jqxDateTimeInput('val');
                    var endDay = $('#endDay').jqxDateTimeInput('val');
                    var item_id = $('#item_id').val().value;
                    var item_name = $('#item_id').val().label;
                     // var item_id = args.row.bounddata.item_id;
                     // var item_name = args.row.bounddata.item_name;
                    var seller_id = $('#seller_id').val().value;
                    var seller_name = $('#seller_id').val().label;
                    var brand_id = $('#brand_id').val().value;
                    var brand_name = $('#brand_id').val().label;
                    var brand_name = $('#brand_id').val().label;

                    var sheetType = $('#sheetType').val();

                    var title = '销售明细表';
                    if (sheetType=="xd") title = '订单明细表'

                //var url = `Report/SalesDetail?sheetType=${sheetType}&senders_id=${oper_id}&sm.senders_name=${oper_name}&startDay=${startDay}&endDay=${endDay}&item_id=${item_id}&item_name=${(item_name)}`;
               // var url = `Report/SalesDetail?sheetType=${sheetType}&senders_id=${oper_id}&sm.senders_name=${oper_name}&startDay=${startDay}&endDay=${endDay}`;
                    if (args.datafield == "oper_name" && oper_name) {
                    window.queryItems = funGetQueryValues();
                    window.queryItems['senders_id'] = oper_id;
                    window.queryItems['sm.senders_name'] = oper_name;
                    var queryItemStr = queryItemsToString(window.queryItems)
                    window.parent.newTabPage(title, 'Report/SalesDetail' + queryItemStr, window);
                    }


                });

                //$("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                QueryData();
            });
           // TODO HTML 
        function btnQuerySaleSummaryBySeller_click() {
            debugger
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（业务员）", 'Report/SalesSummaryBySeller' + queryItemStr, window);
            var len = $('#jqxTabs').jqxTabs('length');
            var content = $('#jqxTabs').jqxTabs('getContentAt', len - 1);
            var frame = content.childNodes[0];
            var w = frame.contentWindow;
            w.g_bRelatedReport_sale = true
            window.g_bRelatedReport_sale = true

        }
        function btnQuerySaleSummaryByClient_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（客户）", 'Report/SalesSummaryByClient' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByItem_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（商品）", 'Report/SalesSummaryByItem' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByRegion_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（片区）", 'Report/SalesSummaryByRegion' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByGroup_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（渠道）", 'Report/SalesSummaryByGroup' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByBrand_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（品牌）", 'Report/SalesSummaryByBrand' + queryItemStr, window);
        }
        function btnQuerySaleDetail_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售明细表", 'Report/SalesDetail' + queryItemStr, window);
        }
        function queryItemsToString(queryItems) {
            debugger
            var query = "?"
            for (var i in queryItems) {
                if (i == 'operKey') {
                    continue;
                }
                query += "&"
                query += i
                query += '='
                query += queryItems[i]
            }

            return query;
        }

    </script>
</head>

<body style="overflow:hidden">
    <style>
        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }
    </style>
    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;min-width:42px;margin-right:0px;border-radius: 3px 0px 0px 3px">查询</button>

        <button id="btnQueryChooseSheets" class="btnright" style="width:30px;margin-right:20px;margin-left:0px;border-radius: 0px 3px 3px 0px">
            <img src="~/PrintTemplate/img/triangle.svg" style="margin-top: -1px; width: 14px; display: inline-block;vertical-align: middle;" />
        </button>
        
        <div id="popoverQuery" style="position:absolute;display:none;border-radius: 5px 5px 5px 5px">
            <div style="width:150px;height:200px;display:flex;flex-direction:column;justify-content:space-between;align-items:center;">
                <ul style="line-height: 26px;font-size:15px;padding:0px;">
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a id="btnshow" value="Show" onclick="btnQuerySaleSummaryBySeller_click();">
                            销售汇总（业务员）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByClient_click();">
                            销售汇总（客户）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByItem_click();">
                            销售汇总（商品）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByRegion_click();">
                            销售汇总（片区）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByGroup_click();">
                            销售汇总（渠道）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByBrand_click();">
                            销售汇总（品牌）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleDetail_click();">
                            销售明细表
                        </a>
                    </li>
                </ul>
                <div id="divClientVersion"></div>
            </div>
        </div>

        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;min-width:42px;">导出</button>
    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>