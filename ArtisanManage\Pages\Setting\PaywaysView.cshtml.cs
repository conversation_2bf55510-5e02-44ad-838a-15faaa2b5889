﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using MathNet.Numerics.LinearAlgebra.Factorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore.Internal;
using Newtonsoft.Json;
using NPOI.POIFS.Crypt.Dsig;
using NPOI.SS.Formula.Functions;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ArtisanManage.Pages.Setting
{
    public class PaywaysViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
        public bool HasCheck = false;

        public PaywaysViewModel(CMySbCommand cmd) : base(Services.MenuId.paywaysView)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                 {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称/助记码",UseJQWidgets=false, SqlFld="sub_name,py_str",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
                 {"status",new DataItem(){Title = "状态",LabelFld="status_name", LabelInDB = false, Value = "normal", Label = "正常",Width="50", ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "normal",
                     Source = @"[{v:'normal',l:'正常',condition:""(status = '1' or status is null)""},
                               {v:'stop',l:'停用',condition:""status = '0' ""},
                               {v:'all',l:'所有',condition:""true""}]"
                 }},
                 {"other_sub",new DataItem(){Title="父科目", LikeWrapper="/", CtrlType="jqxTree",MumSelectable=true,GetOptionsOnLoad=true,FirstOptionAsDefault=true, QueryOnChange=true,CompareOperator="like", MaxRecords="800",
                   SqlForOptions="select sub_id as v,sub_name as l,py_str as z,mother_id as pv from cw_subject where company_id= ~COMPANY_ID and coalesce(status,'1')='1' order by cast(sub_code as text)"}},
            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     IdColumn="i",TableName="cw_subject",
                     ShowContextMenu=true,
                     //HasCheck=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"i",new DataItem(){Title="编号",SqlFld="sub_id",Width="80",Hidden=true,HideOnLoad = true}},
                       {"sub_name",new DataItem(){Title="名称",Width="150",Linkable=true}},
                       {"sub_code",new DataItem(){Title="科目代码",Width="150"}},
                       {"sub_direction",new DataItem(){Title="借贷",Width="50",SqlFld="(case when direction=1 then '借' when direction=-1 then '贷' end)"}},
                       {"sub_type",new DataItem(){Title="类型",Width="80",SqlFld = "(case when sub_type = 'QT' then '现金银行' when sub_type = 'YS' then '预收' when sub_type = 'YF' then '预付' when sub_type = 'ZC' THEN '费用支出' when sub_type = 'QTSR' then '其他收入' when sub_type = 'JK' then '借款' end)"}},
                       {"is_order", new DataItem(){ Title="定货会账户", Width="120", SqlFld="(case when coalesce(is_order,false)=true then '是' else '' end)"} },
                       {"assister_types",new DataItem(){Title="辅助核算",Width="100",SqlFld="replace(replace(replace(replace(replace(assister_types,'C','客户'),'S','供应商'),'INV','商品'),'DEP','部门'),'MAN','业务员')"}},
                       {"order_index",new DataItem(){Title="顺序号",Width="80"}},
                       {"status",new DataItem(){Title="状态",Width="50",SqlFld="(case WHEN status='0' THEN '停用' ELSE '正常' END)" }}
                     },
                     QueryFromSQL=@"from cw_subject 
                                    where company_id=~COMPANY_ID " ,
                     QueryOrderSQL="order by order_index,cast(sub_code as text)"
                  }
                } 
            }; 
        }
        public async Task OnGet(string forSelect,string hasCheck)
        {
            if (hasCheck =="1")
            {
                Grids["gridItems"].HasCheck = true;
                HasCheck = true;
            }
         
            await InitGet(cmd);
            ForSelect = forSelect == "1";

            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }

    }



    [Route("api/[controller]/[action]")]
    public class PaywaysViewController : YjController
    {
        CMySbCommand cmd;
        public PaywaysViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            PaywaysViewModel model = new PaywaysViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            PaywaysViewModel model = new PaywaysViewModel(cmd);
            if (Request != null)
            {
                model.DataItems["other_sub"].SqlForOptions = $"select sub_id as v,sub_name as l,py_str as z,mother_id as pv from cw_subject where company_id= ~COMPANY_ID {(Request.Query["status_name"][0] == "所有" ? "" : (Request.Query["status_name"][0] == "正常" ? "and coalesce(status,'1')='1'" : "and coalesce(status,'1')='0'"))} order by cast(sub_code as text)";
            }
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            PaywaysViewModel model = new PaywaysViewModel(cmd);
            string sub_id = data.rowIDs;
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            string result = "OK";
            string msg = "";
            string sql= "";

            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($@"select coalesce(setting ->> 'useAccounting','false') as useaccounting from company_setting where company_id={companyID}", cmd);
            bool openAccount = false;
            if (setting != null) openAccount = Convert.ToBoolean(setting.useaccounting);

            msg = await CheckBeforeDeleteRecords(companyID, sub_id, openAccount);
            if (msg != "")
            {
                await CwLog.Save(companyID, operID, null, "CwSubject", $"Error: {msg}; delete subject, sub_id[{sub_id}]", cmd);
                return new JsonResult(new { result = "Error", msg });
            }

            dynamic  subChildren = await CDbDealer.GetRecordsFromSQLAsync($@"select sub_id,sub_code,sub_type,mother_id from cw_subject where company_id={companyID} and mother_id=(select mother_id from cw_subject where company_id={companyID} and sub_id={sub_id})", cmd);
              
            sql = $"delete from cw_subject where company_id = {companyID} and sub_id = {sub_id};" +
                         $"delete from info_pay_way where company_id = {companyID} and sub_id = {sub_id};";
            
            if (subChildren.Count == 1)//唯一的子被删除时，父还原子的sub_type
            {
                dynamic subChild = subChildren[0];
				if (subChild.sub_type != "") sql += $@"update cw_subject set sub_type='{subChild.sub_type}' where company_id={companyID} and sub_id={subChild.mother_id};";
            }
            if (openAccount)
            {
                sql += $"delete from cw_op_sub_init_detail where company_id = {companyID} and sub_id = {sub_id};";
                sql += $"delete from cw_sub_balance where company_id = {companyID} and sub_id = {sub_id};";
                sql += $"delete from cw_op_sub_init_assister_detail where company_id={companyID} and sub_id={sub_id};";//辅助项能删则说明有多余的行只能是空数据
                sql += $"delete from cw_sub_balance_assister where company_id={companyID} and sub_id={sub_id};";//每一期都删除
            }
            cmd.CommandText = sql;
            cmd.company_id = companyID;//下一行报错：company_id property of CMySbCommand should be set     为什么执行sql前cmd要手动加company_id?
            await cmd.ExecuteNonQueryAsync();

            await CwLog.Save(companyID, operID, null, "CwSubject", $"OK; delete subject, sub_id[{sub_id}]", cmd);
            return Json(new { result, msg, sub_id });
        }

        public async Task<string> CheckBeforeDeleteRecords(string companyID, string rowIDs, bool openAccount)
        {
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("subChild", $"select sub_code from cw_subject where mother_id in ({rowIDs}) and company_id = {companyID}");
            QQ.Enqueue("check1", $@"
                (select qrcode_id as sheet_id , '现金银行账户' as sheet_type from info_pay_qrcode where company_id={companyID} and sub_id in ({rowIDs}))
                union all
                ( select sheet_id, '销售单' as sheet_type from sheet_sale_main where (payway1_id in ({rowIDs}) or payway2_id in ({rowIDs}) or payway3_id in ({rowIDs})) and company_id={companyID} )
                union all
                ( select sheet_id, '采购单' as sheet_type from sheet_buy_main where (payway1_id in ({rowIDs}) or payway2_id in ({rowIDs}) ) and company_id={companyID} )
                union all
                ( select sheet_id, (case when sheet_type='SK' then '收款单' else '付款单' end) as sheet_type from sheet_get_arrears_main where (payway1_id in ({rowIDs})  or payway2_id in ({rowIDs})) and company_id={companyID} )
                union all
                ( select sheet_id, (case when sheet_type='YS' then '预收款单' when sheet_type='YF' then '预付款单' else '定货会' end) as sheet_type from sheet_prepay where prepay_sub_id in ({rowIDs}) and company_id={companyID} )
                union all
                ( select sub_id as sheet_id, '预收款/预付款余额' as sheet_type from prepay_balance where sub_id in ({rowIDs}) and company_id={companyID} )
                union all
                ( select sheet_id, '定货调整单' as sheet_type from sheet_item_ordered_adjust_main where (prepay_sub_id in ({rowIDs}) or payway1_id in ({rowIDs}) or payway2_id in ({rowIDs})) and company_id={companyID} )
                union all
                ( select prepay_sub_id as sheet_id, '定货会余额' as sheet_type from items_ordered_balance where prepay_sub_id in ({rowIDs}) and company_id={companyID} )
                union all
                ( select sheet_id, (case when sheet_type='ZC' then '费用支出单' else '其他收入单' end) as sheet_type from sheet_fee_out_main where (payway1_id in ({rowIDs}) or payway2_id in ({rowIDs})) and company_id={companyID} )
                union all
                ( select sheet_id, '转账单' as sheet_type from sheet_cashbank_transfer_detail where (money_out_id in ({rowIDs}) or money_in_id in ({rowIDs})) and company_id={companyID} )
                union all
                ( select d.sheet_id, (case when m.sheet_type='ZC' then '费用支出单' else '其他收入单' end) as sheet_type from sheet_fee_out_detail d left join sheet_fee_out_main m on d.company_id=m.company_id and d.sheet_id=m.sheet_id where d.fee_sub_id in ({rowIDs}) and d.company_id={companyID} )
                union all
                ( select sheet_id, '陈列协议' as sheet_type from display_agreement_main where fee_sub_id in ({rowIDs}) and company_id = {companyID} )
                union all
                ( select sheet_id, '会计凭证' as sheet_type from cw_voucher_detail where sub_id in ({rowIDs}) and company_id = {companyID} )
                union all
                ( select sheet_id, '科目期初' as sheet_type from cw_op_sub_init_detail where balance is not null and balance<>0 and sub_id in ({rowIDs}) and company_id={companyID} )
                union all
                ( select brief_id as sheet_id, '备注信息' as sheet_type from info_sheet_detail_brief where company_id={companyID} and sub_id in ({rowIDs}) )
                union all 
                ( select sheet_id, '辅助核算期初' as sheet_type from cw_op_sub_init_assister_detail where company_id={companyID} and sub_id in ({rowIDs}) and coalesce(balance,0)<>0 )");
            QQ.Enqueue("check2", $@"select sub_id as sheet_id from cw_subject where company_id = {companyID} and sub_id in ({rowIDs}) and sub_code in (1122,112301,112302,2202,220301,220302,1405,1901,3103,310406,5001,5051,5401,560212,560213,560304,560302)");
            QQ.Enqueue("sub", $"select * from cw_subject where sub_id = {rowIDs} and company_id = {companyID}");
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            dynamic subChild = null;
            dynamic check1 = null;
            dynamic check2 = null;
            dynamic sub = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "subChild") subChild = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "check1") check1 = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "check2") check2 = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "sub") sub = CDbDealer.Get1RecordFromDr(dr, false);
            }
            QQ.Clear();

            if (subChild != null)
            {
                return "请先删除该科目类别的子科目类别";
            }
            if (check1 != null)
            {
                return $"该科目已被【{check1.sheet_type}】使用过, 无法删除";
            }

            if (openAccount)
            {
                if (check2 != null)
                {
                    return "业务关联科目无法删除";
                }
            }
            else
            {
                if(Array.IndexOf(new string[] { "0","10","1001","1002","100201","100202","100203","1123","22","2203","220301","60","6051","605101","66","6601","6602" }, sub.sub_code.ToString()) > -1)
                {
                    return "初始科目无法删除";
                }
            }

            return "";
        }
        /*
        [HttpPost]
        public async Task<IActionResult> RemoveMumSub([FromBody] dynamic value)
        {
            PaywayEditModel model = new PaywayEditModel(cmd);
            string id = value[model.m_idFld];
            string result = "OK";
            if (id == "")
            {
                result = "请传入科目编号";
                goto end;
            }
            CDbDealer db = new CDbDealer();

            object o = null;

            cmd.CommandText = $"select sub_code from cw_subject where mother_id='{id}'";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该科目类别的子科目类别后再删除该科目类别"; goto end;
            }
            //cmd.CommandText = $"select sub_code from cw_subject where sub_id='{id}'";
            //o = cmd.ExecuteScalar();
            //if (o != null && o != DBNull.Value)
            //{
            //    result = "请删除该科目类别的科目名称后再删除该科目类别"; goto end;
            //}
            string sql = $"delete from cw_subject where sub_id='{id}'";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();


        //  var tt = Convert.ToString(value.uid); 
        //var rr = new { UserID = value.UserID, UserName = value.UserName };
        //return value;
        end:
            return Json(new { result, sub_id = id });
            //return JsonObject<object> (new { UserID = value.UserID, UserName = value.UserName });
        }*/

        [HttpPost]
        public async Task<IActionResult> CheckSubChild([FromBody] dynamic jsonData)
        {
            string result = "OK";
            string msg = "";
            dynamic data = JsonConvert.DeserializeObject(jsonData.ToString());
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);

            //获取mother信息
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("subMother", $@"select sub_id,sub_code,sub_name,direction,level,sub_type,assister_types from cw_subject where company_id={companyID} and sub_id={data.sub_id}");
            QQ.Enqueue("check_use", $@"select * from (
                    (select qrcode_id as id , '现金银行账户' as type from info_pay_qrcode where company_id={companyID} and sub_id={data.sub_id})
                    union all 
                    (select sheet_id as id, (case when sheet_type='X' then '销售单' else '退货单' end) as type from sheet_sale_main where company_id={companyID} and coalesce(is_imported,false)=false and red_flag is null and (payway1_id={data.sub_id} or payway2_id={data.sub_id} or payway3_id={data.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, (case when sheet_type='CG' then '采购单' else '采购退货单' end) as type from sheet_buy_main where company_id={companyID} and coalesce(is_imported,false)=false and red_flag is null and (payway1_id={data.sub_id} or payway2_id={data.sub_id} or payway3_id={data.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, (case when sheet_type='SK' then '收款单' else '付款单' end) as type from sheet_get_arrears_main where company_id={companyID} and coalesce(is_imported,false)=false and red_flag is null and (payway1_id={data.sub_id}  or payway2_id={data.sub_id} or payway3_id={data.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, (case when sheet_type='YS' then '预收款单' when sheet_type='YF' then '预付款单' else '定货单' end) from sheet_prepay where company_id={companyID} and coalesce(is_imported,false)=false and red_flag is null and (prepay_sub_id={data.sub_id} or payway1_id={data.sub_id} or payway2_id={data.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, (case when sheet_type='ZC' then '费用支出单' else '其他收入单' end) as type from sheet_fee_out_main where company_id={companyID} and sheet_attribute->>'imported' is null and red_flag is null and (payway1_id={data.sub_id} or payway2_id={data.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, '定货会调整单' as type from sheet_item_ordered_adjust_main where company_id={companyID} and (prepay_sub_id={data.sub_id} or payway1_id={data.sub_id} or payway2_id={data.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, '转账单' as type from sheet_cashbank_transfer_detail where company_id={companyID} and (money_out_id={data.sub_id} or money_in_id={data.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, '陈列协议' as type from display_agreement_main where company_id = {companyID} and fee_sub_id={data.sub_id}  limit 1)
                    union all 
                    (select brief_id as id, '备注信息' as type from info_sheet_detail_brief where company_id={companyID} and sub_id={data.sub_id})
                    ) t limit 1");
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            dynamic subMother = null;
            dynamic check_use = null;
            while (QQ.Count > 0)
            {
                string sqlName= QQ.Dequeue();
                if (sqlName == "subMother") subMother = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "check_use") check_use = CDbDealer.Get1RecordFromDr(dr, false);
            }
            QQ.Clear();

            if (Array.IndexOf(new Int64[] { 560213, 560304, 220301, 112301 }, Convert.ToInt32(subMother.sub_code)) > -1)//如果允许改code_length，后期要改
            {
                return new JsonResult(new { result, msg = $"模板科目【{subMother.sub_name}】不允许增加子科目", needConfirm = false, subMother });
            }
            if (Array.IndexOf(new Int64[] { 1122, 2202,  5001, 5401, 1405 }, Convert.ToInt32(subMother.sub_code)) > -1)//如果允许改code_length，后期要改
            {
                return new JsonResult(new { result, msg=$"模板科目【{subMother.sub_name}】不允许增加子科目，请查询辅助核算", needConfirm = false, subMother });
            }
            if (subMother.level == "")
            {
                subMother.level = 0;
            }

            //检查是否被业务单据使用过
            if (check_use != null)
            {
                return new JsonResult(new { result, msg = $"该科目已被【{check_use.type}】使用过，无法添加子科目", needConfirm = false, subMother });
            }

            //检查待新增科目是否是第一个子科目
            dynamic subChild = await CDbDealer.GetRecordsFromSQLAsync($@"select sub_id,sub_code from cw_subject where company_id={companyID} and mother_id={subMother.sub_id}", cmd);
            QQ.Clear();
            if (subChild.Count > 0)//已有子科目
            {
                return new JsonResult(new { result, msg, needConfirm = false, subMother });
            }

            return await CheckFirstChild(companyID, null, subMother, cmd);
        }

        /// <param name="sub">如果是添加科目框弹出前的判断：null 没有科目信息；如果是保存时的再次判断：前端传入的待保存信息</param>
        public static async Task<JsonResult> CheckFirstChild(string companyID, dynamic sub, dynamic subMother, CMySbCommand cmd)
        {
            string result = "OK";
            string msg = "";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("cw_op_sub_init_detail", $@"select sub_id,balance from cw_op_sub_init_detail where company_id={companyID} and sub_id={subMother.sub_id}");
            QQ.Enqueue("cw_voucher_detail", $@"select sub_id,change_amount from cw_voucher_detail where company_id={companyID} and sub_id={subMother.sub_id}");
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            dynamic cw_op_sub_init_detail = null;
            dynamic cw_voucher_detail = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "cw_op_sub_init_detail") cw_op_sub_init_detail = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "cw_voucher_detail") cw_voucher_detail = CDbDealer.GetRecordsFromDr(dr, false);
            }
            QQ.Clear();

            //检查mother是否是属于期末结转模板的3103本年利润科目
            int[] arrProfitSubcode = { 3103, 310301, 31030101, 31030101 };//code_length=5是不可能作为mother的（后期允许改code_length要改代码）
            if (Array.IndexOf(arrProfitSubcode,Int32.Parse(subMother.sub_id))>-1)
            {
                return new JsonResult(new { result, msg, needConfirm = true, subMother });
            }

            //后期新增其他自由模板，这里还要添加判断

            //检查mother在init是否有余额
            if (cw_op_sub_init_detail == null)//大类
            {
                return new JsonResult(new { result, msg, needConfirm = false, subMother });
            }

            decimal.TryParse(cw_op_sub_init_detail.balance.ToString(), out decimal init_balance);
            //如果mother没有辅助，科目本身要加辅助，检查mother有没有期初或凭证，没有才能添加
            if ((init_balance > 0 || cw_voucher_detail.Count > 0) && sub!=null && sub.assister_types!="")
            {
                return new JsonResult(new { result="Error", msg="父级科目存在余额且没有辅助核算，无法为新增子科目添加辅助核算" });
            }
            if (init_balance > 0)
            {
                return new JsonResult(new { result, msg, needConfirm = true, subMother });
            }

            //检查mother是否有凭证，包括红冲凭证
            if (cw_voucher_detail.Count > 0)
            {
                return new JsonResult(new { result, msg, needConfirm = true, subMother });
            }

            return new JsonResult(new { result, msg, needConfirm = false, subMother });
        }

        [HttpPost]
        public async Task<JsonResult> CheckSubChildForSelect([FromBody] dynamic jsonData)
        {
            string result = "OK";
            string msg = "";
            dynamic data = JsonConvert.DeserializeObject(jsonData.ToString());
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);

            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("cs2", $@"select sub_id from cw_subject where company_id={companyID} and mother_id={data.sub_id}");
            QQ.Enqueue("cs1", $@"select sub_id,sub_code,substr(sub_code::text,1,4) as sub_code1,status,coalesce(sub_type,'') as sub_type,
                substring((select string_agg(sub_name,'-' order by sub_code::text) as name from cw_subject where company_id={companyID} 
                and sub_id::text in ( select * from REGEXP_SPLIT_TO_TABLE((select substring(concat(other_sub,sub_id),2) from cw_subject where company_id={companyID} and level>=1 and sub_id=cw1.sub_id), '/')  ) ),8) as name 
                from cw_subject cw1 where company_id={companyID} and sub_id={data.sub_id}");
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            dynamic cs2 = null;
            dynamic cs1 = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if(sqlName=="cs2") cs2=CDbDealer.GetRecordsFromDr(dr, false);
                else if (sqlName == "cs1") cs1 = CDbDealer.Get1RecordFromDr(dr, false);
            }
            QQ.Clear();

            if (cs2.Count > 0)
            {
                return Json(new { result, msg = "请选择明细" });
            }
            if (cs1.status == null || cs1.status=="") cs1.status = 1;
            if (Convert.ToInt16(cs1.status) == 0)
            {
                return Json(new { result, msg = "该科目已停用" });
            }
            //if ( cs1.sub_code1=="1122" || cs1.sub_code1 == "2202" || cs1.sub_code1 == "1405" || (cs1.sub_code1.ToString()=="1123" && cs1.sub_type.ToString()!="") || (cs1.sub_code1.ToString() == "2203" && cs1.sub_type.ToString() != "") )
            //{
            //    return Json(new { result, msg = "业务关联科目无法手工制作凭证" });
            //}


            return Json(new { result, msg,sub_name_full=cs1.name});
        }

    }
}
