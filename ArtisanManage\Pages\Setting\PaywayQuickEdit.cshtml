@page
@model ArtisanManage.Pages.Setting.PaywayQuickEditModel
@{
    Layout = null;
} 
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>PaywayQuickEdit</title>
    <link rel="stylesheet" href="~/css/DataForm.css" type="text/css" />
    <script type="text/javascript" src="~/js/DataForm.js"></script>
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmyinput.js"></script>

    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxbuttons.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxscrollbar.js"></script>

    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdropdowntree.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxtree.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmenu.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmygrid.edit.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.selection.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.columnsresize.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.sort.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.aggregates.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcheckbox.js"></script>

    <link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=2">
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=2"></script>

    <script type="text/javascript">
        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
             @Html.Raw(Model.m_showFormScript)
             @Html.Raw(Model.m_createGridScript)
             $("#sub_type").on("click", function (event) {
                var subUsed = $("#subUsed").val()
                if(subUsed === 'true'){
                         bw.toast(`已使用,无法修改类型`, 3000);
                }
            })

            window.onresize();
            function refreshIsOrder() {
                var subValue = $('#sub_type').val()
                if (subValue.value == 'YS') $('#div_is_order').show()
                else {
                    $('#div_is_order').hide()
                    $('#is_order').jqxInput('val', '')
                }
            }
            $('#sub_type').on('optionSelected', function (a, b) {
                refreshIsOrder()
            })
            refreshIsOrder()
        });
        window.onresize = function () {

        };
    </script>
</head>
<body>
    <div id="divHead" class="headtail" style="width:500px;"></div> 
    <div style="text-align:center;margin-top:20px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;">保存</button> 
        <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>
