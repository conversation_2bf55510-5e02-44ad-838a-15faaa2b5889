﻿@page
@model ArtisanManage.Pages.BaseInfo.CommissionStrategyModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <link href="~/css/component.css" rel="stylesheet" />
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        //var rowIndex = -1;
        window.addEventListener('message', function (rs) {
            console.log(rs.data)
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);

        });
        @Html.Raw(Model.m_saveCloseScript)


        $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
        function getNotNullRows(){
            var items = $("#gridItems").jqxGrid('getrows');
            var clients = $("#gridClients").jqxGrid('getrows');
            console.log(items)
            var newClassStrategy = items.filter(e => {
                            return e.plan_id !== "" || e.group_id !== "" || e.region_id !== "" || e.rank_id !== ""
            })
            var newClientsStrategy = clients.filter(e => {
                return e.plan_id !== "" || e.supcust_id !== ""
            })
            return { items: newClassStrategy, clients: newClientsStrategy }
        }
                 
        


                    //$('#gridItems').jqxGrid({cellhover:cellhover});
                    window.operRowID = -1;
                window.g_gridID = 'gridItems'
                var divGrid = '#' + window.g_gridID
                $(divGrid).jqxGrid({
                    cellhover: function (cellhtmlElement, x, y, rowIndex, gridID) {
                        if (cellhtmlElement) {
                            if (cellhtmlElement.className.indexOf('pinned') >= 0) {
                                var displayRows = $(divGrid).jqxGrid('getvisiblerows');
                                var allRows = $(divGrid).jqxGrid('getrows');
                                var arr = $('.row_operator')

                                for (var i = 0; i < arr.length; i++) {

                                    var row_operator = arr[i]
                                    if (row_operator.parentNode) {
                                        // if (row_operator.parentNode.rowIndex != cellhtmlElement.rowIndex || row_operator.parentNode.rowIndex == undefined) {
                                        var row = row_operator.parentNode.parentNode
                                        var id = row.id
                                        id = id.replace('row', '')
                                        id = id.replace(gridID, '')
                                        var curRow = displayRows[id]
                                        var showIndex = -1
                                        for (var j = 0; j < allRows.length; j++) {
                                            var r = allRows[j]

                                            if (r === curRow) {
                                                showIndex = j
                                            }
                                        }
                                        if (showIndex != window.operRowID) {
                                            //  console.log('设置了setPinCell:', showIndex)
                                            var html = "<div style='height:100%;display:flex; justify-content:center;align-items:center;'>" + (showIndex + 1) + "</div>";
                                            row_operator.parentNode.innerHTML = html;// row_operator.parentNode.normalInnerHTML;
                                        }
                                        //}
                                    }
                                }

                                if (cellhtmlElement.innerHTML.indexOf('row_operator') == -1) {
                                    if (!(window.g_dicNotDeleteRows && window.g_dicNotDeleteRows[rowIndex])) {
                                        var pinText = $(cellhtmlElement).text()


                                        // window.g_hoveredRow
                                        cellhtmlElement.innerHTML = `<div class="row_operator" style="height:100%;width:100%;display:flex;justify-content:space-around;align-items:center;">
                                             <svg onclick="onRowAdd('${divGrid.replace('#', '')}',${rowIndex})" class="row-oper" height="15" width="15" style="">
                                                    <use xlink:href="/images/images.svg#add" />
                                             </svg>
                                             <svg onclick="onRowRemove('${divGrid.replace('#', '')}',${rowIndex})" class="row-oper" height="15" width="15" style="">
                                                    <use xlink:href="/images/images.svg#remove" />
                                             </svg>
                                            </div>`;
                                        window.operHTML = cellhtmlElement.innerHTML;
                                        window.operRowID = rowIndex;
                                        // console.log('new rowIndex:',rowIndex)
                                    }

                                }
                            }
                        }

                    }

                });

                $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
                });

                //  QueryData();
                //$("#gridClients").hide();
                $(".Items").parent().addClass("active");

                $(".priceTab div").on("click", function () {

                    var title = $(this).attr("class");
                    if (title == "Clients") {
                        $("#gridClients").css("visibility", "visible");
                        $("#gridItems").css("visibility", "hidden");
                        window.g_gridID = 'gridClients'
                    } else {
                        $("#gridItems").css("visibility", "visible");
                        $("#gridClients").css("visibility", "hidden");
                        window.g_gridID = 'gridItems'

                    }

                    $(this).parent().addClass("active");
                    $(this).parent().siblings().removeClass("active");

                })
                $("#btnSave").on("click",function(){
                    var newRowsData = getNotNullRows()
                    let isItemValid = newRowsData.items.every(e => {
                        if (e.group_id == "" && e.region_id == "" && e.rank_id == "") {
                            bw.toast("片区、渠道、等级不能全为空");
                            return false
                        } else if(e.plan_id == "") {
                            bw.toast("请选择提成方案");
                            return false
                        } else {
                            let newRowsDatalen = newRowsData.items.filter(ec => {
                                return ec.group_id == e.group_id && ec.region_id == e.region_id && ec.rank_id == e.rank_id
                            })
                            if (newRowsDatalen.length > 1) {
                                bw.toast("客户类别已存在");
                                return false
                            }else{
                                return true
                            }
                        }
                    })
                    let isClientValid = newRowsData.clients.every(e => {
                        if (e.supcust == "") {
                            bw.toast("请选择客户");
                            return false
                        } else if (e.plan_id == "") {
                            bw.toast("请选择提成方案");
                            return false
                        } else {
                            let newRowsDatalen = newRowsData.clients.filter(ec => {
                                return ec.supcust_id == e.supcust_id
                            })
                            if (newRowsDatalen.length > 1) {
                                bw.toast("客户重复");
                                return false
                            } else {
                                return true
                            }
                        }
                    })
                    if (isClientValid && isItemValid) {
                        btnSave_Clicked()
                    }
                })
            });


    </script>
</head>

<body style="overflow:hidden">


    <style>
        html, body {
            height: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        .dataArea {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            margin: 0;
            width: 100%;
            height: 100%;
        }

            .dataArea > div:first-child {
                width: 100%;
                height: 40px;
            }

            .dataArea > div:last-child {
                display: flex;
                align-items: stretch;
                align-content: stretch;
                width: 100%;
                flex-grow: 1;
            }

                .dataArea > div:last-child > div:first-child {
                    width: 200px;
                }

                .dataArea > div:last-child > div:last-child {
                    flex-grow: 1;
                }

        .display-none {
            display: none;
        }

        .priceTab ul {
            list-style-type: none;
            overflow: hidden;
            margin-top: 0px;
        }

            .priceTab ul li {
                float: left;
            }

            .priceTab ul li {
                padding: 5px 0;
                margin-right: 5px;
                width: 150px;
                text-align: center;
            }

                .priceTab ul li div {
                    cursor: pointer;
                }

                .priceTab ul li.active {
                    border-bottom: 1px solid #e6214a
                }

                    .priceTab ul li.active div {
                        color: #e6214a;
                    }


        html, body {
            height: 100%;
            padding-left: 10px;
            margin: 0;
            overflow: hidden;
        }

        body {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        #gridItems {
            margin-bottom: 2px;
            width: calc(100% - 20px);
            flex: 1;
        }

        #gridClients {
            margin-bottom: 2px;
            width: calc(100% - 20px);
            flex: 1;
        }
    </style>


    <div id="divHead" class="headtail" style="width:800px;padding-top:10px;">
        <!--<div style="display:none;"><div><label>编号</label></div> <div><div id="item_id"></div></div></div>
        <div><div><label>名称</label></div> <div><div id="item_name"></div></div></div>
        <div><div><label>类别</label></div> <div><div id="item_class"></div></div></div>
        <div><div><label>品牌</label></div> <div><div id="item_brand"></div></div></div>
        <div><div><label>序号</label></div> <div><div id="item_order_index"></div></div></div>
        <div><div><label>状态</label></div> <div><div id="status"></div></div></div>
        <div style="float:none;height:0px; clear:both;"></div>-->
    </div>
    <!--<div style="margin-top:10px; margin-left:40px;margin-bottom:10px; width:400px;">单位</div>-->
    

    <div style="display:flex;justify-content:space-around;margin-top:20px;">

        <div class="priceTab" id="priceTab">
            <ul>
                <li>
                    <div class="Items"> 按客户类别指定 </div>
                </li>
                <li>
                    <div class="Clients">按客户指定</div>
                </li>
            </ul>
        </div>
        <button id="btnSave" style="margin-right:50px;">保存</button>
    </div>

    <div id="gridItems" style="position:absolute;left:20px;width:calc(100% - 50px);top:110px;bottom:50px;"></div>
    <div id="gridClients" style="position:absolute;left:20px;width:calc(100% - 50px);top:110px;bottom:50px;visibility:hidden"></div>

    <script type="text/javascript">
        function btn_change(e) {
            window.parent.newTabPage("提成策略", `BaseInfo/CommissionStrategy`, window);
        }
        function onFormSaved(msg) {
            bw.toast("保存成功");
        }

    </script>

</body>
</html>