@page
@model ArtisanManage.Pages.BaseInfo.AccountHistoryModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">

    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var m_db_id = "10";

    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $('#supcust_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popClient').jqxWindow('open');
                        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                if (args.datafield == "sheet_no") {
                    var sheet_id = args.row.bounddata.sheet_id;
                    var sheet_type = args.row.bounddata.sheet_type;
                    if (sheet_type == '销售单' || sheet_type == '退货单')  window.parent.newTabPage(sheet_type, `Sheets/SaleSheet?sheet_id=${sheet_id}`);
                    if (sheet_type == '销售订单' || sheet_type == '退货订单') window.parent.newTabPage(sheet_type,`Sheets/SaleOrderSheet?sheet_id=${sheet_id}`);
                    if (sheet_type == '预收款单') window.parent.newTabPage(sheet_type, `Sheets/PrepaySheet?sheet_id=${sheet_id}`);
                    if (sheet_type == '定货单') window.parent.newTabPage(sheet_type, `Sheets/OrderItemSheet?sheet_id=${sheet_id}`);
                    if (sheet_type == '收款单') window.parent.newTabPage(sheet_type, `Sheets/GetArrearsSheet?sheet_id=${sheet_id}`);
                    if (sheet_type == '定货调整单') window.parent.newTabPage(sheet_type, `Sheets/OrderItemAdjustSheet?sheet_id=${sheet_id}`);
                    if (sheet_type == '费用支出单') window.parent.newTabPage(sheet_type, `Sheets/FeeOutSheet?forOutOrIn=true&sheet_type=ZC&sheet_id=${sheet_id}`);
                    }


                 });

                $("#gridItems").jqxGrid('beforeRowRender', function (divRow, rowData) {
                    if (rowData.sheet_status == '已红冲')
                        divRow.style.color = '#888'
                    else if (rowData.sheet_status == '红字单')
                        divRow.style.color = '#f00'
                    else
                        divRow.style.color = '#000'

                })
                function toggleGridColumns() {
                    var isChecked = $('#showSaleSheetDetail').jqxCheckBox('checked');
                    var checked = (isChecked === true || isChecked === "true"); //isChecked在页面初始化时获取到"true"，点击复选框获取到bool值
                    var columns = [ 'item_name','real_price','orig_price', 'unit_no', 'qty_no_unit', 'left_amount'];

                    columns.forEach(function (column) {
                    if (checked) {
                            $('#gridItems').jqxGrid('showcolumn', column);
                        } else {
                            $('#gridItems').jqxGrid('hidecolumn', column);
                        }
                    });
                }
                $('#showSaleSheetDetail').on('change', function (event) {
                    toggleGridColumns();
                });
                    toggleGridColumns();
                    QueryData();      
                });
        
            
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                    

                }
                $('#popClient').jqxWindow('close');
            }
        })
    </script>
</head>

<body>

    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;min-width:42px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;min-width:42px;">导出</button>
    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>

    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
</body>
</html>