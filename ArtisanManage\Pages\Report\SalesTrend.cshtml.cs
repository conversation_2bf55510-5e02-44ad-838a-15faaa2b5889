﻿using System;
using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using ArtisanManage.Services;

namespace ArtisanManage.Pages.BaseInfo
{
    public class SalesTrendModel : PageQueryModel
    { 
        public SalesTrendModel(CMySbCommand cmd) : base(Services.MenuId.salesTrend)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期", CtrlType="jqxDateTimeInput", SqlFld="happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00",
                    DealQueryItem = startTime =>
                    {
                        SQLVariable1 = startTime;
                        return "";
                    }
                }},
                {"endDay"  ,new DataItem(){Title="结束日期", CtrlType="jqxDateTimeInput", SqlFld="happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    ",
                    DealQueryItem = endTime =>
                    {
                        SQLVariable2 = endTime;
                        return "";
                    }
                }},
                {"seller_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.seller_id",SqlForOptions=CommonTool.selectSellers,ForQuery=false } },
                 {"depart_path",new DataItem(){Title="部门", FldArea="divHead",LabelFld="depart_path_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department",ForQuery=false
                }},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"interval",  new DataItem(){Title="日期",   Width="60%",SqlFld = "interval"}},
                       {"net_amount",   new DataItem(){Title="销售净额",  Width="40%", SqlFld = "net_amount"}},
                     },
                     QueryFromSQL=@$" 
 from 
(
     SELECT COALESCE(a.interval,b.t) as INTERVAL,COALESCE(net_amount,0) AS net_amount FROM ( SELECT round( SUM ( X_amount ) :: NUMERIC, 2 ) X_amount,round( SUM ( T_amount ) :: NUMERIC, 2 ) T_amount,
                                    round( SUM ( X_amount + T_amount ) :: NUMERIC, 2 ) AS net_amount,to_char( happen_time, 'MM-dd' ) AS INTERVAL,
		                            round( ( SUM ( total ) - SUM ( spec_cost ) ) :: NUMERIC, 2 ) spec_profit,round( ( SUM ( total ) - SUM ( avg_cost ) ) :: NUMERIC, 2 ) avg_profit,
		                            round( ( SUM ( total ) - SUM ( buy_cost ) ) :: NUMERIC, 2 ) buy_profit 
       FROM sheet_sale_main sm
		 LEFT JOIN (SELECT oper_id ,depart_path from info_operator where company_id = ~COMPANY_ID) io on io.oper_id  = sm.seller_id 
	 LEFT JOIN 
    ( 
        SELECT sheet_id,SUM ( CASE WHEN quantity * inout_flag <= 0 THEN sub_amount * inout_flag * ( - 1 ) ELSE 0 END ) AS X_amount,SUM ( CASE WHEN quantity * inout_flag > 0 THEN sub_amount * inout_flag * ( - 1 ) ELSE 0 END ) AS T_amount,
			                        SUM ( sub_amount * inout_flag * ( - 1 ) ) total,
			                        SUM ( quantity * inout_flag * ( - 1 ) * unit_factor * COALESCE ( d.cost_price_avg, 0 ) ) avg_cost,
			                        SUM ( quantity * inout_flag * ( - 1 ) * unit_factor * COALESCE ( P.cost_price_spec, 0 ) ) spec_cost,
			                        SUM ( quantity * inout_flag * ( - 1 ) * unit_factor * COALESCE ( mu.buy_price, 0 ) ) buy_cost 
        FROM sheet_sale_detail d 
        LEFT JOIN info_item_prop P ON P.item_id = d.item_id and P.company_id = d.company_id
		LEFT JOIN 
            ( 
                SELECT item_id, COALESCE ( buy_price, 0 ) buy_price 
                FROM info_item_multi_unit 
                WHERE company_id = ~COMPANY_ID AND unit_type = 's' 
            ) mu ON mu.item_id = d.item_id 
		 WHERE d.company_id = ~COMPANY_ID    GROUP BY sheet_id 
    ) d ON sm.sheet_id = d.sheet_id
	WHERE company_id = ~COMPANY_ID AND approve_time IS NOT NULL AND coalesce(sm.red_flag,0)=0 AND ( happen_time BETWEEN '~SQL_VARIABLE1' AND '~SQL_VARIABLE2' ~VAR_seller_id  ~VAR_depart_path
) 
GROUP BY INTERVAL ORDER BY INTERVAL DESC ) A FULL JOIN (SELECT to_char( b, 'MM-DD' ) AS T
                                    FROM
                                    generate_series ( to_timestamp( '~SQL_VARIABLE1', 'YYYY-MM-DD' ), 
                                    to_timestamp( '~SQL_VARIABLE2', 'YYYY-MM-DD' ), '1 days' ) AS b GROUP BY T
                                    ORDER BY T DESC) b ON A.INTERVAL = b.T) t where 1 = 1 -- or company_id= ~COMPANY_ID
                                    ",//此处需要换行
                  }
                } 
            };             
        }
        public async Task OnGet(string operKey)
        {
            OperKey = operKey; 
            await InitGet(cmd);
        }


        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            var seller_id = DataItems["seller_id"].Value;
            var depart_path = DataItems["depart_path"].Value;

            if (DataItems["seller_id"].Value.IsValid())
            {
                SQLVariables["seller_id"] = @$" and seller_id={seller_id}";
            }
            else
            {
                SQLVariables["seller_id"] = "";

            }
            if (depart_path.IsValid())
            {
                SQLVariables["depart_path"] = @$" and depart_path like '%{"/"+ depart_path + "/"}'";
            }
            else
            {
                SQLVariables["depart_path"] = "";

            }
        }


    }



    [Route("api/[controller]/[action]")]
    public class SalesTrendController : QueryController
    { 
        public SalesTrendController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SalesTrendModel model = new SalesTrendModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            SalesTrendModel model = new SalesTrendModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }



    }
}
