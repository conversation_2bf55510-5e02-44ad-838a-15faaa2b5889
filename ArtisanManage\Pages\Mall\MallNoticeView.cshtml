﻿@page
@model ArtisanManage.Pages.Mall.MallNoticeViewModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxtabs.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);	//先配一下权限变量
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "NoticeEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var row = {};
                        let notice = rs.data.notice
                        row["notice_id"] = notice.notice_id;
                        row["remark"] = notice.remark;
                        row["order_index"] = notice.order_index;
                        row["notice_type_name"] = notice.notice_type.replace('banner', '横幅').replace('popupImg', '弹窗图片')
                        row["show_after_read_type"] = notice.show_after_read.toLowerCase() === 'true' ? '是':'否';
                        row["status"] = notice.status === '1' ? '正常' : '停用';
                        row["show_start_time"] = notice.show_start_time;
                        row["show_end_time"] = notice.show_end_time;
                        row["creator_name"] = notice.creator_name;
                        row["updater_name"] = notice.updater_name;
                        row["create_time"] = notice.create_time;
                        row["update_time"] = notice.update_time;

                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;


                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    let notice = rs.data.notice
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "notice_id", notice.notice_id);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "remark", notice.remark);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "order_index", notice.order_index);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "notice_type_name", notice.notice_type.replace('banner', '横幅').replace('popupImg', '弹窗图片'));
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "show_after_read_type", notice.show_after_read.toLowerCase() === 'true' ? '是' : '否');
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "status", notice.status === '1' ? '正常' : '停用');
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "show_start_time", notice.show_start_time);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "show_end_time", notice.show_end_time);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "creator_name", notice.creator_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "updater_name", notice.updater_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "create_time", notice.create_time);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "update_time", notice.update_time);

                }
                $("#popItem").jqxWindow('close');
            };
        });
        function btnAddNotice_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="/Mall/MallNoticeEdit?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }
        function onGridRowEdit() {
            let notice_id = $('#gridItems').jqxGrid('getcellvalue', RowIndex, 'notice_id');
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="/Mall/MallNoticeEdit?operKey=' + g_operKey + '&notice_id=' + notice_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }
        function onGridRowDelete() {
            console.log("delete row " + RowIndex)
            let notice_id = $('#gridItems').jqxGrid('getcellvalue', RowIndex, 'notice_id');
            jConfirm('确定要删除本条公告吗？', function () {
                $.ajax({
                    url: '/api/MallNoticeEdit/Delete',
                    type: 'GET',
                    contentType: 'application/json',
                    data: { operKey: g_operKey, notice_id: notice_id },
                    success: function (ret) {
                        if (ret.result === 'OK') {
                            bw.toast('成功删除公告')
                            QueryData()
                        } else {
                            bw.toast(ret.msg)
                        }
                    },
                    error: function (xhr) {
                        console.log("返回响应信息：" + xhr.responseText)
                    }
                })
            }, "")
            
        }
        function isRightClick(event) {
            var rightclick;
            if (!event) var event = window.event;
            if (event.which) rightclick = (event.which == 3);
            else if (event.button) rightclick = (event.button == 2);
            return rightclick;
        };
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            
            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                debugger
                var args = event.args;
                RowIndex = args.rowindex;//args包含被点击单元格和参数
                if (isRightClick(event.originalEvent)) {
                    // 展示弹出框
                        let scrollTop = $(window).scrollTop();
                        let scrollLeft = $(window).scrollLeft();
                        contextMenu.jqxMenu('open', parseInt(event.args.originalEvent.clientX) + 5 + scrollLeft, parseInt(event.args.originalEvent.clientY) + 5 + scrollTop);
                    return false
                }
                if (args.datafield == "remark") {
                    // if (args.originalEvent.button == 2) return;
                    onGridRowEdit()
                    
                }
            });

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 720, width: 850, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });


            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });
            // 检查有没有删除权限
            if (window.getRightValue('setting.mallNotice.delete').toLowerCase() === 'false') {
                // 操作元素
                const menuDelete = document.getElementById('menuDelete');
                if (menuDelete) {
                    // 隐藏菜单里的删除元素
                    menuDelete.style.display = 'none';
                }
                
            }
            // 检查有没有编辑权限
            if (window.getRightValue('setting.mallNotice.edit').toLowerCase() === 'false') {
                // 操作元素
                const btnAdd = document.getElementById('btnAdd');
                
                // 隐藏添加按钮
               btnAdd.style.display = 'none';
          
            }

            // 创建一个弹出框
            var contextMenu = $("#gridMenu").jqxMenu({ width: 200, autoOpenPopup: false, mode: 'popup' });

            QueryData()
        })
    </script>
</head>
<body>
    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail" style="width: calc(100% - 200px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <div>
            <div style="width:220px;margin-left:20px;margin-bottom:15px">
                <button onclick="QueryData()" style="margin:0 8px;width:80px">查询</button>
                <button id="btnAdd" onclick="btnAddNotice_click()" style="margin:0 8px;width:80px"> 新建公告</button>
            </div>

        </div>
    </div>


    <div id="gridItems" style="margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 95px);"></div>
    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">公告详情</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="gridMenu">
        <ul>
            <li id="menuEdit" onclick="onGridRowEdit()">编辑</li>
            <li id="menuDelete" onclick="onGridRowDelete()">删除</li>
        </ul>
    </div>
</body>
</html>