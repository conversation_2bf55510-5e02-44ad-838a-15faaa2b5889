﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.Report
{
    public class ArrearsSheetDetailModel : PageQueryModel
    { 
        public ArrearsSheetDetailModel(CMySbCommand cmd) : base(Services.MenuId.arrearsSheetDetail)
        {
            this.cmd = cmd;
            CanQueryByApproveTime = true;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sgam.happen_time+sgad.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sgam.happen_time+sgad.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"mm_startDay",new DataItem(){Title="业务单开始",Hidden=true,FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="ssm.happen_time", CompareOperator=">=",Value=""}},
                {"mm_endDay"  ,new DataItem(){Title="业务单结束",Hidden=true, FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="ssm.happen_time",   CompareOperator="<",Value="",
                    JSDealItemOnSelect=@"                        
                            var s=$('#mm_endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#mm_endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"depart_path",new DataItem(){Title="部门",Hidden=true, FldArea="divHead",LabelFld="depart_path_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
           {"other_region",new DataItem(){Title="片区",MumSelectable=true, FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500", TreePathFld="region_path",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region order by order_index , region_id",
                    DealQueryItem = (value) =>
                    {
                        return "/"+value+"/";
                    }
                }},
           
                {"sheet_type",new DataItem(){FldArea="divHead",SqlFld ="sgam.sheet_type",Title="单据类型",LabelFld="sheet_type_name",ButtonUsage="list",Source = "[{v:'SK',l:'收款单'},{v:'FK',l:'付款单'},{v:'',l:'所有'}]",Value="SK",Label="收款单", CompareOperator="="}},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客户名称",LabelFld="sup_name",ButtonUsage="list",SqlFld="s.supcust_id",CompareOperator="=",
                    SqlForOptions=CommonTool.selectSupcust } },
				{"seller_id",new DataItem(){FldArea="divHead",Title="源单业务员", SqlFld="ssm.seller_id", LabelFld="seller_name" ,ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                
                {"getter_id",new DataItem(){FldArea="divHead",Title="收款人",LabelFld="getter_name" ,ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                {"status",new DataItem(){FldArea="divHead",Title="单据状态",LabelFld = "status_name",ButtonUsage = "list",CompareOperator="=",Value="normal",Label="正常单据",
                        Source = @"[{v:'normal',l:'正常单据',condition:""sgam.red_flag is null""},
                                   {v:'unapproved',l:'未审核单据',condition:""sgam.approve_time is null""},
                                   {v:'approved',l:'已审核单据',condition:""sgam.approve_time is not null and sgam.red_flag is null""},
                                   {v:'red',l:'红冲单',condition:""sgam.red_flag in ('1','2') ""},
                                   {v:'all',l:'所有',condition:""true""}]"
                }},
                {"sk_sheet_no",new DataItem(){SqlFld="sgam.sheet_no", FldArea="divHead",Title="收款单号", CompareOperator="like"}},
                {"mm_sheet_no",new DataItem(){SqlFld="ssm.sheet_no", FldArea="divHead",Title="业务单号", CompareOperator="like"}},
                {"byHappenTime",new DataItem(){FldArea="divHead",Title="按交易时间查询",CtrlType="jqxCheckBox",ForQuery=false,Hidden=true,Value="True"}}

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",   new DataItem(){Title="sheet_id",SqlFld="sgam.sheet_id", Hidden=true, Linkable=true, Width="80",HideOnLoad = true}},
                       {"sheet_no",   new DataItem(){Title="单据编号",SqlFld="sgam.sheet_no",  Linkable=true, Width="150"}},
                       {"x_sheet_id",   new DataItem(){Title="单据id",SqlFld="sgad.mm_sheet_id",Hidden = true}},
                       {"x_sheet_no",   new DataItem(){Title="业务单号",SqlFld="ssm.sheet_no", Linkable=true,Width="150"}},
                       {"sheet_type", new DataItem(){Title="单据类型", Width="80",SqlFld="(case WHEN m_sheet_type='X' THEN '销售单' ELSE '退货单' END)"}},
                       {"sheet_status", new DataItem(){Title="状态",  Width="80",SqlFld="(case when sgam.red_flag='2' then '红字单' when sgam.red_flag='1' then '已红冲' when sgam.approve_time is null then '未审' else '已审' END)"}},
                       {"sup_name",   new DataItem(){Title="结算单位",   Width="150", SqlFld = "s.sup_name", Sortable = true}},
                       {"sup_addr",   new DataItem(){Title="地址",   Width="150"}},
                       {"happen_time",   new DataItem(){Title="收款时间",SqlFld="sgam.happen_time",   Width="150"}},
                       {"approve_time",   new DataItem(){Title="审核时间",SqlFld="sgam.approve_time",   Width="150"}},
					   {"seller_name", new DataItem(){Title="源单业务员",SqlFld="seller.oper_name ",   Width="80"}},
					   {"getter_name", new DataItem(){Title="收款人",SqlFld="iop.oper_name ",   Width="80"}},
                       //{"x_total_amount", new DataItem(){Title="单据金额",SqlFld="(ssm.total_amount*ssm.money_inout_flag)",  Width="80",ShowSum=true}},
                       {"x_total_amount", new DataItem(){Title="单据金额",SqlFld="sgad.sheet_amount",  Width="80",ShowSum=true}},

                       {"x_paid_amount", new DataItem(){Title="已支付",SqlFld="sgad.paid_amount",  Width="80",ShowSum=true}},
                       {"x_disc_amount", new DataItem(){Title="已优惠",SqlFld="sgad.disc_amount",  Width="80",ShowSum=true}},

                       {"now_disc_amount", new DataItem(){Title="本次优惠",SqlFld="sgad.now_disc_amount",  Width="80",ShowSum=true}},
                       {"paid_amount", new DataItem(){Title="本次支付",  Width="80",SqlFld="(sgad.now_pay_amount*sgam.money_inout_flag)",ShowSum=true}},
                       {"arrears", new DataItem(){Title="剩余欠款",  Width="80",SqlFld="sgad.left_amount",ShowSum=true}},
              
                       {"make_brief", new DataItem(){Title="备注", Width="100",SqlFld="sgam.make_brief"}},
                     },
                     QueryFromSQL=@"
FROM sheet_get_arrears_detail sgad 
LEFT JOIN sheet_get_arrears_main sgam on sgam.sheet_id = sgad.sheet_id and sgam.company_id = ~COMPANY_ID
LEFT JOIN info_supcust s on sgam.supcust_id = s.supcust_id and s.company_id = ~COMPANY_ID    
LEFT JOIN 
(
   SELECT sheet_id,sheet_type,sheet_no,happen_time,          seller_id from sheet_sale_main where company_id = ~COMPANY_ID
   union
   SELECT sheet_id,sheet_type,sheet_no,happen_time,getter_id seller_id from sheet_fee_out_main where company_id = ~COMPANY_ID
   union
   SELECT sheet_id,sheet_type,sheet_no,happen_time,getter_id seller_id from sheet_prepay where company_id = ~COMPANY_ID

)
ssm on sgad.m_sheet_type=ssm.sheet_type and sgad.mm_sheet_id = ssm.sheet_id
LEFT JOIN info_operator seller on seller.oper_id = ssm.seller_id and seller.company_id = ~COMPANY_ID
LEFT JOIN info_operator iop on iop.oper_id = sgam.getter_id and s.company_id = ~COMPANY_ID
where sgad.company_id= ~COMPANY_ID
",
                     
                     QueryOrderSQL=" order by happen_time desc"
                  }
                } 
            }; 
        }
        public async Task OnGet()
        {
            string forPayOrGet = CPubVars.RequestV(Request, "forPayOrGet");
            if (forPayOrGet!=null && forPayOrGet.ToLower() == "true")
            {
                DataItems["sheet_type"].Value = "FK";
                DataItems["sheet_type"].Label = "付款单";
                DataItems["supcust_id"].Title = "供应商";
                DataItems["supcust_id"].SqlForOptions=DataItems["supcust_id"].SqlForOptions.Replace("%C%", "%S%");
                DataItems["supcust_id"].OtherQueryStrForOptions = "sheet_type=FK";
                 var gridItems = Grids["gridItems"].Columns;
                gridItems["happen_time"].Title = "付款时间";
                gridItems["seller_name"].Title = "付款人";
                gridItems["left_amount"].Title = "付款前欠款";
                gridItems["pay_ways"].Title = "付款账户";

            }
           
            await InitGet(cmd);
           
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
    }



    [Route("api/[controller]/[action]")]
    public class ArrearsSheetDetailController : QueryController
    { 
        public ArrearsSheetDetailController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value,string sheet_type,string availValues)
        {
            ArrearsSheetDetailModel model = new ArrearsSheetDetailModel(cmd);
            if (sheet_type == "FK")
            {
                model.DataItems["supcust_id"].SqlForOptions = model.DataItems["supcust_id"].SqlForOptions.Replace("%C%", "%S%");
            }
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            ArrearsSheetDetailModel model = new ArrearsSheetDetailModel(cmd);           
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            ArrearsSheetDetailModel model = new ArrearsSheetDetailModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
