﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;
using ArtisanManage.Services;
using Newtonsoft.Json;
using NPOI.Util;
using Org.BouncyCastle.Asn1.Cms;
using static HuaWeiObsController.HuaWeiObs;
using System.Net.Http;
using Org.BouncyCastle.Asn1.Ocsp;
using HuaWeiObsController;
using System.IO;
using NPOI.SS.Formula.Functions;
using System.Dynamic;
using Microsoft.SqlServer.TransactSql.ScriptDom;

namespace ArtisanManage.Pages.Mall
{
    public class MallNoticeEditModel : PageFormModel
    {
        public MallNoticeEditModel(CMySbCommand cmd, string company_id = "", string oper_id = "") : base(Services.MenuId.mallNotice)
        {
            this.cmd = cmd;
            NoIDFld = true;
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class MallNoticeEditController : BaseController {

        // 上传照片需要的对象
        private readonly IHttpClientFactory _httpClientFactory;
        // 字段与类型的映射
        private readonly Dictionary<string, string> fieldTypeMappings = new Dictionary<string, string>
        {
            { "company_id", "int" },
            { "notice_type", "string" },
            { "notice_content", "jsonb" },  // JSON 类型
            { "show_after_read", "bool" },
            { "order_index", "int" },
            { "redirect_target", "jsonb" },
            { "show_start_time", "datetime" },
            { "show_end_time", "datetime" },
            { "status", "int" },
            { "create_time", "datetime" },
            { "creator", "int" },
            { "update_time", "datetime" },
            { "updater", "int" },
            { "groups_id", "string" },
            { "ranks_id", "string" },
            { "regions_id", "string" },
            { "remark", "string" }
            // 新加入的字段可以在这里维护
        };
        public MallNoticeEditController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd;
            _httpClientFactory = httpClientFactory;
        }

        [HttpGet]
        public async Task<IActionResult> GetMallNotice( string notice_id, string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = "";
            dynamic notice = null;
            dynamic regions = null;
            dynamic groups = null;
            dynamic ranks = null;
            dynamic items = null;

            if (notice_id.IsValid())
            {
                sql = @$"select * from mall_notice where company_id = {companyID} and notice_id = {notice_id} ";
                QQ.Enqueue("notice", sql);
            }
            // 片区选项
            sql = @$"select region_id as value,region_name as label,mother_id as pv from info_region where company_id = {companyID}  order by  region_id";
            QQ.Enqueue("regions", sql);
            // 渠道选项
            sql = @$"select group_id as value,group_name as label from info_supcust_group where company_id = {companyID}";
            QQ.Enqueue("groups", sql);
            // 等级选项
            sql = @$"select rank_id as value,rank_name as label from info_supcust_rank where company_id = {companyID}";
            QQ.Enqueue("ranks", sql);
            // 商品档案
            sql = @$"select item_id as value,item_name as label from info_item_prop where company_id = {companyID} and son_mum_item is null";
            QQ.Enqueue("items", sql);

            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "notice")
                {
                    notice = CDbDealer.Get1RecordFromDr(dr, false);
                }
                else if (tbl == "groups")
                {
                    groups = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "ranks")
                {
                    ranks = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "regions")
                {
                    regions = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "items")
                {
                    items = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK",notice = notice, regions = regions, ranks = ranks, groups = groups,items = items});
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request) { 
            string operKey = (string)request.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            Dictionary<string, object> notice = JsonConvert.DeserializeObject<Dictionary<string, object>>(JsonConvert.SerializeObject(request.notice));
            string notice_id = (string)notice["notice_id"];
            string notice_type = (string)notice["notice_type"];
            dynamic content = new ExpandoObject();
            
            if (notice_type == "popupImg")
            {
                content = notice["notice_content"];
                dynamic popupImg = content.popupImg;
                // 需要处理图片上传逻辑
                string src = (string)content.popupImg.src;
                string name = (string)content.popupImg.name;
                string retUrl = await ProcessImageStr(_httpClientFactory, src, name, companyID);

                // 设置content对象
                content.popupImg = retUrl;
                notice["notice_content"] = content;
            } else if (notice_type == "banner")
            {
                // 没什么要做的
            }

            
            
            string noticeSql = "";
            notice["company_id"] = companyID;
            notice["updater"] = operID;
            notice["update_time"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            if (notice_id.IsValid())
            {

                noticeSql = BuildSql(notice, false);
            } else
            {
                notice["creator"] = operID;
                notice["create_time"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                noticeSql = BuildSql(notice, true);
            }
            
            dynamic record = await CDbDealer.Get1RecordFromSQLAsync(noticeSql,cmd);

            noticeSql = @$"select * from mall_notice mn
	LEFT JOIN ( SELECT oper_id, oper_name AS creator_name, depart_path FROM info_operator WHERE company_id ={companyID} ) co ON co.oper_id = mn.creator
    LEFT JOIN ( SELECT oper_id, oper_name AS updater_name, depart_path FROM info_operator WHERE company_id ={companyID} ) uo ON uo.oper_id = mn.updater
    where
	mn.company_id ={companyID} and mn.notice_id = {record.notice_id}";
            dynamic newNotice = await CDbDealer.Get1RecordFromSQLAsync(noticeSql, cmd);

            return new JsonResult(new { result = "OK" ,record = newNotice});
        }

        [HttpGet]
        public async Task<IActionResult> Delete(string notice_id, string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            // 从阅读表中查找记录
            string sql = $@"select * from mall_notice_close where company_id ={companyID} and notice_id = {notice_id}";
            dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(sql,cmd);
            if (ret != null)
            {
                // 已阅读不允许删除
                return new JsonResult(new { result = "Error", msg = "该公告已被阅读，不能进行删除" });
            } else
            {
                sql = $@"delete from mall_notice where company_id = {companyID} and notice_id = {notice_id}";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
                return new JsonResult(new { result = "OK" });
            }
           
        }
        // 构建 SQL 语句的方法
        public string BuildSql(Dictionary<string, object> data, bool isInsert = true)
        {
            var sqlColumns = new List<string>();
            var sqlValues = new List<string>();
            var sqlUpdates = new List<string>();

            foreach (var field in fieldTypeMappings.Keys)
            {
                if (!data.ContainsKey(field))
                {
                    continue;
                }

                // 如果是 UPDATE，跳过 `creator` 和 `create_time` 字段
                if (!isInsert && (field == "creator" || field == "create_time"))
                {
                    continue;
                }

                string formattedValue = FormatData(field, data[field]);

                if (isInsert)
                {
                    sqlColumns.Add(field);
                    sqlValues.Add(formattedValue);
                }
                else
                {
                    sqlUpdates.Add($"{field} = {formattedValue}");
                }
            }

            string sql = "";

            if (isInsert)
            {
                sql = $"INSERT INTO mall_notice ({string.Join(", ", sqlColumns)}) VALUES ({string.Join(", ", sqlValues)}) RETURNING *";
            }
            else
            {
                sql = $"UPDATE mall_notice SET {string.Join(", ", sqlUpdates)} WHERE notice_id = {data["notice_id"]} and company_id = {data["company_id"]} RETURNING *"; // 假设 notice_id 是更新条件
            }

            return sql;
        }

        // 格式化数据的方法
        public string FormatData(string fieldName, object value)
        {
            // 判断字段类型并根据类型返回格式化的字符串
            if (!fieldTypeMappings.ContainsKey(fieldName))
            {
                throw new Exception($"字段 {fieldName} 未定义类型映射");
            }

            string fieldType = fieldTypeMappings[fieldName];

            switch (fieldType.ToLower())
            {
                case "int":
                    return string.IsNullOrEmpty(value?.ToString()) ? "NULL" : Convert.ToInt32(value).ToString();
                case "string":
                    return string.IsNullOrEmpty(value?.ToString()) ? "NULL" : $"'{value.ToString().Replace("'", "''")}'"; // 处理字符串中的单引号
                case "bool":
                    return string.IsNullOrEmpty(value?.ToString()) ? "NULL" : (Convert.ToBoolean(value) ? "TRUE" : "FALSE");
                case "datetime":
                    return string.IsNullOrEmpty(value?.ToString()) ? "NULL" : $"'{Convert.ToDateTime(value):yyyy-MM-dd HH:mm:ss}'"; // 格式化为 PostgreSQL 格式的时间
                case "jsonb":
                    // 如果是 JSON 类型，将对象转换为 JSON 字符串
                    return string.IsNullOrEmpty(value?.ToString()) ? "NULL" : $"'{JsonConvert.SerializeObject(value)}'"; // 转换为 JSON 格式的字符串
                default:
                    throw new Exception($"不支持的字段类型: {fieldType}");
            }

        }

        public static async Task<string> ProcessImageStr(IHttpClientFactory _httpClientFactory, string srcStr,string imgName, string companyID)
        {
            string folderPath = $"uploads/{DateTime.Today:yyyyMM}/";
            if (!System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.CreateDirectory(folderPath);
            }

            string url = "";
            string fileName = $"notice_pic_{companyID}_{CommonTool.GetTimeStamp()}_{new Random().Next(1, 100)}";
            if (HuaWeiObs.IsB64(srcStr))
            {
                string appendixPicture = srcStr.Replace("data:image/jpeg;base64,", "").Replace("data:image/png;base64,", "");
                
                string fileExtension = ".jpeg";
                using (MemoryStream stream = new MemoryStream(Convert.FromBase64String(appendixPicture)))
                {
                    string path = folderPath + fileName + fileExtension;
                    string err = await HuaWeiObs.Save(_httpClientFactory, stream, path);
                    url = $"/{DateTime.Today:yyyyMM}/{fileName}{fileExtension}";
                }
            }
            else if (srcStr.StartsWith(HuaWeiObs.BucketLinkHref))
            {
                url = $"{srcStr.Replace($"{HuaWeiObs.BucketLinkHref}/uploads", "")}";
            }
            else
            {
                NLogger.Error("图片解析错误:不是合法的BASE64字符串，也不是OBS图像链接。详细内容：" + srcStr);
                throw new Exception("图片解析错误");
            }

            
            return url;
        }
    }

}
