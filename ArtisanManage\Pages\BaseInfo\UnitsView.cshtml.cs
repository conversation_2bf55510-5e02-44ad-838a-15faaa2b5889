﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Pages.BaseInfo;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class UnitsViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
 
        public UnitsViewModel(CMySbCommand cmd) : base(Services.MenuId.infoUnit)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                 {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称",UseJQWidgets=false, SqlFld="unit_no",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     IdColumn="unit_id",TableName="info_avail_unit",
                     ShowContextMenu=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"unit_id",new DataItem(){Title="编号", Width="180",Hidden = true}},
                       {"unit_no",new DataItem(){Title="单位名称",Width="180",Linkable=true}},
                       {"order_index",new DataItem(){Title="序号",Width="50"}},
                       {"is_big_unit",new DataItem(){Title="是否大单位",SqlFld ="case when is_big_unit then '是' else '否' end", ButtonUsage="list", Width="180"}}
                     },
                     QueryFromSQL="from info_avail_unit" ,QueryOrderSQL="order by order_index,unit_no"
                  }
                } 
            }; 
        }
        public async Task OnGet(string forSelect)
        {  
            await InitGet(cmd);
            ForSelect = forSelect == "1";

            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }

        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {  
             cmd.CommandText = $"select company_id from info_item_multi_unit where unit_no in (select unit_no from info_avail_unit where company_id ={company_id} and unit_id in(  '{rowIDs}')) and company_id={company_id} limit 1";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                return "单位已使用过,无法删除";
            } 
            return "";
        }
    }




    [Route("api/[controller]/[action]")]
    public class UnitsViewController : QueryController
    { 
        public UnitsViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            UnitsViewModel model = new UnitsViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            UnitsViewModel model = new UnitsViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            UnitsViewModel model = new UnitsViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd, "info_avail_unit");// gridID, startRow, endRow, bNewQuery);
            return records;
        }

    }
}
