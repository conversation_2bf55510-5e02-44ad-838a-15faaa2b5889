@page
@model ArtisanManage.Pages.BaseInfo.PregetBalanceModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
           
    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $('#supcust_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popClient').jqxWindow('open');
                        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });


                $("#gridItems").on("cellclick", function (event) {

                    var args = event.args;
                    var supcust_id = args.row.bounddata.supcust_id;

                    var sup_name = args.row.bounddata.sup_name;

                    var startDay = $('#startDay').jqxDateTimeInput('val');
                    var endDay = $('#endDay').jqxDateTimeInput('val');


                    var addORreduce = args.datafield.split('_');

                    var subColumns=window.GridData.columns
                        for ( i in subColumns) {
                            if (subColumns[i].datafield == args.datafield) {
                                var bb  = subColumns[i].columngroup
                                var cc = bb.split('_');
                                var sub_name=cc[cc.length-1]
                                break;
                            }

                    }
                    

                    var url = ""
                    var title = ""
                if (args.datafield != "pw_add_all" && args.datafield != "pw_reduce_all" && args.datafield != "pw_add_all_without_order" && args.datafield != "pw_reduce_all_without_order") 
                {
                    if (addORreduce[1] == "add"  && sup_name) {
                    url = `Report/AccountHistory?&supcust_id=${supcust_id}&sup_name=${sup_name}&startDay=${startDay}&endDay=${endDay}&sub_type=YS&sub_type_name=预收款&sub_id=${addORreduce[2]}&sub_name=${sub_name}&sheet_type=YS,DH,DR,T&sheet_type_name=预收款单,定货单,定货调整单,退货单`;

                    title = "客户往来账"
                    window.parent.newTabPage(title, `${url}`);
                    }
                    if (addORreduce[1] == "reduce" && sup_name) {
                        url = `Report/AccountHistory?&supcust_id=${supcust_id}&sup_name=${sup_name}&startDay=${startDay}&endDay=${endDay}&sub_type=YS&sub_type_name=预收款&sub_id=${addORreduce[2]}&sub_name=${sub_name}&sheet_type=SK,X&sheet_type_name=收款单,销售单`;

                        title = "客户往来账"
                        window.parent.newTabPage(title, `${url}`);
                    }
                }


                });

                $('.btnAct').on('click', function () {
                    var act = $(this).data('act');
                    window[act]();
                });
                QueryData();
            });

        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });



                }
                $('#popClient').jqxWindow('close');
            }
        })
    </script>
</head>

<body> 
    <div style="display:flex;padding-top:20px;">

        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button class="btnAct" data-act="QueryData" style="margin-left:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>
     
    <div id="gridItems"></div>  
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div> 
    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

  

</body>
</html>