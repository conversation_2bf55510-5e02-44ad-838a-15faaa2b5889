﻿using ArtisanManage.Models;
using ArtisanManage.Pages;
using ArtisanManage.Services;
using ArtisanManage.MyCW;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Data.SqlTypes;
using System.Dynamic;
using System.Linq;
using System.Runtime.Intrinsics.X86;
using System.Threading.Tasks;
using static ArtisanManage.MyJXC.SheetRowBuy;
using static ArtisanManage.MyJXC.SheetRowCostPrice;
using Org.BouncyCastle.Bcpg.OpenPgp;

namespace ArtisanManage.MyJXC
{
    public class SheetRowBuy : SheetRowMM
    {
        //[FromFld("round((t.cost_price_avg*unit_factor)::numeric,2)")]public string cost_price_avg_unit { get; set; }
        [SaveToDB][FromFld] public string virtual_produce_date { get; set; }
        [SaveToDB][FromFld] public string sn_code { get; set; }
        internal decimal cost_price_recent1;
        internal decimal recent_price_qty1;
        internal decimal cost_price_recent2;
        internal decimal recent_price_qty2;
        internal decimal cost_price_recent3;
        internal decimal recent_price_qty3;
        internal decimal recent_time = 0;
        [SaveToDB][FromFld] public decimal allocate_amount { get; set; } = 0;
        [SaveToDB][FromFld] public string last_time_price { get; set; }
        [SaveToDB][FromFld] public string wholesale_price { get; set; }
        [SaveToDB] [FromFld] public decimal order_qty { get; set; } = 0;
        [SaveToDB][FromFld] public string price_cashback { get; set; }="";
        public string order_qty_conv
        {
            get
            {
                return SheetBase<SheetRowItem>.GetUnitQty(order_qty, b_unit_no, m_unit_no, s_unit_no, b_unit_factor, m_unit_factor);
            }

        }
        public string unit_qty_conv
        {
            get
            {
                var s_quantity = quantity * unit_factor;
                return SheetBase<SheetRowItem>.GetUnitQty(s_quantity, b_unit_no, m_unit_no, s_unit_no, b_unit_factor, m_unit_factor);
            }
        }
    }

   
    public class SheetBuy: SheetMM<SheetRowBuy>
    {
        public string bindSheetInfo { get; set; } = "";
        public string feeSheetInfo { get; set; } = "";
        public string fee_apportion_sheet_id { get; set; } = "";//用于刚审核完返回界面便于跳转分摊单（因为feeSheetInfo从buysheet.Load重新取一遍需要多读数据库）
        [SaveToDB][FromFld]public override string sheet_attribute
        {
            get//从前端获取数据，保存数据库
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                string baseAttr = base.sheet_attribute;
                if (baseAttr != "") sheetAttribute = JsonConvert.DeserializeObject<Dictionary<string, string>>(baseAttr);
                if (!string.IsNullOrEmpty(bindSheetInfo))
                {
                    sheetAttribute.Add("bindSheetInfo", bindSheetInfo);
                }
                if (!string.IsNullOrEmpty(feeSheetInfo))//审核时，要额外保存采购单和分摊单的sheet_id，在onsheetidgot，语句会重写这个属性
                {
                    sheetAttribute.Add("feeSheetInfo", feeSheetInfo);
                }

                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set//读取数据库，返回前端
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.bindSheetInfo != null)
                    {
                        this.bindSheetInfo = sheetAttr.bindSheetInfo;
                    }
                    if(sheetAttr.feeSheetInfo != null)
                    {
                        this.feeSheetInfo=sheetAttr.feeSheetInfo.ToString();
                    }

                }
                base.sheet_attribute = value;

            }
        }
        public int suspect_status = 1;
        [SaveToDB] [FromFld] public override string order_sheet_id { get; set; } = "";
        [SaveToDB(false)][FromFld("so.order_sheet_no")] public string order_sheet_no { get; set; } = "";
        public List<string> appendixPhotos { get; set; } = new List<string>();
        [SaveToDB][FromFld] public string order_source { get; set; } = "";
        [SaveToDB][FromFld] public string receivers_id { get; set; } = "";
        [SaveToDB][FromFld] public string receivers_name { get; set; } = "";
        [SaveToDB][FromFld] public string getter_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string getter_name { get; set; } = "";
        //[SaveToDB][FromFld] public decimal other_fee1_amount { get; set; } = 0;//
        //[SaveToDB][FromFld] public decimal other_fee2_amount { get; set; } = 0;//其他费用
        //[SaveToDB][FromFld] public string other_fee1_id  { get; set; }//
        //[SaveToDB][FromFld] public string other_fee2_id { get; set; }//
        //[FromFld(LOAD_PURPOSE.SHOW)] public string other_fee1_name { get; set; }//
        //[FromFld(LOAD_PURPOSE.SHOW)] public string other_fee2_name { get; set; }//
        //[SaveToDB][FromFld] public string fee_allocate_way { get; set; }//分配方式:price,count,不分摊

        //[FromFld("(case when sheet_attribute->'feeSheetInfo' is not null then concat(sheet_attribute->'feeSheetInfo'->>'fee_apportion_sheet_id',',',sheet_attribute->'feeSheetInfo'->>'total_fee_amount') else '' end)", LOAD_PURPOSE.SHOW)] public string feeApportionInfo { get; set; }

        public SheetBuy(SHEET_RETURN sheetReturn, LOAD_PURPOSE loadPurpose) : base("sheet_buy_main", "sheet_buy_detail", loadPurpose)
        {
            ConstructFun();
            sheet_type = sheetReturn == SHEET_RETURN.IS_RETURN ? SHEET_TYPE.SHEET_BUY_RETURN : SHEET_TYPE.SHEET_BUY;
        }
        public SheetBuy(LOAD_PURPOSE loadPurpose) : base("sheet_buy_main", "sheet_buy_detail", loadPurpose)
        {
            ConstructFun();
        }
        public SheetBuy() : base("sheet_buy_main", "sheet_buy_detail", LOAD_PURPOSE.SHOW)
        {
            ConstructFun();
            sheet_type = SHEET_TYPE.SHEET_BUY;
        }
        private void ConstructFun()
        {
            MainLeftJoin += @$"
left join (select sheet_id as order_sheet_id,sheet_no as order_sheet_no from sheet_buy_order_main) so on t.order_sheet_id = so.order_sheet_id
left join (select oper_id,oper_name as getter_name from info_operator where company_id=~COMPANY_ID) getter on t.getter_id=getter.oper_id
  left join(select sub_id,sub_name as other_fee1_name from cw_subject where company_id = ~company_id) cw1 on cw1.sub_id = t.other_fee1_id
  left join(select sub_id,sub_name as other_fee2_name from cw_subject where company_id = ~company_id) cw2 on cw2.sub_id = t.other_fee2_id
            ";
            DetailLeftJoin += $@" 

";
        }

	 
  
        protected class CInfoForApproveBuy : CInfoForApprove
        {
            public List<SheetRowBuyOrder> buyOrderRows = new List<SheetRowBuyOrder>();
        }

        public async Task<string> DoCommonJobBeforeSaveApprove(CMySbCommand cmd)
        {
            if (!IsImported)
            {
                string rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync FROM rs_seller rs
LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
 WHERE rs.reseller_company_id = {company_id} and rs.supplier_id = {supcust_id};";
                dynamic rsSeller = await CDbDealer.Get1RecordFromSQLAsync(rsSellerSql, cmd);
                string msg = "";
                string orderSource = "";
                if (orderSource == "") orderSource = order_source;
                if (rsSeller != null && rsSeller.sheet_sync.ToLower() == "true" && orderSource != "2fx" && order_sheet_id.IsInvalid())
                {
                    //msg = await base.SaveAndApprove(cmd, false);
                    dynamic ret = "";

                    try
                    {
                        ret = await SaveAsSaleSheet(cmd, this);

                        if (ret.msg != "")
                        {
                            return ret.msg;
                        }
                        else
                        {
                            this.bindSheetInfo = JsonConvert.SerializeObject(ret.bindSheetInfo);
                        }
                    }
                    catch (Exception e)
                    {
                        msg = "生成上级销售单出错";
                        MyLogger.LogMsg($"In SaveAndApprove,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);

                    }

                }
                return msg;

            }
            return "";
        }
        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd, CInfoForApproveBase info)
        {
            string msg = await DoCommonJobBeforeSaveApprove(cmd);
            return msg;
        }

        public async Task<dynamic> SaveAsSaleSheet(CMySbCommand cmd, dynamic oriSheet)
        {
            string err = "";
            var sheetType = oriSheet.SheetType;
            SheetSale sheet = JsonConvert.DeserializeObject<SheetSale>(JsonConvert.SerializeObject(oriSheet));
            // 检查同步对象订单里有没有同步的销售订单，如果存在则放弃生成同步销售单
            if (sheet.order_sheet_id.IsValid())
            {
                // 原始的销售单上有订单信息
            }
            // SheetBuyOrder sheet = new SheetBuyOrder();
            // sheet.SheetRows = oriSheet.SheetRows;
            // sheet.SheetRows = oriSheet.SheetRows;
            // TODO 替换为client_id对应的company_id
            string operKey = sheet.OperKey;
            string sellerId = sheet.seller_id;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            sheet.Init();
            sheet.SYNCHRONIZE_SHEETS = true;
            sheet.money_inout_flag = sheet.money_inout_flag * -1;
            sheet.order_source = "2fx";
            sheet.make_brief = "分销自动同步单据";
            // sheet.company_id = "";
            // TODO 操作员ID 暂定为1
            sheet.OperID = "1";
            // TODO 客户 此处为分销商 client
            string resellerInfoSql = $"select * from rs_seller  rss left join (select plan_id,brand_id,client_mapper from rs_plan) rsp on rss.plan_id = rsp.plan_id where reseller_company_id={companyID} and supplier_id = {oriSheet.supcust_id}";
            dynamic resellerInfo = await CDbDealer.Get1RecordFromSQLAsync(resellerInfoSql, cmd);

            sheet.company_id = (string)resellerInfo.company_id;

            if ((string) resellerInfo.client_mapper == "sellerAsClient")
            {
                string sellerClientSql = $"select rs_client_id from info_operator where oper_id = {sellerId}";
                dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sellerClientSql, cmd);
                // 第一种保护，业务员不在父账户客户列表，不允许开单
                // if(record.rs_client_id == null || record.rs_client_id == "")
                // {
                //     err = "当前业务员不在父账户客户列表";
                // }


                if (record.rs_client_id == null || record.rs_client_id == "")
                {
                    // 第二种，开到子公司客户上
                    sheet.supcust_id = (string)resellerInfo.client_id;
                }
                else
                {
                    sheet.supcust_id = (string)record.rs_client_id;
                }
            }
            else
            {
                sheet.supcust_id = (string)resellerInfo.client_id;
            }
            

            sheet.senders_id = "";
            sheet.senders_name = "";
            sheet.branch_id = "0";
            sheet.sheet_id = "";
            sheet.sheet_no = "";
            sheet.sheet_type = SHEET_TYPE.SHEET_SALE;
            if(sheetType == "CT")
            {
                sheet.SheetType = "T";
                sheet.sheet_type = SHEET_TYPE.SHEET_SALE_RETURN;
            }
            sheet.maker_id = "1";
            sheet.maker_name = "";
            sheet.approver_id = "";
            sheet.approve_time = "";
            sheet.approve_brief = "";

            // 抹掉订单信息
            sheet.order_sheet_id = "";
            sheet.order_sheet_no = "";
            sheet.order_sheet_time = "";

            foreach (SheetRowSale row in sheet.SheetRows)
            {
                string itemId = row.item_id;
                string itemIdSql = $"select item_brand,rs_mum_id from info_item_prop where item_id = {itemId} and company_id={companyID}";
                dynamic sonItemInfo = await CDbDealer.Get1RecordFromSQLAsync(itemIdSql, cmd);
                if (sonItemInfo.item_brand == null || sonItemInfo.item_brand == "")
                {
                    err = $"商品“{row.item_name}”没有选择品牌";break;
                }
                // 查询品牌是否在分销方案里，确保方案修改之后，不包括该品牌商品无法开同步单据
                // 不包括的商品之后是否支持分销商自己开单？
                dynamic sonBrandInfo = await CDbDealer.Get1RecordFromSQLAsync($"select rs_mum_id from info_item_brand where brand_id = {sonItemInfo.item_brand}",cmd);
                
                string planBrandSql = $"select * from rs_plan where plan_id = {resellerInfo.plan_id} AND ',' || brand_id || ',' LIKE '%,{sonBrandInfo.rs_mum_id},%';";
                dynamic planBrandRet = await CDbDealer.Get1RecordFromSQLAsync(planBrandSql, cmd);
                if (planBrandRet == null)
                {
                    err = $"商品“{row.item_name}”品牌不在分销方案内";
                }
                string fatherItemId = sonItemInfo.rs_mum_id;
                if (fatherItemId != null && fatherItemId != "")
                {
                    string querySql = $"select item_id,item_class,other_class,batch_level from info_item_prop where item_id = {fatherItemId} and company_id={sheet.company_id}";
                    dynamic itemInfo = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);
                    row.item_id = itemInfo.item_id;
                    row.classId = itemInfo.item_class;
                    row.other_class = itemInfo.other_class;
                    row.inout_flag = row.inout_flag * (-1);
                    if (((string)itemInfo.batch_level).IsInvalid() && row.produce_date.IsValid())
                    {
                        // 对应商品无产期
                        row.produce_date = "";
                    }
                }
                else
                {
                    err = $@"主公司档案没有商品:{row.item_name}";
                    break;
                }
                if (row.quantity > 0)
                {
                    row.trade_type = "X";
                }
                else if (row.quantity < 0)
                {
                    row.trade_type = "T";
                }

            }
            if (err == "")
                err = await sheet.Save(cmd, false);
            if (err != "") {
                return JsonConvert.SerializeObject(new {msg = err });
            }
            var ret = new { msg = "",bindSheetInfo = new { bindSheetId = sheet.sheet_id, bindSheetNo = sheet.sheet_no, bindSheetType = sheet.SheetType, companyId = sheet.company_id} };

            // return JsonConvert.SerializeObject(ret);
            return ret;
            // return err;
        }
        protected override void GetInfoForSave_SetQQ(SQLQueue QQ)
        {
			if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBuy();
			CInfoForApproveBuy info = (CInfoForApproveBuy)InfoForApprove;
			base.GetInfoForSave_SetQQ(QQ);
		}
		protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
			if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBuy();
			CInfoForApproveBuy info = (CInfoForApproveBuy)InfoForApprove;
			base.GetInfoForApprove_SetQQ(QQ);
            string sql = "";
            if (order_sheet_id != "")
            {
                sql = $@"
SELECT ip.item_name ,sd.* FROM sheet_buy_order_detail sd
LEFT JOIN info_item_prop ip on ip.company_id = sd.company_id  and ip.company_id = {company_id} and sd.item_id = ip.item_id

  WHERE sd.company_id = {company_id} and sd.sheet_id = {order_sheet_id}";
                QQ.Enqueue("order_detail", sql);
            }
            if (SheetRows.Count > 0)
            {
                string items_id = string.Join(",", SheetRows.Select(r => r.item_id));
                string mumItemsID = "";
                foreach (SheetRowMM row in SheetRows)
                {
                    if (row.son_mum_item != "")
                    {
                        if (mumItemsID != "") mumItemsID += ",";
                        mumItemsID += row.son_mum_item;
                    }
                }
                sql = $"select item_id,sum(stock_qty) total_qty from stock where company_id={company_id} and item_id in ({items_id}) group by item_id";
                QQ.Enqueue("total_stock", sql);
                sql = $"select item_id,cost_price_avg, item_cost_price_suspect, cost_price_recent  from info_item_prop where company_id={company_id} and item_id in ({items_id})";
                QQ.Enqueue("cost_avg_info", sql);
                string condi = $" mu.item_id in ({items_id}) ";
                if (mumItemsID != "") condi += $"  or mu.item_id in ({mumItemsID}) ";
                sql = $@"select mu.item_id,mu.unit_no as item_unit_no,mu.unit_factor item_unit_factor from info_item_multi_unit mu where mu.company_id =  {company_id} and  ({condi});";
                QQ.Enqueue("recent_price", sql);
                sql = $"select setting from company_setting where company_id={company_id};";
                QQ.Enqueue("setting", sql);
                //当红冲时使用以下数据，判断商品采购为该商品的最近第X次，recent_time表示最近第X次，只获取该公司近一年的未红冲的采购数据
                if (red_flag == "2")
                {
                    sql =@$"select item_id,happen_time,recent_time from (select sd.item_id, sd.happen_time,ROW_NUMBER() over (partition by sd.item_id order by sd.happen_time DESC) as recent_time  from sheet_buy_detail sd left join sheet_buy_main sm on sd.sheet_id= sm.sheet_id and sd.company_id = sm.company_id where sd.company_id = {company_id} and sd.item_id in({items_id}) and sm.red_flag is NULL and sm.approve_time is not null  and sd.inout_flag = 1 and sd.happen_time >= (now() - interval '1 years') 
                      group by sd.item_id, sd.happen_time, sd.sheet_id ) as subquery where recent_time <= 3;";
                    QQ.Enqueue("recent_time", sql);
                }
            }
            string sub_ids = payway1_id;
            if (payway2_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway2_id;
            }
            if (payway3_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway3_id;
            }
            if (sub_ids != "")
            {
                sql = $"select sub_id, sub_type, is_order from cw_subject where company_id={company_id} and sub_id in ({sub_ids}) and sub_id not in (select case when (s.setting->>'feeOutSubForKS')::int is null then -1 else (s.setting->>'feeOutSubForKS')::int end sub_id from  company_setting s where s.company_id = {company_id});";
                QQ.Enqueue("payway_type", sql);
            }
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
			CInfoForApproveBuy info = (CInfoForApproveBuy)InfoForApprove;
			base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (sqlName == "total_stock")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowBuy row in SheetRows)
                    {
                        if (row.item_id == rec.item_id) row.old_total_qty = CPubVars.ToDecimal(rec.total_qty == "" ? 0 : rec.total_qty);
                    }
                }
            }
            else if (sqlName == "cost_avg_info")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowBuy row in SheetRows)
                    {
                        if (row.item_id == rec.item_id)
                        {
                            row.cost_price_avg = rec.cost_price_avg != "" ? rec.cost_price_avg : "0";
                            //   row.old_cost_amt = Convert.ToDouble(rec.cost_amt != "" ? rec.cost_amt : 0);
                            row.item_cost_price_suspect = Convert.ToBoolean(rec.item_cost_price_suspect != "" ? rec.item_cost_price_suspect : false);
                            if (rec.cost_price_recent != "")
                            {
                                dynamic pStr = JsonConvert.DeserializeObject(rec.cost_price_recent);
                                //Console.WriteLine("输出转换后的值：{0}" + "\n" + "转换后的类型：{1} " + "\n", pStr, pStr.GetType());
                                row.cost_price_recent1 = pStr.avg1;
                                row.cost_price_recent2 = pStr.avg2;
                                row.cost_price_recent3 = pStr.avg3;
                                row.recent_price_qty1 = pStr.qty1;
                                row.recent_price_qty2 = pStr.qty2;
                                row.recent_price_qty3 = pStr.qty3;
                            }
                            else
                            {
                                row.cost_price_recent1 = 0;
                                row.cost_price_recent2 = 0;
                                row.cost_price_recent3 = 0;
                                row.recent_price_qty1 = 0;
                                row.recent_price_qty2 = 0;
                                row.recent_price_qty3 = 0;
                            }
                        }
                    }
                }
            }
            else if (sqlName == "recent_price") //判断 保存最近售价
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                if (records != null)
                {
                    Dictionary<string, string> dicUniqueItems = new Dictionary<string, string>();
                    foreach (SheetRowBuy row in SheetRows)
                    {
                        //if (row.quantity * row.inout_flag > 0) continue;
                        if (row.real_price == 0) continue;
                        if (dicUniqueItems.ContainsKey(row.item_id)) continue;
                        dicUniqueItems.Add(row.item_id, row.item_id);
                        foreach (dynamic rec in records)
                        {
                            var itemID = row.son_mum_item != "" && !row.attrRememberPrice ? row.son_mum_item : row.item_id;
                            if (rec.item_id == itemID)
                            {
                                SheetRowBuy BuyRow = new SheetRowBuy();
                                BuyRow.item_id = rec.item_id;
                                BuyRow.unit_no = rec.item_unit_no;

                                if (rec.item_unit_factor == "") continue;
                                else
                                {
                                    BuyRow.real_price = CPubVars.ToDecimal(Math.Round((row.real_price / row.unit_factor * CPubVars.ToDecimal(rec.item_unit_factor)), 2));
                                    decimal orig_price = 0;
                                    if (row.orig_price != null && row.orig_price != "")
                                    {
                                        orig_price = CPubVars.ToDecimal(row.orig_price);
                                        BuyRow.orig_price = (Math.Round((orig_price / row.unit_factor * CPubVars.ToDecimal(rec.item_unit_factor)), 2)).ToString();
                                    }
                                }
                                BuyRow.son_mum_item = row.son_mum_item;
                                BuyRow.quantity = row.quantity;
                                info.UnitPriceRows.Add(BuyRow);
                            }
                        }
                        if (info.ErrMsg != "") return;
                    }
                }
            }
            else if (sqlName == "setting")
            {
                dynamic rec= CDbDealer.Get1RecordFromDr(dr,false);
                if(rec!=null && rec.setting!="")
                   info.CompanySetting =JsonConvert.DeserializeObject(rec.setting); 
            }
            else if(sqlName == "order_detail")
            {
                info.buyOrderRows = CDbDealer.GetRecordsFromDr<SheetRowBuyOrder>(dr, false);
                foreach(var row in SheetRows)
                {
                    foreach(var orderRow in info.buyOrderRows)
                    {
                        var orderQty = orderRow.quantity * orderRow.unit_factor;
                        if(row.item_id == orderRow.item_id&&row.order_qty == orderQty)
                        {
                            var leftQty = orderQty - orderRow.done_qty - row.quantity * row.unit_factor * row.inout_flag;
                            //if (leftQty < 0)
                            //{
                            //    info.ErrMsg += $"{orderRow.item_name}:采购数量大于采购订单中的可用还货数量;";
                            //}
                            break;
                        }
                    }
                }
            }
            else if (sqlName == "recent_time")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowBuy row in SheetRows)
                    {
                        // 将 rec.happen_time 转换为 DateTime 类型
                        DateTime recHappenTime = DateTime.Parse(rec.happen_time);
                        // 将 happen_time 转换为 DateTime 类型
                        DateTime HappenTime = DateTime.Parse(happen_time);
                        if (row.item_id == rec.item_id && HappenTime == recHappenTime)
                        {
                            row.recent_time = CPubVars.ToDecimal(rec.recent_time);
                        }
                    }
                }
            }
            else if (sqlName == "payway_type")
            {
                info.PaywaysInfo = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
            info.SheetRows = SheetRows;
        }

        public List<SheetRowBuy> MergeSheetRows_Buy(List<SheetRowBuy> rows, bool bIgnoreNativeQty = false)
        {
            Dictionary<string, SheetRowBuy> rowsDict = new Dictionary<string, SheetRowBuy>();
            foreach (SheetRowBuy sheetRow in rows)
            {
                if (sheetRow.inout_flag == 0) continue;
                string cur_branch = sheetRow.branch_id;
                string skey = sheetRow.item_id;
                if (bIgnoreNativeQty && sheetRow.quantity < 0) continue;
                SheetRowBuy curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    string s = JsonConvert.SerializeObject(sheetRow);
                    curRow = JsonConvert.DeserializeObject<SheetRowBuy>(s);
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_factor = 1;
                    curRow.real_price = sheetRow.real_price / sheetRow.unit_factor;
                    curRow.StockQty = sheetRow.StockQty;
                    curRow.SellPendQty = sheetRow.SellPendQty;
                    curRow.HasStockQty = sheetRow.HasStockQty;
                    curRow.SaleOrderQty = sheetRow.SaleOrderQty;
                    curRow.recent_price_qty1 = sheetRow.recent_price_qty1;
                    curRow.recent_price_qty2 = sheetRow.recent_price_qty2;
                    curRow.recent_price_qty3 = sheetRow.recent_price_qty3;
                    curRow.cost_price_recent1 = sheetRow.cost_price_recent1;
                    curRow.cost_price_recent2 = sheetRow.cost_price_recent2;
                    curRow.cost_price_recent3 = sheetRow.cost_price_recent3;
                    curRow.recent_time = sheetRow.recent_time;
                    rowsDict.Add(skey, curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                    curRow.sub_amount += sheetRow.sub_amount;
                }
            }
            List<SheetRowBuy> newList = new List<SheetRowBuy>();
            foreach (var k in rowsDict)
            {
                var row = k.Value;
                newList.Add(k.Value);
            }
            return newList;
        }
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            await base.OnSheetIDGot(cmd, sheetID, info1);
            if (info1.ErrMsg != "") return;
            string sql = "";

            if (FIXING_ARREARS) return;
            if (IsImported) return;

            CInfoForApproveBuy info = (CInfoForApproveBuy)info1;

            string dealing_happen_time = happen_time;
            //红冲时red_flag为 2 
            if (red_flag == "2")
            {
                dynamic data = await CDbDealer.Get1RecordFromSQLAsync($"select happen_time from sheet_buy_main where company_id = {company_id} and sheet_id = {red_sheet_id}", cmd);
                if (data != null) dealing_happen_time = data.happen_time;
            }
            List<SheetRowCostPrice> costPriceRows = new List<SheetRowCostPrice>();
            string content = "";
            var SheetRows_tmp = MergeSheetRows_Buy(SheetRows, true);
            foreach (SheetRowBuy row in SheetRows_tmp)
            {
                // if (row.quantity == 0) continue;
                if (row.recent_price_qty1 == 0)
                {
                    row.recent_price_qty1 = row.quantity * row.unit_factor;
                    row.recent_price_qty2 = row.quantity * row.unit_factor;
                    row.recent_price_qty3 = row.quantity * row.unit_factor;
                    row.cost_price_recent1 = Math.Round(row.sub_amount / (row.quantity * row.unit_factor), 6);
                    row.cost_price_recent2 = Math.Round(row.sub_amount / (row.quantity * row.unit_factor), 6);
                    row.cost_price_recent3 = Math.Round(row.sub_amount / (row.quantity * row.unit_factor), 6);
                }
                else
                {
                    //红冲情况下还需判断订单时间，如果为最近3次则需要修改最近X次成本(cost_price_recent)
                    if (red_flag == "2")
                    {
                        if (row.recent_time == 1)//若最近一次订单红冲
                        {//avg3与qty3无法恢复,故以新的avg2和qty2进行替代
                            decimal new_qty2 = row.recent_price_qty3 - row.recent_price_qty1;
                            decimal new_qty1 = row.recent_price_qty2 - row.recent_price_qty1;
                            if (new_qty1 != 0)
                                row.cost_price_recent1 = Math.Round((row.cost_price_recent2 * row.recent_price_qty2 - row.sub_amount) / new_qty1, 6);
                            else
                                row.cost_price_recent1 = 0;
                            row.recent_price_qty1 = new_qty1;
                            if (new_qty2 != 0)
                                row.cost_price_recent2 = Math.Round((row.cost_price_recent3 * row.recent_price_qty3 - row.sub_amount) / new_qty2, 6);
                            else
                                row.cost_price_recent2 = 0;
                            row.recent_price_qty2 = new_qty2;
                            row.recent_price_qty3 = row.recent_price_qty2;
                            row.cost_price_recent3 = row.cost_price_recent2;
                            content = "recent_time = 1";
                        }
                        else if (row.recent_time == 2)//若最近第二次订单红冲
                        {//avg1和qty1都不变,avg3与qty3以新的avg2和qty2进行替代
                            decimal ori_qty = row.recent_price_qty2 - row.recent_price_qty1;
                            decimal ori_price = 0;
                            if (ori_qty != 0)
                                ori_price = Math.Round((row.cost_price_recent2 * row.recent_price_qty2 - row.cost_price_recent1 * row.recent_price_qty1) / ori_qty, 6);
                            decimal new_qty2 = row.recent_price_qty3 - ori_qty;
                            decimal new_avg2 = 0;
                            if (new_qty2 != 0)
                                new_avg2 = Math.Round((row.cost_price_recent3 * row.recent_price_qty3 - ori_price * ori_qty) / new_qty2, 6);
                            row.cost_price_recent2 = new_avg2;
                            row.recent_price_qty2 = new_qty2;
                            row.cost_price_recent3 = new_avg2;
                            row.recent_price_qty3 = new_qty2;
                            content = "recent_time = 2";
                        }
                        else if (row.recent_time == 3)//若最近第三次订单红冲
                        {//avg3与qty3以avg2和qty2进行替代
                            row.cost_price_recent3 = row.cost_price_recent2;
                            row.recent_price_qty3 = row.recent_price_qty2;
                            content = "recent_time = 3";
                        }
                        else content = "recent_time = 0";
                        //Console.WriteLine(content);
                    }
                    else
                    {
                        row.recent_price_qty3 = row.recent_price_qty2 + row.quantity * row.unit_factor;
                        if (row.recent_price_qty3 != 0)
                            row.cost_price_recent3 = Math.Round((row.sub_amount + row.cost_price_recent2 * row.recent_price_qty2) / row.recent_price_qty3, 6);
                        else
                            row.cost_price_recent3 = 0;
                        row.recent_price_qty2 = row.recent_price_qty1 + row.quantity * row.unit_factor;
                        if (row.recent_price_qty2 != 0)
                            row.cost_price_recent2 = Math.Round((row.sub_amount + row.cost_price_recent1 * row.recent_price_qty1) / row.recent_price_qty2, 6);
                        else
                            row.cost_price_recent2 = 0;
                        row.cost_price_recent1 = row.sub_amount / (row.quantity * row.unit_factor);
                        row.recent_price_qty1 = row.quantity * row.unit_factor;
                    }
                }
                content = "{\"avg1\": " + row.cost_price_recent1 + ", \"qty1\": " + row.recent_price_qty1 + ", \"avg2\": " + row.cost_price_recent2 + ", \"qty2\": " + row.recent_price_qty2 + ", \"avg3\": " + row.cost_price_recent3 + ", \"qty3\": " + row.recent_price_qty3 + "}";
                sql = "update info_item_prop set cost_price_recent='" + content + "' where company_id =" + company_id + " and item_id =" + row.item_id + ";";
                //Console.WriteLine(content);
                cmd.CommandText = sql;
                await cmd.ExecuteScalarAsync();
            }
            foreach (SheetRowBuy row in SheetRows)
            {
                SheetRowCostPrice costRow = new SheetRowCostPrice();
                //costRow.batch_id = row.batch_id;
                costRow.item_id = row.item_id;
                costRow.unit_no = row.unit_no;
                costRow.inout_flag = row.inout_flag;
                costRow.quantity = row.quantity * row.unit_factor;
                costRow.sub_amount = row.sub_amount;//★★★ 审核采购单时先重算采购金额，如有分摊金额在分摊单审核时按增量重算
                costRow.cost_price_avg = row.cost_price_avg.IsValid() ? Decimal.Parse(row.cost_price_avg.ToString(), System.Globalization.NumberStyles.Float) : 0;//审核单据前的商品的加权平均成本（小单位）
                costRow.item_cost_price_suspect = row.item_cost_price_suspect;
                costRow.old_total_qty = row.old_total_qty;
                costPriceRows.Add(costRow);
            }

            await UpdateCostPriceAvg(cmd, costPriceRows, dealing_happen_time);

            #region 更新现金银行余额
            string sql_cb = "";
            if (info.BizStartPeriod != "" && info.PaywaysInfo != null && !IsImported)
            {
                Dictionary<string, decimal> pws = new Dictionary<string, decimal>();
                Subject pw1 = info.PaywaysInfo.Find(p => p.sub_id == payway1_id && p.sub_type == "QT");
                if (pw1 != null && payway1_amount != 0)
                {
                    if (!pws.ContainsKey(payway1_id)) pws.Add(payway1_id, payway1_amount);
                    else pws[payway1_id] += payway1_amount;
                }
                Subject pw2 = info.PaywaysInfo.Find(p => p.sub_id == payway2_id && p.sub_type == "QT");
                if (pw2 != null && payway2_amount != 0)
                {
                    if (!pws.ContainsKey(payway2_id)) pws.Add(payway2_id, payway2_amount);
                    else pws[payway2_id] += payway2_amount;
                }
                Subject pw3 = info.PaywaysInfo.Find(p => p.sub_id == payway3_id && p.sub_type == "QT");
                if (pw3 != null && payway3_amount != 0)
                {
                    if (!pws.ContainsKey(payway3_id)) pws.Add(payway3_id, payway3_amount);
                    else pws[payway3_id] += payway3_amount;
                }
                if (pws.Count() > 0)
                {
                    sql_cb = base.UpdateCashBankBalance(pws);
                    if (sql_cb.Trim() != "") AddExecSQL(sql_cb);
                }
            }
            if (sql_cb != "")
            {
                sql += sql_cb;
            }
            #endregion

            #region 采购费用分摊

            if (SheetType == "CT")
            {
                foreach(SheetRowBuy row in SheetRows)
                {
                    if (row.allocate_amount != 0) info.ErrMsg = "采购退货单不支持分摊，请清除分摊金额";
                }
            }
            if (feeSheetInfo != "")
            {
                FeeSheetInfo feeSheetInfoObj = JsonConvert.DeserializeObject<FeeSheetInfo>(feeSheetInfo);
                if (feeSheetInfoObj.sheetRowFeeAppoList.Count > 0)
                {
                    SheetFeeApportion feeAppoSheet = new SheetFeeApportion(LOAD_PURPOSE.APPROVE);
                    feeAppoSheet.company_id = company_id;
                    feeAppoSheet.OperID = OperID;
                    feeAppoSheet.fromBuySheet = true;//★★★
                    feeAppoSheet.SheetType = "FYFT";
                    feeAppoSheet.happen_time = happen_time;
                    feeAppoSheet.make_brief = feeSheetInfoObj.make_brief;
                    feeAppoSheet.seller_id = Convert.ToInt32(seller_id);
                    feeAppoSheet.buy_sheet_id = sheetID;//sheet_attribute
                    feeAppoSheet.fee_allocate_way = feeSheetInfoObj.fee_allocate_way;//sheet_attribute
                    feeAppoSheet.total_fee_amount = feeSheetInfoObj.total_fee_amount.ToString();//sheet_attribute
                    feeAppoSheet.BuySheetRows = SheetRows;//sheet_attribute
                    feeAppoSheet.FeeSheetRows = feeSheetInfoObj.sheetRowFeeAppoList;//sheet_attribute
                    if (red_flag == "")
                    {
                        info.ErrMsg = await feeAppoSheet.SaveAndApprove(cmd, false);
                        if (info.ErrMsg != "") return;
                        fee_apportion_sheet_id = feeAppoSheet.sheet_id;
                    }
                    else
                    {
                        info.ErrMsg = await feeAppoSheet.Red(cmd, company_id, feeSheetInfoObj.fee_apportion_sheet_id, OperID, "红冲采购单", false);
                        if (info.ErrMsg != "") return;
                    }

                }

            }

            #endregion


            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }



            #region 二级分销
 
//            if (!IsImported && red_flag!="2")
//            {
//                string orderSource = "";
//                if (sheet_id != "")
//                {
//                    string orderSourceSql = $"select  * from sheet_buy_main where company_id = {company_id} and supcust_id = {supcust_id} and sheet_id ={sheet_id};";
//                    dynamic dbSheet = await CDbDealer.Get1RecordFromSQLAsync(orderSourceSql, cmd);
//                    if(dbSheet!=null) orderSource = dbSheet.order_source;
//                }
//                if (orderSource == "") orderSource = order_source;
//                string rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync FROM rs_seller rs
//LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
// WHERE rs.reseller_company_id = {company_id} and rs.supplier_id = {supcust_id};";
//                dynamic rsSeller = await CDbDealer.Get1RecordFromSQLAsync(rsSellerSql, cmd);
//                string msg = "";
//                if (rsSeller != null && rsSeller.sheet_sync.ToLower() == "true" && orderSource != "2fx" && order_sheet_id.IsInvalid())
//                {
//                    //msg = await base.SaveAndApprove(cmd, false);
//                    string ret = "";

//                    try
//                    {
//                        ret = await SaveAsSaleSheet(cmd, this);
//                        var ob = JsonConvert.DeserializeObject(ret);
//                    }
//                    catch (Exception e)
//                    {
//                        if (ret != "") msg = ret;
//                        else msg = "生成上级销售单出错";
//                        info.ErrMsg = msg;
//                        MyLogger.LogMsg($"In SaveAndApprove,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);
                         
//                    }
//                    this.bindSheetInfo = ret;  
//                } 
//            }
            
            #endregion
        }

        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            if (FIXING_ARREARS) return "";
            string sql = "";
            if (!IsImported)
            {
                sql = base.GetApproveSQL(info1);
            }

            CInfoForApproveBuy info = (CInfoForApproveBuy)info1;
            string rememberBranchID = "0";

            //string ic = JsonConvert.SerializeObject(info.CompanySetting);
            //Boolean rememberBuyPriceByBranch = JsonConvert.DeserializeObject(info.CompanySetting)["rememberBuyPriceByBranch"];
            //if (ic != null && rememberBuyPriceByBranch)

            bool rememberBuyPriceByBranch = false;
            if(info.CompanySetting!=null && info.CompanySetting.rememberBuyPriceByBranch != null)
            {
                rememberBuyPriceByBranch = ((string)info.CompanySetting.rememberBuyPriceByBranch).ToLower() == "true";
            }
            
            
            if (info.CompanySetting != null && rememberBuyPriceByBranch )
            {
                rememberBranchID = branch_id;
            }
            foreach (var row in info.UnitPriceRows)
            {
                //var itemID = row.son_mum_item != ""&&!row.attrRememberPrice ? row.son_mum_item:row.item_id;
                var itemID = row.item_id;
                if (row.orig_price != null && row.orig_price != "")
                    sql += $@"insert into supplier_recent_prices (company_id,supcust_id,branch_id,item_id,unit_no,recent_price,recent_orig_price,happen_time) values ({company_id},{supcust_id},{rememberBranchID},{itemID},'{row.unit_no}',{row.real_price},{row.orig_price},'{happen_time}') 
                        on conflict(company_id,supcust_id,branch_id,item_id,unit_no) do update set recent_price={row.real_price},recent_orig_price={row.orig_price},happen_time = '{happen_time}';";
                else sql += $@"insert into supplier_recent_prices (company_id,supcust_id,branch_id,item_id,unit_no,recent_price,happen_time) values ({company_id},{supcust_id},{rememberBranchID},{itemID},'{row.unit_no}',{row.real_price},'{happen_time}') 
                        on conflict(company_id,supcust_id,branch_id,item_id,unit_no) do update set recent_price={row.real_price},happen_time = '{happen_time}' ;";

            }

            string sqlUpdateRecentProduceDate = "";
            string sqlUpdateOrderBuy = "";
            var finishOrderToBuy = true;
            string sNow = CPubVars.GetDateText(DateTime.Now);
            var buyCount = 0;
            foreach (var row in info.SheetRows)
            {
                if (!string.IsNullOrEmpty(row.virtual_produce_date))
                {
                    sqlUpdateRecentProduceDate += $"insert into item_recent_produce_date (company_id,item_id,produce_date,happen_time) values ({company_id},{row.item_id},'{row.virtual_produce_date}','{sNow}') on conflict(company_id,item_id) do update set produce_date = '{row.virtual_produce_date}',happen_time='{sNow}';";
                }
                if (row.order_qty != 0)
                {
                    buyCount += 1;
                    foreach (var bRow in info.buyOrderRows)
                    {
                        var orderSheetQty = bRow.quantity * bRow.unit_factor;
                        if(bRow.item_id == row.item_id && row.order_qty == orderSheetQty)
                        {
                            decimal done_qty = bRow.done_qty;
                            var converted_qty = row.quantity * row.unit_factor*row.inout_flag + done_qty;
                            var left_qty = row.order_qty - converted_qty;
                            if (Math.Abs(left_qty) > 0.01m ) finishOrderToBuy = false;
                            sqlUpdateOrderBuy += $"update sheet_buy_order_detail set done_qty ={converted_qty} where company_id ={company_id} and sheet_id = {order_sheet_id} and item_id ={bRow.item_id} and unit_no ='{bRow.unit_no}' and quantity = '{bRow.quantity}';";

                        }
                        
                    }
                }
            }
            if (order_sheet_id != "")
            {
                var orderCount = 0;
                foreach (var bRow in info.buyOrderRows)
                {
                    var orderSheetQty = bRow.quantity * bRow.unit_factor;
                    var left_qty = orderSheetQty - bRow.done_qty;
                    if (Math.Abs(left_qty) > 0.001m) orderCount += 1;

                }
                if (orderCount> buyCount&& finishOrderToBuy) finishOrderToBuy = false;

                if (finishOrderToBuy)
                {
                    var buy_order_status = "qb";
                    if (red_flag == "2") buy_order_status = "";
                    sqlUpdateOrderBuy += $"update sheet_buy_order_main set buy_order_status ='{buy_order_status}' where company_id ={company_id} and sheet_id = {order_sheet_id}; ";
                }
                else
                {
                    sqlUpdateOrderBuy += $"update sheet_buy_order_main set buy_order_status ='bf' where company_id ={company_id} and sheet_id = {order_sheet_id}; ";
                }
            }

            sql += sqlUpdateRecentProduceDate + sqlUpdateOrderBuy;
            return sql;
        }


        /*public override async Task<JsonResult> ToVoucherRows(CMySbCommand cmd, string sheetID, SheetCwVoucher sheetCwVoucher, Dictionary<string, decimal> payways)
        {
            string subsID = "";
            string condi = "";// 库存商品
            string payCondi = "";
            int subLen = 1;
            if (payways.Count == 0)
            {
                if (payway1_id != "" && payway1_amount != 0) payways.Add(payway1_id, payway1_amount * money_inout_flag);
                if (payway2_id != "" && payway2_amount != 0) payways.Add(payway2_id, payway2_amount * money_inout_flag);
                if (left_amount != 0) payways.Add("left", left_amount * money_inout_flag);
                if (now_disc_amount != 0) payways.Add("disc", now_disc_amount * money_inout_flag);
                if (total_amount != 0) payways.Add("total", total_amount * money_inout_flag);
            }
            if (payways == null || payways.Count == 0)
            {
                return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
            }
            foreach (var payway in payways)
            {
                if (payway.Key == "total") continue;
                else if (payway.Key == "left") condi += $@"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='2202' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='2202')  order by sub_code limit 1 )"; // 应付账款
                else if (payway.Key == "disc") condi += $@"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_code=560304 )"; // 财务费用-现金折扣（不允许增加子科目）
                else
                {
                    if (subsID != "") subsID = subsID + ",";
                    subsID += payway.Key;
                }
                subLen++;
            }
            
            if (subsID != "") payCondi += $"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_id in ({subsID}) )";

            string sql = $@"( 
                select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='1405' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='1405')  order by sub_code limit 1 
                ) {condi} {payCondi} ";
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (records == null || records.Count < subLen) return new JsonResult(new { result = "Error", msg = "缺少生成凭证的相关科目，请添加" });
            if (records.Count > subLen) return new JsonResult(new { result = "Error", msg = "生成凭证的相关科目有重复科目代码，请修改" });

            foreach (var rec in records)
            {
                if (rec.status == null || rec.status == "") rec.status = 1;
                if (Convert.ToInt16(rec.status) == 0) return new JsonResult(new { result = "Error", msg = "相关科目已停用，请检查" });
                CwRowVoucher cwRow = new CwRowVoucher();
                cwRow.business_sheet_type = SheetType;
                cwRow.business_sheet_id = sheetID;
                cwRow.sub_id = rec.sub_id;
                cwRow.remark = payways["total"] <= 0 ? "采购商品" : "采购退货";
                if (red_flag == "2") cwRow.remark = payways["total"] <= 0 ? "红冲采购商品" : "红冲采购退货";
                decimal changeAmt = 0;
                foreach (var payway in payways)
                {
                    changeAmt = payway.Value;

                    if (payway.Key == rec.sub_id || (payway.Key == "left" && rec.sub_code.ToString().Substring(0, 4) == "2202") || (payway.Key == "disc" && rec.sub_code == "560304"))
                    {
                        if (changeAmt >= 0) cwRow.debit_amount = changeAmt.ToString();
                        else cwRow.credit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = changeAmt.ToString();
                        break;
                    }
                    else if (rec.sub_code.ToString().Substring(0, 4) == "1405" && payway.Key == "total") // 库存商品
                    {
                        if (changeAmt >= 0) cwRow.credit_amount = changeAmt.ToString();
                        else cwRow.debit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = (-1 * changeAmt).ToString();
                        break;
                    }
                }

                sheetCwVoucher.SheetRows.Add(cwRow);
            }

            return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
        }*/

        //public string CheckSubOtherFeeValid()
        //{
        //    if ((other_fee1_id == "" && other_fee1_amount != 0) || (other_fee2_id == "" && other_fee2_amount != 0))
        //    {
        //        return "费用名称错误";
        //    }
        //    if (fee_allocate_way == "" && (other_fee1_amount != 0 || other_fee2_amount != 0))
        //    {
        //        return "请选择分摊方式";
        //    }

        //    return  "" ;
        //}

        //protected override async Task<string> CheckSheetRowValid(CMySbCommand cmd)
        //{
        //    foreach (dynamic row in this.SheetRows)
        //    {
        //        string branchID = row.branch_id;
        //        string branchName = row.branch_name;
        //        if (branchID.IsInvalid())
        //        {
        //            branchID = branch_id;
        //            branchName = branch_name;
        //        }
        //        if (row.branch_position == "0" || string.IsNullOrWhiteSpace(row.branch_position)) continue;
        //        string sql = $"select flow_id from info_branch_position where company_id = {company_id} and branch_position = {row.branch_position} and branch_id = {branchID};";
        //        dynamic record = await CDbDealer.Get1RecordFromSQLAsync($"select flow_id from info_branch_position where company_id = {company_id} and branch_position = {row.branch_position} and branch_id = {branchID};", cmd);
        //        if (record == null)
        //        {
        //            return $"{branchName}不存在库位：{row.branch_position_name}";
        //        }
        //    }
        //    return "OK";

        //}
    }
}

