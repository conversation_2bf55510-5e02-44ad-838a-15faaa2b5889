﻿@page
@model ArtisanManage.Pages.BaseInfo.CombineTempViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1">
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var RowIndex = -1;
        $(document).ready(function () {
            
        @Html.Raw(Model.m_showFormScript)

        @Html.Raw(Model.m_createGridScript)
            $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);
            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                if (ForSelect) {
                    if (args.columnindex === 1) return
                    var checkedRows = []
                    var rows = $('#gridItems').jqxGrid('getrows')
                    var row = rows.find(r => {
                        return r.uid === args.rowindex
                    })
                    checkedRows.push(row)
                    var msg = {
                        msgHead: 'TempView', action: 'selectTemp', checkedRows: checkedRows
                    }
                    window.parent.postMessage(msg, '*');
                } else {
                    if (args.datafield === "model_name") {
                        if (args.originalEvent.button == 2) return;//右击
                        var sheet_id = args.row.bounddata.sheet_id
                        window.parent.newTabPage('组装拆分模型', `BaseInfo/CombineTemplate?sheet_id=${sheet_id}`, window);
                    }
                }

            });

            $("#gridItems").jqxGrid('beforeRowRender', function (divRow, rowData) {
                if (rowData.sheet_status == '已红冲')
                    divRow.style.color = '#888'
                else if (rowData.sheet_status == '红字单')
                    divRow.style.color = '#f00'
                else
                    divRow.style.color = '#000'

            })

            QueryData();

        })

        function btnTemplateSelected(type, value) {
            switch (type) {
                case 'sale':
                    tmp_sale = value;
                    localStorage.setItem('_SaleSheetView_TemplateSelected_SALE', value);
                    //window['_SaleSheetView_TemplateSelected_SALE'] = value;
                    break;
                case 'return':
                    tmp_return = value;
                    localStorage.setItem('_SaleSheetView_TemplateSelected_RETURN', value);
                    //window['_SaleSheetView_TemplateSelected_RETURN'] = value;
                    break;
                case 'sum':
                    tmp_sum = value;
                    localStorage.setItem('_SaleSheetView_TemplateSelected_SALESUM', value);
                    //window['_SaleSheetView_TemplateSelected_SALESUM'] = value;
                    break;
                default: return;
            }
        }
        function btnSelectItems_click() {
            var rows = window.g_checkedRows
            var checkedRows = []
            for (var id in rows) {
                var row = rows[id]
                checkedRows.push(row)
            }

            if (checkedRows.length === 0) {
                bw.toast('请选择模型')
                return
            } else if (checkedRows.length > 1) {
                bw.toast('最多选择一个模板')
            }
            var msg = {
                msgHead: 'TempView', action: 'selectTemp', checkedRows: checkedRows
            }
            window.parent.postMessage(msg, '*');
        }
        function btnAddItem_click(e) {
            if (ForSelect){
                window.top.newTabPage('组装拆分模型', `/BaseInfo/CombineTemplate`, window);
            }
            else{
                window.parent.newTabPage('组装拆分模型', `BaseInfo/CombineTemplate`, window);
            }
        }
        function onGridRowContextMenuClick(gridID, menuID, rowIndex) {

            var rows = $('#gridItems').jqxGrid('getrows')

            if (menuID == 'BatchOperation') {
                $('#gridItems').jqxGrid('showcolumn', 'sys_check')
                var a = $('#popBatchOperation')
                $('#popBatchOperation').css("display", "block")
            }

        }
    </script>
    <style>

    </style>

</head>

<body style="overflow:hidden;">

    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <div style="width:180px;">
            <button onclick="QueryData()" style="margin-left:20px;margin-bottom:10px;" class="main-button">查询</button>
            <button id="btnAddItem" style="width:60px;" onclick="btnAddItem_click()" class="margin">添加</button>
            @if (Model.ForSelect)
            {
                <button id="btnSelectItems" style="width:60px;" class="margin" onclick="btnSelectItems_click()">选择</button>
            }

        </div>

    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>
</body>
</html>