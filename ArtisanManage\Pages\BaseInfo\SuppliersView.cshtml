@page
@model ArtisanManage.Pages.BaseInfo.SuppliersViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">

        var frame = "SupplierEdit";
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据此对象属性修改对应字段：', rs.data.record);
            if (rs.data.msgHead == frame) {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = rs.data.record;
                        rows[0].i = rows[0].supcust_id;
                        rows[0].status = rows[0].cls_status_name;

                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "sup_name",rs.data.record.sup_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "boss_name", rs.data.record.boss_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "sup_tel", rs.data.record.sup_tel);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "sup_addr", rs.data.record.sup_addr);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "status", rs.data.record.cls_status_name);
                }
                $("#popItem").jqxWindow('close');
            }

            console.log(rs.data);
        });

    	var newCount = 1;

            function btnAddItem_click(e) {
                $('#popItem').jqxWindow('open');
                $("#popItem").jqxWindow('setContent', `<iframe src="${frame}?operKey=${g_operKey}" width="100%" height="100%" scrolling="auto" frameborder="no"></iframe>`);
            }
        function onGridRowEdit(rowIndex) {
            var supcust_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'i');
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="SupplierEdit?operKey=' + g_operKey + '&supcust_id=' + supcust_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }
        function attachContextMenu() {
            function isRightClick(event) {
                var rightclick;
                if (!event) var event = window.event;
                if (event.which) rightclick = (event.which == 3);
                else if (event.button) rightclick = (event.button == 2);
                return rightclick;
            }


        }

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {
                    // event arguments.
                    var args = event.args;
                    if (args.datafield == "sup_name") {
                        /*var supcust_id = args.row.bounddata.supcust_id;
                        var sup_name = args.row.bounddata.sup_name;
                        RowIndex = args.rowindex;
                        if (ForSelect) {
                            // $("#popItem").jqxWindow('setContent', '<iframe src="ClientEdit?operKey=' + g_operKey + '&supcust_id=' + id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                            // var supcust_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "supcust_id");
                            // var sup_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "sup_name");
                            var msg = {
                                msgHead: 'SuppliersView', action: 'select', supcust_id: supcust_id, sup_name: sup_name
                            };
                            window.parent.postMessage(msg, '*');
                        }
                        else {
                            $('#popItem').jqxWindow('open');
                            //supcust_id 需要跟后端定义字段一致
                            $("#popItem").jqxWindow('setContent', `<iframe src="${frame}?operKey=${g_operKey}&supcust_id=${id}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                        }*/

                        if (args.originalEvent.button == 2) return;
                        var supcust_id = args.row.bounddata.i;
                        RowIndex = args.rowindex;
                        if (ForSelect) {
                            var supcust_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                            var sup_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "sup_name");
                            var supcust_flag = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "supcust_flag");
                            var msg = {
                                msgHead: 'SuppliersView', action: 'select', supcust_id: supcust_id, sup_name: sup_name,supcust_flag:supcust_flag
                            };
                            window.parent.postMessage(msg, '*');
                        }
                        else {
                            onGridRowEdit(args.rowindex);
                        }
                     }

                });
                $("#Cancel").on('click', function () {
                    for (var i = 0; i < 10; i++) {
                        $('#jqxgrid').jqxGrid('deleterow', i);
                        $('#jqxgrid').jqxGrid('addrow', i, {})
                    }
                });
            let itemMaxHeight = $("#popClass").parent().height()
            let itemMaxWidth = $("#popClass").parent().width()
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 500, width: 530, maxHeight: itemMaxHeight - 50, maxWidth: itemMaxWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                QueryData();

                attachContextMenu();


                $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
                });

    	    });
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
    </style>
</head>
 
<body> 
    <!--
    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;margin-bottom:10px;">
        <input id="supcust_flag" type="hidden" />
        <div><input id="searchString" class="margin" placeholder="请输入简拼/名称" /><button onclick="QueryData()" class="margin">查询</button></div>
        <div><input id="forAll" type="hidden" /></div>
        <div id="btnAdd"><button onclick="btnAddItem_click()" class="margin" style="width:80px">新增</button></div>
    </div>-->

    <div id="divTop" style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">

            <div style="float:none;height:0px; clear:both;"></div>

        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
       
        <button onclick="btnAddItem_click()" class="margin" style="width:100px">新增</button>
        @if (!Model.ForSelect)
        {
            <button id="btnExport" onclick="ExportExcel()" class="margin">导出</button>
        }
         
       

    </div>
     
    <div id="gridItems" style="margin-bottom:2px;width:calc(100% - 20px);height:100%;"></div>
    <div id="divRowCount"><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>
           

    <div id="popItem" style="display:none;"> 
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">供应商信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popClass" style="display:none;"> 
        <div style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">片区</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

     
     
   

</body>
</html>