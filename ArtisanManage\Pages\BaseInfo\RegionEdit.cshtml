@page
@model ArtisanManage.Pages.BaseInfo.RegionEditModel
@{
    Layout = null;
} 
<!DOCTYPE html> 
<html>
<head id="Head1" runat="server">
    <base target="_self">
    <title></title>
 <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">

        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
             @Html.Raw(Model.m_showFormScript)
        });

    </script>

</head>
<body>     
    <div id="divHead" class="headtail" style="width:500px;"></div>        
    <div style="text-align:center;margin-top:50px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;" >保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>
