@page
@model ArtisanManage.Pages.BaseInfo.VisitSummaryBySellerModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
          
    	    var newCount = 1;


    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
             //   $("#popoverQuery").jqxPopover({ showArrow: false, autoClose: true, offset: { left: 0, top: -10 }, position: "bottom", title: "", showCloseButton: false, selector: $("#btnQueryChooseSheets") });
             //   $("#gridItems").on("cellclick", function (event) {
               //     var args = event.args;
                //});
                $("#gridItems").on("cellclick", function (event) {

                    var args = event.args;
                    var oper_id = args.row.bounddata.oper_id;
                    var oper_name = args.row.bounddata.oper_name;
                    var day_id = args.row.bounddata.day_id;
                    var day_name = args.row.bounddata.day_name;
                    var schedule_id = args.row.bounddata.schedule_id;
                    var schedule_name = args.row.bounddata.schedule_name;
                    var real_num = args.row.bounddata.real_num;

                    var startDay = args.row.bounddata.visit_time +" 00:00";
                    var endDay = args.row.bounddata.visit_time + " 23:59";


                    if (args.datafield == "oper_name" && oper_name) {
                        var url = `Report/VisitRecord?&startDay=${startDay}&endDay=${endDay}&seller_id=${oper_id}&oper_name=${oper_name}&day_id=${day_id}&day_name=${day_name}&schedule_id=${schedule_id}&schedule_name=${schedule_name}`;
                        window.parent.newTabPage("拜访记录", `${url}`);
                    }
                    if (args.datafield == "day_name" && day_id) {
                        window.parent.newTabPage('拜访日程', `BaseInfo/VisitDayEdit?day_id=${day_id}`, window);
                    }
                    if (args.datafield == "real_num" && real_num) {
                        $('#visitDetail').jqxWindow({
                            width: "600px",
                            height: "600px",
                            resizable: true,
                            draggable: true,
                            showCloseButton: true,
                            autoOpen: false,
                            zIndex: 9999,
                        })
                        $('#visitDetail').jqxWindow('open');
                        $("#visitDetail").jqxWindow('setContent', `
                                                       <div id='detail-content' style='overflow:scroll;height:580px;'>123</div>
                                                    `);
                        $.ajax({
                            url: "../api/VisitSummaryBySeller/GetVisitDayDetail",
                            data: {
                                operKey:g_operKey,
                                visitDay: args.row.bounddata.visit_time,
                                dayID: args.row.bounddata.day_id
                            },
                            success: (res) => {
                               var html = res.rows.map(row => {
                                    return `<div style='display:flex;flex-direction:row;padding:10px;'>
                                                <div style='margin-right:10px;'>${row.sup_name}</div>
                                                         ${row.count == "0" ? "<div style='color:#c40000'>未拜访</div>" : "<div style='color:green'>已拜访</div>"}
                                            </div>`
                                }).join("")
                                $("#detail-content").html(html)
                            }
                        })
                        //$("#visitDetail").jqxWindow('setContent', ``);
                    }


                });
   
                //$("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                QueryData();

            });

        function btn_QueryClick() {
            QueryData(null, (res) => {
                console.log(res)
                if (res.totalVisitCount) {
                    $("[id='month-summary']").html("")
                    var oper_id = $($("#seller_id>input")[0]).attr("data-value")
                    if (res.totalVisitCount && oper_id) {
                        $("[id='month-summary']").html("区间总计拜访" + res.totalVisitCount + "家(不重复)");
                    }
                }
            });
        }

    </script>
</head>

<body style="overflow:hidden">
    <style>
        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }
    </style>
    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <div style="font-size:18px;position:absolute;top:28px;right:30px;" id="month-summary"></div>
        <button onclick="btn_QueryClick()" style="margin-left:20px;margin-right:0px;border-radius: 3px 0px 0px 3px">查询</button>

        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>
    <div id="visitDetail" style="display:none;">
        <div style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">日程详情</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>

 

</body>
</html>