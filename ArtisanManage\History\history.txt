2021-8-10 晚出现的bug
1.收款单出现重复行
   sheet.sheetRows=sheetRows
   sheet.SheetRows=sheetRows
   提交上去后，反序列化后，就生成了两倍的行
    看代码应该是一直有这个问题，但是之前测试没有发现这个问题
2.公司设置保存不成功 【判空】
  company_setting表    setting为null的时候保存失败，本来就存在这个问题

3.APP登录报错 【判空】
   空值检测问题getOperRight中  setting为空的时候，没有判断 

3.销售单选客户不带出余额，支付方式不带出【提交之前没测试】
   接口问题，研发修改后没有测试，测试也没有及时进行测试
4.对账单历史打不开【数据库更改】
    白天不能对数据库进行修改 

   佳慧将修改数据库SQL交给测试，由测试进行升级



8.11 上午出现的bug
 5.河南光山客户APP订单打不开  【判空】
   WebApi 订单里    trade_type存成了XD ，应该是X T
   APP 没有考虑预料之外的trade_type,需要考虑   a[trade_type].style  


6.部分Setting为null的客户打印销售单失败 【判空】
   加眉头图片打印的时候没有对setting进行判空处理导致null错误

7.XP打开销售单报错 【新语法】
  票证通增加的功能使用了es6 的async语法，XP客户端不支持

8.采购单输入时2行变1行   【判空】


  
总结:

测试
1.测试的重点是日常常用功能
2.对于新增的功能，重点靠测试兼容设置项的兼容性。
3.要做浏览器兼容性测试（在XP客户端要做测试）
4.自动化测试必须落地
5.发布日志要把功能和bug分开写，功能亮点要分列出来

研发:
1.代码健壮性
    前端和后端代码不要太依赖，前端对后端产生的问题要有一定容错性。后端也同样
2. 判空   
   不能使用a.b.c这样的方式调用,要这样调用
   if(a && a.b){
     var d=a.b.c
   }

    对于新增的功能，要考虑设置项的兼容性，比如考虑到setting为空的情况，就不能直接使用setting.companyName，要判空
3.代码要简洁，冗余容易产生问题
  比如收款单，已经做了赋值操作
       sheet.sheetRows=sheetRows
后面又这样写
     params={
  ...sheet,
    SheetRows:sheetRows
   }
    导致出现了sheetRows和SheetRows重复，后端在序列化的时候就产生了双倍的行
   
   
4.push之前要自己测试跑通


5.不能直接修改生产数据库的结构
  要在升级当天将修改数据库的sql交给测试人员统一修改

6.test-2023-08-09


     

