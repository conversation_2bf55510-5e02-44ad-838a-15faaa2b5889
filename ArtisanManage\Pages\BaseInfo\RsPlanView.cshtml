﻿@page
@model ArtisanManage.Pages.BaseInfo.RsPlanViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());

        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "RsPlanEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var row = {};
                        row["i"] = rs.data.record.plan_id;
                        row["plan_name"] = rs.data.record.plan_name;
                        row["brand_name"] = rs.data.record.brand_name;
                        row["sheet_sync"] = rs.data.record.is_sheet_sync;
                        row["share"] = rs.data.record.is_share;
                        row["client_mapper"] = rs.data.record.type;

                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;


                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "plan_name", rs.data.record.plan_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "brand_name", rs.data.record.brand_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "sheet_sync", rs.data.record.is_sheet_sync);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "share", rs.data.record.is_share);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "client_mapper", rs.data.record.type);
                }
                $("#popItem").jqxWindow('close');
            };
        });
        var m_db_id = "10";

        var newCount = 1;

        function btnAddItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="RsPlanEdit?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }

        function onGridRowEdit(rowIndex) {
            var plan_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'i');
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="RsPlanEdit?operKey=' + g_operKey + '&plan_id=' + plan_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }
        var itemSource = {};
        $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)

            $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                if (args.datafield == "plan_name") {
                    if (args.originalEvent.button == 2) return;
                    var plan_id = args.row.bounddata.i;
                    RowIndex = args.rowindex;
                    if (ForSelect) {
                        var plan_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                        var plan_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "plan_name");
                        var msg = {
                            msgHead: 'RsPlanView', action: 'select', plan_id: plan_id, plan_name: plan_name
                        };
                        window.parent.postMessage(msg, '*');
                    }
                    else {
                        onGridRowEdit(args.rowindex);
                        //$('#popItem').jqxWindow('open');
                        // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                    }
                }
            });

            $("#Cancel").on('click', function () {
                for (var i = 0; i < 10; i++) {
                    $('#jqxgrid').jqxGrid('deleterow', i);
                    $('#jqxgrid').jqxGrid('addrow', i, {})
                }
            });

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 600, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });


            QueryData();

        });
    </script>
</head>

<body>
    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;margin-bottom:10px">
        <div><input id="searchString" style="font-size:14px; border-radius:6px;border-color:#ddd;border-width:0.5px; width:200px;height:25px;" placeholder="请输入简拼/名称" /></div>
        <div><button onclick="btnAddItem_click()">添加方案</button></div>
    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>


    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">方案信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>