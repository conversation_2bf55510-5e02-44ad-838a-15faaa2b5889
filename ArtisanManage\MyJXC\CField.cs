﻿using System;
using System.Collections.Generic;
using System.Text;

namespace myJXC
{
    public class J<PERSON><PERSON><PERSON>
    {        
        public string name;
        public string value;
        public string type;
        public string id;
        public double value1 = 0;
        public double value2 = 0;
    }

    public class CItemBrief
    {
        public string id="";
        public string name="";
        public string quick_key="";
        public string group_id="";
        public string group_name="";
        public string option_group = "";
        public string owner_item_no = "";
        public string for_all_items = "";
        public double add_price = 0;
        public string spec_price = "";
    }
    public class CBriefItemRelation
    {
        public string item_no = "";
        public string group_id = "";
        public string avail_briefs = "";
        public string for_all_items = "";

    }
}
