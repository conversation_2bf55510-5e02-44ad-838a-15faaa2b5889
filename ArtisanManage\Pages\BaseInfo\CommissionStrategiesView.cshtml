﻿@page
@model ArtisanManage.Pages.BaseInfo.CommissionStrategiesViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <link href="~/css/component.css" rel="stylesheet" />
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var EditId = '';
        var RowIndex = -1;
        var employeyMaps = [];//员工信息
        var newEmployeyMaps = [];
        var maps = [];
        var names = [];
        var strategyId;
        //新增策略
        function btnAddItem_click(e) {
            $('#AddDialogId').show();
        }
        //修改提成策略名称
        function onGridRowEdit(rowIndex) {
            var strategy_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'strategy_id');
            EditId = strategy_id
            $('#dialogId').show();
        }
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "StrategyEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var row_arr = new Array;

                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;
                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "i", rs.data.record.unit_no);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "is_big_unit", rs.data.record.is_big_unit);
                }
                $("#popItem").jqxWindow('close');
            };
        });
        //判断是否右击
        function isRightClick(event) {
            var rightclick;
            if (!event) var event = window.event;
            if (event.which) rightclick = (event.which == 3);
            else if (event.button) rightclick = (event.button == 2);
            return rightclick;
        }
        //渲染员工列
        function all_opers(index, datafield, value, defaultvalue, column, rowdata) {
            var allOpers = rowdata.all_opers;
            if (allOpers) allOpers = JSON.parse(allOpers);
            var default_template = '';
            var tmpDivs = '';
            if (allOpers[0].oper_id) {
                allOpers.forEach(function (oper) {
                    var curTmp = `<li >${oper.oper_name}
                                <span class='btnRemove'>
                                  <svg width="15" height="15" style="cursor:pointer;" id=${oper.mapId} class="removeEmployee">
                                    <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#close'  />
                                  </svg>
                                 </span>
                               </li>`
                    tmpDivs += curTmp;
                });
            }
            //tmpDivs+='<image src=""/images/add.svg""/>';
            tmpDivs += `<svg class="addEmployees">
                       <use xlink: href = '/images/images.svg#thinAdd' />
                   </svg>`;

            return `<div class='tmp-list'style="display:flex;align-items:center">${tmpDivs}</div>`;
        }
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            //获取员工
            $.get('../Api/Commission/GetEmployees', { operKey: window.g_operKey }).then(result => {
                if (result.result === 'OK') {
                    employeyMaps = result.data
                        console.log(employeyMaps)
                } else {
                    bw.toast(result.msg);
                }
            });
            
            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                console.log(args)
                //    添加用户
                $('.tmp-list>.addEmployees').on('click', function (e) {
                    maps = []
                    names = []
                    var rowIndex = parseInt($(this.parentNode.parentNode.parentNode).find('.jqx-widget-header>div').text()) - 1;
                    strategyId = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'strategy_id');
                    var operType = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'worker_type');
                    var allOpers = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'all_opers');
                    allOpers = JSON.parse(allOpers)
                    console.log(allOpers)
                    newEmployeyMaps = employeyMaps.filter(employee => {
                        if(operType == '业务员'){
                            return (!allOpers.some(m => m.oper_id === employee.userId)) && employee.isSeller
                        }else{
                            return (!allOpers.some(m => m.oper_id === employee.userId)) && employee.isSender
                        }
                    } );
                    EditId = strategyId
                    $("#thead").empty();
                    $("#tbody").empty();
                    $('#addEmployee').show();
                    var tr1 = '<th><input type="checkbox" name="checkAll"/></th>' + '<th>' + '员工' + '</th>'
                    $("#thead").append('<tr>' + tr1 + '</tr>')
                    for (i in newEmployeyMaps) {
                        var tr;
                        tr = '<td><input type="checkbox" name="checkbox" id = "' + newEmployeyMaps[i].userId + '" username="' + newEmployeyMaps[i].userName + '"/></td>' + '<td>' + newEmployeyMaps[i].userName + '</td>'
                        $("#tbody").append('<tr>' + tr + '</tr>')
                    }

                    $('input[name="checkAll"]').on("click", function () {
                        if ($(this).is(':checked')) {
                            $('input[name="checkbox"]').each(function () {
                                $(this).prop("checked", true);
                            });
                        } else {
                            $('input[name="checkbox"]').each(function () {
                                $(this).prop("checked", false);
                            });
                        }
                    });
                });
                $(".removeEmployee").on('click', function (e) {
                    var mapId = $(this).attr('id');
                    jConfirm("确认要移除吗？", () => {
                        $.post({ url: '../Api/Commission/DeleteStrategyMap?map_id=' + mapId, contentType: 'text/json' }, JSON.stringify({ operKey: window.g_operKey })).then(result => {
                            if (result.result == 'OK') {
                               QueryData()
                            } else {
                                bw.toast(result.msg);
                            }
                        });
                    })
                })
                if (args.datafield == "delete") {
                    console.log("delete")
                    if (args.originalEvent.button == 2) return;
                    rowIndex = args.rowindex;
                    var strategy_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'strategy_id');
                    var id = strategy_id.split("'")[5];
                    jConfirm(`确定要删除选中提成策略吗？`, function () {
                        $.ajax({
                            url: '../api/CommissionStrategriesView/DeleteRecords',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({ operKey: g_operKey, gridID: 'gridItems', rowIDs: id }),
                            success: function (data) {
                                if (data.result == 'OK') {
                                    QueryData();
                                }
                                else {
                                    bw.toast(data.msg, 5000);
                                }
                            }
                        });
                    }, "");
                } 
                else if (args.datafield == "edit") {
                    console.log("edit");
                    onGridRowEdit(args.rowindex);
                }
                else if (args.datafield == "copy") {
                    console.log("copy")
                    var strategy_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, 'strategy_id');
                    EditId = strategy_id.split("'")[5];
                    $('#CopyDialogId').show();
                }
                else if (args.datafield == "strategy_name") {
                    if (window.event.which != 3) {
                        var strategy_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, 'strategy_id');
                        $('#popItem').jqxWindow('open');
                        $("#popItem").jqxWindow('setContent', '<iframe src="CommissionStrategy?operKey=' + g_operKey + '&strategy_id=' + strategy_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                    }
                }
                else if(args.datafield == "employees"){
                   
                }
            });
            
            $("#Cancel").on('click', function () {
                for (var i = 0; i < 10; i++) {
                    $('#jqxgrid').jqxGrid('deleterow', i);
                    $('#jqxgrid').jqxGrid('addrow', i, {})
                }
            });
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            
            QueryData();
        });
        $(document).on('contextmenu', function (e) {
            //禁用浏览器默认右键
            return false;
        });
        

    </script>
    <style>
        .tmp-list > svg {
            width: 20px;
            height: 20px;
            display: inline-block;
            margin: 8px;
            cursor: pointer;
            fill: #aaa;
        }

        .tmp-list>li {
            list-style: none;
            border: 0px solid#ddd;
            line-height: 18px;
            background: #fdd;
            border-radius: 4px;
            margin-right: 0.25rem;
            margin-left: 10px;
            padding: 3px;
            padding-left: 5px;
            padding-right: 3px;
        }

        .tmp-list>li > .btnRemove {
            margin-left: 1px;
            color: #222;
            cursor: pointer;
        }

        table.table-employee {
            border-collapse: collapse;
            margin: 10px auto;
            text-align: center;
        }

        table.table-employee td, table th {
            border: 1px solid #eee;
            color: #222;
            height: 30px;
        }

        table.table-employee thead th {
            background-color: #fff;
            width: 100px;
        }
        
    </style>
</head>

<body style="overflow:hidden">
    <div role="dialog" id="dialogId">
        <div class="dialog_container normal" style="margin-left: 50%;transform: translate(-50%);">
            <div class="dialog_head">
                <a class="btnClose item-none">x</a><b class="item-auto">修改提成策略</b>
            </div>
            <div style="height:50px;padding-top:25px;padding-left:40px">
                新的提成策略名称：<input type="text" id="newName" style="font-size:14px; border-radius:6px;border-color:#ddd;border-width:0.5px; width:200px;height:25px;" />
            </div>
            <div class="dialog_foot">
                <button class="btnClose">取消</button>
                <button class="btnOk">确认</button>
            </div>
        </div>
    </div>

    <div role="dialog" id="AddDialogId">
        <div class="dialog_container normal" style="margin-left: 50%;transform: translate(-50%);">
            <div class="dialog_head">
                <a class="btnClose item-none">x</a><b class="item-auto">新增提成策略</b>
            </div>
            <div style="height:50px;padding-top:25px;padding-left:35px">
                新增提成策略的名称：<input type="text" id="addName" style="font-size:14px; border-radius:6px;border-color:#ddd;border-width:0.5px; width:200px;height:25px;" />
            </div>
            <div style="height:50px;padding-top:25px;padding-left:35px">
                适用类别：<select id="selectType">
                    <option value="seller">业务员</option>
                    <option value="sender">送货员</option>
                </select>
            </div>
            <div class="dialog_foot">
                <button class="btnClose">取消</button>
                <button class="btnOk">确认</button>
            </div>
        </div>
    </div>

    <div role="dialog" id="addEmployee">
        <div class="dialog_container normal" style="margin-left: 50%;transform: translate(-50%);">
            <div class="dialog_head">
                <a class="btnClose item-none">x</a><b class="item-auto">匹配员工</b>
            </div>
            <div style="padding-top:25px;padding-left:35px;overflow:auto;">
                <table class="table table-employee">
                    <thead id="thead"></thead>
                    <tbody id="tbody"></tbody>
                </table>
            </div>
            <div class="dialog_foot">
                <button class="btnClose">取消</button>
                <button class="btnOk">确认</button>
            </div>
        </div>
    </div>
     

    <div style="display:flex;justify-content:space-around;margin-top:20px;">
        <div style="font:35px;font-weight:bold">提成策略列表</div>
        <div style="display:flex">
            <div><button onclick="btnAddItem_click()">新增策略</button></div>
            <div><button onclick="btnBatchRemove_click()">批量删除</button></div>
        </div>
    </div>

  
    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:calc(100% - 80px);"></div>
     

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">提成策略设置</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

    <partial name="dialog" />

    <script type="text/javascript">
        function btnBatchRemove_click(e) {
            var ids = [];
            $('.btn:checked').each(function () {
                ids.push(this.id);
            });
            console.log(ids);
            if (ids.length == 0) {
                bw.toast('请选择至少一个提成策略');
                return;
            };
            jConfirm(`确定要删除选中提成策略吗？`, function () {
                $.ajax({
                    url: '../api/CommissionStrategiesView/BatchDelete?operKey=@Model.OperKey',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ dataids: ids }),
                    success: function (data) {
                        if (data.result == 'OK') {
                            QueryData();
                            bw.toast('删除成功',3000);
                        }
                        else {
                            bw.toast(data.msg, 5000);
                        }
                    }
                });
            },"");
        };
        function EditStrategyName(action) {
            var newN = $("#newName").val();

            var formData = {
                newName: newN,
                id: EditId,
                operKey: window.g_operKey
            };
            $.ajaxSetup({ contentType:"application/json"});
            $.post(`/api/CommissionStrategiesView/${action}?operKey=@Model.OperKey`, JSON.stringify(formData)).then(res => {
                console.log(res);
                if (res) {
                    QueryData();
                }
                else bw.toast(data.msg, 5000);

            });
        }
        function SaveStrategyMaps(action) {
            var newN = $("#newName").val();

            var formData = {
                newName: newN,
                id: EditId,
                operKey: window.g_operKey
            };
            $.ajaxSetup({ contentType: "application/json" });
            var len = $("input[name='checkbox']:checked").length;
            if (len == 0) {         //如果不勾选弹出警告框
                bw.toast('请至少选择一个数据集');
                return false;
            }

            //遍历被选中的checkbox集

            $("input[name='checkbox']:checked").each(function () {
                maps.push({
                    employee: $(this).attr('id'),
                    strategy: strategyId
                });
                names.push({
                    employee: $(this).attr('id'),
                    strategy: strategyId,
                    name: $(this).attr('username')
                })
            });
            console.log(maps)
            $.post({ url: '../Api/Commission/SaveStrategyMaps?operKey=' + window.g_operKey, contentType: 'text/json' }, JSON.stringify(maps)).then(result => {
                if (['OK', 'Warn'].includes(result.result)) {
                    QueryData();
                }
                bw.toast(result.msg);
            });
        }

        function AddStrategy(action) {
            var addN = $("#addName").val();
            var type = $("#selectType").val();

            var formData = {
                addName: addN,
                type:type,
                operKey: window.g_operKey
            };
            ajaxPost(`/api/CommissionStrategiesView/${action}`,formData).then(res => {
                console.log(res);
                if (res) {
                    QueryData();
                }
                else bw.toast(data.msg, 5000);

            })
        }
        $(function () {
            var el = $('#addEmployee')
            el.hide()

            new EventRegister({
                trigger: `[show=addEmployee]`,
                handler() {
                    el.show();
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnClose',
                handler() {
                    el.hide()
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnOk',
                handler() {
                    SaveStrategyMaps("SaveStrategyMaps");
                    el.hide()
                }
            }).register();
        })
        $(function () {
            var el = $('#dialogId')
            el.hide()

            new EventRegister({
                trigger: `[show=dialogId]`,
                handler() {
                    el.show();
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnClose',
                handler() {
                    el.hide()
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnOk',
                handler() {
                    EditStrategyName("EditStrategyName");
                    el.hide()
                }
            }).register();
        })

        $(function () {
            var el = $('#AddDialogId')
            el.hide()

            new EventRegister({
                trigger: `[show=AddDialogId]`,
                handler() {
                    el.show();
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnClose',
                handler() {
                    el.hide()
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnOk',
                handler() {
                    AddStrategy("AddStrategy");
                    el.hide()
                }
            }).register();
        })

        $(function () {
            var el = $('#CopyDialogId')
            el.hide()

            new EventRegister({
                trigger: `[show=CopyDialogId]`,
                handler() {
                    el.show();
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnClose',
                handler() {
                    el.hide()
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnOk',
                handler() {
                    CopyStrategy("CopyStrategy");
                    el.hide()
                }
            }).register();
        })

    </script>

</body>
</html>