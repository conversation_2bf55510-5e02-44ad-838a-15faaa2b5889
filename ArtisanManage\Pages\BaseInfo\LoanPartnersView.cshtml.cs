﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ArtisanManage.Services;


namespace ArtisanManage.Pages.BaseInfo
{
    public class LoanPartnersViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;

        public LoanPartnersViewModel(CMySbCommand cmd) : base(Services.MenuId.infoLoanPartner)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"partner_name",new DataItem(){Title="检索",FldArea="divHead",SqlFld="partner_name,s.py_str,s.mobile",PlaceHolder="名称/简拼/手机", QueryOnChange=true,CompareOperator="ilike"}},
                {"status",new DataItem(){Title = "状态",FldArea="divHead", LabelInDB = false, Value = "normal", Label = "正常",ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "normal",

                     Source = @"[{v:'normal',l:'正常',condition:""(s.status = '1' or s.status is null)""},
                               {v:'stop',l:'停用',condition:""s.status = '0' ""},
                               {v:'all',l:'所有',condition:""true""}]"
                }},
          
            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                    IdColumn="i",TableName="info_loan_partner",
                    ShowContextMenu=true,
                    Columns = new Dictionary<string, DataItem>()
                    {
                        {"i",new DataItem(){Title="编号",SqlFld="partner_id",Width="80",Hidden=true,HideOnLoad = true}},
                        {"partner_name",new DataItem(){Title="名称",Width="150",Linkable=true}},
                        {"mobile",new DataItem(){Title="联系电话",Width="100"}},
                        {"partner_flag",new DataItem(){Title="借贷款标志",Width="100",SqlFld="(case WHEN partner_flag='B' THEN '贷款方' END)" }},
                        {"status",new DataItem(){Title="状态",Width="50",SqlFld="(case WHEN status='0' THEN '停用' ELSE '正常' END)" }},
                    },
                    QueryFromSQL="from info_loan_partner s  where company_id=~COMPANY_ID  " ,QueryOrderSQL="order by partner_id desc"
                  }
                } 
            }; 
        }

        public async Task OnGet()
        {
            await InitGet(cmd);

        }


        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = $@"(select sheet_id, '贷款单' as sheet_type from sheet_loan where partner_id in ({rowIDs}) and company_id={company_id} and red_flag is null limit 1)
                            union all
                            (select sheet_id, '还贷款单' as sheet_type from sheet_repay where partner_id in ({rowIDs}) and company_id={company_id} and red_flag is null limit 1)";
            QQ.Enqueue("sheet", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "sheet")
                {
                    dynamic sheet = CDbDealer.Get1RecordFromDr(dr, false);
                    if(sheet != null)
                    {
                        return $"该借贷款单位已开过【{sheet.sheet_type}】，无法删除";
                    }
                }
            }
            QQ.Clear();

            return "";
        }
    }



    [Route("api/[controller]/[action]")]
    public class LoanPartnersViewController : QueryController
    { 
        public LoanPartnersViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            LoanPartnersViewModel model = new LoanPartnersViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            LoanPartnersViewModel model = new LoanPartnersViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            LoanPartnersViewModel model = new LoanPartnersViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd, "info_loan_partner");// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            LoanPartnersViewModel model = new LoanPartnersViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
