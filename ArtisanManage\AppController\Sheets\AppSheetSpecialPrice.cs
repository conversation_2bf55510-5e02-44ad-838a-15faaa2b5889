﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.AppController.Sheets
{ 
    [Route("AppApi/[controller]/[action]")]
    public class AppSheetSpecialPrice : QueryController
    { 
        public AppSheetSpecialPrice(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        /// <summary>
        /// 加载特价审批单--
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SheetSpecialPrice sheet = new SheetSpecialPrice(LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index";
            QQ.Enqueue("attr_options", sql);
            
            List<ExpandoObject> attrOptions = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "attr_options")
                {
                    attrOptions = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet, attrOptions});
        }

        public async Task<string> CheckOtherInfo(dynamic dSheet)
        {
            string sheet_id = dSheet.sheet_id;
            string supcust_id = dSheet.supcust_id;
            string company_id = dSheet.company_id;
            string condi = "";
            if (sheet_id != "") condi = $" and sheet_id <>{sheet_id} ";
            string sql = $@"SELECT sheet_id from sheet_special_price_main WHERE company_id = {company_id} and supcust_id = {supcust_id} and red_flag is null and approve_time is null {condi} ";
            dynamic checkInfo = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (checkInfo.Count>0) return $"客户存在未审核的特价单,请先审核.";
            
            return "";
        }
        /// <summary>
        /// 提交特价审批单
        /// </summary>
        /// <param name="sheet">
        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic dSheet)
        {
            SheetSpecialPrice sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetSpecialPrice>(sSheet);
                msg = await CheckOtherInfo(sheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Save:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            if (msg == "")
            {
                cmd.ActiveDatabase = "";
                sheet.Init();
                msg = await sheet.Save(cmd);
            }
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.make_time, sheet.happen_time, currentTime });
        }
        
        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="dSheet"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            SheetSpecialPrice sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetSpecialPrice>(sSheet);
                msg = await CheckOtherInfo(sheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Submit:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }

            sheet.Init();
            if(msg=="")msg = await sheet.SaveAndApprove(cmd);
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no,sheet.approve_time, currentTime });
        }

        
        /// <summary>
        /// 红冲
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="sheetID"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            string result = "OK"; string msg = null;
            try
            {
                var currentTime = DateTime.Now.ToText();
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                SheetSpecialPrice sheet = new SheetSpecialPrice(LOAD_PURPOSE.SHOW);
                msg = await sheet.Red(cmd, companyID, sheetID, operID,"");
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetSpecialPrice sheet = new SheetSpecialPrice(LOAD_PURPOSE.SHOW);
            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return Json(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        //public override async Task<JsonResult> GetItemsInfo(string companyID, string items_id, string supcust_id)
        //{
        //    if (supcust_id.IsInvalid()) supcust_id = "-1";
        //    SQLQueue QQ = new SQLQueue(cmd);
        //    string sql = $@"";
        //    QQ.Enqueue("items", sql);
        //}
        /// <summary>  下方注释为下面注释接口注释，不一定参考意义
        /// 商品档案列表----返回商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数
        /// </summary>
        /// <param name="operKey">Aa18nTx5omI=</param>
        /// <param name="searchStr">商品名，助记码，商品编号，商品条码 模糊查询</param>
        /// <param name="brandID">品牌ID查询</param>
        /// <param name="classID">分类ID查询</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="branchID">仓库名 （1）</param>
        /// <returns>商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数</returns>
        [HttpGet]
        public async Task<JsonResult> GetItemList(string operKey, string searchStr, string brandIDs, string classID, int pageSize, int startRow, string supcustID)
        { 
            bool firstRequest = false;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $" where ip.company_id = {companyID} and (ip.status is null or ip.status='1') and son_mum_item is NULL ";

            if (searchStr != null)
            {
                string b = "";
                if (searchStr.Length >= 13)
                {
                    b = "%";
                }
                string flexStr = CPubVars.GetFlexLikeStr(searchStr);
                condi += $"and (ip.item_name ilike '%{flexStr}%' or ip.py_str ilike '%{flexStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%'  or ip.mum_attributes::text ilike '%{searchStr}%' or (t.s->>'f3') like '%{searchStr}{b}' or  (t.b->>'f3') like '%{searchStr}{b}' or (t.m->>'f3') like '%{searchStr}{b}')";
            }
            if (brandIDs != null && brandIDs != "") condi += $"and (ip.item_brand is null OR ip.item_brand in ({brandIDs})) ";
            bool bSearchStrInClass = false;
            if (classID != null) {
                bSearchStrInClass = true;
                condi += $" and ip.other_class like '%/{classID}/%' "; 
            }
            if (startRow == 0) firstRequest = true;
            SQLQueue QQ = new SQLQueue(cmd);
            string sql_noLimit = @$"

SELECT ip.item_id,ip.item_name,ip.mum_attributes,item_class as class_id,other_class,valid_days,item_provenance,produce_date,item_spec,
       ip.item_images, ip.item_brand,
        s_unit_no,s_unit_factor,s_barcode,s_wholesale_price,s_retail_price,s_recent_price,s_buy_price,s_recent_orig_price,s_lowest_price,
        m_unit_no,m_unit_factor,m_barcode,m_wholesale_price,m_retail_price,m_recent_price,m_buy_price,m_recent_orig_price,m_lowest_price,
        b_unit_no,b_unit_factor,b_barcode,b_wholesale_price,b_retail_price,b_recent_price,b_buy_price,b_recent_orig_price,b_lowest_price ,        (
         case when b_unit_factor is not null and m_unit_factor is     null then concat(s_unit_factor,b_unit_no,'=',b_unit_factor,s_unit_no)  
			  when b_unit_factor is not null and m_unit_factor is not null then concat(s_unit_factor,b_unit_no,'=',floor(b_unit_factor::numeric/m_unit_factor::numeric),m_unit_no,'=',b_unit_factor,s_unit_no)
			  when b_unit_factor is null then concat(s_unit_factor,s_unit_no)  end
        ) as unit_conv
FROM info_item_prop as ip    
LEFT JOIN 
(
    select item_id,s,m,b,
            (s->>'f1') s_unit_no,(s->>'f2') as s_unit_factor,(s->>'f3') as s_barcode,(s->>'f4') as s_wholesale_price,(s->>'f5') as s_retail_price,s->>'f6' as s_recent_price,s->>'f7' as s_buy_price,s->>'f8' as s_recent_orig_price,s->>'f9' as s_lowest_price,
            (m->>'f1') as m_unit_no,(m->>'f2') as m_unit_factor,(m->>'f3') as m_barcode,(m->>'f4') as m_wholesale_price,(m->>'f5') as m_retail_price,m->>'f6' as m_recent_price,m->>'f7' as m_buy_price,m->>'f8' as m_recent_orig_price,m->>'f9' as m_lowest_price,
            (b->>'f1') as b_unit_no,(b->>'f2') as b_unit_factor,(b->>'f3') as b_barcode,(b->>'f4') as b_wholesale_price,(b->>'f5') as b_retail_price,b->>'f6' as b_recent_price,b->>'f7' as b_buy_price,b->>'f8' as b_recent_orig_price,b->>'f9' as b_lowest_price
     from crosstab('select mu.item_id,unit_type,row_to_json(row(mu.unit_no,unit_factor,barcode,wholesale_price,retail_price,recent_price,buy_price,recent_orig_price,lowest_price)) as json 
                                 from info_item_multi_unit mu 
                                left join (select item_id,unit_no,recent_price,recent_orig_price FROM client_recent_prices where company_id = {companyID} and supcust_id = {supcustID}) rp 
                                 on rp.item_id = mu.item_id and mu.unit_no = rp.unit_no where mu.company_id = {companyID} order by item_id', $$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)
) t
on ip.item_id=t.item_id
left join 
(
    select item_id,produce_date from item_recent_produce_date where company_id={companyID}
) rpd on t.item_id=rpd.item_id
LEFT JOIN 
(
    select * from info_item_class where company_id = {companyID}
) ic on ip.item_class = ic.class_id 
 {condi}";
            
            var sql = sql_noLimit + $" order by item_order_index,item_name limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);
            if (firstRequest)
            {
                sql = $"select count(*) as itemCount from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
            }
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count"&& firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
            }
            QQ.Clear();

            #region 获取价格策略
            bool doPricePlan = false;
            Dictionary<string, string> planIDs = new Dictionary<string, string>();
            if (supcustID != "-1" && supcustID != "" && supcustID != "0" && data.Count > 0)
            {
                string supSql = $"select region_id,other_region,sup_rank,sup_group from info_supcust where company_id = {companyID} and supcust_id={supcustID}";
                dynamic supInfo = await CDbDealer.Get1RecordFromSQLAsync(supSql, cmd);
                var groupID = supInfo.sup_group == "" ? "null" : supInfo.sup_group;
                var otherRegion = supInfo.other_region;
                var rankID = supInfo.sup_rank == "" ? "null" : supInfo.sup_rank;
                string planSql = $@"
  select null flow_id,      supcust_id,null group_id,null region_id,null rank_id,price1,price2,price3 from price_strategy_client where company_id = {companyID} and supcust_id={supcustID} and coalesce(order_source,'all')!='xcx'
  union
  select      flow_id, null supcust_id,     group_id,     region_id,     rank_id,price1,price2,price3 from price_strategy_class  where company_id={companyID} and (position(concat('/',region_id,'/') in '{otherRegion}')>0   or region_id is null) and (group_id::text = '{groupID}' or group_id is null) and (rank_id is null or rank_id::text = '{rankID}') and not (region_id is null and group_id is null and rank_id is null) and coalesce(order_source,'all')!='xcx' order by supcust_id,flow_id desc";
                List<ExpandoObject> supPlans = await CDbDealer.GetRecordsFromSQLAsync(planSql, cmd);
                foreach (dynamic plan in supPlans)
                {
                    if (plan.supcust_id != "") doPricePlan = true;
                    else if (plan.supcust_id == "" && plan.flow_id != "") doPricePlan = true;

                    if (doPricePlan)
                    {
                        if (plan.price1 != "") planIDs.Add("1", plan.price1);
                        if (plan.price2 != "") planIDs.Add("2", plan.price2);
                        if (plan.price3 != "") planIDs.Add("3", plan.price3);
                        break;
                    }
                }
            }
            #endregion
            var itemIDs = string.Join(",", data.Select(d => (d as dynamic).item_id));
            #region 使用价格策略
            if (doPricePlan && planIDs.Count > 0)
            {
                List<ExpandoObject> plans = null;

                #region 获取所有商品的所有方案
                var fld = ""; var orderCondi1 = "";
                string priceSql = "";
                foreach (var p in planIDs)
                {
                    if (priceSql != "") priceSql += " union ";
                    if (p.Key != null && p.Key != "")
                    {
                        fld = $"{p.Key} as priority,'{p.Value}' plan_id,";
                        orderCondi1 = $" order by priority,price_item ";
                    }

                    priceSql += @$"
select p.item_id,p.other_class,pi.item_id price_item,{fld}plan_name,pi.class_id,s_price,m_price,b_price,class_discount,item_discount
from info_item_prop p 
left join 
(
    select null item_id,null s_price,null m_price,null b_price,     class_id,discount class_discount,null     item_discount from price_plan_class where company_id = {companyID} and plan_id::text = '{p.Value}' and class_id is not null
    union
    select      item_id,     s_price,     m_price,     b_price,null class_id,    null class_discount,discount item_discount from price_plan_item  where company_id = {companyID} and plan_id::text = '{p.Value}' and item_id in ({itemIDs})
) 
pi on (pi.item_id = p.item_id or pi.item_id is null) and (position(concat('/',pi.class_id,'/') in other_class)>0 or pi.class_id is null)
left join price_plan_main m on m.plan_id::text = '{p.Value}'
where p.company_id = {companyID} and p.item_id in ({itemIDs}) ";
                }
                priceSql += orderCondi1;
                plans = await CDbDealer.GetRecordsFromSQLAsync(priceSql, cmd);
                #endregion

                #region 修改结果集  { priority, { plan_id, item_id, priceSetting{price_item,class_id.....}}}
                Dictionary<string, dynamic> prices = new Dictionary<string, dynamic>();
                foreach (dynamic plan in plans)
                {
                    dynamic pr = null;
                    if (prices.ContainsKey(plan.priority + '_' + plan.item_id)) pr = prices[plan.priority + '_' + plan.item_id];
                    else
                    {
                        pr = new ExpandoObject();
                        pr.priority = plan.priority;
                        pr.plan_id = plan.plan_id;
                        pr.plan_name = plan.plan_name;
                        pr.item_id = plan.item_id;
                        pr.other_class = plan.other_class;
                        prices.Add((string)plan.priority + '_' + plan.item_id, pr);
                        pr.priceSetting = new List<dynamic>();//priceSetting 可能会包含一个类别设定和品项设定
                    }
                    List<dynamic> prSetting = pr.priceSetting;
                    if (plan.price_item != "" || plan.class_id != "") prSetting.Add(new { plan.price_item, plan.class_id, plan.class_discount, plan.item_discount, plan.s_price, plan.m_price, plan.b_price });
                }
                #endregion

                #region 调价单的影响 --- 在使用价格策略时，需要多进行一次比较
                // 对于首选方案为最近售价的价格策略，需要考虑调价单的影响
                dynamic adjustItems = null;
                if (planIDs.Count > 1 && planIDs["1"] == "recent") // 当 priority为1 && plan = "recent_price"时，
                {
                    string adjustPlan = planIDs["2"];
                    if (planIDs["2"] == "wholesale") adjustPlan = "-1";
                    if (planIDs["2"] == "retail") adjustPlan = "0";
                    //需要比较 client_recent_prices 表中 happen_time 与 sheet_price_adjust_detail 中 happen_time 的时间早晚
                    string selAdjustSql = @$"select string_agg(distinct d.item_id::text,',') items_id from sheet_price_adjust_detail d 
                                    left join sheet_price_adjust_main m on m.sheet_id = d.sheet_id 
                                    left join (select item_id,max(happen_time) happen_time,supcust_id from client_recent_prices where company_id={companyID} GROUP BY item_id,supcust_id ) r on r.item_id = d.item_id
                                    where m.company_id = {companyID} and supcust_id = {supcustID} and  m.red_flag is null and m.approve_time is not null and m.plans_id like '%{adjustPlan}%' and m.happen_time>r.happen_time and d.item_id in ({itemIDs})
                ";
                    adjustItems = await CDbDealer.Get1RecordFromSQLAsync(selAdjustSql, cmd); // 找出需要使用 新价格的 商品
                }
                #endregion

                #region 遍历商品集 给每个商品添加 大中小价格 setPrice=true，说明已经取到价格，getPlanPrice=true 表示data中添加价格方案价格
                foreach (dynamic unit in data)
                {
                    var s_price = ""; var m_price = ""; var b_price = "";
                    //string s_orig_price = "", m_orig_price = "", b_orig_price = "";
                    bool setPrice = false;
                    bool bOrigPriceDiffWithPrice = false;
                    unit.s_plan_price = "";
                    unit.m_plan_price = "";
                    unit.b_plan_price = "";
                    unit.plan_id = "";
                    unit.plan_name = "";
                    foreach (var p in prices)
                    {
                        if (unit.item_id == p.Value.item_id)
                        {
                            var plan = p.Value.plan_id;
                            var name = unit.item_name;
                            Boolean state = false;
                            if (adjustItems != null) state = adjustItems.items_id.Contains(unit.item_id);
                            if (plan == "") continue;
                            else if (plan == "wholesale" && !setPrice)
                            {
                                s_price = unit.s_wholesale_price;
                                m_price = unit.m_wholesale_price;
                                b_price = unit.b_wholesale_price;
                                if (s_price != "" || m_price != "" || b_price != "") setPrice = true;
                            }
                            else if (plan == "recent" && !setPrice)
                            {
                                if (p.Value.priority == "1" && adjustItems != null && adjustItems.items_id.Contains(unit.item_id)) // 如果需要改价，则取第二次的方案价格
                                    continue;
                                s_price = unit.s_recent_price;
                                m_price = unit.m_recent_price;
                                b_price = unit.b_recent_price;
                                if (s_price != "" || m_price != "" || b_price != "")
                                {
                                    unit.b_orig_price = unit.b_recent_orig_price;
                                    unit.m_orig_price = unit.m_recent_orig_price;
                                    unit.s_orig_price = unit.s_recent_orig_price;
                                    setPrice = true;
                                    bOrigPriceDiffWithPrice = true;
                                }
                            }
                            else if (plan == "retail" && !setPrice)
                            {
                                s_price = unit.s_retail_price;
                                m_price = unit.m_retail_price;
                                b_price = unit.b_retail_price;
                                if (s_price != "" || m_price != "" || b_price != "") setPrice = true;
                            }
                            else//价格方案
                            {
                                string otherClass = p.Value.other_class;
                                var selectClass = ""; var selectDisc = ""; var classIndex = -1; bool getPlanPrice = false;
                                foreach (dynamic s in p.Value.priceSetting)
                                {
                                    if (s.price_item != "")
                                    {
                                        unit.s_plan_price = s.s_price;  //unit中添加价格方案
                                        unit.m_plan_price = s.m_price;
                                        unit.b_plan_price = s.b_price;
                                        if (!setPrice)
                                        {
                                            s_price = s.s_price;
                                            m_price = s.m_price;
                                            b_price = s.b_price;
                                        }
                                        if (s.s_price != "" || s.m_price != "" || s.b_price != "") //可能存在 只有 某一单位有价格，
                                        {
                                            setPrice = true; getPlanPrice = true;
                                            break;
                                        }
                                        else selectDisc = s.item_discount;
                                    }
                                    else if (s.class_id != "")
                                    {
                                        // var classArr = otherClass.Split(otherClass, '/');
                                        var classArr = otherClass.Split('/');
                                        if (Array.IndexOf(classArr, s.class_id) > classIndex)
                                        {
                                            classIndex = Array.IndexOf(classArr, s.class_id);
                                            selectClass = s.class_id;
                                            selectDisc = s.class_discount;
                                            getPlanPrice = true;
                                        }
                                    }
                                    if (getPlanPrice)
                                    {
                                        unit.plan_id = plan;
                                        unit.plan_name = p.Value.plan_name;
                                        break;
                                    }
                                }
                                if (!getPlanPrice && selectDisc != "") // 如果是给 商品类别指定折扣 的价格方案，就取批发价*折扣
                                {
                                    unit.b_orig_price = unit.b_wholesale_price;
                                    unit.m_orig_price = unit.m_wholesale_price;
                                    unit.s_orig_price = unit.s_wholesale_price;
                                    bOrigPriceDiffWithPrice = true;
                                    if (unit.s_wholesale_price != "")
                                    {
                                        if (!setPrice) s_price = Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.s_wholesale_price);
                                        unit.s_plan_price = s_price;
                                    }
                                    if (unit.m_wholesale_price != "")
                                    {
                                        if (!setPrice) m_price = Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.m_wholesale_price);
                                        unit.m_plan_price = m_price;
                                    }
                                    if (unit.bpprice != "")
                                    {
                                        if (!setPrice) b_price = Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.b_wholesale_price);
                                        unit.b_plan_price = b_price;
                                    }
                                    getPlanPrice = true;
                                }
                                if (getPlanPrice) break;
                            }

                        }
                    }

                    if (!bOrigPriceDiffWithPrice)
                    {
                        unit.b_orig_price = b_price;
                        unit.m_orig_price = m_price;
                        unit.s_orig_price = s_price;
                    }

                    unit.s_price = s_price;
                    unit.m_price = m_price;
                    unit.b_price = b_price;
                }
                #endregion

            }
            #endregion
            else
            {
                foreach (dynamic unit in data)
                {
                    unit.s_price = unit.s_recent_price;
                    unit.m_price = unit.m_recent_price;
                    unit.b_price = unit.b_recent_price;
                    unit.b_orig_price = unit.b_recent_orig_price;
                    unit.m_orig_price = unit.m_recent_orig_price;
                    unit.s_orig_price = unit.s_recent_orig_price;
                    if (unit.s_price == "") unit.s_price = unit.s_orig_price = unit.s_wholesale_price;
                    if (unit.m_price == "") unit.m_price = unit.m_orig_price = unit.m_wholesale_price;
                    if (unit.b_price == "") unit.b_price = unit.b_orig_price = unit.b_wholesale_price;
                    unit.s_plan_price = "";
                    unit.m_plan_price = "";
                    unit.b_plan_price = "";
                    unit.plan_id = "";
                    unit.plan_name = "";
                }

            }
            if (bSearchStrInClass)
            {
                bool foundInClass = false;
                foreach (dynamic item in data)
                {
                    string other_class = item.other_class;
                    if (other_class.Contains("/" + classID + "/"))
                    {
                        foundInClass = true;
                    }
                    else
                    {
                        item.beforeInfo2 = "其他类";
                        if (foundInClass)
                        {
                        }
                        else
                        {
                            item.beforeInfo1 = "该类下木有哦~";
                        }

                        break;
                    }
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, itemCount });
        }

        [HttpGet]
        public async Task<IActionResult> GetItemsForAttrRows(string operKey, int item_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);

            // var lstSonItems = new List<dynamic>();

            // string all_son_options_id = string.Join(",", lstSonItems.Select(t => "'" + t.son_options_id + "'"));
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"
SELECT ip.item_id son_item_id,item_name son_item_name,son_options_id,
       s_buy_price,s_wholesale_price,s_cost_price,produce_date
FROM info_item_prop ip
LEFT JOIN
(
    SELECT item_id,cost_price_avg s_cost_price,
           (b->>'f1')::real as b_unit_factor, b->>'f2' as b_unit_no, b->>'f3' as b_retail_price,b->>'f4' as b_buy_price,b->>'f5' as b_wholesale_price,
           (m->>'f1')::real as m_unit_factor, m->>'f2' as m_unit_no, m->>'f3' as m_retail_price,m->>'f4' as m_buy_price,m->>'f5' as m_wholesale_price,
           (s->>'f1')::real as s_unit_factor, s->>'f2' as s_unit_no, s->>'f3' as s_retail_price,s->>'f4' as s_buy_price,s->>'f5' as s_wholesale_price 
    FROM crosstab
    (
       ' SELECT ip.item_id,ip.cost_price_avg,unit_type,row_to_json(row(unit_factor,mu.unit_no,mu.retail_price,mu.buy_price,mu.wholesale_price)) as json 
         FROM info_item_multi_unit mu 
	     LEFT JOIN info_item_prop ip on mu.item_id = ip.son_mum_item and ip.company_id = mu.company_id
         WHERE mu.company_id={companyID} and ip.son_mum_item ={item_id}  order by ip.item_id
       ', $$values ('s'::text),('m'::text),('b'::text)$$
    ) as errr(item_id int,cost_price_avg numeric ,s jsonb,m jsonb,b jsonb)
) unit on unit.item_id = ip.item_id
LEFT JOIN item_recent_produce_date rpd on rpd.company_id = ip.company_id and rpd.item_id = ip.item_id 
WHERE ip.company_id={companyID} and son_mum_item ={item_id}";

            QQ.Enqueue("son_items", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();

            var lstSonItems = new List<dynamic>();

            dynamic data = null;
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "son_items")
                {
                    var lst = CDbDealer.GetRecordsFromDr(dr);
                    lst.ForEach(r =>
                    {
                        lstSonItems.Add(r);
                    });


                    data = lstSonItems.GroupBy(r => new { ((dynamic)r).son_item_id, ((dynamic)r).son_item_name, ((dynamic)r).son_options_id, ((dynamic)r).s_buy_price, ((dynamic)r).s_wholesale_price, ((dynamic)r).s_cost_price, ((dynamic)r).produce_date })
                               .Select(g =>
                               {


                                   return new
                                   {
                                       g.Key.son_item_id,
                                       g.Key.son_item_name,
                                       g.Key.son_options_id,
                                       g.Key.s_buy_price,
                                       g.Key.s_wholesale_price,
                                       g.Key.s_cost_price,
                                       g.Key.produce_date
                                   };
                               }).ToList();

                }
            }
            QQ.Clear();

            return new JsonResult(new { result = "OK", msg = "", data });

        }

    }
}

