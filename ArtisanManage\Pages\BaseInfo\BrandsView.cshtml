@page
@model ArtisanManage.Pages.BaseInfo.BrandsViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());

        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "BrandEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()

                    }
                    else {
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = rs.data.record;
                        rows[0].i = rows[0].brand_id;

                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "brand_name",rs.data.record.brand_name);
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "remark", rs.data.record.remark);
                    QueryData();

                }
                $("#popItem").jqxWindow('close');
            };
        });
            var m_db_id = "10";

    	    var newCount = 1;

        function btnAddItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="BrandEdit?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }

        function onGridRowEdit(rowIndex) {
            var brand_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'i');
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="BrandEdit?operKey=' + g_operKey + '&brand_id=' + brand_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }
        function btnSelectBrands_click() {
            var rows = window.g_checkedRows
            //  var rows = window.g_arrCheckedRows
            var checkedRows = []
            for (var id in rows) {
                var row = rows[id]
                checkedRows.push(getSelectRow(row))
            }

            if (checkedRows.length == 0) {
                bw.toast('请至少选择一个品牌')
                return
            }
            var isLoadNoStock = $("#jqxcheckbox").jqxCheckBox('checked');
            var msg = {
                msgHead: 'BrandsView', action: 'selectMulti', checkedRows: checkedRows, isLoadNoStock: isLoadNoStock
            }
            window.parent.postMessage(msg, '*');
        }
        function getSelectRow(row) {
            var newRow = {
                brand_id: row.i, 
                brand_name: row.brand_name
            }
            return newRow
        }
        function onBrandNameClick(rowIndex) {
            if (ForSelect) {
                var checkedRows = []
                var rows = $('#gridItems').jqxGrid('getrows')
                var row = rows.find(r => r.uid == rowIndex)
                checkedRows.push(getSelectRow(row))
                var isLoadNoStock = $("#jqxcheckbox").jqxCheckBox('checked');
                var msg = {
                    msgHead: 'BrandsView', action: 'selectMulti', checkedRows: checkedRows, isLoadNoStock: isLoadNoStock
                }
                window.parent.postMessage(msg, '*');
            }

        }
        function brandNameRenderer(row, column, value, p4, p5, rowData) {
            return `<div onclick='onBrandNameClick(${row})' style="cursor:pointer;margin-left:4px;color:#49f;margin-right:2px" >${value}</div>`
        }
    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

                $("#gridItems").on("cellclick", function (event) {
                    // event arguments.
                    var args = event.args;
                    if (args.datafield == "brand_name") {
                        if (args.originalEvent.button == 2) return;
                        var brand_id = args.row.bounddata.i;
                        RowIndex = args.rowindex;
                        if (ForSelect) {
                            var brand_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                            var brand_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "brand_name");
                            var msg = {
                                msgHead: 'BrandsView', action: 'select', brand_id: brand_id, brand_name: brand_name
                            };
                            window.parent.postMessage(msg, '*');
                        }
                        else {
                            onGridRowEdit(args.rowindex);
                            //$('#popItem').jqxWindow('open');
                            // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                        }
                    }
                });

                $("#Cancel").on('click', function () {
                    for (var i = 0; i < 10; i++) {
                        $('#jqxgrid').jqxGrid('deleterow', i);
                        $('#jqxgrid').jqxGrid('addrow', i, {})
                    }
                });

                $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
                });
            $("#jqxcheckbox").jqxCheckBox({ checked: false,height:30,width:200 });
            if (!window.ForSelect) {

                $('#btnSelectBrands').hide()
                $('#jqxcheckbox').hide()
            }
            if (window.ForSelect) {
                $('#btnAddBrand').hide()
            }


                QueryData();

    	    });
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
    </style>
</head>

<body>
    <div id="divHead" style="display:flex;justify-content:space-around; margin-top:20px;">
        <div style="display:flex;">
            <input id="searchString" class="margin" placeholder="请输入简拼/名称" />
            <div style="display: flex;width:130px;margin-left:50px;"><label class="label_name"  style="margin-top:8px;margin-right:5px;">状态</label> <div id="status" style="width:70px;" class="label_content"></div></div>
            <div id="jqxcheckbox">加载无库存商品</div>
            <button onclick="QueryData()" class="margin">查询</button>
            <button id="btnSelectBrands" style="width:60px;" class="margin" onclick="btnSelectBrands_click()">选择</button>
        </div>
        <div><button id="btnAddBrand" onclick="btnAddItem_click()" class="margin">添加品牌</button></div>
    </div>

    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:100%;margin-bottom:10px;"></div>


    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">品牌信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>