﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Operations;
using myJXC;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using NPOI.SS.UserModel;
using Org.BouncyCastle.Asn1.Ocsp;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Drawing;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public enum SHEET_SPLIT
    {
        EMPTY,
        COMBINE,
        SPLIT,
        MODEL
    }
    public class SheetRowCombine : SheetRowItem
    {
        public SheetRowCombine()
        {

        }
	    public string branch_type { get; set; }
        [SaveToDB][FromFld] public string buy_price { get; set; }//单价
        [SaveToDB][FromFld("round((t.unit_factor*t.cost_price_avg)::numeric,4)")] public string cost_price_avg { get; set; }//加权成本
        [SaveToDB][FromFld] public string cost_price_spec { get; set; }//预设成本
        public new string branch_id { get; set; }
        public new decimal old_stock_qty { get; set; }
        [SaveToDB][FromFld] public string cost_price { get; set; }
        [SaveToDB][FromFld] public string cost_amount { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_spec { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string son_mum_item { get; set; }
        [FromFld("case when t.unit_no=s_unit_no then s_wholesale_price when t.unit_no=m_unit_no then m_wholesale_price when t.unit_no=b_unit_no then b_wholesale_price end",LOAD_PURPOSE.SHOW)] public string wholesale_price { get; set; }

        public string wholesale_amount
        {
            get
            {
                if (wholesale_price != null && wholesale_price != "")
                {
                    return (CPubVars.ToDecimal(wholesale_price) * quantity).ToString();
                }

                return null;
            }
        }

        public string profit
        {
            get
            {
                if (wholesale_price != null && wholesale_price != "" && cost_amount != null && cost_amount != "")
                {
                    return (CPubVars.ToDecimal(wholesale_price) * quantity - CPubVars.ToDecimal(cost_amount)).ToString();
                }

                return "";
            }
        }
        public bool HasFromStockQty = false, HasToStockQty = false;
        internal decimal FromStockQty = 0, ToStockQty = 0, FromSellPendQty = 0, ToSellPendQty = 0;

        public override void SetInfoForPrint(bool smallUnitBarcode)
        {
            base.SetInfoForPrint(smallUnitBarcode);
            var row = this;
             
        }
    }
    public class SheetRowCombineTemplate : SheetRowItem
    {
        public SheetRowCombineTemplate()
        {

        }
        
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_spec { get; set; }
        public new string branch_id { get; set; } = "";
        public new string branch_name { get; set; } = "";
         public new string branch_position { get; set; } = "";
         public new string branch_position_name { get; set; } = "";
         public new string batch_id { get; set; } = "";
         public new string produce_date { get; set; } = "";
         public new string batch_no { get; set; } = "";
       
        [FromFld(LOAD_PURPOSE.SHOW)] public string son_mum_item { get; set; }
        internal decimal FromStockQty = 0, ToStockQty = 0, FromSellPendQty = 0, ToSellPendQty = 0;
    }

    public class SheetCombine : SheetBase<SheetRowCombine>
    {
        [SaveToDB] [FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB] [FromFld] public string from_branch_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string from_branch_name { get; set; } = "";
        [SaveToDB] [FromFld] public string to_branch_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string to_branch_name { get; set; } = "";
        [SaveToDB] [FromFld] public string seller_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_mobile { get; set; } = "";
        [SaveToDB][FromFld] public string apportionment_way { get; set; }//分配方式:price,count,不分摊
        [SaveToDB][FromFld] public decimal sub_other_fee1 { get; set; }//
        [SaveToDB][FromFld] public decimal sub_other_fee2 { get; set; }//其他费用
        [SaveToDB][FromFld] public decimal sub_other_fee3 { get; set; }//其他费用
        [SaveToDB][FromFld] public string sub_other_fee1_id { get; set; }//
        [SaveToDB][FromFld] public string sub_other_fee2_id { get; set; }//
        [SaveToDB][FromFld] public string sub_other_fee3_id { get; set; }//
        [FromFld(LOAD_PURPOSE.SHOW)]public string sub_other_fee1_name { get; set; }//
        [FromFld(LOAD_PURPOSE.SHOW)]public string sub_other_fee2_name { get; set; }//
        [FromFld(LOAD_PURPOSE.SHOW)] public string sub_other_fee3_name { get; set; }//
		public string other_fee1 { get; set; }//
		public string other_fee2{ get; set; }//
		public string other_fee3 { get; set; }//

		[FromFld("sheet_print_count",LOAD_PURPOSE.SHOW)]
        public override string print_count {
            get {
                string s = _print_count;
                if (s == "") s = "1";
                else s = (Convert.ToInt32(s) + 1).ToString();
                return s;
            }
            set
            { 
                _print_count = value;
            }
        }
        private string _print_count = "";

        [FromFld(LOAD_PURPOSE.SHOW)] public string from_branch_type { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string to_branch_type { get; set; }
 
    
 
        public string SaleOrderSheetIDs = "";
        public string SaleOrderSheetNos = "";
        public string sub_other_fee_way1
        {
            get
            {
                string payAmount = "";
                if (sub_other_fee1 != 0) payAmount = CPubVars.FormatMoney(sub_other_fee1, 2, true);

                if (sub_other_fee1_name != "")
                {
                    return sub_other_fee1_name + ":" + payAmount;
                }
                return "";
            }
        }
        public string sub_other_fee_way2
        {
            get
            {
                string payAmount = "";
                if (sub_other_fee2 != 0) payAmount = CPubVars.FormatMoney(sub_other_fee2, 2, true);

                if (sub_other_fee2_name != "")
                {
                    return sub_other_fee2_name + ":" + payAmount;
                }
                return "";
            }
        }
        public string sub_other_fee_way3
        {
            get
            {
                string payAmount = "";
                if (sub_other_fee3 != 0) payAmount = CPubVars.FormatMoney(sub_other_fee3, 2, true);

                if (sub_other_fee3_name != "")
                {
                    return sub_other_fee3_name + ":" + payAmount;
                }
                return "";
            }
        }

        public string sub_other_fee
        {
            get
            {
                string s = "";
                if (sub_other_fee1 != 0)
                {
                    s = $"{sub_other_fee1_name}:{CPubVars.FormatMoney(sub_other_fee1, 2, true)}";
                }
                if (sub_other_fee2 != 0)
                {
                    s += $"  {sub_other_fee2_name}:{CPubVars.FormatMoney(sub_other_fee2, 2, true)}";
                }
                if (sub_other_fee2 != 0)
                {
                    s += $"  {sub_other_fee2_name}:{CPubVars.FormatMoney(sub_other_fee2, 2, true)}";
                }
                return s;
            }
        }

        private void ConstructFun()
        {
            MainLeftJoin += @$"
                                  left join (select branch_id,branch_name as from_branch_name,branch_type as from_branch_type from info_branch where company_id=~company_id) fb on t.from_branch_id=fb.branch_id    
                                  left join (select branch_id,branch_name as  to_branch_name,branch_type as to_branch_type from info_branch where company_id=~company_id ) tb on t.to_branch_id=tb.branch_id           
                                  left join (select oper_id,oper_name as seller_name,mobile as seller_mobile from info_operator where company_id=~company_id) seller on t.seller_id=seller.oper_id
                                  left join (select oper_id,oper_name as maker_name from info_operator where company_id=~company_id) maker on t.maker_id=maker.oper_id
                                  left join  (select oper_id,oper_name as approver_name from info_operator where company_id=~company_id) approver on t.approver_id=approver.oper_id
                                  left join sheet_status_combine s on t.sheet_id=s.sheet_id and s.company_id = ~company_id
                                  left join(select sub_id,sub_name as sub_other_fee1_name from cw_subject where company_id = ~company_id) cw1 on cw1.sub_id = t.sub_other_fee1_id
                                  left join(select sub_id,sub_name as sub_other_fee2_name from cw_subject where company_id = ~company_id) cw2 on cw2.sub_id = t.sub_other_fee2_id
                                  left join(select sub_id,sub_name as sub_other_fee3_name from cw_subject where company_id = ~company_id) cw3 on cw3.sub_id = t.sub_other_fee3_id
                                   
                           ";
            DetailLeftJoin += $@" 
                                   left join sheet_combine_main m on t.sheet_id=m.sheet_id and m.company_id = ~company_id
                                   left join (select company_id,cost_price_type from company_setting where company_id=~company_id) s on s.company_id = t.company_id
                                   left join (select batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date from info_item_batch where company_id= ~company_id) itb on itb.batch_id = t.batch_id
                                   left join (select item_id,company_id,branch_id,stock_qty as from_stock_qty ,batch_id,branch_position from stock where company_id=~company_id) st on t.item_id = st.item_id  and t.company_id = st.company_id and COALESCE(t.batch_id,0) = st.batch_id and st.branch_position =  COALESCE(t.branch_position,0) and (CASE when t.inout_flag = -1 then COALESCE(t.branch_id,m.from_branch_id) else COALESCE(t.branch_id,m.to_branch_id) end) = st.branch_id
                                   left join info_item_prop ip on t.item_id=ip.item_id and ip.company_id = ~company_id 
                                   left join info_item_brand ib on ip.item_brand=ib.brand_id and ib.company_id = ~company_id
                                   left join (select class_id classId,class_name,order_index as class_order_index from info_item_class where company_id =~company_id) ic on ip.item_class=ic.classId 
                                   
		                           left join (select item_id,s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,s_unit->>'f3' as s_barcode,s_unit->>'f4' as s_wholesale_price,s_unit->>'f5' as s_retail_price,s_unit->>'f6' as s_buy_price,s_unit->>'f7' as s_cost_price_spec,
                                                            m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,m_unit->>'f3' as m_barcode,m_unit->>'f4' as m_wholesale_price,m_unit->>'f5' as m_retail_price,m_unit->>'f6' as m_buy_price,m_unit->>'f7' as m_cost_price_spec,
                                                            b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor,b_unit->>'f3' as b_barcode,b_unit->>'f4' as b_wholesale_price,b_unit->>'f5' as b_retail_price,b_unit->>'f6' as b_buy_price,b_unit->>'f7' as b_cost_price_spec from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,wholesale_price,retail_price,buy_price,cost_price_spec)) as json 
                                        from info_item_multi_unit where company_id=~company_id order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)) mu on ip.item_id=mu.item_id
                                left join info_branch ibb on ibb.branch_id = t.branch_id
                                left join info_branch_position ibp on ibp.branch_id = (CASE when t.inout_flag = -1 then COALESCE(t.branch_id,m.from_branch_id) else COALESCE(t.branch_id,m.to_branch_id) end) and ibp.branch_position = COALESCE(t.branch_position,0)

             
       ";
        }

        public SheetCombine(SHEET_SPLIT sheetSplit, LOAD_PURPOSE loadPurpose) : base("sheet_combine_main", "sheet_combine_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_COMBINE_ITEMS;
            if(sheetSplit== SHEET_SPLIT.SPLIT)
                sheet_type = SHEET_TYPE.SHEET_SPLIT_ITEMS;

            ConstructFun();
        
   
        }
        public SheetCombine(): base("sheet_combine_main", "sheet_combine_detail", LOAD_PURPOSE.SHOW)
        {
            ConstructFun();
        }
        protected class CInfoForApprove : CInfoForApproveBase
        {
            public string ArrearBalance = "", PrepayBalance = "";
            public List<SheetRowCombine> SheetRows = null;
            public bool RoleAllowNegativeStock = true;
            public bool FromBranchAllowNegativeStock = true;
            public string FromNegativeStockAccordance = "real";
            public bool ToBranchAllowNegativeStock = true;
            public string ToNegativeStockAccordance = "real";
        }
        protected override string GetSQLForTemplates(string companyID, string mainTable, string sheetIDs)
        {
            string sql =
            @$"select m.sheet_id,st.setting->>'companyName' as company_name,st.setting->>'contactTel' as company_tel,st.setting->>'companyAddress' as company_address, t.template_id,t.template_name, c.client_group_id,c.client_id from {mainTable} m 
                left join print_template t on t.sheet_type='DB' and t.company_id={companyID} 
                left join print_template_choose c on c.company_id={companyID} and t.template_id=c.template_id
                left join print_template_avail_elements pa on pa.sheet_type ='DB'
                left join company_setting st on m.company_id=st.company_id
                where m.company_id={companyID} and m.sheet_id in ({sheetIDs}) order by case when c.client_id is null then -1 else c.client_id end desc, case when c.client_group_id is null then -1 else c.client_group_id end desc, c.template_id, order_index;
                ";

            return sql;
        }
        protected override void InitForSave()
        {
            base.InitForSave();
            if (seller_id == "") seller_id = OperID;
          

        }
	 
       
        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            foreach (SheetRowCombine row in this.SheetRows)
            {
                string branchID = from_branch_id;
                string branchName = from_branch_name;
               if(row.inout_flag > 0)
                {
                    branchID = to_branch_id;
                    branchName = to_branch_name;
                }
                if (row.branch_position == "0" || string.IsNullOrWhiteSpace(row.branch_position)) continue;
                string sql = $@"select branch_position from info_branch_position where company_id = {company_id} and branch_position = {row.branch_position} and branch_id = {branchID}";
                dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (record == null)
                {
                    return $"{branchName}不存在库位：{row.branch_position_name}";
                }
            }
            string msg=await CheckBatch(cmd);
            if (msg != "") return msg;
            return "OK";

        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql;
            base.GetInfoForApprove_SetQQ(QQ);
            if (OperID != "")
            {
                //允许负库存
                sql = $"select rights->'delicacy'->'allowNegativeStock'->'value' role_allow_negative_stock from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
                QQ.Enqueue("role_allow_negative_stock", sql);
            }
            //查询仓库可不可为负
            if (from_branch_id != "")
            {
                sql = $"select branch_id,allow_negative_stock branch_allow_negative_stock,negative_stock_accordance from info_branch where company_id = {company_id} and branch_id in ({from_branch_id},{to_branch_id})";
                QQ.Enqueue("branch_allow_negative_stock", sql);

            }
      
            if (SheetRows.Count > 0)
            {
                string from_items_id = "";
                string to_items_id = "";
                string from_batchs_id = "";
                string to_batchs_id = "";
                string from_branchs_position = "";
                string to_branchs_position = "";
                foreach (SheetRowCombine row in SheetRows)
                {
                    if((row.inout_flag > 0 && red_flag =="") || (row.inout_flag < 0 && red_flag == "2"))//入仓
                    {
                        if (to_items_id != "") to_items_id += ",";
                        to_items_id += row.item_id;
                        if (to_batchs_id != "") to_batchs_id += ",";
                        to_batchs_id += row.batch_id;
                        if (to_branchs_position != "") to_branchs_position += ",";
                        to_branchs_position += row.branch_position;
                    }
                    else//出仓
                    {
                        if (from_items_id != "") from_items_id += ",";
                        from_items_id += row.item_id;
                        if (from_batchs_id != "") from_batchs_id += ",";
                        from_batchs_id += row.batch_id;
                        if (from_branchs_position != "") from_branchs_position += ",";
                        from_branchs_position += row.branch_position;
                    }
                    
                }
                if (from_batchs_id == "")
                {
                    from_batchs_id = "0";
                }
                if (to_batchs_id == "")
                {
                    to_batchs_id = "0";
                }

                sql = $@"select branch_id,item_id,stock_qty,sell_pend_qty,batch_id,branch_position from stock where company_id={company_id} and branch_id ={from_branch_id} and item_id in ({from_items_id}) and batch_id in ({from_batchs_id}) and branch_position in({from_branchs_position})
                        union
                        select branch_id,item_id,stock_qty,sell_pend_qty,batch_id,branch_position from stock where company_id={company_id} and branch_id ={to_branch_id} and item_id in ({to_items_id})  and batch_id in ({to_batchs_id}) and branch_position in({to_branchs_position})";
                QQ.Enqueue("stock", sql);
                sql = $@"select mu.item_id,mu.unit_no as item_unit_no,mu.unit_factor item_unit_factor,mu.buy_price, p.cost_price_avg from info_item_multi_unit mu left join info_item_prop p on p.item_id=mu.item_id where mu.company_id =  {company_id} and mu.item_id in ({from_items_id + "," + to_items_id}) and unit_type='s';";
                QQ.Enqueue("costPrice", sql);
                sql = $@"select item_id,sum(stock_qty) as old_stock_qty from stock  where company_id =  {company_id} and item_id in ({from_items_id + "," + to_items_id}) group by item_id;";
                QQ.Enqueue("totalStock", sql);
            }
        }

        public override string GetSheetCharactor()
        {
            string res =this.company_id +"_" + this.OperID + "_" + this.from_branch_id +"_" + this.to_branch_id+"_"+this.make_brief;
            foreach(var row in SheetRows)
            {
                res += row.item_id +"_" + row.item_name +"_"+ row.quantity;
            }
            return res;
        }
        public override string GetDataLockKey()
        {
            return this.company_id + "_" + this.from_branch_id;
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }

        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (sqlName == "role_allow_negative_stock")
            {
                dynamic roleAllowNegativeStock = CDbDealer.Get1RecordFromDr(dr);
                if (roleAllowNegativeStock != null)
                {
                    string r = roleAllowNegativeStock.role_allow_negative_stock;
                    if (r.ToLower() == "false") info.RoleAllowNegativeStock = false;
                }
            }
            else if (sqlName == "branch_allow_negative_stock")
            {
                List<ExpandoObject> lstBranchInfo = CDbDealer.GetRecordsFromDr(dr);
                if (lstBranchInfo != null)
                {
                    foreach(dynamic branch in lstBranchInfo)
                    {
                        if(branch.branch_id == from_branch_id)
                        {
                            string b = branch.branch_allow_negative_stock;
                            if (b.ToLower() == "false") info.FromBranchAllowNegativeStock = false;
                            info.FromNegativeStockAccordance = branch.negative_stock_accordance;
                        }
                        if (branch.branch_id == to_branch_id)
                        {
                            string b = branch.branch_allow_negative_stock;
                            if (b.ToLower() == "false") info.ToBranchAllowNegativeStock = false;
                            info.ToNegativeStockAccordance = branch.negative_stock_accordance;
                        }

                    }
                    
                }
            } 
            else if (sqlName == "stock")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowCombine row in SheetRows)
                    {
                        string rowBranchId = from_branch_id;
                        if (red_flag == "")
                        {
                            if(row.inout_flag < 0)
                            {
                                rowBranchId = from_branch_id;
                            }
                            else
                            {
                                rowBranchId = to_branch_id;
                            }
                        }
                        else//红字单
                        {
                            if(row.inout_flag < 0)
                            {
                                rowBranchId = to_branch_id;
                            }
                            else
                            {
                                rowBranchId = from_branch_id;
                            }
                        }
                        if(rowBranchId == rec.branch_id && row.item_id == rec.item_id && row.branch_position == rec.branch_position && row.batch_id == rec.batch_id)
                        {
                            row.HasStockQty = true;
                            row.StockQty = CPubVars.ToDecimal(rec.stock_qty);
                            row.SellPendQty = CPubVars.ToDecimal(rec.sell_pend_qty == "" ? 0 : rec.sell_pend_qty);
                        }
                    }
                }
                info.SheetRows = SheetRows;
                List<SheetRowCombine> s1 = new List<SheetRowCombine>();
                List<SheetRowCombine> s2 = new List<SheetRowCombine>();
                foreach (SheetRowCombine row in info.SheetRows)
                {
                    if(row.inout_flag < 0)
                    {
                        s1.Add(row);
                    }
                    else
                    {
                        s2.Add(row);
                    }
                }
                List<SheetRowCombine> MergedSheetRows1 = MergeSheetRowsByBatchAndItem(s1);
                List<SheetRowCombine> MergedSheetRows2 = MergeSheetRowsByBatchAndItem(s2);
                MergedSheetRows2.ForEach(MergedSheetRow => MergedSheetRows1.Add(MergedSheetRow));
                //this.MergedSheetRows = MergedSheetRows1;
                this.MergedSheetRowByBatchAndItem = MergedSheetRows1;



               int rowIndexFrom = 1;
                int rowIndexTo = 1;
                foreach (var row in this.MergedSheetRowByBatchAndItem)
                {
                    #region 判断出仓负库存
                    if((row.inout_flag < 0 && red_flag=="") || (row.inout_flag > 0 && red_flag=="2"))
                    {
                        decimal newFromStockQty = 0;
                        string fromBranchAccordance = "实际";
                        if (info.FromNegativeStockAccordance == "real")
                        {                           
                            if (red_flag=="") newFromStockQty = row.StockQty - row.quantity;
                            else newFromStockQty = row.StockQty + row.quantity;
                                
                        }
                        else
                        {                            
                            fromBranchAccordance = "可用";    
                        
                            if (red_flag == "")
                            {
                                newFromStockQty = row.StockQty - row.quantity - row.SellPendQty;
                            }
                            else
                            {
                                newFromStockQty = row.StockQty + row.quantity - row.SellPendQty ;
                            }
                         
                        }

                        if (!info.RoleAllowNegativeStock || !info.FromBranchAllowNegativeStock)
                        {
                            if (newFromStockQty < -0.01m)
                            {
                                info.ErrMsg = $"第{rowIndexFrom}行{row.item_name}在{from_branch_name} 出现负{fromBranchAccordance}库存";
                                if (red_flag == "2") info.ErrMsg += ",红冲失败";
                                else info.ErrMsg += ",审核失败";
                                break;
                            }
                        }
                        rowIndexFrom++;
                    }

                    #endregion

                    #region 判断入仓负库存
                    if ((row.inout_flag>0 && red_flag=="")||(row.inout_flag < 0 && red_flag=="2"))
                    {
                        decimal newToStockQty = 0;
                        string toBranchAccordance = "实际";
                        if (info.ToNegativeStockAccordance == "real")
                        {
                           if(red_flag=="")  newToStockQty = row.StockQty + row.quantity;
                           else newToStockQty = row.StockQty - row.quantity;
                        }
                        else
                        {
                            toBranchAccordance = "可用";
                            if(red_flag=="") newToStockQty = row.StockQty + row.quantity - row.SellPendQty;
                            else newToStockQty = row.StockQty - row.quantity - row.SellPendQty;
                        }

                        if (!info.RoleAllowNegativeStock || !info.ToBranchAllowNegativeStock)
                        {
                            if (newToStockQty < -0.01m)
                            {
                                info.ErrMsg = $"第{rowIndexTo}行{row.item_name}在{to_branch_name} 出现负{toBranchAccordance}库存";
                                if (red_flag == "2") info.ErrMsg += ",红冲失败";
                                else info.ErrMsg += ",审核失败";
                                break;
                            }
                        }
                         rowIndexTo++;
                    }
                       
                    #endregion
                }
                
            }
            else if (sqlName == "costPrice")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowCombine row in SheetRows)
                    {
                        if (row.item_id == rec.item_id)
                        {
                            if (rec.buy_price != "" && rec.buy_price != null ) row.buy_price =rec.buy_price;
                            if (rec.cost_price_avg != "" && rec.cost_price_avg != null) row.cost_price_avg = rec.cost_price_avg;
                        }
                    }
                }
            }
            else if(sqlName == "totalStock")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowCombine row in SheetRows)
                    {
                        if (row.item_id == rec.item_id)
                        {
                            row.old_stock_qty = CPubVars.ToDecimal(rec.old_stock_qty == "" ? 0 : rec.old_stock_qty);
                        }
                    }
                }
            }
        }
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
 
            if (from_branch_id == "") return "必须指定出仓";
            if (to_branch_id == "") return "必须指定入仓";
            if (seller_id == "" && IsFromWeb) return "必须指定业务员";
            if (SheetRows.Count == 0) return "必须指定商品行";

            return "OK";
        }
 
        //更新库存
        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            if (FIXING_ARREARS) return "";
            //CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";
            List<SheetRowCombine> newListInRows = new List<SheetRowCombine>();
            List<SheetRowCombine> newListOutRows = new List<SheetRowCombine>();
            foreach (SheetRowCombine row in this.SheetRows)
            {
                if(row.inout_flag > 0)
                {
                    newListInRows.Add(row);
                }else if(row.inout_flag < 0)
                {
                    newListOutRows.Add(row);
                }
            }
            //var mergedInRows = MergeSheetRows(newListInRows);
            //var mergedOutRows = MergeSheetRows(newListOutRows);
            var mergedInRows = MergeSheetRowsByBatchAndItem(newListInRows);
            var mergedOutRows = MergeSheetRowsByBatchAndItem(newListOutRows); 
            foreach (SheetRowCombine row in mergedInRows)
            {
                mergedOutRows.Add(row);
            }
            MergedSheetRows = mergedOutRows;
            foreach (SheetRowCombine row in MergedSheetRows)
            {
                int inoutFlag = row.inout_flag;
                if (red_flag == "2") inoutFlag *= -1;
                string s = "";
                string changeQty = "";

                decimal qty;
                qty = row.inout_flag * row.quantity;
                changeQty = qty.ToString();
                if (changeQty == "-0") changeQty = "0";
                if (qty >= 0)
                {
                    changeQty = "+" + qty.ToString();
                }
                string branch_id = "";
                //string batch_id = "";
                if(inoutFlag > 0)
                {
                    branch_id = to_branch_id;
                }
                else
                {
                    branch_id = from_branch_id;
                }
                if (string.IsNullOrEmpty(row.batch_id))  row.batch_id = "0";
                
                if (row.HasStockQty)
                {
                    s = $"update stock set stock_qty=stock_qty{changeQty} where company_id={company_id} and branch_id={branch_id} and item_id={row.item_id} and batch_id = {row.batch_id} and branch_position = {row.branch_position};";
                }
                else
                {
                    s = $"insert into stock(company_id,branch_id,item_id,stock_qty,batch_id,branch_position) values ({company_id},{branch_id},{row.item_id},{qty},{row.batch_id},{row.branch_position}) on conflict(company_id,branch_id,item_id,batch_id,branch_position) do update set stock_qty=stock.stock_qty{changeQty};";
                }
                sql += s;
                row.NewStockQty = row.StockQty + qty;
            }
            return sql;
        }
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {

            CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";
            foreach (SheetRowCombine row in MergedSheetRows)
            {
                string branch_id = "";
                if ((row.inout_flag > 0 && red_flag == "") || (row.inout_flag < 0 && red_flag == "2"))
                {
                    branch_id = to_branch_id;
                }
                else
                {
                    branch_id = from_branch_id;
                }
                string batchId = row.batch_id == null ? "0" : row.batch_id;
                string s = $@"insert into stock_change_log 
        (company_id, approve_time,     branch_id,    item_id,    sheet_id,  pre_stock_qty ,new_stock_qty,      pre_sell_pend_qty ,new_sell_pend_qty,batch_id,branch_position) 
values ({company_id},'{approve_time}',{branch_id},{row.item_id},{sheet_id},{row.StockQty},{row.NewStockQty}, {row.SellPendQty} ,{row.NewSellPendQty},{batchId},{row.branch_position});";

                sql += s;
            }

            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
            if (FIXING_ARREARS) return;
            if (IsImported) return;
            string dealing_happen_time = happen_time;
            if (red_flag == "2")
            {
                dynamic data = await CDbDealer.Get1RecordFromSQLAsync($"select happen_time from sheet_combine_main where company_id = {company_id} and sheet_id = {red_sheet_id}", cmd);
                if (data != null) dealing_happen_time = data.happen_time;
            }

            List<SheetRowCostPrice> costPriceRows = new List<SheetRowCostPrice>();
            foreach (SheetRowCombine row in SheetRows)
            {
                int inoutFlag = 1;
                if (red_flag == "2") inoutFlag = -1;
                if(row.inout_flag == inoutFlag)
                {
                    SheetRowCostPrice costRow = new SheetRowCostPrice();
                    costRow.item_id = row.item_id;
                    costRow.unit_no = row.unit_no;
                    costRow.inout_flag = row.inout_flag;
                    costRow.unit_factor = row.unit_factor;
                    costRow.quantity = row.quantity;
                    costRow.sub_amount = row.cost_amount.IsValid()?CPubVars.ToDecimal(row.cost_amount):0;//当前成本
                    costRow.cost_price_avg = row.cost_price_avg.IsValid() ? Decimal.Parse(row.cost_price_avg.ToString(), System.Globalization.NumberStyles.Float) : 0;
                    costRow.item_cost_price_suspect = false;//这是啥？
                    costRow.old_total_qty = row.old_stock_qty;//审核单据前的商品库存
                    costPriceRows.Add(costRow);
                }
            }
            await UpdateCostPriceAvg(cmd, costPriceRows, dealing_happen_time);
        }

        public override List<SheetRowCombine> MergeSheetRows(List<SheetRowCombine> rows, bool bIgnoreNativeQty = false)  
        {
            Dictionary<string, SheetRowCombine> rowsDict = new Dictionary<string, SheetRowCombine>();
            foreach (SheetRowCombine sheetRow in rows)
            {
                if (sheetRow.inout_flag == 0) continue;
                if (bIgnoreNativeQty && sheetRow.quantity < 0) continue;
                string skey = sheetRow.item_id;// +"_" + sheetRow.unit_factor.ToString();
                SheetRowCombine curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    string s = JsonConvert.SerializeObject(sheetRow);
                    curRow = JsonConvert.DeserializeObject<SheetRowCombine>(s);
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_factor = 1;
                    curRow.StockQty = sheetRow.StockQty;
                    curRow.SellPendQty = sheetRow.SellPendQty;
                    curRow.HasStockQty = sheetRow.HasStockQty;
                    rowsDict.Add(skey, curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                    curRow.cost_amount += sheetRow.cost_amount;
                }
            }
            List<SheetRowCombine> newList = new List<SheetRowCombine>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }

        public List<SheetRowCombine> MergeSheetRowsByBatchAndItem(List<SheetRowCombine> rows,bool bIgnoreNativeQty = false)
        {
            List<SheetRowCombine> rowsDict = new List<SheetRowCombine>();

            foreach (SheetRowCombine sheetRow in rows)
            {
                if (sheetRow.inout_flag == 0) continue;
                if (bIgnoreNativeQty && sheetRow.quantity < 0) continue;
                SheetRowCombine curRow = null;
                foreach (SheetRowCombine rowDic in rowsDict)
                {
                    if (sheetRow.item_id == rowDic.item_id && sheetRow.batch_id == rowDic.batch_id &&sheetRow.branch_position == rowDic.branch_position)
                    {
                        curRow = rowDic;
                        break;
                    }
                }
                if (curRow == null)
                {
                    string s = JsonConvert.SerializeObject(sheetRow);
                    curRow = JsonConvert.DeserializeObject<SheetRowCombine>(s);
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_factor = 1;
                    curRow.StockQty = sheetRow.StockQty;
                    curRow.SellPendQty = sheetRow.SellPendQty;
                    curRow.HasStockQty = sheetRow.HasStockQty;
                    curRow.batch_id = sheetRow.batch_id;
                    curRow.branch_position = sheetRow.branch_position;
                    rowsDict.Add(curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                    curRow.cost_amount += sheetRow.cost_amount;
                }
            }
            return rowsDict;

        }


        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting);
            bool canSeeBuyPrice =false;
            if (printTemplate != null){
                canSeeBuyPrice = printTemplate[0].canSeeBuyPrice;
            }
            decimal b_qty = 0, m_qty = 0, s_qty = 0;
            foreach (var row in SheetRows)
            {
                row.SetInfoForPrint(smallUnitBarcode);
                b_qty += row.b_quantity;
                m_qty += row.m_quantity;
                s_qty += row.s_quantity;
                if (row.inout_flag == 1)
                {
                    row.branch_type = "入";
                }
                else
                {
                    row.branch_type = "出";
                }
                if (!canSeeBuyPrice)
                {
                    row.buy_price = "";
                    row.cost_price_avg = "";
                    row.cost_price_spec = "";
                }
            }
            string sumQty = "";
            if (b_qty != 0) sumQty += CPubVars.FormatMoney(b_qty, 2) + "大";
            if (m_qty != 0) sumQty += CPubVars.FormatMoney(m_qty, 2) + "中";
            if (s_qty != 0) sumQty += CPubVars.FormatMoney(s_qty, 2) + "小";
            this.sum_quantity_unit_conv = sumQty;
            this.other_fee1 = this.sub_other_fee1_name + (this.sub_other_fee1!=0?"("+ this.sub_other_fee1 + ")" : ""); 
			this.other_fee2 = this.sub_other_fee2_name + (this.sub_other_fee2 != 0 ? "(" + this.sub_other_fee2 + ")" : "");
			this.other_fee3 = this.sub_other_fee3_name +(this.sub_other_fee3 != 0 ? "(" + this.sub_other_fee3 + ")" : "");
            if (printTemplate != null)
            {
                string sPrintTemplate = JsonConvert.SerializeObject(printTemplate);
                if (sPrintTemplate.Contains("\"print_count\""))
                {
                    string sql = $"select sheet_print_count from sheet_status_combine where company_id = {company_id} and sheet_id = {sheet_id};";
                    cmd.CommandText = sql;
                    dynamic rec = await cmd.ExecuteScalarAsync();
                    if (rec != null)
                    {
                        this.print_count = Convert.ToString(rec);
                    }
                    if (this.print_count == "") this.print_count = "1";
                    else this.print_count = (Convert.ToInt32(this.print_count) + 1).ToString();
                }
            }
        }
        public async Task UpdateCostAmount(CMySbCommand cmd,string sheetId,int costPriceAvg)
        {
            string sql = $"select * from sheet_combine_detail where company_id ={this.company_id} and sheet_id = {sheetId} ";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("sheetRowsData", sql);
            List<ExpandoObject> sheetRowsData = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheetRowsData") sheetRowsData = CDbDealer.GetRecordsFromDr(dr, false);
            }
            QQ.Clear();
            Console.WriteLine(sheetRowsData.ToArray());
            //重新分配成本
            //List outRows = null;
            //List inRows = null;
            //sheetRowsData.Value.ForEach(row =>
            //{

            //});
        }
        public override async Task<string> BeforeRedAndChange(CMySbCommand cmd)
        {
            return await CheckBatch(cmd);
        }
        public async Task<string> CheckBatch(CMySbCommand cmd ,bool bAutoCommit = true)
        {
            string msg = "";
            string insertSql = "";
            string insertValue = "";
            string selectSql = "";
            string selectValue = "";
            Dictionary<string, dynamic> batchDic = new Dictionary<string, dynamic>();
            Dictionary<string, string> sheetRowBatch = new Dictionary<string, string>();
            try
            {

                foreach (SheetRowCombine row in SheetRows)
                {
                    if (row.produce_date.IsInvalid() || row.produce_date == "无产期")
                    {
                        continue;
                    }
                    string key = row.produce_date + row.batch_no;
                    if (!batchDic.ContainsKey(key))
                    {
                        batchDic[key] = new { produce_date = row.produce_date, batch_no = row.batch_no };
                        if(selectValue!="")  selectValue += ",";
                        selectValue += $@"('{row.produce_date}','{row.batch_no}')";
                    }
                }
                if (selectValue != "") selectSql = $@"select * from info_item_batch where company_id = {company_id} and (substring(produce_date::text,1,10),batch_no) in ({selectValue});";
                if (selectSql != "")
                {
                    List<ExpandoObject> selectRec = await CDbDealer.GetRecordsFromSQLAsync(selectSql, cmd);
                    foreach (dynamic row in selectRec)
                    {
                        string produceDate = row.produce_date;
                        string batchNo = row.batch_no;
                        produceDate = produceDate.Substring(0, 10);
                        string key = produceDate + batchNo;
                        sheetRowBatch.Add(key, row.batch_id);
                        if (batchDic.ContainsKey(key)) batchDic.Remove(key);
                    }
                    foreach(KeyValuePair<string,dynamic> kv in batchDic)
                    {
                        string produceDate = kv.Value.produce_date;
                        string batchNo = kv.Value.batch_no;
                        if(insertValue!="")  insertValue += ",";
                        insertValue += $@"({company_id},'{produceDate}','{batchNo}')";
                    }
                    if (insertValue!="")//INSERT INTO
                    {
                        insertSql += $"insert into info_item_batch (company_id,produce_date,batch_no) values {insertValue} on CONFLICT(company_id,produce_date,batch_no) DO NOTHING RETURNING batch_id,produce_date,batch_no;";
                        List<ExpandoObject> insertRec = await CDbDealer.GetRecordsFromSQLAsync(insertSql, cmd);
                        foreach (dynamic row in insertRec)
                        {
                            string produceDate = row.produce_date;
                            string batchNo = row.batch_no;
                            produceDate = produceDate.Substring(0, 10);
                            string key = produceDate + batchNo;
                            sheetRowBatch.Add(key, row.batch_id);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                msg = "生产日期/批次错误";
                MyLogger.LogMsg(msg + e.Message + e.StackTrace + "SQL:" + cmd.CommandText, company_id, "produce_date");
            }
            if (msg == "")
            {
                foreach (SheetRowCombine row in SheetRows)
                {
                    if (row.produce_date.IsInvalid() || row.produce_date == "无产期")
                    {
                        row.batch_id = "0";
                        continue;
                    }
                    string key = row.produce_date + row.batch_no;
                    if (sheetRowBatch.ContainsKey(key))
                    {
                        row.batch_id = sheetRowBatch[key];
                    }
                    else
                    {
                        msg = "生产日期/批次错误";
                    }
                }
            }
            return msg;
        }

    }
    public class CombineTemplate : SheetBase<SheetRowCombineTemplate>
    {
        
        public new string sheet_no { get; set; }
        public new string red_flag { get; set; }
       public new string red_sheet_id { get; set; }
        public new string make_time { get; set; }
        public new string approver_id { get; set; }
        public new string approver_name { get; set; }
       public new string approve_time { get; set; }
       public new string approve_brief { get; set; }
        [SaveToDB][FromFld] public string model_name { get; set; }



        private void ConstructFun()
        {
            MainLeftJoin += @$"
                                  left join (select oper_id,oper_name as maker_name from info_operator where company_id=~company_id) maker on t.maker_id=maker.oper_id
                           ";
            DetailLeftJoin += $@" 
                                   left join combine_template_main m on t.sheet_id=m.sheet_id and m.company_id = ~company_id
                                   left join (select company_id,cost_price_type from company_setting where company_id=~company_id) s on s.company_id = t.company_id
                                   left join (select sum(stock_qty) as stock_qty_unit,item_id from stock where company_id = ~company_id group by item_id) stock on stock.item_id = t.item_id
                                   left join info_item_prop ip on t.item_id=ip.item_id and ip.company_id = ~company_id 
                                   left join info_item_brand ib on ip.item_brand=ib.brand_id and ib.company_id = ~company_id
                                   left join (select class_id classId,class_name,order_index as class_order_index from info_item_class where company_id =~company_id) ic on ip.item_class=ic.classId 
                                   
		                           left join (select item_id,s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,s_unit->>'f3' as s_barcode,s_unit->>'f4' as s_wholesale_price,s_unit->>'f5' as s_retail_price,s_unit->>'f6' as s_buy_price,s_unit->>'f7' as s_cost_price_spec,
                                                            m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,m_unit->>'f3' as m_barcode,m_unit->>'f4' as m_wholesale_price,m_unit->>'f5' as m_retail_price,m_unit->>'f6' as m_buy_price,m_unit->>'f7' as m_cost_price_spec,
                                                            b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor,b_unit->>'f3' as b_barcode,m_unit->>'f4' as b_wholesale_price,b_unit->>'f5' as b_retail_price,b_unit->>'f6' as b_buy_price,b_unit->>'f7' as b_cost_price_spec from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,wholesale_price,retail_price,buy_price,cost_price_spec)) as json 
                                        from info_item_multi_unit where company_id=~company_id order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)) mu on ip.item_id=mu.item_id

             
       ";
        }

        //public CombineTemplate(SHEET_SPLIT sheetSplit, LOAD_PURPOSE loadPurpose) : base("combine_template_main", "combine_template_detail", loadPurpose)
        //{
        //    sheet_type = SHEET_TYPE.SHEET_COMBINE_ITEMS;
        //    if (sheetSplit == SHEET_SPLIT.SPLIT)
        //        sheet_type = SHEET_TYPE.SHEET_SPLIT_ITEMS;

        //    ConstructFun();


        //}
        public CombineTemplate() : base("combine_template_main", "combine_template_detail", LOAD_PURPOSE.SHOW)

        {
            sheet_type = SHEET_TYPE.SHEET_COMBINE_MODEL;
            ConstructFun();
        }
       
        protected class CInfoForApprove : CInfoForApproveBase
        {
            public string ArrearBalance = "", PrepayBalance = "";
            public List<SheetRowCombine> SheetRows = null;
            public bool RoleAllowNegativeStock = true;
            public bool FromBranchAllowNegativeStock = true;
            public string FromNegativeStockAccordance = "real";
            public bool ToBranchAllowNegativeStock = true;
            public string ToNegativeStockAccordance = "real";
        }
        protected override string GetSQLForTemplates(string companyID, string mainTable, string sheetIDs)
        {
            string sql =
            @$"select m.sheet_id,st.setting->>'companyName' as company_name,st.setting->>'contactTel' as company_tel,st.setting->>'companyAddress' as company_address, t.template_id,t.template_name, c.client_group_id,c.client_id from {mainTable} m 
                left join print_template t on t.sheet_type='DB' and t.company_id={companyID} 
                left join print_template_choose c on c.company_id={companyID} and t.template_id=c.template_id
                left join print_template_avail_elements pa on pa.sheet_type ='DB'
                left join company_setting st on m.company_id=st.company_id
                where m.company_id={companyID} and m.sheet_id in ({sheetIDs}) order by case when c.client_id is null then -1 else c.client_id end desc, case when c.client_group_id is null then -1 else c.client_group_id end desc, c.template_id, order_index;
                ";

            return sql;
        }
        protected override void InitForSave()
        {
            base.InitForSave();
            //if (seller_id == "") seller_id = OperID;


        }
        //protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        //{
        //    string sql;
        //    base.GetInfoForApprove_SetQQ(QQ);
        //    if (OperID != "")
        //    {
        //        sql = $"select rights->'delicacy'->'allowNegativeStock'->'value' role_allow_negative_stock from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
        //        QQ.Enqueue("role_allow_negative_stock", sql);
        //    }

        //    if (from_branch_id != "")
        //    {
        //        sql = $"select branch_id,allow_negative_stock branch_allow_negative_stock,negative_stock_accordance from info_branch where company_id = {company_id} and branch_id in ({from_branch_id},{to_branch_id})";
        //        QQ.Enqueue("branch_allow_negative_stock", sql);

        //    }

        //    if (SheetRows.Count > 0)
        //    {
        //        string items_id = "";
        //        foreach (SheetRowCombine row in SheetRows)
        //        {
        //            if (items_id != "") items_id += ",";
        //            items_id += row.item_id;
        //        }

        //        sql = $"select branch_id,item_id,stock_qty,sell_pend_qty from stock where company_id={company_id} and branch_id in ({from_branch_id},{to_branch_id}) and item_id in ({items_id})";
        //        QQ.Enqueue("stock", sql);
        //        sql = $@"select mu.item_id,mu.unit_no as item_unit_no,mu.unit_factor item_unit_factor,mu.buy_price,p.cost_price_avg from info_item_multi_unit mu left join info_item_prop p on p.item_id=mu.item_id where mu.company_id =  {company_id} and mu.item_id in ({items_id}) and unit_type='s';";
        //        QQ.Enqueue("costPrice", sql);
        //    }
        //}

        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + "_" +  "_" + this.make_brief;
            //string res = this.company_id + "_" + this.OperID + "_" + this.from_branch_id + "_" + this.to_branch_id + "_" + this.make_brief;
            foreach (var row in SheetRows)
            {
                res += row.item_id + "_" + row.item_name + "_" + row.quantity;
            }
            return res;
        }
        public override string GetDataLockKey()
        {
            return this.company_id;
            //return this.company_id + "_" + this.from_branch_id;
        }
        //protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        //{
        //    if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
        //    CInfoForApprove info = (CInfoForApprove)InfoForApprove;

        //    base.GetInfoForApprove_ReadData(dr, sqlName);
        //    if (sqlName == "role_allow_negative_stock")
        //    {
        //        dynamic roleAllowNegativeStock = CDbDealer.Get1RecordFromDr(dr);
        //        if (roleAllowNegativeStock != null)
        //        {
        //            string r = roleAllowNegativeStock.role_allow_negative_stock;
        //            if (r.ToLower() == "false") info.RoleAllowNegativeStock = false;
        //        }
        //    }
        //    else if (sqlName == "branch_allow_negative_stock")
        //    {
        //        List<ExpandoObject> lstBranchInfo = CDbDealer.GetRecordsFromDr(dr);
        //        if (lstBranchInfo != null)
        //        {
        //            foreach (dynamic branch in lstBranchInfo)
        //            {
        //                if (branch.branch_id == from_branch_id)
        //                {
        //                    string b = branch.branch_allow_negative_stock;
        //                    if (b.ToLower() == "false") info.FromBranchAllowNegativeStock = false;
        //                    info.FromNegativeStockAccordance = branch.negative_stock_accordance;
        //                }
        //                else if (branch.branch_id == to_branch_id)
        //                {
        //                    string b = branch.branch_allow_negative_stock;
        //                    if (b.ToLower() == "false") info.ToBranchAllowNegativeStock = false;
        //                    info.ToNegativeStockAccordance = branch.negative_stock_accordance;
        //                }

        //            }

        //        }
        //    }
        //    else if (sqlName == "stock")
        //    {
        //        List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
        //        foreach (dynamic rec in records)
        //        {
        //            foreach (SheetRowCombine row in SheetRows)
        //            {
        //                if (row.item_id == rec.item_id)
        //                {
        //                    if (rec.stock_qty != "")
        //                    {
        //                        if ((string)rec.branch_id == from_branch_id)
        //                        {
        //                            row.HasFromStockQty = true;
        //                            //row.FromStockQty = CPubVars.ToDecimal(rec.stock_qty);
        //                            // row.FromSellPendQty = CPubVars.ToDecimal(rec.sell_pend_qty);
        //                        }
        //                        else if ((string)rec.branch_id == to_branch_id)
        //                        {
        //                            row.HasToStockQty = true;
        //                            // row.ToStockQty = CPubVars.ToDecimal(rec.stock_qty);
        //                            // row.ToSellPendQty = CPubVars.ToDecimal(rec.sell_pend_qty);
        //                        }
        //                    }
        //                }
        //            }
        //        }
        //        info.SheetRows = SheetRows;

        //        this.MergedSheetRows = MergeSheetRows(this.SheetRows);


        //        int rowIndex = 1;
        //        foreach (var row in this.MergedSheetRows)
        //        {
        //            #region 判断出仓负库存
        //            decimal newFromStockQty = 0;
        //            string fromBranchAccordance = "实际";
        //            if (info.FromNegativeStockAccordance == "real")
        //            {
        //                //  if (red_flag!="2")
        //                //      newFromStockQty = row.FromStockQty - row.quantity;
        //            }
        //            else
        //            {
        //                fromBranchAccordance = "可用";


        //                if (red_flag != "2")
        //                {
        //                    //  newFromStockQty = row.FromStockQty - row.FromSellPendQty - row.quantity; 
        //                }

        //            }

        //            if (!info.RoleAllowNegativeStock || !info.FromBranchAllowNegativeStock)
        //            {
        //                if (newFromStockQty < -0.01m)
        //                {
        //                    info.ErrMsg = $"第{rowIndex}行{row.item_name}在{from_branch_name} 出现负{fromBranchAccordance}库存";
        //                    if (red_flag == "2") info.ErrMsg += ",红冲失败";
        //                    else info.ErrMsg += ",审核失败";
        //                    break;
        //                }
        //            }

        //            #endregion

        //            #region 判断入仓负库存
        //            if (red_flag == "2")
        //            {
        //                decimal newToStockQty = 0;
        //                string toBranchAccordance = "实际";
        //                if (info.ToNegativeStockAccordance == "real")
        //                {
        //                    //  newToStockQty = row.ToStockQty - row.quantity;
        //                }
        //                else
        //                {
        //                    toBranchAccordance = "可用";
        //                    //   newToStockQty = row.ToStockQty - row.quantity - row.ToSellPendQty;
        //                }

        //                if (!info.RoleAllowNegativeStock || !info.ToBranchAllowNegativeStock)
        //                {
        //                    if (newToStockQty < -0.01m)
        //                    {
        //                        info.ErrMsg = $"第{rowIndex}行{row.item_name}在{to_branch_name} 出现负{toBranchAccordance}库存";
        //                        if (red_flag == "2") info.ErrMsg += ",红冲失败";
        //                        else info.ErrMsg += ",审核失败";
        //                        break;
        //                    }
        //                }
        //            }

        //            #endregion

        //            rowIndex++;
        //        }

        //    }
        //    else if (sqlName == "costPrice")
        //    {
        //        List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
        //        foreach (dynamic rec in records)
        //        {
        //            foreach (SheetRowCombine row in SheetRows)
        //            {
        //                if (row.item_id == rec.item_id)
        //                {
        //                    if (rec.buy_price != "" && rec.buy_price != null) row.buy_price = CPubVars.ToDecimal(rec.buy_price);
        //                    if (rec.cost_price_avg != "" && rec.cost_price_avg != null) row.cost_price_avg = CPubVars.TryToDecimal(rec.cost_price_avg);
        //                }
        //            }
        //        }
        //    }
        //}
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSheetValid(cmd);
            if (check != "OK") return check;

            //if (from_branch_id == "") return "必须指定出仓";
            //if (to_branch_id == "") return "必须指定入仓";
            //if (seller_id == "" && IsFromWeb) return "必须指定业务员";
            if (SheetRows.Count == 0) return "必须指定商品行";
            //if (from_branch_id == to_branch_id) return "出仓与入仓不能相同";

            return "OK";
        }

       

        //protected override string GetApproveSQL(CInfoForApproveBase info1)
        //{
        //    CInfoForApprove info = (CInfoForApprove)info1;
        //    string sql = "";
        //    int inoutFlag = -1;
        //    if (red_flag == "2") inoutFlag *= -1;

        //    foreach (SheetRowCombine row in info.SheetRows)
        //    {
        //        string s = "";
        //        string changeQty = "";

        //        decimal qty;
        //        //compute from branch stock qty
        //        qty = inoutFlag * row.quantity * row.unit_factor;
        //        changeQty = qty.ToString();
        //        if (changeQty == "-0") changeQty = "0";
        //        if (qty >= 0)
        //        {
        //            changeQty = "+" + qty.ToString();
        //        }
        //        if (row.HasFromStockQty)
        //        {
        //            s = $"update stock set stock_qty=stock_qty{changeQty} where company_id={company_id} and branch_id={from_branch_id} and item_id={row.item_id};";
        //        }
        //        else
        //        {
        //            s = $"insert into stock(company_id,branch_id,item_id,stock_qty) values ({company_id},{from_branch_id},{row.item_id},{qty}) on conflict (company_id,branch_id,item_id) do update set stock_qty=stock.stock_qty{changeQty};";
        //        }
        //        sql += s;
        //        //row.NewStockQty = row.FromStockQty + qty;
        //        //compute to branch stock qty
        //        qty = -1 * inoutFlag * row.quantity * row.unit_factor;
        //        changeQty = qty.ToString();
        //        if (changeQty == "-0") changeQty = "0";
        //        if (qty >= 0)
        //        {
        //            changeQty = "+" + qty.ToString();
        //        }
        //        if (row.HasToStockQty)
        //        {
        //            s = $"update stock set stock_qty=stock_qty{changeQty} where company_id={company_id} and branch_id={to_branch_id} and item_id={row.item_id};";
        //        }
        //        else
        //        {
        //            s = $"insert into stock(company_id,branch_id,item_id,stock_qty) values ({company_id},{to_branch_id},{row.item_id},{qty}) on conflict (company_id,branch_id,item_id) do update set stock_qty=stock.stock_qty{changeQty};";
        //        }
        //        sql += s;
        //    }
        //    return sql;
        //}

        //        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        //        {
        //            string sql = "";
        //            int inoutFlag = -1;
        //            if (red_flag == "2") inoutFlag *= -1;
        //            foreach (var row in MergedSheetRows)
        //            {
        //                /*
        //                decimal qty = inoutFlag * row.quantity * row.unit_factor;
        //                decimal newStock = row.FromStockQty + qty;
        //                string s = $@"insert into stock_change_log 
        //        (company_id, approve_time,    branch_id,       item_id,    sheet_id,     pre_stock_qty, new_stock_qty ) 
        //values ({company_id},'{approve_time}',{from_branch_id},{row.item_id},{sheet_id},{row.FromStockQty},{newStock});";
        //                sql += s;

        //                qty *= -1;
        //                newStock = row.ToStockQty + qty; 
        //                s = $@"insert into stock_change_log 
        //        (company_id, approve_time,    branch_id,       item_id,    sheet_id,   pre_stock_qty, new_stock_qty ) 
        //values ({company_id},'{approve_time}',{to_branch_id},{row.item_id},{sheet_id},{row.ToStockQty},{newStock});";

        //                sql += s;
        //                */
        //            }
        //            cmd.CommandText = sql;
        //            await cmd.ExecuteNonQueryAsync();
        //        }
//        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
//        {
//            if (IsImported) return;

//            string sql = "";
//            int inoutFlag = -1;
//            if (red_flag == "2") inoutFlag *= -1;
//            foreach (var row in MergedSheetRows)
//            {
//                decimal qty = inoutFlag * row.quantity * row.unit_factor;
//                decimal newStock = row.FromStockQty + qty;
//                string s = $@"insert into stock_change_log 
//        (company_id, approve_time,    branch_id,       item_id,    sheet_id,     pre_stock_qty, new_stock_qty ) 
//values ({company_id},'{approve_time}',{from_branch_id},{row.item_id},{sheet_id},{row.FromStockQty},{newStock});";
//                sql += s;

//                qty *= -1;
//                newStock = row.ToStockQty + qty;
//                s = $@"insert into stock_change_log 
//        (company_id, approve_time,    branch_id,       item_id,    sheet_id,   pre_stock_qty, new_stock_qty ) 
//values ({company_id},'{approve_time}',{to_branch_id},{row.item_id},{sheet_id},{row.ToStockQty},{newStock});";

//                sql += s;
//            }
//            cmd.CommandText = sql;
//            await cmd.ExecuteNonQueryAsync();
//        }

        public override List<SheetRowCombineTemplate> MergeSheetRows(List<SheetRowCombineTemplate> rows, bool bIgnoreNativeQty = false)
        {
            Dictionary<string, SheetRowCombineTemplate> rowsDict = new Dictionary<string, SheetRowCombineTemplate>();
            foreach (SheetRowCombineTemplate sheetRow in rows)
            {

                string skey = sheetRow.item_id;// +"_" + sheetRow.unit_factor.ToString();
                SheetRowCombineTemplate curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = new SheetRowCombineTemplate();
                    curRow.item_id = sheetRow.item_id;
                    curRow.item_name = sheetRow.item_name;
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_no = sheetRow.unit_no;
                    curRow.unit_factor = sheetRow.quantity;
                    curRow.inout_flag = sheetRow.inout_flag;
                    curRow.StockQty = sheetRow.StockQty;
                    curRow.SellPendQty = sheetRow.SellPendQty;
                    rowsDict.Add(skey, curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                }
            }
            List<SheetRowCombineTemplate> newList = new List<SheetRowCombineTemplate>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }

        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting);
            decimal b_qty = 0, m_qty = 0, s_qty = 0;
            foreach (var row in SheetRows)
            {
                row.SetInfoForPrint(smallUnitBarcode);
                b_qty += row.b_quantity;
                m_qty += row.m_quantity;
                s_qty += row.s_quantity;
            }
            string sumQty = "";
            if (b_qty != 0) sumQty += CPubVars.FormatMoney(b_qty, 2) + "大";
            if (m_qty != 0) sumQty += CPubVars.FormatMoney(m_qty, 2) + "中";
            if (s_qty != 0) sumQty += CPubVars.FormatMoney(s_qty, 2) + "小";
            this.sum_quantity_unit_conv = sumQty;

        }


        

    }
}
