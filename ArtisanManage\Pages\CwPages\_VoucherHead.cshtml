﻿@model ArtisanManage.Models.SheetPartialViewModel
<link rel="stylesheet" href="~/css/DataForm.css?v=@Html.Raw(Model.Version)" type="text/css" />
<link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css?v=@Html.Raw(Model.Version)" type="text/css" />
<script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.js?v=@Html.Raw(Model.Version)"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.export.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxbuttons.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmyinput.js?v=@Html.Raw(Model.Version)"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxscrollbar.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmenu.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.js?v=@Html.Raw(Model.Version)"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmygrid.edit.js?v=@Html.Raw(Model.Version)"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.selection.js?v=@Html.Raw(Model.Version)"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.columnsresize.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.export.js"></script>

<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.pager.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdropdownlist.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.sort.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.aggregates.js"></script>

<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxlistbox.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxwindow.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdatetimeinput.js?v=@Html.Raw(Model.Version)"></script>

<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcalendar.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxtooltip.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcheckbox.js"></script>

<script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.culture.zh-CN.js"></script>

<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxnotification.js"></script>

<link rel="stylesheet" href="~/MiniJsLib/jquery.dialog.css?v=@Html.Raw(Model.Version)">
<script src="~/MiniJsLib/jquery.dialog.js?v=@Html.Raw(Model.Version)"></script>


<link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)">
<script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>

<style type="text/css">
    .row-oper {
        fill: #dddddd;
        cursor: pointer;
    }

        .row-oper:hover {
            fill: #777777;
        }

    .jqx-fill-state-hover {
        -moz-box-sizing: content-box;
        box-sizing: content-box;
        border-color: #999;
        background: #f0f9f0;
    }

    .jqx-grid-cell-hover a {
        display: block;
    }

    .jqx-grid-cell-pinned {
        background-color: #fbfbfb;
    }

    .rowBtn {
        display: none;
    }

    #cmbPayWay1, #cmbPayWay2, #cmbPayWay3 {
        right: 1px;
        left: auto;
        width: 70px;
    }

    .makeInfo {
        width: 430px;
    }

        .makeInfo > div {
            float: left;
            height: 18px;
            font-size: 12px;
            color: #555;
        }

            .makeInfo > div > div {
                float: left;
                height: 18px;
                position: relative;
            }

                .makeInfo > div > div:first-child {
                    text-align: right;
                    width: 50px;
                }

                .makeInfo > div > div:last-child {
                    text-align: left;
                    width: 160px;
                }

                .makeInfo > div > div > span {
                    bottom: 1px;
                    left: 1px;
                    top: auto;
                }

                .makeInfo > div > div > label {
                    bottom: 1px;
                    right: 1px;
                }

    #getOrder {
        color: blue;
        cursor: pointer;
    }

    #divButtons button {
        margin-right: 30px;
        margin-top: 20px;
    }

    #divTail {
        margin-top: 10px;
    }



    /*for print template chooser */
    .choosetemplate {
        display: none;
        position: absolute;
        top: 748px;
        left: 980px;
        width: 100px;
        height: 25px;
        text-align: center;
        font-size: 14px;
        overflow: hidden;
        border: #e2e2e2 1px solid;
    }

    #topCoverDiv {
        opacity: 0.4;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0px;
        background-color: #000;
        z-index: 100;
        text-align: center;
    }

    #dia {
        background: rgba(255,255,255,1);
        z-index: 200;
        position: absolute;
        top: 50%;
        left: 50%;
        width: 300px;
        height: 250px;
        margin: -200px 0 0 -250px;
        border-radius: 10px;
        border-top-width: 10px;
        padding: 10px 10px 10px 10px;
    }

    .show_close {
        height: 20px;
        width: 20px;
        margin-left: 480px;
        cursor: pointer;
        display: inline-block;
        float: right;
    }



    /*for assist accounting*/
    #assistEditorOutside{
        width: 380px;
        height: 180px;
        z-index: 1000 !important;
        position: absolute;
        overflow:visible; 
        background-color:#eeeeee; 
        border-radius:5px; 
        padding:1px 1px;
    }
    #assistMain{
        width:calc(100% - 20px); 
        height:calc(90% - 20px); 
        margin:10px;
    }
    #assistSel1, #assistSel2{
        width:100%;
        height:50px; 
        margin-top:20px;
    }
    .assistSpans{
        width:80px; 
        height:100%; 
        float:left;
    }
    .assistMust{
        color:red; 
        margin-right:3px;
    }
    .assistLabel{
        font-size: 12px;
    }
    #jqxdropdownlist1, #jqxdropdownlist2{
        display: inline-block;
    }


</style>
<script>
    //for print template chooser
    function choosetemplate() {
        var maskBg = document.getElementById('topCoverDiv');
        var dia = document.getElementById('dia');
        maskBg.style.display = (maskBg.style.display == 'none') ? 'block' : 'none';
        dia.style.display = (dia.style.display == 'none') ? 'block' : 'none';
    }

    function getPrintTemplate() {
        $('.choosetemplate').css('display', 'block');
        //maskBg.style.display('block');
    }

    function hidePrintTemplate() {
        $('.choosetemplate').css('display', 'none');
        //var maskBg = document.getElementsByClassName('choosetemplate');
        //maskBg.style.display('none');
    }</script>
<script>
    @Html.Raw(Model.m_saveCloseScript)
    var cwAssistSetting=[];
    var assister_data=[];
    var assister_rowIndex=null;

</script>

<partial name="_PageCustomHead" model="Model" />

<script type="text/javascript">
    @Html.Raw(Model.GetFormDataScript)  //this offer function getFormData()

    $(document).ready(function () {
        if (window.g_operRights.cwSheet) {
            if (window.g_operRights.cwSheet.sheetVoucher) {
                if (window.g_operRights.cwSheet.sheetVoucher.make) {
                    $('#btnSave').css('display', 'inline-block');
                }
                if (window.g_operRights.cwSheet.sheetVoucher.delete) {
                    $('#btnDelete').css('display', 'inline-block');
                }
                if (window.g_operRights.cwSheet.sheetVoucher.approve) {
                    $('#btnApprove').css('display', 'inline-block');
                    $('#btnCancelApprove').css('display', 'inline-block');
                }
                if (window.g_operRights.cwSheet.sheetVoucher.red) {
                    $('#btnRed').css('display', 'inline-block');
                }
                if (window.g_operRights.cwSheet.sheetVoucher.print) {
                        $('#btnPrint').css('display', 'inline-block');
                }
            }
        }
            
        var printTemplateChooser = $(`<div id="topCoverDiv" style="display: none;z-index:2000;"></div>
            <div id="dia" style="display: none;z-index:2001;">
                <div style="z-index: 19891015;">
                    <div style="background:#fff;text-align:center;" move="ok">

                        <img src="/PrintTemplate/img/close.svg" class="show_close" onclick="choosetemplate()" />
                        <span style="font-size: 15px; margin-top: 15px;">模板列表</span>

                    </div>
                    <div style="margin-left:20px;margin-top:10px;margin-bottom:10px;line-height:20px;display:flex;align-items:center;"> <input type="checkbox" id="ckPrintSmallBarcode" style="width:20px;height:20px;"/><label for="ckPrintSmallBarcode">打印小单位条码</label> </div>
                    <div id="template-list" style="text-align: left; padding: 5px; overflow: auto; height: 200px;">
                    </div>
                </div>
            </div>`)
        $('body').append(printTemplateChooser)

        @Html.Raw(Model.m_showFormScript)

        window.g_queriedItems =@Html.Raw(Model.ItemsInfoJson)

        onPageReady(@Html.Raw(Model.SheetRowsJson));


        //if( $('#payway1_id').length>0) window.g_initialPayway = $('#payway1_id').jqxInput('val')

        $("#btnClose").on('click', function () {
            window.parent.closeTab(window);
        });


        $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
            return false;
        });


        //获取辅助项下拉框数据
        $.ajax({
            url: `/api/AssistAccounting/GetDataForVoucher`,
            type: 'get',
            data: { operKey: g_operKey },
            contentType: "application/json;charset=UTF-8",
            dataType: 'json',
            async: false,
            error: function (xhr) {
                console.log("返回响应信息：Error " + xhr.responseText);
            }
        }).then(res => {
            console.log('get assister data in voucher', res);
            if (res.result === 'OK') {
                cwAssistSetting = res.cwAssistSetting;
                assister_data=res.data;
            } else {
                bw.toast(res.msg,2000);
            }
        });
        //辅助项筛选框隐藏
        $(document).click(function (e) {
            var target = $(e.target);
            if (target.closest("#assistEditorInside img").length == 0 && target.closest("#assistEditorOutside").length == 0 && target.parent("#assistEditorOutside").length == 0 && target.closest("#listBoxjqxdropdownlist1").length==0 && target.closest("#listBoxjqxdropdownlist2").length==0) {
                setAssisterValue();
            }else if(target.closest("#assistBtn").length){
                setAssisterValue();
                $('#jqxgrid').jqxGrid('endupdate');
                $('#jqxgrid').jqxGrid('refreshdata');
            }
        });


        function setAssisterValue() {
            if (assister_rowIndex != null) {
                let rowData = $("#jqxgrid").jqxGrid('getrowdata', assister_rowIndex);
                let sub = cwAssistSetting.find(item => item.sub_code == rowData.sub_code);
                if (sub) {
                    let type_item1 = null;
                    let type_item2 = null;
                    let types = sub.assister_types.split(',');
                    if (types.length == 1 && $("#jqxdropdownlist1").length == 1) {
                        type_item1 = $("#jqxdropdownlist1").jqxDropDownList('getSelectedItem');
                        if (type_item1 != null) {
                            $('#jqxgrid').jqxGrid('setcellvalue', assister_rowIndex, 'assister1_type', types[0]);
                            $('#jqxgrid').jqxGrid('setcellvalue', assister_rowIndex, 'assister1_id', type_item1.value);
                            let assister_types_names = `【${getAssisterTypeName(types[0])}】${type_item1.label}`;
                            $('#jqxgrid').jqxGrid('setcellvalue', assister_rowIndex, 'assister_types_names', { value: assister_types_names, label: assister_types_names });
                        }
                    } else if (types.length == 2 && $("#jqxdropdownlist1").length == 1 && $("#jqxdropdownlist2").length == 1) {
                        type_item1 = $("#jqxdropdownlist1").jqxDropDownList('getSelectedItem');
                        type_item2 = $("#jqxdropdownlist2").jqxDropDownList('getSelectedItem');
                        if (type_item1 != null && type_item2 != null) {
                            $('#jqxgrid').jqxGrid('setcellvalue', assister_rowIndex, 'assister1_type', types[0]);
                            $('#jqxgrid').jqxGrid('setcellvalue', assister_rowIndex, 'assister1_id', type_item1.value);
                            $('#jqxgrid').jqxGrid('setcellvalue', assister_rowIndex, 'assister2_type', types[1]);
                            $('#jqxgrid').jqxGrid('setcellvalue', assister_rowIndex, 'assister2_id', type_item2.value);
                            let assister_types_names = `【${getAssisterTypeName(types[0])}】${type_item1.label}_【${getAssisterTypeName(types[1])}】${type_item2.label}`;
                            $('#jqxgrid').jqxGrid('setcellvalue', assister_rowIndex, 'assister_types_names', { value: assister_types_names, label: assister_types_names });
                        }
                    }
                }
            }
            $("#assistEditorOutside").remove();
        }

        
        if (!String.prototype.endsWith) {
            String.prototype.endsWith = function(search, this_len) {
                if (!this_len) this_len = this.length;
                return this.substring(this_len - search.length, this_len) === search;
            };
        }
        //业务单据链接
        let bizSheetValue = $('#biz_id_nos').val();
        let bizSheetType = $('#business_sheet_type').val();
        $('#biz_id_nos').val('');
        $('#biz_id_nos').html('');
        if (bizSheetValue != '') {
            let bizSheetHtmlPart = '';
            let bizSheetHtmlAll = '';
            let index = 1;
            bizSheetValue.split(';').forEach(id_no => {
                let biz_id = id_no.split(',')[0];
                let biz_no = id_no.split(',')[1];
                if (index <= 12) {
                        bizSheetHtmlPart += `<span style="color:#0000ff;cursor:pointer;" onmouseup="voucherClickSheet(event,'${bizSheetType}','${biz_id}','${biz_no}')">${biz_no}</span><span>,</span>`;
                    if (index % 3 == 0) {
                        bizSheetHtmlPart += '<br>';
                    }
                }
                    bizSheetHtmlAll += `<span style="color:#0000ff;cursor:pointer;"  onmouseup="voucherClickSheet(event,'${bizSheetType}','${biz_id}','${biz_no}')">${biz_no}</span><span>,</span>`;
                if (index % 3 == 0) {
                    bizSheetHtmlAll += '<br>';
                }
                index++;
            });
            if (bizSheetHtmlPart.endsWith('<span>,</span>')) {
                bizSheetHtmlPart = bizSheetHtmlPart.substring(0, bizSheetHtmlPart.length - 14);
            }
            else if (bizSheetHtmlPart.endsWith('<span>,</span><br>')) {
                bizSheetHtmlPart = bizSheetHtmlPart.substring(0, bizSheetHtmlPart.length - 18);
            }
            if (bizSheetHtmlAll.endsWith('<span>,</span>')) {
                bizSheetHtmlAll = bizSheetHtmlAll.substring(0, bizSheetHtmlAll.length - 14);
            }
            else if (bizSheetHtmlAll.endsWith('<span>,</span><br>')) {
                bizSheetHtmlAll = bizSheetHtmlAll.substring(0, bizSheetHtmlAll.length - 18);
            }
            if (index > 12) {
                $('#jqxNoti_allSheets').append(bizSheetHtmlAll);
                $("#jqxNoti_allSheets").jqxNotification({ width: "450", height: "auto", position: "top-right", opacity: 0.9, autoOpen: false, autoClose: false, template: null, theme: "summer", animationOpenDelay: "fast", animationCloseDelay: "fast" });
                bizSheetHtmlPart += `<a id="biz_id_nos_all" onclick="voShowAllSheets()" style="cursor:pointer;margin-left:5px;">...</a>`;
                $('#biz_id_nos_all').css('display', 'inline-block');
            }
            $('#biz_id_nos').after(bizSheetHtmlPart);
            $('#bizSheets').css('display', 'inline-block');
        }
        

    });

    function getAssisterTypeName(type_id){
        switch(type_id){
            case 'C':
                return '客户';
            case 'S':
                return '供应商';
            case 'INV':
                return '商品';
            case 'DEP':
                return '部门';
            case 'MAN':
                return '业务员';
            default:
                return '';
        }
    }

    function voShowAllSheets(){
        $("#jqxNoti_allSheets").jqxNotification("open");
    }

    function onRowRemove(rowIndex) {
        console.log('row removed')
        var id = $("#jqxgrid").jqxGrid('getrowid', rowIndex);
        $("#jqxgrid").jqxGrid('deleterow', id);
        window.operRowID = -1;
    }
    function onRowAdd(rowIndex) {
        var row = {};
        GridData.columns.forEach(function (col) {
            if (col.datafield) row[col.datafield] = "";
            if (col.displayfield) row[col.displayfield] = "";
        })
        $("#jqxgrid").jqxGrid('addrow', null, row,rowIndex);
    }

    function cellhover(cellhtmlElement, x, y,rowIndex) {
        if (cellhtmlElement) {
            if (cellhtmlElement.className.indexOf('pinned') >= 0) {
                var approve_time = $('#approve_time').text()
                if (approve_time) return
                var displayRows = $('#jqxgrid').jqxGrid('getvisiblerows');
                    var allRows = $('#jqxgrid').jqxGrid('getrows');
                var arr = $('.row_operator')
                //  if (arr.length > 0)
                //      console.log('arr:', arr.length)
                //  if (arr.length > 1) debugger;
                for (var i = 0; i < arr.length; i++) {

                    var row_operator=arr[i]
                    if (row_operator.parentNode) {
                        // if (row_operator.parentNode.rowIndex != cellhtmlElement.rowIndex || row_operator.parentNode.rowIndex == undefined) {
                        var row = row_operator.parentNode.parentNode
                        var id = row.id
                        id = id.replace('row', '')
                        id = id.replace('jqxgrid', '')
                        var curRow = displayRows[id]
                        var showIndex=-1
                        for (var j = 0; j < allRows.length; j++) {
                            var r=allRows[j]

                            if (r ===curRow) {
                                showIndex=j
                            }
                        }

                        if (showIndex != window.operRowID) {
                             //  console.log('设置了setPinCell:', showIndex)
                            var html = "<div style='height:100%;display:flex; justify-content:center;align-items:center;'>" + (showIndex + 1) + "</div>";

                            row_operator.parentNode.innerHTML = html;// row_operator.parentNode.normalInnerHTML;
                        }
                        //}
                    }
                }

                /*
                if (document.all.row_operator && document.all.row_operator.parentNode) {
                    if (document.all.row_operator.parentNode.rowIndex != cellhtmlElement.rowIndex || document.all.row_operator.parentNode.rowIndex == undefined) {
                        document.all.row_operator.parentNode.innerHTML = document.all.row_operator.parentNode.normalInnerHTML;
                    }
                }*/
                debugger
                if (cellhtmlElement.innerHTML.indexOf('row_operator') == -1) {
                    if (!(window.g_dicNotDeleteRows && window.g_dicNotDeleteRows[rowIndex])) {
                        var pinText = $(cellhtmlElement).text()
                        //   console.log('pinText:',pinText)
                        //   var rowIndex = parseInt(pinText) - 1;
                        //   console.log('cellhtmlElement.innerHTML:' + new Date().toUTCString(), cellhtmlElement.innerHTML)
                        //   cellhtmlElement.normalInnerHTML = cellhtmlElement.innerHTML;
                        //   cellhtmlElement.rowIndex = rowIndex;

                        // window.g_hoveredRow
                        cellhtmlElement.innerHTML = `<div class="row_operator" style="height:100%;width:100%;display:flex;justify-content:space-around;align-items:center;">
                            <svg onclick="onRowAdd(${rowIndex})" class="row-oper" height="15" width="15" style="">
                                <use xlink:href="/images/images.svg#add" />
                            </svg>
                            <svg onclick="onRowRemove(${rowIndex})" class="row-oper" height="15" width="15" style="">
                                <use xlink:href="/images/images.svg#remove" />
                            </svg>
                        </div>`;
                        window.operHTML = cellhtmlElement.innerHTML;
                        window.operRowID = rowIndex;
                        //console.log('new rowIndex:',rowIndex)
                    }
                      
                }
            }
        }
    }

        
        
    function cellsrenderer_quantity (row, columnfield, value, defaulthtml, columnproperties) {
        // if (value < 20) {
        //     return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + '; color: #0000ff;">' + value + '</span>';
        // }
        // else {
            return '<span style="margin: 4px; float: ' + columnproperties.cellsalign + ';color:#000;">' + value + '</span>';
        // }
    }

         
    var aggregates_quantity = [{
        'qty':
        function (aggregatedValue, currentValue, column, record) {
            var unit_no = record.unit_no;
            var qty = record.quantity;
            currentValue = parseFloat(toMoney(currentValue));
            if (!currentValue) return aggregatedValue;
            if (qty && unit_no && window.g_queriedItems && record.item_id) {
                if (!aggregatedValue) aggregatedValue = "";
                var unitType = '', unitTypeLabel = '小';
                var item = window.g_queriedItems[record.item_id]
                if (item) {
                    item.units.forEach(function (unit) {
                        if (unit.unit_no == unit_no) unitType = unit.unit_type;
                    })
                }
                if (unitType == 'm') unitTypeLabel = '中';
                else if (unitType == 'b') unitTypeLabel = '大';
                var n = aggregatedValue.indexOf(unitTypeLabel);
                if (n > 0) {
                    var unitQty = 0;

                    for (i = n - 1; i >= 0; i--) {
                        var tmp = parseFloat(aggregatedValue.substring(i, n));
                        if (tmp.toString() != "NaN") {
                            unitQty = tmp;
                        }
                        else break;
                    }
                        
                    aggregatedValue = aggregatedValue.replace(unitQty + unitTypeLabel, toMoney(unitQty + currentValue) + unitTypeLabel)
                }
                else {
                    aggregatedValue = aggregatedValue + toMoney(currentValue).toString() + unitTypeLabel;
                }
            }
            return aggregatedValue;
        }
    }];
          
    function createeditor_sub_name(row, cellvalue, editor, cellText, width, height) {
        var element = $('<div id="txtItemName"></div>');
        editor.append(element);

        var inputElement = editor.find('div')[0];

        var dataFields = new Array( 
            {datafield: "code", text: "科目编码", width: 120 } ,
            {datafield: "name", text: "科目名称", width:180},
            { datafield: "direction_label", text: "借贷方向", width: 80 }
        )

        let res=[];
        $(inputElement).jqxInput({
            placeHolder: "助记码/名称/编号", height: height, width: width,
            borderShape: "none",
            buttonUsage: 'event',
            showHeader: true,
            dropDownHeight: 160,
            displayMember: "name",
            valueMember: "id",
            dataFields: dataFields,
            searchFields: ["zjm", "name", "code","item_no"],
            maxRecords: 9,
            source: function (query, response) {
                $.ajax({
                    url: '/api/CwVoucher/GetSubs',
                    type: 'GET',
                    contentType: 'application/json',
                    data: { operKey: g_operKey, query: query },
                    success: function (data) {
                        if (data.result === 'OK') {
                            response(data.records);     
                            res=data.records;
                        }
                    }
                });
            },
            renderer: function (itemValue, inputValue) {
                // debugger;
                var terms = inputValue.split(/,\s*/);
                // remove the current input
                terms.pop();
                // add the selected item
                terms.push(itemValue);
                // add placeholder to get the comma-and-space at the end
                // terms.push("");
                //var value = terms.join(", ");
                //return terms;

                //set借贷cell的值
                //let cell = $('#jqxgrid').jqxGrid('getselectedcell');
                //let record=res.filter(row=>row.sub_id==cell.value);
                //$('#jqxgrid').jqxGrid('setcellvalue', cell.rowindex, "direction", { value: record.direction, label: record.direction=='1'?'借':'贷' });

                return itemValue;
            }, onButtonClick: function () {
                window.curRowIndex = row;
                $('#popSub').jqxWindow('open');
                $("#popSub").jqxWindow('setContent', `<iframe src="/setting/PaywaysView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
            }

        });

        $(inputElement).on('optionSelected',function (a, b) {
             
            var value = $(inputElement).val();
            var id = '';
            console.log('value:' + JSON.stringify(value));
            if (value.id)
                id = value.id;
            var name = '';
            if (value.name)
                name = value.name;
                
            let cell = $('#jqxgrid').jqxGrid('getselectedcell');
            if (window.setRowOnItemSelected) {
                window.setRowOnItemSelected(cell.rowindex,value)
            } 

            //为行内sub_code列赋值（用于辅助核算）
            let rowIndex = $("#jqxgrid").jqxGrid('getselectedcell').rowindex;
            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'sub_code', value.code);
            if(cwAssistSetting.map(a=>a.sub_code).indexOf(value.code)<=-1){
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister_types_names', '无');
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister1_type', '');
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister1_id', '');
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister2_type', '');
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister2_id', '');
            }else{
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister_types_names', null);
            }
            var editable = $("#jqxgrid").jqxGrid('endcelledit', rowIndex, "sub_id", false);
             $('#jqxgrid').jqxGrid('updategrid');
        })

        $('#txtItemName').on('change', function (event) {
            //let column = $('#jqxGrid').jqxGrid('getcolumn', 'columndatafield');
            if ((event.args.value==null || event.args.value=='') && (event.args.label==null || event.args.label=='')){
                console.log('sub_id clear');
                let rowIndex = $("#jqxgrid").jqxGrid('getselectedcell').rowindex; 
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'sub_id', null);
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'sub_name', null);
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'sub_code', null);
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'direction', null);
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'direction_label', null);
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister_types_names', null);
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister1_type', null);
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister1_id', null);
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister2_type', null);
                $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister2_id', null);
            }

        })
    }

    function initeditor_sub_name(row, cellvalue, editor, celltext, pressedkey) {
        var inputField = editor.find('input');
        if (pressedkey) {
            inputField.val(pressedkey);
            inputField.jqxInput('selectLast');
        }
        else {
            inputField.val({ value: cellvalue, label: celltext });
            inputField[0].value = celltext||'';
            inputField.jqxInput('selectAll');
        }
    }

    function createeditor_assist_name(row, cellvalue, editor, cellText, width, height) {
        let rowIndex = $("#jqxgrid").jqxGrid('getselectedcell').rowindex;
        let rowData = $("#jqxgrid").jqxGrid('getrowdata', rowIndex);
        let elementInput = $(`<div id="assistEditorInside"></div>`);
        editor.append(elementInput);
        let inputElement=editor.find('div')[0];
        $(inputElement).jqxInput({
            placeHolder: "", height: height, width: width,
            borderShape: "none",
            buttonUsage: 'event',
            dropDownWidth: 0,
            dropDownHeight: 0,
            valueMember: "id",
            searchMode:'none',
            onButtonClick: function () {
                let rowIndex = $("#jqxgrid").jqxGrid('getselectedcell').rowindex;
                let rowData = $("#jqxgrid").jqxGrid('getrowdata', rowIndex);
                if (rowData.sub_id == "") {
                    bw.toast('请先选择主科目', 1500);
                    return;
                } else if (cwAssistSetting.find(item=>item.sub_code==rowData.sub_code)==null) {
                    bw.toast('该科目未设置辅助核算', 1500);
                    return;
                }
                if($("#assistEditorOutside").length){
                    return;
                }

                let top1=$('#divTitle')[0].offsetHeight+$('#divTitle')[0].offsetTop;
                let top2=$('#divHead')[0].offsetHeight;
                let top3=$('#columntablejqxgrid')[0].offsetHeight;
                let top4 = $('#templateeditorjqxgridassister_types_names')[0].offsetTop+$("#jqxgrid").jqxGrid('rowsheight');
                let left1=$('#jqxgrid')[0].offsetLeft;
                let left2=$('.jqx-grid-column-header')[0].offsetWidth;
                let left3=$('.jqx-grid-column-header')[1].offsetWidth;
                let left4=$('.jqx-grid-column-header')[3].offsetWidth;
                var elementForm = $(`
                <div id="assistEditorOutside" style="top:${top1+top2+top3+top4+1}px; left:${left1+left2+left3+left4}px; ">
                    <div id='assistMain'></div>
                    <button id='assistBtn' style="width:60px;position:absolute;right:15px;bottom:15px;cursor:pointer;">确定</button>
                </div>
                `);
                $('body').append(elementForm);
                
                //var source = ["Affogato","Americano","Bicerin","Breve","Café Bombón","Café au lait","Caffé Corretto","Café Crema","Caffé Latte", ];
                let sub=cwAssistSetting.find(item=>item.sub_code==rowData.sub_code);
                let source1=[];
                let source2=[];
                let type_ids=sub.assister_types.split(',');
                let type_names=sub.assister_names.split(',');
                if(type_ids.length==1){
                    let elementSel=$(`
                        <div id='assistSel1'>
                            <div class='assistSpans'><span class='assistMust'>*</span><span class='assistLabel'>${type_names[0]}：</span></div>
                            <div id='jqxdropdownlist1'></div>
                        </div>
                    `);
                    $('#assistMain').append(elementSel);
                    source1 = assisterFindTypeData(type_ids[0]);
                    $("#jqxdropdownlist1").jqxDropDownList({ source: source1, selectedIndex: 0, width: '200px', height: '25px', displayMember: "name", valueMember: "id", placeHolder: "请选择", filterable: true, searchMode: 'contains', filterPlaceHolder: '按名称查询' });
                    $("#jqxdropdownlist1").jqxDropDownList('clearSelection');
                }else if(type_ids.length==2){
                    let elementSel = $(`
                            <div id='assistSel1'>
                                <div class='assistSpans'><span class='assistMust'>*</span><span class='assistLabel'>${type_names[0]}：</span></div>
                                <div id='jqxdropdownlist1'></div>
                            </div>
                            <div id='assistSel2'>
                                <div class='assistSpans'><span class='assistMust'>*</span><span class='assistLabel'>${type_names[1]}：</span></div>
                                <div id='jqxdropdownlist2'></div>
                            </div>
                        `);
                    $('#assistMain').append(elementSel);
                    //第一个辅助项
                    source1 = assisterFindTypeData(type_ids[0]);
                    $("#jqxdropdownlist1").jqxDropDownList({ source: source1, selectedIndex: 0, width: '200px', height: '25px', displayMember: "name", valueMember: "id", placeHolder: "请选择", filterable: true, searchMode: 'contains', filterPlaceHolder: '按名称查询' });
                    $("#jqxdropdownlist1").jqxDropDownList('clearSelection');
                    //第二个辅助项
                    source2 = assisterFindTypeData(type_ids[1]);
                    $("#jqxdropdownlist2").jqxDropDownList({ source: source2, selectedIndex: 0, width: '200px', height: '25px', displayMember: "name", valueMember: "id", placeHolder: "请选择", filterable: true, searchMode: 'contains', filterPlaceHolder: '按名称查询' });
                    $("#jqxdropdownlist2").jqxDropDownList('clearSelection');
                }
                assister_rowIndex=rowIndex;
                let assister_types_names=$('#jqxgrid').jqxGrid('getcellvalue', assister_rowIndex, 'assister_types_names');
                if(assister_types_names!=''){
                    let assister1_id = $('#jqxgrid').jqxGrid('getcellvalue', assister_rowIndex, 'assister1_id');
                    let item1 = $("#jqxdropdownlist1").jqxDropDownList('getItemByValue', assister1_id);
                    $("#jqxdropdownlist1").jqxDropDownList('selectItem', item1);
                    let assister2_id = $('#jqxgrid').jqxGrid('getcellvalue', assister_rowIndex, 'assister2_id');
                    if($("#jqxdropdownlist2").length){
                        let item2 = $("#jqxdropdownlist2").jqxDropDownList('getItemByValue', assister2_id);
                        $("#jqxdropdownlist2").jqxDropDownList('selectItem', item2);
                    }
                }
            }
        });

        $('#assistEditorInside').on('change', function (event) {
            console.log('assister_types_names clear');
            let rowIndex = $("#jqxgrid").jqxGrid('getselectedcell').rowindex;
            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister_types_names', null);
            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister1_type', null);
            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister1_id', null);
            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister2_type', null);
            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, 'assister2_id', null);
            //if(event.args.value==null || event.args.value==''){
                
            //}
            
        })
    }

    function assisterFindTypeData(type_id){
        switch(type_id){
            case 'C':
                return assister_data['data_c'];
            case 'S':
                return assister_data['data_s'];
            case 'INV':
                return assister_data['data_inv'];
            case 'DEP':
                return assister_data['data_dep'];
            case 'MAN':
                return assister_data['data_man'];
            default:
                return [];
        }
    }

    function initeditor_assist_name(row, cellvalue, editor, celltext, pressedkey) {
        var inputField = editor.find('input');
        inputField.val({ value: cellvalue, label: celltext });
        inputField[0].value = celltext||'';
        inputField.jqxInput('selectAll');
        
        //if (pressedkey) {
        //    inputField1.val(pressedkey);
        //}
        //else {
        //    inputField1.value=111;
        //    inputField1.label=222;
        //}
    }

    function assisterRender(row, columnfield, value, defaulthtml, columnproperties){
        if(value=='无'){
            return `<div class="jqx-grid-cell-left-align" style="margin-top: 12px; font-size:10px; white-space: pre-wrap; text-align: center;">${value}</div>`;
        }
        return  `<div class="jqx-grid-cell-left-align" style="margin-top: 8px; font-size:10px; white-space: pre-wrap;">${value}</div>`;
    }

    

    //let sourceList=["123","12"];
    //function createeditor_remark(row, cellvalue, editor, cellText, width, height) {
    //    if(cellvalue!="") sourceList.push(cellvalue);
    //    editor.jqxListBox({ 
    //        source: sourceList
    //    });
    //}

        
        window.onresize = function () {
            //return;
            var h1 = document.all.divTitle.offsetHeight;
           // var h2 = document.all.divHead.offsetHeight;// $("#divHead").height();
             var h2 =   $("#divHead").height();

            var h3 = $("#divTail").height();
            var h4 = document.all.divButtons.offsetHeight;
            //var h5 = $(window).height();
            var h6 = window.innerHeight;
            var windowWidth = window.innerWidth;
            //var h7 = document.documentElement.clientHeight;
            
            var h = h6 - h1 - h2 - h3 - h4 - 5;

            var rowsheight = $('#jqxgrid').jqxGrid('rowsheight');
            var headerheight = $('#jqxgrid').jqxGrid('columnsheight');
            var statusbarheight = $('#jqxgrid').jqxGrid('statusbarheight');
            var max_rows = Math.floor((h - headerheight - statusbarheight) / rowsheight);
            var now_rows = $('#jqxgrid').jqxGrid('getrows').length;
            var rowsAreaHeight=max_rows * rowsheight
            h = rowsAreaHeight + headerheight + statusbarheight + 2 - 50;

            $("#jqxgrid").jqxGrid('verticalscrollbarlargestep',rowsAreaHeight-20)

            $("#jqxgrid").jqxGrid(
                {
                    height: h,
                    width: windowWidth - 20
                });
            var fill_rows = max_rows - now_rows - 1;
            if (fill_rows > 0) {
                var rows = new Array(); var i;
                for (i = 0; i < fill_rows; i++) {
                    var row = {};
                    GridData.columns.forEach(function (col) {
                        if (col.datafield) row[col.datafield] = "";
                        if (col.displayfield) row[col.displayfield] = "";
                    })
                    rows.push(row);
                }
                $('#jqxgrid').jqxGrid('addrow', null, rows);
            }

        };

    function loadSheetRows(sheetRows) {
        if (sheetRows) {
            var rowCount = 20;
            $('#jqxgrid').jqxGrid('clear');
            for (var i = 0, sheetRowslength = sheetRows.length; i < sheetRowslength; i++) {
                var row=sheetRows[i]
                if (row.orig_price)
                    row.discount = (row.real_price / row.orig_price * 100).toFixed(0)
                if (row.orig_price == 0 && row.real_price == 0) row.discount = 100
                //if (window.g_companySetting.sheetShowBarcodeStyle == '2') {
                //    row.barcode=row.s_barcode
                //}
                //else if (window.g_companySetting.sheetShowBarcodeStyle == '1') {
                //    if (row.unit_factor == 1)
                //        row.barcode = row.s_barcode
                //    else
                //        row.barcode = row.b_barcode
                //}

            }
            for (var i = sheetRows.length; i < rowCount - sheetRows.length; i++) {
                var row = {};
                GridData.columns.forEach(function (col) {
                    if (col.datafield) row[col.datafield] = "";
                    if (col.displayfield) row[col.displayfield] = "";
                })
                sheetRows[i] = row;
            }
            $('#jqxgrid').jqxGrid('addrow', null, sheetRows);
        }

        var red_flag = $('#red_flag').val();
        var sheet_id = $('#sheet_id').val();
        var approve_time = $('#approve_time').text();
        if (sheet_id == null || sheet_id == "") state = VoucherState.NewVo;
        else if (approve_time != null && approve_time != "" && (red_flag == null || red_flag == "")) state = VoucherState.Approved;
        else if (red_flag == "1") state = VoucherState.Red1;
        else if (red_flag == "2") state = VoucherState.Red2;
        else state = VoucherState.Saved;
        updateSheetState(state);
    }

    var VoucherState={
        NewVo:Symbol('newvo'),
        Saved:Symbol('saved'),
        Approved:Symbol('approved'),
        Red1:Symbol('red_orig'),
        Red2:Symbol('red_new')
    };

    function updateSheetState(state) {
        switch (state) {
            case VoucherState.NewVo:
                $('#btnSave').attr('disabled', false);
                $('#btnApprove').attr('disabled', false);
                $('#btnCancelApprove').attr('disabled', true);
                $('#btnRed').attr('disabled', true);
                $('#btnDelete').attr('disabled', true);
                $('#btnPrint').attr('disabled', true);
                $('#choosetemplate').attr('disabled', true);
                $('#imgState').css('display', 'none');
                $('#lblSheetTitle').css('color', 'black');
                $('#lblSheetTitle').text('会计凭证');
                setPageReadonly(false);
                break;
            case VoucherState.Saved:
                console.log("saved触发")
                $('#btnSave').attr('disabled', false);
                $('#btnApprove').attr('disabled', false);
                $('#btnCancelApprove').attr('disabled', true);
                $('#btnRed').attr('disabled', true);
                $('#btnDelete').attr('disabled', false);
                $('#btnPrint').attr('disabled', true);
                $('#choosetemplate').attr('disabled', true);
                $('#imgState').css('display', 'none');
                setPageReadonly(false);
                break;
            case VoucherState.Approved:
                $('#btnSave').attr('disabled', true);
                $('#btnApprove').attr('disabled', true);
                $('#btnCancelApprove').attr('disabled', false);
                $('#btnRed').attr('disabled', false);
                $('#btnDelete').attr('disabled', true);
                $('#btnPrint').attr('disabled', false);
                $('#choosetemplate').attr('disabled', false);
                $('#imgState').css('display', 'inline-block');
                $('#imgState').attr('src', '/images/approved.ico');
                setPageReadonly(true)
                break;
            case VoucherState.Red1:
                $('#btnSave').attr('disabled', true);
                $('#btnApprove').attr('disabled', true);
                $('#btnCancelApprove').attr('disabled', true);
                $('#btnRed').attr('disabled', true);
                $('#btnDelete').attr('disabled', true);
                $('#btnPrint').attr('disabled', true);
                $('#choosetemplate').attr('disabled', true);
                $('#btnDelete').attr('disabled', true);
                $('#imgState').css('display', 'inline-block');
                $('#imgState').attr('src', '/images/reded.ico');
                setPageReadonly(true)
                break;
            case VoucherState.Red2:
                $('#btnSave').attr('disabled', true);
                $('#btnApprove').attr('disabled', true);
                $('#btnCancelApprove').attr('disabled', true);
                $('#btnRed').attr('disabled', true);
                $('#btnDelete').attr('disabled', true);
                $('#btnPrint').attr('disabled', true);
                $('#choosetemplate').attr('disabled', true);
                $('#btnDelete').attr('disabled', true);
                $("#btnCopy").attr('disabled', true);
                $('#imgState').css('display', 'inline-block');
                $('#imgState').attr('src', '/images/reded.ico');
                $('#lblSheetTitle').css('color', '#f00');
                $('#lblSheetTitle').css('padding-left', '70px');
                var title = $('#lblSheetTitle').text();
                $('#lblSheetTitle').text(title + '(红字单)');
                setPageReadonly(true)
                break;
            default:
                break;
        }
    };

    //复制到其他单据
    function copyToSheets(e) {
        var arr = [];
        var checkeds = $('input[name="copyToSheets"]:checked')

        for (var i = 0; i < checkeds.length; i++) {
            var checked = checkeds[i]
            var row = {}
            row.id = checkeds[i].id;
            row.url = checkeds[i].value;
            row.title = document.querySelector(`label[for="${row.id}"]`).innerHTML;
            arr.push(row)
        }
        return arr;
    }

    function requestString(name)
    {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = location.search.substr(1).match(reg);
        return r ? decodeURI(r[2]) : null;
    }

    function voucherClickSheet(e, biz_sheet_type, biz_sheet_id, biz_sheet_no) {
        e.stopPropagation();
        let sheet_name = '';
        let url = '';
        switch (biz_sheet_type) {
            case 'X':
                sheet_name = '销售单';
                url = `Sheets/SaleSheet?sheet_id=${biz_sheet_id}`;
                break;
            case 'D':
                sheet_name = '销售单';
                url = `Sheets/SaleSheet?sheet_id=${biz_sheet_id}`;
                break;
            case 'T':
                sheet_name = '退货单';
                url = `Sheets/SaleSheet?forReturn=true&sheet_id=${biz_sheet_id}`;
                break;
            case 'CG':
                sheet_name = '采购单';
                url = `Sheets/BuySheet?sheet_id=${biz_sheet_id}`;
                break;
            case 'CT':
                sheet_name = '采购退货单';
                url = `Sheets/BuySheet?forReturn=true&sheet_id=${biz_sheet_id}`;
                break;
            case 'SK':
                sheet_name = '收款单';
                url = `Sheets/GetArrearsSheet?forPayOrGet=false&sheet_id=${biz_sheet_id}`;
                break;
            case 'FK':
                sheet_name = '付款单';
                url = `Sheets/GetArrearsSheet?forPayOrGet=true&sheet_id=${biz_sheet_id}`;
                break;
            case 'YS':
                sheet_name = '预收款单';
                url = `Sheets/PrepaySheet?forPayOrGet=false&sheet_id=${biz_sheet_id}`;
                break;
            case 'YF':
                sheet_name = '预付款单';
                url = `Sheets/PrepaySheet?forPayOrGet=true&sheet_id=${biz_sheet_id}`;
                break;
            case 'ZC':
                sheet_name = '费用支出单';
                url = `Sheets/FeeOutSheet?forOutOrIn=true&sheet_id=${biz_sheet_id}`;
                break;
            case 'SR':
                sheet_name = '其他收入单';
                url = `Sheets/FeeOutSheet?forOutOrIn=false&sheet_id=${biz_sheet_id}`;
                break;
            case 'YK':
                sheet_name = '盘点盈亏单';
                url = `Sheets/InventChangeSheet?forReduce=false&sheet_id=${biz_sheet_id}`;
                break;
            case 'BS':
                sheet_name = '报损单';
                url = `Sheets/InventChangeSheet?forReduce=true&sheet_id=${biz_sheet_id}`;
                break;
            case 'DH':
                sheet_name = '定货会';
                url = `Sheets/OrderItemSheet?sheet_id=${biz_sheet_id}`;
                break;
            case 'DHTZ':
                sheet_name = '定货会调整单';
                url = `Sheets/OrderItemAdjustSheet?sheet_id=${biz_sheet_id}`;
                break;
            case 'CBTJ':
                sheet_name = '成本调价单';
                url = `Sheets/CostPriceAdjustSheet?sheet_id=${biz_sheet_id}`;
                break;
            case 'TR':
                sheet_name = '转账单';
                url = `CwPages/CashBankTransferSheet?sheet_id=${biz_sheet_id}`;
                break;
            case 'RK':
                sheet_name = '其他入库单';
                url = `Sheets/StockInOutSheet?sheet_id=${biz_sheet_id}`;
                break;
            case 'CK':
                sheet_name = '其他出库单';
                url = `Sheets/StockInOutSheet?sheet_id=${biz_sheet_id}`;
                break;
            case 'FYFT':
                sheet_name = '采购费用分摊单';
                url = `Sheets/FeeApportionSheet?sheet_id=${biz_sheet_id}`;
                break;
            case 'DK':
                sheet_name = '贷款单';
                url = `Sheets/LoanSheet?sheet_id=${biz_sheet_id}`;
                break;
            case 'DK':
                sheet_name = '还贷款单';
                url = `Sheets/RepaySheet?sheet_id=${biz_sheet_id}`;
                break;
        }
        window.parent.newTabPage(sheet_name, url, window);
    }
</script>
