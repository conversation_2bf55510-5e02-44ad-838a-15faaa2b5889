using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class RoleEditModel : PageFormModel
    {
       
        public string role_id;
        public string role_name;
        public string remark;
        public string rights;
        public string store;
        public string rightsTemplate;
        public string templ_id = "";
        

        public RoleEditModel(CMySbCommand cmd,string company_id= "",string oper_id="") : base(Services.MenuId.infoRole)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            //operKey = Security.MyUrlEncrypt(company_id);
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"role_id",new DataItem(){Title="编号",/*CtrlType="hidden",*/}},
                {"role_name",new DataItem(){Title="角色名称",Necessary=true}},
                {"remark",new DataItem(){Title="角色描述"}},
                {"templ_id",new DataItem(){Title="模板",/*CtrlType="hidden",*/}},
                {"rights",new DataItem(){Title=""}},
                {"templ_rights",new DataItem(){Title="模板",SaveToDB=false}},
                
            };

            m_idFld = "role_id"; m_nameFld = "role_name";
            m_tableName = "info_role";
            m_selectFromSQL = "from info_role left join info_role_template t on info_role.templ_id=t.templ_id and t.company_id=info_role.company_id where info_role.company_id=~COMPANY_ID and role_id='~ID'";
        }

        public async Task OnGet(string operKey,string role_id,string templ_id,string role_name)
        {
            OperKey = operKey;
            Token.TryParse(operKey, out Token token);
            this.role_id = role_id;
            await InitGet(cmd);
            Rights rights_model = Rights.Create(out Dictionary<MenuId, Store> stores);
            templ_id = DataItems["templ_id"].Value;
            Rights rights_templ = null;

            void FillRightModelWithRights(Rights rights_model, Rights rights)
            {

                foreach (var item in rights_model)
                {
                    if (rights.TryGetValue(item.Key, out UnitRights unitRights))
                    {
                        for (int i = 0; i < item.Value.Count; i++)
                        {
                            var u = item.Value.ElementAt(i);
                            if (unitRights.TryGetValue(u.Key, out BaseRights baseRights))
                            {
                                if (u.Key.ToString() == "sheetViewRange")
                                {

                                }
                                foreach (var k in baseRights)
                                {
                                    if (item.Value[u.Key].ContainsKey(k.Key))
                                    {
                                        item.Value[u.Key][k.Key] = baseRights[k.Key];
                                    }
                                }
                            }
                        }
                    }
                }
            }


            if  ( templ_id != "")
            {
                
                 string sql = @$"
select coalesce(t.templ_id,g.templ_id) templ_id,coalesce(t.templ_name,g.templ_name) templ_name,coalesce(t.templ_rights,g.templ_rights) templ_rights,coalesce(t.fee_discount,g.fee_discount) fee_discount
from 
(
    select * from g_role_template t, g_company c where t.business_type = c.business_id and c.company_id={Token.CompanyID}
) g
full join
(
  select * from info_role_template where company_id={Token.CompanyID}
) t on g.templ_id=t.templ_id where coalesce(g.templ_id,t.templ_id)={templ_id};";

                dynamic data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (data != null)
                {
                    string fee_discount = data.fee_discount;
                    float n_fee_discount = 1;
                    if (fee_discount != "") n_fee_discount = Convert.ToSingle(fee_discount);
                    
                    string templ_rights = data.templ_rights;
                    JObject j_templ_rights = JsonConvert.DeserializeObject<JObject>(templ_rights);
                        
                    bool isRightEmpty(JToken right)
                    {
                        if (right.GetType()==typeof(JValue))
                        {
                            JValue jv = (JValue) right;
                            string v = jv.Value.ToString();
                            if (v == "" || v.ToLower() == "false")
                            {
                                return true;
                            }
                            else return false;
                        }
                        else
                        {
                            int notEmptyCount = 0;
                            JObject jo = (JObject)right;
                            List<JProperty> lstRemove = new List<JProperty>();
                            foreach (var prop in jo.Properties())
                            {  
                                if (!isRightEmpty(prop.Value))
                                {
                                    notEmptyCount++;
                                }
                                else
                                {
                                    lstRemove.Add(prop);
                                }
                            }
                            foreach (var r in lstRemove)
                            {
                                jo.Remove(r.Name);
                            }
                            if (notEmptyCount == 0)
                            {                                   
                                return true;
                            }
                        }                                                     
                        return false;                            
                    }
                    isRightEmpty(j_templ_rights);
                    rights_templ = Rights.FromJson(j_templ_rights);
                    if (n_fee_discount < 1)
                    {
                        rights_model = Rights.FromJson(j_templ_rights);
                    }
                    else
                    {
                        FillRightModelWithRights(rights_model, rights_templ);
                    }                   
                }
            }
           
        
            store = JsonConvert.SerializeObject(stores);
            rightsTemplate = JsonConvert.SerializeObject(rights_model);
            if (role_id != null && role_id != "")
            {               
                var sql = $"select role_name,remark,rights from info_role where company_id = {token.CompanyID} and role_id = {role_id}";
                dynamic data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                role_name =(string)data.role_name;
                remark = (string)data.remark;
                if (data.rights != "") {
                    string s = data.rights;
                    // Rights rights_old = JsonConvert.DeserializeObject<Rights>(s);
                    JObject j = JsonConvert.DeserializeObject<JObject>(s);
                    Rights rights_old = Rights.FromJson(j);
                    FillRightModelWithRights(rights_model,rights_old);
                   /* foreach (var item in rights_model)
                    {
                        if (rights_old.TryGetValue(item.Key,out UnitRights unitRights))
                        {
                            for (int i = 0; i < item.Value.Count; i++)
                            {
                                var u = item.Value.ElementAt(i);
                                if (unitRights.TryGetValue(u.Key, out BaseRights baseRights))
                                {
                                    //item.Value[u.Key] = baseRights;
                                    if (u.Key.ToString() == "sheetViewRange")
                                    { 

                                     }
                                    foreach(var k in baseRights)
                                    {
                                        if (item.Value[u.Key].ContainsKey(k.Key))
                                        {
                                            item.Value[u.Key][k.Key] = baseRights[k.Key];
                                        } 
                                    }
                                }
                            }
                        }
                    }*/

                }
            }
            rights = JsonConvert.SerializeObject(rights_model);
        }



    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class RoleEditController : BaseController
    {  

        public RoleEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            RoleEditModel model = new RoleEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            RoleEditModel model = new RoleEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic data)
        { 
            var role_id = (string)data.role_id;
           // var operKey = (string)data.operKey;
            var templ_id = (string)data.templ_id;
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID);

            object info_role = null;
            try
            {
                info_role = data.rights.info.infoRole.see;
            }
            catch (Exception ex)
            {
                info_role = null;
            }
            if (info_role != null)
            {
                if (!Convert.ToBoolean(info_role))
                {
                    dynamic admin_role = await CDbDealer.Get1RecordFromSQLAsync($@"
                        select io.oper_id,go.is_admin,io.role_id from info_operator io
                        left join g_operator go on go.company_id={companyID} and go.oper_id=io.oper_id
                        where io.company_id={companyID} and go.is_admin", cmd);
                    if(admin_role != null && role_id == admin_role.role_id.ToString() )
                    {
                        return Json(new { result = "Error", msg = "管理员请勿移除员工角色权限" });
                    }
                }
            }

            RoleEditModel model = new RoleEditModel(cmd);
            JsonResult jres = await model.SaveTable(cmd, data);
            dynamic res = jres.Value;
            if(res.result!="OK")
            {
                return jres;
            }

          //  CDbDealer db = new CDbDealer();
          //  db.AddFields(data, "role_name,remark,rights,templ_id");
          //  db.AddField("company_id", companyID);
            string sql;



            if (templ_id != "")
            {
                sql = $"select * from info_role_template where company_id={Token.CompanyID} and templ_id={templ_id}";
                dynamic record =await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (record == null)
                {
                    cmd.CommandText = @$"insert into info_role_template
                             (company_id,                  templ_id,          templ_name,templ_rights,fee_discount)
                      select {Token.CompanyID} company_id,{templ_id} templ_id,templ_name,templ_rights,fee_discount from g_role_template where templ_id={templ_id};
";
                    await cmd.ExecuteNonQueryAsync();
                }
                
            }


            //if (role_id == null || role_id == "")
            //     sql = db.GetInsertSQL("info_role") + " returning role_id";
            // else
            //     sql = db.GetUpdateSQL("info_role",$"role_id = {role_id}");

            // cmd.CommandText = sql;
            // string new_role_id = "";
            //  object ov = await cmd.ExecuteScalarAsync();         
            //  if(ov!=null && ov != DBNull.Value)
            //  {
            //      new_role_id = ov.ToString();
            //  }

            return jres;// new JsonResult(new { result = "OK", msg = "" , role_id, new_role_id });
        }
    }
}