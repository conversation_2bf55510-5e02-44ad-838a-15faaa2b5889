using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NPOI.SS.Formula.Functions;

namespace ArtisanManage.Pages.BaseInfo
{
    public class StocksViewModel : PageQueryModel
    {
        public StocksViewModel(CMySbCommand cmd) : base(MenuId.viewStock)
        {
            this.UsePostMethod = true;
            this.cmd = cmd;
            this.PageTitle = "库存表";
            this.NotQueryHideColumn = true;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){FldArea="divHead", Title="开始时间",allowNullDate=true, CtrlType="jqxDateTimeInput",SqlFld="produce_date",PlaceHolder="生产日期",CompareOperator=">=",ShowTime=false,ForQuery=false,Hidden=true}},
                {"endDay",new DataItem(){FldArea="divHead", Title="结束时间",allowNullDate=true,CtrlType="jqxDateTimeInput",SqlFld="produce_date",PlaceHolder="生产日期",CompareOperator="<",ShowTime=false,ForQuery=false,Hidden=true}},
                {"batch_no",new DataItem(){FldArea="divHead",Title="批次",Checkboxes=true,ButtonUsage="list",CompareOperator="=",HideOnLoad=false,SqlForOptions=CommonTool.selectBatchNo,ForQuery=false,Hidden=true}},
                {"batch_type",new DataItem(){FldArea="divHead",Title="产期类型",ButtonUsage="list",CompareOperator="=",HideOnLoad=false,Hidden=true,
                Source= @"[{v:'y',l:'有产期',condition:""tem.batch_id <> 0""},
                               {v:'n',l:'无产期',condition:""tem.batch_id=0 ""}]"}}, 
				{"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){SqlFld="item_brand"})},
                /*{"item_class",new DataItem(){Title="类别",FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",MumSelectable=true,CompareOperator="=",SqlFld="item_class",
                   SqlForOptions="select class_id as v,class_name as l,py_str as z,mother_id as pv from info_item_class"}},
                */
                {"branch_id",new DataItem(){FldArea="divHead", Title="仓库",Checkboxes=true,SqlFld="branch_id", LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",HideOnLoad=false,ForQuery=false,
                    SqlForOptions=CommonTool.selectBranch}},

                 {"branch_position",new DataItem(){FldArea="divHead",Restrict="query_stock", SqlFld="branch_position",LabelFld="branch_position_name",Title="库位",Checkboxes=true, ButtonUsage="list",CompareOperator="=",HideOnLoad=false,ForQuery=false,
                    SqlForOptions=CommonTool.selectBranchPosition,Hidden=true}},
                {"item_id",new DataItem(){FldArea="divHead",Title="商品名称",SqlFld="item_id",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,Pinned=true,
                SearchFields=CommonTool.itemSearchFields,DropDownWidth ="250",
                    SqlForOptions = CommonTool.selectItemWithBarcode}},
                {"barcode",new DataItem(){FldArea="divHead",Title="条码",SqlFld="s_barcode,b_barcode,m_barcode",CompareOperator="like",Hidden=true
                 }},

                 {"showZeroQty",new DataItem(){FldArea="divHead",Title="显示零库存",CtrlType="jqxCheckBox",ForQuery=false,Hidden=true,
                }},
                 {"showMultiBranch",new DataItem(){FldArea="divHead",Title="分仓展示",CtrlType="jqxCheckBox",ForQuery=false ,Hidden=false,Value="true",AutoRemember=true,
                }},
                  {"showMultiBatch",new DataItem(){FldArea="divHead",Title="分批次展示",CtrlType="jqxCheckBox",ForQuery=false ,Hidden=false,Value="true",AutoRemember=true,
                }},
                {"status",new DataItem(){FldArea="divHead",Title = "状态",LabelFld = "cls_status_name", LabelInDB = false, Value = "all", Label = "所有",ButtonUsage = "list", QueryOnChange = false, Hidden=true, CompareOperator = "=", NullEqualValue = "all",
                     Source = @"[{v:'normal',l:'正常',condition:""(status = '1' or status is null)""},
                               {v:'stop',l:'停用',condition:""status = '0' ""},
                               {v:'all',l:'所有',condition:""true""}]"
                 }},
                {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'2',l:'加权平均价'},{v:'3',l:'预设进价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价',c:'0'}]", CompareOperator="="
                }},
                {"other_class", new DataItem(){Title = "父类", LikeWrapper = "/", CtrlType = "jqxTree", MumSelectable = true,GetOptionsOnLoad = true, QueryOnChange = true, CompareOperator = "like",
                 Value="often",   MaxRecords = "400",SqlForOptions = CommonTool.selectClasses}},
                //{"other_class",new DataItem(){Title="商品品类",LabelFld="other_class_name", LikeWrapper="/", CtrlType="jqxTree",MumSelectable=true,GetOptionsOnLoad=true,CompareOperator="like",QueryOnChange=true,
                //   SqlForOptions="select class_id as v,class_name as l,py_str as z,mother_id as pv from info_item_class"} }
                //{"status",new DataItem(){Title="状态",LabelFld="cls_status_name",SqlFld="status",Value="1",Label="正常", ButtonUsage="list",CompareOperator="=",
                //    Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}},

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,
                     JSBeforeCreate=@"
var gridHeight= $('#gridItems').height()
if(gridHeight==0) { 
    var headHeight=$('#divHead').height()
    var bodyHeight=$('body').height()
    gridHeight=bodyHeight-headHeight-50
    $('#gridItems').height(gridHeight)
}",                  RowsHeight=30, // 设置行高和表头高避免表格缩放时显示不全
                     ColumnsHeight=45,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"item_id", new DataItem(){Title="商品",  Width="150",Hidden=true, HideOnLoad=true,SelectFieldEvenHidden=true}},
                       {"item_name",new DataItem(){Title="商品名称",     Sortable=true, Width="300",Linkable=true,Pinned=true,JSCellRender="itemNameRender" }},
					   {"item_spec",new DataItem(){Title="规格",     Sortable=true, Width="60",Hidden=true }},

						 {"branch_id",new DataItem(){Title="仓库id", Width="90",GetFromDb=true,Hidden=true, HideOnLoad=true }},
                       {"branch_name",new DataItem(){Title="仓库", Width="90",GetFromDb=true,Hidden=true}},
                       {"branch_position_name",new DataItem(){Title="库位", Width="90",GetFromDb=true,Hidden=true,Sortable=true,AutoRemember=true}},
                       {"branch_position",new DataItem(){  Title="库位id", Width="90",GetFromDb=true,Hidden=true,  Sortable=true, AutoRemember=true, HideOnLoad=true}},
                       {"produce_date",new DataItem(){Title="生产日期", Width="150",GetFromDb=true,Hidden=true ,AutoRemember=true,JSCellRender="viewProduceDate",Sortable=true}},
                       {"valid_days_num",new DataItem(){Title="保质期数字",SqlFld="valid_days", Hidden = true,HideOnLoad=true, Width="150"}},
                       {"valid_days_type",new DataItem(){Title="保质期类型",SqlFld="valid_day_type",  Hidden = true,HideOnLoad=true, Width="150"}},
                       {"valid_days_left",new DataItem(){Title="剩余保质期", Width="150",Sortable=false,
                          RelyColumns="valid_days_num,produce_date,valid_days,valid_days_type",
                            FuncGetValueFromRowData=(colName,row)=>{
                             string s="";
                             if (row["valid_days_num"] != "" && row["produce_date"] != "")
                             {
                                 string produceDate=row["produce_date"];
                                 if(!CPubVars.IsDate(produceDate)) return "";
                                 DateTime produce=DateTime.Parse(produceDate);
                                 string valid_days=row["valid_days"];
                                 if (row["valid_days_type"] == "y")
                                 {
                                     s=Convert.ToInt32((produce.AddYears(Convert.ToInt32(row["valid_days_num"]))-DateTime.Now).TotalDays).ToString()+'天';
                                 }
                                 else if (row["valid_days_type"] == "m")
                                 {
                                     s=Convert.ToInt32((produce.AddMonths(Convert.ToInt32(row["valid_days_num"]))-DateTime.Now).TotalDays).ToString()+'天';
                                 }
                                 else
                                 {
                                     s=Convert.ToInt32((produce.AddDays(Convert.ToInt32(row["valid_days_num"]))-DateTime.Now).TotalDays).ToString()+'天';
                                 }
                             }
                             return s;
                         }}},

                         {"valid_days",new DataItem(){Title="保质期",SqlFld="valid_days||case valid_day_type when 'm' then '月' when 'y' then '年' else '天' end", Hidden = true,Width="150"}},

                       {"batch_no",new DataItem(){Title="批次", Width="150",GetFromDb=true,Hidden=true ,AutoRemember=true,JSCellRender="viewBatchNo"}},
                       {"batch_id",new DataItem(){Title="批次编号", Width="150",GetFromDb=true,Hidden=true, AutoRemember=true}},
                       {"item_no", new DataItem(){Title="商品编号",  Width="150", Hidden = true,Sortable=true}},
                       {"brand_name",new DataItem(){Title="品牌", Width="100", Hidden = true,Sortable=true}},
                       {"category",new DataItem(){Title="类别",GetFromDb=false, Width="150", Hidden = true,Sortable=true,SortFld="class_name",
                           RelyColumns="class_name,class2_name,class3_name",
                           FuncGetValueFromRowData=(colName,row)=>
                           {
                               string s = "",q="";
                               q = row["class3_name"]; if (q != "" && q != "全部") {
                                   s += q;
                               }
                               q = row["class2_name"]; if (q != "" && q != "全部"){
                                  if(s!="") s+="/"; s += q;
                               }
                               q = row["class_name"]; if (q != ""){
                                  if(s!="") s+="/"; s += q;
                               }
                               return s;
                           }}
                        },
                       {"class_name",new DataItem(){Hidden=true,HideOnLoad=true}},
                       {"class2_name",new DataItem(){Hidden=true,HideOnLoad=true}},
                       {"class3_name",new DataItem(){Hidden=true,HideOnLoad=true}},
                       {"unit_conv",new DataItem(){Title="单位换算",ButtonUsage="list", CellsAlign="center",Width="200",
                           RelyColumns="b_unit,b_factor,m_unit,m_factor,s_unit",
                           SqlFld = @"yj_get_unit_relation(b_unit,b_factor,m_unit,m_factor,s_unit)"}},
                       {"b_qty",new DataItem(){Title="大库存",CellsAlign="right",Hidden=true,ShowSum=true ,Width="150",HideOnLoad = true} },
                       {"m_qty",new DataItem(){Title="中库存",CellsAlign="right",Hidden=true,ShowSum=true,Width="150",HideOnLoad = true} },
                       {"s_qty",new DataItem(){Title="小库存",CellsAlign="right",Hidden=true,ShowSum=true,Width="150",HideOnLoad = true} },
                       {"avail_qty_s",new DataItem(){Title="可用库存(小单位)",SqlFld="avail_qty_s", CellsAlign="right",Hidden=true,ShowSum=true,Width="150",HideOnLoad = false } },


                       {"avail_b_qty",new DataItem(){Title="可用大库存",CellsAlign="right",Hidden=true,ShowSum=true,Width="150",HideOnLoad = true } },
                       {"avail_m_qty",new DataItem(){Title="可用中库存",CellsAlign="right",Hidden=true,ShowSum=true,Width="150",HideOnLoad = true} },
                       {"avail_s_qty",new DataItem(){Title="可用小库存",CellsAlign="right",Hidden=true,ShowSum=true,Width="150",HideOnLoad = true} },

                       {"sell_pend_b_qty",new DataItem(){Title="占用大库存",CellsAlign="right",Hidden=true,ShowSum=true ,Width="150",HideOnLoad = true} },
                       {"sell_pend_m_qty",new DataItem(){Title="占用中库存",CellsAlign="right",Hidden=true,ShowSum=true,Width="150",HideOnLoad = true} },
                       {"sell_pend_s_qty",new DataItem(){Title="占用小库存",CellsAlign="right",Hidden=true,ShowSum=true,Width="150",HideOnLoad = true} },
                       {"s_recent_move_in_price",new DataItem(){Title="最近调入价(小)",Width="100",Hidden=true, CellsAlign="right"} },
                       {"m_recent_move_in_price",new DataItem(){Title="最近调入价(中)",Width="100",Hidden=true, CellsAlign="right"} },
                       {"b_recent_move_in_price",new DataItem(){Title="最近调入价(大)",Width="100",Hidden=true, CellsAlign="right"} },
                       //{"wholesale_price_total",new DataItem(){Title="批发金额(小)",Width="200",CellsAlign="right",SqlFld = "s_wholesale_price::numeric*stock_qty",ShowSum=true} },
                       {"recent_move_in_amount",new DataItem(){Title="最近调入金额",Width="200",Hidden=true, CellsAlign="right",SqlFld = "round(COALESCE(s_recent_move_in_price,0)*COALESCE(stock_qty,0)::numeric,2) ",ShowSum=true} },

                       {"s_quantity",new DataItem(){Title="库存数(小)",CellsAlign="right",Hidden=true,SqlFld="s_stock_qty" ,ShowSum=true,Width="150",HideOnLoad = false} },
                       {"s_unit",new DataItem(){Title="小单位",Width="50",CellsAlign="right",Hidden=true,SelectFieldEvenHidden=true} },
                       {"b_quantity",new DataItem(){Title="库存数(大)",CellsAlign="right",Hidden=true,SqlFld="b_stock_qty" ,ShowSum=true,Width="150",HideOnLoad = false} },
                       {"b_unit",new DataItem(){Title="大单位",Width="100",CellsAlign="right",Hidden=true} },
                       {"m_quantity",new DataItem(){Title="库存数(中)",CellsAlign="right",Hidden=true,SqlFld="m_stock_qty" ,ShowSum=true,Width="150",HideOnLoad = false} },
                       {"m_unit",new DataItem(){Title="中单位",Width="100",CellsAlign="right",Hidden=true} },

                       {"b_quantity_unit",new DataItem(){Title="库存(大)",CellsAlign="right",Width="150",Hidden=true, SqlFld="concat(round(b_stock_qty,2),b_unit)",Sortable=true, SortFld=" COALESCE(b_stock_qty,0)   ",
                           RelyColumns="b_quantity", 
						   FuncGetSumValue = (sumColumnValues) =>
                            {
                                return sumColumnValues["b_quantity"]+"大";
                            }
                       } },
                       {"m_quantity_unit",new DataItem(){Title="库存(中)",CellsAlign="right",Width="150",Hidden=true,SqlFld="concat(round(m_stock_qty,2),m_unit)",Sortable=true, SortFld=" COALESCE(m_stock_qty,0)   ",
							RelyColumns="m_quantity", 
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               return sumColumnValues["m_quantity"]+"中";
                           }
                       } },
                       {"s_quantity_unit",new DataItem(){Title="库存(小)",CellsAlign="right",Width="150",Hidden=true,SqlFld="concat(round(s_stock_qty,2),s_unit)",FuncDealMe=value=>{ return value=="0"?"":value; },Sortable=true, SortFld=" COALESCE(s_stock_qty,0)   ",
						   RelyColumns="s_quantity,s_unit",
						   FuncGetSumValue = (sumColumnValues) =>
                            {
                                return sumColumnValues["s_quantity"]+"小";
                            }
                       }},
                       {"avail_qty",new DataItem(){Title="可用库存",CellsAlign="right",Width="100",
                          // SqlFld = "1",
                           Sortable=true,
                           SortFld=" COALESCE(avail_b_qty,0)   ",
                           RelyColumns="avail_b_qty,avail_m_qty,avail_s_qty,b_unit,m_unit,s_unit",
                           FuncGetValueFromRowData = (colName,row) =>
                           {
                               string qty = "",q="";
                               q = row["avail_b_qty"]; if (q != ""&& q !="0") qty += q + row["b_unit"];
                               q = row["avail_m_qty"]; if (q != ""&& q !="0") qty += q + row["m_unit"];
                               q = row["avail_s_qty"]; if (q != ""&& q !="0") qty += q + row["s_unit"];
                               return qty;
                           },
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string qty = "",q="";
                               q=sumColumnValues["avail_b_qty"]; if(q!="" && q!="0") qty+=q+"大";
                               q=sumColumnValues["avail_m_qty"]; if(q!="" && q!="0") qty+=q+"中";
                               q=sumColumnValues["avail_s_qty"]; if(q!="" && q!="0") qty+=q+"小";
                               return qty;
                           }


                       }},
                       {"sell_pend_qty",new DataItem(){Title="占用库存",CellsAlign="right",Linkable=true,Width="100",
                           SqlFld = "1",
                            Sortable=true,
                           SortFld="sell_pend_b_qty",
                           RelyColumns="sell_pend_b_qty,sell_pend_m_qty,sell_pend_s_qty,b_unit,m_unit,s_unit",
                           FuncGetValueFromRowData = (colName,row) =>
                           {
                               string qty = "",q="";
                               q = row["sell_pend_b_qty"]; if (q != "" && q !="0") qty += q + row["b_unit"];
                               q = row["sell_pend_m_qty"]; if (q != "" && q !="0") qty += q + row["m_unit"];
                               q = row["sell_pend_s_qty"]; if (q != "" && q !="0") qty += q + row["s_unit"];
                               return qty;
                           },
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string qty = "",q="";
                               q=sumColumnValues["sell_pend_b_qty"]; if(q!="" && q!="0") qty+=q+"大";
                               q=sumColumnValues["sell_pend_m_qty"]; if(q!="" && q!="0") qty+=q+"中";
                               q=sumColumnValues["sell_pend_s_qty"]; if(q!="" && q!="0") qty+=q+"小";
                               return qty;
                           }

                       }},
                       {"stock_qty",new DataItem(){Title="实际库存",CellsAlign="right",Width="100",
                       Sortable=true,
                       SortFld="s_stock_qty",
                       RelyColumns="b_qty,m_qty,s_qty,b_unit,m_unit,s_unit",
                           //SqlFld = "1",
                           FuncGetValueFromRowData = (colName,row) =>
                           {
                               string qty = "",q="";
                               q = row["b_qty"]; if (q != ""&& q !="0") qty += q + row["b_unit"];
                               q = row["m_qty"]; if (q != ""&& q !="0") qty += q + row["m_unit"];
                               q = row["s_qty"]; if (q != ""&& q !="0") qty += q + row["s_unit"];
                               return qty;
                           },

                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string qty = "",q="";
                               q = sumColumnValues["b_qty"]; if(q!="" && q!="0") qty+=q+"大";
                               q = sumColumnValues["m_qty"]; if(q!="" && q!="0") qty+=q+"中";
                               q = sumColumnValues["s_qty"]; if(q!="" && q!="0") qty+=q+"小";
                               return qty;
                           }
                       }},
                       //{"batch_total",new DataItem(){Title="产期明细",CellsAlign="right",Width="200",JSCellRender="viewBatch"}},
                       {"s_wholesale_price",new DataItem(){Title="批发价(小)",Width="100",CellsAlign="right"} },
                       {"b_wholesale_price",new DataItem(){Title="批发价(大)",Width="100",CellsAlign="right"} },
                       //{"wholesale_price_total",new DataItem(){Title="批发金额(小)",Width="200",CellsAlign="right",SqlFld = "s_wholesale_price::numeric*stock_qty",ShowSum=true} },
                       {"b_wholesale_price_total",new DataItem(){Title="批发金额",Width="200",CellsAlign="right",SqlFld = "round((case when b_wholesale_price is not null and stock_qty is not null and stock_qty <> 0  then b_wholesale_price::numeric *( stock_qty/b_factor ) else (COALESCE(s_wholesale_price::numeric,0)*COALESCE(stock_qty,0))  end),2) ",ShowSum=true} },



                       {"s_barcode",new DataItem(){Title="条码", CellsAlign="right",Width="200"} },
                       {"avail_cost_amt",new DataItem(){Title="可用库存成本",CellsAlign="right",Width="200",SqlFld="round(avail_qty_s*s_buy_price,2)",ShowSum=true,Sortable=true } },
                       {"cost_amt",new DataItem(){Title="成本",CellsAlign="right",Width="200",SqlFld="round(stock_qty*s_buy_price,2)",ProfitRelated=true, ShowSum=true,Sortable=true } },
                       {"item_order_index", new DataItem(){Title="商品顺序号", CellsAlign="right", Width="150",Sortable=true,Hidden=true, SqlFld="item_order_index"}},
                       {"buy_price",new DataItem(){ProfitRelated=true,Title="成本单价", CellsAlign="right",Width="200",Sortable=true} },
                       {"s_cost_price",new DataItem(){ProfitRelated=true,Title="成本(小)", CellsAlign="right",Width="200"} },
                       {"b_cost_price",new DataItem(){ProfitRelated=true,Title="成本(大)", CellsAlign="right",Width="200"} },
                       {"weight", new DataItem(){Title="重量(kg)", Hidden=true, CellsAlign="right", Width="200",ShowSum=true } },
                       {"ton", new DataItem(){Title="吨位", Hidden=true,CellsAlign="right", Width="200",ShowSum=true } },
                        {"status",new DataItem(){Title="状态", CellsAlign="right",Width="200",Hidden=true,HideOnLoad=true} },
                     },
                       QueryFromSQL=@"
from 
(
    SELECT ip.*,stock_qty,stock.avail_qty as avail_qty_s, c.order_index,brand_name,class_name,class2_name,class3_name,
        (case when t.b is not null then sign(stock.stock_qty)*round(COALESCE(abs(stock.stock_qty),0) / (t.b->> 'f1')::numeric,2) else null end ) as b_stock_qty,
        (case when t.m is not null then sign(stock.stock_qty)*round(COALESCE(abs(stock.stock_qty),0) / (t.m->> 'f1')::numeric,2) else null end ) as m_stock_qty,
        stock.stock_qty  as s_stock_qty,
     
        yj_get_unit_qty ('b',stock.stock_qty,b_factor,m_factor,false) b_qty,
        yj_get_unit_qty ('m',stock.stock_qty,b_factor,m_factor,false) m_qty,
        yj_get_unit_qty ('s',stock.stock_qty,b_factor,m_factor,false) s_qty,

        yj_get_unit_qty ('b',stock.avail_qty,b_factor,m_factor,false) avail_b_qty,
        yj_get_unit_qty ('m',stock.avail_qty,b_factor,m_factor,false) avail_m_qty,
        yj_get_unit_qty ('s',stock.avail_qty,b_factor,m_factor,false) avail_s_qty,
												
        yj_get_unit_qty ('b',stock.sell_pend_qty,b_factor,m_factor,false) sell_pend_b_qty,
        yj_get_unit_qty ('m',stock.sell_pend_qty,b_factor,m_factor,false) sell_pend_m_qty,
        yj_get_unit_qty ('s',stock.sell_pend_qty,b_factor,m_factor,false) sell_pend_s_qty,

        b_unit,s_unit,m_unit,b_factor,m_factor, 
        s_wholesale_price,m_wholesale_price,b_wholesale_price, m_recent_move_in_price, b_recent_move_in_price, s_recent_move_in_price,s_buy_price,s_weight * stock.stock_qty as weight,round(s_weight * stock.stock_qty/1000,3) as ton,
        s_barcode,m_barcode,b_barcode ~VAR_analyis_date_batch ~VAR_branch_name_fld
        
    FROM info_item_prop as ip
    LEFT JOIN
    (
        select item_id,s,m,b,
               s->>'f2' s_unit,                               s->>'f3' s_wholesale_price, s->>'f4' slPrice, s->>'f5' s_barcode, (s->>'f6')::numeric as s_buy_price,(s->>'f7')::numeric as s_weight,(s->>'f8')::numeric s_recent_move_in_price,
               m->>'f2' m_unit, (m->>'f1')::numeric m_factor, m->>'f3' m_wholesale_price, m->>'f4' mlPrice, m->>'f5' m_barcode,                                                                    (m->>'f8')::numeric m_recent_move_in_price,
               b->>'f2' b_unit, (b->>'f1')::numeric b_factor, b->>'f3' b_wholesale_price, b->>'f4' slPrice, b->>'f5' b_barcode,                                                                    (b->>'f8')::numeric b_recent_move_in_price
                
        from crosstab('select iimu.item_id,iimu.unit_type,row_to_json(row(iimu.unit_factor,iimu.unit_no,wholesale_price,retail_price,barcode,buy_price,weight, recent_price)) as json
        from info_item_multi_unit iimu
        left join(select item_id, unit_no, recent_price from client_recent_prices where company_id= ~COMPANY_ID ~VAR_relate_client) crp on iimu.item_id = crp.item_id and iimu.unit_no = crp.unit_no
        where iimu.company_id= ~COMPANY_ID ORDER BY iimu.item_id',$$values('s'::text),('m'::text),('b'::text)$$) 
        as errr(item_id int, s jsonb,m jsonb, b jsonb)
    ) t
    on ip.item_id = t.item_id
    left join 
    (
      ~VAR_stock_sql
    ) stock on stock.item_id = ip.item_id 
    left join
    (
      select brand_id,brand_name from info_item_brand where company_id = ~COMPANY_ID
    ) b on ip.item_brand = brand_id
    LEFT JOIN    
    (
        SELECT ic1.class_id,ic1.order_index,ic1.class_name, class2_name,class3_name
        FROM info_item_class ic1
	    LEFT JOIN
        (
	        SELECT class_id, class_name class2_name,mother_id FROM info_item_class WHERE class_id <>- 1 and company_id=~COMPANY_ID 
        ) ic2 on ic1.mother_id=ic2.class_id
        LEFT JOIN
        (
	        SELECT class_id, class_name class3_name FROM info_item_class WHERE class_id <>- 1 and company_id=~COMPANY_ID 
        ) ic3 on ic2.mother_id=ic3.class_id
        WHERE ic1.company_id = ~COMPANY_ID 
    ) c ON ip.item_class = c.class_id
    where ip.company_id= ~COMPANY_ID ~VAR_condi_show_0_stock

) tem where tem.company_id= ~COMPANY_ID",


                      QueryOrderSQL="order by item_name,order_index,class_name,item_order_index ~VAR_branch_name_fld"
                  }
                }
            };
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            string branch_id = DataItems["branch_id"].Value;
           /* 这里不要隐式的使用权限，会导致售后，如果要限制仓库权限，就从前端看到限制条件。
            if (string.IsNullOrWhiteSpace(branch_id))
            {
                string sql = $"select obr.*,ib.branch_name from oper_branch_rights obr left join info_branch ib on ib.branch_id = obr.branch_id and ib.company_id = obr.company_id where oper_id={OperID} and obr.company_id={company_id}";
                var rec = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                var hasQueryRightBranchs = rec.Where(branch => ((dynamic)branch).query_stock == "True").Select(branch => ((dynamic)branch).branch_id);
                branch_id = string.Join(",", hasQueryRightBranchs);
            }*/

            string branch_positions = DataItems["branch_position"].Label;
            string batch_nos = DataItems["batch_no"].Value;

            var branchs = "";
            if (branch_id.EndsWith(","))
            {
                branch_id = branch_id.Substring(0, branch_id.Length - 1);
            }
            var branchPositions = "";
            var batchs = "";
            string CHECKED_CONDI = "";

            SQLVariables["relate_client"] = " and supcust_id =-2 ";
            if (branch_id != "")
            {

                branchs = $" and s.branch_id in ({branch_id})";
                dynamic res_sup = await CDbDealer.Get1RecordFromSQLAsync($"SELECT STRING_AGG(relate_client::TEXT, ',') AS relate_client_list FROM info_branch WHERE company_id ={company_id} and branch_id in ({branch_id});", cmd);
                if(res_sup.relate_client_list != "" && res_sup.relate_client_list != null)
                {
                    SQLVariables["relate_client"] = $" and supcust_id in ({res_sup.relate_client_list})";
                }
            }

            if (branch_positions != "")
            {
                var branchPositionList = branch_positions.Split(',');
                foreach (var branchPosition in branchPositionList)
                {
                    if (branchPositions == "")
                    {
                        if (branchPosition == "默认库位")
                        {
                            branchPositions += $@" and (s.branch_position = 0";
                            continue;
                        }
                        branchPositions = $@" and (branch_position_name ='{branchPosition}'";
                    }
                    else
                    {
                        if (branchPosition == "默认库位")
                        {
                            branchPositions += $@" or s.branch_position = 0";
                            continue;
                        }
                        branchPositions += $@" or branch_position_name ='{branchPosition}'";
                    }
                }
                if (branchPositions != "") branchPositions += ")";

            }
            if (batch_nos != "")
            {
                var batchNoList = batch_nos.Split(',');
                foreach (var batchID in batchNoList)
                {
                    if (batchs == "")
                    {
                        batchs = " and batch_no in ('" + batchID + "'";
                    }
                    else
                    {
                        batchs += ",'" + batchID + "'";
                    }
                }
                batchs += ")";

            }
            SQLVariables["CHECKED_CONDI"] = $@"{branchs} {branchPositions} {batchs}";
            CHECKED_CONDI += $@"{branchs} {branchPositions} {batchs}";
            string startDay = DataItems["startDay"].Value;
            if (startDay != "")
            {
                SQLVariables["CHECKED_CONDI"] += @$" and produce_date >= '{startDay}' ";
                CHECKED_CONDI += @$" and produce_date >= '{startDay}' ";
            }
            string endDay = DataItems["endDay"].Value;
            if (endDay != "")
            {
                SQLVariables["CHECKED_CONDI"] += @$" and produce_date <= '{endDay}' ";
                CHECKED_CONDI += @$" and produce_date <= '{endDay}' ";
            }

            DataItems["startDay"].ForQuery = false;
            DataItems["endDay"].ForQuery = false;
            string showZeroQty = DataItems["showZeroQty"].Value;
            this.SQLVariables["condi_show_0_stock"] = " and stock.stock_qty<>0";
            this.SQLVariables["condi_show_0_s"] = " and s.stock_qty<>0";
            if (showZeroQty.ToLower() == "true")
            {
                this.SQLVariables["condi_show_0_stock"] = "";
                this.SQLVariables["condi_show_0_s"] = "";
            }
            string showMultiBranch = DataItems["showMultiBranch"].Value;
            string showMultiBatch = DataItems["showMultiBatch"].Value;
            this.SQLVariables["branch_name_fld"] = "";
            this.SQLVariables["analyis_date_batch"] = ",stock.produce_date as produce_date, stock.produce_date -> 0 ->> 'batch_no'as batch_no,(stock.produce_date -> 0 ->> 'batch_id')::INTEGER as batch_id";

            string stockSql = "";
            if (showMultiBranch.ToLower() == "true" && showMultiBatch.ToLower() == "true")
            {
                //this.SQLVariables["branch_name_fld"] = ",branch_name,branch_id,branch_position,branch_position_name,batch_no,produce_date,batch_id";
                this.SQLVariables["branch_name_fld"] = ",branch_name,branch_id,branch_position,branch_position_name,batch_id";
                Grids["gridItems"].Columns["branch_name"].GetFromDb = true;
                Grids["gridItems"].Columns["branch_id"].GetFromDb = true;
                Grids["gridItems"].Columns["branch_position_name"].GetFromDb = true;
                Grids["gridItems"].Columns["branch_position"].GetFromDb = true;
                //Grids["gridItems"].Columns["batch_no"].GetFromDb = true;
                //Grids["gridItems"].Columns["produce_date"].GetFromDb = true;
                Grids["gridItems"].Columns["batch_id"].GetFromDb = true;

                Grids["gridItems"].Columns["branch_name"].Hidden = false;
                Grids["gridItems"].Columns["branch_position_name"].Hidden = false;
                //Grids["gridItems"].Columns["batch_no"].Hidden = false;
                //Grids["gridItems"].Columns["produce_date"].Hidden = false;
                Grids["gridItems"].Columns["batch_id"].Hidden = false;
                this.SQLVariables["analyis_date_batch"] = ",produce_date,batch_no";
                //Grids["gridItems"].Columns["batch_total"].Hidden = true;
                stockSql = $@" 
SELECT branch_name, s.branch_id,s.branch_position,branch_position_name,s.item_id, stock_qty,stock_qty-COALESCE(sell_pend_qty,0) avail_qty ,COALESCE(sell_pend_qty,0) sell_pend_qty,COALESCE(batch_no,'') batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) produce_date,s.batch_id
FROM stock s 
LEFT JOIN info_branch b on s.company_id = b.company_id and s.branch_id = b.branch_id  
left join info_item_batch itb on itb.batch_id = s.batch_id and itb.company_id = s.company_id
left join info_branch_position ibp on ibp.branch_id = s.branch_id and ibp.branch_position = s.branch_position and ibp.company_id = s.company_id
where s.company_id={company_id} and (b.status is null or b.status = '1') {CHECKED_CONDI}";

            }
            else if (showMultiBranch.ToLower() == "true" && showMultiBatch.ToLower() != "true")
            {
                this.SQLVariables["branch_name_fld"] = ",branch_name,branch_id,branch_position,branch_position_name";
                Grids["gridItems"].Columns["branch_name"].GetFromDb = true;
                Grids["gridItems"].Columns["branch_position_name"].GetFromDb = true;
                //Grids["gridItems"].Columns["batch_no"].GetFromDb = false;
                //Grids["gridItems"].Columns["produce_date"].GetFromDb = false;
                Grids["gridItems"].Columns["batch_id"].GetFromDb = false;

                Grids["gridItems"].Columns["branch_name"].Hidden = false;
                Grids["gridItems"].Columns["branch_position_name"].Hidden = false;
                //Grids["gridItems"].Columns["batch_no"].Hidden = true;
                //Grids["gridItems"].Columns["produce_date"].Hidden = true;
                Grids["gridItems"].Columns["batch_id"].Hidden = true;
                //Grids["gridItems"].Columns["batch_total"].Hidden = false;
                //this.SQLVariables["analyis_date_batch"] = ",produce_date,batch_no";
                stockSql = $@" 
SELECT branch_name, s.branch_id,s.branch_position,branch_position_name,s.item_id,sum(stock_qty) stock_qty,sum(stock_qty-COALESCE(sell_pend_qty,0)) avail_qty ,sum(COALESCE(sell_pend_qty,0)) sell_pend_qty,
json_agg(json_build_object('batch_id','','produce_date','','batch_no','')) as produce_date,
json_agg(json_build_object('batch_id','','produce_date','','batch_no','')) as batch_no
FROM stock s 
left join info_item_batch itb on itb.batch_id = s.batch_id and itb.company_id = s.company_id
LEFT JOIN info_branch b on s.company_id = b.company_id and s.branch_id = b.branch_id  
left join info_branch_position ibp on ibp.branch_id = s.branch_id and ibp.branch_position = s.branch_position and ibp.company_id = s.company_id
where s.company_id={company_id} and (b.status is null or b.status = '1') {CHECKED_CONDI} group by s.item_id,s.branch_id,s.branch_position,branch_name,branch_position_name";
            }
            else if (showMultiBranch.ToLower() != "true" && showMultiBatch.ToLower() == "true")
            {
                //this.SQLVariables["branch_name_fld"] = ",batch_no,produce_date,batch_id";
                this.SQLVariables["branch_name_fld"] = ",batch_id";
                this.SQLVariables["branch_name_fld"] = ",batch_id";
                Grids["gridItems"].Columns["branch_name"].GetFromDb = false;
                Grids["gridItems"].Columns["branch_id"].GetFromDb = false;
                Grids["gridItems"].Columns["branch_position_name"].GetFromDb = false;
                Grids["gridItems"].Columns["branch_position"].GetFromDb = false;
                //Grids["gridItems"].Columns["batch_no"].GetFromDb = true;
                //Grids["gridItems"].Columns["produce_date"].GetFromDb = true;
                Grids["gridItems"].Columns["batch_id"].GetFromDb = true;

                Grids["gridItems"].Columns["branch_name"].Hidden = true;
                Grids["gridItems"].Columns["branch_position_name"].Hidden = true;
                //Grids["gridItems"].Columns["batch_no"].Hidden = false;
                //Grids["gridItems"].Columns["produce_date"].Hidden = false;
                Grids["gridItems"].Columns["batch_id"].Hidden = false;
                this.SQLVariables["analyis_date_batch"] = ",produce_date,batch_no";
                //Grids["gridItems"].Columns["batch_total"].Hidden = true;
                stockSql = $@" 
SELECT s.item_id, sum(stock_qty) stock_qty,sum(stock_qty-COALESCE(sell_pend_qty,0)) avail_qty ,sum(COALESCE(sell_pend_qty,0)) sell_pend_qty,batch_no,s.batch_id,produce_date
FROM stock s 
left join info_item_batch itb on itb.batch_id = s.batch_id and itb.company_id = s.company_id
LEFT JOIN info_branch b on s.company_id = b.company_id and s.branch_id = b.branch_id  
left join info_branch_position ibp on ibp.branch_id = s.branch_id and ibp.branch_position = s.branch_position and ibp.company_id = s.company_id
where s.company_id={company_id} and (b.status is null or b.status = '1') {CHECKED_CONDI} group by s.item_id,s.batch_id,produce_date,batch_no";
            }
            else
            {
                this.SQLVariables["branch_name_fld"] = "";
                Grids["gridItems"].Columns["branch_name"].GetFromDb = false;
                Grids["gridItems"].Columns["branch_id"].GetFromDb = false;
                Grids["gridItems"].Columns["branch_position_name"].GetFromDb = false;
                Grids["gridItems"].Columns["branch_position"].GetFromDb = false;
                //Grids["gridItems"].Columns["batch_no"].GetFromDb = false;
                //Grids["gridItems"].Columns["produce_date"].GetFromDb = false;
                Grids["gridItems"].Columns["batch_id"].GetFromDb = false;

                Grids["gridItems"].Columns["branch_name"].Hidden = true;
                Grids["gridItems"].Columns["branch_position_name"].Hidden = true;
                //Grids["gridItems"].Columns["batch_no"].Hidden = true;
                //Grids["gridItems"].Columns["produce_date"].Hidden = true;
                Grids["gridItems"].Columns["batch_id"].Hidden = true;
                //Grids["gridItems"].Columns["batch_total"].Hidden = false ;
                stockSql = $@" 
SELECT s.item_id,sum(stock_qty) stock_qty,sum(stock_qty-COALESCE(sell_pend_qty,0)) avail_qty ,sum(COALESCE(sell_pend_qty,0)) sell_pend_qty,json_agg(json_build_object('batch_id',s.batch_id,'produce_date',Substring(COALESCE(produce_date::text,''),1,10),'batch_no',COALESCE(batch_no,''),'stock_qty',COALESCE(stock_qty,0),'sell_pend_qty',COALESCE(sell_pend_qty,0))) as produce_date,
json_agg(json_build_object('batch_id',s.batch_id,'produce_date',Substring(COALESCE(produce_date::text,''),1,10),'batch_no',COALESCE(batch_no,''),'stock_qty',COALESCE(stock_qty,0),'sell_pend_qty',COALESCE(sell_pend_qty,0))) as batch_no
FROM stock s 
LEFT JOIN info_branch b on s.company_id = b.company_id and s.branch_id = b.branch_id  
left join info_item_batch itb on itb.batch_id = s.batch_id and itb.company_id = s.company_id
left join info_branch_position ibp on ibp.branch_id = s.branch_id and ibp.branch_position = s.branch_position and ibp.company_id = s.company_id
where s.company_id={company_id} and (b.status is null or b.status = '1') {CHECKED_CONDI} group by s.item_id";
            }
            this.SQLVariables["stock_sql"] = stockSql;
            var cost_price_type = DataItems["cost_price_type"].Value;
            var recentPriceTime = "0";
            if (cost_price_type == "4")
            {
                string sql = $@"SELECT replace((setting->'recentPriceTime')::text,'""','') recentPriceTime FROM company_setting where company_id = {company_id}";

                dynamic re = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (re != null) recentPriceTime = re.recentpricetime;
            }

            var columns = Grids.GetValueOrDefault("gridItems").Columns;
            var cost_amt = columns.GetValueOrDefault("cost_amt");
            var avail_cost_amt = columns.GetValueOrDefault("avail_cost_amt");

            if (cost_price_type == "1")//预设成本价
            {
                cost_amt.SqlFld = "round(stock_qty*cost_price_spec::numeric,2) ";
                avail_cost_amt.SqlFld = "round(avail_qty_s*cost_price_spec::numeric,2) ";
            }
            else if (cost_price_type == "2") //加权平均成本
            {
                cost_amt.SqlFld = "round(stock_qty*cost_price_avg::numeric,2)";
                avail_cost_amt.SqlFld = "round(avail_qty_s*cost_price_avg::numeric,2) ";
            }
            else if (cost_price_type == "3")//预设进价
            {
                cost_amt.SqlFld = "round(stock_qty * s_buy_price::numeric, 2)";
                avail_cost_amt.SqlFld = "round(avail_qty_s*s_buy_price::numeric,2) ";
            }
            else if (cost_price_type == "4" && recentPriceTime != "0")//最近平均进价
            {
                var avgPriceKey = $"avg{recentPriceTime}";
                cost_amt.SqlFld = $"round(stock_qty *(cost_price_recent->'{avgPriceKey}')::numeric, 2)";
                avail_cost_amt.SqlFld = $"round(avail_qty_s* (cost_price_recent->'{avgPriceKey}')::numeric,2) ";
            }


            var buy_price = columns.GetValueOrDefault("buy_price");
            var s_cost_price = columns.GetValueOrDefault("s_cost_price");
            var b_cost_price = columns.GetValueOrDefault("b_cost_price");
            if (cost_price_type == "1")//预设成本价
            {
                buy_price.SqlFld = "round(cost_price_spec::numeric,6) ";
                s_cost_price.SqlFld = "case when round(cost_price_spec::numeric,2)<> 0 then concat(round(cost_price_spec::numeric,2),'/',s_unit)  else '' end";
                b_cost_price.SqlFld = "case when round(cost_price_spec::numeric,2)<> 0 and b_factor is not null then concat(round((cost_price_spec*b_factor::numeric)::numeric,2),'/',b_unit)  else '' end";
            }
            else if (cost_price_type == "2") //加权平均成本
            {
                buy_price.SqlFld = "round(cost_price_avg::numeric,6)";
                s_cost_price.SqlFld = "case when round(cost_price_avg::numeric,6)<>0 then concat (round(cost_price_avg::numeric,6),'/',s_unit)  else '' end ";
                b_cost_price.SqlFld = "case when round(cost_price_avg::numeric,6)<> 0 and b_factor is not null then concat(round((cost_price_avg*b_factor::numeric)::numeric,2),'/',b_unit)  else '' end";
            }
            else if (cost_price_type == "3")//预设进价
            {
                buy_price.SqlFld = "round(s_buy_price::numeric, 6)";
                s_cost_price.SqlFld = "case when round(s_buy_price::numeric,6)<> 0 then concat(round(s_buy_price::numeric,6),'/',s_unit)  else '' end ";
                b_cost_price.SqlFld = "case when round(s_buy_price::numeric,6)<> 0 and b_factor is not null then concat(round((s_buy_price*b_factor::numeric)::numeric,6),'/',b_unit)  else '' end";
            }
            else if (cost_price_type == "4" && recentPriceTime != "0")//最近平均进价
            {
                var avgPriceKey = $"avg{recentPriceTime}";
                buy_price.SqlFld = $"round((cost_price_recent->'{avgPriceKey}')::numeric, 6)";
                s_cost_price.SqlFld = $"case when round((cost_price_recent->'{avgPriceKey}')::numeric,6)<> 0 then concat(round((cost_price_recent->'{avgPriceKey}')::numeric,6),'/',s_unit)  else '' end ";
                b_cost_price.SqlFld = $"case when round((cost_price_recent->'{avgPriceKey}')::numeric,6)<> 0 and b_factor is not null then concat(round(((cost_price_recent->'{avgPriceKey}')::numeric*b_factor::numeric)::numeric,6),'/',b_unit)  else '' end";
            }

        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            var costPriceType = "3";
            var costPriceTypeName = "预设进价";
            if (JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            if (costPriceType == "1") costPriceTypeName = "预设成本价";
            else if (costPriceType == "2") costPriceTypeName = "加权平均价";
            else if (costPriceType == "4") costPriceTypeName = "最近平均进价";
            DataItems["cost_price_type"].Value = costPriceType;
            DataItems["cost_price_type"].Label = costPriceTypeName;
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                var columns = await Grids["gridItems"].GetAllColumns();
                columns["cost_amt"].HideOnLoad = columns["cost_amt"].Hidden = true;
                columns["avail_cost_amt"].HideOnLoad = columns["avail_cost_amt"].Hidden = true;
                columns["buy_price"].HideOnLoad = columns["buy_price"].Hidden = true;
                columns["s_cost_price"].HideOnLoad = columns["s_cost_price"].Hidden = true;
                columns["b_cost_price"].HideOnLoad = columns["b_cost_price"].Hidden = true;
            }

        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class StocksViewController : QueryController
    {
        public StocksViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            StocksViewModel model = new StocksViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<object> GetQueryRecords([FromBody] dynamic data)
        {
            StocksViewModel model = new StocksViewModel(cmd);

            object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
            return records;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            StocksViewModel model = new StocksViewModel(cmd);

            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }
        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            StocksViewModel model = new StocksViewModel(cmd);
            await model.OnPageInitedWithDataAndRight(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
