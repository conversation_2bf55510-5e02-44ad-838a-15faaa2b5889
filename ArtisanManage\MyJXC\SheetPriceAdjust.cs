﻿using ArtisanManage.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static ArtisanManage.Services.CommonTool;


namespace ArtisanManage.MyJXC
{
    // 调价单
    public class SheetRowPriceAdjustItem : SheetRowBase
    {

        public SheetRowPriceAdjustItem()
        {

        }

        [SaveToDB] [FromFld] public string item_id { get; set; }
                   [FromFld(LOAD_PURPOSE.SHOW)] public string item_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_unit_no { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_factor { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_factor { get; set; }
        [FromFld("yj_get_unit_relation(b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no) as unit_conv", LOAD_PURPOSE.SHOW)] public string unit_conv { get; set; }
        [SaveToDB] [FromFld] public string s_price { get; set; }
        [SaveToDB] [FromFld] public string m_price { get; set; }
        [SaveToDB] [FromFld] public string b_price { get; set; }
        public string s_old_price_label { get; set; } = ""; // 应得 plan_name1:sprice1,plan_name2:sprice2
        public string m_old_price_label { get; set; } = "";
        public string b_old_price_label { get; set; } = "";
        [SaveToDB] [FromFld] public string price_info { get; set; } = ""; //数据库中为jsonb格式: [{ "planID":"333","sPrice":"50"},{"planID":"-1","sPrice":"35","bPrice":"70"} ] 
        
    }

    public class SheetPriceAdjust : SheetBase<SheetRowPriceAdjustItem>
    {
        [SaveToDB] [FromFld] public string plans_id { get; set; } // 调的价格类型,存在多选
        [FromFld(LOAD_PURPOSE.SHOW)] public string plans_name { get; set; } = ""; 
        [SaveToDB] [FromFld] public string seller_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";
        [SaveToDB] [FromFld] public override SHEET_TYPE sheet_type { get; set; }

        public SheetPriceAdjust(LOAD_PURPOSE loadPurpose) : base("sheet_price_adjust_main", "sheet_price_adjust_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_PRICE_ADJUST;
            if (loadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @"left join (select oper_id,oper_name seller_name from info_operator) seller on t.seller_id=seller.oper_id
                                 left join (select oper_id,oper_name as maker_name from info_operator) maker on t.maker_id=maker.oper_id
                                 left join (select oper_id,oper_name as approver_name from info_operator) approver on t.approver_id=approver.oper_id
                                 left join (SELECT plan_sheet_id,string_agg(s.plan_name,',') plans_name FROM (select sheet_id plan_sheet_id,plans_id from sheet_price_adjust_main where company_id = ~COMPANY_ID) g 
                                                    LEFT JOIN (select plan_id,plan_name from price_plan_main where company_id = ~COMPANY_ID union select '0','零售价' union select '-1','批发价') s 
                                                    ON s.plan_id::text = ANY(STRING_TO_ARRAY(g.plans_id, ',')) group by plan_sheet_id
                                            ) plan on t.sheet_id = plan.plan_sheet_id
                                ";
                DetailLeftJoin = @"left join info_item_prop i on t.item_id=i.item_id
                                   left join (select item_id,s_unit->>'f1' as s_unit_no,(s_unit->>'f2')::real as s_unit_factor,m_unit->>'f1' as m_unit_no,(m_unit->>'f2')::real as m_unit_factor,
                                                     b_unit->>'f1' as b_unit_no,(b_unit->>'f2')::real as b_unit_factor
                                              from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor)) as json from info_item_multi_unit where company_id=~company_id order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)) unit_barcode on t.item_id=unit_barcode.item_id    
                                   left join info_item_brand ib on i.item_brand=ib.brand_id
                                  ";
            }
            else if (loadPurpose == LOAD_PURPOSE.APPROVE)
                DetailLeftJoin = @"";

        }

        protected class CInfoForApprove : CInfoForApproveBase
        {
            public List<SheetRowPriceAdjustItem> SheetRows = null;
            public bool PricePlanExist = false;
            public string PricePlanIDs = "";
        }


        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql;
            base.GetInfoForApprove_SetQQ(QQ);
            if(plans_id != "")
            {
                sql = $@"select string_agg(plan_id::text,',') plan_id from price_plan_main where company_id = {company_id} and plan_id in ({plans_id}) ";
                QQ.Enqueue("planID", sql);
            }

        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if(sqlName == "planID") // 存在价格方案
            {
                dynamic pricePlan = CDbDealer.Get1RecordFromDr(dr);
                if (pricePlan != null)  // 日后可以拓展---修改某些不应修改的方案或防止修改有问题的方案 的价格
                {
                    info.PricePlanExist = true; 
                    info.PricePlanIDs = pricePlan.plan_id;
                }
            }
        }


        protected override async Task<string> CheckSheetValid(CMySbCommand cmd=null)
        {
            string checkResult = await base.CheckSheetValid(cmd);
            if (checkResult != "OK")
                return checkResult;
            if (plans_id == "") return "必须指定需调整的价格方案";
            if (seller_id == "") return "必须指定业务员";
            return "OK";
        }

        
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
            var allArr = plans_id.Split(',');
            string sql = "";
            string now = CPubVars.GetDateText(DateTime.Now);
            string balSql = "";
            string wholeSql = "";
            string planSql = "";
                
            foreach (var p in allArr)
            {
                foreach (SheetRowPriceAdjustItem row in SheetRows)
                {
                    #region item_price_adjust 记录商品最新修改时间
                    balSql += $@"insert into item_price_adjust(company_id,plan_id,item_id,adjust_time) values ({company_id},{p},{row.item_id},'{now}')
                                on conflict (company_id,plan_id,item_id) do update set adjust_time='{now}';                   
                                ";
                    #endregion
                    #region 批发价
                    if (p=="0"||p=="-1")
                    {
                        var prFld = "wholesale_price";
                        if (p == "0") prFld = "retail_price";
                        wholeSql += @$"insert into info_item_multi_unit(company_id,item_id,unit_no,unit_factor,{prFld},unit_type) values ({company_id},{row.item_id},'{row.s_unit_no}',1,{row.s_price},'s')
                            on conflict (company_id,item_id,unit_type) do update set {prFld}={row.s_price};";
                        if (row.m_price != "" && row.m_price != null)
                            wholeSql += $@"insert into info_item_multi_unit(company_id,item_id,unit_no,unit_factor,{prFld},unit_type) values ({company_id},{row.item_id},'{row.m_unit_no}',{row.m_unit_factor},{row.m_price},'m')
                            on conflict (company_id,item_id,unit_type) do update set {prFld}={row.m_price};";
                        if (row.b_price != "" && row.b_price != null)
                            wholeSql += $@"insert into info_item_multi_unit(company_id,item_id,unit_no,unit_factor,{prFld},unit_type) values ({company_id},{row.item_id},'{row.b_unit_no}',{row.b_unit_factor},{row.b_price},'b')
                            on conflict (company_id,item_id,unit_type) do update set {prFld}={row.b_price};";
                    }
                    #endregion
                    #region 价格方案
                    else if (info.PricePlanIDs.Contains(p))
                    {
                        string mPrice = row.m_price;
                        string bPrice = row.b_price;
                        if (mPrice == "" || mPrice == null) mPrice = "null";
                        if (bPrice == "" || bPrice == null) bPrice = "null";
                        
                        planSql += $@"insert into price_plan_item(company_id,plan_id,item_id,s_price,m_price,b_price) values ({company_id},{p},{row.item_id},{row.s_price},{mPrice},{bPrice})
                                on conflict (company_id,plan_id,item_id) do update set s_price = {row.s_price},m_price = {mPrice},b_price = {bPrice};";

                    }
                    #endregion
                }
            }
            sql += balSql + wholeSql + planSql;

            if(info.PricePlanIDs!="") sql += $"update price_plan_main set update_time = '{now}' where company_id = {company_id} and plan_id in ({info.PricePlanIDs});"; //更新方案时间
            
            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
        }
    }
}