# 1. 非查询语句

## 1.1 执行单条语句 返回   Task\<JsonResult\>  

```c#
string sql = "xxxx";
cmd.CommandText = sql;
await cmd.ExecuteNonQueryAsync();
string result = "OK";
string msg = "";
return new JsonResult(new { result,msg});
```

```c#
CDbDealer db = new CDbDealer();
// 数据库名和字典中key一致 
db.<PERSON>d<PERSON>ields(userInfoDictionary, "subscribe_scene,nickname,qr_scene_str");
sql = db.GetInsertSQL("wx_user");
cmd.CommandText = sql;
await cmd.ExecuteNonQueryAsync();
string result = "OK";
string msg = "";
return new JsonResult(new { result,msg});
```

```c#
CDbDealer db = new CDbDealer();

db.Add<PERSON>ield("key1", v1);
db.<PERSON>d<PERSON>ield("key2", v2);

sql = db.GetInsertSQL("wx_user");
cmd.CommandText = sql;
await cmd.ExecuteNonQueryAsync();
string result = "OK";
string msg = "";
return new JsonResult(new { result,msg});
```

## 1.2  执行单条语句，返回SQL相关数据

```c#
string sql = @$"INSERT INTO wx_user (open_id,  union_id, subscribe_scene, qr_scene,qr_scene_str, nick_name) 
                                         VALUES ({openId},{unionId},{subscribeScene},{qrScene},{qrSceneStr},{nickName}) returning wx_id";
```

### 1.2.1 例：返回单个字段（自增）

```C#
Task<object> ov = cmd.ExecuteScalarAsync();
if (ov != null && ov != DBNull.Value)
{
	string id = ov.ToString();
}
```

### 1.2.2  返回单个字段

```c#
dynamic rec= await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
if (rec != null)
{
	string s = rec.wx_id;

}
```

### 1.2.3 返回多个字段

```c#
 CMySbDataReader dr = await cmd.ExecuteReaderAsync();
 if (dr.Read())
 {
     string s1 = CPubVars.GetTextFromDr(dr, "wx_id_1");
     string s2 = CPubVars.GetTextFromDr(dr, "wx_id_1");
}
```

### 1.2.4 返回多个字段

```c#
List<ExpandoObject> lstRecords = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
foreach (dynamic rec in lstRecords)
{
	string s = rec.wx_id;
}         
```

## 1.3 执行多条语句 

> 在SQL语句中进行拼接

```c#
string sql = "xxxx;yyyyy";
cmd.CommandText = sql;
await cmd.ExecuteNonQueryAsync();
string result = "OK";
string msg = "";
return Json(new { result,msg});
```

# 2. 查询语句

## 2.1 单条语句

> 类似上返回字段

```C#
string sql = "select * from wx_user";
CDbDealer.Get1RecordFromSQLAsync();

```

## 2.2 多条语句

```c#
SQLQueue QQ = new SQLQueue(cmd);
QQ.Enqueue("sql1"，“”);
QQ.Enqueue("sql1"，“”);
CMySbDataReader dr=await QQ.ExecuteReaderAsync();
while (QQ.Count > 0)
{
     string tb=QQ.Dequeue();
     if (tb == "sql1")
     {
         dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
     }
}
QQ.Clear();
```

# 3. 解析JsonResult

```
JsonResult jr;
dynamic d =  jr.Value;
```

