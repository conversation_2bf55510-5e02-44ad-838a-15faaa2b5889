using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.Services.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using ArtisanManage.MyJXC;
using ArtisanManage.YingJiangMallMini.Services;

namespace ArtisanManage
{
    public class FixItemAttrModel : PageBaseModel
    {
        public string operKey;
       
        public FixItemAttrModel(CMySbCommand cmd):base(MenuId.companySetting)
        {
            this.cmd = cmd;
        }
  
        public async Task OnGet()
        {
            

        }
    }

    [Route("setting/[controller]/[action]")]
    public class FixItemAttrController : YjController
    {
        CMySbCommand cmd;


        public FixItemAttrController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        
        string AppCondi = "approve_time is not null and red_flag is null";
        string ArrearsCondi = " abs(total_amount-now_pay_amount-now_disc_amount)>0.1 ";
        //string ArrearsAndPrepayCondi = "(abs(total_amount-now_pay_amount-now_disc_amount)>0.1 or abs(prepay_amount)>0.01) ";
        string ArrearsAndPrepayCondi = "not(abs(total_amount-now_pay_amount-now_disc_amount)<=0.1 and  abs(prepay_amount)<=0.01) ";


        [HttpPost]
        public async Task<JsonResult> FixItemAttr([FromBody] dynamic data)
        { 
            string companyID = data.companyID;
            string companyName = data.companyName;
            
            var result = "OK";
            var msg = "";

            string sql = "";
         
			sql = $"select * from g_company gc where company_id={companyID} and company_name='{companyName}';";
			dynamic clientRec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
			if (clientRec == null)
			{
				return Json(new { result = "Error", msg = "公司信息不匹配" });
			}
			CMySbTransaction tran = cmd.Connection.BeginTransaction();
            try
            { 
				await MallMiniItemInfoService.UpdateAvailAttrCombin(cmd, companyID);
				tran.Commit();
                Console.WriteLine("OK");
            }
            catch(Exception e)
            {
                msg = e.Message;
                tran.Rollback();
            }
            result = msg == "" ? "OK" : "Error";

            return Json(new { result, msg });
        }
     
      
    }
}