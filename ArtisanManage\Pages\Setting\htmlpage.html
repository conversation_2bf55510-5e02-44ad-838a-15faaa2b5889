﻿<!DOCTYPE html>
<html>
<head>
    <title>自定义票据</title>
    <style>
        .container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        .sidebar {
            width: 200px;
        }
        .preview {
            width: 400px;
            height: 300px;
            border: 1px solid #ccc;
        }
        .form-section {
            margin-bottom: 20px;
        }
            .form-section label {
                display: block;
                font-weight: bold;
            }
            .form-section input[type="checkbox"] {
                margin-right: 5px;
            }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="form-section">
                <label>表头</label>
                <input type="checkbox" name="header" value="company-name"> 公司名称<br>
                <input type="checkbox" name="header" value="document-type"> 单据类型<br>
                <input type="checkbox" name="header" value="document-number"> 单号<br>
                <input type="checkbox" name="header" value="customer-name"> 客户名称<br>
                <input type="checkbox" name="header" value="customer-phone"> 客户电话<br>
                <input type="checkbox" name="header" value="customer-address"> 客户地址<br>
                <input type="checkbox" name="header" value="warehouse"> 仓库<br>
                <input type="checkbox" name="header" value="time"> 时间<br>
                <input type="checkbox" name="header" value="print-count"> 打印次数<br>
            </div>
            <div class="form-section">
                <label>表尾</label>
                <input type="checkbox" name="footer" value="total-amount"> 总金额<br>
                <input type="checkbox" name="footer" value="barcode"> 条形码<br>
                <input type="checkbox" name="footer" value="company-seal"> 公司总章<br>
            </div>
            <button id="save-btn">保存</button>
        </div>
        <div class="editor">
            <div class="form-section">
                <label>表头</label>
                <textarea id="header-textarea" rows="5" cols="30"></textarea>
            </div>
            <div class="form-section">
                <label>表体</label>
                <textarea id="body-textarea" rows="10" cols="30"></textarea>
            </div>
            <div class="form-section">
                <label>表尾</label>
                <textarea id="footer-textarea" rows="5" cols="30"></textarea>
            </div>
        </div>
        <div class="preview"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    $(document).ready(function() {
      // 保存按钮点击事件
      $('#save-btn').on('click', function() {
        var headerContent = $('#header-textarea').val();
        var bodyContent = $('#body-textarea').val();
        var footerContent = $('#footer-textarea').val();

        var previewContent = headerContent + '<hr>' + bodyContent + '<hr>' + footerContent;
        $('.preview').html(previewContent);
      });
    });
    </script>
</body>
</html>