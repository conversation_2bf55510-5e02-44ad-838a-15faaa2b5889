﻿
using ArtisanManage.CwPages;
using ArtisanManage.CwPages.Report;
using ArtisanManage.Models;
using ArtisanManage.MyCW;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.CwPages
{
    public class CwVoucherViewModel : PageQueryModel
    {
        public string companyName { get; set; }
        //使用此页面的功能：明细账按钮->未审核凭证，期末结转->结账批量检查辅助凭证
        public CwVoucherViewModel(CMySbCommand cmd) : base(Services.MenuId.sheetVoucher)
        {
            this.cmd = cmd;
            this.PageName = "CwVoucherView";
            this.PageTitle = "查凭证";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="m.happen_time",   CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="m.happen_time",   CompareOperator="<=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"sheet_id", new DataItem(){ FldArea="divHead", Title="sheet_id", SqlFld="m.sheet_id::text", CompareOperator="like", Hidden=true, HideOnLoad=true }},
                {"voucher_no",new DataItem(){FldArea="divHead",Title="凭证号", SqlFld="m.sheet_no::text", CompareOperator="="}},
                {"sub_id",new DataItem(){Title="科目",MumSelectable=true,LikeWrapper="/", FldArea="divHead",LabelFld="sub_name",CtrlType="jqxDropDownTree",DropDownHeight="200",DropDownWidth="150", TreePathFld="other_sub",CompareOperator="like",SqlFld="other_sub", MaxRecords="500",
                    SqlForOptions="select sub_id as v,sub_name as l,mother_id as pv from cw_subject where company_id= ~COMPANY_ID order by sub_code::text ",
                }},
                {"status",new DataItem(){FldArea="divHead",Title="凭证状态",LabelFld = "status_name",ButtonUsage = "list",CompareOperator="=",Value="normal",Label="正常凭证",
                        Source = @"[{v:'normal',l:'正常凭证',condition:""m.red_flag is null""},
                                   {v:'unapproved',l:'未审核凭证',condition:""m.approve_time is null""},
                                   {v:'approved',l:'已审核凭证',condition:""m.approve_time is not null and red_flag is null""},
                                   {v:'red',l:'红冲凭证',condition:""m.red_flag in ('1','2') ""},
                                   {v:'all',l:'所有',condition:""true""}]"
                }},
                {"type",new DataItem(){FldArea="divHead",Title="凭证类型",LabelFld = "type_name",ButtonUsage = "list", Checkboxes=true, CompareOperator="=",
                        Source = @"[{v:'all',l:'所有',condition:""true""},
                                    {v:'sheet',l:'单据生成',condition:""m.sheet_attribute is not null""},
                                    {v:'write',l:'手工录入',condition:""(m.sheet_attribute is null and coalesce(m.make_brief,'') not in ('结转销售成本','结转损益','摊销待摊费用') and coalesce(m.make_brief,'') not like '%年末结转利润')""},
                                    {v:'close',l:'期末结转',condition:""(m.make_brief in ('结转销售成本','结转损益','摊销待摊费用') or m.make_brief like '%年末结转利润')""}]"
                }},
                {"remark",new DataItem(){FldArea="divHead",Title="摘要", SqlFld="d.remark", CompareOperator="like", Hidden=true}},
                {"sheet_no",new DataItem(){FldArea="divHead",Title="业务单号", SqlFld="biz.biz_nos", CompareOperator="like"}},
                {"maker_id", new DataItem(){ FldArea="divHead", Title="制单人", LabelFld="maker_name", CompareOperator="=", ButtonUsage = "list", Checkboxes=true, Hidden=true, SqlForOptions="select oper_id as v,oper_name as l from info_operator where company_id=~COMPANY_ID order by order_index,oper_name " }},
                {"byAccountingPeriod",new DataItem(){Title="按当前会计期间查询",FldArea="divHead",CtrlType="jqxCheckBox",ForQuery=false,JSDealItemOnSelect=@"
                        
                        let by_accounting_period = $('#byAccountingPeriod').val();
                        if (!by_accounting_period){
                            return;
                        }
            
                        $.ajax({
                            url: '/api/CwVoucherView/GetAcPeriod',
                            type: 'get',
                            data: { operKey: g_operKey },
                            contentType: 'application/json;charset=UTF-8',
                            dataType: 'json'
                        }).then((res) => {
                            if (res.result === 'OK') {
                                console.log('cvview-get ac period', res);
                                $('#startDay').val(res.opPeriod);
                                $('#endDay').val(res.edPeriod);
                                QueryData();
                            }else{
                                bw.toast(res.msg);
                            }
                        });
                "}},
            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                    "gridItems",  new QueryGrid()
                    {
                        ShowAggregates=true,
                        IdColumn="sheet_id",
                        HasCheck=true,
                        Sortable=true,
                //      AllowMultiColumnsSort=true,
                        Columns = new Dictionary<string, DataItem>()
                        {
                        //{"available",  new DataItem(){ threestatecheckbox=true, columntype="checkbox", Width="70",ForQuery=false } },
                        {"sheet_id", new DataItem(){Title="sheet_id", Hidden=true,HideOnLoad=true, Linkable=true, Width="80",SqlFld="m.sheet_id"}},
                        {"sheet_no",new DataItem(){Title = "凭证号",Width="100",SqlFld="(case when sheet_no is not null then concat('记-',sheet_no) end )",Linkable=true, Sortable=true, SortFld="sheet_no::decimal" } },
                        {"sheet_status", new DataItem(){Title="状态",  Width="100",SqlFld="(case when m.red_flag='2' then '红字单' when m.red_flag='1' then '已红冲' when m.approve_time is null then '未审' else '已审' END)"}},
                        {"maker_name",new DataItem(){Title = "制单人", Width="100", SqlFld="io.oper_name" } },
                        {"remark",new DataItem(){Title="摘要",Width="220",FldArea="divHead"} },
                        {"happen_time",new DataItem(){Title="交易时间",Width="150",FldArea="divHead",SqlFld = "m.happen_time", Sortable=true, ForQuery=false} },
                        {"make_time",new DataItem(){Title="制单时间",Width="150",FldArea="divHead",SqlFld = "m.make_time", Sortable=true, Hidden=true, ForQuery=false} },
                        {"approve_time",new DataItem(){Title="审核时间",Width="150",FldArea="divHead",SqlFld = "m.approve_time", Sortable=true,Hidden=true} },
                        {"sub_code",new DataItem(){Title="科目编码",Width="100",FldArea="divHead"} },
                        {"sub_id",new DataItem(){Title="科目",Width="100", FldArea="divHead",SqlFld="d.sub_id",Hidden=true,HideOnLoad=true} },
                        {"sub_name",new DataItem(){Title="科目名称",Width="220",FldArea="divHead",SqlFld="substring((select string_agg(sub_name,'-' order by sub_code::text) as name from cw_subject where company_id= ~COMPANY_ID and sub_id::text in ( select * from REGEXP_SPLIT_TO_TABLE((select substring(concat(other_sub,sub_id),2) from cw_subject where company_id= ~COMPANY_ID and level>=1 and sub_id=d.sub_id), '/')  ) ),8)"} },
                        {"assister_types_names",new DataItem(){Title="辅助科目",Width="230",FldArea="divHead",SqlFld=@"
                            (case when (d.assister1_id is not null and d.assister2_id is null) then (case when d.assister1_type='C' then concat('【客户】',type_sc1.sup_name) when assister1_type='S' then concat('【供应商】',type_sc1.sup_name) when d.assister1_type='INV' then concat('【商品】',type_inv1.item_name) when d.assister1_type='DEP' then concat('【部门】',type_dep1.depart_name) when d.assister1_type='MAN' then concat('【业务员】',type_man1.oper_name) else null end) 
                            when (d.assister1_id is not null and d.assister2_id is not null) then concat((case when d.assister1_type='C' then concat('【客户】',type_sc1.sup_name) when assister1_type='S' then concat('【供应商】',type_sc1.sup_name) when d.assister1_type='INV' then concat('【商品】',type_inv1.item_name) when d.assister1_type='DEP' then concat('【部门】',type_dep1.depart_name) when d.assister1_type='MAN' then concat('【业务员】',type_man1.oper_name) else null end),'_',(case when d.assister2_type='C' then concat('【客户】',type_sc2.sup_name) when assister2_type='S' then concat('【供应商】',type_sc2.sup_name) when d.assister2_type='INV' then concat('【商品】',type_inv2.item_name) when d.assister2_type='DEP' then concat('【部门】',type_dep2.depart_name) when d.assister2_type='MAN' then concat('【业务员】',type_man2.oper_name) else null end)) else null end)"} },
                        {"debit_amount",new DataItem(){Title="借方金额",Width="100",FldArea="divHead", SqlFld="(case when coalesce(debit_amount,0)<>0 then debit_amount else null end)", ShowSum=true } },
                        {"credit_amount",new DataItem(){Title="贷方金额",Width="100",FldArea="divHead", SqlFld="(case when coalesce(credit_amount,0)<>0 then credit_amount else null end)", ShowSum=true} },
                        {"business_sheet_id_nos",new DataItem(){Title="业务单据",Width="250",FldArea="divHead",SqlFld = "biz.biz_id_nos",JSCellRender="bizSheetIdNoRender", Sortable=true} },
                        {"business_sheet_type",new DataItem(){Title="业务单据类型", Hidden=true,HideOnLoad=true } },
                        },
                        QueryFromSQL=@"from cw_voucher_main m left join cw_voucher_detail d on m.sheet_id = d.sheet_id and d.company_id= ~COMPANY_ID and d.happen_time>='~VAR_startDay' and d.happen_time<='~VAR_endDay'
                                LEFT JOIN (select sub_id,sub_code,sub_name,sub_type,other_sub||sub_id||'/' other_sub from cw_subject where company_id= ~COMPANY_ID) pw1 on pw1.sub_id = d.sub_id
                                left join (
                                        select sheet_id, row->>'biz_sheet_type' as business_sheet_type,
                                            string_agg(concat(row->>'biz_sheet_id',',',row->>'biz_sheet_no'),';') as biz_id_nos,
                                            string_agg(row->>'biz_sheet_no',',') as biz_nos,
                                            string_agg(row->>'biz_make_brief','；') filter (where coalesce(row->>'biz_make_brief','')<>'') as briefs
                                        from (
                                            select sheet_id,(jsonb_array_elements_text((sheet_attribute->>'biz_info')::jsonb))::jsonb as row from cw_voucher_main
                                            where company_id= ~COMPANY_ID and sheet_attribute is not null 
                                        ) tt group by sheet_id,row->>'biz_sheet_type'
                                ) biz on biz.sheet_id=m.sheet_id 

                                left join info_supcust type_sc1 on type_sc1.company_id=d.company_id and type_sc1.supcust_id=d.assister1_id
                                left join info_item_prop  type_inv1 on type_inv1.company_id=m.company_id and type_inv1.item_id=d.assister1_id
                                left join (select ide.depart_id, ide.depart_name, ide.company_id  from info_department ide left join (select company_id,depart_id,count(oper_id) as c from info_operator where company_id= ~COMPANY_ID  and (status = '1' or status is null) group by company_id,depart_id) io on ide.company_id=io.company_id and ide.depart_id=io.depart_id where ide.company_id= ~COMPANY_ID  and io.c>0 ) type_dep1 on type_dep1.company_id=m.company_id and type_dep1.depart_id=d.assister1_id
                                left join info_operator type_man1 on type_man1.company_id=m.company_id and type_man1.oper_id=d.assister1_id

                                left join info_supcust type_sc2 on type_sc2.company_id=d.company_id and type_sc2.supcust_id=d.assister2_id
                                left join  info_item_prop  type_inv2 on type_inv2.company_id=m.company_id and type_inv2.item_id=d.assister2_id
                                left join (select ide.depart_id, ide.depart_name, ide.company_id  from info_department ide left join (select company_id,depart_id,count(oper_id) as c from info_operator where company_id= ~COMPANY_ID  and (status = '1' or status is null) group by company_id,depart_id) io on ide.company_id=io.company_id and ide.depart_id=io.depart_id where ide.company_id= ~COMPANY_ID  and io.c>0 ) type_dep2 on type_dep2.company_id=m.company_id and type_dep2.depart_id=d.assister2_id
                                left join info_operator type_man2 on type_man2.company_id=m.company_id and type_man2.oper_id=d.assister2_id
                                
                                left join info_operator io on io.company_id=m.company_id and io.oper_id=m.maker_id

                                where m.company_id=~COMPANY_ID and m.happen_time>='~VAR_startDay' and m.happen_time<='~VAR_endDay'",
                        QueryOrderSQL=" order by TO_CHAR(m.happen_time, 'YYYYMM') desc,m.sheet_no desc,m.sheet_id desc,d.row_index,m.happen_time desc"
                    }
                }
            };
        }
        public async Task OnGet()
        {
            await InitGet(cmd);

            // 获取公司名称
            await LoadCompanyName(cmd);
        }

        private async Task LoadCompanyName(CMySbCommand cmd)
        {
            // 首先尝试从 company_setting 表获取公司名称
            var sql = $"select setting->>'companyName' as company_name from company_setting where company_id={company_id}";
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

            if (setting != null && !string.IsNullOrEmpty(setting.company_name))
            {
                companyName = setting.company_name;
            }
            else
            {
                // 如果 company_setting 中没有，则从 g_company 表获取
                sql = $"select company_name from g_company where company_id={company_id}";
                dynamic company = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (company != null)
                {
                    companyName = company.company_name;
                }
            }
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;
        }

        public override void BeforeExportExcel(Dictionary<int, Dictionary<string, dynamic>> rows)
        {

            foreach (var kp in rows)
            {
                dynamic row = kp.Value;
                string s = "";
                string result = "";
                if (row["business_sheet_id_nos"] != null)
                {
                    s = row["business_sheet_id_nos"];
                    int commaIndex = s.IndexOf(',');
                    while (commaIndex != -1)
                    {
                        commaIndex = s.IndexOf(',');
                        if (commaIndex != -1)
                        {
                            s = s.Substring(commaIndex + 1);
                            int semicolonIndex = s.IndexOf(';');
                            if (semicolonIndex == -1)
                            {
                                result = result + s;
                            }
                            else
                            {
                                result = result + s.Substring(0, semicolonIndex) + ";";
                            }

                        }
                        commaIndex = s.IndexOf(',');
                    }
                }
                else continue;
                row["business_sheet_id_nos"] = result;
            }
        }
        //public override void BeforeExportExcel()
        //{

        //}
    }

    [Route("api/[controller]/[action]")]
    public class CwVoucherViewController : Controller
    {
        CMySbCommand cmd;
        public CwVoucherViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value)
        {
            CwVoucherViewModel model = new CwVoucherViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, null);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            CwVoucherViewModel model = new CwVoucherViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpGet]
        public async Task<JsonResult> GetAcPeriod(string operKey)
        {
            string result = "OK";
            string msg = "";
            Security.GetInfoFromOperKey(operKey, out string companyID);
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select coalesce(setting ->>  'useAccounting','false') useaccounting, coalesce(setting ->>  'accountingPeriod','') accountingperiod, coalesce(setting ->>  'cwPeriodFromTo','') cwperiodfromto from company_setting where company_id = {companyID}", cmd);
            if (setting == null || !Convert.ToBoolean(setting.useaccounting)) return Json(new { result = "Error", msg = "请先开账" });

            DateTime opPeriod = Convert.ToDateTime(setting.accountingperiod + "-01");
            DateTime edPeriod = opPeriod.AddMonths(1).AddSeconds(-1);
            if (setting.cwperiodfromto != "")
            {
                string cw_periodfromto = setting.cwperiodfromto;
                if (setting.accountingperiod == cw_periodfromto.Substring(0, 7))
                {
                    opPeriod = Convert.ToDateTime(setting.cwperiodfromto.Split(" ~ ")[0]);
                }
            }
            return Json(new { result, msg, opPeriod, edPeriod });
        }

        [HttpPost]
        public async Task<JsonResult> BulkApprove([FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            CMySbTransaction tran = null;
            Dictionary<string, string> redisKeys = new Dictionary<string, string>();
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            cmd.company_id = companyID;
            if (data.ids.ToString() == "") return Json(new { result = "Error", msg = "未选择凭证" });
            SheetCwVoucher vo = new SheetCwVoucher(MyCW.LOAD_PURPOSE.APPROVE);
            List<SheetCwVoucher> vouchers = await vo.LoadMultiSheets<SheetCwVoucher>(cmd, companyID, data.ids.ToString(), "", "", LOAD_PURPOSE.APPROVE);

            try
            {
                /************tran内判重************/
                foreach (SheetCwVoucher voucher in vouchers)
                {
                    string redisKey = $"voOperateDuplicate{companyID}-{voucher.sheet_id}";
                    string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                    await RedisHelper.ExpireAsync(redisKey, 300); //5分钟
                    redisKeys.Add(voucher.sheet_id, redisKey);
                    if (redisValue == "1")
                    {
                        msg = "点快啦，请勿重复操作凭证！";
                        break;
                    }
                }
                if (msg != "")
                {
                    await CwVoucherController.CwDeleteRedis(redisKeys, true);
                    return Json(new { result = "Error", msg = msg });
                }
                /************************************/

                tran = cmd.Connection.BeginTransaction();
                string voucher_ids = "";
                foreach (SheetCwVoucher voucher in vouchers)
                {
                    msg = await CwVoucherController.CheckVoTime(companyID, Convert.ToDateTime(voucher.happen_time), cmd);
                    if (!voucher.red_flag.IsInvalid()) msg += " 红冲凭证不能审核";
                    if (!voucher.approve_time.IsInvalid()) msg += " 已审核凭证不能再次审核";
                    if (msg != "") break;
                    voucher.OperID = operID;

                    msg = await voucher.SaveAndApprove(cmd, false);
                    if (msg != "") break;

                    voucher_ids += $"{voucher.sheet_id},";
                }
                voucher_ids = voucher_ids.TrimEnd(',');
                if (msg != "")
                {
                    tran.Rollback();
                    await CwVoucherController.CwDeleteRedis(redisKeys, false);//tran内判重延迟结束
                    return Json(new { result = "Error", msg });
                }
                else
                {
                    tran.Commit();
                    await CwVoucherController.CwDeleteRedis(redisKeys, true);//tran内判重立刻结束
                    await CwLog.Save(companyID, operID, null, "CwVoucherView", $"OK; {(msg == "" ? "" : msg + "; ")}BulkApprove; voCount: {vouchers.Count}; voIds: {voucher_ids};", cmd);
                    return Json(new { result, msg });
                }
            }
            catch (Exception ex)
            {
                result = "Error";
                msg = "批量审核凭证发生错误";
                if (tran != null) tran.Rollback();
                await CwVoucherController.CwDeleteRedis(redisKeys, false);//tran内判重延迟结束
                MyLogger.LogMsg($"In CwBulkApproveVouchers, ex_msg:{ex.Message},code:{ex.StackTrace},targetSite:{ex.TargetSite}", companyID);
                return Json(new { result, msg });
            }


        }

        [HttpPost]
        public async Task<JsonResult> BulkCancelApprove([FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            CMySbTransaction tran = null;
            Dictionary<string, string> redisKeys = new Dictionary<string, string>();
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            cmd.company_id = companyID;
            if (data.ids.ToString() == "") return Json(new { result = "Error", msg = "未选择凭证" });
            SheetCwVoucher vo = new SheetCwVoucher(LOAD_PURPOSE.APPROVE);
            List<SheetCwVoucher> vouchers = await vo.LoadMultiSheets<SheetCwVoucher>(cmd, companyID, data.ids.ToString(), "", "", LOAD_PURPOSE.APPROVE);

            try
            {
                /************tran内判重************/
                foreach (SheetCwVoucher voucher in vouchers)
                {
                    string redisKey = $"voOperateDuplicate{companyID}-{voucher.sheet_id}";
                    string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                    await RedisHelper.ExpireAsync(redisKey, 300); //5分钟
                    redisKeys.Add(voucher.sheet_id, redisKey);
                    if (redisValue == "1")
                    {
                        msg = "点快啦，请勿重复操作凭证！";
                        break;
                    }
                }
                if (msg != "")
                {
                    await CwVoucherController.CwDeleteRedis(redisKeys, true);
                    return Json(new { result = "Error", msg = msg });
                }
                /************************************/

                tran = cmd.Connection.BeginTransaction();
                string voucher_ids = "";
                foreach (SheetCwVoucher voucher in vouchers)
                {
                    msg = await CwVoucherController.CheckVoTime(companyID, Convert.ToDateTime(voucher.happen_time), cmd);
                    if (!voucher.red_flag.IsInvalid()) msg += " 红冲凭证不能取消审核";
                    if (voucher.approve_time.IsInvalid()) msg += " 未审核凭证不能取消审核";
                    if (msg != "") break;

                    msg = await voucher.CancelApprove(cmd, companyID, voucher.sheet_id, operID, false);
                    if (msg != "") break;

                    voucher_ids += $"{voucher.sheet_id},";
                }
                voucher_ids = voucher_ids.TrimEnd(',');
                if (msg != "")
                {
                    tran.Rollback();
                    await CwVoucherController.CwDeleteRedis(redisKeys, false);//tran内判重延迟结束
                    return Json(new { result = "Error", msg });
                }
                else
                {
                    tran.Commit();
                    await CwVoucherController.CwDeleteRedis(redisKeys, true);//tran内判重立刻结束
                    await CwLog.Save(companyID, operID, null, "CwVoucherView", $"OK; {(msg == "" ? "" : msg + "; ")}BulkCancelApprove; voCount: {vouchers.Count}; voIds: {voucher_ids};", cmd);
                    return Json(new { result, msg });
                }
            }
            catch (Exception ex)
            {
                result = "Error";
                msg = "批量取消审核凭证发生错误";
                if (tran != null) tran.Rollback();
                await CwVoucherController.CwDeleteRedis(redisKeys, false);//tran内判重延迟结束
                MyLogger.LogMsg($"In CwBulkCancelApproveVouchers, ex_msg:{ex.Message},code:{ex.StackTrace},targetSite:{ex.TargetSite}", companyID);
                return Json(new { result, msg });
            }

        }

        [HttpPost]
        public async Task<JsonResult> BulkRed([FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            CMySbTransaction tran = null;
            Dictionary<string, string> redisKeys = new Dictionary<string, string>();
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            cmd.company_id = companyID;
            cmd.ActiveDatabase = "";
            if (data.ids.ToString() == "") return Json(new { result = "Error", msg = "未选择凭证" });
            SheetCwVoucher vo = new SheetCwVoucher(MyCW.LOAD_PURPOSE.APPROVE);
            List<SheetCwVoucher> vouchers = await vo.LoadMultiSheets<SheetCwVoucher>(cmd, companyID, data.ids.ToString(), "", "", LOAD_PURPOSE.APPROVE);

            try
            {
                /************tran内判重************/
                foreach (SheetCwVoucher voucher in vouchers)
                {
                    string redisKey = $"voOperateDuplicate{companyID}-{voucher.sheet_id}";
                    string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                    await RedisHelper.ExpireAsync(redisKey, 300); //5分钟
                    redisKeys.Add(voucher.sheet_id, redisKey);
                    if (redisValue == "1")
                    {
                        msg = "点快啦，请勿重复操作凭证！";
                        break;
                    }
                }
                if (msg != "")
                {
                    await CwVoucherController.CwDeleteRedis(redisKeys, true);
                    return Json(new { result = "Error", msg = msg });
                }
                /************************************/

                tran = cmd.Connection.BeginTransaction();
                string voucher_ids = "";
                foreach (SheetCwVoucher voucher in vouchers)
                {
                    DateTime happenTime = Convert.ToDateTime(voucher.happen_time);
                    happenTime = happenTime.GetMonthStart();
                    msg = await CwVoucherController.CheckVoTime(companyID, happenTime, cmd);
                    if (!voucher.red_flag.IsInvalid()) msg += " 红冲凭证不能再次红冲";
                    if (voucher.approve_time.IsInvalid()) msg += " 未审核凭证不能红冲";
                    if (msg != "") break;

                    msg = await voucher.Red(cmd, companyID, voucher.sheet_id, operID, false);
                    if (msg != "") break;

                    voucher_ids += $"{voucher.sheet_id},";
                }
                voucher_ids = voucher_ids.TrimEnd(',');
                if (msg != "")
                {
                    tran.Rollback();
                    await CwVoucherController.CwDeleteRedis(redisKeys, false);//tran内判重延迟结束
                    return Json(new { result = "Error", msg });
                }
                else
                {
                    tran.Commit();
                    await CwVoucherController.CwDeleteRedis(redisKeys, true);//tran内判重立刻结束
                    await CwLog.Save(companyID, operID, null, "CwVoucherView", $"OK; {(msg == "" ? "" : msg + "; ")}BulkRed; voCount: {vouchers.Count}; voIds: {voucher_ids};", cmd);
                    return Json(new { result, msg });
                }
            }
            catch (Exception ex)
            {
                result = "Error";
                msg = "批量红冲凭证发生错误";
                if (tran != null) tran.Rollback();
                await CwVoucherController.CwDeleteRedis(redisKeys, false);//tran内判重延迟结束
                MyLogger.LogMsg($"In CwBulkRedVouchers, ex_msg:{ex.Message},code:{ex.StackTrace},targetSite:{ex.TargetSite}", companyID);
                return Json(new { result, msg });
            }

        }

        [HttpPost]
        public async Task<JsonResult> BulkDelete([FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            CMySbTransaction tran = null;
            cmd.ActiveDatabase = "";
            Dictionary<string, string> redisKeys = new Dictionary<string, string>();
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            cmd.company_id = companyID;
            cmd.oper_id = operID;
            if (data.ids.ToString() == "") return Json(new { result = "Error", msg = "未选择凭证" });
            SheetCwVoucher vo = new SheetCwVoucher(MyCW.LOAD_PURPOSE.APPROVE);
            List<SheetCwVoucher> vouchers = await vo.LoadMultiSheets<SheetCwVoucher>(cmd, companyID, data.ids.ToString(), "", "", LOAD_PURPOSE.APPROVE);

            try
            {
                /************tran内判重************/
                foreach (SheetCwVoucher voucher in vouchers)
                {
                    string redisKey = $"voOperateDuplicate{companyID}-{voucher.sheet_id}";
                    string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                    await RedisHelper.ExpireAsync(redisKey, 300); //5分钟
                    redisKeys.Add(voucher.sheet_id, redisKey);
                    if (redisValue == "1")
                    {
                        msg = "点快啦，请勿重复操作凭证！";
                        break;
                    }
                }
                if (msg != "")
                {
                    await CwVoucherController.CwDeleteRedis(redisKeys, true);
                    return Json(new { result = "Error", msg = msg });
                }
                /************************************/

                tran = cmd.Connection.BeginTransaction();
                string voucher_ids = "";
                foreach (SheetCwVoucher voucher in vouchers)
                {
                    msg = await CwVoucherController.CheckVoTime(companyID, Convert.ToDateTime(voucher.happen_time), cmd);
                    if (!voucher.red_flag.IsInvalid()) msg += " 红冲凭证不能删除";
                    if (!voucher.approve_time.IsInvalid()) msg += " 已审核凭证不能删除";
                    if (msg != "") break;

                    msg = await voucher.Delete(cmd, companyID, voucher.sheet_id, operID, false);
                    if (msg != "") break;

                    voucher_ids += $"{voucher.sheet_id},";
                }
                voucher_ids = voucher_ids.TrimEnd(',');
                if (msg != "")
                {
                    tran.Rollback();
                    await CwVoucherController.CwDeleteRedis(redisKeys, false);//tran内判重延迟结束
                    return Json(new { result = "Error", msg });
                }
                else
                {
                    tran.Commit();
                    await CwVoucherController.CwDeleteRedis(redisKeys, true);//tran内判重立刻结束
                    await CwLog.Save(companyID, operID, null, "CwVoucherView", $"OK; {(msg == "" ? "" : msg + "; ")}BulkDelete; voCount: {vouchers.Count}; voIds: {voucher_ids};", cmd);
                    return Json(new { result, msg });
                }
            }
            catch (Exception ex)
            {
                result = "Error";
                msg = "批量删除凭证发生错误";
                if (tran != null) tran.Rollback();
                await CwVoucherController.CwDeleteRedis(redisKeys, false);//tran内判重延迟结束
                MyLogger.LogMsg($"In CwBulkDeleteVouchers, ex_msg:{ex.Message},code:{ex.StackTrace},targetSite:{ex.TargetSite}", companyID);
                return Json(new { result, msg });
            }

        }

        [HttpGet]
        public async Task<JsonResult> GetMultiSheetsToPrint(string operKey, string sheetIDs, bool bPrintSum, bool bPrintReturnSum, bool bPrintEach, bool bPrintSheetsMainInfo, bool smallUnitBarcode, string clientVersion, string sortColumn, string sortDirection)
        {
            SheetCwVoucher.GetSheetsUsage usage = new SheetCwVoucher.GetSheetsUsage();
            usage.ForPrint = true;
            usage.GetEachSheet = bPrintEach;


            SheetCwVoucher.GetSheetsResult<SheetCwVoucher> res = await SheetCwVoucher.GetItemSheets(cmd, operKey, sheetIDs, usage);
            return new JsonResult(res);
            //return new JsonResult(new {result=res.result,msg=res.msg,printSheets=res.sheetGroup,sheetGroup=res.sheetGroup});
            // return await GetMultiSheetsToPrint_Inner<SheetSale,SheetRowSale>(cmd,operKey, sheetIDs, bPrintSum, bPrintEach, smallUnitBarcode, clientVersion, sortColumn, sortDirection);
        }
        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            CwVoucherViewModel model = new CwVoucherViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

        [HttpGet]
        public async Task<JsonResult> GetCwPeriodsForResort(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            List<string> periods = new List<string>();
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select coalesce(setting ->>  'useAccounting','false') useaccounting, coalesce(setting ->>  'accountingPeriod','') accountingperiod from company_setting where company_id = {companyID}", cmd);
            if (setting == null || !Convert.ToBoolean(setting.useaccounting))
            {
                return Json(new { result = "Error", msg = "请先开账" });
            }
            periods.Add(setting.accountingperiod.ToString() + "-01");
            dynamic cvm = await CDbDealer.Get1RecordFromSQLAsync($"select max(period) as period from cw_voucher_main where company_id={companyID}", cmd);
            if (cvm != null)
            {
                DateTime period = Convert.ToDateTime(periods[0]);
                while (period < Convert.ToDateTime(cvm.period))
                {
                    period = period.AddMonths(1);
                    periods.Add(period.ToString("yyyy-MM-dd"));
                }
                ;
            }
            return Json(new { result = "OK", msg = "", periods });
        }

        [HttpPost]
        public async Task<JsonResult> ResortSheetNo([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select coalesce(setting ->> 'useAccounting','false') useaccounting, coalesce(setting ->> 'accountingPeriod','') accountingperiod from company_setting where company_id = {companyID}", cmd);

            if (setting == null || !Convert.ToBoolean(setting.useaccounting))
            {
                return Json(new { result = "Error", msg = "请先开账" });
            }

            // 整理方式
            DateTime periodOp = Convert.ToDateTime(data.period);
            DateTime periodEd = periodOp.AddMonths(1).AddSeconds(-1);
            string orderSql = (data.type == 1 || data.type == 3) ? "order by sheet_no" : "order by happen_time";
            string redSql = (data.type == 3 || data.type == 4) ? "and red_flag is null" : "";

            string sql = "";
            int sheet_no = 1;
            List<string> updateQueries = new List<string>();

            CMySbTransaction tran = cmd.Connection.BeginTransaction();

            // 获取主表数据
            dynamic cvm = await CDbDealer.GetRecordsFromSQLAsync($"select sheet_no, sheet_id, red_sheet_id, make_brief from cw_voucher_main where company_id={companyID} and happen_time between '{periodOp}' and '{periodEd}' {redSql} {orderSql};", cmd);

            // **第一步：先执行更新 sheet_no**
            foreach (dynamic row in cvm)
            {
                updateQueries.Add($"update cw_voucher_main set sheet_no={sheet_no} where company_id={companyID} and sheet_id={row.sheet_id};");
                sheet_no++;
            }

            // 执行批量更新 sheet_no
            cmd.CommandText = string.Join(" ", updateQueries);
            await cmd.ExecuteNonQueryAsync();

            // 清空更新缓存，准备处理 make_brief 和 remark
            updateQueries.Clear();

            // 处理 red_sheet_id 和 make_brief
            foreach (dynamic row in cvm)
            {
                bool hasRedSheetId = row.red_sheet_id != null && !string.IsNullOrEmpty(row.red_sheet_id.ToString());
                //再次遍历主表，找出红
                if (hasRedSheetId)
                {
                    //找到红单的主表
                    string redSheetId = row.red_sheet_id?.ToString();
                    dynamic matchingRow = await CDbDealer.Get1RecordFromSQLAsync($"select sheet_no from cw_voucher_main where company_id={companyID} and sheet_id='{redSheetId}';", cmd);

                    if (matchingRow != null)
                    {
                        string matchedSheetNo = matchingRow.sheet_no.ToString();
                        //找到与当前红单对应的黑单的详单，remark就是这个详单里的内容
                        dynamic cvd = await CDbDealer.GetRecordsFromSQLAsync($"select sheet_id, remark from cw_voucher_detail where company_id={companyID} and sheet_id={row.sheet_id};", cmd);

                        foreach (dynamic detailRow in cvd)
                        {
                            string originalRemark = detailRow.remark?.ToString();
                            string newRemark = $"记-{matchedSheetNo}";
                            string secondRemark = newRemark;

                            if (System.Text.RegularExpressions.Regex.IsMatch(originalRemark, @"记-\d+"))
                            {
                                newRemark = System.Text.RegularExpressions.Regex.Replace(originalRemark, @"记-\d+", $"记-{matchedSheetNo}");
                                secondRemark = System.Text.RegularExpressions.Regex.Replace(newRemark, @"(记-\d+).*", "$1");
                            }

                            // 批量更新 remark 和 make_brief
                            updateQueries.Add($"update cw_voucher_detail set remark='{newRemark}' where company_id={companyID} and sheet_id={detailRow.sheet_id};");
                            updateQueries.Add($"update cw_voucher_main set make_brief='{secondRemark}' where company_id={companyID} and sheet_id={detailRow.sheet_id};");
                        }
                    }
                }
            }

            // 执行批量更新 remark 和 make_brief
            if (updateQueries.Count > 0)
            {
                cmd.CommandText = string.Join(" ", updateQueries);
                await cmd.ExecuteNonQueryAsync();
            }

            if (data.type == 3 || data.type == 4)
            {
                // 获取红冲凭证相关数据
                cvm = await CDbDealer.GetRecordsFromSQLAsync($"select sheet_no, sheet_id, red_sheet_id from cw_voucher_main where company_id={companyID} and happen_time between '{periodOp}' and '{periodEd}' and red_flag is not null {orderSql};", cmd);

                // **第一步：先执行更新 sheet_no**
                foreach (dynamic row in cvm)
                {
                    updateQueries.Add($"update cw_voucher_main set sheet_no={sheet_no} where company_id={companyID} and sheet_id={row.sheet_id};");
                    sheet_no++;
                }

                // 执行批量更新 sheet_no
                cmd.CommandText = string.Join(" ", updateQueries);
                await cmd.ExecuteNonQueryAsync();

                // 清空更新缓存，准备处理 red_sheet_id 和 make_brief
                updateQueries.Clear();

                // 处理 red_sheet_id 和 make_brief
                foreach (dynamic row in cvm)
                {
                    bool hasRedSheetId = row.red_sheet_id != null && !string.IsNullOrEmpty(row.red_sheet_id.ToString());
                    if (hasRedSheetId)
                    {
                        string redSheetId = row.red_sheet_id?.ToString();
                        dynamic matchingRow = await CDbDealer.Get1RecordFromSQLAsync($"select sheet_no from cw_voucher_main where company_id={companyID} and sheet_id='{redSheetId}';", cmd);

                        if (matchingRow != null)
                        {
                            string matchedSheetNo = matchingRow.sheet_no.ToString();
                            dynamic cvd = await CDbDealer.GetRecordsFromSQLAsync($"select sheet_id, remark from cw_voucher_detail where company_id={companyID} and sheet_id={row.sheet_id};", cmd);

                            foreach (dynamic detailRow in cvd)
                            {
                                string originalRemark = detailRow.remark?.ToString();
                                string newRemark = $"记-{matchedSheetNo}";
                                string secondRemark = newRemark;

                                if (System.Text.RegularExpressions.Regex.IsMatch(originalRemark, @"记-\d+"))
                                {
                                    newRemark = System.Text.RegularExpressions.Regex.Replace(originalRemark, @"记-\d+", $"记-{matchedSheetNo}");
                                    secondRemark = System.Text.RegularExpressions.Regex.Replace(newRemark, @"(记-\d+).*", "$1");
                                }

                                // 批量更新 remark 和 make_brief
                                updateQueries.Add($"update cw_voucher_detail set remark='{newRemark}' where company_id={companyID} and sheet_id={detailRow.sheet_id};");
                                updateQueries.Add($"update cw_voucher_main set make_brief='{secondRemark}' where company_id={companyID} and sheet_id={detailRow.sheet_id};");
                            }
                        }
                    }
                }

                // 执行批量更新 remark 和 make_brief
                if (updateQueries.Count > 0)
                {
                    cmd.CommandText = string.Join(" ", updateQueries);
                    await cmd.ExecuteNonQueryAsync();
                }
            }

            // 更新下一个凭证号
            cmd.CommandText = $"update cw_voucher_no set next_voucher_no={sheet_no} where company_id={companyID} and period='{periodOp}';";
            await cmd.ExecuteNonQueryAsync();
            tran.Commit();

            // 保存日志
            await CwLog.Save(companyID, operID, null, "CwVoucherView", $"OK; resort voucher in {orderSql} from 1 to {sheet_no - 1} in period {data.period}; type: {data.type}; ", cmd);

            return Json(new { result = "OK", msg = "", periodOp, periodEd });
        }

        //public async Task<JsonResult> ResortSheetNo([FromBody] dynamic data)
        //{
        //    Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
        //    dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select coalesce(setting ->>  'useAccounting','false') useaccounting, coalesce(setting ->>  'accountingPeriod','') accountingperiod from company_setting where company_id = {companyID}", cmd);
        //    if (setting == null || !Convert.ToBoolean(setting.useaccounting))
        //    {
        //        return Json(new { result = "Error", msg = "请先开账" });
        //    }

        //    DateTime periodOp = Convert.ToDateTime(data.period);
        //    DateTime periodEd = periodOp.AddMonths(1).AddSeconds(-1);
        //    string orderSql = (data.type == 1 || data.type == 3) ? "order by sheet_no" : "order by happen_time";
        //    string redSql = (data.type == 3 || data.type == 4) ? "and red_flag is null" : "";

        //    string sql = "";
        //    int sheet_no = 1;

        //    // 主体逻辑：处理非红冲凭证
        //    dynamic cvm = await CDbDealer.GetRecordsFromSQLAsync($"select sheet_no, sheet_id from cw_voucher_main where company_id={companyID} and happen_time between '{periodOp}' and '{periodEd}' {redSql} {orderSql};", cmd);
        //    foreach (dynamic row in cvm)
        //    {
        //        sql += $"update cw_voucher_main set sheet_no={sheet_no} where company_id={companyID} and sheet_id={row.sheet_id};";
        //        sheet_no++;
        //    }

        //    // 如果是类型 3 或 4，需要单独处理红冲凭证
        //    if (data.type == 3 || data.type == 4)
        //    {
        //        cvm = await CDbDealer.GetRecordsFromSQLAsync($"select sheet_no, sheet_id from cw_voucher_main where company_id={companyID} and happen_time between '{periodOp}' and '{periodEd}' and red_flag is not null {orderSql};", cmd);
        //        foreach (dynamic row in cvm)
        //        {
        //            sql += $"update cw_voucher_main set sheet_no={sheet_no} where company_id={companyID} and sheet_id={row.sheet_id};";
        //            sheet_no++;
        //        }
        //    }

        //    // 更新下一个凭证号
        //    sql += $"update cw_voucher_no set next_voucher_no={sheet_no} where company_id={companyID} and period='{periodOp}';";

        //    // 执行 SQL
        //    CMySbTransaction tran = cmd.Connection.BeginTransaction();
        //    cmd.CommandText = sql;
        //    await cmd.ExecuteNonQueryAsync();
        //    tran.Commit();

        //    // 保存日志
        //    await CwLog.Save(companyID, operID, null, "CwVoucherView", $"OK; resort voucher in {orderSql} from 1 to {sheet_no - 1} in period {data.period}; type: {data.type}; ", cmd);

        //    return Json(new { result = "OK", msg = "", periodOp, periodEd });
        //}

        [HttpPost]
        public async Task<JsonResult> ImportVouchers(string operKey)
        {
            try
            {
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

                if (Request.Form.Files.Count == 0)
                {
                    return Json(new { result = "Error", msg = "未上传文件" });
                }

                var file = Request.Form.Files[0];
                if (file.Length == 0)
                {
                    return Json(new { result = "Error", msg = "文件为空" });
                }

                // 读取Excel文件
                List<Dictionary<string, object>> voucherList = new List<Dictionary<string, object>>();
                using (var stream = file.OpenReadStream())
                {
                    IWorkbook workbook = new XSSFWorkbook(stream);
                    ISheet sheet = workbook.GetSheetAt(0);

                    // 获取表头
                    IRow headerRow = sheet.GetRow(0);
                    List<string> headers = new List<string>();
                    for (int i = 0; i < headerRow.LastCellNum; i++)
                    {
                        headers.Add(headerRow.GetCell(i)?.StringCellValue ?? "");
                    }

                    // 读取数据行
                    for (int i = 1; i <= sheet.LastRowNum; i++)
                    {
                        IRow row = sheet.GetRow(i);
                        if (row == null) continue;

                        Dictionary<string, object> voucher = new Dictionary<string, object>();
                        bool hasData = false;

                        for (int j = 0; j < headers.Count; j++)
                        {
                            ICell cell = row.GetCell(j);
                            if (cell != null)
                            {
                                string value = "";
                                switch (cell.CellType)
                                {
                                    case CellType.Numeric:
                                        if (DateUtil.IsCellDateFormatted(cell))
                                            value = cell.DateCellValue.ToString();
                                        else
                                            value = cell.NumericCellValue.ToString();
                                        break;
                                    case CellType.String:
                                        value = cell.StringCellValue;
                                        break;
                                    case CellType.Boolean:
                                        value = cell.BooleanCellValue.ToString();
                                        break;
                                    case CellType.Formula:
                                        value = cell.CellFormula;
                                        break;
                                    default:
                                        value = "";
                                        break;
                                }

                                if (!string.IsNullOrEmpty(value))
                                    hasData = true;

                                voucher[headers[j]] = value;
                            }
                        }

                        if (hasData)
                            voucherList.Add(voucher);
                    }
                }

                if (voucherList.Count == 0)
                {
                    return Json(new { result = "Error", msg = "导入的文件没有数据" });
                }

                // 导入凭证数据
                string result = await ImportVouchersToDatabase(voucherList, companyID, operID);

                if (string.IsNullOrEmpty(result))
                {
                    return Json(new { result = "OK", msg = "导入成功" });
                }
                else
                {
                    // 检查是否只包含警告信息（不包含错误）
                    if (result.StartsWith("警告信息（不影响导入）:"))
                    {
                        // 只有警告信息，导入成功
                        return Json(new { result = "OK", msg = "导入成功\n\n" + result });
                    }
                    else if (result.Contains("警告信息（不影响导入）:"))
                    {
                        // 包含错误和警告信息
                        return Json(new { result = "Error", msg = result });
                    }
                    else
                    {
                        // 只有错误信息
                        return Json(new { result = "Error", msg = result });
                    }
                }
            }
            catch (Exception ex)
            {
                return Json(new { result = "Error", msg = "导入失败: " + ex.Message });
            }
        }

        private async Task<string> ImportVouchersToDatabase(List<Dictionary<string, object>> voucherList, string companyID, string operID)
        {
            string errorMsg = "";
            List<string> warningMessages = new List<string>(); // 收集警告信息
            CMySbTransaction tran = null;
            CMySbCommand cmd = null;

            try
            {
                // 创建数据库连接和命令
                cmd = new CMySbCommand();
                cmd.Connection.Open();

                // 开始事务
                tran = cmd.Connection.BeginTransaction();
                cmd.Transaction = tran;

                // 获取系统中现有的分区信息
                Dictionary<DateTime, DateTime> partitionRanges = new Dictionary<DateTime, DateTime>();
                cmd.CommandText = @"
                    SELECT 
                        c.relname as partition_name,
                        pg_get_expr(c.relpartbound, c.oid) as partition_bound
                    FROM pg_class c
                    JOIN pg_namespace n ON n.oid = c.relnamespace
                    WHERE c.relname LIKE 'cw_voucher_detail_%'
                    AND n.nspname = 'public'
                    AND c.relispartition = 't'";

                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        string partitionName = reader["partition_name"].ToString();
                        string partitionBound = reader["partition_bound"].ToString();

                        // 解析分区范围
                        // 格式通常是: FOR VALUES FROM ('2024-01-01') TO ('2024-06-01')
                        var match = System.Text.RegularExpressions.Regex.Match(
                            partitionBound,
                            @"FROM \('([^']+)'\) TO \('([^']+)'\)"
                        );

                        if (match.Success && match.Groups.Count >= 3)
                        {
                            DateTime startDate = DateTime.Parse(match.Groups[1].Value);
                            DateTime endDate = DateTime.Parse(match.Groups[2].Value);
                            partitionRanges[startDate] = endDate;
                        }
                    }
                }

                // 首先检查凭证日期是否在有效范围内
                foreach (var voucher in voucherList)
                {
                    if (!voucher.ContainsKey("凭证日期") || string.IsNullOrEmpty(voucher["凭证日期"]?.ToString()))
                    {
                        errorMsg += "凭证日期不能为空;";
                        continue;
                    }

                    DateTime voucherDate;
                    if (!DateTime.TryParse(voucher["凭证日期"]?.ToString(), out voucherDate))
                    {
                        errorMsg += $"凭证日期格式错误: {voucher["凭证日期"]};";
                        continue;
                    }

                    // 检查凭证日期是否在任何分区范围内
                    bool inRange = false;
                    foreach (var range in partitionRanges)
                    {
                        if (voucherDate >= range.Key && voucherDate < range.Value)
                        {
                            inRange = true;
                            break;
                        }
                    }

                    if (!inRange && partitionRanges.Count > 0)
                    {
                        errorMsg += $"凭证日期 {voucherDate:yyyy-MM-dd} 不在任何可用分区范围内，请联系系统管理员;";

                        // 提供可用的分区范围信息
                        if (partitionRanges.Count > 0)
                        {
                            var minDate = partitionRanges.Keys.Min();
                            var maxDate = partitionRanges.Values.Max();
                            errorMsg += $" 当前系统支持的日期范围是 {minDate:yyyy-MM-dd} 至 {maxDate:yyyy-MM-dd};";
                        }
                        continue;
                    }
                }

                // 如果有错误，直接返回
                if (!string.IsNullOrEmpty(errorMsg))
                {
                    return errorMsg;
                }

                // 按凭证号分组处理
                var voucherGroups = voucherList.GroupBy(v => v.ContainsKey("凭证号") ? v["凭证号"]?.ToString() ?? "" : "").ToList();

                foreach (var group in voucherGroups)
                {
                    string groupVoucherNo = group.Key;
                    var groupVouchers = group.ToList();

                    // 验证分组中的所有记录
                    bool groupValid = true;
                    DateTime? groupVoucherDate = null;
                    string groupRemark = "";
                    decimal totalDebitAmount = 0;
                    decimal totalCreditAmount = 0;

                    // 验证分组中的每条记录
                    foreach (var voucher in groupVouchers)
                    {
                        // 验证必填字段
                        if (!voucher.ContainsKey("摘要") || string.IsNullOrEmpty(voucher["摘要"]?.ToString()))
                        {
                            errorMsg += $"凭证号{groupVoucherNo}：摘要不能为空;";
                            groupValid = false;
                            continue;
                        }

                        if (!voucher.ContainsKey("科目名称") || string.IsNullOrEmpty(voucher["科目名称"]?.ToString()))
                        {
                            errorMsg += $"凭证号{groupVoucherNo}：科目名称不能为空;";
                            groupValid = false;
                            continue;
                        }

                        // 解析日期
                        if (!voucher.ContainsKey("凭证日期") || string.IsNullOrEmpty(voucher["凭证日期"]?.ToString()))
                        {
                            errorMsg += $"凭证号{groupVoucherNo}：凭证日期不能为空;";
                            groupValid = false;
                            continue;
                        }

                        DateTime voucherDate;
                        if (!DateTime.TryParse(voucher["凭证日期"].ToString(), out voucherDate))
                        {
                            errorMsg += $"凭证号{groupVoucherNo}：凭证日期格式错误;";
                            groupValid = false;
                            continue;
                        }

                        // 设置分组的日期和摘要（使用第一条记录的）
                        if (groupVoucherDate == null)
                        {
                            groupVoucherDate = voucherDate;
                            groupRemark = voucher["摘要"]?.ToString();
                        }
                    }

                    if (!groupValid)
                    {
                        continue; // 跳过无效的分组
                    }

                    // 验证和处理分组中的每条明细记录
                    List<dynamic> validDetails = new List<dynamic>();

                    foreach (var voucher in groupVouchers)
                    {
                        // 解析借方金额和贷方金额
                        decimal debitAmount = 0;
                        decimal creditAmount = 0;

                        // 解析借方金额
                        if (voucher.ContainsKey("借方金额") && !string.IsNullOrEmpty(voucher["借方金额"]?.ToString()))
                        {
                            if (!decimal.TryParse(voucher["借方金额"]?.ToString(), out debitAmount))
                            {
                                errorMsg += $"凭证号{groupVoucherNo}：借方金额格式错误: {voucher["借方金额"]};";
                                groupValid = false;
                                continue;
                            }
                        }

                        // 解析贷方金额
                        if (voucher.ContainsKey("贷方金额") && !string.IsNullOrEmpty(voucher["贷方金额"]?.ToString()))
                        {
                            if (!decimal.TryParse(voucher["贷方金额"]?.ToString(), out creditAmount))
                            {
                                errorMsg += $"凭证号{groupVoucherNo}：贷方金额格式错误: {voucher["贷方金额"]};";
                                groupValid = false;
                                continue;
                            }
                        }

                        // 验证金额逻辑
                        if (debitAmount == 0 && creditAmount == 0)
                        {
                            errorMsg += $"凭证号{groupVoucherNo}：借方金额和贷方金额不能同时为0;";
                            groupValid = false;
                            continue;
                        }

                        if (debitAmount > 0 && creditAmount > 0)
                        {
                            errorMsg += $"凭证号{groupVoucherNo}：借方金额和贷方金额不能同时有值;";
                            groupValid = false;
                            continue;
                        }

                        // 获取科目名称
                        string subjectName = voucher["科目名称"]?.ToString();

                        // 获取科目ID
                        cmd.CommandText = $"SELECT sub_id FROM cw_subject WHERE company_id = {companyID} AND sub_name = '{subjectName.Replace("'", "''")}'";
                        object subjectId = await cmd.ExecuteScalarAsync();

                        if (subjectId == null || subjectId == DBNull.Value)
                        {
                            errorMsg += $"凭证号{groupVoucherNo}：科目名称不存在: {subjectName};";
                            groupValid = false;
                            continue;
                        }

                        // 处理辅助科目
                        string assister1Type = null;
                        int? assister1Id = null;
                        string assister2Type = null;
                        int? assister2Id = null;
                        List<string> assisterWarnings = new List<string>();

                        if (voucher.ContainsKey("辅助科目") && !string.IsNullOrEmpty(voucher["辅助科目"]?.ToString()))
                        {
                            string assisterString = voucher["辅助科目"]?.ToString();
                            var assisterResults = await ParseAssisterString(assisterString, companyID, cmd);

                            if (assisterResults.Count > 0)
                            {
                                // 第一个辅助科目
                                if (assisterResults.Count >= 1)
                                {
                                    assister1Type = assisterResults[0].type;
                                    assister1Id = assisterResults[0].id;
                                }

                                // 第二个辅助科目
                                if (assisterResults.Count >= 2)
                                {
                                    assister2Type = assisterResults[1].type;
                                    assister2Id = assisterResults[1].id;
                                }

                                // 如果有超过2个辅助科目，记录警告
                                if (assisterResults.Count > 2)
                                {
                                    assisterWarnings.Add($"检测到{assisterResults.Count}个辅助科目，只导入前2个");
                                }
                            }

                            // 检查是否有解析失败的部分
                            string[] parts = assisterString.Split('_');
                            if (parts.Length > assisterResults.Count)
                            {
                                assisterWarnings.Add($"部分辅助科目解析失败: {assisterString}");
                            }

                            // 添加警告信息
                            foreach (string warning in assisterWarnings)
                            {
                                warningMessages.Add($"凭证号{groupVoucherNo}: {warning}");
                            }
                        }

                        // 累计借贷金额
                        totalDebitAmount += debitAmount;
                        totalCreditAmount += creditAmount;

                        // 创建明细记录对象
                        validDetails.Add(new
                        {
                            SubjectId = subjectId,
                            SubjectName = subjectName,
                            DebitAmount = debitAmount,
                            CreditAmount = creditAmount,
                            Remark = voucher["摘要"]?.ToString(),
                            Assister1Type = assister1Type,
                            Assister1Id = assister1Id,
                            Assister2Type = assister2Type,
                            Assister2Id = assister2Id
                        });
                    }

                    // 检查分组是否有效
                    if (!groupValid || validDetails.Count == 0)
                    {
                        continue; // 跳过无效的分组
                    }

                    // 生成凭证号
                    string sql = $"select yj_getnewvoucherno({companyID},'{groupVoucherDate.Value:yyyy-MM-dd}')";
                    cmd.CommandText = sql;
                    object ov = await cmd.ExecuteScalarAsync();
                    string sheet_no = ov?.ToString() ?? "";

                    // 创建凭证主表
                    CDbDealer dbDeal = new CDbDealer();
                    dbDeal.AddField("company_id", companyID);
                    dbDeal.AddField("sheet_no", sheet_no);
                    dbDeal.AddField("period", groupVoucherDate.Value.ToString("yyyy-MM-dd"), "date");
                    dbDeal.AddField("happen_time", groupVoucherDate.Value.ToString("yyyy-MM-dd HH:mm:ss"), "timestamp");
                    dbDeal.AddField("make_brief", groupRemark);
                    dbDeal.AddField("maker_id", operID);
                    dbDeal.AddField("make_time", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "timestamp");

                    string insertMainSql = dbDeal.GetInsertSQL("cw_voucher_main") + " returning sheet_id;";
                    cmd.CommandText = insertMainSql;

                    // 获取凭证ID
                    object sheetId = await cmd.ExecuteScalarAsync();
                    if (sheetId == null || sheetId == DBNull.Value)
                    {
                        errorMsg += $"凭证号{groupVoucherNo}：创建凭证失败;";
                        continue;
                    }

                    // 创建明细记录
                    int rowIndex = 1;
                    foreach (var detail in validDetails)
                    {
                        dbDeal = new CDbDealer();
                        dbDeal.AddField("company_id", companyID);
                        dbDeal.AddField("sheet_id", sheetId.ToString());
                        dbDeal.AddField("sub_id", detail.SubjectId.ToString());
                        dbDeal.AddField("remark", detail.Remark);
                        dbDeal.AddField("debit_amount", detail.DebitAmount.ToString(CultureInfo.InvariantCulture), "number");
                        dbDeal.AddField("credit_amount", detail.CreditAmount.ToString(CultureInfo.InvariantCulture), "number");
                        dbDeal.AddField("row_index", rowIndex.ToString(), "number");
                        dbDeal.AddField("happen_time", groupVoucherDate.Value.ToString("yyyy-MM-dd HH:mm:ss"), "timestamp");

                        // 添加辅助科目字段
                        if (!string.IsNullOrEmpty(detail.Assister1Type) && detail.Assister1Id != null)
                        {
                            dbDeal.AddField("assister1_type", detail.Assister1Type);
                            dbDeal.AddField("assister1_id", detail.Assister1Id.ToString(), "number");
                        }

                        if (!string.IsNullOrEmpty(detail.Assister2Type) && detail.Assister2Id != null)
                        {
                            dbDeal.AddField("assister2_type", detail.Assister2Type);
                            dbDeal.AddField("assister2_id", detail.Assister2Id.ToString(), "number");
                        }

                        string insertDetailSql = dbDeal.GetInsertSQL("cw_voucher_detail");
                        cmd.CommandText = insertDetailSql;
                        await cmd.ExecuteNonQueryAsync();

                        rowIndex++;
                    }

                    // 更新凭证主表的借贷合计金额
                    cmd.CommandText = @"
                        SELECT column_name
                        FROM information_schema.columns
                        WHERE table_name = 'cw_voucher_main'
                        AND column_name IN ('debit_amount', 'credit_amount')";

                    List<string> existingColumns = new List<string>();
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            existingColumns.Add(reader["column_name"].ToString());
                        }
                    }

                    // 如果存在这些列，则更新凭证主表的借贷合计金额
                    if (existingColumns.Contains("debit_amount") && existingColumns.Contains("credit_amount"))
                    {
                        cmd.CommandText = $@"
                            UPDATE cw_voucher_main
                            SET debit_amount = {totalDebitAmount.ToString(CultureInfo.InvariantCulture)},
                                credit_amount = {totalCreditAmount.ToString(CultureInfo.InvariantCulture)}
                            WHERE sheet_id = {sheetId} AND company_id = {companyID}";
                        await cmd.ExecuteNonQueryAsync();
                    }
                    else
                    {
                        // 如果不存在这些列，则计算change_amount并更新
                        decimal changeAmount = totalDebitAmount - totalCreditAmount;

                        // 检查是否存在change_amount列
                        cmd.CommandText = @"
                            SELECT column_name
                            FROM information_schema.columns
                            WHERE table_name = 'cw_voucher_main'
                            AND column_name = 'change_amount'";

                        object changeAmountColumn = await cmd.ExecuteScalarAsync();
                        if (changeAmountColumn != null)
                        {
                            cmd.CommandText = $@"
                                UPDATE cw_voucher_main
                                SET change_amount = {changeAmount.ToString(CultureInfo.InvariantCulture)}
                                WHERE sheet_id = {sheetId} AND company_id = {companyID}";
                            await cmd.ExecuteNonQueryAsync();
                        }
                    }
                }


                // 提交事务
                tran.Commit();

                // 组合错误信息和警告信息
                string resultMsg = errorMsg;
                if (warningMessages.Count > 0)
                {
                    if (!string.IsNullOrEmpty(resultMsg))
                    {
                        resultMsg += "\n\n";
                    }
                    resultMsg += "警告信息（不影响导入）:\n" + string.Join("\n", warningMessages);
                }

                return resultMsg;
            }
            catch (Exception ex)
            {
                // 回滚事务
                if (tran != null) tran.Rollback();
                MyLogger.LogMsg($"ImportVouchersToDatabase error: {ex.Message}, {ex.StackTrace}", companyID);
                return "导入失败: " + ex.Message;
            }
            finally
            {
                // 关闭连接
                if (cmd?.Connection != null)
                {
                    cmd.Connection.Close();
                }
            }
        }

        /// <summary>
        /// 解析辅助科目字符串，支持格式：【商品】105g旺旺仙贝_【客户】李唐烟酒店
        /// </summary>
        /// <param name="assisterString">辅助科目字符串</param>
        /// <param name="companyID">公司ID</param>
        /// <param name="cmd">数据库命令对象</param>
        /// <returns>解析结果列表</returns>
        private async Task<List<(string type, int id, string originalText)>> ParseAssisterString(string assisterString, string companyID, CMySbCommand cmd)
        {
            var results = new List<(string type, int id, string originalText)>();

            if (string.IsNullOrEmpty(assisterString))
            {
                return results;
            }

            try
            {
                // 按_分割多个辅助科目
                string[] parts = assisterString.Split('_');

                foreach (string part in parts)
                {
                    if (string.IsNullOrEmpty(part.Trim()))
                        continue;

                    // 解析格式：【类型】名称
                    var match = System.Text.RegularExpressions.Regex.Match(part.Trim(), @"【(.+?)】(.+)");
                    if (match.Success && match.Groups.Count >= 3)
                    {
                        string typeText = match.Groups[1].Value.Trim();
                        string name = match.Groups[2].Value.Trim();

                        // 将中文类型转换为代码
                        string typeCode = ConvertChineseTypeToCode(typeText);
                        if (!string.IsNullOrEmpty(typeCode))
                        {
                            // 根据类型查询ID
                            int? id = await GetAssisterIdByType(typeCode, name, companyID, cmd);
                            if (id.HasValue)
                            {
                                results.Add((typeCode, id.Value, part.Trim()));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MyLogger.LogMsg($"ParseAssisterString error: {ex.Message}, String: {assisterString}", companyID);
            }

            return results;
        }

        /// <summary>
        /// 将中文类型名称转换为类型代码
        /// </summary>
        /// <param name="chineseType">中文类型名称</param>
        /// <returns>类型代码</returns>
        private string ConvertChineseTypeToCode(string chineseType)
        {
            switch (chineseType)
            {
                case "客户":
                case "供应商":
                    return "C";
                case "商品":
                case "存货":
                case "物料":
                    return "INV";
                case "部门":
                    return "DEP";
                case "业务员":
                case "员工":
                case "人员":
                    return "MAN";
                default:
                    return null;
            }
        }

        /// <summary>
        /// 根据类型和名称获取辅助科目ID
        /// </summary>
        /// <param name="typeCode">类型代码</param>
        /// <param name="name">名称</param>
        /// <param name="companyID">公司ID</param>
        /// <param name="cmd">数据库命令对象</param>
        /// <returns>辅助科目ID</returns>
        private async Task<int?> GetAssisterIdByType(string typeCode, string name, string companyID, CMySbCommand cmd)
        {
            try
            {
                string tableName = "";
                string idField = "";
                string nameField = "";

                switch (typeCode)
                {
                    case "C":
                        tableName = "info_supcust";
                        idField = "supcust_id";
                        nameField = "sup_name";
                        break;
                    case "INV":
                        tableName = "info_item_prop";
                        idField = "item_id";
                        nameField = "item_name";
                        break;
                    case "DEP":
                        tableName = "info_department";
                        idField = "depart_id";
                        nameField = "depart_name";
                        break;
                    case "MAN":
                        tableName = "info_operator";
                        idField = "oper_id";
                        nameField = "oper_name";
                        break;
                    default:
                        return null;
                }

                cmd.CommandText = $"SELECT {idField} FROM {tableName} WHERE company_id = {companyID} AND {nameField} = '{name.Replace("'", "''")}'";
                object result = await cmd.ExecuteScalarAsync();

                if (result != null && result != DBNull.Value)
                {
                    return Convert.ToInt32(result);
                }
            }
            catch (Exception ex)
            {
                MyLogger.LogMsg($"GetAssisterIdByType error: {ex.Message}, Type: {typeCode}, Name: {name}", companyID);
            }

            return null;
        }
    }
}
