﻿@page
@model ArtisanManage.Pages.CwPages.CashBankTransferSheetModel
@{
}
<!DOCTYPE html>
<html lang="en">
<head>
    <partial name="_SheetHead" model="Model.PartialViewModel" />
    <script>
        var OperDepartPath = '@Model.OperDepartPath';
        var JsonOperRights = '@Model.JsonOperRights';
        var JsonOperRightsOrig = '@Model.JsonOperRightsOrig';
        var OperID = '@Model.OperID';
    </script>
    <script type="text/javascript" src="~/Sheet/CashBankTransferSheet.js?v=@Model.PartialViewModel.Version"></script>
    <style></style>
</head>
<body class='default' style="overflow:hidden;">
    <div id="divTitle" style="text-align:center;height:45px;margin-top:5px;">
        <label id="lblSheetTitle" style="font-weight:500;font-size:25px;">@Html.Raw(Model.SheetTitle)</label>
        <img id="imgState" style="display:none;position:fixed;top:0px;left:calc(50% - 150px);" src="" />
        <div class="makeInfo" style="position:absolute;top:5px; right:0px;">
            <div><div><label>单号:</label></div> <div id="sheet_no"></div></div><br />
            <div><div><label>制单:</label></div> <div><span id="make_time"></span><span id="maker_name"></span></div></div>
            <div><div><label>审核:</label></div> <div><span id="approve_time"></span><span id="approver_name"></span></div></div>
        </div>
    </div>

    <div id="divHead" class="headtail" style="margin-bottom:10px;margin-top:0px;">
        <div style="float:none;height:0px; clear:both;"></div>
    </div>

    <div id="jqxgrid" style="margin-left:10px; position:static;width:100%;height:100%;border-bottom-color:#dedede;"></div>

    <div style="display:flex;">
        <div id="divTail" class="headtail" style="margin-bottom:0px;margin-top:10px;margin-left:30px;">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
    </div>
    <div id="divButtons" style="text-align:center;">
        <button id="btnSave" type="button" style="margin-right:40px;">保存</button>
        <button id="btnApprove" type="button" style="margin-right:40px;" class="main-button">审核</button>
        <button id="btnRedAndChange" type="button" style="margin-right:40px;">冲改</button>
        <button id="btnRed" type="button" disabled>红冲</button>
        <button id="btnCopy" type="button">复制</button>
        <button id="btnAdd" type="button">新增</button>
        <button id="btnDelete" type="button" disabled>删除</button>
        <button id="btnPrint" type="button" style="margin-right:40px;">打印</button>
        <button id="btnClose" type="button">关闭</button>
    </div>
</body>
</html>