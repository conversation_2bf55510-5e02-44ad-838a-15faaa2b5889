﻿@page
@model ArtisanManage.CwPages.Report.BalanceModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Balance</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>

    <script src="~/js/FileSaverVue.js"></script>
    <script src="~/js/Blob.js"></script>
    <script src="~/js/jszip.js"></script>
    <script src="~/js/xlsx.full.min.js"></script>
    <script src="~/js/xlsx-style.js"></script>
    <script src="~/js/Export2Excel.js?v=@Html.Raw(Model.Version)"></script>

    <style>
        * {
            font-family: "微软雅黑"
        }
        [v-cloak] {
            display: none;
        }

        body {
        }

        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;

        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #dddddd;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }

        #pages {
            width: 100%;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            /*background-color: #f9f9f9;*/
        }

        .pages_title {
            width: 100%;
            height:5vh;
            font-weight: 500;
            font-size: 25px;
            text-align: center;
            margin-top: 5px;
            padding-bottom:10px;
            display:block;
        }

        .pages_query {
            display: block;
            margin-left: 20px;
            margin-right:20px;
            flex-wrap: wrap;
            padding-bottom: 10px;
            height:5vh;
        }

        .query_item {
            display: flex;
            float:left;
            width:50%;
            margin-top: 3px;
        }

        .item_input {
            width: 149px;
            height: 100%;
            z-index: 0;
            position: relative;
            border-style: none none solid;
            border-bottom: 1px solid #c7c7c7;
            border-radius: 0px;
            margin-left: 10px;
        }

        .item_name {
            height: 20px;
            bottom: 1px;
            right: 1px;
            margin-bottom: 0px;
        }

         .pages_buttons {
            width:49%;
            height: 100%;
            float:right;
            display:flex;
            justify-content:end;
            align-items:center;
        }

        .pages_content {
            padding: 0 20px;
        }

        .level1 {
            font-weight: bold
        }
        .level2 {
            text-indent: 1em;
        }
        .level3 {
            text-indent: 2em;
        }

        .pages_tag{
            border:2px solid #f56c6c;
            color:#f56c6c;
            font-size:25px;
            position:absolute;
            padding:5px;
            padding-left:10px;
            padding-right:10px;
            left:75vw;
            top:30px;
            transform:rotate(7deg);
            border-radius:10px;
        }
        
        .pages_tag_reason{
            position:absolute;
            top:68px;
            left:250px;
            color:#f56c6c;
            font-size:18px;
        }

        .pages_content table thead th{
            background-color:#f0f0f5;
        }

        .pages_content table {
            width: 100%;
            height:85vh;
            border: 0;
            border-collapse: collapse;
            border: 1px solid #ebeef5;
        }

            .pages_content table tbody {
                display: block;
                overflow: auto;
                overflow-x: hidden;
                height: calc(100% - 41px);
            }
        @@media(max-height:700px) {
            .pages_content table tbody {
                display: block;
                overflow: auto;
                overflow-x: hidden;
                height: 460px;
            }
         }
        .pages_content table thead, .pages_content tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .pages_content table thead {
            width: calc( 100% - 1em );
        }

        /*.pages_content table thead th{
            background: #eeeeee;
            border:1px solid #fff;
        }*/

        .pages_content table thead th:nth-child(1), .pages_content table thead th:nth-child(5), .pages_content table tbody td:nth-child(1), .pages_content table tbody td:nth-child(5) {
            width: 20%
        }
        .pages_content table thead th:nth-child(2), .pages_content table thead th:nth-child(6), .pages_content table tbody td:nth-child(2), .pages_content table tbody td:nth-child(6) {
            width: 5%;
            text-align: center
        }
        .pages_content table thead th:nth-child(3), .pages_content table thead th:nth-child(7), .pages_content table tbody td:nth-child(3), .pages_content table tbody td:nth-child(7) {
            width: 12.5%
        }
        .pages_content table thead th:nth-child(4), .pages_content table thead th:nth-child(8), .pages_content table tbody td:nth-child(4), .pages_content table tbody td:nth-child(8) {
            width: 12.5%
        }
        .pages_content table tbody td:nth-child(3), .pages_content table tbody td:nth-child(7), .pages_content table tbody td:nth-child(4), .pages_content table tbody td:nth-child(8) {
            text-align: right;
        }
        @*高度*@
        .pages_content table thead th, .pages_content table tbody td {
            min-height: 40px;
            line-height: 40px;
        }
        @*边框*@
        .pages_content table thead th, .pages_content table tbody td {
            border-bottom: 1px solid #ebeef5;
            border-right: 2px solid #fff;
        }
            .pages_content table thead th:last-child {
                border-right: 0;
            }
        @*背景*@
        .pages_content table tbody tr:nth-child(odd) {
            background: #fafafa;
        }
        .pages_content table tbody tr:hover {
            background-color: #f5f7fa;
        }

        .pages_content table thead th, .pages_content table tbody td {
            padding: 0 15px;
        }

        .not_show_tag{
            display:none;
        }
    </style>

</head>
<body>
    <div id="root" v-cloak>
        <div id="pages" class="" ref="pages">
            <div class="pages_title">资产负债表</div>
            <div class="pages_query">
                <div class="query_item">
                    <label class="item_name"></label>
                    <el-date-picker v-model="queryDate"
                                    type="month"
                                    value-format="yyyy-MM"
                                    :disabled="disabledDatePicker"
                                    @@change="changeDate"
                                    placeholder="选择月"
                                    :picker-options="pickerOptions">
                    </el-date-picker>
                </div>
                <div class="pages_buttons">
                    <el-button type="info" plain :disabled="disabledExportBtn" v-on:click="exportBtn()" style="position:absolute;right:30px;">导出</el-button>
                </div>
            </div>
            <div class="pages_tag not_show_tag" ref="balance_tag"><span>不平</span></div>
            <div class="pages_tag_reason" ref="tag_reason"><span>{{reasons.thisReason}}</span></div>
            <div class="pages_content">
                <table>
                    <thead>
                        <tr>
                            <th>资产</th>
                            <th>行次</th>
                            <th>期末余额</th>
                            <th>年初余额</th>
                            <th>负债和所有者权益</th>
                            <th>行次</th>
                            <th>期末余额</th>
                            <th>年初余额</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="row in sofpData" :key="row.id">
                            <td :class="row.leftClass">{{row.leftName}}</td>
                            <td>{{row.leftRow==0?'':row.leftRow}}</td>
                            <td>{{row.leftRow==0?'':row.leftM}}</td>
                            <td>{{row.leftRow==0?'':row.leftY}}</td>
                            <td :class="row.rightClass">{{row.rightName==0?'':row.rightName}}</td>
                            <td>{{row.rightRow==0?'':row.rightRow}}</td>
                            <td>{{row.rightRow==0?'':row.rightM}}</td>
                            <td>{{row.rightRow==0?'':row.rightY}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script>
        var g_operKey = '@Model.OperKey';

        window.g_operRights =@Html.Raw(Model.JsonOperRightsOrig);
        function checkOperRight(vm){
            if(!window.g_operRights.cwReport){
                return false;
            }
            if (window.g_operRights.cwReport.balanceSheet && window.g_operRights.cwReport.balanceSheet.see) { 
                if(!window.g_operRights.cwReport.balanceSheet.export) vm.disabledExportBtn=true;
                return true;
            }else{
                return false;
            }
        }
    </script>
    <script>

        var vm = new Vue({
            el: '#root',
            data() {
                return {
                    sofpData:[
                        {id:'01',leftName:'流动资产',leftClass:'level1', leftRow:0, leftM:0, leftY:0, rightName:'流动负债', rightClass:'level1', rightRow:0, rightM:0, rightY:0},
                        {id:'02',leftName:'货币资金',leftClass:'level2', leftRow:1, leftM:0, leftY:0, rightName:'短期借款', rightClass:'level2', rightRow:31, rightM:0, rightY:0},
                        {id:'03',leftName:'短期投资',leftClass:'level2', leftRow:2, leftM:0, leftY:0, rightName:'应付票据', rightClass:'level2', rightRow:32, rightM:0, rightY:0},
                        {id:'04',leftName:'应收票据',leftClass:'level2', leftRow:3, leftM:0, leftY:0, rightName:'应付账款', rightClass:'level2', rightRow:33, rightM:0, rightY:0},
                        {id:'05',leftName:'应收账款',leftClass:'level2', leftRow:4, leftM:0, leftY:0, rightName:'预收账款', rightClass:'level2', rightRow:34, rightM:0, rightY:0},
                        {id:'06',leftName:'预付账款',leftClass:'level2', leftRow:5, leftM:0, leftY:0, rightName:'应付职工薪酬', rightClass:'level2', rightRow:35, rightM:0, rightY:0},
                        {id:'07',leftName:'应收股利',leftClass:'level2', leftRow:6, leftM:0, leftY:0, rightName:'应交税费', rightClass:'level2', rightRow:36, rightM:0, rightY:0},
                        {id:'08',leftName:'应收利息',leftClass:'level2', leftRow:7, leftM:0, leftY:0, rightName:'应付利息', rightClass:'level2', rightRow:37, rightM:0, rightY:0},
                        {id:'09',leftName:'其他应收款',leftClass:'level2', leftRow:8, leftM:0, leftY:0, rightName:'应付利润', rightClass:'level2', rightRow:38, rightM:0, rightY:0},
                        {id:'10',leftName:'存货',leftClass:'level2', leftRow:9, leftM:0, leftY:0, rightName:'其他应付款', rightClass:'level2', rightRow:39, rightM:0, rightY:0},
                        {id:'11',leftName:'其中：原材料',leftClass:'level3', leftRow:10, leftM:0, leftY:0, rightName:'', rightClass:'', rightRow:0, rightM:0, rightY:0},
                        {id:'12',leftName:'在产品',leftClass:'level3', leftRow:11, leftM:0, leftY:0, rightName:'', rightClass:'', rightRow:0, rightM:0, rightY:0},
                        {id:'13',leftName:'库存商品',leftClass:'level3', leftRow:12, leftM:0, leftY:0, rightName:'', rightClass:'', rightRow:0, rightM:0, rightY:0},
                        {id:'14',leftName:'周转材料',leftClass:'level3', leftRow:13, leftM:0, leftY:0, rightName:'', rightClass:'', rightRow:0, rightM:0, rightY:0},
                        {id:'15',leftName:'其他流动资产',leftClass:'level2', leftRow:14, leftM:0, leftY:0, rightName:'其他流动负债', rightClass:'level2', rightRow:40, rightM:0, rightY:0},
                        {id:'16',leftName:'流动资产合计',leftClass:'level1', leftRow:15, leftM:0, leftY:0, rightName:'流动负债合计', rightClass:'level1', rightRow:41, rightM:0, rightY:0},
                        {id:'17',leftName:'非流动资产',leftClass:'level1', leftRow:0, leftM:0, leftY:0, rightName:'非流动负债', rightClass:'level1', rightRow:0, rightM:0, rightY:0},
                        {id:'18',leftName:'长期债券投资',leftClass:'level2', leftRow:16, leftM:0, leftY:0, rightName:'长期借款', rightClass:'level2', rightRow:42, rightM:0, rightY:0},
                        {id:'19',leftName:'长期股权投资',leftClass:'level2', leftRow:17, leftM:0, leftY:0, rightName:'长期应付款', rightClass:'level2', rightRow:43, rightM:0, rightY:0},
                        {id:'20',leftName:'固定资产原价',leftClass:'level2', leftRow:18, leftM:0, leftY:0, rightName:'递延收益', rightClass:'level2', rightRow:44, rightM:0, rightY:0},
                        {id:'21',leftName:'减：累计折旧',leftClass:'level3', leftRow:19, leftM:0, leftY:0, rightName:'其他非流动负债', rightClass:'level2', rightRow:45, rightM:0, rightY:0},
                        {id:'22',leftName:'固定资产账面价值',leftClass:'level2', leftRow:20, leftM:0, leftY:0, rightName:'非流动负债合计', rightClass:'level2', rightRow:46, rightM:0, rightY:0},
                        {id:'23',leftName:'在建工程',leftClass:'level2', leftRow:21, leftM:0, leftY:0, rightName:'负债合计', rightClass:'level1', rightRow:47, rightM:0, rightY:0},
                        {id:'24',leftName:'工程物资',leftClass:'level2', leftRow:22, leftM:0, leftY:0, rightName:'', rightClass:'', rightRow:0, rightM:0, rightY:0},
                        {id:'25',leftName:'固定资产清理',leftClass:'level2', leftRow:23, leftM:0, leftY:0, rightName:'', rightClass:'', rightRow:0, rightM:0, rightY:0},
                        {id:'26',leftName:'生产性生物资产',leftClass:'level2', leftRow:24, leftM:0, leftY:0, rightName:'所有者权益（或股东权益）', rightClass:'level1', rightRow:0, rightM:0, rightY:0},
                        {id:'27',leftName:'无形资产',leftClass:'level2', leftRow:25, leftM:0, leftY:0, rightName:'实收资本（或股本）', rightClass:'level2', rightRow:48, rightM:0, rightY:0},
                        {id:'28',leftName:'开发支出',leftClass:'level2', leftRow:26, leftM:0, leftY:0, rightName:'资本公积', rightClass:'level2', rightRow:49, rightM:0, rightY:0},
                        {id:'29',leftName:'长期待摊费用',leftClass:'level2', leftRow:27, leftM:0, leftY:0, rightName:'盈余公积', rightClass:'level2', rightRow:50, rightM:0, rightY:0},
                        {id:'30',leftName:'其他非流动资产',leftClass:'level2', leftRow:28, leftM:0, leftY:0, rightName:'未分配利润', rightClass:'level2', rightRow:51, rightM:0, rightY:0},
                        {id:'31',leftName:'非流动资产合计',leftClass:'level1', leftRow:29, leftM:0, leftY:0, rightName:'所有者权益(或股东权益)合计', rightClass:'level1', rightRow:52, rightM:0, rightY:0},
                        {id:'32',leftName:'资产合计',leftClass:'level1', leftRow:30, leftM:0, leftY:0, rightName:'负债和所有者权益(或股东权益)', rightClass:'level1', rightRow:53, rightM:0, rightY:0},
                    ],
                    queryDate: new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)),
                    pickerOptions:{
                        disabledDate(time) {
                            return time.getTime() > Date.now();
                        }
                    },
                    reasons:{
                        thisReason:'',
                        reasonInit:'不平原因：期初余额不平衡',
                        reasonSofp:'不平原因：往期发生额不平衡',
                        reasonPnl:'不平原因：损益类科目还有余额，请继续结转损益'
                    },
                    companyName:'',
                    disabledDatePicker:false,
                    disabledExportBtn:false
                }
            },
            created(){
                if(!window.checkOperRight(this)){
                    this.disabledDatePicker=true;
                    this.disabledExportBtn=true;
                    return;
                }

                this.getData(this.queryDate+'-1');
            },
            mounted() {
            },
            methods: {
                getData(period) {
                    $.ajax({
                        url: '/api/Balance/GetData',
                        type: 'get',
                        data: {
                            operKey: g_operKey,
                            period: period
                        },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then((res)=> {
                        if (res.result === 'OK') {
                            console.log('get sofp balance', res);
                            this.companyName = res.companyName;
                            this.queryDate = res.pickPeriodOp;
                            this.pickerOptions = {
                                disabledDate(time) {
                                    return (time.getTime() < new Date(res.openPeriod) || time.getTime() > new Date(res.maxPeriod));
                                }
                            };
                            res.sofpReport.forEach(item => {
                                let row = this.sofpData.filter(sd => sd.id == item.id)[0];
                                row.leftM = item.leftM;
                                row.leftY = item.leftY;
                                row.rightM = item.rightM;
                                row.rightY = item.rightY;
                            });
                            let edRow = this.sofpData.filter(sd => sd.id == '32')[0];
                            //显示“不平”
                            let balance_tag = this.$refs.balance_tag;
                            if (edRow.leftM != edRow.rightM || edRow.leftY != edRow.rightY || res.pnl_bal != 0) {
                                balance_tag.classList.remove('not_show_tag');
                            } else {
                                balance_tag.classList.add('not_show_tag');
                            }
                            //显示不平原因
                            this.reasons.thisReason = '';
                            if (edRow.leftY != edRow.rightY) {
                                this.reasons.thisReason = this.reasons.reasonInit;
                            } else if (res.pnl_bal != 0) {
                                this.reasons.thisReason = this.reasons.reasonPnl;
                            } else if (edRow.leftM != edRow.rightM) {
                                this.reasons.thisReason = this.reasons.reasonSofp;
                            }

                        } else {
                            this.$message({ showClose: true, message: res.msg, type: 'warning', offset: 20, duration: 2500 });
                        }
                    });
                },
                changeDate(){
                    this.getData(this.queryDate+'-1');
                },
                exportBtn(){
                    let data=[
                        ['资产负债表'], 
                        [`公司名称：${this.companyName}`,null,null,null, `会计期间：${this.queryDate.substr(0,7)}`,null,null,'单位：元'],
                        ['资产', '行次', '期末余额', '年初余额', '负债和所有者权益', '行次', '期末余额', '年初余额'],
                    ];
                    this.sofpData.forEach(row=>{
                        let excelRow=[row.leftName, row.leftRow, row.leftM, row.leftY, row.rightName, row.rightRow, row.rightM, row.rightY];
                        data.push(excelRow);
                    });
                    let merges=['A1:H1','A2:D2','E2:F2'];
                    let bodyTitleName=[];                    
                    let  specialCellConfig=[
                        { 
                            "type": "s", 
                            "configObj": { 
                                "font": { "sz": 14, "bold": true },
                                "alignment": { "horizontal": "center" }
                            }, 
                            "controlScope": "col", 
                            "scope": [ 1, 1 ] 
                        },
                        { 
                            "type": "s", 
                            "configObj": { 
                                "border": { "top": { "style": "thin" }, "bottom": { "style": "thin" }, "left": { "style": "thin" }, "right": { "style": "thin" } },
                                "font": { "bold": true } 
                            }, 
                            "controlScope": "col", 
                            "scope": [ 3, 3 ] 
                        },
                        { 
                            "type": "s", 
                            "configObj": { "border": { "top": { "style": "thin" }, "bottom": { "style": "thin" }, "left": { "style": "thin" }, "right": { "style": "thin" } } }, 
                            "controlScope": "col", 
                            "scope": [ 4, data.length ] 
                        }, 
                    ];
                    window.webExportExcel(data,`资产负债表[${this.queryDate.substr(0,7)}]`, merges, bodyTitleName, specialCellConfig)
                }

            }
        })
    </script>


</body>
</html>