﻿@page
@model ArtisanManage.Pages.BaseInfo.PricePlanClassEditModel
@{
    Layout = null;
} 
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>PricePlanClassEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    
    <script type="text/javascript">
        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
             @Html.Raw(Model.m_showFormScript)
             @Html.Raw(Model.m_createGridScript)
        });
        function dealGridRowsOnLoad(rows){
            rows.forEach((row)=>{
                if(row.discount) row.discount = parseFloat(row.discount)*100

            })
            return rows
        }

        function dealFormData(formData) {
            for (var i = 0; i < formData.gridUnit.length; i++) {
                var row = formData.gridUnit[i];
                if(row.plan_id&&row.discount) {
                    row.discount = (parseFloat(row.discount)/100).toString()
                }
                formData.gridUnit[i] = row;
            }
        }

        function checkDataValid(formData){
            var msg = ''
            for (var i = 0; i < formData.gridUnit.length; i++) {
                var row = formData.gridUnit[i];
                if(row.plan_id&&!row.discount) {
                    msg = '第'+parseInt(i+1)+'行'+row.plan_name+'的折扣不能为空'
                    break;
                }
            }
            if(msg){
                bw.toast(msg,2000)
                return false
            }
            return true;
        }
    </script>
</head>
<body>
    <div id="divHead" class="headtail" style="width:500px;">
    </div>
    @*<div style="margin-top:10px; margin-left:40px;margin-bottom:10px;font-weight:bold;">价格方案设置</div>*@
    <div id="gridUnit" style="width:100%;margin-left:40px;margin-right:50px;height:50px;"> </div>
    <div style="text-align:center;margin-top:20px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;">保存</button>
        <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
    <div></div>
</body>
</html>
