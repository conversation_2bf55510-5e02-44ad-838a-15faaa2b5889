using System;
using System.Collections.Generic;
using System.Dynamic;
using Newtonsoft.Json;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.CwPages.Report
{
    public class BusinessProfitModel : PageQueryModel
    {
        public BusinessProfitModel() : base(Services.MenuId.businessProfit)
        {

        }
        public void OnGet(string operKey)
        {
            OperKey = operKey;
        }
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class BusinessProfitController : QueryController
    { 
        public BusinessProfitController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpGet]
        public async Task<JsonResult> GetSubjectBanlance(string operKey, string startTime, string endTime,string operId, string departPath)
        {
            if (!startTime.Contains(":")) startTime += " 00:00:00";
            if (!endTime.Contains(":")) endTime += " 23:59:59";
            SQLQueue QQ = new SQLQueue(cmd);
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            string costPrice = "cost_price_buy";
            string cost_amount_type = "buy_amount";//用于报损
            //string costType = "buy_price";

            string sql = "";
            sql = $"select setting from company_setting where company_id = '{companyID}'"; 
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (data != null)
            {
                var company_setting = JsonConvert.DeserializeObject(data.setting);
                var qq = company_setting.costPriceType;
                if (company_setting.costPriceType == "2")
                {
                    costPrice = "cost_price_avg";
                    cost_amount_type = "cost_amount_avg";
                   // costType = "cost_price_avg";
                }
                else if (company_setting.costPriceType == "3")
                {
                    costPrice = "cost_price_buy";
                    cost_amount_type = "buy_amount";
                   // costType = "buy_price";
                }
                else if (company_setting.costPriceType == "1")
                {
                    costPrice = "cost_price_prop";
                    cost_amount_type = "cost_amount_prop";
                    // costType = "buy_price";
                }
                else if (company_setting.costPriceType == "4")
                {//最近进价
                    costPrice = "cost_price_recent";
                    cost_amount_type = "cost_amount_recent";
                }
            }
            string sellerIdCondition = "";
            var getterIdCondition = "";
            string departPathCondition = "";
            string leftJoinGetter = $"LEFT JOIN (select oper_id,oper_name as seller_name, depart_path from info_operator where company_id= {companyID}) seller on m.getter_id = seller.oper_id";
            string leftJoinSeller = $"LEFT JOIN (select oper_id,oper_name as seller_name, depart_path from info_operator where company_id= {companyID}) seller on m.seller_id = seller.oper_id";
            if (operId != null)
            {
                sellerIdCondition = $"and seller_id= { operId }";
                getterIdCondition = $"and getter_id= { operId }";

            }
            if (departPath != null)
            {
                departPathCondition = $"and seller.depart_path = '{departPath}'";
            }

            sql =@$"
select 
round(sum(-inout_flag * sub_amount)::numeric, 2) AS income, 
round( SUM ( CASE WHEN sub_amount <> 0  THEN - inout_flag * quantity * unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 ) AS COST ,
round(sum  (case when sub_amount=0 and coalesce(remark,'') like '%赠品%' and coalesce(trade_type,'X')<>'CL' then - inout_flag * quantity * unit_factor * {costPrice} else 0  end )::numeric,2) as free_cost_gift,
round(sum  (case when sub_amount=0 and coalesce(remark,'') like '%兑奖%' and coalesce(remark,'') not like '%赠品%' and coalesce(trade_type,'X')<>'CL' then - inout_flag * quantity * unit_factor * {costPrice} else 0  end )::numeric,2) as free_cost_change,
round(sum  (case when sub_amount=0 and ((coalesce(remark,'') like '%陈列%' and coalesce(remark,'') not like '%赠品%' and coalesce(remark,'') not like '%兑奖%') or coalesce(trade_type,'X')='CL') then - inout_flag * quantity * unit_factor * {costPrice} else 0  end )::numeric,2) as free_cost_display,
round(sum  (case when sub_amount=0 and ((coalesce(remark,'') not like '%赠品%' and coalesce(remark,'') not like '%兑奖%' and coalesce(remark,'') not like '%陈列%') or remark is null) and coalesce(trade_type,'X')<>'CL'   then - inout_flag * quantity * unit_factor * {costPrice} else 0  end )::numeric,2) as free_cost_other,
round(sum  (case when sub_amount=0  then - inout_flag * quantity * unit_factor * {costPrice} else 0  end )::numeric,2) as free_cost,
round(sum(case when row_index = 1 then inout_flag*(-1)*coalesce(now_disc_amount,0) else 0 end):: NUMERIC,2 ) AS DSC	 
FROM sheet_sale_detail d
LEFT JOIN sheet_sale_main m on d.company_id = m.company_id  and d.sheet_id = m.sheet_id
{leftJoinSeller}
where m.red_flag is null  {sellerIdCondition} {departPathCondition}
and d.company_id = {companyID}
and m.happen_time >= '{startTime}'
and m.happen_time <= '{endTime}'
and d.happen_time >= '{startTime}'
and d.happen_time <= '{endTime}'
and m.approve_time is not null 
AND coalesce(trade_type,'X') NOT IN ( 'J', 'H' )";//如果备注包含了两种或以上特殊关键字，统计优先级为：赠品>兑奖>陈列

            QQ.Enqueue("incomeAndcost", sql);

            sql = $@"
SELECT 
    COALESCE(round(sum((total_amount - now_pay_amount - now_disc_amount)*money_inout_flag)::NUMERIC, 2), 0) AS left_amount
FROM sheet_sale_main m
{leftJoinSeller}
    where m.red_flag is null {sellerIdCondition} {departPathCondition}
    and m.company_id = {companyID}
    and m.happen_time >= '{startTime}'
    and m.happen_time <= '{endTime}'
    and m.approve_time is not null 
    and sheet_type in ('X','T')
";
            QQ.Enqueue("saleArrears", sql);


            sql = @$"
SELECT
        sheet_type, round(sum(coalesce(now_disc_amount,0) ):: NUMERIC,2 ) AS amount 
FROM sheet_get_arrears_main m
{leftJoinGetter}
    where m.red_flag is null  {getterIdCondition} {departPathCondition}
    and m.company_id = {companyID}
    and m.happen_time >= '{startTime}'
    and m.happen_time <= '{endTime}'
    and m.approve_time is not null 
    group by sheet_type
    order by sheet_type desc
";

            QQ.Enqueue("skdiscamount", sql);

            sql = @$"
SELECT
        -round(sum(coalesce(now_disc_amount,0) ):: NUMERIC,2 ) AS amount 
FROM sheet_buy_main m
{leftJoinSeller}
    where m.red_flag is null  {sellerIdCondition} {departPathCondition}
    and m.company_id = {companyID}
    and m.happen_time >= '{startTime}'
    and m.happen_time <= '{endTime}'
    and m.approve_time is not null 
";

            QQ.Enqueue("buydisk", sql);

            sql = @$"
select 
round(SUM ( {costPrice} * quantity *inout_flag*unit_factor)  :: NUMERIC, 2 ) as cost_amount

FROM sheet_invent_change_detail d
LEFT JOIN sheet_invent_change_main m on d.company_id = m.company_id  and d.sheet_id = m.sheet_id
{leftJoinSeller}
where m.red_flag is null  {sellerIdCondition} {departPathCondition}
and d.company_id = {companyID}
and m.happen_time >= '{startTime}'
and m.happen_time <= '{endTime}'
and m.approve_time is not null 
and m.sheet_type ='YK'
";

            QQ.Enqueue("inventory", sql);

            sql = @$"
select round(SUM ({cost_amount_type})  :: NUMERIC,2  ) as cost_amount
from sheet_invent_change_main m
{leftJoinSeller}
where m.red_flag is null  {sellerIdCondition} {departPathCondition}
and m.company_id = {companyID}
and m.happen_time >= '{startTime}'
and m.happen_time <= '{endTime}'
and m.approve_time is not null 
and m.sheet_type ='BS'
";

            QQ.Enqueue("bs", sql);



            sql = $@"
WITH RECURSIVE subject_tree AS (
    SELECT 
        cs.sub_id, 
        cs.sub_name, 
        cs.mother_id as parent_id
    FROM cw_subject cs
    WHERE cs.company_id = {companyID}
    AND cs.sub_id IN (
        SELECT DISTINCT d.fee_sub_id
        FROM sheet_fee_out_detail d
        LEFT JOIN sheet_fee_out_main m ON d.sheet_id = m.sheet_id AND d.company_id = m.company_id
        {leftJoinGetter}
        WHERE m.happen_time >= '{startTime}'
        AND m.happen_time <= '{endTime}'
        AND m.red_flag IS NULL
        AND m.approve_time is not null
        AND d.company_id = {companyID}
        {getterIdCondition} {departPathCondition}
        AND NOT coalesce(m.sheet_attribute,'{{}}'::jsonb) ? 'buy_sheet_id'
    )
    
    UNION
    
    SELECT 
        cs.sub_id,
        cs.sub_name,
        cs.mother_id as parent_id
    FROM cw_subject cs
    JOIN subject_tree st ON cs.sub_id = st.parent_id
    WHERE cs.company_id = {companyID}
)

SELECT
    sub_id,
    sub_name,
    parent_id,
    SUM(detail) as detail
FROM (
    SELECT
        cs.sub_id,
        cs.sub_name,
        cs.mother_id as parent_id,
        round(SUM(d.fee_sub_amount)::NUMERIC, 2) as detail
    FROM sheet_fee_out_detail d
    LEFT JOIN sheet_fee_out_main m ON d.sheet_id = m.sheet_id AND d.company_id = m.company_id
    LEFT JOIN cw_subject cs ON cs.company_id = d.company_id AND d.fee_sub_id = cs.sub_id
    {leftJoinGetter}
    WHERE m.happen_time >= '{startTime}'
    AND m.happen_time <= '{endTime}'
    AND m.red_flag IS NULL
    AND m.approve_time is not null
    AND d.company_id = {companyID}
    {getterIdCondition} {departPathCondition}
    AND cs.sub_type = 'ZC'
    AND NOT coalesce(m.sheet_attribute,'{{}}'::jsonb) ? 'buy_sheet_id'
    GROUP BY cs.sub_id, cs.sub_name, cs.mother_id
    
    UNION ALL
    
    SELECT
        st.sub_id,
        st.sub_name,
        st.parent_id,
        0 as detail
    FROM subject_tree st
    WHERE NOT EXISTS (
        SELECT 1
        FROM sheet_fee_out_detail d
        LEFT JOIN sheet_fee_out_main m ON d.sheet_id = m.sheet_id AND d.company_id = m.company_id
        WHERE d.fee_sub_id = st.sub_id
        AND m.happen_time >= '{startTime}'
        AND m.happen_time <= '{endTime}'
        AND m.red_flag IS NULL
        AND m.approve_time is not null
        AND d.company_id = {companyID}
        {getterIdCondition} {departPathCondition}
        AND NOT coalesce(m.sheet_attribute,'{{}}'::jsonb) ? 'buy_sheet_id'
    )
) AS combined_data
GROUP BY sub_id, sub_name, parent_id
ORDER BY sub_name";
            QQ.Enqueue("fee_detail", sql);

            sql = $@"
SELECT 
    COALESCE(round(sum((total_amount - now_pay_amount - now_disc_amount))::NUMERIC, 2), 0) AS left_amount
FROM sheet_fee_out_main m
{leftJoinGetter}
    where m.red_flag is null {getterIdCondition} {departPathCondition}
    and m.company_id = {companyID}
    and m.happen_time >= '{startTime}'
    and m.happen_time <= '{endTime}'
    and m.approve_time is not null 
    and sheet_type = 'ZC'
";
            QQ.Enqueue("feeArrears", sql);
            sql = @$"SELECT  
sub_id,
sub_name,	
SUM ( detail ) AS detail 
FROM	(	
            SELECT	
            sub_id,		
            sub_name,		
            round( SUM ( d.fee_sub_amount * M.money_inout_flag ) :: NUMERIC, 2 ) AS detail 
            FROM		sheet_fee_out_detail d		
            LEFT JOIN sheet_fee_out_main M ON d.sheet_id = M.sheet_id 		AND d.company_id = M.company_id		
            LEFT JOIN cw_subject cs ON cs.company_id = d.company_id 		AND d.fee_sub_id = cs.sub_id 	
            {leftJoinGetter}
            WHERE		M.happen_time >= '{startTime}'		
            AND M.happen_time <= '{endTime}'		
            AND M.red_flag IS NULL
            AND M.approve_time is not null  
            AND d.company_id ={companyID} 		{getterIdCondition}     {departPathCondition}
            AND cs.sub_type = 'QTSR' 	
            GROUP BY	 
            sub_id,		
            sub_name 
            UNION all	
            SELECT	
            sub_id,		
            sub_name,	
            CASE		WHEN payway1_id = cs.sub_id THEN		SUM ( - M.payway1_amount * M.money_inout_flag ) 	WHEN payway2_id = cs.sub_id THEN	SUM ( - M.payway2_amount * M.money_inout_flag ) 	END detail 
            FROM	sheet_buy_main	M 
            LEFT JOIN cw_subject cs ON cs.company_id = M.company_id 	AND ( M.payway1_id = cs.sub_id OR M.payway2_id = cs.sub_id ) 
            WHERE	M.happen_time >= '{startTime}'	
            AND M.happen_time <= '{endTime}'
            AND M.approve_time is not null
            AND M.red_flag IS NULL 	AND M.company_id ={companyID} {sellerIdCondition}	
            AND cs.sub_type = 'QTSR' 
            GROUP BY	
            sub_name,	
            sub_id,
            payway1_id,
            payway2_id
       UNION all 
            SELECT  
            sub_id,	
            sub_name,
            CASE		WHEN payway1_id = cs.sub_id THEN		SUM (  M.payway1_amount * M.money_inout_flag ) WHEN payway2_id = cs.sub_id THEN	SUM (  M.payway2_amount * M.money_inout_flag ) 	END detail 
            FROM	sheet_prepay M 	
            {leftJoinGetter}
            LEFT JOIN cw_subject cs ON cs.company_id = M.company_id 	AND ( M.payway1_id = cs.sub_id OR M.payway2_id = cs.sub_id ) 
            WHERE	M.happen_time >= '{startTime}'	
            AND M.happen_time <= '{endTime}'	
            AND M.red_flag IS NULL
            AND M.approve_time is not null
            AND M.company_id ={companyID} {getterIdCondition}	{departPathCondition}
            AND cs.sub_type = 'QTSR'
            GROUP BY	
            sub_name,	
            sub_id,	
            payway1_id,	
            payway2_id 
) T
GROUP BY
sub_id,
sub_name ";
            QQ.Enqueue("Otherincome_detail", sql);

            sql = $@"
SELECT 
    COALESCE(round(sum((total_amount - now_pay_amount - now_disc_amount))::NUMERIC, 2), 0) AS left_amount
FROM sheet_fee_out_main m
{leftJoinGetter}
    where m.red_flag is null {getterIdCondition} {departPathCondition}
    and m.company_id = {companyID}
    and m.happen_time >= '{startTime}'
    and m.happen_time <= '{endTime}'
    and m.approve_time is not null 
    and sheet_type = 'SR'
";
            QQ.Enqueue("otherIncomeArrears", sql);

            sql = $@"
SELECT 
    COALESCE(round(sum((now_pay_amount))::NUMERIC, 2), 0) AS gotten_amount
FROM sheet_get_arrears_main m
{leftJoinGetter}
    where m.red_flag is null {getterIdCondition}    {departPathCondition}
    and m.company_id = {companyID}
    and m.happen_time >= '{startTime}'
    and m.happen_time <= '{endTime}'
    and m.approve_time is not null 
    and sheet_type = 'SK'
";
            QQ.Enqueue("gottenArrears", sql);

            var incomeAndcost = new List<ExpandoObject>(); 
            var fee_detail = new List<ExpandoObject>();
            var Otherincome_detail = new List<ExpandoObject>();
            var inventory = new List<ExpandoObject>();
            var skdiscamount = new List<ExpandoObject>();
            var buydisk = new List<ExpandoObject>();
            var bs = new List<ExpandoObject>();

            var dr = await QQ.ExecuteReaderAsync();

            var otherInfo = new List<ExpandoObject>();

            var saleArrears = new ExpandoObject();
            var otherIncomeArrears = new ExpandoObject();
            var feeArrears = new ExpandoObject();
            var gottenArrears = new ExpandoObject();

            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "incomeAndcost")
                {
                    incomeAndcost = CDbDealer.GetRecordsFromDr(dr, false);               
                }
                else if(sqlName == "fee_detail")
                {
                    fee_detail = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "Otherincome_detail")
                {
                    Otherincome_detail = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "inventory")
                {
                    inventory = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "bs")
                {
                    bs = CDbDealer.GetRecordsFromDr(dr, false);
                }

                else if (sqlName == "skdiscamount")
                {
                    skdiscamount = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "buydisk")
                {
                    buydisk = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "saleArrears")
                {
                    saleArrears = CDbDealer.Get1RecordFromDr(dr, false);
                }
                else if (sqlName == "otherIncomeArrears")
                {
                    otherIncomeArrears = CDbDealer.Get1RecordFromDr(dr, false);
                }
                else if (sqlName == "feeArrears")
                {
                    feeArrears = CDbDealer.Get1RecordFromDr(dr, false);
                }
                else if (sqlName == "gottenArrears")
                {
                    gottenArrears = CDbDealer.Get1RecordFromDr(dr, false);
                }

            }
            QQ.Clear();
            string result = "OK";
            string msg = "";


            return Json(new { result, msg, incomeAndcost, fee_detail, Otherincome_detail, inventory,bs, skdiscamount, buydisk, saleArrears,feeArrears,otherIncomeArrears, gottenArrears});
        }
         
        
        [HttpGet]
        public async Task<JsonResult> GetSellerId(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SQLQueue QQ = new SQLQueue(cmd);
            string sellerSql = $"select oper_id as id,oper_name as value,depart_path from info_operator where company_id='{companyID}' and is_seller and COALESCE(status,'1')='1' order by order_index,oper_name";
            QQ.Enqueue("seller", sellerSql);

            string departSql = $@"select depart_id as id,depart_name as value,mother_id as mId from info_department  WHERE company_id = {companyID} ";
            QQ.Enqueue("depart", departSql);
            List<ExpandoObject> seller = null;
            List<ExpandoObject> depart = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "seller")
                {
                    seller = CDbDealer.GetRecordsFromDr(dr, false);
                };
                if (sqlName == "depart")
                {
                    depart = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, seller, depart });

        }

    }



}
