﻿using ArtisanManage.Models;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Pages.Sheets;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static ArtisanManage.AppController.SheetVisitController;
using static ArtisanManage.Services.CommonTool;

namespace ArtisanManage.AppController
{
    /// <summary>
    /// 客户
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class DesktopDataQuerierController : QueryController
    {
        public DesktopDataQuerierController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpPost]
        public async Task<JsonResult> QueryData([FromBody] dynamic param)
        {
            SQLQueue QQ = new SQLQueue(cmd);
            foreach(var kp in param)
            {
                string sql = "";
                dynamic p = param[kp.Name];
                SqlResult sqlResult = null;
                if (kp.Name == "toApproveOrders")
                {
                    sqlResult = AppOrderManageController.GetOrdersForApprove_SQL(cmd, Token, true, true, 1, 0, null, null, null, null, (string)p.operRegions, false, (string)p.startDate, (string)p.endDate,(string)p.operID, (string)p.departID, (string)p.branchID, (string)p.senderID, (string)p.sellerID);                   
                }
                else if (kp.Name == "toReviewOrders")
                {
                    sqlResult = AppOrderManageController.GetOrdersForReview_SQL (cmd, Token, true, false, 1, 0, null, null, null, null, (string)p.operRegions, false, (string)p.startDate, (string)p.endDate, (string)p.departID, (string)p.branchID, (string)p.senderID, (string)p.sellerID);
                }
                else if (kp.Name == "toPrintOrders")
                {
                    sqlResult = AppOrderManageController.GetOrdersForPrint_SQL(cmd, Token, true, false, 1, 0, null, null, null, null, (string)p.operRegions, false, (string)p.startDate, (string)p.endDate,(string)p.operID, (string)p.departID, (string)p.branchID, (string)p.senderID, (string)p.sellerID);
                }
				else if (kp.Name == "toGrabOrders")
				{
					sqlResult = AppOrderManageController.GetOrdersForGrab_SQL(cmd, Token, true, false, 1, 0, null, null, null, null, (string)p.operRegions, (string)p.startDate, (string)p.endDate, (string)p.operID, (string)p.sellerID, (string)p.branchID, (string)p.departID,null);
				}
				else if (kp.Name == "toAssignOrders")
                {
                    sqlResult = AppOrderManageController.GetOrdersForAssignVan_SQL(cmd, Token, true, false, 1, 0, null, null, null, null, (string)p.operRegions, false, (string)p.startDate, (string)p.endDate, (string)p.departID, (string)p.branchID, (string)p.senderID, (string)p.sellerID,CPubVars.GetBool(p.is_sender),p.sheetViewRange, CPubVars.GetBool(p.isBoss), CPubVars.GetBool(p.needReview), CPubVars.GetBool(p.ignorePayFailedSheetOnAssignVan));
                }
                else if (kp.Name == "toApproveAssignVan")
                {
                    sqlResult = AppOrderManageController.GetAssignVanForApprove_SQL(cmd, Token, true, false, 1, 0, false, (string)p.startDate, (string)p.endDate, (string)p.branchID, (string)p.senderID, (string)p.sellerID, (string)p.branches);
                }
                //兼容前端两个key的版本，deliveryReceipt是最新的
                else if (kp.Name == "toDeliveryReceipt" || kp.Name == "deliveryReceipt")
                {
                    sqlResult = AppOrderManageController.GetOrdersForSale_SQL(cmd, Token, true, false, 1, 0, null, null, null, null,(string) p.operRegions, (string)p.startDate, (string)p.endDate,CPubVars.GetBool(p.isBoss), CPubVars.GetBool(p.isAssignVanNecessary), CPubVars.GetBool(p.noVanOrderToSale),(string)p.sellerID, (string)p.senderID, (string)p.branchID, (string)p.carID, CPubVars.GetBool(p.ignorePayFailedSheetOnOrderToSale));
                }
                else if (kp.Name == "toBackBranch")
                {
                    sqlResult = AppOrderManageController.GetOrdersForBackBranch_SQL(cmd, Token, true, false, 1, 0, null, null, null, null, (string)p.operRegions,false, (string)p.startDate, (string)p.endDate, (string)p.departID,(string)p.branchID, (string)p.senderID,(string)p.sellerID, CPubVars.GetBool(p.is_sender), CPubVars.GetBool(p.isBoss));
                }
                else if (kp.Name == "toApproveBackBranch")
                {
                    sqlResult = AppOrderManageController.GetBackBranchSheetForApprove_SQL(cmd, Token, true, false, 1, 0,false, (string)p.startDate, (string)p.endDate,(string)p.branchID,(string)p.senderID,(string)p.sellerID,(string)p.branches);
                }
                else
                {
                    QueryItem.g_QueryItems.TryGetValue(kp.Name, out QueryItem item);
              
                    if (item != null)
                    {
                        sql = item.Sql;
                        Regex reg = new Regex(@"(?i)(?<=\[)(.*)(?=\])");
                        var ms = reg.Matches(sql);
                        var operID = "";
                        foreach (var pp in kp.Value)
                        {
                            var ppKey = kp.Name;
                            var ppVal = pp.Value.ToString();
                            if (ppVal != "")
                            {

                                if (pp.Name == "operID") operID = ppVal;
                                if (pp.Name == "operRegions")
                                {
                                    var regionSql = "";
                                    string operRegions = ppVal;
                                    if (operRegions.IsValid())
                                    {
                                        int[] regions = JsonConvert.DeserializeObject<int[]>(operRegions);
                                        if (regions.Length > 0)
                                            regionSql += " and (" + string.Join(" or ", regions.Select(x => $"other_region  like '%/{x}/%'")) + ") ";
                                    }
                                    foreach (var m in ms)
                                    {
                                        var tempM = m.ToString();
                                        if (tempM != "")
                                        {
                                            if (tempM.IndexOf("~VAR_" + pp.Name) > 0)
                                                sql = sql.Replace("[" + tempM + "]", regionSql);
                                        }
                                    }


                                }
                                else
                                {
                                    sql = sql.Replace("~VAR_" + pp.Name, ppVal);
                                }
                                if (pp.Name == "isAssignVanNecessary" && ppKey == "toDeliveryReceipt")
                                {
                                    sql += " and oss.order_status = 'zc'";
                                }
                                if (pp.Name == "isSender" && (ppKey == "toAssignOrders" || ppKey == "toBackBranch") && operID != "")
                                {
                                    if (ppVal == "True")
                                    {
                                        sql += $" and (oss.senders_id like '%{operID}%' or sm.senders_id like '%{operID}%' or sm.seller_id = {operID}) ";
                                    }
                                    else
                                    {
                                        sql += $" and sm.seller_id = {operID}";
                                    }

                                }

                            }
                            else
                            {
                                if (ms.Count > 0)
                                {
                                    foreach (var m in ms)
                                    {
                                        var tempM = m.ToString();
                                        if (tempM != "")
                                        {
                                            if (tempM.IndexOf("~VAR_" + pp.Name) > 0)
                                                sql = sql.Replace("[" + tempM + "]", "");
                                        }
                                    }

                                }
                            }
                        }
                        sql = sql.Replace("~VAR_companyID", Token.CompanyID);
                        sql = sql.Replace("[", "").Replace("]", "");
                    }
                }
                if(sqlResult!=null) sql = sqlResult.SqlCount;
                if (sql != "") QQ.Enqueue(kp.Name, sql);

            }

            Dictionary<string, string> data = new Dictionary<string, string>();

            try
            {
                var dr = await QQ.ExecuteReaderAsync();
               
                while (QQ.Count > 0)
                {
                    var sqlName = QQ.Dequeue();
                    dr.Read();
                    data[sqlName] = CPubVars.GetTextFromDr(dr, "total");
                }
                QQ.Clear();
            }
            catch(Exception e)
            {
                NLogger.Info("In QueryData:Error" +e.Message+" sql:" + QQ.SQL);
                return new JsonResult(new { result = "Error", msg = "获取数据失败", data });
            }
            

            return new JsonResult(new { result = "OK", msg = "",data });
        }
    }


    public class QueryParam
    { 
        public Dictionary<string, string> Variables = new Dictionary<string, string>();
        
    }
  
    public class QueryItem
    {
        public string Sql = "";
        public Dictionary<string, string> Variables = new Dictionary<string, string>();
        public static Dictionary<string, QueryItem> g_QueryItems = new Dictionary<string, QueryItem>()
        {
            {"toApproveOrders" ,new QueryItem
                {
                    Sql= $@"SELECT count(*) total
                         FROM sheet_sale_order_main sm
                         LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id=~VAR_companyID
                         LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and io.company_id=~VAR_companyID
                         where sm.company_id = '~VAR_companyID' and approve_time is null  and sm.red_flag is null  and supcust_flag = 'C' and (s.status = '1' or s.status is null) 
                         [and other_region  like '%/~VAR_operRegion/%']
                         and sm.happen_time >= '~VAR_startDate' and sm.happen_time <= '~VAR_endDate'
                         [and sm.seller_id = ~VAR_operID]
                         [and io.depart_path like '%/~VAR_departID/%']
                    "
              
                 }
            },
            {"toReviewOrders" ,new QueryItem
                {
                    Sql= $@"SELECT count(*) total
                         FROM sheet_sale_order_main sm
                         LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id=~VAR_companyID
                         LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and io.company_id=~VAR_companyID
                         where sm.company_id = '~VAR_companyID' and review_time is null  and sm.red_flag is null  and supcust_flag = 'C' and (s.status = '1' or s.status is null) 
                         [and other_region  like '%/~VAR_operRegion/%']
                         and sm.happen_time >= '~VAR_startDate' and sm.happen_time <= '~VAR_endDate'
                         [and sm.seller_id = ~VAR_operID]
                         [and io.depart_path like '%/~VAR_departID/%']
                    "

                 }
            },
            {"toPrintOrders" ,new QueryItem
                {
                    Sql= $@"SELECT count(*) total
                         FROM sheet_sale_order_main sm
                         LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id=~VAR_companyID
                         LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and io.company_id=~VAR_companyID
                         LEFT JOIN sheet_status_order oss ON sm.sheet_id = oss.sheet_id and oss.company_id=~VAR_companyID
                         where sm.company_id = '~VAR_companyID'
                         and (oss.sheet_print_count is null  or oss.sheet_print_count =0)  and (oss.order_status is null or oss.order_status in ('xd'))
                         and sm.red_flag is null 
                         and approve_time is not null
                         and supcust_flag = 'C' 
                         and (s.status = '1' or s.status is null)  
                         [and other_region  like '%/~VAR_operRegion/%']  
                         and sm.happen_time >= '~VAR_startDate' 
                         and sm.happen_time <= '~VAR_endDate'
                         [and sm.seller_id = ~VAR_operID]
                         [and io.depart_path like '%/~VAR_departID/%'] 
                    "

                 }
            },
            {"toAssignOrders" ,new QueryItem
                {
                    Sql= $@"SELECT count(*) total
                         FROM sheet_sale_order_main sm
                         LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id=~VAR_companyID
                         LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and io.company_id=~VAR_companyID
                         LEFT JOIN sheet_status_order oss ON sm.sheet_id = oss.sheet_id and oss.company_id=~VAR_companyID
                         LEFT JOIN
                         (
                            SELECT od.* FROM  op_move_to_van_detail od 
                            LEFT JOIN op_move_to_van_main om on om.company_id=~VAR_companyID and od.op_id=om.op_id
                            WHERE od.company_id =~VAR_companyID  and om.red_flag is  null  
                         ) op on  sm.sheet_id = op.sale_order_sheet_id
                         where sm.company_id = '~VAR_companyID'
                         and op.sale_order_sheet_id is null  and (oss.order_status is null or oss.order_status in ('xd','dd'))
                         and sm.red_flag is null 
                         and approve_time is not null
                         and supcust_flag = 'C'
                         and (s.status = '1' or s.status is null)  
                         and sm.happen_time >= '~VAR_startDate' 
                         and sm.happen_time <= '~VAR_endDate' 
                         [and io.depart_path like '%/~VAR_departID/%']"

                }
            },
            {"toDeliveryReceipt" ,new QueryItem
                {
                    Sql= $@"SELECT count(*) total
                            FROM sheet_sale_order_main sm
                            LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id=~VAR_companyID
                            LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and io.company_id=~VAR_companyID
                            LEFT JOIN 
                                    ( 
                                        SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = '~VAR_companyID'
                                    ) A ON s.acct_cust_id = A.a_supcust_id
                            LEFT JOIN sheet_status_order oss ON sm.sheet_id = oss.sheet_id
                            LEFT JOIN 
                                    (
                                        select order_sheet_id, sheet_id as sale_sheet_id,red_flag, sheet_no as sale_sheet_no,senders_id,senders_name,approve_time from sheet_sale_main where red_flag IS NULL and company_id = '~VAR_companyID' and happen_time>='~VAR_startDate'
                                    ) m on sm.sheet_id = m.order_sheet_id
                            left join realtime_supcust r on sm.supcust_id=r.supcust_id and r.company_id=~VAR_companyID  
                            LEFT JOIN (select d.sale_order_sheet_id,m.approve_time FROM op_move_to_van_detail d 
                                        LEFT JOIN op_move_to_van_main m on d.company_id = m.company_id and d.op_id = m.op_id
                                        WHERE d.company_id = '~VAR_companyID' and m.red_flag is null
                              ) otd on otd.sale_order_sheet_id = sm.sheet_id
                            where sm.company_id = '~VAR_companyID' and sm.approve_time is not null  and sm.red_flag is null and  (m.sale_sheet_id is null or m.approve_time is null) and oss.receipt_status is null   and m.red_flag is null and supcust_flag = 'C' and (s.status = '1' or s.status is null)   
                                  and sm.happen_time >= '~VAR_startDate' 
                                  and sm.happen_time <= '~VAR_endDate'
                                  [and other_region  like '%/~VAR_operRegion/%']  
                                  [and (oss.senders_id like '%~VAR_operID%' or sm.senders_id like '%~VAR_operID%')] 
                                  [and io.depart_path like '%/~VAR_departID/%']"

                }
            },
            {"toBackBranch" ,new QueryItem
                {
                    Sql= $@"SELECT
                                count(*) total
                            FROM sheet_sale_order_main sm
                            LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id=~VAR_companyID
                            LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and io.company_id=~VAR_companyID
                            LEFT JOIN 
                            ( 
                                SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = ~VAR_companyID
                            ) A ON s.acct_cust_id = A.a_supcust_id
                            LEFT JOIN sheet_status_order oss ON sm.sheet_id = oss.sheet_id  and oss.company_id=~VAR_companyID
                            LEFT JOIN 
                            (
                                select order_sheet_id, sheet_id as sale_sheet_id,red_flag, sheet_no as sale_sheet_no,senders_id,senders_name from sheet_sale_main where red_flag IS NULL and company_id = ~VAR_companyID and happen_time>='{CPubVars.GetDateText(DateTime.Now.AddDays(-30))}'
                            ) m on sm.sheet_id = m.order_sheet_id
                            LEFT JOIN realtime_supcust r on s.supcust_id=r.supcust_id and r.company_id =~VAR_companyID                     
                            LEFT JOIN info_branch b on b.company_id = ~VAR_companyID and b.branch_id = sm.branch_id
                            LEFT JOIN info_branch car on car.company_id = ~VAR_companyID and car.branch_id = oss.van_id 
                            LEFT JOIN
                            (
                                    SELECT od.op_id,od.move_sheet_id,od.sale_order_sheet_id,od.sale_sheet_id,om.happen_time   FROM op_move_from_van_main om   
                                    LEFT JOIN op_move_from_van_detail od on od.company_id = ~VAR_companyID and od.op_id=om.op_id
				                            LEFT JOIN sheet_move_main sm on sm.company_id = ~VAR_companyID and sm.sheet_id= od.move_sheet_id
                                    WHERE om.company_id = '~VAR_companyID' and  sm.sheet_attribute->>'assignVan'='' and om.happen_time>='{CPubVars.GetDateText(DateTime.Now.AddDays(-30))}'
                            ) rej on sm.sheet_id = rej.sale_order_sheet_id
                            LEFT JOIN
                            (
                                    SELECT od.op_id,od.move_sheet_id,od.sale_order_sheet_id,od.sale_sheet_id,om.happen_time   FROM op_move_from_van_main om  
                                    LEFT JOIN op_move_from_van_detail od  on od.company_id = ~VAR_companyID' and od.op_id=om.op_id
				                            LEFT JOIN sheet_move_main sm on sm.company_id = od.company_id and sm.sheet_id= od.move_sheet_id
                                    WHERE om.company_id = '~VAR_companyID' and  sm.sheet_attribute->>'assignVan'='back' and om.happen_time>='{CPubVars.GetDateText(DateTime.Now.AddDays(-30))}'
                            ) ret on sm.sheet_id = ret.sale_order_sheet_id
                            LEFT JOIN (
SELECT DISTINCT ofr.sale_order_sheet_id,ofr.sale_sheet_id, string_agg(DISTINCT ofr.back_type, ',') back_types ,case when back_done is null then case when  sum( case when need_move_qty-move_qty-sale_qty >0 then 1 else 0 end  )>0 then false else true end else back_done end back_done
    FROM op_move_from_van_row ofr
    LEFT JOIN op_move_from_van_main om on om.company_id =~VAR_companyID and om.op_id = ofr.op_id and om.happen_time >= '~VAR_startDate' 
    WHERE ofr.company_id = '~VAR_companyID' and om.red_flag is null and ofr.is_previous_move is null 
    GROUP BY ofr.op_id,ofr.sale_order_sheet_id,ofr.sale_sheet_id,back_done
) back on back.sale_order_sheet_id=sm.sheet_id
                            where sm.company_id = '~VAR_companyID'
                            and  oss.order_status ='zd'
                            and (oss.receipt_status in ('bf','js') or oss.has_return = 't')
                            and ((back_branch_status is null  or back_branch_status ='bf') or  back_done = false ) and back_branch_done is null
                            and sm.red_flag is null 
                            and sm.approve_time is not null
                            and m.red_flag is null 
                            and sm.happen_time >= '~VAR_startDate' 
                            and sm.happen_time <= '~VAR_endDate'
                            and supcust_flag = 'C' 
                            and (s.status = '1' or s.status is null)  
                            [and other_region  like '%/~VAR_operRegion/%']  
                            [and io.depart_path like '%/~VAR_departID/%']"

                }
            },
            {"toApproveBackBranch" ,new QueryItem
                {
                    Sql= $@"
SELECT count(*) total
FROM op_move_from_van_main otm  
LEFT JOIN ( SELECT op_id, count(op_id),string_agg(sale_order_sheet_id::text,',') sale_order_sheets_id  FROM op_move_from_van_detail WHERE company_id = '~VAR_companyID' GROUP BY op_id) otd on otd.op_id = otm.op_id
LEFT JOIN sheet_move_main sm on sm.company_id =~VAR_companyID and sm.sheet_id =  otm.move_sheet_id
LEFT JOIN info_operator io on io.company_id = ~VAR_companyID and io.oper_id = otm.oper_id
LEFT JOIN info_branch f on f.company_id = ~VAR_companyID and f.branch_id = sm.from_branch_id
LEFT JOIN info_branch t on t.company_id = ~VAR_companyID and t.branch_id = sm.to_branch_id
LEFT JOIN (SELECT DISTINCT op_id,assign_van_type FROM op_move_from_van_row WHERE company_id = '~VAR_companyID') otr on otr.op_id  = otm.op_id
LEFT JOIN (
SELECT op_id , max(reject_branch) reject_branch,max(return_branch) return_branch,max(reject_move) reject_move,max(return_move) return_move FROM (
SELECT DISTINCT op_id ,
case when back_type = 'reject' then back_branch else -1 end reject_branch ,
case when back_type = 'return' then back_branch else -1 end return_branch,
case when back_type = 'reject' then move_sheet_id else -1 end reject_move ,
case when back_type = 'return' then move_sheet_id else -1 end return_move
FROM op_move_from_van_row  
WHERE company_id = '~VAR_companyID'
)t
GROUP BY op_id 
)back on back.op_id=otm.op_id 
LEFT JOIN info_branch bb on bb.company_id = ~VAR_companyID and bb.branch_id = otm.from_branch
LEFT JOIN sheet_move_main ret on ret.company_id=~VAR_companyID and back.reject_move = ret.sheet_id 
LEFT JOIN sheet_move_main ren on ren.company_id=~VAR_companyID and back.return_move = ren.sheet_id 
LEFT JOIN info_branch rtb on rtb.company_id = ~VAR_companyID and rtb.branch_id = back.reject_branch
LEFT JOIN info_branch rnb on rnb.company_id = ~VAR_companyID and rnb.branch_id = back.return_branch
WHERE   otm.company_id = '~VAR_companyID'  and case when  otm.move_sheet_id is not null then  sm.from_branch_id else otm.from_branch end in (~VAR_branchs)   and otm.happen_time >= '~VAR_startDate' and otm.happen_time <= '~VAR_endDate'   and case when otm.move_sheet_id is not null then sm.approve_time else case when ret.sheet_id is not null then ret.approve_time else case when ren.sheet_id is not null then ren.approve_time else otm.approve_time end end end   is null   "
                }
            },
            {"toApproveAssignVan" ,new QueryItem
                {
                    Sql= $@"SELECT count(*) total
FROM op_move_to_van_main otm  
LEFT JOIN ( SELECT op_id, count(op_id),string_agg(sale_order_sheet_id::text,',') sale_order_sheets_id  FROM op_move_to_van_detail WHERE company_id = '~VAR_companyID' GROUP BY op_id) otd on otd.op_id = otm.op_id
LEFT JOIN info_operator io on io.company_id = ~VAR_companyID and io.oper_id = otm.oper_id
LEFT JOIN info_branch f on f.company_id = ~VAR_companyID and f.branch_id = otm.from_branch
LEFT JOIN info_branch t on t.company_id = ~VAR_companyID and t.branch_id = otm.to_van
LEFT JOIN sheet_move_main sm on sm.company_id = ~VAR_companyID and sm.sheet_id =  otm.move_sheet_id
WHERE   otm.company_id = '~VAR_companyID'  and otm.from_branch in (~VAR_branchs) and otm.red_flag is null  and otm.happen_time >= '~VAR_startDate' and otm.happen_time <= '~VAR_endDate'   and case when otm.move_sheet_id is not null then sm.approve_time else otm.approve_time end  is null  
"

                }
            }

        };
    }
}
