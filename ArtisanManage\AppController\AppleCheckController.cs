﻿using ArtisanManage.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    [ApiController]
    public class AppleCheckController : ControllerBase
    {
        public static string teamID = "";
        public static string packageID = "";

        [Route("/apple-app-site-association")]

        public string AppleCheck()
        {
            var details = new dynamic[] {new {
                appID = teamID+"."+packageID,
                paths = new string[] { "*" }
            }
        };
            dynamic applinks = new {
                apps = new string[] { },
                details = details
            };
            return Newtonsoft.Json.JsonConvert.SerializeObject(new { applinks });

        }
    }
}
