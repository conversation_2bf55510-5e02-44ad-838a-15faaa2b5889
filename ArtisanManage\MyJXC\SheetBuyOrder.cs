﻿using ArtisanManage.Models;
using ArtisanManage.Pages;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowBuyOrder : SheetRowMM
    {
        //[SaveToDB][FromFld] public string virtual_produce_date { get; set; }
        [SaveToDB][FromFld] public decimal done_qty { get; set; } = 0;

        public string done_qty_conv
		{
            get
            {
                return SheetBase<SheetRowItem>.GetUnitQty(done_qty, b_unit_no, m_unit_no, s_unit_no, b_unit_factor, m_unit_factor);
            }

        }
    }
    public class SheetBuyOrder: SheetMM<SheetRowBuyOrder>
    {
        [SaveToDB][FromFld] public string buy_order_status { get; set; } = "ff";
        [SaveToDB][FromFld] public string order_source { get; set; } = "";

        //[SaveToDB] [FromFld] public string senders_id { get; set; } = "";
        //[SaveToDB] [FromFld] public string senders_name { get; set; } = "";
        //[FromFld(LOAD_PURPOSE.SHOW)] public string sender_mobile { get; set; } = "";
        // [FromFld("tb_status.buy_sheet_id")] public string buy_sheet_id { get; set; } = "";
        public string bindSheetInfo { get; set; } = ""; // 分销单据绑定
        [SaveToDB]
        [FromFld]
        public override string sheet_attribute
        {
            get//从前端获取数据，保存数据库
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                string baseAttr = base.sheet_attribute;
                if (baseAttr != "") sheetAttribute = JsonConvert.DeserializeObject<Dictionary<string, string>>(baseAttr);
                if (!string.IsNullOrEmpty(bindSheetInfo))
                {
                    sheetAttribute.Add("bindSheetInfo", bindSheetInfo);
                }
                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set//读取数据库，返回前端
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.bindSheetInfo != null)
                    {
                        this.bindSheetInfo = sheetAttr.bindSheetInfo;
                    }

                }
                base.sheet_attribute = value;

            }
        }


        public SheetBuyOrder(SHEET_RETURN sheetReturn, LOAD_PURPOSE loadPurpose):base("sheet_buy_order_main", "sheet_buy_order_detail", loadPurpose)
        {
            sheet_type = sheetReturn == SHEET_RETURN.IS_RETURN ? SHEET_TYPE.SHEET_BUY_DD_RETURN : SHEET_TYPE.SHEET_BUY_DD;
            ConstructFun();
        }
        public SheetBuyOrder(LOAD_PURPOSE loadPurpose) : base("sheet_buy_order_main", "sheet_buy_order_detail", loadPurpose)
        {
            ConstructFun();
        }
        public SheetBuyOrder() : base("sheet_buy_order_main", "sheet_buy_order_detail", LOAD_PURPOSE.SHOW)
        {
            sheet_type = SHEET_TYPE.SHEET_BUY_DD;
            ConstructFun();
        }
        private void ConstructFun()
        {
            MainLeftJoin += $@"  
left join(select sub_id,sub_name as other_fee1_name from cw_subject where company_id = ~company_id) cw1 on cw1.sub_id = t.other_fee1_id
  left join(select sub_id,sub_name as other_fee2_name from cw_subject where company_id = ~company_id) cw2 on cw2.sub_id = t.other_fee2_id
       ";
            DetailLeftJoin += $@" 
       ";

        }
      
   
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            string sql = "";
            if (SheetRows.Count > 0)
            { 
                string items_id = string.Join(",", SheetRows.Select(r => r.item_id));
                
                sql = $"select item_id,sum(stock_qty) total_qty from stock where company_id={company_id} and item_id in ({items_id}) group by item_id";
                QQ.Enqueue("total_stock", sql);

                sql = $"select item_id,cost_price_avg, item_cost_price_suspect from info_item_prop where company_id={company_id} and item_id in ({items_id})";
                QQ.Enqueue("cost_avg_info", sql);
                sql = $@"select mu.item_id,mu.unit_no as item_unit_no,mu.unit_factor item_unit_factor from info_item_multi_unit mu where mu.company_id =  {company_id} and mu.item_id in ({items_id});";
                QQ.Enqueue("recent_price", sql);

                sql = $"select setting from company_setting where company_id={company_id};";
                QQ.Enqueue("setting", sql);

            }

            if (red_flag == "2")
            {
                sql = @$"SELECT * FROM sheet_buy_main where company_id = {company_id} and order_sheet_id = {red_sheet_id} and red_flag is null ;";
                QQ.Enqueue("buy_sheets", sql);
            }
        }

		 
       
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (sqlName == "total_stock")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowBuyOrder row in SheetRows)
                    {
                        if (row.item_id == rec.item_id) row.old_total_qty = CPubVars.ToDecimal(rec.total_qty == "" ? 0 : rec.total_qty);
                    }
                }
            }
             else if (sqlName == "cost_avg_info")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowBuyOrder row in SheetRows)
                    {
                        if (row.item_id == rec.item_id)
                        { 
                            row.cost_price_avg =  rec.cost_price_avg != "" ? rec.cost_price_avg : "0";
                            row.item_cost_price_suspect = Convert.ToBoolean(rec.item_cost_price_suspect!="" ? rec.item_cost_price_suspect : false);
                        }
                    }
                }
            }
            else if (sqlName == "recent_price") //判断 保存最近售价
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                if (records != null)
                {
                    Dictionary<string, string> dicUniqueItems = new Dictionary<string, string>();
                    foreach (SheetRowBuyOrder row in SheetRows)
                    {

                        //if (row.quantity * row.inout_flag > 0) continue;
                        if (row.real_price == 0) continue;
                        if (dicUniqueItems.ContainsKey(row.item_id)) continue;
                        dicUniqueItems.Add(row.item_id, row.item_id);
                        foreach (dynamic rec in records)
                        {
                            if (rec.item_id == row.item_id)
                            {
                                SheetRowBuyOrder BuyRow = new SheetRowBuyOrder();
                                BuyRow.item_id = row.item_id;
                                BuyRow.unit_no = rec.item_unit_no;
                               
                                if (rec.item_unit_factor == "") continue;
                                else
                                {
                                    BuyRow.real_price = CPubVars.ToDecimal(Math.Round((row.real_price / row.unit_factor * CPubVars.ToDecimal(rec.item_unit_factor)), 2));
                                    dynamic orig_price = 0m;
                                    if (row.orig_price != null && row.orig_price != "")
                                    {
                                        orig_price = CPubVars.ToDecimal(row.orig_price);
                                        BuyRow.orig_price = (Math.Round((orig_price / row.unit_factor * CPubVars.ToDecimal(rec.item_unit_factor)), 2)).ToString();
                                    }
                                }
                                BuyRow.son_mum_item = row.son_mum_item;
                                BuyRow.quantity = row.quantity;
                                info.UnitPriceRows.Add(BuyRow);
                            }
                        }
                        if (info.ErrMsg != "") return;
                    }
                }
            }
            else if(sqlName== "setting")
            {
                dynamic rec= CDbDealer.Get1RecordFromDr(dr,false);
                if(rec!=null && rec.setting!="")
                   info.CompanySetting =JsonConvert.DeserializeObject(rec.setting); 
            }else if(sqlName == "buy_sheets")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);

                if (records.Count > 0) { 
                    info.ErrMsg = "订单已转采购单,无法红冲！";
                }
            }

            info.SheetRows = SheetRows;
        }
      
       
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            if (red_flag != "2")
            {
                //            string orderSource = "";
                //            if (sheet_id != "")
                //            {
                //                    string orderSourceSql = $"select  * from sheet_buy_order_main where company_id = {company_id} and supcust_id = {supcust_id} and sheet_id ={sheet_id};";
                //                    dynamic dbSheet = await CDbDealer.Get1RecordFromSQLAsync(orderSourceSql, cmd);
                //                    orderSource = dbSheet.order_source;
                //            }
                //            string rsSellerSql = $@"
                //select rs.*,rp.sheet_sync from rs_seller rs
                //LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
                //    where rs.reseller_company_id={company_id} and rs.supplier_id = {supcust_id};";
                //            dynamic rsSeller = await CDbDealer.Get1RecordFromSQLAsync(rsSellerSql, cmd);

                //            string msg = "";
                //            if (rsSeller != null && rsSeller.sheet_sync.ToLower() == "true" && orderSource != "2fx")
                //            {
                //                string ret = "";
                //                try
                //                {
                //                    ret = await SaveAsSaleOrderSheet(cmd, this);
                //                    var ob = JsonConvert.DeserializeObject(ret);
                //                }catch(Exception e)
                //                {
                //                    if (ret != "") msg = ret;
                //                    else msg = "生成上级销售订单出错";
                //                    // info.ErrMsg = msg;
                //                    MyLogger.LogMsg($"In SaveAndApprove,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);


                //                }
                //                this.bindSheetInfo = ret;

                //            } 
            }

        }

        public async Task<dynamic> SaveAsSaleOrderSheet(CMySbCommand cmd, dynamic oriSheet)
        {
            string err = "";
            SheetSaleOrder sheet = JsonConvert.DeserializeObject<SheetSaleOrder>(JsonConvert.SerializeObject(oriSheet));
            // SheetBuyOrder sheet = new SheetBuyOrder();
            // sheet.SheetRows = oriSheet.SheetRows;
            // TODO 替换为client_id对应的company_id
            string operKey = sheet.OperKey;
            string sellerId = sheet.seller_id;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            sheet.Init();
            sheet.SYNCHRONIZE_SHEETS = true;
            sheet.order_source = "2fx";
            sheet.make_brief = "分销自动同步单据";
            sheet.money_inout_flag = sheet.money_inout_flag * -1;
            // sheet.company_id = "";
            // TODO 操作员ID 暂定为1
            sheet.OperID = "1";
            // TODO 客户 此处为分销商 client
            string resellerInfoSql = $"select * from rs_seller  rss left join (select plan_id,brand_id,client_mapper from rs_plan) rsp on rss.plan_id = rsp.plan_id where reseller_company_id={companyID} and supplier_id = {oriSheet.supcust_id}";
            dynamic resellerInfo = await CDbDealer.Get1RecordFromSQLAsync(resellerInfoSql, cmd);

            sheet.company_id = (string)resellerInfo.company_id;
            if((string)resellerInfo.client_mapper == "sellerAsClient")
            {
                string sellerClientSql = $"select rs_client_id from info_operator where oper_id = {sellerId}";
                dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sellerClientSql, cmd);
                // 第一种保护，业务员不在父账户客户列表，不允许开单
                // if(record.rs_client_id == null || record.rs_client_id == "")
                // {
                //     err = "当前业务员不在父账户客户列表";
                // }

                if (record.rs_client_id == null || record.rs_client_id == "")
                {
                    // 第二种，开到子公司客户上
                    sheet.supcust_id = (string)resellerInfo.client_id;
                }
                else
                {
                    sheet.supcust_id = (string)record.rs_client_id;
                }
            }
            else
            {
                sheet.supcust_id = (string)resellerInfo.client_id;
            }

            sheet.senders_id = "";
            sheet.senders_name = "";
            sheet.branch_id = "0";
            sheet.sheet_id = "";
            sheet.sheet_no = "";
            sheet.sheet_type = SHEET_TYPE.SHEET_SALE_DD;
            sheet.maker_id = "1";
            sheet.maker_name = "";
            sheet.approver_id = "";
            sheet.approve_time = "";
            sheet.approve_brief = "";

            // TODO delete

            foreach (SheetRowSaleOrder row in sheet.SheetRows)
            {
                string itemId = row.item_id;
                string itemIdSql = $"select item_brand,rs_mum_id from info_item_prop where item_id = {itemId} and company_id={companyID}";
                dynamic sonItemInfo = await CDbDealer.Get1RecordFromSQLAsync(itemIdSql, cmd);

                // 查询品牌是否在分销方案里，确保方案修改之后，不包括该品牌商品无法开同步单据
                // 不包括的商品之后是否支持分销商自己开单？
                dynamic sonBrandInfo = await CDbDealer.Get1RecordFromSQLAsync($"select rs_mum_id from info_item_brand where brand_id = {sonItemInfo.item_brand}", cmd);

                string planBrandSql = $"select * from rs_plan where plan_id = {resellerInfo.plan_id} AND ',' || brand_id || ',' LIKE '%,{sonBrandInfo.rs_mum_id},%';";
                dynamic planBrandRet = await CDbDealer.Get1RecordFromSQLAsync(planBrandSql, cmd);
                if (planBrandRet == null)
                {
                    err = $"商品“{row.item_name}”品牌不在分销方案内";
                }
                string fatherItemId = sonItemInfo.rs_mum_id;
                if (fatherItemId != null && fatherItemId != "")
                {
                    string querySql = $"select item_id,item_class,other_class from info_item_prop where item_id = {fatherItemId} and company_id={sheet.company_id}";
                    dynamic itemInfo = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);
                    row.item_id = itemInfo.item_id;
                    row.classId = itemInfo.item_class;
                    row.other_class = itemInfo.other_class;
                    row.inout_flag = row.inout_flag * (-1);
                }
                else
                {
                    err = $@"主公司档案没有商品:{row.item_name}";
                    break;
                }



            }
            if (err=="")
                err = await sheet.Save(cmd,false);
            if (err != "") { return new { msg = err  }; }
            var ret = new { msg = "", bindSheetInfo = new { bindSheetId = sheet.sheet_id, bindSheetNo = sheet.sheet_no, bindSheetType = sheet.SheetType, companyId = sheet.company_id } };

            // return JsonConvert.SerializeObject(ret);
            return ret;
        }

        public async Task<string> DoCommonJobBeforeSaveApprove(CMySbCommand cmd)
        {
            if (!IsImported)
            {
                string orderSource = "";
                if (sheet_id != "")
                {
                    string orderSourceSql = $"select  * from sheet_buy_order_main where company_id = {company_id} and supcust_id = {supcust_id} and sheet_id ={sheet_id};";
                    dynamic dbSheet = await CDbDealer.Get1RecordFromSQLAsync(orderSourceSql, cmd);
                    orderSource = dbSheet.order_source;
                }
                if (orderSource == "") orderSource = order_source;
                string rsSellerSql = $@"
    select rs.*,rp.sheet_sync from rs_seller rs
    LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
        where rs.reseller_company_id={company_id} and rs.supplier_id = {supcust_id};";
                dynamic rsSeller = await CDbDealer.Get1RecordFromSQLAsync(rsSellerSql, cmd);

                string msg = "";
                if (rsSeller != null && rsSeller.sheet_sync.ToLower() == "true" && orderSource != "2fx")
                {
                    dynamic ret = "";
                    try
                    {
                        ret = await SaveAsSaleOrderSheet(cmd, this);
                        if (ret.msg != "")
                        {
                            return ret.msg;
                        }
                        else
                        {
                            this.bindSheetInfo = JsonConvert.SerializeObject(ret.bindSheetInfo);
                        }

                    }
                    catch (Exception e)
                    {
                        msg = "生成上级销售订单出错";
                        // info.ErrMsg = msg;
                        MyLogger.LogMsg($"In SaveAndApprove,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);


                    }

                }
                return msg;
            }
            return "";
        }

        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd, CInfoForApproveBase info)
        {
            string msg = await DoCommonJobBeforeSaveApprove(cmd);
            return msg;
        }


        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            if (FIXING_ARREARS) return "";
            string sql = "";
            CInfoForApprove info = (CInfoForApprove)info1;
            string rememberBranchID = "0";


            //string ic = JsonConvert.SerializeObject(info.CompanySetting);
            //Boolean rememberBuyPriceByBranch = JsonConvert.DeserializeObject(info.CompanySetting)["rememberBuyPriceByBranch"];
            //if (ic != null && rememberBuyPriceByBranch)

            bool rememberBuyPriceByBranch = false;
            if(info.CompanySetting!=null && info.CompanySetting.rememberBuyPriceByBranch != null)
            {
                rememberBuyPriceByBranch = ((string)info.CompanySetting.rememberBuyPriceByBranch).ToLower() == "true";
            }
            
            
            if (info.CompanySetting != null && rememberBuyPriceByBranch )
            {
                rememberBranchID = branch_id;
            }
            foreach (var row in info.UnitPriceRows)
            {
                var itemID = row.son_mum_item != "" ? row.son_mum_item:row.item_id;
                if (row.orig_price != null && row.orig_price != "")
                    sql += $@"insert into supplier_recent_prices (company_id,supcust_id,branch_id,item_id,unit_no,recent_price,recent_orig_price,happen_time) values ({company_id},{supcust_id},{rememberBranchID},{itemID},'{row.unit_no}',{row.real_price},{row.orig_price},'{happen_time}') 
                        on conflict(company_id,supcust_id,branch_id,item_id,unit_no) do update set recent_price={row.real_price},recent_orig_price={row.orig_price},happen_time = '{happen_time}';";
                else sql += $@"insert into supplier_recent_prices (company_id,supcust_id,branch_id,item_id,unit_no,recent_price,happen_time) values ({company_id},{supcust_id},{rememberBranchID},{itemID},'{row.unit_no}',{row.real_price},'{happen_time}') 
                        on conflict(company_id,supcust_id,branch_id,item_id,unit_no) do update set recent_price={row.real_price},happen_time = '{happen_time}' ;";

            }
            //string sqlUpdateRecentProduceDate = "";
            //string sNow = CPubVars.GetDateText(DateTime.Now);
            //foreach (var row in info.SheetRows)
            //{
            //    if (row.produce_date != "")
            //    {
            //        sqlUpdateRecentProduceDate += $"insert into item_recent_produce_date (company_id,item_id,produce_date,happen_time) values ({company_id},{row.item_id},'{row.produce_date}','{sNow}') on conflict(company_id,item_id) do update set produce_date='{row.produce_date}',happen_time='{sNow}';";
            //    }
            //}
            //sql += sqlUpdateRecentProduceDate;
            return sql;
        }

        public SheetBuy ToBuySheet(string operKey)
        {
            SheetBuy buySheet = JsonConvert.DeserializeObject<SheetBuy>(JsonConvert.SerializeObject(this));

            buySheet.sheet_type = SHEET_TYPE.SHEET_BUY;
            if (this.sheet_type == SHEET_TYPE.SHEET_BUY_DD_RETURN) buySheet.sheet_type = SHEET_TYPE.SHEET_BUY_RETURN;

            buySheet.order_sheet_id = this.sheet_id;
            buySheet.order_sheet_no = this.sheet_no;
            buySheet.OperID = OperID;
            buySheet.sheet_id = "";
            buySheet.sheet_no = "";
            buySheet.approver_id = "";
            buySheet.approver_name = "";
            buySheet.approve_time = "";
            buySheet.approve_brief = "";
            buySheet.make_time = "";
            buySheet.maker_id = "";
            buySheet.maker_name = "";
            buySheet.happen_time = "";
            //buySheet.license_no = license_no;
            buySheet.SheetRows = new List<SheetRowBuy>();
            foreach(var row in this.SheetRows)
            {
                var orderRow = JsonConvert.DeserializeObject<dynamic>(JsonConvert.SerializeObject(row));
                decimal unit_factor = orderRow.unit_factor;
                decimal order_qty = orderRow.quantity;
                decimal done_qty = row.done_qty;
                var qty = order_qty* unit_factor - done_qty;
                if (Math.Abs(qty) < 0.01m) continue;
                orderRow.order_qty = order_qty * unit_factor;
                orderRow.quantity = qty/ unit_factor;
                SheetRowBuy rowBuy = JsonConvert.DeserializeObject<SheetRowBuy>(JsonConvert.SerializeObject(orderRow));
                rowBuy.sub_amount = rowBuy.quantity * rowBuy.real_price;
                buySheet.SheetRows.Add(rowBuy);
            }
            


            buySheet.OperKey = operKey;
            buySheet.Init();
            return buySheet;
        }
        
        //protected override async Task<string> CheckSheetRowValid(CMySbCommand cmd)
        //{
        //    foreach (dynamic row in this.SheetRows)
        //    {
        //        string branchID = row.branch_id;
        //        string branchName = row.branch_name;
        //        if (branchID.IsInvalid())
        //        {
        //            branchID = branch_id;
        //            branchName = branch_name;
        //        }
        //        if (row.branch_position == "0" || string.IsNullOrWhiteSpace(row.branch_position)) continue;
        //        dynamic record = await CDbDealer.Get1RecordFromSQLAsync($"select flow_id from info_branch_position where company_id = {company_id} and branch_position = {row.branch_position} and branch_id = {branchID};", cmd);
        //        if (record == null)
        //        {
        //            return $"{branchName}不存在库位：{row.branch_position_name}";
        //        }
        //    }
        //    return "OK";

        //}
    }
}

