﻿using System;
using System.Collections.Generic;
 
 
using System.Xml;
using ArtisanManage.Models;
namespace myJXC
{  
    class COughtMoney
    {
        public double amount = 0;
        public string supcust_no = "";
    }
    public class CSheetSale : CSheet
    { 
        public string m_OrderNo = "";
       
        public string m_in_branch_no = "";
        public string m_in_branch_name = "";

        public string m_in_branch_man_id = "";
        public string m_in_branch_man_name = "";

        public string m_orig_in_branch_no = "";
        public string m_orig_in_branch_name = "";

        public string m_orig_branch_no = "";
        public string m_orig_branch_name = "";


        public string m_branch_man_id = "";
        public string m_branch_man_name = "";
        public string m_CardIDStart = "";
        public string m_CardIDNew = "";
        public string m_CardTypeNew = "";
        public bool m_bGotOrLost = false;
        public string m_cardChangeReason = "";
        public int m_CardQuantity = 0;
        public double m_CardPrice = 0;
        //public string m_CardIDEnd = "";
               
        public double m_pay_money_transfer_fee = 0;
        public int m_invoice_type = 0;
        public double m_tax_amount = 0;
   
        public bool m_bPrintFinished = false;
        public bool m_bPrintTimeout = false;
        public string m_sPrintError = "";
       
        //public bool m_bTakeOutToEat = false;
        public List<string> m_lstChefPrompts = null;
        public bool m_bSaveToPosDb = false;
        public string m_good_inout_flag = "";
      
        public string m_duty_no_to_submit = "";
        public string m_pack_box_no = ""; public string m_box_prefix = "";
        public string m_ClientSubmitTime = ""; public string m_ipnum = "";

        public string m_AlreadySubmitOKTime = "";
        public SheetRow m_OrigNormRowToChange = null;
        public string m_FeeItemNo = ""; public string m_FeeItemName = "";
        public string m_department_id = "";public string m_department_name = "";
        public string m_room_id = ""; public string m_room_name = "";
        public string m_vip_left_amount ="";
        public string m_vip_left_integral = "";

        public string m_push_reduce_amt = "";
        public string m_push_give_ticket = "";
        public string m_push_prompt = "";
        public bool m_bApprovePaidSheet = false;
        public string m_vip_mum_card = "";
        public bool m_bUpdateAllRows = false;
        public string m_curUpdateRow = "";
        public bool m_bUpdateMoneyForQtyChange = false;

        public LinkedList<CVariable> m_lstSheetPrtVariables = null;
         

        public static string GetNewSheetNo(SHEET_TYPE sheetType,string com_no, CMySbConnection conn, CMySbCommand cmd)
        {
            string sheetFlag = "";
            sheetFlag = StrFromSheetType(sheetType);
            //string sheetFlag = "";
            //sheetFlag = StrFromSheetType(sheetType);
            return GetNewSheetNo(sheetFlag, com_no, conn, cmd);
        }
        public static string GetNewSheetNo(string sheetFlag, string com_no, CMySbConnection conn, CMySbCommand cmd) 
        {
            return GetNewSheetNo(sheetFlag, com_no,"","","", conn, cmd);
        }

        public static string GetNewSheetNo(string sheetFlag, string com_no,string pos_no,string ipnum,string client_submit_time, CMySbConnection conn, CMySbCommand cmd)
        {
            if (conn == null)
            {
                conn = new CMySbConnection(CPubVars.ConnString);
                conn.Open();
            }
            if (cmd == null)
            {
                cmd = new CMySbCommand("", conn);
            }
            string sID = "";
            if (pos_no != "")
            {
                string day = ""; DateTime sheetDay = Convert.ToDateTime("2000-1-1"); int nSheetNo = 0;
                cmd.CommandText = "select cur_sheet_no,sheet_day from pos where pos_no='" + pos_no + "'";
                CMySbDataReader dr = cmd.ExecuteReader();
                if (dr.Read())
                {
                    day = CPubVars.GetTextFromDr(dr, "sheet_day");
                    if (day != "")
                    {
                        sheetDay = Convert.ToDateTime(day).Date;
                    } 
                    object ov = dr["cur_sheet_no"];
                    if (ov != DBNull.Value)
                    {
                        nSheetNo = Convert.ToInt32(ov);
                    }
                }
                dr.Close();

                DateTime dtNow=DateTime.Now;
                string sNow=CPubVars.GetDateText(dtNow.Date);
               
             
                cmd.CommandText = "Select CONVERT(varchar(100), GETDATE(), 120)";
                object ovtime = cmd.ExecuteScalar();
                if (ovtime != null && ovtime != DBNull.Value)
                {                        
                    dtNow = Convert.ToDateTime(ovtime);
                    sNow = CPubVars.GetDateText(dtNow.Date);
                }
              

                if (day == "" || sheetDay < dtNow.Date)
                {
                    cmd.CommandText = "update pos set cur_sheet_no='2',sheet_day='" + sNow + "' where pos_no ='" + pos_no + "'";
                    cmd.ExecuteNonQuery();
                    nSheetNo = 1;
                }
                else if (sheetDay == dtNow.Date)
                {
                    cmd.CommandText = "update pos set cur_sheet_no=cur_sheet_no +1 where pos_no ='" + pos_no + "'";
                    cmd.ExecuteNonQuery();
                }
                else if (sheetDay > dtNow.Date)
                {
                    cmd.CommandText = "update pos set cur_sheet_no='6001' where pos_no ='" + pos_no + "'";
                    cmd.ExecuteNonQuery();
                    nSheetNo = 6000;
                    //cmd.CommandText = "update sheet_no set sheet_value=sheet_value+1 where sheet_id ='" + sheetFlag + "'" + com_condi;
                    //cmd.ExecuteNonQuery();
                }

                string sSheetNo = nSheetNo.ToString();
                sSheetNo = sSheetNo.PadLeft(4, '0');

                DateTime dt1 = DateTime.Now;
                if (client_submit_time != "")
                    dt1 = Convert.ToDateTime(client_submit_time);

                string sY = DateTime.Now.Year.ToString();
                sY = sY.Substring(sY.Length - 2, 2);

                string sM = DateTime.Now.Month.ToString();
                sM = sM.PadLeft(2, '0');
                string sD = DateTime.Now.Day.ToString();              
                sD = sD.PadLeft(2, '0');
                string sHour=dt1.Hour.ToString().PadLeft(2,'0');
                string sMinute=dt1.Minute.ToString().PadLeft(2,'0');
                string sSecond=dt1.Second.ToString().PadLeft(2,'0');
                if (ipnum != "")
                {
                    ipnum ="P" + ipnum.PadLeft(3, '0');
                    pos_no = "";
                }
              
               sID = com_no + sheetFlag + sY + sM + sD + sHour + sMinute + sSecond + pos_no + ipnum + sSheetNo;
                 
            }
            else  
            {
                string com_condi = "";
                if (com_no != "")
                    com_condi = " and com_no='" + com_no + "'";
                else
                    com_condi = " and (com_no is null or com_no ='')";

                cmd.CommandText = "if not exists(select * from sheet_no where sheet_id ='" + sheetFlag + "'" + com_condi + ") insert into sheet_no (com_no,sheet_id,sheet_value) values ('" + com_no + "','" + sheetFlag + "','1');";
                cmd.ExecuteNonQuery();
                
                cmd.CommandText = "Select CONVERT(varchar(100), GETDATE(), 120)";
                object ov = cmd.ExecuteScalar();
                DateTime dtNow = Convert.ToDateTime(ov);
                string set_name = "SheetDay" + sheetFlag;
                cmd.CommandText = "if not exists(select set_value from setting where set_name ='" + set_name + "') insert into setting (set_name,set_value) values ('" + set_name + "','2000-1-1');";

                cmd.ExecuteNonQuery();

                cmd.CommandText = "select set_value from setting where set_name='SheetDay" + sheetFlag + "'";
                ov = cmd.ExecuteScalar();
                string day = ""; DateTime sheetDay = Convert.ToDateTime("2000-1-1");

                if (ov != null && ov != DBNull.Value)
                {
                    day = ov.ToString();
                    sheetDay = Convert.ToDateTime(ov);
                }
                if (day == "" || sheetDay < dtNow.Date)
                {
                    cmd.CommandText = "update setting set set_value ='" + CPubVars.GetDateText(dtNow.Date) + "' where set_name='SheetDay" + sheetFlag + "'";
                    cmd.ExecuteNonQuery();
                    cmd.CommandText = "update sheet_no set sheet_value='0' where sheet_id ='" + sheetFlag + "'" + com_condi;
                    cmd.ExecuteNonQuery();
                }
                else if (sheetDay > dtNow.Date)
                {
                    cmd.CommandText = "update sheet_no set sheet_value='6000' where sheet_id ='" + sheetFlag + "' and sheet_value<'6000'" + com_condi;
                    cmd.ExecuteNonQuery();
                    //cmd.CommandText = "update sheet_no set sheet_value=sheet_value+1 where sheet_id ='" + sheetFlag + "'" + com_condi;
                    //cmd.ExecuteNonQuery();
                }
                cmd.CommandText = "select * from sheet_no where sheet_id ='" + sheetFlag + "'" + com_condi + ";update sheet_no set sheet_value=sheet_value+1 where sheet_id ='" + sheetFlag + "'" + com_condi;
                CMySbDataReader dr = cmd.ExecuteReader();

                if (!dr.Read())
                {
                    dr.Close();
                    return "不存在单据标示" + sheetFlag;
                }

                int nSheetNo = Convert.ToInt32(dr["sheet_value"]);
                string sSheetNo = "";
                nSheetNo = nSheetNo + 1;

                sSheetNo = nSheetNo.ToString();
                sSheetNo = sSheetNo.PadLeft(4, '0');

                DateTime dt1 = DateTime.Now;
                string sY = dt1.Year.ToString();
                sY = sY.Substring(sY.Length - 2, 2);

                string sM = dt1.Month.ToString();
                sM = sM.PadLeft(2, '0');
                string sD = dt1.Day.ToString();
                sD = sD.PadLeft(2, '0');
                sID = com_no + sheetFlag + sY + sM + sD + sSheetNo;
                dr.Close();
            } 
            return sID;
             
        }

       public CSheetSale(SHEET_TYPE SheetType)
        {
            m_SheetType = SheetType;
        }
        public CSheetSale()
        {
             
        } 
        public string SetPayInfo(double cash_amout, double card_amount, double ticket_amount, double bank_amount, double integral_amount, int reduce_integral, string card_id, string bank_box_id, string cash_box_id, double money_transfer_fee, int invoice_type, double tax_amount)
        {
            //m_pay_order_amount = order_amount;
            m_pay_cash_amount = cash_amout;
            m_pay_card_amount = card_amount;
            m_pay_ticket_amount = ticket_amount;
            m_pay_bank_amount = bank_amount;
            m_pay_integral_amount = integral_amount;
            m_pay_reduce_integral = reduce_integral;
            m_pay_card_id = card_id;
            m_pay_bank_box_id = bank_box_id;
            m_pay_cash_box_id = cash_box_id;
            m_pay_money_transfer_fee = money_transfer_fee;
            m_invoice_type = invoice_type;
            m_tax_amount = tax_amount;
            return "";

        }
        public string SetGeneralInfo(string branch_no, string branch_name, string supcust_no, string supcust_name, string oper_id, string oper_name, string work_man, string work_man_name, string pay_way, string pay_name, string discount, string total_amount, string ship_amount, string paid_amount, string disc_amt, string oper_date, string work_date, string brief, bool bIgnoreErr)
        { 
            string s = "";
            //s = CPubVars.GetItemID(branch_no);
            //if (s == "") return "请指定仓库";
            m_branch_no = branch_no;
            m_branch_name = branch_name;// CPubVars.GetItemName(branch_no);
            //m_branch_no_l = branch_no;
            //s = CPubVars.GetItemID(supcust_no);
            //if (s == "") return "请指定客户";
            m_supcust_no = supcust_no;
            m_supcust_name = supcust_name;// CPubVars.GetItemName(supcust_no);

            //s = CPubVars.GetItemID(pay_way);
            //if (s == "") return "请指定支付方式";
           // m_pay_way = pay_way;
           // m_pay_name = pay_name;

            //s = CPubVars.GetItemID(work_man);
            //if (s == "") return "请指定销售人员";
            m_work_man = work_man;
            m_work_man_name = work_man_name;

            m_oper_id = oper_id;
            m_oper_name = oper_name;
            s = discount;
            bool bNoMoney = false;
            if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD || m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD || m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                bNoMoney = true;
            if (!bNoMoney)
            {
                if (discount == "") return "请指定折扣";
                if (!CPubVars.IsNumeric(s)) return "折扣应为数字";
                m_discount = Convert.ToDouble(s);

                s = total_amount;
                if (s == "") return "请指定总额";
                if (!CPubVars.IsNumeric(s)) return "总额应为数字";
                m_total_amount = Convert.ToDouble(s);

                s = total_amount;
                if (s == "") return "请指定总额";
                if (!CPubVars.IsNumeric(s)) return "总额应为数字";
                m_total_amount = Convert.ToDouble(s);

                m_ship_amount = 0;
                if (CPubVars.IsNumeric(ship_amount))
                    m_ship_amount = Convert.ToDouble(ship_amount);

                s = paid_amount;
                if (s == "")
                {
                    if (!bIgnoreErr)
                        return "请指定已付金额";
                }
                if (!CPubVars.IsNumeric(s))
                {
                    if (!bIgnoreErr)
                        return "已付金额应为数字";
                }
                else
                    m_paid_amount = Convert.ToDouble(s);

                s = disc_amt;
                if (s == "") s = "0";
                if (!CPubVars.IsNumeric(s))
                {
                    if (!bIgnoreErr)
                        return "折扣金额应为数字";
                }
                else
                {
                    m_disc_amt = Convert.ToDouble(s);
                }
                if (!CPubVars.IsNumeric(s))
                {
                    if (!bIgnoreErr)
                        return "合计金额必须是数字";
                }
                //if (m_total_amount - m_paid_amount - m_disc_amt >= 0.01)
                //{
                //    if (!bIgnoreErr)
                //    {
                //        if (supcust_no == "")
                //        {
                //            if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                //                return "此单据有欠款，必须指定客户";
                //            else
                //                return "此单据有欠款，必须指定供应商";
                //        }
                //    }
                //}

            }


            s = oper_date;
            //if (s == "") return "请指定操作日期";
            if (s != "" && !CPubVars.IsDate(s)) return "操作日期格式不正确，不是合法的日期";
            if (oper_date != "")
            {
                DateTime opdt = Convert.ToDateTime(oper_date);
                if (opdt.Year > 2001)
                    m_oper_date = opdt;
            }
            else
            {
                m_oper_date = DateTime.Now;
            }
            s = work_date;
            //if (s == "") return "请指定工作日期";
            if (s != "" && !CPubVars.IsDate(s)) return "采购日期格式不正确，不是合法的日期";
            if (work_date != "")
            {
                DateTime opdt = Convert.ToDateTime(work_date);
                if (opdt.Year > 2001)
                    m_work_date = opdt;                
            }
            else
            {
                m_work_date = DateTime.Now;
            }
            m_coin_no = "RMB";
            m_brief = brief;
            // base.SetGeneralInfo(branch_no,supcust_no,pay_way,discount,total_amount,paid_amount,disc_amt, oper_id,work_man,oper_date,work_date);
            return "";

        }
        public string SetContactInfo(string cust_addr, string cust_tel, string cust_contact, string send_time,string send_time_range)
        {
            if (send_time != "")
            {
                if (!CPubVars.IsDate(send_time))
                    return "送货时间必须为数字";
            }
            m_cust_addr = cust_addr; m_cust_tel = cust_tel; m_cust_contact = cust_contact; m_send_time = send_time;
            m_send_time_range = send_time_range;
            return "";
        }
        public string SetCardGeneralInfo(string branch_no, string branch_name, string supcust_no, string supcust_name, string oper_id, string oper_name, string work_man, string work_man_name, string pay_way, string pay_name, string discount, string total_amount, string paid_amount, string disc_amt, string oper_date, string work_date, string card_start, int card_quantity,double card_price, string card_pwd, string card_type, double card_money, double card_integral, string till_date,string card_remark)
        {

            string s = "";
            //s = CPubVars.GetItemID(branch_no);
            //if (s == "") return "请指定仓库";
            m_branch_no = branch_no;
            m_branch_name = branch_name;// CPubVars.GetItemName(branch_no);
            //m_branch_no_l = branch_no;
            //s = CPubVars.GetItemID(supcust_no);
            //if (s == "") return "请指定客户";
            m_supcust_no = supcust_no;
            m_supcust_name = supcust_name;// CPubVars.GetItemName(supcust_no);

            //s = CPubVars.GetItemID(pay_way);
            //if (s == "") return "请指定支付方式";
            m_pay_way = pay_way;
            m_pay_name = pay_name;

            //s = CPubVars.GetItemID(work_man);
            //if (s == "") return "请指定销售人员";
            m_work_man = work_man;
            m_work_man_name = work_man_name;

            m_cardTillDate = till_date;

            m_oper_id = oper_id;
            m_oper_name = oper_name;
            s = discount;
            bool bNoMoney = false;

            if (!bNoMoney)
            {
                if (discount == "") return "请指定折扣";
                if (!CPubVars.IsNumeric(s)) return "折扣应为数字";
                m_discount = Convert.ToDouble(s);

                s = total_amount;
                if (s == "") return "请指定总额";
                if (!CPubVars.IsNumeric(s)) return "总额应为数字";
                m_total_amount = Convert.ToDouble(s);

                s = total_amount;
                if (s == "") return "请指定总额";
                if (!CPubVars.IsNumeric(s)) return "总额应为数字";
                m_total_amount = Convert.ToDouble(s);

                s = paid_amount;
                if (s == "") return "请指定已付金额";
                if (!CPubVars.IsNumeric(s)) return "已付金额应为数字";
                m_paid_amount = Convert.ToDouble(s);

                s = disc_amt;
                if (s == "") s = "0";
                if (!CPubVars.IsNumeric(s)) return "折扣金额应为数字";
                m_disc_amt = Convert.ToDouble(s);

                if (!CPubVars.IsNumeric(s))
                {
                    return "合计金额必须是数字";
                }
              

            }

            s = oper_date;
            //if (s == "") return "请指定操作日期";
            if (s != "" && !CPubVars.IsDate(s)) return "操作日期格式不正确，不是合法的日期";
            if (oper_date != "")
                m_oper_date = Convert.ToDateTime(oper_date);

            s = work_date;
            //if (s == "") return "请指定工作日期";
            if (s != "" && !CPubVars.IsDate(s)) return "工作日期格式不正确，不是合法的日期";
            if (work_date != "")
                m_work_date = Convert.ToDateTime(work_date);

            m_coin_no = "RMB";

            m_CardStoredMoney = card_money;
            m_CardIntegral = card_integral;
            m_CardIDStart = card_start;

            m_CardQuantity = card_quantity;
            m_CardPrice = card_price;
            m_CardPwd = card_pwd;
            m_CardType = card_type;
            m_CardRemark = card_remark;
            //base.SetGeneralInfo(branch_no, supcust_no, pay_way, discount, total_amount, paid_amount, disc_amt, oper_id, work_man, oper_date, work_date);
            return "";

        }

        public string SetMoveSheetGeneralInfo(string out_branch_no, string out_branch_name, string in_branch_no, string in_branch_name, string out_branch_man_id, string out_branch_man_name, string in_branch_man_id, string in_branch_man_name, string oper_id, string oper_name, string total_amount, string oper_date, string work_date)
        {

            string s = "";
            //s = CPubVars.GetItemID(branch_no);
            //if (s == "") return "请指定仓库";
            if (out_branch_no == in_branch_no)
            {
                return "出入仓库不能相同";

            }
            m_in_branch_no = in_branch_no;
            m_in_branch_name = in_branch_name;// CPubVars.GetItemName(branch_no);

            m_branch_no = out_branch_no;
            m_branch_name = out_branch_name;// CPubVars.GetItemName(branch_no);

            m_in_branch_man_id = in_branch_man_id;
            m_in_branch_man_name = in_branch_man_name;// CPubVars.GetItemName(branch_no);

            //m_branch_man_id = out_branch_man_id;
            //m_branch_man_name = out_branch_man_name;// CPubVars.GetItemName(branch_no);

            m_work_man = out_branch_man_id;
            m_work_man_name = out_branch_man_name;// CPubVars.GetItemName(branch_no);


            //s = CPubVars.GetItemID(work_man);
            //if (s == "") return "请指定销售人员";
            // m_work_man = work_man;
            // m_work_man_name = work_man_name;


            m_oper_id = oper_id;
            m_oper_name = oper_name;


            s = total_amount;
            if (s == "") return "请指定总额";
            if (!CPubVars.IsNumeric(s)) return "总额应为数字";
            m_total_amount = Convert.ToDouble(s);

            if (CPubVars.IsDate(oper_date))
                m_oper_date = Convert.ToDateTime(oper_date);


            if (CPubVars.IsDate(work_date))
                m_work_date = Convert.ToDateTime(work_date);

            //base.SetGeneralInfo("", "", "", "", total_amount, "", "", oper_id, "", oper_date, work_date);
            return "";

        }
        public string AddRow(string item_no, string item_name, string unit_no, string unit_factor, string size, string size_name, string color, string color_name, string orig_price, string real_price, string quantity, string money, double discount, string branch_no, string branch_name, bool bService)
        {
            return AddRow(item_no, item_name, "", unit_no, unit_factor, size, size_name, color, color_name, orig_price, real_price, quantity, "0", money, discount, branch_no, branch_name, bService, "", "", "", "");
        }
      
        public SheetRow GetLastRow()
        {
            if (SheetRows.Count > 0)
                return SheetRows[SheetRows.Count-1];
            else
                return null;
        }
        //public string AddRow(string item_no, string item_name, string unit_no, string unit_factor, string size, string size_name, string color, string color_name, string color_group, string orig_price, string real_price, string quantity, string money, double discount, string branch_no, string branch_name, bool bService, string worker_id, string servicer_id)
        public bool m_bQuantityCanEmpty = false;
        public bool m_bPriceCanEmpty = false;
        public string AddRow(string item_no, string item_name, string item_subno, string unit_no, string unit_factor, string size, string size_name, string color, string color_name, string orig_price, string real_price, string quantity, string pend_qty, string money, double discount, string branch_no, string branch_name, bool bService, string worker_id, string servicer_id, string servicer1_id, string brief)
        {
            string sRow = (SheetRows.Count + 1).ToString();
            //CPubVars moneyDeal = new CPubVars();
            string sRowPrompt = "第" + sRow + "行";

            SheetRow sheetRow = new SheetRow();

            if (item_no == "") return sRowPrompt + "未指定商品";
            sheetRow.item_no = item_no;
            sheetRow.item_subno = item_subno;
            sheetRow.item_name = item_name;
            // if (unit_no == "") return sRowPrompt + "未指定单位";
            sheetRow.unit_no = unit_no;

            if (unit_factor == "") return sRowPrompt + "未指定单位因子";
            if (!CPubVars.IsNumeric(unit_factor)) return sRowPrompt + "单位因子必须为数字";
            sheetRow.unit_factor = Convert.ToDouble(unit_factor);
            sheetRow.is_service = bService;
            sheetRow.brief = brief;
            //if (orig_price == "") return sRowPrompt + "未指定原始价";
            //if (!CPubVars.IsNumeric(orig_price)) return sRowPrompt + "原始价必须为数字";
            bool bNoMoney = false;
            if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD || m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD || m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                bNoMoney = true;
            if(!m_bQuantityCanEmpty)
                if (quantity == "") return sRowPrompt + "未指定数量";
            if (quantity != "" && !CPubVars.IsNumeric(quantity)) return sRowPrompt +sheetRow.item_name + "的数量必须为数字";
            if(CPubVars.IsNumeric(quantity))
               sheetRow.quantity = Convert.ToDouble(quantity);
            if (pend_qty != "") sheetRow.pend_qty = Convert.ToDouble(pend_qty);
            sheetRow.color_id = color;

            sheetRow.size_id = size;
            sheetRow.color_name = color_name;
            sheetRow.size_name = size_name;
            //sheetRow.bNeedProduceDate = (CPubVars.g_bColorAsProduceDate && color_group != "");
            if (!bNoMoney)
            {
                if (orig_price != "" && !CPubVars.IsNumeric(orig_price))
                {
                    orig_price = "";
                }
                //  return sRowPrompt + "原始价必须为数字";
                if (!m_bPriceCanEmpty)
                {
                    if (real_price == "") return sRowPrompt + sheetRow.item_name + "未指定实际价";
                }
                if (real_price != "" && !CPubVars.IsNumeric(real_price)) return sRowPrompt + sheetRow.item_name + "实际价必须为数字";

                /*if (store_quantity == "") store_quantity="0";
                if (!CPubVars.IsNumeric(quantity)) return sRowPrompt + "数量必须为数字";
                sheetRow.m_store_quantity = Convert.ToDouble(store_quantity);
                */
                if (money == "") return sRowPrompt + "未指定单项金额";
                if (!CPubVars.IsNumeric(money)) return sRowPrompt + "单项金额必须为数字";

            }
            if (CPubVars.IsNumeric(orig_price))
                sheetRow.orig_price = Convert.ToDouble(orig_price);
            if (CPubVars.IsNumeric(real_price))
                sheetRow.real_price = Convert.ToDouble(real_price);
            double sub_amount = sheetRow.real_price * sheetRow.quantity * sheetRow.unit_factor;

            double tax_amount = 0; double tax_rate = 0;
            if (m_invoice_type == 0)
                tax_rate = 0;// CPubVars.m_TaxRateShouJu;
            else if (m_invoice_type == 1)
                tax_rate = 0;// CPubVars.m_TaxRatePuPiao;
            else if (m_invoice_type == 2)
                tax_rate = 0.17;// CPubVars.m_TaxRateZengPiao;
            tax_amount = sub_amount / (1 + tax_rate) * tax_rate;
            sheetRow.tax_amount = tax_amount;
            sheetRow.discount = discount;
            if (CPubVars.IsNumeric(money))
                sheetRow.money = Convert.ToDouble(money);
            sheetRow.branch_no = branch_no;
            sheetRow.branch_name = branch_name;
            sheetRow.worker_id = worker_id;
            sheetRow.servicer_id = servicer_id;
            sheetRow.servicer1_id = servicer1_id;
            SheetRows.Add(sheetRow);
            //base.AddRow(item_no, unit_no, unit_factor, orig_price, real_price, quantity, money);
            return "";

        }
        public string LoadSheet(string Sheet_No)
        {  
            CMySbConnection conn = new CMySbConnection(CPubVars.ConnString);
            conn.Open();
            CMySbCommand cmd = new CMySbCommand("", conn);
            string err = LoadSheet(Sheet_No,conn,cmd);
            conn.Close();
            return err;
        }
        public string LoadSheet(string Sheet_No,CMySbConnection conn, CMySbCommand cmd)
        {            
            CMySbDataReader dr;
            cmd.CommandText = "select * from sheet_sale_master where sheet_no = '" + Sheet_No + "'";
            dr = cmd.ExecuteReader();
            if (!dr.Read())
            {
                dr.Close();             
                return "没有找到单据号"+Sheet_No+"对应的数据记录";
            }
            string rowCondi = "";

            m_sheet_no = Sheet_No;
            string trans_no = "";
            if (dr["trans_no"] != DBNull.Value) trans_no = dr["trans_no"].ToString().Trim();
            
            m_com_no = CPubVars.GetTextFromDr(dr, "com_no");
            m_com_name = CPubVars.GetTextFromDr(dr, "com_name");
            m_SheetType = SheetTypeFromStr(trans_no);

            if (dr["total_quantity"] != DBNull.Value) m_total_qty = Convert.ToDouble(dr["total_quantity"]);

            if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
            {
                if (m_good_inout_flag == "")
                    rowCondi = " and (inout_flag=-1 or now_pend_qty<>0)";
                else
                    rowCondi = " and inout_flag='" + m_good_inout_flag + "'";
                if (dr["d_branch_no"] != DBNull.Value) m_in_branch_no = dr["d_branch_no"].ToString().Trim();
                //if (dr["order_man"] != DBNull.Value) m_branch_man_id = dr["order_man"].ToString().Trim();
                if (dr["d_branch_man"] != DBNull.Value) m_in_branch_man_id = dr["d_branch_man"].ToString().Trim();

                m_store_approver = CPubVars.GetTextFromDr(dr, "branch_approve_oper");
                m_store_approve_time = CPubVars.GetTextFromDr(dr, "branch_approve_time");

                m_in_store_approver = CPubVars.GetTextFromDr(dr, "approve_oper");
                m_in_store_approve_time = CPubVars.GetTextFromDr(dr, "approve_time");
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT || m_SheetType == SHEET_TYPE.SHEET_STORE_IN)
            {
                if (dr["order_sheet_no"] != DBNull.Value) m_OrigSheetNo = dr["order_sheet_no"].ToString().Trim();                
            } 
            else if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD || m_SheetType ==SHEET_TYPE.SHEET_SALE_DD_RETURN || m_SheetType ==SHEET_TYPE.SHEET_BUY_DD || m_SheetType ==SHEET_TYPE.SHEET_BUY_DD_RETURN)
            {
                if (dr["other1"] != DBNull.Value) m_CardIDStart = dr["other1"].ToString().Trim();
                m_prepay_fill_sub_id = CPubVars.GetTextFromDr(dr, "other2");
                if (CPubVars.IsNumeric(dr["other3"])) m_CardPrice = Convert.ToDouble(dr["other3"]);
                if (CPubVars.IsNumeric(dr["other4"])) m_CardStoredMoney = Convert.ToDouble(dr["other4"]);
                m_bCanceled = (CPubVars.GetTextFromDr(dr, "cancel_flag") == "1");
            } 
            else if (m_SheetType == SHEET_TYPE.SHEET_FEE_OUT || m_SheetType == SHEET_TYPE.SHEET_FEE_IN)
            {
                m_FeeItemNo = CPubVars.GetTextFromDr(dr, "other1"); 
            }
           

            if (m_SheetType == SHEET_TYPE.SHEET_SALE)
            {                
                string other2 = CPubVars.GetTextFromDr(dr, "other2");
                if (other2.StartsWith("evt:"))
                {
                    string[] arr = other2.Split(';');
                    foreach (string ss in arr)
                    {
                        string[] arr1 = ss.Split(':');
                        if (arr1.Length == 2)
                        {
                            if (arr1[0] == "evt")
                                m_event = arr1[1];
                            else if (arr1[0] == "person")
                                m_eventPerson = arr1[1];
                            else if (arr1[0] == "age")
                                m_eventPersonAge = arr1[1];                             
                        }
                    }
                }
            }

            if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
            {
                m_vip_left_amount = CPubVars.GetTextFromDr(dr, "vip_left_amount");
                m_vip_left_integral = CPubVars.GetTextFromDr(dr, "vip_left_integral");
                m_now_money_amount = m_vip_left_amount;
                m_now_integral_amount = m_vip_left_integral;
                m_now_give_amount = CPubVars.GetTextFromDr(dr, "vip_give_amount");
                object ovIntegral = dr["add_integral"];
                if(ovIntegral!=DBNull.Value)
                   m_AddIntegral = Convert.ToInt32(ovIntegral); 

            }
             

            if (m_SheetType == SHEET_TYPE.SHEET_BUY)
            {
                m_remote_com_no =CPubVars.GetTextFromDr(dr,"remote_com_no");
                m_remote_com_name =CPubVars.GetTextFromDr(dr,"remote_com_name");
            }
            if (dr["branch_no"] != DBNull.Value) m_branch_no = dr["branch_no"].ToString().Trim();
            m_op_store = CPubVars.GetTextFromDr(dr, "op_store");
            if (dr["supcust_no"] != DBNull.Value) m_supcust_no = dr["supcust_no"].ToString().Trim();


            if (dr["cust_addr"] != DBNull.Value) m_cust_addr = dr["cust_addr"].ToString().Trim();
            if (dr["cust_tel"] != DBNull.Value) m_cust_tel = dr["cust_tel"].ToString().Trim();
            if (dr["cust_contact"] != DBNull.Value) m_cust_contact = dr["cust_contact"].ToString().Trim();
            if (dr["send_time"] != DBNull.Value) m_send_time = dr["send_time"].ToString().Trim();
            if (dr["send_time_range"] != DBNull.Value) m_send_time_range = dr["send_time_range"].ToString().Trim();
            m_GoodGetterName = CPubVars.GetTextFromDr(dr, "good_getter");
            m_bSendByTel=CPubVars.GetTextFromDr(dr,"send_by_tel")== "1" ? true:false;

            if (dr["pay_way"] != DBNull.Value) m_pay_way = dr["pay_way"].ToString().Trim();
            if (dr["discount"] != DBNull.Value) m_discount = Convert.ToDouble(dr["discount"]);
            if (dr["total_amount"] != DBNull.Value) m_total_amount = Convert.ToDouble(dr["total_amount"]);
         
            if (dr["oper_id"] != DBNull.Value) m_oper_id = dr["oper_id"].ToString().Trim();
            string sale_way = CPubVars.GetTextFromDr(dr, "sale_way");

           
            m_duty_no = CPubVars.GetTextFromDr(dr, "duty_no");
            object ov = dr["now_disc_amount"];
            if (ov != DBNull.Value) m_disc_amt = Convert.ToDouble(ov);

            ov = dr["now_pay_amount"];
            if (ov != DBNull.Value) m_paid_amount = Convert.ToDouble(ov);

            m_redFlag = CPubVars.GetTextFromDr(dr, "red_flag");
         
            if (dr["order_sheet_no"] != DBNull.Value) m_OrigSheetNo = dr["order_sheet_no"].ToString().Trim();
            ov = dr["prepay_left_amount"]; if (ov != null && ov != DBNull.Value) m_prepay_left = Convert.ToDouble(ov);

           
            try
            {
                ov = dr["payway1_name"]; if (ov != DBNull.Value) m_payway1_name = ov.ToString();
                ov = dr["payway1_amount"]; if (ov != DBNull.Value) m_payway1_amount = Convert.ToDouble(ov);
                ov = dr["payway2_name"]; if (ov != DBNull.Value) m_payway2_name = ov.ToString();
                ov = dr["payway2_amount"]; if (ov != DBNull.Value) m_payway2_amount = Convert.ToDouble(ov);
                ov = dr["payway3_name"]; if (ov != DBNull.Value) m_payway3_name = ov.ToString();
                ov = dr["payway3_amount"]; if (ov != DBNull.Value) m_payway3_amount = Convert.ToDouble(ov);

                ov = dr["ship_amount"];
                if (ov != DBNull.Value) m_ship_amount = Convert.ToDouble(ov);
                ov = dr["cash_amount"];
                if (ov != DBNull.Value) m_pay_cash_amount = Convert.ToDouble(ov);
                ov = dr["card_amount"];
                if (ov != DBNull.Value) m_pay_card_amount = Convert.ToDouble(ov);
               // ov = dr["ticket_amount"];
               // if (ov != DBNull.Value) m_pay_ticket_amount = Convert.ToDouble(ov);
                ov = dr["bank_amount"];
                if (ov != DBNull.Value) m_pay_bank_amount = Convert.ToDouble(ov);
                ov = dr["integral_amount"];
                if (ov != DBNull.Value) m_pay_integral_amount = Convert.ToDouble(ov);

                ov = dr["reduce_integral"];
                if (ov != DBNull.Value) m_pay_reduce_integral = Convert.ToInt32(ov);

                ov = dr["prepay_amount"];
                if (ov != DBNull.Value) m_pay_prepay_amount = Convert.ToDouble(ov);

       
              
                m_other_payway = CPubVars.GetTextFromDr(dr, "other_payway");
                ov = dr["other_amount"]; if (ov != DBNull.Value) m_pay_other_amount = Convert.ToDouble(ov);               
                //ov = dr["other1_amount"];
                //if (ov != DBNull.Value) m_pay_other1_amount = Convert.ToDouble(ov);
                //ov = dr["other2_amount"];
                //if (ov != DBNull.Value) m_pay_other2_amount = Convert.ToDouble(ov);
                //ov = dr["other3_amount"];
                //if (ov != DBNull.Value) m_pay_other3_amount = Convert.ToDouble(ov);
                //ov = dr["other4_amount"];
                //if (ov != DBNull.Value) m_pay_other4_amount = Convert.ToDouble(ov);
                //ov = dr["other5_amount"];
                //if (ov != DBNull.Value) m_pay_other5_amount = Convert.ToDouble(ov);
                //ov = dr["other6_amount"];
                //if (ov != DBNull.Value) m_pay_other6_amount = Convert.ToDouble(ov);

                ov = dr["money_transfer_fee"];
                if (ov != DBNull.Value) m_pay_money_transfer_fee = Convert.ToDouble(ov);

                ov = dr["invoice_type"];
                if (ov != DBNull.Value) m_invoice_type = Convert.ToInt32(ov);

                ov = dr["tax_amount"];
                if (ov != DBNull.Value) m_tax_amount = Convert.ToDouble(ov);
                if (dr["money_getter"] != DBNull.Value) m_MoneyGetterID = dr["money_getter"].ToString().Trim();
                if (dr["good_sender"] != DBNull.Value) m_GoodSenderID = dr["good_sender"].ToString().Trim();
                if (dr["order_man"] != DBNull.Value) m_work_man = dr["order_man"].ToString().Trim();
                if (dr["order_man1"] != DBNull.Value) m_work_man1 = dr["order_man1"].ToString().Trim();
              
            }
            catch (Exception) { }

            if (dr["oper_date"] != DBNull.Value) m_oper_date = Convert.ToDateTime(dr["oper_date"]);
            if (dr["work_date"] != DBNull.Value) m_work_date = Convert.ToDateTime(dr["work_date"]);

            m_account_day = CPubVars.GetTextFromDr(dr, "sheet_account_day");

            m_approve_flag = CPubVars.GetTextFromDr(dr, "approve_flag");

            m_bApproved = (m_approve_flag == "1");

            if (dr["money_approver"] != DBNull.Value) m_money_approver_id = dr["money_approver"].ToString();
            
            m_approve_oper = CPubVars.GetTextFromDr(dr, "approve_oper");
            if (m_money_approver_id == "")
                m_money_approver_id = m_approve_oper;
            if (dr["money_approve_time"] != DBNull.Value) m_money_approve_time = CPubVars.GetDateText(dr["money_approve_time"]);

            if (dr["branch_approve_oper"] != DBNull.Value) m_branch_approver_id = dr["branch_approve_oper"].ToString();
            if (dr["branch_approve_time"] != DBNull.Value) m_branch_approve_time = CPubVars.GetDateText(dr["branch_approve_time"]);
 
            if (dr["sheet_brief"] != DBNull.Value) m_brief = dr["sheet_brief"].ToString();
            if (m_brief.Contains("VIP计次:"))
            {
                m_card_times_info = m_brief.Replace("VIP计次:", "");
            }
            // if(dr["disc_amt"]!=DBNull.Value)  m_disc_amt =Convert.ToDouble(dr["disc_amt"]);    
            if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
            {
                m_disc_scheme_name = CPubVars.GetTextFromDr(dr, "disc_scheme");
            }
            dr.Close();


            if (m_SheetType == SHEET_TYPE.SHEET_FEE_OUT || m_SheetType == SHEET_TYPE.SHEET_FEE_IN)
            {
                if(m_FeeItemNo!="")
                {
                    cmd.CommandText = "select fee_item_name from info_fee_out_item where fee_item_no='" + m_FeeItemNo + "'";
                    ov = cmd.ExecuteScalar();
                    if(ov!=null && ov!=DBNull.Value)
                    {
                        m_FeeItemName = ov.ToString();
                    }
                }
                
            }
 
            
            if (m_redFlag == "1")
            {
                cmd.CommandText = "select sheet_no from " + m_tb_master + " where order_sheet_no='" + Sheet_No + "'";
                ov = cmd.ExecuteScalar();
                if (ov != null && ov != DBNull.Value)
                {
                    m_red_sheet_no = ov.ToString().Trim();
                }
            }
            if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN || m_SheetType == SHEET_TYPE.SHEET_BUY || m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN || m_SheetType == SHEET_TYPE.SHEET_SALE_DD || m_SheetType == SHEET_TYPE.SHEET_SALE_DD_RETURN || m_SheetType == SHEET_TYPE.SHEET_STORE_IN || m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
            {
                m_supcust_name = "";
                cmd.CommandText = "select sup_name,vip_batch_type,sup_sex from info_supcust where supcust_no='" + m_supcust_no + "'";
                dr = cmd.ExecuteReader();
                if (dr.Read())
                {
                    m_supcust_name = CPubVars.GetTextFromDr(dr, "sup_name");
                    m_CardType = m_vip_batch_type = CPubVars.GetTextFromDr(dr, "vip_batch_type");
                    m_cust_gender = CPubVars.GetTextFromDr(dr, "sup_sex");
                }
                dr.Close();
            }

            cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_oper_id + "'";
            object rr = cmd.ExecuteScalar();
            m_oper_name = "";
            if (rr != null) m_oper_name = rr.ToString();

            if (m_money_approver_id != "")
            {
                if (m_money_approver_id != m_oper_id)
                {
                    cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_money_approver_id + "'";
                    rr = cmd.ExecuteScalar();
                    m_money_approver_name = "";
                    if (rr != null) m_money_approver_name = rr.ToString();
                }
                else
                    m_money_approver_name = m_oper_name;
            }

            if (m_approve_oper != ""  )
            {
                if(m_approve_oper!=m_oper_id)
                {
                    cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_approve_oper + "'";
                    rr = cmd.ExecuteScalar();
                    m_approve_oper_name = "";
                    if (rr != null) m_approve_oper_name = rr.ToString();
                }
                else
                {
                    m_approve_oper_name = m_oper_name;
                }
            }
            
            if (m_branch_approver_id != "")
            {
                cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_branch_approver_id + "'";
                rr = cmd.ExecuteScalar();
                m_branch_approver_name = "";
                if (rr != null) m_branch_approver_name = rr.ToString();
            }
            if (m_work_man != "")
            {
                cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_work_man + "'";
                rr = cmd.ExecuteScalar();
                m_work_man_name = "";
                if (rr != null) m_work_man_name = rr.ToString();
            }
            if (m_work_man1 != "")
            {
                cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_work_man1 + "'";
                rr = cmd.ExecuteScalar();
                m_work_man1_name = "";
                if (rr != null) m_work_man1_name = rr.ToString();
            }
            if (m_MoneyGetterID != "")
            {
                cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_MoneyGetterID + "'";
                rr = cmd.ExecuteScalar();
                m_MoneyGetterName = "";
                if (rr != null) m_MoneyGetterName = rr.ToString();
            }
            if (m_GoodSenderID != "")
            {
                cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_GoodSenderID + "'";
                rr = cmd.ExecuteScalar();
                m_GoodSenderName = "";
                if (rr != null) m_GoodSenderName = rr.ToString();
            }
            if (m_CardType != "")
            {
                cmd.CommandText = "select type_name from info_vip_type where type_id='" + m_CardType + "'";
                ov = cmd.ExecuteScalar();
                if (ov != null && ov != DBNull.Value)
                    m_CardTypeName = ov.ToString().Trim();
            }

            if (m_department_id != "")
            {
                cmd.CommandText = "select department_name from info_department where department_id='" + m_department_id + "'";
                ov = cmd.ExecuteScalar();
                if (ov != null && ov != DBNull.Value)
                    m_department_name = ov.ToString().Trim();  
            }
            
            cmd.CommandText = "SELECT branch_name FROM info_branch where branch_no='" + m_branch_no + "'";
            rr = cmd.ExecuteScalar();
            m_branch_name = "";
            if (rr != null) m_branch_name = rr.ToString();

            m_in_branch_name = "";
            if (m_in_branch_no != "")
            {
                cmd.CommandText = "SELECT branch_name FROM info_branch where branch_no='" + m_in_branch_no + "'";
                rr = cmd.ExecuteScalar();
                if (rr != null) m_in_branch_name = rr.ToString();
            }
            m_in_branch_man_name = "";
            if (m_in_branch_man_id != "")
            {
                cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_in_branch_man_id + "'";
                rr = cmd.ExecuteScalar();
                if (rr != null) m_in_branch_man_name = rr.ToString();
            }
            m_servicers_name = "";
            
            m_table_persons = "";
        
            if (m_prepay_fill_sub_id != "")
            {
                cmd.CommandText = "select sub_name from cw_subject where sub_id='" + m_prepay_fill_sub_id + "'";
                ov = cmd.ExecuteScalar();
                if (ov != null && ov != DBNull.Value)
                {
                    m_prepay_fill_sub_name =ov.ToString().Trim();
                }  
            }
            //cmd.CommandText = "SELECT pay_name FROM info_pay_way where pay_way='" + m_pay_way + "'";
            //rr = cmd.ExecuteScalar();
            //m_pay_name = "";
            //if (rr != null) m_pay_name = rr.ToString();

            //cmd.CommandText = "select " + m_tb_detail + ".*,item_name,item_model,item_subno, size_group,info_item_prop.sale_price,info_item_prop.base_price,info_item_prop.price,info_item_prop.cost_price_avg from " + m_tb_detail + " left join info_item_prop on " + m_tb_detail + ".item_no= info_item_prop.item_no where sheet_no='" + Sheet_No + "'" + rowCondi + "  order by " + m_tb_detail + ".flow_id";//item_no,color_id,size_id";
           
            if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD || m_SheetType == SHEET_TYPE.SHEET_BUY_DD || m_SheetType == SHEET_TYPE.SHEET_SALE_DD_RETURN || m_SheetType == SHEET_TYPE.SHEET_BUY_DD_RETURN)
            {
                    
                   cmd.CommandText = "select " + m_tb_order_detail + ".*,item_name,item_model,item_subno,info_item_prop.item_size,info_item_prop.combine_sta,info_item_color.color_name,info_item_size.size_name,info_item_prop.size_group,info_item_prop.sale_price,info_item_prop.base_price,info_item_prop.price,info_item_prop.cost_price_avg,info_item_prop.rpt_class from " + m_tb_order_detail + " left join info_item_prop on " + m_tb_order_detail + ".item_no= info_item_prop.item_no left join info_item_color on " + m_tb_order_detail + ".color_id=info_item_color.color_id left join info_item_size on " + m_tb_order_detail + ".size_id=info_item_size.size_id where sheet_no='" + Sheet_No + "'" + rowCondi + "  order by " + m_tb_order_detail + ".inout_flag,info_item_prop.item_model,info_item_prop.item_name," + m_tb_order_detail + ".item_no," + m_tb_order_detail + ".color_id," + m_tb_order_detail + ".size_id";
               
            }
            else
            {
               
                    
                    cmd.CommandText = "select item_name,sheet_item_name,item_model,item_subno,info_item_prop.item_size, info_item_prop.size_group,info_item_color.color_name,info_item_size.size_name,info_item_prop.sale_price,info_item_prop.base_price,info_item_prop.price,info_item_prop.cost_price_avg ,info_item_prop.rpt_class,info_item_class.item_clsname,info_item_class.general_class," + m_tb_detail + ".other3," + m_tb_detail + ".* from " + m_tb_detail + " left join info_item_prop on " + m_tb_detail + ".item_no= info_item_prop.item_no left join info_item_color on " + m_tb_detail + ".color_id=info_item_color.color_id left join info_item_size on " + m_tb_detail + ".size_id=info_item_size.size_id  left join info_item_class on info_item_prop.item_clsno =info_item_class.item_clsno where sheet_no='" + Sheet_No + "'" + rowCondi + "  order by " + m_tb_detail + ".flow_id";
               
            }
        
            dr = cmd.ExecuteReader();

            int i = 0;
            SheetRows.Clear();
            //CMySbConnection conn1 = new CMySbConnection(CPubVars.ConnString);
            //conn1.Open();
            // CMySbCommand cmd1 = new CMySbCommand("", conn1); 

            SheetRow preRow = null;
            while (dr.Read())
            {
                i++;
                ov = dr["inout_flag"];             
                int nInoutFlag = -1;
                if (ov != null && ov != DBNull.Value)
                {
                    nInoutFlag = Convert.ToInt32(ov);
                } 
               
                string item_no="";
                if (dr["item_no"] != DBNull.Value)
                    item_no = dr["item_no"].ToString().Trim();
                else
                    item_no = "";
                double unit_factor=1;                
                if (dr["unit_factor"] != DBNull.Value) unit_factor = Convert.ToDouble(dr["unit_factor"]);
                string size_id = CPubVars.GetTextFromDr(dr, "size_id"); 
                string color_id = CPubVars.GetTextFromDr(dr, "color_id");
                if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE && m_good_inout_flag == "" && nInoutFlag == 1)
                { 
                    double now_pend_qty = Convert.ToDouble(dr["now_pend_qty"]);
                    double pend_qty = 0; ov = dr["pend_qty"]; if (ov != DBNull.Value) pend_qty = Convert.ToDouble(ov);

                    foreach (var row1 in SheetRows)
                    { 
                        if (row1.item_no == item_no && row1.unit_factor == unit_factor && row1.color_id == color_id && row1.size_id == size_id)
                        {
                            row1.now_pend_qty_d = now_pend_qty;
                            row1.pend_qty_d = pend_qty;
                            break;
                        }
                    }
                    continue;                     
                }
                
                SheetRow sheetRow = new SheetRow();
                if (dr["item_no"] != DBNull.Value) sheetRow.item_no = item_no;
                sheetRow.inout_flag = nInoutFlag;
                sheetRow.flow_id = CPubVars.GetTextFromDr(dr, "flow_id");
               
                
                if (m_SheetType != SHEET_TYPE.SHEET_SALE_DD && m_SheetType != SHEET_TYPE.SHEET_BUY_DD && m_SheetType != SHEET_TYPE.SHEET_SALE_DD_RETURN && m_SheetType != SHEET_TYPE.SHEET_BUY_DD_RETURN)
                {
                    ov = dr["sht_row_index"];
                    if (ov != DBNull.Value)
                        sheetRow.sht_row_index = Convert.ToInt32(ov);
                }
                if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                {
                    sheetRow.orig_flow_id = CPubVars.GetTextFromDr(dr, "orig_flow_id");
                }
                if (dr["sheet_item_name"] != DBNull.Value) sheetRow.item_name = dr["sheet_item_name"].ToString().Trim();
                if (sheetRow.item_name == "") sheetRow.item_name = CPubVars.GetTextFromDr(dr, "item_name");               

                if (dr["item_model"] != DBNull.Value) sheetRow.item_model = dr["item_model"].ToString().Trim();

                if (dr["unit_no"] != DBNull.Value) sheetRow.unit_no = dr["unit_no"].ToString().Trim();
                if (dr["unit_factor"] != DBNull.Value) sheetRow.unit_factor = unit_factor;
                sheetRow.item_subno = CPubVars.GetTextFromDr(dr, "barcode");

                sheetRow.unit_no1 = CPubVars.GetTextFromDr(dr, "unit_no1");
                sheetRow.quantity1 = CPubVars.GetTextFromDr(dr, "quantity1");
                if (CPubVars.IsNumeric(sheetRow.quantity1))
                {
                    sheetRow.quantity1 = CPubVars.FormatMoney(sheetRow.quantity1, 2);
                }
                if (sheetRow.item_subno == "")
                {
                    //if (sheetRow.unit_factor == 1)
                    {
                        if (dr["item_subno"] != DBNull.Value) sheetRow.item_subno = dr["item_subno"].ToString().Trim();
                    }
                }
                if (dr["inout_flag"] != DBNull.Value) sheetRow.inout_flag = Convert.ToInt32(dr["inout_flag"]);
                if (m_tb_detail == "sheet_item_detail")
                {
                    ov = dr["sheet_item_sn"];
                    if (ov != DBNull.Value) sheetRow.item_sn = ov.ToString().Trim();
                }
          
                ov = dr["branch_no"];
                if (ov != DBNull.Value)
                {
                    if (ov.ToString().Trim() != m_branch_no.Trim())
                        sheetRow.branch_no = ov.ToString().Trim();

                }
               // string size_name = "";
               // string color_name = "";
                 

                sheetRow.size_id = size_id;               
                sheetRow.color_id = color_id;
                 
                {
                   // sheetRow.color_name = CPubVars.GetTextFromDr(dr, "color_name");
                   // sheetRow.size_name = CPubVars.GetTextFromDr(dr, "size_name");
                   // sheetRow.size_group = CPubVars.GetTextFromDr(dr, "size_group");
                }
                
                try
                {
                    if (m_SheetType != SHEET_TYPE.SHEET_SALE_DD)
                    {
                        sheetRow.worker_id = CPubVars.GetTextFromDr(dr, "worker_id");
                        sheetRow.worker_name = CPubVars.GetTextFromDr(dr, "worker_name");
                        sheetRow.servicer_id = CPubVars.GetTextFromDr(dr, "servicer_id");
                        sheetRow.servicer_name = CPubVars.GetTextFromDr(dr, "servicer_name");
                        sheetRow.servicer1_id = CPubVars.GetTextFromDr(dr, "servicer1_id");
                    }
                }
                catch (Exception) { }
                sheetRow.s_orig_price =CPubVars.FormatMoney( CPubVars.GetTextFromDr(dr, "orig_price"),6);
                sheetRow.s_real_price = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "valid_price"),6);

                if (sheetRow.s_orig_price != "") sheetRow.orig_price = Convert.ToDouble(sheetRow.s_orig_price);
                if (sheetRow.s_real_price != "") sheetRow.real_price = Convert.ToDouble(sheetRow.s_real_price);
                if (dr["quantity"] != DBNull.Value)
                {
                    sheetRow.quantity = Convert.ToDouble(dr["quantity"]);
                    sheetRow.s_quantity =CPubVars.FormatMoney(sheetRow.quantity,2);
                }
                if (dr["pend_qty"] != DBNull.Value) sheetRow.pend_qty = Convert.ToDouble(dr["pend_qty"]);
                if (dr["now_pend_qty"] != DBNull.Value) sheetRow.now_pend_qty = Convert.ToDouble(dr["now_pend_qty"]);
                if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD || m_SheetType == SHEET_TYPE.SHEET_BUY_DD)
                {
                    sheetRow.sent_qty = Convert.ToDouble(dr["sent_qty"]);
                }
                
                int inout_flag = 1;

                if (dr["inout_flag"] != DBNull.Value) inout_flag = Convert.ToInt32(dr["inout_flag"]);
                if (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD)
                    sheetRow.quantity = sheetRow.quantity * inout_flag;
                else if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD)
                    sheetRow.quantity = sheetRow.quantity * inout_flag * (-1);

                if (dr["sub_amount"] != DBNull.Value) sheetRow.money = Convert.ToDouble(dr["sub_amount"]);

                if (sheetRow.orig_price != 0)
                    sheetRow.discount = sheetRow.real_price / sheetRow.orig_price;
                else
                    sheetRow.discount = 1;

                if (dr["remark"] != DBNull.Value) sheetRow.brief = CPubVars.GetTextFromDr(dr, "remark");
                //string tables_no=m_tabv
                              
                sheetRow.combine_flag = CPubVars.GetTextFromDr(dr, "combine_flag");
                
                sheetRow.other1= CPubVars.GetTextFromDr(dr, "other1");
               
                if (sheetRow.other1.Contains("sign_oper:"))
                {
                    sheetRow.sign_oper = sheetRow.other1.Replace("sign_oper:", "");
                }
                if (sheetRow.combine_flag == "2" && preRow != null && preRow.combine_flag == "1")
                {
                    preRow.tables_no = sheetRow.tables_no;
                }
             
                if(m_SheetType==SHEET_TYPE.SHEET_STORE_IN || m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
                {
                    sheetRow.other4 = CPubVars.GetTextFromDr(dr, "other4");
                    if(sheetRow.other4.StartsWith("OSht:"))
                    {
                        sheetRow.orig_sheet_no = sheetRow.other4.Replace("OSht:", "");
                    }
                }
                sheetRow.ClassName = CPubVars.GetTextFromDr(dr, "rpt_class");
                if (!(m_SheetType == SHEET_TYPE.SHEET_SALE_DD || m_SheetType == SHEET_TYPE.SHEET_BUY_DD || m_SheetType == SHEET_TYPE.SHEET_SALE_DD_RETURN || m_SheetType == SHEET_TYPE.SHEET_BUY_DD_RETURN))
                {
                    if (sheetRow.ClassName == "")
                    {
                        sheetRow.ClassName = CPubVars.GetTextFromDr(dr, "general_class");
                    }
                    if (sheetRow.ClassName == "")
                    {
                        sheetRow.ClassName = CPubVars.GetTextFromDr(dr, "item_clsname");
                    }
                }

                sheetRow.item_size = CPubVars.GetTextFromDr(dr, "item_size");

                if (m_SheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM || m_SheetType ==SHEET_TYPE.SHEET_INVENT_REDUCE)
                {
                    ov = dr["price"];
                    if (ov != DBNull.Value)
                    {
                        double in_price = Convert.ToDouble(ov);
                        sheetRow.in_price = in_price;
                        sheetRow.in_amount = CPubVars.FormatMoney(in_price * sheetRow.quantity * sheetRow.unit_factor);
                    }
                    ov = dr["cost_price_avg"];
                    if (ov != DBNull.Value)
                    {
                        sheetRow.cost_price = Convert.ToDouble(ov);
                        sheetRow.in_amount = CPubVars.FormatMoney(sheetRow.cost_price * sheetRow.quantity );
                    }
                    ov = dr["base_price"];
                    if (ov != DBNull.Value)
                    {
                        double base_price = Convert.ToDouble(ov);
                        sheetRow.base_price = base_price;
                        sheetRow.base_amount = CPubVars.FormatMoney(base_price * sheetRow.quantity );
                    } 
                }
                ov = dr["sale_price"];
                if (ov != DBNull.Value)
                {
                    double sale_price = Convert.ToDouble(ov);
                    sheetRow.sale_price = sale_price;
                    sheetRow.sale_amount = CPubVars.FormatMoney(sale_price * sheetRow.quantity);
                }
                if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT || m_SheetType == SHEET_TYPE.SHEET_STORE_IN)
                {
                    
                    ov = dr["orig_sht_qty"];
                    if (ov != DBNull.Value) sheetRow.orig_sht_qty = Convert.ToDouble(ov);
                    //ov = dr["pend_qty"];
                   // if (ov != DBNull.Value) sheetRow.orig_sht_qty = Convert.ToDouble(ov);
                }
 

                SheetRows.Add(sheetRow);
                preRow = sheetRow;

            }
            dr.Close();

      
            {
                foreach (var sheetRow in SheetRows)
                { 
                    if (sheetRow.item_subno == "" && sheetRow.unit_factor > 1)
                    {
                        cmd.CommandText = "select subno from info_item_multi_unit where item_no='" + sheetRow.item_no + "' and unit_factor='" + sheetRow.unit_factor.ToString() + "'";
                        ov = cmd.ExecuteScalar();
                        if (ov != null && ov != DBNull.Value)
                            sheetRow.item_subno = ov.ToString().Trim();
                    }

                    if (sheetRow.inout_flag == 0 && sheetRow.quantity > 0)
                    {
                        foreach (var orig_row in SheetRows)
                        { 
                            if (orig_row.flow_id == sheetRow.orig_flow_id)
                            {
                                orig_row.add_qty += sheetRow.quantity;
                                break;
                            }
                        }

                    }
                }

            }
            if (m_SheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM)
            {

                foreach (var sheetRow in SheetRows)
                {
                        
                        cmd.CommandText = "select * from info_item_multi_unit where item_no='" + sheetRow.item_no + "' order by unit_factor desc";
                        dr = cmd.ExecuteReader();
                        if (dr.Read())
                        {
                            double unit_factor = 1;
                            ov = dr["unit_factor"];
                            if (ov != DBNull.Value)
                            {
                                unit_factor = Convert.ToDouble(ov);
                            }
                            ov = dr["price"];
                            if (ov != DBNull.Value)
                            {
                                double in_price = Convert.ToDouble(ov);
                                sheetRow.in_amount = CPubVars.FormatMoney(in_price / unit_factor * sheetRow.quantity * sheetRow.unit_factor);
                            }
                            ov = dr["base_price"];
                            if (ov != DBNull.Value)
                            {
                                double base_price = Convert.ToDouble(ov);
                                sheetRow.base_amount = CPubVars.FormatMoney(base_price / unit_factor * sheetRow.quantity * sheetRow.unit_factor);
                            }
                            if (sheetRow.sale_amount == "")
                            {
                                ov = dr["sale_price"];
                                if (ov != DBNull.Value)
                                {
                                    double sale_price = Convert.ToDouble(ov);
                                    sheetRow.sale_amount = CPubVars.FormatMoney(sale_price / unit_factor * sheetRow.quantity * sheetRow.unit_factor);
                                }
                            }
                        }
                        dr.Close();
                    }
                
            }
           

            CMySbConnection conn_pc = null;

            foreach (var sheetRow in SheetRows)
            {
           
                if (sheetRow.branch_no != "")
                {
                    cmd.CommandText = "select branch_name from info_branch where branch_no='" + sheetRow.branch_no + "'";
                    ov = cmd.ExecuteScalar();
                    if (ov != null && ov != DBNull.Value)
                    {
                        sheetRow.branch_name = ov.ToString();

                    }
                    else
                        sheetRow.branch_name = "已删除仓库,编号" + sheetRow.branch_no;

                }
                //if (sheetRow.size_id != "" && sheetRow.size_id != "0")
                //{
                //    cmd.CommandText = "select size_name from info_item_size where size_id='" + sheetRow.size_id + "'";
                //    ov = cmd.ExecuteScalar();
                //    if (ov != null && ov != DBNull.Value)
                //    {
                //        sheetRow.size_name = ov.ToString().Trim();
                //    }
                //    else
                //        sheetRow.size_name = "";

                //}
                //if (sheetRow.color_id != "" && sheetRow.color_id != "0")
                //{
                //    cmd.CommandText = "select color_name from info_item_color where color_id='" + sheetRow.color_id + "'";
                //    ov = cmd.ExecuteScalar();
                //    if (ov != null && ov != DBNull.Value)
                //    {
                //        sheetRow.color_name = ov.ToString().Trim();
                //    }
                //    else
                //        sheetRow.color_name = "";
                //}
                if (sheetRow.item_no != "" && sheetRow.item_no.Substring(0, 1) == "-")
                {
                    if (sheetRow.unit_factor == 1)
                    {
                        cmd.CommandText = "select item_subno from info_item_prop where item_no='" + sheetRow.item_no + "'";
                        ov = cmd.ExecuteScalar();
                        if (ov != null && ov != DBNull.Value)
                        {
                            sheetRow.item_subno = ov.ToString().Trim();
                        }
                    }
                    else
                    {
                        cmd.CommandText = "select subno from info_item_multi_unit where item_no='" + sheetRow.item_no + "'";
                        ov = cmd.ExecuteScalar();
                        if (ov != null && ov != DBNull.Value)
                        {
                            sheetRow.item_subno = ov.ToString().Trim();
                        }
                    }
            
                }
            }


 

            if (conn_pc != null)
            {
                if (conn_pc.State == System.Data.ConnectionState.Open)
                    conn_pc.Close();
            }
          
            if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT || m_SheetType == SHEET_TYPE.SHEET_STORE_IN)
            {
                cmd.CommandText = "select oper_date,sheet_no,trans_no,branch_no,d_branch_no,out_branch_name,in_branch_name,prepay_left_amount,box_prepay,paid_amount,total_amount,disc_amount from " + m_tb_master + " left join (select branch_no as out_branch_no,branch_name as out_branch_name from info_branch) tb_out_branch on " + m_tb_master + ".branch_no=tb_out_branch.out_branch_no  left join (select branch_no as in_branch_no,branch_name as in_branch_name from info_branch) tb_in_branch on " + m_tb_master + ".d_branch_no=tb_in_branch.in_branch_no  where sheet_no='" + m_OrigSheetNo + "'";
                dr = cmd.ExecuteReader();
                if (dr.Read())
                {
                    string orig_trans_no = CPubVars.GetTextFromDr(dr, "trans_no");
                    m_OrigSheetType = SheetTypeFromStr(orig_trans_no);
                    m_orig_oper_date = CPubVars.GetTextFromDr(dr, "oper_date");

                    m_orig_branch_no = CPubVars.GetTextFromDr(dr, "branch_no");
                    m_orig_branch_name = CPubVars.GetTextFromDr(dr, "out_branch_name");
                    m_orig_in_branch_no = CPubVars.GetTextFromDr(dr, "d_branch_no");
                    m_orig_in_branch_name = CPubVars.GetTextFromDr(dr, "in_branch_name");

                    ov = dr["prepay_left_amount"]; if (ov != null && ov != DBNull.Value) m_prepay_left = Convert.ToDouble(ov);
                    m_pay_prepay_box_id = CPubVars.GetTextFromDr(dr, "box_prepay");
                    ov = dr["paid_amount"];
                    if (ov != DBNull.Value)
                        m_paid_amount = Convert.ToDouble(ov);
                    ov = dr["total_amount"];
                    if (ov != DBNull.Value)
                        m_total_amount = Convert.ToDouble(ov);
                    ov = dr["disc_amount"];
                    if (ov != DBNull.Value)
                        m_disc_amt = Convert.ToDouble(ov);
                }
                dr.Close();
            }
            //cmd.CommandText = "select * from sheet_money where settle_no='" + Sheet_No + "' order  by";
            //dr = cmd.ExecuteReader();
            //if (!dr.HasRows)
            //    return "没有找到该单据对应的收款记录";
            //dr["
            //SheetRow sheetRow = new SheetRow();

            return "";

        }
     
        public string Approve(CMySbConnection conn, CMySbCommand cmd, CMySbTransaction tran)
        {
            return Approve(conn, cmd, tran, false);
        }
        public CMySbTransaction m_TranCardToCommit = null; public CMySbConnection m_connCardToCommit = null;
        public string Approve(CMySbConnection conn, CMySbCommand cmd, CMySbTransaction tran,bool bVipConnCommitLater)
        {
            m_TranCardToCommit = null;
           
            string sError = "";
            string remote_sql = ""; string scmd_update_cost = "";
            if (m_bApproved)
                return "已经审核过,不能再次审核.";
            
            if (m_total_amount - m_paid_amount - m_disc_amt >= 0.05)
            {
                if (m_supcust_no == "")
                {
                    if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                        return "此单据有欠款，必须指定客户";
                    else if (m_SheetType != SHEET_TYPE.SHEET_MOVE_STORE  && m_SheetType !=SHEET_TYPE.SHEET_STORE_IN && m_SheetType !=SHEET_TYPE.SHEET_STORE_OUT)
                        return "此单据有欠款，必须指定供应商";
                }
            }

            if (m_from_red_sheet_no != "")
            {
                CSheetSale from_sheet=new CSheetSale();
                from_sheet.LoadSheet(m_from_red_sheet_no,conn,cmd);
                from_sheet.m_redFlag ="2";
                from_sheet.m_OrigSheetNo = from_sheet.m_sheet_no;
                from_sheet.m_sheet_no ="";
                from_sheet.m_bApproved = false;
                sError = from_sheet.SaveSheet(conn,cmd,tran);
                if(sError !="")
                    return sError;
                sError =from_sheet.Approve(conn,cmd,tran);
                if(sError !="")
                {
                    return sError;
                } 
            } 

            bool bTranPassed = false;
            if (conn == null)
            {
                conn = new CMySbConnection(CPubVars.ConnString);
                conn.Open();
                cmd = new CMySbCommand("", conn);
            }

            if (tran == null)
            {
                tran = conn.BeginTransaction();
                cmd.Transaction = tran;
            }
            else
                bTranPassed = true;

            CMySbConnection conn_card=null;// new CMySbCommand("",conn_card");
            CMySbCommand cmd_card = cmd;
            CMySbTransaction tran_card = null;
            
             
            #region 审核付款
            //int ddd = SheetRows.Count;
            string com_condi = ""; string strErr = "";
            try
            {
                CMySbDataReader dr;
                
                {
                        cmd.CommandText = "SELECT * from "+m_tb_master+" where sheet_no ='" + m_sheet_no + "'";
                        dr = cmd.ExecuteReader();
                        
                        if (!dr.Read())
                        {
                            dr.Close();
                            if (!bTranPassed)
                            {
                                tran.Rollback();
                                conn.Close();
                            }
                            sError  = "审核时发现该单据不存在";
                            goto exit_fun;
                        }
                       

                        string branch_no = dr["branch_no"].ToString();
                        string approve_flag = "";
                        if (dr["approve_flag"] != null)
                            approve_flag = dr["approve_flag"].ToString();
                        dr.Close();
                        m_bApproved = (approve_flag == "1");
                        if (m_bApproved)
                            sError= "该单据已经审批过";
                        if (sError != "")
                            goto exit_fun;
               }

                int money_inout_flag = 0;
                //int inout_flag = 0;
                bool bOrderSheet = false;
                if (m_SheetType == SHEET_TYPE.SHEET_SALE)
                {
                    //  inout_flag = -1;
                    money_inout_flag = 1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                {
                    //inout_flag = 1;
                    money_inout_flag = -1;
                } 
                else if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD)
                {
                    //  inout_flag = -1;
                    money_inout_flag = 1;
                    bOrderSheet = true;

                }
                else if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD_RETURN)
                {
                    //inout_flag = 1;
                    money_inout_flag = -1;
                    bOrderSheet = true;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_BUY)
                {
                    //  inout_flag = 1;
                    money_inout_flag = -1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN)
                {
                    // inout_flag = -1;
                    money_inout_flag = 1;

                }
                else if (m_SheetType == SHEET_TYPE.SHEET_BUY_DD)
                {
                    //  inout_flag = 1;
                    money_inout_flag = -1;
                    bOrderSheet = true;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_BUY_DD_RETURN)
                {
                    // inout_flag = -1;
                    money_inout_flag = 1;
                    bOrderSheet = true;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD)
                {
                    // inout_flag = -1;
                    money_inout_flag = 0;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD)
                {
                    // inout_flag = 1;
                    money_inout_flag = 0;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                {
                    // inout_flag = -1;
                    money_inout_flag = 0;
                } 
                else if (m_SheetType == SHEET_TYPE.SHEET_FEE_OUT)
                {
                    // inout_flag = 1;
                    money_inout_flag = -1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_FEE_IN)
                {
                    // inout_flag = 1;
                    money_inout_flag = 1;
                }
                string sNowDay = CPubVars.GetDateText(DateTime.Now.Date);
                if (!bOrderSheet)
                {             
                    string orig_money_sheet = "";
                    if (m_redFlag == "2")
                    {
                        money_inout_flag *= -1;
                        cmd.CommandText = "select sheet_no,immediate_pay from sheet_money where settle_no='" + m_OrigSheetNo + "' and (red_flag ='0' or red_flag is null) ";
                        string immedia_pay = ""; bool bHaveLaterPay = false;
                        dr = cmd.ExecuteReader();
                        while (dr.Read())
                        {
                            immedia_pay = CPubVars.GetTextFromDr(dr, "immediate_pay");
                            if (immedia_pay == "1")
                            {
                                orig_money_sheet = CPubVars.GetTextFromDr(dr, "sheet_no");
                            }
                            else
                            {
                                bHaveLaterPay = true;
                            }
                        }
                        dr.Close();
                        if (bHaveLaterPay)
                        {  
                            sError = "该单据已有后续的收款单,不能红冲";
                            goto exit_fun;
                        } 
                    }
                    //if (money_inout_flag != 0)
                    //{
                    //    if (m_paid_amount != 0)
                    //    {
                    //        if (!bOrderSheet)//订单无需生成收款单
                    //        {
                    //            CGetMoneySheet moneySheet = new CGetMoneySheet();
                    //            moneySheet.m_com_no = m_com_no;
                    //            moneySheet.m_redFlag = m_redFlag;
                    //            moneySheet.m_bImmediatePay = true;
                    //            moneySheet.m_bPayOrGet = (money_inout_flag == -1);
                    //            moneySheet.SetPayInfo(m_pay_cash_amount, m_pay_card_amount, m_pay_ticket_amount, m_pay_bank_amount, m_pay_prepay_amount, m_pay_prepay_box_id, m_pay_integral_amount, m_pay_reduce_integral, m_pay_card_id, m_pay_bank_box_id, m_pay_cash_box_id, m_pay_money_transfer_fee);

                    //            moneySheet.SetGeneralInfo(m_pay_way, m_supcust_no, "", m_oper_id, m_work_man, "", m_oper_date.ToString(), m_work_date.ToString(), "");
                    //            if (m_redFlag == "2")
                    //            {
                    //                if (orig_money_sheet != "")
                    //                {
                    //                    moneySheet.m_OrigSheetNo = orig_money_sheet;
                    //                    moneySheet.m_oper_date = this.m_oper_date;
                    //                    moneySheet.AddRow(m_OrigSheetNo, m_paid_amount, m_order_amount, m_disc_amt, m_total_amount, 0, m_total_amount - m_paid_amount, 0, 1, "");
                    //                    moneySheet.SaveSheet(true, true, conn, cmd);
                    //                }
                    //            }
                    //            else
                    //            {
                    //                moneySheet.AddRow(m_sheet_no, m_paid_amount, m_order_amount, m_disc_amt, m_total_amount, 0, m_total_amount - m_paid_amount, 0, 1, "");
                    //                moneySheet.m_oper_date = this.m_oper_date;
                    //                sError = moneySheet.SaveSheet(true, true, conn, cmd);
                    //                if (sError != "")
                    //                {
                    //                    goto exit_fun;
                    //                }
                   
                    //            }
                    //        }
                    //    }
                    //}
                }
              
                if (money_inout_flag != 0)
                {
                    bool bPayOrGet=(money_inout_flag==-1);
                    strErr = DealPrepay(cmd, m_redFlag, m_supcust_no, m_pay_prepay_amount, m_pay_prepay_box_id, bPayOrGet, ref m_prepay_left);
                    if (strErr != "")
                        return strErr;

                    if (m_com_no != "")
                        com_condi = " and (com_no='" + m_com_no + "' or com_no is null)";
                    com_condi = "";
                                            
                    if (m_duty_no != "")
                    {
                        if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN ||    m_SheetType == SHEET_TYPE.SHEET_PRE_GET_MONEY || m_SheetType == SHEET_TYPE.SHEET_PRE_PAY_MONEY || m_SheetType == SHEET_TYPE.SHEET_SALE_DD || m_SheetType == SHEET_TYPE.SHEET_SALE_DD_RETURN || m_SheetType == SHEET_TYPE.SHEET_FEE_OUT || m_SheetType == SHEET_TYPE.SHEET_FEE_IN)
                        {
                            strErr = DealDuty(cmd, money_inout_flag);
                            if (strErr != "")
                                return strErr;
                        }
                    }
                       
                   
                }

            } 
            catch (Exception oe)
            {

                sError = "在审核付款时发生错误:" + oe.Message;
                goto exit_fun;
            }

            #endregion
            if (m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN && m_OrigSheetNo != "")
            {
                cmd.CommandText = "update " + m_tb_master + " set cancel_flag='1' where sheet_no='" + m_OrigSheetNo + "' and trans_no='S'"; 
                cmd.ExecuteNonQuery();
            }
            if (m_SheetType == SHEET_TYPE.SHEET_PRE_GET_MONEY || m_SheetType == SHEET_TYPE.SHEET_PRE_PAY_MONEY)
            {
                sError = Approve_PrePay(conn, cmd, tran);
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD || m_SheetType == SHEET_TYPE.SHEET_SALE_DD_RETURN || m_SheetType == SHEET_TYPE.SHEET_BUY_DD)
            {
                sError = Approve_PrePay(conn, cmd, tran);
                if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD_RETURN)
                {
                    if (m_OrigSheetNo == "")
                    {
                        sError = "原始订单编号没有指定";
                    }
                    else
                    {
                        cmd.CommandText = "update "+m_tb_master+" set cancel_flag='1' where sheet_no='" + m_OrigSheetNo + "'";
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_FEE_OUT || m_SheetType == SHEET_TYPE.SHEET_FEE_IN)
            {
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD || m_SheetType == SHEET_TYPE.SHEET_SALE_DD_RETURN || m_SheetType == SHEET_TYPE.SHEET_BUY_DD || m_SheetType == SHEET_TYPE.SHEET_BUY_DD_RETURN)
            {

            }
            else
            {
                try
                {  
                    
                    if (m_SheetType == SHEET_TYPE.SHEET_SALE)
                    {
                        if (m_redFlag == "2")
                            UpdateCost_SaleReturn(conn, cmd, ref scmd_update_cost);
                        else
                            UpdateCost_Sale(conn, cmd, ref scmd_update_cost);
                    }
                    else if (m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                    {
                        if (m_redFlag == "2")
                            UpdateCost_Sale(conn, cmd, ref scmd_update_cost);
                        else
                            UpdateCost_SaleReturn(conn, cmd, ref scmd_update_cost);
                    }
                    else if (m_SheetType == SHEET_TYPE.SHEET_BUY)
                    {
                        if (m_redFlag == "2")
                            UpdateCost_BuyReturn( conn, cmd, ref scmd_update_cost);
                        else
                            UpdateCost_Buy(conn, cmd, ref scmd_update_cost);
                    }
                    else if (m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN)
                    {
                        if (m_redFlag == "2")
                            UpdateCost_Buy( conn, cmd, ref scmd_update_cost);
                        else
                            UpdateCost_BuyReturn( conn, cmd, ref scmd_update_cost);
                    }
                    else if (m_SheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM)
                    {
                        //UpdateCost_Invent(SheetRows, conn, cmd, ref scmd_update_cost);
                    }
                    else if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD)
                    {
                        UpdateCost_Combine( conn, cmd, ref scmd_update_cost);
                    }
                    else if (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD)
                    {
                        UpdateCost_Split( conn, cmd, ref scmd_update_cost);
                    }
                }
                catch (Exception ee)
                {
                    sError ="更新会员卡信息出错:" + ee.Message;
                    goto exit_fun;
                }
                
                {
                    if (m_redFlag == "2")
                        sError = Approve_Good_Red(conn, cmd, tran);
                    else
                        sError = Approve_Good(conn, cmd, tran);
                }
            }
 
            string approve_oper = m_oper_id;
            if (m_approve_oper != "")
                approve_oper = m_approve_oper;
            if (CMySbConnection.g_dbStyle == 0)
            {
                
                {
                    string sNow = CPubVars.GetDateText(DateTime.Now);
                    remote_sql += scmd_update_cost;
                    remote_sql += "update " + m_tb_master + " set approve_flag ='1',approve_oper='" + approve_oper + "',approve_time='" + sNow + "' where sheet_no='" + this.m_sheet_no + "';";
                    remote_sql += "update " + m_tb_master + " set money_approve_time ='" + sNow + "' where sheet_no='" + this.m_sheet_no + "';";

                    remote_sql += "if exists (select * from setting where set_name ='last_sht_time') update setting set set_value='" + sNow + "' where set_name='last_sht_time' else insert into setting (set_name,set_value) values ('last_sht_time','" + sNow + "');";

                    if (m_redFlag == "2")
                    {
                        remote_sql += "update " + m_tb_master + " set red_flag ='1' where sheet_no='" + this.m_OrigSheetNo + "';";
                        remote_sql += "update " + m_tb_master + " set red_flag ='2' where sheet_no='" + this.m_sheet_no + "';";
                    }
                }
                
                try
                {
                    if (remote_sql != "")
                    {
                        cmd.CommandText = remote_sql;
                        cmd.ExecuteNonQuery();
                    }
                }
                catch (Exception ee)
                {
                    sError ="更新标志出错:" + ee.Message;
                }
            }
            else
            {
                try
                {
                    cmd.CommandText = "update "+m_tb_master+" set approve_flag ='1' where sheet_no='" + this.m_sheet_no + "';"; 
                    //cmd.CommandText = "update "+m_tb_master+" set money_approve_time ='" + sNow + "' where sheet_no='" + this.m_sheet_no + "';";
                    cmd.ExecuteNonQuery();
                    if (m_redFlag == "2")
                    {
                        cmd.CommandText = "update "+m_tb_master+" set red_flag ='1' where sheet_no='" + this.m_OrigSheetNo + "';";
                        cmd.ExecuteNonQuery();
                        cmd.CommandText = "update "+m_tb_master+" set red_flag ='2' where sheet_no='" + this.m_sheet_no + "';";
                        cmd.ExecuteNonQuery();
                    }
                }
                catch (Exception ee)
                {
                    sError = "更新标志出错1:" + ee.Message;
                }
            }
            if (sError == "")
            {
                if (m_SheetType == SHEET_TYPE.SHEET_SALE)
                {
                  
                }
            }
            
        exit_fun:
            if (tran_card != null)
            {
                try
                {
                    if (sError == "")
                    {
                        if (bVipConnCommitLater)
                        {
                            m_TranCardToCommit = tran_card;
                            m_connCardToCommit = conn_card;
                        }
                        else 
                            tran_card.Commit();
                    }
                    else
                        tran_card.Rollback();
                }
                catch (Exception)
                {
                    sError = "提交会员卡事务发生错误:" + sError;
                 
                   // CPubVars.LogMsgToDb(cmd_card, sError, "Commit Error");
                }
                if (conn_card != null && m_TranCardToCommit==null)
                {
                    try
                    {
                        conn_card.Close();
                    }
                    catch (Exception)
                    {
                    }
                }
            }
            
            if (!bTranPassed)
            {
                if (tran != null)
                {
                    if (sError == "")
                    {
                        tran.Commit();
                    }
                    else
                    {                        
                        tran.Rollback();
                    }
                    conn.Close();
                }
            }         
            return sError;
        }
     
   
        public string Approve_Good(CMySbConnection conn, CMySbCommand cmd, CMySbTransaction tran)
        {            
            if (m_branch_no == "")
            {
                if (m_SheetType != SHEET_TYPE.SHEET_MOVE_STORE)
                    
                    {
                        bool bHaveEmptyBranchRow = false;
                        foreach (var sheetRow in SheetRows)
                        { 
                            if (sheetRow.branch_no == "")
                            {
                                bHaveEmptyBranchRow = true;
                                break;
                            }
                        }
                        if (bHaveEmptyBranchRow)
                        {
                            return "请指定本单据使用的仓库或单项商品所属的仓库.";
                        }
                    }
            }
            string err = "";
            string sheet_no = m_sheet_no;
            string connString = "";// myJXC.CPubVars.ConnString;
            bool bConnPassed = false;
            try
            {
                if (conn == null)
                {
                    conn = new CMySbConnection(connString);
                    conn.Open();
                    cmd = new CMySbCommand("", conn);
                }
                else
                {
                    bConnPassed = true;
                }
                if (tran == null)
                {
                    tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                }
                CMySbDataReader dr;
                cmd.CommandText = "SELECT branch_no,red_flag from "+m_tb_master+" where sheet_no ='" + m_sheet_no + "'";
                dr = cmd.ExecuteReader();
                string strErr = "";
                if (!dr.Read())
                {
                    dr.Close();
                    // conn.Close();
                    strErr = "该单据不存在";
                    return strErr;
                }

                string branch_no = dr["branch_no"].ToString();
                string red_flag = CPubVars.GetTextFromDr(dr, "red_flag");

              //  string branch_approve_time = "";
                //if (dr["branch_approve_time"] != null)
                //    branch_approve_time = dr["branch_approve_time"].ToString();
                dr.Close();
                if (red_flag == "1")
                {
                    return "该单据已经被红冲,不能继续审核";
                }
                if (m_SheetType == SHEET_TYPE.SHEET_STORE_IN || m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
                {
                    cmd.CommandText = "SELECT red_flag from " + m_tb_master + " where sheet_no ='" + m_OrigSheetNo + "'";
                    dr = cmd.ExecuteReader();
                   
                    if (dr.Read())
                    {
                        red_flag=CPubVars.GetTextFromDr(dr,"red_flag");
                        if(red_flag=="1")
                        {
                           strErr = "单据" + m_OrigSheetNo +"已被红冲,不能进行后续仓库出入库操作";
                        }
                    }
                    dr.Close();
                    if(strErr !="")
                    {
                        return strErr;
                    }
                }
                //if (branch_approve_time != "")
                //    return "该单据已经审批过库存";
             
                object ov = null;
                string remote_sql = "";
                bool bStockChanged = false;
                string update_sheet_sql = "";
                int nApproveRows = 0; int nAllSheetRows = 0;
                #region Update sheet for produce date
                bool bSheetBranchIgnoreBatch = false;
                bool bSheetInBranchIgnoreBatch = false;
           

                #endregion
             
                int nStepCount = 1;

                if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                {
                    nStepCount = 2;
                }
                // string sNow = CPubVars.GetDateText(DateTime.Now);
                string sNow = ""; 
                sNow = CPubVars.GetDateText(DateTime.Now);
                
                string pre_branch_no = "";  
                Dictionary<string, SheetRow> NeedSplitItems = new Dictionary<string, SheetRow>();

                for (int nStep = 1; nStep <= nStepCount; nStep++)
                {
                    foreach (var sheetRow in SheetRows)                    
                    { 
                        nAllSheetRows++;
                        #region  改写库存

                        //  CMySbCommand cmdItemInfo = new CMySbCommand("", conn_info);                    
                        string item_no = sheetRow.item_no;
                        string unit_no = sheetRow.unit_no;
                        if (sheetRow.is_service)
                            continue;
                        double unit_factor = sheetRow.unit_factor;
                        //double nQuantity = Math.Abs(sheetRow.quantity - sheetRow.pend_qty);
         
                        double nQuantity = sheetRow.quantity - sheetRow.now_pend_qty;
                        string size = sheetRow.size_id;
                        string color = sheetRow.color_id;

                        int inout_flag = sheetRow.inout_flag;
                        //if (m_redFlag == "2")
                        //{
                        //    inout_flag *= -1;
                        //}
                        if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                        {
                            if (nStep == 1)
                            {
                                branch_no = m_branch_no;
                                if (bSheetBranchIgnoreBatch)
                                {
                                    color = "0";
                                }
                            }
                            else
                            {
                                branch_no = m_in_branch_no;
                                inout_flag *= -1;
                                if (bSheetInBranchIgnoreBatch)
                                {
                                    color = "0";
                                }
                            }
                        }
                        else
                        {
                            if (sheetRow.bItemBranchIgnoreBatch)
                            {
                                color = "0";
                            }
                            branch_no = sheetRow.branch_no;
                            if (branch_no == "")
                            {
                                branch_no = m_branch_no;
                            }
                        }
                        if (branch_no == "")
                            continue;
                        string size_condi = "";
                        if (size != "")
                        {
                            size_condi = " size_id='" + size + "' ";
                        }
                        else
                        {
                            size_condi = " size_id ='0' ";
                        }
                        string color_condi = "";
                        if (color != "")
                        {
                            color_condi = " color_id='" + color + "' ";
                        }
                        else
                        {
                            color_condi = " color_id ='0' ";
                        }
                        string size_color_condi = size_condi;
                        if (color_condi != "")
                        {
                            if (size_color_condi != "")
                            {
                                size_color_condi += " and ";
                            }
                            size_color_condi += color_condi;
                        }
                        if (size_color_condi != "")
                            size_color_condi = " and " + size_color_condi;
                        //判断是否负库存可卖
                        if (!m_bApproved && m_bDisableNagativeStock && CMySbConnection.g_dbStyle ==0)
                        {
                            if (m_SheetType == SHEET_TYPE.SHEET_SALE && sheetRow.quantity > 0)
                            {
                                double now_stock_qty = 0; double sell_pend_qty = 0;
                                double now_negative_sell_qty=0;
                                cmd.CommandText = "select stock_qty,sell_pend_qty from stock where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi;
                                dr = cmd.ExecuteReader();
                                if (dr.Read())
                                {
                                    ov = dr["stock_qty"]; if (ov != DBNull.Value) now_stock_qty = Convert.ToDouble(ov);
                                    ov = dr["sell_pend_qty"]; if (ov != DBNull.Value) sell_pend_qty = Convert.ToDouble(ov);                                  
                                }
                                dr.Close();
                                now_negative_sell_qty = -(now_stock_qty - sell_pend_qty - sheetRow.quantity);
                              
                                if (now_negative_sell_qty > 0)
                                {
                                    double allow_negative_qty = 0; string s_negative_till = "";  
                                    cmd.CommandText = "select allow_negative_qty,negative_till from negative_sell_limit where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi;
                                    dr = cmd.ExecuteReader();
                                    if (dr.Read())
                                    {
                                        ov = dr["allow_negative_qty"]; if (ov != DBNull.Value) allow_negative_qty = Convert.ToDouble(ov);
                                        s_negative_till = CPubVars.GetTextFromDr(dr, "negative_till");
                                    }
                                    dr.Close();
                                    if (s_negative_till != "")
                                    {
                                        DateTime negative_till = Convert.ToDateTime(s_negative_till);
                                        if (negative_till > DateTime.Now.Date)
                                        {
                                            err = sheetRow.item_name + "库存不足,允许负卖日期已过";
                                            goto exit_fun;
                                        }
                                    }
                                    if (now_negative_sell_qty > allow_negative_qty)
                                    {
                                        double now_can_sell_qty = now_stock_qty - sell_pend_qty + allow_negative_qty;
                                        if (allow_negative_qty > 0)
                                        {
                                            if (now_can_sell_qty > 0)
                                            {
                                                err = sheetRow.item_name + "库存不足,允许负卖数:" + allow_negative_qty.ToString() + ",您现在可卖数为:" + now_can_sell_qty.ToString();
                                            }
                                            else
                                                err = sheetRow.item_name + "库存不足,允许负卖数:" + allow_negative_qty.ToString();
                                        }
                                        else
                                        {
                                            if (now_can_sell_qty > 0)                                            
                                                err = sheetRow.item_name + "库存不足,不允许负卖,您现在可卖数为:" + now_can_sell_qty.ToString();                                           
                                            else
                                                err = sheetRow.item_name + "库存不足,不允许负卖";
                                        }
                                        goto exit_fun;
                                    }

                                }
                            }

                        }
                         
                        #region 标记订单完成数量
                        if (!m_bIsStoreSheet)
                        {
                            if (m_redFlag != "2" && m_OrigSheetNo != "")
                            {
                                if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_BUY)
                                {
                                    string sQuantity = "";
                                    if (nQuantity >= 0)
                                        sQuantity = "+" + nQuantity;
                                    else
                                        sQuantity = nQuantity.ToString();

                                    string sOrderCmd = "update " + m_tb_order_detail + " set sent_qty=sent_qty " + sQuantity + " where sheet_no='" + m_OrigSheetNo + "' and item_no='" + item_no + "' and unit_factor=" + unit_factor.ToString() + " and branch_no='" + branch_no + "'" + size_color_condi + ";";

                                    if (CMySbConnection.g_dbStyle != 2)
                                    {
                                        remote_sql += sOrderCmd;
                                    }
                                    else
                                    {
                                        cmd.CommandText = sOrderCmd;
                                        cmd.ExecuteNonQuery();
                                    }
                                   
                                }
                            }
                        } 
                        #endregion

 
                        pre_branch_no = branch_no;
                        bool bChangeQty = false;
                        if (m_redFlag == "0" || m_redFlag == "")
                        {
                            bChangeQty = !sheetRow.bStoreApproved;
                        }
                        else if (m_redFlag == "2")
                        {
                            bChangeQty = sheetRow.bStoreApproved;
                        }
                        bool bItemIgnoreStock = false; string combine_sta = ""; string use_sn = ""; string service_flag = ""; 
                        //if (CPubVars.g_BusinessType == "餐饮" || CPubVars.g_BusinessType == "快餐" || CPubVars.g_BusinessType == "酒店")
                        {
                            cmd.CommandText = "select ignore_stock,combine_sta,use_sn,service_flag from info_item_prop where item_no='" + sheetRow.item_no + "'";
                            dr = cmd.ExecuteReader();
                            if (dr.Read())
                            {
                                bItemIgnoreStock = (CPubVars.GetTextFromDr(dr, "ignore_stock") == "1");
                                combine_sta=CPubVars.GetTextFromDr(dr, "combine_sta");
                                use_sn = CPubVars.GetTextFromDr(dr, "use_sn");
                                service_flag = CPubVars.GetTextFromDr(dr, "service_flag");
                                if (service_flag == "1")
                                    bItemIgnoreStock = true;                              
                            }
                            dr.Close();
                        }
                        
                        double rowPendQty = sheetRow.now_pend_qty;
                        if (inout_flag != 0 && bChangeQty)
                        {
                            nQuantity = sheetRow.quantity - rowPendQty;
                            nQuantity *= unit_factor;
                            string sCmdUpdateSheet = "";

                            if (!sheetRow.bIgnoreStoreApprove)
                            {
                               
                                    CDbDealer dbDeal = new CDbDealer();
                                    double stock_qty = nQuantity * inout_flag;

                                    dbDeal.AddField("item_no", item_no);
                                    dbDeal.AddField("branch_no", branch_no);
                                    if (size != "")
                                        dbDeal.AddField("size_id", size);
                                    if (color != "")
                                        dbDeal.AddField("color_id", color);
                                    dbDeal.AddField("stock_qty", stock_qty.ToString(), "number");

                                    string firstinout_date =CPubVars.GetDateText(DateTime.Now);
                                    dbDeal.AddField("firstinout_date", firstinout_date, "date");
                                    
                                    string insert_sql = dbDeal.GetInsertSQL("stock");
                                    string sQty = stock_qty.ToString();
                                    if (stock_qty >= 0)
                                    {
                                        sQty = "+" + stock_qty.ToString();
                                        // sCostAmt = "+" + sCostAmt;
                                    }
                                    else
                                    {
                                        // sCostAmt = "-" + sCostAmt;
                                    }
                                    dbDeal.SetField("stock_qty", "stock_qty" + sQty, "number");
                                    //dbDeal.SetField("cost_amt", cost_price.ToString() + " * (stock_qty" + sQty + ")", "number");
                                    //dbDeal.SetField("cost_amt", "cost_amt " + sCostAmt, "number");
                                   if (!bItemIgnoreStock)
                                   {
                                        long nStockID = -1;        
                                        string update_sql = dbDeal.GetUpdateSQL("stock", "item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi);                                                         
                                     
                                            if (m_bDisableNagativeStock)
                                            {
                                                if (inout_flag == -1)
                                                {
                                                    double now_stock_qty = 0;
                                                    cmd.CommandText = "select stock_qty from stock where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi;
                                                    ov = cmd.ExecuteScalar();
                                                    if (ov != null && ov != DBNull.Value)
                                                    {
                                                        now_stock_qty = Convert.ToDouble(ov);
                                                    }
                                                    double new_stock_qty = now_stock_qty + stock_qty;
                                                    if (new_stock_qty < 0)
                                                    {
                                                        err = sheetRow.item_name + "库存量不足";
                                                        goto exit_fun;
                                                    }
                                                }
                                            }
                                        
                                            string edit_sql = "if exists(select item_no from stock where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi + ") " + update_sql + " else " + insert_sql + ";";
                                            remote_sql += edit_sql;        
                                                    
                                }
                         
                                #region 判断是否需要自动拆分父商品
                                if (CMySbConnection.g_dbStyle == 0)
                                    if (m_SheetType == SHEET_TYPE.SHEET_SALE)
                                    {
                                            cmd.CommandText = "select stock_qty from stock where item_no='" + item_no + "' and branch_no='" + branch_no + "'";
                                            ov = cmd.ExecuteScalar();
                                            double cur_qty = 0;
                                            if (ov != null && ov != DBNull.Value)
                                            {
                                                cur_qty = Convert.ToDouble(ov);
                                            }
                                            double next_qty = cur_qty + stock_qty;
                                            if (cur_qty >= 0 && next_qty < 0)
                                            {
                                                cmd.CommandText = "select * from info_combine_model where son_no='" + item_no + "' and auto_split=1";
                                                dr = cmd.ExecuteReader();
                                                if (dr.Read())
                                                {
                                                    string father_no = "";
                                                    ov = dr["father_no"];
                                                    if (ov != DBNull.Value)
                                                        father_no = ov.ToString();
                                                    ov = dr["son_number"];
                                                    double son_num = 0;
                                                    if (ov != DBNull.Value)
                                                    {
                                                        son_num = Convert.ToDouble(ov);
                                                    }
                                                    double father_num = 0;
                                                    if (son_num != 0)
                                                        father_num = Math.Abs(next_qty / son_num) + 1;
                                                    father_num = Convert.ToInt32(father_num);
                                                    SheetRow fatherRow = null;
                                                    if (father_no != "" && father_num > 0)
                                                    {
                                                        if (NeedSplitItems.ContainsKey(father_no))
                                                        {
                                                            fatherRow = NeedSplitItems[father_no];
                                                        }
                                                        else
                                                        {
                                                            fatherRow = new SheetRow();
                                                            fatherRow.item_no = father_no;
                                                            NeedSplitItems.Add(fatherRow.item_no, fatherRow);
                                                        }
                                                        //if (fatherRow.quantity < father_num)
                                                        //{
                                                        fatherRow.quantity += father_num;
                                                        //}
                                                        if (fatherRow.son_nos == null) fatherRow.son_nos = "";
                                                        if (!fatherRow.son_nos.Contains(item_no))
                                                        {
                                                            fatherRow.son_nos += "," + item_no;
                                                        }
                                                        fatherRow.unit_no = unit_no;
                                                    }

                                                }
                                                dr.Close();
                                            }
                                        

                                    }
                                #endregion 判断是否需要自动拆分父商品

                                string sRowUpdateCondi = "";
                                if (sheetRow.flow_id != "" && m_SheetType != SHEET_TYPE.SHEET_MOVE_STORE)
                                {
                                    sRowUpdateCondi = " flow_id='" + sheetRow.flow_id + "'";
                                }
                                else
                                    sRowUpdateCondi = " sheet_no='" + sheet_no + "' and item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi;
                                string sCmdApprove = "update "+m_tb_detail+" set branch_approve_time ='" + sNow + "',branch_approve_oper='" + m_oper_id + "' where " + sRowUpdateCondi +";";

                                nApproveRows++;
                                if (CMySbConnection.g_dbStyle == 0)
                                {
                                   // remote_sql += sCmdApprove;
                                    update_sheet_sql += sCmdApprove;
                                                              
                                }
                                else
                                {                                    
                                    cmd.CommandText = sCmdApprove;
                                    cmd.ExecuteNonQuery();
                                }
                                bStockChanged = true;         
                            }
                            if (CMySbConnection.g_dbStyle != 2)
                            {
                                string pend_qty_fld = "";
                                SHEET_TYPE sheetType = m_SheetType;
                                if (m_SheetType == SHEET_TYPE.SHEET_STORE_IN || m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
                                    sheetType = m_OrigSheetType;
                                if (sheetType == SHEET_TYPE.SHEET_SALE)
                                {
                                    pend_qty_fld = "sell_pend_qty";
                                }
                                else if (sheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                                {
                                    pend_qty_fld = "sell_re_pend_qty";
                                }
                                else if (sheetType == SHEET_TYPE.SHEET_BUY)
                                {
                                    pend_qty_fld = "buy_pend_qty";
                                }
                                else if (sheetType == SHEET_TYPE.SHEET_BUY_RETURN)
                                {
                                    pend_qty_fld = "buy_re_pend_qty";
                                }
                                else if (sheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                                {
                                    if (m_SheetType == SHEET_TYPE.SHEET_STORE_IN)
                                    {
                                        pend_qty_fld = "move_in_pend_qty";
                                    }
                                    else if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
                                    {
                                        pend_qty_fld = "move_in_pend_qty";
                                    }
                                    else
                                    {
                                        if (branch_no == m_branch_no)
                                        {
                                            pend_qty_fld = "move_out_pend_qty";
                                        }
                                        else if (branch_no == m_in_branch_no)
                                        {
                                            pend_qty_fld = "move_in_pend_qty";
                                        }
                                    }
                                }
                                else if (sheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM)
                                {
                                    pend_qty_fld = "sy_pend_qty";

                                }
                         
                                if (m_SheetType != SHEET_TYPE.SHEET_STORE_OUT && m_SheetType != SHEET_TYPE.SHEET_STORE_IN)
                                {
                                    if (sheetRow.bIgnoreStoreApprove)
                                    {
                                        rowPendQty = sheetRow.quantity;
                                    }

                                    if (!m_bApproved)
                                    {
                                        if (rowPendQty != 0)
                                        {
                                            //string row_index_condi = "";
                                            //if (sheetRow.sht_row_index != -1)
                                            //{
                                            //    row_index_condi = " and sht_row_index='" + sheetRow.sht_row_index.ToString() + "'";
                                            //}
                                            sCmdUpdateSheet = "update "+m_tb_detail+" set now_pend_qty=" + rowPendQty.ToString() + ",pend_qty=" + rowPendQty.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "' and branch_no='" + branch_no + "' " + size_color_condi +  ";";
                                            if (CMySbConnection.g_dbStyle == 0)
                                            {
                                                remote_sql += sCmdUpdateSheet;
                                            }
                                            else
                                            {
                                                cmd.CommandText = sCmdUpdateSheet;
                                                cmd.ExecuteNonQuery();
                                            }
                                            double nPendQty = rowPendQty;
                                            //nPendQty *= -inout_flag;
                                            string sPendQty = nPendQty.ToString();
                                            string sPendQtyAdd = "";
                                            if (nPendQty >= 0)
                                                sPendQtyAdd = "+" + sPendQty;
                                            else
                                                sPendQtyAdd = sPendQty;

                                            if (pend_qty_fld != "")
                                            {
                                                string sUpdateCmd = "update stock set " + pend_qty_fld + " = " + pend_qty_fld + sPendQtyAdd + " where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi;
                                                string sInsertCmd = "insert into stock (branch_no,item_no,color_id,size_id,stock_qty," + pend_qty_fld + ") values ('" + branch_no + "','" + item_no + "','" + sheetRow.color_id + "','" + sheetRow.size_id + "','0','" + sPendQty + "')";
                                                string sStoreInOutCmd = "if exists(select item_no from stock where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi + ") " + sUpdateCmd + " else " + sInsertCmd + ";";
                                                 remote_sql += sStoreInOutCmd;
                                              
                                            }
                                        }
                                    }
                                    else if (!sheetRow.bIgnoreStoreApprove)
                                    {
                                        double orig_pend_qty = sheetRow.quantity;
                                       
                                        double nReducePendQty = orig_pend_qty - rowPendQty;
  
                                        if (pend_qty_fld != "")
                                        {
                                            string sPendQtyAdd = "";
                                            if (nReducePendQty >= 0)
                                            {
                                                sPendQtyAdd = "-" + nReducePendQty.ToString();
                                            }
                                            else
                                            {
                                                sPendQtyAdd = "+" + (-nReducePendQty).ToString();
                                            }

                                            sCmdUpdateSheet = "update "+m_tb_detail+" set pend_qty=pend_qty" + sPendQtyAdd + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "' and branch_no='" + branch_no + "' " + size_color_condi + ";";


                                            string sStoreInOutCmd = "update stock set " + pend_qty_fld + "=" + pend_qty_fld + sPendQtyAdd + " where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi + ";";
                                            if (CMySbConnection.g_dbStyle == 0)
                                            {
                                                remote_sql += sCmdUpdateSheet + sStoreInOutCmd;
                                            }
                                            else
                                            {
                                                cmd.CommandText = sCmdUpdateSheet;
                                                cmd.ExecuteNonQuery();
                                                cmd.CommandText = sStoreInOutCmd;
                                                cmd.ExecuteNonQuery();
                                            }
                                        }
                                    }
                                }
                                else if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT || m_SheetType == SHEET_TYPE.SHEET_STORE_IN)
                                {
                                    if (!sheetRow.bIgnoreStoreApprove)
                                    {
                                        if (sheetRow.quantity != 0)
                                        {
                                            string orig_sheet_no = m_OrigSheetNo;
                                            if (sheetRow.orig_sheet_no != "") orig_sheet_no = sheetRow.orig_sheet_no;

                                            double pend_qty = 0;
                                            cmd.CommandText = "select pend_qty from "+m_tb_detail+"  where sheet_no='" + orig_sheet_no + "' and item_no='" + sheetRow.item_no + "'  and branch_no='" + branch_no + "' " + size_color_condi + ";";

                                            ov = cmd.ExecuteScalar();
                                            if (ov != null)
                                            {
                                                double pre_pend_qty = 0;
                                                pre_pend_qty = Convert.ToDouble(ov);
                                                if (pre_pend_qty == 0)
                                                {
                                                    if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT )
                                                      err= "编号为"+sheetRow.item_no +"的商品"+sheetRow.item_name+"已经出库完毕,不能再次出库";
                                                    else
                                                      err= "编号为"+sheetRow.item_no +"的商品"+sheetRow.item_name+"已经入库完毕,不能再次入库";                                                    
                                                }
                                                pend_qty = pre_pend_qty - sheetRow.quantity;
                                                if (pend_qty < 0)
                                                {
                                                    if (false)//!OperRight.Can("ck_out_store_dif_qty"))
                                                    {
                                                      //  if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
                                                     //       err = "编号为" + sheetRow.item_no + "的商品" + sheetRow.item_name + "出库数量大于尚未出库数量了";
                                                     //   else
                                                       //     err = "编号为" + sheetRow.item_no + "的商品" + sheetRow.item_name + "入库数量大于尚未入库数量了";
                                                    }
                                                }
                                                if (err != "")
                                                {
                                                    goto exit_fun;
                                                }
                                                     
                                            }
                                          
                                            string origSheetNoCondi = "";
                                            if(sheetRow.orig_sheet_no!="")
                                            {
                                                origSheetNoCondi = " and other4 like 'OSht:%'";
                                            }
                                            else
                                            {
                                                origSheetNoCondi = " and other4 is null";
                                            }
                                            sCmdUpdateSheet += "update " + m_tb_detail + " set pend_qty=" + pend_qty.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "'  and branch_no='" + branch_no + "' " + size_color_condi + origSheetNoCondi +";";

                                            string sBranchApprove = "";
                                            if (pend_qty == 0)
                                            {
                                                sBranchApprove += ",branch_approve_time='" + sNow + "',branch_approve_oper='" + m_oper_id + "'";
                                            }

                                            string sPendQtyAdd = "-" + sheetRow.quantity.ToString();
                                            if (sheetRow.quantity < 0)
                                                sPendQtyAdd = "+" + (-sheetRow.quantity).ToString();
                                           
                                            sCmdUpdateSheet += "update " + m_tb_detail + " set pend_qty=pend_qty" + sPendQtyAdd + sBranchApprove + " where sheet_no='" + orig_sheet_no + "' and item_no='" + sheetRow.item_no + "'  and branch_no='" + branch_no + "' " + size_color_condi + ";";
                                          
                                            //sCmdUpdateSheet += "update "+m_tb_detail+" set pend_qty=pend_qty" + sPendQtyAdd + " where sheet_no='" + m_OrigSheetNo + "' and item_no='" + sheetRow.item_no + "'  and branch_no='" + branch_no + "' " + size_color_condi + ";";

                                            if (CMySbConnection.g_dbStyle == 0)
                                            {
                                                remote_sql += sCmdUpdateSheet;
                                            }
                                            else
                                            {
                                                cmd.CommandText = sCmdUpdateSheet;
                                                cmd.ExecuteNonQuery();
                                            }
                                            if (pend_qty_fld != "")
                                            {
                                                string sStoreInOutCmd = "update stock set " + pend_qty_fld + "=" + pend_qty_fld + sPendQtyAdd  + " where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi + ";";
                                                if (CMySbConnection.g_dbStyle == 0)
                                                {
                                                    remote_sql += sStoreInOutCmd;
                                                }
                                                else
                                                {
                                                    cmd.CommandText = sStoreInOutCmd;
                                                    cmd.ExecuteNonQuery();
                                                }
                                            }
                                        }

                                    }
                                }
                            }

                        }
                        #endregion　改写库存
                    }
                }
                #region 如果是进货单，在商品档案中标记最近进价
                if (CMySbConnection.g_dbStyle == 0)
                {
                    Dictionary<string, string> dicUsedItem = new Dictionary<string, string>();
                    foreach (var row in this.SheetRows)
                    {
                        if (!dicUsedItem.ContainsKey(row.item_no))
                        {
                            if (m_SheetType == SHEET_TYPE.SHEET_BUY)
                            {
                                remote_sql += "update info_item_prop set last_inprice ='" + row.real_price.ToString() + "' where item_no='" + row.item_no + "';";

                            }
                            dicUsedItem.Add(row.item_no, row.item_no);
                        }
                    }
                }
                #endregion

                if (CMySbConnection.g_dbStyle == 0)
                {
                    try
                    {
                       // if (remote_sql != "")
                        if( bStockChanged )
                        {
                            if (nAllSheetRows == nApproveRows)
                            {
                                remote_sql += "update "+m_tb_detail+" set branch_approve_time='" + sNow + "',branch_approve_oper='" + m_oper_id + "' where sheet_no='" + m_sheet_no + "';";
                            }
                            else
                            {
                                remote_sql += update_sheet_sql;
                            }
                            
                        }

                        if (remote_sql != "")
                        {
                            cmd.CommandText = remote_sql;
                            cmd.ExecuteNonQuery();
                        }
                    } 
                    catch (Exception se)
                    {
                        err = se.Message; 
                    }
                }
                #region 标记订单完成标志
                if (m_redFlag != "2" && m_OrigSheetNo != "")
                {
                    if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_BUY)
                    {
                        cmd.CommandText = "select item_no from " + m_tb_order_detail + " where sheet_no='" + m_OrigSheetNo + "' and sent_qty<quantity";
                        if (cmd.ExecuteScalar() == null)
                        {
                            sNow = CPubVars.GetDateText(DateTime.Now);
                            cmd.CommandText = "update "+m_tb_master+" set finish_time ='" + sNow + "' where sheet_no='" + m_OrigSheetNo + "'";
                            cmd.ExecuteNonQuery();
                        }
                        else
                        {
                            cmd.CommandText = "update "+m_tb_master+" set finish_time =null where sheet_no='" + m_OrigSheetNo + "'";
                            cmd.ExecuteNonQuery();
                        }

                    }
                }
                #endregion
                 

                #region 自动拆分

                foreach (KeyValuePair<string, SheetRow> pr in NeedSplitItems)
                {
                    SheetRow com_row = pr.Value;
                    CSheetSale com_sheet = new CSheetSale(SHEET_TYPE.SHEET_SPLIT_GOOD);
                    com_sheet.m_brief = "自动拆分,销售单:" + m_sheet_no + ",销售商品:" + com_row.son_nos;
                    com_sheet.SetGeneralInfo(m_branch_no, "", "", "","", "", "", "", "", "", "", "", "", "", "", "", "", "", false);
                    string com_err = com_sheet.AddRow(com_row.item_no, "", com_row.unit_no, "1", "", "", "", "", "", "", "-" + com_row.quantity, "", 1, "", "", false);
                    // CMySbCommand cmdCombine = new CMySbCommand("", conn_info);
                    cmd.CommandText = "select * from info_combine_model where father_no='" + com_row.item_no + "'";
                    dr = cmd.ExecuteReader();
                    while (dr.Read())
                    {
                        ov = dr["son_no"];
                        string son_no = "";
                        if (ov != null && ov != DBNull.Value)
                            son_no = ov.ToString().Trim();
                        ov = dr["son_number"];
                        double son_num = 0;
                        if (ov != null && ov != DBNull.Value)
                            son_num = Convert.ToDouble(ov);

                        if (son_no != "" && son_num != 0)
                        {
                            son_num *= com_row.quantity;
                            com_sheet.AddRow(son_no, "", "0", "1", "", "", "", "", "", "", son_num.ToString(), "", 1, "", "", false);
                        }
                    }
                    dr.Close();
                    err = com_sheet.SaveSheet(conn, cmd, tran);
                    if (err == "")
                        err = com_sheet.Approve(conn, cmd, tran);

                }
                #endregion
                 
            
            } 
            catch (Exception eee)
            {
                err = eee.Message;
            }
    exit_fun:
            if (!bConnPassed)
            {
                if (err == "")
                    tran.Commit();
                else
                    tran.Rollback();
                cmd.Dispose();
                conn.Close();
                conn.Dispose();
                tran.Dispose();
            }
            if (err == "")
                m_bApproved = true;
            return err;

        }

        public string Approve_Good_Red(CMySbConnection conn, CMySbCommand cmd, CMySbTransaction tran)
        {           

            if (m_branch_no == "")
            {
                if (m_SheetType != SHEET_TYPE.SHEET_MOVE_STORE)
                    {
                        bool bHaveEmptyBranchRow = false;
                        foreach (var sheetRow in SheetRows)
                        { 
                            if (sheetRow.branch_no == "")
                            {
                                bHaveEmptyBranchRow = true;
                                break;
                            }
                        }
                        if (bHaveEmptyBranchRow)
                        {
                            return "请指定本单据使用的仓库或单项商品所属的仓库.";
                        }
                    }
            }
            string err = "";
            string sheet_no = m_sheet_no;
            string connString = "";// myJXC.CPubVars.ConnString;
            bool bConnPassed = false;
            try
            {
                if (conn == null)
                {
                    conn = new CMySbConnection(connString);
                    conn.Open();
                    cmd = new CMySbCommand("", conn);
                }
                else
                {
                    bConnPassed = true;
                }
                if (tran == null)
                {
                    tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                }
                CMySbDataReader dr;
                cmd.CommandText = "SELECT * from "+m_tb_master+" where sheet_no ='" + m_sheet_no + "'";
                dr = cmd.ExecuteReader();
                string strErr = "";
                if (!dr.Read())
                {
                    dr.Close();
                    // conn.Close();
                    strErr = "该单据不存在";
                    return strErr;
                }

                string branch_no = dr["branch_no"].ToString();
                //string branch_approve_time = "";
                //if (dr["branch_approve_time"] != null)
                //    branch_approve_time = dr["branch_approve_time"].ToString();
                dr.Close();
                // if (branch_approve_time != "")
                //   return "该单据已经审批过库存";
                object ov = null;
                string remote_sql = "";
                string update_sheet_sql = "";
                int nApproveRows = 0; int nAllSheetRows = 0;
                #region Update sheet for produce date
                bool bSheetBranchIgnoreBatch = false;
                bool bSheetInBranchIgnoreBatch = false;
                if ( false)
                {
                    // || m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN 
                    if (m_SheetType == SHEET_TYPE.SHEET_BUY || m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE || m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD || m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD)
                    {
                        cmd.CommandText = "select ignore_batch from info_branch where branch_no='" + m_branch_no + "'";
                        ov = cmd.ExecuteScalar();
                        if (ov != null && ov != DBNull.Value)
                        {
                            bSheetBranchIgnoreBatch = (ov.ToString() == "1");
                        }
                        cmd.CommandText = "select ignore_batch from info_branch where branch_no='" + m_in_branch_no + "'";
                        ov = cmd.ExecuteScalar();
                        if (ov != null && ov != DBNull.Value)
                        {
                            bSheetInBranchIgnoreBatch = (ov.ToString() == "1");
                        }

                        int inout_flag_item = 0;

                        Dictionary<string, SheetRow> sheetRowsDic = new Dictionary<string, SheetRow>();
                        string sCmdProduceDate = "";
                        string split_com_produce_date = "";
                        string item_branch = branch_no;
                        foreach (var sheetRow in SheetRows)
                        {
                            bool bItemBranchIgnoreBatch = bSheetBranchIgnoreBatch;
                   
                            if (sheetRow.branch_no != "")
                            {
                                item_branch = sheetRow.branch_no;
                                cmd.CommandText = "select ignore_batch from info_branch where branch_no='" + sheetRow.branch_no + "'";
                                ov = cmd.ExecuteScalar();
                                if (ov != null && ov != DBNull.Value)
                                {
                                    bItemBranchIgnoreBatch = (ov.ToString() == "1");
                                }
                            }
                            sheetRow.bItemBranchIgnoreBatch = bItemBranchIgnoreBatch;
                            if (!bItemBranchIgnoreBatch || m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                            {
                                bool bReduceItem = false;
                                if (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD)
                                {
                                    if (sheetRow.quantity < 0)
                                    {
                                        bReduceItem = true;
                                    }
                                }
                                else if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD)
                                {
                                    if (sheetRow.quantity > 0)
                                    {
                                        bReduceItem = true;
                                    }
                                }
                                else
                                    bReduceItem = true;

                                cmd.CommandText = "select color_group from info_item_prop where item_no='" + sheetRow.item_no + "'";
                                ov = cmd.ExecuteScalar();
                                if (ov != null && ov != DBNull.Value)
                                {
                                    if (ov.ToString().Trim() != "")
                                    {
                                        sheetRow.bNeedProduceDate = true;
                                    }
                                }
                                if (!sheetRow.bNeedProduceDate)
                                {
                                    sheetRowsDic.Add(sheetRowsDic.Count.ToString(), sheetRow);
                                }
                                else if (sheetRow.color_id != "0" && (m_SheetType == SHEET_TYPE.SHEET_BUY || m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE))
                                {

                                    if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                                        item_branch = m_in_branch_no;
                                    cmd.CommandText = "select stock_qty from stock where branch_no='" + item_branch + "' and item_no='" + sheetRow.item_no + "' and size_id='" + sheetRow.size_id + "'and color_id='0' and stock_qty<0";
                                    ov = cmd.ExecuteScalar();
                                    double needFeedQty = 0;
                                    if (ov != null && ov != DBNull.Value)
                                    {
                                        needFeedQty = Convert.ToDouble(ov);
                                        needFeedQty = -needFeedQty;
                                    }
                                    if (needFeedQty > 0)
                                    {
                                        if (sheetRow.quantity > needFeedQty)
                                        {
                                            SheetRow newSheetRow = sheetRow.colonMe();
                                            newSheetRow.color_id = "0";
                                            newSheetRow.quantity = needFeedQty;
                                            sheetRowsDic.Add(sheetRowsDic.Count.ToString(), newSheetRow);
                                            newSheetRow = sheetRow.colonMe();
                                            newSheetRow.color_id = sheetRow.color_id;
                                            newSheetRow.quantity = sheetRow.quantity - needFeedQty;
                                            sheetRowsDic.Add(sheetRowsDic.Count.ToString(), newSheetRow);
                                        }
                                        else
                                        {
                                            SheetRow newSheetRow = sheetRow.colonMe();
                                            newSheetRow.color_id = "0";
                                            newSheetRow.quantity = sheetRow.quantity;
                                            sheetRowsDic.Add(sheetRowsDic.Count.ToString(), newSheetRow);
                                        }
                                    }
                                    else
                                    {
                                        sheetRowsDic.Add(sheetRowsDic.Count.ToString(), sheetRow);
                                    }

                                }
                                else if (sheetRow.color_id == "0")
                                {
                                    if (bReduceItem)
                                    {
                                        sCmdProduceDate += "delete from "+m_tb_detail+" where sheet_no='" + sheet_no + "' and item_no='" + sheetRow.item_no + "' and size_id='" + sheetRow.size_id + "';";
                                        SheetRow newSheetRow = null;
                                        //newSheetRow.quantity =
                                        double orig_qty = sheetRow.quantity;
                                        double free_qty = sheetRow.quantity;
                                        cmd.CommandText = "select * from stock left join info_item_color on stock.color_id=info_item_color.color_id where item_no='" + sheetRow.item_no + "' and size_id='" + sheetRow.size_id + "'and stock.color_id<>'0' order by produce_date";
                                        dr = cmd.ExecuteReader();
                                        while (dr.Read())
                                        {
                                            double left_qty = 0;
                                            ov = dr["stock_qty"];
                                            if (ov != DBNull.Value) left_qty = Convert.ToDouble(ov);
                                            string cur_color = "0";
                                            ov = dr["color_id"];
                                            if (ov != DBNull.Value) cur_color = ov.ToString().Trim();
                                            if (left_qty >= free_qty)
                                            {
                                                newSheetRow = sheetRow.colonMe();
                                                newSheetRow.color_id = cur_color;
                                                newSheetRow.quantity = free_qty;
                                                newSheetRow.bNewForProduceDate = true;
                                                if (bReduceItem && (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD || m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD))
                                                {
                                                    split_com_produce_date = newSheetRow.color_id;
                                                }
                                                sheetRowsDic.Add(sheetRowsDic.Count.ToString(), newSheetRow);
                                                break;
                                            }
                                            else if (left_qty > 0)
                                            {
                                                newSheetRow = sheetRow.colonMe();
                                                newSheetRow.color_id = cur_color;
                                                newSheetRow.quantity = left_qty;
                                                newSheetRow.bNewForProduceDate = true;
                                                if (bReduceItem && (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD || m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD))
                                                {
                                                    split_com_produce_date = newSheetRow.color_id;
                                                }
                                                sheetRowsDic.Add(sheetRowsDic.Count.ToString(), newSheetRow);
                                                free_qty -= left_qty;
                                            }
                                        }
                                        dr.Close();
                                        if (free_qty > 0)
                                        {
                                            newSheetRow = sheetRow.colonMe();
                                            newSheetRow.color_id = "0";//
                                            newSheetRow.quantity = free_qty;
                                            newSheetRow.bNewForProduceDate = true;
                                            if (bReduceItem && (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD || m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD))
                                            {
                                                split_com_produce_date = newSheetRow.color_id;
                                            }
                                            sheetRowsDic.Add(sheetRowsDic.Count.ToString(), newSheetRow);
                                        }
                                    }
                                    else
                                    {
                                        sheetRowsDic.Add(sheetRowsDic.Count.ToString(), sheetRow);
                                    }
                                }
                                else
                                {
                                    if (bReduceItem && (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD || m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD))
                                    {
                                        split_com_produce_date = sheetRow.color_id;
                                    }
                                    sheetRowsDic.Add(sheetRowsDic.Count.ToString(), sheetRow);
                                }
                            }
                            else
                            {
                                sheetRowsDic.Add(sheetRowsDic.Count.ToString(), sheetRow);
                            }
                        }
                        if (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD || m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD)
                        {
                            foreach (KeyValuePair<string, SheetRow> di in sheetRowsDic)
                            {
                                SheetRow sheetRow = di.Value;
                                bool bReduceItem = false;
                                if (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD)
                                {
                                    if (sheetRow.quantity < 0)
                                    {
                                        bReduceItem = true;
                                    }
                                }
                                else if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD)
                                {
                                    if (sheetRow.quantity > 0)
                                    {
                                        bReduceItem = true;
                                    }
                                }
                                if (!bReduceItem)
                                {
                                    if (sheetRow.color_id == "0" && sheetRow.bNeedProduceDate)
                                    {
                                        if (split_com_produce_date != "")
                                        {
                                            sheetRow.color_id = split_com_produce_date;
                                        }
                                    }
                                }
                            }
                        }
                        foreach (var sheetRow in SheetRows)
                        {
                           
                            if (sheetRow.bNewForProduceDate)
                            {
                                sheetRow.inout_flag = inout_flag_item;
                                // inout_flag_item = inout_flag;
                                CDbDealer dbDeal = new CDbDealer();
                                dbDeal.AddField("item_no", sheetRow.item_no);
                                dbDeal.AddField("sheet_no", sheet_no);
                                string s_branch_no = m_branch_no;
                                if (sheetRow.branch_no != "")
                                    s_branch_no = sheetRow.branch_no;
                                dbDeal.AddField("branch_no", s_branch_no);
                                dbDeal.AddField("unit_no", sheetRow.unit_no.ToString());
                                if (sheetRow.size_id == "")
                                    sheetRow.size_id = "0";
                                dbDeal.AddField("size_id", sheetRow.size_id);
                                if (sheetRow.color_id == "")
                                    sheetRow.color_id = "0";
                                dbDeal.AddField("color_id", sheetRow.color_id);
                                if (sheetRow.worker_id != "")
                                    dbDeal.AddField("worker_id", sheetRow.worker_id);
                                if (sheetRow.servicer_id != "")
                                    dbDeal.AddField("servicer_id", sheetRow.servicer_id);
                                if (sheetRow.servicer1_id != "")
                                    dbDeal.AddField("servicer1_id", sheetRow.servicer1_id);

                                dbDeal.AddField("inout_flag", inout_flag_item.ToString(), "number");
                                dbDeal.AddField("unit_factor", sheetRow.unit_factor.ToString(), "number");
                                dbDeal.AddField("orig_price", sheetRow.orig_price.ToString(), "number");
                                dbDeal.AddField("valid_price", sheetRow.real_price.ToString(), "number");
                                dbDeal.AddField("quantity", Math.Abs(sheetRow.quantity).ToString(), "number");

                                dbDeal.AddField("sub_amount", sheetRow.money.ToString());
                                string s_tax_amount = CPubVars.FormatMoney(sheetRow.tax_amount, 4);
                                dbDeal.AddField("tax_amount", s_tax_amount, "number");
                                //dbDeal.AddField("box_price", "0", "number");
                                //dbDeal.AddField("tax", "0", "number");
                                //dbDeal.AddField("is_tax", "0", "number");

                                string sCmd = dbDeal.GetInsertSQL(m_tb_detail);
                                sCmdProduceDate += sCmd;

                                if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                                {
                                    dbDeal.SetField("inout_flag", (-inout_flag_item).ToString(), "number");
                                    dbDeal.AddField("left_quantity", sheetRow.quantity.ToString());
                                    dbDeal.SetField("branch_no", m_in_branch_no, "");
                                    if (CMySbConnection.g_dbStyle == 0)
                                        sCmdProduceDate += dbDeal.GetInsertSQL(m_tb_detail);
                                    else
                                    {
                                        cmd.CommandText = dbDeal.GetInsertSQL(m_tb_detail);
                                        cmd.ExecuteNonQuery();
                                    }

                                }
                            }
                        }
                        if (sCmdProduceDate != "")
                        {
                            remote_sql += sCmdProduceDate;
                        }
                        
                    }
                }
                #endregion
                //cmd.CommandText = "SELECT * from "+m_tb_detail+" where sheet_no ='" + sheet_no + "'";
                //dr = cmd.ExecuteReader();
                //if (!dr.HasRows)
                //{
                //    err = "该单据不存在详单";
                //    goto endfun;
                //}
                //如果用2个cmd同在一个事务下，如果第一个cmd打开超过２０条记录的dr,第二个cmd执行就会发生错误.所以这里创建另外一个connection conn_info，用于其他的cmd

                int nStepCount = 1;

                if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                {
                    nStepCount = 2;
                }
                // string sNow = CPubVars.GetDateText(DateTime.Now);
                string sNow = "";
           
               
                    cmd.CommandText = "Select CONVERT(varchar(100), GETDATE(), 120)";
                    //cmd.CommandText = MyCeJXC.CPubVars.GetCeSQL(cmd.CommandText);                    
                    ov = cmd.ExecuteScalar();
                    if(ov!=null && ov!=DBNull.Value)
                       sNow = CPubVars.GetDateText(ov);
               
               
                string pre_branch_no = ""; string keepers = ""; string need_confirm = ""; bool bStoreApproved = false;
                Dictionary<string, SheetRow> NeedSplitItems = new Dictionary<string, SheetRow>();
                for (int nStep = 1; nStep <= nStepCount; nStep++)
                {
                    foreach (var sheetRow in SheetRows)
                    {
                      
                        // CMySbConnection conn_info = new CMySbConnection(CPubVars.ConnString);
                        //conn_info.Open();
                        nAllSheetRows++;
                        #region  改写库存

                        //  CMySbCommand cmdItemInfo = new CMySbCommand("", conn_info);                    
                        string item_no = sheetRow.item_no;
                        string unit_no = sheetRow.unit_no;
                        if (sheetRow.is_service)
                            continue;
                        double unit_factor = sheetRow.unit_factor;
                       
                        double nQuantity = sheetRow.quantity - sheetRow.now_pend_qty;
                        string size = sheetRow.size_id;
                        string color = sheetRow.color_id;

                        int inout_flag = sheetRow.inout_flag;
                        //if (m_redFlag == "2")
                        //{
                        //    inout_flag *= -1;
                        //}
                        double rowPendQty = sheetRow.now_pend_qty;
                        if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                        {
                            if (nStep == 1)
                            {
                                branch_no = m_in_branch_no;
                                inout_flag *= -1;
                                if (bSheetInBranchIgnoreBatch)
                                {
                                    color = "0";
                                }
                                rowPendQty = sheetRow.now_pend_qty_d;  
                            }
                            else
                            {
                                branch_no = m_branch_no;
                                if (bSheetBranchIgnoreBatch)
                                {
                                    color = "0";
                                }
                                rowPendQty = sheetRow.now_pend_qty;
                            }
                        }
                        else
                        {
                            if (sheetRow.bItemBranchIgnoreBatch)
                            {
                                color = "0";
                            }
                            branch_no = sheetRow.branch_no;
                            if (branch_no == "")
                            {
                                branch_no = m_branch_no;
                            }
                        }
                        if (branch_no == "")
                            continue;
                        string size_condi = "";
                        if (size != "")
                        {
                            size_condi = " size_id='" + size + "' ";
                        }
                        else
                        {
                            size_condi = " size_id ='0' ";
                        }
                        string color_condi = "";
                        if (color != "")
                        {
                            color_condi = " color_id='" + color + "' ";
                        }
                        else
                        {
                            color_condi = " color_id ='0' ";
                        }
                        string size_color_condi = size_condi;
                        if (color_condi != "")
                        {
                            if (size_color_condi != "")
                            {
                                size_color_condi += " and ";
                            }
                            size_color_condi += color_condi;
                        }
                        if (size_color_condi != "")
                            size_color_condi = " and " + size_color_condi;

                        if (pre_branch_no != branch_no)
                        {
                            bStoreApproved = false;
                            cmd.CommandText = "select keepers,need_keeper_confirm from info_branch where branch_no='" + branch_no + "'";
                            dr = cmd.ExecuteReader();
                            if (dr.Read())
                            {
                                need_confirm = CPubVars.GetTextFromDr(dr, "need_keeper_confirm");
                                keepers = "," + CPubVars.GetTextFromDr(dr, "keepers") + ",";
                            }
                            dr.Close();
                            cmd.CommandText = "select branch_approve_time from "+m_tb_detail+" where sheet_no='" + m_OrigSheetNo + "' and item_no='" + sheetRow.item_no + "' and branch_no='" + branch_no + "' " + size_color_condi + ";";
                            // cmd.CommandText = "select branch_approve_time from "+m_tb_detail+" where sheet_no='" + m_SaleSheetNo + "' and item_no='" + sheetRow.item_no + "' and branch_no='" + branch_no + "' " + size_color_condi + ";";

                            ov = cmd.ExecuteScalar();
                            if (ov != null && ov != DBNull.Value)
                            {
                                bStoreApproved = true;
                            }
                        }

                        #region 标记订单完成数量
                        if (m_OrigOrigSheetNo != "")
                        {
                            if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_BUY)
                            {
                                string sQuantity = "";
                                if (nQuantity >= 0)
                                    sQuantity = "+" + nQuantity;
                                else
                                    sQuantity = nQuantity.ToString();

                                string sOrderCmd = "update " + m_tb_order_detail + " set sent_qty=sent_qty " + sQuantity + " where sheet_no='" + m_OrigOrigSheetNo + "' and item_no='" + item_no + "' and unit_factor=" + unit_factor.ToString() + " and branch_no='" + branch_no + "'" + size_color_condi + ";";

                                if (CMySbConnection.g_dbStyle != 2)
                                {
                                    remote_sql += sOrderCmd;
                                }
                                else
                                {
                                    cmd.CommandText = sOrderCmd;
                                    cmd.ExecuteNonQuery();
                                }
                                //string sOrderCmd = "update " + m_tb_order_detail + " set sent_qty=sent_qty + " + nQuantity.ToString() + " where sheet_no='" + m_OrigSheetNo + "' and item_no='" + item_no + "' and unit_factor=" + unit_factor.ToString() + " and branch_no='" + branch_no + "'" + size_color_condi + ";";


                            }
                        }
                        #endregion

                        if (need_confirm == "1")
                        {
                            if (!keepers.Contains("," + m_oper_id + ","))
                            {
                                sheetRow.bIgnoreStoreApprove = true;
                            }
                        }

                        sheetRow.bStoreApproved = bStoreApproved;

                        pre_branch_no = branch_no;
                        bool bChangeQty = false;
                        if (m_redFlag == "0" || m_redFlag == "")
                        {
                            bChangeQty = !sheetRow.bStoreApproved;
                        }
                        else if (m_redFlag == "2")
                        {
                            bChangeQty = true;// sheetRow.bStoreApproved;// sheetRow.bStoreApproved;
                        }

                        bool bItemIgnoreStock = false; string combine_sta = ""; string use_sn = "";
                        //if (CPubVars.g_BusinessType == "餐饮" || CPubVars.g_BusinessType == "快餐" || CPubVars.g_BusinessType == "酒店")
                        {
                            cmd.CommandText = "select ignore_stock,combine_sta,use_sn from info_item_prop where item_no='" + sheetRow.item_no + "'";
                            dr = cmd.ExecuteReader();
                            if (dr.Read())
                            {
                                bItemIgnoreStock = (CPubVars.GetTextFromDr(dr, "ignore_stock") == "1");
                                combine_sta = CPubVars.GetTextFromDr(dr, "combine_sta");
                                use_sn = CPubVars.GetTextFromDr(dr, "use_sn");
                            }
                            dr.Close();
                        }
                        
                        if (inout_flag != 0 && bChangeQty)
                        {
                            nQuantity = sheetRow.quantity - rowPendQty;
                            nQuantity *= unit_factor;
                            string sCmdUpdateSheet = "";

                           // if (!sheetRow.bIgnoreStoreApprove)
                            if(sheetRow.bStoreApproved)
                            {

                                CDbDealer dbDeal = new CDbDealer();
                                double stock_qty = nQuantity * inout_flag;

                                dbDeal.AddField("item_no", item_no);
                                dbDeal.AddField("branch_no", branch_no);
                                if (size != "")
                                    dbDeal.AddField("size_id", size);
                                if (color != "")
                                    dbDeal.AddField("color_id", color);
                                dbDeal.AddField("stock_qty", stock_qty.ToString(), "number");

                                string firstinout_date = CPubVars.GetDateText(DateTime.Now);
                                // dbDeal.AddField("firstinout_date", firstinout_date, "date");

                                string insert_sql = dbDeal.GetInsertSQL("stock");
                                string sQty = stock_qty.ToString();
                                if (stock_qty >= 0)
                                {
                                    sQty = "+" + stock_qty.ToString();
                                    // sCostAmt = "+" + sCostAmt;
                                }
                                else
                                {
                                    // sCostAmt = "-" + sCostAmt;
                                }
                                dbDeal.SetField("stock_qty", "stock_qty" + sQty, "number");
                                //dbDeal.SetField("cost_amt", cost_price.ToString() + " * (stock_qty" + sQty + ")", "number");
                                
                                //if (color != "" && color != "0")
                                //{
                                //    cmd.CommandText = "select color_state,color_name from info_item_color where color_id='" + color + "'";
                                //    dr = cmd.ExecuteReader();
                                //    if (!dr.Read())
                                //    {
                                //        dr.Close();
                                //        err = "颜色:" + sheetRow.color_name + "不存在";
                                //        goto exit_fun;
                                //    }
                                //    dr.Close(); 
                                //}
                                if (!bItemIgnoreStock)
                                {
                                    long nStockID = -1;         
                                    string update_sql = dbDeal.GetUpdateSQL("stock", "item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi);
                                    if (CMySbConnection.g_dbStyle == 0)
                                    {
                                        if (use_sn == "1")
                                        {
                                            cmd.CommandText = "select stock_flow from stock where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi;
                                            ov = cmd.ExecuteScalar();

                                            if (ov == null)
                                            {
                                                cmd.CommandText = insert_sql + "; select @@identity from stock";
                                                ov = cmd.ExecuteScalar();
                                                if (ov != null && ov != DBNull.Value)
                                                {
                                                    nStockID = Convert.ToInt32(ov);
                                                }

                                            }
                                            else
                                            {
                                                nStockID = Convert.ToInt32(ov);
                                                cmd.CommandText = update_sql;
                                                cmd.ExecuteNonQuery();
                                            }
                                        }
                                        else
                                        {
                                            string edit_sql = "if exists(select item_no from stock where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi + ") " + update_sql + " else " + insert_sql + ";";
                                            remote_sql += edit_sql;
                                        }                                       
                                    }
                                    else
                                    {
                                        if (use_sn == "1")
                                        {
                                            cmd.CommandText = "select stock_flow from stock where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi;
                                            ov = cmd.ExecuteScalar();
                                            if (ov == null)
                                            {
                                                cmd.CommandText = insert_sql + "; select last_insert_rowid() from stock";
                                                ov = cmd.ExecuteScalar();
                                                if (ov != null && ov != DBNull.Value)
                                                {
                                                    nStockID = Convert.ToInt32(ov);
                                                }
                                            }
                                            else
                                            {
                                                nStockID = Convert.ToInt32(ov);
                                                cmd.CommandText = update_sql;
                                                cmd.ExecuteNonQuery();
                                            }
                                        }
                                        else
                                        {
                                            cmd.CommandText = "select item_no from stock where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi;
                                            if (cmd.ExecuteScalar() == null)
                                            {
                                                cmd.CommandText = insert_sql;
                                            }
                                            else
                                            {
                                                cmd.CommandText = update_sql;
                                            }
                                            cmd.ExecuteNonQuery();
                                        } 
                                    }
                                
                                }
                                #region 判断是否需要自动拆分父商品
                                if (CMySbConnection.g_dbStyle == 0)
                                    if (m_SheetType == SHEET_TYPE.SHEET_SALE)
                                    {
                                        // CMySbCommand cmdCombine = new CMySbCommand("", conn_info);
                                        cmd.CommandText = "select stock_qty from stock where item_no='" + item_no + "' and branch_no='" + branch_no + "'";
                                        ov = cmd.ExecuteScalar();
                                        double cur_qty = 0;
                                        if (ov != null && ov != DBNull.Value)
                                        {
                                            cur_qty = Convert.ToDouble(ov);
                                        }
                                        double next_qty = cur_qty + stock_qty;
                                        if (cur_qty >= 0 && next_qty < 0)
                                        {
                                            cmd.CommandText = "select * from info_combine_model where son_no='" + item_no + "' and auto_split=1";
                                            dr = cmd.ExecuteReader();
                                            if (dr.Read())
                                            {
                                                string father_no = "";
                                                ov = dr["father_no"];
                                                if (ov != DBNull.Value)
                                                    father_no = ov.ToString();
                                                ov = dr["son_number"];
                                                double son_num = 0;
                                                if (ov != DBNull.Value)
                                                {
                                                    son_num = Convert.ToDouble(ov);
                                                }
                                                double father_num = 0;
                                                if (son_num != 0)
                                                    father_num = Math.Abs(next_qty / son_num) + 1;
                                                father_num = Convert.ToInt32(father_num);
                                                SheetRow fatherRow = null;
                                                if (father_no != "" && father_num > 0)
                                                {
                                                    if (NeedSplitItems.ContainsKey(father_no))
                                                    {
                                                        fatherRow = NeedSplitItems[father_no];
                                                    }
                                                    else
                                                    {
                                                        fatherRow = new SheetRow();
                                                        fatherRow.item_no = father_no;
                                                        NeedSplitItems.Add(fatherRow.item_no, fatherRow);
                                                    }
                                                    //if (fatherRow.quantity < father_num)
                                                    //{
                                                    fatherRow.quantity += father_num;
                                                    //}
                                                    if (fatherRow.son_nos == null) fatherRow.son_nos = "";
                                                    if (!fatherRow.son_nos.Contains(item_no))
                                                    {
                                                        fatherRow.son_nos += "," + item_no;
                                                    }
                                                    fatherRow.unit_no = unit_no;
                                                }

                                            }
                                            dr.Close();
                                        }

                                    }
                                #endregion 判断是否需要自动拆分父商品
                                string sRowUpdateCondi = "";
                                if (sheetRow.flow_id != "" && m_SheetType != SHEET_TYPE.SHEET_MOVE_STORE)
                                {
                                    sRowUpdateCondi = " flow_id='" + sheetRow.flow_id + "'";
                                }
                                else
                                    sRowUpdateCondi = " sheet_no='" + sheet_no + "' and item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi;
                                string sCmdApprove = "update "+m_tb_detail+" set branch_approve_time ='" + sNow + "',branch_approve_oper='" + m_oper_id + "' where " + sRowUpdateCondi + ";";
                                nApproveRows++;
                              
                                if (CMySbConnection.g_dbStyle == 0)
                                {
                                    //remote_sql += sCmdApprove;
                                    update_sheet_sql += sCmdApprove;
                                }
                                else
                                {
                                    cmd.CommandText = sCmdApprove;
                                    cmd.ExecuteNonQuery();
                                }
                            }

                            string pend_qty_fld = "";
                            SHEET_TYPE sheetType = m_SheetType;
                            if (m_SheetType == SHEET_TYPE.SHEET_STORE_IN || m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
                                sheetType = m_OrigSheetType;
                            if (sheetType == SHEET_TYPE.SHEET_SALE)
                            {
                                pend_qty_fld = "sell_pend_qty";
                            }
                            else if (sheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                            {
                                pend_qty_fld = "sell_re_pend_qty";
                            }
                            else if (sheetType == SHEET_TYPE.SHEET_BUY)
                            {
                                pend_qty_fld = "buy_pend_qty";
                            }
                            else if (sheetType == SHEET_TYPE.SHEET_BUY_RETURN)
                            {
                                pend_qty_fld = "buy_re_pend_qty";
                            }
                            else if (sheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                            {
                                if (branch_no == m_branch_no)
                                {
                                    pend_qty_fld = "move_out_pend_qty";
                                }
                                else if (branch_no == m_in_branch_no)
                                {
                                    pend_qty_fld = "move_in_pend_qty";
                                }
                            }
                            else if (sheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM)
                            {
                                pend_qty_fld = "sy_pend_qty";

                            } 
                            if (m_SheetType != SHEET_TYPE.SHEET_STORE_OUT && m_SheetType != SHEET_TYPE.SHEET_STORE_IN)
                            {
                                //if (sheetRow.bIgnoreStoreApprove)
                                // {
                                //    sheetRow.pend_qty = sheetRow.quantity;
                                // }

                                if (!m_bApproved)
                                {
                                    if (rowPendQty != 0)
                                    {
                                        sCmdUpdateSheet = "update "+m_tb_detail+" set now_pend_qty=" + rowPendQty.ToString() + ",pend_qty=" + rowPendQty.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "' and branch_no='" + branch_no + "' " + size_color_condi + ";";
                                        if (CMySbConnection.g_dbStyle == 0)
                                        {
                                            remote_sql += sCmdUpdateSheet;
                                        }
                                        else
                                        {
                                            cmd.CommandText = sCmdUpdateSheet;
                                            cmd.ExecuteNonQuery();
                                        }
                                        double nPendQty = -rowPendQty;
                                        //nPendQty *= -inout_flag;
                                        string sPendQty = nPendQty.ToString();
                                        string sPendQtyAdd = "";
                                        if (nPendQty >= 0)
                                            sPendQtyAdd = "+" + sPendQty;
                                        else
                                            sPendQtyAdd = sPendQty;

                                        if (pend_qty_fld != "")
                                        {
                                            string sUpdateCmd = "update stock set " + pend_qty_fld + " = " + pend_qty_fld + sPendQtyAdd + " where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi;
                                            string sInsertCmd = "insert into stock (branch_no,item_no,color_id,size_id,stock_qty," + pend_qty_fld + ") values ('" + branch_no + "','" + item_no + "','" + sheetRow.color_id + "','" + sheetRow.size_id + "','0','" + sPendQty.ToString() + "')";
                                            string sStoreInOutCmd = "if exists(select item_no from stock where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi + ") " + sUpdateCmd + " else " + sInsertCmd + ";";
                                            if (CMySbConnection.g_dbStyle == 0)
                                            {
                                                remote_sql += sStoreInOutCmd;
                                            }
                                            else
                                            {
                                                cmd.CommandText = sStoreInOutCmd;
                                                cmd.ExecuteNonQuery();
                                            }
                                        }
                                    }
                                }
                                else if (!sheetRow.bIgnoreStoreApprove)
                                {
                                    double orig_pend_qty = sheetRow.quantity;
                                    //cmd.CommandText = "select pend_qty from "+m_tb_detail+" where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "' and branch_no='" + branch_no + "' " + size_color_condi + ";";
                                    //ov = cmd.ExecuteScalar();
                                    //if (ov != null && ov != DBNull.Value)
                                    //{
                                    //    orig_pend_qty = Convert.ToDouble(ov);
                                    //}

                                    double nReducePendQty = orig_pend_qty - sheetRow.now_pend_qty;

                                    // sCmdUpdateSheet = "update "+m_tb_detail+" set now_pend_qty=" + sheetRow.pend_qty.ToString() + ",pend_qty=" + sheetRow.pend_qty.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "' and branch_no='" + branch_no + "' " + size_color_condi + ";";
  
                                    if (pend_qty_fld != "")
                                    {
                                        string sPendQtyAdd = "";
                                        if (nReducePendQty >= 0)
                                        {
                                            sPendQtyAdd = "-" + nReducePendQty.ToString();
                                        }
                                        else
                                        {
                                            sPendQtyAdd = "+" + (-nReducePendQty).ToString();
                                        }

                                        sCmdUpdateSheet = "update "+m_tb_detail+" set pend_qty=pend_qty" + sPendQtyAdd + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "' and branch_no='" + branch_no + "' " + size_color_condi + ";";


                                        string sStoreInOutCmd = "update stock set " + pend_qty_fld + "=" + pend_qty_fld + sPendQtyAdd + " where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi + ";";
                                        if (CMySbConnection.g_dbStyle == 0)
                                        {
                                            remote_sql += sCmdUpdateSheet + sStoreInOutCmd;
                                        }
                                        else
                                        {
                                            cmd.CommandText = sCmdUpdateSheet;
                                            cmd.ExecuteNonQuery();
                                            cmd.CommandText = sStoreInOutCmd;
                                            cmd.ExecuteNonQuery();
                                        }
                                    }
                                }
                            }
                            else if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT || m_SheetType == SHEET_TYPE.SHEET_STORE_IN)
                            {
                                if (!sheetRow.bIgnoreStoreApprove)
                                {
                                    if (sheetRow.quantity != 0)
                                    {
                                        string sPendQtyAdd = "-" + sheetRow.quantity.ToString();
                                        if (sheetRow.quantity < 0)
                                            sPendQtyAdd = "+" + (-sheetRow.quantity).ToString();
                                        sCmdUpdateSheet += "update "+m_tb_detail+" set pend_qty=pend_qty" + sPendQtyAdd + " where sheet_no='" + m_OrigSheetNo + "' and item_no='" + sheetRow.item_no + "'  and branch_no='" + branch_no + "' " + size_color_condi + ";";
                                        if (CMySbConnection.g_dbStyle == 0)
                                        {
                                            remote_sql += sCmdUpdateSheet;
                                        }
                                        else
                                        {
                                            cmd.CommandText = sCmdUpdateSheet;
                                            cmd.ExecuteNonQuery();
                                        }
                                        if (pend_qty_fld != "")
                                        {
                                            string sStoreInOutCmd = "update stock set " + pend_qty_fld + "=" + pend_qty_fld + sPendQtyAdd + " where item_no='" + item_no + "' and branch_no='" + branch_no + "'" + size_color_condi + ";";
                                            if (CMySbConnection.g_dbStyle == 0)
                                            {
                                                remote_sql += sStoreInOutCmd;
                                            }
                                            else
                                            {
                                                cmd.CommandText = sStoreInOutCmd;
                                                cmd.ExecuteNonQuery();
                                            }
                                        }
                                    }

                                }
                            }
                        }
                        #endregion　改写库存
                    }
                }


                if (CMySbConnection.g_dbStyle == 0)
                {
                    try
                    {
                        if (remote_sql != "")
                        {
                            if (nAllSheetRows == nApproveRows)
                            {
                                remote_sql += "update "+m_tb_detail+" set branch_approve_time='" + sNow + "',branch_approve_oper='" + m_oper_id + "' where sheet_no='" + m_sheet_no + "';";
                            }
                            else
                            {
                                remote_sql += update_sheet_sql;
                            }
                            cmd.CommandText = remote_sql;
                            cmd.ExecuteNonQuery();
                        }
                    } 
                    catch (Exception se)
                    {
                        err = se.Message;

                    }
                }


                #region 自动拆分

                foreach (KeyValuePair<string, SheetRow> pr in NeedSplitItems)
                {
                    SheetRow com_row = pr.Value;

                    CSheetSale com_sheet = new CSheetSale(SHEET_TYPE.SHEET_SPLIT_GOOD);

                    com_sheet.m_brief = "自动拆分,销售单:" + m_sheet_no + ",销售商品:" + com_row.son_nos;
                    com_sheet.SetGeneralInfo(m_branch_no, "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", false);
                    string com_err = com_sheet.AddRow(com_row.item_no, "", com_row.unit_no, "1", "", "", "", "", "", "", "-" + com_row.quantity, "", 1, "", "", false);
                    // CMySbCommand cmdCombine = new CMySbCommand("", conn_info);
                    cmd.CommandText = "select * from info_combine_model where father_no='" + com_row.item_no + "'";
                    dr = cmd.ExecuteReader();
                    while (dr.Read())
                    {
                        ov = dr["son_no"];
                        string son_no = "";
                        if (ov != null && ov != DBNull.Value)
                            son_no = ov.ToString().Trim();
                        ov = dr["son_number"];
                        double son_num = 0;
                        if (ov != null && ov != DBNull.Value)
                            son_num = Convert.ToDouble(ov);

                        if (son_no != "" && son_num != 0)
                        {
                            son_num *= com_row.quantity;
                            com_sheet.AddRow(son_no, "", "0", "1", "", "", "", "", "", "", son_num.ToString(), "", 1, "", "", false);
                        }
                    }
                    dr.Close();
                    err = com_sheet.SaveSheet(conn, cmd, tran);
                    if (err == "")
                        err = com_sheet.Approve(conn, cmd, tran);

                }
                #endregion
            } 
            catch (Exception eee)
            {
                err = eee.Message;
            }
 
            if (!bConnPassed)
            {
                if (err == "")
                    tran.Commit();
                else
                    tran.Rollback();
                cmd.Dispose();
                conn.Close();
                conn.Dispose();
                tran.Dispose();
            }
            if (err == "")
                m_bApproved = true;
            return err;

        }
        public string UpdateCost_Sale( CMySbConnection conn, CMySbCommand cmd, ref string update_sql)
        {
            CMySbDataReader dr = null;
            Dictionary<string, SheetRow> rowsDict = new Dictionary<string, SheetRow>();
            string fifo_update_sql = "";
            object ov = null;
            MergeSheetRows(rowsDict);
            string scmd_update_cost = "";
            int nRowIndex = 0;
            string items_no = ""; Dictionary<string, string> dicItemsNo = new Dictionary<string, string>();
            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                if (sheetRow.quantity == 0)
                    continue;
                if (!dicItemsNo.ContainsKey(sheetRow.item_no))
                {
                    if (items_no != "") items_no += ",";
                    items_no += "'" + sheetRow.item_no + "'";
                    dicItemsNo.Add(sheetRow.item_no, sheetRow.item_no);
                }
            }
            string stock_com_condi = "";
            if (company_id != "")
                stock_com_condi = " and info_branch.com_no ='" + company_id + "'";
            if (items_no != "")
            {
                // string stock_tb = " ( select item_no,sum(stock_qty) as sum_qty from stock left join info_branch on stock.branch_no=info_branch.branch_no " + stock_com_condi + " group by item_no ";

                string stock_tb = " (select item_no,sum(stock_qty) as sum_qty from stock left join info_branch on stock.branch_no=info_branch.branch_no where item_no in (" + items_no + ") " + stock_com_condi + " group by item_no) stock_tb ";
                cmd.CommandText = "select info_item_prop.item_no,info_item_prop.price,info_item_prop.cost_change_qty,info_item_prop.cost_price_avg,cost_price_recent,cost_type,service_flag,info_item_prop.cost_amt,sum_qty from info_item_prop left join " + stock_tb + " on info_item_prop.item_no=stock_tb.item_no where info_item_prop.item_no in (" + items_no + ");";

                dr = cmd.ExecuteReader();
                while (dr.Read())
                {
                    string item_no = CPubVars.GetTextFromDr(dr, "item_no");
                    string cost_change_qty = CPubVars.GetTextFromDr(dr, "cost_change_qty");
                    string cost_price_avg = CPubVars.GetTextFromDr(dr, "cost_price_avg");
                    string cost_amt = CPubVars.GetTextFromDr(dr, "cost_amt");
                    string sum_qty = CPubVars.GetTextFromDr(dr, "sum_qty");

                    foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
                    {
                        SheetRow sheetRow = di_row.Value;
                        if (sheetRow.item_no == item_no)
                        {
                            ov = dr["cost_price_avg"]; if (ov != DBNull.Value) sheetRow.cost_price_avg = Convert.ToDouble(ov);
                            ov = dr["price"]; if (ov != DBNull.Value) sheetRow.cost_price_prop = Convert.ToDouble(ov);
                            ov = dr["cost_amt"]; if (ov != DBNull.Value) sheetRow.cost_amount = ov.ToString();
                            ov = dr["cost_change_qty"]; if (ov != DBNull.Value) sheetRow.cost_change_qty = Convert.ToDouble(ov);
                            sheetRow.s_stock_qty = CPubVars.GetTextFromDr(dr, "sum_qty");
                            ov = dr["cost_price_recent"]; if (ov != DBNull.Value) sheetRow.cost_price_recent = Convert.ToDouble(ov);
                            ov = dr["cost_type"]; if (ov != DBNull.Value) sheetRow.cost_type = Convert.ToInt32(ov);
                            ov = dr["service_flag"]; if (ov != DBNull.Value) sheetRow.is_service = (Convert.ToInt32(ov) == 1);
                            break;
                        }
                    }
                }
                dr.Close();
            }
            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                double cost_price_used = 0;
                double cost_amt = 0;
                double cost_price_fifo = 0;
                cost_amt = sheetRow.cost_amt_all;

                #region get prices

                //cmd.CommandText = "select price,cost_price_avg,cost_amt,cost_price_recent,cost_type,service_flag from info_item_prop where item_no='" + sheetRow.item_no + "'";
                //dr = cmd.ExecuteReader();
                //if (dr.Read())
                //{
                //    ov = dr["cost_price_avg"];
                //    if (ov != DBNull.Value) sheetRow.cost_price_avg = Convert.ToDouble(ov);
                //    ov = dr["price"];
                //    if (ov != DBNull.Value) sheetRow.cost_price_prop = Convert.ToDouble(ov);
                //    ov = dr["cost_price_recent"];
                //    if (ov != DBNull.Value) sheetRow.cost_price_recent = Convert.ToDouble(ov);
                //    ov = dr["cost_amt"];
                //    if (ov != DBNull.Value) sheetRow.cost_amt_all = Convert.ToDouble(ov);
                //    ov = dr["cost_type"];
                //    if (ov != DBNull.Value) sheetRow.cost_type = Convert.ToInt32(ov);
                //    ov = dr["service_flag"];
                //    if (ov != DBNull.Value) sheetRow.is_service = (Convert.ToInt32(ov) == 1);
                //}
                //dr.Close();
                if (sheetRow.is_service)
                    continue;
                #endregion get prices
                if (sheetRow.cost_type == 1)
                    GetFifoPrice(cmd, dr, sheetRow.item_no, sheetRow.quantity, sheetRow.cost_price_prop, ref cost_price_fifo, ref fifo_update_sql);

                if (sheetRow.cost_type == 0)
                    cost_price_used = sheetRow.cost_price_avg;
                else if (sheetRow.cost_type == 1)
                    cost_price_used = cost_price_fifo;
                if (cost_price_used == 0)
                {
                    if (sheetRow.cost_price_recent != 0)
                    {
                        cost_price_used = sheetRow.cost_price_prop;
                    }
                    else if (sheetRow.cost_price_prop != 0)
                    {
                        cost_price_used = sheetRow.cost_price_prop;
                    }
                }
                double nAddAmt = sheetRow.quantity * cost_price_used;
                nAddAmt = double.Parse(nAddAmt.ToString("F2"));
                string sAddAmt = "";
                if (nAddAmt < 0)
                    sAddAmt = "+" + (-nAddAmt).ToString();
                else
                    sAddAmt = "-" + nAddAmt.ToString();

                //scmd_update_cost += "update info_item_prop set cost_amt=cost_amt-" + nAddAmt.ToString() + " where item_no='" + sheetRow.item_no + "';";
                scmd_update_cost += "update info_item_prop set cost_amt=cost_amt " + sAddAmt + " where item_no='" + sheetRow.item_no + "';";

                scmd_update_cost += "update " + m_tb_detail + " set cost_price_prop=" + sheetRow.cost_price_prop.ToString() + ",cost_price=" + cost_price_used.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "';";
                nRowIndex++;
            }
            update_sql += scmd_update_cost + fifo_update_sql;
            return "";
        }
        public string UpdateCost_SaleReturn(CMySbConnection conn, CMySbCommand cmd, ref string update_sql)
        {
            object ov = null;
            CMySbDataReader dr = null;
            Dictionary<string, SheetRow> rowsDict = new Dictionary<string, SheetRow>();
            MergeSheetRows(rowsDict);
            string scmd_update_cost = "";
            double cost_price_used = 0;
            int nRowIndex = 0;
            string items_no = ""; Dictionary<string, string> dicItemsNo = new Dictionary<string, string>();
            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                if (sheetRow.quantity == 0)
                    continue;
                if (!dicItemsNo.ContainsKey(sheetRow.item_no))
                {
                    if (items_no != "") items_no += ",";
                    items_no += "'" + sheetRow.item_no + "'";
                    dicItemsNo.Add(sheetRow.item_no, sheetRow.item_no);
                }
            }

            string stock_com_condi = "";
            if (company_id != "")
                stock_com_condi = " and info_branch.com_no ='" + company_id + "'";
            if (items_no != "")
            {
                // string stock_tb = " ( select item_no,sum(stock_qty) as sum_qty from stock left join info_branch on stock.branch_no=info_branch.branch_no " + stock_com_condi + " group by item_no ";

                string stock_tb = " (select item_no,sum(stock_qty) as sum_qty from stock left join info_branch on stock.branch_no=info_branch.branch_no where item_no in (" + items_no + ") " + stock_com_condi + " group by item_no) stock_tb ";

                string red_orig_sht = " (select distinct item_no,cost_price as red_orig_cost_price from " + m_tb_detail + " where sheet_no='" + m_OrigSheetNo + "') red_orig_sht ";
                cmd.CommandText = "select info_item_prop.item_no,info_item_prop.price,info_item_prop.cost_change_qty,info_item_prop.cost_price_avg,cost_price_recent,cost_type,service_flag,info_item_prop.cost_amt,sum_qty,red_orig_cost_price from info_item_prop left join " + stock_tb + " on info_item_prop.item_no=stock_tb.item_no left join " + red_orig_sht + " on info_item_prop.item_no = red_orig_sht.item_no where info_item_prop.item_no in (" + items_no + ");";
                             
                dr = cmd.ExecuteReader();
                while (dr.Read())
                {
                    string item_no = CPubVars.GetTextFromDr(dr, "item_no");
                    string cost_change_qty = CPubVars.GetTextFromDr(dr, "cost_change_qty");
                    string cost_price_avg = CPubVars.GetTextFromDr(dr, "cost_price_avg");
                    string cost_amt = CPubVars.GetTextFromDr(dr, "cost_amt");
                    string sum_qty = CPubVars.GetTextFromDr(dr, "sum_qty");

                    foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
                    {
                        SheetRow sheetRow = di_row.Value;
                        if (sheetRow.item_no == item_no)
                        {
                            ov = dr["cost_price_avg"]; if (ov != DBNull.Value) sheetRow.cost_price_avg = Convert.ToDouble(ov);
                            ov = dr["price"]; if (ov != DBNull.Value) sheetRow.cost_price_prop = Convert.ToDouble(ov);
                            ov = dr["cost_amt"]; if (ov != DBNull.Value) sheetRow.cost_amount = ov.ToString();
                            ov = dr["cost_change_qty"]; if (ov != DBNull.Value) sheetRow.cost_change_qty = Convert.ToDouble(ov);
                            sheetRow.s_stock_qty = CPubVars.GetTextFromDr(dr, "sum_qty");
                            ov = dr["cost_price_recent"]; if (ov != DBNull.Value) sheetRow.cost_price_recent = Convert.ToDouble(ov);
                            ov = dr["cost_type"]; if (ov != DBNull.Value) sheetRow.cost_type = Convert.ToInt32(ov);
                            ov = dr["service_flag"]; if (ov != DBNull.Value) sheetRow.is_service = (Convert.ToInt32(ov) == 1);
                            ov = dr["red_orig_cost_price"]; if (ov != DBNull.Value) sheetRow.cost_price_used = Convert.ToDouble(ov);                              
                            break;
                        }
                    }
                }
                dr.Close();
            }
            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                double cost_amt = 0;

                double nQuantity = sheetRow.quantity;
                cost_amt = sheetRow.cost_amt_all;
                // string sCostAmt = "";
                #region get prices

                cmd.CommandText = "select price,cost_price_avg,cost_amt,cost_price_recent,cost_type,service_flag from info_item_prop where item_no='" + sheetRow.item_no + "'";
                dr = cmd.ExecuteReader();
                if (dr.Read())
                {
                    ov = dr["cost_price_avg"];
                    if (ov != DBNull.Value) sheetRow.cost_price_avg = Convert.ToDouble(ov);
                    ov = dr["price"];
                    if (ov != DBNull.Value) sheetRow.cost_price_prop = Convert.ToDouble(ov);
                    ov = dr["cost_price_recent"];
                    if (ov != DBNull.Value) sheetRow.cost_price_recent = Convert.ToDouble(ov);
                    ov = dr["cost_amt"];
                    if (ov != DBNull.Value) sheetRow.cost_amt_all = Convert.ToDouble(ov);
                    ov = dr["cost_type"];
                    if (ov != DBNull.Value) sheetRow.cost_type = Convert.ToInt32(ov);
                    ov = dr["service_flag"];
                    if (ov != DBNull.Value) sheetRow.is_service = (Convert.ToInt32(ov) == 1);
                }
                dr.Close();
                #endregion get prices
                if (sheetRow.is_service)
                    continue;

                if (m_OrigSheetNo != "" && m_redFlag =="2" && m_SheetType ==SHEET_TYPE.SHEET_SALE)
                {
                    cost_price_used = sheetRow.cost_price_used;
                    //cmd.CommandText = "select cost_price from " + m_tb_detail + " left join " + m_tb_master + " on " + m_tb_detail + ".sheet_no=" + m_tb_master + ".sheet_no where " + m_tb_detail + ".sheet_no='" + m_OrigSheetNo + "' and item_no='" + sheetRow.item_no + "'";
                    //ov = cmd.ExecuteScalar();
                    //if (ov != null && ov != DBNull.Value)
                    //{
                    //    cost_price_used = Convert.ToDouble(ov);
                    //}
                }

                if (cost_price_used == 0)
                {
                    if (sheetRow.cost_price_avg > 0)
                    {
                        cost_price_used = sheetRow.cost_price_avg;
                    }
                    else if (sheetRow.cost_price_recent != 0)
                    {
                        cost_price_used = sheetRow.cost_price_recent;
                    }
                    else if (sheetRow.cost_price_prop != 0)
                    {
                        cost_price_used = sheetRow.cost_price_prop;
                    }
                }
                double nAddAmt =sheetRow.quantity * cost_price_used;
                nAddAmt = double.Parse(nAddAmt.ToString("F2"));
                string sAddAmt = "";
                if (nAddAmt < 0)
                    sAddAmt = nAddAmt.ToString();
                else
                    sAddAmt = "+" + nAddAmt.ToString();
                scmd_update_cost += "update info_item_prop set cost_amt=cost_amt " + sAddAmt + " where item_no='" + sheetRow.item_no + "';";
                scmd_update_cost += "update " + m_tb_detail + " set cost_price_prop=" + sheetRow.cost_price_prop.ToString() + ",cost_price=" + cost_price_used.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "';";
             
                nRowIndex++;
            }
            update_sql = scmd_update_cost;
            return "";
        }
        public string UpdateCost_Buy(  CMySbConnection conn, CMySbCommand cmd, ref string update_sql)
        {
            object ov = null;
            CMySbDataReader dr = null;
            Dictionary<string, SheetRow> rowsDict = new Dictionary<string, SheetRow>();
            double total_cost = m_total_amount - m_disc_amt;
            if (m_invoice_type == 2)
                total_cost -= m_tax_amount;
            double total_goods = m_total_amount - m_ship_amount;
            if (m_invoice_type == 2)
                total_goods -= m_tax_amount;

            double bl = 1;
            if (total_cost != total_goods && total_goods != 0)
                bl = total_cost / total_goods;
            MergeSheetRows(rowsDict);
            string scmd_update_cost = "";
            double added_total = 0;
            int nRowIndex = 0;
            string items_no = ""; Dictionary<string,string> dicItemsNo=new Dictionary<string,string>();
            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                if (sheetRow.quantity == 0)
                    continue;
                if(!dicItemsNo.ContainsKey(sheetRow.item_no))
                {
                    if (items_no != "") items_no += ",";
                    items_no += "'" + sheetRow.item_no + "'";
                    dicItemsNo.Add(sheetRow.item_no,sheetRow.item_no);
                }
            }
            string stock_com_condi = "";
            if (company_id != "")
                stock_com_condi = " and info_branch.com_no ='" + company_id + "'";
            if (items_no != "")
            {
               // string stock_tb = " ( select item_no,sum(stock_qty) as sum_qty from stock left join info_branch on stock.branch_no=info_branch.branch_no " + stock_com_condi + " group by item_no ";

                string stock_tb = " (select item_no,sum(stock_qty) as sum_qty from stock left join info_branch on stock.branch_no=info_branch.branch_no where item_no in (" + items_no + ") " + stock_com_condi + " group by item_no) stock_tb ";
                cmd.CommandText = "select info_item_prop.item_no,info_item_prop.price,info_item_prop.cost_change_qty,info_item_prop.cost_price_avg,info_item_prop.cost_amt,sum_qty from info_item_prop left join "+ stock_tb +" on info_item_prop.item_no=stock_tb.item_no where info_item_prop.item_no in (" + items_no + ");";

                dr = cmd.ExecuteReader();
                while (dr.Read())
                {
                    string item_no = CPubVars.GetTextFromDr(dr, "item_no");
                    string cost_change_qty = CPubVars.GetTextFromDr(dr, "cost_change_qty");
                    string cost_price_avg = CPubVars.GetTextFromDr(dr, "cost_price_avg");
                    string cost_amt = CPubVars.GetTextFromDr(dr, "cost_amt");
                    string sum_qty = CPubVars.GetTextFromDr(dr, "sum_qty");

                    foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
                    {
                        SheetRow sheetRow = di_row.Value;
                        if (sheetRow.item_no == item_no)
                        {
                            ov=dr["cost_price_avg"];if (ov != DBNull.Value) sheetRow.cost_price_avg = Convert.ToDouble(ov);
                            ov = dr["price"]; if (ov != DBNull.Value) sheetRow.cost_price_prop = Convert.ToDouble(ov);
                            ov = dr["cost_amt"]; if (ov != DBNull.Value) sheetRow.cost_amount = ov.ToString();
                            ov = dr["cost_change_qty"]; if (ov != DBNull.Value)  sheetRow.cost_change_qty = Convert.ToDouble(ov);
                            sheetRow.s_stock_qty = CPubVars.GetTextFromDr(dr,"sum_qty");
                            break;
                        }
                    }
                }
                dr.Close();
            }
            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                if (sheetRow.quantity == 0)
                    continue;
                double ought_cost_avg = 0;
                double cost_price_avg = 0;
                double cost_amt = 0;
                //bool bUpdateCost_change_qty = false;
                sheetRow.real_price = sheetRow.real_price * bl;

                double cost_qty = 0;
                if (sheetRow.s_stock_qty != "")
                    cost_qty = Convert.ToDouble(sheetRow.s_stock_qty);
                
                double nAddAmt = 0;
                if (nRowIndex == rowsDict.Count - 1)
                {
                    nAddAmt = total_cost - added_total;
                }
                else
                {
                    nAddAmt = sheetRow.quantity * sheetRow.real_price;
                    nAddAmt = double.Parse(nAddAmt.ToString("F2"));
                }
                added_total += nAddAmt;
                double cost_change_qty = 0;
                if (cost_qty < 0)
                {
                  
                    cost_price_avg = sheetRow.cost_price_avg;
                    cost_change_qty = sheetRow.cost_change_qty;
                   // cost_amt = sheetRow.cost_amount;

                    double newCostQty = cost_change_qty + sheetRow.quantity * sheetRow.unit_factor;
                    if (newCostQty > 0)
                    {
                        ought_cost_avg = (sheetRow.cost_change_qty * cost_price_avg + nAddAmt) / newCostQty;
                        cost_price_avg = ought_cost_avg;
                        cost_amt = ought_cost_avg * (cost_qty + sheetRow.quantity);
                        cost_amt = double.Parse(cost_amt.ToString("F2"));
                        //bUpdateCost_change_qty = true;
                    }
                    else
                    {
                        cost_amt += nAddAmt;
                    }
                    //cost_qty += sheetRow.quantity;
                    cost_change_qty += sheetRow.quantity;
                }
                else
                {                 
                    cost_amt = sheetRow.cost_price_avg * cost_qty; 
                    cost_amt += nAddAmt;
                    cost_qty += sheetRow.quantity;
                    if (cost_qty != 0)
                    {
                        cost_price_avg = cost_amt / cost_qty;
                    }
                    cost_change_qty = cost_qty;
                }
                scmd_update_cost += "update info_item_prop set cost_price_avg=" + cost_price_avg.ToString() + ",cost_amt=" + cost_amt.ToString() + ",cost_price_recent=" + cost_price_avg.ToString() + " where item_no='" + sheetRow.item_no + "';";
                if (cost_change_qty >= 0)
                {
                    scmd_update_cost += "update info_item_prop set cost_change_qty=" + cost_change_qty.ToString() + " where item_no='" + sheetRow.item_no + "';";
                }
                scmd_update_cost += "update " + m_tb_detail + " set left_quantity=quantity, cost_price_prop=" + sheetRow.cost_price_prop.ToString() + ",cost_price=" + cost_price_avg.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "';";
                nRowIndex++;
            }
            update_sql = scmd_update_cost;
            return "";
        }
        public string UpdateCost_BuyReturn( CMySbConnection conn, CMySbCommand cmd, ref string update_sql)
        {

            Dictionary<string, SheetRow> rowsDict = new Dictionary<string, SheetRow>();
            object ov = null;
            double total_cost = m_total_amount - m_disc_amt;
            if (m_invoice_type == 2)
                total_cost -= m_tax_amount;
            double total_goods = m_total_amount - m_ship_amount;
            if (m_invoice_type == 2)
                total_goods -= m_tax_amount;
            double bl = total_cost / total_goods;
            MergeSheetRows(rowsDict);
            string scmd_update_cost = "";
            double added_total = 0;
            int nRowIndex = 0;
            string items_no = ""; Dictionary<string, string> dicItemsNo = new Dictionary<string, string>();
            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                if (sheetRow.quantity == 0)
                    continue;
                if (!dicItemsNo.ContainsKey(sheetRow.item_no))
                {
                    if (items_no != "") items_no += ",";
                    items_no += "'" + sheetRow.item_no + "'";
                    dicItemsNo.Add(sheetRow.item_no, sheetRow.item_no);
                }
            }
            string stock_com_condi = "";
            if (company_id != "")
                stock_com_condi = " and info_branch.com_no ='" + company_id + "'";
            if (items_no != "")
            {
                // string stock_tb = " ( select item_no,sum(stock_qty) as sum_qty from stock left join info_branch on stock.branch_no=info_branch.branch_no " + stock_com_condi + " group by item_no ";

                string stock_tb = " (select item_no,sum(stock_qty) as sum_qty from stock left join info_branch on stock.branch_no=info_branch.branch_no where item_no in (" + items_no + ") " + stock_com_condi + " group by item_no) stock_tb ";
                cmd.CommandText = "select info_item_prop.item_no,info_item_prop.price,info_item_prop.cost_change_qty,info_item_prop.cost_price_avg,info_item_prop.cost_amt,sum_qty from info_item_prop left join " + stock_tb + " on info_item_prop.item_no=stock_tb.item_no where info_item_prop.item_no in (" + items_no + ");";

                CMySbDataReader dr = cmd.ExecuteReader();
                while (dr.Read())
                {
                    string item_no = CPubVars.GetTextFromDr(dr, "item_no");
                    string cost_change_qty = CPubVars.GetTextFromDr(dr, "cost_change_qty");
                    string cost_price_avg = CPubVars.GetTextFromDr(dr, "cost_price_avg");
                    string cost_amt = CPubVars.GetTextFromDr(dr, "cost_amt");
                    string sum_qty = CPubVars.GetTextFromDr(dr, "sum_qty");

                    foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
                    {
                        SheetRow sheetRow = di_row.Value;
                        if (sheetRow.item_no == item_no)
                        {
                            ov = dr["cost_price_avg"]; if (ov != DBNull.Value) sheetRow.cost_price_avg = Convert.ToDouble(ov);
                            ov = dr["price"]; if (ov != DBNull.Value) sheetRow.cost_price_prop = Convert.ToDouble(ov);
                            ov = dr["cost_amt"]; if (ov != DBNull.Value) sheetRow.cost_amount = ov.ToString();
                            ov = dr["cost_change_qty"]; if (ov != DBNull.Value) sheetRow.cost_change_qty = Convert.ToDouble(ov);
                            sheetRow.s_stock_qty = CPubVars.GetTextFromDr(dr, "sum_qty");
                        }
                    }
                }
                dr.Close();
            }

            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;

                double cost_amt = 0;
                double cost_price_used = 0;
                double cost_qty = 0;
                if(sheetRow.s_stock_qty !="")
                    cost_qty =Convert.ToDouble(sheetRow.s_stock_qty);

                cost_amt = sheetRow.cost_price_avg * cost_qty;

                //cmd.CommandText = "select cost_amt from info_item_prop where item_no='" + sheetRow.item_no + "';";
                //ov = cmd.ExecuteScalar();
                //if (ov != null && ov != DBNull.Value)
                //{
                //    cost_amt = Convert.ToDouble(ov);
                //}

                cost_price_used = sheetRow.real_price;
               
                //cmd.CommandText = "select sum(stock_qty) from stock where item_no='" + sheetRow.item_no + "';";
                //ov = cmd.ExecuteScalar();
                //if (ov != null && ov != DBNull.Value)
                //{
                //    cost_qty = Convert.ToDouble(ov);
                //}
                double nAddAmt = 0;
                if (nRowIndex == rowsDict.Count - 1)
                {
                    nAddAmt = total_cost - added_total;
                }
                else
                {
                    nAddAmt = sheetRow.quantity * cost_price_used;
                    nAddAmt = double.Parse(nAddAmt.ToString("F2"));
                }
                added_total += nAddAmt;
                cost_amt -= nAddAmt;
                cost_qty -= sheetRow.quantity;
                double cost_price_avg = 0;
                if (cost_qty > 0)
                {
                    cost_price_avg = cost_amt / cost_qty;
                }
              
                scmd_update_cost += "update info_item_prop set cost_price_avg=" + cost_price_avg.ToString() + ",cost_amt=" + cost_amt.ToString() + ",cost_change_qty=" + cost_qty.ToString() + " where item_no='" + sheetRow.item_no + "';";
                //if (cost_qty >=0)
                //{
                scmd_update_cost += "update info_item_prop set cost_change_qty=0 where item_no='" + sheetRow.item_no + "' and cost_change_qty<" + sheetRow.quantity.ToString() + ";";

                string sQtyChg = "-" + sheetRow.quantity.ToString();
                if(sheetRow.quantity<0)
                    sQtyChg = "+" + (-sheetRow.quantity).ToString();
                scmd_update_cost += "update info_item_prop set cost_change_qty=cost_change_qty " + sQtyChg + " where item_no='" + sheetRow.item_no + "' and cost_change_qty>=" + sheetRow.quantity.ToString() + ";";
                //}
                scmd_update_cost += "update " + m_tb_detail + " set cost_price_prop=" + sheetRow.cost_price_prop.ToString() + ",cost_price=" + cost_price_avg.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "';";
                nRowIndex++;
            }
            update_sql = scmd_update_cost;
            return "";
        }
        public string UpdateCost_Combine(  CMySbConnection conn, CMySbCommand cmd, ref string update_sql)
        {
            object ov = null;
            CMySbDataReader dr = null;
            Dictionary<string, SheetRow> rowsDict = new Dictionary<string, SheetRow>();
            MergeSheetRows(rowsDict);
            string scmd_update_cost = "";

            int nRowIndex = 0;

            double total_cost = 0;

            total_cost = 0;
            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                if (sheetRow.inout_flag == -1)
                {
                    double cost_price_used = 0;
                    double cost_amt = 0;
                    double cost_price_fifo = 0;
                    cost_amt = sheetRow.cost_amt_all;
                    #region get prices
                    cmd.CommandText = "select price,cost_price_avg,cost_amt,cost_price_recent,cost_type from info_item_prop where item_no='" + sheetRow.item_no + "'";
                    dr = cmd.ExecuteReader();
                    if (dr.Read())
                    {
                        ov = dr["cost_price_avg"];
                        if (ov != DBNull.Value) sheetRow.cost_price_avg = Convert.ToDouble(ov);
                        ov = dr["price"];
                        if (ov != DBNull.Value) sheetRow.cost_price_prop = Convert.ToDouble(ov);
                        ov = dr["cost_price_recent"];
                        if (ov != DBNull.Value) sheetRow.cost_price_recent = Convert.ToDouble(ov);
                        ov = dr["cost_amt"];
                        if (ov != DBNull.Value) sheetRow.cost_amt_all = Convert.ToDouble(ov);
                        ov = dr["cost_type"];
                        if (ov != DBNull.Value) sheetRow.cost_type = Convert.ToInt32(ov);
                    }
                    dr.Close();
                    #endregion get prices

                    if (sheetRow.cost_type == 1)
                    {
                        string fifo_update_sql = "";
                        GetFifoPrice(cmd, dr, sheetRow.item_no, sheetRow.quantity, sheetRow.cost_price_prop, ref cost_price_fifo, ref fifo_update_sql);
                        scmd_update_cost += fifo_update_sql;
                    }

                    if (sheetRow.cost_type == 0)
                        cost_price_used = sheetRow.cost_price_avg;
                    else if (sheetRow.cost_type == 1)
                        cost_price_used = cost_price_fifo;
                    if (cost_price_used == 0)
                    {
                        if (sheetRow.cost_price_recent != 0)
                        {
                            cost_price_used = sheetRow.cost_price_prop;
                        }
                        else if (sheetRow.cost_price_prop != 0)
                        {
                            cost_price_used = sheetRow.cost_price_prop;
                        }
                    }
                    double nAddAmt = sheetRow.quantity * cost_price_used;
                    nAddAmt = double.Parse(nAddAmt.ToString("F2"));
                    string sAddAmt = "";
                    if (nAddAmt < 0)
                        sAddAmt = "+" + (-nAddAmt).ToString();
                    else
                        sAddAmt = "-" + nAddAmt.ToString();
                    scmd_update_cost += "update info_item_prop set cost_amt=cost_amt" + sAddAmt + " where item_no='" + sheetRow.item_no + "';";
                    scmd_update_cost += "update " + m_tb_detail + " set left_quantity=left_quantity-" + sheetRow.quantity.ToString() + ", cost_price_prop=" + sheetRow.cost_price_prop.ToString() + ",cost_price=" + cost_price_used.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "';";
                    total_cost += nAddAmt;
                }
                nRowIndex++;
            }
            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                if (sheetRow.inout_flag == 1)
                {
                    double cost_qty = 0; double cost_amt = 0;
                    double cost_price_avg = 0;
                    cmd.CommandText = "select sum(stock_qty) from stock where item_no='" + sheetRow.item_no + "';";
                    ov = cmd.ExecuteScalar();
                    if (ov != null && ov != DBNull.Value)
                    {
                        cost_qty = Convert.ToDouble(ov);
                    }
                    cmd.CommandText = "select cost_amt from info_item_prop where item_no='" + sheetRow.item_no + "';";
                    ov = cmd.ExecuteScalar();
                    if (ov != null && ov != DBNull.Value)
                    {
                        cost_amt = Convert.ToDouble(ov);
                    }
                    double nAddAmt = total_cost;
                    if (cost_qty < 0)
                    {
                        double cost_change_qty = 0;
                        cmd.CommandText = "select cost_change_qty,cost_price_avg from info_item_prop where item_no='" + sheetRow.item_no + "';";
                        dr = cmd.ExecuteReader();
                        if (dr.Read())
                        {
                            ov = dr["cost_change_qty"];
                            if (ov != DBNull.Value) cost_change_qty = Convert.ToDouble(ov);
                            ov = dr["cost_price_avg"];
                            if (ov != DBNull.Value) cost_price_avg = Convert.ToDouble(ov);
                        }
                        dr.Close();
                        double newQty = cost_change_qty + sheetRow.quantity;
                        double ought_cost_avg = (cost_change_qty * cost_price_avg + nAddAmt) / newQty;
                        cost_price_avg = ought_cost_avg;
                        cost_amt = ought_cost_avg * newQty;
                        cost_amt = double.Parse(cost_amt.ToString("F2"));

                    }
                    else
                    {
                        cost_amt += nAddAmt;
                        cost_qty += sheetRow.quantity;
                        if (cost_qty > 0)
                        {
                            cost_price_avg = cost_amt / cost_qty;
                        }
                    }
                    scmd_update_cost += "update info_item_prop set cost_amt=" + cost_amt.ToString() + ",cost_change_qty=" + cost_qty.ToString() + " where item_no='" + sheetRow.item_no + "';";
                    scmd_update_cost += "update " + m_tb_detail + " set left_quantity=quantity, cost_price_prop=" + sheetRow.cost_price_prop.ToString() + ",cost_price=" + cost_price_avg.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "';";

                    break;
                }
            }
            return "";
        }
        public string UpdateCost_Split(CMySbConnection conn, CMySbCommand cmd, ref string update_sql)
        {
            object ov = null;
            CMySbDataReader dr = null;
            Dictionary<string, SheetRow> rowsDict = new Dictionary<string, SheetRow>();
            MergeSheetRows(rowsDict);
            string scmd_update_cost = "";
            double added_total = 0;
            int nRowIndex = 0;
            double total_cost = 0;
            double total_son_cost = 0;
            double cost_price_used = 0;
            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                #region get prices
                cmd.CommandText = "select price,cost_price_avg,cost_amt,cost_price_recent,cost_type from info_item_prop where item_no='" + sheetRow.item_no + "'";
                dr = cmd.ExecuteReader();
                if (dr.Read())
                {
                    ov = dr["cost_price_avg"];
                    if (ov != DBNull.Value) sheetRow.cost_price_avg = Convert.ToDouble(ov);
                    ov = dr["price"];
                    if (ov != DBNull.Value) sheetRow.cost_price_prop = Convert.ToDouble(ov);
                    ov = dr["cost_price_recent"];
                    if (ov != DBNull.Value) sheetRow.cost_price_recent = Convert.ToDouble(ov);
                    ov = dr["cost_amt"];
                    if (ov != DBNull.Value) sheetRow.cost_amt_all = Convert.ToDouble(ov);
                    ov = dr["cost_type"];
                    if (ov != DBNull.Value) sheetRow.cost_type = Convert.ToInt32(ov);
                }
                dr.Close();
                #endregion get prices
                if (sheetRow.inout_flag == -1)
                {
                    sheetRow.cost_price_used = 0;
                    if (sheetRow.cost_price_avg != 0)
                    {
                        sheetRow.cost_price_used = sheetRow.cost_price_avg;
                    }
                    else if (sheetRow.cost_price_recent != 0)
                    {
                        sheetRow.cost_price_used = sheetRow.cost_price_recent;
                    }
                    else if (sheetRow.cost_price_prop != 0)
                    {
                        sheetRow.cost_price_used = sheetRow.cost_price_prop;
                    }
                    total_son_cost += cost_price_used;
                }

            }
            total_cost = 0;

            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                if (sheetRow.inout_flag == 1)
                {

                    double cost_amt = 0;
                    double cost_price_fifo = 0;
                    cost_amt = sheetRow.cost_amt_all;
                    if (sheetRow.cost_type == 1)
                    {
                        string fifo_update_sql = "";
                        GetFifoPrice(cmd, dr, sheetRow.item_no, sheetRow.quantity, sheetRow.cost_price_prop, ref cost_price_fifo, ref fifo_update_sql);
                        scmd_update_cost += fifo_update_sql;
                    }
                    cost_price_used = 0;
                    if (sheetRow.cost_type == 0)
                        cost_price_used = sheetRow.cost_price_avg;
                    else if (sheetRow.cost_type == 1)
                        cost_price_used = cost_price_fifo;
                    if (cost_price_used == 0)
                    {
                        if (sheetRow.cost_price_recent != 0)
                        {
                            cost_price_used = sheetRow.cost_price_prop;
                        }
                        else if (sheetRow.cost_price_prop != 0)
                        {
                            cost_price_used = sheetRow.cost_price_prop;
                        }
                    }
                    double nAddAmt = sheetRow.quantity * cost_price_used;
                    nAddAmt = double.Parse(nAddAmt.ToString("F2"));
                    scmd_update_cost += "update info_item_prop set cost_amt=cost_amt-" + nAddAmt.ToString() + " where item_no='" + sheetRow.item_no + "';";
                    scmd_update_cost += "update " + m_tb_detail + " set left_quantity=left_quantity-" + sheetRow.quantity.ToString() + ", cost_price_prop=" + sheetRow.cost_price_prop.ToString() + ",cost_price=" + cost_price_used.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "';";
                    total_cost += nAddAmt;
                }
                nRowIndex++;
            }
            double bl = total_cost / total_son_cost;
            foreach (KeyValuePair<string, SheetRow> di_row in rowsDict)
            {
                SheetRow sheetRow = di_row.Value;
                if (sheetRow.inout_flag == -1)
                {
                    double cost_price_avg = 0;
                    double cost_amt = 0;
                    sheetRow.real_price = sheetRow.cost_price_used * bl;

                    double cost_qty = 0;
                    cmd.CommandText = "select sum(stock_qty) from stock where item_no='" + sheetRow.item_no + "';";
                    ov = cmd.ExecuteScalar();
                    if (ov != null && ov != DBNull.Value)
                    {
                        cost_qty = Convert.ToDouble(ov);
                    }
                    cmd.CommandText = "select cost_amt from info_item_prop where item_no='" + sheetRow.item_no + "';";
                    ov = cmd.ExecuteScalar();
                    if (ov != null && ov != DBNull.Value)
                    {
                        cost_amt = Convert.ToDouble(ov);
                    }
                    double nAddAmt = 0;
                    if (nRowIndex == rowsDict.Count - 1)
                    {
                        nAddAmt = total_cost - added_total;
                    }
                    else
                    {
                        nAddAmt = sheetRow.quantity * sheetRow.real_price;
                        nAddAmt = double.Parse(nAddAmt.ToString("F2"));
                    }
                    added_total += nAddAmt;

                    if (cost_qty < 0)
                    {
                        double cost_change_qty = 0;
                        cmd.CommandText = "select cost_change_qty,cost_price_avg from info_item_prop where item_no='" + sheetRow.item_no + "';";
                        dr = cmd.ExecuteReader();
                        if (dr.Read())
                        {
                            ov = dr["cost_change_qty"];
                            if (ov != DBNull.Value) cost_change_qty = Convert.ToDouble(ov);
                            ov = dr["cost_price_avg"];
                            if (ov != DBNull.Value) cost_price_avg = Convert.ToDouble(ov);
                        }
                        dr.Close();
                        double newQty = cost_change_qty + sheetRow.quantity;
                        double ought_cost_avg = (cost_change_qty * cost_price_avg + nAddAmt) / newQty;
                        cost_price_avg = ought_cost_avg;
                        cost_amt = ought_cost_avg * newQty;
                        cost_amt = double.Parse(cost_amt.ToString("F2"));
                    }
                    else
                    {
                        cost_amt += nAddAmt;
                        cost_qty += sheetRow.quantity;
                        cost_price_avg = cost_amt / cost_qty;
                    }
                    scmd_update_cost += "update info_item_prop set cost_price_avg=" + cost_price_avg.ToString() + ",cost_amt=" + cost_amt.ToString() + ",cost_change_qty=" + cost_qty.ToString() + " where item_no='" + sheetRow.item_no + "';";
                    scmd_update_cost += "update " + m_tb_detail + " set left_quantity=quantity, cost_price_prop=" + sheetRow.cost_price_prop.ToString() + ",cost_price=" + cost_price_avg.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "';";
                }
            }
            return "";
        }

        private void MergeSheetRows(Dictionary<string, SheetRow> rowsDict)
        { 
            foreach (var sheetRow in SheetRows)
            { 
                string cur_branch = sheetRow.branch_no;
                if (m_branch_no != "" && sheetRow.branch_no == "")
                    cur_branch = m_branch_no;
                string skey = sheetRow.item_no;// +"_" + sheetRow.unit_factor.ToString();
                SheetRow curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = new SheetRow();
                    curRow.branch_no = sheetRow.branch_no;
                    curRow.item_no = sheetRow.item_no;
                    curRow.item_name = sheetRow.item_name;
                    curRow.quantity = sheetRow.quantity*sheetRow.unit_factor;
                    curRow.unit_no = sheetRow.unit_no;
                    curRow.unit_factor = sheetRow.quantity;
                    curRow.real_price = sheetRow.real_price / sheetRow.unit_factor;
                    curRow.inout_flag = sheetRow.inout_flag;
                    rowsDict.Add(skey, curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                }

            }
        }
        public string GetFifoPrice(CMySbCommand cmd, CMySbDataReader dr, string item_no, double nQuantity, double cost_price_prop, ref double cost_price_fifo, ref string update_sql)
        {
            string scmd_update_cost = "";
            #region caculate fifo cost price
            // int inout_flag = sheetRow.inout_flag;
            object ov = null;

            cmd.CommandText = "select sum(left_quantity) from " + m_tb_detail + " where item_no='" + item_no + "' and left_quantity>0";
            ov = cmd.ExecuteScalar();
            double nSheetLeftQty = 0;
            if (ov != null && ov != DBNull.Value)
            {
                nSheetLeftQty = Convert.ToDouble(ov);
            }
            cmd.CommandText = "select sum(stock_qty) from stock where item_no='" + item_no;
            double nStockQty = 0;
            if (ov != null && ov != DBNull.Value)
            {
                nStockQty = Convert.ToDouble(ov);
            }

            double nInitQty = 0;
            double nSheetQty = 0;

            if (nStockQty > nSheetLeftQty)
            {
                double nXCQty = nStockQty - nSheetLeftQty;
                if (nXCQty >= nQuantity)
                {
                    nSheetQty = 0;
                    nInitQty = nQuantity;
                }
                else
                {
                    nSheetQty = nQuantity - nXCQty;
                    nInitQty = nXCQty;
                }
            }
            else if (nQuantity > nSheetLeftQty)
            {
                nSheetQty = nSheetLeftQty;
                nInitQty = nQuantity - nSheetLeftQty;
            }
            else
            {
                nSheetQty = nQuantity;
                nInitQty = 0;
            }

            double cost_amt_fifo = nInitQty * cost_price_prop;

            cmd.CommandText = "select flow_id,valid_price,cost_price,left_quantity from " + m_tb_detail + " where item_no='" + item_no + "' and left_quantity>0 order by flow_id";
            dr = cmd.ExecuteReader();

            double nNeedReduceQty = nSheetQty;
            while (dr.Read())
            {
                double left_quantity = 0;
                ov = dr["left_quantity"];
                if (ov != DBNull.Value)
                    left_quantity = Convert.ToDouble(ov);
                double in_price = 0;
                ov = dr["cost_price"];
                if (ov != DBNull.Value)
                    in_price = Convert.ToDouble(ov);
                string flow_id = "";
                ov = dr["flow_id"];
                if (ov != DBNull.Value)
                    flow_id = Convert.ToString(ov);

                if (left_quantity > nNeedReduceQty)
                {
                    cost_amt_fifo += in_price * nNeedReduceQty;
                    scmd_update_cost += "update " + m_tb_detail + " set left_quantity=left_quantity-" + nNeedReduceQty.ToString() + " where flow_id=" + flow_id + ";";
                    break;
                }
                else
                {
                    cost_amt_fifo += in_price * left_quantity;
                    nNeedReduceQty = nNeedReduceQty - left_quantity;
                    scmd_update_cost += "update " + m_tb_detail + " set left_quantity=0 where flow_id=" + flow_id + ";";
                }
            }
            dr.Close();
            cost_price_fifo = cost_amt_fifo / nQuantity;


            //                 scmd_update_cost += "update " + m_tb_detail + " set cost_price_prop=" + cost_price_prop.ToString() + ",cost_price_avg=" + cost_price_avg.ToString() + ",cost_price_fifo=" + cost_price_fifo.ToString() + " where sheet_no='" + m_sheet_no + "' and item_no='" + sheetRow.item_no + "';";
            #endregion caculate fifo cost price
            update_sql = scmd_update_cost;
            return "";
        }
         
        public string Approve_PrePay(CMySbConnection conn, CMySbCommand cmd, CMySbTransaction tran)
        { 
            string sheet_no = m_sheet_no;
            string err = "";
            string connString = "";// myJXC.CPubVars.ConnString;
            bool bConnPassed = false;
            if (conn == null)
            {
                conn = new CMySbConnection(connString);
                conn.Open();
                cmd = new CMySbCommand("", conn);
            }
            else
            {
                bConnPassed = true;
            }
            if (tran == null)
            {
                tran = conn.BeginTransaction();
                cmd.Transaction = tran;
            }
            CMySbDataReader dr;

            //CMySbCommand cmd = new CMySbCommand("update "+m_tb_master+" set approve_flag ='1' where sheet_no='" + sheet_no + "'", conn);

            cmd.CommandText = "SELECT * from "+m_tb_master+" where sheet_no ='" + sheet_no + "'";
            dr = cmd.ExecuteReader();

            if (!dr.Read())
            {
                err = "该单据不存在";
                goto endfun;
            }
            string branch_no = dr["branch_no"].ToString();
            string approve_flag = "";
            if (dr["approve_flag"] != null)
                approve_flag = dr["approve_flag"].ToString();
            dr.Close();
            m_bApproved = (approve_flag == "1");
            if (m_bApproved)
            {
                err = "该单据已经审批过";
                goto endfun;
            }

            #region  改写卡信息
            cmd.CommandText = "Select CONVERT(varchar(100), GETDATE(), 120)";
        
            object ov = cmd.ExecuteScalar();
            string sNow = ov.ToString();
            string com_condi = "";
            if (m_com_no != "")
                com_condi = " and com_no='" + m_com_no + "'";
            com_condi = "";
            try
            {                
                   // long start_card = 0;                       
                   // string card_id="";                      
                    cmd.CommandText = "select card_id from info_prepay_card where card_type='" + m_prepay_fill_sub_id + "' and cust_no='" + m_supcust_no + "'";
                    ov =cmd.ExecuteScalar();
                    if(ov!=null && ov!=DBNull.Value)
                       m_CardIDStart =ov.ToString().Trim();
                    //if(m_CardIDStart =="")
                    double nPaidAmount = m_paid_amount;
                    if (m_redFlag == "2")
                        nPaidAmount *= -1;
                 
                    if(ov==null)
                    {                       
                        CDbDealer dbDeal=new CDbDealer();
                        dbDeal.AddField("com_no", m_com_no);
                        dbDeal.AddField("card_id", m_supcust_no + "_" + m_prepay_fill_sub_id);
                        dbDeal.AddField("card_type", m_prepay_fill_sub_id);
                        dbDeal.AddField("money_amount", nPaidAmount.ToString());
                        dbDeal.AddField("init_money_amount", nPaidAmount.ToString());
                       // dbDeal.AddField("money_amount", m_CardStoredMoney.ToString());
                        //dbDeal.AddField("init_money_amount", m_CardStoredMoney.ToString());
                        dbDeal.AddField("oper_id", m_oper_id);
                        
                        dbDeal.AddField("seller_id", m_work_man);
                        dbDeal.AddField("cust_no", m_supcust_no);
                        cmd.CommandText = dbDeal.GetInsertSQL("info_prepay_card");
                        cmd.ExecuteNonQuery();
                    }
                    else
                    {                         
                        string sAddMoney="";
                        if (nPaidAmount >= 0)
                            sAddMoney = "+" + nPaidAmount.ToString();
                        else
                            sAddMoney = nPaidAmount.ToString();
                            //sAddMoney =m_CardStoredMoney.ToString();

                        cmd.CommandText = "update info_prepay_card set money_amount=money_amount " + sAddMoney + " where  card_type='" + m_prepay_fill_sub_id + "' and cust_no='" + m_supcust_no + "'";
                        cmd.ExecuteNonQuery();
                    }
                            
                    cmd.CommandText = "update " + m_tb_master + " set approve_flag ='1' where sheet_no='" + m_sheet_no + "';";
                    cmd.ExecuteNonQuery();
                    if (m_redFlag == "2")
                    {
                        cmd.CommandText = "update " + m_tb_master + " set red_flag='1' where sheet_no='" + m_OrigSheetNo + "'";
                        cmd.ExecuteNonQuery();
                    }
                    m_bApproved = true;
            } 
            catch (Exception ee)
            {
                err = ee.Message;
            }
        endfun:
            if (!bConnPassed)
            {
                if (err == "")
                    tran.Commit();
                else
                    tran.Rollback();
                cmd.Dispose();
                conn.Close();
                conn.Dispose();
                tran.Dispose();
            }
            #endregion　改写卡信息


            return err;

        }
        private bool compare_table(string tables1, string tables2)
        {
            string[] arr1 = tables1.Split(',');
            string[] arr2 = tables2.Split(',');
            foreach (string s1 in arr1)
            {
                bool bMet = false;
                foreach (string s2 in arr2)
                {
                    if (s1 == s2)
                    {
                        bMet = true; break;
                    }
                }
                if (!bMet)
                    return false;

            }
            foreach (string s2 in arr2)
            {
                bool bMet = false;
                foreach (string s1 in arr1)
                {
                    if (s2 == s1)
                    {
                        bMet = true; break;
                    }
                }
                if (!bMet)
                    return false;
            }
            return true;
        }
       
        public string SaveSheet(CMySbConnection conn, CMySbCommand cmd, CMySbTransaction tran)
        {  
            bool bConnPassed = false;
            string err = "";
            string connString = CPubVars.ConnString;
            //string newPdaSheetNo = "";
            object ov = null;
            if (conn == null)
            {
                conn = new CMySbConnection(connString);
                conn.Open();
                cmd = new CMySbCommand("", conn);
                tran = conn.BeginTransaction();
                cmd.Transaction = tran;
            }
            else
            {
                bConnPassed = true;
            }

            try
            {
                CMySbDataReader dr; 
                DateTime dt1 = System.DateTime.Today;
                string sheet_no = m_sheet_no;
                string approve_flag = "";
                string sNow = "";
                sNow = CPubVars.GetDateText(DateTime.Now);
              
                if (m_oper_date.Year <= 2001)
                    m_oper_date = Convert.ToDateTime(sNow);
                if (m_work_date.Year <= 2001)
                    m_work_date = Convert.ToDateTime(sNow);
           
                if (m_sheet_no == "null")
                    m_sheet_no = "";
                if (m_new_sheet_no != "")
                    sheet_no = m_new_sheet_no;
                else if (m_sheet_no == "")
                {
                    string pos_no = ""; string duty_end_time = "";
                    
                    string sht_flag = CSheetSale.StrFromSheetType(m_SheetType);
                    sheet_no = CSheetSale.GetNewSheetNo(sht_flag, m_com_no, pos_no, m_ipnum, m_ClientSubmitTime, conn, cmd);
                }
                else if (m_bOffLineSubmit)
                {
                 
                    string cur_sheet_no = m_sheet_no;
                    for (int i = 1; i < 100; i++)
                    {
                        cmd.CommandText = "select sheet_no from " + m_tb_master + " where sheet_no ='" + cur_sheet_no + "'";
                        ov = cmd.ExecuteScalar();
                        if (ov == null)
                        { 
                            m_sheet_no = cur_sheet_no;
                            sheet_no = m_sheet_no;
                            break;
                        }
                        cur_sheet_no = m_sheet_no + "_" + i.ToString();
                    }
                } 
                else
                {
                    cmd.CommandText = "SELECT * from " + m_tb_master + " where sheet_no ='" + m_sheet_no + "'";
                    dr = cmd.ExecuteReader();
                    string strErr = "";
                    if (!dr.Read())
                    {
                        dr.Close();
                        strErr = "保存时发现该单据不存在";
                        return strErr;
                    }

                    if (dr["approve_flag"] != DBNull.Value)
                        approve_flag = dr["approve_flag"].ToString();
                    dr.Close();
                    m_bApproved = (approve_flag == "1");
                    if (m_SheetType != SHEET_TYPE.SHEET_SALE_DD)
                        if (m_bApproved)
                            return "该单据已经审批过，不能保存";
                }
                   
                string items_no = "";
                foreach (var sheetRow in SheetRows)
                {
                    if (!items_no.Contains("'" + sheetRow.item_no + "'"))
                    {
                        if (items_no != "") items_no += ",";
                        items_no += "'" + sheetRow.item_no + "'";
                    }
                }
                #region 获取每个商品的统计类别
                if (items_no != "" )
                {
                    cmd.CommandText = "select item_no,rpt_class from info_item_prop where not rpt_class is null and item_no in (" + items_no + ")";
                    dr = cmd.ExecuteReader();
                    while (dr.Read())
                    {
                        string item_no_cur = CPubVars.GetTextFromDr(dr, "item_no");
                        string rpt_class = CPubVars.GetTextFromDr(dr, "rpt_class");
                        foreach (var sheetRow in SheetRows)
                        {
                            if (sheetRow.item_no == item_no_cur)
                            {
                                sheetRow.rpt_class = rpt_class;
                            }
                        }
                    }
                    dr.Close();
                }
                #endregion
                  
                #region 获取总体信息
                double nAllQty = 0;
               
                {
                    foreach (var sheetRow in SheetRows)
                    { 
                        if (sheetRow.inout_flag != 0)
                        {
                            nAllQty += sheetRow.quantity;
                        }
                    }
                }

                CDbDealer dbDeal = new CDbDealer();

               
                dbDeal.AddField("sheet_no", sheet_no);
            
                
                dbDeal.AddField("branch_no", m_branch_no);
                dbDeal.AddField("supcust_no", m_supcust_no);
                
               
                dbDeal.AddField("discount", m_discount.ToString(), "number");
            
                dbDeal.AddField("total_amount", m_total_amount.ToString(), "number");
                dbDeal.AddField("total_quantity", nAllQty.ToString(), "number");
                 
                if (!m_bApproved)
                {
                    dbDeal.AddField("inout_amount", m_total_amount.ToString(), "number");
                    dbDeal.AddField("now_pay_amount", m_paid_amount.ToString(), "number");
                    dbDeal.AddField("now_disc_amount", m_disc_amt.ToString(), "number");
                    dbDeal.AddField("paid_amount", m_paid_amount.ToString(), "number");
                    dbDeal.AddField("disc_amount", m_disc_amt.ToString(), "number");

                     
                   
                   
                    {
                        dbDeal.AddField("payway1_name", m_payway1_name);
                        dbDeal.AddField("payway1_amount", m_payway1_amount.ToString());
                    }
                    
                  
                    {
                        dbDeal.AddField("payway2_name", m_payway2_name);
                        dbDeal.AddField("payway2_amount", m_payway2_amount.ToString());
                    }
                  
                    {
                        dbDeal.AddField("payway3_name", m_payway3_name);
                        dbDeal.AddField("payway3_amount", m_payway3_amount.ToString());
                    }

                    dbDeal.AddField("ship_amount", m_ship_amount.ToString(), "number"); 
                    dbDeal.AddField("cash_amount", m_pay_cash_amount.ToString(), "number");
                    dbDeal.AddField("card_amount", m_pay_card_amount.ToString(), "number");
                  
                    dbDeal.AddField("integral_amount", m_pay_integral_amount.ToString(), "number");
                    dbDeal.AddField("reduce_integral", m_pay_reduce_integral.ToString(), "number");

                    dbDeal.AddField("vip_card", m_pay_card_id);
                     
                   
                    dbDeal.AddField("money_transfer_fee", m_pay_money_transfer_fee.ToString(), "number");
                    dbDeal.AddField("invoice_type", this.m_invoice_type.ToString(), "number");
                    dbDeal.AddField("tax_amount", this.m_tax_amount.ToString(), "number");
                    dbDeal.AddField("approve_flag", "0", "number");
                    dbDeal.AddField("duty_no", m_duty_no);
                }

                dbDeal.AddField("oper_id", m_oper_id);
                dbDeal.AddField("sheet_oper_name", m_oper_name);

                dbDeal.AddField("order_man", m_work_man);
                dbDeal.AddField("order_man_name", m_work_man_name);

                dbDeal.AddField("order_man1", m_work_man1);

                dbDeal.AddField("oper_date", CPubVars.GetDateText(m_oper_date));
                dbDeal.AddField("work_date", CPubVars.GetDateText(m_work_date));

                string sheet_account_day = "";// CPubVars.GetSetValue(cmd, "CurAccountDay", "", true);
                dbDeal.AddField("sheet_account_day", sheet_account_day);

                dbDeal.AddField("red_flag", m_redFlag);
                dbDeal.AddField("order_sheet_no", m_OrigSheetNo);
                dbDeal.AddField("com_no", m_com_no, "string", false);
               
                int inout_flag = 0;
                int money_inout_flag = 0;
                string trans_no = StrFromSheetType(m_SheetType);
                dbDeal.AddField("trans_no", trans_no);
                 
                if (m_SheetType == SHEET_TYPE.SHEET_SALE)
                { 
                    dbDeal.AddField("box_bank", m_pay_bank_box_id);
                    dbDeal.AddField("box_cash", m_pay_cash_box_id);
                    dbDeal.AddField("prepay_amount", m_pay_prepay_amount.ToString(), "number");
                    dbDeal.AddField("box_prepay", this.m_pay_prepay_box_id);

                    dbDeal.AddField("money_getter", m_MoneyGetterID);
                    dbDeal.AddField("good_sender", m_GoodSenderID);
                    dbDeal.AddField("good_getter", m_GoodGetterName);
                    dbDeal.AddField("db_no", "-");
               
                    dbDeal.AddField("sale_way", "P");
                   

                    if (m_event != "" || m_eventPerson != "" || m_eventPersonAge != "")
                    {
                        dbDeal.AddField("other2", "evt:" + m_event + ";person:" + m_eventPerson + ";age:" + m_eventPersonAge);
                    }
                    inout_flag = -1;
                    money_inout_flag = 1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD)
                {
                    dbDeal.AddField("box_bank", m_pay_bank_box_id);
                    dbDeal.AddField("box_cash", m_pay_cash_box_id);
                    dbDeal.AddField("money_getter", m_MoneyGetterID);
                    dbDeal.AddField("db_no", "-");
                 
                        dbDeal.AddField("sale_way", "P");
                  
                    dbDeal.AddField("other1", m_CardIDNew);
                    dbDeal.AddField("other2", m_prepay_fill_sub_id.ToString());
                    dbDeal.AddField("other3", m_CardPrice.ToString());
                    dbDeal.AddField("other4", m_paid_amount.ToString());
                    //dbDeal.AddField("other4", m_CardStoredMoney.ToString());
                    inout_flag = -1;
                    money_inout_flag = 1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_BUY_DD)
                {
                    dbDeal.AddField("box_bank", m_pay_bank_box_id);
                    dbDeal.AddField("box_cash", m_pay_cash_box_id);
                    dbDeal.AddField("money_getter", m_MoneyGetterID);
                    dbDeal.AddField("db_no", "-");
                    dbDeal.AddField("other1", m_CardIDNew);
                    dbDeal.AddField("other2", m_prepay_fill_sub_id.ToString());
                    dbDeal.AddField("other3", m_CardPrice.ToString());
                    dbDeal.AddField("other4", m_CardStoredMoney.ToString());
                    inout_flag = 1;
                    money_inout_flag = -1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT || m_SheetType == SHEET_TYPE.SHEET_STORE_IN)
                {
                    // dbDeal.AddField("order_sheet_no", m_SaleSheetNo);
                    if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
                        inout_flag = -1;
                    else
                        inout_flag = 1;
                    if (m_OrigSheetType == SHEET_TYPE.SHEET_SALE || m_OrigSheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                    { 
                        dbDeal.AddField("sale_way", "P"); 
                    }
                    money_inout_flag = 0;
                } 
                else if (m_SheetType == SHEET_TYPE.SHEET_BUY)
                {
                    dbDeal.AddField("box_bank", m_pay_bank_box_id);
                    dbDeal.AddField("box_cash", m_pay_cash_box_id);
                    dbDeal.AddField("prepay_amount", this.m_pay_prepay_amount.ToString(), "number");
                    dbDeal.AddField("box_prepay", this.m_pay_prepay_box_id);
                    dbDeal.AddField("money_getter", m_MoneyGetterID);
                    dbDeal.AddField("good_sender", m_GoodSenderID);

                   
                    inout_flag = 1;
                    money_inout_flag = -1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN)
                {
                    dbDeal.AddField("box_bank", m_pay_bank_box_id);
                    dbDeal.AddField("box_cash", m_pay_cash_box_id);
                    dbDeal.AddField("prepay_amount", this.m_pay_prepay_amount.ToString(), "number");
                    dbDeal.AddField("box_prepay", this.m_pay_prepay_box_id);
                    dbDeal.AddField("money_getter", m_MoneyGetterID);
                    dbDeal.AddField("good_sender", m_GoodSenderID);

                    dbDeal.AddField("db_no", "-");
                    inout_flag = -1;
                    money_inout_flag = 1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                {
                    dbDeal.AddField("box_bank", m_pay_bank_box_id);
                    dbDeal.AddField("box_cash", m_pay_cash_box_id);
                    dbDeal.AddField("prepay_amount", this.m_pay_prepay_amount.ToString(), "number");
                    dbDeal.AddField("box_prepay", this.m_pay_prepay_box_id);
                    dbDeal.AddField("money_getter", m_MoneyGetterID);
                    dbDeal.AddField("good_sender", m_GoodSenderID);
                    dbDeal.AddField("db_no", "+");
                   
                        dbDeal.AddField("sale_way", "P");
                     
                    inout_flag = 1;
                    money_inout_flag = -1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD)
                {
                    //dbDeal.AddField("db_no", "");                    
                    inout_flag = -1;
                    money_inout_flag = 0;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD)
                {
                    //dbDeal.AddField("db_no", "");                    
                    inout_flag = 1;
                    money_inout_flag = 0;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                {
                    //dbDeal.AddField("db_no", "");                 
                    dbDeal.AddField("d_branch_no", m_in_branch_no);
                    dbDeal.AddField("d_branch_man", m_in_branch_man_id);
                    inout_flag = -1;
                    money_inout_flag = 0;
                } 
                else if (m_SheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM)
                {
                    inout_flag = 1;
                    money_inout_flag = 0;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_INVENT_REDUCE)
                {
                    inout_flag = -1;
                    money_inout_flag = 0;
                } 
                else if (m_SheetType == SHEET_TYPE.SHEET_PRE_PAY_MONEY)
                {
                    dbDeal.AddField("box_bank", m_pay_bank_box_id);
                    dbDeal.AddField("box_cash", m_pay_cash_box_id);
                    dbDeal.AddField("other1", m_CardIDNew);
                    dbDeal.AddField("other2", m_prepay_fill_sub_id.ToString());
                    dbDeal.AddField("other3", m_CardPrice.ToString());
                    dbDeal.AddField("other4", m_CardStoredMoney.ToString());
                    inout_flag = 1;
                    money_inout_flag = -1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_FEE_OUT)
                {
                    // inout_flag = -1;
                    dbDeal.AddField("other1", m_FeeItemNo);
                    money_inout_flag = -1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_FEE_IN)
                {
                    //inout_flag = -1;
                    dbDeal.AddField("other1", m_FeeItemNo);
                    money_inout_flag = 1;
                }
              
                if (m_redFlag == "2")
                {
                    money_inout_flag *= -1;
                }
                dbDeal.AddField("money_inout_flag", money_inout_flag.ToString(), "number");

                dbDeal.AddField("pay_way", m_pay_way);
                //dbDeal.AddField("duty_no", CPubVars.GetDutyNo());
               
                dbDeal.AddField("sheet_brief", m_brief);
                string sCmdSheet = "";
                if (m_sheet_no == "" || m_bPDASheet || m_bSaveToPosDb || m_bOffLineSubmit)
                {
                    sCmdSheet = dbDeal.GetInsertSQL(m_tb_master);
                }
                else
                {
                    sCmdSheet = dbDeal.GetUpdateSQL(m_tb_master, "sheet_no='" + sheet_no + "'");
                }

                
                approve_flag = "1";
                
                
                    cmd.CommandText = sCmdSheet;
                    cmd.ExecuteNonQuery();
             
                #endregion

                #region 获取详单信息
                // Dictionary<string, CDbDealer> rows = new Dictionary<string, CDbDealer>();
                string detail_table = m_tb_detail;

                if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD || m_SheetType == SHEET_TYPE.SHEET_BUY_DD || m_SheetType == SHEET_TYPE.SHEET_SALE_DD_RETURN || m_SheetType == SHEET_TYPE.SHEET_BUY_DD_RETURN)
                {
                    detail_table = "" + m_tb_order_detail + "";
                }
                bool bUpdateDeleteDetail = false;
                if (m_bAccountTable && m_from_red_sheet_no == "")
                {
                    bUpdateDeleteDetail = true;
                }
          
                if (!bUpdateDeleteDetail)
                {
                    cmd.CommandText = "delete from " + detail_table + " where sheet_no='" + sheet_no + "'";
                    cmd.ExecuteNonQuery();
                }
                int rowIndex = 0;
                foreach (var sheetRow in SheetRows)
                { 
                    sheetRow.sht_row_index = rowIndex;
                    int inout_flag_item = 0;
                    if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD || m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD)
                    {
                        //if (sheetRow.quantity < 0)
                        //{
                        //    inout_flag_item = -inout_flag;
                        //}
                        //else
                        //{
                        inout_flag_item = inout_flag;

                        //}
                    } 
                    else if (m_SheetType == SHEET_TYPE.SHEET_SALE && sheetRow.inout_flag == 0)
                    {
                        inout_flag_item = 0;
                    }
                    else
                        inout_flag_item = inout_flag;
                    if (m_redFlag == "2")
                        inout_flag_item *= -1;

                    if (sheetRow.flow_id != "" && sheetRow.inout_flag == 0)
                        inout_flag_item = 0;
                    sheetRow.inout_flag = inout_flag_item;

                    // inout_flag_item = inout_flag;
                    dbDeal = new CDbDealer();
                    dbDeal.AddField("item_no", sheetRow.item_no);
                    dbDeal.AddField("sheet_item_name", sheetRow.item_name);
                    dbDeal.AddField("barcode", sheetRow.item_subno);
                    dbDeal.AddField("sheet_no", sheet_no);
                    //bool bIgnoreBranch = false;
                    //if(CPubVars.g_BusinessType =="餐饮") 
                    //{
                    //    if (sheetRow.flow_id != "")
                    //    {
                    //        bIgnoreBranch = true;
                    //    }
                    //}
                    string s_branch_no = m_branch_no;
                    if (sheetRow.branch_no != "")
                        s_branch_no = sheetRow.branch_no;
                    // if(!bIgnoreBranch)
                    dbDeal.AddField("branch_no", s_branch_no);
                    dbDeal.AddField("unit_no", sheetRow.unit_no.ToString());
                    if (sheetRow.size_id == "")
                        sheetRow.size_id = "0";
                    dbDeal.AddField("size_id", sheetRow.size_id);
                    if (sheetRow.color_id == "")
                        sheetRow.color_id = "0";
                    dbDeal.AddField("color_id", sheetRow.color_id);
                    if (sheetRow.worker_id != "")
                    {
                        dbDeal.AddField("worker_id", sheetRow.worker_id);
                        dbDeal.AddField("worker_name", sheetRow.worker_name);
                    }
                    if (sheetRow.servicer_id != "")
                    {
                        dbDeal.AddField("servicer_id", sheetRow.servicer_id);
                        dbDeal.AddField("servicer_name", sheetRow.servicer_name);
                    }
                    if (sheetRow.servicer1_id != "")
                    {
                        dbDeal.AddField("servicer1_id", sheetRow.servicer1_id);

                    }
                    if (!(bUpdateDeleteDetail && sheetRow.flow_id != ""))
                        dbDeal.AddField("inout_flag", inout_flag_item.ToString(), "number");
                    dbDeal.AddField("unit_factor", sheetRow.unit_factor.ToString(), "number");
                    dbDeal.AddField("orig_price", sheetRow.orig_price.ToString(), "number");
                    dbDeal.AddField("valid_price", sheetRow.real_price.ToString(), "number");

                    dbDeal.AddField("quantity", sheetRow.quantity.ToString(), "number");
                    dbDeal.AddField("now_pend_qty", sheetRow.now_pend_qty.ToString());
                    if (sheetRow.item_sn != "")
                        dbDeal.AddField("sheet_item_sn", sheetRow.item_sn);

                    //if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN || m_SheetType == SHEET_TYPE.SHEET_BUY || m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN)
                    // {
                    if (m_SheetType == SHEET_TYPE.SHEET_STORE_IN || m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
                    {
                        dbDeal.AddField("pend_qty", sheetRow.pend_qty.ToString());
                        dbDeal.AddField("orig_sht_qty", sheetRow.orig_sht_qty.ToString());
                    }
                    else
                    {
                        dbDeal.AddField("pend_qty", sheetRow.now_pend_qty.ToString());
                    }
                    if (sheetRow.tablesID != "")
                    {
                        dbDeal.AddField("row_tables_id", sheetRow.tablesID);
                        dbDeal.AddField("row_tables_name", sheetRow.tablesName);
                    }
                    // }
                    //if (m_SheetType == SHEET_TYPE.SHEET_STORE_IN || m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
                    //{
                    //    dbDeal.AddField("order_done_qty", sheetRow.sent_qty.ToString());

                    // }

                    dbDeal.AddField("sub_amount", sheetRow.money.ToString());
                     
                    
                    string s_tax_amount = CPubVars.FormatMoney(sheetRow.tax_amount, 4);
                    dbDeal.AddField("tax_amount", s_tax_amount, "number");
                    if (sheetRow.brief.Length > 10)
                    {
                        sheetRow.brief = sheetRow.brief.Substring(0, 10);
                        //  return "明细备注不能超过10个字";
                    }
                    dbDeal.AddField("remark", sheetRow.brief);
                    dbDeal.AddField("make_way", sheetRow.make_way);
                    dbDeal.AddField("combine_flag", sheetRow.combine_flag);
                   

                    dbDeal.AddField("from_red", sheetRow.from_red_flag);

                    if (sheetRow.orig_flow_id != "")
                    {
                        dbDeal.AddField("orig_flow_id", sheetRow.orig_flow_id);
                    }
                    if (sheetRow.row_flag != "")
                    {
                        dbDeal.AddField("sheet_row_flag", sheetRow.row_flag);
                    }
                    if (detail_table == m_tb_detail)
                    {
                        if (sheetRow.father_no != "")
                            dbDeal.AddField("father_no", sheetRow.father_no);
                        if (sheetRow.combine_price != "")
                            dbDeal.AddField("combine_price", sheetRow.combine_price);
                        dbDeal.AddField("sht_row_index", sheetRow.sht_row_index.ToString());

                        if (sheetRow.box_no_start != "")
                        {
                            dbDeal.AddField("box_no_start", sheetRow.box_no_start);
                        }
                        if (sheetRow.box_no_end != "")
                        {
                            dbDeal.AddField("box_no_end", sheetRow.box_no_end);
                        }
                    }
                     

                    if (sheetRow.sign_oper != "")
                    {
                        dbDeal.AddField("other1", "sign_oper:" + sheetRow.sign_oper);
                    }

                   
                    if (sheetRow.rpt_class != "")
                    {
                        dbDeal.AddField("sheet_rpt_class", sheetRow.rpt_class);
                    }
                    if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD || m_SheetType == SHEET_TYPE.SHEET_SALE_DD_RETURN)
                    {
                        dbDeal.AddField("add_price", sheetRow.s_add_price);
                    }
                    if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT || m_SheetType ==SHEET_TYPE.SHEET_STORE_IN)
                    {
                        if (sheetRow.orig_sheet_no != "")
                            dbDeal.AddField("other4", "OSht:"+sheetRow.orig_sheet_no);
                    }
                    //dbDeal.AddField("box_price", "0", "number");
                    //dbDeal.AddField("tax", "0", "number");
                    //dbDeal.AddField("is_tax", "0", "number");


                    if (bUpdateDeleteDetail)
                    {
                        if (sheetRow.flow_id != "")
                        {
                            string sCmd = dbDeal.GetUpdateSQL(detail_table, "flow_id='" + sheetRow.flow_id + "'");
                            cmd.CommandText = sCmd;
                            cmd.ExecuteNonQuery();
                        }
                        else
                        {
                            cmd.CommandText = dbDeal.GetInsertSQL(detail_table);
                            cmd.ExecuteNonQuery();
                        }
                    }
                    else
                    {
                        string sCmd = dbDeal.GetInsertSQL(detail_table);
                        cmd.CommandText = sCmd;
                        cmd.ExecuteNonQuery();
                        if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                        {
                            dbDeal.SetField("inout_flag", (-inout_flag_item).ToString(), "number");
                            dbDeal.AddField("left_quantity", sheetRow.quantity.ToString());
                            dbDeal.SetField("branch_no", m_in_branch_no, "");
                            sCmd = dbDeal.GetInsertSQL(detail_table);
                            cmd.CommandText = sCmd;
                            cmd.ExecuteNonQuery();
                        }
                    }
                    rowIndex++;
                }
                m_sheet_no = sheet_no;
                #endregion
    
            } 
            catch (Exception ee)
            {
                err = ee.Message;
            }
     
            if (!bConnPassed)
            {
                if (err == "")
                    tran.Commit();
                else
                    tran.Rollback();

                cmd.Dispose();
                conn.Close();
            }
            //if (newPdaSheetNo != "")
            //{
            //    m_newPdaSheetNo = newPdaSheetNo;
            //}

            return err;
        }
       
        public string m_vip_sheet_no = "";
      
        public string SaveMoveOrderSheet(CMySbConnection conn, CMySbCommand cmd, CMySbTransaction tran)
        {  
            bool bConnPassed = false;
            string err = "";
            string connString = CPubVars.ConnString;
            //string newPdaSheetNo = "";
            object ov = null;
            if (conn == null)
            {
                conn = new CMySbConnection(connString);
                conn.Open();
                cmd = new CMySbCommand("", conn);
                tran = conn.BeginTransaction();
                cmd.Transaction = tran;
            }
            else
            {
                bConnPassed = true;
            }

            try
            {
                CMySbDataReader dr;
                // CMySbCommand cmd;
                DateTime dt1 = System.DateTime.Today;
                string sheet_no = m_sheet_no;
                string approve_flag = "";
                string sNow = "";
                 sNow = CPubVars.GetDateText(DateTime.Now);
               
                if (m_oper_date.Year <= 2001)
                    m_oper_date = Convert.ToDateTime(sNow);
                if (m_work_date.Year <= 2001)
                    m_work_date = Convert.ToDateTime(sNow);
                

                if (m_sheet_no == "null")
                    m_sheet_no = "";
                if (m_new_sheet_no != "")
                    sheet_no = m_new_sheet_no;
                else if (m_sheet_no == "")
                { 
                    string sht_flag = CSheetSale.StrFromSheetType(m_SheetType);
                    sheet_no = CSheetSale.GetNewSheetNo(sht_flag, m_com_no, "", m_ipnum, m_ClientSubmitTime, conn, cmd);
                }  
                else
                {
                    cmd.CommandText = "SELECT * from sheet_move_order_master where sheet_no ='" + m_sheet_no + "'";
                    dr = cmd.ExecuteReader();
                    string strErr = "";
                    if (!dr.Read())
                    {
                        dr.Close();
                        strErr = "保存时发现该单据不存在";
                        return strErr;
                    }

                    if (dr["approve_flag"] != DBNull.Value)
                        approve_flag = dr["approve_flag"].ToString();
                    dr.Close();
                    m_bApproved = (approve_flag == "1");
                    if (m_bApproved)
                            return "该单据已经审批过，不能保存";
                } 
               
                #region 获取总体信息
                double nAllQty = 0;

                foreach (var sheetRow in SheetRows)
                { 
                    if (sheetRow.inout_flag != 0)
                    {
                        nAllQty += sheetRow.quantity;
                    }
                }
                
                CDbDealer dbDeal = new CDbDealer();

                //NameValueCollection flds = new NameValueCollection();
                //flds.Add("sheet_no", sID);
                cmd.CommandText = "select owner_store from info_operator where oper_id='" + m_oper_id + "'";
                ov = cmd.ExecuteScalar();
                if (ov != null && ov != DBNull.Value)
                {
                    m_op_store = ov.ToString().Trim();
                }

                dbDeal.AddField("sheet_no", sheet_no);
               
                dbDeal.AddField("op_store", m_op_store);
             
                dbDeal.AddField("branch_no", m_branch_no);
                dbDeal.AddField("supcust_no", m_supcust_no);
                dbDeal.AddField("cust_addr", m_cust_addr);
                dbDeal.AddField("cust_tel", m_cust_tel);
                dbDeal.AddField("cust_contact", m_cust_contact);
                dbDeal.AddField("send_time", m_send_time); 
               

                //dbDeal.AddField("pay_type", "1", "number");
                dbDeal.AddField("discount", m_discount.ToString(), "number");
                dbDeal.AddField("coin_no", m_coin_no);

                dbDeal.AddField("total_amount", m_total_amount.ToString(), "number");
                dbDeal.AddField("total_quantity", nAllQty.ToString(), "number");
 
                if (!m_bApproved)
                {                      
                    dbDeal.AddField("approve_flag", "0", "number"); 
                }
                else
                    dbDeal.AddField("approve_flag", "1", "number"); 

                dbDeal.AddField("oper_id", m_oper_id);
                dbDeal.AddField("sheet_oper_name", m_oper_name);

                
                dbDeal.AddField("oper_date", CPubVars.GetDateText(m_oper_date));
                dbDeal.AddField("work_date", CPubVars.GetDateText(m_work_date));

                string sheet_account_day = "";// CPubVars.GetSetValue(cmd, "CurAccountDay", "", true);
                dbDeal.AddField("sheet_account_day", sheet_account_day);

                //dbDeal.AddField("red_flag", m_redFlag);
                  
                dbDeal.AddField("com_no", m_com_no, "string", false);
                if (m_remote_com_no != "")
                    dbDeal.AddField("remote_com_no", m_remote_com_no);
                if (m_remote_com_name != "")
                    dbDeal.AddField("remote_com_name", m_remote_com_name);
 
                
                int money_inout_flag = 0;
                string trans_no = StrFromSheetType(m_SheetType);
                dbDeal.AddField("trans_no", trans_no);  
                dbDeal.AddField("red_flag", m_redFlag);
                if (m_redFlag == "2")
                {
                    money_inout_flag *= -1;
                }

                dbDeal.AddField("money_inout_flag", money_inout_flag.ToString(), "number");

               //dbDeal.AddField("pay_way", m_pay_way);
                //dbDeal.AddField("duty_no", CPubVars.GetDutyNo());
               
                dbDeal.AddField("sheet_brief", m_brief);
                string sCmdSheet = "";
                if (m_sheet_no == "" || m_bPDASheet || m_bSaveToPosDb || m_bOffLineSubmit)
                {
                    sCmdSheet = dbDeal.GetInsertSQL(m_tb_master);
                }
                else
                {
                    sCmdSheet = dbDeal.GetUpdateSQL("sheet_move_order_master", "sheet_no='" + sheet_no + "'");
                }

                //string paid_amount = s;
                //dbDeal.AddField ("paid_amount", s,"number");
                approve_flag = "1";
                //Debug.WriteLine(sCmdSheet);

                 

                try
                {
                    cmd.CommandText = sCmdSheet;
                    cmd.ExecuteNonQuery();
                }
                catch (Exception oe)
                { 
                  
                    err = oe.Message;
                    if (err != "")
                    {
                        err = "保存单据时出错:" + err;
                        goto fun_end;
                    }
                }
                #endregion

                #region 获取详单信息
                // Dictionary<string, CDbDealer> rows = new Dictionary<string, CDbDealer>();
              
                bool bUpdateDeleteDetail = false;
                if (m_bAccountTable && m_from_red_sheet_no == "")
                {
                    bUpdateDeleteDetail = true;
                }
            
                if (!bUpdateDeleteDetail)
                {
                    cmd.CommandText = "delete from sheet_move_order_detail where sheet_no='" + sheet_no + "'";
                    cmd.ExecuteNonQuery();
                }
                int rowIndex = 0;
                foreach (var sheetRow in SheetRows)
                { 
                    sheetRow.sht_row_index = rowIndex;
                    int inout_flag_item = 0;  
                    inout_flag_item = 1;
                    if (m_redFlag == "2")
                        inout_flag_item *= -1;

           
                    sheetRow.inout_flag = inout_flag_item;

                    // inout_flag_item = inout_flag;
                    dbDeal = new CDbDealer();
                    dbDeal.AddField("item_no", sheetRow.item_no);
                    dbDeal.AddField("sheet_item_name", sheetRow.item_name);
                    //dbDeal.AddField("barcode", sheetRow.item_subno);
                    dbDeal.AddField("sheet_no", sheet_no);
                    //bool bIgnoreBranch = false;
                    //if(CPubVars.g_BusinessType =="餐饮") 
                    //{
                    //    if (sheetRow.flow_id != "")
                    //    {
                    //        bIgnoreBranch = true;
                    //    }
                    //}
                    string s_branch_no = m_branch_no;
                    if (sheetRow.branch_no != "")
                        s_branch_no = sheetRow.branch_no;
                    // if(!bIgnoreBranch)
                    dbDeal.AddField("branch_no", s_branch_no);
                    dbDeal.AddField("unit_no", sheetRow.unit_no.ToString());
                    if (sheetRow.size_id == "")
                        sheetRow.size_id = "0";
                    dbDeal.AddField("size_id", sheetRow.size_id);
                    if (sheetRow.color_id == "")
                        sheetRow.color_id = "0";
                    dbDeal.AddField("color_id", sheetRow.color_id);
                    
                    if (!(bUpdateDeleteDetail && sheetRow.flow_id != ""))
                        dbDeal.AddField("inout_flag", inout_flag_item.ToString(), "number");
                    dbDeal.AddField("unit_factor", sheetRow.unit_factor.ToString(), "number");
                    dbDeal.AddField("orig_price", sheetRow.orig_price.ToString(), "number");
                    dbDeal.AddField("valid_price", sheetRow.real_price.ToString(), "number");

                    dbDeal.AddField("quantity", sheetRow.quantity.ToString(), "number");
                  
                    dbDeal.AddField("sub_amount", sheetRow.money.ToString());

                    dbDeal.AddField("unit_no1", sheetRow.unit_no1.ToString());
                    
                    dbDeal.AddField("quantity1", sheetRow.quantity1.ToString());

                    string s_tax_amount = CPubVars.FormatMoney(sheetRow.tax_amount, 4);
                   
                    if (sheetRow.brief.Length > 10)
                    {
                        sheetRow.brief = sheetRow.brief.Substring(0, 10);
                        //  return "明细备注不能超过10个字";
                    }
                    dbDeal.AddField("remark", sheetRow.brief);
            
                    dbDeal.AddField("sht_row_index", sheetRow.sht_row_index.ToString());
  
                  
                    if (bUpdateDeleteDetail)
                    {
                        if (sheetRow.flow_id != "")
                        {
                            string sCmd = dbDeal.GetUpdateSQL("sheet_move_order_detail", "flow_id='" + sheetRow.flow_id + "'");
                            cmd.CommandText = sCmd;
                            cmd.ExecuteNonQuery();
                        }
                        else
                        {
                            cmd.CommandText = dbDeal.GetInsertSQL("sheet_move_order_detail");
                            cmd.ExecuteNonQuery();
                        }
                    }
                    else
                    {
                        string sCmd = dbDeal.GetInsertSQL("sheet_move_order_detail");
                        cmd.CommandText = sCmd;
                        cmd.ExecuteNonQuery(); 
                    }
                    rowIndex++;
                }
                m_sheet_no = sheet_no;
                #endregion 
                
            } 
            catch (Exception ee)
            {
                err = ee.Message;
            }
        fun_end:
            if (!bConnPassed)
            {
                if (err == "")
                    tran.Commit();
                else
                    tran.Rollback();

                cmd.Dispose();
                conn.Close();
            } 
            return err;
        }
        public string LoadMoveOrderSheet(string Sheet_No)
        {
            CMySbConnection conn = new CMySbConnection(CPubVars.ConnString);
            conn.Open();
            CMySbCommand cmd = new CMySbCommand("", conn);
            string err = LoadMoveOrderSheet(Sheet_No, conn, cmd);
            conn.Close();
            return err;
        }
        public string LoadMoveOrderSheet(string Sheet_No, CMySbConnection conn, CMySbCommand cmd)
        {
            CMySbDataReader dr;

            cmd.CommandText = "select sheet_move_order_master.*,info_sub_company.com_name from sheet_move_order_master left join info_sub_company on sheet_move_order_master.com_no=info_sub_company.com_no where sheet_no = '" + Sheet_No + "'";
            dr = cmd.ExecuteReader();
            if (!dr.Read())
            {
                dr.Close();
                return "没有找到单据号" + Sheet_No + "对应的数据记录";
            }
            string rowCondi = "";

            //SetGeneralInfo("", "","","", string total_amount, string paid_amount, string disc_amt, string oper_id, string  work_man, string oper_date, string work_date);
            m_sheet_no = Sheet_No;
            string trans_no = "";
            if (dr["trans_no"] != DBNull.Value) trans_no = dr["trans_no"].ToString().Trim();
           
            m_com_no = CPubVars.GetTextFromDr(dr, "com_no");
            m_com_name = CPubVars.GetTextFromDr(dr, "com_name");
            m_SheetType = SheetTypeFromStr(trans_no);

            if (dr["total_quantity"] != DBNull.Value) m_total_qty = Convert.ToDouble(dr["total_quantity"]);
                             
            m_remote_com_no = CPubVars.GetTextFromDr(dr, "remote_com_no");
            m_remote_com_name = CPubVars.GetTextFromDr(dr, "remote_com_name");
           
            if (dr["branch_no"] != DBNull.Value) m_branch_no = dr["branch_no"].ToString().Trim();
            m_op_store = CPubVars.GetTextFromDr(dr, "op_store");
           // if (dr["supcust_no"] != DBNull.Value) m_supcust_no = dr["supcust_no"].ToString().Trim();

           // if (dr["cust_addr"] != DBNull.Value) m_cust_addr = dr["cust_addr"].ToString().Trim();
           // if (dr["cust_tel"] != DBNull.Value) m_cust_tel = dr["cust_tel"].ToString().Trim();
            //if (dr["cust_contact"] != DBNull.Value) m_cust_contact = dr["cust_contact"].ToString().Trim();
            if (dr["send_time"] != DBNull.Value) m_send_time = dr["send_time"].ToString().Trim();
           
            //m_GoodGetterName = CPubVars.GetTextFromDr(dr, "good_getter");
           // m_bSendByTel = CPubVars.GetTextFromDr(dr, "send_by_tel") == "1" ? true : false;

           // if (dr["pay_way"] != DBNull.Value) m_pay_way = dr["pay_way"].ToString().Trim();
           // if (dr["discount"] != DBNull.Value) m_discount = Convert.ToDouble(dr["discount"]);
            if (dr["total_amount"] != DBNull.Value) m_total_amount = Convert.ToDouble(dr["total_amount"]);

            if (dr["oper_id"] != DBNull.Value) m_oper_id = dr["oper_id"].ToString().Trim();
            string sale_way = CPubVars.GetTextFromDr(dr, "sale_way");

          //  m_bBatchSale = (sale_way == "P");
          //  m_duty_no = CPubVars.GetTextFromDr(dr, "duty_no");
          
            m_redFlag = CPubVars.GetTextFromDr(dr, "red_flag");



            if (dr["oper_date"] != DBNull.Value) m_oper_date = Convert.ToDateTime(dr["oper_date"]);
            if (dr["work_date"] != DBNull.Value) m_work_date = Convert.ToDateTime(dr["work_date"]);

            m_account_day = CPubVars.GetTextFromDr(dr, "sheet_account_day");

            m_approve_flag = CPubVars.GetTextFromDr(dr, "approve_flag");

            m_bApproved = (m_approve_flag == "1");

            if (dr["money_approver"] != DBNull.Value) m_money_approver_id = dr["money_approver"].ToString();

            m_approve_oper = CPubVars.GetTextFromDr(dr, "approve_oper");
            if (m_money_approver_id == "")
                m_money_approver_id = m_approve_oper;
            if (dr["money_approve_time"] != DBNull.Value) m_money_approve_time = CPubVars.GetDateText(dr["money_approve_time"]);

           // if (dr["branch_approve_oper"] != DBNull.Value) m_branch_approver_id = dr["branch_approve_oper"].ToString();
           // if (dr["branch_approve_time"] != DBNull.Value) m_branch_approve_time = CPubVars.GetDateText(dr["branch_approve_time"]);

            if (dr["sheet_brief"] != DBNull.Value) m_brief = dr["sheet_brief"].ToString();
            // if(dr["disc_amt"]!=DBNull.Value)  m_disc_amt =Convert.ToDouble(dr["disc_amt"]);    
           
            dr.Close();
            object ov = null;
            if (m_redFlag == "1")
            {
                cmd.CommandText = "select sheet_no from " + m_tb_master + " where order_sheet_no='" + Sheet_No + "'";
                ov = cmd.ExecuteScalar();
                if (ov != null && ov != DBNull.Value)
                {
                    m_red_sheet_no = ov.ToString().Trim();
                }
            }
         
            cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_oper_id + "'";
            object rr = cmd.ExecuteScalar();
            m_oper_name = "";
            if (rr != null) m_oper_name = rr.ToString();


            if (m_approve_oper != "")
            {
                if (m_approve_oper != m_oper_id)
                {
                    cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_approve_oper + "'";
                    rr = cmd.ExecuteScalar();
                    m_approve_oper_name = "";
                    if (rr != null) m_approve_oper_name = rr.ToString();
                }
                else
                {
                    m_approve_oper_name = m_oper_name;
                }
            }

           
            if (m_work_man != "")
            {
                cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_work_man + "'";
                rr = cmd.ExecuteScalar();
                m_work_man_name = "";
                if (rr != null) m_work_man_name = rr.ToString();
            }
           
      
            cmd.CommandText = "SELECT branch_name FROM info_branch where branch_no='" + m_branch_no + "'";
            rr = cmd.ExecuteScalar();
            m_branch_name = "";
            if (rr != null) m_branch_name = rr.ToString();

            m_in_branch_name = "";
            if (m_in_branch_no != "")
            {
                cmd.CommandText = "SELECT branch_name FROM info_branch where branch_no='" + m_in_branch_no + "'";
                rr = cmd.ExecuteScalar();
                if (rr != null) m_in_branch_name = rr.ToString();
            }
            m_in_branch_man_name = "";
            if (m_in_branch_man_id != "")
            {
                cmd.CommandText = "select oper_name from info_operator where oper_id='" + m_in_branch_man_id + "'";
                rr = cmd.ExecuteScalar();
                if (rr != null) m_in_branch_man_name = rr.ToString();
            }
          
           
      
            
            cmd.CommandText = "select " + m_tb_order_detail + ".*,item_name,item_model,item_subno,info_item_prop.item_size,info_item_prop.combine_sta,info_item_color.color_name,info_item_size.size_name,info_item_prop.size_group,info_item_prop.sale_price,info_item_prop.base_price,info_item_prop.price,info_item_prop.cost_price_avg,info_item_prop.rpt_class from " + m_tb_order_detail + " left join info_item_prop on " + m_tb_order_detail + ".item_no= info_item_prop.item_no left join info_item_color on " + m_tb_order_detail + ".color_id=info_item_color.color_id left join info_item_size on " + m_tb_order_detail + ".size_id=info_item_size.size_id where sheet_no='" + Sheet_No + "'" + rowCondi + "  order by " + m_tb_order_detail + ".inout_flag,info_item_prop.item_model,info_item_prop.item_name," + m_tb_order_detail + ".item_no," + m_tb_order_detail + ".color_id," + m_tb_order_detail + ".size_id";
           
            dr = cmd.ExecuteReader();

            int i = 0;
            SheetRows.Clear();
            //CMySbConnection conn1 = new CMySbConnection(CPubVars.ConnString);
            //conn1.Open();
            // CMySbCommand cmd1 = new CMySbCommand("", conn1); 

            SheetRow preRow = null;
            while (dr.Read())
            {
                i++;
                ov = dr["inout_flag"];
                int nInoutFlag = -1;
                if (ov != null && ov != DBNull.Value)
                {
                    nInoutFlag = Convert.ToInt32(ov);
                }

                string item_no = "";
                if (dr["item_no"] != DBNull.Value)
                    item_no = dr["item_no"].ToString().Trim();
                else
                    item_no = "";
                double unit_factor = 1;
                if (dr["unit_factor"] != DBNull.Value) unit_factor = Convert.ToDouble(dr["unit_factor"]);
                string size_id = CPubVars.GetTextFromDr(dr, "size_id");
                string color_id = CPubVars.GetTextFromDr(dr, "color_id");
                if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE && m_good_inout_flag == "" && nInoutFlag == 1)
                {
                    double now_pend_qty = Convert.ToDouble(dr["now_pend_qty"]);
                    double pend_qty = 0; ov = dr["pend_qty"]; if (ov != DBNull.Value) pend_qty = Convert.ToDouble(ov);

                    foreach (var row1 in SheetRows)
                    {
                     
                        if (row1.item_no == item_no && row1.unit_factor == unit_factor && row1.color_id == color_id && row1.size_id == size_id)
                        {
                            row1.now_pend_qty_d = now_pend_qty;
                            row1.pend_qty_d = pend_qty;
                            break;
                        }
                    }
                    continue;
                }

                SheetRow sheetRow = new SheetRow();
                if (dr["item_no"] != DBNull.Value) sheetRow.item_no = item_no;
                sheetRow.inout_flag = nInoutFlag;
                sheetRow.flow_id = CPubVars.GetTextFromDr(dr, "flow_id");
                sheetRow.orig_flow_id = CPubVars.GetTextFromDr(dr, "orig_flow_id");

                if (m_SheetType != SHEET_TYPE.SHEET_SALE_DD && m_SheetType != SHEET_TYPE.SHEET_BUY_DD && m_SheetType != SHEET_TYPE.SHEET_SALE_DD_RETURN && m_SheetType != SHEET_TYPE.SHEET_BUY_DD_RETURN)
                {
                    ov = dr["sht_row_index"];
                    if (ov != DBNull.Value)
                        sheetRow.sht_row_index = Convert.ToInt32(ov);
                }
               
                if (dr["sheet_item_name"] != DBNull.Value) sheetRow.item_name = dr["sheet_item_name"].ToString().Trim();
                if (sheetRow.item_name == "") sheetRow.item_name = CPubVars.GetTextFromDr(dr, "item_name");

                if (dr["item_model"] != DBNull.Value) sheetRow.item_model = dr["item_model"].ToString().Trim();

                if (dr["unit_no"] != DBNull.Value) sheetRow.unit_no = dr["unit_no"].ToString().Trim();
                if (dr["unit_factor"] != DBNull.Value) sheetRow.unit_factor = unit_factor;
                sheetRow.item_subno = CPubVars.GetTextFromDr(dr, "barcode");

                sheetRow.unit_no1 = CPubVars.GetTextFromDr(dr, "unit_no1");
                sheetRow.quantity1 = CPubVars.GetTextFromDr(dr, "quantity1");
                if (CPubVars.IsNumeric(sheetRow.quantity1))
                {
                    sheetRow.quantity1 = CPubVars.FormatMoney(sheetRow.quantity1, 2);
                }
                if (sheetRow.item_subno == "")
                {
                    if (sheetRow.unit_factor == 1)
                    {
                        if (dr["item_subno"] != DBNull.Value) sheetRow.item_subno = dr["item_subno"].ToString().Trim();
                    }
                }
                if (dr["inout_flag"] != DBNull.Value) sheetRow.inout_flag = Convert.ToInt32(dr["inout_flag"]);
                if (m_tb_detail == "sheet_item_detail")
                {
                    ov = dr["sheet_item_sn"];
                    if (ov != DBNull.Value) sheetRow.item_sn = ov.ToString().Trim();
                }

                ov = dr["branch_no"];
                if (ov != DBNull.Value)
                {
                    if (ov.ToString().Trim() != m_branch_no.Trim())
                        sheetRow.branch_no = ov.ToString().Trim();

                }
                // string size_name = "";
                // string color_name = "";


                sheetRow.size_id = size_id;
                sheetRow.color_id = color_id;
               
                {
                   // sheetRow.color_name = CPubVars.GetTextFromDr(dr, "color_name");
                  //  sheetRow.size_name = CPubVars.GetTextFromDr(dr, "size_name");
                  //  sheetRow.size_group = CPubVars.GetTextFromDr(dr, "size_group");
                }

                try
                {
                    if (m_SheetType != SHEET_TYPE.SHEET_SALE_DD)
                    {
                        sheetRow.worker_id = CPubVars.GetTextFromDr(dr, "worker_id");
                        sheetRow.worker_name = CPubVars.GetTextFromDr(dr, "worker_name");
                        sheetRow.servicer_id = CPubVars.GetTextFromDr(dr, "servicer_id");
                        sheetRow.servicer_name = CPubVars.GetTextFromDr(dr, "servicer_name");
                        sheetRow.servicer1_id = CPubVars.GetTextFromDr(dr, "servicer1_id");
                    }
                }
                catch (Exception) { }
                sheetRow.s_orig_price = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "orig_price"), 6);
                sheetRow.s_real_price = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "valid_price"), 6);

                if (sheetRow.s_orig_price != "") sheetRow.orig_price = Convert.ToDouble(sheetRow.s_orig_price);
                if (sheetRow.s_real_price != "") sheetRow.real_price = Convert.ToDouble(sheetRow.s_real_price);
                if (dr["quantity"] != DBNull.Value)
                {
                    sheetRow.quantity = Convert.ToDouble(dr["quantity"]);
                    sheetRow.s_quantity = CPubVars.FormatMoney(sheetRow.quantity, 2);
                }
               
                int inout_flag = 1;

                if (dr["inout_flag"] != DBNull.Value) inout_flag = Convert.ToInt32(dr["inout_flag"]);
                if (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD)
                    sheetRow.quantity = sheetRow.quantity * inout_flag;
                else if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD)
                    sheetRow.quantity = sheetRow.quantity * inout_flag * (-1);

                if (dr["sub_amount"] != DBNull.Value) sheetRow.money = Convert.ToDouble(dr["sub_amount"]);

                if (sheetRow.orig_price != 0)
                    sheetRow.discount = sheetRow.real_price / sheetRow.orig_price;
                else
                    sheetRow.discount = 1;

                if (dr["remark"] != DBNull.Value) sheetRow.brief = CPubVars.GetTextFromDr(dr, "remark");
               
                SheetRows.Add(sheetRow);
                preRow = sheetRow;

            }
            dr.Close();

            
            foreach (var sheetRow in SheetRows)
            { 
                if (sheetRow.item_subno == "" && sheetRow.unit_factor > 1)
                {
                    cmd.CommandText = "select subno from info_item_multi_unit where item_no='" + sheetRow.item_no + "' and unit_factor='" + sheetRow.unit_factor.ToString() + "'";
                    ov = cmd.ExecuteScalar();
                    if (ov != null && ov != DBNull.Value)
                        sheetRow.item_subno = ov.ToString().Trim();
                }

                if (sheetRow.inout_flag == 0 && sheetRow.quantity > 0)
                {
                    foreach (var orig_row in SheetRows)
                    { 
                        if (orig_row.flow_id == sheetRow.orig_flow_id)
                        {
                            orig_row.add_qty += sheetRow.quantity;
                            break;
                        }
                    }

                }
            }


            CMySbConnection conn_pc = null;
            foreach (var sheetRow in SheetRows)
            { 
                if (sheetRow.branch_no != "")
                {
                    cmd.CommandText = "select branch_name from info_branch where branch_no='" + sheetRow.branch_no + "'";
                    ov = cmd.ExecuteScalar();
                    if (ov != null && ov != DBNull.Value)
                    {
                        sheetRow.branch_name = ov.ToString();

                    }
                    else
                        sheetRow.branch_name = "已删除仓库,编号" + sheetRow.branch_no;

                }
                //if (sheetRow.size_id != "" && sheetRow.size_id != "0")
                //{
                //    cmd.CommandText = "select size_name from info_item_size where size_id='" + sheetRow.size_id + "'";
                //    ov = cmd.ExecuteScalar();
                //    if (ov != null && ov != DBNull.Value)
                //    {
                //        sheetRow.size_name = ov.ToString().Trim();
                //    }
                //    else
                //        sheetRow.size_name = "";

                //}
                //if (sheetRow.color_id != "" && sheetRow.color_id != "0")
                //{
                //    cmd.CommandText = "select color_name from info_item_color where color_id='" + sheetRow.color_id + "'";
                //    ov = cmd.ExecuteScalar();
                //    if (ov != null && ov != DBNull.Value)
                //    {
                //        sheetRow.color_name = ov.ToString().Trim();
                //    }
                //    else
                //        sheetRow.color_name = "";
                //}
          
            }




            if (conn_pc != null)
            {
                if (conn_pc.State == System.Data.ConnectionState.Open)
                    conn_pc.Close();
            }
            return "";
        }
    
        public void GetInoutFlagForRows()
        {
            int inout_flag = 0;// int money_inout_flag = 0;
            foreach (var sheetRow in SheetRows)
            { 
                if (m_SheetType == SHEET_TYPE.SHEET_SALE)
                {
                    inout_flag = -1;
                 //   money_inout_flag = 1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
                {
                    inout_flag = -1;
                  //  money_inout_flag = 0;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_STORE_IN)
                {
                    inout_flag = 1;
                //    money_inout_flag = 0;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_BUY)
                {
                    inout_flag = 1;
                  //  money_inout_flag = -1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN)
                {
                    inout_flag = -1;
                   // money_inout_flag = 1;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                {
                    inout_flag = 1;
                  //  money_inout_flag = -1;
                } 
                else if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD)
                {
                    //dbDeal.AddField("db_no", "");                    
                    inout_flag = -1;
                 //   money_inout_flag = 0;
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD)
                {
                    inout_flag = 1; 
                }
                else if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                {
                    inout_flag = -1; 
                } 

                int inout_flag_item = 0;
                if (m_SheetType == SHEET_TYPE.SHEET_COMBINE_GOOD || m_SheetType == SHEET_TYPE.SHEET_SPLIT_GOOD)
                {
                    if (sheetRow.quantity < 0)
                    {
                        inout_flag_item = -inout_flag;
                    }
                    else
                    {
                        inout_flag_item = inout_flag; 
                    }
                }
                else
                    inout_flag_item = inout_flag;
                if (m_redFlag == "2")
                    inout_flag_item *= -1;
                sheetRow.inout_flag = inout_flag_item;
            }

        }
        class sub_class
        {
            public string sub_id = "";
            public string item_clsno = "";

        }
        public string CreateSaleVoucher(string com_no,CMySbConnection conn, CMySbCommand cmd, CMySbTransaction tran, string strCondi, bool bReturn)
        {
            CMySbDataReader dr = null;
            Dictionary<string, SheetRow> rowsDict = new Dictionary<string, SheetRow>();
            string sCmd = "";

            string sVoucherNo = GetNewSheetNo("PZ", com_no, conn, cmd);

            cmd.CommandText = "select set_value from setting where set_name='Period'";
            object ov = cmd.ExecuteScalar();
            //if (ov == null || ov == DBNull.Value)
            //    return "No current period";
            //string sPeriod=ov.ToString().Trim();
            string sPeriod = "201001";
            cmd.CommandText = "select max(voucher_index) from cw_voucher_master where period='" + sPeriod + "'";
            ov = cmd.ExecuteScalar();
            int max_index = 0;
            if (ov != null && ov != DBNull.Value)
                max_index = Convert.ToInt32(ov);
            max_index++;

            double nTotalAmount = 0;
            double nTaxAmount = 0;
            double nCashAmount = 0;
            double nBankAmount = 0;
            double nCardAmount = 0;
            double nTicketAmount = 0;
            double nPaidAmount = 0;
            double nDiscAmount = 0;
            string supcust_no = "";
            string sheet_no = "";


            Dictionary<string, COughtMoney> dicOughtMoney = new Dictionary<string, COughtMoney>();
            Dictionary<string, COughtMoney> dicBankMoney = new Dictionary<string, COughtMoney>();
            Dictionary<string, COughtMoney> dicYsMoney = new Dictionary<string, COughtMoney>();
            cmd.CommandText = "select * from " + m_tb_master + " where " + strCondi;
            dr = cmd.ExecuteReader();
            while (dr.Read())
            {
                ov = dr["total_amount"];
                double nTotalCur = 0;
                if (ov != DBNull.Value) nTotalCur = Convert.ToDouble(ov);
                nTotalAmount += nTotalCur;
                double n = 0;
                ov = dr["tax_amount"];
                if (ov != DBNull.Value) n = Convert.ToDouble(ov);
                nTaxAmount += n;
                n = 0;
                ov = dr["cash_amount"];
                if (ov != DBNull.Value) n = Convert.ToDouble(ov);
                nCashAmount += n;
                n = 0;
                ov = dr["bank_amount"];
                if (ov != DBNull.Value) n = Convert.ToDouble(ov);
                nBankAmount += n;
                if (n > 0)
                {
                    string box_bank = "";
                    ov = dr["box_bank"];
                    if (ov != DBNull.Value) box_bank = ov.ToString();
                    COughtMoney oughtMoney = null;
                    dicBankMoney.TryGetValue(box_bank, out oughtMoney);
                    if (oughtMoney == null)
                    {
                        oughtMoney = new COughtMoney();
                        oughtMoney.supcust_no = box_bank;
                        dicBankMoney.Add(box_bank, oughtMoney);
                    }
                    oughtMoney.amount += n;
                }
                n = 0;
                ov = dr["card_amount"];
                if (ov != DBNull.Value) n = Convert.ToDouble(ov);
                nCardAmount += n;
                if (n > 0)
                {
                    ov = dr["supcust_no"];
                    if (ov != DBNull.Value) supcust_no = ov.ToString();
                    COughtMoney oughtMoney = null;
                    dicYsMoney.TryGetValue(supcust_no, out oughtMoney);
                    if (oughtMoney == null)
                    {
                        oughtMoney = new COughtMoney();
                        oughtMoney.supcust_no = supcust_no;
                        dicYsMoney.Add(supcust_no, oughtMoney);
                    }
                    oughtMoney.amount += n;
                }
                n = 0;
                ov = dr["ticket_amount"];
                if (ov != DBNull.Value) n = Convert.ToDouble(ov);
                nTicketAmount += n;
                double nPaidCur = 0; ov = dr["now_pay_amount"];
                if (ov != DBNull.Value) nPaidCur = Convert.ToDouble(ov);
                nPaidAmount += nPaidCur;
                double nDiscCur = 0; ov = dr["now_disc_amount"];
                if (ov != DBNull.Value) nDiscCur = Convert.ToDouble(ov);
                nDiscAmount += nDiscCur;
                double nLeftCur = nTotalCur - nDiscCur - nPaidAmount;
                if (nLeftCur > 0)
                {
                    ov = dr["supcust_no"];
                    if (ov != DBNull.Value) supcust_no = ov.ToString();
                    COughtMoney oughtMoney = null;
                    dicOughtMoney.TryGetValue(supcust_no, out oughtMoney);
                    if (oughtMoney == null)
                    {
                        oughtMoney = new COughtMoney();
                        oughtMoney.supcust_no = supcust_no;
                        dicOughtMoney.Add(supcust_no, oughtMoney);
                    }
                    oughtMoney.amount += nLeftCur;
                }

                ov = dr["sheet_no"];
                if (ov != DBNull.Value)
                {
                    string s = ov.ToString().Trim();
                    if (sheet_no.Length < 50)
                        sheet_no += s;
                    else if (sheet_no.Length < 50)
                    {
                        sheet_no += s;
                        sheet_no += "等";
                    }
                }


            }
            dr.Close();
            double nIncomeAmount = nTotalAmount - nTaxAmount;
            double nLeftAmount = nTotalAmount - nDiscAmount - nPaidAmount;

            CDbDealer dbDeal = new CDbDealer();
            dbDeal.AddField("voucher_no", sVoucherNo);
            dbDeal.AddField("voucher_word", "记");
            dbDeal.AddField("voucher_index", max_index.ToString(), "number");
            dbDeal.AddField("period", sPeriod);
            dbDeal.AddField("summary", "销售单" + sheet_no);
            dbDeal.AddField("total_amount", nTotalAmount.ToString(), "number");
            dbDeal.AddField("make_time", CPubVars.GetDateText(DateTime.Now), "date");

            string sMasterCmd = dbDeal.GetInsertSQL("cw_voucher_master") + ";";
            sCmd += sMasterCmd;
            string sDetailCmd = "";
            int row_index = 0;
            string is_debit = "";
            string debit_flag = "";
            //现金
            if (nCashAmount > 0)
            {
                row_index++;
                dbDeal = new CDbDealer();
                dbDeal.AddField("voucher_no", sVoucherNo);
                dbDeal.AddField("row_index", row_index.ToString(), "number");
                dbDeal.AddField("subject_id", "1001");
                dbDeal.AddField("amount", nIncomeAmount.ToString(), "number");
              //if (bReturn) is_debit = "0"; else is_debit = "1";
                if (bReturn) debit_flag = "-1"; else debit_flag = "1";
                dbDeal.AddField("debit_flag", debit_flag);
                //dbDeal.AddField("is_debit", is_debit, "number");
                dbDeal.AddField("brief", "");
                sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                sCmd += sDetailCmd;
            }
            //银行
            foreach (KeyValuePair<string, COughtMoney> di in dicBankMoney)
            {
                row_index++;
                COughtMoney om = di.Value;
                dbDeal = new CDbDealer();
                dbDeal.AddField("voucher_no", sVoucherNo);
                dbDeal.AddField("row_index", row_index.ToString(), "number");
                dbDeal.AddField("subject_id", om.supcust_no);
                dbDeal.AddField("amount", om.amount.ToString(), "number");
                if (bReturn) is_debit = "0"; else is_debit = "1";
                dbDeal.AddField("is_debit", is_debit, "number");

                if (bReturn) debit_flag = "-1"; else debit_flag = "1";
                dbDeal.AddField("debit_flag", debit_flag);

                dbDeal.AddField("brief", "");
                sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                sCmd += sDetailCmd;
            }
            //应收账款
            if (dicOughtMoney.Count > 0)
            {
                cmd.CommandText = "select check_client from cw_subject where sub_id='1122'";
                ov = cmd.ExecuteScalar();
                bool bCheckClient = false;
                if (ov != null && ov != DBNull.Value)
                {
                    bCheckClient = (ov.ToString().Trim() == "1");
                }
                bool b1122HaveSons = false;
                cmd.CommandText = "select sub_id from cw_subject where mum_id='1122'";
                if (ov != null)
                    b1122HaveSons = true;
                double other_client_amt = 0;
                foreach (KeyValuePair<string, COughtMoney> di in dicOughtMoney)
                {
                    row_index++;
                    COughtMoney om = di.Value;
                    dbDeal = new CDbDealer();
                    dbDeal.AddField("voucher_no", sVoucherNo);
                    dbDeal.AddField("row_index", row_index.ToString(), "number");
                    dbDeal.AddField("amount", om.amount.ToString(), "number");
                    if (bReturn) is_debit = "0"; else is_debit = "1";
                    dbDeal.AddField("is_debit", is_debit, "number");
                    if (bReturn) debit_flag = "-1"; else debit_flag = "1";
                    dbDeal.AddField("debit_flag", debit_flag);

                    dbDeal.AddField("brief", "");

                    if (bCheckClient)
                    {
                        dbDeal.AddField("sub_id", "1122");
                        dbDeal.AddField("sup_id", om.supcust_no);
                    }
                    else if (!b1122HaveSons)
                    {
                        dbDeal.AddField("sub_id", "1122");
                    }
                    else
                    {
                        cmd.CommandText = "select sub_id from cw_subject where supcust_no='" + om.supcust_no + "' and mum_id='1122'";
                        ov = cmd.ExecuteScalar();
                        if (ov != null && ov != DBNull.Value)
                        {
                            dbDeal.AddField("sub_id", ov.ToString().Trim());
                        }
                        else
                        {
                            other_client_amt += om.amount;
                            continue;
                        }

                    }

                    sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                    sCmd += sDetailCmd;
                }
                if (other_client_amt > 0)
                {
                    cmd.CommandText = "select sub_id from cw_subject where supcust_no='Other' and mum_id='1122'";
                    ov = cmd.ExecuteScalar();
                    if (ov != null && ov != DBNull.Value)
                    {
                        dbDeal.AddField("sub_id", ov.ToString().Trim());
                        sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                        sCmd += sDetailCmd;
                    }
                    else
                    {
                        return "应收帐款科目没有设置核算客户,它的二级科目中必须有一个科目对应客户为'其他客户'";
                    }
                }
            }
            //预收账款
            if (dicYsMoney.Count > 0)
            {
                cmd.CommandText = "select check_client from cw_subject where sub_id='2203'";
                ov = cmd.ExecuteScalar();
                bool bCheckClient = false;
                if (ov != null && ov != DBNull.Value)
                {
                    bCheckClient = (ov.ToString().Trim() == "1");
                }
                bool b2203HaveSons = false;
                cmd.CommandText = "select sub_id from cw_subject where mum_id='2203'";
                if (ov != null)
                    b2203HaveSons = true;
                double other_client_amt = 0;
                foreach (KeyValuePair<string, COughtMoney> di in dicYsMoney)
                {
                    row_index++;
                    COughtMoney om = di.Value;
                    dbDeal = new CDbDealer();
                    dbDeal.AddField("voucher_no", sVoucherNo);
                    dbDeal.AddField("row_index", row_index.ToString(), "number");
                    dbDeal.AddField("amount", nCardAmount.ToString(), "number");
                    if (bReturn) is_debit = "0"; else is_debit = "1";
                    dbDeal.AddField("is_debit", is_debit, "number");

                    if (bReturn) debit_flag = "-1"; else debit_flag = "1";
                    dbDeal.AddField("debit_flag", debit_flag);

                    dbDeal.AddField("brief", "");
                    if (bCheckClient)
                    {
                        dbDeal.AddField("subject_id", "2203");
                        dbDeal.AddField("sup_id", om.supcust_no);
                    }
                    else if (!b2203HaveSons)
                    {
                        dbDeal.AddField("sub_id", "2203");
                    }
                    else
                    {
                        cmd.CommandText = "select sub_id from cw_subject where supcust_no='" + om.supcust_no + "' and mum_id='2203'";
                        ov = cmd.ExecuteScalar();
                        if (ov != null && ov != DBNull.Value)
                        {
                            dbDeal.AddField("sub_id", ov.ToString().Trim());
                        }
                        else
                        {
                            other_client_amt += om.amount;
                            continue;
                        }

                    }
                    sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                    sCmd += sDetailCmd;
                }
                if (other_client_amt > 0)
                {
                    cmd.CommandText = "select sub_id from cw_subject where supcust_no='Other' and mum_id='1122'";
                    ov = cmd.ExecuteScalar();
                    if (ov != null && ov != DBNull.Value)
                    {
                        dbDeal.AddField("sub_id", ov.ToString().Trim());
                        sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                        sCmd += sDetailCmd;
                    }
                    else
                    {
                        return "预收帐款科目没有设置核算客户,它的二级科目中必须有一个科目对应客户为'其他客户'";
                    }
                }
            }
            //销售费用
            if (nTicketAmount > 0 || nDiscAmount > 0)
            {
                string sale_ticket_amt_sub = "";//usually under 6601
                string sale_disc_amt_sub = "";
                cmd.CommandText = "select set_value from setting where set_name ='sale_ticket_amt_sub'";
                ov = cmd.ExecuteScalar();
                if (ov != null && ov != DBNull.Value)
                {
                    sale_ticket_amt_sub = ov.ToString().Trim();
                }
                cmd.CommandText = "select set_value from setting where set_name ='sale_disc_amt_sub'";
                ov = cmd.ExecuteScalar();
                if (ov != null && ov != DBNull.Value)
                {
                    sale_ticket_amt_sub = ov.ToString().Trim();
                }
                bool bDiscTicketSame = false;
                if (nTicketAmount > 0 && nDiscAmount > 0 && sale_ticket_amt_sub == sale_disc_amt_sub)
                {
                    bDiscTicketSame = true;
                }

                if (nTicketAmount > 0)
                {
                    if (sale_ticket_amt_sub == "")
                    {
                        return "会员卡积分兑换消费对应的支出科目没有设置";
                    }
                    foreach (KeyValuePair<string, COughtMoney> di in dicYsMoney)
                    {
                        row_index++;
                        COughtMoney om = di.Value;
                        dbDeal = new CDbDealer();
                        dbDeal.AddField("voucher_no", sVoucherNo);
                        dbDeal.AddField("row_index", row_index.ToString(), "number");
                        dbDeal.AddField("subject_id", sale_ticket_amt_sub);
                        dbDeal.AddField("sup_id", om.supcust_no);
                        if (bDiscTicketSame)
                            dbDeal.AddField("amount", (nTicketAmount + nDiscAmount).ToString(), "number");
                        else
                            dbDeal.AddField("amount", nTicketAmount.ToString(), "number");
                        if (bReturn) is_debit = "0"; else is_debit = "1";
                        dbDeal.AddField("is_debit", is_debit, "number");

                        if (bReturn) debit_flag = "-1"; else debit_flag = "1";
                        dbDeal.AddField("debit_flag", debit_flag);

                        dbDeal.AddField("brief", "");
                        sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                        sCmd += sDetailCmd;
                    }
                }
                if (nDiscAmount > 0 && !bDiscTicketSame)
                {
                    if (sale_disc_amt_sub == "")
                    {
                        return "销售抹零对应的的支出科目没有设置";
                    }
                    foreach (KeyValuePair<string, COughtMoney> di in dicYsMoney)
                    {
                        row_index++;
                        COughtMoney om = di.Value;
                        dbDeal = new CDbDealer();
                        dbDeal.AddField("voucher_no", sVoucherNo);
                        dbDeal.AddField("row_index", row_index.ToString(), "number");
                        dbDeal.AddField("subject_id", sale_disc_amt_sub);
                        dbDeal.AddField("sup_id", om.supcust_no);
                        dbDeal.AddField("amount", nDiscAmount.ToString(), "number");
                        if (bReturn) is_debit = "0"; else is_debit = "1";
                        dbDeal.AddField("is_debit", is_debit, "number");

                        if (bReturn) debit_flag = "-1"; else debit_flag = "1";
                        dbDeal.AddField("debit_flag", debit_flag);

                        dbDeal.AddField("brief", "");
                        sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                        sCmd += sDetailCmd;
                    }
                }
            }
            //主营业务收入
            {
                row_index++;

                cmd.CommandText = "select * from cw_subject where sub_id='6001'";
                dr = cmd.ExecuteReader();
                bool bIncomeCheckClient = false; bool bIncomeCheckGood = false;// bool bIncomeCheckProj = false; bool bIncomeCheckDep = false;
                //bool bIncomeCheckPerson = false;
                bool bIncomeHaveSon = false;
                if (dr.Read())
                {
                    bIncomeCheckClient = (CPubVars.GetTextFromDr(dr, "check_client") == "1");
                    bIncomeCheckGood = (CPubVars.GetTextFromDr(dr, "check_good") == "1");
                    // bIncomeCheckProj = (CPubVars.GetTextFromDr(dr, "check_proj") == "1");
                    // bIncomeCheckDep = (CPubVars.GetTextFromDr(dr, "check_department") == "1");
                    // bIncomeCheckPerson = (CPubVars.GetTextFromDr(dr, "check_person") == "1");
                }
                dr.Close();
                cmd.CommandText = "select sub_id from cw_subject where mum_id='6001'";
                ov = cmd.ExecuteScalar();
                if (ov != null)
                {
                    bIncomeHaveSon = true;
                }
                if (bIncomeCheckGood)
                {
                    cmd.CommandText = "select account_class,sum(sub_amount) sub_amount_sum from (select " + m_tb_detail + ".item_no,sub_amount,account_class from " + m_tb_detail + " left join " + m_tb_master + " on " + m_tb_detail + ".sheet_no=" + m_tb_master + ".sheet_no left join info_item_prop on " + m_tb_detail + ".item_no=info_item_prop.item_no where " + strCondi + ") sheet_sql group by account_class";
                    dr = cmd.ExecuteReader();
                    while (dr.Read())
                    {
                        string account_class = CPubVars.GetTextFromDr(dr, "account_class");
                        ov = dr["sub_amount_sum"];
                        double sub_amount_sum = 0;
                        if (ov != DBNull.Value)
                        {
                            sub_amount_sum = Convert.ToDouble(ov);
                        }
                        dbDeal = new CDbDealer();
                        dbDeal.AddField("voucher_no", sVoucherNo);
                        dbDeal.AddField("row_index", row_index.ToString(), "number");
                        dbDeal.AddField("subject_id", "6001");//主营业务收入
                        dbDeal.AddField("class_id", account_class);//主营业务收入
                        dbDeal.AddField("amount", sub_amount_sum.ToString(), "number");
                        if (bReturn) is_debit = "1"; else is_debit = "0";

                        if (bReturn) debit_flag = "1"; else debit_flag = "-1";
                        dbDeal.AddField("debit_flag", debit_flag);

                        dbDeal.AddField("is_debit", is_debit, "number");
                        dbDeal.AddField("brief", "");
                        sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                        sCmd += sDetailCmd;

                    }
                    dr.Close();
                }
                else if (!bIncomeHaveSon)
                {
                    dbDeal = new CDbDealer();
                    dbDeal.AddField("voucher_no", sVoucherNo);
                    dbDeal.AddField("row_index", row_index.ToString(), "number");
                    dbDeal.AddField("subject_id", "6001");//主营业务收入
                    dbDeal.AddField("amount", nIncomeAmount.ToString(), "number");
                    if (bReturn) is_debit = "1"; else is_debit = "0";
                    dbDeal.AddField("is_debit", is_debit, "number");

                    if (bReturn) debit_flag = "1"; else debit_flag = "-1";
                    dbDeal.AddField("debit_flag", debit_flag);

                    dbDeal.AddField("brief", "");
                    sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                    sCmd += sDetailCmd;
                }
                else
                {
                    cmd.CommandText = "select sub_id,sub_code,item_clsno from cw_subject where sub_id like '6001%' and level>=2 and not exists(select mum_id as sub_id from cw_subject where sub_id like '6001%')";
                    dr = cmd.ExecuteReader();
                    string sOtherCondi = "";

                    Dictionary<string, sub_class> dicClass = new Dictionary<string, sub_class>();
                    while (dr.Read())
                    {
                        string sClsNo = CPubVars.GetTextFromDr(dr, "item_clsno");
                        if (sClsNo != "" && sClsNo != "Other")
                        {
                            if (sOtherCondi != "")
                                sOtherCondi += " and ";
                            sOtherCondi += " not other_class like '%," + sClsNo + ",%'";
                        }
                        if (!dicClass.ContainsKey(sClsNo))
                        {
                            sub_class newCls = new sub_class();
                            newCls.sub_id = CPubVars.GetTextFromDr(dr, "sub_id");
                            newCls.item_clsno = sClsNo;
                            dicClass.Add(sClsNo, newCls);
                        }
                    }
                    dr.Close();

                    foreach (KeyValuePair<string, sub_class> di in dicClass)
                    {
                        sub_class CurSubCls = di.Value;
                        if (CurSubCls.item_clsno == "")
                            continue;
                        if (CurSubCls.item_clsno == "Other")
                        {
                            cmd.CommandText = "select sum(sub_amount) as sub_amount_sum from (select item_no,sub_amount,account_class from " + m_tb_detail + " left join " + m_tb_master + " on " + m_tb_detail + ".sheet_no=" + m_tb_master + ".sheet_no left join info_item_prop on " + m_tb_detail + ".item_no=info_item_prop.item_no where " + sOtherCondi + " and " + strCondi + ") sheet_sql";
                            ov = cmd.ExecuteScalar();
                        }
                        else
                        {
                            cmd.CommandText = "select sum(sub_amount) as sub_amount_sum from (select item_no,sub_amount,account_class from " + m_tb_detail + " left join " + m_tb_master + " on " + m_tb_detail + ".sheet_no=" + m_tb_master + ".sheet_no left join info_item_prop on " + m_tb_detail + ".item_no=info_item_prop.item_no where other_class like '%," + CurSubCls.item_clsno + ",% and " + strCondi + ") sheet_sql";
                            ov = cmd.ExecuteScalar();
                        }
                        double sub_amount_sum = 0;
                        if (ov != null && ov != DBNull.Value)
                        {
                            sub_amount_sum = Convert.ToDouble(ov);
                        }
                        dbDeal = new CDbDealer();
                        dbDeal.AddField("voucher_no", sVoucherNo);
                        dbDeal.AddField("row_index", row_index.ToString(), "number");
                        dbDeal.AddField("subject_id", CurSubCls.sub_id);//主营业务收入                      
                        dbDeal.AddField("amount", sub_amount_sum.ToString(), "number");
                        if (bReturn) is_debit = "1"; else is_debit = "0";
                        dbDeal.AddField("is_debit", is_debit, "number");

                        if (bReturn) debit_flag = "1"; else debit_flag = "-1";
                        dbDeal.AddField("debit_flag", debit_flag);

                        dbDeal.AddField("brief", "");
                        sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                        sCmd += sDetailCmd;
                    }


                    //cmd.CommandText = "select account_class,sum(sub_amount) sub_amount_sum from (select item_no,sub_amount,account_class from " + m_tb_detail + " left join " + m_tb_master + " on " + m_tb_detail + ".sheet_no=" + m_tb_master + ".sheet_no left join info_item_prop on " + m_tb_detail + ".item_no=info_item_prop.item_no where " + strCondi + ") sheet_sql group by account_class";
                    //dr = cmd.ExecuteReader();
                    //while (dr.Read())
                    //{
                    //    string account_class = CPubVars.GetTextFromDr(dr, "account_class");
                    //    ov = dr["sub_amount_sum"];
                    //    double sub_amount_sum = 0;
                    //    if (ov != DBNull.Value)
                    //    {
                    //        sub_amount_sum = Convert.ToDouble(ov);
                    //    }
                    //    dbDeal = new CDbDealer();
                    //    dbDeal.AddField("voucher_no", sVoucherNo);
                    //    dbDeal.AddField("row_index", row_index.ToString(), "number");
                    //    dbDeal.AddField("sub_id", "6001");//主营业务收入
                    //    dbDeal.AddField("class_id", account_class);//主营业务收入
                    //    dbDeal.AddField("amount", sub_amount_sum.ToString(), "number");
                    //    if (bReturn) is_debit = "0"; else is_debit = "1";
                    //    dbDeal.AddField("is_debit", is_debit, "number");

                    //    if (bReturn) debit_flag = "1"; else debit_flag = "-1";
                    //    dbDeal.AddField("debit_flag", debit_flag);

                    //    dbDeal.AddField("brief", "");
                    //    sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                    //    sCmd += sDetailCmd;

                    //}
                    //dr.Close();


                }

            }
            //应缴增值税
            if (nTaxAmount > 0)
            {
                row_index++;
                dbDeal = new CDbDealer();
                dbDeal.AddField("voucher_no", sVoucherNo);
                dbDeal.AddField("row_index", row_index.ToString(), "number");
                dbDeal.AddField("subject_id", "22210103");//销项税额
                dbDeal.AddField("amount", nTaxAmount.ToString(), "number");
                if (bReturn) is_debit = "1"; else is_debit = "0";
                dbDeal.AddField("is_debit", is_debit, "number");
                dbDeal.AddField("brief", "");
                sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                sCmd += sDetailCmd;
            }
            sCmd += "update " + m_tb_master + " set voucher_no='" + sVoucherNo + "' where " + strCondi + ";";
            cmd.CommandText = sCmd;
            cmd.ExecuteNonQuery();
            return "";
        }
        public string CreateSaleChengBenVoucher(string com_no,CMySbConnection conn, CMySbCommand cmd, CMySbTransaction tran, string strCondi)
        {
            CMySbDataReader dr = null;
            Dictionary<string, SheetRow> rowsDict = new Dictionary<string, SheetRow>();
            string sCmd = "";
            //string strSQL="";
            string sVoucherNo = GetNewSheetNo("PZ", com_no, conn, cmd);

            cmd.CommandText = "select set_value from setting where set_name='Period'";
            object ov = cmd.ExecuteScalar();
            if (ov == null || ov == DBNull.Value)
                return "No current period";
            string sPeriod = ov.ToString().Trim();
            cmd.CommandText = "select max(voucher_index) from cw_voucher_master where period='" + sPeriod + "'";
            ov = cmd.ExecuteScalar();
            int max_index = 0;
            if (ov != null && ov != DBNull.Value)
                max_index = Convert.ToInt32(ov);
            max_index++;

            double nCostAmount = 0;
            string sheet_no = "";
            cmd.CommandText = "select " + m_tb_detail + ".cost_price," + m_tb_detail + ".sheet_no," + m_tb_detail + ".quantity from " + m_tb_detail + " left join (select * from " + m_tb_master + " where " + strCondi + ") master_tb on " + m_tb_detail + ".sheet_no=master_tb.sheet_no where not master_tb.sheet_no is null";
            dr = cmd.ExecuteReader();
            while (dr.Read())
            {
                ov = dr["cost_price"];
                double nCostPrice = 0;
                if (ov != DBNull.Value) nCostPrice = Convert.ToDouble(ov);
                double nQuantity = 0;
                ov = dr["quantity"];
                if (ov != DBNull.Value) nQuantity = Convert.ToDouble(ov);
                double n = nCostPrice * nQuantity;
                n = double.Parse(n.ToString("F2"));
                nCostAmount += n;

                ov = dr["sheet_no"];
                if (ov != DBNull.Value)
                {
                    string s = ov.ToString().Trim();
                    if (sheet_no.Length < 50)
                        sheet_no += s;
                    else
                    {
                        sheet_no += s;
                        sheet_no += "等";
                    }
                }
            }
            dr.Close();

            CDbDealer dbDeal = new CDbDealer();
            dbDeal.AddField("voucher_no", sVoucherNo);
            dbDeal.AddField("voucher_word", "记");
            dbDeal.AddField("voucher_index", max_index.ToString(), "number");
            dbDeal.AddField("period", sPeriod);
            dbDeal.AddField("summary", "结转成本.销售单:" + sheet_no);
            dbDeal.AddField("total_amount", nCostAmount.ToString(), "number");
            dbDeal.AddField("make_time", CPubVars.GetDateText(DateTime.Now), "date");

            string sMasterCmd = dbDeal.GetInsertSQL("cw_voucher_master") + ";";
            sCmd += sMasterCmd;

            int row_index = 0;

            if (nCostAmount > 0)
            {
                //主营业务成本
                row_index++;
                dbDeal = new CDbDealer();
                dbDeal.AddField("voucher_no", sVoucherNo);
                dbDeal.AddField("row_index", row_index.ToString(), "number");
                dbDeal.AddField("sub_id", "6401");
                dbDeal.AddField("amount", nCostAmount.ToString(), "number");
                dbDeal.AddField("is_debit", "1", "number");
                dbDeal.AddField("brief", "");
                string sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                sCmd += sDetailCmd;

                //库存商品

                row_index++;

                dbDeal = new CDbDealer();
                dbDeal.AddField("voucher_no", sVoucherNo);
                dbDeal.AddField("row_index", row_index.ToString(), "number");

                dbDeal.AddField("sub_id", "1405");
                dbDeal.AddField("amount", nCostAmount.ToString(), "number");
                dbDeal.AddField("is_debit", "0", "number");
                dbDeal.AddField("brief", "");
                sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                sCmd += sDetailCmd;
                sCmd += "update " + m_tb_master + " set voucher_no='" + sVoucherNo + "' where " + strCondi + ";";
                cmd.CommandText = sCmd;
                cmd.ExecuteNonQuery();
            }
            return "";
        }
        public string CreateBuyVoucher(string com_no,CMySbConnection conn, CMySbCommand cmd, CMySbTransaction tran, string strCondi)
        {
            CMySbDataReader dr = null;
            Dictionary<string, SheetRow> rowsDict = new Dictionary<string, SheetRow>();
            string sCmd = "";

            string sVoucherNo = GetNewSheetNo("PZ", com_no, conn, cmd);

            cmd.CommandText = "select set_value from setting where set_name='Period'";
            object ov = cmd.ExecuteScalar();
            if (ov == null || ov == DBNull.Value)
                return "No current period";
            string sPeriod = ov.ToString().Trim();
            cmd.CommandText = "select max(voucher_index) from cw_voucher_master where period='" + sPeriod + "'";
            ov = cmd.ExecuteScalar();
            int max_index = 0;
            if (ov != null && ov != DBNull.Value)
                max_index = Convert.ToInt32(ov);
            max_index++;

            double nTotalAmount = 0;
            double nFaxAmount = 0;
            double nCashAmount = 0;
            double nBankAmount = 0;
            double nCardAmount = 0;
            double nTicketAmount = 0;
            double nPaidAmount = 0;
            double nDiscAmount = 0;
            string supcust_no = "";
            string sheet_no = "";

            Dictionary<string, COughtMoney> dicOughtMoney = new Dictionary<string, COughtMoney>();
            Dictionary<string, COughtMoney> dicBankMoney = new Dictionary<string, COughtMoney>();
            cmd.CommandText = "select * from " + m_tb_master + " where " + strCondi;
            dr = cmd.ExecuteReader();
            while (dr.Read())
            {
                ov = dr["total_amount"];
                double nTotalCur = 0;
                if (ov != DBNull.Value) nTotalCur = Convert.ToDouble(ov);
                nTotalAmount += nTotalCur;
                double n = 0;
                ov = dr["fax_amount"];
                if (ov != DBNull.Value) n = Convert.ToDouble(ov);
                nFaxAmount += n;
                n = 0;
                ov = dr["cash_amount"];
                if (ov != DBNull.Value) n = Convert.ToDouble(ov);
                nCashAmount += n;
                n = 0;
                ov = dr["bank_amount"];
                if (ov != DBNull.Value) n = Convert.ToDouble(ov);
                nBankAmount += n;
                if (n > 0)
                {
                    string box_bank = "";
                    ov = dr["box_bank"];
                    if (ov != DBNull.Value) box_bank = ov.ToString();
                    COughtMoney oughtMoney = null;
                    dicOughtMoney.TryGetValue(box_bank, out oughtMoney);
                    if (oughtMoney == null)
                    {
                        oughtMoney = new COughtMoney();
                        oughtMoney.supcust_no = box_bank;
                        dicOughtMoney.Add(supcust_no, oughtMoney);
                    }
                    oughtMoney.amount += n;
                }
                n = 0;
                ov = dr["card_amount"];
                if (ov != DBNull.Value) n = Convert.ToDouble(ov);
                nCardAmount += n;
                n = 0;
                ov = dr["ticket_amount"];
                if (ov != DBNull.Value) n = Convert.ToDouble(ov);
                nTicketAmount += n;
                double nPaidCur = 0; ov = dr["now_pay_amount"];
                if (ov != DBNull.Value) nPaidCur = Convert.ToDouble(ov);
                nPaidAmount += nPaidCur;
                double nDiscCur = 0; ov = dr["now_disc_amount"];
                if (ov != DBNull.Value) nDiscCur = Convert.ToDouble(ov);
                nDiscAmount += nDiscCur;
                double nLeftCur = nTotalCur - nDiscCur - nPaidAmount;
                if (nLeftCur > 0)
                {
                    ov = dr["supcust_no"];
                    if (ov != DBNull.Value) supcust_no = ov.ToString();
                    COughtMoney oughtMoney = null;
                    dicOughtMoney.TryGetValue(supcust_no, out oughtMoney);
                    if (oughtMoney == null)
                    {
                        oughtMoney = new COughtMoney();
                        oughtMoney.supcust_no = supcust_no;
                        dicOughtMoney.Add(supcust_no, oughtMoney);
                    }
                    oughtMoney.amount += nLeftCur;
                }
                ov = dr["sheet_no"];
                if (ov != DBNull.Value)
                {
                    string s = ov.ToString().Trim();
                    if (sheet_no.Length < 50)
                        sheet_no += s;
                    else
                    {
                        sheet_no += s;
                        sheet_no += "等";
                    }
                }

            }
            dr.Close();
            double nIncomeAmount = nTotalAmount - nFaxAmount;
            double nLeftAmount = nTotalAmount - nDiscAmount - nPaidAmount;

            CDbDealer dbDeal = new CDbDealer();
            dbDeal.AddField("voucher_no", sVoucherNo);
            dbDeal.AddField("voucher_word", "记");
            dbDeal.AddField("voucher_index", max_index.ToString(), "number");
            dbDeal.AddField("period", sPeriod);
            dbDeal.AddField("summary", "进货单" + sheet_no);
            dbDeal.AddField("total_amount", nTotalAmount.ToString(), "number");
            dbDeal.AddField("make_time", CPubVars.GetDateText(DateTime.Now), "date");

            string sMasterCmd = dbDeal.GetInsertSQL("cw_voucher_master") + ";";
            sCmd += sMasterCmd;
            string sDetailCmd = "";
            int row_index = 0;
            //现金
            if (nCashAmount > 0)
            {
                row_index++;
                dbDeal = new CDbDealer();
                dbDeal.AddField("voucher_no", sVoucherNo);
                dbDeal.AddField("row_index", row_index.ToString(), "number");
                dbDeal.AddField("subject_id", "1001");
                dbDeal.AddField("amount", nIncomeAmount.ToString(), "number");
                dbDeal.AddField("is_debit", "0", "number");
                dbDeal.AddField("brief", "");
                sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                sCmd += sDetailCmd;
            }
            //银行
            foreach (KeyValuePair<string, COughtMoney> di in dicOughtMoney)
            {
                row_index++;
                COughtMoney om = di.Value;
                dbDeal = new CDbDealer();
                dbDeal.AddField("voucher_no", sVoucherNo);
                dbDeal.AddField("row_index", row_index.ToString(), "number");

                dbDeal.AddField("subject_id", om.supcust_no);
                dbDeal.AddField("amount", om.amount.ToString(), "number");
                dbDeal.AddField("is_debit", "0", "number");
                dbDeal.AddField("brief", "");
                sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                sCmd += sDetailCmd;
            }
            //应付账款
            foreach (KeyValuePair<string, COughtMoney> di in dicOughtMoney)
            {
                row_index++;
                COughtMoney om = di.Value;
                dbDeal = new CDbDealer();
                dbDeal.AddField("voucher_no", sVoucherNo);
                dbDeal.AddField("row_index", row_index.ToString(), "number");
                dbDeal.AddField("subject_id", "2202");
                dbDeal.AddField("sup_id", om.supcust_no);
                dbDeal.AddField("amount", nIncomeAmount.ToString(), "number");
                dbDeal.AddField("is_debit", "0", "number");
                dbDeal.AddField("brief", "");
                sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
                sCmd += sDetailCmd;
            }
            //库存商品
            row_index++;
            dbDeal = new CDbDealer();
            dbDeal.AddField("voucher_no", sVoucherNo);
            dbDeal.AddField("row_index", row_index.ToString(), "number");
            dbDeal.AddField("subject_id", "1405");
            dbDeal.AddField("amount", nIncomeAmount.ToString(), "number");
            dbDeal.AddField("is_debit", "0", "number");
            dbDeal.AddField("brief", "");
            sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
            sCmd += sDetailCmd;

            //应缴增值税
            row_index++;
            dbDeal = new CDbDealer();
            dbDeal.AddField("voucher_no", sVoucherNo);
            dbDeal.AddField("row_index", row_index.ToString(), "number");
            dbDeal.AddField("subject_id", "22210101");//进项税额
            dbDeal.AddField("amount", nIncomeAmount.ToString(), "number");
            dbDeal.AddField("is_debit", "0", "number");
            dbDeal.AddField("brief", "");
            sDetailCmd = dbDeal.GetInsertSQL("cw_voucher_detail") + ";";
            sCmd += sDetailCmd;
            sCmd += "update " + m_tb_master + " set voucher_no='" + sVoucherNo + "' where " + strCondi + ";";
            cmd.CommandText = sCmd;
            cmd.ExecuteNonQuery();
            return "";
        }

        public static string GetPrintItemName(SHEET_TYPE sheetType, string fld)
        {
            string sItemValue = "";
            int nPos = -1;
            for (int i = fld.Length - 1; i >= 0; i--)
            {
                string s = fld.Substring(i, 1);
                if (!"1234567890".Contains(s))
                {
                    nPos = i;
                    break;
                }
            }
            if (nPos >= 0)
            {
                fld = fld.Substring(0, nPos + 1);
            }
            switch (fld)
            {
                case "sheet_title":
                    sItemValue = "单据标题";
                    break;
                case "money_receiver":
                    sItemValue = "收款人";
                    break;
                case "good_receiver":
                    sItemValue = "收货人";
                    break;
                case "good_sender":
                    sItemValue = "收货人";
                    break;
                case "cust_addr":
                    sItemValue = "发货地址";
                    break;
                case "cust_tel":
                    sItemValue = "联系电话";
                    break;
                case "cust_contact":
                    sItemValue = "联系人";
                    break;
                case "sheet_no":
                    sItemValue = "单据号";
                    break;
                case "orig_sheet_no":
                    sItemValue = "原单号";
                    break;
                case "orig_oper_date":
                    sItemValue = "原制单时间";
                    break;     
                case "page_index":
                    sItemValue = "页号";
                    break;
                case "page_qty":
                    sItemValue = "本页数量";
                    break;
                case "page_amount":
                    sItemValue = "本页金额";
                    break;
                case "branch_no":
                    //if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
                    //    sItemValue = "出货仓库";
                    //else 
                    sItemValue = "仓库";
                    break;
                case "op_store":
                    sItemValue = "制单门店";
                    break;
                case "out_branch_no":
                    sItemValue = "出货仓库";
                    break;
                case "in_branch_no":
                    sItemValue = "进货仓库";
                    break;
                case "supcust_no":
                    sItemValue = "客户";
                    break;
                case "sup_tel":
                    sItemValue = "联系电话";
                    break;
                case "pay_way":
                    sItemValue = "支付方式";
                    break;
                case "discount":
                    sItemValue = "折扣";
                    break;
                case "coin_no":
                    sItemValue = "币种";
                    break;
                case "total_amount":
                    sItemValue = "合计";
                    break;
                case "total_qty":
                    sItemValue = "件数";
                    break;
                case "disc_amount":
                    sItemValue = "抹零";
                    break;
                case "ought_amount":
                    sItemValue = "应收";
                    break;
                case "paid_amount":
                    sItemValue = "已收";
                    break;
                case "left_amount":
                    sItemValue = "尚欠";
                    break;
                case "total_left_amount":
                    sItemValue = "累计欠款";
                    break;
                case "approve_flag":
                    sItemValue = "审核标志";
                    break;
                case "oper_id":
                    sItemValue = "操作员";
                    break;
                case "sale_man":
                    sItemValue = "业务员";
                    break;
                case "work_man":
                    if (sheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM)
                        sItemValue = "盘点人";
                    else
                        sItemValue = "业务员";
                    break;
                case "oper_date":
                    sItemValue = "制单时间";
                    break;
                case "trans_date":
                    if (sheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM)
                    {
                        sItemValue = "盘点时间";
                    }
                    else if (sheetType == SHEET_TYPE.SHEET_SALE)
                        sItemValue = "销售时间";
                    else if (sheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                        sItemValue = "退货时间";
                    else if (sheetType == SHEET_TYPE.SHEET_BUY)
                        sItemValue = "进货时间";
                    else if (sheetType == SHEET_TYPE.SHEET_BUY_RETURN)
                        sItemValue = "退货时间";
                    else
                        sItemValue = "工作时间";
                    break;
                case "remark":
                    sItemValue = "备注";
                    break;
                case "brief":
                    sItemValue = "说明文字";
                    break;
                case "brief1":
                    sItemValue = "说明文字段1";
                    break;
                case "brief2":
                    sItemValue = "说明文字段2";
                    break;
                case "brief3":
                    sItemValue = "说明文字段3";
                    break;
                case "item_index":
                    sItemValue = "序号";
                    break;
                case "item_no":
                    sItemValue = "商品编号";
                    break;
                case "item_name":
                    sItemValue = "商品名称";
                    break;
                case "item_subno":
                    sItemValue = "商品条码";
                    break;
                case "unit_no":
                    sItemValue = "单位";
                    break;
                case "unit_factor":
                    sItemValue = "单位数量";
                    break;
                case "color":
                    sItemValue = "颜色";
                    break;
                case "size":
                    sItemValue = "尺码";
                    break;
                case "quantity":
                    sItemValue = "数量";
                    break;
                case "real_price":
                    if (sheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM)
                    {
                        sItemValue = "成本价";
                    }
                    else
                        sItemValue = "价格";
                    break;

                case "amount":
                    if (sheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM)
                    {
                        sItemValue = "成本金额";
                    }
                    else
                        sItemValue = "金额";
                    break;
                case "in_amount":
                    sItemValue = "进价金额";
                    break;
                case "base_amount":
                    sItemValue = "批发金额";
                    break;
                case "sale_amount":
                    sItemValue = "零售金额";
                    break;
                case "item_branch":
                    sItemValue = "单项仓库";
                    break;
                case "item_brief":
                    sItemValue = "单项备注";
                    break;
                //条码
                case "item_price":
                    sItemValue = "价格";
                    break;
                case "item_model":
                    sItemValue = "货号/型号";
                    break;                
                case "item_size":
                    sItemValue = "规格";
                    break;
                case "item_class":
                    sItemValue = "类别";
                    break;
                case "brand":
                    sItemValue = "品牌";
                    break;
                case "store_name":
                    sItemValue = "商店名称";
                    break;
                case "box_no":
                    sItemValue = "箱号";                    
                    break;
                case "prepay_left":
                    sItemValue = "预收款余额";
                    break;
                case "prepay_amount":
                    sItemValue = "预收款扣减:";
                    break;
                case "fee_type":
                    sItemValue = "支出类别:";
                    break;
                case "fee_in_type":
                    sItemValue = "支出类别:";
                    break;

            }
            if (fld.StartsWith("brief"))
            {
                sItemValue = fld.Replace("brief","说明文字");                
            }
            return sItemValue;
        }
        public override string GetPrintSheetMasterName()
        {
             //string sheet_flag = "";
             //if (m_ReportPrintFlag != "")
             //{
             //    sheet_flag = m_ReportPrintFlag;
             //}
             //else
             //{
             //    sheet_flag = StrFromSheetType(m_SheetType);
             //}
            if (m_SheetType == SHEET_TYPE.SHEET_SALE)
            {
               
                   return "sale_sheet_master";
               
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
            {
                
                   return "sale_return_sheet_master";
                
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
            {
              //  if (m_bBatchSale)
                    return "store_out_sheet_master";
              //  else
              //      return "store_out_sheet_master_l";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_STORE_IN)
            { 
                 return "store_in_sheet_master"; 
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_BUY)
            {
                return "buy_sheet_master";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN)
            {
                return "buy_return_sheet_master";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
            {
                return "move_sheet_master";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM)
            {
                return "invent_add_master";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_INVENT_REDUCE)
            {
                return "invent_reduce_master";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_INVENT_INPUT)
            {
                return "invent_sheet_master";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_PRE_GET_MONEY)
            {
                return "pre_getmoney_master";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_PRE_PAY_MONEY)
            {
                return "pre_paymoney_master";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD)
            {
                return "sale_dd";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD)
            {
                return "sale_dd_master";
            }
            
            return "";
        }
        public override string GetPrintSheetDetailName()
        {
            if (m_SheetType == SHEET_TYPE.SHEET_SALE)
            {
              
                   return "sale_sheet_detail";
               
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
            {
               
                   return "sale_return_sheet_detail";
              
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_STORE_OUT)
            {
                //if (m_bBatchSale)
                    return "store_out_sheet_detail";
                //else
                //    return "store_out_sheet_detail_l";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_STORE_IN)
            {
                return "store_in_sheet_master";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_BUY)
            {
                return "buy_sheet_detail";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN)
            {
                return "buy_return_sheet_detail";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE)
            {
                return "move_sheet_detail";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_INVENT_CONFIRM)
            {
                return "invent_add_detail";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_INVENT_REDUCE)
            {
                return "invent_reduce_detail";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_SALE_DD)
            {
                return "sale_dd_detail";
            }
            else if (m_SheetType == SHEET_TYPE.SHEET_INVENT_INPUT)
            {
                return "invent_sheet_detail";
            }
            return "";
        } 
       
        public void GetReportSheetRows(CMySbCommand cmd,string sTimeCondi)
        {
            if (m_form_xml!="")
            {
                XmlDocument xml=new XmlDocument();
                string s_form_xml = m_form_xml;
                s_form_xml = s_form_xml.Replace("\r\n", "~");
                s_form_xml = s_form_xml.Replace("\n", "~"); 
                xml.LoadXml(s_form_xml);
                string table_xml = "";
                XmlNode show_cls_node = null; 
                foreach (XmlNode node in xml.ChildNodes[0].ChildNodes)
                { 
                    if (node.Name == "table" && node.ChildNodes.Count > 0)
                    {
                        table_xml = node.InnerText;
                        break;
                    }
                    else if (node.Name == "ShowClass")
                    {
                        show_cls_node = node;
                    }
                }
                 
                if (table_xml.Contains("品名"))
                {
                    //string class1 = "(select item_clsno as clsno1,item_clsname as clsname1,mother_clsno as mother_class1,order_index as clsindex1 from info_item_class) class1";
                    //string class2 = "(select item_clsno as clsno2,item_clsname as clsname2,mother_clsno as mother_class2,order_index as clsindex2 from info_item_class) class2 ";
                    //string class3 = "(select item_clsno as clsno3,item_clsname as clsname3,mother_clsno as clsno4,order_index as clsindex3 from info_item_class) class3 ";
                    //string orig_sheet = "(select " + m_tb_detail + ".* from " + m_tb_detail + " left join " + m_tb_master + " on " + m_tb_detail + ".sheet_no=" + m_tb_master + ".sheet_no where trans_no in ('S','D') and approve_flag='1' and (red_flag is null or red_flag='0') and " + sTimeCondi + ") orig_sheet ";

                    //string sheet_tb = "(select item_no,sheet_item_name,sum(quantity * unit_factor * (-inout_flag)) as qty_sum,sum(sub_amount *(-inout_flag)) as amount_sum,sum(orig_price*quantity*(-inout_flag)) as orig_amount_sum from " + orig_sheet + " group by item_no,sheet_item_name) sheet_tb ";
                    //string sql = "select sheet_tb.item_no,sheet_item_name,qty_sum,amount_sum,orig_amount_sum,info_item_prop.item_model,clsno1,clsno2,clsno3,clsno4,clsname1,clsname2,clsname3,clsindex1,clsindex2,clsindex3 from " + sheet_tb + " left join info_item_prop on sheet_tb.item_no=info_item_prop.item_no  left join " + class1 + " on info_item_prop.item_clsno=class1.clsno1 left join " + class2 + " on class1.mother_class1=class2.clsno2 left join " + class3 + " on class2.mother_class2 = class3.clsno3";
 
                    string class1 = "(select item_clsno as clsno1,item_clsname as clsname1,general_class as rpt_cls1,info_rpt_class.show_index as rpt_clsindex1, mother_clsno as mother_class1,order_index as clsindex1 from info_item_class left join info_rpt_class on info_item_class.general_class=info_rpt_class.rpt_class) class1";
                    string class2 = "(select item_clsno as clsno2,item_clsname as clsname2,general_class as rpt_cls2,info_rpt_class.show_index as rpt_clsindex2, mother_clsno as mother_class2,order_index as clsindex2 from info_item_class left join info_rpt_class on info_item_class.general_class=info_rpt_class.rpt_class) class2 ";
                    string class3 = "(select item_clsno as clsno3,item_clsname as clsname3,general_class as rpt_cls3,info_rpt_class.show_index as rpt_clsindex3, mother_clsno as clsno4       ,order_index as clsindex3 from info_item_class left join info_rpt_class on info_item_class.general_class=info_rpt_class.rpt_class) class3 ";
                    string orig_sheet = "(select " + m_tb_detail + ".* from " + m_tb_detail + " left join " + m_tb_master + " on " + m_tb_detail + ".sheet_no=" + m_tb_master + ".sheet_no where trans_no in ('S','D') and approve_flag='1' and (red_flag is null or red_flag='0') and inout_flag<>'0' and " + sTimeCondi + ") orig_sheet ";

                    string sheet_tb = "(select item_no,sheet_item_name,sum(quantity * unit_factor * (-inout_flag)) as qty_sum,sum(sub_amount *(-inout_flag)) as amount_sum,sum(orig_price*quantity*(-inout_flag)) as orig_amount_sum from " + orig_sheet + " group by item_no,sheet_item_name) sheet_tb ";
                    string sql = "select sheet_tb.item_no,sheet_item_name,info_item_prop.rpt_class as rpt_cls0,info_rpt_class.show_index as rpt_clsindex0,qty_sum,amount_sum,orig_amount_sum,info_item_prop.item_model,clsno1,clsno2,clsno3,clsno4,clsname1,clsname2,clsname3,clsindex1,clsindex2,clsindex3,rpt_cls1,rpt_cls2,rpt_cls3,rpt_clsindex1,rpt_clsindex2,rpt_clsindex3 from " + sheet_tb + @" left join info_item_prop on sheet_tb.item_no=info_item_prop.item_no  
                    left join " + class1 + @" on info_item_prop.item_clsno=class1.clsno1 
                    left join " + class2 + @" on class1.mother_class1=class2.clsno2 
                    left join " + class3 + @" on class2.mother_class2 = class3.clsno3
                    left join info_rpt_class on info_item_prop.rpt_class=info_rpt_class.rpt_class ";
 

                    cmd.CommandText = sql;
                    CMySbDataReader dr = cmd.ExecuteReader();
                    SheetRows.Clear();
                    while (dr.Read())
                    {
                        string item_no = CPubVars.GetTextFromDr(dr, "item_no");
                        string item_name = CPubVars.GetTextFromDr(dr, "sheet_item_name");
                        string item_model = CPubVars.GetTextFromDr(dr, "item_model");
                        string qty_sum = CPubVars.GetTextFromDr(dr, "qty_sum");

                        string amount_sum = CPubVars.GetTextFromDr(dr, "amount_sum");
                        string orig_amount_sum = CPubVars.GetTextFromDr(dr, "orig_amount_sum");
                        double nOrigAmt = 0; double nOrigPrice = 0;
                        double nQty = 0;
                        if (orig_amount_sum != "")
                        {
                            nOrigAmt = Convert.ToDouble(orig_amount_sum);
                        }
                        double nRealAmt = 0; double nRealPrice = 0;
                        if (amount_sum != "")
                        {
                            nRealAmt = Convert.ToDouble(amount_sum);
                        }

                        if (qty_sum != "")
                        {
                            nQty = Convert.ToDouble(qty_sum);
                        }
                        if (nQty != 0)
                        {
                            nOrigPrice = nOrigAmt / nQty;
                            nRealPrice = nRealAmt / nQty;
                        }
                        string class1_no = CPubVars.GetTextFromDr(dr, "clsno1");
                        string class2_no = CPubVars.GetTextFromDr(dr, "clsno2");
                        string class3_no = CPubVars.GetTextFromDr(dr, "clsno3");
                        string class4_no = CPubVars.GetTextFromDr(dr, "clsno4");
                        string class1_name = CPubVars.GetTextFromDr(dr, "clsname1");
                        string class2_name = CPubVars.GetTextFromDr(dr, "clsname2");
                        string class3_name = CPubVars.GetTextFromDr(dr, "clsname3");
                      
                        object ov = null;
                        int clsindex1 = 0; ov = dr["clsindex1"]; if (ov != DBNull.Value) clsindex1 = Convert.ToInt32(ov);
                        int clsindex2 = 0; ov = dr["clsindex2"]; if (ov != DBNull.Value) clsindex2 = Convert.ToInt32(ov);
                        int clsindex3 = 0; ov = dr["clsindex3"]; if (ov != DBNull.Value) clsindex3 = Convert.ToInt32(ov);

                        string rpt_cls0 = CPubVars.GetTextFromDr(dr, "rpt_cls0");
                        string rpt_cls1 = CPubVars.GetTextFromDr(dr, "rpt_cls1");
                        string rpt_cls2 = CPubVars.GetTextFromDr(dr, "rpt_cls2");
                        string rpt_cls3 = CPubVars.GetTextFromDr(dr, "rpt_cls3");

                        int rpt_clsindex0 = 0; ov = dr["rpt_clsindex0"]; if (ov != DBNull.Value) rpt_clsindex0 = Convert.ToInt32(ov);
                        int rpt_clsindex1 = 0; ov = dr["rpt_clsindex1"]; if (ov != DBNull.Value) rpt_clsindex1 = Convert.ToInt32(ov);
                        int rpt_clsindex2 = 0; ov = dr["rpt_clsindex2"]; if (ov != DBNull.Value) rpt_clsindex2 = Convert.ToInt32(ov);
                        int rpt_clsindex3 = 0; ov = dr["rpt_clsindex3"]; if (ov != DBNull.Value) rpt_clsindex3 = Convert.ToInt32(ov);

                        SheetRow row = new SheetRow();
                        row.item_no = item_no;
                        row.item_name = item_name;
                        row.item_model = item_model;
                        row.orig_price = nOrigPrice;
                        row.real_price = nRealPrice;
                        if (CPubVars.IsNumeric(qty_sum))
                            row.quantity = Convert.ToDouble(qty_sum);
                        if (CPubVars.IsNumeric(amount_sum))
                            row.money = Convert.ToDouble(amount_sum);
                        row.clsindex1 = clsindex1;
                        row.clsindex2 = clsindex2;
                        row.clsindex3 = clsindex3;
                        row.clsno1 = class1_no;
                        row.clsno2 = class2_no;
                        row.clsno3 = class3_no;
                        row.clsno4 = class4_no;
                        row.clsname1 = class1_name;
                        row.clsname2 = class2_name;
                        row.clsname3 = class3_name;


                        row.rpt_class = rpt_cls0; row.rpt_clsindex = rpt_clsindex0;

                        if (row.rpt_class == "") { row.rpt_class = rpt_cls1; row.rpt_clsindex = rpt_clsindex1; }
                        if (row.rpt_class == "") { row.rpt_class = rpt_cls2; row.rpt_clsindex = rpt_clsindex2; }
                        if (row.rpt_class == "") { row.rpt_class = rpt_cls3; row.rpt_clsindex = rpt_clsindex3; } 
                        
                        SheetRows.Add(row);
                    }
                    dr.Close();

                    if (m_table_show_style == "1")
                    {
                        OrderRowsByClass(false, SheetRows);
                    }
                    else if (m_table_show_style == "2")
                    {
                        OrderRowsByClass(true, SheetRows);
                    }
                    else if (m_table_show_style == "3")
                    {
                         OrderRowsByRptClass(SheetRows); 
                    }
                    else if (m_table_show_style == "4")
                        OrderRowsBySpecClassOrder(show_cls_node, SheetRows);  
                }
                else if (table_xml.Contains("业务员"))
                {
                    string sql = "select order_man_name,sum(total_amount * money_inout_flag) as sum_amount,sum(total_quantity * money_inout_flag) as sum_qty from " + m_tb_master + " where trans_no in ('S','D') and approve_flag='1' and (red_flag is null or red_flag='0') and " + sTimeCondi + " group by order_man_name";
 
                    cmd.CommandText = sql;
                    CMySbDataReader dr = cmd.ExecuteReader();
                    SheetRows.Clear();
                    while (dr.Read())
                    {
                        string order_man_name = CPubVars.GetTextFromDr(dr, "order_man_name");
                        string sum_amount = CPubVars.GetTextFromDr(dr, "sum_amount");
                        string sum_qty = CPubVars.GetTextFromDr(dr, "sum_qty");
                           
                        SheetRow row = new SheetRow();
                      
                        row.item_name = order_man_name;
                     
                        if (CPubVars.IsNumeric(sum_qty))
                            row.quantity = Convert.ToDouble(sum_qty);
                        if (CPubVars.IsNumeric(sum_amount))
                            row.money = Convert.ToDouble(sum_amount); 
                        SheetRows.Add(row);
                    }
                    dr.Close();   
                }
                else if (table_xml.Contains("销售单号"))
                {
                    string sql = "select sheet_no,total_amount * money_inout_flag as total_amount_t,total_quantity * money_inout_flag as total_quantity_t,disc_amount * money_inout_flag as disc_amount_t,(total_amount-disc_amount - now_pay_amount) * money_inout_flag as left_amount_t,cash_amount  * money_inout_flag as cash_amount_t,bank_amount  * money_inout_flag as bank_amount_t,card_amount  * money_inout_flag as card_amount_t ,ticket_amount  * money_inout_flag as ticket_amount_t,integral_amount  * money_inout_flag as integral_amount_t,gp_site_name,(gp_ticket_amount +gp_setmeal_amount)  * money_inout_flag as gp_amount_t ,other_payway,other_amount  * money_inout_flag as other_amount_t from " + m_tb_master + " where trans_no in ('S','D') and approve_flag='1' and (red_flag is null or red_flag='0') and " + sTimeCondi + " order by oper_date";
                    cmd.CommandText = sql;
                    CMySbDataReader dr = cmd.ExecuteReader();
                    SheetRows.Clear();
                    while (dr.Read())
                    {
                        string sheet_no = CPubVars.GetTextFromDr(dr, "sheet_no");
                        string total_amount = CPubVars.GetTextFromDr(dr, "total_amount_t");
                        string total_quantity = CPubVars.GetTextFromDr(dr, "total_quantity_t");
                        string cash_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "cash_amount_t"), 2);
                        string bank_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "bank_amount_t"), 2);
                        string card_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "card_amount_t"), 2);
                        string ticket_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "ticket_amount_t"), 2);
                        string integral_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "integral_amount_t"), 2);
                        string other_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other_amount_t"), 2);
                        string other_payway = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other_payway"), 2);
                        string gp_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "gp_amount_t"), 2);
                        string gp_site_name = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "gp_site_name"), 2);
                        //string other1_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other1_amount_t"), 2);
                        //string other2_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other2_amount_t"), 2);
                        //string other3_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other3_amount_t"), 2);
                        //string other4_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other4_amount_t"), 2);
                        //string other5_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other5_amount_t"), 2);
                        //string other6_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other6_amount_t"), 2);
  
                        string disc_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "disc_amount_t"), 2);
                        string left_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "left_amount_t"), 2);  

                        SheetRow row = new SheetRow();

                        row.item_name = sheet_no;

                        if (CPubVars.IsNumeric(total_quantity))
                            row.quantity = Convert.ToDouble(total_quantity);
                        if (CPubVars.IsNumeric(total_amount))
                            row.money = Convert.ToDouble(total_amount);
                        string pay_info = "";
                        if (cash_amount != "0" )
                        {
                            pay_info = "现金:" + cash_amount+" ";
                        }
                        if (bank_amount != "0")
                        {
                            pay_info += "信用卡:" + bank_amount + " ";
                        }
                        if (card_amount != "0")
                        {
                            pay_info += "储值卡:" + card_amount + " ";
                        }
                        if (ticket_amount != "0")
                        {
                            pay_info += "礼券:" + ticket_amount + " ";
                        }
                        if (integral_amount != "0")
                        {
                            pay_info += "积分兑换:" + integral_amount + " ";
                        }
                        if (gp_amount != "0" && gp_amount !="")
                        {
                            pay_info += gp_site_name + ":" + gp_amount + " ";
                        }
                        if (other_amount != "0" && other_amount != "")
                        {
                            pay_info += other_payway + ":" + other_amount + " ";
                        } 
                        if (disc_amount != "0")
                        {
                            pay_info += "抹零:" + disc_amount + " ";
                        }
                        if (left_amount != "0")
                        {
                            pay_info += "挂账:" + left_amount + " ";
                        }
                        row.other1 = pay_info;
                        SheetRows.Add(row);
                    }
                    dr.Close(); 
                }
                else if (table_xml.Contains("VIP单号") || table_xml.Contains("会员卡号"))
                {
                    string sql = "select sheet_no,other1,trans_no,total_amount * money_inout_flag as total_amount_t,total_quantity * money_inout_flag as total_quantity_t,disc_amount * money_inout_flag as disc_amount_t,(total_amount-disc_amount - now_pay_amount) * money_inout_flag as left_amount_t,cash_amount  * money_inout_flag as cash_amount_t,bank_amount  * money_inout_flag as bank_amount_t,card_amount  * money_inout_flag as card_amount_t ,ticket_amount  * money_inout_flag as ticket_amount_t,integral_amount  * money_inout_flag as integral_amount_t,other1_amount  * money_inout_flag as other1_amount_t ,other2_amount  * money_inout_flag as other2_amount_t,other3_amount  * money_inout_flag as other3_amount_t,other4_amount  * money_inout_flag as other4_amount_t,other5_amount  * money_inout_flag as other5_amount_t,other6_amount  * money_inout_flag as other6_amount_t from " + m_tb_master + " where trans_no in ('L','M','CZ','TC','XK','TX') and approve_flag='1' and (red_flag is null or red_flag='0') and " + sTimeCondi + " order by oper_date";
                    cmd.CommandText = sql;
                    CMySbDataReader dr = cmd.ExecuteReader();
                    SheetRows.Clear();
                    while (dr.Read())
                    {
                        string sheet_no = CPubVars.GetTextFromDr(dr, "sheet_no");
                        string card_id = CPubVars.GetTextFromDr(dr, "other1");
                        string total_amount = CPubVars.GetTextFromDr(dr, "total_amount_t");
                        string total_quantity = CPubVars.GetTextFromDr(dr, "total_quantity_t");
                        string cash_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "cash_amount_t"), 2);
                        string bank_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "bank_amount_t"), 2);
                        string card_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "card_amount_t"), 2);
                        string ticket_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "ticket_amount_t"), 2);
                        string integral_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "integral_amount_t"), 2);
                        string other1_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other1_amount_t"), 2);
                        string other2_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other2_amount_t"), 2);
                        string other3_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other3_amount_t"), 2);
                        string other4_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other4_amount_t"), 2);
                        string other5_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other5_amount_t"), 2);
                        string other6_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "other6_amount_t"), 2);

                        string disc_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "disc_amount_t"), 2);
                        string left_amount = CPubVars.FormatMoney(CPubVars.GetTextFromDr(dr, "left_amount_t"), 2);
                        string trans_no = CPubVars.GetTextFromDr(dr, "trans_no");
                        SheetRow row = new SheetRow();

                        row.item_name = sheet_no;
                        row.other2 = card_id;
                        if (trans_no == "M")
                            row.other3 = "售卡";
                        else if (trans_no == "L")
                            row.other3 = "退卡";
                        else if (trans_no == "CZ")
                            row.other3 = "充值";
                        else if (trans_no == "TZ")
                            row.other3 = "反充值";
                        else if (trans_no == "XK")
                            row.other3 = "续卡";
                        else if (trans_no == "TX")
                            row.other3 = "退续卡";

                        if (CPubVars.IsNumeric(total_quantity))
                            row.quantity = Convert.ToDouble(total_quantity);
                        if (CPubVars.IsNumeric(total_amount))
                            row.money = Convert.ToDouble(total_amount);
                        string pay_info = "";
                        if (cash_amount != "0")
                        {
                            pay_info = "现金:" + cash_amount + " ";
                        }
                        if (bank_amount != "0")
                        {
                            pay_info += "信用卡:" + bank_amount + " ";
                        }
                        if (card_amount != "0")
                        {
                            pay_info += "储值卡:" + card_amount + " ";
                        }
                        if (ticket_amount != "0")
                        {
                            pay_info += "礼券:" + ticket_amount + " ";
                        }
                        if (integral_amount != "0")
                        {
                            pay_info += "积分兑换:" + integral_amount + " ";
                        }
                      
                        if (disc_amount != "0")
                        {
                            pay_info += "抹零:" + disc_amount + " ";
                        }
                        if (left_amount != "0")
                        {
                            pay_info += "挂账:" + left_amount + " ";
                        }
                        row.other1 = pay_info;
                        SheetRows.Add(row);
                    }
                    dr.Close();
                }
            }
        }
       
       // private Dictionary<string, SheetRow> m_dicClassSumValues = new Dictionary<string, SheetRow>();
        public bool HaveSumInfo()
        {
            if (m_form_xml.Contains("类汇总_") || m_form_xml.Contains("lhz_") || m_form_xml.Contains("可优惠汇总") || m_form_xml.Contains("不可优惠汇总"))
            {
                return true;
            }
            return false;
        }
        public void GetSumInfo(CMySbCommand cmd, string sTimeCondi,CCode code)
        {
            Dictionary<string, SheetRow> dicRptClassSumValues = new Dictionary<string, SheetRow>();
            double allow_disc_amt = 0;
            double not_allow_disc_amt = 0;
            if (m_form_xml != "")
            {
                XmlDocument xml = new XmlDocument();
                string s_form_xml = m_form_xml;
                s_form_xml = s_form_xml.Replace("\r\n", "~");
                s_form_xml = s_form_xml.Replace("\n", "~");
                if (s_form_xml.StartsWith("<"))
                {
                    xml.LoadXml(s_form_xml);
                    string table_xml = "";
                    foreach (XmlNode node in xml.ChildNodes[0].ChildNodes)
                    {
                        if (node.Name == "table" && node.ChildNodes.Count > 0)
                        {
                            table_xml = node.InnerText;
                            break;
                        }
                    }
                }
                bool bHaveSumInfo = false;
                if (code != null)
                {
                    foreach (KeyValuePair<string, CFunction> di in code.m_dicCalledFun)
                    {
                        if (di.Value.fun_name == "list" && di.Value.param1 == "统计类")
                        {
                            bHaveSumInfo = true;
                            break;
                        }
                    }
                }
                if (!bHaveSumInfo)
                {
                    bHaveSumInfo = HaveSumInfo();
                }
                if (bHaveSumInfo)
                {
                    cmd.CommandText = "select rpt_class from info_rpt_class";
                    CMySbDataReader dr = cmd.ExecuteReader();
                    while (dr.Read())
                    {
                        string rpt_cls = CPubVars.GetTextFromDr(dr, "rpt_class");
                        SheetRow row = null;
                        if (!dicRptClassSumValues.ContainsKey(rpt_cls)) 
                        {
                            row = new SheetRow();
                            row.item_name = rpt_cls;
                            dicRptClassSumValues.Add(rpt_cls, row);
                        }
                    }
                    dr.Close();
                    SheetRow row_other = new SheetRow();
                    row_other.item_name = "未归类";
                    dicRptClassSumValues.Add("未归类", row_other);

                    string class1 = "(select item_clsno as clsno1,item_clsname as clsname1,general_class as rpt_cls1,mother_clsno as mother_class1,order_index as clsindex1 from info_item_class) class1";
                    string class2 = "(select item_clsno as clsno2,item_clsname as clsname2,general_class as rpt_cls2,mother_clsno as mother_class2,order_index as clsindex2 from info_item_class) class2 ";
                    string class3 = "(select item_clsno as clsno3,item_clsname as clsname3,general_class as rpt_cls3,mother_clsno as clsno4       ,order_index as clsindex3 from info_item_class) class3 ";
                    string approve_condi = "";
                    if (m_SheetType!= SHEET_TYPE.SHEET_SALE) //SHEET_TYPE.SHEET_LEAVE_DUTY SHEET_TYPE.SHEET_REPORT
                        approve_condi = " and approve_flag='1'";

                    string orig_sheet = "(select " + m_tb_detail + ".* from " + m_tb_detail + " left join " + m_tb_master + " on " + m_tb_detail + ".sheet_no=" + m_tb_master + ".sheet_no where trans_no in ('S','D')  and (red_flag is null or red_flag='0') and inout_flag<>'0' and " + sTimeCondi + approve_condi + ") orig_sheet ";

                    string sheet_tb = "(select item_no,sheet_item_name,sum(quantity * unit_factor * (-inout_flag)) as qty_sum,sum(sub_amount *(-inout_flag)) as amount_sum,sum(orig_price*quantity*(-inout_flag)) as orig_amount_sum from " + orig_sheet + " group by item_no,sheet_item_name) sheet_tb ";
                    string sql = "select sheet_tb.item_no,sheet_item_name,rpt_class as rpt_cls0,qty_sum,amount_sum,orig_amount_sum,info_item_prop.item_model,allow_disc,clsno1,clsno2,clsno3,clsno4,clsname1,clsname2,clsname3,clsindex1,clsindex2,clsindex3,rpt_cls1,rpt_cls2,rpt_cls3 from " + sheet_tb + @" left join info_item_prop on sheet_tb.item_no=info_item_prop.item_no  
                    left join " + class1 + @" on info_item_prop.item_clsno=class1.clsno1 
                    left join " + class2 + @" on class1.mother_class1=class2.clsno2 
                    left join " + class3 + @" on class2.mother_class2 = class3.clsno3";
                    //left join info_rpt_class on info_item_prop.rpt_class=info_rpt_class.rpt_class ";

                    cmd.CommandText = sql;
                    dr = cmd.ExecuteReader(); 
                    while (dr.Read())
                    {
                        string item_no = CPubVars.GetTextFromDr(dr, "item_no");
                        string item_name = CPubVars.GetTextFromDr(dr, "sheet_item_name");
                        string item_model = CPubVars.GetTextFromDr(dr, "item_model");
                        string qty_sum = CPubVars.GetTextFromDr(dr, "qty_sum");

                        string amount_sum = CPubVars.GetTextFromDr(dr, "amount_sum");
                        string orig_amount_sum = CPubVars.GetTextFromDr(dr, "orig_amount_sum");
                        string allow_disc = CPubVars.GetTextFromDr(dr, "allow_disc");
                        double nOrigAmt = 0; double nOrigPrice = 0;
                        double nQty = 0;
                        if (orig_amount_sum != "")
                        {
                            nOrigAmt = Convert.ToDouble(orig_amount_sum);
                        }
                        double nRealAmt = 0; double nRealPrice = 0;
                        if (amount_sum != "")
                        {
                            nRealAmt = Convert.ToDouble(amount_sum);
                        }

                        if (qty_sum != "")
                        {
                            nQty = Convert.ToDouble(qty_sum);
                        }
                        if (nQty != 0)
                        {
                            nOrigPrice = nOrigAmt / nQty;
                            nRealPrice = nRealAmt / nQty;
                        }
                        string class1_no = CPubVars.GetTextFromDr(dr, "clsno1");
                        string class2_no = CPubVars.GetTextFromDr(dr, "clsno2");
                        string class3_no = CPubVars.GetTextFromDr(dr, "clsno3");
                        string class4_no = CPubVars.GetTextFromDr(dr, "clsno4");
                        string class1_name = CPubVars.GetTextFromDr(dr, "clsname1");
                        string class2_name = CPubVars.GetTextFromDr(dr, "clsname2");
                        string class3_name = CPubVars.GetTextFromDr(dr, "clsname3");

                        string rpt_cls0 = CPubVars.GetTextFromDr(dr, "rpt_cls0");
                        string rpt_cls1 = CPubVars.GetTextFromDr(dr, "rpt_cls1");
                        string rpt_cls2 = CPubVars.GetTextFromDr(dr, "rpt_cls2");
                        string rpt_cls3 = CPubVars.GetTextFromDr(dr, "rpt_cls3");

                        object ov = null;
                        int clsindex1 = 0; ov = dr["clsindex1"]; if (ov != DBNull.Value) clsindex1 = Convert.ToInt32(ov);
                        int clsindex2 = 0; ov = dr["clsindex2"]; if (ov != DBNull.Value) clsindex2 = Convert.ToInt32(ov);
                        int clsindex3 = 0; ov = dr["clsindex3"]; if (ov != DBNull.Value) clsindex3 = Convert.ToInt32(ov);

                        string rpt_cls = rpt_cls0;
                        if (rpt_cls == "") rpt_cls = rpt_cls1;
                        if (rpt_cls == "") rpt_cls = rpt_cls2;
                        if (rpt_cls == "") rpt_cls = rpt_cls3;
                        if (rpt_cls == "") rpt_cls = "未归类";
                        if (rpt_cls != "")
                        {
                            SheetRow row = null;
                            if (dicRptClassSumValues.ContainsKey(rpt_cls))
                                row = dicRptClassSumValues[rpt_cls];
                            else
                            {
                                row = new SheetRow();
                                dicRptClassSumValues.Add(rpt_cls, row);
                            }
                            row.item_name = rpt_cls;
                            row.orig_price += nOrigAmt;
                            row.real_price += nRealAmt;
                            row.quantity += nQty;
                        }

                        if (allow_disc == "0")
                        {
                            not_allow_disc_amt += nRealAmt;
                        }
                        else
                            allow_disc_amt += nRealAmt; 
                    }
                    dr.Close();
                    if (m_lstSheetPrtVariables == null)
                        m_lstSheetPrtVariables = new LinkedList<CVariable>();
                    foreach (KeyValuePair<string, SheetRow> di in dicRptClassSumValues)
                    {
                        CVariable var = new CVariable();
                        var.name = "类汇总_"+di.Value.item_name;
                        var.short_name = "lhz_"+di.Value.item_name;
                        var.value = CPubVars.FormatMoney(di.Value.real_price, 2,true);                      
                        m_lstSheetPrtVariables.AddLast(var);
                        if (code != null)
                        {
                            foreach (KeyValuePair<string, CFunction> diFun in code.m_dicCalledFun)
                            {
                                CFunction fun = diFun.Value;
                                if (fun.fun_name == "list")
                                {                                
                                    if (fun.param1 == "统计类")
                                    {                                       
                                        if (fun.param2 == "")
                                        {
                                            if (fun.fun_value != "") fun.fun_value += ",";
                                            fun.fun_value += di.Value.item_name;
                                        }
                                        else  if (fun.param2 == "消费金额")
                                        {
                                            if (fun.fun_value != "") fun.fun_value += ",";
                                            fun.fun_value += CPubVars.FormatMoney(di.Value.real_price, 2, true);
                                        }
                                    }   
                                   
                                }
                            }
                        }
                    }
                    {
                        CVariable var = new CVariable();
                        var.name = "可优惠汇总";
                        var.short_name = "kyhhz";
                        var.value = CPubVars.FormatMoney(allow_disc_amt, 2, true);
                        m_lstSheetPrtVariables.AddLast(var);
                        var = new CVariable();
                        var.name = "不可优惠汇总";
                        var.short_name = "bkyhhz";
                        var.value = CPubVars.FormatMoney(not_allow_disc_amt, 2, true);
                        m_lstSheetPrtVariables.AddLast(var);
                    }
                } 
            }
        }
      
         
     
 
        public string ReplaceVar(string s, string var)
        {

            return "";
        }
        
        public static string MyPad(string s, int n, int ChLen)
        {
            int nLen = s.Length;
            ChLen = 0;
            for (int i = 0; i < s.Length; i++)
            {
                if (IsChineseLetter(s, i))
                {
                    ChLen++;
                }
            }
            nLen += ChLen;
            int n1 = n - nLen;
            string s1 = "";
            if (n1 > 0)
            {
                s1 = s + "".PadRight(n1);
            }
            else
            {
                s1 = s;
            }
            return s1;

        }
        private string MyPad(string s, int n, ref string cut_str)
        {
            int nLen = s.Length;
            int ChLen = 0;
            for (int i = 0; i < s.Length; i++)
            {
                if (IsChineseLetter(s, i))
                {
                    ChLen++;
                }
            }
            nLen += ChLen;
            int n1 = n - nLen;
            string s1 = "";
            if (n1 > 0)
            {
                s1 = s + "".PadRight(n1);
            }
            else
            {
                int nLeft = 0; int nByteLeft = 0;// n - ChLen;
                string spad = "";
                for (int i = 0; i < s.Length; i++)
                {
                    if (IsChineseLetter(s, i))
                    {
                        nByteLeft += 2;
                        spad = "  ";
                    }
                    else
                    {
                        nByteLeft++;
                        spad = " ";
                    }
                    if (nByteLeft >= n)
                    {
                        nLeft = i;
                        break;
                    }
                }
                s1 = s.Substring(0, nLeft) + spad;
                cut_str = s.Substring(nLeft, s.Length - nLeft);

            }
            return s1;

        }
       
        public static void OrderByOperTime(List<CSheetSale> lstSheets)
        {
            lstSheets.Sort
            (
                delegate(CSheetSale x, CSheetSale y)
                {
                    DateTime dtx = Convert.ToDateTime("2000-1-1");
                    DateTime dty = Convert.ToDateTime("2000-1-1");
                    if (CPubVars.IsDate(x.m_oper_date))
                        dtx = Convert.ToDateTime(x.m_oper_date);
                    if (CPubVars.IsDate(y.m_oper_date))
                        dty = Convert.ToDateTime(y.m_oper_date);
                    return dtx.CompareTo(dty);
                }
             );
        }

        public string ToXML(CMySbCommand cmd)
        {
            string sheetHead = "SaleSheet";
             
            string sXML = "<" + sheetHead +">";
            #region 获取总体信息
            CSheetSale sheet = this;
            CDbDealer dbDeal = new CDbDealer();

            //NameValueCollection flds = new NameValueCollection();
            //flds.Add("sheet_no", sID);

            dbDeal.AddField("sheet_no", sheet.m_sheet_no);
            dbDeal.AddField("branch_no", sheet.m_branch_no);
            if (sheet.m_com_no != "")
            {
                dbDeal.AddField("com_no", sheet.m_com_no);
                dbDeal.AddField("com_name", sheet.m_com_name);
            }
            if (sheet.m_remote_com_no != "")
            {
                dbDeal.AddField("remote_com_no", sheet.m_remote_com_no);
                dbDeal.AddField("remote_com_name", sheet.m_remote_com_name);
            }
            dbDeal.AddField("supcust_no", sheet.m_supcust_no);
            dbDeal.AddField("cust_name", sheet.m_supcust_name);
            dbDeal.AddField("pay_type", "1", "number");
            dbDeal.AddField("discount", sheet.m_discount.ToString(), "number");
            dbDeal.AddField("coin_no", sheet.m_coin_no);
            //string pay_type = "1";
            int money_inout_flag = -1;
            if (sheet.m_SheetType == SHEET_TYPE.SHEET_BUY)
                money_inout_flag = -1;
            else if (sheet.m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN)
                money_inout_flag = 1;
            else if (sheet.m_SheetType == SHEET_TYPE.SHEET_SALE)
                money_inout_flag = 1;
            else if (sheet.m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                money_inout_flag = -1;
            dbDeal.AddField("money_inout_flag", money_inout_flag.ToString(), "number");
            dbDeal.AddField("total_amount", sheet.m_total_amount.ToString(), "number");
            dbDeal.AddField("inout_amount", sheet.m_total_amount.ToString(), "number");
            dbDeal.AddField("paid_amount", sheet.m_paid_amount.ToString(), "number");
            dbDeal.AddField("disc_amount", sheet.m_disc_amt.ToString(), "number");

            dbDeal.AddField("cash_amount", sheet.m_pay_cash_amount.ToString(), "number");
            dbDeal.AddField("card_amount", sheet.m_pay_card_amount.ToString(), "number");
           // dbDeal.AddField("ticket_amount", sheet.m_pay_ticket_amount.ToString(), "number");
            dbDeal.AddField("bank_amount", sheet.m_pay_bank_amount.ToString(), "number");
            dbDeal.AddField("integral_amount", sheet.m_pay_integral_amount.ToString(), "number");
            dbDeal.AddField("reduce_integral", sheet.m_pay_reduce_integral.ToString(), "number");
            dbDeal.AddField("vip_card", sheet.m_pay_card_id);
            dbDeal.AddField("payway1_name", sheet.m_payway1_name);
            dbDeal.AddField("payway1_amount", sheet.m_payway1_amount.ToString());
            dbDeal.AddField("payway2_name", sheet.m_payway2_name);
            dbDeal.AddField("payway2_amount", sheet.m_payway2_amount.ToString());
            dbDeal.AddField("payway3_name", sheet.m_payway3_name);
            dbDeal.AddField("payway3_amount", sheet.m_payway3_amount.ToString());

            if (sheet.m_bApproved)
                dbDeal.AddField("approve_flag", "1");
            else
                dbDeal.AddField("approve_flag", "0");

            dbDeal.AddField("oper_id", sheet.m_oper_id);
            dbDeal.AddField("oper_name", sheet.m_oper_name);
            dbDeal.AddField("order_man", sheet.m_work_man);

            dbDeal.AddField("oper_date", CPubVars.GetDateText(sheet.m_oper_date), "date");
            dbDeal.AddField("work_date", CPubVars.GetDateText(sheet.m_work_date), "date");
            string inout_flag = "";
            if (sheet.m_SheetType == SHEET_TYPE.SHEET_SALE)
            {
                dbDeal.AddField("db_no", "-");
                dbDeal.AddField("trans_no", "S");
                inout_flag = "-1";
            }
            else if (sheet.m_SheetType == SHEET_TYPE.SHEET_BUY)
            {
                dbDeal.AddField("db_no", "+");
                dbDeal.AddField("trans_no", "A");
                inout_flag = "1";
            }
            else if (sheet.m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN)
            {
                dbDeal.AddField("db_no", "-");
                dbDeal.AddField("trans_no", "F");
                inout_flag = "-1";
            }
            else if (sheet.m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
            {
                dbDeal.AddField("db_no", "+");
                dbDeal.AddField("trans_no", "D");
                inout_flag = "1";
            }

            dbDeal.AddField("now_pay_amount", sheet.m_paid_amount.ToString(), "number");
            dbDeal.AddField("duty_no", sheet.m_duty_no);
            dbDeal.AddField("brief", sheet.m_brief);
          
            sXML += "<General ";
            sXML += dbDeal.GetXmlRecord();
            sXML += " />";



            #endregion

            #region 获取详单信息

            // cmd.CommandText = "delete from " + m_tb_detail + " where sheet_no='" + sheet_no + "'";
            // cmd.ExecuteNonQuery();
            // rda.SubmitSql(sCmd, connStr);

            string sRecordsXML = "<Items>";
            foreach (var sheetRow in sheet.SheetRows)
            { 
                dbDeal = new CDbDealer();
                dbDeal.AddField("item_no", sheetRow.item_no.Trim());
                dbDeal.AddField("unit_no", sheetRow.unit_no.ToString());
                dbDeal.AddField("unit_factor", sheetRow.unit_factor.ToString());
                dbDeal.AddField("item_subno", sheetRow.item_subno);
                dbDeal.AddField("orig_price", sheetRow.orig_price.ToString());
                dbDeal.AddField("real_price", sheetRow.real_price.ToString());
                dbDeal.AddField("quantity", sheetRow.quantity.ToString());
                dbDeal.AddField("color", sheetRow.color_id);
                if (sheetRow.color_id != "0")
                {
                    cmd.CommandText = "select color_name from info_item_color where color_id='" + sheetRow.color_id + "'";
                    object ov = cmd.ExecuteScalar();
                    if (ov != null && ov != DBNull.Value)
                    {
                        dbDeal.AddField("color_name", ov.ToString());
                    }
                }
                dbDeal.AddField("size", sheetRow.size_id.ToString());
                dbDeal.AddField("sub_amount", sheetRow.money.ToString());
                dbDeal.AddField("inout_flag", inout_flag, "number");
                dbDeal.AddField("brief", sheetRow.brief);

                string sRowXML = "<row ";
                sRowXML += dbDeal.GetXmlRecord();
                sRowXML += " />";
                sRecordsXML += sRowXML;

            }
            sRecordsXML += "</Items>";
            #endregion
            sXML += sRecordsXML;
            sXML += "</" + sheetHead + ">";
            return sXML;
        }
        public static CSheetSale fromXML(string sXML, ref string sErr, CMySbCommand cmd)
        {
            CSheetSale sheet = new CSheetSale();
            string sNow = CPubVars.GetDateText(DateTime.Now);
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(sXML);

            if (doc.ChildNodes.Count > 0)
            {
                return fromXMLNode(doc.ChildNodes[0], ref sErr, cmd);
            }
            return null;
        }
        public static CSheetSale fromXMLNode(XmlNode SheetNode, ref string sErr, CMySbCommand cmd)
        {
            CSheetSale sheet = new CSheetSale();
            string oper_id = ""; string oper_name = ""; string oper_pwd = ""; string branch_no = ""; string branch_name = ""; string com_no = ""; string com_name = ""; string supcust_no = ""; string cust_name = ""; string work_man = ""; string pay_way = ""; string total_amount = ""; string paid_amount = ""; string disc_amt = ""; string oper_date = ""; string work_date = ""; string brief = "";// string items = "";
            double cash_amount = 0; double card_amount = 0; double ticket_amount = 0; double bank_amount = 0; double integral_amount = 0;
            string payway1_name = "", payway2_name = "", payway3_name = "";
            double payway1_amount = 0, payway2_amount = 0, payway3_amount = 0;

            int reduce_integral = 0; string vip_card = ""; string bank_box = ""; string cash_box = "";
            string duty_no = "";
            SHEET_TYPE sheetType = SHEET_TYPE.SHEET_SALE;
            bool bApprove = false;

            string sNow = CPubVars.GetDateText(DateTime.Now);

            string pdaSheetNo = "";


            foreach (XmlNode node in SheetNode.ChildNodes)
            {
                if (node.Name == "General")
                {
                    foreach (XmlAttribute attr in node.Attributes)
                    {
                        if (attr.Value == "null")
                            attr.Value = "";
                        if (attr.Name == "trans_no")
                        {
                            if (attr.Value.Trim() == "S")
                            {
                                sheetType = SHEET_TYPE.SHEET_SALE;
                                sheet.m_SheetType = sheetType;
                            }
                            else if (attr.Value.Trim() == "D")
                            {
                                sheetType = SHEET_TYPE.SHEET_SALE_RETURN;
                                sheet.m_SheetType = sheetType;
                            }
                            else if (attr.Value.Trim() == "A")
                            {
                                sheetType = SHEET_TYPE.SHEET_BUY;
                                sheet.m_SheetType = sheetType;
                            }
                            else if (attr.Value.Trim() == "F")
                            {
                                sheetType = SHEET_TYPE.SHEET_BUY_RETURN;
                                sheet.m_SheetType = sheetType;
                            }
                        }
                        else if (attr.Name == "sheet_no")
                        {
                            pdaSheetNo = attr.Value;
                            sheet.m_sheet_no = attr.Value;
                            sheet.m_bPDASheet = true;
                        }
                        else if (attr.Name == "approve_flag")
                        {
                            bApprove = (attr.Value == "1");
                        }
                        else if (attr.Name == "oper_id")
                        {
                            oper_id = attr.Value;
                        }
                        else if (attr.Name == "oper_name")
                        {
                            oper_name = attr.Value;
                        }
                        else if (attr.Name == "oper_pwd")
                        {
                            oper_pwd = attr.Value;
                        }
                        else if (attr.Name == "branch_no")
                        {
                            branch_no = attr.Value;
                        }
                        else if (attr.Name == "branch_name")
                        {
                            branch_name = attr.Value;
                        }
                        else if (attr.Name == "remote_com_no")
                        {
                            sheet.m_remote_com_no = attr.Value;                            
                        }
                        else if (attr.Name == "remote_com_name")
                        {
                            sheet.m_remote_com_name = attr.Value;
                        }    
                        else if (attr.Name == "com_no")
                        {
                            com_no = attr.Value;
                        }
                        else if (attr.Name == "com_name")
                        {
                            com_name = attr.Value;
                        }
                        else if (attr.Name == "supcust_no")
                        {
                            supcust_no = attr.Value;
                        }
                        else if (attr.Name == "cust_name")
                        {
                            cust_name = attr.Value;
                        }
                        else if (attr.Name == "pay_way")
                        {
                            pay_way = attr.Value;
                        }
                        else if (attr.Name == "total_amount")
                        {
                            total_amount = attr.Value;
                        }
                        else if (attr.Name == "paid_amount")
                        {
                            paid_amount = attr.Value;
                        }
                        else if (attr.Name == "disc_amount")
                        {
                            disc_amt = attr.Value;
                        }
                        else if (attr.Name == "payway1_name")
                        {
                            payway1_name = attr.Value.ToString();
                        }
                        else if (attr.Name == "payway1_amount")
                        {
                            payway1_amount = Convert.ToDouble(attr.Value);
                        }
                        else if (attr.Name == "payway2_name")
                        {
                            payway2_name = attr.Value.ToString();
                        }
                        else if (attr.Name == "payway2_amount")
                        {
                            payway2_amount = Convert.ToDouble(attr.Value);
                        }
                        else if (attr.Name == "payway3_name")
                        {
                            payway3_name = attr.Value.ToString();
                        }
                        else if (attr.Name == "payway3_amount")
                        {
                            payway3_amount = Convert.ToDouble(attr.Value);
                        }
                        else if (attr.Name == "cash_amount")
                        {
                            cash_amount = Convert.ToDouble(attr.Value);
                        }
                        else if (attr.Name == "card_amount")
                        {
                            card_amount = Convert.ToDouble(attr.Value);
                        }
                        else if (attr.Name == "ticket_amount")
                        {
                            ticket_amount = Convert.ToDouble(attr.Value);
                        }
                        else if (attr.Name == "bank_amount")
                        {
                            bank_amount = Convert.ToDouble(attr.Value);
                        }
                        else if (attr.Name == "bank_box")
                        {
                            bank_box = attr.Value;
                        }
                        else if (attr.Name == "cash_box")
                        {
                            cash_box = attr.Value;
                        }
                        else if (attr.Name == "integral_amount")
                        {
                            integral_amount = Convert.ToDouble(attr.Value);
                        }
                        else if (attr.Name == "reduce_integral")
                        {
                            reduce_integral = Convert.ToInt32(attr.Value);
                        }
                        else if (attr.Name == "vip_card")
                        {
                            vip_card = attr.Value;
                        }
                        else if (attr.Name == "oper_id")
                        {
                            oper_id = attr.Value;
                        }
                        else if (attr.Name == "work_man")
                        {
                            work_man = attr.Value;
                        }
                        else if (attr.Name == "work_date")
                        {
                            work_date = attr.Value;
                        }
                        else if (attr.Name == "oper_date")
                        {
                            oper_date = attr.Value;
                        }
                        else if (attr.Name == "duty_no")
                        {
                            duty_no = attr.Value;
                        }
                        else if (attr.Name == "brief")
                        {
                            brief = attr.Value;
                        }

                    }
                    if (supcust_no != "" && supcust_no.Substring(0, 1) == "-")
                    {
                        string new_supcust_no = "";
                        if (cust_name != "")
                        {
                            cmd.CommandText = "select supcust_no from info_supcust where sup_name='" + cust_name + "'";
                            object ov = cmd.ExecuteScalar();
                            if (ov != null && ov != DBNull.Value)
                                new_supcust_no = ov.ToString().Trim();
                        }
                        if (new_supcust_no != "")
                            supcust_no = new_supcust_no;
                    }

                    if (oper_id != "")
                    {
                        cmd.CommandText = "select com_no from info_operator where oper_id='" + oper_id + "'";
                        object ov_oper = cmd.ExecuteScalar();
                        if (ov_oper != null && ov_oper != DBNull.Value)
                        {
                            sheet.m_com_no = ov_oper.ToString().Trim();
                        }
                    }

                    sheet.m_duty_no = duty_no;
                    sheet.m_payway1_name =  payway1_name; sheet.m_payway1_amount =  payway1_amount;
                    sheet.m_payway2_name =  payway2_name; sheet.m_payway2_amount =  payway2_amount;
                    sheet.m_payway3_name =  payway3_name; sheet.m_payway3_amount =  payway3_amount;
                    sheet.SetPayInfo(cash_amount, card_amount, ticket_amount, bank_amount,integral_amount, reduce_integral, vip_card, bank_box, cash_box, 0, 0, 0);
                    sErr = sheet.SetGeneralInfo(branch_no, "", supcust_no, "", oper_id,oper_name, work_man, "", pay_way, "", "1", total_amount, "0", paid_amount, disc_amt, oper_date, work_date, brief, false);

                    if (sErr != "")
                        goto exit_fun;
                }
                else if (node.Name == "Items")
                {
                    string item_no = ""; string unit_no = ""; string unit_factor = "1"; string quantity = ""; string real_price = ""; string orig_price = ""; string sub_amount = "";
                    string color = "0"; string color_name = ""; string size = "0"; string item_subno = ""; string row_brief = ""; string row_branch = ""; string row_worker = ""; string row_servicer = ""; string row_servicer1 = ""; bool row_is_service = false;

                    foreach (XmlNode row in node.ChildNodes)
                    {
                        foreach (XmlAttribute attr in row.Attributes)
                        {
                            if (attr.Value == "null")
                                attr.Value = "";
                            if (attr.Name == "item_no")
                            {
                                item_no = attr.Value;
                            }
                            else if (attr.Name == "unit_no")
                            {
                                unit_no = attr.Value;
                            }
                            else if (attr.Name == "unit_factor")
                            {
                                unit_factor = attr.Value;
                            }
                            else if (attr.Name == "item_subno")
                            {
                                item_subno = attr.Value;
                            }
                            else if (attr.Name == "real_price")
                            {
                                real_price = attr.Value;
                            }
                            else if (attr.Name == "orig_price")
                            {
                                orig_price = attr.Value;
                            }
                            else if (attr.Name == "quantity")
                            {
                                quantity = attr.Value;
                            }
                            else if (attr.Name == "color")
                            {
                                color = attr.Value;
                            }
                            else if (attr.Name == "color_name")
                            {
                                color_name = attr.Value;
                            }
                            else if (attr.Name == "size")
                            {
                                size = attr.Value;
                            }
                            else if (attr.Name == "sub_amount")
                            {
                                sub_amount = attr.Value;
                            }
                            else if (attr.Name == "brief")
                            {
                                row_brief = attr.Value;
                            }
                            else if (attr.Name == "branch_no")
                            {
                                row_branch = attr.Value;
                            }
                            else if (attr.Name == "worker")
                            {
                                row_worker = attr.Value;
                            }
                            else if (attr.Name == "servicer")
                            {
                                row_servicer = attr.Value;
                            }
                            else if (attr.Name == "servicer1")
                            {
                                row_servicer1 = attr.Value;
                            }
                            else if (attr.Name == "service")
                            {
                                row_is_service = (attr.Value == "1");
                            }

                        }
                      
                        //sErr = sheet.AddRow(item_no, "", unit_no, unit_factor, size, "", color, "", orig_price, real_price, quantity, sub_amount, 1, "", "", false);
                        sErr = sheet.AddRow(item_no, "", item_subno, unit_no, unit_factor, size, "", color, "", orig_price, real_price, quantity, "0", sub_amount, 1, "", "", row_is_service, "", "", "", row_brief);

                        if (sErr != "")
                            goto exit_fun;
                    }
                }
            }
            if (com_no != "")
            {
                sheet.m_com_no = com_no;
                sheet.m_com_name = com_name;
            }
            return sheet;

        exit_fun:
            return null;
        }

        public static string GetSheetTypeTextFromValue(SHEET_TYPE sheet_type)
        {
            switch (sheet_type)
            { 
                case SHEET_TYPE.SHEET_SALE:
                    return "销售";
                case SHEET_TYPE.SHEET_SALE_RETURN:
                    return "退货";
            }
            return "";
        }
        public void OrderRowsByClass(bool bSmallBigClass, List<SheetRow> sheetRows)
        { 
            foreach (var row in sheetRows)
            { 
                if (row.clsno4 == "AllClass" || row.clsno4 == "AllClass_Service")
                {
                    if (bSmallBigClass)
                    {
                        row.ClassID = row.clsno2;
                        row.ClassName = row.clsname2;
                        row.ClassIndex = row.clsindex2;
                    }
                    else
                    {
                        row.ClassID = row.clsno3;
                        row.ClassName = row.clsname3;
                        row.ClassIndex = row.clsindex3;
                    }
                }
                else if (row.clsno3 == "AllClass" || row.clsno3 == "AllClass_Service")
                {
                    if (bSmallBigClass)
                    {
                        row.ClassID = row.clsno1;
                        row.ClassName = row.clsname1;
                        row.ClassIndex = row.clsindex1;
                    }
                    else
                    {
                        row.ClassID = row.clsno2;
                        row.ClassName = row.clsname2;
                        row.ClassIndex = row.clsindex2;
                    }
                }
                else if (row.clsno2 == "AllClass" || row.clsno2 == "AllClass_Service")
                {
                    row.ClassID = row.clsno1;
                    row.ClassName = row.clsname1;
                    row.ClassIndex = row.clsindex1;
                } 
            }
            SheetRow.OrderByClass_Name(sheetRows);

            // cmd.CommandText="select item_clsno,item_no from info_item_prop left join info_item_class on info_item_prop.item_clsno=info_item_class.item_clsno left join 

        }
        public void OrderRowsByRptClass(List<SheetRow> sheetRows)
        { 
            foreach (var row in sheetRows)
            { 
                row.ClassName = row.rpt_class;
                row.ClassIndex = row.rpt_clsindex; 
            }
            SheetRow.OrderByClass_Name(sheetRows);
            
        }

        public void OrderRowsBySpecClassOrder(XmlNode node, List< SheetRow> sheetRows)
        {
            OrderRowsByClass(false, sheetRows);
            if (node == null)
                return;
            Dictionary<string, SheetRow> dicNewRows = new Dictionary<string, SheetRow>();
            List<SheetRow> lstShowClass = new List<SheetRow>();
            try
            {
                foreach (XmlNode node1 in node.ChildNodes)
                {
                    string name = node1.Attributes["name"].Value;
                    string son_clsno = node1.Attributes["SonClsNo"].Value;
                    string son_clsname = node1.Attributes["SonClsName"].Value;
                    SheetRow newrow = new SheetRow();
                    newrow.show_class_name = name;
                    newrow.item_no = son_clsno;
                    newrow.item_name = son_clsname;
                    lstShowClass.Add(newrow);
                }
            }
            catch (Exception)
            {
            }

            foreach (SheetRow showclass in lstShowClass)
            {
                foreach (var row in sheetRows)
                { 
                    if (("," + showclass.item_name + ",").Contains("," + row.ClassName + ","))
                    {
                        row.show_class_name = showclass.show_class_name;
                        row.ClassName = showclass.show_class_name;
                        row.other4 = "1";
                        dicNewRows.Add((dicNewRows.Count + 1).ToString(), row);
                    }
                }
            }
            if (m_not_show_other_class != "1")
            {
                foreach (var row in sheetRows)
                {
                  
                    if (row.other4 != "1")
                    {
                        row.show_class_name = "其他类";
                        row.ClassName = "其他类";
                        dicNewRows.Add((dicNewRows.Count + 1).ToString(), row);
                    }
                }
            }
          
        }
    } 
}

