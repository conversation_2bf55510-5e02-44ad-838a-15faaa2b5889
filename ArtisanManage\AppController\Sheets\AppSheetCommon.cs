﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using ArtisanManage.Services.SheetService;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.AppController.Sheets
{ 
    [Route("AppApi/[controller]/[action]")]
    public class AppSheetCommon : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public AppSheetCommon(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }
 
        [HttpPost]
        public async Task<IActionResult> AppendSheetBrief([FromBody] dynamic data)
        {
            string sheetID = data.sheetID;
            string sheetType = data.sheetType;
            string newBrief = data.newBrief;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string sheet_tb = "";
            switch (sheetType)
            {
                case "X":
                case "T": 
                    sheet_tb = "sheet_sale_main";break;
                case "XD":
                case "TD":
                    sheet_tb = "sheet_sale_order_main"; break;
                case "CG":
                case "CT":
                    sheet_tb = "sheet_buy_main"; break;
                case "CD":
                case "CTD":
                    sheet_tb = "sheet_buy_order_main"; break;
                case "DB":
                    sheet_tb = "sheet_move_main"; break;
                case "PD":
                    sheet_tb = "sheet_inventory_main"; break;
                case "SS":
                    sheet_tb = "sheet_client_stock_main"; break;
                case "BS":
                case "YK":
                    sheet_tb = "sheet_invent_change_main"; break;
                case "SK":
                case "FK":
                    sheet_tb = "sheet_get_arrears_main"; break;
                case "YS":
                case "YF":
                    sheet_tb = "sheet_prepay_main"; break;
                case "ZC":
                case "SR":
                    sheet_tb = "sheet_fee_out_main"; break;

            }
            string msg = "";
            if (sheet_tb != "")
            {
                cmd.CommandText = $"update {sheet_tb} set make_brief=coalesce(make_brief||' ','') || {newBrief} where company_id={companyID} and sheet_id={sheetID};";
                await cmd.ExecuteNonQueryAsync();
            }
            else
            {
                msg = "没有定义对应单据";
            }
            
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, });
        }
    }
}
