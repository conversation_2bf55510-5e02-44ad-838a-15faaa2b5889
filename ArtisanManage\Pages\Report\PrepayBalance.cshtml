@page
@model ArtisanManage.Pages.BaseInfo.PrepayBalanceModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
           
    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {

                    var args = event.args;
                    var supcust_id = args.row.bounddata.supcust_id;

                    var sup_name = args.row.bounddata.sup_name;

                    var startDay = $('#startDay').jqxDateTimeInput('val');
                    var endDay = $('#endDay').jqxDateTimeInput('val');


                    var addORreduce = args.datafield.split('_');

                    var subColumns=window.GridData.columns
                    for ( i in subColumns) {
                        if (subColumns[i].datafield == args.datafield) {
                            var bb  = subColumns[i].columngroup
                            var cc = bb.split('_')
                            var sub_name=cc[cc.length-1]
                            break
                        }
                    }

                    var url = ""
                    var title = ""
                    if (args.datafield != "pw_add_all" && args.datafield != "pw_reduce_all")
                    {
                        if (addORreduce[1] == "add" && sup_name) {
                            url = `Report/SupplierAccountHistory?supcust_id=${supcust_id}&sup_name=${sup_name}&startDay=${startDay}&endDay=${endDay}&sub_type=YF&sub_type_name=预付款&sub_id=${addORreduce[2]}&sub_name=${sub_name}&sheet_type=YF,CT&sheet_type_name=预付款单,采购退货`;
                            title = "供应商往来账"
                            window.parent.newTabPage(title, `${url}`);
                        }
                        if (addORreduce[1] == "reduce" && sup_name) {
                            url = `Report/SupplierAccountHistory?supcust_id=${supcust_id}&sup_name=${sup_name}&startDay=${startDay}&endDay=${endDay}&sub_type=YF&sub_type_name=预付款&sub_id=${addORreduce[2]}&sub_name=${sub_name}&sheet_type=FK,CG&sheet_type_name=付款单,采购单`;

                            title = "供应商往来账"
                            window.parent.newTabPage(title, `${url}`);
                        }
                    }


                });
                QueryData();
    	    });
    </script>
</head>

<body> 
    <div style="display:flex;padding-top:20px;">

        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>
     
    <div id="gridItems"></div>  
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div> 
        

  

</body>
</html>