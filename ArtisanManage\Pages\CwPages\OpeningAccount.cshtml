﻿@page
@model ArtisanManage.Pages.CwPages.OpeningAccountModel
@{
    Layout = null;
}
<!DOCTYPE html>

<html>

<head>
    <meta name="viewport" content="width=device-width" />
    <title>OpeningAccount</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>

    <style>
        * {
            font-family: "微软雅黑";
        }

        [v-cloak] {
            display: none;
        }

        #pages {
            width: 100%;
            height: 100vh;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
        }

        .pages_main{
            width:1005px;
            height:80%;
            padding-top:100px;
            padding-left:150px;
        }

        .pages_input{
            width:250px !important;
            height:40px;
        }

        .pages_codeSelect {
            width:80px !important;
            text-align:center;
            margin-right:5px;
        }

        .form-item{
            width:500px;
            height:500px;
            margin-bottom:20px;
        }

        .form-item div{
            float:left;
            margin-right:20px;
        }

        .form-item:first-child{
            vertical-align:central;
        }

        .form_left {
            width:600px;
            height:400px;
            display:inline-block;
            float:left;
            position: relative;
        }

        .form_right {
            width: 400px;
            height: 400px;
            display: inline-block;
            float:right;
        }

        .form_right .el-checkbox{
            width:100%;
            display:inline-block;
        }

        .manual_a{
            margin-right:20px;
            margin-top:20px;
            display:block;
        }
    </style>
</head>

<body>
    <div id="root" v-cloak>
        <div id="pages">
            <div class="pages_main" name="account" style="display:block">
                <el-form ref="form" :model="form" label-width="80px" class="form_left">
                    <el-form-item label="公司名称">
                        <el-input v-model="form.company" placeholder="请在公司设置中配置公司名称" class="pages_input" disabled="disabled"></el-input>
                    </el-form-item>
                    <el-form-item label="开账时间">
                        <el-date-picker v-model="form.openAccountPeriod" type="month" placeholder="选择月" class="pages_input" value-format="yyyy-MM" :disabled="form.useAccounting" :editable="false" :clearable="false" @@change="onPeriodPick" :picker-options="pickerOptions"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="期间">
                        <el-input v-model="form.cwPeriodFromTo" class="pages_input" disabled="disabled"></el-input>
                    </el-form-item>
                    <el-form-item label="会计准则">
                        <el-select v-model="form.rule" placeholder="请选择" class="pages_input" ref="rule" :disabled="form.useAccounting">
                            <el-option v-for="item in form.rules"
                                       :key="item.value"
                                       :label="item.label"
                                       :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="编码长度">
                        <el-select v-model="form.accountingCodeLength[0]" :disabled="true" class="pages_codeSelect">
                            <el-option label="4" value="4"></el-option>
                        </el-select>-
                        <el-select v-model="form.accountingCodeLength[1]" class="pages_codeSelect" :disabled="true"><!--后四位暂时设为不允许修改-->
                            <el-option label="2" :value="2"></el-option>
                            <el-option label="3" :value="3"></el-option>
                            <el-option label="4" :value="4"></el-option>
                        </el-select>-
                        <el-select v-model="form.accountingCodeLength[2]" class="pages_codeSelect" :disabled="true">
                            <el-option label="2" :value="2"></el-option>
                            <el-option label="3" :value="3"></el-option>
                            <el-option label="4" :value="4"></el-option>
                        </el-select>-
                        <el-select v-model="form.accountingCodeLength[3]" class="pages_codeSelect" :disabled="true">
                            <el-option label="2" :value="2"></el-option>
                            <el-option label="3" :value="3"></el-option>
                            <el-option label="4" :value="4"></el-option>
                        </el-select>-
                        <el-select v-model="form.accountingCodeLength[4]" class="pages_codeSelect" :disabled="true">
                            <el-option label="2" :value="2"></el-option>
                            <el-option label="3" :value="3"></el-option>
                            <el-option label="4" :value="4"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item style="position:absolute;bottom:0;left:0;">
                        <el-button type="primary" @@click="openAccount" :disabled="disabledOperate">开账</el-button>
                        <el-button type="primary" plain @@click="saveAccount" :disabled="disabledOperate">保存</el-button>
                        <el-button type="danger" plain @@click="clearAccount" :disabled="disabledOperate">反开账</el-button>
                    </el-form-item>
                </el-form>

                <el-form ref="form" :model="form" label-width="80px" class="form_right">
                    <el-form-item label="选项">
                        <el-checkbox label="凭证审核后才允许结账" v-model="form.isApprove" disabled></el-checkbox>
                        <el-checkbox label="业务单据自动创建凭证" v-model="form.autoCreateVoucher" :disabled="disabledOperate" title="业务单据审核自动生成凭证"></el-checkbox>
                        <el-checkbox label="业务单据转凭证自动审核" v-model="form.autoApproveVoucher" :disabled="disabledOperate" title="不勾选则凭证默认保存未审核"></el-checkbox>
                        <el-checkbox label="销售收入成本同时生成" v-model="form.saleIncomeAndCost" :disabled="disabledOperate" title="不勾选则月末一次结转成本"></el-checkbox>
                        @*<el-checkbox label="财务反结账/反开账期间与业务同步" v-model="form.cwBizPeriodSame" :disabled="disabledOperate"></el-checkbox>*@
                    </el-form-item>
                </el-form>
                <a :href="maunalHref" target="_blank" class="manual_a">财务使用手册</a>
            </div>
        </div>
    </div>
    

    <script>
        var g_operKey = '@Model.OperKey';

        window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);
        function checkOperRight(vm) {
            if (window.g_operRights.cwOperate){
                if (window.g_operRights.cwOperate.openingAccount) {
                    if (!window.g_operRights.cwOperate.openingAccount.operate) {
                        vm.disabledOperate = true;
                    }
                    return true;
                } else {
                    return false;
                }
            }else{
                return false;
            }
        }
    </script>
    <script>
        var vm=new Vue({
            el:'#root',
            data(){
                return{
                    form:{
                        company:'',
                        useAccounting: false,
                        openAccountPeriod: '',
                        cwPeriodFromTo:'',
                        accountingCodeLength: [4, 2, 2, 2, 2],
                        rule: '1',
                        rules: [{ value: '1', label: '小企业会计准则（2013年颁）' }],
                        isApprove: true,
                        autoCreateVoucher: false,
                        autoApproveVoucher:false,
                        saleIncomeAndCost: false
                        //cwBizPeriodSame:false,
                    },
                    maunalHref: '/Document/cw/UserManual/index.html?'+g_operKey,
                    disabledOperate: false,
                    pickerOptions: {
                        disabledDate(time) {
                            return null;
                        }
                    },
                }
            },
            computed:{
                currentMonth(){
                    let now=new Date();
                    let year=now.getFullYear();
                    let month=now.getMonth()+1;
                    month=month.toString().length==1?'0'+month:month;
                    return year+'-'+month;
                }
            },
            created(){
                //获取当前月
                this.form.openAccountPeriod = this.currentMonth;

                //获取公司名等信息
                this.getDataFromDB();
                if (this.periodFromTo == '' || this.periodFromTo==null) {
                    this.onPeriodPick();
                }

                if (!window.checkOperRight(this)) {
                    this.disabledOperate=true;
                    return;
                }
            },
            mounted(){

            },
            methods:{
                getDataFromDB() {
                    $.ajax({
                        url: '/api/OpeningAccount/GetAccount',
                        type: 'get',
                        data: { operKey: g_operKey },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                        async: false,
                        error: (xhr) => {
                            this.$message({ type: 'error', message: '网络错误' });
                        }
                    }).then(res=>{
                        console.log('open account get');
                        if (res.result === 'OK') {
                            this.form.company = res.companyName;
                            if (res.useAccounting) {
                                this.form.useAccounting = res.useAccounting;
                                this.form.openAccountPeriod = res.openAccountPeriod;
                                this.form.cwPeriodFromTo = res.cwPeriodFromTo;
                                this.form.accountingCodeLength=res.accountingCodeLength;
                                this.form.autoCreateVoucher = res.autoCreateVoucher;
                                this.form.autoApproveVoucher = res.autoApproveVoucher;
                                this.form.saleIncomeAndCost = res.saleIncomeAndCost;
                                //this.form.cwBizPeriodSame = res.cwBizPeriodSame;
                            }
                            if (res.monthRangeFrom) {
                                this.pickerOptions = {
                                    disabledDate(time) {
                                        return time.getTime() < new Date(`${res.monthRangeFrom} 00:00:00`);
                                    }
                                };
                            }
                        }
                    });
                },
                onPeriodPick() {
                    let _this = this;
                    $.ajax({
                        url: '/api/OpeningAccount/GetCwPeriodFromTo',
                        type: 'get',
                        data: { operKey: g_operKey, openAccountPeriod: _this.form.openAccountPeriod },
                        contentType: "application/json;charset=UTF-8",
                        error: (xhr) => {
                            this.$message({ type: 'error', message: '网络错误' });
                        }
                    }).then(res => {
                        if (res.result == "OK") {
                            console.log('get open cw period from to date');
                            _this.form.cwPeriodFromTo = res.cwPeriodFromTo;
                        } else {
                            this.$message({ type: 'error', message: res.msg });
                        }
                    });
                },
                openAccount(){
                    if (this.form.useAccounting) {
                        this.$alert('当前财务功能已开启，请勿重复开账！', '提示', {
                            confirmButtonText: '确定'
                        });
                        return;
                    }else{
                        this.$confirm(`确认以${this.form.openAccountPeriod}为初始会计期间开账？`, '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            const loading = this.$loading({
                                lock: true,
                                text: '正在处理，请稍候...',
                                spinner: 'el-icon-loading',
                                background: 'rgba(255, 255, 255, 0.7)'
                            });
                            let _this = this;
                            $.ajax({
                                url: '/api/OpeningAccount/SaveAccount?operKey=' + g_operKey,
                                type: 'post',
                                data: JSON.stringify({
                                    operKey: g_operKey,
                                    useAccounting: true,
                                    companyName: _this.form.company,
                                    openAccountPeriod: _this.form.openAccountPeriod,
                                    accountingCodeLength: _this.form.accountingCodeLength,
                                    autoCreateVoucher: _this.form.autoCreateVoucher,
                                    autoApproveVoucher: _this.form.autoApproveVoucher,
                                    companyName: _this.form.company,
                                    saleIncomeAndCost: _this.form.saleIncomeAndCost
                                    //cwBizPeriodSame: _this.form.cwBizPeriodSame
                                    //cwPeriodFromTo不从前端传入，通过后端重查保存
                                }),
                                contentType: "application/json;charset=UTF-8",
                                error: (xhr) => {
                                    loading.close();
                                    this.$message({ type: 'error', message: '网络错误' });
                                }
                            }).then(res => {
                                loading.close();
                                if(res.result=="OK"){
                                    console.log('open account');
                                    _this.form.useAccounting = true;
                                    this.$alert('开账成功！', '提示', {
                                        confirmButtonText: '确定'
                                    });
                                }else{
                                    this.$message({
                                        type: 'error',
                                        message: res.msg
                                    });
                                }
                            });
                        });
                        
                    }
                },
                
                saveAccount(){
                    if (!this.form.useAccounting){
                        this.$alert('财务功能未开启，请先开账！', '提示', {
                            confirmButtonText: '确定'
                        });
                        return;
                    }else{
                        const loading = this.$loading({
                            lock: true,
                            text: '正在保存...',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });
                        let _this = this;
                        $.ajax({
                            url: '/api/OpeningAccount/SaveAccount?operKey=' + g_operKey,
                            type: 'post',
                            data: JSON.stringify({
                                operKey: g_operKey,
                                useAccounting: _this.form.useAccounting,
                                openAccountPeriod: _this.form.openAccountPeriod,
                                accountingCodeLength: _this.form.accountingCodeLength,
                                autoCreateVoucher: _this.form.autoCreateVoucher,
                                autoApproveVoucher: _this.form.autoApproveVoucher,
                                companyName:_this.form.company,
                                saleIncomeAndCost:_this.form.saleIncomeAndCost
                                //cwBizPeriodSame: _this.form.cwBizPeriodSame
                            }),
                            contentType: "application/json;charset=UTF-8",
                            success: (res) => {
                                loading.close();
                                if(res.result=='OK'){
                                    console.log('save account');
                                    _this.$alert('保存成功！', '提示', {
                                        confirmButtonText: '确定'
                                    });
                                }else{
                                    _this.$message({ type: 'error', message: res.msg });
                                }
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                loading.close();
                                _this.$message({ type: 'error', message: '网络错误' });
                            }
                        });
                    }
                },

                clearAccount(){
                    if (!this.form.useAccounting){
                        this.$alert('财务功能未开启，请先开账！', '提示', {
                            confirmButtonText: '确定'
                        });
                        return;
                    }else{
                        this.$confirm('该操作将清除专业财务账套信息、会计凭证、财务余额（科目期初，资产负债表，利润表，科目余额表，明细账，核算项目余额表，核算项目明细账），且不可恢复，确认要反开账吗？（请谨慎操作！）', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning',
                            center: true
                        }).then(() => {
                            const loading = this.$loading({
                                lock: true,
                                text: '正在处理，请稍候...',
                                spinner: 'el-icon-loading',
                                background: 'rgba(255, 255, 255, 0.7)'
                            });
                            let _this = this;
                            $.ajax({
                                url: '/api/OpeningAccount/ClearAccount?operKey=' + g_operKey,
                                type: 'post',
                                data: JSON.stringify({ operKey: g_operKey, company_name: _this.form.company }),
                                contentType: "application/json;charset=UTF-8",
                                error: (xhr) => {
                                    loading.close();
                                    this.$message({ type: 'error', message: '网络错误' });
                                }
                            }).then(res => {
                                loading.close();
                                if (res.result == "OK") {
                                    console.log('clear account');
                                    _this.form.useAccounting = false;
                                    _this.$alert('反开账成功', '提示', {
                                        confirmButtonText: '确定',
                                        callback: action => {
                                            location.reload();
                                        }
                                    });
                                } else {
                                    this.$message({ type: 'error', message: res.msg });
                                }
                            });
                        });
                    }
                }
                //forTest() {
                //    console.log('test');
                //}
            }
        });
    </script>
</body>
</html>