using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;
using System.Data;


namespace ArtisanManage.Pages.BaseInfo 
{
    public class BarcodeScaleEditModel : PageFormModel
    {
        public BarcodeScaleEditModel(CMySbCommand cmd,string company_id="",string oper_id="") : base(Services.MenuId.infoClient)
        {
            this.cmd=cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"scale_id",new DataItem(){Title="条码秤id", Width="100",Hidden=true,HideOnLoad=true,FldArea="divHead"}},
                {"scale_no",new DataItem(){Title="条码秤编号",Width="100",CellsAlign="center",FldArea="divHead"}},
                {"scale_name",new DataItem(){Title="条码秤名称", Width="100",Sortable=true,CellsAlign="center",FldArea="divHead"}},
                {"scale_model",new DataItem(){Title="条码秤类型", Width="100",Sortable=true,CellsAlign="center", FldArea = "divHead"}},
                {"barcode_scale_group_id",new DataItem(){Title="分组", LabelFld="barcode_scale_group_name",Width="100", FldArea = "divHead",Sortable=true,ButtonUsage="list",QueryByLabelLikeIfIdEmpty=true,QueryOnChange=true ,ShowDropDownColumnsHeader=true,
                    CellsAlign="center",SqlForOptions="select barcode_scale_group_id as v,barcode_scale_group_name as l from info_barcode_scale_group where company_id=~COMPANY_ID"}},

                {"bar_ip",new DataItem(){Title="ip", Width="150",Sortable=true,CellsAlign="center", FldArea = "divHead"}},
                {"barcode_head2",new DataItem(){Title="前两位码", Width="100",Sortable=true,CellsAlign="center",FldArea="divHead"}},
                {"owner_store",new DataItem(){Title="所属门店", Width="200",Sortable=true,CellsAlign="center", FldArea = "divHead"}},
            };
 
            m_idFld = "scale_id"; m_nameFld = "scale_name";
            m_tableName = "info_barcode_scale";
            m_selectFromSQL = "from info_barcode_scale where scale_id='~ID'";
        }

        public async Task OnGet()
        {  
            await InitGet(cmd);   
        }
        
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class BarcodeScaleEditController : BaseController
    { 
        public BarcodeScaleEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues)
        {
            BarcodeScaleEditModel model = new BarcodeScaleEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            BarcodeScaleEditModel model = new BarcodeScaleEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            BarcodeScaleEditModel model = new BarcodeScaleEditModel(cmd);
            return await model.SaveTable(cmd, request);

        }
    }
}