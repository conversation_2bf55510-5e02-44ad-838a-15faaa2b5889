@page
@model ArtisanManage.Pages.BaseInfo.BorrowedViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var m_db_id = "10";

    	var newCount = 1;

    	var itemSource = {};
    	$(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            $("#gridItems").on("cellclick", function (event) {

                var args = event.args;
                var supcust_id = args.row.bounddata.supcust_id;

                var sup_name = args.row.bounddata.sup_name;

                var startDay = $('#startDay').jqxDateTimeInput('val');
                var endDay = $('#endDay').jqxDateTimeInput('val');
                var item_id = args.row.bounddata.item_id
                var item_name = args.row.bounddata.item_name
                //var seller_id = $('#seller_id').val().value;
                //var seller_name = $('#seller_id').val().label;
                //var brand_id = $('#brand_id').val().value;

                //var brand_name = $('#brand_id').val().label;
                var url = ""
                var title = ""
                //if (args.datafield == "sup_name" && sup_name) {
                //    url = `Report/AccountHistory?&supcust_id=${supcust_id}&sup_name=${sup_name}&startDay=${startDay}&endDay=${endDay}`;
                //   title="客户往来账"
                //    window.parent.newTabPage(title, `${url}`);
                //}
                if (args.datafield == "j_qty"  && sup_name) {
                        url = `Report/BorrowItem?&supcust_id=${supcust_id}&sup_name=${sup_name}&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&type=borrow&type_name=借货`;
                    title = "借还货明细表"
                    window.parent.newTabPage(title, `${url}`,window);
                }
                if (args.datafield == "h_qty"  && sup_name) {
                        url = `Report/BorrowItem?&supcust_id=${supcust_id}&sup_name=${sup_name}&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&type=return&type_name=还货`;
                    title = "借还货明细表"
                    window.parent.newTabPage(title, `${url}`,window);
                }


            });

            QueryData();
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            var isScroll = document.body.offsetHeight > 600;
            $('#supcust_id').jqxInput({
                onButtonClick: function (event) {
                    $('#popClient').jqxWindow('open');
                    $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling=${isScroll}?"no":"yes" frameborder="no"></iframe>`);
                }
            });
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            $('#item_id').jqxInput({
                onButtonClick: function (event) {
                    $('#popItem').jqxWindow('open');
                    $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling=${isScroll}?"no":"yes" frameborder="no"></iframe>`);
                }
            });
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
        });
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                }
                $('#popClient').jqxWindow('close');
            }
            else if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "selectMulti") {
                    if (rs.data.checkedRows.length == 1) {
                        var item_id = rs.data.checkedRows[0].item_id;
                        var item_name = rs.data.checkedRows[0].item_name;
                    }

                    var rows = rs.data.checkedRows
                    var items_id = ''
                    var items_name = ''
                    rows.forEach(function (row) {
                        if (items_id != '') {
                            items_id += ',';
                        }

                        items_id += row.item_id;
                        items_name += row.item_name + ';';

                    })
                    $('#item_id').jqxInput('val', { value: items_id, label: items_name });

                    //$.ajax({
                    //    url: '/api/SaleSheet/GetItemInfo',
                    //    type: 'GET',
                    //    contentType: 'application/json',
                    //    data: { operKey: g_operKey, item_id: items_id },
                    //    success: function (data) {
                    //        if (data.result === 'OK') {
                    //            if (!window.g_queriedItems) window.g_queriedItems = {};
                    //            window.g_queriedItems[item_id] = data.item;
                    //        }
                    //    }
                    //});
                }

                $('#popItem').jqxWindow('close');
            }

        });
    </script>
</head>

<body style="overflow:hidden">

    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail" style="width:calc(100% - 100px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>


    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>


    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择商品</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
</body>
</html>