﻿@page
@model ArtisanManage.Pages.BaseInfo.PerformanceScheduleShowModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    @*<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.aggregates.js"></script>*@
    <script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.0.0-beta.2/echarts.common.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var m_db_id = "10";
        var newCount = 1;
        var itemSource = {};
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)

            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
            });
             
            QueryData();
            updateCharts();
        });
    </script>

</head>

<body>

<div style="display:flex;margin-top:20px;margin-left:20px" ; id="divHead" class="headtail">

    <div style="display: flex;">
        @*<label>交易日期：</label><input id="startDay" type="date" />~<input id="endDay" type="date" />*@
        <div style="height:30px"><label>开始日期:</label></div><input id="startDay" />
        <div style="height:30px;width:100px"><label>结束日期：</label></div><input id="endDay" />
        <div style="margin-left:20px;margin-top:5px"> <button onclick="QueryData">查询</button> </div>
    </div>

</div>
<div id="main" style="height:50%;padding: 2rem 0;"></div>

<div id="gridItems" style="margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 95px);"></div>



</body>
</html>
<script>
    $(function() {
        //        function updateCharts(rows) {
        //            console.log(rows);
        //            option.xAxis.data = rows.map(row => row.interval).reverse();
        //            option.series[0].data = rows.map(row => row.net_amount).reverse();
        //            myChart.setOption(option);
        //        };

        var Chart = echarts.init(document.getElementById('echart'));
        var option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['销售金额', '退货金额', '赠送金额']
            },
            grid: {
                left: '0%',
                right: '0%',
                bottom: '2%',
                top: "25%",
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: [],
                axisLabel: {
                    interval: 0,
                    rotate: 40
                }
            },
            yAxis: {},
            color: ['#cc163a', '#474b4c', '#40a070', '#d48265', '#91c7ae', '#749f83', '#ca8622', '#bda29a', '#6e7074', '#546570', '#c4ccd3'],
            series: [
                {
                    name: '销售金额',
                    type: 'bar',
                    emphasis: {
                        focus: 'series'
                    },
                    data: []
                },
                {
                    name: '退货金额',
                    type: 'bar',
                    emphasis: {
                        focus: 'series'
                    },
                    data: []
                },
                {
                    name: '赠送金额',
                    type: 'bar',
                    emphasis: {
                        focus: 'series'
                    },
                    data: []
                }
            ]
        }

        function updateCharts(rows) {
            var keys = Object.keys(rows);
            var xAxis = [];
            var series = [];
            var legend = keys;
            rows[keys[0]].forEach(el => {
                xAxis.push(el.oper_name);
            });
            for (var i = 0; i < keys.length; i++) {
                var d = [];
                rows[keys[i]].forEach(el => {
                    d.push(toDecimal(el.sum));
                });
                var datas = {
                    name: keys[i],
                    type: 'bar',
                    emphasis: {
                        focus: 'series'
                    },
                    data: d
                }
                series.push(datas);
            }
            console.log(series);
            console.log(xAxis);
            option.xAxis.data = xAxis;
            option.legend.data = legend;
            option.series = series;
            option && Chart.setOption(option);
            Chart.setOption(option);
        }
    });
</script>