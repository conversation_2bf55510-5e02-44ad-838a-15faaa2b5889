using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class OperatorsViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;

 

        public OperatorsViewModel(CMySbCommand cmd) : base(Services.MenuId.infoOperator)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {
                    "searchString",
                    new DataItem()
                    {
                        Title = "检索字符串", PlaceHolder = "输入名称", UseJQWidgets = false, SqlFld = "oper_name,py_str,mobile",
                        LabelFld = "LabelFld", ButtonUsage = "list", QueryOnChange = true, CompareOperator = "like"
                    }
                },
                {"status",new DataItem(){Title="状态",LabelFld="cls_status_name",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list",QueryOnChange=true,CompareOperator="=",NullEqualValue="1",
                     Source = @"[{v:'normal',l:'正常',condition:""(status = '1' or status is null)""},
                               {v:'stop',l:'停用',condition:""status = '0' ""},
                               {v:'all',l:'所有',condition:""true""}]"


                }},

                {
                    "depart_path", new DataItem()
                    {
                        Title = "父部门", LikeWrapper = "/", CtrlType = "jqxTree", MumSelectable = true,
                        GetOptionsOnLoad = true, QueryOnChange = true, CompareOperator = "like",
                        SqlForOptions = "select depart_id as v,depart_name as l,mother_id as pv from info_department order by order_index"
                    }
                },

            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                    "gridItems", new QueryGrid()
                    {
                        IdColumn = "i", TableName = "info_operator",
                        Sortable=true,
                        ShowContextMenu = true,
                        ContextMenuHTML="<ul><li id='edit'>编辑</li><li id='remove'>删除</li><li id='resetPassword'>重置密码</li></ul>",
                        Columns = new Dictionary<string, DataItem>()
                        {
                            {"i", new DataItem() {Title = "编号", SqlFld = "oper_id", Width = "80", Hidden = true,HideOnLoad = true}},
                            {"oper_name", new DataItem() {Title = "姓名", Width = "180", Linkable = true,Sortable=true}},
                            {"mobile", new DataItem() {Title = "手机", Width = "180"}},
                            {"role_name", new DataItem() {Title = "角色", Width = "180",Sortable=true}},
                            {
                                "is_seller",
                                new DataItem()
                                {
                                    Title = "是否业务员", Width = "180",
                                    SqlFld = "(case WHEN is_seller THEN '是' ELSE '否' END)"
                                }
                            },
                            {"status",new DataItem(){Title="状态",Width="180",SqlFld="(case WHEN status='0' THEN '停用' ELSE '正常' END)",Sortable=true}},
                            {"seller_max_arrears",new DataItem(){Title="欠款额度",Width="100",Hidden=true, HideOnLoad = true}},//迁移到欠款策略了
                            {"auxiliary_balance",new DataItem(){Title="欠款金额",Width="100",Hidden=true}},
                            {"can_login",new DataItem(){Title="可登录",Width="180",SqlFld="(case WHEN can_login or can_login is null THEN '是' ELSE '否' END)"}},
                            {"fee_status",new DataItem(){Title="计费状态",Width="180",SqlFld="case when trial_till_date is not null then '试用' WHEN fee_discount=0 then '免费' when not coalesce(can_login,true) then ''  when coalesce(fee_discount,1)=1 then '计费' else fee_discount::text||'比率' end"}},
                            {"mini_bind_status", new DataItem() {Title = "商城绑定", Width = "180", Linkable = true,  SqlFld = "(case WHEN mini_oper_id is null THEN '绑定' ELSE '解绑' || ioc.contact_mobile END)"}},
                            {"mini_oper_id", new DataItem() {Title = "商城绑定员工",Hidden = true,HideOnLoad = true}},
                            {"contact_id", new DataItem() {Title = "绑定id",Hidden = true,HideOnLoad = true}},
                        },
                        QueryFromSQL = @"
from info_operator
left join info_role on info_operator.role_id = info_role.role_id and info_operator.company_id = info_role.company_id
left join info_role_template t on info_role.templ_id=t.templ_id and t.company_id =~COMPANY_ID
left join arrears_balance_auxiliary aux on info_operator.oper_id=aux.auxiliary_id and aux.auxiliary_type='seller'
left join (select oper_id as ioc_oper_id, mini_oper_id,contact_id, contact_mobile from info_oper_contact  where info_oper_contact.company_id = ~COMPANY_ID) ioc on ioc.mini_oper_id = info_operator.oper_id
where info_operator.company_id=~COMPANY_ID", QueryOrderSQL = "order by order_index, oper_id"
                    }
                }
            };
        }

        public async Task OnGet(string forSelect)
        {  
            await InitGet(cmd);
            ForSelect = forSelect == "1";
        }

        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            SQLQueue QQ = new SQLQueue(cmd);
            //两年内都没用过的业务员或送货员就不管了
            string sql = $@"select sheet_type from (
(select (case sheet_type when 'X' then '销售单' when 'T' then '退货单' end) as sheet_type from sheet_sale_main where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or seller_id in ({rowIDs}) or '{rowIDs}' = ANY (STRING_TO_ARRAY(senders_id, ',')) ) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select (case sheet_type when 'XD' then '销售订单' when 'TD' then '退货订单' end) as sheet_type from sheet_sale_order_main where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or seller_id in ({rowIDs}) or '{rowIDs}' = ANY (STRING_TO_ARRAY(senders_id, ',')) ) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select (case sheet_type when 'SR' then '收款单' when 'FK' then '付款单' end) as sheet_type from sheet_get_arrears_main where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or getter_id in ({rowIDs})) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select (case sheet_type when 'YS' then '预收款单' when 'YF' then '预付款单' when 'DH' then '定货会' end) as sheet_type from sheet_prepay where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or getter_id in ({rowIDs})) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select (case sheet_type when 'CG' then '采购单' when 'CT' then '采购退货单' end) as sheet_type from sheet_buy_main where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or getter_id in ({rowIDs}) or '{rowIDs}' = ANY (STRING_TO_ARRAY(receivers_id, ',')) ) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select '采购订单' as sheet_type from sheet_buy_order_main where (maker_id in ({rowIDs}) or getter_id in ({rowIDs}) or approver_id in ({rowIDs}) or '{rowIDs}' = ANY (STRING_TO_ARRAY(receivers_id, ',')) ) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select (case sheet_type when 'ZC' then '费用支出单' when 'SR' then '其他收入单' end) as sheet_type from sheet_fee_out_main where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or getter_id in ({rowIDs})) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select '转账' as sheet_type from sheet_cashbank_transfer_main where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or seller_id in ({rowIDs})) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select '盘点单' as sheet_type from sheet_inventory_main where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or seller_id in ({rowIDs})) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select (case sheet_type when 'YK' then '盘点盈亏单' when 'BS' then '报损单' end) as sheet_type from sheet_invent_change_main where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or seller_id in ({rowIDs})) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select '定货调整单' as sheet_type from sheet_item_ordered_adjust_main where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or seller_id in ({rowIDs})) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select '贷款单' as sheet_type from sheet_loan where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or getter_id in ({rowIDs})) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select '调拨单' as sheet_type from sheet_move_main where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or seller_id in ({rowIDs}) or sender_id in ({rowIDs}) or receiver_id in ({rowIDs})) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
union all
(select (case sheet_type when 'CK' then '其它出库单' when 'RK' then '其它入库单' end) as sheet_type from sheet_stock_in_out_main where (maker_id in ({rowIDs}) or approver_id in ({rowIDs}) or seller_id in ({rowIDs})) and company_id={company_id} and happen_time between '{DateTime.Now.AddYears(-2)}' and '{DateTime.Now}' limit 1)
) t limit 1";
            QQ.Enqueue("sheet", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            dynamic sheet = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "sheet")
                {
                    sheet = CDbDealer.Get1RecordFromDr(dr, false);
                    if (sheet != null)
                    {
                        return $" 员工开过【{sheet.sheet_type}】，无法删除";
                    }
                }
            }
            QQ.Clear();

            return "";
        }

    }

    [Route("api/[controller]/[action]")]
    public class OperatorsViewController : QueryController
    { 
        public OperatorsViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            OperatorsViewModel model = new OperatorsViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {// string gridID,int startRow,int endRow,bool bNewQuery){
            OperatorsViewModel model = new OperatorsViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            OperatorsViewModel model = new OperatorsViewModel(cmd);
            string operToDel = data.rowIDs;
            string result = "OK", msg = "";
            //限制管理员不能将自己删除
            cmd.CommandText = $"select is_admin from g_operator where company_id={Token.CompanyID} and oper_id={operToDel};";
            var isAdmin = await cmd.ExecuteScalarAsync();
            if (isAdmin != null && Convert.ToBoolean(isAdmin) == true)
            {
                msg = "主账号不能删除";
                result = "Error";
                return new JsonResult(new { result, msg });
            }

            Func<Task> cbDone = new Func<Task>(async() =>
            {
                cmd.CommandText = $"delete from g_operator where company_id={Token.CompanyID} and oper_id={operToDel};";
                await cmd.ExecuteNonQueryAsync();
                TokenChecker.RemoveOper(operToDel);
            });

            dynamic res = (await model.DeleteRecords(data, cmd, "info_operator", cbDone)).Value;// gridID, startRow, endRow, bNewQuery);

            if (res.result != "OK") 
            { 
                msg = "删除失败" + res.msg;
                NLogger.Error(msg);
            }
           
            if (msg != "") result = "Error";
     
            return new JsonResult(new { result, msg });
            
        }
       
        [HttpPost]
        public async Task<IActionResult> RemoveClass([FromBody] dynamic value)
        {
            DepartEditModel model = new DepartEditModel(cmd);
            string id = value[model.m_idFld];
            string result = "OK";
            if (id == "")
            {
                result = "请传入部门编号";
                goto end;
            }
            CDbDealer db = new CDbDealer();

            object o = null;

            cmd.CommandText = $"select depart_id from info_department where mother_id='{id}'";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该部门的子部门后再删除该部门"; goto end;
            }
            cmd.CommandText = $"select oper_id from info_operator where depart_id='{id}'";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该部门的员工后再删除该部门"; goto end;
            }
            string sql = $"delete from info_department where depart_id='{id}'";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();

           
            //  var tt = Convert.ToString(value.uid); 
            //var rr = new { UserID = value.UserID, UserName = value.UserName };
            //return value;
            end:
            return Json(new { result, depart_id = id });
            //return JsonObject<object> (new { UserID = value.UserID, UserName = value.UserName });
        }



        [HttpPost]
        public async Task<JsonResult> ResetPassword([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);

            string sql = $"update g_operator set oper_pw='{data.password}' where oper_id={data.operID} and company_id={companyID}";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            await TokenChecker.UpdateOperPwd((string)data.operID, (string)data.password);
            return Json(new { result="OK", msg=""});
        }

        [HttpPost]
        public async Task<JsonResult> RestrictEmployee([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);

            cmd.CommandText = $@"
select gc.user_count,oper.used_user_count from g_company gc
left join 
(
  select count(*) used_user_count from info_operator where company_id={companyID}
) oper on true
where gc.company_id={companyID}
";
            var dr = await cmd.ExecuteReaderAsync();
            int user_count = 0;
            int used_user_count = 0;
            if (dr.Read())
            {
                    user_count = Convert.ToInt32(CPubVars.GetTextFromDr(dr, "user_count"));
                    used_user_count = Convert.ToInt32(CPubVars.GetTextFromDr(dr, "used_user_count"));
            }
            dr.Close();
            if (user_count == used_user_count) 
            {
                return Json(new { result = "FAIL", msg = "端口数不足" });
            }
            return Json(new { result = "OK", msg = "" });
        }
    }
}
