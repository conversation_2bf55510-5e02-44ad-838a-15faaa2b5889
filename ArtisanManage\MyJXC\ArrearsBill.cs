﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using NPOI.SS.Formula.Functions;
using ArtisanManage.Services.SheetService;
using ArtisanManage.MyCW;
using OBS.Model;
using ArtisanManage.Pages;
using ArtisanManage.WebAPI;
using ArtisanManage.YingJiangCommon.Controller.PromotionController;
using NPOI.HPSF;

namespace ArtisanManage.MyJXC
{
    public class ArrearsBill<TROW> : SheetBase<TROW> where TROW : SheetRowBase, new()
    {

        [SaveToDB][FromFld] public string supcust_id { get; set; } = "";
        [SaveToDB][FromFld(LOAD_PURPOSE.SHOW)] public string supcust_name { get; set; } = "";
        [SaveToDB][FromFld] public string business_sheet_id { get; set; } = "";
        [SaveToDB][FromFld(LOAD_PURPOSE.SHOW)] public string business_sheet_no { get; set; } = "";
        [SaveToDB][FromFld] public string business_sheet_type { get; set; } = "";
        [SaveToDB][FromFld(LOAD_PURPOSE.SHOW)] public decimal orig_amount { get; set; } = 0;
        [SaveToDB][FromFld(LOAD_PURPOSE.SHOW)] public decimal left_amount { get; set; } = 0;
        [SaveToDB][FromFld(LOAD_PURPOSE.SHOW)] public string keeper_id { get; set; } = "";
        [SaveToDB][FromFld(LOAD_PURPOSE.SHOW)] public bool out_company { get; set; } = true;
        [SaveToDB][FromFld(LOAD_PURPOSE.SHOW)] public string arrears_status { get; set; } = "";
        public ArrearsBill(string mainTable, string detailTable, LOAD_PURPOSE loadPurpose) : base(mainTable, detailTable, loadPurpose)
        {
        }
        private void ConstructFun()
        {
        }
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.supcust_id + "_" + this.total_amount.ToString() + "_" + this.make_brief;
            return res;
        }

        protected override void InitForSave()
        {
            base.InitForSave();
            
            if (maker_id == "") maker_id = OperID;
            if (approver_id == "") approver_id = OperID;
           
        }
        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            
            return "OK";
        }
    }
}
