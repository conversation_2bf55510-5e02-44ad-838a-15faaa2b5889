# 陈列协议模板

# 陈列协议流程

## 签约陈列协议

1. 通知复核人员
   1. 通过
   2. 不通过，待办事项通知开协议人

## 维护陈列协议

1. 进行门店拜访的时候，查询这个用户的陈列协议，获取维护的相关信息。陈列协议超过本月的就不在进行展示。
   1.  如果月维护次数为0， 则不进行校验
   2.  每个陈列协议有对应的月维护次数，月维护间隔，
      1. 外连接sum_display_maintain，获取该月份该陈列协议已经维护的次数
      2. 查询op_display_maintain，获取上次的维护时间
      3. 计算下次维护时间
   3.  以下情况将数据不返回前端
      1.  维护次数已经满足共维护次数
   4.  页面展示，已经   维护次数/共续维护次数。
      1. 如果月维护间隔为0，直接展示维护行为
      2. 页面计算距离上次维护后，应该维护的日期
         1. 如果今天大于等于该日期则展示维护行为
         2. 如果小于该日期，则展示下次维护的日期
2. 陈列协议的复核，通过在拜访记录界面进行查看

### op_display_maintain

| 字段           | 中文 | 类型 | 备注 |
| -------------- | ---- | ---- | ---- |
| company_id     |      |      |      |
| maintain_id    |      |      |      |
| oper_id        |      |      |      |
| client_id      |      |      |      |
| disp_temp_id   |      |      |      |
| disp_sheet_id  |      |      |      |
| work_content   |      |      |      |
| happen_time    |      |      |      |
| visit_id       |      |      |      |
| reviewer       |      |      |      |
| review_time    |      |      |      |
| review_comment |      |      |      |
| review_refused |      |      |      |

### sum_display_maintain

| 字段            | 中文 | 类型 | 备注 |
| --------------- | ---- | ---- | ---- |
| company_id      |      |      |      |
| sum_maintain_id |      |      |      |
| client_id       |      |      |      |
| disp_temp_id    |      |      |      |
| disp_sheet_id   |      |      |      |
| maintain_times  |      |      |      |
| months          | 月份 |      |      |

## 续签陈列协议

### op_display_keep

| 字段           | 中文 | 类型 | 备注 |
| -------------- | ---- | ---- | ---- |
| company_id     |      |      |      |
| keep_id        |      |      |      |
| oper_id        |      |      |      |
| client_id      |      |      |      |
| disp_temp_id   |      |      |      |
| disp_sheet_id  |      |      |      |
| work_content   |      |      |      |
| happen_time    |      |      |      |
| visit_id       |      |      |      |
| reviewer       |      |      |      |
| review_time    |      |      |      |
| review_comment |      |      |      |
| review_refused |      |      |      |

### sum_display_keep

| 字段            | 中文         | 类型 | 备注                               |
| --------------- | ------------ | ---- | ---------------------------------- |
| company_id      |              |      |                                    |
| sum_keep_id     |              |      |                                    |
| client_id       |              |      |                                    |
| disp_temp_id    |              |      |                                    |
| disp_sheet_id   |              |      |                                    |
| keep_times      |              |      |                                    |
| need_keep_times | 需要续签次数 |      | 在第一次插入的时候，完成数据的计算 |

续签总次数，（共计多少个月 - 1） / 续签间隔，结果取整









## 兑付陈列协议

查询哪些能兑付

全部列出，灰色标记，Toast

