using ArtisanManage.Models;
using ArtisanManage.MyCW;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Microsoft.VisualBasic;
using myJXC;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Npgsql;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using NuGet.ProjectModel;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Diagnostics.Contracts;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using static ArtisanManage.Services.CommonTool;

namespace ArtisanManage.MyJXC
{
    public class SheetRowItem : SheetRowBase
    {
        public SheetRowItem()
        {

        }
        [SaveToDB][FromFld(LOAD_PURPOSE.SHOW)] public override int row_index { get; set; } = 1;
        [SaveToDB][FromFld] public string branch_position { get; set; } = "0";
    
        [FromFld(LOAD_PURPOSE.SHOW)] public string branch_position_name { get; set; } = "";
        [SaveToDB][FromFld] public string item_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_no { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_images { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_videos { get; set; } = "";
		//临时注释
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_desc { get; set; } = "";
		[SaveToDB][FromFld] public string branch_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string branch_name { get; set; } = "";

        [SaveToDB][FromFld] public string unit_no { get; set; } = "";
        [SaveToDB][FromFld] public decimal unit_factor { get; set; } = 1;
        [SaveToDB][FromFld] public decimal quantity { get; set; }
        [FromFld("itb.produce_date")] public string produce_date { get; set; } = "";

        [FromFld("itb.batch_no")] public string batch_no { get; set; } = "";
        [SaveToDB][FromFld] public string batch_id { get; set; } = "0";
        //public string _batch_id
        //{
        //    get { 
        //        return string.IsNullOrEmpty(batch_id) ? "0" : batch_id;
        //     }
        //    set
        //    {
        //        batch_id = string.IsNullOrEmpty(batch_id) ? "0" : batch_id;
        //    }
        //}
        [FromFld(LOAD_PURPOSE.SHOW)] public string batch_level { get; set; } = "";

        public string give_qty_unit = "";
        public string qty_unit
        {
            get
            {
                return CPubVars.FormatMoney(quantity, 3) + unit_no;
            }
        }
        public string real_qty_unit = "";

        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string b_barcode { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string m_barcode { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string s_barcode { get; set; }
        [FromFld("t.happen_time", LOAD_PURPOSE.SHOW)] public string happen_time { get; set; }
        public string happen_date { 
            get
            {
                return CPubVars.GetDateTextNoTime(happen_time);
            }
        }
        public string smb_barcode
        {
            get
            {
                return s_barcode.IsValid() ? s_barcode : m_barcode.IsValid() ? m_barcode : b_barcode;
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_unit_no { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_factor { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_factor { get; set; }


        [FromFld(LOAD_PURPOSE.SHOW)] public string brand_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string brand_name { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string class_name { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string classId { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string other_class { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_order_index { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string class_order_index { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)]public string location { get; set; }
        public string b_qty_dot
        {
            get
            {
                if (!b_unit_factor.IsValid()) return "";
                string qty = CPubVars.FormatMoney(unit_factor * quantity / CPubVars.ToDecimal(b_unit_factor), 3);
                return qty;
            }
		}
        public string b_qty_dot_unit
        {
            get
            {
                return b_qty_dot + b_unit_no;
            }
        }
        public string b_quantity_show
		{
			get
			{
				if (b_quantity == 0) return "";
				return b_quantity.ToString();
			}
		}
        public string bs_qty_dot
        {
            get
            {
                try
                {
					if (!b_unit_no.IsValid()) return quantity.ToString();

					string qty = CPubVars.FormatMoney(unit_factor * quantity / CPubVars.ToDecimal(b_unit_factor), 3);
					return qty;
				}
                catch(Exception ee)
                {
                    
                    MyLogger.LogMsg( $"获取bs_qty_dot出错,item_id{item_id},item_name{item_name},b_unit_factor:{b_unit_factor},b_unit_no:{b_unit_no},{ee.Message}","0");
                    return "";
                }
            }
        }
        public string bs_unit_no
        {
            get
            {
                if (!b_unit_no.IsValid()) return unit_no;                 
                return b_unit_no;
            }
        }
        public string bs_qty_dot_unit_no
        {
            get
            {                 
                return bs_qty_dot + bs_unit_no;
            }
        }

        public string s_quantity_show
        {
            get
            {
                if (s_quantity == 0) return "";
                return s_quantity.ToString();
            }
        }
      
        public decimal b_quantity { 
            get {
                return SheetBase<SheetRowItem>.GetQtyOfUnit(unit_factor * quantity, "b", b_unit_factor, m_unit_factor);
            }
        }
        public decimal m_quantity
        {
            get
            {
                return SheetBase<SheetRowItem>.GetQtyOfUnit(unit_factor * quantity, "m", b_unit_factor, m_unit_factor);
            }
        }
        public decimal s_quantity
        {
            get
            {
                return SheetBase<SheetRowItem>.GetQtyOfUnit(unit_factor * quantity, "s", b_unit_factor, m_unit_factor);
            }
        }

        public string b_quantity_unit
        {
            get
            {
                return CPubVars.FormatMoney(b_quantity, 0) + b_unit_no;
            }
        }
        public string m_quantity_unit
        {
            get
            {
                return CPubVars.FormatMoney(m_quantity, 0) + m_unit_no;
            }
        }
        public string s_quantity_unit
        {
            get
            {
                return CPubVars.FormatMoney(s_quantity, 3) + s_unit_no;
            }
        }

        public string quantity_unit_conv
        {
            get
            {
                return SheetBase<SheetRowItem>.GetUnitQty(quantity * unit_factor, b_unit_no, m_unit_no, s_unit_no, b_unit_factor, m_unit_factor);
            }
        }
        // public string quantity_unit_conv { get; set;}

        public string s_sub_qty_unit
        {
            get
            {
                return s_sub_qty + this.s_unit_no;
            }
        }
        public string s_sub_qty
        {
            get
            {
                return CPubVars.FormatMoney(this.quantity * this.unit_factor, 2);
            }
        }
        public string ms_sub_qty
        {
            get
            {
                decimal sQty = this.quantity * this.unit_factor;
                if (!string.IsNullOrEmpty(this.m_unit_factor))
                {
                    decimal mFactor = CPubVars.ToDecimal(this.m_unit_factor);
                    return CPubVars.FormatMoney(sQty / mFactor, 3);

                }
                else
                    return CPubVars.FormatMoney(sQty, 3);
            }
        }

        public string ms_sub_qty_unit
        {
            get
            {
                if (!string.IsNullOrEmpty(this.m_unit_factor))
                {
                    return ms_sub_qty + this.m_unit_no;

                }
                else
                    return ms_sub_qty + this.s_unit_no;
            }
        }

        // internal decimal s_sum_qty = 0f;//用于汇总单计算
        //汇总单
        public string sale_quantity_unit_conv { get; set; }
        public string give_quantity_unit_conv { get; set; }



        public string unit_relation
        {
            get
            {
                string r = "";
                if (m_unit_factor.IsValid())
                {
                    r = $"1*{CPubVars.FormatMoney(Convert.ToDouble(b_unit_factor) / Convert.ToDouble(m_unit_factor), 0)}*{m_unit_factor}"; 
                }
                else if(b_unit_factor.IsValid())
                {
                    r = $"1*{b_unit_factor}";
                }
                return r;
            }
        }
        public string unit_relation1
        {
            get
            {
                string r = "";
                if (m_unit_factor.IsValid())
                {

                    r = $"1{b_unit_no}={CPubVars.FormatMoney(Convert.ToDouble(b_unit_factor) / Convert.ToDouble(m_unit_factor), 0)}{m_unit_no}={b_unit_factor}{s_unit_no}";
                }
                else if (b_unit_factor.IsValid())
                {

                    r = $"1{b_unit_no}={b_unit_factor}{s_unit_no}";
                }
                return r;
            }
        }
        private string _barcode = "";
        public string barcode { 
            get 
            { 
                if (_barcode != "") return _barcode;
                if (unit_no == s_unit_no) return s_barcode;
                if (unit_no == b_unit_no) return b_barcode;
                if (unit_no == m_unit_no) return m_barcode;
                return "";
            }
            set {
                _barcode = value;
            } 
        }







        //happen_time

        internal decimal StockQty = 0;
        internal decimal SaleOrderQty = 0;
        internal decimal SellPendQty = 0;
        internal bool HasStockQty = false;
        internal decimal NewStockQty = 0;
        internal decimal NewSellPendQty = 0;
        internal bool BranchAllowNegativeStock = true;
        internal string NegativeStockAccordance = "real";
        public virtual void SetInfoForPrint(bool smallUnitBarcode)
        {
            var row = this;
            // b_quantity = 0; m_quantity = 0; s_quantity = 0;
            if (smallUnitBarcode)
            {
                row.barcode = row.s_barcode;
            }
            else if (row.unit_no == row.b_unit_no)
            {
                row.barcode = row.b_barcode;
            }
            else if (row.unit_no == row.m_unit_no)
            {
                row.barcode = row.m_barcode;
            }
            else if (row.unit_no == row.s_unit_no)
            {
                row.barcode = row.s_barcode;
            }
            if(string.IsNullOrEmpty(row.branch_name)){
                row.branch_name=this.branch_name;
            }

            /*
            decimal leftQty = row.unit_factor * row.quantity;
            if (leftQty == 0) return;
            var absLeftQty = Math.Abs(leftQty);
            var flag = leftQty / absLeftQty;
            string sQty = "";
            if (row.b_unit_factor.IsValid())
            {  
                var qty = Math.Floor(absLeftQty / CPubVars.ToDecimal(row.b_unit_factor));
                if (qty < 0.001m) qty = 0;
                qty = CPubVars.ToDecimal(CPubVars.FormatMoney(qty, 3));
                if (qty > 0)
                    sQty += (qty * flag).ToString() + row.b_unit_no;
                absLeftQty = absLeftQty % CPubVars.ToDecimal(row.b_unit_factor);
                b_quantity +=qty * flag;
            }

            if (row.m_unit_factor.IsValid())
            {
                var qty = Math.Floor(absLeftQty / CPubVars.ToDecimal(row.m_unit_factor));
                if (qty < 0.001m) qty = 0;
                qty = CPubVars.ToDecimal(CPubVars.FormatMoney(qty, 3));
                if (qty > 0)
                    sQty += (qty * flag).ToString() + row.m_unit_no;
                absLeftQty = absLeftQty % CPubVars.ToDecimal(row.m_unit_factor);
                m_quantity += (decimal)qty * flag;
            }
            if (absLeftQty < 0.001m) absLeftQty = 0;

            absLeftQty = CPubVars.ToDecimal(CPubVars.FormatMoney(absLeftQty, 3));
            if (absLeftQty > 0)
            {
                s_quantity = absLeftQty;
                sQty += (absLeftQty * flag).ToString() + row.s_unit_no;
            }*/
            //row.quantity_unit_conv = sQty;

        }
    }
    public class SheetRowMM : SheetRowItem
    {
        public SheetRowMM()
        {

        }
        //[FromFld] public decimal quantity1 { get; set; }
        [SaveToDB][FromFld] public string orig_price { get; set; }
        [SaveToDB][FromFld] public decimal real_price { get; set; }
        [SaveToDB][FromFld] public string sys_price { get; set; }
        [SaveToDB][FromFld] public decimal sub_amount { get; set; }
        [SaveToDB][FromFld] public string remark_id { get; set; }

		[FromFld(LOAD_PURPOSE.SHOW)] public string item_remark { get; set; }
		[FromFld(LOAD_PURPOSE.SHOW)] public string item_spec { get; set; }
		[FromFld(LOAD_PURPOSE.SHOW)] public string item_alias { get; set; } 


		[SaveToDB][FromFld] public string virtual_produce_date { get; set; }
        public string show_produce_date
        {
            get
            {
                if (produce_date.IsValid())
                {
                    return produce_date.Replace("00:00:00", "").Replace(" ", "");
                }
                else return virtual_produce_date;
            }
        }
		public string strict_produce_date
		{
			get
			{
				if (produce_date.IsValid())
				{
					return produce_date.Replace("00:00:00", "").Replace(" ", "");
				}
				else return "";
			}
		}


		//[FromFld] public string produce_date { get; set; }
		//[FromFld] public string batch_no { get; set; }
		//[SaveToDB][FromFld] public string batch_id{ get; set; }

		public string orig_amount
        {
            get
            {
                if (CPubVars.IsNumeric(orig_price))
                {
                    
                    return CPubVars.FormatMoney(CPubVars.ToDecimal(orig_price) * quantity, 2);
                }
                return "";
            }
        }
        public string sub_cent
        {
            get
            {
                return CPubVars.FormatMoney(sub_amount * 100, 0);
            }
        }
       
        public string discount_amount { get; set; }
        [SaveToDB][FromFld] public string discount { get; set; } = "";
        

        [FromFld(LOAD_PURPOSE.SHOW)] public string s_retail_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_retail_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_retail_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_lowest_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_lowest_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_lowest_price { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string son_mum_item { get; set; } = "";
        [SaveToDB][FromFld("t.cost_price_avg", LOAD_PURPOSE.SHOW)] public string cost_price_avg { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string class1_name { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string class1_order_index { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string gen_class1 { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string gen_class2 { get; set; }


        public string gen_class
        {
            get
            {
                return !string.IsNullOrEmpty(gen_class2) ? gen_class2 : !string.IsNullOrEmpty(gen_class1) ? gen_class1 : "";
            }
        }

        [FromFld("cic.item_code", LOAD_PURPOSE.SHOW)] public virtual string store_item_code { get; set; }
        [FromFld("cic_acct.item_code", LOAD_PURPOSE.SHOW)] public virtual string acct_item_code { get; set; }
        [FromFld("cs.setting->>'validDayType'", LOAD_PURPOSE.SHOW)] public string cs_valid_day_type { get; set; }
        [FromFld("valid_day_type", LOAD_PURPOSE.SHOW)] public string valid_day_type { get; set; }
        [FromFld("valid_days", LOAD_PURPOSE.SHOW)] public string _valid_days { get; set; }
        public string valid_days
        {
            get
            {
                if (_valid_days.IsValid())
                {
                    string vt = "d";
                    if (cs_valid_day_type.IsValid()) vt = cs_valid_day_type;
                    if (valid_day_type.IsValid()) vt = valid_day_type;
                    if (vt == "m") vt = "个月"; else if (vt == "d") vt = "天"; else if (vt == "y") vt = "年";
                    return _valid_days + vt;
                }
                else return "";
            }
        }
        public string valid_till_date
        {
            get
            {
                if (_valid_days.IsValid() && CPubVars.IsDate(virtual_produce_date))
                {
                    DateTime dt = Convert.ToDateTime(virtual_produce_date);
                    //CPubVars.IsNumeric(_valid_days))
                    int.TryParse(_valid_days, out int nValidDays);
                    string vt = "d";
                    if (cs_valid_day_type.IsValid()) vt = cs_valid_day_type;
                    if (valid_day_type.IsValid()) vt = valid_day_type;
                    string s = "";
                    if (vt == "m")
                    {
                        s = CPubVars.GetDateTextNoTime(dt.AddMonths(nValidDays));
                    }
                    else if (vt == "y")
                    {
                        s = CPubVars.GetDateTextNoTime(dt.AddYears(nValidDays));
                    }
                    else if (vt == "d")
                    {
                        s = CPubVars.GetDateTextNoTime(dt.AddDays(nValidDays));
                    }
                    return s;
                }
                else return "";

            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string s_weight { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string m_weight { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string b_weight { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string s_volume { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string m_volume { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string b_volume { get; set; }
        public decimal weight
        {
            get
            {
                return Math.Round(unit_weight * quantity, 3);
            }
        }
        public decimal unit_weight
        {
            get
            {
                decimal unitWeight = 0m;
                decimal nb_weight = b_weight.IsValid() ? CPubVars.ToDecimal(b_weight) : 0m;
                decimal nm_weight = m_weight.IsValid() ? CPubVars.ToDecimal(m_weight) : 0m;
                decimal ns_weight = s_weight.IsValid() ? CPubVars.ToDecimal(s_weight) : 0m;

                if (unit_no == b_unit_no)
                {
                    unitWeight = nb_weight;
                    if (unitWeight == 0)
                    {
                        if (m_unit_factor != "" && nm_weight > 0) unitWeight = nm_weight / CPubVars.ToDecimal(m_unit_factor) * CPubVars.ToDecimal(b_unit_factor);
                        else if (ns_weight > 0) unitWeight = ns_weight * CPubVars.ToDecimal(b_unit_factor);
                    }
                }
                else if (unit_no == m_unit_no)
                {
                    unitWeight = nm_weight;
                    if (unitWeight == 0)
                    {
                        if (b_unit_factor != "" && nb_weight > 0) unitWeight = nb_weight / CPubVars.ToDecimal(b_unit_factor) * CPubVars.ToDecimal(m_unit_factor);
                        else if (ns_weight > 0) unitWeight = ns_weight * CPubVars.ToDecimal(m_unit_factor);
                    }
                }
                else if (unit_no == s_unit_no)
                {
                    unitWeight = ns_weight;
                    if (unitWeight == 0)
                    {
                        if (b_unit_factor != "" && nb_weight > 0) unitWeight = nb_weight / CPubVars.ToDecimal(b_unit_factor);
                        else if (m_unit_factor != "" && nm_weight > 0) unitWeight = nm_weight / CPubVars.ToDecimal(m_unit_factor);
                    }
                }
                return Math.Round(unitWeight, 3);
            }
        }
        public decimal volume
        {
            get
            {
                return Math.Round(unit_volume * quantity, 3);
            }
        }
        public decimal unit_volume
        {
            get
            {
                decimal nb_volume = b_volume.IsValid() ? CPubVars.ToDecimal(b_volume) : 0m;
                decimal nm_volume = m_volume.IsValid() ? CPubVars.ToDecimal(m_volume) : 0m;
                decimal ns_volume = s_volume.IsValid() ? CPubVars.ToDecimal(s_volume) : 0m;

                decimal unitVolume = 0m;
                if (unit_no == b_unit_no)
                {
                    unitVolume = nb_volume;
                    if (unitVolume == 0)
                    {
                        if (m_unit_factor != "" && nm_volume > 0) unitVolume = nm_volume / CPubVars.ToDecimal(m_unit_factor) * CPubVars.ToDecimal(b_unit_factor);
                        else if (ns_volume > 0) unitVolume = ns_volume * CPubVars.ToDecimal(b_unit_factor);
                    }
                }
                else if (unit_no == m_unit_no)
                {
                    unitVolume = nm_volume;
                    if (unitVolume == 0)
                    {
                        if (b_unit_factor != "" && nb_volume > 0) unitVolume = nb_volume / CPubVars.ToDecimal(b_unit_factor) * CPubVars.ToDecimal(m_unit_factor);
                        else if (ns_volume > 0) unitVolume = ns_volume * CPubVars.ToDecimal(m_unit_factor);
                    }
                }
                else if (unit_no == s_unit_no)
                {
                    unitVolume = ns_volume;
                    if (unitVolume == 0)
                    {
                        if (b_unit_factor != "" && nb_volume > 0) unitVolume = nb_volume / CPubVars.ToDecimal(b_unit_factor);
                        else if (m_unit_factor != "" && nm_volume > 0) unitVolume = nm_volume / CPubVars.ToDecimal(m_unit_factor);
                    }
                }
                return Math.Round(unitVolume, 3);
            }
        }
        public string client_item_code
        {
            get
            {
                return !string.IsNullOrEmpty(store_item_code) ? store_item_code : !string.IsNullOrEmpty(acct_item_code) ? acct_item_code : "";
            }
        }
        //  [FromFld("unit_from_s_to_bms(stock_qty::real, b_unit_factor::real, m_unit_factor::real, s_unit_factor::real, b_unit_no, m_unit_no, s_unit_no) as stock_qty_unit", LOAD_PURPOSE.SHOW)] public string stock_qty_unit { set; get; }
        // [FromFld(LOAD_PURPOSE.APPROVE)] public decimal stock_qty { get; set; }
        public virtual string trade_type { get; set; } = "";
        public virtual string attr_qty { get; set; }
        public virtual string sale_print_combine_attr { get; set; } = "";//合并打印多口味

        internal bool attrRememberPrice = false;

        internal decimal give_quantity = 0m, sale_quantity = 0m;//汇总单使用，不需要序列化，如果不加internal，参与序列化，手机端提交时容易导致""到single的转换导致报错

        internal decimal old_total_qty = 0;
        //  internal decimal old_cost_amt = 0;
        //internal decimal old_cost_price_avg = 0;
        //internal bool old_item_cost_price_suspect = false;
        internal bool item_cost_price_suspect = false;


        public string retail_price
        {
            get
            {
                if (unit_no == b_unit_no) return b_retail_price;
                else if (unit_no == m_unit_no) return m_retail_price;
                else if (unit_no == s_unit_no) return s_retail_price;
                return "";
            }
        }

        public string s_retail_price_unit
        {
            get
            {
                return CPubVars.FormatMoney(this.s_retail_price, 4) + "/" + s_unit_no;
            }
        }
        public string b_real_price
        {
            get
            {
                if (!b_unit_factor.IsValid()) return "";
                return CPubVars.FormatMoney(this.real_price / this.unit_factor * CPubVars.ToDecimal(this.b_unit_factor), 2);
            }
        }
        public string m_real_price
        {
            get
            {
                if (!m_unit_factor.IsValid()) return "";
                return CPubVars.FormatMoney(this.real_price / this.unit_factor * CPubVars.ToDecimal(this.m_unit_factor), 4);
            }
        }
        public string s_real_price
        {
            get
            {
                return CPubVars.FormatMoney(this.real_price / this.unit_factor, 4);
            }
        }
        public string s_real_price_unit
        {
            get
            {
                return CPubVars.FormatMoney(this.real_price / this.unit_factor, 4) + "/" + s_unit_no;
            }
        }

		public string quantity_trade
		{
			get
			{
				string s = "";
				if (this.trade_type == "J") s = "借";
				else if (this.quantity < 0) s = "退";
				return s + Math.Abs(this.quantity);
			}
		}
		public string qty_unit_trade
		{
			get
			{
				string s = "";
				if (this.trade_type == "J") s = "借";
				else if (this.quantity < 0) s = "退";
				return s + this.qty_unit.Replace("-", "");
			}
		}

		public string quantity_unit_conv_trade
		{
			get
			{
				string s = "";
				if (this.trade_type == "J") s = "借";
				else if (this.quantity < 0) s = "退";
				return s + SheetBase<SheetRowItem>.GetUnitQty(Math.Abs(quantity * unit_factor), b_unit_no, m_unit_no, s_unit_no, b_unit_factor, m_unit_factor);
			}
		}

		public override void SetInfoForPrint(bool smallUnitBarcode)
        {
            base.SetInfoForPrint(smallUnitBarcode);
            var row = this;
            if (row.orig_price.IsValid())
            {
                //row.orig_amount = CPubVars.FormatMoney(CPubVars.ToDecimal(row.orig_price) * row.quantity, 2);
                row.discount_amount = CPubVars.FormatMoney((CPubVars.ToDecimal(row.orig_price) - row.real_price) * row.quantity, 2);
                var nOrigPrice = CPubVars.ToDecimal(row.orig_price);
                if (nOrigPrice != 0 && row.discount.IsInvalid())
                    row.discount = CPubVars.FormatMoney(row.real_price / nOrigPrice * 100, 1);
            }

            //   if(row.b_unit_factor.IsValid())
            //     row.b_real_price =CPubVars.FormatMoney(row.real_price / row.unit_factor * CPubVars.ToDecimal(row.b_unit_factor),2);

            // row.s_real_price = CPubVars.FormatMoney(row.real_price / row.unit_factor, 2);
            // row.s_sub_qty =row.quantity * row.unit_factor;
            // row.s_sub_qty_unit = CPubVars.FormatMoney(row.quantity * row.unit_factor,2)+row.s_unit_no;

        }
    }
    public class SheetMM<TROW> : SheetBase<TROW> where TROW : SheetRowMM, new()
    {
        [SaveToDB][FromFld] public override SHEET_TYPE sheet_type { get; set; }
        public virtual string order_sheet_id { get; set; } = "";

        // public string order_table_name = "";
        [SaveToDB][FromFld] public string supcust_id { get; set; } = "";

        [SaveToDB][FromFld] public string acct_supcust_id { get; set; } = "";

       // [SaveToDB] public string submit_time { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string supcust_no { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_alias { get; set; } = "";
        [FromFld("supcust_remark",LOAD_PURPOSE.SHOW)] public string sup_remark { get; set; } = "";
        [FromFld("region_name",LOAD_PURPOSE.SHOW)] public string sup_region_name { get; set; } = "";
        [FromFld("group_name",LOAD_PURPOSE.SHOW)] public string sup_group_name { get; set; } = "";
        [FromFld("c.acct_type", LOAD_PURPOSE.SHOW)] public string acct_type { get; set; } = "";
       public string acct_type_name
        {
            get
            {
                if (acct_type=="pay") return "现结";
                else if (acct_type == "arrears") return "欠款";
                else return "";
            }
        }

        [FromFld(LOAD_PURPOSE.SHOW)] public string acct_way_name { get; set; } = "";

        public string acct_type_way
		{
			get
			{
                if (acct_way_name.IsValid()) return acct_way_name;
                else return acct_type_name; 
            }
		}
        public string sht_acct_type
        {
            get
            {
                if ( now_pay_amount <= 0.01m)
                {
                    return "欠款";
                }
                else if (total_amount - now_disc_amount - now_pay_amount < 0.01m)
                {
                    return "现结";
                }
                else return $"结{now_pay_amount}元, 欠{total_amount - now_disc_amount - now_pay_amount}元";
            }
        }
        [SaveToDB] [FromFld] public string branch_id { get; set; } = "";
        
        [FromFld(LOAD_PURPOSE.SHOW)] public string branch_name { get; set; } = "";
        [SaveToDB] [FromFld] public string department_id { get; set; } = "";
        [FromFld("depart_name", LOAD_PURPOSE.SHOW)] public string department_name { get; set; } = "";

        public string send_van_id { get; set; } = "";
        public string send_van_name { get; set; } = "";
        [SaveToDB] [FromFld] public override int money_inout_flag { get; set; }
        [SaveToDB] [FromFld] public override decimal total_amount { get; set; }

        public decimal to_get_money_amount
        {
            get
            {
                decimal amt = 0m;
                if (this.payway1_type == "QT") amt += this.payway1_amount;
                if (this.payway2_type == "QT") amt += this.payway2_amount;
                if (this.payway3_type == "QT") amt += this.payway3_amount;
                return amt;// decimal.Round(total_amount - order_item_amount - prepay_amount, 2);
            }
        }

		public decimal ks_amount
		{
			get
			{
				decimal amt = 0m;
				foreach(var row in this.SheetRows)
                {
                    if (row.trade_type == "KS")
                    {
                        amt += row.sub_amount;
					}
                }
				return amt;
			}
		}
		public decimal no_ks_amount
		{
			get
			{
				decimal amt = 0m;
				foreach (var row in this.SheetRows)
				{
					if (row.inout_flag!=0)
					{
						amt += row.sub_amount;
					}
                    else if (row.trade_type == "KS")
                    {
                        amt -= row.sub_amount;
                    }
				}
				return amt;
			}
		}
		public decimal order_item_amount
        {
            get
            {
                decimal orderAmt = 0m;
                if (payway1_is_order.IsValid() && payway1_is_order.ToLower() == "true") orderAmt += payway1_amount;
                if (payway2_is_order.IsValid() && payway2_is_order.ToLower() == "true") orderAmt += payway2_amount;
                if (payway3_is_order.IsValid() && payway3_is_order.ToLower() == "true") orderAmt += payway3_amount;
                return decimal.Round(orderAmt, 2);
            }
        }
        public string total_cn_wan
        {
            get
            {
                int wan = (int)(total_amount / 10000);
                if (wan == 0) return "";
                string s = MoneyToUpper(wan.ToString()).Replace("圆", "").Replace("整", "");
                return s;
            }
        }
        public string cent_amount_cn_qian
        {
            get
            {
                decimal y = total_amount % 10000;
                string s1 = CPubVars.FormatMoney(y, 2, true);
                string s = MoneyToUpper(s1, true);

                for (int i = s.Length; i < 6; i++)
                {
                    s = "零" + s;
                }
                return s;
            }
        }
        [SaveToDB][FromFld] public string payway1_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_type { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_usage { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_is_order { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway1_amount { get; set; }
        [SaveToDB][FromFld] public string payway2_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_type { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_usage { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_is_order { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway2_amount { get; set; }

        [SaveToDB][FromFld] public string payway3_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway3_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway3_type { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway3_usage { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway3_is_order { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway3_amount { get; set; }

        [SaveToDB][FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB][FromFld] public decimal now_disc_amount { get; set; }
        [SaveToDB][FromFld] public virtual decimal prepay_amount { get; set; }

        public string prepay_balance { get; set; } = "";
        public string order_item_balance { get; set; } = "";
              
        public string arrears_balance { get; set; } = "";
        public string total_cent
        {
            get
            {
                return CPubVars.FormatMoney(total_amount * 100, 0);
            }
        }
        public string happen_year
        {
            get
            {
                if (this.happen_time == "") return "";
                return DateTime.Parse(this.happen_time).Year.ToString();
            }
        }
        public string happen_month
        {
            get
            {
                if (this.happen_time == "") return "";
                return DateTime.Parse(this.happen_time).Month.ToString();
            }
        }
        public string happen_day
        {
            get
            {
                if (this.happen_time == "") return "";
                return DateTime.Parse(this.happen_time).Day.ToString();
            }
        }
        public string arrears_pre_balance
        {
            get
            {
                if (arrears_balance.IsValid())
                {
                    decimal n = CPubVars.ToDecimal(arrears_balance);
                    n -= this.left_amount;
                    string s = CPubVars.FormatMoney(n, 2);
                    return s;
                }
                return "";
            }
        }
        public string payway1
        {
            get
            {
                string payAmount = "";
                if (payway1_amount != 0) payAmount = CPubVars.FormatMoney(payway1_amount, 2, true);

                if (payway1_name != "")
                {
                    return payway1_name + ":" + payAmount;
                }
                return "";
            }
        }
        public string payway2
        {
            get
            {
                string payAmount = "";
                if (payway2_amount != 0) payAmount = CPubVars.FormatMoney(payway2_amount, 2, true);

                if (payway2_name != "")
                {
                    return payway2_name + ":" + payAmount;
                }
                return "";
            }
        }
        public string payway3
        {
            get
            {
                string payAmount = "";
                if (payway3_amount != 0) payAmount = CPubVars.FormatMoney(payway3_amount, 2, true);

                if (payway3_name != "")
                {
                    return payway3_name + ":" + payAmount;
                }
                return "";
            }
        }
        public string payway
        {
            get
            {
                string s = "";
                if (payway1_amount != 0)
                {
                    s = $"{payway1_name}:{CPubVars.FormatMoney(payway1_amount, 2, true)}";
                }
                if (payway2_amount != 0)
                {
                    s += $"  {payway2_name}:{CPubVars.FormatMoney(payway2_amount, 2, true)}";
                }
                if (payway3_amount != 0)
                {
                    s += $"  {payway3_name}:{CPubVars.FormatMoney(payway3_amount, 2, true)}";
                }
                return s;
            }
        }
        //public decimal no_disc_amount { get { return CPubVars.ToDecimal(CPubVars.FormatMoney(total_amount - now_disc_amount, 2)); } }
        public string no_disc_amount { get { return CPubVars.ToDecimal(CPubVars.FormatMoney(total_amount - now_disc_amount, 2)).ToString(); } }

        public decimal left_amount
        {
            get
            {
                return CPubVars.ToDecimal(CPubVars.FormatMoney(total_amount - now_disc_amount - now_pay_amount, 2));
            }
        }//这里需要保留,和leftAmount有区别，这里是当次欠款
        public decimal ImportNowLeftAmount { get; set; }
        public decimal real_get_amount { get { return CPubVars.ToDecimal(CPubVars.FormatMoney(now_pay_amount - prepay_amount, 2)); } set { CPubVars.ToDecimal(CPubVars.FormatMoney(now_pay_amount - prepay_amount, 2)); } }//实收金额

        [SaveToDB][FromFld] public decimal paid_amount { get; set; }
        [SaveToDB][FromFld] public decimal disc_amount { get; set; }
        [SaveToDB][FromFld] public string seller_id { get; set; } = "";
        //[FromFld("sheet_attribute->>'acctCustID' as acct_cust_id")] public string acct_cust_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string acct_cust_name { get; set; } = "";
        [SaveToDB][FromFld] public override string is_imported { get; set; } = "";
        private decimal _return_amount=0m;
        public decimal return_amount { 
            get
            {
                if (this.SheetType == "T" || this.SheetType == "TD"|| this.SheetType == "CT" || this.SheetType == "CTD") return total_amount;
                else return _return_amount;
            }
        }
            
        
        public string appendix_photos  { get; set; } = "";
        public string display_give_proofs = "";
        public bool promotion_fulldisc_used = false;
        public string promotion_fullgift_content = "";
        // 小程序相关，下单人，优惠详情
        public string wx_user_id { get; set; } = "";
        public string sheet_attr_fulldiscs { get; set; } = "";
        public string sheet_attr_redpacket { get; set; } = "";
        public string defaultUnit { get; set; } = "";
        public Dictionary<string, string> OtherSheetAttributes = null;
        public string prepay_sheet_info { get; set; } = "";
        [SaveToDB]
        [FromFld]
        public virtual string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();

                decimal returnAmount = 0m;
                if (prepay_sheet_info.IsValid())
                {
                    sheetAttribute.Add("prepaySheetInfo", prepay_sheet_info);
                }
                foreach (var row in SheetRows)
                {

                    if (row.sys_price != "" && row.sys_price != null && !sheetAttribute.ContainsKey("bj") && row.sys_price != "NaN")//变价
                    {
                        decimal sysPrice = CPubVars.ToDecimal(row.sys_price);
                        if (sysPrice != row.real_price) sheetAttribute.Add("bj", "true");
                    }
                    if (row.quantity < 0) returnAmount += -row.sub_amount;
                    if (row.trade_type == "CL" && !sheetAttribute.ContainsKey("cl"))
                    {
                        sheetAttribute.Add("cl", "true");
                    }
                    else if (row.trade_type == "DH" && !sheetAttribute.ContainsKey("dh"))
                    {
                        sheetAttribute.Add("dh", "true");
                    }
                    else if (row.sub_amount == 0 && row.quantity != 0 && !sheetAttribute.ContainsKey("free")) sheetAttribute.Add("free", "true");

                }

                if (promotion_fulldisc_used)
                {
                    sheetAttribute.Add("promotion_fulldisc_used", "true");
                }
                if (promotion_fullgift_content.IsValid())
                {
                    sheetAttribute.Add("promotion_fullgift_content", promotion_fullgift_content);
                }

                if (returnAmount > 0)
                {
                    sheetAttribute.Add("returnAmt", CPubVars.FormatMoney(returnAmount));
                }
                if (appendix_photos != "" && appendix_photos != "[]")
                {
                    sheetAttribute.Add("appendixPhotos", appendix_photos);
                }
                if (display_give_proofs != "" && display_give_proofs != "[]")
                {
                    sheetAttribute.Add("displayGiveProofs", display_give_proofs);
                }
                if (!TempHappenTime)
                {
                    sheetAttribute.Add("tempHappenTime", "false");
                }
                if (OtherSheetAttributes != null)
                {
                    foreach (var k in OtherSheetAttributes)
                    {
                        if (!sheetAttribute.ContainsKey(k.Key))
                            sheetAttribute.Add(k.Key, k.Value);
                    }
                }

               // if (acct_cust_id != "") sheetAttribute.Add("acctCustID", acct_cust_id);
                if (send_van_id != "") sheetAttribute.Add("vanID", send_van_id);
                if (send_van_name != "") sheetAttribute.Add("vanName", send_van_name);
                if (defaultUnit != "") sheetAttribute.Add("defaultUnit", defaultUnit);
                
                if (!string.IsNullOrEmpty(wx_user_id))
                {
                    sheetAttribute.Add("wx_user_id", wx_user_id);
                }
                if (!string.IsNullOrEmpty(sheet_attr_fulldiscs))
                {
                    sheetAttribute.Add("sheet_attr_fulldiscs", sheet_attr_fulldiscs);
                }
                if (!string.IsNullOrEmpty(sheet_attr_redpacket))
                {
                    sheetAttribute.Add("sheet_attr_redpacket", sheet_attr_redpacket);
                }
                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);

                return s;
            }
            set
            {

                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.returnAmt != null)
                    {
                        this._return_amount = CPubVars.ToDecimal(sheetAttr.returnAmt.ToString());
                    }


                    if (sheetAttr.promotion_fulldisc_used != null)
                    {
                        this.promotion_fulldisc_used = sheetAttr.promotion_fulldisc_used;
                    }
                    string pfc = sheetAttr.promotion_fullgift_content;
                    if (pfc.IsValid())
                    {
                        this.promotion_fullgift_content = pfc;
                    }

                    if (sheetAttr.appendixPhotos != null)
                    {
                        this.appendix_photos = sheetAttr.appendixPhotos;
                    }
                    if (sheetAttr.displayGiveProofs != null)
                    {
                        this.display_give_proofs = sheetAttr.displayGiveProofs;
                    }
                    if (sheetAttr.tempHappenTime != null)
                    {
                        this.TempHappenTime = sheetAttr.tempHappenTime != "false";
                    }
                    if (sheetAttr.vanID != null)
                    {
                        this.send_van_id = sheetAttr.vanID;
                    }

                    if (sheetAttr.vanName != null)
                    {
                        this.send_van_name = sheetAttr.vanName;
                    }
                    if (sheetAttr.wx_user_id != null)
                    { 
                        this.wx_user_id = sheetAttr.wx_user_id;
                    }
                    if (sheetAttr.sheet_attr_fulldiscs != null)
                    {
                        this.sheet_attr_fulldiscs = sheetAttr.sheet_attr_fulldiscs;
                    }
                    if (sheetAttr.sheet_attr_redpacket != null)
                    { 
                        this.sheet_attr_redpacket = sheetAttr.sheet_attr_redpacket;
                    }
                    if (sheetAttr.defaultUnit != null)
                    {
                        this.defaultUnit = sheetAttr.defaultUnit;
                    }
                    if(sheetAttr.total_weight != null)
                    {
                        this.total_weight = CPubVars.ToDecimal(sheetAttr.total_weight.ToString());
                    }
                    if(sheetAttr.total_volume != null)
                    {
                        this.total_volume = CPubVars.ToDecimal(sheetAttr.total_volume.ToString());
                    }
         
                    if (sheetAttr.prepaySheetInfo != null)
                    {
                        this.prepay_sheet_info = sheetAttr.prepaySheetInfo;
                    }
                }
            }
        }

		public string sum_qty_unit = "";
        public string sum_qty_unit_trade = "";
        public string total_qty_unit = "";
		public string sum_s_sub_qty_unit = "";
		public string sum_s_sub_qty = "";
		public string sum_quantity = "";
		public string sum_quantity_trade = "";
        public string sum_quantity_unit_conv_trade = "";
		[SaveToDB][FromFld] public string total_quantity { get; set; } = "";

        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";

        [FromFld(LOAD_PURPOSE.SHOW)] public string mobile { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_addr { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string boss_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_mobile { get; set; } = "";

        public decimal PrepayAmount = 0;

        public string sum_give_quantity_unit_conv = "";
        public string sum_sale_quantity_unit_conv = "";
        public string sum_item_name = "";
        public string sum_sub_amount = "";
        public decimal total_weight
        {
            get
            {
                decimal d = 0m;
                foreach (var row in SheetRows)
                {
                    d += row.weight;
                }
                return d;
            }
            set
            {

            }
        }
        public decimal total_qty_num
        {
            get
            {
                decimal d = 0m;
                foreach (var row in SheetRows)
                {
                    if (row.inout_flag != 0)
                        d += row.quantity;
                }
                return d;
            }
        }
        class ItemUnit
        {
            public decimal Factor;
            public decimal Qty = 0;
        }
        public string total_sale_qty_unit
        {
            get
            {
                return GetTotalQtyUnit(false);
            }
        }
        public string total_return_qty_unit
        {
            get
            {
                return GetTotalQtyUnit(true);
            }
        }
		private string GetTotalQtyUnit(bool bReturn)
		{
			// 使用泛型字典存储单位及其对应的ItemUnit对象
			var unitDictionary = new Dictionary<string, ItemUnit>();

			// 显式维护插入顺序的列表（.NET 8推荐方式）
			var insertionOrder = new List<string>();

			foreach (var row in this.SheetRows)
			{
				// 条件判断优化（避免重复计算）
				if (row.inout_flag == 0 || isRedAndChange) continue;

				// 计算符号判断逻辑
				var quantitySign = row.quantity * row.inout_flag;
				var shouldProcess = bReturn ? quantitySign > 0 : quantitySign < 0;

				if (!shouldProcess) continue;

				// 获取单位标识（添加空值保护）
				var unitKey = row.unit_no ?? string.Empty;

				// 字典操作优化（TryGetValue模式）
				if (!unitDictionary.TryGetValue(unitKey, out var itemUnit))
				{
					itemUnit = new ItemUnit
					{
						Factor = 1,
						Qty = 0
					};

					unitDictionary.Add(unitKey, itemUnit);
					insertionOrder.Add(unitKey); // 记录首次出现的顺序
				}

				// 累加数量（明确使用 decimal 类型）
				itemUnit.Qty += row.quantity;
			}

			// 使用StringBuilder优化字符串拼接
			var resultBuilder = new StringBuilder(capacity: insertionOrder.Count * 10);

			foreach (var unitKey in insertionOrder)
			{
				if (unitDictionary.TryGetValue(unitKey, out var unit))
				{
					// 格式化优化（避免多次调用）
					var formattedValue = CPubVars.FormatMoney(Math.Abs(unit.Qty), 3);
					resultBuilder.Append(formattedValue).Append(unitKey);
				}
			}

			return resultBuilder.ToString();
		}
		private string GetTotalQtyUnit_old(bool bReturn)
        {
            Dictionary<string, ItemUnit> units = new Dictionary<string, ItemUnit>();
            var _sheetRows = this.SheetRows;
            if (bReturn)
            {
                _sheetRows = new List<TROW>();
				foreach (var row in this.SheetRows)
                {
                    if(row.quantity * row.inout_flag > 0)
                    {
						_sheetRows.Add(row);
                    }
                }
            }

			var mergedRows = MergeSheetRows(_sheetRows, false);
            foreach (var row in mergedRows)
            {
                bool bMet = false;
                if (bReturn) bMet = row.quantity*row.inout_flag> 0; 
                else bMet = row.quantity * row.inout_flag < 0;
                if (row.inout_flag!= 0 && bMet&&!isRedAndChange)
                {
                    decimal qty = row.quantity;
                    if (row.b_quantity != 0&& row.b_unit_no!=null)
                    {
                        if (!units.ContainsKey(row.b_unit_no)) {
                            units[row.b_unit_no] = new ItemUnit() { Factor = CPubVars.ToDecimal(row.b_unit_factor),Qty = 0 }; 
                        } 
                        units[row.b_unit_no].Qty += row.b_quantity;
                    }
                    if (row.m_quantity != 0 && row.m_unit_no != null)
                    {
                        if (!units.ContainsKey(row.m_unit_no))
                        {
                            units[row.m_unit_no] = new ItemUnit() { Factor = CPubVars.ToDecimal(row.m_unit_factor), Qty = 0 };

                        }
                        units[row.m_unit_no].Qty += row.m_quantity;
                    }
                    if (row.s_quantity != 0 && row.s_unit_no != null)
                    {
                        if (!units.ContainsKey(row.s_unit_no))
                        {
                            units[row.s_unit_no] = new ItemUnit() { Factor = 1, Qty = 0 };

                        }
                        units[row.s_unit_no].Qty += row.s_quantity;
                    }
                }
            }
            var lst = units.ToList();
            lst.Sort((a, b) =>
            {
                return b.Value.Factor.CompareTo(a.Value.Factor);
            });
            string total_qty = "";
            foreach (var u in lst)
            {
                total_qty += CPubVars.FormatMoney(Math.Abs(u.Value.Qty), 3) + u.Key;
            }
            return total_qty;
        }
        public string total_sale_qty_unit_type
        {
            get
            {
                return GetTotalQtyUnitType(false);
            }
        }
        public string total_return_qty_unit_type
        {
            get
            {
                return GetTotalQtyUnitType(true);
            }
        }
        private string GetTotalQtyUnitType(bool bReturn)
        {
            Dictionary<string, ItemUnit> units = new Dictionary<string, ItemUnit>();
			var _sheetRows = this.SheetRows;
			if (bReturn)
			{
                _sheetRows = new List<TROW>();
				foreach (var row in this.SheetRows)
				{
					if (row.quantity * row.inout_flag > 0)
					{
						_sheetRows.Add(row);
					}
				}
			}
			var mergedRows = MergeSheetRows(_sheetRows, false);
            foreach (var row in mergedRows)
            {
                bool bMet = false;
                if (bReturn) bMet = row.quantity * row.inout_flag > 0;
                else bMet = row.quantity * row.inout_flag < 0;
                if (row.inout_flag != 0 && bMet)
                {
                    decimal qty = row.unit_factor * row.quantity;
                    string unitType = "";
                    if (row.b_quantity != 0)
                    {
                        unitType = "大";
                        if (!units.ContainsKey(unitType))
                        {
                            units[unitType] = new ItemUnit() { Factor = 3, Qty = 0 };
                        }
                        units[unitType].Qty += row.b_quantity;
                    }
                    if (row.m_quantity != 0)
                    {
                        unitType = "中";
                        if (!units.ContainsKey(unitType))
                        {
                            units[unitType] = new ItemUnit() { Factor = 2, Qty = 0 };

                        }
                        units[unitType].Qty += row.m_quantity;
                    }
                    if (row.s_quantity != 0)
                    {
                        unitType = "小";
                        if (!units.ContainsKey(unitType))
                        {
                            units[unitType] = new ItemUnit() { Factor = 1, Qty = 0 };
                        }
                        units[unitType].Qty += row.s_quantity;
                    }
                }
            }
            var lst = units.ToList();
            lst.Sort((a, b) =>
            {
                return b.Value.Factor.CompareTo(a.Value.Factor);
            });
            string total_qty = "";
            foreach (var u in lst)
            {
                total_qty += CPubVars.FormatMoney(Math.Abs(u.Value.Qty), 3) + u.Key;
            }
            return total_qty;
        }
        public decimal total_volume
        {
            get
            {
                decimal d = 0m;
                foreach (var row in SheetRows)
                {
                    d += row.volume;
                }
                return d;
            }
            set
            {

            }
        }
        public decimal LeftAmount { get { return CPubVars.ToDecimal(CPubVars.FormatMoney(total_amount - paid_amount - disc_amount, 2)); } }
       
        public bool mall_allow_negative_stock_order { get; set; }
        
        // public bool RoleAllowNegativeStock = true;//移到下面
        // public bool BranchAllowNegativeStock = true;
        protected class CInfoForApprove : CInfoForApproveBase
        {
            // public string ArrearBalance = "";//, PrepayBalance = "";
            public decimal ChangeBal = 0;
            public decimal Balance = 0;
            public List<TROW> SheetRows = null;
            public dynamic CompanySetting = null;
            public List<Subject> PrepaySubjects = new List<Subject>();
            public bool RoleAllowNegativeStock = true;
            public bool BranchAllowNegativeStock = true;
            public string NegativeStockAccordance = "real";
            public bool RoleAllowNegativePrepay = false;
            public bool RoleAllowNoStockHH = true;//无库存换货
            public List<Subject> PaywaysInfo = null;
            public List<SheetRowMM> UnitPriceRows = new List<SheetRowMM>();
            public List<ExpandoObject> ItemOrderedRows = null;
            
            public string MoveToVanOpId = "";
            public string SheetPrintCount = "";
            public string ChargeSeller = "";
			//            public bool sellerauxiliary = false;


			//public bool supcustauxiliary = false;
		}


        protected class Subject
        {
            public string sub_id { get; set; } = "";
            public string sub_name { get; set; } = "";
            public string balance { get; set; } = "";
            public string sub_type { get; set; } = "";
            public string is_order { get; set; } = "";
        }
        public SheetMM(string mainTable, string detailTable, LOAD_PURPOSE loadPurpose) : base(mainTable, detailTable, loadPurpose)
        {
            if (loadPurpose == LOAD_PURPOSE.SHOW || red_flag == "2")
            {
                MainLeftJoin = @" 
left join info_branch b on t.branch_id=b.branch_id and b.company_id=~COMPANY_ID
left join info_supcust c on t.supcust_id=c.supcust_id and c.company_id=~COMPANY_ID
left join info_acct_way aw on c.acct_way_id=aw.acct_way_id and aw.company_id=~COMPANY_ID
left join (select oper_id,oper_name as seller_name,mobile as seller_mobile from info_operator where company_id=~COMPANY_ID) seller on t.seller_id=seller.oper_id
left join (select oper_id,oper_name as maker_name from info_operator where company_id=~COMPANY_ID) maker on t.maker_id=maker.oper_id
left join (select oper_id,oper_name as approver_name from info_operator where company_id=~COMPANY_ID) approver on t.approver_id=approver.oper_id
left join (select sub_id,sub_name as payway1_name,sub_type payway1_type,is_order payway1_is_order,usage payway1_usage from cw_subject where company_id=~COMPANY_ID) pw1 on t.payway1_id=pw1.sub_id
left join (select sub_id,sub_name as payway2_name,sub_type payway2_type,is_order payway2_is_order,usage payway2_usage from cw_subject where company_id=~COMPANY_ID) pw2 on t.payway2_id=pw2.sub_id
left join (select sub_id,sub_name as payway3_name,sub_type payway3_type,is_order payway3_is_order,usage payway3_usage from cw_subject where company_id=~COMPANY_ID) pw3 on t.payway3_id=pw3.sub_id
left join (select supcust_id cust_id,sup_name acct_cust_name from info_supcust where company_id=~COMPANY_ID) cust on cust.cust_id::text = t.sheet_attribute->>'acctCustID'
left join info_region rg on rg.company_id=~COMPANY_ID and c.region_id=rg.region_id
left join info_supcust_group isg on isg.company_id=~COMPANY_ID and c.sup_group=isg.group_id
left join info_department dpt on dpt.company_id=~COMPANY_ID and t.department_id=dpt.depart_id 
                                 
                ";

                DetailLeftJoin = $@"
  left join {mainTable} m on t.sheet_id=m.sheet_id and m.company_id=~COMPANY_ID 
  left join (select brief_id as remark_id from info_sheet_detail_brief where company_id=~COMPANY_ID) b on b.remark_id=t.remark_id
  left join info_item_prop i on t.item_id=i.item_id and i.company_id=~COMPANY_ID
  left join info_supcust cust on m.supcust_id=cust.supcust_id and cust.company_id=~COMPANY_ID
  left join info_client_item_code cic on t.item_id=cic.item_id and m.supcust_id = cic.client_id and cic.company_id=~COMPANY_ID
  left join info_client_item_code cic_acct on t.item_id=cic_acct.item_id and cust.acct_cust_id = cic_acct.client_id and cic_acct.company_id=~COMPANY_ID 
  left join (select item_id,mum_attributes as son_mum_attributes from info_item_prop where company_id =~COMPANY_ID) son_attrs on case when i.son_mum_item is null then i.item_id else i.son_mum_item end=son_attrs.item_id 
  left join info_item_brand ib on i.item_brand=ib.brand_id and ib.company_id=~COMPANY_ID
  left join (select class_id classId,class_name,order_index as class_order_index,general_class as gen_class2 from info_item_class where company_id =~company_id) ic on i.item_class=ic.classId 
  left join (select class_id classId1,class_name as class1_name,order_index as class1_order_index, general_class as gen_class1 from info_item_class where company_id =~company_id) ic1 on text_to_int(split_part(i.other_class,'/', 3)) = ic1.classId1
  left join 
  (
      select item_id,s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,s_unit->>'f3' as s_barcode,s_unit->>'f4' as s_retail_price,s_unit->>'f5' as s_lowest_price,s_unit->>'f6' as s_weight,s_unit->>'f7' as s_volume,s_unit->>'f8' as s_wholesale_price,
                     m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,m_unit->>'f3' as m_barcode,m_unit->>'f4' as m_retail_price,m_unit->>'f5' as m_lowest_price,m_unit->>'f6' as m_weight,m_unit->>'f7' as m_volume,m_unit->>'f8' as m_wholesale_price,
                     b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor,b_unit->>'f3' as b_barcode,b_unit->>'f4' as b_retail_price,b_unit->>'f5' as b_lowest_price,b_unit->>'f6' as b_weight,b_unit->>'f7' as b_volume,b_unit->>'f8' as b_wholesale_price
      from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,retail_price,lowest_price,weight,volume,wholesale_price)) as json from info_item_multi_unit where company_id=~company_id order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)
  )
  unit_barcode on t.item_id=unit_barcode.item_id     
  left join info_supcust sup on i.supplier_id=sup.supcust_id and sup.company_id=~COMPANY_ID
  left join info_manufactor manu on i.manufactor_id=manu.manufactor_id and manu.company_id=~COMPANY_ID

  left join company_setting cs on cs.company_id=~COMPANY_ID
  left join (select batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date from info_item_batch where company_id= ~COMPANY_ID) itb on itb.batch_id = t.batch_id
  left join info_branch ibb on ibb.branch_id = t.branch_id
  left join info_branch_position ibp on ibp.branch_id =COALESCE(t.branch_id,m.branch_id) and ibp.branch_position = COALESCE(t.branch_position,0)
                                     ";



                //  left join stock on t.item_id=stock.item_id and t.company_id=stock.company_id and m.branch_id=stock.branch_id //在页面onMMSheetBranched事件中获取库存数，这里就不用了

            }
            else if (loadPurpose == LOAD_PURPOSE.APPROVE)
                DetailLeftJoin = @$" left join stock on t.item_id=stock.item_id and t.company_id=stock.company_id and COALESCE(t.branch_id,m.branch_id)=stock.branch_id and COALESCE(t.branch_position,0) = stock.branch_position and COALESCE(t.batch_id,0) = stock.batch_id
left join (select batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date from info_item_batch where company_id= ~COMPANY_ID) itb on itb.batch_id = t.batch_id
left join info_branch ibb on ibb.branch_id = t.branch_id
left join info_branch_position ibp on ibp.branch_id =COALESCE(t.branch_id,m.branch_id) and ibp.branch_position = COALESCE(t.branch_position,0)
                                    ";
            else if(loadPurpose == LOAD_PURPOSE.SHOW_OR_APPROVE)
                DetailLeftJoin = $@"
  left join {mainTable} m on t.sheet_id=m.sheet_id and m.company_id=~COMPANY_ID 
  left join (select brief_id as remark_id from info_sheet_detail_brief where company_id=~COMPANY_ID) b on b.remark_id=t.remark_id
  left join info_item_prop i on t.item_id=i.item_id and i.company_id=~COMPANY_ID
  left join info_supcust cust on m.supcust_id=cust.supcust_id and cust.company_id=~COMPANY_ID
  left join info_client_item_code cic on t.item_id=cic.item_id and m.supcust_id = cic.client_id and cic.company_id=~COMPANY_ID
  left join info_client_item_code cic_acct on t.item_id=cic_acct.item_id and cust.acct_cust_id = cic_acct.client_id and cic_acct.company_id=~COMPANY_ID 
  left join (select item_id,mum_attributes as son_mum_attributes from info_item_prop where company_id =~COMPANY_ID) son_attrs on case when i.son_mum_item is null then i.item_id else i.son_mum_item end=son_attrs.item_id 
  left join info_item_brand ib on i.item_brand=ib.brand_id and ib.company_id=~COMPANY_ID
  left join (select class_id classId,class_name,order_index as class_order_index,general_class as gen_class2 from info_item_class where company_id =~company_id) ic on i.item_class=ic.classId 
  left join (select class_id classId1,class_name as class1_name,order_index as class1_order_index, general_class as gen_class1 from info_item_class where company_id =~company_id) ic1 on text_to_int(split_part(i.other_class,'/', 3)) = ic1.classId1
  left join 
  (
      select item_id,s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,s_unit->>'f3' as s_barcode,s_unit->>'f4' as s_retail_price,s_unit->>'f5' as s_lowest_price,s_unit->>'f6' as s_weight,s_unit->>'f7' as s_volume,s_unit->>'f8' as s_wholesale_price,
                     m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,m_unit->>'f3' as m_barcode,m_unit->>'f4' as m_retail_price,m_unit->>'f5' as m_lowest_price,m_unit->>'f6' as m_weight,m_unit->>'f7' as m_volume,m_unit->>'f8' as m_wholesale_price,
                     b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor,b_unit->>'f3' as b_barcode,b_unit->>'f4' as b_retail_price,b_unit->>'f5' as b_lowest_price,b_unit->>'f6' as b_weight,b_unit->>'f7' as b_volume,b_unit->>'f8' as b_wholesale_price,
      from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,retail_price,lowest_price,weight,volume,wholesale_price)) as json from info_item_multi_unit where company_id=~company_id order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)
  )
  unit_barcode on t.item_id=unit_barcode.item_id     
  left join info_supcust sup on i.supplier_id=sup.supcust_id and sup.company_id=~COMPANY_ID
  left join info_manufactor manu on i.manufactor_id=manu.manufactor_id and manu.company_id=~COMPANY_ID
  --left join (select company_id, manufactor_id , manufactor_name from info_manufactor where company_id=~COMPANY_ID )manu1 on t.manufactor_id=manu1.manufactor_id and manu1.company_id=~COMPANY_ID
  left join company_setting cs on cs.company_id=~COMPANY_ID
left join (select batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date from info_item_batch where company_id= ~COMPANY_ID) itb on itb.batch_id = t.batch_id
left join info_branch ibb on ibb.branch_id = t.branch_id
 left join info_branch_position ibp on ibp.branch_id =COALESCE(t.branch_id,m.branch_id) and ibp.branch_position = COALESCE(t.branch_position,0)
";
           
        }

        protected override void InitForSave()
        {
            base.InitForSave();
            if (seller_id == "" || seller_id == "-1") seller_id = OperID;
            paid_amount = now_pay_amount;
            disc_amount = now_disc_amount;
        }
        protected override void NeedUpdateClientHistory(out string supcustID, out bool updateArrears, out string updatePrepaySubIDs)
        {
            supcustID = supcust_id;
            updateArrears = true;
            updatePrepaySubIDs = "";
            if (payway1_type == "YS" || payway1_type == "YF")
            {
                if (updatePrepaySubIDs != "") updatePrepaySubIDs += ',';
                updatePrepaySubIDs += payway1_id;
            }
            if (payway2_type == "YS" || payway2_type == "YF")
            {
                if (updatePrepaySubIDs != "") updatePrepaySubIDs += ',';
                updatePrepaySubIDs += payway2_id;
            }
            if (payway3_type == "YS" || payway3_type == "YF")
            {
                if (updatePrepaySubIDs != "") updatePrepaySubIDs += ',';
                updatePrepaySubIDs += payway3_id;
            }

        }
        protected override void GetInfoForSave_SetQQ(SQLQueue QQ)
        {
            string sql;
            base.GetInfoForSave_SetQQ(QQ);
            
            if (this.supcust_id != "" && this.supcust_id != "-1")
            {
                if (",CG,CT,CD,".Contains("," + this.SheetType + ","))
                {
                    this.acct_supcust_id = this.supcust_id;
                }
                else
                {
                    sql = $@"select acct_cust_id,charge_seller from info_supcust where company_id={this.company_id} and supcust_id={this.supcust_id};";
                    QQ.Enqueue("supcust",sql);
                }
            }
            if (string.IsNullOrEmpty(this.department_id) && !string.IsNullOrEmpty(this.seller_id))
            {
                sql = $@"SELECT depart_id FROM info_operator WHERE oper_id = {this.seller_id};";
                QQ.Enqueue("filter_by_seller_department", sql);

            }
        }
        
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForSave_ReadData(dr, sqlName,bForRed);

            if (sqlName == "supcust")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                // 2025.1.21 添加this.supcust_id == "0"的判断 防止选了散客提示用户不存在
                if (rec != null)
                { 
                    if (rec.acct_cust_id != "")
                    {
                        this.acct_supcust_id = rec.acct_cust_id;
                    }
                    else
                    {
                        this.acct_supcust_id = this.supcust_id;
                    }

                    info.ChargeSeller = rec.charge_seller;
                    /*if (this.acct_supcust_id != this.supcust_id && this.acct_supcust_id != rec.acct_cust_id)
                    {
                        info.ErrMsg = "该客户结算单位改变了,请重新选择客户";
                    }*/
                }
                else if(this.supcust_id != "0")
                {
                    info.ErrMsg = $"客户{sup_name}不存在，请检查";
                }
            }
            else if (sqlName == "filter_by_seller_department")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec != null && !string.IsNullOrEmpty(rec.depart_id))
                {
                    this.department_id = rec.depart_id;
                }
            }

        }



        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql;
            base.GetInfoForApprove_SetQQ(QQ);
            if (branch_id == "-1") FIXING_ARREARS = true;//导入应收款时，branch_id为-1

            //if (Math.Abs(total_amount - paid_amount - disc_amount) >= 0.001m || order_sheet_id != "")
            if (Math.Abs(total_amount - now_pay_amount - now_disc_amount) >= 0.001m || order_sheet_id != "")
            {
                sql = GetSqlForArrearsQQ(supcust_id, seller_id);
                QQ.Enqueue("arrear_balance", sql);
                sql = $"select sub_id from cw_subject where company_id={company_id} and sub_type='SK'";
                QQ.Enqueue("sub_id", sql);
            }

            string sub_ids = payway1_id;

            if (payway2_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway2_id;
            }
            if (payway3_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway3_id;
            }
            if (sub_ids != "")
            {
                string custID = supcust_id;
                if (acct_supcust_id != "") custID = acct_supcust_id;
                sql = $"select s.sub_id,sub_name,balance,sub_type,s.is_order from cw_subject s left join (select sub_id,balance from prepay_balance where supcust_id={custID}) b on s.sub_id=b.sub_id  where sub_type in ('YS','YF') and s.company_id ={company_id};";
                QQ.Enqueue("prepay_sub", sql); 
            }
            if (order_sheet_id.IsValid())
            {

            }
            

            string items_id = "";
            string batchs_id = "";
            string branchs_id = "";
            string branchs_position = "";
            foreach (SheetRowMM row in SheetRows)
            {
                if (items_id != "") items_id += ",";
                items_id += row.item_id;
                if (batchs_id != "") batchs_id += ",";
                if (row.batch_id == "无产期") row.batch_id = "0";
                batchs_id += row.batch_id.IsInvalid() ? "0" : row.batch_id;
                if (branchs_position != "") branchs_position += ",";
                branchs_position += row.branch_position.IsInvalid() ? "0" : row.branch_position;
                if (branchs_id != "") branchs_id += ",";
                branchs_id += row.branch_id.IsInvalid() ? branch_id : row.branch_id;
            }
            bool openPlaceholder = this.GetType().GetProperty("isOpenPlaceholderOrder") != null && this.GetType().GetProperty("isOpenPlaceholderOrder").GetValue(this).ToString().ToLower()=="true"?true:false;

            if (OperID != "" && (sheet_type != SHEET_TYPE.SHEET_SALE_DD || !openPlaceholder))
            {
                sql = $"select rights->'delicacy'->'allowNegativeStock'->'value' role_allow_negative_stock,rights->'delicacy'->'allowNegativePrepay'->'value' role_allow_negative_prepay,rights->'delicacy'->'allowNoStockHH'->'value' role_allow_no_stock_hh from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
                QQ.Enqueue("role_rights", sql);
            }
            if (FIXING_ARREARS || IsImported) return;

            if (SheetRows.Count > 0 && (sheet_type != SHEET_TYPE.SHEET_SALE_DD || !openPlaceholder))
            { 
                sql = $"select allow_negative_stock branch_allow_negative_stock,negative_stock_accordance,branch_id from info_branch where company_id = {company_id} and branch_id in({branchs_id})";
                if (sheet_type == SHEET_TYPE.SHEET_SALE_DD || sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN)
                    sql = $"select allow_negative_stock_order branch_allow_negative_stock,negative_stock_accordance, branch_id from info_branch where company_id = {company_id} and branch_id in({branchs_id})";
                QQ.Enqueue("branch_allow_negative_stock", sql);
            }
            //if (branch_id != "")
            //{
            //    sql = $"select allow_negative_stock branch_allow_negative_stock,negative_stock_accordance from info_branch where company_id = {company_id} and branch_id = {branch_id}";
            //    if (sheet_type == SHEET_TYPE.SHEET_SALE_DD || sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN)
            //        sql = $"select allow_negative_stock_order branch_allow_negative_stock,negative_stock_accordance from info_branch where company_id = {company_id} and branch_id = {branch_id}";
            //    QQ.Enqueue("branch_allow_negative_stock", sql);
            //}

            /*if (order_sheet_id != "" && (sheet_type == SHEET_TYPE.SHEET_SALE || sheet_type == SHEET_TYPE.SHEET_SALE_RETURN))
            {
                sql = $"select item_id,sum(quantity*unit_factor*inout_flag) qty from sheet_sale_order_detail d left join sheet_sale_order_main m on m.sheet_id = d.sheet_id where d.company_id = {company_id} and m.branch_id = {branch_id} and d.sheet_id = {order_sheet_id} and red_flag is null and approve_time is not null group by item_id";
                QQ.Enqueue("sale_order_qty", sql);
                string dt =CPubVars.GetDateText(DateTime.Now.AddDays(-90));
                sql = $"select total_amount-now_pay_amount-now_disc_amount as now_left_amount from sheet_sale_order_main m where company_id = {company_id} and sheet_id={order_sheet_id}  and red_flag is null and approve_time is not null and happen_time>'{dt}'";
                QQ.Enqueue("sale_order_arrears", sql);

            }*/

            if (order_sheet_id != "" && (sheet_type == SHEET_TYPE.SHEET_BUY || sheet_type == SHEET_TYPE.SHEET_BUY_RETURN))
            {
                sql = $"select item_id,sum(quantity*unit_factor*inout_flag) qty from sheet_buy_order_detail d left join sheet_buy_order_main m on m.sheet_id = d.sheet_id where d.company_id = {company_id} and m.branch_id = {branch_id} and d.sheet_id = {order_sheet_id} and red_flag is null and approve_time is not null group by item_id";
                QQ.Enqueue("buy_order_qty", sql);
            }
         
           
            if (sheet_type != SHEET_TYPE.SHEET_BUY_DD && sheet_type != SHEET_TYPE.SHEET_BUY_DD_RETURN && (sheet_type != SHEET_TYPE.SHEET_SALE_DD || !openPlaceholder))
            {
                if (SheetRows.Count > 0)
                { 
                    if(batchs_id == "")
                    {
                        batchs_id = "0";
                    }
                    batchs_id = batchs_id.Replace("无产期", "0");

                    sql = $@"select item_id,stock_qty,branch_id,branch_position,sell_pend_qty,batch_id from stock where company_id = {company_id} and item_id in ({items_id}) and batch_id in({batchs_id}) and branch_id in ({branchs_id}) and branch_position in ({branchs_position});";
         
                    QQ.Enqueue("stock", sql);
                }
              
            }
            if (items_id.IsValid() && this.red_flag!="2")
            {
                sql = @$"
SELECT ip.item_name,ip.son_mum_item, attr.remember_price,units.* FROM info_item_prop ip 
LEFT JOIN 
(
      select item_id,s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,s_unit->>'f3' as s_barcode,s_unit->>'f4' as s_retail_price,s_unit->>'f5' as s_lowest_price,s_unit->>'f6' as s_weight,s_unit->>'f7' as s_volume,
                     m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,m_unit->>'f3' as m_barcode,m_unit->>'f4' as m_retail_price,m_unit->>'f5' as m_lowest_price,m_unit->>'f6' as m_weight,m_unit->>'f7' as m_volume,
                     b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor,b_unit->>'f3' as b_barcode,b_unit->>'f4' as b_retail_price,b_unit->>'f5' as b_lowest_price,b_unit->>'f6' as b_weight,b_unit->>'f7' as b_volume
      from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,retail_price,lowest_price,weight,volume)) as json from info_item_multi_unit where company_id={company_id} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)
)
units on ip.item_id=units.item_id 
LEFT JOIN info_item_prop mum on ip.son_mum_item = mum.item_id and mum.company_id = {company_id}
LEFT JOIN info_attribute attr on (mum.mum_attributes->0->>'attrID')::integer=attr.attr_id and attr.company_id = {company_id}
WHERE ip.company_id = {company_id} and ip.item_id in ({items_id})";
                QQ.Enqueue("checkItems", sql);
            }
        }
        protected override string GetSQLForTemplates(string companyID,string mainTable, string sheetIDs)
        {
            string sheetUsageCondi = "";
            if (mainTable == "sheet_sale_main")
            {
                sheetUsageCondi = " and coalesce(m.sheet_usage,'D')=c.sheet_usage "; 
			}

            string sql =
            @$"
select m.sheet_id,st.setting->>'companyName' as company_name,st.setting->>'contactTel' as company_tel,st.setting->>'companyAddress' as company_address, t.template_id,t.template_name, c.client_group_id,c.client_id from {mainTable} m 
left join info_supcust ic on m.supcust_id=ic.supcust_id and ic.company_id={companyID} 
left join print_template t on m.sheet_type=t.sheet_type and t.company_id={companyID} 
left join print_template_choose c on c.company_id={companyID} and t.template_id=c.template_id and (m.supcust_id=c.client_id or ic.sup_group = c.client_group_id or (c.client_id=0 and c.client_group_id=0)) {sheetUsageCondi}
left join company_setting st on m.company_id=st.company_id
where m.company_id={companyID} and m.sheet_id in ({sheetIDs}) order by case when c.client_id is null then -1 else c.client_id end desc, case when c.client_group_id is null then -1 else c.client_group_id end desc, c.template_id, order_index;";

            return sql;
        }
        public bool RedActionWillCauseQuantityChange()
        {
            bool changed = false;
            if (this.RedChangeSheet == null) return true;
            
            List<TROW> oldRows = MergeSheetRowsByBatch(this.SheetRows);
            List<TROW> newRows = MergeSheetRowsByBatch(this.RedChangeSheet.SheetRows);

            foreach (TROW rowOld in oldRows)
            {
                bool bFound = false;
                foreach(TROW row in newRows)
                {
                    if (row.item_id == rowOld.item_id)
                    {
                        if(row.quantity * row.unit_factor!= rowOld.quantity * rowOld.unit_factor)
                        {
                            changed = true; break;
                        }
                        bFound = true;
                    }
                }
                if (!bFound) return true;
            }
            foreach (TROW row in newRows)
            {
                bool bFound = false;
                foreach (TROW rowOld in oldRows)
                {
                    if (row.item_id == rowOld.item_id)
                    {
                        if (row.quantity * row.unit_factor != rowOld.quantity * rowOld.unit_factor)
                        {
                            changed = true; break;
                        }
                        bFound = true;
                    }
                }
                if (!bFound) return true;
            }
            return changed;
             
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName,bForRed);

            if (sqlName == "arrear_balance")
            {
                DealArrearReadQQ(dr, info, left_amount, supcust_id);
            }
            else if (sqlName == "prepay_sub")
            {
                info.PrepaySubjects = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
            else if (sqlName == "role_rights")
            {
                dynamic right = CDbDealer.Get1RecordFromDr(dr);
                if (right != null)
                {
                    string r = right.role_allow_negative_stock;
                    if (r.ToLower() == "false") info.RoleAllowNegativeStock = false;
                    r = right.role_allow_negative_prepay;
                    if (r.ToLower() == "true") info.RoleAllowNegativePrepay = true;
                    r = right.role_allow_no_stock_hh;
                    if (r.ToLower() == "false") info.RoleAllowNoStockHH = false;

                }
            }
            else if (sqlName == "branch_allow_negative_stock")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);

                if (records.Count == 0)
                {
                    info.ErrMsg = "仓库选择有误，请重新选择";
                }
                else
                {
                    foreach (dynamic rec in records)
                    {
                        foreach (SheetRowMM row in SheetRows)
                        {
                            string rowBranchId = row.branch_id.IsInvalid() ? branch_id:row.branch_id;
                            if (rec.branch_id != "" && rowBranchId == rec.branch_id)
                            {
                                string b = rec.branch_allow_negative_stock;
                                if (b.ToLower() == "false") row.BranchAllowNegativeStock = false;
                                row.NegativeStockAccordance = rec.negative_stock_accordance;
                            }
                        }
                    }
                }
                //if (branchInfo != null)
                //{
                //    string b = branchInfo.branch_allow_negative_stock;
                //    if (b.ToLower() == "false") info.BranchAllowNegativeStock = false;
                //    info.NegativeStockAccordance = branchInfo.negative_stock_accordance;
                //}
            }
            /* else if (sqlName == "sale_order_qty")  // 订单转单 的 销售单，找出原单据的数量，判断可用库存数量 
             {
                 List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                 foreach (dynamic rec in records)
                 {
                     foreach (SheetRowMM row in SheetRows)
                     {
                         if (rec.item_id != "" && row.item_id == rec.item_id)
                         {
                             row.SaleOrderQty = CPubVars.ToDecimal(rec.qty == "" ? 0 : rec.qty);
                         }
                     }
                 }
             }
             else if (sqlName == "sale_order_arrears")  // 订单转单 的 销售单，找出原单据的数量，判断可用库存数量 
             {

             } */
            else if (sqlName == "stock")//前面set qq 一定要放在 role_allow_negative_stock branch_allow_negative_stock之后
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowMM row in SheetRows)
                    {
                        string rowBranchId = row.branch_id.IsInvalid()?branch_id:row.branch_id;
                        if (rec.item_id != "" && row.item_id == rec.item_id && row.batch_id == rec.batch_id && rowBranchId == rec.branch_id && row.branch_position == rec.branch_position)
                        {
                            if (rec.stock_qty != "")
                            {
                                row.HasStockQty = true;
                                row.StockQty = CPubVars.ToDecimal(rec.stock_qty);
                                row.SellPendQty = CPubVars.ToDecimal(rec.sell_pend_qty == "" ? 0 : rec.sell_pend_qty);
                            }
                        }
                    }
                }

                info.SheetRows = SheetRows;



                #region 负库存条件：   库存 - 开单后的占用库存 + 本单据的变化量 < 0

                bool ignoreStockCheck = false;
                if (sheet_type == SHEET_TYPE.SHEET_SALE_DD && this.mall_allow_negative_stock_order) ignoreStockCheck = true;
                
                if ((sheet_type == SHEET_TYPE.SHEET_SALE_DD
                 || sheet_type == SHEET_TYPE.SHEET_SALE
                 || sheet_type == SHEET_TYPE.SHEET_SALE_RETURN
                 || sheet_type == SHEET_TYPE.SHEET_BUY
                 || sheet_type == SHEET_TYPE.SHEET_BUY_RETURN
                 || sheet_type == SHEET_TYPE.SHEET_PLACEHOLDER_DD
                 ) && !ignoreStockCheck
               )
                {
                    
                    
                    List<TROW> _sheetRows = this.SheetRows;
                    bool bIgnoreNegativeQty = false;
                    if ((this.SheetType == "X" && !info.RoleAllowNoStockHH)
                       ||  sheet_type == SHEET_TYPE.SHEET_SALE_DD || sheet_type == SHEET_TYPE.SHEET_PLACEHOLDER_DD
                       )
                    {
                        bIgnoreNegativeQty = true;
                    }
                    if (bIgnoreNegativeQty)
                    {
                        _sheetRows = (from row in this.SheetRows where row.quantity > 0 select row).ToList();
                    }
                    //var mergedRows = MergeSheetRows(_sheetRows);
                    var mergeSheetRowsByBatchAndItem = MergeSheetRowsByBatchAndItem(_sheetRows);
                  
                    //var mergeSheetRowsByBatchAndItemPositive = MergeSheetRowsByBatchAndItem(_sheetRows,true);//用于判断销售正数list/退货负数list是否超出可用库存（采/采退/销订都包含，除了退订已限制不让输负数），需求来自客户青州圣龙酒业。--zy2024.7.30


                  
                        int rowIndex = 1;

                        foreach (var row in mergeSheetRowsByBatchAndItem.ToList())
                        {
                            if ((!info.RoleAllowNegativeStock || !row.BranchAllowNegativeStock))
                            {
                                decimal newStockQty = 0;
                                var branchType = "实际";
                                if (row.NegativeStockAccordance == "real") newStockQty = row.StockQty + row.quantity * row.inout_flag;
                                //if (info.NegativeStockAccordance == "usable" || (sheet_type == SHEET_TYPE.SHEET_SALE_DD || sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN))
                                //退货订单不应该影响
                                // if (sheet_type != SHEET_TYPE.SHEET_SALE_DD_RETURN
                                //     && sheet_type != SHEET_TYPE.SHEET_BUY_DD && sheet_type != SHEET_TYPE.SHEET_BUY_DD_RETURN
                                //  )
                                //{
                                if (row.NegativeStockAccordance == "usable" || (sheet_type == SHEET_TYPE.SHEET_SALE_DD) || (sheet_type == SHEET_TYPE.SHEET_PLACEHOLDER_DD))
                                {
                                    decimal newSellPendQty = 0;
                                    branchType = "可用";
                                    decimal newAvailQty = 0;// row.StockQty - row.SellPendQty + row.quantity * row.inout_flag; // newAvailQty 开完本单据后的可用库存
                                    if (sheet_type == SHEET_TYPE.SHEET_SALE && order_sheet_id != "")
                                    {
                                        if (red_flag == "2")
                                            newSellPendQty = row.SellPendQty + row.SaleOrderQty;
                                        else
                                            newSellPendQty = row.SellPendQty - row.SaleOrderQty;
                                        newAvailQty = row.StockQty - newSellPendQty + row.quantity * row.inout_flag;
                                    }
                                    else
                                    {
                                        newAvailQty = row.StockQty - row.SellPendQty + row.quantity * row.inout_flag;
                                    }
                                    newStockQty = newAvailQty;
                                }
                                var changeQty = row.quantity * row.inout_flag;
                            // (changeQty < 0 || isRedAndChange) 冲改时，会出现changeQty>0，也会导致负库存 ，会出现审核抵扣不了红冲的，


                            bool bIgnoreCheckForRedChangeSameItem = false;// false;
                                if (bForRed && isRedAndChange)
                                {
                                //bIgnoreCheckForRedChangeSameItem = true;
                                   var newRows = this.MergeSheetRowsByBatchAndItem_noPrice(this.RedChangeSheet.SheetRows);
                                    foreach(var newRow in newRows)
                                    {
                                        if(row.item_id == newRow.item_id && row.batch_id== newRow.batch_id && row.branch_id== newRow.branch_id && row.branch_position== newRow.branch_position)//说明新单据里面有这个旧单据商品，那么就不要校验这个商品的负库存
                                        { 
                                            bIgnoreCheckForRedChangeSameItem = true;
                                            break;
                                        }
                                    }
                                }

                                if (newStockQty < -0.01m && (changeQty < 0 || (isRedAndChange && !bForRed)) && !IsImported && !IsRealTimeImported && !bIgnoreCheckForRedChangeSameItem)
                                {
                                    if (red_flag == "2")
                                    {
                                        info.ErrMsg = $"{row.item_name}出现负{branchType}库存，红冲失败";
                                        break;
                                    }
                                    info.ErrMsg += $"{row.item_name}出现负{branchType}库存，审核失败";
                                }
                                //}

                                rowIndex++;
                            }

                        }
                        
                    
                    
                }


                #endregion
                 
            }
            else if(sqlName== "checkItems")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                if(records.Count == 0)
                {
                    if (this.SheetRows.Count > 0) info.ErrMsg = "单据商品行的商品不存在,请检查";
                }
                else
                {
                    foreach (var row in this.SheetRows)
                    {
                        var hasItem = false;
                        var itemNoExist = false;
                        var errorItemUnitFactor = false;
                        foreach (dynamic rec in records)
                        {
                            if (row.item_id == rec.item_id)
                            {
                                hasItem = true;
                                if (row.item_name == "") row.item_name = rec.item_name;
                                row.son_mum_item = rec.son_mum_item;
                                if (row.son_mum_item != "" && ((string)rec.remember_price).ToLower() == "true")
                                {
                                    row.attrRememberPrice = true;
                                }
                                if (rec.b_unit_no != "" && row.unit_no == rec.b_unit_no)
                                {
                                    itemNoExist = true;
                                    if (Convert.ToDecimal(row.unit_factor) != Convert.ToDecimal(rec.b_unit_factor))
                                    {
                                        errorItemUnitFactor = true;
                                    }
                                }
                                if (rec.m_unit_no != "" && row.unit_no == rec.m_unit_no)
                                {
                                    itemNoExist = true;
                                    if (Convert.ToDecimal(row.unit_factor) != Convert.ToDecimal(rec.m_unit_factor))
                                    {
                                        errorItemUnitFactor = true;
                                    }
                                }
                                if (rec.s_unit_no != "" && row.unit_no == rec.s_unit_no)
                                {
                                    itemNoExist = true;
                                    if (Convert.ToDecimal(row.unit_factor) != Convert.ToDecimal(rec.s_unit_factor))
                                    {
                                        errorItemUnitFactor = true;
                                    }
                                }
                            }
                        }
                        if (!hasItem)
                        {
                            info.ErrMsg = $"第{row.row_index}行商品{row.item_name},商品档案里不存在,请检查";
                        }
                        if (!itemNoExist)
                        {
                            info.ErrMsg = $"第{row.row_index}行商品{row.item_name}单位{row.unit_no},商品档案里不存在,请检查";
                        }
                        if (errorItemUnitFactor)
                        {
                            info.ErrMsg = $"第{row.row_index}行商品{row.item_name}单位{row.unit_no}的包装率与商品档案里不一致,请检查";
                        }
                    }
                }
            }

        }
        public override string GetSheetCharactor()
        {
            string res = this.order_sheet_id + this.company_id + "_" + this.sheet_id + "_" + this.sheet_no + "_" + this.supcust_id +"_" + this.branch_id + "_" + this.OperID + "_" + this.seller_id + "_" + this.make_brief+"_"+this.payway1_id+ "_" + this.payway1_amount +"_" +  (this.payway2_amount != 0 ?  this.payway2_id + "_" + this.payway2_amount + "_" : "") + (this.payway3_amount != 0 ? this.payway3_id + "_" + this.payway3_amount + "_" : "");
            foreach (var row in SheetRows)
            {
                res += row.item_id + "_" + row.quantity + row.real_price + row.remark;
            }
            return res;
        }
		protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
		{
		
			string checkResult = await base.CheckSheetValid(cmd);
			if (checkResult != "OK")
				return checkResult;



			
			if (!FIXING_ARREARS)
			{
				decimal total_sub_amount = 0;
				int nRow = 0;
				foreach (SheetRowMM row in SheetRows)
				{
					nRow++;
					if (row.inout_flag != 0) total_sub_amount += row.sub_amount;
				
					
				}
				if (Math.Abs(total_sub_amount - total_amount) >= 1)
				{
					string sSheet = JsonConvert.SerializeObject(this);
					MyLogger.LogMsg("amount diff:" + sSheet, company_id);
				    return $"明细行合计{total_sub_amount}与总额{total_amount}不等，请检查";

				}

			}
			return "OK";


		}

		protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            if (sheet_type == SHEET_TYPE.SHEET_PLACEHOLDER_DD) return "OK";
            string checkResult = await base.CheckSaveSheetValid(cmd);
            if (checkResult != "OK")
                return checkResult;



            
            if (now_pay_amount != 0 && payway1_id == "") return "必须指定支付方式";  

            if (Math.Abs(now_pay_amount - (payway1_amount + payway2_amount + payway3_amount)) > 0.05m)
            {
                return "支付方式与总额不一致";
            }
            if (payway1_amount == 0) payway1_id = "";
            if (payway2_amount == 0) payway2_id = "";
            if (payway3_amount == 0) payway3_id = "";

           

            if (!IsImported)
            {
                if (payway1_id != "" && payway1_id == payway2_id)
                {
                    return $"支付方式1和支付方式2不能同时选{payway1_name}";
                }
                if (payway1_id != "" && payway1_id == payway3_id)
                {
                    return $"支付方式1和支付方式3不能同时选{payway1_name}";
                }
                if (payway2_id != "" && payway2_id == payway3_id)
                {
                    return $"支付方式2和支付方式3不能同时选{payway2_name}";
                }
                if (SheetRows.Count == 0)
                {
                    return "请选择商品";
                }
            }
            
                       

            if (total_amount - paid_amount - disc_amount >= 0.01m)
            {
                if (supcust_id == "" || supcust_id=="0")
                {
                    if (sheet_type == SHEET_TYPE.SHEET_SALE || sheet_type == SHEET_TYPE.SHEET_SALE_RETURN)
                        return "此单据有欠款，必须指定客户";
                    else
                        return "此单据有欠款，必须指定供应商";
                }
            }
            if (!FIXING_ARREARS)
            {
                decimal total_sub_amount = 0;
                int nRow = 0;
                foreach (SheetRowMM row in SheetRows)
                {
                    nRow++;
                    if (row.inout_flag != 0) total_sub_amount += row.sub_amount;
					if (row.quantity != 0)
					{
                        decimal calcPrice = 0;
                        calcPrice = row.sub_amount / (decimal)row.quantity;
                        if (Math.Abs(row.real_price - calcPrice) > 0.5m) return $"{row.item_name}的单价{row.real_price}和金额/数量{calcPrice}有差异";


                    }
                    if(row.discount.IsValid() && !CPubVars.IsNumeric(row.discount))
                    {
                        row.discount = "";
					}
                    if (row.orig_price.IsValid())
                    {
                        if (!CPubVars.IsNumeric(row.orig_price))
                        {
                            return $"第{nRow}行{row.item_name}原价输入错误:{row.orig_price}";
                        }
                    }
                    if(this.sheet_type==SHEET_TYPE.SHEET_SALE_RETURN || this.sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN|| this.sheet_type == SHEET_TYPE.SHEET_BUY_RETURN || this.sheet_type == SHEET_TYPE.SHEET_BUY_DD_RETURN){
                        if (row.quantity < 0)
                        {
                            return "退单商品数量不能为负数";
                        }
                    }
                    if (!string.IsNullOrEmpty(row.remark) && row.remark.Contains("'"))
                    {
                        row.remark = row.remark.Replace("'", "");
                    }
                }
                if (Math.Abs(total_sub_amount - total_amount) >= 1)
                {
                    string sSheet = JsonConvert.SerializeObject(this);
                    MyLogger.LogMsg("amount diff:" + sSheet, company_id);
                   // return $"明细行合计{total_sub_amount}与总额{total_amount}不等，请检查";

                }



                foreach (dynamic row in this.SheetRows)
                {
                    string branchID = string.IsNullOrEmpty(row.branch_id) ? branch_id : row.branch_id;
                    string branchName = string.IsNullOrEmpty(row.branch_name) ? branch_name : row.branch_name;
                    if (row.GetType().GetProperty("branch_position") == null || row.branch_position == "0" || row.branch_position == null || row.branch_position == "") continue;
                    dynamic record = await CDbDealer.Get1RecordFromSQLAsync($"select branch_position from info_branch_position where company_id = {company_id} and branch_position = {row.branch_position} and branch_id = {branchID};", cmd);
                    if (record == null)
                    {
                        return $"{branchName}不存在库位：{row.branch_position_name}";
                    }
                }
            }

            string msg=await CheckBatch(cmd);
            if (msg != "") return msg;
            return "OK";


        }

        protected override async Task<string> CheckForRed(CMySbCommand cmd)
        {
            return await CheckForRed_MoneySheet(cmd);
        }
      
        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            if (FIXING_ARREARS) return "";
            bool openPlaceholder = this.GetType().GetProperty("isOpenPlaceholderOrder")==null || this.GetType().GetProperty("isOpenPlaceholderOrder").GetValue(this).ToString().ToLower()=="false"?false:true;
            if (openPlaceholder) return "";

            string sql = "";
            var mergedRowsByBatchAndItem = MergeSheetRowsByBatchAndItem(this.SheetRows);
            MergedSheetRowByBatchAndItem = mergedRowsByBatchAndItem;
            foreach (SheetRowMM row in mergedRowsByBatchAndItem)
            {
                string s = "";
                string changeQty = "";

                var qty = row.inout_flag * row.quantity;
                changeQty = qty.ToString();
                if (changeQty == "-0") changeQty = "0";
                if (qty >= 0)
                {
                    changeQty = "+" + qty.ToString();
                }
                else { changeQty = qty.ToString(); }

                string rowBranchId = row.branch_id.IsInvalid()?branch_id:row.branch_id;
                if (!row.branch_position.IsValid()) row.branch_position = "0";
                if (row.HasStockQty)
                { 
                    s = $"update stock set stock_qty=stock_qty{changeQty} where company_id={company_id} and branch_id={rowBranchId} and item_id={row.item_id} and batch_id = {row.batch_id} and branch_position = {row.branch_position};";
                }
                else
                {
                    s = $"insert into stock(company_id,branch_id,item_id,stock_qty,batch_id,branch_position) values ({company_id},{rowBranchId},{row.item_id},{qty},{row.batch_id},{row.branch_position}) on conflict (company_id,branch_id,item_id,batch_id,branch_position) do update set stock_qty=stock.stock_qty{changeQty};";
                }
                sql += s;

                row.NewStockQty = row.StockQty + qty;
            }
            return sql;
        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";
           
           if(this.supcust_id== "2597763")
            {

            }
            if (left_amount != 0 || order_sheet_id != "")
            {
                sql += await GetSqlForArrearsChange(cmd,info, left_amount, seller_id);
            }
             
            string redSql = "";
            decimal pre_prepay_amount = this.prepay_amount;
            this.prepay_amount = 0;
         
            if (info.PrepaySubjects != null)
            {
                int flag = money_inout_flag;
                string sRedFlag = "null";
                if (red_flag == "2") sRedFlag = "'" + red_flag + "'";
                string custID = supcust_id;
                if (acct_supcust_id != "") custID = acct_supcust_id;
                //string custID = acct_supcust_id;
                decimal prepayTotalBalance = 0m;
                foreach (var sub in info.PrepaySubjects)
                {
                    if (sub.balance.IsValid())
                    {
                        prepayTotalBalance += CPubVars.ToDecimal(sub.balance);
                    }
                }

                foreach (var sub in info.PrepaySubjects)
                {
                    decimal prepayAmt = 0;
                    if (payway1_id == sub.sub_id)
                    {
                        prepayAmt = payway1_amount;
                    }
                    else if (payway2_id == sub.sub_id)
                    {
                        prepayAmt = payway2_amount;
                    }
                    else if (payway3_id == sub.sub_id)
                    {
                        prepayAmt = payway3_amount;
                    }
                    if (Math.Abs(prepayAmt) > 0.001m)
                    {
                        flag = 1;
                        if (sheet_type == SHEET_TYPE.SHEET_SALE_RETURN || sheet_type == SHEET_TYPE.SHEET_BUY_RETURN)
                        {
                            flag = -1;
                        }
                        if (sheet_type == SHEET_TYPE.SHEET_BUY_DD)
                        {
                            flag = 0;
                        }
                        if (red_flag == "2") flag *= -1;
                        decimal changeBal = flag * prepayAmt * (-1);
                        prepay_amount += prepayAmt;
                        decimal subBalance = 0m;
                        if (!string.IsNullOrEmpty(sub.balance)) subBalance = decimal.Parse(sub.balance, System.Globalization.NumberStyles.Float);
                        decimal preSubBalance = subBalance;
                        subBalance += changeBal;
                      
                        if (sub.is_order.ToLower() == "true")
                        {
                            if (Math.Abs(subBalance) <= 0.2m)
                            {
                                changeBal = -preSubBalance;
                                subBalance = 0m;
                            }

                            if (subBalance < 0m)
                            {
                                info1.ErrMsg = "定货会余额不能小于0";
                                return;
                            }
                        }
                        prepayTotalBalance += changeBal;
                        if (!info.RoleAllowNegativePrepay && subBalance < 0)
                        {
                            if(sheet_type == SHEET_TYPE.SHEET_BUY_RETURN || sheet_type == SHEET_TYPE.SHEET_BUY)
                            {
                                info1.ErrMsg = "预付款余额不能小于0";
                            }
                            else info1.ErrMsg = "预收款余额不能小于0";

                            return;
                        }

                        if (changeBal != 0)
                        {
                            if (sub.balance.IsValid())
                            {
                                sql += $"update prepay_balance set balance=balance+({changeBal}) where company_id={company_id} and supcust_id={custID} and sub_id={sub.sub_id};";
                            }
                            else
                            {
                                sql += $"insert into prepay_balance (company_id,supcust_id,sub_id,balance) values ({company_id},{custID},{sub.sub_id},{changeBal});";
                            }

                            if (red_flag != "2" && changeBal>0)
                            {
                          
                               sql += $@"insert into prepay_detail(supcust_id,    sheet_id,  sheet_no,    company_id,    sub_id,      sheet_type,    red_flag,  balance,    init_balance, init_time,        remark)
                                                            values({supcust_id}, {sheet_id},'{sheet_no}', {company_id}, {sub.sub_id}, '{SheetType}', null,     {changeBal}, {changeBal}, '{approve_time }','{this.make_brief}');";
                               
                            }
                            else if (prepay_sheet_info.IsValid())
                            {
                                string updatePrepaySql = "";
                                dynamic tp = JsonConvert.DeserializeObject(prepay_sheet_info);
                                foreach (var property in tp)
                                {
                                    var key = property.Path;
                                    var value = property.Value;
                                    if (key==sub.sub_id && value is JArray)
                                    {
                                        foreach (dynamic arrayItem in value)
                                        {
                                            string amount = (string)(arrayItem.amount);
                                            if (amount == "0" || amount == "")
                                            {
                                                continue;
                                            }
                                            if (!CPubVars.IsNumeric(amount))
                                            {
                                                info.ErrMsg=$"预收款明细分配金额不合法:{amount}";
                                                return;
                                            }
                                            string addReduce = "-";
                                            if (red_flag == "2") addReduce = "+";

                                            updatePrepaySql += $@"update prepay_detail set balance = balance {addReduce} {amount} where company_id = {company_id} and sheet_id = {arrayItem.sheet_id} and supcust_id = {supcust_id} and sub_id = {key};";
                                        }
                                    }
                                }

                                if (updatePrepaySql != "")
                                {
                                    sql += updatePrepaySql;
                                }

                            }

                            if (red_flag == "2")
                            {
                                redSql += @$"update client_account_history set red_flag = '1' where company_id = {company_id} and sheet_id = {red_sheet_id} and sheet_type = '{SheetType}' and sub_type = '{sub.sub_type}';";
                            }
                            GetAccountHistoryHappenTimePrepayBalance(info, supcust_id, subBalance, prepayTotalBalance, sub.sub_id, out string balance, out string totalBalance);
                            sql += @$"
insert into client_account_history(company_id,   happen_time,                          approve_time,                          sheet_type  ,sheet_id ,change_amount  , now_balance  ,now_prepay_balance  ,now_balance_happen_time,now_prepay_balance_happen_time,supcust_id,sub_id      , sub_type       ,red_flag  ) 
                           values ({company_id},'{CPubVars.GetDateText(happen_time)}','{CPubVars.GetDateText(approve_time)}','{SheetType}',{sheetID},{changeBal}    , {subBalance} ,{prepayTotalBalance},{balance},{totalBalance},{custID}  ,{sub.sub_id},'{sub.sub_type}',{sRedFlag});";
                            if (!HappenNow)
                            {
                                sql += $"update client_account_history set now_balance_happen_time=now_balance_happen_time+{changeBal}  where company_id={company_id} and supcust_id={custID} and sub_type='{sub.sub_type}' and sub_id={sub.sub_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                                sql += $"update client_account_history set now_prepay_balance_happen_time=now_prepay_balance_happen_time+{changeBal} where company_id={company_id} and supcust_id={custID} and sub_type='{sub.sub_type}' and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                            }
                                

                        } 
                    }

                }
          
            }

             

            if (this.prepay_amount !=pre_prepay_amount)
                sql += $"update {MainTable} set prepay_amount = {prepay_amount} where company_id = {company_id} and sheet_id = {sheetID};";


            foreach (SheetRowMM row in MergedSheetRowByBatchAndItem)
            {
                string rowBranchId = row.branch_id.IsInvalid()?branch_id:row.branch_id;
            string s = $@"insert into stock_change_log 
        (company_id, approve_time,  branch_id, item_id, sheet_id,  pre_stock_qty ,new_stock_qty,      pre_sell_pend_qty ,new_sell_pend_qty,batch_id,branch_position) 
values ({company_id},'{approve_time}',{rowBranchId},{row.item_id},{sheet_id},{row.StockQty},{row.NewStockQty}, {row.SellPendQty} ,{row.NewSellPendQty},{row.batch_id},{row.branch_position});";

                sql += s;
            }

            if (sql != "")
            {
                cmd.company_id = company_id;
                cmd.CommandText = sql + redSql;
                await cmd.ExecuteNonQueryAsync();
            }

        }

        public  List<TROW> MergeSheetRows_return(List<TROW> rows)
        {
            Dictionary<string, TROW> rowsDict = new Dictionary<string, TROW>();

            foreach (TROW sheetRow in rows)
            {
                if (sheetRow.inout_flag == 0) continue;
                string cur_branch = sheetRow.branch_id;
                string skey = sheetRow.item_id;// +"_" + sheetRow.unit_factor.ToString();
                if ( sheetRow.quantity > 0) continue;
                TROW curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    string s = JsonConvert.SerializeObject(sheetRow);
                    curRow = JsonConvert.DeserializeObject<TROW>(s);
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_factor = 1;
                    curRow.real_price = sheetRow.real_price / sheetRow.unit_factor;
                    #region Following properties will be used for approve. Setting value to prevent from being ignored while serializing if someone set the property to internal or non-json convertable
                    curRow.StockQty = sheetRow.StockQty;
                    curRow.SellPendQty = sheetRow.SellPendQty;
                    curRow.HasStockQty = sheetRow.HasStockQty;
                    curRow.SaleOrderQty = sheetRow.SaleOrderQty;
                    #endregion
                    rowsDict.Add(skey, curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                    curRow.sub_amount += sheetRow.sub_amount;
                }
            }
            List<TROW> newList = new List<TROW>();
            foreach (var k in rowsDict)
            {
                var row = k.Value;
                /* decimal l = row.quantity;
                 if (row.b_unit_factor.IsValid())
                 {
                     decimal f = CPubVars.ToDecimal(row.b_unit_factor);
                     row.b_quantity = (decimal)Math.Floor(l / f);
                     l = l % f;
                 }
                 if (row.m_unit_factor.IsValid())
                 {
                     decimal f = CPubVars.ToDecimal(row.m_unit_factor);
                     row.m_quantity = (decimal)Math.Floor(l / f);
                     l = l % f;
                 }
                 row.s_quantity = l;*/
                newList.Add(k.Value);
            }
            return newList;

        }
        public List<TROW> MergeSheetRowsByItemAndQuantity(List<TROW> rows)
        {
            List<TROW> rowsDict = new List<TROW>();

            foreach (TROW sheetRow in rows)
            {
                if (sheetRow.inout_flag == 0) continue;
                string cur_branch = sheetRow.branch_id;
                if (cur_branch.IsInvalid()) cur_branch = branch_id;
                if (sheetRow.quantity == 0) continue;
                TROW curRow = null;
                foreach (TROW rowDic in rowsDict)
                {
                    if (sheetRow.item_id == rowDic.item_id&&sheetRow.quantity>0 && rowDic.quantity>0)
                    {
                        curRow = rowDic;
                        break;
                    }
                    if (sheetRow.item_id == rowDic.item_id && sheetRow.quantity < 0 && rowDic.quantity < 0)
                    {
                        curRow = rowDic;
                        break;
                    }
                }
                if (curRow == null)
                {
                    string s = JsonConvert.SerializeObject(sheetRow);
                    curRow = JsonConvert.DeserializeObject<TROW>(s);
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_factor = 1;
                    curRow.real_price = sheetRow.real_price / sheetRow.unit_factor;
                    #region Following properties will be used for approve. Setting value to prevent from being ignored while serializing if someone set the property to internal or non-json convertable
                    curRow.StockQty = sheetRow.StockQty;
                    curRow.SellPendQty = sheetRow.SellPendQty;
                    curRow.HasStockQty = sheetRow.HasStockQty;
                    curRow.SaleOrderQty = sheetRow.SaleOrderQty;
                    curRow.batch_id = sheetRow.batch_id;
                    curRow.NegativeStockAccordance = sheetRow.NegativeStockAccordance;
                    curRow.BranchAllowNegativeStock = sheetRow.BranchAllowNegativeStock;
                    #endregion
                    rowsDict.Add(curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                    curRow.sub_amount += sheetRow.sub_amount;
                }
            }
            return rowsDict;
        }
        public override List<TROW> MergeSheetRows(List<TROW> rows, bool bIgnoreNativeQty = false)
        {
            Dictionary<string, TROW> rowsDict = new Dictionary<string, TROW>();

            foreach (TROW sheetRow in rows)
            {
                if (sheetRow.inout_flag == 0) continue;
                string cur_branch = sheetRow.branch_id;
                string skey = sheetRow.item_id;// +"_" + sheetRow.unit_factor.ToString();
                if (bIgnoreNativeQty && sheetRow.quantity < 0) continue;
                TROW curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    string s = JsonConvert.SerializeObject(sheetRow);
                    curRow = JsonConvert.DeserializeObject<TROW>(s);
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_factor = 1;
                    curRow.real_price = sheetRow.real_price / sheetRow.unit_factor;
                    #region Following properties will be used for approve. Setting value to prevent from being ignored while serializing if someone set the property to internal or non-json convertable
                    curRow.StockQty = sheetRow.StockQty;
                    curRow.SellPendQty = sheetRow.SellPendQty;
                    curRow.HasStockQty = sheetRow.HasStockQty;
                    curRow.SaleOrderQty = sheetRow.SaleOrderQty;
                    #endregion
                    rowsDict.Add(skey, curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                    curRow.sub_amount += sheetRow.sub_amount;
                }
            }
            List<TROW> newList = new List<TROW>();
            foreach (var k in rowsDict)
            {
                var row = k.Value;
                /* decimal l = row.quantity;
                 if (row.b_unit_factor.IsValid())
                 {
                     decimal f = CPubVars.ToDecimal(row.b_unit_factor);
                     row.b_quantity = (decimal)Math.Floor(l / f);
                     l = l % f;
                 }
                 if (row.m_unit_factor.IsValid())
                 {
                     decimal f = CPubVars.ToDecimal(row.m_unit_factor);
                     row.m_quantity = (decimal)Math.Floor(l / f);
                     l = l % f;
                 }
                 row.s_quantity = l;*/
                newList.Add(k.Value);
            }
            return newList;

        }
        public List<TROW> MergeSheetRowsByBatchAndItem_noPrice(List<TROW> rows, bool bIgnoreNativeQty = false)
        {
            List<TROW> rowsDict = new List<TROW>();

            foreach (TROW sheetRow in rows)
            {
                if (sheetRow.inout_flag == 0) continue;
                string cur_branch = sheetRow.branch_id;
                if (cur_branch.IsInvalid()) cur_branch = branch_id;
                if (bIgnoreNativeQty && sheetRow.quantity < 0) continue;
                TROW curRow = null;
                foreach (TROW rowDic in rowsDict)
                {
                    if (sheetRow.item_id == rowDic.item_id && sheetRow.batch_id == rowDic.batch_id && cur_branch == rowDic.branch_id && sheetRow.branch_position == rowDic.branch_position)
                    {
                        curRow = rowDic;
                        break;
                    }
                }
                if (curRow == null)
                {
                    string s = JsonConvert.SerializeObject(sheetRow);
                    curRow = JsonConvert.DeserializeObject<TROW>(s);
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_factor = 1;
                    #region Following properties will be used for approve. Setting value to prevent from being ignored while serializing if someone set the property to internal or non-json convertable
                    curRow.StockQty = sheetRow.StockQty;
                    curRow.SellPendQty = sheetRow.SellPendQty;
                    curRow.HasStockQty = sheetRow.HasStockQty;
                    curRow.SaleOrderQty = sheetRow.SaleOrderQty;
                    curRow.batch_id = sheetRow.batch_id;
                    curRow.branch_id = cur_branch;
                    curRow.branch_position = sheetRow.branch_position;
                    curRow.NegativeStockAccordance = sheetRow.NegativeStockAccordance;
                    curRow.BranchAllowNegativeStock = sheetRow.BranchAllowNegativeStock;
                    #endregion
                    rowsDict.Add(curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                    curRow.sub_amount += sheetRow.sub_amount;
                }
            }
            return rowsDict;

        }
        public List<TROW> MergeSheetRowsByBatchAndItem(List<TROW> rows, bool bIgnoreNativeQty = false)
        {
            List<TROW> rowsDict = new List<TROW>();

            foreach (TROW sheetRow in rows)
            {
                if (sheetRow.inout_flag == 0) continue;
                string cur_branch = sheetRow.branch_id;
                if (cur_branch.IsInvalid()) cur_branch = branch_id;
                if (bIgnoreNativeQty && sheetRow.quantity < 0) continue;
                TROW curRow = null;
                foreach (TROW rowDic in rowsDict)
                {
                    if (sheetRow.item_id == rowDic.item_id && sheetRow.batch_id == rowDic.batch_id && cur_branch == rowDic.branch_id && sheetRow.branch_position == rowDic.branch_position)
                    {
                        curRow = rowDic;
                        break;
                    }
                }
                if (curRow == null)
                {
                    string s = JsonConvert.SerializeObject(sheetRow);
                    curRow = JsonConvert.DeserializeObject<TROW>(s);
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_factor = 1;
                    curRow.real_price = sheetRow.real_price / sheetRow.unit_factor;
                    #region Following properties will be used for approve. Setting value to prevent from being ignored while serializing if someone set the property to internal or non-json convertable
                    curRow.StockQty = sheetRow.StockQty;
                    curRow.SellPendQty = sheetRow.SellPendQty;
                    curRow.HasStockQty = sheetRow.HasStockQty;
                    curRow.SaleOrderQty = sheetRow.SaleOrderQty;
                    curRow.batch_id = sheetRow.batch_id;
                    curRow.branch_id = cur_branch;
                    curRow.branch_position = sheetRow.branch_position;
                    curRow.NegativeStockAccordance = sheetRow.NegativeStockAccordance;
                    curRow.BranchAllowNegativeStock = sheetRow.BranchAllowNegativeStock;
                    #endregion
                    rowsDict.Add(curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                    curRow.sub_amount += sheetRow.sub_amount;
                }
            }
            return rowsDict;

        }
		public List<TROW> MergeSheetRowsByBatch(List<TROW> rows, bool bIgnoreNativeQty = false)
		{
			List<TROW> rowsDict = new List<TROW>();

			foreach (TROW sheetRow in rows)
			{
				if (sheetRow.inout_flag == 0) continue;
				string cur_branch = sheetRow.branch_id;
				if (cur_branch.IsInvalid()) cur_branch = branch_id;
				if (bIgnoreNativeQty && sheetRow.quantity < 0) continue;
				TROW curRow = null;
				foreach (TROW rowDic in rowsDict)
				{
					if (sheetRow.item_id == rowDic.item_id && sheetRow.batch_id == rowDic.batch_id)
					{
						curRow = rowDic;
						break;
					}
				}
				if (curRow == null)
				{
					string s = JsonConvert.SerializeObject(sheetRow);
					curRow = JsonConvert.DeserializeObject<TROW>(s);
					curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
					curRow.unit_factor = 1;
					curRow.real_price = sheetRow.real_price / sheetRow.unit_factor;
					#region Following properties will be used for approve. Setting value to prevent from being ignored while serializing if someone set the property to internal or non-json convertable
					curRow.StockQty = sheetRow.StockQty;
					curRow.SellPendQty = sheetRow.SellPendQty;
					curRow.HasStockQty = sheetRow.HasStockQty;
					curRow.SaleOrderQty = sheetRow.SaleOrderQty;
					curRow.batch_id = sheetRow.batch_id;
					//curRow.branch_id = cur_branch;
					//curRow.branch_position = sheetRow.branch_position;
					curRow.NegativeStockAccordance = sheetRow.NegativeStockAccordance;
					curRow.BranchAllowNegativeStock = sheetRow.BranchAllowNegativeStock;
					#endregion
					rowsDict.Add(curRow);
				}
				else
				{
					curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
					curRow.sub_amount += sheetRow.sub_amount;
				}
			}
			return rowsDict;

		}

		public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting);

			if (printTemplate != null)
			{
				bool canMerge2Rows(TROW row1, TROW row2)
				{
					return row1.item_id == row2.item_id && row1.branch_id == row2.branch_id && row1.branch_position == row2.branch_position
						&& row1.batch_id == row2.batch_id;

				}
				SQLQueue QQ = new SQLQueue(cmd);
				string sPrintTemplate = JsonConvert.SerializeObject(printTemplate);
				if (sPrintTemplate.Contains("\"name\":\"give_qty_unit\""))
				{
					List<TROW> newRows = new List<TROW>();
					List<TROW> usedGiveRows = new List<TROW>();
					foreach (var row in SheetRows)
					{
						if (row.quantity > 0)
						{
							if (row.real_price > 0)
							{
								newRows.Add(row);
								decimal give_qty = 0;
								decimal giveRowCount = 0;
								TROW rowGive1 = null;
								foreach (var rowGive in SheetRows)
								{
									if (canMerge2Rows(row, rowGive) && rowGive.real_price == 0 && rowGive.quantity > 0)
									{
										if (!usedGiveRows.Contains(rowGive))
										{
											give_qty += rowGive.quantity * rowGive.unit_factor;
											giveRowCount++;
											rowGive1 = rowGive;
											usedGiveRows.Add(rowGive);
										}
									}
								}

								if (giveRowCount == 1)
								{
									row.give_qty_unit = rowGive1.quantity.ToString() + rowGive1.unit_no;
								}
								else if (giveRowCount > 1)
								{
									row.give_qty_unit = GetUnitQty(give_qty, row.b_unit_no, row.m_unit_no, row.s_unit_no, row.b_unit_factor, row.m_unit_factor);
								}
							}
                            else if (row.real_price == 0)
                            {
                                bool bMetPriceRow=false;
                                
                                foreach (var row1 in SheetRows)
                                {
                                    if (row1.real_price>0 && canMerge2Rows(row1, row))
                                    {
                                        bMetPriceRow = true;
                                    }
                                }

                                if (!bMetPriceRow && !usedGiveRows.Contains(row))
                                {
                                    row.give_qty_unit = row.quantity.ToString() + row.unit_no;
                                    row.quantity = 0;
                                    newRows.Add(row);
                                    usedGiveRows.Add(row);
                                }
                            }
						}
						else newRows.Add(row);

					}

					foreach (var row in SheetRows)
					{
						if (row.real_price == 0 )
						{
                            if (!usedGiveRows.Contains(row))
                            {
                                row.give_qty_unit = row.quantity.ToString() + row.unit_no;
                                row.quantity = 0;
                                newRows.Add(row);
                            }
							/*var metRow = usedGiveRows.Find(r => canMerge2Rows(row, r));

							if (metRow == null)
							{
								row.give_qty_unit = row.quantity.ToString() + row.unit_no;
								row.quantity = 0;
								newRows.Add(row);
							}*/
						}
					}
					SheetRows = newRows;

				}

				if (sPrintTemplate.Contains("\"prepay_balance\"") && supcust_id!="")
				{
					string sql = $"select b.sub_id,sub_type payway_type,sub_name,round(balance::numeric,2) balance from prepay_balance b left join cw_subject p on p.sub_id = b.sub_id where b.company_id = {company_id} and supcust_id = {supcust_id} and (not is_order or is_order is null);";
					QQ.Enqueue("prepay", sql);
				}
				if (sPrintTemplate.Contains("\"order_item_balance\"") && supcust_id != "")
				{
					string sql = $"select b.sub_id,sub_type payway_type,sub_name,round(balance::numeric,2) balance from prepay_balance b left join cw_subject p on p.sub_id = b.sub_id where b.company_id = {company_id} and supcust_id = {supcust_id} and is_order;";
					QQ.Enqueue("order_item_balance", sql);
				}
				/*if (sPrintTemplate.Contains("\"order_item_left_items\""))
                {
                    string sql = $"select b.sub_id,sub_type payway_type,sub_name,round(balance::numeric,2) balance from order_item_balance b left join info_item_prop p on b.item_id = p.item_id and p.company_id={company_id} where b.company_id = {company_id} and supcust_id = {supcust_id};";
                    QQ.Enqueue("order_item_left_items", sql);
                }*/

				if (sPrintTemplate.Contains("\"arrears_balance\"") && supcust_id != "")
				{
					string sql = $"select round(balance::numeric,2) balance from arrears_balance where company_id = {company_id} and supcust_id = {supcust_id} and balance<>0;";
					QQ.Enqueue("arrears", sql);
				}
				if (sPrintTemplate.Contains("\"print_count\"")
					|| sPrintTemplate.Contains("{打印次数}"))
				{
					string tb = "";
					if (sheet_type == SHEET_TYPE.SHEET_SALE || sheet_type == SHEET_TYPE.SHEET_SALE_RETURN)
					{
						tb = "sheet_status_sale";
					}
					else if (sheet_type == SHEET_TYPE.SHEET_SALE_DD || sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN)
					{
						tb = "sheet_status_order";
					}
					else if (sheet_type == SHEET_TYPE.SHEET_ARREARS_GRANT || sheet_type == SHEET_TYPE.SHEET_ARREARS_REVOKE)
					{
						tb = "sheet_status_arrears_bill";
					}
					if (tb != "")
					{
						string sql = $"select sheet_print_count from {tb} where company_id = {company_id} and sheet_id = {sheet_id};";
						QQ.Enqueue("print_count", sql);
					}
				}
				if (QQ.Count > 0)
				{
					CMySbDataReader dr;
					try
					{
						dr = await QQ.ExecuteReaderAsync();
					}
					catch (Exception ex)
					{
						NLogger.Error("[LoadInfoForPrint] 加载打印信息失败，语句为" + QQ.SQL);
						NLogger.Error(ex.ToString());
                        string logSQL= QQ.SQL.Replace("'", "''");
                        MyLogger.LogMsg("[LoadInfoForPrint] 加载打印信息失败，语句为" + logSQL, this.company_id);
						throw new Exception("加载打印信息失败");
					}
					while (QQ.Count > 0)
					{
						string tbl = QQ.Dequeue();

						if (tbl == "prepay")
						{
							List<ExpandoObject> lstRecords = CDbDealer.GetRecordsFromDr(dr, false);
							this.prepay_balance = "";
							foreach (dynamic rec in lstRecords)
							{
								decimal bal = decimal.Parse(rec.balance);
								decimal reduce_amt = 0m;
								if (payway1_id == rec.sub_id) reduce_amt += payway1_amount;
								if (payway2_id == rec.sub_id) reduce_amt += payway2_amount;
								if (payway3_id == rec.sub_id) reduce_amt += payway3_amount;

								if (reduce_amt != 0 && !this.approve_time.IsValid())
								{
									bal -= reduce_amt * this.money_inout_flag;
								}

								if (this.prepay_balance != "") this.prepay_balance += " ";
								this.prepay_balance += rec.sub_name + "余额:" + CPubVars.FormatMoney(bal, 2, true);
							}
						}
						else if (tbl == "order_item_balance")
						{
							List<ExpandoObject> lstRecords = CDbDealer.GetRecordsFromDr(dr, false);
							this.order_item_balance = "";
							foreach (dynamic rec in lstRecords)
							{
								if (this.order_item_balance != "") this.order_item_balance += " ";
								decimal bal = decimal.Parse(rec.balance);
								decimal reduce_amt = 0m;
								if (payway1_id == rec.sub_id) reduce_amt += payway1_amount;
								if (payway2_id == rec.sub_id) reduce_amt += payway2_amount;
								if (payway3_id == rec.sub_id) reduce_amt += payway3_amount;

								if (reduce_amt != 0 && !this.approve_time.IsValid())
								{
									bal -= reduce_amt * this.money_inout_flag;
								}
								this.order_item_balance += rec.sub_name + "余额:" + CPubVars.FormatMoney(bal, 2, true);
							}
						}

						else if (tbl == "arrears")
						{
							dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
							if (rec != null)
							{
								this.arrears_balance = rec.balance;
								if (!approve_time.IsValid())
								{
									this.arrears_balance = CPubVars.FormatMoney(CPubVars.ToDecimal(this.arrears_balance) + this.left_amount * this.money_inout_flag, 2);
								}
							}
						}
						else if (tbl == "print_count")
						{
							dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
							if (rec != null)
							{
								this.print_count = rec.sheet_print_count;
							}
							if (this.print_count == "") this.print_count = "1";
							else this.print_count = (Convert.ToInt32(this.print_count) + 1).ToString();
						}
					}
					QQ.Clear();
				}
			}

			decimal b_qty = 0, m_qty = 0, s_qty = 0;
			decimal b_qty_t = 0, m_qty_t = 0, s_qty_t = 0;
            Dictionary<string, decimal> dicUnitQty = new Dictionary<string, decimal>();
			Dictionary<string, decimal> dicUnitQty_t = new Dictionary<string, decimal>();
			Dictionary<string, decimal> dicSUnitQty = new Dictionary<string, decimal>();
			Dictionary<string, decimal> dicSUnitQty_t = new Dictionary<string, decimal>();
			decimal l_sum_s_sub_qty = 0;
			decimal l_sum_s_sub_qty_t = 0;
			decimal l_sum_quantity = 0;
			decimal l_sum_quantity_t = 0;
            decimal l_sum_sub_amount = 0;
			foreach (var row in SheetRows)
            {
                row.SetInfoForPrint(smallUnitBarcode);
                if (row.inout_flag == 0) continue;
				l_sum_sub_amount += row.sub_amount;

				if (row.quantity > 0 )
                {
					b_qty += row.b_quantity;
					m_qty += row.m_quantity;
					s_qty += row.s_quantity;
                    if(row.s_sub_qty!="")
						l_sum_s_sub_qty += CPubVars.ToDecimal(row.s_sub_qty);


					if (dicUnitQty.ContainsKey(row.unit_no))
                    {
                        dicUnitQty[row.unit_no] += row.quantity;
                    }
                    else dicUnitQty.Add(row.unit_no, row.quantity);

					if (dicSUnitQty.ContainsKey(row.s_unit_no))
					{
						dicSUnitQty[row.s_unit_no] += row.quantity*row.unit_factor;
					}
					else dicSUnitQty.Add(row.s_unit_no, row.quantity*row.unit_factor);

                    l_sum_quantity += row.quantity;

				}
                else if (row.quantity < 0)
                {
					b_qty_t +=Math.Abs(row.b_quantity);
					m_qty_t +=Math.Abs(row.m_quantity);
					s_qty_t +=Math.Abs(row.s_quantity);

					if (row.s_sub_qty != "")
						l_sum_s_sub_qty_t += (-1) * CPubVars.ToDecimal(row.s_sub_qty);

					if (dicUnitQty_t.ContainsKey(row.unit_no))
                    {
                        dicUnitQty_t[row.unit_no] += (-1)*row.quantity;
                    }
                    else dicUnitQty_t.Add(row.unit_no, (-1) * row.quantity);

					if (dicSUnitQty_t.ContainsKey(row.s_unit_no))
					{
						dicSUnitQty_t[row.s_unit_no] += (-1) * row.quantity * row.unit_factor;
					}
					else dicSUnitQty_t.Add(row.s_unit_no, (-1) * row.quantity * row.unit_factor);

					l_sum_quantity_t += (-1) * row.quantity;
					
				}

                if (row.quantity < 0) row.item_name = "(退)" + row.item_name;
		

			}
            string sumQty = "";
            if (b_qty != 0) sumQty += CPubVars.FormatMoney(b_qty, 2) + "大";
            if (m_qty != 0) sumQty += CPubVars.FormatMoney(m_qty, 2) + "中";
            if (s_qty != 0) sumQty += CPubVars.FormatMoney(s_qty, 2) + "小";

			string sumQty_t = "";
			if (b_qty_t != 0) sumQty_t += CPubVars.FormatMoney(b_qty_t, 2) + "大";
			if (m_qty_t != 0) sumQty_t += CPubVars.FormatMoney(m_qty_t, 2) + "中";
			if (s_qty_t != 0) sumQty_t += CPubVars.FormatMoney(s_qty_t, 2) + "小";
            if (sumQty_t != "") sumQty += " 退:" + sumQty_t;
			this.sum_quantity_unit_conv = sumQty;
			this.sum_quantity_unit_conv_trade = sumQty;
            this.sum_qty_unit = sumQty;
            this.sum_qty_unit_trade = sumQty;
			this.sum_item_name = "合计: " + MoneyToUpper(this.total_amount.ToString());



			this.sum_quantity = CPubVars.FormatMoney(l_sum_quantity, 3);
            if (l_sum_quantity_t != 0)
            {
                this.sum_quantity += " 退" + CPubVars.FormatMoney(l_sum_quantity_t,3);
			}
            this.sum_quantity_trade = this.sum_quantity;


			this.sum_s_sub_qty = CPubVars.FormatMoney(l_sum_s_sub_qty, 3);
			if (l_sum_s_sub_qty_t != 0) this.sum_s_sub_qty += " 退" + CPubVars.FormatMoney(l_sum_s_sub_qty_t, 3);


			string sumUnitQty = "";
            foreach (var unit in dicUnitQty)
            {
                sumUnitQty += CPubVars.FormatMoney(unit.Value, 3) + unit.Key;
            }
			string qty_t = "";
			
			foreach (var unit in dicUnitQty_t)
			{
				qty_t +=   CPubVars.FormatMoney(unit.Value, 3)+ unit.Key;
			}
            if (qty_t != "") sumUnitQty += " 退:" + qty_t;
			this.total_qty_unit = sumUnitQty;
                        	


			this.sum_s_sub_qty_unit = "";
            string qty = "";
			foreach (var unit in dicSUnitQty)
			{
				qty += CPubVars.FormatMoney(unit.Value, 3) + unit.Key;
			}
            this.sum_s_sub_qty_unit = qty;
			qty_t = "";
			foreach (var unit in dicSUnitQty_t)
			{
				qty_t += CPubVars.FormatMoney(unit.Value, 3) + unit.Key;
			}			
            if (qty_t != "") this.sum_s_sub_qty_unit += "退" + qty_t;

            this.sum_sub_amount = CPubVars.FormatMoney(l_sum_sub_amount, 2); 


			//


		}

        public void LoadAttrRowsForPrint(bool smallUnitBarcode)
        {
            List<TROW> lstRows = new List<TROW>();
            foreach (var row in SheetRows)
            {
                JArray arr = null;
                if (row.attr_qty != "" && row.attr_qty != "[]")
                {
                    dynamic attrQty = JsonConvert.DeserializeObject(row.attr_qty);
                    arr = (JArray)attrQty;
                }
                if (!row.son_mum_item.IsValid())//不区分库存的口味商品
                {
                    if (arr != null && row.sale_print_combine_attr.ToString().ToLower() != "true")
                    {
                        foreach (JObject attrRow in arr)
                        {
                            string optNames = "";
                            string optID = "";
                            foreach (var prop in attrRow.Properties())
                            {
                                string optName = "";
                                if (prop.Name.IndexOf("optName_") == 0)
                                {
                                    optName = prop.Value.ToString();
                                    if (optNames != "") optNames += "_";
                                    optNames += optName;
                                }
                                else if (prop.Name.IndexOf("optID_") == 0)
                                {
                                    optID = prop.Value.ToString(); 
                                }
                            }

                            string qty = attrRow.GetValue("qty").ToString();
                            TROW newRow = (TROW)row.Clone();
                            if (optNames != "")
                            {
                                newRow.item_name += "(" + optNames + ")";
                            }

                            newRow.attr_qty = "";
                            newRow.quantity = CPubVars.ToDecimal(qty);
                            newRow.sub_amount = newRow.real_price * newRow.quantity;
                            newRow.s_barcode = attrRow.GetValue("sBarcode")?.ToString();
                            newRow.m_barcode = attrRow.GetValue("mBarcode")?.ToString();
                            newRow.b_barcode = attrRow.GetValue("bBarcode")?.ToString();
                            JObject attr = null; JArray availCombineRows = null;
                            if (typeof(SheetRowSaleOrder) == row.GetType())
                            {
                                SheetRowSaleOrder saleRow = row as SheetRowSaleOrder;
                                if (saleRow._mum_attributes != null && saleRow._mum_attributes.Count > 0) attr = (JObject)saleRow._mum_attributes[0];
                            }
                            if (typeof(SheetRowSale) == row.GetType())
                            {
                                SheetRowSale saleRow = row as SheetRowSale;
                                if (saleRow._mum_attributes != null && saleRow._mum_attributes.Count > 0) attr = (JObject)saleRow._mum_attributes[0];
								if (saleRow._avail_attr_combine != null && saleRow._avail_attr_combine.Count > 0) availCombineRows = (JArray)saleRow._avail_attr_combine;
							}

                            if (attr != null)
                            {
                                JArray opts = (JArray)attr.GetValue("options"); 
                                foreach (dynamic opt in opts)
                                {
                                    if (opt.optID == optID)
                                    {
                                        newRow.s_barcode = opt.sBarcode;
                                        newRow.b_barcode = opt.bBarcode;
                                        newRow.m_barcode = opt.mBarcode;
                                    }
                                }
                            }

							if (availCombineRows != null)
							{ 
								foreach (dynamic cmb in availCombineRows)
								{
									if (cmb.son_options_id == optID)
									{
										newRow.s_barcode = cmb.sBarcode;
										newRow.b_barcode = cmb.bBarcode;
										newRow.m_barcode = cmb.mBarcode;
									}
								}
							}

							

                            
                            // newRow.barcode = smallUnitBarcode ? newRow.s_barcode : newRow.b_barcode;
                            newRow.barcode = newRow.unit_no == newRow.b_unit_no ? newRow.b_barcode : newRow.unit_no == newRow.m_unit_no ? newRow.m_barcode : newRow.s_barcode;
                            if (smallUnitBarcode)
                            {
                                newRow.barcode = newRow.s_barcode;
                            }
                            newRow.attr_qty = "";
                            lstRows.Add(newRow);
                        }
                    }
                    else lstRows.Add(row);
                }
                else//区分库存的口味商品
                {
                    if (arr != null)
                    {
                        foreach (JObject attrRow in arr)
                        {
                            string optNames = "";
                            foreach (var prop in attrRow.Properties())
                            {
                                string optName = "";
                                if (prop.Name.IndexOf("optName_") == 0)
                                {
                                    optName = prop.Value.ToString();
                                    if (optNames != "") optNames += "_";
                                    optNames += optName;
                                }
                            }

                            if (!row.s_barcode.IsValid())
                                row.s_barcode = attrRow.GetValue("sBarcode")?.ToString();
                            if (!row.b_barcode.IsValid())
                                row.b_barcode = attrRow.GetValue("bBarcode")?.ToString();
                            row.barcode = smallUnitBarcode ? row.s_barcode : row.b_barcode;

                        }
                    }
                    lstRows.Add(row);
                }
            }
            SheetRows = lstRows;
            CombineAttrRowsForPrint();
        }
        public override string GetDataLockKey()
        {
            return this.company_id + "_" + this.branch_id;
        }
        public void CombineAttrRowsForPrint()
        {
            Dictionary<string, TROW> dicCombineRows = new Dictionary<string, TROW>();
            string getRowKey(TROW row)
            {
                return row.trade_type + "_" + row.son_mum_item + "_" + row.unit_no + "_" + row.real_price + "_" + Math.Sign(row.quantity).ToString();
            }

            foreach (var row in SheetRows)
            {
                if (row.son_mum_item != "" && row.sale_print_combine_attr.ToString().ToLower() == "true")
                {
                    TROW combineRow = null;

                    string key = getRowKey(row);
                    if (!dicCombineRows.ContainsKey(key))
                    {
                        combineRow = row;
                        combineRow.son_mum_item = "";
                        combineRow.item_name = combineRow.item_name.Split("(")[0];
                        dicCombineRows.Add(key, combineRow);
                    }
                    else
                    {
                        combineRow = dicCombineRows[key];
                        combineRow.quantity += row.quantity;
                        combineRow.sub_amount = CPubVars.ToDecimal(CPubVars.FormatMoney(combineRow.sub_amount + row.quantity * row.real_price, 2));
                    }
                }

            }

            for (var i = SheetRows.Count - 1; i >= 0; i--)
            {
                var row = SheetRows[i];
                if (row.son_mum_item != "")
                {
                    string key = getRowKey(row);
                    if (dicCombineRows.ContainsKey(key))
                    {
                        SheetRows.RemoveAt(i);
                    }
                }
            }

        }
        //        /// <summary>
        //        /// 为销售单/退货单/采购单/采购退货单 更新加权平均价,会影响 sheet_sale_detail中的cost_price_avg,info_item_prop中的cost_price_avg,cost_amt
        //        /// 
        //        /// </summary>
        //        /// <param name="cmd"></param>
        //        /// <param name="infoB">审核时获取到的信息</param>
        //        /// <param name="dealing_happen_time">此单据的发生时间,如果是红冲单,就是被红冲的单据的发生时间</param>
        //        /// <returns></returns>
        //        public async Task UpdateCostPriceAvg(CMySbCommand cmd, CInfoForApproveBase infoB, string dealing_happen_time)
        //        {
        //            string updateItemProp = "";
        //            string updateSaleSql = "";
        //            string log_sql = "";
        //            CInfoForApprove info = (CInfoForApprove)infoB;
        //            List<SheetRowMM> lstRows = new List<SheetRowMM>();

        //            var mergeRowAvg = MergeSheetRowsForAvg(info.SheetRows);//单据里相同商品的合并，当前单据

        //            //获取从 单据发生时间的前一刻 至今 该单据商品的变化的数量和成本额（采购单，销售单，盘点盈亏单)

        //            List<dynamic> lstHistorySheetItems = new List<dynamic>();

        //            if (!HappenNow)
        //            {
        //                dynamic res = (await GetHistorySheetItemsAfterMe(cmd, dealing_happen_time)).Value;

        //                // res.data:[{ sheet_id,sheet_type,money_inout_flag, t.happen_time, t.item_id,quantity,t.unit_factor,inout_flag,t.cost_price_avg,    cost_price_suspect,                        cost_price_buy,ip.cost_price_avg as cost_price_avg_info}]

        //                if (res.result != "OK")
        //                {
        //                    info.ErrMsg = "GetHistorySheetItemsAfterMe return error";
        //                    return;
        //                }

        //                List<ExpandoObject> tmp =  res.data;
        //                List<dynamic> lst = new List<dynamic>();
        //                foreach(dynamic d in tmp)
        //                {
        //                    lst.Add(d);
        //                }
        //                var sumLst = lst.GroupBy(r => new { r.sheet_id, r.sheet_type, r.item_id, r.happen_time, r.cost_price_avg, r.cost_price_avg_info })
        //                    .Select(g =>
        //                        new
        //                        {
        //                            sheet_id = (string)g.Key.sheet_id,
        //                            item_id = (string)g.Key.item_id,
        //                            happen_time = (string)g.Key.happen_time,
        //                            sheet_type = (string)g.Key.sheet_type,
        //                            cost_price_avg = (string)g.Key.cost_price_avg,
        //                            cost_price_avg_info = (string)g.Key.cost_price_avg_info,
        //                            sheet_qty = g.Sum(c => {
        //                                return CPubVars.ToDecimal(c.quantity) * CPubVars.ToDecimal(c.unit_factor) * CPubVars.ToDecimal(c.inout_flag);
        //                            }
        //                            ),
        //                            sheet_amount = g.Sum(c => {
        //                                if (c.sheet_type == "CG" || c.sheet_type == "CT")
        //                                {
        //                                    decimal d = CPubVars.ToDecimal(c.sub_amount) * CPubVars.ToDecimal(c.inout_flag);
        //                                    return d;
        //                                }
        //                                else
        //                                {
        //                                    decimal d = CPubVars.ToDecimal(c.quantity) * CPubVars.ToDecimal(c.unit_factor) * CPubVars.ToDecimal(c.inout_flag);
        //                                    if (c.cost_price_avg != "")
        //                                        d *= CPubVars.ToDecimal(c.cost_price_avg);
        //                                    else if (c.cost_price_avg_info != "")
        //                                        d *= CPubVars.ToDecimal(c.cost_price_avg_info);
        //                                    else d = 0;
        //                                    return d;
        //                                } 

        //                            })
        //                        }
        //                   ).OrderBy(g => g.happen_time).ThenBy(g => g.item_id).ToList();

        //                sumLst.ForEach(r =>
        //                {
        //                    lstHistorySheetItems.Add(r);
        //                });
        //            }


        //            foreach (TROW sheetRow in mergeRowAvg)  
        //            {
        //                decimal sum_qty_change = 0;

        //                decimal firstCostPriceAvg = -1;//从本单据向后 第一个 有效成本价
        //                if (lstHistorySheetItems.Count > 0)
        //                {
        //                    sum_qty_change = lstHistorySheetItems.Sum(i => i.item_id == sheetRow.item_id ? CPubVars.ToDecimal(i.sheet_qty) : 0);

        //                    foreach(var r in lstHistorySheetItems)
        //                    {
        //                        if(r.cost_price_avg!="")
        //                        {
        //                            firstCostPriceAvg = CPubVars.ToDecimal(r.cost_price_avg);
        //                            break;
        //                        }
        //                    } 
        //                }


        //                decimal earliest_stock_qty = sheetRow.old_total_qty; //审核单据前的库存数
        //                decimal earliest_cost_amt = sheetRow.old_total_qty * sheetRow.cost_price_avg;  //sheetRow.old_cost_amt;//单据审核前的库存金额
        //                decimal earliest_cost_price_avg = sheetRow.cost_price_avg;

        //                //算出发生时间之前的平均价 
        //                if (firstCostPriceAvg != -1)
        //                {
        //                    earliest_cost_price_avg = firstCostPriceAvg;
        //                    earliest_stock_qty -= sum_qty_change; 
        //                    earliest_cost_amt = earliest_cost_price_avg * earliest_stock_qty; 
        //                }

        //                decimal now_qty = earliest_stock_qty;
        //                decimal now_cost_amt = earliest_cost_amt;  
        //                decimal now_cost_price_avg = earliest_cost_price_avg;

        //                bool bSuspecting = false;

        //                if (now_qty < 0)
        //                {
        //                    bSuspecting = true;
        //                }

        //                //遍历某个商品 从本单据插入前一刻 至今 的所有单据

        //                List<dynamic> lstDealSheetItems = new List<dynamic>();//当前商品在 当前单据 及 以后的单据中的列表

        //                //将该商品在 当前单据 的信息 加入 待处理列表
        //                dynamic row = new ExpandoObject();
        //                row.item_id = sheetRow.item_id;
        //                row.sheet_id = this.sheet_id;
        //                row.sheet_type = this.SheetType;
        //                row.happen_time = this.happen_time;
        //                row.sheet_qty = sheetRow.quantity;
        //                row.sheet_amount = sheetRow.sub_amount;
        //                // row.cost_price_buy=sheetRow.cost_price_buy
        //                lstDealSheetItems.Add(row);

        //                //将该商品在 当前单据以后的历史单据 的信息 加入 待处理列表
        //                if (firstCostPriceAvg!=-1)
        //                { 
        //                    foreach (dynamic item in lstHistorySheetItems)
        //                    {
        //                        if (sheetRow.item_id == item.item_id)
        //                        {
        //                            lstDealSheetItems.Add(item);
        //                        }
        //                    }
        //                }

        //                foreach (dynamic item in lstDealSheetItems)
        //                { 
        //                    decimal qty_change = CPubVars.ToDecimal(item.sheet_qty);
        //                    decimal amount_change = 0;
        //                    if (",X,T,YK,BS,".Contains("," + item.sheet_type + ","))
        //                    {
        //                        amount_change = qty_change * now_cost_price_avg;
        //                    }
        //                    else if (item.sheet_type == "CG" || item.sheet_type == "CT")
        //                    {
        //                        amount_change = CPubVars.ToDecimal(item.sheet_amount);
        //                    }

        //                    decimal pre_qty = now_qty;
        //                    now_qty += qty_change;
        //                    now_cost_amt += amount_change;

        //                    if (now_qty < 0 || pre_qty < 0)
        //                    {
        //                        bSuspecting = true;

        //                        log_sql += @$"insert into cost_price_log (
        //          company_id,  sheet_id,       item_id,  happen_time,         sheet_type, infected_sheet_id,   suspect_status) 
        // values ({company_id},{sheet_id},{item.item_id},'{happen_time}','{item.sheet_type}','{item.sheet_id}',             '1')
        //on conflict(company_id,sheet_id,       item_id,   happen_time,        sheet_type, infected_sheet_id) do update set suspect_status = '1';";

        //                    }

        //                    if (!bSuspecting)
        //                    {
        //                        //如果是采购单，需要重算加权平均价   
        //                        if (item.sheet_type == "CG" || item.sheet_type == "CT")
        //                        {
        //                            //如果采购后导致货值或库存为0 取本次采购的进价,防止出现加权价为0的情况
        //                            if (now_qty > 0)
        //                                now_cost_price_avg = now_cost_amt / now_qty;

        //                            /*
        //                            decimal buy_price = sheetRow.sub_amount / sheetRow.quantity;

        //                            //发现加权价可能计算错误，就取预设进价
        //                            if (now_cost_price_avg < 0 || (buy_price > 0 && now_cost_price_avg > 10 * buy_price))
        //                            {
        //                                now_cost_price_avg = buy_price;
        //                                now_cost_amt = now_qty * now_cost_price_avg;
        //                            }*/
        //                        }
        //                    }

        //                    if (!HappenNow)
        //                    {
        //                        if (item.sheet_type == "X" || item.sheet_type == "T")
        //                            updateSaleSql += @$"update sheet_sale_detail set cost_price_avg = {now_cost_price_avg}, cost_price_suspect = {bSuspecting} where company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id};";
        //                        else if (item.sheet_type == "YK" || item.sheet_type == "BS")
        //                            updateSaleSql += @$"update sheet_invent_change_detail set cost_price_avg = {now_cost_price_avg} where company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id};";

        //                    } 
        //                } 
        //                if(sheetRow.item_cost_price_suspect!=bSuspecting || now_cost_price_avg != sheetRow.cost_price_avg)
        //                {
        //                    updateItemProp += $"update info_item_prop set cost_price_avg = round({now_cost_price_avg},4), item_cost_price_suspect = {bSuspecting} where company_id = {this.company_id} and item_id = {sheetRow.item_id}; ";

        //                }

        //            }

        //            string sql = updateItemProp + updateSaleSql + log_sql;
        //            if (sql != "")
        //            {
        //                cmd.CommandText = sql;
        //                await cmd.ExecuteNonQueryAsync();
        //            }
        //        }

        //        /// <summary>
        //        /// 获取此单据中的商品在 发生时间 后面的所有单据(采购/盘点盈亏/销售)中出现的记录
        //        /// </summary>
        //        /// <param name="cmd"></param>
        //        /// <param name="dealing_happen_time">此单据的发生时间,如果是红冲单,就是被红冲的单据的发生时间</param>
        //        /// <returns></returns>
        //        public async Task<JsonResult> GetHistorySheetItemsAfterMe(CMySbCommand cmd, string dealing_happen_time)
        //        {
        //            string sql = "";
        //            string items_id = string.Join(",", SheetRows.Select(r => r.item_id));
        //            SQLQueue QQ = new SQLQueue(cmd);
        //            var condi = $" and sd.sheet_id<>{this.sheet_id} ";

        //            if (red_flag == "2") condi = $" and sd.sheet_id<>{this.red_sheet_id} ";
        //            //找出插入单据那一刻
        //            sql = @$" 
        //select        sheet_id,sheet_type,money_inout_flag, t.happen_time, t.item_id,quantity,t.unit_factor,inout_flag,t.cost_price_avg,    cost_price_suspect,                        cost_price_buy,ip.cost_price_avg as cost_price_avg_info from 
        //(
        //    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg ,   cost_price_suspect,                        cost_price_buy
        //    from sheet_sale_detail sd 
        //    left join sheet_sale_main m on m.sheet_id = sd.sheet_id 
        //    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>='{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}

        //    UNION

        //    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg,false cost_price_suspect, real_price/unit_factor cost_price_buy  
        //    from sheet_buy_detail sd 
        //    left join sheet_buy_main m on sd.sheet_id = m.sheet_id 
        //    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>='{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}

        //    UNION

        //    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg,false cost_price_suspect,           sd.buy_price  cost_price_buy    
        //    from sheet_invent_change_detail sd left join sheet_invent_change_main m on sd.sheet_id = m.sheet_id  
        //    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>='{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}

        //) t 
        //left join info_item_prop ip on t.item_id = ip.item_id and ip.company_id={this.company_id}
        //order by t.item_id,t.happen_time";
        //            QQ.Enqueue("data", sql);
        //            // sql = $@"select t.item_id,sum(coalesce(sheet_qty,0)) qty_change,sum(coalesce(sheet_amount,0)) amount_change from ({sql}) t 
        //            //             group by t.item_id ";
        //            // QQ.Enqueue("total", sql);
        //            List<ExpandoObject> data = null;
        //            // List<ExpandoObject> total = null;
        //            var dr = await QQ.ExecuteReaderAsync();
        //            while (QQ.Count > 0)
        //            {
        //                var sqlName = QQ.Dequeue();
        //                if (sqlName == "data") data = CDbDealer.GetRecordsFromDr(dr, false);
        //                // else if (sqlName == "total") total = CDbDealer.GetRecordsFromDr(dr, false);
        //            }
        //            QQ.Clear();
        //            return new JsonResult(new { result = "OK", data });
        //        }
        //        /// <summary>
        //        /// 将单据中相同item_id的商品合并到一行
        //        /// </summary>
        //        /// <param name="rows"></param>
        //        /// <returns></returns>
        //        protected List<TROW> MergeSheetRowsForAvg(List<TROW> rows)
        //        {
        //            Dictionary<string, TROW> rowsDict = new Dictionary<string, TROW>();
        //            foreach (TROW sheetRow in SheetRows)
        //            {
        //                string skey = sheetRow.item_id;// +"_" + sheetRow.unit_factor.ToString();
        //                TROW curRow = null;
        //                rowsDict.TryGetValue(skey, out curRow);
        //                if (curRow == null)
        //                {
        //                    curRow = new TROW();
        //                    curRow.item_id = sheetRow.item_id;
        //                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor * sheetRow.inout_flag;
        //                  //  curRow.old_cost_price_avg = sheetRow.old_cost_price_avg;
        //                    curRow.cost_price_avg = sheetRow.cost_price_avg;
        //                    curRow.item_cost_price_suspect = sheetRow.item_cost_price_suspect;
        //                    curRow.old_total_qty = sheetRow.old_total_qty;                   
        //                    curRow.inout_flag = sheetRow.inout_flag;
        //                    curRow.sub_amount = sheetRow.sub_amount * sheetRow.inout_flag;

        //                    curRow.real_price = sheetRow.real_price;
        //                    curRow.unit_factor = sheetRow.unit_factor;
        //                    rowsDict.Add(skey, curRow);
        //                }
        //                else
        //                {
        //                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor * sheetRow.inout_flag;
        //                    curRow.sub_amount += sheetRow.sub_amount * sheetRow.inout_flag;

        //                }
        //            }
        //            List<TROW> newList = new List<TROW>();
        //            foreach (var k in rowsDict)
        //            {
        //                newList.Add(k.Value);
        //            }
        //            return newList;

        //        }


        //未开启严格生产日期模式=》batch_id = 0
        public override async Task<string> BeforeRedAndChange(CMySbCommand cmd)
        {
            return await CheckBatch(cmd);
        }

        public async Task<string> CheckBatch(CMySbCommand cmd)
        {
            string msg = "";
            string insertSql = "";
            string insertValue = "";
            string selectSql = "";
            string selectValue = "";
            Dictionary<string, dynamic> batchDic = new Dictionary<string, dynamic>();
            Dictionary<string, string> sheetRowBatch = new Dictionary<string, string>();
            try
            {

                foreach (TROW row in SheetRows)
                {
                    if (row.produce_date.IsInvalid() || row.produce_date == "无产期")
                    {
                        continue;
                    }
                    if (row.produce_date.Length != 10 || !CPubVars.IsDate(row.produce_date))
                    {
                        msg = row.item_name + "的" + row.produce_date + "这个产期的格式不正确";
                        goto endFunc;
                        
                       
                    }
                    string key = row.produce_date + row.batch_no;
                    if (!batchDic.ContainsKey(key))
                    {
                        batchDic[key] = new { produce_date = row.produce_date, batch_no = row.batch_no };
                        if(selectValue!="")  selectValue += ",";
                        selectValue += $@"('{row.produce_date}','{row.batch_no}')";
                    }
                }
                if (selectValue != "") selectSql = $@"select * from info_item_batch where company_id = {company_id} and (substring(produce_date::text,1,10),batch_no) in ({selectValue});";
                if (selectSql != "")
                {
                    List<ExpandoObject> selectRec = await CDbDealer.GetRecordsFromSQLAsync(selectSql, cmd);
                    foreach (dynamic row in selectRec)
                    {
                        string produceDate = row.produce_date;
                        string batchNo = row.batch_no;
                       
                        produceDate = produceDate.Substring(0, 10);
                        string key = produceDate + batchNo;
                        sheetRowBatch.Add(key, row.batch_id);
                        if (batchDic.ContainsKey(key)) batchDic.Remove(key);
                    }
                    foreach(KeyValuePair<string,dynamic> kv in batchDic)
                    {
                        string produceDate = kv.Value.produce_date;
                       
                        string batchNo = kv.Value.batch_no;
                        if(insertValue!="")  insertValue += ",";
                        insertValue += $@"({company_id},'{produceDate}','{batchNo}')";
                    }
                    if (insertValue!="")//INSERT INTO
                    {
                        insertSql += $"insert into info_item_batch (company_id,produce_date,batch_no) values {insertValue} on CONFLICT(company_id,produce_date,batch_no) DO NOTHING RETURNING batch_id,produce_date,batch_no;";
                        List<ExpandoObject> insertRec = await CDbDealer.GetRecordsFromSQLAsync(insertSql, cmd);
                        foreach (dynamic row in insertRec)
                        {
                            string produceDate = row.produce_date;
                            string batchNo = row.batch_no;
                            produceDate = produceDate.Substring(0, 10);
                            string key = produceDate + batchNo;
                            sheetRowBatch.Add(key, row.batch_id);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                msg = "生产日期/批次错误";
                MyLogger.LogMsg(msg + e.Message + e.StackTrace + "SQL:" + cmd.CommandText, company_id, "produce_date");
            }
            if (msg == "")
            {
                foreach (TROW row in SheetRows)
                {
                    if (row.produce_date.IsInvalid() || row.produce_date == "无产期")
                    {
                        row.batch_id = "0";
                        continue;
                    }
                    string key = row.produce_date + row.batch_no;
                    if (sheetRowBatch.ContainsKey(key))
                    {
                        row.batch_id = sheetRowBatch[key];
                    }
                    else
                    {
                        msg = "生产日期/批次错误";
                    }
                }
            }
          endFunc:
            return msg;
        }
 

    }
}
