# 消息系统

# 1. 系统功能需求分析

1.  消息系统分类
   - 待办事项
   - 通知
2. 对接形式
   - Web：（1）左上角弹窗（2）页面列表展示 （3）首页待办事项卡片
   - App：（1）导航栏页面 （2）手机系统级别弹窗 （3）App内部弹窗
   - 小程序：（1）消息通知的列表组件

# 2. 系统功能功能设计

## 2.1 待办事项

**谁** 在 **何时** 因为 **什么操作** 创建  **何种类型** 的 **待办事项**， 需要 **哪些人** 接。

**处理人**在 **何时** 进行处理，处理之后的状态变化

待办事项的来源是由相关的功能操作之后，自动生成相关的记录

```
开陈列协议 -> 如果需要复核，就生成一条记录 —> 1.自动通知审核陈列协议的人员 2. 可以主动查看到
```

对于该条待办事项，应该由相关条件，来判断其**状态**（待处理、已完成）。可能存在多个人看到这样的记录，然后进行处理，就有并发的问题情况，或者覆盖修改的情况





不同的事件类型，进行不同的路由跳转，那么应该为全局的公共方法调用。另外，对于路由跳转来说，应该也进行抽离为公共方法，保证对于路由跳转的时候，页面所携带的参数是清晰明确的

## 2.2. 通知功能设计

**谁** 在 **何时** 创建 **通知**， 需要 **哪些人** 接收，**接收人** 是否 **已读**。

- 通知公告栏：全公司的人都能收到，（简易版设置所有人都收到，不设置公司通知栏）
- 通知到部门/个人：指定人群收到

```
系统自动定时创建：如库存预警等
业务流创建：
人工创建：人工创建消息，选择相关的对象接收
```

# 3 系统功能数据表设计

## 3.1 消息类型

```js
{
	displayReview： {	// 陈列协议复核		
		displaySign // 签约
		displayKeep	// 续签
		displayMaintain	// 维护
		displayFdSeller	// 访单业务员
		displayFdSender	 // 访单送货员
		displayCxGive	// 车销
	}，
    
    
}
```

## 消息表 msg_queue

分区create_time

| 字段         | 中文                  | 类型         | 备注 |
| ------------ | --------------------- | ------------ | ---- |
| msg_id       | 自增                  |              |      |
| company_id   | 公司id                |              |      |
| msg_class    | 待办、通知            | todo、notice |      |
| msg_type     | 待办 父级             |              |      |
| msg_sub_type | 待办 子级             |              |      |
| creater_id   | 创建人，业务员id、sys |              |      |
| create_time  |                       |              |      |
| msg_content  | 内容                  | jsonb        |      |
| deal_time    | 处理时间              |              |      |
| deal_worker  | 处理人                |              |      |

## 消息订阅表 msg_subscribe

| 字段         | 中文     | 类型 | 备注             |
| ------------ | -------- | ---- | ---------------- |
| company_id   |          |      | 四个字段组合主键 |
| msg_type     | 消息类型 |      |                  |
| msg_sub_type |          |      |                  |
| worker_id    | 员工id   |      |                  |

## 消息订阅表 msg_subscribe_role

| 字段         | 中文     | 类型 | 备注             |
| ------------ | -------- | ---- | ---------------- |
| company_id   |          |      | 四个字段组合主键 |
| msg_type     | 消息类型 |      |                  |
| msg_sub_type |          |      |                  |
| role_id      | 角色id   |      |                  |

## 消息通知状态表 msg_read

分区read_time

| 字段       | 中文 | 类型 | 备注 |
| ---------- | ---- | ---- | ---- |
| company_id |      |      |      |
| msg_id     |      |      | 索引 |
| worker_id  |      |      |      |
| read_time  |      |      |      |











