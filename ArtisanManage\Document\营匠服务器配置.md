# 营匠服务配置步骤

> 在centos 7.4上使用jexus 部署 asp.net core 项目
>
> https://blog.csdn.net/soband_xiang/article/details/80365898

> Jexus 在centos上配置 https 和 gzip（包括1 jexus设置 2..net core代码修改 3.http跳转https 4.开启gzip）
>
> https://blog.csdn.net/soband_xiang/article/details/80757718
>
> 
>
> https://www.cnblogs.com/nsky/p/10386460.html   可以参考看

切换到根目录（后续部署文件也要放到此目录下，提前切换目录）

```
cd /
```

# **1 安装 .net sdk**

```
sudo rpm -Uvh https://packages.microsoft.com/config/rhel/7/packages-microsoft-prod.rpm
```

```
sudo yum update
sudo yum install libunwind libicu
sudo yum install dotnet-sdk-3.1
```

**如果上面的命令执行失败，需要进行切换源操作。第三方的镜像站中均已移除CentOS 8的源，Centos 8版本已停止更新相应依赖导致的，下载新的yum源即可搞定。 切换完之后，重新执行上述命令。**

```
1、SSH连接到你的CentOS 8云服务器
2、备份之前的repo文件，命令：
mv /etc/yum.repos.d /etc/yum.repos.d.bak
3、创建源文件目录，命令：
mkdir -p /etc/yum.repos.d
4、下载新的yum源，分别执行以下2条命令：
curl https://mirrors.aliyun.com/repo/Centos-vault-8.5.2111.repo > /etc/yum.repos.d/Centos-vault-8.5.2111.repo
curl https://mirrors.aliyun.com/repo/epel-archive-8.repo > /etc/yum.repos.d/epel-archive-8.repo
```

# **2 发布并运行.net core 项目**

## 2.1 创建发布目录

```
cd /
mkdir -p var/www/xxxx    xxx 为自定义命名 此处以 artisan 为案例
mkdir -p var/www/artisan
```

后续可以通过如下命令直接进入该目录

```
cd /var/www/artisan
```

## 2.2 上传项目

需要将项目构建后，将本机bin目录中对应的文件拷贝至/var/www/artisan 这个目录中

```
eg: \artisan-manage\ArtisanManage\bin\Debug\netcoreapp3.1
```

需要修改的配置信息appsettings.json (依据项目情况而定)

```
！！！！！还有其他项目信息配置需要修改例如 UseJexus=1的问题   "IsCentos8":"true"！！！！

jwt项目需要参考 7 Jwt配置
```

```
"ConnectionString":{
    "Postgresql": "xxx",
    "Report": "xxxx"
}



{
  "ConnectionString": {

    "DefaultConnection": "server=localhost;port=3306;database=t_saas_common;user id=root;password=*********", 
  
 //   "Postgresql": "server=*************;port=5432;database=artisan_001;user id=root;password=************************",
    "Postgresql": "server=*************;port=5432;database=artisan_001;user id=root;password=******************;MaxPoolSize=300;",
    "Report": "server=************;port=5432;database=artisan_001;user id=root;password=**********************;MaxPoolSize=300;",
    "center": "server=*************;port=5432;database=artisan_001;user id=postgres;password=********************;MaxPoolSize=300;"

  },
"RedisConnectionString": { //redis连接
    "Connection": "r-2zedctlp8py8vllrrppd.redis.rds.aliyuncs.com:6379,password=19liangpin_, abortConnect=false", //外网
    // "Connection": " r-2zedctlp8py8vllrrp.redis.rds.aliyuncs.com:6379,password=19liangpin_, abortConnect=false",//内网
    "InstanceName": "19liangpin_Redis"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "General": {
    "LogDatabase": "server=************;port=5432;database=artisan_001;user id=root;password=*********;MaxPoolSize=300;",
    "LogSqlLevel": "2",
    "WebRootUrl": "https://www.yingjiang.co",
    "RunByJexus": "1", //1:jexus 0:by dotnet itself
    // Following work when RunByJexus=0
    "SslPfxFile": "2392139_www.19liangpin.com.pfx",
    "SslPfxPwd": "0m8v8r50",
    //"ListenHost": "http://*************:58277"
    "ListenHost":"http://0.0.0.0:80",
   "IsCentos8":"true"
    //"ListenHost": "http://localhost:5000"
  },
  "AllowedHosts": "*",
  "Service": {
    "Name": "AppAPI"
  },
  "Swagger": {
    "IsActive": true,
    "Version": "v1.0",
    "Description": ""
  }
}
```

```
外层wwwroot也需要进行拷贝
AppApi.xml
```

## 2.3 启动项目

需要进入var/www/artisan此文件夹后，在进行donet  xxx.dll命令

```
dotnet ArtisanManage.dll
```

> -bash: dotnet: command not found： 报错
>
> ```
> 方案一：sudo ln -sf /usr/share/dotnet/dotnet /usr/local/bin/dotnet   无效
> ```
>
> ```
> 方案二：https://blog.csdn.net/mls805379973/article/details/115307065   （执行第二条报错sudo: dpkg: command not found，临时放弃）
> ```
>
> ```
> 方案三：dnf install dotnet-sdk-3.1 成功  https://q.cnblogs.com/q/128970/   
> ```

# 3 **jexus**

## 3.1 安装

```
curl https://jexus.org/release/x64/install.sh|sudo sh
```

## 3.2 配置default文件

```
cd /usr/jexus/siteconf
```

这里已经有一个默认的名为default文件，这个文件对应的是一个网站，如果有多个网站，就可以设置多个文件。用vim 命令 修改default 文件。（也可以用mv default mysite 命令修改该文件名，或者新创建一个文件 vi mysite, 一般如果只有一个网站且使用80端口就用默认就好了)

```
vi default 
```

添加以下内容。artisan 为自定义  AppHost 需要进行配置

```
######################
# Web Site: Default 
########################################

port=80
root=/ /var/www/artisan/
hosts=*    #OR your.com,*.your.com

# User=www-data

# AspNet.Workers=2  # Set the number of asp.net worker processes. Defauit is 1.

# addr=0.0.0.0
# CheckQuery=false
NoLog=true
 AppHost={cmd=dotnet ArtisanManage.dll; root=/var/www/artisan/; port=0}
#   ##############################这两个仅供参考是注释内容，可以使用上述，使用时记得删除##############
#        AppHost={cmd=dotnet ArtisanManage.dll; root=/var/www/artisan/; port=5000}
#        AppHost={CmdLine=dotnet /var/www/artisan/ArtisanManage.dll;AppRoot=/var/www/ArtisanManage;Port=0}
  #####################################################################################################
# NoFile=/index.aspx
# Keep_Alive=false
# UseGZIP=false

# UseHttps=true
# ssl.certificate=/x/xxx.crt  #or pem
# ssl.certificatekey=/x/xxx.key
# ssl.protocol=TLSv1.0 TLSv1.1 TLSv1.2
# ssl.ciphers=ECDHE-RSA-AES256-GCM-SHA384:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4:!DH:!DHE 
# ResponseHandler.Add=Strict-Transport-Security:max-age=15768000  #for HSTS

# DenyFrom=*************, 192.168.1.*, ***********/24
# AllowFrom=192.168.*.*
# DenyDirs=~/cgi, ~/upfiles
# indexes=myindex.aspx

# Deny asp ...
rewrite=^/.+?\.(asp|cgi|pl|sh|bash)(\?.*|)$      /.deny->$1
rewrite=.*/editor/.+                             /.deny->editor
# reproxy=/bbs/ http://*************/bbs/
# host.Redirect=abc.com www.abc.com  301
# ResponseHandler.Add=myKey:myValue
ResponseHandler.Add=X-Frame-Options:SAMEORIGIN

# Jexus php fastcgi address is '/var/run/jexus/phpsvr'
#######################################################
# fastcgi.add=php|socket:/var/run/jexus/phpsvr

# php-fpm listen address is '127.0.0.1:9000'
############################################
# fastcgi.add=php|tcp:127.0.0.1:9000
```

```
进入到default创建index.html 文件 !!!! 可能不需要
```

### 3.3 启动jws

 在阿里云的安全规则里把80端口放行，现在在你的电脑上输入阿里云的外网ip就能打开网页了。启动失败可能是防火墙问题或者appsettings.json配置问题（UseJexus=1的问题）。

```
/usr/jexus/./jws start
```

## 3.4 重启jws

```
/usr/jexus/./jws restart 
```

# 3.5 开机启动

暂未配置

# 4 防火墙

（如果需要，一般情况不用配置，多数情况是两个配置文件的问题appsettings.json 和default文件 jexus）

## 4.1 查看80端口是否开启

```
firewall-cmd --query-port=80/tcp
```

##  4.2 开启80端口

```
firewall-cmd --zone=public --add-port=80/tcp --permanent

firewall-cmd --add-port=80/tcp --permanent

firewall-cmd --reload （重启防火墙，就可以使用了）
```

##  4.3  关闭80端口

```
firewall-cmd --remove-port=80/tcp --permanent  # --permanent 永久生效，没有此参数重启后失效
```

## 4.4 FirewallD is not running 处理

1. 查看firewalld状态，发现当前是dead状态，即防火墙未开启。

```
systemctl status firewalld
```

2. 开启防火墙，没有任何提示即开启成功。

```
systemctl start firewalld
```

防火墙关闭

```
systemctl stop firewalld
```

# 5 配置 https 和 gzip

按需继续完善

# 6 云打印配置

如果安装完，打印出现错误SSL的错误， 可能配置文件出错

> 参考文章
>
> https://blog.csdn.net/soband_xiang/article/details/123905689
>
> http://t.zoukankan.com/gaobing-p-14688655.html

## 6.1 gdiplus插件

```
yum install autoconf automake libtool
yum install freetype-devel fontconfig libXft-devel

yum install libjpeg-turbo-devel libpng-devel giflib-devel libtiff-devel libexif-devel
centos8 上面有问题
		1.sudo dnf install giflib giflib-devel zlib-devel libpng-devel motif-devel libXtst-devel 失败
		2. dnf install libjpeg-turbo-devel libpng-devel giflib-devel libtiff-devel libexif-devel 失败
		3. 使用下方Centos8源码安装libgdiplus 
	
    
yum install glib2-devel cairo-devel
yum install git
git clone https://github.com/mono/libgdiplus
cd libgdiplus
yum -y install ftp
./autogen.sh
yum -y install gcc automake autoconf libtool make
yum -y install gcc gcc-c++
make
make install
ln -s /usr/local/lib/libgdiplus.so /usr/lib64/libgdiplus.so
ln -s /usr/local/lib/libgdiplus.so /usr/libgdiplus.so
```

## 6.2 Centos8源码安装libgdiplus

```
dnf -y install   automake autoconf libtool make gcc gcc-c++
dnf --enablerepo=PowerTools -y install giflib-devel
dnf --enablerepo=PowerTools -y install libexif-devel
dnf -y install bison pkgconfig glib2-devel gettext make libpng-devel libjpeg-devel libtiff-devel libexif-devel giflib-devel libX11-devel freetype-devel fontconfig-devel  cairo-devel fribidi-devel
```

## 6.3 安装字体

centos中默认没有中文字体库，

### 6.3.1 拷贝字体

在 /usr/share/fonts/下创建一个文件夹（名字随便），将windows/fonts下的需要字体文件拷贝过来，

### 6.3.2 刷新字体

执行fc-cache可以刷新系统的字体，fc-list可以查看字体列表

如果fc-cache报错，需要安装这个工具

```
yum -y install fontconfig
```

# 7 Jwt配置

```
Unhandled exception. System.IO.FileNotFoundException: Could not load file or assembly 'Microsoft.AspNetCore.Authentication.JwtBearer, Version=********, Culture=neutral, PublicKeyToken=adb9793829ddae60'. The system cannot find the file specified.

File name: 'Microsoft.AspNetCore.Authentication.JwtBearer, Version=********, Culture=neutral, PublicKeyToken=adb9793829ddae60'
   at ArtisanManage.Services.Jwt.AuthConfigure.ConfigureJwt(IServiceCollection services, IConfiguration configuration)

```

```
Unhandled exception. System.ArgumentNullException: Value cannot be null. (Parameter 'value')
   at System.Boolean.Parse(String value)
   at ArtisanManage.Services.Jwt.AuthConfigure.ConfigureJwt(IServiceCollection services, IConfiguration configuration) in F:\yingjiang\ArtisanManage\artisan-manage\ArtisanManage\Services\Jwt\AuthConfigure.cs:line 13
   at ArtisanManage.Startup.ConfigureServices(IServiceCollection servi
```



```
相关dll依赖需要上传
```

```
  需要在配置文件中添加信息
  "Authentication": { 
    "JwtBearer": {
      "IsEnabled": "true",
      "SecurityKey": "YingJiangBackstage168",
      "Issuer": "YingJiangAdmin",
      "Audience": "YingJiangAdmin",
      "Expiration": 60 //token过期时间 （单位：分钟）
    }
  },
```

