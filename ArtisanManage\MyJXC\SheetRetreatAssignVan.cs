﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowRetreatAssignVan:SheetRowBase
    {
        public SheetRowRetreatAssignVan()
        {

        }
        [SaveToDB(false)][FromFld(false)] public override int inout_flag { get; set; } = 0;
        [SaveToDB(false)][FromFld(false)] public override int row_index { get; set; } = 0;

        [FromFld("op_id")]public override string sheet_id { get; set; }
        [SaveToDB][FromFld] public string item_id { get; set; }

        [FromFld("ip.item_name")]public string item_name { get; set; }
        [SaveToDB][FromFld] public string unit_no { get; set; }

        [SaveToDB][FromFld] public decimal unit_factor { get; set; } = 1;

        [SaveToDB][FromFld] public decimal quantity { get; set; }

        [SaveToDB][FromFld] public decimal sheet_order_quantity { get; set; }
        [SaveToDB][FromFld] public string batch_id { get; set; } = "0";
        [SaveToDB][FromFld] public string branch_position { get; set; } = "0";
        [SaveToDB][FromFld] public string branch_id { get; set; }
        public string from_branch_position { get; set; }
        public string to_branch_position { get; set; }
        [FromFld("itb.batch_no")] public string batch_no { get; set; }
        [FromFld("itb.produce_date")] public string produce_date { get; set; }
        [SaveToDB][FromFld] public string sale_order_sheet_id { get; set; } = "";

    }
    public class SheetRetreatAssignVan : SheetBase<SheetRowRetreatAssignVan>
    {
        [SaveToDB("op_id")][IDField][FromFld("op_id")] public override string sheet_id { get; set; }
        [SaveToDB("op_no")][FromFld("case when t.op_no is  null then 'ZC'||t.op_id::text else t.op_no end")] public override string sheet_no { get; set; } = "";
        [SaveToDB][FromFld] public string op_type { get; set; }
        [SaveToDB][FromFld] public string oper_id { get; set; }
        [FromFld("op.oper_name")] public string oper_name { get; set; }
        [SaveToDB][FromFld] public string move_sheet_id { get; set; }
        [SaveToDB][FromFld] public string senders_id { get; set; }
        [SaveToDB][FromFld] public string senders_name { get; set; }
        [SaveToDB(false)][FromFld(false)] public override string red_sheet_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string maker_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_time { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string approve_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string submit_time { get; set; } = "";

        [SaveToDB(false)][FromFld(false)] public override string maker_name { get; set; } = "";

        [FromFld("otd.sale_order_sheets_id")] public string sale_order_sheets_id { get; set; }
        public string sale_order_sheets_no { get; set; }
        [FromFld("sm.sheet_no")] public string move_sheet_no { get; set; }
        [SaveToDB][FromFld] public string from_branch { get; set; }
        [FromFld("v.branch_name")] public string to_van_name { get; set; }
        [SaveToDB][FromFld] public string to_van { get; set; }
        [FromFld("f.branch_name")] public string from_branch_name { get; set; }

        [SaveToDB][FromFld("case when t.approver_id is not null then t.approver_id else sm.approver_id end")] public override string approver_id { get; set; } = "";
        [FromFld("case when t.approver_id is not null then  approver.approver_name else mover.approver_name end")] public override string approver_name { get; set; } = "";
        [SaveToDB][FromFld("to_char( case when t.approve_time is not null then t.approve_time else sm.approve_time end , 'yyyy-MM-dd hh24:mi:ss' ) as approve_time")] public override string approve_time { get; set; } = "";

        [SaveToDB][FromFld("case when t.move_stock is not null then t.move_stock::text else otd.move_stock::text end")] public string move_stock { get; set; }
        [FromFld("otd.move_sheet_ids")] public string move_sheet_ids { get; set; } = "";

        public SheetRetreatAssignVan(LOAD_PURPOSE loadPurpose) : base("op_move_to_van_main", "op_move_to_van_row", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_RETREAT_ASSIGN_VAN;
            MainLeftJoin = @" 
                    LEFT JOIN info_branch f ON T.from_branch = f.branch_id AND f.company_id = ~COMPANY_ID
	                LEFT JOIN info_branch v ON T.to_van = v.branch_id AND v.company_id = ~COMPANY_ID
                    LEFT JOIN ( SELECT oper_id, oper_name  FROM info_operator WHERE company_id = ~COMPANY_ID ) op on op.oper_id = t.oper_id
	                LEFT JOIN ( SELECT oper_id, oper_name AS approver_name FROM info_operator WHERE company_id = ~COMPANY_ID ) approver ON T.approver_id = approver.oper_id 
                    LEFT JOIN 
                    (
                            SELECT op_id , string_agg(DISTINCT so.move_stock::text,',') move_stock,string_agg(sale_order_sheet_id::text,',') sale_order_sheets_id ,jsonb_agg(DISTINCT jsonb_build_object(COALESCE(sale_order_row_branch_id::text,'-1'),move_sheet_id::text||'_'||smm.sheet_no::text))   move_sheet_ids
                            FROM op_move_to_van_detail d 
                            left join sheet_move_main smm on smm.company_id =~COMPANY_ID  and smm.sheet_id = d.move_sheet_id
                            LEFT JOIN sheet_status_order so on so.company_id = d.company_id and d.sale_order_sheet_id = so.sheet_id
                            WHERE d.company_id = ~COMPANY_ID GROUP BY op_id
                    ) otd on otd.op_id = t.op_id
	                LEFT JOIN sheet_move_main sm on sm.company_id = ~COMPANY_ID and sm.sheet_id = t.move_sheet_id
	                LEFT JOIN ( SELECT oper_id, oper_name AS approver_name FROM info_operator WHERE company_id = ~COMPANY_ID ) mover ON sm.approver_id = mover.oper_id 
                ";
            DetailLeftJoin = $@" left join info_item_prop ip on ip.company_id= t.company_id and ip.item_id = t.item_id
left join info_item_batch itb on itb.company_id =t.company_id and itb.batch_id = t.batch_id";
        }


     
        class CInfoForApprove : CInfoForApproveBase
        {
            public List<dynamic> OrderSheets = new List<dynamic>();
            public string PreviousVanInfo = "";
        }
        public override string GetOtherSaveSQL()
        {
            string sqlDetail = "";
            //List<string> moveSheetIds = move_sheet_ids.IsInvalid()?new List<string>():move_sheet_ids.Split(',').ToList();
            List<string> saleOrderSheetIds = sale_order_sheets_id.IsInvalid()?new List<string>():sale_order_sheets_id.Split(",").ToList();
            foreach(string sheetId in saleOrderSheetIds)
            {
                if (move_sheet_id.IsValid())
                {
                    //foreach (string moveSheetId in moveSheetIds)
                    //{
                        sqlDetail += $@"insert into op_move_to_van_detail (op_id,      oper_id,     company_id,  move_sheet_id, sale_order_sheet_id)
                           values ('@op_id', '{OperID}', '{company_id}', {move_sheet_id}, '{sheetId}');";
                    //}
                }
                else
                {
                    sqlDetail += $@"insert into op_move_to_van_detail (op_id,      oper_id,     company_id, sale_order_sheet_id)
                           values ('@op_id', '{OperID}', '{company_id}', '{sheetId}');";
                }
                
            }
           
                
            if (sheet_id != "" && move_sheet_id.IsValid())
            {
                foreach (string sheetId in saleOrderSheetIds)
                {
                    //foreach (string moveSheetId in moveSheetIds)
                    //{
                        sqlDetail +=$@"update op_move_to_van_detail set move_sheet_id = {move_sheet_id} where company_id = '{company_id}' and sale_order_sheet_id = '{sheetId}' and op_id = {sheet_id};";
                    //}

                }
            }
            return sqlDetail;
        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            string sql = "";
            if (sale_order_sheets_id != "")
            {
                sql = $@"SELECT sm.*,sso.order_status,sso.receipt_status,sso.sale_sheet_id FROM sheet_sale_order_main sm 
LEFT JOIN sheet_status_order sso on sm.company_id = sso.company_id and sm.sheet_id = sso.sheet_id
WHERE sm.company_id = {company_id} and sm.sheet_id in ({sale_order_sheets_id});";
                QQ.Enqueue("order_sheets", sql);
                /*SELECT  string_agg(od.op_id::text,',') op_ids   FROM op_move_to_van_detail od 
                        LEFT JOIN op_move_to_van_main om on od.company_id = om.company_id and od.op_id = om.op_id 
                        WHERE od.company_id = {company_id} and od.sale_order_sheet_id in ({sale_order_sheets_id}) and om.red_flag is null*/
                sql = $@"
                        SELECT  string_agg(op_id::text,',') op_ids   FROM(
                                SELECT * FROM (
                                    SELECT om.*,od.sale_order_sheet_id,sm.sheet_no sale_order_sheet_no,sm.red_flag sale_order_sheet_status,row_number() over(partition by od.sale_order_sheet_id order by om.happen_time desc) rn 
                                    FROM op_move_to_van_detail od
                                    LEFT JOIN op_move_to_van_main om  on od.company_id = od.company_id and od.op_id = om.op_id 
                                    LEFT JOIN sheet_sale_order_main sm on od.company_id = sm.company_id and od.sale_order_sheet_id = sm.sheet_id 
                                    WHERE od.company_id = {company_id} and od.sale_order_sheet_id in ({sale_order_sheets_id}) and om.red_flag is null and om.op_type in('2v','v2v')
                                )t WHERE t.rn =1
                        )tt;";
                QQ.Enqueue("previous_vans", sql);

            }

        }

        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (sqlName == "order_sheets")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                if (records.Count > 0)
                {
                    foreach (dynamic rec in records)
                    {
                        info.OrderSheets.Add(rec);
                        string saleSheetId = rec.sale_sheet_id;
                        string orderStatus = rec.order_status;
                        if (red_flag.IsInvalid())
                        {
                            if (saleSheetId != "")
                            {
                                info.ErrMsg += $"{rec.sheet_no}已转单无法回撤;";
                            }
                            if (orderStatus == "" || orderStatus == "xd" || orderStatus == "dd")
                            {
                                info.ErrMsg += $"{rec.sheet_no}未装车无法回撤;";
                            }
                            if (orderStatus == "pzc")
                            {
                                info.ErrMsg += $"{rec.sheet_no}涉及的装车单未审核,无法回撤;";
                            }
                        }
                        
                    }
                }

            }else if(sqlName == "previous_vans")
            {
                dynamic previousVans = CDbDealer.Get1RecordFromDr(dr, false);
                info.PreviousVanInfo = previousVans.op_ids;
            }

        }
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
            return "OK";
        }
        protected override async Task<CInfoForApproveBase> GetInfoForApprove(CMySbCommand cmd)
        {

            SQLQueue QQ = new SQLQueue(cmd);
            if (sheet_id != "")
            {
                if (!FIXING_ARREARS)
                {
                    string check_sql = $"select approve_time from {MainTable} where op_id={sheet_id} and company_id = {company_id}";
                    QQ.Enqueue("check_sheet", check_sql);
                }
            }

            GetInfoForApprove_SetQQ(QQ);
            string errMsg = "";
            if (QQ.Count > 0)
            {
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                try
                {
                    while (QQ.Count > 0)
                    {
                        string tbl = QQ.Dequeue();
                        if (tbl == "check_sheet")
                        {
                            dynamic checkSheet = CDbDealer.Get1RecordFromDr(dr, false);
                            if (checkSheet != null && checkSheet.approve_time != "")
                            {
                                errMsg = "单据已审核过,不能再次审核";
                                break;
                            }
                        }
                        else
                        {
                            GetInfoForApprove_ReadData(dr, tbl, false);
                            if (InfoForApprove != null && InfoForApprove.ErrMsg != "")
                            {
                                errMsg = InfoForApprove.ErrMsg;
                                break;
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    errMsg = "读取数据失败";
                    MyLogger.LogMsg($"in GetInfoForApprove. error:${e.Message}, ${e.StackTrace}", company_id, "approve");
                }
                QQ.Clear();
            }
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            InfoForApprove.ErrMsg = errMsg;
            return InfoForApprove;
        }
        protected override string GetApproveSQL(CInfoForApproveBase info)
        {

            string sqlOrderStatus = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"
                                    update sheet_status_order
                                    set order_status = case when print_time is not null then 'dd' else 'xd' end , senders_id = null , senders_name = null,van_id = null,move_stock = null,assign_van_id = null 
                                    where company_id = {company_id} and sheet_id = {sheetId};");
            string sqlOrderStock = "";
            if (move_stock.ToLower() == "true"&&red_flag.IsInvalid())
            {
                foreach (var row in SheetRows)
                {
                    var order_qty = row.sheet_order_quantity * row.unit_factor;
                    sqlOrderStock += $@"update stock set sell_pend_qty=sell_pend_qty+({order_qty}) where company_id={company_id} and branch_id={row.branch_id} and branch_position={row.branch_position} and batch_id = {row.batch_id} and item_id={row.item_id};";
                }
            }

            string sql =  sqlOrderStatus + sqlOrderStock;

            return sql;
        }
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.from_branch + "_" + this.to_van + "_" + this.move_stock;
            foreach(var row in SheetRows)
            {
                res += row.item_id + "_" + row.quantity;
            }
            return res;
        }
     
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info)
        {
            CInfoForApprove info1 = (CInfoForApprove)info;
            string sql = "";
            if (red_flag.IsValid())
            {
                string preOpVanInfoSql = $@"
                    SELECT * FROM 
                    (
                        SELECT om.*,od.sale_order_sheet_id, dd.sheet_id deliver_sheet_id,dd.happen_time deliver_time,  row_number() over(partition by od.sale_order_sheet_id order by om.happen_time desc) rn 
                        FROM op_move_to_van_detail od 
                        LEFT JOIN op_move_to_van_main om on od.company_id = om.company_id and od.op_id = om.op_id 
                        LEFT JOIN (SELECT sd.* FROM sheet_delivery_detail sd 
                                    LEFT JOIN sheet_delivery_main sm on sd.company_id = sm.company_id and sd.sheet_id = sm.sheet_id 
                                    where sd.company_id = {this.company_id} and sm.red_flag is null and sm.approve_time is not null 
                                    ) dd on dd.company_id = od.company_id and dd.op_id = od.op_id
                        WHERE od.company_id ={this.company_id} and od.sale_order_sheet_id in ({sale_order_sheets_id}) and om.red_flag is null and om.op_type <> 'retreat'
                    )t
                    WHERE t.rn =1";
                dynamic preOpVanInfo = await CDbDealer.GetRecordsFromSQLAsync(preOpVanInfoSql, cmd);
                foreach (var row in preOpVanInfo)
                {
                    if (row.deliver_sheet_id != "" && DateTime.Parse(row.deliver_time) > DateTime.Parse(happen_time))
                    {
                        info1.ErrMsg = "原装车单已经发货,无法回撤";
                    }
                }
                string preOpIDs = info1.PreviousVanInfo;
                string toVanID = "";
                foreach (var sheet in info1.OrderSheets)
                {
                    if (toVanID != "") break;
                    else toVanID = sheet.branch_id;
                }
                sql += @$"UPDATE op_move_to_van_detail set retreat_id = null
             WHERE company_id = {this.company_id} and sale_order_sheet_id in ({sale_order_sheets_id}) and op_id <>{sheetID}  and retreat_id is not null and op_id in({preOpIDs}) ;";
                foreach (dynamic sht in preOpVanInfo)
                {
                    string orderStatus = "zc";
                    if (sht.deliver_sheet_id != "") orderStatus = "fh";
                    sql += $@"UPDATE sheet_status_order 
                 set order_status = '{orderStatus}' , senders_id = '{sht.senders_id}',senders_name='{sht.senders_name}',van_id = {sht.to_van},move_stock={sht.move_stock},assign_van_id ={sht.op_id}
                 where company_id ={this.company_id} and sheet_id = {sht.sale_order_sheet_id};";
                }
                foreach (var row in SheetRows)
                {
                    sql += $@"UPDATE op_move_to_van_row 
                 set retreat_qty = COALESCE(retreat_qty,0)-{row.quantity}
                 WHERE company_id ={this.company_id}  and op_id in ({preOpIDs}) and op_id <>{sheetID} and item_id = {row.item_id} and unit_no = '{row.unit_no}' and retreat_qty is not null;";
                    if (move_stock.ToLower() == "true" && move_sheet_id != ""&&row.sale_order_sheet_id.IsValid())
                    {
                        var order_qty = row.sheet_order_quantity * row.unit_factor;
                        sql += $@"update stock set sell_pend_qty=COALESCE(sell_pend_qty,0)-({order_qty}) where company_id={company_id} and branch_id={toVanID} and item_id={row.item_id} and batch_id ='{row.batch_id}' and branch_position='{row.branch_position}';";

                    }

                }
            }
            else
            {
                var previousOpIDs = info1.PreviousVanInfo;
                //string sql = "";
                if (previousOpIDs != "")
                {
                    //sql += $@"UPDATE op_move_to_van_main set have_retreat = true WHERE company_id = {company_id} and op_id in ({previousOpIDs});";
                    //AddExecSQL($"UPDATE op_move_to_van_main set have_retreat = true WHERE company_id = {company_id} and op_id in ({previousOpIDs});");

                    sql += sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"
                                    update op_move_to_van_detail
                                    set retreat_id  = {sheetID}
                                    where company_id = {company_id} and sale_order_sheet_id = {sheetId} and op_id in ({previousOpIDs});");
                    foreach (var row in SheetRows)
                    {
                        sql += $@"update op_move_to_van_row set retreat_qty =COALESCE(retreat_qty,0)+ {row.quantity} where company_id = {company_id} and op_id in ({previousOpIDs}) and item_id ={row.item_id} and unit_no = '{row.unit_no}' and branch_id = {row.branch_id} and branch_position = {row.branch_position} and batch_id = {row.batch_id} and sale_order_sheet_id = {row.sale_order_sheet_id};";
                    }

                }
            }
            
            cmd.CommandText = sql;
            if(cmd.CommandText != "")
            {
                await cmd.ExecuteNonQueryAsync();
            }
        }
        protected override async Task<string> BeforeRed(CMySbCommand cmd, string sheetID, string rederID,string redBrief,CInfoForApproveBase info)
        {
            string err = "";
            if (move_stock == "true" && move_sheet_id != "")
            {
                SheetMove moveSheet = new SheetMove(LOAD_PURPOSE.APPROVE);
                err = await moveSheet.Red(cmd, this.company_id, move_sheet_id, rederID, redBrief, false);

            }
            return err;
        }
        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd,  CInfoForApproveBase info)
        {
            string err = "";
         
            if (move_stock.ToLower() == "true" && SheetRows.Count() > 0)
            {
                Dictionary<string, List<SheetRowRetreatAssignVan>> sheetRetreatAssignVanRows = new Dictionary<string, List<SheetRowRetreatAssignVan>>();
                foreach (SheetRowRetreatAssignVan row in SheetRows)
                {
                    if (row.item_id == "16666242")
                    {

                    }
                    if (row.quantity == 0) continue;
                    if (row.branch_id == from_branch) continue;
                    
                    if (sheetRetreatAssignVanRows.ContainsKey(row.branch_id))
                    {
                        sheetRetreatAssignVanRows[row.branch_id].Add(row);
                    }
                    else
                    {
                        sheetRetreatAssignVanRows.Add(row.branch_id, new List<SheetRowRetreatAssignVan>() { row });
                    }
                }
                foreach (KeyValuePair<string, List<SheetRowRetreatAssignVan>> kv in sheetRetreatAssignVanRows)
                {
                    var sheetMove = new SheetMove(LOAD_PURPOSE.SHOW);
                    sheetMove.from_branch_id = from_branch;
                    sheetMove.from_branch_name = from_branch_name;
                    sheetMove.to_branch_id = kv.Key;
                    sheetMove.SheetRows = JsonConvert.DeserializeObject<List<SheetRowMove>>(JsonConvert.SerializeObject(kv.Value));
                    sheetMove.company_id = company_id;
                    sheetMove.happen_time = CPubVars.GetDateText(DateTime.Now);
                    sheetMove.maker_id = OperID;
                    sheetMove.make_time = CPubVars.GetDateText(DateTime.Now);
                    sheetMove.submit_time = CPubVars.GetDateText(DateTime.Now);
                    sheetMove.OperKey = OperKey;
                    sheetMove.OperID = OperID;
                    sheetMove.SaleOrderSheetIDs = sale_order_sheets_id;
                    sheetMove.SaleOrderSheetNos = sale_order_sheets_no;
                    sheetMove.assign_van = Assign_Van.ASSIGN_VAN;
                    sheetMove.sheet_id = "";
                    sheetMove.sheet_no = "";
                    err = await sheetMove.SaveAndApprove(cmd, false);
                    if (err != "") break;
                    move_sheet_id = sheetMove.sheet_id;
                    move_sheet_no = sheetMove.sheet_no;
                    if (move_sheet_ids == "") move_sheet_ids = move_sheet_id;
                    else move_sheet_ids += ","+ move_sheet_id;
                }
            }
             
            return err; 
        }

        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting); 
            
        }

    }
}
