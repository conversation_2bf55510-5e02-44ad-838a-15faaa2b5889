﻿@page
@model ArtisanManage.Pages.BaseInfo.PlansSelectModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <link href="~/css/component.css" rel="stylesheet" />
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        window.index=@Html.Raw(Model.Index);
        var RowIndex = -1;
       
    	$(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)

            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                console.log(args);
                if (args.datafield == "plan_name") {
                    if (ForSelect) {
                        // $("#popItem").jqxWindow('setContent', '<iframe src="ClientEdit?operKey=' + g_operKey + '&supcust_id=' + id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                        var plan_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "plan_id");
                        var plan_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "plan_name");
                        var msg = {
                            msgHead: 'PlansSelect', action: 'select', index:window.index, plan_id: plan_id, plan_name: plan_name
                        };
                        window.parent.postMessage(msg, '*');

                    }
                }

              
            });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            QueryData();
    	});
    </script>
</head>

<body style="overflow:hidden">
   

    <style>
        html, body {
            height: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        .dataArea {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            margin: 0;
            width: 100%;
            height: 100%;
        }

            .dataArea > div:first-child {
                width: 100%;
                height: 40px;
            }

            .dataArea > div:last-child {
                display: flex;
                align-items: stretch;
                align-content: stretch;
                width: 100%;
                flex-grow: 1;
            }

                .dataArea > div:last-child > div:first-child {
                    width: 200px;
                }

                .dataArea > div:last-child > div:last-child {
                    flex-grow: 1;
                }
    </style>




    <div style="display:flex;justify-content:space-around;margin-top:20px;">
        <div style="font:35px;font-weight:bold">价格方案列表</div>
    </div>

    <div class="dataArea">
        <div>
            <div>
                <div style="height:5px;">

                </div>
                <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:calc(100% - 80px);"></div>

            </div>
        </div>
    </div>

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

    <partial name="dialog" />


</body>
</html>