﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;

namespace ArtisanManage.Pages.Report
{
    public class BusinessHistoryListsModel : PageQueryModel
    {
        public BusinessHistoryListsModel(CMySbCommand cmd) : base(Services.MenuId.businessHistoryLists)
        {
            this.cmd = cmd;
            this.PageTitle = "经营历程";
            CanQueryByApproveTime = true;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="m.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="m.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                
                //{"supcust_id",new DataItem(){Title="客户/供应商",FldArea="divHead", LabelFld="sup_name",ButtonUsage="list",CompareOperator="=",SqlFld="m.supcust_id",
                //    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where company_id=~COMPANY_ID and (status = '1' or status is null)"}},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客户/供应商",LabelFld="sup_name",Checkboxes=true, ButtonUsage="list",CompareOperator="=",DropDownWidth = "200",SqlFld="m.supcust_id",
                    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where company_id=~COMPANY_ID and (status = '1' or status is null)"}},
                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,ForQuery=false}},
                {"branch_id",new DataItem(){Title="仓库",FldArea="divHead",LabelFld="branch_name",ButtonUsage="list",ForQuery=false,
                    SqlForOptions ="select branch_id as v,branch_name as l from info_branch"}},
                {"sheet_no",new DataItem(){Title="单号编号",FldArea="divHead",  CompareOperator="like"}},
                {"sheet_type",new DataItem(){Title="单据类型",FldArea="divHead",LabelFld="sheet_type_name",ButtonUsage="list",Checkboxes = true,
                    Source = @"[{v:'X',l:'销售单'},{v:'T',l:'退货单'},{v:'XD',l:'销售订单'},{v:'TD',l:'退货订单'},{v:'CG',l:'采购单'},{v:'CT',l:'采购退货单'},
                                {v:'DB',l:'调拨单'},{v:'YK',l:'盘点盈亏单'},{v:'BS',l:'报损单'},{v:'DH',l:'定货会'},{v:'YS',l:'预收款单'},{v:'YF',l:'预付款单'},
                                {v:'SK',l:'收款单'},{v:'FK',l:'付款单'},{v:'ZC',l:'费用支出'},{v:'SR',l:'其他收入'},{v:'',l:'所有'}]",CompareOperator="="}},
                {"byHappenTime",new DataItem(){Title="按交易时间查询",FldArea="divHead",CtrlType="jqxCheckBox",ForQuery=false}},
                 {"sub_id",new DataItem(){Title="支付方式",FldArea="divHead", LabelFld="sub_name",ButtonUsage="list",CompareOperator="=",SqlFld="payway1_id,payway2_id",Checkboxes = true,
                SqlForOptions = "SELECT s.sub_id as v ,s.sub_name as l  FROM cw_subject s WHERE s.company_id =~COMPANY_ID AND sub_type IN ( 'YS', 'YF', 'QT', 'ZC', 'QTSR' ) ORDER BY order_index"}},
                {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Value="1",Label="批发成本",Source = "[{v:'1',l:'批发成本'},{v:'2',l:'加权平均成本'},{v:'3',l:'进价成本'},{v:'4',l:'最近平均进价'}]", CompareOperator="=" }},

            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates = true,
                      HasCheck=true,
                      KeepCheckForQueries=false,
                      IdColumn="sheet_id",
                     Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",new DataItem(){SqlFld="sheet_id",Hidden=true,HideOnLoad = true}},
                       {"sheet_no",     new DataItem(){Title="单号",    Width="100" ,Linkable = true,SqlFld="sheet_no"}},
                       {"sheet_type",     new DataItem(){Title="单据类型号", Hidden = true,   Width="100" ,SqlFld="sheet_type"}},
                       {"sheet_type_name",   new DataItem(){Title="单据类型",    Width="100",
                           SqlFld=@"(case when sheet_type='X' Then '销售单' when sheet_type = 'T' then '退货单'
                                          when sheet_type='XD' Then '销售订单' when sheet_type = 'TD' then '退货订单'
                                          WHEN sheet_type='CG' THEN '采购单' WHEN sheet_type='CT' THEN '采购退货单' 
                                          when sheet_type='DB' Then '调拨单' when sheet_type = 'DH' then '定货会'
                                          when sheet_type='YS' Then '预收款单' when sheet_type = 'YF' then '预付款单'
                                          when sheet_type='FK' Then '付款单' when sheet_type = 'SK' then '收款单'
                                          when sheet_type='YK' Then '盘点盈亏单' when sheet_type = 'BS' then '报损单'
                                         WHEN sheet_type='ZC' THEN '费用支出' WHEN sheet_type='SR' THEN '其他收入' END)"}},
                       {"sup_name",     new DataItem(){Title="供应商/客户",       Width="120",SqlFld="sup_name"}},
                       {"oper_name",    new DataItem(){Title="业务员",     Width="100",SqlFld="oper_name"}},
                       {"happen_time",    new DataItem(){Title="交易时间",  Sortable=true,   Width="150",SqlFld="happen_time"}},
                       {"approve_time",    new DataItem(){Title="审核时间",     Width="150",SqlFld="approve_time"}},
                       {"total_amount",   new DataItem(){Title="总计", CellsAlign="right", Width="80",SqlFld="total_amount",ShowSum=true}},
                       {"now_disc_amount",   new DataItem(){Title="优惠金额", CellsAlign="right", Width="100",SqlFld="now_disc_amount*money_inout_flag", Sortable=true, ShowSum=true}},
                       {"now_pay_amount",   new DataItem(){Title="支付金额", CellsAlign="right", Width="100",SqlFld="now_pay_amount*money_inout_flag",ShowSum=true}},
                       {"make_brief",   new DataItem(){Title="备注", CellsAlign="right", Width="200",SqlFld="make_brief"}},
                       //{"orig_price",   new DataItem(){Title="原价", CellsAlign="right", Width="10%",SqlFld="sd.orig_price",ShowSum=true}},
                       //{"cost_price",   new DataItem(){Title="成本价", CellsAlign="right", Width="10%",SqlFld="sd.cost_price",ShowSum=true}},
                     },
                     QueryFromSQL=@"
FROM
(
  SELECT
   sheet_id,sheet_no,sheet_type,supcust_id,seller_id,happen_time,make_time,approve_time,total_amount, money_inout_flag, now_disc_amount,now_pay_amount,make_brief,company_id,payway1_id,payway2_id,branch_id ,to_branch_id 
from(		SELECT 
            sheet_id,sheet_no,sheet_type,supcust_id,seller_id,happen_time,make_time,approve_time,total_amount,money_inout_flag,now_disc_amount,now_pay_amount, make_brief,company_id, payway1_id,payway2_id,branch_id,CAST(NULL AS INTEGER) AS to_branch_id 
		FROM  
            sheet_sale_main
		WHERE 
             company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL  
        UNION
		SELECT 
            sheet_id,sheet_no,sheet_type,supcust_id,seller_id,happen_time,make_time,approve_time,total_amount,money_inout_flag,now_disc_amount,now_pay_amount,make_brief,company_id,payway1_id,payway2_id ,branch_id,CAST(NULL AS INTEGER) AS to_branch_id 
		FROM 
            sheet_sale_order_main 
		WHERE 
            company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL 
        UNION
		SELECT 
            sheet_id,sheet_no,sheet_type,supcust_id,seller_id,happen_time,make_time,approve_time,total_amount,money_inout_flag,now_disc_amount,now_pay_amount,make_brief,company_id,payway1_id,payway2_id ,branch_id,CAST(NULL AS INTEGER) AS to_branch_id 
		FROM
			sheet_buy_main 
		WHERE
			company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL 

UNION 
		SELECT
			sheet_id,sheet_no,'DB'sheet_type,NULL supcust_id,seller_id,happen_time,make_time,approve_time,~VAR_INVENTORY_SHEET_CONDI  total_amount,NULL money_inout_flag, NULL now_disc_amount,NULL now_pay_amount,make_brief,company_id,'-1'payway1_id,'-1'payway2_id ,from_branch_id branch_id, to_branch_id 
		FROM
			sheet_move_main 
		WHERE
			company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL 
UNION
		SELECT
			sheet_id,sheet_no,sheet_type,null supcust_id,seller_id,happen_time,make_time,approve_time, ~VAR_INVENTORY_SHEET_CONDI total_amount,NULL money_inout_flag,NULL now_disc_amount,NULL now_pay_amount,make_brief,company_id,'-1'payway1_id,'-1'payway2_id ,branch_id,CAST(NULL AS INTEGER) AS to_branch_id 
		FROM
			sheet_invent_change_main 
		WHERE
			company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL 

UNION
		SELECT
			 sheet_id,sheet_no,sheet_type,supcust_id,getter_id seller_id,happen_time,make_time,approve_time,sheet_amount total_amount,money_inout_flag,now_disc_amount,now_pay_amount,make_brief,company_id,payway1_id,payway2_id ,NULL branch_id,CAST(NULL AS INTEGER) AS to_branch_id 
		FROM
			sheet_get_arrears_main 
		WHERE
			company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL 
UNION
		SELECT
			sheet_id,sheet_no,sheet_type,supcust_id,getter_id seller_id,happen_time,make_time,approve_time,total_amount,money_inout_flag,now_disc_amount,now_pay_amount,make_brief,company_id,payway1_id,payway2_id ,NULL branch_id,CAST(NULL AS INTEGER) AS to_branch_id 
		FROM
			sheet_prepay 
		WHERE
			company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL 

)m
WHERE
			m.company_id =~COMPANY_ID  and ~QUERY_CONDITION ~VAR_OTHER_CONDI
UNION
	SELECT
		m.sheet_id,sheet_no,sheet_type,m.supcust_id,getter_id seller_id,m.happen_time,make_time,approve_time,total_amount,money_inout_flag,m.now_disc_amount,m.now_pay_amount,make_brief,m.company_id,payway1_id,payway2_id,NULL branch_id,CAST(NULL AS INTEGER) AS to_branch_id 
	FROM
		sheet_fee_out_main m left join sheet_fee_out_detail d on m.sheet_id=d.sheet_id
	WHERE
		m.company_id =~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL ~VAR_FEE_SHEET_CONDI

)
		M LEFT JOIN info_supcust sc ON M.company_id = sc.company_id 
		AND M.supcust_id = sc.supcust_id
		LEFT JOIN info_operator io ON M.company_id = io.company_id 
		AND io.oper_id = M.seller_id ",
                     
                     QueryOrderSQL=" order by m.approve_time desc"
                  }
                }
            };
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }
        
        public override async Task OnQueryConditionStrGot(string condi, CMySbCommand cmd)
        {
            condi = condi.Replace("where", "and");
            var aa = condi.IndexOf("and (");
            if (DataItems["sub_id"].Value != "")
            {
                string str1 = condi.Substring(0, condi.IndexOf("and (") + 5);
                string str2 = condi.Substring(condi.IndexOf("and (") + 5);
                var str3 = DataItems["sub_id"].Value;
                var str4 = str1 + " fee_sub_id in" + "(" + str3 + ") or" + str2;
                this.SQLVariables["FEE_SHEET_CONDI"] = str1 + " fee_sub_id in" + "(" + str3 + ") or " + str2;
            }
            else
            {
                this.SQLVariables["FEE_SHEET_CONDI"] = condi;
            }
            var costAmount = "sd.wholesale_amount";//批发成本价
            var cost_price_type = DataItems["cost_price_type"].Value;
            switch (cost_price_type)
            {
                case "1"://批发成本价
                    costAmount = "wholesale_amount";
                    break;
                case "2"://加权成本价
                    costAmount = "cost_amount_avg";
                    break;
                case "3"://预设成本价
                    costAmount = "buy_amount";
                    break;
            }
            var branch_id = DataItems["branch_id"].Value;
            var seller_id = DataItems["seller_id"].Value;
            this.SQLVariables["INVENTORY_SHEET_CONDI"] = costAmount;
            //调拨单有出入库 仓库筛选在这里写
            if (branch_id != "")
            {
                this.SQLVariables["OTHER_CONDI"] = $@"and (branch_id = {branch_id} or to_branch_id = {branch_id})";
                this.SQLVariables["FEE_SHEET_CONDI"] += "and 1 = 0 ";//滤掉无仓库属性的fee_out表
            }
            else
            {
                this.SQLVariables["OTHER_CONDI"] = "";
            }
            //seller_id在fee_out表不存在 单独写
            if (seller_id != "" && seller_id != "-1")
            {
                this.SQLVariables["FEE_SHEET_CONDI"] += $@"and getter_id = {seller_id}";
                this.SQLVariables["OTHER_CONDI"] += $@"and seller_id = {seller_id}";
            }
        }
    }
    [Route("api/[controller]/[action]")]
    public class BusinessHistoryListsController : QueryController
    { 
        public BusinessHistoryListsController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BusinessHistoryListsModel model = new BusinessHistoryListsModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            BusinessHistoryListsModel model = new BusinessHistoryListsModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            BusinessHistoryListsModel model = new BusinessHistoryListsModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
