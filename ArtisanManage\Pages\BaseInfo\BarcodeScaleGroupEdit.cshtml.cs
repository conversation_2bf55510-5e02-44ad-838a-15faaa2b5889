using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;

namespace ArtisanManage.Pages.BaseInfo 
{
    public class BarcodeScaleGroupEditModel : PageFormModel
    {
        public BarcodeScaleGroupEditModel(CMySbCommand cmd,string company_id="",string oper_id="") : base(Services.MenuId.infoClient)
        {
            this.cmd=cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"barcode_scale_group_id",new DataItem(){Title="条码秤组id",CtrlType="hidden",FldArea="divHead"}},
                {"barcode_scale_group_no",new DataItem(){Title="条码秤组编码",Necessary=true,FldArea="divHead"}},
                {"barcode_scale_group_name",new DataItem(){Title="条码秤组名称",Necessary=true,FldArea="divHead"}},
            };
 
            m_idFld = "barcode_scale_group_id"; m_nameFld = "barcode_scale_group_name";
            m_tableName = "info_barcode_scale_group";
            m_selectFromSQL = "from info_barcode_scale_group where barcode_scale_group_id='~ID'";
        }

        public async Task OnGet()
        {  
            await InitGet(cmd);   
        } 
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class BarcodeScaleGroupEditController : BaseController
    { 
        public BarcodeScaleGroupEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues)
        {
            BarcodeScaleGroupEditModel model = new BarcodeScaleGroupEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            BarcodeScaleGroupEditModel model = new BarcodeScaleGroupEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            BarcodeScaleGroupEditModel model = new BarcodeScaleGroupEditModel(cmd);
            return await model.SaveTable(cmd, request);

        }
    }
}