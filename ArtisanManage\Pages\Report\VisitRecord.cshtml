@page
@model ArtisanManage.Pages.BaseInfo.VisitRecordModel
@using HuaWeiObsController;
@{
    Layout = null;

}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxwindow.js"></script>
    <script src="~/js/jszip.min.js"></script>
    <script src="~/js/FileSaver.js"></script>
<script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        //start
        var JsonOperRights = `@Html.Raw(Model.JsonOperRights)`;
        var JsonOperRightsOrig = `@Html.Raw(Model.JsonOperRightsOrig)`;
        var OperID = '@Model.OperID';
        var maintainReviewRight = false
        if (JsonOperRights) {

           // JsonOperRightsOrig = JsonOperRightsOrig.replace(/&quot;/g, '"')
           // JsonOperRightsOrig = JsonOperRightsOrig.replace(/&#xA;/g, '')
            var operRights = JSON.parse(JsonOperRightsOrig)
            if (operRights != null && operRights.sale != null) {
                maintainReviewRight = operRights.sale.sheetDisplayAgreement.review
            }
        }
        //end
      
    	    var newCount = 1;
            let gRows=[]
        var itemSource = {};
        //查询结果放入全局rows中//
        function QueryDataAndSetgRows() {
            window.QueryData("", res => {
                gRows = res.rows
            });
        }
        function QueryForDownloadPic(callback){
            const startTime=$("#startDay").val()
            const endTime=$("#endDay").val()
            var seller_id=""
            const seller=$("#seller_id").val()
            if(seller){
               seller_id=seller.value
            }
            $.ajax({
                url:"/api/VisitRecord/GetDataForDownloadPics",
                data: {
                    operKey:window.g_operKey,
                    startTime,
                    endTime,
                    seller_id
                },
                success: (res) => {
                    callback(res)
                }
            })
        }
        getFile = (url) => {
            return new Promise((resolve, reject) => {
                var xhr = new XMLHttpRequest();
                xhr.open('GET', url+"?a="+Math.random()*1000, true);
                xhr.responseType = "blob"; // 设置返回类型blob
                // 定义请求完成的处理函数，请求前也可以增加 加载框/禁用下载按钮的相关逻辑
                xhr.onload = function () {
                    if (this.status === 200) {
                        var blob = this.response;
                        var reader = new FileReader();
                        reader.readAsDataURL(blob); // 转换为base64
                        reader.onload = function (e) {
                            resolve(blob)
                        }
                    }
                    else {
                        resolve("error")
                    }
                };
                xhr.send() //发送ajax请求
            })
        };
        function isNullObj(obj){
            return JSON.stringify(obj)==='{}'
        }
function timeOnRoadRender(row, column, value, p4, p5, rowData) {

  
    if (!rowData.start_time) {
        return ""
    }
    var operLastRows = Object.keys(gRows).map(key => {
        var row = gRows[key]
        return {
            visit_id: row.visit_id,
            oper_name: row.oper_name,
            end_time: row.end_time
        }
    }).filter(row => {
        if (!row.end_time) {
            return false
        }
        var isSameDay = row.end_time.split(" ")[0] == rowData.start_time.split(" ")[0] 
        return isSameDay && row.oper_name == rowData.oper_name && Number(row.visit_id)<Number(rowData.visit_id)
    })
    if (operLastRows.length == 0) {
        return ""
    }
    var lastVisitTime = operLastRows[0].end_time
    var interval = new Date(rowData.start_time).getTime() - new Date(lastVisitTime).getTime() 
    console.log(interval)
     return `<div class="jqx-grid-cell-left-align" style="margin-top: 22px;">${Math.ceil(Number(interval)/1000/60)}分钟</div>`
     
}
        function getDisplayPicList(rows) {
            var showcaseObjectList = []
            rows.forEach((row) => {
                let showcase_pics_str = row.showcase_pictures
                showcase_pics_str = showcase_pics_str.length == 0 ? "[]" : showcase_pics_str
                const showcase_pics_list = JSON.parse(showcase_pics_str)
                showcaseObjectList.push({
                    oper_name: row.oper_name,
                    sup_name: row.sup_name,
                    start_time:row.start_time,
                    end_time: row.end_time,
                    showcase_pics_list: showcase_pics_list
                })
            })
            console.log(showcaseObjectList)
            let total_list = []
            const formatUrlShowcaseObjectList = showcaseObjectList.map(showcaseObject => {
                console.log(showcaseObject)
                showcaseObject.folderName = formatFileName(showcaseObject)
                showcaseObject.showcase_pics_list = formatImageList("@Html.Raw(Model.ObsBucketLinkHref)/uploads", showcaseObject.showcase_pics_list)
                return showcaseObject
            })
            formatUrlShowcaseObjectList.forEach(res => {
                var index = 0
                res.showcase_pics_list.forEach(showcase_pic => {
                    total_list.push({
                        file_name: res.folderName + "_" + index,
                        pic_url: showcase_pic
                    })
                    index++
                })

            })
            return total_list
        }
        function getDoorPicList(rows){
            door_pic_list = []
            rows.forEach((row) => {
                if (row.door_picture) {
                    door_pic_list.push({
                        pic_url: row.door_picture,
                        file_name: formatFileName(row)
                    })
                }
            })
            const door_pic_url_list = door_pic_list.map(e => {                      
                e.pic_url = "@Html.Raw(Model.ObsBucketLinkHref)/uploads" + e.pic_url
                return e
            })
            return door_pic_url_list
        }
        // 批量下载
        handleBatchDownload = async (zip,zipFileName,selectImgList,folderName='') => {
            const data = selectImgList;
            const promises = []
            await data.forEach(item => {
                const promise = this.getFile(item.pic_url).then(data => { // 下载文件, 并存成ArrayBuffer对象
                    let file_name = item.file_name + ".jpeg" // 获取文件名
                    if (folderName === '') {
                        zip.file(file_name, data, {
                            binary: true
                        }) // 逐个添加文件
                    } else {
                        zip.folder(folderName).file(file_name, data, {
                            binary: true
                        }) // 逐个添加文件
                    }
                }).catch(e => {
                    console.log(e)
                })
                promises.push(promise)
            })
            Promise.all(promises).then(() => {
                zip.generateAsync({
                    type: "blob"
                }).then(content => { // 生成二进制流
                    saveAs(content, `${zipFileName}.zip`) // 利用file-saver保存文件
                })
            })

        };
        function chunkArray(imgList,arraySize) {
            var arr = []
            var innerArr = []
            imgList.map((val, index) => {
                if (innerArr.length == arraySize) {
                    arr.push(innerArr)
                    innerArr = []
                }
                innerArr.push(val)
            })
            if (innerArr.length != 0) {
                arr.push(innerArr)
            }
            return arr
        }
        let photo_index = 0
        function previewImage(visitID) {
            var win = $(window);
            var screenWidth = win.width();
            var screenHeight = win.height();
            $('#photos').jqxWindow({
                width: screenWidth,
                height: screenHeight,
                resizable: true,
                draggable: true,
                showCloseButton: true,
                autoOpen: false,
                zIndex: 9999,
            })
            let domain = "@HuaWeiObs.BucketLinkHref" + '/'
            $("#photos").jqxWindow('setContent', `<iframe src="VisitPhoto?operKey=${g_operKey}&visitID=${visitID}&domain=${domain}" width="100%" height="100%" scrolling="auto" frameborder="no"></iframe>`);
            $('#photos').jqxWindow('open');
        };
        function previewMaintainInfo(visitID) {
            var win = $(window);
            var screenWidth = win.width();
            var screenHeight = win.height();
            $('#maintain-photos').jqxWindow({
                width: screenWidth,
                height: screenHeight,
                resizable: true,
                draggable: true,
                showCloseButton: true,
                autoOpen: false,
                zIndex: 9999,
            })
            let domain = "@HuaWeiObs.BucketLinkHref" + '/'
            $("#maintain-photos").jqxWindow('setContent', `<iframe src="MaintainInfoSheetView?operKey=${g_operKey}&visitID=${visitID}&domain=${domain}&maintainReviewRight=${maintainReviewRight}" width="100%" height="100%" scrolling="auto" frameborder="no"></iframe>`);
            $('#maintain-photos').jqxWindow('open');
        };

        //diffcode 防止重名的区分码，随机数/顺序由上层参数
        function formatFileName(data,diffCode="") {
            var { oper_name, sup_name, start_time, end_time } = data
            var startDate = new Date(start_time)
            const startTimeStr = formatDateyyyyMMddHHmmSS(startDate)
            var endDate = new Date(end_time)
            const endTimeStr = formatDateyyyyMMddHHmmSS(endDate)
            return `${oper_name}_${sup_name}_${startTimeStr}_${endTimeStr}`
        };
        function formatDateyyyyMMddHHmmSS(date) {
            const year = date.getFullYear()
            const month = ((date.getMonth() + 101) + "").substring(1)
            const day = ((date.getDay() + 100) + "").substring(1)
            const hour = date.getHours()
            const minute = date.getMinutes()
            const second = date.getSeconds()
            return year + month + day + hour + minute + second
        }
        function formatImageList(prefix, urlInDbList) {
            const urlList=urlInDbList.map(url => {
                return prefix+url
            })
            console.log(urlList)
            return urlList
        }

        function generateZipNameByQueryDom() {
            const startDay = $("div[id='startDay']").val().split("\s")[0]
            const endDay = $("div[id='endDay']").val().split("\s")[0]
            const sellername = $("div[id='seller_id']")[0].children[0].dataset.label
            const sellernameStr = typeof sellername === undefined ? "" : sellername
            const filename = startDay + "_" + endDay + "_" + sellernameStr
            return filename
        }
$(document).ready(function() {
    @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
        QueryDataAndSetgRows()
    $("#gridItems").on("cellclick", function(event) {
        console.log(event)
        if (event.args.datafield === 'address') {
            const row = gRows[event.args.rowindex]
            console.log(gRows)
            window.parent.newTabPage("查看地图", `../Report/bmap?latitude=${row.latitude}&longitude=${row.longitude}`);
                }
                else if (event.args.datafield == "end_address") {
                    const row = gRows[event.args.rowindex]
                    console.log(gRows)
                    window.parent.newTabPage("查看地图", `../Report/bmap?latitude=${row.end_latitude}&longitude=${row.end_longitude}`);
                }
                else if (event.args.datafield == "showcase_pictures") {
                    var visit_id = args.row.bounddata.visit_id;
                    if (visit_id) previewImage(visit_id)
                } else if (event.args.datafield == "maintain_review"){
                    var visit_id = args.row.bounddata.visit_id;
                    if (visit_id) previewMaintainInfo(visit_id)
                }
          

    });
    $("#gridItems").on("cellselect", function(event) {
        console.log(event)
    })
    $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

    $("button[id='searchBtn']").click(function() {
        QueryDataAndSetgRows()
    })

    $("button[id='zipDownloadDoorPicBtn']").click(function() {
        QueryForDownloadPic((data) => {
            const zipFileName = generateZipNameByQueryDom()
            const zip = new JSZip()
            const doorPicUrlList = getDoorPicList(data)
            handleBatchDownload(zip, "yingjiang_" + zipFileName, doorPicUrlList)
        })
    })

    $("button[id='zipDownloadDoorPicBtn']").mouseover(function() {
            this.innerText='一键下载'
    })
     $("button[id='zipDownloadDoorPicBtn']").mouseout(function() {
            this.innerText='门头照片'
    })

    $("button[id='zipDownloadAllBtn']").click(function() {
        QueryForDownloadPic((data) => {
            const zipFileName = generateZipNameByQueryDom()
            const doorPicUrlList = getDoorPicList(data)
            const displayPicUrlList = getDisplayPicList(data)
            const selectImgList = doorPicUrlList.concat(displayPicUrlList)
            console.log(selectImgList)
            var selectImgListGroups = chunkArray(selectImgList, 300)
            console.log(selectImgListGroups)
            selectImgListGroups.map((imgs,index) => {
                   const zip = new JSZip()
                   bw.toast(`照片过多，采用分段打包的方式下载(${index + 1}/${selectImgListGroups.length})`)
                   handleBatchDownload(zip, "yingjiang_" + zipFileName + "_" + index, imgs)
                    setTimeout(() => { },1000)
            })
        })
            })

            $("button[id='zipDownloadDisplayPicBtn']").click(function () {
                 QueryForDownloadPic((data) => {
                const zipFileName = generateZipNameByQueryDom()
                const total_list=getDisplayPicList(data)
                const zip = new JSZip()
                handleBatchDownload(zip, "yingjiang_" + zipFileName, total_list)
                 })
            })

     $("button[id='zipDownloadDisplayPicBtn']").mouseover(function() {
            this.innerText='一键下载'
    })
     $("button[id='zipDownloadDisplayPicBtn']").mouseout(function() {
            this.innerText='陈列照片'
    })
  })

    </script>
</head>

<body style="overflow:hidden">
    <style>
        html, body {
            height: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }
        .photoPage{
            width:100%;
        }
        .prePhoto {
            margin-top:-22px;
            position: absolute;
            top: 50%;
            left: 0px;
            padding: 10px;
            background: #000;
            border-radius: 50%;
            opacity: 0.5;
            font-size: 24px;
            color: #fff;
            margin-left:20px;
        }
        #close_photo{
            position:absolute;
            right:0px;
            font-size:30px;
            cursor:pointer;
            border:1px solid #000;
            background:#000;
            padding:10px;
            color:#fff;
            z-index:999;
        }
        .nextPhoto {
            margin-top: -22px;
            margin-right: 20px;
            position: absolute;
            top: 50%;
            right: 0px;
            opacity: 0.5;
            padding: 10px;
            border-radius: 50%;
            background: #000;
            font-size: 24px;
            color: #fff;
        }
        .dataArea {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            margin: 0;
            width: 100%;
            height: 100%;
        }

        .jqx-grid-cell-middle-align {
            margin-top: 0px !important;
        }
            .dataArea > div:first-child {
                width: 100%;
                height: 40px;
            }

            .dataArea > div:last-child {
                display: flex;
                align-items: stretch;
                align-content: stretch;
                width: 100%;
                flex-grow: 1;
            }

                .dataArea > div:last-child > div:first-child {
                    width: 200px;
                }

                .dataArea > div:last-child > div:last-child {
                    flex-grow: 1;
                }
    </style>
    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button id="searchBtn" style="margin-left:20px;min-width:42px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;min-width:42px;">导出</button>
        <button id="zipDownloadAllBtn" style="margin-left:20px;width:140px">全部下载</button>
        <button id="zipDownloadDoorPicBtn" style="margin-left:20px;width:140px">门头照片</button>
        <button id="zipDownloadDisplayPicBtn" style="margin-left:20px;width:140px">陈列照片</button>
    </div>
    <div id="gridItems" style="margin-left:10px; margin-top:10px;width:calc(100% - 20px);height:calc(100% - 80px);"></div>

    <!--<div class="dataArea">
        <div>
            <div>
                <div style="height:5px;">

                </div>
                
            </div>
        </div>
    </div>-->
    @*  <div id='mapWindow'>
            <div>Header</div>
            <div>Content</div>
        </div>
    *@
<div id="photos" style="display:none">
        <div id="itemCaption" style="padding:10px 0;background-color:#fff;display:flex;justify-content:center"><span style="font-size:20px;">照片</span></div>
        <div style="padding:10px;overflow:hidden;display:flex;justify-content:center"> </div>
</div>
    <div id="maintain-photos" style="display:none">
        <div id="itemCaption" style="padding:10px 0;background-color:#fff;display:flex;justify-content:center"><span style="font-size:20px;">陈列协议维护</span></div>
        <div style="padding:10px;overflow:hidden;display:flex;justify-content:center"> </div>
    </div>
<div id="popItem" style="display:none">
    <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
    <div style="overflow:hidden;"> </div>
</div>
    <div id="preview-image" style="display:none">
        <div id="itemCaption" style="padding:10px 0;background-color:#fff;display:flex;justify-content:center"><span style="font-size:20px;">照片</span></div>
        <div style="padding:10px;overflow:hidden;display:flex;justify-content:center"> </div>
    </div>
 
</body>

</html>