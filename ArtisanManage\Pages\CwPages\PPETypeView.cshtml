﻿@page
@model ArtisanManage.Pages.CwPages.PPETypeViewModel
@{
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var newCount = 1;
        var itemSource = {};
        $(document).ready(function () {
            window.contextMenu0 = $("#jqxMenu_0").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            QueryData();
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            $("#gridItems").on("cellclick", function (event) {
                let args = event.args;
                if (args.datafield == "type_no") {
                    var type_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, 'type_id');
                    $('#popItem').jqxWindow('open');
                    $("#popItem").jqxWindow('setContent', '<iframe src="PPETypeEdit?operKey=' + g_operKey + '&type_id=' + type_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                }else{
                    if (args.rightclick) {//右击menu
                        $('#gridItems').jqxGrid('selectrow', args.rowindex);
                        contextMenu0.jqxMenu('open', event.owner.mousecaptureposition.left, event.owner.mousecaptureposition.top);
                    }
                }
            });

        });

        function checkAndQuery() {
            QueryData();
        }

        function btnAddItem_click(e) {
            let type_no='GDZC-001';
            let rows=$('#gridItems').jqxGrid('getrows').filter(row=> row.type_no.length==8 && row.type_no.substring(0,5)=='GDZC-' && Number.isInteger(parseInt(row.type_no.substring(5))) );
            if (rows.length>0) {
                let max_no = rows.reduce((prev, current) => (Number.isInteger(parseInt(prev.type_no.substring(5))) > Number.isInteger(parseInt(current.type_no.substring(5))) ? prev : current)).type_no.substring(5);
                if ((parseInt(max_no) + 1).toString().length == 1) {
                    max_no = '00' + (parseInt(max_no) + 1).toString();
                } else if ((parseInt(max_no) + 1).toString().length == 2){
                    max_no = '0' + (parseInt(max_no) + 1).toString();
                } else {
                    max_no = (parseInt(max_no) + 1).toString();
                }
                type_no=`GDZC-${max_no}`;
            }

            $('#popItem').jqxWindow('open');
            console.log('cw ppe type view open type edit');
            $("#popItem").jqxWindow('setContent', `<iframe src="PPETypeEdit?operKey=${g_operKey}&type_no=${type_no}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }

        function btnRemoveItem_click(e) {
            let rowindex = $('#gridItems').jqxGrid('getselectedrowindex');
            let selectedItem = $('#gridItems').jqxGrid('getrowdata', rowindex);
            if (!selectedItem) {
                bw.toast("请先选择一行");
                return;
            }
            jConfirm('确定要删除吗？', function () {
                $.ajax({
                    type: "POST",
                    url: "../api/PPETypeView/DeleteRecords",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify({ operKey: g_operKey, gridID: 'gridItems', rowIDs: selectedItem.type_id }),
                    success: function (data) {
                        if (data.result == "OK") {
                            bw.toast('已删除');
                            QueryData();
                        }
                        else {
                            bw.toast(data.msg);
                        }
                    },
                    error: (xhr) => {
                        bw.toast('网络错误 ' + xhr.responseText);
                    }
                });
            }, "");


        };

        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "PPETypeEdit") {
                $('#gridItems').jqxGrid('clear');
                $('#gridItems').jqxGrid('updatebounddata');
                $("#popItem").jqxWindow('close');
                QueryData();
            }
        });

        
    </script>
</head>
<body>

    <div style="display:flex;padding-top:20px;">

        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="checkAndQuery()" style="margin-left:20px;" class="main-button">查询</button>
        <button onclick="btnAddItem_click()" style="margin-right:20px;" class="main-button">添加</button>
    </div>

    <div id="gridItems" style="margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 95px);"></div>
    <div id="divRowCount"><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">资产类别</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

    <div id='jqxMenu_0'>
        <ul>
            <li id="mnuRemoveClass" onclick="btnRemoveItem_click()">删除</li>
        </ul>
    </div>
</body>
</html>