@page
@model ArtisanManage.Pages.BaseInfo.EmartItemsViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">

<head id="Head1" runat="server"> 
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" type="text/css"/>

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead == "ItemEdit") {
               // var newID = ""; var newName = "";
               // if (rs.data.record) { newID = rs.data.record.item_no; newName = rs.data.record.item_name; }
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var gridUnit = rs.data.formData.gridUnit[0]
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = rs.data.record;
                        rows[0].i = rows[0].item_id;
                        rows[0].n = rows[0].item_name;
                        rows[0].status = rs.data.record.cls_status_name;
                        rows[0].b = rs.data.record.brand_name;
                        if (gridUnit) {
                            rows[0].barcode = gridUnit.barcode;
                            rows[0].unit_no = gridUnit.unit_no;
                        }
                        window.source_gridItems.totalrecords++;

                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }

                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "n",rs.data.record.item_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "item_no", rs.data.record.item_no);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "barcode", rs.data.record.barcode);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "unit_no", rs.data.record.unit_no);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "b", rs.data.record.brand_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "status", rs.data.record.cls_status_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "c", rs.data.record.unit_conv);
                   // QueryData();

                }

                $("#popItem").jqxWindow('close');
            }
            else if (rs.data.msgHead == "ClassEdit") {
                var newID = "";  var newName = "";
                if (rs.data.record) { newID = rs.data.record.class_id; newName = rs.data.record.class_name; }
                if (rs.data.action == "add") {
                    var sltItem = $('#other_class').jqxTree('findItem', rs.data.record.mother_id);
                    $('#other_class').jqxTree('addTo', { value: newID, label: newName }, sltItem.element, false);
                    $('#other_class').jqxTree('render');   // update the tree.
                    attachContextMenu();
                }
                else if (rs.data.action == "update") {
                    var sltItem = $('#other_class').jqxTree('findItem', rs.data.record.class_id);
                    $('#other_class').jqxTree('updateItem', sltItem, { label: newName });
                    $('#other_class').jqxTree('render');
                    attachContextMenu();
                }
                $("#popClass").jqxWindow('close');
            }

            console.log(rs.data);
        });

        function myConsoleLog(msg){
            console.log(`%cEmart%cItemsView%c ${msg}`,
                'background: #222;color: #fff;font-weight:bold;padding:2px 2px 2px 4px;border-radius:4px 0 0 4px;',
                'background: #fe9a00;color: #000;font-weight:bold;padding:2px 4px 2px 2px;border-radius:0px 4px 4px 0px;',
                'background:none;color:#000;'
            )
        }

    	function showLog(str) {
    	    if (!log) log = $("#log");
    	    log.append("<li class='" + className + "'>" + str + "</li>");
    	    if (log.children("li").length > 8) {
    	        log.get(0).removeChild(log.children("li")[0]);
    	    }
    	}
    	function getTime() {
    	    var now = new Date(),
		    h = now.getHours(),
		    m = now.getMinutes(),
		    s = now.getSeconds(),
		    ms = now.getMilliseconds();
    	    return (h + ":" + m + ":" + s + " " + ms);
    	}

    	var newCount = 1;
    	function btnAddClass_click(e)
        {

    	    var selectedItem = $('#other_class').jqxTree('selectedItem');
    	    if (!selectedItem) {
    	        bw.toast("请先选择一个类");
    	        return;
    	    }
            $('#popClass').jqxWindow('open');
            $("#popClass").jqxWindow('setContent', `<iframe src="ClassEdit?operKey=${g_operKey}&mother_id=${selectedItem.value}&mother_name=${selectedItem.label}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

    	};

        function btnEditClass_click() {

    	    var selectedItem = $('#other_class').jqxTree('selectedItem');
    	    if (!selectedItem) {
    	        bw.toast("请先选择一个类");
    	        return;
    	    }
    	    $('#popClass').jqxWindow('open');
            $("#popClass").jqxWindow('setContent', `<iframe src="ClassEdit?operKey=${g_operKey}&class_id=${selectedItem.value}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

    	};
    	function btnRemoveClass_click(e) {
    	    var selectedItem = $('#other_class').jqxTree('selectedItem');
    	    if (!selectedItem) {
    	        bw.toast("请先选择一个类");
    	        return;
            }
            jConfirm('确定要删除' + selectedItem.label+'吗？', function () {
		        $.ajax({
    	            type: "POST",
    	            url: "../api/ItemsView/RemoveClass",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify({ operKey: g_operKey, class_id: selectedItem.value }),
    	            success: function (data) {
                        if (data.result == "OK") {
                            var sltItem = $('#other_class').jqxTree('findItem', data.class_id);
                            $('#other_class').jqxTree('removeItem', sltItem, false);
                            $('#other_class').jqxTree('render');
                            attachContextMenu();
    	                }
    	                else {
                            bw.toast(data.result);
    	                }
    	            }
    	        });
                }, "");


    	};

        function btnAddItem_click(e) {

            var selectedItem = $('#other_class').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个类");
                return;
            }
            var path = $('#other_class').jqxTree('getTreePath', selectedItem);
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="ItemEdit?operKey=${g_operKey}&item_class=${selectedItem.value}&class_name=${selectedItem.label}&other_class=${path}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

        }

        function attachContextMenu() {
            if (!window.contextMenu) window.contextMenu = $("#jqxMenu_1").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            function isRightClick(event) {
                var rightclick;
                if (!event) var event = window.event;
                if (event.which) rightclick = (event.which == 3);
                else if (event.button) rightclick = (event.button == 2);
                return rightclick;
            }
            // open the context menu when the user presses the mouse right button.

            $("#other_class li").on('mousedown', function (event) {
                var target = $(event.target).parents('li:first')[0];
                var rightClick = isRightClick(event);
                var contextMenu = event.target.innerText == "全部" ? contextMenu0 : contextMenu1

                if (rightClick && target != null) {
                    $("#other_class").jqxTree('selectItem', target);
                    var scrollTop = $(window).scrollTop();
                    var scrollLeft = $(window).scrollLeft();
                    var y = event.clientY
                    var treeTop = $('#other_class').offset().top
                    var treeHeight = $('#other_class').height()
                    var menuHeight = $("#jqxMenu_1").height()
                    //console.log(event.offsetY)
                    if (event.clientY > treeTop + treeHeight - menuHeight -0) {
                        y=event.clientY - menuHeight -10
                    }
                    contextMenu.jqxMenu('open', parseInt(event.clientX) + 5 + scrollLeft, y + 5 + scrollTop);

                    return false;
                }
            });
        }

        function onGridRowEdit(rowIndex) {
            var item_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'i');
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }
        var itemSource = {};

        function btnSelectItems_click() {
            var rows = window.g_checkedRows
           //  var rows = window.g_arrCheckedRows
            var checkedRows=[]
            for (var id in rows) {
                var row=rows[id]
              
                checkedRows.push({
                    item_id: row.i, item_name: row.n,order_sub_id: row.order_sub_id, order_sub_name: row.order_sub_name, order_price: row.order_price, order_unit_factor: row.order_unit_factor, order_qty: row.order_qty, order_qty_unit: row.order_qty_unit, borrowed_qty: row.borrowed_qty, 
                    have_emart: row.have_emart, b_emart_id: row.b_emart_id, m_emart_id: row.m_emart_id, s_emart_id: row.s_emart_id,
                    b_qty:row.b_qty,m_qty:row.m_qty,s_qty:row.s_qty,b_unit_no:row.b_unit_no,m_unit_no:row.m_unit_no,s_unit_no:row.s_unit_no,b_unit_factor:row.b_unit_factor,m_unit_factor:row.m_unit_factor,s_unit_factor:row.s_unit_factor,
                    disp_flow_id: row.disp_flow_id, disp_sheet_id: row.disp_sheet_id, disp_left_qty: row.disp_left_qty, disp_unit_no: row.disp_unit_no, disp_month: row.disp_month, disp_month_id: row.disp_month_id, disp_items_id: row.disp_items_id, disp_items_name: row.disp_items_name
                }) 
            }

            if (checkedRows.length == 0) {
                bw.toast('请至少选择一行商品')
                return
            }
            var msg = {
                msgHead: 'ItemsView', action: 'selectMulti', checkedRows: checkedRows
            }
            window.parent.postMessage(msg, '*');
        }

        function onItemNameClick(rowIndex) {
            if (ForSelect) {

                //  var rows = $('#gridItems').jqxGrid('getrows')
                var checkedRows = []
                var rows = $('#gridItems').jqxGrid('getrows')
                var row = rows.find(r=>r.uid==rowIndex)
                checkedRows.push({
                    item_id: row.i, item_name: row.n,
                    have_emart: row.have_emart, b_emart_id: row.b_emart_id, m_emart_id: row.m_emart_id, s_emart_id: row.s_emart_id,
                    order_sub_id: row.order_sub_id, order_sub_name: row.order_sub_name, order_price: row.order_price, order_unit_factor: row.order_unit_factor, order_qty: row.order_qty, order_qty_unit: row.order_qty_unit, borrowed_qty: row.borrowed_qty, 
                    disp_flow_id: row.disp_flow_id, disp_sheet_id: row.disp_sheet_id, disp_left_qty: row.disp_left_qty, disp_unit_no: row.disp_unit_no, disp_month: row.disp_month, disp_month_id: row.disp_month_id, disp_items_id: row.disp_items_id, disp_items_name: row.disp_items_name
                })

                var msg = {
                    msgHead: 'ItemsView', action: 'selectMulti', checkedRows: checkedRows
                }

                /*var msg = {
                    msgHead: 'ItemsView', action: 'select', item_id: item_id, item_name: item_name, order_sub_id: order_sub_id, order_sub_name: order_sub_name, order_price: order_price, unit_factor: unit_factor, order_qty:order_qty, order_qty_unit:order_qty_unit
                };*/
                window.parent.postMessage(msg, '*');
            }
            else {
                onGridRowEdit(rowIndex);
                //$('#popItem').jqxWindow('open');
                // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
            }

        }

        function onmouseleaveMultiItems() {
            if (window.tmShowDispItem) {
                clearTimeout(window.tmShowDispItem)
                window.tmShowDispItem = 0
            }
            $('#popMultiItems').hide();
        }
        window.onmousewheel = function (e) {
            onmouseleaveMultiItems;
        }


        function onMouseEnterItemName(event, rowIndex) {

            if (ForSelect) {

                var rows = $('#gridItems').jqxGrid('getrows')
                var row = rows[rowIndex]
                if (row.disp_items_id && row.disp_items_id.indexOf(',') > 0) {
                    if (window.tmShowDispItem) {
                        clearTimeout(window.tmShowSysPrice)
                        window.tmShowSysPrice = 0
                    }
                    window.tmShowDispItem = setTimeout(() => {
                    var items_id = row.disp_items_id.split(',');
                    var items_name = row.disp_items_name.split(',');
                    var pop = `<div id="popMultiItems" onmouseleave="onmouseleaveMultiItems()"  style="z-index:999999999;display:none;position:absolute; background-color:rgb(232 232 232);border-color:#999;border-width:1px;border-style:solid;border-radius:6px;overflow:auto "> </div> `

                    var popDiv = $('#popMultiItems');
                    if (popDiv.length == 0) {
                        $('body').append(pop)
                        popDiv = $('#popMultiItems');
                        //popDiv.on('onmouseleave', ()=>{
                        //    popDiv.hide()
                        //})
                    }
                    popDiv.empty()
                    var html = '<ul id = "ulItems" style="list-style-type:none;font-size:14px;padding-left:10px;display:flex;flex-direction: column;">';
                    for (var i = 0; i < items_id.length; i++) {
                        html += `<li style="height:30px;"> <span style = "cursor:pointer;" onclick='onDispItemliClick(${items_id[i]},"${items_name[i]}",${rowIndex})'>${items_name[i]}</span></li>`;
                    }
                    html += '</ul>';
                    popDiv.append(html);

                    var cx = event.clientX
                    var cy = event.clientY
                    var ht = 200, wd = 250;
                    popDiv.css('left', cx + 15)
                    popDiv.css('top', cy-15)
                    popDiv.css('width', wd)
                    popDiv.show()
                    //setTimeout(function () {

                    //    //popDiv.animate({
                    //    //     width: wd, left: cx - wd/2,top:cy-ht/2
                    //    //},500)
                    //}, 200);
                    }, 300)
                }


            }


        }

        function onDispItemliClick(item_id, item_name, rowIndex) {
            if (ForSelect) {
                var rows = $('#gridItems').jqxGrid('getrows')
                var row = rows[rowIndex]
                var checkedRows = []
                checkedRows.push({
                    item_id: item_id, item_name: item_name,
                    order_sub_id: row.order_sub_id, order_sub_name: row.order_sub_name, order_price: row.order_price, order_unit_factor: row.order_unit_factor, order_qty: row.order_qty, order_qty_unit: row.order_qty_unit,
                    disp_flow_id: row.disp_flow_id, disp_sheet_id: row.disp_sheet_id, disp_left_qty: row.disp_left_qty, disp_unit_no: row.disp_unit_no, disp_month: row.disp_month, disp_month_id: row.disp_month_id, disp_items_id: row.disp_items_id, disp_items_name: row.disp_items_name
                })

                var msg = {
                    msgHead: 'ItemsView', action: 'selectMulti', checkedRows: checkedRows
                }

                /*var msg = {
                    msgHead: 'ItemsView', action: 'select', item_id: item_id, item_name: item_name, order_sub_id: order_sub_id, order_sub_name: order_sub_name, order_price: order_price, unit_factor: unit_factor, order_qty:order_qty, order_qty_unit:order_qty_unit
                };*/
                window.parent.postMessage(msg, '*');
            }
        }

        function itemNameRenderer (row, column, value, p4, p5, rowData) {
            var disp_flow_id = rowData.disp_flow_id;
            var order_sub_id = rowData.order_sub_id;
            let borrowed_qty = rowData.borrowed_qty;
            var lab = rowData.disp_month + '月陈列';
            var dispLab = '', orderLab = '', hideItems = '', borrowLab = '';
            if (rowData.disp_items_id&&rowData.disp_items_id.indexOf(',') > 0) {
                hideItems = `<label onmouseenter='onMouseEnterItemName(event,${row})'  style="cursor:pointer;margin-left:4px;color:#49f;margin-right:2px">${value}</label><label onmouseenter='onMouseEnterItemName(event,${row})'  style="margin-right:5px;cursor:pointer;color:#49f;">(等)</label>`;
            } else hideItems = `<label onmouseenter='onmouseleaveMultiItems(event,${row})'  style="cursor:pointer;margin-left:4px;color:#49f;margin-right:2px">${value}</label>`
            if (disp_flow_id) dispLab = `<label style="margin-right:3px;background:#e6214a;color:white;border-radius:3px;width:50px;font-size:10px;text-align:center;line-height:16px" >${lab}</label>`
            if (order_sub_id) orderLab = `<label style="margin-right:3px;background:#e6214a;color:white;border-radius:3px;width:50px;font-size:10px;text-align:center;line-height:16px" >定</label>`
            if (borrowed_qty) borrowLab = `<label style="margin-right:3px;background:#e6214a;color:white;border-radius:3px;width:50px;font-size:10px;text-align:center;line-height:16px" >借</label>`
            return `<div onclick='onItemNameClick(${row})' style="height:100%;display:flex;align-items:center;justify-content:flex-start;" >${dispLab}${orderLab}${borrowLab}${hideItems}</div>`
        }
        function renderStockQty(row,stock_qty,son_stock_qty) {
            var stockLabel = `<label style="cursor:pointer;margin-left:4px;color:#000;margin-right:2px">${stock_qty}</label>`
             var sonStockLabel=''
             if (son_stock_qty) {
                  sonStockLabel = `<label style="cursor:pointer;margin-left:4px;color:#777;font-size:6px;margin-right:2px">${son_stock_qty}</label>`
             }
            return `<div onclick='onItemNameClick(${row})' style="height:100%;display:flex;align-items:center;justify-content:flex-start;" >${stockLabel}${sonStockLabel}</div>`
       
        }
        function stockQtyRenderer (row, column, value, p4, p5, rowData) { 
            return renderStockQty(row,rowData.stock_qty_unit,rowData.son_stock_qty)
        }
        function usableStockQtyRenderer (row, column, value, p4, p5, rowData) { 
            return renderStockQty(row,rowData.usable_stock_qty,rowData.son_usable_stock_qty)
        }
        function sellPendStockQtyRenderer (row, column, value, p4, p5, rowData) { 
            return renderStockQty(row,rowData.sell_pend_stock_qty,rowData.son_sell_pend_stock_qty)
        }

        $(document).ready(function () {
            window.contextMenu0 = $("#jqxMenu_0").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            window.contextMenu1 = $("#jqxMenu_1").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });

            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)

            //$("#btnAddClass").bind("click", { isParent: false }, btnAddClass_click);
            //$("#btnEditClass").bind("click", btnEditClass_click);
            //$("#btnRemoveClass").bind("click", btnRemoveClass_click);
            var source = $('#other_class').jqxTree('source')
            var topNode = source[0]
            var eleItems = $('#other_class').jqxTree('getItems')
            debugger;
            function setStopItemVisible(node, visible) {
                if (node.status == '0') {
                    var item = eleItems.find(item => item.value == node.v)
                    if (item) {
                        item.element.style.display =visible ? "" : "none"
                    }
                }
                else if (node.items) {
                    node.items.forEach(son => {
                         setStopItemVisible(son,visible)
                    })                    
                }
            }
            setStopItemVisible(topNode,false)

            

            $('#status').on('change', function (event) {
                var status = $('#status').jqxInput('val')
                status = status.value
                setStopItemVisible(topNode,status == 'all' || status == 'stop'?true:false)                
            })
           

            $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

            if (!window.ForSelect) {
                var items = $('#other_class').jqxTree('getItems')
              //  if (items[1].label == '常用') $('#other_class').jqxTree('disableItem', items[1].element)
                 //   $('#gridItems').jqxGrid('hidecolumn', 'order_sub_name')
                //    $('#gridItems').jqxGrid('hidecolumn', 'order_qty_unit')
                //    $('#gridItems').jqxGrid('hidecolumn','order_price')
                $('#btnSelectItems').hide()
                $('#gridItems').jqxGrid('hidecolumn', 'sys_check')
            }
                //var contextMenu = $("#jqxMenu").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' })
                //$('#other_class').on('mousedown', function (e) {
                //    if (e.which == 3 && e.target !== this) {
                //        var scrollTop = $(window).scrollTop();
                //        var scrollLeft = $(window).scrollLeft();
                //        contextMenu.jqxMenu('open', parseInt(event.clientX) + 5 + scrollLeft, parseInt(event.clientY) + 5 + scrollTop);
                //    }
                //    return false;
                //});

                //$('#other_class').on('contextmenu', function (e) {
                //    return false;
                //});


            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                if (args.datafield == "n") {
                    if (args.originalEvent.button == 2) return;
                  //  var item_id = args.row.bounddata.i;
                    /*
                    if (ForSelect) {

                      //  var rows = $('#gridItems').jqxGrid('getrows')
                        var checkedRows = []
                        var row = args.row.bounddata
                       checkedRows.push({ item_id: row.i, item_name: row.n, order_sub_id: row.order_sub_id, order_sub_name: row.order_sub_name, order_price: row.order_price, unit_factor: row.unit_factor, order_qty: row.order_qty, order_qty_unit: row.order_qty_unit })

                        var msg = {
                            msgHead: 'ItemsView', action: 'selectMulti', checkedRows: checkedRows
                        }


                        window.parent.postMessage(msg, '*');
                        }
                        else {
                            onGridRowEdit(args.rowindex);
                           //$('#popItem').jqxWindow('open');
                          // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                        }*/
                }

            });
    	    $("#Cancel").on('click', function () {
    	        for (var i = 0; i < 10; i++) {
    	            $('#jqxgrid').jqxGrid('deleterow', i);
    	            $('#jqxgrid').jqxGrid('addrow', i, {})
    	        }
    	    });

                $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 560, width: 750, theme: 'summer', autoOpen: false, showCloseButton: true,closeButtonSize:32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType:'fade'});
                $("#popClass").jqxWindow({ isModal: true, modalOpacity:0.3, height: 320, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true,closeButtonSize:32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });


            attachContextMenu();

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
            });

            $(document).on('click', function (e) {

                var contextMenu = e.target.innerText
                var ee = $('#popSet').css("display")
                if (contextMenu == "全部" && ee == "block") {
                    bw.toast("请勿将商品类别设置为'全部'");
                }

            });
            QueryData();
        });
        function onGridRowContextMenuClick(gridID, menuID, rowIndex) {

            var rows = $('#gridItems').jqxGrid('getrows')
            var row = rows[rowIndex]
            if (menuID == 'BatchOperation') {
                $('#gridItems').jqxGrid('showcolumn', 'sys_check')
                var a = $('#popBatchOperation')
                $('#popBatchOperation').css("display","block")

            }

        }
        
         window.g_batchSetFld=''
         function popBatchSetDlg(fld) {

           
            
            window.g_batchSetFld=fld
            if(fld=="item_class")  
            {
                
                $('#set_head').append( '类别设置');
                $('#popBatchOperation').css("display", "none")
                $('#popSet').css("display", "block")
                $('#div_set').append('<label  style="line-height: 32px;">类别</label> ');
                $('#div_set').append('<div id = "set" > </div>');
                window.g_pageSetting.dataItems.push({ datafield: 'item_class', text: '类别', origText: '类别', alwaysShow: false, hidden: false, visible: true });
                $('#set').jqxDropDownTree({ dropDownWidth: 200, dropDownHeight: 250, url: '../api/ItemEdit/GetDataItemOptions?operKey= '+g_operKey + '&dataItemName=item_class', source: null, checkboxes: false, mumSelectable: true});          
                $('#set').jqxInput('val', { value: ``, label: `` });         

            }
             else if (fld == "item_brand") {

                 $('#set_head').append('品牌设置');
                 $('#popBatchOperation').css("display", "none")
                 $('#popSet').css("display", "block")
                $('#div_set').append('<label  style="line-height: 32px;">品牌</label> ');
                 $('#div_set').append('<div id = "set" > </div>');
                window.g_pageSetting.dataItems.push({ datafield: 'item_brand', text: '品牌', origText: '品牌', alwaysShow: false, hidden: false, visible: true });
                $('#set').jqxDropDownTree({ dropDownWidth: 200, dropDownHeight: 250, url: '../api/ItemEdit/GetDataItemOptions?operKey= ' + g_operKey + '&dataItemName=item_brand', source: null, checkboxes: false, mumSelectable: true });
                 $('#set').jqxInput('val', { value: ``, label: `` });

             }
            else if(fld=="status")
            {
                $('#set_head').append('状态设置');
                $('#popBatchOperation').css("display", "none")
                $('#popSet').css("display", "block")
                $('#div_set').append('<label   style="line-height: 32px;"  >状态</label>');
                $('#div_set').append('<div > <select id="set" style="width: 200px;"><option >正常</option> <option >停用</option></select></div>');
            }
            else if (fld == "delete") {
                 $('#set_head').append('删除设置');
                  $('#popBatchOperation').css("display", "none")
                $('#popSet').css("display", "block")
                  $('#div_set').append('<label   style="line-height: 32px;"  >范围</label>');
                 $('#div_set').append('<div > <select id="set" style="width: 200px;"><option >勾选</option> </select></div>');


           }
        }
    
   
        

         function btnClose_Clicked() {
            $('#popSet').css("display", "none")
            $('#div_set').empty()
            $('#set_head').empty()            
        }
        function btnSave_Clicked() {
            var rows = []
            
            for (var id in window.g_checkedRows) {

                rows.push(parseInt(window.g_checkedRows[id].i))
                
            }
             if (rows.length == 0) {
                    bw.toast("未做勾选");
                }
  
            var url = ''
            var params = {}
           
              if (g_batchSetFld == 'item_class') {
              
                 item_class = $('#set').val().value        
                 other_class = $('#set').jqxDropDownTree('treePath')
                 params = {item_class:item_class, other_class:other_class}
                 url='../api/itemsView/BatchSetClass'


              }
            else if (g_batchSetFld == 'item_brand') {

                item_brand = $('#set').val().value
                  params = { item_brand: item_brand }
                url = '../api/itemsView/BatchSetBrand'


            }
              else if (g_batchSetFld == 'status') {
                  status = $('#set').val()
                  params = {status:status}
                 url='../api/itemsView/BatchSetStatus'
              }
              else if (g_batchSetFld == 'delete') {
                 type = $('#set').val()
                 params = {type:type}
                 url='../api/itemsView/BatchDelete'

             }
            params.operKey = '@Model.OperKey'
            params.rows = rows
            $.ajaxSetup({ contentType: "application/json" });
            $.post(url, JSON.stringify(params)).then(
                function (data) {
                 if (data.result == 'OK') {
                     bw.toast('操作成功');
                     $('#popSet').css("display", "none")
                     $('#div_set').empty()
                     $('#set_head').empty()
                     QueryData() 
                 } else {
                     bw.toast(data.msg);
                 }
             })
        }



        function beforeQuery() {
             
            var item = $('#other_class').jqxTree('getSelectedItem')
            var often = $('#other_class').find('li:first-child')
            var isOften = false
            if (item == null && window.ForSelect && getSupcustID()) {
                isOften = true
                $('#other_class').jqxTree('selectItem', often[1]);

            }
            if (item && item.value == 'often') {
                isOften = true
            }
            $('#btnAddItem').attr('disabled',isOften)
            $('#gridItems').jqxGrid('_getcolumn', 'order_sub_name').hidden = !isOften
            $('#gridItems').jqxGrid('_getcolumn', 'order_price').hidden = !isOften
            $('#gridItems').jqxGrid('_getcolumn', 'order_balance').hidden = !isOften
            $('#gridItems').jqxGrid('_getcolumn', 'order_qty_unit').hidden = !isOften


        }

        function getSupcustID() {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == "supcust_id") return true;
            }
            return (false);
        }

        function onSearchStrInput() {
            var value = $('#other_class').jqxTree('val')
            if (value && value.value == 'often') {
                var div = $('#other_class').find('div:contains(全部)');
                if(div.length){
                    var li=div[0].parentNode
                    $('#other_class').jqxTree('selectItem',li)
                }
            }
            
        }

        function qtyInputRender(row, column, value, p4, p5, rowData) {
            if(!rowData) return
            var s = ''
             var k=funcGetRowKeyForCheck(rowData)
            var checkRow=window.g_checkedRows[k] 
            if (checkRow) {
                rowData.b_qty = checkRow.b_qty
                rowData.m_qty = checkRow.m_qty
                rowData.s_qty = checkRow.s_qty 
            }
            if (rowData.b_qty) {
                s+=rowData.b_qty+rowData.b_unit_no
            }
            if (rowData.m_qty) {
                s+=rowData.m_qty+rowData.m_unit_no
            }
            if (rowData.s_qty) {
                s+=rowData.s_qty+rowData.s_unit_no
            }
            var html = `<div style="height:100%;display:flex;align-items:center;justify-content:flex-end;"><label style="margin-right:3px;">${s}</label></div>`
           
            //var html = `<div style="height:100%;display:flex;align-items:center;justify-content:flex-end;"><label>${value}</label><label style="margin-right:3px;">${unit_no || ''}</label></div>`
            return html;
        }
        function qtyInputCreateEditor(row, cellvalue, editor, cellText, width, height) {
            var rowData = $('#gridItems').jqxGrid('getrowdata', row)
            if(!rowData) return
            var s=''
            //if (rowData.b_unit_no) {
                //<label>${rowData.b_unit_no}
                //placeholder="${rowData.b_unit_no}"
                s += `<input id='b_qty'  onkeydown="onQtyInputKeyDown(event)" style="width:25px;border:none;outline:none;" autocomplete="off" /> </label>`
          //  }
           // if (rowData.m_unit_no) {
                s += `<input id='m_qty'  onkeydown="onQtyInputKeyDown(event)" style="width:25px;border:none;outline:none;margin-left:2px;" autocomplete="off"/>`
           // }
            s += `<input id='s_qty' style="width:25px;border:none;outline:none;margin-left:2px;" autocomplete="off"/>`
            s=`<div style="text-align:right;font-size:9px;width:100%;display:flex;justify-content:flex-end;align-items:center;">
                    ${s}
               </div>`
             var element = $(s);
             editor.append(element);
        }
        function onQtyInputKeyDown(event) {
            
            if(event.keyCode!=13) return
            var input = event.target
            var nextInput=null
            if (input.id == 'b_qty') {
                nextInput = $(input.parentNode).find('#m_qty:visible') 
            }
            if (nextInput.length==0)
            { 
                nextInput = $(input.parentNode).find('#s_qty')  
            }
            if (nextInput.length) {
                nextInput.focus()
                event.stopPropagation()
            }
        }
        function qtyInputInitEditor(row, cellvalue, editor, celltext, pressedkey) {
            var rowData = $('#gridItems').jqxGrid('getrowdata', row)
            if(!rowData) return
            var b_input = editor.find('#b_qty');
            var m_input = editor.find('#m_qty');
            var s_input = editor.find('#s_qty');
            var attrs=rowData.mum_attributes
            if(attrs) attrs=JSON.parse(attrs)
            var stockAttr=attrs?attrs.find(attr=>attr.distinctStock):null
        
            b_input.css('display',rowData.b_unit_no && !stockAttr ?'block':'none')
            m_input.css('display',rowData.m_unit_no && !stockAttr ?'block':'none')
            s_input.css('display',rowData.s_unit_no && !stockAttr ?'block':'none')
            b_input.attr('placeholder',rowData.b_unit_no)
            m_input.attr('placeholder',rowData.m_unit_no)
            s_input.attr('placeholder',rowData.s_unit_no) 

            b_input.val(rowData.b_qty||'');
            m_input.val(rowData.m_qty||'');
            s_input.val(rowData.s_qty || '');
            if (pressedkey) {
                if (b_input && rowData.b_unit_no) {
                   b_input.val(pressedkey);
                }
                else{
                   s_input.val(pressedkey);
                }
            }
            
             
            setTimeout(function () {
                if (b_input.length && rowData.b_unit_no) b_input.focus()
                else  s_input.focus()
             }, 100)
        }
        function qtyInputGetEditorValue(row, cellvalue, editor) {
            var rowData = $('#gridItems').jqxGrid('getrowdata', row)
            if(!rowData) return
             var b_input = editor.find('#b_qty');
             var m_input = editor.find('#m_qty');
            var s_input = editor.find('#s_qty');
            if (b_input) rowData.b_qty = b_input.val()
            if (m_input) rowData.m_qty = m_input.val()
            if (s_input) rowData.s_qty = s_input.val()
            if (rowData.b_qty || rowData.m_qty || rowData.s_qty) {
                var k=funcGetRowKeyForCheck(rowData)
                window.g_checkedRows[k]=rowData
            }
            return '';
        }
       

    </script>
    <style>
        .margin {
            margin-left: 20px;
        }
        .label_name {
            line-height: 32px;
            margin-left: 10px;
        }

        .label_content {
            width: 120px;
            height: 30px;
            margin-left: 10px;
        }
        #searchString {
            font-size: 14px; 
            border-color: #d2d2d2;
            border-width: 0.5px;
            border-style: none;
            border-bottom-style: solid;
            width: 120px;
            height: 25px;
            outline: none;
            margin-top:2px;
        }
        input::-webkit-input-placeholder { /* WebKit browsers */
            color: #ddd;
        }  
        #popMultiItems span:hover {
            cursor: pointer;
            color:#4499ff;
        }

        .shadow {
            position: relative;
            max-width: 270px;
            box-shadow: 0px 1px 4px rgba(0,0,0,0.3), 0px 0px 20px rgba(0,0,0,0.1) inset;
        }
        #popBatchOperation {
            width: 100px;
            height: 200px;
            position: fixed;
            top: 25%;
            left: 40%;
            z-index: 999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            display: none;
            text-align: center;
            cursor: pointer;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
        }



        #popSet {
            width: 500px;
            height: 300px;
            position: fixed;
            top: 27%;
            left: 30%;
            z-index: 999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            padding: 5px;
            display: none;
            text-align: center;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
        }
        #div_item_class {
            display: none;
            margin-left: 123px;
            margin-top: 70px;
        }
        #item_class {
            width: 200px;
            margin-left: 20px
        }
        
        #close{
            height: 16px;
            width: 20px; 
            cursor: pointer;
            position: relative;
            left: 43px;
            top: 0px;
        }
        #close:hover{
             background: #ddd;
        }
        .magic{
            width: 100px;
            height: 25px;
        }

        .magic:hover {
            background: #ddd;
        }  
        #div_close{
        height: 16px;
        width: 20px; 
        cursor: pointer;
        position: relative;
        left: 43px;
        top: 0px;
        }
        #div_close:hover{
             background: #ddd;
        }
        #div_set {
            display: none;
            margin-left: 123px;
            margin-top: 70px;
            display: flex;
            text-align: center;
        }
      
   
        
    </style>

</head>
 
<body style="overflow:hidden;">
   
    <div id="popBatchOperation">
        <svg id="div_close" onclick=" this.parentNode.style.display = 'none'">
            <use xlink:href="/images/images.svg?v=@Html.Raw(Model.Version)#close" />
        </svg>



        <div class="magic " onclick="popBatchSetDlg('item_class')">设置类别</div>
        <div class="magic " onclick="popBatchSetDlg('item_brand')">设置品牌</div>
        <div class="magic " onclick="popBatchSetDlg('status')">设置状态</div>
        <div class="magic " onclick="popBatchSetDlg('delete')">批量删除</div>
    </div>
     <div id="popSet" style="display: none;">
        <div style="height:30px;background-color:#fff; text-align:center;">
        <span id="set_head"  style="font-size:18px;"></span>
        </div>
        <div id="div_set" >
           
        </div>

        <div style="overflow:hidden;">
            <button onclick="btnSave_Clicked()" style="align-content:center;margin-top:45px;margin-left:20px;">确认</button>
            <button onclick="btnClose_Clicked();" style="align-content:center;margin-top:45px;margin-left:75px">关闭</button>
        </div>
    </div>
    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;">
        <div style="display:inherit">
            <div style="display: flex; width: 130px;"><label class="label_name">品牌</label> <div id="item_brand" style="width:70px;" class="label_content"></div></div>
            <div style="display: flex;width:130px;"><label class="label_name">状态</label> <div id="status" style="width:70px;" class="label_content"></div></div>
            <div style="display: flex;width:250px;"><label class="label_name">电商编号设置状态</label> <div id="has_emart_id" style="width:70px;" class="label_content"></div></div>
            <input id="searchString" oninput="onSearchStrInput()" autocomplete="off" class="margin" placeholder="简拼/名称/编号" />
            @if (Model.BranchID!="")
            {
               <div style="margin-left: 10px;"><input class="magic-checkbox" type="checkbox" id="showHasStock"  /><label for="showHasStock">有库存</label></div>
            }           
            <button onclick="QueryData()" class="margin">查询</button>
            <button id="btnSelectItems" class="margin" onclick="btnSelectItems_click()">选择</button>
        </div>

        <div>
            <button id="btnAddItem" onclick="btnAddItem_click()" class="margin">添加</button>
            @if (!Model.ForSelect)
            {
                <button id="btnExport" onclick="ExportExcel()" class="margin">导出</button>
            }
        </div>


    </div>
    <div style="display:flex; flex-grow: 1;height:100%;">
        <!--如果不加height:100%在浏览器正常，客户端实际高度为0-->
        <div id='other_class' style="width:200px;height:calc(100% - 20px);margin-top:20px;margin-bottom:2px;overflow-y:scroll">
        </div>

        <div style="width:calc(100% - 200px);height:100%; margin-left:10px;">

            <div>
                <div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div>
            </div>

            <div id="gridItems" style="margin-top:0px;margin-bottom:2px;width:calc(100% - 10px);height:calc(100% - 20px);"></div>

        </div>


    </div>
    <div style="display:flex;height:20px;width:100%;margin-bottom:0px;"></div>


    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">商品档案</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popClass" style="display: none;">
        <div style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">商品类别</span></div>
        <div style="overflow:hidden;"> </div>
    </div>


    <div id='jqxMenu_1' style="display:none;">
        <ul>
            <li id="mnuEditClass" onclick="btnEditClass_click()">编辑类</li>
            <li id="mnuAddClass" onclick="btnAddClass_click()">添加下级类</li>
            <li id="mnuRemoveClass" onclick="btnRemoveClass_click()">删除类</li>
        </ul>
    </div>

    <div id='jqxMenu_0' style="display:none;">
        <ul>
            <li id="mnuAddClass" onclick="btnAddClass_click()">添加下级类</li>
        </ul>
    </div>

</body>
</html>