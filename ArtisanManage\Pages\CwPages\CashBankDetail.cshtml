﻿@page
@model ArtisanManage.Pages.CwPages.CashBankDetailModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var bizStartPeriod = '@Html.Raw(Model.BizStartPeriod)';
        bizStartPeriod = new Date(`${bizStartPeriod} 00:00:00`);
        var newCount = 1;
        var itemSource = {};
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            if (bizStartPeriod && !isNaN(bizStartPeriod.getTime())) $("#startDay").jqxDateTimeInput('setMinDate', bizStartPeriod);

            $('#time_type .row-oper').remove();
            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                console.log(args.row);
                if (args.datafield == "sheet_no") {
                    let sheet_id = args.row.bounddata.sheet_id;
                    let sheet_type = args.row.bounddata.sheet_type;
                    voucherClickSheet(event, sheet_id, sheet_type);
                }
                if(args.datafield == "voucher_no"){
                    let voucher_id = args.row.bounddata.voucher_id;
                    window.parent.newTabPage('会计凭证', `CwPages/CwVoucher?sheet_id=${voucher_id}`);
                }
            });

            QueryData();
        });

        function beforeQuery() {
            let startDay = $('#startDay').jqxDateTimeInput('getDate');
            if (new Date(startDay) < bizStartPeriod) {
                $('#startDay ').jqxDateTimeInput('setDate', bizStartPeriod);
            }
        }

        function voucherClickSheet(e, biz_sheet_id, biz_sheet_type) {
            let sheet_name = '';
            let url = '';
            switch (biz_sheet_type) {
                case 'X':
                    sheet_name = '销售单';
                    url = `Sheets/SaleSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'D':
                    sheet_name = '销售单';
                    url = `Sheets/SaleSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'T':
                    sheet_name = '退货单';
                    url = `Sheets/SaleSheet?forReturn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'CG':
                    sheet_name = '采购单';
                    url = `Sheets/BuySheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'CT':
                    sheet_name = '采购退货单';
                    url = `Sheets/BuySheet?forReturn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'SK':
                    sheet_name = '收款单';
                    url = `Sheets/GetArrearsSheet?forPayOrGet=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'FK':
                    sheet_name = '付款单';
                    url = `Sheets/GetArrearsSheet?forPayOrGet=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'YS':
                    sheet_name = '预收款单';
                    url = `Sheets/PrepaySheet?forPayOrGet=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'YF':
                    sheet_name = '预付款单';
                    url = `Sheets/PrepaySheet?forPayOrGet=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'DH':
                    sheet_name = '定货会';
                    url = `Sheets/OrderItemSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'ZC':
                    sheet_name = '费用支出单';
                    url = `Sheets/FeeOutSheet?forOutOrIn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'SR':
                    sheet_name = '其他收入单';
                    url = `Sheets/FeeOutSheet?forOutOrIn=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'TR':
                    sheet_name = '转账单';
                    url = `CwPages/CashBankTransferSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'DK':
                     sheet_name = '贷款单';
                     url = `Sheets/LoanSheet?sheet_id=${biz_sheet_id}`;
                     break;
                 case 'HDK':
                     sheet_name = '还贷款单';
                     url = `Sheets/RepaySheet?sheet_id=${biz_sheet_id}&sheetType=HDK&installment_no=0`;
                     break;
                 
                 break;
             }
            window.parent.newTabPage(sheet_name, url, window);
        }

    </script>
</head>

<body>

    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <div style="position:absolute; right:20px;">
            <button onclick="QueryData()">查询</button>
            <button id="btnExport" onclick="ExportExcel()">导出</button>
        </div>
    </div>


    <div id="gridItems" style="margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 95px);"></div>
    <div id="divRowCount"><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>



</body>
</html>