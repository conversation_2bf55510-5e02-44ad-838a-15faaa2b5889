﻿using ArtisanManage.Models;
using ArtisanManage.MyCW;
using ArtisanManage.Services;
using ArtisanManage.Pages.CwPages;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using NPOI.XWPF.UserModel;
using Org.BouncyCastle.Pqc.Crypto.Saber;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Threading.Tasks;
using static ArtisanManage.Services.CommonTool;

namespace ArtisanManage.MyJXC
{
    public enum SHEET_PREPAY
    {
        EMPTY,
        NOT_PREPAY,
        IS_PREPAY
    }
    public class SheetPrepay<TROW>: SheetBase<TROW> where TROW:SheetRowBase,new()
    { 
        [SaveToDB] [FromFld] public string visit_id { get; set; } = "";
        //[SaveToDB] [FromFld] public string sheet_attribute { get; set; } = "";

        public Dictionary<string, string> OtherSheetAttributes = null;
        public string appendix_photos  { get; set; } = "";
        public string bindSheetInfo { get; set; } = "";
        [SaveToDB]
        [FromFld]
        public virtual string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                if (appendix_photos != "")
                {
                    // 附件添加
                    sheetAttribute.Add("appendixPhotos", appendix_photos);
                }
                if (!string.IsNullOrEmpty(bindSheetInfo))
                {
                    sheetAttribute.Add("bindSheetInfo", bindSheetInfo);
                }
                if (total_amount - paid_amount - disc_amount >= 0.1m) sheetAttribute.Add("arrears", "true");
                if (disc_amount > 0 && !sheetAttribute.ContainsKey("disc")) sheetAttribute.Add("disc", "true");
                if (OtherSheetAttributes != null)
                {
                    foreach (var k in OtherSheetAttributes)
                    {
                        if (!sheetAttribute.ContainsKey(k.Key))
                            sheetAttribute.Add(k.Key, k.Value);
                    }
                }

                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.appendixPhotos != null)
                    {
                        this.appendix_photos = sheetAttr.appendixPhotos;
                    }
                    if (sheetAttr.bindSheetInfo != null)
                    {
                        this.bindSheetInfo = sheetAttr.bindSheetInfo;
                    }

                }
            }
        }

        [SaveToDB][FromFld] public string order_source { get; set; } = "";
        [SaveToDB] [FromFld] public override string is_imported { get; set; } = "";
        [SaveToDB] [FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB] [FromFld] public string prepay_sub_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string prepay_sub_name { get; set; } = "";
        [SaveToDB] [FromFld] public string supcust_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_name { get; set; } = "";
        [SaveToDB] [FromFld] public override decimal total_amount { get; set; } = 0;
        [SaveToDB] [FromFld] public override int money_inout_flag { get; set; }
        [SaveToDB] [FromFld] public string payway1_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_name { get; set; } = "";
        [SaveToDB] [FromFld] public decimal payway1_amount { get; set; }
        [SaveToDB] [FromFld] public string payway2_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_name { get; set; } = "";
        [SaveToDB] [FromFld] public decimal payway2_amount { get; set; }
        [SaveToDB][FromFld] public string payway3_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway3_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway3_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_disc_amount { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string mobile { get; set; } = "";

        public decimal no_disc_amount { get { return total_amount - now_disc_amount; } }
        public decimal left_amount { get { return CPubVars.ToDecimal(prepay_sub_id=="-1"?"0": CPubVars.FormatMoney(total_amount - now_disc_amount - now_pay_amount, 2)); } }//这里需要保留,和leftAmount有区别，这里是当次欠款
        //public decimal left_amount { get { return CPubVars.ToDecimal(CPubVars.FormatMoney(total_amount - now_disc_amount - now_pay_amount, 2)); } }//这里需要保留,和leftAmount有区别，这里是当次欠款
        //public decimal LeftAmount { get { return total_amount - paid_amount - disc_amount; } }

        public decimal LeftAmount { get { return prepay_sub_id == "-1" ? 0 : (total_amount - paid_amount - disc_amount); } }

        [SaveToDB] [FromFld] public decimal paid_amount { get; set; }
        [SaveToDB] [FromFld] public decimal disc_amount { get; set; }
        [SaveToDB] [FromFld] public string getter_id { get; set; } = "";
        [SaveToDB] [FromFld] public string order_adjust_sheet_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string order_adjust_sheet_no { get; set; } = "";
        //[SaveToDB] [FromFld] public string make_brief { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string getter_name { get; set; } = "";
		[SaveToDB][FromFld] public string settle_time { get; set; } = "";

		public SheetPrepay(string mainTable, string detailTable, LOAD_PURPOSE loadPurpose) : base(mainTable, detailTable, loadPurpose)
        {
            
        }
        [JsonConstructor]
        public SheetPrepay(SHEET_PREPAY sheetPrepay,LOAD_PURPOSE loadPurpose) : base("sheet_prepay","",loadPurpose)
        {

            sheet_type = sheetPrepay == SHEET_PREPAY.NOT_PREPAY? SHEET_TYPE.SHEET_PRE_GET_MONEY : SHEET_TYPE.SHEET_PRE_PAY_MONEY;
            ConstructFun(); 

        }
      
        private void ConstructFun()
        {
            if (LoadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @" left join info_supcust c on t.supcust_id=c.supcust_id
                                  left join (select oper_id,oper_name as getter_name from info_operator) getter on t.getter_id=getter.oper_id
                                  left join (select oper_id,oper_name as maker_name from info_operator) maker on t.maker_id=maker.oper_id
                                  left join  (select oper_id,oper_name as approver_name from info_operator) approver on t.approver_id=approver.oper_id
                                  left join (select sub_id,sub_name as payway1_name from cw_subject) pw1 on t.payway1_id=pw1.sub_id
                                  left join (select sub_id,sub_name as payway2_name from cw_subject) pw2 on t.payway2_id=pw2.sub_id
                                  left join (select sub_id,sub_name as payway3_name from cw_subject) pw3 on t.payway3_id=pw3.sub_id
                                  left join (select sub_id,sub_name as prepay_sub_name from cw_subject) pre on t.prepay_sub_id=pre.sub_id
                                  left join (select sheet_id as order_adjust_sheet_id,sheet_no as order_adjust_sheet_no from sheet_item_ordered_adjust_main) om on om.order_adjust_sheet_id=t.order_adjust_sheet_id
              ";
            }
            DetailLeftJoin = "";
        }
     
        public SheetPrepay() : base("sheet_prepay", "", LOAD_PURPOSE.APPROVE)
        {
            sheet_type =  SHEET_TYPE.SHEET_PRE_GET_MONEY;
            ConstructFun();
        }
        public SheetPrepay(LOAD_PURPOSE loadPurpose) : base("sheet_prepay", "", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_PRE_GET_MONEY;
            ConstructFun();
        }
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.supcust_id + "_" + this.total_amount.ToString() + "_" + this.payway1_id+ "_" + this.payway2_id + "_" + this.payway3_id + "_" +  this.make_brief;
            return res;
        }

        protected override void InitForSave()
        {
            base.InitForSave();
            if (getter_id == "") getter_id = OperID;
            if (maker_id == "") maker_id = OperID;
            if (approver_id == "") approver_id = OperID;
            paid_amount = now_pay_amount;
            disc_amount = now_disc_amount;
        }
        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            if (supcust_id == "") return "必须指定往来单位";
            if (getter_id == "" && IsFromWeb) return "必须指定业务员";
            if (now_pay_amount > 0 && payway1_id == "") return "必须指定支付方式";
            if (prepay_sub_id == "") return "必须指定账户";

            if (Math.Abs(now_pay_amount - (payway1_amount + payway2_amount + payway3_amount)) > 0.05m)
            {
                return "支付方式合计与总支付金额不一致";
            }

            //if ((total_amount - now_pay_amount - now_disc_amount) >= 1) return "预收款暂不支持欠款,请重新输入支付金额";

            return "OK";
        }
        protected override async Task<string> CheckForRed(CMySbCommand cmd)
        {
            // 检查欠条
            if (SheetType == "YS")
            {
                string billSheetSql = @$"select sheet_no from sheet_move_arrears_bill_main m left join
                    sheet_move_arrears_bill_detail d on d.company_id = m.company_id and d.sheet_id = m.sheet_id
                    where m.company_id = {company_id} and business_sheet_id = {sheet_id} and business_sheet_type = '{SheetType}'and approve_time is not null and red_flag is null
                    order by m.sheet_id desc nulls last";
                dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(billSheetSql, cmd);
                if (ret != null)
                {
                    return $"请红冲包含关联欠条的单据【{ret.sheet_no}】";
                };
            }

            return await CheckForRed_MoneySheet(cmd);
        }
        public class CInfoForApprove : CInfoForApproveBase
        {
            public string PrepayBalance = "";
            public decimal ChangeBal = 0;
            public string PrepayTotalBalance = "";
            public List<Subject> PaywaysInfo = new List<Subject>();
            //public decimal Balance = 0;
            //public bool BalanceExist = false;


        }

        public class Subject
        {
            public string sub_id { get; set; }
            public string sub_name { get; set; }
            public string sub_type { get; set; }
        }

        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            string sql;
            if (left_amount!=0)
            {
                sql = GetSqlForArrearsQQ(supcust_id, getter_id);
                QQ.Enqueue("arrear_balance", sql);
            }

            sql = $"select sub_id, balance from prepay_balance where company_id={company_id} and supcust_id={supcust_id};";
            QQ.Enqueue("prepay_balance", sql);
            string sub_ids = payway1_id;
            if (payway2_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway2_id;
            }
            if (payway3_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway3_id;
            }
            if (sub_ids != "")
            {
                sql = $"select sub_id, sub_name, sub_type from cw_subject where company_id={company_id} and sub_id in ({sub_ids});";
                QQ.Enqueue("payway_type", sql);
            }
            SetQQForWeChatInfo(QQ, supcust_id);
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (sqlName == "arrear_balance")
            {
                DealArrearReadQQ(dr, info, left_amount, supcust_id);
                
            }
            else if (sqlName == "prepay_balance")
            {
                decimal prepayTotalBalance = 0;
                var records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach(dynamic rec in records)
				{
					if (rec.sub_id == prepay_sub_id)
					{
                        info.PrepayBalance = rec.balance;
                    }
                    prepayTotalBalance += CPubVars.ToDecimal(rec.balance);
                }
                info.PrepayTotalBalance =  CPubVars.FormatMoney(prepayTotalBalance,2);
                
            }

            else if (sqlName == "payway_type")
            {
                info.PaywaysInfo = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
            ReadQQDataForWeChatInfo(sqlName, dr, info);
        }
        protected override void NeedUpdateClientHistory(out string supcustID, out bool updateArrears, out string updatePrepaySubIDs)
        {
            supcustID = supcust_id;
            updateArrears = true;
            updatePrepaySubIDs = prepay_sub_id;
        }


        public async Task ProcessPcAppendix()
        {
            string images = appendix_photos;
            if (!string.IsNullOrEmpty(images) && images != "[]" && images.Contains("photos"))
            {
                // 使用 Newtonsoft.Json 解析 JSON 字符串
                var jsonObject = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(images);

                // 提取 photos 列表
                List<string> photos = jsonObject["photos"];
                appendix_photos = await ProcessAppendixPicsRetDBStr(photos);
            }
        }
        public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, company_id);
            return result;
        }
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
           
            string sql = "";
            int flag = money_inout_flag;
            string redSql = "";
            string prepayDetailSql = "";

            string sRedFlag = "null";
            if (red_flag.IsValid()) sRedFlag = "'" + red_flag + "'";
              
            if (left_amount != 0)
            {
                sql += await GetSqlForArrearsChange(cmd,info, left_amount, getter_id);
            }


            if (Math.Abs(total_amount) >= 0 && prepay_sub_id!="-1")
            //if (Math.Abs(total_amount) >= 0)
            {
                int inoutFlag = 1;
                if (red_flag == "2") inoutFlag = -1;

              
                decimal nChangeBal = total_amount * inoutFlag;
                decimal bal = 0;
               
                if (info.PrepayBalance != "")
                { 
                    bal = CPubVars.ToDecimal(info.PrepayBalance);
                    
                    sql += @$"update prepay_balance set balance=balance+({nChangeBal}) where company_id={company_id} and supcust_id={supcust_id} and sub_id = {prepay_sub_id};";

                }
                else
                {
                    sql += @$"insert into prepay_balance (company_id  ,supcust_id  ,balance     ,sub_id) 
                                                  values ({company_id},{supcust_id},{nChangeBal},{prepay_sub_id}) 
                        on conflict(company_id,supcust_id,sub_id) do update set balance = prepay_balance.balance+({nChangeBal});
                    ; ";
                }
                bal += nChangeBal;

                decimal totalBal = 0m;
                if (info.PrepayTotalBalance != "")
				{
                    totalBal = CPubVars.ToDecimal(info.PrepayTotalBalance);
                }
                totalBal += nChangeBal;

                var sub_type = SheetType;
                if (SheetType == "DH" || SheetType == "DHTZ") sub_type = "YS";
                if (red_flag == "2")
                {
                    redSql += @$"update client_account_history set red_flag = '1' where company_id = {company_id} and sheet_id = {red_sheet_id} and sheet_type = '{SheetType}';";
                    prepayDetailSql += @$"update prepay_detail set red_flag = 2, balance = balance-init_balance where sheet_id = {red_sheet_id} and company_id = {company_id} and sheet_type = '{SheetType}';";
                }
                else
                {
                prepayDetailSql += @$"
insert into prepay_detail(supcust_id,  sheet_id,  sheet_no,     company_id,   sub_id,           sheet_type,   red_flag,   balance,       init_balance  ,  init_time,             remark)
                  values({supcust_id}, {sheet_id},'{sheet_no}', {company_id}, {prepay_sub_id}, '{SheetType}', {sRedFlag}, {total_amount}, {total_amount},'{this.approve_time}','{this.make_brief}');";
                }
                GetAccountHistoryHappenTimePrepayBalance(info, supcust_id, bal, totalBal, prepay_sub_id, out string balance, out string totalBalance);

                sql += @$"
insert into client_account_history(company_id ,happen_time                          ,approve_time                          ,sheet_type   ,sheet_id  ,change_amount,now_balance,now_prepay_balance,now_balance_happen_time,now_prepay_balance_happen_time,supcust_id  ,sub_id         ,sub_type    ,red_flag  )
                           values({company_id},'{CPubVars.GetDateText(happen_time)}','{CPubVars.GetDateText(approve_time)}','{SheetType}',{sheet_id},{nChangeBal} ,{bal}      ,{totalBal}        ,{balance},{totalBalance},{supcust_id},{prepay_sub_id},'{sub_type}',{sRedFlag});";
                if (!HappenNow)
                {
                    sql += $"update client_account_history set now_balance_happen_time=now_balance_happen_time+{nChangeBal} where company_id={company_id} and sub_type='{sub_type}' and sub_id={prepay_sub_id} and supcust_id={supcust_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                    sql += $"update client_account_history set now_prepay_balance_happen_time=now_prepay_balance_happen_time+{nChangeBal} where company_id={company_id} and sub_type='{sub_type}' and supcust_id={supcust_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                }

            }

            #region 更新现金银行余额
            string sql_cb = "";
            if (info.BizStartPeriod != "" && info.PaywaysInfo != null && !IsImported)
            {
                Dictionary<string, decimal> pws = new Dictionary<string, decimal>();
                Subject pw1 = info.PaywaysInfo.Find(p => p.sub_id == payway1_id && p.sub_type == "QT");
                if (pw1 != null && payway1_amount != 0)
                {
                    if (!pws.ContainsKey(payway1_id)) pws.Add(payway1_id, payway1_amount);
                    else pws[payway1_id] += payway1_amount;
                }
                Subject pw2 = info.PaywaysInfo.Find(p => p.sub_id == payway2_id && p.sub_type == "QT");
                if (pw2 != null && payway2_amount != 0)
                {
                    if (!pws.ContainsKey(payway2_id)) pws.Add(payway2_id, payway2_amount);
                    else pws[payway2_id] += payway2_amount;
                }
                Subject pw3 = info.PaywaysInfo.Find(p => p.sub_id == payway3_id && p.sub_type == "QT");
                if (pw2 != null && payway3_amount != 0)
                {
                    if (!pws.ContainsKey(payway3_id)) pws.Add(payway3_id, payway3_amount);
                    else pws[payway3_id] += payway3_amount;
                }
                if (pws.Count() > 0)
                {
                    sql_cb = base.UpdateCashBankBalance(pws);
                }
            }
            #endregion
            #region 存欠款单或删除欠款单（红冲）
            // 预收款单这里存的keeper_id是getter_id
            if (Math.Abs(this.left_amount) > 0 && !red_flag.IsValid() && this.SheetType == "YS")
            {
                string arrears_status = "no";
                if (Math.Abs(this.left_amount) < Math.Abs(this.total_amount)) arrears_status = "part";
                string insertArrearBillSql = $@"insert into arrears_bill 
    (company_id,business_sheet_type,business_sheet_id,business_sheet_no,supcust_id,supcust_name,orig_amount,left_amount,keeper_id,out_company,arrears_status)
values ({company_id},'{this.SheetType}',{this.sheet_id},'{this.sheet_no}',{this.supcust_id},'{this.sup_name}',{this.total_amount * this.money_inout_flag},{this.left_amount * this.money_inout_flag},{this.getter_id},true,'{arrears_status}')
returning bill_id";
                dynamic insertRet = await CDbDealer.Get1RecordFromSQLAsync(insertArrearBillSql, cmd);

            }
            else if (this.left_amount > 0 && red_flag.IsValid() && this.SheetType == "YS")
            {
                string billSql = @$"delete from arrears_bill where company_id = {company_id} and business_sheet_type = '{this.SheetType}'
            and business_sheet_id = {this.sheet_id} and business_sheet_no = '{this.sheet_no}' and supcust_id = {this.supcust_id}";
                cmd.CommandText = billSql;
                await cmd.ExecuteNonQueryAsync();
            }
            #endregion
            cmd.CommandText = redSql + sql + sql_cb + prepayDetailSql;
            if(cmd.CommandText!="")
                await cmd.ExecuteNonQueryAsync();

            #region 二级分销
//            if (!IsImported)
//            {
               
//                string rsSellerSql = "";
//                if (SheetType == "YF")
//                {
//                    // 预付款以供应商查看rs_seller
//                    rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync,rp.client_mapper FROM rs_seller rs
//LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
// WHERE rs.reseller_company_id = {company_id} and rs.supplier_id = {supcust_id};";
//                }
//                else
//                {
//                    // SheetType = "YS"
//                    // 预收款以客户查看
//                    string rsCompanySql = $"select company_id from info_operator where rs_client_id = {supcust_id}";
//                    dynamic rsCompanyInfo = await CDbDealer.Get1RecordFromSQLAsync(rsCompanySql, cmd);
//                    if (rsCompanyInfo != null)
//                    {
//                        rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync,rp.client_mapper FROM rs_seller rs
//LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
// WHERE rs.company_id = {company_id} and rs.reseller_company_id = {rsCompanyInfo.company_id};";
//                    }
//                    else
//                    {
//                        rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync,rp.client_mapper FROM rs_seller rs
//LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
// WHERE rs.company_id = {company_id} and rs.client_id = {supcust_id};";
//                    }
//                }
//                dynamic rsSeller = await CDbDealer.Get1RecordFromSQLAsync(rsSellerSql, cmd);
//                // string clientType = (string) rsSeller.client_mapper;
//                string orderSource = "";

//                if (sheet_id != "")
//                {
//                    string orderSourceSql = $"select  * from sheet_prepay where company_id = {company_id} and supcust_id = {supcust_id} and sheet_id ={sheet_id};";
//                    dynamic dbSheet = await CDbDealer.Get1RecordFromSQLAsync(orderSourceSql, cmd);
//                    orderSource = dbSheet.order_source;
//                }
//                if (orderSource == "") orderSource = order_source; 
//                if (rsSeller != null && rsSeller.sheet_sync.ToLower() == "true" && orderSource != "2fx")
//                { 
//                        string ret = ""; 
//                        ret = await SyncPrepay(cmd, this, SheetType);
//                        try
//                        {
//                            var ob = JsonConvert.DeserializeObject(ret);
//                        }
//                        catch (Exception ex)
//                        {
//                           info.ErrMsg=ex.Message;
//                        }
//                        bindSheetInfo = ret; 
                    
//                }
               

//            }
           #endregion

            if (this.SheetType == "YS")
            {
                if (info.WeChatInfo != null && info.WeChatInfo.Count > 0)
                {
                    await SendSheetSimple(cmd, _httpClientFactory,
                    info.WeChatInfo,
                    this.SheetType,
                    this.now_disc_amount.ToString(),
                    this.left_amount.ToString(),
                    this.payway1_name,
                    this.payway1_amount.ToString(),
                    this.payway2_name,
                    this.payway2_amount.ToString(),
                    this.payway3_name,
                    this.payway3_amount.ToString(), this.supcust_id);
                }
            }
        }
        public override string GetWeChatMsgHead()
        {
            string sheetTypeName = "";
            string first = "";
            switch (this.SheetType)
            {
                case "YS": sheetTypeName = "预收款单"; break;
            }
            switch (this.red_flag)
            {
                case "": first = $"您有新的【{sheetTypeName}】,来自【{this.company_name}】,请注意查收"; break;
                case "2": first = $"您的【{sheetTypeName}】【被红冲】,来自【{this.company_name}】,请注意查收"; break;
            }
            return first;
        }
        public override string GetWeChatMsgTail()
        {
            string remark = "";
            if (this.sheet_no != "") remark += "单据编号：" + this.sheet_no + "\n";
            return remark;
        }

        /*
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            string redSql = "";
            string sql = "";
            var sheetType = "";
            int inoutFlag = -1;
             

            CInfoForApprove info = (CInfoForApprove)info1;
            
            if (LeftAmount != 0)
            {
                decimal changeBal = LeftAmount * inoutFlag * (-1);
                decimal arrears = 0;
                if (info.ArrearBalance != "")   arrears = Convert.ToSingle(info.ArrearBalance);
                arrears += changeBal;
                if (red_flag.IsValid())
                {
                    redSql += @$"update client_account_history set red_flag = '1' where company_id = {company_id} and sheet_id = {red_sheet_id} and sheet_type = '{SheetType}' and sub_type = 'QK';";
                    sql += $"insert into client_account_history(company_id,happen_time,sheet_type,sheet_id,change_amount,now_balance,supcust_id,sub_type,red_flag) values ({company_id},'{CPubVars.GetDateText(approve_time)}','{SheetType}',{sheetID},{changeBal},{arrears},{supcust_id},'QK',{red_flag});";
                }
                if (red_flag.IsInvalid())
                {
                    sql += $"insert into client_account_history(company_id,happen_time,sheet_type,sheet_id,change_amount,now_balance,supcust_id,sub_type) values ({company_id},'{CPubVars.GetDateText(approve_time)}','{SheetType}',{sheetID},{changeBal},{arrears},{supcust_id},'QK');";
                }
            }
            if (Math.Abs(total_amount) > 0)
            {
                if (red_flag.IsValid())
                {
                    redSql += @$"update client_account_history set red_flag = '1' where company_id = {company_id} and sheet_id = {red_sheet_id} and sheet_type = '{SheetType}';";
                    sql += @$"insert into client_account_history(company_id, happen_time, sheet_type, sheet_id, change_amount, now_balance, supcust_id, sub_id, sub_type,red_flag)
                            values({ company_id},'{CPubVars.GetDateText(approve_time)}','{SheetType}',{ sheet_id},{ info.ChangeBal},{ info.Balance},{ supcust_id},{ prepay_sub_id},'{SheetType}',{red_flag});";
                }
                if (red_flag.IsInvalid())
                {
                    sql += @$"insert into client_account_history(company_id, happen_time, sheet_type, sheet_id, change_amount, now_balance, supcust_id, sub_id, sub_type)
                            values({ company_id},'{CPubVars.GetDateText(approve_time)}','{SheetType}',{ sheet_id},{ info.ChangeBal},{ info.Balance},{ supcust_id},{ prepay_sub_id},'{SheetType}');";
                }
            }
            if (info.AdjustSheetID != "")
            {
                string sheet_ids = sheetID + ",";
                sql += @$"update sheet_item_ordered_adjust_main set prepay_sheet_id=({sheet_ids}) where company_id={company_id} and supcust_id={supcust_id} and sheet_id={info.AdjustSheetID};";
            }
            cmd.CommandText = redSql+sql;
            await cmd.ExecuteNonQueryAsync(); 
        }
        */
        public async Task<string> DoCommonJobBeforeSaveApprove(CMySbCommand cmd)
        {
            if (!IsImported)
            {

                string rsSellerSql = "";
                if (SheetType == "YF")
                {
                    // 预付款以供应商查看rs_seller
                    rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync,rp.client_mapper FROM rs_seller rs
LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
 WHERE rs.reseller_company_id = {company_id} and rs.supplier_id = {supcust_id};";
                }
                else
                {
                    // SheetType = "YS"
                    // 预收款以客户查看
                    string rsCompanySql = $"select company_id from info_operator where rs_client_id = {supcust_id}";
                    dynamic rsCompanyInfo = await CDbDealer.Get1RecordFromSQLAsync(rsCompanySql, cmd);
                    if (rsCompanyInfo != null)
                    {
                        rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync,rp.client_mapper FROM rs_seller rs
LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
 WHERE rs.company_id = {company_id} and rs.reseller_company_id = {rsCompanyInfo.company_id};";
                    }
                    else
                    {
                        rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync,rp.client_mapper FROM rs_seller rs
LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
 WHERE rs.company_id = {company_id} and rs.client_id = {supcust_id};";
                    }
                }
                dynamic rsSeller = await CDbDealer.Get1RecordFromSQLAsync(rsSellerSql, cmd);
                // string clientType = (string) rsSeller.client_mapper;
                string orderSource = "";

                if (sheet_id != "")
                {
                    string orderSourceSql = $"select  * from sheet_prepay where company_id = {company_id} and supcust_id = {supcust_id} and sheet_id ={sheet_id};";
                    dynamic dbSheet = await CDbDealer.Get1RecordFromSQLAsync(orderSourceSql, cmd);
                    orderSource = dbSheet.order_source;
                }
                if (orderSource == "") orderSource = order_source;
                if (rsSeller != null && rsSeller.sheet_sync.ToLower() == "true" && orderSource != "2fx")
                {
                    dynamic ret = "";
                    ret = await SyncPrepay(cmd, this, SheetType);
                    if (ret.msg != "")
                    {
                        return ret.msg;
                    }
                    else
                    {
                        this.bindSheetInfo = JsonConvert.SerializeObject(ret.bindSheetInfo);
                    }

                }


            }
            return "";
        }

        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd, CInfoForApproveBase info)
        {
            string msg = await DoCommonJobBeforeSaveApprove(cmd);
            return msg;
        }
        public async Task<dynamic> SyncPrepay(CMySbCommand cmd, dynamic oriSheet, string sheetType)
        {
            string err = "";
            SheetPrepay<SheetRowBase> sheet = JsonConvert.DeserializeObject<SheetPrepay<SheetRowBase>>(JsonConvert.SerializeObject(oriSheet));
            sheet.Init();
            string operKey = sheet.OperKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            sheet.OperID = "1";
            sheet.SYNCHRONIZE_SHEETS = true;
            sheet.order_source = "2fx";
            sheet.make_brief = "分销自动同步单据";
            sheet.money_inout_flag = sheet.money_inout_flag * -1;
            dynamic resellerInfo = null;
            if (SheetType == "YF")
            {
                string resellerInfoSql = @$"select * from rs_seller  rss 
                    left join (select plan_id,client_mapper,auto_approve_reseller_sheet from rs_plan) rsp
                    on rss.plan_id = rsp.plan_id where reseller_company_id={companyID} and supplier_id = {oriSheet.supcust_id}";
                resellerInfo = await CDbDealer.Get1RecordFromSQLAsync(resellerInfoSql, cmd);

                string sellerId = sheet.getter_id;
                sheet.company_id = (string)resellerInfo.company_id;

                if ((string)resellerInfo.client_mapper == "sellerAsClient")
                {
                    string sellerClientSql = $"select rs_client_id from info_operator where oper_id = {sellerId}";
                    dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sellerClientSql, cmd);
                    // 第一种保护，业务员不在父账户客户列表，不允许开单
                    // if(record.rs_client_id == null || record.rs_client_id == "")
                    // {
                    //     err = "当前业务员不在父账户客户列表";
                    // }
                    
                    if (record.rs_client_id == null || record.rs_client_id == "")
                    {
                        // 第二种，开到子公司客户上
                        sheet.supcust_id = (string)resellerInfo.client_id;
                    }
                    else
                    {
                        sheet.supcust_id = (string)record.rs_client_id;
                    }
                    
                }
                else
                {
                    sheet.supcust_id = (string)resellerInfo.client_id;
                }
                sheet.company_id = (string)resellerInfo.company_id;
                sheet.sheet_type  = SHEET_TYPE.SHEET_PRE_GET_MONEY;
                sheet.SheetType = "YS";
                sheet.getter_id = "1";
                sheet.getter_name = "";
            }
            else
            {
                string rsCompanySql = $"select company_id,oper_id from info_operator where rs_client_id = {supcust_id}";
                dynamic rsCompanyInfo = await CDbDealer.Get1RecordFromSQLAsync(rsCompanySql, cmd);
                string resellerInfoSql = "";
                string operId = "";
                if (rsCompanyInfo != null)
                {
                    resellerInfoSql = @$"select * from rs_seller  rss 
                    left join (select plan_id,client_mapper,auto_approve_reseller_sheet from rs_plan) rsp on rss.plan_id = rsp.plan_id 
                    where company_id={companyID} and reseller_company_id = {rsCompanyInfo.company_id}";
                    operId = rsCompanyInfo.oper_id;
                    
                }
                else
                {
                    resellerInfoSql = @$"select * from rs_seller  rss 
                    left join (select plan_id,client_mapper,auto_approve_reseller_sheet from rs_plan) rsp on rss.plan_id = rsp.plan_id 
                    where company_id={companyID} and client_id = {oriSheet.supcust_id}";

                }
                resellerInfo = await CDbDealer.Get1RecordFromSQLAsync(resellerInfoSql, cmd);
                // TODO 客户 此处为厂家 supplier
                if ((string)resellerInfo.client_mapper == "sellerAsClient")
                {
                    // string sellerClientSql = $"select rs_seller_id from info_supcust where oper_id = {clientId}";
                    // dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sellerClientSql, cmd);
                    sheet.getter_id = operId;
                }
                else
                {
                    sheet.getter_id = "1";
                }
                sheet.company_id = (string)resellerInfo.reseller_company_id;
                sheet.supcust_id = (string)resellerInfo.supplier_id;
                sheet.sheet_type = SHEET_TYPE.SHEET_PRE_PAY_MONEY;
                sheet.SheetType = "YF";
            }

            sheet.sheet_id = "";
            sheet.sheet_no = "";
            sheet.maker_id = "1";
            sheet.maker_name = "";
            
            //sheet.payway1_id = "0";
            // 不知道为什么有这一行，有这一行没法审核通过？
           // sheet.payway1_amount = 0;
            //sheet.prepay_sub_id = "0";
            //sheet.prepay_sub_name = "";

            async Task<dynamic> getSonPayway(string payway_id, string payway_name)
            {
                if (payway_id == "") return new { sub_id = "", sub_name = "" };
                dynamic metSub = null;
                int metCount = 0;
                string sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={companyID} and sub_id={payway_id}";
                dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (rec != null)
                {
                    if (rec.sub_type == "YS")
                    {
                        sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={sheet.company_id} and sub_type ='YF'";
                        var prepaySubs = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);


                        foreach (dynamic sonSub in prepaySubs)
                        {
                            string subName = rec.sub_name;
                            string sonSubName = sonSub.sub_name;
                            // 要求主账号和分校账号设置一对命名相同的预收预付款如主账号：XX预收款 分销账号：XX预付款
                            if (subName.Replace("预收款", "").Replace("预收", "") == sonSubName.Replace("预付款", "").Replace("预付", ""))
                            // if (subName.Contains("预收") && sonSubName.Contains("预付"))
                            {
                                metSub = sonSub;
                                metCount++;
                            }
                        }
                        if (metCount != 1)
                        {
                            metSub = null;
                        }
                    }
                    else if (rec.sub_type == "YF")
                    {
                        sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={sheet.company_id} and sub_type ='YS'";
                        var prepaySubs = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);


                        foreach (dynamic sonSub in prepaySubs)
                        {
                            string subName = rec.sub_name;
                            string sonSubName = sonSub.sub_name;
                            // 要求主账号和分校账号设置一对命名相同的预收预付款如主账号：XX预收款 分销账号：XX预付款
                            if (subName.Replace("预付款", "").Replace("预付", "") == sonSubName.Replace("预收款", "").Replace("预收", ""))
                            // if (subName.Contains("预收") && sonSubName.Contains("预付"))
                            {
                                metSub = sonSub;
                                metCount++;
                            }
                        }
                        if (metCount != 1)
                        {
                            metSub = null;
                        }
                    }
                    else if (((string)rec.sub_name).Contains("现金"))
                    {
                        sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={sheet.company_id} and sub_name like '%现金%'";
                        metSub = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

                    }
                    else if (((string)rec.sub_name).Contains("微信支付"))
                    {
                        sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={sheet.company_id} and sub_name like '%微信支付%'";
                        metSub = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

                    }
                    else if (((string)rec.sub_name).Contains("支付宝"))
                    {
                        sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={sheet.company_id} and sub_name like '%支付宝%'";
                        metSub = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

                    }
                    else
                    {
                        sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={sheet.company_id} and sub_name like '%{rec.sub_name}%'";
                        metSub = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    }
                }
                if (metSub == null) metSub = new { sub_id = "", sub_name = "" };
                return metSub;
            }


            

            // 不清除的话同步单据也会被审核
            sheet.approver_id = "";
            sheet.approve_time = "";
            sheet.approve_brief = "";
            if (err == "")
            {
                dynamic payway = await getSonPayway(sheet.payway1_id, sheet.payway1_name);
                sheet.payway1_id = payway.sub_id; sheet.payway1_name = payway.sub_name;

                payway = await getSonPayway(sheet.payway2_id, sheet.payway2_name);
                sheet.payway2_id = payway.sub_id; sheet.payway2_name = payway.sub_name;

                payway = await getSonPayway(sheet.payway3_id, sheet.payway3_name);
                sheet.payway3_id = payway.sub_id; sheet.payway3_name = payway.sub_name;

                payway = await getSonPayway(sheet.prepay_sub_id, sheet.prepay_sub_name);
                sheet.prepay_sub_id = payway.sub_id; sheet.prepay_sub_name = payway.sub_name;

                if (sheet.payway1_id == "") sheet.payway1_id = "0";
                if (sheet.prepay_sub_id == "") sheet.prepay_sub_id = "0";
                //sheet.payway1_id = "0";
                // 不知道为什么有这一行，有这一行没法审核通过？
                // sheet.payway1_amount = 0;
                //sheet.prepay_sub_id = "0";
                //sheet.prepay_sub_name = "";
                string autoApprove = ((string)resellerInfo.auto_approve_reseller_sheet).ToLower();
                if ( autoApprove == "true" && SheetType == "YS")
                {
                    // 预收款单自动审核预付款单
                    

                    err = await sheet.SaveAndApprove(cmd, false);
                    if (err != "")
                    {
                        err = "审核分销商单据失败:" + err;
                    }
                }
                else
                {
                    err = await sheet.Save(cmd, false);
                    if (err != "")
                    {
                        err = "保存分销商单据失败:" + err;
                    }
                }

                // err = await sheet.Save(cmd, false);

                if (err != "") { return new {msg = "err" }; }
                var ret = new { msg = "",bindSheetInfo = new { bindSheetId = sheet.sheet_id, bindSheetNo = sheet.sheet_no, bindSheetType = sheet.SheetType, companyId = sheet.company_id, autoApprove = autoApprove } };

                // return JsonConvert.SerializeObject(ret);
                return ret;

            }

            return new { msg = err };
        }
		protected override Task<string> CheckSheetValid(CMySbCommand cmd)
		{
			if (Math.Abs(total_amount - now_disc_amount - now_pay_amount) < 0.05m)
			{
				if (this.happen_time.IsValid())
					settle_time = this.happen_time;
				else
					settle_time = CPubVars.GetDateText(DateTime.Now);

			}
			else settle_time = "";

			return base.CheckSheetValid(cmd);
		} 
        
        protected override async Task<string> BeforeRed(CMySbCommand cmd, string sheetID, string rederID,string redBrief, CInfoForApproveBase info)
        { 
            string sError = "";
            string sql = $"select order_adjust_sheet_id from {MainTable} where sheet_id={sheetID} and company_id = {this.company_id} ";
            cmd.CommandText = sql;
            dynamic origSheet = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (origSheet.order_adjust_sheet_id != "")
            {
                sError = "该单据由定货会调整单生成，不能红冲"; 
            }
        
            dynamic bindInfo = JsonConvert.DeserializeObject(bindSheetInfo);
            if (bindInfo != null && sError == "")
            {
                string sid = bindInfo.bindSheetId;
                string autoApprove = bindInfo.autoApprove;
                if (!string.IsNullOrEmpty(sid) && bindInfo.bindSheetType == "YF" && autoApprove == "true")
                {
                    SheetPrepay<SheetRowBase> psheet = new SheetPrepay<SheetRowBase>(SHEET_PREPAY.EMPTY, LOAD_PURPOSE.SHOW);
                    psheet.company_id = bindInfo.companyId;
                    redBrief += "(同步单据自动红冲)";
                    sError = await psheet.Red(cmd, psheet.company_id, sid, "1", redBrief, false);
                    if (sError != "")
                    {
                        sError = "分销账户预付款业务单据红冲失败";
                    }
                }
            }
 
            return sError;
        }

        /*public override async Task<JsonResult> ToVoucherRows(CMySbCommand cmd, string sheetID, SheetCwVoucher sheetCwVoucher, Dictionary<string, decimal> payways)
        {
            string subsID = "";
            string condi = "";
            int subLen = 0;
            if (payways==null || payways.Count == 0)
            {
                if (payway1_id != "" && payway1_amount != 0) payways.Add(payway1_id, payway1_amount * money_inout_flag);
                if (payway2_id != "" && payway2_amount != 0) payways.Add(payway2_id, payway2_amount * money_inout_flag);
                if (left_amount != 0) payways.Add("left", left_amount * money_inout_flag);
                if (now_disc_amount != 0) payways.Add("disc", now_disc_amount * money_inout_flag);
                payways.Add("p"+prepay_sub_id, total_amount * money_inout_flag);
            }
            if (payways == null || payways.Count == 0)
            {
                return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
            }
            foreach (var payway in payways)
            {
                if (payway.Key == "left") condi += $@"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='1122' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='1122')  order by sub_code limit 1 )"; // 应收账款
                else if (payway.Key == "disc") condi += $@"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_code=560304 )"; //财务费用-现金折扣（不允许增加子科目）;涉及code_length
                else
                {
                    if (subsID != "") subsID = subsID + ",";
                    subsID += payway.Key.StartsWith("p") ? payway.Key.Substring(1) : payway.Key;
                }
                subLen++;
            }
            string sql = "";
            if($@"{condi}  " .Trim()!= "") sql = $@"{condi}  ".Substring(9);
            if (subsID != "") sql += $@"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_id in ({subsID}) )";
            if(sql.Trim().StartsWith('u')) sql = sql.Substring(9);
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (records == null || records.Count < subLen) return new JsonResult(new { result = "Error", msg = "缺少生成凭证的相关科目，请添加" });
            if (records.Count > subLen) return new JsonResult(new { result = "Error", msg = "生成凭证的相关科目有重复科目代码，请修改" });
            
            foreach (var rec in records)
            {
                if (rec.status == null || rec.status == "") rec.status = 1;
                if (Convert.ToInt16(rec.status) == 0) return new JsonResult(new { result = "Error", msg = "相关科目已停用，请检查" });
                CwRowVoucher cwRow = new CwRowVoucher();
                cwRow.business_sheet_type = SheetType;
                cwRow.business_sheet_id = sheetID;
                cwRow.sub_id = rec.sub_id;
                cwRow.remark = "收预收款";
                if (SheetType == "DH") cwRow.remark = "定货";
                if (SheetType == "DHTZ") cwRow.remark = "定货调整";
                if (SheetType == "YF") cwRow.remark = "付预付款";

                decimal changeAmt = 0;
                foreach (var payway in payways)
                {
                    changeAmt = payway.Value;

                    if (payway.Key == rec.sub_id || (payway.Key == "left" && rec.sub_code.ToString().Substring(0, 4) == "1122") || (payway.Key == "disc" && rec.sub_code == "560304"))
                    {
                        if (changeAmt >= 0) cwRow.debit_amount = changeAmt.ToString();
                        else cwRow.credit_amount = Math.Abs(changeAmt).ToString();// YS正常开单: changeAmt>0  借payway，贷预收Sub； YF开单: changeAmt<0  借预付账款，贷payway
                        cwRow.change_amount = changeAmt.ToString();
                        break;
                    }
                    else if (payway.Key == "p"+ rec.sub_id) 
                    {
                        if (changeAmt >= 0) cwRow.credit_amount = changeAmt.ToString();
                        else cwRow.debit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = (-1 * changeAmt).ToString();
                        break;
                    }
                }
                sheetCwVoucher.SheetRows.Add(cwRow);
            }
            
            return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
        }*/
    }
}
