﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.MyJXC;
using System.Dynamic;
using Newtonsoft.Json;
using System;

namespace ArtisanManage.Pages
{
    public class StockAlertDetailModel : PageSheetModel<SheetRowInventory>
    { 
        public string SheetTitle = "";
        public string is_set = ""; // 用来控制按钮disabled状态
        public bool isSet { get; set; }
        public StockAlertDetailModel(CMySbCommand cmd):base(MenuId.stockAlertDetail)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"branch_id",new DataItem(){FldArea="divHead",Title="仓库",LabelFld="branch_name", ButtonUsage="list",SqlForOptions=$"select 0 as v,'所有' l,'sy' z union ({CommonTool.selectBranch})"}},
                {"forSetting",new DataItem(){FldArea="divHead",Title="设置模式",CtrlType="jqxCheckBox", Hidden=true}},
            };
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            
        }
        public async Task OnGet(bool forSetting)
        {
            SheetInventory sheet = new SheetInventory(LOAD_PURPOSE.SHOW);
            await InitGet(cmd,sheet);
            SheetTitle = forSetting ? "库存预警设置" : "库存预警表";
            if (forSetting) is_set = "disabled";
            SheetRowsJson = Newtonsoft.Json.JsonConvert.SerializeObject(sheet.SheetRows);
        }
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class StockAlertDetailController : BaseController
    { 
        public StockAlertDetailController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues)
        {
            var model = new StockAlertDetailModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey,model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<IActionResult> GetItems(string operKey, string query)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            //var model = new SaleSheetModel(cmd);
            string sql = $"select  distinct item_name as name,i.item_id as id,py_str as zjm,string_agg(u.barcode,',') as code from info_item_prop i left join info_item_multi_unit u on i.item_id=u.item_id where i.company_id={companyID} and u.company_id={companyID} and son_mum_item is null and (item_name ilike '%{query}%' or py_str ilike '%{query}%' or u.barcode ilike '%{query}%' or item_name ilike '%{query}%') group by item_name,i.item_id,py_str limit 30";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);  
            return new JsonResult(new {result="OK", records });
        }
        [HttpGet]
        public async Task<IActionResult> GetItemInfo(string operKey, string item_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            // var model = new SaleSheetModel();
            string sql = $"SELECT unit_no,unit_factor,wholesale_price as price from info_item_multi_unit where company_id={companyID} and item_id={item_id}";

            dynamic units = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //string items=Newtonsoft.Json.JsonConvert.SerializeObject(rec); 

            return new JsonResult(new { result = "OK",item = new {item_id,units} });
        }
        [HttpGet]
        public async Task<IActionResult> GetUnits(string operKey, string item_id, string query)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            // var model = new SaleSheetModel();
            string sql = $"select  distinct unit_no,unit_factor from info_item_multi_unit where company_id={companyID} and item_id={item_id} and unit_no like '%{query}%' limit 30";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //string items=Newtonsoft.Json.JsonConvert.SerializeObject(rec); 
            return new JsonResult(new { result = "OK", records });
        }

        [HttpGet]
        public async Task<IActionResult> GetStockQtyList(string operKey, string branch_id,string items_id,bool stockOnly,bool bGetAttrs, bool isSet = false, bool isLoad = false)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            // var model = new SaleSheetModel();
            var condi = "";
            var orderBy = "";
            if (stockOnly) stockOnly = !(branch_id == "0" || branch_id == "" || branch_id == null);
            var branch = !stockOnly ? "" : $" and branch_id={branch_id}";
            if (items_id != null) { 
                condi = $"and ip.item_id in ({items_id})";
                orderBy = $"order by position(ip.item_id::text in '{items_id}')";
            }
            if (stockOnly)
            {
                condi += $" and isa.branch_id = {branch_id}";
            }
            if (isLoad) condi += (isSet ?$" AND (threshold_overload is not null OR threshold_lack is not null)" : $" AND (stock.stock_qty > threshold_overload OR stock.stock_qty < threshold_lack)");
            
            SQLQueue QQ = new SQLQueue(cmd);

            string sql = @$"
select item_id,item_name,mum_attributes,stock_qty,s_wholesale_price wholesale_price,cost_price_avg,cost_price_spec,s_buy_price buy_price,yj_get_bms_qty(stock_qty,bunit,b_unit_factor::float4,munit,m_unit_factor::float4,sunit) as current_qty,
    (case when m_unit_factor is null and b_unit_factor is not null then concat(s_unit_factor,bunit,'=',b_unit_factor,sunit)  
		  when (b_unit_factor is not null) and (m_unit_factor is not null) then concat(s_unit_factor,bunit,'=',floor(b_unit_factor::numeric/m_unit_factor::numeric),munit,'=',b_unit_factor,sunit)
		  when b_unit_factor is null then concat(s_unit_factor,sunit)  end ) as unit_conv,
    bUnit as b_unit_no,munit as m_unit_no,sunit as unit_no,s_barcode barcode,s_unit_factor as unit_factor,m_unit_factor,b_unit_factor,bstock,mstock,sstock,threshold_lack,threshold_overload,remark,
    bLack as b_unit_lack, mLack as m_unit_lack, sLack as s_unit_lack,
    bOverload as b_unit_overload, mOverload as m_unit_overload, sOverload as s_unit_overload,
	(case when stock_qty>threshold_overload then '积压' when stock_qty<threshold_lack then '缺货' else '正常' end)as current_status,
	yj_get_bms_qty(lacker,bunit,b_unit_factor::float4,munit,m_unit_factor::float4,sunit) as num_lack,
	yj_get_bms_qty(overloader,bunit,b_unit_factor::float4,munit,m_unit_factor::float4,sunit) as num_overload
from(
    SELECT ip.item_id, ip.item_name,mum_attributes,stock_qty,ip.wholesale_price,ip.cost_price_avg,ip.cost_price_spec,threshold_lack,threshold_overload,remark,
		(case when stock_qty<threshold_lack then threshold_lack-stock_qty else null end)as lacker,
		(case when stock_qty>threshold_overload then stock_qty-threshold_overload else null end)as overloader,
        (case when b is not null then sign(COALESCE(stock.stock_qty,0))*floor(COALESCE(abs(stock.stock_qty),0) / (t.b->> 'f1')::numeric) else null end ) as bStock,(t.b->> 'f2') as bUnit,
        (CASE WHEN(t.m->>'f1') is null THEN null ELSE sign(COALESCE(stock.stock_qty,0))*floor((COALESCE(abs(stock.stock_qty),0)%(t.b->>'f1')::numeric)/(t.m->>'f1')::numeric) END) as mStock,(t.m->> 'f2') as mUnit,
        (CASE WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NOT NULL THEN sign(stock.stock_qty)*floor(COALESCE(abs(stock.stock_qty),0)%(t.b->>'f1')::numeric%(t.m->>'f1')::numeric)
			    WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NULL     THEN round(COALESCE(stock.stock_qty,0) % (t.b->> 'f1')::numeric)
                WHEN(t.b->>'f1') is NULL AND (t.m->>'f1') is NULL THEN round(COALESCE(stock.stock_qty,0)) END) sStock,
            
        (case when b is not null then sign(COALESCE(threshold_lack,0))*floor(COALESCE(abs(threshold_lack),0) / (t.b->> 'f1')::numeric) else null end ) as bLack,
        (CASE WHEN(t.m->>'f1') is null THEN null ELSE sign(COALESCE(threshold_lack,0))*floor((COALESCE(abs(threshold_lack),0)%(t.b->>'f1')::numeric)/(t.m->>'f1')::numeric) END) as mLack,
        (CASE WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NOT NULL THEN sign(threshold_lack)*floor(COALESCE(abs(threshold_lack),0)%(t.b->>'f1')::numeric%(t.m->>'f1')::numeric)
			    WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NULL     THEN round(COALESCE(threshold_lack,0) % (t.b->> 'f1')::numeric)
                WHEN(t.b->>'f1') is NULL AND (t.m->>'f1') is NULL THEN round(COALESCE(threshold_lack,0)) END) sLack,

        (case when b is not null then sign(COALESCE(threshold_overload,0))*floor(COALESCE(abs(threshold_overload),0) / (t.b->> 'f1')::numeric) else null end ) as bOverload,
        (CASE WHEN(t.m->>'f1') is null THEN null ELSE sign(COALESCE(threshold_overload,0))*floor((COALESCE(abs(threshold_overload),0)%(t.b->>'f1')::numeric)/(t.m->>'f1')::numeric) END) as mOverload,
        (CASE WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NOT NULL THEN sign(threshold_overload)*floor(COALESCE(abs(threshold_overload),0)%(t.b->>'f1')::numeric%(t.m->>'f1')::numeric)
			    WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NULL     THEN round(COALESCE(threshold_overload,0) % (t.b->> 'f1')::numeric)
                WHEN(t.b->>'f1') is NULL AND (t.m->>'f1') is NULL THEN round(COALESCE(threshold_overload,0)) END) sOverload,

        (t.s->> 'f2') as sUnit,(t.s->>'f1') as s_unit_factor,(t.m->>'f1') as m_unit_factor,(t.b->>'f1') as b_unit_factor,s->>'f3' as s_wholesale_price,s->>'f5' s_barcode,t.s->>'f6' s_buy_price
    FROM 
    info_item_prop as ip
    LEFT JOIN
    (
        select item_id, s, m, b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,retail_price,barcode,buy_price)) as json from info_item_multi_unit where company_id = {companyID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) 
        as errr(item_id int, s jsonb,m jsonb, b jsonb)
    ) t on ip.item_id = t.item_id 
    LEFT JOIN 
    (
        select item_id, sum(stock_qty) stock_qty from stock where company_id = {companyID} {branch} group by item_id
    ) stock on ip.item_id = stock.item_id
    LEFT JOIN 
    (
        select * from info_item_class where company_id = {companyID}
    ) as ic on ip.item_class = ic.class_id 
    LEFT JOIN
    (
        select item_id, branch_id, threshold_lack, threshold_overload, remark from info_stock_alert where company_id = {companyID} {branch}
    ) as isa on ip.item_id = isa.item_id
    WHERE ip.company_id={companyID}  {condi}
    {orderBy}
) tem ";


            QQ.Enqueue("items", sql);

            sql = @$" 
SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
(
    SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item
) t"; //  order by opt.order_index
            QQ.Enqueue("attr_options", sql);

            if (bGetAttrs)
            {
                sql = $"select attr_id,attr_name,distinct_stock,order_index from info_attribute where company_id={companyID} "; //  order by opt.order_index
                QQ.Enqueue("attrs", sql);
            }
            dynamic records = null;
            List <ExpandoObject> units = null;
            List <ExpandoObject> attributes = null;
            dynamic attrOptions = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "items")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }
                else if (tbl == "attrs")
                {
                    attributes = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", records,attributes,attrOptions });
        }
        public class test {
            public string id = "", name = "", tt="";
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic dSheet)
        {
            dynamic a = dSheet.SheetRows.ToString();
            string operKey = dSheet.operKey.ToString();
            List<AlertSet> alert = JsonConvert.DeserializeObject<List<AlertSet>>(a);

            int branch_id = int.Parse(dSheet.branch_id.ToString());
            Security.GetInfoFromOperKey(operKey, out string company_id);

            string sql = $@"
                DELETE FROM
                    info_stock_alert
                WHERE
                    company_id = {company_id}
                    and branch_id = {branch_id};
            ";

            string insertValues = string.Empty;
            int i = 0;
            foreach (AlertSet item in alert)
            {
                string remark = "'" + item.remark + "'";
                double threshold_lack = 0;
                double threshold_overload = 0;

                // s unit
                double.TryParse(item.unit_factor, out double s_factor);
                double.TryParse(item.s_unit_lack, out double s_lack);
                double.TryParse(item.s_unit_overload, out double s_overload);
                threshold_lack += s_lack * s_factor;
                threshold_overload += s_overload * s_factor;

                // m unit
                double.TryParse(item.m_unit_factor, out double m_factor);
                double.TryParse(item.m_unit_lack, out double m_lack);
                double.TryParse(item.m_unit_overload, out double m_overload);
                threshold_lack += m_factor * m_lack;
                threshold_overload += m_factor * m_overload;

                // b unit
                double.TryParse(item.b_unit_factor, out double b_factor);
                double.TryParse(item.b_unit_lack, out double b_lack);
                double.TryParse(item.b_unit_overload, out double b_overload);
                threshold_lack += b_factor * b_lack;
                threshold_overload += b_factor * b_overload;

                // sql
                insertValues += $"({company_id}, {branch_id}, {item.item_id}, {threshold_lack}, {threshold_overload}, {remark})";
                if (i < alert.Count - 1)
                {
                    insertValues += ",";
                }
                i++;
            }

            if (insertValues.Length > 0)
            {
                sql += $@"
                    INSERT INTO 
                        info_stock_alert
                        ( company_id, branch_id, item_id, threshold_lack, threshold_overload, remark )
                    VALUES 
                        {insertValues}
                    ON CONFLICT (company_id, branch_id, item_id )
                    DO UPDATE SET
                        threshold_lack = EXCLUDED.threshold_lack, 
                        threshold_overload = EXCLUDED.threshold_overload, 
                        remark = EXCLUDED.remark;
                ";
            }

            var msg = string.Empty;
            try
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                NLogger.Error("[库存预警设置/StockAlertDetail.Save] 数据库操作失败，语句为" + sql);
                NLogger.Error(ex.ToString());
                msg = "保存失败:" + ex.Message;
            }
            string result = msg.Length > 0 ? "Error" : "OK";
            return new JsonResult(new { result, msg });
        }
        private bool IsNull(string str)
        {
            return str == null || str == "";
        }
        private struct AlertSet
        {
            public int item_id { get; set; }
            public int branch_id;
            public string unit_factor { get; set; }
            public string b_unit_lack { get; set; }
            public string b_unit_overload { get; set; }
            public string m_unit_lack { get; set; }
            public string m_unit_overload { get; set; }
            public string s_unit_lack { get; set; }
            public string s_unit_overload { get; set; }
            public string b_unit_factor { get; set; }
            public string m_unit_factor { get; set; }
            //public int threshold_lack;
            //public int threshold_overload;
            public string remark { get; set; }
        }
    }
}