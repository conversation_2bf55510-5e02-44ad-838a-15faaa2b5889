﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowBackBranchVan:SheetRowBase
    {
        public SheetRowBackBranchVan()
        {

        }
        public string sheet_no { get; set; }
        [SaveToDB(false)][FromFld(false)] public override int inout_flag { get; set; } = 0;
        [SaveToDB(false)][FromFld(false)] public override int row_index { get; set; } = 0;
        [SaveToDB("op_id")][IDField][FromFld("op_id")] public override string sheet_id { get; set; }
        //[SaveToDB][FromFld] public string sale_order_sheet_id { get; set; }
        [SaveToDB][FromFld] public string item_id { get; set; }
        [FromFld("ip.item_name")]public string item_name { get; set; }
        [SaveToDB][FromFld] public string unit_no { get; set; }

        [SaveToDB][FromFld] public decimal unit_factor { get; set; } = 1;

        [SaveToDB][FromFld] public string back_unit_no { get; set; }

        [SaveToDB][FromFld] public decimal back_unit_factor { get; set; } = 1;

        [SaveToDB][FromFld] public string sale_unit_no { get; set; }

        [SaveToDB][FromFld] public decimal sale_unit_factor { get; set; } = 1;
        [SaveToDB][FromFld("case when t.move_qty is null then case when t.assign_van_type = 'reject' then reject_qty else back_qty end  else t.move_qty end")] public decimal move_qty { get; set; }

        [SaveToDB][FromFld("case when t.need_move_qty is null then case when t.assign_van_type='reject' then old_reject_qty else old_back_qty end  else t.need_move_qty end")] public decimal need_move_qty { get; set; }

        [SaveToDB][FromFld] public decimal sale_qty { get; set; }
        [SaveToDB][FromFld] public string sale_order_sheet_id { get; set; }
        [FromFld("som.sheet_no")] public string sale_order_sheet_no { get; set; }

        [SaveToDB][FromFld("case when t.back_type is null then t.assign_van_type else t.back_type end")] public string back_type { get; set; }

        [SaveToDB][FromFld] public string move_sheet_id { get; set; }
        [FromFld("smm.sheet_no")] public string move_sheet_no { get; set; }
        [SaveToDB][FromFld] public string sale_sheet_id { get; set; }
        [FromFld("sm.sheet_no")] public string sale_sheet_no { get; set; }
        public int move_status { get; set; }
        [SaveToDB][FromFld] public string is_previous_move { get; set; }


        [SaveToDB][FromFld] public string back_branch { get; set; }
        [FromFld("b.branch_name")] public string back_branch_name { get; set; }

    }
    public class SheetBackBranchVan : SheetBase<SheetRowBackBranchVan>
    {
        [SaveToDB("op_id")][IDField][FromFld("op_id")] public override string sheet_id { get; set; }
        [SaveToDB("op_no")][FromFld("case when t.op_no is null then 'HK'||t.op_id else t.op_no end")] public override string sheet_no { get; set; } = "";
        [SaveToDB][FromFld] public string move_sheet_id { get; set; }
        [SaveToDB][FromFld] public string oper_id { get; set; }
        [FromFld("op.oper_name")] public string oper_name { get; set; }
        [SaveToDB][FromFld] public string senders_id { get; set; }
        [SaveToDB][FromFld] public string senders_name { get; set; }
        
        public string have_previous_move { get; set; } = "false";

        public dynamic rowInfo { get; set; }
        [SaveToDB(false)][FromFld(false)] public override string red_sheet_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string maker_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_time { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string approve_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string submit_time { get; set; } = "";

        [SaveToDB(false)][FromFld(false)] public override string maker_name { get; set; } = "";
        [FromFld("ofd.sale_order_sheets_id")] public string sale_order_sheets_id { get; set; }
        public string sale_order_sheets_no { get; set; }
        [FromFld("sm.sheet_no")] public string move_sheet_no { get; set; }

        [SaveToDB][FromFld("case when t.approver_id is null then sm.approver_id else t.approver_id end ")] public override string approver_id { get; set; } = "";
        [FromFld("case when t.approver_id is null then mover.approver_name else approver.approver_name end")] public override string approver_name { get; set; } = "";
        [SaveToDB][FromFld("to_char( case when t.approve_time is null then sm.approve_time else t.approve_time end , 'yyyy-MM-dd hh24:mi:ss' ) as approve_time")] public override string approve_time { get; set; } = "";

        public List<SheetRowMove> RejectedSheetRows = new List<SheetRowMove>();
        public List<SheetRowMove> ReturnedSheetRows = new List<SheetRowMove>();
        [SaveToDB][FromFld] public string from_branch { get; set; }
        [FromFld("b.branch_name")] public string from_branch_name { get; set; }
        //[SaveToDB][FromFld] public string from_branch_position { get; set; }
        //[FromFld("b.branch_position_name")] public string from_branch_position_name { get; set; }
        public Dictionary<string, string> lsSheets = new Dictionary<string, string>();
        public List<dynamic> lsSheetRows
        {
            get
            {
                List<dynamic> lst = new List<dynamic>();
                foreach (var row in this.SheetRows)
                {
                    lst.Add(row);
                }
                return lst;

            }
            set
            {
                foreach(var row in value)
                {
                    SheetRowBackBranchVan rowBackBranch = new SheetRowBackBranchVan();
                    rowBackBranch.item_id = row.item_id;
                    rowBackBranch.unit_no = row.unit_no;
                    rowBackBranch.unit_factor = row.unit_factor;
                    rowBackBranch.move_qty = row.move_qty;
                    rowBackBranch.need_move_qty = row.need_move_qty;
                    rowBackBranch.move_status = row.need_move_qty - row.move_qty > 0 ? 1 : 0;
                    rowBackBranch.sale_order_sheet_id = row.sale_order_sheet_id;
                    rowBackBranch.sale_sheet_id = row.sale_sheet_id;
                    rowBackBranch.back_type = row.back_type;
                    rowBackBranch.back_unit_factor = row.back_unit_factor;
                    rowBackBranch.back_unit_no = row.back_unit_no;
                    rowBackBranch.move_sheet_id = row.move_sheet_id;
                    rowBackBranch.back_branch = row.back_branch;
                    rowBackBranch.remark = row.remark;
                    rowBackBranch.sale_qty = row.sale_qty;
                    rowBackBranch.sale_unit_factor = row.sale_unit_factor;
                    rowBackBranch.sale_unit_no = row.sale_unit_no;
                    this.SheetRows.Add(rowBackBranch);
                }

            }
        }
        [SaveToDB][FromFld] public string back_done { get; set; }
        public string rejectToBranchID { get; set; }
        public string returnToBranchID { get; set; }

        public string rejectSheetNO { get; set; }
        public string rejectSheetID { get; set; }
        public string returnSheetNO { get; set; }
        public string returnSheetID { get; set; }

        [SaveToDB][FromFld("case when t.move_stock is null then ofd.move_stock else t.move_stock end")] public string move_stock { get; set; }


        public SheetBackBranchVan(LOAD_PURPOSE loadPurpose) : base("op_move_from_van_main", "op_move_from_van_row", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_BACK_BRANCH_VAN;
                MainLeftJoin = @" 	
                        LEFT JOIN info_branch b ON t.from_branch = b.branch_id and b.company_id = ~COMPANY_ID
	                    LEFT JOIN ( SELECT oper_id, oper_name  FROM info_operator WHERE company_id = ~COMPANY_ID ) op on op.oper_id = t.oper_id 
	                    LEFT JOIN sheet_move_main sm on sm.company_id = t.company_id and sm.sheet_id = t.move_sheet_id
	                    LEFT JOIN ( SELECT oper_id, oper_name AS approver_name FROM info_operator WHERE company_id = ~COMPANY_ID ) approver ON t.approver_id = approver.oper_id 
	                    LEFT JOIN ( SELECT oper_id, oper_name AS approver_name FROM info_operator WHERE company_id = ~COMPANY_ID ) mover ON sm.approver_id = mover.oper_id 
	                    LEFT JOIN ( 
                            SELECT DISTINCT op_id , so.move_stock,string_agg(sale_order_sheet_id::text,',') sale_order_sheets_id 
                            FROM op_move_from_van_detail d 
		                    LEFT JOIN sheet_status_order so on so.company_id = d.company_id and d.sale_order_sheet_id = so.sheet_id
	                        WHERE d.company_id = ~COMPANY_ID GROUP BY op_id,so.move_stock ) ofd on ofd.op_id= t.op_id
                ";
                DetailLeftJoin = $@" 
	                    LEFT JOIN info_item_prop ip ON ip.company_id = T.company_id AND ip.item_id = T.item_id
	                    LEFT JOIN sheet_sale_main sm ON sm.company_id = T.company_id AND sm.sheet_id = T.sale_sheet_id
	                    LEFT JOIN sheet_sale_order_main som ON som.company_id = T.company_id AND som.sheet_id = T.sale_order_sheet_id
	                    LEFT JOIN sheet_move_main smm ON smm.company_id = T.company_id AND smm.sheet_id = T.move_sheet_id
	                    LEFT JOIN info_branch b on b.company_id = t.company_id and b.branch_id = t.back_branch
                ";   
        }

        public async Task<string> LoadSheet(CMySbCommand cmd,string companyID, string op_id,string order_sheets_id)
		{
            var res = await base.Load(cmd, companyID, op_id);
            
            return "";

		}
     
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.from_branch + "_" +this.happen_time+ "_" + this.move_stock;
            foreach (var row in SheetRows)
            {
                res += row.item_id + "_" + row.sale_order_sheet_id+"_"+row.back_branch+"_"+row.move_qty;
            }
            return res;
        }
        public override string GetOtherSaveSQL()
        {
            string sqlDetail = "";
            if (sheet_id == "")
            {
                foreach (var lst in lsSheets)
                {

                    sqlDetail += $@"insert into op_move_from_van_detail (company_id, oper_id, op_id, sale_order_sheet_id, sale_sheet_id) values ('{company_id}', '{OperID}', '@op_id', {lst.Key}, {lst.Value});";
                }
            }
            string sqlPreviousRow = "";
            if (have_previous_move.ToLower() == "true")
            {
                foreach(var row in SheetRows)
                {
                    sqlPreviousRow += $@"UPDATE op_move_from_van_row ofr 
                                            set is_previous_move = true 
                                            from op_move_from_van_main ofm 
                                            WHERE ofm.company_id = ofr.company_id and ofm.op_id = ofr.op_id and ofr.company_id = {company_id} and ofr.happen_time< '{happen_time}' and ofr.op_id <> @op_id and ofm.red_flag is null and ofr.sale_order_sheet_id = {row.sale_order_sheet_id} ;";
                }
            }
            string sqlOrderStatus = "";
            foreach (var lst in lsSheets)
            {
                if (back_done.ToLower()=="false")
                {
                    var orderSheetStatus = true;
                    foreach(var row in SheetRows)
                    {
                        if (lst.Key == row.sale_order_sheet_id &&row.need_move_qty*row.unit_factor-row.sale_qty*row.sale_unit_factor-row.move_qty*row.back_unit_factor>0)
                        {
                            orderSheetStatus=false;
                            break;
                        }
                    }
                    if (orderSheetStatus)
                    {
                        sqlOrderStatus += $"update sheet_status_order set back_branch_status = 'qb' where company_id={company_id} and sheet_id={lst.Key};";
                    }
                    else
                    {
                        sqlOrderStatus += $"update sheet_status_order set back_branch_status = 'bf' where company_id={company_id} and sheet_id={lst.Key};";
                    }

                }
                else if (back_done.ToLower() == "true")
                {
                    sqlOrderStatus += $"update sheet_status_order set back_branch_status = 'qb' where company_id={company_id} and sheet_id={lst.Key};";
                }
                //sql += $"update sheet_status_order set back_branch_done = 't' where company_id={company_id} and sheet_id={lst.Key};";
            }
            return sqlDetail + sqlPreviousRow + sqlOrderStatus;
        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            
            base.GetInfoForApprove_SetQQ(QQ);
          
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            
        }
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
            if (this.SheetRows.Count() == 0) return "该回库单没有获取订单明细";
            return "OK";
        }
        protected override async Task<CInfoForApproveBase> GetInfoForApprove(CMySbCommand cmd)
        {

            SQLQueue QQ = new SQLQueue(cmd);
            if (sheet_id != "")
            {
                if (!FIXING_ARREARS)
                {
                    string check_sql = $"select approve_time from {MainTable} where op_id={sheet_id} and company_id = {company_id}";
                    QQ.Enqueue("check_sheet", check_sql);
                }
            }

            GetInfoForApprove_SetQQ(QQ);
            string errMsg = "";
            if (QQ.Count > 0)
            {
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                try
                {
                    while (QQ.Count > 0)
                    {
                        string tbl = QQ.Dequeue();
                        if (tbl == "check_sheet")
                        {
                            dynamic checkSheet = CDbDealer.Get1RecordFromDr(dr, false);
                            if (checkSheet != null && checkSheet.approve_time != "")
                            {
                                errMsg = "单据已审核过,不能再次审核";
                                break;
                            }
                        }
                        else
                        {
                            GetInfoForApprove_ReadData(dr, tbl, false);
                            if (InfoForApprove != null && InfoForApprove.ErrMsg != "")
                            {
                                errMsg = InfoForApprove.ErrMsg;
                                break;
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    errMsg = "读取数据失败";
                    MyLogger.LogMsg($"in GetInfoForApprove. error:${e.Message}, ${e.StackTrace}", company_id, "approve");
                }
                QQ.Clear();
            }
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            InfoForApprove.ErrMsg = errMsg;
            return InfoForApprove;
        }
        //protected override string GetApproveSQL(CInfoForApproveBase info)
        //{
        //    string sql = "";
        //    foreach (var lst in lsSheets)
        //    {
        //        var tempStatus = 0;
        //        foreach(var row in SheetRows)
        //        {
        //            if (lst.Key == row.sale_order_sheet_id)
        //            {
        //                tempStatus += row.move_status;
        //            }

        //        }
        //        if(tempStatus > 0)
        //        {
        //            sql += $"update sheet_status_order set back_branch_status = 'bf' where company_id={company_id} and sheet_id={lst.Key};";
        //        }
        //        else if(tempStatus == 0){
        //            sql += $"update sheet_status_order set back_branch_status = 'qb' where company_id={company_id} and sheet_id={lst.Key};";
        //        }
        //        //sql += $"update sheet_status_order set back_branch_done = 't' where company_id={company_id} and sheet_id={lst.Key};";
        //    }

            
        //    return sql;
        //}
     
        
        public class CInfoForApprove : CInfoForApproveBase
        {
            public string ArrearBalance = "", PrepayBalance = "";
            public List<SheetRowInventory> SheetRows = null;
        }

		protected override async Task<string> BeforeRed(CMySbCommand cmd, string sheetID, string rederID, string redBrief, CInfoForApproveBase info)
		{
			//cmd.ActiveDatabase = "";
			// await Load(cmd, companyID, sheetID, true);


			string getFollowingSheet = $@"";
            string err = "";
            string sql = "";
            if(move_stock.ToLower() == "true")
            {
                SheetMove returnMoveSheet = new SheetMove(LOAD_PURPOSE.APPROVE);
                SheetMove rejectMoveSheet = new SheetMove(LOAD_PURPOSE.APPROVE);
                if (rejectSheetID != "" && rejectSheetID!="-1") err = await rejectMoveSheet.Red(cmd, this.company_id, rejectSheetID, rederID, redBrief, false);
                
                if (err == "" && returnSheetID!="" && returnSheetID!="-1")
                {
                    err = await returnMoveSheet.Red(cmd, this.company_id, returnSheetID, rederID, redBrief, false);
                }
            }

            if (err == "")
            {  
               // sql += $"update op_move_from_van_main set red_flag='1' where company_id={this.company_id} and op_id={sheet_id};";
                sql += sale_order_sheets_id.Split(',').Aggregate("", (current, orderSheetId) => current + $@"update sheet_status_order sso  set back_branch_done = null,back_branch_status = case when position(',' in op_move)>0  then 'bf' else null end 
from (SELECT string_agg( vr.op_id||'_'||vr.sale_order_sheet_id ,',') op_move FROM op_move_from_van_row vr 
LEFT JOIN op_move_to_van_main vm on vm.company_id = vr.company_id and vm.op_id = vr.op_id 
 WHERE vr.company_id={this.company_id} and vr.sale_order_sheet_id={orderSheetId}   and vm.red_flag is null )tt

where sso.company_id={this.company_id} and sso.sheet_id={orderSheetId}; ");
                foreach(dynamic row in rowInfo)
                {
                    if(row.is_previous_move.ToLower() == "true")
                    {
                        var moveQty = Convert.ToSingle( row.move_qty) * Convert.ToSingle(row.back_unit_factor);

                        sql += $@"  UPDATE op_move_from_van_row ofr 
                                    set  need_move_qty =( need_move_qty::numeric*unit_factor::numeric +  {moveQty})/unit_factor::numeric
                                    FROM op_move_from_van_main ofm 
                                    WHERE ofm.company_id = ofr.company_id and ofm.op_id = ofm.op_id and ofr.company_id = {this.company_id} and ofr.op_id <> {sheet_id} and ofr.happen_time >'{row.happen_time}' and ofm.red_flag is null   and ofr.sale_order_sheet_id ={row.sale_order_sheet_id} ;
                                ";
                    }
                    else
                    {
                        sql += $@"UPDATE op_move_from_van_row ofr 
                                  set is_previous_move = null 
                                  FROM (SELECT MAX(ofr.happen_time) happen_time FROM op_move_from_van_row ofr 
			                            LEFT JOIN op_move_from_van_main ofm on ofm.company_id = ofr.company_id and ofm.op_id = ofr.op_id 
			                            WHERE ofr.company_id = {this.company_id} and ofm.red_flag is null and ofr.sale_order_sheet_id ={row.sale_order_sheet_id} and ofr.item_id ={row.item_id} and ofr.unit_no ='{row.unit_no}' and ofr.happen_time <'{row.happen_time}' 
			                            GROUP BY ofr.sale_order_sheet_id
                                        ) t 
                                  WHERE ofr.company_id = {this.company_id} and ofr.sale_order_sheet_id ={row.sale_order_sheet_id}  and ofr.happen_time = t.happen_time;
                                ";

                    }

                }
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
               
            }               
           
            
            return err;
        }
        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd,  CInfoForApproveBase info)
        {
            string err = "";
           
            var sheetMove = new SheetMove(LOAD_PURPOSE.SHOW);
            if (move_stock.ToLower() == "true")
            {
                if (ReturnedSheetRows.Count > 0)
                {
                    sheetMove.from_branch_id = from_branch;
                    sheetMove.company_id = company_id;
                    sheetMove.happen_time = CPubVars.GetDateText(DateTime.Now);
                    sheetMove.maker_id = OperID;
                    sheetMove.make_time = CPubVars.GetDateText(DateTime.Now);
                    sheetMove.submit_time = CPubVars.GetDateText(DateTime.Now);
                    sheetMove.OperKey = OperKey;
                    sheetMove.OperID = OperID;
                    sheetMove.assign_van = Assign_Van.BACK_VAN;
                    sheetMove.SheetRows = JsonConvert.DeserializeObject<List<SheetRowMove>>(JsonConvert.SerializeObject(ReturnedSheetRows));
                    sheetMove.to_branch_id = returnToBranchID;
                    if (returnSheetID != ""&&returnSheetID!="null"&&returnSheetID!=null) sheetMove.sheet_id = returnSheetID;
                    if (returnSheetNO != ""&&returnSheetNO!=null) sheetMove.sheet_no = returnSheetNO;
                    err = await sheetMove.SaveAndApprove(cmd, false);
                    if (err == "")
                    {
                        returnSheetID = sheetMove.sheet_id;
                        returnSheetNO = sheetMove.sheet_no;
                    }
                }
                if(err == "")
                {
                    sheetMove = new SheetMove(LOAD_PURPOSE.SHOW);
                    if (RejectedSheetRows.Count > 0)
                    {
                        sheetMove.from_branch_id = from_branch;
                        sheetMove.company_id = company_id;
                        sheetMove.happen_time = CPubVars.GetDateText(DateTime.Now);
                        sheetMove.maker_id = OperID;
                        sheetMove.make_time = CPubVars.GetDateText(DateTime.Now);
                        sheetMove.submit_time = CPubVars.GetDateText(DateTime.Now);
                        sheetMove.OperKey = OperKey;
                        sheetMove.OperID = OperID;
                        sheetMove.assign_van = Assign_Van.BACK_VAN;
                        sheetMove.SheetRows = JsonConvert.DeserializeObject<List<SheetRowMove>>(JsonConvert.SerializeObject(RejectedSheetRows));
                        sheetMove.to_branch_id = rejectToBranchID;
                        if (rejectSheetID != ""&& rejectSheetID!="null"&&rejectSheetID!=null) sheetMove.sheet_id = rejectSheetID;
                        if (rejectSheetNO != ""&& rejectSheetNO != null) sheetMove.sheet_no = rejectSheetNO;
                        err = await sheetMove.SaveAndApprove(cmd, false);
                        if (err == "")
                        {
                            rejectSheetID = sheetMove.sheet_id;
                            rejectSheetNO = sheetMove.sheet_no;
                        }
                    }
                }
            }
            

            
            if (err == "")
            {
                foreach(var row in this.SheetRows)
                {
                    if(row.back_type == "reject")
                    {
                        row.move_sheet_id = rejectSheetID;
                    }
                    else if (row.back_type == "return")
                    {
                        row.move_sheet_id = returnSheetID;
                    }
                }
              
            }
            
            return err;
        }

        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting); 
            
        }

    }
}
