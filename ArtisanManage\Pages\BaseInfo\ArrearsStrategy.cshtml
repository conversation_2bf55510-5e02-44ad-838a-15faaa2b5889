@page
@model ArtisanManage.Pages.BaseInfo.ArrearsStrategyModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <link href="~/css/component.css" rel="stylesheet" />
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
        });
        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                window.operRowID = -1;
                window.g_gridID = 'gridItems'
                var divGrid = '#' + window.g_gridID
                $(divGrid).jqxGrid({
                    cellhover: function (cellhtmlElement, x, y, rowIndex, gridID) {
                        if (cellhtmlElement) {
                            if (cellhtmlElement.className.indexOf('pinned') >= 0) {
                                var displayRows = $(divGrid).jqxGrid('getvisiblerows');
                                var allRows = $(divGrid).jqxGrid('getrows');
                                var arr = $('.row_operator')

                                for (var i = 0; i < arr.length; i++) {
                                    var row_operator = arr[i]
                                    if (row_operator.parentNode) {
                                        var row = row_operator.parentNode.parentNode
                                        var id = row.id
                                        id = id.replace('row', '')
                                        id = id.replace('gridItems', '')
                                        var curRow = displayRows[id]
                                        var showIndex = -1
                                        for (var j = 0; j < allRows.length; j++) {
                                            var r = allRows[j]

                                            if (r === curRow) {
                                                showIndex = j
                                            }
                                        }
                                        var html = "<div style='height:100%;display:flex; justify-content:center;align-items:center;'>" + (showIndex + 1) + "</div>";
                                        row_operator.parentNode.innerHTML = html;// row_operator.parentNode.normalInnerHTML;
                                    }
                                }

                                if (cellhtmlElement.innerHTML.indexOf('row_operator') == -1) {
                                    if (!(window.g_dicNotDeleteRows && window.g_dicNotDeleteRows[rowIndex])) {
                                        var pinText = $(cellhtmlElement).text()
                                        cellhtmlElement.innerHTML = `<div class="row_operator" style="height:100%;width:100%;display:flex;justify-content:space-around;align-items:center;">
                                     <svg onclick="onRowAdd('${divGrid.replace('#', '')}',${rowIndex})" class="row-oper" height="15" width="15" style="">
                                            <use xlink:href="/images/images.svg#add" />
                                     </svg>
                                     <svg onclick="onRowRemove('${divGrid.replace('#', '')}',${rowIndex})" class="row-oper" height="15" width="15" style="">
                                            <use xlink:href="/images/images.svg#remove" />
                                     </svg>
                                    </div>`;
                                        window.operHTML = cellhtmlElement.innerHTML;
                                        window.operRowID = rowIndex;
                                    }

                                }
                            }
                        }

                    }

                });

                $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
                });

                $(".Items").parent().addClass("active");

                $(".arrearsTab div").on("click", function () {

                    var title = $(this).attr("class");
                    if (title == "Clients") {
                        $("#gridClients").css("visibility", "visible");
                        $("#gridItems").css("visibility", "hidden");
                        $("#gridOperators").css("visibility", "hidden");

                        $("#searchClients").show();
                        $("#searchItems").hide();
                        $("#searchOperators").hide();

                        window.g_gridID = 'gridClients'
                    } else if (title == "Items") {
                        $("#gridItems").css("visibility", "visible");
                        $("#gridClients").css("visibility", "hidden");
                        $("#gridOperators").css("visibility", "hidden");

                        $("#searchItems").show();
                        $("#searchClients").hide();
                        $("#searchOperators").hide();

                        window.g_gridID = 'gridItems'
                    } else {
                        $("#gridOperators").css("visibility", "visible");
                        $("#gridItems").css("visibility", "hidden");
                        $("#gridClients").css("visibility", "hidden");

                        $("#searchOperators").show();
                        $("#searchClients").hide();
                        $("#searchItems").hide();

                        window.g_gridID = 'gridOperators'
                    }

                    $(this).parent().addClass("active");
                    $(this).parent().siblings().removeClass("active");

                })
            });

        function performSearchClass() {
            var rows = $("#gridItems").jqxGrid("getRows");
            var searchGroup = $('#search-group').val();
            var searchRegion = $('#search-region').val();
            var searchRank = $('#search-rank').val();
            var searchBalanceMin = !isNaN(parseFloat($('#items-search-balance-min').val())) ? parseFloat($('#items-search-balance-min').val()) : -999999;
            var searchBalanceMax = !isNaN(parseFloat($('#items-search-balance-max').val())) ? parseFloat($('#items-search-balance-max').val()) : Infinity;

            var field = $('#items-search-field').val();

            for (var i = 0; i < rows.length; i++) {
                var row = rows[i].uid;
                var groupName = rows[i].group_name;
                var regionName = rows[i].region_name;
                var rankName = rows[i].rank_name;

                var rowBalance = rows[i][field]

                if ((groupName && groupName.includes(searchGroup)) && (regionName && regionName.includes(searchRegion)) && (rankName && rankName.includes(searchRank)) && (rowBalance <= searchBalanceMax && rowBalance >= searchBalanceMin)) {
                    $("#gridItems").jqxGrid('showrow', row);// 如果包含，显示这一行
                } else {
                    $("#gridItems").jqxGrid('hiderow', row);// 如果不包含，隐藏这一行
                }
            }
        }
        
        function performSearchClient() {
            var rows = $("#gridClients").jqxGrid("getRows");
            var searchName = $('#search-supcust').val();
            var searchBalanceMin = !isNaN(parseFloat($('#clients-search-balance-min').val())) ? parseFloat($('#clients-search-balance-min').val()) : -999999;
            var searchBalanceMax = !isNaN(parseFloat($('#clients-search-balance-max').val())) ? parseFloat($('#clients-search-balance-max').val()) : Infinity;
            var field = $('#clients-search-field').val();

            for (var i = 0; i < rows.length; i++) {
                var row = rows[i].uid;
                var supName = rows[i].sup_name; 
                var rowBalance = rows[i][field]
                
                if (supName && supName.includes(searchName) && rowBalance <= searchBalanceMax && rowBalance >= searchBalanceMin) {
                    $("#gridClients").jqxGrid('showrow', row);// 如果包含，显示这一行
                } else {
                    $("#gridClients").jqxGrid('hiderow', row);// 如果不包含，隐藏这一行
                }
            }
            
        }

        function performSearchOperator() {
            var rows = $("#gridOperators").jqxGrid("getRows");
            var searchName = $('#search-operator').val();
            var searchBalanceMin = !isNaN(parseFloat($('#operators-search-balance-min').val())) ? parseFloat($('#operators-search-balance-min').val()) : -999999;
            var searchBalanceMax = !isNaN(parseFloat($('#operators-search-balance-max').val())) ? parseFloat($('#operators-search-balance-max').val()) : Infinity;
            var field = $('#operators-search-field').val();

            for (var i = 0; i < rows.length; i++) {
                var row = rows[i].uid;
                var operName = rows[i].oper_name;
                var rowBalance = rows[i][field]

                if (operName && operName.includes(searchName) && rowBalance <= searchBalanceMax && rowBalance >= searchBalanceMin) {
                    $("#gridOperators").jqxGrid('showrow', row);// 如果包含，显示这一行
                } else {
                    $("#gridOperators").jqxGrid('hiderow', row);// 如果不包含，隐藏这一行
                }
            }

        }
        
    </script>
</head>

<body style="overflow:hidden">
    <style>
        html, body {
            height: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        .dataArea {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            margin: 0;
            width: 100%;
            height: 100%;
        }

            .dataArea > div:first-child {
                width: 100%;
                height: 40px;
            }

            .dataArea > div:last-child {
                display: flex;
                align-items: stretch;
                align-content: stretch;
                width: 100%;
                flex-grow: 1;
            }

                .dataArea > div:last-child > div:first-child {
                    width: 200px;
                }

                .dataArea > div:last-child > div:last-child {
                    flex-grow: 1;
                }

        .display-none {
            display: none;
        }

        .arrearsTab ul {
            list-style-type: none;
            overflow: hidden;
            margin-top: 0px;
        }

        .arrearsTab ul li {
            float: left;
        }

        .arrearsTab ul li {
            padding: 5px 0;
            margin-right: 5px;
            width: 150px;
            text-align: center;
        }

        .arrearsTab ul li div {
            cursor: pointer;
        }

        .arrearsTab ul li.active {
            border-bottom: 1px solid #e6214a
        }

        .arrearsTab ul li.active div {
            color: #e6214a;
        }


        html, body {
            height: 100%;
            padding-left: 10px;
            margin: 0;
            overflow: hidden;
        }

        body {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        #gridItems {
            margin-bottom: 2px;
            width: calc(100% - 20px);
            flex: 1;
        }

        #gridClients {
            margin-bottom: 2px;
            width: calc(100% - 20px);
            flex: 1;
        }

        #gridOperators {
            margin-bottom: 2px;
            width: calc(100% - 20px);
            flex: 1;
        }
    </style>
    
    <div style="display:inline-link;justify-content:space-around;margin-top:20px;">
        <div class="arrearsTab" id="arrearsTab" style="float:left">
            <ul>
                <li>
                    <div class="Items"> 按客户类别指定 </div>
                </li>
                <li>
                    <div class="Clients">按客户指定</div>
                </li>
                <li>
                    <div class="Operators">按业务员指定</div>
                </li>
            </ul>
        </div>

        <div id="searchItems" class="search-container" style="float:left">
            <input type="text" id="search-group" placeholder="渠道">
            <input type="text" id="search-region" placeholder="片区">
            <input type="text" id="search-rank" placeholder="等级">

            <input type="text" id="items-search-balance-min" placeholder="下限" style="width:50px">
            <select id="items-search-field">
                <option value="max_arrears">欠款额度</option>
                <option value="max_arrears_days">欠款天数</option>
            </select>
            <input type="text" id="items-search-balance-max" placeholder="上限" style="width:50px">
            <button id="search-button" onclick="performSearchClass()">搜索</button>
        </div>


        <div id="searchClients" class="search-container" style="float:left;display:none;">
            <input type="text" id="search-supcust" placeholder="客户名称">
            <input type="text" id="clients-search-balance-min" placeholder="下限" style="width:50px">
            <select id="clients-search-field">
                <option value="max_arrears">欠款额度</option>
                <option value="max_arrears_days">欠款天数</option>
                <option value="balance">欠款金额</option>
                <option value="pend_amount">欠款金额(订单)</option>
                <option value="total_amount">欠款金额(总)</option>
            </select>
            <input type="text" id="clients-search-balance-max" placeholder="上限" style="width:50px">
            <button id="search-button" onclick="performSearchClient()">搜索</button>
        </div>

        <div id="searchOperators" class="search-container" style="float:left;display:none;">
            <input type="text" id="search-operator" placeholder="业务员名称">
            <input type="text" id="operators-search-balance-min" placeholder="下限" style="width:50px">
            <select id="operators-search-field">
                <option value="max_arrears">欠款额度</option>
                <option value="auxiliary_balance">欠款金额</option>
            </select>
            <input type="text" id="operators-search-balance-max" placeholder="上限" style="width:50px">
            <button id="search-button" onclick="performSearchOperator()">搜索</button>
        </div>

        <button id="btnSave" onclick="btnSave_Clicked();" style="float:right;margin-right:50px;">保存</button>
    </div>

    <div id="gridItems" style="position:absolute;left:20px;width:calc(100% - 50px);top:70px;bottom:50px;"></div>
    <div id="gridClients" style="position:absolute;left:20px;width:calc(100% - 50px);top:70px;bottom:50px;visibility:hidden"></div>
    <div id="gridOperators" style="position:absolute;left:20px;width:calc(100% - 50px);top:70px;bottom:50px;visibility:hidden"></div>

    <script type="text/javascript">
        function btn_change(e) {
            window.parent.newTabPage("欠款策略", `BaseInfo/ArrearsStrategy`, window);
        }
        function onFormSaved(msg) {
            bw.toast("保存成功");
        } 
    </script>
</body>
</html>