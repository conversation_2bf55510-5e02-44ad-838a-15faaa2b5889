﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.AppController.Sheets
{ 
    [Route("AppApi/[controller]/[action]")]
    public class AppSheetInventory : QueryController
    { 
        public AppSheetInventory(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        /// <summary>
        /// 加载盘点单--
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SheetInventory sheet = new SheetInventory(LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index";
            QQ.Enqueue("attr_options", sql);
            
            List<ExpandoObject> attrOptions = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "attr_options")
                {
                    attrOptions = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet, attrOptions});
        }
        
        /// <summary>
        /// 提交盘点单
        /// </summary>GetItemList
        /// <param name="sheet">
        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic dSheet)
        {
            SheetInventory sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetInventory>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Save:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            if (msg == "")
            {
                cmd.ActiveDatabase = "";
                sheet.Init();
                msg = await sheet.Save(cmd);
            }
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.make_time, sheet.happen_time, currentTime });
        }
        
        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="dSheet"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            SheetInventory sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetInventory>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetInventory.Submit:" + msg +" sheet:" + sSheet);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            sheet.Init();
            msg = await sheet.SaveAndApprove(cmd);
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no,sheet.approve_time, currentTime });
        }

        
        /// <summary>
        /// 红冲
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="sheetID"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            string result = "OK"; string msg = null;
            try
            {
                var currentTime = DateTime.Now.ToText();
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                SheetInventory sheet = new SheetInventory(LOAD_PURPOSE.SHOW);
                msg = await sheet.Red(cmd, companyID, sheetID, operID,"");
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetInventory sheet = new SheetInventory(LOAD_PURPOSE.SHOW);
            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return Json(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        /// <summary>  下方注释为下面注释接口注释，不一定参考意义
        /// 商品档案列表----返回商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数
        /// </summary>
        /// <param name="operKey">Aa18nTx5omI=</param>
        /// <param name="searchStr">商品名，助记码，商品编号，商品条码 模糊查询</param>
        /// <param name="brandID">品牌ID查询</param>
        /// <param name="classID">分类ID查询</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="branchID">仓库名 （1）</param>
        /// <returns>商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数</returns>
        [HttpGet]
        public async Task<JsonResult> GetItemList(string operKey,string sortType, string searchStr, string brandIDs, string classID, int pageSize, int startRow, string branchID, bool showStockOnly)
        { 
            bool firstRequest = false;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $" where ip.company_id = {companyID} and (ip.status is null or ip.status='1') and son_mum_item is NULL ";

            if (searchStr != null)
            {
                string b = "%";
                if (searchStr.Length >= 6)
                {
                    b = "";
                }
                string flexStr = CPubVars.GetFlexLikeStr(searchStr);
                condi += $"and (ip.item_name ilike '%{flexStr}%' or ip.py_str ilike '%{searchStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%'  or ip.mum_attributes::text ilike '%{searchStr}%' or (t.s->>'f3') like '%{searchStr}{b}' or  (t.b->>'f3') like '%{searchStr}{b}' or (t.m->>'f3') like '%{searchStr}{b}')";
            }
            if (brandIDs != null && brandIDs != "") condi += $"and (ip.item_brand is null OR ip.item_brand in ({brandIDs})) ";
            if (classID != null && classID != "0") condi += $" and ip.other_class like '%/{classID}/%' ";
            if (showStockOnly) condi += $" and stock_qty > 0 ";
            string showStockOnlyCondi = "";
            if (showStockOnly) showStockOnlyCondi += $" and stock_qty>0 ";
            if (startRow == 0) firstRequest = true;
            string sortSQL = "";
            if (sortType == "item_name")
            {
                sortSQL = "py_str asc";
            }
            else if (sortType == "order_index")
            {
                sortSQL = " item_order_index asc";
            }
            else
            {
                sortSQL = "item_order_index,item_id desc";
            }

            SQLQueue QQ = new SQLQueue(cmd);
            string sql_noLimit = @$"
select item_id,item_name,item_images,mum_attributes,stock_qty,s_wholesale_price wholesale_price,cost_price_avg,cost_price_spec,item_order_index,s_buy_price buy_price,yj_get_bms_qty(stock_qty,bunit,b_unit_factor::float4,munit,m_unit_factor::float4,sunit) as current_qty,batch_level,
                    (case when m_unit_factor is null and b_unit_factor is not null then concat(s_unit_factor,bunit,'=',b_unit_factor,sunit)  
			              when (b_unit_factor is not null) and (m_unit_factor is not null) then concat(s_unit_factor,bunit,'=',floor(b_unit_factor::numeric/m_unit_factor::numeric),munit,'=',b_unit_factor,sunit)
						  when b_unit_factor is null then concat(s_unit_factor,sunit)  end ) as unit_conv,
                  bUnit as b_unit_no,munit as m_unit_no,sunit as s_unit_no,branch_id,s_barcode,b_barcode,m_barcode,m_unit_factor,b_unit_factor,bstock,mstock,sstock  
from
(
    SELECT ip.item_id, ip.item_name,mum_attributes,stock_qty,item_order_index,ip.wholesale_price,ip.cost_price_avg,ip.cost_price_spec,stock.branch_id,item_images,ip.py_str,ip.py_str1,ip.item_no,ip.batch_level,
                        (case when b is not null then sign(COALESCE(stock.stock_qty,0))*floor(COALESCE(abs(stock.stock_qty),0) / (t.b->> 'f1')::numeric) else null end ) as bStock,
                        (CASE WHEN(t.m->>'f1') is null THEN null ELSE sign(COALESCE(stock.stock_qty,0))*floor((COALESCE(abs(stock.stock_qty),0)%(t.b->>'f1')::numeric)/(t.m->>'f1')::numeric) END) as mStock,
                        (CASE WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NOT NULL THEN sign(stock.stock_qty)*floor(COALESCE(abs(stock.stock_qty),0)%(t.b->>'f1')::numeric%(t.m->>'f1')::numeric)
			             WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NULL THEN round(COALESCE(stock.stock_qty,0) % (t.b->> 'f1')::numeric)
                         WHEN(t.b->>'f1') is NULL AND (t.m->>'f1') is NULL THEN round(COALESCE(stock.stock_qty,0)) END) sStock,
                        (t.s->>'f1') as s_unit_factor, (t.s->> 'f2') as sUnit, t.s->>'f3' s_barcode,s->>'f4' as s_wholesale_price,t.s->>'f6' s_buy_price,
                        (t.b->>'f1') as b_unit_factor, (t.b->> 'f2') as bUnit, t.b->>'f3' b_barcode,s->>'f4' as b_wholesale_price,t.b->>'f6' b_buy_price,
                        (t.m->>'f1') as m_unit_factor, (t.m->> 'f2') as mUnit, t.m->>'f3' m_barcode
    FROM info_item_prop as ip
    LEFT JOIN
    (
        select item_id, s, m, b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,wholesale_price,retail_price,buy_price)) as json from info_item_multi_unit where company_id = {companyID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) 
                 as errr(item_id int, s jsonb,m jsonb, b jsonb)
    ) t
    on ip.item_id = t.item_id
    LEFT JOIN 
    (
        select s.company_id,case when son_mum_item is null then s.item_id else son_mum_item end item_id,branch_id,sum(stock_qty) as stock_qty from stock s
        LEFT JOIN info_item_prop ip on ip.company_id={companyID} and s.item_id = ip.item_id
            where s.company_id = {companyID} and branch_id = {branchID} {showStockOnlyCondi}
            GROUP BY s.company_id ,case when son_mum_item is null then s.item_id else son_mum_item end,branch_id
    
    ) stock on t.item_id = stock.item_id
    LEFT JOIN (select * from info_item_class where company_id = {companyID}) as ic on ip.item_class = ic.class_id {condi}
) tem ";
            
            var sql = sql_noLimit + $" order by {sortSQL} limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);
            if (firstRequest)
            {
                sql = $"select count(*) as itemCount from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
            }
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count"&& firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, itemCount });
        }
    }
}

