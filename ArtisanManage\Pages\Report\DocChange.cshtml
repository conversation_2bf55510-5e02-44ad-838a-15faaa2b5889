﻿@page
@model ArtisanManage.Pages.BaseInfo.DocChangeModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <link type="text/css" rel="Stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css" />
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/> 

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var newCount = 1;
    	var itemSource = {};
    	$(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            QueryData();
            $("#popChangeLog").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
        });

        var diffObj = {}
        function findDifferences(newValue, oldValue) {
            let differences = [];     
            if (!newValue.gridUnit || !oldValue.gridUnit) {
                return;
            }

            // 将 oldValue 的数组元素转换为对象，方便比较
            let oldValueMap = {};

            oldValue.gridUnit.forEach(item => {
                oldValueMap[item.unit_type] = item;
            });

            newValue.gridUnit.forEach(item => {
                let oldItem = oldValueMap[item.unit_type];

                // 旧值与新值不同
                if (JSON.stringify(oldItem) !== JSON.stringify(item)) {
                    differences.push({
                        unit_type: item.unit_type,
                        oldValue: oldItem,
                        newValue: item
                    });
                }
            });

            return differences;
        }
        function compareObjects(obj1, obj2) {
            const updeteValue = []
            // 获取属性列表
            const keys1 = Object.keys(obj1);
            const keys2 = Object.keys(obj2);

            // 检查属性数量是否相同
            if (keys1.length !== keys2.length) {
                return '属性数量不同';
            }

            for (let key of keys1) {
                // 比较属性值
                if (obj1[key] !== obj2[key]) {
                    //let tempObj = {}
                    diffObj[obj1["unit_type"] + "_" + key] = { newValue: obj1[key], oldValue: obj2[key] }
                    //updeteValue.push(tempObj);
                }
            }
        }

        function onChangeLogClick(value) {
            let docType = $("#obj_name").val().value
            let obj = null
            switch (docType) { 
                case "商品档案":
                    obj = obj = {
                        "item_name": "商品名称",
                        "item_alias": "别名",
                        "item_no": "编号",
                        "py_str": "助记名",
                        "location": "存放位置",
                        "class_name": "类别",
                        "brand_name": "品牌",
                        "item_order_index": "顺序号",
                        "valid_days": "保质期保质期",
                        "valid_day_type": "保质期类型",
                        "item_spec": "规格",
                        "item_provenance": "原产地",
                        "status": "状态",
                        "supplier_id": "主供应商",
                        "manufactor_id": "生产商",
                        "itemUsed": "已使用",
                        // "mum_attributes":"",
                        // "item_images":"",
                        "batch_level": "产期/批次",
                        "approve_status": "审核状态",
                        // "price_plan_item":"",
                        // "s_unit_type": "包装类型",
                        "s_unit_no": "单位(小)",
                        "s_unit_factor": "包装率(小)",                      
                        "s_wholesale_price": "批发价(小)",
                        "s_retail_price": "零售价(小)",
                        "s_buy_price": "进价(小)",
                        "s_profit_rate": "利润率(小)",
                        "s_cost_price_spec": "预设成本(小)",
                        "s_lowest_price": "最低售价(小)",
                        "s_contract_price": "承包价(小)",
                        "s_cost_price_avg": "加权价(小)",
                        "s_cost_price_recent": "最近平均进价(小)",
                        "b_wholesale_price": "批发价(大)",
                        "b_retail_price": "零售价(大)",
                        "b_buy_price": "进价(大)",
                        "b_profit_rate": "利润率(大)",
                        "b_cost_price_spec": "预设成本(大)",
                        "b_lowest_price": "最低售价(大)",
                        "b_contract_price": "承包价(大)",
                        "b_cost_price_avg": "加权价(大)",
                        "b_cost_price_recent": "最近平均进价(大)",
                        "m_wholesale_price": "批发价(中)",
                        "m_retail_price": "零售价(中)",
                        "m_buy_price": "进价(中)",
                        "m_profit_rate": "利润率(中)",
                        "m_cost_price_spec": "预设成本(中)",
                        "m_lowest_price": "最低售价(中)",
                        "m_contract_price": "承包价(中)",
                        "m_cost_price_avg": "加权价(中)",
                        "m_cost_price_recent": "最近平均进价(中)",
                        "s_barcode": "条码(小)",
                        "s_weight": "重量 kg(小)",
                        "s_volume": "体积 m³(小)",
                        "m_unit_no": "单位(中)",
                        "m_unit_factor": "包装率(中)",
                        "m_barcode": "条码(中)",
                        "m_weight": "重量 kg(中)",
                        "m_volume": "体积 m³(中)",
                        "b_unit_no": "单位(大)",
                        "b_unit_factor": "包装率(大)",
                        "b_barcode": "条码(大)",
                        "b_weight": "重量 kg(大)",
                        "b_volume": "体积 m³(大)",
                    }
                    break
                case "客户档案":
                    obj ={
                        "sup_name": "客户名称",
                        "supcust_no": "客户编号v",
                        "py_str": "助记名",
                        "boss_name": "老板姓名",
                        "mobile": "联系电话",
                        "sup_addr": "客户地址",
                        "addr_lat": "纬度",
                        "addr_lng": "经度",
                        "region_name": "片区",
                        "group_name": "渠道",
                        "rank_name": "等级",
                        "supcust_flag": "类型",
                        "status": "状态",
                        "create_time": "添加时间",
                        "cust_type": "类型",
                        "acct_cust_name": "结算单位",
                        "sup_door_photo": "门头照",
                        "sup_other_photo": "其他照片",
                        "acct_type": "结算类型",
                        "acct_way_name": "结算方式",
                        "charge_seller": "业务员",
                        "creator_id": "创建人",
                        "license_no": "营业执照",
                        "max_arrears": "欠款额度",
                        "sup_order_index": "显示顺序",
                        "supcust_remark": "备注",
                        "approve_status": "审核状态",
                        "arrears_order_start_date": "对账起始日期",
                        "arrears_order_end_date": "对账截至日期",
                        "arrears_get_start_date": "收款起始日期",
                        "arrears_get_end_date": "重量收款截至日期",
                        "addr_desc": "收货地址",
                    }
                    break
                default:
                    break
            }
            console.log(JSON.stringify(value))
            
            if (value.grid != undefined) {
                let differences = findDifferences(value.grid.newValue, value.grid.oldValue);
                differences.forEach(item => {
                    compareObjects(item.newValue, item.oldValue)
                })
                value = diffObj
            }
            
            let columns = []
            let gridData = [{}, {}]
            debugger
            for(let key in value){
                if ((key.indexOf("addr_desc") >= 0 && obj["addr_desc"] !== void (0)) || obj[key] !== void (0)) {
                    if (columns.length == 0) { 
                        columns.push({
                            "text": "",
                            "datafield": "index",
                            "width": 100,
                        })
                    }
                    let text = obj[key]
                    if(key.indexOf("addr_desc") >= 0) {
                        text = key.replace("addr_desc","收货地址")
                    }
                    columns.push({
                        text:text,
                        datafield: key,
                        width: 100,
                    })
                    if (key == "batch_level") { 
                        value[key]["newValue"] = value[key]["newValue"] == "1" ? "开启产期" : (value[key]["newValue"]=="2"?"开启批次":"非严格")
                        value[key]["oldValue"] = value[key]["oldValue"] == "1" ? "开启产期" : (value[key]["oldValue"] == "2" ? "开启批次" : "非严格")
                    }
                    if (key == "status") {
                        value[key]["newValue"] = value[key]["newValue"] == "1" ? "正常" : "停用"
                        value[key]["oldValue"] = value[key]["oldValue"] == "1" ? "正常" : "停用"
                    }
                    if (key == "acct_type") {
                        value[key]["newValue"] = value[key]["newValue"] == "pay" ? "现结" : "欠款"
                        value[key]["oldValue"] = value[key]["oldValue"] == "pay" ? "现结" : "欠款"
                    }
                    if (key == "cust_type") {
                        value[key]["newValue"] = value[key]["newValue"] == "client" ? "客户" : "门店"
                        value[key]["oldValue"] = value[key]["oldValue"] == "client" ? "客户" : "门店"
                    }
                    gridData[0][key] = value[key]["newValue"]
                    gridData[1][key] = value[key]["oldValue"]
                }
            }
            gridData[0]["index"] = "新"
            gridData[1]["index"] = "旧"
            $('#popChangeLog').jqxWindow('open');
            var source =
            {
                localdata: gridData,
                datatype: "array"
            };
            var dataAdapter = new $.jqx.dataAdapter(source);
            $('#logDetailGrid').jqxGrid({
                source: dataAdapter,
                columns:columns,
                width: "100%",
                height:"80%",
                columnsresize:true
            })


        }
        function changeLogView(row, column, value, p4, p5, rowData) { 
           //  debugger
            if(!value)return ``
            return `<div onclick='onChangeLogClick(${value})' style="height:100%;display:flex;align-items:center;justify-content:center;color:#4499ff;" >查看</div>`
        }
        // function handleApprove(approveFlag) {
        //     if (approveFlag) {

        //     } else {

        //     }
        // }

            
    </script>
</head>

<body style="overflow:hidden"> 
    <div style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>

        <button onclick="QueryData()" style="margin-right:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>

    </div>
    
     <div id="gridItems"></div>  
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div> 
        

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">商品信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popChangeLog" style="display:none">
        <div id="logCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">修改详情</span></div>
        <div id="logBox" style="width:100%;height:calc(100%-30px);box-sizing:border-box;padding:20px;overflow:hidden;">
            <div id="logDetailGrid">
            </div>
            @*
            <div id="approveBtn" style="padding:10px;display:flex;align-items:center;justify-content:center;">
            <button @@click="handleApprove(true)" style="margin-right:20px;">通过</button>
            <button @@click="handleApprove(false)" style="margin-right:20px;">不通过</button>
            </div>
            *@
        </div>
    </div>

</body>
</html>