@page
@model StockAlertDetailModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="en">
<head>
    <partial name="_SheetHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript" src="~/Sheet/StockAlertDetail.js?v=@Model.Version"></script>

    <script type="text/javascript">
        var hideCost = true;
        var seeInPrice = @Html.Raw(Model.JsonOperRights.Replace("stock.sheetInvent", "delicacy.seeInPrice.value"));       

        //$(document).ready(() => {
        //    $("#divInventType").jqxPopover({ showArrow: false, autoClose: false, offset: { left: 0, top: -10 }, position: "bottom", title: "", showCloseButton: false, selector: $("#importItem") });
        //})
    </script>
    <link href="~/css/component.css" rel="stylesheet" />
    <style>
        .jqx-popover-content{
            padding: 0 14px;
        }
        ul li{
            list-style:none;
            line-height:30px;
            height:30px;
        }
/*
        #divInventType {
            display:none;
            position:absolute;
            width:150px;
        }

        #divInventType ul{
            padding-left:10px;
        }
        
        #divInventType ul li input{
            cursor: pointer;
        }
        #divInventType ul li label{
            cursor: pointer;
        }

        
        div#jqxgrid{
            width: 100%;
            height: 100%;
        }
*/
    </style>
</head>

<body class='default' style="overflow:hidden;">
    <form id="form1" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server">
            <Services>
                <asp:ServiceReference Path="Service.asmx" InlineScript="true" />
            </Services>
        </asp:ScriptManager>

        <div id="divTitle" style="text-align:center;height:45px;margin-top:5px;">
            <label id="lblSheetTitle" style="font-weight:500;font-size:25px;">@Html.Raw(Model.SheetTitle)</label>
            <img id="imgState" style="display:none;position:fixed;top:0px;left:calc(50% - 150px);" src="" />
            @*<div class="makeInfo" style="position:absolute;top:5px; right:0px;">
                <div><div><label>单号:</label></div> <div id="sheet_no"></div></div>
                <div><div><label>制单:</label></div> <div><span id="make_time"></span><span id="maker_name"></span></div></div>
                <div><div><label>审核:</label></div> <div><span id="approve_time"></span><span id="approver_name"></span></div></div>
            </div>*@

        </div>
        <div>
            <div id="divHead" class="headtail" style="margin-bottom:10px;margin-top:0px;">

                <div style="float:none;height:0px; clear:both;"></div>
                <button onclick="onStartInvent()" style="margin-left:10px;width:60px;" type="button">加载</button>
                <button id="btnSave" type="button" style="margin-left:10px;width:60px">保存</button>
                <button id="btnSwitch" @Model.is_set style="margin-left:10px;width:150px;" type="button">库存预警设置</button>
                @*<button onclick="onZeroInvent()" style="margin-left:10px;width:170px;" type="button">未录入盘点数量商品置零</button>
                <button onclick="onCurrQtyInvent()" style="margin-left:10px;width:220px;" type="button">未录入盘点数量商品设为当前库存</button>
                <button onclick="onCurrentInvent()" style="margin-left:10px;width:150px;" type="button">未盘点商品为当前库存</button>
                <button id="importItem" show="Dialog_库存" style="margin-left:10px;width:100px;" type="button">导入</button>*@
            </div>
        </div>

        <div id="jqxgrid" style="margin-left: 10px; position: static;
            width:100%; height:100%;
            border-bottom-color:#dedede;">
        </div>

        <div id="divTail" class="headtail" style="margin-bottom:10px;margin-top:10px;">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <div id="divButtons" style="text-align:center;">
      
        </div>
        <div id="cellbegineditevent"></div>

        <div id="divInventType" >
            @*<ul>
                <li>
                    <input class="magic-checkbox" name="inventType" type="radio" id="partInvent" checked />
                    <label for="partInvent">部分盘点</label> 
                </li>
                <li>
                    <input class="magic-checkbox" name="inventType" type="radio" id="wholeInvent" />
                    <label for="wholeInvent">整仓盘点</label> 
                    
                </li>
            </ul>*@
        </div>

        <input id="hdDbID" type="hidden" />

        <div id="popupWindow" style="display:none;">
            @*<div style="margin: 10px">
                <div id="pop_sale_sheet"> </div>
                <div>
                    <input style="margin-right: 5px;" type="button" id="choose" value="选择" /><input type="button" value="取消" id="cancelButton" />
                </div>
                <br />
                <br />
            </div>*@
        </div>


        <div id="popItem" style="display:none">
            <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择商品</span></div>
            <div style="overflow:hidden;"> </div>
        </div>
        <div id="popMessage" style="display:none">
            <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;"></span></div>
            <div id="divMessage" style="overflow:scroll;">
            </div>
        </div>
    </form>
    <partial name="dialog" />
    <div role="dialog" id="Dialog_库存"></div>

</body>
</html>
