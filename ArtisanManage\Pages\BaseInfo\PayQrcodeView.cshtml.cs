﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Asn1.Ocsp;

namespace ArtisanManage.Pages.BaseInfo
{
    /* 20230419: 接下来的改造计划
     * 将'会计科目'中的支付方式拿取出来，做一个比较完善的'支付方式'edit-view界面
     * '会计科目'的原有内容不作改动
     * 添加一个名为'移动支付'的支付分类
     * 在'支付方式'界面添加/修改支付方式时，允许设置支付类别为[银行/移动支付/现金]
     * 银行/移动支付相当于在类下添加支付方式
     * 现金应先检查有无现金科目，有则不允许增加，无则添加一个叫现金的支付方式
    */
    public class PayQrCodeViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;

        public PayQrCodeViewModel(CMySbCommand cmd) : base(Services.MenuId.infoPayQrCode)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
             {

             };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                    "gridItems",  new QueryGrid()
                    {
                        IdColumn="qrcode_id",TableName="info_pay_qrcode",
                        ShowContextMenu=true,
                        Columns = new Dictionary<string, DataItem>()
                        {
                            {"qrcode_id",new DataItem(){Title="qrcode_id", Hidden=true,HideOnLoad = true}},
                            {"sub_code",new DataItem(){Title="账户编码", Width="200",Linkable=true}},
                            {"sub_name",new DataItem(){Title="账户名称", Width="200"}},
                            {"bank_no",new DataItem(){Title="银行账号", Width="250"}},
                            {"bank_name",new DataItem(){Title="开户银行", Width="200"}},
                            {"order_index", new DataItem(){ Title="顺序号", Width="100" } },
                            {"remark",new DataItem(){Title="备注", Width="250"}},
                            {"status",new DataItem(){Title="状态", Width="200", SqlFld="(case WHEN status='0' THEN '停用' ELSE '正常' END)"}},
                            {
                               "qrcode_uri",   new DataItem()
                               { 
                                    Title = "收款二维码",
                                    Width="200",
                                    FuncDealMe = url =>{
                                        return url==""?"<div>暂无上传</div>": $"<div style=\"color:#409EFF;cursor:pointer;\"><a target=\"_blank\" onclick=\"previewImage(\'{HuaWeiObs.BucketLinkHref+"/"+url}\')\" >查看</a></div>";
                                    }
                               }
                            }
                        },
                        QueryFromSQL="from info_pay_qrcode q left join cw_subject s on q.company_id=s.company_id and q.sub_id=s.sub_id where q.company_id=~COMPANY_ID" ,
                        QueryOrderSQL="order by s.order_index,s.sub_code::text"
                    }
                }
            };
        }
        public async Task OnGet(string forSelect)
        {
            await InitGet(cmd);
            ForSelect = forSelect == "1";

        }
    }



    [Route("api/[controller]/[action]")]
    public class PayQrCodeViewController : QueryController
    {
        public PayQrCodeViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            PayQrCodeViewModel model = new PayQrCodeViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {// string gridID,int startRow,int endRow,bool bNewQuery){
            PayQrCodeViewModel model = new PayQrCodeViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            dynamic qr = await CDbDealer.Get1RecordFromSQLAsync($"select * from info_pay_qrcode where qrcode_id={data.rowIDs}", cmd);
            dynamic check_use = await CDbDealer.Get1RecordFromSQLAsync($@"select * from (
                    (select qrcode_id as id, '现金银行期初' as type from cashbank_balance_init where company_id={companyID} and qrcode_id={data.rowIDs} and coalesce(balance,0)<>0) 
                    union all
                    (select sub_id as id, '现金银行余额' as type from cashbank_balance where company_id={companyID} and sub_id={qr.sub_id} and coalesce(balance,0)<>0)
                    union all 
                    (select sheet_id as id, (case when sheet_type='X' then '销售单' else '退货单' end) as type from sheet_sale_main where company_id={companyID} and coalesce(is_imported,false)=false and red_flag is null and (payway1_id={qr.sub_id} or payway2_id={qr.sub_id} or payway3_id={qr.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, (case when sheet_type='CG' then '采购单' else '采购退货单' end) as type from sheet_buy_main where company_id= {companyID} and coalesce(is_imported,false)=false and red_flag is null and (payway1_id={qr.sub_id} or payway2_id={qr.sub_id} or payway3_id={qr.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, (case when sheet_type='SK' then '收款单' else '付款单' end) as type from sheet_get_arrears_main where company_id={companyID} and coalesce(is_imported,false)=false and red_flag is null and (payway1_id={qr.sub_id}  or payway2_id={qr.sub_id} or payway3_id={qr.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, (case when sheet_type='YS' then '预收款单' when sheet_type='YF' then '预付款单' else '定货单' end) from sheet_prepay where company_id={companyID} and coalesce(is_imported,false)=false and red_flag is null and (payway1_id={qr.sub_id} or payway2_id={qr.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, (case when sheet_type='ZC' then '费用支出单' else '其他收入单' end) as type from sheet_fee_out_main where company_id={companyID} and sheet_attribute->>'imported' is null and red_flag is null and (payway1_id={qr.sub_id} or payway2_id={qr.sub_id}) limit 1)
                    union all
                    (select sheet_id as id, '会计凭证' as type from cw_voucher_detail where company_id = {companyID} and sub_id={qr.sub_id} limit 1)
                    union all
                    (select sheet_id as id, '财务期初数据' as type from cw_op_sub_init_detail where company_id = {companyID} and sub_id={qr.sub_id} and coalesce(balance,0)<>0)
                    union all
                    (select sheet_id as id, '财务期初辅助核算数据' as type from cw_op_sub_init_assister_detail where company_id={companyID} and coalesce(balance,0)<>0 and sub_id={qr.sub_id} limit 1 )
                    ) t limit 1", cmd);
            if (check_use != null)
            {
                return Json(new { result = "Error", msg = $"该账户已被【{check_use.type}】使用过，无法删除" });
            }

            PayQrCodeViewModel model = new PayQrCodeViewModel(cmd);
            JsonResult result  = await model.DeleteRecords(data, cmd, "info_pay_qrcode");
            return result;
        }
    }
}
