﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class OrderToSaleDifferencelModel : PageQueryModel
    { 
        public OrderToSaleDifferencelModel(CMySbCommand cmd) : base(Services.MenuId.orderToSaleDifferencel)
        {
            this.cmd = cmd;
            this.PageTitle = "订单核销明细表";
            DataItems = new Dictionary<string, DataItem>()
            {
             {"startDay",new DataItem(){Title="开始日期",FldArea="divHead",ForQuery=false, CtrlType="jqxDateTimeInput", SqlFld="approve_time",CompareOperator=">=",QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
            {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", ForQuery=false, CtrlType="jqxDateTimeInput", SqlFld="approve_time", CompareOperator="<",QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
            }},
                 {"supcust_id",new DataItem(){FldArea="divHead",Title="客户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",ForQuery = false,QueryByLabelLikeIfIdEmpty=true,
                   SqlForOptions=CommonTool.selectSupcust } },
                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",Checkboxes=true, LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},
                {"maker_id",new DataItem(){FldArea="divHead",Title="操作人",Checkboxes=true, LabelFld="maker_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},
                 {
                    "senders_id",
                    new DataItem()
                    {
                        FldArea = "divHead", Title = "送货员",Checkboxes = true, LabelFld = "senders_name", ButtonUsage = "list",
                        DealQueryItem = status => ","+status+",",ForQuery = false,
                        SqlForOptions=CommonTool.selectSenders,  //SqlForOptions = "select oper_id as v,oper_name as l,py_str as z from info_operator",
                        CompareOperator = "like"
                    }
                },
                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="oc.region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
             {"sheet_no",new DataItem(){FldArea="divHead",Title="订单号", CompareOperator="like"}},
             {"sale_sheet_no",new DataItem(){FldArea="divHead",Title="销售单号", CompareOperator="like"}},
                {"make_brief",new DataItem(){FldArea="divHead",Title="备注", CompareOperator="like"}},

                {"difference_status",new DataItem(){FldArea="divHead",Title = "差异情况",LabelFld = "difference_status_name", LabelInDB = false, Value = "all", Label = "正常",ButtonUsage = "list", QueryOnChange = false, Hidden=true, CompareOperator = "=", NullEqualValue = "all",
                     Source = @"[{v:'greate',l:'拒收',condition:""( COALESCE(order_qty,0)  >  COALESCE(sale_qty,0) )""},
                               {v:'less',l:'多配',condition:""(COALESCE(order_qty,0)  < COALESCE(sale_qty,0) ) ""},
                               {v:'all',l:'所有',condition:""true""}]"
                 }},

            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",     new DataItem(){Hidden = true, HideOnLoad = true,SqlFld="osd.sheet_id "} },
                       {"order_sheet_no",     new DataItem(){Title="订单号",   Width="100" ,Linkable = true,SqlFld=" (case when osd.sheet_no is not null then osd.sheet_no else sd.order_sheet_no end ) "}},
                       {
                                "receipt_status",
                                new DataItem()
                                {
                                    Title = "签收情况",Width = "80",CellsAlign = "center",
                                    SqlFld = "(case when sso.receipt_status = 'bf' then '部分签收' when sso.receipt_status = 'zd' then '整单签收' when sso.receipt_status = 'js' then '整单拒签' end)"
                                }
                            },
                       {"sale_sheet_id ",   new DataItem(){Hidden = true, HideOnLoad = true,  Width="200",SqlFld="sd.sale_sheet_id" }},
                       {"sale_sheet_no",     new DataItem(){Title="销售单号",      Width="100" ,Linkable = true}},
                       {"item_id",     new DataItem(){  Width="100" ,SqlFld="ip.item_id" ,Hidden=true,HideOnLoad=true}},
                       {"supcust_id",     new DataItem(){Title="supcust_id",Hidden = true,SqlFld="case when sc.supcust_id is null then oc.supcust_id else sc.supcust_id end", HideOnLoad = true} },
                       {"sup_name",     new DataItem(){Title="客户", Width="300",SqlFld ="case when sc.supcust_id is null then oc.sup_name else sc.sup_name end"}},
                       {"oper_name",    new DataItem(){Title="业务员",     Width="80",SqlFld="case when io.oper_id is null then op.oper_name else io.oper_name end"}},
                       {"senders_name",    new DataItem(){Title="送货员",      Width="80",SqlFld="case when sd.senders_name is null then osd.order_senders_name else sd.senders_name end"}},
                         {"item_name",     new DataItem(){Title="商品",      Width="100" }},

                       {"qty",new DataItem(){Title="数量", Width="60", Sortable=true, FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                               
                            Columns =new Dictionary<string,DataItem>()
                              {
                               {"order_quantity",     new DataItem(){Title="订单",
                                   SqlFld="unit_from_s_to_bms (sum(order_qty),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)"} },
                               {"sale_quantity",     new DataItem(){Title="销售",
                                     SqlFld="unit_from_s_to_bms (sum(sale_qty),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)"} },
                               {"difference_quantity",     new DataItem(){Title="差异",
                                     SqlFld="unit_from_s_to_bms (sum(   COALESCE(sale_qty,0) - COALESCE(order_qty,0)),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)"} },

                             },

                            }
                       } },

                       {"amount",new DataItem(){Title="金额", Width="60", Sortable=true, FuncGetSubColumns = async (col) =>
                            new ColumnsResult{

                            Columns=new Dictionary<string,DataItem>()
                              {

                            {"order_amount",     new DataItem(){Title="订单",   SqlFld=" sum(order_sub_amount)"} },
                            {"sale_amount",     new DataItem(){Title="销售",SqlFld=" sum(sale_sub_amount)"} },
                            {"difference_amount",     new DataItem(){Title="差异",SqlFld=" COALESCE(sum(sale_sub_amount),0)- COALESCE(sum(order_sub_amount),0)"} },
                            }
                            }
                       } },



                     },
                     QueryFromSQL=@"
                from (
                        select 
                        sheet_no ,
                        osd.sheet_id ,
                        osm.supcust_id as order_supcust_id,seller_id order_seller_id,
						senders_id order_senders_id,senders_name order_senders_name,
                        osd.item_id,
		                 sum(COALESCE(quantity::numeric * unit_factor::numeric,0))   as order_qty,	
                        sum(sub_amount) as order_sub_amount
                        from   (select sheet_id ,sheet_no,red_flag,supcust_id,seller_id,senders_id,senders_name  from  sheet_sale_order_main where company_id =~COMPANY_ID )osm     
                        LEFT JOIN  sheet_sale_order_detail osd

                        on osd.sheet_id=osm.sheet_id 

                        where osd.company_id =~COMPANY_ID and osd.happen_time >='~VAR_startDay'and osd.happen_time <='~VAR_endDay' and red_flag is null
                        GROUP BY sheet_no ,
                        osd.sheet_id ,
                        osm.supcust_id ,seller_id,
						senders_id ,senders_name,
                        osd.item_id

                ) osd
                full JOIN 
                (
                        select 
                            sale_sheet_no,
                            order_sheet_no,
                            sale_sheet_id,
                            seller_id,
                            maker_id,
                            senders_name,
                            senders_id,
                            order_sheet_id,
                            sm.supcust_id as sale_supcust_id,
                            item_id sale_item_id,
                            sum(COALESCE(quantity::numeric*unit_factor::numeric,0) )  as sale_qty,	
                            sum(sub_amount) as sale_sub_amount
                            from   (select
                                sm.sheet_id sale_sheet_id,
                                sm.sheet_no as sale_sheet_no,
                                sm.seller_id,
                                sm.maker_id,
                                sm.senders_name,
                                sm.senders_id,
                                order_sheet_id ,
                                osm.sheet_no as order_sheet_no,
                                sm.red_flag ,
                                sm.supcust_id
                                from  sheet_sale_main sm
                                left join  sheet_sale_order_main  osm on sm.order_sheet_id =osm.sheet_id
                                 where sm.company_id =~COMPANY_ID  and osm.happen_time >='~VAR_startDay'and osm.happen_time <='~VAR_endDay'
                        )sm
                        LEFT JOIN    sheet_sale_detail  sd on sd.sheet_id=sm.sale_sheet_id 
                        where sd.company_id =~COMPANY_ID and red_flag is null and sd.happen_time >='~VAR_startDay'and sd.happen_time <='~VAR_endDay' and order_sheet_id is not null
                        GROUP BY sale_sheet_no,
                            order_sheet_no,
                            sale_sheet_id,
                            seller_id,
                            maker_id,
                            senders_name,
                            senders_id,
                            order_sheet_id,
                            sm.supcust_id,
                            item_id
                )
                sd on osd.sheet_id =sd.order_sheet_id and osd.item_id=sd.sale_item_id

                LEFT JOIN info_item_prop ip on (case when osd.item_id is not null then osd.item_id else sale_item_id  end ) =ip.item_id
left join 
(
    select item_id,     (s->>'f1')::numeric as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_barcode,
                        (b->>'f1')::numeric as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_barcode,
                        (m->>'f1')::numeric as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_barcode
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,weight,volume)) as json from info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
    as errr(item_id int, s jsonb,m jsonb,b jsonb) 
) t
on (case when osd.item_id is not null then osd.item_id else sale_item_id  end )=t.item_id  
LEFT JOIN info_supcust sc ON  sale_supcust_id  = sc.supcust_id AND sc.company_id =~COMPANY_ID 
LEFT JOIN info_supcust oc on order_supcust_id = oc.supcust_id and oc.company_id = ~COMPANY_ID
LEFT JOIN info_operator io on io.oper_id = seller_id and io.company_id = ~COMPANY_ID 
LEFT JOIN info_operator op on op.oper_id = osd.order_seller_id and op.company_id = ~COMPANY_ID 
LEFT JOIN sheet_status_order sso on sso.company_id = ~COMPANY_ID and sso.sheet_id = osd.sheet_id 
where true ~VAR_senders_id  ~VAR_supcust_id and sso.order_status = 'zd'
", 

                     QueryGroupBySQL = @$"group by osd.sheet_id ,sd.sale_sheet_id,sso.receipt_status ,order_sheet_no,sheet_no,  sale_sheet_no, ip.item_id ,item_name    ,
                     case when sc.supcust_id is null then oc.supcust_id else sc.supcust_id end  , case when sc.supcust_id is null then oc.sup_name else sc.sup_name end,case when io.oper_id is null then op.oper_name else io.oper_name end, case when sd.senders_name is null then osd.order_senders_name else sd.senders_name end ,io.oper_name,   b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no",
                     QueryOrderSQL="ORDER BY   (case when osd.sheet_no is not null then osd.sheet_no else sd.order_sheet_no end ) "
                  }
                } 
            };             
        }


        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;

            if (DataItems["senders_id"].Value != "")
            {
                var sendersID = DataItems["senders_id"].Value.Split(",");
                var sql = "";
                foreach (var senderID in sendersID)
                {
                    if (sql != "") sql += " or ";
                    sql += $"','||sd.senders_id||',' like '%,{senderID},%'";
                }
                this.SQLVariables["senders_id"] = "and ( " + sql + " )";
            }
            else
            {
                this.SQLVariables["senders_id"] = "";
            }
            if (DataItems["supcust_id"].Value != "")
            {
                var supcustID = DataItems["supcust_id"].Value;
                this.SQLVariables["supcust_id"] = $" and ( oc.supcust_id ={supcustID} or sc.supcust_id = {supcustID} ) ";

            }
            else
            {
                this.SQLVariables["supcust_id"] = "";
            }

        }
    }



    [Route("api/[controller]/[action]")]
    public class OrderToSaleDifferencelController : QueryController
    { 
        public OrderToSaleDifferencelController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            OrderToSaleDifferencelModel model = new OrderToSaleDifferencelModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            OrderToSaleDifferencelModel model = new OrderToSaleDifferencelModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            OrderToSaleDifferencelModel model = new OrderToSaleDifferencelModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
