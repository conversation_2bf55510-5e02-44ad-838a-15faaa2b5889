using System;
using System.Collections.Generic;
using ArtisanManage.Services;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;

namespace ArtisanManage.Pages.BaseInfo 
{
    public class SupplierEditModel : PageFormModel
    {
        public SupplierEditModel(CMySbCommand cmd, string company_id="",string oper_id="") : base(Services.MenuId.infoSupplier)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"supcust_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"sup_name",new DataItem(){Title="名称",Necessary=true,FldArea="divHead"}},
                {"py_str",new DataItem(){Title="助记名",FldArea="divHead"}},
                {"boss_name",new DataItem(){Title="联系人",Necessary=true,FldArea="divHead"}},
                {"sup_addr",new DataItem(){Title="地址",FldArea="divHead"}},
                {"mobile",new DataItem(){Title="联系电话",FldArea="divHead"}},
                //{"supcust_flag",new DataItem(){Title="类型",FldArea="divHead",CtrlType="hidden", Value="S"}},
                {"supcust_flag",new DataItem(){Title="往来类型",FldArea="divHead",ButtonUsage="list", Necessary=true, Value="S",Label="供应商",Source = "[{v:'S',l:'供应商'},{v:'CS',l:'供应商/客户'}]"}},
                {"status",new DataItem(){Title="状态",LabelFld="cls_status_name",DropDownHeight="60",FldArea="divHead",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}},
                {"pay_info",new DataItem(){Title="支付信息",FldArea="divHead"}},
                {"acct_type",new DataItem(){Title="结算类型",SqlFld="info_supcust.acct_type", GetOptionsOnLoad=true, LabelFld="acct_type_name",FldArea="divHead",ButtonUsage="list",QueryOnChange = true,MaxRecords="500", LabelInDB = false,
                         Source = @"[{v:'pay',l:'现结',condition:""(s.acct_type = 'pay'or s.acct_type is null)""},
                                   {v:'arrears',l:'欠款',condition:""s.acct_type = 'arrears' ""},]"
                        }},

            };
 
            m_idFld = "supcust_id"; m_nameFld = "sup_name";//供货商名称
            m_tableName = "info_supcust";
            m_selectFromSQL = "from info_supcust where supcust_id='~ID'";

            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridManufactor" ,new FormDataGrid(){

                   MinRows=3,
                   Height=150,
                   PinColumnWidth=40,
                   AllowInsertRemoveRow=true,
                   AddRowsCount=3,
                   AutoAddRow=true,
                   Columns = new Dictionary<string, DataItem>()
                   {                    
                      // {"manufactor_id",new DataItem(){Title="单位",Width="70",Necessary=true, SqlForOptions="select unit_no from info_avail_unit order by order_index",GetOptionsOnLoad=true,ButtonUsage="list"}},
                        {"manufactor_id",new DataItem(){Title="编号",Width="60",EditableInFormGrid=true,Hidden=true}},
                        {"manufactor_name",new DataItem(){Title="名称",Width="150",Necessary=true}},                        
                       {"manufactor_addr",new DataItem(){Title="地址",Width="170"}},
                       {"manufactor_tel",new DataItem(){Title="联系电话",Width="100"}}
                   },
                   TableName="info_manufactor",
                   IdFld="supplier_id",
                   GridIdFldIsSerial=true,
                   GridIdFld="manufactor_id",
                   //通过supplier_id直接从info_manufactor中找制造商信息
                   //如何知道有哪些供应商？
                   SelectFromSQL=@"from info_manufactor where supplier_id='~ID' order by order_index"
                }}

            };
        }

        public async Task OnGet()
        { 
            await InitGet(cmd);  
        } 
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class SupplierEditController : BaseController
    {       
        public SupplierEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues)
        {
            SupplierEditModel model = new SupplierEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            SupplierEditModel model = new SupplierEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            
            SupplierEditModel model = new SupplierEditModel(cmd);
            Security.GetInfoFromOperKey((string)request.operKey, out string companyID);
            cmd.CommandText = $"select supcust_flag from info_supcust  where company_id={companyID}  and  sup_name='{request.sup_name}';";
            var supcust_flag = await cmd.ExecuteScalarAsync();
            var isNewRecord = request.isNewRecord.ToString();
            string mobile = request.mobile;
            if (supcust_flag != null && isNewRecord == "True")
            {
                if (supcust_flag.ToString() == "C")
                {
                    return new JsonResult(new {result = "Error", msg = "客户档案已存在同名的记录"});
                }
                else if (supcust_flag.ToString() == "S")
                {
                    return new JsonResult(new {result = "Error", msg = "供应商档案已存在同名的记录"});
                }
                else if (supcust_flag.ToString() == "W")
                {
                    return new JsonResult(new { result = "Error", msg = "费用单位档案已存在同名的记录" });
                }

            }
            //if (!string.IsNullOrEmpty(mobile))
            //{
            //    string condiOldCondi = "";
            //    string supcust_id = request.supcust_id;
            //    if (isNewRecord != "True")
            //    {
            //        condiOldCondi = $@"and supcust_id <> {supcust_id}";
            //    }
            //    string sql = $@"select supcust_flag from info_supcust where company_id={companyID} and TRIM(mobile)='{mobile}'{condiOldCondi} ";
            //    var rec = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //    if (rec != null && rec.Count > 0)
            //    {
            //        return new JsonResult(new { result = "Error", msg = "已存在相同手机号的客户或供应商" });
            //    }
            //}

            return await model.SaveTable(cmd, request);

        }
    }
}