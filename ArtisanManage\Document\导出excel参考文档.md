> 参考链接
>
> xlsx-style 官网  https://www.npmjs.com/package/xlsx-style
>
> 





# 1 单元格

| key  | 简介                                                  |
| ---- | ----------------------------------------------------- |
| v    | 原始值（有关更多信息，请参见“数据类型”部分）          |
| w    | 格式化文本（如果适用)                                 |
| t    | 单元格类型：b布尔值，n数字，e错误，s字符串，d日期     |
| f    | 单元格公式（如果适用）                                |
| r    | 富文本编码（如果适用）                                |
| h    | 富文本格式的HTML呈现（如果适用）                      |
| c    | 与单元格相关的评论**                                  |
| z    | 与单元格关联的数字格式字符串（如果要求）              |
| l    | 单元超链接对象（.Target包含链接，.tooltip为工具提示） |
| s    | 单元格的样式/主题（如果适用）                         |