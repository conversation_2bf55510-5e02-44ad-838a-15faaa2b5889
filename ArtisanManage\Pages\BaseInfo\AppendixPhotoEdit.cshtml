﻿@page
@model ArtisanManage.Pages.BaseInfo.AppendixPhotoEditModel
@{
    Layout = null;
}
<!DOCTYPE html>

 <head>
    <meta name="viewport" content="width=device-width" />
    <title>AppendixPhotoEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <style>
        .el-upload-dragger{
            width:148px;
            height:148px;
            border-width:2px

        }

        .el-upload--picture-card :hover {
            width: 148px;
            height: 148px;
            border-color: #87CEFA
        }

        .el-upload:focus .el-upload-dragger {
            border-color: #87CEFA
        }
        .el-upload--picture-card{
            border:none
        }
        #uploadArea{
            margin:10px
        }
        #hintText{
            color: #787878;
            font-size: 14px;
        
        }
        #bottomArea {
            position: absolute;
            bottom: 15px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        #btnArea {
            display: flex;
            margin-bottom:15px
        }
        #saveBtn{
            
            margin-right:15px 
        }
        #saveBtn:hover{
            background-color: #F0F8FF
        }

        #saveBtn:active{
            background-color: #e0F8ff
        }
        #closeBtn{
            margin-left:15px
        }
        
    </style>
 </head>

<body>
    <div id="app">
        <div id="uploadArea">
            <el-upload action="#"
                       list-type="picture-card"
                       drag
                       multiple
                       :disabled="disableUpload"
                       accept=".jpeg,.png,.jpg"
                       :limit="5"
                       :on-exceed="handleExceed"
                       :auto-upload="false"
                       :on-change="onFileChange"
                       :file-list="fileList">
                <i class="el-icon-upload"></i>
                
                <div slot="file" slot-scope="{file}">
                    <img class="el-upload-list__item-thumbnail"
                         :src="file.url" alt="">
                    <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview"
                              v-on:click="handlePreview(file)">
                            <i class="el-icon-zoom-in"></i>
                        </span>
                        @* <span v-if="!disabled"
                        class="el-upload-list__item-delete"
                        v-on:click="handleDownload(file)">
                        <i class="el-icon-download"></i>
                        </span> *@
                        <span v-if="!disableRemove"
                              class="el-upload-list__item-delete"
                              v-on:click="handleRemove(file)">
                            <i class="el-icon-delete"></i>
                        </span>
                    </span>
                </div>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible" width="850px">
                <img width="800px" :src="dialogImageUrl" alt="">
            </el-dialog>
        </div>
        <div id="bottomArea">
            <div id="btnArea">
                <button id="saveBtn" onclick="btnSaveAppendix()">保存</button>
                <button id="closeBtn" onclick="btnCloseAppendix()">关闭</button>
            </div>
            <div id="hintText">点击或拖拽上传图片文件（仅支持.jpg .jepg .png格式）</div>
        </div>
    </div>
    

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';

        @Html.Raw(Model.m_saveCloseScript)
            $(document).ready(function () {
               
                @Html.Raw(Model.m_showFormScript)
                    @Html.Raw(Model.m_createGridScript)

                    if(editable === 'false') {
                    // 如果指定了单据，要隐藏上传组件，上传组件在document加载好之后才能通过类名检索
                        $('.el-upload-dragger').css('display', 'none')
                        $('#btnArea').css('display','none')
                    }
               
            })
      
        window.addEventListener('message', function (event) {
            console.log("wojieshoulexiaoxi")
            debugger
            // 确保消息来自父页面
            if (event.data.msg !== 'loadPhoto' && event.data.msg !== 'loadPhotoView' && event.data.msg !== 'setDisable' && event.data.msg !== 'setAble') {
                return;
            }
            if (event.data.msg === 'setDisable') {
                debugger
                $('.el-upload-dragger').css('display', 'none')
                $('#btnArea').css('display', 'none')
                vm.disableUpload = true
                vm.disableRemove = true
            } else if (event.data.msg === 'setAble') {
                $('.el-upload-dragger').css('display', 'block')
                $('#btnArea').css('display', 'flex')
                vm.disableUpload = false
                vm.disableRemove = false
            } else if (event.data.msg === 'loadPhoto' || event.data.msg === 'loadPhotoView') {
                // 获取发送的对象数据
                let href = event.data.href
                processLoadImg(event.data.photo, href)
                
            }
            if (vm.fileList.length === 5) {
                vm.disableUpload = true
                $('.el-upload-dragger').css('display', 'none')
            }

            
        });
        var vm = new Vue({
            el: '#app',
            data: {
                disableUpload : false,
                disableRemove: false,
                dialogVisible: false,
                dialogImageUrl: '',
                disabled: false,
                fileList: [],
                origFileList: [],

            },
            methods: {
                
                handleRemove(file) {
                    console.log(this.disableUpload)
                    // 根据uid查找要删除的文件在fileList数组中的索引
                    let index = this.fileList.findIndex(item => item.uid === file.uid);
                    if (index !== -1) {
                        // 如果找到了对应的文件，则从fileList数组中移除
                        this.fileList.splice(index, 1);
                        if (this.fileList.length < 5) {
                            this.disableUpload = false
                            $('.el-upload-dragger').css('display', 'block')
                        }
                        console.log(this.fileList);
                    } else {
                        console.log('文件未找到');
                    }
                },

                handlePreview(file) {
                    this.dialogImageUrl = file.url;
                    this.dialogVisible = true;
                },
                handleDownload(file) {
                    console.log(file);
                },
                onFileChange(file, fileList) {
                    // 过滤非图片文件，好像accept可以直接过滤
                    let fileType = file.raw.type
                    if (fileType !== "image/jpeg" && fileType !== "image/jpg" && fileType !== "image/png") {
                        this.$message.error('不支持上传非图片文件');
                        // 移除文件
                        let index = fileList.findIndex(item => item.uid === file.uid);
                        this.fileList.splice(index, 1);
                        return false
                    }
                    let reader = new FileReader();
                    reader.onload = () => {
                        // 将文件的 base64 编码存储到 fileUrlList 中
                        // this.fileUrlList.push(reader.result);
                        file.raw.src = reader.result
                        console.log("get src")
                    };

                    reader.readAsDataURL(file.raw);

                    // 将文件对象存储到 fileList 中
                   
                    let url = URL.createObjectURL(file.raw);
                    file.raw.url = url
                    this.fileList.push(file.raw)
                    console.log("push raw")
                    debugger
                    if (this.fileList.length === 5) {
                        this.disableUpload = true
                        $('.el-upload-dragger').css('display', 'none')
                    } else if (this.fileList.length < 5) {
                        $('.el-upload-dragger').css('display', 'block')
                    }
                },
                beforeUpload(file) {
                    console.log(file.type)
                },
                handleExceed(files, fileList) {
                    this.$message.warning(`当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
                }
            }



                    });

        let sheet_Id = getQueryVariable("sheetId")
        let sheet_Type = getQueryVariable("sheetType")
        let editable = getQueryVariable("editable")
        if (editable === 'false') {
            debugger
            // 设置弹窗编辑区域以及按钮不可见
            vm.disableUpload = true
            vm.disableRemove = true
        }
        if (sheet_Id && sheet_Type) {

            
            //指定了sheet_id 查询图片附件
            $.ajax({
                url: '/api/AppendixPhotoEdit/GetSheetAppendixPhotos',
                type: 'GET',
                contentType: 'application/json',
                data: { operKey: g_operKey, sheetId: sheet_Id, sheetType: sheet_Type },
                success: function (data) {
                    // console.log(data)
                    if (data.result === "OK") {
                        debugger
                        var photoStr = data.photoStr
                        let href = getQueryVariable("href")
                        processLoadImg(photoStr,href)
                    }

                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText)
                }
            });

        }
        function processLoadImg_old(photoStr,href) {
            debugger
            let loadphoto = JSON.parse(photoStr)
            if (loadphoto.photos !== '' && loadphoto.photos !== null && loadphoto.photos !== undefined) {
                for (let p of loadphoto.photos) {
                    let photoUrl = href + p
                    vm.origFileList.push({ uid: uid, url: photoUrl, src: photoUrl })
                    uid++
                }
            }

            vm.fileList = [...vm.origFileList]; // 复制数组
            if (vm.fileList.length === 5) {
                vm.disableUpload = true
                $('.el-upload-dragger').css('display', 'none')
            }
        }

        function processLoadImg(photoStr, href) {
            debugger
            let loadphoto = JSON.parse(photoStr)
            if (loadphoto !== '' && loadphoto !== null && loadphoto !== undefined) {
                for (let p of loadphoto) {
                    let photoUrl = href + p
                    vm.origFileList.push({ uid: uid, url: photoUrl, src: photoUrl })
                    uid++
                }
            }

            vm.fileList = [...vm.origFileList]; // 复制数组
            // if (vm.fileList.length === 5) {
            //     vm.disableUpload = true
            //     $('.el-upload-dragger').css('display', 'none')
            // }
        }
        function btnSaveAppendix() {
            // 把照片加入msg里
            vm.origFileList = [...vm.fileList];        
            
            let fileSrcList = vm.fileList.map(file => file.src).filter(src => src);      
            var msg = { msgHead: 'AppendixPhotoEdit', action: 'close',appendix:vm.fileList,src:fileSrcList };
            
            window.parent.postMessage(msg, '*');
        }

        function btnCloseAppendix() {
            debugger
            vm.fileList = [...vm.origFileList]; // 复制数组
            if (vm.fileList.length < 5) {
                vm.disableUpload = false
            }
            let fileSrcList = vm.fileList.map(file => file.src).filter(src => src);
            var msg = { msgHead: 'AppendixPhotoEdit', action: 'close', appendix: vm.fileList, src: fileSrcList };

            window.parent.postMessage(msg, '*');
        }

        function getQueryVariable(variable) {
            debugger
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) { return pair[1]; }
            }
            return (false);
        }
        
       
       
    </script>

    
</body>


</html>