using ArtisanManage.Models;
using ArtisanManage.MyCW;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowBackBranch:SheetRowBase
    {
        public SheetRowBackBranch()
        {

        }
        public string need_move_stock_conv { get; set; }
        public string move_stock_conv { get; set; }
        public string sale_qty_conv { get; set; }
        public string item_van_stock { get; set; }
        public string mm_sheet_no { get; set; }
        [SaveToDB(false)][FromFld(false)] public override int inout_flag { get; set; } = 0;
        [SaveToDB(false)][FromFld(false)] public override int row_index { get; set; } = 0;
        [IDField][FromFld("op_id")] public override string sheet_id { get; set; }
        //[SaveToDB("op_id")][IDField][FromFld("op_id")] public override string sheet_id { get; set; }
        //[SaveToDB][FromFld] public string sale_order_sheet_id { get; set; }
        [SaveToDB][FromFld] public string item_id { get; set; }
        [FromFld("ip.item_name")]public string item_name { get; set; }
        [SaveToDB][FromFld] public string unit_no { get; set; }

        [SaveToDB][FromFld] public decimal unit_factor { get; set; } = 1;

        [SaveToDB][FromFld] public string back_unit_no { get; set; }

        [SaveToDB][FromFld] public decimal back_unit_factor { get; set; } = 1;

        [SaveToDB][FromFld] public string sale_unit_no { get; set; }

        [SaveToDB][FromFld] public decimal sale_unit_factor { get; set; } = 1;
        [SaveToDB][FromFld("case when t.move_qty is null then case when t.assign_van_type = 'reject' then reject_qty else back_qty end  else t.move_qty end")] public decimal move_qty { get; set; }

        [SaveToDB][FromFld("case when t.need_move_qty is null then case when t.assign_van_type='reject' then old_reject_qty else old_back_qty end  else t.need_move_qty end")] public decimal need_move_qty { get; set; }

        [SaveToDB][FromFld] public decimal sale_qty { get; set; }
        [SaveToDB][FromFld] public string sale_order_sheet_id { get; set; }
        [FromFld("som.sheet_no")] public string sale_order_sheet_no { get; set; }

        [SaveToDB][FromFld("case when t.back_type is null then t.assign_van_type else t.back_type end")] public string back_type { get; set; }

        [SaveToDB][FromFld] public string move_sheet_id { get; set; }
        [FromFld("smm.sheet_no")] public string move_sheet_no { get; set; }
        [SaveToDB][FromFld] public string sale_sheet_id { get; set; }
        [FromFld("sm.sheet_no")] public string sale_sheet_no { get; set; }
        public int move_status { get; set; }
        [SaveToDB][FromFld] public string is_previous_move { get; set; }
        [SaveToDB(false)][FromFld("case when t.back_type ='reject' then sp.supcust_id else sup.supcust_id end")] public string supcust_id { get; set; }
        [SaveToDB(false)][FromFld("case when t.back_type ='reject' then sp.sup_name else sup.sup_name end")] public string sup_name { get; set; }

        [SaveToDB][FromFld] public string back_branch { get; set; }
        [FromFld("b.branch_name")] public string back_branch_name { get; set; }

        [SaveToDB][FromFld] public string batch_id { get; set; }
        [SaveToDB(false)][FromFld(false)] public string batch_level { get; set; }
        [SaveToDB(false)][FromFld(false)] public string batch_no { get; set; }
        [SaveToDB(false)][FromFld("case when T.batch_id = 0 then '无产期' else to_char(iib.produce_date, 'YYYY-MM-DD') end")] public string produce_date { get; set; }
        [SaveToDB][FromFld] public string branch_position { get; set; }
        [SaveToDB(false)][FromFld(false)] public string branch_position_name { get; set; }

    }
    public class SheetBackBranch : SheetBase<SheetRowBackBranch>
    {
        [SaveToDB("op_id")][IDField][FromFld("op_id")] public override string sheet_id { get; set; }
        [SaveToDB("op_no")][FromFld("case when t.op_no is null then 'HK'||t.op_id else t.op_no end")] public override string sheet_no { get; set; } = "";
        [SaveToDB][FromFld] public string move_sheet_id { get; set; }
        [SaveToDB][FromFld] public string oper_id { get; set; }
        [FromFld("op.oper_name")] public string oper_name { get; set; }
        [SaveToDB][FromFld] public string senders_id { get; set; }
        [SaveToDB][FromFld] public string senders_name { get; set; }
        
      
        public dynamic rowInfo { get; set; }
        [SaveToDB(false)][FromFld(false)] public override string red_sheet_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string maker_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_time { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string approve_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string submit_time { get; set; } = "";

        [SaveToDB(false)][FromFld(false)] public override string maker_name { get; set; } = "";
        [FromFld("ofd.sale_order_sheets_id")] public string sale_order_sheets_id { get; set; }
        public string sale_order_sheets_no { get; set; }
        [FromFld("sm.sheet_no")] public string move_sheet_no { get; set; }

        [SaveToDB][FromFld("case when t.approver_id is null then sm.approver_id else t.approver_id end ")] public override string approver_id { get; set; } = "";
        [FromFld("case when t.approver_id is null then mover.approver_name else approver.approver_name end")] public override string approver_name { get; set; } = "";
        [SaveToDB][FromFld("to_char( case when t.approve_time is null then sm.approve_time else t.approve_time end , 'yyyy-MM-dd hh24:mi:ss' ) as approve_time")] public override string approve_time { get; set; } = "";

        public List<SheetRowMove> RejectedSheetRows = new List<SheetRowMove>();
        public List<SheetRowMove> ReturnedSheetRows = new List<SheetRowMove>();
        [SaveToDB][FromFld] public string from_branch { get; set; }
        [FromFld("b.branch_name")] public string from_branch_name { get; set; }

        //public Dictionary<string, string> lsSheets = new Dictionary<string, string>();
        public Dictionary<string, List<dynamic>> ItemsUnits = new Dictionary<string, List<dynamic>>();

  

        public class SumItemRow
        {
            public string item_id { get; set; }
            public string item_name { get; set; }
            public decimal need_move_qty { get; set; }
            public decimal move_qty { get; set; }
            public decimal sale_qty { get; set; }
            public decimal unit_factor { get; set; }
            public string unit_no { get; set; }
            public string b_unit_factor { get; set; }
            public string b_unit_no { get; set; }
            public string m_unit_factor { get; set; }
            public string m_unit_no { get; set; }
            public string s_unit_no { get; set; }
            public string back_type { get; set; }
            public string unit_relation1 { get; set; }

            public string batch_id { get; set; }
            public string batch_level { get; set; }
            public string batch_no { get; set; }
            public string produce_date { get; set; }
            public string branch_position { get; set; }//订单或者销售单里获取的库位
            public string branch_position_name { get; set; }
            public string back_branch_position { get; set; }
            public string back_branch_position_name { get; set; }
            public string back_branch { get; set; }
            public string back_branch_name { get; set; }
            public string remark { get; set; }
            public string move_sheet_id { get; set; }
            public string move_sheet_no { get; set; }
            public string isAdd { get; set; } = "false";
        }
        public List<SumItemRow> sumItemsRows = new List<SumItemRow>();
        [SaveToDB][FromFld] public string back_done { get; set; }
        public string rejectToBranchID { get; set; }
        public string returnToBranchID { get; set; }

        [SaveToDB(false)] public string rejectToBranchName { get; set; }
        
        [SaveToDB(false)]public string returnToBranchName { get; set; }

        public string rejectSheetNO { get; set; }
        public string rejectSheetID { get; set; }
        public string returnSheetNO { get; set; }
        public string returnSheetID { get; set; }

        [SaveToDB][FromFld("case when t.move_stock is null then ofd.move_stock else t.move_stock end")] public string move_stock { get; set; }


        public SheetBackBranch(LOAD_PURPOSE loadPurpose) : base("op_move_from_van_main", "op_move_from_van_row", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_BACK_BRANCH_VAN;
                MainLeftJoin = @" 	
                        LEFT JOIN info_branch b ON t.from_branch = b.branch_id AND b.company_id = ~COMPANY_ID
	                    LEFT JOIN ( SELECT oper_id, oper_name  FROM info_operator WHERE company_id = ~COMPANY_ID ) op on op.oper_id = t.oper_id 
	                    LEFT JOIN sheet_move_main sm on sm.company_id = t.company_id and sm.sheet_id = t.move_sheet_id
	                    LEFT JOIN ( SELECT oper_id, oper_name AS approver_name FROM info_operator WHERE company_id = ~COMPANY_ID ) approver ON t.approver_id = approver.oper_id 
	                    LEFT JOIN ( SELECT oper_id, oper_name AS approver_name FROM info_operator WHERE company_id = ~COMPANY_ID ) mover ON sm.approver_id = mover.oper_id 
	                    LEFT JOIN ( 
                            SELECT DISTINCT op_id , so.move_stock,string_agg(sale_order_sheet_id::text,',') sale_order_sheets_id 
                            FROM op_move_from_van_detail d 
		                    LEFT JOIN sheet_status_order so on so.company_id = d.company_id and d.sale_order_sheet_id = so.sheet_id
	                        WHERE d.company_id = ~COMPANY_ID GROUP BY op_id,so.move_stock ) ofd on ofd.op_id= t.op_id
                ";
                DetailLeftJoin = $@" 
	                    LEFT JOIN info_item_prop ip ON ip.company_id = T.company_id AND ip.item_id = T.item_id
	                    LEFT JOIN sheet_sale_main sm ON sm.company_id = T.company_id AND sm.sheet_id = T.sale_sheet_id
	                    LEFT JOIN sheet_sale_order_main som ON som.company_id = T.company_id AND som.sheet_id = T.sale_order_sheet_id
	                    LEFT JOIN sheet_move_main smm ON smm.company_id = T.company_id AND smm.sheet_id = T.move_sheet_id
	                    LEFT JOIN info_branch b on b.company_id = t.company_id and b.branch_id = t.back_branch
                        LEFT JOIN info_supcust sup on sup.company_id = sm.company_id and sup.supcust_id = sm.supcust_id
						LEFT JOIN info_supcust sp on sp.company_id = som.company_id and sp.supcust_id = som.supcust_id
                        LEFT JOIN info_item_batch iib on iib.batch_id = T.batch_id and iib.company_id = T.company_id
                ";   
        }

        public async Task<string> LoadSheet(CMySbCommand cmd,string operKey, string companyID, string op_id,string orderSheetsID)
		{
            List<string> itemsID = new List<string>();
            if (!op_id.IsValid() && orderSheetsID.IsValid())
			{
                this.sale_order_sheets_id = orderSheetsID;

                List<SheetSaleOrder> lstOrderSheets = null;
                List<SheetSale> lstSaleSheets = null;
                List<SheetRowSale> lstReturnedRows = new List<SheetRowSale>();
                SheetSaleOrder sumOrderSheet_noReturn = null;
                SheetSale sumSaleSheet_noReturn = null;

                List<dynamic> lstReturnedSheets = new List<dynamic>();
                List<dynamic> lstRejectedSheets = new List<dynamic>();
                string previousSql = "";
                dynamic previousRows = null;
                previousSql = $@"SELECT ofr.* FROM op_move_from_van_row ofr 
LEFT JOIN op_move_from_van_main ofm on ofr.company_id = ofm.company_id and ofr.op_id = ofm.op_id
WHERE ofr.company_id = {companyID} and ofm.red_flag is null and ofm.approve_time is not null and ofr.is_previous_move is null and ofr.sale_order_sheet_id in({orderSheetsID}); ";
                previousRows = await CDbDealer.GetRecordsFromSQLAsync(previousSql, cmd);
                string saleSheetIDs = "";

                {
                    SheetSaleOrder.GetSheetsUsage usage = new SheetSaleOrder.GetSheetsUsage();
                    usage.GetSumSheet = true;
                    usage.GetEachSheet = true;
                    usage.SplitUnitRows = false;
                    SheetSaleOrder.GetSheetsResult res = await SheetSaleOrder.GetItemSheets<SheetSaleOrder, SheetRowSaleOrder>(cmd, operKey, orderSheetsID, usage, true, "0", "", "");
                    string van_id = "";
                    string van_name = "";
                    sumOrderSheet_noReturn = res.sheetGroup[0].sheets[0];
                    lstOrderSheets = res.sheetGroup[1].sheets;
                    string orderSheetIDs = "";
                    foreach (var dSheet in res.sheetGroup[1].sheets)
                    {
                        SheetSaleOrder sheet = (SheetSaleOrder) dSheet;
                        if (sheet.order_status != "zd")
                        {
                            return   "订单" + sheet.sheet_no + "尚未转单";
                        }

                        if (van_id != "" && van_id != sheet.van_id)
                        {
                            return   "选中订单不是装到一个车的";
                        }
                        else
                        {
                            van_id = sheet.van_id;
                            van_name = sheet.van_name;
                        }
                        if (sumOrderSheet_noReturn.move_stock != sheet.move_stock)
                        {
                            return "选中订单装车时有的转移库存，有的不转移库存，不能一起回库";
                        }
                        move_stock = sheet.move_stock;
                        if (orderSheetIDs != "") orderSheetIDs += ",";
                        orderSheetIDs += sheet.sheet_id;
                        if (saleSheetIDs != "" && sheet.sale_sheet_id != "") saleSheetIDs += ",";
                        saleSheetIDs += sheet.sale_sheet_id; 
                    }
                    if (van_id.IsValid())
                    {
                        this.from_branch = van_id;
                        this.from_branch_name = van_name;
                    }
                    
                }
                if (saleSheetIDs != "")
                {
                    SheetSale.GetSheetsUsage usage = new SheetSale.GetSheetsUsage();
                    usage.GetSumSheet = true;
                    usage.GetEachSheet = true;
                    usage.SplitUnitRows = false;
                    SheetSale.GetSheetsResult res = await SheetSale.GetItemSheets<SheetSale, SheetRowSale>(cmd, operKey, saleSheetIDs, usage, true, "0", "", "");


                    sumSaleSheet_noReturn = res.sheetGroup[0].sheets[0];
                    lstSaleSheets = res.sheetGroup[1].sheets;

                }

                var lstSaleRows = new List<SheetRowSale>();
                var lstOrderRows = new List<SheetRowSaleOrder>();

                foreach (var orderSheet in lstOrderSheets)
                {
                    List<SheetRowSale> saleSheetRows_notReturn = new List<SheetRowSale>();
                    List<SheetRowSale> lstCurReturnedRows = new List<SheetRowSale>();
                    
                    
                    SheetSale saleSheet = null;
                    if (lstSaleSheets != null)
                    {
                        saleSheet = lstSaleSheets.Find(sht => sht.order_sheet_id == orderSheet.sheet_id);
                    }

                    if (saleSheet != null)
                    {
                        if (this.from_branch.IsInvalid())
                        {
                            this.from_branch = saleSheet.branch_id;
                            this.from_branch_name = saleSheet.branch_name;
                        }
                        if (saleSheet.sheet_type == SHEET_TYPE.SHEET_SALE_RETURN)
                        {
                            saleSheet.SheetRows.ForEach(row =>
                            {
                                lstCurReturnedRows.Add(row);
                            });

                        }
                        else
                        {
                            GetReturnRows(saleSheet.SheetRows, lstCurReturnedRows, out bool hasReturn);
                        }
                        if (lstCurReturnedRows.Count > 0)
                        {
                            dynamic returnedSheet = new { supcust_id = saleSheet.supcust_id,sup_name = saleSheet.sup_name, sale_sheet_id = saleSheet.sheet_id, order_sheet_id = saleSheet.order_sheet_id,order_sheet_no = orderSheet.sheet_no, sheet_no = saleSheet.sheet_no, sheet_type = saleSheet.SheetType, sheetRows = new List<dynamic>() };
                            foreach (var row in lstCurReturnedRows)
                            {
                                returnedSheet.sheetRows.Add(new { row.item_id, row.item_name, row.quantity, row.unit_no, row.unit_factor, row.remark, row.b_unit_no, row.b_unit_factor, row.m_unit_no, row.m_unit_factor, row.s_unit_no, row.unit_relation, row.unit_relation1, row.quantity_unit_conv, row.branch_id, row.branch_name, row.branch_position, row.branch_position_name, row.batch_id, row.batch_level, row.batch_no, row.produce_date, row.show_produce_date });
                            }
                            lstReturnedSheets.Add(returnedSheet);
                            lstReturnedRows.AddRange(lstCurReturnedRows);
                        }
                        //saleSheetRows_notReturn = saleSheet.MergeSheetRows(saleSheet.SheetRows, true);
                        saleSheetRows_notReturn = saleSheet.MergeSheetRowsByBatch(saleSheet.SheetRows, true);
                    }
                    var orderSheetRows_notReturn = new List<SheetRowSaleOrder>();
                    if(orderSheet.sheet_type==SHEET_TYPE.SHEET_SALE_DD)
                        orderSheetRows_notReturn = orderSheet.MergeSheetRowsByBatch(orderSheet.SheetRows, true);
                    //var orderSheetRows_notReturn = orderSheet.MergeSheetRowsByBatch(orderSheet.SheetRows, true);
                    if (this.from_branch.IsInvalid())
                    {
                        this.from_branch = orderSheet.branch_id;
                        this.from_branch_name = orderSheet.branch_name;
                    }
                    List<SheetRowSale> lstCurRejectedRows;
                    GetRejectedRows(saleSheetRows_notReturn, orderSheetRows_notReturn, false, out lstCurRejectedRows);
                    if (lstCurRejectedRows.Count > 0)
                    {
                        var saleSheetNo = "";
                        if (saleSheet != null) saleSheetNo = saleSheet.sheet_no;
                        dynamic rejectedSheet = new { supcust_id =orderSheet.supcust_id,sup_name = orderSheet.sup_name, order_sheet_id = orderSheet.sheet_id, sale_sheet_id = orderSheet.sale_sheet_id,sale_sheet_no= saleSheetNo, sheet_no = orderSheet.sheet_no, sheet_type = orderSheet.SheetType, sheetRows = new List<dynamic>() };
                        foreach (var row in lstCurRejectedRows)
                        {
                            rejectedSheet.sheetRows.Add(new { row.item_id, row.item_name, row.quantity, row.unit_no, row.unit_factor, row.remark, row.b_unit_no, row.b_unit_factor, row.m_unit_no, row.m_unit_factor, row.s_unit_no, row.unit_relation, row.unit_relation1, row.quantity_unit_conv,row.branch_id,row.branch_name,row.branch_position,row.branch_position_name,row.batch_id,row.batch_level,row.batch_no,row.produce_date,row.show_produce_date });
                        }
                        lstRejectedSheets.Add(rejectedSheet);
                    }
                }

                //if (sumSaleSheet_noReturn != null)
                //    lstReturnedRows = sumSaleSheet_noReturn.MergeSheetRowsByBatchAndItem_noPrice(lstReturnedRows);
                    //lstReturnedRows = sumSaleSheet_noReturn.MergeSheetRows(lstReturnedRows);
                
                //foreach (var row in lstReturnedRows)
                //{
                //    if (row.item_id == "2044938")
                //    {

                //    }
                //    var quantity = row.quantity * row.unit_factor;
                //    SumItemRow sR = new SumItemRow();
                //    sR.item_id = row.item_id;
                //    sR.item_name = row.item_name;
                //    sR.need_move_qty = quantity;
                //    sR.move_qty = quantity;
                //    sR.sale_qty = 0;
                //    sR.unit_factor = 1;
                //    sR.unit_no = row.s_unit_no;
                //    sR.s_unit_no = row.s_unit_no;
                //    sR.m_unit_factor = CPubVars.ToDecimal(row.m_unit_factor);
                //    sR.m_unit_no = row.m_unit_no;
                //    sR.b_unit_factor =CPubVars.ToDecimal(row.b_unit_factor);
                //    sR.b_unit_no = row.b_unit_no;
                //    sR.back_type = "return";
                //    sR.unit_relation1 = row.unit_relation1;
                //    if(previousRows.Count > 0)
                //    {
                //        decimal needMoveQty = 0;
                //        foreach(var pRow in previousRows)
                //        {
                //            if(pRow.back_type=="return" && pRow.item_id == sR.item_id)
                //            {
                //                needMoveQty += CPubVars.ToDecimal(pRow.need_move_qty) * CPubVars.ToDecimal(pRow.unit_factor) - CPubVars.ToDecimal(pRow.move_qty) * CPubVars.ToDecimal(pRow.back_unit_factor)- CPubVars.ToDecimal(pRow.sale_qty) * CPubVars.ToDecimal(pRow.sale_unit_factor);
                //            }
                //        }
                //        sR.need_move_qty = needMoveQty;
                //        sR.move_qty = needMoveQty;
                //    }
                //    this.sumItemsRows.Add(sR);
                //    //this.sumItemsRows.Add(new { row.item_id, row.item_name, need_move_qty = quantity, unit_factor=1, unit_no=row.s_unit_no, move_qty = row.quantity,sale_qty =0, row.b_unit_no, row.b_unit_factor, row.m_unit_no, row.m_unit_factor, row.s_unit_no, row.unit_relation, row.unit_relation1, row.quantity_unit_conv,back_type="return" });
                //    if (!itemsID.Contains(row.item_id)) itemsID.Add(row.item_id);
                //}

                //if (sumSaleSheet_noReturn == null) sumSaleSheet_noReturn = new SheetSale();

                //GetRejectedRows(sumSaleSheet_noReturn.SheetRows, sumOrderSheet_noReturn.SheetRows, true, out List<SheetRowSale> lstRejectedRows);

                //foreach (var row in lstRejectedRows)
                //{
                //    if(row.item_id == "2044938")
                //    {

                //    }
                //    var quantity = row.quantity * row.unit_factor;
                //    SumItemRow sR = new SumItemRow();
                //    sR.item_id = row.item_id;
                //    sR.item_name = row.item_name;
                //    sR.need_move_qty = quantity;
                //    sR.move_qty = quantity;
                //    sR.sale_qty = 0;
                //    sR.unit_factor = 1;
                //    sR.unit_no = row.s_unit_no;
                //    sR.s_unit_no = row.s_unit_no;
                //    sR.m_unit_factor = CPubVars.ToDecimal(row.m_unit_factor);
                //    sR.m_unit_no = row.m_unit_no;
                //    sR.b_unit_factor = CPubVars.ToDecimal(row.b_unit_factor);
                //    sR.b_unit_no = row.b_unit_no;
                //    sR.back_type = "reject";
                //    sR.unit_relation1 = row.unit_relation1;
                //    if (previousRows.Count > 0)
                //    {
                //        decimal needMoveQty = 0;
                //        foreach (var pRow in previousRows)
                //        {
                //            if (pRow.back_type == "reject" && pRow.item_id == sR.item_id)
                //            {
                //                needMoveQty += CPubVars.ToDecimal(pRow.need_move_qty) * CPubVars.ToDecimal(pRow.unit_factor) - CPubVars.ToDecimal(pRow.move_qty) * CPubVars.ToDecimal(pRow.back_unit_factor) - CPubVars.ToDecimal(pRow.sale_qty) * CPubVars.ToDecimal(pRow.sale_unit_factor);
                //            }
                //        }
                //        sR.need_move_qty = needMoveQty;
                //        sR.move_qty = needMoveQty;
                //    }
                //    this.sumItemsRows.Add(sR);
                //    //this.sumItemsRows.Add(new { row.item_id, row.item_name, need_move_qty = quantity, unit_factor = 1, unit_no = row.s_unit_no, move_qty = row.quantity, sale_qty = 0, row.b_unit_no, row.b_unit_factor, row.m_unit_no, row.m_unit_factor, row.s_unit_no, row.unit_relation, row.unit_relation1, row.quantity_unit_conv, back_type = "reject" });
                //    if (!itemsID.Contains(row.item_id)) itemsID.Add(row.item_id);
                //}

                foreach(var sht in lstReturnedSheets)
                {
                    
                    foreach (var row in sht.sheetRows)
                    {
                        SheetRowBackBranch rowBackBranch = new SheetRowBackBranch();
                        rowBackBranch.sale_order_sheet_id = sht.order_sheet_id;
                        rowBackBranch.sale_order_sheet_no = sht.order_sheet_no;
                        rowBackBranch.sale_sheet_id = sht.sale_sheet_id;
                        rowBackBranch.sale_sheet_no = sht.sheet_no;
                        rowBackBranch.back_type = "return";
                        rowBackBranch.move_sheet_id = "";
                        rowBackBranch.back_branch = "";
                        rowBackBranch.back_branch_name = "";
                        rowBackBranch.sup_name = sht.sup_name;
                        rowBackBranch.supcust_id = sht.supcust_id;
                        rowBackBranch.item_id = row.item_id;
                        rowBackBranch.item_name = row.item_name;
                        rowBackBranch.unit_no = row.unit_no;
                        rowBackBranch.unit_factor = row.unit_factor;
                        rowBackBranch.back_unit_factor = row.unit_factor;
                        rowBackBranch.back_unit_no = row.unit_no;
                        rowBackBranch.sale_qty = 0;
                        rowBackBranch.sale_unit_factor = row.unit_factor;
                        rowBackBranch.sale_unit_no = row.unit_no;
                        rowBackBranch.batch_id = row.batch_id;
                        rowBackBranch.batch_level = row.batch_level;
                        rowBackBranch.batch_no = row.batch_no;
                        rowBackBranch.produce_date= row.produce_date;
                        rowBackBranch.branch_position = row.branch_position;
                        rowBackBranch.branch_position_name = row.branch_position_name;
                        bool bPreviousRowsMet = false;
                        if (previousRows.Count > 0)
                        {
                            decimal qty = 0;
                            foreach(var pRow in previousRows)
                            {
                                if(pRow.sale_sheet_id == sht.sale_sheet_id && pRow.back_type == "return" && pRow.item_id == row.item_id &&pRow.unit_no == row.unit_no )
                                {
                                    qty = CPubVars.ToDecimal( pRow.need_move_qty)* CPubVars.ToDecimal(pRow.unit_factor)- CPubVars.ToDecimal(pRow.move_qty)* CPubVars.ToDecimal(pRow.back_unit_factor) - CPubVars.ToDecimal(pRow.sale_qty) * CPubVars.ToDecimal(pRow.sale_unit_factor);
                                    rowBackBranch.need_move_qty = qty/rowBackBranch.unit_factor;
                                    rowBackBranch.move_qty = rowBackBranch.need_move_qty;
                                    bPreviousRowsMet=true;
                                    break;
                                }
                            }
                            if(bPreviousRowsMet&&Math.Floor(rowBackBranch.need_move_qty)!= rowBackBranch.need_move_qty)
                            {
                                rowBackBranch.need_move_qty = qty;
                                rowBackBranch.move_qty = qty;
                                rowBackBranch.unit_no = row.s_unit_no;
                                rowBackBranch.unit_factor = 1;
                                rowBackBranch.back_unit_factor = 1;
                                rowBackBranch.back_unit_no = row.s_unit_no;
                                rowBackBranch.sale_qty = 0;
                                rowBackBranch.sale_unit_factor = 1;
                                rowBackBranch.sale_unit_no = row.s_unit_no;
                            }
                            
                        }
                        if(!bPreviousRowsMet)
                        {
                            rowBackBranch.need_move_qty = row.quantity;
                            rowBackBranch.move_qty = row.quantity;

                        }
                        this.SheetRows.Add(rowBackBranch);
                    }
                    

                }
                foreach(var sht in lstRejectedSheets)
                {
                    SheetRowBackBranch rowBackBranch = new SheetRowBackBranch();
                    rowBackBranch.sale_order_sheet_id = sht.order_sheet_id;
                    rowBackBranch.sale_order_sheet_no = sht.sheet_no;
                    rowBackBranch.sale_sheet_id = sht.sale_sheet_id;
                    rowBackBranch.sale_sheet_no = sht.sale_sheet_no;
                    rowBackBranch.back_type = "reject";
                    rowBackBranch.move_sheet_id = "";
                    rowBackBranch.back_branch = "";
                    rowBackBranch.back_branch_name = "";
                    rowBackBranch.sup_name = sht.sup_name;
                    rowBackBranch.supcust_id = sht.supcust_id;
                    foreach (var row in sht.sheetRows)
                    {
                        rowBackBranch.item_id = row.item_id;
                        rowBackBranch.item_name = row.item_name;
                        rowBackBranch.unit_no = row.unit_no;
                        rowBackBranch.unit_factor = row.unit_factor;
                        rowBackBranch.back_unit_factor = row.unit_factor;
                        rowBackBranch.back_unit_no = row.unit_no;
                        rowBackBranch.sale_qty = 0;
                        rowBackBranch.sale_unit_factor = row.unit_factor;
                        rowBackBranch.sale_unit_no = row.unit_no;
                        rowBackBranch.batch_id = row.batch_id;
                        rowBackBranch.batch_level = row.batch_level;
                        rowBackBranch.batch_no = row.batch_no;
                        rowBackBranch.produce_date = row.produce_date;
                        rowBackBranch.branch_position = row.branch_position;
                        rowBackBranch.branch_position_name = row.branch_position_name;
                        bool bPreviousRowsMet = false;
                        if (previousRows.Count > 0)
                        {
                            decimal qty = 0;
                            foreach (var pRow in previousRows)
                            {
                                if (pRow.sale_order_sheet_id == sht.order_sheet_id && pRow.back_type == "reject" && pRow.item_id == row.item_id && pRow.unit_no == row.unit_no)
                                {
                                    qty = CPubVars.ToDecimal(pRow.need_move_qty) * CPubVars.ToDecimal(pRow.unit_factor) - CPubVars.ToDecimal(pRow.move_qty) * CPubVars.ToDecimal(pRow.back_unit_factor) - CPubVars.ToDecimal(pRow.sale_qty) * CPubVars.ToDecimal(pRow.sale_unit_factor);
                                    rowBackBranch.need_move_qty = qty / rowBackBranch.unit_factor;
                                    rowBackBranch.move_qty = rowBackBranch.need_move_qty;
                                    bPreviousRowsMet = true;
                                    break;
                                }
                            }
                            if (bPreviousRowsMet&&Math.Floor(rowBackBranch.need_move_qty) != rowBackBranch.need_move_qty)
                            {
                                rowBackBranch.need_move_qty = qty;
                                rowBackBranch.move_qty = qty;
                                rowBackBranch.unit_no = row.s_unit_no;
                                rowBackBranch.unit_factor = 1;
                                rowBackBranch.back_unit_factor = 1;
                                rowBackBranch.back_unit_no = row.s_unit_no;
                                rowBackBranch.sale_qty = 0;
                                rowBackBranch.sale_unit_factor = 1;
                                rowBackBranch.sale_unit_no = row.s_unit_no;
                            }

                        }
                        if(!bPreviousRowsMet)
                        {
                            rowBackBranch.need_move_qty = row.quantity;
                            rowBackBranch.move_qty = row.quantity;

                        }
                        this.SheetRows.Add(JsonConvert.DeserializeObject<SheetRowBackBranch>(JsonConvert.SerializeObject(rowBackBranch)) );
                    }

                }
                foreach(var row in this.SheetRows)
                {
                    SumItemRow sr = new SumItemRow();
                    sr.item_id = row.item_id;
                    sr.item_name = row.item_name;
                    sr.need_move_qty = row.need_move_qty * row.unit_factor;
                    sr.move_qty = row.move_qty * row.back_unit_factor;
                    sr.sale_qty = row.sale_qty * row.sale_unit_factor;
                    sr.unit_factor = 1;
                    sr.back_type = row.back_type;
                    sr.batch_id = row.batch_id;
                    sr.batch_level = row.batch_level;
                    sr.batch_no = row.batch_no;
                    sr.produce_date = row.produce_date;
                    sr.branch_position = row.branch_position;
                    sr.branch_position_name = row.branch_position_name;
                    var exist = false;
                    foreach(var item in this.sumItemsRows)
                    {
                        if(row.back_type == item.back_type && row.item_id == item.item_id &&row.batch_id == item.batch_id &&row.branch_position==item.branch_position &&row.produce_date == item.produce_date)
                        {
                            exist = true;
                            item.need_move_qty += row.need_move_qty * row.unit_factor;
                            item.move_qty += row.move_qty * row.back_unit_factor;
                            item.sale_qty += row.sale_qty * row.sale_unit_factor;
                        }
                    }
                    if (!exist) {
                        this.sumItemsRows.Add(sr);
                        if (!itemsID.Contains(row.item_id)) itemsID.Add(row.item_id);
                    }


                }
                Dictionary<string, List<dynamic>> dicItemUnit = new Dictionary<string, List<dynamic>>();
                if (itemsID.Count() > 0)
                {
                    string itemsId = string.Join(',', itemsID);
                    string sql = $@"SELECT item_id,unit_no,unit_type,unit_factor,barcode,wholesale_price FROM info_item_multi_unit WHERE company_id ={companyID} and item_id in ({itemsId}) ORDER BY item_id ";
                    dynamic itemUnits = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                    if (itemUnits.Count > 0)
                    {
                        foreach (var id in itemsID)
                        {
                            List<dynamic> lstItemUnit = new List<dynamic>();
                            foreach (var unit in itemUnits)
                            {
                                if (unit.item_id == id)
                                {
                                    lstItemUnit.Add(unit);
                                }
                            }
                            dicItemUnit.Add(id, lstItemUnit);
                            foreach(var sr in this.sumItemsRows)
                            {
                                if(sr.item_id == id)
                                {
                                    foreach(var u in lstItemUnit)
                                    {
                                        if(u.unit_type == "b")
                                        {
                                            sr.b_unit_no = u.unit_no;
                                            sr.b_unit_factor = u.unit_factor;
                                        }else if(u.unit_type == "m")
                                        {
                                            sr.m_unit_no = u.unit_no;
                                            sr.m_unit_factor = u.unit_factor;
                                        }
                                        else if (u.unit_type == "s")
                                        {
                                            sr.s_unit_no = u.unit_no;
                                            sr.unit_no = u.unit_no;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    this.ItemsUnits = dicItemUnit;
                }


                this.senders_id = sumOrderSheet_noReturn.senders_id;
                if (this.senders_id == "") this.senders_id = sumOrderSheet_noReturn.assigned_senders_id;

                this.senders_name = sumOrderSheet_noReturn.senders_name;
                if (this.senders_name == "") this.senders_name = sumOrderSheet_noReturn.assigned_senders_name;

            }
            else if (op_id.IsValid()&&!orderSheetsID.IsValid())
            {
                var loadInfo = await base.Load(cmd, companyID, op_id);
                if (loadInfo!=null &&loadInfo.ErrMsg != "") return loadInfo.ErrMsg;
                string sumItemsRowSql = $@"SELECT oi.*,ip.item_name,ip.batch_level,ib.produce_date,ib.batch_no,d.b_unit_factor,d.m_unit_factor,d.b_unit_no,d.m_unit_no,d.s_unit_no,bp.branch_position_name ,b.branch_name back_branch_name, bb.branch_position_name back_branch_position_name, sm.sheet_no move_sheet_no FROM op_move_from_van_item oi
LEFT JOIN info_item_prop ip on oi.company_id = ip.company_id and oi.item_id = ip.item_id 
left join 
(
    select item_id,s_barcode, m_barcode,b_barcode,m_unit_no,s_unit_no,b_unit_no,b_unit_factor,m_unit_factor,
    yj_get_unit_relation(b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no) as unit_conv
    from 
    (
        select item_id,(s->>'f1')::real as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_barcode,
                       (m->>'f1')::real as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_barcode,
                       (b->>'f1')::real as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_barcode
        from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode)) as json from info_item_multi_unit where company_id = {companyID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb) 
    )t
) d on oi.item_id = d.item_id and oi.company_id={companyID}
LEFT JOIN info_item_batch ib on ib.company_id = oi.company_id and ib.batch_id = oi.batch_id
LEFT JOIN info_branch_position bp on bp.company_id = oi.company_id and bp.branch_position = oi.branch_position
LEFT JOIN info_branch b on b.company_id = oi.company_id and oi.back_branch = b.branch_id
LEFT JOIN info_branch_position bb on bb.company_id = oi.company_id and oi.back_branch = bb.branch_id and bb.branch_position = oi.back_branch_position
LEFT JOIN sheet_move_main sm on sm.company_id = oi.company_id and sm.sheet_id = oi.move_sheet_id
WHERE oi.company_id = {companyID} and op_id = {op_id}";
                dynamic sumItems = await CDbDealer.GetRecordsFromSQLAsync(sumItemsRowSql, cmd);
                var hasSumData = false;
                if (sumItems.Count > 0)
                {
                    hasSumData = true;
                    foreach (var re in sumItems)
                    {
                        SumItemRow sR = JsonConvert.DeserializeObject<SumItemRow>(JsonConvert.SerializeObject(re));
                        if (!itemsID.Contains(sR.item_id)) itemsID.Add(sR.item_id);
                        this.sumItemsRows.Add(sR);
                    }
                }
                foreach (var row in this.SheetRows)
                {
                    if(approve_time.IsInvalid())row.sheet_id = "";
                    if (!itemsID.Contains(row.item_id)) itemsID.Add(row.item_id);
                }
                Dictionary<string, List<dynamic>> dicItemUnit = new Dictionary<string, List<dynamic>>();
                if (itemsID.Count() > 0)
                {
                    string itemsId = string.Join(',', itemsID);
                    string sql = $@"SELECT item_id,unit_no,unit_type,unit_factor,barcode,wholesale_price FROM info_item_multi_unit WHERE company_id ={companyID} and item_id in ({itemsId}) ORDER BY item_id ";
                    dynamic itemUnits = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                    if (itemUnits.Count > 0)
                    {
                        foreach (var id in itemsID)
                        {
                            List<dynamic> lstItemUnit = new List<dynamic>();
                            foreach (var unit in itemUnits)
                            {
                                if (unit.item_id == id&&unit.unit_no!="")
                                {
                                    lstItemUnit.Add(unit);
                                }
                            }
                            dicItemUnit.Add(id, lstItemUnit);
                        }
                    }
                    this.ItemsUnits = dicItemUnit;
                }
                foreach (var row in this.SheetRows)
                {
                    if(row.back_type == "return")
                    {
                        returnToBranchID = row.back_branch;
                        returnToBranchName = row.back_branch_name;
                        returnSheetID = row.move_sheet_id;
                        returnSheetNO = row.move_sheet_no;

                    }
                    else if(row.back_type == "reject")
                    {
                        rejectToBranchID = row.back_branch;
                        rejectToBranchName = row.back_branch_name;
                        rejectSheetID = row.move_sheet_id;
                        rejectSheetNO = row.move_sheet_no;
                    }
                    var units = this.ItemsUnits[row.item_id];
                    string sUnitNo = "", mUnitNo = "", bUnitNo = "";
                    decimal mUnitFactor = 0, bUnitFactor = 0;
                    foreach (var unit in units)
                    {
                        if (unit.unit_type == "s" && unit.unit_no != "")
                        {
                            sUnitNo = unit.unit_no;
                        }
                        else if (unit.unit_type == "m"&& unit.unit_no!="")
                        {
                            mUnitNo = unit.unit_no;
                            mUnitFactor = CPubVars.ToDecimal(unit.unit_factor);
                        }
                        else if (unit.unit_type == "b" && unit.unit_no != "")
                        {
                            bUnitNo = unit.unit_no;
                            bUnitFactor = CPubVars.ToDecimal(unit.unit_factor);
                        }
                    }
                    if (!hasSumData)
                    {


                        SumItemRow sR = new SumItemRow();
                        sR.item_id = row.item_id;
                        sR.item_name = row.item_name;
                        sR.need_move_qty = row.need_move_qty * row.unit_factor;
                        sR.move_qty = row.move_qty * row.back_unit_factor;
                        sR.sale_qty = row.sale_qty * row.sale_unit_factor;
                        sR.unit_factor = 1;
                        sR.unit_no = sUnitNo;
                        sR.s_unit_no = sUnitNo;
                        sR.m_unit_factor = mUnitFactor == 0?"":mUnitFactor.ToString();
                        sR.m_unit_no = mUnitNo;
                        sR.b_unit_factor = bUnitFactor==0?"": bUnitFactor.ToString();
                        sR.b_unit_no = bUnitNo;
                        sR.back_type = row.back_type;
                        sR.produce_date = row.produce_date;
                        sR.batch_id = row.batch_id;
                        sR.batch_level = row.batch_level;
                        sR.batch_no = row.batch_no;
                        sR.branch_position = row.branch_position;
                        sR.branch_position_name = row.branch_position_name;
                        if(sR.back_type == "return")
                        {
                            sR.back_branch = returnToBranchID;
                            sR.back_branch_name = returnToBranchName;
                        }
                        else if(sR.back_type =="reject")
                        {
                            sR.back_branch = rejectToBranchID;
                            sR.back_branch_name = rejectToBranchName;

                        }
                        if (this.sumItemsRows.Count > 0)
                        {
                            bool rowExist = false;
                            foreach (var sRow in this.sumItemsRows)
                            {
                                if (row.item_id == sRow.item_id && row.back_type == sRow.back_type && row.batch_id == sRow.batch_id && sR.produce_date == sRow.produce_date &&row.branch_position == sRow.branch_position)
                                {
                                    rowExist = true;
                                    sRow.need_move_qty += row.need_move_qty * row.unit_factor;
                                    sRow.move_qty += row.move_qty * row.back_unit_factor;
                                    sRow.sale_qty += row.sale_qty * row.sale_unit_factor;
                                    break;
                                }
                            }
                            if (!rowExist)
                            {
                                this.sumItemsRows.Add(sR);
                            }

                        }
                        else
                        {
                            this.sumItemsRows.Add(sR);
                        }
                    }
                }

            }
            else
            {
                return "回库信息获取错误";
            }
            

            return "";

		}

        private void GetRejectedRows(List<SheetRowSale> sheetRowSaleList, List<SheetRowSaleOrder> sheetRowSaleOrderList, bool getSumRow, out List<SheetRowSale> rejectedRows)
        {
            var saleDic = new Dictionary<string, SheetRowSale>();
            //销售单中拆出退货商品
            foreach (var sheetRow in sheetRowSaleList.Where(row => row.quantity > 0))
            {
                var key = sheetRow.item_id + "_" + sheetRow.batch_id;
                saleDic.Add(key, sheetRow);
            }

            List<SheetRowSale> lstRejected = new List<SheetRowSale>();

            foreach (var sheetRow in sheetRowSaleOrderList.Where(sheetRow => sheetRow.quantity > 0))
            {
                SheetRowSale rejectedRow = null;
                var orderKey = sheetRow.item_id + "_" + sheetRow.batch_id;
				if (saleDic.ContainsKey(orderKey))
                {
                    var saleRow = saleDic[orderKey];
                    var diffCount = sheetRow.quantity - saleRow.quantity;//销售订单-销售单
                    if (diffCount <= 0) continue;

                    rejectedRow = DeepCopy(saleDic[orderKey]);
                    rejectedRow.quantity = diffCount;
                    rejectedRow.branch_id = saleRow.branch_id;
                    rejectedRow.branch_name = saleRow.branch_name;
                    rejectedRow.branch_position = saleRow.branch_position;
                    rejectedRow.branch_position_name = saleRow.branch_position_name;
                }
                else
                {
                    rejectedRow = JsonConvert.DeserializeObject<SheetRowSale>(JsonConvert.SerializeObject(sheetRow));
                }
                lstRejected.Add(rejectedRow);
            }

            if (getSumRow) rejectedRows = lstRejected;
            else rejectedRows = SheetBase<SheetRowSale>.SplitToMultiUnitRows(lstRejected);

        }

        private void GetReturnRows(List<SheetRowSale> sheetRows, List<SheetRowSale> returnGoods, out bool bHasReturnRow)
        {
            bHasReturnRow = false;
            for (var i = 0; i < sheetRows.Count; i++)
            {
                if (sheetRows[i].quantity < 0)
                {
                    var sheetRowSale = DeepCopy(sheetRows[i]);
                    sheetRowSale.quantity = -sheetRowSale.quantity;
                    bHasReturnRow = true;
                    returnGoods.Add(sheetRowSale);

                }
            }
        }
     
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.from_branch + "_"  +  this.move_stock;
            foreach (var row in SheetRows)
            {
                res += row.item_id + "_" + row.sale_order_sheet_id+"_"+row.back_branch+"_"+row.move_qty+"_"+row.back_type;
            }
            return res;
        }
        public override string GetOtherSaveSQL()
        {
            string sqlDetail = "";
            if (sheet_id == "")
            {
                Dictionary<string, string> dic = new Dictionary<string, string>();
                foreach(var row in this.SheetRows)
				{
                    if (!dic.ContainsKey(row.sale_order_sheet_id)) dic.Add(row.sale_order_sheet_id, row.sale_sheet_id); 
				}
                string[] arr = sale_order_sheets_id.Split(',');
                foreach (var kp in dic)
                {
                    var v = kp.Value;
                    if (!v.IsValid()) v = "null";
                     sqlDetail += $@"insert into op_move_from_van_detail (company_id, oper_id, op_id, sale_order_sheet_id, sale_sheet_id) values ('{company_id}', '{OperID}', '@op_id', {kp.Key}, {v});";
                }
            }
            string sqlSumItems = "";
            var sheetID = "@op_id";
            if (sheet_id != "") {
                sqlSumItems += $@"delete from op_move_from_van_item where company_id ='{company_id}' and op_id ={sheet_id} ; ";
                sheetID = sheet_id;
            }
            if (sumItemsRows.Count > 0)
            {
                foreach (var row in sumItemsRows)
                {
                    if (row.move_sheet_id.IsInvalid()) row.move_sheet_id = "null";
                    if (row.remark.IsInvalid()) row.remark = null;
                    if (row.back_branch.IsInvalid()) row.back_branch = "null";
                    if(row.back_branch_position.IsInvalid())row.back_branch_position = "null";
                    if (row.branch_position.IsInvalid()) row.branch_position = "null";
                    if (row.batch_id.IsInvalid()) row.batch_id = "null";
                    sqlSumItems += $@"insert into op_move_from_van_item (company_id,  op_id, item_id,unit_no,unit_factor,batch_id,branch_position,back_type,back_branch,back_branch_position,need_move_qty,move_qty,move_sheet_id,sale_qty,remark,happen_time,is_add) values ('{company_id}','{sheetID}','{row.item_id}','{row.unit_no}','{row.unit_factor}',{row.batch_id},{row.branch_position},'{row.back_type}',{row.back_branch},{row.back_branch_position},'{row.need_move_qty}','{row.move_qty}',{row.move_sheet_id},'{row.sale_qty}','{row.remark}','{happen_time}','{row.isAdd}');";
                }
            }
            
            return sqlDetail + sqlSumItems;
        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            base.GetInfoForApprove_SetQQ(QQ);
            string sql;

            // #8397 - 2025.06.09
            // 部分用户红冲回库单的时候也走到这来了(应该是审核红字单时)
            // 于是红冲会报错“存在订单已全部回库”
            // 因此这里添加一个判断，是否是在红冲
            var bForRed = red_flag.IsValid();
            if (!bForRed && sale_order_sheets_id.IsValid())
            {
				sql = $@"select * from sheet_status_order where back_branch_status='qb' and company_id = {company_id} and sheet_id in({sale_order_sheets_id})";
				QQ.Enqueue("qbBack", sql);
			}
            
            //            if (sheet_id != "")
            //            {
            //                sql = $@"SELECT oi.*,ip.item_name,ip.batch_level,ib.produce_date,ib.batch_no,d.b_unit_factor,d.m_unit_factor,d.b_unit_no,d.m_unit_no,d.s_unit_no,bp.branch_position_name ,b.branch_name back_branch_name, bb.branch_position_name back_branch_position_name, sm.sheet_no move_sheet_no FROM op_move_from_van_item oi
            //LEFT JOIN info_item_prop ip on oi.company_id = ip.company_id and oi.item_id = ip.item_id 
            //left join 
            //(
            //    select item_id,s_barcode, m_barcode,b_barcode,m_unit_no,s_unit_no,b_unit_no,b_unit_factor,m_unit_factor,
            //    yj_get_unit_relation(b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no) as unit_conv
            //    from 
            //    (
            //        select item_id,(s->>'f1')::real as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_barcode,
            //                       (m->>'f1')::real as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_barcode,
            //                       (b->>'f1')::real as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_barcode
            //        from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode)) as json from info_item_multi_unit where company_id = {company_id} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb) 
            //    )t
            //) d on oi.item_id = d.item_id and oi.company_id={company_id}
            //LEFT JOIN info_item_batch ib on ib.company_id = oi.company_id and ib.batch_id = oi.batch_id
            //LEFT JOIN info_branch_position bp on bp.company_id = oi.company_id and bp.branch_position = oi.branch_position
            //LEFT JOIN info_branch b on b.company_id = oi.company_id and oi.back_branch = b.branch_id
            //LEFT JOIN info_branch_position bb on bb.company_id = oi.company_id and oi.back_branch = bb.branch_id and bb.branch_position = oi.back_branch_position
            //LEFT JOIN sheet_move_main sm on sm.company_id = oi.company_id and sm.sheet_id = oi.move_sheet_id
            //WHERE oi.company_id = {company_id} and op_id = {sheet_id}";
            //                QQ.Enqueue("sumItemRows", sql);
            //            }
            /*
            sql = $@"SELECT ofr.* FROM op_move_from_van_row ofr 
LEFT JOIN op_move_from_van_main ofm on ofr.company_id = ofm.company_id and ofr.op_id = ofm.op_id
WHERE ofr.company_id = {company_id} and ofm.red_flag is null and ofm.approve_time is not null and ofr.is_previous_move is null and ofr.sale_order_sheet_id in({sheetIDs}); ";
 
            QQ.Enqueue("previous_rows", sql);*/
        }

        protected override void GetInfoForSave_SetQQ(SQLQueue QQ)
        {
            string sql;
            base.GetInfoForSave_SetQQ(QQ);

            if (sale_order_sheets_id.IsValid())
            {
                string sheetIdCodi = "";
                if(sheet_id.IsValid())
                {
                    sheetIdCodi = $@" and od.op_id not in ({sheet_id}) ";
                }
                sql = $@"SELECT DISTINCT om.op_no,sm.sheet_no sale_order_sheet_no FROM op_move_from_van_detail od 
LEFT JOIN op_move_from_van_main om on om.company_id = od.company_id and od.op_id = om.op_id 
LEFT JOIN sheet_sale_order_main sm on sm.company_id = od.company_id and sm.sheet_id = od.sale_order_sheet_id
 where od.company_id = {company_id} and om.red_flag is null  and sale_order_sheet_id in({sale_order_sheets_id}) {sheetIdCodi} ;";
                QQ.Enqueue("checkOrderSheets", sql);
            }
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
            if(sqlName == "checkOrderSheets")
            {
                dynamic records = CDbDealer.GetRecordsFromDr(dr, false);
                if (records.Count > 0)
                {
                    foreach(dynamic re in records)
                    {
                        InfoForApprove.ErrMsg = $@"{re.sale_order_sheet_no}订单已在回库单{re.op_no}里进行回库操作";
                        break;
                    }
                    
                }
            }
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            //if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBackBranch();
            //CInfoForApproveBackBranch info = (CInfoForApproveBackBranch)InfoForApprove;
            //CInfoForApproveBase info = new CInfoForApproveBase();
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            CInfoForApproveBase info = (CInfoForApproveBase)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if(sqlName == "qbBack")
            {
                dynamic records = CDbDealer.GetRecordsFromDr(dr, false);
                if(records.Count > 0)
                {
                    InfoForApprove.ErrMsg = "存在订单已全部回库";
                }
            }
            //if (sqlName == "sumItemRows")
            //{ 
            //             dynamic records = CDbDealer.GetRecordsFromDr(dr,false);
            //             if(records.Count > 0)
            //             {
            //                 foreach(var re in records)
            //                 {
            //                     SumItemRow sR = JsonConvert.DeserializeObject<SumItemRow>(JsonConvert.SerializeObject(re));
            //                     this.sumItemsRows.Add(sR);
            //                 }


            //             }

            //         }
        }
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
            if (this.SheetRows.Count() == 0) return "该回库单没有获取订单明细";
            return "OK";
        }
        
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            //CInfoForApproveBackBranch info = (CInfoForApproveBackBranch)info1;



            string sqlPreviousRow = "";
            string sqlOrderStatus = "";
            Dictionary<string, string> dic = new Dictionary<string, string>();
            if (red_flag.IsInvalid())
            {
                foreach (var row in SheetRows)
                {
                    if (!dic.ContainsKey(row.sale_order_sheet_id))
                    {
                        dic.Add(row.sale_order_sheet_id, "");
                        sqlPreviousRow += $@"
UPDATE op_move_from_van_row  r set is_previous_move = true 
  FROM op_move_from_van_main m 
WHERE m.company_id = r.company_id and m.op_id = r.op_id and r.company_id = {company_id} and r.happen_time< '{happen_time}' and r.op_id <>{sheetID} and m.red_flag is null and r.sale_order_sheet_id = {row.sale_order_sheet_id} ;";
                    }
                }



                foreach (var kp in dic)
                {
                    if (back_done.ToLower() == "false")
                    {
                        var orderSheetStatus = true;
                        foreach (var row in SheetRows)
                        {
                            if (kp.Key == row.sale_order_sheet_id && row.need_move_qty * row.unit_factor - row.sale_qty * row.sale_unit_factor - row.move_qty * row.back_unit_factor > 0)
                            {
                                orderSheetStatus = false;
                                break;
                            }
                        }

                        if (orderSheetStatus)
                        {
                            sqlOrderStatus += $"update sheet_status_order set back_branch_status = 'qb' where company_id={company_id} and sheet_id={kp.Key};";
                        }
                        else
                        {
                            sqlOrderStatus += $"update sheet_status_order set back_branch_status = 'bf' where company_id={company_id} and sheet_id={kp.Key};";
                        }

                    }
                    else if (back_done.ToLower() == "true")
                    {
                        sqlOrderStatus += $"update sheet_status_order set back_branch_status = 'qb' where company_id={company_id} and sheet_id={kp.Key};";
                    }
                    //sql += $"update sheet_status_order set back_branch_done = 't' where company_id={company_id} and sheet_id={lst.Key};";
                }
            }
            if (sqlPreviousRow.IsValid() || sqlOrderStatus.IsValid())
            {
                cmd.CommandText = sqlPreviousRow + sqlOrderStatus;
                await cmd.ExecuteScalarAsync();
            }
            

        }
         
		protected override async Task<string> BeforeRed(CMySbCommand cmd, string sheetID, string rederID, string redBrief, CInfoForApproveBase info)
		{
			//  cmd.ActiveDatabase = "";
			//   await Load(cmd, sheetID, true);

			//CMySbTransaction tran = cmd.Connection.BeginTransaction();
			string getFollowingSheet = $@"";
            string err = "";
            string sql = "";
            if(move_stock.IsValid()&&move_stock.ToLower() == "true")
            {
                SheetMove returnMoveSheet = new SheetMove(LOAD_PURPOSE.APPROVE);
                SheetMove rejectMoveSheet = new SheetMove(LOAD_PURPOSE.APPROVE);
                string rejectMoveSheetId = "",returnMoveSheetId="";
                foreach(var row in this.SheetRows)
                {
                    if(row.back_type == "reject")
                    {
                        rejectMoveSheetId = row.move_sheet_id;
                    }else if(row.back_type == "return")
                    {
                        returnMoveSheetId = row.move_sheet_id;
                    }
                }

                if (rejectMoveSheetId.IsValid()) err = await rejectMoveSheet.Red(cmd, this.company_id, rejectMoveSheetId, rederID, redBrief, false);
                
                if (err == "" && returnMoveSheetId.IsValid())
                {
                    err = await returnMoveSheet.Red(cmd, this.company_id, returnMoveSheetId, rederID, redBrief, false);
                }
                var lstMoveSheet= new List<string>();
                foreach(var ss in this.sumItemsRows)
                {
                    if (!lstMoveSheet.Contains(ss.move_sheet_id))
                    {
                        lstMoveSheet.Add(ss.move_sheet_id);
                    }
                }
                if (lstMoveSheet.Count > 0)
                {
                    foreach(var lst in lstMoveSheet)
                    {
                        SheetMove moveSheet = new SheetMove(LOAD_PURPOSE.APPROVE);
                        if (err == "")
                        {
                            err = await moveSheet.Red(cmd, this.company_id, lst, rederID, redBrief, false);
                        }
                    }
                    
                }
            }

            if (err == "")
            {  
               // sql += $"update op_move_from_van_main set red_flag='1' where company_id={companyID} and op_id={sheet_id};";
                sql += sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"update sheet_status_order sso  set back_branch_done = null,back_branch_status = case when position(',' in op_move)>0  then 'bf' else null end 
from (SELECT string_agg( DISTINCT(vr.op_id||'_'||vr.sale_order_sheet_id) ,',') op_move FROM op_move_from_van_row vr 
LEFT JOIN op_move_from_van_main vm on vm.company_id = vr.company_id and vm.op_id = vr.op_id 
 WHERE vr.company_id={this.company_id} and vr.sale_order_sheet_id={sheetId}   and vm.red_flag is null )tt

where sso.company_id={this.company_id} and sso.sheet_id={sheetId}; ");
                foreach(var row in this.SheetRows)
                {
                    if (row.is_previous_move.IsValid() && row.is_previous_move.ToLower() == "true")
                    {
                        var moveQty = Convert.ToSingle(row.move_qty) * Convert.ToSingle(row.back_unit_factor);
                        var salQty = Convert.ToSingle(row.sale_qty) * Convert.ToSingle(row.sale_unit_factor);
                        sql += $@"  UPDATE op_move_from_van_row ofr 
                                    set  need_move_qty =( need_move_qty::numeric*unit_factor::numeric +  {moveQty}+{salQty})/unit_factor::numeric
                                    FROM op_move_from_van_main ofm 
                                    WHERE ofm.company_id = ofr.company_id and ofm.op_id = ofm.op_id and ofr.company_id = {this.company_id} and ofr.op_id <> {sheet_id} and ofr.happen_time >'{happen_time}' and ofm.red_flag is null   and ofr.sale_order_sheet_id ={row.sale_order_sheet_id} and ofr.item_id = {row.item_id} and ofr.back_type = '{row.back_type}' ;
                                ";
                    }
                    else
                    {
                        sql += $@"UPDATE op_move_from_van_row ofr 
                                  set is_previous_move = null 
                                  FROM (SELECT MAX(ofr.happen_time) happen_time FROM op_move_from_van_row ofr 
			                            LEFT JOIN op_move_from_van_main ofm on ofm.company_id = ofr.company_id and ofm.op_id = ofr.op_id 
			                            WHERE ofr.company_id = {this.company_id} and ofm.red_flag is null and ofr.sale_order_sheet_id ={row.sale_order_sheet_id} and ofr.item_id ={row.item_id} and ofr.unit_no ='{row.unit_no}' and ofr.happen_time <'{happen_time}' 
			                            GROUP BY ofr.sale_order_sheet_id
                                        ) t 
                                  WHERE ofr.company_id = {this.company_id} and ofr.sale_order_sheet_id ={row.sale_order_sheet_id}  and ofr.happen_time = t.happen_time and ofr.item_id = {row.item_id} and ofr.back_type = '{row.back_type}';
                                ";

                    }

                }
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
                
            }               
            
            
            return err;
        }
        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd,  CInfoForApproveBase info)
        {
            string err = "";
     
            //var sheetMove = new SheetMove(LOAD_PURPOSE.SHOW);
            back_done = "true";
            foreach(var row in this.SheetRows)
            {
                row.sheet_id = "";
                if(Math.Abs(row.need_move_qty-row.move_qty-row.sale_qty)>0) back_done = "false";
            }
            if (move_stock.IsValid() &&move_stock.ToLower() == "true")
            {
                Dictionary<string, List<SheetRowMove>> moveSheets = new Dictionary<string, List<SheetRowMove>>();
                foreach(var sR in sumItemsRows)
                {
                    //if (Math.Abs(sR.need_move_qty - sR.move_qty - sR.sale_qty) > 0) back_done = "false";
                    List<SheetRowMove> lst = new List<SheetRowMove>();
                    SheetRowMove mR = new SheetRowMove();
                    mR.item_id = sR.item_id;
                    mR.item_name = sR.item_name;
                    mR.unit_no = sR.unit_no;
                    mR.unit_factor = sR.unit_factor;
                    mR.quantity = sR.move_qty;
                    mR.m_unit_no = sR.m_unit_no;
                    mR.m_unit_factor = sR.m_unit_factor;
                    mR.b_unit_factor = sR.b_unit_factor;
                    mR.b_unit_no = sR.b_unit_no;
                    mR.s_unit_no = sR.s_unit_no;
                    mR.from_branch_position = sR.branch_position.IsInvalid()?"0":sR.branch_position;
                    mR.from_branch_position_name = sR.branch_position_name;
                    mR.batch_id = sR.batch_id;
                    mR.batch_level = sR.batch_level;
                    mR.batch_no = sR.batch_no;
                    mR.produce_date = sR.produce_date;
                    mR.to_branch_position = sR.back_branch_position.IsInvalid()?"0": sR.back_branch_position;
                    mR.to_branch_position_name = sR.back_branch_position_name;
                    lst.Add(mR);
                    if (moveSheets.ContainsKey(sR.back_branch))
                    {
                        moveSheets[sR.back_branch].Add(mR);
                    }
                    else
                    {
                        moveSheets.Add(sR.back_branch, lst);
                    }
                    
                }
                foreach(KeyValuePair<string,List<SheetRowMove>>kvp in moveSheets)
                {
                    SheetMove sheetMove = new SheetMove(LOAD_PURPOSE.SHOW);
                    sheetMove.from_branch_id = from_branch;
                    sheetMove.company_id = company_id;
                    sheetMove.happen_time = CPubVars.GetDateText(DateTime.Now);
                    sheetMove.maker_id = OperID;
                    sheetMove.make_time = CPubVars.GetDateText(DateTime.Now);
                    sheetMove.submit_time = CPubVars.GetDateText(DateTime.Now);
                    sheetMove.OperKey = OperKey;
                    sheetMove.OperID = OperID;
                    sheetMove.assign_van = Assign_Van.BACK_VAN;
                    sheetMove.SheetRows = kvp.Value;
                    sheetMove.to_branch_id = kvp.Key;
                    err = await sheetMove.SaveAndApprove(cmd, false);
                    if(err == "")
                    {
                        foreach(var row in sumItemsRows)
                        {
                            if (row.back_branch == kvp.Key)
                            {
                                row.move_sheet_id = sheetMove.sheet_id;
                                row.move_sheet_no = sheetMove.sheet_no;
                            }
                        }
                    }
                    else
                    {
                        break;
                    }
                }
                 
            }
             
            return err; 
        }

        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting); 
            
        }

    }
}
