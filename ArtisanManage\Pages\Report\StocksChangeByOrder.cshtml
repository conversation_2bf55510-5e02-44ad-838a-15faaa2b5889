@page
@model ArtisanManage.Pages.BaseInfo.StocksChangeByOrderModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel"/> 

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var newCount = 1;
    	var itemSource = {};
    	$(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)



            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                if (args.datafield == "sheet_no") {
                    var sheet_id = args.row.bounddata.sheet_id;
                    var inventory_sheet_id = args.row.bounddata.inventory_sheet_id;
                    var sheet_type1 = args.row.bounddata.sheet_type1;
                    var sheet_type = args.row.bounddata.sheet_type;
                        var sheet_no = args.row.bounddata.sheet_no;
                        console.log(sheet_type1)
                        console.log(sheet_type)
                    if (sheet_type1 == 'X' || sheet_type1 == 'T')  window.parent.newTabPage(sheet_type, `Sheets/SaleSheet?sheet_id=${sheet_id}`);
                        if (sheet_type1 == 'JH' || sheet_type1 == 'HH') window.parent.newTabPage(sheet_type, `Sheets/BorrowItemSheet?sheet_id=${sheet_id}`);
                        if (sheet_type1 == 'CG' || sheet_type1 == 'CT') window.parent.newTabPage(sheet_type, `Sheets/BuySheet?sheet_id=${sheet_id}`);
                    if (sheet_type1 == 'DR' || sheet_type1 == 'DC')  window.parent.newTabPage(sheet_type, `Sheets/MoveSheet?sheet_id=${sheet_id}`);
                    if (sheet_type1 == 'YK' || sheet_type1 == 'BS') window.parent.newTabPage(sheet_type, `Sheets/InventChangeSheet?sheet_id=${sheet_id}`);
                    if (sheet_type1 == 'ZCR' || sheet_type1 == 'ZCC') {
                        if (sheet_no.indexOf('ZZ')>=0){
                                window.parent.newTabPage(sheet_type, `Sheets/CombineSheet?sheet_id=${sheet_id}&forSplit=false`);
                        }else{
                                window.parent.newTabPage(sheet_type, `Sheets/CombineSheet?sheet_id=${sheet_id}&forSplit=true`);
                        }
                    }
                    if (sheet_type1 == 'RK' || sheet_type1 == 'CK') {
                        if (sheet_no.indexOf('RK') >= 0) {
                            window.parent.newTabPage(sheet_type, `Sheets/StockInOutSheet?sheet_id=${sheet_id}&forReduce=false`);
                        } else {
                            window.parent.newTabPage(sheet_type, `Sheets/StockInOutSheet?sheet_id=${sheet_id}&forReduce=true`);
                        }

                    }
                    if (sheet_type1 == 'QCKC') window.parent.newTabPage(sheet_type, `Sheets/OpeningStockSheet?sheet_id=${sheet_id}`);
                }
            });
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            $('#item_id').jqxInput({
                onButtonClick: function (event) {
                    $('#popItem').jqxWindow('open');
                    $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&operKey=${g_operKey}&showSonItems=1" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                }
            });
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            $("#gridItems").jqxGrid('beforeRowRender', function (divRow, rowData) {
                if (rowData.status == '红冲单')
                        divRow.style.color = '#888'
                else if (rowData.status == '红字单')
                    divRow.style.color = '#f00'
                else
                    divRow.style.color = '#000'

                })

            QueryData();
        });
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "selectMulti") {
                    if (rs.data.checkedRows.length == 1) {
                        var item_id = rs.data.checkedRows[0].item_id;
                        var item_name = rs.data.checkedRows[0].item_name;
                    }

                    var rows = rs.data.checkedRows
                    var items_id = ''
                    rows.forEach(function (row) {
                        if (items_id != '') items_id += ','
                        items_id += row.item_id
                    })
                    $('#item_id').jqxInput('val', { value: item_id, label: item_name });

                    $.ajax({
                        url: '/api/SaleSheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: items_id },
                        success: function (data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });
                }

                $('#popItem').jqxWindow('close');
            }

        });
        function viewProduceDate(row, column, value, p4, p5, rowData) {
            let showBatch = $("#showBatch").val().toString().toLowerCase() == "true" ? true : false;
            if (showBatch) {
                let cellValue = value
                return `<div style="height:100%;width:100%;display: flex;align-items:center;justify-content:center;">${cellValue ? cellValue : '无产期'}</div>`
            } else {
                let cellValue = value ? JSON.parse(JSON.stringify([value])) : []
                if (cellValue.length == 0) return ""
                if (cellValue.length == 1) {
                    let e = cellValue[0]
                    if (!e.produce_date) return `<div style="height:100%;width:100%;display: flex;align-items:center;justify-content:center;">无产期</div>`
                    else {
                        return `<div style="height:100%;width:100%;display: flex;align-items:center;justify-content:center;">${e.produce_date}</div>`
                    }
                }
                // if (cellValue.length > 1) return `<div onclick='showBatchDetail(${value})' style="height:100%;display:flex;align-items:center;justify-content:center;color:#4499ff;" >查看</div>`
            }
        }
    </script>
</head>

<body style="overflow:hidden"> 
    <div style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>

        <button onclick="QueryData()" style="margin-right:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>

    </div>
    
     <div id="gridItems"></div>  
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div> 
        

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">商品信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>