﻿@page
@model ArtisanManage.Pages.Report.AttenanceMonthReportViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.addEventListener('message', function (rs) {
            console.log(rs)
            if (rs.data.msgHead === "AttendanceSettingEdit") {
                console.log($("#popItem"))
                $('#popItem').jqxWindow('close');
            }
        })
        let attendanceMap = {}
  
        let theMonthAttendances=[]
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            QueryData("", res => {
                console.log(res)
            });
          

        })
function QueryDataClick() {
    const selectedMonth = $("#query_month")[0].value
    const operID = $("#oper_id>input")[0].value
    if (!operID) {
        bw.toast("请选择业务员")
        return
    }
    var date = new Date(selectedMonth)
    QueryData("", res => {
        if (res.result == 'OK' && res.msg === '该业务员未设置考勤组！') {
            bw.toast(res.msg)
            return
        }
        let statusDIC = {
            "缺勤": 0,
            "请假": 0,
            "休息": 0,
            "迟到": 0,
            "早退": 0,
            "其他": 0,
            "正常": 0,
            "未签退": 0
        }

        if (Object.keys(res.matchRowDictionary).length != 0) { 
             Object.keys(res.matchRowDictionary).map(key => {
                res.rows = res.rows.concat( res.matchRowDictionary[key] )
            })
            console.log(res.rows)
            $("#gridItems").html(renderMultiMonthRecords(res.matchRowDictionary))
        }
        res.rows.map(row => {
            if (!row.status) {
                statusDIC["其他"]++
            } else {
                row.status.split(",").map(status => {
                    statusDIC[status]++
                })
            }
        })
        var html = ""
        Object.keys(statusDIC).map(key => {
            html +=   `<div>
                            <span> ${key} </span>
                            <span style= "color:${key === '缺勤'||key === '迟到'?'red':'green'}"> ${statusDIC[key]} <span>
                      </div>`
        })
        console.log(html)
        $("#calRes").html(html)

    })
    }
        function renderMultiMonthRecords(recordsMap) {
            return "<div style=' display: grid;grid: 100px / auto auto auto auto; '>"+
            Object.keys(recordsMap).map(key => {
                var itemsText =  recordsMap[key].map((record)=>{
                    if (!record) {
                        return ""
                    }
                        return `<div style="display:flex;flex-direction:row;justify-content:center;margin-top:4px;margin-bottom:4px;">
                                    <div style="margin-right:4px;">签到:${!record.start_time?"无":record.start_time.split(" ")[1]}</div>
                                    <div style="margin-right:4px;">签退:${!record.end_time?"无":record.end_time.split(" ")[1]}</div>
                                    <div  style="margin-right:4px;color:${record.status.indexOf('缺勤')!=-1||record.status.indexOf('迟到')!=-1||record.status.indexOf('早退')!=-1?'#c45656':'#529b2e'}">${record.status}</div>
                                </div>`
                    }).join("")
                return `<div style="border:1px solid #e0e0e0;width:280px;">
                            <div style="background:#f0f0f0;padding:4px;display:flex;flex-direction:row;justify-content:center;">${key}</div>
                            <div>${itemsText}</div>
                        </div>`

            }).join("")+"</div>"
        }
       
    

    </script>
</head>

<script type="text/javascript" src="../wwwroot/jqwidgets/jqwidgets/jqxtabs.js"></script>

<body class='default' style="overflow:hidden;">
   
    <style>
        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }
    </style>


    <div id="divHead" class="headtail" style="width:calc(100% - 110px);">

        <div style="float:none;height:0px; clear:both;"></div>

    </div>
    <div id="operbar">
         <button onclick="QueryDataClick()" style="margin-left:20px;margin-bottom:10px;" class="main-button">查询</button>
         <button @@click="handleExportAttendance">导出</button>
    </div>

    <div id="calRes" style="display:flex;direction:row;"></div>

    <div style="flex-grow:1;display:flex;width:100%;height:100%;">
        <div style="width:calc(100% - 200px);height:100%; margin-left:10px;">
            <div><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>
            <div id="gridItems" style="margin-top:0px;margin-left:10px; margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 20px);"></div>

        </div>
    </div>
    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">客户信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
<script src="~/js/Vue.js"></script>
<script src="~/js/FileSaverVue.js"></script>
<script src="~/js/Blob.js"></script>
<script src="~/js/jszip.js"></script>
<script src="~/js/ods.js"></script>
<script src="~/js/cpexcel.js"></script>
<script src="~/js/xlsx.full.min.js"></script>
<script src="~/js/xlsx-style.js"></script>
<script src="~/js/Export2Excel.js?v=@Html.Raw(Model.Version)"></script>
<script src="~/js/YJExportSheet.js?v=@Html.Raw(Model.Version)"></script>
<script src="~/js/site.js"></script>
<script>

    var app = new Vue({
        el: "#operbar", // 目前控制divExportOuter 区域，后续根据需要扩大范围
        data() {
            return {
                msg: "123"
            }
        },
        mounted() {
            console.log("我加载了！")
        },
        methods: {
            // 导出详细, 获取单据信息代码来自 function  btnExportTable_click()
            handleExportAttendance() {
                this.getPrintAttendance((rows) => {
                    this.handleExportDetailExcel(rows)
                })
                /**
                   this.pritntSheetsInfo = []
                   if (exportType === 'detail') {
                       this.getPrintSheetDetail(() => {
                           console.log('pritntSheetsInfo', this.pritntSheetsInfo)
                           this.handleExportDetailExcel()
                       })
                   }
                   */
            },
            handleExportDetailExcel(attendanceRows) {
             
                // excel表头
                let header = ["日期","签到时间","签退时间","打卡地址","考勤状态"]
                //请求数据的字段
                const filterVal = ['day', 'start_time', 'end_time', 'addr', 'status']
                // 数据来源
                const list = attendanceRows
                console.log(attendanceRows)
                // 拼接数据
                const data = this.formatJson(filterVal, attendanceRows)
                const selectedMonth = $($("#query_month>input")[0]).attr("data-value")
                const operName = $($("#oper_id>input")[0]).attr("data-label")
                export_json_to_excel(header, data, selectedMonth+'_'+operName+'考勤报表')

            },
            // 拼接数据
            formatJson(filterVal, jsonData) {
                return jsonData.map(v => filterVal.map(j => {
                    return v[j]
                }))
            },
            // callBack 为ajax 成功获取数据后，进行执行
            getPrintAttendance(callBack) {
                const selectedMonth = $($("#query_month>input")[0]).attr("data-value")
                const operID = $($("#oper_id>input")[0]).attr("data-value")
                $.ajax({
                    url: `/api/AttenanceMonthReportView/GetQueryRecords?gridID=gridItems&startRow=0&endRow=200&GetRowsCount=true&gridSetting=&operKey=@Model.OperKey&query_month=${selectedMonth}&oper_id=${operID}&sortColumn=&sortDirection=`,
                    success: (res) => {
                        callBack(res.rows)
                    }
                })
            }
        }
    })
</script>
</html>
