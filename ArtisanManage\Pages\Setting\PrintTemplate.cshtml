@page
@model ArtisanManage.Pages.Setting.PrintTemplateModel
@{
    Layout = null;
}
<!DOCTYPE html>
<!---webkit-user-select:none;user-select:none;-->
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<title></title>
		<link rel="stylesheet" type="text/css" href="~/PrintTemplate/css/PrintTemplate.css?v=@Html.Raw(Model.Version)" />
		<link rel="stylesheet" href="~/PrintTemplate/font/iconfont.css">
        <link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)">

	    <script src="~/PrintTemplate/js/jquery-1.7.1.min.js"></script> 
		<script src="~/MiniJsLib/MiniJsLibPC.js"></script>

		<link rel="stylesheet" href="~/MiniJsLib/jquery.dialog.css?v=@Html.Raw(Model.Version)">
		<script src="~/MiniJsLib/jquery.dialog.js?v=@Html.Raw(Model.Version)"></script>
		<script type="text/javascript" src="~/tinymce_5.8.2/tinymce.min.js" ></script>
	    <script type="text/javascript" src="~/PrintTemplate/js/exif.min.js" ></script>
		<style>
            * {
                -webkit-user-select: none; /* Chrome/Safari/Opera */
                -moz-user-select: none; /* Firefox */
                -ms-user-select: none; /* Internet Explorer/Edge */
                user-select: none; /* Non-prefixed version, currently not supported by any browser */
                font-family:inherit;
			    font-size:revert;  /*去除miniJsLib中*{}的影响 */
			}
 

            ::-webkit-scrollbar {
                width: 16px;
                height: 16px;
                background-color: #888;
            }

            ::-webkit-scrollbar-track {
                background-color: #888;
            }


            ::-webkit-scrollbar-thumb {
                border-radius: 7px;
                -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
                background-color: #aaaaaa;
            }

            ::-webkit-scrollbar-corner {
                background-color: #888;
            }
            .mce-content-body{
				margin:0px;
            }
            table:not([cellpadding]) td, table:not([cellpadding]) th {
                padding: 0px;padding-bottom:0px;padding-top:0px;padding-left:0px;padding-right:0px;
            }
            table td{
                padding: 0px;
                padding-bottom: 0px;
                padding-top: 0px;
                padding-left: 0px;
                padding-right: 0px;

            }
            p, .print-item {
                margin-top: 0;
                margin-bottom: 0;
                padding-top: 0px;
                padding-bottom: 0;
                
                margin: 0;
                padding: 0;
            }
            .drag_wid_input .drag_wid_input_input {
				font-size:8px;
                padding-left: 1px;
            }
            .drag_wid_input {
                font-size: 8px;
                padding-left: 1px;
            }
                .drag_wid_input .drag_wid_input_name {
                    font-size: 12px;
                }
                .drag_wid_input .drag_wid_input_input input  {
                    font-size: 12px;
                }

           
            .drag_wid_input .drag_wid_input_input .drag_wid_input_input_r {
                font-size: 8px;
            }
            .submit_btn{
				display:flex;
            }

            .submit_btn .main-btn {
                height: 25px;
                border: none;
                line-height: 25px;
                width: 45px;
                background: #f66;
				color:#fff;
                border-radius: 5px;
            }
            .main-btn:active {
                background-color: #333;
            }
            .main-btn:hover {
                background-color: #f22;
            }
            .submit_btn .other_btn {
                border-color: #ddd;
                border-width: 1px;
                border-style: solid;
                background-color: #fff;
                line-height: 25px;
				height:25px; 
                width: 45px;
                border-radius: 5px;
            }
		
            .other_btn:active {
                background-color: #888;
            }

            .other_btn:hover {
                background-color: #ddd;
            }
		</style>
	</head>
	<body>
		<div id="app">
			<!-- 左边栏 -->
			<div class="drag_navs" style=" padding:0px;margin-top:0px;top:0px;height:100%;">
				<!--<div class="drag_navs_fath iconfont" id="drag_navs_fath"  style="background-color:#fff;padding:10px;margin-top:0px;top:0px;">
					&#xe6a9;
				</div>-->
				<div class="drag_navs_boxs" id="drag_navs_boxs"  style="padding:0px;margin-top:50px;top:0px;left:0px;height:100%;">
					<div class="drag_navs_boxs_m" id="drag_navs_boxs_m">

					</div>
				</div>
			
			</div>
			<!-- 中间栏 -->
				<div class="drag_navs_wid" id="drag_navs_wid" style="position:absolute;display:flex;padding-left:40px;  width:100vw;height:50px; margin-top:0px;top:0px;left:0px;margin-left:0px;z-index:99999;">
					<div class="drag_wid_input drag_wid_input">
						<div class="drag_wid_input_name" style="width:40px;">
							名称:
						</div>
						<div class="drag_wid_input_input">
							<input type="text" placeholder="" id="templateName" value="">
							 
						</div>

					</div>
					
					<div class="drag_wid_input" style="margin-left:-35px;width:140px;">
						<div class="drag_wid_input_name" style="width:40px;" >
							类型:
						</div>
						<div class="drag_wid_input_input" id="sheet_type_wrapper" style="width:100px;">
							<input type="text" placeholder="" readonly value="" id="sheetType">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>

					<div class="drag_wid_input" style="width: 140px;margin-left: -30px;">
						<div class="drag_wid_input_name" style="width:40px;">
							尺寸:
						</div>
						<div class="drag_wid_input_input" id="sheet_size_wrapper"  style="width:100px;">
							<input type="text" placeholder="" readonly value="" name="inputPageSize">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					<div class="drag_wid_input drag_wid_input_small" style="width:100px;margin-left:-30px;">
						<div class="drag_wid_input_name" style="width:40px;">
							宽:
						</div>
						<div class="drag_wid_input_input" style="width:60px;">
							<input type="text" placeholder="" id="page_width" name="page_width" value="">
							<div class="drag_wid_input_input_r">
								mm
							</div>
						</div>
					</div>

					<div class="drag_wid_input drag_wid_input_small" style="width:100px;margin-left:-30px;">
						<div class="drag_wid_input_name" style="width:40px;">
							高:
						</div>
						<div class="drag_wid_input_input" id="divPageHeight" style="width:80px;">
							<input type="text" placeholder="" id="page_height" name="page_height" value="">
							<div class="drag_wid_input_input_r">
								mm
							</div>
						</div>
					</div>
			<div class="drag_wid_input drag_wid_input_small" id="divOuterPaperHeight" style="width:100px;margin-left:-15px;display:none;">
				<div class="drag_wid_input_name" style="width:40px;">
					纸高:
				</div>
				<div class="drag_wid_input_input" id="divPaperHeight" style="width:60px;">
					<input type="text" placeholder="" id="paper_height" name="paper_height" value="">
					<div class="drag_wid_input_input_r">
						mm
					</div>
				</div>
			</div>
			<div class="drag_wid_input drag_wid_input_small" id="divOuterMaxPageHeight" style="width:130px;margin-left:-15px;display:none;">
				<div class="drag_wid_input_name" style="width:60px;font-size:10px;">
					最大页高
				</div>
				<div class="drag_wid_input_input" id="divMaxPageHeight" style="width:70px;">
					<input type="text" placeholder="" id="max_page_height" name="max_page_height" value="">
					<div class="drag_wid_input_input_r">
						mm
					</div>
				</div>
			</div>
			<div class="drag_wid_input drag_wid_input_small" id="divLabelLineCount" style="width:130px;margin-left:-15px;display:none;">
				<div class="drag_wid_input_name" style="width:60px;font-size:10px;">
					每行标签数量
				</div>
                <div class="drag_wid_input_input" id="divLabelLineCount" style="width:70px;">
					<input type="text" placeholder="1" id="eachLineLabel" name="eachLineLabel" value="">
					<div class="drag_wid_input_input_r">
						个
					</div>
				</div>
			</div>
			<div class="drag_wid_input drag_wid_input_small" id="divLabelHorizontalGap" style="width:130px;margin-left:-15px;display:none;">
				<div class="drag_wid_input_name" style="width:60px;font-size:10px;">
					标签横向间距
				</div>
                <div class="drag_wid_input_input" id="divLabelHorizontalGap" style="width:70px;">
					<input type="text" placeholder="2" id="horizontalGap" name="horizontalGap" value="">
					<div class="drag_wid_input_input_r">
						mm
					</div>
				</div>
            </div>
            <div class="drag_wid_input drag_wid_input_small" id="divLabelVerticalGap" style="width:130px;margin-left:-15px;display:none;">
                <div class="drag_wid_input_name" style="width:60px;font-size:10px;">
                    标签纵向间距
                </div>
                <div class="drag_wid_input_input" id="divLabelVerticalGap" style="width:70px;">
                    <input type="text" placeholder="2" id="verticalGap" name="verticalGap" value="">
                    <div class="drag_wid_input_input_r">
                        mm
                    </div>
                </div>
            </div>
					<div class="submit_btn" style="display:block;">  
						<button class="main-btn" id="btnSave" style="">保存</button> 
						<button class="other_btn" id="btnSaveAs" style="margin-left:5px;">另存</button>
						<button class="other_btn" id="btnReturn" style="margin-top:5px;">返回</button>
						
					</div>
				</div>
			<!-- 中间栏 -->
			<div class="whole-container" style="background-color:#888; position:absolute;display:block; overflow:auto;left:60px;top:50px; height:calc(100% - 59px);width:calc(100% - 320px);" draggable="false">
			   <div id="print_sheet_outer"  style="min-width:calc(100% -  100px);min-height:calc(100% - 100px);padding:50px; display:flex; justify-content:center;align-items:center;flex-direction:row;background:#888;">  
		
			<!--<div id="print_sheet" draggable="false" style="background-color:#f00; position:absolute;left: 0;top:0;right:0;bottom: 0;margin:auto;">
					-->			
			 
				<div id="print_sheet" draggable="false" style="background-color:#f00;display:flex;flex-direction:column;justify-content:space-between;">					
				    <div class="print_sheet_selects iconfont" style="margin-top:-43px;position:absolute;opacity:0.6;visibility:visible;">
						&#xe600; <span>单据</span>
					</div> 
					<div style="width:100%;height:auto;">
							<div class="print-area" id="print_pageHead" data="pageHead" draggable="false" style="min-height:20px;width:100%;" >
								<div class="print_sheet_selects iconfont">
									&#xe600; <span>页眉</span>
								</div>
							</div>
							<div class="print-area" id="print_tableHead" data="tableHead" draggable="false" style="min-height:20px;width:100%;" >
								<div class="print_sheet_selects iconfont">
									&#xe600; <span>表头</span>
								</div>
							</div>
							<div class="print-area" id="print_table" data="table" style="display: none; min-height: 70px;height:150px;" draggable="false">
								<div class="print_sheet_selects iconfont">
									&#xe600; <span>表格</span>
								</div>
							</div>
							<div class="print-area" id="print_tableTail" data="tableTail" draggable="false" style="min-height:20px;width:100%;" >
								<div class="print_sheet_selects iconfont">
									&#xe600; <span>表尾</span>
								</div>
							</div>
				    </div>	
					<div class="print-area" id="print_pageTail" data="pageTail" style="min-height:20px;left:0px;width:100%;">
						<div class="print_sheet_selects iconfont">
							&#xe600; <span>页脚</span>
						</div>
					</div>
				</div>
		       </div>
			</div>
			<!-- 中间栏 -->

			<!-- 右边栏 -->
			<div class="drag_style" style="width:250px;padding:5px;top:0px;right:0px;height:100%;padding-top:50px;">
				<div class="drag_style_box" style="width: 250px; height:90%; overflow-y: auto; overflow-x: hidden;">  

					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name">
							选中:
						</div>
						 <div class="drag_wid_input_input" style="border:none;line-height:30px;font-size:15px;" id="">
							<label id="lblSelectedElemenet"></label>
						</div>
					</div>

					<div id="divLandscape" style="padding-top:15px;margin-left:20px;">
						<input id="ckLandscape" type="checkbox" style="width:20px;height:20px;margin-right:10px;"/><label for="ckLandscape">横向</label>
					</div>

                    <div id="divInvert" style="padding-top:15px;margin-left:20px;">
                        <input id="ckInvertX" type="checkbox" style="width:20px;height:20px;margin-right:10px;" /><label for="invertX">水平反转</label>
                        <input id="ckInvertY" type="checkbox" style="width:20px;height:20px;margin-right:10px;" /><label for="invertY">垂直反转</label>
                    </div>

					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name" style="">
							上边距:
						</div>
						<div class="drag_wid_input_input" id="divPaddingTop">
							<input type="text" value="" id="paddingTop"/>
							<div id="btnRemoveItem" class="drag_wid_input_input_r" style="font-size:12px;right:10px;">
								mm
							</div>
							 
						</div> 
					</div>

					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name" style="">
							下边距:
						</div>
						<div class="drag_wid_input_input" id="divPaddingBottom">
							<input type="text" value="" id="paddingBottom">
							<div id="btnRemoveItem" class="drag_wid_input_input_r" style="font-size:12px;right:10px;">
								mm
							</div>
						</div>
					</div>

					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name" style="">
							左边距:
						</div>
						<div class="drag_wid_input_input" id="divPaddingLeft">
							<input type="text" value="" id="paddingLeft">
							<div id="btnRemoveItem" class="drag_wid_input_input_r" style="font-size:12px;right:10px;">
								mm
							</div>
						</div>
					</div>

					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name" style="">
							右边距:
						</div>
						<div class="drag_wid_input_input" id="divPaddingRight">
							<input type="text" value="" id="paddingRight">
							<div id="btnRemoveItem" class="drag_wid_input_input_r" style="font-size:12px;right:10px;">
								mm
							</div>
						</div>
					</div>

					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name" style="">
							标题:
						</div>
						<div class="drag_wid_input_input" id="title">
							<input type="text" value="" id="prop_title">
							<!--<div id="btnRemoveItem" class="drag_wid_input_input_r iconfont" style="font-size: 25px;">
								&#xe684;
							</div>-->
						</div>
					</div>

					<div class="drag_wid_input style_input" style="">
						<div class="drag_wid_input_name">
							显示标题:
						</div>
						<div class="drag_wid_input_input" id="show_title">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>

					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name">
							字体:
						</div>
						<div class="drag_wid_input_input" id="font">
							<input type="text" placeholder="默认" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name">
							加粗:
						</div>
						<div class="drag_wid_input_input" id="font_bold">
							<input type="text" placeholder="默认" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name">
							斜体:
						</div>
						<div class="drag_wid_input_input" id="font_style">
							<input type="text" placeholder="默认" value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					
					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name">
							字号:
						</div>
						<div class="drag_wid_input_input" id="font_size">
							<input type="text" placeholder="默认" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>

					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							下划线:
						</div>
						<div class="drag_wid_input_input" id="font_underline">
							<input type="text" placeholder="默认" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>

				 				 
					<div class="drag_wid_input style_input" id="text_cents">
						<div class="drag_wid_input_name">
							居中:
						</div>
						<div class="drag_wid_input_input" id="font_cent">
							<input type="text" placeholder="文字居中" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>

					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							表格线:
						</div>
						<div class="drag_wid_input_input" id="showLines">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
			 


					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name" style="">
						表格线宽:
						</div>
					    <div class="drag_wid_input_input" id="lineWidth">
							<input type="text" value="">
							<!--<div id="btnRemoveItem" class="drag_wid_input_input_r iconfont" style="font-size: 25px;">
								&#xe684;
							</div>-->
						</div>
					</div>



					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							排序字段:
						</div>
						<div class="drag_wid_input_input" id="sortColumns">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							升降序:
						</div>
						<div class="drag_wid_input_input" id="sortDirection">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							分组字段:
						</div>
						<div class="drag_wid_input_input" id="groupSumByColumn">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					
					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name" style="">
							行高:
						</div>
						<div class="drag_wid_input_input" id="rowHeight">
							<input type="text" value="">
							<!--<div id="btnRemoveItem" class="drag_wid_input_input_r iconfont" style="font-size: 25px;">
								&#xe684;
							</div>-->
						</div>
					</div>

					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							填充空行:
						</div>
						<div class="drag_wid_input_input" id="showEmptyRows">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							合计:
						</div>
						<div class="drag_wid_input_input" id="show_sum">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>

					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name">
							页合计:
						</div>
						<div class="drag_wid_input_input" id="show_page_sum">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
				<div class="drag_wid_input style_input" style="display:none;">
					<div class="drag_wid_input_name">
						合计加粗:
					</div>
					<div class="drag_wid_input_input" id="bold_sum">
						<input type="text" placeholder="" readonly value="">
						<div class="drag_wid_input_input_r iconfont">
							&#xe680;
						</div>
					</div>
				</div>
					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name">
							组合计:
						</div>
						<div class="drag_wid_input_input" id="showGroupSum">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
			      
					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name">
							超出处理:
						</div>
						<div class="drag_wid_input_input" id="exceedAction">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					 
					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							空值隐藏:
						</div>
						<div class="drag_wid_input_input" id="hide_empty">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							大写金额:
						</div>
						<div class="drag_wid_input_input" id="chinese_num">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					 
					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							条码图文本:
						</div>
						<div class="drag_wid_input_input" id="barcode_pic_text_hide">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>
					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							条码图文本大小:
						</div>
						<div class="drag_wid_input_input" id="barcode_pic_text_size">
							<input type="text" value=""/>
						</div>
					</div>

					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name">
							方向:
						</div>
						<div class="drag_wid_input_input" id="align">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>

				<div class="drag_wid_input style_input">
					<div class="drag_wid_input_name">
						显示方式:
					</div>
					<div class="drag_wid_input_input" id="show_style">
						<input type="text" placeholder="" readonly value="">
						<div class="drag_wid_input_input_r iconfont">
							&#xe680;
						</div>
					</div>
				</div>

					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name" style="">
							宽度:
						</div>
						<div class="drag_wid_input_input" id="element_width">
							<input type="text" value=""/>
						</div>
					</div>

					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name" style="">
							小数位:
						</div>
						<div class="drag_wid_input_input" id="dot_len">
							<input type="text" value=""/>
						</div>
					</div>

					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name" style="">
							字符间距:
						</div>
						<div class="drag_wid_input_input" id="char_spacing">
							<input type="text" value=""/>
						</div>
					</div>
					
					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name" style="">
							显示内容:
						</div>
						<div class="drag_wid_input_input" id="show_value">
							<input type="text" value="">
					 
						</div>
					</div>
					

					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name" style="">
						   合计内容:
						</div>
						<div class="drag_wid_input_input" id="column_sum_value">
							<input type="text" value="">
					 
						</div>
					</div>
					
					 

					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name" style="">
							高度:
						</div>
						<div class="drag_wid_input_input" id="element_height">
							<input type="text" value=""/>
							<!--<div id="btnRemoveItem" class="drag_wid_input_input_r iconfont" style="font-size: 25px;">
								&#xe684;
							</div>-->
						</div>
					</div>

					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name" style="">
							图片:
						</div>
						<div class="drag_wid_input_input" id="image_browse" style="border-bottom-style:none;">
							<button type="button" style="width:70px;height:30px;border-radius:8px; border-color:#ddd;border-style:solid; ">选择</button>
							<!--<div id="btnRemoveItem" class="drag_wid_input_input_r iconfont" style="font-size: 25px;">
								&#xe684;
							</div>-->
						</div>
					</div>

					<div class="drag_wid_input style_input">
						<div class="drag_wid_input_name" style="">
							背景:
						</div>
						<div class="drag_wid_input_input" id="back_image_browse" style="border-bottom-style:none;width:100px;">
							<button type="button" id="btnSelectBackImage" style="display:inline; width:35px;height:30px;border-radius:8px; border-color:#ddd;border-style:solid; ">选择</button>
							<button type="button" id="btnDelBackImage" style="display: inline; width: 35px; height: 30px; border-radius: 8px; border-color: #ddd; border-style: solid; ">清除</button>
							
							<!--<div id="btnRemoveItem" class="drag_wid_input_input_r iconfont" style="font-size: 25px;">
								&#xe684;
							</div>-->
						</div>
					</div>

					<div class="drag_wid_input style_input" style="display:none;">
						<div class="drag_wid_input_name">
							打印背景:
						</div>
						<div class="drag_wid_input_input" id="printBackImage">
							<input type="text" placeholder="" readonly value="">
							<div class="drag_wid_input_input_r iconfont">
								&#xe680;
							</div>
						</div>
					</div>

				</div>
			</div>
			<!-- 右边栏 -->
			
			 
		</div>
	</body>
</html>
<script src="~/PrintTemplate/js/print_json.js?v=@Html.Raw(Model.Version)" type="text/javascript" charset="utf-8"></script>
<script>
	var TemplateID = "@Html.Raw(Model.TemplateID)"; 
	var SheetType = "@Html.Raw(Model.SheetType)";	
	var g_operKey = "@Html.Raw(Model.OperKey)";
	 
	$(function () {
		let print_sheet_selects_iconfont_down = false;		//是否标记过
		let PropertyOptions = jQuery.printJson();
		//let onloadDataAttr = {}
		let SheetTemplate = {
			name: "",
			title: "",
			width: 0,
			height: 0,
			fontSize: "",
			fontName: "",
			fontBold: "",
			showTitle: "",
			pageHead: {
				name: "pageHead",
				title: "",
				fontSize: "",
				fontName: "",
				fontBold: "",
				showTitle: "",
				font_underline:"",
				left: 0, //左边距
				height: 0, //高度
				elements: []
			},
			tableHead: {
				name: "tableHead",
				title: "表头区",
				fontSize: "",
				fontName: "",
				fontBold: "",
				showTitle: "",
				font_underline:"",
				left: 0, //左边距
				height: 0, //高度
				images: [],
				elements: [],
			},
			table: {
				name: "table",
				title: "表格区",
				left: 0, //左边距
				height: 0,
				showLines: true, //是否打印表格线
				showEmptyRows: false, //当行数少时，是否用空行补足页面
				sortColumns: '',
				sortDirection:'',
				groupSumByColumn:'',
				fontSize: "",
				fontName: "",
				fontBold: "",
				showTitle: "", 
				elements: []
			},
			tableTail: {
				name: "tableTail",
				title: "",
				fontSize: "",
				fontName: "",
				fontBold: "",
				showTitle: "",
				font_underline:"",
				left: 0, //左边距
				height: 0, //高度
				htmlContent: "",
				elements: []
			},
			pageTail: {
				name: "pageTail",
				title: "",
				fontSize: "",
				fontName: "",
				fontBold: "",
				showTitle: "",
				font_underline:"",
				left: 0, //左边距
				height: 0, //高度
			    htmlContent: "",
				elements: []
			}
		}
		var AvailElements = {}
		/*$(document).bind("contextmenu",function(e){
		   return false;
		 });*/
		rightClick()
		function rightClick() {
		}

		window.loadTemplate = function (sheetType, templateID) {
			TemplateID = templateID;
			$.ajax({
				url: "/api/PrintTemplate/LoadTemplate", //url地址
				dataType: "json", //返回的数据类型
				type: "get", //发起请求的方式
				data: {
					sheetType: sheetType,
					templateID: templateID,
					operKey: g_operKey
				},
				success: function (res) {
					if (res.result === "OK") {
						//onloadDataAttr = res;
						AvailElements=res.avail_elements
						loadTabAreas(PropertyOptions.tabAreas,res.avail_elements)
						$('#templateName').val(res.template_name)
						SheetTemplate = res.template
						renderTemplate(res.template)

                        if (SheetType === 'BQ') {
                            $('#divInvert').show()
                        } else {
                            $('#divInvert').hide()
                        }

						appendPropertyItem(PropertyOptions.sheetTypeList, 'sheet_type_wrapper', false)
						appendPropertyItem(PropertyOptions.pageSize, 'sheet_size_wrapper', true)
						appendPropertyItem(PropertyOptions.pageHeight, 'divPageHeight', true)
						appendPropertyItem(PropertyOptions.paperHeight, 'divPaperHeight', true)
					    appendPropertyItem(PropertyOptions.maxPageHeight, 'divMaxPageHeight', false)
						 
						appendPropertyItem(PropertyOptions.showTitle, 'show_title', false)
						appendPropertyItem(PropertyOptions.showSum, 'show_sum', false)
						appendPropertyItem(PropertyOptions.boldSum, 'bold_sum', false)
						
						appendPropertyItem(PropertyOptions.showPageSum, 'show_page_sum', false)
						appendPropertyItem(PropertyOptions.showGroupSum, 'showGroupSum', false)

                        appendPropertyItem(PropertyOptions.barcodePicText, 'barcode_pic_text_hide', false)
		
						appendPropertyItem(PropertyOptions.hideEmpty, 'hide_empty', false)
						appendPropertyItem(PropertyOptions.chineseNum, 'chinese_num', false)

						appendPropertyItem(PropertyOptions.showLines, 'showLines', false)
						if (AvailElements.table) {
							appendPropertyItem(AvailElements.table, 'sortColumns', false, true)
							var cols = JSON.parse(JSON.stringify(AvailElements.table))
							cols.splice(0, 0, { name: '', title: '' })
							appendPropertyItem(cols, 'groupSumByColumn', false)							
						}

						appendPropertyItem(PropertyOptions.sortDirection, 'sortDirection', false)
						appendPropertyItem(PropertyOptions.showEmptyRows, 'showEmptyRows', false)
						appendPropertyItem(PropertyOptions.printBackImage, 'printBackImage', false)
						appendPropertyItem(PropertyOptions.showStyle, 'show_style', false)

						appendPropertyItem(PropertyOptions.align, 'align', false)
						appendPropertyItem(PropertyOptions.font, 'font', false)
						appendPropertyItem(PropertyOptions.font_style, 'font_style', false)
						appendPropertyItem(PropertyOptions.font_bold, 'font_bold', false)
						appendPropertyItem(PropertyOptions.font_cent, 'font_cent', false)
						appendPropertyItem(PropertyOptions.font_underline, 'font_underline', false)
						appendPropertyItem(PropertyOptions.exceedAction, 'exceedAction', false)

						console.log('sheetType is' + sheetType)
						if (sheetType === 'BQ') {
							$('#divLabelLineCount').show()
                            $('#divLabelHorizontalGap').show()
                            $('#divLabelVerticalGap').show()
						} else {
							$('#divLabelLineCount').hide()
							$('#divLabelHorizontalGap').hide()
                            $('#divLabelVerticalGap').hide()
						}

						setTimeout(function () {
							EnableDragToResizeTable();
							EnableDragToResizeArea();
							//EnableDragToMoveArea();
						}, 500);
					}
				},
				error: function () {

				}
			});
			$(".print_sheet_selects.iconfont").mousedown(function () {
				 
				print_sheet_selects_iconfont_down = true;
			});
			$(".print_sheet_selects.iconfont").mouseup(function () {
				 
				print_sheet_selects_iconfont_down = false;
			});
		}
		function EnableDragToResizeTable() {
			var tTD; //用来存储当前更改宽度的Table Cell,避免快速移动鼠标的问题
			var table = document.getElementById("table_table");    //table的id名称
			if(!table) return
			for (j = 0; j < table.rows.length; j++) {
				table.rows[j].onmousedown = function () {
					//console.log("down 421 准备进行表格拖动");
					//记录单元格
					tTD = this;
					if (event.offsetX > tTD.offsetWidth - 10) {
						tTD.mouseDown = true;
						tTD.oldX = event.x;
						tTD.oldWidth = tTD.offsetWidth;
						//resetTableWidthByColumns()
					}

				};
				table.rows[j].onmouseup = function () {
					//	console.log("437 table.rows[j].onmouseup");
					//结束宽度调整
					if (tTD == undefined) tTD = this;
					tTD.mouseDown = false;
					tTD.style.cursor = 'default';
					//resetTableWidthByColumns()
				};
				table.rows[j].onmousemove = function (e) {
					// console.log("onmousemove",438);
					//更改鼠标样式
					if (tTD == undefined) tTD = this;

					if (event.offsetX > this.offsetWidth - 10) {
						this.style.cursor = 'col-resize';
						this.setAttribute('draggable', false);
						this.parentNode.setAttribute('draggable', false);
						this.parentNode.parentNode.parentNode.setAttribute('draggable', false);
						//console.log("onmousemove 开始拖动tr",454);
						disableDragToMoveTableColumns()
						DisableDragToMoveArea()
					}
					else {
						this.style.cursor = 'default';
						this.setAttribute('draggable', true);
					}

					if (event.buttons == 0) {
						tTD.mouseDown = false;
						return;
					}
					//取出暂存的Table Cell 
					//调整宽度
					if (tTD.mouseDown != null && tTD.mouseDown == true) {
						//记录原table宽度
						tTD.style.cursor = 'default';
						if (tTD.oldWidth + (event.x - tTD.oldX) > 0)
							tTD.width = tTD.oldWidth + (event.x - tTD.oldX);
						//调整列宽
						tTD.style.width = tTD.width + 'px';
						tTD.style.cursor = 'col-resize';

						resetTableWidthByColumns()
						//console.log("更改宽度" + w);
					}
				};
			}
		}
		console.log('before load template')
		debugger
		window.loadTemplate(SheetType, TemplateID);
		function EnableDragToResizeArea() {
			var mouseDownY = -1; var canResize = false, resizeArea = null, mode = "botton";
			$('.print-area').on('mousemove', function (e) {
				//console.log("mousemove  491");
				if (e.target.className.indexOf('print-area') == -1) return
				if (e.buttons == 0) {
					if (e.offsetY > this.offsetHeight - 10) {
						this.style.cursor = 'row-resize';
						this.setAttribute('draggable', false);
						mode = "botton";
					} else if (e.offsetY < 10) {
						this.style.cursor = 'row-resize';
						this.setAttribute('draggable', false);
						mode = "top";
					} else {
						this.style.cursor = 'default';
						this.setAttribute('draggable', true);
					}
				}
				else if (e.buttons == 1 && canResize) {
					if (mouseDownY != -1) {
						// var addY = e.offsetY - mouseDownY;
						// var curHeight = $(this).height();
						//  $(this).height(preHeight + addY+2);
						
						// var curHeight = parseFloat(this.style.height.replace('px', ''));
						//this.style.height = this.clientHeight + addY +'px';
					}
				}
			});
			$(document).on('mousemove', function (e) {
				//console.log("mousemove 鼠标移动到空白处  514");
				if (e.buttons == 1 && canResize) {
					if (mouseDownY != -1) {
						var addY = (mode == "top") ? mouseDownY - e.pageY + 2 : e.pageY - mouseDownY + 2;
						// var curHeight = $(this).height();
						//$(resizeArea).height(preHeight + addY+2);

						if (resizeArea) {
							$(resizeArea).find('.tox-tinymce').hide();
							$(resizeArea).height(preHeight + addY);
                        }
							

						// var curHeight = parseFloat(this.style.height.replace('px', ''));
						//this.style.height = this.clientHeight + addY +'px';
					}
				}
			})
			/*$('.print-area').on('mouseup', function (e) {
				//console.log("停止拖拽 528  $print-area').on('");
				//enableDragToMoveTableColumns();
				mouseDownY = -1; canResize = false; mode = "";
				if (resizeArea) {
					$(resizeArea).find('.tox-tinymce').show();
				}
			})*/
			$(document).on('mouseup', function (e) {
				//console.log("停止拖拽 528  $print-area').on('");
				//enableDragToMoveTableColumns();
				mouseDownY = -1; canResize = false; mode = "";
				if (resizeArea) {
					$(resizeArea).find('.tox-tinymce').show();
				}
			})
			$('.tr-column').on('mousedown', function (e) {
				//console.log("停止拖拽 528  $('.print-area').on('");
				var wd = e.currentTarget.offsetWidth
				if (e.offsetX > 10 && e.offsetX < wd - 20) {
					enableDragToMoveTableColumns();
				}
				//	mouseDownY = -1; canResize = false;	mode = "";
			});

			$('.print-area').on('mousedown', function (e) {
				console.log(e.offsetY, this.offsetHeight, e.pageY);
				if (e.target.className.indexOf('print_sheet_selects') >= 0) {
					resizeArea = null
					return
				}
				if (e.target.className.indexOf('print-area') == -1) return
				if (e.offsetY > this.offsetHeight - 10) {
					canResize = true;
					mouseDownY = e.pageY; preHeight = $(this).height();
					resizeArea = this;
				} else if (e.offsetY < 10) {
					canResize = true;
					mouseDownY = e.pageY;
					preHeight = $(this).height();
					resizeArea = this;
				} else {
					resizeArea = null;
					//EnableDragToMoveArea();
				}
			});
		}
		function EnableDragToMoveArea() {
			return
			//var mouseDownY = -1; var canResize = false, resizeArea = null;
			$(".print-area").attr('draggable', true)
			$(".print-area").off("dragstart")//added by xiang to avoid multiple events

			$(".print-area").on("dragstart", function (e) {
				DraggingObj = $(this);
				window.ElementOffsetX = e.originalEvent.offsetX;//modified by xiang 
				//console.log("开始拖拽", window.ElementOffsetX);
				window.ElementOffsetY = e.originalEvent.offsetY;
				PrepareSheetToAcceptAreaDrop()
			})
			 
		}
		function DisableDragToMoveArea() {
			return
			//var mouseDownY = -1; var canResize = false, resizeArea = null;
			$(".print-area").attr('draggable', false)
			$(".print-area").off("dragstart");
			$("#print_sheet").off("drop");
			/*
			 $(".print-area").off("dragover");
			$(".print-area").on("dragover", function(e){
				e.preventDefault();
			})*/
		}
		function StopSheetToAcceptAreaDrop() {
			$(".print-area").off("dragover");
			$("#print_sheet").off("dragover");
			$("#print_sheet").off("drop");
			$(".whole-container").off("dragover");
			$(".whole-container").off("drop");
		}
		function PrepareSheetToAcceptAreaDrop() {
			 
			//console.log("关闭表格拖拽，开始整体拖拽");
			$(".tr-column").off("drop");

			$(".print-area").off("dragover");
			$(".print-area").on("dragover", function (e) {
				//console.log("dragover。");
				e.preventDefault();
			})

			$("#print_sheet").off("dragover");
			$("#print_sheet").on("dragover", function (e) {
				//console.log("dragover#");
				e.preventDefault();
			})
			$("#print_sheet").off("drop");
			$("#print_sheet").on("drop", function (e) {
				//console.log("drop#570");
				e.preventDefault();
				e.stopPropagation();

				fat_id = $(this).parent().context.id;
				var offsetX = e.originalEvent.offsetX;
				var offsetY = e.originalEvent.offsetY;
				if (e.originalEvent.toElement.id != fat_id) {//toElement might be the son of dragging obj
					offsetX = $(e.originalEvent.toElement).offset().left - $('#' + fat_id).offset().left + offsetX >> 0;
					offsetY = $(e.originalEvent.toElement).offset().top - $('#' + fat_id).offset().top + offsetY;
				}

				var l = offsetX - window.ElementOffsetX >> 0;//modified by xiang 
				//console.log("drop#", print_sheet_selects_iconfont_down, l, offsetX, window.ElementOffsetX);
				//	t: offsetY-window.ElementOffsetY  
				//if (print_sheet_selects_iconfont_down)
				{
					DraggingObj.css({
						'left': l + 'px',
						//'top': style_le.t + 'px'
					})
				}
				print_sheet_selects_iconfont_down = false;
			})

			$(".whole-container").off("dragover");
			$(".whole-container").on("dragover", function (e) {
				//console.log("dragover#");
				e.preventDefault();
			})
			$(".whole-container").off("drop");
			$(".whole-container").on("drop", function (e) {
				console.log("602  drop,whole-container");
				e.preventDefault();
				e.stopPropagation();

				fat_id = $(this).parent().context.id;
				var offsetX = e.originalEvent.offsetX;
				var offsetY = e.originalEvent.offsetY;
				if (e.originalEvent.toElement.id != fat_id) {//toElement might be the son of 
					offsetX = $(e.originalEvent.toElement).offset().left - $('#' + fat_id).offset().left + offsetX;
					offsetY = $(e.originalEvent.toElement).offset().top - $('#' + fat_id).offset().top + offsetY;
				}
				//var l = -offsetX - window.ElementOffsetX>>0;//modified by xiang 
				var l = window.ElementOffsetX >> 0;//modified by xiang 
				console.log("whole-container", l, offsetX, window.ElementOffsetX);
				//l -= $("#print_sheet").offset().left;
				//l -= $("#print_sheet").offset().left;
				//	t: offsetY-window.ElementOffsetY  
				//if (print_sheet_selects_iconfont_down)
				{
					DraggingObj.css({
						'left': l + 'px',
						//'top': style_le.t + 'px'
					})
				}
				print_sheet_selects_iconfont_down = false;
			})
		}
		// 左边列表
		function loadTabAreas(areas, availElements) {
			//console.log(availElements)
			let htmls = '';
			areas.map((area) => {
				htmls += `<div class="drag_navs_more" style="z-index:10;">
					<div class="drag_navs_more_on" style="cursor:pointer;border-bottom-style:solid;border-color:#eee; border-bottom-width:1px;padding-top:10px;padding-left:12px;" id="${area.id}">
					  <span class="iconfont" style="margin:15px;margin-right:12px;"> ${area.icons}</span >`;
				if (area.id !== 5) {
					htmls += '<i class="iconfont">&#xe621;</i>'
				}
				htmls += `<div style="margin-left:14px;margin-bottom:10px;font-size:12px;color:#777;">` + area.title + "</div>"
				htmls += '</div>'
				var cls = "toolbox-item"
				if (area.id == 'nav_table') {
					cls = "toolbox-column"
				}
				if (area.name && availElements[area.name]) {
					htmls += `<div class="toolbox ${cls}" id="toolBox_${area.id}">` +
						'<ul>';
					availElements[area.name].map((element) => {
						let data = {
							math: area.name,
							name: element.name,
							title: element.title,
							value: element.value
						}
						//console.log(element)						 
					 
						htmls += `<li class="toolbox_li"  id=${element.name} value="leftNav" data=${JSON.stringify(data)} 
							draggable="true">${element.title}</li>`
					})
					htmls += '</ul></div>';
				} else { }
				htmls += '</div>'
			})
			$("#drag_navs_boxs_m").html(htmls)
			bindTooboxDragStart();
			dragNavsShow();
			dragClass();
			//enableDragToMoveTableColumns();
			$(".toolbox-item .toolbox_li").on("mousedown ", function (e) {
				bindTooboxDragStart()
				//  enableDragToMoveTableColumns()
			})
			$(".toolbox-column #custom_table").on("mousedown ", function (e) {
				// bindTooboxDragStart()
				bindTooboxDragStart()
			})
			$(".toolbox-column .toolbox_li:not(#custom_table)").on("mousedown ", function (e) {
				// bindTooboxDragStart()
				enableDragToMoveTableColumns()
			})

		}
		// 点击显示左边栏进行拖拽
		function dragNavsShow() {
			$(".drag_navs_more_on").on("click", function (e) {
				console.log("点击显示左边栏进行拖拽");
				e.preventDefault();
				e.stopPropagation();
				$('.drag_navs_more_on').removeClass('drag_navs_more_active')
				$(this).addClass('drag_navs_more_active')
				// '#409eff
				$('.toolbox').hide(100)
				$(this).parent().find('.toolbox').show(100)
			})
		}
		//点击显示中间栏的框
		function dragClass() {
			//勾选显示区域
			$('.drag_navs_more_on i').toggle(function () {
				$(this).addClass('selectIs')
				$(this).html('&#xe6cc;')
				let that_ = $(this).parent()[0].id;
				if (that_ === "nav_pageHead") {
					console.log(123)

					$("#print_pageHead").show(100)
				} else if (that_ === "nav_tableHead") {
					$("#print_tableHead").show(100)
				} else if (that_ === "nav_table") {
					$("#print_table").show(100)
				} else if (that_ === "nav_tableTail") {
					$("#print_tableTail").show(100)
				} else if (that_ === "nav_pageTail") {
					$("#print_pageTail").show(100)
				}
			}, function () {
				$(this).removeClass('selectIs')
				$(this).html('&#xe621;')
				let that_ = $(this).parent()[0].id;
				if (that_ === "nav_pageHead") {
					$("#print_pageHead").hide(100)
				} else if (that_ === "nav_tableHead") {
					$("#print_tableHead").hide(100)
				} else if (that_ === "nav_table") {
					$("#print_table").hide(100)
				} else if (that_ === "nav_tableTail") {
					$("#print_tableTail").hide(100)
				} else if (that_ === "nav_pageTail") {
					$("#print_pageTail").hide(100)
				}
			})
		}
		// 拖拽事件
		let dragObjectType = '';
		let DraggingObj = ''

		function bindTooboxDragStart() {
			//ElementOffsetX
			$(".toolbox_li").attr('draggable', true)
			$(".toolbox_li").off("dragstart")
			$(".toolbox_li").on("dragstart", function (e) {
				DraggingObj = $(this)
				window.ElementOffsetX = e.originalEvent.offsetX;//modified by xiang 
				console.log("按下", window.ElementOffsetX);
				window.ElementOffsetY = e.originalEvent.offsetY;
				dragObjectType = 1;
				bindPrintItemDropEndEvent()
			})
		}

		function bindPrintItemDropEndEvent() {
			//  console.log("bindPrintItemDropEndEvent");
			let steDat = '';
			let style_le = {};
			let fat_id = '';
			$(".print-area").off("dragover");
			$(".print-area").on("dragover", function (e) {
				e.preventDefault();
			})
			$(".print-area").off("drop");//added by xiang to avoid multiple events
			$(".print-area").on("drop", function (e) {
				$(".print-area").off("drop");
				if (print_sheet_selects_iconfont_down) { return; }
				console.log("drop#747", print_sheet_selects_iconfont_down);
				e.preventDefault();
				e.stopPropagation();

				fat_id = $(this).parent().context.id;
				var offsetX = e.originalEvent.offsetX;
				var offsetY = e.originalEvent.offsetY;
				if (e.originalEvent.toElement.id != fat_id) {//toElement might be the son of 
					offsetX = $(e.originalEvent.toElement).offset().left - $('#' + fat_id).offset().left + offsetX;
					offsetY = $(e.originalEvent.toElement).offset().top - $('#' + fat_id).offset().top + offsetY;
				}

				let objs = {
					l: offsetX - window.ElementOffsetX,//modified by xiang 

					t: offsetY - window.ElementOffsetY
				}
				style_le = objs;
				let stys = 'left:' + style_le.l + 'px;top:' + style_le.t + 'px;'
				//console.log("DraggingObj", DraggingObj[0].attributes.data, DraggingObj[0]);
				let datas = {};
				let bl = 0;
				try {
					JSON.parse(DraggingObj[0].attributes.data.nodeValue);
					bl = 1;
				}
				catch (e) {
					bl = 0;
				}
				if (bl) {
					console.log("*" + DraggingObj[0].attributes.data.nodeValue + "*", DraggingObj[0].attributes.data);
					datas = JSON.parse(DraggingObj[0].attributes.data.nodeValue)
				}
				let objS = datas
				var html_fat = '<div class="print-item" id="' + $("#" + fat_id)[0].children.length + '"  style="' +
					stys + '" draggable="true" data=' + JSON.stringify(objS) + '><image class="remove-btn"/><p>' + datas.title + '</p><span>' + datas.value +
					'<span></div>';
				if (datas.title == '图片') {
					var demoImg = "data:image/png;base64,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"
                    html_fat = `<div class="print-item" id="${$("#" + fat_id)[0].children.length}"  style="${stys}" draggable="true" data=${JSON.stringify(objS)}><img class="print-image" src="${demoImg}" /><image class="remove-btn"/><span></div>`;
                } else if (datas.name.match('barcode_picture')) {
                    var demoImg = "https://yingjiang.obs.cn-east-3.myhuaweicloud.com/common-resources/sample_barcode_img.png";
                    html_fat = `<div class="print-item" id="${$("#" + fat_id)[0].children.length}"  style="${stys}" draggable="true" data=${JSON.stringify(objS)}><img class="barcode-image" src="${demoImg}" width="60"/><image class="remove-btn"/><span></div>`;
                }
				if (dragObjectType === 0) {
					DraggingObj[0].id = $("#" + fat_id)[0].children.length + 1;
					DraggingObj.css({
						'left': style_le.l + 'px',
						'top': style_le.t + 'px'
					})
					console.log($(this)[0].id)
					console.log(DraggingObj.parent()[0].id)
					if (DraggingObj.parent()[0].id === $(this)[0].id) {
						DraggingObj.css({
							'left': style_le.l + 'px',
							'top': style_le.t + 'px'
						})
					} else {
						// $("#"+fat_id).append(DraggingObj);
						if ($(this)[0].id === "print_table") {
							console.log(4321)
						} else {
							console.log($(this))
							$(this).append(DraggingObj);
						}
					}
					dragObjectType = '';
					bindDataItemsClickEvent();
				} else if (dragObjectType === 1) {
					dragObjectType = '';

					if (datas.name == 'custom_table') {
						/*$(this).append(`<textarea id="custom_table_head">Hello, World!</textarea>`)
						var areaHeight=$(this).height()
						tinymce.init({
							selector: '#custom_table_head',
							language:'zh_CN',
							menubar: '',
							toolbar: '',
							table_toolbar: '',
							statusbar: false, 
							plugins: "table,quickbars",
							quickbars_selection_toolbar: 'bold italic | link fontsizeselect fontselect',
							fontsize_formats: '2.5mm 3mm 3.5mm 4mm 4.5mm',
							font_formats: '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;宋体=simsun,serif;',
							height:"95%",
							// 此处为图片上传处理函数，这个直接用了base64的图片形式上传图片
							images_upload_handler: (blobInfo, success, failure) => {
								// 这是将图片以base64存储在代码上
								const img = 'data:image/jpeg;base64,' + blobInfo.base64()
								success(img)
							}
						})*/
						
				var tbContent = `

				<table style="border-collapse: collapse; width:100%;height: 20px;font-size: 3mm;font-family:宋体;" border="1">

				<tr style="height: 5mm;">
				<td style="width: 50px;">&nbsp;</td>
				<td style="width: 50px;">&nbsp;</td>
				<td style="width: 50px;">&nbsp;</td>
				<td style="width: 50px;">&nbsp;</td>
				</tr>
				<tr style="height: 5mm;">
				<td style="width: 50px;">&nbsp;</td>
				<td style="width: 50px;">&nbsp;</td>
				<td style="width: 50px;">&nbsp;</td>
				<td style="width: 50px;">&nbsp;</td>
				</tr>
				</table>`
						var areaID = this.id
                appendTinyMceEditor(areaID, tbContent) 

/*
						 
	<table border="1" data-mce-style="border-collapse: collapse; width: 98.7345%; height: 77px;" style="border-collapse: collapse; width: 98.7345%; height: 77px;" data-mce-selected="1">
	  <tbody>
	    <tr style="height: 18px;" data-mce-style="height: 50%;">
		   <td style="width: 46.1845%; height: 18px;">合计金额(大写):{总额}&nbsp; &nbsp;{总额_大写}</td>
		   <td style="width: 24.0819%; height: 18px;">销｛销数｝ 赠｛赠数｝</td>
		   <td style="width: 11.9271%; height: 18px;"><br></td>
		   <td style="width: 11.1369%; height: 18px;"><br>
		   </td>
		</tr>
		<tr style="height: 10px;" data-mce-style="height: 50%;">
		   <td style="height: 10px; width: 93.3304%;" colspan="4">单据说明</td>
		</tr>
	</tbody>
	</table>


						 
 */
						/*
						setTimeout(() => {
							tinyMCE.editors['custom_table_head'].setContent(tbContent); 
							var divEditor = tinyMCE.editors['custom_table_head'].getContainer()
							var bd = $(divEditor).find('.tox-edit-area');//.mce-content-body')
							var bd1 = bd[0].childNodes[0].contentDocument.body
							$(bd1).css('margin', '0px');
                        },500)*/
					
					}
					else {
						if ($(this)[0].id === "print_table") {

						} else {
							$(this).append(html_fat);
						}
						bindPrintItemsDragStartEvent();
						bindDataItemsClickEvent();
                    }
					
				}
				// else if(dragObjectType === 2){
				bindRemoveBtnEvent()
				// }
			})

		}
		function bindRemoveBtnEvent() {
			$('.remove-btn').off('click');
			$('.remove-btn').on('click', function () {
				//console.log("781 remove-btn");

				if (SelectedObj) SelectedObj.remove();
				if (SelectedObj.length > 0 && SelectedObj[0].className.indexOf('tr-column') >= 0) {
					resetTableWidthByColumns()
				}
			});
		}
		function bindPrintItemsDragStartEvent() {
			$(".print-item").off("dragstart");//added by xiang to avoid multiple events
			$(".print-item").on("dragstart", function (e) {
				e.stopPropagation();
				//e.preventDefault()
				DraggingObj = $(this);
				dragObjectType = 0;
				window.ElementOffsetX = e.originalEvent.offsetX;//modified by xiang 
				console.log("按下826", window.ElementOffsetX);
				window.ElementOffsetY = e.originalEvent.offsetY;
				bindPrintItemDropEndEvent()
			})
		}

		function renderTemplate(obj) {
			//console.log(obj)

			let widths = '';
			let heights = '';
			let sWidth = '';
			let sHeight = '';
			let sPaperHeight = '';
			let fontSizes = 0;
			let fontFami = ''
			if (obj.fontSize) {
				fontSizes = mm2px(obj.fontSize);
			} else {
				fontSizes = 0
			}
			if (obj.fontName) {
				PropertyOptions.font.map(item => {
					if (item.title === obj.fontName) {
						fontFami = item.name
					}
				})
			} else {
				fontFami = ''
			}

			if (window.SheetType) {
				PropertyOptions.font.map(item => {
					if (item.title === obj.fontName) {
						fontFami = item.name
					}
				})
			}


			$("#print_sheet").css({
				'font-size': mm2px(obj.fontSize ? obj.fontSize : 0) + 'px',
				'font-family': fontFami,
				'font-weight': obj.fontBold ? 'bold' : ''
			})

			if (obj.exceedAction !== undefined && obj.exceedAction !== '') $('#print_sheet').data('data-exceedaction',obj.exceedAction)


			 
			if(!obj.paddingTop) obj.paddingTop=3
			if (obj.paddingTop) {
				var d=mm2px(obj.paddingTop)
				$('#paddingTop').val(obj.paddingTop);				
				$('#print_sheet').css('padding-top',d+'px')
			}
			if(!obj.paddingLeft) obj.paddingLeft=3
			if (obj.paddingLeft) {
				d=mm2px(obj.paddingLeft)
				$('#paddingLeft').val(obj.paddingLeft);				
				$('#print_sheet').css('padding-left',d+'px')
			}
			if(!obj.paddingRight) obj.paddingRight=3
			if (obj.paddingRight) {
				d=mm2px(obj.paddingRight)
				$('#paddingRight').val(obj.paddingRight); 
				$('#print_sheet').css('padding-right',d+'px')
			}
			if(!obj.paddingBottom) obj.paddingBottom=3
			if (obj.paddingBottom) {
				d=mm2px(obj.paddingBottom)
				$('#paddingBottom').val(obj.paddingBottom); 
				$('#print_sheet').css('padding-bottom',d+'px')
			}

			 

			$("#print_sheet").css('background-size', '100% 100%')
			$("#print_sheet").css('background-repeat', 'no-repeat')
			$("#print_sheet").css('background-color', `#fff`)
			if (obj.backImage) {
				if (obj.backImage.indexOf('url(') == -1)
					obj.backImage = `url(${obj.backImage})`
				$("#print_sheet").css('background-image', obj.backImage)
				$("#print_sheet").data('printBackImage', obj.printBackImage)
			}

			// 渲染初始化元素
			if (obj.pageHead) {
				loadPrintArea('print_pageHead', obj.pageHead, PropertyOptions)
				if(obj.pageHead.htmlContent){ 
				     appendTinyMceEditor('print_pageHead',  obj.pageHead.htmlContent) 
				}
			}
			if (obj.tableHead) {
				loadPrintArea('print_tableHead', obj.tableHead, PropertyOptions)

				if(obj.tableHead.htmlContent){ 
				     appendTinyMceEditor('print_tableHead',  obj.tableHead.htmlContent) 
				}

			}
			if (obj.table) {
				loadPrintArea('print_table', obj.table, PropertyOptions)
				if(obj.table.htmlContent){ 
				     appendTinyMceEditor('print_table',  obj.table.htmlContent) 
				}
			}
			if (obj.tableTail) {
				loadPrintArea('print_tableTail', obj.tableTail, PropertyOptions)
				if(obj.tableTail.htmlContent){ 
				     appendTinyMceEditor('print_tableTail',  obj.tableTail.htmlContent) 
				}
				// 加载新表格数据，暂时先放在外面
				
			}
			if (obj.pageTail) {
				loadPrintArea('print_pageTail', obj.pageTail, PropertyOptions)
				if(obj.pageTail.htmlContent){ 
				     appendTinyMceEditor('print_pageTail',  obj.pageTail.htmlContent) 
				}
			}

			bindPrintItemsDragStartEvent();//modified by xiang. moved to here from loadPrintArea to avoid multiple events
			bindDataItemsClickEvent();
			bindAreaClickEventToShowProperties();
			bindSheetClickEventToShowProperties();
			function loadPrintArea(areaID, objs, PropertyOptions) {

				if (areaID === "print_table") {
					loadTable(objs)
				}
				let son_fonts = ''
				if (objs.fontName) {
					PropertyOptions.font.map(item => {
						if (item.title === objs.fontName) {
							son_fonts = item.name
						}
					})
				} else {
					/*son_fonts = '<div class="print-item" id="' + $("#" + fat_id)[0].children.length + '"  style="' + stys +
						'" draggable="true" data=' + JSON.stringify(objS) + '><image class="remove-btn"/><p>' + datas.title + '</p><span>' + datas.value +
						'<span></div>'*/
				}
				if (objs.fontSize) $("#" + areaID).css({ 'font-size': mm2px(objs.fontSize || 0) + 'px' })
				if (son_fonts) $("#" + areaID).css({ 'font-family': son_fonts })
				if (objs.fontBold) $("#" + areaID).css({ 'font-weight': objs.fontBold })

				if (objs.exceedAction !== undefined && objs.exceedAction !== '') $("#" + areaID).data('data-exceedaction',objs.exceedAction)

				setTimeout(function () {
					$("#" + areaID).css({
						//'left': mm2px(objs.left || 20) + 'px',
						'height': mm2px(objs.height || 20) + 'px'
					})

				}, 500);//很奇怪，如果不延时,print_tableHead高度会很矮

				//document.all[areaID].style.height = mm2px(objs.height || 20) + 'px';
				// ,'height':mm2px(objs.height? objs.height:0)+'px'
				$("#" + areaID).show(0)
				if (objs.elements && objs.elements.length > 0) {
					
					if (areaID === "print_table") {
					}
					else {
						let objs_son = objs.elements;
						let htmls_sons = ''
						objs_son.map((item, index) => {
							let objS_Sons = {
								name: item.name,
								title: item.title,
								value: item.value ? item.value : ''
							}

								var dataChineseNum = '', dataHideEmpty = '', dataShowTitle = '',data_font_underline='',data_element_width='',data_element_height='',data_dot_len='',data_char_spacing='',dataExceedAction ='',dataShowStyle='', data_show_value
							if (item.chineseNum && item.chineseNum.toString() == "true") dataChineseNum = ' data-chineseNum="true" '
							if (item.hideEmpty && item.hideEmpty.toString() == "true") dataHideEmpty = ' data-hideEmpty="true" '
							if (item.showTitle !== undefined && item.showTitle !== '') dataShowTitle = ` data-showTitle="${item.showTitle}" `

						    if (item.font_underline !== undefined && item.font_underline !== '') data_font_underline = ` data-font_underline="${item.font_underline}" `

						    if (item.exceedAction !== undefined && item.exceedAction !== '') dataExceedAction = ` data-exceedaction="${item.exceedAction}" `
							if (item.showStyle !== undefined && item.showStyle !== '') dataShowStyle = ` data-show_style="${item.showStyle}" `

                            console.log('item:', item)
                            let dataBarcodePict = ''
                            if (item.barcode_pic_text_hide && item.barcode_pic_text_hide.toString() == "true")
                                dataBarcodePict += ' data-barcode_pic_text_hide="true"'
                            if (item.barcode_pic_text_size !== undefined && item.barcode_pic_text_size !== '')
                                dataBarcodePict += ` data-barcode_pic_text_size="${item.barcode_pic_text_size}"`

						 
							//if (item.chineseNum == "true") objS_Sons.chineseNum=item.chineseNum 
							//if (item.hideEmpty == "true") objS_Sons.hideEmpty=item.hideEmpty
							//if (item.showTitle) objS_Sons.showTitle=item.showTitle

							let fontFami_son = ''
							if (item.fontName) {
								PropertyOptions.font.map(itemFont => {
									if (itemFont.title === item.fontName) {
										fontFami_son = itemFont.name
									}
								})
							} else {
								fontFami_son = ''
							}
							let itemStyle = "left:" + mm2px(item.x ? item.x : 0) + "px;top:" + mm2px(item.y ? item.y : 0) + "px;";
							if (item.fontSize) {
								itemStyle += "font-size:" + mm2px(item.fontSize) + "px;";
							}

							if (item.fontBold) {
								itemStyle += "font-weight:" + item.fontBold + ";";
							}
							if (item.fontStyle) {
								itemStyle += "font-style:" + item.fontStyle + ";";
							}
							if (fontFami_son) {
								itemStyle += "font-family:" + fontFami_son + ";";
							}
							if (item.align) {
								itemStyle += "text-align:" + item.align + ";";
							}
							var imgStyle = ''
							var barcodeMode = false
							if (item.name.match('barcode_picture')) {
								barcodeMode = true
								item.image = `https://yingjiang.obs.cn-east-3.myhuaweicloud.com/common-resources/sample_barcode_img.png`
							}
							if (item.image) {
								if (item.width) {
									imgStyle += "width:" + mm2px(item.width) + "px;";
								}
								if (item.height) {
									if (barcodeMode) {
										imgStyle += "height:" + (Number(item.height) / 1.4).toFixed(4) + "px;";
									} else {
										imgStyle += "height:" + mm2px(item.height) + "px;";
									}
								}
									
								itemStyle += imgStyle
							}
							else if (item.width) {
								if (item.width) {
									itemStyle += "width:" + mm2px(item.width) + "px;";
								}
							}
							 
							if (item.width) {
								//debugger
								data_element_width = ` data-element_width="${item.width}" `
							}
							if (item.height) data_element_height = ` data-element_height="${item.height}" `

							if (item.dotLen) { 
								data_dot_len = ` data-dot_len="${item.dotLen}" `
							}
							if (item.charSpacing) { 
								data_char_spacing = ` data-char_spacing="${item.charSpacing}" `
							}

							if (item.showValue) {
								data_show_value = ` data-show_value="${item.showValue}" `
							}
						 
							var itemTitle = item.title
							if (!itemTitle) itemTitle = '未设置'
							var curItem = ''
							var id = Number($("#" + areaID)[0].children.length) + Number(index)
							var data = JSON.stringify(objS_Sons)
					 

							if (item.image) {
                                curItem = `<div class="print-item" id="${id}" ${data_element_width} ${data_element_height} ${dataBarcodePict} style="${itemStyle}" draggable="true" data='${data}'><img class="print-image" style="${imgStyle}" src="${item.image}" /><image class="remove-btn"/><span></div>`;
							}
							else {

								curItem = `<div class="print-item" id="${id}" ${dataChineseNum} ${dataHideEmpty} ${dataShowTitle} ${data_font_underline} ${data_element_width} ${data_dot_len} ${data_char_spacing} ${data_show_value} ${dataExceedAction} ${dataShowStyle} style="${itemStyle}" draggable="true" data='${data}'><image class="remove-btn"/><p>${itemTitle}</p>`;

								if (item.value) {
									curItem += '<span>' + item.value + '<span></div>'
								}
								else {
									curItem += '<span><span></div>'
								}
							}

							htmls_sons += curItem
						})
						$("#" + areaID).append(htmls_sons);
						//var imgs = $('.print-image')
						//for (var i = 0; i < imgs.length; i++) {
						//imgs[i].base64=imgs[i].src
						//}

					}
				}

				//bindPrintItemsDragStartEvent();
				//bindDataItemsClickEvent;
				if (objs.show == false) {
					$('#' + areaID).hide();
				}
				else {
					$('#' + areaID.replace('print_', 'nav_') + ' i').addClass('selectIs').html('&#xe6cc;')

				}
			}
			if (obj.width) {
				widths = mm2px(obj.width);
				sWidth = obj.width
			} else {
				widths = mm2px(215.9);
				sWidth = 215.9
			}
			if (obj.height.toString() == "0") {
				heights = mm2px(139.7)
				sHeight = "自适应"
			}
			else if (obj.height) {
				heights = mm2px(obj.height)
				sHeight = obj.height
			} else {
				heights = mm2px(139.7)
				sHeight = 139.7
			}

			if (!obj.paperHeight || obj.paperHeight.toString() == "0") { 
				sPaperHeight = "未设"
			}
			else{ 
				sPaperHeight = obj.paperHeight
			}

			var sMaxPageHeight=''
			if (!obj.maxPageHeight || obj.maxPageHeight.toString() == "0") { 
				sMaxPageHeight = "未设"
			}
			else{ 
				sMaxPageHeight = obj.maxPageHeight
			}
			
			var labelLineCount = ''
			if (!obj.eachLineLabel) {
				labelLineCount = '1'
			} else {
				labelLineCount = obj.eachLineLabel
			}
			var labelHorizonGap = ''
			if (!obj.horizontalGap) {
				labelHorizonGap = '2'
			} else {
				labelHorizonGap = obj.horizontalGap
			}
			var labelVerticalGap = ''
            if (!obj.verticalGap) {
				labelVerticalGap = '2'
			} else {
                labelVerticalGap = obj.verticalGap
			}

			if (obj.landscape) {
				//var tmp = sWidth;
				//sWidth = sHeight;
				//sHeight = tmp;
				var tmp = widths;
				widths = heights;
				heights = tmp;
			}
			$("#ckLandscape")[0].checked = obj.landscape
			$("#ckInvertX")[0].checked = obj.invertX
			$("#ckInvertY")[0].checked = obj.invertY
			$("input[name='page_width']").val(sWidth)
			$("input[name='page_height']").val(sHeight)
			$("input[name='inputPageSize']").val(sWidth + '*' + sHeight)

			$("input[name='paper_height']").val(sPaperHeight)
			$("input[name='max_page_height']").val(sMaxPageHeight)
			
			$("input[name='eachLineLabel']").val(labelLineCount)
            $("input[name='horizontalGap']").val(labelHorizonGap)
            $("input[name='verticalGap']").val(labelVerticalGap)
			
			//$("#templateName").val(obj.name)

			PropertyOptions.sheetTypeList.map(item => {
				if (item.name === SheetType) {
					$("#sheetType").val(item.title)
				}
			})

			$("#print_sheet").css({
				'width': widths + 'px',
				'height': heights + 'px'
			})

			bindRemoveBtnEvent()


			$("#print_tableHead").css('display:block');
			//setTimeout(function () {
			//	document.all.print_tableHead.style.height = mm2px(100 || 20) + 'px';
			//}, 3000);
			updatePageSize()
			//RefreshSheetOuterSize()
		}

		function appendTinyMceEditor(areaID,htmlContent) {
			//print_pageTail  obj.pageTail.htmlContent
			$(`#${areaID}`).append(`<textarea id="${areaID}_editor_textarea" style="visibility:hidden;"></textarea>`)

			tinymce.init({
				selector: `#${areaID}_editor_textarea`,
						language:'zh_CN',
						menubar: '',
						toolbar: '',
						table_toolbar: '',
						statusbar: false,
						plugins: "table,quickbars",
				        content_style: 'td {overflow: hidden; white-space: nowrap;  word-break: break-all; } table{table-layout: fixed;}',
				        table_style_by_css: true,
						//contextmenu: "bold copy quickimage",
						quickbars_insert_toolbar:'',
						quickbars_selection_toolbar: 'bold italic | link fontsizeselect fontselect ',
						fontsize_formats: '2.5mm 3mm 3.5mm 4mm 4.5mm 5mm 5.5mm 6mm 6.5mm 7mm',
						font_formats: "微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;宋体=simsun,serif;黑体=SimHei;楷体=KaiTi;仿宋=FangSong;隶书=LiSu;幼圆=YouYuan;",
						height: "40px", 
				   // 此处为图片上传处理函数，这个直接用了base64的图片形式上传图片
						images_upload_handler: (blobInfo, success, failure) => {
							// 这是将图片以base64存储在代码上
							const img = 'data:image/jpeg;base64,' + blobInfo.base64()
							success(img)
						}
					});

					var hInterval=setInterval(function() {
						var content= htmlContent// obj.pageTail.htmlContent
						
						var replaceSize3=function(html,sizeType) {
							//var reg = /height: (?<size>[\d+\.]*?)mm/g
							//var reg = new RegExp(`${sizeType}: (?<size>[\\d+\\.]*?)mm`, 'g')
							var reg = new RegExp(`${sizeType}:(\\s*)(<size>[\\d+\\.]*)(\\s*)mm`, 'g')

							//var reg=new RegExp(sizeType+': (?<size>[\\d+\\.]*?)mm','g')	
							var sizes = {}
							//debugger
							while (true) {
								var result = reg.exec(content);
					
								//提取组时，结果数组中的0元素表示本身，从1元素开始是与(匹配的内容
								if (result == null) {
									break;
								}
								var size = result.groups.size						
								var pxSize = mm2px(size)
								sizes[size] = pxSize						 
							}
							for (var size in sizes) {
								var pxSize=sizes[size]
								content=content.replace(new RegExp( `${sizeType}: ` + size + "mm", "gm"), `${sizeType}: ` + pxSize + "px")
							}
							return content
						}

						var replaceSize=function(html,sizeType) {
						     var reg=new RegExp(`${sizeType}:(\\s*)(\\d+|(\\d*.\\d*))(\\s*)mm`,'g')
							var result = html.match(reg)
							if (result) {
								result.forEach(sizeStr => { 
									var numReg = sizeStr.match(/(\-|\+)?\d+(\.\d+)?/)
									var numMM = numReg[0]
									var pxSize = mm2px(numMM)
									html = html.replace(new RegExp( `${sizeType}: ` + numMM + "mm", "gm"), `${sizeType}: ` + pxSize + "px")
								})
                            }
						    return html
						}
			 
						content = replaceSize(content, 'height')
						content = replaceSize(content, 'width')
						$(`#${areaID} .tox-tinymce`).css({ 'margin-top': '2px', 'margin-bottom': '2px'})
						tinyMCE.editors[`${areaID}_editor_textarea`].setContent(content);

					//	var divEditor = tinyMCE.editors[`${areaID}_editor_textarea`].getContainer()
						var mce = $(`#${areaID} .tox-tinymce`)
                        debugger

						//var bd = $(divEditor).find('.tox-edit-area');//.mce-content-body')
					   var bd = $(`#${areaID} .tox-edit-area`)
						if (bd[0].childNodes[0] == null) 
						{ 
							console.log("覃总测试")
							return 
						}
						var frame = bd[0].childNodes[0];
						$(frame).css('height','auto')
						var bd1 = frame.contentDocument.body
						$(bd1).css('margin', '0px');
						$(bd1).css('position', 'relative');

						var $tb= $(bd1).find('table')
						var ht = $tb.height()
						mce.css('height', ht + 'px')

						$(bd1).append($('<iframe style="top:0px;height:100%;visibility:hidden;position:absolute;">hello world</iframe>'))
						var sonFrame = $(bd1).find('iframe');
						var sonWin = sonFrame[0].contentWindow
						sonWin.onresize = function () {
							ht = $(sonWin).height()
							mce.css('height',ht+'px')
						}
					 
						window.onMceTableRemoved = function (editor) {
							if (editor) {
							   $(editor.parentNode).find('textarea').remove()
							   $(editor).remove()
                            }
							
						}

						window.onMceTableRowRemoved = function (editor) {

                        }
						clearInterval(hInterval)	
                    },500)
        }

		function RefreshSheetOuterSize() {
			var sheet_w = $('#print_sheet').width(); 
			var outer_w = $('#print_sheet_outer').width();
			var container_w = $('.whole-container').width();
			var padding =parseInt($('#print_sheet_outer').css('padding'));
			outer_w = sheet_w + 100;
			if (outer_w < container_w - padding * 2 -25) outer_w = container_w - padding * 2 - 25; 
		    $('#print_sheet_outer').width(outer_w)
			
        }
	

		function titlesAbost() {
			let width_ = ($("#print_sheet").css("width")).split("px");
			let leftPx = (Number(width_[0]) - Number($(".print-item_titles").width())) / 2
			$(".print-item_titles").css({
				'left': leftPx + 'px',
				'top': '0'
			})
	    }

	    function px2mm(val, direction) {
		    val = val.toString().replace('px','')
			let dpi = windowDips
			let resultNumber
			if (direction === "w") {
				resultNumber =Number((Number(val) / Number(dpi[0]) * 25.4).toFixed(4));
			} else {
				resultNumber = Number((Number(val) / Number(dpi[1]) * 25.4).toFixed(4));
			}
			return resultNumber;
		}
		function mm2px(val, direction) {
			let dpi = windowDips;
			if(!val) return ''
			let resultNumber
			if (direction === "w") {
				resultNumber = Number((Number(val) / 25.4 * Number(dpi[0])).toFixed(4));

			} else {
				resultNumber = Number((Number(val) / 25.4 * Number(dpi[1])).toFixed(4));
			}
			return resultNumber;
		}
		let windowDips;
		windowDipsFun();
		// 左边栏显示隐藏
		$("#drag_navs_fath").toggle(() => {
			$("#drag_navs_wid").hide(200);
			$("#drag_navs_boxs").hide(200);
			$("#drag_navs_fath").css('color', '#409eff');
		}, () => {
			$("#drag_navs_boxs").show(200);
			$("#drag_navs_wid").show(200);
			$("#drag_navs_fath").css('color', '#999999');
		})
		// let ;
		let fontSizeSumber = [];
		font_sizes()

		function font_sizes() {
			let sizes = [{ name: '', title: '默认'}];
			for (var i = 2; i <= 10; i+=0.5) {
				let objs = {
					title: i,
					name: i
				}
				sizes.push(objs)
			}
			fontSizeSumber = sizes
			appendPropertyItem(sizes, 'font_size', false)
		} 

		// 单选公用
		function appendPropertyItem(objs, propertyID, isPageSize,multiSelect) {
			let classFath = $('#' + propertyID);
			let tops = classFath.height() + 10
			let htmls = '<div class="public_selects" style="top:' + tops + 'px;"><ul class="public_selects_ul">'
			objs.map(item => {
				if (multiSelect) {
				   htmls += `<li id="${item.name}"><input type="checkbox" id="ck_${propertyID}_${item.name}" style="width:20px;height:20px;margin-right:10px;"/><label for="ck_${propertyID}_${item.name}"> ${item.title}</label></li>`
				}
				else
				   htmls += '<li id="' + item.name + '">' + item.title + '</li>'
			})
			htmls += '</ul></div>'
			classFath.append(htmls);
			bindPropItemClickToShowOptions(propertyID, isPageSize,multiSelect)
		}
	 
		// 点击显示单选
		function bindPropItemClickToShowOptions(className, isPageSize,multiSelect) {
			//属性框input被点击后弹出选项列表
			$('#' + className + ' input').on("click", function(e) {
				e.stopPropagation();
				$('.public_selects').hide(100);
				$(this).parent().find('.public_selects').show(100)
				bindPropOptionsClickEvent(className, isPageSize,multiSelect)
			})
		};
		$(document).on("click", function() {
			$('.public_selects').hide(100);
			$('.toolbox').hide(100);
		});
		// 点击选中并赋值
		let font_style = {
			font_f: '',
			font_w: '',
			font_sty: '',
			font_s: '',
			font_cent: ''
		}
		let SelectedObj = ''
		//绑定属性下拉框的点击事件
		function bindPropOptionsClickEvent(className_son, isPageSize, multiSelect) {
			let datas;
			//属性项下拉框被选后触发
			//ck_${ propertyID } _${ item.name }
			//$('#' + className_son + ' .public_selects_ul li').off("click")
			//$('#' + className_son + ' .public_selects_ul li').on("click", function () {
			var li=`#${className_son} .public_selects_ul li`
			
			if (multiSelect) {
				$(`${li} input`).off("click")
				$(`${li} input`).on("click", function (e) {
					//if (this[0].id.indexOf('ck_') == 0) {
					onOptionClick(this.parentNode)
					//e.preventDefault()
				//	e.stopPropagation()

					//}
				})
			}
			else {
				$(`${li}`).off("click")
				$(`${li}`).on("click", function () {
					onOptionClick(this)
				})
            }
			
			

			function onOptionClick(eleLi) {
				datas = $(eleLi).text();
			 
				if(!multiSelect)
				  $('#' + className_son + ' input').val(datas)

			

				if (className_son === "font") {
					console.log($(eleLi)[0].id)
					SelectedObj.css('font-family', $(eleLi)[0].id)
				}
				if (className_son === "font_style") {
					SelectedObj.css('font-style', $(eleLi)[0].id)
					SelectedObj.find('table').css('font-style', $(eleLi)[0].id)
				}
				if (className_son === "font_bold") {
					SelectedObj.css('font-weight', $(eleLi)[0].id)
					SelectedObj.find('table').css('font-weight', $(eleLi)[0].id)
				}


			    if (className_son === "exceedAction") {
					SelectedObj.data('exceedaction',$(eleLi)[0].id)
				}

				if (className_son === "show_style") {
					SelectedObj.data('show_style',$(eleLi)[0].id)
				}


				if (className_son === "font_size") {				 
					var fontSize = $(eleLi)[0].id;	
					if (fontSize) {
						fontSize = mm2px(fontSize, "h")+ 'px';
                    } 
					SelectedObj.css('font-size', fontSize)
					 
					SelectedObj.find('table').css('font-size', fontSize)
				}

				if (className_son === "show_page_sum") {
					SelectedObj.data('showpagesum',$(eleLi)[0].id)
				}

				if (className_son === "showGroupSum") {
					SelectedObj.data('showGroupSum'.toLowerCase(), $(eleLi)[0].id)
				}
				
				if (className_son === "show_sum") { 
					SelectedObj.data('showsum',$(eleLi)[0].id)
				}
				if (className_son === "bold_sum") {
					SelectedObj.data('boldsum', $(eleLi)[0].id)
				}
				
			
				if (className_son === "showEmptyRows") { 
					SelectedObj.data('showEmptyRows',$(eleLi)[0].id)
				}

				if (className_son === "hide_empty") { 
					SelectedObj.data('hideEmpty'.toLowerCase(),$(eleLi)[0].id)
				}

				if (className_son === "chinese_num") { 
					SelectedObj.data('chineseNum'.toLowerCase(),$(eleLi)[0].id)
				}

                if (className_son === "barcode_pic_text_hide") {
                    SelectedObj.data('barcode_pic_text_hide'.toLowerCase(), $(eleLi)[0].id)
                }
					 
				if (className_son === "showLines") { 
					SelectedObj.data('showLines',$(eleLi)[0].id)
				}

				if (className_son === "groupSumByColumn") { 
					SelectedObj.data('groupSumByColumn',$(eleLi)[0].id)
				}

				if (className_son === "sortColumns") {
					var optID = $(eleLi)[0].id
					 
					var curTitles = $('#' + className_son + ' input').val()||''
					var curNames=SelectedObj.data('sortColumns') ||''
					AvailElements.table.forEach(col => {
						if (optID == col.name) {
							if (curNames.indexOf(col.name) >= 0) {
								//curTitles = curTitles.replace(col.title, '')								
								curNames = curNames.replace(col.name, '')								
							}
							else {
								//if (curTitles) curTitles += ','
							//	curTitles += col.title
								if (curNames) curNames += ','
								curNames+=col.name
							}
							curNames = curNames.replace(',,', ',')
							//curTitles = curTitles.replace(',,', ',')
							if (curNames.indexOf(',') == 0) curNames = curNames.substr(1, curNames.length - 1)
						//	if (curTitles.indexOf(',') == 0) curTitles = curTitles.substring(1, curTitles.length - 1)
							if (curNames.indexOf(',') == curNames.length) curNames = curNames.substr(0, curNames.length - 1)
						//	if (curTitles.indexOf(',') == curTitles.length) curTitles = curTitles.substring(0, curTitles.length - 1)
                        }
					})
					var arr = curNames.split(',')
					var curTitles=''
					arr.forEach(name => {
						var col = AvailElements.table.find(col => col.name == name)
						if (col) {
							if (curTitles) curTitles += ','
							curTitles+=col.title
                        }
                    })
					SelectedObj.data('sortColumns', curNames)
				    $('#' + className_son + ' input').val(curTitles) 
				}
				if (className_son === "sortDirection") {
					SelectedObj.data('sortDirection', $(eleLi)[0].id)
				}

			    if (className_son === "show_title") { 
					SelectedObj.data('showTitle'.toLowerCase(), $(eleLi)[0].id)
				}
				else if(className_son === "font_underline") { 
					SelectedObj.data('font_underline'.toLowerCase(),$(eleLi)[0].id)
				}
			
				
				 
						

				if (className_son === "printBackImage") { 
					SelectedObj.data('printBackImage',$(eleLi)[0].id)
				}
 
				if (className_son === "align") {
					//SelectedObj.find('th,td').css('text-align', $(eleLi)[0].id)
				 
					SelectedObj.css('text-align', $(eleLi)[0].id)
				}
				 
				if (className_son === "sheet_type_wrapper") {
					window.SheetType=SheetTemplate.sheetType = $(eleLi)[0].id;
					
					//SelectedObj.find('th,td').css('text-align', $(eleLi)[0].id)
				}
				 

				if (isPageSize) {
					let attrs = datas.split('*')
					var ht,wd
					if (attrs.length == 2) {
						wd = attrs[0]
						ht = attrs[1]
						ht= ht.split('(')[0]
						$("input[name='page_width']").val(wd)
						$("input[name='page_height']").val(ht)						 
					}

					ht = $("input[name='page_height']").val()
					wd = $("input[name='page_width']").val()
					if (ht == '自适应') {
					    ht=0
					}
					SheetTemplate.height = ht
					SheetTemplate.width = wd
					if (ht == 0) {
					    ht=139.7
					}
					updatePageSize()
				//	im2px(wd, 'print_sheet', 'w')
				//	im2px(ht, 'print_sheet', 'h')					

				}
			}
		}
		// 获取dpi
		function windowDipsFun() {
			let arrDPI = new Array;
			
		   if (window.screen.deviceXDPI) {
				arrDPI[0] = window.screen.deviceXDPI;
				arrDPI[1] = window.screen.deviceYDPI;
			} else {
				var tmpNode = document.createElement("DIV");
				tmpNode.style.cssText = "width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden";
				document.body.appendChild(tmpNode);
				arrDPI[0] = parseInt(tmpNode.offsetWidth);
				arrDPI[1] = parseInt(tmpNode.offsetHeight);
				tmpNode.parentNode.removeChild(tmpNode);
			}
			
			if (window.parent.CefGlue && window.parent.CefGlue.getTrueDPI) {
				var scale = window.parent.CefGlue.getScreenScale() 
				arrDPI[0] =parseFloat(window.parent.CefGlue.getTrueDPI())/scale
				arrDPI[1] = arrDPI[0]

            }
			windowDips = arrDPI;
			// onloadW_H();
		}
		// 显示中间框
		function im2px(val, contentIds, types) {
			let dpi = windowDips;
			let thats_ = $('#' + contentIds);
			if (types === "w") {						    
				let resultNumber = Number(val) / 25.4 * Number(dpi[0]);
				thats_.css('width', resultNumber + 'px');
			} else {				 
				let resultNumber = Number(val) / 25.4 * Number(dpi[1]);
				thats_.css('height', resultNumber + 'px');
			}
		}

		bindInput('page_width', 'print_sheet', 'w')
		bindInput('page_height', 'print_sheet', 'h')

		 
		$('#ckLandscape').on('change', function () {
			updatePageSize()
		})
		function updatePageSize() {
			var bLandscape = $('#ckLandscape')[0].checked;
			var w, h;
			h = $('#page_height').val()
			w = $('#page_width').val()
			if (h == '自适应') {
				bLandscape = false;
				$('#ckLandscape')[0].checked = false
				h = 0;
				$('#divOuterPaperHeight').show()
				$('#divOuterMaxPageHeight').show()
			}
			else{
				$('#divOuterPaperHeight').hide()
				$('#divOuterMaxPageHeight').hide()
			}

			SheetTemplate.width = w
			SheetTemplate.height = h
			if(h==0) h=139.7
			 
			if (bLandscape) {
				var tmp = w; w = h; h = tmp;
			}
			 
			im2px(w, 'print_sheet', 'w')
			im2px(h, 'print_sheet', 'h')

			

			RefreshSheetOuterSize() 

		}
		// 输入框
		function bindInput(className, contentIds, types) {
			$("input[name='" + className + "']").on("input", function() {
				let nums = 0;
				if ($(this).val() < 93) {
					nums = 93;
				} else {
					nums = $(this).val();
				}
				im2px(nums, contentIds, types)
				if (className == 'page_width' || className == 'page_height') {
					 updatePageSize()
                }
			})

		}
		drag_select_box()
		function drag_select_box() {
			$('.print_sheet_selects').on("click", function () {
				 
				var areaType = this.parentNode.getAttribute('data');


				//console.log($(this).parent()[0].attributes['data'].n);
				$('.print-area').css('border', '1px dashed #DCDCDC')
				$('.print_sheet_selects').css('color', '#000000')
				$(this).css('color', '#ee0a24')
				$(this).parent().css('border', '1px dashed #ee0a24')
				SelectedObj = $(this).parent()
			})
		}

	    function onElementClick(element,className,e) {
				let familys = '';
				let fontWeight = '';
				let fontSize = '';
			let fontStyles = '';
	 
				e.preventDefault();
				e.stopPropagation();
			
				//$('input[name="modify_"]').val($(element).find('p').text())
			$('.print-item').css('border', "none")
			$('.print-item').css('margin-top', "0px")
			$('.print-item').css('margin-left', "0px")
	 
				//$('.print_sheet').css('border', "none")
				$(".tr-column").removeClass('trBorders')
			    $('#print_sheet').css('border', "none")
			    $('.print-area').css('border', "1px dashed #dddddd")

			

				//console.log($(this))
			var fontFamily = $(element)[0].style.fontFamily||''
			fontFamily = fontFamily.replace('"','').replace('"','')
			if (fontFamily) {
				//console.log(123)
				PropertyOptions.font.map(item => { 
					if (item.name === fontFamily) {
						familys = item.title
					}
				})
				//console.log(familys)
			}
			if ($(element)[0].style.fontWeight) {
				PropertyOptions.font_bold.map(item => {
					if (item.name === $(element)[0].style.fontWeight) {
						fontWeight = item.title
					}
				})
			}
			if ($(element)[0].style.fontSize) {
				let datas = ($(element)[0].style.fontSize).split('px')
				fontSize = datas[0]
				if (fontSize) fontSize = px2mm(fontSize, 'h')
			}

		    if ($(element)[0].style.fontStyle) {
				PropertyOptions.font_style.map(item => {
					if (item.name === $(element)[0].style.fontStyle) {
						fontStyles = item.title
					}
				})
			}

		  			
			var font_underline = $(element).data('font_underline'.toLowerCase())
			if (font_underline !== undefined) {
				PropertyOptions.font_underline.map(item => {
					if (item.name.toString() ==font_underline.toString()) {
						font_underline = item.title
					}
				})
            }  
			$("#font_underline input").val(font_underline || '')


	  
		 
			 var hideEmpty=$(element).data('hideEmpty'.toLowerCase())
		    if (hideEmpty) {
				PropertyOptions.hideEmpty.map(item => {
					if (item.name.toString() ==hideEmpty.toString()) {
						hideEmpty = item.title
					}
				})
			}
			$("#hide_empty input").val(hideEmpty || '')

			var chineseNum = $(element).data('chineseNum'.toLowerCase())
		    if (chineseNum) {
				PropertyOptions.chineseNum.map(item => {
					if (item.name.toString() ==chineseNum.toString()) {
						chineseNum = item.title
					}
				})
			}
			$("#chinese_num input").val(chineseNum || '')

			var showTitle = $(element).data('showTitle'.toLowerCase())
			if (showTitle !== undefined) {
				PropertyOptions.showTitle.map(item => {
					if (item.name.toString() ==showTitle.toString()) {
						showTitle = item.title
					}
				})
            }  
			$("#show_title input").val(showTitle || '')


			var element_width = $(element).data('element_width')		 
			$("#element_width input").val(element_width||'')
            var element_height = $(element).data('element_height')		 
			$("#element_height input").val(element_height || '')

            var barcode_pic_text_hide = $(element).data('barcode_pic_text_hide')
            $("#barcode_pic_text_hide input").val(barcode_pic_text_hide || '')
            var barcode_pic_text_size = $(element).data('barcode_pic_text_size')
            $("#barcode_pic_text_size input").val(barcode_pic_text_size || '')

			var dot_len = $(element).data('dot_len')		 
			$("#dot_len input").val(dot_len||'')

			var char_spacing = $(element).data('char_spacing')		 
			$("#char_spacing input").val(char_spacing||'')

			var show_value = $(element).data('show_value')		 
			$("#show_value input").val(show_value || '')
			 
			var column_sum_value = $(element).data('column_sum_value')		 
			$("#column_sum_value input").val(column_sum_value || '')
			/*var image=$(element).find('.print-image')
			if (image.length > 0) {
				var d = image[0].style.width
				d = px2mm(d)
				if(!d) d=''
				$("#element_width input").val(d)

				d = image[0].style.height
				d = px2mm(d)
				if(!d) d=''
				$("#element_height input").val(d)
			}*/

		
		     $("#rowHeight").parent().hide()
			

			  $("#lineWidth").parent().hide()

			 
			$('#show_sum').parent().hide()
			 $('#bold_sum').parent().hide()
			
			$('#show_page_sum').parent().hide()
			$('#showGroupSum').parent().hide()


			//$('#align').parent().hide()
			
			$("#font input").val(familys ? familys : '')
			$("#font_bold input").val(fontWeight ? fontWeight : '')
			$("#font_size input").val(fontSize ? fontSize : '')
			$("#font_style input").val(fontStyles ? fontStyles : '')
			$("#font_underline input").val(font_underline ? font_underline : '')
			

			SelectedObj = $(element)
			var display = 'none'
			var isImage = false, isItem = false, isBarcodeImage = false;
			if (SelectedObj.attr('class') == 'print-item') {
				isItem = true
				if (SelectedObj.find('.print-image').length == 1)
				    isImage = true
			    var attrData=SelectedObj.attr('data')
				if (attrData && attrData.includes('barcode_picture'))
                    isBarcodeImage = isImage = true
            }
            console.log('SelectedObj:', SelectedObj, "SelectedObj.attr('data'):", SelectedObj.attr('data'))
            console.log(`isImage: ${isImage}, isItem: ${isItem}, isBarcodeImage: ${isBarcodeImage}`)
			
			if(isImage)
				display = 'flex'

			var align =SelectedObj.css('text-align'); 
			align = align == "left" ? "居左" : align =="right" ? "居右" : align =="center" ? "居中" : ""; 
			$("#align input").val(align)
			$('#align').parent().show();


			$("#element_width").parent().css('display', isItem ? 'flex' : 'none');
			$("#element_height").parent().css('display', display);
			$("#image_browse").parent().css('display', display);

            $("#barcode_pic_text_hide").parent().css('display', isBarcodeImage ? 'flex' : 'none');
            $("#barcode_pic_text_size").parent().css('display', isBarcodeImage ? 'flex' : 'none');

			$("#dot_len").parent().css('display', isItem ? 'flex' : 'none');
			$("#char_spacing").parent().css('display', isItem ? 'flex' : 'none');		
			
			$("#show_value").parent().css('display', 'flex');		
			
			display = 'flex'
			if (isImage) display = 'none'
			$("#show_title").parent().css('display', display);
			$("#title").parent().css('display', display);
			$("#font").parent().css('display', display);
			$("#font_bold").parent().css('display', display);
			$("#font_size").parent().css('display', display);
			$("#font_style").parent().css('display', display);
			$("#font_underline").parent().css('display',  isItem ? 'flex' : 'none');
			
			$("#printBackImage").parent().hide()
			$("#back_image_browse").parent().hide()
			$("#divPaddingTop").parent().hide()
			$("#divPaddingBottom").parent().hide()
			$("#divPaddingLeft").parent().hide()
			$("#divPaddingRight").parent().hide()
            
			$("#divLandscape").hide()
            $('#divInvert').hide()

			$("#show_title").parent().hide()
			$("#title").parent().hide()

			//$("#font_underline").parent().hide()
			 var exceedAction = $(element).data('exceedaction')
			 exceedAction = exceedAction=="cut" ?'截断':exceedAction=="zoom" ?'缩小':'换行'
			 $("#exceedAction input").val(exceedAction)
			 $("#exceedAction").parent().show()

			 var showStyle = $(element).data('show_style')
			 showStyle = showStyle=="qrcode" ?'二维码':showStyle=="barcode" ?'一维码':'普通'
			 $("#show_style input").val(showStyle)
			 $("#show_style").parent().show()


            var barcode_pic_text_hide = $(element).data('barcode_pic_text_hide')
            barcode_pic_text_hide = (barcode_pic_text_hide == "true" || barcode_pic_text_hide == true) ? '不显示' : '显示'
            $("#barcode_pic_text_hide input").val(barcode_pic_text_hide)
			
			var id = element.id
			if (id == "print_sheet") {
				$('#lblSelectedElemenet').text('单据')
				var printBackImage = $(element).data('printBackImage')
				printBackImage = (printBackImage == "true" || printBackImage == true) ? "打印" : "不打印"
				$("#printBackImage input").val(printBackImage)
				$("#printBackImage").parent().show()
				$("#back_image_browse").parent().show()
				$("#divPaddingTop").parent().show()
				$("#divPaddingBottom").parent().show()
				$("#divPaddingLeft").parent().show()
				$("#divPaddingRight").parent().show()
				$("#divLandscape").show()
                console.log("SheetType:", SheetType)
				if (SheetType === 'BQ') {
                    $('#divInvert').show()
                }
			}
			else if (id == "print_pageHead") {
				$('#lblSelectedElemenet').text('页眉')
			}
			else if (id == "print_tableHead") {
				$('#lblSelectedElemenet').text('表头')
			}
			else if (id == "print_table") {
				$('#lblSelectedElemenet').text('表格')
			    var showEmptyRows = $(element).data('showEmptyRows')
			    showEmptyRows = (showEmptyRows=="true" ||showEmptyRows==true) ? "填充":"不填充"
				$("#showEmptyRows input").val(showEmptyRows)
				$("#showEmptyRows").parent().show()

				var showLines = $(element).data('showLines')
			    showLines = (showLines=="false" || showLines==false)? "不显示":"显示"
				$("#showLines input").val(showLines)
				$("#showLines").parent().show()
				 
				var sortColumns = $(element).data('sortColumns')
				var value=''
				if (sortColumns) {

					var arr = sortColumns.split(',')
					
					AvailElements.table.forEach(col => {
					 
						//if (colChecked) 
					   $(`#ck_sortColumns_${col.name}`).removeAttr("checked")
					})

					arr.forEach(opt => {
						var col=AvailElements.table.find(col => col.name == opt)
						if (col) {
							$(`#ck_sortColumns_${col.name}`).attr("checked", "checked")
							if (value) value += ','
							value+=col.title
                        }
                    })
                }
			    $("#sortColumns input").val(value)
				$("#sortColumns").parent().show()
				
				var sortDirection = $(element).data('sortDirection')
				var value=''
				// if (sortDirection) {
				// 	// AvailElements.table.forEach(col => {
				// 	// 	$(`#ck_sortDirection_${col.name}`).removeAttr("checked")
				// 	// })

				// 	// arr.forEach(opt => {
				// 		// var col=AvailElements.table.find(col => col.name == opt)
				// 		// $(`#ck_sortDirection_${col.name}`).attr("checked", "checked")
				// 		value+="降序"
				// 		value += ','
				// 		value += "升序"
    //                 // })
    //             }
				value = sortDirection.toString() == "desc" ? "降序" : "升序";
				$("#sortDirection input").val(value)
				$("#sortDirection").parent().show()
				 
				var groupSumByColumn = $(element).data('groupSumByColumn')
				var value=''
				if (groupSumByColumn) {  
					AvailElements.table.some(col => {
						if (col.name == groupSumByColumn) {
							value = col.title
							return true
                        }
                    })
				}

			    $("#groupSumByColumn input").val(value)
				$("#groupSumByColumn").parent().show()

				 
                var rowHeight = $(element).data('rowHeight')
				$("#rowHeight input").val(rowHeight)
				$("#rowHeight").parent().show()


				var showValue = $(element).data('showValue')
				$("#showValue input").val(showValue)
				$("#showValue").parent().show()

			    var sumColumnValue = $(element).data('sumColumnValue')
				$("#sumColumnValue input").val(sumColumnValue)
				$("#sumColumnValue").parent().show()
				 

				var lineWidth = $(element).data('lineWidth')||'1'
				$("#lineWidth input").val(lineWidth)
				$("#lineWidth").parent().show()

				

				$("#show_title").parent().show()
			 		
			}
			else if (id == "print_tableTail") {
				$('#lblSelectedElemenet').text('表尾')
				
			}
			else if (id == "print_pageTail") {
				$('#lblSelectedElemenet').text('页脚')
			}
			else {
				var data = element.getAttribute('data')
				if (data) data = JSON.parse(data)
				var name = data.name
				for (var k in AvailElements) {
					var ele = AvailElements[k]
					if (ele && ele.length > 0) {
						for (var n = 0; n < ele.length;n++) {
							var ee = ele[n]
							if (ee.name == name) {
								$('#lblSelectedElemenet').text(ee.title)
                            }
                        }
                    }
				}

				$("#show_title").parent().show()
				$("#title").parent().show()
				 

			}
			if (id != "print_table") {
				$("#showEmptyRows").parent().hide()
				$("#showLines").parent().hide()
				$("#sortColumns").parent().hide()
				$("#sortDirection").parent().hide()
				$("#groupSumByColumn").parent().hide()
				$('#hide_empty').parent().show()
				$('#chinese_num').parent().show() 
			}
			else {
				$('#hide_empty').parent().hide()
				$('#chinese_num').parent().hide() 
            }
			 
			onKeyDowns();
			drag_dli();				
				
	    }
		function bindDataItemsClickEvent() {
			//数据项被点击后触发，更新属性框内容
			$(".print-item").on("click", function (e) {
				onElementClick(this, 'print-item', e);
				$('input[id="prop_title"]').val($(this).find('p').text())			
				$(this).css('border', "1px dashed #ee0a24") 
				$(this).css('margin-top', "-1px") 
				 $(this).css('margin-left', "-1px") 
				 
				$('.remove-btn').css('display', "none");
				$(this).find('.remove-btn').css('display', "block");
				
			})
			 
	}


    function onUploadPic(picFld, imgSlt,uploadImgUrl) {
        var renderForm = function () {
            var fromHtml = `<form enctype="multipart/form-data"  method="post">
                  <input id="fileUploadPic" type="file" accept="image/*">
                </form>`;
            var $container = $('<div style="display:none;"></div>');
            // var $container = $('<div></div>');
            $container.html(fromHtml);
            // 渲染到页面中
            $(document.body).append($container);
        };
        var $inputFile = $('#fileUploadPic');
        if ($inputFile.length === 0)
            renderForm();
        $inputFile = $('#fileUploadPic');
        function convertBase64UrlToBlob(urlData, filetype) {
            //去掉url的头，并转换为byte
            var bytes = window.atob(urlData.split(',')[1]);

            //处理异常,将ascii码小于0的转换为大于0
            var ab = new ArrayBuffer(bytes.length);
            var ia = new Uint8Array(ab);
            for (var i = 0; i < bytes.length; i++) {
                ia[i] = bytes.charCodeAt(i);
            }
            if (filetype === '' || !filetype) {
                filetype = 'image/png';
            }
            return new Blob([ab], { type: filetype });
		}

		function blobToDataURI(blob, callback) {
		   var reader = new FileReader();
		   reader.readAsDataURL(blob);
		   reader.onload = function (e) {
			   callback(e.target.result);
		   }
		}

        $inputFile.off('change');
        $inputFile.on('change', function (e) {
            // 获取配置项内容 
            
            //var testHostname = config.testHostname || 'localhost';
            var loadingImgUrl = ''; //config.loadingImg;
            var timeout = 10000;

            //   idDebugger = testHostname === window.location.hostname;

            var files = $inputFile[0].files || [];

            if (files.length === 0) {
                return;
            }

            var $focusElem = self.$focusElem;
            var loaded = 0;
            for (var i = 0; i < files.length; i++) {
                var file = files[i];
                var fileType = file.type || '';
                var reader = new FileReader();
              //  bw.toast('所选图片不能大于AAA0M');
                if (file.size > 10 * 1024 * 1024) {
                    bw.toast('所选图片不能大于10M');
                    $inputFile.val('');
                    return;
                }
                //log('选中的文件为：' + file.name);
                //log('服务器端上传地址为：' + uploadImgUrl);
                bw.toast("正在上传...", 0, 'center', true, 'load.gif');
                var loadFile = function (reader, file, fileType) {
                    reader.onload = function (e) {
                        loaded++;
                        var base64 = e.target.result || this.result,
                            prevImgSrc,
                            prveImgContainerId = 'div' + Math.random().toString().slice(2),
                            prevImgInfoId = 'info' + Math.random().toString().slice(2),
                            xhr,
                            formData,
                            timeoutId;

                        if (window.URL && window.URL.createObjectURL) {
                            // 如果浏览器支持预览本地图片功能，则预览本地图片
                            prevImgSrc = window.URL.createObjectURL(file);
                            var img = $(imgSlt);// document.createElement('img');
							// img[0].src = prevImgSrc;
							if (img[0].tagName.toLowerCase() == "img") {
								img[0].fileName = file.name;
								img[0].blob = convertBase64UrlToBlob(base64, fileType);
								img[0].src = base64
							}
							else {
								img[0].style['background-image'] =`url(${base64})`
                            }
						
                            // img.fileType = fileType; 
							 
                            window.resizeImage(base64, 800, 800, 0.6,5, function (newBlob) {
								img[0].blob = newBlob;
								blobToDataURI(newBlob, function (base64) {
									//img[0].base64 = base64;
									if (img[0].tagName.toLowerCase() == "img") {
										img[0].src = base64
									}
									else {
									    img[0].style['background-image'] = `url(${base64})`
                                    }
									if (uploadImgUrl) {
										var xhr = new XMLHttpRequest();
										var formData = new FormData();

										function timeoutCallback() {
											//log('访问超时（配置的超时事件是：' + timeout + '）');
											if (xhr.abort) {
												xhr.abort();
											}
										}
										//log('准备上传文件...');
										xhr.open('POST', uploadImgUrl, true);
										xhr.setRequestHeader("XSRF-TOKEN",
											$('input:hidden[name="__RequestVerificationToken"]').val());
										// 计时开始
										timeoutId = setTimeout(timeoutCallback, 60000);
										xhr.onload = function () {
											// 得到消息之后，清除计时
											//uploaded++;
											clearTimeout(timeoutId);
											var resultSrc = xhr.responseText; //服务器端要返回图片url地址 
											var loadImg;
											var $loadImg;
											console.log('服务器端的返回数据为：' + xhr.responseText);
											var res = JSON.parse(xhr.responseText);
											if (res.result === 'OK') {
												me[picFld] = res.imageUrl;
												var tm = new Date().getTime();
												me['img_' + picFld] = res.imageUrl + '?v=' + tm;
												app.input.validate(Dom7('.page-current #img_' + picFld));
												bw.toast('上传成功!', 1000);
											} else {

												//self.saveSourceCode();
											}
											self.uploaded = true;
										};
										//myEditorMobileFile 要和后台一致
										formData.append('userID', me.m_userID);
										formData.append('companyID', me.m_companyID);
										formData.append('picFld', picFld);
										formData.append('file', img.blob, img.fileName);
										xhr.send(formData);
									}
									else {
										bw.toast('上传成功!', 1000);
                                    }
                                }) 
                            });

                        } else {
                            // 如果浏览器不支持预览本地图片，则复制为一个配置的图片地址
                            prevImgSrc = loadingImgUrl;
                            // 生成预览图片
                            $focusElem.after(
                                '<div class="previmg-container" id="' + prveImgContainerId + '">' +
                                '	<img src="' + prevImgSrc + '" style="max-width:100%;"/>' +
                                '</div>'
                            );
                        }

                        if (loaded === files.length) {
                            $inputFile.val('');
                        }
                    };
                    reader.readAsDataURL(file);
                };
                loadFile(reader, file, fileType);
            }
        });
        $inputFile.trigger('click');

    }

		function bindAreaClickEventToShowProperties() {
			//数据项被点击后触发，更新属性框内容
			$(".print-area").on("click", function (e) {
				onElementClick(this, 'print-item', e);				
				$(this).css('border', "1px dashed #ee0a24") 
			    $('.remove-btn').css('display', "none");
				 
			})
		}
		function bindSheetClickEventToShowProperties() {
			//数据项被点击后触发，更新属性框内容
			$("#print_sheet").on("click", function (e) {
				console.log("#print_sheet_ click 1457");
				onElementClick(this, 'print-sheet', e)				
				$(this).css('border', "1px dashed #ee0a24") 
			    $('.remove-btn').css('display', "none")				 
			})
		}
		
		$('input[id="prop_title"]').on("input", function() {
			if (SelectedObj[0].classList[0] === "print-item") {
				SelectedObj.find('p').html($(this).val())
			}
			else if (SelectedObj[0].classList[0] === "tr-column") {
				SelectedObj.find('p').html($(this).val())
			}
		})

		$('#paddingTop').on("input", function () {
		    var d = $(this).val();
		    d= mm2px(d) 
		    $('#print_sheet').css('padding-top', d + 'px')  
		})
		$('#paddingBottom').on("input", function () {
				var d = $(this).val();
				d= mm2px(d)
				$('#print_sheet').css('padding-bottom', d + 'px')  
		 })
		$('#paddingLeft').on("input", function () {
				var d = $(this).val();
				d= mm2px(d)
				$('#print_sheet').css('padding-left', d + 'px')  
		})
		$('#paddingRight').on("input", function () {
			var d = $(this).val();
			d= mm2px(d)
			$('#print_sheet').css('padding-right', d + 'px')  
		 })

		$('#element_width input').on("input", function () {		 

			 if (SelectedObj[0].classList[0] === "print-item") {
				 var d = $(this).val()
				 $(SelectedObj).data('element_width',d)	
				 d = mm2px(d)
				 SelectedObj.find('.print-image').width(d)
				 SelectedObj.width(d)
			}
		})

	 $('#element_height input').on("input", function() {
		if (SelectedObj[0].classList[0] === "print-item") {
			var d = $(this).val()
			$(SelectedObj).data('element_height',d)
			let barcodeMode = false
			const _data = SelectedObj[0].getAttribute('data')
			if (_data) {
				try {
					const _o = JSON.parse(_data)
					if (_o.name && _o.name.match('barcode_picture'))
						barcodeMode = true
				} catch (e) {
					console.warn(e)
				}
			} // todo: 虽然能成功地区分条码图片与普通图片，但条码图片的显示高度不知为何不会立即更新
			d = barcodeMode ? (Number(d) / 1.4).toFixed(4) : mm2px(d)
			SelectedObj.find('.print-image').height(d)
		}
	 })

        $('#barcode_pic_text_hide input').on("input", function () {
            var d = $(this).val()
            console.log('#barcode_pic_text_hide input, d:', d)
            $(SelectedObj).data('barcode_pic_text_hide', d)
        })
        $('#barcode_pic_text_size input').on("input", function () {
            var d = $(this).val()
            $(SelectedObj).data('barcode_pic_text_size', d)
        })

	$('#dot_len input').on("input", function () {		 

			//if (SelectedObj[0].classList[0] === "print-item") {
				var d = $(this).val()
				$(SelectedObj).data('dot_len',d)
		//}
	})

	$('#char_spacing input').on("input", function () {
		//if (SelectedObj[0].classList[0] === "print-item") {
			var d = $(this).val()
			$(SelectedObj).data('char_spacing',d)
		//}
	 })

		$('#show_value input').on("input", function () {
		//if (SelectedObj[0].classList[0] === "print-item") {
			var d = $(this).val()
			$(SelectedObj).data('show_value', d)
		//}
	 })
		$('#column_sum_value input').on("input", function () {
			//if (SelectedObj[0].classList[0] === "print-item") {
			var d = $(this).val()
			$(SelectedObj).data('column_sum_value', d)
			//}
		})

		$('#image_browse button').on("click", function() {
		if (SelectedObj[0].classList[0] === "print-item") {
			var img = SelectedObj.find('.print-image')
			if (img.length > 0) {
				onUploadPic("",img[0],"")
            }
		}
	 })


	 $('#btnSelectBackImage').on("click", function () {
		   SelectedObj.css('background-repeat','no-repeat') 
		   onUploadPic("",SelectedObj[0],"")  
	 })

	 $('#btnDelBackImage').on("click", function () { 
		 SelectedObj[0].style['background-image']='url()' 
	 })

	$('#rowHeight input').on("input", function () {
		var v = $(this).val() 
		$("#print_table").data('rowHeight',v) 
		if (v) {
			 var pxHeight = mm2px(v, "h")
			 $('#print_table th').css('height', pxHeight + 'px')
			 $('#print_table td').css('height', pxHeight + 'px')
		 }
	 })

	 $('#lineWidth input').on("input", function () {
		var v = $(this).val()
		$("#print_table").data('lineWidth', v)
	 })
		


    function disableDragToMoveTableColumns()
	{
		//$(".toolbox_li").off("dragstart");
		$(".tr-column").attr('draggable',false)
			
		$(".tr-column").off("dragstart");
		$(".tr-column").off("dragover");
		$(".tr-column").off("drop");
	}
	function enableDragToMoveTableColumns() {
        //console.log("enableDragToMoveTableColumns")
		    $(".tr-column").attr('draggable',true)
			let bAddingNewColumn = 0;
			let htmlTables_sons = '';
			let htmlTables_fath = '';
			 $(".toolbox_li").off("dragstart")
			$(".toolbox_li").on("dragstart", function(e) {
				htmlTables_fath = $(this);
				bAddingNewColumn = 1
			})
			$(".tr-column").off("dragstart")
			$(".tr-column").on("dragstart", function (e) {
				e.stopPropagation();
				//if(tTD.style.cursor = 'default';)
				$("#print_sheet").off("drop"); 
				$(".tr-column").off("drop");
				$(".tr-column").on("drop",droppedOnColumn);
				console.log("表格拖动中",this.style.cursor)
				//console.log($(this))
				htmlTables_sons = $(this);
				bAddingNewColumn = 0
			})
			$(".tr-column").off("dragover")
			$(".tr-column").on("dragover", function(e) {
				//console.log('我一直会动')
				e.preventDefault();
			})
			$(".tr-column").off("drop");
			$(".tr-column").on("drop",droppedOnColumn);
		    function droppedOnColumn(e)
		   {  
				//console.log("1506 dorp,当前选择对象",4, bAddingNewColumn)
				e.preventDefault();
				e.stopPropagation();
				if (bAddingNewColumn === 0) {
					//console.log($(this)[0].innerHTML)
					//console.log(htmlTables_sons)
					var toCol = $(this)[0] 
					let storHtml = toCol.innerHTML
					var toColIndex = toCol.rowIndex
					var fromCol = htmlTables_sons[0]
					var fromColIndex = fromCol.rowIndex
					var fromColInnerHTML = fromCol.innerHTML
					var fromColWidth = fromCol.style.width
					var fromColData = fromCol.getAttribute('data')
					var tbody = this.parentNode;
					if (toColIndex == fromColIndex) return;
					if (toColIndex > fromColIndex) {
						for (var i = fromColIndex; i <= toColIndex - 1;i++) {
							var col = tbody.childNodes[i]
							col.innerHTML = tbody.childNodes[i + 1].innerHTML
							col.setAttribute('data', tbody.childNodes[i + 1].getAttribute('data')) 
							col.style.width = tbody.childNodes[i + 1].style.width
						}
					}
					else if (toColIndex < fromColIndex) {
						for (var i = fromColIndex; i >= toColIndex + 1;i--) {
							var col = tbody.childNodes[i]
							col.innerHTML = tbody.childNodes[i - 1].innerHTML
							col.setAttribute('data', tbody.childNodes[i - 1].getAttribute('data')) 
							col.style.width = tbody.childNodes[i - 1].style.width
						}
					}
					toCol.innerHTML = fromColInnerHTML
					toCol.setAttribute('data', fromColData) 
					toCol.style.width = fromColWidth
					//htmlTables_sons[0].innerHTML = storHtml;
				} else {//adding a new column
					//console.log("插入一个表格");
					let navListData = JSON.parse(htmlTables_fath[0].attributes.data.nodeValue)
					let htmls_s = '';
					let objes = {
						name: navListData.name
					}
//获取原始高度
					var table = document.getElementById("table_table"); 
					//console.log(table.rows[0]);
					//console.log(table.rows[0].cells[0].style.cssText);
                    //    w += parseInt(table.rows[0].cols[0].style.width);
		
					let htmlsTrLength = $(this).parent()[0].children.length
					var style = table.rows[0].style.cssText
					var widthStyle = table.rows[0].style.width
					style = style.replace(widthStyle,'90px')
					htmls_s += '<tr class="tr-column" id="' + htmlsTrLength +
						'" style="'+ style +'" draggable="true" data =' + JSON.stringify(objes) + '><th class="thClass" style="'+table.rows[0].cells[0].style.cssText+'"><image class="remove-btn"/><p>' + navListData.title + '</p></th>';
					for (let i = 0; i < 2; i++) {
						htmls_s += '<td class="tdClass" style="'+table.rows[0].cells[i+1].style.cssText+'">a</td>'
					}
					htmls_s += '</tr>'
				//	$(this).parent().append(htmls_s);
					$(this).parent()[0].insertBefore($(htmls_s)[0], this)

				     resetTableWidthByColumns() 
                    
					 
					//enableDragToMoveTableColumns();
					EnableDragToResizeTable()
					bindColumnClickToShowProperties() 
					bindRemoveBtnEvent()
					//$(".tr-column").off("drop");
					//StopSheetToAcceptAreaDrop()
				}
		
	}

	}
	function resetTableWidthByColumns() {
		var table = document.getElementById("table_table")
		var w = 0;
        for (var i = 0; i < table.rows.length; i++) {
			var w0 = parseInt(table.rows[i].style.width) 
			w +=w0;
		} 
		//if (addExtra) w += 10;
		//w=table.offsetWidth
		w += mm2px(5);
		document.getElementById("print_table").style.width = w + "px"; 
		document.getElementById("print_table").style['border-right-width'] = "0px";
		document.getElementById("table_table").style['border-right-width'] = "0px";

		$('#table_table tbody').css('border-right-width', '0.5px')
		$('#table_table tbody').css('border-right-style', 'solid') 

    }
		//绑定列事件
		function bindColumnClickToShowProperties() {
			//列  tdClass
			$(".tr-column").on("click",function(e){
				$(".tr-column").removeClass('trBorders')
				$(this).addClass('trBorders')
				$('.tdClass').removeClass('trBorders')
				if (e.target.className.indexOf('tdClass') >= 0) {
					SelectedObj =$(this).find('.tdClass')
					SelectedObj.addClass('trBorders')
				}
				else {
					SelectedObj = $(this)
					$(this).find('.tdClass').removeClass('trBorders')
                }

				let familys = '';
				let fontWeight = '';
				let fontSize = '';
				let fontStyle = '';
				e.preventDefault();
				e.stopPropagation();
				var fontFamily = SelectedObj.css('font-family')
			    fontFamily = fontFamily.replace('"', '').replace('"', '')
				/*if (fontFamily) { 
					PropertyOptions.font.map(item => {
						if (item.ids === $(this)[0].style.fontFamily) {
							familys = item.title
						}
					}) 
				}*/
				fontWeight = SelectedObj[0].style['font-weight']
				var nFontWeight=parseInt(fontWeight)
				if (nFontWeight >= 700) fontWeight = 'bold'
				else if (nFontWeight <= 400 && nFontWeight > 0) fontWeight = 'normal'
				 
				if (fontWeight) {
					PropertyOptions.font_bold.map(item => {
						if (item.name === fontWeight) {
							fontWeight = item.title
						}
					})
				}
				
				fontSize=SelectedObj[0].style['font-size']
			    if (fontSize) fontSize = px2mm(fontSize, 'h')

				fontStyle = SelectedObj[0].style['font-style'] 
			    if (fontStyle) {
					PropertyOptions.font_style.map(item => {
						if (item.name === fontStyle) {
							fontStyle = item.title
						}
					})
				} 	 
				 
				$('input[id="prop_title"]').val($(this).find('th').text())			

				//$(this).css('border', "1px dashed #ee0a24")
			
				 
				 
				$("#font input").val(fontFamily)
				$("#font_bold input").val(fontWeight)
				$("#font_size input").val(fontSize)
				$("#font_style input").val(fontStyle)
				 
				var showsum = $(this).data('showsum');
				if (!showsum) showsum = false;
				showsum =showsum.toString() == "true" ? "显示" : "不显示"; 
				$("#show_sum input").val(showsum)
				$("#show_sum").parent().show()

				var boldsum = $(this).data('boldsum');
			    if (!boldsum) boldsum = false;
				boldsum = boldsum.toString() == "true" ? "加粗" : "不加粗";
				$("#bold_sum input").val(boldsum)
				$("#bold_sum").parent().show()
				
				var sortDirection = $(this).data('sortDirection');
				if (!sortDirection) sortDirection = "asc";
				sortDirection = sortDirection.toString() == "desc" ? "降序" : "升序";
				$("#sortDirection input").val(sortDirection)
				$("#sortDirection").parent().show()
				var showPageSum = $(this).data('showpagesum'); 
				 if (!showPageSum) showPageSum = false;
			    showPageSum =showPageSum.toString()=="true"? "显示":"不显示"; 
				$("#show_page_sum input").val(showPageSum)
				$("#show_page_sum").parent().show()
				$("#title").parent().show()
				var showGroupSum = $(this).data('showGroupSum'.toLowerCase());
				 if (!showGroupSum) showGroupSum = false;
				showGroupSum = showGroupSum.toString()=="true"? "显示":"不显示"; 
				$("#showGroupSum input").val(showGroupSum)
				$("#showGroupSum").parent().show()

				var exceedAction = $(this).data('exceedaction')
			    exceedAction = exceedAction=="cut" ?'截断':exceedAction=="zoom" ?'缩小':'换行'
			    $("#exceedAction input").val(exceedAction)
				$("#exceedAction").parent().show()

				var showStyle = $(this).data('show_style')
				showStyle = showStyle=="qrcode" ?'二维码':showStyle=="barcode" ?'一维码':'普通'
				$("#show_style input").val(showStyle)
				$("#show_style").parent().show()
				 

				$("#rowHeight").parent().hide()
				$("#lineWidth").parent().hide()

				$("#showValue").parent().hide()
				$("#columnSumValue").parent().hide()

				$("#showEmptyRows").parent().hide()
				$("#show_title").parent().hide()
				$("#showLines").parent().hide()
                
		        $("#sortColumns").parent().hide()
				$("#sortDirection").parent().hide()
		        $("#groupSumByColumn").parent().hide()

				$("#divPaddingTop").parent().hide()
				$("#divPaddingBottom").parent().hide()
				$("#divPaddingLeft").parent().hide()
				$("#divPaddingRight").parent().hide() 
				$("#divLandscape").hide()

				$("#element_width").parent().hide()
				$("#font_underline").parent().hide()

				$("#dot_len").parent().show()
				$("#char_spacing").parent().show()
				
				$("#show_value").parent().show()
				$("#column_sum_value").parent().show()

				var dot_len = $(this).data('dot_len')
				$("#dot_len input").val(dot_len)

				//var char_spacing = $(this).data('char_spacing')
				var char_spacing = SelectedObj.data('char_spacing')				
				$("#char_spacing input").val(char_spacing)

			    var show_value = SelectedObj.data('show_value')				
				$("#show_value input").val(show_value)

				var column_sum_value = SelectedObj.data('column_sum_value')				
				$("#column_sum_value input").val(column_sum_value)


				var align =SelectedObj.css('text-align'); 
				align = align == "left" ? "居左" : align =="right" ? "居右" : align =="center" ? "居中" : ""; 
			    $("#align input").val(align)
				$('#align').parent().show();

				

				var data = this.getAttribute('data');
				if (data) data = JSON.parse(data);
				var name = data.name;
				for (var k in AvailElements) {
					var ele = AvailElements[k];
					if (ele && ele.length > 0) {
						for (var n = 0; n < ele.length;n++) {
							var ee = ele[n];
							if (ee.name == name) {
								$('#lblSelectedElemenet').text(ee.title);
                            }
                        }
                    }
				}
				$('.remove-btn').css('display', "none");
				$(this).find('.remove-btn').css('display', "block");
				onKeyDowns();
				drag_dli();
			})
		}
		function loadTable(objs) {
			let htmls = '';
			htmls += '<table id="table_table" style="height:auto;min-height:50px;">';
			if (objs.elements && objs.elements.length > 0) {
				// console.log(objs.elements)
				objs.elements.map((item, index) => {
			 
					let objes = {
						name: item.name,
					}
					let fontName = '';
					PropertyOptions.font.map(item_F => {
						if (item.fontName === item_F.title) {
							fontName = item_F.name
						}
					})
					
					let styles = 'width:' + mm2px(item.width, "w") +'px;'
					if (item.fontSize) styles += 'font-size:' + mm2px(item.fontSize, "h")+'px;';
					if (item.fontBold) { 
						styles += 'font-weight:' + item.fontBold + ';';
					}
					if (item.fontStyle) styles+=`font-style:${item.fontStyle};`
					if (fontName) styles += 'font-family:' + fontName + ';';
					var alignStyle=''
					if (item.align) { 
						alignStyle= 'text-align:' + item.align + ';'
						styles +=alignStyle
					}
				
					var dataShowSum=""
					if (!item.showSum) item.showSum = "false";  
					dataShowSum = " data-showsum=" + item.showSum.toString()


					var dataBoldSum = ""
					if (!item.boldSum) item.boldSum = "false";
					dataBoldSum = " data-boldsum=" + item.boldSum.toString()

					var dataShowTitle=""
					if (!item.showTitle) item.showTitle = "false"

					dataShowTitle = " data-showTitle=" + item.showTitle.toString()
					
					var dataShowPageSum=""
					if (!item.showPageSum) item.showPageSum = false
					dataShowPageSum = " data-showPageSum=" + item.showPageSum.toString()

					var dataShowGroupSum=""
					if (!item.showGroupSum) item.showGroupSum = false
					dataShowGroupSum = " data-showgroupsum=" + item.showGroupSum.toString()



					var cellStyle = ''
					if (item.cellFontName) cellStyle+=`font-family:${item.cellFontName};`
					if (item.cellFontSize) {
						var d = mm2px(item.cellFontSize)
						cellStyle += `font-size:${d}px;`
					}
					if (item.cellAlign) cellStyle += `text-align:${item.cellAlign};`
				    if (item.cellFontBold) cellStyle+=`font-weight:${item.cellFontBold};`
					if (item.cellFontStyle) cellStyle += `font-style:${item.cellFontStyle};`

				    var dataDotLen=""
					if (item.dotLen) 
					   dataDotLen = " data-dot_len=" + item.dotLen.toString()

					var dataCharSpacing = ""
					if (item.charSpacing)
					  dataCharSpacing = " data-char_spacing=" + item.charSpacing.toString()
				   var dataCellCharSpacing = ""
					if (item.cellCharSpacing)
					  dataCellCharSpacing = " data-char_spacing=" + item.cellCharSpacing.toString()
					var dataExceedAction=''
					if (item.exceedAction !== undefined && item.exceedAction !== '') dataExceedAction = ` data-exceedaction="${item.exceedAction}" `

					var dataShowStyle=''
					if (item.showStyle !== undefined && item.showStyle !== '') dataShowStyle = ` data-show_style="${item.showStyle}" `

					if (item.name == 'item_name') {
						debugger
                    }

					var data_show_value='',data_column_sum_value=''
					if (item.showValue) {
						data_show_value = ` data-show_value="${item.showValue}" `
					}
					if (item.columnSumValue) {
						data_column_sum_value = ` data-column_sum_value="${item.columnSumValue}" `
					}
				    /*if (item.name === "sub_amount") {
						if (item.showSum) {
							htmls += '<tr class="tr-column" id="' + index +
								'" style="flex1:1;' + styles + '" draggable="false" data =' + JSON.stringify(objes) + ' ><th class="thClass" style="flex1:2">' +
								item.title + '</th>';
							for (let i = 0; i < 3; i++) {
								htmls += '<td class="tdClass" style="flex1:2">参考值</td>'
							}
							htmls += '</tr>'
						} else {
							htmls += '<tr class="tr-column" id="' + index +
								'" style="flex1:1;display:none;' + styles + '" draggable="false"  data =' + JSON.stringify(objes) + '><th class="thClass" style="flex1:2">' + item
								.title +
								'</th>';
							for (let i = 0; i < 3; i++) {
								htmls += '<td class="tdClass" style="flex1:2">参考值</td>'
							}
							htmls += '</tr>'
						}
					}
					else*/
					{
							htmls += `<tr class="tr-column" id="${index}" style="flex1:1; ${styles} ${alignStyle} draggable="false" data =${JSON.stringify(objes)} ${dataShowTitle} ${dataShowSum}  ${dataBoldSum} ${dataShowPageSum} ${dataShowGroupSum} ${dataDotLen} ${dataCharSpacing} ${dataExceedAction} ${dataShowStyle} ${data_show_value} ${data_column_sum_value}><th class="thClass"><image class="remove-btn"/><p>${item.title}</p></th>`;
						var values = null
						for (var i in AvailElements.table) {
							var col = AvailElements.table[i]
							if (col.name == item.name) {
								values=col.values
                            }
                        }
						//debugger
				 
						if (values) {
							for (let i = 0; i < values.length; i++) {
								var v = values[i]
								if(v.value) v=v.value
								htmls += `<td class="tdClass" style="${cellStyle}" ${dataCellCharSpacing}>${v}</td>`
							}
                        } 
						htmls += '</tr>'
					}
				})
			}

			htmls += '</table>'
			$("#print_table").append(htmls)
			enableDragToMoveTableColumns()
			bindColumnClickToShowProperties()
			if (objs.showEmptyRows === undefined ||objs.showEmptyRows==='') objs.showEmptyRows = false
			 $("#print_table").data('showEmptyRows',objs.showEmptyRows)
			if (objs.showLines === undefined ||objs.showLines==='') objs.showLines = true
			$("#print_table").data('showLines', objs.showLines)
			$("#print_table").data('sortColumns', objs.sortColumns || '')
			$("#print_table").data('sortDirection', objs.sortDirection || '')
			$("#print_table").data('groupSumByColumn', objs.groupSumByColumn || '')
			 
			$("#print_table").data('showTitle'.toLowerCase(), objs.showTitle) 

		    if (objs.rowHeight === undefined ||objs.rowHeight==='') objs.rowHeight = ''
			$("#print_table").data('rowHeight', objs.rowHeight) 
			if (objs.rowHeight) {
				var pxHeight = mm2px(objs.rowHeight, "h")
				$('#print_table th').css('height', pxHeight + 'px')
			    $('#print_table td').css('height', pxHeight + 'px')
			}



			if (objs.lineWidth === undefined || objs.lineWidth === '') objs.lineWidth = ''
			$("#print_table").data('lineWidth', objs.lineWidth)
			  

			if (objs.fontSize) {
				 var pxSize = mm2px(objs.fontSize, "h")
				$("#print_table").css('font-size', pxSize + 'px')
			    $("#table_table").css('font-size',  pxSize + 'px')
            }
			
			if (objs.fontName) {
				$("#print_table").css('font-family', objs.fontName)
				$("#table_table").css('font-family',objs.fontName)
			}
			setTimeout(() => {
				 resetTableWidthByColumns()
            },300)
           
			// bindColumnClickToShowProperties();
		}

		function onKeyDowns() {
			document.onkeydown = function(event) {
				let e = event || window.event || arguments.callee.caller.arguments[0];	   
				let letsUns = '';
				let topUns = '';
				let fat_hei = Number(SelectedObj.parent().height())
				let son_hei = Number(SelectedObj.height())
				let fat_wei = Number(SelectedObj.parent().width())
				let son_wei = Number(SelectedObj.width())
				console.log('keydown')
				var cls = SelectedObj.attr('class');
				if (cls.indexOf('print-sheet') >= 0) return;
				if (cls.indexOf('print-area') >= 0) {
					if (e && e.keyCode == 38) {
						return;
					}
                }
				if (SelectedObj[0].style.top) {
					let tops = (SelectedObj[0].style.top).split("px");
					topUns = Number(tops[0])
				} else {
					topUns = ''
				}
				if (SelectedObj[0].style.left) {
					let lets = (SelectedObj[0].style.left).split("px");
					letsUns = Number(lets[0])
				} else {
					letsUns = ''
				}
			     //console.log(SelectedObj[0],SelectedObj[0].attributes.data.,SelectedObj[0].attributes.data.nodeValue);
				var bl = 0;
			    try
				{
					JSON.parse(SelectedObj[0].attributes.data.nodeValue);
					bl = 1;
				}
				catch (e)
				{
					bl = 0;
				}
			if(bl)
			{
				if (e && e.keyCode == 38) {
					let topNumbers = 0
					if (topUns === '' || topUns <= 0) {
						topNumbers = 0;
					} else {
						topNumbers = topUns - 1;
					}
					SelectedObj.css('top', topNumbers + 'px')
				}
				if (e && e.keyCode == 40) {
					let topNumbers = 0
					if (topUns === '' || topUns >= (fat_hei - son_hei)) {
						topNumbers = (fat_hei - son_hei);
					} else {
						topNumbers = topUns + 1;
					}
					SelectedObj.css('top', topNumbers + 'px')
				}
			}
				if (e && e.keyCode == 37) {
					//console.log(123)
					let letNumbers = 0;
					//if (letsUns === '' || letsUns <= 0) {
					//	letNumbers = 0;
				//	} else {
						letNumbers = letsUns - 1;
				//	}
					SelectedObj.css('left', letNumbers + 'px')
				}
				if (e && e.keyCode == 39) {
					let letNumbers = 0;
					//console.log(fat_wei - son_wei,fat_wei,son_wei)
				//	if (letsUns === '' || letsUns >= (fat_wei - son_wei - 3)) {
					//	letNumbers = (fat_wei - son_wei - 3);
				//	} else {
						letNumbers = letsUns + 1;
				//	}
					SelectedObj.css('left', letNumbers + 'px')
				}
			}
		}

		
		$("#btnSave").on("click", function() { 
			save();
			//window.parent.closeTab(window);
			//window.parent.ReQueryData();
		})
		
		$("#btnSaveAs").on("click", function () {
			 var newName = window.prompt("请输入另存的名称", "")
			 SheetTemplate.title = newName;
			 
			 $('#templateName').val(newName);
			 TemplateID = "";
			 save();
			 
			
		});

	$("#btnReturn").on("click", function () {
		window.srcWindow.WaitToReQueryData(500);
		window.parent.closeTab(window);
		 

	});
	function save() {
		var printSheet = $("#print_sheet");
			 let dataStyle = printSheet[0].style;
			var templateName = $('#templateName').val();
			if (!templateName) {
				  bw.toast("请输入模板名称", 5000); return; 
            }
		 
			//SheetTemplate.width = width2mm(dataStyle.width);
		//SheetTemplate.height = height2mm(dataStyle.height);
		SheetTemplate.landscape = $('#ckLandscape')[0].checked; 
        SheetTemplate.invertX = $('#ckInvertX')[0].checked; 
        SheetTemplate.invertY = $('#ckInvertY')[0].checked; 
		 
		SheetTemplate.fontSize = height2mm(dataStyle.fontSize);

	

	    SheetTemplate.paperHeight = $('#paper_height').val()||0
		if( SheetTemplate.paperHeight.toString().indexOf('未设')==0){
			SheetTemplate.paperHeight=0
		}

		
	    SheetTemplate.maxPageHeight = $('#max_page_height').val()||0
		if( SheetTemplate.maxPageHeight.toString().indexOf('未设')==0){
			SheetTemplate.maxPageHeight=0
		}

		SheetTemplate.eachLineLabel = $('#eachLineLabel').val() || 1
        SheetTemplate.horizontalGap = $('#horizontalGap').val() || 2
            SheetTemplate.verticalGap = $('#verticalGap').val() || 2
		
		var d= $('#paddingTop').val();
		SheetTemplate.paddingTop =d||0
		 d= $('#paddingBottom').val()
		SheetTemplate.paddingBottom = d||0
		 d= $('#paddingLeft').val()
		SheetTemplate.paddingLeft =d||0
		 d= $('#paddingRight').val()
		SheetTemplate.paddingRight =d||0

		var fontFamily = dataStyle.fontFamily || ''
		fontFamily=fontFamily.replace('"','').replace('"','')
			PropertyOptions.font.map(item => {
				if (fontFamily === item.name) {
					SheetTemplate.fontName = item.title
				}
			})

			SheetTemplate.name = templateName;
			//SheetTemplate.title = onloadDataAttr.template.title;
			SheetTemplate.fontBold = dataStyle.fontWeight
		    SheetTemplate.backImage = dataStyle['background-image'] || ''
			SheetTemplate.backImage = SheetTemplate.backImage.replace('url()', '')
			SheetTemplate.backImage = SheetTemplate.backImage.replace('"', '')
			SheetTemplate.backImage = SheetTemplate.backImage.replace('"', '')

			SheetTemplate.printBackImage = printSheet.data('printBackImage') 

		    SheetTemplate.exceedAction = printSheet.data('exceedaction')
			 


			let allAttrs = $("#print_sheet .print-area")
			for (let i = 0; i < allAttrs.length; i++) {
				let area = allAttrs[i]
				//if (area.className.indexOf('print_sheet_selects') >= 0) continue;
				
				let item_style = area.style
				let attr_son = SheetTemplate[area.attributes.data.nodeValue]
				if (!attr_son) {
					continue
                }
				if (area.style.display == "none") {
					attr_son.show = false;
				}
				else attr_son.show = true

				attr_son.fontSize = !item_style.fontSize?'':height2mm(item_style.fontSize)
				attr_son.fontBold = item_style.fontWeight;//? '': Number(item_style.fontWeight) >= 500?'bold':'normal'
				attr_son.fontStyle = item_style.fontStyle;
				
				attr_son.name = area.attributes.data.nodeValue
				attr_son.showTitle = true; 
				var fontFamily = item_style.fontFamily || ''
				fontFamily = fontFamily.replace('"','').replace('"','')
				PropertyOptions.font.map(itemson => {
					if (fontFamily === itemson.name) {
						attr_son.fontName = itemson.title
					}
				})

				attr_son.exceedAction = $(area).data('exceedaction')
				if (attr_son.exceedAction == undefined) attr_son.exceedAction = true 
				attr_son.exceedAction = (attr_son.exceedAction === "true" || attr_son.exceedAction === true)


				console.log(area)
				if (attr_son.show) {
					attr_son.left = width2mm(area.offsetLeft+'px');// width2mm("10px")
				    attr_son.height = height2mm($("#" + area.attributes.id.nodeValue).height() + "px")
					attr_son.elements = [];
					for (let j = 0; j <= area.children.length; j++) {
						let son_attrs = area.children[j]
						if (son_attrs && son_attrs.className === 'print-item') {
							let left_ = (son_attrs.style.left).split("px")
							let son_son_at = son_attrs.attributes
							let son_son_st = son_attrs.style
							let son_son_data = JSON.parse(son_son_at.data.nodeValue)
							let text = $(son_attrs).find('p').text()
							let font_famls_son = ''
							fontFamily = son_son_st.fontFamily || ''
							fontFamily = fontFamily.replace('"','').replace('"','')
							//if (fontFamily.toLowerCase().indexOf('yahei') >= 0) {
							//	debugger
                           // }
							


							PropertyOptions.font.map(itemson_son => {
								if (fontFamily=== itemson_son.name) {
									font_famls_son = itemson_son.title
								}
							})
							let obj = {
								name: son_son_data.name,
								title: text,
								x: width2mm(son_son_st.left),
								y: height2mm(son_son_st.top),
								//showTitle: "true"
							}
							var img=$(son_attrs).find('.print-image')
							if (img.length>0) {
								var wd = img[0].style.width 
								wd=px2mm(wd)
								var ht= img[0].style.height 
								ht = px2mm(ht)
								if (wd) obj.width = wd
								if (ht) obj.height = ht 
								obj.image = img[0].src								 
							}
							var element_width = $(son_attrs).data("element_width");
							if (element_width) {
								obj.width = element_width
							}
							var element_height = $(son_attrs).data("element_height");
							if (element_height) {
								obj.height = element_height
							}

                            var barcode_pic_text_hide = $(son_attrs).data("barcode_pic_text_hide");
                            var barcode_pic_text_size = $(son_attrs).data("barcode_pic_text_size");
                            console.log('barcode_pic_text_hide:', barcode_pic_text_hide)
                            console.log('barcode_pic_text_size:', barcode_pic_text_size)
                            if (barcode_pic_text_hide == true || barcode_pic_text_hide == "true") {
                                obj.barcode_pic_text_hide = barcode_pic_text_hide
                            }
                            if (barcode_pic_text_size) {
                                obj.barcode_pic_text_size = barcode_pic_text_size
                            }

							var dot_len = $(son_attrs).data("dot_len")
							if (dot_len) {
								obj.dotLen = dot_len
							}
							var exceedAction = $(son_attrs).data("exceedaction".toLowerCase());
							if (exceedAction) obj.exceedAction = exceedAction;
      
							var showStyle = $(son_attrs).data("show_style".toLowerCase());
							if (showStyle) obj.showStyle = showStyle;

							var char_spacing = $(son_attrs).data("char_spacing")
							if (char_spacing) {
								obj.charSpacing = char_spacing
							}
							var show_value = $(son_attrs).data("show_value")
							if (show_value) {
								obj.showValue = show_value
							}
							var hideEmpty = $(son_attrs).data("hideEmpty".toLowerCase());
							var chineseNum = $(son_attrs).data("chineseNum".toLowerCase());
							if (chineseNum == true || chineseNum == "true") obj.chineseNum = chineseNum
							if (hideEmpty == true || hideEmpty == "true") obj.hideEmpty = hideEmpty
							var showTitle = $(son_attrs).data("showTitle".toLowerCase());
							if (showTitle !== "" && showTitle !== undefined) obj.showTitle = showTitle

							var font_underline = $(son_attrs).data("font_underline".toLowerCase());
							if (font_underline !== "" && font_underline !== undefined) obj.font_underline = font_underline
														
							if (son_son_st.textAlign) obj.align = son_son_st.textAlign

							if (son_son_st.fontSize) obj.fontSize = height2mm(son_son_st.fontSize);
							if (font_famls_son) obj.fontName = font_famls_son;
							
							if (son_son_st.fontWeight) obj.fontBold = son_son_st.fontWeight
							 if (son_son_st.fontStyle) obj.fontStyle = son_son_st.fontStyle;
							attr_son.elements.push(obj);
						}
					}
					var tableID=''
					if (area.children.length > 1) {
						tableID=area.children[1].id
                    }
					if (tableID=== "table_table") {
						let tab_ = area.children[1]
						SheetTemplate.table.showEmptyRows = $(area).data('showEmptyRows')
						if (SheetTemplate.table.showEmptyRows == undefined) SheetTemplate.table.showEmptyRows = false

						SheetTemplate.table.showEmptyRows = (SheetTemplate.table.showEmptyRows === "true" || SheetTemplate.table.showEmptyRows === true)

						SheetTemplate.table.showTitle = $(area).data('showTitle'.toLowerCase())   

						SheetTemplate.table.showLines = $(area).data('showLines')
						if (SheetTemplate.table.showLines == undefined) SheetTemplate.table.showLines = true
						SheetTemplate.table.showLines = (SheetTemplate.table.showLines==="true" ||SheetTemplate.table.showLines===true)

						SheetTemplate.table.sortColumns = $(area).data('sortColumns') || ''
						SheetTemplate.table.sortDirection = $(area).data('sortDirection') || ''
						SheetTemplate.table.groupSumByColumn = $(area).data('groupSumByColumn') || ''				 
						  
						SheetTemplate.table.rowHeight =Number($(area).data('rowHeight'))||6
						if (SheetTemplate.table.rowHeight == undefined) SheetTemplate.table.rowHeight = ''

						SheetTemplate.table.lineWidth = Number($(area).data('lineWidth')) || 1
						if (SheetTemplate.table.lineWidth == undefined) SheetTemplate.table.lineWidth = ''

						
					    SheetTemplate.table.elements=[]
					for (let s = 0; s < tab_.children[0].children.length; s++) {
						let trColumn = tab_.children[0].children[s];
						let namse = JSON.parse(trColumn.attributes.data.nodeValue)
						let font_famls_son = ''
						console.log(trColumn.style)
						
						var title = $(trColumn.children[0]).find('p').text();
						let col = {
							name: namse.name,
							title: title,
							width: width2mm(trColumn.style.width)
							//width: width2mm(trColumn.offsetWidth+"px")
						}
						
						var showTitle = $(trColumn).data("showTitle".toLowerCase());
						if (showTitle) col.showTitle = showTitle;
						var showsum = $(trColumn).data("showsum".toLowerCase());
						if (showsum) col.showSum = showsum;

						var boldsum = $(trColumn).data("boldsum".toLowerCase());
						if (boldsum) col.boldSum = boldsum;

						var showpagesum = $(trColumn).data("showpagesum".toLowerCase());
						if (showpagesum) col.showPageSum = showpagesum;

						var showGroupSum = $(trColumn).data("showGroupSum".toLowerCase());
						if (showGroupSum) col.showGroupSum = showGroupSum;

						var dotLen = $(trColumn).data("dot_len".toLowerCase());
						if (dotLen) col.dotLen = dotLen;

						var exceedAction = $(trColumn).data("exceedaction".toLowerCase());
						if (exceedAction) col.exceedAction = exceedAction;

						var showStyle = $(trColumn).data("show_style".toLowerCase());
						if (showStyle) col.showStyle = showStyle;

						//var charSpacing = $(trColumn).data("char_spacing".toLowerCase());
						//if (charSpacing) col.charSpacing = charSpacing;
						 
						/*
						var align = $(trColumn).css("text-align");
						if (align) col.align = align;
						if (trColumn.style.fontSize) col.fontSize = height2mm(trColumn.style.fontSize);

						fontFamily=trColumn.style.fontFamily
						fontFamily = fontFamily.replace('"','').replace('"','')
						PropertyOptions.font.map(itemson_son => { 
							if (fontFamily=== itemson_son.ids) { 
								font_famls_son = itemson_son.title
							}
						})

						console.log(font_famls_son)

						if (font_famls_son) col.fontName = font_famls_son;
						if (trColumn.style.fontWeight) col.fontBold = trColumn.style.fontWeight
						*/
						function setColProp($ele,preFix) {
							function upper1(str) {
								return str.replace(str[0], str[0].toUpperCase());
							}
							function getColName(colName) {
								if (preFix) return preFix + upper1(colName); else return colName;
                            }
							 align = $ele[0].style["text-align"]
							if (align) col[getColName('align')] = align;
							 var fontSize = $ele[0].style['font-size']
							if (fontSize) col[getColName('fontSize')] = height2mm(fontSize);
							 var fontName = $ele[0].style['font-family']
							if (fontName) col[getColName('fontName')] = fontName;
							var fontBold = $ele[0].style['font-weight']
							var nFontWeight=parseInt(fontBold)
							if (nFontWeight >= 700) fontBold = 'bold'
							else if (nFontWeight <= 400 && nFontWeight > 0) fontBold = 'normal'
							if (fontBold) col[getColName('fontBold')] = fontBold

							var fontStyle = $ele[0].style['font-style']
							if (fontStyle) col[getColName('fontStyle')] = fontStyle

							var charSpacing = $ele.data("char_spacing".toLowerCase());
						    if (charSpacing) col.charSpacing = charSpacing;

							var showStyle = $ele.data("show_style".toLowerCase());
							if (showStyle) col.showStyle = showStyle;

							var showValue = $ele.data("show_value".toLowerCase());
							if (showValue) col.showValue = showValue;

							var columnSumValue = $ele.data("column_sum_value".toLowerCase());
							if (columnSumValue) col.columnSumValue = columnSumValue;

						}
						setColProp($(trColumn))
						setColProp($(trColumn).find('.tdClass'),'cell')
						/*
						var $ele=$(trColumn).find('.tdClass')
						 align = $ele.css("text-align")
						if (align) col.cellAlign = align;
						 var fontSize = $ele.css("font-size")
						if (fontSize) col.cellFontSize = height2mm(fontSize);
						 var fontName = $ele.css("font-family")
						if (fontName) col.cellFontName = fontName;
						var fontBold = $ele.css("font-weight")
						if (fontBold) col.cellFontBold = fontBold
						*/

						SheetTemplate.table.elements.push(col)
					}
				}
			}
		}
		if (SheetTemplate.pageHead)
			setHtmlContentForArea('print_pageHead', SheetTemplate.pageHead)
		if (SheetTemplate.tableHead)
			setHtmlContentForArea('print_tableHead', SheetTemplate.tableHead)
		if(SheetTemplate.table)
		   setHtmlContentForArea('print_table', SheetTemplate.table)		
		if (SheetTemplate.tableTail)
			setHtmlContentForArea('print_tableTail', SheetTemplate.tableTail)
		if (SheetTemplate.pageTail)
			setHtmlContentForArea('print_pageTail', SheetTemplate.pageTail)

		function setHtmlContentForArea(areaID,areaJSON) {

			areaJSON.htmlContent = null
			var ifr = document.getElementById(`${areaID}_editor_textarea_ifr`)
			if(ifr){
				// iframe内的元素不能直接使用document.get读取，需要先将document指向ifr.contentWindow
				var newTableContent = ifr.contentWindow.document.getElementById('tinymce');
				 
				if (newTableContent) {
					//var table = newTableContent.firstChild
					var table = $(newTableContent).find('table')
					if (table.length > 0) {
						table = table[0]
						
						var div = `
<div id='divTemp' style="top:0px;left:0px;z-index:-1;position:absolute;height:${table.parentNode.offsetHeight}px;width:${table.parentNode.offsetWidth}px;">${table.outerHTML}</div>
                        `
						var $div = $(div)
						$('#divTemp').remove()
						$('body').append($div[0])

						var trs = $div.find('tr')
						var elementsToSet = []
						for (var i = 0; i < trs.length; i++) {
							var tr = trs[i]
							var mm = px2mm(tr.offsetHeight)
							//$(tr).css('height', mm + 'mm')
							//	tr.setAttribute('data-mce-style', '')
							elementsToSet.push({ ele: $(tr), style: 'height', value: mm + 'mm' })
							elementsToSet.push({ ele: $(tr), attr: 'data-mce-style', value: '' })
						}
						var tds = $div.find('td')
						for (var i = 0; i < tds.length; i++) {
							var td = tds[i]
							var mm = px2mm(td.offsetWidth)
							elementsToSet.push({ ele: $(td), style: 'width', value: mm + 'mm' })
							elementsToSet.push({ ele: $(td), attr: 'data-mce-style', value: '' })
							elementsToSet.push({ ele: $(td), style: 'height', value: '' })
							//$(td).css('width', mm + 'mm')
							// $(td).css('height','')
							//td.setAttribute('data-mce-style','')
						}
						elementsToSet.forEach((ele) => {
							if (ele.style) ele.ele.css(ele.style, ele.value)
							else if (ele.attr) ele.ele.attr(ele.attr, ele.value)
						}) 

						areaJSON.htmlContent = $div[0].innerHTML;
					}
				}
			}

        }
		
			
			

			var s = JSON.stringify(SheetTemplate)
			var json=JSON.parse(s)
			$.ajax({
				url: "/api/PrintTemplate/SaveTemplate", //url地址
				contentType: 'application/json',
				type: "post", //发起请求的方式
				data: JSON.stringify({
					operKey:g_operKey,
					sheet_type: SheetType,
					template_id: TemplateID,
					template_name:templateName,
					template_content:json
				}),
				success: function(res) {
					console.log(res)
					if (res.newTemplateID) {
						TemplateID = res.newTemplateID;
					}
					bw.toast("保存成功", 1000);
				},
				error: function(res) {
					console.log(res)
				}
			});
        }
		function drag_dli(){
			$("#btnRemoveItem").on("click",function(){
				SelectedObj.remove()
			})
		}
		function width2mm(obj) {
			let wie_;
			if (obj) {
				wie_ = obj.split("px")
			} else {
				wie_ = 0
			}
			return px2mm(wie_.length >= 2 ? wie_[0] : wie_, "w");
		}

		function height2mm(obj) {
			let wie_;
			if (obj) {
				wie_ = obj.split("px")
			} else {
				wie_ = 0
			}
			return px2mm(wie_.length >= 2 ? wie_[0] : wie_, "h");
		}
		// #print_pageHead,#print_tableHead,#print_table,#print_tableTail,#print_pageTail
		// tables
	})



	

var BePrompt = {
    defaultConfig: {
        type: 'text',
        Desc: '',
        length: 5
    },
    Html: '<div class="Prompt"><div class="PromptTitle"><div class="PromptCancel">取消</div><div class="PromptConfirm">完成</div><em class="PromptName"></em></div><div class="PromptList"></div></div>',
    HtmlArea: '<textarea name="PromptArea" id="PromptArea" ></textarea>',
    open: function (message, defaultText, callback, o) {
        var opts = {}, that = this;
        $.extend(opts, this.defaultConfig, o);
        $('body').append(this.Html);
        message && $('.PromptName').html(message).show();
        var PromptList = $('.PromptList')
 
        if (opts.type == 'text') {
            PromptList.append('<input id="PromptText" name="PromptText" type="' + opts.type + '" autocomplete="off" required="required" maxlength="' + opts.length + '" placeholder="请输入' + message + '" value="' + defaultText + '" />');
        } else {
            PromptList.append('<textarea name="PromptText" id="PromptText" required="required" placeholder="' + opts.Desc + '">' + defaultText + '</textarea>');
        }
        PromptList.append('<span class="PromptLen">' + defaultText.length+'/'+ opts.length+'</span>')
        $('.Prompt').animate({ 'top': '0px', 'opacity': '1' }, 200);
        $('#PromptText').bind('keydown',
            function () {
                var text = $(this).val()
                if (text.length >= opts.length) {
                    $(this).val(text.substring(0, opts.length-1));
                }
                $(".PromptLen").text(text.length + "/" + opts.length);
            });
        $('.PromptConfirm').bind('click',
            function () {
                that.close();
                typeof callback === 'function' && callback(true, $('#PromptText').val())
            });
 
        $('.PromptCancel').bind('click',
            function () {
                that.close();
                typeof callback === 'function' && callback(false)
            })
    },
    close: function () {
        $(".Prompt").animate({ 'top': '100%', 'opacity': '0' }, 300, function () {
            $(".Prompt").remove()
        })
    }
};
var _prompt = window.prompt;
/*window.prompt = function (message, defaultText, callback, opts) {
    if (typeof callback == 'function') {
        BePrompt.open(message, defaultText, callback, opts)
    } else { return _prompt(message, defaultText) }
};*/

// scales the image by (float) scale < 1
// returns a canvas containing the scaled image.
function downScaleImage(img, scale) {
    var imgCV = document.createElement('canvas');
    imgCV.width = img.width;
    imgCV.height = img.height;
    var imgCtx = imgCV.getContext('2d');
    imgCtx.drawImage(img, 0, 0);
   
    return downScaleCanvas(imgCV, scale);
}

// scales the canvas by (float) scale < 1
// returns a new canvas containing the scaled image.
function downScaleCanvas(cv, scale) {
   // bw.toast('downscaleimage a scale:'+scale);
    if (!(scale < 1) || !(scale > 0)) { bw.toast('scale must be a positive number < 1'); return;} //throw ('scale must be a positive number <1 ');
    
    var sqScale = scale * scale; // square scale = area of source pixel within target
    
    var sw = cv.width; // source image width
     
    var sh = cv.height; // source image height
     
    var tw = Math.floor(sw * scale); // target image width
    var th = Math.floor(sh * scale); // target image height
    var sx = 0, sy = 0, sIndex = 0; // source x,y, index within source array
    var tx = 0, ty = 0, yIndex = 0, tIndex = 0; // target x,y, x,y index within target array
    var tX = 0, tY = 0; // rounded tx, ty
    var w = 0, nw = 0, wx = 0, nwx = 0, wy = 0, nwy = 0; // weight / next weight x / y

 
    // weight is weight of current source point within target.
    // next weight is weight of current source point within next target's point.
    var crossX = false; // does scaled px cross its current px right border ?
    var crossY = false; // does scaled px cross its current px bottom border ?
    var sBuffer = cv.getContext('2d').getImageData(0, 0, sw, sh).data; // source buffer 8 bit rgba
    var tBuffer = new Float32Array(3 * tw * th); // target buffer Float32 rgb
    var sR = 0, sG = 0, sB = 0; // source's current point r,g,b
    /* untested !
    var sA = 0;  //source alpha  */
 
    for (sy = 0; sy < sh; sy++) {
        ty = sy * scale; // y src position within target
        tY = 0 | ty;     // rounded : target pixel's y
        yIndex = 3 * tY * tw;  // line index within target array
        crossY = (tY != (0 | ty + scale));
        if (crossY) { // if pixel is crossing botton target pixel
            wy = (tY + 1 - ty); // weight of point within target pixel
            nwy = (ty + scale - tY - 1); // ... within y+1 target pixel
        }
        for (sx = 0; sx < sw; sx++ , sIndex += 4) {
            tx = sx * scale; // x src position within target
            tX = 0 | tx;    // rounded : target pixel's x
            tIndex = yIndex + tX * 3; // target pixel index within target array
            crossX = (tX != (0 | tx + scale));
            if (crossX) { // if pixel is crossing target pixel's right
                wx = (tX + 1 - tx); // weight of point within target pixel
                nwx = (tx + scale - tX - 1); // ... within x+1 target pixel
            }
            sR = sBuffer[sIndex];   // retrieving r,g,b for curr src px.
            sG = sBuffer[sIndex + 1];
            sB = sBuffer[sIndex + 2];

            /* !! untested : handling alpha !!
               sA = sBuffer[sIndex + 3];
               if (!sA) continue;
               if (sA != 0xFF) {
                   sR = (sR * sA) >> 8;  // or use /256 instead ??
                   sG = (sG * sA) >> 8;
                   sB = (sB * sA) >> 8;
               }
            */
            if (!crossX && !crossY) { // pixel does not cross
                // just add components weighted by squared scale.
                tBuffer[tIndex] += sR * sqScale;
                tBuffer[tIndex + 1] += sG * sqScale;
                tBuffer[tIndex + 2] += sB * sqScale;
            } else if (crossX && !crossY) { // cross on X only
                w = wx * scale;
                // add weighted component for current px
                tBuffer[tIndex] += sR * w;
                tBuffer[tIndex + 1] += sG * w;
                tBuffer[tIndex + 2] += sB * w;
                // add weighted component for next (tX+1) px                
                nw = nwx * scale
                tBuffer[tIndex + 3] += sR * nw;
                tBuffer[tIndex + 4] += sG * nw;
                tBuffer[tIndex + 5] += sB * nw;
            } else if (crossY && !crossX) { // cross on Y only
                w = wy * scale;
                // add weighted component for current px
                tBuffer[tIndex] += sR * w;
                tBuffer[tIndex + 1] += sG * w;
                tBuffer[tIndex + 2] += sB * w;
                // add weighted component for next (tY+1) px                
                nw = nwy * scale
                tBuffer[tIndex + 3 * tw] += sR * nw;
                tBuffer[tIndex + 3 * tw + 1] += sG * nw;
                tBuffer[tIndex + 3 * tw + 2] += sB * nw;
            } else { // crosses both x and y : four target points involved
                // add weighted component for current px
                w = wx * wy;
                tBuffer[tIndex] += sR * w;
                tBuffer[tIndex + 1] += sG * w;
                tBuffer[tIndex + 2] += sB * w;
                // for tX + 1; tY px
                nw = nwx * wy;
                tBuffer[tIndex + 3] += sR * nw;
                tBuffer[tIndex + 4] += sG * nw;
                tBuffer[tIndex + 5] += sB * nw;
                // for tX ; tY + 1 px
                nw = wx * nwy;
                tBuffer[tIndex + 3 * tw] += sR * nw;
                tBuffer[tIndex + 3 * tw + 1] += sG * nw;
                tBuffer[tIndex + 3 * tw + 2] += sB * nw;
                // for tX + 1 ; tY +1 px
                nw = nwx * nwy;
                tBuffer[tIndex + 3 * tw + 3] += sR * nw;
                tBuffer[tIndex + 3 * tw + 4] += sG * nw;
                tBuffer[tIndex + 3 * tw + 5] += sB * nw;
            }
        } // end for sx 
    } // end for sy
   
    // create result canvas
    var resCV = document.createElement('canvas');
    resCV.width = tw;
    resCV.height = th;
    var resCtx = resCV.getContext('2d');
    var imgRes = resCtx.getImageData(0, 0, tw, th);
    var tByteBuffer = imgRes.data;
    // convert float32 array into a UInt8Clamped Array
    var pxIndex = 0; //  
    for (sIndex = 0, tIndex = 0; pxIndex < tw * th; sIndex += 3, tIndex += 4, pxIndex++) {
        tByteBuffer[tIndex] = Math.ceil(tBuffer[sIndex]);
        tByteBuffer[tIndex + 1] = Math.ceil(tBuffer[sIndex + 1]);
        tByteBuffer[tIndex + 2] = Math.ceil(tBuffer[sIndex + 2]);
        tByteBuffer[tIndex + 3] = 255;
    }

 // 将canvas的透明背景设置成白色 
	 
	for(var i = 0; i < tByteBuffer.length; i += 4) {
		// 当该像素是透明的，则设置成白色
		if(tByteBuffer[i + 3] == 0) {
			tByteBuffer[i] = 255;
			tByteBuffer[i + 1] = 255;
			tByteBuffer[i + 2] = 255;
			tByteBuffer[i + 3] = 255; 
		}
	}

 
    // writing result to canvas.
    resCtx.putImageData(imgRes, 0, 0);
    return resCV;
}


	window.resizeImage = function (blobURL, max_width, max_height, resolution, maxHtBl, callback) {
    window.URL = window.URL || window.webkitURL;
		 
		 
	//		blobURL = window.URL.createObjectURL(blob); // and get it's URL
		  
    if (!maxHtBl) {
        maxHtBl = 2;
    }
  
    // helper Image object
    var image = new Image();
    image.src = blobURL;

    image.onload = function () {
        // have to wait till it's loaded
        // var resized = resizeMe(image); // send it to canvas

        EXIF.getData(image, function () {
            //EXIF.getAllTags(this);

            var orient = EXIF.getTag(this, 'Orientation');
            var canvas = document.createElement('canvas');
            // resize the canvas and draw the image data into it
            orient = 1;//since recent ios update,iphone seems does not roate the image even the orient is 6. So we needn't rotate it any longer
 

            var ctx = canvas.getContext("2d");
            var width = image.width;
            var height = image.height;
            var wd = width, ht = height;
            var degree = 0;
            var step = 0;

            var funcDoResize = function () {


                if (navigator.userAgent.match(/iphone/i)) {
                    if (orient !== "" && orient !== 1) {
                        switch (orient) {
                            case 6://需要顺时针(向左)90度旋转   
                                //rotateImg(this, 'left', canvas);
                                step = 1;
                                ht = width;
                                wd = height;
                                break;
                            case 8://需要逆时针(向右)90度旋转   
                                step = 3;
                                ht = width;
                                wd = height;
                                break;
                            case 3://需要180度旋转   
                                // rotateImg(this, 'right', canvas);//左转两次  
                                // rotateImg(this, 'right', canvas);
                                step = 2;
                                break;
                        }
                    }
                }
                degree = step * 90 * Math.PI / 180;
                if (max_width > wd) max_width = wd;
                if (max_height > ht) max_height = ht;
                var wl = max_width / wd, hl = max_height / ht;
                var bl = wl < hl ? wl : hl;

                //  bw.toast('rk5q wd:' + wd + 'ht:' + ht + 'max_width:' + max_width + 'bl:' + bl + 'wl:' + wl + 'hl:' + hl + 'max_height:' + max_height);


                ht = Math.round(ht * bl); wd = Math.round(wd * bl);
                canvas.width = wd; canvas.height = ht;



                //bw.toast('rk error'+err.message);
                var drawImage = function () {
                    if (step === 1) {
                        if (ht / wd > maxHtBl) {
                            var keepHt = wd * maxHtBl;
                            var cutHt = (ht - keepHt) / 2;
                            canvas.height = keepHt;
                            ctx.rotate(degree);
                            ctx.translate(-cutHt, 0);
                            ctx.drawImage(image, cutHt / bl, 0, keepHt / bl, height, cutHt, 0, keepHt, -wd);
                        }
                        else {
                            ctx.rotate(degree);
                            ctx.drawImage(image, 0, 0, ht, -wd);
                        }
                    }
                    else if (step === 2) {
                        ctx.rotate(degree);
                        ctx.drawImage(image, 0, 0, -wd, -ht);
                    }
                    else if (step === 3) {
                        ctx.rotate(degree);
                        ctx.drawImage(image, 0, 0, -ht, wd);
                    }
                    else if (step === 0) {
                        if (ht / wd > maxHtBl) {
                            var keepHt = wd * maxHtBl;
                            var cutHt = (ht - keepHt) / 2;
                            canvas.height = keepHt;
                            //ctx.translate(0,-cutHt);
                            // ctx.drawImage(image, 0, cutHt / bl, width, keepHt / bl, 0, 0, wd, keepHt);
                            ctx.drawImage(image, 0, cutHt, wd, keepHt, 0, 0, wd, keepHt);
                        }
                        else ctx.drawImage(image, 0, 0, wd, ht);
					}
					// 将canvas的透明背景设置成白色
					var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
					for(var i = 0; i < imageData.data.length; i += 4) {
						// 当该像素是透明的，则设置成白色
						if(imageData.data[i + 3] == 0) {
							imageData.data[i] = 255;
							imageData.data[i + 1] = 255;
							imageData.data[i + 2] = 255;
							imageData.data[i + 3] = 255; 
						}
					}
					ctx.putImageData(imageData, 0, 0);
                    canvas.toBlob(callback, "image/jpeg", resolution); // get the data from canvas as 70% JPG    

                }
                if (bl == 1) {
                    drawImage();
                }
                else {
                    var canvasScale = downScaleImage(image, bl);
                    canvasScale.toBlob(function (newblob) {

                        blobURL = window.URL.createObjectURL(newblob); // and get it's URL

                        // helper Image object
                        image = new Image();
                        image.src = blobURL;
                        image.onload = function () {
                            drawImage();
                        }

                    }, "image/jpeg", 1);
                }
            }
            if (width * maxHtBl < height) {
                jConfirm('图片很长，是否只截取中间部分？', function () {
                    funcDoResize();
                }, function () {
                    maxHtBl = 20;
                    funcDoResize();
                },"是","否");
            }
            else
                funcDoResize();

           



        });
    }
    //preview.appendChild(image); // preview commented out, I am using the canvas instead

};

</script>
