﻿@page
@model ArtisanManage.Pages.BaseInfo.CombineTemplateModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="en">

<head>

    <partial name="_SheetHead" model="Model.PartialViewModel" />

    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxtooltip.js"></script>

    <script type="text/javascript" src="~/Sheet/CombineTemplate.js?v=@Model.PartialViewModel.Version"></script>

    <script type="text/javascript">
        var hideCost = true;
        var seeInPrice = @Html.Raw(Model.JsonOperRights.Replace("stock.CombineTemplate", "delicacy.seeInPrice.value"));
        if (seeInPrice == 'true') hideCost = false;
        function isRightClick(event) {
            if (!event) {
                event = window.event;
            }
            if (event.which) {
                return event == 3;
            }
            else if (event.button) {
                return event == 2;
            }
            else {
                return false;
            }
        }
        function attachContextMenu() {
            $('#depart_path').on('mousedown', (event) => {
                if (event.target.tagName == "DIV" && event.target.tagName == "LI") {
                    var target = event.target.parentNode;
                    var contextMeun = event.target.innerText == "全部" ? contextMenu0 : contextMenu1;
                    if (isRightClick(event)) {
                        $("#depart_path").jqxTree('selectItem', target);
                        var scrollTop = $(window).scrollTop();
                        var scrollLeft = $(window).scrollLeft();
                        contextMenu.jqxMenu('open', parseInt(event.clientX) + 5 + scrollLeft, parseInt(event.clientY) + 5 + scrollTop);
                        return false;
                    }
                }
            })
        }
        function getQueryString(name) {
            var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
            var r = window.location.search.substr(1).match(reg);
            if (r != null) {
                return decodeURI(r[2])
            }
            return null;
        }
        function ShowApportionmentDetail() {
            let apportionmentWay = $("#apportionment_way>input").val().value
            let processFee = $("#process_fee>input").val();
            if ((apportionmentWay == "price" || apportionmentWay == "count") && Number(processFee)) {
                $("#apportionmentDetail").attr("disabled", false)
            } else {
                $("#apportionmentDetail").attr("disabled", true)
            }
        }
        function GetJqxgridBSheetRows(gridSlt, inoutFlag) {
            var rows = $(gridSlt).jqxGrid('getrows')//出仓
            var sheetRows = new Array
            var rowIndex = 0;

            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                if (!row.item_id) {
                    continue
                }
                var sheetRow = row;
                if (sheetRow.cost_price)
                    sheetRow.cost_amount = toMoney(sheetRow.cost_price * sheetRow.quantity, 2)

                sheetRows[rowIndex++] = sheetRow
            }
            if (sheetRows.length == 0) {
                return { result: 'Error', msg: '请输入至少一行商品' }
            }
            return { result: 'OK', msg: '', sheetRows: sheetRows }
        }
        $(document).ready(() => {
            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return true;
            });
            var divMenu = `<div id='gridMenu_jqxgrid'>
                                                        <ul>
                                                            <li>历史单据</li>
                                                            <li>取消</li>
                                                        </ul>
                                                   </div>`;
            $('body').append(divMenu);
            var contextMenu = $('#gridMenu_jqxgrid').jqxMenu({ width: 200, height: 58, autoOpenPopup: false, mode: 'popup' });
            $("#jqxgrid").on('rowclick', function (event) {
                if (event.args.rightclick) {
                    $("#jqxgrid").jqxGrid('selectrow', event.args.rowindex);
                }
            })

        })
    </script>
    <style>

        #topCoverDiv {
            opacity: 0.4;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0px;
            background-color: #000;
            z-index: 100;
            text-align: center;
        }

        #dia {
            background: rgba(255, 255, 255, 1);
            z-index: 200;
            position: absolute;
            top: 50%;
            left: 50%;
            width: 300px;
            height: 250px;
            margin: -200px 0 0 -250px;
            border-radius: 10px;
            border-top-width: 10px;
            padding: 10px 10px 10px 10px;
        }

        .show_close {
            height: 20px;
            width: 20px;
            margin-left: 480px;
            cursor: pointer;
            display: inline-block;
            float: right;
        }

        #rp:hover {
            display: block;
        }

        #payway1 {
            overflow-y: auto;
        }

        #apportionmentDetail:hover {
            cursor: pointer;
        }

    </style>
</head>

<body class='default' style="overflow:hidden;">
    <style>
        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }
    </style>
    <div id="divTitle" style="text-align:center;height:45px;margin-top:5px;">
        <label id="lblSheetTitle" style="font-weight:500;font-size:25px;">@Html.Raw(Model.SheetTitle)</label>
        <img id="imgState" style="display:none;position:fixed;top:0px;left:calc(50% - 150px);" src="" />
        <div id="orderSourceId" class="makeInfo" style="position:absolute;top:15px; right:100px">
            <div style="font-size: 16px !important; font-weight: bolder">
                <div id="orderSourceText" style="color: #C61848"></div>
            </div>
        </div>
    </div>
    <div id="divHead" class="headtail" style="margin-bottom:5px;margin-top:0px;">
        <div style="float:none;height:0px; clear:both;"></div>
    </div>
    <div style="padding:10px">出仓商品</div>
    <div id="jqxgrid" style="margin-left:10px; position:static;width:100%;height:50%;border-bottom-color:#dedede;"></div>
    <div style="padding:10px">入仓商品</div>
    <div id="divMiddle" class="headtail" style="margin-bottom:10px;margin-top:0px;">
        <div style="float:none;height:0px; clear:both;"></div>
    </div>

    <div id="jqxgridB" style="margin-left:10px; position:static;width:100%;height:50%;border-bottom-color:#dedede;"></div>

    <div style="display:flex;flex-direction:column">
        <!--<div id="divTail" class="headtail" style="position:relative;margin-top:10px;display:flex;align-items:center;">
            <div style="float:none;height:0px; clear:both;"></div>
            <span style="position:absolute;left:360px;top:0; color:#aaa;font-size:14px;line-height:40px">加工费均作用于入仓商品</span>
            <button type="button" id="apportionmentDetail" style="position:absolute;left:560px;top:0;width:100px">查看分摊详情</button>

        </div>-->

        <div id="divButtons" style="text-align:center;">
            <button id="btnSave" type="button">保存</button>
            <button id="btnDelete" type="button" disabled>删除</button>
            <button id="btnAdd" type="button">新增</button>
            <button id="btnClose" type="button">关闭</button>

        </div>

        <div id="popItem" style="display:none">
            <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;">
                <span style="font-size:20px;">选择商品</span>
            </div>
            <div style="overflow:hidden;"> </div>
        </div>
        <div id="popApportionmentDetail" style="display:none;">
            <div id="ApportionmentDetailCaption" style="background-color:#fff;width:100%;display:flex;flex-direction:column">
                <span style="padding:10px 0;width:100%;font-size:20px;display:flex;justify-content:center">分摊明细</span>
            </div>
            <div style="overflow:hidden;"> </div>
        </div>
    </div>




</body>

</html>
