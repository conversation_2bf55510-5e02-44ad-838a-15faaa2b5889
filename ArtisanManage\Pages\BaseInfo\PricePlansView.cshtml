﻿@page
@model ArtisanManage.Pages.BaseInfo.PricePlansViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <link href="~/css/component.css" rel="stylesheet" />
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "PlanEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var row_arr = new Array;

                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;
                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "i",rs.data.record.unit_no);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "is_big_unit", rs.data.record.is_big_unit);
                }
                $("#popItem").jqxWindow('close');
            };
        });
        function btnAddItem_click(e) {
            $('#AddDialogId').show();
        }

        function onGridRowEdit(rowIndex) {
            var plan_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'plan_id');
            //EditId = plan_id.split("'")[5];
            EditId =  plan_id
            $('#dialogId').show();
            /*
            var plan_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'plan_id');
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="PlanEdit?operKey=' + g_operKey + '&plan_id=' + plan_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
            */
        }



        var EditId = '';
    	$(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)


            $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                console.log(args);
                if (args.datafield == "delete") {
                    if (args.originalEvent.button == 2) return;
                    rowIndex = args.rowindex;
                    var plan_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'plan_id');
                    console.log("plan_id",plan_id);
                    var id = plan_id.split("'")[5];
                    jConfirm(`确定要删除选中价格方案吗？`, function () {
                        $.ajax({
                            url: '../api/PricePlansView/DeleteRecords',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({ operKey: g_operKey, gridID: 'gridItems', rowIDs: id }),
                            success: function (data) {
                                if (data.result == 'OK') {
                                    QueryData();
                                }
                                else {
                                    bw.toast(data.msg, 5000);
                                }
                            }
                        });
                    }, "");
                } 
                else if (args.datafield == "edit") {
                    console.log("edit")
                    onGridRowEdit(args.rowindex)
                    /*var plan_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, 'plan_id');
                    EditId = plan_id.split("'")[5];
                    $('#dialogId').show();*/
                }
                else if (args.datafield == "copy") {
                    var plan_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, 'plan_id');
                    EditId = plan_id.split("'")[5];
                    $('#CopyDialogId').show();
                } else if (args.datafield == "plan_name") {
                    if (ForSelect) {
                        // $("#popItem").jqxWindow('setContent', '<iframe src="ClientEdit?operKey=' + g_operKey + '&supcust_id=' + id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                        var plan_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "plan_id");
                        var plan_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "plan_name");
                        var msg = {
                            msgHead: 'PricePlansView', action: 'select', plan_id: plan_id, plan_name: plan_name
                        };
                        window.parent.postMessage(msg, '*');

                    }
                }

              
            });

            $("#Cancel").on('click', function () {
                for (var i = 0; i < 10; i++) {
                    $('#jqxgrid').jqxGrid('deleterow', i);
                    $('#jqxgrid').jqxGrid('addrow', i, {})
                }
            });

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });


            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            QueryData();
    	});
    </script>
</head>

<body style="overflow:hidden">
    <div role="dialog" id="dialogId">
        <div class="dialog_container normal" style="margin-left: 50%;transform: translate(-50%);">
            <div class="dialog_head">
                <a class="btnClose item-none">x</a><b class="item-auto">修改价格方案</b>
            </div>
            <div style="height:50px;padding-top:25px;padding-left:40px">
                新的价格方案名称：<input type="text" id="newName" style="font-size:14px; border-radius:6px;border-color:#ddd;border-width:0.5px; width:200px;height:25px;" />
            </div>
            <div class="dialog_foot">
                <button class="btnClose">取消</button>
                <button class="btnOk">确认</button>
            </div>
        </div>
    </div>

    <div role="dialog" id="AddDialogId">
        <div class="dialog_container normal" style="margin-left: 50%;transform: translate(-50%);">
            <div class="dialog_head">
                <a class="btnClose item-none">x</a><b class="item-auto">新增价格方案</b>
            </div>
            <div style="height:50px;padding-top:25px;padding-left:35px">
                新增价格方案的名称：<input type="text" id="addName" style="font-size:14px; border-radius:6px;border-color:#ddd;border-width:0.5px; width:200px;height:25px; padding: 8px;" />
            </div>
            <div class="dialog_foot">
                <button class="btnClose">取消</button>
                <button class="btnOk">确认</button>
            </div>
        </div>
    </div>

    <div role="dialog" id="CopyDialogId">
        <div class="dialog_container normal" style="margin-left: 50%;transform: translate(-50%);">
            <div class="dialog_head">
                <a class="btnClose item-none">x</a><b class="item-auto">复制价格方案</b>
            </div>
            <div style="height:50px;padding-top:25px;padding-left:35px">
                复制后价格方案名称：<input type="text" id="copyName" style="font-size:14px; border-radius:6px;border-color:#ddd;border-width:0.5px; width:200px;height:25px;" />
            </div>
            <div class="dialog_foot">
                <button class="btnClose">取消</button>
                <button class="btnOk">确认</button>
            </div>
        </div>
    </div>

     

    <div style="display:flex;justify-content:space-around;margin-top:20px;">
        <div style="font:35px;font-weight:bold">价格方案列表</div>
        <div style="display:flex">
            <div><button onclick="btnAddItem_click()">新增方案</button></div>
            <div><button onclick="btnBatchRemove_click()">批量删除</button></div>
        </div>
    </div>

  
    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:calc(100% - 80px);"></div>
     

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

    <partial name="dialog" />

    <script type="text/javascript">
        function btnBatchRemove_click(e) {
            var ids = [];
            $('.btn:checked').each(function () {
                ids.push(this.id);
            });
            console.log(ids);
            if (ids.length == 0) {
                bw.toast('请选择至少一个价格方案');
                return;
            };
            jConfirm(`确定要删除选中价格方案吗？`, function () {
                $.ajax({
                    url: '../api/PricePlansView/BatchDelete?operKey=@Model.OperKey',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ dataids: ids }),
                    success: function (data) {
                        if (data.result == 'OK') {
                            QueryData();
                            bw.toast('删除成功',3000);
                        }
                        else {
                            bw.toast(data.msg, 5000);
                        }
                    }
                });
            },"");
        };

        function EditPlanName(action) {
            debugger
            var newN = $("#newName").val();

            var formData = {
                newName: newN,
                id: EditId,
                operKey: window.g_operKey
            };
            $.ajaxSetup({ contentType:"application/json"});
            $.post(`/api/PricePlansView/${action}?operKey=@Model.OperKey`, JSON.stringify(formData)).then(res => {
                console.log(res);
                if (res) {
                    QueryData();
                }
                else bw.toast(data.msg, 5000);

            });
        }

        function AddPlan(action) {
            var addN = $("#addName").val();

            var formData = {
                addName: addN,
                operKey: window.g_operKey
            };
            /*
            $.ajaxSetup({ contentType:"application/json"});
            $.post(`/api/PricePlansView/${action}`, JSON.stringify(formData)).then(res => {
                console.log(res);
                if (res) {
                    QueryData();
                }
                else bw.toast(data.msg, 5000);

            });*/
            ajaxPost(`/api/PricePlansView/${action}`,formData).then(res => {
                console.log(res);
                if (res.result == 'OK') {
                    QueryData();
                    bw.toast(res.msg, 3000);
                } 
                else {
                    bw.toast(res.msg, 3000);
                }
            })
        }

        function CopyPlan(action) {
            var copyN = $("#copyName").val();

            var formData = {
                copyName: copyN,
                operKey: window.g_operKey
            };
            $.ajaxSetup({ contentType: "application/json" });
            $.post(`/api/PricePlansView/${action}`, JSON.stringify(formData)).then(res => {
                console.log(res);
                if (res) {
                    QueryData();
                }
                else bw.toast(data.msg, 5000);

            });
        }

        $(function () {
            var el = $('#dialogId')
            el.hide()

            new EventRegister({
                trigger: `[show=dialogId]`,
                handler() {
                    el.show();
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnClose',
                handler() {
                    el.hide()
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnOk',
                handler() {
                    EditPlanName("EditPlanName");
                    el.hide()
                }
            }).register();
        })

        $(function () {
            var el = $('#AddDialogId')
            el.hide()

            new EventRegister({
                trigger: `[show=AddDialogId]`,
                handler() {
                    el.show();
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnClose',
                handler() {
                    el.hide()
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnOk',
                handler() {
                    AddPlan("AddPlan");
                    el.hide()
                }
            }).register();
        })

        $(function () {
            var el = $('#CopyDialogId')
            el.hide()

            new EventRegister({
                trigger: `[show=CopyDialogId]`,
                handler() {
                    el.show();
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnClose',
                handler() {
                    el.hide()
                }
            }).register();
            new EventRegister({
                listener: el,
                trigger: '.btnOk',
                handler() {
                    CopyPlan("CopyPlan");
                    el.hide()
                }
            }).register();
        })

    </script>

</body>
</html>