@page
@model ArtisanManage.Pages.BaseInfo.PayQrCodeViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());

        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "PayQrCodeEdit") {
                QueryData();
                $("#popItem").jqxWindow('close');
            };
        });

    	var newCount = 1;
        var itemSource = {};
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)

            $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                if (args.datafield == "sub_code") {
                    if (args.originalEvent.button == 2) return;

                    if (ForSelect) {
                        var sub_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "sub_id");
                        var sub_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "sub_name");
                        var msg = {
                            msgHead: 'PayQrCodeView', action: 'select', sub_id: sub_id, sub_name: sub_name
                        };
                        window.parent.postMessage(msg, '*');
                    }
                    else {
                        onGridRowEdit(args.rowindex);
                    }
                }
            });


            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 450, width: 550, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            $("#photo").jqxWindow({ isModal: true, modalOpacity: 0.6, height: 600, width: 550, theme: 'dark', autoOpen: false, showCloseButton: false, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            $('.jqx-window-modal-dark').on('click',()=>{
                $('#photo').jqxWindow('close');
            });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            QueryData();
        });

        function btnAddItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="PayQrCodeEdit?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }

        function onGridRowEdit(rowIndex) {
            var qrcode_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, "qrcode_id");
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="PayQrCodeEdit?operKey=' + g_operKey + '&qrcode_id=' + qrcode_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }

        let photo_index = 0
        function previewImage(url) {//调用写在后端
            $('#photo').jqxWindow('open');
            $("#photo").jqxWindow('setContent', `<img id='photo_img' style="width: 100%;height:auto;" src='${url.split(",")[0]}'/>`);
        }
  
    </script>

    <style>
        .photoPage{
            position:absolute;
        }
        #close_photo{
            position:absolute;
            right:30px;
            font-size:30px;
            cursor:pointer;
            border:1px solid #fff;
            background:#fff;
            padding:20px;
            color:#aaa;
            z-index:999;
        }

        .btn_add {
            width:80px;
            display: block;
            margin-right: 100px;
            float: right;
        }

        .label_name {
            line-height: 32px;
            margin-left: 10px;
        }

        .label_content {
            width: 120px;
            height: 30px;
            margin-left: 10px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }

    </style>
</head>

<body>
    <div id="divHead" style="display:block;justify-content:space-around;margin-top:20px;">
        <div><button onclick="btnAddItem_click()" class="btn_add">新增</button></div>
    </div>

    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:100%;margin-bottom:10px;"></div>

    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">账户信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

    <div id="photo" style="display:none;">
        <div style="width:100%;height:0px;background-color:#fff;"></div>
        <div style="overflow:auto;"></div>
    </div>

</body>
</html>