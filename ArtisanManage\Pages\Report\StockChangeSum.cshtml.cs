﻿
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Jint.Native;
using Jint.Parser;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using NPOI.SS.Formula.Functions;
using static Antlr4.Runtime.Atn.SemanticContext;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class StockChangeSumModel : PageQueryModel
    {
        /// <summary>
        /// 库存变化表仅用于查看数量变化，期初期末的金额是用数量变化乘以当前成本价；销售采购等过程金额是用的当时的成本价，但仅用于查看，期初期末金额与此没有关系。
        /// </summary>
        public StockChangeSumModel(CMySbCommand cmd, bool useMainDb = false) : base(Services.MenuId.stockChangeSum)
        {
            if (useMainDb) this.Database = "";
            this.cmd = cmd;
            this.PageTitle = "库存变化表";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},

            {"item_id",new DataItem(){Title="商品名称",FldArea="divHead", LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="s.item_id",DropDownWidth="300",
              SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
            {"item_brand", new DataItem(){Title = "品牌",FldArea="divHead", LabelFld = "brand_name", ButtonUsage = "list", CompareOperator="=",SqlFld="ip.item_brand",
                SqlForOptions = CommonTool.selectBrands}},
            {"item_class",new DataItem(){Title="类别",FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",MumSelectable=true,CompareOperator="like",SqlFld="ip.other_class",
                SqlForOptions=CommonTool.selectClasses}},

             {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'2',l:'加权平均价'},{v:'3',l:'预设进价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价',c:'0'}]", CompareOperator="=" }},
            {"branch_id",new DataItem(){Title="仓库",FldArea="divHead",LabelFld="branch_name",ForQuery=false,ButtonUsage="list",CompareOperator="=",SqlFld="s.branch_id",
                SqlForOptions=CommonTool.selectBranch }},
            {"branch_position",new DataItem(){Title="库位",FldArea="divHead",LabelFld="branch_position_name",Checkboxes=false,ForQuery=false,ButtonUsage="list",CompareOperator="=",
                SqlForOptions=CommonTool.selectBranchPosition }},
            {"produce_date",new DataItem(){Title="生产日期",FldArea="divHead",ForQuery=false,CtrlType="jqxDateTimeInput",ShowTime=false}},
            {"batch_no",new DataItem(){Title="批次",FldArea="divHead",ForQuery=false}},
            {"status",new DataItem(){FldArea="divHead",Title = "状态",LabelFld = "cls_status_name", LabelInDB = false, Value = "all", Label = "所有",ButtonUsage = "list", QueryOnChange = false, Hidden=true, CompareOperator = "=", NullEqualValue = "all",
     Source = @"[{v:'normal',l:'正常',condition:"" COALESCE(ip.status,'1') ='1' ""},
               {v:'stop',l:'停用',condition:""ip.status = '0' ""},
               {v:'all',l:'所有',condition:""true""}]"
 }},
            //{"bySmallUnit",new DataItem(){FldArea="divHead",Title="按小单位",CtrlType="jqxCheckBox",ForQuery=false,Value="false",AutoRemember=true}},
            {"byUnit",new DataItem(){Title="展示单位",FldArea="divHead", ButtonUsage="list",AutoRemember=true, Hidden = true, ForQuery=false, Source = "[{v:'b',l:'大单位'},{v:'s',l:'小单位'},{v:'default',l:'混合单位'}]", Value="default", Label="混合单位"}},
            {"byApproveTime",new DataItem(){FldArea="divHead",Title="按审核时间查",CtrlType="jqxCheckBox",ForQuery=false,Value="false",AutoRemember=true}},
            {"showBatch",new DataItem(){FldArea="divHead",Title="按产期批次查",CtrlType="jqxCheckBox",ForQuery=false,Value="false",Hidden = false,AutoRemember=true}},
            {"onlyNoBatch",new DataItem(){FldArea="divHead",Title="仅查无产期/批次",CtrlType="jqxCheckBox",ForQuery=false,Value="false",Hidden = false,AutoRemember=true}},
            {"HideNoTransaction",new DataItem(){FldArea="divHead",Title="隐藏无交易商品",CtrlType="jqxCheckBox",ForQuery=false,Value="false",Hidden = false,AutoRemember=true}}

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, Sortable=true,


                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"item_id",    new DataItem(){Title="商品编号",  Width="250",SqlFld="s.item_id",Hidden=true,HideOnLoad=true}},
                       {"item_name",    new DataItem(){Title="商品名称", Sortable=true,  Width="100",SqlFld="ip.item_name",Linkable=true,Pinned=true}},
                       {"item_no",    new DataItem(){Title="商品编号", Width="100",SqlFld="ip.item_no",Hidden=true}},
                       {"produce_date",new DataItem(){Title="生产日期", Width="180",GetFromDb=false,SqlFld="s.produce_date",JSCellRender="viewProduceDate"}},
                       {"batch_no",new DataItem(){Title="批次", Width="180",GetFromDb=false,SqlFld="s.batch_no"}},
                       {"s_barcode",    new DataItem(){Title="条码(小)", Sortable=true,  Width="100"}},
                       {"b_barcode",    new DataItem(){Title="条码(大)", Sortable=true,  Width="100",Hidden=true}},
                       {"item_spec",    new DataItem(){Title="规格", Width="50",Hidden=true}},
                       {"unit_no",new DataItem(){Title="单位", Width="50", FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                  {"b_unit_no",   new DataItem(){Title="大",Width="50",Linkable=true,Hidden=true,HideOnLoad=true,
                                  SqlFld="b_unit_no"}},
                                  {"m_unit_no",   new DataItem(){Title="中",Width="50",Linkable=true,Hidden=true,HideOnLoad=true,
                                  SqlFld="m_unit_no"}},
                                  {"s_unit_no",   new DataItem(){Title="小",Width="50",Linkable=true,Hidden=true,HideOnLoad=true,
                                  SqlFld="s_unit_no"}},

                                }
                            }
                       } },
                       {"start_b_qty",   new DataItem(){Title="大",Width="10%",Linkable=true,Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=@" yj_getunitqty('b',round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0)            + COALESCE (xs_add_qty, 0 )         - COALESCE (xs_reduce_qty , 0 )        + COALESCE (jh_reduce_qty, 0 )        - COALESCE (jh_add_qty, 0 )        - COALESCE (cg_add_qty , 0 )        + COALESCE (cg_reduce_qty , 0     )    - COALESCE (cr_add_qty , 0 )        + COALESCE (cr_reduce_qty , 0 )          - COALESCE (zc_add_qty , 0 )        + COALESCE (zc_reduce_qty , 0 )         - COALESCE (yk_add_qty , 0 )         + COALESCE (   yk_reduce_qty , 0 )      - COALESCE ( db_add_qty , 0 )       + COALESCE (db_reduce_qty , 0 ) 
                                                                                   - COALESCE (qc_add_qty_period , 0 )+ COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) + COALESCE (jh_reduce_qty_period, 0 ) - COALESCE (jh_add_qty_period, 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 )  - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 ) ):: NUMERIC,b_unit_factor,m_unit_factor) "
					   }},
                       {"start_m_qty",   new DataItem(){Title="中",Width="10%",Linkable=true,Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=@" yj_getunitqty('m',round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0)             + COALESCE (xs_add_qty, 0 )         - COALESCE (xs_reduce_qty , 0 )        + COALESCE (jh_reduce_qty, 0 )        - COALESCE (jh_add_qty, 0 )        - COALESCE (cg_add_qty , 0 )        + COALESCE (cg_reduce_qty , 0 )        - COALESCE (cr_add_qty , 0 )        + COALESCE (cr_reduce_qty , 0 )          - COALESCE (zc_add_qty , 0 )        + COALESCE (zc_reduce_qty , 0 )         - COALESCE (yk_add_qty , 0 )         + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 )            + COALESCE (db_reduce_qty , 0 ) 
                                                                                  - COALESCE (qc_add_qty_period , 0 )  + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) + COALESCE (jh_reduce_qty_period, 0 ) - COALESCE (jh_add_qty_period, 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 )  - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 ) ):: NUMERIC,b_unit_factor,m_unit_factor) "
					   }},
                       {"start_s_qty",   new DataItem(){Title="小",Width="50",Linkable=true,Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=@" yj_getunitqty('s',round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0)            + COALESCE (xs_add_qty, 0 )          - COALESCE (xs_reduce_qty , 0 )        + COALESCE (jh_reduce_qty, 0 )        - COALESCE (jh_add_qty, 0 )        - COALESCE (cg_add_qty , 0 )        + COALESCE (cg_reduce_qty , 0 )        - COALESCE (cr_add_qty , 0 )        + COALESCE (cr_reduce_qty , 0 )          - COALESCE (zc_add_qty , 0 )        + COALESCE (zc_reduce_qty , 0 )         - COALESCE (yk_add_qty , 0 )         + COALESCE (   yk_reduce_qty , 0 )      - COALESCE ( db_add_qty , 0 )       + COALESCE (db_reduce_qty , 0 )    
                                                                                  - COALESCE (qc_add_qty_period , 0 ) + COALESCE (xs_add_qty_period , 0 )  - COALESCE (xs_reduce_qty_period , 0 ) + COALESCE (jh_reduce_qty_period, 0 ) - COALESCE (jh_add_qty_period, 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 )  - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 ) ):: NUMERIC,b_unit_factor,m_unit_factor) "
					   }},
                       {"start_qty_unit",   new DataItem(){Title="期初数量",Width="10%",
                           /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower() == "true"?"round(COALESCE (stock_Qty - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - COALESCE (qc_add_qty_period , 0 ) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 ) - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC"
                                                                        : "yj_get_bms_qty (round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - COALESCE (qc_add_qty_period , 0 ) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 )- COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC,b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )",
                           */
                           SqlFld = this.DataItems["byUnit"].Value.ToLower() == "b" ? "round(COALESCE (stock_Qty - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - COALESCE (qc_add_qty_period , 0 ) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) + COALESCE (jh_reduce_qty_period, 0 ) - COALESCE (jh_add_qty_period, 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 ) - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                                    : this.DataItems["byUnit"].Value.ToLower() == "s" ? "round(COALESCE (stock_Qty - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - COALESCE (qc_add_qty_period , 0 ) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) + COALESCE (jh_reduce_qty_period, 0 ) - COALESCE (jh_add_qty_period, 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 ) - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC"
                                    : @"yj_get_bms_qty (round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0)            + COALESCE (xs_add_qty, 0 )         - COALESCE (xs_reduce_qty , 0 )        + COALESCE (jh_reduce_qty, 0 )        - COALESCE (jh_add_qty, 0 )        - COALESCE (cg_add_qty , 0 )        + COALESCE (cg_reduce_qty , 0 )        - COALESCE (cr_add_qty , 0 )        + COALESCE (cr_reduce_qty , 0 )        - COALESCE (zc_add_qty , 0 )        + COALESCE (zc_reduce_qty , 0 )        - COALESCE (yk_add_qty , 0 )         + COALESCE (   yk_reduce_qty , 0 )      - COALESCE ( db_add_qty , 0 )       + COALESCE (db_reduce_qty , 0 )
                                                                                   - COALESCE (qc_add_qty_period , 0 ) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) + COALESCE (jh_reduce_qty_period, 0 ) - COALESCE (jh_add_qty_period, 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 )- COALESCE (zc_add_qty_period , 0 ) + COALESCE (zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC,b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )",
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string qty = "",q="";
                               /*if (this.DataItems["bySmallUnit"].Value.ToLower() == "true")
                               {
                                    qty=sumColumnValues["start_qty"];
                                    return qty;
                               }
							   else
							   {
                                    q=sumColumnValues["start_b_qty"]; if(q!="") qty+=q+"大";
                                    q=sumColumnValues["start_m_qty"]; if(q!="") qty+=q+"中";
                                    q=sumColumnValues["start_s_qty"]; if(q!="") qty+=q+"小";
                                    return qty;
                               }*/
                                if (this.DataItems["byUnit"].Value.ToLower() == "b")
                                {
                                    qty=sumColumnValues["start_qty_b"];
                                    return qty;
                                }
                                else if (this.DataItems["byUnit"].Value.ToLower() == "s")
                                {
                                   qty=sumColumnValues["start_qty"];
                                    return qty;
                                }
                                else
                                {
                                    q=sumColumnValues["start_b_qty"]; if(q!="") qty+=q+"大";
                                    q=sumColumnValues["start_m_qty"]; if(q!="") qty+=q+"中";
                                    q=sumColumnValues["start_s_qty"]; if(q!="") qty+=q+"小";
                                    return qty;
                                }
                           }
                       }},
                       {"start_qty",   new DataItem(){Title="期初(小)",Width="10%",Hidden=true, HideOnLoad=true,ShowSum=true,
                           SqlFld="round(COALESCE (stock_Qty  + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 ) - COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - COALESCE (qc_add_qty_period , 0 ) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 )+ COALESCE (jh_reduce_qty_period, 0 ) - COALESCE (jh_add_qty_period, 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 ) - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 ):: NUMERIC,4 )"}},
                       {"start_qty_b",   new DataItem(){Title="期初(大)",Width="10%",Hidden=true, HideOnLoad=true,ShowSum=true,
                           SqlFld="round(COALESCE (stock_Qty  + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 ) - COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - COALESCE (qc_add_qty_period , 0 ) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 )+ COALESCE (jh_reduce_qty_period, 0 ) - COALESCE (jh_add_qty_period, 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 ) - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 ):: NUMERIC,4 ) / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"}},

                        {"start_amount",   new DataItem(){Title="期初金额",Hidden=true,Width="100",ShowSum=true}},

                        {"qc_qty",   new DataItem(){Title="期初录入数量",Hidden=true,Width="100",
                            /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ? "qc_add_qty_period" : "yj_get_bms_qty((qc_add_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)" }},
*/
                            SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "(qc_add_qty_period)::float8 / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                            : this.DataItems["byUnit"].Value.ToLower()=="s" ? "qc_add_qty_period"
                            : "yj_get_bms_qty((qc_add_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)" }},

                       {"buy",new DataItem(){Title="采购数量", Width="10%",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                  {"cg_add_qty_unit",   new DataItem(){Title="采",Width="50",Linkable=true,
                                  /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true"?"cg_add_qty_period" : "yj_get_bms_qty(cg_add_qty_period,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},
                                  */
                                  SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "cg_add_qty_period / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END "
                                  :  this.DataItems["byUnit"].Value.ToLower()=="s" ? "cg_add_qty_period"
                                  : "yj_get_bms_qty(cg_add_qty_period,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},

                                  {"cg_reduce_qty_unit",   new DataItem(){Title="退",Width="50",Linkable=true,
                                  /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ?"cg_reduce_qty_period" : "yj_get_bms_qty((cg_reduce_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},
                                  */
                                  SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "cg_reduce_qty_period / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END "
                                  : this.DataItems["byUnit"].Value.ToLower()=="s" ? "cg_reduce_qty_period"
                                  : "yj_get_bms_qty((cg_reduce_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},

                                }
                            }
                       } },
                        {"buy_s",new DataItem(){Title="采购(小)", Width="100",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                { 
                                  {"cg_add_qty",   new DataItem(){Title="采(小)",Width="100",Linkable=true,Hidden=true,HideOnLoad=true, 
                                  SqlFld="cg_add_qty_period"}},
                                   
                                  {"cg_reduce_qty",   new DataItem(){Title="退(小)",Width="100",Linkable=true,Hidden=true,HideOnLoad=true,
                                  SqlFld="cg_reduce_qty_period"}},

                                }
                            }
                       } },
                       {"buy_amount",new DataItem(){Title="采购金额", Width="100",Hidden=true,  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                { 

                                    {"cg_add_amount",   new DataItem(){Title="采",Width="50",Linkable=true,
                                 SqlFld="cg_add_amount_period",ShowSum=true}},


                                   {"cg_reduce_amount",   new DataItem(){Title="退",Width="50",Linkable=true,
                                 SqlFld="cg_reduce_amount_period",ShowSum=true}},



                                }
                            }
                       } },

                       {"sale",new DataItem(){Title="销售数量", Width="100",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"xs_add_qty_unit",   new DataItem(){Title="销",Width="50",Linkable=true,
                                 /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ? "xs_add_qty_period" : "yj_get_bms_qty((xs_add_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},
                                 */ 
                                 SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "xs_add_qty_period / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                                 : this.DataItems["byUnit"].Value.ToLower()=="s" ? "xs_add_qty_period "
                                 : "yj_get_bms_qty((xs_add_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},

                                   {"xs_reduce_qty_unit",   new DataItem(){Title="退",Width="50",Linkable=true,
                                 /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ? "xs_reduce_qty_period" : "yj_get_bms_qty(xs_reduce_qty_period,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},
                                 */
                                 SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "xs_reduce_qty_period / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END "
                                 : this.DataItems["byUnit"].Value.ToLower()=="s" ? "xs_reduce_qty_period "
                                 : "yj_get_bms_qty(xs_reduce_qty_period,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},

                                }
                            }
                        }},
                       {"sale_s",new DataItem(){Title="销售(小)", Width="100",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                { 
                                    {"xs_add_qty",   new DataItem(){Title="销(小)",Width="50",Linkable=true,HideOnLoad=true,
                                 SqlFld="xs_add_qty_period",Hidden=true}},
 
                                   {"xs_reduce_qty",   new DataItem(){Title="退(小)",Width="50",Linkable=true,HideOnLoad=true,
                                 SqlFld="xs_reduce_qty_period",Hidden=true}},
                                }
                            }
                       } },
                       {"borrow",new DataItem(){Title="借还", Width="100",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"jh_add_qty_unit",   new DataItem(){Title="借",Width="50",Linkable=true,
                                 SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "jh_add_qty_period / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                                 : this.DataItems["byUnit"].Value.ToLower()=="s" ? "jh_add_qty_period "
                                 : "yj_get_bms_qty((jh_add_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},

                                   {"jh_reduce_qty_unit",   new DataItem(){Title="还",Width="50",Linkable=true,
                                 SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "jh_reduce_qty_period / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END "
                                 : this.DataItems["byUnit"].Value.ToLower()=="s" ? "jh_reduce_qty_period "
                                 : "yj_get_bms_qty(jh_reduce_qty_period,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},

                                }
                            }
                        }},
                       {"borrow_s",new DataItem(){Title="借还(小)", Width="100",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                { 
                                    {"jh_add_qty",   new DataItem(){Title="借(小)",Width="50",Linkable=true,HideOnLoad=true,
                                 SqlFld="jh_add_qty_period",Hidden=true}},
 
                                   {"jh_reduce_qty",   new DataItem(){Title="还(小)",Width="50",Linkable=true,HideOnLoad=true,
                                 SqlFld="jh_reduce_qty_period",Hidden=true}},
                                }
                            }
                       } },
                        
                       {"sale_amount",new DataItem(){Title="销售金额", Width="100",Hidden=true,  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                     
                                    {"xs_add_amount",   new DataItem(){Title="销",Width="50",Linkable=true,
                                 SqlFld="xs_add_amount_period",ShowSum=true}}, 

                                   {"xs_reduce_amount",   new DataItem(){Title="退",Width="50",Linkable=true,
                                 SqlFld="xs_reduce_amount_period",ShowSum=true}},
                                    
                                }
                            }
                       } },
                       {"sale_cost_amount",new DataItem(){Title="销售成本", Width="100",Hidden=true,
                           SubColumns=new Dictionary<string,DataItem>()
                            {
                                {"xs_add_cost_period",   new DataItem(){Title="销",Width="50",Linkable=true, ShowSum=true}},
                                {"xs_reduce_cost_period",   new DataItem(){Title="退",Width="50",Linkable=true, ShowSum=true}},
                            }
                        }},

                       {"move",new DataItem(){Title="调拨数量", Width="100", Hidden=true,  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                  {"db_add_qty_unit",   new DataItem(){Title="入",Width="50",Linkable=true, 
                                   /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ? "db_add_qty_period" : "yj_get_bms_qty(db_add_qty_period,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)" }},
                                   */
                                   SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "db_add_qty_period / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                                   : this.DataItems["byUnit"].Value.ToLower()=="s" ? "db_add_qty_period"
                                   : "yj_get_bms_qty(db_add_qty_period,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)" }},
                                   {"db_reduce_qty_unit",   new DataItem(){Title="出",Width="50",Linkable=true,
                                   /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ? "db_reduce_qty_period" : "yj_get_bms_qty(db_reduce_qty_period,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},
                                 */
                                   SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "db_reduce_qty_period / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END" 
                                   : this.DataItems["byUnit"].Value.ToLower()=="s" ? "db_reduce_qty_period" 
                                   : "yj_get_bms_qty(db_reduce_qty_period,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},

                                }
                            }
                       } },
                       {"move_s",new DataItem(){Title="调拨(小)", Width="100", Hidden=true, FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                { 
                                 {"db_add_qty",   new DataItem(){Title="入",Width="50",Linkable=true,HideOnLoad=true,
                                   SqlFld="db_add_qty_period" ,Hidden=true}},

                                 {"db_reduce_qty",   new DataItem(){Title="出",Width="50",Linkable=true,HideOnLoad=true,
                                   SqlFld="db_reduce_qty_period",Hidden=true}}, 
                                }
                            }
                       } },
                       {"inventory",new DataItem(){Title="盘点数量", Width="100",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                   {"yk_add_qty_unit",   new DataItem(){Title="盈",Width="50",Linkable=true,
                                       /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ? "yk_add_qty_period" : "yj_get_bms_qty ( yk_add_qty_period::numeric, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )"}},
                                       */
                                       SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "yk_add_qty_period / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                                       : this.DataItems["byUnit"].Value.ToLower()=="s" ? "yk_add_qty_period"
                                       : "yj_get_bms_qty ( yk_add_qty_period::numeric, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )"}},

                                   {"yk_reduce_qty_unit",   new DataItem(){Title="亏",Width="50",Linkable=true, 
                                       /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ? "yk_reduce_qty_period" : "yj_get_bms_qty  ( (yk_reduce_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no, m_unit_factor,  s_unit_no)"}},
                              */
                                       SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "(yk_reduce_qty_period)::float8 / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                                       : this.DataItems["byUnit"].Value.ToLower()=="s" ? "yk_reduce_qty_period"
                                       : "yj_get_bms_qty ( (yk_reduce_qty_period)::float8, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )"}},

                                }
                            }
                       } },
                        {"inventory_s",new DataItem(){Title="盘点(小)", Width="100",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                { 
                                   {"yk_add_qty",   new DataItem(){Title="盈(小)",Width="50",Linkable=true, SqlFld="yk_add_qty_period",Hidden=true,HideOnLoad=true}},

                                   {"yk_reduce_qty",   new DataItem(){Title="亏(小)",Width="50",Linkable=true, SqlFld="yk_reduce_qty_period",Hidden=true,HideOnLoad=true}},

                                }
                            }
                       } },
                        {"stockInOut",new DataItem(){Title="出入库数量", Width="100",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                     {"cr_add_qty_unit",   new DataItem(){Title="盈",Width="50",Linkable=true,
                                   SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "cr_add_qty_period / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                                 : this.DataItems["byUnit"].Value.ToLower()=="s" ? "cr_add_qty_period "
                                 : "yj_get_bms_qty((cr_add_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},
                                     //{"cr_add_qty_unit",   new DataItem(){Title="盈",Width="50",Linkable=true, SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ?"cr_add_qty_period" : "	yj_get_bms_qty ( cr_add_qty_period::numeric, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )", ShowSum=this.DataItems["bySmallUnit"].Value.ToLower()=="true"?true:false }},

                                   {"cr_reduce_qty_unit",   new DataItem(){Title="亏",Width="50",Linkable=true, 
                                SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "cr_reduce_qty_period / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                                 : this.DataItems["byUnit"].Value.ToLower()=="s" ? "cr_reduce_qty_period "
                                 : "yj_get_bms_qty((cr_reduce_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},
                                   //{"cr_reduce_qty_unit",   new DataItem(){Title="亏",Width="50",Linkable=true, SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ?"cr_reduce_qty_period" : "yj_get_bms_qty  ( (cr_reduce_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no, m_unit_factor,  s_unit_no)", ShowSum=this.DataItems["bySmallUnit"].Value.ToLower()=="true"?true:false }},

                                }
                            }
                       } },
                        {"stockInOut_s",new DataItem(){Title="出入库(小)", Width="100",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                   {"cr_add_qty",   new DataItem(){Title="盈(小)",Width="50",Linkable=true, SqlFld="cr_add_qty_period",Hidden=true,HideOnLoad=true}},

                                   {"cr_reduce_qty",   new DataItem(){Title="亏(小)",Width="50",Linkable=true, SqlFld="cr_reduce_qty_period",Hidden=true,HideOnLoad=true}},

                                }
                            }
                       } },
                       {"combine",new DataItem(){Title="组装拆分数量", Width="100",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                     {"zc_add_qty_unit",   new DataItem(){Title="组拆入",Width="100",Linkable=true,
                                   /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ? "zc_add_qty_period" :"	yj_get_bms_qty ( zc_add_qty_period::numeric, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )"}},
*/
                                    SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "zc_add_qty_period::numeric / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                                       : this.DataItems["byUnit"].Value.ToLower()=="s" ? "zc_add_qty_period"
                                       : "yj_get_bms_qty ( zc_add_qty_period::numeric, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )"}},
                                   {"zc_reduce_qty_unit",   new DataItem(){Title="组拆出",Width="100",Linkable=true,
                                 /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ? "zc_reduce_qty_period" :"yj_get_bms_qty  ( (zc_reduce_qty_period)::float8,b_unit_no,b_unit_factor,m_unit_no, m_unit_factor,  s_unit_no)"}},
                                  */ 
                                 SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "zc_reduce_qty_period::float8 / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                                       : this.DataItems["byUnit"].Value.ToLower()=="s" ? "zc_reduce_qty_period"
                                       : "yj_get_bms_qty ( zc_reduce_qty_period::float8, b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )"}},
                                }
                            }
                       } },

                       {"combine_s",new DataItem(){Title="组装拆分(小)", Width="100",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                { 
                                    {"zc_add_qty",   new DataItem(){Title="组拆入(小)",Width="100",Linkable=true,
                                   SqlFld="zc_add_qty_period",Hidden=true,HideOnLoad=true}},

                                   {"zc_reduce_qty",   new DataItem(){Title="组拆出(小)",Width="100",Linkable=true,
                                     SqlFld="zc_reduce_qty_period",Hidden=true,HideOnLoad=true}},

                                }
                            }
                       } },

                      {"end_qty_unit", new DataItem(){Title="期末数量",Width="80",Linkable=true,
                           /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ? "COALESCE (stock_Qty  - coalesce(qc_add_qty,0)  + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC"
                                                                       : " yj_get_bms_qty (COALESCE (stock_Qty  - coalesce(qc_add_qty,0)  + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC, b_unit_no, b_unit_factor,m_unit_no,m_unit_factor,s_unit_no ) ",
                           */
                          SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0)  + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC,4)::numeric / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                          : this.DataItems["byUnit"].Value.ToLower()=="s" ? "round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0)  + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC,4)::numeric"
                          : " yj_get_bms_qty (COALESCE (stock_Qty  - coalesce(qc_add_qty,0)  + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC, b_unit_no, b_unit_factor,m_unit_no,m_unit_factor,s_unit_no ) ",

                          FuncGetSumValue = (sumColumnValues) =>
                           {
                               string qty = "",q="";
							   /*if (this.DataItems["bySmallUnit"].Value.ToLower() == "true")
							   {
                                    qty=sumColumnValues["end_qty"];
                                    return qty;
                               }
							   else
							   {
                                   q=sumColumnValues["end_b_qty"]; if(q!="") qty+=q+"大";
                                   q=sumColumnValues["end_m_qty"]; if(q!="") qty+=q+"中";
                                   q=sumColumnValues["end_s_qty"]; if(q!="") qty+=q+"小";
                                   return qty;
                               }*/
                               if (this.DataItems["byUnit"].Value.ToLower() == "b")
                               {
                                    qty=sumColumnValues["end_qty_b"];
                                    return qty;
                               }
                               else if (this.DataItems["byUnit"].Value.ToLower() == "s")
                               {
                                    qty=sumColumnValues["end_qty"];
                                    return qty;
                               }
                               else
                               {
                                   q=sumColumnValues["end_b_qty"]; if(q!="") qty+=q+"大";
                                   q=sumColumnValues["end_m_qty"]; if(q!="") qty+=q+"中";
                                   q=sumColumnValues["end_s_qty"]; if(q!="") qty+=q+"小";
                                   return qty;
                               }
                           }
                       }},
                       {"end_b_qty",   new DataItem(){Title="大",Width="80",Linkable=true,Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=" yj_getunitqty('b',COALESCE (stock_Qty  - coalesce(qc_add_qty,0)  + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC,b_unit_factor,m_unit_factor) "
                       }},                                                                                                                                        
                       {"end_m_qty",   new DataItem(){Title="中",Width="80",Linkable=true,Hidden=true,HideOnLoad=true,ShowSum=true,                                
                           SqlFld=" yj_getunitqty('m',COALESCE (stock_Qty  - coalesce(qc_add_qty,0)  + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC,b_unit_factor,m_unit_factor) "
                       }},                                                                                                                                         
                       {"end_s_qty",   new DataItem(){Title="小",Width="80",Linkable=true,Hidden=true,HideOnLoad=true,ShowSum=true,                               
                           SqlFld=" yj_getunitqty('s',COALESCE (stock_Qty  - coalesce(qc_add_qty,0)  + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC,b_unit_factor,m_unit_factor) "
                       }},


                       {"end_qty",   new DataItem(){Title="期末(小)",Width="80",Linkable=true,Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=" COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC "
                       }},
                       {"end_qty_b",   new DataItem(){Title="期末(大)",Width="80",Linkable=true,Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=" COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC  / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                       }},
                       {"end_amount",   new DataItem(){Title="期末金额",Hidden=true, Width="10",ShowSum=true}},
                     },

                     QueryFromSQL=@"
FROM
(
    SELECT item_id, sum(stock_qty) as stock_Qty ~VAR_produceDateSql from stock m
    left join (select batch_id,produce_date,batch_no from info_item_batch where company_id = ~COMPANY_ID) itb on itb.batch_id = m.batch_id 
LEFT JOIN (select branch_position_name,branch_position branch_position_f from info_branch_position where company_id = ~COMPANY_ID) ibp on ibp.branch_position_f = m.branch_position
    WHERE company_id = ~COMPANY_ID ~VAR_stock_branch_id ~VAR_branch_position ~VAR_produce_date ~VAR_batch_no GROUP BY item_id ~VAR_showBatchGroupSql
)  s

LEFT JOIN
(
	select d.item_id, 
    sum(CASE WHEN m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN (case when red_sheet_id is not null then -1 else 1 end)*quantity * unit_factor ELSE 0 END ) AS qc_add_qty_period,
	sum(CASE WHEN m.~VAR_time > '~VAR_endDay' THEN (case when red_sheet_id is not null then -1 else 1 end)*quantity * unit_factor ELSE 0 END ) AS qc_add_qty ~VAR_produceDateSql
    from sheet_stock_opening_detail d
    left join sheet_stock_opening_main m on d.company_id=m.company_id and d.sheet_id=m.sheet_id
    left join (select batch_id,produce_date,batch_no from info_item_batch where company_id = ~COMPANY_ID) itb on itb.batch_id = d.batch_id
    LEFT JOIN (select branch_position_name,branch_position as branch_position_f  from info_branch_position where company_id = ~COMPANY_ID) ibp on ibp.branch_position_f = d.branch_position
    where d.company_id=~COMPANY_ID and m.red_flag is null and m.~VAR_time >= '~VAR_startDay' and m.~VAR_time <= '~VAR_endDay' ~VAR_branch_id ~VAR_branch_position  ~VAR_produce_date ~VAR_batch_no
    group by d.item_id,coalesce(d.branch_id,m.branch_id) ~VAR_showBatchGroupSql

) qc ON s.item_id = qc.item_id ~VAR_qcSql

LEFT JOIN 
(
	SELECT
		d.item_id AS item_id,
		sum(CASE WHEN inout_flag*quantity < 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN (inout_flag * quantity * unit_factor * (-1))::float4 ELSE 0 END ) AS xs_add_qty_period,
		sum(CASE WHEN inout_flag*quantity > 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN (inout_flag * quantity * unit_factor)::float4 ELSE 0 END ) AS xs_reduce_qty_period,
		sum(CASE WHEN inout_flag*quantity < 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN round((inout_flag * quantity * real_price * (-1))::numeric,2) ELSE 0 END ) AS xs_add_amount_period,
		sum(CASE WHEN inout_flag*quantity > 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN round((inout_flag * quantity * real_price)::numeric,2)  ELSE 0 END ) AS xs_reduce_amount_period,
        sum(CASE WHEN inout_flag*quantity < 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' and COALESCE(d.trade_type,'') !='J' THEN (inout_flag * quantity * unit_factor * ~VAR_COST_TYPE * (-1))::float4 ELSE 0 END ) AS xs_add_cost_period,
		sum(CASE WHEN inout_flag*quantity > 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' and COALESCE(d.trade_type,'') !='J' THEN (inout_flag * quantity * unit_factor * ~VAR_COST_TYPE)::float4 ELSE 0 END ) AS xs_reduce_cost_period,
	    sum(CASE WHEN inout_flag*quantity < 0 AND m.~VAR_time > '~VAR_endDay' THEN (inout_flag * quantity * unit_factor * (-1))::float4 ELSE 0 END ) AS xs_add_qty,
		sum(CASE WHEN inout_flag*quantity > 0 AND m.~VAR_time > '~VAR_endDay' THEN (inout_flag * quantity * unit_factor)::float4 ELSE 0 END ) AS xs_reduce_qty ~VAR_produceDateSql
	FROM
		sheet_sale_detail d
	LEFT JOIN sheet_sale_main M ON d.sheet_id = M.sheet_id and d.company_id =m.company_id
    LEFT JOIN (select batch_id,produce_date,batch_no from info_item_batch where company_id = ~COMPANY_ID) itb on itb.batch_id = d.batch_id
    LEFT JOIN (select branch_position_name,branch_position as branch_position_f  from info_branch_position where company_id = ~COMPANY_ID) ibp on ibp.branch_position_f = d.branch_position
	WHERE
		red_flag IS NULL and d.happen_time>='~VAR_min_happen_time' and m.happen_time>='~VAR_min_happen_time'
		AND M.approve_time IS NOT NULL  and d.company_id=~COMPANY_ID  and NOT COALESCE ( M.is_imported, FALSE )  ~VAR_branch_id ~VAR_branch_position  ~VAR_produce_date ~VAR_batch_no 
	GROUP BY
		d.item_id ~VAR_showBatchGroupSql

) xs ON s.item_id = xs.item_id  ~VAR_xsSql

LEFT JOIN 
(
	SELECT
		d.item_id AS item_id,
		sum(CASE WHEN inout_flag*quantity > 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN (inout_flag * quantity * unit_factor)::float4 ELSE 0 END )AS jh_add_qty_period,
		sum(CASE WHEN inout_flag*quantity < 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN (inout_flag * quantity * unit_factor  * (-1))::float4 ELSE 0 END )AS jh_reduce_qty_period,
		sum(CASE WHEN inout_flag*quantity > 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN round((inout_flag * quantity * real_price)::numeric,2) ELSE 0 END ) AS jh_add_amount_period,
		sum(CASE WHEN inout_flag*quantity < 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN round((inout_flag * quantity * real_price  * (-1))::numeric,2)  ELSE 0 END ) AS jh_reduce_amount_period,
	    sum(CASE WHEN inout_flag*quantity > 0 AND m.~VAR_time > '~VAR_endDay' THEN (inout_flag * quantity * unit_factor )::float4 ELSE 0 END ) AS jh_add_qty,
		sum(CASE WHEN inout_flag*quantity < 0 AND m.~VAR_time > '~VAR_endDay' THEN (inout_flag * quantity * unit_factor * (-1))::float4 ELSE 0 END ) AS jh_reduce_qty ~VAR_produceDateSql
	FROM
		borrow_item_detail d
	LEFT JOIN borrow_item_main M ON d.sheet_id = M.sheet_id and d.company_id =m.company_id
    LEFT JOIN (select batch_id,produce_date,batch_no from info_item_batch where company_id = ~COMPANY_ID) itb on itb.batch_id = d.batch_id
    LEFT JOIN (select branch_position_name,branch_position as branch_position_f  from info_branch_position where company_id = ~COMPANY_ID) ibp on ibp.branch_position_f = d.branch_position
	WHERE
		red_flag IS NULL and d.happen_time>='~VAR_min_happen_time' and m.happen_time>='~VAR_min_happen_time'
		AND M.approve_time IS NOT NULL  and d.company_id=~COMPANY_ID  and NOT COALESCE ( M.is_imported, FALSE )  ~VAR_branch_id ~VAR_branch_position  ~VAR_produce_date ~VAR_batch_no 
	GROUP BY
		d.item_id ~VAR_showBatchGroupSql

) jh ON s.item_id = jh.item_id  ~VAR_jhSql         
LEFT JOIN
(
	SELECT
		d.item_id AS item_id,
		 sum(CASE WHEN inout_flag*quantity > 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN (inout_flag * quantity * unit_factor)::float4 ELSE 0 END ) AS cg_add_qty_period,
		 sum(CASE WHEN inout_flag*quantity < 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN (inout_flag * quantity * unit_factor * (-1))::float4 ELSE 0 END ) AS cg_reduce_qty_period,
		 sum(CASE WHEN inout_flag*quantity > 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN round((inout_flag * d.sub_amount)::numeric,2) ELSE 0 END ) AS cg_add_amount_period,
		 sum(CASE WHEN inout_flag*quantity < 0 AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN round((inout_flag * d.sub_amount * (-1))::numeric,2) ELSE 0 END ) AS cg_reduce_amount_period,
		 sum(CASE WHEN inout_flag*quantity > 0 AND m.~VAR_time > '~VAR_endDay' THEN (inout_flag * quantity * unit_factor)::float4 ELSE 0 END ) AS cg_add_qty,
		 sum(CASE WHEN inout_flag*quantity < 0 AND m.~VAR_time > '~VAR_endDay' THEN (inout_flag * quantity * unit_factor* (-1))::float4 ELSE 0 END ) AS cg_reduce_qty ~VAR_produceDateSql
	FROM
		sheet_buy_detail d
	LEFT JOIN sheet_buy_main M ON d.sheet_id = M.sheet_id and d.company_id =m.company_id 
    LEFT JOIN (select batch_id,produce_date,batch_no from info_item_batch where company_id = ~COMPANY_ID) itb on itb.batch_id = d.batch_id
    LEFT JOIN (select branch_position_name,branch_position branch_position_f from info_branch_position where company_id = ~COMPANY_ID) ibp on ibp.branch_position_f = d.branch_position
	WHERE
		red_flag IS NULL and d.happen_time>='~VAR_min_happen_time' and m.happen_time>='~VAR_min_happen_time'
		AND M.approve_time IS NOT NULL  and d.company_id=~COMPANY_ID and NOT COALESCE ( M.is_imported, FALSE )      ~VAR_branch_id ~VAR_branch_position  ~VAR_produce_date ~VAR_batch_no 
	GROUP BY
		d.item_id ~VAR_showBatchGroupSql

) cg ON s.item_id = cg.item_id ~VAR_cgSql
LEFT JOIN 
(
	SELECT
		d.item_id AS item_id,
		 sum(CASE WHEN inout_flag*quantity > 0 ~VAR_combine_to_branch_id AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN (inout_flag * quantity * unit_factor)::float4 ELSE 0 END ) AS zc_add_qty_period,
		 sum(CASE WHEN inout_flag*quantity < 0 ~VAR_combine_from_branch_id AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN (inout_flag * quantity * unit_factor * (-1))::float4 ELSE 0 END ) AS zc_reduce_qty_period,
		 sum(CASE WHEN inout_flag*quantity > 0 ~VAR_combine_to_branch_id AND m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN round((cost_amount)::numeric,2) ELSE 0 END ) AS zc_add_amount_period,
		 sum(CASE WHEN inout_flag*quantity < 0 ~VAR_combine_from_branch_id and m.~VAR_time >= '~VAR_startDay' AND m.~VAR_time <= '~VAR_endDay' THEN round((cost_amount * (-1))::numeric,2) ELSE 0 END ) AS zc_reduce_amount_period,
		 sum(CASE WHEN inout_flag*quantity > 0 ~VAR_combine_to_branch_id AND m.~VAR_time > '~VAR_endDay' THEN (inout_flag * quantity * unit_factor)::float4 ELSE 0 END ) AS zc_add_qty,
		 sum(CASE WHEN inout_flag*quantity < 0 ~VAR_combine_from_branch_id AND m.~VAR_time > '~VAR_endDay' THEN (inout_flag * quantity * unit_factor* (-1))::float4 ELSE 0 END ) AS zc_reduce_qty ~VAR_produceDateSql
	FROM sheet_combine_detail d
	LEFT JOIN sheet_combine_main M ON d.sheet_id = M.sheet_id and d.company_id =m.company_id 
    left join (select batch_id,produce_date,batch_no from info_item_batch where company_id = ~COMPANY_ID) itb on itb.batch_id = d.batch_id
    LEFT JOIN (select branch_position_name,branch_position branch_position_f from info_branch_position where company_id = ~COMPANY_ID) ibp on ibp.branch_position_f = d.branch_position
	WHERE
        d.happen_time>='~VAR_min_happen_time' and m.happen_time>='~VAR_min_happen_time' and
		red_flag IS NULL AND M.approve_time IS NOT NULL  and d.company_id=~COMPANY_ID  ~VAR_combine_branch_id ~VAR_branch_position  ~VAR_produce_date ~VAR_batch_no 
	GROUP BY
		d.item_id ~VAR_showBatchGroupSql

) zc ON s.item_id = zc.item_id ~VAR_zcSql
LEFT JOIN 
(
	SELECT
		d.item_id AS item_id,
		 sum(CASE WHEN inout_flag*quantity > 0 AND M.~VAR_time >= '~VAR_startDay' AND M.~VAR_time <= '~VAR_endDay' THEN (inout_flag*quantity * unit_factor)::float4 ELSE 0 END ) AS yk_add_qty_period,
		 sum(CASE WHEN inout_flag*quantity < 0 AND M.~VAR_time >= '~VAR_startDay' AND M.~VAR_time <= '~VAR_endDay' THEN (inout_flag*quantity * unit_factor*(-1))::float4 ELSE 0 END ) AS yk_reduce_qty_period,
		 sum(CASE WHEN inout_flag*quantity > 0 AND M.~VAR_time > '~VAR_endDay' THEN inout_flag * quantity * unit_factor ELSE 0 END ) AS yk_add_qty,
		 sum(CASE WHEN inout_flag*quantity < 0 AND M.~VAR_time > '~VAR_endDay' THEN inout_flag * quantity * unit_factor* (-1) ELSE 0 END ) AS yk_reduce_qty ~VAR_produceDateSql
	FROM
		sheet_invent_change_detail d
		LEFT JOIN sheet_invent_change_main M ON d.sheet_id = M.sheet_id and d.company_id =m.company_id
 left join (select batch_id,produce_date,batch_no from info_item_batch where company_id = ~COMPANY_ID) itb on itb.batch_id = d.batch_id
LEFT JOIN (select branch_position_name,branch_position branch_position_f from info_branch_position where company_id = ~COMPANY_ID) ibp on ibp.branch_position_f = d.branch_position
	WHERE
        d.happen_time>='~VAR_min_happen_time' and m.happen_time>='~VAR_min_happen_time' and
		red_flag IS NULL AND M.approve_time IS NOT NULL and d.company_id=~COMPANY_ID and M.~VAR_time >= '~VAR_startDay'  and m.~VAR_time>= '~VAR_startDay' ~VAR_branch_id ~VAR_branch_position  ~VAR_produce_date ~VAR_batch_no 
	GROUP BY
		d.item_id ~VAR_showBatchGroupSql

) yk ON s.item_id = yk.item_id ~VAR_ykSql
LEFT JOIN 
(
	SELECT
		d.item_id AS item_id,
		 sum(CASE WHEN inout_flag*quantity > 0 AND M.~VAR_time >= '~VAR_startDay' AND M.~VAR_time <= '~VAR_endDay' THEN (inout_flag*quantity * unit_factor)::float4 ELSE 0 END ) AS cr_add_qty_period,
		 sum(CASE WHEN inout_flag*quantity < 0 AND M.~VAR_time >= '~VAR_startDay' AND M.~VAR_time <= '~VAR_endDay' THEN (inout_flag*quantity * unit_factor*(-1))::float4 ELSE 0 END ) AS cr_reduce_qty_period,
		 sum(CASE WHEN inout_flag*quantity > 0 AND M.~VAR_time > '~VAR_endDay' THEN inout_flag * quantity * unit_factor ELSE 0 END ) AS cr_add_qty,
		 sum(CASE WHEN inout_flag*quantity < 0 AND M.~VAR_time > '~VAR_endDay' THEN inout_flag * quantity * unit_factor* (-1) ELSE 0 END ) AS cr_reduce_qty ~VAR_produceDateSql
	FROM
		sheet_stock_in_out_detail d
    LEFT JOIN sheet_stock_in_out_main M ON d.sheet_id = M.sheet_id and d.company_id =m.company_id
    LEFT JOIN (select batch_id,produce_date,batch_no from info_item_batch where company_id = ~COMPANY_ID) itb on itb.batch_id = d.batch_id
    LEFT JOIN (select branch_position_name,branch_position branch_position_f from info_branch_position where company_id = ~COMPANY_ID) ibp on ibp.branch_position_f = d.branch_position
	WHERE
        d.happen_time>='~VAR_min_happen_time' and m.happen_time>='~VAR_min_happen_time' and
		red_flag IS NULL AND M.approve_time IS NOT NULL and d.company_id=~COMPANY_ID and M.~VAR_time >= '~VAR_startDay'  and m.~VAR_time>= '~VAR_startDay' ~VAR_branch_id ~VAR_branch_position  ~VAR_produce_date ~VAR_batch_no 
	GROUP BY
		d.item_id ~VAR_showBatchGroupSql

) cr ON s.item_id = cr.item_id ~VAR_crSql

LEFT JOIN 
(
	SELECT
		d.item_id AS item_id,
		 sum(CASE WHEN M.~VAR_time >= '~VAR_startDay' AND M.~VAR_time <= '~VAR_endDay' THEN quantity * unit_factor ELSE 0 END ) AS db_add_qty_period,
		 sum(CASE WHEN M.~VAR_time > '~VAR_endDay' THEN quantity * unit_factor ELSE 0 END ) AS db_add_qty ~VAR_produceDateSql
	FROM
		sheet_move_detail d
		LEFT JOIN sheet_move_main M ON d.sheet_id = M.sheet_id and d.company_id =m.company_id  AND not coalesce(M.is_imported,false)
    left join (select batch_id,produce_date,batch_no from info_item_batch where company_id = ~COMPANY_ID) itb on itb.batch_id = d.batch_id
    LEFT JOIN (select branch_position_name,branch_position from info_branch_position where company_id = ~COMPANY_ID) ibp on ibp.branch_position = d.to_branch_position

	WHERE
        d.happen_time>='~VAR_min_happen_time' and m.happen_time>='~VAR_min_happen_time' and
		red_flag IS NULL AND M.approve_time IS NOT NULL and d.company_id=~COMPANY_ID and M.~VAR_time >= '~VAR_startDay'  and m.~VAR_time>= '~VAR_startDay' ~VAR_to_branch_id ~VAR_branch_position  ~VAR_produce_date ~VAR_batch_no 
	GROUP BY
		d.item_id ~VAR_showBatchGroupSql
) dr ON s.item_id = dr.item_id ~VAR_drSql

LEFT JOIN 
(
	SELECT
		d.item_id AS item_id,
		 sum(CASE WHEN M.~VAR_time >= '~VAR_startDay' AND M.~VAR_time <= '~VAR_endDay' THEN quantity * unit_factor ELSE 0 END ) AS db_reduce_qty_period,
		 sum(CASE WHEN M.~VAR_time > '~VAR_endDay' THEN quantity * unit_factor ELSE 0 END ) AS db_reduce_qty ~VAR_produceDateSql
	FROM
	sheet_move_detail d
	LEFT JOIN sheet_move_main M ON d.sheet_id = M.sheet_id and d.company_id =m.company_id 	AND not coalesce(M.is_imported,false)
    LEFT JOIN (select batch_id,produce_date,batch_no from info_item_batch where company_id = ~COMPANY_ID) itb on itb.batch_id = d.batch_id
    LEFT JOIN (select branch_position_name,branch_position from info_branch_position where company_id = ~COMPANY_ID) ibp on ibp.branch_position = d.from_branch_position
	WHERE
        d.happen_time>='~VAR_min_happen_time' and m.happen_time>='~VAR_min_happen_time' and
		red_flag IS NULL AND M.approve_time IS NOT NULL and d.company_id=~COMPANY_ID and M.~VAR_time >= '~VAR_startDay'  and m.~VAR_time>= '~VAR_startDay'  ~VAR_from_branch_id ~VAR_branch_position  ~VAR_produce_date ~VAR_batch_no 
	GROUP BY
		d.item_id ~VAR_showBatchGroupSql

) dc ON s.item_id = dc.item_id ~VAR_dcSql
	
LEFT JOIN info_item_prop ip ON s.item_id = ip.item_id AND ip.company_id=~COMPANY_ID 
LEFT JOIN info_item_multi_unit mu on s.item_id=mu.item_id and mu.unit_type='s' and mu.company_id=~COMPANY_ID		
LEFT JOIN
(
	SELECT
		item_id,
		s ->> 'f1' s_unit_no,                                          s ->> 'f3' s_barcode,
		m ->> 'f1' m_unit_no, ( m ->> 'f2' ) :: NUMERIC m_unit_factor, m ->> 'f3' m_barcode,
		b ->> 'f1' b_unit_no, ( b ->> 'f2' ) :: NUMERIC b_unit_factor, b ->> 'f3' b_barcode 
		 
	FROM
		crosstab ( 'select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,wholesale_price,retail_price)) as json from info_item_multi_unit where company_id=~COMPANY_ID ORDER BY item_id',$$ VALUES ( 's' :: TEXT ), ( 'm' :: TEXT ), ( 'b' :: TEXT ) $$ ) AS errr ( item_id INT, s jsonb, M jsonb, b jsonb ) 

) T ON ip.item_id = T.item_id
 where ip.company_id=~COMPANY_ID ~VAR_HideNoTransactionSql
",

                     QueryGroupBySQL = "",
                     QueryOrderSQL=""
                  }
                }
            };
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {

            var costPrice = "mu.buy_price";//当前进价
            var cost_price_type = DataItems["cost_price_type"].Value;
            var recentPriceTime = "0";
            if (cost_price_type == "4")
            {
                string sql = $@"SELECT replace((setting->'recentPriceTime')::text,'""','') recentPriceTime FROM company_setting where company_id = {company_id}";

                dynamic re = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (re != null) recentPriceTime = re.recentpricetime;
            }
            SQLVariables["COST_TYPE"] = "cost_price_buy";
            switch (cost_price_type)
            {
                case "4"://最近平均进价
                    if(recentPriceTime!="0") costPrice = $"(ip.cost_price_recent->'avg{recentPriceTime}')::numeric";
                    SQLVariables["COST_TYPE"] = "cost_price_recent";
                    break;
                case "3"://预设进价
                    costPrice = "mu.buy_price";
                    SQLVariables["COST_TYPE"] = "cost_price_buy";
                    break;
                case "2"://加权价
                    costPrice = "ip.cost_price_avg";
                    SQLVariables["COST_TYPE"] = "cost_price_avg";
                    break;
                case "1"://预设成本
                    costPrice = "mu.cost_price_spec";
                    SQLVariables["COST_TYPE"] = "cost_price_prop";
                    break;
            }

            var columns = Grids.GetValueOrDefault("gridItems").Columns;

            columns["end_amount"].SqlFld = $@" round((COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 ) - COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 ) - COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 )- COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 )*{costPrice})::numeric,2) ";
            columns["start_amount"].SqlFld = $@" round((COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (   cr_reduce_qty , 0 )- COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - coalesce(qc_add_qty_period,0) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 )- COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )*{costPrice})::numeric,2) ";


            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;

            SQLVariables["min_happen_time"] = DataItems["startDay"].Value;
            if (DataItems["byApproveTime"].Value.ToLower() == "true")
            {
                SQLVariables["time"] = "approve_time";
                string startDay = DataItems["startDay"].Value; 
                SQLVariables["min_happen_time"] = CPubVars.GetDateText(Convert.ToDateTime(startDay).AddMonths(-3));
            }
            else
            {
                SQLVariables["time"] = "happen_time";

            }


            /*if (DataItems["bySmallUnit"].Value.ToLower() == "true")
            {
                this.Grids["gridItems"].Columns["start_qty_unit"].SqlFld = this.DataItems["bySmallUnit"].Value.ToLower() == "true" ? "round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - coalesce(qc_add_qty_period,0) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 ) - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC"
                                                 : "yj_get_bms_qty (round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - coalesce(qc_add_qty_period,0) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 ) - COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 )- COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC,b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )";
                
                this.Grids["gridItems"].Columns["end_qty_unit"].SqlFld = this.DataItems["bySmallUnit"].Value.ToLower() == "true" ? "COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC"
                                                 : " yj_get_bms_qty (COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 ) - COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC, b_unit_no, b_unit_factor,m_unit_no,m_unit_factor,s_unit_no ) ";
                 
            }*/
            //if (DataItems["byUnit"].Value == "")    
            //{
            //    if (DataItems["bySmallUnit"].Value.ToLower() == "true")
            //    {
            //        DataItems["byUnit"].Value = "s";
            //        DataItems["byUnit"].Label = "小单位";
            //    }
            //    else
            //    {
            //        DataItems["byUnit"].Value = "default";
            //        DataItems["byUnit"].Label = "混合单位";
            //    }
            //}
            this.Grids["gridItems"].Columns["start_qty_unit"].SqlFld = this.DataItems["byUnit"].Value.ToLower() == "b" ? "round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - coalesce(qc_add_qty_period,0) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) + COALESCE (jh_reduce_qty_period, 0 ) - COALESCE (jh_add_qty_period, 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 ) - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC  / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                : this.DataItems["byUnit"].Value.ToLower() == "s" ? "round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - coalesce(qc_add_qty_period,0) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 ) + COALESCE (jh_reduce_qty_period, 0 ) - COALESCE (jh_add_qty_period, 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 ) - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC"
                : "yj_get_bms_qty (round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - coalesce(qc_add_qty_period,0) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) + COALESCE (jh_reduce_qty_period, 0 ) - COALESCE (jh_add_qty_period, 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 ) - COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 )- COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC,b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )";

            this.Grids["gridItems"].Columns["end_qty_unit"].SqlFld = this.DataItems["byUnit"].Value.ToLower() == "b" ? "(COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC)  / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                : this.DataItems["byUnit"].Value.ToLower() == "s" ? "COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC"
                : " yj_get_bms_qty (COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) + COALESCE (jh_reduce_qty, 0 ) - COALESCE (jh_add_qty, 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 ) - COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC, b_unit_no, b_unit_factor,m_unit_no,m_unit_factor,s_unit_no ) ";



            if (DataItems["branch_id"].Value != "")
            {
				SQLVariables["combine_from_branch_id"] = $" and from_branch_id ={ DataItems["branch_id"].Value} ";
				SQLVariables["combine_to_branch_id"] = $" and to_branch_id ={DataItems["branch_id"].Value} ";

				SQLVariables["combine_branch_id"] = "and (from_branch_id =" + DataItems["branch_id"].Value + " or to_branch_id = " + DataItems["branch_id"].Value + ")";
                SQLVariables["stock_branch_id"] = "and m.branch_id =" + DataItems["branch_id"].Value;
                SQLVariables["branch_id"] = "and COALESCE(d.branch_id,m.branch_id) =" + DataItems["branch_id"].Value;
                SQLVariables["from_branch_id"] = "and from_branch_id =" + DataItems["branch_id"].Value;
                SQLVariables["to_branch_id"] = "and to_branch_id =" + DataItems["branch_id"].Value;
            }
            else
            {
				
			    SQLVariables["combine_from_branch_id"] = " ";
				SQLVariables["combine_to_branch_id"] = " ";
				SQLVariables["combine_branch_id"] = " ";
                SQLVariables["branch_id"] = " ";
                SQLVariables["stock_branch_id"] = "";
                SQLVariables["from_branch_id"] = " ";
                SQLVariables["to_branch_id"] = " ";
            }
            string branchPositionStr = DataItems["branch_position"].Label;

            string branchs_position = "";
            if (branchPositionStr != "")
            {
                var branchPositionList = branchPositionStr.Split(',');
                foreach (var branchPosition in branchPositionList)
                {
                    if (branchs_position == "")
                    {
                        if (branchPosition == "默认库位")
                        {
                            branchs_position += $@" and (branch_position = 0";
                            continue;
                        }
                        branchs_position = $@" and (branch_position_name ='{branchPosition}'";
                    }
                    else
                    {
                        if (branchPosition == "默认库位")
                        {
                            branchs_position += $@" or branch_position = 0";
                            continue;
                        }
                        branchs_position += $@" or branch_position_name ='{branchPosition}'";
                    }
                }
                if (branchs_position != "") branchs_position += ")";
                SQLVariables["branch_position"] = branchs_position;
            }
            else
            {
                SQLVariables["branch_position"] = $@"";

            }
            if (DataItems["produce_date"].Value != "")
            {
                SQLVariables["produce_date"] = $@" and produce_date = '{DataItems["produce_date"].Value}'";
            }
            else
            {
                SQLVariables["produce_date"] = "";
            }
            if (DataItems["batch_no"].Value != "")
            {
                SQLVariables["batch_no"] = $@" and batch_no = '{DataItems["batch_no"].Value}'";
            }
            else
            {
                SQLVariables["batch_no"] = "";
            }
            if(DataItems["onlyNoBatch"].Value.ToLower() == "true")
            {
                SQLVariables["produce_date"] = $@" and produce_date is null";
                SQLVariables["batch_no"] = $@" and batch_no is null";
            }
            string showBatchGroupSql = "";
            string produceDateSql = "";
            string qcSql = "";
            string xsSql = "";
            string jhSql = "";
            string cgSql = "";
            string ykSql = "";
            string crSql = "";
            string zcSql = "";
            string drSql = "";
            string dcSql = "";
            if (DataItems["showBatch"].Value.ToLower() == "true")
            {
                showBatchGroupSql = ",produce_date,batch_no,itb.batch_id";
                produceDateSql = ",Substring(COALESCE(produce_date::text,''),1,10) as produce_date,COALESCE(batch_no,'') as batch_no,COALESCE(itb.batch_id,0) AS batch_id";
                qcSql = " and qc.batch_id = s.batch_id";
                xsSql = " and xs.batch_id = s.batch_id";
                jhSql = " and jh.batch_id = s.batch_id";
                cgSql = " and cg.batch_id = s.batch_id";
                ykSql = " and yk.batch_id = s.batch_id";
                crSql = " and cr.batch_id = s.batch_id";
                zcSql = " and zc.batch_id = s.batch_id";
                drSql = " and dr.batch_id = s.batch_id";
                dcSql = " and dc.batch_id = s.batch_id";
                this.Grids["gridItems"].Columns["produce_date"].GetFromDb = true;
                this.Grids["gridItems"].Columns["batch_no"].GetFromDb = true;
                this.Grids["gridItems"].Columns["produce_date"].Hidden = false;
                this.Grids["gridItems"].Columns["batch_no"].Hidden = false;
            }
            else
            {
                this.Grids["gridItems"].Columns["produce_date"].Hidden = true;
                this.Grids["gridItems"].Columns["batch_no"].Hidden = true;
                this.Grids["gridItems"].Columns["produce_date"].GetFromDb = false;
                this.Grids["gridItems"].Columns["batch_no"].GetFromDb = false;
                
            }
            SQLVariables["showBatchGroupSql"] = showBatchGroupSql;
            SQLVariables["produceDateSql"] = produceDateSql;
            SQLVariables["qcSql"] = qcSql;
            SQLVariables["xsSql"] = xsSql;
            SQLVariables["jhSql"] = jhSql;
            SQLVariables["cgSql"] = cgSql;
            SQLVariables["ykSql"] = ykSql;
            SQLVariables["crSql"] = crSql;
            SQLVariables["zcSql"] = zcSql;
            SQLVariables["drSql"] = drSql;
            SQLVariables["dcSql"] = dcSql;
            string HideNoTransactionSql = "";
            if (DataItems["HideNoTransaction"].Value.ToLower() == "true")
            {
                HideNoTransactionSql = @" AND( COALESCE(xs_add_qty_period,0)<>0 or COALESCE(xs_reduce_qty_period,0)<> 0 
                                        or COALESCE(cg_add_qty_period,0)<> 0 or COALESCE(cg_reduce_qty_period,0)<>0 
                                        or COALESCE(cr_add_qty_period,0)<>0 or COALESCE(cr_reduce_qty_period,0)<>0 
                                        or COALESCE(zc_add_qty_period,0)<>0 or COALESCE(zc_reduce_qty_period,0)<>0
                                        or COALESCE(yk_add_qty_period,0)<>0 or COALESCE(yk_reduce_qty_period,0)<>0
                                        or COALESCE(db_add_qty_period,0)<>0 or COALESCE(db_reduce_qty_period,0)<>0)";
            }
            SQLVariables["HideNoTransactionSql"] = HideNoTransactionSql;
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            var costPriceType = "3";
            var costPriceTypeName = "预设进价";
            if (JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            var columns = Grids["gridItems"].Columns;
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                columns["start_amount"].HideOnLoad = columns["start_amount"].Hidden = true;
                columns["end_amount"].HideOnLoad = columns["end_amount"].Hidden = true;
                columns["buy_amount"].HideOnLoad = columns["buy_amount"].Hidden = true;
            }

            if (costPriceType == "1") costPriceTypeName = "预设成本";
            else if (costPriceType == "2") costPriceTypeName = "加权平均成本";
            else if (costPriceType == "4") costPriceTypeName = "最近平均进价";
            DataItems["cost_price_type"].Value = costPriceType;
            DataItems["cost_price_type"].Label = costPriceTypeName;
        }


    }



    [Route("api/[controller]/[action]")]
    public class StockChangeSumController : QueryController
    {
        public StockChangeSumController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            StockChangeSumModel model = new StockChangeSumModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            StockChangeSumModel model = new StockChangeSumModel(cmd);

            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            StockChangeSumModel model = new StockChangeSumModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
