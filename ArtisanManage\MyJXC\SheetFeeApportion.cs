﻿using ArtisanManage.CwPages;
using ArtisanManage.Models;
using ArtisanManage.MyCW;
using ArtisanManage.Services;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using Microsoft.CodeAnalysis.Elfie.Model.Strings;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public enum SHEET_FEE_APPORTION
    {
        EMPTY,//0
        SAVE,//1
        APPROVE,//2
        RED1,//3
        RED2//4
    }

    public class ItemInfo//用于获取当前加权价
    {
        public string item_id { get; set; } = "";
        public string item_name { get; set; } = "";
        public decimal cost_price_avg { get; set; } = 0;
        public decimal total_qty { get; set; } = 0;//加权价角度的总数量，不考虑仓库批次
    }

    public class FeeSheetInfo//用于采购单审核前的分摊 和 采购单审核后已审核的分摊单
    {
        public List<SheetRowFeeApportion> sheetRowFeeAppoList { get; set; }
        public string fee_allocate_way { get; set; }
        public decimal total_fee_amount { get; set; }
        public string fee_apportion_sheet_id { get; set; } = "";
        public string fee_apportion_sheet_no { get; set; } = "";
        public SHEET_FEE_APPORTION status { get; set; } = SHEET_FEE_APPORTION.EMPTY;
        public string make_brief { get; set; } = "";
    }

    #region 费用信息（gridB）
    public class SheetRowFeeApportion:SheetRowBase
    {
        public SheetRowFeeApportion() 
        {
            
        }

        [SaveToDB][FromFld] public string relate_sheet_type { get; set; }
        [SaveToDB][FromFld] public string relate_sheet_id { get; set; }
        [SaveToDB(false)][FromFld(false)] public override int row_index { get; set; }
        [SaveToDB(false)][FromFld(false)] public override int inout_flag { get; set; }
        [SaveToDB(false)][FromFld(false)] public override string remark { get; set; }

        public bool is_new { get; set; } = false;//通过sheet_attribute/FeeSheetInfo/sheetRowFeeAppoList保存
        public string fee_sheet_id { get; set; }
        public string fee_sheet_no { get; set; }
        public string supcust_id { get; set; } = "";
        public string sup_name { get; set; }
        public string fee_sub_id { get; set; }
        public string fee_sub_name { get; set; }
        public string fee_sub_amount { get; set; }
        public string payway1_id { get; set; }
        public string payway1_name { get; set; }
        public string payway1_amount { get; set; } = "0";
        public string left_amount { get; set; } = "0";
        public string fee_make_brief { get; set; } = "";
    }

    public class SheetFeeApportion: SheetBase<SheetRowFeeApportion>
    {
        public bool fromBuySheet = false;
        public bool fromFeeAppoSheet = false;
        public bool from_buy_sheet { get; set; } = false;//savetodb

        public SheetFeeApportion(LOAD_PURPOSE loadPurpose) : base("sheet_fee_apportion_main", "sheet_fee_apportion_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_FEE_APPORTION;
            ConstructFun();
        }

        private void ConstructFun()
        {
            if (LoadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @"left join sheet_fee_apportion_detail d on t.sheet_id=d.sheet_id and d.relate_sheet_type='CG'
                                                left join (select sheet_id as buy_sheet_id, sheet_no as buy_sheet_no from sheet_buy_main where company_id=~COMPANY_ID) bm on t.sheet_attribute->>'buy_sheet_id'=bm.buy_sheet_id::text
                                                left join (select oper_id, oper_name as maker_name from info_operator where company_id=~COMPANY_ID) om on om.oper_id=t.maker_id
                                                left join (select oper_id, oper_name as seller_name from info_operator where company_id=~COMPANY_ID) os on os.oper_id=t.seller_id
                                                left join (select oper_id, oper_name as approver_name from info_operator where company_id=~COMPANY_ID) oa on oa.oper_id=t.approver_id";
                DetailLeftJoin = @"left join  (select sheet_id as buy_sheet_id, sheet_no as buy_sheet_no, supcust_id  from sheet_buy_main where company_id=~COMPANY_ID) bm on bm.buy_sheet_id=t.relate_sheet_id and t.relate_sheet_type='CG'
                                                left join (select sheet_id as fee_sheet_id, sheet_no as fee_sheet_no, supcust_id, make_brief as fee_make_brief from sheet_fee_out_main where company_id=~COMPANY_ID) fm on t.relate_sheet_id=fm.fee_sheet_id and t.relate_sheet_type='ZC'
                                                left join (select sheet_id, fee_sub_id,  fee_sub_amount  from sheet_fee_out_detail where company_id=~COMPANY_ID) fd on fd.sheet_id=t.relate_sheet_id 
                                                left join (select sub_id,sub_name from cw_subject where company_id=~COMPANY_ID) s on fd.fee_sub_id=s.sub_id
                                                left join (select supcust_id, sup_name from info_supcust where company_id=~COMPANY_ID) c on c.supcust_id=bm.supcust_id";
            }

        }

        [SaveToDB][FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB][FromFld]public int seller_id { get; set; }
        [FromFld("os.seller_name",LOAD_PURPOSE.SHOW)] public string seller_name { get; set; }

        public List<SheetRowBuy> BuySheetRows { get; set; } = new List<SheetRowBuy>();
        public List<SheetRowFeeApportion> FeeSheetRows { get; set; } = new List<SheetRowFeeApportion>();

        [FromFld("bm.buy_sheet_id", LOAD_PURPOSE.SHOW)] public string buy_sheet_id { get; set; }
        [FromFld("bm.buy_sheet_no", LOAD_PURPOSE.SHOW)] public string buy_sheet_no { get; set; }
        [FromFld("sheet_attribute->>'fee_allocate_way'", LOAD_PURPOSE.SHOW)] public string fee_allocate_way { get; set; }
        [FromFld("(case sheet_attribute->>'fee_allocate_way' when 'price' then '按价格分摊' when 'countS' then '按小单位数量分摊' when 'countB' then '按大单位数量分摊' when 'weight' then '按重量分摊' else '' end)", LOAD_PURPOSE.SHOW)] public string fee_allocate_label { get; set; }
        [FromFld("sheet_attribute->>'total_fee_amount'", LOAD_PURPOSE.SHOW)] public string total_fee_amount { get; set; }//sheet_attribute存一份，单据合计如果和这个不一致就是bug

        [SaveToDB][FromFld] public string sheet_attribute 
        {
            get//从前端获取数据，保存数据库
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                if (!string.IsNullOrEmpty(buy_sheet_id))
                {
                    sheetAttribute.Add("buy_sheet_id", buy_sheet_id);
                }
                if (!string.IsNullOrEmpty(fee_allocate_way))
                {
                    sheetAttribute.Add("fee_allocate_way", fee_allocate_way);
                }
                if (!string.IsNullOrEmpty(total_fee_amount))
                {
                    sheetAttribute.Add("total_fee_amount", total_fee_amount);
                }
                if (!string.IsNullOrEmpty(JsonConvert.SerializeObject(BuySheetRows)))
                {
                    List<Dictionary<string, string>> buySheetRowsList = new List<Dictionary<string, string>>();
                    foreach (SheetRowBuy rowBuy in BuySheetRows)
                    {
                        Dictionary<string, string> rowStr = new Dictionary<string, string>();
                        rowStr.Add("row_index", rowBuy.row_index.ToString());
                        rowStr.Add("item_id", rowBuy.item_id.ToString());
                        rowStr.Add("allocate_amount", rowBuy.allocate_amount.ToString());
                        buySheetRowsList.Add(rowStr);
                    }
                    sheetAttribute.Add("BuySheetRows", JsonConvert.SerializeObject(buySheetRowsList));
                }
                if (!string.IsNullOrEmpty(JsonConvert.SerializeObject(FeeSheetRows)))
                {
                    sheetAttribute.Add("FeeSheetRows", JsonConvert.SerializeObject(FeeSheetRows));
                }
                if(fromBuySheet) from_buy_sheet = fromBuySheet;
                if (!string.IsNullOrEmpty(from_buy_sheet.ToString().ToLower()))
                {
                    sheetAttribute.Add("from_buy_sheet", from_buy_sheet.ToString().ToLower());
                }

                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set//读取数据库，返回前端
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.buy_sheet_id != null)
                    {
                        this.buy_sheet_id = sheetAttr.buy_sheet_id;
                    }
                    if (sheetAttr.fee_allocate_way != null)
                    {
                        this.fee_allocate_way = sheetAttr.fee_allocate_way;
                    }
                    if (sheetAttr.total_fee_amount != null)
                    {
                        this.total_fee_amount = sheetAttr.total_fee_amount;
                    }
                    if (sheetAttr.BuySheetRows != null)
                    {
                        this.BuySheetRows = JsonConvert.DeserializeObject<List<SheetRowBuy>>(sheetAttr.BuySheetRows.ToString());
                    }
                    if (sheetAttr.FeeSheetRows != null)
                    {
                        this.FeeSheetRows = JsonConvert.DeserializeObject<List<SheetRowFeeApportion>>(sheetAttr.FeeSheetRows.ToString());
                    }
                    if (sheetAttr.from_buy_sheet != null)
                    {
                        this.from_buy_sheet = Convert.ToBoolean(sheetAttr.from_buy_sheet);
                    }
                }
            }
        }


        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            //string sql = "";

        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);

        }

        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.sheet_id + "_" + this.OperID + "_" + this.make_brief + "_" + this.fee_allocate_way + "_" + this.total_fee_amount + "_" + this.buy_sheet_id + "_";
            foreach (var row in FeeSheetRows)
            {
                res += row.fee_sub_id + "_" + row.fee_sub_amount + row.remark;
            }
            return res;
        }


        public async Task<string> CheckBeforeSaveOrApprove(CMySbCommand cmd)
        {
            //check1. 保存时采购单是否已红冲/已分摊过（分摊单已审核），分摊时间是否晚于采购时间
            if (!fromBuySheet)
            {
                dynamic buysheet = await CDbDealer.Get1RecordFromSQLAsync($@"select bm.sheet_no, bm.approve_time, bm.red_flag, fm.sheet_id as fa_sheet_id, fm.sheet_no as fa_sheet_no, fm.approve_time as fa_approve_time from sheet_buy_main bm 
                    left join sheet_fee_apportion_main fm on bm.company_id=fm.company_id and coalesce((bm.sheet_attribute->'feeSheetInfo'->>'fee_apportion_sheet_id')::numeric,0)=fm.sheet_id
                    where bm.company_id={company_id} and bm.sheet_id={buy_sheet_id};", cmd);
                if (buysheet.red_flag != "") return $"采购单【{buysheet.sheet_no}】已红冲，请重新选择";
                if (sheet_id != "" && buysheet.fa_approve_time != "") return $"采购单【{buysheet.sheet_no}】已分摊至采购费用分摊单【{buysheet.fa_sheet_no}】，请重新选择";//同一采购单可以在多个分摊单保存，但审核只能审核一次
                if ((happen_time == "" ? DateTime.Now : Convert.ToDateTime(happen_time)) < Convert.ToDateTime(buysheet.approve_time)) return "分摊单交易时间不能早于采购单审核时间";
            }

            //check2. 检查分摊金额是否不为负
            for (int i = 0; i < BuySheetRows.Count(); i++)
            {
                if (BuySheetRows[i].allocate_amount < 0) return $"采购单 第{i + 1}行 分摊金额不能为负";
            }

            //check3. 保存时费用支出单是否已红冲，是否已用于分摊，分摊时间是否晚于费用单时间
            if (FeeSheetRows.Where(r => r.fee_sheet_id.IsValid()).Count() > 0)
            {
                dynamic oldfeesheets = await CDbDealer.GetRecordsFromSQLAsync($"select sheet_no, sheet_id, approve_time, red_flag, sheet_attribute->>'fee_apportion_sheet_id' as fee_apportion_sheet_id, sheet_attribute->>'fee_apportion_sheet_no' as fee_apportion_sheet_no from sheet_fee_out_main where company_id={company_id} and sheet_id in ( {String.Join(',', FeeSheetRows.Where(r => r.fee_sheet_id.IsValid()).Select(r => r.fee_sheet_id).ToList())} )", cmd);
                foreach (dynamic row in oldfeesheets)
                {
                    if (row.red_flag != "") return $"费用支出单【{row.sheet_no}】已红冲，请重新选择";
                    if (row.fee_apportion_sheet_id != "") return $"费用支出单【{row.sheet_no}】已分摊至采购费用分摊单【{row.fee_apportion_sheet_no}】，请重新选择";
                    if (!fromBuySheet)
                    {
                        if ((happen_time == "" ? DateTime.Now : Convert.ToDateTime(happen_time)) < Convert.ToDateTime(row.approve_time)) return "分摊单交易时间不能早于费用支出单审核时间";
                    }
                }
            }

            return "";
        }

		public override async Task<string> OnSheetBeforeSave(CMySbCommand cmd, CInfoForApproveBase info)
        {
			//保存：分摊单保存主表（采购单信息，分摊单信息都存在f分摊单的sheet_attribute里），详表只存buy_sheet

			string msg = "";
             
            msg = await CheckBeforeSaveOrApprove(cmd);
            if (msg != "") return msg;

            //save1. 分摊单保存
            SheetRowFeeApportion rowFA = new SheetRowFeeApportion();
            rowFA.relate_sheet_type = "CG";
            rowFA.relate_sheet_id = buy_sheet_id;//采购单必选才能保存
            rowFA.row_index= 0;
            SheetRows.Add(rowFA); 

            return msg;
        }

        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd, CInfoForApproveBase info)
		{
            //审核的存储顺序：
            //1. 采购明细回写：allocate_amount
            //2. 新增费用单的审核（没有保存草稿，草稿在分摊单里）
            //3. 分摊单的审核：分摊单主表（基础信息+sheet_attribute: buy_sheet_id，fee_allocate_way，total_fee_amount，BuySheetRows，FeeSheetRows，status）；分摊单详表：采购的一条单号，费用的多条单号
            //4. (onsheetidgot阶段) 重算成本价
            //5. (onsheetidgot阶段) 采购单回写sheet_attribute（FeeSheetInfo：sheetRowFeeAppoList，fee_apportion_sheet_id，fee_allocate_way，total_fee_amount）
            //6. (onsheetidgot阶段) 新旧费用单回写sheet_attribute（buy_sheet_id，fee_apportion_sheet_id）


            string msg = "";
            string sql = "";
           
         
            msg = await CheckBeforeSaveOrApprove(cmd);
            if (msg != "") return msg;

            //approve1. 采购明细回写
            SheetRowFeeApportion rowToDB = new SheetRowFeeApportion();
            rowToDB.relate_sheet_type = "CG";
            rowToDB.relate_sheet_id = buy_sheet_id;
            rowToDB.row_index = 0;
            SheetRows.Add(rowToDB);

            if (!fromBuySheet)
            {
                foreach (SheetRowBuy rowBuy in BuySheetRows)
                {
                    sql += $"update sheet_buy_detail set allocate_amount=({rowBuy.allocate_amount}) where company_id={company_id} and sheet_id={buy_sheet_id} and row_index={rowBuy.row_index} and item_id={rowBuy.item_id};";
                }
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }

            //approve2. 新增费用单的审核
            //暂时无法合并生成新的费用单，因为每一行支付方式单独统计，如果超过了两种支付方式无法匹配费用单
            //两个两个合并不如直接一行做一个，逻辑简单清晰
            int rowIndex = 1;//CG明细行是0，ZC行从1开始
            for(int i=0;i< FeeSheetRows.Count; i++)
            {
                SheetRowFeeApportion rowFee = FeeSheetRows[i];
                rowToDB = new SheetRowFeeApportion();
                rowToDB.relate_sheet_type = "ZC";
                rowToDB.row_index=rowIndex;

                if (rowFee.fee_sheet_id.IsInvalid())
                {
                    SheetFeeOut newfeesheet=new SheetFeeOut();
                    newfeesheet.SheetType = "ZC";
                    newfeesheet.money_inout_flag = -1;
                    newfeesheet.company_id = company_id;
                    newfeesheet.OperID = OperID;
                    newfeesheet.happen_time = happen_time;
                    newfeesheet.supcust_id = rowFee.supcust_id==null?"": rowFee.supcust_id;
                    newfeesheet.payway1_id = rowFee.payway1_id;
                    if (rowFee.payway1_id.IsInvalid())//如果走欠款，没选支付方式，就取一个现金银行类型的刻模具
                    {
                        cmd.CommandText = $"select v from ({CommonTool.SelectPayWayNormal.Replace("~COMPANY_ID", company_id).Replace("~OPER_ID", OperID)}) t limit 1";
                        newfeesheet.payway1_id = (await cmd.ExecuteScalarAsync()).ToString();
                    }
                    if (newfeesheet.payway1_id.IsInvalid() || newfeesheet.payway1_id == "-1") return $"第{i+1}行 费用支出单请指定支付方式";
                    newfeesheet.payway1_amount = Convert.ToDecimal(rowFee.payway1_amount == "" ? "0" : rowFee.payway1_amount);
                    newfeesheet.now_pay_amount = Convert.ToDecimal(rowFee.payway1_amount == "" ? "0" : rowFee.payway1_amount);
                    newfeesheet.total_amount = Convert.ToDecimal(rowFee.fee_sub_amount);
                    newfeesheet.make_brief = rowFee.fee_make_brief;
                    SheetRowFeeOut newrow=new SheetRowFeeOut();
                    newrow.fee_sub_id = rowFee.fee_sub_id;
                    newrow.fee_sub_amount = Convert.ToDecimal(rowFee.fee_sub_amount);
                    newfeesheet.SheetRows.Add(newrow);

                    msg = await newfeesheet.SaveAndApprove(cmd, false);
                    if (msg != "") return msg;

                    FeeSheetRows[i].fee_sheet_id=newfeesheet.sheet_id;
                    FeeSheetRows[i].fee_sheet_no = newfeesheet.sheet_no;
                    rowToDB.relate_sheet_id = newfeesheet.sheet_id;
                    SheetRows.Add(rowToDB);
                }
                else
                {
                    if (SheetRows.Where(r => r.relate_sheet_id == rowFee.fee_sheet_id).Count() == 0)
                    {
                        rowToDB.relate_sheet_id = rowFee.fee_sheet_id;
                        SheetRows.Add(rowToDB);
                    }        
                }
                rowIndex++;
            }

            
            
            return msg;
        }


        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info)
        {
            await base.OnSheetIDGot(cmd, sheetID, info);
            cmd.ActiveDatabase = "";
            string sql = "";
            //CInfoForApprove info = (CInfoForApprove)info1;

            //红冲：
            //传入的sheetID是红冲单的sheet_id，info是原单的信息
            //1. 红冲分摊单（base已冲）
            //2. 采购单按原金额重算成本价
            //3. 撤销分摊单对采购单的影响
            //4. 撤销分摊单对旧的费用单的影响
            //5. 红冲新开的费用单



            //approve4/red2：重算成本价
            SheetBuy sheetBuy = new SheetBuy();
            await sheetBuy.Load(cmd, company_id, buy_sheet_id);
            string dealing_happen_time = happen_time;
            //红冲采购单时：
            //分摊单时间如果与采购单相同，采购单重算逻辑会带上分摊单，分摊单重算逻辑不带采购单，因为采购单先于分摊单被红冲，等分摊单跑到重算逻辑时已经查不到采购单了；
            //分摊单时间如果与采购单不同，采购单重算必定带上分摊单，分摊单重算必定不带采购单，因为分摊单时间必定晚于采购单。
            //红冲分摊单时：
            //分摊单时间如果与采购单相同，分摊单重算逻辑会带上采购单，但不影响结果；
            //分摊单时间如果与采购单不同，分摊单重算必定不带采购单。
            List<ItemInfo> itemInfoList = await CDbDealer.GetRecordsFromSQLAsync<ItemInfo>($@"select iip.item_id, iip.item_name, coalesce(iip.cost_price_avg,0) as cost_price_avg, s.total_qty from info_item_prop iip 
                        left join (select item_id,sum(stock_qty) total_qty from stock where company_id={company_id} group by item_id) s on iip.item_id=s.item_id 
                        where iip.company_id={company_id} and iip.item_id in ({ string.Join(',',sheetBuy.SheetRows.Where(r=>r.quantity>0).Select(r=>r.item_id)) })", cmd);
            List<SheetRowCostPrice> costPriceRows = new List<SheetRowCostPrice>();
            foreach (SheetRowBuy rowBuy in sheetBuy.SheetRows.Where(r => r.quantity > 0))
            {
                SheetRowCostPrice costRow = new SheetRowCostPrice();
                costRow.item_id = rowBuy.item_id;
                costRow.unit_no = rowBuy.unit_no;
                costRow.inout_flag = rowBuy.inout_flag;
                costRow.sub_amount = rowBuy.allocate_amount * (red_flag == "" ? 1 : -1);//只改变成本，不改变数量
                if (itemInfoList.Find(r => r.item_id == rowBuy.item_id) == null)
                {
                    info.ErrMsg = "商品数据检索失败";
                    return;
                }
                costRow.cost_price_avg = itemInfoList.Find(r => r.item_id == rowBuy.item_id).cost_price_avg;
                costRow.old_total_qty = itemInfoList.Find(r => r.item_id == rowBuy.item_id).total_qty;
                costRow.item_cost_price_suspect = rowBuy.item_cost_price_suspect;//?
                costPriceRows.Add(costRow);
            }
            await base.UpdateCostPriceAvg(cmd, costPriceRows, dealing_happen_time);

            
            sql = "";
            FeeSheetInfo feeSheetInfo = new FeeSheetInfo();
            feeSheetInfo.sheetRowFeeAppoList = FeeSheetRows;
            feeSheetInfo.total_fee_amount = Convert.ToDecimal(total_fee_amount);
            feeSheetInfo.fee_allocate_way = fee_allocate_way;
            feeSheetInfo.fee_apportion_sheet_id = sheetID;
            feeSheetInfo.fee_apportion_sheet_no = sheet_no;
            feeSheetInfo.status = SHEET_FEE_APPORTION.APPROVE;
            if (red_flag == "")//approve5：采购单回写sheet_attribute
            {
                sql += $"update sheet_buy_main set sheet_attribute = jsonb_set(COALESCE(sheet_attribute, '{{}}'::jsonb), '{{feeSheetInfo}}','{JsonConvert.SerializeObject(feeSheetInfo)}', true) where company_id = {company_id} and sheet_id={buy_sheet_id};";
            }
            else//red3：采购单主表移除sheet_attribute/feeSheetInfo，采购单详表移除分摊金额
            {
                sql += $@"update sheet_buy_main set sheet_attribute=sheet_attribute::jsonb - 'feeSheetInfo' where company_id={company_id} and sheet_id={buy_sheet_id};
                            update sheet_buy_detail set allocate_amount=null where company_id={company_id} and sheet_id={buy_sheet_id};";
            }

            fromFeeAppoSheet = true;
            foreach (SheetRowFeeApportion feeFA in FeeSheetRows)
            {
                if (red_flag == "")//approve6：费用单回写sheet_attribute
                {
                    sql += $@"update sheet_fee_out_main set sheet_attribute = jsonb_set(COALESCE(sheet_attribute, '{{}}'::jsonb), '{{buy_sheet_id}}','{buy_sheet_id}', true) where company_id = {company_id} and sheet_id={feeFA.fee_sheet_id};
                                    update sheet_fee_out_main set sheet_attribute = jsonb_set(COALESCE(sheet_attribute, '{{}}'::jsonb), '{{fee_apportion_sheet_id}}','{sheetID}', true) where company_id = {company_id} and sheet_id={feeFA.fee_sheet_id};
                                    update sheet_fee_out_main set sheet_attribute = jsonb_set(COALESCE(sheet_attribute, '{{}}'::jsonb), '{{fee_apportion_sheet_no}}','""{sheet_no}""', true) where company_id = {company_id} and sheet_id={feeFA.fee_sheet_id};";
                }
                else//red4. 费用单移除sheet_attribute/buy_sheet_id，fee_apportion_sheet_id，fee_apportion_sheet_no
                {
                    sql += $@"update sheet_fee_out_main set sheet_attribute=sheet_attribute::jsonb - 'buy_sheet_id' where company_id={company_id} and sheet_id={feeFA.fee_sheet_id};
                                    update sheet_fee_out_main set sheet_attribute=sheet_attribute::jsonb - 'fee_apportion_sheet_id' where company_id={company_id} and sheet_id={feeFA.fee_sheet_id};
                                    update sheet_fee_out_main set sheet_attribute=sheet_attribute::jsonb - 'fee_apportion_sheet_no' where company_id={company_id} and sheet_id={feeFA.fee_sheet_id};";
                    if (feeFA.is_new)//red5. 红冲分摊单新开的费用单
                    {
                        SheetFeeOut feeSheet=new SheetFeeOut();
                        if(fromFeeAppoSheet) feeSheet.allowRedByApportion = true;//fromFeeAppoSheet=true表示从分摊单红冲过来的，可以红冲费用单
                        info.ErrMsg=await feeSheet.Red(cmd, company_id, feeFA.fee_sheet_id, OperID, $"红冲分摊单【{sheet_no}】", false);
                        if (info.ErrMsg != "") return;
                    }
                }
            }

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
        }
    }


    #endregion
}
