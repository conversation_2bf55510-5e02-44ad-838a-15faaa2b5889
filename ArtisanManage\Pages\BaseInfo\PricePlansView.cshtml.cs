﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class PricePlansViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
 
        public PricePlansViewModel(CMySbCommand cmd) : base(MenuId.infoPrice)
        {
            this.cmd = cmd;
            
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     IdColumn="plan_id",TableName="price_plan_main",
                     ShowContextMenu=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       //{"plan_id",new DataItem(){Title="方案编号", SqlFld = "plan_item.plan_id",Width="180"}},

                       {"plan",   new DataItem(){Title="", Width="80",  SqlFld="plan_id",
                           FuncDealMe=planId=>{
                               return $"<input type='checkbox' class='btn' id='{planId}' />";
                           }
                       }},
                       {"plan_id",new DataItem(){Title="方案名称",SqlFld ="plan_id",Hidden=true, Width="180"}},
                       {"plan_name",new DataItem(){Title="方案名称",SqlFld ="plan_name", Width="180"}},
                       //{ "edit",new DataItem(){Title="编辑",SqlFld="t.edit",Width="180",SaveToDB=false,Linkable=true } },
                       //{ "copy",new DataItem(){Title="复制",SqlFld="t.copy",Width="180",SaveToDB=false,Linkable=true } },
                       //{ "delete",new DataItem(){Title="删除",SqlFld="t.delete",Width="180",SaveToDB=false,Linkable=true} },
                       {"update_time",new DataItem(){Title="更新时间",SqlFld ="update_time",  Width="180"}}
                     },
                     QueryFromSQL="from price_plan_main  where company_id='~COMPANY_ID'" ,
                     QueryOrderSQL="order by update_time desc"
                  }
                } 
            }; 
        }
        public async Task OnGet(string forSelect)
        {
            await InitGet(cmd);
            ForSelect = forSelect == "1";
        }
        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            cmd.CommandText = $"select company_id from price_strategy_client where company_id={company_id} and (price1 in ('{rowIDs}') or price2 in ('{rowIDs}') or price3 in ('{rowIDs}') or price4 in ('{rowIDs}') or price5 in ('{rowIDs}') ) limit 1";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                return "方案已被使用过,无法删除";
            }
            cmd.CommandText = $"select company_id from price_strategy_class where company_id={company_id} and (price1 in ('{rowIDs}') or price2 in ('{rowIDs}') or price3 in ('{rowIDs}') or price4 in ('{rowIDs}') or price5 in ('{rowIDs}') ) limit 1";
            ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                return "方案已被使用过了,无法删除";
            }

            return "";
        }
    }




    [Route("api/[controller]/[action]")]
    public class PricePlansViewController : QueryController
    { 
        public PricePlansViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            PricePlansViewModel model = new PricePlansViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            PricePlansViewModel model = new PricePlansViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            PricePlansViewModel model = new PricePlansViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd, "price_plan_main");// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<object> BatchDelete([FromBody] dynamic data)
        {
            string ids = data.dataids.ToString();
            //Security.GetInfoFromOperKey((string)data.operKey, out string company_id);
            var planIDs = Newtonsoft.Json.JsonConvert.DeserializeObject<string[]>(ids);
            var rowIDs = "";
            foreach (var p in planIDs)
            {
                if (rowIDs != "") rowIDs += ",";
                rowIDs += "'" + p + "'";
            }
            //PricePlansViewModel model = new PricePlansViewModel(cmd);
            //var errmsg = model.CheckBeforeDeleteRecords(rowIDs);
            //if(errmsg != null)return new JsonResult(new { result = "OK", msg = errmsg });
            cmd.CommandText = @$"delete from price_plan_main where company_id={Token.CompanyID} and plan_id in ({rowIDs});
                                 delete from price_plan_item where company_id={Token.CompanyID} and plan_id in ({rowIDs});
                                 delete from price_plan_class where company_id={Token.CompanyID} and plan_id in ({rowIDs});
                                ";
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new { result = "OK", msg = "" });
        }

        [HttpPost]
        public async Task<object> EditPlanName([FromBody] dynamic data)
        {
            string newName = data.newName;
            string id = data.id;
            Security.GetInfoFromOperKey((string)data.operKey, out string company_id);

            cmd.CommandText = $"update price_plan_main set plan_name='{newName}',update_time=now() where plan_id = {id} and company_id={company_id};";
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new { result = "OK", msg = "" });
        }

        [HttpPost]
        public async Task<object> AddPlan([FromBody] dynamic data)
        {
            string addName = data.addName;
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string sql = $"SELECT COUNT(*) rn FROM price_plan_main WHERE company_id = {companyID} and plan_name = '{addName}'; ";
            dynamic result = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (result != null)
            {
                Console.WriteLine(result.rn);
                int n = Convert.ToInt32(result.rn);
                if(n>0)
                {
                    return new JsonResult(new { result = "OK", msg = "价格方案已存在！" });
                }
            }
            cmd.CommandText = $"insert into price_plan_main (company_id,plan_name,create_time,update_time) values ('{companyID}','{addName}',now(),now() );";
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new { result = "OK", msg = "添加成功！" });
        }

        [HttpPost]
        public async Task<object> CopyPlan([FromBody] dynamic data)
        {
            string copyName = data.copyName;
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            cmd.CommandText = $"insert into price_plan_main (company_id,plan_name,create_time,update_time) values ('{companyID}','{copyName}',now(),now() );";
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new { result = "OK", msg = "" });
        }
    }
}
