﻿@page
@model ArtisanManage.Pages.BaseInfo.BranchPositionTypeEditModel
@{
    Layout = null;
}
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>BranchPositionTypeEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">


        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            $('#type_name input').on('input', function () {
                $('#py_str input').val(this.value.ToPinYinCode());
            })
        });
    </script>
</head>
<body>
    <div id="divHead" class="headtail" style="width:500px;"></div>
    <div style="text-align:center;margin-top:20px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;">保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>
