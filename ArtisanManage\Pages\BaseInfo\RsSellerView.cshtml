﻿@page
@model ArtisanManage.Pages.BaseInfo.RsSellerViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());

        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            debugger
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "RsSellerEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var now_arr = new Array
                        var row = {
                            reseller_id: rs.data.record.reseller_id,
                            reseller_name: rs.data.record.reseller_name,
                            reseller_mobile: rs.data.record.reseller_mobile,
                            reseller_count: rs.data.record.reseller_count,
                            plan_name: rs.data.record.plan_name,
                            plan_id: rs.data.record.plan_id
                        }
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;


                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        //$('#gridItems').jqxGrid('setcellvalue', rs.data.record.boundindex, "branch_type", rs.data.record.branch_type_name);
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "branch_name",rs.data.record.branch_name);
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "branch_addr", rs.data.record.branch_addr);
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "branch_type", rs.data.record.branch_type_name);
                    QueryData();

                }
                $("#popItem").jqxWindow('close');
            };
            if (rs.data.msgHead == "RsSellerBind") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var now_arr = new Array
                        var row = {
                            reseller_id: rs.data.record.reseller_id,
                            reseller_name: rs.data.record.reseller_name,
                            reseller_mobile: rs.data.record.reseller_mobile,
                            reseller_count: rs.data.record.reseller_count,
                            plan_name: rs.data.record.plan_name,
                            plan_id: rs.data.record.plan_id
                        }
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;


                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        //$('#gridItems').jqxGrid('setcellvalue', rs.data.record.boundindex, "branch_type", rs.data.record.branch_type_name);
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "branch_name",rs.data.record.branch_name);
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "branch_addr", rs.data.record.branch_addr);
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "branch_type", rs.data.record.branch_type_name);
                    QueryData();

                }
                $("#popItem").jqxWindow('close');
            };
        });
        var m_db_id = "10";

        var newCount = 1;

        function btnAddItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="RsSellerEdit?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }

        function btnBindItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="RsSellerBind?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }

        function onGridRowEdit(rowIndex) {
            var reseller_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'reseller_id');
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="RsSellerEdit?operKey=' + g_operKey + '&reseller_id=' + reseller_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }
        
        var itemSource = {};
        $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)

                $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                if (args.datafield == "reseller_name") {
                    if (args.originalEvent.button == 2) return;
                    var plan_id = args.row.bounddata.i;
                    RowIndex = args.rowindex;
                    if (ForSelect) {
                        var reseller_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "reseller_id");
                        var reseller_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "reseller_name");
                        var msg = {
                            msgHead: 'RsSellerView', action: 'select', reseller_id: reseller_id, reseller_name: reseller_name
                        };
                        window.parent.postMessage(msg, '*');
                    }
                    else {
                        onGridRowEdit(args.rowindex);
                        //$('#popItem').jqxWindow('open');
                        // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                    }
                }
            });

            $("#Cancel").on('click', function () {
                for (var i = 0; i < 10; i++) {
                    $('#jqxgrid').jqxGrid('deleterow', i);
                    $('#jqxgrid').jqxGrid('addrow', i, {})
                }
            });

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 400, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });


            QueryData();

        });
    </script>
</head>

<body>
    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;margin-bottom:10px">

        <div><input id="searchString" style="font-size:14px; border-radius:6px;border-color:#ddd;border-width:0.5px; width:200px;height:25px;" placeholder="请输入简拼/名称" /></div>
        <button onclick="QueryData()" class="margin">查询</button>
        <div><button style="width:100px" onclick="btnAddItem_click()">添加分销商</button></div>
        <div><button style="width:100px" onclick="btnBindItem_click()">绑定分销商</button></div>

    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>


    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">分销商信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>