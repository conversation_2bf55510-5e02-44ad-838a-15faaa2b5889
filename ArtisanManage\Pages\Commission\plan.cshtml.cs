﻿using ArtisanManage.Enums;
using ArtisanManage.Models;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualBasic;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.Pages
{
    public class CommisionPlansViewModel : PageBaseModel
    {
        public CommisionPlansViewModel() : base(MenuId.commissionPlan)
        {

        }
    }

    /// <summary>
    /// 提成方案
    /// </summary>
    [YjTable("Commission_Plan")]
    public class CommissionPlan : BaseModel
    {
        [YjColumn(DbField = "plan_id", IsSerial = true, IsPK = true)]
        public int Id { get; set; }

        [YjColumn(DbField = "plan_name")]
        public string Name { get; set; }

        /// <summary>
        /// json格式
        /// </summary>
        [YjColumn(DbField = "content")]
        public string Content { get; set; }
        private PlanContent _planContent = null;
        public PlanContent PlanContent { 
            get
            {
                if (_planContent == null)
                {
                    var jsonSetting = new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DateFormatString = "yyyy-MM-dd HH:mm:ss" };
                    _planContent = JsonConvert.DeserializeObject<PlanContent>(Content, jsonSetting);//拿到方案数据
                }
                return _planContent;
            }
        }

        [YjColumn(DbField = "type")]
        public string Type { get; set; }
    }
    [YjTable("Commission_Strategy")]
    public class CommissionStrategy : BaseModel
    {
        [YjColumn(DbField = "strategy_id", IsSerial = true, IsPK = true)]
        public int Id { get; set; }

        [YjColumn(DbField = "strategy_name")]
        public string Name { get; set; }
        [YjColumn(DbField = "worker_type")]
        public string workerType { get; set; }
    }
    [YjTable("Commission_Strategy_Client")]
    public class CommissionStrategyClient : BaseModel
    {
        [YjColumn(DbField = "id", IsSerial = true, IsPK = true)]
        public int Id { get; set; }

        [YjColumn(DbField = "strategy_id")]
        public string StrategyId { get; set; }
        [YjColumn(DbField = "plan_id")]
        public string PlanId { get; set; }
        [YjColumn(DbField = "supcust_id")]
        public string SupcustId { get; set; }
    }
    [YjTable("Commission_Strategy_Class")]
    public class CommissionStrategyClass : BaseModel
    {
        [YjColumn(DbField = "id", IsSerial = true, IsPK = true)]
        public int Id { get; set; }

        [YjColumn(DbField = "strategy_id")]
        public string strategyId { get; set; }
        [YjColumn(DbField = "plan_id")]
        public string planId { get; set; }
        [YjColumn(DbField = "group_id")]
        public string groupId { get; set; }
        [YjColumn(DbField = "rank_id")]
        public string rankId { get; set; }
        [YjColumn(DbField = "region_id")]
        public string regionId { get; set; }
    }
    /// <summary>
    /// 提成规则
    /// </summary>
    public class CommissionRule
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 提成模式
        /// </summary>
        public string CommissionBy { get; set; }

        /// <summary>
        /// 计算方式
        /// </summary>
        public string CountMode { get; set; }

        /// <summary>
        /// 成本计算方式
        /// </summary>
        public string CostType { get; set; } = Enums.CostType.buyPrice.ToString();

        public string Flag { get; set; } = CommissionFlag.amount.ToString();

        /// <summary>
        /// 扣除退货
        /// </summary>
        public bool DeductReturned { get; set; }

        /// <summary>
        /// 排除欠款单据
        /// </summary>
        public bool DeductArrearages { get; set; }

        /// <summary>
        /// 赠品纳入数量计算
        /// </summary>
        public bool IncludeGift { get; set; }

        /// <summary>
        /// 按大单位计算数量
        /// </summary>
        public bool ByBigUnit { get; set; }

        /// <summary>
        /// 本品赠品影响价格
        /// </summary>
        public bool GiftAffectAvgPrice { get; set; }

        /// <summary>
        /// 提成区间
        /// </summary>
        public List<CommissionRate> Rates { get; set; }

        public void Compute(CommissionDetail commission, List<CommissionRate> rates, double rateFlag,out string errMsg)
        {
            errMsg = "";
            if (CountMode == Enums.CountMode.fix.ToString())
            {
                commission.Compute_fix(rates, CommissionBy, Flag,DeductReturned,out errMsg);
            }
            else if (CountMode == Enums.CountMode.change.ToString())
            {
                commission.Compute_change(rates, CommissionBy, Flag, rateFlag, DeductReturned,out errMsg);

            }
            else if (CountMode == Enums.CountMode.range.ToString())
            {
                commission.Compute_range(rates, CommissionBy, Flag, DeductReturned);
            }
        }

    }

    /// <summary>
    /// 提成区间
    /// </summary>
    public class CommissionRate
    {
        /// <summary>
        /// 区间开始（不包含）
        /// </summary>
        public double From { get; set; }

        /// <summary>
        /// 区间结束（包含）
        /// </summary>
        
        public double To { get; set; }

        /// <summary>
        /// 销售提成配额
        /// </summary>
        public string X { get; set; }

        /// <summary>
        /// 退货扣减配额
        /// </summary>
        public string T { get; set; }

    }

    [YjTable("Commission_Plan_Map")]
    public class CommissionPlanMap : BaseModel
    {
        [YjColumn(IsSerial = true, IsPK = true)]
        public int Id { get; set; }

        [YjColumn(DbField = "plan_id")]
        public int Plan { get; set; }

        [YjColumn(DbField = "employee_id ")]
        public int Employee { get; set; }

        public string Type { get; set; }
    }

    public class PlanWithMap : CommissionPlan
    {
        [YjColumn(DbField = "id")]
        public int MapId { get; set; }

        [YjColumn(DbField = "oper_id")]
        public int EmployeeId { get; set; }

        [YjColumn(DbField = "oper_name")]
        public string EmployeeName { get; set; }

        public async static Task<List<PlanWithMap>> GetList(CMySbCommand cmd, string company_id)
        {
            cmd.CommandText = @$"SELECT commission_plan.*,commission_plan_map.id,info_operator.oper_id,info_operator.oper_name FROM commission_plan 
                    LEFT JOIN commission_plan_map on commission_plan_map.plan_id = commission_plan.plan_id
                    LEFT JOIN info_operator on info_operator.oper_id = commission_plan_map.employee_id
                    WHERE commission_plan.company_id = {company_id}";
            var dr = await cmd.ExecuteReaderAsync();
            var list = new List<PlanWithMap>();
            while (dr.Read())
            {
                list.Add((PlanWithMap)new PlanWithMap().ReadFromDataReader(dr.dr_sql));
            };
            dr.Close();
            return list;
        }


    }
    [YjTable("Commission_Strategy_Map")]
    public class CommissionStrategyMap : BaseModel
    {
        [YjColumn(IsSerial = true, IsPK = true)]
        public int flow_id { get; set; }

        [YjColumn(DbField = "strategy_id")]
        public int Strategy { get; set; }

        [YjColumn(DbField = "worker_id ")]
        public int Employee { get; set; }

        public string Type { get; set; }
    }
    public class StrategyWithMap : CommissionStrategy
    {
        [YjColumn(DbField = "flow_id", IsSerial = true, IsPK = true)]
        public int MapId { get; set; }

        [YjColumn(DbField = "oper_id")]
        public int EmployeeId { get; set; }

        [YjColumn(DbField = "oper_name")]
        public string EmployeeName { get; set; }

        public async static Task<List<StrategyWithMap>> GetList(CMySbCommand cmd, string company_id)
        {
            cmd.CommandText = @$"SELECT commission_strategy_map.flow_id,commission_strategy.*,info_operator.oper_id,info_operator.oper_name FROM commission_strategy
                    LEFT JOIN commission_strategy_map on commission_strategy_map.strategy_id = commission_strategy.strategy_id
                    LEFT JOIN info_operator on info_operator.oper_id = commission_strategy_map.worker_id
                    WHERE commission_strategy.company_id = {company_id}";
            var dr = await cmd.ExecuteReaderAsync();
            var list = new List<StrategyWithMap>();
            while (dr.Read())
            {
                list.Add((StrategyWithMap)new StrategyWithMap().ReadFromDataReader(dr.dr_sql));
            };
            dr.Close();
            return list;
        }


    }
    public class PlanContent
    {
        public List<CommissionRule> rules { get; set; }
        public List<RuleAndTarget> targets { get; set; }
        public static RuleAndTarget GetTargetFromItem(List<RuleAndTarget> targets, CommissionInfo item)
        {
            var ruleAndTarget = targets.FirstOrDefault(target => target.Type == "i" && target.Target == item.item_id);
            if (ruleAndTarget == null || ruleAndTarget.Rule == "")/*如果未找到，就从该商品所属类别向上查找*/
            {
                if (item.other_class.IsValid())
                {
                    var classes = item.other_class.Trim('/').Split('/');
                    for (int i = classes.Length - 1; i >= 0; i--)
                    {
                        if (targets.FirstOrDefault(target => target.Type == "c" && target.Target.ToString() == classes[i]) is RuleAndTarget t)
                        {
							if (ruleAndTarget == null)
							{
								ruleAndTarget = t;//TargetName 另外处理。
							}
							else
							{
								ruleAndTarget.Rule = t.Rule;
							}
                            if (ruleAndTarget.Rule != "")
                             break;

                        }

						
					}
                }
            }
            else ruleAndTarget.TargetName = item.item_name;
            return ruleAndTarget;
        }
        /// <summary>
        /// 划分商品到对应规则下，并分别汇总处理;
        /// </summary>
        /// <param name="infos"></param>
        /// <param name="sumResult">汇总结果</param>
        /// <returns></returns>
        internal KeyValuePair<CommissionDetail, List<CommissionDetail>> MapTargetsAndSum_old(List<CommissionInfo> infos,CommissionDetail sumResult,PlanWithMap plan, out string errMsg)
        {
            errMsg = "";
            foreach (var info in infos)//将infos分配到targets
            {
                var ruleAndTarget = targets.FirstOrDefault(target => target.Type == "i" && target.Target == info.item_id);
                if (ruleAndTarget == null)/*如果未找到，就从该商品所属类别向上查找*/
                {
                    if (info.other_class.IsValid())
                    {
                        var classes = info.other_class.Trim('/').Split('/');
                        for (int i = classes.Length - 1; i >= 0; i--)
                        {
                            if (targets.FirstOrDefault(target => target.Type == "c" && target.Target.ToString() == classes[i]) is RuleAndTarget t)
                            {
                                ruleAndTarget = t;//TargetName 另外处理
                                break;
                            }
                        }
                    }
                }
                else ruleAndTarget.TargetName = info.item_name;
                ruleAndTarget?.Infos.Add(info);
            }

            var result = new List<CommissionDetail>();
            string err = "";
            foreach (var target in targets)
            {
                    if (target.Infos.Count == 0) continue;
                    if (target.Rule == "") continue;
                

                    target.CommissionRule = rules.FirstOrDefault(x => x.Name == target.Rule);

                    if (target.CommissionRule==null)
                    {
                        //err += $"方案{pla=n.Name}中的规则{target.Rule}已经被删除却还在品项上";
                        continue;
                      // throw new Exception($"方案{plan.Name}中的规则{target.Rule}已经被删除却还在品项上");
                    }
                  
                    if (target.Rates.Count == 0) target.Rates = target.CommissionRule.Rates;//
                    target.AggregateTo(result,out  err);//*汇总得到详情*/
            }
            if(err!="")
            {
                errMsg = err;                
            }

            sumResult.Sum(result);//汇总处理结果
            return new KeyValuePair<CommissionDetail, List<CommissionDetail>>(sumResult, result);
        }
        public class MapTargetResult
        {
            public string result = "";
            public string msg = "";
            public KeyValuePair<CommissionDetail, List<CommissionDetail>> data;
		}
        internal MapTargetResult MapTargetsAndSum(List<CommissionInfo> infos, CommissionDetail sumResult, PlanWithMap plan)
        {
           
            foreach (var info in infos)//将infos分配到targets
            {
                var ruleAndTarget = targets.FirstOrDefault(target => target.Type == "i" && target.Target == info.item_id);
                if (ruleAndTarget == null || ruleAndTarget.Rule=="")/*如果未找到，就从该商品所属类别向上查找*/
				//if (ruleAndTarget == null)//|| ruleAndTarget.Infos.Count == 0)/*如果未找到，就从该商品所属类别向上查找*/
				{
                    if (info.other_class.IsValid())
                    {
                        var classes = info.other_class.Trim('/').Split('/');
                        for (int i = classes.Length - 1; i >= 0; i--)
                        {
                            if (targets.FirstOrDefault(target => target.Type == "c" && target.Target.ToString() == classes[i]) is RuleAndTarget t)
                            {
                                //ruleAndTarget = t;//TargetName 另外处理
                                /*	if (ruleAndTarget != null && ruleAndTarget.Type == "i")
                                    {
                                        ruleAndTarget.TargetName = info.item_name;
                                        ruleAndTarget.Rule = t.Rule;
                                    }
                                    else
                                        ruleAndTarget = t;//TargetName 另外处理
                                    */


                                if (ruleAndTarget == null || ruleAndTarget.Rates.Count == 0)//如果当前目标为空，或未设置提成数值 那么就直接把当前目标设为上级类
                                {
                                    ruleAndTarget = t;
                                }
                                else//如果当前目标设置了数值，还是使用该目标，只是把规则变成上级的
                                {

                                    ruleAndTarget.TargetName = info.item_name;                                 
                                    ruleAndTarget.Rule = t.Rule;
                                }

								if (ruleAndTarget.Rule!="")//一旦找到规则，就退出
                                    break;
                            }
                        }
                    }
                }
                else ruleAndTarget.TargetName = info.item_name;
                if (ruleAndTarget?.Infos.Count > 0)
                {

                }
                ruleAndTarget?.Infos.Add(info);
            }

            var lstDetail = new List<CommissionDetail>();
            string err = "";
            foreach (var target in targets)
            {
                if (target.Infos.Count == 0) continue;
                if (target.Rule == "") continue;


                target.CommissionRule = rules.FirstOrDefault(x => x.Name == target.Rule);

                if (target.CommissionRule == null)
                {
                    //err += $"方案{pla=n.Name}中的规则{target.Rule}已经被删除却还在品项上";
                    continue;
                    // throw new Exception($"方案{plan.Name}中的规则{target.Rule}已经被删除却还在品项上");
                }
                if (target.CommissionRule.CommissionBy == "price")
                {
                    target.CommissionRule.IncludeGift = true;
                }
                bool bValueSetOnTarget = true;
                if (target.Rates.Count == 0)
                {
                    bValueSetOnTarget = false; 
					target.Rates = target.CommissionRule.Rates;
                }
                //
                target.AggregateTo(lstDetail, out err);//*汇总得到详情*/
                if (err != "")
                {
                    if (bValueSetOnTarget)
                    {
                        err += $",方案:{plan.Name}, 规则:{target.CommissionRule.Name}，目标:{target.TargetName}";
					}
                    else
                    {
						err += $",方案:{plan.Name}, 规则:{target.CommissionRule.Name}";
					}
                }
         
                if (err != "") break;
                 
            }

            string result = "OK";
            if (err != "")
            {
                result = "error";
            }
           
            sumResult.Sum(lstDetail);//汇总处理结果
            return new MapTargetResult() { result = result, msg = err, data = new KeyValuePair<CommissionDetail, List<CommissionDetail>>(sumResult, lstDetail)};
        }


    }


    [Route("Api/[controller]/[action]")]
    public class CommissionController : YjController
    {
        CMySbCommand cmd;

        public CommissionController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<Response> GetPlanMaps()
        {
            return await RespondAsync(async response =>
            {
                var list = await PlanWithMap.GetList(cmd, Token.CompanyID);
                var data = list.GroupBy(x => x.Id).Select(x =>
                {
                    var plan = x.First();
                    return new
                    {
                        plan.Id,
                        plan.Name,
                        plan.Content,
                        plan.Type,
                        EmployeeMaps = x.Where(v => v.MapId > 0).Select(v => new { v.MapId, v.EmployeeId, v.EmployeeName })
                    };
                }).ToList();
                response.Add("data", data);
            });
        }
        [HttpGet]
        public async Task<Response> GetStrategyMaps()
        {
            return await RespondAsync(async response =>
            {
                var list = await StrategyWithMap.GetList(cmd, Token.CompanyID);
                var data = list.GroupBy(x => x.Id).Select(x =>
                {
                    var plan = x.First();
                    return new
                    {
                        plan.Id,
                        plan.Name,
                        EmployeeMaps = x.Where(v => v.MapId > 0).Select(v => new { v.MapId, v.EmployeeId, v.EmployeeName })
                    };
                }).ToList();
                response.Add("data", data);
            });
        }


        [HttpGet]
        public async Task<Response> GetPlan(int id)
        {
            return await RespondAsync(async res =>
            {
                var list = await cmd.QueryAsync<CommissionPlan>(Token.CompanyID, $"plan_id={id} limit 1");
                res.Add("data", list);
            });
        }

        [HttpGet]
        public async Task<Response> GetEmployees()
        {
            return await RespondAsync(async res =>
            {
                var condition = "status<>0";
                var list = await cmd.QueryAsync<User>(Token.CompanyID, condition);
                res.Add("data", list);
            });
        }

        [HttpPost]
        public async Task<Response> SavePlan([FromBody] CommissionPlan plan)
        {
            plan.company_Id = int.Parse(Token.CompanyID);

            //var table = new List<CommissionPlan> { plan }.ToDataTable();
            return await RespondAsync(async res =>
            {
                if (plan.Id == 0)
                {
                    await cmd.InsertAsync(plan);
                }
                else
                {
                    await cmd.UpdateWhereAsync(plan, $"where company_id={plan.company_Id} and plan_id={plan.Id}");
                }
            });
        }

        //[HttpPost]
        //public async Task<Response> SavePlanMaps([FromBody] List<CommissionPlanMap> planMaps)
        //{            
        //    if (planMaps.Count == 0) return Error("空数据");
        //    var list = await PlanWithMap.GetList(cmd, Token.CompanyID);
        //    var errMsg = new List<string>();
        //    var position = new List<int>();
        //    var maps = planMaps.Where((pm, i) =>
        //    {
        //        var p = list.FirstOrDefault(x => x.EmployeeId == pm.Employee && x.Type == pm.Type);
        //        if (p == null)
        //        {
        //            position.Add(i);
        //            return true;
        //        }
        //        errMsg.Add($"员工{p.EmployeeName} 已经有了同类型的方案'{p.Name}',操作已取消。");
        //        return false;
        //    }).ToList();
        //    if (maps.Count == 0) return Error($"分配失败：\r\n{string.Join('\r', errMsg)}");
        //    if (errMsg.Count > 0)  Warn($"以下员工分配失败：\r\n{string.Join('\r', errMsg)}");
        //    var company_Id = int.Parse(Token.CompanyID);
        //    var table = maps.ToDataTable(x => x.company_Id = company_Id);
        //    return await RespondAsync(async res =>
        //    {
        //        var ids = await table.SaveAsync(cmd, "id");
        //        res.Add("ids", ids);
        //        res.Add("position", position);
        //    });
        //}
        [HttpPost]
        public async Task<Response> SaveStrategyMaps([FromBody] List<CommissionStrategyMap> strategyMaps)
        {
            if (strategyMaps.Count == 0) return Error("空数据");
            var list = await StrategyWithMap.GetList(cmd, Token.CompanyID);
            var errMsg = new List<string>();
            var position = new List<int>();
            var maps = strategyMaps.Where((pm, i) =>
            {
                position.Add(i);
                return true;
            }).ToList();
            var company_Id = int.Parse(Token.CompanyID);
            var table = maps.ToDataTable(x => x.company_Id = company_Id);
            return await RespondAsync(async res =>
            {
                var ids = await table.SaveAsync(cmd, "flow_id");
                res.Add("ids", ids);
                res.Add("position", position);
            });
        }

        public class ItemClassAndItem : BaseModel, ITree<ItemClassAndItem>
        {
            public string Name { get; set; }
            public int Mother_Id { get; set; }
            public int Id { get; set; }
            public List<ItemView> Items { get; set; }
            public List<ItemClassAndItem> SubNodes { get; set; }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="classId"></param>
        /// <returns></returns>
        [HttpGet]
        //public async Task<Response> GetItemOrClass(int classId)
        //{
        //    return await RespondAsync(async response =>
        //    {
        //        if(classId == 0) response.Add("data", (await cmd.QueryAsync<ItemClass>(Token.CompanyID)).ToTree1());
        //        else response.Add("data", await cmd.QueryAsync<ItemView>(Token.CompanyID, $"item_class={classId}"));
        //    });
        //} 
        public async Task<Response> GetItemOrClass(int classId)
        {
            return await RespondAsync(async response =>
            {
                if (classId == 0)
                {
                    List<ItemClass> noChildrenClass = await cmd.QueryAsync<ItemClass>((Token.CompanyID), $" EXISTS (SELECT 1  FROM info_item_class AS sub  WHERE sub.mother_id = info_item_class.class_id)");
                    string result = string.Join(", ", noChildrenClass.Select(item => item.Id));
                    List<ItemView> items = await cmd.QueryAsync<ItemView>(Token.CompanyID, $"item_class in ({result}) and (status='1' or status is null)");
                    List<ItemClass> classes = (await cmd.QueryAsync<ItemClass>(Token.CompanyID));
                    List<ItemClassAndItem> subNodes = new List<ItemClassAndItem>();
                    foreach (ItemClass class_tmp in classes)
                    {
                        var classAndItems = new ItemClassAndItem
                        {
                            Id = class_tmp.Id,
                            Name = class_tmp.Name,
                            Mother_Id = class_tmp.Mother_Id,
                            Items = items.Where(item => item.ItemClass == class_tmp.Id).ToList()
                        };
                        subNodes.Add(classAndItems);
                    }
                    response.Add("data", subNodes.ToTree1());
                }
                else response.Add("data", await cmd.QueryAsync<ItemView>(Token.CompanyID, $"item_class={classId} and (status='1' or status is null)"));
            });
        }
        [HttpPost]
        public async Task<Response> DeletePlan(int id)
        {
            
            return await RespondAsync(async res =>
            {
                await cmd.DeleteAsync<CommissionPlan>(Token.CompanyID, $"plan_id={id}");
                await cmd.DeleteAsync<CommissionStrategyClass>(Token.CompanyID, $"plan_id={id}");
                await cmd.DeleteAsync<CommissionStrategyClient>(Token.CompanyID, $"plan_id={id}");

            });
        }

        public async Task<Response> DeletePlanMap(int id)
        {
            return await RespondAsync(async res =>
            {
                await cmd.DeleteAsync<CommissionPlanMap>(Token.CompanyID, $"id={id}");
            });
        }
        [HttpPost]
        public async Task<Response> DeleteStrategyMap(int map_id)
        {
            return await RespondAsync(async res =>
            {
                await cmd.DeleteAsync<CommissionStrategyMap>(Token.CompanyID, $"flow_id={map_id}");
            });
        }
        [HttpPost]
        public async Task<string> CheckBeforeDeletePlan(int id)
        {
            cmd.CommandText = $"select company_id from commission_strategy_client where company_id={Token.CompanyID} and plan_id = {id}  limit 1";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                return "方案已被使用过,无法删除";
            }
            cmd.CommandText = $"select company_id from commission_strategy_class where company_id={Token.CompanyID} and plan_id = {id} limit 1";
            ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                return "方案已被使用过了,无法删除";
            }
            return "";
        }
    }
}
