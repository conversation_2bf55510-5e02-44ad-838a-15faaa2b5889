﻿@page
@model ArtisanManage.Pages.ImportInfoModel
@{
    Layout = null;
    var operKey = Request.Query["operKey"].ToString();
}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>ImportInfo</title>
    @*<link href="~/css/component.css" rel="stylesheet" />*@
    <script src="~/js/jquery.min.js"></script>

    <link rel="stylesheet" href="~/MiniJsLib/jquery.dialog.css?v=@Html.Raw(Model.Version)">
    @*<script src="~/MiniJsLib/jquery.dialog.js?v=@Html.Raw(Model.Version)"></script>*@
    <link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)">
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>

    <link rel="stylesheet" href="~/css/DataForm.css?v=@Html.Raw(Model.Version)" type="text/css" />
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css?v=@Html.Raw(Model.Version)" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.js?v=@Html.Raw(Model.Version)"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.export.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxbuttons.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmyinput.js?v=@Html.Raw(Model.Version)"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxwindow.js"></script>

    @*<script src="~/js/commission.js"></script>*@

    <style>
        .btn {
            width: 210px;
            height: 70px;
            margin-right: 60px;
            margin-left: 60px;
            margin-top: 45px;
            font-size: 20px;
            border-radius: 10px;
            float: left;
            border:solid 2px #eee;
        }


        .dialog_foot :last-child {
            width: 120px;
        }

        .dialog_foot {
            border: none !important;
        }

        #div_check {
            margin-top: 10px;
        }

        .el-tabs__nav-scroll {
            background: #e8e8e8
        }

        #tab-basic {
            background: #e8e8e8
        }

        .el-tabs__active-bar {
            display: none;
        }
        #root {
            height: 98%;
            margin: 20px;
        }
        #page {
            border: solid 1px #eee;
            height: 98%;
        }
    </style>


</head>
<body>

    <div id="root">

        <el-tabs  id="page" tab-position="left" v-model="activeTab" >
            <el-tab-pane label="基础信息" name="basic">
                <button  v-for="(item,index) in baseItem" :key="index" class="btn" @@click="showDialog(item)">{{item[0]}} </button>

            </el-tab-pane>
            <el-tab-pane label="期初余额" name="initial">
                <button v-for="(item,index) in balanceItem" :key="index" class="btn" @@click="showDialog(item)">{{item[0]}} </button>

            </el-tab-pane>
            <el-tab-pane label="历史单据" name="history">
                <button v-for="(item,index) in sheetItem" :key="index" class="btn" @@click="showDialog(item)">{{item[0]}} </button>
            </el-tab-pane>
            <el-tab-pane label="其他" name="other" v-if="companyId===1296">
                <button v-for="(item,index) in contractItem" :key="index" class="btn" @@click="showDialog(item)" > {{item[0]}} </button>

            </el-tab-pane>
        </el-tabs>


        <el-dialog :title="selectedItem[0]" :visible.sync="dialogOperate" :show-close="false" width="40%" color="#e3e3e3">
            <form style="font-size:16px;">
                <p>第一步 下载模板，填入数据：</p>
                <a id="downloadLink" href="javascript:void(0)" onclick="downloadFile('https://yingjiang.obs.myhuaweicloud.com/download/档案导入模板.xlsx','@(Model.companyName)_导入模板.xlsx')">下载模板</a>
                <p>第二步 选择模板，导入：</p>
                <input type="file" id="import-file" name="file" accept=".xlsx" />
            </form>
            
            <div style="margin-top:20px;">
                <div v-if="selectedItem[2]">
                    <label style="margin-top:5px;font-size:13px; color:red;text-align:left;">{{selectedItem[2]}} </label>
                </div>
                <div v-if="selectedItem[3]">
                    <label style="margin-top:5px;font-size:13px; color:#727171  ;text-align:left;">{{selectedItem[3]}} </label>
                </div>
            </div>

            <span slot="footer" class="dialog-footer" :show-close="false">
                <button @@click="uploadFile()">确定</button>
                <button @@click="dialogOperate = false">取消</button>
            </span>
        </el-dialog>


        <el-dialog :title=" selectedItem[0] " :visible.sync="dialogProcess" :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false" :before-close="clearProcess" color="#e3e3e3" width="30%" top="20vh">
            <el-progress :text-inside="true" :stroke-width="26" :percentage="percentage" v-if="!success" color="#409eff"></el-progress>
            <span v-if="success"> 导入成功</span>
        </el-dialog>


        <div id="popMessage" style="display:none">
            <div style="height:30px;background-color:#fff; text-align:center;">
            <span style="font-size:20px;">导入结果</span></div>
            <div id="divMessage" style="overflow-y:scroll;">

            </div>
        </div>


    </div>

    <script>
        var company_id_vue=@Html.Raw(Model.company_id);
        var bizStartDate = '@Html.Raw(Model.bizStartDate)';

        /// js实现跨域下载文件（由于a标签下载跨域文件download属性失效做的更改）
        // 下载
        function downloadFile(url, filename) {
            getBlob(url, function (blob) {
                saveDownloadFile(blob, filename);
            })
        }
        function getBlob(url, cb) {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.responseType = 'blob';
            xhr.onload = function () {
                if (xhr.status === 200) {
                    cb(xhr.response);
                }
            };
            xhr.send();
        }
        // 保存
        function saveDownloadFile(blob, filename) {
            if (window.navigator.msSaveOrOpenBlob) {
                navigator.msSaveBlob(blob, filename);
            } else {
                var link = document.createElement('a');
                var body = document.querySelector('body');

                link.href = window.URL.createObjectURL(blob);
                link.download = filename;

                // fix Firefox
                link.style.display = 'none';
                body.appendChild(link);

                link.click();
                body.removeChild(link);

                window.URL.revokeObjectURL(link.href);
            }
        }

    </script>

    <script type="text/javascript">
        var vm = new Vue({
            el: '#root',
            data() {
                return {
                    dialogOperate: false,
                    dialogProcess: false,
                    percentage: 0,
                    startTimer: null,
                    success: false,
                    selectedItem: [],
                    companyId :company_id_vue,
                    baseItem: [
                        ["商品档案", "ImportItems?", "", ""],
                        ["客户档案", "ImportClients?", "", ""],
                        ["供应商档案", "ImportSupplierInfo?type=S", "", ""],
                        ["上次售价", "ImportLastedPrice?", "", ""],
                        ["线路", "ImportVisit?", "在导入客户档案后，在客户档案excel中，填入客户名 行程 线路 ", ""],
                        ["价格方案", "ImportPricePlan?", "", ""],
                        ["商品图片", "ImportItemsPhoto?", "", ""],
                        ["促销方案", "ImportPromotionStrategy?", "", ""],
                        ["拜访记录", "ImportVisitRecords?", "", ""]

                    ],
                    balanceItem: [
                        ["应收款余额", "ImportArrears?", "产生虚拟销售单，多次导入则累加", "欠款时间应早于系统启用时间"],
                        ["预收款余额", "ImportPregetBalance?", "产生一张预收款单，多次导入则累加",  "欠款时间应早于系统启用时间"],
                        ["预付款余额", "ImportPrepayBalance?", "产生一张预付款单，多次导入则累加", "欠款时间应早于系统启用时间"],
                        ["应付款余额", "ImportDue?", "产生虚拟采购单，多次导入则累加", "欠款时间应早于系统启用时间"],
                        ["定货会余额", "ImportOrderedBalance?", "根据剩余未还货数量，产生一张新的定货会单据", "欠款时间应早于系统启用时间"],
                        ["陈列协议", "ImportDispalyagreement?", "", ""],
                    ],
                    sheetItem: [
                        ["销售订单", "ImportSaleOrderSheet?", "填待转单则占用库存，否则不占用", "不占用库存的订单交易时间应早于系统启用时间"],
                        ["销售单", "ImportSaleSheet?", "不影响库存，仅填主表影响往来余额，无往来则只填详表", "交易时间应早于系统启用时间"],
                        ["收款单", "ImportGetArrears?", "导入收款单前，必须先导入销售单、费用支出单这些产生欠款的单据 (注意:单据欠款金额为开单当时的欠款)", "交易时间应早于系统启用时间"],
                        ["预收款单", "ImportPrePaySheet?", "产生历史预收款单据", "交易时间应早于系统启用时间"],
                        ["采购单", "ImportBuySheet?", "", "交易时间应早于系统启用时间"],
                        ["付款单", "ImportPayArrears?", "", "交易时间应早于系统启用时间"],
                        ["预付款单", "ImportPaySheet?", "", "交易时间应早于系统启用时间"],
                        ["费用收入单", "ImportFeeOut?", "", "交易时间应早于系统启用时间"],
                        ["调拨单", "ImportMoveSheet?", "", "交易时间应早于系统启用时间"],
                    ],
                    contractItem: [
                        ["历史合同", "ImportContracHistory?", "", ""],
                        ["代理信息", "ImportAgentInfo?", "", ""]
                    ],
                    activeTab: "basic"
                }
            },
            mounted() {
                this.load();
                this.$nextTick(() => {
                    const tabs = this.$refs.tabs;
                    this.changeTabColor('basic');

                });
            },
            methods: {
                load() {
                    $("#popMessage").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 500, width: 450, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                    if (bizStartDate == '') {
                        this.balanceItem.forEach(item => item[3] = "");
                        this.sheetItem.forEach(item => item[3] = "");
                    } else {
                        this.balanceItem.forEach(item => {
                            if (item[0] != "陈列协议") item[3] += `【${bizStartDate}】`;
                        });
                        this.sheetItem.forEach(item => item[3] += `【${bizStartDate}】`);
                    }
                },
                uploadFile() {

                    if (!$('#import-file').val()){
                        bw.toast("未选择模板")
                        return
                    }


                    var formData = new FormData();

                    let file = $("#import-file")[0].files[0];

                    //if (file.size > 1024 * 1024 * 40) {
                    //    bw.toast("文件不能超过50M")
                    //    return
                    //}
                    if (file.name.indexOf('@(Model.companyName)')==-1) {
                        bw.toast("模板文件名须包含商贸公司名称！")
                        return
                    }


                    function sha256(buffer) {
                        return crypto.subtle.digest('SHA-256', buffer).then(function(hash) {
                          return hex(hash);
                        });
                    }

                    // 将字节数组转换为十六进制字符串
                    function hex(buffer) {
                        var hexCodes = [];
                        var view = new DataView(buffer);
                        for (var i = 0; i < view.byteLength; i += 4) {
                          var value = view.getUint32(i);
                          var stringValue = value.toString(16);
                          var padding = '00000000';
                          var paddedValue = (padding + stringValue).slice(-padding.length);
                          hexCodes.push(paddedValue);
                        }
                        return hexCodes.join('');
                    }
                    var reader = new FileReader();

                    var isFileOK=false
                    // 监听 FileReader 加载完成事件
                    reader.addEventListener('load', ()=>{
                      // 计算文件哈希值
                        // var hash = sha256(reader.result);
                        isFileOK = true

                        if(file.name.indexOf(`_${g_company_id}_`)==-1){
                            var tm=getDateTimeText(new Date())
                            tm=tm.replace(/\:/g,'')
                            tm=tm.replace(/\-/g,'')
                            tm=tm.replace(/\ /g,'_')
                            let arr = file.name.split(".");
                            Object.defineProperty(file,'name',{
			                   writable:true,
		                    })
		                    file.name = `${arr[0]}_${g_company_id}_${tm}.${arr[arr.length-1]}`
                        }

                        formData.append('file', file, file.name);
                        this.startProgress()


                        let that = this
                        $.ajax({
                            url: `@Html.Raw(Model.CoolieServerUri)/api/ImportInfo/${this.selectedItem[1]}&operKey=@operKey`,
                            //此处禁止改动，会导致服务器压力 url: `/api/ImportInfo/${this.selectedItem[1]}&operKey=@operKey`,
                            type: "post",
                            data: formData,
                            processData: false,
                            contentType: false,
                            /*headers:{
                             "Access-Control-Allow-Origin":"*",
                             "cache-control": "no-cache",
                             "If-Modified-Since":"0"
                            },*/
                            success: function (res) {
                                if (res.msg == "操作成功") {
                                    that.percentage = 100
                                    that.success = true

                                } else {
                                    $('#divMessage').html(res.msg)
                                    $('#popMessage').jqxWindow('open');
                                }
                                that.clearProcess()
                            //    form[0].reset();
                            },
                            error: function (err) {
                              bw.toast("连接失败,请刷新重试")
                              that.clearProcess()
                            //    form[0].reset();
                            }
                        })
                    })

                    // 读取文件数据

                    reader.readAsArrayBuffer(file)
                    setTimeout(() => {
                        if (!isFileOK) {
                             bw.toast("文件已改变,请重新选择")
                        }
                    },1000)


                },
                showDialog(item) {
                    this.dialogOperate = true;
                    this.selectedItem = item;

                },
                startProgress() {
                    this.dialogOperate = false
                    this.dialogProcess = true
                    var that = this
                    this.startTimer = setInterval(() => {

                        $.ajax({
                            url:"@Html.Raw(Model.CoolieServerUri)/api/ImportInfo/GetCurrentImportProgress?operKey=@operKey",
                            success: (res) => {

                                if (res.processedCount == null) {
                                    res.processedCount = 0
                                    res.total = 0
                                }
                                that.percentage = Number((res.processedCount / res.total) * 100).toFixed(0)
                                console.log(that.percentage)

                            }
                        })
                    }, 5000);
                },
                clearProcess() {
                    this.dialogProcess = false
                    this.percentage = 0
                    this.success=false
                    clearInterval(this.startTimer)
                },
                changeTabColor(tab) {
                    const activeTab = tab;
                    const tabs = document.querySelectorAll(".el-tabs__item")
                    tabs.forEach(tab => {
                        //debugger
                        if (tab.getAttribute("aria-controls") === `pane-${activeTab}`) {
                            tab.style.backgroundColor = "#fff";
                        } else {
                            tab.style.backgroundColor = "";
                        }
                    });
                }
            },
            watch: {
                activeTab(newTab, oldTab) {
                    this.previousTab = oldTab;
                    this.changeTabColor(newTab);
                }
            }
        })
        window.g_company_id=@Html.Raw(Model.company_id);
    </script>
</body>
</html>