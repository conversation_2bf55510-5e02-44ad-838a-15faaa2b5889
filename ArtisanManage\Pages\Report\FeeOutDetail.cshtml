@page
@model ArtisanManage.Pages.BaseInfo.FeeOutDetailModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var m_db_id = "10";

    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)


                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                    if (args.datafield == "sheet_no") {
                        var sheet_id = args.row.bounddata.sheet_id;
                        var sheet_type = args.row.bounddata.sheet_type;
                        if (sheet_type == '费用支出单') window.parent.newTabPage(sheet_type, `Sheets/FeeOutSheet?sheet_id=${sheet_id}`);
                        if (sheet_type == '其他收入单') window.parent.newTabPage(sheet_type, `Sheets/FeeOutSheet?sheet_id=${sheet_id}`);
                        if (sheet_type == '销售单') window.parent.newTabPage(sheet_type, `Sheets/SaleSheet?sheet_id=${sheet_id}`);
                        if (sheet_type == '销售退货单') window.parent.newTabPage(sheet_type, `Sheets/SaleSheet?sheet_id=${sheet_id}`);
                        if (sheet_type == '采购单') window.parent.newTabPage(sheet_type, `Sheets/BuySheet?sheet_id=${sheet_id}`);
                        if (sheet_type == '采购退货单') window.parent.newTabPage(sheet_type, `Sheets/BuySheet?sheet_id=${sheet_id}`);
                        if (sheet_type == '预收款单') window.parent.newTabPage(sheet_type, `Sheets/PrepaySheet?sheet_id=${sheet_id}`);
                        if (sheet_type == '预付款单') window.parent.newTabPage(sheet_type, `Sheets/PrepaySheet?sheet_id=${sheet_id}`);
                         if (sheet_type == '收款单') window.parent.newTabPage(sheet_type, `Sheets/GetArrearsSheet?sheet_id=${sheet_id}`);
                         if (sheet_type == '付款单') window.parent.newTabPage(sheet_type, `Sheets/GetArrearsSheet?sheet_id=${sheet_id}`);
                        if (sheet_type == '订货会') window.parent.newTabPage(sheet_type, `Sheets/OrderItemSheet?sheet_id=${sheet_id}`);
                        if (sheet_type == '采购费用分摊单') window.parent.newTabPage(sheet_type, `Sheets/FeeApportionSheet?sheet_id=${sheet_id}`);
                        if (sheet_type == '销售费用分摊单') window.parent.newTabPage(sheet_type, `Sheets/SaleFeeApportionSheet?sheet_id=${sheet_id}`);
                    }
                });
                QueryData();


            });


    </script>
</head>

<body>
    <style>
        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }
    </style>
    <div style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">

            <div style="float:none;height:0px; clear:both;"></div>

        </div>

        <button onclick="QueryData()" style="margin-right:0px;margin-top:30px;border-radius: 3px 0px 0px 3px">查询</button>


        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;margin-top:30px;">导出</button>

    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>

</body>
</html>