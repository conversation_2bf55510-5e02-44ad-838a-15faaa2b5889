﻿@page
@model ArtisanManage.Pages.CwPages.CashBankOpBalViewModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        window.AllowEdit = @Html.Raw(Model.AllowEdit).ToString().ToLower();
        window.StartPeriod = '@Html.Raw(Model.StartPeriod)';

        var RowIndex = -1;
    	var newCount = 1;
        var itemSource = {};
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)

            if (!AllowEdit && !StartPeriod) {
                bw.toast('如需使用现金银行模块，请在【设置-业务结账】中启用业务期间',3000);
            }

            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                if (args.datafield == "sub_code") {
                    if (!AllowEdit){
                        bw.toast('业务开账月已结账，无法修改期初余额', 2500);
                        return;
                    }
                    if (args.originalEvent.button == 2) return;
                    let rowdata = $('#gridItems').jqxGrid('getrowdata', args.rowindex);
                    $('#popItem').jqxWindow('open');
                    $("#popItem").jqxWindow('setContent', `<iframe src="CashBankOpBalEdit?operKey=${g_operKey}&qrcode_id=${rowdata.qrcode_id}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                }
            });

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 400, width: 550, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            $('#start_period').text(StartPeriod);

            QueryData();
            //$('#mask').css('display', 'none');
            //$('#mask').css('display','flex');
        });

        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "CashBankOpBalEdit") {
                $("#popItem").jqxWindow('close');
                location.reload();
            }
        });
 
        //清空余额
    	/*function Clear(){
            jConfirm(`该操作将清空所有现金银行期初余额、现金银行余额表、现金银行明细账数据，确认初始化？`, function () {
                $.ajax({
                    url: '/api/CashBankOpBalView/Clear',
                    type: 'POST',
                    contentType: 'application/json',
                    async: false,
                    data: JSON.stringify({ operKey: g_operKey }),
                    success: function (data) {
                        console.log('cashbank balance clear success');
                        if (data.result === 'OK') {
                            bw.toast('初始化完成', 3000);
                        }
                        else {
                            bw.toast(data.msg, 3000);
                        }
                    },
                    error: function (xhr) {
                        console.log("返回响应信息：" + xhr.responseText);
                    }
                });
            }, "");
        }*/

        /*function Import(){
            jConfirm(`确认使用启用日期${StartPeriod}导入其之前的余额作为期初？`, function () {
                $.ajax({
                    url: '/api/CashBankOpBalView/Import',
                    type: 'POST',
                    contentType: 'application/json',
                    async: false,
                    data: JSON.stringify({ operKey: g_operKey, period: StartPeriod }),
                    success: function (data) {
                        console.log('cashbank balance clear success');
                        if (data.result === 'OK') {
                            bw.toast('导入完成', 3000);
                        }
                        else {
                            bw.toast(data.msg, 3000);
                        }
                    },
                    error: function (xhr) {
                        console.log("返回响应信息：" + xhr.responseText);
                    }
                });
            }, "");
        }*/
    </script>

    <style>
        #clearSvg{
            display:none;
        }

        /*#shuoming{
            margin-right: 20px;
            display: inline-block;
            margin-top: 5px;
            border-radius: 50%;
            background-color: darkgrey;
            width: 20px;
            height: 20px;
            color: #fff;
            font-size: 12px;
            cursor: pointer;
        }*/

        #mask {
            position: fixed; /*设置为固定定位*/
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255,0.8); /*使用半透明的白色作为背景色*/
            z-index: 9999; /*设置遮罩层的层级*/
            display: none;
            align-items: center;
            justify-content: center;
            color: dimgrey;
            font-size: 18px;
        }

        #shuoming_ul{
            padding: 20px 20px 20px 40px;
            font-size:16px;
            line-height: 25px;
        }
    </style>
</head>

<body>
    <div style="display:block;padding-top:10px;">
        <div id="divHead" class="headtail" style="display:inline-block;justify-content:space-around;">
            <span id="start_period_label" style="font-size:16px;display:inline-block;padding-top:5px;padding-left:5px;">启用日期：</span>
            <span id="start_period" style="font-size:15px;display:inline-block;padding-top:5px;"></span>
            <div style="display:inline-block;float:none;height:0px; clear:both;"></div>
        </div>
        <div id="divHeadRight" style="margin-right:20px;float:right;display:inline-block;">
            @*<button id="shuoming" title="说明" onclick="$('#jqxwindow_shuoming').jqxWindow('open');">?</button>*@
            @*<button style="display:inline-block;" onclick="Clear()">初始化</button>*@
            @*<button style="display:inline-block;" onclick="Import()">导入</button>*@
            <button style="display:inline-block;" onclick="ExportExcel()">导出</button>
        </div>
    </div>
    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:100%;margin-bottom:10px;"></div>

    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">现金银行期初余额</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

    @*<div id='jqxwindow_shuoming' style="display:none;">
        <div>说明</div>
        <div style="background-color:#f9f9f9">
            <ul id="shuoming_ul">
                <li>现金银行账户启用日期，在【设置-公司设置-业务-现金银行】中修改，格式为0000-00-00，给出的参考日期为财务开账期初或开户日期所在月月初，建议使用参考日期或在此之前没有任何业务单据的月初日，或财务开账期初，存在账户余额或日期格式不正确则无法修改；</li>
                <li>如果启用日期之前某一账户有用在任一业务单据支付方式中，则提示“该启用日期前存在业务单据/财务数据，现金银行历史余额将无法记录在现金银行报表中，确认以此启用日期修改账户初始余额？”</li>
                <li>账户期初只允许在启用日期当月、且下个月及以后没有发生额时无限次修改，如果启用日期的下一个月及以后某一账户有用在任一业务单据支付方式中，则该账户期初只允许修改一次，如果一定要修改第二次，则选择本页面【初始化】功能清除账户余额后可修改，例如：启用日期为2023-06-01，如果在2023-07-01及以后没有任何相关业务单据使用账户“支付宝”作为支付方式，则2023年6月内可以随时修改“支付宝”的账户期初金额，一旦做了第一张2023年7月发生的单据，则“支付宝”期初余额不再允许修改；如果启用日期为2023-11-01，且2023年11月前有业务单据，2023年12月也有业务单据，此时每个账户只能修改一次期初金额；</li>
                <li>【现金银行期初余额-初始化】只清空账户余额和明细账，保留启用日期，清空余额后可修改启用日期。</li>
                <li>【现金银行期初余额-导入】导入的是自启用日期起所有支付方式包含某一账户的已审核业务单据的余额合计作为发生额，业务单据包括：销售单，退货单，定货会，采购单，采购退货单，收款单，付款单，预收款单，预付款单，费用支出单，其他收入单，转账单；只有初始化后才能导入；导入将替换原值；导入不包括停用账户。</li>
                </ul>
            </div>
        </div>*@

    <div id="mask">处理中…</div>
</body>
</html>

