﻿using System;
using System.Collections.Generic;
using System.Drawing;

namespace ArtisanManage.Models
{
    public class PromotionModel
    {
        public string company_id { get; set; }
        public int promotion_id {  get; set; }
        public string promotion_name {  get; set; }
        public string start_time {  get; set; }
        public string end_time {  get; set; }
        public string departments_id {  get; set; }
        public string groups_id {  get; set; }
        public string ranks_id {  get; set; }
        public string regions_id {  get; set; }
        public string promotion_content {  get; set; }
        public string promotion_scope {  get; set; }
        public string promotion_type { get; set; }
    }
    public class PromotionContentModel
    {
        public List<PromotionContentDetailModel> gifts { get; set; }
        public List<PromotionContentDetailModel> sources { get; set; }
        public string promotionType = "combine";
        public List<string> promotionImg = [];
    }
    public class PromotionExcelRow
    {
        public string item_name {  get; set; }
        public string promotion_name { get; set; }
        public string group_name { get; set; }
        public string amount_str { get; set; }
        public string sub_amount { get; set; }
        public string start_time { get; set; }
        public string end_time { get; set; }

    }
    public class PromotionContentDetailModel {
        public string amount {  get; set; }
        public string remark { get; set; }
        public string unit_no {  get; set; }
        public string items_id {  get; set; }

        public string wholesale_price { get; set; }

        public string items_name { get; set;}
        public List<ItemUnitInfoModel> otherUnitInfo { get; set; }
        public int sub_amount { get; set;}
        public string item_images { get; set; }

    }
    public class ItemUnitInfoModel
    {
        public string unit_no { get; set; }
        public string unit_type { get; set; }
        public string unit_factor { get; set; }
        public string wholesale_price { get; set; } = "0";
    }

    }
