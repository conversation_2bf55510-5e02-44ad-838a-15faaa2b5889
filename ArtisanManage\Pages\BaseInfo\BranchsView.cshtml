@page
@model ArtisanManage.Pages.BaseInfo.BranchsViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());

        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "BranchEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()

                    }
                    else
                    {
                        var now_arr = new Array
                        var row = {
                            branch_id: rs.data.record.branch_id,
                            branch_name: rs.data.record.branch_name,
                            branch_addr: rs.data.record.branch_addr,
                            branch_type: rs.data.record.branch_type_name,
                            status: rs.data.record.cls_status_name,
                            allow_negative_stock: rs.data.record.allow_negative_stock == "true" ? "是" : "否",
                            allow_negative_stock_order: rs.data.record.allow_negative_stock_order == "true" ? "是" : "否",
                            negative_stock_accordance: rs.data.record.negative_stock_accordance == "usable" ? "可用库存" : "实际库存"
                        }
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;


                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        //$('#gridItems').jqxGrid('setcellvalue', rs.data.record.boundindex, "branch_type", rs.data.record.branch_type_name);
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "branch_name",rs.data.record.branch_name);
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "branch_addr", rs.data.record.branch_addr);
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "branch_type", rs.data.record.branch_type_name);
                    QueryData();

                }
                $("#popItem").jqxWindow('close');
            };
        });

    	    var newCount = 1;

        function btnAddItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="BranchEdit?operKey=${g_operKey}" width="100%" height="100%" frameborder="no"></iframe>`);
        }
        function btnAddItemPosition_click(e) {
            $('#popItemPosition').jqxWindow('open');
            $("#popItemPosition").jqxWindow('setContent', `<iframe src="BranchPositionEdit?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }
        function btnBatchSetPosition() {
            //1.选择仓库
            //2.选择快捷库位方式：仓库-货架-货架层
            //3.仓库从几号开始、几位数
            //4.货架从几号开始、几位数
        }
        function onGridRowContextMenuClick(gridID, menuID, rowIndex) {

            var rows = $('#gridItems').jqxGrid('getrows')
            var row = rows[rowIndex]
            console.log(menuID)
            if (menuID == 'BatchOperation') {
                $('#gridItems').jqxGrid('showcolumn', 'sys_check')
                var a = $('#popBatchOperation')
                $('#popBatchOperation').css("display", "block")
            }
        }
        function popBatchSetDlg(fld) {
            if (fld == "position") {
                var win = $(window);
                var screenWidth = win.width();
                var screenHeight = win.height() - 100;
                $('#popSet').jqxWindow({
                    width: screenWidth,
                    height: screenHeight,
                    resizable: true,
                    draggable: true,
                    showCloseButton: true,
                    autoOpen: false,
                    zIndex: 9999,
                })
                var data =[
                    {id:4747,branch_name:"总仓"},
                    { id: 4748, branch_name: "总仓2" },
                    { id: 4749, branch_name: "总仓3" }
                ]
                $("#popSet").jqxWindow('setContent', `<iframe src="../BaseInfo/BatchSetBranchPosition?operKey=${g_operKey}" width="100%" height="100%" scrolling="auto" frameborder="no" id="brnchPosition"></iframe>`);
                document.getElementById("brnchPosition").onload = function () {
                    document.getElementById("brnchPosition").contentWindow.postMessage(JSON.stringify(data), '*');
                }
                $('#popSet').jqxWindow('open');
            }
        }
        function btnClose_Clicked() {
            $('#popSet').css("display", "none")
            $('#div_set').empty()
            $('#set_head').empty()
        }
        function onGridRowEdit(rowIndex) {
            var branch_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, "branch_id");
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="BranchEdit?operKey=' + g_operKey + '&branch_id=' + branch_id + '" width="100%" height="100%" frameborder="no"></iframe>');
        }

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
                if (!window.ForSelect) {

                    //$('#btnSelectItems').hide()
                    $('#gridItems').jqxGrid('hidecolumn', 'sys_check')
                }

                $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

                $("#gridItems").on("cellclick", function (event) {
                    // event arguments.
                    var args = event.args;
                    if (args.datafield == "branch_name") {
                        if (args.originalEvent.button == 2) return;
                        //var branch_id = args.row.bounddata.i;
                       // var branch_name = args.row.bounddata.branch_name;
                        if (ForSelect) {
                            var branch_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "branch_id");
                            var branch_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "branch_name");
                            var msg = {
                                msgHead: 'BranchsView', action: 'select', branch_id: branch_id, branch_name: branch_name
                            };
                            window.parent.postMessage(msg, '*');
                        }
                        else {
                            onGridRowEdit(args.rowindex);
                            //$('#popItem').jqxWindow('open');
                            // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                        }
                    }
                });


                $("#Cancel").on('click', function () {
                    for (var i = 0; i < 10; i++) {
                        $('#jqxgrid').jqxGrid('deleterow', i);
                        $('#jqxgrid').jqxGrid('addrow', i, {})
                    }
                });
                $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, maxHeight: "90%",height:600, width: 850, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                //$("#popSet").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 800, width: 850, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
                });

                QueryData();
            });
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        .label_name {
            line-height: 32px;
            margin-left: 10px;
        }

        .label_content {
            width: 120px;
            height: 30px;
            margin-left: 10px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }

        #popBatchOperation {
            width: 100px;
            height: 200px;
            position: fixed;
            top: 25%;
            left: 40%;
            z-index: 999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            display: none;
            text-align: center;
            cursor: pointer;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
        }

        #div_close {
            height: 16px;
            width: 20px;
            cursor: pointer;
            position: relative;
            left: 43px;
            top: 0px;
        }

        #div_close:hover {
            background: #ddd;
        }

        .magic {
            width: 100px;
            height: 25px;
        }

        .magic:hover {
            background: #ddd;
        }

        #popSet {
            width: 500px;
            height: 300px;
            position: fixed;
            top: 27%;
            left: 30%;
            z-index: 999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            padding: 5px;
            display: none;
            text-align: center;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
        }
    </style>
</head>

<body>

    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;">
        <div style="display:inherit">
            <div style="display: flex;"><label class="label_name">状态</label> <div id="status" class="label_content"></div></div>
            <input id="searchString" class="margin" placeholder="请输入简拼/名称" />
            <button onclick="QueryData()" class="margin">查询</button>
        </div>
        
        <div>
            <button onclick="btnAddItem_click()" class="margin">新增仓库</button>
            <!--<button onclick="btnAddItemPosition_click()" class="margin">新增库位</button>
            <button onclick="btnBatchSetPosition()" class="margin" style="width:120px">批量设置库位</button>-->
        </div>
    </div>

    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:100%;margin-bottom:10px;"></div>


    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">仓库信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    @*<div id="popBatchOperation">
    <svg id="div_close" onclick=" this.parentNode.style.display = 'none'">
    <use xlink:href="/images/images.svg?v=@Html.Raw(Model.Version)#close" />
    </svg>
    <div class="magic " onclick="popBatchSetDlg('position')">批量设置库位</div>
    </div>
    <div id="popSet" style="display: none;">
        <div id="popSetCaption" style="background-color:#fff;width:100%;display:flex;flex-direction:column">
            <span style="padding:10px 0;width:100%;font-size:20px;display:flex;justify-content:center">批量设置库位</span>
        </div>
        <div style="overflow:hidden;"> </div>
    </div>*@

</body>
</html>