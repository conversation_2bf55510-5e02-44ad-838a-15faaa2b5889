@page
@model ArtisanManage.Pages.BaseInfo.LoanPartnerEditModel
@{
    Layout = null;
    ///jkkll
} 
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>LoanPartnerEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    
    <script type="text/javascript">

        window.g_operKey = '@Html.Raw(Model.OperKey)';

        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            window.onresize();
            debugger
            $('#partner_name input').on('input', function () {
               
                $('#py_str input').val(this.value.ToPinYinCode());
            });
        });
        window.onresize = function () {

        };
    </script>
</head>
<body>
    <div id="divHead" class="headtail" style="width:500px;padding-right:0px;"></div> 
    <div style="text-align:center;margin-top:20px;">
        <button id="btnSave" onclick="btnSave_checkData();" style="margin-right:50px;">保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>
<script type="text/javascript">
    function btnSave_checkData(bCopy, callback) {
        debugger
        var formFlds = getFormData();
        if (formFlds.errMsg) {
            return;
        }
        
        $.ajax({
            url: '../api/LoanPartnerEdit/Save',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formFlds),
            success: function (data) {
                if (data.result == 'OK') {
                    var msg = { msgHead: 'LoanPartnerEdit', action: 'add', data: data };
                    window.parent.postMessage(msg, '*');
                }
                else {
                    bw.toast(data.msg,3000);
                }
            },
            error: function (response, ajaxOptions, thrownError) {
                bw.toast('error' + response);
            }
        });
    }
</script>