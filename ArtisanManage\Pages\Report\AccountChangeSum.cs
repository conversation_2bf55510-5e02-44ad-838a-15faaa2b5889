﻿
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class AccountChangeSumModel : PageQueryModel
    {
        public string flagForCwImport = "";
        public AccountChangeSumModel(CMySbCommand cmd, bool useMainDb = false) : base(Services.MenuId.accountChangeSum)
        {
            if (useMainDb) this.Database = "";
            this.cmd = cmd;
            this.PageTitle = "客户往来汇总表";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, CompareOperator="=", QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, SqlFld="sm.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59", 
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"supcust_id",new DataItem(){ FldArea="divHead", Title="客户", LabelFld="sup_name", ButtonUsage="list", CompareOperator="=", SqlFld="sc.supcust_id",SqlForOptions=CommonTool.selectSupcust } },
                {"group_id",new DataItem(){Title="渠道",FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
                {"showNoData",new DataItem(){FldArea="divHead",Title="显示无发生额的客户",CtrlType="jqxCheckBox",ForQuery=false,Value="false"}}

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, Sortable=true,
                     ZeroAsEmpty = "true",


                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"supcust_id",    new DataItem(){Title="客户编号",  Width="250",SqlFld="sc.supcust_id",HideOnLoad=true,Hidden=true}},
                       {"sup_name",    new DataItem(){Title="客户名称", Sortable=true,  Width="150",SqlFld=""}},
                       {"balance",     new DataItem(){Title="当前总欠款", Sortable=true,Width="100",SqlFld="qk.balance",ShowSum=true }},
                       {"prepay",new DataItem(){Title="预收款", Width="60", Sortable=true, FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                       {"prepay_strart",    new DataItem(){Title="初", Sortable=true,  Width="50",SqlFld=" cast((COALESCE(YS_end,0) - (COALESCE(s_over.YS_add,0) - COALESCE(s_over.YS_reduce,0)) - (COALESCE(s_between.YS_add,0) - COALESCE(s_between.YS_reduce,0))) as decimal(18,2))",ShowSum=true}},
                       {"prepay_add",    new DataItem(){Title="增", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.YS_add,0) as decimal(18,2))",Linkable=true,ShowSum=true}},
                       {"prepay_reduce",    new DataItem(){Title="减", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.YS_reduce,0) as decimal(18,2))",Linkable=true,ShowSum=true}},
                       {"prepay_end",    new DataItem(){Title="末", Sortable=true,  Width="50",SqlFld=" cast((COALESCE(YS_end,0) - (COALESCE(s_over.YS_add,0) - COALESCE(s_over.YS_reduce,0))) as decimal(18,2))",ShowSum=true}},
                                }
                            }
                       } },
                       {"arrear",new DataItem(){Title="应收款", Width="80", Sortable=true, FuncGetSubColumns = async (col) =>
                            new ColumnsResult{

                                Columns=new Dictionary<string,DataItem>()
                                {
                       {"arrear_strart",    new DataItem(){Title="初", Sortable=true,  Width="50",SqlFld=" cast((COALESCE(QK_end,0) - (COALESCE(s_over.QK_add,0) - COALESCE(s_over.QK_reduce,0)) - (COALESCE(s_between.QK_add,0) - COALESCE(s_between.QK_reduce,0))) as decimal(18,2))",ShowSum=true}},
                       {"arrear_add",    new DataItem(){Title="增", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.QK_add,0) as decimal(18,2))",Linkable=true,ShowSum=true}},
                       {"arrear_reduce",    new DataItem(){Title="减", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.QK_reduce,0) as decimal(18,2))",Linkable=true,ShowSum=true}},
                       {"arrear_end",    new DataItem(){Title="末", Sortable=true,  Width="50",SqlFld=" cast((COALESCE(QK_end,0) - (COALESCE(s_over.QK_add,0) - COALESCE(s_over.QK_reduce,0))) as decimal(18,2))",ShowSum=true}},

                                }
                            }
                       } },
                       {"total",new DataItem(){Title="往来合计（预收-应收）", Width="160", Sortable=true, FuncGetSubColumns = async (col) =>
                            new ColumnsResult{

                                Columns=new Dictionary<string,DataItem>()
                                {
                       {"total_strart",    new DataItem(){Title="初", Sortable=true,  Width="50",SqlFld=" cast((COALESCE(YS_end,0) - (COALESCE(s_over.YS_add,0) - COALESCE(s_over.YS_reduce,0)) - (COALESCE(s_between.YS_add,0) - COALESCE(s_between.YS_reduce,0))) as decimal(18,2)) - cast((COALESCE(QK_end,0) - (COALESCE(s_over.QK_add,0) - COALESCE(s_over.QK_reduce,0)) - (COALESCE(s_between.QK_add,0) - COALESCE(s_between.QK_reduce,0))) as decimal(18,2))",ShowSum=true}},
                       {"total_add",    new DataItem(){Title="增", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.YS_add,0)-COALESCE(s_between.QK_add,0) as decimal(18,2))",ShowSum=true}},
                       {"total_reduce",    new DataItem(){Title="减", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.YS_reduce,0)-COALESCE(s_between.QK_reduce,0) as decimal(18,2))",ShowSum=true}},
                       {"total_end",    new DataItem(){Title="末", Sortable=true,  Width="50",SqlFld=" cast((COALESCE(YS_end,0) - (COALESCE(s_over.YS_add,0) - COALESCE(s_over.YS_reduce,0))) as decimal(18,2)) - cast((COALESCE(QK_end,0) - (COALESCE(s_over.QK_add,0) - COALESCE(s_over.QK_reduce,0))) as decimal(18,2))",ShowSum=true}},

                                }
                            }
                       } },


                     },

                     QueryFromSQL=@"
FROM
	(SELECT distinct supcust_id FROM prepay_balance pb WHERE company_id =~COMPANY_ID
	union
	SELECT distinct supcust_id FROM arrears_balance pb WHERE company_id =~COMPANY_ID ) t
LEFT JOIN ( select supcust_id, sum(balance) YS_end from prepay_balance where company_id =~COMPANY_ID GROUP BY supcust_id ) ys ON ys.supcust_id = t.supcust_id
LEFT JOIN ( select supcust_id, sum(balance) QK_end,balance from arrears_balance where  company_id =~COMPANY_ID GROUP BY supcust_id,balance ) qk ON qk.supcust_id = t.supcust_id
LEFT JOIN ( SELECT supcust_id,
    SUM ( CASE WHEN sub_type = 'QK' AND sheet_type in ('X','YS','DH') THEN change_amount ELSE 0 END ) AS QK_add,
    SUM ( CASE WHEN sub_type = 'QK' AND sheet_type in ('SK','T','ZC') THEN -change_amount ELSE 0 END ) AS QK_reduce,
    SUM ( CASE WHEN sub_type = 'YS' and (sheet_type in ('DH','DHTZ_zr','ZC') or (sheet_type='YS' and change_amount>0))  THEN change_amount ELSE 0 END ) AS YS_add,
    SUM ( CASE WHEN sub_type = 'YS' and (sheet_type in ('X','DHTZ_zc','SK') or (sheet_type='YS' and change_amount<0)) THEN -change_amount ELSE 0 END ) AS YS_reduce
    FROM client_account_history
    WHERE red_flag IS NULL AND company_id =~COMPANY_ID AND happen_time >= '~VAR_startDay' AND happen_time <= '~VAR_endDay' GROUP BY supcust_id
) s_between ON T.supcust_id = s_between.supcust_id
LEFT JOIN ( SELECT supcust_id,
    SUM ( CASE WHEN sub_type = 'QK' AND sheet_type in ('X','YS','DH') THEN change_amount ELSE 0 END ) AS QK_add,
    SUM ( CASE WHEN sub_type = 'QK' AND sheet_type in ('SK','T','ZC') THEN -change_amount ELSE 0 END ) AS QK_reduce,
    SUM ( CASE WHEN sub_type = 'YS' and sheet_type in ('YS','DH','DHTZ_zr')  THEN change_amount ELSE 0 END ) AS YS_add,
    SUM ( CASE WHEN sub_type = 'YS' and sheet_type in ('X','DHTZ_zc','SK')  THEN -change_amount ELSE 0 END ) AS YS_reduce
    FROM client_account_history
    WHERE red_flag IS NULL AND company_id =~COMPANY_ID AND happen_time > '~VAR_endDay' and happen_time <= cast(concat(CURRENT_DATE::text,' 23:59:59.999') as timestamp) GROUP BY supcust_id
) s_over ON T.supcust_id = s_over.supcust_id
left join info_supcust sc  on t.supcust_id=sc.supcust_id
where sc.company_id=~COMPANY_ID  and supcust_flag  in ('C','CS') ~VAR_showNoData 
",

                     QueryGroupBySQL = "",
                     QueryOrderSQL="ORDER BY supcust_id "
                  }
                }
            };
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {

            SQLVariables["startDay"] = DataItems["startDay"].Value;
            //if (Convert.ToDateTime(DataItems["endDay"].Value) > DateTime.Now.AddDays(1).AddSeconds(-1))
            //{
            //    DataItems["endDay"].Value = $"{DateTime.Now.ToString("yyyy-MM-dd")} 23:59:59.999";
            //}
            SQLVariables["endDay"] = DataItems["endDay"].Value;
            //SQLVariables["supcust_id"] = DataItems["supcust_id"].Value == "" ? "" : $"and sc.supcust_id = {DataItems["supcust_id"].Value}";
            if (DataItems["showNoData"].Value.ToLower() == "true")
            {
                SQLVariables["showNoData"] = "  ";
            }
            else
            {
                SQLVariables["showNoData"] = " and (COALESCE(s_between.YS_add,0) !=0 or COALESCE(s_between.YS_reduce,0) !=0 or COALESCE(s_between.QK_add,0) !=0 or COALESCE(s_between.QK_reduce,0)!=0) ";
            }


        }




    }



    [Route("api/[controller]/[action]")]
    public class AccountChangeSumController : QueryController
    {
        public AccountChangeSumController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            AccountChangeSumModel model = new AccountChangeSumModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            AccountChangeSumModel model = new AccountChangeSumModel(cmd);

            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            AccountChangeSumModel model = new AccountChangeSumModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
