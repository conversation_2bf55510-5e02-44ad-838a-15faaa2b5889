﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class BorrowItemModel : PageQueryModel
    {
        public BorrowItemModel(CMySbCommand cmd) : base(Services.MenuId.borrowItem)
        {
            this.cmd = cmd;
            this.PageTitle = "借还货明细表";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time",ForQuery = false, CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time",ForQuery = false,   CompareOperator="<",Value = CPubVars.GetDateText(DateTime.Now.Date) + " 23:59",
                    JSDealItemOnSelect =@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"brand_id",new DataItem(){Title="品牌",FldArea="divHead",LabelFld="brand_name",ButtonUsage="list",CompareOperator="=",SqlFld="ip.item_brand",ForQuery = false,
                    SqlForOptions = CommonTool.selectBrands}},
                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",SqlFld="sd.item_id",ForQuery = false,DropDownWidth="300",QueryByLabelLikeIfIdEmpty=true,
                SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode   }},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="sm.supcust_id",ForQuery = false,
                  SqlForOptions=CommonTool.selectSupcust  } },
                {"seller_id",new DataItem(){Title="业务员", FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="seller_id",ForQuery = false,
                    SqlForOptions ="select oper_id as v,oper_name as l from info_operator where company_id=~COMPANY_ID and is_seller and (status=1 or status is null)"}},
                {"branch_id",new DataItem(){Title="仓库",FldArea="divHead",LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.branch_id",
                     SqlForOptions=CommonTool.selectBranch,ForQuery = false}},


                {"remark",new DataItem(){Title="行备注",FldArea="divHead",TextAsValue=true, LabelFld="remark_name",ButtonUsage="list",CompareOperator="like",ForQuery = false,
                    SqlForOptions ="select brief_id as v,brief_text as l from info_sheet_detail_brief where sheet_type='X'"}},
                {"sheet_no",new DataItem(){Title="单号", FldArea="divHead",CompareOperator="like",ForQuery = false }},
                {"type",new DataItem(){FldArea="divHead",Title="类型",LabelFld = "type_name",Value="all",Label="所有",ButtonUsage = "list",CompareOperator="=",ForQuery = false,
                    Source = @"[ {v:'all',l:'所有',condition:""true""},
                                 {v:'borrow',l:'借货',condition:""sd.trade_type='J'""},
                                 {v:'return',l:'还货',condition:""sd.trade_type='H'""}
                                 ]"
                }},


                // {
                //    "senders_id",
                //    new DataItem()
                //    {
                //        FldArea = "divHead", Title = "送货员",Checkboxes = true, LabelFld = "sm.senders_name", ButtonUsage = "list",
                //        DealQueryItem = status => ","+status+",",ForQuery = false,
                //        SqlForOptions=CommonTool.selectSenders,  //SqlForOptions = "select oper_id as v,oper_name as l,py_str as z from info_operator",
                //        CompareOperator = "like"
                //    }
                //},

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates = true,
                      Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",new DataItem(){SqlFld="B.sheet_id",Hidden=true,HideOnLoad = true}},
                       {"sheet_no",     new DataItem(){Title="单据编号", Sortable=true,    Width="100",SqlFld="B.sheet_no" ,Linkable = true}},
                       {"sheet_type",   new DataItem(){Title="类型",     Width="50",SqlFld="sheet_type"}},
                       {"happen_time",     new DataItem(){Title="交易时间", Sortable=true, Width="100",SqlFld="happen_time"}},
                       {"approve_time",     new DataItem(){Title="审核时间", Sortable=true, Width="100",SqlFld="approve_time"}},
                       {"sup_name",     new DataItem(){Title="客户", Sortable=true,       Width="100",SqlFld="sup_name"}},
                       //{"sup_addr",     new DataItem(){Title="地址",         Width="10%",SqlFld="sc.sup_addr",Hidden = true}},
                       {"oper_name",    new DataItem(){Title="业务员",     Width="80",SqlFld="oper_name"}},
                       {"senders_name",    new DataItem(){Title="送货员",      Width="80",SqlFld="senders_name"}},
                       {"branch_name",    new DataItem(){Title="仓库",    Width="100"}},
                       {"item_name",    new DataItem(){Title="商品名称",    Width="100",SqlFld="item_name"}},

                       //{"s_barcode",new DataItem(){Title = "条码",Width="8%",SqlFld="u.barcode" } },
                       {"quantity1",     new DataItem() {Title="纯数", Width="80",Hidden=true,
                           SqlFld="quantity1 ",CellsAlign="right",
                        FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"unit_no",     new DataItem() {Title="单位",Hidden=true, Width="50",SqlFld="unit_no ",CellsAlign="right",}},
                       {"quantity",     new DataItem() {Title="数量", Sortable=true, Width="50",
                           SqlFld="quantity",CellsAlign="right",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty ="";
                               if(sumColumnValues["quantity_b"]!="") sQty+= sumColumnValues["quantity_b"]+"大";
                               if(sumColumnValues["quantity_m"]!="") sQty+= sumColumnValues["quantity_m"]+"中";
                               if(sumColumnValues["quantity_s"]!="") sQty+= sumColumnValues["quantity_s"]+"小";
                               return sQty;
                           }

                       }},
					{"b_quantity",     new DataItem() {Title="大单位数", Width="80",Hidden=true,ShowSum=true,
						   CellsAlign="right",
						    FuncDealMe=(value)=>{return value=="0"? "":value; },
					   }},

					  {"type",   new DataItem(){Title="大小", Hidden=true,HideOnLoad = true,Sortable=true,CellsAlign="right", Width="70",SqlFld="type"}},
                      {"quantity_b",   new DataItem(){Title="大数", Hidden=true,HideOnLoad = true,ShowSum=true,Sortable=true, CellsAlign="right", Width="70",
                          SqlFld="quantity_b" }},
                      {"quantity_m",   new DataItem(){Title="中数",Hidden=true,HideOnLoad = true, ShowSum=true,Sortable=true, CellsAlign="right", Width="70",
                        SqlFld="quantity_m"}},
                      {"quantity_s",   new DataItem(){Title="小数", Hidden=true,HideOnLoad = true,ShowSum=true,Sortable=true, CellsAlign="right", Width="70",
                          SqlFld="quantity_s"
                      }},

                       {"remark",   new DataItem(){Title="商品备注", Sortable=true, CellsAlign="left", Width="80",SqlFld="remark"}}
                     },
                     QueryFromSQL=@"
FROM 
(
    SELECT sm.sheet_id                                      as sheet_id,
        sm.sheet_no                                                                              as sheet_no,
        (case WHEN sd.trade_type = 'J' THEN '借货' when sd.trade_type = 'H' then '还货' END)      as sheet_type,
        sm.happen_time                                                                           as happen_time,
        sm.approve_time                                                                          as approve_time,
        sc.sup_name                                                                              as sup_name,
        io.oper_name                                                                             as oper_name,
        sm.senders_name                                                                          as senders_name,
        branch_name                                                                              as branch_name,
        ip.item_name                                                                             as item_name,
        sd.quantity * sd.inout_flag * (-1)                                                       as quantity1,
        sd.unit_no                                                                               as unit_no,
        concat(sd.quantity * sd.inout_flag * (-1), sd.unit_no)                                   as quantity,
        round((sd.quantity * sd.inout_flag * (-1) * sd.unit_factor/mub.unit_factor)::numeric,3)  as b_quantity,
        (case
            when itu.unit_type = 's' then '小'
            else (case when itu.unit_type = 'b' then '大' else '中' end) end)               as type,
        case when itu.unit_type = 'b' then sd.quantity * inout_flag*(-1) else null end as quantity_b,
        case when itu.unit_type = 'm' then sd.quantity * inout_flag*(-1) else null end as quantity_m,
        case when itu.unit_type = 's' then sd.quantity * inout_flag*(-1) else null end as quantity_s,
        sd.remark as remark
         
    FROM sheet_sale_detail sd

    LEFT JOIN info_item_multi_unit itu on itu.unit_no = sd.unit_no and itu.company_id = ~COMPANY_ID and itu.item_id = sd.item_id
    LEFT JOIN info_item_multi_unit mub on mub.unit_type = 'b' and mub.company_id = ~COMPANY_ID and mub.item_id = sd.item_id
    LEFT JOIN sheet_sale_main sm on sd.sheet_id = sm.sheet_id and sm.company_id = ~COMPANY_ID
    LEFT JOIN sheet_status_order oss on sd.sheet_id = oss.sheet_id and oss.company_id = ~COMPANY_ID
    LEFT JOIN info_supcust sc ON sm.supcust_id = sc.supcust_id and sc.company_id = ~COMPANY_ID
    LEFT JOIN info_operator io on io.oper_id = sm.seller_id and io.company_id = ~COMPANY_ID
    LEFT JOIN info_branch ib on ib.branch_id = sm.branch_id and ib.company_id = ~COMPANY_ID
    LEFT JOIN info_item_prop ip on ip.item_id = sd.item_id and ip.company_id = ~COMPANY_ID
    LEFT JOIN info_item_multi_unit u on sd.item_id = u.item_id and u.unit_type = 's' and u.company_id = ~COMPANY_ID
     

    WHERE sd.trade_type in ('J', 'H')
    and sd.company_id = ~COMPANY_ID
    and red_flag is null
    and approve_time is not null
    and (sm.happen_time >= '~VAR_STARTDAY')
    and (sm.happen_time < '~VAR_ENDDAY')
    ~VAR_ITEM_ID ~VAR_BRAND_ID ~VAR_SUPCUST_ID ~VAR_SELLER_ID ~VAR_BRANCH_ID ~VAR_TYPE ~VAR_REMARK ~VAR_SHEET_NO

    UNION

    SELECT   sm.sheet_id                                                                          as sheet_id,
             sm.sheet_no                                                                          as sheet_no,
             (case WHEN sd.trade_type = 'J' THEN '借货' when sd.trade_type = 'H' then '还货' END) as sheet_type,
             sm.happen_time                                                                       as happen_time,
             sm.approve_time                                                                      as approve_time,
             sc.sup_name                                                                          as sup_name,
             io.oper_name                                                                         as oper_name,
             sm.senders_name                                                                      as senders_name,
             branch_name,
             ip.item_name                                                                         as item_name,
             (sd.quantity * sd.inout_flag * (-1))                                                 as quantity1,
             sd.unit_no                                                                           as unit_no,
             concat(sd.quantity * sd.inout_flag * (-1), sd.unit_no)                               as quantity,
             round((sd.quantity * sd.inout_flag * (-1) * sd.unit_factor/mub.unit_factor)::numeric,3)  as b_quantity,
             (case when itu.unit_type = 's' then '小' else (case when itu.unit_type = 'b' then '大' else '中' end) end) as type,
             case when itu.unit_type = 'b' then sd.quantity * inout_flag*(-1) else null end as quantity_b,
             case when itu.unit_type = 'm' then sd.quantity * inout_flag*(-1) else null end as quantity_m,
             case when itu.unit_type = 's' then sd.quantity * inout_flag*(-1) else null end as quantity_s,
             sd.remark   as remark

     FROM borrow_item_detail sd

        LEFT JOIN info_item_multi_unit itu on itu.unit_no = sd.unit_no and itu.company_id = ~COMPANY_ID and itu.item_id = sd.item_id
        LEFT JOIN info_item_multi_unit mub on mub.unit_type = 'b' and mub.company_id = ~COMPANY_ID and mub.item_id = sd.item_id
        LEFT JOIN borrow_item_main sm on sd.sheet_id = sm.sheet_id and sm.company_id = ~COMPANY_ID
        LEFT JOIN sheet_status_order oss on sd.sheet_id = oss.sheet_id and oss.company_id = ~COMPANY_ID
        LEFT JOIN info_supcust sc ON sm.supcust_id = sc.supcust_id and sc.company_id = ~COMPANY_ID
        LEFT JOIN info_operator io on io.oper_id = sm.seller_id and io.company_id = ~COMPANY_ID
        LEFT JOIN info_branch ib on ib.branch_id = sm.branch_id and ib.company_id = ~COMPANY_ID
        LEFT JOIN info_item_prop ip on ip.item_id = sd.item_id and ip.company_id = ~COMPANY_ID
        LEFT JOIN info_item_multi_unit u on sd.item_id = u.item_id and u.unit_type = 's' and u.company_id = ~COMPANY_ID
             
    WHERE sd.company_id = ~COMPANY_ID
        and red_flag is null
        and approve_time is not null
        and (sm.happen_time >= '~VAR_STARTDAY')
        and (sm.happen_time < '~VAR_ENDDAY')
        ~VAR_ITEM_ID ~VAR_BRAND_ID ~VAR_SUPCUST_ID ~VAR_SELLER_ID ~VAR_BRANCH_ID ~VAR_TYPE ~VAR_REMARK ~VAR_SHEET_NO) B",
                 
                      QueryOrderSQL=" order by approve_time desc"
                  }
                }
            };
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
           string startDay = DataItems["startDay"].Value;
           string endDay = DataItems["endDay"].Value;
           string brand_id = DataItems["brand_id"].Value;
           string item_id = DataItems["item_id"].Value;
           string supcust_id = DataItems["supcust_id"].Value;
           string seller_id = DataItems["seller_id"].Value;
           string branch_id = DataItems["branch_id"].Value;
           string remark = DataItems["remark"].Value;
           string sheet_no = DataItems["sheet_no"].Value;
           //string senders_id = DataItems["senders_id"].Value;
           string type = DataItems["type"].Value;
            //DataItems["startDay"].ForQuery = false;
            //DataItems["endDay"].ForQuery = false;
            //DataItems["supcust_id"].ForQuery = false;
            //DataItems["item_id"].ForQuery = false;
            //DataItems["brand_id"].ForQuery = false;
            //DataItems["branch_id"].ForQuery = false;
            //DataItems["remark"].ForQuery = false;
            //DataItems["sheet_no"].ForQuery = false;
            //DataItems["senders_id"].ForQuery = false;
            //DataItems["type"].ForQuery = false;

           this.SQLVariables["STARTDAY"] = @$"{startDay}";
           this.SQLVariables["ENDDAY"] = @$"{endDay}";
           this.SQLVariables["ITEM_ID"] = "";
           this.SQLVariables["BRAND_ID"] = "";
           this.SQLVariables["SUPCUST_ID"] = "";
           this.SQLVariables["SELLER_ID"] = "";
           this.SQLVariables["BRANCH_ID"] = "";
           this.SQLVariables["REMARK"] = "";
           this.SQLVariables["SHEET_NO"] = "";
           this.SQLVariables["SENDERS_ID"] = "";
           this.SQLVariables["TYPE"] = "";
            
            if(brand_id.IsValid())
            {
                this.SQLVariables["BRAND_ID"] = @$"and ip.item_brand = '{brand_id}'";
            }
            if(item_id.IsValid())
            {
                this.SQLVariables["ITEM_ID"] = @$"and sd.item_id in ({item_id})";
            }
            if(supcust_id.IsValid())
            {
                this.SQLVariables["SUPCUST_ID"] = @$"and sm.supcust_id = '{supcust_id}'";
            }
            if(seller_id.IsValid())
            {
                this.SQLVariables["SELLER_ID"] = @$"and seller_id = '{seller_id}'";
            }
            if(branch_id.IsValid())
            {
                this.SQLVariables["BRANCH_ID"] = @$"and sm.branch_id = '{branch_id}'";
            }
            if(brand_id.IsValid())
            {
                this.SQLVariables["BRAND_ID"] = @$"and ip.item_brand = '{brand_id}'";
            }
            if(type=="borrow")
            {
                this.SQLVariables["TYPE"] = @$"and sd.trade_type='J'";
            } 
            if(type == "return")
            {
                this.SQLVariables["TYPE"] = @$"and sd.trade_type='H'";
            }
            if(remark.IsValid())
            {
                this.SQLVariables["REMARK"] = @$" and (remark ilike '%{remark}%') ";
            }
            if(sheet_no.IsValid())
            {
                this.SQLVariables["REMARK"] = @$" and (sm.sheet_no ilike '%{sheet_no}%') ";
            }


        }
        public async Task OnGet()
        {
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
    }



    [Route("api/[controller]/[action]")]
    public class BorrowItemController : QueryController
    {
        public BorrowItemController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BorrowItemModel model = new BorrowItemModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {

            BorrowItemModel model = new BorrowItemModel(cmd);

            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {

            BorrowItemModel model = new BorrowItemModel(cmd);

            return await model.ExportExcel(Request, cmd);
        }
    }
}
