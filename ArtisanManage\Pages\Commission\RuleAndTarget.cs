﻿using ArtisanManage.Enums;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Services;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using System.Collections.Generic;
using System.Linq;

namespace ArtisanManage.Pages
{
    /// <summary>
    /// 提成规则及其应用对象
    /// </summary>
    public class RuleAndTarget
    {
        /// <summary>
        /// 对象Id
        /// </summary>
        public int Target { get; set; }

        public string TargetName { get; set; }

        /// <summary>
        /// 规则名称
        /// </summary>
        public string Rule { get; set; }

        /// <summary>
        /// 对象类型（商品:i，商品类别:c）
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 可能的提成区间
        /// </summary>
        public List<CommissionRate> Rates { get; set; } = new List<CommissionRate>();

        /// <summary>
        /// 提成规则
        /// </summary>
        public CommissionRule CommissionRule { get; set; }

        /// <summary>
        /// 属于此规则的待处理数据
        /// </summary>
        public List<CommissionInfo> Infos { get; set; } = new List<CommissionInfo>();

        public string RuleDisplay (){
            var body = Rule + ";";
            int i = 0;
            CommissionRate rate;
            for (i = 0; i<Rates.Count ; i++)
            {
                rate = Rates[i];
                if (i == Rates.Count - 1 && (rate.To == 0 || rate.To == -0.001))
                {
                    if (CommissionRule.CountMode != CountMode.fix.ToString()) body += $"{ rate.From} ≤ X ";
                    body += $" 销:{ rate.X}";
                    if (CommissionRule.DeductReturned) body += $",退{rate.T}";
                    body += ";";
                }
                else
                {
                    if (CommissionRule.CountMode != CountMode.fix.ToString()) body += $"{rate.From} ≤ X ＜ {rate.To} ";
                    body += $" 销:{rate.X}";
                    if (CommissionRule.DeductReturned) body += $",退{rate.T}";
                    body += ";";
                }
            }
            return body;
        }

        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="commissionDetails"></param>
        public void AggregateTo(List<CommissionDetail> commissionDetails, out string errMsg)
        {
            errMsg = "";
            var infoGroups = Infos.GroupBy(x => 1.0);
            if(CommissionRule.CommissionBy == CommissionBy.price.ToString())
            {
                infoGroups = CommissionRule.ByBigUnit ? Infos.GroupBy(x =>(double) x.b_real_price) : Infos.GroupBy(x =>(double) x.real_price); 
            } 
                foreach (var infoGroup in infoGroups)
                {              
                    var commission = new CommissionDetail(infoGroup, CommissionRule)
                    {
                        Id = Target,
                        Name = TargetName,
                        RuleDisplay = RuleDisplay(),
                        strategy_id = Infos[0].strategy_id,
                        strategy_name = Infos[0].strategy_name,
                        strategy_type = Infos[0].strategy_type,
                        plan_source = Infos[0].plan_source == "client" ? "按客户指定" : "按类别指定",
                        plan_name = Infos[0].plan_name,
                    };
                    CommissionRule.Compute(commission, Rates, infoGroup.Key, out errMsg);
                    if (errMsg != "")
                    {
                        break;
                    }
                    commissionDetails.Add(commission);
                } 
            Infos.Clear();
        }
    }
}
