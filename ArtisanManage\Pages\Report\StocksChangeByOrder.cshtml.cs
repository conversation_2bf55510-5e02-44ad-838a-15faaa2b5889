﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.CodeAnalysis.Operations;
using Newtonsoft.Json.Linq;

namespace ArtisanManage.Pages.BaseInfo
{
    public class StocksChangeByOrderModel : PageQueryModel
    {
        public string unionSql { get; set; } = ""; 
       
        public StocksChangeByOrderModel(CMySbCommand cmd) : base(Services.MenuId.stocksChangeByOrder)
        {
            unionSql = @"
(
    SELECT '' sup_name,d.item_id,produce_date,m.sheet_id,'-1' inventory_sheet_id,sheet_no,'QCKC' sheet_type,sum(inout_flag*quantity*unit_factor) quantity,sum(quantity_positve)quantity_add,sum(quantity_nagetive)quantity_reduce,m.happen_time,m.approve_time,red_flag ,'' as from_branch_name,'' as to_branch_name,-1 senders_id ~VAR_produceDateSql
    FROM sheet_stock_opening_main m
    LEFT JOIN 
    (
        SELECT CASE WHEN inout_flag*quantity*unit_factor > 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_positve,
               CASE WHEN inout_flag*quantity*unit_factor < 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_nagetive,sd.*
        FROM sheet_stock_opening_detail sd
    ) d on m.company_id=d.company_id and m.sheet_id = d.sheet_id
    LEFT JOIN 
    (
        SELECT batch_id,produce_date,batch_no FROM info_item_batch WHERE company_id = ~COMPANY_ID
    ) itb on itb.batch_id = d.batch_id
    WHERE m.company_id=~COMPANY_ID and m.approve_time IS NOT NULL  ~VAR_startDay  ~VAR_endDay ~VAR_branch_id  ~VAR_branch_position ~VAR_produce_date ~VAR_batch_no
    GROUP BY sup_name,item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,inventory_sheet_id,produce_date,senders_id ~VAR_showBatchGroupSql
UNION
    SELECT sup_name,item_id,produce_date,m.sheet_id,'-1' inventory_sheet_id,sheet_no,sheet_type,sum(inout_flag*quantity*unit_factor)quantity,sum(quantity_positve)quantity_add,sum(quantity_nagetive)quantity_reduce,m.happen_time,m.approve_time,red_flag,'' as from_branch_name,'' as to_branch_name,split_part(m.senders_id,',',1)::integer as senders_id ~VAR_produceDateSql
    FROM sheet_sale_main m
    LEFT JOIN 
    (
        SELECT  CASE WHEN inout_flag*quantity*unit_factor > 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_positve,
               CASE WHEN inout_flag*quantity*unit_factor < 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_nagetive,sd.*
        FROM sheet_sale_detail sd
    ) d  on m.company_id=d.company_id and m.sheet_id = d.sheet_id 
    LEFT JOIN info_supcust sc on m.company_id=sc.company_id and m.supcust_id = sc.supcust_id 
    LEFT JOIN 
    (
        SELECT batch_id,produce_date,batch_no FROM info_item_batch WHERE company_id = ~COMPANY_ID
    ) itb on itb.batch_id = d.batch_id 
    WHERE m.company_id=~COMPANY_ID AND not coalesce(M.is_imported,false) ~VAR_startDay  ~VAR_endDay ~VAR_branch_id  ~VAR_branch_position ~VAR_produce_date ~VAR_batch_no
    GROUP BY sup_name,item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,produce_date,inventory_sheet_id,senders_id ~VAR_showBatchGroupSql
UNION
    SELECT sup_name,item_id,produce_date,m.sheet_id,'-1' inventory_sheet_id,sheet_no,sheet_type,sum(inout_flag*quantity*unit_factor)quantity,sum(quantity_positve)quantity_add,sum(quantity_nagetive)quantity_reduce,m.happen_time,m.approve_time,red_flag,'' as from_branch_name,'' as to_branch_name,m.senders_id::integer as senders_id ~VAR_produceDateSql
    FROM borrow_item_main m
    LEFT JOIN 
    (
        SELECT  CASE WHEN inout_flag*quantity*unit_factor > 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_positve,
                CASE WHEN inout_flag*quantity*unit_factor < 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_nagetive,sd.*
        FROM borrow_item_detail sd
    ) d on m.company_id=d.company_id and m.sheet_id = d.sheet_id 
    LEFT JOIN info_supcust sc on m.company_id=sc.company_id and m.supcust_id = sc.supcust_id 
    LEFT JOIN 
    (
        SELECT batch_id,produce_date,batch_no FROM info_item_batch WHERE company_id = ~COMPANY_ID
    ) itb on itb.batch_id = d.batch_id 
    WHERE m.company_id=~COMPANY_ID AND not coalesce(M.is_imported,false) ~VAR_startDay  ~VAR_endDay ~VAR_branch_id  ~VAR_branch_position ~VAR_produce_date ~VAR_batch_no
    GROUP BY sup_name,item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,produce_date,inventory_sheet_id,senders_id ~VAR_showBatchGroupSql
 UNION
    SELECT sup_name,item_id,produce_date,m.sheet_id,'-1' inventory_sheet_id,sheet_no,sheet_type,sum(inout_flag*quantity*unit_factor)quantity,sum(quantity_positve)quantity_add,sum(quantity_nagetive)quantity_reduce,m.happen_time,m.approve_time,red_flag ,'' as from_branch_name,'' as to_branch_name,-1 as senders_id ~VAR_produceDateSql
    FROM sheet_buy_main m
    LEFT JOIN 
    (
        SELECT CASE WHEN inout_flag*quantity*unit_factor > 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_positve,
               CASE WHEN inout_flag*quantity*unit_factor < 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_nagetive,sd.*
        FROM sheet_buy_detail sd
    ) d on m.company_id=d.company_id and m.sheet_id = d.sheet_id
    LEFT JOIN info_supcust sc on m.company_id=sc.company_id and m.supcust_id = sc.supcust_id
    LEFT JOIN 
    (
        SELECT batch_id,produce_date,batch_no FROM info_item_batch WHERE company_id = ~COMPANY_ID
    ) itb on itb.batch_id = d.batch_id 
    WHERE m.company_id=~COMPANY_ID  AND not coalesce(M.is_imported,false)     ~VAR_startDay  ~VAR_endDay ~VAR_branch_id  ~VAR_branch_position ~VAR_produce_date ~VAR_batch_no
    GROUP BY sup_name,item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,produce_date,inventory_sheet_id,senders_id ~VAR_showBatchGroupSql
UNION
    SELECT '' as sup_name,item_id,produce_date,m.sheet_id,'-1' inventory_sheet_id,sheet_no,'ZCC'  as sheet_type,
        sum(inout_flag*quantity*unit_factor)quantity,sum(quantity_positve)quantity_add,sum(quantity_nagetive)quantity_reduce,m.happen_time,m.approve_time,red_flag ,'' as from_branch_name,'' as to_branch_name,-1 senders_id ~VAR_produceDateSql
    FROM sheet_combine_main m
    LEFT JOIN
    (
       SELECT CASE WHEN inout_flag*quantity*unit_factor > 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_positve,
              CASE WHEN inout_flag*quantity*unit_factor < 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_nagetive,sd.*
       FROM sheet_combine_detail sd
    ) d on m.company_id=d.company_id and m.sheet_id = d.sheet_id
    LEFT JOIN 
    (
          SELECT batch_id,produce_date,batch_no FROM info_item_batch WHERE company_id = ~COMPANY_ID
    ) itb on itb.batch_id = d.batch_id 
    WHERE m.company_id=~COMPANY_ID   ~VAR_from_branch_id  and inout_flag*quantity<0  ~VAR_startDay  ~VAR_endDay  ~VAR_branch_position ~VAR_produce_date ~VAR_batch_no
    GROUP BY item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,inventory_sheet_id,produce_date,inout_flag,senders_id ~VAR_showBatchGroupSql
 UNION
    SELECT '' as sup_name,item_id,produce_date,m.sheet_id,'-1' inventory_sheet_id,sheet_no,'ZCR'  as sheet_type,
        sum(inout_flag*quantity*unit_factor)quantity,sum(quantity_positve)quantity_add,sum(quantity_nagetive)quantity_reduce,m.happen_time,m.approve_time,red_flag ,'' as from_branch_name,'' as to_branch_name,-1 senders_id ~VAR_produceDateSql
    FROM sheet_combine_main m
    LEFT JOIN
    (
       SELECT CASE WHEN inout_flag*quantity*unit_factor > 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_positve,
              CASE WHEN inout_flag*quantity*unit_factor < 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_nagetive,sd.*
       FROM sheet_combine_detail sd
    ) d on m.company_id=d.company_id and m.sheet_id = d.sheet_id
    LEFT JOIN 
    (
          SELECT batch_id,produce_date,batch_no FROM info_item_batch WHERE company_id = ~COMPANY_ID
    ) itb on itb.batch_id = d.batch_id 
    WHERE m.company_id=~COMPANY_ID   ~VAR_to_branch_id  and inout_flag*quantity>0  ~VAR_startDay  ~VAR_endDay  ~VAR_branch_position ~VAR_produce_date ~VAR_batch_no
    GROUP BY item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,inventory_sheet_id,produce_date,inout_flag,senders_id ~VAR_showBatchGroupSql
 UNION
    SELECT '' sup_name,item_id,produce_date,m.sheet_id,'-1' inventory_sheet_id,sheet_no,'DR' sheet_type,sum( case when red_flag ='2' then -1 else 1 end * quantity * unit_factor ) quantity,sum( case when red_flag ='2' then -1 else 1 end * quantity * unit_factor ) quantity_add,sum( case when red_flag ='2' then -1 else 1 end * quantity * unit_factor ) quantity_reduce,m.happen_time,m.approve_time,red_flag,fb.branch_name as from_branch_name,tb.branch_name as to_branch_name,m.sender_id as senders_id ~VAR_produceDateSql
    FROM sheet_move_detail d 
    LEFT JOIN sheet_move_main m on d.company_id=m.company_id and m.sheet_id = d.sheet_id 
    LEFT JOIN info_branch fb on fb.company_id = m.company_id and m.from_branch_id=fb.branch_id
    LEFT JOIN info_branch tb on tb.company_id = m.company_id and m.to_branch_id=tb.branch_id
    LEFT JOIN (SELECT batch_id,produce_date,batch_no FROM info_item_batch WHERE company_id = ~COMPANY_ID) itb on itb.batch_id = d.batch_id 
    WHERE m.company_id=~COMPANY_ID  ~VAR_startDay  ~VAR_endDay  AND not coalesce(M.is_imported,false)  ~VAR_combine_branch_id  ~VAR_produce_date ~VAR_batch_no ~VAR_to_branch_id
    GROUP BY sup_name,item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,inventory_sheet_id,fb.branch_name,produce_date,tb.branch_name,senders_id ~VAR_showBatchGroupSql
 UNION
    SELECT '' sup_name,item_id,produce_date,m.sheet_id,'-1' inventory_sheet_id,sheet_no,'DC' sheet_type,sum( case when red_flag ='2' then 1 else -1 end * quantity * unit_factor ) quantity,sum( case when red_flag ='2' then 1 else -1 end * quantity * unit_factor ) quantity_add,sum( case when red_flag ='2' then 1 else -1 end * quantity * unit_factor ) quantity_reduce,m.happen_time,m.approve_time,red_flag,fb.branch_name as from_branch_name,tb.branch_name as to_branch_name,m.sender_id as senders_id ~VAR_produceDateSql
    FROM sheet_move_detail d 
    LEFT JOIN sheet_move_main m on d.company_id=m.company_id and d.sheet_id = m.sheet_id 
    LEFT JOIN info_branch fb on fb.company_id = m.company_id and m.from_branch_id=fb.branch_id
    LEFT JOIN info_branch tb on tb.company_id = m.company_id and m.to_branch_id=tb.branch_id

    LEFT JOIN (SELECT batch_id,produce_date,batch_no FROM info_item_batch WHERE company_id = ~COMPANY_ID) itb on itb.batch_id = d.batch_id 
    WHERE m.company_id=~COMPANY_ID  ~VAR_startDay  ~VAR_endDay  AND not coalesce(M.is_imported,false) ~VAR_combine_branch_id ~VAR_produce_date ~VAR_batch_no ~VAR_from_branch_id
    GROUP BY sup_name,item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,inventory_sheet_id,fb.branch_name,produce_date,tb.branch_name,senders_id ~VAR_showBatchGroupSql
 UNION
    SELECT '' sup_name,item_id,produce_date,m.sheet_id,inventory_sheet_id::text,sheet_no,'YK' sheet_type,sum(inout_flag*quantity*unit_factor) quantity,sum(quantity_positve) quantity_add,sum(quantity_nagetive)quantity_reduce,m.happen_time,m.approve_time,red_flag ,'' as from_branch_name,'' as to_branch_name,-1 as senders_id ~VAR_produceDateSql
    FROM sheet_invent_change_main m
    LEFT JOIN 
    (
       SELECT  CASE WHEN inout_flag*quantity*unit_factor > 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_positve,
               CASE WHEN inout_flag*quantity*unit_factor < 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_nagetive,sd.*
       FROM sheet_invent_change_detail sd
    ) d on m.company_id=d.company_id and m.sheet_id = d.sheet_id 
    LEFT JOIN 
    (
       SELECT batch_id,produce_date,batch_no FROM info_item_batch WHERE company_id = ~COMPANY_ID
    ) itb on itb.batch_id = d.batch_id 
    WHERE m.company_id=~COMPANY_ID and m.approve_time IS NOT NULL  ~VAR_startDay  ~VAR_endDay ~VAR_branch_id  ~VAR_branch_position ~VAR_produce_date ~VAR_batch_no
    GROUP BY sup_name,item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,produce_date,inventory_sheet_id,senders_id ~VAR_showBatchGroupSql
UNION
    SELECT '' sup_name,item_id,produce_date,m.sheet_id,'-1' inventory_sheet_id,sheet_no,sheet_type,sum(inout_flag*quantity*unit_factor) quantity,sum(quantity_positve)quantity_add,sum(quantity_nagetive)quantity_reduce,m.happen_time,m.approve_time,red_flag ,'' as from_branch_name,'' as to_branch_name,-1 as senders_id ~VAR_produceDateSql
    FROM sheet_stock_in_out_main m
    LEFT JOIN 
    (
        SELECT CASE WHEN inout_flag*quantity*unit_factor > 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_positve,
               CASE WHEN inout_flag*quantity*unit_factor < 0 THEN inout_flag*quantity*unit_factor ELSE 0 END AS quantity_nagetive,sd.*
        FROM sheet_stock_in_out_detail sd
    ) d on m.company_id=d.company_id and m.sheet_id = d.sheet_id 
    LEFT JOIN 
    (
        SELECT batch_id,produce_date,batch_no FROM info_item_batch WHERE company_id = ~COMPANY_ID
    ) itb on itb.batch_id = d.batch_id 
    WHERE m.company_id=~COMPANY_ID and m.approve_time IS NOT NULL  ~VAR_startDay  ~VAR_endDay ~VAR_branch_id  ~VAR_branch_position ~VAR_produce_date ~VAR_batch_no
    GROUP BY sup_name,item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,produce_date,inventory_sheet_id,senders_id ~VAR_showBatchGroupSql
) t
";
            this.cmd = cmd;
            this.PageTitle = "库存变化明细表";
            DataItems = new Dictionary<string, DataItem>()
            {
             {"startDay",new DataItem(){Title="开始日期",FldArea="divHead",ForQuery=false, CtrlType="jqxDateTimeInput", SqlFld="approve_time",CompareOperator=">=",QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
            {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", ForQuery=false, CtrlType="jqxDateTimeInput", SqlFld="approve_time", CompareOperator="<",QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
            }},
            {"item_id", new DataItem(){Title="商品名称",FldArea="divHead",Necessary=true, LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="u.item_id",DropDownWidth="300",
                 SearchFields=CommonTool.itemSearchFields, SqlForOptions=CommonTool.selectItemWithBarcode } },
            //{"other_class",new DataItem(){Title="类别",FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",TreePathFld="other_class",MumSelectable="true",CompareOperator="like",
            //   SqlForOptions="SELECT class_id as v,class_name as l,py_str as z,mother_id as pv FROM info_item_class"} },
            {"branch_id",new DataItem(){Title="仓库",FldArea = "divHead",LabelFld="branch_name",ForQuery=false,ButtonUsage="list",CompareOperator="=",
                SqlForOptions=CommonTool.selectBranch } },
             {"branch_position",new DataItem(){Title="库位",FldArea = "divHead",LabelFld="branch_position_name",CONDI_DATA_ITEM="branch_id",ForQuery=false,ButtonUsage="list",CompareOperator="=",
                //SqlForOptions=CommonTool.selectBranchPosition
                SqlForOptions="select branch_position as v,branch_position_name as l from info_branch_position where company_id=~COMPANY_ID  and branch_id = '~CONDI_DATA_ITEM' union select 0 as v,'默认库位' as l "
             } },
             {"produce_date",new DataItem(){Title="生产日期",FldArea="divHead",ForQuery=false,CtrlType="jqxDateTimeInput",ShowTime=false}},
            {"batch_no",new DataItem(){Title="批次",FldArea="divHead",ForQuery=false}},
            {"sheet_type",new DataItem(){Title="单据类型",FldArea="divHead",LabelFld="sheet_type_name",ButtonUsage="list",CompareOperator="=",ForQuery=false,
                    Source = @"[{v:'QCKC',l:'期初库存单'},{v:'X',l:'销售单'},{v:'T',l:'销售退货单'},{v:'CG',l:'采购单'},{v:'CT',l:'采购退货单'},
                                {v:'DR',l:'调入单'},{v:'DC',l:'调出单'},{v:'YK',l:'盘点盈亏单'},{v:'PD',l:'盘点单'},{v:'ZCR',l:'组拆入仓单'},{v:'ZCC',l:'组拆出仓单'},{v:'BS',l:'报损单'},{v:'RK',l:'其他入库单'},{v:'CK',l:'其他出库单'},{v:'',l:'所有'}]"}},

             {"byApproveTime",new DataItem(){FldArea="divHead",Title="按审核时间查询",CtrlType="jqxCheckBox",ForQuery=false,Value="false",AutoRemember=true}},
             {"showRedSheet",new DataItem(){FldArea="divHead",Title="显示红冲单",CtrlType="jqxCheckBox",ForQuery=false,Value="true",AutoRemember=true}},
             {"showBatch",new DataItem(){FldArea="divHead",Title="按产期批次查询",CtrlType="jqxCheckBox",ForQuery=false,Value="",Hidden = false}},
             {"onlyNoBatch",new DataItem(){FldArea="divHead",Title="仅查询无产期/批次",CtrlType="jqxCheckBox",ForQuery=false,Value="",Hidden = false}}

        };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {ShowAggregates=true,
                  Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_no",new DataItem(){Title="单据编号", Width="180",Linkable=true}},
                       {"sheet_id",new DataItem(){Title="单据", Width="180",Hidden=true, HideOnLoad=true}},
                       {"from_branch_name",new DataItem(){Title="出仓", Width="180",Hidden=true}},
                       {"to_branch_name",new DataItem(){Title="入仓", Width="180",Hidden=true}},
                       {"inventory_sheet_id",new DataItem(){Title="单据1", Width="180",Hidden=true, HideOnLoad=true}},
                       {"sup_name",new DataItem(){Title="客户/供应商", Width=""}},
                       {"senders_name",new DataItem(){Title="送货员", Width="90",Hidden=true, SqlFld="io.senders_name"}},
                       {"sheet_type1",new DataItem(){Title="类型",Hidden=true,SqlFld="sheet_type",HideOnLoad=true} },
                       {"sheet_type",new DataItem(){Title="单据类型", Width="100",
                           SqlFld=@"(case when sheet_type = 'CG' then '采购单'  
                                          when sheet_type = 'CT' then '采购退货单' 
                                          when sheet_type='X' then '销售单' 
                                          when sheet_type='JH' then '借货单' 
                                          when sheet_type='HH' then '还货单' 
                                          when sheet_type='T' then '销售退货单' 
                                          when sheet_type = 'DR' then '调入单' 
                                          when sheet_type = 'DC' then '调出单'
                                          when sheet_type = 'PD' then '盘点单'
                                          when sheet_type = 'YK' then '盘点盈亏单'
                                          when sheet_type = 'BS' then '报损单'
                                          when sheet_type = 'ZCR' then '组拆入仓单'
                                          when sheet_type = 'ZCC' then '组拆出仓单'
                                          when sheet_type = 'RK' then '其他入库单'
                                          when sheet_type = 'CK' then '其他出库单'
                                          when sheet_type = 'QCKC' then '期初库存单'
                                          when sheet_type = 'SS' then '门店库存上报单'
                                    end)"}},

                       {"status",new DataItem(){Title = "单据状态",Width="180",
                           SqlFld=@" case when red_flag='1' then '红冲单'  
                                          when red_flag='2' then '红字单' 
                                          else '已审核'
                                    end",Hidden=true,HideOnLoad = true } },
                       {"item_id",new DataItem(){Title="商品",Width="",Hidden=true,HideOnLoad = true,SqlFld = "u.item_id" } },
                       {"item_name",new DataItem(){Title="商品名称", Width="180",Hidden=true}},
                       {"produce_date",new DataItem(){Title="生产日期", Width="180",SqlFld=@"produce_date",JSCellRender="viewProduceDate"}},
                       {"batch_no",new DataItem(){Title="批次", Width="180",GetFromDb=false}},
                       {"before_quantity",new DataItem(){Title="开单前库存", Width="100",SqlFld="''"}},
                       //{"unit_no",new DataItem(){Title="商品名称", Width="180",Hidden=true,SqlFld="mu.unit_no" } },
                       {"add_quantity",new DataItem(){Title="增加数量", Width="100",  
                           SqlFld=" unit_from_s_to_bms ((case when qty_change_add>0 then qty_change_add else 0 end)::float8,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) ",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                            {
                               string sQty = "";
                               if(sumColumnValues["add_quantity_b"]!="") sQty+= sumColumnValues["add_quantity_b"]+"大";
                               if(sumColumnValues["add_quantity_m"]!="") sQty+= sumColumnValues["add_quantity_m"]+"中";
                               if(sumColumnValues["add_quantity_s"]!="") sQty+= sumColumnValues["add_quantity_s"]+"小";
                               return sQty;
                            }

                       }},
                       {"add_quantity_b",new DataItem(){Title="增加数量(大)", Width="100", ShowSum=true,Sortable=true, FuncDealMe=value=>{return value=="0"?"":value;},Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty ('b',(CASE WHEN qty_change_add  > 0 THEN qty_change_add ELSE 0 END )::numeric,b_unit_factor,m_unit_factor,false)"
                       }},
                       {"add_quantity_m",new DataItem(){Title="增加数量(中", Width="100", ShowSum=true,Sortable=true, FuncDealMe=value=>{return value=="0"?"":value;},Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty ('m',(CASE WHEN qty_change_add  > 0 THEN qty_change_add ELSE 0 END )::numeric,b_unit_factor,m_unit_factor,false)"
                       }},
                       {"add_quantity_s",new DataItem(){Title="增加数量(小)", Width="100", ShowSum=true,Sortable=true, FuncDealMe=value=>{return value=="0"?"":value;},Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty ('s',(CASE WHEN qty_change_add  > 0 THEN qty_change_add ELSE 0 END )::numeric,b_unit_factor,m_unit_factor,false)"
                       }},
                       {"reduce_quantity",new DataItem(){Title="减少数量", Width="100",
                           SqlFld=" unit_from_s_to_bms ((case when qty_change_reduce<0 then -1*qty_change_reduce else 0 end)::float8,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) ",
                             FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                            {
                               string sQty = "";
                               if(sumColumnValues["reduce_quantity_b"]!="") sQty+= sumColumnValues["reduce_quantity_b"]+"大";
                               if(sumColumnValues["reduce_quantity_m"]!="") sQty+= sumColumnValues["reduce_quantity_m"]+"中";
                               if(sumColumnValues["reduce_quantity_s"]!="") sQty+= sumColumnValues["reduce_quantity_s"]+"小";
                               return sQty;
                            }
                       }},
                       {"reduce_quantity_b",new DataItem(){Title="减少数量(大)", Width="100", ShowSum=true,Sortable=true, FuncDealMe=value=>{return value=="0"?"":value;},Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty ('b',(case when qty_change_reduce<0 then -1*qty_change_reduce else 0 end)::numeric,b_unit_factor,m_unit_factor,false)"
                       }},
                       {"reduce_quantity_m",new DataItem(){Title="减少数量(中）", Width="100", ShowSum=true,Sortable=true, FuncDealMe=value=>{return value=="0"?"":value;},Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty ('m',(case when qty_change_reduce<0 then -1*qty_change_reduce else 0 end)::numeric,b_unit_factor,m_unit_factor,false)"
                       }},
                       {"reduce_quantity_s",new DataItem(){Title="减少数量(小)", Width="100", ShowSum=true,Sortable=true, FuncDealMe=value=>{return value=="0"?"":value;},Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty ('s',(case when qty_change_reduce<0 then -1*qty_change_reduce else 0 end)::numeric,b_unit_factor,m_unit_factor,false)"
                       }},

                       {"after_quantity",new DataItem(){Title="开单后库存", Width="100",SqlFld="''"}},
                       {"change_qty",new DataItem(){Title="库存变化",Hidden=true,Width="100",SqlFld="qty_change"}},
                       {"change_qty_add",new DataItem(){Title="入库数量",Hidden=true,Width="100",SqlFld="(CASE WHEN qty_change_add  > 0 THEN qty_change_add ELSE 0 END)"}},
                       {"change_qty_reduce",new DataItem(){Title="出库数量",Hidden=true,Width="100",SqlFld="(case when qty_change_reduce<0 then -1*qty_change_reduce else 0 end)"}},
                       {"happen_time",new DataItem(){Title="交易时间", Width="180"}},
                       {"approve_time",new DataItem(){Title="审核时间", Width="180"}},
                       {"b_unit_factor",new DataItem(){Title="大包装率",Hidden=true ,HideOnLoad = true} },
                       {"m_unit_factor",new DataItem(){Title="中包装率",Hidden=true ,HideOnLoad = true } },
                       {"s_unit_factor",new DataItem(){Title="小包装率",Hidden=true ,HideOnLoad = true } },
                       {"b_unit_no",new DataItem(){Title="大单位",Hidden=true  ,HideOnLoad = true} },
                       {"m_unit_no",new DataItem(){Title="中单位",Hidden=true  ,HideOnLoad = true} },
                       {"s_unit_no",new DataItem(){Title="小单位",Hidden=true ,HideOnLoad = true } },
                     },

                     QueryFromSQL=$@"
from  (SELECT t.*,0 stock_qty,round(quantity::numeric,2) qty_change,round(quantity_add::numeric,2) qty_change_add,round(quantity_reduce::numeric,2) qty_change_reduce FROM  
    {unionSql}                                        
    WHERE approve_time is not null  ~VAR_red_flag ~VAR_sheet_type       order by happen_time,sheet_id 
) u 
LEFT JOIN  info_item_prop i on i.company_id=~COMPANY_ID and i.item_id = u.item_id 
LEFT JOIN (SELECT oper_id as senders_id,oper_name as senders_name FROM info_operator WHERE company_id=~COMPANY_ID and is_sender  and COALESCE(status,'1')='1') io on io.senders_id = u.senders_id
LEFT JOIN 
(SELECT item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no 
    FROM crosstab('SELECT item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json FROM info_item_multi_unit WHERE company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
            as errr(item_id int, s jsonb,m jsonb,b jsonb)
   ) mu on mu.item_id = i.item_id
where i.company_id=~COMPANY_ID",

                      QueryOrderSQL="order by ~VAR_time"
                  }
                } 
            }; 
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {

           if(DataItems["byApproveTime"].Value.ToLower()=="false")
            {
                var day = DataItems["startDay"].Value;
                SQLVariables["startDay"] = $"AND M.happen_time >= '{day}'";
                day = DataItems["endDay"].Value;
                SQLVariables["endDay"] = $"AND M.happen_time <= '{day}'";
                SQLVariables["time"] = "happen_time,approve_time";
            }
            else
            {
                var day = DataItems["startDay"].Value;
                SQLVariables["startDay"] = $"AND M.approve_time >= '{day}'";
                day = DataItems["endDay"].Value;
                SQLVariables["endDay"] = $"AND M.approve_time <= '{day}'";
                SQLVariables["time"] = "approve_time";
            }

            if (DataItems["showRedSheet"].Value.ToLower() == "true")
            {
                
                SQLVariables["red_flag"] = "  ";
            }
            else
            {
                SQLVariables["red_flag"] = "and red_flag is null";
            }



            if (DataItems["branch_id"].Value != "")
            {
                SQLVariables["combine_branch_id"] = $"and (from_branch_id ={DataItems["branch_id"].Value} or to_branch_id ={DataItems["branch_id"].Value})";
                SQLVariables["stock_branch_id"] = "and m.branch_id =" + DataItems["branch_id"].Value;
                SQLVariables["branch_id"] = "and COALESCE(d.branch_id,m.branch_id) =" + DataItems["branch_id"].Value;
                SQLVariables["from_branch_id"] = "and from_branch_id =" + DataItems["branch_id"].Value;
                SQLVariables["to_branch_id"] = "and to_branch_id =" + DataItems["branch_id"].Value;
                
            }
            else
            {
                SQLVariables["combine_branch_id"] = " ";
                SQLVariables["branch_id"] = " ";
                SQLVariables["from_branch_id"] = " ";
                SQLVariables["to_branch_id"] = " ";
            }
            if (DataItems["branch_position"].Value != "")
            {
                SQLVariables["branch_position"] = $@" and branch_position = {DataItems["branch_position"].Value}"; ;
                SQLVariables["from_branch_position"] = $@" and from_branch_position = {DataItems["branch_position"].Value}";
                SQLVariables["to_branch_position"] = $@" and to_branch_position = {DataItems["branch_position"].Value}";
            }
            else
            {
                SQLVariables["branch_position"] = "";
                SQLVariables["from_branch_position"] = "";
                SQLVariables["to_branch_position"] = "";
            }

            var sheet_type = DataItems["sheet_type"].Value;

            if (sheet_type != "")
            {
                string[] sheetTypes = sheet_type.Split(',');
                SQLVariables["sheet_type"] = "";
                foreach (var st in sheetTypes)
                {
                    if (SQLVariables["sheet_type"] == "")
                    {
                        SQLVariables["sheet_type"] = $"and sheet_type in ('{st}'";
                    }
                    else{
                        SQLVariables["sheet_type"] += $",'{st}'";
                    }
                }
                SQLVariables["sheet_type"] += $")";

            }
            else
            {
                SQLVariables["sheet_type"] = " " ;
            }
           
            if (DataItems["produce_date"].Value != "")
            {
                SQLVariables["produce_date"] = $@" and produce_date = '{DataItems["produce_date"].Value}'";
            }
            else
            {
                SQLVariables["produce_date"] = "";
            }
            if (DataItems["batch_no"].Value != "")
            {
                SQLVariables["batch_no"] = $@" and batch_no = '{DataItems["batch_no"].Value}'";
            }
            else
            {
                SQLVariables["batch_no"] = "";
            }
            if(DataItems["onlyNoBatch"].Value.ToLower() == "true")
            {
                SQLVariables["produce_date"] = $@" and produce_date is null";
                SQLVariables["batch_no"] = $@" and batch_no is null";
            }

            string showBatchGroupSql = "";
            string produceDateSql = "";
             //var columns = Grids.GetValueOrDefault("gridItems").Columns;
            if (DataItems["showBatch"].Value.ToLower() == "true")
            {
                showBatchGroupSql = ",produce_date,batch_no";
                produceDateSql = ",Substring(COALESCE(produce_date::text,''),1,10) as produce_date_t,COALESCE(batch_no,'') as batch_no";//原来的列本就有produce_date，在开启产期批次查询后再加入produce_date会导致重复列，进而报错
                 this.Grids["gridItems"].Columns["produce_date"].GetFromDb = true;
                this.Grids["gridItems"].Columns["batch_no"].GetFromDb = true;
                this.Grids["gridItems"].Columns["produce_date"].Hidden = false;
                this.Grids["gridItems"].Columns["batch_no"].Hidden = false;
            }
            else
            {
                this.Grids["gridItems"].Columns["produce_date"].Hidden = true;
                this.Grids["gridItems"].Columns["batch_no"].Hidden = true;
                this.Grids["gridItems"].Columns["produce_date"].GetFromDb = false;
                this.Grids["gridItems"].Columns["batch_no"].GetFromDb = false;
                
            }
             SQLVariables["showBatchGroupSql"] = showBatchGroupSql;
            SQLVariables["produceDateSql"] = produceDateSql;


        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            //var sheet_type = DataItems["sheet_type"].Value;
            //var columns = Grids["gridItems"].Columns;

            ////if (sheet_type != "")
            ////{

            ////    columns.Remove("after_quantity");
            ////    columns.Remove("before_quantity");
            ////}
        }

        public override async Task<JsonResult> DealQueriedRecords(dynamic data,dynamic postParams)
        {
            string showBatchGroupSql = "";
            string produceDateSql = "";
            string showBatchGroupSql_sheet = "";
            string produceDateSql_sheet = "";
            string produceDateSql_t = "";
            string xsSql = "";
            string cgSql = "";
            string ykSql = "";
            string crSql = "";
            string zccSql = "";
            string zcrSql = "";
            string drSql = "";
            string dcSql = "";
            bool showBatch = DataItems["showBatch"].Value.ToLower() == "true" ? true : false;
            if (showBatch)
            {
                showBatchGroupSql = ",m.batch_id,produce_date,batch_no";
                showBatchGroupSql_sheet = ",iib.batch_id";
                produceDateSql_sheet = ",COALESCE(iib.batch_id,0) as batch_id";
                produceDateSql = ",COALESCE(m.batch_id,0) as batch_id,produce_date,batch_no";
                produceDateSql_t = " ,s.batch_id,produce_date,batch_no";
                xsSql = " and xs.batch_id = s.batch_id";
                cgSql = " and cg.batch_id = s.batch_id";
                ykSql = " and yk.batch_id = s.batch_id";
                crSql = " and cr.batch_id = s.batch_id";
                zccSql = " and zcc.batch_id = s.batch_id";
                zcrSql = " and zcr.batch_id = s.batch_id";
                drSql = " and dr.batch_id = s.batch_id";
                dcSql = " and dc.batch_id = s.batch_id";
                //produceDateSql = " and Substring(COALESCE(produce_date::text,''),1,10) and COALESCE(batch_no,'')";
            }

            var rows = (Dictionary<int, Dictionary<string, string>>)data.rows;

           // var sheet_type = CPubVars.RequestV(Request, "sheet_type");
            var sheet_type = GetParamFromRequest(Request, postParams, "sheet_type");
            //  GetParamFromRequest(request,)

            if (rows.Count > 0 && sheet_type=="")
            {
                decimal b_unit_factor = 0;
                var firstRow=rows.First().Value;
                 
                if (firstRow.GetValueOrDefault("b_unit_factor").IsValid()) b_unit_factor = CPubVars.ToDecimal(firstRow.GetValueOrDefault("b_unit_factor"));
                decimal m_unit_factor = 0;
                if (firstRow.GetValueOrDefault("m_unit_factor").IsValid()) m_unit_factor = CPubVars.ToDecimal(firstRow.GetValueOrDefault("m_unit_factor"));
                decimal s_unit_factor = 0;
                if (firstRow.GetValueOrDefault("s_unit_factor").IsValid()) s_unit_factor = CPubVars.ToDecimal(firstRow.GetValueOrDefault("s_unit_factor"));
                var b_unit_no = firstRow.GetValueOrDefault("b_unit_no");
                var m_unit_no = firstRow.GetValueOrDefault("m_unit_no");
                var s_unit_no = firstRow.GetValueOrDefault("s_unit_no");
                var item_id = GetParamFromRequest(Request, postParams,"item_id");
              //if(item_id=="") item_id=firstRow.GetValueOrDefault("item_id");
                var branch_id = GetParamFromRequest(Request, postParams, "branch_id");
                var from_branch_id="";
                var to_branch_id="";
                string branch_id_mm = "";
                if (branch_id != "")
                {
                    from_branch_id = "and from_branch_id = " + branch_id;
                    to_branch_id = "and to_branch_id = " + branch_id;
                    branch_id_mm = $" and COALESCE(d.branch_id,m.branch_id) = {branch_id}";
                    branch_id = " and m.branch_id = " + branch_id;
                };
                var branch_position = GetParamFromRequest(Request, postParams, "branch_position");
                var from_branch_position="";
                var to_branch_position="";
                if (branch_position != "")
                {
                    from_branch_position += " and from_branch_position = " + branch_position;
                    to_branch_position = "and to_branch_position = " + branch_position;
                    branch_position = " and branch_position = " + branch_position;
                     
                };
                var produce_date = GetParamFromRequest(Request, postParams, "produce_date");
                if (produce_date != "")
                {
                    produce_date =$" and produce_date = '{ produce_date}'";
                }
                var batch_no = GetParamFromRequest(Request, postParams, "batch_no");
                if (batch_no != "")
                {
                    batch_no =$" and batch_no = '{batch_no}'";
                }
                var onlyNoBatch = GetParamFromRequest(Request, postParams, "onlyNoBatch");
                if(onlyNoBatch.ToLower() == "true")
                {
                    produce_date = $@" and produce_date is null";
                    batch_no = $@" and batch_no is null";
                }
                var showRedSheet = GetParamFromRequest(Request, postParams, "showRedSheet");




                var endDay = CPubVars.RequestV(Request, "endDay");
                var operKey = CPubVars.RequestV(Request, "operKey");
                Security.GetInfoFromOperKey(operKey, out string companyID);
                var lastRow = rows[rows.Keys.Last<int>()];
                var timeCondi = "";

                IFormCollection requestForm = null;
                string byApproveTime = "";

                if (Request.Method == "POST")
                {
                    requestForm = Request.Form;
                }

                if (requestForm != null && requestForm.ContainsKey("params"))
                {
                    // 导出时获取 byApproveTime
                    string encodedParams = requestForm["params"];
                    if (!string.IsNullOrEmpty(encodedParams))
                    {
                        string decodedParams = HttpUtility.UrlDecode(encodedParams);
                        var jsonObj = JObject.Parse(decodedParams);
                        byApproveTime = jsonObj["byApproveTime"]?.ToString();
                    }
                }
                else
                {
                    // 正常查询
                    byApproveTime = CPubVars.RequestV(Request, "byApproveTime");
                }


                byApproveTime = byApproveTime.ToLower();
                
                
                
                if (byApproveTime == "false")
                {
                    timeCondi = $" (M.happen_time > '{lastRow["happen_time"].ToString()}' or ( M.happen_time = '{lastRow["happen_time"].ToString()}' and M.approve_time  > '{lastRow["approve_time"].ToString()}' ))";
                }
                else
                {
                    timeCondi = $" M.approve_time > '{lastRow["approve_time"].ToString()}'";
                }
                var redFlag = "";
                if(CPubVars.RequestV(Request, "showRedSheet") == "false"||showRedSheet.ToLower() == "false")
                {
                    redFlag = " and red_flag is null ";
                }
                string sql = $@"
SELECT 
      (stock_qty +( COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty, 0 ) - COALESCE ( cg_add_qty, 0 ) + COALESCE ( cg_reduce_qty, 0 )- COALESCE ( cr_add_qty, 0 ) + COALESCE ( cr_reduce_qty, 0 )- COALESCE ( zc_add_qty, 0 ) + COALESCE ( zc_reduce_qty, 0 ) - COALESCE ( yk_add_qty, 0 ) + COALESCE ( yk_reduce_qty, 0 ) - COALESCE ( db_add_qty, 0 ) + COALESCE ( db_reduce_qty, 0 ) )) end_qty {produceDateSql_t}
FROM 
( 
    SELECT item_id, sum(stock_qty) as stock_qty {produceDateSql} FROM stock m
    LEFT JOIN info_item_batch iib on iib.batch_id = m.batch_id and iib.company_id = ~COMPANY_ID
    WHERE m.company_id = ~COMPANY_ID {branch_id} {branch_position} {produce_date} {batch_no} GROUP BY item_id {showBatchGroupSql}
) s
LEFT JOIN
(
    SELECT d.item_id as item_id,
        sum(case when inout_flag*quantity <0  then (inout_flag * quantity * unit_factor * (-1))::float4 else 0 end) as xs_add_qty,
        sum(case when inout_flag*quantity  >0  then (inout_flag * quantity * unit_factor)::float4 else 0 end) as xs_reduce_qty {produceDateSql_sheet}
    FROM sheet_sale_detail  d 
    LEFT JOIN sheet_sale_main m on d.sheet_id = m.sheet_id and m.company_id = ~COMPANY_ID
    LEFT JOIN info_item_batch iib on iib.batch_id = d.batch_id and iib.company_id = ~COMPANY_ID
    WHERE d.company_id=~COMPANY_ID and {timeCondi} and m.approve_time is not null {redFlag} AND not coalesce(M.is_imported,false)   {branch_id_mm} {branch_position} {produce_date} {batch_no}
    GROUP BY d.item_id {showBatchGroupSql_sheet}
) xs on s.item_id = xs.item_id {xsSql}
LEFT JOIN
(
    SELECT d.item_id as item_id,
       sum(case when inout_flag*quantity >0  then (inout_flag * quantity * unit_factor)::float4 else 0 end) as cg_add_qty,
       sum(case when inout_flag*quantity  <0  then (inout_flag * quantity * unit_factor * (-1))::float4 else 0 end) as cg_reduce_qty {produceDateSql_sheet}
    FROM sheet_buy_detail  d 
    LEFT JOIN sheet_buy_main m  on   d.sheet_id =m.sheet_id and m.company_id = ~COMPANY_ID
    LEFT JOIN info_item_batch iib on iib.batch_id = d.batch_id and iib.company_id = ~COMPANY_ID
    WHERE  d.company_id=~COMPANY_ID and {timeCondi}  and m.approve_time is not null {redFlag} AND not coalesce(M.is_imported,false) {branch_id_mm} {branch_position} {produce_date} {batch_no}
    GROUP BY d.item_id {showBatchGroupSql_sheet}
) cg on s.item_id = cg.item_id  {cgSql}
LEFT JOIN
(
    SELECT d.item_id as item_id,
      sum(case when inout_flag*quantity  >0  then (inout_flag*quantity * unit_factor)::float4 else 0 end) as yk_add_qty,
      sum(case when inout_flag*quantity <0   then  (inout_flag*quantity * unit_factor*(-1))::float4 else 0 end) as yk_reduce_qty {produceDateSql_sheet}
    FROM sheet_invent_change_detail  d 
    LEFT JOIN sheet_invent_change_main m on d.sheet_id =m.sheet_id  and m.company_id = ~COMPANY_ID
    LEFT JOIN info_item_batch iib on iib.batch_id = d.batch_id and iib.company_id = ~COMPANY_ID
    WHERE d.company_id=~COMPANY_ID and {timeCondi}  and m.approve_time is not null {redFlag} {branch_id} {branch_position} {produce_date} {batch_no}
    GROUP BY  d.item_id {showBatchGroupSql_sheet}
) yk on s.item_id = yk.item_id  {ykSql}
LEFT JOIN
(
    SELECT d.item_id as item_id,
        sum(case when inout_flag*quantity  >0  then (inout_flag*quantity * unit_factor)::float4 else 0 end) as cr_add_qty,
        sum(case when inout_flag*quantity <0   then  (inout_flag*quantity * unit_factor*(-1))::float4 else 0 end) as cr_reduce_qty {produceDateSql_sheet}
    FROM sheet_stock_in_out_detail  d 
    LEFT JOIN sheet_stock_in_out_main m on d.sheet_id =m.sheet_id  and m.company_id = ~COMPANY_ID
    LEFT JOIN info_item_batch iib on iib.batch_id = d.batch_id and iib.company_id = ~COMPANY_ID
    WHERE d.company_id=~COMPANY_ID and {timeCondi}  and m.approve_time is not null {redFlag} {branch_id} {branch_position} {produce_date} {batch_no}
    GROUP BY  d.item_id {showBatchGroupSql_sheet}
) cr on s.item_id = cr.item_id {crSql}

LEFT JOIN
(
    SELECT d.item_id as item_id,sum( quantity*unit_factor ) as zc_add_qty {produceDateSql_sheet}
    FROM sheet_combine_detail  d 
        LEFT JOIN sheet_combine_main m on d.sheet_id =m.sheet_id  and m.company_id = ~COMPANY_ID
    LEFT JOIN info_item_batch iib on iib.batch_id = d.batch_id and iib.company_id = ~COMPANY_ID
    WHERE   d.company_id=~COMPANY_ID and {timeCondi}  and m.approve_time is not null and inout_flag = 1 {redFlag}  {to_branch_id} {branch_position} {produce_date} {batch_no}
    GROUP BY d.item_id {showBatchGroupSql_sheet}
) zcr on s.item_id=zcr.item_id  {zcrSql}
LEFT JOIN
(
    SELECT d.item_id as item_id,sum( quantity*unit_factor ) as zc_reduce_qty {produceDateSql_sheet}
    FROM sheet_combine_detail  d 
        LEFT JOIN sheet_combine_main m on d.sheet_id =m.sheet_id  and m.company_id = ~COMPANY_ID
    LEFT JOIN info_item_batch iib on iib.batch_id = d.batch_id and iib.company_id = ~COMPANY_ID
    WHERE   d.company_id=~COMPANY_ID and {timeCondi}  and m.approve_time is not null and inout_flag = -1 {redFlag}  {from_branch_id} {branch_position} {produce_date} {batch_no}
    GROUP BY d.item_id {showBatchGroupSql_sheet}
) zcc on s.item_id=zcc.item_id  {zccSql}

LEFT JOIN
(
    SELECT d.item_id as item_id,sum( case when m.red_flag ='2' then -1* quantity*unit_factor else quantity*unit_factor end ) as db_add_qty {produceDateSql_sheet} 
    FROM sheet_move_detail  d 
        LEFT JOIN sheet_move_main m on d.sheet_id =m.sheet_id  and m.company_id = ~COMPANY_ID
    LEFT JOIN info_item_batch iib on iib.batch_id = d.batch_id and iib.company_id = ~COMPANY_ID
    WHERE   d.company_id=~COMPANY_ID and {timeCondi}  and m.approve_time is not null {redFlag} 	AND not coalesce(M.is_imported,false)  {to_branch_id} {to_branch_position} {produce_date} {batch_no}
    GROUP BY d.item_id {showBatchGroupSql_sheet}
) dr on s.item_id=dr.item_id  {drSql}
LEFT JOIN
( 
    SELECT d.item_id as item_id, sum( case when m.red_flag ='2' then -1* quantity*unit_factor else quantity*unit_factor end ) as db_reduce_qty {produceDateSql_sheet}
    FROM sheet_move_detail  d 
    LEFT JOIN sheet_move_main m on d.sheet_id =m.sheet_id  and m.company_id = ~COMPANY_ID
    LEFT JOIN info_item_batch iib on iib.batch_id = d.batch_id and iib.company_id = ~COMPANY_ID
    WHERE  d.company_id=~COMPANY_ID and {timeCondi}   and m.approve_time is not null {redFlag}  AND not coalesce(M.is_imported,false){from_branch_id} {from_branch_position} {produce_date} {batch_no}
    GROUP BY d.item_id {showBatchGroupSql_sheet}
)dc on s.item_id=dc.item_id  {dcSql}
WHERE   s.item_id = {item_id}  ";




                sql = sql.Replace("~COMPANY_ID", companyID, StringComparison.OrdinalIgnoreCase);
                var laterChangeQty = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                decimal stockQty = 0;
                Dictionary<string, decimal> stockDic = new Dictionary<string, decimal>();
                if (showBatch)
                {
                    foreach(dynamic stockItem in laterChangeQty)
                    {
                        string key = stockItem.produce_date + stockItem.batch_no;
                        stockDic.Add(key,CPubVars.ToDecimal(stockItem.end_qty));
                    }
                }
                else
                {
                    if (laterChangeQty != null && laterChangeQty.Count > 0)
                    { 
                        stockQty = CPubVars.ToDecimal(((dynamic)laterChangeQty[0]).end_qty);
                    }
                }
                
                string before_quantity = "";
                string after_quantity= "";
                //string before_quantity_s = "";
                string after_quantity_s = "";
                decimal  change_qty = 0;


                var reversedKeys = rows.Keys.Reverse<int>();

                foreach (var k in reversedKeys)
                {
                    var row = rows[k];

                    var sheet_no = row["sheet_no"];
                    if(sheet_no== "T24040746702015")
                    {
                        //stockQty = 4.1m;
                    }
                    if(showBatch)
                    {
                        string key = row["produce_date"] + row["batch_no"];
                        if (!stockDic.ContainsKey(key)) stockDic.Add(key,0);
                        stockQty = stockDic[key];
                    }
                    after_quantity = CommonTool.units_from_s_to_bms_old(stockQty, b_unit_factor, m_unit_factor, s_unit_factor, b_unit_no, m_unit_no, s_unit_no);
                    after_quantity_s = Convert.ToString(stockQty);
                    row["after_quantity"] =after_quantity;
                    row["after_quantity_s"] = after_quantity_s + s_unit_no;

                    change_qty = CPubVars.ToDecimal(row["change_qty"]);
                    stockQty -= change_qty;
                    if (showBatch)
                    {
                        string key = row["produce_date"] + row["batch_no"];
                        stockDic[key] -=change_qty;
                    }
                    before_quantity = CommonTool.units_from_s_to_bms_old(stockQty, b_unit_factor, m_unit_factor, s_unit_factor, b_unit_no, m_unit_no, s_unit_no);
                    row["before_quantity"] = before_quantity;

                }


            }

            return new JsonResult(data);
        }
        public async Task OnGet()
        {  
            await InitGet(cmd);
         
        }
    }



    [Route("api/[controller]/[action]")]
    public class StocksChangeByOrderController : QueryController
    { 
        public StocksChangeByOrderController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues,string condiDataItem)
        {  
            StocksChangeByOrderModel model = new StocksChangeByOrderModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues, condiDataItem);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            StocksChangeByOrderModel model = new StocksChangeByOrderModel(cmd);
             model.Request = Request;
            dynamic records = await model.GetRecordFromQuerySQL(Request, cmd);
            /* var rows =(Dictionary<int, Dictionary<string, string>>) records.Value.rows;

             if (rows.Count > 0)
             {
                 float b_unit_factor = 0;
                 if (rows[0].GetValueOrDefault("b_unit_factor").IsValid()) b_unit_factor = Convert.ToSingle(rows[0].GetValueOrDefault("b_unit_factor"));
                 float m_unit_factor = 0;
                 if (rows[0].GetValueOrDefault("m_unit_factor").IsValid()) m_unit_factor = Convert.ToSingle(rows[0].GetValueOrDefault("m_unit_factor"));
                 float s_unit_factor = 0;
                 if (rows[0].GetValueOrDefault("s_unit_factor").IsValid()) s_unit_factor = Convert.ToSingle(rows[0].GetValueOrDefault("s_unit_factor"));
                 var b_unit_no = rows[0].GetValueOrDefault("b_unit_no");
                 var m_unit_no = rows[0].GetValueOrDefault("m_unit_no");
                 var s_unit_no = rows[0].GetValueOrDefault("s_unit_no");
                 var item_id = CPubVars.RequestV(Request, "item_id");
                 var branch_id = CPubVars.RequestV(Request, "branch_id");
                 //if (branch_id.IsInvalid()) branch_id = "-1";
                 //if (item_id.IsInvalid()) item_id = "-1";
                 var startDay = CPubVars.RequestV(Request, "startDay");
                 var endDay = CPubVars.RequestV(Request, "endDay");
                 var operKey = CPubVars.RequestV(Request, "operKey");
                 Security.GetInfoFromOperKey(operKey, out string companyID);
                 var lastRow = rows[rows.Keys.Last<int>()];
                 var happen_time = lastRow["approve_time"];
                 //var unit_no = lastRow["unit_no"];
                 string sql = @$"SELECT round(COALESCE(s.stock_qty,0),2) stock_qty,round(COALESCE(quantity,0)::numeric,2) change_qty 
                                   FROM {model.unionSql} 
                                   LEFT JOIN stock s on s.item_id = t.item_id and t.branch_id = s.branch_id 
                                  WHERE t.item_id = {item_id} and t.branch_id = {branch_id} and approve_time = '{happen_time}' and approve_time is not null and red_flag is null";
                 sql = sql.Replace("~COMPANY_ID", companyID,StringComparison.OrdinalIgnoreCase);
                 dynamic laterChangeQty = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                 float stockQty = 0;
                 if (laterChangeQty!=null) stockQty = Convert.ToSingle(laterChangeQty.stock_qty);
                 float current_qty = stockQty;
                 var reversedKeys=rows.Keys.Reverse<int>();
                 string before_quantity_unit_no = "";
                 string after_quantity_unit_no = "";
                 foreach (var k in reversedKeys)
                 {
                     var row = rows[k];
                     row.Remove("current_quantity1");

                     before_quantity_unit_no = CommonTool.units_from_s_to_bms(current_qty, b_unit_factor, m_unit_factor, s_unit_factor, b_unit_no, m_unit_no, s_unit_no);

                     row.Add("current_quantity1", before_quantity_unit_no);
                     row.Remove("current_quantity");
                     float change_qty = Convert.ToSingle(row["change_qty"]);
                     current_qty -= change_qty;
                     after_quantity_unit_no = CommonTool.units_from_s_to_bms(current_qty, b_unit_factor, m_unit_factor, s_unit_factor, b_unit_no, m_unit_no, s_unit_no);
                     row.Add("current_quantity", after_quantity_unit_no);
                 }

             }*/


            return records;
        }
        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            StocksChangeByOrderModel model = new StocksChangeByOrderModel(cmd);
            model.Request = Request;
            return await model.ExportExcel(Request, cmd);
        }
    }
}
