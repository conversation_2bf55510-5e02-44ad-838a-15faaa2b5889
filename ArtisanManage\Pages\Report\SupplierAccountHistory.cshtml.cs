using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class SupplierAccountHistoryModel : PageQueryModel
    {
     
        public SupplierAccountHistoryModel(CMySbCommand cmd) : base(Services.MenuId.supplierBusinessHistory)
        {
            this.cmd = cmd;
            this.PageTitle = "供应商往来账";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea = "divHead",CtrlType="jqxDateTimeInput", SqlFld="happen_time",ForQuery=false,  CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea = "divHead", CtrlType="jqxDateTimeInput", SqlFld="happen_time", ForQuery=false,   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }"
                }},
                {"queryTimeAccord",new DataItem(){FldArea="divHead",Title="时间类型",LabelFld = "time_status_name",ButtonUsage = "list",CompareOperator="=",Value="byApproveTime",Label="审核时间",ForQuery=false, AutoRemember=true,
                    Hidden = true,
                    Source = @"[{v:'byApproveTime',l:'审核时间'},
                                   {v:'byHappenTime',l:'交易时间'},  
                                     ]"
                }},
                {"sub_type",new DataItem(){FldArea="divHead",Title="往来类型",ButtonUsage="list",LabelFld="sub_type_name",LabelInDB=false,SqlFld="cah.sub_type",ForQuery=false,
                    Source = "[{v:'QK',l:'欠款'},{v:'YF',l:'预付款'},{v:'',l:'所有'}]",CompareOperator="="}},
                {"sheet_type",  new DataItem(){FldArea="divHead",Title="单据类型",ButtonUsage="list",LabelFld="sheet_type_name",Checkboxes = true,SqlFld="cah.sheet_type",
                    Source = "[{v:'CG',l:'采购单'},{v:'CT',l:'采购退货'},{v:'YF',l:'预付款单'},{v:'FK',l:'付款单'},{v:'ZC',l:'费用支出单'},{v:'SR',l:'其他收入单'},{v:'',l:'所有'}]",CompareOperator="="}},

                {"supcust_id",new DataItem(){FldArea="divHead",Title="供应商",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="supcust_id",ForQuery=false,
                SqlForOptions=CommonTool.selectSuppliers } },
                 
                  {"sub_id",new DataItem(){Title="科目",FldArea="divHead", LabelFld="sub_name",ButtonUsage="list",CompareOperator="=",SqlFld="payway1_id,payway2_id,payway3_id",ForQuery=false,
                SqlForOptions = "SELECT s.sub_id as v ,s.sub_name as l  FROM cw_subject s WHERE s.company_id =~COMPANY_ID AND sub_type IN ( 'YF' ) ORDER BY order_index"}},


             {"showRedSheet",new DataItem(){FldArea="divHead",Title="显示红冲单",CtrlType="jqxCheckBox",ForQuery=false,Value="true",AutoRemember=true}},
                {"showImmediatePaySheet",new DataItem(){FldArea="divHead",Title="包含现结单据",CtrlType="jqxCheckBox",ForQuery=false,Value="true",AutoRemember=true,
                    DealQueryItem=showImmediatePaySheet=>{
                        if(showImmediatePaySheet!="true")
                           Grids["gridItems"].QueryFromSQL2="";
                        return "";
                    }
                }},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     Sortable=true,
                     ShowAggregates=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",new DataItem(){Title = "",Hidden = true,SqlFld = "cah.sheet_id" ,HideOnLoad = true} },
                       {"flow_id",new DataItem(){Title = "",Hidden = true,SqlFld = "flow_id",HideOnLoad = true} },
                       {"sheet_no",   new DataItem(){Title="单据编号",    Width="200",Linkable = true}},
                        {"sub_name",   new DataItem(){Title="科目名称",    Width="200"}},
                        {"make_brief", new DataItem() { Title = "备注", Width = "100", Hidden = false, SqlFld = "make_brief" }},
                       {"sup_name",     new DataItem(){Title="供应商名称",  Width="150" }},
                       {"sheet_type",   new DataItem(){Title="单据类型",    Width="100",
                           SqlFld="(CASE cah.sheet_type when 'CG' then '采购单' when 'CT' then '采购退货单'  WHEN 'YF' THEN '预付款单' WHEN 'FK' THEN '付款单'WHEN 'ZC' THEN '费用支出单'WHEN 'SR' THEN '其他收入单' end) "}},
                       {"sheet_status", new DataItem(){Title="状态",  Width="55",SqlFld="(case when red_flag='2' then '红字单' when red_flag='1' then '已红冲'  END)"}},
                       {"happen_time", new DataItem(){Title="交易时间",    Width="200", Sortable = true}},
                       {"approve_time", new DataItem(){Title="审核时间",    Width="200"}},
                       {"total_amount",     new DataItem(){Title="单据金额",  Width="150"}},
                       {"now_disc_amount",     new DataItem(){Title="优惠",  Width="100",ShowSum=true }},
                       {"now_pay_amount",     new DataItem(){Title="收款",  Width="100",ShowSum=true }},
                       {"arrears_add",     new DataItem(){Title="应付款增",  Width="110",ShowSum=true,
                           SqlFld=@"(CASE when qk_amount*(-1)>0 THEN (qk_amount*(-1))::numeric
                                          when sk_amount*(-1)>0 THEN (sk_amount*(-1))::numeric END)"}},
                       {"arrears_reduce",     new DataItem(){Title="应付款减",  Width="110",ShowSum=true,
                           SqlFld=@"(CASE when qk_amount*(-1)<0 THEN qk_amount::numeric 
                                          when sk_amount*(-1)<0 THEN sk_amount::numeric END)"}},
                       {"arrears_balance",     new DataItem(){Title="应付款余额",  Width="110",
                           SqlFld="(CASE when qk_balance is not null then (qk_balance*(-1))::text when sk_balance is not null THEN (sk_balance*(-1))::text END)"}},
                       //{"net_receivable",     new DataItem(){Title="应付款余额",  Width="80",SqlFld="gad.left_amount"}},
                       {"prepay_add",     new DataItem(){Title="预付款增",  Width="110",
                           SqlFld="(CASE when ys_amount>0 THEN ys_amount::text END)"}},
                       {"prepay_reduce",     new DataItem(){Title="预付款减",  Width="110",
                           SqlFld="(CASE when ys_amount<0 THEN (-ys_amount)::text END)"}},
                       {"prepay_balance",     new DataItem(){Title="预付款余额",  Width="110",
                           SqlFld="ys_balance::text "}},
                       {"prepay_total_balance",     new DataItem(){Title="预付款总余额",  Width="110",
                           SqlFld="ys_total_balance::text "}},
                       
                     },


                     QueryFromSQL = @"
FROM
(
    SELECT 
        cah.*,
        (case when b.sheet_no is not null then b.sheet_no when pp.sheet_no is not null then pp.sheet_no when gam.sheet_no is not null then gam.sheet_no when fm.sheet_no is not null then fm.sheet_no else null end) sheet_no,
        (case when b.total_amount is not null then b.total_amount when pp.total_amount is not null then pp.total_amount when gam.sheet_amount is not null then gam.sheet_amount WHEN fm.total_amount IS NOT NULL THEN fm.total_amount else null end) total_amount,
        (case when b.now_disc_amount is not null then b.now_disc_amount when pp.now_disc_amount is not null then pp.now_disc_amount when gam.now_disc_amount is not null then gam.now_disc_amount else null end) now_disc_amount,
        (case when b.now_pay_amount is not null then b.now_pay_amount when pp.now_pay_amount is not null then pp.now_pay_amount when gam.now_pay_amount is not null then gam.now_pay_amount when fm.now_pay_amount is not null then fm.now_pay_amount else null end) now_pay_amount,
        (case when b.make_brief is not null then b.make_brief when pp.make_brief is not null then pp.make_brief when gam.make_brief is not null then gam.make_brief when fm.make_brief is not null then fm.make_brief else null end) make_brief
    FROM
    (
        SELECT  
            (sheet->>'f1')::int sheet_id,
            sheet->>'f2' sheet_type,
            (sheet->>'f3')::int company_id,
            (sheet->>'f4')::TIMESTAMP happen_time,
            (sheet->>'f5')::int supcust_id,
            (sheet->>'f6') sub_type,
            (sheet->>'f7') red_flag,
            (sheet->>'f8') sub_id,
            (sheet->>'f9')::TIMESTAMP approve_time,
            sheet->>'f10' flow_id,
            (qk->>'f1')::numeric qk_amount,
            (qk->>'f2')::numeric qk_balance,
            (qk->>'f4')::numeric qk_balance_happen_time,
            (ys->>'f1')::numeric ys_amount,
            (ys->>'f2')::numeric ys_balance,
            (ys->>'f3')::numeric ys_total_balance,
            (ys->>'f4')::numeric ys_balance_happen_time,
            (ys->>'f5')::numeric ys_total_balance_happen_time,
            (sk->>'f1')::numeric sk_amount,
            (sk->>'f2')::numeric sk_balance,
            (sk->>'f4')::numeric sk_balance_happen_time
        FROM crosstab
        (
            'SELECT row_to_json(row(sheet_id,sheet_type,company_id,happen_time,supcust_id,sub_type,red_flag,sub_id,approve_time,flow_id)) json1, sub_type,row_to_json(row(change_amount,now_balance,now_prepay_balance,now_balance_happen_time,now_prepay_balance_happen_time,sheet_type)) as json 
             FROM client_account_history 
             WHERE company_id =~COMPANY_ID  and sheet_id is not null 
                   and ~VAR_timeFld >= ''~VAR_startDay'' AND ~VAR_timeFld <= ''~VAR_endDay''   
                   and happen_time >= ''~VAR_ForHappenTime'' AND happen_time <= ''~VAR_endDay''
                   ~VAR_red_flag    ~VAR_sub_id ~VAR_supcust_id
             ORDER BY SHEET_ID desc',
             $$VALUES ('SK'::text),('YF'::text),('QK'::text)$$
        ) AS errr(sheet jsonb,SK jsonb,YS jsonb,QK jsonb)
    ) cah
    LEFT JOIN sheet_prepay pp ON pp.sheet_id = cah.sheet_id and 'YF' = cah.sheet_type and pp.company_id=~COMPANY_ID 
    LEFT JOIN sheet_get_arrears_main gam ON gam.sheet_id = cah.sheet_id and 'FK' = cah.sheet_type and gam.company_id=~COMPANY_ID 
    LEFT JOIN sheet_buy_main b on b.sheet_id = cah.sheet_id and b.sheet_type = cah.sheet_type and b.company_id=~COMPANY_ID 
    LEFT JOIN sheet_fee_out_main fm ON fm.sheet_id = cah.sheet_id AND fm.sheet_type = cah.sheet_type and fm.company_id=~COMPANY_ID 
) cah
LEFT JOIN info_supcust sc ON sc.supcust_id = cah.supcust_id and sc.company_id = ~COMPANY_ID
LEFT JOIN cw_subject cw on cah.sub_id=cw.sub_id::text and cw.company_id = ~COMPANY_ID
where cah.company_id=~COMPANY_ID and supcust_flag in ('S','CS') ~VAR_SUB_TYPE
",
                     //单据类型移除，有的供应商也是客户 ，所以销售单也要可以查的  AND SHEET_TYPE IN (''CG'',''CT'',''YF'',''FK'',''ZC'',''SR'') 
                     QueryFromSQL2 = @"
FROM
(
    SELECT  
        null flow_id,
        sheet_id,
        sheet_no,
        sheet_type,
        sm.company_id,
        happen_time,
        approve_time,
        sm.supcust_id,
        SC.sup_name,
        null sub_type,
        null sub_name,
        null sub_id,
        red_flag,
        null::numeric qk_amount,
        null::numeric qk_balance,
        null::numeric qk_balance_happen_time,
        null::numeric ys_amount,
        null::numeric ys_balance,
        null::numeric ys_total_balance,
        null::numeric ys_balance_happen_time,
        null::numeric ys_total_balance_happen_time,
        null::numeric sk_amount,
        null::numeric sk_balance,
        null::numeric sk_balance_happen_time,
        total_amount,
        now_disc_amount,
        now_pay_amount,
        sm.make_brief -- 新增 make_brief 字段
    FROM sheet_buy_main sm 
    LEFT JOIN info_supcust sc ON sc.supcust_id = sm.supcust_id AND sc.company_id = ~COMPANY_ID
    WHERE 
        sm.company_id = ~COMPANY_ID 
        AND coalesce(sm.prepay_amount, 0) = 0 
        AND ABS(total_amount - now_disc_amount - now_pay_amount) < 0.01  
        AND approve_time IS NOT NULL
        AND ~VAR_timeFld >= '~VAR_startDay' 
        AND ~VAR_timeFld <= '~VAR_endDay' 
        ~VAR_red_flag

    UNION

    SELECT  
        null flow_id,
        sheet_id,
        sheet_no,
        sheet_type,
        sm.company_id,
        happen_time,
        approve_time,
        sm.supcust_id,
        sc.sup_name,
        null sub_type,
        null sub_name,
        null sub_id,
        red_flag,
        null::float4 qk_amount,
        null::float4 qk_balance,
        null::float4 qk_balance_happen_time,
        null::float4 ys_amount,
        null::float4 ys_balance,
        null::numeric ys_total_balance,
        null::float4 ys_balance_happen_time,
        null::numeric ys_total_balance_happen_time,
        null::float4 sk_amount,
        null::float4 sk_balance,
        null::float4 sk_balance_happen_time,  
        money_inout_flag * total_amount total_amount,
        money_inout_flag * now_disc_amount now_disc_amount,
        money_inout_flag * now_pay_amount now_pay_amount,
        sm.make_brief -- 新增 make_brief 字段
    FROM sheet_fee_out_main sm 
    LEFT JOIN info_supcust sc ON sm.supcust_id = sc.supcust_id AND sc.company_id = ~COMPANY_ID
    LEFT JOIN (SELECT oper_id, oper_name AS seller_name FROM info_operator WHERE company_id = ~COMPANY_ID) seller ON sm.getter_id = seller.oper_id
    WHERE 
        sm.company_id = ~COMPANY_ID 
        AND ABS(total_amount - now_disc_amount - now_pay_amount) < 0.01 
        AND supcust_flag IN ('S', 'CS')
        AND approve_time IS NOT NULL 
        AND ~VAR_timeFld >= '~VAR_startDay' 
        AND ~VAR_timeFld <= '~VAR_endDay' 
        ~VAR_red_flag
) cah 
WHERE 
    cah.company_id = ~COMPANY_ID  
    ~VAR_condi2_supcust_id
",
                     QueryOrderSQL=" order by ~VAR_forOrdeTimeFld ,sup_name"
                  }
                }
            };
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            string now = CPubVars.GetDateText(DateTime.Now.Date) + " 00:00";
            var supcust_id = CPubVars.RequestV(Request, "supcust_id");
            //var sub_type = CPubVars.RequestV(Request, "sub_type");
            //if (supcust_id.IsValid() && sub_type.IsValid())
            //{
            //    string sql = $"select min(happen_time) v from client_account_history where company_id = {company_id} and supcust_id = {supcust_id} and sub_type = '{sub_type}'";
            //    dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            //    if (record != null) DataItems["startDay"].Value = record.v;
            //}
            //else DataItems["startDay"].Value = now;
        }


        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;
            var startDay = Convert.ToDateTime(DataItems["startDay"].Value);
            string queryTimeAccord = DataItems["queryTimeAccord"].Value;
            SQLVariables["timeFld"] = "approve_time";
            SQLVariables["forOrdeTimeFld"] = "approve_time desc,flow_id desc";
            if (queryTimeAccord == "byHappenTime")
            {
                SQLVariables["timeFld"] = "happen_time";
                SQLVariables["forOrdeTimeFld"] = "happen_time desc,approve_time desc,flow_id desc";
                Grids["gridItems"].Columns["arrears_balance"].SqlFld = "(CASE when qk_balance_happen_time is not null then (qk_balance_happen_time*(-1))::text when sk_balance_happen_time is not null THEN (sk_balance_happen_time*(-1))::text END)";
                Grids["gridItems"].Columns["prepay_balance"].SqlFld = "ys_balance_happen_time::text";
                Grids["gridItems"].Columns["prepay_total_balance"].SqlFld = "ys_total_balance_happen_time::text";
            }

            startDay = startDay.AddDays(-180);//如果审核时间和交易时间不一，最多往前查180天
            // fix: 如果是日期是2024-01-01 00:00或者 2024-01-01 00:00:00的时候就不要进行拼接
            if (startDay.TimeOfDay == TimeSpan.Zero)
            {
                SQLVariables["ForHappenTime"] = CPubVars.GetDateText(startDay) + " 00:00"; // 只有日期，拼接上 " 00:00"
            }
            else
            {
                SQLVariables["ForHappenTime"] = CPubVars.GetDateText(startDay); // 已包含时间，不需要拼接
            }
           
            if (DataItems["sub_id"].Value != "")
            {
                var sub_id = DataItems["sub_id"].Value;
                SQLVariables["sub_id"] = @$"and sub_id={sub_id}";
            }

            else
            {
                SQLVariables["sub_id"] = "";
            }
            if (DataItems["showRedSheet"].Value.ToLower() == "true")
            {

                SQLVariables["red_flag"] = "  ";
            }
            else
            {
                SQLVariables["red_flag"] = "and red_flag is  null";
            }

            if (DataItems["supcust_id"].Value != "")
            {
                var supcustID = DataItems["supcust_id"].Value;
                SQLVariables["supcust_id"] = $" and supcust_id={supcustID} ";
                SQLVariables["condi2_supcust_id"] = $" and cah.supcust_id={supcustID}";

            }
            else
            {
                SQLVariables["supcust_id"] = "";
                SQLVariables["condi2_supcust_id"] = $"";
            }


            SQLVariables["SUB_TYPE"] = DataItems["sub_type"].Value == "" ? "" : $"and cah.sub_type='{DataItems["sub_type"].Value}'";


        }


        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class SupplierAccountHistoryController : QueryController
    {
        public SupplierAccountHistoryController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SupplierAccountHistoryModel model = new SupplierAccountHistoryModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            SupplierAccountHistoryModel model = new SupplierAccountHistoryModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            SupplierAccountHistoryModel model = new SupplierAccountHistoryModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
