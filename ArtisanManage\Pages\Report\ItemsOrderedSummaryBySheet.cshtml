@page
@model ArtisanManage.Pages.BaseInfo.ItemsOrderedSummaryBySheetModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server"> 
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
           
    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                });
                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;



                    @*if (args.datafield == "sheet_no") {
                        var sheet_id = args.row.bounddata.sheet_id;
                        var sheet_type1 = args.row.bounddata.sheet_type1;
                        var sheet_type = args.row.bounddata.sheet_type;
                        if (sheet_type1 == 'X' || sheet_type1 == 'T') window.parent.newTabPage(sheet_type, `Sheets/SaleSheet?sheet_id=${sheet_id}`);
                        if (sheet_type1 == 'DH') window.parent.newTabPage(sheet_type, `Sheets/OrderItemSheet?sheet_id=${sheet_id}`);
                        if (sheet_type1 == 'DHTZ') window.parent.newTabPage(sheet_type, `Sheets/OrderItemAdjustSheet?sheet_id=${sheet_id}`);
                    }*@
                    if (args.datafield == "item_name") {
                        var item_id = args.row.bounddata.item_id;
                        var item_name = args.row.bounddata.item_name;

                        var order_sub_id = args.row.bounddata.order_sub_id;
                        var order_sub_name = args.row.bounddata.order_sub_name;
                        var supcust_id = args.row.bounddata.supcust_id;
                        var sup_name = args.row.bounddata.sup_name;
                        var sup_name = args.row.bounddata.sup_name;

                        window.parent.newTabPage('定货明细变化', `Report/ItemsOrderedSummaryByItem?item_id=${item_id}&item_name=${encodeURIComponent(item_name)}&order_sub_id=${order_sub_id}&sub_name=${encodeURIComponent(order_sub_name)}&supcust_id=${supcust_id}&sup_name=${encodeURIComponent(sup_name)}`);
                    }
                    if (args.datafield == "sheet_no") {
                        var sheet_no = args.row.bounddata.sheet_no;
                        var sheet_id = args.row.bounddata.sheet_id;
                        var searchResult = sheet_no.includes('DHTZ', 0)
                        if (searchResult) {
                            window.parent.newTabPage('定货会调整单', `Sheets/OrderItemAdjustSheet?sheet_id=${sheet_id}`);
                        }
                        else {
                            window.parent.newTabPage('定货单', `Sheets/OrderItemSheet?sheet_id=${sheet_id}`);
                        }
                    }
                    if (args.datafield == "item_order_sheet_no") {
                        var sheet_no = args.row.bounddata.item_order_sheet_no;
                        var item_order_sheet_id = args.row.bounddata.item_order_sheet_id;
                        var searchResult = sheet_no.includes('DHTZ', 0)
                        if (searchResult){
                            window.parent.newTabPage('定货会调整单', `Sheets/OrderItemAdjustSheet?sheet_id=${item_order_sheet_id}`);
                        }else{
                            window.parent.newTabPage('定货单', `Sheets/OrderItemSheet?sheet_id=${item_order_sheet_id}`);
                        }
                    }
                });
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
                $('#item_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popItem').jqxWindow('open');
                        $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                $('#supcust_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popClient').jqxWindow('open');
                        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
         
                QueryData();
            });

        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "selectMulti") {
                    if (rs.data.checkedRows.length == 1) {
                        var item_id = rs.data.checkedRows[0].item_id;
                        var item_name = rs.data.checkedRows[0].item_name;
                    }

                    var rows = rs.data.checkedRows
                    var items_id = ''
                    rows.forEach(function (row) {
                        if (items_id != '') items_id += ','
                        items_id += row.item_id
                    })
                    $('#item_id').jqxInput('val', { value: item_id, label: item_name });

                    $.ajax({
                        url: '/api/SaleSheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: item_id },
                        success: function (data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });
                    $('#popItem').jqxWindow('close');

                }
                

            } else if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });
                }
                $('#popClient').jqxWindow('close');
            }

        });
    </script>
</head>

<body style="overflow:hidden">
    <style>
    </style>
    <div style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>

        <button onclick="QueryData()" style="margin-right:20px;margin-top:30px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;margin-top:30px">导出</button>

    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>

    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>