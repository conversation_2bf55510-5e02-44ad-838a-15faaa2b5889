### 常用Sql语句：

1. 根据商品单位表与商品信息表，通过行转列获取商品信息 (crosstab)

   ```sql
   select item_id,s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,
   			   m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,
                  b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor 
    from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor)) as unit from info_item_multi_unit where company_id = {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
    as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)
   ```

   具体可见：[行转列用法](https://blog.csdn.net/weixin_42748292/article/details/*********?spm=1001.2014.3001.5501) 
   
2. split_apart 

```sql
split_part(items_id, ',',1)::int item_id
```
3. 当数据库出现too many clients 导致连接失败时
```sql
– 查询当前连接
select * from pg_stat_activity;
–查询最大连接数
show max_connections;
– 释放空闲连接
SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE state='idle';
```


# 数据库表格

符号说明：

:star::star::star: ：常用

:star::star:：一般

:star:偶尔

无：暂未用到

## 一、 基础档案相关表格

### 一、公司相关 (公司、员工、设置、权限)

#### 1. 相关业务场景

1. 开户 初始化参数
2. 员工权限设置
3. 获取 operKey 及 登录账户及密码

#### 2. 相关表格

##### a 公司档案 g_company

| 列名         | 类型  | 不允许空值         | 主键               | 说明     | 重要性             |
| ------------ | ----- | ------------------ | ------------------ | -------- | ------------------ |
| company_id   | int   | :heavy_check_mark: | :heavy_check_mark: | 公司号   | :star::star::star: |
| company_name | text  |                    |                    | 公司名称 | :star::star::star: |
| boss_mobile  | text  |                    |                    | 老板账户 | :star::star::star: |
| master_id    | int   |                    |                    |          |                    |
| server_id    | int   |                    |                    | 服务器   |                    |
| app_var      | float |                    |                    |          |                    |
| pc_var       | float |                    |                    |          |                    |
| agent_name   | text  |                    |                    | 代理名称 | :star:             |
| user_count   | int2  |                    |                    | 端口数   | :star:             |
| pc_mb_ver    |       |                    |                    |          |                    |
| province     | text  |                    |                    | 省       |                    |
| city         | text  |                    |                    | 市       |                    |
| county       | text  |                    |                    | 区       |                    |
| create_time  | date  |                    |                    | 开户日期 | :star:             |
| expire_time  | date  |                    |                    |          |                    |
| pay_time     | date  |                    |                    | 付款时间 |                    |
| boss_name    | text  |                    |                    | 老板名称 | :star:             |
| business_id  | int   |                    |                    |          |                    |

##### b 可使用账户 g_operator

| 列名       | 类型 | 不允许空值         | 主键               | 说明         | 重要性             |
| ---------- | ---- | ------------------ | ------------------ | ------------ | ------------------ |
| company_id | int  | :heavy_check_mark: |                    | 公司号       | :star::star::star: |
| oper_id    | int  | :heavy_check_mark: | :heavy_check_mark: | 可登录账户ID |                    |
| oper_name  | text |                    |                    | 名称         |                    |
| mobile     | text |                    |                    | 登录账户     | :star::star::star: |
| oper_pw    | text |                    |                    | 登录密码     | :star::star::star: |
| is_admin   | bool |                    |                    | 是否管理员   | :star::star:       |

##### c 公司设置 company_setting 

| 列名       | 类型  | 不允许空值         | 主键               | 说明                         | 重要性             |
| ---------- | ----- | ------------------ | ------------------ | ---------------------------- | ------------------ |
| company_id | int   | :heavy_check_mark: | :heavy_check_mark: | 公司号                       | :star::star::star: |
| setting    | jsonb |                    |                    | 参考项目中的 (role.json)文件 | :star::star::star: |

##### d 白名单 white_list 

| 列名       | 类型  | 不允许空值         | 主键               | 说明                         | 重要性             |
| ---------- | ----- | ------------------ | ------------------ | ---------------------------- | ------------------ |
| white_type | text  | :heavy_check_mark: | :heavy_check_mark: | 白名单类型                   | :star::star::star: |
| company_id | int   | :heavy_check_mark: | :heavy_check_mark: | 公司号                       | :star::star::star: |

### 二、 商品相关(商品、单位、类别、品牌)

#### 1. 相关业务场景

1. 查询商品基础信息
2. 开单时选择商品
3. 商品相关库存、利润报表

#### 2. 商品相关表格

##### a 商品档案 info_supcust

| 列名                    | 类型        | 不允许空值         | 主键               | 说明                         | 重要性             |
| ----------------------- | ----------- | ------------------ | ------------------ | ---------------------------- | ------------------ |
| company_id              | int         | :heavy_check_mark: | :heavy_check_mark: | 公司号                       | :star::star::star: |
| item_id                 | int(serial) | :heavy_check_mark: | :heavy_check_mark: | 商品号                       | :star::star::star: |
| item_name               | text        |                    |                    | 商品名称                     | :star::star::star: |
| item_no                 | text        |                    |                    | 商品自定义编号               | :star:             |
| barcode                 | text        |                    |                    | 商品条码                     | :star::star::star: |
| py_str                  | text        |                    |                    | 拼音助记码                   | :star::star::star: |
| item_spec               | text        |                    |                    | 商品规格，例如(4*50)         | :star:             |
| shelf_life              | int         |                    |                    | 生产日期                     | :star:             |
| valid_days              | int         |                    |                    | 有效期                       | :star:             |
| origin_city             | text        |                    |                    | 产地                         | :star:             |
| simple_name             | text        |                    |                    | 简称                         |                    |
| service_flag            | boolean     |                    |                    |                              |                    |
| item_class              | int         | :heavy_check_mark: |                    | 类别ID                       | :star::star::star: |
| other_class             | text        |                    |                    | 类别路径                     | :star:             |
| item_brand              | int         | :heavy_check_mark: |                    | 品牌ID                       | :star:             |
| unit_no                 | text        |                    |                    | 商品单位                     |                    |
| buy_price               | float       |                    |                    | 小单位进价                   |                    |
| wholesale_price         | float       |                    |                    | 小单位批发价                 |                    |
| retail_price            | float       |                    |                    | 小单位零售价                 |                    |
| min_sale_price          | float       |                    |                    |                              |                    |
| cost_change_qty         | float       |                    |                    |                              |                    |
| cost_price_spec         | float       |                    |                    | 指定价格                     | :star:             |
| cost_price_recent       | float       |                    |                    | 上次售价                     | :star:             |
| cost_price_avg          | float       |                    |                    | 小单位加权平均价             | :star:             |
| cost_amt                | float       |                    |                    | 货值(加权价*商品小单位库存)  | :star:             |
| item_cost_price_suspect | boolean     |                    |                    | 商品质疑状态(加权价是否为负) | :star:             |
| cost_type               | float       |                    |                    |                              |                    |
| combine_sta             | text        |                    |                    |                              |                    |
| status                  | int         |                    |                    | 0、1标记商品是否停用         | :star::star:       |
| display_flag            | int         |                    |                    | 0、1标记                     |                    |
| vip_price               | float       |                    |                    | 会员价                       |                    |
| color_group             | int         |                    |                    |                              |                    |
| use_part_colors         | boolean     |                    |                    |                              |                    |
| available_color         | boolean     |                    |                    |                              |                    |
| size_group              | int         |                    |                    |                              |                    |
| barcode_style           | int2        |                    |                    |                              |                    |
| py_str1                 | text        |                    |                    | 拼音助记码                   |                    |
| item_season             | int4        |                    |                    |                              |                    |
| other_barcode           | text        |                    |                    |                              |                    |
| barcode_other           | text        |                    |                    |                              |                    |
| photo1                  | text        |                    |                    |                              |                    |
| photo2                  | text        |                    |                    |                              |                    |
| allow_disc              | boolean     |                    |                    |                              |                    |
| vip_price1              | float       |                    |                    |                              |                    |
| vip_price2              | float       |                    |                    |                              |                    |
| ignore_stock            | boolean     |                    |                    |                              |                    |
| item_order_index        | int         |                    |                    |                              |                    |
| rpt_class               | text        |                    |                    |                              |                    |
| create_time             | timestamp   |                    |                    | 商品创建时间                 |                    |
| update_time             | timestamp   |                    |                    | 商品更新时间                 |                    |
| update_photo_time       | timestamp   |                    |                    |                              |                    |

##### b 商品单位 info_item_multi_unit

| 列名            | 类型    | 不为空             | 键   | 说明              | 重要性             |
| --------------- | ------- | ------------------ | ---- | ----------------- | ------------------ |
| company_id      | int     | :heavy_check_mark: |      | 公司号            | :star::star::star: |
| item_id         | int     | :heavy_check_mark: |      | 商品号            | :star::star::star: |
| unit_no         | text    |                    |      | 单位              | :star::star::star: |
| unit_factor     | float   |                    |      | 包装率            | :star::star::star: |
| wholesale_price | numeric |                    |      | 批发价            | :star::star::star: |
| retail_price    | numeric |                    |      | 零售价            | :star:             |
| buy_price       | numeric |                    |      | 进价              | :star::star:       |
| vip_price       | numeric |                    |      | 会员价            |                    |
| vip_price1      | numeric |                    |      | 会员价1           |                    |
| vip_price2      | numeric |                    |      | 会员价2           |                    |
| vip_price3      | numeric |                    |      | 会员价3           |                    |
| barcode         | text    |                    |      | 条码              | :star::star::star: |
| unit_type       | text    |                    |      | 单位类型(b、m、s) | :star::star::star: |
| min_sale_price  | float   |                    |      |                   |                    |
| cost_price_spec | float   |                    |      | 指定售价          | :star:             |
| lowest_price    | numeric |                    |      | 最低售价          | :star:             |

##### c 商品类别 info_item_class

| 列名               | 类型    | 不为空             | 键                 | 说明           | 重要性             |
| ------------------ | ------- | ------------------ | ------------------ | -------------- | ------------------ |
| company_id         | int     | :heavy_check_mark: | :heavy_check_mark: | 公司号         | :star::star::star: |
| class_id           | int     | :heavy_check_mark: | :heavy_check_mark: | 类别号         | :star::star::star: |
| class_name         | text    |                    |                    | 类别名称       | :star::star::star: |
| service_flag       | int     |                    |                    |                |                    |
| cls_type           | int     |                    |                    |                |                    |
| brand_id           | int     |                    |                    | 品牌号         |                    |
| mother_id          | int     |                    |                    | 上级类别号     | :star:             |
| order_index        | int     |                    |                    | 顺序号(自定义) | :star:             |
| cls_status         | text    |                    |                    |                |                    |
| py_str             | text    |                    |                    | 拼音助记码     | :star::star::star: |
| ignore_stock_class | boolean |                    |                    |                |                    |
| general_class      | text    |                    |                    |                |                    |

##### d 商品品牌 info_item_brand

| 列名       | 类型        | 不为空             | 键                 | 说明     | 重要性             |
| ---------- | ----------- | ------------------ | ------------------ | -------- | ------------------ |
| company_id | int         | :heavy_check_mark: | :heavy_check_mark: | 公司号   | :star::star::star: |
| brand_id   | int(serial) | :heavy_check_mark: | :heavy_check_mark: | 品牌号   | :star::star::star: |
| brand_name | text        |                    |                    | 品牌名称 | :star::star::star: |
| remark     | text        |                    |                    | 备注     | :star:             |

### 三、 客户相关(客户、片区、部门、渠道、等级)

#### 1. 相关业务场景

1. 查询客户相关信息
2. 开单选择客户
3. 客户相关销售利润报表

#### 2. 客户相关表格

##### a 客户、供应商档案 info_supcust

| 列名                    | 类型      | 不为空             | 键   | 说明                   | 重要性             |
|-----------------------| --------- | ------------------ | ---- | ---------------------- | ------------------ |
| company_id            | int       | :heavy_check_mark: |      | 公司号                 | :star::star::star: |
| supcust_id            | int       | :heavy_check_mark: |      | 客户号                 | :star::star::star: |
| supcust_no            | text      |                    |      | 客户编号               |                    |
| boss_name             | text      |                    |      | 客户老板名称           | :star:             |
| sup_rank              | int       |                    |      | 客户等级               | :star:             |
| retail_wholesale_flag | text      |                    |      | 批发、零售标记         |                    |
| supcust_flag          | text      |                    |      | 客户(C)供应商(S)标记   | :star:             |
| sup_name              | text      |                    |      | 客户名称               | :star::star::star: |
| py_str                | text      |                    |      | 客户名助记码           | :star::star::star: |
| mobile                | text      |                    |      | 手机号码               | :star:             |
| sup_groups            | int       |                    |      | 客户渠道               | :star:             |
| status                | int2      |                    |      | 使用状态(0 停用 1正常) | :star::star:       |
| region_id             | int       |                    |      | 片区号                 | :star::star::star: |
| other_region          | text      |                    |      | 片区路径               | :star::star::star: |
| sup_addr              | text      |                    |      | 客户地址               |                    |
| sup_addr2             | text      |                    |      | 客户地址2              |                    |
| addr_lat              | numeric   |                    |      | 客户纬度位置           |                    |
| addr_lng              | numeric   |                    |      | 客户经度位置           |                    |
| addr2_lat             | numeric   |                    |      | 客户纬度位置2          |                    |
| addr_lng              | numeric   |                    |      | 客户经度位置2          |                    |
| zip                   | text      |                    |      |                        |                    |
| sup_mail              | text      |                    |      | 客户邮箱               |                    |
| sup_tel               | text      |                    |      | 客户电话               |                    |
| sup_tax_no            | text      |                    |      | 客户传真号码           |                    |
| birthday              | text      |                    |      | 生日                   |                    |
| cn_birthday           | text      |                    |      | 生日                   |                    |
| create_time           | timestamp |                    |      | 创建时间               | :star:             |
| addr_lnglat           | text      |                    |      | 位置经纬度             |                    |
| acct_cust_id          | int       |                    |      | 结算单位               | :star:             |
| creator_id            | int       |                    |      | 创建人                 | :star:             |
| oper_id               | int       |                    |      | 操作人                 | :star:             |
| acct_type             | text      |                    |      | 结算方式               | :star::star:       |
| license_no            | text      |                    |      | 营业执照号码           | :star:             |

##### b 客户渠道 info_supcust_group

| 列名         | 类型 | 不为空             | 键   | 说明             | 重要性             |
| ------------ | ---- | ------------------ | ---- | ---------------- | ------------------ |
| company_id   | int  | :heavy_check_mark: |      | 公司号           | :star::star::star: |
| group_id     | int  | :heavy_check_mark: |      | 渠道号           | :star::star::star: |
| group_name   | text |                    |      | 渠道名称         | :star::star:       |
| supcust_flag | text |                    |      | 客户、供应商标志 |                    |
| remark       | text |                    |      | 备注             | :star:             |

##### c 客户等级 info_supcust_rank

| 列名       | 类型 | 不为空             | 键                 | 说明     | 重要性             |
| ---------- | ---- | ------------------ | ------------------ | -------- | ------------------ |
| company_id | int  | :heavy_check_mark: | :heavy_check_mark: | 公司号   | :star::star::star: |
| rank_id    | int  |                    |                    | 等级号   | :star::star::star: |
| rank_name  | text |                    |                    | 等级名称 | :star::star::star: |
| rank_note  | text |                    |                    |          |                    |

##### d 客户片区 info_region

| 列名        | 类型 | 不为空             | 键                 | 说明       | 重要性             |
| ----------- | ---- | ------------------ | ------------------ | ---------- | ------------------ |
| company_id  | int  | :heavy_check_mark: | :heavy_check_mark: | 公司号     | :star::star::star: |
| region_id   | int  | :heavy_check_mark: | :heavy_check_mark: | 片区号     | :star::star::star: |
| region_name | text |                    |                    | 片区名称   | :star::star::star: |
| mother_id   | int  |                    |                    | 上级片区号 | :star::star:       |
| order_index | int  |                    |                    | 顺序号     | :star::star:       |

##### 

### 四、 员工相关 

#### 1. 相关业务场景

1. 查看员工基本信息
2. 根据员工权限开单查看
3. 查询员工相关报表

#### 2. 员工相关表格

##### a 员工档案info_operator

| 列名                 | 类型      | 不为空             | 键                 | 说明       | 重要性             |
| -------------------- | --------- | ------------------ | ------------------ | ---------- | ------------------ |
| company_id           | int       | :heavy_check_mark: |                    | 公司号     | :star::star::star: |
| oper_id              | int       | :heavy_check_mark: | :heavy_check_mark: | 员工号     | :star::star::star: |
| mobile               | text      |                    |                    | 员工手机号 | :star::star::star: |
| oper_name            | text      |                    |                    | 员工名称   |                    |
| oper_group           | text      |                    |                    |            |                    |
| oper_pw              | text      |                    |                    |            |                    |
| can_login            | int       |                    |                    |            |                    |
| is_seller            | boolean   |                    |                    | 是否业务员 | :star::star:       |
| is_sender            | boolean   |                    |                    | 是否送货员 | :star:             |
| salary_style         | int       |                    |                    |            |                    |
| base_salary          | numeric   |                    |                    |            |                    |
| base_salary_change   | int       |                    |                    |            |                    |
| last_salary_sum_time | timestamp |                    |                    |            |                    |
| ticheng_set_name     | text      |                    |                    |            |                    |
| py_str               | text      |                    |                    | 拼音助记码 | :star::star::star: |
| depart_id            | int       |                    |                    | 部门号     | :star::star:       |
| depart_path          | text      |                    |                    | 部门路径   | :star:             |
| oper_customdesk      | jsonb     |                    |                    |            |                    |
| role_id              | int       |                    |                    | 角色号     |                    |
| oper_regions         | json      |                    |                    |            |                    |
| status               | int2      |                    |                    | 状态       |                    |
| log_report           | text      |                    |                    |            |                    |
| manual_goback        | bool      |                    |                    |            |                    |
| restrict_branches    | bool      |                    |                    |            |                    |
| app_desktop          | jsonb     |                    |                    |            |                    |
| pc_desktop           | jsonb     |                    |                    |            |                    |

##### b 员工部门 info_department

| 列名        | 类型 | 不为空             | 键   | 说明       | 重要性             |
| ----------- | ---- | ------------------ | ---- | ---------- | ------------------ |
| company_id  | int  | :heavy_check_mark: |      | 公司号     | :star::star::star: |
| depart_id   | int  | :heavy_check_mark: |      | 部门号     | :star::star::star: |
| depart_name | text |                    |      | 部门名称   | :star::star::star: |
| mother_id   | int  |                    |      | 上级部门号 | :star:             |

c 角色信息

| 列名       | 类型 | 不为空             | 键   | 说明     | 重要性             |
| ---------- | ---- | ------------------ | ---- | -------- | ------------------ |
| company_id | int  | :heavy_check_mark: |      | 公司号   | :star::star::star: |
| role_id    | int  | :heavy_check_mark: |      | 角色号   | :star::star::star: |
| role_name  | text |                    |      | 角色名称 | :star::star:       |
| rights     | json |                    |      | 角色权限 | :star::star::star: |
| remark     | text |                    |      | 备注     | :star:             |

### 五、其他

#### 1. 业务相关

1. 选择财务类别
2. 单位选择
3. 仓库选择
4. 单据备注选择

#### 2.相关表格

##### a.1 财务表格(总) cw_subject

| 列名             | 类型 | 不为空             | 键                 | 说明           | 重要性             |
| ---------------- | ---- | ------------------ | ------------------ | -------------- | ------------------ |
| company_id       | int  | :heavy_check_mark: |                    | 公司号         | :star::star::star: |
| sub_id           | int  | :heavy_check_mark: | :heavy_check_mark: | 账户号         | :star::star::star: |
| sub_code         | text |                    |                    | 账户编码       | :star::star::star: |
| sub_name         | text |                    |                    | 账户名称       | :star::star::star: |
| sub_type         | text |                    |                    | 账户类型       | :star::star::star: |
| mother_id        | int  |                    |                    | 上级账户号     | :star::star::star: |
| level            | int2 |                    |                    | 科目层级       | :star::star::star: |
| py_str           | text |                    |                    | 拼音助记码     | :star:             |
| direction        | int2 |                    |                    | 借贷方向       | :star::star::star: |
| status           | int2 |                    |                    | 状态           | :star::star::star: |
| order_index      | int  |                    |                    | 顺序号         | :star:             |
| other_sub        | text |                    |                    | 树形层次结构   | :star::star::star: |
| is_order         | bool |                    |                    | 是否定货会     | :star::star::star: |
| for_pay          | bool |                    |                    | 是否业务支付方式| :star::star::star: |

##### a.2 银行账户(用于支付) info_pay_way

| 列名         | 类型    | 不为空             | 键                 | 说明             | 重要性             |
| ------------ | ------- | ------------------ | ------------------ | ---------------- | ------------------ |
| company_id   | int     | :heavy_check_mark: |                    | 公司号           | :star::star::star: |
| sub_id       | int     | :heavy_check_mark: | :heavy_check_mark: | 账户号           | :star::star::star: |
| sub_name     | text    |                    |                    | 账户名称         | :star::star::star: |
| payway_index | int     |                    |                    | 支付顺序         | :star::star:       |
| payway_key   | text    |                    |                    |                  |                    |
| payway_type  | text    |                    |                    | 支付类型         | :star::star::star: |
| py_str       | text    |                    |                    | 拼音助记码       | :star:             |
| sale_avail   | boolean |                    |                    |                  |                    |
| buy_avail    | boolean |                    |                    |                  |                    |
| order_index  | int     |                    |                    | 账户顺序(没用？) |                    |
| mother_id    | int     |                    |                    | 上级账户         |                    |
| other_sub    | text    |                    |                    | 账户路径         |                    |
| is_order     | bool    |                    |                    | 是否定货会       | :star:             |
| bank_no      | int     |                    |                    | 银行账户         | :star:             |
| bank_name    | text    |                    |                    | 银行名称         | :star:             |
| remark       | text    |                    |                    | 备注             | :star:             |

##### b 单位信息表 info_avail_unit

| 列名        | 类型    | 不为空             | 键   | 说明       | 重要性             |
| ----------- | ------- | ------------------ | ---- | ---------- | ------------------ |
| company_id  | int     | :heavy_check_mark: |      | 公司号     | :star::star::star: |
| unit_id     | int     |                    |      | 单位号     | :star::star::star: |
| unit_no     | text    |                    |      | 单位名称   | :star::star::star: |
| is_big_unit | boolean |                    |      | 是否大单位 | :star:             |

##### c 仓库信息 info_branch

| 列名                 | 类型    | 不为空             | 键                 | 说明       | 重要性             |
| -------------------- | ------- | ------------------ | ------------------ | ---------- | ------------------ |
| company_id           | int     | :heavy_check_mark: | :heavy_check_mark: | 公司号     | :star::star::star: |
| branch_id            | int     | :heavy_check_mark: | :heavy_check_mark: | 仓库号     | :star::star::star: |
| branch_name          | text    |                    |                    | 仓库名称   | :star::star::star: |
| ignore_batch         | boolean |                    |                    |            |                    |
| branch_addr          | text    |                    |                    | 仓库地址   |                    |
| branch_tel           | text    |                    |                    | 仓库电话   |                    |
| py_str               | text    |                    |                    | 助记码     | :star:             |
| keepers              | text    |                    |                    | 仓库负责人 |                    |
| need_keeper_confirm  | bool    |                    |                    |            |                    |
| distinct_pos         | bool    |                    |                    |            |                    |
| branch_type          | text    |                    |                    | 仓库类型   | :star:             |
| allow_negative_stock | bool    |                    |                    | 允许负库存 | :star::star::star: |
| status               | int     |                    |                    | 状态       | :star::star:       |

##### d.1 主表备注信息

| 列名       | 类型 | 不为空             | 键                 | 说明     | 重要性 |
| ---------- | ---- | ------------------ | ------------------ | -------- | ------ |
| company_id | int  | :heavy_check_mark: |                    | 公司号   |        |
| brief_id   | int  | :heavy_check_mark: | :heavy_check_mark: | 备注号   |        |
| brief_text | text |                    |                    | 备注名称 |        |
| sheet_type | text |                    |                    | 单据类型 |        |

##### d.1 详表备注信息

| 列名              | 类型 | 不为空             | 键                 | 说明             | 重要性             |
| ----------------- | ---- | ------------------ | ------------------ | ---------------- | ------------------ |
| company_id        | int  | :heavy_check_mark: |                    | 公司号           | :star::star::star: |
| brief_id          | int  | :heavy_check_mark: | :heavy_check_mark: | 备注号           | :star::star:       |
| brief_text        | text |                    |                    | 备注名称         | :star::star:       |
| sheet_type        | text |                    |                    | 单据类型         | :star:             |
| is_price_remember | bool |                    |                    | 是否影响记忆价格 | :star:             |

## 二、 单据相关

### 一、主要业务单据

#### 1. 相关业务场景

1. 开采购单，增加商品库存
2. 开销售订单
3. 开销售单，影响库存、影响账户余额
4. 开调拨单，影响不同仓库库存
5. 开盘点单，影响库存
6. 开盘点盈亏单，影响库存
7. 收款单，收取采购、销售过程中的欠款
8. 预收款单，影响预收账款
9. 定货会，增加定货会商品
10. 定货会调整单
11. 费用支出单
12. 陈列协议，增加陈列商品与陈列费
13. 交账单，结算交账

#### 2.业务单据表格

##### a.1  采购单 (主) sheet_buy_main

| 列名             | 类型      | 不为空             | 键   | 说明           | 重要性             |
| ---------------- | --------- | ------------------ | ---- | -------------- | ------------------ |
| company_id       | int       | :heavy_check_mark: |      | 公司号         | :star::star::star: |
| sheet_id         | int       | :heavy_check_mark: |      | 单号           | :star::star::star: |
| sheet_no         | text      | :heavy_check_mark: |      | 单据编号       | :star::star::star: |
| order_sheet_id   | int       |                    |      | 采购订单       |                    |
| order_sheet_date | date      |                    |      | 订单日期       |                    |
| sheet_type       | text      |                    |      | 单据类型       | :star::star::star: |
| money_inout_flag | int2      |                    |      | 金钱正负标记   | :star::star::star: |
| branch_id        | int       |                    |      | 仓库           | :star::star::star: |
| red_flag         | int2      |                    |      | 红冲标记       | :star::star::star: |
| red_sheet_id     | int       |                    |      | 红冲单号       | :star::star::star: |
| red_sheet_date   | date      |                    |      | 红冲单日期     |                    |
| supcust_id       | int       |                    |      | 客户号         | :star::star::star: |
| total_amount     | float     | :heavy_check_mark: |      | 单据金额       | :star::star::star: |
| is_retail        | boolean   |                    |      | 是否零售       |                    |
| tax_amount       | float     |                    |      | 税额           |                    |
| discount_info    | text      |                    |      | 折扣信息       |                    |
| payway1_id       | int       |                    |      | 支付方式1      | :star::star::star: |
| payway1_amount   | float     |                    |      | 支付金额1      | :star::star::star: |
| payway2_id       | int       |                    |      | 支付方式2      | :star::star::star: |
| payway2_amount   | float     |                    |      | 支付金额2      | :star::star::star: |
| now_pay_amount   | float     |                    |      | 当前支付金额   | :star::star::star: |
| now_disc_amount  | float     |                    |      | 当前折扣金额   | :star::star::star: |
| paid_amount      | float     |                    |      | 已支付总额     | :star::star::star: |
| disc_amount      | float     |                    |      | 折扣总额       | :star::star::star: |
| maker_id         | int       |                    |      | 制单人         | :star::star::star: |
| make_time        | timestamp |                    |      | 制单时间       | :star::star::star: |
| happen_time      | timestamp |                    |      | 交易时间       | :star::star:       |
| approver_id      | int       |                    |      | 审核人         | :star::star:       |
| approve_time     | timestamp |                    |      | 审核时间       | :star::star:       |
| seller_id        | int       |                    |      | 业务员         | :star::star:       |
| make_brief       | text      |                    |      | 整单备注       | :star:             |
| approve_brief    | text      |                    |      | 审核备注       | :star:             |
| submit_time      | timestamp |                    |      | 提交时间       |                    |
| prepay_amount    | float     |                    |      | 使用预收金额   | :star:             |
| sheet_attribute  | jsonb     |                    |      | 单据信息       | :star:             |
| total_quantity   | text      |                    |      | 总数量(大中小) | :star:             |

##### a.2 采购单 (详) sheet_buy_detail

| 列名            | 类型      | 不为空             | 键   | 说明         | 重要性             |
| --------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id      | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| flow_id         | int       | :heavy_check_mark: |      | 流水号       | :star:             |
| inout_flag      | int2      |                    |      | 商品进出标志 | :star::star::star: |
| sheet_id        | int       | :heavy_check_mark: |      | 单号         | :star::star::star: |
| row_index       | int       |                    |      | 商品行号     | :star:             |
| item_id         | int       | :heavy_check_mark: |      | 商品号       | :star::star::star: |
| sheet_item_name | text      |                    |      | 商品名       |                    |
| branch_id       | int       |                    |      | 仓库         |                    |
| batch_id        | int       |                    |      |              |                    |
| unit_no         | text      |                    |      | 单位         | :star::star::star: |
| unit_factor     | float     |                    |      | 包装率       | :star::star::star: |
| quantity        | float     |                    |      | 数量         | :star:             |
| orig_price      | float     |                    |      | 原价         |                    |
| real_price      | float     |                    |      | 实际售价     | :star::star::star: |
| cost_price_prop | float     |                    |      | 成本价       |                    |
| cost_price      | float     |                    |      | 成本价       |                    |
| sub_amount      | float     |                    |      | 小计         | :star::star::star: |
| happen_time     | timestamp |                    |      | 交易时间     | :star::star::star: |
| remark          | text      |                    |      | 备注自定义   | :star::star::star: |
| remark_id       | int       |                    |      | 选择备注号   |                    |
| sys_price       | float     |                    |      | 系统推荐价格 | :star::star:       |

##### b.1  销售单 (主) sheet_sale_main

| 列名             | 类型      | 不为空             | 键   | 说明           | 重要性             |
| ---------------- | --------- | ------------------ | ---- | -------------- | ------------------ |
| company_id       | int       | :heavy_check_mark: |      | 公司号         | :star::star::star: |
| sheet_id         | int       | :heavy_check_mark: |      | 单号           | :star::star::star: |
| sheet_no         | text      | :heavy_check_mark: |      | 单据编号       | :star::star::star: |
| order_sheet_id   | int       |                    |      | 销售订单       | :star::star:       |
| order_sheet_date | date      |                    |      | 订单日期       |                    |
| sheet_type       | text      |                    |      | 单据类型       | :star::star::star: |
| money_inout_flag | int2      |                    |      | 金钱正负标记   | :star::star::star: |
| branch_id        | int       |                    |      | 仓库           | :star::star::star: |
| red_flag         | int2      |                    |      | 红冲标记       | :star::star::star: |
| red_sheet_id     | int       |                    |      | 红冲单号       | :star::star::star: |
| red_sheet_date   | date      |                    |      | 红冲单日期     |                    |
| supcust_id       | int       |                    |      | 客户号         | :star::star::star: |
| shop_id          | int       |                    |      | 门店号         |                    |
| total_amount     | float     | :heavy_check_mark: |      | 单据金额       | :star::star::star: |
| is_retail        | boolean   |                    |      | 是否零售       |                    |
| tax_amount       | float     |                    |      | 税额           |                    |
| discount_info    | text      |                    |      | 折扣信息       |                    |
| payway1_id       | int       |                    |      | 支付方式1      | :star::star::star: |
| payway1_amount   | float     |                    |      | 支付金额1      | :star::star::star: |
| payway2_id       | int       |                    |      | 支付方式2      | :star::star::star: |
| payway2_amount   | float     |                    |      | 支付金额2      | :star::star::star: |
| now_pay_amount   | float     |                    |      | 当前支付金额   | :star::star::star: |
| now_disc_amount  | float     |                    |      | 当前折扣金额   | :star::star::star: |
| paid_amount      | float     |                    |      | 已支付总额     | :star::star:       |
| disc_amount      | float     |                    |      | 折扣总额       | :star::star:       |
| prepay_amount    | float     |                    |      | 使用预收金额   | :star::star:       |
| maker_id         | int       |                    |      | 制单人         | :star::star:       |
| make_time        | timestamp |                    |      | 制单时间       | :star::star:       |
| happen_time      | timestamp |                    |      | 交易时间       | :star::star:       |
| approver_id      | int       |                    |      | 审核人         | :star::star:       |
| approve_time     | timestamp |                    |      | 审核时间       | :star::star:       |
| seller_id        | int       |                    |      | 业务员         | :star::star:       |
| make_brief       | text      |                    |      | 整单备注       | :star::star:       |
| invoice_no       | text      |                    |      | 发票号         |                    |
| approve_brief    | text      |                    |      | 审核备注       |                    |
| submit_time      | timestamp |                    |      | 提交时间       |                    |
| visit_id         | int       |                    |      | 拜访单号       | :star:             |
| senders_id       | text      |                    |      | 送货员号       | :star::star:       |
| senders_name     | text      |                    |      | 送货员名称     | :star::star:       |
| sheet_attribute  | jsonb     |                    |      | 单据信息       | :star::star:       |
| total_quantity   | text      |                    |      | 总数量(大中小) | :star::star:       |

##### b.2 销售单 (详) sheet_sale_detail

| 列名                 | 类型      | 不为空             | 键   | 说明               | 重要性             |
| -------------------- | --------- | ------------------ | ---- | ------------------ | ------------------ |
| company_id           | int       | :heavy_check_mark: |      | 公司号             | :star::star::star: |
| flow_id              | int       | :heavy_check_mark: |      | 流水号             | :star::star::star: |
| inout_flag           | int2      |                    |      | 商品进出标志       | :star::star:       |
| sheet_id             | int       | :heavy_check_mark: |      | 单号               | :star::star::star: |
| row_index            | int       |                    |      | 商品行号           | :star::star:       |
| item_id              | int       | :heavy_check_mark: |      | 商品号             | :star::star::star: |
| sheet_item_name      | text      |                    |      | 商品名             |                    |
| branch_id            | int       |                    |      | 仓库               |                    |
| batch_id             | int       |                    |      |                    |                    |
| unit_no              | text      |                    |      | 单位               | :star::star::star: |
| unit_factor          | float     |                    |      | 包装率             | :star::star::star: |
| quantity             | float     |                    |      | 数量               | :star::star::star: |
| orig_price           | float     |                    |      | 原价               |                    |
| real_price           | float     |                    |      | 实际售价           | :star::star::star: |
| cost_price_prop      | float     |                    |      | 成本价             |                    |
| cost_price           | float     |                    |      | 成本价             |                    |
| cost_price_avg       | float     |                    |      | 加权成本价         | :star::star::star: |
| sub_amount           | float     |                    |      | 小计               | :star::star::star: |
| combine_flag         | text      |                    |      |                    |                    |
| tax_amount           | float     |                    |      | 税额               |                    |
| happen_time          | timestamp |                    |      | 交易时间           | :star::star::star: |
| remark               | text      |                    |      | 备注自定义         | :star::star::star: |
| cost_price_suspect   | boolean   |                    |      | 商品加权价质疑标记 | :star:             |
| remark_id            | int       |                    |      | 选择备注号         |                    |
| sys_price            | float     |                    |      | 系统推荐价格       | :star::star:       |
| produce_date | text      |                    |      | 虚拟生产日期       | :star:             |
| order_sub_id         | int       |                    |      | 定货会账户         | :star:             |
| cost_price_buy       | float     |                    |      | 进价               | :star:             |
| trade_type           | text      |                    |      | 交易类型           | :star:             |
| disp_month_id        | int       |                    |      | 陈列月份           | :star:             |
| disp_flow_id         | int       |                    |      | 陈列单flow         | :star:             |
| disp_sheet_id        | int       |                    |      | 陈列单单号         | :star:             |

##### c.1  销售订单 (主) sheet_sale_order_main

| 列名             | 类型      | 不为空             | 键   | 说明           | 重要性             |
| ---------------- | --------- | ------------------ | ---- | -------------- | ------------------ |
| company_id       | int       | :heavy_check_mark: |      | 公司号         | :star::star::star: |
| sheet_id         | int       | :heavy_check_mark: |      | 单号           | :star::star::star: |
| sheet_no         | text      | :heavy_check_mark: |      | 单据编号       | :star::star::star: |
| sheet_type       | text      |                    |      | 单据类型       | :star::star::star: |
| money_inout_flag | int2      |                    |      | 金钱正负标记   | :star::star::star: |
| branch_id        | int       |                    |      | 仓库           | :star::star::star: |
| red_flag         | int2      |                    |      | 红冲标记       | :star::star::star: |
| red_sheet_id     | int       |                    |      | 红冲单号       | :star::star::star: |
| supcust_id       | int       |                    |      | 客户号         | :star::star::star: |
| shop_id          | int       |                    |      | 门店号         |                    |
| total_amount     | float     | :heavy_check_mark: |      | 单据金额       | :star::star::star: |
| is_retail        | boolean   |                    |      | 是否零售       |                    |
| tax_amount       | float     |                    |      | 税额           |                    |
| discount_info    | text      |                    |      | 折扣信息       |                    |
| payway1_id       | int       |                    |      | 支付方式1      | :star::star::star: |
| payway1_amount   | float     |                    |      | 支付金额1      | :star::star::star: |
| payway2_id       | int       |                    |      | 支付方式2      | :star::star::star: |
| payway2_amount   | float     |                    |      | 支付金额2      | :star::star::star: |
| now_pay_amount   | float     |                    |      | 当前支付金额   | :star::star::star: |
| now_disc_amount  | float     |                    |      | 当前折扣金额   | :star::star::star: |
| paid_amount      | float     |                    |      | 已支付总额     | :star::star::star: |
| disc_amount      | float     |                    |      | 折扣总额       | :star::star::star: |
| maker_id         | int       |                    |      | 制单人         | :star::star:       |
| make_time        | timestamp |                    |      | 制单时间       | :star::star:       |
| happen_time      | timestamp |                    |      | 交易时间       | :star::star:       |
| approver_id      | int       |                    |      | 审核人         | :star::star:       |
| approve_time     | timestamp |                    |      | 审核时间       | :star::star:       |
| seller_id        | int       |                    |      | 业务员         | :star::star:       |
| make_brief       | text      |                    |      | 整单备注       | :star::star:       |
| approve_brief    | text      |                    |      | 审核备注       | :star::star:       |
| submit_time      | timestamp |                    |      | 提交时间       |                    |
| visit_id         | int       |                    |      | 拜访单号       | :star:             |
| senders_id       | text      |                    |      | 送货员号       | :star::star:       |
| senders_name     | text      |                    |      | 送货员名称     | :star::star:       |
| sheet_attribute  | jsonb     |                    |      | 单据信息       | :star::star:       |
| total_quantity   | text      |                    |      | 总数量(大中小) | :star:             |

##### c.2 销售订单 (详) sheet_sale_order_detail

| 列名                 | 类型      | 不为空             | 键   | 说明         | 重要性             |
| -------------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id           | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| flow_id              | int       | :heavy_check_mark: |      | 流水号       | :star:             |
| inout_flag           | int2      |                    |      | 商品进出标志 | :star::star:       |
| sheet_id             | int       | :heavy_check_mark: |      | 单号         | :star::star::star: |
| row_index            | int       |                    |      | 商品行号     | :star::star:       |
| item_id              | int       | :heavy_check_mark: |      | 商品号       | :star::star::star: |
| sheet_item_name      | text      |                    |      | 商品名       |                    |
| branch_id            | int       |                    |      | 仓库         |                    |
| batch_id             | int       |                    |      |              |                    |
| unit_no              | text      |                    |      | 单位         | :star::star::star: |
| unit_factor          | float     |                    |      | 包装率       | :star::star::star: |
| quantity             | float     |                    |      | 数量         | :star::star::star: |
| orig_price           | float     |                    |      | 原价         | :star::star:       |
| real_price           | float     |                    |      | 实际售价     | :star::star:       |
| cost_price_prop      | float     |                    |      | 成本价       |                    |
| cost_price           | float     |                    |      | 成本价       |                    |
| cost_price_avg       | float     |                    |      | 加权成本价   | :star::star:       |
| sub_amount           | float     |                    |      | 小计         | :star::star:       |
| combine_flag         | text      |                    |      |              | :star:             |
| tax_amount           | float     |                    |      | 税额         |                    |
| happen_time          | timestamp |                    |      | 交易时间     |                    |
| remark               | text      |                    |      | 备注自定义   | :star::star::star: |
| remark_id            | int       |                    |      | 选择备注号   | :star::star:       |
| sys_price            | float     |                    |      | 系统推荐价格 | :star::star:       |
| produce_date | text      |                    |      | 虚拟生产日期 | :star:             |
| cost_price_buy       | float     |                    |      | 进价         | :star::star::star: |
| trade_type           | text      |                    |      | 交易类型     | :star::star::star: |
| disp_month_id        | int       |                    |      | 陈列月份     | :star:             |
| disp_flow_id         | int       |                    |      | 陈列单flow   | :star:             |
| disp_sheet_id        | int       |                    |      | 陈列单单号   | :star:             |

##### d.1  调拨单(主) sheet_move_main

| 列名             | 类型      | 不为空             | 键   | 说明       | 重要性             |
| ---------------- | --------- | ------------------ | ---- | ---------- | ------------------ |
| company_id       | int       | :heavy_check_mark: |      | 公司号     | :star::star::star: |
| sheet_id         | int       | :heavy_check_mark: |      | 单号       | :star::star::star: |
| sheet_no         | text      | :heavy_check_mark: |      | 单据编号   | :star::star::star: |
| order_sheet_id   | int       |                    |      | 订单单号   | :star:             |
| red_flag         | int2      |                    |      | 红冲标记   | :star:             |
| red_sheet_id     | int       |                    |      | 红冲单号   |                    |
| seller_id        | int       |                    |      | 业务员     | :star::star::star: |
| from_branch_id   | int       |                    |      | 出仓号     | :star::star::star: |
| to_branch_id     | int       |                    |      | 入仓号     | :star::star::star: |
| wholesale_amount | float     |                    |      | 批发金额   | :star::star:       |
| buy_amount       | float     |                    |      | 进价金额   | :star::star:       |
| cost_amount_avg  | float     |                    |      | 加权价金额 |                    |
| cost_amount_prop | float     |                    |      | 成本价额   |                    |
| maker_id         | int       |                    |      | 制单人     | :star::star:       |
| make_time        | timestamp |                    |      | 制单时间   | :star::star:       |
| happen_time      | timestamp |                    |      | 交易时间   | :star::star:       |
| approver_id      | int       |                    |      | 审核人     | :star::star:       |
| approve_time     | timestamp |                    |      | 审核时间   | :star::star:       |
| make_brief       | text      |                    |      | 整单备注   | :star::star:       |
| approve_brief    | text      |                    |      | 审核备注   | :star::star:       |
| submit_time      | timestamp |                    |      | 提交时间   |                    |

##### d.2 调拨单 (详) sheet_move_detail

| 列名            | 类型      | 不为空             | 键   | 说明         | 重要性             |
| --------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id      | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| flow_id         | int       | :heavy_check_mark: |      | 流水号       | :star::star:       |
| inout_flag      | int2      |                    |      | 商品进出标志 | :star::star:       |
| sheet_id        | int       | :heavy_check_mark: |      | 单号         | :star::star::star: |
| row_index       | int       |                    |      | 商品行号     | :star::star:       |
| item_id         | int       | :heavy_check_mark: |      | 商品号       | :star::star::star: |
| sheet_item_name | text      |                    |      | 商品名       |                    |
| branch_id       | int       |                    |      | 仓库         | :star::star::star: |
| batch_id        | int       |                    |      |              |                    |
| unit_no         | text      |                    |      | 单位         | :star::star::star: |
| unit_factor     | float     |                    |      | 包装率       | :star::star::star: |
| quantity        | float     |                    |      | 数量         | :star::star:       |
| cost_price_prop | float     |                    |      | 成本价       |                    |
| buy_price       | float     |                    |      | 进价         |                    |
| cost_price_avg  | float     |                    |      | 加权成本价   | :star::star:       |
| wholesale_price | float     |                    |      | 批发价       | :star::star:       |
| happen_time     | timestamp |                    |      | 交易时间     |                    |
| remark          | text      |                    |      | 备注自定义   | :star::star:       |
| remark_id       | int       |                    |      | 选择备注号   | :star:             |

##### e.1  盘点单(主) sheet_inventory_main

| 列名             | 类型      | 不为空             | 键   | 说明       | 重要性             |
| ---------------- | --------- | ------------------ | ---- | ---------- | ------------------ |
| company_id       | int       | :heavy_check_mark: |      | 公司号     | :star::star::star: |
| sheet_id         | int       | :heavy_check_mark: |      | 单号       | :star::star::star: |
| sheet_no         | text      | :heavy_check_mark: |      | 单据编号   | :star::star:       |
| red_flag         | int2      |                    |      | 红冲标记   | :star::star:       |
| red_sheet_id     | int       |                    |      | 红冲单号   |                    |
| sheet_type       | text      |                    |      | 单据类型   | :star::star::star: |
| seller_id        | int       |                    |      | 业务员     | :star::star::star: |
| branch_id        | int       |                    |      | 仓库号     | :star::star::star: |
| wholesale_amount | float     |                    |      | 批发金额   | :star::star:       |
| cost_amount_avg  | float     |                    |      | 加权价金额 | :star:             |
| cost_amount_prop | float     |                    |      | 成本价额   |                    |
| buy_amount       | float     |                    |      | 进价金额   |                    |
| maker_id         | int       |                    |      | 制单人     | :star::star:       |
| make_time        | timestamp |                    |      | 制单时间   | :star::star:       |
| happen_time      | timestamp |                    |      | 交易时间   | :star::star:       |
| approver_id      | int       |                    |      | 审核人     | :star::star:       |
| approve_time     | timestamp |                    |      | 审核时间   | :star::star:       |
| make_brief       | text      |                    |      | 整单备注   | :star::star:       |
| approve_brief    | text      |                    |      | 审核备注   | :star::star:       |
| submit_time      | timestamp |                    |      | 提交时间   |                    |

##### e.2 盘点单 (详) sheet_move_detail

| 列名            | 类型      | 不为空             | 键   | 说明         | 重要性             |
| --------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id      | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| flow_id         | int       | :heavy_check_mark: |      | 流水号       | :star:             |
| inout_flag      | int2      |                    |      | 商品进出标志 | :star::star:       |
| sheet_id        | int       | :heavy_check_mark: |      | 单号         | :star::star::star: |
| row_index       | int       |                    |      | 商品行号     | :star::star:       |
| item_id         | int       | :heavy_check_mark: |      | 商品号       | :star::star::star: |
| sheet_item_name | text      |                    |      | 商品名       |                    |
| branch_id       | int       |                    |      | 仓库         | :star::star::star: |
| batch_id        | int       |                    |      |              |                    |
| unit_no         | text      |                    |      | 单位         | :star::star::star: |
| unit_factor     | float     |                    |      | 包装率       | :star::star::star: |
| quantity        | float     |                    |      | 数量         | :star::star::star: |
| cost_price_prop | float     |                    |      | 成本价       | :star:             |
| buy_price       | float     |                    |      | 进价         | :star::star:       |
| cost_price_avg  | float     |                    |      | 加权成本价   | :star::star:       |
| wholesale_price | float     |                    |      | 批发价       | :star::star:       |
| happen_time     | timestamp |                    |      | 交易时间     |                    |
| remark          | text      |                    |      | 备注自定义   | :star::star:       |
| remark_id       | int       |                    |      | 选择备注号   | :star:             |

##### f.1  盘点盈亏单(主) sheet_invent_change_main

| 列名               | 类型      | 不为空             | 键   | 说明       | 重要性             |
| ------------------ | --------- | ------------------ | ---- | ---------- | ------------------ |
| company_id         | int       | :heavy_check_mark: |      | 公司号     | :star::star::star: |
| sheet_id           | int       | :heavy_check_mark: |      | 单号       | :star::star::star: |
| sheet_no           | text      | :heavy_check_mark: |      | 单据编号   | :star::star::star: |
| order_sheet_id     | int       |                    |      |            |                    |
| red_flag           | int2      |                    |      | 红冲标记   | :star::star:       |
| red_sheet_id       | int       |                    |      | 红冲单号   | :star::star:       |
| sheet_type         | text      |                    |      | 单据类型   | :star::star::star: |
| seller_id          | int       |                    |      | 业务员     | :star::star::star: |
| branch_id          | int       |                    |      | 仓库号     | :star::star::star: |
| wholesale_amount   | float     |                    |      | 批发金额   | :star::star:       |
| cost_amount_avg    | float     |                    |      | 加权价金额 | :star::star:       |
| cost_amount_prop   | float     |                    |      | 成本价额   | :star::star:       |
| buy_amount         | float     |                    |      | 进价金额   | :star::star:       |
| maker_id           | int       |                    |      | 制单人     | :star::star:       |
| make_time          | timestamp |                    |      | 制单时间   | :star::star:       |
| happen_time        | timestamp |                    |      | 交易时间   | :star::star:       |
| approver_id        | int       |                    |      | 审核人     | :star::star:       |
| approve_time       | timestamp |                    |      | 审核时间   | :star::star:       |
| make_brief         | text      |                    |      | 整单备注   | :star::star:       |
| approve_brief      | text      |                    |      | 审核备注   | :star::star:       |
| submit_time        | timestamp |                    |      | 提交时间   |                    |
| inventory_sheet_id | int       |                    |      | 盘点单单号 | :star:             |

##### f.2 盘点盈亏单 (详) sheet_invent_change_detail

| 列名            | 类型      | 不为空             | 键   | 说明         | 重要性             |
| --------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id      | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| flow_id         | int       | :heavy_check_mark: |      | 流水号       | :star:             |
| inout_flag      | int2      |                    |      | 商品进出标志 | :star::star::star: |
| sheet_id        | int       | :heavy_check_mark: |      | 单号         | :star::star::star: |
| row_index       | int       |                    |      | 商品行号     | :star:             |
| item_id         | int       | :heavy_check_mark: |      | 商品号       | :star::star::star: |
| sheet_item_name | text      |                    |      | 商品名       |                    |
| branch_id       | int       |                    |      | 仓库         | :star::star::star: |
| batch_id        | int       |                    |      |              |                    |
| unit_no         | text      |                    |      | 单位         | :star::star::star: |
| unit_factor     | float     |                    |      | 包装率       | :star::star::star: |
| quantity        | float     |                    |      | 数量         | :star::star::star: |
| cost_price_prop | float     |                    |      | 成本价       |                    |
| buy_price       | float     |                    |      | 进价         | :star:             |
| cost_price_avg  | float     |                    |      | 加权成本价   | :star:             |
| wholesale_price | float     |                    |      | 批发价       | :star:             |
| cost_price      | float     |                    |      | 成本价       |                    |
| happen_time     | timestamp |                    |      | 交易时间     |                    |
| remark          | text      |                    |      | 备注自定义   | :star::star::star: |

##### g.1收款单(主) sheet_get_arrears_main

| 列名             | 类型      | 不为空             | 键   | 说明         | 重要性             |
| ---------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id       | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| sheet_id         | int       | :heavy_check_mark: |      | 单号         | :star::star::star: |
| sheet_no         | text      | :heavy_check_mark: |      | 单据编号     | :star::star::star: |
| sheet_type       | text      |                    |      | 单据类型     | :star::star::star: |
| supcust_id       | int       |                    |      | 客户号       | :star::star::star: |
| money_inout_flag | int       |                    |      | 金钱正负标记 | :star::star:       |
| sheet_amount     | float     |                    |      | 需收款总额   | :star::star::star: |
| disc_amount      | float     |                    |      | 优惠金额     | :star::star::star: |
| paid_amount      | float     |                    |      | 已支付金额   | :star::star::star: |
| left_amount      | float     |                    |      | 尚欠金额     | :star::star::star: |
| approve_flag     | int2      |                    |      |              |                    |
| red_flag         | int2      |                    |      | 红冲标记     | :star::star:       |
| red_sheet_id     | int       |                    |      | 红冲单号     | :star::star::star: |
| maker_id         | int       |                    |      | 制单人       | :star::star:       |
| make_time        | timestamp |                    |      | 制单时间     | :star::star:       |
| happen_time      | timestamp |                    |      | 交易时间     | :star::star:       |
| approver_id      | int       |                    |      | 审核人       | :star::star:       |
| approve_time     | timestamp |                    |      | 审核时间     | :star::star:       |
| getter_id        | int       |                    |      | 业务员       | :star::star::star: |
| payway1_id       | int       |                    |      | 支付方式1    | :star::star::star: |
| payway1_amount   | float     |                    |      | 支付金额1    | :star::star::star: |
| payway2_id       | int       |                    |      | 支付方式2    | :star::star:       |
| payway2_amount   | float     |                    |      | 支付金额2    | :star::star:       |
| now_pay_amount   | float     |                    |      | 当前支付金额 | :star::star::star: |
| now_disc_amount  | float     |                    |      | 当前折扣金额 | :star::star::star: |
| make_brief       | text      |                    |      | 整单备注     | :star::star:       |
| approve_brief    | text      |                    |      | 审核备注     |                    |
| submit_time      | timestamp |                    |      | 提交时间     |                    |
| visit_id         | int       |                    |      | 拜访单号     | :star:             |

##### g.2收款单(详) sheet_get_arrears_detail

| 列名            | 类型      | 不为空             | 键   | 说明           | 重要性             |
| --------------- | --------- | ------------------ | ---- | -------------- | ------------------ |
| company_id      | int       | :heavy_check_mark: |      | 公司号         | :star::star::star: |
| sheet_id        | int       |                    |      | 单号           | :star::star::star: |
| mm_sheet_id     | int       |                    |      | 详单单号       | :star::star::star: |
| inout_flag      | int       |                    |      |                | :star:             |
| row_index       | int       |                    |      | 顺序号         | :star::star::star: |
| sheet_amount    | float     |                    |      | 单据金额       | :star::star::star: |
| paid_amount     | numeric   |                    |      | 单据已付金额   | :star::star::star: |
| left_amount     | numeric   |                    |      | 单据剩余金额   | :star::star::star: |
| disc_amount     | numeric   |                    |      | 单据已折扣金额 | :star::star::star: |
| now_pay_amount  | numeric   |                    |      | 当前支付金额   | :star::star::star: |
| now_disc_amount | numeric   |                    |      | 当前折扣金额   | :star::star::star: |
| happen_time     | timestamp |                    |      | 交易时间       |                    |
| remark          | text      |                    |      | 备注           | :star:             |
| mm_sheet_type   | text      |                    |      | 单据类型       | :star::star::star: |

##### h 预收款单 sheet_prepay

| 列名                  | 类型      | 不为空             | 键   | 说明         | 重要性             |
| --------------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id            | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| sheet_id              | int       | :heavy_check_mark: |      | 单号         | :star::star::star: |
| red_sheet_id          | int       |                    |      | 红冲单号     | :star::star:       |
| sheet_no              | text      | :heavy_check_mark: |      | 单据编号     | :star::star::star: |
| sheet_type            | text      |                    |      | 单据类型     | :star::star::star: |
| supcust_id            | int       |                    |      | 客户号       | :star::star::star: |
| getter_id             | int       |                    |      | 业务员       | :star::star::star: |
| total_amount          | float     | :heavy_check_mark: |      | 单据金额     | :star::star::star: |
| prepay_sub_id         | int       |                    |      | 预收款账户   | :star::star::star: |
| approve_flag          | int2      |                    |      |              |                    |
| red_flag              | int2      |                    |      | 红冲标记     | :star::star:       |
| order_sheet_id        | int       |                    |      | 销售订单     |                    |
| maker_id              | int       |                    |      | 制单人       | :star::star:       |
| make_time             | timestamp |                    |      | 制单时间     | :star::star:       |
| approver_id           | int       |                    |      | 审核人       | :star::star:       |
| approve_time          | timestamp |                    |      | 审核时间     | :star::star:       |
| happen_time           | timestamp |                    |      | 交易时间     | :star::star:       |
| payway1_id            | int       |                    |      | 支付方式1    | :star::star:       |
| payway1_amount        | float     |                    |      | 支付金额1    | :star::star:       |
| payway2_id            | int       |                    |      | 支付方式2    | :star::star:       |
| payway2_amount        | float     |                    |      | 支付金额2    | :star::star:       |
| make_brief            | text      |                    |      | 整单备注     | :star::star:       |
| submit_time           | timestamp |                    |      | 提交时间     |                    |
| disc_amount           | float     |                    |      | 折扣总额     | :star::star:       |
| money_inout_flag      | int2      |                    |      | 金钱正负标记 | :star::star:       |
| approve_brief         | text      |                    |      | 审核备注     |                    |
| visit_id              | int       |                    |      | 拜访单号     | :star:             |
| now_pay_amount        | numeric   |                    |      | 当前支付金额 | :star::star::star: |
| now_disc_amount       | numeric   |                    |      | 当前折扣金额 | :star::star::star: |
| order_adjust_sheet_id | int       |                    |      | 调整单       | :star:             |
| sheet_attribute       | jsonb     |                    |      | 单据信息     | :star:             |

##### i.1 定货会(主) sheet_prepay

##### i.2 定货会(详) sheet_order_item_detail

| 列名                 | 类型      | 不为空             | 键   | 说明         | 重要性             |
| -------------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id           | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| flow_id              | int       | :heavy_check_mark: |      | 流水号       | :star::star::star: |
| inout_flag           | int2      |                    |      | 商品进出标志 | :star::star:       |
| sheet_id             | int       | :heavy_check_mark: |      | 单号         | :star::star:       |
| row_index            | int       |                    |      | 商品行号     | :star:             |
| item_id              | int       | :heavy_check_mark: |      | 商品号       | :star::star::star: |
| sheet_item_name      | text      |                    |      | 商品名       |                    |
| branch_id            | int       |                    |      | 仓库         |                    |
| batch_id             | int       |                    |      |              |                    |
| unit_no              | text      |                    |      | 单位         | :star::star::star: |
| unit_factor          | float     |                    |      | 包装率       | :star::star::star: |
| quantity             | float     |                    |      | 数量         | :star::star::star: |
| orig_price           | float     |                    |      | 原价         | :star::star::star: |
| real_price           | float     |                    |      | 实际售价     | :star::star:       |
| cost_price_prop      | float     |                    |      | 成本价       |                    |
| cost_price           | float     |                    |      | 成本价       |                    |
| sub_amount           | float     |                    |      | 小计         | :star::star::star: |
| produce_date | text      |                    |      | 虚拟生产日期 |                    |
| combine_flag         | text      |                    |      |              |                    |
| tax_amount           | float     |                    |      | 税额         |                    |
| happen_time          | timestamp |                    |      | 交易时间     | :star::star::star: |
| remark               | text      |                    |      | 备注自定义   | :star:             |

#####  j.1 定货会调整单(主) sheet_item_ordered_adjust_main

| 列名             | 类型      | 不为空             | 键   | 说明         | 重要性             |
| ---------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id       | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| sheet_id         | int       | :heavy_check_mark: |      | 单号         | :star::star::star: |
| sheet_no         | text      | :heavy_check_mark: |      | 单据编号     | :star::star::star: |
| red_sheet_id     | int       |                    |      | 红冲单号     |                    |
| sheet_type       | text      |                    |      | 单据类型     | :star::star:       |
| money_inout_flag | int2      |                    |      | 金钱正负标记 |                    |
| supcust_id       | int       |                    |      | 客户号       | :star::star:       |
| prepay_sub_id    | int       |                    |      | 预收款账户   | :star::star::star: |
| seller_id        | int       |                    |      | 业务员       | :star::star::star: |
| red_flag         | int2      |                    |      | 红冲标记     |                    |
| total_amount     | float     |                    |      | 单据金额     | :star::star::star: |
| now_pay_amount   | numeric   |                    |      | 当前支付金额 | :star::star::star: |
| now_disc_amount  | numeric   |                    |      | 当前折扣金额 | :star::star::star: |
| maker_id         | int       |                    |      | 制单人       | :star::star:       |
| make_time        | timestamp |                    |      | 制单时间     | :star::star:       |
| approver_id      | int       |                    |      | 审核人       | :star::star:       |
| approve_time     | timestamp |                    |      | 审核时间     | :star::star:       |
| happen_time      | timestamp |                    |      | 交易时间     | :star::star:       |
| make_brief       | text      |                    |      | 整单备注     | :star:             |
| approve_brief    | text      |                    |      | 审核备注     |                    |
| submit_time      | timestamp |                    |      | 提交时间     |                    |
| payway1_id       | int       |                    |      | 支付方式1    | :star::star:       |
| payway1_amount   | float     |                    |      | 支付金额1    | :star::star:       |
| payway2_id       | int       |                    |      | 支付方式2    | :star::star:       |
| payway2_amount   | float     |                    |      | 支付金额2    | :star::star:       |
| prepay_sheet_id  | int       |                    |      | 定货单号     | :star::star:       |

##### j.2  定货会调整单(详) sheet_item_ordered_adjust_detail

| 列名        | 类型      | 不为空             | 键   | 说明         | 重要性             |
| ----------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id  | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| flow_id     | int       | :heavy_check_mark: |      | 流水号       | :star:             |
| inout_flag  | int2      |                    |      | 商品进出标志 | :star::star:       |
| sheet_id    | int       |                    |      | 单号         | :star::star::star: |
| row_index   | int       |                    |      | 商品行号     | :star::star:       |
| item_id     | int       |                    |      | 商品号       | :star::star::star: |
| unit_no     | text      |                    |      | 单位         | :star::star::star: |
| unit_factor | float     |                    |      | 包装率       | :star::star::star: |
| quantity    | float     |                    |      | 数量         | :star::star::star: |
| real_price  | numeric   |                    |      | 实际售价     | :star::star::star: |
| sub_amount  | numeric   |                    |      | 小计         | :star::star:       |
| happen_time | timestamp |                    |      | 交易时间     |                    |
| remark      | text      |                    |      | 备注自定义   | :star::star:       |

##### k.1 费用支出(主) 

| 列名             | 类型           | 不为空             | 键   | 说明         | 重要性             |
| ---------------- | -------------- | ------------------ | ---- | ------------ | ------------------ |
| company_id       | int            | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| sheet_id         | int            | :heavy_check_mark: |      | 单号         | :star::star::star: |
| sheet_no         | text           | :heavy_check_mark: |      | 单据编号     | :star::star::star: |
| sheet_type       | text           |                    |      | 单据类型     | :star::star::star: |
| money_inout_flag | int2           |                    |      | 金钱正负标记 | :star::star::star: |
| branch_id        | int            |                    |      | 仓库         |                    |
| red_flag         | int2           |                    |      | 红冲标记     | :star::star:       |
| red_sheet_id     | int            |                    |      | 红冲单号     | :star::star:       |
| red_sheet_date   | date           |                    |      | 红冲单日期   |                    |
| supcust_id       | int            |                    |      | 客户号       | :star::star:       |
| shop_id          | int            |                    |      | 门店号       |                    |
| total_amount     | float          | :heavy_check_mark: |      | 单据金额     | :star::star::star: |
| payway1_id       | int            |                    |      | 支付方式1    | :star::star::star: |
| payway1_amount   | float          |                    |      | 支付金额1    | :star::star::star: |
| payway2_id       | int            |                    |      | 支付方式2    | :star::star:       |
| payway2_amount   | float          |                    |      | 支付金额2    | :star::star:       |
| now_pay_amount   | float          |                    |      | 当前支付金额 | :star::star::star: |
| now_disc_amount  | float          |                    |      | 当前折扣金额 | :star::star:       |
| getter_id        | int            |                    |      | 业务员       | :star::star:       |
| maker_id         | int            |                    |      | 制单人       | :star::star:       |
| make_time        | timestamp      |                    |      | 制单时间     | :star::star:       |
| approver_id      | int            |                    |      | 审核人       | :star::star:       |
| approve_time     | timestampfloat |                    |      | 审核时间     | :star::star:       |
| happen_time      | timestamp      |                    |      | 交易时间     | :star::star:       |
| make_brief       | text           |                    |      | 整单备注     | :star::star:       |
| approve_brief    | text           |                    |      | 审核备注     |                    |
| submit_time      | timestamp      |                    |      | 提交时间     | :star::star:       |
| visit_id         | int            |                    |      | 拜访单号     | :star:             |

##### k.2 费用支出(详) sheet_fee_out_detail 

| 列名            | 类型      | 不为空             | 键   | 说明         | 重要性             |
| --------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id      | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| sheet_id        | int       |                    |      | 单号         | :star::star::star: |
| fee_sub_id      | int       |                    |      | 费用支出账户 | :star::star::star: |
| supcust_id      | int       |                    |      | 客户号       | :star::star::star: |
| inout_flag      | int       |                    |      |              |                    |
| row_index       | int       |                    |      | 顺序号       | :star:             |
| fee_sub_amount  | float     |                    |      | 支出小计     | :star::star::star: |
| now_pay_amount  | numeric   |                    |      | 当前支付金额 | :star::star::star: |
| now_disc_amount | numeric   |                    |      | 当前折扣金额 | :star::star::star: |
| happen_time     | timestamp |                    |      | 交易时间     | :star::star::star: |
| remark          | text      |                    |      | 备注         | :star::star:       |
| display_id      | int       |                    |      | 陈列协议单号 | :star::star:       |

##### l.1 陈列协议(主) display_agreement_main 

| 列名              | 类型           | 不为空             | 键   | 说明                   | 重要性             |
| ----------------- | -------------- | ------------------ | ---- | ---------------------- | ------------------ |
| company_id        | int            | :heavy_check_mark: |      | 公司号                 | :star::star::star: |
| sheet_id          | int            | :heavy_check_mark: |      | 单号                   | :star::star::star: |
| sheet_no          | text           | :heavy_check_mark: |      | 单据编号               | :star::star::star: |
| sheet_type        | text           |                    |      | 单据类型               | :star::star::star: |
| money_inout_flag  | int2           |                    |      | 金钱正负标记           | :star::star:       |
| red_flag          | int2           |                    |      | 红冲标记               | :star::star:       |
| red_sheet_id      | int            |                    |      | 红冲单号               | :star::star:       |
| red_sheet_date    | date           |                    |      | 红冲单日期             |                    |
| supcust_id        | int            |                    |      | 客户号                 | :star::star::star: |
| fee_sub_id        | int            |                    |      | 陈列账户               | :star::star::star: |
| total_amount      | float          | :heavy_check_mark: |      | 单据金额               | :star::star::star: |
| tax_amount        | float          |                    |      | 税额                   |                    |
| start_time        | timestamp      |                    |      | 陈列开始时间           | :star::star:       |
| end_time          | timestamp      |                    |      | 陈列结束时间           | :star::star:       |
| maker_id          | int            |                    |      | 制单人                 | :star::star:       |
| make_time         | timestamp      |                    |      | 制单时间               | :star::star:       |
| approver_id       | int            |                    |      | 审核人                 | :star::star:       |
| approve_time      | timestampfloat |                    |      | 审核时间               | :star::star:       |
| make_brief        | text           |                    |      | 整单备注               | :star::star:       |
| approve_brief     | text           |                    |      | 审核备注               | :star::star:       |
| happen_time       | timestamp      |                    |      | 交易时间               | :star::star:       |
| submit_time       | timestamp      |                    |      | 提交时间               |                    |
| adjust_sheet_id   | int            |                    |      | 调成单单号             | :star::star::star: |
| orig_sheet_id     | int            |                    |      | 陈列原始单号           | :star::star::star: |
| adjusted_sheet_id | int            |                    |      | 该单据对应的被调整单号 | :star::star::star: |
| total_money       | float          |                    |      | 总金额                 | :star::star::star: |
| total_quantity    | text           |                    |      | 大中小数量             | :star::star:       |
| terminator_oper   | int            |                    |      | 终止协议业务员         | :star::star:       |
| terminate_time    | timestamp      |                    |      | 终止时间               | :star::star:       |

##### l.2 陈列协议(详) display_agreement_detail 

| 列名          | 类型      | 不为空             | 键   | 说明        | 重要性             |
| ------------- | --------- | ------------------ | ---- | ----------- | ------------------ |
| company_id    | int       | :heavy_check_mark: |      | 公司号      | :star::star::star: |
| flow_id       | int       | :heavy_check_mark: |      | 流水号      | :star::star::star: |
| sheet_id      | int       |                    |      | 单号        | :star::star::star: |
| items_id      | text      |                    |      | 商品号      | :star::star::star: |
| items_name    | text      |                    |      | 商品名称    | :star::star::star: |
| unit_no       | text      |                    |      | 商品单位    | :star::star::star: |
| month1_qty    | float     |                    |      | 1月数量     | :star::star:       |
| month1_given  | float     |                    |      | 1月已兑数量 | :star::star:       |
| month2_qty    | float     |                    |      | 2月数量     | :star::star:       |
| month2_given  | float     |                    |      | 2月已兑数量 | :star::star:       |
| month3_qty    | float     |                    |      | 3月数量     | :star::star:       |
| month3_given  | float     |                    |      | 3月已兑数量 | :star::star:       |
| month4_qty    | float     |                    |      | 4月数量     | :star::star:       |
| month4_given  | float     |                    |      | 4月已兑数量 | :star::star:       |
| month5_qty    | float     |                    |      | 5月数量     | :star::star:       |
| month5_given  | float     |                    |      | 5月已兑数量 | :star::star:       |
| month6_qty    | float     |                    |      | 6月数量     | :star::star:       |
| month6_given  | float     |                    |      |             | :star::star:       |
| month7_qty    | float     |                    |      |             | :star::star:       |
| month7_given  | float     |                    |      |             | :star::star:       |
| month8_qty    | float     |                    |      |             | :star::star:       |
| month8_given  | float     |                    |      |             | :star::star:       |
| month9_qty    | float     |                    |      |             | :star::star:       |
| month9_given  | float     |                    |      |             | :star::star:       |
| month10_qty   | float     |                    |      |             | :star::star:       |
| month10_given | float     |                    |      |             | :star::star:       |
| month11_qty   | float     |                    |      |             | :star::star:       |
| month11_given | float     |                    |      |             | :star::star:       |
| month12_qty   | float     |                    |      |             | :star::star:       |
| month12_given | float     |                    |      |             | :star::star:       |
| happen_time   | timestamp |                    |      | 交易日期    |                    |
| sub_amount    | float     |                    |      | 小计        | :star::star:       |
| remark        | text      |                    |      | 备注        | :star::star:       |
| inout_flag    | int2      |                    |      |             |                    |
| row_index     | int       |                    |      |             |                    |
| all_given     | bool      |                    |      | 全部已兑    | :star::star::star: |

##### m.1 拜访单据

| 列名             | 类型      | 不为空             | 键   | 说明         | 重要性             |
| ---------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id       | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| visit_id         | int       | :heavy_check_mark: |      | 拜访单号     | :star::star::star: |
| seller_id        | int       |                    |      | 业务员       | :star::star:       |
| shop_id          | int       |                    |      | 门店号       |                    |
| start_time       | timestamp |                    |      | 开始时间     | :star::star::star: |
| end_time         | timestamp |                    |      | 结束时间     | :star::star::star: |
| door_picture     | text      |                    |      | 门店照片     | :star::star::star: |
| showcase_picture | jsonb     |                    |      | 门头照       | :star::star::star: |
| longitude        | float     |                    |      | 经度         | :star::star:       |
| latitude         | float     |                    |      | 纬度         | :star::star:       |
| have_trade       | bool      |                    |      | 交易方式     | :star::star:       |
| visit_region     | text      |                    |      | 拜访片区     |                    |
| sale_amount      | float     |                    |      | 销售金额     | :star:             |
| order_amount     | float     |                    |      | 销售订单金额 | :star::star:       |
| remark           | text      |                    |      | 备注         |                    |
| supcust_id       | int       |                    |      | 客户         | :star:             |
| sign_distance    | int       |                    |      |              |                    |

##### m.2 拜访轨迹 seller_trail

| 列名        | 类型      | 不为空             | 键   | 说明     | 重要性             |
| ----------- | --------- | ------------------ | ---- | -------- | ------------------ |
| company_id  | int       | :heavy_check_mark: |      | 公司号   | :star::star::star: |
| flow_id     | int       | :heavy_check_mark: |      | 流水号   |                    |
| seller_id   | int       |                    |      | 业务员   | :star::star::star: |
| longitude   | float     |                    |      | 经度     | :star::star::star: |
| latitude    | float     |                    |      | 纬度     | :star::star::star: |
| happen_time | timestamp |                    |      | 发生时间 | :star::star:       |
| visit_id    | int       |                    |      | 拜访单号 | :star::star::star: |

##### n.1 交账单(主) sheet_check_sheets_main 

| 列名                 | 类型           | 不为空             | 键   | 说明                 | 重要性             |
| -------------------- | -------------- | ------------------ | ---- | -------------------- | ------------------ |
| company_id           | int            | :heavy_check_mark: |      | 公司号               | :star::star::star: |
| sheet_id             | int            | :heavy_check_mark: |      | 单号                 | :star::star::star: |
| getter_id            | int            |                    |      | 业务员               | :star::star::star: |
| happen_time          | timestamp      |                    |      | 交易时间             | :star::star:       |
| maker_id             | int            |                    |      | 制单人               | :star::star:       |
| make_time            | timestamp      |                    |      | 制单时间             | :star::star:       |
| approver_id          | int            |                    |      | 审核人               | :star::star:       |
| approve_time         | timestampfloat |                    |      | 审核时间使用预收金额 | :star::star:       |
| make_brief           | text           |                    |      | 整单备注             | :star:             |
| red_flag             | int2           |                    |      | 红冲标记             | :star::star:       |
| sale_amount          | float          |                    |      | 销售金额             | :star::star::star: |
| return_amount        | float          |                    |      | 退货金额             | :star::star::star: |
| get_arrears          | float          |                    |      | 收款单金额           | :star::star::star: |
| get_prepay           | float          |                    |      | 预收款单金额         | :star::star::star: |
| fee_out              | float          |                    |      | 费用支出单总额       | :star::star::star: |
| income               | float          |                    |      | 其他收入单总额       | :star::star::star: |
| start_time           | timestamp      |                    |      | 单据开始时间         | :star:             |
| end_time             | timestamp      |                    |      | 结束时间             | :star:             |
| sale_total_amount    | float          |                    |      | 销售净额             | :star::star::star: |
| sale_disc_amount     | float          |                    |      | 销售优惠金额         | :star::star::star: |
| sale_left_amount     | float          |                    |      | 销售单欠款           | :star::star::star: |
| prepay_total_amount  | float          |                    |      | 预收款单总额         | :star::star::star: |
| prepay_disc_amount   | float          |                    |      | 预收款单优惠         | :star::star::star: |
| prepay_left_amount   | float          |                    |      | 预收款单欠款         | :star::star::star: |
| arrears_disc_amount  | float          |                    |      | 欠款单优惠           | :star::star::star: |
| sale_prepay_amount   | float          |                    |      | 欠款单总额           | :star::star::star: |
| arrears_total_amount | float          |                    |      | 收欠款单总额         | :star::star::star: |

##### n.1 交账单(详) sheet_check_sheets_detail

| 列名                | 类型 | 不为空             | 键   | 说明         | 重要性             |
| ------------------- | ---- | ------------------ | ---- | ------------ | ------------------ |
| company_id          | int  | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| sheet_id            | int  | :heavy_check_mark: |      | 单号         | :star::star::star: |
| business_sheet_id   | int  |                    |      | 业务单据号   | :star::star::star: |
| business_sheet_no   | text |                    |      | 业务单据编号 | :star::star:       |
| business_sheet_type | text |                    |      | 业务单据类型 | :star::star:       |

##### o.1 贷款单 sheet_loan

| 列名                | 类型      | 不为空             | 键   | 说明         | 重要性               | 值             | 
| ------------------- | --------- | ------------------ | ---- | ------------ | -------------------- | -------------- | 
| company_id          | int       | :heavy_check_mark: |      | 公司号       | :star: :star: :star: |                |
| sheet_id            | int       | :heavy_check_mark: |      | 单号         | :star: :star: :star: |                |
| sheet_no            | int       | :heavy_check_mark: |      | 单号         | :star: :star: :star: |                |
| sheet_type          | int       | :heavy_check_mark: |      | 单据类型     | :star: :star: :star: |  DK            |
| red_flag            | int       |                    |      | 红冲标记     | :star: :star:        |                |
| red_sheet_id        | int       |                    |      | 红字单单号   | :star: :star:        |                |
| partner_id          | int       | :heavy_check_mark: |      | 借贷款单位   | :star: :star: :star: |                |
| getter_id           | int       |                    |      | 业务员       | :star: :star:        |                |
| loan_partner_id     | int       | :heavy_check_mark: |      | 账户         | :star: :star: :star: |                |
| happen_time         | timestamp | :heavy_check_mark: |      | 交易时间     | :star: :star:        |                |
| maker_id            | int       | :heavy_check_mark: |      | 制单人       | :star: :star:        |                |
| make_time           | timestamp | :heavy_check_mark: |      | 制单时间     | :star: :star:        |                |
| approver_id         | int       |                    |      | 审核人       | :star: :star:        |                |
| approve_time        | timestamp |                    |      | 审核时间     | :star: :star:        |                |
| repay_way           | text      | :heavy_check_mark: |      | 利息结算方式 | :star: :star: :star: | EPI等额本息，IOP先息后本  |
| repay_period        | text      | :heavy_check_mark: |      | 还款频率     | :star: :star: :star: | M按月还款，Y按年还款，Q按季度还款，SY按半年还款 |
| installment_count   | int       | :heavy_check_mark: |      | 总还款月数   | :star: :star: :star: |                |
| interest_rate_month | decimal   | :heavy_check_mark: |      | 月利率%      | :star: :star: :star: |                |
| interest_rate_year  | decimal   | :heavy_check_mark: |      | 年利率%      | :star: :star: :star: |                |
| money_inout_flag    | int       | :heavy_check_mark: |      | 金额进出标记 | :star: :star:        | 1/-1           |
| total_amount        | decimal   | :heavy_check_mark: |      | 贷款金额     | :star: :star: :star: |                |
| payway1_id          | int       |                    |      | 支付方式1    | :star: :star:        |                |
| payway1_amount      | decimal   |                    |      | 支付方式1金额| :star: :star:        |                |
| payway2_id          | int       |                    |      | 支付方式2    | :star: :star:        |                |
| payway2_amount      | decimal   |                    |      | 支付方式2金额| :star: :star:        |                |
| now_pay_amount      | decimal   | :heavy_check_mark: |      | 本单支付金额 | :star: :star: :star: |                |
| paid_amount         | decimal   |                    |      | 整单已还金额 | :star: :star: :star: | 由还贷款单回写 |
| paid_principal_total| decimal   |                    |      | 整单已还本金 | :star: :star: :star: | 由还贷款单回写 |
| paid_interest_total | decimal   |                    |      | 整单已还本金 | :star: :star: :star: | 由还贷款单回写 |
<!--其他字段暂不需要-->

##### o.2 贷款计划 sheet_loan_repay_plan

| 列名                    | 类型      | 不为空             | 键   | 说明         |   重要性                | 值 |
| ----------------------- | --------- | ------------------ | ---- | ------------ | ------------------ | --- | 
| company_id              | int       | :heavy_check_mark: |      | 公司号       | :star: :star: :star:  | |
| sheet_id                | int       | :heavy_check_mark: |      | 贷款单单号  loan_sheet_id       | :star: :star: :star: | |
| red_flag                | int       |                    |      | 红冲标记         | :star: :star: :star: | null/1 |
| repay_way               | text      |                    |      | 利息结算方式   | :star: :star: | EPI等额本息，IOP先息后本 |
| repay_period            | text      |                    |      | 还款频率 | :star: :star:   | M按月还款，Y按年还款，Q按季度还款，SY按半年还款 |
| installment_count       | int       | :heavy_check_mark: |      | 总还款次数 | :star: :star: :star:      | （不是月份，是次数） |
| installment_no          | int       | :heavy_check_mark: |      | 第几期 | :star: :star: :star:      | |
| period_range            | text      | :heavy_check_mark: |      | 当前还款期间 | :star: :star: :star: | 2025-01-01 13:20:55 ~ 2025-02-01 13:20:54 |
| repay_date              | timestamp | :heavy_check_mark: |      | 本期还款日 | :star: :star: :star: | 2025-02-01 13:20:54 |
| period_total_to_pay     | double    | :heavy_check_mark: |      | 本期剩余待还金额（本金+利息）(记录当时) | :star: :star: :star:      | |
| period_principal_to_pay | double    | :heavy_check_mark: |      | 本期剩余待还本金 (记录当时) | :star: :star: :star:      | |
| period_interest_to_pay  | double    | :heavy_check_mark: |      | 本期剩余待还利息 (记录当时) | :star: :star: :star:      | |
| paid_amount             | double    |                    |      | 本期已还总计 | :star: :star: :star:      | 本期还款回写在plan表，贷款单总还款记在贷款单paid_amount |
| principal_paid          | double    |                    |      | 本期已还本金 | :star: :star: :star:      | 本期剩余待还=principal_due-principal_paid |
| interest_paid           | double    |                    |      | 本期已还利息 | :star: :star: :star:      | |
| status                  | text      | :heavy_check_mark: |      | 还款状态   | :star: :star: | no未还，part部分还款，all本期还清 |
<!--其他字段暂不需要-->

##### o.3 还贷款单 sheet_repay

| 列名                | 类型 | 不为空             | 键   | 说明         | 重要性             |
| ------------------- | ---- | ------------------ | ---- | ------------ | ------------------ |
| company_id          | int  | :heavy_check_mark: |      | 公司号       | :star: :star: :star: |
| sheet_id            | int  | :heavy_check_mark: |      | 单号         | :star: :star: :star: |
| sheet_no            | int  | :heavy_check_mark: |      | 单号         | :star: :star: :star: |  |
| sheet_type          | int  | :heavy_check_mark: |      | 单据类型         | :star: :star: :star: |  HDK |
| loan_sheet_id       | int  | :heavy_check_mark: |      | 贷款单单号         | :star: :star: :star: |
| red_flag            | int  | :heavy_check_mark: |      | 红冲标记         | :star: :star:  |  |
| red_sheet_id        | int  |  |      | 红字单单号         | :star: :star: |  |
| partner_id          | int  | :heavy_check_mark: |      | 借贷款单位         | :star: :star: :star: |  |
| getter_id           | int  |  |      | 业务员         | :star: :star: |  |
| loan_partner_id     | int  | :heavy_check_mark: |      | 账户         | :star: :star: :star: |  |
| money_inout_flag    | int  | :heavy_check_mark: |      | 金额进出标记         | :star: :star: | 1/-1  |
| happen_time         | timestamp  | :heavy_check_mark: |      | 交易时间         | :star: :star:  |   |
| maker_id            | int  | :heavy_check_mark: |      | 制单人         | :star: :star: |   |
| make_time           | timestamp  | :heavy_check_mark: |      | 制单时间         | :star: :star:  |   |
| approver_id         | int  |  |      | 审核人         | :star: :star: |   |
| approve_time        | timestamp  |  |      | 审核时间         | :star: :star:  |   |
| installment_no      | int | :heavy_check_mark: |      | 第几期 | :star: :star: :star:      | |
| total_amount        | decimal  | :heavy_check_mark: |      | 还款金额         | :star: :star: :star: |  |
| now_pay_amount      | decimal  | :heavy_check_mark: |      | 本单支付金额         | :star: :star: :star: | =total_amount |
| pay_principal_due   | decimal  | :heavy_check_mark: |      | 本次还本金         | :star: :star: :star: |  |
| pay_interest_due    | decimal  | :heavy_check_mark: |      | 本次还利息         | :star: :star: :star: |  |
| payway1_id          | int  |  |  | 支付方式1    | :star::star::star: |  |
| payway1_amount      | float  |  |      | 支付金额1    | :star::star::star: |  |
| payway2_id          | int  |  |      | 支付方式2    | :star::star:       |  |
| payway2_amount      | float  |  |      | 支付金额2    | :star::star:       |  |
<!--其他字段暂不需要-->

### 二、主要业务报表

#### 1.相关业务场景

1. 库存
2. 借货商品数量
3. 定货会商品余量
4. 定货会调整记录
5. 加权价质疑日志
6. 应收款余额
7. 预收款账户余额
8. 客户往来账
9. 最近售价

#### 2.报表表格

##### a.1 库存表 stock 

| 列名               | 类型    | 不为空             | 键   | 说明             | 重要性             |
| ------------------ | ------- | ------------------ | ---- | ---------------- | ------------------ |
| company_id         | int     | :heavy_check_mark: |      | 公司号           | :star::star::star: |
| stock_flow         | int     | :heavy_check_mark: |      | 流水号           |                    |
| branch_id          | int     |                    |      | 仓库号           | :star::star::star: |
| item_id            | int     |                    |      | 商品号           | :star::star::star: |
| branch_position    | int     |                    |      | 仓库位置         |                    |
| batch_id           | int     |                    |      |                  |                    |
| size_id            | int     |                    |      | 尺寸             |                    |
| color_id           | int     |                    |      | 颜色             |                    |
| stock_qty          | numeric |                    |      | 库存             | :star::star::star: |
| sell_pend_qty      | numeric |                    |      | 销售订单占用库存 | :star::star:       |
| sell_re_pend_qty   | numeric |                    |      |                  |                    |
| buy_pend_qty       | numeric |                    |      |                  |                    |
| buy_re_pend_qty    | numeric |                    |      |                  |                    |
| move_out_pend_qty  | numeric |                    |      |                  |                    |
| move_in_pend_qty   | numeric |                    |      |                  |                    |
| cost_price         | numeric |                    |      | 成本价           |                    |
| cost_amt           | numeric |                    |      | 货值             |                    |
| order_qty          | numeric |                    |      | 定货数           |                    |
| allow_negative_qty | numeric |                    |      | 允许负库存       | :star::star::star: |
| negative_till      | text    |                    |      |                  |                    |
| negative_oper      | text    |                    |      |                  |                    |

##### b 借货商品数量 borrow_cust_items 

| 列名         | 类型    | 不为空             | 键                 | 说明     | 重要性             |
| ------------ | ------- | ------------------ | ------------------ | -------- | ------------------ |
| company_id   | int     | :heavy_check_mark: | :heavy_check_mark: | 公司号   | :star::star::star: |
| cust_id      | int     | :heavy_check_mark: | :heavy_check_mark: | 客户号   | :star::star::star: |
| item_id      | int     | :heavy_check_mark: | :heavy_check_mark: | 商品号   | :star::star::star: |
| borrowed_qty | numeric |                    |                    | 借货数量 | :star::star::star: |

##### c.1 定货会商品余量 items_ordered_balance

| 列名          | 类型    | 不为空             | 键                 | 说明       | 重要性             |
| ------------- | ------- | ------------------ | ------------------ | ---------- | ------------------ |
| company_id    | int     | :heavy_check_mark: | :heavy_check_mark: | 公司号     | :star::star::star: |
| supcust_id    | int     | :heavy_check_mark: | :heavy_check_mark: | 客户       | :star::star::star: |
| item_id       | int     | :heavy_check_mark: | :heavy_check_mark: | 商品       | :star::star::star: |
| unit_no       | text    | :heavy_check_mark: | :heavy_check_mark: | 单位(大)   | :star::star::star: |
| unit_factor   | float   |                    |                    | 包装率     | :star::star::star: |
| quantity      | numeric |                    |                    | 数量       | :star::star::star: |
| order_price   | numeric | :heavy_check_mark: | :heavy_check_mark: | 定货会价格 | :star::star::star: |
| balance       | numeric |                    |                    | 定货会余额 | :star::star:       |
| prepay_sub_id | int     | :heavy_check_mark: | :heavy_check_mark: | 定货会账户 | :star::star::star: |

##### c.2 定货会调整记录 items_ordered_change

| 列名           | 类型      | 不为空             | 键                 | 说明             | 重要性             |
| -------------- | --------- | ------------------ | ------------------ | ---------------- | ------------------ |
| company_id     | int       | :heavy_check_mark: |                    | 公司号           | :star::star::star: |
| flow_id        | int       | :heavy_check_mark: | :heavy_check_mark: | 流水号           | :star::star::star: |
| sheet_id       | int       |                    |                    | 单号             | :star::star::star: |
| prepay_sub_id  | int       |                    |                    | 定货会账户       | :star::star::star: |
| supcust_id     | int       |                    |                    | 客户             | :star::star::star: |
| item_id        | int       |                    |                    | 商品             | :star::star::star: |
| unit_no        | text      |                    |                    | 单位             | :star::star::star: |
| old_quantity   | numeric   |                    |                    | 开单前数量       | :star::star:       |
| now_quantity   | numeric   |                    |                    | 开单后数量       | :star::star:       |
| real_price     | numeric   |                    |                    | 定货金额         | :star:             |
| old_sub_amount | numeric   |                    |                    | 开单前小计       | :star:             |
| now_sub_amount | numeric   |                    |                    | 开单后小计       | :star:             |
| happen_time    | timestamp |                    |                    | 交易日期         | :star:             |
| oper_type      | text      |                    |                    | 调整类型(增删改) | :star::star:       |
| red_flag       | int       |                    |                    | 红冲             | :star:             |

##### d 加权价质疑日志 cost_price_log

| 列名              | 类型      | 不为空             | 键   | 说明         | 重要性             |
| ----------------- | --------- | ------------------ | ---- | ------------ | ------------------ |
| company_id        | int       | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| sheet_id          | int       | :heavy_check_mark: |      | 单号         | :star::star::star: |
| item_id           | int       | :heavy_check_mark: |      | 商品号       | :star::star::star: |
| happen_time       | timestamp | :heavy_check_mark: |      | 交易日期     | :star::star::star: |
| suspect_status    | text      |                    |      | 质疑状态     | :star::star::star: |
| sheet_type        | text      | :heavy_check_mark: |      | 单据类型     | :star::star::star: |
| infected_sheet_id | int       | :heavy_check_mark: |      | 提出质疑单据 | :star:             |

##### e 应收款余额 arrears_balance

| 列名        | 类型      | 不为空             | 键                 | 说明     | 重要性             |
| ----------- | --------- | ------------------ | ------------------ | -------- | ------------------ |
| company_id  | int       | :heavy_check_mark: | :heavy_check_mark: | 公司号   | :star::star::star: |
| supcust_id  | int       | :heavy_check_mark: | :heavy_check_mark: | 客户号   | :star::star::star: |
| balance     | float     |                    |                    | 应收余额 | :star::star::star: |
| happen_time | timestamp |                    |                    | 欠款时间 | :star::star::star: |

##### f 预收款余额 prepay_balance

| 列名       | 类型  | 不为空             | 键                 | 说明       | 重要性             |
| ---------- | ----- | ------------------ | ------------------ | ---------- | ------------------ |
| company_id | int   | :heavy_check_mark: | :heavy_check_mark: | 公司号     | :star::star::star: |
| supcust_id | int   | :heavy_check_mark: | :heavy_check_mark: | 客户号     | :star::star::star: |
| sub_id     | int   | :heavy_check_mark: | :heavy_check_mark: | 预收款账户 | :star::star::star: |
| balance    | float |                    |                    | 预收余额   | :star::star::star: |

##### g 往来账 client_account_history

| 列名          | 类型      | 不为空             | 键   | 说明     | 重要性             |
| ------------- | --------- | ------------------ | ---- | -------- | ------------------ |
| company_id    | int       | :heavy_check_mark: |      | 公司号   | :star::star::star: |
| flow_id       | int       | :heavy_check_mark: |      | 流水号   | :star::star::star: |
| supcust_id    | int       |                    |      | 客户     | :star::star::star: |
| happen_time   | timestamp |                    |      | 交易日期 | :star::star::star: |
| sheet_type    | text      |                    |      | 单号类型 | :star::star::star: |
| sub_type      | text      |                    |      | 账户类型 | :star::star::star: |
| sheet_id      |           |                    |      | 单据号   | :star::star::star: |
| sub_id        | int       |                    |      | 账户     | :star::star::star: |
| change_amount | float     |                    |      | 金额变化 | :star::star::star: |
| now_balance   | float     |                    |      | 当前余额 | :star::star::star: |
| red_flag      | int       |                    |      | 红冲状态 | :star:             |

##### h 最近售价 client_recent_price

| 列名              | 类型      | 不为空             | 键   | 说明               | 重要性             |
| ----------------- | --------- | ------------------ | ---- | ------------------ | ------------------ |
| company_id        | int       | :heavy_check_mark: |      | 公司号             | :star::star::star: |
| supcust_id        | int       | :heavy_check_mark: |      | 客户               | :star::star::star: |
| item_id           | int       | :heavy_check_mark: |      | 商品               | :star::star::star: |
| unit_no           | text      | :heavy_check_mark: |      | 单位               | :star::star::star: |
| recent_price      | numeric   |                    |      | 最近售价           | :star::star::star: |
| happen_time       | timestamp |                    |      | 发生日期           | :star:             |
| recent_orig_price | numeric   |                    |      | 最近原价           | :star::star::star: |
| is_recent         | bool      |                    |      | 是否是最近一次售价 | :star:             |

### 三、财务相关

#### 1.凭证相关

##### a 凭证主表 cw_voucher_main

| 列名            | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| --------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id      | int       | :heavy_check_mark: |                    | 公司号            | :star::star::star: |
| sheet_id        | int       | :heavy_check_mark: | :heavy_check_mark: | 单号              | :star::star::star: |
| period          | timestamp | :heavy_check_mark: |                    | 所在月            | :star:             |
| sheet_no        | int       | :heavy_check_mark: |                    | 单号              | :star::star::star: |
| make_time       | timestamp | :heavy_check_mark: |                    | 制单时间          | :star:             |
| maker_id        | int       | :heavy_check_mark: |                    | 制单人            | :star:             |
| approve_time    | timestamp |                    |                    | 审核时间          | :star:             |
| approver_id     | int       |                    |                    | 审核人            | :star:             |
| happen_time     | timestamp | :heavy_check_mark: |                    | 交易时间          | :star::star::star: |
| make_brief      | text      |                    |                    | 备注              | :star:             |
| red_flag        | text      |                    |                    | 红冲标记          | :star:             |
| red_sheet_id    | int       |                    |                    | 红冲原单号        | :star:             |
| sheet_attribute | text      |                    |                    | 其它信息          | :star:             |

##### b 凭证详表 cw_voucher_detail

| 列名            | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| --------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id      | int       | :heavy_check_mark: |                    | 公司号            | :star::star::star: |
| sheet_id        | int       | :heavy_check_mark: | :heavy_check_mark: | 单号              | :star::star::star: |
| row_index       | int       | :heavy_check_mark: | :heavy_check_mark: | 行号              | :star::star::star: |
| sub_id          | int       | :heavy_check_mark: |                    | 科目id            | :star::star::star: |
| happen_time     | timestamp | :heavy_check_mark: |                    | 交易时间          | :star::star::star: |
| remark          | text      | :heavy_check_mark: |                    | 摘要              | :star::star::star: |
| debit_amount    | decimal   |                    |                    | 借方金额          | :star::star::star: |
| credit_amount   | decimal   |                    |                    | 贷方金额          | :star::star::star: |
| assister1_type  | text      |                    |                    | 辅助项1类型       | :star:             |
| assister1_id    | int       |                    |                    | 辅助项1id         | :star:             |
| assister2_type  | text      |                    |                    | 辅助项2类型       | :star:             |
| assister2_id    | int       |                    |                    | 辅助项2id         | :star:             |

##### c 科目余额表 cw_sub_balance

| 列名                | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| ------------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id          | int       | :heavy_check_mark: | :heavy_check_mark: | 公司号            | :star::star::star: |
| period              | timestamp | :heavy_check_mark: | :heavy_check_mark: | 财务月            | :star::star::star: |
| sub_id              | int       | :heavy_check_mark: | :heavy_check_mark: | 科目id            | :star::star::star: |
| month_start_balance | decimal   |                    |                    | 月初余额          | :star::star::star: |
| year_start_balance  | decimal   |                    |                    | 年初余额          | :star::star::star: |
| balance             | decimal   |                    |                    | 本期发生额        | :star::star::star: |
| debit_amount        | decimal   |                    |                    | 本期借方发生额    | :star::star::star: |
| credit_amount       | decimal   |                    |                    | 本期贷方发生额    | :star::star::star: |

* 本期余额=month_start_balance+balance
* balance=direction*(debit_amount-credit_amount)  (direction来自cw_subject表)

##### d 辅助科目余额表 cw_sub_balance_assister

| 列名                | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| ------------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id          | int       | :heavy_check_mark: | :heavy_check_mark: | 公司号            | :star::star::star: |
| period              | timestamp | :heavy_check_mark: | :heavy_check_mark: | 财务月            | :star::star::star: |
| sub_id              | int       | :heavy_check_mark: | :heavy_check_mark: | 科目id            | :star::star::star: |
| month_start_balance | decimal   |                    |                    | 月初余额          | :star::star::star: |
| year_start_balance  | decimal   |                    |                    | 年初余额          | :star::star::star: |
| balance             | decimal   |                    |                    | 本期发生额        | :star::star::star: |
| debit_amount        | decimal   |                    |                    | 本期借方发生额    | :star::star::star: |
| credit_amount       | decimal   |                    |                    | 本期贷方发生额    | :star::star::star: |
| assister_type       | text      |                    |                    | 辅助项类型        | :star::star::star: |
| assister_id         | int       |                    |                    | 辅助项id          | :star::star::star: |

##### e 凭证号表 cw_voucher_no

| 列名                | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| ------------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id          | int       | :heavy_check_mark: | :heavy_check_mark: | 公司号            | :star::star::star: |
| period              | timestamp | :heavy_check_mark: | :heavy_check_mark: | 财务月            | :star::star::star: |
| next_voucher_no     | int       | :heavy_check_mark: |                    | 本月下一凭证号    | :star::star::star: |

##### f 凭证单据关联表 cw_voucher_sheet_mapper

| 列名                | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| ------------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id          | int       | :heavy_check_mark: | :heavy_check_mark: | 公司号            | :star::star::star: |
| voucher_id          | int       | :heavy_check_mark: | :heavy_check_mark: | 凭证sheet_id      | :star::star::star: |
| business_sheet_type | text      | :heavy_check_mark: | :heavy_check_mark: | 单据类型          | :star::star::star: |
| business_sheet_id   | int       | :heavy_check_mark: | :heavy_check_mark: | 单据sheet_id      | :star::star::star: |

##### g 科目期初主表 cw_op_sub_init_main

| 列名                | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| ------------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id          | int       | :heavy_check_mark: | :heavy_check_mark: | 公司号            | :star::star::star: |
| sheet_id            | int       | :heavy_check_mark: | :heavy_check_mark: | 表格号            | :star::star::star: |
| happen_time         | timestamp |                    |                    | 创建时间          |                    |
| period              | timestamp |                    |                    | 财务月            |                    |

##### h 科目期初详表 cw_op_sub_init_detail

| 列名                | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| ------------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id          | int       | :heavy_check_mark: | :heavy_check_mark: | 公司号            | :star::star::star: |
| sheet_id            | int       | :heavy_check_mark: | :heavy_check_mark: | 表格号            | :star::star::star: |
| sub_id              | int       | :heavy_check_mark: | :heavy_check_mark: | 科目              | :star::star::star: |
| year_start_balance  | decimal   |                    |                    | 年初余额（未使用）|                    |
| balance             | decimal   |                    |                    | 期初余额          | :star::star::star: |
| remark              | text      |                    |                    | 备注（未使用）    |                    |

##### i 科目期初辅助主表 cw_op_sub_init_assister_main

| 列名                | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| ------------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id          | int       | :heavy_check_mark: | :heavy_check_mark: | 公司号            | :star::star::star: |
| sheet_id            | int       | :heavy_check_mark: | :heavy_check_mark: | 表格号            | :star::star::star: |
| make_time           | timestamp |                    |                    | 创建时间          |                    |
| period              | timestamp |                    |                    | 财务月            |                    |

##### j 科目期初辅助详表 cw_op_sub_init_assister_detail

| 列名                | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| ------------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id          | int       | :heavy_check_mark: | :heavy_check_mark: | 公司号            | :star::star::star: |
| sheet_id            | int       | :heavy_check_mark: | :heavy_check_mark: | 表格号            | :star::star::star: |
| row_index           | int       | :heavy_check_mark: | :heavy_check_mark: | 行号              | :star::star::star: |
| period              | timestamp |                    |                    | 财务月            |                    |
| make_time           | timestamp |                    |                    | 创建时间          |                    |
| sub_id              | int       | :heavy_check_mark: | :heavy_check_mark: | 科目              | :star::star::star: |
| assister1_type      | text      |                    |                    | 辅助项1类型       | :star::star::star: |
| assister1_id        | int       |                    |                    | 辅助项1id         | :star::star::star: |
| assister2_type      | text      |                    |                    | 辅助项2类型       | :star::star::star: |
| assister2_id        | int       |                    |                    | 辅助项2id         | :star::star::star: |
| balance             | decimal   |                    |                    | 期初余额          | :star::star::star: |
| remark              | text      |                    |                    | 备注（未使用）    |                    |

##### k 财务结账记录 cw_op_monthly_closing

| 列名                | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| ------------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id          | int       | :heavy_check_mark: | :heavy_check_mark: | 公司号            | :star::star::star: |
| sheet_id            | int       | :heavy_check_mark: | :heavy_check_mark: | 表格号            | :star::star::star: |
| period              | timestamp | :heavy_check_mark: |                    | 财务月            | :star::star::star: |
| happen_time         | timestamp |                    |                    | 结账时间          | :star:             |
| red_flag            | text      |                    |                    | 反结账标记        | :star:             |

##### l 财务log cw_log

| 列名                | 类型      | 不为空             | 键                 | 说明              | 重要性             |
| ------------------- | --------- | ------------------ | ------------------ | ----------------- | ------------------ |
| company_id          | int       | :heavy_check_mark: | :heavy_check_mark: | 公司号            | :star::star::star: |
| flow_id             | int       | :heavy_check_mark: | :heavy_check_mark: | 记录号            | :star::star::star: |
| oper_id             | int       | :heavy_check_mark: |                    | 操作人            | :star::star::star: |
| happen_time         | timestamp |                    |                    | 结账时间          | :star:             |
| sheet_id            | int       |                    |                    | 凭证sheet_id      | :star:             |
| sheet_type          | text      |                    |                    | 操作类型          | :star::star::star: |
| log                 | text      |                    |                    | log info          | :star::star::star: |

### 四、其他

#### 1. 相关业务场景

1. 单据号生成
2. 价格方案、价格策略
3. 打印相关

#### 2. 表格

1. ##### 生成单号sheet_no

| 列名       | 类型      | 不为空 | 键                 | 说明           | 重要性             |
| ---------- | --------- | ------ | ------------------ | -------------- | ------------------ |
| company_id | int       |        | :heavy_check_mark: | 公司号         | :star::star::star: |
| sheet_day  | timestamp |        | :heavy_check_mark: | 开单时间       | :star::star::star: |
| oper_id    | Int       |        | :heavy_check_mark: | 操作员         | :star::star::star: |
| next_id    | int       |        | :heavy_check_mark: | 下一张单子单号 | :star::star::star: |
| sheet_type | text      |        | :heavy_check_mark: | 单据类型       | :star::star::star: |

##### 2.a 价格方案主表 price_plan_main

| 列名        | 类型      | 不为空             | 键                 | 说明     | 重要性             |
| ----------- | --------- | ------------------ | ------------------ | -------- | ------------------ |
| company_id  | int       | :heavy_check_mark: |                    | 公司号   | :star::star::star: |
| plan_id     | int       | :heavy_check_mark: | :heavy_check_mark: | 方案号   | :star::star::star: |
| plan_name   | text      |                    |                    | 方案名称 | :star::star::star: |
| create_time | timestamp |                    |                    | 创建时间 | :star:             |
| update_time | timestamp |                    |                    | 修改时间 | :star:             |

##### 2.b 价格方案设置商品 price_plan_item

| 列名       | 类型    | 不为空             | 键   | 说明         | 重要性             |
| ---------- | ------- | ------------------ | ---- | ------------ | ------------------ |
| company_id | int     | :heavy_check_mark: |      | 公司号       | :star::star::star: |
| plan_id    | int     | :heavy_check_mark: |      | 方案号       | :star::star::star: |
| item_id    | int     | :heavy_check_mark: |      | 商品号       | :star::star::star: |
| s_price    | numeric |                    |      | 小单位价格   | :star::star::star: |
| m_price    | numeric |                    |      | 中单位价格   | :star::star::star: |
| b_price    | numeric |                    |      | 大单位价格   | :star::star::star: |
| discount   | numeric |                    |      | 相对进价折扣 | :star:             |

##### 2.c 价格方案设置类别 price_plan_class

| 列名       | 类型    | 不为空             | 键   | 说明   | 重要性             |
| ---------- | ------- | ------------------ | ---- | ------ | ------------------ |
| company_id | int     | :heavy_check_mark: |      | 公司号 | :star::star::star: |
| plan_id    | int     | :heavy_check_mark: |      | 方案号 | :star::star::star: |
| class_id   | int     | :heavy_check_mark: |      | 类别号 | :star::star::star: |
| discount   | numeric |                    |      | 折扣   | :star::star:       |

##### 2.d价格策略设置客户类别 price_strategy_class

| 列名       | 类型 | 不为空             | 键   | 说明   | 重要性             |
| ---------- | ---- | ------------------ | ---- | ------ | ------------------ |
| company_id | int  | :heavy_check_mark: |      | 公司号 | :star::star::star: |
| flow_id    | int  | :heavy_check_mark: |      | 流水号 | :star::star:       |
| group_id   | int  |                    |      | 渠道   | :star::star:       |
| region_id  | int  |                    |      | 片区   | :star::star:       |
| rank_id    | int  |                    |      | 等级   | :star::star:       |
| price1     | text |                    |      | 方案1  | :star::star::star: |
| price2     | text |                    |      | 方案2  | :star::star::star: |
| price3     | text |                    |      | 方案3  | :star::star::star: |
| price4     | text |                    |      | 方案4  |                    |
| price5     | text |                    |      | 方案5  |                    |

##### 2.e 价格策略设置客户 price_strategy_client

| 列名       | 类型 | 不为空             | 键   | 说明   | 重要性             |
| ---------- | ---- | ------------------ | ---- | ------ | ------------------ |
| company_id | int  | :heavy_check_mark: |      | 公司号 | :star::star::star: |
| flow_id    | int  | :heavy_check_mark: |      | 流水号 | :star::star::star: |
| supcust_id | int  | :heavy_check_mark: |      | 客户   | :star::star::star: |
| price1     | text |                    |      | 方案1  | :star::star::star: |
| price2     | text |                    |      | 方案2  | :star::star::star: |
| price3     | text |                    |      | 方案3  | :star::star::star: |
| price4     | text |                    |      | 方案4  |                    |
| price5     | text |                    |      | 方案5  |                    |



