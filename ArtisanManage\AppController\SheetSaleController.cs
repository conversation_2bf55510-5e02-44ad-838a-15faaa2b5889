﻿using ArtisanManage.Models;
using ArtisanManage.Pages.Sheets;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    /// <summary>
    /// 销售单
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class SheetSaleController : BaseController
    { 
        public SheetSaleController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        /// <summary>
        /// 销售单列表
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="getTotal"></param>
        /// <param name="supcust_id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetSheetList(string operKey, int pageSize, int startRow, bool getTotal,string supcust_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where company_id = {companyID}";
            if (supcust_id != null) condi += $"and supcust_id = {supcust_id}";
            SQLQueue QQ = new SQLQueue(cmd);
             var sql = @$"select sheet_id,sheet_no,happen_time from sheet_sale_main {condi}
                limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            { 
                sql = $"select count(sheet_id) as total from sheet_sale_main {condi}";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total"); 
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total });
        }
    
        /// <summary>
        /// 销售单商品列表
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="sheet_id"></param>
        /// <param name="branch_id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetSheetRows(string operKey, string sheet_id, string branch_id )
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"company_id = {companyID} and sheet_id = {sheet_id}";
            if (!String.IsNullOrEmpty(branch_id)) condi += $"and branch_id={branch_id}";
            var sql = @$"select item_id,item_name,unit_no,unit_factor,quantity,sub_amount,orig_price,real_price
                     from sheet_sale_detail where {condi};"; 
            
            var data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";  
            return Json(new { result,msg, data, total = data.Count, });
        }

    }
}
