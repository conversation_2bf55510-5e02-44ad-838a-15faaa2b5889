using ArtisanManage.Models;
using ArtisanManage.Services;
using MathNet.Numerics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.DotNet.MSIdentity.Shared;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.Formula.Functions;
using NuGet.Protocol;
using Quartz.Impl.Triggers;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class OperatorEditModel : PageFormModel
    {
        // private readonly CMySbCommand cmd;
        class BranchGrid : FormDataGrid
        {
            public override void OnGridDataGot(List<Dictionary<string, dynamic>> lstRows)
            {
                foreach (var row in lstRows)
                {
                    foreach (var col in this.Columns)
                    {
                        if (col.Key != "branch_name" && col.Key != "branch_name")
                        {
                            if (row.ContainsKey(col.Key))
                            {
                                string value = row[col.Key];
                                if (value == "t") row[col.Key] = "True";
                            }

                        }
                    }
                }
            }
        }
        public OperatorEditModel(CMySbCommand cmd = null) : base(MenuId.infoOperator)
        {
            this.cmd = cmd;

            DataItems = new Dictionary<string, DataItem>()
            {
                {"oper_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"oper_name",new DataItem(){Title="姓名",Necessary=true,FldArea="divHead"}},
                //{"oper_pw",new DataItem(){Title="密码",Necessary=true,FldArea="divHead"}},
                {"order_index",new DataItem(){Title="顺序号",FldArea="divHead"}},
                {"py_str",new DataItem(){Title="助记名",FldArea="divHead"}},
                {"mobile",new DataItem(){Title="手机",FldArea="divHead",Necessary=true}},
                {"depart_id",new DataItem(){Title="部门",Necessary=true,FldArea="divHead", LabelFld="depart_name",CtrlType="jqxDropDownTree",TreePathFld="depart_path",MumSelectable=true,
                   SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"} },
                {"role_id",new DataItem(){Title="角色",FldArea="divHead",SqlFld="info_operator.role_id", LabelFld="role_name",ButtonUsage="list",
                   SqlForOptions="select role_id as v,role_name as l from info_role"} },
                {"status",new DataItem(){Title="状态",LabelFld="cls_status_name",FldArea="divHead",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}},
                  {"seller_max_arrears",new DataItem(){Title="欠款额度",FldArea="divHead",CtrlType="hidden"}},//迁移到欠款策略了
                {"visit_schedule_id",new DataItem(){Title="拜访行程",LabelFld="schedule_name", FldArea="divHead",CtrlType="jqxInput",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",Hidden=true,HideOnLoad = true,
                    SqlForOptions=$"select schedule_id as v,schedule_name as l from info_visit_schedule where COALESCE(status,'1')='1' and company_id = ~COMPANY_ID order by order_index "
                }},
                {"entry_date",new DataItem(){Title="入职日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ShowTime=false}},
                {"identity_card",new DataItem(){Title="身份证号",FldArea="divHead"}},
				//{"trial_till_date",new DataItem(){Title="使用期限",CtrlType="hidden", FldArea="divHead"}},
				{"avail_brands_id",new DataItem(){FldArea="divHead",Title="负责品牌",DropDownWidth="80", LabelFld="avail_brands_name",PlaceHolder="全部", LabelInFormTable=true, Checkboxes=true,ButtonUsage="list",SqlForOptions=CommonTool.selectBrands}},
                {"is_seller",new DataItem(){FldArea="divHead",Title="业务员",CtrlType="jqxCheckBox"}},
                {"is_sender",new DataItem(){FldArea="divHead",Title="送货员",CtrlType="jqxCheckBox"}},
                {"oper_regions",new DataItem(){Title="负责区域", CtrlType="hidden", FldArea="divHead"}},
                {"can_login",new DataItem(){Title="可以登录",CtrlType="jqxCheckBox",FldArea="divHead",Value="True"}},
                {"restrict_branches",new DataItem(){Title="限制使用仓库",CtrlType="jqxCheckBox",FldArea="rightboxTop"}},
                { "avail_pay_ways",new DataItem(){Title="可用支付方式", CtrlType="hidden", FldArea="divHead" }},
                { "restrict_pay_ways",new DataItem(){Title="限制使用支付方式",CtrlType="jqxCheckBox",FldArea="paywayboxTop" }}
            
            };
            m_idFld = "oper_id"; m_nameFld = "oper_name"; IdFldIsSerial = false;
            m_tableName = "info_operator";
            m_selectFromSQL = @"
from info_operator
left join info_role on info_operator.role_id=info_role.role_id and info_role.company_id=~COMPANY_ID
left join info_department on info_operator.depart_id= info_department.depart_id and info_department.company_id=~COMPANY_ID
left join 
(
   select oper_id as visit_oper,string_agg(schedule_name,',') schedule_name from info_operator o 
   left join info_visit_schedule s on s.schedule_id::text = any(string_to_array(o.visit_schedule_id::text,','))
   where o.company_id=~COMPANY_ID group by visit_oper
) s on s.visit_oper = info_operator.oper_id where oper_id='~ID' and info_operator.company_id=~COMPANY_ID";/*left join info_item_class  on info_item_prop.item_class=info_item_class.class_id left join info_item_brand on info_item_prop.item_brand=info_item_brand.brand_id*/

            var cellsRender = @"function (row, columnfield, value, defaulthtml, columnproperties) {
                        return  `<label index='${row}' field='${columnfield}'   role='checkbox' class='${value} cell'></label>`
            }
            ";
            var cellsRender_select_all = @"function cellsrenderer_quantity (row, columnfield, value, defaulthtml, columnproperties) {
                        return  `<input type='checkbox' index='${row}'  ${value?'checked':''}   class='selectrow' />`;
            }
            ";
            var cellbeginedit = @"function(row, datafield, columntype, value) {
                 return false;
            }";

            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"rightbox" ,new BranchGrid(){
                   MinRows=1,AutoAddRow=false,
                   Columns = new Dictionary<string, DataItem>()
                   {
                       //{"unit_no",new DataItem(){title="单位",width="100",url="../api/ItemEdit/GetUnits"}},
                       {"branch_name",new DataItem(){Title="仓库名称",Width="16%",SaveToDB=false, Necessary=true,SqlFld="info_branch.branch_name",
                           JSCellBeginEdit = cellbeginedit
                       }},
                       {"branch_id",new DataItem(){Title="仓库",Hidden=true, SqlFld="info_branch.branch_id"}},
                       {"select_all",new DataItem(){Title="",Width="4px", GetFromDb=false,SaveToDB=false,
                           JSCellRender=cellsRender_select_all,JSCellBeginEdit=cellbeginedit,JSHeaderRender = @"function cellsrenderer_quantity (row, columnfield, value, defaulthtml, columnproperties) {
                               return  `<input type='checkbox'  ${window.selectAll?'checked':''}   class='selectall' />`;
                            }"
                       }},
                       {"sheet_x",new DataItem(){Title="销售",Width="6%",
                           JSCellRender=cellsRender,JSCellBeginEdit=cellbeginedit
                       }},
                       {"sheet_t",new DataItem(){Title="退货",Width="6%",
                           JSCellRender=cellsRender,JSCellBeginEdit=cellbeginedit
                       }},
                       {"sheet_xd",new DataItem(){Title="销售订单",Width="10%",
                           JSCellRender=cellsRender,JSCellBeginEdit=cellbeginedit
                       }},
                       {"sheet_td",new DataItem(){Title="退货订单",Width="10%",
                           JSCellRender=cellsRender,JSCellBeginEdit=cellbeginedit
                       }},
                       {"sheet_dr",new DataItem(){Title = "调入",Width="6%", JSCellRender = cellsRender, JSCellBeginEdit = cellbeginedit}},
                       {"sheet_jh",new DataItem(){Title = "借货",Width="6%", JSCellRender = cellsRender, JSCellBeginEdit = cellbeginedit}},
                       {"sheet_hh",new DataItem(){Title = "还货",Width="6%", JSCellRender = cellsRender, JSCellBeginEdit = cellbeginedit}},
                       {"sheet_dc",new DataItem(){Title = "调出",Width="6%", JSCellRender = cellsRender, JSCellBeginEdit = cellbeginedit}},
                       {"sheet_cg",new DataItem(){Title = "采购",Width="6%", JSCellRender = cellsRender, JSCellBeginEdit = cellbeginedit}},
                       {"sheet_ct",new DataItem(){Title = "采购退货",Width="10%", JSCellRender = cellsRender, JSCellBeginEdit = cellbeginedit}},
                       {"sheet_pd",new DataItem(){Title = "盘点", Width="6%",JSCellRender = cellsRender, JSCellBeginEdit = cellbeginedit}},
                       //{"sheet_ss",new DataItem(){Title = "门店库存上报", Width="6%",JSCellRender = cellsRender, JSCellBeginEdit = cellbeginedit}},
                       {"query_stock",new DataItem(){Title = "库存查询",Width="10%", JSCellRender = cellsRender, JSCellBeginEdit = cellbeginedit}},
                   },
                   TableName="oper_branch_rights",
                   IdFld="oper_id",
                  // SelectFromSQL="from oper_branch_rights right join info_branch on oper_branch_rights.branch_id = info_branch.branch_id order by branch_id"
                   SelectFromSQL="select ~SQL_FLDS from  info_branch left join (select * from oper_branch_rights where oper_id=~ID) oper_branch_rights on info_branch.branch_id=oper_branch_rights.branch_id where info_branch.company_id = ~COMPANY_ID and (info_branch.status is null or info_branch.status=1)  order by order_index,  oper_branch_rights.branch_id"
                }}
            };
        }

        public Region Region { get; set; }
        public PayWayNew PayWayNew { get; set; }
        public async Task OnGet()
        {
            var regions = await cmd.QueryAsync<Region>(Token.CompanyID);
            regions.ForEach(x => x.IsOpen = x.SubNodes != null);
            Region = regions.ToTree1();
            string condition = @$"
sub_id in 
(
    WITH RECURSIVE sub_tree AS 
    ( 
       SELECT 1 recur_level,sub_id, mother_id FROM cw_subject cw WHERE sub_type IS NOT NULL AND company_id = {Token.CompanyID} and coalesce(status,'1')!='0' and sub_id<>mother_id
       UNION ALL 
       SELECT recur_level+1, s.sub_id, s.mother_id  FROM cw_subject s JOIN sub_tree st ON st.mother_id = s.sub_id where s.company_id={Token.CompanyID} and s.sub_id<>s.mother_id and recur_level<=5

    ) SELECT DISTINCT sub_id FROM sub_tree
)"; 
            var payways = await cmd.QueryAsync<PayWayNew>(Token.CompanyID,condition);
            payways.ForEach(x => x.IsOpen = x.SubNodes != null);
            PayWayNew = payways.ToTree1();

            await InitGet(cmd);
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            cmd.CommandText = $"SELECT ir.role_name FROM info_operator io left JOIN info_role ir ON io.role_id = ir.role_id and ir.company_id={Token.CompanyID} WHERE io.company_id={Token.CompanyID} and io.oper_id = {Token.OperID};";
            var isAdmin = await cmd.ExecuteScalarAsync();
            string role_id = DataItems["role_id"].Label;
            if (isAdmin.ToString() != "管理员" && role_id=="管理员")
                DataItems["role_id"].Disabled = true;
            string oper_id = DataItems["oper_id"].Value;
            var restrict_branches = DataItems["restrict_branches"].Value;
            var restrict_pay_ways = DataItems["restrict_pay_ways"].Value;
            if (oper_id != "")
            {
                if (restrict_branches == "") DataItems["restrict_branches"].Value = "True";
                if (restrict_pay_ways == "") DataItems["restrict_pay_ways"].Value = "False";
                string can_login = DataItems["can_login"].Value;
                if (can_login == "") DataItems["can_login"].Value = "True";
            }
        }
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class OperatorEditController : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public OperatorEditController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd;
            _httpClientFactory = httpClientFactory;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            OperatorEditModel model = new OperatorEditModel();
            if(dataItemName== "role_id")
            {
				cmd.CommandText = $"SELECT ir.role_name FROM info_operator io left JOIN info_role ir ON io.role_id = ir.role_id and ir.company_id={Token.CompanyID} WHERE io.company_id={Token.CompanyID} and io.oper_id = {Token.OperID};";
				var isAdmin = await cmd.ExecuteScalarAsync();
				string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
				if (isAdmin.ToString() != "管理员")
				{
					var a = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(data);
					// Filter out dictionaries with "l" value of "管理员"
					var filteredData = a.Where(d => d["l"] != "管理员").ToList();
					// Serialize the filtered data back to JSON string
					string filteredJsonData = JsonConvert.SerializeObject(filteredData, Formatting.Indented);
					return filteredJsonData;
				}
				else
					return data;
			}
            else
            {
				string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
				return data;
			}

		}
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            OperatorEditModel model = new OperatorEditModel();
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string can_login = data.can_login;
            string role_id = data.role_id;
            string oper_id = data.oper_id;
            string status = data.status;
            string mobile = data.mobile;
            string seller_max_arrears = data.seller_max_arrears;
            string is_seller = data.is_seller;
            string is_sender = data.is_sender;

            if (status == "") status = "1";
            if (mobile == "")
            {
                return new JsonResult(new { result = "Error", msg = "请输入手机号" });
            }

            if (can_login == "True" && role_id == "")
            {
                return new JsonResult(new { result = "Error", msg = "请指定角色" });
            }
            string operID = oper_id;
            if (operID == "") operID = "-1";
            if (role_id == "") role_id = "-1";
            // int left_user_count = 0, left_user_count_discount1 = 0, left_user_count_discount2 = 0;
            

            string sql = $@"
select o.role_id old_role_id,o.mobile old_mobile,go.trial_till_date, gc.user_count,user_count_discount1,fee_discount1,user_count_discount2,fee_discount2,used_fee_discount, oper.used_user_count,o.can_login,o.status,go.is_admin,t.fee_discount cur_oper_fee_discount,pre_t.fee_discount pre_oper_fee_discount 
from g_company gc

left join info_operator o on o.company_id={companyID} and o.oper_id='{operID}'
left join g_operator go on o.company_id={companyID} and go.oper_id='{operID}'
left join info_role r on r.company_id={companyID} and r.role_id={role_id}

left join info_role_template t on r.templ_id=t.templ_id and t.company_id={companyID}
left join info_role pre_role on pre_role.company_id={companyID} and pre_role.role_id=o.role_id
left join info_role_template pre_t on pre_role.templ_id=pre_t.templ_id and pre_t.company_id={companyID}


left join 
(
  select coalesce(fee_discount,1) used_fee_discount,count(*) used_user_count
  from info_operator o 
  left join info_role r on o.role_id=r.role_id and o.company_id=r.company_id
  left join info_role_template t on r.templ_id=t.templ_id and r.company_id=t.company_id where o.company_id={companyID} and COALESCE(can_login,true) and COALESCE(status,'1')='1' and coalesce(fee_discount,1)>0 group by coalesce(fee_discount,1)
) oper on coalesce(t.fee_discount,1)=oper.used_fee_discount

where gc.company_id={companyID};
";

            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            string pre_status = "";
            bool pre_can_login = true;
            float pre_oper_fee_discount = 1;
            float cur_oper_fee_discount = 1;
            float fee_discount1 = 1;
            float fee_discount2 = 1;
            int user_count = 0; int user_count_discount1 = 0; int user_count_discount2 = 0;
            int used_user_count_no_discount = 0, used_user_count_discount1 = 0, used_user_count_discount2 = 0;
            if (rec.pre_oper_fee_discount != "") pre_oper_fee_discount = Convert.ToSingle(rec.pre_oper_fee_discount);

            if (rec.fee_discount1 != "") fee_discount1 = Convert.ToSingle(rec.fee_discount1);
            if (rec.fee_discount2 != "") fee_discount2 = Convert.ToSingle(rec.fee_discount2);
            if (rec.cur_oper_fee_discount != "") cur_oper_fee_discount = Convert.ToSingle(rec.cur_oper_fee_discount);

            if (rec.user_count != "") user_count = Convert.ToInt32(rec.user_count);
            if (rec.user_count_discount1 != "") user_count_discount1 = Convert.ToInt32(rec.user_count_discount1);
            if (rec.user_count_discount2 != "") user_count_discount2 = Convert.ToInt32(rec.user_count_discount2);
                 

            int used_user_count = 0; if (rec.used_user_count != "") used_user_count = Convert.ToInt32(rec.used_user_count);
            float used_fee_discount = 1;
            if (rec.used_fee_discount != "") used_fee_discount = Convert.ToSingle(rec.used_fee_discount);

            if (used_fee_discount == 1)
            {
                used_user_count_no_discount = used_user_count;
                // left_user_count = user_count - used_user_count; 
            }
            else if (used_fee_discount == fee_discount1)
            {
                used_user_count_discount1 = used_user_count;
                //  left_user_count_discount1 = user_count_discount1 - used_user_count;
            }
            else if (used_fee_discount == fee_discount2)
            {
                used_user_count_discount2 = used_user_count;
                //   left_user_count_discount2 = user_count_discount2 - used_user_count;
            }
            pre_status = rec.status == "0" ? "0" : "1";
            pre_can_login = rec.can_login != "False";
            string msg = "";
            if (rec.is_admin == "True")
            {
                if (status == "0")
                {
                    msg = $"主账号不能停用";
                }
                else if (can_login == "False")
                {
                    msg = $"主账号必须可登录";
                }
                else if (role_id != rec.old_role_id)
                {
                    msg = "主账号不可以更改角色";
                }
                else if (mobile != rec.old_mobile)
                {
                    msg = "主账号不可以更改手机号码";
                }
            }
            if(msg!="")
                return Json(new { result = "FAIL", msg });
            /* if(can_login == "True")
             {
                 if(is_fee_count(role_id, companyID).Result>0 &&is_fee_count(rec.old_role_id, companyID).Result <=0)
                 {
                     used_user_count_no_discount++;
                 }
             }
             */
            /*
            if (rec.trial_till_date == "")
            {
				int leftCount = 0;
				if (cur_oper_fee_discount == 1)
				{
					leftCount = user_count - used_user_count_no_discount;
				}
				else if (cur_oper_fee_discount == fee_discount1)
				{
					leftCount = user_count_discount1 - used_user_count_discount1;
				}
				else if (cur_oper_fee_discount == fee_discount2)
				{
					leftCount = user_count_discount2 - used_user_count_discount2;
				}

				if (can_login == "True" && cur_oper_fee_discount > 0)
				{
					int nAddLoginUsers = 0;
					if (oper_id == "")
					{
						if (can_login == "True" && status == "1") nAddLoginUsers = 1;
					}
					else
					{
						if (can_login == "True" && status == "1" && (!pre_can_login || pre_status == "0" || pre_oper_fee_discount != cur_oper_fee_discount)) nAddLoginUsers = 1;
					}

					leftCount = leftCount - nAddLoginUsers;
					if (leftCount < 0)
					{

						if (cur_oper_fee_discount < 1)
						{
							msg = $"{cur_oper_fee_discount}比率计价的端口数不足,请联系客服";
						}
						else msg = $"端口数不足,请联系客服";
						return Json(new { result = "FAIL", msg });
					}
				}

			}*/


			async Task<string> checkUserCountValid()
            {
                if (status == "0") return "";
                sql = $@"
select io.oper_id,go.trial_till_date, rt.fee_discount,gc.user_count,gc.user_count_discount1,gc.user_count_discount1,gc.fee_discount1,gc.fee_discount2 
from info_operator io 
left join g_operator go on io.oper_id=go.oper_id and go.company_id={companyID} 
left join info_role ir on io.role_id=ir.role_id and ir.company_id={companyID}
left join info_role_template rt on ir.templ_id=rt.templ_id and rt.company_id={companyID}
left join g_company gc on io.company_id=gc.company_id
where io.company_id={companyID} and coalesce(io.can_login,true) and coalesce(io.status,'1')='1' and go.trial_till_date is null";
                var lstOper = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                Dictionary<float, int> dicDiscountOpers = new Dictionary<float, int>();

                // int ct = 0;
                foreach (dynamic oper in lstOper)
                {
                    string cur_oper_id = oper.oper_id;
                    string fee_discount = oper.fee_discount;
                    float disc = 1;
                    if (fee_discount != "") disc = Convert.ToSingle(fee_discount);


                    if (dicDiscountOpers.ContainsKey(disc))
                    {
                        dicDiscountOpers[disc] += 1;
                    }
                    else dicDiscountOpers.Add(disc, 1);

                }

                foreach (var kp in dicDiscountOpers)
                {
                    int discUserCount = kp.Value;
                    float discount = kp.Key;
                    if (discount == 1)
                    {
                        if (discUserCount > user_count)
                        {
                            msg = "端口数不够";
                            break;
                        }
                    }
                    else if (discount == fee_discount1)
                    {
                        if (discUserCount > user_count_discount1)
                        {
                            msg = "折扣1端口数不够";
                            break;
                        }
                    }
                    else if (discount == fee_discount2)
                    {
                        if (discUserCount > user_count_discount2)
                        {
                            msg = "折扣2端口数不够";
                            break;
                        }
                    }
                }

                 return msg;
            

            }

            CDbDealer db = new CDbDealer();


            db.AddFields(data, "oper_name,mobile,can_login");
            db.AddField("oper_status", (string)data.status);

            string new_oper_id = "";
            string pwd = "";
            JsonResult res;


            if (oper_id != null && oper_id != "")
            {
                string sqlUpdateSellerArrears = "";
                if (seller_max_arrears != null && seller_max_arrears != "")
                {
                    //更新老员工时检查老员工有没有使用过额度限制
                    string sqlArrears = $@"
select round(sum(left_amount)::numeric,2) sum_left_amount 
from 
(
    select sum(round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)*money_inout_flag) left_amount  from sheet_sale_main where company_id={companyID} and seller_id= {operID} and round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)<>0 and approve_time is not null union
    select sum(round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)*money_inout_flag) left_amount  from sheet_prepay where company_id={companyID}  and getter_id={operID} and round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)<>0  and approve_time is not null union
    select sum(round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)*money_inout_flag) left_amount from sheet_fee_out_main where company_id={companyID} and getter_id={operID} and round((COALESCE ( total_amount, 0 ) - COALESCE ( disc_amount, 0 ) - COALESCE ( paid_amount, 0 ))::numeric,2)<>0   and approve_time is not null
) t ";

                    dynamic balance = await CDbDealer.Get1RecordFromSQLAsync(sqlArrears, cmd);
                    double sum_left_amount = Convert.ToDouble(balance.sum_left_amount != "" ? balance.sum_left_amount : 0);

                    string earlierDate = CPubVars.GetDateText(DateTime.Now.AddDays(-90));
                    string sqlPendAmount = $@"
select round(sum(left_amount)::numeric,2) sum_left_amount 
from 
(
    select sum(round((COALESCE ( o.total_amount, 0 ) - COALESCE ( o.disc_amount, 0 ) - COALESCE ( o.paid_amount, 0 ))::numeric,2)*o.money_inout_flag) left_amount  
    from sheet_sale_order_main o
    left join sheet_sale_main s on o.sheet_id=s.order_sheet_id and s.company_id={companyID} 
    where o.company_id={companyID} and o.happen_time>'{earlierDate}' and s.happen_time>'{earlierDate}' and o.seller_id= {operID} and round((COALESCE ( o.total_amount, 0 ) - COALESCE ( o.disc_amount, 0 ) - COALESCE ( o.paid_amount, 0 ))::numeric,2)<>0  and o.approve_time is not null and s.approve_time is null
    
) t ";

                    dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sqlPendAmount, cmd);
                    double pendAmount = Convert.ToDouble(record.sum_left_amount != "" ? record.sum_left_amount : 0);
                    sqlUpdateSellerArrears = $"insert into arrears_balance_auxiliary(company_id,auxiliary_type,auxiliary_id,auxiliary_balance,auxiliary_pend_amount) values ({companyID},'seller',{oper_id},{sum_left_amount},{pendAmount}) on conflict(company_id,auxiliary_type,auxiliary_id) do update set auxiliary_balance={sum_left_amount},auxiliary_pend_amount={pendAmount};";
                    cmd.CommandText = sqlUpdateSellerArrears;
                    await cmd.ExecuteNonQueryAsync();
                     
                    sql = $"select auxiliary_balance+auxiliary_pend_amount bal from arrears_balance_auxiliary where company_id={companyID} and auxiliary_id={oper_id}; ";
                    record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    if (record != null)
                    {
                        if (double.Parse(seller_max_arrears) < double.Parse(record.bal != "" ? record.bal : "0"))
                        {
                            return new JsonResult(new { result = "Error", msg = $"该业务员已产生过欠款{record.bal}元,超过欠款额度{seller_max_arrears}元" });
                        }
                    }                     
                }

                CMySbTransaction tran = cmd.Connection.BeginTransaction();
                try
                {
                    cmd.CommandText = $"select oper_name from g_operator where company_id={companyID} and mobile='{mobile}' and oper_id<>{oper_id};";
                    object ov = await cmd.ExecuteScalarAsync();
                    if (ov != null && ov != DBNull.Value)
                    {

                        return new JsonResult(new { result = "Error", msg = $"该手机号已被{ov.ToString()}使用" });
                    }
                    
                    sql = db.GetUpdateSQL("g_operator", $"company_id={companyID} and oper_id='{oper_id}' returning oper_pw;");
                    

                    cmd.CommandText = sql;

                    ov = await cmd.ExecuteScalarAsync();
                    if (ov != null && ov != DBNull.Value)
                    {
                        pwd = ov.ToString();
                    }
                    // 如果改了名相应把主账户客户名称改了
                    await UpdateResellerClient(oper_id, data.oper_name);
                    // if (can_login == "True" && data.status == "1")
                    //    TokenChecker.UpdateOperPwd(oper_id, pwd);
                    //else
                    await TokenChecker.RemoveOper(oper_id);

                    OperatorEditModel model = new OperatorEditModel();
                    if (new_oper_id != "")
                        data.oper_id = new_oper_id;
                    res = await model.SaveTable(cmd, data, tran);

                    msg = await checkUserCountValid();
                    if (msg != "")
                    {
                        tran.Rollback();
                        return new JsonResult(new { result = "Error", msg });
                    }

                    tran.Commit();
                }
                catch (Exception ex)
                {
                    NLogger.Error($"保存员工发生错误{ex.Message}");
                    tran.Rollback();
                    return new JsonResult(new { result = "Error", msg = $"保存发生错误" });
                }
            }
            else
            {
                CMySbTransaction tran = cmd.Connection.BeginTransaction();
                try
                {
                    cmd.CommandText = $"select oper_name from g_operator where company_id={companyID} and mobile='{mobile}'";
                    object ov = await cmd.ExecuteScalarAsync();
                    if (ov != null && ov != DBNull.Value)
                    {
                        // conn.Close();
                        return new JsonResult(new { result = "Error", msg = $"该手机号已经被{ov.ToString()}使用" });
                    }
                    string initPwd = pwd = "123456";
                    db.AddField("company_id", companyID);
                    db.AddField("oper_pw", initPwd);

                    sql = db.GetInsertSQL("g_operator") + " returning oper_id";
                    cmd.CommandText = sql;

                    ov = await cmd.ExecuteScalarAsync();
                    if (ov != null && ov != DBNull.Value)
                    {
                        new_oper_id = ov.ToString();
                        if (can_login == "True")
                            await TokenChecker.UpdateOperPwd(new_oper_id, initPwd);
                    }
                    //新员工是否开额度限制

                    if (seller_max_arrears != null && seller_max_arrears != "")
                    {
                        CDbDealer db1 = new CDbDealer();
                        db1.AddField("company_id", companyID);
                        db1.AddField("auxiliary_type", "seller");
                        db1.AddField("auxiliary_id", new_oper_id);
                        db1.AddField("auxiliary_balance", "0");
                        sql = db1.GetInsertSQL("arrears_balance_auxiliary");
                        cmd.CommandText = sql;
                        await cmd.ExecuteNonQueryAsync();
                    }

                    OperatorEditModel model = new OperatorEditModel();

                    if (new_oper_id != "")
                    {
                        data.oper_id = new_oper_id;
                    }
                    res = await model.SaveTable(cmd, data, tran);

                    // 获取返回的数据，如果msg有值代表出现问题，拒绝提交事务
                    dynamic value = res.Value;
                    msg = value.msg;
                    if (new_oper_id != "")
                    {
                        data.oper_id = new_oper_id;
                        /*                        sql = $"SELECT rs_plan.company_id as father_company_id, reseller_name, rs_plan.client_mapper " +
                                                    $" FROM rs_seller LEFT JOIN rs_plan " +
                                                    $" ON rs_seller.plan_id = rs_plan.plan_id" +
                                                    $" WHERE rs_seller.reseller_company_id = {companyID}";
                                                dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                                                if (record != null)
                                                {
                                                    string client_mapper = record.client_mapper;
                                                    if (client_mapper == "sellerAsClient" && is_seller.ToLower() == "true")
                                                    {
                                                        string operName = data.oper_name;
                                                        // 供应商插入客户
                                                        string py_str = PinYinConverter.GetJP(operName).ToLower();


                                                        sql = @$"
                                        insert into info_supcust (company_id, sup_name, boss_name, mobile, supcust_flag, status, supcust_remark, py_str, rs_seller_id,  region_id,  other_region)  
                                        values  ({record.father_company_id},'{operName}({record.reseller_name})', '{record.reseller_name}','{mobile}','C', '1',  '系统自动创建分销商客户档案--{operName}', '{py_str}',{new_oper_id}, 
                                        (select region_id from info_region where company_id = {record.father_company_id} and  mother_id = 0),  '/' ||
                                        (select region_id from info_region where company_id = {record.father_company_id} and  mother_id = 0) || '/' ) returning supcust_id;";


                                                        dynamic fatherClientInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                                                        string fatherClientId = fatherClientInfo.supcust_id;
                                                        // 业务员设置rs_client_id
                                                        cmd.CommandText = $"UPDATE info_operator SET rs_client_id = {fatherClientId} WHERE oper_id  = {new_oper_id};";
                                                        await cmd.ExecuteNonQueryAsync();
                                                    }

                                                }*/
                        await AddResellerClient(new_oper_id);
                    }
                    msg = await checkUserCountValid();
                    if (msg != "")
                    {
                        tran.Rollback();
                        return new JsonResult(new { result = "Error", msg });
                    }
                    tran.Commit();
                }
                catch (Exception ex)
                {
                    tran.Rollback();
                    return new JsonResult(new { result = "Error", msg = $"保存发生错误" });
                }

            }

            //conn.Close();


           
            //if (Startup.Localhost == "https://www.yingjiang168.co")
            {
                // dynamic postData = new {oper_id=new_oper_id,pwd};

                // var ddd=await CommonTool.PostJsonByFactory(_httpClientFactory, "https://coolies.yingjiang.co/api/OpenAccount/UpdateOperInfo", postData);
                //  var ddd = await CommonTool.PostJsonData("https://manage.yingjiang.co/api/OpenAccount/UpdateOperInfo", postData);

                // NLogger.Info($"operatorEdit save:ddd={ddd}");
            }
            async Task AddResellerClient(string target_oper_id)
            {

                sql = @$"
SELECT rs_plan.company_id as father_company_id, reseller_name, rs_plan.client_mapper
FROM rs_seller
LEFT JOIN rs_plan ON rs_seller.plan_id = rs_plan.plan_id
WHERE rs_seller.reseller_company_id = {companyID}";

                dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (record == null || record.client_mapper != "sellerAsClient") { return; }
                sql = $"select * from info_supcust where rs_seller_id = {target_oper_id} and company_id = {record.father_company_id}";
                dynamic existClient= await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (existClient != null) { return; }

                if (record != null)
                {
                    string client_mapper = record.client_mapper;
                    if (client_mapper == "sellerAsClient" && is_seller.ToLower() == "true")
                    {
                        string operName = data.oper_name;
                        // 供应商插入客户
                        string py_str = PinYinConverter.GetJP(operName).ToLower();

                        dynamic rec = await CDbDealer.Get1RecordFromSQLAsync($"select region_id from info_region where company_id = {record.father_company_id} and  mother_id = 0", cmd);
                        string region_id = "null",other_region="null";
                        if (rec != null)
                        {
                            region_id = rec.region_id;
                            other_region = "'/"+ rec.region_id + "/'";
                        }
                       

                        sql = @$"
insert into info_supcust (company_id,                            sup_name,                boss_name,    mobile, supcust_flag, status,                         supcust_remark,      py_str,   rs_seller_id,  region_id,  other_region)  
values   ({record.father_company_id},'{operName}({record.reseller_name})', '{record.reseller_name}','{mobile}',          'C',    '1',  '系统自动创建分销商客户档案--{operName}', '{py_str}',{target_oper_id}, {region_id},{other_region}) returning supcust_id;";


                        rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                        string mumClientID = rec.supcust_id;
                        // 业务员设置rs_client_id
                        cmd.CommandText = $"UPDATE info_operator SET rs_client_id = {mumClientID} WHERE oper_id  = {target_oper_id};";
                        await cmd.ExecuteNonQueryAsync();
                    }

                }
            }
            async Task UpdateResellerClient(string target_oper_id,string newName)
            {
                sql = @$"
SELECT rs_plan.company_id as father_company_id, reseller_name, rs_plan.client_mapper 
FROM rs_seller 
LEFT JOIN rs_plan ON rs_seller.plan_id = rs_plan.plan_id
WHERE rs_seller.reseller_company_id = {companyID}";

                dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (record == null) { return; }
                sql = $"select supcust_id from info_supcust where rs_seller_id = {target_oper_id} and company_id = {record.father_company_id}";
                dynamic existClient = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (existClient == null) {
                    await AddResellerClient(target_oper_id);
                }
                else
                {
                    sql = $"update info_supcust set sup_name = '{newName}({record.reseller_name})' where company_id = {record.father_company_id} and supcust_id = {existClient.supcust_id}";
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                }
                

            }
            return res;

           
        }

        private async Task<int> is_fee_count(string role_id, string company_id)
        {
            string sql = $@"select count(*) is_fee_count
                    from info_role r
                             left join info_role_template t on r.templ_id = t.templ_id and r.company_id = t.company_id
                    where role_id = {role_id}
                      and r.company_id = {company_id}
                      and coalesce(fee_discount, 1) > 0";
            dynamic res_count = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            
            return Convert.ToInt32(res_count.is_fee_count);
        }
        /*
       private async Task<bool> beforeSave()
       { 
           var n = await cmd.QueryAsync<Company>(Token.CompanyID);
           var r = await cmd.QueryAsync<User_g>(Token.CompanyID);
           return n[0].userCount > r.Count;
           
        }*/
    }
}