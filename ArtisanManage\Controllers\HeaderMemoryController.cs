using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ArtisanManage.MyJXC;
using ArtisanManage.Models;
using ArtisanManage.Services;

namespace ArtisanManage.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HeaderMemoryController : BaseController
    {
        public HeaderMemoryController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpPost("save")]
        public async Task<IActionResult> SaveHeaderMemory([FromBody] SaveHeaderMemoryRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.OperKey))
                {
                    return BadRequest(new { result = "Error", msg = "operKey不能为空" });
                }

                if (request.HeaderData == null)
                {
                    return BadRequest(new { result = "Error", msg = "headerData不能为空" });
                }

                Security.GetInfoFromOperKey(request.OperKey, out string companyID, out string operID);

                if (string.IsNullOrEmpty(companyID) || string.IsNullOrEmpty(operID))
                {
                    return BadRequest(new { result = "Error", msg = "无效的操作密钥" });
                }

                // 创建包含saleSheetHeaderMemory的JSON对象
                var options = new Dictionary<string, object>
                {
                    { "saleSheetHeaderMemory", request.HeaderData }
                };

                string optionsJson = JsonConvert.SerializeObject(options);
                optionsJson = optionsJson.Replace("'", "''"); // 转义单引号，防止SQL注入

                try
                {
                    string sql = $@"
                    INSERT INTO options_remembered (company_id, oper_id, options)
                    VALUES ({companyID}, {operID}, '{optionsJson}'::jsonb)
                    ON CONFLICT (company_id, oper_id)
                    DO UPDATE SET options = options_remembered.options || '{optionsJson}'::jsonb";

                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                }
                catch (Exception dbEx)
                {
                    NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                    logger.Error($"数据库操作失败: {dbEx.Message}\nSQL: {cmd.CommandText}\n{dbEx.StackTrace}");
                    MyLogger.LogMsg($"数据库操作失败: {dbEx.Message}", companyID);
                    return StatusCode(500, new { result = "Error", msg = "保存抬头记忆失败：数据库操作错误" });
                }

                return Ok(new { result = "OK", msg = "抬头记忆保存成功" });
            }
            catch (Exception ex)
            {
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error($"SaveHeaderMemory error: {ex.Message}\n{ex.StackTrace}");
                return StatusCode(500, new { result = "Error", msg = "保存抬头记忆失败: " + ex.Message });
            }
        }

        [HttpGet("get")]
        public async Task<IActionResult> GetHeaderMemory([FromQuery] string operKey)
        {
            try
            {
                if (string.IsNullOrEmpty(operKey))
                {
                    return BadRequest(new { result = "Error", msg = "operKey不能为空" });
                }

                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

                if (string.IsNullOrEmpty(companyID) || string.IsNullOrEmpty(operID))
                {
                    return BadRequest(new { result = "Error", msg = "无效的操作密钥" });
                }

                try
                {
                    string sql = $@"
                    SELECT options->'saleSheetHeaderMemory' as header_memory
                    FROM options_remembered
                    WHERE company_id = {companyID} AND oper_id = {operID}";

                    cmd.CommandText = sql;
                    var result = await cmd.ExecuteScalarAsync();

                    if (result == null || result == DBNull.Value)
                    {
                        return Ok(new { result = "OK", headerData = new Dictionary<string, object>() });
                    }

                    string headerMemoryJson = result.ToString();
                    var headerData = JsonConvert.DeserializeObject<Dictionary<string, object>>(headerMemoryJson);

                    return Ok(new { result = "OK", headerData = headerData ?? new Dictionary<string, object>() });
                }
                catch (Exception dbEx)
                {
                    NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                    logger.Error($"数据库操作失败: {dbEx.Message}\nSQL: {cmd.CommandText}\n{dbEx.StackTrace}");
                    MyLogger.LogMsg($"数据库操作失败: {dbEx.Message}", companyID);
                    return StatusCode(500, new { result = "Error", msg = "获取抬头记忆失败：数据库操作错误" });
                }
            }
            catch (Exception ex)
            {
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error($"GetHeaderMemory error: {ex.Message}\n{ex.StackTrace}");
                return StatusCode(500, new { result = "Error", msg = "获取抬头记忆失败: " + ex.Message });
            }
        }
    }

    public class SaveHeaderMemoryRequest
    {
        public string OperKey { get; set; }
        public Dictionary<string, object> HeaderData { get; set; }
    }
}
