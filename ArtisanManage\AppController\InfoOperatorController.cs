﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    /// <summary>
    /// 商品
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class InfoOperatorController : QueryController
    { 
        public InfoOperatorController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        /// <summary>
        /// 获取员工信息 （某一个）
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="oper_id"></param>
        /// <returns></returns>


        [HttpGet]
        public async Task<JsonResult> GetOperator(string operKey, string oper_id) 
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql = $"select oper_id,oper_name,depart_id,mobile,oper_customdesk,visit_schedule_id from info_operator where company_id ={companyID} AND oper_id = {oper_id} ";
            var data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            return Json(new { result = "OK", msg ="", data });
        }


        /// <summary>
        /// 获取业务员列表
        /// </summary>
        /// <param name="operKey"> </param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetSellers(string operKey, string operID,string departID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where company_id = {companyID}";
            if(operID != null) condi += $" AND oper_id = {operID}";
            if (departID != null) condi += $" AND depart_path like '%/{departID}/%'";
            var sql = $"SELECT oper_id,oper_name,status FROM info_operator {condi} and is_seller and COALESCE(status,'1')='1'";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

        /// <summary>
        /// 获取员工列表
        /// </summary>
        /// <param name="operKey"> </param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetOperators(string operKey, string operID, string departID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where company_id = {companyID}";
            if (operID != null) condi += $" AND oper_id = {operID}";
            if (departID != null) condi += $" AND depart_path like '%/{departID}/%'";
            var sql = $"SELECT oper_id,oper_name,status FROM info_operator {condi}  and COALESCE(status,'1')='1'";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }
        [HttpGet]
        public async Task<JsonResult> GetSellersWithOperRight(string operKey, string operID, string departID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where company_id = {companyID} ";
            if (operID != null) condi += $" and oper_id = {operID} ";
            if (departID != null) condi += $" and depart_path like '%{"/"+ departID + "/"}%' ";
            var sql = $"SELECT oper_id,oper_name,status FROM info_operator {condi} and is_seller and COALESCE(status,'1')='1'";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }


        [HttpGet]
        public async Task<JsonResult> GetSenders(string operKey, string operID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where company_id = {companyID}";
            if (operID != null) condi += $"oper_id = {operID}";
            var sql = $"SELECT oper_id,oper_name,status FROM info_operator {condi} and COALESCE(status,'1')='1' and is_sender ";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

        [HttpGet]
        public async Task<JsonResult> GetSendersWithOperRight(string operKey,string operID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where company_id = {companyID}";
            if (operID != null) condi += $"oper_id = {operID}";
            var sql = $"SELECT oper_id,oper_name,status FROM info_operator {condi} and COALESCE(status,'1')='1' and is_sender ";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

        /// <summary>
        /// 修改账户密码且返回旧密码
        /// </summary>
        /// <param name="data">wcAqiAdqGYG39sTafoxzNuV7gjl0d-zEX5Q5vIEsZ4CJBL8L71cPvCkNmSBpbvSukmnIwUZFvIg~</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> UpdateOperPwd([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID,out string operID); 
            string oper_pw = data.oper_pw;
            var sql = $@"SELECT oper_pw FROM g_operator WHERE company_id = {companyID} and oper_id = {operID};
                         UPDATE g_operator SET oper_pw = '{oper_pw}' WHERE  company_id = {companyID} and oper_id = {operID};";
            cmd.CommandText = sql;
            object ov = await cmd.ExecuteScalarAsync();
            string oper_pw1 = "";
            if (ov != null && ov != DBNull.Value) {  
                oper_pw1 = ov.ToString();  
            }
            await TokenChecker.UpdateOperPwd(operID, oper_pw);
            string operKey = new Token
            {
                OperID = operID,
                CompanyID = companyID, 
                Pwd = oper_pw
            }.ToString();
            return Json(new { result = "OK", msg = "", operKey, oper_pw1 });
        }
            

    }
}
