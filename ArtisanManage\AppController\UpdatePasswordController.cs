﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.CodeAnalysis.CSharp.Syntax;

namespace ArtisanManage.AppController
{
    [Route("AppApi/[controller]/[action]")]
    public class UpdatePasswordController : YjController
    {
        CMySbCommand cmd; 
        public UpdatePasswordController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpPost]
        public async Task<Response> UpdatePassword([FromBody] dynamic user)
        {
            string originalpass = user.oldPassword;
            string sqlcheck =
                $"select * from g_operator where company_id = {Token.CompanyID} and oper_id = {Token.OperID} and oper_pw = '{originalpass}'";
            cmd.CommandText = sqlcheck;
            var val = await cmd.ExecuteScalarAsync();
            if (val == null)
            {
                return Error("原密码错误");
            }
            string sql = $"update g_operator set oper_pw='{user.Password}' where company_id = {Token.CompanyID} and oper_id={Token.OperID}";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            await TokenChecker.UpdateOperPwd(Token.OperID, (string) user.Password);
            return response;
        }
       
    }


}

