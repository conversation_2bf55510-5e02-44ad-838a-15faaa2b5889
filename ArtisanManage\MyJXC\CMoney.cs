﻿using System;
using System.Collections.Generic;
using System.Text;

namespace myJXC
{
    public class CMoney
    {
    private char 转换数字(char x)
　　{        
　　string stringChnNames="零壹贰叁肆伍陆柒捌玖";
　　string stringNumNames="0123456789";
　　return stringChnNames[stringNumNames.IndexOf(x)];
　　}
　　private string 转换万以下整数(string x)
　　{
　　string[] stringArrayLevelNames=new string[4] {"","拾","佰","仟"};
　　string ret="";
　　int i;
　　for (i=x.Length-1;i>=0;i--)
　　if (x[i]=='0')
　　ret=转换数字(x[i])+ret;
　　else
　　ret=转换数字(x[i])+stringArrayLevelNames[x.Length-1-i]+ret;
　　while ((i=ret.IndexOf("零零"))!=-1)
　　ret=ret.Remove(i,1); 
　　if (ret[ret.Length-1]=='零' && ret.Length>1)
　　　　　ret=ret.Remove(ret.Length-1,1);
　　if (ret.Length >= 2 && ret.Substring(0, 2) == "壹拾")
　　　　　ret=ret.Remove(0,1);
　　return ret;
　　}
　　private string 转换整数(string x)
　　{
　　int len=x.Length;
　　string ret,temp;
　　if (len<=4)
　　ret=转换万以下整数(x);
　　else if (len<=8)
　　{
　　ret=转换万以下整数(x.Substring(0,len-4))+"万";
　　temp=转换万以下整数(x.Substring(len-4,4));
  if (temp.IndexOf("仟") == -1 && temp != "")
　　ret+="零"+temp;
　　else
　　ret+=temp;
　　}
　　else
　　{
　　ret=转换万以下整数(x.Substring(0,len-8))+"亿";
　　temp=转换万以下整数(x.Substring(len-8,4));
  if (temp.IndexOf("仟") == -1 && temp != "")
　　ret+="零"+temp;
　　else
　　ret+=temp;
　　ret+="万";
　　temp=转换万以下整数(x.Substring(len-4,4));
  if (temp.IndexOf("仟") == -1 && temp != "")
　　ret+="零"+temp;
　　else
　　ret+=temp;
　　}
　　int i;
　　if ((i=ret.IndexOf("零万"))!=-1) 
　　ret=ret.Remove(i+1,1);
　　while ((i=ret.IndexOf("零零"))!=-1)
　　ret=ret.Remove(i,1);
　　if (ret[ret.Length-1]=='零' && ret.Length>1)
　　ret=ret.Remove(ret.Length-1,1);
　　return ret;
　　}
　　
　　private string 转换小数(string x)
　　{
　　  string ret="";
      for (int i = 0; i < x.Length; i++)
      {
          ret += 转换数字(x[i]);
          if (i == 0)
              ret += "角";
          else if (i == 1)
              ret += "分";
          else
              break;
      }
　  　return ret;
　　}
　　
　　public string NumToChn(string x)
　　{
　　    if (x.Length==0)
　　    return "";
　　    string ret="";
　　    if (x[0]=='-')
　　    {
　　    ret="负";
　　    x=x.Remove(0,1);
　　    }
　　    if (x[0].ToString()==".")
　　    x="0"+x;
　　    if (x[x.Length-1].ToString()==".")
　　    x=x.Remove(x.Length-1,1);
        if (x.IndexOf(".") > -1)
        {
            
            string sInt = ""; string sDot = "";   
            sInt = 转换整数(x.Substring(0, x.IndexOf(".")));
            sDot = 转换小数(x.Substring(x.IndexOf(".") + 1));
            ret += sInt + "圆" + sDot;
        }
        else
            ret += 转换整数(x)+"圆整";
　　        return ret;
　　    }

        public string NumToSpeak(string x)
        {
            if (x.Length == 0)
                return "";
            string ret = "";
            if (x[0] == '-')
            {
                ret = "负";
                x = x.Remove(0, 1);
            }
            if (x[0].ToString() == ".")
                x = "0" + x;
            if (x[x.Length - 1].ToString() == ".")
                x = x.Remove(x.Length - 1, 1);
            if (x.IndexOf(".") > -1)
            {

                string sInt = ""; string sDot = "";
                sInt = 转换整数(x.Substring(0, x.IndexOf(".")));
                
                ret += sInt + "点" + sDot;
            }
            else
                ret += 转换整数(x) ;
            return ret;
        }
       public string ConvertSum(string str)
          {
               if(!IsPositveDecimal(str))
               return "输入的不是正数字！";
               if(Double.Parse(str)>999999999999.99)
               return "数字太大，无法换算，请输入一万亿元以下的金额";
               char[] ch=new char[1];
               ch[0]='.'; //小数点
               string[] splitstr=null; //定义按小数点分割后的字符串数组
               splitstr=str.Split(ch[0]);//按小数点分割字符串
               if(splitstr.Length==1) //只有整数部分
               return ConvertData(str)+"圆整";
               else //有小数部分
               {
                    string rstr;
                    rstr=ConvertData(splitstr[0])+"圆";//转换整数部分
                    rstr+=ConvertXiaoShu(splitstr[1]);//转换小数部分
                    return rstr;
               }
          } 


           ///
          /// 判断是否是正数字字符串
          ///
          /// 判断字符串
          /// 如果是数字，返回true，否则返回false
          public bool IsPositveDecimal(string str)
          {
               Decimal d;
               try
               {
                    d=Decimal.Parse(str);
               }
               catch(Exception)
               {
                   return false;
               }
               if(d>0)
               return true;
               else
               return false;
          } 


          ///
          /// 转换数字（整数）
          ///
          /// 需要转换的整数数字字符串
          /// 转换成中文大写后的字符串
          public string ConvertData(string str)
          {
               string tmpstr="";
               string rstr="";
               int strlen=str.Length;
               if (strlen<=4)//数字长度小于四位
               {
                    rstr= ConvertDigit(str);

               }
               else
               {
                    if (strlen<=8)//数字长度大于四位，小于八位
                    {
                         tmpstr=str.Substring(strlen-4,4);//先截取最后四位数字
                         rstr=ConvertDigit(tmpstr);//转换最后四位数字
                         tmpstr=str.Substring(0,strlen-4);//截取其余数字
                         //将两次转换的数字加上万后相连接
                         rstr= String.Concat(ConvertDigit(tmpstr)+"万",rstr);
                         rstr=rstr.Replace("零零","零");
                    }
                else
                if(strlen<=12)//数字长度大于八位，小于十二位
                {
                     tmpstr=str.Substring(strlen-4,4);//先截取最后四位数字
                     rstr=ConvertDigit(tmpstr);//转换最后四位数字
                     tmpstr=str.Substring(strlen-8,4);//再截取四位数字
                     rstr= String.Concat(ConvertDigit(tmpstr)+"万",rstr);
                     tmpstr=str.Substring(0,strlen-8);
                     rstr= String.Concat(ConvertDigit(tmpstr)+"亿",rstr);
                     rstr=rstr.Replace("零亿","亿");
                     rstr=rstr.Replace("零万","零");
                     rstr=rstr.Replace("零零","零");
                     rstr=rstr.Replace("零零","零");
                }
               }
               strlen=rstr.Length;
               if (strlen>=2)
               {
                    switch(rstr.Substring(strlen-2,2))
                    {
                         case "佰零":rstr=rstr.Substring(0,strlen-2)+"佰"; break;
                         case "仟零":rstr=rstr.Substring(0,strlen-2)+"仟"; break;
                         case "万零":rstr=rstr.Substring(0,strlen-2)+"万";break;
                         case "亿零":rstr=rstr.Substring(0,strlen-2)+"亿";break;
                    }
               }
               return rstr;
          }

          ///
          /// 转换数字（小数部分）
          ///
          /// 需要转换的小数部分数字字符串
          /// 转换成中文大写后的字符串
          public string ConvertXiaoShu(string str)
          {
              int strlen = str.Length;
              string rstr;
              if (strlen == 1)
              {
                  rstr = ConvertChinese(str) + "角";
                  return rstr;
              }
              else
              {
                  string tmpstr = str.Substring(0, 1);
                  rstr = ConvertChinese(tmpstr) + "角";
                  tmpstr = str.Substring(1, 1);
                  rstr += ConvertChinese(tmpstr) + "分";
                  rstr = rstr.Replace("零分", "");
                  rstr = rstr.Replace("零角", "");
                  return rstr;
              }
          }


          ///
          /// 转换数字
          ///
          /// 转换的字符串（四位以内）
          ///
          public string ConvertDigit(string str)
          {
              int strlen = str.Length;
              string rstr = "";
              switch (strlen)
              {
                  case 1: rstr = ConvertChinese(str); break;
                  case 2: rstr = Convert2Digit(str); break;
                  case 3: rstr = Convert3Digit(str); break;
                  case 4: rstr = Convert4Digit(str); break;
              }
              rstr = rstr.Replace("拾零", "拾");
              strlen = rstr.Length;
              return rstr;
          }


          ///
          /// 转换四位数字
          ///
          public string Convert4Digit(string str)
          {
              string str1 = str.Substring(0, 1);
              string str2 = str.Substring(1, 1);
              string str3 = str.Substring(2, 1);
              string str4 = str.Substring(3, 1);
              string rstring = "";
              rstring += ConvertChinese(str1) + "仟";
              rstring += ConvertChinese(str2) + "佰";
              rstring += ConvertChinese(str3) + "拾";
              rstring += ConvertChinese(str4);
              rstring = rstring.Replace("零仟", "零");
              rstring = rstring.Replace("零佰", "零");
              rstring = rstring.Replace("零拾", "零");
              rstring = rstring.Replace("零零", "零");
              rstring = rstring.Replace("零零", "零");
              rstring = rstring.Replace("零零", "零");
              return rstring;
          }
          ///
          /// 转换三位数字
          ///
          public string Convert3Digit(string str)
          {
              string str1 = str.Substring(0, 1);
              string str2 = str.Substring(1, 1);
              string str3 = str.Substring(2, 1);
              string rstring = "";
              rstring += ConvertChinese(str1) + "佰";
              rstring += ConvertChinese(str2) + "拾";
              rstring += ConvertChinese(str3);
              rstring = rstring.Replace("零佰", "零");
              rstring = rstring.Replace("零拾", "零");
              rstring = rstring.Replace("零零", "零");
              rstring = rstring.Replace("零零", "零");
              return rstring;
          }


          ///
          /// 转换二位数字
          ///
          public string Convert2Digit(string str)
          {
              string str1 = str.Substring(0, 1);
              string str2 = str.Substring(1, 1);
              string rstring = "";
              rstring += ConvertChinese(str1) + "拾";
              rstring += ConvertChinese(str2);
              rstring = rstring.Replace("零拾", "零");
              rstring = rstring.Replace("零零", "零");
              return rstring;
          }


          ///
          /// 将一位数字转换成中文大写数字
          ///
          public string ConvertChinese(string str)
          {
              //"零壹贰叁肆伍陆柒捌玖拾佰仟万亿圆整角分"
              string cstr = "";
              switch (str)
              {
                  case "0": cstr = "零"; break;
                  case "1": cstr = "壹"; break;
                  case "2": cstr = "贰"; break;
                  case "3": cstr = "叁"; break;
                  case "4": cstr = "肆"; break;
                  case "5": cstr = "伍"; break;
                  case "6": cstr = "陆"; break;
                  case "7": cstr = "柒"; break;
                  case "8": cstr = "捌"; break;
                  case "9": cstr = "玖"; break;
              }
              return (cstr);
          }
 



}
}
