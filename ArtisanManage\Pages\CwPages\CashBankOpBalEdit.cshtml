﻿@page
@model ArtisanManage.Pages.CwPages.CashBankOpBalEditModel
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>CashBankOpBalEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
             @Html.Raw(Model.m_showFormScript)
             @Html.Raw(Model.m_createGridScript)
        });

        function CheckAndSave(){
            var formData = getFormData();
            if (formData.status != '正常') {
                bw.toast('账户已停用，无法修改期初');
                return;
            }
            if (isNaN(formData.balance)) {
                bw.toast('期初余额请输入数字', 2000);
                return;
            }
            btnSave_Clicked();
        }      

    </script>
    <style>
    </style>
</head>
<body>
    <div id="divHead" class="headtail" style="margin-top:30px;"></div> 
    <div style="text-align:center;margin-top:20px;">
        <button id="btnSave" onclick="CheckAndSave();" style="margin-right:50px;">保存</button>
        <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>@*进入view的listen message*@
    </div>
</body>
</html>
