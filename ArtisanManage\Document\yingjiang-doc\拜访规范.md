#  拜访规范

# 1. 需求分析

1. 通过一套代码实现多端适配，主要手机端页面展示电脑端页面
2. 通过**拜访规范**实现**拜访门店**中动态展示，例如配置不同类型的图片及数量，不同的备注等
3. 对应的权限确认

# 2. 数据库设计

## info_visit_template（公司规范信息表）

| 字段          | 中文         | 类型      | 备注         |
| ------------- | ------------ | --------- | ------------ |
| company_id    | 公司id       |           |              |
| template_id   | 主键，规范id |           |              |
| template_name | 模板名称     |           |              |
| actions       | 规范配置     | 【jsonb】 | 示例如下所示 |

```json
【{
    name: '门头照', 
    type: 'photo',
    minNum: 3,
    maxNum: 5, 
    tip: '此处可以填写老板的一些需求，展示给业务员看，为改配置总体情况'
    items :[{
    			title: 'xxx',
    			tip: 'xxxxx'
			}]		// 数组长度应该 ≤ maxNum
}，

{
    name: '日志1', 
    type: 'brief',
  
    minLen: 3,
    maxLen: 5, 
    tip: '此处可以填写老板的一些需求，展示给业务员看，为改配置总体情况'
}】

```



## info_display_template（公司陈列协议信息表）

| 字段                   | 中文           | 类型  | 备注                                      |
| ---------------------- | -------------- | ----- | ----------------------------------------- |
| company_id             | 公司id         |       |                                           |
| disp_template_id       | 主键，规范id   |       |                                           |
| disp_template_name     | 模板名称       |       |                                           |
| sup_id                 | 供应商id       |       |                                           |
| display_form           | 陈列形式       |       | info_display_form 中id 堆头、xxx          |
| sign_actions           | 签约凭证模板   | jsonb | 模板单独保存，可以选择info_display_action |
| keep_actions           | 续签凭证模板   |       |                                           |
| maintain_actions       | 维护凭证模板   |       |                                           |
| give_actions           | 兑付凭证模板   |       |                                           |
| month_maintain_times   | 月维护次数     |       |                                           |
| maintain_interval_days | 月维护间隔     |       |                                           |
| latest_sign_month_day  | 几号以前为当月 |       |                                           |
| start_time             | 开始时间       |       |                                           |
| end_time               | 结束时间       |       |                                           |
| give_condition         | 兑付时机       |       | after_sign 立即  after_maintain拜访后     |
| give_items             | 钱、货         | jsonb | 类似sheetRows，考虑商品删除，禁用情况     |
| sign_need_review       | 签约需要复核   |       |                                           |
| maintain_need_review   | 维护需要复核   |       |                                           |
| give_need_review       | 兑付需要复核   |       |                                           |
|                        |                |       |                                           |
|                        |                |       |                                           |
| resign_interval_months | 续签间隔       |       |                                           |

```json
{
    name: '门头照', 
    type: 'photo',
    minNum: 3,
    maxNum: 5, 
    tip: '此处可以填写老板的一些需求，展示给业务员看，为改配置总体情况'
    items :[{
    			title: 'xxx',
    			tip: 'xxxxx'
			}]		// 数组长度应该 ≤ maxNum
}
```

```json
[
    {								// 影响后续的统计，需要考虑
        items_id: 'cash',
        items_name: '钱'，
        unit_no: '元',
        month1_qty: 11,
        month2_qty: 11,
        month3_qty: 11,
        ....
        month12_qty: 11,
        remark: ''
    }，
    {
        items_id: 'xxx,xxx,xxx',
        items_name: 'yy,yy,yy'，
        unit_no: '箱',
        month1_qty: 11,
        month2_qty: 11,
        month3_qty: 11,
        ....
        month12_qty: 11,
        remark: ''
    }，
]

```

## info_display_form

| 字段       | 中文   | 类型   | 备注           |
| ---------- | ------ | ------ | -------------- |
| company_id | 公司id |        |                |
| form_id    | 自增   |        |                |
| form_name  | 名称   |        |                |
| status     | 状态   | number | 1 有效 0已删除 |

## info_display_action（暂定，还未设置）

| 字段        | 中文   | 类型 | 备注 |
| ----------- | ------ | ---- | ---- |
| company_id  | 公司id |      |      |
| action_id   | 自增   |      |      |
| action_name | 名称   |      |      |

## info_display_actions_mapping_main (开陈列协议关联模板主表 放弃)

| 字段                   | 中文                         | 类型  | 备注                                      |
| ---------------------- | ---------------------------- | ----- | ----------------------------------------- |
| mapping_main_id        | 主键，自增id                 |       |                                           |
| company_id             | 公司id                       |       |                                           |
| disp_template_id       | 规范id                       |       |                                           |
| disp_template_name     | 模板名称                     |       |                                           |
| sup_id                 | 供应商id                     |       |                                           |
| display_form           | 陈列形式                     |       | info_display_form 中id 堆头、xxx          |
| sign_actions           | 签约凭证模板                 | jsonb | 模板单独保存，可以选择info_display_action |
| keep_actions           | 续签凭证模板                 |       |                                           |
| maintain_actions       | 维护凭证模板                 |       |                                           |
| give_actions           | 兑付凭证模板                 |       |                                           |
| month_maintain_times   | 月维护次数                   |       |                                           |
| maintain_interval_days | 月维护间隔                   |       |                                           |
| latest_sign_month_day  | 几号以前为当月               |       |                                           |
| start_time             | 开始时间                     |       |                                           |
| end_time               | 结束时间                     |       |                                           |
| give_condition         | 兑付时机                     |       | after_sign 立即  after_maintain拜访后     |
| give_items             | 钱、货                       | jsonb | 类似sheetRows，考虑商品删除，禁用情况     |
| sign_need_review       | 签约需要复核                 |       |                                           |
| maintain_need_review   | 维护需要复核                 |       |                                           |
| give_need_review       | 兑付需要复核                 |       |                                           |
|                        | 续签间隔                     |       |                                           |
|                        |                              |       |                                           |
|                        | 本月已维护次数每个月维护次数 |       |                                           |
| sheet_id               | 关联的单据id                 |       |                                           |
| happen_time            | 开单时间                     |       |                                           |
|                        | 业务员id                     |       |                                           |
|                        | 客户id                       |       |                                           |



## info_display_actions_mapping_detail (开陈列协议关联模板详表 放弃)

| 字段              | 中文         | 类型    | 备注                                                         |
| ----------------- | ------------ | ------- | ------------------------------------------------------------ |
| mapping_detail_id | 自增id       |         |                                                              |
| mapping_main_id   | 关联主表id   |         |                                                              |
|                   | 业务员id     |         |                                                              |
|                   | 客户id       |         | 0                                                            |
| happen_time       | 时间         |         |                                                              |
| type              | 类型         |         | sign   签约<br />keep  续签<br />maintain 维护<br />give  兑付 |
| work_content      | 拜访记录详情 |         | [<br />       { action: [], work_content: []}<br />]         |
| review            | 复核状态     | boolean | 待复核、合格、不合格需要重新记录、不合格无需重新记录         |
| review_remark     | 复核备注     |         |                                                              |
|                   |              |         |                                                              |
|                   |              |         |                                                              |







## info_equipment_template（设备表）

| 字段                 | 中文         | 类型  | 备注            |
| -------------------- | ------------ | ----- | --------------- |
| company_id           | 公司id       |       |                 |
| template_id          | 主键，规范id |       |                 |
| return_visit_actions | 回访行为     | jsonb | 示例如下所示    |
| return_visit_cycle   | 回访周期     |       | 几天一次，存N天 |

```json
{
    name: '门头照', 
    type: 'photo',
    minNum: 3,
    maxNum: 5, 
    tip: '此处可以填写老板的一些需求，展示给业务员看，为改配置总体情况'
    items :[{
    			title: 'xxx',
    			tip: 'xxxxx'
			}]		// 数组长度应该 ≤ maxNum
}

{
    name: '日志1', 
    type: 'brief',
    minLen: 3,
    maxLen: 5, 
    tip: '此处可以填写老板的一些需求，展示给业务员看，为改配置总体情况'
}

```



## info_visit_template_mapping（公司规范信息表映射表）

| 字段                  | 中文        | 类型 | 备注  |
| --------------------- | ----------- | ---- | ----- |
| mapping_id            | 自增        |      |       |
| company_id            | 公司id      |      |       |
| sup_group             | 渠道        |      |       |
| sup_rank              | 客户等级    |      |       |
| dept_id               | 部门id 单选 |      |       |
| sellers_id            | 员工的id    |      | 1,2,3 |
| visit_template_id     | 常规拜访id  |      |       |
| display_template_id   | 陈列拜访    |      |       |
| equipment_template_id | 设备id      |      |       |
| order_index           | 排序        |      |       |

```sql
create table info_visit_template_mapping
(
    mapping_id        serial
        constraint info_visit_template_mapping_pk
            primary key,
    company_id        integer not null,
    sup_group         integer,
    sup_rank          integer,
    dept_id           integer,
    sellers_id        text,
    info_visit_id     integer,
    info_display_id   integer,
    info_equipment_id integer
);

create unique index info_visit_template_mapping_mapping_id_uindex
    on info_visit_template_mapping (mapping_id);


```



# 3. 需求设计

## 3.1 前端

1. 主体代码  为服务器端，手机端通过传入operKey进行页面操作
2. info_visit_standard  表中standard_config 在前端进行定义
3. 考虑切换不同的服务器，手机端的地址应该是随之变化

## 3.2 后端

1. 公司规范配置接口，CRUD
2. 员工关联，CRUD

```
[{
		"id": 1,
		"name": "门头照",
		"type": "photo",
		"minNum: 3,
		"maxNum: 5,
		"tip: "此处可以填改配置总体情况",
		"items: "[{
			id: 1,
			title: 'xxx',
			tip: 'xxxxx'
		}, {
			id: 2,
			title: 'xxx',
			tip: 'xxxxx'
		}]"
	},
	{
		id: 2,
		name: '门照',
		type: 'photo',
		minNum: 3,
		maxNum: 5,
		tip: '此处可以体情况',
		items: [{
			id: 1,
			title: 'xxx',
			tip: 'xxxxx'
		}, {
			id: 2,
			title: 'xxx',
			tip: 'xxxxx'
		}]
	}
]
```

## 3.3 陈列协议相关考虑







```json
[
    {
        "action": {"id": 1, "tip": "需要把门店及店招拍下来，没有店招的直接拍门店即可！角度要正", "name": "门头照片", "type": "photo", "items": [{"id": 1, "tip": "暂无要求", "title": "必选"}], "maxNum": 1, "minNum": 1}, 
     "work_content": {"optional": [], "mandatory": ["uploads/202212/930_4980_593767_a0m0_20221204185300.jpg"]}}, 
    
    
    
    {"action": {"id": 2, "tip": "产品理货前摆放的样子", "name": "理货前照片", "type": "photo", "items": [{"id": 1, "tip": "暂无要求", "title": "必选"}], "maxNum": 1, "minNum": 1}, "work_content": {"optional": [], "mandatory": ["uploads/202212/930_4980_593767_a1m0_20221204185300.jpg"]}}, {"action": {"id": 3, "tip": "自己负责产品动手整理摆放后的照片", "name": "理货后照片", "type": "photo", "items": [{"id": 1, "tip": "暂无要求", "title": "必选"}], "maxNum": 1, "minNum": 1}, "work_content": {"optional": [], "mandatory": ["uploads/202212/930_4980_593767_a2m0_20221204185300.jpg"]}}, {"action": {"id": 4, "tip": "离开门店记得拍照，要求与进店照片一致", "name": "离店照片", "type": "photo", "items": [{"id": 1, "tip": "暂无要求", "title": "必选"}], "maxNum": 1, "minNum": 1}, "work_content": {"optional": [], "mandatory": ["uploads/202212/930_4980_593767_a3m0_20221204185300.jpg"]}}]
```



