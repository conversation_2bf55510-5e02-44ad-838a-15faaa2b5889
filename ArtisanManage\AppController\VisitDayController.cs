﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    [Route("AppApi/[controller]/[action]")]
    public class VisitDayController : BaseController
    {
 

        public VisitDayController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpPost]
        public async Task<JsonResult> InsertTheDaySupcust([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string order_index = data.order_index;
            if (order_index.IsValid()&&data.operType=="insert")
            {
                string updateIndexSQL = @$"update info_visit_day_client set order_index=order_index+1 where day_id={data.day_id} and company_id={companyID} and order_index>={data.order_index}";
                cmd.CommandText = updateIndexSQL;
                await cmd.ExecuteNonQueryAsync();
            }
            string insertSQL = $@"insert into info_visit_day_client(company_id,order_index,day_id,supcust_id) values({companyID},{data.order_index},{data.day_id},{data.supcust_id})";
            cmd.CommandText = insertSQL;
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new
            {
                result = "OK"
            });
        }
        [HttpPost]
        public async Task<JsonResult> RemoveTheDaySupcust([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string removeSQL = $@"delete from info_visit_day_client where order_index={data.order_index} and  day_id={data.day_id} and company_id={companyID}";
            cmd.CommandText = removeSQL;
            await cmd.ExecuteNonQueryAsync();
            string updateIndexSQL = @$"update info_visit_day_client set order_index=order_index-1 where day_id={data.day_id} and company_id={companyID} and order_index>={data.order_index}";
            cmd.CommandText = updateIndexSQL;
            await cmd.ExecuteNonQueryAsync();

            return new JsonResult(new
            {
                result = "OK"
            });
        }
        /**
            [HttpGet]
            public async Task<JsonResult> GetRegions([FromBody] dynamic data)
            {
                Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
                string removeSQL = $@"SELECT  company_id={companyID}";
                cmd.CommandText = removeSQL;
                await cmd.ExecuteNonQueryAsync();

                return new JsonResult(new
                {
                    result = "OK"
                });
            }**/
        [HttpPost]
        public async Task<JsonResult>UpdateOrderIndex([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string updateOrderIndex = data.updateOrderIndex;
            string[] orderGroup = updateOrderIndex.Split("$");
            string sql = "";
            foreach(string orderSeg in orderGroup)
            {
                string  []orderSegs = orderSeg.Split("#");
                string supcustID = orderSegs[0];
                string orderIndex = orderSegs[1];
                sql+= @$"UPDATE info_visit_day_client set  supcust_id={supcustID},order_index={orderIndex} where company_id={companyID} and supcust_id={supcustID} and day_id={data.day_id};";
            }

            //old index，new index
            //1. 比待插入的index大的值+1
            //2. 赋值插入的index
            // 2 3 1 
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            
            return new JsonResult(new
            {
                result = "OK"
            });
        }
        [HttpPost]
        public async Task<JsonResult> SaveOrUpdateVisitDay([FromBody] dynamic data) {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string sql;
            if (data.day_id!=null&&data.day_id!="")
            {
                sql = $@"update info_visit_day set day_name={data.day_name} where day_id={data.day_id}";
          
            }
            else
            {
                sql = $@"insert into info_visit_day (company_id,schedule_id,day_name,status,py_str) values({companyID},{data.schedule_id},'{data.day_name}','1','{data.py_str}')";
            }
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new
            {
                result = "OK"
            });
        }
    }
}
