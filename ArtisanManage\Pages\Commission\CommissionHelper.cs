﻿using ArtisanManage.Enums;
using ArtisanManage.Models;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Data;
using Microsoft.CodeAnalysis.Elfie.Model.Structures;
using NPOI.SS.Formula;
using Org.BouncyCastle.Asn1.Cmp;
using NPOI.SS.Formula.PTG;

namespace ArtisanManage.Pages
{
    /// <summary>
    /// 
    /// </summary>
    public static class CommissionHelper
    {
        /// <summary>
        ///  统计总额
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        public static double[] GetAmt(this CommissionInfo info, bool excludeArrearages)
        {
            if (excludeArrearages && info.is_arrears) return new double[] { 0, 0 };
            return new double[] { info.x_amount, info.t_amount };
        }

        public static double[] GetCost(this CommissionInfo info, bool excludeArrearages, string costType)
        {
            if (excludeArrearages && info.is_arrears) return new double[] { 0, 0 };
            if (costType == CostType.avgCost.ToString())
            {
                return new double[] { info.x_cost_price_avg, info.t_cost_price_avg };
            }
            else
            {
                return new double[] { info.x_cost_buy_price, info.t_cost_buy_price };
            }
        }

        public static double[] GetQty(this CommissionInfo info, bool byBigUnit, bool includeGift,bool excludeArrearages)
        {
            if (excludeArrearages && info.is_arrears) return new double[] { 0, 0 };
            var result = new double[] { info.x_quantity, info.t_quantity };
            if (includeGift)
            {
                result[0] += info.x_gift_quantity;
                result[1] += info.t_gift_quantity;
            }
            if (byBigUnit)
            {
                result[0] /= (double)info.factor_b;
                result[1] /= (double)info.factor_b;
            }
            
            return result;
        }

        
        /// <summary>
        /// 固定模式，没有提成基准
        /// </summary>
        /// <param name="commission"></param>
        /// <param name="Rates"></param>
        /// <param name="commissionBy"></param>
        /// <param name="flag"></param>
        public static void Compute_fix(this CommissionDetail commission, List<CommissionRate> Rates, string commissionBy, string flag,bool unDeductReturned,out string errMsg)
        {
            errMsg = "";
            System.Data.DataTable dt = new System.Data.DataTable();
            var qty_x = commission.Quantity_x;//销量
            var qty_t = commission.Quantity_t;//退量
            var amount_x = commission.Amount_x;//销金
            var amount_t = commission.Amount_t;//退金
            var profi_x = commission.Profi_x;//销利
            var profi_t = commission.Profi_t;//退利
            var isFormula_x = false;
            var isFormula_t = false;
            
            var comm_x = 0d;
            var comm_t = 0d;

            var rate_x = Rates[0].X;
            string formula_x = rate_x.Substring(1);
            
            if (rate_x.StartsWith("="))
            {
                isFormula_x = true;
                string origFormula = formula_x;

				formula_x = formula_x.Replace("（", "(").Replace("）", ")");
                formula_x = formula_x.Replace("%", "/100");
                formula_x = formula_x.Replace("销量", qty_x.ToString()).Replace("退量", qty_t.ToString()).Replace("销金", amount_x.ToString()).Replace("退金", amount_t.ToString()).Replace("销利", profi_x.ToString()).Replace("退利", profi_t.ToString());
				object result;
				try
				{
					result = dt.Compute(formula_x, "");
				}
				catch (Exception e)
				{
					errMsg = $"公式设置不正确:{origFormula}";
					return;
				}
				if (result != DBNull.Value)
                {
                    comm_x = Convert.ToDouble(result.ToString());

                }
            }

            var rate_t = Rates[0].T;
            string formula_t = rate_t.Substring(1);

            if (unDeductReturned)
            {
                if(rate_t.StartsWith("="))
                {
                    isFormula_t = true;
                    string origFormula = formula_t;
					formula_t = formula_t.Replace("（", "(").Replace("）", ")");
                    formula_t = formula_t.Replace("%", "/100");
                    formula_t = formula_t.Replace("销量", qty_x.ToString()).Replace("退量", qty_t.ToString()).Replace("销金", amount_x.ToString()).Replace("退金", amount_t.ToString()).Replace("销利", profi_x.ToString()).Replace("退利", profi_t.ToString());
					object result;
					try
					{
						result = dt.Compute(formula_t, "");
					}
					catch (Exception e)
					{
						errMsg = $"公式设置不正确:{origFormula}";
						return;
					}

					if (result != DBNull.Value)
                    {
                        comm_t = Convert.ToDouble(result.ToString());
                    }
                }

            }
            if (commissionBy == CommissionBy.qty.ToString())//仅金额
            {
                flag = CommissionFlag.amount.ToString();
                if (isFormula_x)
                {
                    commission.Commission_x = comm_x;
                }
                else
                {
                    commission.Commission_x = commission.Quantity_x * Convert.ToDouble(Rates[0].X);
                    commission.FormulaDisplay_x = $"(销售数量){commission.Quantity_x_str} * ";
                }

                if (unDeductReturned)
                {
                    if (isFormula_t)
                    {
                        commission.Commission_t = comm_t;
                    }
                    else
                    {
                        commission.Commission_t = commission.Quantity_t * Convert.ToDouble(Rates[0].T);
                        commission.FormulaDisplay_t = $"(退货数量){commission.Quantity_t_str} * ";
                    }
                }
            }
            else if (commissionBy == CommissionBy.price.ToString())
            {
                /*不支持固定模式*/
            }
            else if (commissionBy == CommissionBy.amt.ToString())//仅百分比
            {
                flag = CommissionFlag.rate.ToString();
                if (isFormula_x)
                {
                    commission.Commission_x = comm_x;
                }
                else
                {
                    commission.Commission_x = commission.Amount_x * Convert.ToDouble(Rates[0].X);
                    commission.FormulaDisplay_x = $"(销售总额){commission.Amount_x_str.DefaultIfEmpty("0")} * ";
                }
                if (unDeductReturned)
                {
                    if (isFormula_t)
                    {
                        commission.Commission_t = comm_t;
                    }
                    else
                    {
                        commission.Commission_t = commission.Amount_t * Convert.ToDouble(Rates[0].T);
                        commission.FormulaDisplay_t = $"(退货总额){commission.Amount_t_str.DefaultIfEmpty("0")} * ";
                    }
                }
            }
            else if (commissionBy == CommissionBy.profit.ToString())//仅百分比
            {
                flag = CommissionFlag.rate.ToString();
                if (isFormula_x)
                {
                    commission.Commission_x = comm_x;
                }
                else
                {
                    commission.Commission_x = commission.Profi_x * Convert.ToDouble(Rates[0].X);
                    commission.FormulaDisplay_x = $"(销售总利润){commission.Profi_x} * ";
                }
                
                if (unDeductReturned)
                {
                    if (isFormula_t)
                    {
                        commission.Commission_t = comm_t;
                    }
                    else
                    {
                        commission.Commission_t = commission.Profi_t * Convert.ToDouble(Rates[0].T);
                        commission.FormulaDisplay_t = $"(退货总利润){commission.Profi_t} * ";
                    }
                }
            }
            commission.FormulaDisplay_x += $"(提成额度){Rates[0].X}";
            if (isFormula_x) commission.FormulaDisplay_x += $" = {formula_x} ";
            if (unDeductReturned)
            {
                commission.FormulaDisplay_t += $"(提成额度){Rates[0].T}";
                if (isFormula_t) commission.FormulaDisplay_t += $" = {formula_t}";
            }

            if (flag == CommissionFlag.rate.ToString())
            {
                if (!isFormula_x)
                {
                    commission.Commission_x /= 100;
                    commission.FormulaDisplay_x += $"/100";
                }
                if (unDeductReturned&&!isFormula_t)
                {
                    commission.Commission_t /= 100;
                    commission.FormulaDisplay_t += $"/100";
                }
            }
            commission.FormulaDisplay_x += $" = {commission.Commission_x_str.DefaultIfEmpty("0")}";
            if (unDeductReturned)
            {
                commission.FormulaDisplay_t += $" = {commission.Commission_t_str.DefaultIfEmpty("0")}";
            }
        }

        /// <summary>
        /// 变化模式，需要提成选择标准
        /// </summary>
        /// <param name="commission"></param>
        /// <param name="Rates"></param>
        /// <param name="commissionBy"></param>
        /// <param name="flag"></param>
        /// <param name="rateFlag">提成基准</param>
        public static void Compute_change(this CommissionDetail commission, List<CommissionRate> Rates, string commissionBy, string flag, double rateFlag,bool unDeductReturned,out string errMsg)
        {
            errMsg = "";
            if(Rates.Last().To==0|| Rates.Last().To ==-0.001)
            {
                Rates.Last().To = double.MaxValue;
            }
            var times_x = 1.0; var times_t = 1.0;
            var rateFlag_x = rateFlag; var rateFlag_t = rateFlag;
            if (commissionBy == CommissionBy.qty.ToString())//仅金额
            {
                flag = CommissionFlag.amount.ToString();
                times_x = rateFlag_x = commission.Quantity_x;
                commission.FormulaDisplay_x = $"(销售数量){commission.Quantity_x_str} * ";
                if (unDeductReturned)
                {
                    times_t = rateFlag_t = commission.Quantity_t;
                    commission.FormulaDisplay_t = $"(退货数量){commission.Quantity_t_str} * ";
                }
            }
            else if (commissionBy == CommissionBy.price.ToString())//仅金额
            {            
                if(flag == CommissionFlag.amount.ToString())
                {
                    commission.FormulaDisplay_x = $"(销售数量){commission.Quantity_x_str} * ";
                    times_x = commission.Quantity_x;
                    if (unDeductReturned)
                    {
                        times_t = commission.Quantity_t;
                        commission.FormulaDisplay_t = $"(退货数量){commission.Quantity_t_str} * ";
                    }
                }
                else if(flag == CommissionFlag.rate.ToString())
                {
                    commission.FormulaDisplay_x = $"(销售总额){commission.Amount_x_str} * ";
                    times_x = commission.Amount_x;
                    if (unDeductReturned)
                    {
                        times_t = commission.Amount_t;
                        commission.FormulaDisplay_t = $"(退货总额){commission.Amount_t_str} * ";
                    }
                }
                
            }
            else if (commissionBy == CommissionBy.amt.ToString())
            {
                rateFlag_x = commission.Amount_x; rateFlag_t = commission.Amount_t;
                if (flag == CommissionFlag.rate.ToString())
                {
                    times_x = commission.Amount_x; 
                    commission.FormulaDisplay_x = $"(销售总额){commission.Amount_x_str.DefaultIfEmpty("0")} * ";
                    if (unDeductReturned)
                    {
                        times_t = commission.Amount_t;
                        commission.FormulaDisplay_t = $"(退货总额){commission.Amount_t_str.DefaultIfEmpty("0")} * ";
                    }
                }
            }
            else if (commissionBy == CommissionBy.profit.ToString())
            {
                rateFlag_x = commission.Profi_x; rateFlag_t = commission.Profi_t;
                if (flag == CommissionFlag.rate.ToString())
                {
                    times_x = commission.Profi_x; 
                    commission.FormulaDisplay_x = $"(销售总利润){commission.Profi_x} * ";
                    if (unDeductReturned)
                    {
                        times_t = commission.Profi_t;
                        commission.FormulaDisplay_t = $"(退货总利润){commission.Profi_t} * ";
                    }
                }
            }

            double rate_x = 0,rate_t=0;
            var range = Rates.FindRate(rateFlag_x);
            var isFormula = false;
            string fm = "";
            if (range != null)
            {
                if (range.X.IndexOf("%")>0 && !range.X.StartsWith("="))
                {
                    rate_x = Convert.ToDouble(range.X.Replace("%", ""));
                    
                    if (commissionBy==CommissionBy.price.ToString())
                    {
                        rate_x = rate_x / 100;
                        if(flag == CommissionFlag.rate.ToString())
                        {
                            times_x = commission.Amount_x;
                            commission.FormulaDisplay_x = $"(销售总额){commission.Amount_x_str} * ";
                        }
                    }
                }
                else if (range.X.StartsWith("="))
                {
                    if (commissionBy == CommissionBy.price.ToString())
                    {
                        
                        string formula = range.X.Substring(1);
                        string origFormula = formula;

                        formula = formula.Replace("（", "(").Replace("）", ")");
                        formula = formula.Replace("%", "/100");
                        formula = formula.Replace("底价", range.From.ToString()).Replace("售价",rateFlag_x.ToString());
                        System.Data.DataTable dt = new System.Data.DataTable();
                        object result;
                        try
                        {
							result = dt.Compute(formula, "");
						}
                        catch (Exception e)
                        {
                            errMsg = $"公式设置不正确:{origFormula}";
                            return;
                        }
                      
                        if (result != DBNull.Value)
                        {
                            rate_x = Convert.ToDouble(result.ToString());
                            isFormula = true;
                            fm = formula;
                        }
                    }
                }
                else
                    rate_x = Convert.ToDouble(range.X);
            }
            if(!isFormula)
                commission.FormulaDisplay_x += $"(提成额度){rate_x}";
            else
                commission.FormulaDisplay_x += $"(提成额度){fm}";
            if (unDeductReturned)
            {
                range = Rates.FindRate(rateFlag_t);
                if (range != null)
                {
                    if (Rates.FindRate(rateFlag_t).T.IndexOf("%") > 0)
                    {
                        rate_t = Convert.ToDouble(Rates.FindRate(rateFlag_t).T.Replace("%", ""));
                        if (commissionBy == CommissionBy.price.ToString() )
                        {
                            rate_t = rate_t / 100;
                            if (flag == CommissionFlag.rate.ToString())
                            {
                                times_t = commission.Amount_t;
                                commission.FormulaDisplay_t = $"(退货总额){commission.Amount_t_str} * ";
                            }
                        }
                    }
                    else if (Rates.FindRate(rateFlag_t).T.StartsWith("="))
                    {
                        if (commissionBy == CommissionBy.price.ToString())
                        {

                            string formula = Rates.FindRate(rateFlag_t).T.Substring(1);
                            string origFormula = formula;

							formula = formula.Replace("（", "(").Replace("）", ")");
                            formula = formula.Replace("%", "/100");
                            formula = formula.Replace("底价", range.From.ToString()).Replace("售价", rateFlag_x.ToString());
                            System.Data.DataTable dt = new System.Data.DataTable();
							object result;
							try
							{
								result = dt.Compute(formula, "");
							}
							catch (Exception e)
							{
								errMsg = $"公式设置不正确:{origFormula}";
								return;
							}
							if (result != DBNull.Value)
                            {
                                rate_t = Convert.ToDouble(result.ToString());
                                isFormula = true;
                                fm = formula;
                            }
                        }
                    }
                    else
                        rate_t = Convert.ToDouble(Rates.FindRate(rateFlag_t).T);
                }
                if (!isFormula)
                    commission.FormulaDisplay_t += $"(提成额度){rate_t}";
                else
                    commission.FormulaDisplay_t += $"(提成额度){fm}";
            }

            if (flag == CommissionFlag.rate.ToString())
            {
                rate_x /= 100; 
                commission.FormulaDisplay_x += $"/100";
                if (unDeductReturned)
                {
                    rate_t /= 100;
                    commission.FormulaDisplay_t += $"/100";
                }
            }
            commission.Commission_x = times_x * rate_x; 
            commission.FormulaDisplay_x += $" = {commission.Commission_x_str.DefaultIfEmpty("0")}";
            if (unDeductReturned)
            {
                commission.Commission_t = times_t * rate_t;
                commission.FormulaDisplay_t += $" = {commission.Commission_t_str.DefaultIfEmpty("0")}";
            }
        }

        /// <summary>
        /// 分段变化模式
        /// </summary>
        /// <param name="commission"></param>
        /// <param name="Rates"></param>
        /// <param name="commissionBy"></param>
        /// <param name="flag"></param>
        public static void Compute_range(this CommissionDetail commission, List<CommissionRate> Rates, string commissionBy, string flag,bool unDeductReturned)
        {
            Rates.Last().To = double.MaxValue;
            if (commissionBy == CommissionBy.qty.ToString())//仅金额
            {
                flag = CommissionFlag.amount.ToString();
                Rates.ForEach(rate =>
                {
                    if (commission.Quantity_x > rate.From)
                    {
                        var qty = commission.Quantity_x > rate.To ? rate.To - rate.From : commission.Quantity_x - rate.From;
                        commission.Commission_x += qty * Convert.ToDouble(rate.X);
                        commission.FormulaDisplay_x += $"+ (区间数量){qty} * (区间提成额度){rate.X} ";
                    }
                    if (unDeductReturned)
                    {
                        if (commission.Quantity_t > rate.From)
                        {
                            var qty = commission.Quantity_t > rate.To ? rate.To - rate.From : commission.Quantity_t - rate.From;
                            commission.Commission_t += qty * Convert.ToDouble(rate.T);
                            commission.FormulaDisplay_t += $"+ (区间数量){qty} * (区间提成额度){rate.T} ";
                        }
                    }
                });
            }
            else if (commissionBy == CommissionBy.price.ToString())
            {
                /*不支持此模式*/
            }
            else if (commissionBy == CommissionBy.amt.ToString())
            {
                Rates.ForEach(rate =>
                {
                    if (commission.Amount_x > rate.From)
                    {
                        var amt = commission.Amount_x > rate.To ? rate.To - rate.From : commission.Amount_x - rate.From;
                        commission.Commission_x += amt * Convert.ToDouble(rate.X);
                        commission.FormulaDisplay_x += $"+ (区间销售总额){amt} * (区间提成额度){rate.X} ";
                    }
                    if (unDeductReturned)
                    {
                        if (commission.Amount_t > rate.From)
                        {
                            var amt = commission.Amount_t > rate.To ? rate.To - rate.From : commission.Amount_t - rate.From;
                            commission.Commission_t += amt * Convert.ToDouble(rate.T);
                            commission.FormulaDisplay_t += $"+ (区间退货总额){amt} * (区间提成额度){rate.T} ";
                        }
                    }
                });
            }
            else if (commissionBy == CommissionBy.profit.ToString())
            {
                Rates.ForEach(rate =>
                {
                    if (commission.Profi_x > rate.From)
                    {
                        var profi = commission.Profi_x > rate.To ? rate.To - rate.From : commission.Profi_x - rate.From;
                        commission.Commission_x += profi * Convert.ToDouble(rate.X);
                        commission.FormulaDisplay_x += $"+ (区间利润总额){profi} * (区间提成额度){rate.X} ";
                    }
                    if (unDeductReturned)
                    {
                        if (commission.Profi_t > rate.From)
                        {
                            var profi = commission.Profi_t > rate.To ? rate.To - rate.From : commission.Profi_t - rate.From;
                            commission.Commission_t += profi * Convert.ToDouble(rate.T);
                            commission.FormulaDisplay_t += $"+ (区间利润总额){profi} * (区间提成额度){rate.T} ";
                        }
                    }
                });
            }
            if (flag == CommissionFlag.rate.ToString())
            {
                if (commission.FormulaDisplay_x != null && commission.FormulaDisplay_x.StartsWith("+"))
                {
                    commission.FormulaDisplay_x = commission.FormulaDisplay_x.Substring(1);
                }
                if (commission.FormulaDisplay_t != null&&commission.FormulaDisplay_t.StartsWith("+"))
                {
                    commission.FormulaDisplay_t = commission.FormulaDisplay_t.Substring(1);
                }
                commission.Commission_x /= 100;
                commission.FormulaDisplay_x = $"({commission.FormulaDisplay_x})/100";
                if (unDeductReturned)
                {
                    commission.Commission_t /= 100;
                    commission.FormulaDisplay_t = $"({commission.FormulaDisplay_t})/100";
                }
            }

            commission.FormulaDisplay_x += $" = {commission.Commission_x_str.DefaultIfEmpty("0")}";
            
            if (unDeductReturned)
            {
                commission.FormulaDisplay_t += $" = {commission.Commission_t_str.DefaultIfEmpty("0")}";
                
            }

        }


        /// <summary>
        /// 仅适用于变化模式
        /// </summary>
        /// <param name="rates"></param>
        /// <param name="rateFlag"></param>
        /// <returns></returns>
        public static CommissionRate FindRate(this List<CommissionRate> rates,double rateFlag)
        {
            return rates.FirstOrDefault(rate => rate.From <= rateFlag && rateFlag < rate.To);
        }

        /// <summary>
        /// 将同一组提成明细汇总
        /// </summary>
        /// <param name="detail">保存汇总结果的容器</param>
        /// <param name="details">需要进行汇总的一组明细数据</param>
        /// <returns></returns>
        public static CommissionDetail Sum(this CommissionDetail detail, IEnumerable<CommissionDetail> details)
        {
            details.Aggregate(detail, (a, b) =>
            {
                a.Amount_t += b.Amount_t;
                a.Amount_t_arrears += b.Amount_t_arrears;
                a.Amount_t_from_arrears += b.Amount_t_from_arrears;
                a.Amount_x += b.Amount_x;
                a.Amount_x_arrears += b.Amount_x_arrears;
                a.Amount_x_from_arrears += b.Amount_x_from_arrears;
                a.Cost_t += b.Cost_t;
                a.Cost_x += b.Cost_x;
                a.Quantity_t += b.Quantity_t;
                a.Quantity_x += b.Quantity_x;
                a.Commission_t += b.Commission_t;
                a.Commission_x += b.Commission_x;
                return a;
            });
            return detail;
        }

        /// <summary>
        /// 按员工分组处理
        /// </summary>
        /// <param name="commissionInfos"></param>
        /// <param name="planMaps"></param>
        /// <param name="itemClasses"></param>
        /// <returns></returns>
        public static List<KeyValuePair<CommissionDetail, List<CommissionDetail>>> GroupAndSumByEmployee(this List<CommissionInfo> commissionInfos, List<CommissionInfo> getArrearItems, List<PlanWithMap> planMaps, List<ItemClass> itemClasses =null)
        {
            commissionInfos.AddRange(getArrearItems);
            var groups = commissionInfos.GroupBy(info => $"{info.oper_id},{info.oper_type}");
            //bool bSellItemsEmpty = false;
            //if (groups.Count() == 0)
            //{
            //    bSellItemsEmpty = true;
            //    if (getArrearItems!=null && getArrearItems.Count > 0)
            //    {
            //        groups = getArrearItems.GroupBy(info => $"{info.oper_id},{info.oper_type}");
            //    }
            //}
            
            var list = groups.Select((infos, i) =>
            {
                
                var id_type = infos.Key.Split(',');
                var planMap = planMaps.FirstOrDefault(v => v.EmployeeId.ToString() == id_type[0] && v.Type == id_type[1]);//找到该员工的方案
                if (planMap == null) return new KeyValuePair<CommissionDetail, List<CommissionDetail>>(null, null);
                 
                var planContent = planMap.PlanContent; 
                if (itemClasses != null)
                {
                    foreach (var target in planContent.targets)
                    {
                        if (target.Type == "c")
                        {
                            var cls = itemClasses.Find(x => x.Id == target.Target);
                            if (cls != null) target.TargetName = cls.Name;
                        }
                    }
                }

                List<CommissionInfo> lstInfos = new List<CommissionInfo>();
                var lstInfosOrig = infos.ToList();
 
                foreach (var item in lstInfosOrig)
                {
                    if (item.FromGetArrears)
                    {
                       // string key = infos.Key;
                       // var keyLs = key.Split(",");
                       // var sellerID = Convert.ToSingle(keyLs[0]);
                       // var operType = keyLs[1];
                        //if (item.oper_id == sellerID && item.oper_type == operType)
                        //{
                        var ruleAndTarget = PlanContent.GetTargetFromItem(planContent.targets, item);
                        if (ruleAndTarget != null)
                        {
                            ruleAndTarget.CommissionRule = planContent.rules.Find(x => x.Name == ruleAndTarget.Rule);
                            // ruleAndTarget.CommissionRule = planContent.rules.First(x => x.Name == ruleAndTarget.Rule);
                            if (ruleAndTarget.CommissionRule != null && ruleAndTarget.CommissionRule.DeductArrearages)
                            {
                                lstInfos.Add(item);
                            }
                        }
                        //}
                    }
                    else
                    {
                        lstInfos.Add(item);
                    }
                }
                  
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Info("in GroupAndSumByEmployee1:");
                var res= planContent.MapTargetsAndSum(lstInfos, new CommissionDetail(planMap.Type, planMap.EmployeeName, planMap.EmployeeId), planMap);
            
                return res.data;
            });



            // var l=list.ToList();
            NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
            var res = new List<KeyValuePair<CommissionDetail, List<CommissionDetail>>>();
          
            var res1 = list.Where(x => x.Key != null);
            logger.Info("in GroupAndSumByEmployee2:");

            res = res1.ToList();     
           
            logger.Info("in GroupAndSumByEmployee3:");
            return res;// list.Where(x => x.Key != null);
        }

        public static List<KeyValuePair<CommissionDetail, List<CommissionDetail>>> newGroupAndSumByEmployee(this List<CommissionInfo> commissionInfos, List<CommissionInfo> getArrearItems, List<PlanWithMap> planMaps, out string errMsg, List<ItemClass> itemClasses = null)
        {
            errMsg = "";
            commissionInfos.AddRange(getArrearItems);
            var groups = commissionInfos.GroupBy(info => $"{info.plan_id},{info.oper_type}");
            string err = "";
            var list = groups.Select((infos, i) =>
            {

                var id_type = infos.Key.Split(',');
                var planMap = planMaps.FirstOrDefault(v => v.Id.ToString() == id_type[0] && v.Type == id_type[1]);//找到该员工的方案
                if (planMap == null) return new KeyValuePair<CommissionDetail, List<CommissionDetail>>(null, null);
                string workerName = "";
                int workerId = -1;
                var planContent = planMap.PlanContent;
                if (itemClasses != null)
                {
                    foreach (var target in planContent.targets)
                    {
                        if (target.Type == "c")
                        {
                            var cls = itemClasses.Find(x => x.Id == target.Target);
                            if (cls != null) target.TargetName = cls.Name;
                        }
                    }
                }

                List<CommissionInfo> lstInfos = new List<CommissionInfo>();
                var lstInfosOrig = infos.ToList();

                foreach (var item in lstInfosOrig)
                {
                    if (workerName == "")
                    {
                        workerName = item.oper_name;
                        workerId = item.oper_id;
                    }

                    if (item.FromGetArrears)
                    {
                        var ruleAndTarget = PlanContent.GetTargetFromItem(planContent.targets, item);
                        if (ruleAndTarget != null)
                        {
                            ruleAndTarget.CommissionRule = planContent.rules.Find(x => x.Name == ruleAndTarget.Rule);
                            if (ruleAndTarget.CommissionRule != null && ruleAndTarget.CommissionRule.DeductArrearages)
                            {
                                lstInfos.Add(item);
                            }
                        }
                    }
                    else
                    {
                        lstInfos.Add(item);
                    }
                }

                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Info("in GroupAndSumByEmployee1:");
                
                var res = planContent.MapTargetsAndSum(lstInfos, new CommissionDetail(planMap.Type, workerName, workerId), planMap);
                err = res.msg;
                return res.data;
            });

        
            // var l=list.ToList();
            NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
            var res = new List<KeyValuePair<CommissionDetail, List<CommissionDetail>>>();

            var res1 = list.Where(x => x.Key != null);
		 

			logger.Info("in GroupAndSumByEmployee2:");
            var sellerResults = new List<CommissionDetail>();
            var sellerSumResult = new CommissionDetail();
            var senderResults = new List<CommissionDetail>();
            var senderSumResult = new CommissionDetail();
            foreach (var rs1 in res1)
            {
                if (rs1.Key.OperType == "seller")
                {
                    if(sellerSumResult.Id==0) sellerSumResult.Id = rs1.Key.Id;
                    if (sellerSumResult.Name == null) sellerSumResult.Name = rs1.Key.Name;
                    if (sellerSumResult.OperType == null) sellerSumResult.OperType = rs1.Key.OperType;
                    foreach (var val in rs1.Value)
                    {
                        sellerResults.Add(val);
                    }
                }else if(rs1.Key.OperType == "sender")
                {
                    if (senderSumResult.Id == 0) senderSumResult.Id = rs1.Key.Id;
                    if (senderSumResult.Name == null) senderSumResult.Name = rs1.Key.Name;
                    if (senderSumResult.OperType == null) senderSumResult.OperType = rs1.Key.OperType;
                    foreach (var val in rs1.Value)
                    {
                        senderResults.Add(val);
                    }

                }
                
            }

			if (err != "")
			{
				errMsg = err;
				return null;
			}
			sellerSumResult.Sum(sellerResults);
            senderSumResult.Sum(senderResults);
            if(sellerResults.Count() > 0)res.Add(new KeyValuePair<CommissionDetail, List<CommissionDetail>>(sellerSumResult, sellerResults));
            if (senderResults.Count() > 0) res.Add(new KeyValuePair<CommissionDetail, List<CommissionDetail>>(senderSumResult, senderResults));
            //var res2 = new KeyValuePair<CommissionDetail, List<CommissionDetail>>(sumResult, results);
            //res = res1.ToList();

            logger.Info("in GroupAndSumByEmployee3:");
            return res;// list.Where(x => x.Key != null);
        }

        
        public static List<KeyValuePair<CommissionDetail, List<CommissionDetail>>> GroupAndSumByEmployee_old(this List<CommissionInfo> commissionInfos, List<CommissionInfo> getArrearItems, List<PlanWithMap> planMaps, List<ItemClass> itemClasses = null)
        {
            var groups = commissionInfos.GroupBy(info => $"{info.oper_id},{info.oper_type}");

            bool bSellItemsEmpty = false;
            if (groups.Count() == 0)
            {
                bSellItemsEmpty = true;
                if (getArrearItems != null && getArrearItems.Count > 0)
                {
                    groups = getArrearItems.GroupBy(info => $"{info.oper_id},{info.oper_type}");
                }
            }

            var list = groups.Select((infos, i) =>
            {
                var lstInfos = infos.ToList();
                var id_type = infos.Key.Split(',');
                var planMap = planMaps.FirstOrDefault(v => v.EmployeeId.ToString() == id_type[0] && v.Type == id_type[1]);//找到该员工的方案
                if (planMap == null) return new KeyValuePair<CommissionDetail, List<CommissionDetail>>(null, null);

                var planContent = planMap.PlanContent;
                if (itemClasses != null)
                {
                    foreach (var target in planContent.targets)
                    {
                        if (target.Type == "c")
                        {
                            var cls = itemClasses.Find(x => x.Id == target.Target);
                            if (cls != null) target.TargetName = cls.Name;
                        }
                    }
                }

                if (!bSellItemsEmpty && getArrearItems != null)
                {
                    var lstExistItems = infos.ToList();
                    foreach (var item in getArrearItems)
                    {
                        string key = infos.Key;
                        var keyLs = key.Split(",");
                        var sellerID = Convert.ToSingle(keyLs[0]);
                        var operType = keyLs[1];
                        if (item.oper_id == sellerID && item.oper_type == operType)
                        {
                            var ruleAndTarget = PlanContent.GetTargetFromItem(planContent.targets, item);
                            if (ruleAndTarget != null)
                            {
                                ruleAndTarget.CommissionRule = planContent.rules.Find(x => x.Name == ruleAndTarget.Rule);
                                // ruleAndTarget.CommissionRule = planContent.rules.First(x => x.Name == ruleAndTarget.Rule);
                                if (ruleAndTarget.CommissionRule != null && ruleAndTarget.CommissionRule.DeductArrearages)
                                {
                                    lstInfos.Add(item);
                                }
                            }
                        }
                    }
                }
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Info("in GroupAndSumByEmployee1:");
                var res = planContent.MapTargetsAndSum(lstInfos, new CommissionDetail(planMap.Type, planMap.EmployeeName, planMap.EmployeeId), planMap);
                return res.data;

            });
            // var l=list.ToList();
            NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
            var res = new List<KeyValuePair<CommissionDetail, List<CommissionDetail>>>();

            var res1 = list.Where(x => x.Key != null);
            logger.Info("in GroupAndSumByEmployee2:");

            res = res1.ToList();

            logger.Info("in GroupAndSumByEmployee3:");
            return res;// list.Where(x => x.Key != null);
        }

    }
}
