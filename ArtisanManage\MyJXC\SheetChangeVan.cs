﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowChangeVan:SheetRowBase
    {
        public SheetRowChangeVan()
        {

        }
        [SaveToDB(false)][FromFld(false)] public override int inout_flag { get; set; } = 0;
        [SaveToDB(false)][FromFld(false)] public override int row_index { get; set; } = 0;

        [FromFld("op_id")]public override string sheet_id { get; set; }
        [SaveToDB][FromFld] public string item_id { get; set; }

        [FromFld("ip.item_name")]public string item_name { get; set; }
        [SaveToDB][FromFld] public string unit_no { get; set; }

        [SaveToDB][FromFld] public decimal unit_factor { get; set; } = 1;

        [SaveToDB][FromFld] public decimal quantity { get; set; }

        [SaveToDB][FromFld] public decimal sheet_order_quantity { get; set; }
        [SaveToDB][FromFld] public string batch_id { get; set; } = "0";
        [SaveToDB][FromFld] public string branch_position { get; set; } = "0";
        [SaveToDB][FromFld] public string branch_id { get; set; }
        public string from_branch_position { get; set; } = "0";
        public string to_branch_position { get; set; } = "0";
        [FromFld(LOAD_PURPOSE.SHOW)] public string batch_no { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string produce_date { get; set; } = "";
        [SaveToDB][FromFld] public string sale_order_sheet_id { get; set; } = "";

    }
    public class SheetChangeVan : SheetBase<SheetRowChangeVan>
    {
        [SaveToDB("op_id")][IDField][FromFld("op_id")] public override string sheet_id { get; set; }
        [SaveToDB("op_no")][FromFld("case when t.op_no is  null then 'ZC'||t.op_id::text else t.op_no end")] public override string sheet_no { get; set; } = "";
        [SaveToDB][FromFld] public string op_type { get; set; }
        [SaveToDB][FromFld] public string oper_id { get; set; }
        [FromFld("op.oper_name")] public string oper_name { get; set; }
        [SaveToDB][FromFld] public string move_sheet_id { get; set; }
        [SaveToDB][FromFld] public string senders_id { get; set; }
        [SaveToDB][FromFld] public string senders_name { get; set; }
        [SaveToDB(false)][FromFld(false)] public override string red_sheet_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string maker_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_time { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string approve_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string submit_time { get; set; } = "";

        [SaveToDB(false)][FromFld(false)] public override string maker_name { get; set; } = "";
        [SaveToDB(false)][FromFld("case when od.retreats_id is null then null else true end")] public string have_retreat { get; set; } = "";
        [FromFld("od.no_retreat_order_sheet_id")] public string no_retreat_order_sheet_id { get; set; } = "";

        [FromFld("otd.sale_order_sheets_id")] public string sale_order_sheets_id { get; set; }
        public string sale_order_sheets_no { get; set; }
        [FromFld("sm.sheet_no")] public string move_sheet_no { get; set; }
        [SaveToDB][FromFld] public string from_branch { get; set; }
        [FromFld("v.branch_name")] public string to_van_name { get; set; }
        [SaveToDB][FromFld] public string to_van { get; set; }
        [FromFld("f.branch_name")] public string from_branch_name { get; set; }

        [SaveToDB][FromFld("case when t.approver_id is not null then t.approver_id else sm.approver_id end")] public override string approver_id { get; set; } = "";
        [FromFld("case when t.approver_id is not null then  approver.approver_name else mover.approver_name end")] public override string approver_name { get; set; } = "";
        [SaveToDB][FromFld("to_char( case when t.approve_time is not null then t.approve_time else sm.approve_time end , 'yyyy-MM-dd hh24:mi:ss' ) as approve_time")] public override string approve_time { get; set; } = "";

        [SaveToDB][FromFld("case when t.move_stock is not null then t.move_stock::text else otd.move_stock::text end")] public string move_stock { get; set; }
        public string sheetsInfo { get; set; } = "";
        public string newSheetsInfo { get; set; } = "";
        public string move_sheet_ids { get; set; } = "";
        public string order_status { get; set; }
        public SheetChangeVan(LOAD_PURPOSE loadPurpose) : base("op_move_to_van_main", "op_move_to_van_row", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_CHANGE_VAN;
            MainLeftJoin = @" 
                    LEFT JOIN info_branch f ON T.from_branch = f.branch_id AND f.company_id = ~COMPANY_ID
	                LEFT JOIN info_branch v ON T.to_van = v.branch_id AND v.company_id = ~COMPANY_ID
                    LEFT JOIN ( SELECT oper_id, oper_name  FROM info_operator WHERE company_id = ~COMPANY_ID ) op on op.oper_id = t.oper_id
	                LEFT JOIN ( SELECT oper_id, oper_name AS approver_name FROM info_operator WHERE company_id = ~COMPANY_ID ) approver ON T.approver_id = approver.oper_id 
	                LEFT JOIN 
                    (
                            SELECT DISTINCT op_id , string_agg(DISTINCT so.move_stock::text,',') move_stock,string_agg(sale_order_sheet_id::text,',') sale_order_sheets_id 
                            FROM op_move_to_van_detail d 
		                    LEFT JOIN sheet_status_order so on so.company_id = d.company_id and d.sale_order_sheet_id = so.sheet_id
	                        WHERE d.company_id = ~COMPANY_ID GROUP BY op_id
                    ) otd on otd.op_id = t.op_id
	                LEFT JOIN sheet_move_main sm on sm.company_id = ~COMPANY_ID and sm.sheet_id = t.move_sheet_id
                    LEFT JOIN (  SELECT op_id, string_agg(retreat_id::text,',') retreats_id,string_agg(case when retreat_id is null then sale_order_sheet_id::text else null end,','  ) no_retreat_order_sheet_id FROM op_move_to_van_detail WHERE company_id = ~COMPANY_ID  GROUP BY op_id) od on od.op_id = t.op_id
	                LEFT JOIN ( SELECT oper_id, oper_name AS approver_name FROM info_operator WHERE company_id = ~COMPANY_ID ) mover ON sm.approver_id = mover.oper_id 
                ";
            DetailLeftJoin = $@" left join info_item_prop ip on ip.company_id= t.company_id and ip.item_id = t.item_id
left join (select batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date from info_item_batch where company_id= ~COMPANY_ID) itb on itb.batch_id = t.batch_id
left join (select from_branch,op_id from op_move_to_van_main where company_id = ~COMPANY_ID) omtvm on omtvm.op_id = t.op_id
left join info_branch ibb on ibb.branch_id =COALESCE(t.branch_id,omtvm.from_branch)
left join info_branch_position ibp on ibp.branch_id =ibb.branch_id and ibp.branch_position = COALESCE(t.branch_position,0)";
        }


 
        class CInfoForApprove : CInfoForApproveBase
        {
            public List<dynamic> PreviousSheets = new List<dynamic>();
            public List<dynamic> FollowingSheets = new List<dynamic>();
        }
        public override string GetOtherSaveSQL()
        {
            string sqlDetail = "";
            if (move_sheet_id == "")
            {
                sqlDetail = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"insert into op_move_to_van_detail (op_id,oper_id,company_id,sale_order_sheet_id)
                           values ('@op_id', '{OperID}', '{company_id}', '{sheetId}');");
                //if (sheet_id != "")
                //{
                //    sqlDetail = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current +
                //            $@"update op_move_to_van_detail set move_sheet_id = {move_sheet_id} where company_id = '{company_id}' and sale_order_sheet_id = '{sheetId}' and op_id = {sheet_id};");
                //}
            }
            else
            {
                sqlDetail = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"insert into op_move_to_van_detail (op_id,oper_id,company_id,move_sheet_id, sale_order_sheet_id,sale_order_row_branch_id)
                           values ('@op_id', '{OperID}', '{company_id}', {move_sheet_id}, '{sheetId}',{from_branch});");
                if (sheet_id != "")
                {
                    sqlDetail = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current +
                            $@"update op_move_to_van_detail set move_sheet_id = {move_sheet_id} where company_id = '{company_id}' and sale_order_sheet_id = '{sheetId}' and op_id = {sheet_id};");
                }
            }
            
            return sqlDetail;
        }
        //public override string GetSaveSQL(bool bForApproveOrRed, out string err)
        //{
        //    err = "";
        //    string sql = base.GetSaveSQL(bForApproveOrRed, out err);
        //    string sqlOrderStatus = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"
        //                               insert into sheet_status_order 
        //            (company_id,    sheet_id, order_status,  senders_id,                 senders_name,                     van_id, sheet_print_count, sum_print_count, open_stock_print_count,move_stock) 
        //    values ('{company_id}', '{sheetId}', 'pzc', '{senders_id}', '{senders_name}', {to_van}, '0',                 '0',             '0',                {move_stock})
        //    on conflict(sheet_id) do update set order_status = 'pzc', senders_id = '{senders_id}', senders_name = '{senders_name}', van_id = {to_van},move_stock={move_stock};");
        //    sql = sql + sqlOrderStatus;
        //    return sql;
        //}
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            string sql = "";
            if (sale_order_sheets_id != "")
            {
                sql = $@"SELECT * FROM (
                                SELECT om.op_id,om.op_type,om.company_id,om.happen_time,om.move_sheet_id,om.senders_id,om.senders_name,om.red_flag,om.from_branch,om.to_van,case when om.op_no is null then om.happen_time else om.approve_time end approve_time , om.move_stock,om.op_no,sm.sheet_id sale_order_sheet_id,sm.sheet_no sale_order_sheet_no,sm.red_flag sale_order_sheet_status,dd.sheet_id deliver_sheet_id,row_number() over(partition by od.sale_order_sheet_id order by om.happen_time desc) rn 
                                FROM op_move_to_van_detail od
                                LEFT JOIN op_move_to_van_main om  on od.company_id = od.company_id and od.op_id = om.op_id 
                                LEFT JOIN sheet_sale_order_main sm on od.company_id = sm.company_id and od.sale_order_sheet_id = sm.sheet_id 
                                LEFT JOIN sheet_delivery_detail dd on dd.company_id = od.company_id and dd.op_id = od.op_id
                                WHERE od.company_id = {company_id} and od.sale_order_sheet_id in ({sale_order_sheets_id}) and om.red_flag is null and om.happen_time<'{happen_time}'
                                    )t
                    WHERE t.rn =1";
                QQ.Enqueue("previous_sheets", sql);
                sql = $@"SELECT * FROM (
                                SELECT om.*,sm.sheet_no sale_order_sheet_no,sm.red_flag sale_order_sheet_status,dd.sheet_id deliver_sheet_id,row_number() over(partition by od.sale_order_sheet_id order by om.happen_time ) rn FROM op_move_to_van_detail od
                                LEFT JOIN op_move_to_van_main om  on od.company_id = od.company_id and od.op_id = om.op_id 
                                LEFT JOIN sheet_sale_order_main sm on od.company_id = sm.company_id and od.sale_order_sheet_id = sm.sheet_id 
                                LEFT JOIN sheet_delivery_detail dd on dd.company_id = od.company_id and dd.op_id = od.op_id
                                WHERE od.company_id = {company_id} and od.sale_order_sheet_id in ({sale_order_sheets_id}) and om.red_flag is null and om.happen_time>'{happen_time}'
                                    )t
                     WHERE t.rn =1";
                QQ.Enqueue("following_sheets", sql);

            }
            
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (sqlName == "previous_sheets")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                if (records.Count > 0)
                {
                    foreach (dynamic rec in records)
                    {
                        info.PreviousSheets.Add(rec);
                        if (rec.approve_time == "")
                        {
                            if (rec.op_type == "2v")
                            {
                                info.ErrMsg += $"包含{rec.sale_order_sheet_no}的装车单未审核,请先审核";
                            }else if (rec.op_type == "v2v")
                            {
                                info.ErrMsg += $"包含{rec.sale_order_sheet_no}的换车单未审核,请先审核";
                            }
                            
                        }
                        if(rec.deliver_sheet_id!="") info.ErrMsg += $"该订单{rec.sale_order_sheet_no}已发货,无法换车";
                    }
                }
                
            }else if(sqlName == "following_sheets")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                if (records.Count > 0)
                {

                    foreach (dynamic rec in records)
                    {
                        info.FollowingSheets.Add(rec);
                    }
                }
            }
            
        }
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
            return "OK";
        }
        protected override async Task<CInfoForApproveBase> GetInfoForApprove(CMySbCommand cmd)
        {

            SQLQueue QQ = new SQLQueue(cmd);
            if (sheet_id != "")
            {
                if (!FIXING_ARREARS)
                {
                    string check_sql = $"select approve_time from {MainTable} where op_id={sheet_id} and company_id = {company_id}";
                    QQ.Enqueue("check_sheet", check_sql);
                }
            }

            GetInfoForApprove_SetQQ(QQ);
            string errMsg = "";
            if (QQ.Count > 0)
            {
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                try
                {
                    while (QQ.Count > 0)
                    {
                        string tbl = QQ.Dequeue();
                        if (tbl == "check_sheet")
                        {
                            dynamic checkSheet = CDbDealer.Get1RecordFromDr(dr, false);
                            if (checkSheet != null && checkSheet.approve_time != "")
                            {
                                errMsg = "单据已审核过,不能再次审核";
                                break;
                            }
                        }
                        else
                        {
                            GetInfoForApprove_ReadData(dr, tbl,false);
                            if (InfoForApprove != null && InfoForApprove.ErrMsg != "")
                            {
                                errMsg = InfoForApprove.ErrMsg;
                                break;
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    errMsg = "读取数据失败";
                    MyLogger.LogMsg($"in GetInfoForApprove. error:${e.Message}, ${e.StackTrace}", company_id, "approve");
                }
                QQ.Clear();
            }
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            InfoForApprove.ErrMsg = errMsg;
            return InfoForApprove;
        }
        //protected override string GetApproveSQL(CInfoForApproveBase info)
        //{
            //string sqlOrderStock = "";
            //foreach (var row in SheetRows)
            //{
            //    var order_qty = row.sheet_order_quantity * row.unit_factor;
            //    sqlOrderStock += $@"update stock set sell_pend_qty=sell_pend_qty-({order_qty}) where company_id={company_id} and branch_id={from_branch} and item_id={row.item_id};";
            //}

        //    string sqlOrderStatus = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"
        //                           insert into sheet_status_order 
        //        (company_id,    sheet_id, order_status,  senders_id,                 senders_name,                     van_id, sheet_print_count, sum_print_count, open_stock_print_count,move_stock) 
        //values ('{company_id}', '{sheetId}', 'zc', '{senders_id}', '{senders_name}', {to_van}, '0',                 '0',             '0',                {move_stock})
        //on conflict(sheet_id) do update set order_status = 'zc', senders_id = '{senders_id}', senders_name = '{senders_name}', van_id = {to_van},move_stock={move_stock};");
            //string sqlSetting = $"update company_setting set setting=setting::jsonb || '{{\"orderMoveStock\":{move_stock.ToLower()} }}'::jsonb where company_id={company_id} and (setting->>'orderMoveStock'<>'{move_stock}' or setting->'orderMoveStock' is null);";
            //string sql =  sqlOrderStatus;
            //string sql = sqlOrderStock + sqlOrderStatus;
        //    return sql;
        //}
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info)
        {

            string sqlOrderStatus = "";
            CInfoForApprove info1 = (CInfoForApprove)info;
            string updatePreviousSheetSql = "";
            if (red_flag.IsInvalid())
            {
                sqlOrderStatus = sale_order_sheets_id.Split(',').Aggregate("", (current, sheetId) => current + $@"
                                   insert into sheet_status_order 
                (company_id,    sheet_id, order_status,  senders_id,                 senders_name,                     van_id, sheet_print_count, sum_print_count, open_stock_print_count,move_stock,assign_van_id) 
        values ('{company_id}', '{sheetId}', 'zc', '{senders_id}', '{senders_name}', {to_van}, '0',                 '0',             '0',                {move_stock},'{sheetID}')
        on conflict(sheet_id) do update set order_status = 'zc', senders_id = '{senders_id}', senders_name = '{senders_name}', van_id = {to_van},move_stock={move_stock},assign_van_id='{sheetID}';");


                if (info1.PreviousSheets.Count > 0)
                {
                    foreach (var sht in info1.PreviousSheets)
                    {
                        updatePreviousSheetSql += $@"update op_move_to_van_detail set change_van_id = {sheetID} where company_id ={company_id} and op_id ={sht.op_id} and sale_order_sheet_id ={sht.sale_order_sheet_id};";
                        SheetSaleOrder orderSheet = new SheetSaleOrder(LOAD_PURPOSE.SHOW);
                        await orderSheet.Load(cmd, company_id, sht.sale_order_sheet_id);
                        foreach (var row in orderSheet.SheetRows)
                        {
                            string rowBranchId = row.branch_id.IsValid() ? row.branch_id : orderSheet.branch_id;
                            string rowBranchPosition = row.branch_position.IsValid() ? row.branch_position : "0";
                            string rowBatchId = row.batch_id.IsValid() ? row.batch_id : "0";
                            updatePreviousSheetSql += $@"update op_move_to_van_row set change_van_qty = COALESCE(change_van_qty,0)+{row.quantity} where company_id = {company_id} and item_id = {row.item_id} and unit_factor = {row.unit_factor} and branch_id={rowBranchId} and branch_position = {rowBranchPosition} and batch_id = {rowBatchId} and op_id = {sht.op_id} ; ";
                        }


                    }
                }
            }
            
            cmd.CommandText = sqlOrderStatus+ updatePreviousSheetSql;
            if (cmd.CommandText != "")
            {
                await cmd.ExecuteNonQueryAsync();
            }
        }
      
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.from_branch + "_" + this.to_van + "_" + this.move_stock;
            foreach (var row in SheetRows)
            {
                res += row.item_id + "_" + row.quantity;
            }
            return res;
        }
		protected override async Task<string> BeforeRed(CMySbCommand cmd, string sheetID, string rederID, string redBrief, CInfoForApproveBase info)
		{

			SQLQueue QQ = new SQLQueue(cmd);
            string otherInfosql = "";
            string err = "";
            if (sale_order_sheets_id != "")
            {
                otherInfosql = $@"SELECT * FROM (
                                SELECT om.op_id,om.op_type,om.company_id,om.happen_time,om.move_sheet_id,om.senders_id,om.senders_name,om.red_flag,om.from_branch,om.to_van,case when om.op_no is null then om.happen_time else om.approve_time end approve_time , om.move_stock,om.op_no,od.sale_order_sheet_id,sm.sheet_no sale_order_sheet_no,sm.red_flag sale_order_sheet_status,row_number() over(partition by od.sale_order_sheet_id order by om.happen_time desc) rn 
                                FROM op_move_to_van_detail od
                                LEFT JOIN op_move_to_van_main om  on od.company_id = od.company_id and od.op_id = om.op_id 
                                LEFT JOIN sheet_sale_order_main sm on od.company_id = sm.company_id and od.sale_order_sheet_id = sm.sheet_id 
                                WHERE od.company_id = {company_id} and od.sale_order_sheet_id in ({sale_order_sheets_id}) and om.red_flag is null and om.happen_time<'{happen_time}'
                                    )t
                    WHERE t.rn =1";
                QQ.Enqueue("previous_sheets", otherInfosql);

                otherInfosql = $@"SELECT * FROM (
                                SELECT om.*,od.sale_order_sheet_id,sm.sheet_no sale_order_sheet_no,sm.red_flag sale_order_sheet_status,row_number() over(partition by od.sale_order_sheet_id order by om.happen_time ) rn FROM op_move_to_van_detail od
                                LEFT JOIN op_move_to_van_main om  on od.company_id = od.company_id and od.op_id = om.op_id 
                                LEFT JOIN sheet_sale_order_main sm on od.company_id = sm.company_id and od.sale_order_sheet_id = sm.sheet_id 
                                WHERE od.company_id = {company_id} and od.sale_order_sheet_id in ({sale_order_sheets_id}) and om.red_flag is null and om.happen_time>'{happen_time}'
                                    )t
                     WHERE t.rn =1";
                QQ.Enqueue("following_sheets", otherInfosql);

            }
            string checkBeforeRed = @$"
select m.op_type, m.move_sheet_id,sale_order_sheet_id,m.red_flag,mm.red_flag move_flag,sm.sheet_id sale_sheet_id,om.sheet_no order_sheet_no,sm.sheet_no sale_sheet_no,so.receipt_status,m.happen_time,so.order_status,dm.sheet_id delivery_id,so.assign_van_id,ovm.op_type assign_type
from op_move_to_van_detail d
left join op_move_to_van_main m on d.company_id=m.company_id and d.op_id=m.op_id
left join sheet_sale_order_main om on d.company_id=om.company_id and d.sale_order_sheet_id=om.sheet_id
left join sheet_sale_main sm on om.company_id=sm.company_id and om.sheet_id=sm.order_sheet_id and sm.approve_time is not null and sm.red_flag is null
left join sheet_status_order so on d.company_id=so.company_id and d.sale_order_sheet_id=so.sheet_id
LEFT JOIN op_move_to_van_main ovm on ovm.company_id = so.company_id and ovm.op_id = so.assign_van_id 
LEFT JOIN sheet_move_main mm on m.company_id = mm.company_id and m.move_sheet_id=mm.sheet_id
LEFT JOIN sheet_delivery_detail dd on dd.company_id = d.company_id and dd.op_id = d.op_id
LEFT JOIN sheet_delivery_main dm on dm.company_id = d.company_id and dm.sheet_id = dd.sheet_id and dm.red_flag is null 
where d.company_id={this.company_id} and d.op_id={red_sheet_id};";
            QQ.Enqueue("check_for_red", checkBeforeRed);
            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> previousSheets = null;
            List<ExpandoObject> followingSheets = null;

            while(QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if(sqlName== "previous_sheets")
                {
                    previousSheets = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if(sqlName== "following_sheets")
                {
                    followingSheets = CDbDealer.GetRecordsFromDr(dr, false);
                }else if(sqlName == "check_for_red")
                {
                    List<ExpandoObject> lstOrderSheet = CDbDealer.GetRecordsFromDr(dr, false);
                    foreach (dynamic sht in lstOrderSheet)
                    {
                        move_sheet_id = sht.move_sheet_id;
                        op_type = sht.op_type;
                        happen_time = sht.happen_time;
                        string sale_sheet_id = sht.sale_sheet_id;
                        string receipt_status = sht.receipt_status;
                        if (sale_sheet_id != "")
                        {
                            err = $"订单{sht.order_sheet_no}已转单,无法红冲";
                            break;
                        }
                        if (sht.assign_van_id != red_sheet_id)
                        {
                            if (sht.assign_van_id == "" && op_type != "retreat") err = $"装车单{sheet_no}中订单{sht.order_sheet_no}已经回撤,请先红冲回撤单";
                            if (sht.assign_type == "v2v") err = $"装车单{sheet_no}中订单{sht.order_sheet_no}已经换车,无法红冲";
                            if (sht.assign_type == "2v") err = $"装车单{sheet_no}中订单{sht.order_sheet_no}已经重新装车,无法红冲";
                            if (err != "") break;
                        }
                        if (sht.move_flag != "")
                        {
                            err = "调拨单已红冲，无法撤销装车";
                            break;
                        }
                    }
                }
            }
            QQ.Clear();
         
            if (followingSheets.Count>0) err = "订单已再次换车,请先撤销后面的换车单";
            string sql = "";
            if(err ==""&& move_stock == "true"&&move_sheet_id!="")
            {
                SheetMove moveSheet = new SheetMove(LOAD_PURPOSE.APPROVE);
                moveSheet.isRedAndChange = isRedAndChange;
                err = await moveSheet.Red(cmd, this.company_id, move_sheet_id, rederID, redBrief, false);

            }
            //CInfoForApprove info =(CInfoForApprove) await GetInfoForApprove(cmd);
            if (err == "")
            {  
               // sql += $"update op_move_to_van_main set red_flag='1' where company_id={this.company_id} and op_id={sheet_id};";
                foreach (dynamic pst in previousSheets)
                {
                    sql += $"update sheet_status_order set  senders_id  = {pst.senders_id} , senders_name = '{pst.senders_name}' , van_id = {pst.to_van} , move_stock = {pst.move_stock},assign_van_id ={pst.op_id} where company_id={this.company_id} and sheet_id = {pst.sale_order_sheet_id};";
                    sql += @$"update op_move_to_van_detail set change_van_id =null where company_id ={this.company_id} and op_id = {pst.op_id} and sale_order_sheet_id ={pst.sale_order_sheet_id}; ";
                    SheetSaleOrder orderSheet = new SheetSaleOrder(LOAD_PURPOSE.SHOW);
                    await orderSheet.Load(cmd, company_id, pst.sale_order_sheet_id);
                    foreach (var row in orderSheet.SheetRows)
                    {
                        string rowBranchId = row.branch_id.IsValid() ? row.branch_id : orderSheet.branch_id;
                        string rowBranchPosition = row.branch_position.IsValid() ? row.branch_position : "0";
                        string rowBatchId = row.batch_id.IsValid() ? row.batch_id : "0";
                        sql += $@"update op_move_to_van_row set change_van_qty =COALESCE(change_van_qty,0)- ({row.quantity}) where company_id = {this.company_id} and op_id ={pst.op_id} and item_id = {row.item_id} and unit_factor = {row.unit_factor} and branch_id = {rowBranchId} and branch_position = {rowBranchPosition} and batch_id = {rowBatchId};";
                    }
                }
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
               
            }
             
            return err;
        }
        
        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd,  CInfoForApproveBase info)
        {
            string err = "";
            var sheetMove = new SheetMove(LOAD_PURPOSE.SHOW);
            if (move_stock.ToLower() == "true")
            {
                sheetMove.from_branch_id = from_branch;
                sheetMove.to_branch_id = to_van;
                sheetMove.SheetRows = JsonConvert.DeserializeObject<List<SheetRowMove>>(JsonConvert.SerializeObject(SheetRows));
                sheetMove.company_id = company_id;
                sheetMove.happen_time = CPubVars.GetDateText(DateTime.Now);
                sheetMove.maker_id = OperID;
                sheetMove.make_time = CPubVars.GetDateText(DateTime.Now);
                sheetMove.submit_time = CPubVars.GetDateText(DateTime.Now);
                sheetMove.OperKey = OperKey;
                sheetMove.OperID = OperID;
                sheetMove.SaleOrderSheetIDs = sale_order_sheets_id;
                sheetMove.SaleOrderSheetNos = sale_order_sheets_no;
                sheetMove.assign_van = Assign_Van.ASSIGN_VAN;
                sheetMove.sheet_id = move_sheet_id == "null" ? "" : move_sheet_id;
                sheetMove.sheet_no = move_sheet_no == "" ? "" : move_sheet_no;
                err = await sheetMove.SaveAndApprove(cmd, false);
                if (err == "")
                {
                    move_sheet_id = sheetMove.sheet_id;
                    move_sheet_no = sheetMove.sheet_no;
                }
            }
            return err;
        }

        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting); 
            
        }

    }
}
