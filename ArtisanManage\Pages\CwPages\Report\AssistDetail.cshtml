﻿@page
@model ArtisanManage.Pages.CwPages.Report.AssistDetailModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>DetailAccount</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css?v=@Html.Raw(Model.Version)" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.js?v=@Html.Raw(Model.Version)"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxwindow.js"></script>

    <script src="~/js/FileSaverVue.js"></script>
    <script src="~/js/Blob.js"></script>
    <script src="~/js/jszip.js"></script>
    <script src="~/js/xlsx.full.min.js"></script>
    <script src="~/js/xlsx-style.js"></script>
    <script src="~/js/Export2Excel.js?v=@Html.Raw(Model.Version)"></script>

    <style>
        * {
            font-family: "微软雅黑"
        }
        [v-cloak] {
            display: none;
        }

        body {
            overflow:hidden;
        }

        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;

        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #dddddd;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }

        #pages {
            width: 100%;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            /*background-color: #f9f9f9;*/
        }

        .pages_title {
            width: 100%;
            display:block;
            font-weight: 500;
            font-size: 25px;
            text-align: center;
            margin-top: 5px;
            padding-bottom:10px;
        }

        .pages_query {
            width: 100%;
            height:5vh;
            display: block;
            padding-left: 20px;
            padding-right: 20px;
            flex-wrap: wrap;
            padding-bottom: 10px;
        }

        .query_item {
            display: inline-block;
            float:left;
            height: 100%;
            width:88%;
            margin-top: 3px;
        }

        .item_input {
            width: 149px;
            height: 100%;
            z-index: 0;
            position: relative;
            border-style: none none solid;
            border-bottom: 1px solid #c7c7c7;
            border-radius: 0px;
            margin-left: 10px;
        }

        .item_name {
            height: 20px;
            bottom: 1px;
            right: 1px;
            margin-bottom: 0px;
        }

        #datePickerM, #datePickerD{
            display: inline-block;
        }

        .pages_buttons {
            width:10%;
            height: 100%;
            float:right;
            display:inline-block;
            justify-content:end;
            align-items:center;
        }

        .pages_content {
            width:98%;
            height: 85%;
            margin: 10px 1%;
            display:block;
        }

        .level2 {
            text-indent: 1em;
        }
        .level3 {
            text-indent: 2em;
        }


        .pages_content table {
            width: 83%;
            height:100.5%;
            float:right;
            border-collapse: collapse;
            border: 2px solid #ebeef5;
        }

        .pages_content table tbody {
            display: block;
            overflow-y: scroll;
            overflow-x: hidden;
            height: calc(100% - 41px);
        }
        @@media(max-width:1687px) {
            .pages_content table tbody {
                height: calc(100% - 82px);
            }
         }
         @@media(max-width:1159px) {
            .pages_content table tbody {
                height: calc(100% - 162px);
            }
         }

        .pages_content table thead, .pages_content tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
            font-size:14px;
        }

        .pages_content table thead {
            width: calc( 100% - 1em - 1px );
            position:sticky;
            top:0;
        }

        .pages_content table thead th{
            background-color:#c9c9c4;
        }

        .pages_content table thead th:nth-child(1),  .pages_content table tbody td:nth-child(1) {
            width: 8%
        }
        .pages_content table thead th:nth-child(2),  .pages_content table tbody td:nth-child(2) {
            width: 6.5%
        }
        .pages_content table thead th:nth-child(3),  .pages_content table tbody td:nth-child(3) {
            width: 10%
        }
        .pages_content table thead th:nth-child(4),  .pages_content table tbody td:nth-child(4) {
            width: 5%
        }
        .pages_content table thead th:nth-child(8),  .pages_content table tbody td:nth-child(8) {
            min-width: 8%
        }
        .pages_content table tbody td:nth-child(5), .pages_content table tbody td:nth-child(6), .pages_content table tbody td:nth-child(7){
            text-align: right;
        }
        .pages_content table tbody td:nth-child(2), .pages_content table tbody td:nth-child(4){
            text-align: center;
        }
        @*高度*@
        .pages_content table thead th, .pages_content table tbody td {
            min-height: 40px;
            line-height: 40px;
        }
        @*边框*@
        .pages_content table thead th, .pages_content table tbody td {
            border-bottom: 2px solid #fff;
            border-right: 2px solid #fff;
        }
            .pages_content table thead th:last-child {
                border-right: 0;
            }
        @*背景*@
        .pages_content table tbody tr:nth-child(odd) {
            background: #e6eae6;
        }
        .pages_content table tbody tr:nth-child(even) {
            background: #f8fbf8;
        }
        .pages_content table tbody tr:hover {
            background-color: #f6bfbc;
        }

        .pages_content table thead th, .pages_content table tbody td {
            padding: 0 15px;
        }

        .el-date-editor{
            width:200px;
            margin-right:20px;
        }

        .box-card {
            width: 15%;
            height:100%;
            float:left;
        }

        .el-card__body{
            height:100%;
        }

        .filter-tree{
            margin-top:10px;
            overflow-y:scroll;
            overflow-x: auto;
            height:88%;
            display: block;
        }

        ::-webkit-scrollbar-corner{
            background-color:transparent;
        }
        
        .el-date-editor, .el-input{
            width:200px;
            margin-right:20px;
        }

        .sub_select_input{
            width:300px;
        }

        .sub_tree_input{
            width:100%;
        }

        .voucher_a:visited, .voucher_find_sheet_a:visited{
            color:blue;
        }

        .btnMonthOrDate{
            margin-right:20px;
        }

        .elselect_sub  .el-input{
            width:300px;
        }
        @@media(max-width:1433px) {
            .elselect_type .el-input{
                width: 150px;
            }
            .elselect_sub  .el-input {
                width: 230px;
            }
         }
         @@media(max-width:1157px) {
            .elselect_type .el-input{
                width: 8vw
            }
            .elselect_sub  .el-input {
                width: 15vw;
            }
         }

    </style>

</head>
<body>
    <div id="root" v-cloak>
        <div id="pages" class="" ref="pages">
            <div class="pages_title">核算项目明细账</div>
            <div class="pages_query">
                <div class="query_item">
                    <el-dropdown @@command="periodTypeClick">
                        <el-button class="btnMonthOrDate">{{periodType.text}}<i class="el-icon-arrow-down el-icon--right"></i></el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item v-for="type in periodTypeList" :key="type.id" :value="type.id" :command="type.id">{{type.text}}</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <label class="item_name"></label>
                    <div id="datePickerM">
                        <el-date-picker
                            :value="queryDate"
                            type="monthrange"
                            :disabled="disabledDatePicker"
                            range-separator="至"
                            start-placeholder="开始月份"
                            end-placeholder="结束月份"
                            :picker-options="pickerOptions"
                            @@input="changeDate"
                        >
                        </el-date-picker>
                    </div>
                    <div id="datePickerD" style="visibility:hidden;width:0;">
                        <el-date-picker
                            v-model="queryDate"
                            type="daterange"
                            :disabled="disabledDatePicker"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :picker-options="pickerOptions"
                            @@change="changeDate">
                        </el-date-picker>
                    </div>
                    <span>辅助类别：</span>
                    <el-select v-model="typeSelect" placeholder="请选择辅助类别" class="elselect_type" @@change="typeChange">
                        <el-option v-for="type in assisterTypes"  :key="type.id"  :label="type.name" :value="type.id"></el-option>
                    </el-select>
                    <span>科目：</span>
                    <el-select v-model="subSelect" placeholder="请选择科目" class="elselect_sub" @@change="subChange">
                        <el-option v-for="sub in mainSubList"  :key="sub.sub_id"  :label="sub.full_name" :value="sub.sub_id"></el-option>
                    </el-select>
                </div>
                <div class="pages_buttons">
                    <el-button type="info" plain v-on:click="exportBtn()" :disabled="disabledExportBtn" style="position:absolute;right:30px;">导出</el-button>
                </div>
            </div>
            <div class="pages_content">
                <el-card class="box-card">
                    <el-input class="sub_tree_input" placeholder="助记码/名称/编号" v-model="treeFilterText"></el-input>
                    <el-tree
                          class="filter-tree"
                          :data="treeAllSubs"
                          node-key="id"
                          @@node-click="handleTreeNodeClick" 
                          default-expand-all
                          :expand-on-click-node="false"
                          :check-on-click-node="true"
                          :filter-node-method="treeFilterNode"
                          :highlight-current="true"
                          ref="tree">
                    </el-tree>
                </el-card>

                <table>
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>凭证字号</th>
                            <th>摘要</th>
                            <th>方向</th>
                            <th>借方</th>
                            <th>贷方</th>
                            <th>余额</th>
                            <th>业务单据</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="row in dataList" :key="row.rowIndex">
                            <td>{{row.happen_time}}</td>
                            <td><a class="voucher_a" href="#" @@click="openVoucher(row.sheet_id)">{{row.sheet_no}}</a></td>
                            <td>{{row.remark}}</td>
                            <td>{{row.dir_label}}</td>
                            <td>{{row.debit_amount}}</td>
                            <td>{{row.credit_amount}}</td>
                            <td>{{row.balance}}</td>
                            <td ref="biz_id_nos"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>   
    <script>
        var g_operKey = '@Model.OperKey';

        window.g_operRights =@Html.Raw(Model.JsonOperRightsOrig);
        function checkOperRight(vm){
            if(!window.g_operRights.cwReport){
                return false;
            }
            if (window.g_operRights.cwReport.assistDetail && window.g_operRights.cwReport.assistDetail.see) { 
                if(!window.g_operRights.cwReport.assistDetail.export) vm.disabledExportBtn=true;
                return true;
            }else{
                return false;
            }
        }

        function voucherClickSheet(e,biz_sheet_type,biz_sheet_id,biz_sheet_no){
            e.stopPropagation();
            let sheet_name='';
            let url='';
            switch (biz_sheet_type){
                case 'X':
                    sheet_name='销售单';
                    url = `Sheets/SaleSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'D':
                    sheet_name='销售单';
                    url = `Sheets/SaleSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'T':
                    sheet_name='退货单';
                    url = `Sheets/SaleSheet?forReturn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'CG':
                    sheet_name='采购单';
                    url = `Sheets/BuySheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'CT':
                    sheet_name='采购退货单';
                    url = `Sheets/BuySheet?forReturn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'SK':
                    sheet_name='收款单';
                    url = `Sheets/GetArrearsSheet?forPayOrGet=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'FK':
                    sheet_name = '付款单';
                    url = `Sheets/GetArrearsSheet?forPayOrGet=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'YS':
                    sheet_name = '预收款单';
                    url = `Sheets/PrepaySheet?forPayOrGet=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'YF':
                    sheet_name = '预付款单';
                    url = `Sheets/PrepaySheet?forPayOrGet=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'ZC':
                    sheet_name = '费用支出单';
                    url = `Sheets/FeeOutSheet?forOutOrIn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'SR':
                    sheet_name = '其他收入单';
                    url = `Sheets/FeeOutSheet?forOutOrIn=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'YK':
                    sheet_name = '盘点盈亏单';
                    url = `Sheets/InventChangeSheet?forReduce=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'BS':
                    sheet_name = '报损单';
                    url = `Sheets/InventChangeSheet?forReduce=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'DH':
                    sheet_name = '定货会';
                    url = `Sheets/OrderItemSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'DHTZ':
                    sheet_name = '定货会调整单';
                    url = `Sheets/OrderItemAdjustSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'CBTJ':
                    sheet_name = '成本调价单';
                    url = `Sheets/CostPriceAdjustSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'TR':
                    sheet_name = '转账单';
                    url = `CwPages/CashBankTransferSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'RK':
                    sheet_name = '其他入库单';
                    url = `Sheets/StockInOutSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'CK':
                    sheet_name = '其他出库单';
                    url = `Sheets/StockInOutSheet?sheet_id=${biz_sheet_id}`;
                    break;
            }
            window.parent.newTabPage(sheet_name, url, window);
        }
    </script>
    <script>

        var vm = new Vue({
            el: '#root',
            data() {
                return {
                    companyName: '',
                    disabledDatePicker: false,
                    disabledExportBtn: false,

                    dataList:[/*    //演示数据
                        { rowIndex:1, happen_time:'2023-3-12', sub_id:'12345',  sheet_no:'记-1', sheet_id:1, dir_label:'借',remark:'发生费用', debit_amount:1000, credit_amount:200, balance:800, biz_id_nos:[ {sheet_id:123, sheet_no:'X-001'} ] },
                        { rowIndex:2, happen_time:'2023-3-31', sub_id:'12345',  sheet_no:'', sheet_id:0, dir_label:'平',remark:'本期合计', debit_amount:800, credit_amount:800, balance:0, biz_id_nos:[ {sheet_id:124, sheet_no:'X-002'}, {sheet_id:345, sheet_no:'CG011'} ] },
                        { rowIndex:3, happen_time:'2023-3-31', sub_id:'12345',  sheet_no:'', sheet_id:0, dir_label:'平',remark:'本年累计', debit_amount:800, credit_amount:200, balance:0, biz_id_nos:[ {sheet_id:125, sheet_no:'X-003'} ] },
                    */],
                    queryDate: [ this.dateFormat(new Date(),'yyyy-MM-01'), this.dateFormat(new Date(),'yyyy-MM-01') ],
                    dateRange:['2000-01-01','2000-02-01'],
                    pickerOptions:{
                        disabledDate(time) {
                            return time.getTime() > Date.now();
                        }
                    },
                    periodTypeList: [ { id:'m', text:'按月份' }, { id:'d', text:'按日期' } ],
                    periodType: { id:'m', text:'按月份' }, 

                    assisterTypes:[ { id:'C', name: '客户' }, { id: 'S', name: '供应商' }, { id: 'INV', name: '商品' }, { id: 'DEP', name: '部门' }, { id: 'MAN', name: '业务员' } ],
                    typeSelect:'C',

                    mainSubList:[],
                    subSelect: '',

                    treeAllSubs:[],
                    assisterSelect:'',
                    treeFilterText: '',
                }
            },
            created(){
                if(!window.checkOperRight(this)){
                    this.treeAllSubs=[];
                    this.detailSubData=[];
                    this.disabledDatePicker=true;
                    this.disabledExportBtn=true;
                    return;
                }

                this.getAllSubs();
            },
            mounted(){
                this.$refs.tree.setCurrentKey(this.assisterSelect);
            },
            updated(){
                this.$refs.tree.setCurrentKey(this.assisterSelect);
            },
            watch: {
                 treeFilterText(val) {
                    this.$refs.tree.filter(val);
                 }
            },
            methods: {
                getAllSubs(){//mainSubs+assistSubs+data
                    let _this=this;
                    const loading = this.$loading({ lock: true, text: '加载中...', spinner: 'el-icon-loading', background: 'rgba(255, 255, 255, 0.7)' });
                    _this.dataList=[];
                    $.ajax({
                        url: '/api/AssistDetail/GetAllSubs',
                        type: 'get',
                        data: { 
                            operKey: g_operKey, 
                            assistType: _this.typeSelect, 
                            assister_id: _this.assisterSelect, 
                            sub_id: _this.subSelect, 
                            periodType: _this.periodType.id,
                            pickFromTime: _this.queryDate[0],
                            pickToTime: _this.queryDate[1]
                        },
                        contentType: "application/json;charset=UTF-8",
                        error: function (xhr) {
                            loading.close();
                            _this.$message({ showClose: true, message: '网络错误', type: 'error', offset: 20, duration: 2500 });
                        }
                    }).then((res)=> {
                        if (res.result === 'OK') {
                            console.log('assist detail account-get all subs');
                            _this.companyName=res.companyName;
                            if(this.periodType.id=='m'){
                                _this.dateRange=[new Date(res.opTime).setDate(1), new Date(res.edTime)];
                            }else if(this.periodType.id=='d'){
                                _this.dateRange=[new Date(res.opTime), new Date(res.edTime)];
                            }
                            _this.pickerOptions={
                                disabledDate(time) {
                                    return (time.getTime()<_this.dateRange[0] || time.getTime() > _this.dateRange[1]);
                                }
                            };
                            _this.queryDate=[res.pickFromTime_, res.pickToTime_];
                            _this.mainSubList=res.subs_main;
                            _this.subSelect=res.sub_id;

                            if(res.msg!=''){
                                loading.close();
                                _this.$message({  message: res.msg });
                                _this.treeAllSubs=[];
                                return;
                            }

                            if(res.subs_assist.length>0){
                                _this.treeAllSubs=res.subs_assist;
                                if(_this.assisterSelect=='') _this.assisterSelect=_this.treeAllSubs[0].id;
                                
                                _this.dataList=res.data;
                                let index=0;
                                _this.dataList.forEach(row=>{
                                    row.rowIndex=(++index);
                                    row.happen_time=_this.dateFormat(new Date(row.happen_time),'yyyy-MM-dd');
                                    row.debit_amount=row.debit_amount==0?'':row.debit_amount;
                                    row.credit_amount=row.credit_amount==0?'':row.credit_amount;
                                    row.balance=row.balance==0?'':row.balance;
                                });
                                _this.appendBizNos(res.data);
                            }
                            
                            loading.close();
                        }else{
                            loading.close();
                            _this.$message({ type: 'warning', message: res.msg });
                        }
                    });
                },
                changeDate(time){
                    //elementui-datePicker-monthrange-change事件不生效：是原生组件bug
                    //解决方法：monthrange事件改为input
                     if(this.periodType.id=='m'){
                        this.$forceUpdate();
                        $('.el-date-range-picker').each(function(){
                            if($(this).find('.el-month-table').length>0 && !$(this).hasClass('datePickerM_picker')) $(this).addClass('datePickerM_picker');
                        });
                        let fromDate=this.dateFormat(time[0],'yyyy-MM-dd');
                        let toDate=this.dateFormat(time[1],'yyyy-MM-dd');
                        this.queryDate=[ fromDate, toDate ];
                    }else if(this.periodType.id=='d'){
                        $('.el-date-range-picker').each(function(){
                            if($(this).find('.el-date-table').length>0 && !$(this).hasClass('datePickerD_picker')) $(this).addClass('datePickerD_picker');
                        });
                    }
                    this.queryDate=[this.dateFormat(new Date(this.queryDate[0]),'yyyy-MM-dd'),this.dateFormat(new Date(this.queryDate[1]),'yyyy-MM-dd') ];
                    this.getAllSubs();
                },
                getMonthFromText(text){
                    switch(text){
                        case '一月':
                            text='01';
                            break;
                        case '二月':
                            text='02';
                            break;
                        case '三月':
                            text='03';
                            break;
                        case '四月':
                            text='04';
                            break;
                        case '五月':
                            text='05';
                            break;
                        case '六月':
                            text='06';
                            break;
                        case '七月':
                            text='07';
                            break;
                        case '八月':
                            text='08';
                            break;
                        case '九月':
                            text='09';
                            break;
                        case '十月':
                            text='10';
                            break;
                        case '十一月':
                            text='11';
                            break;
                        case '十二月':
                            text='12';
                            break;
                        default:
                            text='01';
                            break;
                    }
                    return text;
                },
                appendBizNos(data){
                    this.$nextTick(()=>{
                        let td_biz=this.$refs.biz_id_nos;
                        td_biz.forEach(td=>{ td.innerHTML=''; });//先清空，再添加
                        for(let i=0;i<data.length;i++){
                            let row=data[i];
                            let biz_id_no_arr = row.biz_id_nos.split(';');
                            let a_biz='';
                            biz_id_no_arr.forEach((id_no)=>{
                                if(id_no!=''){
                                    let id_no_arr = id_no.split(',');
                                    a_biz +=`<a class="voucher_find_sheet_a" href="#" onmouseup="voucherClickSheet(event,'${row.business_sheet_type}','${id_no_arr[0]}','${id_no_arr[1]}')">${id_no_arr[1]}</a><span>,</span><br>`;
                                }
                            });
                            if(a_biz!=''){
                                a_biz=a_biz.slice(0, -14);
                                td_biz[i].innerHTML=a_biz;
                            } 
                        }
                    });
                },
                dateFormat(date, fmt) { 
                    var o = {
                        "M+": date.getMonth() + 1, //月份
                        "d+": date.getDate(), //日
                        "h+": date.getHours(), //小时
                        "m+": date.getMinutes(), //分
                        "s+": date.getSeconds(), //秒
                        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
                        "S": date.getMilliseconds() //毫秒
                    };
                    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
                    for (var k in o)
                        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                    return fmt;
                },
                treeFilterNode(value, data) {
                    if (!value) return true;
                    return data.label.indexOf(value) !== -1  ;
                },
                handleTreeNodeClick(data){
                    this.assisterSelect=data.id;
                    this.getAllSubs();
                },
                openVoucher(sheet_id){
                     window.parent.newTabPage('凭证', `/CwPages/CwVoucher?sheet_id=${sheet_id}`, window);
                },
                exportBtn(){
                    let period=`${this.queryDate[0].substr(0,4)}${this.queryDate[0].substr(5,2)}-${this.queryDate[1].substr(0,4)}${this.queryDate[1].substr(5,2)}`;
                    let _this=this;
                    let mainSub=this.mainSubList.find(row=>row.sub_id==_this.subSelect);
                    let assistSub=this.treeAllSubs.find(row=>row.id==_this.assisterSelect);
                    let data=[
                        ['核算项目明细账'], 
                        [`公司名称：${this.companyName}`,null,`主科目：${mainSub.full_name}`,null, `会计期间：${period}`,null,null,'单位：元'],
                        ['日期', '凭证字号', '摘要', '辅助科目', '方向', '借方', '贷方', '余额'],
                    ];
                    this.dataList.forEach(row=>{
                        let excelRow=[row.happen_time, row.sheet_no, row.remark, assistSub.label, row.dir_label, row.debit_amount, row.credit_amount, row.balance];
                        data.push(excelRow);
                    });
                    let merges=['A1:H1','A2:B2','C2:D2','E2:F2'];
                    let bodyTitleName=[];                    
                    let  specialCellConfig=[
                        { 
                            "type": "s", 
                            "configObj": { 
                                "font": { "sz": 14, "bold": true },
                                "alignment": { "horizontal": "center" }
                            }, 
                            "controlScope": "col", 
                            "scope": [ 1, 1 ] 
                        },
                        { 
                            "type": "s", 
                            "configObj": { 
                                "border": { "top": { "style": "thin" }, "bottom": { "style": "thin" }, "left": { "style": "thin" }, "right": { "style": "thin" } },
                                "font": { "bold": true } 
                            }, 
                            "controlScope": "col", 
                            "scope": [ 3, 3 ] 
                        },
                        { 
                            "type": "s", 
                            "configObj": { "border": { "top": { "style": "thin" }, "bottom": { "style": "thin" }, "left": { "style": "thin" }, "right": { "style": "thin" } } }, 
                            "controlScope": "col", 
                            "scope": [ 4, data.length ] 
                        }, 
                    ];
                    window.webExportExcel(data,`${this.companyName.replace(' ','')}_核算项目明细账[${period}][${mainSub.full_name.replace(' ','')}][${assistSub.label}]`, merges, bodyTitleName, specialCellConfig)
                },
                periodTypeClick(command){
                    this.periodType=this.periodTypeList.find(row=>row.id==command);
                    let pickFromDate=new Date(this.queryDate[0]);
                    let pickToDate=new Date(this.queryDate[1]);
                    let dateRangeOp=new Date(this.dateFormat(new Date(this.dateRange[0]),'yyyy-MM-dd 00:00:00'));
                    let dateRangeEd=new Date(this.dateFormat(new Date(this.dateRange[1]),'yyyy-MM-dd 23:59:59'));
                    switch(command){
                        case 'm':
                            $('#datePickerM').css('visibility','visible');
                            $('#datePickerM').css('width','auto');
                            $('#datePickerD').css('visibility','hidden');
                            $('#datePickerD').css('width','0');
                            //修改日期格式
                            pickFromDate.setDate(1);
                            pickToDate.setDate(1);
                            this.queryDate=[ this.dateFormat(pickFromDate,'yyyy-MM-dd'),this.dateFormat(pickToDate,'yyyy-MM-dd') ];
                            this.getAllSubs();
                            //修改日期范围
                            dateRangeEd=new Date(this.dateRange[1]);
                            dateRangeEd.setDate(1);
                            break;
                        case 'd':
                            $('#datePickerD').css('visibility','visible');
                            $('#datePickerD').css('width','auto');
                            $('#datePickerM').css('visibility','hidden');
                            $('#datePickerM').css('width','0');
                            //修改日期格式
                            pickToDate.setMonth(pickToDate.getMonth()+1);
                            pickToDate.setDate(0);
                            this.queryDate=[this.dateFormat(pickFromDate,'yyyy-MM-dd'),this.dateFormat(pickToDate,'yyyy-MM-dd') ];
                            this.getAllSubs();
                            //修改日期范围
                            dateRangeEd=new Date(this.dateRange[1]);
                            dateRangeEd.setMonth(dateRangeEd.getMonth()+1);
                            dateRangeEd.setDate(0);
                            break;
                        default:
                            break;
                    }
                    this.dateRange[1]=this.dateFormat(new Date(dateRangeEd),'yyyy-MM-dd');
                    this.pickerOptions={
                        disabledDate(time) {
                            return (time.getTime()<new Date(dateRangeOp) || time.getTime() > new Date(dateRangeEd));
                        }
                    }
                },
                typeChange(){
                    this.subSelect="";
                    this.assisterSelect="";
                    this.getAllSubs();
                },
                subChange(){
                    this.assisterSelect="";
                    this.getAllSubs();
                }
            }
        })
    </script>


</body>
</html>