﻿@page
@model ArtisanManage.Pages.CommisionEditModel
@{
    Layout = null;
    var operKey = Request.Query["operKey"];
    
} 
@using ArtisanManage.Enums
 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - ArtisanManage</title>
    <link rel="stylesheet" href="~/css/site.css?v=@Html.Raw(Model.Version)" />
    <link href="~/css/component.css?v=@Html.Raw(Model.Version)" rel="stylesheet" />
    <link href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)" rel="stylesheet" />
    <link href="~/NiceWidgets/NiceWidgets.css?v=@Html.Raw(Model.Version)" rel="stylesheet" />
    <script src="~/NiceWidgets/NiceWidgets.js?v=@Html.Raw(Model.Version)"></script>
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>

    <link rel="stylesheet" href="~/ChosenSelect/chosen.css" />
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/MiniJsLib/jquery.dialog.js"></script>
    <link rel="stylesheet" href="~/MiniJsLib/jquery.dialog.css?v=@Html.Raw(Model.Version)">
   
    <style>
        tr, td {
            height: 35px;
        }
        tfoot{
            display:none;
        }
        .treeNode:before {
            display: inline-block;
            content: '';
            border: 0.25rem solid transparent;
            border-right: 0.25rem solid #999;
            border-bottom: 0.25rem solid #999;
            margin-right: 0.25rem;
            cursor: default;
        }

        .open, .close {
            color: #333;
            font-size: 15px;
        }

        .open:before {
            content: "+";
            font-size: 20px;
        }

        .close:before {
            content: "-";
            font-size: 20px;
        }

        body {
            overflow: hidden;
        }

        tr:hover {
            background: #fff2f2;
        }

        .col2 {
            min-width: 100px;
            width: 20%;
        }

        .col3 {
            min-width: 8rem;
            width: 30%;
        }

        i {
            cursor: pointer;
        }

        li {
            padding: 0.2rem;
        }

        .unActive {
            color: #bbb;
        }

            .unActive svg {
                fill: #aaa
            }

        .grid{
            flex-grow:9;
        }
        .cards1 {
            display: flex;
            flex-flow: column;
            max-height: 720px;
            width: 6rem;
            overflow: hidden auto;
            flex-grow: 1;
            padding-right:1rem;

        }
        .cards {
            display: flex;
            flex-flow: column;
            max-height: 720px;
            width: 13rem;
            overflow: hidden auto;
            flex-grow: 1;
            padding-right:1rem;

        }
        .card {
            position: relative;
            border: 1px solid #cacaca;
            height: 5rem;
            flex:none;
            margin: 0.1rem;
            margin-bottom: 7px;
            cursor: pointer;
        }

            .card h3 {
                /*margin: 2rem auto;*/
                text-align: center;
            }

            .card p {
                font-size: 10px;
            }

        form {
            display: flex;
            flex-flow: column nowrap;
        }

        .content {
            width: 100%;
        }

        .showByMe ~ .showWithSup {
            display: none;
        }

        .showByMe:checked ~ .showWithSup {
            display: block;
            margin: 0 1rem;
        }

        td:first-child {
            width: 2rem;
        }

        .name {
            text-align: left;
        }

            .name > .priority0 {
                text-align: left;
                padding-left: 0.5rem;
            }

            .name > .priority1 {
                text-align: left;
                padding-left: 1.5rem;
            }

            .name > .priority2 {
                text-align: left;
                padding-left: 2.5rem;
            }

            .name > .priority3 {
                text-align: left;
                padding-left: 3.5rem;
            }

            .name > .priority100 {
                padding-left: 4.5rem;
            }

        [draggable=true] {
            cursor: all-scroll;
        }

        .droppable {
            background: #ffd800
        }

        .showOne .showHover {
            display: none;
        }

        .showOne:hover .showHover {
            display: block;
            width: 100%;
            height: 100%;
        }

        .showOne:hover .showDefault {
            display: none;
        }

        .rule p:hover:after {
            display: inline-block;
            content: 'X';
            cursor: pointer;
            margin-left: 6px;
            color: red;
        }

        .noBorder input {
            border: none;
            width: 2rem;
        }

        .x {
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 20px;
            border: none;
            color: #f00;
            background: transparent;
        }

        ::-webkit-input-placeholder {
            color: rgb(227, 227, 227);
        }


        table input {
            font-size: 1rem;
            padding: 2px;
        }

        .btn {
            width: 80px;
            height: 30px;
        }

        #other {
            display: flex;
        }

            #other > label {
                margin-right: 0.5rem;
            }

        #rate label {
            display: flex;
        }
        /*结构控制*/
        [way=amt] [show-if-way ~=amt],
        [way=qty] [show-if-way ~=qty],
        [way=profit] [show-if-way ~=profit],
        [way=price] [show-if-way ~=price],
        [mode=fix] [show-if-mode ~=fix],
        [mode=change] [show-if-mode ~=change],
        [mode=range] [show-if-mode ~=range],
        [flag=amount] [show-if-flag ~=amount],
        [flag=rate] [show-if-flag ~=rate],
        [returned=true] .show-if-returned {
            display: table-row;
        }

        [returned=true] label.show-if-returned {
            display: inline;
        }

        [returned=true] tr .show-if-returned {
            display: table-cell;
        }

        [way=amt] [mode=fix],
        [way=profit] [mode=fix],
        [way=price] [hide-if-way ~=price],
        [way=qty] [hide-if-way ~=qty] {
            display: none;
        }

        * {
            font-size: 15px;
        }

        th, tr, td {
            border: 1px solid #cacaca;
            font-size: 15px;
        }

        td.index {
            color: #ccc;
        }

        .dialog_container {
            width: 750px;
            height: 500px;
        }
    </style>

</head>
<body style="display:flex;flex-direction:column;height:100%;" keycode="33,34">
    <header>
        <label>方案名称:<input validator="required" id="planName" name="name" class="magic-input" style="height:15px;" /></label>
        <label>
            适用岗位:
            <select id="type" class="chosen-select-deselect" style="width:150px;">
                <option value="seller">业务员</option>
                <option value="sender">送货员</option>
                @* <option value="sendhelper">协送员</option> *@
            </select>
        </label>
        <label>
            <button class="magic-button main-button" save="1" >保存</button>
            <button class="magic-button main-button" save="0" >另存为</button>
        </label>
    </header>
 
    <main >
        <div class="cards1">
            <div style="text-align:center;">
                <svg width="30" height="30" show="dialog" style="margin-top:20px" fill="#999">
                    <use xlink:href='/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd' />
                </svg>
            </div>
            <ul class="cards">
            </ul>
        </div>
        
        <div id="grid"></div>
    </main>
    <script type="text/template" id="dialogTmpl">
        <form id="form1" way="@CommissionBy.amt">
            <h3 class="formItem">规则名称:<input name="name" validator="required" class="magic-input"/></h3>
            <div class="commissionWay" style="margin-top:15px;">
                提成依据:
                <label style="margin-left:20px;"><input id="optCommissionByAmt" name="commissionBy" value="@CommissionBy.amt" type="radio" class="magic-radio" checked /><label for="optCommissionByAmt">销售额</label></label>
                <label style="margin-left:20px;"><input id="optCommissionByProfit"  name="commissionBy" value="@CommissionBy.profit" type="radio" class="magic-radio"/><label for="optCommissionByProfit">利润</label></label>
                <label style="margin-left:20px;"><input id="optCommissionByQty" name="commissionBy" value="@CommissionBy.qty" type="radio" class="magic-radio"/><label for="optCommissionByQty">销售数量</label></label>
                <label style="margin-left:20px;"><input id="optCommissionByPrice"  name="commissionBy" value="@CommissionBy.price" type="radio" class="magic-radio"/><label for="optCommissionByPrice">价格</label></label>
            </div>
             <div id="cost" hidden show-if-way="@CommissionBy.profit" style="margin-top:15px;">
                成本核算:
                <label style="margin-left:20px;"><input name="costType" value="@CostType.avgCost" type="radio"  class="magic-radio" id="optCostTypeAvgCost" checked /><label for="optCostTypeAvgCost">加权平均价</label></label>
                <label style="margin-left:20px;"><input name="costType" value="@CostType.buyPrice" type="radio"  class="magic-radio" id="optCostTypeBuyPrice"/><label for="optCostTypeBuyPrice">预设进价</label></label>
            </div>
            <div id="other" class="other" style="margin-top:20px;">
                <label style="margin-right:6px;">核算范围:</label>
                <input id="ckDeductReturned" name="deductReturned" type="checkbox" class="otherT magic-checkbox" /><label style="margin-left:20px;" for="ckDeductReturned">退货扣减提成</label>
                <input id="ckDeductArrearages" name="deductArrearages" type="checkbox" class="magic-checkbox" /><label style="margin-left:20px;" for="ckDeductArrearages">欠款单据不参与提成</label>
               
               <label style="margin-left:20px;" hidden show-if-way="@CommissionBy.qty" >
                  <input id="ckIncludeGift" name="includeGift" type="checkbox" class="magic-checkbox"/>
                  <label for="ckIncludeGift" > 赠品参与提成</label>
               </label>
                
                <label style="margin-left:20px;" hidden show-if-way="@(CommissionBy.qty)  @(CommissionBy.price)"><input id="ckByBigUnit" name="byBigUnit" type="checkbox" class="magic-checkbox"/><label for="ckByBigUnit">按大单位核算</label></label>
                <label style="margin-left:20px;" hidden show-if-way="@CommissionBy.price"><input id="ckGiftAffectAvgPrice" name="giftAffectAvgPrice" type="checkbox" class="magic-checkbox"/><label for="ckGiftAffectAvgPrice">赠品计入均价</label></label>
            </div>
            <div style="margin-top:5px;">
                <div style="margin-top:10px;">计算方式:</div>
                <div style="border-bottom:1px #eee solid;margin-left: 70px;width: 330px;margin-top:-15px;padding-bottom:10px;">
                    <label style="margin-left:20px;" hide-if-way="@(CommissionBy.price)">
                        <input class="countMode magic-radio" name="countMode" value="@CountMode.fix" id="optCountModeFix" type="radio"   checked /><label for="optCountModeFix">固定</label>
                    </label>
                    <label style="margin-left:20px;">
                        <input class="countMode magic-radio" name="countMode" value="@CountMode.change" id="optCountModeChange" type="radio"   /><label for="optCountModeChange">变化</label>
                    </label>
                    <label style="margin-left:20px;" hide-if-way="@(CommissionBy.price)">
                        <input class="countMode magic-radio" name="countMode" value="@CountMode.range" id="optCountModeRange" type="radio"  /><label for="optCountModeRange">分段变化</label>
                    </label>
                </div>
                <div style="margin-left: 70px;width: 340px;margin-top:10px;" id="rateFlag">
                    <label style="margin-left:20px;" hide-if-way="@(CommissionBy.qty)"><input class="flag magic-radio" name="flag" value="@(CommissionFlag.rate)" id="optRate" type="radio" checked/><label for="optRate">按百分比计算</label> </label>
                    <label style="margin-left:20px;" mode="@(CountMode.fix)"><input class="flag  magic-radio" name="flag" value="@(CommissionFlag.amount)" id="optAmount" type="radio"  /><label for="optAmount">指定提成金额</label></label>
                </div>
            </div>
        </form>

        <form id="form2" returned="false" style="margin-top:15px;width:500px;" mode="@(CountMode.fix)" way="@(CommissionBy.amt)" flag="@CommissionFlag.rate">
            <div id="rate" hidden show-if-mode="@(CountMode.fix)">
                <label style="margin-top:10px;">
                    销售提成<span hidden show-if-flag="@CommissionFlag.amount">(元/件)</span><span hidden show-if-flag="@CommissionFlag.rate">(%)</span>:
                    <input name="x" class="magic-input" autocomplete="off" validator="number"/>
                </label>
                <div hidden class="show-if-returned" style="margin-top:10px;">
                    <label>
                        退货扣减<span hidden show-if-flag="@CommissionFlag.amount">(元/件)</span><span hidden show-if-flag="@CommissionFlag.rate">(%)</span>:
                        <input name="t" class="magic-input" autocomplete="off" validator="number" />
                    </label>
                </div>
            </div>
            <table hidden style="width:700px;" show-if-mode="@(CountMode.change) @(CountMode.range)">
                <thead>
                    <tr >
                        <th style="width:300px;">区间</th>
                        <th style="width:250px;">
                            <label style="display:flex;justify-content:center">
                                销售提成 <span hidden show-if-flag="@CommissionFlag.amount"><span hidden show-if-way="@(CommissionBy.qty)  @(CommissionBy.price)"> (元/件)</span><span hidden show-if-way="@(CommissionBy.amt)  @(CommissionBy.profit)"> (元)</span></span><span hidden show-if-flag="@CommissionFlag.rate">(%)</span>
                            </label>
                        </th>
                        <th style="width:180px;" hidden class="show-if-returned">
                            <label style="display:flex;justify-content:center">
                                退货扣减 <span hidden show-if-flag="@CommissionFlag.amount"><span hidden show-if-way="@(CommissionBy.qty)  @(CommissionBy.price)"> (元/件)</span><span hidden show-if-way="@(CommissionBy.amt)  @(CommissionBy.profit)"> (元)</span></span><span hidden show-if-flag="@CommissionFlag.rate">(%)</span>
                            </label>
                        </th>
                        <th style="width:70px;" ></th></tr>
                </thead>
                <tbody class="interval_card"></tbody>
            </table>
        </form>
    </script>
    <script type="text/template" id="intervalTmpl">
        <table returned="false">
            <thead>
                <tr><th>区间</th><th>销售提成</th><th hidden class="show-if-returned">退货扣减</th> <th></th></tr>
            </thead>
            <tbody class="interval"></tbody>
        </table>
    </script>
    <script type="text/template" id="interval">
@*         <tr hidden show-if-way="@(CommissionBy.qty) @(CommissionBy.amt) @(CommissionBy.profit)" >
            <td validator="intervals"><input name="from" style="width:60px;outline:none; border:none;border-bottom:1px solid #bbb;text-align:center"  autocomplete="off" value="${data.from || 0}"/>≤ X ＜
                <input name="to"  style="width:60px;outline:none;border:none;border-bottom:1px solid #bbb;text-align:center"  autocomplete="off" value="${data.to || 0}"/></td>
            <td><input name="x"  style="width:50px;outline:none;border:none;border-bottom:1px solid #bbb;"  autocomplete="off" value="${data.x}" validator="number"/></td>
            <td hidden class="show-if-returned"><input name="t"   value="${data.t}" style="width:50px;outline:none;border:none;border-bottom:1px solid #bbb;"  autocomplete="off" validator="number"/></td>
            <td>
               <svg width="16" height="16" style="cursor:pointer;" class="addRow">
                    <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  />
               </svg>
               <svg width="16" height="16" style="cursor:pointer;margin-left:10px;" class="removeRow">
                    <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#remove'  />
               </svg>
            </td>
        </tr> *@
        <tr >
            <td validator="intervals"><input name="from" style="width:60px;outline:none; border:none;border-bottom:1px solid #bbb;text-align:center"  autocomplete="off" value="${data.from ||""}" validator="intervalTest"/>≤ X ＜
                    <input name="to"  style="width:60px;outline:none;border:none;border-bottom:1px solid #bbb;text-align:center"  autocomplete="off" value="${(!data.to || data.to === -0.001) ? "" : data.to}" validator="intervalTest"/></td>
            <td><input name="x"  style="outline:none;border:none;border-bottom:1px solid #bbb;"  autocomplete="off" value="${data.x}" validator="numberWithPrecent"/></td>
            <td hidden class="show-if-returned"><input name="t"   value="${data.t}" style="outline:none;border:none;border-bottom:1px solid #bbb;"  autocomplete="off" validator="numberWithPrecent"/></td>
            <td>
               <svg width="16" height="16" style="cursor:pointer;" class="addRow">
                    <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  />
               </svg>
               <svg width="16" height="16" style="cursor:pointer;margin-left:10px;" class="removeRow">
                    <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#remove'  />
               </svg>
            </td>
        </tr>
    </script>
    <script type="text/template" id="commissionTh">
        <div hidden show-if-mode="@(CountMode.fix)" class="noBorder" >
            <label>
                销:<input name="x" class="xt magic-input" placeholder="" />
            </label>
            <label hidden class="show-if-returned">
                退:<input name="t" class="xt magic-input" placeholder="" />
            </label>
        </div>
        <a hidden show-if-mode='@(CountMode.change) @(CountMode.range)' class='addInterval'>
           <svg width="16" height="16">
                <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#setting'  />
           </svg>
        </a>
    </script>
           
        <script src="~/js/commission.js?v=@Html.Raw(Model.Version)" asp-append-version="true"></script>
        <script type="text/javascript">
            app = new App('@operKey', {
                setup() {
                    this.addServices(['Grid', 'Dialog', 'Validator', 'QueryBox'])
                        .useKeyboard('keydown', {
                            PageUp(e) {
                                var size = app.pageSize;
                                app.grid.go(-size);
                            },
                            PageDown() {
                                var size = app.pageSize;
                                app.grid.go(size);
                            },
                        });
                },
                pageSize: parseInt($('main').height() / 35) - 2,
                '.': {
                    click: {
                        removeRow(e) {
                            this.closestParent('tr').remove();
                        },
                        addRow(e) {
                            var tr = this.closestParent('tr');
                            var newTr = tr.cloneNode(true);
                            tr.after(newTr);
                            var inputs = $(newTr.cells[0]).find('input');
                            inputs[0].value = inputs[1].value;
                            inputs[1].value = "";
                            $(newTr.cells[1]).find('input').val("");
                            $(newTr.cells[2]).find('input').val("");
                        },
                        btnDelete(e) {
                            var name = this.closestParent('name', '[]').getAttribute('name');
                            app.cards.data.delete(name);
                            this.closestParent('li').remove();
                        },
                        card(e) {
                            app.cards.changing = this;
                            app.dialog.rule = app.cards.getRule(this.lastElementChild.getAttribute('name'));
                            app.dialog.Show(e, this);
                        },
                    }
                },
                '[]': {
                    click: {
                        save(e) {
                           var changedTargets = [...app.store('ruleTargetsChanged')].map(ruler => {
                                return {
                                    target: ruler.target,
                                    type: ruler.type,
                                    rule: ruler.rule,
                                    rates: ruler.rates
                                }
                            }) 
                           debugger
                            changedTargets.forEach((changed) => {
                                var target = app.data.planContent.targets.find(t => t.target === changed.target && t.type===changed.type);
                                if (target) {
                                    Object.assign(target, changed)
                                }
                                else {
                                    app.data.planContent.targets.push(changed)
                                }
                            }) 


                            var plan = {
                                id: app.requestString('id') || 0,
                                name: $('#planName').val(),
                                type: $('#type').val(),
                                content: JSON.stringify({
                                    rules: [...app.cards.data.values()],
                                    targets:app.data.planContent.targets
                                }),
                                operKey: '@operKey'
                            };
                            if (e.prop == 0) {
                                bw.toast('温馨提示：方案名称不可重复，如果保存失败，请检查名称已修改且唯一。', 3000);
                                plan.id = 0;
                            }
                            if (!plan.name) {
                                bw.toast('请填写方案名称');
                                return;
                            }
                            console.log(plan);
                            //return;
                            $.post({ url: '../Api/Commission/SavePlan', contentType: 'text/json' }, JSON.stringify(plan)).then(result => {
                                if (result.result == 'OK') {
                                    bw.toast('保存成功', 2000);
                                    parent.newTabPage('提成方案', '/commission/plan');
                                }
                            });
                        },
                    },
                    change: {
                        validator(e) {
                            app.validator[e.prop].verify(this, e.target);
                        }
                    },
                    dragstart: {
                        draggable(e) {
                            e.originalEvent.dataTransfer.setData("ruleName", this.getAttribute('name'));
                        }
                    },
                },
                '#': {
                    input: {
                        type() {
                            var planType = app.store('planType');
                            if (planType && planType != this.value) {
                                bw.toast('温馨提示：现存方案的适用岗位不可修改。但您可以修改方案名称后另存为一个新方案。', 3000);
                                $('button[save=1]').attr("disabled", "disabled");
                            } else {
                                $('button[save=1]').removeAttr("disabled");
                            }
                        }
                    }
                },
                cards: {
                    $el: $('.cards'),
                    data: new Map(),
                    changing: null,
                    load(rules) {
                        rules.forEach(rule => {
                            this.add(new CommissionRule(rule));
                        });
                        var cardAdd = `<li ><div style="height:300px"></div></li>`;
                        this.$el.append(cardAdd);
                    },
                    add(rule) {
                        if (this.data.has(rule.name)) {
                            bw.toast('名称为: "' + rule.name + '" 的规则已存在', 1500);
                            return false;
                        }
                        this.data.set(rule.name, rule);
                        var card = `<li class='card showOne'>${toCard.call(rule)}</li>`;
                        card = this.$el.prepend(card);
                        return true;
                    },
                    getRule(el) {
                        if (el instanceof Node) el = el.view(app.hook).rule;
                        return this.data.get(el);
                    },
                    displayRates(rates, countMode, deductReturned) {
                        var str = "";
                        rates.forEach(rate => {
                            var s = `销:${rate.x || 0}`;
                            if (deductReturned) s += ` 退${rate.t || 0}`
                            if (countMode === '@(CountMode.fix)') {
                                str += `<p>${s}</p>`;
                            }
                            else {
                                str += `<p>${rate.from}≤X＜${rate.to}:${s}</p>`;
                            }
                        });
                        return str;
                    },
                    update(rule) {
                        this.changing.innerHTML = toCard.call(rule);
                    },
                },
            });

            const display = {
                amt:'销售额',
                qty: '销量',
                profit: '利润',
                price: '价格',
                fix: '固定',
                change: '变化',
                range: '分段变化',
                amount: '金额',
                rate:'百分比',
                avgCost: '加权平均价',
                buyPrice:'预设进价',
            };
            function toCard() {
                var titlte = `<h3 class="showDefault" style="margin-top:25px;">${this.name}</h3>`;
                var body = `<p>${display[this.commissionBy]} * ${display[this.countMode]} * ${display[this.flag]}</p>`
                if (this.deductReturned) body += `<p>退货纳入计算</p>`;
                if (this.deductArrearages) body += `<p>不计欠款单据</p>`;
                if (this.includeGift) body += `<p>赠品纳入计算</p>`;
                if (this.byBigUnit) body += `<p>按大单位核算</p>`;
            if (this.giftAffectAvgPrice) body += `<p>赠品计入均价</p>`;
                if (this.commissionBy == '@(CommissionBy.profit)') body += `<p>成本计算方式:${display[this.costType]}</p>`
                body += app.cards.displayRates(this.rates, this.countMode, this.deductReturned);
                body += `<div style="position:absolute;top:30px;color:#bbb;font-size:12px;width:90%;text-align:right;">拖我到品项</div>`;
                return `${titlte}<div class="content showHover" style="cursor:pointer;font-size:8px;" draggable="true" name="${this.name}">${body}
                    <button  class="x btnDelete">X</button>
                </div>
`;
            };
            function CommissionRule(rule) {
                rule = Object.assign({
                    name:"",
                    commissionBy: '@(CommissionBy.amt)',
                    countMode: '@(CountMode.fix)',
                    costType: '@(CostType.buyPrice)',
                    deductReturned: false,
                    deductArrearages: false,
                    includeGift: false,
                    byBigUnit: false,
                    giftAffectAvgPrice:false,
                    rates: [{from:0,to:"",x:"",t:""}],
                    flag: "@(CommissionFlag.amount)",
                }, rule);
                return rule;
            };
            function RuleAndTarget(data, priority, type = 'c', parent) {
                this.parent = parent;
                this.data = data;
                this.target = data.id;
                this.type = type;
                this.rule = "";
                this.priority = priority;
                this.rates = [];
                this.getRates = () => {
                    if (this.rates.length) return this.rates;
                    if (this.rule) return app.cards.getRule(this.rule).rates;
                    if (this.parent) return this.parent.getRates();
                };
                this.getRuleName = () => {
                    if (this.rule) return this.rule;
                    if (this.parent) return this.parent.getRuleName();
                    return "";
                };
                this.reset = () => {
                    this.rule = "";
                    app.store('ruleTargetsChanged').add(this);
                };
                this.bindRule = (name) => {
                    //if (this.rule === name) return
                    this.rates = [];
                    this.getRates();
                    this.rule = name;
                    this.commit();
                };
                this.merge = (other) => {
                    this.rule = other.rule;
                    this.rates = other.rates;
                    this.commit();
                };
                this.commit = () => {
                    if (this.rule==null)this.rule = this.getRuleName();
                    app.store('ruleTargetsChanged').add(this);
                }
            };
            function flatten(tree, list, priority = 0, parent) {//将树展平
                var ruleAndTarget = new RuleAndTarget(tree, priority, 'c', parent)
                list.push(ruleAndTarget);
            if (tree.items.length) {
                var itemRuleAndTargets = tree.items.map(item => {
                    return new RuleAndTarget(item, 100, 'i', ruleAndTarget);
                })
                itemRuleAndTargets.forEach(itemRuleAndTarget => {
                    list.push(itemRuleAndTarget);
                });
            }
                priority += 1;
                if (tree.subNodes) tree.subNodes.forEach(node => flatten(node, list, priority, ruleAndTarget));
            };
            function loadClassOrItems(classId, afterLoad) {
                var arg = {
                    operKey: app.token.operKey,
                    classId: classId
                };
                $.get('../Api/Commission/GetItemOrClass', arg).then(result => {
                    if (result.result === "OK") afterLoad(result.data)
                });
            };
            app.store('ruleTargetsChanged', new Set());

            app.ready(function () {
                app.useGrid({
                    buildTable(table) {
                        table.child('thead', {
                            cols: ['', '品项', '应用规则', '提成']
                        }).child('tbody', {
                            size: app.pageSize,
                            cols: ['index', 'name', 'rule', 'rates'],
                            render: {
                                index(v, i) {
                                    return  ++i;
                                },
                                name(data) {
                                    var css = 'priority' + data.priority;
                                    if (data.data.subNodes) css += ' treeNode';
                                    //if (data.data.subNodes) css += data.open ? ' close ' : ' open';
                                    else if (data.type === 'c') css += data.open ? ' close ' : ' open';
                                    return `<a  class='${css}' id='${data.data.id}'> ${data.data.name}</a>`;
                                },
                                rule(data, i, cell) {
                                    var mode = '@(CountMode.fix)', returned = false;
                                    var ruleName = data.getRuleName();
                                    if (ruleName) {
                                        var rule = app.cards.getRule(ruleName);
                                        if (rule) {
                                            if (rule.name !== ruleName) {
                                                app.cards.data.set(rule.name, rule);
                                                app.cards.data.delete(ruleName);
                                                ruleName = rule.name;
                                                if (data.rule) data.rule = ruleName;
                                            }
                                            mode = rule.countMode;
                                            returned = rule.deductReturned;
                                        } else {
                                            data.rule = "";
                                        }
                                    }
                                    cell.nextElementSibling.setAttribute("mode", mode);
                                    cell.nextElementSibling.setAttribute("returned", returned);
                                    return data.rule ? `<p class='removeRule'>${data.rule}</span>` : `<span style="color:#bbb">${ruleName}</span>`;
                                },
                                rates() {
                                    return "";
                                }
                            }
                        });
                    },
                    ".": {
                        click: {
                            name() {//在name列监听单击事件
                                var ruler = this.view(app.hook);
                                var dataIndex = this.getKey();
                            },
                            removeRule() {
                                var ruler_target = this.view(app.hook);
                                if (!ruler_target.rule) return;
                                jConfirm(`确认要取消规则:"${ruler_target.rule}"吗？`, () => {
                                    ruler_target.reset();
                                    app.grid.table.refresh(this.getKey('rowIndex'), this.getKey());
                                }) 
                            },
                            open() {
                                var ruler = this.view(app.hook);
                                var dataIndex = this.getKey();
                                if (!ruler.subNodes) loadClassOrItems(ruler.target, items => {
                                    var newRows = items.map(item => {
                                        return new RuleAndTarget(item, 100, 'i', ruler);
                                    });
                                    /*合并数据*/
                                    var itemAndRules = app.store('itemAndRules');
                                    if (itemAndRules && itemAndRules.length) {
                                        itemAndRules = itemAndRules.filter(oldTarget => {
                                            var t = newRows.find(newTarget => newTarget.target === oldTarget.target && newTarget.type === oldTarget.type);
                                            if (!t) return true;
                                            t.merge(oldTarget);
                                            return false;
                                        });
                                        app.store('itemAndRules', itemAndRules);
                                    };

                                    ruler.subNodes = newRows;
                                    ruler.open = true;
                                    app.grid.table.dataRows.splice(dataIndex + 1, 0, ...newRows);
                                    app.grid.resetScrollBar();
                                    app.grid.table.refresh(this.getKey('rowIndex'), dataIndex);

                                });
                                else {
                                    var newRows = ruler.subNodes;
                                    ruler.open = true;
                                    app.grid.table.dataRows.splice(dataIndex + 1, 0, ...newRows);
                                    app.grid.resetScrollBar();
                                    app.grid.table.refresh(this.getKey('rowIndex'), dataIndex);
                                }
                            },
                            close() {
                                var dataIndex = this.getKey();
                                var ruler = app.view(this);
                                ruler.open = false;
                                app.grid.table.dataRows.splice(dataIndex + 1, ruler.subNodes.length);
                                app.grid.resetScrollBar();
                                app.grid.table.refresh(this.getKey('rowIndex'), dataIndex);
                            },
                        },
                        input: {
                            xt() {
                                //if (Number(this.value) > -1) {
                                //    return true;
                                //}
                                //else {
                                //    bw.toast("请输入正数", 1500);
                                //    app.grid.table.refresh(this.getKey('rowIndex'), this.getKey());
                                //}
                                return true;
                            },
                            noBorder(e) {
                                var rt = app.view(this);
                                if (rt.rates.length) {
                                    var rate = rt.rates[0];
                                    rate[e.target.name] = e.target.value;
                                    if (!rate.x && !rate.t) {
                                        this.closestParent('td').classList.add('unActive')
                                        rt.rates = [];
                                    } else {
                                        this.closestParent('td').classList.remove('unActive');
                                        app.grid.table.refresh(this.getKey('rowIndex') + 1, this.getKey() + 1);
                                        rt.commit();
                                    }
                                }
                                else {
                                    var rate = { x: 0, t: 0 };
                                    rate[e.target.name] = e.target.value;
                                    if (!rate.x && !rate.t) {
                                        this.closestParent('td').classList.add('unActive')
                                        rt.rates = [];
                                    } else {
                                        rt.rates.push(rate);
                                        this.closestParent('td').classList.remove('unActive');
                                        app.grid.table.refresh(this.getKey('rowIndex') + 1, this.getKey() + 1);
                                        rt.commit();
                                    }
                                }
                            }
                        },
                        drop: {
                            'name, .rule'(e) {/**有些人非要往规则列拖，故改成此代码*/
                                this.classList.remove('droppable');
                                var ruleName = e.originalEvent.dataTransfer.getData('ruleName');
                                var ruler_target = app.view(this);
                                ruler_target.bindRule(ruleName);
                                app.grid.table.refresh(this.getKey('rowIndex'), this.getKey());
                            },
                        },
                        dragover: {
                            'name, .rule'(e) {/**理由见上*/
                                e.preventDefault();
                                this.classList.add('droppable');
                            },
                        },
                        dragleave: {
                            'name, .rule'(e) {/**理由见上*/
                                this.classList.remove('droppable');
                            },
                        },
                        afterFill: {
                            rates(e) {
                                var r_t = e.data;
                                if (!r_t.getRuleName()) return;
                                app.link(this, '#commissionTh');
                                if (r_t.rates.length) {
                                    var rate = r_t.rates[0];
                                    $(this).find('input').each((i, x) => {
                                        x.value = rate[x.name] || "";
                                    });
                                    this.classList.remove('unActive');
                                } else {
                                    var rates = r_t.getRates();
                                    if (rates && rates.length) {
                                        var rate = rates[0];
                                        $(this).find('input').each((i, x) => {
                                            x.placeholder = rate[x.name];
                                        });
                                        this.classList.add('unActive');
                                    }
                                }
                            }
                        }
                    },
                });
                app.useValidator({
                    intervals: {
                        verify(td, target) {
                        if (target.name == "to") {
                            if (target.value - target.previousElementSibling.value > 0) td.removeAttribute('verification-tip');
                            else td.setAttribute('verification-tip', this.tip1);
                        }
                        else {
                            var tr = td.closestParent('tr').previousElementSibling;
                            if (!tr) return;
                            if (tr.cells[0].children[1].value - target.value > 0) td.setAttribute('verification-tip', this.tip2);
                            else td.removeAttribute('verification-tip');
                        }
                    },
                    tip1: '区间结束必须大于区间开始',
                    tip2: '区间开始不得小于上一区间的结束',                       
                        }
                }).watch();

                /**页面初始化 */
                /*首先加载全部商品类别*/
                debugger
                loadClassOrItems(0, classTree => {
                    /*然后将树展平、缓存*/
                    if (classTree) flatten(classTree, app.grid.table.dataRows);
                    app.storeGrid(app.grid.table.dataRows);
                    /*再然后渲染*/
                    /*重组数据*/
                    var planId = app.requestString("id");
                    if (planId) {
                        var plan;
                        $.get(`../Api/Commission/GetPlan?id=${planId}`, app.token).then(result => {
                            if (result.result === 'OK') {
                                plan = result.data[0];
                                $('#planName').val(plan.name);
                                $('#type').val(plan.type);
                                var content = JSON.parse(plan.content);
                                /*初始化card*/
                                debugger
                                app.data.planContent = content || {targets:[]}
                                app.cards.load(content.rules);
                                var itemAndRules = content.targets.filter(oldTarget => {
                                    var t = app.grid.table.dataRows.find(newTarget => newTarget.target === oldTarget.target && newTarget.type === oldTarget.type);
                                    if (!t) return true;
                                    t.merge(oldTarget);
                                    return false;
                                });
                                app.store('planType', plan.type);/*缓存备用*/
                                app.store('itemAndRules', itemAndRules);/*缓存备用*/
                                $('.chosen-select-deselect').chosen({ allow_single_deselect: true });
                                app.grid.resetScrollBar(1);
                            } else {
                                bw.toast(result.msg, 3000);
                            }
                        });
                    } else {
                        app.grid.resetScrollBar(1);
                        $('.chosen-select-deselect').chosen({ allow_single_deselect: true });
                        app.data.planContent =  { targets: [] }
                    }
                });

                app.useDialog({
                    id:'dialog',
                    title: '提成规则',
                    okTitle: '保存',
                    ".": {
                        click: {
                            commissionWay(e) {
                                console.log(11)
                                if (e.target instanceof HTMLInputElement) {
                                    var ele = app.dialog.$el.find("[way]").attr('way', e.target.value);
                                    if (e.target.value == 'price') $('#optCountModeChange').click();
                                    else {
                                        var flag = $('#rateFlag>label:visible');
                                        if (flag.length === 1) $(flag[0]).find('input').click();
                                    }
                                }
                            },
                            countMode(e) {
                                console.log(22, e.target.value);
                                var ele = app.dialog.$el.find('[mode]').attr('mode', this.value);
                                var flag = $('#rateFlag>label:visible');
                                if (flag.length === 1) {
                                    $(flag[0]).find('input').click();
                                }
                            },
                            flag(e) {
                                console.log(33, e.target.value);
                                var ele = app.dialog.$el.find('[flag]').attr('flag', this.value);
                            },
                            otherT(e) {
                                var ele = app.dialog.$el.find("[returned]")
                                ele.attr('returned', this.checked);
                            },
                            reLink(e) {
                                console.log(app.dialog.rates);
                                app.dialog.link(app.dialog.rates);
                            },
                        }
                    },
                    afterShow() {
                        var rule = this.rule;
                        var rates = [{
                            from: 0,
                            to: '',
                            x: '',
                            t: ''
                        }];
                        if (rule) {
                            this.$el.find(`.flag[value=${rule.flag}]`).click();
                            this.$el.find('h3 input').val(rule.name);
                            this.$el.find(`#other input`).each((i, x) => {
                                x.checked = !rule[x.name];
                                $(x).click();
                            });
                            this.$el.find(`#cost [value=${rule.costType}]`).click();
                            this.$el.find(`.countMode[value=${rule.countMode}]`).click();
                            this.$el.find(`.commissionWay [value=${rule.commissionBy}]`).click();
                            if (rule.countMode === '@(CountMode.fix)') {
                                this.$el.find('#rate input').each((i, x) => {
                                    x.value = rule.rates[0][x.name];
                                });
                            }
                            rates = rule.rates;
                        }
                        app.dialog.link(rates);
                    },
                    link(rates) {
                        if (this.rule) rates = this.rule.rates;
                        if (rates.length === 0) rates.push({ from: 0, to: "", x: "", t: "" });
                        app.link('.interval_card', '#interval', content => {
                            content = "`" + content + "`";
                            var trs = rates.map(data => {
                                return eval(content);
                            });
                            return trs.join( '');
                        });
                    },
                    okEvent(dialog) {
                        if (!app.validator.verify('#dialog')) return;
                        var data = this.rule || new CommissionRule();
                    if (!this.$el.find('#optCountModeFix').prop('checked')) {
                        var previousTo = "";
                        var pass = true;
                        dialog.$el.find('#form2 tr:not(:first)').each(function (index, element) {
                            var fromInput = $(this).find('input[name="from"]');
                            var toInput = $(this).find('input[name="to"]');
                            var fromTd = fromInput.closest('td');
                            var toTd = toInput.closest('td');
                            if (index != dialog.$el.find('#form2 tr:not(:first)').length - 1) {
                                if (fromInput.val() == "") {
                                    fromTd.attr('verification-tip', '不能为空');
                                    pass = false;
                                }
                                if (toInput.val() == "") {
                                    toTd.attr('verification-tip', '不能为空');
                                    pass = false;
                                }
                            }
                            else {
                                if (fromInput.val() == "") {
                                    fromTd.attr('verification-tip', '最大区间左部分不能为空');
                                    pass = false;
                                }
                            }

                            if (previousTo != "" && parseInt(fromInput.val()) - parseInt(previousTo) < 0) 
                            {
                                fromTd.attr('verification-tip', '下一区间开始必须大于等于上一区间结束');
                                pass = false;
                            } 
                            else if (parseInt(toInput.val()) - parseInt(fromInput.val()) < 0) 
                            {
                                toTd.attr('verification-tip', '区间结束必须大于区间开始');
                                pass = false;
                            } 
                            
                            previousTo = toInput.val();
                        });
                        if (!pass) return;
                    }
                    
                        data.rates = [];
                        this.$el.find('#form1 :input').each(function () {
                            //console.log(this);
                            var bVisible = false;
                            if (this.type == 'checkbox' || this.type == 'radio') {
                                var forLabel = $(`label[for=${this.id}]`)
                                if (forLabel.length && forLabel[0].offsetWidth > 0) bVisible=true
                            }
                            else
                                bVisible = this.offsetWidth > 0
                            if (bVisible) {
                                if (this.type == 'checkbox') data[this.name] = this.checked;

                                else if (this.type == 'text' || this.checked) data[this.name] = this.value;
                            }
                            if (this.type == 'checkbox') data[this.name] = this.checked;
                            else if (this.type == 'text' || this.checked) data[this.name] = this.value;
                        });
                        if (data.countMode == "@(CountMode.fix)") {
                            var d = {};
                            dialog.$el.find('#rate :input').each(function () {
                                d[this.name] = this.value ||-0.001;
                                console.log(this, d);
                            });
                            data.rates.push(d);
                        } else {
                            dialog.$el.find('#form2 tr:not(:first)').each(function () {
                                var d = {};
                                $(this).find("input").each(function () {
                                d[this.name] = this.value || -0.001;
                                    console.log(this,d);
                                });
                                data.rates.push(d);
                            });
                        };
                        console.log(data.rates);

                        if (this.rule) {
                            app.cards.update(data);
                            this.Hide()
                        }
                        else if (app.cards.add(data)) this.Hide();
                    },
                    beforeHide() {
                        app.grid.scrollBar.reset();
                        this.rule = null;
                    },
                });
                app.useDialog({
                    id: "intervalModel",
                    title: '提成区间',
                    tmpl: "intervalTmpl",
                    caller: '.addInterval',
                    ".": {
                        click: {
                            reLink(e) {
                                app.intervalModel.link();
                            },
                        }
                    },
                    beforeShow(e, caller) {
                        this.ruleTarget = app.view(caller);
                        this.link();
                        var ruleName = this.ruleTarget.getRuleName();
                        if (ruleName) {
                            var rule = app.cards.getRule(ruleName);
                            window.selectedRule = rule
                            app.intervalModel.$el.find("[returned]").attr('returned', rule.deductReturned);
                        }
                    },
                    link(rates) {
                        rates = rates || this.ruleTarget.getRates();
                        app.link('.interval', '#interval', content => {
                            content = "`" + content + "`";
                            var trs = rates.map(data => {
                                return eval(content);
                            });
                            return trs.join('');
                        });
                    },
                    okEvent(dialog) {
                        var rates = [];
                        var lastRateTo = "";
                        var pass = true;
                        if (!app.validator.verify('#intervalModel')) return;
                        dialog.$el.find('tr:visible:not(:first)').each(function (index, element) {
                            var d = {};
                        var fromInput = $(this).find('input[name="from"]');
                        var toInput = $(this).find('input[name="to"]');
                        var fromTd = fromInput.closest('td');
                        var toTd = toInput.closest('td');
                        if (index != dialog.$el.find('tr:visible:not(:first)').length - 1) {
                            if (fromInput.val() == "") {
                                fromTd.attr('verification-tip', '不能为空');
                                pass = false;
                            }
                            if (toInput.val() == "") {
                                toTd.attr('verification-tip', '不能为空');
                                pass = false;
                            }
                        }
                        else {
                            if (fromInput.val() == "") {
                                fromTd.attr('verification-tip', '最大区间左部分不能为空');
                                pass = false;
                            }
                        }
                        if (lastRateTo != "" && parseInt(fromInput.val()) - parseInt(lastRateTo) < 0) {
                            fromTd.attr('verification-tip', '下一区间开始必须大于等于上一区间结束');
                            pass = false;
                        }
                        else if (parseInt(toInput.val()) - parseInt(fromInput.val()) < 0) {
                            toTd.attr('verification-tip', '区间结束必须大于区间开始');
                            pass = false;
                        }
                            lastRateTo = toInput.val();
                            
                            $(this).find("input").each(function () {
                            d[this.name] = this.value || -0.001;
                            });
                            rates.push(d);
                        });
                        if (!pass) return;
                        this.ruleTarget.rates = rates;
                        this.ruleTarget.commit();
                        this.Hide();
                        $(this.caller).trigger("afterFill", this.ruleTarget);
                    },
                    beforeHide() {
                        window.selectedRule=null
                    },
                });                 
            })
        </script>
    <script src="~/ChosenSelect/chosen.jquery.js"></script>
</body>
</html>