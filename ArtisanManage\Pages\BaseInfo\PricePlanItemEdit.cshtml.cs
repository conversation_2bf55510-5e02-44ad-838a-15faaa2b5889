﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class PricePlanItemEditModel : PageFormModel
    { 
        public int pid = 0;
        public string buyPrice = "";
        public string wholesalePrice = "";
        public string sonMumItem = "";
        public PricePlanItemEditModel(CMySbCommand cmd, string company_id = "", string oper_id = "") : base(Services.MenuId.infoPrice)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"item_id",new DataItem(){Title="编号",CtrlType="hidden"}},
                {"son_mum_item",new DataItem(){Title="父id",CtrlType="hidden"}},
                {"item_name",new DataItem(){Title="名称",UseJQWidgets=false}},
                 
                {"s_unit_no",new DataItem(){Title="小单位",SqlFld="s_unit_no",UseJQWidgets=false,Readonly=true}},
                {"m_unit_no",new DataItem(){Title="中单位",SqlFld="m_unit_no",UseJQWidgets=false,Readonly=true}},
                {"b_unit_no",new DataItem(){Title="大单位",SqlFld="b_unit_no",UseJQWidgets=false,Readonly=true}},
                {"s_unit_no_1",new DataItem(){Title="小单位",SqlFld="s_unit_no",UseJQWidgets=false,Readonly=true}},
                {"m_unit_no_1",new DataItem(){Title="中单位",SqlFld="m_unit_no",UseJQWidgets=false,Readonly=true}},
                {"b_unit_no_1",new DataItem(){Title="大单位",SqlFld="b_unit_no",UseJQWidgets=false,Readonly=true}},
                
                {"m_unit_factor",new DataItem(){SqlFld="m_unit_factor",FldArea="divHead",Hidden=true}},
                {"b_unit_factor",new DataItem(){SqlFld="b_unit_factor",FldArea="divHead",Hidden=true}},
                {"units_relation",new DataItem(){GetFromDb=false,UseJQWidgets=false}},
                 
                
                {"s_wholesale_price",new DataItem(){Title="小单位价格",SqlFld="s_wholesale_price",UseJQWidgets=false,Readonly=true}},
                {"m_wholesale_price",new DataItem(){Title="中单位价格",SqlFld="m_wholesale_price",UseJQWidgets=false,Readonly=true}},
                {"b_wholesale_price",new DataItem(){Title="大单位价格",SqlFld="b_wholesale_price",UseJQWidgets=false,Readonly=true}},
                
                {"s_buy_price",new DataItem(){Title="小单位价格",SqlFld="s_buy_price",UseJQWidgets=false,Readonly=true}},
                {"m_buy_price",new DataItem(){Title="中单位价格",SqlFld="m_buy_price",UseJQWidgets=false,Readonly=true}},
                {"b_buy_price",new DataItem(){Title="大单位价格",SqlFld="b_buy_price",UseJQWidgets=false,Readonly=true}},


            };
            m_idFld = "item_id"; m_nameFld = "item_name";
            m_tableName = "info_item_prop";
            SaveMainTable = false;
            m_selectFromSQL = @"
from info_item_prop 
left join info_item_class on info_item_prop.item_class=info_item_class.class_id 
left join info_item_brand on info_item_prop.item_brand=info_item_brand.brand_id
left join
(
    select item_id,                           s->>'f2' as s_unit_no, s->>'f3' as s_buy_price, s->>'f4' as s_wholesale_price,
                    m->>'f1' as m_unit_factor,m->>'f2' as m_unit_no, m->>'f3' as m_buy_price, m->>'f4' as m_wholesale_price,
                    b->>'f1' as b_unit_factor,b->>'f2' as b_unit_no, b->>'f3' as b_buy_price, b->>'f4' as b_wholesale_price
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price,wholesale_price)) as json from info_item_multi_unit where item_id=~ID',$$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)
) mu on info_item_prop.item_id = mu.item_id where info_item_prop.item_id='~ID' and info_item_prop.company_id = ~COMPANY_ID
";

            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridUnit" ,new FormDataGrid(){
                   AutoAddRow=true,
                   AllowDragRow=true,
                   AllowInsertRemoveRow=true,
                   MinRows=9,
                   Columns = new Dictionary<string, DataItem>()
                   {
                       {"plan_id",new DataItem(){Title="方案名称",SqlFld="plan_id",Necessary=true,LabelFld = "plan_name",Width="160",MaxRecords="500",
                         SqlForOptions=@"select plan_id::text as plan_id,plan_name as plan_name from price_plan_main where price_plan_main.company_id=~COMPANY_ID"
                       } },
                       {"plan_price",new DataItem(){Title="方案价格", Width="60",  FuncGetSubColumns = async (col) =>
                        {
                            ColumnsResult result=new ColumnsResult();
                            Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>(){
                                {"b_price",new DataItem(){Title="大",Width="100",SqlFld="b_price",
                                    JSCellRender=@"function (row, column, value,p4,p5,rowData) { 
                                    var b_unit_no = rowData.bunit
                                    if(rowData.plan_id&&b_unit_no) b_unit_no='/'+b_unit_no
                                    var html = `<div style=""height:100%; display: flex; align-items:center; justify-content:flex-end; ""><label>${value}</label><label style=""margin-right:3px; "">${b_unit_no || ''}</label></div>`
                                    return html;
                                    }",
                                    JSCellBeginEdit =
                                        @"function(row, datafield, columntype, value) {
                                                        var b_unit_no = $('#gridUnit').jqxGrid('getcellvalue', row, 'bunit');
                                                        if(!b_unit_no) return false;
                                        }"
                               } },
                               {"m_price",new DataItem(){Title="中",Width="100",SqlFld="m_price", JSCellRender=@"function (row, column, value,p4,p5,rowData) { 
                                    var m_unit_no = rowData.munit
                                    if(rowData.plan_id&&m_unit_no) m_unit_no='/'+m_unit_no
                                    var html = `<div style=""height:100%; display: flex; align-items:center; justify-content:flex-end; ""><label>${value}</label><label style=""margin-right:3px; "">${m_unit_no || ''}</label></div>`
                                    return html;
                                    }",
                                   JSCellBeginEdit =
                                        @"function(row, datafield, columntype, value) {
                                                        var m_unit_no = $('#gridUnit').jqxGrid('getcellvalue', row, 'munit');
                                                        if(!m_unit_no) return false;
                                        }"
                                }},
                               {"s_price",new DataItem(){Title="小",Width="100",SqlFld="s_price", JSCellRender=@"function (row, column, value,p4,p5,rowData) { 
                                    var s_unit_no = rowData.sunit;
                                    if(rowData.plan_id&&s_unit_no) s_unit_no='/'+s_unit_no
                                    var html = `<div style=""height:100%; display: flex; align-items:center; justify-content:flex-end; ""><label>${value}</label><label style=""margin-right:3px; "">${s_unit_no || ''}</label></div>`
                                    return html;
                                }"}},
                                {"discount",new DataItem(){Title="折扣%",SqlFld="s.discount",Width="100",
                                    JSCellRender=@"function (row, column, value,p4,p5,rowData) { 
                                    var plan_name = rowData.plan_name;
                                    var symbol='';
                                    if(plan_name) symbol='%' 
                                    var html = `<div style=""height:100%; display: flex; align-items:center; justify-content:flex-end; ""><label>${value}</label><label style=""margin-right:3px; "">${symbol}</label></div>`
                                    return html;
                                 }"
                                }}

                            };
                            result.Columns=subColumns;
                            return result;
                       }} },
                      
                  
                       //{"s_price_unit",new DataItem(){Title="小单位价格",SqlFld="(case when small_price is not null then concat(small_price,'元/',s_unit_no) end)",SaveToDB=false}},
                       
                       //{"b_price_unit",new DataItem(){Title="大单位价格",SqlFld="(case when big_price is not null then concat(big_price,'元/',b_unit_no) end)",SaveToDB=false}},
                      
                       {"sunit",new DataItem(){Title="小单位",SqlFld="s_unit_no",SaveToDB=false,Hidden=true} },
                       //{"sfactor",new DataItem(){Title="小数量",SqlFld="unitTable.sfactor",SaveToDB=false,Hidden=true} },
                       {"munit",new DataItem(){Title="中单位",SqlFld="m_unit_no",SaveToDB=false,Hidden=true} },
                       {"mfactor",new DataItem(){Title="中数量",SqlFld="m_unit_factor",SaveToDB=false,Hidden=true} },
                       {"bunit",new DataItem(){Title="大单位",SqlFld="b_unit_no",SaveToDB=false,Hidden=true} },
                       {"bfactor",new DataItem(){Title="大数量",SqlFld="b_unit_factor",SaveToDB=false,Hidden=true} },
                       
                   },
                   TableName="price_plan_item",
                   IdFld="item_id",
                   SelectFromSQL=@"
from 
(
    select plan_id pid,item_id,s_price,m_price,discount,b_price from price_plan_item where company_id=~COMPANY_ID and item_id='~ID'
) s left join price_plan_main plan on s.pid=plan.plan_id and plan.company_id=~COMPANY_ID 
right join 
(
    SELECT ip.item_id,item_name,item_no,
       t.s->> 'f2' as s_unit_no, t.s->>'f3' s_buy_price, t.s->>'f4' s_wholesale_price, 
       t.m->> 'f2' as m_unit_no, t.m->>'f3' m_buy_price, t.m->>'f4' m_wholesale_price, t.m->>'f1' m_unit_factor,
       t.b->> 'f2' as b_unit_no, t.b->>'f3' b_buy_price, t.b->>'f4' b_wholesale_price, t.b->>'f1' b_unit_factor   

    FROM info_item_prop as ip 
    LEFT JOIN 
    (
        select item_id, s, m, b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price,wholesale_price)) as json from info_item_multi_unit where item_id=~ID',$$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)
    ) t on ip.item_id = t.item_id where ip.item_id='~ID' and ip.company_id = ~COMPANY_ID
) ip on ip.item_id = s.item_id"
                   /*SelectFromSQL="from (select price.plan_id,price.small_discount,price.small_price,price.mid_discount,price.mid_price," +
                   "price.big_discount,price.big_price from price_prop_item price where item_id='~ID') s right join plan_item plan " +
                   "on s.plan_id=plan.plan_id order by plan.update_time"*/
                   //SelectFromSQL="from price_prop_item price right join plan_item plan on price.company_id=plan.company_id where item_id='~ID' order by plan.update_time"
                }}
            };
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            string sql = $"select rights->'delicacy'->'seeInPrice'->'value' see_cost_right from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
            dynamic seeCostPrice = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            var itemCol = Grids["gridUnit"].Columns;
            //itemCol["buy_price"].Hidden = true;
            if (seeCostPrice != null && seeCostPrice.see_cost_right == "true")
            {
             //   itemCol["buy_price"].Hidden = false;
            }
            string units_relation = "";
            if (DataItems["b_unit_factor"].Value != "")
            {
                double b_unit_factor = Convert.ToDouble(DataItems["b_unit_factor"].Value);
                units_relation += "1" + DataItems["b_unit_no"].Value+"=";
                if (DataItems["m_unit_factor"].Value != "")
                {
                    double m_unit_factor = Convert.ToDouble(DataItems["m_unit_factor"].Value);
                    string bm =CPubVars.FormatMoney(b_unit_factor / m_unit_factor,2);
                    units_relation += bm + DataItems["m_unit_no"].Value+"="; 
                } 
                units_relation += b_unit_factor.ToString() + DataItems["s_unit_no"].Value;
                
            }
            DataItems["units_relation"].Value = units_relation;


        }


        public async Task OnGet(int plan_id,string buy_price,string wholesale_price,string son_mum_item)
        {
            await InitGet(cmd);
            pid = plan_id;
            buyPrice = buy_price;
            wholesalePrice = wholesale_price;
            sonMumItem = son_mum_item;
        }
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class PricePlanItemEditController : BaseController
    {
        public PricePlanItemEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            PricePlanItemEditModel model = new PricePlanItemEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            PricePlanItemEditModel model = new PricePlanItemEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            string error = "";
            PricePlanItemEditModel model = new PricePlanItemEditModel(cmd);
            var gridUnit = request.gridUnit;
            foreach (var row in gridUnit)
            {
                if (row.plan_name != "" && (string)row.s_price=="" && (string)row.m_price == "" && (string)row.b_price == "" && (string)row.discount == "")
                {
                    error = row.plan_name+"必须设置价格或折扣";
                    break;
                }
            }
            if(error!="")
                return new JsonResult(new { result = "Error", msg = error });
            string operKey =request.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
             

            string item_id = request.item_id;
            
            string s_unit_no =request.s_unit_no;
            string m_unit_no =request.m_unit_no;
            string b_unit_no =request.b_unit_no;
            string s_wholesale_price =request.s_wholesale_price;
            string m_wholesale_price =request.m_wholesale_price;
            string b_wholesale_price =request.b_wholesale_price;
          
            string s_buy_price =request.s_buy_price;
            string m_buy_price =request.m_buy_price;
            string b_buy_price =request.b_buy_price;
            string sql = "";
            CDbDealer db = new CDbDealer();
            db.AddField("wholesale_price", s_wholesale_price);
            db.AddField("buy_price", s_buy_price);
            sql+=db.GetUpdateSQL("info_item_multi_unit", $"company_id={companyID} and item_id={item_id} and unit_type='s'")+";";

            if (m_unit_no != "")
            {
                db = new CDbDealer();
                db.AddField("wholesale_price", m_wholesale_price);
                db.AddField("buy_price", m_buy_price);
                sql += db.GetUpdateSQL("info_item_multi_unit", $"company_id={companyID} and item_id={item_id} and unit_type='m'") + ";";

            }
            if (b_unit_no != "")
            {
                db = new CDbDealer();
                db.AddField("wholesale_price", b_wholesale_price);
                db.AddField("buy_price", b_buy_price);
                sql += db.GetUpdateSQL("info_item_multi_unit", $"company_id={companyID} and item_id={item_id} and unit_type='b'") + ";";

            }
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            //string sql=$"update info_item_multi_unit set wholesale_price={s_wholesale_price}"
            
            // 价格方案更新同步去修改
            string son_mum_item = request.son_mum_item??null;
            string son_options_id = "";
            if (!string.IsNullOrEmpty(son_mum_item))
            {
                string smiSql = @$"select mum_attributes, avail_attr_combine from info_item_prop where company_id = {companyID} and item_id = {son_mum_item}";
                dynamic result = await CDbDealer.Get1RecordFromSQLAsync(smiSql, cmd);
                string availAttrCombineStr = result.avail_attr_combine;
                string mumAttributesStr = result.mum_attributes;
                JArray availAttrCombine = JsonConvert.DeserializeObject<JArray>(availAttrCombineStr);
                JArray mumAttributes = JsonConvert.DeserializeObject<JArray>(mumAttributesStr);
                string newAvailAttrCombineStr = "";
                string newMumAttributesStr = "";
                if (mumAttributes.Count == 1)
                {
                    // 获取第一个JObject
                    JObject firstObj = (JObject)mumAttributes[0];
                    // 检查是否存在options数组
                    if (firstObj["options"] is JArray options)
                    {
                        foreach (JObject option in options)
                        {
                            // 检查optID是否匹配
                            if ((string)option["optID"] == son_options_id)
                            {
                                // 更新或添加bPrice、mPrice、sPrice字段
                                option["bPrice"] = b_wholesale_price; // 这里假设您要更新的值
                                option["mPrice"] = m_wholesale_price; // 添加了mPrice字段
                                option["sPrice"] = s_wholesale_price; // 更新了sPrice的
                                break; // 找到匹配项后退出循环
                            }
                        }
                    }
                    newMumAttributesStr = JsonConvert.SerializeObject(mumAttributes);
                }
                if (availAttrCombine.Count > 0)
                {
                    foreach (JObject obj in availAttrCombine)
                    {
                        // 检查是否为目标item_id
                        if ((string)obj["item_id"] == item_id)
                        {
                            // 更新价格
                            obj["bPrice"] = b_wholesale_price;
                            obj["mPrice"] = m_wholesale_price;
                            obj["sPrice"] = s_wholesale_price;
                            break; // 如果只需要更新一个匹配的对象，找到后即退出循环
                        }
                    }
                    newAvailAttrCombineStr = JsonConvert.SerializeObject(availAttrCombine);
                }
                var setClauses = new List<string>();
                if (!string.IsNullOrEmpty(newMumAttributesStr))
                {
                    setClauses.Add($"mum_attributes = '{newMumAttributesStr}'");
                }
                if (!string.IsNullOrEmpty(newAvailAttrCombineStr))
                {
                    setClauses.Add($"avail_attr_combine = '{newAvailAttrCombineStr}'");
                }
                // 只有当至少有一个属性非空时，才构建和执行SQL语句
                if (setClauses.Count > 0)
                {
                    string setClause = string.Join(", ", setClauses);
                    smiSql = $"UPDATE info_item_prop SET {setClause} WHERE company_id = {companyID} AND item_id = {son_mum_item}";
                    // 执行smiSql语句
                    cmd.CommandText = smiSql;
                    await cmd.ExecuteNonQueryAsync();
                }

            }
            return await model.SaveTable(cmd, request);

        }
    }
}