﻿@page
@model ArtisanManage.Pages.CwPages.Report.SubBalanceModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>SubBalance</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css?v=@Html.Raw(Model.Version)" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.js?v=@Html.Raw(Model.Version)"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxwindow.js"></script>

    <script src="~/js/FileSaverVue.js"></script>
    <script src="~/js/Blob.js"></script>
    <script src="~/js/jszip.js"></script>
    <script src="~/js/xlsx.full.min.js"></script>
    <script src="~/js/xlsx-style.js"></script>
    <script src="~/js/Export2Excel.js?v=@Html.Raw(Model.Version)"></script>

    <style>
        * {
            font-family: "微软雅黑"
        }
        [v-cloak] {
            display: none;
        }

        body {
        }

        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;

        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #dddddd;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }

        #pages {
            width: 100%;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            /*background-color: #f9f9f9;*/
        }

        .pages_title {
            width: 100%;
            height:5vh;
            display:block;
            font-weight: 500;
            font-size: 25px;
            text-align: center;
            margin-top: 5px;
            padding-bottom:10px;
        }

        .pages_query {
            height:5vh;
            display: block;
            margin-left: 20px;
            margin-right: 20px;
            flex-wrap: wrap;
            padding-bottom: 10px;
        }

        .query_item {
            display: flex;
            width:50%;
            float:left;
            margin-top: 3px;
        }

        .item_input {
            width: 149px;
            height: 100%;
            z-index: 0;
            position: relative;
            border-style: none none solid;
            border-bottom: 1px solid #c7c7c7;
            border-radius: 0px;
            margin-left: 10px;
        }

        .item_name {
            height: 20px;
            bottom: 1px;
            right: 1px;
            margin-bottom: 0px;
        }

        .pages_buttons {
            width:49%;
            height: 100%;
            float:right;
            display:flex;
            justify-content:end;
            align-items:center;
        }

        .pages_content {
            height: 100%;
            padding: 0 20px;
            margin-bottom:10px;
        }

        .level2 {
            text-indent: 1em;
        }
        .level3 {
            text-indent: 2em;
        }


        .pages_content table {
            width: 100%;
            height:86vh;
            border-collapse: collapse;
            border: 2px solid #ebeef5;
        }

        .pages_content table tbody {
            display: block;
            overflow-y: scroll;
            overflow-x: hidden;
            height: calc(100% - 84px);
        }
        @@media(max-height:700px) {
            .pages_content table tbody {
                display: block;
                overflow: auto;
                overflow-x: hidden;
                height: 460px;
            }
         }
        .pages_content table thead, .pages_content tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .pages_content table thead {
            width: calc( 100% - 1em - 2px );
        }

        .pages_content table thead th{
            background-color:#f0f0f5;
        }

       .pages_content table thead th:nth-child(2),  .pages_content table tbody td:nth-child(2) {
            width: 15%
        }
        .pages_content table thead th:nth-child(2),  .pages_content table tbody td:nth-child(2) {
            width: 15%
        }
        .pages_content table tbody td:not(:nth-child(1),:nth-child(2)){
            text-align: right;
        }
        @*高度*@
        .pages_content table thead th, .pages_content table tbody td {
            min-height: 40px;
            line-height: 40px;
        }
        @*边框*@
        .pages_content table thead th, .pages_content table tbody td {
            border-bottom: 2px solid #fff;
            border-right: 2px solid #fff;
        }
            .pages_content table thead th:last-child {
                border-right: 0;
            }
        @*背景*@
        .pages_content table tbody tr:nth-child(odd) {
            background: #fafafa;
        }
        .pages_content table tbody tr:hover {
            background-color: #f5f7fa;
        }

        .pages_content table thead th, .pages_content table tbody td {
            padding: 0 15px;
        }

        .pages_content table tbody td:nth-child(2){
            color:#49f;
            text-decoration:underline;
            cursor:pointer;
        }

        .el-date-editor, .el-input{
            width:200px;
            margin-right:20px;
        }

        .el-input__icon:hover{
            cursor:pointer;
            color:#000;
        }

        .table_row_sub_code_a{
            color:blue;
            cursor:pointer;
        }

    </style>

</head>
<body>
    <div id="root" v-cloak>
        <div id="pages" class="" ref="pages">
            <div class="pages_title">科目余额表</div>
            <div class="pages_query">
                <div class="query_item">
                    <label class="item_name"></label>
                    <el-date-picker
                          v-model="queryDate"
                          type="monthrange"
                          range-separator="至"
                          start-placeholder="开始月份"
                          end-placeholder="结束月份"
                          :disabled="disabledDatePicker"
                          :picker-options="pickerOptions"
                          @@change="changeDate">
                    </el-date-picker>
                    <el-input v-model="subsShow" :title="subsShow" :disabled="disabledDatePicker" placeholder="请选择科目" readonly>
                          <i  class="el-icon-more el-input__icon" slot="suffix" @@click="subIconClick"></i> 
                    </el-input>
                    @*<div id="aaa"></div>*@
                    <div id="popSub" style="display:none">
                        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择科目</span></div>
                        <div style="overflow:hidden;"> </div>
                    </div>
                </div>
                <div class="pages_buttons">
                    <el-button type="info" plain :disabled="disabledExportBtn" v-on:click="exportBtn()" style="position:absolute;right:20px;">导出</el-button>
                </div>
            </div>
            <div class="pages_content">
                <table>
                    <thead>
                        <tr>
                            <th rowspan="2">科目代码</th>
                            <th rowspan="2">科目名称</th>
                            <th colspan="2">期初余额</th>
                            <th colspan="2">本期发生</th>
                            <th colspan="2">本年累计</th>
                            <th colspan="2">期末余额</th>
                        </tr>
                        <tr>
                            <th>借方</th>
                            <th>贷方</th>
                            <th>借方</th>
                            <th>贷方</th>
                            <th>借方</th>
                            <th>贷方</th>
                            <th>借方</th>
                            <th>贷方</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="row in balData" :key="row.rowIndex">
                            <td :class="row.class">{{row.sub_code}}</td>
                            <td :class="row.class" @@click="openDetail(row)">{{row.sub_name}}</td>
                            <td>{{row.op_bal_debit}}</td>
                            <td>{{row.op_bal_credit}}</td>
                            <td>{{row.this_bal_debit}}</td>
                            <td>{{row.this_bal_credit}}</td>
                            <td>{{row.year_bal_debit}}</td>
                            <td>{{row.year_bal_credit}}</td>
                            <td>{{row.ed_bal_debit}}</td>
                            <td>{{row.ed_bal_credit}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>   
    <script>
        var g_operKey = '@Model.OperKey';

        window.g_operRights =@Html.Raw(Model.JsonOperRightsOrig);
        function checkOperRight(vm){
            if(!window.g_operRights.cwReport){
                return false;
            }
            if (window.g_operRights.cwReport.accountBalance && window.g_operRights.cwReport.accountBalance.see) { 
                if(!window.g_operRights.cwReport.accountBalance.export) vm.disabledExportBtn=true;
                return true;
            }else{
                return false;
            }
        }

        //jqxWindows初始化
        $("#popSub").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 560, width: 830, theme: 'summer', autoOpen: false, showCloseButton: true,closeButtonSize:32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType:'fade'});

        //监听接收jqxWindows的消息
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "PaywaysView") {
                if (rs.data.action === "multi-select") {
                    console.log('select subs from paywayview');
                    let subsStr='';
                    rs.data.subs.forEach(item=>{
                        let code=item.sub_code;
                        if(code!='' && code!='1' && code!='2' && code!='3' && code!='4' && code!='5' && item.status=='正常'){
                            subsStr+=item.sub_code+',';
                        }
                    });
                    vm.subsShow=subsStr;
                    vm.getData(vm.queryDate[0], vm.queryDate[1],subsStr);

                    $('#popSub').jqxWindow('close');
                    //$("#aaa").html('');
                }
            }
        });
    </script>
    <script>

        var vm = new Vue({
            el: '#root',
            data() {
                return {
                    balData:[/* //演示数据
                        { 
                            rowIndex:1, sub_id:'12345', sub_code:'5601', sub_name:'销售费用', class:'',  
                            op_bal_debit:0, op_bal_credit:0, 
                            this_bal_debit:0, this_bal_credit:0, 
                            year_bal_debit:0, year_bal_credit:0, 
                            ed_bal_debit:0, ed_bal_credit:0 
                        },
                        {
                            rowIndex:2, sub_id:'12346', sub_code:'560117', sub_name:'其他销售费', class:'level2',  
                            op_bal_debit:0, op_bal_credit:0, 
                            this_bal_debit:0, this_bal_credit:0, 
                            year_bal_debit:0, year_bal_credit:0, 
                            ed_bal_debit:0, ed_bal_credit:0 
                        },
                        {
                            rowIndex:3, sub_id:'12347', sub_code:'56011701', sub_name:'路费', class:'level3',  
                            op_bal_debit:0, op_bal_credit:0, 
                            this_bal_debit:0, this_bal_credit:0, 
                            year_bal_debit:0, year_bal_credit:0, 
                            ed_bal_debit:0, ed_bal_credit:0 
                        },
                    */],
                    queryDate: [
                        new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)),
                        new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1))
                    ],
                    pickerOptions:{
                        disabledDate(time) {
                            return time.getTime() > Date.now();
                        }
                    },
                    subsShow:'',
                    companyName:'',
                    disabledDatePicker:false,
                    disabledExportBtn:false
                }
            },
            created(){
                if(!window.checkOperRight(this)){
                    this.balData=[];
                    this.disabledDatePicker=true;
                    this.disabledExportBtn=true;
                    return;
                }

                this.getData(this.queryDate[0]+'-1',this.queryDate[1]+'-1','');
            },
            methods: {
                getData(fromPeriod, toPeriod,subsSelect){
                    $.ajax({
                        url: '/api/SubBalance/GetData',
                        type: 'get',
                        data: {
                            operKey: g_operKey,
                            fromPeriod: fromPeriod,
                            toPeriod: toPeriod,
                            subsSelect:subsSelect
                        },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then((res)=> {
                        if (res.result === 'OK') {
                            console.log('get subbal data');
                            this.companyName=res.companyName;
                            if(res.msg!=''){
                                this.balData=[];
                                this.$message({ showClose: true, message: res.msg, type: 'warning', offset: 20, duration: 2500 });
                                return;
                            }
                            this.queryDate=[res.pickFromPeriod,res.pickToPeriod];
                            this.pickerOptions={
                                disabledDate(time) {
                                    return (time.getTime()<new Date(res.openPeriod) || time.getTime() > new Date(res.maxPeriod));
                                }
                            };
                            if(res.balData.length==0){
                                this.$alert('所选科目无余额', '提示', { confirmButtonText: '确定' });
                            }
                            this.balData=res.balData;
                            this.balData.forEach(row=>{
                                row.op_bal_debit=row.op_bal_debit=='0'?'':row.op_bal_debit;
                                row.op_bal_credit=row.op_bal_credit=='0'?'':row.op_bal_credit;
                                row.this_bal_debit=row.this_bal_debit=='0'?'':row.this_bal_debit;
                                row.this_bal_credit=row.this_bal_credit=='0'?'':row.this_bal_credit;
                                row.year_bal_debit=row.year_bal_debit=='0'?'':row.year_bal_debit;
                                row.year_bal_credit=row.year_bal_credit=='0'?'':row.year_bal_credit;
                                row.ed_bal_debit=row.ed_bal_debit=='0'?'':row.ed_bal_debit;
                                row.ed_bal_credit=row.ed_bal_credit=='0'?'':row.ed_bal_credit;
                            });
                        }
                    });
                },
                changeDate(){
                    this.getData( this.dateFormat(this.queryDate[0],'yyyy-MM-dd'), this.dateFormat(this.queryDate[1],'yyyy-MM-dd'),this.subsShow );
                },
                dateFormat(date, fmt) { 
                    var o = {
                        "M+": date.getMonth() + 1, //月份
                        "d+": date.getDate(), //日
                        "h+": date.getHours(), //小时
                        "m+": date.getMinutes(), //分
                        "s+": date.getSeconds(), //秒
                        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
                        "S": date.getMilliseconds() //毫秒
                    };
                    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
                    for (var k in o)
                        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                    return fmt;
                },
                subIconClick(){
                    $('#popSub').jqxWindow('open');
                    $("#popSub").jqxWindow('setContent', `<iframe src="/setting/PaywaysView?forSelect=1&hasCheck=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

                    //$("#aaa").html(`
                    //<div id="popSub" style="outline: none; width: 900px; height: 600px; min-height: 50px; max-height: 1200px; min-width: 100px; max-width: 1200px; top: calc(50% - 300px); left: calc(50% - 450px); z-index: 1801; display: block;" role="dialog" class="jqx-rc-all jqx-rc-all-summer jqx-window jqx-window-summer jqx-popup jqx-popup-summer jqx-widget jqx-widget-summer jqx-widget-content jqx-widget-content-summer" tabindex="0" hidefocus="true">
                    //    <div class="jqx-resize jqx-rc-all" style="z-index: 8000; height: auto; width: auto; cursor: default;">
                    //        <div id="clientCaption" style="height: 30px; background-color: rgb(255, 255, 255); text-align: center; position: relative; width: 890px; cursor: move;" class="jqx-window-header jqx-window-header-summer jqx-widget-header jqx-widget-header-summer jqx-disableselect jqx-disableselect-summer jqx-rc-t jqx-rc-t-summer" tabindex="0">
                    //            <div style="float: left; direction: ltr; margin-top: 1.875px;"><span style="font-size:20px;">选择科目</span></div>
                    //            <div class="jqx-window-close-button-background jqx-window-close-button-background-summer" style="visibility: visible; width: 32px; height: 32px; margin-right: 5px; margin-left: 0px; position: absolute; right: 0px;">
                    //                <div class="jqx-window-close-button jqx-window-close-button-summer jqx-icon-close jqx-icon-close-summer" style="width: 100%; height: 100%;" onclick="$('#aaa').html('');"></div>
                    //            </div>
                    //            <div class="jqx-window-collapse-button-background jqx-window-collapse-button-background-summer" style="visibility: hidden; width: 16px; height: 16px; margin-right: 5px; margin-left: 0px; position: absolute; right: 32px;">
                    //                <div class="jqx-window-collapse-button jqx-window-collapse-button-summer jqx-icon-arrow-up jqx-icon-arrow-up-summer" style="width: 100%; height: 100%; top: 0px;"></div>
                    //            </div>
                    //        </div>
                    //        <div style="overflow: hidden; width: 890px; height: 549px;" class="jqx-window-content jqx-window-content-summer jqx-widget-content jqx-widget-content-summer jqx-rc-b jqx-rc-b-summer" tabindex="0">
                    //            <iframe src="/setting/PaywaysView?forSelect=1&hasCheck=1&amp;operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>
                    //        </div>
                    //    </div>
                    //</div>
                    //<div class="jqx-window-modal jqx-window-modal-summer" style="opacity: 0.3; position: absolute; top: 0px; left: 0px; z-index: 1800; width: 100%; height: 100%; display: block;"></div>`);
                },
                exportBtn(){
                    let period=`${this.queryDate[0].substr(0,4)}${this.queryDate[0].substr(5,2)}-${this.queryDate[1].substr(0,4)}${this.queryDate[1].substr(5,2)}`;
                    let data=[
                        ['科目余额表'], 
                        [`公司名称：${this.companyName}`,null,null,null,null,null, `会计期间：${period}`,null,null,'单位：元'],
                        ['科目代码', '科目名称', '期初余额', null, '本期发生', null, '本年累计', null, '期末余额', null ],
                        [ null, null, '借方', '贷方', '借方', '贷方', '借方', '贷方', '借方', '贷方' ]
                    ];
                    this.balData.forEach(row=>{
                        let excelRow=[row.sub_code, row.sub_name, row.op_bal_debit, row.op_bal_credit, row.this_bal_debit, row.this_bal_credit, row.year_bal_debit, row.year_bal_credit, row.ed_bal_debit, row.ed_bal_credit ];
                        data.push(excelRow);
                    });
                    let merges=['A1:J1', 'A2:D2', 'G2:H2', 'A3:A4', 'B3:B4', 'C3:D3', 'E3:F3', 'G3:H3', 'I3:J3' ];
                    let bodyTitleName=[];                    
                    let  specialCellConfig=[
                        { 
                            "type": "s", 
                            "configObj": { 
                                "font": { "sz": 14, "bold": true },
                                "alignment": { "horizontal": "center" }
                            }, 
                            "controlScope": "col", 
                            "scope": [ 1, 1 ] 
                        },
                        { 
                            "type": "s", 
                            "configObj": { 
                                "border": { "top": { "style": "thin" }, "bottom": { "style": "thin" }, "left": { "style": "thin" }, "right": { "style": "thin" } },
                                "font": { "bold": true },
                                "alignment": { "horizontal": "center" }
                            }, 
                            "controlScope": "col", 
                            "scope": [ 3, 4 ] 
                        },
                        { 
                            "type": "s", 
                            "configObj": { "border": { "top": { "style": "thin" }, "bottom": { "style": "thin" }, "left": { "style": "thin" }, "right": { "style": "thin" } } }, 
                            "controlScope": "col", 
                            "scope": [ 5, data.length ] 
                        }, 
                    ];
                    window.webExportExcel(data,`科目余额表[${period}]`, merges, bodyTitleName, specialCellConfig)
                },
                openDetail(row){
                    window.parent.newTabPage('明细账', `/CwPages/Report/DetailAccount?period=${this.queryDate[0].substring(0,10)}~${this.queryDate[1].substring(0,10)}&sub_id=${row.sub_id}`, window);
                }
            }
        })
    </script>


</body>
</html>