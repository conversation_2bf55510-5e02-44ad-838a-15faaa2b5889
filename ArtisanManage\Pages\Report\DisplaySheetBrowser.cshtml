﻿@page
@model ArtisanManage.Pages.Sheets.DisplaySheetBrowserModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html style="width: 100%;height: 100%;overflow: hidden">
<head>
  <title>陈列协议全览</title>
  <script>var commonVersion=@Model.Version</script>
  <script src="~/js/commonConfig.js?v=@Model.Version"></script>
  <script src="~/js/Vue.js"></script>
  <style>
    iframe{
      width:100%;
      height:100%;
      margin-left:0;
      margin-right:0;
      border: 0;
      background-color: transparent;
    }
  </style>
</head>
<body style="width: 100%;height: 100%">
<div id="pages"  style="width: 100%;height: 100%">
  <iframe :src="iframeSrc"></iframe>
</div>
<script>
    var g_operKey = '@Model.OperKey';
</script>
<script>
  const app = new Vue({
    el: "#pages",
    data() {
        return {
          iframeSrc : pageRouter.displaySheetBrowser.router + g_operKey
        }
    },
    mounted() {
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "DisplayAgreementSheet") {
                window.parent.newTabPage(rs.data.title, `Sheets/DisplayAgreementSheet?sheet_id=${rs.data.sheet_id}`);
            }
        });
    }
  })
</script>
</body>
</html>