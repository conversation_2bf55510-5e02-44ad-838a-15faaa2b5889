using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Configuration;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.Cashier;

[Route("api/cashier/[action]")]
public class SheetController : BaseController
{
    #region Predefs
    private readonly IHttpClientFactory _httpClientFactory;

    public SheetController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
    {
        this.cmd = cmd; _httpClientFactory = httpClientFactory;
    }
    #endregion

    [HttpGet]
    public async Task<CallResult<dynamic>> SearchItemsByBarcode(string operKey, string barcode, string branch_id) // 兼容旧接口，等新版发布后删除
    {
        return await SearchItems(operKey, barcode,branch_id);
    }
    [HttpGet]
    public async Task<CallResult<dynamic>> SearchItems(string operKey, string keyword, string branch_id)
    {
        if (keyword.IsInvalid())
            return new("Error", "请输入条码");
        // 2024.06.20 - #2492
        // 允许输入商品名称或助记码来检索商品
        //if (!long.TryParse(barcode, out long _))
        //    return new("Error", "目前只支持输入纯数字条码来检索商品");
        Security.GetInfoFromOperKey(operKey, out string companyId);

        var sql = $@"
            SELECT
                p.item_id, p.item_no, p.item_name, mu.barcode, p.item_images,
                p.item_class as item_class_id, c.class_name as item_class_name,
                mu.unit_no, mu.unit_type, mu.unit_factor, 
                mu.retail_price, mu.wholesale_price,
                COALESCE(s.stock_qty, 0) as stock_qty, p.batch_level, iib.produce_date,
                 unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as stock_qty_unit
            FROM
                info_item_multi_unit mu
                LEFT JOIN info_item_prop p on  p.company_id = '{companyId}' and  p.item_id = mu.item_id
                LEFT JOIN info_item_class c on  c.company_id = '{companyId}' and  c.class_id = p.item_class
                LEFT JOIN stock s on  s.company_id = '{companyId}' and  s.item_id = p.item_id and s.branch_id = {branch_id} 
                left join info_item_batch iib on  iib.company_id = '{companyId}' and  iib.company_id = {companyId} and iib.batch_id = s.batch_id
            left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,
                                                         b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no,s->>'f3' as s_buy_price
                                            from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id={companyId} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                                as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu1 on s.item_id = mu1.item_id
            WHERE
                mu.company_id = '{companyId}'
                and (
				    mu.barcode like '%{keyword}%'
				    or p.item_name like '%{keyword}%'
				    or p.py_str like '%{keyword}%'
		        )
		        and (p.item_id is not null and p.item_id <> 0)
                and (p.status is null or p.status = '1')
            ORDER BY
                p.item_id DESC
            LIMIT
                100;
        ";
        var result = await GetSingleRecord(sql);
        return result;
    }

    [HttpGet]
    public async Task<CallResult<dynamic>> GetPayways(string operKey)
    {
        Security.GetInfoFromOperKey(operKey, out string companyId);
        var sql = CommonTool.selectPayWay_Sale.Replace("~COMPANY_ID", companyId);
        var result = await GetSingleRecord(sql);
        return result;
    }

    [HttpPost]
    public async Task<CallResult<dynamic>> SubmitSheet([FromBody] dynamic data)
    {
        string operKey = data.operKey;
        string branchId = data.branchId;
        string s_SheetRows = JsonConvert.SerializeObject(data.sheetRows);
        Console.WriteLine(s_SheetRows);
        Security.GetInfoFromOperKey(operKey, out string companyId, out string operId);
        decimal subAmount = data.sheetProps?.subAmount;
        decimal discAmount = data.sheetProps?.discAmount ?? 0;
        decimal payAmount = subAmount - discAmount;
        string payway1_id = data.sheetProps?.payway1_id ?? "";
        decimal payway1_amount = data.sheetProps?.payway1_amount ?? 0;
        string payway2_id = data.sheetProps?.payway2_id ?? "";
        decimal payway2_amount = data.sheetProps?.payway2_amount ?? 0;
        string payway3_id = data.sheetProps?.payway3_id ?? "";
        decimal payway3_amount = data.sheetProps?.payway3_amount ?? 0;
        string sheetType = data.sheetProps.sheetType ?? "X";
        var sheet_type = sheetType.Equals("X") ? SHEET_TYPE.SHEET_SALE : SHEET_TYPE.SHEET_SALE_RETURN;
        if (payAmount < 0)
            return new("Error", "付款金额不能为负数");

        if (payway1_id.IsInvalid()) // 兼容老版本，规避异常情况
        {
            var getPaywayResponse = await GetPayways(operKey);
            if (!getPaywayResponse.IsOK) return getPaywayResponse;
            if (getPaywayResponse.data.Count == 0) return new("Error", "未设置可用的支付方式");
            payway1_id = getPaywayResponse.data[0].v;
        }

        string msg;
        var sheet = new SheetSale(SHEET_RETURN.NOT_RETURN, LOAD_PURPOSE.APPROVE)
        {
            OperKey = operKey,
            is_retail = true,
            order_source = "cashier",
            supcust_id = "0",
            branch_id = branchId,
            maker_id = operId,
            getter_id = operId,
            total_amount = subAmount,
            now_pay_amount = payAmount,
            now_disc_amount = discAmount,
            paid_amount = payAmount,
            disc_amount = discAmount,
            payway1_id = payway1_id,
            payway1_amount = payway1_amount,
            payway2_id = payway2_id,
            payway2_amount = payway2_amount,
            payway3_id = payway3_id,
            payway3_amount = payway3_amount,
            sheet_type = sheet_type
        };
        sheet.Init();
        var quantityStat = new Dictionary<string, decimal>()
        {
            { "b", 0 }, { "m", 0 }, { "s" , 0 }
        };
        try {
            var sheetRows = JsonConvert.DeserializeObject<List<dynamic>>(s_SheetRows);
            int row_index = 1;
            foreach (var row in sheetRows)
            {
                var sheetRow = new SheetRowSale
                {
                    row_index = row_index++,
                    inout_flag = sheetType.Equals("X") ? -1 : 1,
                    item_id = row.itemDetail.item_id,
                    item_name = row.itemName,
                    real_price = row.itemSalePrice,
                    orig_price = row.itemOrigSalePrice,
                    quantity = row.itemQuantity,
                    wholesale_price = row.itemDetail.wholesale_price,
                    sub_amount = row.rowSubAmount,
                    barcode = row.itemDetail.barcode,
                    class_name = row.itemDetail.item_class_name,
                    unit_no = row.itemUnit,
                    unit_factor = CPubVars.ToDecimal(row.itemDetail.unit_factor),
                    trade_type = sheetType.Equals("X") ? "X" : "T",
                    produce_date = row.produce_date
                };
                sheet.SheetRows.Add(sheetRow);

                string unit_type = row.itemDetail.unit_type;
                if (quantityStat.ContainsKey(unit_type))
                    quantityStat[unit_type] += sheetRow.quantity;
            }

            // 手动组装total_quantity
            var quantity_des = "销: ";
            foreach (var unit_qs in quantityStat)
            {
                if (unit_qs.Value == 0) continue;
                quantity_des += unit_qs.Value;
                quantity_des += unit_qs.Key switch {
                    "b" => "大",
                    "m" => "中",
                    "s" => "小",
                    _ => "未知"
                };
            }
            sheet.total_quantity = quantity_des;
        }
        catch (Exception e) {
            LogError($"在处理单据行时发生了错误", e);
            return new("Error", "提交发生了错误(5001)");
        }
        try {
            msg = await sheet.SaveAndApprove(cmd);
        } catch (Exception e) {
            LogError("在审核时抛出了异常", e);
            msg = "提交发生了错误(5002)";
        }
        var response = new
        {
            sheetId = sheet.sheet_id
        };
        var result = msg.Length > 0 ? "Error" : "OK";
        return new(result, msg, response);

        void LogError(string msg, Exception e)
        {
            NLogger.Error("[收银系统][SubmitSheet] " + msg +
                $"\ninfo - company: {companyId}, oper: {operId}" + 
                $"\ndata - {JsonConvert.SerializeObject(data)}" +
                $"\nerror - {e}");
        }
    }

    [HttpPost]
    public async Task<CallResult> MarkPrintResult([FromBody] dynamic data)
    {
        //这个接口暂时没有用到，因为目前的逻辑是打印成功才标记次数，所以直接用api/Printer/PrintMark了
        string operKey = data.operKey;
        string sheetId = data.sheetId;
        string sheetType = data.sheetType;
        string printResult = data.printResult;
        bool printSuccess = data.printSuccess;
        Security.GetInfoFromOperKey(operKey, out string companyId, out string operId);

        // 获取flow_id并记录打印结果
        bool printSum = false;
        bool printEach = true;
        bool printOpenStock = false;
        SQLQueue QQ = new SQLQueue(cmd);
        string logFlowID = "", sql = "", err = "";

        try
        {
            string print_sum = printSum ? printSum.ToString().ToLower() : "null";
            string print_sheet = printEach ? printEach.ToString().ToLower() : "null";
            string print_open_stock = printOpenStock ? printOpenStock.ToString().ToLower() : "null";
            sql = @$"
            insert into
                print_log (
                    company_id, oper_id, happen_time, sheet_type, 
                    print_sum, print_sheet, print_open_stock, sheet_ids, print_result
                )
            values (
                '{companyId}', '{operId}', '{CPubVars.GetDateText(DateTime.Now)}', '{sheetType}',
                {print_sum} ,{print_sheet}, {print_open_stock}, '{sheetId}', '{printResult}'
            )
            returning flow_id;";
            cmd.CommandText = sql;
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
                logFlowID = ov.ToString();
        }
        catch (Exception e)
        {
            err = "标记失败(5001)";
            NLogger.Error("[收银系统][MarkPrintResult] 数据库操作失败" +
                $"\nsql - {sql}" +
                $"\nerror - {e}");
            throw;
        }

        if (err.IsValid())
            return new("Error", err);
        else if (logFlowID.IsInvalid())
            return new("Error", "意外错误(5001)", "未获取到flow_id");

        // 标记打印次数(仅打印成功时)
        if (printSuccess)
        {
            // todo
        }

        return new("OK", "");
    }

    [HttpPost]
    public async Task<CallResult<SheetSale>> LoadSheet([FromBody] dynamic data)
    {
        string operKey = data.operKey;
        string sheetId = data.sheetId;
        Security.GetInfoFromOperKey(operKey, out string companyId, out string operId);
        if (sheetId.IsInvalid())
            return new("Error", "未找到单据");

        var sheet = new SheetSale();
        try
        {
            await sheet.Load(cmd, companyId, sheetId);
            return new("OK", "", sheet);
        }
        catch (Exception e)
        {
            NLogger.Error($"{companyId}在加载单据'id:{sheetId}'时发生错误：{e}");
            return new("Error", "单据加载失败" + e.Message);
        }
    }

    [HttpPost]
    public async Task<CallResult<dynamic>> LoadHistorySheets([FromBody] dynamic data)
    {
        string operKey = data.operKey;
        Security.GetInfoFromOperKey(operKey, out string companyId);
        int pageSize = data.pageSize;
        int pageIndex = data.pageIndex;

        string startTime = data.startTime;
        string endTime = data.endTime;
        string timeCondi = "and m.happen_time >= NOW() - INTERVAL '1 MONTH'";
        if (!string.IsNullOrEmpty(startTime) && !string.IsNullOrEmpty(endTime))
            {
            timeCondi = $@"and m.happen_time >= '{startTime}' and m.happen_time <= '{endTime}'";
        }

        var condi = $@"
            m.company_id = {companyId} 
            and m.order_source = 'cashier'
            and m.red_flag is null
            {timeCondi}
        ";

        int offset = (pageIndex - 1) * pageSize;
        string totalCountSql = $@"
            SELECT
                COUNT(*) as total_sheet_count
            FROM
                sheet_sale_main m
            WHERE
                {condi};
        ";
        string totalSheetInfoSql = $@"
            SELECT
               count(sheet_id) as total,
               sum(total_amount * money_inout_flag) as total_amount, 
               sum(now_disc_amount * money_inout_flag) as now_disc_amount, 
               sum(now_pay_amount * money_inout_flag - coalesce(prepay_amount,0) * money_inout_flag ) as real_pay_amount
            FROM
                sheet_sale_main m
            WHERE
                {condi};
        ";
        string paginatedResultsSql = $@"
            SELECT
                m.*,
                maker.oper_name as maker_name
            FROM
                sheet_sale_main m
                LEFT JOIN info_operator maker on m.company_id = maker.company_id and m.maker_id = maker.oper_id
            WHERE
                {condi}
            ORDER BY
                happen_time DESC
            OFFSET {offset}
            LIMIT {pageSize};
        ";
        
        

        var loadTotalResponse = await GetSingleRecord(totalCountSql);
        if (!loadTotalResponse.IsOK)
            return loadTotalResponse;
        var loadPageResponse = await GetSingleRecord(paginatedResultsSql);
        if (!loadPageResponse.IsOK)
            return loadPageResponse;
        
        var totalSheetInfoResponse = await GetSingleRecord(totalSheetInfoSql);
        if (!totalSheetInfoResponse.IsOK)
            return totalSheetInfoResponse;

        int.TryParse(loadTotalResponse.data?[0]?.total_sheet_count ?? "0",
            out int total_sheet_count);

        dynamic totalSheetInfo = null;
        if (totalSheetInfoResponse.data.Count == 1)
        {
            totalSheetInfo = totalSheetInfoResponse.data[0];
        }

        var result = new
        {
            pageTotal = total_sheet_count,
            pageContent = loadPageResponse.data,
            totalSheetInfo
        };
        return new("OK", "", result);
    }

    private async Task<CallResult<dynamic>> GetSingleRecord(string sql)
    {
        try
        {
            var QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
            using var dr = await QQ.ExecuteReaderAsync();

            List<ExpandoObject> data = null;
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            dr.Close();
            QQ.Clear();

            return new("OK", "", data);
        }
        catch (Exception e)
        {
            NLogger.Error("[收银系统][GetSingleRecord] 数据库操作失败" +
                $"\nsql - {sql}" +
                $"\nerror - {e}");
            return new("Error", "读取失败(5000)");
        }
    }
}
