﻿using ArtisanManage.Models;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Services;
using Microsoft.AspNetCore.JsonPatch.Internal;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
 
using static ArtisanManage.Services.CommonTool;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace ArtisanManage.AppController
{
    ///商品
    ///商品类别
    ///商品档案
    ///单个商品详情
    ///保存商品详情修改



    /// <summary>
    /// 商品
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class SelectOptionsProvider : QueryController
    {
        public static string ToFieldValue(dynamic d)
        {
            string fld = (string) d;
            return String.IsNullOrEmpty(fld) ? "null" : $"'{fld}'";
        }
        private readonly IHttpClientFactory _httpClientFactory;
        public SelectOptionsProvider(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }


        /// <summary>
        /// 获取品牌ID,名称
        /// </summary>
        /// <param name="operKey"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetOptionsForSelect(string operKey, string target, string departID, bool bViewSelf, string sellerID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID);
            if (target == "sender")
            {
                return await GetSenders(companyID, operID, departID, bViewSelf);
            }
            else if (target == "seller")
            {
                return await GetSellers(companyID, operID, departID, bViewSelf);
            }
            else if (target == "worker")
            {
                return await GetWorkers(companyID, operID, departID, bViewSelf);
            }
            else if (target == "brand")
            {
                return await GetBrands(companyID, operID, departID, bViewSelf);
            }
            else if (target == "group")
            {
                return await GetGroups(companyID, operID, departID, bViewSelf);
            }
            else if (target == "rank")
            {
                return await GetRanks(companyID, operID, departID, bViewSelf);
            }
            else if (target == "day")
            {
                return await GetDaySchedual(companyID, sellerID, departID, bViewSelf);
            }
            else if (target == "arrears" || target == "pay")
            {
                return await GetAcctWay(companyID, target);
            }
            else if (target == "supplier")
            {
                // info_supcust where supcust_flag in ('S')
                return await GetSupplier(companyID);
            }
            else if (target == "brief")
            {
                return await GetBriefs(companyID, operID, departID, bViewSelf);
            }
            else
            {
                string result = "OK";
                string msg = "未匹配到相应参数";
                return new JsonResult( new { result, msg } );
            }       
            
        }
        public async Task<JsonResult> GetAcctWay(string companyID, string target)
        {
            var sql = $"SELECT acct_way_id id,acct_way_name as name FROM info_acct_way where company_id = {companyID} and acct_type = '{target}' ";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

        public async Task<JsonResult> GetSenders(string companyID,string operID, string departID, bool bViewSelf)
        {
            var condi = "";
            if (bViewSelf) condi += $" and oper_id = {operID}";
            if (departID != null) condi += $" and depart_path  like '/%{departID}%/'";
            var sql = $"SELECT oper_id id,oper_name as name FROM info_operator where company_id = {companyID} {condi} and COALESCE(status,'1')='1' and is_sender ";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

        public async Task<JsonResult> GetSellers(string companyID,string operID, string departID, bool bViewSelf)
        { 
            var condi = "";
            //if (bViewSelf) condi += $" and oper_id = {operID}";
            if (departID != null) condi += $" and depart_path  like '/%{departID}%/'";
            var sql = $"SELECT oper_id id,oper_name as name FROM info_operator where company_id = {companyID} {condi} and is_seller and COALESCE(status,'1')='1'";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

        public async Task<JsonResult> GetWorkers(string companyID, string operID, string departID, bool bViewSelf)
        {
            var condi = "";
            if (bViewSelf) condi += $" and oper_id = {operID}";
            if (departID != null) condi += $" and depart_path  like '/%{departID}%/'";
            var sql = $"SELECT oper_id id,oper_name as name FROM info_operator where company_id = {companyID} {condi} and COALESCE(status,'1')='1'";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

        public async Task<JsonResult> GetSupplier(string companyID)
        { 
            var sql = $"SELECT supcust_id id, sup_name as name FROM info_supcust where company_id = {companyID} and supcust_flag in ('S') and COALESCE(status,'1')='1'";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }
        public async Task<JsonResult> GetBrands(string companyID,string operID,string departID,bool bViewSelf)
        {
            var condi = "";
            if (bViewSelf) condi += $" and oper_id = {operID}";
            if (departID != null) condi += $" and depart_id  = {departID}";
            var sql = $"SELECT brand_id id,remark,brand_status status,brand_order_index order_index,brand_name as name FROM info_item_brand where company_id = {companyID} and (brand_status = '1' or brand_status is null)";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }
        public async Task<JsonResult> GetGroups(string companyID,string operID,string departID,bool bViewSelf)
        {
            var condi = "";
            if (bViewSelf) condi += $" and oper_id = {operID}";
            if (departID != null) condi += $" and depart_id  = {departID}";
            var sql = $"SELECT group_id id,group_name as name,remark FROM info_supcust_group where company_id = {companyID}";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }
        public async Task<JsonResult> GetRanks(string companyID, string operID, string departID, bool bViewSelf)
        {
            var condi = "";
            if (bViewSelf) condi += $" and oper_id = {operID}";
            if (departID != null) condi += $" and depart_id  = {departID}";
            var sql = $"SELECT rank_id id,rank_name as name,rank_note note FROM info_supcust_rank where company_id = {companyID}";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

        public async Task<JsonResult> GetDaySchedual(string companyID, string sellerID, string departID, bool bViewSelf)
        {
            var sql = $"SELECT day_id as id,day_name as name FROM info_visit_day WHERE schedule_id = ANY (SELECT unnest(string_to_array(multi_schedule_id, ',')::int[]) FROM info_operator WHERE company_id = {companyID} AND oper_id = {sellerID});";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

        public async Task<JsonResult> GetBriefs(string companyID, string operID, string departID, bool bViewSelf)
        {
            var condi = "";
            if (bViewSelf) condi += $" and oper_id = {operID}";
            if (departID != null) condi += $" and depart_id  = {departID}";
            var sql = $@"select brief_id id,brief_text as name from info_sheet_detail_brief where company_id = {companyID};";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }
    }
}
