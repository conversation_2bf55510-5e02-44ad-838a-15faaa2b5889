using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;
using ArtisanManage.Services;
using Microsoft.CodeAnalysis.Operations;
using System.Dynamic;
using System.Collections;
using NPOI.SS.Formula.Functions;
using Newtonsoft.Json;
using static ArtisanManage.Pages.BaseInfo.ItemEditModel;
using static NPOI.HSSF.Util.HSSFColor;
using System.ComponentModel.Design;

namespace ArtisanManage.Pages.BaseInfo 
{
    public class BranchEditModel : PageFormModel
    {
        public class BranchPositionGrid : FormDataGrid
        {
            public override void OnGridDataGot(List<Dictionary<string, dynamic>> lstRows)
            {

                foreach (var row in lstRows)
                {
                    Dictionary<string, string> newValues = new Dictionary<string, string>();
                    foreach (var cell in row)
                    {
                        if ("wholesale_price,retail_price,buy_price,cost_price_spec,cost_price_avg".Contains(cell.Key))
                        {
                            string s = cell.Value;
                            if (s.EndsWith(".00"))
                                s = s.Replace(".00", "");
                            newValues.Add(cell.Key, s);
                        }
                    }
                    foreach (var cell in newValues)
                    {
                        row[cell.Key] = cell.Value;
                    }
                }
                
            }
        }
        public static string getPositionStatusSql()
        {
            return @$"select '正常' as cls_status_name,1 as position_status
union select '停用' as cls_status_name,0 as position_status";
        }
        public BranchEditModel(CMySbCommand cmd,string company_id="",string oper_id=""):base(Services.MenuId.infoBranch){
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            this.LogChange = true;
            this.PageTitle = "仓库";
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                
                {"branch_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"branch_name",new DataItem(){Title="仓库名称",Necessary=true,FldArea="divHead"}},
                {"order_index",new DataItem(){Title="顺序号",FldArea="divHead"}},
                {"py_str",new DataItem(){Title="助记名",FldArea="divHead"}},
                {"branch_addr",new DataItem(){Title="仓库地址",FldArea="divHead"}},
                {"branch_type",new DataItem(){Title="仓库类型",LabelFld="branch_type_name",FldArea="divHead",LabelInDB=false,Value="store",Label="仓库", ButtonUsage="list", DropDownHeight="80",DropDownWidth="150",
                    Source = "[{v:'store',l:'仓库'},{v:'truck',l:'车辆'},{v:'approaching',l:'临期'}]"}},
                {"status",new DataItem(){Title="状态",LabelFld="cls_status_name",FldArea="divHead",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]",DropDownHeight="80",DropDownWidth="150"}},
                {"negative_stock_accordance",new DataItem(){Title="负库存依据",LabelFld="negative_stock_accordance_name",FldArea="divHead",LabelInDB=false,Value="usable",Label="可用库存", ButtonUsage="list", DropDownHeight="80",DropDownWidth="150",
                    Source = "[{v:'real',l:'实际库存'},{v:'usable',l:'可用库存'}]",Hidden=true}},
                {"allow_negative_stock",new DataItem(){FldArea="divHead",Title="允许负库存",CtrlType="jqxCheckBox",Value="true"}},
                {"allow_negative_stock_order",new DataItem(){FldArea="divHead",Title="订单允许负库存",CtrlType="jqxCheckBox",Value="true"}},
                {"for_contract_seller",new DataItem(){FldArea="divHead",Title="承包仓库",CtrlType="jqxCheckBox",Value="false"}},
            };
 
            m_idFld = "branch_id"; m_nameFld = "branch_name";
            m_tableName = "info_branch";
            m_selectFromSQL = "from info_branch where branch_id='~ID'";
            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridPosition" ,new BranchPositionGrid(){

                   MinRows=3,AutoAddRow=true,
                   AllowDragRow=true,
                   AllowInsertRemoveRow=true,
                   Height=200,
                   Columns = new Dictionary<string, DataItem>()
                   {
                       {"branch_id",new DataItem(){Title="仓库",Width="120",CtrlType="hidden",Hidden=true}},
                       {"branch_position",new DataItem(){Title="库位",Width="120",CtrlType="hidden",Hidden=true,SaveToDB=false}},
                       {"branch_position_name",new DataItem(){Title="库位名",Necessary=true}},
                       {"type_id",new DataItem(){Title="库位类型名",Width="200",LabelFld="type_name",ButtonUsage="list",
                       SqlForOptions="select type_id ,type_name from info_branch_position_type where company_id = ~COMPANY_ID"}},
                      {"l",new DataItem(){Title="状态",Width="120",SqlFld="(case WHEN position_status='0' THEN '停用' ELSE '正常' END)", GetFromDb=true,Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]",
                      ButtonUsage="list",DropDownHeight="80",DropDownWidth="80" }},
                   },
                   TableName="info_branch_position",
                   IdFld="branch_id",
                   SelectFromSQL=@"from info_branch_position 
left join info_branch ib on ib.company_id = ~COMPANY_ID and ib.branch_id = info_branch_position.branch_id
left join info_branch_position_type ibpt on ibpt.type_id = info_branch_position.type_id and  ibpt.company_id = ~COMPANY_ID 
where info_branch_position.company_id =  ~COMPANY_ID and info_branch_position.branch_id = '~ID'
"
                }}

            };
        }

        

        public async Task OnGet()
        {       
            await InitGet(cmd);   
        }
        //public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        //{
        //    if (Grids["gridPosition"].Columns["v"].Value == "0")
        //    {
        //        Grids["gridPosition"].Columns["v"].Label = "停用";
        //    }
        //    else
        //    {
        //        Grids["gridPosition"].Columns["v"].Label = "正常";
        //    }
        //}
        public async Task<string> CheckBeforeDeleteRecords(string branch_id,string branch_position)
        {
            string err = "";
            //cmd.CommandText = $"select company_id from stock where branch_id in ({rowIDs}) and company_id={company_id} limit 1";
            if (branch_id.IsInvalid()) return "仓库不存在";
            cmd.CommandText = $@"
               select * from info_braanch_position where branch_id = {branch_id} and branch_position = {branch_position} and company_id = {company_id}
             ";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                return "已产生库存记录,无法删除";
            }

            return err;
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class BranchEditController : BaseController
    { 
        public BranchEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues)
        {
            BranchEditModel model = new BranchEditModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            BranchEditModel model = new BranchEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.Grids[gridID].Columns, colName, flds, value,availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            BranchEditModel model = new BranchEditModel(cmd);
            string branchID = request.branch_id.ToString();
            if (branchID.IsInvalid()) branchID = "-1";
            cmd.ActiveDatabase = "";
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            Security.GetInfoFromOperKey(request.operKey.ToString(), out string companyID);
            string sqlForBranchPosition = "";
            dynamic res = null;
      
            if(branchID == "-1")
            {
                foreach (var row in request["gridPosition"])
                {
                    string typeId = row["type_id"];
                    if (typeId.IsInvalid()) return new JsonResult(new { result = "ERROR", msg = $@"请选择{row["branch_position_name"]}的库位类型", });
                    string branchPosition = row["branch_position"];
                    string branchPositionName = row["branch_position_name"];
                    string positionStatus = row["l"];
                    positionStatus = positionStatus.IsInvalid() ? "1" : (positionStatus=="正常"?"1":"0");
                    sqlForBranchPosition += $@"insert into info_branch_position (company_id,branch_id,branch_position_name,type_id,position_status) values ({companyID},{"@branchID"},'{branchPositionName}',{typeId},{positionStatus});";
                }
                request["gridPosition"] = null;
               // dynamic dd = await model.SaveTable(cmd, request, tran);
              //  res = dd.Value;

                res = (await model.SaveTable(cmd, request,tran)).Value;
                 
                branchID = res.record.branch_id;
                sqlForBranchPosition = sqlForBranchPosition.Replace("@branchID", branchID);
            }
            else
            {
                string sql = $@"select branch_position from info_branch_position where branch_id = {branchID} and company_id = {companyID};";
                dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                string branch_positions = "";
                if (records.Count != 0)
                {
                    if (request["gridPosition"].Count < records.Count)
                    {
                        foreach(dynamic re in records)
                        {
                            bool isDeleted = true;
                            foreach (var row in request["gridPosition"])
                            {
                                if (row["branch_position"] == re.branch_position.ToString())
                                {
                                    isDeleted = false;
                                    break;
                                }
                            }
                            if (isDeleted)
                            {
                                if (branch_positions == "") branch_positions = re.branch_position.ToString();
                                else branch_positions += "," + re.branch_position.ToString();
                            }
                        }
                        if (branch_positions != "")
                        {
                            cmd.CommandText = $@"
                    select company_id from stock where branch_id ={request.branch_id} and branch_position in ({branch_positions}) and company_id={companyID}
                    union select d.company_id from sheet_sale_detail d left join sheet_sale_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where COALESCE(d.branch_id,m.branch_id) ={request.branch_id} and branch_position in ({branch_positions}) and d.company_id={companyID} 
                    union select d.company_id from sheet_sale_order_detail d left join sheet_sale_order_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where COALESCE(d.branch_id,m.branch_id) ={request.branch_id} and branch_position in ({branch_positions}) and d.company_id={companyID} 
                    union select d.company_id from sheet_buy_detail d left join sheet_buy_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where COALESCE(d.branch_id,m.branch_id) ={request.branch_id} and branch_position in ({branch_positions}) and d.company_id={companyID}
                    union select d.company_id from sheet_buy_order_detail d left join sheet_buy_order_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where COALESCE(d.branch_id,m.branch_id) ={request.branch_id} and branch_position in ({branch_positions}) and d.company_id={companyID}
                    union select d.company_id from sheet_move_detail d left join sheet_move_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where (from_branch_id ={request.branch_id} or to_branch_id = {request.branch_id}) and (from_branch_position in (
                {branch_positions}) or from_branch_position in ({branch_positions})) and d.company_id={companyID} limit 1
                 ";
                            object ov = await cmd.ExecuteScalarAsync();
                            if (ov != null && ov != DBNull.Value)
                            {
                                return new JsonResult(new { result = "ERROR", msg = "已产生库存记录,无法删除", });
                            }
                            else
                            {
                                sqlForBranchPosition += $@"delete from info_branch_position where company_id = {companyID} and branch_id = {branchID} and branch_position in ({branch_positions}); ";
                            }
                        }
                    }
                }
                foreach (var row in request["gridPosition"])
                {
                    string typeId = row["type_id"];
                    if (typeId.IsInvalid()) return new JsonResult(new { result = "ERROR", msg = $@"请选择{row["branch_position_name"]}的库位类型", });
                    string branchPosition = row["branch_position"];
                    string branchPositionName = row["branch_position_name"];
                    string positionStatus = row["l"];
                    positionStatus = positionStatus.IsInvalid() ? "1" : (positionStatus=="正常"?"1":"0");
                    if (branchPosition.IsInvalid())//insert
                    {
                        sqlForBranchPosition += $@"insert into info_branch_position (company_id,branch_id,branch_position_name,type_id,position_status) values ({companyID},{branchID},'{branchPositionName}',{typeId},{positionStatus});";
                    }
                    else//update
                    {
                        sqlForBranchPosition += $@"update info_branch_position set type_id = {typeId},branch_position_name = '{branchPositionName}',position_status={positionStatus} where company_id = {companyID} and branch_id={branchID} and branch_position={branchPosition};";
                    }
                }
                request["gridPosition"] = null;
                res = (await model.SaveTable(cmd, request,tran)).Value;
              
            }
            if(res.result == "OK" && sqlForBranchPosition!="")
            {
                try
                {
                    cmd.CommandText = sqlForBranchPosition;
                    await cmd.ExecuteNonQueryAsync();
                }
                catch(Exception e)
                { 
                    res.result = "ERROR";
                    res.msg = "保存库位出错";
                }
            }
            if(res.result == "OK")
            {
                tran.Commit();
            }
            else
            {
                tran.Rollback();
            }

            return new JsonResult(res);

        }
    }
}