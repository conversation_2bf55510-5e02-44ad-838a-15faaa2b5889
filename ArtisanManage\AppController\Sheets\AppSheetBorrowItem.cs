﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using ArtisanManage.Services.SheetService;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using ArtisanManage.WebAPI.MessageOSUtil;
using ArtisanManage.YingjiangMessage.Pojo;
using ArtisanManage.YingjiangMessage.Services;
using Microsoft.EntityFrameworkCore.Internal;
using Newtonsoft.Json.Linq;
using JsonSerializer = System.Text.Json.JsonSerializer;
using ArtisanManage.YingJiangCommon.Controller.PromotionController;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.WebAPI;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using System.ComponentModel.Design;

namespace ArtisanManage.AppController.Sheets
{ 
    [Route("AppApi/[controller]/[action]")]
    public class AppSheetBorrowItem : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public AppSheetBorrowItem(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }



        /// <summary>
        /// 加载单据--返回--支付方式,备注
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID, string sheetType)
        {
			Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
			var sheet_type = sheetType == "HH" ? SHEET_BORROW_RETURN.IS_RETURN : SHEET_BORROW_RETURN.NOT_RETURN;
			SheetBorrowItem sheet = new SheetBorrowItem(sheet_type, LOAD_PURPOSE.SHOW);
			await sheet.Load(cmd, companyID, sheetID);
			await sheet.LoadInfoForPrint(cmd, true);

			/*var sql = @$"select s.sub_id,sub_name,sub_type as payway_type,order_index payway_index,qrcode_uri,'false' is_ks from cw_subject s left join info_pay_qrcode q on s.sub_id = q.sub_id and s.company_id = q.company_id where s.company_id = {companyID} and is_order is not true and sub_type in ('QT','YS') 
                            union 
                         select sub_id,sub_name,sub_type as payway_type,order_index payway_index,''qrcode_uri,'true' is_ks from  company_setting s 
                        left join cw_subject c on s.setting->>'feeOutSubForKS' = c.sub_id::text and s.company_id = c.company_id where s.company_id = {companyID} order by payway_index
                        ";
            */
			SQLQueue QQ = new SQLQueue(cmd);
			//        string restrictPayWayCondi = $@" (s.sub_id::text IN (
			//        SELECT 
			//            json_array_elements_text(avail_pay_ways) AS individual_value 
			//        FROM 
			//            info_operator 
			//        WHERE  company_id = ~COMPANY_ID AND oper_id = ~OPER_ID AND restrict_pay_ways = TRUE 
			//        )
			//    OR
			//    (   SELECT 
			//            COUNT(*) 
			//        FROM 
			//            info_operator 
			//        WHERE  company_id = ~COMPANY_ID AND oper_id = ~OPER_ID AND restrict_pay_ways = TRUE ) = 0 
			//)";
			var sql = @$"
SELECT * FROM
(
    select s.sub_id,sub_name,sub_type as payway_type,order_index as payway_index,'false' is_ks,qrcode_uri,q.pay_channel_id, pc.channel_name as pay_channel_name
    from cw_subject s
    left join info_pay_qrcode q on s.company_id = q.company_id and s.sub_id = q.sub_id
    left join pay_channel pc on pc.channel_id = q.pay_channel_id
    where s.company_id = {companyID} and s.is_order is not true and (sub_type in ('YS') or (sub_type='ZC' and for_pay) or (sub_type='QT' and coalesce(for_pay,true))) and coalesce(s.status, '1') = '1'
    union 
    select sub_id,sub_name,sub_type as payway_type,order_index as payway_index,'true' is_ks,'',null,''
    from company_setting c
    left join cw_subject s on c.company_id = s.company_id and c.setting->>'feeOutSubForKS' = s.sub_id::text where c.company_id = {companyID} and c.setting->>'feeOutSubForKS' is not null 
) t  
where (sub_id::text IN (
            SELECT 
                json_array_elements_text(avail_pay_ways) AS individual_value 
            FROM 
                info_operator 
            WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
            )
        OR
        (   SELECT 
                COUNT(*) 
            FROM 
                info_operator 
            WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
    )
order by case payway_type when 'QT' then 0 when 'YS' then 1 else 2 end, payway_index;
                        ";

			QQ.Enqueue("payways", sql);
			//sql = $@"select brief_id,brief_text,default_for_give from info_sheet_detail_brief where company_id = {companyID} and sheet_type = '{sheetType}';";
			//QQ.Enqueue("briefInfo", sql);
			sql = $@"select oper_id as senders_id,oper_name as senders_name  from info_operator where company_id = {companyID} and is_sender  and COALESCE(status,'1')='1' order by oper_name";
			QQ.Enqueue("sendersInfo", sql);
			/*sql = @$" 
                    SELECT array_to_json(array_agg(row_to_json(t))) attr_options FROM
                    (
                        SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index
                    ) t";*/
			sql = @$"SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={companyID} and not attr.spec_opt_in_item order by opt.order_index";
			QQ.Enqueue("attr_options", sql);
			if (!string.IsNullOrEmpty(sheetID))
			{   // 小程序查看单据增加红包使用详情，后续可拓展其他平台的信息显示
				sql = @$"
select flow_id, 
       happen_time, 
       change_amount, 
       change_type 
from red_packet_history 
where company_id = {companyID} 
  and relate_sheet_id = {sheetID} and relate_sheet_type = 'X' 
order by happen_time desc
";
				QQ.Enqueue("red_packet_history", sql);
			}

			List<ExpandoObject> payways = null;
			List<ExpandoObject> brief = null;
			List<ExpandoObject> senders = null;
			List<ExpandoObject> attrOptions = null;
			List<ExpandoObject> red_packet_history = null;
			var dr = await QQ.ExecuteReaderAsync();
			while (QQ.Count > 0)
			{
				var sqlName = QQ.Dequeue();
				if (sqlName == "payways")
				{
					payways = CDbDealer.GetRecordsFromDr(dr, false);
				}
				else if (sqlName == "briefInfo")
				{
					brief = CDbDealer.GetRecordsFromDr(dr, false);
				}
				else if (sqlName == "sendersInfo")
				{
					senders = CDbDealer.GetRecordsFromDr(dr, false);
				}
				else if (sqlName == "attr_options")
				{
					attrOptions = CDbDealer.GetRecordsFromDr(dr, false);
				}
				else if (sqlName == "red_packet_history")
				{
					red_packet_history = CDbDealer.GetRecordsFromDr(dr, false);
				}
				/*
                else if (sqlName == "attr_options")
                {
                    dr.Read();
                    string s = CPubVars.GetTextFromDr(dr, "attr_options");
                    if (s != "")
                    {
                        attrOptions = JsonConvert.DeserializeObject(s);
                    }
                }*/
			}

			string result = "OK";
			string msg = "";
			return Json(new { result, msg, sheet, payways, brief, senders, attrOptions, red_packet_history });
		}

        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic dSheet)
        {
            SheetBorrowItem sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);

            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetBorrowItem>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetBorrowItemSave.Submit:" + msg + sSheet);
                MyLogger.LogMsg("in AppSheetBorrowItemSave.Submit:" + msg + sSheet, Token.CompanyID);

                msg = "提交失败,请联系技术支持";
           
                return new JsonResult(new { result = "Error", msg });
            }
            if (msg == "")
            {
                cmd.ActiveDatabase = "";
              
                sheet.Init();

                //if (dSheet.appendixPhotos != null)
                //{
                //    List<string> appendixBase64s = new List<string>();
                //    foreach (string appendixPhoto in dSheet.appendixPhotos)
                //    {
                //        // if (!CommonTool.IsBase64(appendixPhoto))
                //        // {
                //        //      continue;
                //        //   }
                //        //   else {
                //        appendixBase64s.Add(appendixPhoto);
                //        //   }
                //    }
                //    sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());
                //}
                if (dSheet.displayGiveProofs != null)
                {
                    Security.GetInfoFromOperKey((string)dSheet.operKey, out string companyID, out string operID);
                    string subType = "";
                    string sheetType = dSheet.sheetType;
                    string order_sheet_id = dSheet.order_sheet_id;
                    string supcust_id = dSheet.supcust_id;
                    dynamic displayGiveProofs = dSheet.displayGiveProofs;
            
                    if (sheetType.Equals("XD"))
                    {
                        subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSellerSubType.SubTypeKey;
                    } else if (sheetType.Equals("X") && (!string.IsNullOrEmpty(order_sheet_id)))
                    {
                        subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSenderSubType.SubTypeKey;
                    } else if (sheetType.Equals("X") && (string.IsNullOrEmpty(order_sheet_id)))
                    {
                        subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayCxGiveSubType.SubTypeKey;
                    }
                    
                   
                    
                    //foreach (dynamic displayGiveProofItem in displayGiveProofs)
                    //{
                    //    string disp_sheet_id = displayGiveProofItem.disp_sheet_id;
                    //    string uploadsMainPath = $@"{dSheet.company_id}_{sheetType}_{subType}_{operID}_{supcust_id}_{disp_sheet_id}";
                    //    var workContent = displayGiveProofItem.work_content;
                    //    string work_content = JsonConvert.SerializeObject(workContent);
                    //    string work_content_result = await ActionsTemplateUtils.HandleActionTemplate(work_content, uploadsMainPath, _httpClientFactory);
                    //    dynamic work_content_obj = JsonConvert.DeserializeObject(work_content_result);
                    //    displayGiveProofItem.work_content = work_content_obj;
                    //}
                    //sheet.display_give_proofs =  JsonConvert.SerializeObject(dSheet.displayGiveProofs);
                }
               
                sheet.promotion_fulldisc_used = dSheet.promotion_fulldisc_used ?? false;
                sheet.promotion_fullgift_content = dSheet.promotion_fullgift_content ?? "";
                //sheet.TempHappenTime = false;
                msg = await sheet.Save(cmd);
            }
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.make_time, sheet.happen_time, currentTime });
        }
        /// <summary>
        /// 提交销售单
        /// </summary>
        /// <param name="dSheet"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            SheetBorrowItem sheet = null;
          //  dSheet.appendixPhotos = this.ProcessAppendixPicsRetDBStr(dSheet.appendixPhotos,dS);
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetBorrowItem>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Submit:" + msg + sSheet);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            if (msg == "")
            {
              
                cmd.ActiveDatabase = "";
                sheet.Init();
                sheet._httpClientFactory = this._httpClientFactory;
                if (dSheet.appendixPhotos != null) {
                    List<string> appendixBase64s = new List<string>();
                    foreach (string appendixPhoto in dSheet.appendixPhotos)
                    {
                    // if (!CommonTool.IsBase64(appendixPhoto))
                    // {
                    //      continue;
                    //   }
                    //   else {
                        appendixBase64s.Add(appendixPhoto);
                    //   }
                    }
                    sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());
                } 
                if (dSheet.displayGiveProofs != null)
                {
                    Security.GetInfoFromOperKey((string)dSheet.operKey, out string companyID, out string operID);
                    string sheetType = dSheet.sheetType;
                    string order_sheet_id = dSheet.order_sheet_id;
                    string supcust_id = dSheet.supcust_id;
                    dynamic displayGiveProofs = dSheet.displayGiveProofs;
                    string subType = "";
                    string subTypeName = "";
                    if (sheetType.Equals("XD"))
                    {
                        subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSellerSubType.SubTypeKey;
                    } else if (sheetType.Equals("X") && (!string.IsNullOrEmpty(order_sheet_id)))
                    {
                        subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSenderSubType.SubTypeKey;
                    } else if (sheetType.Equals("X") && (string.IsNullOrEmpty(order_sheet_id)))
                    {
                        subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayCxGiveSubType.SubTypeKey;
                    }
                   
                    foreach (dynamic displayGiveProofItem in displayGiveProofs)
                    {
                        string disp_sheet_id = displayGiveProofItem.disp_sheet_id;
                        string uploadsMainPath = $@"{dSheet.company_id}_{sheetType}_{subType}_{operID}_{supcust_id}_{disp_sheet_id}";
                        var workContent = displayGiveProofItem.work_content;
                        string work_content = JsonConvert.SerializeObject(workContent);
                        string work_content_result = await ActionsTemplateUtils.HandleActionTemplate(work_content, uploadsMainPath, _httpClientFactory);
                        dynamic work_content_obj = JsonConvert.DeserializeObject(work_content_result);
                        displayGiveProofItem.work_content = work_content_obj;
                    }
                    sheet.display_give_proofs =  JsonConvert.SerializeObject(dSheet.displayGiveProofs);
                }
                if (sheet.isRedAndChange)
                {
                    if (!sheet.old_sheet_id.IsValid())
                    {
                        msg = "冲改时没有获取原单据的编号";
                    }
                    else
                    {
                        msg = await sheet.RedAndChange<SheetBorrowItem>(cmd);
                    }
                }
                else 
                {
                    msg = await sheet.SaveAndApprove(cmd);
                }
               
                
            }
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.approve_time, sheet.happen_time, currentTime });
        }
        public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64, string companyID)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, companyID);
            return result;
        }

        private static int SingeImageIndex = 0;
        [HttpPost]
        public async Task<CallResult> SaveSingleImage([FromBody] dynamic parameters)
        {
            try
            {
                string operKey = parameters.operKey;
                string imageBase64 = parameters.imageBase64;
                Security.GetInfoFromOperKey(operKey, out string companyId, out string operId);
                var dt = DateTime.Now;
                if (SingeImageIndex > 65000) { SingeImageIndex = 0; }
                SingeImageIndex++;
                string path = $"uploads/{dt:yyyyMM}/{companyId}_any_{operId}_undef_aXm{SingeImageIndex}_{dt:yyyyMMddHHmmssfff}.jpg";
                string saveResult = await HuaWeiObs.Save(_httpClientFactory, imageBase64, path);
                var result = saveResult.Length == 0 ? "OK" : "Failed";
                path = HuaWeiObs.BucketLinkHref + "/" + path;
                return new CallResult(result, saveResult, path);
            }
            catch (Exception ex)
            {
                NLogger.Error(ex.ToString());
                return new CallResult("Error", "上传图片失败\n" + ex.Message);
            }
        }

        /// <summary>
        /// 红冲销售单
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            string redBrief = data.redBrief;
            string result = "OK"; string msg = null;
            try
            {
                var currentTime = DateTime.Now.ToText();
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                SheetBorrowItem sheet = new SheetBorrowItem(SHEET_BORROW_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
                sheet._httpClientFactory = this._httpClientFactory;
                msg = await sheet.Red(cmd, companyID, sheetID, operID, redBrief);
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }
        }

        /// <summary>
        /// 获取客户借货余额
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="supcustId"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetCustomerBorrowBalance(string operKey, string supcustId)
        {
            string result = "OK";
            string msg = null;
            decimal balance = 0;

            try
            {
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

                if (!string.IsNullOrEmpty(supcustId) && supcustId != "0")
                {
                    string sql = $"select balance from borrow_balance where company_id = {companyID} and supcust_id = {supcustId}";
                    dynamic balanceRec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    if (balanceRec != null)
                    {
                        balance = CPubVars.ToDecimal(balanceRec.balance);
                    }
                }
            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                MyLogger.LogMsg("in GetCustomerBorrowBalance:" + msg, Token.CompanyID);
            }

            return new JsonResult(new { result, msg, balance });
        }

        [HttpGet]
        public async Task<JsonResult> GetBorrowItemPromotions(string operKey, string dept, string group, string rank, string regions,string supcust_id)
        {
            var result = await PromotionController.GetPromotionContentsStatic(cmd, operKey, dept, group, rank, regions, supcust_id, PromotionController.PromotionScope.YjApp);
            return Json(result);
        }
        [HttpGet]
        public async Task<dynamic> SearchBorrowItemPromotions(string operKey, string promotionIDs)
        {
            var promotions = promotionIDs.Split(',').ToList();
            if (promotions.Count == 0)
                return new CallResult("OK", "", "[]");
            foreach (var id in promotions)
            {
                if (!int.TryParse(id, out int _))
                    return new CallResult("Error", "输入不合法");
            }
            var result = await PromotionController.GetPromotionContentsStatic(cmd, operKey,
                "", "", "", "","", PromotionController.PromotionScope.YjApp,
                promotionIDs);
            return result;
        }
        //2024-6-1后删除，废弃了，名字起的不好，代码冗余
        // public async Task<JsonResult> NoNewSheetsAfterChecked(string operKey)
        //{
        //    Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
        //    string msg = "";
        //    SQLQueue QQ=new SQLQueue(cmd);

        //    var sql = $@"select * from sheet_check_sheets_main where company_id={companyID} getter_id={operID} and approve_time and DATE_TRUNC('day', approve_time) = CURRENT_DATE;";
        //    QQ.Enqueue("data", sql);
        //    List<ExpandoObject> data = null;
        //    var dr = await QQ.ExecuteReaderAsync();
        //    while (QQ.Count > 0)
        //    {
        //        var sqlName = QQ.Dequeue();
        //        if (sqlName == "data")
        //        {
        //            data = CDbDealer.GetRecordsFromDr(dr, false);
        //        }
        //    }
        //    dr.Close();
        //    QQ.Clear();
        //    string result = "OK";
        //    if (data != null)
        //    {
        //        result = "Wrong";
        //    }
        //    if (msg != "") result = "Error";
        //    return Json(new {result, msg, data });
        //}

        public async Task<JsonResult> CanApproveSheetToday(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string msg = "";
           
            string today=CPubVars.GetDateTextNoTime(DateTime.Now);
            string checkTime = today + " 15:00";//早交账可能是昨天的。15点以后的一般是当天交账
            var sql = $@"select * from sheet_check_sheets_main where company_id={companyID} and getter_id={operID} and approve_time>= '{checkTime}' and red_flag is null limit 1;";
            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd); 
            if (rec != null)
            {
                msg = "今天已交账,不能再次开单";
            }
             
            string result = "OK"; 
            if (msg != "") result = "Error";
            return Json(new { result, msg});
        }
        /// <summary>
        /// 促销活动 获取商品详情
        /// </summary>
        /// <param name="promotionItemList">[{"item_id":"18977","item_name":"黄瓜","amount":13,"unit_no":"件","orig_price":2},{"item_id":"18976","item_name":"青菜","amount":2,"unit_no":"件","orig_price":2}]</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetPromotionItemsDetail(string operKey, string promotionItemList)
        {
            promotionItemList ??= "";
            dynamic data = null;
            if (promotionItemList.Length > 0)
            {
                Security.GetInfoFromOperKey(operKey, out string companyId);
                var items = JsonConvert.DeserializeObject<List<ExpandoObject>>(promotionItemList);
                var itemIds = new List<string>();
                foreach (dynamic item in items)
                {
                    string itemId = item.item_id;
                    string itemUnit = item.unit_no;
                    if (!itemIds.Contains(itemId) && itemId.IsValid())
                    {
                        itemIds.Add(itemId);
                    }
                }
                string ids = string.Empty;
                foreach(var id in itemIds)
                {
                    if (ids.Length > 0) { ids += ","; }
                    ids += id;
                }
                SQLQueue QQ = new SQLQueue(cmd);
                string sql = $@"
                    SELECT
                        item_id, unit_no, unit_type, unit_factor, wholeBorrowItem_price
                    FROM
                        info_item_multi_unit
                    WHERE
                        company_id = {companyId} AND item_id IN ({ids})
                    ORDER BY
                        item_id DESC;";
                QQ.Enqueue("unit_info", sql);
                List<ExpandoObject> unitInfo = null;
                var dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    var sqlName = QQ.Dequeue();
                    if (sqlName == "unit_info")
                    {
                        // dr.Read();
                        unitInfo = CDbDealer.GetRecordsFromDr(dr, false);
                    }
                }
                dr.Close();
                QQ.Clear();

                // 对SQL获取到的数据进行重新组装,形成一个单位信息词典 uis
                // 格式为 { item_id -> Dict( {unit_no -> obj(unit_type, unit_factor, wholeBorrowItem_price)} ) }
                var uis = new Dictionary<string, Dictionary<string, dynamic>>();
                foreach(dynamic unit in unitInfo)
                {
                    string item_id = unit.item_id ?? "";
                    if (item_id.IsInvalid()) { continue; }
                    string unit_factor_str = unit.unit_factor ?? "";
                    // if (unit_factor_str.IsInvalid()) { continue; }
                    string unit_no = unit.unit_no ?? "无单位";
                    string unit_type = unit.unit_type ?? "";
                    int unit_factor = int.Parse(unit_factor_str);
                    double.TryParse(unit.wholeBorrowItem_price ?? "0", out double wholeBorrowItem_price);
                    var v2 = new { unit_type, unit_factor, wholeBorrowItem_price };
                    if (uis.ContainsKey(item_id))
                    {
                        if (uis[item_id].ContainsKey(unit_no))
                        {
                            // ?
                        }
                        else
                        {
                            uis[item_id].Add(unit_no, v2);
                        }
                    }
                    else
                    {
                        var v1 = new Dictionary<string, dynamic>() { { unit_no, v2 } };
                        uis.Add(item_id, v1);
                    }
                }

                // 根据单位信息词典uis对化为ExpandoObject的items进行改造
                foreach (dynamic item in items)
                {
                    string itemId = item.item_id;
                    string itemUnit = item.unit_no;
                    if (uis.ContainsKey(itemId))
                    {
                        if (uis[itemId].ContainsKey(itemUnit))
                        {
                            string unit_type = uis[itemId][itemUnit].unit_type;
                            int unit_factor = uis[itemId][itemUnit].unit_factor;
                            double wholeBorrowItem_price = uis[itemId][itemUnit].wholeBorrowItem_price;
                            item.orig_price = wholeBorrowItem_price;
                            item.unit_factor = unit_factor;
                        }
                        else
                        {
                            item.unit_factor = 1;
                        }

                        foreach(var ui in uis[itemId])
                        {
                            string unit_type = ui.Value.unit_type;
                            int unit_factor = ui.Value.unit_factor;
                            if (unit_type == "b")
                            {
                                item.b_unit_no = ui.Key;
                                item.b_unit_factor = unit_factor.ToString();
                            }
                            else if (unit_type == "m")
                            {
                                item.m_unit_no = ui.Key;
                                item.m_unit_factor = unit_factor.ToString();
                            }
                            else if (unit_type == "s")
                            {
                                item.s_unit_no = ui.Key;
                            }
                        }
                        /*
                         * row.b_unit_no = ''
                         * row.b_unit_factor = ''
                         * row.m_unit_no = ''
                         * row.m_unit_factor = ''
                         * row.s_unit_no = ''
                        */
                    }
                }

                data = items;

                return Json(new { result = "OK", msg = "", data });
            }
            else
            {
                return Json(new { result = "Error", msg = "无数据", data });
            }
        }

        /// <summary>
        /// get请求参数过长导致报错
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> PostPromotionItemsDetail([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string promotionItemList = data.promotionItemList;
            return await GetPromotionItemsDetail(operKey, promotionItemList);
        }

        /// <summary>
        /// 商品档案列表----返回商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="searchStr">商品名，助记码，商品编号，商品条码 模糊查询</param>
        /// <param name="brandID">品牌ID查询</param>
        /// <param name="classID">分类ID查询</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="branchID">仓库名 （1）</param>
        /// <returns>商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数</returns>


        [HttpGet]
        public async Task<JsonResult> GetItemList(string sourcrSheet,string operKey, string searchStr, string sortFld1, string sortFld2, string sortFld3, string thirdSort, string brandIDs, string classID, int pageSize, int startRow, string branchID, bool showStockOnly, string supcustID, string stockFirst, string seller_dept_path, bool canGiveDisplayCrossDept, string oftenMonths, string fromOtherSheetItemsId, bool noStockAttrSplitShow = false)
        {
            bool firstRequest = false;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $" and (ip.status is null or ip.status='1') ";
            string condiStock = "";
            string crosstabSql = "";
            string orderSql = "";
            string orderSelest = "";
            string borrowSql = "";
            string borrowSelect = "";
            string orderCondi = "";
            string oftenSql = "";
            string selectItemOrderByOptions = "";
            bool fromOtherSheetItemsIdFlag = false;
            string showStockOnlyCondi = "";
            

            Dictionary<string, string> sortValues = new Dictionary<string, string>
            {
                { "item_name", "ip.py_str asc" },
                { "order_index", "item_order_index asc" },
                { "recent_create", "item_id desc" },
                { "stock", "case when stock.stock_qty > 0 then 0 else 1 end" },
                { "no_stock", "case when stock.stock_qty <= 0 then 0 else 1 end" },
                { "more_stock", "stock.stock_qty desc" },
                { "less_stock", "stock.stock_qty asc" },
                /*{ "higherPrice", "" },
                { "lowerPrice", "" },*/
                { "none", "" }
            };

            string sortFlds = "";
            if (sortFld1.IsValid() && sortValues.TryGetValue(sortFld1, out string firstSortField) && !string.IsNullOrWhiteSpace(firstSortField))
            {
                if (sortFlds.IsValid()) sortFlds += ",";
                sortFlds += firstSortField;
            }

            if (sortFld2.IsValid() && sortValues.TryGetValue(sortFld2, out string secondSortField) && !string.IsNullOrWhiteSpace(secondSortField))
            {
                if (sortFlds.IsValid()) sortFlds += ",";
                sortFlds += secondSortField;
            }
            if (sortFld3.IsValid() && sortValues.TryGetValue(sortFld3, out string thirdSortField) && !string.IsNullOrWhiteSpace(thirdSortField))
            {
                if (sortFlds.IsValid()) sortFlds += ",";
                sortFlds += thirdSortField;
            }

            // string sortSQL = "";
            if (sortFlds == "")
            {
                if (stockFirst != null && stockFirst != "")
                {
                    sortFlds = "(case when coalesce(stock.stock_qty, 0) > 0 then 0 else 1 end)";

                }

                if (sortFlds != "") sortFlds += ",";
                sortFlds = "item_order_index,ip.item_id desc";
            }
          //  sortSQL = @$" order by {sortFlds}";


           // sortSQL = sortSQL + @$" {sortFld1SQL} {sortFld2SQL} {sortFld3SQL}";
            

            if (!string.IsNullOrEmpty(searchStr) && classID == "-1" && classID == "borrowItemClass" && classID == "orderItemClass" && classID == "displayItemClass") classID = null;
            
            string ORDER_BY = "order by";
            bool bSearchStrInClass = false;
            //0:全部 -1:常用
            //-1:常用时使用查询。支持查出其他类
            if (!string.IsNullOrEmpty(searchStr) && classID != "0" && classID != "borrowItemClass" && classID != "orderItemClass" && classID != "displayItemClass") bSearchStrInClass = true;
            if (classID != "0" && classID != "-1" &&classID!= "borrowItemClass" && classID != "orderItemClass" && classID != "displayItemClass" && !string.IsNullOrEmpty(searchStr) && !string.IsNullOrEmpty(classID)) {
                ORDER_BY += $" case when ip.other_class like '%/{classID}/%' then 0 else 1 end,";
            }
            //ORDER_BY += @$" {selectItemOrderByOptions} item_order_index,ip.item_name";
            ORDER_BY += @$" {sortFlds}";
  
            if (searchStr.IsValid())
            {
                string b = "%";
                if (searchStr.Length >= 6) b = "";
                string flexStr = CPubVars.GetFlexLikeStr(searchStr);
                string py_flexStr = CPubVars.GetPyStrFlexLikeStr(searchStr);
                condi += $" and (ip.item_name ilike '%{flexStr}%' or ip.py_str ilike '%{py_flexStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%' or s_barcode like '%{searchStr}{b}' or b_barcode like '%{searchStr}{b}' or m_barcode like '%{searchStr}{b}' or ip.mum_attributes::text ilike '%{searchStr}%' or ip.avail_attr_combine::text ilike '%{searchStr}%')";
            }
            if (brandIDs != null && brandIDs != "") condi += $"and (ip.item_brand is null OR ip.item_brand in ({brandIDs})) ";
            if (!bSearchStrInClass && classID != null && classID != "-1" && classID != "0" && classID != "orderItemClass" && classID != "borrowItemClass") condi += $"and ip.other_class like '%/{classID}/%' ";
            // if (!string.IsNullOrEmpty(fromOtherSheetItemsId))
            // {
            //     condi += @$" and ip.item_id::text IN (SELECT unnest(string_to_array('{fromOtherSheetItemsId}', ',')))";
            // }
            // else
            // {
            //     // 判断复制的转单，多口味区分库存需要and son_mum_item is NUL 放开
            //     condi += " and  son_mum_item is NULL ";
            //
            // }
            if (!string.IsNullOrEmpty(fromOtherSheetItemsId))
            {
                condi += @$" and ip.item_id::text IN (SELECT unnest(string_to_array('{fromOtherSheetItemsId}', ',')))";
            }
            else
            {
                // 判断复制的转单，多口味区分库存需要and son_mum_item is NUL 放开
                // 判断如果不区分库存独立行的时候也需要放开
                if (noStockAttrSplitShow)
                {
                    condi += " and  (mum_attributes IS NULL OR NOT EXISTS (SELECT 1  FROM jsonb_array_elements(mum_attributes) AS elem  WHERE elem->>'distinctStock' = 'true')) ";
                }
                else
                {
                    condi += " and  son_mum_item is NULL ";
                }
            }

            string sonItemFile = "s_barcode,m_barcode,b_barcode,ip.mum_attributes, '' as avail_attr_combine_item";
            string showAttrSonItemsJoin = "";
            string whereSearch = "";
            if (noStockAttrSplitShow)
            {
                sonItemFile =
                    @$" 
'' as mum_attributes,
opts as avail_attr_combine_item,
case when opts.value is null then item_name else item_name|| '(' || (opts.value->>'optName') || ')' end as atrr_item_name,
case when opts.value is null then s_barcode else opts.value->>'sBarcode'   end as s_barcode,
case when opts.value is null then m_barcode else opts.value->>'mBarcode' end as m_barcode,
case when opts.value is null then b_barcode else opts.value->>'bBarcode' end as b_barcode
";
                showAttrSonItemsJoin =
                    " LEFT JOIN LATERAL jsonb_array_elements(avail_attr_combine) AS opts on avail_attr_combine is not null ";
                  if (searchStr.IsValid()) {
                        whereSearch = @$"
 and ( CASE WHEN opts.value IS NOT NULL 
 THEN ((opts.value ->> 'sBarcode' LIKE '%{searchStr}%') 
 OR  (opts.value ->> 'bBarcode' LIKE '%{searchStr}%') 
 OR (opts.value ->> 'mBarcode' LIKE '%{searchStr}%')  
OR (opts.value ->> 'optName' LIKE '%{searchStr}%')
OR (opts.value ->> 'item_name' LIKE '%{searchStr}%')
OR (opts.value ->> 'py_str' LIKE '%{searchStr}%'))
 ELSE true END)";
                  }
              
                
            }
            if ( classID == "borrowItemClass" && supcustID != null)
            {
                //关联还货表
                borrowSql = @$"left join (select cust_id,item_id,borrowed_qty,true as is_borrowed,null order_sub_id,null order_sub_name,null order_item_sheets_id,null order_item_sheets_no,null order_price,null unit_factor,null s_order_price,null order_balance,null order_qty,null order_flow_id from borrowed_cust_items where company_id = {companyID} and borrowed_qty<>0 and cust_id={supcustID}) ob on ip.item_id = ob.item_id ";
                borrowSelect = @",is_borrowed,borrowed_qty,floor(borrowed_qty / bFactor::numeric) b_borrowed_stock,
                         (CASE WHEN mFactor is null THEN null ELSE floor((borrowed_qty % bFactor::numeric) / mFactor::numeric) END) as m_borrowed_stock,
                         (CASE WHEN bFactor is NOT NULL AND mFactor is NOT NULL THEN floor(borrowed_qty % bFactor::numeric % mFactor::numeric)
			                   WHEN bFactor is NOT NULL AND mFactor is NULL THEN round(borrowed_qty % bFactor::numeric)
                               WHEN bFactor is NULL AND mFactor is NULL THEN round(borrowed_qty) END) s_borrowed_stock";
                orderCondi = " and  is_borrowed ";

            }

            if (classID == "orderItemClass" && supcustID != null)
            {
                orderSql = $@"left join (select null cust_id,item_id,null borrowed_qty,null is_borrowed,prepay_sub_id order_sub_id,sub_name as order_sub_name,order_item_sheets_id,order_item_sheets_no, order_price,b.unit_factor,(order_price/b.unit_factor)::numeric as s_order_price,
                                                          b.quantity * b.order_price  as order_balance,quantity*unit_factor order_qty, b.flow_id  order_flow_id from items_ordered_balance b
                                                     left join prepay_balance pb on b.supcust_id = pb.supcust_id and b.prepay_sub_id = pb.sub_id
                                                     left join cw_subject pw on pw.sub_id = b.prepay_sub_id
                                                    where b.company_id={companyID} and b.supcust_id = {supcustID} and b.quantity!=0 )  ob on ip.item_id = ob.item_id ";
                orderSelest = @",order_sub_id,order_sub_name,order_item_sheets_id,order_item_sheets_no,order_price,order_balance,ob.order_qty,ob.order_flow_id,
                      unit_from_s_to_bms (ob.order_qty::float8,bFactor::numeric, mFactor::numeric,1,bUnit,mUnit,sUnit) as order_qty_unit,
                s_order_price s_order_price ,
                case when mFactor=unit_factor::text then order_price else s_order_price::numeric * mFactor::numeric end m_order_price,
                case when  bFactor=unit_factor::text then order_price else s_order_price::numeric * bFactor::numeric end b_order_price";
                orderCondi = " and order_sub_id is not null ";
            }

            if (classID == "-1" && supcustID != null)
            {
                //关联还货表
                borrowSql = @$"left join (select cust_id,item_id,borrowed_qty,true as is_borrowed,null order_sub_id,null order_sub_name,null order_item_sheets_id,null order_item_sheets_no,null order_price,null unit_factor,null s_order_price,null order_balance,null order_qty,null order_flow_id from borrowed_cust_items where company_id = {companyID} and borrowed_qty<>0 and cust_id={supcustID} ";
                //modified by xiang: round(order_price/b.unit_factor::numeric,2) as s_order_price 会导致计算大中单位价格产生误差,误差会导致核销时比对价格不成立报错。精度改成6
                orderSql = $@"union select null cust_id,item_id,null borrowed_qty,null is_borrowed,prepay_sub_id order_sub_id,sub_name as order_sub_name,order_item_sheets_id,order_item_sheets_no, order_price,b.unit_factor,(order_price/b.unit_factor)::numeric as s_order_price,
                                                          b.quantity * b.order_price  as order_balance,quantity*unit_factor order_qty, b.flow_id  order_flow_id from items_ordered_balance b
                                                     left join prepay_balance pb on b.supcust_id = pb.supcust_id and b.prepay_sub_id = pb.sub_id
                                                     left join cw_subject pw on pw.sub_id = b.prepay_sub_id
                                                    where b.company_id={companyID} and b.supcust_id = {supcustID} and b.quantity !=0 )  ob on ip.item_id = ob.item_id ";
                if (!oftenMonths.IsValid()) oftenMonths = "3";
                if (Convert.ToInt32(oftenMonths) > 48) oftenMonths = "48";
                
                oftenSql = $@"
left join 
( 
    select s.item_id,true as is_often ,count(*) ct,max(happen_time) recent_BorrowItem_time
	from
	(
		select item_id, sd.happen_time from sheet_BorrowItem_detail sd 
		left join sheet_BorrowItem_main sm on sd.sheet_id = sm.sheet_id and sm.company_id = {companyID}
		where sd.company_id = {companyID}  and sm.supcust_id = {supcustID} 
            and sd.happen_time >= (now()-interval'{oftenMonths} months')
            and sm.happen_time >= (now()-interval'{oftenMonths} months')
            and sm.approve_time is not null
	) s group by s.item_id,is_often order by ct desc limit 200 
) often on often.item_id = ip.item_id";
             
                orderSelest = @",order_sub_id,order_sub_name,order_item_sheets_id,order_item_sheets_no,order_price,order_balance,ob.order_qty,ob.order_flow_id,
              unit_from_s_to_bms (ob.order_qty::float8,bFactor::numeric, mFactor::numeric,1,bUnit,mUnit,sUnit) as order_qty_unit,
                s_order_price s_order_price ,
case when mFactor=unit_factor::text then order_price else s_order_price::numeric * mFactor::numeric end m_order_price,
case when  bFactor=unit_factor::text then order_price else s_order_price::numeric * bFactor::numeric end b_order_price,is_often,
case when order_sub_id is not null or is_often or is_borrowed then true else false end often_order";

                borrowSelect = @",is_borrowed,borrowed_qty,floor(borrowed_qty / bFactor::numeric) b_borrowed_stock,
                         (CASE WHEN mFactor is null THEN null ELSE floor((borrowed_qty % bFactor::numeric) / mFactor::numeric) END) as m_borrowed_stock,
                         (CASE WHEN bFactor is NOT NULL AND mFactor is NOT NULL THEN floor(borrowed_qty % bFactor::numeric % mFactor::numeric)
			                   WHEN bFactor is NOT NULL AND mFactor is NULL THEN round(borrowed_qty % bFactor::numeric)
                               WHEN bFactor is NULL AND mFactor is NULL THEN round(borrowed_qty) END) s_borrowed_stock";
                //orderCondi = " and (order_sub_id is not null or is_often or is_borrowed)";
                if (searchStr.IsValid()) orderCondi = "";
                else orderCondi = " and (order_sub_id is not null or is_often or is_borrowed)";
                ORDER_BY = @$"order by often_order desc,{selectItemOrderByOptions} order_sub_name,is_borrowed,recent_BorrowItem_time desc";
            }

            if (branchID != null) condiStock = $" and branch_id = {branchID}";
            if (startRow == 0) firstRequest = true;
            if (supcustID == null) supcustID = "-1";

            crosstabSql = @$"select mu.item_id,unit_type,row_to_json(row(mu.unit_no,unit_factor,barcode,wholeBorrowItem_price,retail_price,recent_price,buy_price,recent_orig_price,lowest_price,weight,cost_price_spec)) as json 
                                 from info_item_multi_unit mu 
                                left join (select item_id,unit_no,recent_price,recent_orig_price FROM client_recent_prices where company_id = {companyID} and supcust_id = {supcustID}) rp 
                                 on rp.item_id = mu.item_id and mu.unit_no = rp.unit_no where mu.company_id = {companyID} order by item_id";
            if (showStockOnly) condi += $" and stock.stock_qty>0 ";
            if (showStockOnly) showStockOnlyCondi += $" and stock_qty>0 ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql_noLimit = @$"
SELECT ip.item_id,ip.item_name,{sonItemFile},item_class as class_id,other_class,valid_days,valid_day_type,virtual_produce_date,item_spec,stock_qty,sign(stock.stock_qty)*floor(abs(stock.stock_qty) / bFactor::numeric) bStock,ip.batch_level,
        yj_get_unit_qty('b', stock.stock_qty, bFactor::NUMERIC, mFactor::NUMERIC, false) bstock,
	    yj_get_unit_qty('m', stock.stock_qty, bFactor::NUMERIC, mFactor::NUMERIC, false) mstock,
	    yj_get_unit_qty('s', stock.stock_qty, bFactor::NUMERIC, mFactor::NUMERIC, false) sstock,
        yj_get_bms_qty(stock.stock_qty,bunit,bFactor::float4,mUnit,mFactor::float4,sunit) as stock_qty_unit,
       ip.item_images, ip.item_brand,cost_price_avg,cost_price_spec,cost_price_recent,
        sUnit,sFactor,s_barcode,spPrice,slPrice,s_recent_price,s_buy_price,s_recent_orig_price,s_lowest_price,s_weight,
        mUnit,mFactor,m_barcode,mpPrice,mlprice,m_recent_price,m_buy_price,m_recent_orig_price,m_lowest_price,m_weight,
        bUnit,bFactor,b_barcode,bpPrice,blPrice,b_recent_price,b_buy_price,b_recent_orig_price,b_lowest_price,b_weight{orderSelest}{borrowSelect}  
FROM info_item_prop as ip {oftenSql} {borrowSql} {orderSql} 
{showAttrSonItemsJoin}
LEFT JOIN 
(
    select item_id,s,m,b,
            (s->>'f1') as sUnit,(s->>'f2') as sFactor,(s->>'f3') as s_barcode,(s->>'f4') as spPrice,(s->>'f5') as slPrice,s->>'f6' as s_recent_price,s->>'f7' as s_buy_price,s->>'f8' as s_recent_orig_price,s->>'f9' as s_lowest_price,s ->> 'f10' as s_weight,
            (m->>'f1') as mUnit,(m->>'f2') as mFactor,(m->>'f3') as m_barcode,(m->>'f4') as mpPrice,(m->>'f5') as mlprice,m->>'f6' as m_recent_price,m->>'f7' as m_buy_price,m->>'f8' as m_recent_orig_price,m->>'f9' as m_lowest_price,m ->> 'f10' as m_weight,
            (b->>'f1') as bUnit,(b->>'f2') as bFactor,(b->>'f3') as b_barcode,(b->>'f4') as bpPrice,(b->>'f5') as blPrice,b->>'f6' as b_recent_price,b->>'f7' as b_buy_price,b->>'f8' as b_recent_orig_price,b->>'f9' as b_lowest_price,b ->> 'f10' as b_weight
     from crosstab('{crosstabSql}', $$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)
) t
on ip.item_id=t.item_id
LEFT JOIN 
(
        select s.company_id,case when son_mum_item is null then s.item_id else son_mum_item end item_id,branch_id,sum(stock_qty - COALESCE(sell_pend_qty,0)) as stock_qty from stock s
        LEFT JOIN info_item_prop ip on ip.company_id={companyID} and s.item_id = ip.item_id
         where s.company_id = {companyID} {condiStock} {showStockOnlyCondi}
         GROUP BY s.company_id ,case when son_mum_item is null then s.item_id else son_mum_item end,branch_id
    
) stock on t.item_id = stock.item_id
left join 
(
    select item_id,produce_date as virtual_produce_date from item_recent_produce_date where company_id={companyID}
) rpd on t.item_id=rpd.item_id
LEFT JOIN 
(
    select * from info_item_class where company_id = {companyID}
) ic on ip.item_class = ic.class_id 
where ip.company_id = {companyID} {condi} {whereSearch} {orderCondi} {ORDER_BY}";
            // 判断复制的转单，多口味区分库存需要and son_mum_item is NUL 放开
            // where ip.company_id = {companyID} and son_mum_item is NULL {condi} {orderCondi} {ORDER_BY}";
            var sql = "";
            
            if (sourcrSheet=="SS")
                sql=sql_noLimit + $"  offset {startRow};";
            else 
                sql = sql_noLimit + $"  limit {pageSize} offset {startRow};";

            QQ.Enqueue("data", sql);
            if (firstRequest)
            {
                sql = $"select count(*) as itemCount from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
            }

            string dispRecDept = @$"";
            string dispRecWhere = @$"";
            if (!string.IsNullOrEmpty(seller_dept_path) && !canGiveDisplayCrossDept)
            {
                dispRecDept = $@" left join info_operator io ON m.company_id = io.company_id AND (coalesce(m.responsible_worker,m.seller_id) = io.oper_id) ";
                dispRecWhere = $@" AND io.depart_path like '{seller_dept_path}%' ";
            }

            if ((classID == "-1" || classID == "displayItemClass") && supcustID != null)
            {
                sql = @$"
select tt.*,
       item_name,
       unit_factor,
       mu.barcode,
       recent_orig_price,
       sdm.maintain_times,
       idt.disp_template_id,
       idt.disp_template_name,
       idt.month_maintain_times,  
       fee_sub_name,
       idt.fd_sender_actions,
       idt.give_condition, 
       idt.fd_seller_actions, 
       idt.fd_sender_actions, 
       idt.cx_give_actions, 
       idt.fd_seller_need_review, 
       idt.fd_sender_need_review, 
       idt.cx_give_need_review,
       disp_unit.sFactor,
       disp_unit.mFactor,
       disp_unit.bFactor,
batch_stock
from 
( 
    select flow_id disp_flow_id,sheet_id disp_sheet_id,fee_sub_name,regexp_split_to_table(items_id, ',')::int item_id,left_qty,unit_no,
        (case when start_month+months-1>12 then start_month+months-13 else start_month+months-1 end) as month,
        (case when start_month+months-1>12 then start_year+1 else start_year end) as year,months as month_id,
        disp_template_id,disp_sheet_no,reviewer, review_refused
    from 
    (  
        select d.sheet_id,d.flow_id,sub.sub_name fee_sub_name,items_id,unit_no,unnest(string_to_array((	
            COALESCE(month1_qty,0)-COALESCE(month1_given,0)||','||COALESCE(month2_qty,0)-COALESCE(month2_given,0)||','||
            COALESCE(month3_qty,0)-COALESCE(month3_given,0)||','||COALESCE(month4_qty,0)-COALESCE(month4_given,0)||','||
            COALESCE(month5_qty,0)-COALESCE(month5_given,0)||','||COALESCE(month6_qty,0)-COALESCE(month6_given,0)||','||
            COALESCE(month7_qty,0)-COALESCE(month7_given,0)||','||COALESCE(month8_qty,0)-COALESCE(month8_given,0)||','||
            COALESCE(month9_qty,0)-COALESCE(month9_given,0)||','||COALESCE(month10_qty,0)-COALESCE(month10_given,0)||','||
            COALESCE(month11_qty,0)-COALESCE(month11_given,0)||','||COALESCE(month12_qty,0)-COALESCE(month12_given,0)) ,','))::numeric left_qty,
            unnest(string_to_array('01,02,03,04,05,06,07,08,09,10,11,12',','))::int months,to_char(m.start_time,'YYYY')::int start_year,
            to_char(m.start_time,'MM')::int start_month,
            m.disp_template_id,m.sheet_no as disp_sheet_no,m.reviewer, m.review_refused
            from display_agreement_detail d 
            left join display_agreement_main m on m.sheet_id = d.sheet_id {dispRecDept}
            left join cw_subject sub on sub.company_id={companyID} and m.fee_sub_id=sub.sub_id
            where d.company_id = {companyID} and red_flag is null and approve_time is not null and supcust_id = {supcustID} and items_id!='money' {dispRecWhere}
        ) t
        where left_qty > 0 
        GROUP BY items_id,flow_id,sheet_id,fee_sub_name,months,left_qty,unit_no,start_year,start_month,disp_template_id, disp_sheet_no,reviewer, review_refused 
    ) tt 
    left join info_item_prop ip on ip.item_id = tt.item_id and ip.company_id = {companyID} 
    left join info_item_multi_unit mu on mu.company_id = {companyID} and mu.item_id = tt.item_id and mu.unit_no = tt.unit_no 

LEFT JOIN 
( 
    select item_id,s,m,b,
            (s->>'f1') as sUnit,(s->>'f2') as sFactor,(s->>'f3') as s_barcode,
            (m->>'f1') as mUnit,(m->>'f2') as mFactor,(m->>'f3') as m_barcode,
            (b->>'f1') as bUnit,(b->>'f2') as bFactor,(b->>'f3') as b_barcode
     from crosstab('{crosstabSql}', $$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)
  
) disp_unit
on ip.item_id=disp_unit.item_id
 
    left join (select item_id,unit_no,recent_price,recent_orig_price FROM client_recent_prices where company_id = {companyID} and supcust_id = {supcustID}) rp on rp.item_id = tt.item_id and rp.unit_no = tt.unit_no 
    LEFT JOIN (select company_id,item_id,branch_id,stock_qty-COALESCE(sell_pend_qty,0) as stock_qty from stock where company_id = {companyID} {condiStock}) stock on tt.item_id = stock.item_id 
    LEFT JOIN info_display_template idt ON idt.company_id = {companyID} AND idt.disp_template_id = tt.disp_template_id
    left JOIN sum_display_maintain sdm ON sdm.company_id = {companyID} AND sdm.disp_sheet_id = tt.disp_sheet_id AND sdm.client_id = {supcustID} AND sdm.months = (tt.year || '-' || lpad(tt.month :: text, 2, '0') || '-01 00:00:00') :: Date
left join(
    select s.item_id,json_agg(json_build_object('stock_qty',COALESCE(stock_qty,0),'batch_id',itb.batch_id,'produce_date',itb.produce_date,'batch_no',itb.batch_no,
    'stock_qty_unit',unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no),
    'sell_pend_qty_unit',unit_from_s_to_bms (COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no),
    'usable_stock_qty_unit',unit_from_s_to_bms (COALESCE(stock_qty,0)::numeric-COALESCE(sell_pend_qty,0)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)
)) as batch_stock
        from stock s
        left join info_item_batch itb on itb.company_id ={companyID} and itb.batch_id = s.batch_id
        left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,
                                                            b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no,s->>'f3' as s_buy_price
                                            from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                                as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu1 on s.item_id = mu1.item_id
        where s.company_id = {companyID} and branch_id={branchID} and stock_qty is not null group by s.item_id
)ss on ss.item_id = tt.item_id
    {condi} 
    where (tt.disp_template_id is NULL OR (tt.disp_template_id is NOT NULL and ((sign_need_review = false) or (sign_need_review = true  and reviewer is NOT NULL AND (review_refused is null OR review_refused = false))) ))
    order by disp_flow_id,month_id,item_name";

                QQ.Enqueue("dispRec", sql);
            }

            List<ExpandoObject> data = null;
            List<ExpandoObject> dispRec = null;
            List<ExpandoObject> hasStockClass = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                if (sqlName == "dispRec")
                {
                    dispRec = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count" && firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
                else if (sqlName == "hasStockClass")
                {
                    hasStockClass = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            
            // 处理在某类下搜索商品
            if (bSearchStrInClass)
            {
                bool foundInClass = false;
                foreach (dynamic item in data)
                {
                    string other_class = item.other_class;
                    string often_order = "";
                    if (classID == "-1" && supcustID != null) often_order = item.often_order;
                    if (other_class.Contains("/" + classID + "/") || often_order.ToLower() == "true")
                    {
                        foundInClass = true;
                    }
                    else
                    {
                        item.beforeInfo2 = "其他类";
                        if (foundInClass)
                        {
                        }
                        else
                        {
                            item.beforeInfo1 = "该类下木有哦~";
                        }

                        break;
                    }
                }
            }

            #region 获取价格策略
            bool doPricePlan = false;
            Dictionary<string, string> planIDs = new Dictionary<string, string>();
            if (supcustID != "-1" && supcustID != "" && supcustID != "0" && data.Count > 0)
            {
                string supSql = $"select region_id,other_region,sup_rank,sup_group from info_supcust where company_id = {companyID} and supcust_id={supcustID}";
                dynamic supInfo = await CDbDealer.Get1RecordFromSQLAsync(supSql, cmd);
                var groupID = supInfo.sup_group == "" ? "null" : supInfo.sup_group;
                var otherRegion = supInfo.other_region;
                var rankID = supInfo.sup_rank == "" ? "null" : supInfo.sup_rank;
                string planSql = $@"
  select null flow_id,      supcust_id,null group_id,null region_id,null rank_id,price1,price2,price3 from price_strategy_client where company_id = {companyID} and supcust_id={supcustID} and coalesce(order_source,'all')!='xcx'
  union
  select      flow_id, null supcust_id,     group_id,     region_id,     rank_id,price1,price2,price3 from price_strategy_class  where company_id={companyID} and (position(concat('/',region_id,'/') in '{otherRegion}')>0   or region_id is null) and (group_id::text = '{groupID}' or group_id is null) and (rank_id is null or rank_id::text = '{rankID}') and coalesce(order_source,'all')!='xcx'  order by supcust_id,flow_id desc";
                List<ExpandoObject> supPlans = await CDbDealer.GetRecordsFromSQLAsync(planSql, cmd);
                if (supPlans.Count > 0)
                {
                    foreach (dynamic plan in supPlans)
                    {
                        if (plan.supcust_id != "") doPricePlan = true;
                        else if (plan.supcust_id == "" && plan.flow_id != "") doPricePlan = true;

                        if (doPricePlan)
                        {
                            if (plan.price1 != "") planIDs.Add("1", plan.price1);
                            if (plan.price2 != "") planIDs.Add("2", plan.price2);
                            if (plan.price3 != "") planIDs.Add("3", plan.price3);
                            break;
                        }
                    }
                }
                else
                {
                    doPricePlan = true;
                    planIDs.Add("1", "recent");
                    planIDs.Add("2", "wholeBorrowItem");
                }
                
            }
            #endregion
            var itemIDs = string.Join(",", data.Select(d => (d as dynamic).item_id));
            #region 使用价格策略
            if (doPricePlan && planIDs.Count > 0)
            {
                List<ExpandoObject> plans = null;

                #region 获取所有商品的所有方案
                var fld = ""; var orderCondi1 = "";
                string priceSql = "";
                foreach (var p in planIDs)
                {
                    if (priceSql != "") priceSql += " union ";
                    if (p.Key != null && p.Key != "")
                    {
                        fld = $"{p.Key} as priority,'{p.Value}' plan_id,";
                        orderCondi1 = $" order by priority,price_item ";
                    }

                    priceSql += @$"
select p.item_id,p.other_class,pi.item_id price_item,{fld}plan_name,pi.class_id,s_price,m_price,b_price,class_discount,item_discount
from info_item_prop p 
left join 
(
    select null item_id,null s_price,null m_price,null b_price,     class_id,discount class_discount,null     item_discount from price_plan_class where company_id = {companyID} and plan_id::text = '{p.Value}' and class_id is not null
    union
    select      item_id,     s_price,     m_price,     b_price,null class_id,    null class_discount,discount item_discount from price_plan_item  where company_id = {companyID} and plan_id::text = '{p.Value}' and item_id in ({itemIDs})
) 
pi on (pi.item_id = p.item_id or pi.item_id is null) and (position(concat('/',pi.class_id,'/') in other_class)>0 or pi.class_id is null)
left join price_plan_main m on m.plan_id::text = '{p.Value}'
where p.company_id = {companyID} and p.item_id in ({itemIDs}) ";
                }
                priceSql += orderCondi1;
                plans = await CDbDealer.GetRecordsFromSQLAsync(priceSql, cmd);
                #endregion

                #region 修改结果集  { priority, { plan_id, item_id, priceSetting{price_item,class_id.....}}}
                Dictionary<string, dynamic> prices = new Dictionary<string, dynamic>();
                foreach (dynamic plan in plans)
                {
                    dynamic pr = null;
                    if (prices.ContainsKey(plan.priority + '_' + plan.item_id)) pr = prices[plan.priority + '_' + plan.item_id];
                    else
                    {
                        pr = new ExpandoObject();
                        pr.priority = plan.priority;
                        pr.plan_id = plan.plan_id;
                        pr.plan_name = plan.plan_name;
                        pr.item_id = plan.item_id;
                        pr.other_class = plan.other_class;
                        prices.Add((string)plan.priority + '_' + plan.item_id, pr);
                        pr.priceSetting = new List<dynamic>();//priceSetting 可能会包含一个类别设定和品项设定
                    }
                    List<dynamic> prSetting = pr.priceSetting;
                    if (plan.price_item != "" || plan.class_id != "") prSetting.Add(new { plan.price_item, plan.class_id, plan.class_discount, plan.item_discount, plan.s_price, plan.m_price, plan.b_price });
                }
                #endregion

                #region 调价单的影响 --- 在使用价格策略时，需要多进行一次比较
                // 对于首选方案为最近售价的价格策略，需要考虑调价单的影响
                dynamic adjustItems = null;
                if (planIDs.Count > 1 && planIDs["1"] == "recent") // 当 priority为1 && plan = "recent_price"时，
                {
                    string adjustPlan = planIDs["2"];
                    if (planIDs["2"] == "wholeBorrowItem") adjustPlan = "-1";
                    if (planIDs["2"] == "retail") adjustPlan = "0";
                    //需要比较 client_recent_prices 表中 happen_time 与 sheet_price_adjust_detail 中 happen_time 的时间早晚
                    string selAdjustSql = @$"select string_agg(distinct d.item_id::text,',') items_id from sheet_price_adjust_detail d 
                                    left join sheet_price_adjust_main m on m.sheet_id = d.sheet_id 
                                    left join (select item_id,max(happen_time) happen_time,supcust_id from client_recent_prices where company_id={companyID} GROUP BY item_id,supcust_id ) r on r.item_id = d.item_id
                                    where m.company_id = {companyID} and supcust_id = {supcustID} and  m.red_flag is null and m.approve_time is not null and m.plans_id like '%{adjustPlan}%' and m.happen_time>r.happen_time and d.item_id in ({itemIDs})
                ";
                    adjustItems = await CDbDealer.Get1RecordFromSQLAsync(selAdjustSql, cmd); // 找出需要使用 新价格的 商品
                }
                #endregion

                #region 遍历商品集 给每个商品添加 大中小价格 setPrice=true，说明已经取到价格，getPlanPrice=true 表示data中添加价格方案价格
                foreach (dynamic unit in data)
                {
                    var s_price = ""; var m_price = ""; var b_price = "";
                    //string s_orig_price = "", m_orig_price = "", b_orig_price = "";
                    bool setPrice = false;
                    bool bOrigPriceDiffWithPrice = false;
                    unit.s_plan_price = "";
                    unit.m_plan_price = "";
                    unit.b_plan_price = "";
                    unit.plan_id = "";
                    unit.plan_name = "";
                    foreach (var p in prices)
                    {
                        if (unit.item_id == p.Value.item_id)
                        {
                            var plan = p.Value.plan_id;
                            var name = unit.item_name;
                            Boolean state = false;
                            if (adjustItems != null) state = adjustItems.items_id.Contains(unit.item_id);
                            if (plan == "") continue;
                            else if (plan == "wholeBorrowItem" && !setPrice)
                            {
                                s_price = unit.spprice;
                                m_price = unit.mpprice;
                                b_price = unit.bpprice;
                                unit.plan_name = "批发";
                                if (s_price != "" || m_price != "" || b_price != "") setPrice = true;
                            }
                            else if (plan == "recent" && !setPrice)
                            {
                                if (p.Value.priority == "1" && adjustItems != null && adjustItems.items_id.Contains(unit.item_id)) // 如果需要改价，则取第二次的方案价格
                                    continue;
                                s_price = unit.s_recent_price;
                                m_price = unit.m_recent_price;
                                b_price = unit.b_recent_price;
                                if (s_price != "" || m_price != "" || b_price != "")
                                {
                                    unit.b_orig_price = unit.b_recent_orig_price;
                                    unit.m_orig_price = unit.m_recent_orig_price;
                                    unit.s_orig_price = unit.s_recent_orig_price;
                                    setPrice = true;
                                    bOrigPriceDiffWithPrice = true;
                                }
                                unit.plan_name = "上次";
                            }
                            else if (plan == "retail" && !setPrice)
                            {
                                s_price = unit.slprice;
                                m_price = unit.mlprice;
                                b_price = unit.blprice;
                                if (s_price != "" || m_price != "" || b_price != "") setPrice = true;
                                unit.plan_name = "零售";
                            }
                            else//价格方案
                            {
                                string otherClass = p.Value.other_class;
                                var selectClass = ""; var selectDisc = ""; var classIndex = -1; bool getPlanPrice = false;
                                foreach (dynamic s in p.Value.priceSetting)
                                {
                                    if (s.price_item != "")
                                    {
                                        unit.s_plan_price = s.s_price;  //unit中添加价格方案
                                        unit.m_plan_price = s.m_price;
                                        unit.b_plan_price = s.b_price;
                                        if (!setPrice)
                                        {
                                            s_price = s.s_price;
                                            m_price = s.m_price;
                                            b_price = s.b_price;
                                        }
                                        if (s.s_price != "" || s.m_price != "" || s.b_price != "") //可能存在 只有 某一单位有价格，
                                        {
                                            setPrice = true; getPlanPrice = true;
                                            break;
                                        }
                                        else selectDisc = s.item_discount;
                                    }
                                    else if (s.class_id != "")
                                    {
                                        // var classArr = otherClass.Split(otherClass, '/');
                                        var classArr = otherClass.Split('/');
                                        if (Array.IndexOf(classArr, s.class_id) > classIndex)
                                        {
                                            classIndex = Array.IndexOf(classArr, s.class_id);
                                            selectClass = s.class_id;
                                            selectDisc = s.class_discount;
                                            getPlanPrice = true;
                                        }
                                    }
                                    if (getPlanPrice)
                                    {
                                        unit.plan_id = plan;
                                        unit.plan_name = p.Value.plan_name;
                                        break;
                                    }
                                }
                                if (!getPlanPrice && selectDisc != "") // 如果是给 商品类别指定折扣 的价格方案，就取批发价*折扣
                                {
                                    unit.b_orig_price = unit.bpprice;
                                    unit.m_orig_price = unit.mpprice;
                                    unit.s_orig_price = unit.spprice;
                                    bOrigPriceDiffWithPrice = true;
                                    if (unit.spprice != "") {
                                        if (!setPrice) s_price = Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.spprice);
                                        unit.s_plan_price = s_price;
                                    }
                                    if (unit.mpprice != "")
                                    {
                                        if (!setPrice) m_price = Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.mpprice);
                                        unit.m_plan_price = m_price;
                                    }
                                    if (unit.bpprice != "")
                                    {
                                        if (!setPrice) b_price = Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.bpprice);
                                        unit.b_plan_price = b_price;
                                    }
                                    getPlanPrice = true;
                                }
                                if (getPlanPrice) break;
                            }

                        }
                    }

                    if (!bOrigPriceDiffWithPrice)
                    {
                        unit.b_orig_price = b_price;
                        unit.m_orig_price = m_price;
                        unit.s_orig_price = s_price;
                    }

                    unit.s_price = s_price;
                    unit.m_price = m_price;
                    unit.b_price = b_price;
                

                }
                #endregion

            }
            #endregion
            else
            {
                foreach (dynamic unit in data)
                {
                    unit.s_price = unit.s_recent_price;
                    unit.m_price = unit.m_recent_price;
                    unit.b_price = unit.b_recent_price;
                    unit.b_orig_price = unit.b_recent_orig_price;
                    unit.m_orig_price = unit.m_recent_orig_price;
                    unit.s_orig_price = unit.s_recent_orig_price;
                    if (unit.s_price == "") unit.s_price = unit.s_orig_price = unit.spprice;
                    if (unit.m_price == "") unit.m_price = unit.m_orig_price = unit.mpprice;
                    if (unit.b_price == "") unit.b_price = unit.b_orig_price = unit.bpprice;
                    unit.s_plan_price = "";
                    unit.m_plan_price = "";
                    unit.b_plan_price = "";
                    unit.plan_id = "";
                    unit.plan_name = "";
                    if (unit.s_recent_price != "") unit.price_type = "上次";
                    else unit.price_type = "批发";
                }

            }
            
            
            #region 获取特价

            if (supcustID != "-1" && supcustID != "" && supcustID != "0" && data.Count > 0)
            {
                string specialPriceSql = @$"SELECT * FROM (SELECT sm.supcust_id,sm.start_time,sm.end_time,(sm.end_time::date-sm.start_time::date)+1 as special_days ,(sm.end_time::date-now()::date)+1 left_days,sd.item_id,sd.unit_no,sd.unit_factor,mu.unit_type,sd.special_price,sd.happen_time,ip.son_mum_item,row_number() over(partition by sd.item_id order by sd.happen_time desc ) rn FROM sheet_special_price_detail sd 
LEFT JOIN sheet_special_price_main sm on sd.company_id = sm.company_id and sd.sheet_id= sm.sheet_id 
LEFT JOIN info_item_multi_unit mu on mu.company_id = sd.company_id and mu.item_id = sd.item_id and mu.unit_factor = sd.unit_factor 
LEFT JOIN info_item_prop ip on ip.company_id = sd.company_id and ip.item_id = sd.item_id
WHERE sd.company_id = {companyID} and sm.approve_time is not null and sm.red_flag is null and sm.end_time::date >= NOW()::date and sm.supcust_id = {supcustID})t
WHERE t.rn = 1 ";
                List<ExpandoObject> specialPriceItems = await CDbDealer.GetRecordsFromSQLAsync(specialPriceSql, cmd);
                if (specialPriceItems.Count > 0)
                {
                    foreach(dynamic item in data)
                    {
                        foreach(dynamic  spItem in specialPriceItems)
                        {
                            if (item.item_id == spItem.item_id) {
                                if (spItem.unit_type == "s")
                                {
                                    item.s_price = spItem.special_price;
                                    if(item.mfactor!="") item.m_price = CPubVars.ToDecimal(spItem.special_price) * CPubVars.ToDecimal(item.mfactor);
                                    if(item.bfactor!="") item.b_price =CPubVars.ToDecimal(spItem.special_price) * CPubVars.ToDecimal(item.bfactor);

                                }else if(spItem.unit_type == "m")
                                {
                                    item.s_price = CPubVars.ToDecimal(spItem.special_price)/ CPubVars.ToDecimal(item.mfactor);
                                    item.m_price = spItem.special_price ;
                                    item.b_price = CPubVars.ToDecimal(spItem.special_price) / CPubVars.ToDecimal(item.mfactor) * CPubVars.ToDecimal(item.bfactor);

                                }
                                else
                                {
                                    item.s_price = CPubVars.ToDecimal(spItem.special_price) / CPubVars.ToDecimal(item.bfactor);
                                    if (item.mfactor!="") item.m_price = CPubVars.ToDecimal(spItem.special_price) / CPubVars.ToDecimal(item.bfactor) * CPubVars.ToDecimal(item.mfactor);
                                    item.b_price = spItem.special_price;

                                }
                                item.s_special_price = item.s_price;
                                item.m_special_price = item.m_price;
                                item.b_special_price = item.b_price;
                                item.left_days = spItem.left_days;
                                item.haveSpecialPrice = true;
                            }

                            if(item.item_id == spItem.son_mum_item)
                            {
                                item.haveSpecialPrice = true;
                            }
                        }
                    }
                }
            }
                #endregion
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, dispRec, itemCount});
        }


        /// <summary>
        /// 获得指定商品大中小单位
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="itemID"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetItemUnit(String operKey, string itemID,string branchID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            branchID = branchID.IsInvalid()? "-1":branchID;
            var sql = $@"select item_id,
                yj_get_bms_qty (
                    (SELECT SUM( stock_qty - COALESCE ( sell_pend_qty, 0 ) ) FROM stock WHERE company_id = {companyID} AND item_id = {itemID}  AND branch_id = {branchID} ),
		            b_unit ->> 'f1',float4(b_unit ->> 'f2'),
		            m_unit ->> 'f1',float4(m_unit ->> 'f2'),
		            s_unit ->> 'f1' ) as stock_qty,
            s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,s_unit ->> 'f3' as s_weight,m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,m_unit ->> 'f3' as m_weight,b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor,b_unit ->> 'f3' as b_weight 
            from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor,weight)) as unit from info_item_multi_unit where company_id = {companyID} and item_id = {itemID} ',$$values ('s'::text),('m'::text),('b'::text)$$) 
            as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)";
            ExpandoObject data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

      
       
        /// <summary>
        /// 选择客户时候，返回相关支付等信息
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="supcust_id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetClientAccountInfo(string operKey, string supcust_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            string custID = supcust_id;
            string acctSql = @$"
select i.acct_cust_id,s.sup_name acct_cust_name,i.allow_change_price,s.allow_change_price acct_allow_change_price 
from info_supcust i 
left join info_supcust s on i.acct_cust_id = s.supcust_id and s.company_id={companyID}
where i.company_id = {companyID} and i.supcust_id = {supcust_id} and coalesce(s.status,'1')= '1';";
            string restrictPaywayCondi = $"(b.sub_id::text IN(SELECT json_array_elements_text(avail_pay_ways) AS individual_value FROM info_operator WHERE company_id = {companyID} and oper_id = {operID} and restrict_pay_ways = TRUE) OR (select COUNT(*) from info_operator where company_id = {companyID} and oper_id = {operID} and restrict_pay_ways = TRUE) = 0)";
            dynamic acctInfo = await CDbDealer.Get1RecordFromSQLAsync(acctSql, cmd);
            if (acctInfo != null && acctInfo.acct_cust_id != "")
                custID = acctInfo.acct_cust_id;
            string allow_change_price = acctInfo.allow_change_price;
            if(allow_change_price=="") allow_change_price = acctInfo.acct_allow_change_price;
            
            var dispCondi = " where ((disp_year < (to_char(now(),'YYYY')::int)) OR (t.disp_month<=(to_char(now(),'MM')::int) and disp_year = (to_char(now(),'YYYY')::int))) ";
            
            //查询提前兑付权限 
            if (!operID.StartsWith("YingJiang168MiNiCheckPass"))  // 小程序没有operID， 跳过
            {
                string rightSql = $"select rights->'delicacy'->'allowAdvanceDisplayFee'->'value' allow_advance_disp_fee from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={companyID} and oper_id={operID}";
                dynamic advanceDisp = await CDbDealer.Get1RecordFromSQLAsync(rightSql, cmd);
                if (advanceDisp != null && advanceDisp.allow_advance_disp_fee == "true")
                    dispCondi = $" ";
            }
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = $"select b.sub_id,sub_type payway_type,sub_name,is_order,round(balance::numeric,2) balance from prepay_balance b left join cw_subject p on p.sub_id = b.sub_id where b.company_id = {companyID} and supcust_id = {custID} and {restrictPaywayCondi};";
            QQ.Enqueue("prepay", sql);
            restrictPaywayCondi = $"(t.sub_id::text IN(SELECT json_array_elements_text(avail_pay_ways) AS individual_value FROM info_operator WHERE company_id = {companyID} and oper_id = {operID} and restrict_pay_ways = TRUE) OR (select COUNT(*) from info_operator where company_id = {companyID} and oper_id = {operID} and restrict_pay_ways = TRUE) = 0)";
            sql = @$"
select t.sub_id,sub_name,sum(disp_left_money) balance,'ZC' as payway_type
from 
(
    select sub_id,disp_left_money,
        (case when start_month+months-1>12 then start_month+months-13 else start_month+months-1 end) disp_month,
        (case when start_month+months-1>12 then start_year+1 else start_year end) as disp_year,months as disp_month_id
    from 
    (
        select m.fee_sub_id sub_id,d.sheet_id,items_id,items_name,unnest(string_to_array((								
            COALESCE(month1_qty,0)-COALESCE(month1_given,0)||','||COALESCE(month2_qty,0)-COALESCE(month2_given,0)||','||
            COALESCE(month3_qty,0)-COALESCE(month3_given,0)||','||COALESCE(month4_qty,0)-COALESCE(month4_given,0)||','||
            COALESCE(month5_qty,0)-COALESCE(month5_given,0)||','||COALESCE(month6_qty,0)-COALESCE(month6_given,0)||','||
            COALESCE(month7_qty,0)-COALESCE(month7_given,0)||','||COALESCE(month8_qty,0)-COALESCE(month8_given,0)||','||
            COALESCE(month9_qty,0)-COALESCE(month9_given,0)||','||COALESCE(month10_qty,0)-COALESCE(month10_given,0)||','||
            COALESCE(month11_qty,0)-COALESCE(month11_given,0)||','||COALESCE(month12_qty,0)-COALESCE(month12_given,0)) ,','))::numeric disp_left_money,
            unnest(string_to_array('01,02,03,04,05,06,07,08,09,10,11,12',','))::int months,to_char(m.start_time,'YYYY')::int start_year,
            to_char(m.start_time,'MM')::int start_month 
        from display_agreement_detail d
        left join display_agreement_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID}
        where d.company_id = {companyID} and approve_time is not null and red_flag is null and supcust_id = {custID} and items_id ='money' 
    ) t
    where disp_left_money >= 0 
) t
left join cw_subject c on c.sub_id = t.sub_id and c.company_id = {companyID} 
{dispCondi} and {restrictPaywayCondi} GROUP BY t.sub_id,sub_name;
";
            QQ.Enqueue("disp", sql);
            sql = $"select round(balance::numeric,2) balance from arrears_balance where company_id = {companyID} and supcust_id = {custID} and balance<>0;";
            QQ.Enqueue("arrears", sql);
            sql = $"select mobile,sup_addr,boss_name,sup_rank,sup_group,other_region as sup_regions from info_supcust where company_id = {companyID} and supcust_id = {supcust_id}";
            QQ.Enqueue("info", sql);
            /* sql = $"select sub_id,sub_name,payway_type from info_pay_way where company_id = {companyID} and is_order is not true order by payway_index;";
              QQ.Enqueue("subInfo", sql);//小程序用的
              sql = @$"SELECT c.sub_id,sub_name,sub_type,round(balance::NUMERIC, 2) balance 
                  FROM cw_subject c
                  left JOIN prepay_balance pb on c.company_id = pb.company_id AND c.sub_id = pb.sub_id and supcust_id = {custID} 
                  WHERE c.company_id = {companyID} AND is_order IS NOT TRUE AND sub_type IN( 'QT', 'YS' ) ORDER BY order_index;";
              QQ.Enqueue("orderPayWay", sql);
              */
            sql = @$"
select wu.open_id,wuss.subscribe_content,wu.subscribe_flag subscribewechatflag,wuss.subscribe_flag subscribemsgflag
from info_cust_contact icc
left join wx_user wu on icc.wx_user_id = wu.wx_user_id
left join wx_user_subscribe_setting wuss on wu.wx_user_id = wuss.wx_user_id
where company_id = '{companyID}' and supcust_id = '{supcust_id}'";
            QQ.Enqueue("weChatInfo", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            var prepay = new List<ExpandoObject>();
            var disp = new List<ExpandoObject>();
            var arrears = new List<ExpandoObject>();
            var subInfo = new List<ExpandoObject>();
            var orderPayWay = new List<ExpandoObject>();
            var weChatInfo = new List<ExpandoObject>();
            dynamic info = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "prepay")
                {
                    prepay = CDbDealer.GetRecordsFromDr(dr, false);
                }
                if (sqlName == "disp")
                {
                    disp = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "arrears")
                {
                    arrears = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "info")
                {
                    info = CDbDealer.Get1RecordFromDr(dr, false);
                }
                else if (sqlName == "subInfo")
                {
                    subInfo = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "orderPayWay")
                {
                    orderPayWay = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "weChatInfo")
                {
                    weChatInfo = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            return Json(new { result = "OK", prepay, disp, arrears, info.mobile, info.sup_addr, info.boss_name, info.sup_rank, info.sup_group, info.sup_regions, allowChangePrice=allow_change_price!="False", subInfo, orderPayWay,weChatInfo });
        }

        [HttpPost]
        //public async Task<JsonResult> Delete([FromBody] dynamic data)
        //{
        //    string sheet_id = data.sheet_id;
        //    string operKey = data.operKey;
        //    string sheetType = data.sheetType;
        //    Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
        //    SheetBorrowItem sheet = new SheetBorrowItem(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);

        //    string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
        //    string result = msg == "" ? "OK" : "Error";
        //    if (msg != "") result = "Error";
        //    return Json(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        //}

        /// <summary>
        /// 销售单、销售订单 选择商品的历史记录
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="supcustID"></param>
        /// <param name="itemID"></param>
        /// <param name="isBorrowItemOrder"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetBorrowItemOrOrderBorrowItemHistory(string operKey, string supcustID, string itemID, string sheetType, string startRow, string pageSize, string operID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where m.company_id = {companyID} and item_id = {itemID} and approve_time is not null and red_flag is null ";
            var selectMainTab = "";
            var selectDetailTab = "";
            string operCondi = "";
            if (!string.IsNullOrEmpty(operID)) 
            {
               operCondi = $@" and sm.seller_id={operID} ";
            }
          
            if (sheetType == "X" ||sheetType == "T")
            {
                selectMainTab = "sheet_BorrowItem_main";
                selectDetailTab = "sheet_BorrowItem_detail";
            }
            else if (sheetType == "XD" || sheetType == "TD")
            {
                selectMainTab = "sheet_BorrowItem_order_main";
                selectDetailTab = "sheet_BorrowItem_order_detail";
            }
            else if (sheetType == "CG")
            {
                selectMainTab = "sheet_buy_main";
                selectDetailTab = "sheet_buy_detail";
            }

            var sql = @$"SELECT sd.sheet_id,sd.item_id,iip.item_name,sd.unit_no,sd.quantity,sub_amount,sd.happen_time,sm.make_time,sm.sheet_no,sm.sheet_type,sd.remark,sd.remark_id,sd.real_price FROM {selectDetailTab} sd 
                        LEFT JOIN (SELECT * FROM {selectMainTab} WHERE company_id = {companyID}  AND supcust_id = {supcustID} AND red_flag is NULL AND approve_time IS NOT NULL) sm ON sm.sheet_id = sd.sheet_id 
                        LEFT JOIN (SELECT * FROM info_item_prop WHERE company_id = {companyID}  AND item_id = {itemID} ) iip ON sd.item_id = iip.item_id WHERE sd.company_id = {companyID} AND sd.item_id = {itemID} AND sheet_no IS NOT NULL  {operCondi} ORDER BY sd.happen_time DESC limit {pageSize} offset {startRow};";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
            sql = @$"SELECT COUNT(*) as total FROM {selectDetailTab} sd 
                        LEFT JOIN (SELECT * FROM {selectMainTab} WHERE company_id = {companyID}  AND supcust_id = {supcustID} AND red_flag is NULL AND approve_time IS NOT NULL) sm ON sm.sheet_id = sd.sheet_id 
                        LEFT JOIN (SELECT * FROM info_item_prop WHERE company_id = {companyID}  AND item_id = {itemID} ) iip ON sd.item_id = iip.item_id WHERE sd.company_id = {companyID} AND sd.item_id = {itemID} AND sheet_no IS NOT NULL  {operCondi};";
            QQ.Enqueue("total", sql);
            List<ExpandoObject> data = null;
            var total = "";
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "total")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total });
        }


        [HttpPost]
        //public async Task<IActionResult> AppendBrief([FromBody] dynamic data)
        //{
        //    string sheetID = data.sheetID;
        //    string newBrief = data.newBrief;
        //    string operKey = data.operKey;
        //    Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
        //    SheetBorrowItem sheet = new SheetBorrowItem(SHEET_RETURN.EMPTY, LOAD_PURPOSE.APPROVE);

        //    string msg = await sheet.AppendBrief(cmd, companyID, sheetID, newBrief);
        //    string result = msg == "" ? "OK" : "Error";
        //    if (msg != "") result = "Error";
        //    return Json(new { result, msg, });
        //}

        /// <summary>
        /// 销售单、销售订单 选择商品的历史记录
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="supcustID"></param>
        /// <param name="itemID"></param>
        /// <param name="isBorrowItemOrder"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetPrintInfo(string operKey, string supcustID, bool printArrearsBalance, bool printPrepayBalance, bool printOrderItemsBalance)
        {
            cmd.ActiveDatabase = "";
            Security.GetInfoFromOperKey(operKey, out string companyID);

            SQLQueue QQ = new SQLQueue(cmd);

            string sql = "";
            if (printArrearsBalance)
            {
                sql = $"select balance from arrears_balance where company_id={companyID} and supcust_id={supcustID};";
                QQ.Enqueue("arrears", sql);
            }
            if (printPrepayBalance)
            {
                sql = $"select sub_name,balance from prepay_balance b left join cw_subject s on b.sub_id=s.sub_id and b.company_id=s.company_id where b.company_id={companyID} and supcust_id={supcustID};";
                QQ.Enqueue("prepay", sql);
            }
            if (printOrderItemsBalance)
            {
                sql = $"select item_name,b.item_id,b.unit_no,sum(round(quantity,2)) as qty from items_ordered_balance b left join info_item_prop p on b.item_id=p.item_id where b.company_id={companyID} and b.supcust_id={supcustID} group by item_name,b.item_id,b.unit_no having sum(round(quantity,2))>0.01;";
                QQ.Enqueue("orderItems", sql);
            }


            List<ExpandoObject> arrearsBalance = null, prepayBalance = null, orderItemsBalance = null;
            
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "arrears")
                {
                    arrearsBalance = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "prepay")
                {
                    prepayBalance = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "orderItems")
                {
                    orderItemsBalance = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, arrearsBalance, prepayBalance, orderItemsBalance });
        }
        [HttpPost]
        public async Task<JsonResult> NoRedSheetAfterPrint(string operKey, [FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            string sql = "";
            string sheet_id = data.sheetID;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            sql = $@"select sheet_print_count from sheet_status_BorrowItem where company_id={companyID} and sheet_id={sheet_id}";
            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);        
            if (rec != null&&rec.sheet_print_count != ""&&int.Parse(rec.sheet_print_count) > 0) 
            { 
                result = "false";
                msg = "单据已打印，无法红冲/冲改";
            }
            return new JsonResult(new { result, msg });
        }

 
        [HttpGet]
        public async Task<JsonResult> GetBranchsInfo(string operKey, string branchID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $@"";
            string result = "OK";
            string msg = "";
            if (branchID.IsInvalid())
            {
                //一旦多仓
                sql = $@"select ib.branch_id as value,branch_name as text, (
           json_build_object('text', '默认库位', 'value', '0',
                                  'typeValue', '', 'typeText','')::jsonb ||
          json_agg(json_build_object('text', COALESCE(ibp.branch_position_name, ''), 'value',
                                   COALESCE(ibp.branch_position, '0'),
                                   'typeValue', COALESCE(ibp.type_id::TEXT, ''), 'typeText',
                                   COALESCE(ibpt.type_name, '')))::jsonb) as children
from info_branch ib
  right join info_branch_position ibp on ibp.company_id = {companyID} and ib.branch_id = ibp.branch_id 
  left join info_branch_position_type ibpt on ibpt.company_id = {companyID} and ibpt.type_id = ibp.type_id
       
where ib.company_id = {companyID} and ib.status <> 0 and COALESCE(ibp.position_status,'1')='1'
group by ib.branch_id, ib.branch_name
union
select ib.branch_id                                               as value,
       branch_name                                                as text,
     json_agg(json_build_object('text', '默认库位', 'value', '0',
                                  'typeValue', '', 'typeText',''))::jsonb as children

from info_branch ib
         left join info_branch_position ibp on ibp.company_id = {companyID} and ib.branch_id = ibp.branch_id
where ib.company_id = {companyID}
  and ib.status <> 0
and branch_position is null
group by ib.branch_id, ib.branch_name
;";
            }
            else
            {
                sql = $@"select COALESCE(ibp.branch_position::text,'0') as value,COALESCE(ibp.branch_position_name,'默认库位') as text
from info_branch_position ibp
         left join info_branch_position_type ibpt on ibpt.company_id = {companyID} and ibpt.type_id = ibp.type_id
         left join info_branch ib on ib.company_id = {companyID} and ib.branch_id = ibp.branch_id and ib.status <> 0
where ibp.company_id = {companyID} and ib.branch_id = {branchID} and COALESCE(ibp.position_status,'1')='1'
union
select '0' as value,'默认库位' as text
";
            }
            dynamic data = null;
            try
            {
                data= await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            }
            catch(Exception ex)
            {
                result = "ERROR";
                msg = "数据库位信息获取失败，请重试";
            }

            return new JsonResult(new { result, msg,data });

        }

        [HttpGet]
        public async Task<JsonResult> GetStockInfo(string operKey, string branchID,string branchPosition)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $@"";
            string result = "OK";
            string msg = "";
            if (branchID.IsInvalid()) branchID = "-1";
            if (branchPosition.IsInvalid()) branchPosition = "0";
            dynamic data = null;
            sql = $@"select * from stock s
left join info_item_batch itb on itb.company_id = {companyID} and itb.batch_id = s.batch_id
left join info_branch_position ibp ob ibp.company_id = {companyID} and ibp.branch_id = s.branch_id
where s.company_id = {companyID} and branch_id = {branchID}";
            try
            {
                data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            }
            catch (Exception ex)
            {
                result = "ERROR";
                msg = "数据库位信息获取失败，请重试";
            }

            return new JsonResult(new { result, msg, data });

        }
    }
}
