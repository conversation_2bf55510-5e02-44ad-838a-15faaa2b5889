﻿using ArtisanManage.Models;
using myJXC;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Services;

namespace ArtisanManage.MyJXC
{

    public class SheetRowSpecialPrice : SheetRowBase
    {
        public SheetRowSpecialPrice(){}
        [SaveToDB][FromFld] public string item_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_name { get; set; } = "";
        [SaveToDB][FromFld] public string unit_no { get; set; } = "";
        [SaveToDB][FromFld] public decimal unit_factor { get; set; } = 1;
        [SaveToDB][FromFld] public string real_price { get; set; }
        [SaveToDB][FromFld] public string special_price { get; set; }
        [SaveToDB][FromFld] public string recent_price { get; set; }
        [SaveToDB][FromFld] public string wholesale_price { get; set; }
        [SaveToDB(false)][FromFld(false)] public override int inout_flag { get; set; } = 1;
        //[SaveToDB(false)][FromFld(false)] public override int row_index { get; set; } = 1;


        public string s_special_price { get; set; } = "";
        public string m_special_price { get; set; } = "";
        public string b_special_price { get; set; } = "";

        public string s_recent_price { get; set; } = "";
        public string m_recent_price { get; set; } = "";
        public string b_recent_price { get; set; } = "";

        public string s_real_price { get; set; } = "";
        public string m_real_price { get; set; } = "";
        public string b_real_price { get; set; } = "";

        public string s_wholesale_price { get; set; } = "";

        public string m_wholesale_price { get; set; } = "";
        public string b_wholesale_price { get; set; } = "";

        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_unit_no { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_factor { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_factor { get; set; }
        public string s_unit_factor { get; set; } = "1";

    }
    public class SheetSpecialPrice : SheetBase<SheetRowSpecialPrice>
    {
        [SaveToDB][FromFld] public string supcust_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_name { get; set; } = "";
        [SaveToDB][FromFld] public string seller_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";

        [SaveToDB][FromFld] public string start_time { get; set; } = "";
        [SaveToDB][FromFld] public string end_time { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override int money_inout_flag { get; set; } = 1;


        public SheetSpecialPrice(LOAD_PURPOSE loadPurpose) : base("sheet_special_price_main", "sheet_special_price_detail", loadPurpose)
        {

            sheet_type = SHEET_TYPE.SHEET_SPECIAL_PRICE;

            if (loadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @" left join (select oper_id,oper_name as seller_name,mobile as seller_mobile from info_operator) seller on t.seller_id=seller.oper_id
                                 left join (select oper_id,oper_name as maker_name from info_operator) maker on t.maker_id=maker.oper_id
                                 left join (select oper_id,oper_name as approver_name from info_operator) approver on t.approver_id=approver.oper_id
                                 LEFT JOIN info_supcust sup on sup.company_id = t.company_id and sup.supcust_id = t.supcust_id
              ";
                DetailLeftJoin = @"left join sheet_special_price_main m on t.sheet_id=m.sheet_id
                                   left join info_item_prop ip on t.item_id = ip.item_id
                                   left join info_item_multi_unit mu on mu.item_id = t.item_id and mu.unit_factor = t.unit_factor
                                   left join(select item_id, s_unit->> 'f1' as s_unit_no,m_unit->> 'f1' as m_unit_no,(m_unit->> 'f2')::real as m_unit_factor,
                                                     b_unit->> 'f1' as b_unit_no,(b_unit->> 'f2')::real as b_unit_factor
                                              from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor)) as json from info_item_multi_unit where company_id=~company_id order by item_id', $$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)) unit_barcode on t.item_id = unit_barcode.item_id
                                   ";
            }
            else if (loadPurpose == LOAD_PURPOSE.APPROVE)
                DetailLeftJoin = @"left join sheet_special_sale_price_main m on t.sheet_id=m.sheet_id
                                   left join info_item_multi_unit mu on mu.item_id = t.item_id and mu.unit_factor = t.unit_factor
                                  ";
        }

        protected override void InitForSave()
        {
            base.InitForSave();
            if (seller_id == "") seller_id = OperID;

        }
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSheetValid(cmd);
            if (seller_id == "") return "必须指定业务员";

            return "OK";
        }
        class CInfoForApprove : CInfoForApproveBase
        {
            public List<dynamic> PreviousSheets = new List<dynamic>();
            public List<dynamic> FollowingSheets = new List<dynamic>();
        }



        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {

            base.GetInfoForApprove_SetQQ(QQ);
            string sql = "";
            if (SheetRows.Count > 0)
            {
                var happenTime = happen_time;
                if(happenTime=="")happenTime = CPubVars.GetDateText(DateTime.Now);
                string items_id = string.Join(",", SheetRows.Select(r => r.item_id));
                sql = $@"SELECT * FROM (
select d.*,m.start_time,m.end_time,row_number() over(partition by item_id,unit_factor order by d.happen_time desc) rn from sheet_special_price_detail d left join sheet_special_price_main m on m.sheet_id = d.sheet_id 
where m.approve_time is not null and red_flag is null  and d.company_id = {company_id} and m.supcust_id = {supcust_id} and d.item_id in ({items_id}) and m.happen_time <'{happenTime}'
)t
WHERE rn =1";
                QQ.Enqueue("previous_sheets",sql);
                sql = $@"select d.*,m.start_time,m.end_time,row_number() over(partition by item_id,unit_factor order by d.happen_time ) rn from sheet_special_price_detail d left join sheet_special_price_main m on m.sheet_id = d.sheet_id 
where m.approve_time is not null and red_flag is null  and d.company_id = {company_id} and m.supcust_id = {supcust_id} and d.item_id in ({items_id}) and m.happen_time >'{happenTime}'";
                QQ.Enqueue("following_sheets", sql);
            }

            
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {

            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if(sqlName== "previous_sheets")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    //info.PreviousSheets.Add(rec);
                    info.PreviousSheets.Add(new {item_id = rec.item_id,unit_no=rec.unit_no,unit_factor=rec.unit_factor,special_price= rec.special_price,start_time=rec.start_time,end_time= rec.end_time});
                }
            }else if(sqlName== "following_sheets")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach(dynamic rec in records)
                {
                    info.FollowingSheets.Add(new { index = rec.rn, item_id = rec.item_id, unit_no = rec.unit_no, unit_factor = rec.unit_factor, special_price = rec.special_price, start_time = rec.start_time, end_time = rec.end_time });
                }
            }

        }


        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info)
        {
            CInfoForApprove info1 = (CInfoForApprove)info;
            await base.OnSheetIDGot(cmd, sheetID, info);
            //var previouSheetsInfo = info1.PreviousSheets;
           // string specialPriceSql = "",sql="";
            //foreach(var row in SheetRows)
            //{
            //    specialPriceSql += $@"insert into client_special_item_price
            //                               (company_id,supcust_id,item_id,unit_no,unit_factor,real_price,special_price,start_time,end_time) 
            //                        values ({company_id},{supcust_id},{row.item_id},'{row.unit_no}',{row.unit_factor},{row.real_price},{row.special_price},'{start_time}','{end_time}')
            //                        on conflict (company_id,supcust_id,item_id,unit_factor) 
            //                        do update set special_price = {row.special_price},real_price ={row.real_price},start_time = '{start_time}',end_time='{end_time}';";
            //    //var itemSql = $@"insert into client_special_item_price(company_id,supcust_id,item_id,unit_no,unit_factor,real_price,special_price,start_time,end_time) values ({company_id},{supcust_id},{row.item_id},'{row.unit_no}',{row.unit_factor},{row.real_price},{row.special_price},'{start_time}','{end_time}')
            //    //            on conflict (company_id,supcust_id,item_id,unit_factor) do update set special_price = {row.special_price},real_price ={row.real_price},start_time = '{start_time}',end_time='{end_time}';";
            //    //if (previouSheetsInfo.Count > 0)
            //    //{
            //    //    foreach(var pRow in previouSheetsInfo)
            //    //    {

            //    //    }
            //    //    //specialPriceSql += $@"insert into client_special_item_price(company_id,supcust_id,item_id,unit_no,unit_factor,real_price,special_price,start_time,end_time) values ({company_id},{supcust_id},{row.item_id},'{row.unit_no}',{row.unit_factor},{row.real_price},{row.special_price},'{start_time}','{end_time}')
            //    //    //        on conflict (company_id,supcust_id,item_id,unit_factor) do update set old_start_time =client_special_item_price.start_time,old_end_time = client_special_item_price.end_time,  old_special_price = client_special_item_price.special_price , special_price = {row.special_price},real_price ={row.real_price},start_time = '{start_time}',end_time='{end_time}';";

            //    //}
            //    //specialPriceSql += itemSql;


            //}
            //sql += specialPriceSql;
            //if (sql != "")
            //{
            //    cmd.CommandText = sql;
            //    await cmd.ExecuteNonQueryAsync();
            //}




        }

    }
}
