using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;
using ArtisanManage.Services;
using ArtisanManage.YingJiangBackstage.Dao.OpenAccountDao;
using Newtonsoft.Json;
using NPOI.POIFS.Crypt.Dsig;
using System.Numerics;

namespace ArtisanManage.Pages.BaseInfo
{
    public class RsSellerEditModel : PageFormModel
    {
        public RsSellerEditModel(CMySbCommand cmd, string company_id = "", string oper_id = "") : base(Services.MenuId.rsSeller)
        {
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"reseller_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead",SqlFld="rs.reseller_id"}},
                {"reseller_name",new DataItem(){Title="分销商名称",Necessary=true,FldArea="divHead",SqlFld="rs.reseller_name"}},
                {"reseller_mobile",new DataItem(){Title="分销商电话",Necessary=true,FldArea="divHead",SqlFld="rs.reseller_mobile"}},
                {"reseller_count",new DataItem(){Title="端口数",FldArea="divHead" ,SqlFld="rs.reseller_count", Hidden = true} },
                {"plan_id",new DataItem(){Title="分销方案",FldArea="divHead",SqlFld="rp.plan_id",LabelFld="plan_name",ButtonUsage="list",SqlForOptions="select plan_id as v,plan_name as l from rs_plan"}},
            };

            m_idFld = "reseller_id"; m_nameFld = "reseller_name";
            m_tableName = "rs_seller";
            m_selectFromSQL = @"from rs_seller rs 
left join rs_plan rp on rs.company_id = rp.company_id and rs.plan_id = rp.plan_id
where rs.company_id= ~COMPANY_ID and reseller_id='~ID'";
        }

        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class RsSellerEditController : BaseController
    {
        public RsSellerEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            RsSellerEditModel model = new RsSellerEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            RsSellerEditModel model = new RsSellerEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        { 
            RsSellerEditModel model = new RsSellerEditModel(cmd);
            Security.GetInfoFromOperKey((string)request.operKey, out string companyID);
  
            string resellerId = request.reseller_id.Value ?? "";
            string resellerName = request.reseller_name.Value ?? "";
            string resellerMobile = request.reseller_mobile.Value ?? "";
            string planId = request.plan_id.Value ?? "";
            string sql = @$"";
            if (string.IsNullOrEmpty(resellerName)) {
                return Json(new { result = "Error", msg = "分销商名称未填", added = false, record = new { } });
            }
            if (string.IsNullOrEmpty(resellerMobile))
            {
                return Json(new { result = "Error", msg = "分销商手机号未填", added = false, record = new { } });
            }
            if (string.IsNullOrEmpty(planId))
            {
                return Json(new { result = "Error", msg = "分销方案未选", added = false, record = new { } });
            }
            sql = @$"SELECT plan_name,share,client_mapper FROM rs_plan where plan_id = {planId}";
            dynamic planInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            if (resellerId == "")
            {
                string errMsg = "创建分销商失败";
                try
                {
                    #region 1. 进行开户
                    // 获取父公司信息
                    dynamic mumCompany = null;
                    sql = @$"select * from g_company where company_id = {companyID};";
                    mumCompany = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    dynamic sonCompany = JsonConvert.DeserializeObject(JsonConvert.SerializeObject(mumCompany));
                    //string fatherCompanyName = mumCompany.company_name;
                    sonCompany.company_id = "";
                    sonCompany.boss_mobile = resellerMobile;
                    sonCompany.boss_name = resellerName;
                    sonCompany.company_name = resellerName;
                    sonCompany.company_remark = mumCompany.company_name + "下级分销商";
                    sonCompany.create_time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    sonCompany.fee_discount1 = "";
                    sonCompany.fee_discount2 = "";
                    sonCompany.user_count_discount1 = "";
                    sonCompany.user_count_discount2 = "";
                    sonCompany.user_count = 2;
                    
                    Dictionary<string, dynamic> checkResult = await OpenAccountQueryDao.CheckOpenAccountQuery(sonCompany, cmd);
                    int repeatCompanyNum = 0;
                    repeatCompanyNum = Convert.ToInt32(checkResult["countNum"]);
                    if (repeatCompanyNum > 0)
                    {
                        errMsg = "该公司已被该手机号开户，请检查";
                        throw new Exception("该公司已被该手机号开户，请检查");
                    }
                    Dictionary<string, dynamic> daoResult = await OpenAccountCreateDao.CreateOpenAccountApply(sonCompany, cmd);
                    dynamic sonCompanyId = daoResult["company_id"];
                    sonCompany.company_id = sonCompanyId;
               
                    sql = @$"select company_id from business where business_id = {sonCompany.business_id};";
                    dynamic businessInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    sonCompany.template_company_id = businessInfo.company_id;
                    // 根据sonCompany创建公司
                    await OpenAccountCreateDao.CreateOpenAccountInfo(sonCompany, cmd, tran);
                    #endregion
                    #region 2. 父公司创建客户档案
                    string plan_client_mapper = planInfo.client_mapper;
                    string fatherClientId = null;
                    string py_str = PinYinConverter.GetJP(resellerName).ToLower();
                    // 如果支持修改同步方式，就应当预先插入一个子账户的id
                    // 这段代码是如果不支持修改同步方式，当同步方式为使分销商作为客户，则不给分销商的账号创建一条客户记录
                    /*                    if (plan_client_mapper == "resellerAsClient")
                                        {
                                            // 分销商作为客户时，在这一步创建客户档案
                                            // 分销商业务员作为客户，在自行创建业务员时创建客户，这一步为父公司创建的员工不创建客户

                                            sql = @$"
                    insert into info_supcust (company_id,sup_name,boss_name,mobile,supcust_flag,status,region_id, other_region, supcust_remark, py_str) 
                    values  ({mumCompany.company_id},
                             '{sonCompany.company_name}',
                             '{sonCompany.company_name}',
                             '{resellerMobile}','C','1',
                             (select region_id from info_region where company_id = {mumCompany.company_id} and  mother_id = 0), 
                             '/' || (select region_id from info_region where company_id = {mumCompany.company_id} and  mother_id = 0) || '/',
                             '系统自动创建分销商客户档案--{resellerName}',
                             '{py_str}'
                             ) returning supcust_id;";
                                            dynamic fatherClientInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                                            fatherClientId = fatherClientInfo.supcust_id;
                                        }*/

                    dynamic supRet = await CDbDealer.Get1RecordFromSQLAsync($"select supcust_id from info_supcust where company_id = {mumCompany.company_id} and sup_name = '{sonCompany.company_name}'", cmd); 
                    if ( supRet != null) fatherClientId = supRet.supcust_id; 
                    else
                    {
                        dynamic rec = await CDbDealer.Get1RecordFromSQLAsync($"select region_id from info_region where company_id = {mumCompany.company_id} and  mother_id = 0",cmd);
                        string region_id = "null", other_region = "null";
                        if(rec != null)
                        {
                            region_id= rec.region_id;
                            other_region = "'/" + region_id + "/'";

                        }
                        sql = @$"
insert into info_supcust (company_id,                      sup_name,                   boss_name,            mobile,supcust_flag,status,region_id,  other_region,  supcust_remark,                             py_str) 
       values  ({mumCompany.company_id}, '{sonCompany.company_name}','{sonCompany.company_name}','{resellerMobile}',         'C',   '1',{region_id},{other_region},'系统自动创建分销商客户档案--{resellerName}','{py_str}') 
returning supcust_id;";
                        dynamic fatherClientInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                        fatherClientId = fatherClientInfo.supcust_id;
                    }

                    #endregion
                    #region 3. 子公司创建供应商档案
                    string sonSupplierId = "";
                    py_str = PinYinConverter.GetJP(mumCompany.company_name).ToLower();

                    supRet = await CDbDealer.Get1RecordFromSQLAsync($"select supcust_id from info_supcust where company_id = {sonCompany.company_id} and sup_name = '{mumCompany.company_name}'", cmd);
                    if (supRet != null) sonSupplierId = supRet.supcust_id;
                    else
                    {
                        sql = @$"
insert into info_supcust (company_id,                   sup_name,                 boss_name,                     mobile,supcust_flag,status, py_str) 
values      ({sonCompany.company_id},'{mumCompany.company_name}','{mumCompany.company_name}','{mumCompany.boss_mobile}',         'S','1', '{py_str}') returning supcust_id;";
                        dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                        sonSupplierId = rec.supcust_id;
                    }

                    #endregion
                    #region 4. 子公司创建员工
                    if (plan_client_mapper == "sellerAsClient")
                    {
                        // 更新子账户的客户记录
                        dynamic operInfo = await CDbDealer.Get1RecordFromSQLAsync($"select oper_id from g_operator where company_id = {sonCompany.company_id} and oper_name = '{resellerName}'", cmd);
                        cmd.CommandText = @$"update info_operator set rs_client_id = {fatherClientId} where company_id = {sonCompany.company_id} and oper_id = {operInfo.oper_id};
update info_supcust set rs_seller_id = {operInfo.oper_id} where company_id = {mumCompany.company_id} and supcust_id = {fatherClientId}";
                        await cmd.ExecuteNonQueryAsync();
                    }
                    dynamic operRet = await CDbDealer.Get1RecordFromSQLAsync($"select oper_id from g_operator where company_id = {sonCompany.company_id} and oper_name = '{mumCompany.company_name}'",cmd);
                    if (operRet == null)
                    {
                        sql = $@"
INSERT INTO g_operator (company_id,                  oper_name,                     mobile, oper_pw, is_admin,can_login, oper_status)
VALUES ('{sonCompany.company_id}', '{mumCompany.company_name}', '{mumCompany.boss_mobile}','123456',    false,    null,            1) returning oper_id;
";
                        dynamic operInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                        string oper_id = operInfo.oper_id;
                        sql = $@"
INSERT INTO info_operator (company_id,    oper_id,                    mobile, oper_group,                  oper_name, can_login, is_seller, depart_id,                       depart_path, role_id)
SELECT      '{sonCompany.company_id}','{oper_id}','{mumCompany.boss_mobile}',       null,'{mumCompany.company_name}',      true,      true, dept.depart_id, '/' || dept.depart_id || '/', ir.role_id
FROM info_operator io,
(
    SELECT depart_id FROM info_department WHERE company_id = '{sonCompany.company_id}'
) dept,
(
    SELECT role_id FROM info_role WHERE company_id = '{sonCompany.company_id}' AND role_name = '管理员'
) ir
WHERE io.company_id = '{sonCompany.template_company_id}';";
                        await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    }

                    #endregion
                    #region 5. 插入映射表
                    if (fatherClientId != null)
                    {//
                        sql = $@"
INSERT INTO rs_seller(company_id,   reseller_company_id, reseller_name,  reseller_count,   reseller_mobile,    plan_id,   client_id,               supplier_id,                company_name)
values ({mumCompany.company_id},{sonCompany.company_id},'{resellerName}',             2,'{resellerMobile}', '{planId}', '{fatherClientId}', '{sonSupplierId}', '{mumCompany.company_name}') returning reseller_id;
";
                    }
                    else
                    {
                        sql = $@"
INSERT INTO rs_seller(company_id,   reseller_company_id, reseller_name, reseller_count,    reseller_mobile,    plan_id,   client_id,                 supplier_id,                company_name)
values ({mumCompany.company_id},{sonCompany.company_id},'{resellerName}',            2, '{resellerMobile}', '{planId}',  null,               '{sonSupplierId}', '{mumCompany.company_name}') returning reseller_id;
";
                    }
                    
                    dynamic resellerInfo =  await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    resellerId = resellerInfo.reseller_id;

                    
                    #endregion


                    #region 6. 同步商品

                    string plan_name = planInfo.plan_name;
                    dynamic itemInfoSyncResult = await ResellerService.ItemInfoSyncService(new
                    {
                        operKey = (string)request.operKey,
                        resellerCompanyId = sonCompany.company_id,
                        planID = planId,
                    }, cmd, tran);
                    if (itemInfoSyncResult.result != "OK")
                    {
                        return Json(new { result = "Error", msg = itemInfoSyncResult.msg, added = false, record = new { } });
                    }
                    #endregion
                    #region 7. 同步类别
                    dynamic classInfoSyncResult = await ResellerService.ClassSyncService(new
                    {
                        fatherItemId = "",
                        fatherCompanyId = mumCompany.company_id,
                        sonCompanyId = sonCompany.company_id,
                        planId = planId,
                        brandId = "",
                        reverseBind = false,
                    }, cmd, tran);
                    if (classInfoSyncResult.result != "OK")
                    {
                        return Json(new { result = "Error", msg = classInfoSyncResult.msg, added = false, record = new { } });
                    }
                    #endregion
                    tran.Commit();
                    return Json(new { result = "OK", msg = "", added = true, record = new
                    {
                        reseller_id = resellerId,
                        reseller_name = resellerName,
                        reseller_mobile = resellerMobile,
                        reseller_count = 2,
                        plan_id = planId,
                        plan_name = plan_name
                    }
                    });
                }
                catch (Exception e)
                {
                    string logErrMsg = $"In ReSellerEdit Save,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}";
                    NLogger.Error(e.ToString());
                    MyLogger.LogMsg(logErrMsg, companyID);
                    Console.WriteLine(e);
                    tran.Rollback();
                    return Json(new { result = "Error", msg = errMsg, added = false, record = new {}});
                }
            }
            else
            {
                string errMsg = "编辑分销商失败";
                // sql = @$"select company_id, reseller_id, reseller_company_id, reseller_name,  reseller_mobile, from rs_seller where reseller_id = {resellerId}";
                // dynamic originReSellerInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                // string resellerCompanyId = originReSellerInfo.reseller_company_id;
                // string reseller_name = originReSellerInfo.reseller_name;
                // string reseller_mobile = originReSellerInfo.reseller_mobile;
                try
                {
                    // 似乎不需要更新
                    //                     #region 2.员工信息更新
                    //                     sql = $@"UPDATE info_operator 
                    // SET oper_name = '{resellerName}', mobile = '{resellerMobile}' 
                    // WHERE company_id = {resellerCompanyId} and oper_name = '{reseller_name}' and mobile = '{reseller_mobile}'";
                    //                     cmd.CommandText = sql;
                    //                     int infoOperatorNumber = await cmd.ExecuteNonQueryAsync();
                    //                     if (infoOperatorNumber == 0)
                    //                     {
                    //                         throw new Exception("子公司");
                    //                     }
                    //
                    //                     sql = $@"UPDATE g_operator 
                    // SET oper_name = '{resellerName}', mobile = '{resellerMobile}' 
                    // WHERE company_id = {resellerCompanyId} and oper_name = '{reseller_name}' and mobile = '{reseller_mobile}'";
                    //                     await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    //                     #endregion

                    sql = $"select * from rs_seller where reseller_id = {resellerId}";
                    dynamic resellerRet = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    if ((string)resellerRet.plan_id != planId)
                    {
                        // 切换了分销方案
                        #region 品牌更新同步
                        // 
                        errMsg = "商品档案同步失败";
                        sql = $@"select * from rs_seller rs left join rs_plan rp on rs.plan_id = rp.plan_id where reseller_id = {resellerId}";
                        dynamic oldPlanInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                        sql = $@"select * from rs_plan where plan_id = {planId}";
                        dynamic newPlanInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                        if ((string)newPlanInfo.brand_id != (string)oldPlanInfo.brand_id)
                        {
                            // 更换了方案，档案不一致
                            dynamic brandChangeRet = await ResellerService.BrandChangeSyncService(new
                            {
                                operKey = (string)request.operKey,
                                brandId = newPlanInfo.brand_id,
                                planId = planId,
                                fatherCompanyId = companyID,
                                sonCompanyId = resellerRet.reseller_company_id,
                                priceSyncOptions = newPlanInfo.price_sync_options

                            }, cmd, tran);
                            if (brandChangeRet.result != "OK")
                            {
                                return Json(new { result = "Error", msg = "分销商品牌商品档案同步失败", added = false, record = new { } });
                            }
                        }
                        #endregion
                        #region 同步类别
                        errMsg = "商品档案类别同步失败";
                        dynamic classInfoSyncResult = await ResellerService.ClassSyncService(new
                        {
                            fatherItemId = "",
                            fatherCompanyId = companyID,
                            sonCompanyId = resellerRet.reseller_company_id,
                            planId = planId,
                            brandId = "",
                            reverseBind = false,
                        }, cmd, tran);
                        if (classInfoSyncResult.result != "OK")
                        {
                            return Json(new { result = "Error", msg = classInfoSyncResult.msg, added = false, record = new { } });
                        }
                        #endregion
                        #region 切换映射方式可能需要同步业务员
                        errMsg = "业务员同步失败";
                        if ((string)newPlanInfo.client_mapper !=(string)oldPlanInfo.client_mapper && (string)newPlanInfo.client_mapper == "sellerAsClient")
                        {
                            dynamic clientSyncResult = await ResellerService.ClientToSellerSyncService(new
                            {
                                operKey = (string)request.operKey,
                                sonCompanyId = resellerRet.reseller_company_id,
                                sonCompanyName = resellerRet.reseller_name,
                                fatherCompanyId = resellerRet.company_id,
                                fatherCompanyName = resellerRet.company_name,

                            }, cmd, tran);
                            if (clientSyncResult.result != "OK")
                            {
                                return Json(new { result = "Error", msg = clientSyncResult.msg, added = false, record = new { } });
                            }
                        }
                        #endregion
                    }


                    #region 映射表更新
                    sql = $@"UPDATE rs_seller SET reseller_name = '{resellerName}', reseller_mobile = '{resellerMobile}', plan_id = '{planId}' WHERE reseller_id = {resellerId}";
                    await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    #endregion
                    tran.Commit();
                    return Json(new { result = "OK", msg = "", added = false, record = new {}});
                }
                catch (Exception e)
                {
                    string logErrMsg = $"In ReSellerEdit Save,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}";
                    NLogger.Error(e.ToString());
                    MyLogger.LogMsg(logErrMsg, companyID);
                    Console.WriteLine(e);
                    tran.Rollback();
                    return Json(new { result = "Error", msg = errMsg, added = false, record = new {}});
                }
            }
        }
    }
}