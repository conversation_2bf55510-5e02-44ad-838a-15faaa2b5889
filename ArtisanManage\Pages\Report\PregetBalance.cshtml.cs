﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class PregetBalanceModel : PageQueryModel
    { 
        public string SubType = "YS";
        /*
        public PregetBalanceModel(CMySbCommand cmd) : base(Services.MenuId.pregetBalance)
        {
            this.cmd = cmd;
            this.PageTitle = "预收款余额";
            QueryConditionIsAfterAnd = true;
            FuncDealCondition = (condi) =>
            {
                return condi.Replace("'", "''");
            };
           
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, CompareOperator="=", QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, SqlFld="sm.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="sc.supcust_id",
                SqlForOptions=CommonTool.selectSupcust } },
                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
                {"group_id",new DataItem(){Title="渠道",FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                //{"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入客户名称", SqlFld="sup_name",CompareOperator="like"}},
                {"now_balance_min",new DataItem(){FldArea="divHead",Title="余额&ge;",ForQuery=false}},
                {"now_balance_max",new DataItem(){FldArea="divHead",Title="余额&le;",ForQuery=false}},

            };

            Grids = new Dictionary<string, QueryGrid>()
            {
               
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sup_name",     new DataItem(){Title="客户名称",  Width="150",SqlFld="sup_name",Pinned=true }},
                       {"supcust_id",    new DataItem(){Title="客户编号",  Width="250",SqlFld="supcust_id",HideOnLoad=true,Hidden=true}},
                       {"prepay_subname",  new DataItem(){Title="预收款账户", Width="70",HideTopGroupName=true,
                           FuncGetSubColumns = async (col) =>
                           {
                                ColumnsResult result=new ColumnsResult();
                                Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>();
                                List<System.Dynamic.ExpandoObject> payways =  await CDbDealer.GetRecordsFromSQLAsync($"select sub_id,sub_name from cw_subject where company_id={company_id} and sub_type = '{this.SubType}';",cmd);
                                
                                string crossTable1="",crossTable2="";
                                string totalF1="",totalF2="",totalF3="";
                                foreach(dynamic pw in payways)
                                { string colKey="";
                                   string sub_name='a'+pw.sub_name;
                                   DataItem subcol;
                                   subcol =new DataItem{
                                       Title="增",Width=col.Width,SubMumTitle=pw.sub_name,ShowSum=true,Linkable = true,
                                       SqlFld=$" ({sub_name}->>'f1')::numeric"
                                   };
                                   colKey="pw_add_"+ (string) pw.sub_id;
                                   subColumns.Add(colKey, subcol);
                                   
                                   if(totalF1!="") totalF1+="+"; totalF1+=$" COALESCE(({sub_name}->>'f1')::float,0)";
                                   if(totalF2!="") totalF2+="+"; totalF2+=$" COALESCE(({sub_name}->>'f2')::float,0)";
                                   if(totalF3!="") totalF3+="+"; totalF3+=$" COALESCE(({sub_name}->>'f3')::float,0)";

                                   subcol =new DataItem{
                                       Title="减",  Width=col.Width,SubMumTitle=pw.sub_name,ShowSum=true,Linkable = true,
                                       SqlFld=$" (a{(string) pw.sub_name}->>'f2')::numeric"
                                    };
                                   colKey="pw_reduce_"+ (string) pw.sub_id;
                                   subColumns.Add(colKey, subcol);
                                   
                                   subcol =new DataItem{
                                       Title="当前余额",Width=col.Width,SubMumTitle=pw.sub_name,ShowSum=true,
                                       SqlFld=$"(a{(string) pw.sub_name}->>'f3')::numeric"
                                   };
                                   colKey="pw_balance_"+ (string) pw.sub_id;
                                   subColumns.Add(colKey, subcol);
                                   

                                   if(crossTable1!="") crossTable1+=",";
                                   crossTable1+=$"('{pw.sub_name}'::text)";
                                    if(crossTable2!="") crossTable2+=",";
                                   crossTable2+=$"{sub_name} jsonb";

                                }

                                { string colKey="";
                                   string sub_name="总计";
                                   DataItem subcol; string fld;
                                   subcol =new DataItem{
                                       Title="增",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld =$"{totalF1}"
                                   };
                                   colKey="pw_add_all";
                                   subColumns.Add(colKey, subcol);
                                   

                                   subcol =new DataItem{
                                       Title="减",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld=$"{totalF2}"
                                   };
                                   colKey="pw_reduce_all";
                                   subColumns.Add(colKey, subcol);
                                  

                                   subcol =new DataItem{
                                       Title="余额",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld=$"{totalF3}"
                                   };
                                   colKey="pw_balance_all";
                                   subColumns.Add(colKey, subcol);
                                   

                                   if(crossTable1!="") crossTable1+=",";
                                   crossTable1+=$"('{sub_name}'::text)";
                                   if(crossTable2!="") crossTable2+=",";
                                   crossTable2+=$"{sub_name} jsonb";
                                }


                               
                                result.Columns=subColumns;
                                SQLVariable1=crossTable1;
                                SQLVariable2=crossTable2;

                                return result;
                           }
                       }} 
               //        {"prepay_balance",   new DataItem(){Title="预收款余额",  Width="80",SqlFld="sum(p.total_amount)"}},
                     },
                     QueryFromSQL=@"
from crosstab('
   select sup_name,sub_name,row_to_json((add_amount,reduce_amount,now_balance)) as json from 
   (
       SELECT
        sc.sup_name,
        cw.sub_name,
        SUM ( CASE WHEN cah.change_amount > 0 and cw.sub_type IN ( ''YS'', ''DH'' ) AND red_flag IS NULL AND happen_time >= ''~VAR_startDay''  AND happen_time <= ''~VAR_endDay'' THEN cah.change_amount ELSE 0 END ) AS add_amount,
        SUM ( CASE WHEN cah.change_amount < 0 and cw.sub_type IN ( ''YS'', ''DH'' ) AND red_flag IS NULL AND happen_time >= ''~VAR_startDay''  AND happen_time <= ''~VAR_endDay'' THEN cah.change_amount ELSE 0 END ) AS reduce_amount,
        balance now_balance
        from prepay_balance pb 
        left join 
             client_account_history cah on pb.supcust_id=cah.supcust_id and pb.company_id = cah.company_id
        LEFT JOIN cw_subject AS cw ON cw.sub_id = pb.sub_id   and pb.company_id = cw.company_id
        LEFT JOIN info_supcust AS sc ON pb.supcust_id = sc.supcust_id and pb.company_id = sc.company_id
        where pb.company_id = ~COMPANY_ID and pb.sub_id = cah.sub_id ~QUERY_CONDITION ~VAR_now_balance_min ~VAR_now_balance_max
        GROUP BY 
        pb.supcust_id,pb.sub_id,balance,sup_name,sub_name
        ORDER BY
        pb.supcust_id
    ) t ',
    $$values ~SQL_VARIABLE1 $$) as tb_result(sup_name text, ~SQL_VARIABLE2) 
    LEFT JOIN info_supcust AS sc ON tb_result.sup_name = sc.sup_name and sc.company_id =~COMPANY_ID
",
                     QueryGroupBySQL = "",
                     QueryOrderSQL=""
                  }
                } 
            };             
        }
        */

        public PregetBalanceModel(CMySbCommand cmd, bool useMainDb = false) : base(Services.MenuId.pregetBalance)
        {
            if (useMainDb) this.Database = "";
            this.cmd = cmd;
            this.PageTitle = "预收款余额";
            QueryConditionIsAfterAnd = true;
            FuncDealCondition = (condi) =>
            {
                return condi.Replace("'", "''");
            };

            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, CompareOperator="=", QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, SqlFld="sm.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="sc.supcust_id",
                SqlForOptions=CommonTool.selectSupcust } },
                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region order by mother_id,order_index "
                }},
                {"group_id",new DataItem(){Title="渠道",FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                //{"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入客户名称", SqlFld="sup_name",CompareOperator="like"}},
                {"now_balance_min",new DataItem(){FldArea="divHead",Title="余额&ge;",ForQuery=false}},
                {"now_balance_max",new DataItem(){FldArea="divHead",Title="余额&le;",ForQuery=false}},

            };

            Grids = new Dictionary<string, QueryGrid>()
            {

                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sup_name",     new DataItem(){Title="客户名称",  Width="150",SqlFld="sc.sup_name",Pinned=true }},
                       {"supcust_id",    new DataItem(){Title="客户编号",  Width="250",SqlFld="sc.supcust_id",HideOnLoad=true,Hidden=true}},
                       {"prepay_subname",  new DataItem(){Title="预收款账户", Width="70",HideTopGroupName=true,
                           FuncGetSubColumns = async (col) =>
                           {
                                
                                ColumnsResult result=new ColumnsResult();
                                Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>();
                                List<System.Dynamic.ExpandoObject> payways =  await CDbDealer.GetRecordsFromSQLAsync($"select sub_id,sub_name,is_order from cw_subject where company_id={company_id} and sub_type = '{this.SubType}';",cmd);

                                string crossTable1="",crossTable2="";
                                string totalF1="",totalF2="",totalF3="",totalF4="",totalF5="",totalF6="";
                                foreach(dynamic pw in payways)
                                { string colKey="";
                                   string sub_name='a'+pw.sub_name;
                                   DataItem subcol;
                                   subcol =new DataItem{
                                       Title="期初",Width=col.Width,SubMumTitle=pw.sub_name,ShowSum=true,
                                       SqlFld=$"( ((a{(string) pw.sub_name}->>'f3')::numeric) -  (({sub_name}->>'f1')::numeric) + ((a{(string) pw.sub_name}->>'f2')::numeric) )"
                                   };
                                   colKey="pw_start_"+ (string) pw.sub_id;
                                   subColumns.Add(colKey, subcol);

                                   subcol =new DataItem{
                                       Title="增",Width=col.Width,SubMumTitle=pw.sub_name,ShowSum=true,Linkable = true,
                                       SqlFld=$"  COALESCE(({sub_name}->>'f1')::numeric,0)"
                                   };
                                   colKey="pw_add_"+ (string) pw.sub_id;
                                   subColumns.Add(colKey, subcol);

                                   if(totalF1!="") totalF1+="+"; totalF1+=$" COALESCE(({sub_name}->>'f1')::float,0)";
                                   if(totalF2!="") totalF2+="+"; totalF2+=$" COALESCE(({sub_name}->>'f2')::float,0)";
                                   if(totalF3!="") totalF3+="+";totalF3+=$"COALESCE(({sub_name}->>'f3')::float,0)";
                                    if(pw.is_order.ToLower() != "true") {
                                        if(totalF4!="") totalF4+="+"; totalF4+=$" COALESCE(({sub_name}->>'f1')::float,0)";
                                   if(totalF5!="") totalF5+="+"; totalF5+=$" COALESCE(({sub_name}->>'f2')::float,0)";
                                   if(totalF6!="") totalF6+="+";totalF6+=$"COALESCE(({sub_name}->>'f3')::float,0)";
                                    }
                                   subcol =new DataItem{
                                       Title="减",  Width=col.Width,SubMumTitle=pw.sub_name,ShowSum=true,Linkable = true,
                                       SqlFld=$" COALESCE((a{(string) pw.sub_name}->>'f2')::numeric,0)"
                                    };
                                   colKey="pw_reduce_"+ (string) pw.sub_id;
                                   subColumns.Add(colKey, subcol);

                                   subcol =new DataItem{
                                       Title="当前余额",Width=col.Width,SubMumTitle=pw.sub_name,ShowSum=true,
                                       SqlFld=$"COALESCE((a{(string) pw.sub_name}->>'f3')::numeric,0)"
                                   };
                                   colKey="pw_balance_"+ (string) pw.sub_id;
                                   subColumns.Add(colKey, subcol);
                               

                                   if(crossTable1!="") crossTable1+=",";
                                   crossTable1+=$"('{pw.sub_name}'::text)";
                                    if(crossTable2!="") crossTable2+=",";
                                   crossTable2+=$"{sub_name} jsonb";

                                }

                                { string colKey="";
                                   string sub_name="合计";
                                   DataItem subcol; string fld;
                                   string totalStart="";
                                   if (totalF3.IsValid())
                                   {
                                       totalStart+=$"({totalF3})";
                                   }
                                   else
                                   {
                                       totalStart+="0";
                                   }
                                   if (totalF1.IsValid())
                                   {
                                       totalStart+=$"-({totalF1})";
                                   }
                                   else
                                   {
                                       totalStart+="-0";
                                   }
                                   if (totalF2.IsValid())
                                   {
                                       totalStart+=$"+({totalF2})";
                                   }
                                   else
                                   {
                                       totalStart+="-0";
                                   }
                                   totalStart=$"( {totalStart} )";
                                   subcol =new DataItem{
                                       Title="期初",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld =$"{totalStart}"
                                   };
                                   colKey="pw_start_all";
                                   subColumns.Add(colKey, subcol);

                                   subcol =new DataItem{
                                       Title="增",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld =$"{totalF1}"
                                   };
                                   colKey="pw_add_all";
                                   subColumns.Add(colKey, subcol);


                                   subcol =new DataItem{
                                       Title="减",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld=$"{totalF2}"
                                   };
                                   colKey="pw_reduce_all";
                                   subColumns.Add(colKey, subcol);


                                   subcol =new DataItem{
                                       Title="余额",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld=$"{totalF3}"
                                   };
                                   this.SQLVariables["prepay_bal_fld"]=totalF3;
								   colKey="pw_balance_all";
                                   subColumns.Add(colKey, subcol);


                                   if(crossTable1!="") crossTable1+=",";
                                   crossTable1+=$"('{sub_name}'::text)";
                                   if(crossTable2!="") crossTable2+=",";
                                   crossTable2+=$"{sub_name} jsonb";
                                }
                                { string colKey="";
                                   string sub_name="合计（不含定货会）";
                                   DataItem subcol; string fld;
                                   string totalStartNoDH="";
                                   if (totalF6.IsValid())
                                   {
                                       totalStartNoDH+=$"({totalF6})";
                                   }
                                   else
                                   {
                                       totalF6 = "0";
                                       totalStartNoDH+="0";
                                   }
                                   if (totalF4.IsValid())
                                   {
                                       totalStartNoDH+=$"-({totalF4})";
                                   }
                                   else
                                   {
                                       totalF4 = "0";
                                       totalStartNoDH+="-0";
                                   }
                                   if (totalF5.IsValid())
                                   {
                                       totalStartNoDH+=$"+({totalF5})";
                                   }
                                   else
                                   {
                                       totalF5 = "0";
                                       totalStartNoDH+="-0";
                                   }
                                   totalStartNoDH=$"( {totalStartNoDH} )";
                                   subcol =new DataItem{
                                       Title="期初",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld =$"{totalStartNoDH}"
                                   };
                                   colKey="pw_start_all_without_order";
                                   subColumns.Add(colKey, subcol);


                                   subcol =new DataItem{
                                       Title="增",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,Hidden=true,
                                       SqlFld =$"{totalF4}"
                                   };
                                   colKey="pw_add_all_without_order";
                                   subColumns.Add(colKey, subcol);


                                   subcol =new DataItem{
                                       Title="减",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld=$"{totalF5}"
                                   };
                                   colKey="pw_reduce_all_without_order";
                                   subColumns.Add(colKey, subcol);


                                   subcol =new DataItem{
                                       Title="余额",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld =$"{totalF6}"
                                   };
                                   colKey="pw_balance_all_without_order";
                                   subColumns.Add(colKey, subcol);


                                   if(crossTable1!="") crossTable1+=",";
                                   crossTable1+=$"('{sub_name}'::text)";
                                   if(crossTable2!="") crossTable2+=",";
                                   crossTable2+=$"{sub_name} jsonb";
                                }
                                result.Columns=subColumns;
                                SQLVariable1=crossTable1;
                                SQLVariable2=crossTable2;

                                return result;
                           }
                       }} ,
					   {"arrears_balance",    new DataItem(){Title="应收款余额", SqlFld="ab.balance", Width="250" }},
					   {"prepay_arrears_bal",    new DataItem(){Title="往来余额",SqlFld="~VAR_prepay_bal_fld-coalesce(ab.balance,0)",  Width="250" }},

               //        {"prepay_balance",   new DataItem(){Title="预收款余额",  Width="80",SqlFld="sum(p.total_amount)"}},
                     },

                     //不要改reduce_amount的方向！会影响财务！！
                     //请保持PrepayBalanceModel和PregetBalanceModel中reduce_amount方向一致！！
                     QueryFromSQL=@"
from crosstab
(
'
   select supcust_id,sub_name,row_to_json((add_amount,reduce_amount,now_balance)) as json from
   (
        SELECT
            pb.supcust_id,cw.sub_name,
            SUM ( CASE WHEN cah.change_amount > 0 and cw.sub_type IN ( ''YS'', ''DH'' ) AND red_flag IS NULL THEN cah.change_amount ELSE 0 END ) AS add_amount,
            SUM ( CASE WHEN cah.change_amount < 0 and cw.sub_type IN ( ''YS'', ''DH'' ) AND red_flag IS NULL THEN -cah.change_amount ELSE 0 END ) AS reduce_amount,
            balance now_balance
        FROM prepay_balance pb 
        LEFT JOIN client_account_history cah on pb.supcust_id=cah.supcust_id and pb.sub_id = cah.sub_id and pb.company_id = cah.company_id and happen_time >= ''~VAR_startDay'' AND happen_time <= ''~VAR_endDay'' 
        LEFT JOIN cw_subject AS cw ON cw.sub_id = pb.sub_id and pb.company_id = cw.company_id      
        LEFT JOIN info_supcust AS sc ON pb.supcust_id = sc.supcust_id and sc.company_id =~COMPANY_ID
        WHERE pb.company_id = ~COMPANY_ID and cw.sub_type=''YS'' ~QUERY_CONDITION ~VAR_now_balance_min ~VAR_now_balance_max
        GROUP BY pb.supcust_id,pb.sub_id,balance,sub_name
        ORDER BY pb.supcust_id
    ) t
',
    $$values ~SQL_VARIABLE1 $$
) as tb_result(supcust_id integer, ~SQL_VARIABLE2) 
 LEFT JOIN arrears_balance ab ON tb_result.supcust_id = ab.supcust_id and ab.company_id =~COMPANY_ID
 LEFT JOIN info_supcust AS sc ON tb_result.supcust_id = sc.supcust_id and sc.company_id =~COMPANY_ID
",
                     //LEFT JOIN info_supcust AS sc ON tb_result.supcust_id = sc.supcust_id and sc.company_id =~COMPANY_ID
                     QueryGroupBySQL = "",
                     QueryOrderSQL="order by pw_balance_all desc"
                  }
                }
            };
        }


        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {

            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;
            if (DataItems["now_balance_min"].Value != "")
            {
                SQLVariables["now_balance_min"] = "and balance >=" + DataItems["now_balance_min"].Value ;

            }
            else
            {
                SQLVariables["now_balance_min"] = " ";

            }
            if (DataItems["now_balance_max"].Value != "")
            {
                SQLVariables["now_balance_max"] = "and balance <=" + DataItems["now_balance_max"].Value;

            }
            else
            {
                SQLVariables["now_balance_max"] = " ";

            }
        }

    }



    [Route("api/[controller]/[action]")]
    public class PregetBalanceController : QueryController
    { 
        public PregetBalanceController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            PregetBalanceModel model = new PregetBalanceModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            PregetBalanceModel model = new PregetBalanceModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            PregetBalanceModel model = new PregetBalanceModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
