﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using System;
using System.Linq;
using static ArtisanManage.Models.AttenceLeave;
using NPOI.SS.Formula.Functions;
using Quartz;
using Humanizer;
using static ArtisanManage.Pages.Report.AttendanceReportAllViewController;
using System.Reflection;
using ArtisanManage.Pages.BaseInfo;

namespace ArtisanManage.Pages.Report
{
    public class AttendanceReportAllViewModel : PageQueryModel
    {
        public AttendanceReportAllViewModel(CMySbCommand cmd) : base(Services.MenuId.infoBranch)
        {
            this.cmd = cmd;

            DataItems = new Dictionary<string, DataItem>()
            {
                {"start_date",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="start_date",CompareOperator="=",Value=CPubVars.GetDateText(new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1))}},
                {"end_date",new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="end_date",CompareOperator="=",Value=CPubVars.GetDateText(DateTime.Now.Date.AddHours(23).AddMinutes(59).AddSeconds(59))} }
            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {

                     IdColumn="flow_id",

                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"oper_name", new DataItem(){Title="业务员",SqlFld="oper_name",  Width="100"}},
                       {"zc_count", new DataItem(){Title="正常天数",SqlFld="zc_count", Width="100"}},
                       {"cd_count", new DataItem(){Title="迟到天数",SqlFld="cd_count", Width="100"}},
                       {"zt_count", new DataItem(){Title="早退天数",SqlFld="zt_count", Width="100"}},
                        {"qj_count", new DataItem(){Title="请假天数",SqlFld="qj_count", Width="100"}},
                         {"xx_count", new DataItem(){Title="休息天数",SqlFld="xx_count", Width="100"}},
                       {"qq_count", new DataItem(){Title="缺勤天数",SqlFld="qq_count", Width="100"}},
                       {"wqt_count", new DataItem(){Title="未签退",SqlFld="wqt_count", Width="100"}},
               //        {"", new DataItem(){Title="其他",SqlFld="_", Width="100"}},
                     },
                  }
                }
            };
        }

        public async Task OnGet()
        {
            await InitGet(cmd);
        }

        public override void BeforeExportExcel(Dictionary<int, Dictionary<string, dynamic>> rows)
        {
            Console.WriteLine(rows.Count);
        }
    }


    [Route("api/[controller]/[action]")]
    public class AttendanceReportAllViewController : Controller
    {
        CMySbCommand cmd;
        public AttendanceReportAllViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        public class Row
        {
            //      public string oper_id { get; set; }
            public string oper_name { get; set; }
            public int qq_count { get; set; } = 0;
            public int qj_count { get; set; } =0; 
            public int xx_count { get; set; } = 0;
            public int cd_count { get; set; } = 0;
            public int zc_count { get; set; } = 0;
            public int wqt_count { get; set; } = 0;
            public int zt_count { get; set; } = 0;

        }
        [HttpGet]
        public async Task<object> GetQueryRecords(string start_date, string end_date, string operKey)
        {

            Security.GetInfoFromOperKey(operKey, out string companyID, out string my_oper_id);

            List<Row> records = new List<Row>();

            start_date = start_date.Substring(0, 10);
            end_date = end_date.Substring(0, 10);
            DateTime st = DateTime.Parse(start_date).Date;
            DateTime et = DateTime.Parse(end_date).Date;

            string sql = $@"
SELECT  ar.oper_id,iop.oper_name,ar.status,start_time,end_time,addr,longitude,latitude,schedule_id from attence_record ar 
left join g_operator iop on iop.oper_id=ar.oper_id
where ar.company_id={companyID} and ar.start_time between '{start_date} 00:00' and '{end_date} 23:59'
";
            dynamic attendance_records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            string recsql = $@"
select members_id,check_days_id,check_longitude,check_latitude,check_distance,check_start_time,check_end_time,schedule from info_attence_group where company_id='{companyID}'
";
            dynamic record = await CDbDealer.GetRecordsFromSQLAsync(recsql, cmd);

            string opsql = $@"select oper_name,oper_id from g_operator where company_id = {companyID}";
            dynamic oplist = await CDbDealer.GetRecordsFromSQLAsync(opsql, cmd);
            Dictionary<string, string> keyValuePairs = new Dictionary<string, string>();
            foreach (var o in oplist)
            {
                keyValuePairs[o.oper_id] = o.oper_name;
            }

            foreach (var row in record)
            {
                string[] seller_ids = row.members_id.Split(",");
                string[] checkDaysList = row.check_days_id.Split(",");
                foreach (string seller_id in seller_ids)
                {
                    Row single_row = new Row();
                    //   single_row.oper_id = seller_id;

                    if (seller_id == "") continue;

                    single_row.oper_name = keyValuePairs[seller_id];
                    int flag = 0;
                    int ii = 0;
                    for (DateTime sourceDate = st; sourceDate <= et; sourceDate = sourceDate.AddDays(1))
                    {
                        ii++;
                        string sourceDayOfWeek = sourceDate.DayOfWeek.ToString("d");
                        flag = 0;
                        foreach (var rec in attendance_records)
                        {
                            DateTime signDate = Convert.ToDateTime(rec.start_time);
                            if (signDate.Date == sourceDate.Date &&rec.oper_id == seller_id) // 直接比较日期
                            {
                                flag = 1; // 当天有记录
                                if (checkDaysList.Contains(sourceDayOfWeek)) // 工作日
                                {
                                    if (rec.status != null)
                                    {
                                        string[] sta = rec.status.Split(",");
                                        foreach (string s in sta)
                                        {
                                            switch (s)
                                            {
                                                case "CD": single_row.cd_count++; break;
                                                case "QJ": single_row.qj_count++; break;
                                                case "ZT": single_row.zt_count++; break;
                                                case "WQT": single_row.wqt_count++; break;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        single_row.zc_count++;
                                    }
                                    break;
                                }
                            }
                        }
                        if (flag == 0)
                        {
                            if (checkDaysList.Contains(sourceDayOfWeek))
                                single_row.qq_count++;
                            else
                                single_row.xx_count++;
                        }
                    }
                    records.Add(single_row); 
                }
            }
            return new JsonResult(
             new
             {
                 result = "OK",
                 rows = records,
                 rowsCount = records.Count()
             });
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel(string sheetType)
        {
            AttendanceReportAllViewModel model = new AttendanceReportAllViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
