﻿using System;
using System.Dynamic;

namespace ArtisanManage.Models
{
    public class ItemWorldModel
    {

        public string id
        {
            get; set;
        }
        public string sbarcode
        {
            get;set;
        }
        public string mbarcode
        {
            get;set;
        }
        public string bbarcode
        {
            get;set;
        }
        public string sunit
        {
            get;set;
        }
        public string bunit
        {
            get;set;
        }
        public string munit
        {
            get;set;
        }
        public string bfactor
        {
            get;set;
        }
        public string mfactor
        {
            get;set;
        }
        public string sfactor
        {
            get;set;
        }
        public string item_name
        {
            get;set;
        }
        public string item_images
        {
            get;set;
        }
        public string item_videos
        {
            get; set;
        }
        public string item_desc
        {
            get; set;
        }
        public string update_time
        {
            get;set;
        }="2000-01-01";
        public string image_judge
        {
            get;set;
        }

 
    }
}
