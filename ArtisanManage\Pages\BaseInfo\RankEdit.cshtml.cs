using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;

namespace ArtisanManage.Pages.BaseInfo 
{
    public class RankEditModel : PageFormModel
    {
        public RankEditModel(CMySbCommand cmd,string company_id="",string oper_id="") : base(Services.MenuId.infoClient)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                { "rank_id",new DataItem(){Title="等级ID",CtrlType="hidden",FldArea="divHead"}},
                { "rank_name",new DataItem(){Title="名称",Necessary=true,FldArea="divHead"}},
                {"rank_note",new DataItem(){Title="描述",FldArea="divHead"}},
               // {"company_id",new DataItem(){FldArea="divHead", Title="公司id",LabelFld = "123", LabelInDB = false, Source = "[{v:1, l:'欠款'}, {v:2, l:'现结'}]",Value="pay", Label="", ButtonUsage = "list", DropDownHeight = "200", DropDownWidth = "150"}},
            };

            m_idFld = "rank_id"; IdFldIsSerial = true;
            m_tableName = "info_supcust_rank";
            m_selectFromSQL = "from info_supcust_rank where rank_id='~ID'";
        }

        public async Task OnGet()
        {   
            await InitGet(cmd);   
        } 
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class RankEditController : BaseController
    {
        public RankEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues)
        {
            RankEditModel model = new RankEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            RankEditModel model = new RankEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            RankEditModel model = new RankEditModel(cmd);
            return await model.SaveTable(cmd, request);

        }
    }
}