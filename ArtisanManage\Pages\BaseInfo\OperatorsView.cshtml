@page
@model ArtisanManage.Pages.BaseInfo.OperatorsViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <script src="~/js/Vue.js"></script>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">

        var frame = "OperatorEdit";
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());

        var RowIndex = -1;
        var m_db_id = "10";

        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == frame) {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData() 
                    }
                    else
                    {
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = rs.data.record;
                        rows[0].i = rows[0].oper_id;
                        rows[0].status = rs.data.record.cls_status_name;
                        rows[0].is_seller = rs.data.record.is_seller == "True" ? "是" : "否";
                        rows[0].is_sender = rs.data.record.is_sender == "True" ? "是" : "否";

                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "oper_name",rs.data.record.oper_name);
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "mobile", rs.data.record.mobile);
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "is_seller", rs.data.record.is_seller_state);
                    //$('#gridItems').jqxGrid('setcellvalue', RowIndex, "status", rs.data.record.cls_status_name);

                    QueryData();

                }
                $("#popItem").jqxWindow('close');
            }
            else if (rs.data.msgHead == "DepartEdit") {
                var newID = "";  var newName = "";
                if (rs.data.record) { newID = rs.data.record.depart_id; newName = rs.data.record.depart_name; }
                if (rs.data.action == "add") {
                    var sltItem = $('#depart_path').jqxTree('findItem', rs.data.record.mother_id);
                    $('#depart_path').jqxTree('addTo', { value: newID, label: newName }, sltItem.element, false);
                    $('#depart_path').jqxTree('render');   // update the tree.
                }
                else if (rs.data.action == "update") {
                    var sltItem = $('#depart_path').jqxTree('findItem', rs.data.record.depart_id);
                    $('#depart_path').jqxTree('updateItem', sltItem, { label: newName });
                    $('#depart_path').jqxTree('render');
                }
                $("#popClass").jqxWindow('close');
            }
            console.log(rs.data);
        });

    	    var newCount = 1;
    	    function btnAddClass_click(e)
            {
    	        var selectedItem = $('#depart_path').jqxTree('selectedItem');
                
    	        if (!selectedItem) {
    	            bw.toast("请先选择一个部门");
    	            return;
    	        }
                console.log(selectedItem.element)
                $('#popClass').jqxWindow('open');
                $("#popClass").jqxWindow('setContent', `<iframe src="DepartEdit?operKey=${g_operKey}&mother_id=${selectedItem.value}&mother_name=${selectedItem.label}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

    	    };

    	    function btnEditClass_click() {
    	        var selectedItem = $('#depart_path').jqxTree('selectedItem');
    	        if (!selectedItem) {
    	            bw.toast("请先选择一个部门");
    	            return;
    	        }
    	        $('#popClass').jqxWindow('open');
                $("#popClass").jqxWindow('setContent', `<iframe src="DepartEdit?operKey=${g_operKey}&depart_id=${selectedItem.value}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
    	    };
    	    function btnRemoveClass_click(e) {
    	        var selectedItem = $('#depart_path').jqxTree('selectedItem');
    	        if (!selectedItem) {
    	            bw.toast("请先选择一个部门");
    	            return;
                }
                jConfirm(`确定要删除${selectedItem.label}吗？`, function () {
		            $.ajax({
    	                type: "POST",
    	                url: "../api/OperatorsView/RemoveClass",
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify({ operKey: g_operKey, depart_id: selectedItem.value }),
    	                success: function (data) {
                            if (data.result == "OK") {
                                var sltItem = $('#depart_path').jqxTree('findItem', data.depart_id);
                                $('#depart_path').jqxTree('removeItem', sltItem, false);
                                $('#depart_path').jqxTree('render');
    	                    }
    	                    else {
                                bw.toast(data.result);
    	                    }
    	                }
    	            });
                 }, " ");


    	    };

        function btnAddItem_click(e) {
             debugger
            var selectedItem = $('#depart_path').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个部门");
                return;
            }


            var path = $('#depart_path').jqxTree('getTreePath', selectedItem);
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="${frame}?operKey=${g_operKey}&depart_id=${selectedItem.value}&depart_name=${selectedItem.label}&depart_path=${path}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);


            /*
            $.ajaxSetup({ contentType: "application/json" });
            $.post('../api/OperatorsView/RestrictEmployee', JSON.stringify({
                operKey: 'Model.OperKey'
            })).then(function (data) {
                if (data.result == 'OK') {
                    var path = $('#depart_path').jqxTree('getTreePath', selectedItem);
                    $('#popItem').jqxWindow('open');
                    $("#popItem").jqxWindow('setContent', `<iframe src="${frame}?operKey=${g_operKey}&depart_id=${selectedItem.value}&depart_name=${selectedItem.label}&depart_path=${path}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                   
                } else {
                    bw.toast(data.msg);
                }
            })*/

                
        }

        function attachContextMenu() {
                  function isRightClick(event) {
                        var rightclick;
                        if (!event) var event = window.event;
                        if (event.which) rightclick = (event.which == 3);
                        else if (event.button) rightclick = (event.button == 2);
                        return rightclick;
                  }
                    // open the context menu when the user presses the mouse right button.
                    //$("#depart_path li").on('mousedown', function (event) {
                    //    var target = $(event.target).parents('li:first')[0];
                    //    var rightClick = isRightClick(event);
                    //    if (rightClick && target != null) {
                    //        $("#depart_path").jqxTree('selectItem', target);
                    //        var scrollTop = $(window).scrollTop();
                    //        var scrollLeft = $(window).scrollLeft();
                    //        contextMenu.jqxMenu('open', parseInt(event.clientX) + 5 + scrollLeft, parseInt(event.clientY) + 5 + scrollTop);
                    //        return false;
                    //    }
                    //});

                $("#depart_path").on('mousedown', function (event) {
                    if (event.target.tagName == "DIV" && event.target.parentNode.tagName == "LI") {
                        var target = event.target.parentNode;

                        var rightClick = isRightClick(event);
                        var contextMenu = event.target.innerText == "全部" ? contextMenu0 : contextMenu1
                        if (rightClick) {
                            $("#depart_path").jqxTree('selectItem', target);
                            var scrollTop = $(window).scrollTop();
                            var scrollLeft = $(window).scrollLeft();
                            contextMenu.jqxMenu('open', parseInt(event.clientX) + 5 + scrollLeft, parseInt(event.clientY) + 5 + scrollTop);
                            return false;
                        }

                    }

                });



        }

        function onGridRowEdit(rowIndex) {
            var oper_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'i');
              let windowHeight = document.body.offsetHeight-50
                let windowWidth = document.body.offsetWidth-80
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="OperatorEdit?operKey=' + g_operKey + '&oper_id=' + oper_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
            //下面的函数用来配置弹出层的css等相关配置
            
        }
    	    var itemSource = {};
        $(document).ready(function () {
            window.contextMenu0 = $("#jqxMenu_0").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            window.contextMenu1 = $("#jqxMenu_1").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });

                @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)


            $("#btnAddClass").bind("click", { isParent: false }, btnAddClass_click);
            $("#btnEditClass").bind("click", btnEditClass_click);
            $("#btnRemoveClass").bind("click", btnRemoveClass_click);
    	    $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);


            $("#gridItems").on("cellclick", function (event) {
                /*// event arguments.
                var args = event.args;
                if (args.datafield == "oper_name") {
                    var id = args.row.bounddata.oper_id;
                    RowIndex = args.rowindex;

                    $('#popItem').jqxWindow('open');
                    $("#popItem").jqxWindow('setContent', `<iframe src="${frame}?operKey=${g_operKey}&oper_id=${id}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                }*/

                // event arguments.
                var args = event.args;
                console.log(args.row);

                if (args.datafield == "oper_name") {
                    if (args.originalEvent.button == 2) return;
                    var oper_id = args.row.bounddata.i;
                    RowIndex = args.rowindex;
                    if (ForSelect) {
                        var oper_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                        var oper_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "oper_name");
                        var msg = {
                            msgHead: 'OperatorsView', action: 'select', oper_id: oper_id, oper_name: oper_name
                        };
                        window.parent.postMessage(msg, '*');
                    }
                    else {
                        onGridRowEdit(args.rowindex);
                        //$('#popItem').jqxWindow('open');
                        // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                    }
                } else if (args.datafield === "mini_bind_status") {
                    let mini_bind_status = args.row.bounddata.mini_bind_status;
                    // let mini_oper_id = args.row.bounddata.mini_oper_id;
                    if (mini_bind_status === '绑定') {
                        handleBindOperInfoToMini(args.row.bounddata)
                    } else {
                        handleUnBindOperInfoToMini(args.row.bounddata)
                    }
                    
                    
                }
            });
            
            function handleBindOperInfoToMini(operInfoRow) {
                let oper_id = operInfoRow.i;
                let oper_name = operInfoRow.oper_name;
                bw.toast("获取二维码中...")
                QueryMallSettingInfo().then(res => {
                    if (res.code === 0) {
                        const mallSettingInfo = res.result
                        // 简易化处理
                        let errMsg = handelSettingMiniConfigInfo(mallSettingInfo)
                        if (errMsg) {
                          bw.toast(errMsg)
                          return
                        }
                        CreateMiniQrCodeBase64(mallSettingInfo,oper_id).then((res) => {
                            if( res.code !== 0) {
                                 bw.toast(res.message)
                                 return
                            }
                            const miniQrUrl  = "data:image/png;base64," + res.result
                             $('#popItem').jqxWindow('open');
                             $("#popItem").jqxWindow('setContent', `
<div class="mini-code-wrapper">
    <div class="mini-code-name">【${oper_name}】扫码</div>
    <div class="mini-code-img">
        <img src="${miniQrUrl}"/>
    </div>
</div>
                             
                             
                             
                             `);  
                        }).catch(() => {
                            bw.toast("获取失败")
                        })
                    }
                })
            }
            function handleUnBindOperInfoToMini(operInfoRow) {
                console.log(operInfoRow)
                let oper_name = operInfoRow.oper_name
                let contact_id = operInfoRow.contact_id
                let mini_oper_id = operInfoRow.mini_oper_id
                 jConfirm(`确定要解绑${oper_name}吗？`, function () {
                    $.ajax({
                        type: "POST",
                        url: "/MallApi/MallUser/UnBindMallOperInfo",
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify({ operKey: g_operKey, contact_id: contact_id,  mini_oper_id:mini_oper_id }),
                        success: function (res) {
                            if (res.code !== 0) {
                               bw.toast(res.message)
                               return
                            }
                            bw.toast("解绑成功")
                            QueryData()
                        }
                    });
                 }, " ");
            }
            function QueryMallSettingInfo() {
              return  $.ajax({
                    url: '/MallApi/MallSetting/QueryMallSettingInfo',
                    type: 'POST',
                    data: JSON.stringify({operKey: g_operKey}),
                    contentType: "application/json;charset=UTF-8",
                    dataType: 'json'
                })
            }
            function handelSettingMiniConfigInfo(mallSettingInfo) {
              mallSettingInfo.qr_code_enable_expired = mallSettingInfo.qr_code_enable_expired === 'True'
              mallSettingInfo.mini_app_private = mallSettingInfo.mini_app_private === 'True'
              // 在进行校验
              let yingjiangMiniAppId = "wx7b10b3a9f0a20b6b"
              mallSettingInfo.mini_app_private = mallSettingInfo.mini_app_id !== '' && mallSettingInfo.mini_app_id !== yingjiangMiniAppId;
              if (mallSettingInfo.mall_type === '') {  // 未开通小程序
                 return '当前未开通小程序'
              }
              if (mallSettingInfo.default_branch_id === '') {
                return '未设置公司仓库'
              }
              return ''    
            }
            function CreateMiniQrCodeBase64(mallSettingInfo, oper_id) {
                const dataParams = {
                  "qrCodeExpiresTime": mallSettingInfo.qr_code_enable_expired ? Number(mallSettingInfo.qr_code_alive_time) : 0,
                  "operKey": g_operKey,
                  "supcustId": -1,
                  "miniOperId": oper_id,
                  "otherInfo": {},
                  "miniAppPrivate": mallSettingInfo.mini_app_private,
                  "miniAppId": mallSettingInfo.mini_app_id
                }
                return $.ajax({
                    url: '/MallApi/MallQrCode/CreateMiniQrCodeBase64',
                    type: 'POST',
                    data: JSON.stringify(dataParams),
                    contentType: "application/json;charset=UTF-8",
                    dataType: 'json'
                })
            }


    	    $("#Cancel").on('click', function () {
    	        for (var i = 0; i < 10; i++) {
    	            $('#jqxgrid').jqxGrid('deleterow', i);
    	            $('#jqxgrid').jqxGrid('addrow', i, {})
    	        }
    	    });
            let itemMaxHeight = $("#popItem").parent().height()
            let itemMaxWidth = $("#popItem").parent().width()
            let classMaxHeight = $("#popClass").parent().height()
            let classMaxWidth = $("#popClass").parent().width()
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: itemMaxHeight - 50, maxWidth: itemMaxWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
           // $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 548, width: 1000, maxHeight:windowHeight,maxWidth:windowWidth,theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            $("#popResetPassword").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 220, width: 360, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade'});
            $("#popClass").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 400, width: 600, maxHeight: classMaxHeight - 50, maxWidth: classMaxWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });


                attachContextMenu();

                $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                     return false;
                });

                QueryData();
    	    });


        function onGridRowContextMenuClick(gridID, menuID, rowIndex) {
            
            var operID = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'i');
            //var row = rows[rowIndex]
            if (menuID == 'resetPassword') {
              //  console.log(row)
                window.operIdForResetPwd = operID
                $('#popResetPassword').jqxWindow('open')
            }
        }
        function resetPassword() {
            
            var pwd = $('#new_password').val();
            var confPwd=$('#confirm_password').val()
            if (pwd.trim().length < 1) {
                bw.toast('请输入密码');
                return
            }
            if (pwd != confPwd) {
                bw.toast('两次输入的新密码不一致');
                return
            }
            $.ajaxSetup({ contentType: "application/json" });
            $.post('../api/OperatorsView/ResetPassword', JSON.stringify({
                operKey: '@Model.OperKey',
                operID: window.operIdForResetPwd,
                password: pwd
            })).then(function (data) {
                if (data.result == 'OK') {
                    bw.toast('重置完成');
                    $('#popResetPassword').jqxWindow('close')
                    $('#new_password').val("")
                    $('#confirm_password').val("")
                } else {
                    bw.toast(data.msg);
                }
            });
        }
        function btnClose_Clicked() {
          
            $('#new_password').val("")
            $('#confirm_password').val("")
            $('#popResetPassword').jqxWindow('close');
        }
    </script>


</head>
 
<body>
    <style>
        /*设置弹出框的padding*/
        .jqx-window-content {
            padding: 0px;
        }
        /*设置弹出框的标题栏横线颜色*/
        .jqx-widget-header {
            border-color: #eeeeee;
        }

        .margin {
            margin-left: 20px;
        }

        .label_name {
            line-height: 32px;
            margin-left: 10px;
        }

        .label_content {
            width: 120px;
            height: 30px;
            margin-left: 10px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
        
        .mini-code-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        flex-direction: column;
        justify-content: center;
        }
        .mini-code-name {
        width: 100%;
        text-align: center;
        font-size: 30px;
        }
        .mini-code-img {
        margin-top: 10px;
        display: flex;
        justify-content: center;
        }
        
        
    </style>
    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;">
        <div style="display:inherit">
           
            <div style="display: flex;"><label class="label_name">状态</label> <div id="status" class="label_content"></div></div>
            <input id="searchString" class="margin" type="text" autoComplete="off" placeholder="请输入简拼/名称/手机号"  />
           
            <button onclick="QueryData()" class="margin">查询</button>
        </div>
        <div><button onclick="btnAddItem_click()" class="margin">新增员工</button></div>
    </div>
    <div style="height:100%;display:flex;width:100%;">

        <div id='depart_path' style="width:200px;height:calc(100% - 40px);margin-top:20px;margin-bottom:2px;overflow-y:scroll">
        </div>

        <div style="width:calc(100% - 200px);height:calc(100% - 0px); margin-left:10px;">

            <div><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>

            <div id="gridItems" style="margin-top: 0px; margin-left: 10px; margin-bottom: 2px; width: calc(100% - 20px); height: calc(100% - 10px);"></div>

        </div>

    </div>

    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;">
            <span style="font-size:20px;">员工档案</span>
        </div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popClass" style="">
        <div style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">部门组织</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popResetPassword" style="display:none;">
        <div id="passwordCaption" style="height:20px;background-color:#fff; text-align:center;">
            <span style="font-size:15px;">重置密码</span>
        </div>
        <div style="overflow:hidden;align-content:center;align-items:center;justify-content:center;margin-top:8px;margin-left:50px;">
            密码：<input type="text" onfocus="this.type='password'" id="new_password" autoComplete="off" placeholder="请输入新密码" style="border-left: none; border-right: none; border-top: none; margin-top: 10px;margin-left:28px;" />
          
            <br />
            确认密码：<input type="text" onfocus="this.type='password'"  id="confirm_password" autoComplete="off" placeholder="确认密码" style="border-left:none;border-right:none;border-top:none;margin-top:15px;" />
           
            <br />
            <button onclick="resetPassword()" style="align-content:center;margin-top:45px;margin-left:20px;">确定</button>
            <button onclick="btnClose_Clicked();" style="align-content:center;margin-top:45px;margin-left:75px">关闭</button>
        </div>
    </div>
    <div id='jqxMenu_1'>
        <ul>
            <li id="mnuEditClass" onclick="btnEditClass_click()">编辑部门</li>
            <li id="mnuAddClass" onclick="btnAddClass_click()">添加下级部门</li>
            <li id="mnuRemoveClass" onclick="btnRemoveClass_click()">删除部门</li>
        </ul>
    </div>
    <div id='jqxMenu_0'>
        <ul>
            <li id="mnuAddClass" onclick="btnAddClass_click()">添加下级部门</li>
        </ul>
    </div>
    



</body>
</html>