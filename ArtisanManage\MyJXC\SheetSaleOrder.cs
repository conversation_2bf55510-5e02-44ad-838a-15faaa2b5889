﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using HuaweiCloud.SDK.Core;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using ArtisanManage.WebAPI;
using ArtisanManage.YingJiangCommon.Controller.PromotionController;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.ComponentModel.Design;
using NPOI.Util.Collections;
using System.Reflection;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using static System.Runtime.InteropServices.JavaScript.JSType;
using ArtisanManage.YingjiangMessage.Services;
using ArtisanManage.YingjiangMessage.Pojo;
using ArtisanManage.YingJiangMallMini.Services;
using ArtisanManage.YingJiangMallMini.Model;
using ArtisanManage.MyCW;

namespace ArtisanManage.MyJXC
{
    public class SheetRowSaleOrder : SheetRowMM
    {
        [SaveToDB][FromFld] public string recent_retail_price { get; set; }
        [SaveToDB][FromFld] public string last_time_price { get; set; }

        [FromFld("sup.sup_name", LOAD_PURPOSE.SHOW)] public string supplier_name { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string manufactor_name { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string manufactor_addr { get; set; }


        [FromFld(LOAD_PURPOSE.SHOW)] public string item_provenance { get; set; }
        private string _attr_qty = "";
        [SaveToDB][FromFld] public string sn_code { get; set; }
       [FromFld("case when t.unit_factor::text=m_unit_factor then m_wholesale_price when t.unit_factor::text=b_unit_factor then b_wholesale_price else  s_wholesale_price end")] public string wholesale_price { get; set; }

        [SaveToDB]
        [FromFld]
        public override string attr_qty
        {
            get
            {
                return _attr_qty;
            }
            set
            {
                if (value == "[]") value = "";
                _attr_qty = value;
            }
        }

        [FromFld(LOAD_PURPOSE.SHOW)]
        public override string s_barcode
        {
            get
            {
                string origbarcode = base.s_barcode;
                string code = "";
                string attrOptNames = "";
                if (attr_qty != "" && attr_qty != "[]" && !string.IsNullOrEmpty(mum_attributes))
                {
                    if (origbarcode != "" && int.TryParse(origbarcode, out int i))
                    {
                        origbarcode = $"主商品({origbarcode})";
                    }
                    dynamic attrQty = JsonConvert.DeserializeObject(attr_qty);
                    foreach (dynamic arr in ((JArray)attrQty))
                    {
                        foreach (dynamic kvp in arr)
                        {
                            if (kvp.Name.StartsWith("optName_") && arr.qty != 0)
                            {
                                code = arr.sBarcode;
                                if (!string.IsNullOrEmpty(code))
                                    attrOptNames = $"{kvp.Value.Value}({code})";
                            }
                        }
                        origbarcode += attrOptNames;
                    }

                }
                return origbarcode;
            }
            set
            {
                base.s_barcode = value;
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)]
        public override string m_barcode
        {
            get
            {
                string origbarcode = base.m_barcode;
                string code = "";
                string attrOptNames = "";
                if (attr_qty != "" && attr_qty != "[]" && !string.IsNullOrEmpty(mum_attributes))
                {
                    if (origbarcode != "" && int.TryParse(origbarcode, out int i))
                    {
                        origbarcode = $"主商品({origbarcode})";
                    }
                    dynamic attrQty = JsonConvert.DeserializeObject(attr_qty);
                    foreach (dynamic arr in ((JArray)attrQty))
                    {
                        foreach (dynamic kvp in arr)
                        {
                            if (kvp.Name.StartsWith("optName_") && arr.qty != 0)
                            {
                                code = arr.mBarcode;
                                if (!string.IsNullOrEmpty(code))
                                    attrOptNames = $"{kvp.Value.Value}({code})";
                            }
                        }
                        origbarcode += attrOptNames;
                    }

                }
                return origbarcode;
            }
            set
            {
                base.m_barcode = value;
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)]
        public override string b_barcode
        {
            get
            {
                string origbarcode = base.b_barcode;
                string code = "";
                string attrOptNames = "";
                if (attr_qty != "" && attr_qty != "[]" && !string.IsNullOrEmpty(mum_attributes))
                {
                    if (origbarcode != "" && int.TryParse(origbarcode, out int i))
                    {
                        origbarcode = $"主商品({origbarcode})";
                    }
                    dynamic attrQty = JsonConvert.DeserializeObject(attr_qty);
                    foreach (dynamic arr in ((JArray)attrQty))
                    {
                        foreach (dynamic kvp in arr)
                        {
                            if (kvp.Name.StartsWith("optName_") && arr.qty != 0)
                            {
                                code = arr.bBarcode;
                                if (!string.IsNullOrEmpty(code))
                                    attrOptNames = $"{kvp.Value.Value}({code})";
                            }
                        }
                        origbarcode += attrOptNames;
                    }

                }
                return origbarcode;
            }
            set
            {
                base.b_barcode = value;
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public override string sale_print_combine_attr { get; set; } = "";//合并打印多口味


        internal JArray _mum_attributes = null;
        [FromFld("case when son_mum_attributes is not null then son_mum_attributes else mum_attributes end", LOAD_PURPOSE.SHOW)]
        public string mum_attributes
        {
            get
            {
                if (_mum_attributes == null) return "";
                if (son_mum_item != "")
                {
                    for (int i = _mum_attributes.Count - 1; i >= 0; i--)
                    {
                        JObject a = (JObject)_mum_attributes[i];
                        JValue s = (JValue)a.GetValue("distinctStock");
                        if (s != null && s.ToString().ToLower() == "true")
                        {
                            _mum_attributes.RemoveAt(i);
                        }
                    }
                }
                if (_mum_attributes.Count == 0) return "";
                return JsonConvert.SerializeObject(_mum_attributes);
            }

            set
            {
                if (value == null) _mum_attributes = null;
                else
                {
                    _mum_attributes = JsonConvert.DeserializeObject<JArray>(value);
                }
            }
        }

        internal JArray _avail_attr_combine = null;
        [FromFld(LOAD_PURPOSE.SHOW)]
        public string avail_attr_combine //前端用来在quantity单元格的render函数cellsrenderer_quantity判断是否需要展示属性按钮
        {
            get
            {
                if (_avail_attr_combine == null) return ""; 
                if (_avail_attr_combine.Count == 0) return "";
                return JsonConvert.SerializeObject(_avail_attr_combine);
            }

            set
            {
                if (value == null) _avail_attr_combine = null;
                else
                {
                    _avail_attr_combine = JsonConvert.DeserializeObject<JArray>(value);
                }
            }
        }
        
        public float cost_price_recent1;
        public float cost_price_recent2;
        public float cost_price_recent3;
        [SaveToDB] [FromFld("t.cost_price_buy", LOAD_PURPOSE.SHOW)] public string cost_price_buy { get; set; }
        [SaveToDB] [FromFld("t.cost_price_prop", LOAD_PURPOSE.SHOW)] public string cost_price_prop { get; set; }
        [SaveToDB][FromFld("t.cost_price_recent", LOAD_PURPOSE.SHOW)] public string cost_price_recent { get; set; }
        [FromFld("setting->>'costPriceType'",LOAD_PURPOSE.SHOW)] public string cost_price_type { get; set; }
        [FromFld("setting->>'recentPriceTime'", LOAD_PURPOSE.SHOW)] public string recent_price_time { get; set; }
        public decimal cost_price
        {
            get
            {
                decimal n = 0;
                if (cost_price_type == "2")
                {
                    if (cost_price_avg.IsValid())
                        n = CPubVars.ToDecimal(cost_price_avg) * unit_factor;
                }
                else if (cost_price_type == "3")
                {
                    if(cost_price_buy.IsValid())
                        n =CPubVars.ToDecimal(cost_price_buy) * unit_factor;
                }
                else if (cost_price_type == "1")
                {
                    if (cost_price_prop.IsValid())
                        n = CPubVars.ToDecimal(cost_price_prop) * unit_factor;
                }

                else if (cost_price_type == "4")
                {
                    if (recent_price_time == "1")
                    {
                        n = CPubVars.ToDecimal(cost_price_recent1) * unit_factor;
                    }
                    else if (recent_price_time == "2")
                    {
                        n = CPubVars.ToDecimal(cost_price_recent2) * unit_factor;
                    }
                    else if (recent_price_time == "3")
                    {
                        n = CPubVars.ToDecimal(cost_price_recent3) * unit_factor;
                    }
                }
                n = Math.Round(n, 4);
                //n = Convert.ToSingle(CPubVars.FormatMoney(n, 4));
                return n;
            }
        }
        

        public decimal money_amount
        {
            get
            {
                decimal amt = sub_amount;
                if (this.order_sub_id.IsValid()) amt = 0m;
                return amt;
            }
        }
        public decimal cost_amount
        {
            get
            {
                decimal n = cost_price * quantity;
                n = Math.Round(n, 2);
                // n = Convert.ToSingle(CPubVars.FormatMoney(n, 2));
                return n;
            }
        }
        public decimal profit
        {
            get
            {
                return Math.Round(sub_amount - cost_amount, 2);
            }
        }
        public string profit_rate
        {
            get
            {
                string s = "";
                if (sub_amount != 0)
                {
                    s = CPubVars.FormatMoney(profit / sub_amount * 100, 1);
                }
                return s;
            }
        }

        public bool isSpecialPrice { get; set; } = false;
        public string special_price { get; set; } = "";
        public string scanBarcode {  get; set; } = "";
        public string order_sub_id { get; set; } = "";
        public string order_price
        {
            get
            {
                if (order_sub_id.IsValid())
                {
                    return real_price.ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        [FromFld("cw.sub_name")] public string order_sub_name { get; set; }
        public string order_item_sheets_no { get; set; } = "";
        public string order_item_sheets_id { get; set; } = "";
        public string promotion_id { get; set; }
        public string promotion_type { get; set; }
        /// <summary>
        /// 是否为会员积分兑换商品（true代表是，其他代表不是）
        /// </summary>
        public string vip_is_redeem { get; set; } = "";
        /// <summary>
        /// 该行商品消耗了多少会员积分（纯数字，哪张卡用了多少分后续后端计算）
        /// </summary>
        public string vip_used_point { get; set; } = "";
        /// <summary>
        /// 只在该行商品是用积分+钱兑换时记录。传字符串格式的单价
        /// 有这一字段时，不允许用户在转单时修改单价
        /// </summary>
        public string vip_redeem_price { get; set; } = "";
        /// <summary>
        /// 只在该行商品与积分兑换有关时记录。传字符串格式的商品单位(具体的，和sheetRow.unit_no一致)
        /// 有这一字段时，不允许用户在转单时修改单位
        /// </summary>
        public string vip_redeem_unit { get; set; } = "";
        /// <summary>
        /// 只在该行商品与积分兑换有关时记录。传字符串格式的商品数量(小程序传1即可)
        /// 有这一字段时，不允许用户在转单时修改数量
        /// </summary>
        public string vip_redeem_amount { get; set; } = "";
        /// <summary>
        /// 兑换详情（一个JSON，格式待定）
        /// </summary>
        public string vip_redeem_description { get; set; } = "";
        public string sheet_attr_fulldiscs { get; set; }
        public string sheet_attr_redpacket { get; set; }
        public string order_flow_id { get; set; } = "";

        /// <summary>
        /// 这个变量用于标识此行商品是否与其他行的商品相关联
        /// 如果试图删除这个商品，那么和它关联(deleteLinkId相同)的商品行也应被同时删除
        /// 这个逻辑目前只在APP生效，用于促销活动的组合/兑奖
        /// </summary>
        public string deleteLinkId = "";


        [SaveToDB]  [FromFld]
        public string other_info
        {
            get
            {
                IDictionary<string, object> d = new ExpandoObject();
                if (scanBarcode != "") d["scanBarcode"] = scanBarcode;
                if (disp_flow_id != "") d["dispFlowID"] = disp_flow_id;
                if (disp_month_id != "") d["dispMonthID"] = disp_month_id;
                if (disp_sheet_id != "") d["dispSheetID"] = disp_sheet_id;
                if (order_sub_id != "") d["orderSubID"] = order_sub_id;
                if (order_item_sheets_id != "") d["orderItemSheetsID"] = order_item_sheets_id;
                if (order_item_sheets_no != "") d["orderItemSheetsNO"] = order_item_sheets_no;
                if (disp_template_id != "") d["dispTmpID"] = disp_template_id;
                if (isSpecialPrice ) d["isSpecialPrice"] = isSpecialPrice;
                if (special_price != "") d["specialPrice"] = special_price;
                if (promotion_id != "") d["promotion_id"] = promotion_id;
                if (promotion_type != "") d["promotion_type"] = promotion_type;
                if (vip_is_redeem != "") d["vip_is_redeem"] = vip_is_redeem;
                if (vip_used_point != "") d["vip_used_point"] = vip_used_point;
                if (vip_redeem_price != "") d["vip_redeem_price"] = vip_redeem_price;
                if (vip_redeem_unit != "") d["vip_redeem_unit"] = vip_redeem_unit;
                if (vip_redeem_amount != "") d["vip_redeem_amount"] = vip_redeem_amount;
                if (vip_redeem_description != "") d["vip_redeem_description"] = vip_redeem_description;
                if (sheet_attr_fulldiscs != "") d["sheet_attr_fulldiscs"] = sheet_attr_fulldiscs;
                if (sheet_attr_redpacket != "") d["sheet_attr_redpacket"] = sheet_attr_redpacket;
                if (order_flow_id != "") d["order_flow_id"] = order_flow_id;
                if (deleteLinkId != "") d["deleteLinkId"] = deleteLinkId;
                if (d.Count > 0)
                {
                    return JsonConvert.SerializeObject(d);
                }
                return "";
            }
            set
            {
                if (value.IsValid())
                {
                    dynamic d = JsonConvert.DeserializeObject(value);
                    if (d.scanBarcode != null) scanBarcode = d.scanBarcode;
                    if (d.dispFlowID != null) disp_flow_id = d.dispFlowID;
                    if (d.dispMonthID != null) disp_month_id = d.dispMonthID;
                    if (d.dispSheetID != null) disp_sheet_id = d.dispSheetID;
                    if (d.orderSubID != null) order_sub_id = d.orderSubID;
                    if (d.orderItemSheetsID != null) order_item_sheets_id = d.orderItemSheetsID;
                    if (d.orderItemSheetsNO != null) order_item_sheets_no = d.orderItemSheetsNO;
                    if (d.dispTmpID != null) disp_template_id = d.dispTmpID;
                    if (d.isSpecialPrice != null) isSpecialPrice = d.isSpecialPrice;
                    if (d.specialPrice != null) special_price = d.specialPrice;
                    if (d.promotion_id != null) promotion_id = d.promotion_id;
                    if (d.promotion_type != null) promotion_type = d.promotion_type;
                    if (d.vip_is_redeem != null) vip_is_redeem = d.vip_is_redeem;
                    if (d.vip_used_point != null) vip_used_point = d.vip_used_point;
                    if (d.vip_redeem_price != null) vip_redeem_price = d.vip_redeem_price;
                    if (d.vip_redeem_unit != null) vip_redeem_unit = d.vip_redeem_unit;
                    if (d.vip_redeem_amount != null) vip_redeem_amount = d.vip_redeem_amount;
                    if (d.vip_redeem_description != null) vip_redeem_description = d.vip_redeem_description;
                    if (d.sheet_attr_fulldiscs != null) sheet_attr_fulldiscs = d.sheet_attr_fulldiscs;
                    if (d.sheet_attr_redpacket != null) sheet_attr_redpacket = d.sheet_attr_redpacket;
                    if (d.order_flow_id != null) order_flow_id = d.order_flow_id;
                    if (d.deleteLinkId != null) deleteLinkId = d.deleteLinkId;
                }
            }
        }

        [SaveToDB] [FromFld] public override string trade_type { get; set; } = "";
        public string trade_type_name
        {
            get
            {
                switch (trade_type)
                {
                    case "J": return "借货";
                    case "H": return "还货";
                    case "DH": return "定货";
                    case "CL": return "陈列";
                    case "KS": return "客损";
                }
                if (this.inout_flag == 1)
                {
                    return "退货";
                }
                else
                {
                    if (quantity < 0) return "退货";
                    return "销售";
                }

            }
        }
        [SaveToDB] [FromFld] public string disp_sheet_id { get; set; } = ""; //使用的陈列协议单号
        [SaveToDB] [FromFld] public string disp_flow_id { get; set; } = "";
        [SaveToDB] [FromFld] public string disp_month_id { get; set; } = ""; //使用的陈列协议 对应数据库month1、month2
        public string disp_template_id { get; set; } = ""; // 陈列协议模板

        public string move_stock { get; set; } = "";
        internal decimal disp_month_left { get; set; } = 0;
        public bool isPromotionItem { get { return promotion_type.IsValid() && promotion_type != "unknown"; } }
        [FromFld("m.sheet_no")] public string sheet_no { get; set; }
    }

    public class SheetSaleOrder : SheetMM<SheetRowSaleOrder>
    {
        //[SaveToDB] [FromFld] public string appendix_photos { get; set; } = "[]";
        //[SaveToDB] [FromFld] 
        public string[] appendixPhotos { get; set; }
        public dynamic[] displayGiveProofs { get; set; }

        public decimal? redpacket_use_amount { get; set; }
        public decimal? redpacket_earn { get; set; }

        [SaveToDB] [FromFld] public string shop_id { get; set; } = "";
        [SaveToDB] [FromFld] public string receive_addr { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string receive_addr_desc { get; set; } = "";
        [FromFld("addr.receiver_name")] public string receiver_name { get; set; } = "";
        [FromFld("addr.receiver_mobile")] public string receiver_mobile { get; set; } = "";
        public string receive_addr_show
        {
            get
            {
                return receive_addr_desc.IsValid() ? receive_addr_desc : sup_addr;
            }
        }
        [SaveToDB] [FromFld] public string visit_id { get; set; } = ""; 
        [SaveToDB] [FromFld] public bool is_retail { get; set; }
        [SaveToDB][FromFld] public string order_source { get; set; } = ""; // 单据来源
        [SaveToDB] [FromFld] public string sheet_usage { get; set; } = "";
        [SaveToDB] [FromFld] public string review_time { get; set; } = "";
        [SaveToDB] [FromFld] public string reviewer_id { get; set; } = "";
        public bool bReview { get; set; } = false;
        
        [FromFld(LOAD_PURPOSE.SHOW)] public string reviewer_name { get; set; } = "";

		[SaveToDB][FromFld] public string work_brief { get; set; } = "";
		[SaveToDB(false)] [FromFld(false)] public override decimal prepay_amount { get; set; }
        private string _senders_id = "";
        [SaveToDB] [FromFld] public string senders_id {
            get
            {
                if (assigned_senders_id != "") return assigned_senders_id;
                else return _senders_id ?? "";
            }
            set
            {
                _senders_id = value;
            }
        }
        private string _senders_name = "";
        [SaveToDB] [FromFld] public string senders_name { 
            get {
                if (assigned_senders_name != "") return assigned_senders_name;
                else return _senders_name ?? "";
            }
            set
            {
                _senders_name = value;
                assigned_senders_name = "";
            }
        }
        [FromFld] public bool is_del { get; set; } = false;//订单是否删除
        [FromFld(LOAD_PURPOSE.SHOW)] public string sender_mobile { get; set; } = "";
        [FromFld("tb_status.van_id")] public string van_id { get; set; } = "";
        [FromFld("van.van_name")] public string van_name { get; set; } = "";
        [FromFld("tb_status.move_stock")] public string move_stock { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string license_no { get; set; } = "";
        [FromFld("tb_status.order_status")] public string order_status { get; set; } = "";
        [FromFld("tb_status.receipt_status")] public string receipt_status { get; set; } = ""; 
        [FromFld("tb_status.sale_sheet_id")] public string sale_sheet_id { get; set; } = "";
        [FromFld("tb_status.assigned_senders_id",LOAD_PURPOSE.SHOW)] public string assigned_senders_id { get; set; } = "";
        [FromFld("tb_status.assigned_senders_name", LOAD_PURPOSE.SHOW)] public string assigned_senders_name { get; set; } = "";
        [FromFld("tb_status.move_sheet_no")] public string move_sheet_no { get; set; } = "";
        [FromFld("tb_status.move_sheet_id")] public string move_sheet_id { get; set; } = "";
        [FromFld("pm.placeholder_sheet_id")] public string placeholder_sheet_id { get; set; } = "";
        [FromFld("pm.placeholder_sheet_no")] public string placeholder_sheet_no { get; set; } = "";
        [SaveToDB]
        public string cost_amount_avg
        {
            get
            {
                decimal cost_amt = 0;
                foreach (var row in this.SheetRows)
                {
                    if (!string.IsNullOrEmpty(row.cost_price_avg))
                        cost_amt += row.quantity * row.unit_factor * CPubVars.ToDecimal(row.cost_price_avg);
                }
                return CPubVars.FormatMoney(cost_amt,2);
            }
        }
        [SaveToDB]
        public string cost_amount_buy
        {
            get
            {
                decimal cost_amt = 0;
                foreach (var row in this.SheetRows)
                {
                    if (!string.IsNullOrEmpty(row.cost_price_buy))
                        cost_amt += row.quantity * row.unit_factor * CPubVars.ToDecimal(row.cost_price_buy);
                }
                return CPubVars.FormatMoney(cost_amt, 2);
            }
        }
        [SaveToDB]
        public string cost_amount_recent
        {
            get
            {
                decimal cost_amt = 0;
                foreach (var row in this.SheetRows)
                {
                    if (!string.IsNullOrEmpty(row.cost_price_recent))
                        cost_amt += row.quantity * row.unit_factor * CPubVars.ToDecimal(row.cost_price_recent);
                }
                return CPubVars.FormatMoney(cost_amt, 2);
            }
        }
        [SaveToDB]
        public string cost_amount_prop
        {
            get
            {
                decimal cost_amt = 0;
                foreach (var row in this.SheetRows)
                {
                    if (!string.IsNullOrEmpty(row.cost_price_prop))
                        cost_amt += row.quantity * row.unit_factor * CPubVars.ToDecimal(row.cost_price_prop);
                }
                return CPubVars.FormatMoney(cost_amt, 2);
            }
        }
        private bool _isOpenPlaceholderOrder = false;
        public bool isOpenPlaceholderOrder
        {
            get {
                if (_isOpenPlaceholderOrder || placeholder_sheet_id.IsValid()) return true;
                else return false;
            }
            set {
                _isOpenPlaceholderOrder = value;
            }
         }

        //public bool hasPlaceholderSheet { get; set; } = false;
        public SheetPlaceholderOrder placeholderOrderSheet { get; set; } = null;
        [SaveToDB] [FromFld("to_char(t.send_time,'yyyy-MM-dd hh24:mi:ss') as send_time")] public string send_time { get; set; } = "";

        /// <summary>
        /// 单据是否与会员积分有关
        /// 仅在以下情况时，设为"true"（字符串）：
        ///    * 单据中含有消耗积分兑换得来的商品
        /// 此时，单据将不允许冲改，复制单据时此字段应当被清除
        /// </summary>
        public string relatedToVipPoint { get; set; } = "";

        /// <summary>
        /// 单据占用的会员积分MAP
        /// 是一个JSON，可以使用Dictionary(int, decimal)解析
        /// key为vip_point_gain_detail的flow_id，value为占用积分量
        /// 复制时此字段应当被清除
        /// </summary>
        public string vipPointPendMap { get; set; } = "";
       
        public string bindSheetInfo { get; set; } = ""; // 分销单据绑定
        // [FromFld("sheet_attribute->>'returnAmt'", LOAD_PURPOSE.SHOW)] public float return_amount { get; set; } = 0;
        [SaveToDB]
        [FromFld]
        public override string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                string baseAttr = base.sheet_attribute;
                if (baseAttr != "") sheetAttribute = JsonConvert.DeserializeObject<Dictionary<string, string>>(baseAttr);
                if (!string.IsNullOrEmpty(bindSheetInfo))
                {
                    sheetAttribute.Add("bindSheetInfo", bindSheetInfo);
                }
                foreach (var row in SheetRows)
                {
                    //陈列协议
                    if (row.disp_flow_id != "" && row.quantity != 0! && !sheetAttribute.ContainsKey("display")) sheetAttribute.Add("display", "true");

                    string tradeType = (row.trade_type).ToLower();
                    if (row.trade_type != "" && row.trade_type != "x" && row.trade_type != "t" && !sheetAttribute.ContainsKey(tradeType)) sheetAttribute.Add(tradeType, "true");

                }
                if (total_weight > 0)
                {
                    sheetAttribute.Add("total_weight", CPubVars.FormatMoney(total_weight));
                }
                if (total_volume > 0)
                {
                    sheetAttribute.Add("total_volume", CPubVars.FormatMoney(total_volume));
                }
                if (!string.IsNullOrEmpty(relatedToVipPoint))
                {
                    sheetAttribute.Add("relatedToVipPoint", relatedToVipPoint);
                }
                if (!string.IsNullOrEmpty(vipPointPendMap))
                {
                    sheetAttribute.Add("vipPointPendMap", vipPointPendMap);
                }
                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.relatedToVipPoint != null)
                    {
                        this.relatedToVipPoint = sheetAttr.relatedToVipPoint;
                    }
                    if (sheetAttr.vipPointPendMap != null)
                    {
                        this.vipPointPendMap = sheetAttr.vipPointPendMap;
                    }
                    if (sheetAttr.bindSheetInfo != null)
                    {
                        this.bindSheetInfo = sheetAttr.bindSheetInfo;
                    }
                }
                base.sheet_attribute = value;

            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public override string print_count { get; set; } = "";

        /// <summary>
        /// 此订单总共消耗的会员积分
        /// </summary>
        public decimal vip_used_point_total
        {
            get
            {
                decimal totalCost = 0;
                foreach (var row in this.SheetRows)
                {
                    var str_cost = row.vip_used_point;
                    if (decimal.TryParse(str_cost, out var cost))
                        totalCost += cost;
                }
                return totalCost;
            }
        }
        /// <summary>
        /// 批发/零售标记，主要用于会员积分项目
        /// 单据是零售单时返回"r", 是批发单据时返回"w"
        /// </summary>
        public string RetailWholeFlag
        {
            get
            {
                return (is_retail || order_source == "cashier") ? "r" : "w";
            }
        }

        public decimal sale_amount
        {
            get { return total_amount + return_amount; }
        }

        #region 支付系统相关
        [SaveToDB][FromFld] public string pay_bill_id { get; set; } = "";

        /// <summary> 关联支付系统订单的订单状态 </summary>
        [FromFld(LOAD_PURPOSE.SHOW)] public string payb_status { get; set; } = "";

        /// <summary> 关联支付系统订单的订单单号 </summary>
        [FromFld(LOAD_PURPOSE.SHOW)] public string payb_trade_no { get; set; } = "";

        public string payb_status_name
        {
            get
            {
                if (this.order_source == "xcx")
                {
                    return payb_status switch
                    {
                        WebAPI.PayBillController.BillStatus.Paid => "在线支付",
                        WebAPI.PayBillController.BillStatus.Unpaid => "未支付",
                        WebAPI.PayBillController.BillStatus.Returned => "已退款",
                        WebAPI.PayBillController.BillStatus.Canceled => "已取消",
                        _ => "货到付款",
                    };
                }
                else return "";
               
            }
        }
        public string order_source_name
        {
            get
            {
                if (order_source == "xcx")
                    return "商城";
                else if (order_source == "cashier")
                    return "收银系统";
                else
                    return "";
            }

        }
        #endregion

        [JsonConstructor]  //if there is more constructor, it is necessary to set on one of them
        public SheetSaleOrder(SHEET_RETURN sheetReturn, LOAD_PURPOSE loadPurpose): base("sheet_sale_order_main", "sheet_sale_order_detail", loadPurpose)
        { 
            sheet_type = sheetReturn == SHEET_RETURN.IS_RETURN? SHEET_TYPE.SHEET_SALE_DD_RETURN : SHEET_TYPE.SHEET_SALE_DD;
            ConstructFun();
        }
        public SheetSaleOrder(): base("sheet_sale_order_main", "sheet_sale_order_detail", LOAD_PURPOSE.SHOW)
        {
            sheet_type = SHEET_TYPE.SHEET_SALE_DD;
            ConstructFun();
        }
        private void ConstructFun()
        {
            MainLeftJoin += $@" 
left join (select sheet_id as placeholder_sheet_id,sheet_no as placeholder_sheet_no,sale_order_sheet_id from sheet_placeholder_order_main where company_id =~COMPANY_ID  and red_flag is null) pm on pm.sale_order_sheet_id = t.sheet_id
left join (select sso.van_id,sso.move_stock,sso.sheet_id as status_sheet_id,sso.order_status,sso.receipt_status,sso.senders_id as assigned_senders_id,sso.senders_name as assigned_senders_name, sso.sale_sheet_id,sso.sheet_print_count print_count,sso.company_id,om.move_sheet_id,van_move.sheet_no move_sheet_no from sheet_status_order sso 
LEFT JOIN op_move_to_van_main om on om.company_id = ~COMPANY_ID and om.op_id = sso.assign_van_id
LEFT JOIN sheet_move_main van_move on van_move.company_id = ~COMPANY_ID and van_move.sheet_id = om.move_sheet_id
WHERE sso.company_id = ~COMPANY_ID  ) tb_status on t.company_id=tb_status.company_id and t.sheet_id=tb_status.status_sheet_id
left join (select company_id,branch_id as van_id,branch_name as van_name from info_branch) van on t.company_id=van.company_id and tb_status.van_id=van.van_id
left join (select oper_id,oper_name as sender_name,mobile as sender_mobile from info_operator) sender on split_part(coalesce(tb_status.assigned_senders_id,t.senders_id),',',1)::integer=sender.oper_id                
left join (select oper_id,oper_name as reviewer_name,company_id from info_operator) reviewer on t.company_id=reviewer.company_id and t.reviewer_id=reviewer.oper_id         
left join (select bill_id as payb_flowid, bill_status as payb_status, trade_no as payb_trade_no from pay_bill where company_id=~COMPANY_ID) payb on t.pay_bill_id = payb.payb_flowid
left join (select addr_id, addr_desc receive_addr_desc,receiver_name,receiver_mobile from info_client_address where company_id=~COMPANY_ID) addr on t.receive_addr=addr.addr_id
       ";
            DetailLeftJoin += $@"  
left join (select company_id,attr_id,sale_combine_print as sale_print_combine_attr from info_attribute where company_id=~COMPANY_ID) attr on (son_attrs.son_mum_attributes->0->>'attrID')::integer=attr.attr_id
left join cw_subject  cw on cw.company_id=~COMPANY_ID and cw.sub_id::text=COALESCE(t.other_info->>'orderSubID','0')

       ";

        }
        public SheetSaleOrder(LOAD_PURPOSE loadPurpose) : base("sheet_sale_order_main", "sheet_sale_order_detail",  loadPurpose)
        {
            ConstructFun();
        }
       
        public override string Init()
        {
            string msg=base.Init();
            if (msg != "") return msg;
            foreach (var row in SheetRows)
            {
                if (row != null)
                {
                    if (row.trade_type == "KS") row.inout_flag = 0;
                } 
            }
            return "";

        }
        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            if (seller_id == "" && IsFromWeb && order_source == "") return "必须指定业务员";

            string tradeType = null;
            bool allSameTradeType = true;
            foreach (var row in SheetRows)
            {
                if (tradeType == null) 
                    tradeType = row.trade_type;
                else if (tradeType != row.trade_type)
                {
                    allSameTradeType = false;
                    break;
                }
            }
            if(allSameTradeType && tradeType != null && sheet_usage == "")
            {
                switch (tradeType)
                {
                    case "DH":
                        sheet_usage = "DH";
                        break;
                    case "HR":
                    case "HC":
                        sheet_usage = "HH";
                        break;
                    case "T":
                        sheet_usage = "T";
                        break;
                    case "CL":
                        sheet_usage = "CL";
                        break;
                    case "KS":
                        sheet_usage = "KS";
                        break;
					case "J":
						sheet_usage = "J";
						break;
					case "H":
						sheet_usage = "H";
						break;

				}
			}

            if (!receive_addr.IsValid() && receive_addr_desc.IsValid())
            {
                cmd.CommandText = $"select addr_id from info_client_address where company_id={company_id} and client_id={supcust_id} and addr_desc='{receive_addr_desc}'";
                object ov = await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value)
                {
                    receive_addr = ov.ToString();
                }
                else
                {
                    cmd.CommandText = $"update info_client_address set addr_order=addr_order+1 where company_id={company_id} and client_id={supcust_id};insert into info_client_address (company_id,client_id,addr_desc,addr_order) values ({company_id},{supcust_id},'{receive_addr_desc}',1) returning addr_id";
                    ov = await cmd.ExecuteScalarAsync();
                    receive_addr = ov.ToString();
                }
            }

            return await base.CheckSaveSheetValid(cmd);
        }
		protected override void GetInfoForSave_SetQQ(SQLQueue QQ)
		{
			if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
			base.GetInfoForSave_SetQQ(QQ);

		}
		protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql = "";
			if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
			CInfoForApprove info = (CInfoForApprove)InfoForApprove;

			base.GetInfoForApprove_SetQQ(QQ);
            if (SheetRows.Count > 0)
            {
                string items_id = "";
                string mumItemsID = "";
                foreach (SheetRowMM row in SheetRows)
                {
                    if (items_id != "") items_id += ",";
                    items_id += row.item_id;

                    if (row.son_mum_item != "")
                    {
                        if (mumItemsID != "") mumItemsID += ",";
                        mumItemsID += row.son_mum_item;
                    }
                }
				if (!IsImported)
				{
                    string condi = $" mu.item_id in ({items_id}) ";
                    if (mumItemsID != "") condi += $"  or mu.item_id in ({mumItemsID}) ";
                    sql = $"select string_agg(brief_text, ',') briefs from info_sheet_detail_brief where company_id = {company_id} and (is_price_remember is not true)";
                    QQ.Enqueue("briefs", sql);
                    sql = $@"select mu.item_id,mu.unit_no as item_unit_no,mu.unit_factor item_unit_factor,lowest_price item_lowest_price,mu.buy_price,mu.cost_price_spec,ip.cost_price_avg,ip.cost_price_recent ,cs.setting->>'unitPriceRelated' unit_price_related from info_item_multi_unit mu left join info_item_prop ip on mu.item_id=ip.item_id left join company_setting cs on cs.company_id={company_id} where mu.company_id =  {company_id} and ({condi}) order by mu.item_id,unit_factor ;";
                    QQ.Enqueue("price", sql);
                    sql = $@"select item_id,borrowed_qty from borrowed_cust_items where company_id = {company_id} and cust_id = {supcust_id} and item_id in ({items_id})";
                    QQ.Enqueue("borrow", sql);
                }
             
                sql = $"select setting from company_setting where company_id = '{company_id}'";
                QQ.Enqueue("setting", sql);
               
                sql = "";
                foreach (SheetRowSaleOrder row in SheetRows)
                {
                    if (row.disp_sheet_id != null && row.disp_sheet_id != "" && row.disp_flow_id != null && row.disp_flow_id != "")
                    {
                        string item_id = row.item_id;
                        if (row.son_mum_item.IsValid()) item_id = row.son_mum_item;
                        if (sql != "") sql += " union ";
                        sql += $@"select d.* from display_agreement_detail d left join display_agreement_main m on m.sheet_id = d.sheet_id and d.company_id = m.company_id 
                                 where d.company_id = {company_id} and supcust_id = {supcust_id} and d.sheet_id = {row.disp_sheet_id} and items_id like '%{item_id}%' and unit_no = '{row.unit_no}' and flow_id = {row.disp_flow_id}";

                    }
                }
                if (sql != "")
                {
                    sql += ";";
                    QQ.Enqueue("display", sql);
                }
            }
            if (red_flag == "2")
            {
                sql = $"select sheet_id from sheet_sale_main where order_sheet_id ={sheet_id} and company_id={company_id} and red_flag is null;";
                QQ.Enqueue("sale_sheet", sql);
                sql = @$"SELECT t.*fROM (
		SELECT od.sale_order_sheet_id,op_type,om.op_id ,case when om.op_no is null then 'ZC'||od.op_id else om.op_no end move_to_van_op_no,((now()::date - om.happen_time::date)+1) assign_van_days,ROW_NUMBER() over(partition by od.sale_order_sheet_id ORDER BY om.happen_time DESC ) rn FROM op_move_to_van_detail od
		LEFT JOIN op_move_to_van_main om on od.company_id = om.company_id and od.op_id = om.op_id
		WHERE od.company_id =  {company_id} and om.red_flag is null and om.approve_time is not null  and sale_order_sheet_id ={sheet_id} 
) t
WHERE t.rn = 1  and t.op_type in('2v','v2v') ";
                QQ.Enqueue("assign_status", sql);
            }
            string sub_ids = payway1_id;
            if (payway2_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway2_id;
            }
            if (payway3_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway3_id;
            }
            if (sub_ids != "")
            {
                sql = $"select sub_id,sub_type,is_order from cw_subject where company_id={company_id} and sub_id in ({sub_ids}) and sub_id not in (select case when (s.setting->>'feeOutSubForKS')::int is null then -1 else (s.setting->>'feeOutSubForKS')::int end sub_id from  company_setting s where s.company_id = {company_id});";
                QQ.Enqueue("payway_type", sql);
            }
            if (sheet_id != "")
            {
                sql = $"select is_del from sheet_sale_order_main where company_id = {company_id} and sheet_id = {sheet_id};";                
                QQ.Enqueue("is_del", sql);
            }

        }
        string NotRememberPriceBriefs = "";
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
            if (sqlName == "briefs")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec.briefs != "") NotRememberPriceBriefs = "," + rec.briefs + ",";
            }
            else if (sqlName == "price" ) //判断 最低售价校验
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                if (records != null)
                {
                    foreach (dynamic rec in records)
                    { 
                        foreach (SheetRowSaleOrder row in SheetRows)
                        {
                            if (row.item_id == rec.item_id)
                            {
                                if (rec.cost_price_avg != "")
                                    row.cost_price_avg = rec.cost_price_avg;
                                if (rec.buy_price != "")
                                {
                                    if (rec.item_unit_factor == "")
                                    {
                                      
                                        logger.Error("in GetInfoForApprove_ReadData,rec.item_unit_factor == ''");
                                    }
                                    try
                                    {
                                        row.cost_price_buy = (CPubVars.ToDecimal(rec.buy_price) / CPubVars.ToDecimal(rec.item_unit_factor)).ToString();
                                    }
                                    catch(Exception e)
                                    {
                                        logger.Error($"in GetInfoForApprove_ReadData, rec.buy_price:{rec.buy_price},rec.item_unit_factor:{rec.item_unit_factor}, row.cost_price_buy can not be got:" +e.Message);
                                    }            
                                }

                                if (rec.cost_price_spec != "")
                                {
                                    if (rec.item_unit_factor == "")
                                    {
                                        logger.Error("in GetInfoForApprove_ReadData,rec.item_unit_factor == ''");
                                    }
                                    try
                                    {
                                        row.cost_price_prop = (CPubVars.ToDecimal(rec.cost_price_spec) / CPubVars.ToDecimal(rec.item_unit_factor)).ToString();
                                    }
                                    catch (Exception e)
                                    {
                                        logger.Error($"in GetInfoForApprove_ReadData, rec.buy_price:{rec.cost_price_spec},rec.item_unit_factor:{rec.item_unit_factor}, row.cost_price_buy can not be got:" + e.Message);
                                    }
                                }

                                if (rec.cost_price_recent != "")
                                {
                                    dynamic pStr = JsonConvert.DeserializeObject(rec.cost_price_recent);
                                    //Console.WriteLine("输出转换后的值：{0}" + "\n" + "转换后的类型：{1} " + "\n", pStr, pStr.GetType());
                                    row.cost_price_recent1 = pStr.avg1;
                                    row.cost_price_recent2 = pStr.avg2;
                                    row.cost_price_recent3 = pStr.avg3;
                                }
                                else
                                {
                                    row.cost_price_recent1 = 0;
                                    row.cost_price_recent2 = 0;
                                    row.cost_price_recent3 = 0;
                                }

								/*if (!IsRealTimeImported && !IsImported)
								{
                                    if (!IsFromWeb && red_flag != "2" && money_inout_flag == 1 && row.quantity > 0)
                                    {
                                        decimal lowPrice = 0;
                                        if (rec.item_lowest_price != "") lowPrice = CPubVars.ToDecimal(rec.item_lowest_price);
                                        if (rec.item_unit_no == row.unit_no && row.real_price != 0 && row.real_price < lowPrice && row.trade_type != "H" && !row.isPromotionItem)
                                            info.ErrMsg = $"{row.item_name}的售价低于最低售价，请重新输入";
                                    }
                                }*/
                                
                            }   
                        }
                        if (info.ErrMsg != "") return;
                    }
                    Dictionary<string, string> dicUniqueItems = new Dictionary<string, string>();
                    foreach(SheetRowSaleOrder row in SheetRows)
                    {
                        if (NotRememberPriceBriefs != "" && NotRememberPriceBriefs.Contains("," + row.remark + ",")) continue;
						if (row.remark.IsValid())
						{
							if (row.remark.Contains("特价") || row.remark.Contains("打折") || row.remark.Contains("临期") || row.remark.Contains("处理")) continue;
						}

						if (row.order_sub_id != "") continue;
                        if (row.quantity * row.inout_flag > 0 && red_flag == "") continue;
                        if (row.quantity * row.inout_flag < 0 && red_flag == "2") continue;
                        if (row.real_price == 0) continue;
                        if (row.isSpecialPrice) continue;
                        if (dicUniqueItems.ContainsKey(row.item_id)) continue;
                        if (row.promotion_id.IsValid()) continue; // 促销活动商品不记录上次售价
                        if (row.vip_is_redeem == "true") continue;
                        dicUniqueItems.Add(row.item_id, row.item_id);
                        foreach(dynamic rec in records)
                        {
                            var itemID = row.son_mum_item != "" && !row.attrRememberPrice ? row.son_mum_item : row.item_id;
                            if(rec.item_id == itemID)
                            {
                                SheetRowSaleOrder orderRow = new SheetRowSaleOrder();
                                orderRow.item_id = itemID;
                                orderRow.unit_no = rec.item_unit_no;
                                orderRow.sys_price = row.sys_price;
                                string unit_price_related = rec.unit_price_related;

                                if (unit_price_related.ToLower() != "false" || rec.item_unit_no == row.unit_no)
                                {
                                    if (rec.item_unit_factor == "") continue;
                                    else
                                    {
                                        int roundNumber = 2;
                                        if (rec.item_unit_factor == "1")
                                        {
                                            roundNumber = 4;
                                        }
                                        orderRow.real_price = CPubVars.ToDecimal(Math.Round((row.real_price / row.unit_factor * CPubVars.ToDecimal(rec.item_unit_factor)), roundNumber));
                                        decimal orig_price = 0;
                                        decimal recent_retail_price = 0;
                                        if (row.orig_price != null && row.orig_price != "")
                                        {
                                            orig_price = CPubVars.ToDecimal(row.orig_price);
                                            orderRow.orig_price = (Math.Round((orig_price / row.unit_factor * CPubVars.ToDecimal(rec.item_unit_factor)), roundNumber)).ToString();
                                        }
                                        if (row.recent_retail_price != null && row.recent_retail_price != "")
                                        {
                                            recent_retail_price = CPubVars.ToDecimal(row.recent_retail_price);
                                            orderRow.recent_retail_price = (Math.Round((recent_retail_price / row.unit_factor * CPubVars.ToDecimal(rec.item_unit_factor)), roundNumber)).ToString();
                                        }
                                    }
                                    orderRow.quantity = row.quantity;
                                    info.UnitPriceRows.Add(orderRow);

                                }
                            }
                        }
                    }
                }
            }
            else if (red_flag == "2" && sqlName == "assign_status")
            {
                dynamic assignStatus = CDbDealer.GetRecordsFromDr(dr, false);
                if (assignStatus.Count > 0)
                {
                    info.ErrMsg = $"该订单进行装车操作,请回退致未装车状态";
                }
            }
            else if (red_flag == "2" && sqlName == "sale_sheet")
            {
                dynamic saleSheet = CDbDealer.Get1RecordFromDr(dr, false);
                if (saleSheet != null)
                {
                    info.ErrMsg = $"订单已被销售单使用,无法红冲";
                }
            }
            else if (sqlName == "display")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowSaleOrder row in SheetRows)
                    {
                        string item_id = row.item_id;
                        if (row.son_mum_item.IsValid()) item_id = row.son_mum_item;
                        if (row.disp_flow_id != null && row.disp_sheet_id != null && row.disp_flow_id != "" && rec.flow_id == row.disp_flow_id && (rec.items_id.IndexOf(item_id) >= 0))
                        {
                            var month_qty = "month" + row.disp_month_id + "_qty";
                            var month_given = "month" + row.disp_month_id + "_given";
                            decimal qty = 0; decimal given = 0;
                            foreach (KeyValuePair<string, object> col in rec)
                            {
                                if (!col.Key.Contains("month" + row.disp_month_id)) continue;
                                if (month_qty == col.Key && !string.IsNullOrWhiteSpace(col.Value.ToString())) qty = CPubVars.ToDecimal(col.Value.ToString());
                                if (month_given == col.Key && !string.IsNullOrWhiteSpace(col.Value.ToString())) given = CPubVars.ToDecimal(col.Value.ToString());
                            }
                            row.disp_month_left = qty - given;
                        }
                    }
                }
                info.SheetRows = SheetRows; 
            }
            else if(sqlName == "payway_type")
            {
                info.PaywaysInfo = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
                var haveOrderItem = false;
                decimal orderAmount = 0;
                foreach (var row in SheetRows)
                {
                    if (row.trade_type == "DH" && row.order_sub_id != "-1") orderAmount += row.sub_amount;
                }
                if (orderAmount > 0) haveOrderItem = true;
                var haveOrderPayWay = false;
                foreach (dynamic payway in info.PaywaysInfo)
                {
                    if (payway.is_order != null && payway.is_order.ToLower() == "true") haveOrderPayWay = true;
                }
                if (haveOrderPayWay && !haveOrderItem) info.ErrMsg = "没有定货会商品无法使用定货会账户支付";
                if (haveOrderItem && !haveOrderPayWay) info.ErrMsg = "定货会商品需要用定货会账户支付";
            }
            else if (sqlName == "setting")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec != null && rec.setting != null)
                {
                    string s = rec.setting;
                    if (s != null)
                        info.CompanySetting = JsonConvert.DeserializeObject(rec.setting);
                }
            }
             else if(sqlName == "is_del")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                string isDel = "";
                if (rec != null) isDel = rec.is_del;
                if (isDel.IsValid()&&isDel.ToLower()== "true") info.ErrMsg="订单已经删除，不能操作！";
            }            
        }
        //public string GetSQLForApprove()
        //{
        //    return  GetApproveSQL(null);
        //}
        public string GetRejectSQL(bool recover)
        {
            // CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";
            if (sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN) return "";
            var orderRows = (from row in this.SheetRows where row.quantity > 0 select row).ToList();
            //MergedSheetRows = MergeSheetRows(orderRows);
            MergedSheetRowByBatchAndItem = MergeSheetRowsByBatchAndItem(orderRows);

            if (!(red_flag == "2" && (this.move_stock.ToLower() == "true" || this.receipt_status == "js")))
            {
                foreach (SheetRowMM row in MergedSheetRowByBatchAndItem)
                {
                    string s = "";
                    string changeQty = "";

                    var qty = row.quantity;
                    if (red_flag == "2") qty *= -1;
                    changeQty = qty.ToString();
                    if (changeQty == "-0") changeQty = "0";
                    if (qty >= 0)
                    {
                        changeQty = "+" + qty.ToString();
                    }
                    else { changeQty = qty.ToString(); }
                    if (string.IsNullOrEmpty(row.batch_id)) row.batch_id = "0";
                    if (string.IsNullOrEmpty(row.branch_position)) row.branch_position = "0";
                    if (string.IsNullOrEmpty(row.branch_id)) row.branch_id = branch_id;
                    s = $"insert into stock(company_id,branch_id,item_id,stock_qty,sell_pend_qty,batch_id,branch_position) values ({company_id},{branch_id},{row.item_id},0,{qty},{row.batch_id},{row.branch_position}) on conflict (company_id,branch_id,item_id,batch_id,branch_position) do update set sell_pend_qty=stock.sell_pend_qty{changeQty};";
                    sql += s;
                    row.NewSellPendQty = row.SellPendQty + qty;
                }
            }
            return sql;

        }
        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";
            if (sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN) return "";
            var orderRows = (from row in this.SheetRows where row.quantity > 0 select row).ToList();
            //MergedSheetRows = MergeSheetRows(orderRows);
            MergedSheetRowByBatchAndItem = MergeSheetRowsByBatchAndItem(orderRows);

            if (!IsImported)
            {
                if (!(red_flag == "2" && (this.move_stock.ToLower() == "true" || this.receipt_status == "js")) && !isOpenPlaceholderOrder)
                {
                    foreach (SheetRowMM row in MergedSheetRowByBatchAndItem)
                    {
                        string s = "";
                        string changeQty = "";

                        //var qty = row.inout_flag * row.quantity1;
                        var qty = row.quantity;
                        if (red_flag == "2") qty *= -1;
                        changeQty = qty.ToString();
                        if (changeQty == "-0") changeQty = "0";
                        if (qty >= 0)
                        {
                            changeQty = "+" + qty.ToString();
                        }
                        else { changeQty = qty.ToString(); }


                        if (string.IsNullOrEmpty(row.batch_id)) row.batch_id = "0";
                        if (string.IsNullOrEmpty(row.branch_position)) row.branch_position = "0";
                        if (string.IsNullOrEmpty(row.branch_id)) row.branch_id = branch_id;
                        if (row.HasStockQty)
                        {
                            s = $"update stock set sell_pend_qty=sell_pend_qty{changeQty} where company_id={company_id} and branch_id={row.branch_id} and item_id={row.item_id} and batch_id = {row.batch_id} and branch_position = {row.branch_position};";
                        }
                        else
                        {
                            s = $"insert into stock(company_id,branch_id,item_id,stock_qty,sell_pend_qty,batch_id,branch_position) values ({company_id},{row.branch_id},{row.item_id},0,{qty},{row.batch_id},{row.branch_position}) on conflict (company_id,branch_id,item_id,batch_id,branch_position) do update set sell_pend_qty=stock.sell_pend_qty{changeQty};";
                        }
                        sql += s;
                        row.NewSellPendQty = row.SellPendQty + qty;
                    }
                }
            }
            if (red_flag != "2")
            {
                string sqlUpdateRecentProduceDate = "";
                string sNow = CPubVars.GetDateText(DateTime.Now);
                foreach (var row in SheetRows)
                {
                    if (!string.IsNullOrEmpty(row.virtual_produce_date))
                    {
                        sqlUpdateRecentProduceDate += $"insert into item_recent_produce_date (company_id,item_id,produce_date,happen_time) values ({company_id},{row.item_id},'{row.virtual_produce_date}','{sNow}') on conflict(company_id,item_id) do update set produce_date='{row.virtual_produce_date}',happen_time='{sNow}';";
                    }
                }

                string sqlUpdateRecentPrice = "";
                if (info.UnitPriceRows.Count() > 0)
                {
                    foreach (SheetRowSaleOrder row in info.UnitPriceRows)
                    {
                        string recent_price = row.real_price.ToString();
                        string recent_orig_price = row.orig_price.IsValid() ? row.orig_price : "null";
                        string recent_retail_price = row.recent_retail_price.IsValid() ? row.recent_retail_price : "null";
                        sqlUpdateRecentPrice += $@"insert into client_recent_prices (company_id,supcust_id,item_id,unit_no,recent_price,recent_orig_price,happen_time,recent_retail_price) values ({company_id},{supcust_id},{row.item_id},'{row.unit_no}',{recent_price},{recent_orig_price},'{happen_time}',{recent_retail_price}) 
                    on conflict(company_id,supcust_id,item_id,unit_no) do update set recent_price={recent_price},recent_orig_price={recent_orig_price},happen_time = '{happen_time}',recent_retail_price = {recent_retail_price}  where (client_recent_prices.happen_time is null or client_recent_prices.happen_time <= '{happen_time}');";
                    }
                }
                


                sql += sqlUpdateRecentProduceDate + sqlUpdateRecentPrice;


            }
            if (visit_id.IsValid())
            {
                decimal amount = this.total_amount * money_inout_flag;
                string s = $"update sheet_visit set order_amount=coalesce(order_amount,0)+{amount} where company_id={company_id} and visit_id={visit_id};";
                sql += s;
            }


            #region 使用陈列协议

            if (red_flag != "2")
            {
                var mergedDispRows = MergeDispSheetRows(SheetRows);
                int flag = 1;
                if (sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN) flag = -1;
                foreach (SheetRowSaleOrder row in mergedDispRows)
                {
                    if (row.disp_flow_id != null && row.disp_flow_id != "")
                    {
                        var month_given = "month" + row.disp_month_id + "_given";
                        decimal qty = row.quantity * flag;
                        if (row.disp_month_left - qty < -0.01m) info1.ErrMsg = $"{row.item_name}使用数量超过相关陈列协议的剩余数量，请重新输入";
                    }
                }
            }
            #endregion
            return sql; 
      
        }

		#region 订单通知
	 

		// 封装查找receiver的逻辑
		private async Task<List<string>> GetReceiverList(CMySbCommand cmd, string order_source = null, bool bchecked = false)
		{
			// 初始化 workerIdList
			List<string> workerIdList = new List<string>();
			// 初始化角色ID列表
			List<string> roleIdList = new List<string>();
			string msg_subtype;

			// 小程序逻辑
			if (order_source != null && order_source.Equals("xcx"))
			{
				if (bchecked == true)
				{
					msg_subtype = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetXCXApproveSubType.SubTypeKey;
				}
				msg_subtype = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetXCXNoticeSubType.SubTypeKey;
			}
			// 其他来源逻辑
			else
			{
				if (bchecked == true)
				{
					msg_subtype = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetApproveSubType.SubTypeKey;

				}
				msg_subtype = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetNoticeSubType.SubTypeKey;

			}


			// 查找直接订阅的worker_id
			string sql = $@"
SELECT worker_id 
FROM msg_subscribe 
WHERE company_id = {this.company_id} 
  AND msg_type = '{MessageType.SaleOrderSheetMessageType.MessageType}'
  AND msg_sub_type = '{msg_subtype}'";

			// 调用异步方法并传递 SQL 和参数
			dynamic directSubscribers = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
			// 检查 directSubscribers 是否为空
			if (directSubscribers != null && directSubscribers.Count > 0)
			{
				// 遍历 directSubscribers 并提取 worker_id
				foreach (var record in directSubscribers)
				{
					string workerId = record.worker_id;
					workerIdList.Add(workerId);
				}
			}
			// 查找订阅者的role_id
			dynamic roleResult = await CDbDealer.GetRecordsFromSQLAsync($@"
        SELECT role_id 
        FROM msg_subscribe_role 
        WHERE company_id = {this.company_id} 
        AND msg_type = '{MessageType.SaleOrderSheetMessageType.MessageType}'
        AND msg_sub_type =  '{msg_subtype}'", cmd);
			// 检查 roleResult 是否为空
			if (roleResult != null && roleResult.Count > 0)
			{
				// 遍历 roleResult 并提取 role_id
				foreach (var record in roleResult)
				{
					string roleId = record.role_id;
					roleIdList.Add(roleId);
				}
			}
			// 查找可视范围权限为all的role list
			List<string> allowedRoleIdList = new List<string>();
			if (roleIdList.Count > 0)
			{
				dynamic rolePermissionsResult = await CDbDealer.GetRecordsFromSQLAsync($@"
            SELECT role_id, rights 
            FROM info_role 
            WHERE company_id = {this.company_id} 
              AND role_id IN ({string.Join(",", roleIdList.Select(id => $"'{id}'"))})", cmd);
				// 创建一个字典来存储查询结果
				Dictionary<string, string> rolePermissions = new Dictionary<string, string>();

				// 遍历查询结果并填充字典
				foreach (var record in rolePermissionsResult)
				{
					string roleId = record.role_id;
					string rights = record.rights;

					// 将结果添加到字典中
					rolePermissions[roleId] = rights;
				}

				// 过滤出拥有delicacy.sheetViewRange == "all"的role_id
				allowedRoleIdList = rolePermissions
			   .Where(p =>
			   {
				   dynamic rights = JsonConvert.DeserializeObject<Dictionary<string, dynamic>>(p.Value);
				   return rights.ContainsKey("delicacy") &&
						  rights["delicacy"].ContainsKey("sheetViewRange") &&
						  rights["delicacy"]["sheetViewRange"]["value"]?.ToString() == "all";
			   })
			   .Select(p => p.Key.ToString())
			   .ToList();
			}

			// 根据允许的role_id（订阅且权限允许）查找worker_id
			if (allowedRoleIdList.Count > 0)
			{
				dynamic workerResult = await CDbDealer.GetRecordsFromSQLAsync($@"
            SELECT oper_id 
            FROM info_operator
            WHERE company_id = {this.company_id} 
              AND role_id IN ({string.Join(",", allowedRoleIdList.Select(id => $"'{id}'"))})", cmd);

				// 合并角色查询的worker_id
				if (workerResult != null && workerResult.Count > 0)
				{
					// 遍历 workerResult 并提取 oper_id
					foreach (var record in workerResult)
					{
						string operId = record.oper_id;
						workerIdList.Add(operId);
					}
				}
			}
			// 去重
			workerIdList = workerIdList.Distinct().ToList();
			return workerIdList;
		}

		// 封装发送消息的逻辑
		private async Task SendMessage(CMySbCommand cmd, string operKey, string operID, string receiverId, string messageTitle, bool isXCX)
		{
            string msgsubtype;
            if (isXCX)
            {
                msgsubtype = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetXCXNoticeSubType.SubTypeKey;
            }
            else
            {
                msgsubtype = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetNoticeSubType.SubTypeKey;
            } 
            await MessageCreateServices.CreateMessageService(new
			{
				operKey,
				createrId = operID,
				msgClass = MessageType.ClassType.Notice,
				msgType = MessageType.SaleOrderSheetMessageType.MessageType,
				msgSubType = msgsubtype,
				receiverId,
				msgTitle = messageTitle,
			}, cmd);
		}


		 

		private async Task UpdateAndCreateNotice(CMySbCommand cmd, string order_source)
		{
			string operKey = this.OperKey;
			string operID = this.OperID;
			string maker_name = this.maker_name;
			string sheetNo = this.sheet_no;
			string msg_subtype;
			// 创建通知消息
			// 查找创建人
			if (order_source != null && order_source.Equals("xcx"))
			{
				msg_subtype = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetXCXApproveSubType.SubTypeKey;
			}
			else
			{
				msg_subtype = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetApproveSubType.SubTypeKey;
			}
			Dictionary<string, dynamic> result = await MessageQueryServices.QueryMessageMsgItemInfoByMsgContentService(new
			{
				operKey,
				msgId = "",
				sheetID = this.sheet_id,
				msgClass = MessageType.ClassType.Todo,
				msgType = MessageType.SaleOrderSheetMessageType.MessageType,
				msgSubType = msg_subtype
			}, cmd);
			string receiverId;
			if (result != null)
			{
				receiverId = result["creater_id"];
			}
			else
			{
				receiverId = maker_id;
			}
			await MessageCreateServices.CreateMessageService(new
			{
				operKey,
				createrId = operID,
				msgClass = MessageType.ClassType.Notice,
				msgType = MessageType.SaleOrderSheetMessageType.MessageType,
				msgSubType = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetApproveSubType.SubTypeKey,
				receiverId,
				msgTitle = @$"{maker_name} 完成了销售订单审核。 单号: {sheetNo}",
			}, cmd);
			//  更新旧消息，增加dealtime+dealworker
			await MessageUpdateServices.UpdateDealMessageService(new
			{
				operKey,
				msgId = "",
				sheetID = this.sheet_id,
				msgClass = MessageType.ClassType.Todo,
				msgType = MessageType.SaleOrderSheetMessageType.MessageType,
				msgSubType = msg_subtype
			}, cmd);

		}

		#endregion


		public dynamic getOpInfo()
        {
            return "";
        }
        public async Task<dynamic> SaveAsBuyOrderSheet(CMySbCommand cmd, dynamic oriSheet)
        {
            string err = "";
            // 送货员
            oriSheet.senders_id = "";
            oriSheet.senders_name = "";
            var sheetType = oriSheet.SheetType;
            SheetBuyOrder sheet = JsonConvert.DeserializeObject<SheetBuyOrder>(JsonConvert.SerializeObject(oriSheet));
            // SheetBuyOrder sheet = new SheetBuyOrder();
            // sheet.SheetRows = oriSheet.SheetRows;
            // TODO 替换为client_id对应的company_id
            string operKey = sheet.OperKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            sheet.Init();
            sheet.SYNCHRONIZE_SHEETS = true;
            sheet.order_source = "2fx";
            sheet.money_inout_flag = sheet.money_inout_flag * -1;
            sheet.OperID = "1";
            string operId = "";
            string rsCompanySql = $"select company_id,oper_id from info_operator where rs_client_id = {supcust_id}";
            dynamic rsCompanyInfo = await CDbDealer.Get1RecordFromSQLAsync(rsCompanySql, cmd);
            string resellerInfoSql = "";
            if (rsCompanyInfo != null)
            {
                resellerInfoSql = $"select * from rs_seller  rss " +
                $"left join (select plan_id,brand_id,client_mapper from rs_plan) rsp on rss.plan_id = rsp.plan_id " +
                $"where company_id={companyID} and reseller_company_id = {rsCompanyInfo.company_id}";
                operId = rsCompanyInfo.oper_id;
            }
            else
            {
                resellerInfoSql = $"select * from rs_seller  rss " +
                $"left join (select plan_id,brand_id,client_mapper from rs_plan) rsp on rss.plan_id = rsp.plan_id " +
                $"where company_id={companyID} and client_id = {oriSheet.supcust_id}";

            }
            dynamic resellerInfo = await CDbDealer.Get1RecordFromSQLAsync(resellerInfoSql, cmd);
            // TODO 客户 此处为厂家 supplier

            sheet.company_id = (string)resellerInfo.reseller_company_id;

            if ((string)resellerInfo.client_mapper == "sellerAsClient")
            {
                // string sellerClientSql = $"select rs_seller_id from info_supcust where oper_id = {clientId}";
                // dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sellerClientSql, cmd);
                sheet.seller_id = operId;
            }
            sheet.supcust_id = (string)resellerInfo.supplier_id;

            sheet.branch_id = "0";

            sheet.sheet_id = "";
            sheet.sheet_no = "";
            sheet.sheet_type = SHEET_TYPE.SHEET_BUY_DD;
            sheet.maker_id = "1";
            sheet.maker_name = "";
            sheet.approver_id = "";
            sheet.approve_time = "";
            sheet.approve_brief = "";

            // TODO delete

            foreach (SheetRowBuyOrder row in sheet.SheetRows)
            {
                string itemId = row.item_id;
                string querySql = $"select item_id,item_class,other_class from info_item_prop where rs_mum_id = {itemId} and company_id ={sheet.company_id}";
                dynamic itemInfo = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);

                dynamic fatherBrand = await CDbDealer.Get1RecordFromSQLAsync($"select item_brand from info_item_prop where item_id = {itemId}", cmd);
                // 查询品牌是否在分销方案里，确保方案修改之后，不包括该品牌商品无法开同步单据
                // 不包括的商品之后是否支持分销商自己开单？
                string planBrandSql = $"select * from rs_plan where plan_id = {resellerInfo.plan_id} AND ',' || brand_id || ',' LIKE '%,{fatherBrand.item_brand},%';";
                dynamic planBrandRet = await CDbDealer.Get1RecordFromSQLAsync(planBrandSql, cmd);
                if (planBrandRet == null)
                {
                    err = $"商品“{row.item_name}”品牌不在分销方案内";
                }

                if (itemInfo == null)
                {
                    err = $@"子公司档案没有商品:{row.item_name}";
                    break;
                }
                row.item_id = itemInfo.item_id;
                row.classId = itemInfo.item_class;
                row.other_class = itemInfo.other_class;
                row.inout_flag = row.inout_flag * (-1);
                if (sheetType == "TD")
                {
                    row.quantity = -1 * row.quantity;
                    row.sub_amount = -1* row.sub_amount;
                }

            }
            if (sheetType == "TD")
            {
                sheet.total_amount = -1 * sheet.total_amount;
                sheet.paid_amount = -1 * sheet.paid_amount;
                sheet.now_pay_amount = -1 * sheet.now_pay_amount;
                sheet.disc_amount = -1 * sheet.disc_amount;
                sheet.now_disc_amount = -1 * sheet.now_disc_amount;
                if (sheet.payway1_amount != 0) sheet.payway1_amount = -1 * sheet.payway1_amount;
                if (sheet.payway2_amount != 0) sheet.payway2_amount = -1 * sheet.payway2_amount;
                if (sheet.payway3_amount != 0) sheet.payway3_amount = -1 * sheet.payway3_amount;

            }

            if(err =="")
                err = await sheet.Save(cmd,false);
            if (err != "") { return new { msg = err }; }
            var ret = new { msg = "", bindSheetInfo = new { bindSheetId = sheet.sheet_id, bindSheetNo = sheet.sheet_no, bindSheetType = sheet.SheetType, companyId = sheet.company_id } };

            // return JsonConvert.SerializeObject(ret);
            return ret;
        } 
        public async override Task<string> RedAndChange<TSheet>(CMySbCommand cmd, bool bAutoCommit = true)
        {
            string err = "";
            cmd.ActiveDatabase = "";
            CMySbTransaction tran = null;
            if (bAutoCommit) tran = cmd.Connection.BeginTransaction();

            if (relatedToVipPoint == "true")
            {
                return "本单与会员积分有关，无法冲改";
            }
            
            Stack<dynamic> stack = new Stack<dynamic>();
            var oldSheetId = old_sheet_id;
            string orderStatusSql = $@"SELECT sso.*,om.op_type  from sheet_status_order sso
            LEFT JOIN op_move_to_van_main om on om.company_id = sso.company_id and om.op_id = sso.assign_van_id 
                WHERE sso.company_id = {company_id} and sso.sheet_id = {oldSheetId};";
            dynamic orderStatus = await CDbDealer.Get1RecordFromSQLAsync(orderStatusSql, cmd);
            string rightsSql = $@"SELECT rights FROM info_operator o 
    left join info_role r on o.company_id = r.company_id and o.role_id = r.role_id
where o.company_id = {company_id} and o.oper_id = {OperID}";
            dynamic jsonRights = await CDbDealer.Get1RecordFromSQLAsync(rightsSql, cmd);
            dynamic rights = JsonConvert.DeserializeObject(jsonRights.rights);
            if (orderStatus != null)
            {
                if (orderStatus.order_status == "zd")
                {
                    err = "订单已转单,冲改失败";
                }
                else if(orderStatus.order_status=="zc")
                {
                    var orderFlow = rights.orderFlow;
                    dynamic assignVan = null;
                    if(orderFlow!=null) assignVan = orderFlow.assignVan;
                    var canRedAssignVan = false;
                    if (assignVan != null)
                    {
                        canRedAssignVan = assignVan.red;
                    }
                    if(!canRedAssignVan)  err = "无装车红冲权限,冲改订单无法修改装车记录";
                    if (orderStatus.assign_van_id != ""&& canRedAssignVan)
                    {
                        string sql = $@"SELECT od.op_id,tt.sale_order_sheet_ids ,om.op_type,om.happen_time,om.approve_time FROM op_move_to_van_detail od
                    LEFT JOIN op_move_to_van_main om on od.company_id = om.company_id and od.op_id = om.op_id
                    LEFT JOIN (SELECT op_id,string_agg(sale_order_sheet_id::text,',') sale_order_sheet_ids FROM op_move_to_van_detail WHERE company_id = {company_id} GROUP BY op_id) tt on tt.op_id = od.op_id
                    WHERE od.company_id = {company_id} and od.sale_order_sheet_id ={oldSheetId} and om.red_flag is null
                    ORDER BY om.happen_time desc --limit 1";
                        dynamic opInfo = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                        if (opInfo.Count > 0)
                        {
                            foreach (dynamic op in opInfo)
                            {
                                var opID = op.op_id;
                                var opType = op.op_type;
                                string saleOrderSheetIds = op.sale_order_sheet_ids;
                                var orderIDsArr = saleOrderSheetIds.Split(",");
                                string sheetIDs = "";
                                foreach (var id in orderIDsArr)
                                {
                                    if (sheetIDs != "") sheetIDs += ",";
                                    if (id != oldSheetId) sheetIDs += id;
                                }
                                if (opType == "2v")
                                {
                                    SheetAssignVan aSheet = new SheetAssignVan(LOAD_PURPOSE.SHOW);
                                    await aSheet.Load(cmd, company_id, opID);
                                    aSheet.OperKey = OperKey;
                                    aSheet.OperID = OperID;
                                 
                                    if(aSheet.approve_time.IsInvalid())
                                    {
                                        err = await aSheet.Delete(cmd, company_id, opID, false);
                                    }
                                    else
                                    { 
										err = await aSheet.Red(cmd, company_id, opID, OperID, "", false);
                                    }
                                    if (err == "")
                                    {
                                        if (sheetIDs == "") sheetIDs = "-1";
                                        stack.Push(aSheet);
                                    }
                                    else
                                    {
                                        break;
                                    }
                                }
                                else if (opType == "v2v")
                                {
                                    SheetChangeVan cSheet = new SheetChangeVan(LOAD_PURPOSE.SHOW);
                                    await cSheet.Load(cmd, company_id, opID);
                                    cSheet.isRedAndChange = true;
                                    cSheet.OperKey = OperKey;
                                    cSheet.OperID = OperID;
                                    err = await cSheet.Red(cmd, company_id, opID, OperID, "", false);
                                    if (err == "")
                                    {
                                        if (sheetIDs == "") sheetIDs = "-1";
                                        stack.Push(cSheet);
                                    }
                                    else
                                    {
                                        break;
                                    }
                                }
                                else if (opType == "retreat")
                                {
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            if(err=="")
                err = await base.RedAndChange<SheetSaleOrder>(cmd, false);


            if (err == "")
            {
                //订单已装车，冲改订单=》红冲原装车单=》生成新的装车单
                //获取装车单的订单号，查询是占位单还是销售订单
                //传递sheets
                while (stack.Count > 0)
                {
                    var assignSheet = stack.Pop();
                    var sheetIDs = "";
                    //获取原装车单对应订单
                    string saleOrderSheetIDs = "";
                    string placeholderOrderSheetIDs = "";
                    string sale_order_sheet_ids = assignSheet.no_retreat_order_sheet_id;
                    string hasPlaceholderSql = $@"select m.sheet_id as sheet_id,sp.sheet_id as placeorder_sheet_id from sheet_sale_order_main m
left join sheet_placeholder_order_main sp on sp.company_id = m.company_id and sp.sale_order_sheet_id = m.sheet_id
where m.company_id = {company_id} and m.sheet_id in ({sale_order_sheet_ids}) and m.red_flag is null and sp.red_flag is null";
                    var records = await CDbDealer.GetRecordsFromSQLAsync(hasPlaceholderSql, cmd);
                    foreach (dynamic rec in records)
                    {
                        string placeholderSheetId = rec.placeorder_sheet_id;
                        string saleOrderSheetId = rec.sheet_id;
                        if (placeholderSheetId.IsValid())
                        {
                            if (placeholderOrderSheetIDs != "") placeholderOrderSheetIDs += ",";
                            placeholderOrderSheetIDs += placeholderSheetId;
                        }
                        else
                        {
                            if (saleOrderSheetIDs != "") saleOrderSheetIDs += ",";
                            saleOrderSheetIDs += saleOrderSheetId;
                        }
                    }
                    if (this.placeholder_sheet_id.IsInvalid())
                    {
                        if (saleOrderSheetIDs != "") saleOrderSheetIDs += ",";
                        saleOrderSheetIDs += this.sheet_id;
                    }
                    else
                    {
                        if (placeholderOrderSheetIDs != "") placeholderOrderSheetIDs += ",";
                        placeholderOrderSheetIDs += this.placeholder_sheet_id;
                    }
                    List<SheetSaleOrder> sumSheets = new List<SheetSaleOrder>();
                    List<dynamic> sheetGroup1 = new List<dynamic>();
                    List<dynamic> newSheetGroup1 = new List<dynamic>();
                    if (saleOrderSheetIDs != "")
                    {
                        SheetSaleOrder.GetSheetsUsage usage = new SheetSaleOrder.GetSheetsUsage();
                        usage.GetSumSheet = true;
                        usage.GetEachSheet = true;
                        usage.SplitUnitRows = true;
                        usage.OptionToRemember = "";
                        SheetSaleOrder.GetSheetsResult res = await SheetSaleOrder.GetItemSheets<SheetSaleOrder, SheetRowSaleOrder>(cmd, OperKey, saleOrderSheetIDs, usage, true, "0", "", "");
                        sumSheets.Add(res.sheetGroup[0].sheets[0]);
                
                        foreach (dynamic resRow in res.sheetGroup[1].sheets)
                        {
                            sheetGroup1.Add(resRow);
                        }
                    }
                    if (placeholderOrderSheetIDs != "")
                    {
                        SheetPlaceholderOrder.GetSheetsUsage placeholderUsage = new SheetPlaceholderOrder.GetSheetsUsage();
                        placeholderUsage.GetSumSheet = true;
                        placeholderUsage.GetEachSheet = true;
                        placeholderUsage.SplitUnitRows = true;
                        placeholderUsage.OptionToRemember = "";
                        SheetPlaceholderOrder.GetSheetsResult placeholderRes = await SheetPlaceholderOrder.GetItemSheets<SheetPlaceholderOrder, SheetRowPlaceholderOrder>(cmd, OperKey, placeholderOrderSheetIDs, placeholderUsage, true, "0", "", "");
                        SheetSaleOrder sheetSaleOrderSheet = JsonConvert.DeserializeObject<SheetSaleOrder>(JsonConvert.SerializeObject(placeholderRes.sheetGroup[0].sheets[0]));
                        sumSheets.Add(sheetSaleOrderSheet);
                        foreach (dynamic resRow in placeholderRes.sheetGroup[1].sheets)
                        {
                            sheetGroup1.Add(resRow);
                        }
                    }
                    //获取原装车单对应订单end
                    var orderIDsArr = sale_order_sheet_ids.Split(',').Distinct().ToList();
                    foreach (var id in orderIDsArr)
                    {
                        if (id != old_sheet_id)
                        {
                            if (sheetIDs != "") sheetIDs += ",";
                            sheetIDs += id;
                        }
                    }
                    if (sheetIDs != "") sheetIDs += ",";
                    sheetIDs += sheet_id;
                    var oldSheetRows = assignSheet.SheetRows;
                    Dictionary<string, SheetRowAssignVan> sheetRowAssign = new Dictionary<string, SheetRowAssignVan>();
                    foreach (dynamic sheet in sheetGroup1)
                    {
                        string sheetType = sheet.SheetType;
                        if(sheetType == "XD" || sheetType == "ZWD")
                        {
                            dynamic newSheet = new ExpandoObject();
                            IDictionary<string, object> newSheetDic = (IDictionary<string, object>)newSheet;
                            PropertyInfo[] properties;
                            string orderSheetId = "";
                            if(sheetType == "ZWD")
                            {
                                orderSheetId = sheet.sale_order_sheet_id;
                                properties = typeof(SheetPlaceholderOrder).GetProperties();
                            }
                            else
                            {
                                orderSheetId = sheet.sheet_id;
                                properties = typeof(SheetSaleOrder).GetProperties();
                            }
                            foreach (PropertyInfo property in properties)
                            {
                                dynamic v = property.GetValue(sheet);
                                if (v != null)
                                {
                                    newSheetDic[property.Name] = JsonConvert.DeserializeObject<dynamic>(JsonConvert.SerializeObject(v));
                                }
                                else
                                {
                                    newSheetDic[property.Name] = null;
                                }
                                    
                            }
                            newSheet.sheetRows = JsonConvert.DeserializeObject<dynamic>(JsonConvert.SerializeObject(sheet.SheetRows));
                            newSheet.SheetRows = null;
                            newSheet.SheetType = null;
                            newSheet.sheetType = sheet.SheetType;
                            newSheetGroup1.Add(newSheet);
                            foreach(dynamic row in sheet.SheetRows)
                            {
                                if(CPubVars.ToDecimal(row.quantity) * CPubVars.ToDecimal(row.inout_flag)<0)
                                {
                                    SheetRowAssignVan newRow = JsonConvert.DeserializeObject<SheetRowAssignVan>(JsonConvert.SerializeObject(row));
                                    newRow.sale_order_sheet_id = orderSheetId;
                                    newRow.sheet_id = "";
                                    newRow.sheet_no = "";
                                    newRow.branch_id = newRow.branch_id.IsInvalid()? sheet.branch_id : newRow.branch_id;
                                    string key = orderSheetId + newRow.item_id + newRow.unit_no + newRow.branch_id + newRow.branch_position + newRow.produce_date + newRow.batch_no;
                                    if (sheetRowAssign.ContainsKey(key)) sheetRowAssign[key].quantity +=newRow.quantity;
                                    else sheetRowAssign[key] = newRow;
                                }
                            }
                        }
                    }
                    dynamic newRows = null;
                    if (assignSheet.op_type == "2v")
                        newRows = JsonConvert.DeserializeObject<List<SheetRowAssignVan>>(JsonConvert.SerializeObject(sheetRowAssign.Values.ToList()));
                    else if (assignSheet.op_type == "v2v")
                        newRows = JsonConvert.DeserializeObject<List<SheetRowChangeVan>>(JsonConvert.SerializeObject(sheetRowAssign.Values.ToList()));
                    if(assignSheet.op_type == "2v")//装车需要获取原装车中row的sale_order_sheet_id == "-1"的row
                    {
                        foreach (var row in oldSheetRows)
                        {
                            string saleOrderSheetId = row.sale_order_sheet_id;
                            if(saleOrderSheetId.IsValid() && saleOrderSheetId == "-1")
                            {
                                newRows.Add(JsonConvert.DeserializeObject<SheetRowAssignVan>(JsonConvert.SerializeObject(row)));
                            }
                        }
                    }
                    string ApproveTime = assignSheet.approve_time;
                    assignSheet.SheetRows = newRows;
                    assignSheet.sheetsInfo = JsonConvert.SerializeObject(newSheetGroup1);
                    assignSheet.newSheetsInfo = JsonConvert.SerializeObject(newSheetGroup1);
                    assignSheet.approve_time = "";
                    assignSheet.approver_id = "";
                    assignSheet.approver_name = "";
                    assignSheet.move_sheet_id = "";
                    assignSheet.move_sheet_ids = "";
                    assignSheet.move_sheet_no = "";
                    assignSheet.sale_order_sheets_id = sheetIDs;
                    assignSheet.sheet_id = "";
                    assignSheet.OperID = OperID;
                    assignSheet.OperKey = OperKey;
                    assignSheet.red_flag = "";
                    if (assignSheet.op_type == "2v")
                    {
                        if(ApproveTime.IsInvalid()) assignSheet.order_status = "pzc";
                        else assignSheet.order_status = "zc";
                    }
                    if(ApproveTime.IsInvalid())  err = await assignSheet.Save(cmd, false);
                    else err = await assignSheet.SaveAndApprove(cmd, false);
                    if (err != "") break;
                }
               
                // 处理红包记录迁移
                // 迁移的意义有:(1)使转单时能够正常结算红包获取;(2)使单据的红包历史连贯
                if (oldSheetId.IsValid() && sheet_id.IsValid())
                {
                    var moveRedPacketHistory = await RedPacketController.MoveLogOnOrderRedAndChanged(cmd,
                        company_id, oldSheetId, sheet_id, SheetType, supcust_id);
                    Console.WriteLine($"SheetSaleOrder.RedAndChange({oldSheetId}->{sheet_id})." +
                        $"MoveRedPacketHistory.Result: {moveRedPacketHistory.ToJsonText()}");
                }
            }
            if (bAutoCommit)
            {
                if (err == "") tran.Commit();
                else tran.Rollback();
            }
            return err;
        }
        protected override async Task<string> CheckForRed(CMySbCommand cmd)
        {
            if (order_status == "zc" )
            {
                return "该订单已装车, 请撤消装车后再红冲";
            }
            else if (order_status == "pzc" && this.placeholderOrderSheet == null)
            {
                return "该订单已装车未审核，请先撤销装车后再红冲";
            }
            return "";
        }

        //这里onSheetIDGot只是覆盖SheetMM中的，以便于什么都不做，否则会产生往来账记录
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            string sql = "";
            CInfoForApprove info = (CInfoForApprove)info1;
            if (left_amount != 0)
            {
                sql += GetSqlForArrears_Order(info1, left_amount, seller_id);
            }

            string sql1 = "";
            string recentPriceTime = "1";

            if (info.CompanySetting != null && info.CompanySetting.recentPriceTime != null)
            {
                recentPriceTime = Convert.ToString(info.CompanySetting.recentPriceTime);
            }
            foreach (SheetRowSaleOrder row in MergedSheetRowByBatchAndItem)
            {
                if (!isOpenPlaceholderOrder)
                {
                    string rowBranchId = row.branch_id;
                    if (rowBranchId.IsInvalid()) rowBranchId = branch_id;
                    string s = $@"insert into stock_change_log 
        (company_id, approve_time,  branch_id,    item_id,    sheet_id,  pre_sell_pend_qty ,new_sell_pend_qty,batch_id,branch_position) 
    values ({company_id},'{approve_time}',{rowBranchId},{row.item_id},{sheet_id},{row.SellPendQty},{row.NewSellPendQty},{row.batch_id},{row.branch_position});";
                    sql += s;
                }

                float cost_price_recent = 0;

                if (recentPriceTime == "1") cost_price_recent = row.cost_price_recent1;
                else if (recentPriceTime == "2") cost_price_recent = row.cost_price_recent2;
                else if (recentPriceTime == "3") cost_price_recent = row.cost_price_recent3;
                sql1 = @$"update sheet_sale_order_detail set  cost_price_recent = {cost_price_recent}  where company_id = {company_id} and item_id = {row.item_id} and sheet_id = {sheet_id};";
                cmd.CommandText = sql1;
                await cmd.ExecuteScalarAsync();
            }


            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
            /*
			#region 检查是否产生了负占用库存
			var itemsID = "";
			foreach (var row in this.SheetRows)
			{
				if (itemsID != "") itemsID += ",";
				itemsID += row.item_id;
			}

			sql = @$"
select item_id,item_name,sell_pend_qty from stock s where s.company_id={this.company_id} and item_id in ({itemsID}) and sell_pend_qty<0";
			var records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
			string itemsName = "";
			foreach (dynamic rec in records)
			{
				if (itemsName != "") itemsName += ",";
				itemsName += rec.item_name;
			}
			if (itemsName != "")
			{
				info.ErrMsg = $"{itemsName}等商品出现负占用库存";
			}
			#endregion
            */
            

            string err = "";

            if (red_flag == "")
            {
        //        string orderSource = "";
        //        if (sheet_id != "")
        //        {
        //            string orderSourceSql = $"select  * from sheet_sale_order_main where company_id = {company_id} and supcust_id = {supcust_id} and sheet_id ={sheet_id};";
        //            dynamic dbSheet = await CDbDealer.Get1RecordFromSQLAsync(orderSourceSql, cmd);
        //            orderSource = dbSheet.order_source;
        //        }
        //        string rsCompanySql = $"select company_id from info_operator where rs_client_id = {supcust_id}";
        //        dynamic rsCompanyInfo = await CDbDealer.Get1RecordFromSQLAsync(rsCompanySql, cmd);
        //        string rsSellerSql = "";
        //        if (rsCompanyInfo != null)
        //        {
        //            rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync FROM rs_seller rs
        //LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
        //WHERE rs.company_id = {company_id} and rs.reseller_company_id = {rsCompanyInfo.company_id};";
        //        }
        //        else
        //        {
        //            rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync FROM rs_seller rs
        //LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
        //WHERE rs.company_id = {company_id} and rs.client_id = {supcust_id};";
        //        }
        //        dynamic rsSeller = await CDbDealer.Get1RecordFromSQLAsync(rsSellerSql, cmd);
        //        string msg = "";
        //        if (rsSeller != null && rsSeller.sheet_sync.ToLower() == "true" && orderSource != "2fx")
        //        {
        //            string ret = "";
        //            try
        //            {
        //                ret = await SaveAsBuyOrderSheet(cmd, this);
        //                var ob = JsonConvert.DeserializeObject(ret);
        //            }
        //            catch (Exception e)
        //            {
        //                if (ret != "") msg = ret;
        //                else msg = "生成上级销售订单出错";
        //                info.ErrMsg = msg;
        //                MyLogger.LogMsg($"In SaveAndApprove,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);


        //            }
        //            this.bindSheetInfo = ret;
        //        }

                #region 红包
                if (err.Length == 0)
                {
                    // 记录红包
                    if (redpacket_use_amount != null || redpacket_earn != null)
                    {
                        // 注：这两个变量不会存储到数据库，目前使用红包的小程序也不会重复保存审核，因此暂时不需要担心重复记录
                        var redPacketChanges = new Dictionary<string, decimal>();
                        decimal redPacketUse = redpacket_use_amount ?? 0;
                        decimal redPacketGet = redpacket_earn ?? 0;
                        if (redPacketUse > 0)
                            redPacketChanges.Add(RedPacketController.RedPacketChangeType.PurchaseCost, redPacketUse * -1);
                        /* Note 2023.11.15: 红包的获取调整到转单审核后结算
                        * if (redPacketGet > 0)
                            redPacketChanges.Add(RedPacketController.RedPacketChangeType.PurchaseReward, redPacketGet);*/
                        if (redPacketChanges.Count > 0)
                        {
                            var logRedPacketResult = await RedPacketController.LogSheetRedPacketChange(cmd, sheet_id,
                                SheetType, company_id, supcust_id, redPacketChanges.ToList());
                            if (!logRedPacketResult.IsOK)
                            {
                            
                                info.ErrMsg = $"{sheet_id}标记红包失败: '{logRedPacketResult.ToJsonText()}'";
                                NLogger.Error($"{sheet_id}标记红包失败: '{logRedPacketResult.ToJsonText()}'");
                                return; 
                            }
                                
                        }
                    }

                    // 记录限时特价商品
                    var promotionItems = PromotionController.GetSeckillItems(this);
                    var logSeckillItemsResult = await PromotionController.LogSeckillItems(cmd,
                        company_id, sheet_id, SheetType, supcust_id, promotionItems);
                    Console.WriteLine($"logSeckillItemsResult.Result: '{logSeckillItemsResult.ToJsonText()}'");
                }
                #endregion

                #region 占位单

                if (isOpenPlaceholderOrder)
                { 
                    if (placeholderOrderSheet == null) placeholderOrderSheet = await SavePlaceholderOrderSheet(cmd);
                    placeholderOrderSheet.sale_order_sheet_id = this.sheet_id;
                    err = await placeholderOrderSheet.SaveAndApprove(cmd, false);
                    if (err != "")
                    {
                        info.ErrMsg = err; return;
                    }
                    this.placeholder_sheet_id = placeholderOrderSheet.sheet_id;
                    this.placeholder_sheet_no = placeholderOrderSheet.sheet_no;
                    
                }
                #endregion

                #region 会员
                if (err == "" && this.supcust_id.IsValid() && this.supcust_id != "-1" && this.supcust_id != "0")
                {
                    // 处理会员积分扣减 - 审核前
                    CheckSheetVipPointResponse checkSheetVipPointResponse = null;

                    if (this.supcust_id!=null && this.supcust_id!="" && this.supcust_id!="-1" && this.supcust_id != "0")
                    {
                        var checkSheetVipPointCallResult = await MallMiniVipPointService.CheckSheetVipPoint(cmd,
                    company_id, supcust_id, wx_user_id, this);
                        if (!checkSheetVipPointCallResult.IsOK)
                        {
                            info.ErrMsg = checkSheetVipPointCallResult.msg;
                            return;
                        }
                        checkSheetVipPointResponse = checkSheetVipPointCallResult.data;
                        if (checkSheetVipPointResponse?.pendMap is not null)
                            this.vipPointPendMap = JsonConvert.SerializeObject(checkSheetVipPointResponse.pendMap);

                    }

                    var logSheetVipPointPendCallResult = await MallMiniVipPointService.LogSheetVipPointPend(cmd,
                    company_id, supcust_id, wx_user_id, this, checkSheetVipPointResponse);
                    if (!logSheetVipPointPendCallResult.IsOK)
                    {
                         info.ErrMsg =  logSheetVipPointPendCallResult.msg;
                         return;
                    }
                      
                }

                if (err == "")
                {
                    List<string> workerIdList = new List<string>();
                    workerIdList = await GetReceiverList(cmd);
                    foreach (var worker_id in workerIdList)
                    {
                        if (workerIdList.Count > 0)
                        {
                            await UpdateAndCreateNotice(cmd, this.order_source);
                        }
                    }
                }
                #endregion
            }
             
           
		}
		public string GetSqlForArrears_Order(CInfoForApproveBase info, decimal leftAmount, string sellerId)
        {
            string sql = "";
          //  decimal arrearsBal = 0;
            decimal changeBal = leftAmount * money_inout_flag;
            //if (info.ArrearsBalance != "") arrearsBal = CPubVars.ToDecimal(info.ArrearsBalance);
         //   arrearsBal += changeBal;
            string custID = info.ClientIdForAcct;
          
            sql += $"insert into arrears_balance (company_id,supcust_id,pend_amount) values ({company_id},{custID},{changeBal}) on conflict(company_id,supcust_id) do update set pend_amount = arrears_balance.pend_amount + ({changeBal});";
         
            if (info.SellerHasMaxArrears && leftAmount != 0)
            {
                sql += @$"insert into arrears_balance_auxiliary(company_id,auxiliary_type,auxiliary_id,auxiliary_pend_amount) 
                                                        values ({company_id},'seller',     {sellerId},  {changeBal})
             on conflict(company_id,auxiliary_type,auxiliary_id) do update set auxiliary_pend_amount= arrears_balance_auxiliary.auxiliary_pend_amount + ({changeBal});";
            }
            return sql;

        }

        protected List<SheetRowSaleOrder> MergeDispSheetRows(List<SheetRowSaleOrder> rows)
        {
            Dictionary<string, SheetRowSaleOrder> rowsDict = new Dictionary<string, SheetRowSaleOrder>();
            foreach (SheetRowSaleOrder sheetRow in SheetRows)
            {
                if (sheetRow.disp_flow_id != null && sheetRow.disp_flow_id == "") continue;
                string skey = sheetRow.disp_flow_id + "_" + sheetRow.disp_month_id;
                SheetRowSaleOrder curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = new SheetRowSaleOrder();
                    curRow.item_id = sheetRow.item_id;
                    curRow.item_name = sheetRow.item_name;
                    curRow.unit_no = sheetRow.unit_no;
                    curRow.quantity = sheetRow.quantity;
                    curRow.inout_flag = sheetRow.inout_flag;
                    curRow.trade_type = sheetRow.trade_type;
                    curRow.disp_flow_id = sheetRow.disp_flow_id;
                    curRow.disp_month_id = sheetRow.disp_month_id;
                    curRow.disp_sheet_id = sheetRow.disp_sheet_id;
                    curRow.disp_month_left = sheetRow.disp_month_left;
                    rowsDict.Add(skey, curRow);
                }
                else curRow.quantity += sheetRow.quantity;

            }
            List<SheetRowSaleOrder> newList = new List<SheetRowSaleOrder>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }

		#region 重载SheetBase里的方法
		/* Note
         * 通过重载SheetBase的SaveAndApprove、Red等方法来为本类型单据的处理流程统一地添加逻辑
         * await base.xxx是原逻辑的调用节点，可以在此之前或之后视情况地插入代码
         * 在base.xxx之前的检查,应当重载到CheckSheetValid方法中
        */
		public override async Task<string> OnSheetBeforeSave(CMySbCommand cmd, CInfoForApproveBase info)
		{
            // 检查限时特价商品限购
            var checkPromotionSeckillResult = await PromotionController.CheckSeckillItems(cmd, this);
            Console.WriteLine($"SaleOrderSheet({sheet_id}).checkPromotionSeckillResult.Result: " +
                checkPromotionSeckillResult.ToJsonText());
            if (!checkPromotionSeckillResult.IsOK)
                return checkPromotionSeckillResult.msg;

            return "";
        }
		public override async Task<string> OnSheetSaved(CMySbCommand cmd, string sheetID)
        {
			// 记录红包
			if (redpacket_use_amount != null || redpacket_earn != null)
			{
				// 注：这两个变量不会存储到数据库，目前使用红包的小程序也不会重复保存审核，因此暂时不需要担心重复记录
				var redPacketChanges = new Dictionary<string, decimal>();
				decimal redPacketUse = redpacket_use_amount ?? 0;
				decimal redPacketGet = redpacket_earn ?? 0;
				if (redPacketUse > 0)
					redPacketChanges.Add(RedPacketController.RedPacketChangeType.PurchaseCost, redPacketUse * -1);
				/* Note 2023.11.15: 红包的获取调整到转单审核后结算
                 * if (redPacketGet > 0)
                    redPacketChanges.Add(RedPacketController.RedPacketChangeType.PurchaseReward, redPacketGet);*/
				if (redPacketChanges.Count > 0)
				{
					var logRedPacketResult = await RedPacketController.LogSheetRedPacketChange(cmd, sheetID,
						SheetType, company_id, supcust_id, redPacketChanges.ToList());
					if (!logRedPacketResult.IsOK)
						NLogger.Error($"{sheet_id}标记红包失败: '{logRedPacketResult.ToJsonText()}'");
				}
			}

			// 记录限时特价商品
			var promotionItems = PromotionController.GetSeckillItems(this);
			var logSeckillItemsResult = await PromotionController.LogSeckillItems(cmd,
				company_id, sheetID, SheetType, supcust_id, promotionItems);
			if (!logSeckillItemsResult.IsOK)
				NLogger.Error($"{sheet_id}记录限时特价商品失败: '{logSeckillItemsResult.ToJsonText()}'");
             
			return "";

		}
		public override async Task<string> OnSheetCreated(CMySbCommand cmd, CInfoForApproveBase info)
		{ 
			//CInfoForApprove info = (CInfoForApprove)info1;
			string operKey = this.OperKey;
			string operID = this.OperID;
			string maker_name = this.maker_name;
			string sheetNo = this.sheet_no;

			List<string> workerIdList = new List<string>();

			// 确定订单来源,获取相应接收者
			if (order_source != null && order_source == "xcx")
			{
				workerIdList = await GetReceiverList(cmd, "xcx");
				// 查找权限并发送消息
				foreach (var worker_id in workerIdList)
				{
					if (workerIdList.Count > 0)
					{
						string messageTitle = $"小程序来单啦！单号: {sheetNo}";
						await SendMessage(cmd, operKey, operID, worker_id, messageTitle, true);
					}
				}
			}
			else
			{
				workerIdList = await GetReceiverList(cmd);
				foreach (var worker_id in workerIdList)
				{
					if (workerIdList.Count > 0)
					{
						string messageTitle =  $"线下来单啦！单号: {sheetNo}";
						await SendMessage(cmd, operKey, operID, worker_id, messageTitle, false);
					}
				}
			}


		 
			// 未订阅则不创建消息
			if (workerIdList.Contains(operID))
			{
				// 检查是否存在消息
				Dictionary<string, dynamic> result = await MessageQueryServices.QueryMessageMsgItemInfoByMsgContentService(new
				{
					operKey,
					msgId = "",
					sheetID = this.sheet_id,
					msgClass = MessageType.ClassType.Todo,
					msgType = MessageType.SaleOrderSheetMessageType.MessageType,
					msgSubType = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetApproveSubType.SubTypeKey
				}, cmd);
				string msgId = result["msgId"];
				//
				if (msgId.Equals(""))
				{
					if (order_source != null && this.order_source.Equals("xcx"))
					{
						await MessageCreateServices.CreateMessageService(new
						{
							operKey,
							createrId = operID,
							msgClass = MessageType.ClassType.Todo,
							msgType = MessageType.SaleOrderSheetMessageType.MessageType,
							msgSubType = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetXCXApproveSubType.SubTypeKey,
							receiverId = "",
							sheetID = this.sheet_id,

							msgTitle = @$"{maker_name} 申请小程序销售订单审核。 单号: {sheetNo}， 请尽快处理",
						}, cmd);
					}
					else
					{
						await MessageCreateServices.CreateMessageService(new
						{
							operKey,
							createrId = operID,
							msgClass = MessageType.ClassType.Todo,
							msgType = MessageType.SaleOrderSheetMessageType.MessageType,
							msgSubType = MessageType.SaleOrderSheetMessageType.MessageSubType.SaleOrderSheetApproveSubType.SubTypeKey,
							receiverId = "",
							sheetID = this.sheet_id,
							msgTitle = @$"{maker_name} 申请销售订单审核。 单号: {sheetNo}， 请尽快处理",
						}, cmd);
					}
				}
			}
			return "";

		}
		public async override Task<string> Delete(CMySbCommand cmd, string companyID, string sheetID, string operID, bool bAutoCommit = true)
        {
            var checkPaybillSafe = await PayBillController.CheckPayBillOnSafe(cmd,
                companyID, sheetID, "XD");
            if (checkPaybillSafe.result == "No")
            {
                var msg = "本单据有尚未结算的支付订单，暂时无法删除！";
                if (checkPaybillSafe.data.Length > 0)
                    msg += $"请在{checkPaybillSafe.data}之后再尝试。";
                return msg;
            }

            var baseDeleteResult = await base.Delete(cmd, companyID, sheetID, operID, bAutoCommit);
            if (baseDeleteResult.Length > 0)
                return baseDeleteResult;

            // 回滚单据记录过的特价商品使用
            var unlogSeckillItemsResult = await PromotionController.UnlogSeckillItems(cmd,
                companyID, sheetID, SheetType, supcust_id);
            Console.WriteLine($"unlogSeckillItemsResult.Result: '{unlogSeckillItemsResult.ToJsonText()}'");

            // 回滚单据的会员积分占用
            if(this.supcust_id!="" && this.supcust_id != "-1")
            {
				var restoreVipPointPend = await MallMiniVipPointService.RestoreVipPointPend(cmd, this);
				Console.WriteLine($"restoreVipPointPend.Result: '{restoreVipPointPend.ToJsonText()}'");

			}

			return string.Empty;
        }
        #endregion

        public async Task<string> DoCommonJobBeforeSaveApprove(CMySbCommand cmd)
        {
            if (!IsImported)
            {
                string orderSource = "";
                if (sheet_id != "")
                {
                    string orderSourceSql = $"select  * from sheet_sale_order_main where company_id = {company_id} and supcust_id = {supcust_id} and sheet_id ={sheet_id};";
                    dynamic dbSheet = await CDbDealer.Get1RecordFromSQLAsync(orderSourceSql, cmd);
                    orderSource = dbSheet.order_source;
                }
                string rsCompanySql = $"select company_id from info_operator where rs_client_id = {supcust_id}";
                dynamic rsCompanyInfo = await CDbDealer.Get1RecordFromSQLAsync(rsCompanySql, cmd);
                string rsSellerSql = "";
                if (orderSource == "") orderSource = order_source;
                if (rsCompanyInfo != null)
                {
                    rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync FROM rs_seller rs
        LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
        WHERE rs.company_id = {company_id} and rs.reseller_company_id = {rsCompanyInfo.company_id};";
                }
                else
                {
                    rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync FROM rs_seller rs
        LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
        WHERE rs.company_id = {company_id} and rs.client_id = {supcust_id};";
                }
                dynamic rsSeller = await CDbDealer.Get1RecordFromSQLAsync(rsSellerSql, cmd);
                string msg = "";
                if (rsSeller != null && rsSeller.sheet_sync.ToLower() == "true" && orderSource != "2fx")
                {
                    dynamic ret = "";
                    try
                    {
                        ret = await SaveAsBuyOrderSheet(cmd, this);
                        if (ret.msg != "")
                        {
                            return ret.msg;
                        }
                        else
                        {
                            this.bindSheetInfo = JsonConvert.SerializeObject(ret.bindSheetInfo);
                        }
                    }
                    catch (Exception e)
                    {
                        msg = "生成下级采购订单出错";
                        MyLogger.LogMsg($"In SaveAndApprove,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);


                    }
                }
                return msg;
            }
            return "";
            
        }

        public SheetSale ToSaleSheet(string operKey)
        {
            SheetSale saleSheet = JsonConvert.DeserializeObject<SheetSale>(JsonConvert.SerializeObject(this));
            foreach (SheetRowSale row in saleSheet.SheetRows)
            {
                string batchLevel = (string)row.batch_level;
                if (!string.IsNullOrEmpty(batchLevel) && batchLevel != "0")
                {
                    row.produce_date = row.produce_date;
                }
                else
                {
                    // row.produce_date = "无产期"; // 会导致定制列从不显示到显示的问题
                    row.produce_date = "";
                    row.batch_id = "0";
                    row.batch_no = "";
                }
            }
                
            if (this.move_stock.ToLower() == "true")
            {
                if (!string.IsNullOrWhiteSpace(this.van_id))
                {
                    saleSheet.branch_id = this.van_id;
                    saleSheet.branch_name = this.van_name;
                    Dictionary<string, SheetRowSale> saleRows = new Dictionary<string, SheetRowSale>();
                    foreach(SheetRowSale row in saleSheet.SheetRows)
                    {
                        if (this.van_id.IsValid())
                        {
                            row.branch_id = "";
                            row.branch_name = "";
                            row.branch_position = "0";
                            row.branch_position_name = "";
                            row.batch_id = "0";
                        }
                       
                        SheetRowSale newRow = JsonConvert.DeserializeObject<SheetRowSale>(JsonConvert.SerializeObject(row));
                        newRow.branch_id = "";
                        newRow.branch_name = "";
                        newRow.branch_position = "0";
                        newRow.branch_position_name = "";
                        newRow.batch_id = string.IsNullOrEmpty(newRow.batch_id) ? "0": newRow.batch_id;
                        //string key = newRow.item_id + newRow.batch_id;
                        //if (!saleRows.ContainsKey(key))
                        //{
                        //    saleRows.Add(key, newRow);
                        //}
                        //else
                        //{
                        //    saleRows[key].quantity += newRow.quantity;
                        //}
                    }
                    //List<SheetRowSale> newSheetRows = new List<SheetRowSale>();
                    //foreach(KeyValuePair<string, SheetRowSale> kv in saleRows)
                    //{
                    //    newSheetRows.Add(kv.Value);
                    //}
                    //saleSheet.SheetRows = newSheetRows;
                }

            }
            saleSheet.sheet_type = SHEET_TYPE.SHEET_SALE;
            if (this.sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN) saleSheet.sheet_type = SHEET_TYPE.SHEET_SALE_RETURN;

            saleSheet.order_sheet_id = this.sheet_id;
            saleSheet.order_sheet_no = this.sheet_no;
            saleSheet.OperID = OperID;
            saleSheet.sheet_id = "";
            saleSheet.sheet_no = "";
            saleSheet.approver_id = "";
            saleSheet.approver_name = "";
            saleSheet.approve_time = "";
            saleSheet.approve_brief = "";
            saleSheet.make_time = "";
            saleSheet.maker_id = "";
            saleSheet.maker_name = "";
            saleSheet.happen_time = "";
            saleSheet.license_no = license_no;
            saleSheet.order_source = this.order_source;
            saleSheet.OperKey = operKey;
            saleSheet.send_van_name = this.van_name;
            saleSheet.send_van_id = this.van_id;
            saleSheet.pay_bill_id = this.pay_bill_id;
            saleSheet.payb_status = this.payb_status;
            saleSheet.payb_trade_no = this.payb_trade_no;
            saleSheet.relatedToVipPoint = this.relatedToVipPoint;
            saleSheet.vipPointPendMap = this.vipPointPendMap;
            saleSheet.wx_user_id = this.wx_user_id;
            saleSheet.Init();
            return saleSheet;
        }
    
        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd, CInfoForApproveBase info)
        {
            cmd.ActiveDatabase = "";
            string msg = "";
            msg = await DoCommonJobBeforeSaveApprove(cmd);
            
               
                    if (!IsImported && !IsRealTimeImported)
                    {
                        if(placeholderOrderSheet == null)
                        {
                            await ManageOrderPlaceholderOrder(cmd);
                        }
                        else
                        {
                            isOpenPlaceholderOrder = true;
                        } 

                        var checkPromotionSeckillResult = await PromotionController.CheckSeckillItems(cmd, this);
                        Console.WriteLine($"SaleOrderSheet({sheet_id}).checkPromotionSeckillResult.Result: " +
                            checkPromotionSeckillResult.ToJsonText());
                        if (!checkPromotionSeckillResult.IsOK)
                            return checkPromotionSeckillResult.msg;

                        
                }
                      
           
            return msg;
        }
        //protected override async Task<string> CheckSheetRowValid(CMySbCommand cmd)
        //{
        //    foreach (dynamic row in this.SheetRows)
        //    {
        //        string branchID = row.branch_id;
        //        string branchName = row.branch_name;
        //        if (branchID.IsInvalid())
        //        {
        //            branchID = branch_id;
        //            branchName = branch_name;
        //        }
        //        if (row.branch_position == "0" || string.IsNullOrWhiteSpace(row.branch_position)) continue;
        //        dynamic record = await CDbDealer.Get1RecordFromSQLAsync($"select flow_id from info_branch_position where company_id = {company_id} and branch_position = {row.branch_position} and branch_id = {branchID};", cmd);
        //        if (record == null)
        //        {
        //            return $"{branchName}不存在库位：{row.branch_position_name}";
        //        }
        //    }
        //    return "OK";

        //}
        public async Task ManageOrderPlaceholderOrder(CMySbCommand cmd)
        {
            if (sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN) return;
            //开启占位单，销售订单是否生成占位单规则：
            //1.录入产期或rowbranchid或库位或无仓库=》占位单
            //4.无产期，无库位=》是否有库位，是否存在产期
            string sqlForSetting = $"select setting from company_setting where company_id = '{company_id}'";
            cmd.CommandText = sqlForSetting;
            var jsonSetting = await cmd.ExecuteScalarAsync();
            dynamic setting = null;
            if (jsonSetting != null)
            {
                setting = JsonConvert.DeserializeObject(jsonSetting.ToString());
            }
            if (setting==null || setting.openPlaceholderSheet ==null || setting.openPlaceholderSheet.ToString().ToLower() == "false")
            {
                return;
            }
            string sql = "";
            string items_id = "";
            var orderRows = (from row in this.SheetRows where row.quantity > 0 select row).ToList();
            foreach (SheetRowSaleOrder row in orderRows)
            {
                if(row.batch_id!="0" || row.branch_position != "0" || row.branch_id.IsValid() || branch_id.IsInvalid())
                {
                    isOpenPlaceholderOrder = true;
                    break;
                }
                if (items_id == "") items_id =row.item_id;
                else
                {
                    items_id += ","+row.item_id;
                }
            }
            if (!isOpenPlaceholderOrder && items_id !="")
            {
                sql = $@"select company_id from stock where company_id = {company_id} and item_id in ({items_id}) and branch_id = {branch_id} and (branch_position <>0 or batch_id <> 0) limit 1;";
                dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (records.Count !=0)
                {
                    isOpenPlaceholderOrder = true;
                }
            }
        }

        public async Task<SheetPlaceholderOrder> SavePlaceholderOrderSheet (CMySbCommand cmd)
        {
            string item_ids = "";
            //需要分配信息的商品行
            //1.数量>0、产期或库位、仓库未填
            List<SheetRowSaleOrder> noNeedAlocateRows = new List<SheetRowSaleOrder>();
            List<SheetRowSaleOrder> needAlocateRows = new List<SheetRowSaleOrder>();
            foreach (SheetRowSaleOrder sheetrow in SheetRows)
            {
                SheetRowSaleOrder row = JsonConvert.DeserializeObject<SheetRowSaleOrder>(JsonConvert.SerializeObject(sheetrow));
                if (row.quantity >0 && row.branch_id.IsInvalid() && branch_id.IsInvalid())
                {
                    needAlocateRows.Add(row);
                    continue;
                }
                if(row.quantity >0 && row.batch_level.IsValid() && row.produce_date.IsInvalid())
                {
                    needAlocateRows.Add(row);
                    continue;
                }
                if (row.quantity >0 && row.branch_position_name.IsInvalid())
                {
                    needAlocateRows.Add(row);
                    continue;
                }
                noNeedAlocateRows.Add(row);
            }
            string defaultBranchId = "-1";
            string operBranchRightsOperInfo = OperID.Equals("-1") ? $" and oper_id={seller_id} " : $" and oper_id={OperID} ";   // 小程序的operid此处是-1
            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync($"select string_agg(branch_id::text,',') as branchs_id from oper_branch_rights where company_id={company_id} {operBranchRightsOperInfo} and sheet_xd = 'True'", cmd);
            string branchs_id = rec.branchs_id;
            List<SheetRowPlaceholderOrder> placeholderOrderSheetRows = new List<SheetRowPlaceholderOrder>();//占位单
            if (branchs_id.IsValid())
            {
                defaultBranchId = branchs_id.Split(",")[0];
            }
            foreach (dynamic row in needAlocateRows)
            {
                if (item_ids == "") item_ids = row.item_id;
                else
                {
                    item_ids += "," + row.item_id;
                }
            }
            if (item_ids !="")
            {
                List<ItemStock> itemStockInfo = new List<ItemStock>();
                if (branchs_id.IsValid())
                {
                    string sql = $@"select item_id,branch_id,branch_position,s.batch_id,stock_qty,sell_pend_qty,(COALESCE(stock_qty,0)-coalesce(sell_pend_qty,0)) as avail_stock_qty,
COALESCE(produce_date::text,'') as produce_date,COALESCE(batch_no,'') as batch_no
from stock s
LEFT JOIN info_item_batch itb on itb.company_id = {company_id} and itb.batch_id = s.batch_id
where s.company_id = {company_id} and stock_qty >0 and (COALESCE(stock_qty,0)-coalesce(sell_pend_qty,0)) > 0 and item_id in({item_ids}) and branch_id in ({branchs_id});";
                    dynamic stockInfo = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                    itemStockInfo =  JsonConvert.DeserializeObject<List<ItemStock>>(JsonConvert.SerializeObject(stockInfo));
                }
                Dictionary<string, decimal> stockInfoDic = new Dictionary<string, decimal>();
                foreach (ItemStock item in itemStockInfo)
                {
                    string key = item.item_id + item.branch_id +item.branch_position + item.batch_id;
                    if (stockInfoDic.ContainsKey(key)) stockInfoDic[key] += CPubVars.ToDecimal(item.avail_stock_qty);//可用库存
                    else stockInfoDic[key] = CPubVars.ToDecimal(item.avail_stock_qty);
                }
                foreach(SheetRowSaleOrder row in noNeedAlocateRows)
                {
                    string rowBranchId = row.branch_id.IsInvalid() ? branch_id : row.branch_id;
                    string branchPosition = row.branch_position.IsInvalid() ? "0" : row.branch_id;
                    string key = row.item_id + rowBranchId +branchPosition + row.batch_id;
                    if(stockInfoDic.ContainsKey(key)) stockInfoDic[key] -= row.quantity * row.unit_factor;
                }
                foreach (SheetRowSaleOrder row in needAlocateRows)
                {
                    List<ItemStock> inventoryStock= itemStockInfo.Where(stock => stock.item_id == row.item_id).ToList();
                    string rowBranchId = string.IsNullOrEmpty(row.branch_id) ? branch_id : row.branch_id;
                    if (row.batch_id != "0") inventoryStock = inventoryStock.Where(stock => stock.batch_id == row.batch_id).ToList();
                    if (row.branch_position != "0") inventoryStock = inventoryStock.Where(stock => stock.branch_position == row.branch_position).ToList();
                    if (!rowBranchId.IsInvalid()) inventoryStock = inventoryStock.Where(stock => stock.branch_id == rowBranchId).ToList();
                    inventoryStock = inventoryStock.Where(stock => CPubVars.ToDecimal(stock.avail_stock_qty)>0).ToList();
                    inventoryStock = inventoryStock.OrderBy(item => item.produce_date)
                              .ThenBy(item => item.batch_no)
                              .ThenByDescending(item => item.avail_stock_qty).ToList();
                    if (inventoryStock.Count != 0)
                    {
                        foreach (ItemStock item in inventoryStock)
                        {
                            if (row.quantity > 0)
                            {
                                decimal qty = row.quantity * row.unit_factor;
                                decimal quantityToAllocate = Math.Min(qty, CPubVars.ToDecimal(item.avail_stock_qty));
                                decimal curQty = Math.Floor( quantityToAllocate/row.unit_factor);
                                decimal leftQty = quantityToAllocate%row.unit_factor;
                                if (curQty != 0)
                                {
                                    SheetRowSaleOrder newRow = JsonConvert.DeserializeObject<SheetRowSaleOrder>(JsonConvert.SerializeObject(row));
                                    newRow.quantity = curQty;
                                    newRow.sub_amount = newRow.quantity * newRow.real_price;
                                    newRow.batch_id = item.batch_id;
                                    newRow.produce_date = item.produce_date;
                                    newRow.batch_no = item.batch_no;
                                    newRow.branch_id = item.branch_id;
                                    newRow.branch_position = item.branch_position;
                                    newRow.sheet_id = "";
                                    SheetRowPlaceholderOrder placeholderOrderRow = JsonConvert.DeserializeObject<SheetRowPlaceholderOrder>(JsonConvert.SerializeObject(newRow));
                                    placeholderOrderSheetRows.Add(placeholderOrderRow);
                                }
                                if (leftQty != 0)
                                {
                                    SheetRowSaleOrder newRow = JsonConvert.DeserializeObject<SheetRowSaleOrder>(JsonConvert.SerializeObject(row));
                                    newRow.quantity = leftQty;//小单位
                                    decimal newUnitFactor = newRow.unit_factor;//原单位的包装率
                                    if (newRow.unit_no == newRow.b_unit_no) newRow.b_unit_factor=newUnitFactor.ToString();//为陈列协议
                                    if (newRow.unit_no == newRow.m_unit_no) newRow.m_unit_factor=newUnitFactor.ToString();
                                    newRow.unit_no = newRow.s_unit_no;
                                    newRow.real_price = newRow.real_price / newRow.unit_factor;
                                    newRow.sub_amount = newRow.quantity * newRow.real_price;
                                    newRow.orig_price = (CPubVars.ToDecimal(newRow.orig_price) / newRow.unit_factor).ToString();
                                    newRow.sys_price = (CPubVars.ToDecimal(newRow.sys_price) / newRow.unit_factor).ToString();
                                    newRow.unit_factor = 1;
                                    newRow.batch_id = item.batch_id;
                                    newRow.produce_date = item.produce_date;
                                    newRow.batch_no = item.batch_no;
                                    newRow.branch_id = item.branch_id;
                                    newRow.branch_position = item.branch_position;
                                    newRow.sheet_id = "";
                                    SheetRowPlaceholderOrder placeholderOrderRow = JsonConvert.DeserializeObject<SheetRowPlaceholderOrder>(JsonConvert.SerializeObject(newRow));
                                    placeholderOrderSheetRows.Add(placeholderOrderRow);
                                }
                                // 更新库存数量
                                item.sell_pend_qty = (CPubVars.ToDecimal(item.sell_pend_qty) + quantityToAllocate).ToString();
                                item.avail_stock_qty = (CPubVars.ToDecimal(item.avail_stock_qty) - quantityToAllocate).ToString();
                                // 更新请求的数量
                                row.quantity = (qty - quantityToAllocate) / row.unit_factor;
                                //库存不足=》剩余的分配
                                if (row.quantity > 0 && inventoryStock.IndexOf(item) == (inventoryStock.Count - 1))
                                {
                                    decimal leftCurQty = Math.Floor(row.quantity);
                                    decimal leftRowQty = (row.quantity * row.unit_factor)%row.unit_factor;
                                    if (leftCurQty != 0)
                                    {
                                        SheetRowSaleOrder newRow = JsonConvert.DeserializeObject<SheetRowSaleOrder>(JsonConvert.SerializeObject(row));
                                        newRow.quantity = leftCurQty;
                                        newRow.sub_amount = newRow.quantity * newRow.real_price;
                                        newRow.batch_id = "0";
                                        newRow.produce_date = "";
                                        newRow.batch_no = "";
                                        newRow.branch_id = item.branch_id;
                                        newRow.branch_position = "0";
                                        newRow.sheet_id = "";
                                        SheetRowPlaceholderOrder placeholderOrderRow = JsonConvert.DeserializeObject<SheetRowPlaceholderOrder>(JsonConvert.SerializeObject(newRow));
                                        placeholderOrderSheetRows.Add(placeholderOrderRow);
                                    }
                                    if (leftRowQty != 0)
                                    {
                                        SheetRowSaleOrder newRow = JsonConvert.DeserializeObject<SheetRowSaleOrder>(JsonConvert.SerializeObject(row));
                                        newRow.quantity = leftRowQty;//小单位
                                        decimal newUnitFactor = newRow.unit_factor;//原单位的包装率
                                        if (newRow.unit_no == newRow.b_unit_no) newRow.b_unit_factor=newUnitFactor.ToString();//为陈列协议
                                        if (newRow.unit_no == newRow.m_unit_no) newRow.m_unit_factor=newUnitFactor.ToString();
                                        newRow.unit_no = newRow.s_unit_no;
                                        newRow.real_price = newRow.real_price / newRow.unit_factor;
                                        newRow.sub_amount = newRow.quantity * newRow.real_price;
                                        newRow.orig_price = (CPubVars.ToDecimal(newRow.orig_price) / newRow.unit_factor).ToString();
                                        newRow.sys_price = (CPubVars.ToDecimal(newRow.sys_price) / newRow.unit_factor).ToString();
                                        newRow.unit_factor = 1;
                                        newRow.batch_id = "0";
                                        newRow.produce_date = "";
                                        newRow.batch_no = "";
                                        newRow.branch_id = item.branch_id;
                                        newRow.branch_position = "0";
                                        newRow.sheet_id = "";
                                        SheetRowPlaceholderOrder placeholderOrderRow = JsonConvert.DeserializeObject<SheetRowPlaceholderOrder>(JsonConvert.SerializeObject(newRow));
                                        placeholderOrderSheetRows.Add(placeholderOrderRow);
                                    }
                                }
                            }
                            else
                            {
                                break; // 已满足请求数量，退出循环
                            }
                        }

                    }
                    else
                    {
                        SheetRowSaleOrder newRow = JsonConvert.DeserializeObject<SheetRowSaleOrder>(JsonConvert.SerializeObject(row));
                        string newRowBranchId = row.branch_id.IsInvalid() ? (branch_id.IsInvalid() ? defaultBranchId : branch_id) : row.branch_id;
                        newRow.branch_id = newRowBranchId;
                        SheetRowPlaceholderOrder placeholderOrderRow = JsonConvert.DeserializeObject<SheetRowPlaceholderOrder>(JsonConvert.SerializeObject(newRow));
                        placeholderOrderSheetRows.Add(placeholderOrderRow);
                    }
                }
            }
            SheetPlaceholderOrder placeholderOrderSheet = JsonConvert.DeserializeObject<SheetPlaceholderOrder>(JsonConvert.SerializeObject(this));
            placeholderOrderSheet.sale_order_sheet_id = sheet_id;
            placeholderOrderSheet.sheet_id = "";
            placeholderOrderSheet.sheet_no = "";
            placeholderOrderSheet.happen_time = "";
            placeholderOrderSheet.approve_time = "";
            placeholderOrderSheet.SheetType = "ZWD";
            placeholderOrderSheet.SheetRows = placeholderOrderSheetRows;
            foreach(dynamic row in noNeedAlocateRows)
            {
                SheetRowPlaceholderOrder newRow = JsonConvert.DeserializeObject<SheetRowPlaceholderOrder>(JsonConvert.SerializeObject(row));
                placeholderOrderSheet.SheetRows.Add(newRow);
            }
            return placeholderOrderSheet;
        }

		protected override async Task<string> BeforeRed(CMySbCommand cmd, string sheetID, string rederID, string redBrief, CInfoForApproveBase info)
		{
			string msg = "";
            string sql = "";
      
            try
            {
                
                if (msg==""&& isOpenPlaceholderOrder)
                {
                    SheetPlaceholderOrder placeholderOrderSheet = new SheetPlaceholderOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
                    msg = await placeholderOrderSheet.Red(cmd, this.company_id, placeholder_sheet_id, rederID, redBrief, false);
                    if (msg == "") placeholderOrderSheet.sale_order_sheet_id = this.red_sheet_id;
                }

                // 回滚单据记录过的特价商品使用
                var unlogSeckillItemsResult = await PromotionController.UnlogSeckillItems(cmd,
					this.company_id, sheetID, SheetType, supcust_id);
                Console.WriteLine($"unlogSeckillItemsResult.Result: '{unlogSeckillItemsResult.ToJsonText()}'");

                // 回滚单据的会员积分占用
                var oriSheet = new SheetSaleOrder();
                await oriSheet.Load(cmd, this.company_id, sheetID);
                if(this.supcust_id!="" && this.supcust_id != "-1")
                {
					var restoreVipPointPend = await MallMiniVipPointService.RestoreVipPointPend(cmd, oriSheet);
					Console.WriteLine($"restoreVipPointPend.Result: '{restoreVipPointPend.ToJsonText()}'");
				}
            }
            catch (Exception e)
            { 
                string errMsg = $"In Save,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}";
                NLogger.Error(errMsg);
                MyLogger.LogMsg(errMsg, company_id);
            }
            return msg;
        }

        
    }
    public class ItemStockInfo
    {
        public string item_id;
        public string sum_stock_qty;
        public List<ItemStock> item_stock;
    }
    public class ItemStock
    {
        public string item_id;
        public string produce_date;
        public string batch_no;
        public string batch_id;
        public string branch_position;
        public string branch_id;
        public string stock_qty;
        public string sell_pend_qty;
        public string avail_stock_qty;
    }
}


