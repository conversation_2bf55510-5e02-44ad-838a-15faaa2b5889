﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HSSF.UserModel;
using NPOI.HSSF.Util;
using NPOI.XSSF.UserModel;
using NPOI.XSSF.Util;
using NPOI.XSSF.Streaming;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using static ArtisanManage.Models.DataItem;
using static ArtisanManage.Models.FormDataGrid;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Asn1.X509;
using NPOI.XWPF.UserModel;
using ICell = NPOI.SS.UserModel.ICell;
using System.Text.RegularExpressions;
using System.Web;//2024.9.6ICell可能是NPOI.SS.UserModel.ICell或NPOI.XWPF.UserModel.ICell,为解决该问题添加命名空间申明

namespace ArtisanManage.Models
{
	public class QueryGrid
	{
        public Dictionary<string, DataItem> Columns = new Dictionary<string, DataItem>();
        public async Task<Dictionary<string, DataItem>> GetAllColumns()
        {
            Dictionary<string, DataItem> columns = new Dictionary<string, DataItem>();
            foreach (var kp in Columns)
            {
                if (kp.Value.FuncGetSubColumns != null) await kp.Value.TryGetSubColumns();
                if (kp.Value.SubColumns != null)
                {
                    foreach (var kp1 in kp.Value.SubColumns)
                    {
                        columns.Add(kp1.Key, kp1.Value);
                    }
                }
                else columns.Add(kp.Key, kp.Value);
            }
            return columns;
        }
        public string TableName = "", IdColumn = "";
        public bool DistictFields = false;
        public string JsFuncGetRowKeyForCheck = "";
        public string JSBeforeCreate = "";
        public bool HasCheck = false, KeepCheckForQueries = false;
        public bool Sortable = false;
        public string SortColumn = "";
        public string SortDirection = "";

		public bool PageByOverAgg = true;
        public bool AllowMultiColumnsSort = false;

        public bool ShowAggregates = false;
        public int ColumnsHeight = 0;
        public int RowsHeight = 0;
        public string SubRowsNameInRow = "";//代表下级表的对象在行中的名称
        public List<Dictionary<string, DataItem>> Rows = new List<Dictionary<string, DataItem>>();
        public string QueryFromSQL = "";//from sheet_item_master left join info_supcust on sheet_item_master.supcust_no=info_supcust.sup_no
        public string QueryFromSQL2 = "";
        public bool AddEmptyRow = true; // 表格末尾添加一个空行
 
		public enum QUERY_SQL_RELATION
        {
            UNION,
            FULL_JOIN
        }

        public Func<Task<string>> FuncQueryFromSQL;
        public string QueryGroupBySQL = "";
        public string QueryOrderSQL = "";
        //public string QueryCondi = "";
        public bool AutoAppendQueryCondition = true;
        public int MinRows = 3;
        public bool AllowAddRow = false;
        public int PageSize = 200;


        public string ContextMenuHTML = @"<ul>
                        <li id=""edit"">编辑</li>
                        <li id=""delete"">删除</li>
                       </ul> ";
        public bool ShowContextMenu = false;
        public string ZeroAsEmpty = "";
        // public Dictionary<string, string> QueryItemsSqlFld = new Dictionary<string, string>();

    }
    public class PageQueryModel : PageBaseModel
    {
        public PageQueryModel(MenuId pageMenuID) : base(pageMenuID)
        {
            Database = "Report";
        }
        public bool UsePostMethod = false;
        public bool EnableBigDataMode = false;
        public Dictionary<string, QueryGrid> Grids = new Dictionary<string, QueryGrid>();
        public string m_querySQL = "";
        public string SQLVariable1 = "", SQLVariable2 = "", SQLVariable3 = "", SQLVariable4 = "";
        public Dictionary<string, string> SQLVariables = new Dictionary<string, string>();
        public bool QueryConditionIsAfterAnd = false;
        //public string m_idFld = "",m_nameFld="",
        public string m_defaultTable = "";
        public JObject record = new JObject();
        public bool NotQueryHideColumn = false;
        public string m_createGridScript = "";
        public bool m_bNewRecord = false;
        protected Func<string, string> FuncDealCondition;
        public bool CanQueryByApproveTime = false;
        public string QueryTimeCondi = "";

        public HttpRequest HttpRequest = null;
        public List<string> CoalesceDataItems = null;
        public string DefaultUrlPrefix = "../api/";
        // public object GetRecordFromQuerySQL(CMySbCommand cmd,dynamic request, string gridID,int startRow,int endRow,bool bGetRowsCount)
        public virtual async Task<JsonResult> DealQueriedRecords(dynamic data, dynamic postParams)
        {
            return new JsonResult(data);
        }
        class GroupCondiResult
        {
            public string Condi = "", HavingCondi = "";
        }

        public static string GetParamFromRequest(HttpRequest request, dynamic postData, string param)
        {
            if (postData != null)
            {
                object ov = postData[param]; string value = null; if (ov != null) value = ov.ToString();
                if (value != null) return value;
            }
            if (request != null)
            {
                object ov = request.Query[param]; string value = null; if (ov != null) value = ov.ToString(); return value;
            }
            return null;
        }
        private static string removeTagContent(string input, string tagName)
		{
			// 正则表达式匹配 <DelForSum> 标签及其中的内容，跨多行匹配
			string pattern = $@"<({tagName})>[\s\S]*?</\1>";

			// 替换匹配到的部分为空字符串
			return Regex.Replace(input, pattern, "");
		}
        public static bool IsColumnReliedByOtherColumn(ref int loopCount, string columnKey,string orderSQL,Dictionary<string,DataItem> columns,  Dictionary<string,string> dicReachedPath=null)
        {
            if (columnKey == "return_rate")
            {

            }
            loopCount++;
            if (loopCount > 3000)
                return true;
            if (dicReachedPath==null) dicReachedPath=new Dictionary<string, string>();
            dicReachedPath.Add(columnKey, columnKey);
            if (dicReachedPath.Count > 3000)
            {
                throw new Exception("IsColumnReliedByOtherColumn回调超出最大限制次数");
            }
            if(columnKey== "profit_rate_hasfree")
            {

            }
            var col = columns[columnKey];

            string sqlFld = columnKey;
            if (sqlFld == "other_class")
            {

            }
            if (col.SqlFld != "") sqlFld = col.SqlFld;
            string[] arr=sqlFld.Split(".");
            if (arr.Length == 2) sqlFld = arr[1];
            if (orderSQL.IndexOf(sqlFld) >= 0)
            {
                return true;
            }

            int relayMeColumnCount = 0;
            foreach (var kp in columns)
            {
                DataItem curItem = kp.Value;

                if (curItem.RelyColumns != "" && kp.Key!=columnKey)
                {
                    relayMeColumnCount++;
                    arr=curItem.RelyColumns.Split(',');
                    bool curItemRely = false;
                    foreach(string s in arr)
                    {
                        if (s == columnKey )
                        {   
                          // // if (!curItem.Hidden)
                           // {
                                curItemRely = true; break;
                           // }                
                        }
                    }
                    if (curItemRely)
                    {
                        if (!curItem.Hidden)
                        {
                            return true;
                        }
                        else
                        {
                            if (!dicReachedPath.ContainsKey(kp.Key))
                            {
                                // throw new Exception("列之间循环依赖,relyColumns的引用必须是单向的，相关列:" + kp.Key);

                                if (IsColumnReliedByOtherColumn(ref loopCount, kp.Key,orderSQL, columns, dicReachedPath))
                                {
                                    return true;
                                }
                            }
                        } 
                    }
                }
                if (curItem.SortFld == columnKey)
                {
                    return true;
                }
            }

            if (relayMeColumnCount == 0)
            {
              //  return true;
            }
            dicReachedPath.Remove(columnKey);
            return false;

        }
		public virtual async Task<JsonResult> GetRecordFromQuerySQL(HttpRequest request, CMySbCommand cmd, dynamic postData = null)
        {

            this.HttpRequest = request;
            string ip = "";
            if (this.HttpRequest != null)
            {
                this.HttpRequest.HttpContext.Connection.RemoteIpAddress.ToString();
            }
            string gridID = "";
            int startRow = 0;
            int endRow = 0;
            bool bGetRowsCount = false;
            object o = null;
            //  UpdateDataItemsByDataLimit();
            #region Add new dataitem for tree structure that has top node limited
            
            string[] keys = DataItems.Keys.ToArray();
            for (int i = 0; i < keys.Length; i++)
            {
                string key = keys[i];
                var dataItem = DataItems[key];
                //if (dataItem.TopNodeValueRestricted)
                if (!DataItems.ContainsKey(key + "_hidden"))
                {
                    string value = GetParamFromRequest(request, postData, key + "_hidden");
                    
                    if (value.IsValid())
                    {
                        DataItem hidden = new DataItem();
                        hidden.HideOnLoad = true;
                        hidden.SqlAreaToPlace = dataItem.SqlAreaToPlace;
                        hidden.Hidden = true;
                        hidden.Value = value;
                        hidden.CompareOperator = "like";
                        hidden.SqlFld = key;
                        this.DataItems.Add(key + "_hidden", hidden);
                    }
                }
            }
            #endregion


            /*
			string optionToRemember = CPubVars.RequestV(Request, "optionToRemember");
			if (optionToRemember.IsValid())
			{
				optionToRemember = System.Web.HttpUtility.UrlDecode(optionToRemember);
				string opt = optionToRemember;
				cmd.CommandText = $"insert into options_remembered (company_id,oper_id,options) values ({companyID},{operID},'{opt}'::jsonb) on conflict(company_id,oper_id) do update set options=jsonb_merge(options_remembered.options,'{opt}'::jsonb);";
				await cmd.ExecuteNonQueryAsync();
			}
            */

            gridID = GetParamFromRequest(request, postData, "gridID");
            string operKey = GetParamFromRequest(request, postData, "operKey");
            JObject gridSetting = null;
            if (postData != null)
            {
                gridSetting = postData.gridSetting;
            }

            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            //problem code xiang  companyID没有使用
            company_id = companyID;
            OperID = operID;
            o = GetParamFromRequest(request, postData, "startRow"); if (o != null) startRow = Convert.ToInt32(o.ToString());
            o = GetParamFromRequest(request, postData, "endRow"); if (o != null) endRow = Convert.ToInt32(o.ToString());
            o = GetParamFromRequest(request, postData, "GetRowsCount"); if (o != null && o.ToString() != "") bGetRowsCount = Convert.ToBoolean(o.ToString());

            QueryGrid grid = Grids[gridID];

            if (grid.AllowAddRow) { startRow = 0; endRow = 500; }


            // string condi = "",havingCondi="";
            string ignoreDataItems = "";

            DataItemGroup.FillDataItemsByRequest(DataItems, request, postData, ref ignoreDataItems);
            DataItemGroup.ClearIgnoreDataItems(DataItems, ignoreDataItems);
            if (CoalesceDataItems != null)
            {
                foreach (string fields in CoalesceDataItems)
                {
                    string[] arr = fields.Split(',');
                    int metCount = 0;
                    foreach (var f in arr)
                    {
                        DataItems.TryGetValue(f, out DataItem item);
                        if (item != null && item.Value != "")
                        {
                            metCount++;
                        }
                    }
                    if (metCount == arr.Length)
                    {
                        foreach (var f in arr)
                        {
                            DataItems.TryGetValue(f, out DataItem item);
                            if (item != null && item.Value != "")
                            {
                                string sqlFld = f;
                                if (item.SqlFld != "") sqlFld = item.SqlFld;
                                if (!sqlFld.Contains("coalesce", StringComparison.OrdinalIgnoreCase))
                                {
                                    item.SqlFld = $"coalesce({sqlFld},'0')";
                                }

                            }
                        }
                    }



                }
            }

            foreach (var kp in DataItems)
            {
                DataItem dataItem = kp.Value;
                if (kp.Key == "endDay")
                {
                    if (dataItem.Value != null && dataItem.Value.EndsWith("23:59:00"))
                    {
                        dataItem.Value = dataItem.Value.Replace("23:59:00", "23:59:59");
                    }
                }


				if (kp.Key == "startDay" || kp.Key == "endDay")
				{
					if (dataItem.Value != null && dataItem.Value!="")
                    {
                        if (!CPubVars.IsDate(dataItem.Value))
                        {
                            return new JsonResult(new { result = "Error", msg = $"{dataItem.Title}{dataItem.Value}不是正确的日期格式" });
                        }
					}
				}

			}
            Dictionary<string, DataItem> addDataItems = null;
            List<DataItem> lstNeedResetDataItems = new List<DataItem>();
            //var IstNeedResetDataItems = {};
            if (this.CanQueryByApproveTime)//默认使用单据审核时间查询，会根据传过来的happen_time,自动加上approve_time查询条件，happen_time的开始时间会向前推30天以确保查询速度（因为数据表按happen_time分区）
            {
                
                string sByHappenTime = GetParamFromRequest(request, postData, "byHappenTime") ?? "";
                string queryTimeType = GetParamFromRequest(request, postData, "queryTimeAccord") ?? "";
                bool byHappenTime = sByHappenTime.ToLower() == "true" || (queryTimeType != "" && queryTimeType == "byHappenTime");
                bool byMakeTime = queryTimeType != "" && queryTimeType == "byMakeTime";
                bool byCheckedTime = queryTimeType != "" && queryTimeType == "byCheckedTime";
                bool bySendTime = queryTimeType != "" && queryTimeType == "bySendTime";
                bool byClearArrearsTime =  queryTimeType == "byClearArrearsTime";
				//NLogger.Info($"byHappenTime:{sByHappenTime},{byHappenTime}");
				addDataItems = new Dictionary<string, DataItem>();
                foreach (KeyValuePair<string, DataItem> kp in DataItems)
                {
                    DataItem dataItem = kp.Value;
                    //if(kp.Key == "endDay")
                    //{
                    //    if(dataItem.Value!=null && dataItem.Value.EndsWith("23:59:00"))
                    //    {
                    //        dataItem.Value = dataItem.Value.Replace("23:59:00", "23:59:59");
                    //    }
                    //}
                    if ( kp.Key == "startDay" || kp.Key == "endDay")
                    {
                        if (queryTimeType == "byMakeTime")
                        {
                            DataItem newDataItem = JsonConvert.DeserializeObject<DataItem>(JsonConvert.SerializeObject(dataItem));
                            newDataItem.SqlFld = "make_time";
                            addDataItems.Add(kp.Key + "_sheetTime", newDataItem);

                            DataItem newHappenTimeDataItem = JsonConvert.DeserializeObject<DataItem>(JsonConvert.SerializeObject(dataItem));
                            addDataItems.Add(kp.Key + "_happenTime", newHappenTimeDataItem);

                            dataItem.ForQuery = false;
                            lstNeedResetDataItems.Add(dataItem);
                            if (newHappenTimeDataItem.CompareOperator.Contains(">"))
                            {
                                DateTime tm = Convert.ToDateTime(newHappenTimeDataItem.Value);
                                tm = tm.AddDays(-360);
                                newHappenTimeDataItem.Value = CPubVars.GetDateText(tm);
                            }
                            else if (newHappenTimeDataItem.CompareOperator.Contains("<"))
                            {
                                DateTime tm = Convert.ToDateTime(newHappenTimeDataItem.Value);
                                tm = tm.AddDays(180);
                                newHappenTimeDataItem.Value = CPubVars.GetDateText(tm);
                            }
                            grid.QueryOrderSQL = grid.QueryOrderSQL.Replace(" happen_time", " " + newDataItem.SqlFld);

                        }
                        else if (queryTimeType == "bySendTime")
                        {
                            DataItem newDataItem = JsonConvert.DeserializeObject<DataItem>(JsonConvert.SerializeObject(dataItem));
                            newDataItem.SqlFld = "send_time";
                            addDataItems.Add(kp.Key + "_sheetTime", newDataItem);

                            DataItem newHappenTimeDataItem = JsonConvert.DeserializeObject<DataItem>(JsonConvert.SerializeObject(dataItem));
                            addDataItems.Add(kp.Key + "_happenTime", newHappenTimeDataItem);

                            dataItem.ForQuery = false;
                            lstNeedResetDataItems.Add(dataItem);
                            if (newHappenTimeDataItem.CompareOperator.Contains(">"))
                            {
                                DateTime tm = Convert.ToDateTime(newHappenTimeDataItem.Value);
                                tm = tm.AddDays(-180);
                                newHappenTimeDataItem.Value = CPubVars.GetDateText(tm);
                            }
                            grid.QueryOrderSQL = grid.QueryOrderSQL.Replace(" happen_time", " " + newDataItem.SqlFld);
                        }
						else if (queryTimeType == "byCheckedTime")
						{
							DataItem newDataItem = JsonConvert.DeserializeObject<DataItem>(JsonConvert.SerializeObject(dataItem));
							newDataItem.SqlFld = "check_account_time";
							addDataItems.Add(kp.Key + "_sheetTime", newDataItem);

                            DataItem newHappenTimeDataItem = JsonConvert.DeserializeObject<DataItem>(JsonConvert.SerializeObject(dataItem));
                            addDataItems.Add(kp.Key + "_happenTime", newHappenTimeDataItem);

                            dataItem.ForQuery = false;
                            lstNeedResetDataItems.Add(dataItem);
                            if (newHappenTimeDataItem.CompareOperator.Contains(">"))
							{
								DateTime tm = Convert.ToDateTime(newHappenTimeDataItem.Value);
								tm = tm.AddDays(-180);
                                newHappenTimeDataItem.Value = CPubVars.GetDateText(tm);
							}
							grid.QueryOrderSQL = grid.QueryOrderSQL.Replace(" happen_time", " " + newDataItem.SqlFld);
						}
						else if (queryTimeType == "byClearArrearsTime")
						{
							DataItem newDataItem = JsonConvert.DeserializeObject<DataItem>(JsonConvert.SerializeObject(dataItem));
							newDataItem.SqlFld = "settle_time";
							addDataItems.Add(kp.Key + "_sheetTime", newDataItem);

                            DataItem newHappenTimeDataItem = JsonConvert.DeserializeObject<DataItem>(JsonConvert.SerializeObject(dataItem));
                            addDataItems.Add(kp.Key + "_happenTime", newHappenTimeDataItem);

                            dataItem.ForQuery = false;
                            lstNeedResetDataItems.Add(dataItem);
                            if (newHappenTimeDataItem.CompareOperator.Contains(">"))
							{
								DateTime tm = Convert.ToDateTime(newHappenTimeDataItem.Value);
								tm = tm.AddDays(-180);
                                newHappenTimeDataItem.Value = CPubVars.GetDateText(tm);
							}
							grid.QueryOrderSQL = grid.QueryOrderSQL.Replace(" happen_time", " " + newDataItem.SqlFld);
						} 
						else if (!byCheckedTime && !byHappenTime && dataItem.Value != "")
						{
							DataItem newDataItem = JsonConvert.DeserializeObject<DataItem>(JsonConvert.SerializeObject(dataItem));
                           // string fld=kp.Key
                            newDataItem.SqlFld = "coalesce(approve_time,make_time)";// "approve_time,make_time";
							
                            addDataItems.Add(kp.Key + "_sheetTime", newDataItem);

                            DataItem newHappenTimeDataItem = JsonConvert.DeserializeObject<DataItem>(JsonConvert.SerializeObject(dataItem));
                            addDataItems.Add(kp.Key + "_happenTime", newHappenTimeDataItem);

                            dataItem.ForQuery = false;
                            lstNeedResetDataItems.Add(dataItem);
                            if (newHappenTimeDataItem.CompareOperator.Contains(">"))
							{
								DateTime tm = Convert.ToDateTime(newHappenTimeDataItem.Value);
								tm = tm.AddDays(-360);
                                newHappenTimeDataItem.Value = CPubVars.GetDateText(tm);
							}
							grid.QueryOrderSQL = grid.QueryOrderSQL.Replace(" happen_time", " coalesce(approve_time,make_time)");
						}
		 

					}

                }

                foreach (var k in addDataItems)
                {
                    if (!DataItems.ContainsKey(k.Key)) DataItems.Add(k.Key, k.Value);
                }
            }

            if (DataItems.ContainsKey("startDay") && DataItems.ContainsKey("endDay"))
            {
                string _sheetTime = "";
                if (DataItems.ContainsKey("startDay_sheetTime"))
                {
                    _sheetTime = "_sheetTime";
                }
                this.QueryTimeCondi = $" {DataItems[$"startDay{_sheetTime}"].SqlFld}>='{DataItems[$"startDay{_sheetTime}"].Value}' and {DataItems[$"endDay{_sheetTime}"].SqlFld}<='{DataItems[$"endDay{_sheetTime}"].Value}'";
                if (_sheetTime != "")
                {
                    this.QueryTimeCondi += $" and {DataItems["startDay"].SqlFld}>='{DataItems["startDay"].Value}' and {DataItems["endDay"].SqlFld}<='{DataItems["endDay"].Value}'";
                }

            }

            Dictionary<string, DataItem> dicAllColumns = new Dictionary<string, DataItem>();
            foreach (KeyValuePair<string, DataItem> kp in grid.Columns)
            {
                dicAllColumns.Add(kp.Key, kp.Value);
                if (kp.Value.SubColumns != null)
                {
                    foreach (var kp1 in kp.Value.SubColumns)
                    {
                        dicAllColumns.Add(kp1.Key, kp.Value);
                    }
                }
            }

            if (gridSetting != null)
            {
                foreach (var kp in dicAllColumns)
                {
                    dynamic col = gridSetting.GetValue(kp.Key);
                    if (col != null)
                    {
                        if (col.hidden != null)
                        {
                            if (col.hidden == true)
                                kp.Value.Hidden = true;
                            else if (col.hidden == false)
                                kp.Value.Hidden = false;
                        }
                    }
                }
            }

            //用法：<RELATE_COLUMNS columns="col1,col2,col3..."> ...<ELSE/> </RELATE_COLUMNS>
            //放在QueryFromSQL中，如果RELATE_COLUMNS冒号后的列名都设置为Hidden，则从QueryFromSQL中去掉上述尖括号内sql
            //目的：优化查询速度
            string fromSql = grid.QueryFromSQL;
            for(int i = 0; i < 10; i++)
            {
                string tagName = "RELATE_COLUMNS";

                int headStart = fromSql.IndexOf( $"<{tagName}");
                if (headStart == -1) break;
				string columns=CPubVars.GetPropertyFromTag(fromSql, headStart, tagName,"columns");
                string relateID = CPubVars.GetPropertyFromTag(fromSql, headStart, tagName, "id");

                //int nEnd = fromSql.IndexOf(">", nStart+1);
                int headEnd = fromSql.IndexOf($">",headStart+1);

                int tailStart = fromSql.IndexOf($"</", headEnd + 1);
                int tailEnd = fromSql.IndexOf($">", tailStart + 1) ;
                if (tailStart == -1 || tailEnd==-1)
                {
                    throw new Exception("=\" no matching end tag");
                }

                 

                if (columns == "")
                {
                    throw new Exception("=\" should exist after columns tag should be specified in RELATE_COLUMNS block"); 
                }


                string[] arr = columns.Split(',');
                bool bHaveVisibleColumn = false;
                foreach(string c in arr)
                {
                    dicAllColumns.TryGetValue(c, out DataItem col);
                    if (col != null && !col.Hidden)
                    {
                            bHaveVisibleColumn=true;								
                    }
				}

                string left = fromSql.Substring(0, headStart);
                string right = "";
                if(tailEnd+1<=fromSql.Length-1) right = fromSql.Substring(tailEnd + 1, fromSql.Length-1 - (tailEnd + 1) + 1);
                if (!bHaveVisibleColumn)
                { 
                    
                    fromSql = left + right;
                    if (relateID != "")
                    {
                        foreach (var kp in dicAllColumns)
                        {
                            if (kp.Value.SqlFldOnHiddenRelateColumn != null)
                            {

                                if (kp.Value.SqlFldOnHiddenRelateColumn.ContainsKey(relateID))
                                {
                                    kp.Value.SqlFld = kp.Value.SqlFldOnHiddenRelateColumn[relateID];
                                }
                            }
                        }
                    
                    }
                   
				}
                else
                { 
                    string mid=fromSql.Substring(headEnd+1,tailStart-1-(headEnd+1)+1); 
                    fromSql = left+mid+right;
				
				}
                if (!this.NotQueryHideColumn)
                {
                    throw new Exception("NotQueryHideColumn should be true when using <RELATE_COLUMNS:> blocks");
                }


				
				
			}
            grid.QueryFromSQL = fromSql;

               
			await OnQueryConditionGot(cmd);

            GroupCondiResult getGroupCondi(Dictionary<string, DataItem> _dataItems)
            {
                string andOr = "and";
                if (_dataItems is QueryDataItemGroup group)
                {
                    andOr = group.AndOr;
                }
                GroupCondiResult curGrpRes = new GroupCondiResult();
                andOr = $" {andOr} ";
                bool bHavEmptyValue = false;
                foreach (KeyValuePair<string, DataItem> kp in _dataItems)
                {
                    DataItem dataItem = kp.Value;
                    if (dataItem.SubGroup != null)
                    {
                        GroupCondiResult subGrpRes = getGroupCondi(dataItem.SubGroup);
                        if (subGrpRes.Condi != "")
                        {
                            if (curGrpRes.Condi != "") curGrpRes.Condi += andOr;
                            curGrpRes.Condi += "(" + subGrpRes.Condi + ")";
                        }
                        if (subGrpRes.HavingCondi != "")
                        {
                            if (curGrpRes.HavingCondi != "") curGrpRes.HavingCondi += andOr;
                            curGrpRes.HavingCondi += "(" + subGrpRes.HavingCondi + ")";
                        }
                        continue;
                    }

                    var fld = kp.Key;
                    if (dataItem.SqlFld != "" && !dataItem.AfterGroup)
                    {
                        fld = dataItem.SqlFld;
                    }
                    string itemValue = dataItem.Value;
                    string itemLabel = dataItem.Label;
                    if (itemValue.IsValid() && dataItem.DealQueryItem != null)
                    {
                        itemValue = dataItem.DealQueryItem(itemValue);
                    }
                    if (fld == "work_brief")
                    {

                    }
                    if (itemValue == "" && dataItem.LabelFld != "" && dataItem.Label != "" && dataItem.QueryByLabelLikeIfIdEmpty)
                    {
                        itemValue = dataItem.Label;
                        dataItem.CompareOperator = "like";
                        fld = dataItem.LabelFld;
                    }
                  
                    if (itemValue.IsValid())
                    {
                        if (!dataItem.ForQuery) continue;

                        if (dataItem.CompareOperator == "")
                        {
                            throw (new Exception("CompareOperator should not be empty"));
                        }

                        if (itemValue == ",") continue;

                        string itemCondi = "";
                        string nullCondiForReverse = "";
                        bool bCommaArround = false;
                        if (itemValue.StartsWith(",") && itemValue.EndsWith(","))
                        {
                            bCommaArround = true;
                            
                            itemValue = itemValue.Substring(1, itemValue.Length - 2);
                        }
                        string[] arr = itemValue.Split(',');
                        if (kp.Key == "type")
                        {

                        }
                        if (bCommaArround)
                        {
                            List<string> lst = new List<string>();
                            foreach (var ss in arr)
                            {
                                lst.Add("," + ss + ",");
                            }
                            arr = lst.ToArray();
                        }

                        JArray lstOptions = null;
                        if (dataItem.Source != "")
                        {
                            lstOptions = (JArray)JsonConvert.DeserializeObject(dataItem.Source);
                        }
                        foreach (var value in arr)
                        {
                            if (string.IsNullOrWhiteSpace(value)) continue;

                            itemValue = value;

                            string curCondi = "";
                            if (lstOptions != null)
                            {
                                foreach (dynamic opt in lstOptions)
                                {
                                    if (string.Compare((string)opt.v, value, true) == 0)
                                    {
                                        if (opt.condition != null && opt.condition != "")
                                        {
                                            curCondi = opt.condition;
                                        }
                                    }
                                }
                            }

                            if (curCondi == "")
                            {
                                if (dataItem.CompareOperator == "=")
                                {

                                    string[] fldArr;
                                    if (fld.Contains("coalesce", StringComparison.OrdinalIgnoreCase))
                                    {
                                        fldArr = new string[1];
                                        fldArr[0] = fld;
                                    }
                                    else fldArr = fld.Split(',');
                                    if (fldArr.Length == 2)
                                    {

                                    }
                                    /*string and_or = " or ";
                                    if (fld.Contains("+"))
                                    {
                                        fldArr = fld.Split('+');
                                        and_or = " and ";
                                    }*/
                                    if (fld.StartsWith("',") && fld.EndsWith(",'"))
                                    {
                                        fldArr = new string[1];
                                        fldArr[0] = fld;
                                    }
                                    curCondi = "";
                                    foreach (string f in fldArr)
                                    {   if (itemLabel == "全部")
                                        {
                                            // 树状结构里的全部选项不需要做筛选
                                            break;
                                        }
                                        if (string.IsNullOrWhiteSpace(f)) continue;

                                        string fldCondi = "";
                                        if (itemValue == "null")
                                        {
                                            fldCondi += $" {f} is null ";
                                        }
                                        else
                                        {
                                            fldCondi = f + "='" + itemValue + "'";
                                            if (dataItem.NullEqualValue != "" && dataItem.NullEqualValue == itemValue)
                                            {
                                                fldCondi += $" or {f} is null ";
                                                fldCondi = " (" + fldCondi + ") ";
                                            }
                                            else if (dataItem.ReverseCondition)
                                            {
                                                nullCondiForReverse += $" or {f} is null ";
                                            }
                                        }

                                        if (fldCondi != "")
                                        {
                                            if (curCondi != "") curCondi += " or ";
                                            curCondi += fldCondi;
                                        }
                                    }

                                    if (fldArr.Length > 1)
                                    {
                                        curCondi = "(" + curCondi + ")";
                                    }

                                    /*
                                    if (itemValue == "null")
                                    {
                                        curCondi += $" {fld} is null ";
                                    }
                                    else
                                    {
                                        curCondi = fld + "='" + itemValue + "'";
                                        if (dataItem.NullEqualValue != "" && dataItem.NullEqualValue == itemValue)
                                        {
                                            curCondi += $" or {fld} is null ";
                                            curCondi = " (" + curCondi + ") ";
                                        }
                                    }*/

                                }
                                else if (dataItem.CompareOperator == "like" || dataItem.CompareOperator == "ilike")
                                {
                                    string[] fldArr = fld.Split(',');
                                    if (fld.StartsWith("',") && fld.EndsWith(",'"))
                                    {
                                        fldArr = new string[1];
                                        fldArr[0] = fld;
                                    }
                                    curCondi = "";
                                    foreach (string f in fldArr)
                                    {
                                        if (itemLabel != "全部")
                                        {
                                            if (string.IsNullOrWhiteSpace(f)) continue;
                                            if (curCondi != "") curCondi += " or ";
                                            // curCondi += f + " ilike '%" + dataItem.LikeWrapper + itemValue + dataItem.LikeWrapper + "%'";

                                            string fldValue = (dataItem.LikeWrapper + itemValue + dataItem.LikeWrapper).ToLower();
                                            if (dataItem.UseFlexLikeQuery)
                                            {
												curCondi += $" {f} {dataItem.LikeString} '%{CPubVars.GetFlexLikeStr(fldValue)}%' ";
											}
                                            else
                                                curCondi += $" position('{fldValue}' in lower({f}))>0 ";

                                            if (dataItem.ReverseCondition)
                                            {
                                                nullCondiForReverse += $" or {f} is null ";
                                            }
                                        }
                                    }
                                    if(curCondi!="")
                                       curCondi = "(" + curCondi + ")";


                                }
                                else if (dataItem.CompareOperator == "not like" || dataItem.CompareOperator == "not ilike")
                                {
                                    string[] fldArr = fld.Split(',');
                                    if (fld.StartsWith("',") && fld.EndsWith(",'"))
                                    {
                                        fldArr = new string[1];
                                        fldArr[0] = fld;
                                    }
                                    curCondi = "";
                                    foreach (string f in fldArr)
                                    {
                                        if (string.IsNullOrWhiteSpace(f)) continue;
                                        if (curCondi != "") curCondi += " or ";
                                        //curCondi += $"({f} is null or {f} not ilike '%{dataItem.LikeWrapper}{itemValue}{dataItem.LikeWrapper}%')";
										string fldValue = (dataItem.LikeWrapper + itemValue + dataItem.LikeWrapper).ToLower();
										curCondi += $" coalesce(position('{fldValue}' in lower({f}) ),0)=0 ";

									}
									curCondi = "(" + curCondi + ")";

                                }
                                else if (",>=,<=,>,<,".Contains("," + dataItem.CompareOperator + ","))
                                {
                                    curCondi = fld + " >='" + itemValue + "'";

                                    string[] fldArr = fld.Split('+');
									/*if (fld.StartsWith("',") && fld.EndsWith(",'"))
                                    {
                                        fldArr = new string[1];
                                        fldArr[0] = fld;
                                    }*/
									string[] fldArrOr;
									if (fld.Contains("coalesce", StringComparison.OrdinalIgnoreCase))
									{
										fldArrOr = new string[1];
										fldArrOr[0] = fld;
									}
									else fldArrOr = fld.Split(',');
 
									curCondi = "";
									if (fldArrOr.Length > 1)
                                    {
										foreach (string f in fldArrOr)
										{
											if (string.IsNullOrWhiteSpace(f)) continue;
											if (curCondi != "") curCondi += " or ";
											curCondi += $" {f} {dataItem.CompareOperator} '{itemValue}'";

										}
									}
                                    else
                                    {
										foreach (string f in fldArr)
										{
											if (string.IsNullOrWhiteSpace(f)) continue;
											if (curCondi != "") curCondi += " and ";
											curCondi += $" {f} {dataItem.CompareOperator} '{itemValue}'";

										}
									}
									
                                    
                                    curCondi = "(" + curCondi + ")";
                                }

                            }
                            if (curCondi != "")
                            {
                                if (dataItem.OtherConditionForOptions != null && dataItem.OtherConditionForOptions.ContainsKey(itemValue))
                                {
                                    string otherCondi = dataItem.OtherConditionForOptions[itemValue];
                                    if (otherCondi != "")
                                    {
                                        curCondi = "(" + curCondi + " " + otherCondi + ")";
                                    }
                                }

                                if (itemCondi != "")
                                {
                                    if (dataItem.CompareOperator == "not like" || dataItem.CompareOperator == "not ilike")
                                        itemCondi += " and ";
                                    else
                                        itemCondi += " or ";
                                }
                                itemCondi += curCondi;
                            }
                       
                        }

                        if (itemCondi != "")
                        {
                            if (arr.Length > 1)
                            {
                                itemCondi = "(" + itemCondi + ")";
                            }

                            if (dataItem.ReverseCondition && itemCondi != "")
                            {
                                if (nullCondiForReverse != "")
                                {
                                    itemCondi = $" (not {itemCondi} {nullCondiForReverse})";
                                }
                                else itemCondi = $" not {itemCondi}";
                            }

                            if (dataItem.SqlAreaToPlace != "")
                                dataItem.SqlCondition = " and " + itemCondi;
                            else
                            {
                                if (dataItem.AfterGroup)
                                {
                                    if (curGrpRes.HavingCondi != "") curGrpRes.HavingCondi += $" {andOr} ";
                                    curGrpRes.HavingCondi += itemCondi;
                                }
                                else
                                {
                                    if (curGrpRes.Condi != "") curGrpRes.Condi += andOr;
                                    curGrpRes.Condi += itemCondi;
                                }
                            }
                        }
                        

                    }
                    else
                        bHavEmptyValue = true;
                }
                //if this group is an or group, if 1 query item is empty, the group condition will be cleared.
                //if (andOr.Trim() == "or" && bHavEmptyValue) { curGrpRes.Condi = ""; curGrpRes.HavingCondi = ""; }
                return curGrpRes;
            }

            GroupCondiResult condiRes = getGroupCondi(DataItems);

            if (condiRes.Condi != "")
            {
                if (QueryConditionIsAfterAnd)
                    condiRes.Condi = " and " + condiRes.Condi;
                else condiRes.Condi = " where " + condiRes.Condi;
            } 

            if (FuncDealCondition != null)
            {
                condiRes.Condi = FuncDealCondition(condiRes.Condi);
            }

            

            string flds = "";
            string flds_inSum = "";
            Dictionary<string, string> dicFldsArea = new Dictionary<string, string>();
            Dictionary<string, string> dicFldsArea_inSum = new Dictionary<string, string>();
            string sum_flds = "", duplicate_sum_flds = "", duplicate_distinct_flds = "";
            string idColumnKey = "";
            foreach (var kp in grid.Columns)
            {
                if (kp.Value.IsIDColumn)
                {
                    idColumnKey = kp.Key;
                }
            }
            Dictionary<string, DataItem> dicDuplicateColumns = new Dictionary<string, DataItem>();
            List<string> lstAggFlds = new List<string>();
            string aggFld = "";

          
            string extractFieldNames(string input)
            { 
 


                // 正则表达式用于匹配 AS 后的别名，或直接的字段名
                string pattern = @"\bAS\b\s+(\w+)|\b(\w+)$";

                // 使用正则表达式提取别名或字段名
                MatchCollection matches = Regex.Matches(input, pattern, RegexOptions.IgnoreCase);

                HashSet<string> fieldNames = new HashSet<string>(); // 使用 HashSet 避免重复字段

                foreach (System.Text.RegularExpressions.Match match in matches)
                {
                    // match.Groups[1] 是 AS 后面的别名
                    // match.Groups[2] 是直接的字段名
                    if (match.Groups[1].Success)
                    {
                        fieldNames.Add(match.Groups[1].Value);  // AS 别名
                    }
                    else if (match.Groups[2].Success)
                    {
                        fieldNames.Add(match.Groups[2].Value);  // 直接的字段名
                    }
                }

                // 使用逗号将字段名连接
                return string.Join(",", fieldNames);

            }

            foreach (KeyValuePair<string, DataItem> kp in grid.Columns)
            {
                DataItem col = kp.Value;

                string columnFlds = "";
                string columnFlds_inSum = "";
                if (col.linkButtons == null && col.GetFromDb && col.FuncGetValueFromRowData == null)
                {

                    if (col.FuncGetSubColumns != null) await col.TryGetSubColumns();
                    if (col.SubColumns != null)
                    {
                        if (col.SubColumnsFldsSQL != "")
                        {
                            if (columnFlds != "") columnFlds += ",";
                            columnFlds += col.SubColumnsFldsSQL;
                            columnFlds_inSum = columnFlds;
                        }

                        foreach (var k in col.SubColumns)
                        {
                            if (!k.Value.GetFromDb) continue;
                            //if (k.Value.Hidden) continue;
                            if (this.NotQueryHideColumn && k.Value.Hidden && !k.Value.SelectFieldEvenHidden && !k.Value.IsIDColumn)
                            {
                                int loopCount = 0;
                                if (!IsColumnReliedByOtherColumn(ref loopCount,k.Key,grid.QueryOrderSQL, dicAllColumns))
                                {
                                    continue;
                                }
                                /*
                                if (!k.Value.HideOnLoad && k.Value.Hidden)
                                {
									bool bHaveUsableForColumn = false;
									if (k.Value.ForColumn.IsValid())
                                    {
                                        string[] arr = k.Value.ForColumn.Split(',');
                                       
                                        foreach(string fc in arr)
                                        {
                                            if (grid.Columns.ContainsKey(fc))
                                            {
												var forCol = grid.Columns[fc];
                                                if (forCol.HideOnLoad || !forCol.Hidden)
                                                {
													bHaveUsableForColumn = true; 													 
                                                }
											}											
										}										
                                    }
									if (!bHaveUsableForColumn)
									{
										continue;
									}
								}*/
                                 

                            }
                            if (col.SubColumnsFldsSQL == "" && col.FuncGetValueFromRowData == null)
                            {
                                if (columnFlds != "") columnFlds += ",";
                                string f = k.Key;
                                string sqlFld = k.Value.SqlFld;
                                if (sqlFld != "")
                                {
                                    f = sqlFld + " as " + f;
                                }

                                columnFlds += f;

                                if (k.Value.ShowSum || k.Value.ShowAvg||k.Key == idColumnKey)
                                {
                                    if (columnFlds_inSum != "") columnFlds_inSum += ",";
                                    columnFlds_inSum += f;
                                }
                            }
                            

                            if (k.Value.ShowSum)
                            {
                                if (k.Value.MightDuplicate)
                                {
                                    dicDuplicateColumns.Add(k.Key, k.Value);

                                    //if (duplicate_sum_flds != "") duplicate_sum_flds += ",";
                                    // duplicate_sum_flds += $"sum({k.Key}) as {k.Key}";

                                }
                                else
                                {
                                    if (sum_flds != "") sum_flds += ",";
                                    if (grid.PageByOverAgg)
                                    {
                                        aggFld = $"sum_{k.Key}";
                                        lstAggFlds.Add(aggFld);
                                        sum_flds += $"sum({k.Key}) over() as {aggFld}";
                                    }
                                    else
                                    {
                                        sum_flds += $"sum({k.Key}) as {k.Key}";
                                    }
                                }



                            }
                            if (kp.Value.ShowAvg)
                            {
                                if (sum_flds != "") sum_flds += ",";
                                if (grid.PageByOverAgg)
                                {
                                    aggFld = $"avg_{k.Key}";
                                    lstAggFlds.Add(aggFld);
                                    sum_flds += $"avg({k.Key}) over() as {aggFld}";
                                }
                                else
                                {
                                    sum_flds += $"avg({k.Key}) as {k.Key}";
                                }
                            }
                        }
                    }
                    else
                    {
                        if (kp.Key.Contains("rebate"))
                        {

                        }
						//if (col.Hidden) continue;
						if (this.NotQueryHideColumn && col.Hidden && !col.SelectFieldEvenHidden && !col.IsIDColumn)
                        {
                            int loopCount = 0;
                            if (!IsColumnReliedByOtherColumn(ref loopCount,kp.Key,grid.QueryOrderSQL, dicAllColumns))
                            {
                                continue;
                            }
                            /*
                            bool bHaveUsableForColumn = false;
							if (col.ForColumn.IsValid())
							{
								string[] arr = col.ForColumn.Split(',');

								foreach (string fc in arr)
								{
									if (grid.Columns.ContainsKey(fc))
									{
										var forCol = grid.Columns[fc];
										if (col.HideOnLoad || !forCol.Hidden)
										{
											bHaveUsableForColumn = true;
										}
									}
								}
							}
							if (!bHaveUsableForColumn)
							{
								continue;
							}*/
                        }

                        if (columnFlds != "") columnFlds += ",";
                        string f = kp.Key;

                        if (kp.Value.ShowSum)
                        {
                            if (kp.Value.MightDuplicate)
                            {
                                //  if (duplicate_sum_flds != "") duplicate_sum_flds += ",";
                                //  duplicate_sum_flds += $"sum({kp.Key}) as {kp.Key}";
                                dicDuplicateColumns.Add(kp.Key, kp.Value);
                            }
                            else
                            {
                                if (sum_flds != "") sum_flds += ",";
                                if (grid.PageByOverAgg)
                                {
                                    aggFld = $"sum_{f}";
                                    lstAggFlds.Add(aggFld);
                                    sum_flds += $"sum({f}) over() as {aggFld}";
                                }
                                else
                                {
                                    sum_flds += $"sum({f}) as {f}";
                                }
                            }


                        }
                        if (kp.Value.ShowAvg)
                        {
                            if (sum_flds != "") sum_flds += ",";
                            if (grid.PageByOverAgg)
                            {
                                aggFld = $"avg_{f}";
                                lstAggFlds.Add(aggFld);
                                sum_flds += $"avg({f}) over() as {aggFld}";
                            }
                            else
                            {
                                sum_flds += $"avg({f}) as {f}";
                            }
                        }
                        string sqlFld = col.SqlFld;
                        if (sqlFld.StartsWith("~VAR_"))
                        {
                            foreach (var kk in this.SQLVariables)
                            {
                                sqlFld = sqlFld.Replace("~VAR_" + kk.Key, kk.Value);
                            }

                        }
                        if (col.SqlFld != "")
                            f = col.SqlFld + " as " + f;
                        columnFlds += f;

                        if (kp.Value.ShowSum || kp.Value.ShowAvg||kp.Key==idColumnKey)
                        {
                            if (columnFlds_inSum != "") columnFlds_inSum += ",";
                            columnFlds_inSum += f;
                        }
                    }
                    if (columnFlds != "")
                    {
                        if (col.SqlAreaToPlace != "")
                        {
                            string areaContent = "";
                            if (dicFldsArea.ContainsKey(col.SqlAreaToPlace))
                            {
                                areaContent = dicFldsArea[col.SqlAreaToPlace];
                                areaContent += ",";
                            }
                            areaContent += columnFlds;
                            dicFldsArea[col.SqlAreaToPlace] = areaContent;
                             
                            string columnKeys = extractFieldNames(columnFlds); 
                            if (flds != "") flds += ",";
                            flds += columnKeys;
                            

                        }
                        else
                        {
                            if (flds != "") flds += ",";
                            flds += columnFlds;
                        }
                    }
                    if (columnFlds_inSum != "")
                    {
                        if (col.SqlAreaToPlace != "")
                        {
                            string areaContent = "";
                            if (dicFldsArea_inSum.ContainsKey(col.SqlAreaToPlace))
                            {
                                areaContent = dicFldsArea_inSum[col.SqlAreaToPlace];
                                areaContent += ",";
                            }
                            areaContent += columnFlds_inSum;
                            dicFldsArea_inSum[col.SqlAreaToPlace] = areaContent;

                            string columnKeys = extractFieldNames(columnFlds_inSum);
                            if (flds_inSum != "") flds_inSum += ",";
                            flds_inSum += columnKeys;

                        }
                        else
                        {
                            if (flds_inSum != "") flds_inSum += ",";
                            flds_inSum += columnFlds_inSum;
                        }
                    }                   

                }

            }

            string orderSQL = grid.QueryOrderSQL;

            string sortColumn = GetParamFromRequest(request, postData, "sortColumn");
            string sortDirection = GetParamFromRequest(request, postData, "sortDirection");
            if (!string.IsNullOrEmpty(sortColumn))
            {
                void getOrderSQLfromCol(DataItem col)
                {
                    if (col.SortFld != "") sortColumn = col.SortFld;
                    //else sortColumn = colName;

                    //else if (col.SqlFld != "") sortColumn = col.SqlFld;
                    if (col.IsChinese)
                        sortColumn += " collate \"zh_CN\"";
                    if (sortDirection == "desc") sortColumn += " desc";
                    string[] arr = orderSQL.Trim().Split("order by");
                    if (arr.Length == 2) orderSQL = " order by " + sortColumn + "," + arr[1];
                    else orderSQL = " order by " + sortColumn;
                }
                var g = Grids.First().Value;
                g.Columns.TryGetValue(sortColumn, out DataItem col);
                if (col != null)
                {
                    getOrderSQLfromCol(col);
                }
                else
                {
                    foreach (var kp in g.Columns)
                    {
                        if (kp.Value.Sortable && kp.Value.SubColumns != null)
                        {
                            kp.Value.SubColumns.TryGetValue(sortColumn, out DataItem subCol);
                            if (subCol != null)
                            {
                                getOrderSQLfromCol(subCol);
                                break;
                            }
                        }
                    }
                }
            }
            await OnQueryConditionStrGot(condiRes.Condi, cmd);
  
            List<string> lstFromSQL = new List<string>();
            if (grid.QueryFromSQL != "") lstFromSQL.Add(grid.QueryFromSQL);
            if (grid.QueryFromSQL2 != "") lstFromSQL.Add(grid.QueryFromSQL2);
        
                /* if (false && dicFldsArea.Count > 0)
                 {
                     if (flds != "") flds += ",";
                     flds += "*";
                     foreach (var kp in grid.Columns)
                     {
                         var key = kp.Key;
                         var col = kp.Value;
                         if (col.SqlAreaToPlace == "" && (col.SqlFld == "" || col.SqlFld == key))
                         {
                             flds = "," + flds;
                             flds = flds.Replace("," + key + ",", ",");
                             flds = flds.Substring(1, flds.Length - 1);
                         }
                         if (col.SubColumns != null && col.SubColumns.Count > 0)
                         {
                             foreach (var kp1 in col.SubColumns)
                             {
                                 key = kp.Key;
                                 col = kp1.Value;
                                 if (col.SqlAreaToPlace == "" && (col.SqlFld == "" || col.SqlFld == key))
                                 {
                                     flds = "," + flds;
                                     flds = flds.Replace("," + key + ",", ",");
                                     flds = flds.Substring(1, flds.Length - 1);
                                 }
                             }
                         }
                     }
                 }*/
            string get1SQL(string flds, Dictionary<string, string> dicFldsArea)
            {
                string sqlUnion = "";
                foreach (var fromSQL in lstFromSQL)
                {
                    string sql = "";
                    if (grid.DistictFields) flds = " distinct " + flds;
                    if (fromSQL.Contains("~QUERY_CONDITION"))
                    {
                        sql = "select " + flds + " " + fromSQL.Replace("~QUERY_CONDITION", condiRes.Condi.Replace("where ", "", StringComparison.OrdinalIgnoreCase));

                    }
                    else
                    {
                        sql = "select " + flds + " " + fromSQL;

                        if (grid.AutoAppendQueryCondition)
                        {
                            if (fromSQL.Contains("where",StringComparison.OrdinalIgnoreCase))
                            {
                                sql += " " + condiRes.Condi.Replace(" where ", " and ");

                            }
                            else
                            {
                                sql += condiRes.Condi;

                            }
                            sql = GetRealSQL(sql, company_id);
                        }


                    }



                    sql += " " + grid.QueryGroupBySQL;
                    if (grid.QueryGroupBySQL != "" && condiRes.HavingCondi != "")
                    {
                        sql += " having " + condiRes.HavingCondi;
                    }
                    sql = sql.Replace("~SQL_VARIABLE1", SQLVariable1);
                    sql = sql.Replace("~SQL_VARIABLE2", SQLVariable2);
                    sql = sql.Replace("~SQL_VARIABLE3", SQLVariable3);
                    sql = sql.Replace("~SQL_VARIABLE4", SQLVariable4);

                    foreach (var kp in SQLVariables)
                    {
                        sql = sql.Replace($"~VAR_{kp.Key}", kp.Value);
                        orderSQL = orderSQL.Replace($"~VAR_{kp.Key}", kp.Value);
                    }

                    foreach (var kp in DataItems)
                    {
                        if (kp.Value.SqlAreaToPlace != "" && kp.Value.SqlCondition != "")
                        {
                            string[] arr = kp.Value.SqlAreaToPlace.Split(',');
                            foreach(string area in arr)
                            {
								sql = sql.Replace("~AREA_" + area, "~AREA_" + area + " " + kp.Value.SqlCondition);
							}							
                        }
                    }
                    foreach (var kp in DataItems)
                    {
                        if (kp.Value.SqlAreaToPlace != "")
                        {
							string[] arr = kp.Value.SqlAreaToPlace.Split(',');
							foreach (string area in arr)
							{
								sql = sql.Replace("~AREA_" + area, "");
							}
                        }
                    }

                    foreach (var kp in dicFldsArea)
                    {
                        sql = sql.Replace("~AREA_" + kp.Key, kp.Value);
                    }

                    sql = sql.Replace("~COMPANY_ID", company_id, StringComparison.OrdinalIgnoreCase);
                    sql = sql.Replace("~OperID", OperID, StringComparison.OrdinalIgnoreCase);
              

                  //  o = null;
                    //sql = GetRealSQL(sql, companyID);
                    if (sqlUnion != "") sqlUnion += " union ";
                    sqlUnion += sql;
                }
                if (lstFromSQL.Count > 1)
                {
                    sqlUnion = $"select * from ({sqlUnion}) union_sql";
                }
                return sqlUnion;
            }
            
            string sql = get1SQL(flds, dicFldsArea);

            string sql_inSum = "";
            if(bGetRowsCount && !grid.PageByOverAgg) sql_inSum = get1SQL(flds_inSum, dicFldsArea_inSum);
            sql = sql.Replace("<DelForSum>", "");
            sql = sql.Replace("</DelForSum>", "");

			if (addDataItems != null)//这里需要删掉临时添加的dataItem,否则会在导出时影响第二页的查询。 一定要在get1sql后面执行，否则dataItems里的新增的时间查询项都删除了，就无法用sqlCondition替换AREA_了
			{
				foreach (var k in addDataItems)
				{
					if (DataItems.ContainsKey(k.Key)) DataItems.Remove(k.Key);
				}
			}


			foreach (var item in lstNeedResetDataItems)
			{
				item.ForQuery = true;
			}

			int rowsCount = 0;


            int pageSize = endRow - startRow + 1;
           // string origSQL = sql;

            //sql = sql + $"  {orderSQL}  limit " + pageSize.ToString() + " offset " + startRow.ToString();
            sql = sql + $"  {orderSQL}  ";
            SQLQueue QQ = new SQLQueue(cmd);
            if (pageSize > 1000)
            {
                MyLogger.LogMsg($"ip: {ip}| limit pageSize: {pageSize.ToString()} offset: {startRow.ToString()}| in GetRecordFromQuerySQL,page size{pageSize},sql:{sql}", company_id);
            }
            if (!IsExportingExcel && pageSize > 3000)
            {
                return new JsonResult(new { result = "Error", msg = "记录获取太多" });
            }
            if (bGetRowsCount)
            {
                if (sum_flds != "") sum_flds = "," + sum_flds;

                if (grid.PageByOverAgg)
                {
                    sum_flds = ",count(0) over() as row_count" + sum_flds;

                    int n = sql.IndexOf("from", 0, StringComparison.OrdinalIgnoreCase);
                    string l = sql.Substring(n - 1, 1);
                    string r = sql.Substring(n + 4, 1);
                    if ("\n ".Contains(l) && "\n ".Contains(r))
                    {
                        n = n - 1;
                    }


                    if (n > 0)
                    {
                        sql = $"select *{sum_flds} from ({sql}) t";
                    }
                }
                else
                { 
                    sql_inSum = removeTagContent(sql_inSum, "DelForSum");
                    string sqlSum = $"select count(*) as total_records {sum_flds} from ({sql_inSum}) t";
                    QQ.Enqueue("sumResult", sqlSum);
                }
                if (dicDuplicateColumns.Count > 0 && idColumnKey != "")
                {
                    foreach (var k in dicDuplicateColumns)
                    {

                        if (!k.Value.Hidden)
                        {
                            if (duplicate_sum_flds != "") duplicate_sum_flds += ",";
                            duplicate_sum_flds += $"sum({k.Key}) as {k.Key}";
                            if (duplicate_distinct_flds != "") duplicate_distinct_flds += ",";
                            duplicate_distinct_flds += $"{k.Key}";
                        }
                    }
                    if (duplicate_sum_flds != "")
                    {
                        string origSqlForSum=sql_inSum;
                        if (grid.PageByOverAgg)
                        {
                            origSqlForSum = sql;
                        }
                        string sqlSum = $"select {duplicate_sum_flds} from (select distinct {idColumnKey},{duplicate_distinct_flds} from ({origSqlForSum}) t) t";
                        QQ.Enqueue("distinctSumResult", sqlSum);
                    }

                }
            }

            if (EnableBigDataMode && startRow >= 0)
            {
                string s = orderSQL.Replace("order by ", "", StringComparison.OrdinalIgnoreCase);
                string[] arr = s.Split(',');
                string newOrderSQL = "";
                foreach (var f in arr)
                {
                    string fld = f;
                    var arr1 = fld.Split('.');
                    if (arr1.Length == 2)
                    {
                        fld = arr1[1];
                    }
                    if (newOrderSQL != "") newOrderSQL += ",";
                    newOrderSQL += fld;
                }
                newOrderSQL = "order by " + newOrderSQL;

                sql = @$"select * from 
                        (
                                select *,row_number() over({newOrderSQL}) sys_row_number from ({sql}) t  
                        ) t where sys_row_number>={startRow + 1} order by sys_row_number limit {pageSize};";
            }
            else
            {
                sql = sql + $" limit " + pageSize.ToString() + " offset " + startRow.ToString();
            }

            QQ.Enqueue("records", sql);


            var recordsResult = await PageBaseModel.GetRecordsFromQQ(QQ, cmd, startRow, grid);
            Dictionary<int, Dictionary<string, string>> rows = new Dictionary<int, Dictionary<string, string>>();
            Dictionary<string, string> sumResult = new Dictionary<string, string>();


            foreach (var k in recordsResult)
            {
                if (k.Key == "records")
                {
                    rows = k.Value;
                    Dictionary<string, string> row0 = null;
                    Dictionary<string, string> preRow = null;
                    bool canHideDuplicateCells = false;
                    if (this.DataItems.ContainsKey("sys_hide_duplicate_cells"))
                    {
                        DataItem dtHideDuplicate = this.DataItems["sys_hide_duplicate_cells"];
                        if (dtHideDuplicate.Value.ToLower() == "true") canHideDuplicateCells = true;
                    }

                    foreach (var kRow in rows)
                    {
                        var row = kRow.Value;
                        if (row0 == null) row0 = row;
                        foreach (var kp in grid.Columns)
                        {
                            if (kp.Value.FuncGetValueFromRowData != null && !(this.NotQueryHideColumn && kp.Value.Hidden))
                            {
                                row[kp.Key] = kp.Value.FuncGetValueFromRowData(kp.Key, row);
                            }
                            if (canHideDuplicateCells && kp.Value.HideDuplicateCells && kp.Value.MightDuplicate && idColumnKey != "" && idColumnKey != kp.Key && preRow != null)
                            {
                                if (row[idColumnKey] == preRow[idColumnKey])
                                {
                                    row[kp.Key] = "";
                                }
                            }
                        }

                        preRow = row;
                    }
                    if (bGetRowsCount && grid.PageByOverAgg && row0 != null)
                    {
                        rowsCount = Convert.ToInt32(row0["row_count"]);

                        foreach (string fld in lstAggFlds)
                        {
                            string s = "";
                            string[] arr = fld.Split("_", 2);
                            if (arr.Length == 2) s = arr[1];

                            if (s != "")
                            {
                                sumResult[s] = CPubVars.FormatMoney(row0[fld.ToLower()], 2, false);
                            }
                        }
                    }
                }
                else if (k.Key == "sumResult")
                {
                    Dictionary<string, string> dic = null;
                    foreach (var k1 in k.Value)
                    {
                        dic = k1.Value;
                    }
                    foreach (var k1 in dic)
                    {
                        var value = k1.Value;

                        if (k1.Key == "total_records")
                        {
                            if (value != "")
                                rowsCount = Convert.ToInt32(value);
                        }
                        else
                        {
                            sumResult[k1.Key] = CPubVars.FormatMoney(value, 2, false);
                        }

                    }
                }
                else if (k.Key == "distinctSumResult")
                {
                    Dictionary<string, string> dic = null;
                    foreach (var k1 in k.Value)
                    {
                        dic = k1.Value;
                    }
                    foreach (var k1 in dic)
                    {
                        var value = k1.Value;
                        sumResult[k1.Key] = CPubVars.FormatMoney(value, 2, false);
                    }
                }

            }
            if (bGetRowsCount && sumResult.Count > 0)
            {
                Dictionary<string, string> dicSumValusByFun = new Dictionary<string, string>();
                foreach (KeyValuePair<string, DataItem> kp in grid.Columns)
                {
                    DataItem col = kp.Value;
                    
                    if (col.FuncGetSumValue != null && !(this.NotQueryHideColumn && col.Hidden))
                    {
                        string sumValue = col.FuncGetSumValue(sumResult);
                        dicSumValusByFun[kp.Key] = sumValue;
                    }
                }
                foreach (var k in dicSumValusByFun)
                {
                    sumResult[k.Key] = k.Value;
                }
            }

            if (bGetRowsCount && grid.AllowAddRow) rowsCount += 18;
            var data = new { result = "OK", rows, rowsCount, sumResult };
            Dictionary<string, dynamic> pageValues = new Dictionary<string, dynamic>();
            foreach (var kp in DataItems)
            {
                var item = kp.Value;
                if (item.AutoRemember)
                {
                    pageValues[kp.Key] = new { value = item.Value, label = item.Label };
                }
            }

            if (pageValues.Count > 0)
            {
                string pageName = this.GetPageName();
                Dictionary<string, dynamic> optionToRemember = new Dictionary<string, dynamic>();
                optionToRemember["page_" + pageName] = pageValues;

                string opt = JsonConvert.SerializeObject(optionToRemember);
                cmd.CommandText = $"insert into options_remembered (company_id,oper_id,options) values ({companyID},{operID},'{opt}'::jsonb) on conflict(company_id,oper_id) do update set options=jsonb_merge(options_remembered.options,'{opt}'::jsonb);";
                await cmd.ExecuteNonQueryAsync();

            }

            return await DealQueriedRecords(data, postData);

        }
        public virtual async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            return "";
        }
        public async Task<JsonResult> DeleteRecords(dynamic data, CMySbCommand cmd, string table_name, Func<Task> cbDone = null)
        {
            string gridID = data.gridID;
            string rowIDs = data.rowIDs;
            string operKey = data.operKey;
            cmd.ActiveDatabase = "";
            Security.GetInfoFromOperKey(operKey, out string companyID);


            company_id = companyID;
            string err = await CheckBeforeDeleteRecords(rowIDs);
            if (err != "")
            {
                return new JsonResult(new { result = "Error", msg = err });
            }
            QueryGrid grid = Grids[gridID];
            if (table_name != grid.TableName)
            {
                return new JsonResult(new { result = "Error", msg = "删除的表名不对" });
            }
            string idFld = grid.IdColumn;
            if (grid.Columns[grid.IdColumn].SqlFld != "") idFld = grid.Columns[grid.IdColumn].SqlFld;
            var arr = idFld.Split('.');
            if (arr.Length == 2) idFld = arr[1];
            arr = rowIDs.Split(',');
            rowIDs = "";
            foreach (var s in arr)
            {
                if (rowIDs != "") rowIDs += ",";
                rowIDs += "'" + s + "'";
            }
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            cmd.CommandText = $"delete from {grid.TableName} where company_id = {companyID} and {idFld} in ({rowIDs});";
            await cmd.ExecuteNonQueryAsync();
            if (cbDone != null)
            {
                await cbDone();
            }
            tran.Commit();
            return new JsonResult(new { result = "OK", msg = "" });

        }

        // public virtual async Task OnDataItemsGotFromSheet(CMySbCommand cmd)
        // {

        // }
        public virtual async Task OnQueryConditionGot(CMySbCommand cmd)
        {

        }
        public virtual async Task OnQueryConditionStrGot(string condi, CMySbCommand cmd)
        {

        }
        public override async Task SetPageCommonRights(CMySbCommand cmd)
        {
            bool seeInPrice = false;
			bool seeProfit = false;
			if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
				if (operRights?.delicacy?.seeProfit?.value is not null)
					seeProfit = ((string)operRights.delicacy.seeProfit.value).ToLower() == "true";
			}
            foreach (var kp in this.Grids)
            {
                foreach (var k in kp.Value.Columns)
                {
                    if (k.Value.BuyPriceRelated && !seeInPrice)
                    {
                        k.Value.HideOnLoad = k.Value.Hidden = true;
                    }
					if (k.Value.ProfitRelated && (!seeProfit||!seeInPrice))
					{
						k.Value.HideOnLoad = k.Value.Hidden = true;
					}
				}
            }
        }
        public override async Task GetJavaScripts(CMySbCommand cmd, bool bGetFldAreaCtrls = false)
        {
            //  await OnDataItemsGotFromSheet(cmd);
            foreach(var kp in this.DataItems)
            {
                kp.Value.InQueryPage = true;
            }

            await GetScriptsForDataItems(cmd, bGetFldAreaCtrls, false);

            string funGetQueryValues = "window.funGetQueryValues=function(){var formFlds = {operKey:'~operKey'};  ~m_getDataItemsScript  return formFlds;};";
            Dictionary<string, string> vars = new Dictionary<string, string>();
            vars["m_getDataItemsScript "] = m_getDataItemsScript;
            vars["operKey"] = OperKey;

            funGetQueryValues = PageBaseModel.GetStringWithVar(funGetQueryValues, vars);
            m_createGridScript += funGetQueryValues;
            int n = 0;
            foreach (KeyValuePair<string, QueryGrid> kp in Grids)
            {
                if (n == 0) m_createGridScript += "window.m_curGrid='" + kp.Key + "';\r\n";
                string scriptCreateGrid = "";
                scriptCreateGrid = await GetGridScript(kp.Key);
                m_createGridScript += scriptCreateGrid;
                n++;
            }

            m_createGridScript += @$"
window.QueryData = function (event,callback) {{ 
   
    window.gridRowIDsToDelete=null
    if(window.beforeQuery){{
        window.beforeQuery();
    }}
                          
 
    var fun=window['Query_'+ window.m_curGrid]; 
    if(fun) fun(callback); 
    window.changedItems={{}}
                           
}}; ";

            string msgHead = this.GetType().FullName.Replace("Model", "").Split(".").Last();
            string firstGridID = Grids.First().Key;
            m_createGridScript += @$"
                window.ExportExcel = function (event,callback) {{ 
                        if(window.beforeQuery){{
                            window.beforeQuery();
                        }}
                           
                        var queryItems=funGetQueryValues();
                        if( window.queryItems.errMsg){{
                            return;
                        }}

                        $.ajax({{
                            url: ""/AppApi/Main/GetProgressToShow?operKey="" + window.g_operKey,
                            method: ""GET"",
                            contentType: ""application/json;charset=UTF-8"",
                            data: {{

                            }},
                            success: (res) => {{
                                if (res.result == 'OK') {{

                                    if (res.progress) {{
                                        var pg = res.progress
                                        var arr = pg.split('|')
                                        var title = arr[0]
                                        var value = arr[1]
                                        bw.toast('有一个正在进行的操作:'+title)
                                        return
                                    }}
                                    
                                    var mainWin= window.parent
                                    if(mainWin.parent) mainWin=mainWin.parent
                                    mainWin.showProgress(true)

                                    var s=window.g_pageSetting;
                                    if(s){{
                                        s=encodeURIComponent(JSON.stringify(s))  
                                    }}
                                    var gridSetting={{}}
                                     if(window.g_pageSetting && window.g_pageSetting.grid){{
                                        
                                        window.g_pageSetting.grid.forEach(col=>{{
                                            gridSetting[col.datafield]={{hidden:col.hidden}}                     
                                        }}) 
                                     }}
                        
    	                            var getParams={{gridID:'{firstGridID}',startRow:0,endRow:50000,GetRowsCount:true,gridSetting}};
                         
                                    queryItems.sortColumn=window.sortColumn||'';
                                    queryItems.sortDirection=window.sortDirection||''; 

                                    $.extend(getParams, queryItems)
                                    getParams=encodeURIComponent(JSON.stringify(getParams))  

                                    var columnsWidth=window.g_columnsWidth;
                                    if(s){{
                                        columnsWidth=encodeURIComponent(JSON.stringify(columnsWidth))  
                                    }}
                                    var postParams={{pageSetting: s,params: getParams,columnsWidth:columnsWidth}}
                                    var queryStr='operKey='+window.g_operKey;
                                    // for(var k in getParams){{
                                            // var value=getParams[k];
                                            // if(queryStr!='') queryStr+='&';
                                        //  queryStr+=k+'='+value;
                                    //}}

                                    //window.location.href = `../api/{msgHead}/ExportExcel?${{queryStr}}`;
                                    var url = `{DefaultUrlPrefix}{msgHead}/ExportExcel?${{queryStr}}`;
                                    if(window.ExportServerUri){{
                                        url = `${{window.ExportServerUri}}/api/{msgHead}/ExportExcel?${{queryStr}}`;

                                    }}
                                    var downLoadFile = function (url,postParams) {{
                                        var config = $.extend(true, {{ method: 'post' }}, postParams);
                                        //var $iframe = $('<iframe id=""down-file-iframe"" enctype=""multipart/form-data"" />');
                       
                                        var $form = $('<form  method=""' + config.method + '"" />');
                                        $form.attr('action', url);
                                        for (var key in postParams) {{
                                            $form.append('<input type=""hidden"" name=""' + key + '"" value=""' + postParams[key] + '"" />');
                                        }}
                      
                                        $(document.body).append($form);
                                        $form[0].submit();
                                        $form[0].remove();
                      
                                    }}
                                    downLoadFile(url,postParams) 
                                    
                                   
                                  


                                }} else {{
                                    //bw.toast(res.msg);
                                }}
                            }},
                            error: (res) => {{
                                // bw.toast('网络错误');
                            }}
                        }});


           

                }};

            ";

            foreach (KeyValuePair<string, DataItem> kp in DataItems)
            {
                DataItem dataItem = kp.Value;

                if (dataItem.QueryOnChange || dataItem.JSDealItemOnSelect != null)
                {
                    string queryData = "";
                    if (dataItem.QueryOnChange) queryData = " window.QueryData();";
                    if (dataItem.CtrlType == "jqxTree")//|| dataItem.CtrlType == "jqxDateTimeInput"
                    {
                        string script = @$" $('#{kp.Key}').on('select', function (event) {{ 
                            {dataItem.JSDealItemOnSelect}   
    	                    {queryData}
                         }}); 
                         ";
                        m_createGridScript += script;
                    }
                    else
                    {

                        string script = @$"
                        $('#{kp.Key}').on('change', function (event) {{ 
                            {dataItem.JSDealItemOnSelect}   
    	                    {queryData}
                        }});
                         ";
                        m_createGridScript += script;
                    }
                }
            }
        }
        private async Task<string> GetGridScript(string gridID)
        {
            string scriptCreateGrid = "";
            QueryGrid grid = Grids[gridID];

            Dictionary<string, string> varsG = new Dictionary<string, string>();
            string sColumns = @"{
                            text: '', sortable: false, filterable: false, editable: false, pinned: true,
                            groupable: false, draggable: false, resizable: false,
                            datafield: '', columntype: 'number', width: 40, cellsalign:'center', align: 'center',
                            cellclassname: fixColCss,
                            cellsrenderer: function(row, column, value) {
                                 return '<div style=""display:flex;justify-content:center;align-items:center;height:100%"">' + (value + 1) + '</div>';
                            },
                            renderer: leftTopCellRenderer
             }";
            //string checkFunc = "";
            varsG["checkRow"] = "";


            string scriptFunc = "";
            if (grid.JsFuncGetRowKeyForCheck == "" && grid.IdColumn != "") grid.JsFuncGetRowKeyForCheck = $"function(row){{return row.{grid.IdColumn}}}";
            scriptFunc = @$"window.funcGetRowKeyForCheck={grid.JsFuncGetRowKeyForCheck}
window.funcGetRowKeyColumn= function(){{return '{grid.IdColumn}'}}
";
            if (grid.SortColumn != "")
            {
                scriptCreateGrid += $"window.sortColumn='{grid.SortColumn}';";
				scriptCreateGrid += $"window.sortDirection='{grid.SortDirection}';";
			}

            string linkColumns = ",";
            foreach (var kp in grid.Columns)
            {
                if (kp.Value.Linkable)
                {
                    linkColumns += kp.Key + ",";
                }
            }
            if (grid.HasCheck)
            {
                sColumns += @$",{{
                            text: '', editable: false,sortable:false,pinned:true,
                            groupable: false,  resizable: false,
                            datafield: 'sys_check', width:50, cellsalign:'center', align: 'center',    
                            renderer:function(){{
                                 var rows=$('#{gridID}').jqxGrid('getrows');
                                 var checkedCount=0;
                                 rows.forEach((row)=>{{ 
                                    var row = window.g_checkedRows[window.funcGetRowKeyForCheck(row)];
                                    if(row) checkedCount++;   
                                 }});
                                var checked='';
                                if(checkedCount==rows.length) checked='checked';
                                return `<div style=""display:flex;height:100%;align-items:center;"" ><input id='sys_checkColumn' onclick='onGridColumnCheck(event)' style='width:18px;height:18px;z-index:999999;' type='checkbox' class='4magic-checkbox' /><label id='lblCheckedCount'></label></div>`;
                            }},
                            cellsrenderer: function(index, datafield, value, defaultvalue, column, rowdata) {{
                            var id=window.funcGetRowKeyForCheck(rowdata); 
                            var checked='';
                            if(id){{
                               var checkRow = window.g_checkedRows[id];
                               if(checkRow) checked='checked';
                               
                            }} if(id){{
                                 // 有id才渲染勾选框
var checkRow = window.g_checkedRows[id];
                               if(checkRow) checked='checked';
                                 var checkIndex=window.checkedIds.indexOf(id)
                                 if(checkIndex>=0 && checkRow) {{checked='checked';checkIndex++}}
                                 else checkIndex=''
                                 return `<div style=""display:flex;height:100%;align-items:center;""><input id='check_${{id}}' type='checkbox' style='width:18px;height:18px;' class='magic11-checkbox' onclick='onGridRowCheck(${{index}},event)' ${{checked}} /><label for='22check_${{id}}'>${{checkIndex}}</label></div>`;
                               }}
                            }} 
                         }}";
                varsG["checkRow"] = @$"
                        window.g_checkedRows={{}};
                        window.g_arrCheckedRows=[]
                        window.checkedIds=[]
                        window.onGridColumnCheck=function(event){{
                                if(`{grid.JsFuncGetRowKeyForCheck}`=='') return;
                                var checked=document.all.sys_checkColumn.checked;
                                setTimeout(function(){{
                                    document.all.sys_checkColumn.checked=checked;
                                }},100);
                                $('#{gridID}').jqxGrid('clearselection',true)
                                //var rows=$('#{gridID}').jqxGrid('getrows'); 
                                var rows= window.gridData_{gridID}.localRows; 
                                if (checked) $('#{gridID}').jqxGrid({{ selectionMode: 'none' }})
                                for(var k in rows){{
                                    var row=rows[k]
                                    var rowKey=window.funcGetRowKeyForCheck(row)
                                    var keyColumn = window.funcGetRowKeyColumn();
                                    if(checked && rowKey){{
                                      window.g_checkedRows[rowKey]=row; 
                                      if(window.checkedIds.indexOf(rowKey)==-1){{
                                         window.g_arrCheckedRows.push(row); 
                                         window.checkedIds.push(rowKey)
                                      }}
                                      
                                   
                                      $('#{gridID}').jqxGrid('selectrow',row.boundindex,false); 
                                   }}
                                   else{{
                                      var index = window.checkedIds.indexOf(rowKey)
                                      if(index!=-1)
                                        window.checkedIds.splice(index,1)
                                      delete window.g_checkedRows[rowKey]; 
                                      window.g_arrCheckedRows.some((r,idx)=>{{
                                          if(r[keyColumn]===row[keyColumn]){{
                                              window.g_arrCheckedRows.splice(idx,1)
                                              return true
                                           }}
                                        }})

                                    }}
                                }}
                              
                                 document.all.lblCheckedCount.innerText = Object.keys(g_checkedRows).length;
                                 if(window.onGridCheckChanged) window.onGridCheckChanged()
                         }};

                        window.onGridRowCheck=function(rowIndex,event){{
                                if(`{grid.JsFuncGetRowKeyForCheck}`=='') return;
                              
                                var rowdata=window.gridData_{gridID}.localRows[rowIndex];
                                var rowKey=window.funcGetRowKeyForCheck(rowdata);
                                var keyColumn = window.funcGetRowKeyColumn();
                                 if(!window.checkedIds)window.checkedIds=[]
                                if(!rowKey) return;
                                var checked=!window.g_checkedRows[rowKey]
                                $('#{gridID}').jqxGrid({{ selectionMode: 'none' }})
                                if (checked){{        
                                    window.g_checkedRows[rowKey]=rowdata;
                                    window.g_arrCheckedRows.push(rowdata);
                                    window.checkedIds.push(rowKey)
                                }}
                                else{{
                                    var index = window.checkedIds.indexOf(rowKey)
                                    if(index!=-1)
                                        window.checkedIds.splice(index,1)
                                    delete window.g_checkedRows[rowKey]
                                    window.g_arrCheckedRows.some((r,idx)=>{{
                                          if(r[keyColumn]==rowdata[keyColumn]){{
                                              window.g_arrCheckedRows.splice(idx,1)
                                              return true
                                           }}
                                     }})
                                }}
                                document.all.lblCheckedCount.innerText=Object.keys(g_checkedRows).length;
                                var len =Object.keys(g_checkedRows).length;
                                if(len==0)window.checkedIds=[]
                                if(Object.keys(window.g_checkedRows).length ==1){{
                                    $('#{gridID}').jqxGrid('clearselection')
                                }}
 
                                if(checked)  
                                   $('#{gridID}').jqxGrid('selectrow',rowIndex)  
                                else $('#{gridID}').jqxGrid('unselectrow',rowIndex)  
                               
                                if(window.onGridCheckChanged) window.onGridCheckChanged()
                         }};

                     $('#{gridID}').on('cellclick', (event) => {{
                             var column=$('#{gridID}').jqxGrid('getcolumn','sys_check')
                             if(column.hidden) return
                             if('{linkColumns}'.indexOf(','+event.args.column.datafield+',')==-1){{
                                window.onGridRowCheck(event.args.rowindex,event)
                            }}                       
                           
                     }})


";



            }
            bool bHaveLinkeBtn = false;
            //grid.Columns
            ColumnJsResult colRes = new ColumnJsResult() { sColumns = sColumns };

            async Task GetColumnsJS(Dictionary<string, DataItem> columns, string groupName, ColumnJsResult colRes, bool hideTopGroupName = false)

            //async Task GetColumnsJS(Dictionary<string,DataItem> columns,string groupName,ref string sColumns, ref bool bHaveLinkeBtn,ref string columnGroups,bool hideTopGroupName=false)
            {
                foreach (KeyValuePair<string, DataItem> key in columns)
                {
                    DataItem dataItem = key.Value;
                    if (dataItem.FuncGetSubColumns != null) await dataItem.TryGetSubColumns();
                    if (dataItem.SubColumns != null)
                    {
                        await GetColumnsJS(dataItem.SubColumns, key.Key, colRes, dataItem.HideTopGroupName);
                        //if (sColumns != "") sColumns += ",";
                        // sColumns += cols;
                        if (!hideTopGroupName)
                        {
                            if (colRes.columnGroups != "") colRes.columnGroups += ",";
                            colRes.columnGroups += $"{{ text: '{dataItem.Title}', align: 'center', name: '{key.Key}'}}";
                        }
                        continue;
                    }



                    Dictionary<string, string> vars = new Dictionary<string, string>();
                    vars["datafield"] = key.Key;
                    vars["displayfield"] = ""; vars["title"] = dataItem.Title;
                    /*if (!string.IsNullOrEmpty(dataItem.Tooltip))
                    {
                        vars["title"] += $@"<div id=""{key.Key}-table-tooltip"" onmouseenter=""showTableTooltip(event,\'{dataItem.Tooltip}\')""  style=""position: relative;z-index: 1000000;cursor: help;justify-content: center; display: flex; width: 14px; height: 14px; float: right; text-align: center; font-size: 12px; font-weight: bolder; border: 1px solid #999999; border-radius: 50%; align-items: center; color: #999;"">?</div>";
                    }*/
                    if (dataItem.LabelFld != "") { vars["displayfield"] = ", displayfield: '" + dataItem.LabelFld + "'"; }
                    vars["width"] = "";
                    if (dataItem.Width != "") { vars["width"] = ", width: '" + dataItem.Width + "'"; }
                    vars["cellsalign"] = "";
                    if (dataItem.CellsAlign != "") vars["cellsalign"] = $",cellsalign:'{dataItem.CellsAlign}'"; ;
                    vars["sortable"] = ",sortable:false";
                    if (dataItem.Sortable) vars["sortable"] = $",sortable:true"; ;


                    vars["gridID"] = gridID;
                    vars["cellsrenderer"] = "";
                    if (grid.ZeroAsEmpty != "" && dataItem.ZeroAsEmpty == "")
                    {
                        dataItem.ZeroAsEmpty = grid.ZeroAsEmpty;
                    }
                    if (dataItem.Linkable || dataItem.ShowRowPercent || dataItem.ZeroAsEmpty == "true")
                    {
                        string linkStyle = "";
                        if (dataItem.Linkable) linkStyle = "cursor:pointer;color: #4499ff;";

                        string flexAlign = "";
                        if (dataItem.CellsAlign == "right") flexAlign = "justify-content:flex-end;";
                        if (dataItem.ShowRowPercent) flexAlign = "justify-content:space-between;";

                        if (dataItem.ZeroAsEmpty == "") dataItem.ZeroAsEmpty = "false";

                        vars["cellsrenderer"] = GetStringWithVar($@",cellsrenderer:function(row, columnfield, value, defaulthtml, columnproperties) {{ 
                             if({dataItem.ZeroAsEmpty} && value=='0'){{value=''}}
                        var height = $('#~gridID').jqxGrid('rowsheight')-8;
                         var percent='';
                         if({dataItem.ShowRowPercent.ToString().ToLower()}){{
                             var sum = window.source_{gridID}.sumResult.{key.Key};
                             sum=parseFloat(sum);
                             if(sum){{
                                  var v=parseFloat(value); 
                                  if(isNaN(v)) percent='0%';
                                  else percent=toMoney(v/sum*100,2)+'%';
                             }}
                             percent=`<span style=""color:#ccc;"">${{percent}}</span>`;
                         }}
                         return `<div style=""{linkStyle}margin:4px;display:flex;line-height:${{height}}px;{flexAlign}"">${{percent}}<span>${{value}}</span></div>`; 
                       }}", vars);
                    }
                    else if (dataItem.linkButtons != null && dataItem.linkButtons.Count > 0)
                    {
                        bHaveLinkeBtn = true;
                        var slbs = "";
                        foreach (var lb in dataItem.linkButtons)
                        {
                            var margin = "";
                            if (slbs != "") margin = "margin-left:10px;";
                            var slb = $@" <button style=""text-decoration:none;border:none;cursor:pointer; background:transparent;color:#44a;{margin}""  onclick=""{gridID}_rowBtnClicked(${{row}},'{lb.id}');"">{lb.text}</button>";
                            slbs += slb;
                        }
                        string cellrender_orig = $@",cellsrenderer:function(row, columnfield, value, defaulthtml, columnproperties) {{ 
                          return `<div class=""linkBtn"" style=""margin:0px;display:none; width:100%;height:100%;text-align:center;align-items:center;justify-content:center;"">{slbs}</div>`;  
                        }}";
                        vars["cellsrenderer"] = GetStringWithVar(cellrender_orig, vars);
                    }

                    if (dataItem.JSCellRender != "")
                    {
                        vars["cellsrenderer"] = ",cellsrenderer:" + dataItem.JSCellRender;
                    }


                    vars["createeditor"] = "";
                    if (dataItem.JSCreateEditor != "")
                    {
                        vars["createeditor"] = ",createeditor:" + dataItem.JSCreateEditor;
                    }

                    vars["cellbeginedit"] = "";
                    if (dataItem.JSCellBeginEdit != "")
                    {
                        vars["cellbeginedit"] = ",cellbeginedit:" + dataItem.JSCellBeginEdit;
                    }
                    vars["cellendedit"] = "";
                    if (dataItem.JSCellEndEdit != "")
                    {
                        vars["cellendedit"] = ",cellendedit:" + dataItem.JSCellEndEdit;
                    }

                    vars["initeditor"] = "";
                    if (dataItem.JSInitEditor != "")
                    {
                        vars["initeditor"] = ",initeditor:" + dataItem.JSInitEditor;
                    }
                    vars["geteditorvalue"] = "";
                    if (dataItem.JSGetEditorValue != "")
                    {
                        vars["geteditorvalue"] = ",geteditorvalue:" + dataItem.JSGetEditorValue;
                    }


                    vars["columntype"] = "";
                    if (dataItem.columntype != "")
                    {
                        vars["columntype"] = ",columntype:'" + dataItem.columntype + "'";
                    }
                    if (dataItem.JSCreateEditor != "")
                    {
                        vars["columntype"] = ",columntype:'template'";
                    }

                    vars["editable"] = "";
                    if (dataItem.columntype != "checkbox" && !dataItem.editable)
                    {
                        vars["editable"] = ",editable:false";
                    }

                    if (dataItem.ShowSum || dataItem.FuncGetSumValue != null || dataItem.ShowAvg)
                    {
                        if (dataItem.JsAggregatesRender == "")
                        {
                            dataItem.JsAggregatesRender = $@"
                                    function(aggregates, column, element, summaryData) {{
                                            var renderstring = `<div class='jqx-widget-content style='float: left; width: 100%; height: 100%; '>`;
                                            var sumValue=0;
                                            if(window.source_{gridID}.sumResult) sumValue=window.source_{gridID}.sumResult.{key.Key}||'0';
                            
                                            renderstring += `<div style='position: relative; margin: 6px; text-align: right; overflow: hidden;'>` +  sumValue + '</div>';
                                    
                                            renderstring += '</div>';
                                            return renderstring;
                                        }} 
                           ";
                        }
                    }

                    vars["aggregates_computer"] = "";
                    if (dataItem.JsAggregatesComputer != "")
                    {
                        vars["aggregates_computer"] = ",aggregates:" + dataItem.JsAggregatesComputer;
                    }
                    vars["aggregatesrenderer"] = "";
                    if (dataItem.JsAggregatesRender != "")
                    {
                        vars["aggregatesrenderer"] = ",aggregatesrenderer:" + dataItem.JsAggregatesRender;
                    }


                    vars["hidden"] = "";
                    if (dataItem.Hidden)
                    {
                        vars["hidden"] = ",hidden:true";
                    }

                    vars["hideOnLoad"] = "";
                    if (dataItem.HideOnLoad)
                    {
                        vars["hideOnLoad"] = ",hideOnLoad:true";
                    }

                    vars["alwaysShow"] = "";
                    if (dataItem.AlwaysShow)
                    {
                        vars["alwaysShow"] = ",alwaysShow:true";
                    }

                    vars["pinned"] = "";
                    if (dataItem.Pinned)
                    {
                        vars["pinned"] = ",pinned:true";
                    }

                    string columngroup = "";

                    string curGroupName = "";

                    if (groupName != "")
                    {
                        if (dataItem.SubMumTitle != "")//添加三级合并列的中级
                        {
                            curGroupName = groupName + "_" + dataItem.SubMumTitle;

                            if (!colRes.columnGroups.Contains(dataItem.SubMumTitle))
                            {
                                if (colRes.columnGroups != "") colRes.columnGroups += ",";
                                string mumGroup = "";
                                if (!hideTopGroupName) mumGroup = groupName;
                                colRes.columnGroups += $"{{ text: '{dataItem.SubMumTitle}', align: 'center', name: '{curGroupName}',parentgroup: '{mumGroup}'}}";
                            }
                        }
                        else curGroupName = groupName;
                    }

                    if (curGroupName != "") columngroup = $",columngroup:'{curGroupName}'";
                    vars["columngroup"] = columngroup;
                    vars["headertooltip"] = "";
                    if (dataItem.Tooltip != "")
                    {
                        vars["headertooltip"] = $",headertooltip:'{dataItem.Tooltip}'";
						//~tooltip
					}
                    string col = @"{text: '~title' ~headertooltip  ~columngroup~columntype~editable,datafield: '~datafield'~displayfield~width~cellsalign~sortable,align:'center'~hidden~hideOnLoad~alwaysShow~pinned~cellsrenderer~aggregates_computer~aggregatesrenderer~createeditor~initeditor~geteditorvalue~cellbeginedit~cellendedit}";
                    col = GetStringWithVar(col, vars);
                    if (sColumns != "") sColumns += ",";
                    sColumns += col;
                }
            }
            string columnGroups = "";
            await GetColumnsJS(grid.Columns, "", colRes);
            // await GetColumnsJS(grid.Columns,"",ref sColumns, ref bHaveLinkeBtn,ref columnGroups);
            sColumns = "[" + sColumns + "]";
            if (colRes.columnGroups != "") columnGroups = ",columngroups:[" + colRes.columnGroups + "]";
            string msgHead = this.GetType().FullName.Replace("Model", "").Split(".").Last();
            varsG["msgHead"] = msgHead;
            varsG["columns"] = sColumns; //varsG["rowCount"] = rowCount.ToString(); 


            varsG["columnGroups"] = columnGroups;
            varsG["gridID"] = gridID;
            varsG["PageSize"] = grid.PageSize.ToString();
            varsG["showaggregates"] = "";
            if (grid.ShowAggregates)
            {
                varsG["showaggregates"] = "showaggregates:true,showstatusbar:true,statusbarheight:25,";
            }
            varsG["sortable"] = "";
            if (grid.Sortable)
            {
                varsG["sortable"] = "sortable:true,";
            }
            varsG["sortmode"] = "";
            if (grid.AllowMultiColumnsSort)
            {
                //   varsG["sortmode"] = "sortmode:'many',";
            }
            varsG["ColumnsHeight"] = "";
            if (grid.ColumnsHeight > 0)
            {
                varsG["ColumnsHeight"] = $"columnsheight:{grid.ColumnsHeight},";
            }
            varsG["RowsHeight"] = "";
            if (grid.RowsHeight > 0)
            {
                varsG["RowsHeight"] = $"rowsheight:{grid.RowsHeight},";
            }
            varsG["addemptyrow"] = "";
            if (grid.AddEmptyRow)
            {
                varsG["addemptyrow"] = "addemptyrow:true,";
            }

            string contextMenu = "";

            string rowClickFunc = @$"
$('#{gridID}').on('rowclick',
 function (event) {{
   if (event.args.rightclick) {{
       if({grid.ShowContextMenu.ToString().ToLower()}){{
           $('#{gridID}').jqxGrid('clearselection',false) 
           $('#{gridID}').jqxGrid('selectrow', event.args.rowindex,true);
         
           var scrollTop = $(window).scrollTop();
           var scrollLeft = $(window).scrollLeft();
           var top=parseInt(event.args.originalEvent.clientY) + 5 + scrollTop
           var menuHeight=$('#gridMenu_{gridID}').height()
           if(top + menuHeight + 30>window.innerHeight){{ 
               top=top-menuHeight -10  
           }}

           contextMenu.jqxMenu('open', parseInt(event.args.originalEvent.clientX) + 5 + scrollLeft, top);
                        return false;
       }}
   }}
   else{{
       // setTimeout(()=>{{
        //    if(!window.g_checkedRows || Object.keys(window.g_checkedRows).length==0){{
       //         var autorowheight = $('#{gridID}').jqxGrid('autorowheight');
       //         if(!autorowheight){{
       //             $('#{gridID}').jqxGrid('clearselection',false)  
       //             $('#{gridID}').jqxGrid('selectrow',event.args.rowindex)  
      //              $('#{gridID}').jqxGrid({{selectionmode:'singlerow'}}); 
      //          }}
      //      }}
      //  }},10)
        
   }}
   return true

}});

";
            scriptFunc += rowClickFunc;
            if (grid.ShowContextMenu)
            {
                contextMenu =
                 $@"
$('#{gridID}').on('contextmenu', function () {{
    return false;
 }});
var divMenu=`<div id='gridMenu_{gridID}'>
                {grid.ContextMenuHTML}
   </div>`;
$('body').append(divMenu);
var contextMenu = $('#gridMenu_{gridID}').jqxMenu({{ width: 200, height: 87, autoOpenPopup: false, mode: 'popup'}});



$('#gridMenu_{gridID}').on('itemclick', function (event) {{
    var args = event.args;
    event.stopPropagation();
        event.preventDefault();
    var menuItemID=$(args).attr('id')
    var rowindex = $('#{gridID}').jqxGrid('getselectedrowindex');
    if ($(args).attr('id') == 'edit') {{
        editrow = rowindex;
        if(window.onGridRowEdit){{window.onGridRowEdit(rowindex,'{gridID}');}}                         
    }}
    else if ($.trim($(args).text()) == '删除') {{
        var rowid = $('#{gridID}').jqxGrid('getcellvalue', rowindex,'{grid.IdColumn}');
        if(rowid){{
                jConfirm(`确定要删除选中行吗？`, function () {{ 
                    $.ajax({{
                            url: '../api/{msgHead}/DeleteRecords',
                            type: 'POST', 
                            contentType: 'application/json', 
                            data:JSON.stringify({{operKey:g_operKey,gridID:'{gridID}',rowIDs:rowid}}), 
                            success: function(data) {{
                                if (data.result == 'OK')
                                {{  
                                        QueryData(); 
                                    // $('#gridItems').trigger('afterDelete',rowid)
                                    //底层修改要谨慎，gridItems是商品档案里的,其他gridID不一样就会报错
                                        $('#{gridID}').trigger('afterDelete',rowid)
                                }}
                                else{{
                                        bw.toast(data.msg,5000);
                                }}
                            }} 
                    }});   
                }}, ''); 
        }}  
    }}
    else {{  
            window.onGridRowContextMenuClick('{gridID}', menuItemID, rowindex);
                            
        }}
}});";
            }

            varsG["contextMenu"] = contextMenu;
            varsG["JSBeforeCreate"] = grid.JSBeforeCreate;
            string ajaxMethod = "ajaxGet";
            if (this.UsePostMethod)
            {
                ajaxMethod = "ajaxPost";
            }

            string autoRememberJs = "";
            foreach (var kp in DataItems)
            {
                var dt = kp.Value;
                if (dt.AutoRemember)
                {
                    autoRememberJs += @$"
var value=window.queryItems[{kp.Key}];
var preValue='';
if(window.m_lastQueryItems) preValue=window.m_lastQueryItems[{kp.Key}]
if(value!=preValue){{
                   
}}
";

                }
            }

            scriptCreateGrid = @$"
            if(true){{
               {scriptFunc}
                   window.gridData_~gridID={{}};
            window.gridData_~gridID.pageSize=~PageSize;
            window.gridData_~gridID.localRows={{}};
            var theme = ''; 
           
            window.Query_~gridID=function(callback){{  
                     if({(grid.HasCheck && !grid.KeepCheckForQueries).ToString().ToLower()}){{
                        window.g_checkedRows={{}};   
                        window.g_arrCheckedRows=[];
                        window.checkedIds=[];
                    }}
                  $('#~gridID').jqxGrid('clear'); 
                  $('#~gridID').jqxGrid('clearselection');
                  
                  funGetDataRows_~gridID(0, window.gridData_~gridID.pageSize-1,true,function(data){{ 
                      if(data.result!='OK'){{
                            bw.toast(data.msg) 
                      }} 
                      else{{
                          window.source_~gridID.totalrecords= data.rowsCount;
                          window.source_~gridID.sumResult= data.sumResult;   
                         
                         
                      }}
                      window.g_bNewQuery=true
                      $('#~gridID').jqxGrid('updatebounddata'); 
                      var iconContainer=$('#svgLeftTop').parent().parent().find('.iconscontainer');
                      iconContainer.hide() //这个div会覆盖左上角小齿轮，导致无法点击
                      if(callback) callback(data); 

                  }});  
               
            }};
            
            window.funGetDataRows_~gridID=function(startRow,endRow,bNewQuery,callback){{
                     var pageRows={{}};  
                     
                    if(bNewQuery) {{
                        window.m_lastQueryItems={{}};
                        window.g_lastQueriedData={{}};
                        window.queryItems=funGetQueryValues();
                   
                        if( window.queryItems.errMsg){{
                            return;
                        }}
                        
                         if(window.dealFormData) {{
                             dealFormData(window.queryItems); 
                         }}



                        window.queryItems.sortColumn=window.sortColumn||'';
                        window.queryItems.sortDirection=window.sortDirection||''; 
                        window.gridData_~gridID.localRows={{}};

                       // $('#~gridID').jqxGrid('showloadelement')
                    }}
                    else{{
                        var ct=0;
                        for(var i=startRow;i<=endRow;i++){{
                            var row=window.gridData_~gridID.localRows[i]; 
                            if(row) {{
                               pageRows[i]=row;ct++;
                            }}
                           
                        }}
                        if(ct==endRow-startRow+1){{
                          if(callback){{
                              callback({{result:'OK',rows:pageRows}});
                              if(window.onPageShown){{window.onPageShown();}}
                          }}
                          return {{result:'OK',rows:pageRows}};
                        }}
                     }}
                    var gridSetting=null
                    if(window.g_pageSetting && window.g_pageSetting.grid && '{ajaxMethod}'=='ajaxPost'){{
                        gridSetting={{}}
                        window.g_pageSetting.grid.forEach(col=>{{
                            gridSetting[col.datafield]={{hidden:col.hidden}}                     
                        }})

                    }}
                    var postData={{gridID:'~gridID',startRow:startRow,endRow:startRow+~PageSize,GetRowsCount:bNewQuery,gridSetting}};
                    var async=false;
                    if(callback) async=true;                

                    if(bNewQuery) $.extend(postData, window.queryItems);
                    else if(window.m_lastQueryItems) $.extend(postData,window.m_lastQueryItems);
                    var dataGot=null;
                    window.g_bCallingAjax=true
                    var tmLoading=setTimeout(()=>{{
                       $('#~gridID').jqxGrid('showloadelement')
                    }},1000)
                    var tm=new Date().getTime()
                    postData.stm=tm
                    {ajaxMethod}('{DefaultUrlPrefix}~msgHead/GetQueryRecords',postData,40000).then(data=>{{
                            window.g_bCallingAjax=false
                            clearTimeout(tmLoading)
                            if (data.result == 'OK')
                            {{                                
                                if(startRow==0) window.gridData_~gridID.rowsCount=data.rowsCount; 
                                var dataRows=data.rows;
                                dataGot=data;
                                window.g_lastQueriedData=dataGot
                                for(var i in dataRows){{
                                    if(window.gridData_~gridID.localRows[i])
                                       dataRows[i]=window.gridData_~gridID.localRows[i] 
                                    else{{     
                                       window.gridData_~gridID.localRows[i]=dataRows[i];
                                    }}
                                }}
 
                                window.m_lastQueryItems= window.queryItems;
                                if(callback) callback(dataGot);

                                $('#rows_count').text(window.gridData_~gridID.rowsCount);
                                if(window.onPageShown){{window.onPageShown();}}
                            }}
                            else{{
                                if(callback) callback(data);
                            }}


                        }} 
                    ).catch((xhr, textStatus, errorThrown)=>{{
                            var result='Error',msg='连接失败'
                            window.g_bCallingAjax=false
                            var bNeedRetry=false
                            if (textStatus == 'timeout') {{                                   
                                result='Timeout'
                                msg='连接超时'
                                bNeedRetry=true
                            }}                              
                            else if (xhr.status == 500) {{
                                    result='Timeout'
                                    msg='连接超时'
                                    bNeedRetry=false
                            }} else {{
                                bNeedRetry=true
                            }}
                            this.tryCount++;        
                            if (bNeedRetry && this.tryCount <= this.retryLimit) {{ 
                                    $.ajax(this);
                                    return;
                            }}
                            if(callback){{
                                callback({{result:result,msg:msg}}); 
                            }}
                            return {{result:result,msg:msg}}; 

                    }})
                        
　               
                 return dataGot; 
           }}; 

            var rendergridrows = function(params,callback) {{
                    var startRow=params.startindex,endRow=params.endindex;
                    if(window.g_bCallingAjax) return 
                   if(window.g_bNewQuery){{
                       window.g_bNewQuery=false
                       callback(window.g_lastQueriedData)
                  }}
                  else
                  {{
                      funGetDataRows_~gridID(startRow,endRow,false,callback);
                  }}


                   // var data=funGetDataRows_~gridID(startRow,endRow,window.g_bNewQuery,callback);
                  
 
            }};
           window.getWidth=function(ctl){{
             return document.getElementById(ctl).offsetWidth;
           }};
           window.getHeight=function(ctl){{
             var ht = document.getElementById(ctl).clientHeight;
             return ht;
           }};
           function funcSort(column, direction,sortColumns){{ 
               window.sortColumn=column;
               window.sortDirection=direction===null?'' : (direction===false||direction==='descending') ?'desc' : 'asc';
                if(window.sortDirection=='') window.sortColumn='';
               window.QueryData();
               return true;
           }}
            window.source_~gridID =
            {{
                sort:funcSort,   
                datatype: 'array',
                localdata: {{}},
                totalrecords: 0,
                deleterow:function(rowId,cb){{
                  // delete this._source.records[rowId]
                   cb()   
                }}
                 
            }};
            var dataAdapter = new $.jqx.dataAdapter(window.source_~gridID); 
            var fixColCss = 'jqx-widget-header';
            if (theme != '') fixColCss += ' jqx-widget-header-' + theme;
              ~JSBeforeCreate  
               var gridHeight=getHeight('~gridID')
                         
               window.GridData= {{ 
                    width:getWidth('~gridID'),
                    height:gridHeight,
                    source:dataAdapter,
showpinnedcolumnbackground:false,
                    enabletooltips:false,
                    ~showaggregates
                    ~ColumnsHeight
                    ~RowsHeight
                    ~sortable
                    ~sortmode
                    ~addemptyrow
                    pageable:false,
                    virtualmode: true,
                    rendergridrows: rendergridrows, 
                    editable:true,
editmode:'click',
                    //selectionmode: 'none',
                    columnsresize:true,
                    theme: theme,
                    columns:~columns 
                    ~columnGroups
                }} 
               adjustColumnsBySetting()
             var ddd= $('#~gridID');
               $('#~gridID').jqxGrid(
                   window.GridData
               );
   
               ~contextMenu
               ~checkRow

            }}";

            if (bHaveLinkeBtn)
            {
                scriptCreateGrid += $@"
                      var $lastLinkBtn = null;
                var lastHoverRow=-1
                $('#{gridID}').jqxGrid({{
                    cellhover: function(cellhtmlElement, x, y,rowIndex,gridID) {{
                       
                            var ele = cellhtmlElement; 
                            var $linkBtn = $(ele).find('.linkBtn'); 
                            if ($linkBtn.length > 0) {{  
                                 var $allLinkBtn = $('#{gridID}').find('.linkBtn:visible');
                                 $allLinkBtn.css('display','none') 
                                 $linkBtn.css('display','flex') 
                            }} 
                    }}
                }}); 
                ";
            }
            scriptCreateGrid = PageBaseModel.GetStringWithVar(scriptCreateGrid, varsG);
            return scriptCreateGrid;
        }
        public async Task InitGet(CMySbCommand cmd, Action<CMySbCommand> adjustDataItems = null)
        {

            GetOperKey();
            InitDataItemsFromRequest();
            await GetJavaScripts(cmd, true);
        }


        public class NPOIMemoryStream : MemoryStream
        {
            /// <summary>
            /// 获取流是否关闭
            /// </summary>
            public bool IsColse
            {
                get;
                private set;
            }
            public NPOIMemoryStream(bool colse = false)
            {
                IsColse = colse;
            }
            public override void Close()
            {
                if (IsColse)
                {
                    base.Close();
                }
            }
        }
        public virtual void BeforeExportExcel(Dictionary<int, Dictionary<string, dynamic>> rows)
        {

        }
	

		private void SetCellValue(IRow curRow, int nCol, string value,string col_id)
        {
           // System.Diagnostics.Debug.WriteLine($"Column Id: {col_id}");

            if (value != null && value.Length > 100)
            {
                value = value.Trim();
            }
            // 9.25 解决条码科学计数法显示
            if ((col_id == "item_no" || col_id.Contains("barcode") ) && value != null && value.Length > 0)
            {
                // 设置为文本格式
                curRow.CreateCell(nCol).SetCellValue(value);
            }
            else if (value != null && value.Length < 12 && !value.StartsWith("0") && double.TryParse(value, out double dValue))
            {
                curRow.CreateCell(nCol).SetCellValue(dValue);
            }
            //9.22 解决导出excel应确保框选部分单元格的0都是数值而非文本的问题
            else if (value != null && value.Length < 20 && double.TryParse(value, out double num0Value))
            {
                curRow.CreateCell(nCol).SetCellValue(num0Value);
            }
            else
            {
                curRow.CreateCell(nCol).SetCellValue(value);
            }
        }
        private bool IsExportingExcel = false;
        public async Task<ActionResult> ExportExcel(HttpRequest request, CMySbCommand cmd, dynamic queryParams = null)
        {

            T getColumnByKey<T>(Dictionary<string, T> dic, string key, out bool bExist)
            {
                T col;
                bExist = false;
                if (dic.ContainsKey(key))
                {
                    bExist = true;
                    col = dic[key]; return col;
                }
                else if (dic.ContainsKey(key.ToggleCamp()))
                {
                    bExist = true;
                    col = dic[key.ToggleCamp()]; return col;
                }
                else return default(T);
            }
       
            if (queryParams == null)
            {
                string sParams = request.Form["params"];
                sParams = System.Web.HttpUtility.UrlDecode(sParams);
                queryParams = JsonConvert.DeserializeObject(sParams);
            }

            string operKey = queryParams.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            string progress = await RedisHelper.GetAsync("progress_" + operID);
            if (!string.IsNullOrEmpty(progress))
            {
                string[] arr = progress.Split('|');
                if (arr.Length == 2)
                {
                    return new JsonResult(new { result = "Error", msg = "有正在进行的操作:" + arr[0] });
                }

            }
            await RedisHelper.SetAsync("progress_" + operID, "导出" + this.PageTitle + "|" + "0.02", 15);
            string sql = @$"select * from info_operator o left join info_role r on o.company_id=r.company_id 
                            and o.role_id=r.role_id where o.company_id={companyID} and o.oper_id={operID}";

            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

            dynamic rights = JsonConvert.DeserializeObject(rec.rights);
            bool canSeeInPrice = rights?.delicacy?.seeInPrice?.value?.ToString()?.ToLower() == "true";
			bool canSeeProfit = rights?.delicacy?.seeProfit?.value?.ToString()?.ToLower() == "true";

			int pageSize = 3000;
            queryParams.endRow = pageSize - 1;
            queryParams.GetRowsCount = true;
            IsExportingExcel = true;

            #region  Get page custom setting, Only for export excel
            string pageSetting = "";
            pageSetting = request.Form["pageSetting"];

            string columnsWidth = "";
            columnsWidth = request.Form["columnsWidth"];

            //string pageSetting = CPubVars.RequestV(request, "pageSetting");
            Dictionary<string, DataItem> setColumnsVisible(Dictionary<string, DataItem> gridColumns, JArray setColumns, JObject columnsWidthSet)
            {
                if (setColumns == null) return gridColumns;
                int orderIndex = 0;
                foreach (dynamic col in setColumns)
                {
                    string key = col.datafield;
                    gridColumns.TryGetValue(key, out DataItem gridCol);
                    if (gridCol != null)
                    {
                        string hidden = col.hidden;
                        if (hidden == "true" || hidden == "True")
                            gridCol.Hidden = true;
                        else gridCol.Hidden = false;
                        orderIndex++;

                        gridCol.OrderIndex = orderIndex;


                        if (columnsWidthSet != null)
                        {
                            dynamic d = columnsWidthSet.GetValue(key);
                            if (d != null)
                            {
                                gridCol.Width = (string)d;

                            }
                        }

                    }
                }
                foreach (var kp in gridColumns)
                {
                    var col = kp.Value;
                    if (col.BuyPriceRelated && !canSeeInPrice)
                    {
                        col.Hidden = true;
                    }
					if (col.ProfitRelated && (!canSeeProfit|| !canSeeInPrice))
					{
						col.Hidden = true;
					}
				}

                var sortedGridColumns = gridColumns.OrderBy(kv => {
                    return kv.Value.OrderIndex;

                }).ToDictionary(kv => kv.Key, kv => kv.Value);
                return sortedGridColumns;
            }

            JArray setColumns = null;
            if (pageSetting.IsValid())
            {
                pageSetting = System.Web.HttpUtility.UrlDecode(pageSetting);
                dynamic pageSet = JsonConvert.DeserializeObject(pageSetting);
                //queryParams.gridSetting = pageSet.grid;
                setColumns = (JArray)pageSet.grid;
                //queryParams.gridSetting = setColumns;
            }
            JObject columnsWidthSet = null;
            if (columnsWidth.IsValid())
            {
                columnsWidth = System.Web.HttpUtility.UrlDecode(columnsWidth);
                dynamic widthSet = JsonConvert.DeserializeObject(columnsWidth);
                columnsWidthSet = (JObject)widthSet;
            }
            
            #endregion



            JsonResult jsonResult = await GetRecordFromQuerySQL(request, cmd, queryParams);

            dynamic data = jsonResult.Value;
            // Dictionary<int, Dictionary<string, string>> rows = JsonConvert.DeserializeObject<Dictionary<int, Dictionary<string, string>>>(JsonConvert.SerializeObject(data.rows));
            //Dictionary<int, Dictionary<string, dynamic>> rows = null;
            int rowsCount = Convert.ToInt32(data.rowsCount);
            Dictionary<int, Dictionary<string, dynamic>> rows = null;
            string sRows = JsonConvert.SerializeObject(data.rows);
            rows = JsonConvert.DeserializeObject<Dictionary<int, Dictionary<string, dynamic>>>(sRows);

            // string sRows = JsonConvert.SerializeObject(data.rows);
            // rows = JsonConvert.DeserializeObject<Dictionary<int, Dictionary<string, dynamic>>>(sRows);

            Dictionary<string, dynamic> sumResult = null;

            string sSumRow = JsonConvert.SerializeObject(data.sumResult);
            sumResult = JsonConvert.DeserializeObject<Dictionary<string, dynamic>>(sSumRow);

       

			IWorkbook workbook = new SXSSFWorkbook(5000);
            string pageTitle = this.PageTitle;
            if (pageTitle == "" || pageTitle == null) pageTitle = "sheet1";
            pageTitle = pageTitle.Replace("/", "_");
            ISheet worksheet = workbook.CreateSheet(pageTitle);
            IRow curRow = null;
            ICell curCell = null;

            // 2024.9.6 修改表头起始列ncol=1为ncol=0，使表头起始列左移一格，待测试是否有问题
            int nCol = 0;
             
            // 2024.9.6初始化表头列数计数器,用以获取表头列数，合并标题行单元格使用
            int headerColumnCount = 0;

            curRow = worksheet.CreateRow(1);

            // 该部分记录表头信息，每次查询增加两个空列，用以记录（k:v），例如（"开始日期":"2022-01-01 00:00")
            // 创建一个用于label的样式,调整label字体为粗体
            IFont labelFont = workbook.CreateFont();
            labelFont.IsBold = true; // 加粗
            ICellStyle labelStyle = workbook.CreateCellStyle();
            labelStyle.SetFont(labelFont);
            foreach (var k in DataItems)
            {
                var queryItem = k.Value;
                if (queryItem.ForQuery && queryItem.Value != "")
                {
                    curCell= curRow.CreateCell(nCol);
                    curCell.SetCellValue(k.Value.Title + ":");
                    // 2024.9.6 动态调整列宽
                    int cellWidth = (int)(curCell.StringCellValue.Length * 2); 
                    worksheet.SetColumnWidth(nCol, cellWidth * 256);
                    curCell.CellStyle = labelStyle;
                    //curRow.CreateCell(nCol).SetCellValue(k.Value.Title + ":");
                    curCell = curRow.CreateCell(nCol+1);
                    if (queryItem.Label != "")
                       curCell.SetCellValue(queryItem.Label);
                    else
                        curCell.SetCellValue(queryItem.Value);
                    cellWidth = (int)(curCell.StringCellValue.Length * 1.2);
                    if(cellWidth > 255)
                        cellWidth = 255;
                    worksheet.SetColumnWidth(nCol+1, cellWidth * 256);
                    nCol += 2;
                    headerColumnCount = nCol - 1; // 2024.9.6记录表头列数，合并标题行单元格使用
                }
            }
            curRow = worksheet.CreateRow(0);

            //2024.9.6 修改表头标题行信息，合并居中curCell = curRow.CreateCell(5)-》curCell = curRow.CreateCell(0),待测试
            curCell = curRow.CreateCell(0);
            try
            {
                // 2024.9.6 修改标题行单元格样式
                IFont font = workbook.CreateFont();
                font.IsBold = true; // 加粗
                font.FontHeight = 300; // 加大字体大小，单位为1/20点
                // XSSFCellStyle style = (XSSFCellStyle)workbook.CreateCellStyle();
                ICellStyle style = workbook.CreateCellStyle();
                style.SetFont(font);

                // 设置对齐方式
                style.Alignment = HorizontalAlignment.Center; // 水平居中
                style.VerticalAlignment = VerticalAlignment.Center; // 垂直居中

                
                curCell.CellStyle = style;
     
                // 合并标题行的单元格
                worksheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, headerColumnCount));
                curCell.SetCellValue(this.PageTitle);
                curRow.Height = 700; // 设置行高，单位为1/20点
            }
            catch (Exception e)
            {
                // 2024.9.6 增加异常处理逻辑
                Console.WriteLine("Error occurred: " + e.Message);
            }
            //2024.9.6 加粗表头标题

            // 设置标题格式
            IFont headerFont = workbook.CreateFont();
            headerFont.IsBold = true; // 设置字体为加粗

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(headerFont);

            int nRow = 3;
            curRow = worksheet.CreateRow(nRow);
            ICell numcell = curRow.CreateCell(0);
            numcell.SetCellValue("序号");
            numcell.CellStyle = headerStyle;

            Dictionary<string, DataItem> columns = new Dictionary<string, DataItem>();

            bool bHaveSubColumns = false;
            foreach (var k in this.Grids.First().Value.Columns)
            {
                var col = k.Value;
                if (col.FuncGetSubColumns != null) await col.TryGetSubColumns();
                if (col.SubColumns != null)
                {
                    foreach (var k1 in col.SubColumns)
                    {
                        bHaveSubColumns = true;
                        columns.Add(k1.Key, k1.Value);
                        k1.Value.Title = col.Title + k1.Value.Title;
                    }
                    if (setColumns != null)
                    {
                        foreach (JObject c in setColumns)
                        {
                            string fld = (string)c.GetValue("datafield");
                            if (fld == k.Key)
                            {
                                string visible = (string)c.GetValue("visible") ?? "true";
                                visible = visible.ToLower();
                                foreach (var k1 in col.SubColumns)
                                {
                                    k1.Value.Hidden = visible == "false";
                                }
                            }
                        }
                    }
                }
                else
                {
                    columns.Add(k.Key, k.Value);
                }
            }

            columns=setColumnsVisible(columns, setColumns, columnsWidthSet);
            Dictionary<string, DataItem> dicVisibleColumns = new Dictionary<string, DataItem>();
            

			nCol = 1;
            foreach (var k in columns)
            {
                var col = k.Value;
                bool bColNotExistInRecord = false;
                if (rows.Count > 0)
                {
                    var firstRow = rows.First().Value;
                    getColumnByKey<dynamic>(firstRow, k.Key, out bool bExist);
                    if (!bExist)
                    {
                        bColNotExistInRecord = true;
                    }
                }
                if (!col.Hidden && !bColNotExistInRecord)
                {
                    ICell cell = curRow.CreateCell(nCol);
                    cell.SetCellValue(col.Title);
                    dicVisibleColumns.Add(k.Key, col);
                    cell.CellStyle = headerStyle; // 应用加粗样式
                    double wd = 200;
                    if (CPubVars.IsNumeric(col.Width))
                    {
                        wd = Convert.ToDouble(col.Width);
                    }
                    if (columnsWidthSet != null)
                    {
						//columnsWidthSet

					}
					worksheet.SetColumnWidth(nCol, wd * 30); // 20 characters width
					nCol++;
                }
            }

            nCol = 1;
            if (bHaveSubColumns)
            {
                var tempCurRow = worksheet.CreateRow(nRow - 1);
                tempCurRow.CreateCell(0).SetCellValue("序号");
                CellRangeAddress tempca = new CellRangeAddress(nRow - 1, nRow, 0, 0);
                worksheet.AddMergedRegion(tempca);
                foreach (var k in columns)
                {
                    //tempCurRow.CreateCell(nCol);
                    var col = k.Value;
                    if (col.FuncGetSubColumns != null) await col.TryGetSubColumns();
                    if (col.SubColumns != null)
                    {

                        int nStartCol = -1, nEndCol = -1;
                        foreach (var k1 in col.SubColumns)
                        {
                            if (dicVisibleColumns.ContainsKey(k1.Key))
                            {
                                if (nStartCol == -1) nStartCol = nCol;
                                nEndCol = nCol;
                                ICell cell = tempCurRow.CreateCell(nCol);
                                cell.CellStyle = headerStyle; // 应用加粗样式
                                nCol++;
                            }
                        }
                        if (nStartCol != -1)
                        {
                            CellRangeAddress ca = new CellRangeAddress(nRow - 1, nRow - 1, nStartCol, nEndCol);
                            worksheet.AddMergedRegion(ca);
                            curRow = worksheet.GetRow(nRow - 1);
                            if (curRow == null) curRow = worksheet.CreateRow(nRow - 1);
                            curRow.CreateCell(nStartCol).SetCellValue(col.Title);

                        }

                    }
                    else
                    {
                        if (dicVisibleColumns.ContainsKey(k.Key))
                        {
                            curRow = worksheet.GetRow(nRow - 1);
                            if (curRow == null) curRow = worksheet.CreateRow(nRow - 1);
                            curRow.CreateCell(nCol).SetCellValue(col.Title);
                            CellRangeAddress ca = new CellRangeAddress(nRow - 1, nRow, nCol, nCol);
                            worksheet.AddMergedRegion(ca);
                            nCol++;
                        }

                    }
                }
            }


            nRow++;
            int nShowRow = 1;

            for (int n = 0; n < rowsCount; n += pageSize)
            {
                if (n > 0)
                {
                    queryParams.GetRowsCount = false;
                    queryParams.startRow = n;
                    queryParams.endRow = n + pageSize-1;
                    dynamic dd = this.DataItems;

                    jsonResult = await GetRecordFromQuerySQL(request, cmd, queryParams);
                    data = jsonResult.Value;
                    sRows = JsonConvert.SerializeObject(data.rows);
                    rows = JsonConvert.DeserializeObject<Dictionary<int, Dictionary<string, dynamic>>>(sRows);
                }
                //int showN = n + pageSize;
                // if(showN > rowsCount) showN = rowsCount;
                await RedisHelper.SetAsync("progress_" + operID, "导出" + pageTitle + "|" + ((float)n / rowsCount).ToString(), 20);
                //await RedisHelper.SetAsync("progress_" + operID, pageTitle, 20);
                BeforeExportExcel(rows);
                foreach (var kRow in rows)
                {
                    curRow = worksheet.CreateRow(nRow);
                    curRow.CreateCell(0).SetCellValue(nShowRow);
                    nCol = 1;
                    var row = kRow.Value;

                    foreach (var k in columns)
                    {
                        var col = k.Value;
                        string value = getColumnByKey<dynamic>(row, k.Key, out bool bExist);

                        if (!col.Hidden && bExist)
                        {
                            SetCellValue(curRow, nCol, value,k.Key);
                            nCol++;
                        }
                    }
                    nRow++;
                    nShowRow++;
                    var subRowsName = this.Grids.First().Value.SubRowsNameInRow;
                    if (subRowsName != "")
                    {
                        dynamic subRows = row[subRowsName];
                        if (subRows != null)
                        {
                            Type tp = subRows.GetType();
                            string sSubRows = "";
                            if (subRows.GetType() != typeof(string))
                            {
                                sSubRows = JsonConvert.SerializeObject(subRows);
                            }
                            else
                                sSubRows = (string)subRows;

                            // 2024.07.05
                            // #2736 - 电脑端查费用支出单，日期5月1到今天，右上角的导出显示系统需升级
                            // 部分客户在导出时可能因为备注中含有单斜杠等特殊字符而出现解析报错
                            // 这一块代码集中处理类似问题
                            sSubRows = sSubRows.Replace("\\", "\\\\");

                            JArray lstSubRows = JsonConvert.DeserializeObject<JArray>(sSubRows);
                            foreach (dynamic subRow in lstSubRows)
                            {
                                curRow = worksheet.CreateRow(nRow);
                                curRow.CreateCell(0).SetCellValue(nShowRow);
                                nCol = 1;
                                foreach (var k in columns)
                                {
                                    var col = k.Value;
                                    string value = getColumnByKey<dynamic>(row, k.Key, out bool bExist);
                                    if (col.GetFromDb && !col.Hidden && bExist)
                                    {
                                        if (col.SubRowsColumn != "")
                                        {
                                            string subRowValue = subRow[col.SubRowsColumn];
                                            if (subRowValue == null)
                                                subRowValue = subRow[col.SubRowsColumn.ToggleCamp()];
                                            //if (subRowValue != null)
                                            SetCellValue(curRow, nCol, subRowValue,k.Key);
                                        }
                                        nCol++;
                                    }
                                }
                                nRow++;
                                nShowRow++;
                            }
                        }
                    }
                }

            }
            await RedisHelper.SetAsync("progress_" + operID, "导出" + pageTitle + "|" + "1", 10);

            //   await RedisHelper.SetAsync("info_type_" + operID, pageTitle, 3);

            //  await RedisHelper.RPushAsync("mylist", ["initialValue"]);

            //await RedisHelper.HSetAsync("message_"+operID,1)

            if (sumResult.Count > 0)
            {
                nCol = 1;
                curRow = worksheet.CreateRow(nRow);
                curRow.CreateCell(0).SetCellValue("合计:");
                foreach (var k in columns)
                {
                    var col = k.Value;
                    if (!col.Hidden)
                    {
                        string value = getColumnByKey<dynamic>(sumResult, k.Key, out bool bExist);
                        if (bExist)
                        {
                            SetCellValue(curRow, nCol, value,k.Key);
                            //curRow.CreateCell(nCol).SetCellValue(value);
                        }
                        else
                        {
                            curRow.CreateCell(nCol).SetCellValue("");
                        }
                        nCol++;
                    }
                }
                nRow++;
            }


		
			// 设置每行的高度
			for (int i = 0; i <= worksheet.LastRowNum; i++)
            {
                // worksheet.GetRow(i).Height = 25 * 20; // 25 points height

                IRow row = worksheet.GetRow(i); // 尝试获取行  
                if (row != null) // 检查行是否为null  
                {
                    row.Height = 25 * 20; // 设置行高（注意：这里的单位可能需要根据实际情况调整）  
                }
                //else
                //{
                //    //如果行不存在，则创建一个新行（如果需要的话）  
                //    row = worksheet.CreateRow(i);
                //    row.Height = 25 * 20; // 设置新创建的行的行高  
                //}
            }

                // 创建一个边框样式
                ICellStyle borderStyle = workbook.CreateCellStyle();
                borderStyle.BorderBottom = BorderStyle.Thin;
                borderStyle.BorderLeft = BorderStyle.Thin;
                borderStyle.BorderRight = BorderStyle.Thin;
                borderStyle.BorderTop = BorderStyle.Thin;

            for (int i = 2; i <= worksheet.LastRowNum; i++)
            {
                IRow row = worksheet.GetRow(i);
                if (row != null)
                {
                    for (int j = 0; j < row.LastCellNum; j++)
                    {
                        ICell cell = row.GetCell(j) ?? row.CreateCell(j);
                        if (i < 4) // 检查是否是前四行
                        {
                            // 克隆原有的样式
                            ICellStyle clonedStyle = workbook.CreateCellStyle();
                            if (cell.CellStyle != null)
                                clonedStyle.CloneStyleFrom(cell.CellStyle); // 克隆现有样式
                                                                            // 设置边框
                            clonedStyle.BorderBottom = BorderStyle.Thin;
                            clonedStyle.BorderLeft = BorderStyle.Thin;
                            clonedStyle.BorderRight = BorderStyle.Thin;
                            clonedStyle.BorderTop = BorderStyle.Thin;
                            // 应用新的样式
                            cell.CellStyle = clonedStyle;
                        }
                        else
                        {
                            // 直接应用边框样式
                            cell.CellStyle = borderStyle;
                        }
                    }
                }
            }


            string sNow = CPubVars.GetDateText(DateTime.Now);
                sNow = sNow.Replace("-", "").Replace(" ", "");

                string fileName = this.PageTitle + "_" + sNow + ".xlsx";
                fileName = System.Net.WebUtility.UrlEncode(fileName);

                // 设置响应头信息
                request.HttpContext.Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                request.HttpContext.Response.Headers.Add("Content-Disposition", $"attachment; filename=\"{fileName}\"");

                // 直接将workbook写入到HTTP响应的输出流中
                // 注意：这里不使用using语句，因为ASP.NET Core框架会负责关闭流
                await Task.Run(() =>
                {
                    workbook.Write(request.HttpContext.Response.Body);
                });

                // 清理临时文件
                workbook.Dispose();
                IsExportingExcel = false;
                // 因为我们已经写入到Response.Body，所以不需要返回一个ActionResult
                return new EmptyResult();
            }
        }
        /*
        public class QueryDataItem : DataItem
        {
            public QueryDataItemGroup SubGroup = null;
        }*/
        public class QueryDataItemGroup : DataItemGroup
        {
            public QueryDataItemGroup(string andOr)
            {
                this.AndOr = andOr;
            }
            public QueryDataItemGroup()
            {
            }
            public string AndOr = "and";//can be 'and' or 'or'
            /*
              public void FillDataItemsByRequest(HttpRequest request)
              {            
                  foreach (KeyValuePair<string, DataItem> kp in this)
                  {
                      QueryDataItem dataItem = (QueryDataItem)kp.Value;

                      if (dataItem.SubGroup!= null && dataItem.SubGroup.Count>0)
                      {
                          if (dataItem.SubGroup != this)//防止死循环
                          {
                              dataItem.SubGroup.FillDataItemsByRequest(request);
                          }                  
                      } 
                  }

                  string ignoreDataItems = "";

                  foreach (KeyValuePair<string, DataItem> kp in this)
                  {
                      QueryDataItem dataItem =(QueryDataItem) kp.Value;

                      var fld = kp.Key;

                      object rv = request.Query[fld];
                      if (rv == null) rv = "";
                      dataItem.Value = rv.ToString().Trim();

                      if (dataItem.LabelFld != "")
                      {
                          rv = request.Query[dataItem.LabelFld];
                          if (rv == null) rv = "";
                          dataItem.Label = rv.ToString().Trim();
                      }
                      if (dataItem.IgnoreDataItemsIfNotEmpty != "" && dataItem.Value != "")
                      {
                          ignoreDataItems += "," + dataItem.IgnoreDataItemsIfNotEmpty;
                      }
                  }
                  if (ignoreDataItems != "")
                  {
                      ignoreDataItems += ",";

                      foreach (KeyValuePair<string, DataItem> kp in this)
                      {
                          DataItem dataItem = kp.Value;
                          var fld = kp.Key;
                          if (ignoreDataItems.Contains("," + fld + ",") || ignoreDataItems.Contains(",all,"))
                          {
                              if (!(dataItem.IgnoreDataItemsIfNotEmpty != "" && dataItem.Value != ""))
                              {
                                  dataItem.Value = "";
                              }
                          }
                      }
                  }  

              }
          */
        }

    }

