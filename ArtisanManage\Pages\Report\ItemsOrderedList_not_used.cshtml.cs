﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.Report
{
    public class ItemsOrderedListModel : PageQueryModel
    { 
        public bool ForSelect;

        private string buildJsAggregatesComputer(string unit_no, string qty)
        {
            var JsAggregatesComputer = $@"[{{
                                     'qty':
                                          function (aggregatedValue, currentValue, column, record) {{
                                                  var unit_no = record.{unit_no};
                                                  var qty = record.{qty};
                                                  currentValue = parseFloat(currentValue);
                                                  if (!currentValue) return aggregatedValue;
                                                  if (qty && unit_no) {{
                                                    if (!aggregatedValue) aggregatedValue = '';
                                                         var n = aggregatedValue.indexOf(unit_no);
                                                         if (n > 0) {{
                                                            var unitQty = 0;
                                                            for (i = n - 1; i >= 0; i--) {{
                                                                 var tmp = parseFloat(aggregatedValue.substring(i, n));
                                                                 if (tmp.toString() != 'NaN') {{
                                                                      unitQty = tmp;
                                                                 }}
                                                                 else break;
                                                             }}
                                                            if (unitQty > 0) {{
                                                               aggregatedValue = aggregatedValue.replace(unitQty + unit_no, toDecimal(unitQty + currentValue) + unit_no)
                                                            }}
                                                        }}
                                                        else
                                                        {{
                                                            aggregatedValue = aggregatedValue + currentValue.toString() + unit_no;
                                                         }}
                                                   }}
                                                return aggregatedValue;
                                                }}
                                        }}]
                                    ";

            return JsAggregatesComputer;
        }
        private string buildJsAggregatesRender()
        {
            return @" function aggregatesrenderer_quantity (aggregates, column, element, summaryData) {
                                               var renderstring = `<div class='jqx-widget-content style='float: left; width: 100%; height: 100%; '>`;
                                               $.each(aggregates, function (key, value) {
                                                    renderstring += '<div style=`position: relative; margin: 6px; text-align: right; overflow: hidden;`>' + value + '</div>';
                                               });
                                               renderstring +=`</div>`;
                                          return renderstring;
                                         } 
                     ";
        }
        public ItemsOrderedListModel(CMySbCommand cmd) : base(Services.MenuId.itemsOrderedSummary)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"prepay_sub_id",new DataItem(){FldArea="divHead",Title="定货款账户",LabelFld="sub_name",ButtonUsage="list",CompareOperator="=",
                    SqlForOptions ="select sub_id as v,sub_name as l from info_pay_way where company_id=~COMPANY_ID and payway_type = 'YS' and is_order"}},
                /*           
                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",SqlFld="sd.item_id",DropDownWidth="300",
                    SqlForOptions ="select item_id as v,item_name as l,py_str as z from info_item_prop" }},*/
                {"other_class",new DataItem(){Title="商品品类", LikeWrapper="/", CtrlType="jqxTree",MumSelectable=true,GetOptionsOnLoad=true,CompareOperator="like",QueryOnChange=true,
                   SqlForOptions=CommonTool.selectClasses} },
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="iob.supcust_id",
                    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID "}},
                };
            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       { "item_id",new DataItem(){Title="商品",Hidden = true,SqlFld="iob.item_id" } },
                       {"supcust_id",new DataItem(){Title = "客户",Hidden=true,SqlFld = "iob.supcust_id" } },
                       {"sup_name",new DataItem(){Title="客户名称",Width = "200", } },
                       {"multi_selected",new DataItem(){Title="", Width="50",columntype="checkbox", GetFromDb=false } },
                       {"prepay_sub_id",new DataItem(){Title = "账户",Hidden = true } },
                       {"sub_name",new DataItem(){Title = "定货款账户",Width ="200" } },
                       {"item_name",new DataItem(){Title="商品名称",  Width="300",SqlFld="ip.item_name"}},
                       {"unit_no",new DataItem(){Title="单位", CellsAlign="right",Width="200",SqlFld="iob.unit_no",Hidden = true} },
                       {"quantity",new DataItem(){Title="数量", CellsAlign="right",Width="200",Hidden = true} },
                       {"quantity_unit",new DataItem(){Title="数量", CellsAlign="right",Width="200",SqlFld = "(case when quantity = round(quantity) then concat(round(quantity),iob.unit_no) else concat(round(quantity,4),iob.unit_no) end)",
                       //JsAggregatesComputer = buildJsAggregatesComputer("unit_no","quantity"),
                       //     JsAggregatesRender=buildJsAggregatesRender()
                       } },
                       {"order_price",new DataItem(){Title="价格", CellsAlign="right",Width="200",Hidden = true} },
                       {"order_price_unit",new DataItem(){Title = "价格",Width = "200",CellsAlign = "right",SqlFld = "(case when order_price = round(order_price) then concat(round(order_price),'/',iob.unit_no) else concat(round(order_price,4),'/',iob.unit_no) end)" } },
                       {"balance",new DataItem(){Title="总额", CellsAlign="right",Width="200",ShowSum = true} }
                     },
                     QueryFromSQL = @"from items_ordered_balance iob 
                                      left join info_pay_way p on p.sub_id = iob.prepay_sub_id
                                      left join info_supcust as isup on iob.supcust_id = isup.supcust_id
                                      left join info_item_prop ip on iob.item_id = ip.item_id ",
                      QueryOrderSQL="order by item_name"
                  }
                }
            };
        }

        public async Task OnGet(string forSelect)
        {
            await InitGet(cmd);
            ForSelect = forSelect == "1";
        }
    }


    [Route("api/[controller]/[action]")]
    public class ItemsOrderedListController : QueryController
    { 
        public ItemsOrderedListController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ItemsOrderedListModel model = new ItemsOrderedListModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            ItemsOrderedListModel model = new ItemsOrderedListModel(cmd);
       
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

    }
}
