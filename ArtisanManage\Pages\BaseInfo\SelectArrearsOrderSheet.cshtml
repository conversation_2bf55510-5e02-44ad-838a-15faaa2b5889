﻿@page
@model ArtisanManage.Pages.BaseInfo.SelectArrearsOrderSheetModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head >
    <meta name="viewport" content="width=device-width" />
    <title>选择单据</title>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdropdownlist.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        $(document).ready(function () { 
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
            function callback(){
                // console.log("imcallllllllllllbaaaaaaaaaack")
                // let rows  = $('#gridItems').jqxGrid('getrows')
                // console.log(rows.length + "rows")
                // QueryData的回调函数，如果已经选中一个结算单位，则启用全选复选框
                // 如果没有选中结算单位则禁用全选
                let acct_id = $("#acct_id").val().value
                if (!acct_id) {
                    $("#sys_checkColumn").prop('disabled', true);
                } else {
                    $("#sys_checkColumn").prop('disabled', false);
                }
            }
            // 初始化一个全局的 acct_id变量用于判断是否勾选了来自不同结算单位的单据
            let acct_id = $("#acct_id").val().value
            let acct_name = $("#acct_id").val().label
            window.onGridCheckChanged = function () {
                debugger
                console.log(window.g_checkedRows)
                if (acct_id) {
                    debugger
                    let rowIndex = ""
                    let checkedRow = { ...window.g_checkedRows }
                    for (let k of Object.keys(checkedRow)) {
                        if (checkedRow[k].acct_id != acct_id) {
                            // 选择了不同的结算单位的单子
                            // 删除这个元素
                            rowIndex = checkedRow[k].visibleindex
                            bw.toast("不能同时选择来自不同结算单位的业务单据")
                            // window.onGridRowCheck(rowIndex, event) 会循环调用，虽然可以结束
                            var index = window.checkedIds.indexOf(k)
                            var keyColumn = window.funcGetRowKeyColumn();
                            var rowdata = window.gridData_gridItems.localRows[rowIndex];
                            if (index != -1) {
                                window.checkedIds.splice(index, 1)
                            }
                            delete window.g_checkedRows[k]
                            window.g_arrCheckedRows.some((r, idx) => {
                                {
                                    if (r[keyColumn] == rowdata[keyColumn]) {
                                        {   // 从选择的行里删除该行
                                            window.g_arrCheckedRows.splice(idx, 1)
                                            return true
                                        }
                                    }
                                }
                            })
                            // 更新选择的行数量
                            document.all.lblCheckedCount.innerText = Object.keys(g_checkedRows).length;
                            // jqxgrid撤销行选中效果
                            $('#gridItems').jqxGrid('unselectrow', rowIndex)

                            break
                        }
                    }


                }
                else {
                    for (let k of Object.keys(window.g_checkedRows)){
                        // 没有acct_id时，把第一个选中行的id复制给该变量
                        acct_id = window.g_checkedRows[k].acct_id
                        acct_name = window.g_checkedRows[k].acct_sup_name
                        break
                    }
                }
                if (Object.keys(window.g_checkedRows).length === 0) {
                    // 如果撤销了所有行的选择，这个变量要置空
                    acct_id = ""
                }
            }
            QueryData(event,callback);

            $("#btnQuery").on('click', function () {
                QueryData(event, callback);
            })

            $("#btnAdd").on('click', function () {
                let acct_info = $("#acct_id").val()
                
                var msg = {
                    msgHead: 'AddSheets', action: 'add', checkedRows: Object.values(window.g_checkedRows), acct_id: acct_id, acct_name: acct_name
                }
                // 如果单据里没有acct_id需要把acct_id带过去
                window.parent.postMessage(msg, '*');
            })

            $("#btnClose").on('click', function () {
                var msg = {
                    msgHead: 'AddSheets', action: 'close'
                }
                window.parent.postMessage(msg, '*');
            })

        })
    </script>
</head>
<body>
    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail" style="width:800px;"></div>
        <button id="btnQuery" margin:0 8px;">查询</button>
    </div>
    <div id="gridItems" style="width:calc(850px);margin-top:10px;margin-left:10px;margin-right:0px;height:75%;"> </div>
    <div style="text-align:center;margin-top:15px;margin-bottom:15px">
        <button id="btnAdd"  style="margin-right:50px;">添加</button> <button id="btnClose" >关闭</button>
    </div>
</body>
</html>