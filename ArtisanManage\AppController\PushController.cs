﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.Services.PushModel;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    [Route("AppApi/[controller]/[action]")]

    public class PushController : YjController
    {
        CMySbCommand cmd;
        public PushController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpPost]
        public async Task<JsonResult> Push([FromBody]dynamic data)
        {
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID,out string operID);
            string receiverOperIDs = data.receiverOperIDs;

            PushService pushService = new PushService(cmd);
            #region 向小米手机推送信息
            MiPushModel miPushModel = new MiPushModel();
            miPushModel.payload = data.payload;
            miPushModel.description = data.description;
            miPushModel.title = data.title;
            dynamic miRegIDDatas= await pushService.GetRegIDByOperID(operID, companyID, "Xiaomi");
            List<string> miRegIDs = new List<string>();
            foreach(dynamic miRegIDData in miRegIDDatas){
                miRegIDs.Add(miRegIDData.reg_id);
            }
            miPushModel.registrationIDs = String.Join(",", miRegIDs);
            pushService.PushMi(miPushModel);
            #endregion
            #region 向OPPO手机推送信息
            OPPOModel oppoModel = new OPPOModel();
            dynamic oppoRegIDDatas = await pushService.GetRegIDByOperID(operID, companyID, "OPPO");
            List<string> oppoRegIDs = new List<string>();
            foreach (dynamic oppoRegIDData in oppoRegIDDatas)
            {
                oppoRegIDs.Add(oppoRegIDData.reg_id);
            }
            oppoModel.target_value = String.Join(";", oppoRegIDs);
            OPPONotification oppoNotification = new OPPONotification();
            oppoNotification.title = data.title;
            oppoNotification.content = data.payload;
            oppoModel.notification = oppoNotification;
            pushService.PushOppo(oppoModel);
            #endregion
            #region 向VIVO手机推送信息
            VivoModel vivoModel = new VivoModel();
            vivoModel.title = data.title;
            vivoModel.content = data.payload;
            dynamic vivoRegIDDatas = await pushService.GetRegIDByOperID(operID, companyID, "vivo");
            List<string> vivoRegIDs = new List<string>();
            foreach (dynamic vivoRegIDData in vivoRegIDDatas)
            {
                vivoRegIDs.Add(vivoRegIDData.reg_id);
            }
            var cheatVivoRegID = "12345678431234467890126";
            if (vivoRegIDs.Count ==1)
            {
                vivoRegIDs.Add(cheatVivoRegID);
            }
            pushService.PushVivo(vivoModel, vivoRegIDs);
            #endregion

            return Json(new {  });
        }
    }
}
