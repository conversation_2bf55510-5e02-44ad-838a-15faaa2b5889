﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
/*
namespace ArtisanManage.Models
{
    public class DataGrid
    {
        public Dictionary<string, DataItem> Columns = new Dictionary<string, DataItem>();
        public List<Dictionary<string, DataItem>> Rows = new List<Dictionary<string, DataItem>>();
        public string TableName = "";public string IdFld = "";
        public string SelectFromSQL = "";
        public int MinRows = 3;
        public async Task GetScript(CMySbCommand cmd, string gridID,string ID,ref string scriptCreateGrid,ref string scriptSaveGrid)
        {
            SelectFromSQL = SelectFromSQL.Replace("~ID", ID);
            string sql =MyPageModel.getSQLFromDataItems(Columns, TableName, SelectFromSQL);
            cmd.CommandText = sql;
            CMySbDataReader dr = await cmd.ExecuteReaderAsync();
            List<Dictionary<string, dynamic>> lstRows = new List<Dictionary<string, dynamic>>();
            while (dr.Read())
            {
                Dictionary<string, dynamic> row = new Dictionary<string, dynamic>();
                lstRows.Add(row);
                foreach (KeyValuePair<string, DataItem> key in Columns)
                {
                    DataItem dataItem = key.Value;
                    object ov = dr[key.Key];
                    string value = "";string label = "";
                    if (ov != DBNull.Value)  value = ov.ToString().Trim();
                    row[key.Key] =value;
                    if (dataItem.LabelFld != "")
                    {
                        if (dataItem.Source == "null")
                        {
                            ov = dr[dataItem.LabelFld];
                            if (ov != DBNull.Value) label = ov.ToString();
                        }
                        else
                        {
                            List<dynamic> lst = JsonConvert.DeserializeObject<List<dynamic>>(dataItem.Source);
                            foreach (dynamic obj in lst)
                            {
                                if (obj.v == dataItem.Value)
                                {
                                    label = obj.l; break;
                                }
                            }
                        }
                        row[dataItem.LabelFld] = label;
                    }
                } 
            }
            dr.Close();
            int rowCount = MinRows;
            if (lstRows.Count >= MinRows) rowCount = lstRows.Count + 1;
           
            for (int i = lstRows.Count; i < rowCount; i++)
            {
                Dictionary<string, dynamic> row = new Dictionary<string, dynamic>();
                lstRows.Add(row);
                foreach (KeyValuePair<string, DataItem> key in Columns)
                {
                    DataItem dataItem = key.Value;
                    row[key.Key] = "";
                } 
            }
            Dictionary<string, string> varsG = new Dictionary<string, string>(); 
            varsG["rows"]  = JsonConvert.SerializeObject(lstRows);

            string sColumns  = @"{
                            text: '', sortable: false, filterable: false, editable: false, pinned: true,
                            groupable: false, draggable: false, resizable: false,
                            datafield: '', columntype: 'number', width: 30,
                            cellclassname: fixColCss,
                            cellsrenderer: function(row, column, value) {
                                 return '<div style=""margin:4px;"">' + (value + 1) + '</div>';
                            } 
                           }";
            foreach (KeyValuePair<string, DataItem> key in Columns)
            {
                DataItem dataItem = key.Value; 
                Dictionary<string, string> vars = new Dictionary<string, string>(); 
                vars["datafield"] = key.Key;
                vars["displayfield"] = ""; vars["title"] =dataItem.Title;
                if (dataItem.LabelFld != "") { vars["displayfield"] = ", displayfield: '"+dataItem.LabelFld + "'"; }
                vars["width"] = "";
                if (dataItem.Width != "") { vars["width"] = ", width: '" + dataItem.Width + "'"; }
                vars["columntype"] = ""; vars["createeditor"] = ""; vars["initeditor"] = "";
                if (dataItem.Url!="" || dataItem.Source != "null") {
                    vars["columntype"] = ", columntype: 'template'";

                    Dictionary<string, string> varsE = new Dictionary<string, string>(); 
                    varsE["valueFld"] = key.Key; varsE["labelFld"] = key.Key;
                    if (dataItem.LabelFld != "") {varsE["labelFld"] = dataItem.LabelFld;}
                    varsE["url"] = dataItem.Url;

                    vars["createeditor"] = @",createeditor:
                    function (row, cellvalue, editor, cellText, width, height) {
                        var element = $('<div></div >');
                        editor.append(element);
                        var inputElement = editor.find('div')[0]; 
                        var datafields = new Array({ datafield: '~labelFld', text: '', width: 120 }); 
                        $(inputElement).jqxInput({
                            height: height, width: width,
                            borderShape: 'none',
                            buttonUsage: 'list', 
                            dropDownHeight: 160,
                            displayMember: '~labelFld',
                            valueMember:  '~valueFld',
                            dataFields: dataFields,
                            searchFields: ['~labelFld'],
                            maxRecords: 9,
                            url:'~url'
                        });
                     }";
                    vars["createeditor"] = MyPageModel.GetStringWithVar(vars["createeditor"], varsE);
                    vars["initeditor"] = @",initeditor: function(row, cellvalue, editor, celltext, pressedkey) { 
                            var inputField = editor.find('input');
                            if (pressedkey)
                            {
                                inputField.val(pressedkey);
                                inputField.jqxInput('selectLast');
                            }
                            else
                            {
                                inputField.val({ value: cellvalue, label: celltext });
                                inputField.jqxInput('selectAll');
                            }
                           },
                        geteditorvalue: function(row, cellvalue, editor) {
                             var v = editor.find('input').val();
                              return v;}";
                } 

                string col = @"{text: '~title', datafield: '~datafield'~displayfield~width~columntype~createeditor,align:'center'~initeditor
                   }";
                col = MyPageModel.GetStringWithVar(col, vars);
                if (sColumns != "") sColumns += ",";
                sColumns += col;  
            }
            sColumns = "[" + sColumns + "]";

            varsG["columns"] = sColumns; varsG["rowCount"] = rowCount.ToString(); varsG["gridID"] = gridID;  

            scriptCreateGrid = @"
            if(true){
            var theme = ''; var datafields = []; 
            var source =
            {
                localdata: ~rows,
                unboundmode: true,
                totalrecords: ~rowCount,
                datafields: datafields 
            };
            var dataAdapter = new $.jqx.dataAdapter(source);   
            var fixColCss = 'jqx-widget-header';
            if (theme != '') fixColCss += ' jqx-widget-header-' + theme;
               $('#~gridID').jqxGrid(
                {
                    height:150,
                    source:dataAdapter,
                    pageable:false, 
                    sortable:false,
                    editable:true,
                    columnsresize:true,
                    editmode:'selectedcell',
                    selectionmode:'multiplecellsadvanced',
                    theme: theme,
                    columns:~columns 
                });
            }";
            scriptCreateGrid = MyPageModel.GetStringWithVar(scriptCreateGrid, varsG);
            
            string scriptSetFlds = "";string scriptCheckRowEmpty = "";
            foreach (KeyValuePair<string, DataItem> key in Columns)
            {
                DataItem dataItem = key.Value;
                //if (dataItem.necessary)
                {
                    if (scriptCheckRowEmpty != "") scriptCheckRowEmpty += " && ";
                    {
                        scriptCheckRowEmpty += " row1." + key.Key + "==''";
                    }
                }

                scriptSetFlds += "row." + key.Key + "=row1." + key.Key+";";
                if(dataItem.Label!="" && dataItem.LabelInDB)
                {
                    scriptSetFlds += "row." + dataItem.LabelFld + "=row1." + dataItem.LabelFld + ";";
                }
            }
                scriptSaveGrid = @" 
if(true){
var gridRows=$('#~gridID').jqxGrid('getrows');
var saveRows=new Array();
for(var i=0;i<gridRows.length;i++)
{
    var row1=gridRows[i];
    if(~scriptCheckRowEmpty){
       continue;
    }
   var row={};
   
   ~scriptSetFlds
    saveRows.push(row);
}
formFlds.~gridID=saveRows;

}
            ";
            varsG = new Dictionary<string, string>();
            varsG["gridID"] = gridID;
            varsG["scriptSetFlds"] = scriptSetFlds;
            varsG["scriptCheckRowEmpty"] = scriptCheckRowEmpty; 
            scriptSaveGrid = MyPageModel.GetStringWithVar(scriptSaveGrid, varsG);
        }
          

    }
    public class MyPageModel:PageModel
    {
        public Dictionary<string, string> m_dicFldAreaCtrls = new Dictionary<string, string>();
        public Dictionary<string, DataItem> DataItems = new Dictionary<string, DataItem>();
        public Dictionary<string, DataGrid> Grids  = new Dictionary<string, DataGrid>();
        public string m_selectFromSQL = "";
         
        public string m_idFld = "",m_nameFld="", m_tableName = "";
        public JObject record = new JObject(); 
        public string formatStr(string str, params string[] list)
        {
            str = str.Replace("{", "{{");
            str = str.Replace("}", "}}"); 
            string[] arr = str.Split("@");
            str = "";
            for(int i = 0; i < arr.Length; i++)
            {
                if (i>0) str += "{" + (i-1).ToString() + "}";
                str += arr[i];
            } 
            return string.Format(str, list);
        }
        public string m_showFormScript = "", m_getDataItemsScript  = "", m_saveCloseScript = "";
        public string m_createGridScript = "";public string m_saveGridScript = "";
        public string m_idValue = ""; public bool m_bNewRecord = false;
        public async Task getJavaScripts(CMySbCommand cmd, bool bGetFldAreaCtrls=false)
        {
            Dictionary<string, string> vars = null;
            m_getDataItemsScript  += "var s_value=''; ";
            foreach (KeyValuePair<string, DataItem> key in DataItems)
            {
                DataItem dataItem = key.Value;
                string ctrl = "jqxInput"; if (dataItem.CtrlType != "") ctrl = dataItem.CtrlType;
                if (dataItem.FldArea != "" && (ctrl == "jqxInput" || ctrl == "jqxDropDownTree"))
                {
                    string areaCtrl = ""; string curCtrl = "";
                    curCtrl = "<div><div>" + dataItem.Title + "</div><div id=\""+key.Key+"\">" + dataItem.Title + "</div></div>";
                    if (m_dicFldAreaCtrls.ContainsKey(dataItem.FldArea))
                    {
                        areaCtrl = m_dicFldAreaCtrls[dataItem.FldArea];
                        m_dicFldAreaCtrls[dataItem.FldArea] = areaCtrl + curCtrl;
                    }
                    else
                    {
                        m_dicFldAreaCtrls.Add(dataItem.FldArea, curCtrl);
                    } 
                }
                vars = new Dictionary<string, string>();
                Action<string> SetVarsByDataItems= (flds) =>
                {
                    Type type = typeof(DataItem);
                    string[] arr = flds.Split(",");
                    foreach (string s in arr) {
                        string fld = s.Trim();
                        PropertyInfo prop = type.GetProperty(fld);
                        vars[fld] = prop.GetValue(dataItem).ToString(); 
                    } 
                };
                vars["itemID"] = key.Key;
                vars["ctrl"] = ctrl;
                SetVarsByDataItems(@"buttonUsage,borderShape,dataFields,searchFields,maxRecords,url,source,dropDownWidth,dropDownHeight,checkboxes,mumSelectable,
labelFld,value,label,treePathFld,treePath");
                string showFormScript = "";string getDataItemsScript = "";
                if (ctrl == "jqxInput")
                {
                    showFormScript+= @"$('#~itemID').jqxInput({buttonUsage: '~buttonUsage',borderShape: '~borderShape', showHeader:false,displayMember: 'l',
valueMember: 'v',dataFields:~dataFields, searchFields: ~searchFields, maxRecords:~maxRecords, url:'~url',source:~source});" + "\r\n"; 
                }
                else if (ctrl == "jqxDropDownTree")
                {
                    showFormScript += @"$('#~itemID').jqxDropDownTree({dropDownWidth:~dropDownWidth, dropDownHeight:~dropDownHeight, url:'~url',source:~source,checkboxes:~checkboxes,mumSelectable:~mumSelectable});" + "\r\n"; 
                } 
                if (dataItem.LabelFld!="")
                {
                    showFormScript += @"$('#~itemID').~ctrl('val',{value:'~value',label:'~label'});" + "\r\n";
                    getDataItemsScript += @"s_value=$('#~itemID').~ctrl('val');
                    if(s_value && s_value.value){formFlds.~itemID = s_value.value;formFlds.~labelFld=s_value.label;}" + "\r\n"; 
                }
                else
                {
                    showFormScript += @"$('#~itemID').~ctrl('val','~value');" + "\r\n";
                    getDataItemsScript += @" formFlds.~itemID=$('#~itemID').~ctrl('val');" + "\r\n"; 
                }
               
                if (dataItem.CtrlType == "jqxDropDownTree" && dataItem.TreePathFld != "")
                {
                    showFormScript += @"$('#~itemID').jqxDropDownTree('treePath','~treePath');"+"\r\n";
                    getDataItemsScript += @"formFlds.~treePathFld=$('#~itemID').jqxDropDownTree('treePath');"+"\r\n";
                }
                showFormScript=MyPageModel.GetStringWithVar(showFormScript, vars); m_showFormScript+= showFormScript;
                getDataItemsScript = MyPageModel.GetStringWithVar(getDataItemsScript, vars); m_getDataItemsScript  += getDataItemsScript;
            }

            if (Grids!=null)
            { 
                foreach (KeyValuePair<string, DataGrid> kp in Grids)
                {
                    string createGridScript = "";string saveGridScript = "";
                    await kp.Value.GetScript(cmd, kp.Key,DataItems[m_idFld].Value, ref createGridScript,ref saveGridScript);
                    m_createGridScript += createGridScript;
                    m_saveGridScript += saveGridScript;
                } 
                m_getDataItemsScript  += m_saveGridScript;
            }
            m_getDataItemsScript  += "\r\n" +  "formFlds.isNewRecord = m_bNewRecord;";
            string msgHead = this.GetType().FullName.Replace("Model", "").Split(".").Last();
            m_saveCloseScript = @" 
            var m_bNewRecord = ~m_bNewRecord;
            function btnSave_Clicked()
            {
                var formFlds = { };
                try{
                    if(typeof(eval(checkDataValid))=='function') {
                        var bOK = checkDataValid();
                        if(!bOK) return;
                    }
                }catch(e){}
                ~m_getDataItemsScript 
                $.ajax({
                    url: '../api/~msgHead/Save',
                type: 'POST',
                contentType: 'application/json', 
                data: JSON.stringify(formFlds), 
                success: function(data) {
                        if (data.result == 'OK')
                        {
                            var action = 'update';
                            if (m_bNewRecord)
                            {
                                action = 'add';
                            }
                            var msg = { msgHead: '~msgHead', action: action, record: data.record};
                            window.parent.postMessage(msg, '*'); 
                       } 
                } 
            }); 
            } 
            function btnClose_Clicked()
            {
                var msg = { msgHead: '~msgHead', action: 'close'};
                window.parent.postMessage(msg, '*');
            }";
            vars = new Dictionary<string, string>();
            vars["m_bNewRecord"] = m_bNewRecord.ToString().ToLower(); vars["msgHead"] = msgHead;  vars["m_getDataItemsScript "] = m_getDataItemsScript ;
            m_saveCloseScript = MyPageModel.GetStringWithVar(m_saveCloseScript, vars);  
        }
        public static string getSQLFromDataItems(Dictionary<string, DataItem> dicDataItems,string tableName,string sqlFrom)
        {
            string sql_flds = "";
            foreach (KeyValuePair<string, DataItem> key in dicDataItems)
            {
                DataItem dataItem = key.Value;
                if (dataItem != null)
                {
                    Action<string> getSqlFlds = (fld) =>
                    {
                        if (sql_flds != "") sql_flds += ",";
                        sql_flds += fld;
                    };
                    string valueFld = key.Key; 
                    valueFld = tableName + "." + key.Key; 
                    getSqlFlds(valueFld);
                    if (dataItem.LabelFld != "" && dataItem.LabelInDB)
                    {
                        getSqlFlds(dataItem.LabelFld);
                    }
                    if (dataItem.TreePathFld != "")
                    {
                        getSqlFlds(dataItem.TreePathFld);
                    }
                }
            }
            string sql = "select " + sql_flds + " " + sqlFrom;
            return sql;
        }
        public async Task<bool> getDataItemsFromDB(CMySbCommand cmd, string sqlFrom)
        { 
            string sql = getSQLFromDataItems(DataItems,m_tableName, sqlFrom);
            cmd.CommandText = sql;
            CMySbDataReader dr =await cmd.ExecuteReaderAsync();
            if (dr.Read())
            {
                foreach (KeyValuePair<string, DataItem> key in DataItems)
                {
                    DataItem dataItem = key.Value;  
                    object ov = dr[key.Key];
                    if (ov != DBNull.Value) dataItem.Value = ov.ToString().Trim();
                    if(dataItem.LabelFld!="" )
                    {
                        if (dataItem.Source=="null")
                        {
                            ov = dr[dataItem.LabelFld];
                            if (ov != DBNull.Value) dataItem.Label = ov.ToString();
                        }
                        else
                        {
                            List<dynamic> lst = JsonConvert.DeserializeObject<List<dynamic>>(dataItem.Source);
                            foreach(dynamic obj in lst)
                            {
                                if (obj.v==dataItem.Value)
                                {
                                    dataItem.Label = obj.l;break;
                                }
                            }   
                        }
                    }
                    if (dataItem.TreePathFld != "")
                    {
                        ov = dr[dataItem.TreePathFld];
                        if (ov != DBNull.Value) dataItem.TreePath = ov.ToString();
                    }
                }
                dr.Close();
                return true;
            }
            else
            {
                dr.Close();
                return false;
            }
        }
       public string getSaveTableSQL(CMySbCommand cmd,dynamic request)
       {
            object o = null;
            string sql = "";
            CDbDealer db = new CDbDealer();
            db.AddField("db_id", "10");
            foreach (KeyValuePair<string, DataItem> key in DataItems)
            { 
                DataItem dataItem = key.Value; 
                o= request[key.Key];
                if (o != null)
                {
                    if (key.Key != m_idFld)
                    { 
                        db.AddField(key.Key, o.ToString()); 
                    }
                    dataItem.Value = o.ToString();
                    if (dataItem.TreePathFld != "")
                    {
                        o = request[dataItem.TreePathFld];
                        if (o != null) dataItem.TreePath = o.ToString();
                        db.AddField(dataItem.TreePathFld, dataItem.TreePath);
                    } 
                }
            }
            string id_value = "";
            o = request.isNewRecord;
            string sss = o.ToString();
            if (o!=null && o.ToString()=="True") 
            { 
                o= request[m_idFld];
                if (o.ToString()== "")
                {
                    cmd.CommandText = "select max("+m_idFld+") from "+ m_tableName + " where (item_clsno ~ '^([0-9]+)$')='t' ";
                    o = cmd.ExecuteScalar();
                    int max_no = 0;
                    if (o != null && o != DBNull.Value)
                    {
                        max_no = Convert.ToInt32(o);
                    }
                    id_value = (max_no++).ToString();
                    DataItems[m_idFld].Value = id_value;
                }
                else
                {
                    id_value = request[m_idFld];
                }
                db.AddField(m_idFld, id_value);
                sql= db.GetInsertSQL(m_tableName);
                m_bNewRecord = true;
            }
            else
            {
                id_value = DataItems[m_idFld].Value;
                sql = db.GetUpdateSQL(m_tableName,m_idFld +"= '"+id_value+"'");
                m_bNewRecord = false;
            }
            record = new JObject();
            foreach (KeyValuePair<string, DataItem> key in DataItems)
            {
                record[key.Key] = key.Value.Value;
            }
            string gridSQL = "";
           
            foreach(KeyValuePair<string,DataGrid> kg in Grids)
            {
                dynamic d= request[kg.Key];
                if (d != null)
                {
                    JArray rows = d;
                   
                    //List<Dictionary<string, string>> rows = (List<Dictionary<string, string>>)d;
                    DataGrid grid = kg.Value;
                    gridSQL += "delete from " + grid.TableName + " where " + grid.IdFld + "='" + DataItems[m_idFld].Value + "';";
                    foreach (JToken row in rows)
                    { 
                        db = new CDbDealer();
                        db.AddField("db_id", "10"); 
                        db.AddField(grid.IdFld, DataItems[grid.IdFld].Value);
                        foreach (KeyValuePair<string, DataItem> kp in grid.Columns)
                        {
                            o= row[kp.Key];
                            string v = "";if (o != null) v = o.ToString();
                            db.AddField(kp.Key, v); 
                        }
                        string sqlInsert = db.GetInsertSQL(grid.TableName)+";";
                        gridSQL += sqlInsert;
                    } 
                }
            }
            sql += gridSQL;
            return sql;
       }
        public async Task initGet(CMySbCommand cmd)
        {
            if (m_idFld != "")
            {
                DataItems[m_idFld].Value = CPubVars.RequestV(Request, m_idFld);
                m_bNewRecord = (DataItems[m_idFld].Value == ""); 
            }
            if (m_bNewRecord)
            { 
                foreach(KeyValuePair<string,DataItem> kp in DataItems)
                { 
                     string v= CPubVars.RequestV(Request, kp.Key);
                   
                    if (v != null)
                    {
                        DataItems[kp.Key].Value = v;
                        if (kp.Value.LabelFld != "")
                        {
                            string l = CPubVars.RequestV(Request, kp.Value.LabelFld);
                            if (l != null) { DataItems[kp.Key].Label = l; }
                        }
                        if (kp.Value.TreePathFld != "")
                        {
                            string l = CPubVars.RequestV(Request, kp.Value.TreePathFld);
                            if (l != null) { DataItems[kp.Key].TreePath = l; }
                        }
                    }

                } 
            }
            else
            {
                // m_postFormScript += "m_bNewRecord=false;";
                string sql= m_selectFromSQL.Replace("~ID",DataItems[m_idFld].Value );
                bool bExist = await getDataItemsFromDB(cmd, sql);
            }
            getJavaScripts(cmd); 
        }
    public static string GetStringWithVar(string str, Dictionary<string, string> vars)
    {
        Dictionary<string, string> vars1 = vars.OrderByDescending(o => o.Key).ToDictionary(o => o.Key, p => p.Value); 
        foreach (KeyValuePair<string, string> kp in vars1)
        {
            str = str.Replace("~" + kp.Key + "", kp.Value);
        }
        return str;
    }
        public async static Task<string> GetJsonFromSQL(string sql,CMySbCommand cmd)
        {
            cmd.CommandText = sql;
            CMySbDataReader dr = await cmd.ExecuteReaderAsync();
            string json = "";
            while (dr.Read())
            {
                string row = "";
                for(int i=0;i<dr.FieldCount;i++)
                {
                    string n = dr.GetName(i);
                    string v = CPubVars.GetTextFromDr(dr, n);
                    if (row != "") { row += ","; }
                    row += "\"" + n + "\":\"" + v + "\"";
                }
                row = "{" + row + "}";
                if (json != "") json += ",";
                json += row;
            }
            dr.Close();
            json = "[" + json + "]";
            return json;
        }
}
  
}
*/