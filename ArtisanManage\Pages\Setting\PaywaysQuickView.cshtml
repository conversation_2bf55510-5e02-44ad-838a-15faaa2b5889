@page
@model ArtisanManage.Pages.Setting.PaywaysQuickViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel"/> 

    <script type="text/javascript">

        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据此对象属性修改对应字段：', rs.data.record);
            var newID = ""; var newName = "";
            if (rs.data.record) { newID = rs.data.record.value.record.sub_id; newName = rs.data.record.value.record.sub_name; }

            if (rs.data.msgHead == "PaywayEdit" ) {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = rs.data.record;
                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');

                        var newID = ""; var newName = "";
                        if (rs.data.record) { newID = rs.data.record.value.record.sub_id; newName = rs.data.record.value.record.sub_name; }
                        if (rs.data.action == "add") {
                            var sltItem = $('#other_sub').jqxTree('findItem', rs.data.record.value.record.mother_id);
                            $('#other_sub').jqxTree('addTo', { value: newID, label: newName }, sltItem.element, false);
                            $('#other_sub').jqxTree('render');   // update the tree.
                            attachContextMenu();
                        }
                    }

                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "sub_name", rs.data.record.value.record.sub_name);
                    var sltItem = $('#other_sub').jqxTree('findItem', rs.data.record.value.record.sub_id);
                    $('#other_sub').jqxTree('updateItem', sltItem, { label: newName });
                    $('#other_sub').jqxTree('render');
                    attachContextMenu();
                }
                $("#popItem").jqxWindow('close');
            }
        
            console.log(rs.data);javascript:;
            QueryData();
        });
        
     //   var m_db_id = "10";

    	//var newCount = 1;

     //       function btnAddItem_click(e) {
     //           $('#popItem').jqxWindow('open');
     //           $("#popItem").jqxWindow('setContent', `<iframe src="${frame}?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

     //   }
        
        function showLog(str) {
            if (!log) log = $("#log");
            log.append("<li class='" + className + "'>" + str + "</li>");
            if (log.children("li").length > 8) {
                log.get(0).removeChild(log.children("li")[0]);
            }
        }
        function getTime() {
            var now = new Date(),
                h = now.getHours(),
                m = now.getMinutes(),
                s = now.getSeconds(),
                ms = now.getMilliseconds();
            return (h + ":" + m + ":" + s + " " + ms);
        }

        var newCount = 1;
        function btnAddClass_click(e) {
            var selectedItem = $('#other_sub').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个科目");
                return;
            }
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="PaywayEdit?operKey=${g_operKey}&mother_id=${selectedItem.value}&mother_name=${selectedItem.label}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

        };

        function btnEditClass_click() {
            var selectedItem = $('#other_sub').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个科目");
                return;
            }
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="PaywayEdit?operKey=${g_operKey}&sub_id=${selectedItem.value}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

        };
        function btnRemoveClass_click(e) {
            var selectedItem = $('#other_sub').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个科目");
                return;
            }
            jConfirm('确定要删除' + selectedItem.label + '吗？', function () {
                $.ajax({
                    type: "POST",
                    url: "../api/PaywaysView/DeleteRecords",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify({ operKey: g_operKey, sub_id: selectedItem.value, rowIDs: selectedItem.value }),
                    success: function (data) {
                        if (data.result == "OK") {
                            var sltItem = $('#other_sub').jqxTree('findItem', data.sub_id);
                            $('#other_sub').jqxTree('removeItem', sltItem, false);
                            $('#other_sub').jqxTree('render');
                            attachContextMenu();
                            bw.toast('删除成功',3000);
                        }
                        else {
                            bw.toast(data.msg,3000);
                        }
                    }
                });
            }, "");


        };

        function btnAddItem_click(e) {
            var selectedItem = $('#other_sub').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个科目");
                return;
            }
            var path = $('#other_sub').jqxTree('getTreePath', selectedItem);
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="PaywayEdit?operKey=${g_operKey}&mother_id=${selectedItem.value}&mother_name=${selectedItem.label}&other_sub=${path}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

        }



        function attachContextMenu() {
            if (!window.contextMenu) window.contextMenu = $("#jqxMenu_1").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            function isRightClick(event) {
                var rightclick;
                if (!event) event = window.event;
                if (event.which) rightclick = (event.which == 3);
                else if (event.button) rightclick = (event.button == 2);
                return rightclick;
            }
            
            // open the context menu when the user presses the mouse right button.
            $("#other_sub li").on('mousedown', function (event) {
                var target = $(event.target).parents('li:first')[0];
                var rightClick = isRightClick(event);
                var contextMenu = event.target.innerText == "全部" ? contextMenu0 : contextMenu1

                if (rightClick && target != null) {
                    $("#other_sub").jqxTree('selectItem', target);
                    var scrollTop = $(window).scrollTop();
                    var scrollLeft = $(window).scrollLeft();

                    contextMenu.jqxMenu('open', parseInt(event.clientX) + 5 + scrollLeft, parseInt(event.clientY) + 5 + scrollTop);

                    return false;
                }
            });

        }

        function onGridRowEdit(rowIndex) {
            var sub_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'i');
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="PaywayEdit?operKey=' + g_operKey + '&sub_id=' + sub_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }
        
    	    var itemSource = {};
        $(document).ready(function () {
            window.contextMenu0 = $("#jqxMenu_0").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            window.contextMenu1 = $("#jqxMenu_1").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });

                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

            $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

                    
                $("#gridItems").on("cellclick", function (event) {
                    // event arguments.
                    var args = event.args;
                    if (args.datafield == "sub_name") {
                        
                        if (args.originalEvent.button == 2) return;
                        var sub_id = args.row.bounddata.i;
                        RowIndex = args.rowindex;
                        if (ForSelect) {
                            var sub_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                            var sub_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "sub_name");
                            var msg = {
                                msgHead: 'PaywaysView', action: 'select', sub_id: sub_id, sub_name: sub_name
                            };
                            window.parent.postMessage(msg, '*');
                        }
                        else {
                            onGridRowEdit(args.rowindex);
                        }
                     }

                }).on("afterDelete", function (event, data) {
                    var sltItem = $('#other_sub').jqxTree('findItem', data);
                    $('#other_sub').jqxTree('removeItem', sltItem, false);
                    $('#other_sub').jqxTree('render');
                    attachContextMenu();           
                });
                $("#Cancel").on('click', function () {
                    for (var i = 0; i < 10; i++) {
                        $('#jqxgrid').jqxGrid('deleterow', i);
                        $('#jqxgrid').jqxGrid('addrow', i, {})
                    }
                });

                $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 400, width: 550, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                //$("#popClass").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 350, width: 550, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                attachContextMenu();




                $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
                });

                QueryData();
    	    });
    </script>


</head>
 
<body> 
    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;">
        <div><input id="searchString" style="font-size:14px; border-radius:6px;border-color:#ddd;border-width:0.5px; width:200px;height:25px;" placeholder="请输入简拼/名称" /></div>
        <div><button onclick="btnAddItem_click()" style="width:100px">新增科目</button></div>
    </div>

    <div style="display:flex;height:100%;">

        <div id='other_sub' style="width:200px;height:calc(100% - 90px);margin-top:20px;margin-bottom:2px;overflow-y:scroll">
        </div>

        <div style="width:calc(100% - 200px);height:100%;margin-left:10px;">

            <div>
                <div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div>
            </div>

            <div id="gridItems" style="margin-top:0px;margin-bottom:2px;width:calc(100% - 10px);height:calc(100% - 90px);"></div>

        </div>


    </div>

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">会计科目</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    
    <div id='jqxMenu_1'>
        <ul>
            <li id="mnuEditClass" onclick="btnEditClass_click()">编辑类</li>
            <li id="mnuAddClass" onclick="btnAddClass_click()">添加下级类</li>
            <li id="mnuRemoveClass" onclick="btnRemoveClass_click()">删除类</li>
        </ul>
    </div>
    <div id='jqxMenu_0'>
        <ul>
            <li id="mnuEditClass" onclick="btnEditClass_click()">编辑类</li>

            <li id="mnuAddClass" onclick="btnAddClass_click()">添加下级类</li>

        </ul>
    </div>



</body>
</html>