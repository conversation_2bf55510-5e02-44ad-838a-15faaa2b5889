using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ArtisanManage.MyJXC;
using ArtisanManage.Pages.Sheets;
using ArtisanManage.Services.SheetService;
using NPOI.HSSF.Record;
using Newtonsoft.Json.Linq;
using System.Linq;
using System.Net.Http;
using System.Net;
using Microsoft.CodeAnalysis.Operations;
using NPOI.SS.Formula.Functions;
using NPOI.POIFS.Crypt.Dsig;
using System.ComponentModel.Design;
using Org.BouncyCastle.Asn1.X9;
using System.Threading.Tasks.Dataflow;
using Microsoft.VisualStudio.TextTemplating;
using Microsoft.AspNetCore.Http;
using NPOI.XSSF.UserModel;
using System.IO;
using System.Text;
using SixLabors.ImageSharp.Memory;
using System.Xml;
namespace ArtisanManage.Pages.BaseInfo
{
    public class BarcodeScaleGroupEditPluModel : PageFormModel
    {

        public BarcodeScaleGroupEditPluModel(CMySbCommand cmd, string company_id = "", string oper_id = "") : base(Services.MenuId.infoBarcodeScale)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"operate",new DataItem(){Title="操作",FldArea="divHead",SaveToDB=false,GetFromDb=false,Hidden=true}},
                {"barcode_scale_group_no",new DataItem(){Title="条码秤组编号",FldArea="divHead",Width="100"}},
                {"barcode_scale_group_id",new DataItem(){Title="条码秤id",FldArea="divHead",GetFromDb=false,Hidden=true,HideOnLoad=true}},
                {"barcode_scale_group_name",new DataItem(){Title="条码秤名称",FldArea="divHead",Necessary=true,GetFromDb=true}},

            };
            m_idFld = "barcode_scale_group_id"; m_nameFld = "barcode_scale_group_name"; AllowSameName = true;
            m_tableName = "info_barcode_scale_group";
            m_selectFromSQL = @" from info_barcode_scale_group  where company_id=~COMPANY_ID and barcode_scale_group_id=~ID ";
            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridBarcodeGroupPlu" ,new FormDataGrid(){
                    AutoAddRow=true,
                    AllowDragRow=true,
                    AllowInsertRemoveRow=true,
                    MinRows=20,
                    SelectionMode="multiplecellsadvanced", //选中单元格时，默认选中行，设置后为选中某一格
                    EditMode = "click",
                    Columns = new Dictionary<string, DataItem>()
                    {
                        {"barcode_scale_group_id",new DataItem(){Title="条码秤组",FldArea="divHead",Hidden=true} },
                        //{"plu_no",new DataItem(){Title="plu编码",Width="200"} },
                        {"plu_no", new DataItem()
                            {
                                Title = "plu编号 <span title=\"开头的0不作数\" style=\"cursor: help;\">*</span>",
                                Width = "200"
                            }
                        },
                        {"plu_id",new DataItem(){Title="plu_id",SaveToDB=false,Hidden=true} },
//                        {"item_id",new DataItem(){Title="商品名称",Width="300",LabelFld="item_name",ButtonUsage="list",CompareOperator="=",ShowDropDownColumnsHeader=true, datafields="[{datafield: 'item_name', text: '品名', width: 180,hidden: false},{datafield: 'barcode', text: '条码', width: 120,hidden: false}]", SearchFields="['item_name','py_str','barcode']", QueryOnChange=true ,
//                   QueryByLabelLikeIfIdEmpty=true, SqlForOptions =CommonTool.selectItemWithBarcode1,SaveToDB=false,DropDownWidth = "300",JSGetEditorValue =  @"
//function(row, cellvalue, editor) { 
//    var v = editor.find('input').val();
//    console.log(v.item_id,v.item_name)
//    var grid = '#gridItems'     
    
//    if (v.item_id) {
//        $(grid).jqxGrid('setcellvalue', row, 'item_id', v.item_id);
//    }
//    else {
//    $(grid).jqxGrid('setcellvalue', row, 'item_id', '');
//    return ''
//    }
//    if (v.label === null || v.label === undefined){
//return ''}
//return v.label
//}
//"}},
                        {"item_id",new DataItem(){Title="商品id",Necessary=true, Width="0",Hidden=true,HideOnLoad = true}},
                                                {"item_name",new DataItem(){Title="商品名称",Necessary=true,Width="150",ButtonUsage="event",
                            SqlForOptions=@"select item_id,item_name from info_item_prop where info_item_prop.company_id=~COMPANY_ID",
                            JSInitEditor=@"initeditor_item_name",
                            JSCreateEditor =  @"createeditor_item_name",
                            JSGetEditorValue = @"function(row, cellvalue, editor) {
                                var v = editor.find('input').val();
                                return v;
                            }"
                        }},
                        {"item_no",new DataItem(){Title="商品编号",Width="200",EditableInFormGrid=false} },
                        {"retail_price",new DataItem(){Title="零售价",Width="100",EditableInFormGrid=false} },
                        {"unit_no",new DataItem(){Title="单位（小）",Width="100",EditableInFormGrid=false} },

                    },
                   TableName="info_barcode_scale_group_plu",
                   IdFld="item_id",
                   SelectFromSQL=@"from info_barcode_scale_group_plu 
                                   left join info_barcode_scale_group on info_barcode_scale_group_plu.barcode_scale_group_id = info_barcode_scale_group.barcode_scale_group_id and  info_barcode_scale_group.company_id = ~COMPANY_ID
                                   where info_barcode_scale_group.barcode_scale_group_id=~ID"
                }}
            };
        }
        public async Task OnGet(string operate)
        {
            
            await InitGet(cmd);


        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class BarcodeScaleGroupEditPluController : BaseController
    {
        public BarcodeScaleGroupEditPluController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BarcodeScaleGroupEditPluModel model = new BarcodeScaleGroupEditPluModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            BarcodeScaleGroupEditPluModel model = new BarcodeScaleGroupEditPluModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<JsonResult> GetRegions(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            string regionsql = @$"SELECT region_name,region_id,mother.mother_id FROM info_region
LEFT JOIN (SELECT region_id as mother_id from info_region where company_id={companyID} and region_name='全部' limit 1) mother
on info_region.mother_id=mother.mother_id
where company_id={companyID} and info_region.mother_id=mother.mother_id";
            dynamic regionData = await CDbDealer.GetRecordsFromSQLAsync(regionsql, cmd);

            return new JsonResult(new { result = "OK", regionData });

        }
        [HttpGet]
        public async Task<IActionResult> GetSupcust(string operKey, string query, string itemID, string centerLng, string centerLat, string radius, string region)
        {
            var condi = "";
            if (query.IsValid()) condi += $"and (sup_name ilike '%{query}%' or py_str ilike '%{query}%')";
            if (itemID.IsValid()) condi += $" and supcust_id in ({itemID})";
            if (centerLng.IsValid() && centerLat.IsValid())
            {
                condi += @$"and st_dwithin(st_transform(st_geometryfromtext('POINT('|| info_supcust.addr_lng ||' '||info_supcust.addr_lat ||')',4326),26986),st_transform(st_geometryfromtext('POINT({centerLng} {centerLat})',4326),26986),{radius})";
            }
            //and other_region like '%{region}%' 
            if (region.IsValid())
            {
                condi += @$"and other_region like '%{region}%' ";
            }
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $"select supcust_id,sup_name,py_str,addr_lng,addr_lat,sup_addr,boss_name,mobile, (SELECT count(1) from info_visit_day_client where company_id={companyID} and supcust_id=info_supcust.supcust_id ) day_info_count from info_supcust where company_id={companyID} and COALESCE(status,'1')='1' {condi} ";
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", records });
        }
        [HttpGet]
        public async Task<IActionResult> GetItemsInfo(string operKey, string query, string itemID, string centerLng, string centerLat, string radius)
        {
            var condi = "";
            if (query.IsValid()) condi += $"and (item_name ilike '%{query}%' or py_str ilike '%{query}%')";
            if (itemID.IsValid()) condi += $" and ip.item_id in ({itemID})";
            if (centerLng.IsValid() && centerLat.IsValid())
            {
                condi += @$"and st_dwithin(st_transform(st_geometryfromtext('POINT('|| info_supcust.addr_lng ||' '||info_supcust.addr_lat ||')',4326),26986),st_transform(st_geometryfromtext('POINT({centerLng} {centerLat})',4326),26986),{radius})";
            }            
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $"select ip.item_id,item_name,item_no,imu.retail_price,imu.unit_no from info_item_prop ip left join info_item_multi_unit imu on ip.item_id=imu.item_id and imu.unit_factor=1 and imu.company_id={companyID} where ip.company_id={companyID} and COALESCE(status,'1')='1' {condi} ";
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", records });
        }

        [HttpGet]
        public async Task<IActionResult> GetRememberOption(string operKey)
        {


            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string sql = $"SELECT options from options_remembered where company_id='{companyID}' and oper_id='{operID}'";
            dynamic options = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", data = options });
        }
        [HttpPost]
        public async Task<IActionResult> RememberOption([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            dynamic obj = new
            {
                visitDay = new
                {
                    zoom = data.zoom,
                    lng = data.lng,
                    lat = data.lat
                }
            };
            string visitDayJson = JsonConvert.SerializeObject(obj);
            string sql = @$"insert into options_remembered (company_id,oper_id,options) values ({companyID},{operID},'{visitDayJson}'::jsonb) on conflict(company_id,oper_id) do update set options=jsonb_merge(options_remembered.options,'{visitDayJson}'::jsonb);";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new
            {
                msg = "OK"
            });
            //   update users set data = data::jsonb || '{"uptate_minute": "10"}'::jsonb where id = 3;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            if (request["operate"] == "copy")
            {
                request["isNewRecord"] = true;
            }
            if (request.gridBarcodeGroupPlu != null)
            {
                foreach (var item in request.gridBarcodeGroupPlu)
                {
                    if (item.plu_no != "")
                    {
                        if (!int.TryParse(item.plu_no.ToString(), out int pluNo) || pluNo > 9999)
                        {
                            return new JsonResult(new { result = "Error", msg = "PLU编号不能超过四位数" });
                        }
                    }
                    if (item.item_no != "")
                    {
                        if (!int.TryParse(item.item_no.ToString(), out int itemNo) || itemNo > 999)
                        {
                            return new JsonResult(new { result = "Error", msg = "商品编号不能超过五位数" });
                        }
                    }
                    else
                    {
                        return new JsonResult(new { result = "Error", msg = "商品编号请在商品档案录入！不能为空！" });
                    }
                    if (item.retail_price == "")
                    {
                        return new JsonResult(new { result = "Error", msg = "零售价请在商品档案录入！不能为空！" });
                    }
                    if (item.unit_no == "")
                    {
                        return new JsonResult(new { result = "Error", msg = "单位请在商品档案录入！不能为空！" });
                    }
                }
            }
            BarcodeScaleGroupEditPluModel model = new BarcodeScaleGroupEditPluModel(cmd);
            Security.GetInfoFromOperKey((string)request.operKey, out string companyID);
            return await model.SaveTable(cmd, request);
        }
    }
}
