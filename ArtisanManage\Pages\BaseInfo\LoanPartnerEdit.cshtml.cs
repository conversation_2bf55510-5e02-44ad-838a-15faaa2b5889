using System;
using System.Collections.Generic;
using ArtisanManage.Services;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;

namespace ArtisanManage.Pages.BaseInfo 
{
    public class LoanPartnerEditModel : PageFormModel
    {
        public LoanPartnerEditModel(CMySbCommand cmd, string company_id="",string oper_id="") : base(Services.MenuId.infoLoanPartner)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"partner_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"partner_name",new DataItem(){Title="名称",Necessary=true,FldArea="divHead"}},
                {"py_str",new DataItem(){Title="助记名",FldArea="divHead"}},
                {"mobile",new DataItem(){Title="联系电话",FldArea="divHead"}},
                {"partner_flag",new DataItem(){Title="借贷款类型",DropDownHeight="90",DropDownWidth="90",FldArea="divHead",ButtonUsage="list", Necessary=true, Value="B",Label="贷款方",Source = "[{v:'B',l:'贷款方'}]"}},//,{v:'L',l:'借款方'},{v:'BL',l:'借款方/贷款方'}暂时不做借款逻辑
                {"status",new DataItem(){Title="状态",LabelFld="cls_status_name",DropDownHeight="60",DropDownWidth="60",FldArea="divHead",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}},            

            };
 
            m_idFld = "partner_id"; m_nameFld = "partner_name";
            m_tableName = "info_loan_partner";
            m_selectFromSQL = "from info_loan_partner where partner_id='~ID'";

            
        }

        public async Task OnGet()
        { 
            await InitGet(cmd);  
        } 
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class LoanPartnerEditController : BaseController
    {       
        public LoanPartnerEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues)
        {
            LoanPartnerEditModel model = new LoanPartnerEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            LoanPartnerEditModel model = new LoanPartnerEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            LoanPartnerEditModel model = new LoanPartnerEditModel(cmd);
            Security.GetInfoFromOperKey((string)request.operKey, out string companyID);
            cmd.CommandText = $"select partner_flag from info_loan_partner  where company_id={companyID}  and  partner_name='{request.partner_name}';";
            var partner_flag = await cmd.ExecuteScalarAsync();
            var isNewRecord = request.isNewRecord.ToString();
            if (partner_flag != null && isNewRecord == "True")
            {
                return new JsonResult(new { result = "Error", msg = "借贷款单位档案已存在同名的记录" });
            }

            return await model.SaveTable(cmd, request);
        }
        
    }
}