using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;
using ArtisanManage.Services;
using System.Dynamic;

namespace ArtisanManage.Pages.Setting
{
    public class PaywayQuickEditModel : PageFormModel
    { 
        public PaywayQuickEditModel(CMySbCommand cmd, string company_id = "", string oper_id = "") : base(Services.MenuId.paywaysQuickView)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"sub_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"sub_name",new DataItem(){Title="科目名称",Necessary=true,FldArea="divHead"}},
                {"sub_code",new DataItem(){Title="科目代码",Necessary=true,FldArea="divHead"}},
                {"order_index",new DataItem(){Title="顺序号",FldArea="divHead" }},
                {"mother_id",new DataItem(){Title="上级科目", LabelFld="mother_name",CtrlType="jqxDropDownTree",MumSelectable=true,FldArea="divHead",TreePathFld="other_sub",
                   SqlForOptions="select sub_id as v,sub_name as l,mother_id as pv from cw_subject where company_id= ~COMPANY_ID "
                }},
                {"sub_type",new DataItem(){Title="类型",FldArea="divHead",LabelFld="sub_type_name",LabelInDB=false,ButtonUsage="list",Source = "[{v:'ZC',l:'费用支出类型'},{v:'YS',l:'预收账户'},{v:'YF',l:'预付款账户'},{v:'QT',l:'其他'},{v:'QTSR',l:'其他收入'}]",CompareOperator="="}},
                {"status",new DataItem(){Title="状态",LabelFld="cls_status_name",FldArea="divHead",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}},
                {"is_order",new DataItem(){FldArea="divHead",Title="定货会账户",CtrlType="jqxCheckBox",SqlFld = "is_order"}},
                {"subUsed",new DataItem(){Title="已使用",FldArea="divHead",GetFromDb=false,SaveToDB=false,Hidden=true}},
            };

            m_idFld = "sub_id"; m_nameFld = "sub_name";
            m_tableName = "cw_subject";
            m_selectFromSQL = @"from cw_subject left join (select sub_id as mother_id1,sub_name as mother_name from cw_subject where company_id= ~COMPANY_ID ) mc on mc.mother_id1=cw_subject.mother_id
                                where sub_id='~ID' and company_id= ~COMPANY_ID";
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            string mother_id = Request.Query["mother_id"];
            string sql = "";
            if (DataItems["sub_code"].Value == "" &&!string.IsNullOrEmpty(mother_id))
            {
                sql = $"select  max(sub_code) v from cw_subject where company_id={company_id} and mother_id = {mother_id}";
                dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (record.v != "")
                {
                    int new_sub_code = Convert.ToInt32(record.v) + 1;
                    DataItems["sub_code"].Value = new_sub_code.ToString();

                }
                else
                {
                    sql = $"select  sub_code v from cw_subject where company_id={company_id} and sub_id = {mother_id}";
                    record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    DataItems["sub_code"].Value =(String)record.v + "01";
                }
            }
            var hasBalance = false;
            var sub_id = DataItems["sub_id"].Value;
            var sub_type = DataItems["sub_type"].Value;
            if(sub_id != "" && (sub_type=="YS"||sub_type=="YF"))
            {
                sql = $"SELECT sub_id FROM prepay_balance WHERE company_id = {company_id} and sub_id = {sub_id} limit 1";
                var record = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                
                if (record.Count>0) hasBalance = true;

            }

            if (hasBalance)
            {
                DataItems["sub_type"].Disabled = true;
                DataItems["subUsed"].Value = "true";
            }

        }
        public async Task OnGet()
        { 
            await InitGet(cmd); 
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class PaywayQuickEditController : BaseController
    {
        public PaywayQuickEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            var model = new PaywayQuickEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            var model = new PaywayQuickEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic data)
        {
            string msg = "";
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string sql = "";

            var model = new PaywayQuickEditModel(cmd);
            if (data.sub_id != "")
            {
                sql = $"select c.sub_id from cw_subject c left join items_ordered_balance b on b.prepay_sub_id = c.sub_id where c.company_id = {companyID} and is_order is true and b.prepay_sub_id is not null and c.sub_id = {data.sub_id} ";
                dynamic subID = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (subID != null && data.is_order != "true")
                {
                    msg = "当前定货款正在使用中，请勿调整";
                    return new JsonResult(new { result = "Error", msg });
                }

                if (data.sub_id == data.mother_id)
                {
                    msg = "不能选择自己作为自己的父级科目";
                    return new JsonResult(new { result = "Error", msg });
                }
            }
            
            if (msg == "")
            {
                JsonResult record = await model.SaveTable(cmd, data);
                //JObject record = await model.SaveTable(cmd, data);
                //string sub_id = record["sub_id"].ToString();
                string sub_id = (record.Value as dynamic).record.sub_id;
                string result = (record.Value as dynamic).result;
                msg = (record.Value as dynamic).msg;
                if (result == "OK")
                {
                    CDbDealer db = new CDbDealer();
                    string sub_type = data.sub_type;
                    string payway_index = data.order_index;
                    int sub_code = Convert.ToInt32(data.sub_code);
                    //是否定货会账户
                    if ((sub_type == "YS" || sub_type == "QT" || sub_type == "YF") && sub_code > 1000)
                    {
                       // CMySbConnection conn = new CMySbConnection(CPubVars.ConnString);
                       // conn.Open();
                       // CMySbCommand g_cmd = new CMySbCommand("", conn);
                        db.AddFields(data, "sub_name,other_sub,mother_id,is_order");
                        db.AddField("company_id", companyID);
                        db.AddField("sub_id", sub_id);
                        db.AddField("payway_type", sub_type);
                        db.AddField("payway_index", payway_index);
                        dynamic subID = await CDbDealer.Get1RecordFromSQLAsync($"select sub_id from cw_subject where company_id = {companyID} and sub_id = {sub_id}", cmd);
                        if (subID == null) sql = db.GetInsertSQL("info_pay_way");
                        else sql = db.GetUpdateSQL("info_pay_way", $"sub_id='{sub_id}' and company_id = {companyID}");
                        cmd.CommandText = sql;
                        await cmd.ExecuteNonQueryAsync();
                       // conn.Close();
                    }
                    else
                    {
                        dynamic unneededSubID = await CDbDealer.Get1RecordFromSQLAsync($"select p.sub_id from info_pay_way p left join cw_subject c on p.sub_id = c.sub_id where p.company_id = {companyID} and p.sub_id = {sub_id} and (c.sub_type = 'ZC' or c.sub_type is null) and p.payway_type in ('QT','YS','YF')", cmd);
                        if (unneededSubID != null)
                        {
                            cmd.CommandText = $"delete from info_pay_way where company_id = {companyID} and sub_id = {sub_id}";
                            await cmd.ExecuteNonQueryAsync();
                        } 
                    }

                }

                return new JsonResult(new { result, msg, added = model.m_bNewRecord, record });
            }
            else return new JsonResult(new { result = "Error" });
        }
    }
}