﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualBasic;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    /// <summary>
    /// 业务员交账
    /// </summary>

    [Route("AppApi/[controller]/[action]")]
    public class SheetCheckAcount : BaseController
    {
        public SheetCheckAcount(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }



        /// <summary>
        /// 交账
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="strSearch"></param>
        /// <param name="sellerID"></param>
        /// <param name="startRow"></param>
        /// <param name="pageSize"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetCheckSheetList(string operKey, string strSearch, string sellerName,string startRow, string pageSize, string startTime, string endTime, string sellerID, string departID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            bool checkAccountBySender = false;
            cmd.CommandText = $"select setting->>'checkAccountBySender' as check_by_sender from company_setting where company_id={companyID}";
            object ov = await cmd.ExecuteScalarAsync();

            if (ov != null && ov != DBNull.Value)
                checkAccountBySender = Convert.ToBoolean(ov?.ToString().ToLower());

            if (sellerName != null) condi += $" and oper_name ilike '%{sellerName}%' ";
            //if (sellerID != null) condi += $" and oper_id = {sellerID}  ";
            if (sellerID != null)
            {
                condi += $" and oper_id = {sellerID} ";
            }
            else if (departID != null)
            {
                condi += $" and depart_id = {departID}";
            }
            var dateCondi1 = $"start_time >= '{startTime}'  and start_time <= '{endTime}' ";
            var dateCondi2 = $"sm.happen_time >= '{startTime}'  and sm.happen_time <= '{endTime}' ";
            //不含预收支付方式的ID
            var amount = $"round(sum(((case when payway1_type <> 'QT' then 0 else payway1_amount end)+(case when payway2_type <> 'QT' then 0 else payway2_amount end)+(case when payway3_type <> 'QT' then 0 else coalesce(payway3_amount,0) end))* sm.money_inout_flag)::numeric,2)";
            /*  var leftJoin = @$"
  left join 
  (
      select sub_id from info_pay_way where company_id = {companyID} and payway_type = 'YS'
  ) p on (p.sub_id=sm.payway1_id or p.sub_id=sm.payway2_id)	
  WHERE sm.company_id = {companyID}  and sm.approve_time is not null and sm.red_flag is null and  {dateCondi2} 
  and sm.sheet_id not in 
    (
      select business_sheet_id from sheet_check_sheets_detail cd left join sheet_check_sheets_main cm on cd.sheet_id=cm.sheet_id where cd.company_id = {companyID} and approve_time is not null and red_flag is null 
                                      ";
              */
            string tailPart(string sheetType)
            {
                //if (sheetType != "") sheetType += $" and sm.sheet_typ in ({sheetType}) ";
                return @$"
left join 
(
    select sub_id,sub_type as payway1_type from cw_subject where company_id = {companyID}
) p1 on sm.payway1_id=p1.sub_id 	
left join 
(
    select sub_id,sub_type as payway2_type from cw_subject where company_id = {companyID}
) p2 on sm.payway2_id=p2.sub_id 
left join 
(
    select sub_id,sub_type as payway3_type from cw_subject where company_id = {companyID}
) p3 on sm.payway3_id=p3.sub_id
WHERE sm.company_id = {companyID} and sm.sheet_type in ({sheetType}) and sm.approve_time is not null and sm.red_flag is null and  {dateCondi2} 
     and sm.check_account_time is null and sm.sheet_id not in 
      (
        select business_sheet_id from sheet_check_sheets_detail cd left join sheet_check_sheets_main cm on cd.sheet_id=cm.sheet_id where cd.company_id = {companyID} and approve_time is not null and red_flag is null  and business_sheet_type in ({sheetType})
      )
                                    ";
            }
            string tailPart_approved(string sheetType)
            {
                return @$"
left join 
(
   select sub_id,sub_type as payway1_type from cw_subject where company_id = {companyID}
) p1 on sm.payway1_id=p1.sub_id
left join 
(
    select sub_id,sub_type as payway2_type from cw_subject where company_id = {companyID}
) p2 on sm.payway2_id=p2.sub_id
left join 
(
    select sub_id,sub_type as payway3_type from cw_subject where company_id = {companyID}
) p3 on sm.payway3_id=p3.sub_id
left join sheet_check_sheets_detail cd on sm.sheet_id=cd.business_sheet_id and sm.company_id=cd.company_id and cd.business_sheet_type in ({sheetType})
left join 
(
 select *,(sale_amount + get_arrears + get_preget + fee_out + get_prepay) real_get_amount
 from sheet_check_sheets_main 
where company_id = {companyID} and approve_time is not null and red_flag is null
) cm on cd.sheet_id=cm.sheet_id 
WHERE sm.company_id = {companyID}  and sm.sheet_type in ({sheetType})  and sm.approve_time is not null and sm.red_flag is null and  {dateCondi2}  ";

            }

            //var al_sql = $@"and cm.approve_time is not null and cm.red_flag is null";
            //var un_sql = $@"and (cm.approve_time is null or cm.red_flag is not null)";
            //未交账单包含 当天交账单中包含的单据与当天红冲交账单的单据   已交账单就是交账表里有的
            SQLQueue QQ = new SQLQueue(cmd);

            var sql_noLimit = $@"
select oper_id seller_id,oper_name seller_name,status,v_count,(COALESCE(s_count,0)+COALESCE(borrow_count,0)+COALESCE(a_count,0)+COALESCE(p_count,0)+COALESCE(yf_count,0)+COALESCE(f_count,0)+COALESCE(sr_count,0)) s_count,(COALESCE(al_s_count,0)+COALESCE(al_a_count,0)+COALESCE(al_p_count,0)+COALESCE(al_yf_count,0)+COALESCE(al_f_count,0)+COALESCE(al_sr_count,0)) al_s_count,(COALESCE(s_amount,0)+COALESCE(p.preget,0)+COALESCE(yf.prepay,0)+COALESCE(a.arrears,0)+COALESCE(f.fee_out,0)+COALESCE(income,0)) total_amount,
                                 (COALESCE(al_s_amount,0)+COALESCE(ap.al_preget,0)+COALESCE(ayf.al_prepay,0)+COALESCE(ala.al_arrears,0)+COALESCE(alf.al_fee_out,0)+COALESCE(al_income,0)) al_total_amount,
     al_s_check_sheet_ids,al_a_check_sheet_ids,al_p_check_sheet_ids,al_yf_check_sheet_ids,al_f_check_sheet_ids,al_sr_check_sheet_ids
from 
(
    select oper_id,oper_name,status from info_operator WHERE company_id = {companyID} {condi}
) o 
left join 
(
    SELECT seller_id,count(visit_id) v_count from sheet_visit  WHERE company_id = {companyID} and {dateCondi1} group by seller_id 
) v on o.oper_id = v.seller_id 														
left join 
(
    select real_seller_id seller_id,count(sm.sheet_id) s_count,{amount} s_amount 
    from
    ( 
        select DISTINCT sm.*,payway1_type,payway2_type,payway3_type,(case when getter_id is not null then getter_id when ({checkAccountBySender} or order_sheet_id is not null) and senders_id is not null then split_part(replace(senders_id,'undefined,',''), ',',1)::int else seller_id end) real_seller_id
        from sheet_sale_main sm {tailPart("'X','T'")}
    ) sm group by real_seller_id
) tot on tot.seller_id = o.oper_id   
left join 
(
    select real_seller_id seller_id,count(sm.sheet_id) borrow_count,0 borrow_amount 
    from
    ( 
        select DISTINCT sm.*,payway1_type,payway2_type,payway3_type,(case when getter_id is not null then getter_id when ({checkAccountBySender} or order_sheet_id is not null) and senders_id is not null then split_part(replace(senders_id,'undefined,',''), ',',1)::int else seller_id end) real_seller_id
        from borrow_item_main sm {tailPart("'JH','HH'")}
    ) sm group by real_seller_id
) borrow on borrow.seller_id = o.oper_id  
left join 
(
    select sm.getter_id,count(sm.sheet_id) a_count,{amount} arrears
    from 
    (
        select DISTINCT sm.*,payway1_type,payway2_type,payway3_type from sheet_get_arrears_main sm {tailPart("'SK'")}
    ) sm group by sm.getter_id
) a on a.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) p_count,{amount} preget 
    from
    ( 
        select DISTINCT sm.*,payway1_type,payway2_type,payway3_type  from sheet_prepay sm {tailPart("'YS','DH'")} and sm.order_adjust_sheet_id is null
    ) sm group by sm.getter_id
) p on p.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) yf_count,{amount} prepay 
    from
    ( 
        select DISTINCT sm.*,payway1_type,payway2_type,payway3_type from sheet_prepay sm {tailPart("'YF'")}
    ) sm group by sm.getter_id
) yf on yf.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) f_count,{amount} fee_out from 
    (
        select DISTINCT sm.*,payway1_type,payway2_type,payway3_type from sheet_fee_out_main sm {tailPart("'ZC'")}
    ) sm group by sm.getter_id
) f on f.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) sr_count,{amount} income 
    from 
    ( 
        select DISTINCT sm.*,payway1_type,payway2_type,payway3_type from sheet_fee_out_main sm {tailPart("'SR'")}
    ) sm group by sm.getter_id
) sr on sr.getter_id = o.oper_id
left join 
(
    select getter_id,count(sm.sheet_id) al_s_count,{amount} al_s_amount,
          string_agg(distinct chec_sheet_info,',') al_s_check_sheet_ids
    from
    ( 
        select DISTINCT cm.getter_id,  sm.sheet_id,sm.payway1_amount,sm.payway2_amount,sm.payway3_amount,sm.money_inout_flag,payway1_type,payway2_type,payway3_type,(case when ({checkAccountBySender} or order_sheet_id is not null) and senders_id is not null then split_part(replace(senders_id,'undefined,',''), ',',1)::int else seller_id end) real_seller_id,cd.sheet_id::text||'|' ||cm.getter_id || '|' || cm.happen_time || '|' || round(cm.real_get_amount::numeric,2)::text as chec_sheet_info
        from sheet_sale_main sm {tailPart_approved("'X','T'")}
    ) sm group by getter_id

    
) al_tot on al_tot.getter_id = o.oper_id   
left join 
(
    select cm.getter_id,count(sm.sheet_id) al_a_count,{amount} al_arrears,string_agg(distinct cd.sheet_id::text || '|' ||cm.getter_id || '|' || cm.happen_time || '|' || round(cm.real_get_amount::numeric,2)::text,',') al_a_check_sheet_ids
    from sheet_get_arrears_main sm {tailPart_approved("'SK'")}  group by cm.getter_id
) ala on ala.getter_id = o.oper_id
left join 
(
    select cm.getter_id,count(sm.sheet_id) al_p_count,{amount} al_preget,string_agg(distinct cd.sheet_id::text || '|' ||cm.getter_id ||'|' || cm.happen_time || '|' || round(cm.real_get_amount::numeric,2)::text,',') al_p_check_sheet_ids
    from sheet_prepay sm {tailPart_approved("'YS','DH'")} and sm.order_adjust_sheet_id is null  group by cm.getter_id
) ap on ap.getter_id = o.oper_id
left join 
(
    select cm.getter_id,count(sm.sheet_id) al_yf_count,{amount} al_prepay,string_agg(distinct cd.sheet_id::text || '|' ||cm.getter_id ||'|' || cm.happen_time || '|' || round(cm.real_get_amount::numeric,2)::text,',') al_yf_check_sheet_ids 
    from sheet_prepay sm {tailPart_approved("'YF'")}  group by cm.getter_id
) ayf on ayf.getter_id = o.oper_id
left join 
(
    select cm.getter_id,count(sm.sheet_id) al_f_count,{amount} al_fee_out,string_agg(distinct cd.sheet_id::text || '|' ||cm.getter_id ||'|' || cm.happen_time || '|' || round(cm.real_get_amount::numeric,2)::text,',') al_f_check_sheet_ids 
    from sheet_fee_out_main  sm {tailPart_approved("'ZC'")}  group by cm.getter_id
) alf on alf.getter_id = o.oper_id
left join 
(
    select cm.getter_id,count(sm.sheet_id) al_sr_count,{amount} al_income,string_agg(distinct cd.sheet_id::text || '|' ||cm.getter_id ||'|' || cm.happen_time || '|' || round(cm.real_get_amount::numeric,2)::text
,',') al_sr_check_sheet_ids 
    from sheet_fee_out_main sm {tailPart_approved("'SR'")}  group by cm.getter_id
) alsr on alsr.getter_id = o.oper_id

";
            string sql = sql_noLimit + $@"order by oper_id limit {pageSize} offset {startRow}";
            QQ.Enqueue("data", sql);
            sql = @$"select sum(v_count) v_tcount,sum(s_count) s_tcount,sum(total_amount) total,sum(al_total_amount) al_total from ({sql}) tb";
            QQ.Enqueue("total", sql);
            sql = @$"select count(seller_id) as count from ({sql_noLimit}) tt ";
            QQ.Enqueue("count", sql);
            List<ExpandoObject> records = null;
            List<ExpandoObject> total = null;
            var dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "total")
                {
                    total = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    count = CPubVars.GetTextFromDr(dr, "count");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, records, total, count });
        }
        [HttpGet]
        public async Task<JsonResult> GetCheckSheetList_old(string operKey, string strSearch, string sellerName, string startRow, string pageSize, string startTime, string endTime, string sellerID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            bool checkAccountBySender = false;
            cmd.CommandText = $"select setting->>'checkAccountBySender' as check_by_sender from company_setting where company_id={companyID}";
            object ov = await cmd.ExecuteScalarAsync();

            if (ov != null && ov != DBNull.Value)
                checkAccountBySender = Convert.ToBoolean(ov?.ToString().ToLower());

            if (sellerName != null) condi += $" and oper_name ilike '%{sellerName}%' ";
            if (sellerID != null) condi += $" and oper_id = {sellerID}  ";
            var dateCondi1 = $"start_time >= '{startTime}'  and start_time <= '{endTime}' ";
            var dateCondi2 = $"sm.happen_time >= '{startTime}'  and sm.happen_time <= '{endTime}' ";
            //不含预收支付方式的ID
            var amount = $"sum(((case when payway1_type  <> 'QT' then 0 else payway1_amount end)+(case when payway2_type <> 'QT' then 0 else payway2_amount end))* sm.money_inout_flag)";
            /*  var leftJoin = @$"
  left join 
  (
      select sub_id from info_pay_way where company_id = {companyID} and payway_type = 'YS'
  ) p on (p.sub_id=sm.payway1_id or p.sub_id=sm.payway2_id)	
  WHERE sm.company_id = {companyID}  and sm.approve_time is not null and sm.red_flag is null and  {dateCondi2} 
  and sm.sheet_id not in 
    (
      select business_sheet_id from sheet_check_sheets_detail cd left join sheet_check_sheets_main cm on cd.sheet_id=cm.sheet_id where cd.company_id = {companyID} and approve_time is not null and red_flag is null 
                                      ";
              */
            string tailPart(string sheetType)
            {
                //if (sheetType != "") sheetType += $" and sm.sheet_typ in ({sheetType}) ";
                return @$"
left join 
(
    select sub_id,sub_type as payway1_type from cw_subject where company_id = {companyID}
) p1 on sm.payway1_id=p1.sub_id 	
left join 
(
    select sub_id,sub_type as payway2_type from cw_subject where company_id = {companyID}
) p2 on sm.payway2_id=p2.sub_id 
WHERE sm.company_id = {companyID}  and sm.sheet_type in ({sheetType}) and sm.approve_time is not null and sm.red_flag is null and  {dateCondi2} 
      and sm.sheet_id not in 
      (
        select business_sheet_id from sheet_check_sheets_detail cd left join sheet_check_sheets_main cm on cd.sheet_id=cm.sheet_id where cd.company_id = {companyID} and approve_time is not null and red_flag is null  and business_sheet_type in ({sheetType})
      )
                                    ";
            }
            string tailPart_approved(string sheetType)
            {
                return @$"
left join 
(
   select sub_id,sub_type as payway1_type from cw_subject where company_id = {companyID}
) p1 on sm.payway1_id=p1.sub_id
left join 
(
    select sub_id,sub_type as payway2_type from cw_subject where company_id = {companyID}
) p2 on sm.payway2_id=p2.sub_id
left join sheet_check_sheets_detail cd on sm.sheet_id=cd.business_sheet_id and sm.company_id=cd.company_id and cd.business_sheet_type in ({sheetType})
left join (select *,(sale_amount + return_amount + get_arrears + get_preget + fee_out + get_prepay) real_get_amount from sheet_check_sheets_main where company_id = {companyID} and approve_time is not null and red_flag is null) cm on cd.sheet_id=cm.sheet_id 
WHERE sm.company_id = {companyID}  and sm.sheet_type in ({sheetType})  and sm.approve_time is not null and sm.red_flag is null and  {dateCondi2}  ";

            }

            //var al_sql = $@"and cm.approve_time is not null and cm.red_flag is null";
            //var un_sql = $@"and (cm.approve_time is null or cm.red_flag is not null)";
            //未交账单包含 当天交账单中包含的单据与当天红冲交账单的单据   已交账单就是交账表里有的
            SQLQueue QQ = new SQLQueue(cmd);

            var sql_noLimit = $@"
select oper_id seller_id,oper_name seller_name,status,v_count,(COALESCE(s_count,0)+COALESCE(a_count,0)+COALESCE(p_count,0)+COALESCE(yf_count,0)+COALESCE(f_count,0)+COALESCE(sr_count,0)) s_count,(COALESCE(al_s_count,0)+COALESCE(al_a_count,0)+COALESCE(al_p_count,0)+COALESCE(al_yf_count,0)+COALESCE(al_f_count,0)+COALESCE(al_sr_count,0)) al_s_count,(COALESCE(s_amount,0)+COALESCE(p.preget,0)+COALESCE(yf.prepay,0)+COALESCE(a.arrears,0)+COALESCE(f.fee_out,0)+COALESCE(income,0)) total_amount,
                                 (COALESCE(al_s_amount,0)+COALESCE(ap.al_preget,0)+COALESCE(ayf.al_prepay,0)+COALESCE(ala.al_arrears,0)+COALESCE(alf.al_fee_out,0)+COALESCE(al_income,0)) al_total_amount,
     al_s_check_sheet_ids,al_a_check_sheet_ids,al_p_check_sheet_ids,al_yf_check_sheet_ids,al_f_check_sheet_ids,al_sr_check_sheet_ids
from 
(
    select oper_id,oper_name,status from info_operator WHERE company_id = {companyID} {condi}
) o 
left join 
(
    SELECT seller_id,count(visit_id) v_count from sheet_visit  WHERE company_id = {companyID} and {dateCondi1} group by seller_id 
) v on o.oper_id = v.seller_id 														
left join 
(
    select real_seller_id seller_id,count(sm.sheet_id) s_count,{amount} s_amount 
    from
    ( 
        select DISTINCT sm.*,payway1_type,payway2_type,(case when ({checkAccountBySender} or order_sheet_id is not null) and senders_id is not null then split_part(replace(senders_id,'undefined,',''), ',',1)::int else seller_id end) real_seller_id
        from sheet_sale_main sm {tailPart("'X','T'")}
    ) sm group by real_seller_id
) tot on tot.seller_id = o.oper_id   
left join 
(
    select sm.getter_id,count(sm.sheet_id) a_count,{amount} arrears
    from 
    (
        select DISTINCT sm.*,payway1_type,payway2_type,payway3_type from sheet_get_arrears_main sm {tailPart("'SK'")}
    ) sm group by sm.getter_id
) a on a.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) p_count,{amount} preget 
    from
    ( 
        select DISTINCT sm.*,payway1_type,payway2_type,payway3_type  from sheet_prepay sm {tailPart("'YS','DH'")} and sm.order_adjust_sheet_id is null
    ) sm group by sm.getter_id
) p on p.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) yf_count,{amount} prepay 
    from
    ( 
        select DISTINCT sm.*,payway1_type,payway2_type,payway3_type from sheet_prepay sm {tailPart("'YF'")}
    ) sm group by sm.getter_id
) yf on yf.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) f_count,{amount} fee_out from 
    (
        select DISTINCT sm.*,payway1_type,payway2_type  from sheet_fee_out_main sm {tailPart("'ZC'")}
    ) sm group by sm.getter_id
) f on f.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) sr_count,{amount} income 
    from 
    ( 
        select DISTINCT sm.*,payway1_type,payway2_type  from sheet_fee_out_main sm {tailPart("'SR'")}
    ) sm group by sm.getter_id
) sr on sr.getter_id = o.oper_id
left join 
(
    select seller_id,count(sm.sheet_id) al_s_count,{amount} al_s_amount,string_agg(distinct cd.sheet_id::text||'|' ||cm.getter_id || '|' || cm.happen_time || '|' || cm.real_get_amount,',') al_s_check_sheet_ids 
    from sheet_sale_main sm {tailPart_approved("'X','T'")}   group by seller_id
) al_tot on al_tot.seller_id = o.oper_id   
left join 
(
    select sm.getter_id,count(sm.sheet_id) al_a_count,{amount} al_arrears,string_agg(distinct cd.sheet_id::text || '|' ||cm.getter_id || '|' || cm.happen_time || '|' || cm.real_get_amount,',') al_a_check_sheet_ids
    from sheet_get_arrears_main sm {tailPart_approved("'SK'")}  group by sm.getter_id
) ala on ala.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) al_p_count,{amount} al_preget,string_agg(distinct cd.sheet_id::text || '|' ||cm.getter_id ||'|' || cm.happen_time || '|' || cm.real_get_amount,',') al_p_check_sheet_ids
    from sheet_prepay sm {tailPart_approved("'YS'")} and sm.order_adjust_sheet_id is null  group by sm.getter_id
) ap on ap.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) al_yf_count,{amount} al_prepay,string_agg(distinct cd.sheet_id::text || '|' ||cm.getter_id ||'|' || cm.happen_time || '|' || cm.real_get_amount,',') al_yf_check_sheet_ids 
    from sheet_prepay sm {tailPart_approved("'YF'")}  group by sm.getter_id
) ayf on ayf.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) al_f_count,{amount} al_fee_out,string_agg(distinct cd.sheet_id::text || '|' ||cm.getter_id ||'|' || cm.happen_time || '|' || cm.real_get_amount,',') al_f_check_sheet_ids 
    from sheet_fee_out_main  sm {tailPart_approved("'ZC'")}  group by sm.getter_id
) alf on alf.getter_id = o.oper_id
left join 
(
    select sm.getter_id,count(sm.sheet_id) al_sr_count,{amount} al_income,string_agg(distinct cd.sheet_id::text || '|' ||cm.getter_id ||'|' || cm.happen_time || '|' || cm.real_get_amount
,',') al_sr_check_sheet_ids 
    from sheet_fee_out_main sm {tailPart_approved("'SR'")}  group by sm.getter_id
) alsr on alsr.getter_id = o.oper_id

";
            string sql = sql_noLimit + $@"order by oper_id limit {pageSize} offset {startRow}";
            QQ.Enqueue("data", sql);
            sql = @$"select sum(v_count) v_tcount,sum(s_count) s_tcount,sum(total_amount) total,sum(al_total_amount) al_total from ({sql}) tb";
            QQ.Enqueue("total", sql);
            sql = @$"select count(seller_id) as count from ({sql_noLimit}) tt ";
            QQ.Enqueue("count", sql);
            List<ExpandoObject> records = null;
            List<ExpandoObject> total = null;
            var dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    records = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "total")
                {
                    total = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    count = CPubVars.GetTextFromDr(dr, "count");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, records, total, count });
        }

        /// <summary>
        /// 查看历史记录汇总
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> LoadAccountHistory_old(string operKey, string sellerName, string operID, string operName, string pageSize, string startRow, string startTime, string endTime, string sheetNo)
        {
            bool firstRequest = false;
            Security.GetInfoFromOperKey(operKey, out string companyID);

            var condi = $" ";

            if (sellerName != null) condi += $" and seller_name ilike '%{sellerName}%' ";
            if (operName != null) condi += $" and oper_name ilike '%{operName}%' ";
            //查看自己单据 权限
            if (operID != null) condi += $" and getter_id = {operID} ";
            if (sheetNo != null) condi += $" and d.sheet_no ilike '%{sheetNo}%' ";
            var pageCondi = "";
            if (pageSize != null && startRow != null) pageCondi += $" limit {pageSize} offset {startRow}";
            if (!endTime.Contains(":"))
            {
                endTime = endTime + " 23:59:59";
            }
            if (startTime != null && endTime != null) condi += $" and cm.happen_time >= '{startTime}' and cm.happen_time <= '{endTime}'";
            if (startRow == "0") firstRequest = true;
            SQLQueue QQ = new SQLQueue(cmd);

            #region .old
            /* 2022.12.13 - 曾尝试使用SQL-LeftJoin获取并计算支付方式与金额
             * 后决定在保存交账单时以JSONB格式存储，旧单据不予显示
             * 旧SQL于此备份，以待它用。
             *  - InfSein.
             * select distinct cm.sheet_id,getter_id as seller_id,seller_name,maker_id as oper_id,oper_name,cm.happen_time,
                        sale_amount,return_amount,sale_total_amount,sale_disc_amount,sale_left_amount,sale_prepay_amount,
                        get_prepay,prepay_total_amount,prepay_disc_amount,prepay_left_amount,
                        get_preget,preget_total_amount,preget_disc_amount,preget_left_amount,
                        get_arrears,arrears_disc_amount,fee_out,income, 
                        pw1.sub_name as payway1_name, pw2.sub_name as payway2_name,
						pw.payway1_amount, pw.payway2_amount
                from  sheet_check_sheets_detail csd 
                left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id
			    left join (
				    select cm.sheet_id,
                            COALESCE( SUM ( payway1_amount * money_inout_flag ), 0 ) as payway1_amount, 
                            COALESCE( SUM ( payway2_amount * money_inout_flag ), 0 ) as payway2_amount
				    from  sheet_check_sheets_detail csd 
				    left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id
				    left join (
					    select       sheet_id,sheet_type,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag from sheet_sale_main        where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_type,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag from sheet_sale_order_main  where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_type,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag from sheet_get_arrears_main where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_type,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag from sheet_prepay           where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_type,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag from sheet_fee_out_main     where company_id = {companyID} and red_flag is null and approve_time is not null 
				    ) d on d.sheet_id = csd.business_sheet_id and d.sheet_type = csd.business_sheet_type
				    where cm.company_id = {companyID} and cm.approve_time is not null and cm.red_flag is null {condi} 
				    group by cm.sheet_id
			    ) pw on pw.sheet_id = cm.sheet_id
                left join (
                        select sheet_id,sheet_no,sheet_type,payway1_id,payway2_id  from sheet_sale_main where company_id = {companyID} and red_flag is null and approve_time is not null
                        union   select sheet_id,sheet_no,sheet_type,payway1_id,payway2_id from sheet_sale_order_main where company_id = {companyID} and red_flag is null and approve_time is not null
                        union   select sheet_id,sheet_no,sheet_type,payway1_id,payway2_id from sheet_get_arrears_main where company_id = {companyID} and red_flag is null and approve_time is not null
                        union   select sheet_id,sheet_no,sheet_type,payway1_id,payway2_id from sheet_prepay where company_id = {companyID} and red_flag is null and approve_time is not null
                        union   select sheet_id,sheet_no,sheet_type,payway1_id,payway2_id from sheet_fee_out_main where company_id = {companyID} and red_flag is null and approve_time is not null ) d 
                    on d.sheet_id = csd.business_sheet_id and d.sheet_type = csd.business_sheet_type
			    left join (select sub_id, sub_name from info_pay_way WHERE company_id = {companyID}) pw1 on pw1.sub_id = d.payway1_id
			    left join (select sub_id, sub_name from info_pay_way WHERE company_id = {companyID}) pw2 on pw2.sub_id = d.payway2_id
                left join (select oper_id,oper_name as seller_name from info_operator WHERE company_id = {companyID}) o on o.oper_id = cm.getter_id
                left join (select oper_id,oper_name from info_operator WHERE company_id = {companyID}) s on s.oper_id = cm.maker_id
                where cm.company_id = {companyID} and cm.approve_time is not null and cm.red_flag is null {condi} order by happen_time desc";
            */
            #endregion
            //var sql_noLimit = @$"select sheet_id,getter_id,oper_name,cm.happen_time,
            //                        sale_amount,return_amount,sale_total_amount,sale_disc_amount,sale_left_amount,sale_prepay_amount,
            //                        get_prepay,prepay_total_amount,prepay_disc_amount,prepay_left_amount,
            //                        get_arrears,arrears_disc_amount,fee_out 
            //                        from sheet_check_sheets_main cm
            //                        left join (select oper_id,oper_name from info_operator WHERE company_id = {companyID} and is_seller = 't') o on o.oper_id = cm.getter_id
            //                        where company_id = {companyID} and red_flag is null {condi} order by happen_time desc";
            var sql_noLimit = $@"
select distinct cm.sheet_id,getter_id as seller_id,seller_name,maker_id as oper_id,oper_name,cm.happen_time,
    sale_amount,return_amount,sale_total_amount,sale_disc_amount,sale_left_amount,sale_prepay_amount,
    get_prepay,prepay_total_amount,prepay_disc_amount,prepay_left_amount,
    get_preget,preget_total_amount,preget_disc_amount,preget_left_amount,
    get_arrears,arrears_disc_amount,fee_out,income, payway 
from  sheet_check_sheets_detail csd 
left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id
left join 
(
          select sheet_id,sheet_no,sheet_type from sheet_sale_main        where company_id = {companyID} and red_flag is null and approve_time is not null
    union select sheet_id,sheet_no,sheet_type from sheet_sale_order_main  where company_id = {companyID} and red_flag is null and approve_time is not null
    union select sheet_id,sheet_no,sheet_type from sheet_get_arrears_main where company_id = {companyID} and red_flag is null and approve_time is not null
    union select sheet_id,sheet_no,sheet_type from sheet_prepay           where company_id = {companyID} and red_flag is null and approve_time is not null
    union select sheet_id,sheet_no,sheet_type from sheet_fee_out_main     where company_id = {companyID} and red_flag is null and approve_time is not null 
) d 
on d.sheet_id = csd.business_sheet_id and d.sheet_type = csd.business_sheet_type
left join 
(
   select oper_id,oper_name as seller_name from info_operator WHERE company_id = {companyID}
) o on o.oper_id = cm.getter_id
left join 
(
   select oper_id,oper_name from info_operator WHERE company_id = {companyID}
) s on s.oper_id = cm.maker_id
where cm.company_id = {companyID} and cm.approve_time is not null and cm.red_flag is null {condi} order by happen_time desc";

            var sql = sql_noLimit + $"{pageCondi}";
            QQ.Enqueue("data", sql);
            if (firstRequest)
            {
                sql = $"select count(*) as itemCount from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
                sql = $@"select
	                COALESCE( SUM ( sale_amount ), 0 ) +   COALESCE ( SUM ( get_arrears ), 0 ) + COALESCE ( SUM ( get_preget ), 0 ) + COALESCE ( SUM ( get_prepay ), 0 ) + COALESCE ( SUM ( fee_out ), 0 ) + COALESCE ( SUM ( income ), 0 )  AS amount 
from({sql_noLimit}) t
";
                QQ.Enqueue("amount", sql);

            }



            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count" && firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
                else if (sqlName == "amount" && firstRequest)
                {
                    dr.Read();
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, itemCount, amount });
        }
        [HttpGet]
        public async Task<JsonResult> LoadAccountHistory(string operKey, string sellerName, string departPath, string operID, string operName, string pageSize, string startRow, string startTime, string endTime, string sheetNo)
        {
            bool firstRequest = false;
            Security.GetInfoFromOperKey(operKey, out string companyID);

            var condi = $" ";
            var extra_leftjoin = $" "; // 在某些筛选条件下添加额外的LEFT-JOIN语句，减少无筛选状态下的负载。
                                       // 后续增加额外LEFT-JOIN时须务必注意与此前代码的冲突!

            if (sellerName != null) condi += $" and seller_name ilike '%{sellerName}%' ";
            if (departPath != null) condi += $" and depart_path ilike '%{departPath}%' ";
            if (operName != null) condi += $" and oper_name ilike '%{operName}%' ";
            //查看自己单据 权限
            if (operID != null) condi += $" and getter_id = {operID} ";
            if (sheetNo != null)
            {
                condi += $" and d.sheet_no ilike '%{sheetNo}%' ";
                extra_leftjoin += $@"
                    left join sheet_check_sheets_detail csd on csd.sheet_id = cm.sheet_id
                    left join (
					    select       sheet_id,sheet_no,sheet_type from sheet_sale_main        where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_no,sheet_type from sheet_sale_order_main  where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_no,sheet_type from sheet_get_arrears_main where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_no,sheet_type from sheet_prepay           where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_no,sheet_type from sheet_fee_out_main     where company_id = {companyID} and red_flag is null and approve_time is not null 
				    ) d on d.sheet_id = csd.business_sheet_id and d.sheet_type = csd.business_sheet_type
                ";
            }
            var pageCondi = "";
            if (pageSize != null && startRow != null) pageCondi += $" limit {pageSize} offset {startRow}";
            if (!endTime.Contains(":"))
            {
                endTime = endTime + " 23:59:59";
            }
            if (startTime != null && endTime != null) condi += $" and cm.happen_time >= '{startTime}' and cm.happen_time <= '{endTime}'";
            if (startRow == "0") firstRequest = true;
            SQLQueue QQ = new SQLQueue(cmd);

            #region .old
            /* 2022.12.13 - 曾尝试使用SQL-LeftJoin获取并计算支付方式与金额
             * 后决定在保存交账单时以JSONB格式存储，旧单据不予显示
             * 旧SQL于此备份，以待它用。
             *  - InfSein.
             * select distinct cm.sheet_id,getter_id as seller_id,seller_name,maker_id as oper_id,oper_name,cm.happen_time,
                        sale_amount,return_amount,sale_total_amount,sale_disc_amount,sale_left_amount,sale_prepay_amount,
                        get_prepay,prepay_total_amount,prepay_disc_amount,prepay_left_amount,
                        get_preget,preget_total_amount,preget_disc_amount,preget_left_amount,
                        get_arrears,arrears_disc_amount,fee_out,income, 
                        pw1.sub_name as payway1_name, pw2.sub_name as payway2_name,
						pw.payway1_amount, pw.payway2_amount
                from  sheet_check_sheets_detail csd 
                left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id
			    left join (
				    select cm.sheet_id,
                            COALESCE( SUM ( payway1_amount * money_inout_flag ), 0 ) as payway1_amount, 
                            COALESCE( SUM ( payway2_amount * money_inout_flag ), 0 ) as payway2_amount
				    from  sheet_check_sheets_detail csd 
				    left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id
				    left join (
					    select       sheet_id,sheet_type,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag from sheet_sale_main        where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_type,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag from sheet_sale_order_main  where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_type,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag from sheet_get_arrears_main where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_type,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag from sheet_prepay           where company_id = {companyID} and red_flag is null and approve_time is not null
                        union select sheet_id,sheet_type,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag from sheet_fee_out_main     where company_id = {companyID} and red_flag is null and approve_time is not null 
				    ) d on d.sheet_id = csd.business_sheet_id and d.sheet_type = csd.business_sheet_type
				    where cm.company_id = {companyID} and cm.approve_time is not null and cm.red_flag is null {condi} 
				    group by cm.sheet_id
			    ) pw on pw.sheet_id = cm.sheet_id
                left join (
                        select sheet_id,sheet_no,sheet_type,payway1_id,payway2_id  from sheet_sale_main where company_id = {companyID} and red_flag is null and approve_time is not null
                        union   select sheet_id,sheet_no,sheet_type,payway1_id,payway2_id from sheet_sale_order_main where company_id = {companyID} and red_flag is null and approve_time is not null
                        union   select sheet_id,sheet_no,sheet_type,payway1_id,payway2_id from sheet_get_arrears_main where company_id = {companyID} and red_flag is null and approve_time is not null
                        union   select sheet_id,sheet_no,sheet_type,payway1_id,payway2_id from sheet_prepay where company_id = {companyID} and red_flag is null and approve_time is not null
                        union   select sheet_id,sheet_no,sheet_type,payway1_id,payway2_id from sheet_fee_out_main where company_id = {companyID} and red_flag is null and approve_time is not null ) d 
                    on d.sheet_id = csd.business_sheet_id and d.sheet_type = csd.business_sheet_type
			    left join (select sub_id, sub_name from info_pay_way WHERE company_id = {companyID}) pw1 on pw1.sub_id = d.payway1_id
			    left join (select sub_id, sub_name from info_pay_way WHERE company_id = {companyID}) pw2 on pw2.sub_id = d.payway2_id
                left join (select oper_id,oper_name as seller_name from info_operator WHERE company_id = {companyID}) o on o.oper_id = cm.getter_id
                left join (select oper_id,oper_name from info_operator WHERE company_id = {companyID}) s on s.oper_id = cm.maker_id
                where cm.company_id = {companyID} and cm.approve_time is not null and cm.red_flag is null {condi} order by happen_time desc";
            */
            #endregion
            //var sql_noLimit = @$"select sheet_id,getter_id,oper_name,cm.happen_time,
            //                        sale_amount,return_amount,sale_total_amount,sale_disc_amount,sale_left_amount,sale_prepay_amount,
            //                        get_prepay,prepay_total_amount,prepay_disc_amount,prepay_left_amount,
            //                        get_arrears,arrears_disc_amount,fee_out 
            //                        from sheet_check_sheets_main cm
            //                        left join (select oper_id,oper_name from info_operator WHERE company_id = {companyID} and is_seller = 't') o on o.oper_id = cm.getter_id
            //                        where company_id = {companyID} and red_flag is null {condi} order by happen_time desc";
            var sql_noLimit = $@"
select cm.sheet_id,getter_id as seller_id,seller_name,depart_path,maker_id as oper_id,oper_name,cm.happen_time,
    sale_amount,return_amount,sale_total_amount,sale_disc_amount,sale_left_amount,sale_prepay_amount,
    get_prepay,prepay_total_amount,prepay_disc_amount,prepay_left_amount,
    get_preget,preget_total_amount,preget_disc_amount,preget_left_amount,
    get_arrears,arrears_disc_amount,fee_out,fee_out_total_amount,income,fee_out_left_amount,fee_out_disc_amount,income_left_amount,income_disc_amount,income_total_amount, payway 
from sheet_check_sheets_main cm 
left join 
(
   select oper_id,oper_name as seller_name,depart_path from info_operator WHERE company_id = {companyID}
) o on o.oper_id = cm.getter_id
left join 
(
   select oper_id,oper_name from info_operator WHERE company_id = {companyID}
) s on s.oper_id = cm.maker_id
{extra_leftjoin}
where cm.company_id = {companyID} and cm.approve_time is not null and cm.red_flag is null {condi} order by happen_time desc";

            var sql = sql_noLimit + $"{pageCondi}";
            QQ.Enqueue("data", sql);
            if (firstRequest)
            {
                sql = $"select count(*) as itemCount from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
                sql = $@"select
	                COALESCE( SUM ( sale_amount ), 0 ) +   COALESCE ( SUM ( get_arrears ), 0 ) + COALESCE ( SUM ( get_preget ), 0 ) + COALESCE ( SUM ( get_prepay ), 0 ) + COALESCE ( SUM ( fee_out ), 0 ) + COALESCE ( SUM ( income ), 0 )  AS amount 
from({sql_noLimit}) t
";
                QQ.Enqueue("amount", sql);

            }



            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count" && firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
                else if (sqlName == "amount" && firstRequest)
                {
                    dr.Read();
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, itemCount, amount });
        }


        /// <summary>
        /// 根据sheetID拉取单据
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="sheetID"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> LoadCheckSheet(string operKey, string sheetID, string pageSize, string happenTime, string getterID, string startRow, string startTime, string endTime)
        {
            //var sheet_type = "(case when sheet_type='X' then '销' else '退' end)";
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            var pageCondi = "";
            if (pageSize != null && startRow != null) pageCondi += $" limit {pageSize} offset {startRow}";
            if (startTime != null && endTime != null) condi += $" and cm.happen_time >= '{startTime}' and cm.happen_time <='{endTime}'";
            var leftJoin = $@"
LEFT JOIN (select supcust_id,sup_name from info_supcust WHERE company_id = {companyID}) s on s.supcust_id = t.supcust_id
LEFT JOIN (select sub_id,sub_name,sub_type payway_type,is_order from cw_subject where company_id = {companyID}) w1 on w1.sub_id = t.payway1_id 
LEFT JOIN (select sub_id,sub_name,sub_type payway_type,is_order from cw_subject where company_id = {companyID}) w2 on w2.sub_id = t.payway2_id 
LEFT JOIN (select sub_id,sub_name,sub_type payway_type,is_order from cw_subject where company_id = {companyID}) w3 on w3.sub_id = t.payway3_id 
";
            SQLQueue QQ = new SQLQueue(cmd);

            var sql = $@"select string_agg(business_sheet_id::text,',') sale_sheets from sheet_check_sheets_detail csd left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id
                           where business_sheet_type  in ('X','T')  and cm.company_id = {companyID} and approve_time is not null and red_flag is null and csd.sheet_id in ({sheetID}) {condi} {pageCondi};";

            QQ.Enqueue("sheet_sale", sql);
			sql = $@"select string_agg(business_sheet_id::text,',') borrow_sheets from sheet_check_sheets_detail csd left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id
                           where cm.company_id = {companyID} and approve_time is not null and red_flag is null and csd.sheet_id in ({sheetID}) {condi} {pageCondi};";

			QQ.Enqueue("sheet_borrow", sql);

			sql = $"select class_id,class_name,mother_id from info_item_class where company_id={companyID};";
            QQ.Enqueue("item_class", sql);

            sql = $"SELECT maker_id,happen_time,start_time,end_time,red_flag,oper_name FROM sheet_check_sheets_main scsm LEFT JOIN info_operator io ON scsm.maker_id = io.oper_id where scsm.company_id={companyID} and scsm.sheet_id in ({sheetID})";
            QQ.Enqueue("print_title", sql);

            sql = $@"select sheet_type,s.supcust_id,s.sup_name,seller_id,seller_name,t.sheet_id,t.sheet_no,t.happen_time,real_get_amount,w1.sub_name payway1_name,payway1_amount,w2.sub_name payway2_name,payway2_amount,w3.sub_name payway3_name,payway3_amount,t.disc_amount,left_amount,make_brief
                    from 
                        (select p.sheet_type,money_inout_flag*p.disc_amount disc_amount,supcust_id,p.getter_id as seller_id,oper_name as seller_name,p.sheet_id,sheet_no,p.happen_time,money_inout_flag*total_amount real_get_amount,money_inout_flag * (total_amount-now_pay_amount-now_disc_amount)::numeric as left_amount,p.payway1_id,p.payway1_amount,p.payway2_id,p.payway2_amount,p.payway3_id,p.payway3_amount,p.make_brief 
                        from sheet_prepay p left join info_operator o on o.oper_id = p.getter_id
                         right JOIN  sheet_check_sheets_detail csd on p.sheet_id = csd.business_sheet_id left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id
                        where cm.company_id = {companyID} and cm.approve_time is not null and cm.red_flag is null and p.sheet_id in (business_sheet_id) and csd.business_sheet_type in ('YS','DH','YF') and csd.sheet_id in ({sheetID}) {condi} ) t  
                    {leftJoin}  ORDER by t.happen_time desc ;";
            QQ.Enqueue("sheet_prepay", sql);
            sql = $@" select s.supcust_id,s.sup_name,seller_id,seller_name,t.sheet_id,t.sheet_no,t.happen_time,real_get_amount,a_now_disc,
w1.sub_name payway1_name,payway1_amount,w1.payway_type as payway1_type,
w2.sub_name payway2_name,payway2_amount,w2.payway_type as payway2_type,
w3.sub_name payway3_name,payway3_amount,w3.payway_type as payway3_type, make_brief from
( select supcust_id,m.getter_id as seller_id,oper_name as seller_name,m.sheet_id,sheet_no,m.happen_time, money_inout_flag * now_pay_amount::numeric as real_get_amount, now_disc_amount as a_now_disc,payway1_id, money_inout_flag*payway1_amount payway1_amount,payway2_id,money_inout_flag*payway2_amount payway2_amount,payway3_id,money_inout_flag*payway3_amount payway3_amount,m.make_brief 
 from sheet_get_arrears_main m left join info_operator o on o.oper_id = m.getter_id right JOIN  sheet_check_sheets_detail csd on m.sheet_id = csd.business_sheet_id  left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id
 where cm.company_id = {companyID} and cm.approve_time is not null and cm.red_flag is null and m.sheet_id in (business_sheet_id) and csd.business_sheet_type in ('SK', 'FK') and csd.sheet_id in ({sheetID}) {condi} ) t
 {leftJoin}  ORDER by t.happen_time desc ";

            QQ.Enqueue("sheet_getArrears", sql);
            //sql = $@" select cm.sheet_id,string_agg(business_sheet_id::text,',') arrear_sheets,cm.happen_time as check_happen_time
            //            from sheet_check_sheets_detail csd left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id
            //            where cm.company_id = {companyID} and cm.approve_time is not null and cm.red_flag is null and csd.business_sheet_type = 'SK' and csd.sheet_id in ({sheetID}) {condi} GROUP BY cm.sheet_id,cm.happen_time
            //         ORDER by cm.happen_time desc ";
            //QQ.Enqueue("sheet_getArrears", sql);

            sql = $@" 
select sheet_type,seller_id,seller_name,t.sheet_id,t.sheet_no,t.happen_time,real_get_amount,now_disc_amount,
   w1.sub_name payway1_name,payway1_amount,
   w2.sub_name payway2_name,payway2_amount,
   w3.sub_name payway3_name,payway3_amount,fee_subs_id,fee_sub_name,fee_sub_amount,t.supcust_id,sup_name,make_brief, left_amount,total_amount 
from 
(
    select o.sheet_type,o.getter_id as seller_id,oper_name as seller_name,o.sheet_id,sheet_no,o.happen_time,money_inout_flag* now_pay_amount as real_get_amount,now_disc_amount,supcust_id,
        o.payway1_id,money_inout_flag *o.payway1_amount::numeric payway1_amount,
        o.payway2_id,money_inout_flag *o.payway2_amount::numeric payway2_amount,
        o.payway3_id,money_inout_flag *o.payway3_amount::numeric payway3_amount, o.make_brief,
        money_inout_flag * (total_amount - now_pay_amount - now_disc_amount)::numeric as left_amount,
        money_inout_flag * (total_amount)::numeric as total_amount
    from sheet_fee_out_main o 
    left join info_operator op on op.company_id={companyID} and op.oper_id = o.getter_id
    right JOIN  sheet_check_sheets_detail csd on csd.company_id={companyID} and o.sheet_id = csd.business_sheet_id 
    left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id and cm.company_id={companyID} 
    where o.company_id = {companyID} and cm.approve_time is not null and cm.red_flag is null and o.sheet_id in (business_sheet_id) and csd.business_sheet_type in ('ZC','SR') and csd.sheet_id in ({sheetID}) {condi}
) t
LEFT JOIN
(
    select sheet_id,string_agg(fee_sub_id::text,',') fee_subs_id,string_agg(fee_sub_name, ',') fee_sub_name,string_agg(fee_sub_amount::text, ',') fee_sub_amount
    from sheet_fee_out_detail d 
    left join (select sub_id, sub_name as fee_sub_name from cw_subject where company_id = {companyID}) c on c.sub_id = d.fee_sub_id 
    where d.company_id = {companyID} group by sheet_id) tt on tt.sheet_id = t.sheet_id
{leftJoin}
ORDER by t.happen_time desc  ";
            QQ.Enqueue("sheet_feeOut", sql);
            //sql = $@" select seller_id,seller_name,t.sheet_id,t.sheet_no,t.happen_time,real_get_amount,now_disc_amount,w1.sub_name payway1_name,payway1_amount,w2.sub_name payway2_name,payway2_amount,fee_subs_id,fee_sub_name,fee_sub_amount,t.supcust_id,sup_name,make_brief 
            //          from (
            //              select o.getter_id as seller_id,oper_name as seller_name,o.sheet_id,sheet_no,o.happen_time,money_inout_flag* total_amount as real_get_amount,now_disc_amount,payway1_id,money_inout_flag *payway1_amount::numeric payway1_amount,supcust_id,
            //                     payway2_id,money_inout_flag *payway2_amount::numeric payway2_amount,o.make_brief
            //                from sheet_fee_out_main o left join info_operator op on op.oper_id = o.getter_id right JOIN  sheet_check_sheets_detail csd on o.sheet_id = csd.business_sheet_id left join sheet_check_sheets_main cm on csd.sheet_id=cm.sheet_id
            //               where cm.company_id = {companyID} and cm.approve_time is not null and cm.red_flag is null and o.sheet_id in (business_sheet_id) and csd.business_sheet_type = 'SR' and csd.sheet_id in ({sheetID}) {condi}) t
            //          LEFT JOIN(select sheet_id,string_agg(fee_sub_id::text,',') fee_subs_id,string_agg(fee_sub_name, ',') fee_sub_name,string_agg(fee_sub_amount::text, ',') fee_sub_amount
            //                      from sheet_fee_out_detail d left join(select sub_id, sub_name as fee_sub_name from cw_subject where company_id = {companyID}) c on c.sub_id = d.fee_sub_id 
            //                     where d.company_id = { companyID} group by sheet_id) tt on tt.sheet_id = t.sheet_id
            //          {leftJoin}
            //          ORDER by t.happen_time desc  ";
            //QQ.Enqueue("sheet_income", sql);

            if (!string.IsNullOrEmpty(happenTime))
            {
                string[] arr = happenTime.Split(" ");
                if (happenTime.Length > 0) happenTime = arr[0];

                sql = $@"select string_agg(sheet_id::text,',') as sheets_id from sheet_check_sheets_main where happen_time>='{happenTime}' and happen_time<='{happenTime} 23:59:59' and getter_id={getterID} and red_flag is null;";
                QQ.Enqueue("day_check_sheets_id", sql);
            }

            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            var saleSheetIDs = "";
            var borrowSheetIDs = "";

			var saleSheets = new List<SheetSale>();
			var borrowSheets = new List<SheetBorrowItem>();
			var prepaySheets = new List<ExpandoObject>();
            var pregetSheets = new List<ExpandoObject>();
            var getArrearSheets = new List<ExpandoObject>();
            var feeOutSheets = new List<ExpandoObject>();
            var incomeSheets = new List<ExpandoObject>();
            var itemClasses = new List<ExpandoObject>();
            dynamic printTitle = null;

            string dayCheckSheetsID = "";
            string start_time = "", end_time = "", happen_time = "", red_flag = "";
            while (QQ.Count > 0)
            {
                var tbl = QQ.Dequeue();
                if (tbl == "sheet_sale")
                {
                    dr.Read();
                    saleSheetIDs = CPubVars.GetTextFromDr(dr, "sale_sheets");
                }
				else if (tbl == "sheet_borrow")
				{
					dr.Read();
					borrowSheetIDs = CPubVars.GetTextFromDr(dr, "borrow_sheets");
				} 
				else if (tbl == "item_class")
                {
                    itemClasses = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "sheet_prepay")
                {
                    List<ExpandoObject> preSheets = CDbDealer.GetRecordsFromDr(dr, false);
                    foreach (dynamic sheet in preSheets)
                    {
                        if (sheet.sheet_type == "YS" || sheet.sheet_type == "DH") pregetSheets.Add(sheet);
                        if (sheet.sheet_type == "YF") prepaySheets.Add(sheet);
                    }
                }
                else if (tbl == "sheet_getArrears")
                {
                    getArrearSheets = CDbDealer.GetRecordsFromDr(dr, false);

                }
                else if (tbl == "sheet_feeOut")
                {
                    List<ExpandoObject> zcSheets = CDbDealer.GetRecordsFromDr(dr, false);
                    foreach (dynamic sheet in zcSheets)
                    {
                        if (sheet.sheet_type == "SR") incomeSheets.Add(sheet);
                        if (sheet.sheet_type == "ZC") feeOutSheets.Add(sheet);
                    }
                }
                //else if (tbl == "sheet_income")
                //{
                //    incomeSheets = CDbDealer.GetRecordsFromDr(dr, false);
                //} 
                else if (tbl == "print_title")
                {
                    printTitle = CDbDealer.Get1RecordFromDr(dr, false);
                    start_time = printTitle.start_time;
                    end_time = printTitle.end_time;
                    happen_time = printTitle.happen_time;
                    red_flag = printTitle.red_flag;
                }
                else if (tbl == "day_check_sheets_id")
                {
                    dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                    if (rec != null)
                        dayCheckSheetsID = rec.sheets_id;
                }
            }
            QQ.Clear();
            if (saleSheetIDs != "")
            {
                SheetSale sheetSale = new SheetSale(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
                List<SheetSale> sheets = null;
                List<SheetSale> dSheets = (List<SheetSale>)await sheetSale.LoadMultiSheets<SheetSale>(cmd, companyID, saleSheetIDs, "sheet_no", "");
                sheets = Newtonsoft.Json.JsonConvert.DeserializeObject<List<SheetSale>>(Newtonsoft.Json.JsonConvert.SerializeObject(dSheets));
                foreach (var sheet1 in sheets)
                {
                    sheet1.total_amount = sheet1.total_amount * sheet1.money_inout_flag;
                    sheet1.payway1_amount = sheet1.payway1_amount * sheet1.money_inout_flag;
                    sheet1.payway2_amount = sheet1.payway2_amount * sheet1.money_inout_flag;
                    sheet1.payway3_amount = sheet1.payway3_amount * sheet1.money_inout_flag;
                    sheet1.now_pay_amount = sheet1.now_pay_amount * sheet1.money_inout_flag;
                    sheet1.real_get_amount = sheet1.real_get_amount * sheet1.money_inout_flag;
                    sheet1.now_disc_amount = sheet1.now_disc_amount * sheet1.money_inout_flag;
                    sheet1.prepay_amount = sheet1.prepay_amount * sheet1.money_inout_flag;
                    //sheet1.left_amount = sheet1.left_amount * sheet1.money_inout_flag;
                }
                saleSheets = sheets;
            }
			if (borrowSheetIDs != "")
			{
				SheetBorrowItem sheet = new SheetBorrowItem(SHEET_BORROW_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
				List<SheetBorrowItem> sheets = null;
				List<SheetBorrowItem> dSheets = (List<SheetBorrowItem>)await sheet.LoadMultiSheets<SheetBorrowItem>(cmd, companyID, borrowSheetIDs, "sheet_no", "");
				sheets = JsonConvert.DeserializeObject<List<SheetBorrowItem>>(JsonConvert.SerializeObject(dSheets));
				foreach (var sheet1 in sheets)
				{
					sheet1.total_amount = sheet1.total_amount * sheet1.money_inout_flag;
					sheet1.payway1_amount = sheet1.payway1_amount * sheet1.money_inout_flag;
					sheet1.payway2_amount = sheet1.payway2_amount * sheet1.money_inout_flag;
					sheet1.payway3_amount = sheet1.payway3_amount * sheet1.money_inout_flag;
					sheet1.now_pay_amount = sheet1.now_pay_amount * sheet1.money_inout_flag;
					sheet1.real_get_amount = sheet1.real_get_amount * sheet1.money_inout_flag;
					sheet1.now_disc_amount = sheet1.now_disc_amount * sheet1.money_inout_flag;
					sheet1.prepay_amount = sheet1.prepay_amount * sheet1.money_inout_flag;
					//sheet1.left_amount = sheet1.left_amount * sheet1.money_inout_flag;
				}
				borrowSheets = sheets;
			}
			string result = "OK";
            string msg = "";
            return Json(new { result, msg, itemClasses, sheet_id = sheetID, start_time, end_time, happen_time, red_flag, saleSheets, borrowSheets, pregetSheets, prepaySheets, getArrearSheets, feeOutSheets, incomeSheets, printTitle, dayCheckSheetsID });
        }


        /// <summary>
        /// 查看费用明细  
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sellerID"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="dateType"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> NewCheckSheet(string operKey, string sellerID, string pageSize, string startRow, string startTime, string endTime, string dateType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            var dateCondi = "";
            bool checkAccountBySender = false;
            cmd.CommandText = $"select setting->>'checkAccountBySender' as check_by_sender  from company_setting where company_id={companyID}";
            object ov = await cmd.ExecuteScalarAsync();

            if (ov != null && ov != DBNull.Value)
                checkAccountBySender = Convert.ToBoolean(ov?.ToString().ToLower());

            if (dateType == "today")
            {
                var today = DateTime.Today.ToText();
                dateCondi += $"and happen_time = '{today}' ";
            }
            else if (dateType == "yesterday")
            {
                var yesterday = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day - 1).ToText();
                dateCondi = $"and happen_time = '{yesterday}'";
            }
            else
            {
                dateCondi = $"and happen_time >= '{startTime}'  and happen_time <= '{CPubVars.PadDateWith2359(endTime)}' ";
            }

            SQLQueue QQ = new SQLQueue(cmd);
            var leftJoin = @$"
LEFT JOIN (select supcust_id,sup_name from info_supcust WHERE company_id = {companyID}) s on s.supcust_id = m.supcust_id
LEFT JOIN (select sub_id,sub_name,sub_type payway_type,is_order from cw_subject where company_id = {companyID}) w1 on w1.sub_id = m.payway1_id 
LEFT JOIN (select sub_id,sub_name,sub_type payway_type,is_order from cw_subject where company_id = {companyID}) w2 on w2.sub_id = m.payway2_id
LEFT JOIN (select sub_id,sub_name,sub_type payway_type,is_order from cw_subject where company_id = {companyID}) w3 on w3.sub_id = m.payway3_id

";


            var sql = $@"
select string_agg(sheet_id::text,',') sale_sheets from 
(
    SELECT m.sheet_id,(case when getter_id is not null then getter_id when ({checkAccountBySender} or order_sheet_id is not null) and senders_id is not null then split_part(replace(senders_id,'undefined,',''), ',',1)::int else seller_id end) real_seller_id 
    FROM sheet_sale_main m 
    LEFT JOIN
    (
        SELECT business_sheet_id from sheet_check_sheets_detail cd left join sheet_check_sheets_main cm on cd.sheet_id=cm.sheet_id where approve_time is not null and red_flag is null and business_sheet_type  in ('X','T')  and cd.company_id = {companyID}
    ) bs on m.sheet_id=bs.business_sheet_id
    WHERE m.check_account_time is null and business_sheet_id is null {dateCondi} and m.company_id = {companyID} and m.approve_time is not null and m.red_flag is null 
) t WHERE real_seller_id = {sellerID}
";

            QQ.Enqueue("sheet_sale", sql);

			sql = $@"
select string_agg(sheet_id::text,',') borrow_sheets from 
(
    SELECT m.sheet_id,(case when getter_id is not null then getter_id when ({checkAccountBySender} or order_sheet_id is not null) and senders_id is not null then split_part(replace(senders_id,'undefined,',''), ',',1)::int else seller_id end) real_seller_id 
    FROM borrow_item_main m 
    LEFT JOIN
    (
        SELECT business_sheet_id from sheet_check_sheets_detail cd left join sheet_check_sheets_main cm on cd.sheet_id=cm.sheet_id where approve_time is not null and red_flag is null and business_sheet_type  in ('JH','HH')  and cd.company_id = {companyID}
    ) bs on m.sheet_id=bs.business_sheet_id
    WHERE m.check_account_time is null and business_sheet_id is null {dateCondi} and m.company_id = {companyID} and m.approve_time is not null and m.red_flag is null 
) t WHERE real_seller_id = {sellerID}
";

			QQ.Enqueue("sheet_borrow", sql);

			sql = $"select class_id,class_name,mother_id from info_item_class where company_id={companyID};";
            QQ.Enqueue("item_class", sql);

            sql = $@"
select m.sheet_type,s.supcust_id,s.sup_name,getter_id as seller_id,oper_name as seller_name,sheet_id,sheet_no,happen_time,money_inout_flag *disc_amount disc_amount ,money_inout_flag * (total_amount-now_pay_amount-now_disc_amount)::numeric as left_amount,money_inout_flag * total_amount::numeric as real_get_amount,
w1.sub_name payway1_name,payway1_amount,w1.payway_type payway1_type,
w2.sub_name payway2_name,payway2_amount,w2.payway_type payway2_type,
w3.sub_name payway3_name,payway3_amount,w3.payway_type payway3_type,m.make_brief 
from sheet_prepay m
left join (select oper_id,oper_name from info_operator where company_id = {companyID}) op on op.oper_id = m.getter_id
{leftJoin}
LEFT JOIN 
(
select business_sheet_id from sheet_check_sheets_detail cd left join sheet_check_sheets_main cm on cd.sheet_id=cm.sheet_id where approve_time is not null and red_flag is null and business_sheet_type in ('YS','DH','YF') and cd.company_id = {companyID}
) bs on m.sheet_id=bs.business_sheet_id
where company_id = {companyID} and m.approve_time is not null and m.red_flag is null and getter_id = {sellerID} 
and m.check_account_time is null and business_sheet_id is null and m.order_adjust_sheet_id is null {dateCondi}  ORDER by m.happen_time,m.sheet_id desc {condi}";

            QQ.Enqueue("sheet_prepay", sql);

            sql = $@"
select m.sheet_type,s.supcust_id,s.sup_name,getter_id as seller_id,
       oper_name as seller_name,sheet_id,sheet_no,happen_time,
       money_inout_flag * now_pay_amount::numeric as real_get_amount,
       money_inout_flag * now_disc_amount ::numeric as a_now_disc,
w1.sub_name payway1_name,w1.payway_type payway1_type,money_inout_flag *payway1_amount payway1_amount,
w2.sub_name payway2_name,w2.payway_type payway2_type,money_inout_flag *payway2_amount payway2_amount,
w3.sub_name payway3_name,w3.payway_type payway3_type,money_inout_flag *payway3_amount payway3_amount,
m.make_brief 

from sheet_get_arrears_main m
left join (select oper_id,oper_name from info_operator where company_id = {companyID}) op on op.oper_id = m.getter_id 
{leftJoin}
LEFT JOIN 
(
select business_sheet_id from sheet_check_sheets_detail cd
left join sheet_check_sheets_main cm on cd.sheet_id=cm.sheet_id 
where approve_time is not null and red_flag is null and business_sheet_type  in ('SK', 'FK')
and cd.company_id = {companyID}
) bs on m.sheet_id=bs.business_sheet_id
where company_id = {companyID} and m.approve_time is not null and m.red_flag is null
and getter_id = {sellerID} and m.check_account_time is null and business_sheet_id is null {dateCondi} ORDER by m.happen_time,m.sheet_id desc {condi}";
            QQ.Enqueue("sheet_get_arrears", sql);

            sql = $@"
select m.sheet_type,getter_id as seller_id,oper_name as seller_name,m.sheet_id,sheet_no,happen_time,money_inout_flag * now_pay_amount::numeric as real_get_amount,money_inout_flag *total_amount total_amount,money_inout_flag *disc_amount now_disc_amount,money_inout_flag * (total_amount - paid_amount - m.disc_amount) left_amount,w1.sub_name payway1_name,money_inout_flag * payway1_amount::numeric as payway1_amount,w1.payway_type payway1_type,m.make_brief,
    w2.sub_name payway2_name,money_inout_flag * payway2_amount::numeric as payway2_amount,w2.payway_type payway2_type,fee_subs_id,fee_sub_name,fee_sub_amount,m.supcust_id,sup_name,m.sheet_type
from sheet_fee_out_main m
LEFT JOIN 
(
    select sheet_id,string_agg(fee_sub_id::text,',') fee_subs_id,string_agg(fee_sub_name,',') fee_sub_name,string_agg(fee_sub_amount::text,',') fee_sub_amount 
            from sheet_fee_out_detail d 
            left join (select sub_id,sub_name as fee_sub_name from cw_subject where company_id = {companyID}) 
                c on c.sub_id = d.fee_sub_id where d.company_id = {companyID}  group by sheet_id) t on t.sheet_id = m.sheet_id
left join 
(
  select oper_id,oper_name from info_operator where company_id = {companyID}
) op on op.oper_id = m.getter_id
{leftJoin}
LEFT JOIN 
(
  select business_sheet_id from sheet_check_sheets_detail cd left join sheet_check_sheets_main cm on cd.sheet_id=cm.sheet_id where approve_time is not null and red_flag is null and business_sheet_type in ('ZC','SR') and cd.company_id = {companyID}
) bs on m.sheet_id=bs.business_sheet_id
where m.company_id = {companyID} and m.approve_time is not null and m.red_flag is null and getter_id = {sellerID}
and m.check_account_time is null and business_sheet_id is null and m.sheet_type in ('ZC','SR') {dateCondi} ORDER by m.happen_time,m.sheet_id desc {condi}";
            QQ.Enqueue("sheet_fee_out", sql);


            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            var saleSheetIDs = "";
			var borrowSheetIDs = "";
			var saleSheets = new List<SheetSale>();
			var borrowSheets = new List<SheetBorrowItem>();
			//List<dynamic> saleSheets = new List<dynamic>();
			var pregetSheets = new List<ExpandoObject>();
            var prepaySheets = new List<ExpandoObject>();
            var getArrearSheets = new List<ExpandoObject>();
            var feeOutSheets = new List<ExpandoObject>();
            var incomeSheets = new List<ExpandoObject>();
            var itemClasses = new List<ExpandoObject>();
            while (QQ.Count > 0)
            {
                var tbl = QQ.Dequeue();
                if (tbl == "sheet_sale")
                {
                    dr.Read();
                    saleSheetIDs = CPubVars.GetTextFromDr(dr, "sale_sheets");
                }
				if (tbl == "sheet_borrow")
				{
					dr.Read();
					borrowSheetIDs = CPubVars.GetTextFromDr(dr, "borrow_sheets");
				}
				else if (tbl == "item_class")
                {
                    itemClasses = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "sheet_prepay")
                {
                    List<ExpandoObject> preSheets = CDbDealer.GetRecordsFromDr(dr, false);
                    foreach (dynamic sheet in preSheets)
                    {
                        if (sheet.sheet_type == "YS" || sheet.sheet_type == "DH") pregetSheets.Add(sheet);
                        if (sheet.sheet_type == "YF") prepaySheets.Add(sheet);
                    }
                }
                else if (tbl == "sheet_get_arrears")
                {
                    getArrearSheets = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (tbl == "sheet_fee_out")
                {
                    List<ExpandoObject> zcSheets = CDbDealer.GetRecordsFromDr(dr, false);
                    foreach (dynamic sheet in zcSheets)
                    {
                        if (sheet.sheet_type == "SR") incomeSheets.Add(sheet);
                        if (sheet.sheet_type == "ZC") feeOutSheets.Add(sheet);
                    }
                }
                else if (tbl == "sheet_income")
                {
                    incomeSheets = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            if (saleSheetIDs != "")
            {
                SheetSale sheetSale = new SheetSale(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
                List<SheetSale> sheets = null;
                List<SheetSale> dSheets = (List<SheetSale>)await sheetSale.LoadMultiSheets<SheetSale>(cmd, companyID, saleSheetIDs, "happen_time", "desc");
                sheets = Newtonsoft.Json.JsonConvert.DeserializeObject<List<SheetSale>>(Newtonsoft.Json.JsonConvert.SerializeObject(dSheets));
                foreach (var sheet1 in sheets)
                {
                    sheet1.total_amount = sheet1.total_amount * sheet1.money_inout_flag;
                    sheet1.payway1_amount = sheet1.payway1_amount * sheet1.money_inout_flag;
                    sheet1.payway2_amount = sheet1.payway2_amount * sheet1.money_inout_flag;
                    sheet1.payway3_amount = sheet1.payway3_amount * sheet1.money_inout_flag;
                    sheet1.now_pay_amount = sheet1.now_pay_amount * sheet1.money_inout_flag;
                    sheet1.real_get_amount = sheet1.real_get_amount * sheet1.money_inout_flag;
                    sheet1.now_disc_amount = sheet1.now_disc_amount * sheet1.money_inout_flag;
                    sheet1.paid_amount = sheet1.paid_amount * sheet1.money_inout_flag;
                    sheet1.disc_amount = sheet1.disc_amount * sheet1.money_inout_flag;
                    sheet1.prepay_amount = sheet1.prepay_amount * sheet1.money_inout_flag;
                    //sheet1.left_amount = sheet1.left_amount * sheet1.money_inout_flag;
                }
                saleSheets = sheets;
            }
			if (borrowSheetIDs != "")
			{
				SheetBorrowItem sheetBorrow = new SheetBorrowItem(SHEET_BORROW_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
				List<SheetBorrowItem> sheets = null;
				List<SheetBorrowItem> dSheets = (List<SheetBorrowItem>)await sheetBorrow.LoadMultiSheets<SheetBorrowItem>(cmd, companyID, borrowSheetIDs, "happen_time", "desc");
				sheets = JsonConvert.DeserializeObject<List<SheetBorrowItem>>(JsonConvert.SerializeObject(dSheets));
				foreach (var sheet1 in sheets)
				{
					sheet1.total_amount = sheet1.total_amount * sheet1.money_inout_flag;
					sheet1.payway1_amount = sheet1.payway1_amount * sheet1.money_inout_flag;
					sheet1.payway2_amount = sheet1.payway2_amount * sheet1.money_inout_flag;
					sheet1.payway3_amount = sheet1.payway3_amount * sheet1.money_inout_flag;
					sheet1.now_pay_amount = sheet1.now_pay_amount * sheet1.money_inout_flag;
					sheet1.real_get_amount = sheet1.real_get_amount * sheet1.money_inout_flag;
					sheet1.now_disc_amount = sheet1.now_disc_amount * sheet1.money_inout_flag;
					sheet1.paid_amount = sheet1.paid_amount * sheet1.money_inout_flag;
					sheet1.disc_amount = sheet1.disc_amount * sheet1.money_inout_flag;
					sheet1.prepay_amount = sheet1.prepay_amount * sheet1.money_inout_flag; 
				}
				borrowSheets = sheets;
			}

			string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet_id = "", happen_time = "", start_time = startTime, end_time = endTime, itemClasses, sellerID, saleSheets, borrowSheets, pregetSheets, prepaySheets, getArrearSheets, feeOutSheets, incomeSheets });
        }



        /// <summary>
        /// 交账 传参：sheetID
        /// </summary>
        /// <param name="data">
        /// {"operKey":"Aa18nTx5omI=","operKey":"Aa18nTx5omI=","getter_id":"1","make_time":"","approve_time":"","make_brief":"","sale_amount":"","return_amount":"","get_arrears":"","get_prepay":"","fee_out":"",
        /// "sheetRows":[{"business_sheet_id":"3","sheet_type":"X"},{"business_sheet_id":"4","business_sheet_type":"T"}]}
        /// 
        /// </param>
        /// <returns></returns>

        [HttpPost]
        public async Task<JsonResult> SubmitCheckAccount([FromBody] dynamic data)
        {
            string msg = "";
            cmd.ActiveDatabase = "";
            string start_time = data.start_time, end_time = data.end_time;
            if (data.sheet_id == "" || data.sheet_id == null)
            {
                Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);

                string now = CPubVars.GetDateText(DateTime.Now);
                string happen_time = now;
                if (data.happen_time != "" && data.happen_time != null) happen_time = data.happen_time;
                CDbDealer db = new CDbDealer();
                string fld = @"make_brief,sale_amount,return_amount,get_arrears,get_prepay,get_preget,fee_out,fee_out_total_amount,fee_out_left_amount,fee_out_disc_amount,sale_total_amount,sale_disc_amount,sale_prepay_amount,sale_left_amount,prepay_total_amount,prepay_disc_amount,prepay_left_amount,preget_total_amount,preget_disc_amount,preget_left_amount,arrears_disc_amount,getter_id,start_time,end_time,income,income_left_amount,income_disc_amount,income_total_amount,arrears_prepay_amount,arrears_total_amount";
                db.AddFields(data, fld);
                db.AddField("maker_id", operID);
                db.AddField("approve_id", operID);
                db.AddField("make_time", now);
                db.AddField("approve_time", now);
                db.AddField("happen_time", happen_time);
                db.AddField("company_id", companyID);

                string sql = "";
                string sqlDetail = "";
                JArray sheetRows = (JArray)data.sheetRows;
                
                //string sheetsInfo = "";

                // 欠条更新，有欠款的有效单子有欠条
                string arrearsBillUpdateSql = "";
                var seller_id = (string)data.getter_id;
                string checkSheetExistSQL = "";
                string redisKey = GetRedisLockKey(sheetRows);
                if (redisKey != "")
                {
                    string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                    await RedisHelper.ExpireAsync(redisKey, 15);
                    if (redisValue == "1")
                    {
                        return Json(new { result = "Error", msg = "请勿重复提交" });
                    }

                }
                string sheet_ids_sale = "", sheet_ids_borrow = "", sheet_ids_prepay = "",sheet_ids_fee = "", sheet_ids_arrears = "";
                string sqlUpdateSheet = "";
                foreach (dynamic row in sheetRows)
                {
                    var db2 = new CDbDealer();
                    db2.AddFields(row, "business_sheet_id,business_sheet_type");
                    db2.AddField("company_id", companyID);
                    db2.AddField("sheet_id", "@sheet_id");
                    sqlDetail += db2.GetInsertSQL("sheet_check_sheets_detail") + ";";
                   // if (sheetsInfo != "") sheetsInfo += ",";
                    //sheetsInfo += String.Concat("'", row.business_sheet_id, row.business_sheet_type, "'");
                    if (row.business_sheet_type=="X" || row.business_sheet_type == "T")
                    {
                        if (sheet_ids_sale != "") sheet_ids_sale += ",";
                        sheet_ids_sale += row.business_sheet_id;
                        
                    }
                    else if (row.business_sheet_type == "JH" || row.business_sheet_type == "HH")
					{
						if (sheet_ids_borrow != "") sheet_ids_borrow += ",";
						sheet_ids_borrow += row.business_sheet_id; 
					}
					else if (row.business_sheet_type == "YS" || row.business_sheet_type == "YF" || row.business_sheet_type == "DH")
                    {
                        if (sheet_ids_prepay != "") sheet_ids_prepay += ",";
                        sheet_ids_prepay += row.business_sheet_id;
                    }
                    else if (row.business_sheet_type == "ZC" || row.business_sheet_type == "SR")
                    {
                        if (sheet_ids_fee != "") sheet_ids_fee += ",";
                        sheet_ids_fee += row.business_sheet_id;
                    }
                    else if (row.business_sheet_type == "SK" || row.business_sheet_type == "FK")
                    {
                        if (sheet_ids_arrears != "") sheet_ids_arrears += ",";
                        sheet_ids_arrears += row.business_sheet_id;
                    }
                    if (checkSheetExistSQL != "") checkSheetExistSQL += " or "; 
                    checkSheetExistSQL += $"(business_sheet_type='{row.business_sheet_type}' and business_sheet_id={row.business_sheet_id})";

                    string billStatusSql = $"select last_revoke_time,out_company from arrears_bill where company_id = {companyID} and business_sheet_type = '{row.business_sheet_type}' and business_sheet_id={row.business_sheet_id}";
                    dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(billStatusSql, cmd);
                    if (ret != null && ((string)ret.last_revoke_time).IsInvalid() && ((string)ret.out_company).IsValid())
                    {
                        // 只有没有回收过的欠条以及状态不为"未给欠条"需要在交帐时把状态变为公司内部
                        arrearsBillUpdateSql += $"update arrears_bill set keeper_id = {operID},out_company = false,last_revoke_time = '{happen_time}' where company_id = {companyID} and business_sheet_type = '{row.business_sheet_type}' and business_sheet_id={row.business_sheet_id};";
                    }
                    
                    
                }
                checkSheetExistSQL = "(" + checkSheetExistSQL + ")";
                checkSheetExistSQL = $"select d.sheet_id from sheet_check_sheets_detail d left join sheet_check_sheets_main m on d.company_id=m.company_id and d.sheet_id=m.sheet_id where d.company_id={companyID} and m.red_flag is null and {checkSheetExistSQL};";

                var payway_dict = new Dictionary<string, ExpandoObject>(); // paywayid: name, amount
                string checkRedSQL = "";
           
                    SQLQueue QQ = new SQLQueue(cmd);
                    // UNION SELECT 会去重，但这里不需要。所以多SELECT一个sheet_id。
                    var pw_sql = "";
                if (sheet_ids_sale != "")
                    {
                        pw_sql += $@"
SELECT       sheet_id,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag FROM sheet_sale_main          WHERE company_id = {companyID} and approve_time is not null and sheet_id in({sheet_ids_sale}) and happen_time>='{start_time}'";
                        checkRedSQL+=$@"
SELECT       sheet_no FROM sheet_sale_main   WHERE company_id = {companyID} and red_flag='1' and sheet_id in ({sheet_ids_sale}) and happen_time>='{start_time}'";
                    sqlUpdateSheet += $"update sheet_sale_main set check_account_time='{happen_time}' where company_id={companyID} and sheet_id in ({sheet_ids_sale});";
                    }

				if (sheet_ids_borrow != "")
				{
					if (pw_sql != "") pw_sql += @" union ";
					pw_sql += $@"
SELECT       sheet_id,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag FROM borrow_item_main   WHERE company_id = {companyID} and approve_time is not null and sheet_id in({sheet_ids_borrow}) and happen_time>='{start_time}'";
					if (checkRedSQL != "") checkRedSQL += @" union ";
					checkRedSQL += $@"
SELECT       sheet_no FROM borrow_item_main   WHERE company_id = {companyID} and red_flag='1' and sheet_id in ({sheet_ids_borrow}) and happen_time>='{start_time}'";
					sqlUpdateSheet += $"update borrow_item_main set check_account_time='{happen_time}' where company_id={companyID} and sheet_id in ({sheet_ids_borrow});";
				}
				


				if (sheet_ids_prepay != "")
                    {
                        if (pw_sql != "") pw_sql += @" union ";
                        pw_sql += $@"
SELECT sheet_id,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag FROM sheet_prepay                   WHERE company_id = {companyID} and approve_time is not null and sheet_id in({sheet_ids_prepay}) and happen_time>='{start_time}'";
                        if (checkRedSQL != "") checkRedSQL += @" union ";
                        checkRedSQL += $@"
SELECT       sheet_no FROM sheet_prepay  WHERE company_id = {companyID} and red_flag='1' and sheet_id in ({sheet_ids_prepay}) and happen_time>='{start_time}'";
                    sqlUpdateSheet += $"update sheet_prepay set check_account_time='{happen_time}' where company_id={companyID} and sheet_id in ({sheet_ids_prepay});";

                }

                if (sheet_ids_fee != "")
                    {
                        if (pw_sql != "") pw_sql += @" union ";
                        pw_sql += $@"
SELECT sheet_id,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag FROM sheet_fee_out_main             WHERE company_id = {companyID} and approve_time is not null and sheet_id in({sheet_ids_fee}) and happen_time>='{start_time}'";

                        if (checkRedSQL != "") checkRedSQL += @" union ";
                        checkRedSQL += $@"
SELECT       sheet_no FROM sheet_fee_out_main  WHERE company_id = {companyID} and red_flag='1' and sheet_id in({sheet_ids_fee}) and happen_time>='{start_time}'";
                    sqlUpdateSheet += $"update sheet_fee_out_main set check_account_time='{happen_time}' where company_id={companyID} and sheet_id in ({sheet_ids_fee});";

                }

                if (sheet_ids_arrears != "")
                {
                        if (pw_sql != "") pw_sql += @" union ";
                        pw_sql += $@"
SELECT sheet_id,payway1_id,payway1_amount,payway2_id,payway2_amount,money_inout_flag FROM sheet_get_arrears_main         WHERE company_id = {companyID} and approve_time is not null and sheet_id in({sheet_ids_arrears}) and happen_time>='{start_time}'";

                        if (checkRedSQL != "") checkRedSQL += @" union ";
                        checkRedSQL += $@"
SELECT       sheet_no FROM sheet_get_arrears_main  WHERE company_id = {companyID} and red_flag='1' and sheet_id in({sheet_ids_arrears}) and happen_time>='{start_time}'";
                    sqlUpdateSheet += $"update sheet_get_arrears_main set check_account_time='{happen_time}' where company_id={companyID} and sheet_id in ({sheet_ids_arrears});";

                }

                if (pw_sql != "")
                {
                    QQ.Enqueue("pay_way", pw_sql);
                }
                    
                var pn_sql = $"SELECT sub_id, sub_name FROM cw_subject WHERE company_id = {companyID};";
                QQ.Enqueue("pay_name", pn_sql);

                    var dr = await QQ.ExecuteReaderAsync();
                    List<System.Dynamic.ExpandoObject> sheets = null;
                    List<System.Dynamic.ExpandoObject> payways = null;
                    while (QQ.Count > 0)
                    {
                        var sqlName = QQ.Dequeue();
                        if (sqlName == "pay_way")
                        {
                            sheets = CDbDealer.GetRecordsFromDr(dr, false);
                        }
                        else if (sqlName == "pay_name")
                        {
                            payways = CDbDealer.GetRecordsFromDr(dr, false);
                        }
                    }
                    QQ.Clear();

                    var payway_name_dict = new Dictionary<string, string>();
                    foreach (dynamic pw in payways)
                    {
                        var payway_id = pw.sub_id.ToString();
                        var payway_name = pw.sub_name.ToString();
                        if (payway_id != null && payway_name != null)
                        {
                            payway_name_dict.TryAdd(payway_id, payway_name);
                        }
                    }

                if (sheets is not null)
                {
                    foreach (dynamic sheet in sheets)
                    {
                        var money_inout_flag = (string)sheet.money_inout_flag;
                        var p1_id = (string)sheet.payway1_id;
                        var p2_id = (string)sheet.payway2_id;
                        var p1_amt = (string)sheet.payway1_amount;
                        var p2_amt = (string)sheet.payway2_amount;

                        if (money_inout_flag.IsInvalid()) { continue; } // to-do: default process?
                        int.TryParse(money_inout_flag, out int flag);

                        if (p1_id.IsValid() && double.TryParse(p1_amt, out double p1amt) && payway_name_dict.ContainsKey(p1_id))
                        {
                            string id = p1_id;
                            string name = payway_name_dict[id];
                            double amt = p1amt * flag;
                            if (payway_dict.ContainsKey(id))
                            {
                                dynamic p = payway_dict[id];
                                p.amt += amt;
                            }
                            else
                            {
                                dynamic value = new ExpandoObject();
                                value.name = name;
                                value.amt = amt;
                                payway_dict.Add(id, value);
                            }
                        }
                        if (p2_id.IsValid() && double.TryParse(p2_amt, out double p2amt) && payway_name_dict.ContainsKey(p2_id))
                        {
                            string id = p2_id;
                            string name = payway_name_dict[id];
                            double amt = p2amt * flag;
                            if (payway_dict.ContainsKey(id))
                            {
                                dynamic p = payway_dict[id];
                                p.amt += amt;
                            }
                            else
                            {
                                dynamic value = new ExpandoObject();
                                value.name = name;
                                value.amt = amt;
                                payway_dict.Add(id, value);
                            }
                        }
                    }
                }
                
                string payway = JsonConvert.SerializeObject(payway_dict);
                db.AddField("payway", payway);

                string sqlMaster = db.GetInsertSQL("sheet_check_sheets_main") + " returning sheet_id;";

                
               
                    /*
                    checkSql = $@"
                        SELECT sheet_no FROM sheet_sale_main WHERE company_id = {companyID} and red_flag = 1 and approve_time is not null and seller_id = {seller_id}  and sheet_id||sheet_type in({sheetsInfo})
                        UNION 
                        SELECT sheet_no FROM sheet_prepay WHERE company_id = {companyID} and red_flag = 1 and approve_time is not null and getter_id = {seller_id}   and  sheet_id||sheet_type in({sheetsInfo})
                        UNION
                        SELECT sheet_no FROM sheet_fee_out_main WHERE company_id = {companyID} and red_flag = 1 and approve_time is not null and getter_id = {seller_id}  and sheet_id||sheet_type in({sheetsInfo})
                        UNION
                        SELECT sheet_no FROM sheet_get_arrears_main WHERE company_id = {companyID} and red_flag = 1 and approve_time is not null and getter_id =  {seller_id} and sheet_id||sheet_type in({sheetsInfo})";
                    */
                    QQ = new SQLQueue(cmd);
                    if(checkRedSQL!="") QQ.Enqueue("redSheetNo", checkRedSQL);
                    QQ.Enqueue("checkSheetExist", checkSheetExistSQL);


                     dr = await QQ.ExecuteReaderAsync();
                    List<System.Dynamic.ExpandoObject> redSheetNo = null;
                    List<System.Dynamic.ExpandoObject> existSheets = null;
                    while (QQ.Count > 0)
                    {
                        var sqlName = QQ.Dequeue();
                        if (sqlName == "redSheetNo")
                        {
                            redSheetNo = CDbDealer.GetRecordsFromDr(dr, false);
                        }
                        else if (sqlName == "checkSheetExist")
                        {
                            existSheets = CDbDealer.GetRecordsFromDr(dr, false);
                        }
                    }
                    QQ.Clear();
                    if (redSheetNo?.Count > 0)
                    {
                        return Json(new { result = "hasRed", msg = "所选单据已经被红冲", redSheetNo });
                    }
                    if (existSheets?.Count > 0)
                    {
                        return Json(new { result = "sheetsExist", msg = "所选单据已经交账", existSheets });
                    }

               
                sql = $@"SELECT yj_exeSqlByInsertedRowID('{sqlMaster.Replace("'", "''")}','{sqlDetail.Replace("'", "''")}','@sheet_id')";
                var tran = cmd.Connection.BeginTransaction();
                cmd.CommandText = sql;

                object ov = await cmd.ExecuteScalarAsync();
                string sheet_id = "";
                if (ov != null && ov != DBNull.Value) sheet_id = ov.ToString();
                sheet_id = sheet_id.Split(",")[0];
                // 交账时变更arrears_bill
                cmd.CommandText = arrearsBillUpdateSql + sqlUpdateSheet;
                await cmd.ExecuteNonQueryAsync();

                tran.Commit();

                /*Dictionary<string, string> printTitle = new Dictionary<string, string>();
                sql = $"SELECT maker_id,start_time,end_time,oper_name FROM sheet_check_sheets_main scsm LEFT JOIN info_operator io ON scsm.maker_id = io.oper_id where scsm.company_id={companyID} and scsm.sheet_id = " + sheet_id;
                cmd.CommandText = sql;
                var reader = await cmd.ExecuteReaderAsync();
                while (reader.Read())
                {
                    printTitle.Add("maker_id", reader["maker_id"].ToString());
                    printTitle.Add("start_time", ((DateTime)reader["start_time"]).ToText());
                    printTitle.Add("end_time", ((DateTime)reader["end_time"]).ToText());
                    printTitle.Add("oper_name", reader["oper_name"].ToString());
                }
                reader.Close();*/

                string daySheetsID = "";
                if (!string.IsNullOrEmpty(happen_time))
                {
                    string[] arr = happen_time.Split(" ");
                    if (happen_time.Length > 0) happen_time = arr[0];

                    sql = $@"select string_agg(sheet_id::text,',') as sheets_id from sheet_check_sheets_main where happen_time>='{happen_time}' and happen_time<='{happen_time} 23:59:59' and maker_id={operID} and red_flag is null;";
                    dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    if (rec != null)
                    {
                        daySheetsID = rec.sheets_id;
                    }

                }
                await RedisHelper.DelAsync(redisKey);
                return Json(new { result = "OK", msg = "", sheet_id, happen_time, daySheetsID });

            }
            else return Json(new { result = "Error", msg = "请勿重复提交" });


        }

        string GetRedisLockKey(dynamic sheetRows)
        {
            string redisKey = "checkAccount";
            foreach (dynamic row in sheetRows)
            {
                redisKey += $"{row.business_sheet_type}{row.business_sheet_id}";
            }

            return redisKey;
        }

        [HttpPost]
        public async Task<JsonResult> RedCheckAccount([FromBody] dynamic data, bool bAutoCommit = true)
        {
            cmd.ActiveDatabase = "";
            CMySbTransaction tran = null;
            if (bAutoCommit)tran = await cmd.Connection.BeginTransactionAsync();
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string sheet_id = data.sheet_id;
            try
            {
                cmd.CommandText = $@"update sheet_check_sheets_main set red_flag = 1 where sheet_id ={sheet_id} and company_id = {companyID};";
                await cmd.ExecuteNonQueryAsync();
                
                // 交账单红冲后，把关联的欠条持有人回退为销售单业务员
                string businessSheetSql = @$"SELECT 
    scs.business_sheet_id,
    scs.business_sheet_type,
    sm.seller_id
FROM 
    sheet_check_sheets_detail scs
LEFT JOIN 
    (
        SELECT  sheet_id,sheet_type, seller_id FROM  sheet_sale_main  WHERE  company_id = {companyID}
        UNION
        SELECT sheet_id,sheet_type, getter_id AS seller_id FROM sheet_prepay WHERE  company_id = {companyID}
        UNION
        SELECT sheet_id,sheet_type, getter_id AS seller_id FROM sheet_fee_out_main WHERE company_id = {companyID}
    )  sm
    ON scs.business_sheet_id = sm.sheet_id 
WHERE 
    scs.sheet_id = {sheet_id} AND scs.business_sheet_type = sm.sheet_type AND scs.company_id = {companyID};
";

                string rollBackKeeperSql = "";

                dynamic bSheetList = await CDbDealer.GetRecordsFromSQLAsync(businessSheetSql, cmd);


                foreach (dynamic row in bSheetList)
                {
                    if (row.seller_id != "")
                    {
                        string billStatusSql = @$"select ab.bill_id,m.sheet_id,ab.out_company from arrears_bill ab
left join sheet_move_arrears_bill_detail d  on d.bill_id = ab.bill_id and d.company_id = ab.company_id
left join sheet_move_arrears_bill_main m
on d.sheet_id = m.sheet_id and d.company_id = m.company_id and m.red_flag is null and m.approve_time is not null 
where ab.company_id = {companyID} and ab.business_sheet_type = '{row.business_sheet_type}' and ab.business_sheet_id={row.business_sheet_id} 
order by m.sheet_id desc NULLS LAST";
                        dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(billStatusSql, cmd);
                        if (ret != null && ((string)ret.sheet_id).IsInvalid() && !((string)ret.out_company).IsInvalid())
                        {
                            // 只有没有发放/回收记录的欠条在交账单红冲时变更状态
                            rollBackKeeperSql += $"update arrears_bill set keeper_id = {row.seller_id},out_company = true,last_revoke_time = null where business_sheet_id = {row.business_sheet_id} and business_sheet_type = '{row.business_sheet_type}' and company_id = {companyID};";
                        }
                        
                    }
                   
                }
                string sheetSql = @$"select business_sheet_id,business_sheet_type, seller_id from sheet_check_sheets_detail  scs
                                left join (select seller_id,sheet_id from sheet_sale_main where company_id={companyID}  ) ssm on business_sheet_id = ssm.sheet_id  and scs.business_sheet_type in ('X','T')
                                where scs.sheet_id = {sheet_id} and scs.company_id = {companyID}";
                dynamic sheetList = await CDbDealer.GetRecordsFromSQLAsync(sheetSql, cmd);

                string sheet_ids_sale = "", sheet_ids_borrow = "", sheet_ids_prepay = "", sheet_ids_fee = "", sheet_ids_arrears = "";
                foreach (dynamic row in sheetList)
                {
                    
                    if (row.business_sheet_type == "X" || row.business_sheet_type == "T")
                    {
                        if (sheet_ids_sale != "") sheet_ids_sale += ",";
                        sheet_ids_sale += row.business_sheet_id; 
                    }
					else if (row.business_sheet_type == "JH" || row.business_sheet_type == "HH")
					{
						if (sheet_ids_borrow != "") sheet_ids_borrow += ",";
						sheet_ids_borrow += row.business_sheet_id; 
					}
					else if (row.business_sheet_type == "YS" || row.business_sheet_type == "YF" || row.business_sheet_type == "DH")
                    {
                        if (sheet_ids_prepay != "") sheet_ids_prepay += ",";
                        sheet_ids_prepay += row.business_sheet_id;
                    }
                    else if (row.business_sheet_type == "ZC" || row.business_sheet_type == "SR")
                    {
                        if (sheet_ids_fee != "") sheet_ids_fee += ",";
                        sheet_ids_fee += row.business_sheet_id;
                    }
                    else if (row.business_sheet_type == "SK" || row.business_sheet_type == "FK")
                    {
                        if (sheet_ids_arrears != "") sheet_ids_arrears += ",";
                        sheet_ids_arrears += row.business_sheet_id;
                    }
                }
                string sqlUpdateSheet = "";
                if (sheet_ids_sale != "")
                {
                    sqlUpdateSheet += $"update sheet_sale_main set check_account_time=null where company_id={companyID} and sheet_id in ({sheet_ids_sale});";
                }

				if (sheet_ids_borrow != "")
				{
					sqlUpdateSheet += $"update borrow_item_main set check_account_time=null where company_id={companyID} and sheet_id in ({sheet_ids_borrow});";
				}
				


				if (sheet_ids_prepay != "")
                {
                    sqlUpdateSheet += $"update sheet_prepay set check_account_time=null where company_id={companyID} and sheet_id in ({sheet_ids_prepay});";

                }

                if (sheet_ids_fee != "")
                {
                    sqlUpdateSheet += $"update sheet_fee_out_main set check_account_time=null where company_id={companyID} and sheet_id in ({sheet_ids_fee});";

                }

                if (sheet_ids_arrears != "")
                {
                    sqlUpdateSheet += $"update sheet_get_arrears_main set check_account_time=null where company_id={companyID} and sheet_id in ({sheet_ids_arrears});";

                }


                cmd.CommandText = rollBackKeeperSql + sqlUpdateSheet;
                await cmd.ExecuteNonQueryAsync();
                tran.Commit();
            }
            catch(Exception E)
            {   
             tran.Rollback();
            }
            return Json(new { result = "OK", msg = "", sheet_id });
        }

        [HttpGet]
        public async Task<JsonResult> GetSaleOrOrderSaleHistory(string operKey)
        {
            // select * from info_operator WHERE company_id = '51' and is_seller = 't' and status <> 0
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);
            string operSql = $@"select oper_id as id,oper_name as value,depart_path from info_operator WHERE company_id = {companyID} and status <> 0";
            QQ.Enqueue("oper", operSql);
            string departSql = $@"select depart_id as id,depart_name as value,mother_id as mId from info_department  WHERE company_id = {companyID} ";
            QQ.Enqueue("depart", departSql);
            List<ExpandoObject> opers = null;
            List<ExpandoObject> departs = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "oper")
                {
                    opers = CDbDealer.GetRecordsFromDr(dr, false);
                };
                if (sqlName == "depart")
                {
                    departs = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, opers, departs });
        }
        
        
        //获取筛选日期记忆
        [HttpGet]
        public async Task<JsonResult> GetRememberOption(string operKey) 
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string sql = $"SELECT options from options_remembered where company_id='{companyID}' and oper_id='{operID}'";
            dynamic options = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", data = options });
        }
        //更新筛选日期记忆
        [HttpPost]
        public async Task<JsonResult> RememberOption([FromBody] dynamic data)
        {
            try
            {
                Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);

                dynamic obj = new
                {
                    page_CheckSheetsSheet = new
                    {
                        dateSlot = data.diffDays
                    }
                };

                string CheckSheetsSheetJson = JsonConvert.SerializeObject(obj);
                string sql = @$"insert into options_remembered (company_id,oper_id,options) values ({companyID},{operID},'{CheckSheetsSheetJson}'::jsonb) on conflict(company_id,oper_id) do update set options=jsonb_merge(options_remembered.options,'{CheckSheetsSheetJson}'::jsonb);";

                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
                return Json(new { result = "OK" , day = data.diffDays });
            }
            catch (Exception E)
            {
                return Json(new { result = "Error", msg = E.Message });
            }
        }

    }
}
