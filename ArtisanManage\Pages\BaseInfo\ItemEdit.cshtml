@page
@model ArtisanManage.Pages.BaseInfo.ItemEditModel
@{
  Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
  <meta name="viewport" content="width=device-width"/>
  <title>ItemEdit</title>
  <partial name="_FormPageHead" model="Model.PartialViewModel"/>
  <link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js?v=@Html.Raw(Model.Version)"></script>
  <script src="~/js/Vue.js"></script>
    <script src="/tinymce_5.8.2/tinymce.min.js"></script>

    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
  <style>
        
        select {
            background: #FFF0F5;
            color: #000000;
            margin: 5px;
            margin-bottom:15px;
            width: 100px;
            height: 40px;
            padding: 5px;
            position: relative;
            border-radius: 8px;
            appearance: none;
            -moz-appearance: none;
            -webkit-appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20fill%3D%22%23ffffff%22%20d%3D%22M7%2010l5%205%205-5z%22%2F%3E%3C%2Fsvg%3E"), linear-gradient(#FFF0F5, #FFF0F5);
            background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20fill%3D%22%23ffffff%22%20d%3D%22M7%2010l5%205%205-5z%22%2F%3E%3C%2Fsvg%3E"), -webkit-linear-gradient(#FFF0F5, #FFF0F5);
            background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20fill%3D%22%23ffffff%22%20d%3D%22M7%2010l5%205%205-5z%22%2F%3E%3C%2Fsvg%3E"), -moz-linear-gradient(#FFF0F5, #FFF0F5);
            background-position: right 0.5em top 50%, 0 0;
            background-size: contain, cover;
            background-repeat: no-repeat, repeat;
        }

        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 90px 20px rgba(0, 0, 0, 0.2);
        }
        .video-wrapper {
            position: relative;
            margin-right: 20px;
            margin-bottom: 20px;
        }

        .video-wrapper video {
                width: 100px;
        }

        .video-wrapper .el-button {
                position: absolute;
                top: 0;
                right: 0;
        }
        #divAttributesTop{
            padding-top:10px;padding-left:20px;            
        }
        #divAttributesTop [type=checkbox] {
            width:20px;height:20px;
        }
        .el-input__inner{
            border:none;
             padding:0px ;
             width:270px;
             padding-left:10px
        }
        .el-table .cell {
            height:29px;
            padding:0px !important;
        }
        .el-table__row td {
            padding:0px
        }
   
        .el-select {
            width:100%;
            
        }
        .el-input {
            width:100%;
       
        }
        .el-select .el-input__suffix .el-icon-arrow-up {
            display: none;
        }
        .el-table  .discount {
            width:60px
        }
       .el-table  .discount .el-input__inner {
            width:50px !important;
        }
        .el-table thead {
                color:#000000;
                
        }
        .el-table thead.is-group th {
                background-color:rgb(245, 245, 245);
                text-align:center;
                font-weight:normal;
                padding:0px;
        }
    </style>
  <script type="text/javascript">
	window.ObjOperRights = @Html.Raw(Model.ObjOperRights);
    console.log('window.ObjOperRights:', window.ObjOperRights)
        // Global var for Item images.
        var othPictNum = 0; // Current Pict Num
        var othVideoMainNum = 0;
        var othVideoOtherNum = 0;
        var pictNumMaximum = 9;
        var pictNumOtherMaximum = 9;
        var descVideoNum = 0;
        var descImageNum = 0;
        var descVideoMaximum = 9;
        var descImageMaximum = 9;
        var openFlag = true
        var attributeVm
        window.unitPriceRelated=true
        if('@Html.Raw(Model.UnitPriceRelated)'=='false'){ 
            window.unitPriceRelated=false 
        }
        @Html.Raw(Model.m_saveCloseScript)
        window.ObjOperRights = @Html.Raw(Model.ObjOperRights);
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            @Html.Raw(Model.AvaliPricePlan);
      
            let pricePlanInfo = $('#price_plan_item').jqxInput('val');
            $('#divTab').jqxTabs({ width: getWidth('divTab'), height: getHeight('divTab'), position: 'top'});
            $('#divTab').css('visibility', 'visible')
           
            //包装一个商品信息对象，用来计算大小单位价格联动
            function getItemInfo(itemInfo) {
            
            for (let i = 0; i < 3; i++) { 
                let unit_no = $('#gridUnit').jqxGrid('getcelltext', i, "unit_no")
                let unit_factor = $('#gridUnit').jqxGrid('getcelltext', i, "unit_factor")
                let wholesale_price = $('#gridUnit').jqxGrid('getcelltext', i, "wholesale_price")
                if (i == 0) { 
                    itemInfo.s_unit_no = unit_no
                    itemInfo.s_unit_factor = unit_factor
                    itemInfo.s_wholesale_price = wholesale_price
                }
                if (i == 1) {
                    itemInfo.b_unit_no = unit_no
                    itemInfo.b_unit_factor = unit_factor
                    itemInfo.b_wholesale_price = wholesale_price
            
                }
                if (i == 2) { 
                     itemInfo.m_unit_no = unit_no
                    itemInfo.m_unit_factor = unit_factor
                    itemInfo.m_wholesale_price = wholesale_price
                }
            }
            
            }
            //价格方案vue模块
            var vm = new Vue(
                {
                    el: "#pricePlan",
                    data() {
                        return {
                            tableData: [],
                            pricePlanInfo: [],
                            pricePlanOptions: [],
                            itemInfo: {}


                        }
                    },
                    mounted() {
                        getItemInfo(this.itemInfo)
                        let a = this.itemInfo
                        if (pricePlanInfo) {
                            //获得该商品已添加的价格方案
                            this.pricePlanInfo = JSON.parse(pricePlanInfo)
                        }
                        if (availPricePlan) {
                            //获得该公司的价格方案列表
                            this.pricePlanOptions = availPricePlan
                        }

                        //将数据放入tableData渲染表格
                        this.pricePlanInfo.forEach(item => {
                            const tableDataItem = {}
                            tableDataItem.plan_id = item.price_plan_item.plan_id
                            tableDataItem.b_price = item.price_plan_item.b_price
                            tableDataItem.m_price = item.price_plan_item.m_price
                            tableDataItem.s_price = item.price_plan_item.s_price
                            tableDataItem.discount = item.price_plan_item.discount * 100 == 0 ? '' : item.price_plan_item.discount * 100

                            //因为老的价格方案那边没有联动，会出现有折扣没价格，或者有价格没折扣情况，这边做一个处理
                            //if ((tableDataItem.s_price == '' || tableDataItem.s_price == null) && (tableDataItem.discount != '' || tableDataItem.discount != null)) {
                            //    tableDataItem.s_price = toMoney(this.itemInfo.s_wholesale_price * (tableDataItem.discount / 100), 2)
                            //    if (this.itemInfo.m_unit_factor) tableDataItem.m_price = toMoney(tableDataItem.s_price * this.itemInfo.m_unit_factor, 2)
                            //    if (this.itemInfo.b_unit_factor) tableDataItem.b_price = toMoney(tableDataItem.s_price * this.itemInfo.b_unit_factor, 2)
                            //}
                            //if ((tableDataItem.discount == '' || tableDataItem.discount == null) && (tableDataItem.s_price != '' || tableDataItem.s_price != null)) {
                            //    tableDataItem.discount = toMoney(tableDataItem.s_price / this.itemInfo.s_wholesale_price * 100, 2)
                            //}



                            //tableDataItem.plan_name = item.plan_name

                            //selectPlanId是对plan_id的一个预处理，例如选中时判重，不重复再将其赋值给plan_id
                            tableDataItem.selectPlanId = tableDataItem.plan_id
                            this.tableData.push(tableDataItem)
                        })

                   this.addTableRow()

                    },
                    watch: {
                        //动态监视这个值的变化，更新window上最终提交的数据
                        tableData: {
                            immediate: true,
                            deep: true,
                            handler(newValue) {
                                let lastIndex = this.tableData.length
                                if(lastIndex>0){
                                               if (this.tableData[lastIndex-1].plan_id != "" && this.tableData[lastIndex-1].plan_id != null) { 
                                                this.addTableRow()
                                }
                                }
                         
                                window.submitPricePlan = newValue
                            }
                        }

                    },
                    computed: {
                        pricePlanRight: function () {
                            console.log('infoPrice:', window.ObjOperRights.info.infoPrice)
                            return window.ObjOperRights.info.infoPrice
                        }
                    },
                    methods: {
                        //折扣列变化时触发
                        onDiscountColumnChange(row) {
                           
                            //if (row.discount > 1) {
                            //    bw.toast("请输入正确的折扣小数")
                            //    row.discount = ''
                            //} else { 
                            //row.b_price = toMoney(this.itemInfo.b_wholesale_price * (row.discount / 100), 2) == 0 ? '' : toMoney(this.itemInfo.b_wholesale_price * (row.discount / 100), 2)
                            //row.s_price = toMoney(this.itemInfo.s_wholesale_price * (row.discount / 100), 2) == 0 ? '' : toMoney(this.itemInfo.s_wholesale_price * (row.discount / 100), 2)
                            //row.m_price = toMoney(this.itemInfo.m_wholesale_price * (row.discount / 100), 2) == 0 ? '' : toMoney(this.itemInfo.m_wholesale_price * (row.discount / 100), 2)
                            //}
                        },


                        //方案名称列发生变化
                        onPlanNameColumnChange(selectPlanId, row) {
                            let findResult = this.tableData.find(plan => plan.plan_id == selectPlanId)
                            if (findResult && selectPlanId != '') {
                                bw.toast("该价格方案已存在")
                                if (row.plan_id) {
                                    row.selectPlanId = row.plan_id
                                } else {
                                    row.selectPlanId = ''
                                }
                            } else {
                                row.plan_id = row.selectPlanId
                            }
                        },

                        //小单位列发生变化
                        onSPriceColumnChange(row) {
                           if(window.unitPriceRelated){
                                     row.b_price = toMoney(row.s_price * this.itemInfo.b_unit_factor, 2) == 0 ? '' : toMoney(row.s_price * this.itemInfo.b_unit_factor, 2)
                                    row.m_price = toMoney(row.s_price * this.itemInfo.m_unit_factor, 4) == 0 ? '' : toMoney(row.s_price * this.itemInfo.m_unit_factor, 4)
                           }
                           

                            //row.discount = toMoney(row.s_price / this.itemInfo.s_wholesale_price, 3) == 0 ? '' : toMoney(row.s_price / this.itemInfo.s_wholesale_price * 100, 2)

                        },

                        //中单位列发生变化
                        onMPriceColumnChange(row) {
                            if (window.unitPriceRelated) {
                                     row.s_price = toMoney(row.m_price / this.itemInfo.m_unit_factor, 8) == 0 ? '' : toMoney(row.m_price / this.itemInfo.m_unit_factor, 8)
                                     row.b_price = toMoney(row.s_price * this.itemInfo.b_unit_factor, 2) == 0 ? '' : toMoney(row.s_price * this.itemInfo.b_unit_factor, 2)
                            }
                           
                            //row.discount = toMoney(row.s_price / this.itemInfo.s_wholesale_price, 3) == 0 ? '' : toMoney(row.s_price / this.itemInfo.s_wholesale_price * 100, 2)
                        },

                        //大单位列发生变化
                        onBPriceColumnChange(row) {
                            if (window.unitPriceRelated) {
                                      row.s_price = toMoney(row.b_price / this.itemInfo.b_unit_factor, 8) == 0 ? '' : toMoney(row.b_price / this.itemInfo.b_unit_factor, 8)
                                      row.m_price = toMoney(row.s_price * this.itemInfo.m_unit_factor, 4) == 0 ? '' : toMoney(row.s_price * this.itemInfo.m_unit_factor, 4)
                            }
                          
                            //row.discount = toMoney(row.s_price / this.itemInfo.s_wholesale_price, 3) == 0 ? '' : toMoney(row.s_price / this.itemInfo.s_wholesale_price * 100, 2)

                        },

                        //添加空行
                        addTableRow() { 
                                    for (var i = 0; i < 10; i++) {
                                         const templateTableRow = {
                                             plan_id: '',
                                             b_price: '',
                                             m_price: '',
                                             s_price: '',
                                            discount: '',
                                    }
                            this.tableData.push(templateTableRow)
                                }
                        },

                  



                },

            }
            
        )

        $('#item_name input').on('input', function () {
            $('#py_str input').val(this.value.ToPinYinCode());
        })
        $('#valid_day_type input').on('change',function () {
            $('#approaching_day_type input').val(this.value)
            //$('#approaching_day_type').val($('#valid_day_type').val)
        });
        var item_id = $("#item_id").val()
        var son_mum_item = $("#son_mum_item").val()
        if (item_id) {

            if (ObjOperRights.info.infoItem && ObjOperRights.info.infoItem.edit == false) {
                $("#btnSave").attr("disabled", "true")
            }
        }else{
            if (ObjOperRights.info.infoItem && ObjOperRights.info.infoItem.create == false) {
                $("#btnSave").attr("disabled", "true")
            }
        }
      
       
            $("#upload-image").on('change',function (event) {
                    //var item_id = $('#item_id').val()
                    //console.log("id:"+item_id)
                    //if (item_id == '') {
                    //    bw.toast('只能给已有商品添加商品图片，请先保存', 3000);
                    //    return;
                    //}
                    if (pictNumMaximum > 29) {
                        bw.toast('删除次数超限，重新打开才能继续添加图片', 3000);
                        return;
                    }
                    if (othPictNum > pictNumMaximum){
                        bw.toast('最多上传10张图片', 3000);
                        return;
                    }
                var fileDom = document.getElementById('upload-image')
                var file = fileDom.files[0]
                if (!file.name.endsWith(".jpg") && !file.name.endsWith(".jpeg") && !file.name.endsWith(".png")) {
                    bw.toast("系统仅支持jpg,png,jpeg格式文件，请转换该图片")
                    return
                }
                var imgid = 'uploaded-image_'+othPictNum;
                    var spanid = 'itemimage_'+othPictNum;
                    var imghtml = `<div id="${spanid}" style="position:relative;cursor:pointer;height:100px" onmouseenter='onMouseEnterItemImage_Span("${spanid}", "${imgid}", event)' onmouseleave="onMouseLeaveItemImage(this.dataset.cbtnid, this.dataset.ebtnid, this.dataset.dbtnid)" data-cbtnid='' data-ebtnid='' data-dbtnid=''> <img id="${imgid}" class="imgclass" data-clicked='0' data-isnull='false' src="" style="width:100px; margin-left:20px; margin-top:10px;" /> </div>`
                $('#images_mixed').append(imghtml)
                var showDom = document.getElementById(imgid)
                previewImage(fileDom, showDom)
                othPictNum++;
                
                // Keep add button on the last position.
                document.getElementById('uploadImage').remove();
                var addhtml = `<div id="uploadImage" onclick="addImage()" title="添加商品图片 第一个图片将作为主图展示" style="margin-left:10px; width:100px; height:100px; text-align: center; cursor: pointer;"> <svg width="30px" height="30px" fill="#AAA" style="margin-top:30px;vertical-align: middle;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  /> </svg> </div>`
                $('#images_mixed').append(addhtml)
            });
            // EDIT.
            $("#edit-image").on('change', function(event) {
                var target = document.getElementById('edit-image').dataset.target;
                if (target == '') return;
                var fileDom = document.getElementById('edit-image')
                var showDom = document.getElementById(target)
                previewImage(fileDom, showDom)
            });
            // Load Images
            var item_images = $('#item_images').val()
            console.log("Load item image success. Val:" + item_images);
            if (item_images != null && item_images != "" && item_images != undefined) {
                var imgs = JSON.parse(item_images);

            let href="@Html.Raw(Model.ObsBucketLinkHref)/"
                var main = imgs.main == null ? '' : href + imgs.main;
                if (main != '') { 
                    var imageSrc = main;
            var imgid = 'uploaded-image_'+othPictNum;
            var spanid = 'itemimage_'+othPictNum;
            var imghtml = `<div id="${spanid}" style="position:relative;cursor:pointer;height:100px" onmouseenter='onMouseEnterItemImage_Span("${spanid}", "${imgid}", event)' onmouseleave="onMouseLeaveItemImage(this.dataset.cbtnid, this.dataset.ebtnid, this.dataset.dbtnid)" data-cbtnid='' data-ebtnid=''data-dbtnid=''> <img id="${imgid}" class="imgclass"   src="${imageSrc}" data-clicked='0' data-isnull='false' style="width:100px; margin-left:20px; margin-top:10px;" /> </div>`
                    $('#images_mixed').append(imghtml)
                    othPictNum++;
                }
                if (imgs.other) {
                             console.log("imgs.other:" + imgs.other + ", length: " + imgs.other.length);
                       for (var i = 0; i < imgs.other.length; i++) {
                    if (othPictNum > 9) { break; }
                    if (imgs.other[i] == null) { continue; }
                    var imageSrc = href + imgs.other[i];
                  var imgid = 'uploaded-image_'+othPictNum;
                 var spanid = 'itemimage_'+othPictNum;
                     var imghtml = `<div id="${spanid}" style="position:relative;cursor:pointer;height:100px" onmouseenter='onMouseEnterItemImage_Span("${spanid}", "${imgid}", event)' onmouseleave="onMouseLeaveItemImage(this.dataset.cbtnid, this.dataset.ebtnid, this.dataset.dbtnid)" data-cbtnid='' data-ebtnid='' data-dbtnid=''> <img id="${imgid}" class="imgclass"   src="${imageSrc}" data-clicked='0' data-isnull='false' style="width:100px; margin-left:20px; margin-top:10px;" /> </div>`
                    $('#images_mixed').append(imghtml)
                    othPictNum++;
                }
                }
   

            }

            var addhtml = `<div id="uploadImage" onclick="addImage()" title="添加商品图片 第一个图片将作为主图展示" style="margin-left:10px; width:100px; height:100px; text-align: center; cursor: pointer;"> <svg width="30px" height="30px" fill="#AAA" style="margin-top:30px;vertical-align: middle;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  /> </svg> </div>`
            $('#images_mixed').append(addhtml)

                  //点击图片发大
            $(".imgclass").on('click', function(e) {
            e.preventDefault()
                if (openFlag) {
                console.log(e.target.src)
                       let bigImg = `<div id='bigImg' style="position: absolute;height: 600px;width: 100%;top:-100px;background-color:RGBA(190 ,190 ,190,0.9);border-radius:30px;margin-top:70px"><img src="${e.target.src}" style="height:250px;width:250px;margin-left:300px;margin-top:150px"></div>`
                       $('#bigImageBox').append(bigImg)
                       openFlag = false
                }
        })

           $('#bigImageBox').on('click', function(e) {
            if (e.target.id == 'bigImg') { 
                        $('#bigImageBox').html('')
                        openFlag = true
                    }
        })

            @Html.Raw(Model.AttributeInitScript);
            var itemUsed = $('#itemUsed').val()
            // 老版
            // var newAttributes = [];
            // if (availAttributes.length) {
            //     availAttributes.forEach((aAttr) => {
            //         var metAttr=mumAttributes.find((mAttr) => {
            //             return aAttr.attr_id==mAttr.attrID
            //         })
            //         var newAttr=metAttr
            //         if (metAttr) {
            //             // newAttributes.push(metAttr)
            //             newAttr.enabled=true
            //         }
            //         else {
            //             newAttr={enabled:false,attrID:aAttr.attr_id,attrName:aAttr.attr_name,distinctStock:aAttr.distinct_stock,groupID:'', options:[],specOptInItem:aAttr.spec_opt_in_item}
            //         }
            //         newAttr.distinctStockEditable = aAttr.distinct_stock_editable
            //         newAttr.specOptInItem = aAttr.spec_opt_in_item
            //         newAttributes.push(newAttr)
            //     })
            // }
            // window.mumAttributes=newAttributes
            // window.onAttributeChecked = function (id,checked) {
            // var metAttr = mumAttributes.find((attr) => {
            //     return attr.attrID == id
            //     })
            //     metAttr.enabled=checked
            //     if(!checked) return
            //     if(window.gridAttrCreated) return
            //     //var attrID = $('#' + id).data('attr_id')
            //
            //
            // if (metAttr) {
            //     if (metAttr.specOptInItem) {
            //         var columns=[{
            //         text: '', sortable: false, filterable: false, editable: false, pinned: true,
            //                 groupable: false, draggable: false, resizable: false,
            //                 datafield: '', columntype: 'number', width: 50,
            //                 cellclassname: 'jqx-widget-header',
            //                 cellsrenderer: function(row, column, value) {
            //                 return '<div style="height:100%;display:flex;justify-content:center;align-items:center;">' + (value + 1) + '</div>';
            //             }
            //         },
            //                {
            //         text: '名称',columntype: 'template', displayfield: 'optName', datafield: 'optID', width: '70', sortable: false, align: 'center', align: 'center',
            //                     createeditor:  function (row, cellvalue, editor, cellText, width, height) {
            //                 var element = $('<div></div >');
            //                 editor.append(element);
            //                 var inputElement = editor.find('div')[0];
            //                 var datafields = new Array({ datafield: 'opt_name', text: '', width: 120 });
            //             var source = window.attrOptions[metAttr.attrID]
            //                         debugger
            //             if (!source) source = []
            //                        
            //             $(inputElement).jqxInput({
            //             height: height, width: width,
            //                 borderShape: 'none',
            //                 buttonUsage: 'list', 
            //                 dropDownHeight: 160,
            //                 displayMember: 'opt_name',
            //                 valueMember:  'opt_id',
            //                 datafields: datafields,
            //                 searchFields: ['attr_name'],
            //                 maxRecords: 9,
            //                 url:'',
            //                 source:source,
            //             });
            //         },initeditor: function(row, cellvalue, editor, celltext, pressedkey) {
            //             var inputField = editor.find('input');
            //             if (pressedkey) {
            //                 inputField.val(pressedkey);
            //                 inputField.jqxInput('selectLast');
            //             }
            //             else {
            //                 inputField.val({ value: cellvalue, label: celltext });
            //                 inputField[0].value = celltext||'';
            //                 inputField.jqxInput('selectAll');
            //             }
            //         },
            //                geteditorvalue: function (row, cellvalue, editor) {
            //             var $input=editor.find('input')
            //                 var v = $input.val();
            //             if (!v) {
            //                 v= { value: '', label: $input[0].value }
            //             }
            //             return v;
            //         }
            //
            //     },
            //                { text: '小', datafield: 'sBarcode', width: '130', sortable: false, align: 'center', align: 'center',columngroup:'barcode' },
            //                { text: '中', datafield: 'mBarcode', width: '130', sortable: false, align: 'center', align: 'center',columngroup:'barcode' },
            //                { text: '大', datafield: 'bBarcode', width: '130', sortable: false, align: 'center', align: 'center',columngroup:'barcode' },
            //
            //                {text: '小', datafield: 'sPrice', width: '60', sortable: false, align: 'center', align: 'center',columngroup:'price' },
            //                {text: '中', datafield: 'mPrice', width: '60', sortable: false, align: 'center', align: 'center',columngroup:'price' },
            //                {text: '大', datafield: 'bPrice', width: '60', sortable: false, align: 'center', align: 'center',columngroup:'price' },
            //                
            //             ]
            //             
            //             var theme = '';
            //     if (!metAttr.options) metAttr.options = []
            //
            // // 注释供新版使用，不需要进行这样方式添加
            // //             var opt = {optID:'',optName:'',sBarcode:'',mBarcode:'',bBarcode:'',sPrice:'',mPrice:'',bPrice:''}
            // // for (var i = 0; i < 5; i++) {
            // //     metAttr.options.push(JSON.parse(JSON.stringify(opt)))
            // //             }
            //
            //
            // //var datafields = new Array({ datafield: 'attr_name', text: 'abc', width: 120 }); 
            // var source =
            // {
            //                 localdata: metAttr.options,
            //                 unboundmode: true,
            //                 totalrecords: metAttr.options.length,
            //                 datafields: []
            //             }
            // var dataAdapter = new $.jqx.dataAdapter(source);
            //  window.gridAttrCreated=true
            //             $('#gridAttr').jqxGrid(
            //             {
            //                 width: window.innerWidth - 40,//  getWidth('gridAttr'),
            //                 height:300,                      
            //                 source:dataAdapter,
            //                 pageable:false, 
            //                 sortable:false,
            //                 editable:true,
            //                 columnsresize:true,
            //                 editmode:'selectedcell',
            //                 selectionmode:'singlerow',
            //                 theme: theme,
            //                 cellhover:cellhover,
            //                 columns: columns,  
            //                 columngroups:[{ text: '条码', align: 'center', name: 'barcode'},{ text: '批发价', align: 'center', name: 'price'}]
            //             });
            //         }
            //     }
            //
            // }
            // window.ckAttribute_click = function (event) {
            // var id = event.currentTarget.id
            // var checked = $('#' + id).prop('checked')
            //     id=id.replace('attr_','')
            //     onAttributeChecked(id,checked)
            // }
            // window.ckAttrDistinctStock_click = function (event) {
            //     var id = event.currentTarget.id
            //     var checked = $('#' + id).prop('checked')
            //     debugger
            //     var attr_id = $(event.currentTarget).data('attr_id')
            //     mumAttributes.forEach(attr => {
            //         if (attr.attrID == attr_id) {
            //             attr.distinctStock=checked
            //         }
            //     })
            // }
            //
            // newAttributes.forEach((attr) => {   
            //     var metAttr = mumAttributes.find(mAttr => attr.attrID == mAttr.attrID)
            //     if (itemUsed) {
            //
            // }
            //     $('#divAttributesTop').append(`<div style="margin-left:20px;"><input type="checkbox" class="magic-checkbox" style="margin-left:30px;" onclick="ckAttribute_click(event)" data-attr_id="${attr.attrID}"  ${metAttr.enabled?'checked':''} id="attr_${attr.attrID}" ${attr.distinctStock && itemUsed ? 'disabled':''}/><label for="attr_${attr.attrID}">${attr.attrName}</label></div>`)
            //     $('#divAttributesTop').append(`<div style="margin-left:20px;${!attr.distinctStockEditable?'display:none;':''}"><input type="checkbox"  class="magic-checkbox" style="margin-left:60px;" onclick="ckAttrDistinctStock_click(event)" ${attr.distinctStock?'checked':''} data-attr_id="${attr.attrID}" id="attr_distinctStock_${attr.attrID}" ${itemUsed?'disabled':''}/><label for="attr_distinctStock_${attr.attrID}" >${attr.attrName}区分库存</label></div>`)
            //     //onAttributeChecked('attr_'+attr.attrID)
            //     onAttributeChecked(attr.attrID,attr.enabled)
            // })


            //视频-主视频版
            $("#upload-video-main").on('change', function (event) {
                //var item_id = $('#item_id').val()
                //console.log("id:"+item_id)
                //if (item_id == '') {
                //    bw.toast('只能给已有商品添加商品图片，请先保存', 3000);
                //    return;
                //}
                if (pictNumMaximum > 29) {
                    bw.toast('删除次数超限，重新打开才能继续添加视频', 3000);
                    return;
                }
                if (othVideoMainNum >= 1) {
                    bw.toast('只能上传一个视频作为主视频', 3000);
                    return;
                }
                var fileDom = document.getElementById('upload-video-main')

                var file = fileDom.files[0]; // 获取选中的第一个文件
                // 获取文件大小（单位：字节）
                var fileSize = file.size;

                // 文件大小限制 (0MB 到 10MB)
                var minSize = 0 * 1024 * 1024; // 5MB
                var maxSize = 10 * 1024 * 1024; // 10MB
                // 检查文件大小
                if (fileSize < minSize || fileSize > maxSize) {
                    bw.toast('文件大小必须小于10MB', 3000);
                    return;
                }

                let item_videos = $('#item_videos').val()
                if (item_videos != null && item_videos != "" && item_videos != undefined) {
                    let videos = JSON.parse(item_videos);
                    if (videos.main) {var VideoMainTitle = videos.main.title;
                    var VideoMainShowcard = videos.main.showcard;}
                }
                var videoid = 'uploaded-video-main_' + othVideoMainNum;
                var spanid = 'itemvideomain_' + othVideoMainNum;
                var titleid = 'title_' + othVideoMainNum;
                var showcardid = 'show-card_' + othVideoMainNum;
                var reader = new FileReader();
                reader.onload = function (e) {
                    var uploadVideosrc = e.target.result;
                    uploadVideo(0, uploadVideosrc, null, videoid);
                };
                reader.readAsDataURL(file);
                //var videohtml = `<div id="${spanid}" style="position:relative;cursor:pointer;height:100px" onmouseenter='onMouseEnterItemVideoMain_Span("${spanid}", "${videoid}", event)' onmouseleave="onMouseLeaveItemImage(this.dataset.cbtnid, this.dataset.ebtnid, this.dataset.dbtnid)" data-cbtnid='' data-ebtnid='' data-dbtnid=''> <img id="${videoid}" class="imgclass" data-clicked='0' data-isnull='false' src="" style="width:100px; margin-left:20px; margin-top:10px;" /> </div>`
                var videohtml = `<div id="${spanid}" style="position:relative;cursor:pointer;height:100px" onmouseenter='onMouseEnterItemVideoMain_Span("${spanid}", "${videoid}", event)' onmouseleave="onMouseLeaveItemImage(this.dataset.cbtnid, this.dataset.ebtnid, this.dataset.dbtnid)" data-cbtnid='' data-ebtnid='' data-dbtnid=''><video id="${videoid}" class="video-class" width="100" height="100" controls style="width:100px; height:100px; margin-top:10px;margin-right:10px;"></video> </div>`;
                $('#video_main').append(videohtml)
                // var showDom = document.getElementById(videoid)
                 // previewVideo(fileDom, showDom)
                
                othVideoMainNum++;

                // Keep add button on the last position.
                document.getElementById('uploadVideoMain').remove();
                document.getElementById('selectTitle').remove();
                document.getElementById('selectShowCard').remove();
                //var addhtml = `<div id="uploadVideoMain" onclick="addVideo_main()" title="添加商品主视频" style="margin-left:10px; width:100px; height:100px; text-align: center; cursor: pointer;"> <svg width="30px" height="30px" fill="#AAA" style="margin-top:30px;vertical-align: middle;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  /> </svg> </div>`
                // var addhtml = `<div id="uploadVideoMain" onclick="addVideo_main()" title="添加商品主视频" style="margin-left:10px; width:100px; height:100px; text-align: center; cursor: pointer;"><svg width="30px" height="30px" fill="#AAA" style="margin-top:30px;vertical-align: middle;"><use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  /></svg></div>`;
                var addhtml1 = `<div style="margin-top:5px;"><div class="news-filter"  id="selectTitle">
                                        <select name="name" id="${titleid}">
                                        <option value="0">效果</option>
                                        <option value="1">讲解</option>
                                        <option value="2">宝贝介绍</option>
                                        <option value="3">推荐</option>
                                        <option value="4">教程使用</option>
                                        <option value="5">用途</option>
                                        <option value="6">宝贝讲解</option>
                                    </select>
                                </div>
                                <div class="show-card" display:block; id="selectShowCard">
                                    <span style="color:#C0C0C0;font-size:12px;margin-left:5px;margin-top:10px;"> 更多应用场景:</span>
                                                <div class="checkbox-container" style="margin-top:10px;font-size:15px">
                                                      <input type="checkbox" id="${showcardid}" >
                                            <label for="showcard">推荐卡片展示</label>
                                    </div>
                                </div></div>`;
                // $('#video_main').append(addhtml)
                 $('#video_main').append(addhtml1)
                // 设置下拉框的值
                if (VideoMainTitle) $('#' + titleid).val(VideoMainTitle);
                // 设置勾选框的选中状态
                if (VideoMainShowcard) $('#' + showcardid).prop('checked', VideoMainShowcard);
                
            });
            // EDIT.
            $("#edit-video-main").on('change', function (event) {
                var target = document.getElementById('edit-video-main').dataset.target;
                if (target == '') return;
                var fileDom = document.getElementById('edit-video-main')
                // var showDom = document.getElementById(target)
                // previewVideo(fileDom, showDom)
                reader.onload = function (e) {
                    var uploadVideosrc = e.target.result;
                };
                reader.readAsDataURL(target);
                uploadVideo(0, uploadVideosrc, null, videoid);
                uploadVideo(0, uploadVideosrc, null, videoid);
            });
            // Load Video-main
            let item_videos = $('#item_videos').val()
            console.log($('#item_videos'))
            console.log("Load item video main success. Val:" + item_videos);
            if (item_videos != null && item_videos != "" && item_videos != undefined) {
                let videos = JSON.parse(item_videos);

                let href = "@Html.Raw(Model.ObsBucketLinkHref)/"
                var main = videos.main == null ? '' : href + videos.main.path;
                // var main = videos.main == null ? '' :  videos.main.path;
                if (main != '') {
                    var VideoMainSrc = main;
                    var VideoMainTitle = videos.main.title;
                    var VideoMainShowcard = videos.main.showcard;
                    var videoid = 'uploaded-video-main_' + othVideoMainNum;
                    var spanid = 'itemvideomian_' + othVideoMainNum;
                    var titleid = 'title_' + othVideoMainNum;
                    var showcardid = 'show-card_' + othVideoMainNum;
                    //var videohtml = `<div id="${spanid}" style="position:relative;cursor:pointer;height:100px" onmouseenter='onMouseEnterItemVideoMain_Span("${spanid}", "${videoid}", event)' onmouseleave="onMouseLeaveItemImage(this.dataset.cbtnid, this.dataset.ebtnid, this.dataset.dbtnid)" data-cbtnid='' data-ebtnid=''data-dbtnid=''> <video id="${videoid}"  class="video-class"  src="${VideoMainSrc}" data-clicked='0' data-isnull='false' style="width:100px; margin-left:20px; margin-top:10px;" /> </div>`
                    var videohtml = `<div id="${spanid}" style="position:relative;cursor:pointer;height:100px" onmouseenter='onMouseEnterItemVideoMain_Span("${spanid}", "${videoid}", event)' onmouseleave="onMouseLeaveItemImage(this.dataset.cbtnid, this.dataset.ebtnid, this.dataset.dbtnid)" data-cbtnid='' data-ebtnid='' data-dbtnid=''> <video id="${videoid}"   class="video-class"  src="${VideoMainSrc}" data-clicked='0' data-isnull='false' style="width:100px; height:100px; margin-top:10px;margin-right:10px;" controls preload="auto"> </video> </div>`;
                    var addhtml1 = `<div style="margin-top:5px;"><div class="news-filter"  id="selectTitle">
                                                        <select name="name" id="${titleid}">
                                                        <option value="0">效果</option>
                                                        <option value="1">讲解</option>
                                                        <option value="2">宝贝介绍</option>
                                                        <option value="3">推荐</option>
                                                        <option value="4">教程使用</option>
                                                        <option value="5">用途</option>
                                                        <option value="6">宝贝讲解</option>
                                                    </select>
                                                </div>
                                                <div class="show-card" display:block; id="selectShowCard">
                                                    <span style="color:#C0C0C0;font-size:12px;margin-left:5px;margin-top:10px;"> 更多应用场景:</span>
                                                                <div class="checkbox-container" style="margin-top:10px;font-size:15px">
                                                                    <input type="checkbox" id="${showcardid}">
                                                            <label for="showcard">推荐卡片展示</label>
                                                    </div>
                                                </div></div>`;
                   
                    $('#video_main').append(videohtml)
                    $('#video_main').append(addhtml1)
                    // 设置下拉框的值
                    $('#' + titleid).val(VideoMainTitle);
                    // 设置勾选框的选中状态
                    $('#' + showcardid).prop('checked', VideoMainShowcard);
                    othVideoMainNum++;
                }
            }

            var addhtml = `<div id="uploadVideoMain" onclick="addVideo_main()" title="添加主商品视频" style="margin-right:20px; width:100px; height:100px; text-align: center; cursor: pointer;"> <svg width="30px" height="30px" fill="#AAA" style="margin-top:30px;vertical-align: middle;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  /> </svg> </div>`
            var addhtml1 = `<div style="margin-top:5px;"><div class="news-filter"  id="selectTitle">
                                                <select name="name" id="${titleid}">
                                                <option value="0">效果</option>
                                                <option value="1">讲解</option>
                                                <option value="2">宝贝介绍</option>
                                                <option value="3">推荐</option>
                                                <option value="4">教程使用</option>
                                                <option value="5">用途</option>
                                                <option value="6">宝贝讲解</option>
                                            </select>
                                        </div>
                                        <div class="show-card" display:block; id="selectShowCard">
                                            <span style="color:#C0C0C0;font-size:12px;margin-left:5px;margin-top:10px;"> 更多应用场景:</span>
                                                        <div class="checkbox-container" style="margin-top:10px;font-size:15px">
                                                            <input type="checkbox" id="${showcardid}">
                                                    <label for="showcard">推荐卡片展示</label>
                                            </div>
                                        </div></div>`;
            if (othVideoMainNum < 1) $('#video_main').append(addhtml);
            if (othVideoMainNum < 1) $('#video_main').append(addhtml1)

            //点击图片发大
            $(".imgclass").on('click', function (e) {
                e.preventDefault()
                if (openFlag) {
                    console.log(e.target.src)
                    let bigImg = `<div id='bigImg' style="position: absolute;height: 600px;width: 100%;top:-100px;background-color:RGBA(190 ,190 ,190,0.9);border-radius:30px;margin-top:70px"><img src="${e.target.src}" style="height:250px;width:250px;margin-left:300px;margin-top:150px"></div>`
                    $('#bigImageBox').append(bigImg)
                    openFlag = false
                }
            })

            $('#bigImageBox').on('click', function (e) {
                if (e.target.id == 'bigImg') {
                    $('#bigImageBox').html('')
                    openFlag = true
                }
            })

        @Html.Raw(Model.AttributeInitScript);
            var itemUsed = $('#itemUsed').val()
            //这个上面都是主视频的

            //视频-其他版
            $("#upload-video-other").on('change', function (event) {
                if (pictNumOtherMaximum > 29) {
                    bw.toast('删除次数超限，重新打开才能继续添加视频', 3000);
                    return;
                }
                if (othVideoOtherNum >= pictNumOtherMaximum) {
                    bw.toast('只能上传9个视频作为其他视频', 3000);
                    return;
                }

                var fileDom = document.getElementById('upload-video-other')

                var file = fileDom.files[0]; // 获取选中的第一个文件
                // 获取文件大小（单位：字节）
                var fileSize = file.size;

                // 文件大小限制 (0MB 到 10MB)
                var minSize = 0 * 1024 * 1024; // 5MB
                var maxSize = 10 * 1024 * 1024; // 10MB
                // 检查文件大小
                if (fileSize < minSize || fileSize > maxSize) {
                    bw.toast('文件大小必须小于10MB', 3000);
                    return;
                }

                var videoid = 'uploaded-video-other_' + othVideoOtherNum;
                var spanid = 'itemvideoother_' + othVideoOtherNum;
                var title_otherid = 'titleother_' + othVideoOtherNum;
                var videohtml = `<div id="${spanid}" style="position:relative;cursor:pointer;height:100px;margin-bottom:60px; margin-right:10px;margin-top:10px;" onmouseenter='onMouseEnterItemVideoOther_Span("${spanid}", "${videoid}", event)' onmouseleave="onMouseLeaveItemImage(this.dataset.cbtnid, this.dataset.ebtnid, this.dataset.dbtnid)" data-cbtnid='' data-ebtnid='' data-dbtnid=''><video id="${videoid}" class="video-class" width="100" height="100" controls style="width:100px; margin-left:5px;margin-top:10px;"></video>
                        <div class="news-filter"  id="selectOtherTitle">
                                                        <select name="name" id="${title_otherid}">
                                                        <option value="0">效果</option>
                                                        <option value="1">讲解</option>
                                                        <option value="2">宝贝介绍</option>
                                                        <option value="3">推荐</option>
                                                        <option value="4">教程使用</option>
                                                        <option value="5">用途</option>
                                                        <option value="6">宝贝讲解</option>
                                                    </select>
                                                </div></div>`;
                $('#video_other').append(videohtml)
                // var showDom = document.getElementById(videoid)
                // previewVideo(fileDom, showDom)
                var reader = new FileReader();
                reader.onload = function (e) {
                    var uploadVideosrc = e.target.result;
                    uploadVideo(1, uploadVideosrc, othVideoOtherNum, videoid);
                };
                reader.readAsDataURL(file);               
                

                // Keep add button on the last position.
                document.getElementById('uploadVideoOther').remove();
                var addhtml = `<div id="uploadVideoOther" onclick="addVideo_other()" title="添加商品其他视频" style="margin-left:5px; margin-top:20px;margin-bottom:60px; margin-right:10px; width:100px; height:100px; text-align: center; cursor: pointer;"><svg width="30px" height="30px" fill="#AAA" style="margin-top:30px;vertical-align: middle;"><use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  /></svg></div>`;
                $('#video_other').append(addhtml)
            });
            // EDIT.
            $("#edit-video-other").on('change', function (event) {
                var target = document.getElementById('edit-video-other').dataset.target;
                if (target == '') return;
                var fileDom = document.getElementById('edit-video-other')
                var showDom = document.getElementById(target)
                previewVideo(fileDom, showDom)
            });
            //Load Video-other
            //var item_videos = $('#item_videos').val()
            console.log("Load item video main success. Val:" + item_videos);
            if (item_videos != null && item_videos != "" && item_videos != undefined) {
                var videos = JSON.parse(item_videos);

                let href = "@Html.Raw(Model.ObsBucketLinkHref)/"
                    console.log("videos.other:" + videos.other + ", length: " + videos.other.length);
                    for (var i = 0; i < videos.other.length; i++) {
                        if (othVideoOtherNum > 9) { break; }
                        if (videos.other[i] == null) { continue; }
                        // var VideoOtherSrc = href + videos.other[i].path;
                        var VideoOtherSrc = href + videos.other[i].path;
                        var VideoOtherTitle = videos.other[i].title;
                        var videoid = 'uploaded-video-other_' + othVideoOtherNum;
                        var spanid = 'itemvideoother_' + othVideoOtherNum;
                        var titleid = 'title_' + othVideoMainNum;
                        var title_otherid = 'titleother_' + othVideoOtherNum;
                    //var videohtml = `<div id="${spanid}" style="position:relative;cursor:pointer;height:100px" onmouseenter='onMouseEnterItemVideoOther_Span("${spanid}", "${videoid}", event)' onmouseleave="onMouseLeaveItemImage(this.dataset.cbtnid, this.dataset.ebtnid, this.dataset.dbtnid)" data-cbtnid='' data-ebtnid='' data-dbtnid=''> <video id="${videoid}"   class="video-class"  src="${VideoOtherSrc}" data-clicked='0' data-isnull='false' style="width:100px; margin-left:20px; margin-top:10px;" /> </div>`
                    var videohtml = `<div id="${spanid}" style="position:relative;cursor:pointer;height:100px;margin-bottom:60px; margin-right:10px;margin-top:10px;" onmouseenter='onMouseEnterItemVideoOther_Span("${spanid}", "${videoid}", event)' onmouseleave="onMouseLeaveItemImage(this.dataset.cbtnid, this.dataset.ebtnid, this.dataset.dbtnid)" data-cbtnid='' data-ebtnid='' data-dbtnid=''> <video id="${videoid}"   class="video-class"  src="${VideoOtherSrc}" data-clicked='0' data-isnull='false' style="width:100px;height:100px; margin-left:5px;margin-top:10px;" controls preload="auto"> </video>
                                                        <div class="news-filter"  id="selectOtherTitle">
                                                        <select name="name" id="${title_otherid}" >
                                                        <option value="0">效果</option>
                                                        <option value="1">讲解</option>
                                                        <option value="2">宝贝介绍</option>
                                                        <option value="3">推荐</option>
                                                        <option value="4">教程使用</option>
                                                        <option value="5">用途</option>
                                                        <option value="6">宝贝讲解</option>
                                                    </select>
                                                </div></div>`;
                    $('#video_other').append(videohtml)
                    // 设置下拉框的值
                    $('#' + title_otherid).val(VideoOtherTitle);
                        othVideoOtherNum++;
                    }
            }

            var addhtml = `<div id="uploadVideoOther" onclick="addVideo_other()" title="添加其他视频" style="margin-left:5px; margin-top:20px;margin-bottom:60px; margin-right:10px; width:100px; height:100px; text-align: center; cursor: pointer;"> <svg width="30px" height="30px" fill="#AAA" style="margin-top:30px;vertical-align: middle;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  /> </svg> </div>`
            $('#video_other').append(addhtml)

            //点击图片发大
            $(".imgclass").on('click', function (e) {
                e.preventDefault()
                if (openFlag) {
                    console.log(e.target.src)
                    let bigImg = `<div id='bigImg' style="position: absolute;height: 600px;width: 100%;top:-100px;background-color:RGBA(190 ,190 ,190,0.9);border-radius:30px;margin-top:70px"><img src="${e.target.src}" style="height:250px;width:250px;margin-left:300px;margin-top:150px"></div>`
                    $('#bigImageBox').append(bigImg)
                    openFlag = false
                }
            })

            $('#bigImageBox').on('click', function (e) {
                if (e.target.id == 'bigImg') {
                    $('#bigImageBox').html('')
                    openFlag = true
                }
            })

        @Html.Raw(Model.AttributeInitScript);
            var itemUsed = $('#itemUsed').val()
            //这个上面都是其他视频的



            window.onresize();
            $('#gridUnit').jqxGrid('setcellvalue', 0, "unit_factor","1");
            $("#gridUnit").on('cellbeginedit', function (event)
            {
            // event arguments.
            var args = event.args;
            // column data field.
            var datafield = event.args.datafield;
            // row's bound index.
            var rowBoundIndex = event.args.rowindex;
            // cell value
            if (rowBoundIndex == 0 && datafield == "unit_factor") {
                setTimeout(function () {
                         $("#gridUnit").jqxGrid('endcelledit', 0, "unit_factor", false);
                }, 1);
            }
            var value = args.value;
            // row's data.
            var rowData = args.row;

        });
            $('#gridUnit').off('rowclick');
            $('#gridUnit').on('rowclick', function (event) {
            if (event.args.rightclick) {
                if (event.args.rowindex == 0) return;
                         $('#gridUnit').jqxGrid('selectrow', event.args.rowindex);
                var scrollTop = $(window).scrollTop();
                var scrollLeft = $(window).scrollLeft();
                contextMenu.jqxMenu('open', parseInt(event.args.originalEvent.clientX) + 5 + scrollLeft, parseInt(event.args.originalEvent.clientY) + 5 + scrollTop);
                return false;
            }
        })

        var lastUnitClickTime=new Date()

            $("#gridUnit").on("cellclick", function (event) {
            // event arguments.
            var args = event.args;
                if (args.datafield == "unit_factor" || args.datafield == "unit_no") {
                    if (args.originalEvent.button == 2) return;

                    var itemUsed = $('#itemUsed').val()
                    var unitRows = $('#unitRows').val()
                    if (itemUsed == 'true' && args.rowindex < unitRows) 
                    {
                        var allowChangeUnitFactor = window.ObjOperRights.delicacy.changeUnitfactorAfterNewSheet.value
                        if (itemUsed == 'true' && allowChangeUnitFactor != 'allow') {
                            var now = new Date()
                            if (now.getTime() - lastUnitClickTime.getTime() < 500) {
                                debugger
                                bw.toast(`已开单商品无法修改包装率或者单位`, 3000);
                            }
                            lastUnitClickTime = now
                        }
                    }
                } 
        })
  
        function formatPrice(m,n){
            if (isNaN(m)) t = '';
            else {
                m=parseFloat(parseFloat(m).toFixed(n));
                for(var i=0;i<=n;i++){
                    var t = parseFloat(parseFloat(m).toFixed(i));
                    if(m==t)  break;
                }
            }
            return t;
        }
            $("#gridUnit").on('cellendedit', function (event)
            {
             //debugger
            // event arguments.
            var args = event.args;
            // column data field.
            var datafield = event.args.datafield;
            // row's bound index.
            var row = event.args.rowindex;
            // cell value
            var value = args.value;
            // cell old value.
            var oldvalue = args.oldvalue;

            // Calculate profit_rate.
            function updateProfitRate() {
                for (var i = 0; i < 3; i++) {
                    var wholesale_price = $('#gridUnit').jqxGrid('getcellvalue', i, "wholesale_price");
                    var buy_price = $('#gridUnit').jqxGrid('getcellvalue', i, "buy_price");
                    if (wholesale_price && buy_price) {
                        var profit_rate = (Math.round((wholesale_price - buy_price) / wholesale_price * 10000) / 100.00) + '%';
                            $('#gridUnit').jqxGrid('setcellvalue', i, "profit_rate", profit_rate);
                    }
                }
            }

            
           
            function updatePriceByUnitPrice(priceCol) {
                //var priceValue = $('#gridUnit').jqxGrid('getcellvalue', row, price);
                var priceValue = value;
            
                var unit_factor_value = parseFloat($('#gridUnit').jqxGrid('getcellvalue', row, "unit_factor"));
                unit_factor_value=formatPrice(unit_factor_value,4)
                    var price_unit = (priceValue / unit_factor_value);
          
                if (price_unit) {
                    for (var i = 0; i < 3; i++) {
                        var unit_no = $('#gridUnit').jqxGrid('getcellvalue', i, "unit_no").trim();
                        var unit_factor_value = parseFloat($('#gridUnit').jqxGrid('getcellvalue', i, "unit_factor"));
                        var priceCur = $('#gridUnit').jqxGrid('getcellvalue', i, priceCol)
                        var priceNew = priceCur
                        // formatPrice(priceValue1,4)
                        if (window.unitPriceRelated || priceCur == '' || priceCol == "cost_price_avg") {
                             
                             if (unit_no && unit_factor_value) {
                                priceNew = (price_unit * unit_factor_value);
                                var n = 2
                                if (i == 0) n = 4
                                if (priceCol == 'weight' || priceCol == 'volume') n = 3
                                priceNew=formatPrice(priceNew,n)
                                $('#gridUnit').jqxGrid('setcellvalue', i, priceCol, priceNew);
                            }
                        }                      

                    }
                }
            }
       
            function updatePriceByUnitFactor(row) {
                var wholesale_price = $('#gridUnit').jqxGrid('getcellvalue', 0, "wholesale_price");
                var retail_price = $('#gridUnit').jqxGrid('getcellvalue', 0, "retail_price");
                var buy_price = $('#gridUnit').jqxGrid('getcellvalue', 0, "buy_price");
                var cost_price_spec = $('#gridUnit').jqxGrid('getcellvalue', 0, "cost_price_spec");
                var lowest_price = $('#gridUnit').jqxGrid('getcellvalue', 0, "lowest_price");
                var contract_price = $('#gridUnit').jqxGrid('getcellvalue', 0, "contract_price");
                var cost_price_avg = $('#gridUnit').jqxGrid('getcellvalue', 0, "cost_price_avg");
                 
                var weight = $('#gridUnit').jqxGrid('getcellvalue', 0, "weight");
                var volume = $('#gridUnit').jqxGrid('getcellvalue', 0, "volume");


                var unit_factor_value = $('#gridUnit').jqxGrid('getcellvalue', 0, "unit_factor");
                var wholesale_price_unit, retail_price_unit, buy_price_unit, cost_price_spec_unit, lowest_price_unit,contract_price_unit,cost_price_avg_unit,weight_unit,volume_unit
                    if (wholesale_price) wholesale_price_unit = (wholesale_price / unit_factor_value)
                    if (retail_price) retail_price_unit = (retail_price / unit_factor_value)
                    if (buy_price) buy_price_unit = (buy_price / unit_factor_value)
                    if (cost_price_spec) cost_price_spec_unit = (cost_price_spec / unit_factor_value)
                    if (lowest_price) lowest_price_unit = (lowest_price / unit_factor_value)
                if (contract_price) contract_price_unit = (contract_price / unit_factor_value)
                 if (cost_price_avg) cost_price_avg_unit = (cost_price_avg / unit_factor_value)

                


                    if (weight) weight_unit = (weight / unit_factor_value)
                    if (volume) volume_unit = (volume / unit_factor_value)


                    var rowindex = row;
                var unit_no = $('#gridUnit').jqxGrid('getcellvalue', rowindex, "unit_no");
                var wholesale_price1 = $('#gridUnit').jqxGrid('getcellvalue', rowindex, "wholesale_price");
                var retail_price1 = $('#gridUnit').jqxGrid('getcellvalue', rowindex, "retail_price");
                var buy_price1 = $('#gridUnit').jqxGrid('getcellvalue', rowindex, "buy_price");
                var cost_price_spec1 = $('#gridUnit').jqxGrid('getcellvalue', rowindex, "cost_price_spec");
                var lowest_price1 = $('#gridUnit').jqxGrid('getcellvalue', rowindex, "lowest_price");
                var contract_price1 = $('#gridUnit').jqxGrid('getcellvalue', rowindex, "contract_price")
                var cost_price_avg1 = $('#gridUnit').jqxGrid('getcellvalue', rowindex, "cost_price_avg")
                

                var weight1 = $('#gridUnit').jqxGrid('getcellvalue', rowindex, "weight");
                var volume1 = $('#gridUnit').jqxGrid('getcellvalue', rowindex, "volume");

                if (unit_no && value) {
                    var n = 2
                        if(value=="1") n=4
                        wholesale_price1 = (wholesale_price_unit * value);
                    wholesale_price1 = formatPrice(wholesale_price1,n)
                        retail_price1 = (retail_price_unit * value);
                    retail_price1 = formatPrice(retail_price1,n)
                        buy_price1 = (buy_price_unit * value);
                    buy_price1 = formatPrice(buy_price1,n)
                        cost_price_spec1 = (cost_price_spec_unit * value);
                    cost_price_spec1 = formatPrice(cost_price_spec1,n)
                        lowest_price1 = (lowest_price_unit * value);
                    lowest_price1 = formatPrice(lowest_price1, n)
                        contract_price1 = (contract_price_unit * value);
                    contract_price1 = formatPrice(contract_price1, n)
                     cost_price_avg1 = (cost_price_avg_unit * value);
                    cost_price_avg1 = formatPrice(cost_price_avg1, n)
                    
                        weight1 = weight_unit * value;
                    weight1 = formatPrice(weight1, 3)
                        volume1 = volume_unit * value;
                    volume1 = formatPrice(volume1, 3)
                        
                        $('#gridUnit').jqxGrid('setcellvalue', rowindex, "wholesale_price", wholesale_price1);
                        $('#gridUnit').jqxGrid('setcellvalue', rowindex, "retail_price", retail_price1);
                        $('#gridUnit').jqxGrid('setcellvalue', rowindex, "buy_price", buy_price1);
                        $('#gridUnit').jqxGrid('setcellvalue', rowindex, "cost_price_spec", cost_price_spec1);
                        $('#gridUnit').jqxGrid('setcellvalue', rowindex, "lowest_price", lowest_price1);
                    $('#gridUnit').jqxGrid('setcellvalue', rowindex, "contract_price", contract_price1);
                     $('#gridUnit').jqxGrid('setcellvalue', rowindex, "cost_price_avg", cost_price_avg1);
                     
                        $('#gridUnit').jqxGrid('setcellvalue', rowindex, "weight", weight1);
                        $('#gridUnit').jqxGrid('setcellvalue', rowindex, "volume", volume1);

                }
            }
            if (datafield == "unit_factor") {
                if (row > 0) {
                    //改包装率时，同时改变vue实例中的itemInfo
                    if(row == 1) vm.itemInfo.b_unit_factor = value
                    if(row ==2) vm.itemInfo.m_unit_factor = value
                    $('#gridUnit').jqxGrid('setcellvalue', row, "unit_factor", value);

                    //checkUnitsValid();//注释原因：保存和cellendedit会检查两次，提示两次
                    updatePriceByUnitFactor(row)
                    updateProfitRate();
                }
            }
            else if (datafield == "unit_no") {
                checkUnitsValid();
            }
            else if (datafield == "wholesale_price") {
                updatePriceByUnitPrice("wholesale_price")
                updateProfitRate();
            }
            else if (datafield == "retail_price") {
                updatePriceByUnitPrice("retail_price")
                }
            else if (datafield == "buy_price") {
                updatePriceByUnitPrice("buy_price")
                    updateProfitRate();
            }
            else if (datafield == "cost_price_spec") {
                updatePriceByUnitPrice("cost_price_spec")
                }
            else if (datafield == "lowest_price") {
                updatePriceByUnitPrice("lowest_price")
                }
            else if (datafield == "contract_price") {
                updatePriceByUnitPrice("contract_price")
            }
            else if (datafield == "cost_price_avg") {
               updatePriceByUnitPrice("cost_price_avg")
            }
            else if (datafield == "weight") {
                updatePriceByUnitPrice("weight")
                }
            else if (datafield == "volume") {
                updatePriceByUnitPrice("volume")
                }

            });
            
            /*
              $('#gridMenu_gridUnit').on('itemclick', function (event) {
                            var args = event.args;
                    var rowindex = $('#gridUnit').jqxGrid('getselectedrowindex');
                            if ($.trim($(args).text()) == '编辑') {
                                editrow = rowindex;
                                var offset = $('#~gridID').offset();

                            }
                            else if ($.trim($(args).text()) == '删除') {
                                var rowid = $('#gridUnit').jqxGrid('getrowid', rowindex);
                                $('#gridUnit').jqxGrid('deleterow', rowid);
                              }
              });
            */

            @Html.Raw(Model.SonItemList);
             attributeVm = new Vue({
              el: '#attributeWrapper',
              data() {
                return {
                  itemId: '',
                  son_mum_item: '',
                  itemUsed: false,                  // 商品使用
                  combineNeedCreate: false,         // 处理列表可以生成
                  distinctStock: false,             // 区分库存标记
                  distinctStockEditable: false,     // 可以进行区分库存标记
                  companyAvailAttributes: [],       // 公司可用属性
                  itemMumAttributes: [],            // 商品的mum_attributes
                  availAttrCombine:[],              // 商品的组合
                  sonItemList: [],                  // 区分库存子商品列表
                  deleteItemList: [],               // 存放需要删除的已经创建的子口味商品
                  optAttrNameInput: '',               // 新增新的子属性
                  filterAttrItem: [],
                  debounceTimer: null,
                  //remberSonItemPrice: false,
                  popverValue: {
                    
                  }
                }
              },
              mounted() {
                this.handleBaseAttrInfo()
              },
              methods: {
                nanoid (t=21) {
                     return crypto.getRandomValues(new Uint8Array(t)).reduce(((t,e)=>t+=(e&=63)<36?e.toString(36):e<62?(e-26).toString(36).toUpperCase():e>62?"-":"_"),"")
                },
                // 处理初始化基本信息
                handleBaseAttrInfo() {
                  this.itemId = $('#item_id').val()
                  // 0. 商品有无使用，
                  this.itemUsed = $('#itemUsed').val() === 'true'
                  this.son_mum_item = $('#son_mum_item').val()
                  if (this.son_mum_item) {
                    return
                  }
                  // 1. 获取公司的所有的属性及属性子选项
                  const availAttributes = JSON.parse(JSON.stringify(window.availAttributes))
                  const attrOptions = JSON.parse(JSON.stringify(window.attrOptions))
                  for (const attrOptionsKey in attrOptions) {
                    attrOptions[attrOptionsKey].forEach(item => { this.$set(item, 'isSelect', false)})
                  }
                  this.itemMumAttributes = JSON.parse(JSON.stringify(window.mumAttributes))
                  this.availAttrCombine = window.availAttrCombine? JSON.parse(JSON.stringify(window.availAttrCombine)) : null
                  this.sonItemList = window.SonItemList ? JSON.parse(JSON.stringify(window.SonItemList)):[]
                  this.sonItemList.forEach(sonItem => {
                    sonItem.other_info = sonItem.other_info ? JSON.parse(sonItem.other_info) : []
                  })
                  // 2. 处理公司可用属性情况
                  availAttributes.forEach(item => {
                    item.attrOption = []
                    if (attrOptions[item.attr_id]) {
                      item.attrOption = attrOptions[item.attr_id]
                    }
                  })
                  this.companyAvailAttributes =  JSON.parse(JSON.stringify(availAttributes))
                  // 3.处理mum_attribute
                  this.itemMumAttributes.forEach(mumItem => {
                    mumItem.attrOption = []
                    if (attrOptions[mumItem.attrID]) {
                      mumItem.attrOption = attrOptions[mumItem.attrID]
                      // 兼容老数据没有存的情况
                      if (mumItem.specOptInItem === false && (!mumItem.options || mumItem.options.length === 0)) {
                       mumItem.options = mumItem.attrOption.map(attrItem => ({
                           optID: attrItem.opt_id,
                           optName: attrItem.opt_name
                       }));
                      }
                    }
                    mumItem.distinct_stock_editable = false
                    const result = this.companyAvailAttributes.find(attr => attr.attr_id === mumItem.attrID)
                    if (result) {
                      mumItem.distinct_stock_editable = result.distinct_stock_editable
                    }
                    this.$set(this.popverValue, 'popver_' + mumItem.attrID, false);
                  })
                  // 4. 判断编辑
                  this.handleCheckDistinctStockAndEdit()
                  // 5. 处理组合情况
                  if (this.availAttrCombine === null) {
                    this.availAttrCombine = this.handleInitAvailAttrCombin(this.itemMumAttributes, 1)
                    if (this.distinctStock) {   // 区分库存商品需要
                      this.handleCombinSonItem(this.availAttrCombine)
                    }
                  } else {
                    if (this.distinctStock) {   // 区分库存商品处理兼容性数据 如手机端后续修改的情况
                      this.handleInitStockCombinSonItem(this.availAttrCombine)
                    } else { // 对于手机端创建的一维属性处理，又availAttrCombine但是不全或者是条码没同步
                        if(this.itemMumAttributes.length === 1) {
                            this.handleInitNoDistinctStockCombinSonItem(this.availAttrCombine)
                        }
                    }
                  }
                  
                  // 6.根据组合等情况生成停用商品
                 this.handleCombinToStopList()
                 
                 console.log('availAttrCombine', this.availAttrCombine)
                },
                // 生成初始化组合列表
                handleInitAvailAttrCombin(mumAttributes, status) {
                    const combinations = [];
                    if (mumAttributes.length === 0) {
                       return combinations
                    }
                    function permute(index, currentCombination, that) {
                        if (index === mumAttributes.length) {
                            combinations.push(currentCombination);
                            return;
                        }
                        const { options, attrID, specOptInItem } = mumAttributes[index];
                        options.forEach(option => {
                            //const newCombination = { ...currentCombination };
                            const newCombination = Object.assign({}, currentCombination);
                            if (specOptInItem) {
                                newCombination.bPrice = option.bPrice ? option.bPrice : '';
                                newCombination.mPrice = option.mPrice ? option.mPrice : '';
                                newCombination.sPrice = option.sPrice ? option.sPrice : '';
                                newCombination.bBarcode = option.bBarcode ? option.bBarcode : '';
                                newCombination.mBarcode = option.mBarcode ? option.mBarcode : '';
                                newCombination.sBarcode = option.sBarcode ? option.sBarcode : '';
                            }
                            const { optID, optName, attr_id } = option;
                
                            if (newCombination.son_options_id) {
                                newCombination.son_options_id += `_${optID}`;
                            } else {
                                newCombination.son_options_id =  optID;
                            }
                            if (newCombination.attrID) {
                                newCombination.attrID += `_${attrID}`;
                            } else {
                                newCombination.attrID =  attrID;
                            }
                             if (newCombination.optName) {
                                newCombination.optName += `_${optName}`;
                             } else {
                                newCombination.optName = optName
                             }
                             // newCombination.attrID = attrID; // 对于多维情况似乎没有存在的必要，目前予以保留，多维情况应该进行
                              // 生成随机的 item_id
                             newCombination.item_id = 'nanoid' + that.nanoid();
                             newCombination.combine = newCombination.son_options_id.split('_').map(item => item.length === 1 ? [item] : item);
                             permute(index + 1, newCombination, that);
                        });
                    }
                    permute(0, {
                      distinctStock: this.distinctStock,
                      //remberSonItemPrice: this.remberSonItemPrice,
                      item_id: '',
                      son_mum_item : this.itemId,
                      status: status
                    }, this);
                    return combinations;
                },
                // 对于opt_opt判断
                isMatchingSonOptionsId(itemA, itemB) {
                    const parts1 = itemA.son_options_id.split('_').sort();
                    const parts2 = itemB.son_options_id.split('_').sort();
                    return parts1.join('_') === parts2.join('_');
                },
                // 对于区分库存商品数据列表合并
                handleInitStockCombinSonItem(combineList) {
                  // nanoid的商品。区分库存，在已经创建的商品列表中查找，
                  combineList.forEach(combineItem => {
                  const findSonItem = this.sonItemList.find(sonItem => sonItem.item_id.toString() === combineItem.item_id.toString() )
                  if (findSonItem) {
                    if (findSonItem.status !== '' && Number(findSonItem.status) === 0) {
                      combineItem.status = 0
                    }
                    findSonItem.other_info.forEach(otherItem => {
                      combineItem[`${otherItem.unit_type}Barcode`] = otherItem.barcode
                      combineItem[`${otherItem.unit_type}Price`] = otherItem.wholesale_price
                    })
                  }
                  
                  })
                },
                handleInitNoDistinctStockCombinSonItem(combineList){
                // 去除不存在itemMumAttributes的组合
                    let updatedAvailAttrCombine = combineList.filter(item => {
                        // 步骤2: 查找 son_options_id 是否存在于 itemMumAttributes[0].options 中
                        let optionExists = this.itemMumAttributes[0].options.some(option => {
                            return option.optID === item.son_options_id;
                        });

                        // 如果不存在，optionExists 为 false，该对象将被过滤掉
                        if (!optionExists) {
                            return false;
                        }

                        // 步骤4: 找到匹配的选项对象
                        let matchingOption = this.itemMumAttributes[0].options.find(option => {
                            return option.optID+'' === item.son_options_id+''
                        });

                        // 步骤5和6: 检查并赋值
                        if (matchingOption) {
                            item.bBarcode = item.bBarcode || matchingOption.bBarcode;
                            item.mBarcode = item.mBarcode || matchingOption.mBarcode;
                            item.sBarcode = item.sBarcode || matchingOption.sBarcode;
                            item.bPrice = item.bPrice || matchingOption.bPrice;
                            item.mPrice = item.mPrice || matchingOption.mPrice;
                            item.sPrice = item.sPrice || matchingOption.sPrice;
                        }

                        // 如果存在，返回 true，保留该对象
                        return true;
                    });
                    // 更新 this.availAttrCombine
                    combineList = updatedAvailAttrCombine;
   
                },
                handleCombinSonItem(combineList) {
                  combineList.forEach(combineItem => {
                      if (combineItem.item_id.startsWith('nanoid')) {
                        const findSonItem = this.sonItemList.find(sonItem => this.isMatchingSonOptionsId(combineItem,sonItem))
                        if (findSonItem) {
                          combineItem.item_id = findSonItem.item_id
                          if (findSonItem.status !== '' && Number(findSonItem.status) === 0) {
                            combineItem.status = 0
                          }
                          findSonItem.other_info.forEach(otherItem => {
                            combineItem[`${otherItem.unit_type}Barcode`] = otherItem.barcode
                            combineItem[`${otherItem.unit_type}Price`] = otherItem.wholesale_price
                          })
                        }
                      }
                    })
                },
                // 创建停用的子商品列表清理
                handleCombinToStopList() {
                  const allCombine = this.handleInitAvailAttrCombin(this.itemMumAttributes, 0)
                  const filteredArray = allCombine.filter(allItem => !this.availAttrCombine.some(availItem => this.isMatchingSonOptionsId(allItem, availItem)))
                  this.handleCombinSonItem(filteredArray)
                 // this.availAttrCombine.push(...filteredArray)
                    for (let i = 0; i < filteredArray.length; i++) {
                        this.availAttrCombine.push(filteredArray[i]);
                    }
                },
                // 校验能否编辑属性
                handleCheckDistinctStockAndEdit() {
                  if (this.itemMumAttributes.length === 0) {
                    this.distinctStock = false
                    this.distinctStockEditable = false
                    //this.remberSonItemPrice = false
                  }
                  this.distinctStock = this.itemMumAttributes.some(item => item.distinctStock === true) // 存在区分库存属性
                  this.distinctStockEditable = this.itemMumAttributes.every(item => item.distinct_stock_editable === true)
                  //this.remberSonItemPrice = this.itemMumAttributes.some(item => item.remberSonItemPrice === true)
                 },
                // 增加新的属性\删除属性
                handleMumAttributeItemChange(attrItem, optFlag) {
                  if (this.itemUsed) {
                    if(this.itemMumAttributes.length > 0 && this.itemMumAttributes[0].distinctStock) {
                         bw.toast('商品已经使用，不能增减新的区分库存属性'); 
                         return
                      }
                  }
                  const findItem = this.itemMumAttributes.find(item => item.attrID === (attrItem.attr_id ? attrItem.attr_id : attrItem.attrID))
                  const findItemIndex = this.itemMumAttributes.findIndex(item => item.attrID === (attrItem.attr_id ? attrItem.attr_id : attrItem.attrID))
                  let changeFlag = true
                  const that = this
                  if(optFlag === 'del' || (this.itemId && this.itemUsed === false && this.availAttrCombine.length > 0)) {
                    this.$confirm(`${optFlag === 'add' ? '添加' : '删除'}属性将清空已经创建的属性商品, 是否继续?`, '提示', {
                      confirmButtonText: '确定',
                      cancelButtonText: '取消',
                      type: 'warning'
                    }).then(() => {
                     changeList()
                    }).catch(() => {
                      
                    })
                  } else {
                    changeList()
                    this.$set(this.popverValue, 'popver_' + attrItem.attrID, false);
                  }
                  function changeList() {
                    if (changeFlag) {
                    if (optFlag === 'add') {
                        if (findItem) {
                             bw.toast(`${attrItem.attr_name}属性存在，请勿重复添加`); 
                             return
                        }
                        let distinctStock = attrItem.distinct_stock
                        let distinct_stock_editable =  attrItem.distinct_stock_editable
                        
                        if (that.itemUsed) {    // 使用过就不能添加可编辑属性，且只能添加不区分库存属性
                          distinctStock = false
                          distinct_stock_editable = false
                        }
                        that.itemMumAttributes.push({
                            attrID:attrItem.attr_id,
                            attrName: attrItem.attr_name,
                            attrOption:attrItem.attrOption,
                            distinctStock: distinctStock,
                            distinct_stock_editable: distinct_stock_editable,
                            groupID : '',
                            options:[],
                            specOptInItem:attrItem.spec_opt_in_item
                        })
                      } 
                      else if (optFlag === 'del') { 
                        that.itemMumAttributes.splice(findItemIndex, 1)
                      }
                      
                      if(that.distinctStock) {
                        const tempDeleteItemList = that.availAttrCombine.filter(combineItem => {
                          return !combineItem.item_id.startsWith('nanoid')
                        })
                        //that.deleteItemList.push(...tempDeleteItemList)
                        for (let i = 0; i < tempDeleteItemList.length; i++) {
                            this.deleteItemList.push(tempDeleteItemList[i]);
                        }
                      }
                      that.availAttrCombine = []
                      that.handleAttrChange(that.itemMumAttributes)
                  }
                  }
                },
                // 增加属性新的子选项列表
                handleAddNewOptItem(mumItem) {
                  this.popverValue['popver_' + mumItem.attrID] = true
                  
                  this.optAttrNameInput = ''
                  this.filterAttrItem = []
                  mumItem.attrOption.forEach(item => { 
                    this.$set(item, 'isSelect', false)
                  })
                  // console.log('mumItem', mumItem)
                  // console.log('this.itemMumAttributes', this.itemMumAttributes)
                  // this.$forceUpdate()
                },
                // 确认添加新的属性
                handleConfirmAddNewOptItems(mumItem) {
                    this.$set(this.popverValue, 'popver_' + mumItem.attrID, false);
                  // bw.toast('生成商品中，请稍后...', 2000)
                  // 
                  // $('#popver_'+mumItem.attrID).parent().attr('aria-hidden', true)
                  this.$forceUpdate()
                  let addOption = mumItem.attrOption
                  .filter(item => item.isSelect === true)
                  .filter(item => {return !mumItem.options.some(mumOpt => item.opt_id === mumOpt.optID) })
                  const mumAttributesTemp = JSON.parse(JSON.stringify(this.itemMumAttributes))
                  const mumAttrItemTemp = mumAttributesTemp.find(item => item.attrID === mumItem.attrID)
                  mumAttrItemTemp.options = []
                  addOption.forEach(addItem => {
                    mumAttrItemTemp.options.push({  optID: addItem.opt_id,optName: addItem.opt_name })
                    mumItem.options.push({ optID: addItem.opt_id,  optName: addItem.opt_name })
                  })
                  this.handleAttrChange(mumAttributesTemp)
                }, 
                // 删除属性子选项
                handleDeleteOptItem(mumItem, optItem) {
                  if (this.itemUsed && mumItem.distinctStock) {
                    bw.toast('商品已经使用，不能删除区分库存属性选项。可在下方进行停用'); 
                    return
                  }
                  const index = mumItem.options.findIndex(item => item.optID === optItem.optID)
                  if (index > -1) {
                    mumItem.options.splice(index, 1)
                  }
                  if(this.distinctStock) {
                      const tempDeleteItemList = this.availAttrCombine
                      .filter(item => item.combine.some(combineItem => combineItem === optItem.optID))
                      .filter(combineItem => {
                        return !combineItem.item_id.startsWith('nanoid')
                      })
                      //this.deleteItemList.push(...tempDeleteItemList)
                        for (let i = 0; i < tempDeleteItemList.length; i++) {
                            this.deleteItemList.push(tempDeleteItemList[i]);
                        }
                  }
                  const result = this.availAttrCombine.filter(item => !item.combine.some(combineItem => combineItem === optItem.optID))
                  this.availAttrCombine = result
                },
                // 属性列表变化，商品行的变化更改
                handleAttrChange(mumAttributes) {
                  this.handleCheckDistinctStockAndEdit()
                  // 判断是否存在属性 空子属性
                  const findEmpty = mumAttributes.find(item => item.options.length === 0)
                  if (findEmpty) {
                    return
                  }
                  const tempCombinList =  this.handleInitAvailAttrCombin(mumAttributes, 1)
                   // 判断这个组合是否存在，存在就status 改为1， 不存在就push进去
                    tempCombinList.forEach(tempItem => {
                     const findItem = this.availAttrCombine.find(availItem => this.isMatchingSonOptionsId(tempItem, availItem))
                     const findItemIndex = this.deleteItemList.findIndex(availItem => this.isMatchingSonOptionsId(tempItem, availItem))
                     if (findItem) {
                       findItem.status = 1
                     } else {
                       this.availAttrCombine.push(tempItem)
                     }
                     // 从待删除列表中去除
                     if (findItemIndex > -1) {
                       this.deleteItemList.splice(findItemIndex, 1)
                     }
                   })
                },
                handleAttrItemChange(val,attrItem) {
                  attrItem.isSelect = val
                  this.$forceUpdate()
                  console.log('this.availAttrCombine', this.availAttrCombine)
                },
                handleSelectAllOptItem(mumItem, flag) {
                  mumItem.attrOption.forEach(item => { this.$set(item, 'isSelect', flag)})
                  this.$forceUpdate()
                },
                // 停用/启用
                handleCombineStatus(combineItem, status) {
                  combineItem.status = status
                },
                debounce(fn,wait){
                  
                },
                // 过滤子属性
                handleFilterAttr(keyword, mumItem) {
                  if(this.debounceTimer !== null){
                    clearTimeout(this.debounceTimer)
                  }
                  this.debounceTimer = setTimeout(() => {
                    if(keyword === '') {
                      this.filterAttrItem = []
                      return
                    }
                    var regex = new RegExp(keyword, 'i');
                    this.filterAttrItem = mumItem.attrOption.filter(attrItem => {
                      return regex.test(attrItem.opt_name); // 返回 true 或 false
                    })
                    }, 500)
                },
                // api 创建单个属性列表
                handleCreateOptItem(mumItem) {
                  
                  const findItem =  mumItem.attrOption.find(item => item.opt_name === this.optAttrNameInput)
                  if (findItem) {
                    bw.toast(`【${findItem.opt_name}】选项存在，请勿重复添加`)
                    return
                  }
                  const that = this
                  console.log('this.mumAttributes', this.itemMumAttributes)
                  $.ajax({
                        url: '/api/ItemEdit/CreateAttrOptItem',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                             operKey: g_operKey,
                             attr_id: mumItem.attrID,
                             opt_name: that.optAttrNameInput
                        }),
                        success: (res) => {
                          if (res.code !== 0) {
                            bw.toast('创建商品属性选项失败,请重试')
                            console.error('ajax CreateAttrOptItem catch:', res.message)
                            return
                          }
                          const createItem = {
                             attr_id: mumItem.attrID,
                             isSelect : true,
                             opt_id : res.result.opt_id,
                             opt_name :res.result.opt_name
                          }
                          mumItem.attrOption.unshift(createItem) 
                          this.filterAttrItem.push(createItem)
                        },
                        error: err => {
                            bw.toast('网络错误:' + err.responseText)
                            console.error('ajax CreateAttrOptItem error:', err)
                        }
                  })
                  
                },
                // 保存并新增处理已有数据itemid
                handleCopyCreateNewItem() {
                  this.availAttrCombine.forEach(item => {
                    item.item_id = 'nanoid' + this.nanoid();
                    item.son_mum_item = '';
                  })
                  this.deleteItemList.forEach(item => {
                      item.item_id = 'nanoid' + this.nanoid();
                      item.son_mum_item = '';
                    })
                }
               
              },
            })
            $("#div_help").jqxPopover({ autoClose: true, position: "bottom", title: "", offset: { left: 0, top: -8 }, showCloseButton: false, selector: $("#help") });

        });
        window.onresize = function () {
            // var windowWidth = window.innerWidth;
            //  $("#gridUnit").jqxGrid(
            //      {
            //          width: windowWidth - 20
            //     });
        };
        //$('#gridUnit').attr('JSCellBeginEdit')
        function checkUnitsComplete() {

            for (var i = 0; i < 3; i++) {
                var unit_no = $('#gridUnit').jqxGrid('getcelltext', i, "unit_no").trim();
                var unit_factor = parseFloat($('#gridUnit').jqxGrid('getcelltext', i, "unit_factor"));
                if (unit_no && !unit_factor) {
                    bw.toast(`请输入单位${unit_no}的包装率`, 5000);
                    return false;
                }
                else if(!unit_no && unit_factor) {
                    bw.toast(`请输入包装率${unit_factor}对应的单位`,5000);
                    return false;
                }
            }
            return true;
        }
        function checkUnitsValid() {
            //判断单位是否有重复
            var units = {}
                for (var i = 0; i < 3; i++) {
                var unit_no = $('#gridUnit').jqxGrid('getcelltext', i, "unit_no").trim();
                if (unit_no) {
                    if (units[unit_no]) {
                        bw.toast(`单位${unit_no}被用在了多处`, 5000);
        return false;
                        }
                        units[unit_no] = unit_no;
                    }
                }

                 //判断大单位的包装率是否是中单位的整数倍
                var unit_factor1 = parseFloat($('#gridUnit').jqxGrid('getcelltext', 1, "unit_factor"));
            var unit_factor2 = parseFloat($('#gridUnit').jqxGrid('getcelltext', 2, "unit_factor"));
      
        if (unit_factor1 && unit_factor2) {
            var bInvaid = false;
            if (unit_factor1 == unit_factor2) {
                bInvaid = true;
            }
            else if (unit_factor1 > unit_factor2) {
             //取消大中的单位整数倍关系
            //    if (Math.abs(unit_factor1 % unit_factor2)>0.01) {
            //        bInvaid = true;
            //    }
            }
            else {
                if (Math.abs(unit_factor2 % unit_factor1)>0.01) {
                    bInvaid = true;
                }
            }

            if (bInvaid) {
                bw.toast('大单位包装率必须大于中单位包装率', 5000);
                return false;
            }
        }
        return true;
            }
        function checkDataValid() {
            if (!checkUnitsValid()) {
                return false;
            }
            if (!checkUnitsComplete()) {
                return false;
            }
            return true;
        }
        function dealFormData(formData) {
            // Deal item images.
            console.log('readItemImages started');
            console.log("$('#item_images'):" + $('#item_images'))
            console.log("$('#item_videos'):" + $('#item_videos'))
            console.log("$('#item_videos').val():" + $('#item_videos').val())

            console.log("formData:" + formData)
            var images = { "main": null, "tiny": null, "other": [] };
            var counter = 0;
            for (var a = 0; a <= 30; a++) {
                var imgid = 'uploaded-image_' + a;
                var showDom = document.getElementById(imgid)
                    if (showDom == undefined) continue;
                if (showDom.dataset.isnull != "true") {
                    if (counter == 0) {
                        images.main = showDom.src;
                        counter++;
                    }
                    else{
                        images.other.push(showDom.src)
                        }
                }
            }
            var originImg_str = $('#item_images').val();
            if(originImg_str!=null && originImg_str!=undefined && originImg_str!=""){
                var originImg = JSON.parse(originImg_str);
                if (images.main!=null && images.main.match("main.")) { // 主图含main代表主图未变; 加‘.’防止BASE64混入
                    images.tiny = originImg.tiny;
                }
            }

            formData.item_images = JSON.stringify(images); // Activate infact.
                $('#item_images').val(JSON.stringify(images)); // Save for possible use.
            console.log("$('#item_images').val():" + $('#item_images').val())
            console.log('readItemImages completed');
            // 上架单位丢失
            const mallUnitSource = [{ v: 'b', l: '大' }, { v: 'm', l: '中' }, { v: 's', l: '小' }];
            if (!formData.mall_units && formData.mall_units_LABEL) {
                const labelArr = formData.mall_units_LABEL.split(',').map(l => l.trim());
                const labelToValueMap = mallUnitSource.reduce((acc, item) => {
                    acc[item.l] = item.v;
                    return acc;
                }, {});
                const valueArr = labelArr.map(label => labelToValueMap[label]).filter(Boolean);
                formData.mall_units = valueArr.join(',');
            }

            //视频
            console.log('readItemVideo started');
            var videos = {
                "main": {
                    path: null,     // 压缩视频路径
                    title: null,  // 压缩视频标题
                    showcard: false
                },
                "tiny": {
                    path: null,     // 压缩视频路径
                    title: null,  // 压缩视频标题
                    showcard: false
                },
                "other": [],
                "videos_delete": []
            };
            let item_videos = $('#item_videos').val()
            //let videos_origin = item_videos? JSON.parse(item_videos):'';

            let href = "@Html.Raw(Model.ObsBucketLinkHref)/"
            
            //记录原来的主视频（为了加入删除队列）       
            var counter = 0;
            var mainFlag = 0;
            var videos_origin
            if (item_videos) {
                videos_origin = JSON.parse(item_videos);
                var mainpath_origin = !videos_origin.main ? '' : href + videos_origin.main.path;
            }
            //形成数组
            for (var a = 0; a <= 30; a++) {
                var videoid_main = 'uploaded-video-main_' + a;
                var videoid_other = 'uploaded-video-other_' + a;
                var titleid = 'title_' + a;
                var titleotherid = 'titleother_' + a;
                var showcardid = 'show-card_' + a;
                var showDom_main = document.getElementById(videoid_main)
                var showDom_main_title = document.getElementById(titleid)
                var showDom_main_showcard = document.getElementById(showcardid)
                var showDom_other = document.getElementById(videoid_other)
                var showDom_other_title = document.getElementById(titleotherid)
                if (showDom_main== undefined && showDom_other == undefined) continue;
                // if (item_videos) {
                 //把原来的主视频删除
                    // if (mainpath_origin && (showDom_main ? mainpath_origin != showDom_main.src : true) && !mainFlag) 
                    //     { 
                    //         videos.videos_delete.push(mainpath_origin); mainpath_origin = null; 
                    //     }
                //把原来的其他视频删除
                    // if (item_videos != null && item_videos != undefined && item_videos != "") {
                    //     if (videos_origin.other[a] != null && videos_origin.other[a] != undefined && videos_origin.other[a] != "") {
                    //         if (videos_origin.other[a].path != null && videos_origin.other[a].path != undefined && videos_origin.other[a].path != "") {
                    //             var otherpath_origin = videos_origin.other[a] == null ? '' : href + videos_origin.other[a].path;
                    //             if (otherpath_origin && (showDom_other ? otherpath_origin != showDom_other.src : true)) 
                    //             {
                    //                 videos.videos_delete.push(otherpath_origin)
                    //             }
                    //         }
                    //     }
                    //   }
                // }

                if (showDom_main)
                {
                    if (showDom_main.src) {
                        videos.main.path = showDom_main.src.includes("__tiny.mp4") ? showDom_main.src.replace("__tiny.mp4", "__main.mp4") : showDom_main.src;
                    videos.main.title = showDom_main_title.value;
                    // 获取勾选框的选中状态
                    videos.main.showcard = showDom_main_showcard ? showDom_main_showcard.checked : false;
                    videos.tiny.path = showDom_main.src;
                    videos.tiny.title = videos.main.title;
                    videos.tiny.showcard = videos.main.showcard;
                    //videos.main.showcard = showDom_main_showcard.checked;
                    console.log("videos.main.path:" + videos.main.path)
                    console.log("videos.main.title:" + videos.main.title)
                    console.log("videos.main.showcard:" + videos.main.showcard)
                    counter++;
                    mainFlag = 1;
                    }
                }
                if (showDom_other) {
                    if (mainFlag==1&&showDom_other.dataset.isnull != "true") {
                    var videoInfo = {
                        path: showDom_other.src,
                        title: showDom_other_title.value,
                        showcard: false
                    };
                    videos.other.push(videoInfo)
                    counter++;
                    }
                }
                
            }
            var originVideo_str = $('#item_videos').val();
            if (originVideo_str != null && originVideo_str != undefined && originVideo_str != "") {
                var originVideo = JSON.parse(originVideo_str);
                if (videos.main != null) {
                    if (videos.main.path != null) {if ( videos.main.path.match("main.path") && videos.main.title.match("main.title") && videos.main.showcard.match("main.showcard")) { // 主图含main代表主图未变; 加‘.’防止BASE64混入
                    videos.tiny = originVideo.tiny;
                    }}
                }
            }

            formData.item_videos = JSON.stringify(videos); // Activate infact.
            $('#item_videos').val(JSON.stringify(videos)); // Save for possible use.
            console.log("JSON.stringify(videos):" + JSON.stringify(videos))
            console.log("$('#item_videos').val():" + $('#item_videos').val())
            console.log('readItemVideo completed');
            var item_desc = tinymce.get('tinymce-editor').getContent(); // 直接获取富文本内容
            item_desc = item_desc.replace(/<img(.*?)>/g, function (match, capture) {
                if (!/width=["'][^"']*["']/.test(capture)) {
                    return '<img' + capture + ' width="100%" />';
                }
                return match; 
            });
            item_desc = item_desc.replace(/'/g, '"');
            $('#item_desc').val(item_desc);
            formData.item_desc =item_desc;

            var maxFactor = 1;
            var bigUnitRowIndex = -1;
            var smallBuyPrice=''
            for (var i = 0; i < formData.gridUnit.length; i++) {
                var row = formData.gridUnit[i];
                row.unit_factor=parseFloat(row.unit_factor);
                if (row.unit_factor > maxFactor) {
                    maxFactor = row.unit_factor;
                    bigUnitRowIndex = i;
                }
                if (row.buy_price) smallBuyPrice = row.buy_price / row.unit_factor
            }

            for (var i = 0; i < formData.gridUnit.length; i++) {
                var row = formData.gridUnit[i];
                row.unit_factor = parseFloat(row.unit_factor);
                if (!row.buy_price && smallBuyPrice) row.buy_price = smallBuyPrice * row.unit_factor
                if(row.barcode) row.barcode = row.barcode.trim()
            }

            formData.gridUnit[0]["unit_type"] = 's';
            if (bigUnitRowIndex == 2) {
                formData.gridUnit[1].unit_type = 'm';
                formData.gridUnit[2].unit_type = 'b';
            }
            else if (formData.gridUnit.length == 3) {
                formData.gridUnit[2].unit_type = 'm';
                formData.gridUnit[1].unit_type = 'b';
            }
            else if (formData.gridUnit.length == 2) {
                formData.gridUnit[1].unit_type = 'b';
            }

            formData.cost_price_spec = formData.gridUnit[0].cost_price_spec;
          
            
            formData.attrInfo = JSON.parse(JSON.stringify({
                distinctStock: attributeVm.distinctStock,
                itemMumAttributes: attributeVm.itemMumAttributes,
                availAttrCombine: attributeVm.availAttrCombine,
                deleteItemList: attributeVm.deleteItemList,
                //remberSonItemPrice:attributeVm.remberSonItemPrice,
            }))
            formData.attrInfo.itemMumAttributes.forEach(mumItem => {
              mumItem.distinctStock = attributeVm.distinctStock
               // mumItem.remberSonItemPrice = attributeVm.remberSonItemPrice
              delete mumItem.attrOption
            })
            formData.attrInfo.availAttrCombine.forEach(availItem => {
              availItem.item_name = `${formData.item_name}(${availItem.optName})`
              availItem.py_str = availItem.item_name.ToPinYinCode()
              availItem.distinctStock = attributeVm.distinctStock
              //availItem.remberSonItemPrice = attributeVm.remberSonItemPrice
            })
            if (formData.attrInfo.distinctStock) {
              formData.attrInfo.availAttrCombine = formData.attrInfo.availAttrCombine.filter(availItem => {
                return availItem.status !== 0 || (availItem.status === 0 && !availItem.item_id.startsWith('nanoid'))
              })
            } else {
              formData.attrInfo.availAttrCombine = formData.attrInfo.availAttrCombine.filter(availItem => {
                  return availItem.status === 1
              })
            }
            if(formData.attrInfo.itemMumAttributes.length === 1) {
                const mumAttribute = formData.attrInfo.itemMumAttributes[0]
                mumAttribute.options.forEach(mumOptitem => {
                  const findItem = formData.attrInfo.availAttrCombine.find(availItem => mumOptitem.optID === availItem.son_options_id)
                  if (findItem) {
                    mumOptitem.bBarcode = findItem.bBarcode
                    mumOptitem.bPrice = findItem.bPrice
                    mumOptitem.mBarcode = findItem.mBarcode
                    mumOptitem.mPrice = findItem.mPrice
                    mumOptitem.sBarcode = findItem.sBarcode
                    mumOptitem.sPrice = findItem.sPrice
                  }
                })
            }
            formData.attrInfo.deleteItemList = formData.attrInfo.deleteItemList.filter(delItem => {
               return !delItem.item_id.startsWith('nanoid')
            })
            // formData.mum_attributes = mumAttrs
            if (window.submitPricePlan) { 
                debugger
               const parseResult = JSON.parse(JSON.stringify(window.submitPricePlan))
               //将没有选价格方案名称的过滤
              const  pricePlanSubmitInfo= parseResult.filter(plan => { 
                  return plan.plan_id != '' && plan.plan_id != null
                })

               //修改discount的值，数据库存的是小数
                pricePlanSubmitInfo.forEach(plan => {
                    if(plan.discount != '' && plan.discount != null )
                        plan.discount = toMoney(plan.discount/100,2)
                })
                formData.pricePlanSubmitInfo=pricePlanSubmitInfo
            }
        }
        function addImage() {
            $("#upload-image").trigger("click");
        }
        function dealGridRowsOnLoad(rows) {
            var rows_count = 0;
            rows.forEach((row) => {

                if (row.unit_factor!="") rows_count+=1
            })
            $('#unitRows').jqxInput('val', rows_count);
            return rows
        }
        function previewImage(uploadDocument,showDocument) {
            var reads = new FileReader();
            var file = uploadDocument.files[0];
            console.log(file)
            reads.readAsDataURL(file)
            reads.onload = function (e) {
                console.log(e)
                showDocument.src = e.currentTarget.result
            }
        }
        
        function onMouseClickItemImage(nodeid, imgnodeid, event){

        }
        function onMouseEnterItemImage(nodeid, imgnodeid, event) {

        }
        function onMouseEnterItemImage_Span(nodeid, imgnodeid, event) {
            if(document.getElementById(nodeid).getElementsByTagName('svg').length != 0){
                var pid = '#' + document.getElementById(nodeid).dataset.cbtnid;
                $(pid).show();
                var pid2 = '#' + document.getElementById(nodeid).dataset.ebtnid;
                $(pid2).show();
                var pid3 ='#' + document.getElementById(nodeid).dataset.dbtnid;
                $(pid3).show();
                return;
            }
            var popi = nodeid.charAt(nodeid.length-1)
            var popid = 'popDeleteOthImg_' + popi
            var pop = `<div id="${popid}" data-imgid="${imgnodeid}" onclick="onImgDeleteBtnClicked('${imgnodeid}','${nodeid}')" title="删除此图片" style="z-index:999999999;position:absolute;  top: -7px; right: 0px; "><svg width="15" height="15" style="cursor:pointer;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#close'  /> </svg></div> `
            var popid2 = 'popEditImg_' + popi
            var pop2 = `<div id="${popid2}" data-imgid="${imgnodeid}" onclick="onImgEditBtnClicked('${imgnodeid}')" title="编辑此图片" style="z-index:999999999;position:absolute;  top:-7px; right: 19px "><svg width="15" height="15" style="cursor:pointer;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#pen'  /> </svg></div> `;
            var popid3 ='popDownload_' +popi
            var pop3 =  `<div id="${popid3}" data-imgid="${imgnodeid}"  onclick="onImgDownloadClicked('${imgnodeid}')" title="下载此图片" style="z-index:999999999;position:absolute;  top:-7px; right: 43px "><img src="../images/download.svg" alt="" width="16" height="16"  style="cursor:pointer;"></a></div> `;
            var nodeR = '#'+nodeid
            var node = $(nodeR)
            node.append(pop)
            node.append(pop2)
            node.append(pop3)
            document.getElementById(imgnodeid).dataset.clicked = '1';
            document.getElementById(nodeid).dataset.cbtnid = popid
            document.getElementById(nodeid).dataset.ebtnid = popid2
            document.getElementById(nodeid).dataset.dbtnid = popid3
        }
        
        function onImgDeleteBtnClicked(imgnodeid, dbtnid){
            document.getElementById(imgnodeid).src = ''
            document.getElementById(imgnodeid).dataset.isnull = 'true'
            document.getElementById(dbtnid).remove();
            pictNumMaximum++;
        }
        
        function onImgEditBtnClicked(imgnodeid) {
            document.getElementById('edit-image').dataset.target = imgnodeid;
            $("#edit-image").trigger("click");
        }
        function onMouseLeaveItemImage(id, id2,id3) {
            if (id != '') {
                var popDiv = '#' + id;
                $(popDiv).hide();
            }
            if (id2 != '') {
                var popDiv2 = '#' + id2;
                $(popDiv2).hide();
            }
            if (id3 != '') {
                var popDiv3 = '#' + id3;
                $(popDiv3).hide();
            }
        }
        function addVideo_main() {
            $("#upload-video-main").trigger("click");
        }
        function addVideo_other() {
            $("#upload-video-other").trigger("click");
        }
        function previewVideo(uploadDocument, showDocument) {
           var file = uploadDocument.files[0];
            var reader = new FileReader();
            reader.onload = function (e) {
                showDocument.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
        function onMouseEnterItemVideoMain_Span(nodeid, videomainnodeid, event) {
            if (document.getElementById(nodeid).getElementsByTagName('svg').length != 0) {
                var pid = '#' + document.getElementById(nodeid).dataset.cbtnid;
                $(pid).show();
                var pid2 = '#' + document.getElementById(nodeid).dataset.ebtnid;
                $(pid2).show();
                // var pid3 = '#' + document.getElementById(nodeid).dataset.dbtnid;
                // $(pid3).show();
                return;
            }
            var popi = nodeid.charAt(nodeid.length - 1)
            var popid = 'popDeleteOthVideoMain_' + popi
            // var pop = `<div id="${popid}" data-video-mainid="${videomainnodeid}" onclick="onVideoMainDeleteBtnClicked('${videomainnodeid}','${nodeid}')" title="删除主视频" style="z-index:999999999;position:absolute;  top: -7px; right: 0px; "><svg width="15" height="15" style="cursor:pointer;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#close'  /> </svg></div> `
            // var popid2 = 'popEditVideoMain_' + popi
            // var pop2 = `<div id="${popid2}" data-video-mainid="${videomainnodeid}" onclick="onVideoEditBtnClicked('${videomainnodeid}')" title="编辑主视频" style="z-index:999999999;position:absolute;  top:-7px; right: 19px "><svg width="15" height="15" style="cursor:pointer;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#pen'  /> </svg></div> `;
            // var popid3 = 'popDownload_' + popi
            // var pop3 = `<div id="${popid3}" data-video-mainid="${videomainnodeid}"  onclick="onVideoDownloadClicked('${videomainnodeid}')" title="下载主视频" style="z-index:999999999;position:absolute;  top:-7px; right: 43px "><img src="../images/download.svg" alt="" width="16" height="16"  style="cursor:pointer;"></a></div> `;
            var pop = `<div id="${popid}" data-video-mainid="${videomainnodeid}" onclick="onVideoMainDeleteBtnClicked('${videomainnodeid}','${nodeid}')" title="删除主视频" style="z-index:999999999;position:absolute; top: -7px; right: 0px; "><svg width="15" height="15" style="cursor:pointer;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#close'  /> </svg></div>`;
            var popid2 = 'popEditVideoMain_' + popi;
            var pop2 = `<div id="${popid2}" data-video-mainid="${videomainnodeid}" onclick="onVideoEditBtnClicked('${videomainnodeid}')" title="编辑主视频" style="z-index:999999999;position:absolute; top:-7px; right: 19px "><svg width="15" height="15" style="cursor:pointer;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#pen'  /> </svg></div>`;
            // var popid3 = 'popDownload_' + popi;
            // var pop3 = `<div id="${popid3}" data-video-mainid="${videomainnodeid}" onclick="onVideoDownloadClicked('${videomainnodeid}')" title="下载主视频" style="z-index:999999999;position:absolute; top:-7px; right: 43px "><img src="../images/download.svg" alt="" width="16" height="16" style="cursor:pointer;"></a></div>`;

            var nodeR = '#' + nodeid
            var node = $(nodeR)
            node.append(pop)
            node.append(pop2)
            // node.append(pop3)
            document.getElementById(videomainnodeid).dataset.clicked = '1';
            document.getElementById(nodeid).dataset.cbtnid = popid
            document.getElementById(nodeid).dataset.ebtnid = popid2
            // document.getElementById(nodeid).dataset.dbtnid = popid3
        }
        function onVideoMainDeleteBtnClicked(videomainnodeid, dbtnid) {
            // 弹出二次确认对话框
            const confirmation = confirm("您确定要删除这个视频吗？一旦删除将无法找回。");

            // 如果用户点击了“确定”，则执行删除操作
            if (confirmation) {
            uploadVideosrc = document.getElementById(videomainnodeid).src;
            uploadVideo(2, uploadVideosrc,null, videomainnodeid);
            othVideoMainNum--
            var titleid = 'title_' + othVideoMainNum
            var showcardid = 'show-card_' + othVideoMainNum
            var item_videos = $('#item_videos').val()
            var VideoMainTitle = document.getElementById(titleid).value
            var VideoMainShowcard = document.getElementById(showcardid).checked;
            document.getElementById(videomainnodeid).src = ''
            document.getElementById(videomainnodeid).dataset.isnull = 'true'
            document.getElementById(dbtnid).remove();
           
            
            // Keep add button on the last position.
            document.getElementById('selectTitle').remove();
            document.getElementById('selectShowCard').remove();
            var addhtml = `<div id="uploadVideoMain" onclick="addVideo_main()" title="添加商品主视频" style="margin-right:10px;width:100px; height:100px; text-align: center; cursor: pointer;"><svg width="30px" height="30px" fill="#AAA" style="margin-top:30px;vertical-align: middle;"><use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  /></svg></div>`;
            var addhtml1 = `<div style="margin-top:5px;"><div class="news-filter"  id="selectTitle">
                                                <select name="name" id="${titleid}">
                                                <option value="0">效果</option>
                                                <option value="1">讲解</option>
                                                <option value="2">宝贝介绍</option>
                                                <option value="3">推荐</option>
                                                <option value="4">教程使用</option>
                                                <option value="5">用途</option>
                                                <option value="6">宝贝讲解</option>
                                            </select>
                                        </div>
                                        <div class="show-card" display:block; id="selectShowCard">
                                            <span style="color:#C0C0C0;font-size:12px;margin-left:5px;margin-top:10px;"> 更多应用场景:</span>
                                                        <div class="checkbox-container" style="margin-top:10px;font-size:15px">
                                                              <input type="checkbox" id="${showcardid}">
                                                    <label for="showcard">推荐卡片展示</label>
                                            </div>
                                        </div></div>`;
            $('#video_main').append(addhtml)
            $('#video_main').append(addhtml1)
            // 设置下拉框的值
            $('#' + titleid).val(VideoMainTitle);
            // 设置勾选框的选中状态
            $('#' + showcardid).prop('checked', VideoMainShowcard);}
        }
        function onMouseEnterItemVideoOther_Span(nodeid, videomainnodeid, event) {
            if (document.getElementById(nodeid).getElementsByTagName('svg').length != 0) {
                var pid = '#' + document.getElementById(nodeid).dataset.cbtnid;
                $(pid).show();
                var pid2 = '#' + document.getElementById(nodeid).dataset.ebtnid;
                $(pid2).show();
                // var pid3 = '#' + document.getElementById(nodeid).dataset.dbtnid;
                // $(pid3).show();
                return;
            }
            var popi = nodeid.charAt(nodeid.length - 1)
            var popid = 'popDeleteOthVideoMain_' + popi
            // var pop = `<div id="${popid}" data-video-mainid="${videomainnodeid}" onclick="onVideoMainDeleteBtnClicked('${videomainnodeid}','${nodeid}')" title="删除主视频" style="z-index:999999999;position:absolute;  top: -7px; right: 0px; "><svg width="15" height="15" style="cursor:pointer;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#close'  /> </svg></div> `
            // var popid2 = 'popEditVideoMain_' + popi
            // var pop2 = `<div id="${popid2}" data-video-mainid="${videomainnodeid}" onclick="onVideoEditBtnClicked('${videomainnodeid}')" title="编辑主视频" style="z-index:999999999;position:absolute;  top:-7px; right: 19px "><svg width="15" height="15" style="cursor:pointer;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#pen'  /> </svg></div> `;
            // var popid3 = 'popDownload_' + popi
            // var pop3 = `<div id="${popid3}" data-video-mainid="${videomainnodeid}"  onclick="onVideoDownloadClicked('${videomainnodeid}')" title="下载主视频" style="z-index:999999999;position:absolute;  top:-7px; right: 43px "><img src="../images/download.svg" alt="" width="16" height="16"  style="cursor:pointer;"></a></div> `;
            var pop = `<div id="${popid}" data-video-mainid="${videomainnodeid}" onclick="onVideoOtherDeleteBtnClicked('${videomainnodeid}','${nodeid}')" title="删除该视频" style="z-index:999999999;position:absolute; top: -7px; right: 0px; "><svg width="15" height="15" style="cursor:pointer;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#close'  /> </svg></div>`;
            var popid2 = 'popEditVideoMain_' + popi;
            var pop2 = `<div id="${popid2}" data-video-mainid="${videomainnodeid}" onclick="onVideoEditBtnClicked('${videomainnodeid}')" title="编辑该视频" style="z-index:999999999;position:absolute; top:-7px; right: 19px "><svg width="15" height="15" style="cursor:pointer;"> <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#pen'  /> </svg></div>`;
            // var popid3 = 'popDownload_' + popi;
            // var pop3 = `<div id="${popid3}" data-video-mainid="${videomainnodeid}" onclick="onVideoDownloadClicked('${videomainnodeid}')" title="下载该视频" style="z-index:999999999;position:absolute; top:-7px; right: 43px "><img src="../images/download.svg" alt="" width="16" height="16" style="cursor:pointer;"></a></div>`;

            var nodeR = '#' + nodeid
            var node = $(nodeR)
            node.append(pop)
            node.append(pop2)
            // node.append(pop3)
            document.getElementById(videomainnodeid).dataset.clicked = '1';
            document.getElementById(nodeid).dataset.cbtnid = popid
            document.getElementById(nodeid).dataset.ebtnid = popid2
            // document.getElementById(nodeid).dataset.dbtnid = popid3
        }
        function onVideoOtherDeleteBtnClicked(videomainnodeid, dbtnid) {
            // 弹出二次确认对话框
            const confirmation = confirm("您确定要删除这个视频吗？一旦删除将无法找回。");

            // 如果用户点击了“确定”，则执行删除操作
            if (confirmation) {
            uploadVideosrc = document.getElementById(videomainnodeid).src;
            uploadVideo(2, uploadVideosrc,null, videomainnodeid);
            document.getElementById(videomainnodeid).src = ''
            document.getElementById(videomainnodeid).dataset.isnull = 'true'
            document.getElementById(dbtnid).remove();
            }
        }
        function onVideoEditBtnClicked(videomainnodeid) {
            document.getElementById('edit-video-main').dataset.target = videomainnodeid;
            $("#edit-video-main").trigger("click");
        }
        function onImgDownloadClicked(imgnodeid) {
            var video = document.createElement('video');
            video.setAttribute('crossOrigin', 'anonymous');
            video.onloadeddata = function () {
                var canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                var context = canvas.getContext('2d');
                context.drawImage(video, 0, 0);
                var url = canvas.toDataURL('video/mp4');
                var a = document.createElement('a');
                a.download = 'Yj商品视频';
                a.href = url;
                var event = new MouseEvent('click');
                a.dispatchEvent(event);
            }
            video.src = document.getElementById(videomainnodeid).src + '?time=' + Date.now();

        }
        window.onmousewheel = function (e) {
            onMouseLeaveItemImage;
        }
        function onVideoDownloadClicked(videomainnodeid) {
            var image = new Image()
            // 解决跨域 Canvas 污染问题
            image.setAttribute('crossOrigin', 'anonymous')
            image.onload = function () {
                var canvas = document.createElement('canvas')
                canvas.width = image.width
                canvas.height = image.height

                var context = canvas.getContext('2d')
                context.drawImage(image, 0, 0, image.width, image.height)
                var url = canvas.toDataURL('image/png')
                var a = document.createElement('a')
                // 创建一个单击事件
                var event = new MouseEvent('click')
                a.download = name || 'Yj商品图片'
                a.href = url

                // 触发a的单击事件
                a.dispatchEvent(event)
            }
            //加时间戳解决客户端跨域问题，不加仅浏览器可以实现
            image.src = document.getElementById(videomainnodeid).src + '?time=' + Date.now();

        }
        window.onmousewheel = function (e) {
            onMouseLeaveItemImage;
        }
        // 将 blobToBase64 函数移到 tinymce.init 外部
        function blobToBase64(blob, callback) {
            const reader = new FileReader();
            reader.onloadend = function () {
                callback(reader.result);
            };
            reader.readAsDataURL(blob);
        }
        //富文本
        tinymce.init({
            selector: '#tinymce-editor',
            plugins: 'lists link image media table code fullscreen',
            toolbar: 'undo redo | formatselect fontsizeselect | bold italic | alignleft aligncenter alignright | bullist numlist outdent indent | image',
            height: 400,
            branding: false,
            menubar: false,
            media_live_embeds: true,
            content_style: `
                body { font-family:Helvetica,Arial,sans-serif; font-size:14px; }
                img { width: 100%; height: auto; } /* 全局设置图片宽度 100% */
            `,
            language: 'zh_CN',
            language_url: '/tinymce_5.8.2/langs/zh_CN.js',
            image_dimensions: false,

            // 修改上传逻辑，将 blob 转换为 Base64 格式传递给 uploadDesc
            images_upload_handler: function (blobInfo, success, failure) {
                const fileName = blobInfo.filename();
                const fileExtension = fileName.split('.').pop().toLowerCase();
                const type = ['jpg', 'jpeg', 'png'].includes(fileExtension) ? 'image' : 'unknown';

                if (type === 'image') {
                    const uploadDescFlag = 3; 
                    descImageNum++;  // 计数器
                    const descid = null; // 可选，描述ID

                    // 将 Blob 转换为 Base64 字符串
                    blobToBase64(blobInfo.blob(), function (base64Data) {
                        // 调用 uploadDesc 方法上传图片
                        uploadDesc(uploadDescFlag, base64Data, descImageNum, descid,function (data) {
                            if (data.result === 'OK') {
                                const newDescPath = data.newPath.substring(9, data.newPath.length - 2);  // 从第8个字符开始，到倒数第2个字符结束
                                success(newDescPath); // 将返回的路径插入到富文本框中
                            } else {
                                failure('图片上传失败：' + data.msg);  // 如果上传失败，提示错误信息
                                descImageNum--;  // 计数器减1
                            }
                        });
                    });
                } else {
                    failure('仅支持上传 .jpg, .jpeg, .png 格式的图片');  // 如果上传的不是图片格式，提示错误
                }
            },
            // 页面加载时加载已有的富文本内容，并处理图片和视频的路径
            init_instance_callback: function (editor) {
                const item_desc = $('#item_desc').val(); // 获取富文本内容（例如从后端传来的数据）
                if (item_desc) {
                    // 将获取到的富文本内容加载到编辑器
                    editor.setContent(item_desc);
                }
            },
            setup: function (editor) {
                editor.on('paste', function (e) {
                    const clipboardData = e.clipboardData;

                    if (clipboardData && clipboardData.items) {
                        Array.from(clipboardData.items).forEach(function (item) {
                            if (item.type.indexOf('image') > -1) {
                                // 只处理图片，不阻止默认粘贴行为
                                const blob = item.getAsFile();

                                // 通过 Base64 处理上传图片
                                blobToBase64(blob, function (base64Data) {
                                    uploadDesc(3, base64Data, descImageNum, null, function (data) {
                                        if (data.result === 'OK') {
                                            const newDescPath = data.newPath.substring(9, data.newPath.length - 2);
                                            const imgTag = editor.dom.select('img')[editor.dom.select('img').length - 1];
                                            if (imgTag) {
                                                imgTag.src = newDescPath;  // 替换为新的图片路径
                                            }
                                        } else {
                                            alert('图片上传失败：' + data.msg);
                                        }
                                    });
                                });
                            }
                        });
                    }
                });
            },
            // 自定义右键菜单：移除链接相关选项
            contextmenu: 'cut copy paste | bold italic | image | remove'
        });
    </script>
  <style>
    #attributeWrapper {
        width: 100%;
        /*height: 100%;*/
        box-sizing: border-box;
    }
    .attribute-content {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding: 10px;
        display: flex;
        flex-direction: column;
    }
    /*进度条*/
        #mask {
            position: fixed; /*设置为固定定位*/
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255,0.8); /*使用半透明的白色作为背景色*/
            z-index: 9999; /*设置遮罩层的层级*/
            display: none;
            align-items: center;
            justify-content: center;
            color: dimgrey;
            font-size: 18px;
        }

        #progress {
            width: 220px;
            height: 70px;
            transform: translate(calc(50vw - 50%), calc(50vh - 50%));
        }

        #progress_border {
            width: 200px;
            height: 30px;
            background-color: #fff;
            border: 4px solid #000;
            border-radius: 12px; 
            display: inline-block;
        }

        #progress_content {
            width: 0;
            height: 88%;
            margin: 2px 2px 2px 2px;
            background-color: FireBrick;
            border-radius: 12px;
        }

        #progress_text {
            /* width: 200px;
            height: 30px;
            display: inline-block;
            color: FireBrick; */
            width: 200px;
            height: 30px;
            display: flex; /* 使用 flexbox 布局 */
            align-items: center; /* 垂直居中 */
            justify-content: center; /* 左对齐 */
            color: #000; /* 文字颜色为白色 */
            font-size: 16px; /* 增加字体大小 */
            position: absolute; /* 使文本绝对定位 */
            top: 0; /* 顶部对齐 */
            left: 0; /* 左侧对齐 */
            padding-left: 5px; /* 左侧内边距，稍微调整位置 */
        }
    
    /*操作区*/
    .attr-opt-wrapper {
       width: 100%;
       height: 35px;
       display: flex;
       justify-content: space-between;
       align-items: center;
    }
    .attr-opt-add {
        padding-left: 5px;
        font-size: 16px;
    }
    .attr-opt-add > button {
        padding: 0;
    }
    .attr-opt-add:hover {
        cursor: pointer;
    }
    .attr-opt-stock-change {
        display: flex;
        gap: 5px;
        align-items: center;
    }
    
    
    
    .attr-info {
        width: 100%;
        display: flex;
        flex-direction: column;
    }
    .info-item {
        display: flex;
        box-sizing: border-box;
        align-items: center;
        gap: 5px;
        padding: 5px;
    }
    .info-item + .info-item  {
        border-top: 1px solid #ddd;
    }
    .info-attr-edit {
        display: flex;
        box-sizing: border-box;
        margin-bottom: 8px;
        align-items: center;
        gap: 10px;
        justify-content: space-between;
    }
    .info-attr-edit-item {
        width: 80px;
    }
    .info-attr-edit-item > button {
         width: 82px;
    }
    .info-item-attr-name {
        font-size: 16px;
        padding: 0 5px;
        display: flex;
        gap: 10px;
        align-items: center;
    }
    .info-item-options {
        display: flex;
        flex: 1;
        gap: 10px;
        flex-wrap: wrap;
        padding: 4px 0;
    }
    .info-item-options::-webkit-scrollbar {/*滚动条整体样式*/
            width: 10px;     /*高宽分别对应横竖滚动条的尺寸*/
            height: 4px;
        }
    .info-item-options::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
            border-radius: 4px;
             -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            background: #ddd;
        }
    .info-item-options::-webkit-scrollbar-track {/*滚动条里面轨道*/
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 10px;
            background: #f8f8f8;
        }
    .info-item-edit {
        
        display: flex;
        justify-content: flex-end;
    }

    .info-item-edit > button, .info-item-options > buttonx {
        width: 26px;
        height: 26px;
        padding: 0;
    }
    .info-item-options-btn {
        width: 65px;
        display: flex;
        /*justify-content: space-between;*/
        padding: 2px 0;
        align-items: center;
        gap: 4px;
    }
    .info-item-options-btn button {
         width: 30px;
         height: 25px;
         padding: 0;
         margin: 0 !important;
     }
    .opt-item-select-wrapper {
        width: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        gap: 5px;
        height: 100%;
        
    }
    .opt-item-select-add {
        width: 100%;
         box-sizing: border-box;
         display: flex;
         gap: 5px;
         height: 40px;
    }
    .opt-item-select-add .opt-input {
        border-bottom: 1px solid #bbb;
     }
     .info-item-options-popover {
        box-sizing: border-box; 
        height: 95%;
        width: 95%;
     }
    .opt-filter-content {
        width: 100%;
        display: flex;
        margin-top: 10px;
        max-height: 125px;
    }
    .opt-create-item {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .opt-create-item button {
        padding: 0;
    }
     .opt-item-select-content, .opt-filter-content .opt-item-select-content {
        width: 100%;
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        margin: 10px 0;
        flex: 1;
        height: 0;
        overflow-y: auto;
        overflow-x: hidden;
        align-content: baseline;
     }
     .opt-filter-content .opt-item-select-content {
       height: 125px;
     }
     .opt-item-select-item {
        box-sizing: border-box;
     	height: 40px;
     	width: 165px;
     	min-width: 165px;
     	margin: 0 !important;
     }
     .opt-btn-wrapper {
        width:  100%;
        display: flex;
        justify-content: space-between;

     }
     
    .attr-item-list {
        width: 100%;
        flex: 1;
        display: flex;
    }
    .attr-item-list .el-table {
        width: 100%; 
        height: 100%
    }
    .attr-item-list .el-table .cell {
        line-height: 29px;
     }                      
    .attr-item-list .el-table .cell .el-input__inner {
        width: 100%;
        height: 30px;
        padding: 0;
     }
     
     .attr-item-list .el-table .cell input[type=number]::-webkit-outer-spin-button,
     .attr-item-list .el-table .cell input[type=number]::-webkit-inner-spin-button {
       -webkit-appearance: none;
       margin: 0;
     }
     .attr-item-list .el-table .cell input[type=number] {
       -moz-appearance: textfield; /* Firefox */
     }

  </style>
</head>
<body style="display:flex;flex-direction:column;align-items:center;background:#f2f2f2;">
@*
        此处使用了jqxTabs组件
        会以li列表的元素为配置源，同时必须在此处id="divTab"这个盒子内部，在配置与li元素个数相同的div
        这样再由 $('#divTab').jqxTabs({ width: getWidth('divTab'), height: getHeight('divTab'), position: 'top'});这个函数去渲染
 *@
<div id="divTab" style="width:calc(100% - 0px);flex-grow:1;visibility:hidden;">
    <ul>
        <li>基本</li>
        <li>属性</li>
        <li>图片</li>
        <li>价格方案</li>
        <li>视频</li>
        <li>商品详情</li>
    </ul>
    
    <div style="display:flex;flex-direction:column;">


        <div id="divHead" class="headtail" style="width:800px;padding-top:10px;">
            <!--<div style="display:none;"><div><label>编号</label></div> <div><div id="item_id"></div></div></div>
            <div><div><label>名称</label></div> <div><div id="item_name"></div></div></div>
            <div><div><label>类别</label></div> <div><div id="item_class"></div></div></div>
            <div><div><label>品牌</label></div> <div><div id="item_brand"></div></div></div>
            <div><div><label>序号</label></div> <div><div id="item_order_index"></div></div></div>
            <div><div><label>状态</label></div> <div><div id="status"></div></div></div>
            <div style="float:none;height:0px; clear:both;"></div>-->
        </div>
        <!--<div style="margin-top:10px; margin-left:40px;margin-bottom:10px; width:400px;">单位</div>-->
            <div id="gridUnit" style="width:calc(100% - 20px);margin-top:10px;margin-left:10px;margin-right:0px;height:50px;overflow-x:auto;">
            </div>
    </div>
    <div>
        <div id="attributeWrapper">
            <div class="attribute-content" v-if="son_mum_item === ''">
                <div class="attr-opt-wrapper">
                    <div class="attr-opt-add">
                        <el-dropdown style="font-size: 16px" trigger="click" @@command="handleMumAttributeItemChange($event, 'add')">
                            <span class="el-dropdown-link">
                                添加属性<i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <template v-for="attrItem in companyAvailAttributes">
                                    <el-dropdown-item :command="attrItem" :key="'dropdownAttrItem_' + attrItem.attr_id">{{attrItem.attr_name}}</el-dropdown-item>
                                </template>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>

                    <div class="attr-opt-stock-change">
                        <div>区分库存</div>
                        <div>
                            <el-switch
                                :disabled="itemUsed || !distinctStockEditable"
                                v-model="distinctStock"
                                active-color="#13ce66"
                                inactive-color="#ddd">
                            </el-switch>
                        </div>
                    </div>
                    @* <div class="attr-opt-stock-change" v-if="distinctStock">
                        <div>记忆口味商品价格</div>
                        <div>
                            <el-switch v-model="remberSonItemPrice"
                                    active-color="#13ce66"
                                    inactive-color="#ddd">
                            </el-switch>
                        </div>
                    </div> *@
                </div>
                <div class="attr-info">
                    <template v-for="mumItem in itemMumAttributes">
                        <div class="info-item" :key="'itemMumAttributes_' + mumItem.attrID">
                            <div class="info-item-attr-name">
                                <div>{{mumItem.attrName}}</div>
                            </div>
                            <div class="info-item-options">
                                <div class="info-item-options-btn" :id="'add_popver_' + mumItem.attrID">
                                    <el-popover
                                        :value="popverValue['popver_' + mumItem.attrID]"
                                        placement="right"
                                        popper-class="info-item-options-popover"
                                        trigger="manual">
                                        <div class="opt-item-select-wrapper" :id="'popver_' + mumItem.attrID">
                                            <div class="opt-item-select-add">
                                                <div class="opt-input">
                                                    <el-input v-model="optAttrNameInput" placeholder="搜索、添加属性" @@input="handleFilterAttr($event, mumItem)"></el-input>
                                                </div>
                                                <div v-if="optAttrNameInput !== ''" class="opt-create-item">
                                                    <el-button size="mini" type="info" plain @@click="handleCreateOptItem(mumItem)">创建</el-button>
                                                </div>
                                            </div>
                                            <div class="opt-filter-content" v-show="optAttrNameInput !== ''  && filterAttrItem.length > 0">
                                                @* <div v-if="filterAttrItem.length === 0" class="opt-create-item"> *@
                                                @*     <div>是否创建属性【{{optAttrNameInput}}】</div> *@
                                                @*     <div><el-button size="mini" type="info" plain @@click="handleCreateOptItem(mumItem)">创建</el-button></div> *@
                                                @* </div> *@
                                                <div class="opt-item-select-content">
                                                    <template v-for="attrItem in filterAttrItem">
                                                        <el-checkbox class="opt-item-select-item" border  @@change="handleAttrItemChange($event, attrItem)" v-model="attrItem.isSelect" :key="'filter' + attrItem.opt_id">{{attrItem.opt_name}}</el-checkbox>
                                                    </template>
                                                </div>
                                            </div>
                                            <div class="opt-item-select-content">
                                                <template v-for="attrItem in mumItem.attrOption">
                                                    <el-checkbox class="opt-item-select-item" border  @@change="handleAttrItemChange($event, attrItem)" v-model="attrItem.isSelect" :key="'attrOption' + attrItem.opt_id">{{attrItem.opt_name}}</el-checkbox>
                                                </template>
                                            </div>
                                            <div class="opt-btn-wrapper">
                                                <div>
                                                    <el-button type="text" @@click="handleSelectAllOptItem(mumItem, true)">全选</el-button>
                                                    <el-button type="text" @@click="handleSelectAllOptItem(mumItem, false)">全不选</el-button>
                                                </div>
                                                <div>
                                                    <el-button style="padding: 0" type="info" plain @@click="handleConfirmAddNewOptItems(mumItem)">确认</el-button>
                                                </div>
                                            </div>
                                        </div>
                                        <el-button slot="reference" size="mini" type="info" icon="el-icon-plus" plain @@click.stop="handleAddNewOptItem(mumItem)"></el-button>
                                    </el-popover>
                                    <el-button size="mini" type="danger" icon="el-icon-delete" plain @@click="handleMumAttributeItemChange(mumItem, 'del')"></el-button>
                                </div>
                                <template v-for="optItem in mumItem.options">
                                    <el-tag @@close="handleDeleteOptItem(mumItem, optItem)" size="medium" :key="'tag' + optItem.optID" closable :disable-transitions="false">
                                        {{ optItem.optName }}
                                    </el-tag>
                                </template>
                            </div>
                            @* <div class="info-item-edit" v-if="!itemUsed" > *@
                            @*     <el-button size="mini" type="danger" icon="el-icon-delete" plain></el-button> *@
                            @* </div> *@
                        </div>
                    </template>
                </div>
                <div class="attr-item-list">
                    <el-table :data="availAttrCombine">
                        <el-table-column label="状态" width="80">
                            <template slot-scope="scope">
                                <el-button style="color: #67c23a" type="text" size="small" v-if="scope.row.status === 1" @@click="handleCombineStatus(scope.row, 0)">正常</el-button>
                                <el-button style="color: #f56c6c" type="text" size="small" v-else @@click="handleCombineStatus(scope.row, 1)">停用</el-button>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="optName"
                            label="名称"
                            width="150">
                        </el-table-column>
                        <el-table-column label="条码">
                            <el-table-column
                                prop="sBarcode"
                                label="小"
                                width="120">
                                <template #default="scope">
                                    <el-input v-model="scope.row.sBarcode" />
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="mBarcode"
                                label="中"
                                width="120">
                                <template #default="scope">
                                    <el-input v-model="scope.row.mBarcode"  />
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="bBarcode"
                                label="大"
                                width="120">
                                <template #default="scope">
                                    <el-input v-model="scope.row.bBarcode"  />
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="批发价">
                          <el-table-column
                                prop="sPrice"
                                label="小">
                                <template #default="scope">
                                    <el-input v-model="scope.row.sPrice" type="number" step="0.0001"  />
                                </template>
                            </el-table-column>
                          <el-table-column
                                prop="mPrice"
                                label="中">
                                <template #default="scope">
                                    <el-input v-model="scope.row.mPrice" type="number" step="0.0001"  />
                                </template>
                            </el-table-column>
                          <el-table-column
                                prop="bPrice"
                                label="大">
                                <template #default="scope">
                                    <el-input v-model="scope.row.bPrice" type="number" step="0.0001"/>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <div v-else>
                子商品不允许创建属性
            </div>
        </div>
    </div>
    @* <div> *@
    @*     <div id="divAttributesTop" style="display:flex;height:30px;align-items:center;"> *@
    @* *@
    @*     </div> *@
    @*     <div id="gridAttr" style="width:calc(100% - 20px);margin-top:10px;margin-left:10px;margin-right:0px;height:50px;"> </div> *@
    @* </div> *@
    <div>
        @*商品图片*@
        <form class="input-block" id="formMainImage">
            <input id="upload-image" type="file" name="file" accept="image/png,image/bmp,image/jpeg" style=" display:none"/>
        </form>
        <form class="input-block-2" id="formImageChange">
            <input id="edit-image" type="file" name="file" accept="image/png,image/bmp,image/jpeg" style=" display:none" data-target=''/>
        </form>
        <div id="images_mixed" style="display:flex; flex-flow: wrap; width:calc(100%- 20px); margin-top:20px; margin-left:10px; margin-right:0px; height:auto;">

        </div>
        <div id="bigImageBox"></div>

    </div>
    <div>
        <div id="pricePlan">
            <template>
                <div v-if="!pricePlanRight.see" style="color:red;">你没有价格方案的查看权限</div>
                <div v-else-if="!pricePlanRight.edit" style="color:darkorange;">提示：你没有价格方案的编辑权限，只能查看</div>
                <el-table
                    v-if="pricePlanRight.see"
                    :data="tableData"
                    style="width: 100%;"
                    :highlight-current-row="false">
                    <el-table-column
                        label="方案名称"
                        width="270">
                        <template slot-scope="scope">
                            <el-select :disabled="!pricePlanRight.edit" v-model="scope.row.selectPlanId" filterable clearable placeholder="" @@change="onPlanNameColumnChange($event,scope.row)">
                                <el-option
                                    v-for="(item,index) in pricePlanOptions"
                                    :key="item.plan_id"
                                    :label="item.plan_name"
                                    :value="Number(item.plan_id)">
                                </el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="方案价格">
                        <el-table-column
                            label="大"
                            width="130">
                            <template slot-scope="scope">
                                <el-input
                                    v-model="scope.row.b_price"
                                    v-if=" itemInfo.b_unit_factor!=''"
                                    :disabled="!pricePlanRight.edit"
                                    size="mini"

                                    @@change="onBPriceColumnChange(scope.row)">
                                </el-input>
                            </template>
                        </el-table-column>

                        <el-table-column
                            label="中"
                            width="130">
                            <template slot-scope="scope">
                                <el-input
                                    v-model="scope.row.m_price"
                                    v-if=" itemInfo.m_unit_factor!=''"
                                    :disabled="!pricePlanRight.edit"
                                    size="mini"
                                    @@change="onMPriceColumnChange(scope.row)">
                                </el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="小"
                            width="130">
                            <template slot-scope="scope">
                                <el-input
                                    v-model="scope.row.s_price"
                                    :disabled="!pricePlanRight.edit"
                                    size="mini"
                                    @@change="onSPriceColumnChange(scope.row)">
                                </el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="折扣(%)"
                            width="130">
                            <template slot-scope="scope">
                                <el-input class="discount"
                                          v-model="scope.row.discount"
                                          :disabled="!pricePlanRight.edit"
                                          size="mini"
                                          @@change="onDiscountColumnChange(scope.row)">
                                </el-input>
                                <span v-if="scope.row.discount != '' ">%</span>
                            </template>
                        </el-table-column>


                    </el-table-column>
                </el-table>
            </template>
        </div>
    </div>
    <div>
        <div id="video" style="padding: 20px;">
                <div id="help" style="float:right;line-height:30px;text-align:center;color:#FFFFFF; background-color:#585858;width:30px;height:30px;border-radius:20px;margin-right:35px;margin-top:5px;">
                    <svg height="15" width="15" style="margin:7px auto 0px auto; cursor:pointer" fill="#dddddd">
                        <use xlink:href="/images/images.svg#help" />
                    </svg>
                </div>
                @* <div id="parent" style="position: relative; width: 100%; height: 100%;"> *@
                <div id="div_help" style="position:relative;display:none;border-radius: 80px;">
                    <div style="width:100px;height:100px;display:flex;flex-direction:column;justify-content:space-between;right:0px;border-radius: 80px; background-color: #FFF0F5;">
                        <ul style="line-height: 25px;font-size:15px;padding:0px;margin-left:8px;">
                            <li style="list-style:none;text-align:center">
                                <a>
                                    上传提示：
                                </a>
                            </li>
                            <li><a>建议比例 3:4</a></li>
                            <li><a>大小10M内 </a></li>
                        </ul>

                    </div>
                    </div>
                @* </div> *@
                <div id="video_main_part" style="margin-bottom: 20px;">
                <div id="video_main_part_head">商品主视频</div>
                <form class="input-block" id="formMainVideo">
                        <input id="upload-video-main" type="file" name="file" accept="video/mp4,video/webm" style=" display:none" />
                </form>
                    <form class="input-block-2" id="formVideoMainChange">
                        <input id="edit-video-main" type="file" name="file" accept="video/mp4,video/webm" style=" display:none" data-target='' />
                </form>
                    <div id="video_main" style="display:flex; flex-wrap: wrap; width:calc(100%- 20px); margin-top:15px; margin-left:10px;  margin-right:0px; height:auto;">
                </div>
            </div>
            <div>


            </div>
                <div id="video_other_part" style="margin-bottom: 20px;">
                    <div id="video_other_part_head">其他视频</div>
                    <form class="input-block" id="formOtherVideo">
                        <input id="upload-video-other" type="file" name="file" accept="video/mp4,video/webm" style=" display:none" />
                    </form>
                    <form class="input-block-2" id="formVideoOtherChange">
                        <input id="edit-video-other" type="file" name="file" accept="video/mp4,video/webm" style=" display:none" data-target='' />
                    </form>
                    <div id="video_other" style="display:flex; flex-flow: wrap; width:calc(100%- 20px); margin-top:15px; margin-left:10px; margin-right:0px; height:auto;">
                    </div>
                </div>
        </div>

    </div>
        <div>
            <div id="richContext" style="padding: 20px;">
                <!-- 富文本编辑器区域 -->
                <textarea id="tinymce-editor" style="height: 400px;"></textarea>
            </div>
        </div>

</div>
<div style="text-align:center;padding-top:10px;padding-bottom:10px; flex-grow:0;">
    <button id="btnSave" onclick="btnSave_Clicked();" class="main-button" style="margin-right:50px;">保存</button> 
    <button id="btnSaveAndCopy" onclick="btnSaveAndCopy_Clicked();" style="margin-right:50px; width:110px;">保存并新增</button>
    @* <button id="btnPrint" onclick="btnPrint_Clicked();" style="margin-right:50px;">打印</button> *@

    <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
</div>
<script>
    var g_operKey = `@Model.OperKey`
    function btnSaveAndCopy_Clicked() {
        btnSave_Clicked(true, () => {+
            $('#item_id').val('')
            $('#itemUsed').val('false')
            attributeVm.handleCopyCreateNewItem()
            console.log('attributeVm', attributeVm)
            window.m_bNewRecord=true
        })

        
    }
    function btnPrint_Clicked() {
        var itemId = $('#item_id').val()
        if (!itemId) {
            bw.toast('请先保存'); return
        }

        var container = window.parent.CEFPrinter
        if (!container)
            container = window.parent.CefGlue
        if (!container)
            container = window.parent
        if (!container.printTagByTemplate) {
            bw.toast('需要到客户端打印'); return
        }

        $.ajax({
            url: '/api/TagPrint/LoadTagPrintData',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                items: itemId
            },
            success: res => {
                console.log('ajax LoadTagPrintData res:', res)
                if (res.result == 'OK') {
                    var items = res.items_db
                    var templates = res.templates
                    var default_template = res.default_template
                    //var shared_template = res.shared_template

                    var use_template = default_template.length > 0 ? default_template
                        : templates.length > 0 ? templates
                        : [] //shared_template
                    if (use_template.length == 0) {
                        bw.toast('您还没有设置商品标签的打印模板'); return
                    }
                    use_template = use_template[0].template_content

                    container.printTagByTemplate(items, use_template)
                } else {
                    bw.toast(res.msg)
                }
            },
            error: error => {
                bw.toast('网络错误:' + error.responseText)
                console.error('ajax LoadTagPrintData error:', error)
            }
        })
    }
        function uploadVideo(uploadVideoFlag, uploadVideosrc, othCount, videoid, callback) {
            debugger
            var formFlds = getFormData();
            if (formFlds.errMsg) {
                return;
            }
            try {
                if (typeof (eval(checkDataValid)) == 'function') {
                    var bOK = checkDataValid(formFlds);
                    if (!bOK) return;
                }
            } catch (e) { }
            // 将 uploadVideoFlag 和 uploadVideosrc 添加到 formFlds 对象中
            formFlds.uploadVideoFlag = uploadVideoFlag;
            formFlds.uploadVideosrc = uploadVideosrc;
            formFlds.othCount = othCount;
            formFlds.uploadDesc = false;
            //进度条
            $('#mask').css('display', 'block');
            //每行230毫秒
            let progressWidth = 0;
            const widthEachStep = 0.02; // 进度条每次增加 2%
            var timeEachRow = setInterval(function () {
                progressWidth += widthEachStep;
                //console.log(progressWidth);
                if (progressWidth < 0.98) {
                    $('#progress_content').width(`${Math.round(progressWidth * 100)}%`);
                    $('#progress_text').text(`处理中...${Math.round(progressWidth / 0.98 * 100)}%`);
                } else {
                    $('#progress_content').width('98%');
                    $('#progress_text').text(`处理中...100%`);
                    clearInterval(timeEachRow);
                }
            }, 230);
            $.ajax({
                url: '../api/ItemEdit/uploadVideo',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formFlds),
                success: function (data) {
                    if (data.result == 'OK') {
                        var paths = JSON.parse(data.newPath); // 假设返回的是 JSON 格式的字符串
                        if(uploadVideoFlag==0){
                            var newVideoPath = paths.tinyVideoPath;
                        }
                        if (uploadVideoFlag == 1 ) {
                            var newVideoPath = paths.otherVideoPath;
                            othVideoOtherNum++;
                        }
                        if (uploadVideoFlag == 2) {
                            pictNumOtherMaximum++;
                        }
                        // 获取你需要设置 src 的视频元素
                        var videoElement = document.getElementById(videoid);
                        // 将新视频路径赋给视频元素的 src 属性
                        if (videoElement) {
                            
                            videoElement.src = "https://yingjiang.obs.cn-east-3.myhuaweicloud.com/" + newVideoPath;
                            console.log('videoElement.src:', videoElement.src)
                        }
                        //进度条逻辑
                        clearInterval(timeEachRow);
                        $('#progress_content').width('98%');
                        $('#progress_text').text(`处理中...100%`);
                        setTimeout(() => {
                            $('#mask').css('display', 'none');
                            $('#progress_content').width('0');
                            $('#progress_text').text(`处理中...0%`);

                            bw.toast(`处理成功`, 3000)
                            console.log(`处理成功`);
                        }, 500);
                    }
                    else {
                        $('#mask').css('display', 'none');
                        $('#progress_content').width('0');
                        $('#progress_text').text(`处理中...0%`);
                        clearInterval(timeEachRow);

                        bw.toast("处理失败"+data.msg, 5000);
                    }
                    if (callback) callback(data)
                },
                error: function (response, ajaxOptions, thrownError) {
                    bw.toast('error' + response);
                }
            });
        }
        function uploadDesc(uploadDescFlag, uploadDescsrc, othCount, descid, callback) {
            debugger
            var formFlds = getFormData();
            if (formFlds.errMsg) {
                return;
            }
            try {
                if (typeof (eval(checkDataValid)) == 'function') {
                    var bOK = checkDataValid(formFlds);
                    if (!bOK) return;
                }
            } catch (e) { }
            // 将 uploadDescFlag 和 uploadDescsrc 添加到 formFlds 对象中
            formFlds.uploadDescFlag = uploadDescFlag;
            formFlds.uploadDescsrc = uploadDescsrc;
            formFlds.othCount = othCount;
            formFlds.uploadDesc = true;
            $.ajax({
                url: '../api/ItemEdit/uploadVideo',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formFlds),
                success: function (data) {
                    if (data.result == 'OK') {
                    }
                    else {
                        bw.toast("处理失败"+data.msg, 5000);
                    }
                    if (callback) callback(data)
                },
                error: function (response, ajaxOptions, thrownError) {
                    bw.toast('error' + response);
                }
            });
        }
</script>
    <div id="mask">
        <div id="progress">
            <div id="progress_border">
                <div id="progress_content"></div>
            </div>
            <div id="progress_text">处理中...</div>
        </div>
    </div>
</body>
</html>