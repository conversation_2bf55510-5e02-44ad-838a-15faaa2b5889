@page
@model ArtisanManage.Pages.BaseInfo.SalesSummaryByItemModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';

             var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)

                @Html.Raw(Model.m_createGridScript)
                $("#popoverQuery").jqxPopover({ showArrow: false, autoClose: true, offset: { left: 0, top: -10 }, position: "bottom", title: "", showCloseButton: false, selector: $("#btnQueryChooseSheets") });
                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                     
                    var item_id = args.row.bounddata.item_id;
                    var item_name = args.row.bounddata.item_name;
                    var remarks = "";
                    var remarks_name = "";
                    var vv=$('#remarks').jqxInput('val')
                     if(vv){
                        if(vv.length > 1){
                            $('#remarks').val().forEach((remark)=>{
                                if(remarks!=="")
                                    remarks += ",";
                                if(remarks_name!=="")
                                    remarks_name += ","
                                remarks += remark.value;
                                remarks_name += remark.label;
                            })
                        }
                        else{
                            remarks = vv.value
                            remarks_name = vv.label
                        }
                     }
                   /* var startDay = $('#startDay').jqxDateTimeInput('val');
                    var endDay = $('#endDay').jqxDateTimeInput('val');
                    var supcust_id = $('#supcust_id').val().value;
                    var sup_name = $('#supcust_id').val().label;
                    var multi_supcust_flag = Array.isArray($('#supcust_id').val())
                    var seller_id = $('#seller_id').val().value;
                    var seller_name = $('#seller_id').val().label;
                  //  var saleWay = $('#saleWay').val().value;
                  //  var sale_ways = $('#saleWay').val().label;
                    var remark = $('#remark').val();
                    var brand_id = $('#brand_id').val().value;
                    var brand_name = $('#brand_id').val().label;
                    var branch_id = $('#branch_id').val().value;
                    var branch_name = $('#branch_id').val().label;
                    var arrears_status = $('#arrears_status').val().value;

                

                    console.log($('#saleWay'))
                    var sale_way = $('#saleWay').val().value;
                    var sale_way_label = $('#saleWay').val().label;*/

                    var sheetType = $('#sheetType').val();

                    //var url = `Report/SalesDetail?sheetType=${sheetType}&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}`;

                    var title = '销售明细表';
                    if (sheetType=="xd") title = '订单明细表'
                    if (args.datafield == "item_name" && item_id) {

                        /*if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}`;
                        if (sup_name) url += `&supcust_id=${supcust_id}&sup_name=${sup_name}`;
                         var department_id = $('#department_id').val().value;
                        var department_name = $('#department_id').val().label;
                        var department_path = $('#department_id').jqxDropDownTree('treePath')
                        if (department_name) url += `&department_id=${department_id}&department_name=${department_name}&department_path=${department_path}`;
                        
                        if (multi_supcust_flag) {
                            let multi_supcust_id = ''
                            let multi_sup_name = ''
                            $('#supcust_id').val().forEach(item => {
                                if (!multi_supcust_id || !multi_sup_name) {
                                    multi_supcust_id = item.value
                                    multi_sup_name = item.label
                                } else { 
                                    multi_supcust_id = multi_supcust_id + ',' + item.value
                                    multi_sup_name = multi_sup_name + ',' + item.label
                                }
                                if(multi_supcust_id) url += `&supcust_id=${multi_supcust_id}&sup_name=${multi_sup_name}`;

                            })
                        }
                        if (seller_name) url += `&seller_id=${seller_id}&seller_name=${seller_name}`;
                      //  if (saleWay) url += `&saleWay=${saleWay}&sale_ways=${sale_ways}`;
                        if (remark) url += `&remark=${remark}`;
                        if (arrears_status) url += `&arrears_status=${arrears_status}`;
                        if (brand_name) url += `&brand_id=${brand_id}&brand_name=${brand_name}`;
                        //if (sale_way_label) url += `&sale_way=${sale_way}&sale_way_label=${sale_way_label}`;
                         

                        window.parent.newTabPage(title, `${url}`);*/
                    window.queryItems = funGetQueryValues();
                    // 特殊处理一些字段
                    window.queryItems['remark'] = remarks;
                    window.queryItems['remark_name'] = remarks_name;
                    const className = $('#other_class').val().label;
                    const classPath = $('#other_class').jqxDropDownTree('treePath')
                    if (className) {
                        window.queryItems['class_name'] = className;
                        window.queryItems['class_path'] = classPath;
                    }

                    // 添加商品信息到查询参数对象
                    window.queryItems['item_id'] = item_id;
                    window.queryItems['item_name'] = item_name;
                    var queryItemStr = queryItemsToString(window.queryItems)
                    //queryItemsToString += `&item_id=${item_id}&item_name=${item_name}`;
                    window.parent.newTabPage(title, 'Report/SalesDetail' + queryItemStr, window);
                    }
                    //if (args.datafield == "disc_amount" && item_id) {
                    //    if (sup_name) url += `&supcust_id=${supcust_id}&sup_name=${sup_name}`;
                    //    if (seller_name) url += `&seller_id=${seller_id}&oper_name=${seller_name}`;
                    //    if (saleWay) url += `&saleWay=${saleWay}&sale_ways=${sale_ways}`;
                    //    if (remark) url += `&remark=${remark}`;
                    //    if (brand_name) url += `&brand_id=${brand_id}&brand_name=${brand_name}`;
                    //    url += `&disc_status=yes` ;
                    //    debugger
                    //    window.parent.newTabPage(title, `${url}`);
                    //}



                });
                QueryData();
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
                $('#supcust_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popClient').jqxWindow('open');
                        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&multiSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                $('#item_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popItem').jqxWindow('open');
                        $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });



                QueryData();


            });
        function btnQuerySaleSummaryBySeller_click() {
            debugger
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（业务员）", 'Report/SalesSummaryBySeller' + queryItemStr, window);
            var len = $('#jqxTabs').jqxTabs('length');
            var content = $('#jqxTabs').jqxTabs('getContentAt', len - 1);
            var frame = content.childNodes[0];
            var w = frame.contentWindow;
            w.g_bRelatedReport_sale = true
            window.g_bRelatedReport_sale = true

        }
        function btnQuerySaleSummaryByClient_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（客户）", 'Report/SalesSummaryByClient' + queryItemStr, window);
        }
        function btnQuerySaleSummaryBySender_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（送货员）", 'Report/SalesSummaryBySender' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByRegion_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（片区）", 'Report/SalesSummaryByRegion' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByGroup_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（渠道）", 'Report/SalesSummaryByGroup' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByBrand_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（品牌）", 'Report/SalesSummaryByBrand' + queryItemStr, window);
        }
        function btnQuerySaleDetail_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售明细表", 'Report/SalesDetail' + queryItemStr, window);
        }
        function queryItemsToString(queryItems) {
            debugger
            var query = "?"
            for (var i in queryItems) {
                if (i == 'operKey') {
                    continue;
                }
                query += "&"
                query += i
                query += '='
                query += queryItems[i]
            }

            return query;
        }
            /*
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                    $.ajax({
                        url: '/api/SaleSheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: item_id },
                        success: function (data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });

                }
                $('#popClient').jqxWindow('close');
            }
            else if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "selectMulti") {
                    if (rs.data.checkedRows.length == 1) {
                        var item_id = rs.data.checkedRows[0].item_id;
                        var item_name = rs.data.checkedRows[0].item_name;
                    }

                    var rows = rs.data.checkedRows
                    var items_id = ''
                    rows.forEach(function (row) {
                        if (items_id != '') items_id += ','
                        items_id += row.item_id
                    })
                    $('#item_id').jqxInput('val', { value: item_id, label: item_name  });

                    $.ajax({
                        url: '/api/SaleSheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: items_id },
                        success: function (data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });
                }

                $('#popItem').jqxWindow('close');
            }

        });

        */
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                }
                else if (rs.data.action === "selectMulti") {
                    var rows = rs.data.checkedRows
                    var supcusts_id = '',supcusts_name = ''
                    rows.forEach(function (row) {
                        if (supcusts_id != '') supcusts_id += ','
                        supcusts_id += row.supcust_id
                        if (supcusts_name != '') supcusts_name += ','
                        supcusts_name += row.sup_name
                    })
                     $('#supcust_id').jqxInput('val', { value: supcusts_id, label: supcusts_name });

                }
                $('#popClient').jqxWindow('close');
            }
            else if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "selectMulti") {
                    if (rs.data.checkedRows.length == 1) {
                        var item_id = rs.data.checkedRows[0].item_id;
                        var item_name = rs.data.checkedRows[0].item_name;
                    }

                    var rows = rs.data.checkedRows
                    var items_id = '',items_name = ''
                    rows.forEach(function (row) {
                        if (items_id != '') items_id += ','
                        items_id += row.item_id
                        if (items_name != '') items_name += ','
                        items_name += row.item_name
                    })
                    $('#item_id').jqxInput('val', { value: items_id, label: items_name });
                    //$('#item_id').jqxInput('val', { value: item_id, label: item_name });

                    $.ajax({
                        url: '/api/SaleSheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: items_id },
                        success: function (data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });
                }

                $('#popItem').jqxWindow('close');
            }

        });
    </script>
</head>

<body style="overflow:hidden">
  

    <div style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">

            <div style="float:none;height:0px; clear:both;"></div>

        </div>

        @*<button onclick="QueryData()" style="margin-right:20px;margin-top:30px;">查询</button>*@
        <button onclick="QueryData()" style="margin-top: 30px;margin-right:0px; border-right: none; border-radius: 3px 0px 0px 3px">查询</button>

        <button id="btnQueryChooseSheets" class="btnright" style="width:30px;margin-right:20px;margin-left:0px;margin-top:30px;border-radius: 0px 3px 3px 0px">
            <img src="~/PrintTemplate/img/triangle.svg" style="margin-top: -1px; width: 14px; display: inline-block;vertical-align: middle;" />
        </button>
        <style>
            .jqx-popover {
                border-color: #e2e2e2;
                border-radius: 20px;
                box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
            }
        </style>
        <div id="popoverQuery" style="display:none;position:absolute;border-radius:60px;border:none;">
            <div style="width: 150px; height: 200px; display: flex; flex-direction: column; justify-content: space-between; align-items: center;">
                <ul style="line-height: 26px;font-size:15px;padding:0px;">
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a id="btnshow" value="Show" onclick="btnQuerySaleSummaryBySeller_click();">
                            销售汇总（业务员）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByClient_click();">
                            销售汇总（客户）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryBySender_click();">
                            销售汇总（送货员）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByRegion_click();">
                            销售汇总（片区）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByGroup_click();">
                            销售汇总（渠道）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByBrand_click();">
                            销售汇总（品牌）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleDetail_click();">
                            销售明细表
                        </a>
                    </li>
                </ul>
                <div id="divClientVersion"></div>
            </div>
            <div style="overflow:hidden;"> </div>
        </div>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;margin-top:30px;">导出</button>

    </div>
    
    <div id="gridItems"></div>  
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div> 
        

    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popItem" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择商品</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
</body>
</html>