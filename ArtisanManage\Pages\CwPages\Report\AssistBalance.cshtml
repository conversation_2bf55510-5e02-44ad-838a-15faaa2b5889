﻿@page
@model ArtisanManage.Pages.CwPages.Report.AssistBalanceModel
@{
}

<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>AssistBalance</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css?v=@Html.Raw(Model.Version)" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.js?v=@Html.Raw(Model.Version)"></script>

    <script src="~/js/FileSaverVue.js"></script>
    <script src="~/js/Blob.js"></script>
    <script src="~/js/jszip.js"></script>
    <script src="~/js/xlsx.full.min.js"></script>
    <script src="~/js/xlsx-style.js"></script>
    <script src="~/js/Export2Excel.js?v=@Html.Raw(Model.Version)"></script>

    <style>
        * {
            font-family: "微软雅黑"
        }
        [v-cloak] {
            display: none;
        }

        body {
        }

        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;

        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #dddddd;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }

        #pages {
            width: 100%;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            /*background-color: #f9f9f9;*/
        }

        .pages_title {
            width: 100%;
            font-weight: 500;
            font-size: 25px;
            text-align: center;
            margin-top: 5px;
            padding-bottom:10px;
        }

        .pages_query {
            display: block;
            margin-left: 20px;
            margin-right: 20px;
            flex-wrap: wrap;
            padding-bottom: 10px;
        }

        .query_item {
            display: inline-block;
            width:80%;
            float:left;
            margin-top: 3px;
        }

        .item_input {
            width: 149px;
            height: 100%;
            z-index: 0;
            position: relative;
            border-style: none none solid;
            border-bottom: 1px solid #c7c7c7;
            border-radius: 0px;
            margin-left: 10px;
        }

        .item_name {
            height: 20px;
            bottom: 1px;
            right: 1px;
            margin-bottom: 0px;
        }

        .pages_buttons {
            width:10%;
            height: 100%;
            float:right;
            display:flex;
            justify-content:end;
            align-items:center;
        }

        .pages_content {
            height: 100%;
            padding: 0 20px;
            margin-bottom:10px;
        }

        .level2 {
            text-indent: 1em;
        }
        .level3 {
            text-indent: 2em;
        }


        .pages_content table {
            width: 100%;
            height:76vh;
            border-collapse: collapse;
            border: 2px solid #ebeef5;
        }

            .pages_content table tbody {
                display: block;
                overflow-y: scroll;
                overflow-x: hidden;
                height: 76vh;
            }
        @@media(max-height:700px) {
            .pages_content table tbody {
                display: block;
                overflow: auto;
                overflow-x: hidden;
                height: 460px;
            }
         }
        .pages_content table thead, .pages_content tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .pages_content table thead {
            width: calc( 100% - 1em - 2px );
        }

        .pages_content table thead th{
            background-color:#f0f0f5;
        }

       
        .pages_content table tbody td:not(:nth-child(1)){
            text-align: right;
        }
        @*高度*@
        .pages_content table thead th, .pages_content table tbody td {
            min-height: 40px;
            line-height: 40px;
        }
        @*边框*@
        .pages_content table thead th, .pages_content table tbody td {
            border-bottom: 2px solid #fff;
            border-right: 2px solid #fff;
        }
            .pages_content table thead th:last-child {
                border-right: 0;
            }
        @*背景*@
        .pages_content table tbody tr:nth-child(odd) {
            background: #fafafa;
        }
        .pages_content table tbody tr:hover {
            background-color: #f5f7fa;
        }

        .pages_content table tbody tr:last-child{
            position:sticky;
            bottom:0;
            background:#f3f3f3;
        }

        .pages_content table thead th, .pages_content table tbody td {
            padding: 0 15px;
        }

        .pages_content table thead th:nth-child(1),  .pages_content table tbody td:nth-child(1) {
            width: 17%;
            max-width:50%;
        }

        .pages_content table tbody tr:last-child {
            position: sticky;
            bottom: 0;
            background-color:#f3f3f3 ;
        }

        .el-date-editor, .el-input{
            width:200px;
            margin-right:20px;
        }

        .el-input__icon:hover{
            cursor:pointer;
            color:#000;
        }

    </style>

</head>
<body>
    <div id="root" v-cloak>
        <div id="pages" class="" ref="pages">
            <div class="pages_title">核算项目余额表</div>
            <div class="pages_query">
                <div class="query_item">
                    <label class="item_name"></label>
                    <el-date-picker
                          v-model="queryDate"
                          type="monthrange"
                          range-separator="至"
                          start-placeholder="开始月份"
                          end-placeholder="结束月份"
                          :disabled="disabledDatePicker"
                          :picker-options="pickerOptions"
                          @@change="changeDate">
                    </el-date-picker>
                    辅助类别：
                    <el-select v-model="typeSelected" placeholder="请选择辅助类别" @@change="typeChange" >
                        <el-option v-for="type in assistTypes"
                                   :key="type.id"
                                   :label="type.name"
                                   :value="type.id" >
                        </el-option>
                    </el-select>
                    科目：
                    <el-select v-model="subSelected" placeholder="请选择科目" @@change="subChange">
                        <el-option v-for="sub in typeSelectedInfo.subs"
                                   :key="sub.sub_code"
                                   :label="sub.sub_code_name"
                                   :value="sub.sub_code" >
                        </el-option>
                    </el-select>
                </div>
                <div class="pages_buttons">
                    <el-button type="info" plain :disabled="disabledExportBtn" v-on:click="exportBtn()" style="position:absolute;right:30px;">导出</el-button>
                </div>
            </div>
            <div class="pages_content">
                <table>
                    <thead>
                        <tr>
                            <th rowspan="2">辅助项</th>
                            <th colspan="2">期初余额</th>
                            <th colspan="2">本期发生</th>
                            <th colspan="2">本年累计</th>
                            <th colspan="2">期末余额</th>
                        </tr>
                        <tr>
                            <th>借方</th>
                            <th>贷方</th>
                            <th>借方</th>
                            <th>贷方</th>
                            <th>借方</th>
                            <th>贷方</th>
                            <th>借方</th>
                            <th>贷方</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="row in assistBalData" :key="row.assister_id">
                            <td>{{row.assister_name}}</td>
                            <td>{{row.op_bal_debit}}</td>
                            <td>{{row.op_bal_credit}}</td>
                            <td>{{row.this_bal_debit}}</td>
                            <td>{{row.this_bal_credit}}</td>
                            <td>{{row.year_bal_debit}}</td>
                            <td>{{row.year_bal_credit}}</td>
                            <td>{{row.ed_bal_debit}}</td>
                            <td>{{row.ed_bal_credit}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>   
    <script>
        var g_operKey = '@Model.OperKey';

        window.g_operRights =@Html.Raw(Model.JsonOperRightsOrig);
        function checkOperRight(vm){
            if (window.g_operRights.cwReport.assistBalance && window.g_operRights.cwReport.assistBalance.see) { 
                if(!window.g_operRights.cwReport.assistBalance.export) vm.disabledExportBtn=true;
                return true;
            }else{
                return false;
            }
        }
    </script>
    <script>

        var vm = new Vue({
            el: '#root',
            data() {
                return {
                    assistTypes:[
                        { id:'C', name:'客户', subs:[] }, //subs:[{ sub_code:'1122', sub_code_name:'1122 应收账款' }]
                        { id:'S', name:'供应商', subs:[] },
                        { id:'INV', name:'商品', subs:[] },
                        { id:'DEP', name:'部门', subs:[] },
                        { id:'MAN', name:'业务员', subs:[] }
                    ],
                    typeSelected:'C',
                    typeSelectedInfo:{},
                    subSelected: '',
                    assistBalData:[],
                    queryDate: [
                        new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)),
                        new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1))
                    ],
                    pickerOptions:{
                        disabledDate(time) {
                            return time.getTime() > Date.now();
                        }
                    },
                    companyName:'',
                    disabledDatePicker:false,
                    disabledExportBtn:false
                }
            },
            created(){
                if(!window.checkOperRight(this)){
                    this.disabledDatePicker=true;
                    this.disabledExportBtn=true;
                    return;
                }

                this.getTypeInfo();
            },
            methods: {
                getTypeInfo(){
                    $.ajax({
                        url: '/api/AssistBalance/GetTypeInfo',
                        type: 'get',
                        data: { operKey: g_operKey },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then((res) => {
                        if (res.result === 'OK') {
                            console.log('get assist setting');
                            if (res.msg != '') {
                                this.assistBalData = [];
                                this.$message({ message: res.msg });
                                return;
                            }
                            this.companyName = res.companyName;
                            if (this.dateFormat(res.maxPeriod, 'yyyy-MM-dd') > new Date()) {
                                this.queryDate = [this.dateFormat(new Date(), 'yyyy-MM-dd'), this.dateFormat(new Date(), 'yyyy-MM-dd')];
                            } else {
                                this.queryDate = [res.maxPeriod, res.maxPeriod];
                            }
                            this.pickerOptions = {
                                disabledDate(time) {
                                    return (time.getTime() < new Date(res.openAccountPeriod + ' 00:00:00') || time.getTime() > new Date(res.maxPeriod));
                                }
                            };
                            for (let i = 0; i < this.assistTypes.length; i++) {
                                let subs = res.cwAssistSetting.filter(row => row.type == this.assistTypes[i].id);
                                for (let j = 0; j < subs.length; j++) {
                                    this.assistTypes[i].subs.push({ sub_code: subs[j].sub_code, sub_code_name: subs[j].sub_code_name });
                                }
                                if (subs.length > 0 && this.subSelected == '') {
                                    this.typeSelected = this.assistTypes[i].id;
                                    this.subSelected = subs[0].sub_code;
                                    this.typeSelectedInfo = this.assistTypes.find(a => a.id == subs[0].type);
                                }
                            }
                            this.getData(this.dateFormat(new Date(this.queryDate[0]), 'yyyy-MM-dd'), this.dateFormat(new Date(this.queryDate[1]), 'yyyy-MM-dd'), this.typeSelected, this.subSelected);
                        } else{
                            this.$message({ type:'warning', message: res.msg });
                        }
                    });
                },
                getData(fromPeriod, toPeriod, type_id, sub_code){
                    $.ajax({
                        url: '/api/AssistBalance/GetData',
                        type: 'get',
                        data: {
                            operKey: g_operKey,
                            fromPeriod: fromPeriod,
                            toPeriod: toPeriod,
                            type_id: type_id,
                            sub_code:sub_code
                        },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then((res)=> {
                        if (res.result === 'OK') {
                            console.log('get assist subbal data');
                            if(res.data.length==0){
                                this.assistBalData=[];
                                this.$message({ message: '所选项无余额' });
                                return;
                            }
                            this.assistBalData=res.data;
                            this.queryDate=[res.pickFromPeriod,res.pickToPeriod];
                            this.assistBalData.forEach(row=>{
                                row.op_bal_debit=row.op_bal_debit=='0'?'':row.op_bal_debit;
                                row.op_bal_credit=row.op_bal_credit=='0'?'':row.op_bal_credit;
                                row.this_bal_debit=row.this_bal_debit=='0'?'':row.this_bal_debit;
                                row.this_bal_credit=row.this_bal_credit=='0'?'':row.this_bal_credit;
                                row.year_bal_debit=row.year_bal_debit=='0'?'':row.year_bal_debit;
                                row.year_bal_credit=row.year_bal_credit=='0'?'':row.year_bal_credit;
                                row.ed_bal_debit=row.ed_bal_debit=='0'?'':row.ed_bal_debit;
                                row.ed_bal_credit=row.ed_bal_credit=='0'?'':row.ed_bal_credit;
                            });
                        }
                    });
                },
                changeDate(){
                    this.getData( this.dateFormat(this.queryDate[0],'yyyy-MM-dd'), this.dateFormat(this.queryDate[1],'yyyy-MM-dd'),this.typeSelected, this.subSelected );
                },
                dateFormat(date, fmt) { 
                    if(typeof(date)=='string') date=new Date(date);
                    var o = {
                        "M+": date.getMonth() + 1, //月份
                        "d+": date.getDate(), //日
                        "h+": date.getHours(), //小时
                        "m+": date.getMinutes(), //分
                        "s+": date.getSeconds(), //秒
                        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
                        "S": date.getMilliseconds() //毫秒
                    };
                    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
                    for (var k in o)
                        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                    return fmt;
                },
                typeChange(type_id){
                    this.assistBalData=[];
                    this.typeSelectedInfo=this.assistTypes.find(t=>t.id==type_id);
                    let _sub_code=this.subSelected;
                    let sub=this.typeSelectedInfo.subs.find(s=>s.sub_code==_sub_code);
                    if(sub==null){
                        if(this.typeSelectedInfo.subs.length>0){
                            this.subSelected=this.typeSelectedInfo.subs[0].sub_code;
                            this.getData(this.dateFormat(this.queryDate[0],'yyyy-MM-dd'), this.dateFormat(this.queryDate[1],'yyyy-MM-dd'), this.typeSelected, this.subSelected);
                        }else{
                            this.subSelected='';
                        }
                    }else{
                        this.getData(this.dateFormat(this.queryDate[0],'yyyy-MM-dd'), this.dateFormat(this.queryDate[1],'yyyy-MM-dd'), this.typeSelected, this.subSelected);
                    }
                },
                subChange(sub_code){
                    this.assistBalData=[];
                    this.getData(this.dateFormat(this.queryDate[0],'yyyy-MM-dd'), this.dateFormat(this.queryDate[1],'yyyy-MM-dd'), this.typeSelected, this.subSelected);
                },
                exportBtn(){
                    let period=`${this.queryDate[0].substr(0,4)}${this.queryDate[0].substr(5,2)}-${this.queryDate[1].substr(0,4)}${this.queryDate[1].substr(5,2)}`;
                    let sub_code=this.subSelected;
                    let sub=this.typeSelectedInfo.subs.find(s=>s.sub_code==sub_code);
                    let data=[
                        ['核算项目余额表'], 
                        [`公司名称：${this.companyName}`,`核算类型：${this.typeSelectedInfo.name}`,null,`会计科目：${sub.sub_code_name}`,null, `会计期间：${period}`,null,null,'单位：元'],
                        ['辅助项名称', '期初余额', null, '本期发生', null, '本年累计', null, '期末余额', null ],
                        [ null, '借方', '贷方', '借方', '贷方', '借方', '贷方', '借方', '贷方' ]
                    ];
                    this.assistBalData.forEach(row=>{
                        let excelRow=[ row.assister_name, row.op_bal_debit, row.op_bal_credit, row.this_bal_debit, row.this_bal_credit, row.year_bal_debit, row.year_bal_credit, row.ed_bal_debit, row.ed_bal_credit ];
                        data.push(excelRow);
                    });
                    let merges=['A1:I1', 'B2:C2', 'D2:E2', 'F2:G2', 'A3:A4', 'B3:C3', 'D3:E3', 'F3:G3', 'H3:I3' ];
                    let bodyTitleName=[];
                    let  specialCellConfig=[
                        { 
                            "type": "s", 
                            "configObj": { 
                                "font": { "sz": 14, "bold": true },
                                "alignment": { "horizontal": "center" }
                            }, 
                            "controlScope": "col", 
                            "scope": [ 1, 1 ] 
                        },
                        { 
                            "type": "s", 
                            "configObj": { 
                                "border": { "top": { "style": "thin" }, "bottom": { "style": "thin" }, "left": { "style": "thin" }, "right": { "style": "thin" } },
                                "font": { "bold": true },
                                "alignment": { "horizontal": "center" }
                            }, 
                            "controlScope": "col", 
                            "scope": [ 3, 4 ] 
                        },
                        { 
                            "type": "s", 
                            "configObj": { "border": { "top": { "style": "thin" }, "bottom": { "style": "thin" }, "left": { "style": "thin" }, "right": { "style": "thin" } } }, 
                            "controlScope": "col", 
                            "scope": [ 5, data.length ] 
                        }, 
                    ];
                    window.webExportExcel(data,`核算项目余额表[${period}]`, merges, bodyTitleName, specialCellConfig);
                }
            }
        })
    </script>


</body>
</html>