﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.Models
{
    public class DbCreator
    {
        public string CreateDb()
        { 
            string sql = @"
         CREATE TABLE sheet_sale_main(
            company_id int4 NOT NULL,
            sheet_id serial,
            sheet_no varchar(25) NOT NULL, 
            order_sheet_no text,
            return_flag integer,
            branch_id integer, 
            red_flag text,
            supcust_no int, 
            total_amount numeric(9, 2) DEFAULT 0 NOT NULL,   
            is_retail boolean,
            cost_amount numeric(9, 2), 
            tax_amount numeric(9, 2),
            discount_info text,
            payway1_account text,
            payway1_amount numeric(9,2),
            payway2_account text,
            payway2_amount numeric(9,2),  
            now_pay_amount numeric(9, 2) DEFAULT 0 NOT NULL,
            now_disc_amount numeric(9, 2) DEFAULT 0 NOT NULL,
            paid_amount numeric(9, 2) DEFAULT 0 NOT NULL,
            disc_amount numeric(9, 2) DEFAULT 0 NOT NULL,   
            make_date timestamp,
            maker_id integer, 
            trade_date timestamp,
            approver_id integer,
            approve_time timestamp, 
            seller_id integer,
            seller_name text,
            seller2_id integer,  
            send_time timestamp,
            make_brief text,
            approve_brief text, 
            submit_time timestamp
            ) partition by range(make_date);

create table sheet_sale_main_20072102 partition of sheet_sale_main for values from ('2020-07-01') to ('2021-03-01');
create table sheet_sale_main_20072102 partition of sheet_sale_main for values from ('2021-03-01') to ('2021-06-01');

";

            sql += @"
CREATE TABLE sheet_sale_detail (
company_id int4 NOT NULL,
flow_id serial NOT NULL,
inout_flag int2,
sheet_id integer,
row_index int4,  
item_no integer not null,
sheet_item_name text,
branch_no int, 
batch_id varchar(6),
unit_no varchar(2),
unit_factor numeric(6,2),
quantity numeric(9,3),
orig_price numeric(9,4),
real_price numeric(9,4),
cost_price_prop numeric(9,4),
cost_price numeric(9,4), 
sub_amount numeric(14,4),
combine_flag text,
father_no integer,
combine_price numeric(14,4),
tax_amount numeric(9,2),
make_date timestamp, 
remark text COLLATE default
) partition by range(make_date);

create table sheet_sale_detail_20072102 partition of sheet_sale_detail for values from ('2020-07-01') to ('2021-03-01');
create table sheet_sale_detail_20072102 partition of sheet_sale_detail for values from ('2021-03-01') to ('2021-06-01');

";

 sql += @"create table info_item_class(company_id integer,class_id serial not null, class_name text,service_flag int,cls_type int,brand_id integer,mother_id integer,order_index int,cls_status text, ignore_stock_class boolean,general_class text) partition by hash(company_id);
 CREATE TABLE info_item_class_0 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 0);
 CREATE TABLE info_item_class_1 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 1);
 CREATE TABLE info_item_class_2 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 2);
 CREATE TABLE info_item_class_3 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 3);
";

 sql += @"create table info_item_class(company_id integer,class_id serial not null, class_name text,service_flag int,cls_type int,brand_id integer,mother_id integer,order_index int,cls_status text, ignore_stock_class boolean,general_class text) partition by hash(company_id);
CREATE INDEX idx_info_item_class_name ON info_item_class(class_name);

CREATE TABLE info_item_class_0 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 0);
 CREATE TABLE info_item_class_1 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 1);
 CREATE TABLE info_item_class_2 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 2);
 CREATE TABLE info_item_class_3 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 3);
";

            sql += @"create table info_item_brand(company_id integer,brand_id serial not null,brand_name text,remark text);
                CREATE INDEX idx_info_item_brand_name ON info_item_brand(brand_name);
";
            
            sql += @"create table info_item_prop(company_id integer,item_id serial not null,item_name text,item_model text,barcode text,simple_name text,service_flag boolean,item_class integer,item_brand integer, unit_no text, buy_price numeric(8, 2),wholesale_price numeric(8, 2),retail_price numeric(8, 2),min_sale_price numeric(8, 2),cost_change_qty numeric(14, 4),cost_price_avg numeric(14, 4),cost_amt numeric(14, 2), cost_price_recent numeric(14, 4),cost_type int, combine_sta char (1),status char (1) ,display_flag char (1),vip_price numeric(8,2),color_group integer,use_part_colors boolean, available_color text,size_group integer,barcode_style integer,py_str text, py_str1 text, item_season integer, other_barcode  text, barcode_other varchar (512), photo1 image,photo2 image, allow_disc boolean, vip_price1 numeric(14, 4) ,vip_price2 numeric(14, 4) ,valid_days int,  other_class varchar (255), ignore_stock booleab, item_order_index int,  rpt_class text, create_time datetime,update_time datetime,update_photo_time datetime) partition by hash(company_id);
                CREATE INDEX idx_info_item_prop_company_id ON info_item_prop(company_id);
                CREATE INDEX idx_info_item_prop_py_str ON info_item_prop(py_str);
                CREATE INDEX idx_info_item_prop_item_name ON info_item_prop(item_name);
                CREATE INDEX idx_info_item_prop_barcode ON info_item_prop(barcode);
 CREATE TABLE info_item_prop_0 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 0);
 CREATE TABLE info_item_prop_1 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 1);
 CREATE TABLE info_item_prop_2 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 2);
 CREATE TABLE info_item_prop_3 PARTITION OF info_item_class FOR VALUES WITH(MODULUS 4, REMAINDER 3); 
";

            sql += @"create table info_item_multi_unit(company_id integer,item_id integer,unit_no text,unit_factor int,wholesale_price numeric(8,2),retail_price numeric(8,2),buy_price numeric(8,2),vip_price numeric(8,2),vip_price1 numeric(8,2),vip_price2 numeric(8,2),vip_price3 numeric(8,2),barcode text) partition by hash(company_id);
                    
 CREATE INDEX idx_info_item_multi_unit_company_id ON info_item_multi_unit(company_id);
 CREATE INDEX idx_info_item_multi_unit_unit_id ON info_item_multi_unit(item_id);

CREATE TABLE info_item_multi_unit_0 PARTITION OF info_item_multi_unit FOR VALUES WITH(MODULUS 4, REMAINDER 0);
                     CREATE TABLE info_item_multi_unit_1 PARTITION OF info_item_multi_unit FOR VALUES WITH(MODULUS 4, REMAINDER 1);
                     CREATE TABLE info_item_multi_unit_2 PARTITION OF info_item_multi_unit FOR VALUES WITH(MODULUS 4, REMAINDER 2);
                     CREATE TABLE info_item_multi_unit_3 PARTITION OF info_item_multi_unit FOR VALUES WITH(MODULUS 4, REMAINDER 3);
      
";
            sql += @"create table info_avail_unit(company_id integer, unit_no text not null, is_big_unit boolean);";
            

            return "";
        }
    }
}
