﻿using ArtisanManage.CwPages;
using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Pages.CwPages;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualBasic;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace ArtisanManage.MyCW
{
    public class CwRowBase: ICloneable
    {
        public CwRowBase()
        {

        }

        [SaveToDB] public int row_index { get; set; } = 1;

        [SaveToDB] [FromFld] public virtual string remark { get; set; }
        [FromFld] public string sheet_id { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone(); //浅复制
        }
    }
    public class CInfoForApproveBase
    {
        public string ErrMsg = "";
    }
    public enum LOAD_PURPOSE
    {
        SHOW_OR_APPROVE,
        SHOW,
        APPROVE
    }

    public class CwBizInfo
    {
        public string biz_sheet_type { get; set; } = "";
        public string biz_sheet_id { get; set; } = "";
        public string biz_sheet_no { get; set; } = "";
        public string biz_make_brief { get; set; } = "";
    }

    public class SheetCwBase<CROW> where CROW : CwRowBase ,new ()
    {
        protected LOAD_PURPOSE LoadPurpose;
        [JsonIgnore] public string MainTable { get; set; } = "";
        [JsonIgnore] public string DetailTable { get; set; } = "";
        public bool FIXING_ARREARS = false;
        //   public int ItemInoutFlag = 0;
        protected CInfoForApproveBase InfoForApprove = null;
       
        [SaveToDB] [IDField] [FromFld] public string sheet_id { get; set; } = "";
        [SaveToDB][IDField][FromFld] public string sheet_no { get; set; } = "";
        [SaveToDB] [FromFld] public string company_id { get; set; } = "";
        public string company_name { get; set; } = "";

        public string company_tel = "", company_address="";

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public dynamic printTemplate = null;
        public string OperKey { get; set; } = ""; 
        public string OperID { get; set; } = "";

        //  [SaveToDB] public string oper_id { get; set; } = ""; 
        
        public virtual async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true)
        {
            
        }


        [SaveToDB] [FromFld] public string red_flag { get; set; } = "";
        [SaveToDB] [FromFld] public string red_sheet_id { get; set; } = "";
        //[SaveToDB] [FromFld] public string red_sheet_date { get; set; } = "";

        public virtual float total_amount { get; set; }
        public virtual int money_inout_flag { get; set; }
        [SaveToDB] [FromFld] public string maker_id { get; set; } = "";
        [FromFld((MyJXC.LOAD_PURPOSE)LOAD_PURPOSE.SHOW)] public string maker_name { get; set; } = "";
        [SaveToDB] [FromFld("to_char(t.make_time,'yyyy-MM-dd hh24:mi:ss') as make_time")] public string make_time { get; set; } = "";
        [SaveToDB] [FromFld("to_char(t.happen_time,'yyyy-MM-dd hh24:mi:ss') as happen_time")] public string happen_time { get; set; } = "";
        public string happen_date
		{
			get
			{
                return CPubVars.GetDateTextNoTime(this.happen_time);
			}
		}
        [SaveToDB] [FromFld] public string approver_id { get; set; } = "";
        [FromFld((MyJXC.LOAD_PURPOSE)LOAD_PURPOSE.SHOW)] public string approver_name { get; set; } = "";
        [SaveToDB] [FromFld("to_char(t.approve_time,'yyyy-MM-dd hh24:mi:ss') as approve_time")] public string approve_time { get; set; } = "";
         
        [SaveToDB] [FromFld] public string make_brief { get; set; } = "";
        [SaveToDB] [FromFld] public string approve_brief { get; set; } = "";
        [FromFld("biz.biz_id_nos", (MyJXC.LOAD_PURPOSE)LOAD_PURPOSE.SHOW)] public string biz_id_nos { get; set; }
        [FromFld("biz.biz_nos", (MyJXC.LOAD_PURPOSE)LOAD_PURPOSE.SHOW)] public string biz_nos { get; set; }
        [FromFld((MyJXC.LOAD_PURPOSE)LOAD_PURPOSE.SHOW)] public string business_sheet_type { get; set; }

        //[FromFld("map.biz_nos")] public string biz_nos { get; set; }//可能以后用于打印模板

        public bool IsFromWeb { get; set; } = false;
        public List<CROW> SheetRows = new List<CROW>();
        public SheetCwBase(string mainTable, string detailTable, LOAD_PURPOSE loadPurpose)
        {
            MainTable = mainTable;
            DetailTable = detailTable;
            LoadPurpose = loadPurpose;
        }
        private string GetFieldsFromClassType(Type tp)
        {
            string flds = "";
            PropertyInfo[] props = tp.GetProperties();
            foreach(PropertyInfo p in props)
            {
                dynamic dAttr = p.GetCustomAttribute(typeof(FromFld));
                string fromFld = null;
                bool load = true;
                if (dAttr != null) load=Convert.ToBoolean(dAttr.Load);
                if (!load) continue;
                if (dAttr != null) fromFld = dAttr.Field.ToString();

                if (fromFld != null)
                {
                    LOAD_PURPOSE Purpose =(LOAD_PURPOSE) dAttr.Purpose;
                    
                    if(Purpose==LOAD_PURPOSE.SHOW && LoadPurpose!=LOAD_PURPOSE.SHOW) continue;                     
                    if (Purpose == LOAD_PURPOSE.APPROVE && LoadPurpose != LOAD_PURPOSE.APPROVE) continue;
                    string prefix = "";
                    if (Purpose == LOAD_PURPOSE.SHOW_OR_APPROVE)
                    {
                        if(!fromFld.Contains("."))
                           prefix = $"t.";
                    }
                    if (flds != "") flds += ",";
                    if (fromFld == "")
                        flds += prefix + p.Name;
                    else if(fromFld.StartsWith("tochar"))
                        flds += fromFld;
                    else 
                        flds += prefix + fromFld;                    
                }
            }
            return flds;
        }
        [JsonIgnore] public string MainLeftJoin { get; set; } = "";
        [JsonIgnore] public string DetailLeftJoin { get; set; } = "";
        public async Task<CInfoForApproveBase> Load(CMySbCommand cmd, string companyID, string sheetID,bool bForRed=false)  
        {
            this.company_id = companyID;
            if (sheetID == null) sheetID = "";
            this.sheet_id = sheetID;
            
            if (sheet_id != "")
            {
                string sql;
                SQLQueue QQ = new SQLQueue(cmd);
                string flds = GetFieldsFromClassType(this.GetType());
                sql = $"select {flds} from {MainTable} t {MainLeftJoin} where t.company_id={companyID} and t.sheet_id={sheet_id};";
                sql = sql.Replace("~sheet_id", sheet_id).Replace("~SHEET_ID", sheet_id);
                sql = sql.Replace("~company_id", companyID).Replace("~COMPANY_ID", companyID);
                QQ.Enqueue("main", sql);
                if (DetailTable != "")
                {
                    flds = GetFieldsFromClassType(typeof(CROW));
                    sql = $"select {flds} from {DetailTable} t {DetailLeftJoin} where t.company_id={companyID} and t.sheet_id={sheet_id} order by row_index;";
                    sql = sql.Replace("~sheet_id", sheet_id).Replace("~SHEET_ID", sheet_id);
                    sql = sql.Replace("~company_id", companyID).Replace("~COMPANY_ID", companyID);
                    if (bForRed)
                    {
                        sql = sql.Replace("t.debit_amount", "-t.debit_amount as debit_amount");
                        sql = sql.Replace("t.credit_amount", "-t.credit_amount as credit_amount");
                    }
                    QQ.Enqueue("detail", sql);
                }
            
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    string sqlName = QQ.Dequeue();
                    if (sqlName == "main")
                    {
                        //Type T = this.GetType();

                        if (dr.Read())
                        {
                            InvokeStaticMethod(typeof(CDbDealer), "SetObjectByDr", new Type[] { this.GetType() }, new object[] { dr, this, false });
                        }

                    }
                    else if (sqlName == "detail")
                    {
                        List<CROW> rows = CDbDealer.GetRecordsFromDr<CROW>(dr, false);
                        SheetRows.Clear();
                        List<CwRowVoucher> rowsVo = rows as List<CwRowVoucher>;
                        if (rowsVo.All(row => row.debit_amount == 0 && row.credit_amount == 0))
                        {
                            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
                            InfoForApprove.ErrMsg = "凭证数据未刷新，请联系客服更新版本";
                            QQ.Clear();
                            return InfoForApprove;
                        }
                            
                        foreach (CROW r in rows) SheetRows.Add(r);
                    }
                }
                QQ.Clear();

                if (bForRed)
                {
                    red_flag = "2";
                    money_inout_flag *= -1;
                    
                }

                if (LoadPurpose == LOAD_PURPOSE.APPROVE)
                {
                    QQ = new SQLQueue(cmd);
                    GetInfoForApprove_SetQQ(QQ);
                    if(QQ.Count > 0)
                    {
                        dr = await QQ.ExecuteReaderAsync();
                        while (QQ.Count > 0)
                        {
                            string sqlName = QQ.Dequeue();
                            GetInfoForApprove_ReadData(dr, sqlName, bForRed);
                        }
                        QQ.Clear();
                    }
                    
                }          
            }
            return InfoForApprove; 
        }
    
        public async Task<List<TSHEET>> LoadMultiSheets<TSHEET>(CMySbCommand cmd, string companyID, string sheetIDs, string sortColumn, string sortDirection, LOAD_PURPOSE loadPurpose = LOAD_PURPOSE.SHOW) where TSHEET : SheetCwBase<CROW>
        {
            this.company_id = companyID;
            if (string.IsNullOrEmpty(sheetIDs)) throw new Exception("sheetIDs should be specified");
            List<TSHEET> lstSheets = new List<TSHEET>();

            Dictionary<string, TSHEET> dicSheets = new Dictionary<string, TSHEET>();

            if (sheetIDs != "")
            {
                string sql;
                SQLQueue QQ = new SQLQueue(cmd);
                string flds = GetFieldsFromClassType(this.GetType());
                if (!string.IsNullOrEmpty(sortColumn))
                {
                    if (flds.Contains(sortColumn))
                    {
                        if (sortDirection == "desc") sortColumn += " desc";
                        sortColumn += ",";
                    }
                    else sortColumn = "";
                }

                sql = $"select {flds} from {MainTable} t {MainLeftJoin} where t.company_id={companyID} and t.sheet_id in ({sheetIDs}) order by {sortColumn}sheet_id;";
                // sql = sql.Replace("~sheet_id", sheet_id);
                sql = sql.Replace("~company_id", companyID, StringComparison.OrdinalIgnoreCase);
                QQ.Enqueue("main", sql);
                if (DetailTable != "")
                {
                    flds = GetFieldsFromClassType(typeof(CROW));
                    sql = $"select {flds} from {DetailTable} t {DetailLeftJoin} where t.company_id={companyID} and t.sheet_id in ({sheetIDs}) order by row_index;";
                    sql = sql.Replace("~company_id", companyID,StringComparison.OrdinalIgnoreCase);
                    QQ.Enqueue("detail", sql);
                }

                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    string sqlName = QQ.Dequeue();
                    if (sqlName == "main")
                    {
                        //SheetBase<TROW> sheet = new SheetBase<TROW>(MainTable,DetailTable,LOAD_PURPOSE.SHOW);

                        while (dr.Read())
                        {
                            TSHEET sheet = (TSHEET)Activator.CreateInstance(typeof(TSHEET), new object[] { loadPurpose });
                            //  TSHEET sheet = new TSHEET(MainTable, DetailTable, LOAD_PURPOSE.SHOW);
                            InvokeStaticMethod(typeof(CDbDealer), "SetObjectByDr", new Type[] { this.GetType() }, new object[] { dr, sheet, false });

                            dicSheets.Add(sheet.sheet_id, sheet);
                            lstSheets.Add(sheet);
                        }
                    }
                    else if (sqlName == "detail")
                    {
                        List<CROW> rows = CDbDealer.GetRecordsFromDr<CROW>(dr, false);
                        foreach (var row in rows)
                        {
                            if (dicSheets.ContainsKey(row.sheet_id))
                            {
                                var sheet = dicSheets[row.sheet_id];
                                sheet.SheetRows.Add((CROW)row);
                            }
                        }
                    }
                }
                QQ.Clear();
            }
            return lstSheets;
        }

        public virtual void Init()
        { 
            Security.GetInfoFromOperKey(OperKey, out string companyID,out string operID);
            this.OperID = operID;
            this.company_id = companyID;

        }
        protected virtual void InitForGetSaveSQL()
        {
            if (maker_id == "") maker_id = OperID; 
        }
        public string GetSaveSQL()
        {
            if(approver_id.IsInvalid() || make_time.IsInvalid()) make_time = CPubVars.GetDateText(DateTime.Now);
            //maker_id = OperID;
            InitForGetSaveSQL();
            if (happen_time == "") happen_time = make_time;

            string sqlMain = InvokeStaticMethod(typeof(CDbDealer), "GetSaveSqlFromObject", new Type[] { this.GetType() }, new object[] {company_id, this, MainTable, true, null}).ToString() + ";";
            string sqlDetail = "";
            List<CField> lstOtherFlds = new List<CField>();
            lstOtherFlds.Add(new CField("company_id", company_id));
            lstOtherFlds.Add(new CField("happen_time", happen_time));
            lstOtherFlds.Add(new CField("sheet_id", "@sheet_id"));         

            if(sheet_id!="" && DetailTable!="")
               sqlDetail += $"delete from {DetailTable} where sheet_id={sheet_id};";
            int rowIndex = 1;
            foreach (CROW row in SheetRows)
            {
                row.row_index = rowIndex;
                string sqlRow = CDbDealer.GetSaveSqlFromObject<CROW>(company_id, row, DetailTable, false, lstOtherFlds) + ";";
                sqlDetail += sqlRow;
                rowIndex++;
            }
            string sql;
            if (sheet_id=="")
            {
                sql = $@"SELECT yj_exeSqlByInsertedRowID('{sqlMain.Replace("'", "''")}','{sqlDetail.Replace("'", "''")}','@sheet_id');";
            }
            else
            {
                sqlDetail = sqlDetail.Replace("@sheet_id", sheet_id);
                sql = sqlMain+sqlDetail;
            }
            return sql;
        }
        public virtual async Task<string> Save(CMySbCommand cmd, bool bAutoCommit=true)
        {
            string sql;
            maker_id = OperID;

            //if (sheet_id == "")
            //{
            //    string sheetType = StrFromSheetType(sheet_type);
            //    sql = $"select yj_getnewsheetno({company_id},'{sheetType}',{maker_id})";
            //    cmd.CommandText = sql;
            //    object ov=await cmd.ExecuteScalarAsync();
            //    if (ov != null && ov != DBNull.Value)
            //        sheet_no = ov.ToString();
            //}
            //else
            if(sheet_id!="")
            {
                sql = $"select approve_time from {MainTable} where sheet_id={sheet_id} and company_id = {company_id}";
                cmd.CommandText = sql;
                object ov = await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value)
                {
                    return "该凭证已审核,不能保存";
                }
            }
            CMySbTransaction tran = null;
            if (bAutoCommit)
                tran =await cmd.Connection.BeginTransactionAsync();


            sql = GetSaveSQL();
            cmd.company_id = this.company_id;
            cmd.oper_id = this.OperID;
            cmd.CommandText = sql;
            try
            {
                object ov = await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value)
                {
                    sheet_id = ov.ToString().Split(",")[0];
                }
                await OnSheetIDGot(cmd, sheet_id,null,VoucherOperType.Save);
                if (tran != null) tran.Commit();
            }
            catch (Exception e)
            {
                if (tran != null) tran.Rollback();
                string errMsg = $"In SheetCwBase.Save, sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite},sql:{sql}";
                NLogger.Error(errMsg);
                MyLogger.LogMsg(errMsg, company_id);
                return "保存凭证发生了错误";
            
            }
            return "";
        }

        protected virtual void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            if(maker_id=="") maker_id = OperID;
            approver_id = OperID;
            //if (red_flag != "2" && sheet_id == "") sheet_no = "";
            //if (sheet_no == "")
            //{
            //    string sheetType = StrFromSheetType(sheet_type);
            //    var sql = $"select yj_getnewsheetno({company_id},'{sheetType}',{maker_id})";
            //    QQ.Enqueue("sheet_no", sql);
            //    NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
            //   // logger.Info("in GetInfoForApprove_SetQQ:sql=" + sql);
            //}
        }
        protected virtual void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName,bool bForRed = false)
        {
             
        }
        protected async Task<CInfoForApproveBase> GetInfoForApprove(CMySbCommand cmd)
        { 
            SQLQueue QQ = new SQLQueue(cmd);
            if (sheet_id != "")
            {
                if (!FIXING_ARREARS)
                {
                    string check_sql = $"select approve_time,red_flag from {MainTable} where sheet_id={sheet_id} and company_id = {company_id}";
                    QQ.Enqueue("check_sheet", check_sql);
                } 
            }
         
            GetInfoForApprove_SetQQ(QQ);
            string errMsg = "";
            if (QQ.Count > 0)
            {
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    string tbl = QQ.Dequeue();
                    if (tbl == "check_sheet")
                    {
                        dynamic checkSheet = CDbDealer.Get1RecordFromDr(dr, false);
                        if (checkSheet!=null&&checkSheet.approve_time != "")
                        {
                            errMsg = "凭证已审核过,不能再次审核";
                        }
                        if(checkSheet!=null && checkSheet.red_flag != "")
                        {
                            errMsg = "凭证已红冲，不能审核";
                        }
                        if(checkSheet==null && sheet_id != "")
                        {
                            sheet_id = "";//在查凭证界面删除了该凭证，但是之前已经打开了未审核的，在凭证界面点审核导致更新了没有sheet_id的记录，这里必须清空然后重新生成sheet_id
                        }  
                    }
                    else
                    {
                        GetInfoForApprove_ReadData(dr, tbl);
                        if (InfoForApprove!=null && InfoForApprove.ErrMsg != "")
                        {
                            errMsg = InfoForApprove.ErrMsg;
                            break;
                        }
                    }
                }
                QQ.Clear();
            }
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            InfoForApprove.ErrMsg = errMsg;
            return InfoForApprove;
        }
    
        protected virtual async Task<string> CheckSheetValid(CMySbCommand cmd=null)
        {
            if (company_id == "")
                return "请指定公司ID";
           
            if (!FIXING_ARREARS)
            {
                if (OperID == "") return "请指定操作员";
                if (approve_time!="")
                {
                    return "凭证已经审核完成，请勿重复提交";
                }
            }
            
            return "OK";
        }
        protected virtual string GetApproveSQL(CInfoForApproveBase info)
        {
            return "";
        }

        public virtual async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info, VoucherOperType operType)
        {
            if (operType==VoucherOperType.Red)
            {
                string sql = $"update {DetailTable} set remark=concat('冲销{Convert.ToDateTime(happen_time).ToString("yyyy-MM")} 记-{sheet_no} ',remark) where company_id={company_id} and sheet_id={sheetID};";
                cmd.CommandText = sql;
                await cmd.ExecuteScalarAsync();
            }
        }
        public async virtual Task<string> SaveAndApprove(CMySbCommand cmd, bool bAutoCommit = true)
        {
            string sError = "";
            string checkResult = await CheckSheetValid();
            if (checkResult != "OK") return checkResult;
            cmd.company_id = this.company_id;
            cmd.oper_id = this.OperID;
            /************tran内判重************/
            string redisKey = $"voOperateDuplicate{company_id}-{sheet_id}";
            bool hasSheetID = sheet_id == "" ? false : true;
            if(bAutoCommit && hasSheetID)//保存凭证可以重复，审核不能重复
            {
                string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                await RedisHelper.ExpireAsync(redisKey, 30); //30秒
                if (redisValue == "1")
                {
                    return "点快啦，请勿重复操作凭证";
                }
            }
            /************************************/

            approve_time = CPubVars.GetDateText(DateTime.Now);
            approver_id = OperID;
            CInfoForApproveBase info = await GetInfoForApprove(cmd);

            if (info.ErrMsg != "") return info.ErrMsg;

            string sqlSave = GetSaveSQL();
            string sqlApprove = GetApproveSQL(info);
            if (info.ErrMsg != "") return info.ErrMsg;
            
        
            CMySbTransaction tran = null;
            try
            {
                if (bAutoCommit)
                    tran = await cmd.Connection.BeginTransactionAsync();

                string err=await OnSheetBeforeApprove(cmd, info);
                if (err != "")
                {
                    if (bAutoCommit && tran != null) tran.Rollback();
                    return err;
                 }
                cmd.CommandText = sqlSave + sqlApprove;
                object ov = await cmd.ExecuteScalarAsync();
                if (sheet_id == "")
                {
                    if (ov != DBNull.Value && ov != null)
                       sheet_id = ov.ToString().Split(',')[0];
                }
                await OnSheetIDGot(cmd,sheet_id, info,VoucherOperType.SaveAndApprove);
                if (info.ErrMsg != "")
                {
                    if (bAutoCommit && tran != null) tran.Rollback();
                    if (bAutoCommit && hasSheetID) await RedisHelper.ExpireAsync(redisKey, 5);//tran内判重延迟结束
                    return info.ErrMsg;
                }
                if (bAutoCommit && tran != null) tran.Commit();
            }
            catch (Exception e)
            {
                if (bAutoCommit && tran != null) tran.Rollback();
                if (bAutoCommit && hasSheetID) await RedisHelper.ExpireAsync(redisKey, 5);//tran内判重延迟结束

                string errMsg = $"In SheetCwBase.SaveAndApprove, sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite},sql:{cmd.CommandText}";
                NLogger.Error(errMsg);
                MyLogger.LogMsg(errMsg, company_id);
                return "审核凭证发生了错误";
            }
            
            if (bAutoCommit && hasSheetID) await RedisHelper.DelAsync(redisKey);//tran内判重立刻结束
            return sError;
        }

    
        public virtual async Task<string> OnSheetBeforeApprove(CMySbCommand cmd,  CInfoForApproveBase info)
		{
            return "";
		}
        public async virtual Task<string> Red(CMySbCommand cmd, string companyID, string sheetID, string rederID, bool bAutoCommit = true)
        {
            string sError = "";
            #region CheckBeforeRed
            if (sheetID == "")
            {
                sError = "红冲凭证必须指定凭证号"; return sError;
            }

            /************tran内判重************/
            string redisKey = $"voOperateDuplicate{company_id}-{sheet_id}";
            if (bAutoCommit)
            {
                string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                await RedisHelper.ExpireAsync(redisKey, 30); //30秒
                if (redisValue == "1")
                {
                    return "点快啦，请勿重复操作凭证";
                }
            }
            /************************************/

            /************tran外判重************/
            dynamic origSheet = await CDbDealer.Get1RecordFromSQLAsync($"select red_flag,approve_time,happen_time,sheet_no from {MainTable} where sheet_id={sheetID} and company_id = {companyID}", cmd);
            if (origSheet == null) return "该凭证不存在，不能红冲";
            if (origSheet.approve_time == "") return "该凭证未被审核，不能红冲";
            if (origSheet.red_flag != "") return "该凭证已被红冲，不能再次红冲";
            /************************************/

            DateTime approveTime = DateTime.Now;
            if (origSheet.approve_time != "")
            {
                approveTime = Convert.ToDateTime(origSheet.approve_time);

                dynamic redDays = await CDbDealer.Get1RecordFromSQLAsync($"select rights->'delicacy'->'redSheetDayLimit'->'value' as redsheetdaylimit from info_operator o left join info_role r on o.company_id=r.company_id and o.role_id=r.role_id where oper_id={rederID}", cmd);
                string limitDays = "";
                if (redDays != null) limitDays = redDays.redsheetdaylimit;
                if (limitDays.Contains("days")) limitDays = limitDays.Replace("days", ""); else limitDays = "";
                limitDays = limitDays.Replace("\"", "");
                int nLimitDays = 90;
                if (limitDays != "") nLimitDays = Convert.ToInt32(limitDays);
                DateTime sevenDaysBefore = DateTime.Now.Date.AddDays(-(nLimitDays - 1));

                if (approveTime < sevenDaysBefore)
                {
                    sError = $"只能红冲最近{nLimitDays}天内审核的凭证";
                    return sError;
                }

            }

            DateTime happenTime = DateTime.Now;
            if (origSheet.happen_time != null)
            {
                happenTime= Convert.ToDateTime(origSheet.happen_time);
            }

            CInfoForApproveBase info = await Load(cmd, companyID, sheetID,true);
            if (info!=null && info.ErrMsg != "")
            {
                sError = info.ErrMsg;
                return sError;
            }
            #endregion


            // red_flag = "2";
            sheet_id = "";
            sheet_no = origSheet.sheet_no.ToString();
            red_sheet_id = sheetID;
            approver_id = rederID;
            approve_time = CPubVars.GetDateText(DateTime.Now);
            happen_time = happenTime.ToString();
            //money_inout_flag *= -1;

            string sqlSave = GetSaveSQL();
            string sqlApprove = GetApproveSQL(info);
            string sql = sqlSave + sqlApprove;

            CMySbTransaction tran = null;
            try
            {
                string sql2 = $"select yj_getnewvoucherno({company_id},'{happen_time}');";
                cmd.CommandText = sql2;
                object ov = await cmd.ExecuteScalarAsync();
                string newSheetNo = "";
                if (ov != null && ov != DBNull.Value) newSheetNo = ov.ToString();

                sql += $"update {MainTable} set red_flag='1' where sheet_id={sheetID} and company_id = {companyID};";
                sql += $"update {MainTable} set sheet_no={newSheetNo},make_brief=concat('冲销{happenTime.ToString("yyyy-MM")} 记-{sheet_no} ',make_brief) where company_id={companyID} and period='{happenTime.GetMonthStart()}' and sheet_no={sheet_no} and red_flag='2';";
                cmd.CommandText = sql;
                if (bAutoCommit) tran = await cmd.Connection.BeginTransactionAsync();
                ov = await cmd.ExecuteScalarAsync();
                if (sheet_id == "")
                {
                    if (ov != DBNull.Value && ov != null)
                        sheet_id = ov.ToString().Split(',')[0];
                }

                OperID = rederID;
                await OnSheetIDGot(cmd, sheet_id, info, VoucherOperType.Red);
                if (info.ErrMsg != "")
                {
                    if (bAutoCommit && tran != null)
                    {
                        tran.Rollback();
                        await RedisHelper.ExpireAsync(redisKey, 5);//tran内判重延迟结束
                    }
                    return info.ErrMsg;
                }
                if (bAutoCommit && tran != null) tran.Commit();
            }
            catch (Exception e)
            {
                if (bAutoCommit && tran != null)
                {
                    tran.Rollback();
                    await RedisHelper.ExpireAsync(redisKey, 5);//tran内判重延迟结束
                }
                return e.Message;
            }
            if (bAutoCommit) await RedisHelper.DelAsync(redisKey);//tran内判重立刻结束
            return sError;
        }

        public object InvokeStaticMethod(Type clsType,string method, Type[] arrT, object[] arrParam)
        {
            MethodInfo mi = clsType.GetMethod(method).MakeGenericMethod(arrT);
            return mi.Invoke(null, arrParam);
        }
        public object InvokeMethod(Object obj, string method, Type[] arrT, object[] arrParam)
        {
            Type clsType = obj.GetType();
            MethodInfo mi = clsType.GetMethod(method).MakeGenericMethod(arrT);
            return mi.Invoke(null, arrParam);
        }
       
        public async virtual Task<string> Delete(CMySbCommand cmd, string companyID, string sheetID, string rederID, bool bAutoCommit = true)
        {
            string sError = "";
            if (sheetID == "")
            {
                sError = "删除必须指定单据号"; return sError;
            }

            /************tran外判重************/
            // string sql1 = $"select approve_time from {MainTable} where sheet_id={sheetID}";
            dynamic main = await CDbDealer.Get1RecordFromSQLAsync($"select approve_time,red_flag from {MainTable} where company_id={companyID} and sheet_id={sheetID}", cmd);
            if (main == null) return "该凭证不存在";
            if (main != null && main.approve_time!="") return "该凭证已审核，不能删除";
            if (main != null && main.red_flag != "") return "该凭证已红冲，不能删除";

            List<string> sqlAndMsg = await CheckBeforeDeleteVoucher(companyID, sheetID,cmd);
            string sql = sqlAndMsg[0];
            if (sqlAndMsg[1] != "")
            {
                return sqlAndMsg[1];//return error msg
            }
            /************************************/

            /************tran内判重************/
            string redisKey = $"voOperateDuplicate{company_id}-{sheet_id}";
            if (bAutoCommit)
            {
                string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                await RedisHelper.ExpireAsync(redisKey, 30); //30秒
                if (redisValue == "1")
                {
                    return "点快啦，请勿重复操作凭证";
                }
            }
            /************************************/

            sql += $"delete from {MainTable} where  company_id={companyID} and sheet_id={sheetID};";
            sql += $"delete from {DetailTable} where  company_id={companyID} and sheet_id={sheetID};";
            sql += $"delete from cw_voucher_sheet_mapper where company_id={companyID} and voucher_id={sheetID};";
            cmd.company_id = companyID;
            cmd.oper_id = rederID;
            cmd.CommandText = sql;
            CMySbTransaction tran = null;
            try
            {
                if (bAutoCommit) tran = cmd.Connection.BeginTransaction();
                object ov = await cmd.ExecuteScalarAsync();
                if (tran != null) tran.Commit();
            }
            catch (Exception e)
            {
                if (bAutoCommit && tran != null)
                {
                    tran.Rollback();
                    await RedisHelper.ExpireAsync(redisKey, 5);//tran内判重延迟结束
                }
                return e.Message;
            }
            if (bAutoCommit) await RedisHelper.DelAsync(redisKey);//tran内判重立刻结束
            return sError;
        }

        public async Task<List<string>> CheckBeforeDeleteVoucher(string companyID, string sheetID, CMySbCommand cmd)
        {
            List<string> sqlAndMsg = new List<string>();
            sqlAndMsg.Add("");//update sql
            sqlAndMsg.Add("");//msg
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select coalesce(setting ->>  'useAccounting','false') useaccounting, coalesce(setting ->>  'accountingPeriod','') accountingperiod from company_setting where company_id = {companyID}", cmd);
            if (setting == null)
            {
                sqlAndMsg[1] = "请先保存公司设置";
                return sqlAndMsg;
            }
            if (Convert.ToBoolean(setting.useaccounting) == false)
            {
                sqlAndMsg[1] = "请先开账";
                return sqlAndMsg;
            }
            dynamic cw_voucher_main = await CDbDealer.Get1RecordFromSQLAsync($"select happen_time,period,sheet_no,red_flag,make_brief from {MainTable} where company_id={companyID} and sheet_id={sheetID};", cmd);
            dynamic cw_voucher_no = await CDbDealer.Get1RecordFromSQLAsync($"select * from cw_voucher_no where company_id={companyID} and period='{cw_voucher_main.period}';", cmd);

            //1.判断会计期间
            DateTime happenTime = Convert.ToDateTime(cw_voucher_main.happen_time);
            DateTime opPeriod = Convert.ToDateTime(setting.accountingperiod + "-01");
            if (!(opPeriod <= happenTime))
            {
                sqlAndMsg[1] = $"当前会计期间为【{setting.accountingperiod}】，请在该凭证所在会计期间及以后删除凭证";
                return sqlAndMsg;
            }

            //2.处理sheet_no断号
            int sheetNo = Convert.ToInt32(cw_voucher_main.sheet_no);
            int nextVoucherNo = 0;
            if (cw_voucher_no != null)
            {
                nextVoucherNo = Convert.ToInt32(cw_voucher_no.next_voucher_no);
            }
            if (sheetNo == nextVoucherNo - 1)//删除最后一个sheet_no，next_voucher_no就往前退一个
            {
                sqlAndMsg[0] = $"update cw_voucher_no set next_voucher_no={nextVoucherNo - 1} where company_id={companyID} and period='{setting.accountingperiod}-01';";
            }
            //如果断的是中间的号，函数里会判断并填补，直接跑函数即可

            return sqlAndMsg;
        }

        public async virtual Task<string> CancelApprove(CMySbCommand cmd, string companyID, string sheetID, string rederID, bool bAutoCommit = true)
        {
            /************tran内判重************/
            string redisKey = $"voOperateDuplicate{company_id}-{sheet_id}";
            if (bAutoCommit)
            {
                string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                await RedisHelper.ExpireAsync(redisKey, 30); //30秒
                if (redisValue == "1")
                {
                    return "点快啦，请勿重复操作凭证！";
                }
            }
            /************************************/


            /************tran外判重************/
            dynamic cw_voucher_main = await CDbDealer.Get1RecordFromSQLAsync($"select period,happen_time,approve_time,approver_id,approve_brief,red_flag from cw_voucher_main where company_id={companyID} and sheet_id={sheetID};", cmd);
            string msg = await CheckBeforeCancelApproveVoucher(companyID, sheetID, cw_voucher_main, cmd);
            if (msg != "") return msg;
            /************************************/

            CMySbTransaction tran = null;
            if (bAutoCommit) tran = await cmd.Connection.BeginTransactionAsync();

            await CwLog.Save(companyID, rederID, null, "Voucher", $"Info: sheet_id: {sheetID}, before cancel [ approve_time: {cw_voucher_main.approve_time}, approver_id: {cw_voucher_main.approver_id}, approve_brief: {cw_voucher_main.approve_brief} ]; cancel approve voucher", cmd);

            string approve_brief = cw_voucher_main.approve_brief;
            int cancelTime = 0;
            if (approve_brief!=null && approve_brief != "") cancelTime = Convert.ToInt16(approve_brief.Split(':')[1]);
            string sql = $"update cw_voucher_main set approve_time=null, approver_id=null, approve_brief='cancelTime:{cancelTime+1}' where company_id={companyID} and sheet_id={sheetID};";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            await CwLog.Save(companyID, rederID, null, "Voucher", $"OK: [cancel approve vo ] sheet_id: {sheetID},  cancel_approver_id: {rederID}, cancelTime:{cancelTime + 1} ", cmd);

            CInfoForApproveBase info = await Load(cmd, companyID, sheetID, false);
            OperID = rederID;
            await OnSheetIDGot(cmd, sheetID, info, VoucherOperType.CancelApprove);
            if (info.ErrMsg != "")
            {
                if (bAutoCommit && tran != null) 
                {
                    tran.Rollback();
                    await RedisHelper.ExpireAsync(redisKey, 5);//tran内判重延迟结束
                }
                return info.ErrMsg;
            }
            if (bAutoCommit && tran != null)
            {
                tran.Commit();
                await RedisHelper.DelAsync(redisKey);//tran内判重立刻结束
            }
            return "";
        }

        public async Task<string> CheckBeforeCancelApproveVoucher(string companyID, string sheetID, dynamic cw_voucher_main, CMySbCommand cmd)
        {
            if (sheetID == null || sheetID == "") return "单号不存在";
            if (cw_voucher_main == null) return "凭证不存在";
            string msg = await CwVoucherController.CheckVoTime(companyID, Convert.ToDateTime(cw_voucher_main.happen_time), cmd);
            if (msg != "") return msg;
            if (cw_voucher_main.approve_time == null || cw_voucher_main.approve_time == "") return "该凭证未审核";
            if (cw_voucher_main.red_flag != null && cw_voucher_main.red_flag!="") return "红冲凭证不能取消审核";

            return "";
        }


        public async Task LoadCompanySetting(CMySbCommand cmd)
        {
            var sql = @$"select setting->>'companyName' as company_name,setting->>'contactTel' as company_tel,setting->>'companyAddress' as company_address from company_setting where company_id={company_id}";
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (setting != null)
            {
                company_name = setting.company_name;
                company_tel = setting.company_tel;
                company_address = setting.company_address;
            }
        }

        #region 追加备注
        public async virtual Task<string> AppendBrief(CMySbCommand cmd, string companyID, string sheetID, string newBrief, bool bAutoCommit = true)
        {
            string sError = "";
            if (sheetID == "")
            {
                sError = "删除必须指定单据号"; 
                return sError;
            }
            
            string sql = "";
            if (newBrief != null && newBrief != "")
            {

                //if (oldBrief != null && oldBrief != "") newBrief += ',' + newBrief;
                // make_brief += newBrief;
                //if (make_brief != oldBrief)
                 sql = $"update {MainTable} set make_brief= coalesce(make_brief,'') || ' {newBrief}' where company_id = {companyID} and sheet_id = {sheetID}";
            }
            cmd.CommandText = sql;
            CMySbTransaction tran = null;
            try
            {
                if (bAutoCommit)
                    tran = cmd.Connection.BeginTransaction();

                await cmd.ExecuteNonQueryAsync();

                if (tran != null) tran.Commit();
            }
            catch (Exception e)
            {
                if (tran != null)
                    tran.Rollback();
                return e.Message;
            }
            return sError;
        }
        #endregion
 



        public struct GetSheetsUsage 
        {
            public bool GetSumSheet;
            public bool GetEachSheet;
            public bool GetSmallUnitSheet;
            public bool GetBigUnitSumSheet;
            public bool ForPrint;
            public bool SplitUnitRows;
            public string OptionToRemember; 

        }
        public class GetSheetsResult<TSheet> 
        {
            public class SheetGroup
            {
                public dynamic template;
                public List<TSheet> sheets=new List<TSheet>();
            }
            public string result="";
            public string msg="";
            public string operName = "";
            public List<SheetGroup> sheetGroup=new List<SheetGroup>();
            public Dictionary<string, string> templVariables = new Dictionary<string, string>();
            public string sheetIDs = "";
            public List<ExpandoObject> cloudPrinters = new List<ExpandoObject>();
        }
        public static Type typeFromName(string typeName)
        {
            Type type = null;
            Assembly[] assemblyArray = AppDomain.CurrentDomain.GetAssemblies();
            int assemblyArrayLength = assemblyArray.Length;
            for (int i = 0; i < assemblyArrayLength; ++i)
            {
                type = assemblyArray[i].GetType(typeName);
                if (type != null)
                {
                    return type;
                }
            }

            for (int i = 0; (i < assemblyArrayLength); ++i)
            {
                Type[] typeArray = assemblyArray[i].GetTypes();
                int typeArrayLength = typeArray.Length;
                for (int j = 0; j < typeArrayLength; ++j)
                {
                    if (typeArray[j].Name.Equals(typeName))
                    {
                        return typeArray[j];
                    }
                }
            }
            return type;
        }
        public virtual List<CROW> MergeSheetRows(List<CROW> rows,bool bIgnoreNativeQty=false)  
        {
            return null;

        }
        private static T DeepCopy<T>(T obj)
        {
            var type = obj.GetType();
            var o = Activator.CreateInstance(type);
            var pi = type.GetProperties();
            foreach (var p in pi)
            {
                if (p.CanWrite)
                    p.SetValue(o, p.GetValue(obj));
            }
            return (T)o;
        }

        private string SqlToExecute = "";
        protected void AddExecSQL(string addSql)
        {
            if (!addSql.EndsWith(";")) throw new Exception("sql should end with ;"); 
            SqlToExecute += addSql;
        }
        protected string GetExecSQL()
        {
            string sql = SqlToExecute;
            SqlToExecute = "";
            return sql ;
        }
        protected void ClearExecSQL()
        { 
            SqlToExecute = ""; 
        }

    }
    

}
