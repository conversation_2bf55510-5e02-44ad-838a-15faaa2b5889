
@page
@model ArtisanManage.Pages.Report.ArrearsSheetDetailModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>

    <script type="text/javascript">
           window.g_operKey = '@Html.Raw(Model.OperKey)'; 

    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
            
                $("#gridItems").on("cellclick", function (event) {
                    // event arguments.
                    var args = event.args;
                    console.log(args.row);
                    if (args.datafield == "x_sheet_no") { 
                        var x_sheet_id = args.row.bounddata.x_sheet_id;
                        var sheet_type = args.row.bounddata.sheet_type;
                        //var forPayOrGet = false
                        //if (sheet_type == "X") 
                       // rowIndex = args.rowindex;
                   
                        //var sheet_type=$('#gridItems').jqxGrid('getcellvalue', rowIndex, 'sheet_type');
                        window.parent.newTabPage(sheet_type, `Sheets/SaleSheet?sheet_id=${x_sheet_id}`,window); 
                    } 
                    if (args.datafield == "sheet_no") { 
                        var sheet_id = args.row.bounddata.sheet_id;
                        var sheet_type = "收款单";
                        //var forPayOrGet = false
                        //if (sheet_type == "X") 
                       // rowIndex = args.rowindex;
                   
                        //var sheet_type=$('#gridItems').jqxGrid('getcellvalue', rowIndex, 'sheet_type');
                        window.parent.newTabPage(sheet_type, `Sheets/GetArrearsSheet?sheet_id=${sheet_id}`,window); 
                    }
                });

                $("#gridItems").jqxGrid('beforeRowRender', function (divRow, rowData) {
                    if (rowData.sheet_status == '已红冲') 
                        divRow.style.color='#aaa' 
                    else if (rowData.sheet_status == '红字单')
                        divRow.style.color='#f00'                  
                    else
                        divRow.style.color='#000'

                })

                $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                 
                QueryData();
            });

        function btnExportTable_click() {
            var bPrintEach = true, bPrintSum = false;

            var smallUnitBarcode = document.all.ckSmallUnitBarcode.checked;

            var checkedRows = window.g_checkedRows
            var sheetIDs = ''
            for (var id in checkedRows) {
                if (sheetIDs != '') sheetIDs += ","
                sheetIDs += id
            }
            if (!sheetIDs) {
                bw.toast('请至少勾选一个单据')
                return
            }
            $.ajax({
                url: '/api/GetArrearSheetView/GetMultiSheetsToPrint',
                type: 'GET',
                contentType: 'application/json',
                // data: JSON.stringify({sheet_id:'1',supcust_id:'2'}),
                data: {
                    operKey: g_operKey, sheetIDs: sheetIDs, bPrintSum: bPrintSum, bPrintEach: bPrintEach, smallUnitBarcode: smallUnitBarcode,
                    sortColumn: window.sortColumn || '', sortDirection: window.sortDirection || ''
                },
                success: function (result) {

                    if (result.result === 'OK') {
                        vm.templeSheets = result.sheetGroup;
                        vm.$nextTick(function () {
                            $('#divExportExcel .data-label').css('text-align', 'right')
                            $('#divExportExcel .data-value').css('text-align', 'left')
                            $('#divExportExcel .sheet-table').css('border', '2px solid #f6f0f0');//.css('background-color','#f9f9f9')
                            $('#divExportExcel .sheet-table tbody td').css('border', '0.5px solid #777')//.css('mso-number-format','\@@')
                            $('#divExportExcel .sheet-table tr td:first-child').css('border-left-style', 'none').css('border-bottom-style', 'none').css('border-top-style', 'none').css('width', '20px')

                            table2excel("divExportExcel");
                        });
                    }
                    else {
                        bw.toast(result.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            })
        }
    </script>
</head>

<body style="overflow:hidden">
 
    <div style="display:flex;padding-top:20px;">

        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnPrintSheets" onmouseenter="btnPrintSheets_hover()"  onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>
   
    <div id="gridItems" style="margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 95px);"></div>
    <div id="divRowCount"><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>
  

    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>