@page
@model ArtisanManage.Pages.BaseInfo.SalesDetailModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var m_db_id = "10";

    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
                $("#popoverQuery").jqxPopover({ showArrow: false, autoClose: true, offset: { left: 0, top: -10 }, position: "bottom", title: "", showCloseButton: false, selector: $("#btnQueryChooseSheets") });
                //$("#gridItems").on("cellclick", function (event) {
                //    var args = event.args;
                //});
                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                    if (args.datafield == "sheet_no") {
                        var sheet_id = args.row.bounddata.sheet_id;
                        var sheet_type = args.row.bounddata.sheet_type;
                        var placeholder_sheet_id = args.row.bounddata.placeholder_sheet_id;
                        debugger
                        console.log(placeholder_sheet_id)
                        //var sheetType = $('#sheetType').val();
                    var sheetType = sheet_type
                        debugger
                    console.log(sheetType)
                        var sheet = 'SaleSheet';
                        if (sheetType == 'XD') {
                            if (!placeholder_sheet_id) {
                                sheet = 'SaleOrderSheet'
                                sheetType = '销售订单'
                            } else {
                                sheet = 'PlaceholderOrderSheet'
                                sheetType = '占位单'
                                sheet_id = placeholder_sheet_id
                            }
                        }
                        else if (sheetType == 'X') sheetType = '销售单'
                        else if (sheetType == 'T') sheetType = '退货单'
                        else if (sheetType == 'TD') {
                            sheetType = '退货订单'
                            sheet = 'SaleOrderSheet'
                        }                        
                        var win = window.parent
                        if(!win.newTabPage) win=win.parent
                        win.newTabPage(sheetType, `Sheets/${sheet}?sheet_id=${sheet_id}`);
                    }
                });
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
                $('#supcust_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popClient').jqxWindow('open');
                        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&multiSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                $('#item_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popItem').jqxWindow('open');
                        $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                QueryData();
            });
        function btnQuerySaleSummaryByClient_click() {
             
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（客户）", 'Report/SalesSummaryByClient' + queryItemStr, window);
            var len = $('#jqxTabs').jqxTabs('length');
            var content = $('#jqxTabs').jqxTabs('getContentAt', len - 1);
            var frame = content.childNodes[0];
            var w = frame.contentWindow;
            w.g_bRelatedReport_sale = true
            window.g_bRelatedReport_sale = true

        }
        function btnQuerySaleSummaryByItem_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（商品）", 'Report/SalesSummaryByItem' + queryItemStr, window);
        }
        function btnQuerySaleSummaryBySeller_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（业务员）", 'Report/SalesSummaryBySeller' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByBrand_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（品牌）", 'Report/SalesSummaryByBrand' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByRegion_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（片区）", 'Report/SalesSummaryByRegion' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByGroup_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（渠道）", 'Report/SalesSummaryByGroup' + queryItemStr, window);
        }

        function remarkRender(row, column, value, p4, p5, rowData) {
            var remark = rowData.remark;
            if ( remark) {
                var div =
                    `<div onmouseenter='onMouseEnterRemark(event,${row},"${remark}")' onmousedown='onMouseLeaveRemark()' onmouseleave='onMouseLeaveRemark()'  style = "height:100%;margin-right:6px;text-align:right;line-height:28px;">${value}</div>`
                    return div;
            }
        }
        function briefRender(row, column, value, p4, p5, rowData) {
            var make_brief = rowData.make_brief;
            if (make_brief) {
                var div =
                    `<div onmouseenter='onMouseEnterRemark(event,${row},"${make_brief}")' onmousedown='onMouseLeaveRemark()' onmouseleave='onMouseLeaveRemark()'  style = "height:100%;margin-right:6px;text-align:right;line-height:28px;">${value}</div>`
                return div;
            }
        }
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                }
                else if (rs.data.action === "selectMulti") {
                    var rows = rs.data.checkedRows
                    var supcusts_id = '', supcusts_name = ''
                    rows.forEach(function (row) {
                        if (supcusts_id != '') supcusts_id += ','
                        supcusts_id += row.supcust_id
                        if (supcusts_name != '') supcusts_name += ','
                        supcusts_name += row.sup_name
                    })
                    $('#supcust_id').jqxInput('val', { value: supcusts_id, label: supcusts_name });
                }
                $('#popClient').jqxWindow('close');
            }
            else if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "selectMulti") {
                    if (rs.data.checkedRows.length == 1) {
                        var item_id = rs.data.checkedRows[0].item_id;
                        var item_name = rs.data.checkedRows[0].item_name;
                    }

                    var rows = rs.data.checkedRows
                    var items_id = ''
                    var items_name = ''
                    rows.forEach(function (row) {
                        if (items_id != '') {
                            items_id += ',';
                        }

                            items_id += row.item_id;
                            items_name += row.item_name+';';

                    })
                    $('#item_id').jqxInput('val', { value: items_id, label: items_name });

                    //$.ajax({
                    //    url: '/api/SaleSheet/GetItemInfo',
                    //    type: 'GET',
                    //    contentType: 'application/json',
                    //    data: { operKey: g_operKey, item_id: items_id },
                    //    success: function (data) {
                    //        if (data.result === 'OK') {
                    //            if (!window.g_queriedItems) window.g_queriedItems = {};
                    //            window.g_queriedItems[item_id] = data.item;
                    //        }
                    //    }
                    //});
                }

                $('#popItem').jqxWindow('close');
            }

        });

    </script>
</head>

<body>
    <style>
        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }
    </style>
    <div style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">

            <div style="float:none;height:0px; clear:both;"></div>

        </div>

        <button onclick="QueryData()" style="margin-right:0px;margin-top:30px;border-radius: 3px 0px 0px 3px">查询</button>

        <button id="btnQueryChooseSheets" class="btnright" style="width:30px;margin-left:0px;margin-top:30px;margin-left:0px;border-radius: 0px 3px 3px 0px">
            <img src="~/PrintTemplate/img/triangle.svg" style="margin-top: -1px; width: 14px; display: inline-block;vertical-align: middle;" />
        </button>
        <div id="popoverQuery" style="position:absolute;display:none;border-radius: 5px 5px 5px 5px">
            <div style="width:150px;height:200px;display:flex;flex-direction:column;justify-content:space-between;align-items:center;">
                <ul style="line-height: 32px;font-size:15px;padding:0px;">
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a id="btnshow" value="Show" onclick="btnQuerySaleSummaryByClient_click();">
                            销售汇总（客户）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByItem_click();">
                            销售汇总（商品）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryBySeller_click();">
                            销售汇总（业务员）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByBrand_click();">
                            销售汇总（品牌）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByRegion_click();">
                            销售汇总（片区）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByGroup_click();">
                            销售汇总（渠道）
                        </a>
                    </li>
                </ul>
                <div id="divClientVersion"></div>
            </div>
        </div>

        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;margin-top:30px;">导出</button>

    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>
    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择商品</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>