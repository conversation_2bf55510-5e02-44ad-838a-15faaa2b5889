@page
@model ArtisanManage.Pages.BaseInfo.ClientValueModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    @*<link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" type="text/css"/>*@
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
    	    var newCount = 1;
    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
                 QueryData();
               
                $("#gridItems").on("cellclick", function (event) {

                    var args = event.args;
                    var supcust_id = args.row.bounddata.supcust_id;
                    var sup_name = args.row.bounddata.sup_name;
                    var startDay = $('#startDay').jqxDateTimeInput('val');
                    var endDay = $('#endDay').jqxDateTimeInput('val');
                    var brand_id = $('#brand_id').val().value;
                     var brand_name = $('#brand_id').val().label;
                    var seller_id = $('#seller_id').val().value;
                    var seller_name = $('#seller_id').val().label;
                    
                    var url =""
                    var title =""
                    if (args.datafield == "sup_name" && sup_name) {
                        url = `Report/CustomerLiveness?&supcust_id=${supcust_id}&sup_name=${sup_name}`;
                       title="客户活跃度"
                        window.parent.newTabPage(title, `${url}`);
                    }
                     if (args.datafield == "item_num" && sup_name) {
                         url = `Report/SalesSummaryByItem?&supcust_id=${supcust_id}&sup_name=${sup_name}&startDay=${startDay}&endDay=${endDay}`;
                        title="销售汇总（商品）"
                         if (brand_id) url += `&brand_id=${brand_id}&brand_name=${brand_name}`;
                         if (seller_id) url += `&seller_id=${seller_id}&seller_name=${seller_name}`;
                        window.parent.newTabPage(title, `${url}`);
                    }
                     if (args.datafield == "sheet_num" && sup_name) {
                         url = `Report/SalesDetail?&supcust_id=${supcust_id}&sup_name=${sup_name}&startDay=${startDay}&endDay=${endDay}`;
                        title="销售明细表"
                         if (brand_id) url += `&brand_id=${brand_id}&brand_name=${brand_name}`;
                         if (seller_id) url += `&seller_id=${seller_id}&seller_name=${seller_name}`;
                        window.parent.newTabPage(title, `${url}`);
                     }  
                      
                });
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
                $('#supcust_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popClient').jqxWindow('open');
                        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });



              
         });
          window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                    $.ajax({
                        url: '/api/SaleSheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: null },
                        success: function(data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });

                }
                $('#popClient').jqxWindow('close');
            }
            

        });
      
    </script>
</head>

<body>
    <style>
        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }
    </style>
    <div style="display:flex;padding-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-right:0px;margin-top:30px;border-radius: 3px 0px 0px 3px">查询</button>

        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;margin-top:30px;">导出</button>
    </div>


    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>

    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    

</body>
</html>