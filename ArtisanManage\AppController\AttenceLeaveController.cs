﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    [Route("AppApi/[controller]/[action]")]
    public class AttenceLeaveController : QueryController
    {

        public AttenceLeaveController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpPost]
        public async Task<JsonResult> Add([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            string sql = $@"INSERT INTO 
                attence_leave(leave_type,leave_reason,status,seller_id,create_time,start_date,end_date,company_id)
                VALUES({data.leave_type},'{data.leave_reason}',{(int)AttenceLeave.STATUS.AUDITING},{operID},
                    '{DateTime.Now.ToString()}','{data.start_date}','{data.end_date}',{companyID})";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new
            {
                result = "OK"
            });
        }
        /**
        * 员工端使用
        * */
        [HttpGet]
        public async Task<JsonResult> GetMyApplys(string operKey, string operID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $@"SELECT
               flow_id, leave_type,leave_reason,al.status,seller_id,iop.oper_name,create_time,start_date,end_date,al.company_id
                from attence_leave al
                left join info_operator iop on iop.oper_id=seller_id and iop.company_id = al.company_id
                where al.company_id={companyID} and seller_id={operID} order by create_time desc";
            cmd.CommandText = sql;
            dynamic data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return Json(new
            {
                result = "OK",
                data = data
            });
        }
        /**
         * 审核通过
         * */
        [HttpPost]
        public async Task<JsonResult> AuditOK([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID);
            string sql = $@" UPDATE attence_leave SET status={(int)AttenceLeave.STATUS.FINISHED} WHERE flow_id={data.flow_id} AND company_id={companyID} AND seller_id={data.seller_id} AND status={(int)AttenceLeave.STATUS.AUDITING}";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            string insertAttendanceSQL = "insert into attence_record(company_id,oper_id,start_time,end_time,status,attence_leave_id) values";
            DateTime curDate = Convert.ToDateTime(data.start_date);
            DateTime endDate = Convert.ToDateTime(data.end_date);
            List<string> leaveDays = new List<string>();
            while (curDate <= endDate)
            {
                leaveDays.Add(curDate.ToString("yyyy-MM-dd"));
                curDate = curDate.AddDays(1);
            }
            List<string> attendanceSQLValues = new List<string>();
            foreach (string leaveDay in leaveDays)
            {

                string attendanceSQLValue = @$"('{companyID}','{data.seller_id}','{leaveDay} 00:01','{leaveDay} 23:58','QJ','{data.flow_id}')";
                attendanceSQLValues.Add(attendanceSQLValue);
            }
            insertAttendanceSQL += string.Join(",", attendanceSQLValues);
            cmd.CommandText = insertAttendanceSQL;
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new
            {
                result = "OK"
            });
        }
        /**
        * 审核驳回
        * */
        [HttpPost]
        public async Task<JsonResult> AuditReject([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            string sql = $@" UPDATE attence_leave SET status={(int)AttenceLeave.STATUS.REJECTED} WHERE flow_id={data.flow_id} AND company_id={companyID}  AND status={(int)AttenceLeave.STATUS.AUDITING}";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new
            {
                result = "OK"
            });
        }
        /**
        * 申请撤回
        * */
        [HttpPost]
        public async Task<JsonResult> ApplyRecall([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            string sql = $@" UPDATE attence_leave SET status={(int)AttenceLeave.STATUS.WITHDRAW} WHERE flow_id={data.flow_id} AND company_id={companyID} AND seller_id={operID} AND status={(int)AttenceLeave.STATUS.AUDITING}";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new
            {
                result = "OK"
            });
        }
        /**
         * 老板端使用
         * */
        [HttpPost]
        public async Task<JsonResult> DeleteLeave([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            string sql = $@" UPDATE attence_leave SET status={(int)AttenceLeave.STATUS.WITHDRAW} WHERE flow_id={data.flow_id} AND company_id={companyID}  AND status={(int)AttenceLeave.STATUS.FINISHED};
            DELETE FROM attence_record WHERE attence_leave_id ={data.flow_id} AND company_id={companyID} ";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new
            {
                result = "OK"
            });
        }
        [HttpGet]
        public async Task<JsonResult> GetMyAudits(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $@"SELECT
                flow_id,leave_type,leave_reason,al.status,seller_id,iop.oper_name,create_time,start_date,end_date,al.company_id 
                from attence_leave al
                left join info_operator iop on iop.oper_id=seller_id and iop.company_id = al.company_id
                where al.company_id={companyID} order by create_time desc ";
            cmd.CommandText = sql;
            dynamic data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return Json(new
            {
                result = "OK",
                data = data
            });
        }
    }
}
