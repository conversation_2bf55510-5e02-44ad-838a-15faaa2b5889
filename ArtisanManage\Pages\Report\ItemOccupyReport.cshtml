﻿@page
@model ArtisanManage.Pages.Report.ItemsOccupyReportModel

@{
  Layout = null;
}

<!DOCTYPE html>
<html style="width: 100%;height: 100%;overflow: hidden">
<head>
  <title>单品铺市率</title>
  <script src="~/js/Vue.js"></script>
  <script>var commonVersion=@Model.Version</script>
  <script src="~/js/commonConfig.js?v=@Model.Version"></script>
  <style>
    iframe{
      width:100%;
      height:100%;
      margin-left:0;
      margin-right:0;
      border: 0;
      background-color: transparent;
    }
  </style>
</head>
<body style="width: 100%;height: 100%">
<div id="pages"  style="width: 100%;height: 100%">
  <iframe :src="iframeSrc"></iframe>
</div>
<script>
    var g_operKey = '@Model.OperKey';
</script>
<script>
  const app = new Vue({
    el: "#pages",
     data() {
        return {
          iframeSrc : pageRouter.itemOccupyReport.router + g_operKey+"&&type=item"

        }
      },
       mounted() {
          window.addEventListener('message', function (msg) {
             window.parent.newTabPage("销售汇总（客户）",`Report/SalesSummaryByClient?sheetType=x&item_id=${msg.data.params.itemId}&item_name=${msg.data.params.itemName}&startDay=${msg.data.params.startTime} 00:00&endDay=${msg.data.params.endTime} 23:59`);
          })
      }
  })
</script>
</body>
</html>