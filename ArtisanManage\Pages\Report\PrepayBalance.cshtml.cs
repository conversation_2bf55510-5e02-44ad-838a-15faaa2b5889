﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class PrepayBalanceModel : PageQueryModel
    { 
        public string SubType = "YF";
        public PrepayBalanceModel(CMySbCommand cmd, bool useMainDb=false) : base(Services.MenuId.prepayBalance)
        {
            if (useMainDb) this.Database = "";
            this.cmd = cmd;
            this.PageTitle = "预付款余额";
            QueryConditionIsAfterAnd = true;
            FuncDealCondition = (condi) =>
            {
                return condi.Replace("'", "''");
            };
            DataItems = new Dictionary<string, DataItem>()
            {
               {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, CompareOperator="=", QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
               {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, SqlFld="sm.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"sup_name",new DataItem(){FldArea="divHead",Title="客户名称", CompareOperator="like"}},
                 {"now_balance_min",new DataItem(){FldArea="divHead",Title="余额&ge;",ForQuery=false}},
                {"now_balance_max",new DataItem(){FldArea="divHead",Title="余额&le;",ForQuery=false}},
                //{"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入客户名称", SqlFld="sup_name",CompareOperator="like"}},
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,                     
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sup_name",     new DataItem(){Title="供应商",  Width="150" ,Pinned=true}},
                       //{"sheet_type",new DataItem(){Title="单据类型",Hidden=true } },
                       {"supcust_id",    new DataItem(){Title="客户编号",  Width="250",SqlFld="sc.supcust_id",HideOnLoad=true,Hidden=true}},
                       {"prepay_subname",  new DataItem(){Title="预付款账户", Width="70",HideTopGroupName=true,
                           FuncGetSubColumns = async (col) =>
                           {
                                ColumnsResult result=new ColumnsResult();
                                Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>();
                                List<System.Dynamic.ExpandoObject> payways =  await CDbDealer.GetRecordsFromSQLAsync($"select sub_id,sub_name from cw_subject where company_id={company_id} and sub_type = '{this.SubType}';",cmd);

                                string crossTable1="",crossTable2="";
                                string totalF1="",totalF2="",totalF3="";
                               if (payways.Count == 0)
                               {
                                   return result;
                               }
                                foreach(dynamic pw in payways)
                                { string colKey="";
                                   string sub_name='a'+pw.sub_name;
                                   DataItem subcol;
                                   subcol =new DataItem{
                                       Title="期初",Width=col.Width,SubMumTitle=pw.sub_name,ShowSum=true,
                                       SqlFld=$"( ((a{(string) pw.sub_name}->>'f3')::numeric) -  (({sub_name}->>'f1')::numeric) + ((a{(string) pw.sub_name}->>'f2')::numeric) )"
                                   };
                                   colKey="pw_start_"+ (string) pw.sub_id;
                                   subColumns.Add(colKey, subcol);

                                   subcol =new DataItem{
                                       Title="增",Width=col.Width,SubMumTitle=pw.sub_name,ShowSum=true,Linkable = true,
                                       SqlFld=$" ({sub_name}->>'f1')::numeric"
                                   };
                                   colKey="pw_add_"+ (string) pw.sub_id;
                                   subColumns.Add(colKey, subcol);

                                   if(totalF1!="") totalF1+="+"; totalF1+=$" COALESCE(({sub_name}->>'f1')::float,0)";
                                   if(totalF2!="") totalF2+="+"; totalF2+=$" COALESCE(({sub_name}->>'f2')::float,0)";
                                   if(totalF3!="") totalF3+="+"; totalF3+=$" COALESCE(({sub_name}->>'f3')::float,0)";             

                                   subcol =new DataItem{
                                       Title="减",  Width=col.Width,SubMumTitle=pw.sub_name,ShowSum=true,Linkable = true,
                                       SqlFld=$" (a{(string) pw.sub_name}->>'f2')::numeric"
                                    };
                                   colKey="pw_reduce_"+ (string) pw.sub_id;
                                   subColumns.Add(colKey, subcol);

                                   subcol =new DataItem{
                                       Title="余额",Width=col.Width,SubMumTitle=pw.sub_name,ShowSum=true,
                                       SqlFld=$"(a{(string) pw.sub_name}->>'f3')::numeric"
                                   };
                                   colKey="pw_balance_"+ (string) pw.sub_id;
                                   subColumns.Add(colKey, subcol);


                                   if(crossTable1!="") crossTable1+=",";
                                   crossTable1+=$"('{pw.sub_name}'::text)";
                                    if(crossTable2!="") crossTable2+=",";
                                   crossTable2+=$"{sub_name} jsonb";

                                }

                                { string colKey="";
                                   string sub_name="总计";
                                   DataItem subcol; string fld;

                                   string totalStart="";
                                   if (totalF3.IsValid())
                                   {
                                       totalStart+=$"({totalF3})";
                                   }
                                   else
                                   {
                                       totalStart+="0";
                                   }
                                   if (totalF1.IsValid())
                                   {
                                       totalStart+=$"-({totalF1})";
                                   }
                                   else
                                   {
                                       totalStart+="-0";
                                   }
                                   if (totalF2.IsValid())
                                   {
                                       totalStart+=$"+({totalF2})";
                                       // 2024.09.15
                                       // 这里期初应该加上“减”的金额，而不是减去，因为“减”是正数
                                   }
                                   else
                                   {
                                       totalStart+="+0";
                                   }
                                   totalStart=$"( {totalStart} )";

                                   subcol =new DataItem{
                                       Title="期初",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld =$"{totalStart}"
                                   };
                                   colKey="pw_start_all";
                                   subColumns.Add(colKey, subcol);

                                   subcol =new DataItem{
                                       Title="增",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld =$"{totalF1}"
                                   };
                                   colKey="pw_add_all";
                                   subColumns.Add(colKey, subcol);


                                   subcol =new DataItem{
                                       Title="减",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld=$"{totalF2}"
                                   };
                                   colKey="pw_reduce_all";
                                   subColumns.Add(colKey, subcol);


                                   subcol =new DataItem{
                                       Title="余额",Width=col.Width,SubMumTitle=sub_name,ShowSum=true,
                                       SqlFld=$"{totalF3}"
                                   };
                                   colKey="pw_balance_all";
                                   subColumns.Add(colKey, subcol);


                                   if(crossTable1!="") crossTable1+=",";
                                   crossTable1+=$"('{sub_name}'::text)";
                                   if(crossTable2!="") crossTable2+=",";
                                   crossTable2+=$"{sub_name} jsonb";
                                }



                                result.Columns=subColumns;
                                SQLVariable1=crossTable1;
                                SQLVariable2=crossTable2;

                                return result;
                           }
                       }} 
                     },
                    /* QueryFromSQL=@"from crosstab('
                                    select sup_name,sub_name,row_to_json((add_amount,reduce_amount,now_balance)) as json from 
                                    (select isu.sup_name,pw.sub_name,sum(case when ah.change_amount>0 THEN ah.change_amount else 0 end) as add_amount,
                                    sum(case when ah.change_amount<0 THEN ah.change_amount else 0 end) as reduce_amount,pb.balance as now_balance
                                    from client_account_history as ah 
                                    left join info_supcust as isu on ah.supcust_id = isu.supcust_id
                                    left join cw_subject as pw on pw.sub_id = ah.sub_id
                                    left join sheet_prepay as sp on sp.sheet_id = ah.sheet_id
                                    left join prepay_balance as pb on (pb.supcust_id = ah.supcust_id and pb.sub_id = ah.sub_id)
                                    left join info_operator as io on io.oper_id` = sp.maker_id where ah.company_id=~COMPANY_ID and ah.sub_type = ''YF'' and ah.red_flag is null ~QUERY_CONDITION
                                    group by ah.supcust_id,isu.sup_name,pw.sub_name,pb.balance order by sup_name,sub_name ) t ',
                                    $$values ~SQL_VARIABLE1 $$) as tb_result(sup_name text, ~SQL_VARIABLE2)",
                     */


                     //不要改reduce_amount的方向！会影响财务！！
                     //请保持PrepayBalanceModel和PregetBalanceModel中reduce_amount方向一致！！
                    QueryFromSQL=@"
from crosstab('
   select supcust_id,sub_name,row_to_json((add_amount,reduce_amount,now_balance)) as json from 
   (
       SELECT
        pb.supcust_id,
        cw.sub_name,
        SUM ( CASE WHEN cah.change_amount > 0 and cw.sub_type = ''YF'' AND red_flag IS NULL THEN cah.change_amount ELSE 0 END ) AS add_amount,
        SUM ( CASE WHEN cah.change_amount < 0 and cw.sub_type = ''YF'' AND red_flag IS NULL THEN -cah.change_amount ELSE 0 END ) AS reduce_amount,
        balance now_balance
        from prepay_balance pb 
        left join client_account_history cah on pb.supcust_id=cah.supcust_id and pb.sub_id = cah.sub_id and pb.company_id = cah.company_id and happen_time >= ''~VAR_startDay'' AND happen_time <= ''~VAR_endDay'' 
        LEFT JOIN cw_subject AS cw ON cw.sub_id = pb.sub_id   and pb.company_id = cw.company_id       
        LEFT JOIN info_supcust AS sc ON pb.supcust_id = sc.supcust_id and sc.company_id =~COMPANY_ID
        where pb.company_id = ~COMPANY_ID and cw.sub_type=''YF'' ~QUERY_CONDITION ~VAR_now_balance_min ~VAR_now_balance_max
        GROUP BY 
        pb.supcust_id,pb.sub_id,balance,sub_name
        ORDER BY
        pb.supcust_id
    ) t ',
    $$values ~SQL_VARIABLE1 $$) as tb_result(supcust_id integer, ~SQL_VARIABLE2)  
    LEFT JOIN info_supcust AS sc ON tb_result.supcust_id = sc.supcust_id and sc.company_id =~COMPANY_ID
",
                     QueryGroupBySQL = "",
                     QueryOrderSQL=""
                  }
                } 
            };             
        }
        public async Task OnGet()
        { 
            await InitGet(cmd);
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;
            if (DataItems["now_balance_min"].Value != "")
            {
                SQLVariables["now_balance_min"] = "and balance >=" + DataItems["now_balance_min"].Value;
            }
            else
            {
                SQLVariables["now_balance_min"] = " ";
            }
            if (DataItems["now_balance_max"].Value != "")
            {
                SQLVariables["now_balance_max"] = "and balance <=" + DataItems["now_balance_max"].Value;
            }
            else
            {
                SQLVariables["now_balance_max"] = " ";
            }
        }
    }



    [Route("api/[controller]/[action]")]
    public class PrepayBalanceController : QueryController
    { 
        public PrepayBalanceController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            PrepayBalanceModel model = new PrepayBalanceModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            PrepayBalanceModel model = new PrepayBalanceModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            PrepayBalanceModel model = new PrepayBalanceModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
