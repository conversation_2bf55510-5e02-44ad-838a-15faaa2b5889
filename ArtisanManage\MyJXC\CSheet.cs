﻿using System;
using System.Collections.Generic;
using System.Collections;
using System.Text;
 
using System.Drawing;
 
using ArtisanManage.Models;
using System.Xml;
using System.Runtime.CompilerServices;
using ArtisanManage.MyJXC;

namespace myJXC
{
    public enum SHEET_TYPE 
    { 
       [DBValue("X")] SHEET_SALE,
       [DBValue("T")] SHEET_SALE_RETURN, 
       [DBValue("XD")] SHEET_SALE_DD,
       [DBValue("TD")] SHEET_SALE_DD_RETURN,
       [DBValue("C")] SHEET_BUY,
       [DBValue("CT")] SHEET_BUY_RETURN,
       [DBValue("CD")] SHEET_BUY_DD,
       [DBValue("CTD")] SHEET_BUY_DD_RETURN,
       [DBValue("DB")] SHEET_MOVE_STORE,
       [DBValue("SK")] SHEET_GET_MONEY,
       [DBValue("FK")] SHEET_PAY_MONEY,
       [DBValue("YS")] SHEET_PRE_GET_MONEY,
       [DBValue("YS")] SHEET_PRE_PAY_MONEY,
       [DBValue("ZH")] SHEET_COMBINE_GOOD,
       [DBValue("CF")] SHEET_SPLIT_GOOD,
       [DBValue("CK")] SHEET_STORE_OUT,
       [DBValue("RK")] SHEET_STORE_IN,

       [DBValue("PD")] SHEET_INVENT_INPUT,
       [DBValue("YK")] SHEET_INVENT_CONFIRM,
       [DBValue("BS")] SHEET_INVENT_REDUCE,

       [DBValue("ZC")] SHEET_FEE_OUT,
       [DBValue("SR")] SHEET_FEE_IN,
        EMPTY
    }
    public class CSize
    {
        public string size_id = "";
        public string size_name = "";
        public int size_index = -1;
        public string size_group = "";
        public string barcode = "";
        public double qty = 0;
    }
    public class SheetRow
    {
        public string item_no="";
        public string item_name="";
        public string item_model = "";
        public string item_subno="";
        public string item_brand = "";
        public string item_class = "";
        public string item_size = "";
        public string item_sn = "";
        public string unit_no="";
        public double unit_factor=1;
 
        public string size_name="";
        public string size_id="0";
        public string size_code = "";
        public string size_group="";
        public string color_name="";
        public string color_id="0";
        public string color_code = "";
        public string py_str = "";
        public string s_orig_price="";
        public string s_real_price="";
        public string s_add_price = "";
        public double orig_price = 0;
        public double real_price = 0;


        public double money=0;
        public string in_amount = "";
        public string cost_amount = "";
        public string base_amount = "";
        public string sale_amount = "";
       
        public double quantity;
        public double pend_qty = 0;
        public double pend_qty_d = 0;
        public double now_pend_qty = 0;
        public double now_pend_qty_d = 0;
        public double sent_qty = 0;
        public double orig_sht_qty = 0;
        public double discount=1;
        public double tax_amount = 0;
        public string son_nos;
        public double new_qty = -1; public double new_price =-1;
        public string s_quantity;
        public string from_steelyard = "";//1 indicate from steelyard
        public string unit_no1 = "";
        public string quantity1="";
        public string branch_no = "";
        public string branch_name = "";
        public string branch_position_id = "0";
        public string branch_position_name = "";
        public string batch_id = "0";
        public string batch_name = "";
        public string from_red_flag = "";
        public string worker_id = "";
        public string worker_name = "";
        public string servicer_id = ""; public string servicer_name = "";
        public string servicer1_id = ""; public string servicer1_name = "";
        public string combine_flag = "";
        public string father_no = "";
        public bool bCombineFlagAutoSet = false;
        public string combine_price = "";
        public string is_temp = "";
        public string brief = ""; public string make_way = ""; public string return_reason = ""; public string norm_status = ""; public string norm_need_confirm = "";
        public int inout_flag = -1; public string row_flag = ""; public string first_spec_qty = ""; public double add_qty = 0; public string approved = "";
        public string update_flag = "";
        public string need_update = "";
        public int sht_row_index = -1;
        public bool is_service = false;
        public bool bNeedProduceDate = false;
        public bool bNewForProduceDate = false;
        public bool bItemBranchIgnoreBatch = false;
       
      //  public object other_fields = null;

        public bool bIgnoreStoreApprove = false;
        public bool bStoreApproved = false;
#pragma warning disable CS1591 // 缺少对公共可见类型或成员的 XML 注释
        public bool bNewRow = false;
#pragma warning restore CS1591 // 缺少对公共可见类型或成员的 XML 注释
        public int nRowIndex = -1;
        public string sale_flow_id = "";
        public string sn = "";
        public string repair_years = "";
        public string negative_till = "";
        public string tables_no = "";
        public string renling_flag = "";
        public string orig_flow_id = "";
        public SheetRow origSheetRow = null;
        public string up_style = "";
        public string worker_must="";
        public string sign_oper = "";
        public bool bService = false;
        public string s_stock_qty = "";
        public string s_can_sell_qty = "";
        public string show_class_name = "";
        public string box_no_start = "";
        public string box_no_end = "";
        public string min_fee_qty = "";
        public string orig_sheet_no = "";
        //public virtual string GetPrintItemValue(string fld)
        //{
        //    return "";
        //}
        //public double store_quantity;
        //public double box_price;
        //public double tax;
        //public string is_tax;
         public double cost_price_avg =0;
         public double cost_price_prop =0;
         public double cost_price_recent = 0;
        public double cost_price_used = 0;
         public double cost_amt_all = 0;
         public double cost_type = 0;
        public double cost_change_qty = 0;
        public bool bPrintChef = false;
        public bool bIgnorePrintChef = false;
        public bool bNotNeedPrintChef = false;
        public string ChefPrintFlagByItem = "";
        public string ChefPrintFlagByClass1 = "";
        public string ChefPrintFlagByClass2 = "";
        public string ChefPrintFlagByClass3 = "";
        public string ChefPrintFlagByClass4 = "";
        public string flow_id = "";

        public string clsno1="";
        public string clsno2="";
        public string clsno3="";
        public string clsno4="";
        public string clsname1="";
        public string clsname2="";
        public string clsname3="";
        public string clsname4="";

        public int clsindex1=0;
        public int clsindex2=0;
        public int clsindex3=0;
        public int clsindex4=0;
        public int rpt_clsindex = 0;
        public int ClassIndex=0;
        public string ClassID = "";
        public string ClassName="";
        public bool bShowClassSum = false;

        public double in_limit_qty=0;

        public double in_price = 0;
        public double base_price = 0;
        public double sale_price = 0;
        public double cost_price = 0;
        public bool bAddedToOtherRow = false;
        public bool bNotAddToTable = false;
        public string tablesID = ""; public string tablesName = ""; public string total_people = ""; public int table_count = 1;
        public string other1 = ""; public string other2 = ""; public string other3 = ""; public string other4 = "";
        public string do_room_id = "";
        public bool bRemoved = false;
        public bool bUsed = false;
        public double fill_money = 0;
        public double fill_integral = 0;
        public double packed_qty = 0;
        public List<CSize> lstSize = null;
        public bool bMainRow = false;
        public string rpt_class = "";

        public string item_season = "";
        public string item_year = "";
        public string other_class = "";
        public string service_flag = "";
        public double vip_price = -1;
        public double vip_price1 = -1;
        public double vip_price2 = -1;
        public string min_sale_price = "";
        public string allow_disc = "";
        public SheetRow colonMe()
        {
            SheetRow newrow = new SheetRow();
            newrow.flow_id = flow_id;
            newrow.item_no=item_no;
            newrow.item_name = item_name;
            newrow.item_model = item_model;
            
            newrow.item_subno = item_subno;
            newrow.unit_no = unit_no;
            newrow.unit_factor = unit_factor;
            newrow.unit_no1 = unit_no1;
            newrow.quantity1 = quantity1;

            newrow.size_name = size_name;
            newrow.size_id = size_id;
            newrow.size_group = size_group;
            newrow.color_name = color_name;
            newrow.color_id = color_id;
            newrow.orig_price = orig_price;
            newrow.real_price = real_price;

            newrow.money = money;
            newrow.quantity = quantity;

             newrow.s_orig_price=s_orig_price;
             newrow.s_real_price=s_real_price;
             newrow.pend_qty=pend_qty;
             newrow.pend_qty_d=pend_qty_d;
             newrow.now_pend_qty=now_pend_qty;
            newrow.now_pend_qty=now_pend_qty;
            newrow.now_pend_qty_d=now_pend_qty_d;
            newrow.sent_qty=sent_qty;
            newrow.discount=discount;

            newrow.tax_amount = tax_amount;
            newrow.son_nos = son_nos;

            newrow.s_quantity = s_quantity;

            newrow.branch_no = branch_no;
            newrow.branch_name = branch_name;

            newrow.worker_id = worker_id;
            newrow.worker_name = worker_name;
            newrow.servicer_id = servicer_id;
            newrow.servicer_name = servicer_name;

            newrow.inout_flag = inout_flag;
            newrow.is_service = is_service;
           // public bool bNeedProduceDate = true;
           // public bool bNewForProduceDate = false;

           
            newrow.make_way = make_way;
            newrow.brief = brief;
            newrow.cost_price_avg =cost_price_avg;
            newrow.cost_price_prop =cost_price_prop;
            newrow.cost_price_recent = cost_price_recent;
            newrow.cost_price_used =cost_price_used;
            newrow.cost_amt_all = cost_amt_all;
            newrow.cost_type = cost_type;
            newrow.sale_price = sale_price;
            newrow.sale_amount = sale_amount;
            newrow.bItemBranchIgnoreBatch = bItemBranchIgnoreBatch;
            newrow.bCombineFlagAutoSet = this.bCombineFlagAutoSet;
            newrow.tables_no = this.tables_no;
            newrow.combine_flag = combine_flag;
            newrow.father_no =father_no;
            newrow.combine_price=combine_price;
            newrow.norm_status=norm_status;
            newrow.norm_need_confirm=norm_need_confirm;
            newrow.nRowIndex=nRowIndex;
            newrow.bPrintChef=bPrintChef;
            newrow.combine_price=combine_price;
            newrow.combine_price=combine_price;
            newrow.combine_price=combine_price;
            newrow.son_nos = son_nos;
            newrow.tax_amount = tax_amount;
            newrow.tablesID = tablesID;
            newrow.tablesName = tablesName;
            newrow.ClassName = ClassName;
            return newrow; 
             
        }
        public SheetRow WiseClone()
        {
            return (SheetRow) this.MemberwiseClone();
        }
        public static void OrderByItemColorSize(Dictionary<string,SheetRow> dicRows)
        {
            List<SheetRow> lstRows = new List<SheetRow>();
            foreach (KeyValuePair<string, SheetRow> di in dicRows)
            {
                SheetRow row = di.Value;
                lstRows.Add(row);
            }

            lstRows.Sort(delegate(SheetRow x, SheetRow y) 
            {
                if (x.item_no != y.item_no)
                      return y.item_no.CompareTo(x.item_no); 
                if (x.branch_no != y.branch_no)
                     return y.branch_no.CompareTo(x.branch_no); 
                if (x.branch_position_id != y.branch_position_id)
                     return y.branch_position_id.CompareTo(x.branch_position_id); 
                if (x.batch_id != y.batch_id)
                     return y.batch_id.CompareTo(x.batch_id); 
                if (x.color_id != y.color_id)
                     return y.color_id.CompareTo(x.color_id); 
               // if (x.size_id != y.size_id)
                return y.size_id.CompareTo(x.size_id);  
            
            });
            int nRow=1;
            dicRows.Clear();
            foreach (SheetRow row1 in lstRows)
            {
                dicRows.Add(nRow.ToString(), row1);
                nRow ++;
            }

        }

        public static void OrderByClass_Name(List<SheetRow> sheetRows)
        { 
            sheetRows.Sort(delegate(SheetRow x, SheetRow y)
            {
                if(x.tablesName!=y.tablesName)
                    return x.tablesName.CompareTo(y.tablesName);
                if (x.norm_status != y.norm_status)
                    return x.norm_status.CompareTo(y.norm_status);
                if (x.ClassIndex != y.ClassIndex)
                    return x.ClassIndex - y.ClassIndex;
                if (x.ClassName != y.ClassName)
                    return x.ClassName.CompareTo(y.ClassName);
               // if (x.item_name != y.item_name)
                return x.item_name.CompareTo(y.item_name);
            });
              
        }
    }

    public class CCombineSon
    {
        public string son_no = "";
        public string son_name = "";
        public string son_price = "";
        public string son_qty = "";
        public string son_unit = "";
        public string son_no1 = "";
        public string son_name1 = "";
        public string add_price1 = "";
        public string son_no2 = "";
        public string son_name2 = "";
        public string add_price2 = "";
        public string mum_id = "";
        public string mum_model = "";
        public string son_model = "";
        public int son_index = 0;
        public string defalt_flag = "0";
    }
     
    public class CPrintItem
    {
        public string name = "";
        public string friendlyName = "";
        public string title = "";
        public string text = "";
        public float x = 0;
        public float y = 0;
        public float x1 = 0;
        public float y1 = 0;
        public float width = 0;
        public float height = 0;
        public bool bShow = true;
        public bool ShowBarcodeText = true;
        public bool PrintTitle = true;
        public bool bInGrid = false;
        public string item_tail = "";
        public bool InAllPage = false;
        public bool BelowTable = false;

        public int location_style = 0;//0 fixed,1 below table
        public bool show_column_sum = false;
        public int format = 0;
        
        public float FontSize = 10;
        public string FontName = "宋体";

        public bool FontBold=false;
        public bool  FontItalic =false;
        public bool FontUnderline = false;

        public string show_index = "";
        public string view_type = "";
       // public bool bPrintedBelowTable = false;
       
   
         
    }
    public class CPrintPage
    {
        public int PageStartRow = 0;
        public int PageIndex = 0;
        public float HeightBelowTable = 0;
        public float TableTop = 0;
    }
    public class CSheet
    {         
    
        protected Dictionary<string, CPrintItem> m_PrintItems = new Dictionary<string, CPrintItem>();
        protected Dictionary<string, CPrintItem> m_PrintTableItems = new Dictionary<string, CPrintItem>();
        public bool m_bPrintingBySystem = false;
        public bool m_bPrintingFirstPage = false;
        public string company_id = "";
        public byte[] m_PrintBuf = null;
        public SHEET_TYPE m_SheetType;
        public SHEET_TYPE m_OrigSheetType;
        public bool m_bIsStoreSheet = false;
        public string m_sheet_no = ""; public string m_new_sheet_no = "";
        public string m_red_sheet_no = "";
        public string m_OrigSheetNo = ""; public string m_OrigOrigSheetNo = "";
        public string m_orig_oper_date = "";
        public bool m_bCanceled = false;
        public string m_from_red_sheet_no = "";
        public string m_clientMarkID = "";
        public bool m_bPrintChef = false;
        public bool m_bAccountTable = false; public bool m_bRealTable = false;
        public string m_com_no = ""; public string m_com_name = "";
        public string m_remote_com_no = ""; public string m_remote_com_name = "";
        public string m_branch_no="";
        public string m_branch_name = "";
        public string m_op_store = "";
        public string m_op_name = "";
        public bool m_bSheetChanged = false;
        public string m_supcust_no="";
        public string m_supcust_name = "";
        public string m_vip_batch_type = "";
        public string m_cust_gender = "";
        public string m_pay_way = "";
        public string m_pay_name = "";
        public string m_deposit_amt = "", m_deposit_payway="";
        public string m_play_hours = "";
        public string m_hotel_sheet_no = "";
        public string m_hotel_room_info = "";
        public string m_hotel_room_no = "";

        public string m_oper_id="";
        public string m_oper_name="";
        public string m_oper_pw = "";
        public string m_servicers_name = "";
        public string m_table_persons = "";
        public string m_approve_oper = ""; public string m_approve_oper_name = "";
        public string m_money_approver_id = "";
        public string m_money_approver_name = "";
        public string m_money_approve_time = "";


        public string m_branch_approver_id = "";
        public string m_branch_approver_name = "";
        public string m_branch_approve_time = "";

        public string m_work_man="";
        public string m_work_man_name="";
        public string m_work_man1 = "";
        public string m_work_man1_name = "";

        public string m_GoodSenderID = "";
        public string m_GoodSenderName = "";
        public string m_GoodGetterName = "";
        public string m_MoneyGetterID = "";
        public string m_MoneyGetterName = "";
        public bool m_bEverAddSheet = false;

        public double m_discount=1;
        public double m_total_amount;
        public double m_total_qty=0;
        public double m_paid_amount=0;
        public double m_total_paid_amount=0;

        public double m_disc_amt;
        public double m_order_amount = 0;
        public double m_prepay_left = 0;
        public DateTime m_oper_date;
        public DateTime m_work_date;       
        public string m_s_oper_date = "";
        public string m_account_day = "";
        public string m_coin_no = "RMB";
        public bool m_bApproved = false;
        public string m_approve_flag = "";
        public bool m_bNeedApprove = true;
        public bool m_bBackFromAccount = false;
        //public bool m_bNeedConfirm= true;
        public string m_store_approve_time = "";
        public string m_in_store_approve_time = "";
        public string m_store_approver = "";
        public string m_in_store_approver = "";
        public string m_approve_time = "";
        public string m_brief = "";
        public bool m_bOnPos = false;
        public string m_duty_no = "";
        public string m_cardTillDate = "";
        public bool m_bDisableNagativeStock = false;
        public string m_event = "";
        public string m_eventPerson = "";
        public string m_eventPersonAge ="";
        //public Dictionary<string, SheetRow> SheetRows = new Dictionary<string, SheetRow>();
        public List<SheetRow> SheetRows = new List<SheetRow>();
 
        private List<CPrintPage> m_PrintPages = new List<CPrintPage>();
        private int m_PrintCurPage = 0;
        //public int m_PageCount = 0;
        private int m_PrintPageStartRow = 0;
        private int m_PrintGridLeft = 0;
        private int m_PrintGridTop = 0;
        private bool m_PrintHaveEmptyRows = false;
        public int m_PrintSpecPage = -1;
        public int m_PrintTableHeight = 0;
        public int m_PrintWidth = 0;
        public int m_PrintHeight = 0;
        private bool m_PrintPrintGridLine = true;
        private float m_PrintPageBottomMargin = 20;
        private float m_PrintPageTopMargin=20;
        private float m_PrintPageLeftMargin = 0;
        private float m_PrintRowHeight = 20;
        public int m_PrintSizeStyle = 0;
        public int m_specPageWidth = 0;
        public int m_specPageHeight = 0;
        public bool m_bPDASheet = false;
        public string m_redFlag = "";
        public string m_tableStartTime="";
        public string m_tableLeaveTime= "";
        public string m_tablePlayHours = "";
        public string m_from_device_type = "";
        public bool m_bTakeOut = false;
        public string m_finish_time = "";
        //public string m_newPdaSheetNo = "";

        // for chef print 
        public string m_sheet_id = "";
        public string m_sheet_name = "";
        public int m_nTriedTimes = 0; public string m_lastTryTime = ""; public string m_firstTryTime = "";
       
        public string m_form_xml = ""; public string m_table_show_style = ""; public string m_not_show_other_class = ""; public string m_table_show_direction = ""; public bool m_bHave1Item = false; public int m_chefSheetWidth = 0;
        
        public string m_Result = ""; public string m_desc = ""; public bool m_bMarked = false;
        public string m_usedPrinterName = "";
        public int m_print_times = 1; public bool m_bAlert = false;
        public string m_print_times_mark_name = ""; public int m_nCurPrintTime = 1;

        public int m_print_interval = 0;
        public string m_prt_sheet_no = "";  
        public string m_print_all_items = ""; public string m_print_all_tables = "";

        public string m_pos_print_port = ""; public string m_pos_printer_model = ""; public string m_pos_printer_ip = ""; public string m_pos_printer_com_rate = "";

        public string m_cuicai_items_no = ""; public string m_cuicai_items_name = "";

        public string m_spec_prt_style = ""; public string m_spec_prt_name = ""; public string m_spec_prt_port = ""; public string m_spec_printer_com_rate = ""; public string m_spec_prt_model = "";

        public string m_prepay_fill_sub_id = ""; public string m_prepay_fill_sub_name = "";

        public string m_CardType = "";
        public string m_CardTypeName = "";
        public string m_RemoteCardTypeId = "";
        public string m_CardPwd = "";
        public double m_CardStoredMoney = 0;
        public double m_CardGiveMoney = 0;
        public double m_CardIntegral = 0;
        public string m_ToCardType = ""; public string m_ToCardTypeName = "";
        public string m_CardRemark = "";
        public string m_CardRemoveDigits = "";
        
        public string m_card_class = "";
        public string m_ic_card_money = ""; public string m_ic_card_give_money = "";
        public string m_ic_card_integral = "";
        public string m_ic_card_times = "";

        public bool m_bNormSheet = false; public string m_NormID = ""; public string m_NormName = ""; public string m_NormPrice = ""; public string m_NormFlowID = "";
        public bool m_bNormNeedConfirm = false;
        public string m_norm_confirm_time = ""; public string m_norm_confirm_brief = "";
        public bool m_bAdjustAllNorms = false; public bool m_bChangeNorms = false;
        public bool m_bSubmitOrderAsAddSheet = false;
        public string m_disc_scheme_name = ""; public string m_disc_scheme_no = "";
        public bool m_bNotMarkIcWriteRecord = false;
        public bool m_bOffLineSubmit = false;
        public string m_ReportPrintFlag = "";
        public string m_PosCompanyName = "";
        public string m_tb_master = "sheet_item_master";
        public string m_tb_detail = "sheet_item_detail";
        public string m_tb_add = "sheet_item_add";
        public string m_tb_table = "info_service_table";
        public string m_tb_area = "info_service_area";
        public string m_tb_order_detail = "sheet_order_detail";
        public bool m_bHaveBarcodeToPrint = false;
 
        public List<CField> lstGroupPurchaseFlds = null;
        public List<CField> lstPayWayFlds = null;
        public enum PRINT_USAGE
        {
            PRINT_CHEF=0,
            REPRINT_CHEF=1,
            PRINT_ACCOUNT=2,
            PRINT_SYD=3,
            PRINT_CANCEL_SYD = 4,
            PRINT_HISTORY =5,
            PRINT_JBD = 6,
            PRINT_ZCD = 7,
            PRINT_REPORT=8
        }
        public PRINT_USAGE m_print_usage = PRINT_USAGE.PRINT_CHEF;


        //for payment
        public double m_ship_amount = 0;
        public double m_pay_cash_amount = 0;
        public double m_pay_card_amount = 0;
        public double m_pay_ticket_amount = 0;
        public double m_pay_bank_amount = 0;
        public double m_pay_integral_amount = 0;
        public double m_pay_prepay_amount = 0;
        public double m_pay_hotel_amount = 0;
        public double m_left_amount = 0;
        public string m_pay_prepay_box_id = "";
        public int m_pay_reduce_integral = 0;
        public string m_pay_card_id = "";
        public string m_pay_bank_box_id = "";
        public string m_pay_bank_box_name = "";
        public string m_pay_cash_box_id = "";
        public string m_other_payway = "";
        public double m_pay_other_amount = 0;

        public string m_payway1_name = "";
        public double m_payway1_amount = 0;
        public string m_payway2_name = "";
        public double m_payway2_amount = 0;
        public string m_payway3_name = "";
        public double m_payway3_amount = 0;

        //public double m_pay_other1_amount = 0;
        //public double m_pay_other2_amount = 0;
        //public double m_pay_other3_amount = 0;
        //public double m_pay_other4_amount = 0;
        //public double m_pay_other5_amount = 0;
        //public double m_pay_other6_amount = 0;
        public double m_rpt_other1_amount = 0;
        public double m_rpt_other2_amount = 0;
        public double m_rpt_other3_amount = 0;
        public double m_rpt_other4_amount = 0;
        public double m_rpt_other5_amount = 0;
        public double m_rpt_other6_amount = 0;
        public string m_gp_site_name = "";
        public double m_gp_ticket_amount = 0; public double m_gp_ticket_sell_amount = 0; public double m_gp_setmeal_amount = 0;
        public string m_now_sheet_card_amount = "";
        public string m_now_sheet_give_amount = "";
        public int m_AddIntegral = 0;
        public string m_now_integral_amount = "";
        public string m_now_money_amount = "";
        public string m_now_give_amount = "";
        public string m_card_times_info = "";
        public double m_realGetAmt = 0;
        public double m_returnAmt = 0;
        public string m_lastOperDate = "";
        public string m_son_card_id = "";
        public double m_vip_disc_reduce = 0;
        //
        public enum SHEET_PRINT_PROGRESS
        {
            PROGRESS_IDLE = 0,
            PROGRESS_WAITING = 1,
            PROGRESS_PREPARE_DATA = 2,
            PROGRESS_CONNECTING = 3,
            PROGRESS_SENDING_DATA = 4,
            PROGRESS_PRINTING = 5,
            PROGRESS_CONNECT_ERROR = 6,
            PROGRESS_SEND_ERROR = 7
        }
        public SHEET_PRINT_PROGRESS m_PrintProgress = SHEET_PRINT_PROGRESS.PROGRESS_IDLE;

        public static List<CSheet> g_lstChefPrintSheet = new List<CSheet>();
       
        public string m_cust_addr = ""; public string m_cust_tel = ""; public string m_cust_contact = ""; public string m_send_time = ""; public bool m_bSendByTel = false;
        public string m_send_time_range = "";
        public double m_rpt_card_amt = 0;
        public double m_rpt_card_store_amt = 0;
        public string m_makeTime = "";
        public string m_shoppe_id = ""; public string m_shoppe_name = "";
        public Dictionary<string, List<CSize>> m_dicSizeGroup = new Dictionary<string, List<CSize>>();
        
        //

        public int GetPageCount()
        {
            return m_PrintPages.Count;
        }
        public CSheet()
        { 
     

        }
        public void SetSheetTableByBusinessType(string business_type)
        {
            if (business_type == "洗浴")
            {
                m_tb_master = "xiyu_sheet_item_master";
                m_tb_detail = "xiyu_sheet_item_detail";
                m_tb_add = "xiyu_sheet_item_add";
                m_tb_area = "xiyu_handcard_area";
                m_tb_table = "xiyu_handcards";
                m_tb_order_detail = "xiyu_sheet_order_detail";
            }
            else if (business_type == "KTV")
            {
                m_tb_master = "ktv_sheet_item_master";
                m_tb_detail = "ktv_sheet_item_detail";
                m_tb_add = "ktv_sheet_item_add";
                m_tb_area = "ktv_room_area";
                m_tb_table = "ktv_rooms";
                m_tb_order_detail = "ktv_sheet_order_detail";
            }
            else 
            {
                m_tb_master = "sheet_item_master";
                m_tb_detail = "sheet_item_detail";
                m_tb_add = "sheet_item_add";
                m_tb_area = "info_service_area";
                m_tb_table = "info_service_table";
                m_tb_order_detail = "sheet_order_detail";
            }
        }
       
       
        private string TrimStart(string stext,string cc)
        { 
                int hhl = 0;
                for (int nnn = 0; nnn < stext.Length; nnn++)
                {
                    string sss = stext.Substring(nnn, 1);
                    if (sss != cc)
                        break;
                    hhl++;
                }
            if(hhl>0)
            {
                string s=stext.Substring(hhl,stext.Length -hhl);
                return s;
            }
            return stext;
        } 
     
        public int m_sht_bottom_margin = 5;
        public int m_sht_top_margin = 5;
        public int m_sht_left_margin = 5;
        public static string StrFromSheetType(SHEET_TYPE sheetType)
        {
            System.Type type = sheetType.GetType(); 
            System.Reflection.FieldInfo fi = type.GetField(sheetType.ToString());
            DBValue[] attrs = fi.GetCustomAttributes(typeof(DBValue), false) as DBValue[];
            if (attrs.Length > 0)
            {
                var str = attrs[0].Value;
                return str;
            } 
            return "";
        }
        public SHEET_TYPE SheetTypeFromStr(string trans_flag)
        {
            System.Type type = typeof(SHEET_TYPE);
            foreach (var enumValue in Enum.GetValues(type))
            {
                System.Reflection.FieldInfo fi = type.GetField(enumValue.ToString());
                DBValue[] attrs = fi.GetCustomAttributes(typeof(DBValue), false) as DBValue[];
                if (attrs.Length > 0)
                {
                    string str = attrs[0].Value;
                    if (str == trans_flag)
                    {
                        return (SHEET_TYPE)enumValue;
                    }
                }
            } 
           
            return SHEET_TYPE.EMPTY;
            

        }
       
   
      
        public string SetGeneralInfo(string branch_no, string supcust_no, string pay_way, string discount, string total_amount, string paid_amount, string disc_amt, string oper_id, string work_man, string oper_date, string work_date)
        {
            return "";

        }
        public string AddRow(string item_no, string unit_no, string unit_factor, string orig_price, string real_price, string quantity, string money)
        {
            SheetRow row=new SheetRow();
            row.item_no=item_no;
            row.unit_no=unit_no;
            if(CPubVars.IsNumeric(unit_factor))
                row.unit_factor=Convert.ToDouble(unit_factor);
            if (CPubVars.IsNumeric(orig_price))
                row.orig_price=Convert.ToDouble(orig_price);
            if (CPubVars.IsNumeric(real_price))
                row.real_price =Convert.ToDouble(real_price);
            if (CPubVars.IsNumeric(quantity))
                 row.quantity=Convert.ToDouble(quantity);
             if (CPubVars.IsNumeric(money))
                 row.money=Convert.ToDouble(money);
            SheetRows.Add(row);
            return "";

        }
        public string DealPrepay(CMySbCommand cmd,string red_flag, string supcust_no,double pay_prepay_amount,string pay_prepay_box_id,bool bPayOrGet,ref double prepay_amt_left)
        {
            string strErr = ""; object ov = null;
            if (pay_prepay_amount != 0 && pay_prepay_box_id != "")
            {
                string ys_com_condi = "";
                if (company_id != "")
                {
                    ys_com_condi = " and com_no='" + company_id + "'";
                }

                int prepay_flg = 1;
                if (pay_prepay_box_id.Contains("2203"))
                {
                    if (bPayOrGet)
                    {
                        prepay_flg = -1;
                    }
                }
                else if (pay_prepay_box_id.Contains("1123"))
                {
                    if (!bPayOrGet)
                    {
                        prepay_flg = -1;
                    }
                }
                if (red_flag == "2") prepay_flg *= -1;

                double nAddPrePayAmt = -pay_prepay_amount * prepay_flg;
                //if (m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN || m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN)
                //    nAddPrePayAmt *= -1;
                if (red_flag == "2")
                    nAddPrePayAmt *= -1;
                string sAddAmt = "";
                if (nAddPrePayAmt > 0)
                    sAddAmt = "+" + nAddPrePayAmt.ToString();
                else
                    sAddAmt = nAddPrePayAmt.ToString();
                cmd.CommandText = "select * from info_prepay_card where card_type='" + pay_prepay_box_id + "' and cust_no='" + supcust_no + "' " + ys_com_condi;
                double prepayLeftAmount = 0; bool bPrePayExist = false;
                CMySbDataReader dr = cmd.ExecuteReader();
                if (dr.Read())
                {
                    bPrePayExist = true;
                    ov = dr["money_amount"];
                    if (ov != DBNull.Value)
                    {
                        prepayLeftAmount = Convert.ToDouble(ov);
                    }
                }
                dr.Close();
                if (!bPrePayExist)
                {
                    if (bPayOrGet)
                    {
                        strErr = "该供应商没有预付款往来业务";
                    }
                    else
                    {
                        strErr = "该客户没有预收款往来业务";
                    }
                    return strErr;
                }
                cmd.CommandText = "update info_prepay_card set money_amount=money_amount " + sAddAmt + " where card_type='" + pay_prepay_box_id + "' and cust_no='" + supcust_no + "' " + ys_com_condi;
                cmd.ExecuteNonQuery();
                cmd.CommandText = "select money_amount from info_prepay_card where card_type='" + pay_prepay_box_id + "' and cust_no='" + supcust_no + "' " + ys_com_condi;
                ov = cmd.ExecuteScalar();
                if (ov != null && ov != DBNull.Value)
                {
                    prepay_amt_left = Convert.ToDouble(ov);
                }
                if (m_redFlag != "2")
                {
                    m_prepay_left = prepay_amt_left;
                    if (m_pay_prepay_box_id != "")
                    {
                        cmd.CommandText = "update sheet_item_master set prepay_left_amount ='" + m_prepay_left.ToString() + "' where sheet_no='" + m_sheet_no + "'";
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            return "";
        }
        public string DealDuty(CMySbCommand cmd, double money_inout_flag)
        {
            string sAddAmount = "";
            double nAddAmount = 0;
          //  nAddAmount = m_pay_cash_amount + m_pay_ticket_amount;
            nAddAmount = m_pay_cash_amount;// +m_pay_ticket_amount;
            nAddAmount = money_inout_flag * nAddAmount;
            if (nAddAmount < 0)
                sAddAmount = nAddAmount.ToString();
            else
                sAddAmount = "+" + nAddAmount.ToString();

            string sTotalGet = "";
            double nTotalGet = money_inout_flag * (m_total_amount - m_disc_amt);
            if (nTotalGet < 0)
                sTotalGet = nTotalGet.ToString();
            else
                sTotalGet = "+" + nTotalGet.ToString();

            string sDiscAmt = "";
            double nDiscAmt = money_inout_flag * m_disc_amt;
            if (nDiscAmt < 0)
                sDiscAmt = nDiscAmt.ToString();
            else
                sDiscAmt = "+" + nDiscAmt.ToString();

            string sBankAmt = "";
            double nBankAmt = money_inout_flag * m_pay_bank_amount;
            if (nBankAmt < 0)
                sBankAmt = nBankAmt.ToString();
            else
                sBankAmt = "+" + nBankAmt.ToString();


            string sCardAmt = "";
            double nCardAmt = money_inout_flag * m_pay_card_amount;
            if (nCardAmt < 0)
                sCardAmt = nCardAmt.ToString();
            else
                sCardAmt = "+" + nCardAmt.ToString();

            string sPrepayAmt = "";
            double nPrepayAmt = money_inout_flag * m_pay_prepay_amount;
            if (nPrepayAmt < 0)
                sPrepayAmt = nPrepayAmt.ToString();
            else
                sPrepayAmt = "+" + nPrepayAmt.ToString();


            string sLeftAmt = "";
            double nLeftAmt = money_inout_flag * (m_total_amount-m_disc_amt-m_paid_amount);
            if (nPrepayAmt < 0)
                sLeftAmt = nLeftAmt.ToString();
            else
                sLeftAmt = "+" + nLeftAmt.ToString();

            string sTicketAmt = "";
            double nTicketAmt = money_inout_flag * m_pay_ticket_amount;
            if (nTicketAmt < 0)
                sTicketAmt = nTicketAmt.ToString();
            else
                sTicketAmt = "+" + nTicketAmt.ToString();

            string sIntegralAmt = "";
            double nIntegralAmt = money_inout_flag * m_pay_integral_amount;
            if (nIntegralAmt < 0)
                sIntegralAmt = nIntegralAmt.ToString();
            else
                sIntegralAmt = "+" + nIntegralAmt.ToString();

            string sReduceIntegral = "";
            double nReduceIntegral = money_inout_flag * m_pay_reduce_integral;
            if (nReduceIntegral < 0)
                sReduceIntegral = nReduceIntegral.ToString();
            else
                sReduceIntegral = "+" + nReduceIntegral.ToString();

            //string sOther1Amt = "";
            //double nOther1Amt = money_inout_flag * m_pay_other1_amount;
            //if (nOther1Amt < 0)
            //    sOther1Amt = nOther1Amt.ToString();
            //else
            //    sOther1Amt = "+" + nOther1Amt.ToString();

            //string sOther2Amt = "";
            //double nOther2Amt = money_inout_flag * m_pay_other2_amount;
            //if (nOther2Amt < 0)
            //    sOther2Amt = nOther2Amt.ToString();
            //else
            //    sOther2Amt = "+" + nOther2Amt.ToString();

            //string sOther3Amt = "";
            //double nOther3Amt = money_inout_flag * m_pay_other3_amount;
            //if (nOther3Amt < 0)
            //    sOther3Amt = nOther3Amt.ToString();
            //else
            //    sOther3Amt = "+" + nOther3Amt.ToString();

            //string sOther4Amt = "";
            //double nOther4Amt = money_inout_flag * m_pay_other4_amount;
            //if (nOther4Amt < 0)
            //    sOther4Amt = nOther4Amt.ToString();
            //else
            //    sOther4Amt = "+" + nOther4Amt.ToString();

            //string sOther5Amt = "";
            //double nOther5Amt = money_inout_flag * m_pay_other5_amount;
            //if (nOther5Amt < 0)
            //    sOther5Amt = nOther5Amt.ToString();
            //else
            //    sOther5Amt = "+" + nOther5Amt.ToString();

            //string sOther6Amt = "";
            //double nOther6Amt = money_inout_flag * m_pay_other6_amount;
            //if (nOther6Amt < 0)
            //    sOther6Amt = nOther6Amt.ToString();
            //else
            //    sOther6Amt = "+" + nOther6Amt.ToString();


            //cmd.CommandText = "update duty_record set get_money=get_money" + sAddAmount + ",get_total=get_total" + sTotalGet + ",disc_amt=disc_amt" + sDiscAmt + ",get_bank=get_bank" + sBankAmt + ",get_card=get_card" + sCardAmt + ",get_prepay=get_prepay" + sPrepayAmt + ",get_left_amt=get_left_amt" + sLeftAmt + ",get_ticket=get_ticket" + sTicketAmt + ",get_other1=get_other1" + sOther1Amt + ",get_other2=get_other2" + sOther2Amt + ",get_other3=get_other3" + sOther3Amt + ",get_other4=get_other4" + sOther4Amt + ",get_other5=get_other5" + sOther5Amt + ",get_other6=get_other6" + sOther6Amt + ",get_integral=get_integral" + sIntegralAmt + ",reduce_integral=reduce_integral" + sReduceIntegral + "  where duty_no='" + m_duty_no + "'";
            
            cmd.CommandText = "update duty_record set get_money=get_money" + sAddAmount + ",get_total=get_total" + sTotalGet + ",disc_amt=disc_amt" + sDiscAmt + ",get_bank=get_bank" + sBankAmt + ",get_card=get_card" + sCardAmt + ",get_prepay=get_prepay" + sPrepayAmt  + ",get_left_amt=get_left_amt" + sLeftAmt +  ",get_ticket=get_ticket" + sTicketAmt + ",get_integral=get_integral" + sIntegralAmt + ",reduce_integral=reduce_integral" + sReduceIntegral + "  where duty_no='" + m_duty_no + "'";
            cmd.ExecuteNonQuery();

            return "";
        }
        
        public string SaveSheet1()
        {
           
            return "";

        }

        public string Approve1()
        {            
            return "";

        }

        public virtual string GetPrintSheetMasterName()
        {
            return "";
        }
        public virtual string GetPrintSheetDetailName()
        {
            return "";
        }
        public virtual string GetPrintItemValue(CPrintItem prtItem)
        {
            return "";
        }
        //public virtual string GetPrintItemName(string fld)
        //{   return "";
        //}
        public virtual string GetRowPrintItemValue(SheetRow sheetRow,string fld)
        {
            return "";
        }
        public string GetRowPrintItemSumValue(string fld)
        {
           // string s = "";
            double nSum = 0;
            bool bNotNumber = false;
            foreach (var sheetRow in SheetRows)
            { 
                bool bIgnore = false;
                if (m_SheetType == SHEET_TYPE.SHEET_BUY || m_SheetType == SHEET_TYPE.SHEET_BUY_RETURN || m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN || m_SheetType == SHEET_TYPE.SHEET_SALE_DD || m_SheetType == SHEET_TYPE.SHEET_MOVE_STORE|| m_SheetType ==SHEET_TYPE.SHEET_INVENT_INPUT || m_SheetType ==SHEET_TYPE.SHEET_INVENT_CONFIRM ||m_SheetType ==SHEET_TYPE.SHEET_INVENT_REDUCE)
                {
                    if (sheetRow.item_no == "")
                        bIgnore = true;
                }
                if (!bIgnore)
                {
                    string value = GetRowPrintItemValue(sheetRow, fld);
                    if (CPubVars.IsNumeric(value))
                    {
                        nSum += Convert.ToDouble(value);
                    }
                    else
                        bNotNumber = true;
                }
            }
            if (bNotNumber)
            {
                if (m_PrintTableItems.ContainsKey(fld))
                {
                    CPrintItem pt = m_PrintTableItems[fld];
                    if (pt.format == 1)
                    {
                        CMoney money = new CMoney();
                        return money.NumToChn(CPubVars.FormatMoney(m_total_amount));
                    }
                    else
                        return CPubVars.FormatMoney(m_total_amount,2,true)+"元";                       
                }
            }
            else
            {
                return CPubVars.FormatMoney(nSum);
            }
            return "";
        }
        
        private SheetRow GetRowAt(int nRow)
        {
            int i=0;
            foreach (var row in SheetRows)
            {
                if (i == nRow)
                   return row;
                i++;
            }
            return null;
        }
     
        private float m_SheetContentHeight = 0;
     
        private bool m_bHaveMorePages = false;
      
        private int m_nRepeatRows = 1;
        private int m_nRepeatColumns = 1;
        private double m_pageWidth = 0;
        private double m_pageHeight = 0;
   
       
        public static bool IsChineseLetter(string input, int index)
        {
            int code = 0;
            int chfrom = Convert.ToInt32("4e00", 16);    //范围（0x4e00～0x9fff）转换成int（chfrom～chend）
            int chend = Convert.ToInt32("9fff", 16);
            if (input != "")
            {               
                string s = input.Substring(index,1);
                if (s == "（" || s == "）")
                {
                    return true;
                }
                code = Char.ConvertToUtf32(input, index);    //获得字符串input中指定索引index处字符unicode编码

                if (code >= chfrom && code <= chend)
                {
                    return true;     //当code在中文范围内返回true
                }
                else
                {
                    return false;    //当code不在中文范围内返回false
                }
            }
            return false;
        }
        private string GetDestText_local(string sOrig, SheetRow sheetRow)
        {
            return GetDestText_local(sOrig, sheetRow, null);
        }
        public string GetDestText_local(string sOrig, SheetRow sheetRow, LinkedList<CVariable> lstVariables)
        {
            return GetDestText_local(sOrig, sheetRow, lstVariables, false);
        }
        public string GetDestText_local(string sOrig, SheetRow sheetRow, LinkedList<CVariable> lstVariables, bool bIgnoreSpace)
        {
            string form_text = sOrig;
            // bool bInOptBlock = false; int n_OptStart = -1;
            string dest_text = form_text;
            string new_dest_text = "";
            if (!sOrig.Contains("{"))
            {
                return sOrig;
            }

            int n1 = -1; int n2 = -1;
            for (int i = 0; i < dest_text.Length; i++)
            {
                string cur = dest_text.Substring(i, 1);

                if (cur == "{")
                {
                    n1 = i;
                    int nStart = 0;
                    if (n2 != -1)
                    {
                        nStart = n2 + 1;
                    }
                    string sLeft = dest_text.Substring(nStart, i - nStart);
                    new_dest_text += sLeft;
                }
                else if (cur == "}" && n1 != -1)
                {
                    n2 = i;

                    string cc = dest_text.Substring(n1, n2 - n1 + 1);
                    string ll = dest_text.Substring(0, n1);
                    string rr = dest_text.Substring(n2 + 1, dest_text.Length - n2 - 1);

                    //double cc_len = 0;
                    string var_name = ""; string var_left = ""; string var_right = "";
                    for (int ci = 0; ci < cc.Length; ci++)
                    {
                        string cc_cur = cc.Substring(ci, 1);
                        if (cc_cur == " " || cc_cur == "{")
                        {
                            var_left += cc_cur;
                        }
                        else
                            break;
                    }
                    for (int ci = cc.Length - 1; ci >= 0; ci--)
                    {
                        string cc_cur = cc.Substring(ci, 1);
                        if (cc_cur == " " || cc_cur == "}")
                        {
                            var_right = cc_cur + var_right;
                        }
                        else
                            break;
                    }
                    string orig_var_name = cc.Substring(1, cc.Length - 2).Trim();
                    var_name = orig_var_name;
                    int n_sep = var_name.IndexOf('\\');
                    string style = "";
                    if (n_sep > 0)
                    {
                        style = var_name.Substring(n_sep + 1, orig_var_name.Length - n_sep - 1);
                        var_name = orig_var_name.Substring(0, n_sep);
                    }

                    string var_value = var_name;
                    bool bVarMet = false;
                    if (lstVariables != null)
                    {
                        foreach (CVariable var in lstVariables)
                        {
                            if (var.name == var_name || var.short_name == var_name || var.other_name == var_name)
                            {
                                if (var.value != null)
                                    var_value = var.value.ToString();
                                bVarMet = true;
                                break;
                            }
                        }
                        if (!bVarMet)
                        {
                            CCode code = new CCode();
                            code.m_lstVariables = lstVariables;
                            string codeErr = "";

                            string expr_value = code.getExpressionValue(false, var_name, ref codeErr).ToString();
                            if (expr_value != "")
                            {
                                var_value = expr_value;
                                bVarMet = true;
                            }
                            //
                        }

                    }
                    if (!bVarMet && sheetRow == null)
                    {
                        // var_value = var_name;
                        string sCompanyName = "";
                        if (m_PosCompanyName != "")
                        {
                            sCompanyName = m_PosCompanyName;
                        }
                        else
                        {
                            sCompanyName = "CPubVars.m_CompanyName";
                        }

                        var_value = var_value.Replace("店名", sCompanyName);

                        var_value = var_value.Replace("单据号", m_sheet_no);

                        if (var_value.Contains("当日单号"))
                        {
                            string sht_no;
                            sht_no = m_sheet_no;
                            if (sht_no.Length > 3)
                            {
                                sht_no = sht_no.Substring(sht_no.Length - 3, 3);
                                var_value = var_value.Replace("当日单号", sht_no);
                            }
                        }

                         
                         
                        var_value = var_value.Replace("POS机", "CPubVars.m_PosName");
                        //var_value = var_value.Replace("人数", m_TotalPeople);


                        // var_value = var_value.Replace("单据名", m_sheet_name);


                        if (m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
                        {
                            var_value = var_value.Replace("单据名", m_sheet_name + "-退单");
                        }
                        else if (m_SheetType == SHEET_TYPE.SHEET_SALE)
                        {
                            if (m_redFlag == "2")
                            {
                                var_value = var_value.Replace("单据名", m_sheet_name + "红字! 红冲单据:" + m_OrigSheetNo);
                            }
                            else if (m_approve_flag == "4")
                            {
                                var_value = var_value.Replace("单据名", "撤台单");
                            }
                            else
                                var_value = var_value.Replace("单据名", m_sheet_name);
                        }
                        else
                            var_value = var_value.Replace("单据名", m_sheet_name);
                        if (var_name.Contains("单据名"))
                        {
                            if (m_NormID != "" && m_sheet_id != "SYD" && m_sheet_id != "JZD")
                            {
                                var_value += "(标准餐:" + m_NormName + ")";
                            }
                            if (m_bChangeNorms)
                            {
                                var_value += "(修改标准)";
                            }
                        }
                        if (var_value == "制单人")
                        {

                        }
                        if (m_oper_name != "")
                            var_value = var_value.Replace("制单人", m_oper_name);
                        else
                            var_value = var_value.Replace("制单人", m_oper_id);

                        if (m_servicers_name != "")
                            var_value = var_value.Replace("服务员", m_servicers_name);
                        else
                            var_value = var_value.Replace("服务员", "");

                        if (m_table_persons != "")
                            var_value = var_value.Replace("人数", m_table_persons);
                        else
                            var_value = var_value.Replace("人数", "");



                        var_value = var_value.Replace("制单时间", CPubVars.GetDateText(m_oper_date));
                        var_value = var_value.Replace("制单日期", CPubVars.GetDateText(m_oper_date.Date));
                        if (var_name == "送货时间")
                        {
                            var_value = CPubVars.GetDateText(m_send_time);
                        }
                        if (var_name == "加单")
                        {
                            if (m_bEverAddSheet)
                            {
                                var_value = "加单";
                            }
                            else
                                var_value = "";
                        }
                        if (var_name == "相对制单日")
                        {
                            TimeSpan ts = DateTime.Now.Date - m_oper_date.Date;
                            double days = ts.TotalDays;
                            if (days == 0)
                                var_value = "今天";
                            else if (days == 1)
                                var_value = "昨天";
                            else if (days == 2)
                                var_value = "前天";
                            else if (days == 3)
                                var_value = "大前天";
                            else if (days == 7)
                                var_value = "一周前";
                            else
                                var_value = days.ToString() + "天前";
                        }

                        var_value = var_value.Replace("整单备注", m_brief);
                        if (var_name == "合计" || var_name == "hj" || var_name == "销售汇总金额" || var_name == "xshzje")
                        {
                            double nOrigTotal = 0;
                            foreach (var row in SheetRows)
                            { 
                                bool bIgnoreRow = (row.inout_flag == 0);
                                if (row.combine_flag != "2" && row.norm_status != "2" && !bIgnoreRow)
                                {
                                    nOrigTotal += row.money;
                                }
                            }
                            string g_totalamt_qz_jd = "0.01";
                            if (g_totalamt_qz_jd == "1")
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 0));
                            else if (g_totalamt_qz_jd == "0.1")
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 1));
                            else
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 2));
                            var_value = nOrigTotal.ToString();
                        }
                        else if (var_name == "抹后合计" || var_name == "mhhj")
                        {
                            var_value = CPubVars.FormatMoney(m_total_amount - m_disc_amt, 2, true);
                        }
                        else if (var_name == "原价合计" || var_name == "yjhj" || var_name == "销售汇总原价金额" || var_name == "xshzyjje")
                        {
                            double nOrigTotal = 0;
                            foreach (var row in SheetRows)
                            { 
                                bool bIgnoreRow = (row.inout_flag == 0);
                                if (row.combine_flag != "2" && row.norm_status != "2" && !bIgnoreRow)
                                {
                                    double orig_amt = row.orig_price * row.quantity;
                                    if (row.money > orig_amt)
                                    {
                                        orig_amt = row.money;
                                    }
                                    nOrigTotal += orig_amt;
                                }
                            }
                            string g_totalamt_qz_jd = "0.01";
                            if ( g_totalamt_qz_jd == "1")
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 0));
                            else if (g_totalamt_qz_jd == "0.1")
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 1));
                            else
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 2));
                            var_value = nOrigTotal.ToString();
                        }
                        else if (var_name == "总件数" || var_name == "zjs" || var_name == "销售汇总数量" || var_name == "xshzsl")
                        {
                            double nOrigTotal = 0;
                            foreach (var row in SheetRows)
                            {
                               
                                if (row.combine_flag != "2" && row.norm_status != "2" && row.inout_flag != 0)
                                {
                                    nOrigTotal += row.quantity;
                                }
                            }
                            var_value = nOrigTotal.ToString();
                        }
                        else if (var_name.Contains("lhjyj:") || var_name.Contains("类合计原价:"))
                        {
                            double nOrigTotal = 0;
                            var_name = var_name.Replace("lhjyj:", "");
                            var_name = var_name.Replace("类合计原价:", "").Trim();
                            var_name = var_name.Replace(" ", "");
                            string[] arr = var_name.Split('+');

                            foreach (var row in SheetRows)
                            {
                                
                                if (row.combine_flag != "2" && row.norm_status != "2" && row.inout_flag != 0)
                                {
                                    bool bClassMet = false;
                                    foreach (string cur_var in arr)
                                    {
                                        if (cur_var == "") continue;
                                        if (cur_var.Substring(0, 1) == "*")
                                        {
                                            string cur_var1 = cur_var.Replace("*", "");
                                            if (row.ClassName.IndexOf(cur_var1) == row.ClassName.Length - cur_var1.Length)
                                            {
                                                bClassMet = true;
                                            }
                                            else if (row.ClassID.IndexOf(cur_var1) == row.ClassID.Length - cur_var1.Length)
                                            {
                                                bClassMet = true;
                                            }
                                        }
                                        else if (cur_var.Substring(cur_var.Length - 1, 1) == "*")
                                        {
                                            string cur_var1 = cur_var.Replace("*", "");
                                            if (row.ClassName.IndexOf(cur_var1) == 0)
                                            {
                                                bClassMet = true;
                                            }
                                            else if (row.ClassID.IndexOf(cur_var1) == 0)
                                            {
                                                bClassMet = true;
                                            }
                                        }
                                        else if (cur_var == row.ClassName || cur_var == row.ClassID)
                                        {
                                            bClassMet = true;
                                        }
                                    }
                                    if (bClassMet)
                                    {
                                        double orig_amt = row.orig_price * row.quantity;
                                        if (row.money > orig_amt)
                                        {
                                            orig_amt = row.money;
                                        }
                                        nOrigTotal += orig_amt;
                                    }
                                }
                            }
                            string g_totalamt_qz_jd="0.01";
                            if ( g_totalamt_qz_jd == "1")
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 0));
                            else if ( g_totalamt_qz_jd == "0.1")
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 1));
                            else
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 2));
                            var_value = nOrigTotal.ToString();
                        }
                        else if (var_name.Contains("lhj:") || var_name.Contains("类合计:"))
                        {
                            double nOrigTotal = 0;
                            var_name = var_name.Replace("lhj:", "");
                            var_name = var_name.Replace("类合计:", "").Trim();
                            var_name = var_name.Replace(" ", "");
                            string[] arr = var_name.Split('+');

                            foreach (var row in SheetRows)
                            { 
                                if (row.combine_flag != "2" && row.norm_status != "2" && row.inout_flag != 0)
                                {
                                    bool bClassMet = false;
                                    foreach (string cur_var in arr)
                                    {
                                        if (cur_var == "") continue;
                                        if (cur_var.Substring(0, 1) == "*")
                                        {
                                            string cur_var1 = cur_var.Replace("*", "");
                                            if (row.ClassName.IndexOf(cur_var) == row.ClassName.Length - cur_var.Length)
                                            {
                                                bClassMet = true;
                                            }
                                            else if (row.ClassID.IndexOf(cur_var) == row.ClassID.Length - cur_var.Length)
                                            {
                                                bClassMet = true;
                                            }
                                        }
                                        else if (cur_var.Substring(cur_var.Length - 1, 1) == "*")
                                        {
                                            string cur_var1 = cur_var.Replace("*", "");
                                            if (row.ClassName.IndexOf(cur_var1) == 0)
                                            {
                                                bClassMet = true;
                                            }
                                            else if (row.ClassID.IndexOf(cur_var1) == 0)
                                            {
                                                bClassMet = true;
                                            }
                                        }
                                        else if (cur_var == row.ClassName || cur_var == row.ClassID)
                                        {
                                            bClassMet = true;
                                        }
                                    }
                                    if (bClassMet)
                                    {
                                        nOrigTotal += row.money;
                                    }
                                }
                            }
                            string g_totalamt_qz_jd = "";
                            if (g_totalamt_qz_jd == "1")
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 0));
                            else if (g_totalamt_qz_jd == "0.1")
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 1));
                            else
                                nOrigTotal = Convert.ToDouble(CPubVars.FormatMoney(nOrigTotal, 2));
                            var_value = nOrigTotal.ToString();
                        }
                        else if (var_name == "总计折扣" || var_name == "zjzk")
                        {
                            double nOrigTotal = 0;
                            foreach (var row in SheetRows)
                            { 
                                if (row.combine_flag != "2" && row.norm_status != "2" && row.inout_flag != 0)
                                {
                                    double orig_amt = row.orig_price * row.quantity;
                                    if (row.money > orig_amt)
                                    {
                                        orig_amt = row.money;
                                    }
                                    nOrigTotal += orig_amt;
                                }
                            }

                            double nRealTotal = 0;
                            foreach (var row in SheetRows)
                            { 
                                if (row.combine_flag != "2" && row.norm_status != "2" && row.inout_flag != 0)
                                {
                                    nRealTotal += row.money;
                                }
                            }

                            double disc_total = nOrigTotal - nRealTotal;
                            if (disc_total > 0)
                            {
                                string g_totalamt_qz_jd = "0.01";
                                if (g_totalamt_qz_jd == "1")
                                    disc_total = Convert.ToDouble(CPubVars.FormatMoney(disc_total, 0));
                                else if (g_totalamt_qz_jd == "0.1")
                                    disc_total = Convert.ToDouble(CPubVars.FormatMoney(disc_total, 1));
                                else
                                    disc_total = Convert.ToDouble(CPubVars.FormatMoney(disc_total, 2));
                                var_value = disc_total.ToString();
                            }
                            else
                                var_value = "";
                        }
                        else if (var_name == "去赠折扣" || var_name == "qzzk")
                        {
                            double nOrigTotal = 0;
                            foreach (var row in SheetRows)
                            { 
                                if (row.combine_flag != "2" && row.norm_status != "2" && row.inout_flag != 0 && row.money != 0)
                                {
                                    double orig_amt = row.orig_price * row.quantity;
                                    if (row.money > orig_amt)
                                    {
                                        orig_amt = row.money;
                                    }
                                    nOrigTotal += orig_amt;
                                }
                            }

                            double nRealTotal = 0;
                            foreach (var row in SheetRows)
                            { 
                                if (row.combine_flag != "2" && row.norm_status != "2" && row.inout_flag != 0 && row.money != 0)
                                {
                                    nRealTotal += row.money;
                                }
                            }

                            double disc_total = nOrigTotal - nRealTotal;
                            if (disc_total > 0)
                            {
                                string g_totalamt_qz_jd = "";
                                if (g_totalamt_qz_jd == "1")
                                    disc_total = Convert.ToDouble(CPubVars.FormatMoney(disc_total, 0));
                                else if (g_totalamt_qz_jd == "0.1")
                                    disc_total = Convert.ToDouble(CPubVars.FormatMoney(disc_total, 1));
                                else
                                    disc_total = Convert.ToDouble(CPubVars.FormatMoney(disc_total, 2));
                                var_value = disc_total.ToString();
                            }
                            else
                                var_value = "";
                        }
                        else if (var_name.Contains("加价合计") || var_name.Contains("jjhj"))
                        {
                            double nAddSum = 0;
                            foreach (var row in SheetRows)
                            { 
                                if (row.combine_flag != "2" && row.norm_status != "2" && row.inout_flag != 0)
                                {
                                    double nAddPrice = 0;
                                    if (CPubVars.IsNumeric(row.s_add_price))
                                    {
                                        nAddPrice = Convert.ToDouble(row.s_add_price);
                                        double nQtyMakeWay = sheetRow.quantity;
                                        if (/*CPubVars.g_bMkFeeBiger1U &&*/ nQtyMakeWay < 1)
                                            nQtyMakeWay = 1;
                                        nAddPrice = nAddPrice * nQtyMakeWay;
                                        nAddSum += nAddPrice;
                                    }
                                }
                            }
                            var_value = var_value.Replace("加价合计", CPubVars.FormatMoney(nAddSum, 2));
                            var_value = var_value.Replace("jjhj", CPubVars.FormatMoney(nAddSum, 2));
                        }
                     
                        else if (var_name == "折扣" || var_name == "zk")
                        {
                            if (m_discount >= 1)
                                var_value = "";
                            else
                                var_value = CPubVars.FormatMoney((m_discount * 100), 0) + "%";
                        }
                        else if (var_name == "促销方案" || var_name == "cxfa")
                        {
                            var_value = m_disc_scheme_name;
                        }
                        else if (var_name == "抹零")
                            var_value = CPubVars.FormatMoney(m_disc_amt, 2, true);
                        else if (var_name == "已收")
                            var_value = CPubVars.FormatMoney(m_paid_amount, 2, true);
                        else if (var_name == "欠款")
                        {
                            //   if (m_bApproved)
                            var_value = CPubVars.FormatMoney(m_total_amount - m_disc_amt - m_paid_amount, 2, true);
                            //  else
                            //      var_value = "";
                        }
                        else if (var_name == "实收")
                            var_value = CPubVars.FormatMoney(m_realGetAmt, 2, true);
                        else if (var_name == "找零")
                            var_value = CPubVars.FormatMoney(m_returnAmt, 2, true);
                        else if (var_name == "现金" || var_name == "现金支付")
                            var_value = CPubVars.FormatMoney(m_pay_cash_amount, 2, true);
                        else if (var_name == "信用卡" || var_name == "信用卡支付")
                            var_value = CPubVars.FormatMoney(m_pay_bank_amount, 2, true);
                        else if (var_name == "储值" || var_name == "储值支付")
                            var_value = CPubVars.FormatMoney(m_pay_card_amount, 2, true);
                        else if (var_name == "礼券" || var_name == "礼券支付")
                            var_value = CPubVars.FormatMoney(m_pay_ticket_amount, 2, true);
                        /*else if (var_name == CPubVars.g_OtherPayWay1 || var_name == CPubVars.g_OtherPayWay1 + "支付" || var_name == "qtzf1" || var_name == "其他支付1")
                        {
                            var_value = "0.00";
                            if(m_other_payway == CPubVars.g_OtherPayWay1)
                               var_value = CPubVars.FormatMoney(m_pay_other_amount, 2, true);                            
                             
                        }
                        else if (var_name == CPubVars.g_OtherPayWay2 || var_name == CPubVars.g_OtherPayWay2 + "支付" || var_name == "qtzf2" || var_name == "其他支付2")
                        {
                            //var_value = CPubVars.FormatMoney(m_pay_other2_amount, 2, true);
                            var_value = "0.00";
                            if (m_other_payway == CPubVars.g_OtherPayWay2)
                                var_value = CPubVars.FormatMoney(m_pay_other_amount, 2, true);
                        }
                        else if (var_name == CPubVars.g_OtherPayWay3 || var_name == CPubVars.g_OtherPayWay3 + "支付" || var_name == "qtzf3" || var_name == "其他支付3")
                        {
                            //var_value = CPubVars.FormatMoney(m_pay_other3_amount, 2, true);
                            var_value = "0.00";
                            if (m_other_payway == CPubVars.g_OtherPayWay3)
                                var_value = CPubVars.FormatMoney(m_pay_other_amount, 2, true);
                        }
                        else if (var_name == CPubVars.g_OtherPayWay4 || var_name == CPubVars.g_OtherPayWay4 + "支付" || var_name == "qtzf4" || var_name == "其他支付4")
                        {
                            //var_value = CPubVars.FormatMoney(m_pay_other4_amount, 2, true);
                            var_value = "0.00";
                            if (m_other_payway == CPubVars.g_OtherPayWay4)
                                var_value = CPubVars.FormatMoney(m_pay_other_amount, 2, true);
                        }
                        else if (var_name == CPubVars.g_OtherPayWay5 || var_name == CPubVars.g_OtherPayWay5 + "支付" || var_name == "qtzf5" || var_name == "其他支付5")
                        {
                            //var_value = CPubVars.FormatMoney(m_pay_other5_amount, 2, true);
                            var_value = "0.00";
                            if (m_other_payway == CPubVars.g_OtherPayWay5)
                                var_value = CPubVars.FormatMoney(m_pay_other_amount, 2, true);
                        }
                        else if (var_name == CPubVars.g_OtherPayWay6 || var_name == CPubVars.g_OtherPayWay6 + "支付" || var_name == "qtzf6" || var_name == "其他支付6")
                        {
                            //var_value = CPubVars.FormatMoney(m_pay_other6_amount, 2, true);
                            var_value = "0.00";
                            if (m_other_payway == CPubVars.g_OtherPayWay6)
                                var_value = CPubVars.FormatMoney(m_pay_other_amount, 2, true);
                        }
                        else if (var_name == "积分兑换")
                            var_value = CPubVars.FormatMoney(m_pay_integral_amount, 2, true);
                        */
                        else if (var_name == "扣减积分")
                            var_value = CPubVars.FormatMoney(m_pay_reduce_integral, 2, true);
                        else if (var_name == "累计积分" || var_name == "积分余额")
                            var_value = CPubVars.FormatMoney(m_now_integral_amount, 2, true);
                        else if (var_name == "累计储值" || var_name == "储值余额")
                            var_value = CPubVars.FormatMoney(m_now_money_amount, 2, true);
                        else if (var_name == "计次信息")
                            var_value = m_card_times_info;
                        else if (var_name == "本次积分")
                            var_value = CPubVars.FormatMoney(m_AddIntegral, 2, true);
                        else if (var_name == "会员卡号")
                            var_value = m_pay_card_id;
                        else if (var_name == "会员卡类别")
                            var_value = m_CardTypeName;
                        else if (var_name == "顾客")
                            var_value = m_supcust_name;
                        else if (var_name == "送货地址")
                            var_value = m_cust_addr;
                        else if (var_name == "联系电话")
                            var_value = m_cust_tel;
                        else if (var_name == "送货人")
                        {
                            if (m_GoodSenderName != "")
                                var_value = m_GoodSenderName;
                            else
                                var_value = m_GoodSenderID;
                        }
                        else if (var_name == "整单备注")
                            var_value = m_brief;  
                        else if (var_name == "多份页码" || var_name == "dfym")
                        {
                            var_value = m_nCurPrintTime.ToString();
                        } 
                        else if (var_name == "zfxx" || var_name == "支付信息")
                        {
                            var_value = "";
                            if (m_payway1_name != "")
                            {
                                var_value += m_payway1_name + ":" + CPubVars.FormatMoney(m_payway1_amount, 2);
                            }
                            if (m_payway2_name != "")
                            {
                                var_value += "\n" + m_payway2_name + ":" + CPubVars.FormatMoney(m_payway2_amount, 2);
                            }
                            if (m_payway3_name != "")
                            {
                                var_value += "\n" + m_payway3_name + ":" + CPubVars.FormatMoney(m_payway3_amount, 2);
                            }
                        }
                        else if (var_name == "zfxx1" || var_name == "支付信息1")
                        {
                            var_value = "";
                            if (m_payway1_name != "")
                            {
                                var_value = m_payway1_name + ":" + CPubVars.FormatMoney(m_payway1_amount, 2);
                            }
                        }
                        else if (var_name == "zfxx2" || var_name == "支付信息2")
                        {
                            var_value = "";
                            if (m_payway2_name != "")
                            {
                                var_value = m_payway2_name + ":" + CPubVars.FormatMoney(m_payway2_amount, 2);
                            }
                        }
                        else if (var_name == "zfxx3" || var_name == "支付信息3")
                        {
                            var_value = "";
                            if (m_payway3_name != "")
                            {
                                var_value = m_payway3_name + ":" + CPubVars.FormatMoney(m_payway2_amount, 2);
                            }
                        }
                        else if (var_name == "支付方式1" || var_name == "zffs1")
                        {
                            var_value = m_payway1_name;
                        }
                        else if (var_name == "支付方式2" || var_name == "zffs2")
                        {
                            var_value = m_payway2_name;
                        }
                        else if (var_name == "支付方式3" || var_name == "zffs3")
                        {
                            var_value = m_payway3_name;
                        }
                        else if (var_name == m_payway1_name || var_name == "支付金额1" || var_name == "zfje1")
                        {
                            var_value = CPubVars.FormatMoney(m_payway1_amount, 2);
                        }
                        else if (var_name == m_payway2_name || var_name == "支付金额2" || var_name == "zfje2")
                        {
                            var_value = CPubVars.FormatMoney(m_payway2_amount, 2);
                        }
                        else if (var_name == m_payway3_name || var_name == "支付金额3" || var_name == "zfje3")
                        {
                            var_value = CPubVars.FormatMoney(m_payway3_amount, 2);
                        }
                        else //if(var_name ==m_gp_site_name) 
                        { 
                        }
                    }
                    // else if (!bVarMet && sheetRow != null)
                    else if (sheetRow != null)
                    {
                        string v = "";
                        var_value = var_name;
                        if (var_name.Contains("汇总类名"))
                        {
                            string clsname = "";
                            if (sheetRow.bShowClassSum)
                            {
                                clsname = sheetRow.ClassName;
                            }
                            else
                                clsname = "";
                            var_value = var_value.Replace("汇总类名", clsname);

                        }
                        string prtItemName = sheetRow.item_name;
                        if (m_SheetType == SHEET_TYPE.SHEET_SALE && sheetRow.quantity < 0)
                        {
                            if (!prtItemName.Contains("(退)"))
                                prtItemName = "(退)" + prtItemName;
                        }

                        if (sheetRow.combine_flag == "2")
                        {
                            var_value = var_value.Replace("品名", "[套餐]" + prtItemName);
                        }
                        else
                            var_value = var_value.Replace("品名", prtItemName);

                        var_value = var_value.Replace("品号", sheetRow.item_model);
                     
                        if (var_name.Contains("原价") || var_name.Contains("yj"))
                        {
                            double orig_price = sheetRow.orig_price;
                           
                            if (sheetRow.real_price > orig_price)
                                orig_price = sheetRow.real_price;
                            var_value = var_value.Replace("原价", CPubVars.FormatMoney(orig_price, 2));
                            var_value = var_value.Replace("yj", CPubVars.FormatMoney(orig_price, 2));
                            if (sheetRow.combine_flag == "2" || sheetRow.norm_status == "2")
                            {
                                var_value = "";
                            }
                        }
                        if (var_name.Contains("单价") || var_name.Contains("dj"))
                        {
                            double nRealPrice = sheetRow.real_price; 

                            if (sheetRow.combine_flag == "2" || sheetRow.norm_status == "2")
                            {
                                var_value = ""; 
                            }
                            else
                            {
                                var_value = var_value.Replace("单价", CPubVars.FormatMoney(nRealPrice, 2));
                                var_value = var_value.Replace("dj", CPubVars.FormatMoney(nRealPrice, 2));
                            }
                        }


                        if (var_name.Contains("加价") || var_name.Contains("jj"))
                        {
                            double nAddPrice = 0;
                            string sAddPrice = "";
                            if (CPubVars.IsNumeric(sheetRow.s_add_price))
                            {
                                nAddPrice = Convert.ToDouble(sheetRow.s_add_price);
                                double nQtyMakeWay = sheetRow.quantity;
                                if (/*CPubVars.g_bMkFeeBiger1U &&*/ nQtyMakeWay < 1)
                                    nQtyMakeWay = 1;
                                nAddPrice = nAddPrice * nQtyMakeWay;
                                sAddPrice = CPubVars.FormatMoney(nAddPrice, 2);
                            }
                            var_value = var_value.Replace("加价", sAddPrice);
                            var_value = var_value.Replace("jj", sAddPrice);

                            if (sheetRow.combine_flag == "2" || sheetRow.norm_status == "2")
                            {
                                var_value = "";
                            }
                        }

                        if (var_name.Contains("单位"))
                        {
                            var_value = var_value.Replace("单位", sheetRow.unit_no);
                        }
                        if (var_name.Contains("单位数量")) var_value = var_value.Replace("单位数量", CPubVars.FormatMoney(sheetRow.unit_factor, 2));
                        if (var_name.Contains("纯数量") || var_name.Contains("csl"))
                        {
                           
                            {
                                string sQty = ""; string sUnit = sheetRow.unit_no;
                                if (sheetRow.s_quantity != null && sheetRow.s_quantity != "")
                                    sQty = sheetRow.s_quantity;
                                else
                                    sQty = CPubVars.FormatMoney(sheetRow.quantity, 2);
                               
                                
                                v = sQty;
                                var_value = var_value.Replace("纯数量", v);
                                var_value = var_value.Replace("csl", v);
                            }
                        }
                        if (var_name.Contains("数量") || var_name.Contains("sl"))
                        {
                            
                            {
                                string sQty = ""; string sUnit = sheetRow.unit_no;
                                if (sheetRow.s_quantity != null && sheetRow.s_quantity != "")
                                    sQty = sheetRow.s_quantity;
                                else
                                    sQty = CPubVars.FormatMoney(sheetRow.quantity, 2);
                                if (m_print_usage == PRINT_USAGE.PRINT_SYD || m_print_usage == PRINT_USAGE.PRINT_ACCOUNT)
                                {
                                     
                                }
 
                                v = sQty + sUnit;
                                var_value = var_value.Replace("数量", v);
                                var_value = var_value.Replace("sl", v);
                            }
                        }

                        if (var_name == "原小计" || var_name == "yxj")
                        {
                            double orig_amt = sheetRow.orig_price * sheetRow.quantity;
                            if (sheetRow.money > orig_amt)
                            {
                                orig_amt = sheetRow.money;
                            }
                            if (m_print_usage == PRINT_USAGE.PRINT_SYD || m_print_usage == PRINT_USAGE.PRINT_ACCOUNT)
                            {
                                 
                            }
                            //if (CPubVars.g_totalamt_qz_jd == "1")
                            //{
                            v = CPubVars.FormatMoney(orig_amt, 2, false);
                            // }
                            // else 
                            //    v = CPubVars.FormatMoney(orig_amt, 2,true);
                            var_value = v;
                            // var_value = yxj", v);

                            if (sheetRow.combine_flag == "2" || sheetRow.norm_status == "2")
                            {
                                var_value = "";
                            }
                        }
                        else if (var_name == "小计" || var_name == "xj")
                        {
                            //if (CPubVars.g_totalamt_qz_jd == "1")
                            // {
                            v = CPubVars.FormatMoney(sheetRow.money, 2, false);
                            // }
                            // else
                            // {
                            //    v = CPubVars.FormatMoney(sheetRow.money, 2, true);
                            // }
                            
                            var_value = v;
                            if (sheetRow.combine_flag == "2" || sheetRow.norm_status == "2")
                            {
                                var_value = "";
                            }
                        }

                        if (var_name.Contains("折扣") || var_name.Contains("zk"))
                        {
                            //double disc = 1;
                            //if (sheetRow.orig_price > sheetRow.real_price)
                            //{
                            //    disc = sheetRow.real_price / sheetRow.orig_price;
                            //}
                            // v = CPubVars.FormatMoney(disc * 100,0) + "%";
                            if (sheetRow.discount >= 1)
                            {
                                v = "";
                            }
                            else
                            {
                                v = CPubVars.FormatMoney(sheetRow.discount * 100, 0) + "%";
                            }
                            var_value = var_value.Replace("折扣", v);
                            var_value = var_value.Replace("zk", v);
                        }

                        if (var_name.Contains("优惠金额") || var_name.Contains("yhje"))
                        {
                            double origAmt = sheetRow.orig_price * sheetRow.quantity;
                            string g_subamt_qz_jd = "";
                            if ( g_subamt_qz_jd == "0.01")
                            {
                                origAmt = Convert.ToDouble(CPubVars.FormatMoney(origAmt, 2));
                            }
                            else if ( g_subamt_qz_jd == "0.1")
                            {
                                origAmt = Convert.ToDouble(CPubVars.FormatMoney(origAmt, 1));
                            }
                            else if ( g_subamt_qz_jd == "1")
                            {
                                origAmt = Convert.ToDouble(CPubVars.FormatMoney(origAmt, 0));
                            }
                            double yhje = origAmt - sheetRow.money;
                            v = "";
                            if (yhje > 0)
                            {
                                v = "优惠" + CPubVars.FormatMoney(yhje, 2) + "元";
                            }
                            var_value = var_value.Replace("优惠金额", v);
                            var_value = var_value.Replace("yhje", v);
                            if (sheetRow.combine_flag == "2" || sheetRow.norm_status == "2")
                            {
                                var_value = "";
                            }

                        }

                        if (var_name.Contains("赠品"))
                        {
                            //if (sheetRow.real_price == 0 && !sheetRow.brief.Contains("赠品"))
                            if (sheetRow.real_price == 0)
                            {
                                v = "赠品";
                                var_value = var_value.Replace("赠品", v);
                            }
                            else
                                var_value = "";

                            if (sheetRow.combine_flag == "2" || sheetRow.norm_status == "2")
                            {
                                var_value = "";
                            }
                        }
                        if (var_name.Contains("单项备注") || var_name.Contains("dxbz"))
                        {
                            var_value = var_value.Replace("单项备注", sheetRow.brief);
                            var_value = var_value.Replace("dxbz", sheetRow.brief);
                        }
                        if (var_name == "做法" || var_name == "zf")
                        {
                            string mkway = sheetRow.make_way;
                            if (sheetRow.quantity < 0)
                            {
                                if (!sOrig.Contains("退菜原因") && !sOrig.Contains("tcyy") && sheetRow.return_reason != "")
                                {
                                    if (mkway != "") mkway += ",";
                                    mkway += sheetRow.return_reason;
                                }
                            }
                            var_value = var_value.Replace("做法", mkway);
                            var_value = var_value.Replace("zf", mkway);
                        }
                        if (var_name.Contains("退菜原因") || var_name == "tcyy")
                        {
                            var_value = var_value.Replace("退菜原因", sheetRow.return_reason);
                            var_value = var_value.Replace("tcyy", sheetRow.return_reason);
                        }
                        if (var_name == "起菜方式" || var_name == "qcfs")
                        {
                            var_value = var_value.Replace("起菜方式", sheetRow.up_style);
                            var_value = var_value.Replace("qcfs", sheetRow.up_style);
                        }
                        if (var_name == "服务员名" || var_name == "fwym")
                        {
                            var_value = var_value.Replace("服务员", sheetRow.servicer_name);
                            var_value = var_value.Replace("qcfs", sheetRow.servicer_name);
                        }
                        if (var_name == "服务员号" || var_name == "fwyh")
                        {
                            var_value = var_value.Replace("服务员号", sheetRow.servicer_id);
                            var_value = var_value.Replace("fwyh", sheetRow.servicer_id);
                        }
                        if (var_name == "销售单号")
                        {
                            var_value = var_value.Replace("销售单号", sheetRow.item_name);
                        }
                        else if (var_name == "业务员")
                        {
                            var_value = var_value.Replace("业务员", sheetRow.item_name);
                        }
                        else if (var_name == "支付方式")
                        {
                            var_value = var_value.Replace("支付方式", sheetRow.other1);
                        }
                        else if (var_name == "VIP单号")
                        {
                            var_value = var_value.Replace("支付方式", sheetRow.item_name);
                        }
                        else if (var_name == "会员卡号")
                        {
                            var_value = var_value.Replace("支付方式", sheetRow.other2);
                        }
                        else if (var_name == "单据类型")
                        {
                            var_value = var_value.Replace("支付方式", sheetRow.other3);
                        }
                        else if (var_name.Contains("BARCODE"))
                        {
                            string barcode = sheetRow.item_subno;
                            if (barcode == "" /*&&  "CPubVars.g_bINoBarcodePrtModel"="true"*/) barcode = sheetRow.item_model;
                            if (barcode != "")
                            {
                                var_value = ((char)10).ToString() + ((char)29).ToString() + ((char)107).ToString() + ((char)72).ToString() + ((char)barcode.Length).ToString() + barcode;
                                m_bHaveBarcodeToPrint = true;
                                // + " birthday" + ((char)27).ToString() + ((char)100).ToString() + ((char)0).ToString()

                            }
                        }
                    }
                    double hz_len = 2;
                    var_value = var_value.Replace("()", "");
                    
                    double value_len = 0;
                    if (style.Contains("0"))
                    {
                        var_value = CPubVars.FormatMoney(var_value, 2, true);
                    }

                    for (int ii = 0; ii < var_value.Length; ii++)
                    {
                        if (IsChineseLetter(var_value, ii))
                        {
                            value_len += hz_len;
                        }
                        else
                            value_len += 1;
                    }

                    double vl_len = var_left.Length;
                    double vr_len = var_right.Length;
                    double v_len = 0;

                    for (int ii = 0; ii < var_name.Length; ii++)
                    {
                        if (IsChineseLetter(var_name, ii))
                        {
                            v_len += hz_len;
                        }
                        else
                            v_len += 1;
                    }

                    double vl_len1 = 0;

                    double vr_len1 = 0;

                    int spec_len = 0;
                    if (style.Contains("2")) spec_len = 2;
                    if (style.Contains("3")) spec_len = 3;
                    if (style.Contains("4")) spec_len = 4;
                    if (style.Contains("5")) spec_len = 5;
                    if (style.Contains("6")) spec_len = 6;
                    if (style.Contains("7")) spec_len = 7;
                    if (style.Contains("8")) spec_len = 8;
                    if (style.Contains("9")) spec_len = 9;
                    if (style.Contains("10")) spec_len = 10;
                    if (style.Contains("11")) spec_len = 11;
                    if (style.Contains("12")) spec_len = 12;
                    if (style.Contains("13")) spec_len = 13;
                    if (style.Contains("14")) spec_len = 14;
                    if (style.Contains("15")) spec_len = 15;
                    //if (style.Contains("16")) spec_len = 16;
                    //if (style.Contains("17")) spec_len = 17;
                    //if (style.Contains("18")) spec_len = 18;
                    //if (style.Contains("19")) spec_len = 19;
                    //if (style.Contains("20")) spec_len = 20;

                    double design_len = vl_len + vr_len + v_len;
                    if (spec_len > 0)
                    {
                        design_len = spec_len;
                        if (style.Contains("r"))
                        {
                            vl_len1 = spec_len - value_len;
                            vr_len1 = 0;
                        }
                        else
                        {
                            vr_len1 = spec_len - value_len;
                            vl_len1 = 0;
                        }
                    }
                    {
                        if (vl_len + vr_len > 2)
                        {
                            vl_len1 = (design_len - value_len) * (vl_len - 1) / (vl_len + vr_len - 2);
                            vr_len1 = (design_len - value_len) * (vr_len - 1) / (vl_len + vr_len - 2);
                        }
                        else if (spec_len == 0)
                        {
                            vl_len1 = 0;
                            vr_len1 = vl_len + vr_len + v_len - value_len;
                            // vr_len1 = 0;
                        }
                    }
                    bool bItemNameLong = false;
                    if (vl_len1 < 0) vl_len1 = 0;
                    if (vr_len1 < 0)
                    {
                        vr_len1 = 0;
                        if (var_name == "品名")
                        {
                            bItemNameLong = true;
                        }
                    }
                    string var_value_text = "";
                    if (bIgnoreSpace)
                        var_value_text = var_value;
                    else
                    {
                        var_value_text = "".PadRight((int)vl_len1, ' ') + var_value + "".PadRight((int)vr_len1, ' ');
                    }
                    if (bItemNameLong)
                        var_value_text += "\n" + "".PadRight((int)design_len, ' ');
                    //dest_text = ll + var_value_text + rr;
                    if (var_name.Contains("IMAGE_"))
                    {
                        var_value_text = "{" + var_name + "}";
                    }

                    new_dest_text += var_value_text;
                    //break;
                }
                else if (i == dest_text.Length - 1)
                {
                    if (n2 != -1)
                    {
                        string sLeft = dest_text.Substring(n2 + 1, dest_text.Length - n2 - 1);
                        new_dest_text += sLeft;
                    }
                    else
                    {
                        new_dest_text = dest_text;
                    }
                }
            }
            return new_dest_text;
        }


        public string GetDestText(string sOrig,SheetRow sheetRow, LinkedList<CVariable> lstVariables)
        {
            string sDest = RemoveOptBlock(sOrig, sheetRow, lstVariables);
            sDest = GetDestText_local(sDest, sheetRow,lstVariables);
            return sDest;
        }
        public string GetDestText(string sOrig, SheetRow sheetRow)
        {
            string sDest=RemoveOptBlock(sOrig, sheetRow,null);
            sDest = GetDestText_local(sDest, sheetRow);
            return sDest;
        }
        public string RemoveOptBlock(string sOrig, SheetRow sheetRow, LinkedList<CVariable> lstVariables)
        {
            if (!(sOrig.Contains("[") && sOrig.Contains("]")))
                return sOrig;
            string new_dest_text = "";
            string form_text = sOrig;
            int n_OptStart = -1; int n_OptEnd = -1;
            string dest_text = form_text;
             
            for (int i = 0; i < dest_text.Length; i++)
            {
                string cur = dest_text.Substring(i, 1);
                if (cur == "[")
                {                    
                    n_OptStart = i;
                    int nStart = 0;
                    if (n_OptEnd != -1)
                    {
                        nStart = n_OptEnd + 1;
                    }
                    string me = dest_text.Substring(nStart,i - nStart);
                    new_dest_text += me;

                }
                else if (cur == "]" && n_OptStart!=-1)
                {
                    if (dest_text.Contains("欠款"))
                    {

                    }
                    n_OptEnd = i;
                    bool bHaveValidVar = false;
                    int block_start = -1; int block_end = -1;
                    for (int j = n_OptStart; j <= n_OptEnd; j++)
                    {
                        cur = dest_text.Substring(j, 1);
                        if (cur == "{")
                        {
                            block_start = j;
                        }
                        else if (cur == "}" && block_start != -1)
                        {                             
                            block_end = j;
                            string var = dest_text.Substring(block_start, block_end - block_start+1);
                            string value = GetDestText_local(var, sheetRow, lstVariables).Trim();
                            if (value != "0" && value != "" && value!="0.00")
                            {
                                bHaveValidVar = true;
                                break;
                            }
                            block_start = -1;
                        }
                    }
                    if (!bHaveValidVar)
                    {
                       //string ll = dest_text.Substring(0, n_OptStart);
                       //string rr = dest_text.Substring(n_OptEnd, dest_text.Length - n_OptEnd - 1);
                       //new_dest_text += ll + rr;                     
                    }
                    else
                    {
                        int nStart = n_OptStart + 1;                        
                        string me = dest_text.Substring(nStart,i - nStart);
                        new_dest_text += me;
                       
                    }
                    n_OptStart=-1;

                }
                else if (i == dest_text.Length - 1)
                {
                    int nStart = 0;
                    if(n_OptEnd !=-1)
                       nStart = n_OptEnd + 1;
                    string me = dest_text.Substring(nStart,i + 1 - nStart);
                    new_dest_text += me;
                } 
            }

            new_dest_text = new_dest_text.Replace("[", "");
            new_dest_text = new_dest_text.Replace("]", "");
            return new_dest_text;

        }      
     
       
        public List<byte[]> m_lstPrintBuf =null;
        public string m_printer_no = ""; public string m_printer_name = "";
        public string m_printer1_no = "";
        public string m_printer2_no = "";
        
        public double GetOrigAmount()
        {
            double amt = 0;
            if (m_SheetType == SHEET_TYPE.SHEET_SALE || m_SheetType == SHEET_TYPE.SHEET_SALE_RETURN)
            {
                foreach (SheetRow row in this.SheetRows)
                { 
                    if (row.inout_flag == 0)
                        continue;
                    double orig_amount = 0;
                    if (row.orig_price > row.real_price)
                        orig_amount = row.orig_price * row.quantity;
                    else
                        orig_amount = row.money;
                    amt += orig_amount;
                }
                return amt;
            }
            else
                return m_total_amount;
        }
        public double GetRowDiscAmount()
        {
            double discamt = GetOrigAmount() - m_total_amount;
            return discamt;
        } 
    } 
}

