﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace ArtisanManage.Pages.Report;

public class VipPointsDetailModel : PageQueryModel
{
    #region 存放会员积分报表通用变量
    /// <summary>
    /// 查询选项：销售类型
    /// </summary>
    public static DataItem QueryOptionRwFlag
    {
        get
        {
            return new()
            {
                Title = "销售类型",
                FldArea = "divHead",
                Checkboxes = true,
                ButtonUsage = "list",
                CompareOperator = "=",
                Value = "a",
                Label = "所有",
                Source = @"[
                    { v:'a', l:'所有', condition:""1=1"" },
                    { v:'w', l:'批发', condition:""(log.retail_whole_flag='w' or log.retail_whole_flag is null)"" },
                    { v:'r', l:'零售', condition:""(log.retail_whole_flag='r' or log.retail_whole_flag is null)"" }
                ]"
            };
        }
    }
    /// <summary>
    /// 查询选项：积分变化类型
    /// </summary>
    public static DataItem QueryOptionPointType
    {
        get
        {
            return new()
            {
                Title = "积分变化类型",
                FldArea = "divHead",
                Checkboxes = true,
                ButtonUsage = "list",
                CompareOperator = "=",
                Value = "all",
                Label = "所有",
                Source = @"[
                    { v:'all',            l:'所有', condition:""1=1"" },
                    { v:'redemption',     l:'兑换', condition:""log.point_change_type='redemption'"" },
                    { v:'point_to_money', l:'积分抵扣金额', condition:""log.point_change_type='point_to_money'"" },
                    { v:'sheet_points',   l:'下单获取', condition:""log.point_change_type='sheet_points'"" },
                    { v:'sheet_reded',    l:'单据红冲', condition:""log.point_change_type='sheet_reded'"" },
                    { v:'point_expire',   l:'过期', condition:""log.point_change_type='point_expire'"" },
                ]"
            };
        }
    }

    /// <summary>
    /// 积分变化类型(point_change_type)的SqlFld
    /// </summary>
    public static string SqlfldPointChangeType
    {
        get
        {
            return @"
                case
                    when log.point_change_type='redemption' then '兑换'
                    when log.point_change_type='point_to_money' then '积分抵扣金额'
                    when log.point_change_type='sheet_points' then '下单获取'
                    when log.point_change_type='sheet_reded' then '单据红冲'
                    when log.point_change_type='point_expire' then '过期'
                    else ''
                end
            ";
        }
    }
    #endregion

    public VipPointsDetailModel(CMySbCommand cmd) : base(MenuId.vipPointsDetail)
    {
        this.cmd = cmd;
        PageTitle = "会员积分明细表";
        UsePostMethod = true;
        NotQueryHideColumn = false;

        DataItems = new Dictionary<string, DataItem>()
        {
            // 用于过滤会员卡/客户的查询条件
            { "supcust_id",   new DataItem(){ Title="客户", SqlFld="s.supcust_id",FldArea="divHead",LabelFld="sup_name",ButtonUsage="list",Checkboxes=true,QueryByLabelLikeIfIdEmpty=true, CompareOperator="=",
                SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID" }},
            { "vip_level_id", new DataItem(){ Title="会员等级", SqlFld="l.vip_level_id",FldArea="divHead",LabelFld="l.vip_level_name",ButtonUsage="list",Checkboxes=true,CompareOperator="=",
                SqlForOptions = "select vip_level_id as v,vip_level_name as l from vip_level where company_id = ~COMPANY_ID" }},
            { "other_region", new DataItem(){ Title="片区", FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                SqlForOptions = "select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index " }},
            { "group_id",     new DataItem(){ Title="渠道",FldArea="divHead", Checkboxes=true,LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                SqlForOptions = "select group_id as v,group_name as l from info_supcust_group" }},
            { "sup_rank",     new DataItem(){ Title="等级",FldArea="divHead",Checkboxes=true,LabelFld="rank_name",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",CompareOperator="=",
                SqlForOptions = "select rank_id as v,rank_name as l from info_supcust_rank" }},
            // 用于过滤积分变动的查询条件
            { "startDay",     new DataItem(){ Title="开始日期", SqlFld="log.create_time",FldArea="divHead", CtrlType="jqxDateTimeInput", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
            { "endDay",       new DataItem(){ Title="结束日期", SqlFld="log.create_time",FldArea="divHead", CtrlType="jqxDateTimeInput", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                JSDealItemOnSelect="var s=$('#endDay').jqxDateTimeInput('val').toString();if(s!=''){s=s.replace('00:00','23:59');$('#endDay').jqxDateTimeInput('val',s);}" }},
            { "rw_flag",      QueryOptionRwFlag },
            { "point_type",   QueryOptionPointType }
        };

        Grids = new Dictionary<string, QueryGrid>()
        {{
            "gridItems", new QueryGrid()
            {
                ShowAggregates=true,
                Sortable=true,
                Columns = new Dictionary<string, DataItem>()
                {
                    { "supcust_id",          new() { Title="客户ID", SqlFld="s.supcust_id",Width="150",Hidden=true,HideOnLoad=true } },
                    { "sup_name",            new() { Title="客户名称", SqlFld="s.sup_name",Width="200",Sortable=true,IsChinese=true } },
                    { "vip_level_id",        new() { Title="会员卡等级ID", SqlFld="l.vip_level_id",Width="150",Hidden=true,HideOnLoad=true } },
                    { "vip_level_name",      new() { Title="会员卡等级", SqlFld="l.vip_level_name",Width="100",Sortable=true,IsChinese=true } },
                    { "create_time",         new() { Title="日期", SqlFld="log.create_time",Width="150",Sortable=true } },
                    { "point_change_type",   new() { Title="积分变化类型", SqlFld=SqlfldPointChangeType,Width="125",Sortable=true } },
                    { "point_group_total",   new() { Title="本次积分变化", SqlFld="log.point_group_total",Width="100",Sortable=true, ShowSum=true } },
                    { "pre_points",          new() { Title="总积分变化前", SqlFld="log.pre_points",Width="100",Sortable=true } },
                    { "cur_points",          new() { Title="总积分变化后", SqlFld="log.cur_points",Width="100",Sortable=true } },
                    { "sale_sheet_id",       new() { Title="销售单ID", SqlFld="sx.sheet_id",Width="100",Hidden=true,HideOnLoad=true } },
                    { "sale_sheet_no",       new() { Title="销售单号", SqlFld="sx.sheet_no",Width="150",Linkable=true,Sortable=true } },
                    { "sale_order_sheet_id", new() { Title="销售订单ID", SqlFld="sxd.sheet_id",Width="100",Hidden=true,HideOnLoad=true } },
                    { "sale_order_sheet_no", new() { Title="销售订单号", SqlFld="sxd.sheet_no",Width="150",Linkable=true,Sortable=true } },
                    { "retail_whole_flag",   new() { Title="销售类型", SqlFld="(case when log.retail_whole_flag='w' then '批发' when log.retail_whole_flag='r' then '零售' else '' end)",Width="100",Sortable=true,IsChinese=true } },
                    { "order_source",        new() { Title="下单来源", SqlFld="(case when log.order_source='xcx' then '线上' when log.retail_whole_flag='offline' then '线下' else '' end)",Width="100",Sortable=true } },
                    // 下面这两个暂时做不了，搁置
                    { "redeem_item_name",    new() { Title="兑换商品名称", SqlFld="('???')",Width="100",Sortable=true,Hidden=true,HideOnLoad=true } },
                    { "redeem_item_amount",  new() { Title="兑换商品数量", SqlFld="('???')",Width="100",Sortable=true,Hidden=true,HideOnLoad=true } },
                    { "sheet_remark",        new() { Title="单据备注", SqlFld="(case when log.sheet_type = 'X' then sx.make_brief when log.sheet_type = 'XD' then sxd.make_brief else '' end)", Width="200",Sortable=true } },
                },
                QueryFromSQL=$@"
                    FROM
                        (
                            select
                                company_id, point_group_id,
                                max(retail_whole_flag) as retail_whole_flag,  
                                max(vip_card_id) as vip_card_id,  
                                max(client_id) as client_id,  
                                max(create_time) as create_time,
                                max(point_group_total) as point_group_total, 
                                max(pre_points) as pre_points, 
                                max(cur_points) as cur_points, 
                                max(point_change_type) as point_change_type, 
                                max(sheet_id) as sheet_id, 
                                max(sheet_type) as sheet_type, 
                                max(order_source) as order_source
                            from vip_point_change_log
                            where
                                company_id = ~COMPANY_ID
                                and create_time > '~START_DAY'
                                and create_time < '~END_DAY'
                            group by company_id, point_group_id
                        ) log
                        left join vip_card c on c.vip_card_id = log.vip_card_id and c.company_id = ~COMPANY_ID
                        left join info_supcust s on log.client_id = s.supcust_id and s.company_id = ~COMPANY_ID
                        left join vip_level l on c.vip_level_id = l.vip_level_id and l.company_id = ~COMPANY_ID
                        left join sheet_sale_main sx on log.sheet_id = sx.sheet_id and log.sheet_type = 'X' and sx.company_id = ~COMPANY_ID
                        left join sheet_sale_order_main sxd on log.sheet_id = sxd.sheet_id and log.sheet_type = 'XD' and sxd.company_id = ~COMPANY_ID
                    WHERE
                        c.company_id = ~COMPANY_ID
                ",
                QueryGroupBySQL = "",
                QueryOrderSQL = "ORDER BY log.create_time desc"
            }
        }};
    }

    #pragma warning disable CS1998
    public override async Task OnQueryConditionGot(CMySbCommand cmd)
    {
        var startDay = DataItems["startDay"].Value;
        var endDay = DataItems["endDay"].Value;
        SQLVariables["startDay"] = startDay;
        SQLVariables["endDay"] = endDay;

        var sql = Grids["gridItems"].QueryFromSQL;
        sql = sql.Replace("~START_DAY", startDay);
        sql = sql.Replace("~END_DAY", endDay);
        Grids["gridItems"].QueryFromSQL = sql;
    }
    public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
    {
    }
    #pragma warning restore CS1998

    public async Task OnGet()
    {
        await InitGet(cmd);
    }
}

[Route("api/[controller]/[action]")]
public class VipPointsDetailController : QueryController
{
    public VipPointsDetailController(CMySbCommand cmd)
    {
        this.cmd = cmd;
    }

    [HttpGet]
    public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
    {
        var model = new VipPointsDetailModel(cmd);
        var response = await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
        return response;
    }

    [HttpPost]
    public async Task<object> GetQueryRecords([FromBody] dynamic data)
    {
        var model = new VipPointsDetailModel(cmd);
        object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
        return records;
    }

    [HttpPost]
    public async Task<ActionResult> ExportExcel()
    {
        string sParams = Request.Form["params"];
        sParams = System.Web.HttpUtility.UrlDecode(sParams);
        dynamic queryParams = JsonConvert.DeserializeObject(sParams);

        var model = new VipPointsDetailModel(cmd);
        return await model.ExportExcel(Request, cmd, queryParams);
    }
}
