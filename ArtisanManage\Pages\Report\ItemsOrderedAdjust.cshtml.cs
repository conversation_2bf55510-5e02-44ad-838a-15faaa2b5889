﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ItemsOrderedAdjustModel : PageQueryModel
    { 
        public ItemsOrderedAdjustModel(CMySbCommand cmd) : base(Services.MenuId.itemsOrderedAdjust)
        {
            this.cmd = cmd;

            this.PageTitle = "定货调整记录";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="oc.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="oc.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"item_id",new DataItem(){FldArea="divHead",Title="商品名称",SqlFld="oc.item_id",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",
                   SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode }},
                {"supcust_id",new DataItem(){Title="客户",FldArea="divHead", LabelFld="sup_name",ButtonUsage="list",CompareOperator="=",SqlFld="oc.supcust_id",
                    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID"}},
                {"prepay_sub_id",new DataItem(){Title = "定货款账户",FldArea = "divHead",LabelFld="prepay_sub_name",ButtonUsage = "list",CompareOperator="=",SqlFld="oc.prepay_sub_id",
                    SqlForOptions = "select sub_id as v,sub_name as l,py_str as z from cw_subject where sub_type = 'YS' and is_order = true  and company_id=~COMPANY_ID" } }

                //{"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入客户名称", SqlFld="sup_name",CompareOperator="like"}},
            };
            var cellsRender = @"function cellsrenderer_style (row, columnfield, value, defaulthtml, columnproperties) {
                                return  `<div style=""color:#aaa;margin-top:6px;margin-left:4px"">${value}</div>`
                            }";

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"item_id", new DataItem(){Title="现商品",Width = "200px",Hidden = true,SqlFld = "oc.item_id" }},
                       {"oper_type",new DataItem(){Title = "操作类型",Width = "80px",
                           SqlFld = "case when oper_type = 'add' then '新增' when oper_type = 'change' then '修改' when oper_type = 'delete' then '删除' when oper_type = 'same' then '保持' end " } },
                       {"sheet_id",new DataItem(){Title = "调整单",Hidden = true,SqlFld = "oc.sheet_id" } },
                       {"sheet_no",new DataItem(){Title = "调整单",Width = "180px",Linkable = true } },
                       {"sheet_type", new DataItem(){Title="类型",Hidden = true, Width="55",SqlFld="(case WHEN sheet_type='DHTZ' THEN '定货调整单' END)"}},
                       {"sup_name",new DataItem(){Title = "客户",Width = "180px",CellsAlign="right" } },
                       {"item_name",new DataItem(){Title = "商品",Width = "180px",CellsAlign = "right",
                       } },
                       {"unit_no",new DataItem(){Title = "单位",Width = "100px",Hidden = true,CellsAlign = "right",
                           JSCellRender = @"function cellsrenderer_style (row, columnfield, value, defaulthtml, columnproperties) {
                                var old_unit_no = $('#gridItems').jqxGrid('getcellvalue', row, 'old_unit_no');
                                if(old_unit_no != value) return  `<div style=""color:#f00;margin-top:6px;margin-left:4px;text-align:right"">${value}</div>`
                            }"
                       } },
                       {"real_price",new DataItem(){Title="定货单价",Width = "150px",CellsAlign = "right",Hidden = true } },
                       {"real_price_unit",new DataItem(){Title="定货单价",Width = "150px",CellsAlign = "right",SqlFld = @"(case when real_price = round(real_price) then concat(round(real_price),'/',unit_no)
                                                                                                                                     when real_price is null then ''
                                                                                                                                 else concat(real_price,'/',unit_no) end) " } },
                       {"old_quantity",new DataItem(){Title="原数量",Width = "150",Hidden = true } },
                       {"now_quantity",new DataItem(){Title = "现数量",Width = "150",Hidden = true,
                            JSCellRender = @"function cellsrenderer_style (row, columnfield, value, defaulthtml, columnproperties) {
                                var old_quantity = $('#gridItems').jqxGrid('getcellvalue', row, 'old_quantity');
                                if(old_quantity != value) return  `<div style=""color:#f00;margin-top:6px;margin-left:4px;text-align:right"">${value}</div>`
                            }"
                       } },
                       {"old_qty_unit",new DataItem(){Title = "调整前数量",Width = "120",SqlFld = "(case when old_quantity = round(old_quantity) then concat(round(old_quantity),unit_no) else concat(old_quantity,unit_no) end)",JSCellRender = cellsRender } },
                       {"now_qty_unit",new DataItem(){Title = "调整后数量",Width = "120",CellsAlign = "right",SqlFld = "(case when now_quantity = round(now_quantity) then concat(round(now_quantity),unit_no) else concat(now_quantity,unit_no) end) ",
                            JSCellRender = @"function cellsrenderer_style (row, columnfield, value, defaulthtml, columnproperties) {
                                var old_qty_unit = $('#gridItems').jqxGrid('getcellvalue', row, 'old_qty_unit');
                                if(old_qty_unit != value) return  `<div style=""color:#f00;margin-top:6px;margin-left:4px;text-align:right"">${value}</div>`
                            }"
                       } },

                       {"old_sub_amount",new DataItem(){Title="原余额",Width = "120",CellsAlign = "right" ,JSCellRender = cellsRender } },
                       {"now_sub_amount",new DataItem(){Title = "现余额",Width = "120",CellsAlign = "right",
                       JSCellRender = @"function cellsrenderer_style (row, columnfield, value, defaulthtml, columnproperties) {
                                var old_sub_amount = $('#gridItems').jqxGrid('getcellvalue', row, 'old_sub_amount');
                                if(old_sub_amount != value) return  `<div style=""color:#f00;margin-top:6px;margin-left:4px;text-align:right"">${value}</div>`
                            }"
                       } }
                     },
                     QueryFromSQL=@"from items_ordered_change oc
                                    left join sheet_item_ordered_adjust_main om on om.sheet_id = oc.sheet_id AND om.company_id=~COMPANY_ID
                                    left join (select item_id,item_name from info_item_prop where company_id=~COMPANY_ID) np on np.item_id = oc.item_id
                                    left join info_supcust s on oc.supcust_id = s.supcust_id AND s.company_id=~COMPANY_ID
                                    left join cw_subject pw on oc.prepay_sub_id = pw.sub_id  AND pw.company_id=~COMPANY_ID
                                    where oc.company_id = ~COMPANY_ID ",
                     QueryOrderSQL="order by oc.happen_time desc"
                  }
                } 
            };             
        }
        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class ItemsOrderedAdjustController : QueryController
    { 
        public ItemsOrderedAdjustController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ItemsOrderedAdjustModel model = new ItemsOrderedAdjustModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            ItemsOrderedAdjustModel model = new ItemsOrderedAdjustModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            ItemsOrderedAdjustModel model = new ItemsOrderedAdjustModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
