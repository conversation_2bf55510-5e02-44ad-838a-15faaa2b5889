﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class RanksViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
 
        public RanksViewModel(CMySbCommand cmd) : base(Services.MenuId.infoClient)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                 {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称",UseJQWidgets=false, SqlFld="rank_name",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     IdColumn="i",TableName="info_supcust_rank",
                     ShowContextMenu=true,
                      Columns = new Dictionary<string, DataItem>()
                     {
                       {"i",new DataItem(){Title="等级ID",SqlFld="rank_id",Hidden=true,Width="180",Linkable=true,HideOnLoad = true}},                       
                       {"rank_name",new DataItem(){Title="名称",SqlFld="rank_name",Width="180",Linkable=true}},                       
                       {"rank_note",new DataItem(){Title="描述",Width="80"}},

                     },
                     QueryFromSQL="from info_supcust_rank" ,QueryOrderSQL="order by rank_name"
                  }
                } 
            }; 
        }
        public async Task OnGet(string forSelect)
        {  
            await InitGet(cmd);
            ForSelect = forSelect == "1";
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }

        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            string err = "";
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = $"select count(*) from info_supcust where sup_rank in ('{rowIDs}') and company_id={company_id}";
            QQ.Enqueue("class", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (dr.Read())
                {
                    object ov = dr[0];
                    if (ov != null && ov != DBNull.Value)
                    {
                        int ct = Convert.ToInt32(ov);
                        if (ct > 0)
                        {
                            err = "该等级正在使用,无法删除";
                            break;
                        }
                    }
                }
            }
            QQ.Clear();
            return err;
        }
    }



    [Route("api/[controller]/[action]")]
    public class RanksViewController : QueryController
    { 
        public RanksViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            RanksViewModel model = new RanksViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {// string gridID,int startRow,int endRow,bool bNewQuery){
            RanksViewModel model = new RanksViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }
        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            RanksViewModel model = new RanksViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd, "info_supcust_rank");// gridID, startRow, endRow, bNewQuery);
            return records;
        }
    }
}
