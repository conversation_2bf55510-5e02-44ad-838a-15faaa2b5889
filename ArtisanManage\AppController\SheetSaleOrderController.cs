﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Text;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    /// <summary>
    /// 销售订单
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class SheetSaleOrderController : BaseController
    { 
        public SheetSaleOrderController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="pageSize">分页大小</param>
        /// <param name="startRow">分页开始位置</param>
        /// <param name="getTotal">请在首次查询时使用</param>
        /// <param name="supcust_id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetSheetList(string operKey, int pageSize, int startRow, bool getTotal, string supcust_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where company_id = {companyID}";
            if (supcust_id != null) condi += $"and supcust_id = {supcust_id}";
            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"select sheet_id,sheet_no,happen_time from sheet_sale_order_main {condi} limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                sql = $"select count(sheet_id) as total from sheet_sale_order_main {condi}";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result,msg, data, total });
        }
    }
}
