using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.MyJXC;
using System.IO;
using ArtisanManage.YingJiangCommon.Services;
using HuaWeiObsController;
using Newtonsoft.Json.Linq;
using System.Net.Http;
using ArtisanManage.WebAPI.MessageOSUtil;
using ArtisanManage.YingjiangMessage.Pojo;
using ArtisanManage.YingjiangMessage.Services;
using NPOI.SS.Formula.Functions;

namespace ArtisanManage.AppController
{
    /// <summary>
    /// 一、外勤轨迹查看 （1）查询人员列表  （2）外勤轨 迹
    /// 二、拜访记录查询  --拜访记录
    /// 三、拜访门店  （1）门店信息 （2）门店签到 （3）门店签退
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class SheetVisitController : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public SheetVisitController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd;
            _httpClientFactory = httpClientFactory;
        }

        ///一、外勤轨迹查看
        ///外勤轨迹
        [HttpGet]
        public async Task<JsonResult> GetTrail(string operKey, string seller_id, string happen_day)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where ist.company_id = {companyID}";
            var sql = $"SELECT st.longitude,st.latitude,st.visit_id,st.happen_time," +
                      $"sv.start_time,sv.end_time,sv.door_picture,sv.showcase_pictures," +
                      $"ist.boss_name,ist.sup_name,ist.mobile,ist.status " +
                      $"FROM seller_trail st " +
                      $"LEFT JOIN sheet_visit sv ON sv.visit_id = st.visit_id  and sv.company_id = {companyID} " +
                      $"LEFT JOIN info_supcust ist ON ist.supcust_id=sv.supcust_id and ist.company_id = {companyID} " +
                      $"WHERE st.seller_id = {seller_id} AND to_char(happen_time, 'yyyy-mm-dd') = '{happen_day}' and st.company_id = {companyID}  order by happen_time ASC";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new {result, msg, data});
        }


        ///业务员实时位置查看
        [HttpGet]
        public async Task<JsonResult> GetSellerRealPosition(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            DateTime today = DateTime.Today;
            DateTime nextDay = DateTime.Today.AddDays(1);
            var condi = $"where st.company_id = {companyID}  AND st.happen_time BETWEEN '{today}' AND '{nextDay}'";
            //此处两种写法，看以后查询效率
            /**
            var condi = $"where st.company_id = {companyID}  ";
             SELECT
	        st.happen_time,iop.oper_name,st.seller_id,longitude,latitude FROM seller_trail st
	        RIGHT JOIN ( SELECT seller_id, MAX ( happen_time ) AS happen_time FROM seller_trail where group_st.happen_time BETWEEN '{today}' AND '{nextDay}' GROUP BY seller_id ) group_st ON group_st.seller_id = st.seller_id 
	        AND group_st.happen_time = st.happen_time
	        LEFT JOIN info_operator iop ON iop.oper_id = st.seller_id 
            {condi}
             */
            var sql = $@"SELECT
	        st.happen_time,iop.oper_name,st.seller_id,longitude,latitude FROM seller_trail st
	        RIGHT JOIN ( SELECT seller_id, MAX ( happen_time ) AS happen_time,company_id FROM seller_trail where seller_trail.company_id = {companyID}  GROUP BY seller_id,company_id ) group_st ON group_st.seller_id = st.seller_id  and group_st.company_id = st.company_id
	        AND group_st.happen_time = st.happen_time
	        LEFT JOIN info_operator iop ON iop.oper_id = st.seller_id and iop.company_id = st.company_id
            {condi}";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new {result, msg, data});
        }

        ///二、拜访记录
        ///查询拜访记录
        [HttpGet]
        public async Task<JsonResult> GetVisitRecords(string operKey, string dateType, string shop_id, string seller_id, string visit_region,
            bool have_trade, int startRow, int pageSize)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where sv.company_id={companyID} ";
            var today = DateTime.Today.ToText();
            if (dateType == "month")
            {
                var month = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).ToText();

                condi += $"and sv.start_time >= '{month}'";
            }
            else if (dateType == "yesterday")
            {
                var yesterday = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day - 1).ToText();
                condi += $"and sv.start_time = '{yesterday}' ";
            }
            else if (dateType == "today")
            {
                condi += $"and sv.start_time = '{today}' ";
            }

            SQLQueue QQ = new SQLQueue(cmd);
            var sql =
                $@"SELECT sv.supcust_id,sup_name,sv.start_time,round(CAST((extract(epoch from end_time::timestamp) - extract(epoch from start_time::timestamp)) /60.0 as numeric),1) as duration,
                            COALESCE(sv.sale_amount,0) AS sale_amount,COALESCE(sv.order_amount,0) AS order_amount,sv.showcase_pictures,sv.end_sign_distance
                        FROM sheet_visit as sv
                        LEFT JOIN info_supcust as ss on ss.supcust_id = sv.supcust_id  and ss.company_id = sv.company_id
                        {condi} AND sv.supcust_id = {shop_id} AND sv.seller_id = {seller_id} ORDER BY start_time DESC
                        limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            sql =
                $"SELECT count(visit_id) AS total,sum(case when sale_amount is not null then 1 else 0 end) sale_count,sum(sale_amount) as sale_amount FROM sheet_visit as sv {condi}";
            QQ.Enqueue("total", sql);
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            string saleCount = "", saleAmount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "total")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    saleCount = CPubVars.GetTextFromDr(dr, "sale_count");
                    saleAmount = CPubVars.GetTextFromDr(dr, "sale_amount");
                }
            }

            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new {result, msg, data, total, pageSize, saleAmount, saleCount});
        }


        ///拜访门店
        /// <summary>
        /// 新增拜访记录(只传operKey和supcustID)
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="shop_id"></param>
        /// <param name="supcustID"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetVisitInfo(string operKey, string shop_id, string supcustID, string visitID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            if (shop_id.IsValid()) condi = $"where sm.company_id = {companyID} And sm.supcust_id = {shop_id}";
            if (supcustID.IsValid()) condi = $"where sm.company_id = {companyID} And sm.supcust_id = {supcustID}";
            if (visitID.IsValid()) condi = $"where sm.company_id = {companyID} And sv.visit_id = {visitID}";
            var sql =
                @$"SELECT sv.supcust_id,sup_name,to_char(sv.start_time,'YYYY-MM-DD HH:mm:SS') as lastTime,extract(day FROM (age(now()::date , sv.start_time::date))) as passedDays,sv.""have_trade"" 
                        FROM
                            (SELECT supcust_id,start_time, ""have_trade"", row_number() OVER(ORDER BY start_time DESC) AS row_num  FROM sheet_visit sm {condi} ) sv
                        LEFT JOIN info_supcust ss on sv.supcust_id = ss.supcust_id 
                        WHERE sv.row_num = 1";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("lastVisit", sql);

            sql =
                $@"SELECT t.total_amount AS lastAmount,t.happen_time as lastTime,extract(day FROM (age(now()::date , t.happen_time::date))) as passedDays 
                     FROM
                       (SELECT total_amount,happen_time,row_number() OVER(ORDER BY happen_time DESC) as row_num FROM sheet_sale_main sm {condi}) t 
                    WHERE t.row_num = 1";
            QQ.Enqueue("lastTrade", sql);

            sql = $@"SELECT balance as lastArrearPassedDays,tem.passedDays 
                     FROM
                        (SELECT t.supcust_id,date_part('day',now()::timestamp - t.happen_time::timestamp) as passedDays,row_number() OVER(ORDER BY t.happen_time) as row_num 
                         FROM  
	                         (SELECT supcust_id,happen_time,sum(money_inout_flag*total_amount-paid_amount-disc_amount)>0 as arrears_balance
		                      FROM sheet_sale_main sm {condi} GROUP BY happen_time,supcust_id) t WHERE t.arrears_balance = 't'  ORDER BY happen_time) tem
                    LEFT JOIN arrears_balance as ab on ab.supcust_id=tem.supcust_id 
                    WHERE tem.row_num=1 ";
            QQ.Enqueue("arrears", sql);

            sql = $@"SELECT balance as prepayBalance FROM  prepay_balance sm
                     LEFT JOIN info_supcust t on sm.supcust_id = t.supcust_id {condi}";
            QQ.Enqueue("prepay_balance", sql);
            List<ExpandoObject> lastVisit = null;
            List<ExpandoObject> lastTrade = null;
            List<ExpandoObject> arrears = null;

            var dr = await QQ.ExecuteReaderAsync();
            var prepay_balance = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "lastVisit")
                {
                    lastVisit = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "lastTrade")
                {
                    lastTrade = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "arrears")
                {
                    arrears = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "prepay_balance")
                {
                    if (dr.Read())
                        prepay_balance = CPubVars.GetTextFromDr(dr, "prepayBalance");
                }
            }

            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new {result, msg, lastVisit, lastTrade, arrears, prepay_balance});
        }


        /// <summary>
        /// 拜访门店签到
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SubmitVisitStart([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string) data.operKey, out string companyID, out string operID);
            CDbDealer db = new CDbDealer();
            //db.AddFields(data, "shop_id,longitude,latitude");
            string clientPositionEmpty = data.clientPositionEmpty;
            string latitude = data.latitude;
            string longitude = data.longitude;
            string supcust_id = data.supcust_id;
            string sign_distance = data.sign_distance;
            db.AddFields(data, "supcust_id,longitude,latitude,start_time,day_id");
            db.AddField("company_id", companyID);
            db.AddField("seller_id", operID);
            db.AddField("sign_distance", sign_distance);
            string visit_sql = db.GetInsertSQL("sheet_visit") + " returning visit_id";
            string trail_sql = "";
            if (data.longitude != null && data.latitude != null)
            {
                trail_sql = $"INSERT INTO seller_trail(company_id,seller_id,longitude,latitude,happen_time,visit_id) VALUES ({companyID},{operID},{data.longitude},{data.latitude},'{DateTime.Now.ToText()}'," +
                    "@visit_id);";
            }
            string sql = $@"SELECT yj_exeSqlByInsertedRowID('{visit_sql.Replace("'", "''")}','{trail_sql.Replace("'", "''")}','@visit_id');";
            sql += $@"INSERT INTO oper_activity(company_id, oper_id, happen_date, visit_times) VALUES ({companyID}, {operID}, '{DateTime.Now.ToString("yyyy-MM-dd")}', 1) 
                        ON CONFLICT(company_id, oper_id, happen_date) DO UPDATE SET visit_times = oper_activity.visit_times + 1;";

            if (clientPositionEmpty == "true")
            {
                if (latitude.IsValid() && longitude.IsValid())
                {
                    sql +=
                        $"update info_supcust set addr_lat='{latitude}',addr_lng='{longitude}',addr_lnglat=ST_MakePoint({longitude},{latitude}) where company_id={companyID} and supcust_id={supcust_id};";
                }

                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Info("in SubmitVisitStart:sql=" + sql);
            }


            string realTime = $"{{\"lastVisitTime\":\"{CPubVars.GetDateText(DateTime.Now)}\"}}";
            string realtimeSql =
                $"insert into realtime_supcust(company_id,supcust_id,realtime) values ({companyID},{supcust_id},'{realTime}') on conflict(company_id,supcust_id) do update set realtime=realtime_supcust.realtime||'{realTime}'::jsonb;";
            sql += realtimeSql;

            cmd.company_id = companyID;
            cmd.CommandText = sql;
            object ov = await cmd.ExecuteScalarAsync();
            string visit_id = "";
            if (ov != null && ov != DBNull.Value) visit_id = ov.ToString();
            visit_id = visit_id.Split(",")[0];
            return Json(new {result = "OK", msg = "", visit_id});
        }
        [HttpPost]
        public async Task<JsonResult> RemoveVisitRecord([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID,out string operID);
            string visitCountSQL = $@"
           
            SELECT sheet_id FROM sheet_sale_main 
                WHERE happen_time between '{DateTime.Now.ToString("yyyy-MM-dd") + " 00:00"}' AND '{DateTime.Now.ToString("yyyy-MM-dd") + " 23:59"}' 
                AND visit_id = '{data.visitID}' AND company_id = {companyID}
            
            UNION
               
            SELECT sheet_id FROM sheet_sale_order_main 
                WHERE happen_time between '{DateTime.Now.ToString("yyyy-MM-dd") + " 00:00"}' AND '{DateTime.Now.ToString("yyyy-MM-dd") + " 23:59"}' 
                AND visit_id = '{data.visitID}' AND company_id = {companyID}
          
            ";
            dynamic res = await CDbDealer.Get1RecordFromSQLAsync(visitCountSQL, cmd);
            if(res != null)
            {
                return Json(new { result = "Error", msg = "本次拜访已开单,无法撤销!" });
            }
            string curVisitSQL =@$"select supcust_id from sheet_visit   WHERE  company_id = {companyID} and visit_id = '{data.visitID}'";
            dynamic curVisitData = await CDbDealer.Get1RecordFromSQLAsync(curVisitSQL, cmd);

            string sql = $@"DELETE FROM sheet_visit WHERE visit_id = '{data.visitID}' AND company_id = {companyID} AND seller_id = {operID}";
            cmd.CommandText = sql;
            await cmd.ExecuteScalarAsync();
            if (curVisitData != null)
            {
                var supcust_id = curVisitData.supcust_id;
                //查询从拜访表里捞上次拜访数据
                string lastVisitSQL = $@"SELECT start_time from sheet_visit where company_id = {companyID} AND supcust_id = {supcust_id} order by start_time desc limit 1";
                dynamic lastVisitData = await CDbDealer.Get1RecordFromSQLAsync(lastVisitSQL, cmd);
                var updateRealtimeSQL = "";
                //恢复上次拜访数据
                if (lastVisitData != null)
                {
                    updateRealtimeSQL = $@"UPDATE realtime_supcust
                                    SET realtime=(realtime::jsonb ||'{{""lastVisitTime"":""{lastVisitData.start_time}""}}')
                                    WHERE company_id = {companyID} and supcust_id = '{supcust_id}';";
                }
                else
                {
                    updateRealtimeSQL = $@"UPDATE realtime_supcust
                                        SET realtime = realtime - 'lastVisitTime'
                                        WHERE company_id = {companyID} and supcust_id = '{supcust_id}'";
                }
                cmd.CommandText = updateRealtimeSQL;
                await cmd.ExecuteScalarAsync();
            }


            return Json(new { result = "OK", msg = "" });

        }

        /// <summary>
        /// 拜访门店签退 
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        //todo 验证是否有数据传入
        [HttpPost]
        public async Task<JsonResult> SubmitVisitEnd([FromBody] dynamic data)
        {
            // NLogger.Info("Submit visit end");
            string visit_id = data.visit_id;
            string supcust_id = data.supcust_id;
            string sup_door_photo = data.sup_door_photo;
            string door_picture = data.door_picture;
            JArray showcase_pictures = data.showcase_pictures;
            string operKey = data.operKey;
            string templateResult = data.templateResult;
            string supName = data.supName;
            string sellerDept = data.sellerDept;
            string supGroup = data.supGroup;
            string supRank = data.supRank;
            string startTime = data.start_time;
            string endTime = data.end_time;
            //data.end_sign_distance = data.end_sign_distance;
            string submitVisitEndSql = @$"";
            // NLogger.Info("Submit visit end 2");
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            CMySbTransaction tran = null;
            //   NLogger.Info("Submit visit end 3");
            //  NLogger.Info("Submit visit end 4, data:" +JsonConvert.SerializeObject(data));
            try
            {
                if (!string.IsNullOrEmpty(door_picture))
                {
                    var upload_door_pic_res = await process_door_pic_return_dbstring(door_picture, visit_id, companyID);
                    if (upload_door_pic_res.result != "OK")
                    {
                        MyLogger.LogMsg(
                            $"in subVisitEnd,process_door_pic_return_dbstring return msg:{upload_door_pic_res.msg},door_picture_base64:{door_picture}",
                            companyID);
                        return Json(new {upload_door_pic_res.result, upload_door_pic_res.msg});
                    }

                    string door_pic_indb = "";
                    if (upload_door_pic_res.urls.Count > 0) door_pic_indb = upload_door_pic_res.urls[0];
                    data.door_picture = door_pic_indb;
                }

                if (showcase_pictures != null)
                {
                    List<string> showcase_pictures_base64_list = showcase_pictures.ToObject<List<string>>();
                    var upload_display_pic_res = await process_display_pic_return_dbstring(showcase_pictures_base64_list, visit_id, companyID);
                    if (upload_display_pic_res.result != "OK")
                    {
                        MyLogger.LogMsg(
                            $"in subVisitEnd,process_display_pic_return_dbstring return msg:{upload_display_pic_res.msg},display_pic_base64:{JsonConvert.ToString(showcase_pictures_base64_list)}",
                            companyID);
                        return Json(new {upload_display_pic_res.result, upload_display_pic_res.msg});
                    }

                    string showcase_pictures_indb = JsonConvert.SerializeObject(upload_display_pic_res.urls);
                    data.showcase_pictures = showcase_pictures_indb;
                }

                string sup_door_photo_indb = "";
                if (!string.IsNullOrEmpty(sup_door_photo))
                {
                    var upload_sup_door_photo_res = await process_sup_pic_return_dbstring(sup_door_photo, companyID);
                    if (upload_sup_door_photo_res.result != "OK")
                    {
                        MyLogger.LogMsg(
                            $"in subVisitEnd,process_sup_pic_return_dbstring return msg:{upload_sup_door_photo_res.msg},sup_pic_base64:{sup_door_photo}",
                            companyID);
                        return Json(new {upload_sup_door_photo_res.result, upload_sup_door_photo_res.msg});
                    }

                    if (upload_sup_door_photo_res.urls.Count > 0)
                    {
                        sup_door_photo_indb = upload_sup_door_photo_res.urls[0];
                    }
                }

                List<dynamic> workContentList = new List<dynamic>();
                // 处理拜访模板的数据
                if (!string.IsNullOrEmpty(templateResult))
                {
                    // 转为json
                    // NLogger.Info("Submit visit end 5");
                    dynamic jsonTemplate = JsonConvert.DeserializeObject(templateResult);
                    // 循环取出每一项
                    for (int i = 0; i < jsonTemplate.Count; i++)
                    {
                        dynamic jsonTemplateItem = jsonTemplate[i];
                        //   NLogger.Info("Submit visit end 6 ");

                        //NLogger.Info("Submit visit end 6 jsonTemplateItem:" + JsonConvert.SerializeObject(jsonTemplate));
                        if (jsonTemplateItem.action.type == "photo")
                        {
                            //  NLogger.Info("Submit visit end 6 a");
                            dynamic mandatory = jsonTemplateItem.actionResult.mandatory; // 必选图片
                            //  NLogger.Info("Submit visit end 6 b mandatory:" + JsonConvert.SerializeObject(mandatory));
                            //  200111/company_id _ oper_id _ sup_id _ 20211130034022.jpg
                            DateTime dt = DateTime.Now;
                            for (int index = 0; index < mandatory.Count; index++)
                            {
                                // NLogger.Info($"Submit visit end for loop {index}");
                                if (index >= jsonTemplateItem.actionResult.mandatoryName.Count)
                                {
                                    //NLogger.Info($"Submit visit end for continue");
                                    continue;
                                }
                                string imgBase64 = mandatory[index];
                                string obsString = HuaWeiObs.BucketLinkHref + "/";
                                // 兼容没有obs的情况
                                if (imgBase64.StartsWith(obsString))
                                {
                                    jsonTemplateItem.actionResult.mandatoryName[index] = imgBase64.Replace(obsString, "");;
                                }
                                else
                                {
                                    dt = DateTime.Now;
                                    string mm = dt.ToString("yyyMM");
                                    string ss = dt.ToString("yyyMMddHHmmss");
                                    using (var image = HuaWeiObs.Base64ToImage(imgBase64))
                                    {
                                        string path = $"uploads/{mm}/{companyID}_{operID}_{supcust_id}_a{i}m{index}_{ss}.jpg";
                                        jsonTemplateItem.actionResult.mandatoryName[index] = path;
                                        string err = await HuaWeiObs.Save(_httpClientFactory, image.Image, path);
                                        if (err != "")
                                        {
                                            return Json(new {result = "Error", msg = err});
                                        }
                                    }
                                }
                            }

                            dynamic optional = jsonTemplateItem.actionResult.optional; // 可选图片
                            for (int index = 0; index < optional.Count; index++)
                            {
                                if (index >= jsonTemplateItem.actionResult.optionalName.Count) continue;
                                string imgBase64 = optional[index];
                                string obsString = HuaWeiObs.BucketLinkHref + "/";
                                // 兼容没有obs的情况
                                if (imgBase64.StartsWith(obsString))
                                {
                                    jsonTemplateItem.actionResult.optionalName[index]  = imgBase64.Replace(obsString, "");;
                                }
                                else
                                {
                                    dt = DateTime.Now;
                                    string mm = dt.ToString("yyyMM");
                                    string ss = dt.ToString("yyyMMddHHmmss");
                                    using (var image = HuaWeiObs.Base64ToImage(imgBase64))
                                    {
                                        string path = $"uploads/{mm}/{companyID}_{operID}_{supcust_id}_a{i}o{index}_{ss}.jpg";
                                        jsonTemplateItem.actionResult.optionalName[index] = path;
                                        string err = await HuaWeiObs.Save(_httpClientFactory, image.Image, path);
                                        if (err != "")
                                        {
                                            return Json(new {result = "Error", msg = err});
                                        }
                                    }
                                }
                            }

                            //  NLogger.Info("Submit visit end 10");
                            Dictionary<string, dynamic> item = new Dictionary<string, dynamic>();
                            item.Add("action", jsonTemplateItem.action);
                            Dictionary<string, dynamic> work_content_temp = new Dictionary<string, dynamic>();
                            work_content_temp.Add("mandatory", jsonTemplateItem.actionResult.mandatoryName);
                            work_content_temp.Add("optional", jsonTemplateItem.actionResult.optionalName);
                            item.Add("work_content", work_content_temp);
                            workContentList.Add(item);
                        }
                        else if (jsonTemplateItem.action.type == "brief")
                        {
                            //  NLogger.Info("Submit visit end 6 c");
                            Dictionary<string, dynamic> item = new Dictionary<string, dynamic>();
                            item.Add("action", jsonTemplateItem.action);
                            item.Add("work_content", jsonTemplateItem.actionResult);
                            workContentList.Add(item);
                        }
                        //  NLogger.Info("Submit visit end 11");
                    }
                }

                // NLogger.Info("Submit visit end 12");
                CDbDealer db = new CDbDealer();
                db.AddFields(data, "end_time,door_picture,showcase_pictures,remark,end_longitude,end_latitude,end_sign_distance");
                if (workContentList.Count > 0)
                {
                    string workContentJson = JsonConvert.SerializeObject(workContentList);
                    db.AddField("work_content", workContentJson);
                }

                // List<dynamic> displayMaintainActionList = new List<dynamic>();
                submitVisitEndSql = db.GetUpdateSQL("sheet_visit", $"company_id={companyID} and visit_id={visit_id}") + ";";
                if (!string.IsNullOrEmpty(supcust_id) && !string.IsNullOrEmpty(sup_door_photo_indb))
                {
                    string sqlClientPic = @$"
                    update info_supcust set sup_door_photo ='{sup_door_photo_indb}' 
                    where company_id ={companyID} and supcust_id={supcust_id} and coalesce(sup_door_photo,'')=''";
                    if (!submitVisitEndSql.EndsWith(";")) submitVisitEndSql += ";";
                    submitVisitEndSql += sqlClientPic;
                }

                // 由于此时是只读服务器，在进行自动切换的时候就会出现问题，导致tran.Commit(); 出错。
                cmd.ActiveDatabase = "";
                tran = cmd.Connection.BeginTransaction();
                // 处理维护
                string displayMaintainAction = data.displayMaintainAction;
                if (!string.IsNullOrEmpty(displayMaintainAction))
                {
                    string sql = "";
                    dynamic jsonDisplayMaintainSheets = JsonConvert.DeserializeObject(displayMaintainAction);
                    string happenTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    for (int i = 0; i < jsonDisplayMaintainSheets.Count; i++)
                    {
                        dynamic jsonDisplayMaintainSheet = jsonDisplayMaintainSheets[i]; // 每个单据的情况
                        string dispTempId = jsonDisplayMaintainSheet.disp_temp_id;
                        string dispSheetNo = jsonDisplayMaintainSheet.disp_sheet_no;
                        string dispSheetId = jsonDisplayMaintainSheet.disp_sheet_id;
                        string subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey;
                        string supId = jsonDisplayMaintainSheet.client_id;
                        string uploadsMainPath = $@"{companyID}_{subType}_{operID}_{supId}_{visit_id}_{dispSheetId}";
                        if (!supId.Equals(supcust_id))
                        {
                            continue;
                        }
                        string workContentParam = jsonDisplayMaintainSheet.work_content;
                        string workContentResult = await ActionsTemplateUtils.HandleActionTemplate(workContentParam, uploadsMainPath, _httpClientFactory);
                        sql +=
                            @$"INSERT INTO op_display_maintain (company_id, oper_id, client_id, disp_temp_id, disp_sheet_id, work_content, happen_time, visit_id) 
                                                                VALUES ('{companyID}', '{operID}', '{supId}', '{dispTempId}', '{dispSheetId}', '{workContentResult}', '{happenTime}', '{visit_id}');";
                        
                        bool maintainNeedReview = jsonDisplayMaintainSheet.maintain_need_review;
                        if (maintainNeedReview)
                        {
                            await MessageCreateServices.CreateMessageService(new
                            {
                                operKey,
                                createrId = operID,
                                msgClass = MessageType.ClassType.Todo,
                                msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                                msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey,
                                receiverId = "",
                                visitID = visit_id,
                                dispSheetID = dispSheetId,
                                supcustID = supcust_id,
                                sellerDept,
                                supGroup,
                                supRank,
                                sellerId = operID,
                                msgTitle = @$"{supName}的陈列单据{dispSheetNo}进行了维护，请尽快复核",
                                startTime,
                                endTime
                            }, cmd);
                        }
                        else
                        {
                            if (!sql.EndsWith(";")) sql += ";";
                            int maintainTimes = jsonDisplayMaintainSheet.maintain_times;
                            string sumMaintainId = jsonDisplayMaintainSheet.sum_maintain_id;
                            string months = DateTime.Parse(happenTime).ToString("yyyy-MM") + "-01 00:00:00";
                            if (maintainTimes == 0)
                            {
                                sql += $@"INSERT INTO sum_display_maintain (company_id, client_id, disp_temp_id, disp_sheet_id, maintain_times, months)
VALUES ('{companyID}', '{supId}','{dispTempId}', '{dispSheetId}', 1, '{months}');";
                            }
                            else
                            {
                                sql += $@"UPDATE sum_display_maintain SET maintain_times = sum_display_maintain.maintain_times + 1 WHERE company_id = '{companyID}' AND sum_maintain_id = '{sumMaintainId}';";
                            }
                        }
                        
                    }
                    if (!submitVisitEndSql.EndsWith(";")) submitVisitEndSql += ";";
                    submitVisitEndSql += sql;
                    
                }

                string displayKeepAction = data.displayKeepAction;
                if (!string.IsNullOrEmpty(displayKeepAction))
                {
                    string sql = "";
                    dynamic jsonDisplayKeepActionSheets = JsonConvert.DeserializeObject(displayKeepAction);
                    string happenTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    for (int i = 0; i < jsonDisplayKeepActionSheets.Count; i++)
                    {
                        dynamic jsonDisplayKeepSheet = jsonDisplayKeepActionSheets[i]; // 每个单据的情况
                        string dispTempId = jsonDisplayKeepSheet.disp_template_id;
                        string dispSheetId = jsonDisplayKeepSheet.disp_sheet_id;
                        string dispSheetNo = jsonDisplayKeepSheet.disp_sheet_no;
                        string supId = jsonDisplayKeepSheet.supcust_id;
                        if (!supId.Equals(supcust_id))
                        {
                            continue;
                        }
                        string workContentParam = jsonDisplayKeepSheet.work_content;
                        string subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayKeepSubType.SubTypeKey;
                        string uploadsMainPath = $@"{companyID}_{subType}_{operID}_{supId}_{visit_id}_{dispSheetId}";
                        string workContentResult = await ActionsTemplateUtils.HandleActionTemplate(workContentParam, uploadsMainPath, _httpClientFactory);
                        sql +=
                            @$"INSERT INTO op_display_keep (company_id, oper_id, client_id, disp_temp_id, disp_sheet_id, work_content, happen_time, visit_id) 
                                                                VALUES ('{companyID}', '{operID}', '{supId}', '{dispTempId}', '{dispSheetId}', '{workContentResult}', '{happenTime}', '{visit_id}');";
                        
                        bool keepNeedReview = jsonDisplayKeepSheet.keep_need_review;
                        if (keepNeedReview)
                        {
                            await MessageCreateServices.CreateMessageService(new
                            {
                                operKey,
                                createrId = operID,
                                msgClass = MessageType.ClassType.Todo,
                                msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                                msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayKeepSubType.SubTypeKey,
                                receiverId = "",
                                visitID = visit_id,
                                dispSheetID = dispSheetId,
                                supcustID = supcust_id,
                                sellerDept,
                                supGroup,
                                supRank,
                                sellerId = operID,
                                msgTitle = @$"{supName}的陈列单据{dispSheetNo}进行了续签，请尽快复核",
                                startTime,
                                endTime
                            }, cmd);
                        }
                        else
                        {
                            if (!sql.EndsWith(";")) sql += ";";
                            int keepTimes = jsonDisplayKeepSheet.keep_times;
                            int needKeepTimes = jsonDisplayKeepSheet.need_keep_times;
                            string sumKeepId = jsonDisplayKeepSheet.sum_keep_id;
                            if (keepTimes == 0)
                            {
                                sql += $@"INSERT INTO sum_display_keep (company_id, client_id, disp_temp_id, disp_sheet_id, keep_times, need_keep_times)
VALUES ('{companyID}', '{supId}','{dispTempId}', '{dispSheetId}', 1, {needKeepTimes});";
                            }
                            else
                            {
                                sql += $@"UPDATE sum_display_keep SET keep_times = sum_display_keep.keep_times + 1 WHERE company_id = '{companyID}' AND sum_keep_id = '{sumKeepId}';";
                            }
                        }
                    }
                    if (!submitVisitEndSql.EndsWith(";")) submitVisitEndSql += ";";
                    submitVisitEndSql += sql;
                }
                
                // 处理补录
                string displayForMonthMaintainInfoAction = data.displayForMonthMaintainInfoAction;
                if (!string.IsNullOrEmpty(displayForMonthMaintainInfoAction))
                {
                    string sql = "";
                    dynamic jsonAction = JsonConvert.DeserializeObject(displayForMonthMaintainInfoAction);
                    string happenTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    for (int i = 0; i < jsonAction.Count; i++)
                    {
                        dynamic forMonthMaintainSheet = jsonAction[i]; // 每个单据的情况
                        string dispTempId = forMonthMaintainSheet.disp_temp_id;
                        string dispSheetNo = forMonthMaintainSheet.disp_sheet_no;
                        string dispSheetId = forMonthMaintainSheet.disp_sheet_id;
                        string subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey;
                        string supId = forMonthMaintainSheet.client_id;
                        string forMonth = forMonthMaintainSheet.forMonth;
                        string forMonthDate = DateTime.Parse(forMonth).ToString("yyyy-MM") + "-01 00:00:00";
                        string uploadsMainPath = $@"{companyID}_{subType}_{operID}_{supId}_{visit_id}_{dispSheetId}_{forMonth}_{i}";
                        if (!supId.Equals(supcust_id))
                        {
                            continue;
                        }
                        string workContentParam = forMonthMaintainSheet.work_content;
                        string workContentResult = await ActionsTemplateUtils.HandleActionTemplate(workContentParam, uploadsMainPath, _httpClientFactory);
                        sql +=
                            @$"INSERT INTO op_display_maintain (company_id, oper_id, client_id, disp_temp_id, disp_sheet_id, work_content, happen_time, visit_id, for_month) 
                                                                VALUES ('{companyID}', '{operID}', '{supId}', '{dispTempId}', '{dispSheetId}', '{workContentResult}', '{happenTime}', '{visit_id}', '{forMonthDate}');";
                        
                        bool maintainNeedReview = forMonthMaintainSheet.maintain_need_review;
                        if (maintainNeedReview)
                        {
                            await MessageCreateServices.CreateMessageService(new
                            {
                                operKey,
                                createrId = operID,
                                msgClass = MessageType.ClassType.Todo,
                                msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                                msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey,
                                receiverId = "",
                                visitID = visit_id,
                                dispSheetID = dispSheetId,
                                supcustID = supcust_id,
                                sellerDept,
                                supGroup,
                                supRank,
                                sellerId = operID,
                                msgTitle = @$"{supName}的陈列单据{dispSheetNo}进行了{forMonth}补录维护，请尽快复核",
                                startTime,
                                endTime,
                                uploadsMainPath //用于补录的查找
                            }, cmd);
                        }
                        else
                        {
                            // 无需复核，对统计表进行更新
                            if (!sql.EndsWith(";")) sql += ";";
                            sql += @$"
INSERT INTO sum_display_maintain (company_id, client_id, disp_temp_id, disp_sheet_id, maintain_times, months)
VALUES ('{companyID}', '{supId}','{dispTempId}', '{dispSheetId}', 1, '{forMonthDate}')
on conflict (company_id, client_id, disp_sheet_id, months) do update 
set maintain_times = sum_display_maintain.maintain_times + 1;";
                        }
                    }
                    if (!submitVisitEndSql.EndsWith(";")) submitVisitEndSql += ";";
                    submitVisitEndSql += sql;
                }

                cmd.CommandText = submitVisitEndSql;
                // NLogger.Info("Submit visit end 13");
                await cmd.ExecuteNonQueryAsync();
                tran.Commit();
            }
            catch (Exception e)
            {
                tran.Rollback();
                MyLogger.LogMsg($"in subVisitEnd, exception met, e.message: {e.Message},stackTrace:{e.StackTrace},commandText:{cmd.CommandText}", companyID);
                NLogger.Error($"in subVisitEnd,companyID{companyID} exception met, e.message: {e.Message},stackTrace:{e.StackTrace}");
                return Json(new {result = "Error", msg = "签退发生异常,请联系客服"});
            }

            return Json(new {result = "OK", msg = ""});
        }

        public async Task<UploadImageResult> process_door_pic_return_dbstring(string door_picture_base64, string visit_id, string companyID)
        {
            var res = new UploadImageResult
            {
                result = "OK"
            };

            // 2023.05.12 处理新版本APP拍一张上传一张时的签退逻辑
            if (!HuaWeiObs.IsB64(door_picture_base64) && door_picture_base64.StartsWith(HuaWeiObs.BucketLinkHref))
            {
                var db_url = door_picture_base64.Replace($"{HuaWeiObs.BucketLinkHref}/uploads", "");
                res.urls.Add(db_url);
                return res;
            }

            NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();

            door_picture_base64 = door_picture_base64.Replace("data:image/jpeg;base64,", "");

            if (string.IsNullOrEmpty(door_picture_base64))
            {
                return res;
            }

            // 20220507upd: local save changed to obs-bucket save.            
            //string folderPath = Environment.CurrentDirectory + $"/wwwroot/uploads/{DateTime.Today:yyyyMM}/";
            string dt = $"{DateTime.Today:yyyyMM}"; // 预防23点59分保存的情况，dt设为固定值
            string folderPath = $"uploads/{dt}/";
            //if (!System.IO.Directory.Exists(folderPath))
            //{
            //    System.IO.Directory.CreateDirectory(folderPath);
            //}
            string doorPicName = $"door_pic_{visit_id}_{companyID}_{CommonTool.GetTimeStamp()}";
            string fileExtension = ".jpeg";
            string path = folderPath + doorPicName + fileExtension;

            using (MemoryStream stream = new MemoryStream(Convert.FromBase64String(door_picture_base64)))
            {
                try
                {
                    await HuaWeiObs.Save(_httpClientFactory, stream, path);
                    logger.Info("in process_door_pic_return_dbstring:saved");
                    string door_picture_indb = $"/{dt}/{doorPicName}{fileExtension}";
                    res.urls.Add(door_picture_indb);
                    return res;
                }
                catch (Exception err)
                {
                    stream.Close();
                    logger.Info($"in process_door_pic_return_dbstring,err:{err}:door_picture_base64:" + door_picture_base64);
                    res.result = "Error";
                    res.msg = "传门头照出错:";
                    return res;
                }
                finally
                {
                }
            }

            //CommonTool.ConvertBase64ToImage(door_picture_base64, folderPath, doorPicName, fileExtension);
            //return door_picture_indb;
        }

        public class UploadImageResult
        {
            public string result = "", msg = "";
            public List<string> urls = new List<string>();
        }

        public async Task<UploadImageResult> process_sup_pic_return_dbstring(string door_picture_base64, string companyID)
        {
            var res = new UploadImageResult
            {
                result = "OK"
            };

            if (string.IsNullOrEmpty(door_picture_base64))
            {
                return res;
            }


            door_picture_base64 = door_picture_base64.Replace("data:image/jpeg;base64,", "");


            string dt = $"{DateTime.Today:yyyyMM}"; // 预防23点59分保存的情况，dt设为固定值
            string folderPath = $"uploads/{dt}/";

            string doorPicName = $"sup_pic_{companyID}_{CommonTool.GetTimeStamp()}_{new Random().Next(1, 100)}";
            string fileExtension = ".jpeg";
            string path = folderPath + doorPicName + fileExtension;

            using (MemoryStream stream = new MemoryStream(Convert.FromBase64String(door_picture_base64)))
            {
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                //logger.Info($"in process_sup_pic_return_dbstring,door_picture_base64 is:" + door_picture_base64);

                try
                {
                    await HuaWeiObs.Save(_httpClientFactory, stream, path);
                    logger.Info("in process_sup_pic_return_dbstring:saved");
                    string door_picture_indb = $"/{dt}/{doorPicName}{fileExtension}";
                    res.urls.Add(door_picture_indb);
                    return res;
                }
                catch (Exception err)
                {
                    stream.Close();
                    logger.Info($"in process_sup_pic_return_dbstring,err:{err}:door_picture_base64:" + door_picture_base64);
                    res.result = "Error";
                    res.msg = "上传客户照片出错";
                    return res;
                }
                finally
                {
                }
            }


            //CommonTool.ConvertBase64ToImage(door_picture_base64, folderPath, doorPicName, fileExtension);
            //return door_picture_indb;
        }

        public async Task<UploadImageResult> process_display_pic_return_dbstring(List<string> showcase_pictures_base64, string visit_id,
            string companyID)
        {
            var res = new UploadImageResult
            {
                result = "OK"
            };

            if (showcase_pictures_base64.Count == 0)
            {
                return res;
            }

            int uploadPicIdx = 0;
            // 20220507upd: local save changed to obs-bucket save.
            //string folderPath = Environment.CurrentDirectory + $"/wwwroot/uploads/{DateTime.Today:yyyyMM}/";
            string dt = $"{DateTime.Today:yyyyMM}"; // 预防23点59分保存的情况，dt设为固定值
            string folderPath = $"uploads/{dt}/";

            bool allSuccess = true;
            string errs = "";
            List<string> paths = new List<string>();
            foreach (string pic in showcase_pictures_base64)
            {
                if (!HuaWeiObs.IsB64(pic) && pic.StartsWith(HuaWeiObs.BucketLinkHref))
                {
                    var db_url = pic.Replace($"{HuaWeiObs.BucketLinkHref}/uploads", "");
                    paths.Add(db_url);
                }
                else
                {
                    string showcase_picture = pic.Replace("data:image/jpeg;base64,", "");
                    uploadPicIdx++;
                    string fileName = $"{visit_id}_{companyID}_{CommonTool.GetTimeStamp()}_{uploadPicIdx}";
                    string fileExtension = ".jpeg";
                    using (MemoryStream stream = new MemoryStream(Convert.FromBase64String(showcase_picture)))
                    {
                        string path = folderPath + fileName + fileExtension;
                        try
                        {
                            await HuaWeiObs.Save(_httpClientFactory, stream, path);
                        }
                        catch (Exception err)
                        {
                            stream.Close();
                            allSuccess = false;
                            errs += err;
                        }
                        finally
                        {

                        }
                    }
                    paths.Add($"/{dt}/{fileName}{fileExtension}");
                }
            }
        
            res.urls = paths.ToList();
            if (!allSuccess)
            {
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Info($"in process_display_pic_return_dbstring,err:{errs}:");
                res.result = "Error";
                res.msg = "传陈列失败:";
            }

            return res;
        }

        /// <summary>
        /// 获取业务员列表
        /// </summary>
        /// <param name="operKey"> </param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetSellers(string operKey, string operID, string queryDay)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where company_id = {companyID}";
            if (operID != null) condi += $"oper_id = {operID}";
            var sql = $@"SELECT 
                        (SELECT count(1) from sheet_visit where seller_id=info_operator.oper_id and start_time BETWEEN '{queryDay} 00:00' and '{queryDay} 23:59')as visit_count,
                        oper_id,oper_name,status FROM info_operator {condi}  and COALESCE(status,'1')='1'";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new {result, msg, data});
        }

        /// <summary>
        /// 保存轨迹
        /// </summary>
        /// <param name="data">{"operKey":"bIuYnVotW7J33Q8abhxoBzLx-HlRyzltkJ10Eqfo87IWpKgtDVFJm6v-7l9arOEsnzTie3RHPCpwdJudsSMyryEboWsPKh-I","longitude":"100.25","latitude":"12.5"}</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SaveSellerTrail([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string) data.operKey, out string companyID, out string operID);
            CDbDealer db = new CDbDealer();
            var currentTime = DateTime.Now.ToText();
            db.AddFields(data, "longitude,latitude");
            db.AddField("company_id", companyID);
            db.AddField("seller_id", operID);
            db.AddField("happen_time", currentTime);
            string sql = db.GetInsertSQL("seller_trail");
            cmd.CommandText = sql;
            cmd.company_id = companyID;
            await cmd.ExecuteNonQueryAsync();
            return Json(new {result = "OK", msg = ""});
        }

        [HttpPost]
        public async Task<JsonResult> SaveTrailsAfterNetworkFail([FromBody] dynamic data)
        {
            string operKey = data[0].operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            CDbDealer db = new CDbDealer();
            //  db.AddFields(data, "longitude,latitude");
            //  db.AddField("company_id", companyID);
            //  db.AddField("seller_id", operID);
            //  db.AddField("happen_time", currentTime);
            string sql = @$"INSERT INTO seller_trail(company_id,seller_id,longitude,latitude,happen_time) VALUES ";
            List<string> valStatements = new List<string>();

            foreach (var trail in data)
            {
                var valStatement = @$"({companyID},{operID},{trail.longitude},{trail.latitude},{trail.happen_time})";
                valStatements.Add(valStatement);
            }

            string valSql = string.Join(",", valStatements);
            cmd.CommandText = sql + valSql;
            cmd.company_id = companyID;
            await cmd.ExecuteNonQueryAsync();
            return Json(new {result = "OK", msg = ""});
        }

        /// <summary>
        /// 业务员拜访详情列表
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="supcust_id"></param>
        /// <param name="shop_id"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="seller_id"></param>
        /// <param name="sellerOrder"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <returns></returns>
        /*
        [HttpGet]
        public async Task<JsonResult> GetVisitDetailLists(string operKey, string supcust_id,string startTime,string endTime,string seller_id,bool sellerOrder,int pageSize,int startRow)
        {
            bool firstRequest = false;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $" where v.company_id = {companyID} ";
            string orderSql = $" order by start_time desc ";
            if (sellerOrder) orderSql = $" order by oper_name,start_time desc ";
            if (startTime != null && endTime != null) condi += $" and start_time>='{startTime}' and start_time<='{endTime}' ";
            if (seller_id != null) condi += $" and v.seller_id= {seller_id} ";
            if (supcust_id != null) condi += $" and v.supcust_id= {supcust_id} ";
            //if (startRow == 0) firstRequest = true;
            SQLQueue QQ = new SQLQueue(cmd);
            string sel_sql = @"visit_id,bool_or((case when sub_amount=0 then true else false end)) has_free,bool_or((case when sub_amount<0 then true else false end)) has_return,
                                bool_or((case when (total_amount-now_pay_amount-now_disc_amount > 0) then true else false end))  has_arrears,
                                bool_or((case when now_disc_amount>0 then true else false end)) has_disc ";
            
            string sql_noLimit = $@" select v.visit_id,v.remark,sale_order_total_amount,v.seller_id,oper_name,v.supcust_id,sup_name,start_time,door_picture,showcase_pictures,EXTRACT(epoch from age(end_time,start_time))/60 as intervals,
                                            sale_total_amount total_amount,has_free,has_return,has_arrears,has_disc 
                                     from sheet_visit v 
                                     left join (select s.supcust_id,sup_name from info_supcust s where s.company_id = {companyID}) s on s.supcust_id = v.supcust_id
                                     left join (select visit_id,bool_or(has_free) has_free,bool_or(has_return) has_return,bool_or(has_arrears) has_arrears,bool_or(has_disc) has_disc,sum(sale_order_total_amount) sale_order_total_amount,sum(sale_total_amount) sale_total_amount  
                                                  from (
                                                        (select {sel_sql},total_amount sale_total_amount,0 sale_order_total_amount from sheet_sale_main m left join sheet_sale_detail d on d.sheet_id = m.sheet_id where m.company_id = {companyID} and approve_time is not null and red_flag is null group by visit_id,total_amount)
                                                         UNION 
                                                        (select {sel_sql},0 sale_total_amount,total_amount sale_order_total_amount from sheet_sale_order_main m left join sheet_sale_order_detail d on d.sheet_id = m.sheet_id where m.company_id = {companyID} and approve_time is not null and red_flag is null group by visit_id,total_amount)
                                                       ) t group by visit_id ) t on v.visit_id = t.visit_id 
                                     left join (select oper_id,oper_name from info_operator where company_id = {companyID}) o on v.seller_id = o.oper_id
                                     {condi}";
            string sql = sql_noLimit + $"{orderSql} limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);
            if (firstRequest)
            {
                sql = $"select count(*) as itemCount from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
            }
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count" && firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, itemCount });
        }
        */
        [HttpGet]
        public async Task<JsonResult> GetVisitDetailLists(string operKey, string supcust_id, string startTime, string endTime, string seller_id,string depart_id, bool sellerOrder, int pageSize, int startRow, string regionsID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $" where v.company_id = {companyID} ";
            string orderSql = $" order by start_time desc ";
            if (sellerOrder) orderSql = $" order by oper_name,start_time desc ";
            string timeCondi = "";
            if (startTime != null && endTime != null)
            {
                condi += $" and start_time>='{startTime}' and start_time<='{endTime}' ";
                timeCondi = $" and happen_time>='{startTime}' and happen_time<='{endTime}' ";
            }
            string departCondi = "";
            if (depart_id.IsValid())
            {
                departCondi = @$" and (o.depart_path like '%/{depart_id}/%' or o.depart_path is null)  ";
                condi += departCondi;
            }
            if (seller_id != null) condi += $" and v.seller_id= {seller_id} ";
            if (supcust_id != null) condi += $" and v.supcust_id= {supcust_id} ";
            /**
            if (regionsID.IsValid())
            {
                var regions = JsonConvert.DeserializeObject<int[]>(regionsID);
                if (regions.Length > 0)
                    condi += " and (" + string.Join(" or ", regions.Select(x => $"other_region  like '%/{x}/%'")) + ") ";
            }
            **/
            SQLQueue QQ = new SQLQueue(cmd);
            string sel_sql =
                @"visit_id,sheet_id,sheet_type,total_amount, (sheet_attribute->>'forReturn')::boolean as has_return,(sheet_attribute->>'free')::boolean as has_free, 
                                (CASE WHEN (cast(total_amount as DECIMAL(16,4)) - cast(now_pay_amount as DECIMAL(16,4))- CAST(now_disc_amount AS DECIMAL(16,4)) > 0 ) THEN TRUE ELSE FALSE END )  has_arrears,
                                (case when now_disc_amount>0 then true else false end) has_disc ";

            string sql_noLimit = $@"
select v.visit_comment,v.visit_thumb,v.visit_score,v.longitude,v.latitude, v.visit_id,v.remark,v.seller_id,oper_name,v.supcust_id,sup_name,start_time,end_time,door_picture,showcase_pictures,EXTRACT(epoch from age(end_time,start_time))/60 as intervals,
        v.sale_amount as visit_sale_amount,v.order_amount as visit_sale_order_amount,has_free,has_return,has_arrears,has_disc ,visit_sheets,d.managers_id,sc.sup_rank,sc.sup_group,v.work_content ,v.end_sign_distance,v.sign_distance

from sheet_visit v  

left join 
(

    select visit_id,bool_or(has_free) has_free,bool_or(has_return) has_return,bool_or(has_arrears) has_arrears,bool_or(has_disc) has_disc,
         json_agg(format('{{""sheet_id"":%s,""sheet_type"":""%s"",""total_amount"":""%s""}}',sheet_id,sheet_type,total_amount)::jsonb) as visit_sheets
    from
    (
          (select {sel_sql} from sheet_sale_main       m where m.company_id = {companyID} {timeCondi} and approve_time is not null and red_flag is null)
          UNION 
          (select {sel_sql} from sheet_sale_order_main m where m.company_id = {companyID} {timeCondi} and approve_time is not null and red_flag is null)
    
    ) t group by visit_id 

) vs on v.visit_id = vs.visit_id 

left join info_supcust sc on v.supcust_id =sc.supcust_id and v.company_id=sc.company_id
 
left join info_operator o on v.seller_id = o.oper_id  and v.company_id=o.company_id

left join info_department d on d.depart_id = o.depart_id  and d.company_id=o.company_id

 
 {condi}";
            string sql = sql_noLimit + $"{orderSql} limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);
            if (startRow == 0)
            {
                sql =
                    $"select count(*) as itemCount,sum(visit_sale_amount) sale_total_amount,sum(case when visit_sale_amount is not null then 1 else 0 end) sale_count,sum(case when visit_sale_order_amount is not null then 1 else 0 end) sale_order_count, sum(visit_sale_order_amount) sale_order_total_amount,sum(ceil(intervals)) total_intervals  from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
            }

            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            string sale_total_amount = "", sale_order_total_amount = "", total_intervals = "";
            string sale_count = "", sale_order_count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = await CDbDealer.GetRecordsFromDrAsync(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                    sale_total_amount = CPubVars.GetTextFromDr(dr, "sale_total_amount");
                    sale_order_total_amount = CPubVars.GetTextFromDr(dr, "sale_order_total_amount");
                    total_intervals = CPubVars.GetTextFromDr(dr, "total_intervals");
                    sale_count = CPubVars.GetTextFromDr(dr, "sale_count");
                    sale_order_count = CPubVars.GetTextFromDr(dr, "sale_order_count");
                }
            }

            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new
                {result, msg, data, itemCount, sale_total_amount, sale_order_total_amount, total_intervals, sale_count, sale_order_count});
        }


        [HttpGet]
        public async Task<JsonResult> GetVisitSummaryBySeller(string operKey, string startDate, string endDate, int pageSize, int startRow,
            string sellerId,string depart_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);


            string condi = "";
            string condiOper = "";
            if (sellerId.IsValid())
            {
                condiOper = $" and oper_id={sellerId}";
                condi += $" and seller_id={sellerId}";
            }
            if (depart_id.IsValid())
            {
                condiOper += $" and io.depart_path like '%{depart_id}%'";
            }


            string sql_nolimit = $@"
SELECT * FROM
info_operator  io
LEFT JOIN
(
    SELECT seller_id,COUNT (*) visit_count,COUNT (distinct supcust_id) client_count,COALESCE (sum(visit_score),0) visit_score
    FROM sheet_visit sv    
    WHERE sv.company_id ={companyID} and start_time >= '{startDate}'and start_time <= '{endDate}' {condi} 
    GROUP BY seller_id
) visit on io.oper_id=visit.seller_id
left JOIN 
(
	select creator_id,count( * ) creat_count
	FROM info_supcust sc
    WHERE sc.company_id ={companyID}  and create_time >= '{startDate}' and create_time <= '{endDate}' 
    GROUP BY creator_id 	
) sc on io.oper_id = sc.creator_id
LEFT JOIN
(
   select seller_id,sum(case when inout_flag*quantity<0 then inout_flag*sub_amount*(-1) else 0 end) sale_amount,
                    sum(case when inout_flag*quantity>0 then inout_flag*sub_amount  else 0 end) return_amount,
                    count(distinct supcust_id) sale_clients

   from
   (
       select seller_id,sub_amount,inout_flag,quantity,supcust_id from 
       (
          select inout_flag,sub_amount,sheet_id,quantity from sheet_sale_detail where company_id ={companyID} and happen_time>='{startDate}' and happen_time <= '{endDate}' 
       ) sd 
       LEFT JOIN
       (
          select sheet_id,seller_id,red_flag,approve_time,supcust_id from sheet_sale_main where company_id ={companyID} and happen_time>='{startDate}' and happen_time <= '{endDate}' 
       ) sm on sd.sheet_id=sm.sheet_id
       where red_flag is null and approve_time is not null 
    ) dm group by seller_id   
) sale on io.oper_id=sale.seller_id

where io.company_id = {companyID}  and io.is_seller {condiOper} and COALESCE(io.status,'1')='1' 

";


            string sql = @$"
{sql_nolimit} 
ORDER BY coalesce(visit_count,0) DESC,coalesce(sale_amount,0) DESC
limit {pageSize} offset {startRow}";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
            if (startRow == 0)
            {
                sql = @$"
SELECT count(*) as itemCount,sum(visit_count) visit_total_count,sum(client_count) client_total_count,sum(sale_amount) sale_total_amount, sum(return_amount) return_total_amount, sum(creat_count) creat_total_count,sum(visit_score) visit_score
FROM 
(
    {sql_nolimit}
) tt";
                QQ.Enqueue("count", sql);
            }

            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            string sale_total_amount = "";
            string visit_total_count = "";
            string client_total_count = "";
            string creat_total_count = "";
            string return_total_amount = "";
            string visit_score = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                    sale_total_amount = CPubVars.GetTextFromDr(dr, "sale_total_amount");
                    visit_total_count = CPubVars.GetTextFromDr(dr, "visit_total_count");
                    client_total_count = CPubVars.GetTextFromDr(dr, "client_total_count");
                     
                    creat_total_count = CPubVars.GetTextFromDr(dr, "creat_total_count");
                    return_total_amount = CPubVars.GetTextFromDr(dr, "return_total_amount");
                    visit_score = CPubVars.GetTextFromDr(dr, "visit_score");
                }
            }

            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, itemCount, sale_total_amount, visit_total_count, client_total_count, creat_total_count, return_total_amount, visit_score });


        }
        [HttpGet]
        public async Task<JsonResult> SearchVisitSheetByVisitId(string operKey, string visitID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);
            string saleSheetsSQL = @$"
SELECT
	round( SUM ( total_amount ) :: NUMERIC, 2 ) AS total,
	sheet_type
FROM
	sheet_sale_main
WHERE
    company_id={companyID}
AND visit_id = {visitID}
AND red_flag is null
GROUP BY sheet_type,visit_id
";
            QQ.Enqueue("saleSheets", saleSheetsSQL);
            string  saleOrderSheetsSQL = @$"
SELECT
	round( SUM ( total_amount ) :: NUMERIC, 2 ) AS total,
	sheet_type
FROM
	sheet_sale_order_main
WHERE
	company_id={companyID}
AND visit_id = {visitID}
AND red_flag is null
GROUP BY sheet_type,visit_id
";
            QQ.Enqueue("saleOrderSheets", saleOrderSheetsSQL);
            string prepaySheetsSQL=@$"
SELECT
	round( SUM ( total_amount ) :: NUMERIC, 2 ) AS total,
	sheet_type 
FROM
	sheet_prepay
WHERE
	company_id={companyID}
AND visit_id = {visitID}
AND red_flag is null
GROUP BY sheet_type,visit_id
";
            QQ.Enqueue("prepaySheets", prepaySheetsSQL);
            string arrearSheetsSQL = @$"
SELECT
	round( SUM ( now_pay_amount ) :: NUMERIC, 2 ) AS total,
	sheet_type
FROM
	sheet_get_arrears_main
WHERE
	visit_id = {visitID}
AND company_id={companyID}
AND red_flag is null
GROUP BY sheet_type,visit_id
";
            QQ.Enqueue("arrearSheets", arrearSheetsSQL);
            string  feeOutSheetsSQL = $@"
SELECT
	round( SUM ( total_amount ) :: NUMERIC, 2 ) AS total,
	sheet_type
FROM
	sheet_fee_out_main
WHERE
	visit_id = {visitID}
AND company_id={companyID}
AND red_flag is null
GROUP BY sheet_type,visit_id
";
            QQ.Enqueue("feeOutSheets", feeOutSheetsSQL);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            List<SheetTypeTotal> saleSheets = new List<SheetTypeTotal>();
            List<SheetTypeTotal> saleOrderSheets = new List<SheetTypeTotal>();
            List<SheetTypeTotal> prepaySheets = new List<SheetTypeTotal>();
            List<SheetTypeTotal> arrearSheets = new List<SheetTypeTotal>();
            List<SheetTypeTotal> feeOutSheets = new List<SheetTypeTotal>();
            
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "saleSheets")
                {
                    saleSheets = CDbDealer.GetRecordsFromDr<SheetTypeTotal>(dr, false);
                } else if (tbl == "saleOrderSheets")
                {
                    saleOrderSheets = CDbDealer.GetRecordsFromDr<SheetTypeTotal>(dr, false);
                }else if (tbl == "prepaySheets")
                {
                    prepaySheets = CDbDealer.GetRecordsFromDr<SheetTypeTotal>(dr, false);
                }else if(tbl =="arrearSheets")
                {
                    arrearSheets = CDbDealer.GetRecordsFromDr<SheetTypeTotal>(dr, false);
                }else if (tbl == "feeOutSheets")
                {
                    feeOutSheets = CDbDealer.GetRecordsFromDr<SheetTypeTotal>(dr, false);
                }
            }

          
            QQ.Clear();

            Dictionary<string, dynamic> data = new Dictionary<string, dynamic>();
            saleSheets.ForEach(item =>
            {
                string sheetType = item.sheet_type;
                decimal total = item.total;
                data.Add(sheetType, total);
            });
            saleOrderSheets.ForEach(item =>
            {
                string sheetType = item.sheet_type;
                decimal total = item.total;
                data.Add(sheetType, total);
            });
            prepaySheets.ForEach(item =>
            {
                if (item.sheet_type =="DH"|| item.sheet_type=="YS")
                {
                string sheetType = item.sheet_type;
                decimal total = item.total;
                data.Add(sheetType, total);
                }
            });
            arrearSheets.ForEach(item =>
            {
                if (item.sheet_type == "SK")
                {
                string sheetType = item.sheet_type;
                decimal total = item.total;
                data.Add(sheetType, total);
                }
            });
            feeOutSheets.ForEach(item =>
            {
                if (item.sheet_type == "ZC")
                {
                string sheetType = item.sheet_type;
                decimal total = item.total;
                data.Add(sheetType, total);
                }
            });
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, visitID });
        }

        private class SheetTypeTotal {
            public string  sheet_type { get; set; }
            public decimal  total { get; set; }
        }

        /// <summary>
        /// 查看拜访门店
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="visitID"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> LoadVisitInfo(string operKey, string visitID, string supcustID, string supGroup, string supRank,
            string seller_id, string seller_dept)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);
            var currentTime = DateTime.Now.ToText();
            var condi = $"(SELECT start_time FROM sheet_visit where company_id = {companyID} and supcust_id = {supcustID} and visit_id = {visitID})";
            if (visitID.IsInvalid())
            {
                visitID = "-1";
                condi = $"'{currentTime}'";
            }

            // 销售 销售订单 收款 预收款 费用支出 
            string sql = "";
            if (visitID != "-1")
            {
                var month = DateTime.Now.ToString("yyyy-MM");
                sql = $@"select 
       t.supcust_id,
       sup_name,
       sale_sheets,
       order_sheets,
       arrear_sheets,
       feeout_sheets,
       start_time,
       end_time,
       door_picture,
       showcase_pictures,
       seller_id,
       oper_name,
       remark
       from sheet_visit t
                            left join  (select visit_id,array_to_json(array_agg(('{{""sheet_id"":'||sheet_id||',""total_amount"":'||total_amount||'}}')::jsonb)) sale_sheets from sheet_sale_main where company_id = {companyID} and visit_id = {visitID} and red_flag is null and approve_time is not null group by visit_id) s on s.visit_id = t.visit_id
                            left join  (select visit_id,array_to_json(array_agg(('{{""sheet_id"":'||sheet_id||',""total_amount"":'||total_amount||'}}')::jsonb)) order_sheets from sheet_sale_order_main where company_id = {companyID} and visit_id = {visitID} and red_flag is null and approve_time is not null group by visit_id) os on os.visit_id = t.visit_id
                            left join  (select visit_id,array_to_json(array_agg(('{{""sheet_id"":'||sheet_id||',""total_amount"":'||total_amount||'}}')::jsonb)) preget_sheets from sheet_fee_out_main where company_id = {companyID} and visit_id = {visitID} and red_flag is null and approve_time is not null group by visit_id) f on f.visit_id = t.visit_id
                            left join  (select visit_id,array_to_json(array_agg(('{{""sheet_id"":'||sheet_id||',""total_amount"":'||now_pay_amount||'}}')::jsonb)) arrear_sheets from sheet_get_arrears_main where company_id = {companyID} and visit_id = {visitID} and red_flag is null and approve_time is not null group by visit_id) g on g.visit_id = t.visit_id
                            left join  (select visit_id,array_to_json(array_agg(('{{""sheet_id"":'||sheet_id||',""total_amount"":'||total_amount||'}}')::jsonb)) feeout_sheets from sheet_prepay where company_id = {companyID} and visit_id = {visitID} and red_flag is null and approve_time is not null group by visit_id) p on p.visit_id = t.visit_id
                            left join info_operator op on op.oper_id = t.seller_id  
                            left join info_supcust su on t.supcust_id = su.supcust_id 
                            where t.company_id = {companyID} and t.visit_id = {visitID} ";
                QQ.Enqueue("CurrentVisitInfo", sql);
                sql = $@"
                select 
                       odm.maintain_id,
                       odm.visit_id,
                       odm.client_id,
                       odm.oper_id,
                       odm.disp_temp_id,
                       odm.disp_sheet_id,
                       odm.work_content,
                       odm.happen_time,
                       odm.reviewer,
                       reviewer.oper_name as reviewerName,
                       dam.seller_id,    --制单人
                       io.oper_name,    --制单人
                       dam.responsible_worker, -- 负责人
                       io2.oper_name as responsible_worker_name,
                       odm.review_time,
                       odm.review_comment,
                       odm.review_refused,
                       dam.company_id,
                       dam.sheet_no,
                       idt.month_maintain_times,
                       idt.maintain_interval_days,
                       idt.maintain_need_review,
                       idt.disp_template_name,
       isup.sup_name as disp_temp_sup_name,
                       sdm.months,
                       sdm.maintain_times,
                       sdm.sum_maintain_id,
                        (SELECT max(odm2.happen_time) FROM op_display_maintain odm2 WHERE odm2.company_id = {companyID} AND odm2.client_id = dam.supcust_id AND odm2.visit_id = {visitID}) as last_maintain_time
                from op_display_maintain odm 
                    LEFT JOIN info_operator reviewer ON odm.reviewer= reviewer.oper_id 
                    LEFT JOIN display_agreement_main dam ON odm.company_id = dam.company_id AND odm.disp_sheet_id = dam.sheet_id 
                    LEFT JOIN info_display_template idt ON dam.company_id = idt.company_id AND odm.disp_temp_id = idt.disp_template_id
                    LEFT JOIN info_supcust isup ON idt.company_id = isup.company_id AND isup.supcust_id = idt.sup_id
                    LEFT JOIN info_operator io ON io.company_id = odm.company_id AND io.oper_id = dam.seller_id
                    LEFT JOIN info_operator io2 ON io2.company_id = dam.company_id AND io2.oper_id = dam.responsible_worker
                    LEFT JOIN sum_display_maintain sdm ON 
                        dam.company_id = sdm.company_id 
                            AND sdm.disp_sheet_id = dam.sheet_id 
                            AND sdm.disp_temp_id = dam.disp_template_id 
                            AND sdm.client_id = odm.client_id 
                            AND sdm.months = '{month}-01 00:00:00' 
                where odm.visit_id = {visitID} and odm.company_id = {companyID} and odm.for_month is null;";
                QQ.Enqueue("CurrentVisitDisplayMaintainInfo", sql);
                sql = $@"

SELECT 
       odk.keep_id, 
       odk.oper_id, 
       odk.client_id, 
       odk.disp_temp_id, 
       odk.disp_sheet_id, 
       odk.work_content, 
       odk.happen_time, 
       odk.visit_id, 
       odk.reviewer,
       reviewer.oper_name as reviewerName,
       odk.review_time, 
       odk.review_comment, 
       odk.review_refused,
       dam.company_id,
       dam.sheet_no,
       dam.start_time,
       dam.end_time,
       idt.keep_actions,
       idt.keep_need_review,
       idt.resign_interval_months,
       idt.disp_template_name,
       isup.sup_name as disp_temp_sup_name,
       sdk.sum_keep_id,
       sdk.keep_times,
       sdk.need_keep_times,
        dam.seller_id,    --制单人
       io.oper_name,    --制单人
       dam.responsible_worker, -- 负责人
       io2.oper_name as responsible_worker_name,
    EXTRACT(MONTH from age(dam.end_time, dam.start_time)) + 1 AS disp_sign_month
FROM op_display_keep odk
LEFT JOIN info_operator reviewer ON odk.reviewer= reviewer.oper_id 
LEFT JOIN display_agreement_main dam ON odk.company_id = dam.company_id AND odk.disp_sheet_id = dam.sheet_id 
LEFT JOIN info_display_template idt ON dam.company_id = idt.company_id AND odk.disp_temp_id = idt.disp_template_id 
LEFT JOIN info_supcust isup ON idt.company_id = isup.company_id AND isup.supcust_id = idt.sup_id
LEFT JOIN info_operator io ON io.company_id = odk.company_id AND io.oper_id = dam.seller_id
LEFT JOIN info_operator io2 ON io2.company_id = dam.company_id AND io2.oper_id = dam.responsible_worker
LEFT JOIN sum_display_keep sdk ON 
    dam.company_id = sdk.company_id 
        AND sdk.disp_sheet_id = dam.sheet_id 
        AND sdk.disp_temp_id = dam.disp_template_id 
        AND sdk.client_id = odk.client_id
WHERE odk.company_id = {companyID} AND odk.visit_id = {visitID};
";
                QQ.Enqueue("CurrentVisitDisplayKeepInfoSQL", sql);
                                sql = $@"
                select 
                       odm.maintain_id,
                       odm.visit_id,
                       odm.client_id,
                       odm.oper_id,
                       odm.disp_temp_id,
                       odm.disp_sheet_id,
                       odm.work_content,
                       odm.happen_time,
                       odm.reviewer,
                       odm.for_month,
                      dam.seller_id,    --制单人
                       io.oper_name,    --制单人
                       dam.responsible_worker, -- 负责人
                       io2.oper_name as responsible_worker_name,
                       reviewer.oper_name as reviewerName,
                       odm.review_time,
                       odm.review_comment,
                       odm.review_refused,
                       dam.company_id,
                       dam.sheet_no,
                       idt.month_maintain_times,
                       idt.maintain_interval_days,
                       idt.maintain_need_review,
                       idt.disp_template_name,
       isup.sup_name as disp_temp_sup_name,
                       sdm.months,
                       sdm.maintain_times,
                       sdm.sum_maintain_id,
                        (SELECT max(odm2.happen_time) FROM op_display_maintain odm2 WHERE odm2.company_id = {companyID} AND odm2.client_id = dam.supcust_id AND odm2.visit_id = {visitID}) as last_maintain_time
                from op_display_maintain odm 
                    LEFT JOIN info_operator reviewer ON odm.reviewer= reviewer.oper_id 
                    LEFT JOIN display_agreement_main dam ON odm.company_id = dam.company_id AND odm.disp_sheet_id = dam.sheet_id 
                    LEFT JOIN info_display_template idt ON dam.company_id = idt.company_id AND odm.disp_temp_id = idt.disp_template_id
                    LEFT JOIN info_supcust isup ON idt.company_id = isup.company_id AND isup.supcust_id = idt.sup_id
                    LEFT JOIN info_operator io ON io.company_id = odm.company_id AND io.oper_id = dam.seller_id
                    LEFT JOIN info_operator io2 ON io2.company_id = dam.company_id AND io2.oper_id = dam.responsible_worker
                    LEFT JOIN sum_display_maintain sdm ON 
                        dam.company_id = sdm.company_id 
                            AND sdm.disp_sheet_id = dam.sheet_id 
                            AND sdm.disp_temp_id = dam.disp_template_id 
                            AND sdm.client_id = odm.client_id 
                            AND sdm.months = '{month}-01 00:00:00' 
                where odm.visit_id = {visitID} and odm.company_id = {companyID} and odm.for_month is not null order by odm.maintain_id;";
                QQ.Enqueue("CurrentVisitDisplayForMonthMaintainInfoAction", sql);
            }

            string fromTime = CPubVars.GetDateText(DateTime.Now.AddDays(-360));
                
            
            sql = $@"
select  date_part('day', DATE_TRUNC('day', NOW()) - (case when last_sale_time ='' then null else DATE_TRUNC('day', to_date(last_sale_time,'YYYY-MM-DD 00:00')) end)::timestamp ) AS sale_passed_days,date_part('day',DATE_TRUNC('day', NOW())-(case when last_visit_time ='' then null else DATE_TRUNC('day', to_date(last_visit_time,'YYYY-MM-DD 00:00')) end)::timestamp) as passed_days,rt.sale_amount sale_amount
from
(
   select realtime->>'lastSaleTime' last_sale_time,realtime->>'lastVisitTime' last_visit_time, realtime->>'lastSaleAmt' as sale_amount from realtime_supcust where company_id={companyID} and supcust_id={supcustID}
) rt                                
                       ";
            //   sql= $@"
            //select date_part('day',now()::timestamp-start_time::timestamp) as passed_daysselect ,date_part('day', now( ) :: TIMESTAMP - s.happen_time::TIMESTAMP) AS sale_passed_days"
            QQ.Enqueue("LastVisitInfo", sql);

            string PrepayBalanceSQL = $@"select is_order,round(sum(balance)::numeric,2) as balance from prepay_balance b left join cw_subject s on b.company_id=s.company_id and b.sub_id=s.sub_id where b.company_id = {companyID} and b.supcust_id = {supcustID} group by s.is_order";
            QQ.Enqueue("PrepayBalance", PrepayBalanceSQL);

            string ArrearBalanceSQL =
                $@"select round(sum(balance)::numeric,2) as balance  from arrears_balance where  company_id = {companyID} and supcust_id = {supcustID}";
            QQ.Enqueue("ArrearBalance", ArrearBalanceSQL);
             
            string SupcustRemarkSQL = $@"select supcust_remark,sup_door_photo from info_supcust where company_id = {companyID} and supcust_id = {supcustID}";
            QQ.Enqueue("SupcustRemark", SupcustRemarkSQL);

            string InfoVisitTemplateMappingListSQL = $@"
SELECT 
       ivtm.mapping_id,
       ivtm.sup_group, 
       ivtm.sup_rank, 
       ivtm.dept_id,
       ivtm.sellers_id, 
       ivtm.visit_tmp_id, ivt.actions as visit_temp_actions,
       ivtm.display_tmp_id, 
       ivtm.equipment_tmp_id, 
       ivtm.order_index
FROM info_visit_template_mapping ivtm
LEFT JOIN info_visit_template ivt on ivt.company_id = {companyID} AND ivt.template_id = ivtm.visit_tmp_id
WHERE ivtm.company_id = {companyID}
ORDER by ivtm.order_index
";
            QQ.Enqueue("InfoVisitTemplateMappingList", InfoVisitTemplateMappingListSQL);
            var months = DateTime.Now.ToString("yyyy-MM");
            string displayMaintainInfoSQL = $@"
SELECT
       dam.company_id,
       dam.sheet_id,
       dam.sheet_no,
       dam.supcust_id,
       dam.start_time,
       dam.end_time,
       dam.disp_template_id,
       dam.seller_id,    --制单人
       dam.responsible_worker, -- 负责人dam.seller_id,    --制单人
       dam.responsible_worker, -- 负责人
       io2.oper_name as responsible_worker_name,
       io2.depart_path as responsible_depart_path,
       io.oper_name,    --制单人
       io.depart_path,  --制单部门
       idt.disp_template_name,
       isup.sup_name as disp_temp_sup_name,
       idt.maintain_actions,
       idt.month_maintain_times,
       idt.maintain_interval_days,
       idt.maintain_need_review,
       idt.dept_path as temp_dept_path,
       sdm.months,
       sdm.maintain_times,
       sdm.sum_maintain_id,
       (SELECT odm.reviewer FROM op_display_maintain odm WHERE  odm.for_month is null and odm.company_id = dam.company_id AND odm.client_id = dam.supcust_id AND odm.disp_sheet_id = dam.sheet_id AND odm.happen_time = (SELECT max(odm2.happen_time) FROM op_display_maintain odm2 WHERE odm2.company_id = dam.company_id AND odm2.client_id = dam.supcust_id AND odm2.disp_sheet_id = dam.sheet_id)) as last_reviewer,
       (SELECT odm.happen_time FROM op_display_maintain odm WHERE  odm.for_month is null and odm.company_id = dam.company_id AND odm.client_id = dam.supcust_id AND odm.disp_sheet_id = dam.sheet_id AND odm.happen_time = (SELECT max(odm2.happen_time) FROM op_display_maintain odm2 WHERE odm2.company_id = dam.company_id AND odm2.client_id = dam.supcust_id AND odm2.disp_sheet_id = dam.sheet_id)) as last_reviewer_happen_time,
       (SELECT max(odm.happen_time) FROM op_display_maintain odm WHERE odm.company_id = dam.company_id AND odm.client_id = dam.supcust_id AND odm.disp_sheet_id = dam.sheet_id and odm.for_month is null) as last_maintain_time
FROM display_agreement_main dam
LEFT JOIN info_display_template idt ON dam.company_id = idt.company_id AND dam.disp_template_id = idt.disp_template_id 
LEFT JOIN info_operator io ON io.company_id = idt.company_id AND io.oper_id = dam.seller_id
LEFT JOIN info_operator io2 ON io2.company_id = idt.company_id AND io2.oper_id = dam.responsible_worker
LEFT JOIN info_supcust isup ON idt.company_id = isup.company_id AND isup.supcust_id = idt.sup_id
LEFT JOIN sum_display_maintain sdm ON dam.company_id = sdm.company_id AND sdm.disp_sheet_id = dam.sheet_id  AND sdm.client_id = dam.supcust_id AND sdm.months = '{months}-01 00:00:00'
WHERE dam.company_id = {companyID} 
    AND dam.supcust_id = {supcustID} 
    AND dam.approve_time IS NOT NULL 
    AND dam.red_flag IS NULL 
    AND dam.start_time <= now() 
    AND dam.disp_template_id is NOT NULL 
    and ((idt.sign_need_review = true and dam.reviewer is NOT NULL AND (dam.review_refused IS NULL OR dam.review_refused = false)) OR idt.sign_need_review = false)
  AND idt.month_maintain_times IS NOT NULL 
  AND idt.maintain_actions <> '[]' 
  AND idt.maintain_actions IS NOT NULL 
  AND ((idt.month_maintain_times IS NULL) OR (coalesce(sdm.maintain_times,0) < coalesce(idt.month_maintain_times,0)))
  AND (case when io2.depart_path is NOT NULL then io2.depart_path like '{seller_dept}%' when io2.depart_path is null and io.depart_path is not null then io.depart_path like '{seller_dept}%' END);
";
            QQ.Enqueue("displayMaintainInfoSQL", displayMaintainInfoSQL);
            string displayKeepInfoSQL = $@"
SELECT dam.company_id,
       dam.sheet_id,
       dam.sheet_no,
       dam.supcust_id,
       dam.start_time,
       dam.end_time,
       dam.disp_template_id,
       dam.seller_id,    --制单人
       io.oper_name,    --制单人
       io.depart_path,  --制单部门
       dam.responsible_worker, -- 负责人
       io2.oper_name as responsible_worker_name,
       io2.depart_path as responsible_depart_path,
       idt.keep_actions,
       idt.keep_need_review,
       idt.resign_interval_months,
       idt.dept_path as temp_dept_path,
       idt.disp_template_name,
       isup.sup_name as disp_temp_sup_name,
       sdk.keep_times,
       sdk.need_keep_times,
       EXTRACT(MONTH from age(dam.end_time, dam.start_time)) + 1 AS disp_sign_month,
    (SELECT max(odk.happen_time)
        FROM op_display_keep odk
        WHERE odk.company_id = dam.company_id
          AND odk.client_id = dam.supcust_id
          AND odk.disp_sheet_id = dam.sheet_id) as last_keep_time,
       (SELECT reviewer FROM op_display_keep WHERE company_id = dam.company_id AND client_id = dam.supcust_id AND disp_sheet_id = dam.sheet_id AND happen_time = (SELECT MAX(happen_time) FROM op_display_keep WHERE company_id = dam.company_id AND client_id = dam.supcust_id AND disp_sheet_id = dam.sheet_id)) AS last_keep_reviewer,
       (SELECT odkk.happen_time FROM op_display_keep odkk WHERE odkk.company_id = dam.company_id AND odkk.client_id = dam.supcust_id AND odkk.disp_sheet_id = dam.sheet_id AND odkk.happen_time = (SELECT MAX(happen_time) FROM op_display_keep WHERE company_id = dam.company_id AND client_id = dam.supcust_id AND disp_sheet_id = dam.sheet_id)) AS last_keep_reviewer_happen_time 
FROM display_agreement_main dam
         LEFT JOIN info_display_template idt
                   ON dam.company_id = idt.company_id AND dam.disp_template_id = idt.disp_template_id
         LEFT JOIN sum_display_keep sdk ON dam.company_id = sdk.company_id AND sdk.disp_sheet_id = dam.sheet_id AND
                                           sdk.client_id = dam.supcust_id 
    LEFT JOIN info_operator io ON io.company_id = idt.company_id AND io.oper_id = dam.seller_id
LEFT JOIN info_operator io2 ON io2.company_id = idt.company_id AND io2.oper_id = dam.responsible_worker 
LEFT JOIN info_supcust isup ON idt.company_id = isup.company_id AND isup.supcust_id = idt.sup_id
WHERE dam.company_id = {companyID}
  AND dam.supcust_id = {supcustID} 
  AND dam.approve_time IS NOT NULL
  AND dam.red_flag IS NULL
  AND dam.start_time <= now()
  AND dam.disp_template_id is NOT NULL 
  AND idt.resign_interval_months IS NOT NULL
  AND idt.keep_actions <> '[]'
  AND idt.keep_actions IS NOT NULL 
  AND ((idt.sign_need_review = true and dam.reviewer is NOT NULL AND (dam.review_refused IS NULL OR dam.review_refused = false)) OR idt.sign_need_review = false)
  AND ((sdk.need_keep_times IS NULL) OR (coalesce(sdk.keep_times, 0) < coalesce(sdk.need_keep_times, 0))) 
  AND (case when io2.depart_path is NOT NULL then io2.depart_path like '{seller_dept}%' when io2.depart_path is null and io.depart_path is not null then io.depart_path like '{seller_dept}%' END);
";
            QQ.Enqueue("displayKeepInfoSQL", displayKeepInfoSQL);

            string displayForMonthMaintainInfoSQL = $@"
select
t.company_id,
t.sheet_id,
t.sheet_no,
t.supcust_id,
t.start_time,
t.end_time,
t.disp_template_id,
t.seller_id,          --制单人
t.responsible_worker, -- 负责人
t.responsible_worker_name,
t.oper_name,          --制单人
t.disp_template_name,
t.disp_temp_sup_name,
t.maintain_actions,
t.month_maintain_times,
t.maintain_need_review,
json_agg(json_build_object('month',  t.every_month, 'selectTimes', 0, 'times', COALESCE(sdm.maintain_times, 0))) as maintain_array
from (
         SELECT dam.company_id,
                dam.sheet_id,
                dam.sheet_no,
                dam.supcust_id,
                isup.sup_name as disp_temp_sup_name,
                dam.start_time,
                dam.end_time,
                dam.disp_template_id,
                idt.disp_template_name,
                dam.seller_id,          --制单人
                io.oper_name,           --制单人
                io.depart_path,         --制单部门
                dam.responsible_worker, -- 负责人
                io2.oper_name  as responsible_worker_name,
                io2.depart_path as responsible_depart_path,
                idt.maintain_actions,
                idt.month_maintain_times,
                idt.maintain_interval_days,
                idt.maintain_need_review,
                generate_series(dam.start_time::timestamp, dam.end_time::timestamp, '1 month')::date AS every_month
         FROM display_agreement_main dam
                  LEFT JOIN info_display_template idt
                            ON dam.company_id = idt.company_id AND dam.disp_template_id = idt.disp_template_id
                  LEFT JOIN info_operator io ON io.company_id = idt.company_id AND io.oper_id = dam.seller_id
                  LEFT JOIN info_operator io2
                            ON io2.company_id = idt.company_id AND io2.oper_id = dam.responsible_worker
                  LEFT JOIN info_supcust isup ON idt.company_id = isup.company_id AND isup.supcust_id = idt.sup_id 
         WHERE dam.company_id = {companyID} 
           AND dam.supcust_id = {supcustID} 
           AND dam.approve_time IS NOT NULL
           AND dam.red_flag IS NULL
           AND dam.start_time <= now()
           AND dam.disp_template_id is NOT NULL
           AND ((idt.sign_need_review = true and dam.reviewer is NOT NULL AND
                 (dam.review_refused IS NULL OR dam.review_refused = false)) OR idt.sign_need_review = false)
           AND idt.month_maintain_times IS NOT NULL
           AND idt.maintain_actions <> '[]'
           AND idt.maintain_actions IS NOT NULL
           AND (case
                    when io2.depart_path is NOT NULL then io2.depart_path like '{seller_dept}%'
                    when io2.depart_path is null and io.depart_path is not null
                        then io.depart_path like '{seller_dept}%' END)
     ) t
left join sum_display_maintain sdm on sdm.company_id = t.company_id
    and sdm.disp_sheet_id = t.sheet_id
    and sdm.disp_temp_id = t.disp_template_id
    and date_trunc('month', sdm.months::timestamp) = date_trunc('month', t.every_month::timestamp)
    and sdm.maintain_times < t.month_maintain_times
where date_trunc('month', t.every_month::timestamp) < date_trunc('month', now()::timestamp)
group by
t.company_id,
t.sheet_id,
t.sheet_no,
t.supcust_id,
t.start_time,
t.end_time,
t.disp_template_id,
t.seller_id,
t.responsible_worker,
t.responsible_worker_name,
t.responsible_depart_path,
t.oper_name,
t.depart_path,
t.disp_template_name,
t.disp_temp_sup_name,
t.maintain_actions,
t.month_maintain_times,
t.maintain_interval_days,
t.maintain_need_review;
";
            QQ.Enqueue("displayForMonthMaintainInfoSQL", displayForMonthMaintainInfoSQL);

            string leftDisplayAgreementAmountSQL = $@"
select count(*) as left_display_agreement_amount 
from display_agreement_main 
where company_id={companyID} and supcust_id={supcustID} and red_flag is null and settle_time is null ";
            
            QQ.Enqueue("leftDisplayAgreementAmountSQL",leftDisplayAgreementAmountSQL);

            string outstandingItemSQL = $@"select count(*) as outstanding_item from borrowed_cust_items where company_id={companyID} and cust_id={supcustID} and borrowed_qty != 0; ";
            QQ.Enqueue("outstandingItemSQL", outstandingItemSQL);

            dynamic CurrentVisitInfo = null;
            List<ExpandoObject> CurrentVisitDisplayMaintainInfo = new List<ExpandoObject>();
            List<ExpandoObject> CurrentVisitDisplayForMonthMaintainInfoAction = new List<ExpandoObject>();
            List<dynamic> CurrentVisitDisplayKeepInfo = new List<dynamic>();
            List<dynamic> InfoVisitTemplateMappingList = new List<dynamic>();
            ExpandoObject LastVisitInfo = null;
            List<ExpandoObject> displayMaintainInfoList = new List<ExpandoObject>();
            List<ExpandoObject> displayForMonthMaintainInfo = new List<ExpandoObject>();
            List<dynamic> displayKeepInfoList = new List<dynamic>();
            String PrepayBalance = "";
            String ArrearBalance = "";
            string ItemOrderedBalance = "";
            string LeftDisplayAgreementAmount = "";
            string OutstandingItem = "";
            String SupcustRemark = "";
            string sup_door_photo = "";
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "CurrentVisitInfo")
                {
                    CurrentVisitInfo = CDbDealer.Get1RecordFromDr(dr, false);
                    if (CurrentVisitInfo != null)
                    {
                        if (CurrentVisitInfo.sale_sheets != "")
                            CurrentVisitInfo.saleSheets = Newtonsoft.Json.JsonConvert.DeserializeObject(CurrentVisitInfo.sale_sheets);
                        CurrentVisitInfo.sale_sheets = "";
                        if (CurrentVisitInfo.order_sheets != "")
                            CurrentVisitInfo.orderSheets = Newtonsoft.Json.JsonConvert.DeserializeObject(CurrentVisitInfo.order_sheets);
                        CurrentVisitInfo.order_sheets = "";
                        if (CurrentVisitInfo.arrear_sheets != "")
                            CurrentVisitInfo.arrearSheets = Newtonsoft.Json.JsonConvert.DeserializeObject(CurrentVisitInfo.arrear_sheets);
                        CurrentVisitInfo.arrear_sheets = "";
                        if (CurrentVisitInfo.feeout_sheets != "")
                            CurrentVisitInfo.feeOutSheets = Newtonsoft.Json.JsonConvert.DeserializeObject(CurrentVisitInfo.feeout_sheets);
                        CurrentVisitInfo.feeout_sheets = "";
                    }
                }
                else if (sqlName == "LastVisitInfo")
                {
                    LastVisitInfo = CDbDealer.Get1RecordFromDr(dr, false);
                }
                else if (sqlName == "CurrentVisitDisplayMaintainInfo")
                {
                    CurrentVisitDisplayMaintainInfo = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "CurrentVisitDisplayForMonthMaintainInfoAction")
                {
                    CurrentVisitDisplayForMonthMaintainInfoAction = CDbDealer.GetRecordsFromDr(dr, false);
                }
                
                
                else if (sqlName == "CurrentVisitDisplayKeepInfoSQL")
                {
                    List<ExpandoObject> recordsFromDr = CDbDealer.GetRecordsFromDr(dr, false);
                    foreach (dynamic expandoObject in recordsFromDr)
                    {
                        int dispSignMonth = int.Parse(expandoObject.disp_sign_month);
                        int resignIntervalMonths = int.Parse(expandoObject.resign_interval_months);
                        if (expandoObject.need_keep_times.Equals(""))
                        {
                            // 续签总次数，（共计多少个月 - 1） / 续签间隔，结果取整
                            expandoObject.need_keep_times = ((int)((dispSignMonth - 1) / resignIntervalMonths)).ToString();
                        }
                        CurrentVisitDisplayKeepInfo.Add(expandoObject);
                    }

                }
                else if (sqlName == "PrepayBalance")
                {
                    List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                    if (records != null)
                    {
                        decimal d_PrepayBalance = 0m;
						decimal d_ItemOrderedBalance = 0m;

						foreach (dynamic rec in records)
                        {
                            if (((string) rec.is_order).ToLower() == "true")
                            {
								d_ItemOrderedBalance += decimal.Parse(rec.balance); 
                            }
                            else
								d_PrepayBalance += decimal.Parse(rec.balance);
                        }
                        if (d_ItemOrderedBalance != 0)
                            ItemOrderedBalance = CPubVars.FormatMoney(d_ItemOrderedBalance, 2);
						if (d_PrepayBalance != 0)
							PrepayBalance = CPubVars.FormatMoney(d_PrepayBalance, 2);


					}
				}
                else if (sqlName == "ItemOrderedBalance")
                {
                    dynamic data = CDbDealer.Get1RecordFromDr(dr, false);
                    if (data != null)
                    {
                        ItemOrderedBalance = data.balance;
                    }
                }
                else if (sqlName == "ArrearBalance")
                {
                    dynamic data = CDbDealer.Get1RecordFromDr(dr, false);
                    if (data != null)
                    {
                        ArrearBalance = data.balance;
                    }
                }
                else if (sqlName == "SupcustRemark")
                {
                    dynamic data = CDbDealer.Get1RecordFromDr(dr, false);
                    if (data != null)
                    {
                        SupcustRemark = data.supcust_remark;
                        sup_door_photo = data.sup_door_photo;
                    }
                }
                else if (sqlName == "InfoVisitTemplateMappingList")
                {
                    List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                    foreach (dynamic rec in records)
                    {
                        bool supRankBool = TemplateRulesCompare.CompareAnd(rec.sup_rank, supRank);
                        bool supGroupBool = TemplateRulesCompare.CompareAnd(rec.sup_group, supGroup);
                        string recSellersId = rec.sellers_id;
                        string recDeptId = rec.dept_id;
                        bool supSellersAndDeptBool = TemplateRulesCompare.CompareOr(
                            recSellersId,
                            TemplateRulesCompare.CompareLike(recSellersId, seller_id, ",", true),
                            TemplateRulesCompare.CompareLike(recDeptId, seller_dept, "/", false));
                        if (supRankBool && supGroupBool && supSellersAndDeptBool)
                        {
                            InfoVisitTemplateMappingList.Add(rec);
                        }
                    }
                }
                else if (sqlName == "displayMaintainInfoSQL")
                {
                    bool FilterCondition(dynamic displayMaintainInfo)
                    {
                        bool needReview = displayMaintainInfo.maintain_need_review.Equals("True");
                        bool hasReviewer = !string.IsNullOrEmpty(displayMaintainInfo.last_reviewer);
                        bool last_reviewer_happen_time = string.IsNullOrEmpty(displayMaintainInfo.last_reviewer_happen_time);

                        return (needReview && (hasReviewer || last_reviewer_happen_time))  || !needReview;
                    }
                    displayMaintainInfoList = CDbDealer.GetRecordsFromDr(dr, false);
                    displayMaintainInfoList = displayMaintainInfoList
                        .Where(displayMaintainInfo => FilterCondition(displayMaintainInfo))
                        .ToList();
                }
                else if (sqlName == "displayForMonthMaintainInfoSQL")
                {
                    displayForMonthMaintainInfo = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "displayKeepInfoSQL")
                {
                    List<ExpandoObject> recordsFromDr = CDbDealer.GetRecordsFromDr(dr, false);
                    foreach (dynamic expandoObject in recordsFromDr)
                    {
                        if (!string.IsNullOrEmpty(expandoObject.disp_template_id))
                        {
                            int dispSignMonth = int.Parse(expandoObject.disp_sign_month);
                            int resignIntervalMonths = int.Parse(expandoObject.resign_interval_months);
                            Boolean addFlag = true;
                            if (expandoObject.need_keep_times.Equals(""))
                            {
                                // 续签总次数，（共计多少个月 - 1） / 续签间隔，结果取整
                                expandoObject.need_keep_times = ((int)((dispSignMonth - 1) / resignIntervalMonths)).ToString();
                                if (expandoObject.need_keep_times.Equals("0"))
                                {
                                    addFlag = false;
                                }
                            }

                            if (addFlag)
                            {
                                if (expandoObject.last_keep_time.Equals(""))
                                {
                                    // 以开单时间为间隔进行计算 
                                    expandoObject.last_need_keep_time = DateTime.Parse(expandoObject.start_time).AddMonths(resignIntervalMonths).ToString("yyyy-MM") + "-01 00:00:00";
                                }
                                else
                                {
                                    DateTime dt = DateTime.Parse(expandoObject.last_keep_time); // 最后一次续签时间
                                    expandoObject.last_need_keep_time = dt.AddMonths(resignIntervalMonths).ToString("yyyy-MM") + "-01";
                                }
                                DateTime tempStartTime = DateTime.Parse(expandoObject.last_need_keep_time);
                                DateTime tempStopTime = DateTime.Parse(expandoObject.end_time);
                                if (DateTime.Compare(tempStartTime, tempStopTime) > 0)
                                {
                                    addFlag = false;
                                }
                                if (addFlag)
                                {
                                    displayKeepInfoList.Add(expandoObject);
                                }
                            }
                        }
                    }
                    
                    bool FilterCondition(dynamic displayKeepInfo)
                    {
                        bool needReview = displayKeepInfo.keep_need_review.Equals("True");
                        bool hasReviewer = !string.IsNullOrEmpty(displayKeepInfo.last_keep_reviewer);
                        bool last_keep_reviewer_happen_time = string.IsNullOrEmpty(displayKeepInfo.last_keep_reviewer_happen_time);
                        return needReview && (hasReviewer || last_keep_reviewer_happen_time) || !needReview;
                    }
                    displayKeepInfoList = displayKeepInfoList
                        .Where(displayKeepInfo => FilterCondition(displayKeepInfo))
                        .ToList();
                } 
                else if (sqlName == "leftDisplayAgreementAmountSQL")
                {
                    dynamic data = CDbDealer.Get1RecordFromDr(dr, false);
                    if (data != null)
                    {
                        LeftDisplayAgreementAmount = data.left_display_agreement_amount;
                    }
                }
                else if (sqlName == "outstandingItemSQL")
                {
                    dynamic data = CDbDealer.Get1RecordFromDr(dr, false);
                    if (data != null)
                    {
                        OutstandingItem = data.outstanding_item;
                    }
                }
            }

            dynamic infoVisitTemplateMappingObj = "";
            if (InfoVisitTemplateMappingList.Count > 0)
            {
                infoVisitTemplateMappingObj = TemplateRulesCompare.CompareResultOne(InfoVisitTemplateMappingList);
            }

            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, CurrentVisitInfo, LastVisitInfo, PrepayBalance, ItemOrderedBalance, ArrearBalance, SupcustRemark, sup_door_photo,LeftDisplayAgreementAmount,OutstandingItem,
                infoVisitTemplateMappingObj, displayMaintainInfoList, CurrentVisitDisplayMaintainInfo, displayKeepInfoList, CurrentVisitDisplayKeepInfo, displayForMonthMaintainInfo,CurrentVisitDisplayForMonthMaintainInfoAction
            });
        }

        [HttpGet]
        public async Task<JsonResult> GetDisplayAgreementInfo(string operKey, string supcustID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);
            
            string sql = @$"
SELECT tt.*,
       x.item_id as item_id,
       case when x.item_id is null then '钱' else ip.item_name end as item_name,
       unit_factor,mu.barcode,sdm.maintain_times,idt.disp_template_id,idt.disp_template_name,
       idt.month_maintain_times, fee_sub_name,idt.fd_sender_actions,idt.give_condition,idt.fd_seller_actions, 
       idt.fd_sender_actions,idt.cx_give_actions,idt.fd_seller_need_review, idt.fd_sender_need_review, idt.cx_give_need_review
FROM 
( 
    SELECT flow_id disp_flow_id,sheet_id disp_sheet_id,make_time,fee_sub_name,left_qty,unit_no,
        (case when start_month+months-1>12 then start_month+months-13 else start_month+months-1 end) as month,
        (case when start_month+months-1>12 then start_year+1 else start_year end) as year,months as month_id,
        disp_template_id,disp_sheet_no,reviewer, review_refused,
        -- case when items_id != 'money' then items_id::int else null end as item_id
        items_id
    FROM 
    (  
        SELECT d.sheet_id,d.flow_id,sub.sub_name fee_sub_name,items_id,unit_no,m.make_time,unnest(string_to_array((	
        COALESCE(month1_qty,0)-COALESCE(month1_given,0)||','||COALESCE(month2_qty,0)-COALESCE(month2_given,0)||','||
        COALESCE(month3_qty,0)-COALESCE(month3_given,0)||','||COALESCE(month4_qty,0)-COALESCE(month4_given,0)||','||
        COALESCE(month5_qty,0)-COALESCE(month5_given,0)||','||COALESCE(month6_qty,0)-COALESCE(month6_given,0)||','||
        COALESCE(month7_qty,0)-COALESCE(month7_given,0)||','||COALESCE(month8_qty,0)-COALESCE(month8_given,0)||','||
        COALESCE(month9_qty,0)-COALESCE(month9_given,0)||','||COALESCE(month10_qty,0)-COALESCE(month10_given,0)||','||
        COALESCE(month11_qty,0)-COALESCE(month11_given,0)||','||COALESCE(month12_qty,0)-COALESCE(month12_given,0)) ,','))::numeric left_qty,
        unnest(string_to_array('01,02,03,04,05,06,07,08,09,10,11,12',','))::int months,to_char(m.start_time,'YYYY')::int start_year,
        to_char(m.start_time,'MM')::int start_month,
        m.disp_template_id,m.sheet_no as disp_sheet_no,m.reviewer, m.review_refused
        from display_agreement_detail d 
        left join display_agreement_main m on m.sheet_id = d.sheet_id
        left join cw_subject sub on sub.company_id={companyID} and m.fee_sub_id=sub.sub_id
        where d.company_id = {companyID} and red_flag is null and approve_time is not null and supcust_id = {supcustID}
    ) t
    WHERE left_qty > 0 
    GROUP BY items_id,flow_id,sheet_id,make_time,fee_sub_name,months,left_qty,unit_no,start_year,start_month,disp_template_id, disp_sheet_no,reviewer, review_refused 
) tt 
left join lateral (select regexp_split_to_table(tt.items_id, ',')::int as item_id where tt.items_id != 'money') as x on true
left join info_item_prop ip on ip.item_id = x.item_id and ip.company_id = {companyID} and x.item_id is not null
left join info_item_multi_unit mu on mu.company_id = {companyID} and mu.item_id = x.item_id and mu.unit_no = tt.unit_no and x.item_id is not null

LEFT JOIN info_display_template idt ON idt.company_id = {companyID} AND idt.disp_template_id = tt.disp_template_id
LEFT JOIN sum_display_maintain sdm ON sdm.company_id = {companyID} AND sdm.disp_sheet_id = tt.disp_sheet_id AND sdm.client_id = {supcustID} AND sdm.months = (tt.year || '-' || lpad(tt.month :: text, 2, '0') || '-01 00:00:00') :: Date
WHERE (tt.disp_template_id is NULL OR (tt.disp_template_id is NOT NULL and ((sign_need_review = false) or (sign_need_review = true  and reviewer is NOT NULL AND (review_refused is null OR review_refused = false))) ))
ORDER BY disp_flow_id,month_id,item_name";

            QQ.Enqueue("DisplayAgreementList", sql);
            
            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> DisplayAgreementList = CDbDealer.GetRecordsFromDr(dr, false);
            
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, DisplayAgreementList });
        }

        /**
     * visit_id
     * oper_id
     * oper_name
     * **/
        [HttpPost]
        public async Task<JsonResult> CancelThumb([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string visit_id = data.visit_id;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string sql = @$"
UPDATE sheet_visit
SET visit_thumb = t.js_new
FROM
    (
    SELECT jsonb_agg((visit_thumb->> (idx - 1)::int)::jsonb) AS js_new
        FROM sheet_visit
    CROSS JOIN jsonb_array_elements(visit_thumb)
        WITH ORDINALITY arr(j, idx)
    WHERE company_id={companyID} and visit_id={visit_id} and  j->> 'oper_id' NOT IN('{operID}') 
    ) t
where company_id='{companyID}' and visit_id='{data.visit_id}'
";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            string result = "OK";
            return Json(new {result, msg = ""});
        }

        [HttpPost]
        public async Task<JsonResult> SaveThumb([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            var thumbModel = new
            {
                oper_id = operID,
                oper_name = data.oper_name
            };
            var sql =
                $@"update sheet_visit set visit_thumb = COALESCE(visit_thumb,'[]')||jsonb_build_array('{JsonConvert.SerializeObject(thumbModel)}'::json) where visit_id = '{data.visit_id}' and company_id='{companyID}'";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            string result = "OK";
            return Json(new {result, msg = ""});
        }

        [HttpPost]
        public async Task<JsonResult> SaveScore([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            var thumbModel = new
            {
                oper_id = operID,
                oper_name = data.oper_name
            };
            var sql = $@"update sheet_visit set visit_score = {data.score} where visit_id = '{data.visit_id}' and company_id='{companyID}'";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            string result = "OK";
            return Json(new {result, msg = ""});
        }

        /**
         * 
         * visit_id
         * pid 大节点
         * sub_pid 子大节点
         * id
         * content
         * comment_username
         * 可以根据pid得知回复的是哪条消息
         * **/
        [HttpPost]
        public async Task<JsonResult> SaveComment([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql =
                $@"update sheet_visit set visit_comment = COALESCE(visit_comment,'[]')||jsonb_build_array('{data.visit_comment}'::json) where visit_id = '{data.visit_id}' and company_id='{companyID}'";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            string result = "OK";
            return Json(new {result, msg = ""});
        }

        [HttpPost]
        public async Task<JsonResult> SaveSupcustRemark([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string supcust_remark = data.supcust_remark;
            string supcust_id = data.supcust_id;
            var sql = $"update info_supcust set supcust_remark ='{supcust_remark}' where company_id = {companyID} and supcust_id = {supcust_id}";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            string result = "OK";
            return Json(new {result, msg = ""});
        }
        /// <summary>
        /// 显示行程列表
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="seller_id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> LoadVisitSchedule(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            /**            string sql = @$"select day_id,day_name,c.supcust_id,sup_name from info_visit_day d left join info_visit_day_client c on d.day_id = c.day_id
                                        left join info_supcust s on c.supcust_id = s.supcust_id
                                        where company_id = {companyID} and selller_id = {seller_id} and (status = 1 or status is null) order by order_index";
            **/
            string scheduleIdSQL = @$"SELECT  multi_schedule_id  FROM  info_operator where oper_id = {operID}";
            dynamic scheduleData = await CDbDealer.Get1RecordFromSQLAsync(scheduleIdSQL, cmd);
            string[] scheduleIds = scheduleData.multi_schedule_id?.Split(",");
            List<string> scheduleIdList = scheduleIds.ToList().FindAll(p => p.IsValid());
            dynamic data = null;
            if (scheduleIdList.Count > 0)
            {
                string sql = @$"SELECT schedule_id,schedule_name from info_visit_schedule where schedule_id in (
                               {string.Join(",", scheduleIdList)}
                            )";
                data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            }


            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }



        /// <summary>
        /// 显示日程列表
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="seller_id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> LoadVisitDayList(string operKey, string schedule_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            /**            string sql = @$"select day_id,day_name,c.supcust_id,sup_name from info_visit_day d left join info_visit_day_client c on d.day_id = c.day_id
                                        left join info_supcust s on c.supcust_id = s.supcust_id
                                        where company_id = {companyID} and selller_id = {seller_id} and (status = 1 or status is null) order by order_index";
            **/
            string sql = "";
            if (schedule_id.IsInvalid())
            {
                sql = @$"SELECT ivd.day_id,ivd.day_name FROM info_operator iop 
                            LEFT JOIN info_visit_schedule ivs
                            on ivs.schedule_id=iop.visit_schedule_id and iop.company_id = ivs.company_id
                            LEFT JOIN info_visit_day ivd
                            on ivd.schedule_id=ivs.schedule_id and  iop.company_id = ivd.company_id
                            WHERE iop.oper_id='{operID}' and iop.company_id='{companyID}' and  (ivd.status is null or ivd.status = 1)";
            }
            else
            {
                sql = @$"SELECT ivd.day_id,ivd.day_name FROM info_visit_day ivd
                        WHERE schedule_id = '{schedule_id}'";
            }
            dynamic data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            if (data.Count == 0)
            {
                result = "bug";
                return Json(new { result, msg = "该行程暂无日程，请添加！" });
            }
            dynamic oneData = data[0];
            if (data.Count == 1 && oneData.day_id == "")
            {
                result = "bug";
                return Json(new { result });
            }

            string msg = "";
            return Json(new { result, msg, data });
        }

        /// <summary>
        /// 显示日程列表
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="seller_id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> LoadTheDaySupcusts(string operKey, string cur_lng, string cur_lat, string dayID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            /**            string sql = @$"select day_id,day_name,c.supcust_id,sup_name from info_visit_day d left join info_visit_day_client c on d.day_id = c.day_id
                                        left join info_supcust s on c.supcust_id = s.supcust_id
                                        where company_id = {companyID} and selller_id = {seller_id} and (status = 1 or status is null) order by order_index";
            **/
            string distance_sql =
                @$"st_distance(ST_Transform(ST_SetSRID(isp.addr_lnglat,4326)::geometry, 3857),ST_Transform('SRID=4326;POINT({cur_lng} {cur_lat})'::geometry, 3857)) as distance";
            string sql = @$"
SELECT day_id,ivdc.supcust_id,order_index,isp.mobile as sup_tel,isp.sup_name,isp.mobile,isp.addr_lng,isp.addr_lat,isp.sup_addr,{distance_sql},rsp.realtime->>'lastSaleTime' as last_sale_time,rsp.realtime->>'lastVisitTime' as last_visit_time
FROM info_visit_day_client ivdc
LEFT JOIN info_supcust     isp on isp.company_id ={companyID} and ivdc.supcust_id = isp.supcust_id  
LEFT JOIN realtime_supcust rsp on rsp.company_id ={companyID} and ivdc.supcust_id = rsp.supcust_id
WHERE day_id='{dayID}' and ivdc.company_id='{companyID}' and COALESCE(isp.status,'1') = '1' order by order_index";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new {result, msg, data});
        }
        
        [HttpPost]
        public async Task<JsonResult> UpdateDisplayMaintainActionReview([FromBody] dynamic data)
        {
            string result = "OK"; 
            string msg = "";
            try
            {
                var currentTime = DateTime.Now.ToText();
                string operKey = data.operKey;
                bool review_refused = data.review_refused;
                string review_comment = data.review_comment;
                string maintain_id = data.maintain_id;
                string visitTime = data.visit_time;
                string months = DateTime.Parse(visitTime).ToString("yyyy-MM") + "-01 00:00:00";
                string clientId = data.client_id;
                string dispTempId = data.disp_temp_id;
                string dispSheetId = data.disp_sheet_id;
                string sumMaintainId = data.sum_maintain_id;
                string visit_id = data.visit_id;
                string dispSheetNo = data.disp_sheet_no;
                string supName = data.supName;
                int maintainTimes = data.maintain_times;
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                CDbDealer db = new CDbDealer();
                string sql = "";
                string commandTextSQL = "";
                db.AddField("reviewer", operID);
                db.AddField("review_time", currentTime);
                db.AddField("review_comment", review_comment);
                db.AddField("review_refused", review_refused.ToString());
                sql = db.GetUpdateSQL("op_display_maintain", $@" company_id = {companyID} AND maintain_id = {maintain_id};");
                commandTextSQL = sql;
                // 更新消息表
                Dictionary<string, dynamic> updateMessageItem = await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey,
                    msgClass = MessageType.ClassType.Todo,
                    msgId = "",
                    msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                    msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey,
                    visitID = visit_id,
                    dispSheetID = dispSheetId,
                    supcustID = clientId
                }, cmd);
                string receiverId = ""; 
                string msgContent = "";
                if (updateMessageItem.ContainsKey("createrId"))
                {
                    receiverId = updateMessageItem["createrId"];
                    msgContent = updateMessageItem["msgContent"];
                }
                // 创建消息
                if (review_refused)
                {   // 不通过需要创建消息
                    if (!receiverId.Equals(""))
                    {
                        dynamic msgContentObj = JsonConvert.DeserializeObject(msgContent);
                        await MessageCreateServices.CreateMessageService(new
                        {
                            operKey,
                            createrId = operID,
                            msgClass = MessageType.ClassType.ProofPolish,
                            msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                            msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey,
                            receiverId = receiverId,
                            visitID = visit_id,
                            dispSheetID = dispSheetId,
                            supcustID = clientId,
                            sellerDept = msgContentObj.sellerDept,
                            supGroup = msgContentObj.supGroup,
                            supRank = msgContentObj.supRank,
                            sellerId = operID,
                            msgTitle = @$"{supName}的陈列单据{dispSheetNo}维护复核未通过，请尽快重新上传",
                            startTime = msgContentObj.startTime,
                            endTime = msgContentObj.endTime,
                        }, cmd);
                    }
                }
                else
                {   // false 说明通过， 需要创建通知
                    if (maintainTimes == 0)
                    {
                        sql = $@"INSERT INTO sum_display_maintain (company_id, client_id, disp_temp_id, disp_sheet_id, maintain_times, months)
VALUES ('{companyID}', '{clientId}','{dispTempId}', '{dispSheetId}', 1, '{months}');";
                    }
                    else
                    {
                        sql = $@"UPDATE sum_display_maintain SET maintain_times = sum_display_maintain.maintain_times + 1 WHERE company_id = '{companyID}' AND sum_maintain_id = '{sumMaintainId}';";
                    }
                    commandTextSQL += sql;
                    // 通知到当时的创建单据的业务员
                    if (!receiverId.Equals(""))
                    {
                        await MessageCreateServices.CreateMessageService(new
                        {
                            operKey,
                            createrId = operID,
                            msgClass = MessageType.ClassType.Notice,
                            msgType = MessageType.NoticeMessageType.CommonNotice.NoticeType,
                            msgSubType =MessageType.NoticeMessageType.CommonNotice.NoticeSubType.CommonNoticeSubType.SubTypeKey,
                            receiverId,
                            msgTitle = @$"{supName}中的{dispSheetNo}单据本次维护复核已通过",
                        }, cmd);
                    }
                }
                cmd.CommandText = commandTextSQL;
                await cmd.ExecuteNonQueryAsync();
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, currentTime, operID });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = "复核失败";
                return new JsonResult(new { result, msg });
            }
        }
        
        /// <summary>
        /// 复核不通过，重新上传
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> UpdateDisplayMaintainWorkContent([FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            try
            {
                var currentTime = DateTime.Now.ToText();
                string operKey = data.operKey;
                string workContent = data.workContent;
                string maintain_id = data.maintain_id;
                string visit_id = data.visit_id;
                string dispSheetId = data.disp_sheet_id;
                string dispSheetNo = data.disp_sheet_no;
                string supName = data.supName;
                string clientId = data.client_id;
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                //需要二次处理上传OBS
                string subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey;
                string supId = data.supcust_id;
                string uploadsMainPath = $@"{companyID}_{subType}_{operID}_{supId}";
                workContent = await ActionsTemplateUtils.HandleActionTemplate(workContent, uploadsMainPath, _httpClientFactory);
                string sql = @$"UPDATE op_display_maintain SET work_content = '{workContent}', reviewer = null ,review_time = null ,review_comment = null ,review_refused = null WHERE company_id = {companyID} AND maintain_id = '{maintain_id}' RETURNING maintain_id";
                dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                string maintainId = "";
                if (rec != null)
                {
                    maintainId = rec.maintain_id;
                }
                
                // 更新待办消息
                Dictionary<string, dynamic> updateMessageItem = await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey,
                    msgClass = MessageType.ClassType.ProofPolish,
                    msgId = "",
                    msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                    msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey,
                    visitID = visit_id,
                    dispSheetID = dispSheetId,
                    supcustID = clientId
                }, cmd);
                string receiverId = ""; 
                string msgContent = "";
                if (updateMessageItem.ContainsKey("createrId"))
                {
                    receiverId = updateMessageItem["createrId"];
                    msgContent = updateMessageItem["msgContent"];
                    dynamic msgContentObj = JsonConvert.DeserializeObject(msgContent);
                    await MessageCreateServices.CreateMessageService(new
                    {
                        operKey,
                        createrId = operID,
                        msgClass = MessageType.ClassType.Todo,
                        msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                        msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey,
                        receiverId,
                        visitID = visit_id,
                        dispSheetID = dispSheetId,
                        supcustID = clientId,
                        sellerDept = msgContentObj.sellerDept,
                        supGroup = msgContentObj.supGroup,
                        supRank = msgContentObj.supRank,
                        sellerId = msgContentObj.sellerId,
                        msgTitle = @$"{supName}的陈列单据{dispSheetNo}重新进行了维护，请尽快复核",
                        startTime = msgContentObj.startTime,
                        endTime = msgContentObj.endTime,
                    }, cmd);
                }
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, currentTime, maintainId });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = "更新失败";
                return new JsonResult(new { result, msg });
            }
        }

        [HttpPost]
        public async Task<JsonResult> UpdateDisplayKeepActionReview([FromBody] dynamic data)
        {
            string result = "OK"; 
            string msg = "";
            try
            {
                var currentTime = DateTime.Now.ToText();
                string operKey = data.operKey;
                bool review_refused = data.review_refused;
                string review_comment = data.review_comment;
                string keep_id = data.keep_id;
                string visitTime = data.visit_time;
                string clientId = data.client_id;
                string dispTempId = data.disp_temp_id;
                string dispSheetId = data.disp_sheet_id;
                string sumKeepId = data.sum_keep_id;
                int keepTimes = data.keep_times;
                int needKeepTimes = data.need_keep_times;
                string visit_id = data.visit_id;
                string dispSheetNo = data.disp_sheet_no;
                string supName = data.supName;
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                CDbDealer db = new CDbDealer();
                string sql = "";
                string commandTextSQL = "";
                db.AddField("reviewer", operID);
                db.AddField("review_time", currentTime);
                db.AddField("review_comment", review_comment);
                db.AddField("review_refused", review_refused.ToString());
                sql = db.GetUpdateSQL("op_display_keep", $@" company_id = {companyID} AND keep_id = {keep_id};");
                commandTextSQL = sql;
                // 更新消息表
                Dictionary<string, dynamic> updateMessageItem = await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey,
                    msgClass = MessageType.ClassType.Todo,
                    msgId = "",
                    msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                    msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayKeepSubType.SubTypeKey,
                    visitID = visit_id,
                    dispSheetID = dispSheetId,
                    supcustID = clientId
                }, cmd);

                string receiverId = ""; 
                string msgContent = "";
                if (updateMessageItem.ContainsKey("createrId"))
                {
                    receiverId = updateMessageItem["createrId"];
                    msgContent = updateMessageItem["msgContent"];
                }

                
                if (review_refused)    // false 说明通过
                {       // 不通过需要创建消息
                    if (!receiverId.Equals(""))
                    {
                        dynamic msgContentObj = JsonConvert.DeserializeObject(msgContent);
                        await MessageCreateServices.CreateMessageService(new
                        {
                            operKey,
                            createrId = operID,
                            msgClass = MessageType.ClassType.ProofPolish,
                            msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                            msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayKeepSubType.SubTypeKey,
                            receiverId = receiverId,
                            visitID = visit_id,
                            dispSheetID = dispSheetId,
                            supcustID = clientId,
                            sellerDept = msgContentObj.sellerDept,
                            supGroup = msgContentObj.supGroup,
                            supRank = msgContentObj.supRank,
                            sellerId = operID,
                            msgTitle = @$"{supName}的陈列单据{dispSheetNo}续签复核未通过，请尽快重新上传",
                            startTime = msgContentObj.startTime,
                            endTime = msgContentObj.endTime,
                        }, cmd);
                    }
                }
                else
                {
                    if (keepTimes == 0)
                    {
                        sql = $@"INSERT INTO sum_display_keep (company_id, client_id, disp_temp_id, disp_sheet_id, keep_times, need_keep_times)
VALUES ('{companyID}', '{clientId}','{dispTempId}', '{dispSheetId}', 1, '{needKeepTimes}');";
                    }
                    else
                    {
                        sql = $@"UPDATE sum_display_keep SET keep_times = sum_display_keep.keep_times + 1 WHERE company_id = '{companyID}' AND sum_keep_id = '{sumKeepId}';";
                    }
                    commandTextSQL += sql;
                    // 通知到当时的创建单据的业务员
                    if (!receiverId.Equals(""))
                    {
                        await MessageCreateServices.CreateMessageService(new
                        {
                            operKey,
                            createrId = operID,
                            msgClass = MessageType.ClassType.Notice,
                            msgType = MessageType.NoticeMessageType.CommonNotice.NoticeType,
                            msgSubType =MessageType.NoticeMessageType.CommonNotice.NoticeSubType.CommonNoticeSubType.SubTypeKey,
                            receiverId,
                            msgTitle = @$"{supName}中的{dispSheetNo}单据本次续签复核已通过",
                        }, cmd);
                    }
                }
                cmd.CommandText = commandTextSQL;
                await cmd.ExecuteNonQueryAsync();
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, currentTime, operID });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = "复核失败";
                return new JsonResult(new { result, msg });
            }
        }
        [HttpPost]
        public async Task<JsonResult> UpdateDisplayKeepWorkContent([FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            try
            {
                var currentTime = DateTime.Now.ToText();
                string operKey = data.operKey;
                string workContent = data.workContent;
                string keep_id = data.keep_id;
                string supId = data.supcust_id;
                string visit_id = data.visit_id;
                string dispSheetId = data.disp_sheet_id;
                string dispSheetNo = data.disp_sheet_no;
                string supName = data.supName;
                string clientId = data.client_id;
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                //需要二次处理上传OBS
                string subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayKeepSubType.SubTypeKey;
                string uploadsMainPath = $@"{companyID}_{subType}_{operID}_{supId}";
                workContent = await ActionsTemplateUtils.HandleActionTemplate(workContent, uploadsMainPath, _httpClientFactory);
                string sql = @$"UPDATE op_display_keep SET work_content = '{workContent}', reviewer = null ,review_time = null ,review_comment = null ,review_refused = null WHERE company_id = {companyID} AND keep_id = '{keep_id}' RETURNING keep_id";
                dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                string keepId = "";
                if (rec != null)
                {
                    keepId = rec.keep_id;
                }
                // 更新待办消息
                Dictionary<string, dynamic> updateMessageItem = await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey,
                    msgClass = MessageType.ClassType.ProofPolish,
                    msgId = "",
                    msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                    msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayKeepSubType.SubTypeKey,
                    visitID = visit_id,
                    dispSheetID = dispSheetId,
                    supcustID = clientId
                }, cmd);
                string receiverId = ""; 
                string msgContent = "";
                if (updateMessageItem.ContainsKey("createrId"))
                {
                   receiverId = updateMessageItem["createrId"];
                   msgContent = updateMessageItem["msgContent"];
                   dynamic msgContentObj = JsonConvert.DeserializeObject(msgContent);
                   await MessageCreateServices.CreateMessageService(new
                   {
                       operKey,
                       createrId = operID,
                       msgClass = MessageType.ClassType.Todo,
                       msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                       msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayKeepSubType.SubTypeKey,
                       receiverId,
                       visitID = visit_id,
                       dispSheetID = dispSheetId,
                       supcustID = clientId,
                       sellerDept = msgContentObj.sellerDept,
                       supGroup = msgContentObj.supGroup,
                       supRank = msgContentObj.supRank,
                       sellerId = msgContentObj.sellerId,
                       msgTitle = @$"{supName}的陈列单据{dispSheetNo}重新进行了续签，请尽快复核",
                       startTime = msgContentObj.startTime,
                       endTime = msgContentObj.endTime,
                   }, cmd);
                }
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, currentTime, keepId });
            }
            catch (Exception e)
            {
                result = "Error";
                msg = "更新失败";
                return new JsonResult(new { result, msg });
            }
        }
        
        /// <summary>
        /// 复核 补录单据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> UpdateDisplayMaintainForMonthActionReview([FromBody] dynamic data)
        {
            string result = "OK"; 
            string msg = "";
            try
            {
                string operKey = data.operKey;
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                var currentTime = DateTime.Now.ToText();
                bool review_refused = data.review_refused;
                string review_comment = data.review_comment;
                string maintain_id = data.maintain_id;
                string visitTime = data.visit_time;
                string forMonth = data.for_month; // yyyy-MM-dd
                string months = DateTime.Parse(forMonth).ToString("yyyy-MM") + "-01 00:00:00";
                string clientId = data.client_id;
                string dispTempId = data.disp_temp_id;
                string dispSheetId = data.disp_sheet_id;
                string sumMaintainId = data.sum_maintain_id;
                string visit_id = data.visit_id;
                string dispSheetNo = data.disp_sheet_no;
                string supName = data.supName;
                int maintainTimes = data.maintain_times;
                int indexArr = data.indexArr;
                string subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey;
                string uploadsMainPathForMonthFlag = $@"{companyID}_{subType}_{operID}_{clientId}_{visit_id}_{dispSheetId}_{forMonth}_{indexArr}";
                string uploadsMainPath = $@"{companyID}_{subType}_{operID}_{clientId}_{visit_id}_{dispSheetId}_{forMonth}_{indexArr}";
               
                CDbDealer db = new CDbDealer();
                string sql = "";
                string commandTextSQL = "";
                db.AddField("reviewer", operID);
                db.AddField("review_time", currentTime);
                db.AddField("review_comment", review_comment);
                db.AddField("review_refused", review_refused.ToString());
                sql = db.GetUpdateSQL("op_display_maintain", $@" company_id = {companyID} AND maintain_id = {maintain_id};");
                commandTextSQL = sql;
                // 更新消息表
                Dictionary<string, dynamic> updateMessageItem = await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey,
                    msgClass = MessageType.ClassType.Todo,
                    msgId = "",
                    msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                    msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey,
                    visitID = visit_id,
                    dispSheetID = dispSheetId,
                    supcustID = clientId,
                    uploadsMainPathForMonthFlag
                }, cmd);
                string receiverId = ""; 
                string msgContent = "";
                if (updateMessageItem.ContainsKey("createrId"))
                {
                    receiverId = updateMessageItem["createrId"];
                    msgContent = updateMessageItem["msgContent"];
                }
                // 创建消息
                if (review_refused)
                {   // 不通过需要创建消息
                    if (!receiverId.Equals(""))
                    {
                        dynamic msgContentObj = JsonConvert.DeserializeObject(msgContent);
                        await MessageCreateServices.CreateMessageService(new
                        {
                            operKey,
                            createrId = operID,
                            msgClass = MessageType.ClassType.ProofPolish,
                            msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                            msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey,
                            receiverId = receiverId,
                            visitID = visit_id,
                            dispSheetID = dispSheetId,
                            supcustID = clientId,
                            sellerDept = msgContentObj.sellerDept,
                            supGroup = msgContentObj.supGroup,
                            supRank = msgContentObj.supRank,
                            sellerId = operID,
                            msgTitle = @$"{supName}的陈列单据{dispSheetNo}补录复核未通过，请尽快重新上传",
                            startTime = msgContentObj.startTime,
                            endTime = msgContentObj.endTime,
                            uploadsMainPath
                        }, cmd);
                    }
                }
                else
                {   // false 说明通过， 需要创建通知
                    if (maintainTimes == 0)
                    {
                        sql = $@"INSERT INTO sum_display_maintain (company_id, client_id, disp_temp_id, disp_sheet_id, maintain_times, months)
VALUES ('{companyID}', '{clientId}','{dispTempId}', '{dispSheetId}', 1, '{months}');";
                    }
                    else
                    {
                        sql = $@"UPDATE sum_display_maintain SET maintain_times = sum_display_maintain.maintain_times + 1 WHERE company_id = '{companyID}' AND months = '{months}';";
                    }
                    commandTextSQL += sql;
                    // 通知到当时的创建单据的业务员
                    if (!receiverId.Equals(""))
                    {
                        await MessageCreateServices.CreateMessageService(new
                        {
                            operKey,
                            createrId = operID,
                            msgClass = MessageType.ClassType.Notice,
                            msgType = MessageType.NoticeMessageType.CommonNotice.NoticeType,
                            msgSubType =MessageType.NoticeMessageType.CommonNotice.NoticeSubType.CommonNoticeSubType.SubTypeKey,
                            receiverId,
                            msgTitle = @$"{supName}中的{dispSheetNo}单据本次补录复核已通过",
                        }, cmd);
                    }
                }
                cmd.CommandText = commandTextSQL;
                await cmd.ExecuteNonQueryAsync();
                if (msg != "") result = "Error";
               
                return new JsonResult(new { result, msg, currentTime, operID });

            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                result = "Error";
                msg = "复核失败";
               
                return new JsonResult(new { result, msg });
            }
        }
        
                /// <summary>
        /// 复核不通过，重新上传
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> UpdateDisplayMaintainWorkContentForMonth([FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            try
            {
                var currentTime = DateTime.Now.ToText();
                string operKey = data.operKey;
                string workContent = data.workContent;
                string maintain_id = data.maintain_id;
                string visit_id = data.visit_id;
                string dispSheetId = data.disp_sheet_id;
                string dispSheetNo = data.disp_sheet_no;
                string supName = data.supName;
                string clientId = data.client_id;
                string forMonth = data.for_month; // yyyy-MM-dd
                int indexArr = data.indexArr;
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                //需要二次处理上传OBS
                string subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey;
                string supId = data.supcust_id;
                string uploadsMainPathOld = $@"{companyID}_{subType}_{operID}_{supId}";
                workContent = await ActionsTemplateUtils.HandleActionTemplate(workContent, uploadsMainPathOld, _httpClientFactory);
                string sql = @$"UPDATE op_display_maintain SET work_content = '{workContent}', reviewer = null ,review_time = null ,review_comment = null ,review_refused = null WHERE company_id = {companyID} AND maintain_id = '{maintain_id}' RETURNING maintain_id";
                dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                string maintainId = "";
                if (rec != null)
                {
                    maintainId = rec.maintain_id;
                }
                string uploadsMainPathForMonthFlag = $@"{companyID}_{subType}_{operID}_{clientId}_{visit_id}_{dispSheetId}_{forMonth}_{indexArr}";

                // 更新待办消息
                Dictionary<string, dynamic> updateMessageItem = await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey,
                    msgClass = MessageType.ClassType.ProofPolish,
                    msgId = "",
                    msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                    msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey,
                    visitID = visit_id,
                    dispSheetID = dispSheetId,
                    supcustID = clientId,
                    uploadsMainPathForMonthFlag
                }, cmd);
                string receiverId = ""; 
                string msgContent = "";
                if (updateMessageItem.ContainsKey("createrId"))
                {
                    receiverId = updateMessageItem["createrId"];
                    msgContent = updateMessageItem["msgContent"];
                    dynamic msgContentObj = JsonConvert.DeserializeObject(msgContent);
                    string uploadsMainPath = $@"{companyID}_{subType}_{operID}_{clientId}_{visit_id}_{dispSheetId}_{forMonth}_{indexArr}";
                    await MessageCreateServices.CreateMessageService(new
                    {
                        operKey,
                        createrId = operID,
                        msgClass = MessageType.ClassType.Todo,
                        msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                        msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayMaintainSubType.SubTypeKey,
                        receiverId,
                        visitID = visit_id,
                        dispSheetID = dispSheetId,
                        supcustID = clientId,
                        sellerDept = msgContentObj.sellerDept,
                        supGroup = msgContentObj.supGroup,
                        supRank = msgContentObj.supRank,
                        sellerId = msgContentObj.sellerId,
                        msgTitle = @$"{supName}的陈列单据{dispSheetNo}重新进行了补录，请尽快复核",
                        startTime = msgContentObj.startTime,
                        endTime = msgContentObj.endTime,
                        uploadsMainPath //用于补录的查找
                    }, cmd);
                }
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, currentTime, maintainId });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = "更新失败";
                return new JsonResult(new { result, msg });
            }
        }
        
    }
}