﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowPlaceholderOrder : SheetRowMM
    {
        [SaveToDB][FromFld] public string last_time_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_provenance { get; set; }
        private string _attr_qty = "";

        [SaveToDB]
        [FromFld]
        public override string attr_qty
        {
            get
            {
                return _attr_qty;
            }
            set
            {
                if (value == "[]") value = "";
                _attr_qty = value;
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public override string sale_print_combine_attr { get; set; } = "";//合并打印多口味


        private JArray _mum_attributes = null;
        [FromFld("case when son_mum_attributes is not null then son_mum_attributes else mum_attributes end", LOAD_PURPOSE.SHOW)]
        public string mum_attributes
        {
            get
            {
                if (_mum_attributes == null) return "";
                if (son_mum_item != "")
                {
                    for (int i = _mum_attributes.Count - 1; i >= 0; i--)
                    {
                        JObject a = (JObject)_mum_attributes[i];
                        JValue s = (JValue)a.GetValue("distinctStock");
                        if (s != null && s.ToString().ToLower() == "true")
                        {
                            _mum_attributes.RemoveAt(i);
                        }
                    }
                }
                if (_mum_attributes.Count == 0) return "";
                return JsonConvert.SerializeObject(_mum_attributes);
            }

            set
            {
                if (value == null) _mum_attributes = null;
                else
                {
                    _mum_attributes = JsonConvert.DeserializeObject<JArray>(value);
                }
            }
        }
        public float cost_price_recent1;
        public float cost_price_recent2;
        public float cost_price_recent3;
        [SaveToDB][FromFld("t.cost_price_buy", LOAD_PURPOSE.SHOW)] public string cost_price_buy { get; set; }
        [SaveToDB][FromFld("t.cost_price_prop", LOAD_PURPOSE.SHOW)] public string cost_price_prop { get; set; }
        [FromFld("setting->>'costPriceType'", LOAD_PURPOSE.SHOW)] public string cost_price_type { get; set; }
        [FromFld("setting->>'recentPriceTime'", LOAD_PURPOSE.SHOW)] public string recent_price_time { get; set; }
        public decimal cost_price
        {
            get
            {
                decimal n = 0;
                if (cost_price_type == "2")
                {
                    if (cost_price_avg.IsValid())
                        n = CPubVars.ToDecimal(cost_price_avg) * unit_factor;
                }
                else if (cost_price_type == "3")
                {
                    if (cost_price_buy.IsValid())
                        n = CPubVars.ToDecimal(cost_price_buy) * unit_factor;
                }
                else if (cost_price_type == "1")
                {
                    if (cost_price_prop.IsValid())
                        n = CPubVars.ToDecimal(cost_price_prop) * unit_factor;
                }

                else if (cost_price_type == "4")
                {
                    if (recent_price_time == "1")
                    {
                        n = CPubVars.ToDecimal(cost_price_recent1) * unit_factor;
                    }
                    else if (recent_price_time == "2")
                    {
                        n = CPubVars.ToDecimal(cost_price_recent2) * unit_factor;
                    }
                    else if (recent_price_time == "3")
                    {
                        n = CPubVars.ToDecimal(cost_price_recent3) * unit_factor;
                    }
                }
                n = Math.Round(n, 4);
                //n = Convert.ToSingle(CPubVars.FormatMoney(n, 4));
                return n;
            }
        }
        public decimal money_amount
        {
            get
            {
                decimal amt = sub_amount;
                if (this.order_sub_id.IsValid()) amt = 0m;
                return amt;
            }
        }
        public decimal cost_amount
        {
            get
            {
                decimal n = cost_price * quantity;
                n = Math.Round(n, 2);
                // n = Convert.ToSingle(CPubVars.FormatMoney(n, 2));
                return n;
            }
        }
        public decimal profit
        {
            get
            {
                return Math.Round(sub_amount - cost_amount, 2);
            }
        }
        public string profit_rate
        {
            get
            {
                string s = "";
                if (sub_amount != 0)
                {
                    s = CPubVars.FormatMoney(profit / sub_amount * 100, 1);
                }
                return s;
            }
        }

        public bool isSpecialPrice { get; set; } = false;
        public string special_price { get; set; } = "";
        public string scanBarcode { get; set; } = "";
        public string order_sub_id { get; set; } = "";
        [FromFld("cw.sub_name")] public string order_sub_name { get; set; }

        [SaveToDB]
        [FromFld]
        public string other_info
        {
            get
            {
                IDictionary<string, object> d = new ExpandoObject();
                if (scanBarcode != "") d["scanBarcode"] = scanBarcode;
                if (disp_flow_id != "") d["dispFlowID"] = disp_flow_id;
                if (disp_month_id != "") d["dispMonthID"] = disp_month_id;
                if (disp_sheet_id != "") d["dispSheetID"] = disp_sheet_id;
                if (order_sub_id != "") d["orderSubID"] = order_sub_id;
                if (disp_template_id != "") d["dispTmpID"] = disp_template_id;
                if (isSpecialPrice) d["isSpecialPrice"] = isSpecialPrice;
                if (special_price != "") d["specialPrice"] = special_price;
                if (d.Count > 0)
                {
                    return JsonConvert.SerializeObject(d);
                }
                return "";
            }
            set
            {
                if (value.IsValid())
                {
                    dynamic d = JsonConvert.DeserializeObject(value);
                    if (d.scanBarcode != null) scanBarcode = d.scanBarcode;
                    if (d.dispFlowID != null) disp_flow_id = d.dispFlowID;
                    if (d.dispMonthID != null) disp_month_id = d.dispMonthID;
                    if (d.dispSheetID != null) disp_sheet_id = d.dispSheetID;
                    if (d.orderSubID != null) order_sub_id = d.orderSubID;
                    if (d.dispTmpID != null) disp_template_id = d.dispTmpID;
                    if (d.isSpecialPrice != null) isSpecialPrice = d.isSpecialPrice;
                    if (d.specialPrice != null) special_price = d.specialPrice;

                }
            }
        }

        [SaveToDB][FromFld] public override string trade_type { get; set; } = "";
        [FromFld("(case trade_type when 'J' then '借货' when 'H' then '还货' end) trade_type_name", LOAD_PURPOSE.SHOW)] public string trade_type_name { get; set; } = "";
        [SaveToDB][FromFld] public string disp_sheet_id { get; set; } = ""; //使用的陈列协议单号
        [SaveToDB][FromFld] public string disp_flow_id { get; set; } = "";
        [SaveToDB][FromFld] public string disp_month_id { get; set; } = ""; //使用的陈列协议 对应数据库month1、month2
        public string disp_template_id { get; set; } = ""; // 陈列协议模板

        public string move_stock { get; set; } = "";
        internal decimal disp_month_left { get; set; } = 0;
         [FromFld("m.sheet_no")] public string sheet_no { get; set; }
    }

    public class SheetPlaceholderOrder : SheetMM<SheetRowPlaceholderOrder>
    {
        //[SaveToDB] [FromFld] public string appendix_photos { get; set; } = "[]";
        //[SaveToDB] [FromFld] 
        public bool modify { get; set; } = false;
        [SaveToDB][FromFld] public string sale_order_sheet_id { get; set; } = "";
        [FromFld("ssom.sheet_no")] public string sale_order_sheet_no { get; set; } = "";
        public string[] appendixPhotos { get; set; }
        public dynamic[] displayGiveProofs { get; set; }

        public decimal? redpacket_use_amount { get; set; }
        public decimal? redpacket_earn { get; set; }

        [SaveToDB][FromFld] public string shop_id { get; set; } = "";
        [SaveToDB][FromFld] public string visit_id { get; set; } = "";
        [SaveToDB][FromFld] public bool is_retail { get; set; }
        [SaveToDB][FromFld] public string order_source { get; set; } = ""; // 单据来源

        [SaveToDB][FromFld] public string review_time { get; set; } = "";
        [SaveToDB][FromFld] public string reviewer_id { get; set; } = "";
        public bool bReview { get; set; } = false;

        [FromFld(LOAD_PURPOSE.SHOW)] public string reviewer_name { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override decimal prepay_amount { get; set; }
        private string _senders_id = "";
        [SaveToDB]
        [FromFld]
        public string senders_id
        {
            get
            {
                if (assigned_senders_id != "") return assigned_senders_id;
                else return _senders_id ?? "";
            }
            set
            {
                _senders_id = value;
            }
        }
        private string _senders_name = "";
        [SaveToDB]
        [FromFld]
        public string senders_name
        {
            get
            {
                if (assigned_senders_name != "") return assigned_senders_name;
                else return _senders_name ?? "";
            }
            set
            {
                _senders_name = value;
                assigned_senders_name = "";
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public string sender_mobile { get; set; } = "";
        [FromFld("tb_status.van_id")] public string van_id { get; set; } = "";
        [FromFld("van.van_name")] public string van_name { get; set; } = "";
        [FromFld("tb_status.move_stock")] public string move_stock { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string license_no { get; set; } = "";
        [FromFld("tb_status.order_status")] public string order_status { get; set; } = "";
        [FromFld("tb_status.receipt_status")] public string receipt_status { get; set; } = "";
        [FromFld("tb_status.sale_sheet_id")] public string sale_sheet_id { get; set; } = "";
        [FromFld("tb_status.assigned_senders_id", LOAD_PURPOSE.SHOW)] public string assigned_senders_id { get; set; } = "";
        [FromFld("tb_status.assigned_senders_name", LOAD_PURPOSE.SHOW)] public string assigned_senders_name { get; set; } = "";
        [FromFld("tb_status.move_sheet_no")] public string move_sheet_no { get; set; } = "";
        [FromFld("tb_status.move_sheet_id")] public string move_sheet_id { get; set; } = "";
        [SaveToDB] [FromFld("to_char(t.send_time,'yyyy-MM-dd hh24:mi:ss') as send_time")] public string send_time { get; set; } = "";


        // [FromFld("sheet_attribute->>'returnAmt'", LOAD_PURPOSE.SHOW)] public float return_amount { get; set; } = 0;
        [SaveToDB]
        [FromFld]
        public override string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                string baseAttr = base.sheet_attribute;
                if (baseAttr != "") sheetAttribute = JsonConvert.DeserializeObject<Dictionary<string, string>>(baseAttr);

                foreach (var row in SheetRows)
                {
                    //陈列协议
                    if (row.disp_flow_id != "" && row.quantity != 0! && !sheetAttribute.ContainsKey("display")) sheetAttribute.Add("display", "true");

                    string tradeType = (row.trade_type).ToLower();
                    if (row.trade_type != "" && row.trade_type != "x" && row.trade_type != "t" && !sheetAttribute.ContainsKey(tradeType)) sheetAttribute.Add(tradeType, "true");

                }

                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set
            {
                base.sheet_attribute = value;

            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public override string print_count { get; set; } = "";

        public decimal sale_amount
        {
            get { return total_amount + return_amount; }
        }

        #region 支付系统相关
        [SaveToDB][FromFld] public string pay_bill_id { get; set; } = "";

        /// <summary> 关联支付系统订单的订单状态 </summary>
        [FromFld(LOAD_PURPOSE.SHOW)] public string payb_status { get; set; } = "";

        /// <summary> 关联支付系统订单的订单单号 </summary>
        [FromFld(LOAD_PURPOSE.SHOW)] public string payb_trade_no { get; set; } = "";

        public string payBillStatusText
        {
            get
            {
                return payb_status switch
                {
                    WebAPI.PayBillController.BillStatus.Paid => "已在线支付",
                    /* WebAPI.PayBillController.BillStatus.Unpaid => "未支付",
                    WebAPI.PayBillController.BillStatus.Returned => "已退款",
                    WebAPI.PayBillController.BillStatus.Canceled => "已取消", */
                    _ => "",
                };
            }
        }
        #endregion

        [JsonConstructor]  //if there is more constructor, it is necessary to set on one of them
        public SheetPlaceholderOrder(SHEET_RETURN sheetReturn, LOAD_PURPOSE loadPurpose) : base("sheet_placeholder_order_main", "sheet_placeholder_order_detail", loadPurpose)
        {
            sheet_type = sheetReturn == SHEET_RETURN.IS_RETURN ? SHEET_TYPE.SHEET_SALE_DD_RETURN : SHEET_TYPE.SHEET_PLACEHOLDER_DD;
            ConstructFun();
        }
        public SheetPlaceholderOrder() : base("sheet_placeholder_order_main", "sheet_placeholder_order_detail", LOAD_PURPOSE.SHOW)
        {
            sheet_type = SHEET_TYPE.SHEET_PLACEHOLDER_DD;
            ConstructFun();
        }
        private void ConstructFun()
        {
            MainLeftJoin += $@"
left join (select sso.van_id,sso.move_stock,sso.sheet_id as status_sheet_id,sso.order_status,sso.receipt_status,sso.senders_id as assigned_senders_id,sso.senders_name as assigned_senders_name, sso.sale_sheet_id,sso.sheet_print_count print_count,sso.company_id,om.move_sheet_id,van_move.sheet_no move_sheet_no from sheet_status_order sso 
LEFT JOIN op_move_to_van_main om on om.company_id = ~COMPANY_ID and om.op_id = sso.assign_van_id
LEFT JOIN sheet_move_main van_move on van_move.company_id = ~COMPANY_ID and van_move.sheet_id = om.move_sheet_id
WHERE sso.company_id = ~COMPANY_ID  ) tb_status on t.company_id=tb_status.company_id and t.sale_order_sheet_id=tb_status.status_sheet_id
left join sheet_sale_order_main ssom on ssom.company_id = ~COMPANY_ID and ssom.sheet_id = t.sale_order_sheet_id
left join (select company_id,branch_id as van_id,branch_name as van_name from info_branch) van on t.company_id=van.company_id and tb_status.van_id=van.van_id
left join (select oper_id,oper_name as sender_name,mobile as sender_mobile from info_operator) sender on split_part(coalesce(tb_status.assigned_senders_id::text,t.senders_id::text),',',1)::integer=sender.oper_id                
left join (select oper_id,oper_name as reviewer_name,company_id from info_operator) reviewer on t.company_id=reviewer.company_id and t.reviewer_id=reviewer.oper_id         
left join (select bill_id as payb_flowid, bill_status as payb_status, trade_no as payb_trade_no from pay_bill where company_id=~COMPANY_ID) payb on t.pay_bill_id = payb.payb_flowid
 
       ";
            DetailLeftJoin += $@"  
left join (select company_id,attr_id,sale_combine_print as sale_print_combine_attr from info_attribute where company_id=~COMPANY_ID) attr on (son_attrs.son_mum_attributes->0->>'attrID')::integer=attr.attr_id
left join cw_subject  cw on cw.company_id=~COMPANY_ID and cw.sub_id::text=COALESCE(t.other_info->>'orderSubID','0')
    
       ";

        }
        public SheetPlaceholderOrder(LOAD_PURPOSE loadPurpose) : base("sheet_placeholder_order_main", "sheet_placeholder_order_detail", loadPurpose)
        {
            ConstructFun();
        }

        public override string Init()
        {
            string msg = base.Init();
            if (msg != "") return msg;
            foreach (var row in SheetRows)
            {
                if (row != null)
                {
                    if (row.trade_type == "KS") row.inout_flag = 0;
                }
            }
            return "";

        }

        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql = "";
            if (IsImported) return;
            if (maker_id == "") maker_id = OperID;
            approver_id = OperID;
            if (sheet_no == "")
            {
                string sheetType = StrFromSheetType(sheet_type);
                sql = $"select o_tmp,o_company_month_num,o_company_num,o_oper_num,o_oper_no from yj_get_new_sheet_no({company_id},'{sheetType}',{maker_id})";
                QQ.Enqueue("sheet_no", sql);
            }
            string items_id = "";
            string batchs_id = "";
            string branchs_id = "";
            string branchs_position = "";
            foreach (SheetRowPlaceholderOrder row in SheetRows)
            {
                if (items_id != "") items_id += ",";
                items_id += row.item_id;
                if (batchs_id != "") batchs_id += ",";
                batchs_id += row.batch_id;
                if (branchs_position != "") branchs_position += ",";
                branchs_position += row.branch_position.IsValid() ? row.branch_position:"0";
                if (branchs_id != "") branchs_id += ",";
                branchs_id += row.branch_id.IsInvalid() ? branch_id : row.branch_id;
            }
            if (OperID != "")
            {
                sql = $"select rights->'delicacy'->'allowNegativeStock'->'value' role_allow_negative_stock,rights->'delicacy'->'allowNegativePrepay'->'value' role_allow_negative_prepay from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
                QQ.Enqueue("role_allow_negative_stock", sql);
            }
            if (SheetRows.Count > 0)
            { 
                sql = $"select allow_negative_stock branch_allow_negative_stock,negative_stock_accordance,branch_id from info_branch where company_id = {company_id} and branch_id in({branchs_id})";
                if (sheet_type == SHEET_TYPE.SHEET_SALE_DD || sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN)
                    sql = $"select allow_negative_stock_order branch_allow_negative_stock,negative_stock_accordance, branch_id from info_branch where company_id = {company_id} and branch_id in({branchs_id})";
                QQ.Enqueue("branch_allow_negative_stock", sql);
            }
            if (SheetRows.Count > 0)
            { 
                if(batchs_id == "")
                {
                    batchs_id = "0";
                }
                sql = $@"select item_id,stock_qty,branch_id,branch_position,sell_pend_qty,batch_id from stock where company_id = {company_id} and item_id in ({items_id}) and batch_id in({batchs_id}) and branch_id in ({branchs_id}) and branch_position in ({branchs_position});";
         
                QQ.Enqueue("stock", sql);
            }


        }
        string NotRememberPriceBriefs = "";
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName);
            //NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
        }
        public string GetRejectSQL(bool recover)
        {
            // CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";
            if (sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN) return "";
            var orderRows = (from row in this.SheetRows where row.quantity > 0 select row).ToList();
            //MergedSheetRows = MergeSheetRows(orderRows);
            MergedSheetRowByBatchAndItem = MergeSheetRowsByBatchAndItem(orderRows);

            if (!(red_flag == "2" && (this.move_stock.ToLower() == "true" || this.receipt_status == "js")))
            {
                foreach (SheetRowMM row in MergedSheetRowByBatchAndItem)
                {
                    string s = "";
                    string changeQty = "";

                    var qty = row.quantity;
                    if (red_flag == "2") qty *= -1;
                    changeQty = qty.ToString();
                    if (changeQty == "-0") changeQty = "0";
                    if (qty >= 0)
                    {
                        changeQty = "+" + qty.ToString();
                    }
                    else { changeQty = qty.ToString(); }
                    if (string.IsNullOrEmpty(row.batch_id)) row.batch_id = "0";
                    if (string.IsNullOrEmpty(row.branch_position)) row.branch_position = "0";
                    if (string.IsNullOrEmpty(row.branch_id)) row.branch_id = branch_id;
                    s = $"insert into stock(company_id,branch_id,item_id,stock_qty,sell_pend_qty,batch_id,branch_position) values ({company_id},{branch_id},{row.item_id},0,{qty},{row.batch_id},{row.branch_position}) on conflict (company_id,branch_id,item_id,batch_id,branch_position) do update set sell_pend_qty=stock.sell_pend_qty{changeQty};";
                    sql += s;
                    row.NewSellPendQty = row.SellPendQty + qty;
                }
            }
            return sql;

        }
        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";
            if (sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN) return "";
            var orderRows = (from row in this.SheetRows where row.quantity > 0 select row).ToList();
            MergedSheetRowByBatchAndItem = MergeSheetRowsByBatchAndItem(orderRows);

            if (!IsImported)
            {
                if (!(red_flag == "2" && (this.move_stock.ToLower() == "true" || this.receipt_status == "js")))
                {
                    foreach (SheetRowMM row in MergedSheetRowByBatchAndItem)
                    {
                        string s = "";
                        string changeQty = "";
                        var qty = row.quantity;
                        if (red_flag == "2") qty *= -1;
                        changeQty = qty.ToString();
                        if (changeQty == "-0") changeQty = "0";
                        if (qty >= 0)
                        {
                            changeQty = "+" + qty.ToString();
                        }
                        else { changeQty = qty.ToString(); }


                        if (string.IsNullOrEmpty(row.batch_id)) row.batch_id = "0";
                        if (string.IsNullOrEmpty(row.branch_position)) row.branch_position = "0";
                        if (string.IsNullOrEmpty(row.branch_id)) row.branch_id = branch_id;
                        if (row.HasStockQty)
                        {
                            s = $"update stock set sell_pend_qty=sell_pend_qty{changeQty} where company_id={company_id} and branch_id={row.branch_id} and item_id={row.item_id} and batch_id = {row.batch_id} and branch_position = {row.branch_position};";
                        }
                        else
                        {
                            s = $"insert into stock(company_id,branch_id,item_id,stock_qty,sell_pend_qty,batch_id,branch_position) values ({company_id},{row.branch_id},{row.item_id},0,{qty},{row.batch_id},{row.branch_position}) on conflict (company_id,branch_id,item_id,batch_id,branch_position) do update set sell_pend_qty=stock.sell_pend_qty{changeQty};";
                        }
                        sql += s;
                        row.NewSellPendQty = row.SellPendQty + qty;
                    }
                }
            }
            return sql;

        }


        public dynamic getOpInfo()
        {
            return "";
        }
        public async override Task<string> RedAndChange<TSheet>(CMySbCommand cmd,  bool bAutoCommit = true)
        {
            string err = "";
            cmd.ActiveDatabase = "";
            CMySbTransaction tran = null;
            if(bAutoCommit) tran = cmd.Connection.BeginTransaction();
            Stack<dynamic> stack = new Stack<dynamic>();
            var oldSheetId = old_sheet_id;
            if (err == "")
                err = await base.RedAndChange<SheetPlaceholderOrder>(cmd, false);
            if (bAutoCommit)
            {
                if (err == "") tran.Commit();
                else tran.Rollback();
            }
            return err;
        }
        protected override async Task<string> CheckForRed(CMySbCommand cmd)
        {
            if (order_status == "zc")
            {
                return "该订单已装车, 请撤消装车后再红冲";
            }
            return "";
        }
		protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            var msg = await base.CheckSaveSheetValid(cmd); 
            if (msg != "") return msg;
            msg =await CheckBatch(cmd);
            if (msg != "") return msg;

            return "OK";
        }
        //这里onSheetIDGot只是覆盖SheetMM中的，以便于什么都不做，否则会产生往来账记录
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            string sql = "";
            //CInfoForApprove info = (CInfoForApprove)info1;
            foreach (SheetRowPlaceholderOrder row in MergedSheetRowByBatchAndItem)
            {
                if (string.IsNullOrEmpty(row.branch_id)) row.branch_id = branch_id;
                string s = $@"insert into stock_change_log 
        (company_id, approve_time,  branch_id,    item_id,    sheet_id,  pre_sell_pend_qty ,new_sell_pend_qty,batch_id,branch_position) 
values ({company_id},'{approve_time}',{row.branch_id},{row.item_id},{sheet_id},{row.SellPendQty},{row.NewSellPendQty},{row.batch_id},{row.branch_position});";
                sql += s;
            }
            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
        }
        public string GetSqlForArrears_Order(CInfoForApproveBase info, decimal leftAmount, string sellerId)
        {
            string sql = "";
            //  decimal arrearsBal = 0;
            decimal changeBal = leftAmount * money_inout_flag;
            //if (info.ArrearsBalance != "") arrearsBal = CPubVars.ToDecimal(info.ArrearsBalance);
            //   arrearsBal += changeBal;
            string custID = info.ClientIdForAcct;

            sql += $"insert into arrears_balance (company_id,supcust_id,pend_amount) values ({company_id},{custID},{changeBal}) on conflict(company_id,supcust_id) do update set pend_amount = arrears_balance.pend_amount + ({changeBal});";

            if (info.SellerHasMaxArrears && leftAmount != 0)
            {
                sql += @$"insert into arrears_balance_auxiliary(company_id,auxiliary_type,auxiliary_id,auxiliary_pend_amount) 
                                                        values ({company_id},'seller',     {sellerId},  {changeBal})
             on conflict(company_id,auxiliary_type,auxiliary_id) do update set auxiliary_pend_amount= arrears_balance_auxiliary.auxiliary_pend_amount + ({changeBal});";
            }
            return sql;

        }

        protected List<SheetRowPlaceholderOrder> MergeDispSheetRows(List<SheetRowPlaceholderOrder> rows)
        {
            Dictionary<string, SheetRowPlaceholderOrder> rowsDict = new Dictionary<string, SheetRowPlaceholderOrder>();
            foreach (SheetRowPlaceholderOrder sheetRow in SheetRows)
            {
                if (sheetRow.disp_flow_id != null && sheetRow.disp_flow_id == "") continue;
                string skey = sheetRow.disp_flow_id + "_" + sheetRow.disp_month_id;
                SheetRowPlaceholderOrder curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = new SheetRowPlaceholderOrder();
                    curRow.item_id = sheetRow.item_id;
                    curRow.item_name = sheetRow.item_name;
                    curRow.unit_no = sheetRow.unit_no;
                    curRow.quantity = sheetRow.quantity;
                    curRow.inout_flag = sheetRow.inout_flag;
                    curRow.trade_type = sheetRow.trade_type;
                    curRow.disp_flow_id = sheetRow.disp_flow_id;
                    curRow.disp_month_id = sheetRow.disp_month_id;
                    curRow.disp_sheet_id = sheetRow.disp_sheet_id;
                    curRow.disp_month_left = sheetRow.disp_month_left;
                    rowsDict.Add(skey, curRow);
                }
                else curRow.quantity += sheetRow.quantity;

            }
            List<SheetRowPlaceholderOrder> newList = new List<SheetRowPlaceholderOrder>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }
        public SheetSale ToSaleSheet(string operKey)
        {
            SheetSale saleSheet = JsonConvert.DeserializeObject<SheetSale>(JsonConvert.SerializeObject(this));

            if (this.move_stock.ToLower() == "true")
            {
                if (!string.IsNullOrWhiteSpace(this.van_id))
                {
                    saleSheet.branch_id = this.van_id;
                    saleSheet.branch_name = this.van_name;
                    Dictionary<string, SheetRowSale> saleRows = new Dictionary<string, SheetRowSale>();
                    foreach (SheetRowSale row in saleSheet.SheetRows)
                    {
                        SheetRowSale newRow = JsonConvert.DeserializeObject<SheetRowSale>(JsonConvert.SerializeObject(row));
                        newRow.branch_id = "";
                        newRow.branch_name = "";
                        newRow.branch_position = "0";
                        newRow.branch_position_name = "";
                        newRow.batch_id = string.IsNullOrEmpty(newRow.batch_id) ? "0" : newRow.batch_id;
                    }
                }

            }
            saleSheet.sheet_type = SHEET_TYPE.SHEET_SALE;
            saleSheet.order_sheet_id = this.sale_order_sheet_id;
            saleSheet.order_sheet_no = this.sale_order_sheet_no;
            saleSheet.OperID = OperID;
            saleSheet.sheet_id = "";
            saleSheet.sheet_no = "";
            saleSheet.approver_id = "";
            saleSheet.approver_name = "";
            saleSheet.approve_time = "";
            saleSheet.approve_brief = "";
            saleSheet.make_time = "";
            saleSheet.maker_id = "";
            saleSheet.maker_name = "";
            saleSheet.happen_time = "";
            saleSheet.license_no = license_no;
            saleSheet.order_source = this.order_source;
            saleSheet.OperKey = operKey;
            saleSheet.send_van_name = this.van_name;
            saleSheet.send_van_id = this.van_id;
            saleSheet.pay_bill_id = this.pay_bill_id;
            saleSheet.payb_status = this.payb_status;
            saleSheet.payb_trade_no = this.payb_trade_no;
            saleSheet.Init();
            return saleSheet;
        }
        
       
    }
}


