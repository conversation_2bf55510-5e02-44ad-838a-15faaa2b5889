﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.Mall
{
    public class MallNoticeViewModel : PageQueryModel
    {
        public bool ForSelect = false;
        public MallNoticeViewModel(CMySbCommand cmd) : base(Services.MenuId.mallNotice)
        {
            this.cmd = cmd;
            this.PageTitle = "商城公告";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"remark",new DataItem(){FldArea="divHead",Title="描述说明", CompareOperator="like",PlaceHolder="请输入说明关键词",QueryOnChange=true}},
                {"creator",new DataItem(){Title="创建人",FldArea="divHead",LabelFld="creator_name",ButtonUsage="list",CompareOperator="=",SqlFld="mn.creator",
                    SqlForOptions=CommonTool.selectSellers}},
                {"updater",new DataItem(){Title="更新人",FldArea="divHead",LabelFld="updater_name",ButtonUsage="list",CompareOperator="=",SqlFld="mn.updater",
                    SqlForOptions=CommonTool.selectSellers}},
                {"notice_type",new DataItem(){Title="公告类型",FldArea="divHead",Width="5%", LabelFld="notice_type_name",ButtonUsage="list",Source = "[{v:'banner',l:'横幅'},{v:'popupImg',l:'弹窗图片'},{v:'',l:'所有'}]",CompareOperator="=" }},
                {"status",new DataItem(){FldArea="divHead",Title="公告状态",LabelFld = "status_name",ButtonUsage = "list",CompareOperator="=",Value="normal",Label="正常",
                         Source = @"[{v:'normal',l:'正常',condition:""(mn.status = 1 or mn.status is null)""},
                               {v:'stop',l:'停用',condition:""mn.status = 0 ""},
                               {v:'all',l:'所有',condition:""true""}]"
                }},
            };


            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     IdColumn="notice_id",
                     HasCheck=false,
                     Sortable=true,
                     PageByOverAgg=false,
               //      AllowMultiColumnsSort=true,
                    //  ColumnsHeight=15,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"notice_id", new DataItem(){Title="业务员", Width="30",Hidden=true}},
                       {"remark",new DataItem(){Title="公告说明",Width="300",Linkable=true}},
                       {"notice_type_name",new DataItem(){Title="类型",Width="50",
                           SqlFld="case when notice_type='banner' then '横幅' when notice_type = 'popupImg' then '弹窗图片' else ''end" }},
                       {"show_after_read_type",new DataItem(){Title="阅后重复提醒",Width="100",SqlFld="case when show_after_read = false then'否' else '是' end"}},
                       {"order_index", new DataItem(){Title="优先级顺序", Width="100"}},
                       {"show_start_time", new DataItem(){Title="公告展示开始时间", Width="150"}},
                       {"show_end_time", new DataItem(){Title="公告展示截至时间", Width="150"}},
                       {"status", new DataItem(){Title="状态", Width="30",SqlFld="case when status = 0 then '停用' else '正常' end"}},
                       {"creator_name", new DataItem(){Title="创建人", Width="50"}},
                       {"create_time", new DataItem(){Title="创建时间", Width="80"}},
                       {"updater_name", new DataItem(){Title="更新人", Width="50"}},
                       {"update_time", new DataItem(){Title="更新时间", Width="80"}},

                     },
                     QueryFromSQL=@" from mall_notice mn
	LEFT JOIN ( SELECT oper_id, oper_name AS creator_name, depart_path FROM info_operator WHERE company_id =~COMPANY_ID ) co ON co.oper_id = mn.creator
    LEFT JOIN ( SELECT oper_id, oper_name AS updater_name, depart_path FROM info_operator WHERE company_id =~COMPANY_ID ) uo ON uo.oper_id = mn.updater
    where
	mn.company_id =~COMPANY_ID
    ",
                     QueryOrderSQL="order by notice_id"
                  }
                }
            };
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }

    [Route("api/[controller]/[action]")]
    public class MallNoticeViewController : BaseController
    {
        public MallNoticeViewController(CMySbCommand cmd)
        {
            Database = "";
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string sheet_type, string availValues)
        {
            MallNoticeViewModel model = new MallNoticeViewModel(cmd);

            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            MallNoticeViewModel model = new MallNoticeViewModel(cmd);
            // 这个方法里也可以通过request获取一些值，然后对model进行设置
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }
    }
}
