@page
@model ArtisanManage.Pages.BaseInfo.ClientEditModel
@{
    Layout = null;
    ///jkkll
} 
<!DOCTYPE html>

<html>
    <!--
        <script  type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=@Html.Raw(Model.BaiduKey)"></script>

    -->

<head>
    <style>

        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
            background-color: #fff;
        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 5px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #eeeeee;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }
    </style>
    <meta name="viewport" content="width=device-width" />
    <title>ClientEdit</title>

    <partial name="_FormPageHead" model="Model.PartialViewModel" />

    
    <script type="text/javascript">
        g_loadBaiduMap = false
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ObjOperRights = @Html.Raw(Model.ObjOperRights);
        @Html.Raw(Model.m_saveCloseScript)
        var doorPhoto = ""
        var otherPhotoUrls = []
        var aosd = null
        var agsd = null
        var aoed = null
        var aged = null
        function loadBaiduScript(cb) {
                if (g_loadBaiduMap) {
                    cb()
                    return
                }
                var script = document.createElement('script');
                script.src = 'https://api.map.baidu.com/api?v=2.0&ak=@Html.Raw(Model.BaiduKey)&callback=commonMapInit';
                script.type = 'text/javascript';
                document.head.appendChild(script);
                console.log(document)
                window.commonMapInit = function() {
                    g_loadBaiduMap = true
                    cb()
                }
        }
     
        $(document).ready(function () {
             @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            var width = document.getElementById('divTab').offsetWidth
            var height = document.getElementById('divTab').offsetHeight
            var  obsPrefix = "@Html.Raw(HuaWeiObsController.HuaWeiObs.BucketLinkHref)";
            $('#divTab').jqxTabs({ width: width, height:height, position: 'top'});
            var originDoorUrl= $("#sup_door_photo").val()
            $("#door-photo").attr('src',obsPrefix+"/uploads"+originDoorUrl);
            $("#door-photo").attr('height',"80");
            $("#sup_door_photo").val(obsPrefix+"/uploads"+originDoorUrl)
            var otherPhotoOriginUrls = $("#sup_other_photo").val().split(",");
            var otherPhotoUrls = otherPhotoOriginUrls.map(url => {
                return obsPrefix+"/uploads"+url;
            })
            otherPhotoUrls.forEach((photoUrl,index) => {
                $("#other-photo").append(`<img id='other-photo-img-${index}' height='80' style='margin-left:5px;margin-right:5px;' src='${photoUrl}'/>`)
             })
            $("#sup_other_photo").val(otherPhotoUrls.join(","))
            window.onresize();
            $('#sup_name input').on('input', function () {
                $('#py_str input').val(this.value.ToPinYinCode());
            });
            $("#door-photo").on("click", () => {
                $('#picturePreview').jqxWindow('open');//popclient和popitems不是一个东西
                $("#picturePreview").jqxWindow('setContent', `<img src="${obsPrefix + "/uploads" + originDoorUrl}" height="600px" />`);
                $("#picturePreview").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            })
            otherPhotoImgDoms = $("img[id^='other-photo-img-']")
            $.each(otherPhotoImgDoms, (index, dom) => {
                $(`#other-photo-img-${index}`).on("click", () => {
                    var imgUrl = $(dom).attr("src")
                    $('#picturePreview').jqxWindow('open');//popclient和popitems不是一个东西
                    $("#picturePreview").jqxWindow('setContent', `<img src="${imgUrl}" height="600px" />`);
                    $("#picturePreview").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                })
            })
            $("#upload-image").on("change", function(e) {
                var file = $("#upload-image")[0].files[0]
                var fr=new FileReader();
               fr.onload=function () {
                    $("#door-photo").attr('src',this.result);
                    $("#door-photo").attr('height',"80");
                   compressImage(this.result, (compressBase64Url) => {
                     doorPhoto = compressBase64Url
                     $("#sup_door_photo").val(doorPhoto)
                   })
                };
               fr.readAsDataURL(file);
            })
            $("#upload-other-image").on("change", function(e) {
               var file = $("#upload-other-image")[0].files[0]
               var fr=new FileReader();
               fr.onload=function () {
                   compressImage(this.result, (compressBase64Url) => {
                      otherPhotoUrls.push( compressBase64Url)
                      $("#sup_other_photo").val(otherPhotoUrls.join("|"))
                      $("#other-photo").append(`<img height='80' src='${compressBase64Url}'/>`)
                   })
                };
               fr.readAsDataURL(file);
            })
            // 系统通过坐标点猜测地址位置并填充（伪需求2024/1/8注释）
            /** 
            var longitude = $("#addr_lng").attr("value")//获取经纬度
            var latitude = $("#addr_lat").attr("value")
            var supAddr = $("#sup_addr input").attr("data-value")

            if (!supAddr && longitude && latitude) {
            loadBaiduScript(() => {
                var geoc = new BMap.Geocoder()
                console.log(geoc)
                geoc.getLocation(new BMap.Point(longitude,latitude) , result => {
                var addr = ''
                if (result.surroundingPois.length != 0) {
                    addr = result.address+ result.surroundingPois[0].title + "附近"
                }
                $("#sup_addr input").val(addr)
                $("#sup_addr input").attr("data-value",addr)
                $("#sup_addr input").attr("data-label",addr)
                $("#sup_addr input").css("color","#999")
            })
            })
        
            }
            */
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            $('#acct_cust_id').jqxInput({
                onButtonClick: function (event) {
                    $('#popClient').jqxWindow('open');//popclient和popitems不是一个东西
                    $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                }
            });
                $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            function refreshAcctCustID() {
                var value = $('#cust_type').val()
                if (value.value == 'shop') $('#div_acct_cust_id').show()
                else {
                    $('#div_acct_cust_id').hide()
                    $('#acct_cust_id').jqxInput('val','')
                }
            }
            $('#cust_type').on('optionSelected', function (a, b) {
                refreshAcctCustID() 
            })
            refreshAcctCustID()
            var supcust_id = $("#supcust_id").val()
            if (supcust_id) {
                 
                if (ObjOperRights.info.infoClient && ObjOperRights.info.infoClient.edit==false) {
                    $("#btnSave").attr("disabled", "true") 
                }
                /*  不合理代码，用户不知道为啥禁用了，造成售后
                if (approveStatus) {
                    $("#btnSave").attr("disabled", "true")
                    return
                }else{
                    $("#btnSave").removeAttr("disabled")
                }*/
            }
            // setDateStatus()
            // if(acct_cust_id)

            aosd = $("#arrears_order_start_date").val()
            agsd = $("#arrears_get_start_date").val()
            aoed = $("#arrears_order_end_date").val()
            aged = $("#arrears_get_end_date").val()

            $("#arrears_order_start_date").on("change", function (e) {
                debugger
                t_aosd = e.args.item
                if (!t_aosd && aoed) {
                    bw.toast('请同时设置对账起止日期', 2000)
                    // $('#arrears_order_start_date').jqxInput('val', { value: aosd.value, label: aosd.label });
                } else if (parseInt(t_aosd.value) > parseInt(aoed.value)){
                    bw.toast('对账起始日期不可大于截止日期', 2000)
                    $('#arrears_order_start_date').jqxInput('val', { value: aosd.value, label: aosd.label });
                }
                updateCurrentDateSet()
            })
            $("#arrears_order_end_date").on("change", function (e) {
                debugger
                t_aoed = e.args.item
                if (aosd && !t_aoed) {
                    bw.toast('请同时设置对账起止日期', 2000)
                    // $('#arrears_order_end_date').jqxInput('val', { value: aoed.value, label: aoed.label });
                } else if (parseInt(aosd.value) > parseInt(t_aoed.value)){
                    bw.toast('对账截至日期不可小于起始日期', 2000)
                    $('#arrears_order_end_date').jqxInput('val', { value: aoed.value, label: aoed.label });
                }
                updateCurrentDateSet()
            })
            $("#arrears_get_start_date").on("change", function (e) {
                debugger
                t_agsd = e.args.item
                if (!t_agsd && aged) {
                    bw.toast('请同时设置收款起止日期', 2000)
                    // $('#arrears_get_start_date').jqxInput('val', { value: agsd.value, label: agsd.label });
                } else if (parseInt(t_agsd.value) > parseInt(aged.value)){
                    bw.toast('收款起始日期不可大于截止日期', 2000)
                    $('#arrears_get_start_date').jqxInput('val', { value: agsd.value, label: agsd.label });
                }
                updateCurrentDateSet()
            })
            $("#arrears_get_end_date").on("change", function (e) {
                debugger
                t_aged = e.args.item
                if (agsd && !t_aged) {
                    bw.toast('请同时设置收款起止日期', 2000)
                    // $('#arrears_get_end_date').jqxInput('val', { value: aged.value, label: aged.label });
                } else if (parseInt(agsd.value) > parseInt(t_aged.value)){
                    bw.toast('收款截至日期不可小于起始日期', 2000)
                    $('#arrears_get_end_date').jqxInput('val', { value: aged.value, label: aged.label });
                }
                updateCurrentDateSet()
            })

            var acct_cust_id = $("#acct_cust_id").val().value
            getDateSet()

            $("#acct_cust_id").on("change", function (e) { 
                getDateSet()
            })
            $("#cust_type").on("change", function (e){
                let cust_type = $("#cust_type").val().value
                console.log(cust_type)
                if (cust_type === 'client'|| !cust_type) {
                    setDateStatus();
                }
            })
        });
        function updateCurrentDateSet(){
            aosd = $("#arrears_order_start_date").val()
            agsd = $("#arrears_get_start_date").val()
            aoed = $("#arrears_order_end_date").val()
            aged = $("#arrears_get_end_date").val()
        }
        function getDateSet() {
            // console.log($("#acct_cust_id").val())
            debugger
            let acct_id = $("#acct_cust_id").val().value
            if (acct_id) {
                debugger
                acct_cust_id = acct_id
                $.ajax({
                    url: '/api/ClientEdit/GetDataSet',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ operKey: g_operKey, acct_cust_id: acct_cust_id }),

                    success: function (data) {
                        if (data.result === 'OK') {
                            setDateStatus(data.ret)
                        }
                        else {
                            // empty
                            setDateStatus();
                        }

                    },
                    error: function (xhr) {
                        console.log("返回响应信息：" + xhr.responseText);
                    }
                })

            }
            setDateStatus();
        }
        function setDateStatus(dateInfo) {
            debugger
            if(dateInfo){
                let sv = ''; let sl = ''; let ev = ''; let el = '';
                if (dateInfo.arrears_order_start_date) {
                    sv = dateInfo.arrears_order_start_date
                    sl = '每月' + parseInt(sv) + '号';

                    ev = dateInfo.arrears_order_end_date
                    el = '每月' + parseInt(ev) + '号';
                   
                }
                $('#arrears_order_start_date').jqxInput('val', { value: sv, label: sl });
                $('#arrears_order_end_date').jqxInput('val', { value: ev, label: el });

                sv = ''; sl = ''; ev = ''; el = '';
                if (dateInfo.arrears_get_start_date) {
                    sv = dateInfo.arrears_get_start_date
                    sl = '每月' + parseInt(sv) + '号';

                    ev = dateInfo.arrears_get_end_date
                    el = '每月' + parseInt(ev) + '号';
                    
                }
                $('#arrears_get_start_date').jqxInput('val', { value: sv, label: sl });
                $('#arrears_get_end_date').jqxInput('val', { value: ev, label: el });
                
            }
            var acct_cust_id = $("#acct_cust_id").val()
            var arrears_order_start_date = $("#arrears_order_start_date").val()
            var arrears_get_start_date = $("#arrears_get_start_date").val()
            var arrears_order_end_date = $("#arrears_order_start_date").val()
            var arrears_get_end_date = $("#arrears_get_start_date").val()

            if (acct_cust_id && arrears_order_start_date && dateInfo) {
                console.log($("#arrears_order_start_date .row-oper").length);
                $("#arrears_order_start_date").jqxInput({ disabled: true });
                $("#arrears_order_start_date .row-oper").css("visibility", "hidden");
                $("#arrears_order_end_date").jqxInput({ disabled: true });
                $("#arrears_order_end_date .row-oper").css("visibility", "hidden");
            }
            else{
                $("#arrears_order_start_date").jqxInput({ disabled: false });
                $("#arrears_order_start_date .row-oper").css("visibility", "visible");
                $("#arrears_order_end_date").jqxInput({ disabled: false });
                $("#arrears_order_end_date .row-oper").css("visibility", "visible");
            }
            if (acct_cust_id && arrears_get_start_date && dateInfo) {
                $("#arrears_get_start_date").jqxInput({ disabled: true });
                $("#arrears_get_start_date .row-oper").css("visibility", "hidden");
                $("#arrears_get_end_date").jqxInput({ disabled: true });
                $("#arrears_get_end_date .row-oper").css("visibility", "hidden");
            }
            else {
                $("#arrears_get_start_date").jqxInput({ disabled: false });
                $("#arrears_get_start_date .row-oper").css("visibility", "visible");
                $("#arrears_get_end_date").jqxInput({ disabled: false });
                $("#arrears_get_end_date .row-oper").css("visibility", "visible");
            }
        }
        function compressImage(imageUrl, cb) {
            // 创建 Image 对象
            var img = new Image();
            img.src = imageUrl;
            img.onload = function() {
                var _this = this;
                // 获取 canvas 元素
                var canvas = document.createElement('canvas');
                canvas.id = 'canvas';
                // 绘制图像到 canvas
                canvas.width = img.width;
                canvas.height = img.height;
                var ctx = canvas.getContext("2d");
                ctx.drawImage(_this, 0, 0, img.width, img.height);
                // 使用 toDataURL 方法压缩图像
                var dataUrl = canvas.toDataURL("image/jpeg", 0.5);
                // 使用新的 Data URL 更新图像
                cb(dataUrl)
            }
        }
        window.onresize = function () {
           // var windowWidth = window.innerWidth;
          //  $("#gridUnit").jqxGrid(
          //      {
          //          width: windowWidth - 20
           //     });
        };
        window.addEventListener('message', function (rs) {//监听事件（无论是子元素还是父元素的事件），父元素是clientsview
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#acct_cust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                }
                $('#popClient').jqxWindow('close');
            }
        });

       

    </script>
    <style>
        html,body{
            height:unset;
        }
        #divHead{
            padding-right:0px;
        }
    </style>
</head>
<body style="display: block; flex-direction:column;">
    
  <div id="divTab" style="width:calc(100% - 0px);height:calc(100vh - 70px);">
      <ul>
        <li>基本</li>
        <li>收货地址</li>
        <li>图片</li>
        <li>对账设置</li>
      </ul>
      <div style="display:flex;flex-direction:column;">
            <div id="divHead" class="headtail" style="width:550px;padding-top:10px;">
            </div>
      </div>
      <div style="display:flex;flex-direction:column;">
        <div id="divAddressTop" style="display:flex;height:30px;align-items:center;">

        </div>
        <div id="gridAddress" style="width: calc(100% - 50px); margin-top: 10px; margin-left: 10px; margin-right: 0px; height: 200px;">
        
        </div>
      </div>
      <div style="display:flex;flex-direction:column;">
            <div style="margin-bottom:10px;font-size:18px;font-weight:700;">门头照</div>
            <input style="margin-bottom:10px;" id="upload-image" type="file" name="file" accept="image/png,image/bmp,image/jpeg"/>
            <div style="margin-bottom:10px;">
                <img id="door-photo"/>
            </div>
            <div style="margin-bottom:10px;font-size:18px;font-weight:700;">其他</div>
            <input style="margin-bottom:10px;" id="upload-other-image" type="file" name="file" accept="image/png,image/bmp,image/jpeg"/>
            <div style="margin-bottom:10px;" id="other-photo"></div>
      </div>

       <div style="display:flex;flex-direction:column;">
     
            <div id="divTestPage" class="headtail" style="width:500px;padding-top:10px;margin-left:30px">
            </div>
     
        </div>
   </div>

 
    <div style="text-align: center; margin-top: 20px; flex-grow: 0;">
        <button id="btnSave" onclick="btnSave_checkData();" style="margin-right:50px;">保存</button>
        <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
    <div id="picturePreview" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:scroll;"> </div>
    </div>

    <div id="popClient" style="display:none"> 
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
</body>

</html>
<script type="text/javascript">
    function btnSave_checkData(bCopy, callback) {
        debugger
        var formFlds = getFormData();
        if (formFlds.errMsg) {
            return;
        }
        try {
            if (typeof (eval(checkDataValid)) == 'function') {
                var bOK = checkDataValid(formFlds);
                if (!bOK) return;
            }
        } catch (e) { }

        if (window.dealFormData && typeof (eval(window.dealFormData)) == 'function') {
            dealFormData(formFlds);
        }
        $.ajax({
            url: '../api/ClientEdit/CheckData',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formFlds),
            success: function (data) {
                if (data.result == 'OK') {

                    btnSave_Clicked(bCopy, callback)
                }
                else {
                    jConfirm(data.msg+",是否继续?", function () {
                        btnSave_Clicked(bCopy, callback)
                    }, "");
                    //bw.toast(data.msg, 5000);
                }
            },
            error: function (response, ajaxOptions, thrownError) {
                bw.toast('error' + response);
            }
        });
    }
</script>