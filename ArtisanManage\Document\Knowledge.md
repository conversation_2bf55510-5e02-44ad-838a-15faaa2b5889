## 正则表达式，从
SELECT REGEXP_MATCHES(
    'company_id= ''123'' ',
    '(?<=\s*company_id\s*=\s*\''*\s*)[0-9]+(?=\s*\''*)') as rates

更新语句
update log_sql set company_id= (select ((REGEXP_MATCHES(sql, '(?<=\s*company_id\s*=\s*\''*\s*)[0-9]+(?=\s*\''*)','')::text[])[1]) ::integer) where flow_id=1043540

##  查询速度优化
正常sql：

	SELECT * FROM sheet_sale_main sm
	
	LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id = 930 

	left join realtime_supcust r on r.company_id=930 and s.supcust_id=r.supcust_id  

	LEFT JOIN ( SELECT supcust_id AS supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = 930) A ON s.acct_cust_id = A.supcust_id  

	where sm.company_id = 930 and sm.senders_id like '%11049%' and sm.happen_time >= '2023-06-29 ' and sm.happen_time <= '2023-06-29 23:59:59' 

	order by sm.approve_time desc limit 20 offset 0;

查询时间为0.6s

优化后sql:

	SELECT * FROM sheet_sale_main sm
	
	LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id = 930 

	left join realtime_supcust r on r.company_id=930 and s.supcust_id=r.supcust_id  and s.company_id=930

	LEFT JOIN ( SELECT supcust_id AS supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = 930) A ON s.acct_cust_id = A.supcust_id  and s.company_id=930

	where sm.company_id = 930 and sm.senders_id like '%11049%' and sm.happen_time >= '2023-06-29 ' and sm.happen_time <= '2023-06-29 23:59:59' 

	order by sm.approve_time desc limit 20 offset 0;

查询时间为0.032s

优化方式：

	realtime_supcust 表是关联info_supcust表查询的，虽然info_supcust表指定了company_id，但是在left join realtime_supcust的时候，realtime_supcust 关联info_supcust表的时候还是对表info_supcust表进行了全表检索，导致查询慢（仅为个人基于结果的合理推测）,所以在left join realtime_supcust的后面加上s.company_id = 930 查询速度得到提升。下面的left join也是同理

其他优化方式：

		1、sheet_sale_main表是按happen_time分区的，把order by sm.approve_time 换成 order by sm.happen_time查询速度也会提成
		2、sql的order by sm.approve_time desc 和 limit 20 offset 0 任意去掉一个，查询速度也会提升（无法解释）
		3、去除where里的and sm.senders_id like '%11049%' 查询速度也会提升