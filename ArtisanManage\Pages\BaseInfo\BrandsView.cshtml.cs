﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class BrandsViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
        /// <summary>
        /// 43165qgreqfgreg
        /// </summary>
 
        public BrandsViewModel(CMySbCommand cmd):base(Services.MenuId.infoBrand)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                 {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称",UseJQWidgets=false, SqlFld="brand_name",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
                  {"status",new DataItem(){Title = "状态",LabelFld = "cls_status_name", LabelInDB = false, Value = "normal", Label = "正常",Width="50", ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "normal",

                     Source = @"[{v:'normal',l:'正常',condition:""(brand_status = '1' or brand_status is null)""},
                               {v:'stop',l:'停用',condition:""brand_status = '0' ""},
                               {v:'all',l:'所有',condition:""true""}]"

                 }},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     IdColumn="i",TableName="info_item_brand",
                     ShowContextMenu=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"i",new DataItem(){Title="编号", Width="80",SqlFld = "brand_id",Hidden=true}},
                       {"brand_name",new DataItem(){Title="品牌名称", Width="180",Linkable=true, JSCellRender="brandNameRenderer"}},                       
                       {"remark",new DataItem(){Title="备注",Width="80"}},
                     },
                     QueryFromSQL="from info_item_brand" ,QueryOrderSQL="order by brand_order_index,brand_id"
                  }
                } 
            }; 
        }
        public async Task OnGet(string forSelect)
        {  
            ForSelect = forSelect == "1";
            if (ForSelect)
            {
                Grids["gridItems"].HasCheck = true;
                Grids["gridItems"].KeepCheckForQueries = true;
            }
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }

        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            cmd.CommandText = $"select company_id from info_item_class where brand_id in ({rowIDs}) and company_id={company_id} limit 1";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该品牌已关联类别档案,无法删除";

            cmd.CommandText = $"select company_id from info_item_prop where item_id in ({rowIDs}) and company_id={company_id} limit 1"; 
            ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该品牌已关联商品档案,无法删除";
 
            return "";
        }
    }




    [Route("api/[controller]/[action]")]
    public class BrandsViewController : QueryController
    { 
        public BrandsViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BrandsViewModel model = new BrandsViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {// string gridID,int startRow,int endRow,bool bNewQuery){
            BrandsViewModel model = new BrandsViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            BrandsViewModel model = new BrandsViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd,"info_item_brand");// gridID, startRow, endRow, bNewQuery);
            return records;
        }
    }
}
