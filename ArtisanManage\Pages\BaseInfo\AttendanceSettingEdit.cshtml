﻿@page
@model ArtisanManage.Pages.BaseInfo.AttendanceSettingEditModel
@{
    Layout = null;
}
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title></title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />

    <script src="//api.map.baidu.com/api?type=webgl&v=1.0&ak=@Html.Raw(Model.BaiduKey)"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var g_getPoiFlag
        @Html.Raw(Model.m_saveCloseScript)
        var g_map = null;
        var g_roundCircle = null;
        function initMap() {
            var map = new BMapGL.Map("baidumap");    // 创建Map实例
	        map.centerAndZoom(new BMapGL.Point(116.280190, 40.049191), 19);  // 初始化地图,设置中心点坐标和地图级别
	        map.enableScrollWheelZoom(true);     //开启鼠标滚轮缩放
            g_map = map;
            var fix_position = $("#fix_position").val()
            console.log(fix_position)
           // if (fix_position==='False'){
           //     $("#div_check_addr").css("display","none")
           //     $("#div_check_distance").css("display","none")
           // }
            var addr_lng =  $("#check_longitude > input").attr("data-value")
            var addr_lat =  $("#check_latitude > input").attr("data-value")
            console.log(addr_lng,addr_lat)
            if (addr_lng  &&  addr_lat) {
                renderPoiOnMap( addr_lng,addr_lat)
            }
            const check_addr = $("#check_addr  > input").attr("data-value")
            if (!check_addr) { 
               const companyCity = localStorage.getItem("companyCity")
               $("#check_addr").val(companyCity)
                g_getPoiFlag = setInterval(() => {
                    if (g_getPoiFlag) {
                        return
                    }
                    getPois(companyCity, res => {
                        const poi = res.result[0]
                        renderPoiOnMap(poi.location.lng, poi.location.lat)
                        $("#check_addr").val('')
                    })
                },200)

            }
        }
$(document).ready(function() {
    @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
        onCheckAddrChange()
    onCheckDistanceChange()
    onFixPositionChange()
    initMap()

    //$("#check_start_time>input").focus(() => {
    //    window.timepicker_type='start_time'
    //    renderPicker()
    //    $("#timepicker_start").css("display",'block')
    //   $("#timepicker_end").css("display",'none')
    //})
    //$("#check_end_time>input").focus(() => {
    //    window.timepicker_type='end_time'
    //    renderPicker()
    //    $("#timepicker_start").css("display",'none')

    //    $("#timepicker_end").css("display",'block')
    //})
    //})
    })

        /////////////////////时分picker////////////
        function renderPicker() {
             console.log("render",window.timepicker_type)
            $(".hour-col").html(getHourPickerDOM())
            $(".minute-col").html(getMinutesPickerDOM())
            if(window.timepicker_type==='start_time'){
                $(".hour-col").css("top","80px")
                $(".hour-col").css("left","80px")
                $(".minute-col").css("top","80px")
                $(".minute-col").css("left","140px")
            }
            if(window.timepicker_type==='end_time'){
                $(".hour-col").css("top","80px")
                $(".hour-col").css("left","200px")
                $(".minute-col").css("top","80px")
                $(".minute-col").css("left","260px")
            }
        }
        function getHourPickerDOM() {
            var divTemplate=""
            for (i = 0; i <= 23; i++) {
                divTemplate+=`<div style='cursor:pointer;' class='hour-item'onclick='chooseHour(${i})'>${i}</div>`
            }
        
            return  divTemplate
        }
        function getMinutesPickerDOM() {
            var divTemplate=""
            for (i = 0; i <= 59; i++) {
                divTemplate+=`<div style='cursor:pointer;' class='minutes-item' onclick='chooseMinute(${i})'>${i}</div>`
            }

            return  divTemplate
        }
        function chooseHour(e) {
         if(window.timepicker_type==='start_time'){
           let [hour,minute]=$(check_start_time).val().split(":")
           hour=e
           $("#check_start_time").val([hour,minute].join(":"))
         }
         if(window.timepicker_type==='end_time'){
           let [hour,minute]=$(check_end_time).val().split(":")
           hour=e
           $("#check_end_time").val([hour,minute].join(":"))
           }
        }
        function chooseMinute(e) { 
            if(window.timepicker_type==='start_time'){
           let [hour,minute]=$(check_start_time).val().split(":")
           minute=e
           $("#check_start_time").val([hour,minute].join(":"))
            }
           if(window.timepicker_type==='end_time'){
           let [hour,minute]=$(check_end_time).val().split(":")
           minute=e
           $("#check_end_time").val([hour,minute].join(":"))
           }
        }
        ////////////////////////////////////////////
        function onFixPositionChange(){
            $("#div_fix_position").click((e)=>{
                console.log("onFixPositionChange")
                var fixPosition=$("#fix_position").val()
              //  if (!fixPosition){
                   // $("#div_check_addr").css("display","none")
                   // $("#div_check_distance").css("display","none")
              //  }else{
                  //  $("#div_check_addr").css("display","block")
                  //  $("#div_check_distance").css("display","block")
              //  }
            })     
       }
        function onCheckDistanceChange() {
                $(check_distance).on("input propertychange", e => {
                const distance = e.target.value
                g_roundCircle.setRadius(Number(distance))
            })
        }
        function onCheckAddrChange() {
            $(check_addr).on("input propertychange", e => {
            const checkAddr= e.target.value
            if (!checkAddr) {
                return
            }
            getPois(checkAddr, res => {
                    const pois = res.result
                    if (pois.length==0) {
                    $("#address_result").css("display","block")
                         return
                    }
                    var curIndex=0
                    const poisources = pois.map(poi => {
                    console.log({ poi })
                    curIndex++
                    if (poi) {
                            return `<div style="padding:10px;cursor:pointer;background:${curIndex % 2 == 0 ? '#fff' : '#eee'}" onclick="attrAddressInput('${poi.province + poi.city + poi.district + poi.name}',${poi.location.lng},${poi.location.lat})">` + poi.province + poi.city + poi.district + poi.name + `</div>`
                     }
                     return ``
                    })
                    $("#address_result").html(poisources.join(""))
                    $("#address_result").css("display","block")
                })
           })
        }
        function getPois(searchKey,cb) {
           $.ajax({
                url: "/api/AttendanceSettingEdit/SearchPoi",
                data: {
                key: searchKey,
                operKey: window.g_operKey
                },
                success: (res) => {
                    cb(res)
           }
           })
        }
        function attrAddressInput(address, addr_lng,addr_lat) {
             $("#check_addr").val(address)
             $("#check_longitude").val(addr_lng)
             $("#check_latitude").val(addr_lat)
             $("#address_result").css("display","none")
             renderPoiOnMap(addr_lng,addr_lat)
     
        }
        function renderAttendanceRound(markerPoint,distance){
               var circle = new BMapGL.Circle(markerPoint,Number(distance),{fillColor:"blue", strokeWeight: 1 ,fillOpacity: 0.3, strokeOpacity: 0.3});
               g_map.addOverlay(circle);
               g_roundCircle = circle
        }
        function renderPoiOnMap( addr_lng,addr_lat) {
                g_map.clearOverlays()
                var markerPoint = new BMapGL.Point(addr_lng, addr_lat)
                var marker = new BMapGL.Marker(markerPoint, {enableDragging: true});
                marker.addEventListener("dragend", ({ latLng}) => {
                    var dragCenterPoint = new BMapGL.Point(latLng.lng, latLng.lat)
                    $("#check_longitude").val(latLng.lng)
                    $("#check_latitude").val(latLng.lat)
                    g_roundCircle.setCenter(dragCenterPoint)	
                    var geoc = new BMapGL.Geocoder(); 
                    geoc.getLocation(dragCenterPoint,function(rs){ 
                        var addComp = rs.addressComponents; 
                        $("#check_addr").val(addComp.province  + addComp.city  + addComp.district  + addComp.street  + addComp.streetNumber); 
                    }); 
                })
               
                g_map.centerAndZoom(markerPoint, 15);  
                g_map.addOverlay(marker);
                const checkDistance=$("#check_distance > input").attr("data-value")
            if (checkDistance > 0) {
                renderAttendanceRound(markerPoint, checkDistance)
            } else {
                renderAttendanceRound(markerPoint, 0.01)
            }
        }
    </script>
</head>
<body>
  
    <div id="divHead" class="headtail" style="width:500px;">
        
    </div>
     <div style='display:none;z-index:100;' class="address_result" id="address_result"></div>
    <div id="baidumap"></div>
    <div style="display:none;" id="timepicker_start">
          <div class="hour-col"></div>
          <div class="minute-col"></div>
    </div>
     <div style="display:none;" id="timepicker_end">
          <div class="hour-col"></div>
          <div class="minute-col"></div>
    </div>
    <div style="z-index:999;position:fixed;bottom:10px;left:180px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;">保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>
<style>
    .address_result{
        position: absolute;
        height: 100px;
        overflow-y: scroll;
        left: 90px;
        top: 150px;
        width:300px;
    }
    #baidumap{
        height:400px;
        margin:10px;
        width:100%;
        z-index:1;
    }
    #timepicker{
    }
    ::-webkit-scrollbar{
    width: 0px;
     }
    .hour-col{
        border-right:1px solid;
        width: 50px;
        background: #fff;
        z-index: 999;
        position: absolute;
        text-align: center;
        width: 60px;
        overflow-x: hidden;
        height: 100px;
    }
    .start_time_picker{
       top: 80px;
       left: 240px;
    }
    .end_time_picker{
       top: 80px;
       left: 120px;
    }
    .minute-col{
        ::-webkit-scrollbar:none;

        background: #fff;
        z-index: 999;
        width: 60px;
        position: absolute;
        top: 80px;
        left: 300px;
        text-align: center;
        height: 100px;
        overflow-x: hidden;

    }
        /* Tabled Portrait */
    @@media screen and (max-width: 768px) {
        #wrapper { width:70%; }
     }

    /* Tabled Portrait */
    @@media screen and (max-width: 768px) {
        #wrapper { width:70%; }
     }
</style>
