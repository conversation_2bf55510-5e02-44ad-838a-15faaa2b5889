﻿@page
@model ArtisanManage.CwPages.Report.FeeOutSummaryModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>FeeOutSummary</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>
    <script src="~/js/jQuery.print.js"></script>
    <script  src="~/js/jszip.min.js"></script>
    <script  src="~/js/FileSaver.js"></script>
    <script  src="~/js/excel-gen.js"></script>
    <script  src="~/js/demo.page.js"></script>
    <style>
        * {
            font-family: "微软雅黑"
        }

        body {

        }
        [v-cloak] {
          display: none;
        }

        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;

        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #dddddd;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }

        #pages {
            display: flex;
            flex-direction: column;
            height: 90vh;
            cursor:pointer;
        }

        .pages_title {
            width: 100%;
            font-weight: 500;
            font-size: 25px;
            text-align: center;
            margin-top: 5px;
            padding-bottom:10px;
        }

        .pages_query {
            display: flex;
            width: 100%;
            margin-left: 100px;
            margin-right:20px;

        }


        .query_item {
            display: flex;
            width: 260px;
            margin-right:20px;
        }
        .query_item>div {
            line-height:35px;
        }
        .query_item input {
            padding-top: 15px;
            padding-right: 0px;
            height: 35px;
            
            border: none;
            border-bottom: 1px #ddd solid;
            border-radius: 0;
            text-align: center;
        }
        .query_item input:active {

            border-bottom: 1px #ddd solid ;

        }
        .el-select__caret.el-input__icon.el-icon-arrow-up{
            position:absolute;
            right:0px;
            top:9px;
        }
        .el-date-editor.el-input, .el-date-editor.el-input__inner {
            width: 210px;
        }
        .item_input {
            width: 100px;
            height: 50%;
            z-index: 0;
            position: relative;
            border-style: none none solid;
            border-bottom: 1px solid #c7c7c7;
            border-radius: 0px;
            margin-left: 10px;


        }


        .pages_content {
            height: 50%;
            padding: 0 20px;
            margin-top:20px;
        }

        .level_1 {
            font-weight: bold
        }
        .level_2 {
            text-indent: 1em;
            text-align:center;
        }
        .level_3 {
            text-indent: 2em;
            text-align: right;

        }
        .level_4 {
            font-weight: bold;
            display:none;
        }
        .link {
           color:blue;
        }

        .pages_content table {
            width: 800px;
            border-width: 0;
            border-collapse: collapse;
            border: 1px solid #ebeef5;
            margin: 0 auto;
        }

        .pages_content table tbody {
            display: block;

            overflow-x: hidden;
            height: 80%;
        }
        @@media(max-height:700px) {
        .pages_content table tbody {
            display: block;

            overflow-x: hidden;

        }
         }
        .pages_content table thead, .pages_content tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .pages_content table thead {
            width: 100%;
        }

        .pages_content table thead th:nth-child(1), .pages_content table thead th:nth-child(5), .pages_content table tbody td:nth-child(1), .pages_content table tbody td:nth-child(5) {
            width: 50%
        }
        .pages_content table thead th:nth-child(2), .pages_content table thead th:nth-child(6), .pages_content table tbody td:nth-child(2), .pages_content table tbody td:nth-child(6) {
            width: 50%;
            text-align: center
        }
        .pages_content table thead th:nth-child(3), .pages_content table thead th:nth-child(7), .pages_content table tbody td:nth-child(3), .pages_content table tbody td:nth-child(7) {
            width: 20%
        }
        .pages_content table thead th:nth-child(4), .pages_content table thead th:nth-child(8), .pages_content table tbody td:nth-child(4), .pages_content table tbody td:nth-child(8) {
            width: 12.5%
        }
        .pages_content table tbody td:nth-child(3), .pages_content table tbody td:nth-child(7), .pages_content table tbody td:nth-child(4), .pages_content table tbody td:nth-child(8) {
            text-align: right;
        }
        @*高度*@
        .pages_content table thead th, .pages_content table tbody td {
            min-height: 40px;
            line-height: 40px;
        }
        @*边框*@
        .pages_content table thead th, .pages_content table tbody td {
            border-bottom: 1px solid #ebeef5;
            border-right: 1px solid #ebeef5;
        }
            .pages_content table thead th:last-child {
                border-right: 0;
            }
        @*背景*@
        .pages_content table tbody tr:nth-child(odd) {
            background: #fafafa;
        }
        .pages_content table tbody tr:hover {
            background-color: #f5f7fa;
        }

        .pages_content table thead th, .pages_content table tbody td {
            padding: 0 15px;
        }

        .main-button {
            margin-left: 30px;
            margin-top: 7px;
            background-color: #f2f2f2;
            border-style: solid;
            border-color: #e6e6e6;
            border-width: 1px;
            border-radius: 5px;
            width: 72px;
            height: 32px;
            background-color: #ffcccc;
            border-color: #ffcccc;
        }
       
        .main-button:hover {
            background-color: #eebbbb;
            border-color: #eebbbb;
        }

        .main-button:active {
            background-color: #ddaaaa;
        }
        .el-input__icon {
            position:absolute;
            left:0px;
            top:0px;
        }

    </style>

</head>
<body>
    <div id="root" >

        <div id="pages" class="" ref="pages" v-cloak>
            <div class="pages_title">费用合计表</div>

            <div id="page_top" style="display:flex;margin-left:0px">

                <div class="pages_query">
                    <div class="query_item">
                        <div style="display:flex; flex-wrap:nowrap;width:80px;">开始时间</div>
                        <el-date-picker v-model="StartDay"
                                        type="datetime"
                                        :clearable="false"
                                        width="65"
                                        value-format="yyyy-MM-dd HH:mm:ss">
                        </el-date-picker>
                    </div>
                    <div class="query_item">
                        <div style="display:flex; flex-wrap:nowrap;width:70px;">结束时间</div>
                        <el-date-picker v-model="EndDay"
                                        type="datetime" :clearable="false"
                                        value-format="yyyy-MM-dd HH:mm:ss">
                        </el-date-picker>


                    </div>

                    <div class="query_item" style="width:150px;margin-left:15px;">
                        <div style="display:flex; flex-wrap:nowrap;width:100px;">部门</div>
                        <el-select v-model="selectDepartId" clearable placeholder="部门">
                            <el-option v-for="item in depart" :key="item.id" :label="item.value" :value="item.id"> </el-option>
                        </el-select>
                    </div>

                    <div class="query_item" style="width:150px;margin-left:15px;">
                        <div style="display:flex; flex-wrap:nowrap;width:100px;">业务员</div>
                        <el-select v-model="selectOperId" clearable placeholder="业务员" filterable >
                            <el-option label="全部" value=""></el-option>
                            <el-option v-for="item in filteredSellers" :key="item.id" :label="item.value" :value="item.id"> </el-option>
                        </el-select>
                    </div>
                  




                </div>





                    <div style="display:flex;justify-content:space-between;flex-wrap:nowrap;">
                        <button class="main-button" @@click="queryData">查询</button>
                        <button class="main-button" @@click="print()" style="background:#eee;border-color: #eee;">导出</button>
                        <button class="main-button" @@click="printPage()" style="background:#eee;border-color: #eee;">打印</button>
                    </div>


                </div>





            <div class="pages_content">
                <table id="test_table">
                    <thead>
                        <tr>
                            <th class="level_1">项目</th>
                            <th class="level_1">金额</th>
                            <th class="level_1">欠款</th>
                            @*<th class="level_4">{{StartDay+' '+EndDay}}</th>*@
                        </tr>
                    </thead>

                    <tbody ref="tbodyRef">
                        
                        <tr>
                            <td class="level_1">费用合计</td>
                            <td class="link" @@click="toTotal(fee_detail,fee_amout)" ref="closingBalance_1">{{toMoney(fee_amout)||'0'}}</td>
                            <td>{{toMoney(parseFloat(feeArrears))}}</td>
                        </tr>

                        <tr v-for="(item,index) in fee_detail" :key="item.sub_name">

                            <td class="level_3" ref="closingBalance_1">{{item.sub_name}}</td>
                            <td class="link" @@click="toSheet(item.sub_id,item.sub_name,toMoney(item.detail))" ref="closingBalance_1">{{toMoney(item.detail)}}</td>
                            <td></td>
                        </tr>



                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script>
        var g_operKey = '@Model.OperKey';
    </script>
    <script>


        var vm = new Vue({
            el: '#root',
            data() {
                return {
                    fee_detail: [],
                    fee_amout: 0,
                    seller: [],
                    depart: [],
                    selectOperId:"",
                    selectDepartId:"",
                    StartDay: new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)) + '-' + new Date().getDate() +" 00:00:00",
                    EndDay: new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)) + '-' + new Date().getDate() + " 23:59:59",
                    checked:true,
                   
                    feeArrears: 0,
                 
                }
            },
            computed: {

                filteredSellers() {
                    // 如果没有选中部门ID，则显示所有业务员
                    if (!this.selectDepartId) return this.seller;
                    
                    // 过滤 seller 列表，dept_path 包含 selectDepartId 的业务员才显示
                    return this.seller.filter(item => 
                        item.depart_path && item.depart_path.indexOf('/'+this.selectDepartId+'/') !== -1
                    );
                }

            },
            watch: {
                // 监听 selectDepartId 的变化
                selectDepartId(newVal) {
                    // 当 selectDepartId 变化时，将 selectOperId 重置为 "全部"
                    this.selectOperId = '';
                }
            },
            mounted() {
                this.$nextTick(() => {
                    this.getSellerId()
                })

            },
            methods: {
                
                queryData() {
                    //console.log(this.selectValue)
                    var param = {
                        operKey: g_operKey,
                        startTime: this.StartDay ,
                        endTime: this.EndDay,
                        operId: this.selectOperId,
                        departPath: this.getDepartPath()
                    }
                    this.GetSubjectBanlance(param)
                },
                toMoney(m) {
                    return toMoney(m)
                },
                GetSubjectBanlance(param) {
                    var that = this
                    console.log('22', that.skDiscAmount)
                    $.ajax({
                        url: '/api/FeeOutSummary/GetSubjectBanlance',
                        type: 'get',
                        data: param,
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        if (res.result === 'OK') {
                          
                            that.fee_detail = res.fee_detail
                         
                            that.feeArrears = res.feeArrears.left_amount
                          
                            let sum2 = 0
                            that.fee_detail.forEach(item => {

                                sum2 += Number(item.detail)

                            })
                            that.fee_amout = sum2
                        }
                    });
                },
                toTotal(amout,total_amt){
                    var sub_id=''
                    var sub_name=''
                    amout.forEach(item => {
                        sub_id += item.sub_id+','
                        sub_name += item.sub_name+','
                    });
                    sub_id= sub_id.slice(0,sub_id.length-1);
                    sub_name=sub_name.slice(0,sub_name.length-1);

                    let url =  `Report/FeeOutDetail?&startDay=${this.StartDay}&endDay=${this.EndDay}&sub_id=${sub_id}&sub_name=${sub_name}&byHappenTime=true`
                    if (this.selectOperId) {
                        let sellerName = this.getSellerName()
                        url += `&seller_id=${this.selectOperId}&seller_name=${sellerName}`
                    }
                    
                    if (toMoney(total_amt)) {
                        window.parent.newTabPage("收入支出明细表",url)
                    }                },
                toSheet(sub_id,sub_name,amt) {

                    let url = `Report/FeeOutDetail?&startDay=${this.StartDay}&endDay=${this.EndDay}&sub_id=${sub_id}&sub_name=${sub_name}&byHappenTime=true`
                    if (this.selectOperId) {
                        let sellerName = this.getSellerName()
                        url += `&seller_id=${this.selectOperId}&seller_name=${sellerName}`
                    }
                    
                    if (amt) { 
                        window.parent.newTabPage("收入支出明细表", url)
                    }
                },
               
             
                getSellerName() {
                    for (let s of this.seller) {
                        if (s.id === this.selectOperId) {
                            return s.value
                        }
                    }
                },
                getDepartName() {
                    for (let d of this.depart) {
                        if (d.id === this.selectDepartId) {
                             return d.value
                        }
                    }
                },
                getDepartPath(){
                    if (!this.selectDepartId) {
                        return null
                    }
                    let motherId = this.selectDepartId
                    let path = `/`
                    while (motherId && motherId !=='0') {
                        path = `/${motherId}`+ path
                        motherId = this.getMotherId(motherId)
                        
                    } 
                    return path
                },
                getMotherId(did){
                    for (let d of this.depart) {
                        if (d.id === did) {
                             return d.mid
                        }
                    }
                },
                printPage(){
                    $("#pages").print({
                         globalStyles: true,//是否包含父文档的样式，默认为true
                        mediaPrint: false,//是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                        stylesheet: null,//外部样式表的URL地址，默认为null
                        noPrintSelector: ".no-print",//不想打印的元素的jQuery选择器，默认为".no-print"
                        iframe: true,//是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                        append: null,//将内容添加到打印内容的后面
                        prepend: null,//将内容添加到打印内容的前面，可以用来作为要打印内容
                        deferred: $.Deferred()//回调函数

                    });
                },
                getSellerId() {
                    var that = this
                    $.ajax({
                        url: '/api/FeeOutSummary/GetSellerId',
                        type: 'get',
                        data: {
                            operKey: g_operKey,
                        },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        if (res.result === 'OK') {
                            that.seller = res.seller
                            that.depart = res.depart
                        }
                    });
                }, print() {

                    excel = new ExcelGen({
                        "src_id": "test_table",
                        "show_header": true
                    });
                    excel.generate("经营利润表.xlsx");
                }

            }                  
        })
    </script>
    

</body>
</html>