﻿@page
@model ArtisanManage.Pages.BaseInfo.BarcodeScaleViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js?v=@Html.Raw(Model.Version)"></script>
    <script type="text/javascript">
        var frame = "BarcodeScaleEdit";

        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            $("#popItem").jqxWindow('close');
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == frame) {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var row = rs.data.record;
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;
                        rows[0].i = row.group_id;

                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "group_name", rs.data.record.group_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "remark", rs.data.record.remark);
                }
            };
        });
        var m_db_id = "10";
        var newCount = 1;

        function btnAddBarcodeScale_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="${frame}?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

        }
        function btnUpLoad_click(e) {
           
        }
        function onGridRowEdit(rowIndex) {
            var scale_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'scale_id');
            var barcode_scale_group_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'barcode_scale_group_id');
            var barcode_scale_group_name = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'barcode_scale_group_name');
            // $('#popItem').jqxWindow('open');
            // $("#popItem").jqxWindow('setContent', '<iframe src="GroupEdit?operKey=' + g_operKey + '&group_id=' + group_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
            if (!scale_id || scale_id === 'null' ) {
                console.error('Invalid scale_id:', scale_id);
                return;
            }
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="${frame}?operKey=${g_operKey}&scale_id=${scale_id}&barcode_scale_group_id=${barcode_scale_group_id}&barcode_scale_group_name=${barcode_scale_group_name}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }
        var itemSource = {};
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)

            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                console.log(args);
                //if (args.datafield == "barcode_scale_group_name") {
                    //if (args.originalEvent.button == 2) return;
                        //var barcode_scale_group_id = args.row.bounddata.barcode_scale_group_id;
                        //RowIndex = args.rowindex;
                        // if (ForSelect) {
                        //     var group_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                        //     var group_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "group_name");
                        //     var msg = {
                        //         msgHead: 'GroupsView', action: 'select', group_id: group_id, group_name: group_name
                        //     };
                        //     window.parent.postMessage(msg, '*');
                        // }
                        // else {
                        // var barcode_scale_group_id = $('#gridItems').jqxGrid('getcellvalue', "i");
                        // window.parent.newTabPage('条码秤组', `BaseInfo/BarcodeScaleGroupEditPlu?barcode_scale_group_id=${barcode_scale_group_id}`, window);
                            //onGridRowEdit(args.rowindex);
                            //$('#popItem').jqxWindow('open');
                            // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                   // }
               //}

         });


            $("#Cancel").on('click', function () {
                for (var i = 0; i < 10; i++) {
                    $('#jqxgrid').jqxGrid('deleterow', i);
                    $('#jqxgrid').jqxGrid('addrow', i, {})
                }
            });

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 400, width: 600, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });
             
            QueryData();
        });
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
    </style>
</head>

<body>

    <div id="divHead" style="display:flex;justify-content:flex-start; margin-top:20px;">
        @* <div><button onclick="QueryData()" class="margin">查询</button></div> *@
        <div style="margin-left: auto;">
            <div style="display:flex;margin-right:40px">
                <div><button onclick="btnAddBarcodeScale_click()" class="margin" style="width:100px;">添加条码秤</button></div>
                <div><button onclick="btnUpLoad_click()" class="margin" style="width:100px" >下传</button></div>
            </div>
        </div>
    </div>

    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:100%;margin-bottom:10px;"></div>


    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">条码秤信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <script>
        function getFormData() {
            var formData = [];
            var errMsg = '';

            // 获取 jqxGrid 选中行数据
            var grid = $('#gridItems');
            if (grid.length > 0) {
                // 获取所有选中行的索引
                var selectedRowIndexes = grid.jqxGrid('getselectedrowindexes');

                if (selectedRowIndexes && selectedRowIndexes.length > 0) {
                    selectedRowIndexes.forEach(function (rowIndex) {
                        // 获取每个选中行的实际数据
                        var rowData = grid.jqxGrid('getrowdata', rowIndex);
                        if (rowData) {
                            // 将 rowIndex 添加到 rowData 中
                            rowData.rowIndex = rowIndex;
                            formData.push(rowData);
                        }
                    });
                } else {
                    errMsg = '请选择至少一行。';
                }
            } else {
                errMsg = '未找到 jqxGrid 组件，请检查页面配置。';
            }

            return {
                formData: formData,
                errMsg: errMsg
            };
        }
        async function downloadGroupData(row) {
            try {
                const response = await $.ajax({
                    url: '/api/BarcodeScaleView/DownloadGroupData',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        operKey: g_operKey,
                        groupData: row
                    })
                });

                if (response.result !== 'OK') {
                    throw new Error(response.msg || '连接条码秤失败');
                }

                return response.lines;
            } catch (error) {
                bw.toast(error.message);
                return false;
            }
        }
        async function btnUpLoad_click() {
            const formFlds = getFormData();
            const gridRows = $('#gridItems').jqxGrid('getrows');
            var rowIndex = -1;
            if (formFlds.errMsg) {
                bw.toast(formFlds.errMsg);
                return;
            }

            var container = window.parent.CefGlue;
            window.parent.g_SheetsWindowForPrint = window;
            if (!container || !container.printSheetByTemplate) {
                bw.toast('在营匠客户端程序中才可以下传', 3000);
                return;
            }
            // 验证前缀码
            for (const row of formFlds.formData) {
                const preCode = row.barcode_head2;
                if (!preCode || preCode.length !== 2) {
                    rowIndex = gridRows.findIndex(gridRow =>
                        gridRow.scale_id === row.scale_id
                    );
                    $('#gridItems').jqxGrid('setcellvalue', rowIndex, 'upload_result', "前缀码必须为2位数");
                    continue;
                }
                if (!row.bar_ip || !row.barcode_scale_group_name || !row.barcode_head2) {
                    rowIndex = gridRows.findIndex(gridRow =>
                        gridRow.scale_id === row.scale_id
                    );
                    $('#gridItems').jqxGrid('setcellvalue', rowIndex, 'upload_result', "缺少必要字段");
                    continue;
                }
            }
            // 为每个分组执行下传
            var rows = await downloadGroupData(formFlds.formData);
            await sleep(3000);  // 1000 毫秒 = 1秒 延时
            try {
                var results = container.DownloadPLU(rows);
                if (results) {
                    const resultsArray = typeof results === 'string' ? JSON.parse(results) : results;
                    const dataArray = Array.isArray(resultsArray) ? resultsArray : [resultsArray];

                    dataArray.forEach(item => {
                        if (item && typeof item.rowIndex !== 'undefined') {
                            $('#gridItems').jqxGrid('setcellvalue', item.rowIndex, 'upload_result', item.result);
                        }
                    });
                }
            } catch (error) {
                bw.toast('下传失败：' + error.message);
            }
            
        }
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    </script>
</body>
</html>