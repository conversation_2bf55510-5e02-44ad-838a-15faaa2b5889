﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.Elfie.Model.Strings;
using Microsoft.CodeAnalysis.Elfie.Serialization;
using Microsoft.EntityFrameworkCore.ChangeTracking.Internal;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.Formula.Functions;
using NPOI.Util;
using OBS.Model;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Dynamic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using static ArtisanManage.AppController.VisitStrategyController;

namespace ArtisanManage.AppController
{
    [Route("AppApi/[controller]/[action]")]
    public class VisitStrategyController : BaseController
    {
        public VisitStrategyController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        public class Supcust
        {
            public string supcust_id { get; set; }
            public string sup_name { get; set; } = "";
            public string sup_addr { get; set; } = "";
            public string mobile { get; set; } = "";
            public int visit_cycle { get; set; } = 0;
        }

        public class SupcustBaseInfo : Supcust
        {
            public string last_visit_date { get; set; } = "";
        }

        public class PageInfo
        {
            public int total { get; set; }
            public int PageNo { get; set; }
            public int PageSize { get; set; }
            public List<SupcustEInfo> list { get; set; }
        }

        public class SupcustInfo : Supcust
        {
            public string pre_visit_date { get; set; } = "";
            public string next_visit_date { get; set; } = "";
            public SupcustInfo(string supcust_id, string sup_name, string sup_addr, string mobile, int visit_cycle)
            {
                this.supcust_id = string.IsNullOrEmpty(supcust_id) ? string.Empty : supcust_id;
                this.sup_name = string.IsNullOrEmpty(sup_name) ? string.Empty : sup_name;
                this.sup_addr = string.IsNullOrEmpty(sup_addr) ? string.Empty : sup_addr;
                this.mobile = string.IsNullOrEmpty(mobile) ? string.Empty : mobile;
                this.visit_cycle = visit_cycle;
            }
        }

        public class SupcustEInfo : SupcustBaseInfo
        {
            public string seller_id { get; set; } = "";
        }

        public class DateVisitInfo
        {
            public string date { get; set; }
            public List<SupcustInfo> supcust_list { get; set; }
            public DateVisitInfo(string date, List<SupcustInfo> supcust_list)
            {
                this.date = date;
                this.supcust_list = supcust_list;
            }
        }

        /// <summary>
        /// 获取、保存计划日程
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> GetVisitSchedule([FromBody] dynamic info)
        {
            string operKey = info.operKey;
            DateTime start_visit_date = info.start_visit_date;
            DateTime end_visit_date = info.end_visit_date;
            string seller_id = info.seller_id;
            bool assign = info.assign;
            Security.GetInfoFromOperKey(operKey, out string companyID);          

            string result = "OK";
            string msg = "";
            List<DateVisitInfo> data = new List<DateVisitInfo>();
            //获取业务员负责的客户信息查询客户拜访周期 
            List<SupcustBaseInfo> supcustData = await getSupcustList(companyID, seller_id);

            if (supcustData == null || supcustData.Count == 0)
            {
                msg = "业务员安排客户列表为空";
                return Json(new { result, data, msg });
            }

            data = getVisitSchedule(operKey, start_visit_date, end_visit_date, seller_id, supcustData, assign).Result;
            result = "OK";

            return Json(new { result, data, msg });
        }

        /// <summary>
        /// 业务员获取单日计划
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetVisitScheduleForSeller(string operKey, string cur_lng, string cur_lat, string visit_date,string oper_id)
        {
            string seller_id = oper_id;
            Security.GetInfoFromOperKey(operKey, out string companyID);

            string result = "OK";
            string msg = "";

            string distance_sql =
                @$"st_distance(ST_Transform(ST_SetSRID(isp.addr_lnglat,4326)::geometry, 3857),ST_Transform('SRID=4326;POINT({cur_lng} {cur_lat})'::geometry, 3857)) as distance";

            string sql = $@"
select vp.supcust_id, isp.sup_name, isp.sup_addr, isp.mobile as sup_tel,isp.addr_lng,
st_distance(ST_Transform(ST_SetSRID(isp.addr_lnglat,4326)::geometry, 3857),ST_Transform('SRID=4326;POINT({cur_lng} {cur_lat})'::geometry, 3857)) as distance ,
ic.visit_cycle,
rsp.realtime->>'lastSaleTime' as last_sale_time,rsp.realtime->>'lastVisitTime' as last_visit_time
from info_visit_schedule_plan vp
left join info_supcust isp on isp.supcust_id = vp.supcust_id
left join info_seller_supcust_cycle ic on isp.supcust_id = ic.supcust_id and ic.seller_id = {seller_id}
left join realtime_supcust rsp on {companyID} = rsp.company_id and vp.supcust_id = rsp.supcust_id
where vp.company_id = {companyID} and vp.seller_id = {seller_id} and visit_date = '{visit_date}'
";

            dynamic data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            return Json(new { result, data, msg });
        }

        /// <summary>
        /// //根据业务员、渠道、片区、等级筛选出负责的客户集合
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> GetSupcustList([FromBody] dynamic info)
        {
            string result = "";
            string msg = "";
            string operKey = info.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string seller_id = info.seller_id;
            string group_id = info.group_id;
            string sup_name = info.sup_name;
            string rank_id = info.rank_id;
            List<string> region_ids = info.region_id.ToObject<List<string>>();  // 片区id 因为多选 传来要解析为列表
            List<string> day_ids = JsonConvert.DeserializeObject<List<string>>(Convert.ToString(info.day_id));


            string condi =
                (group_id != "" ? $" AND {group_id} = sup_group" : "") +
                (region_ids != null && region_ids.Count > 0 ? $" AND EXISTS (SELECT 1 FROM unnest(string_to_array(other_region, '/')) AS region WHERE region IN ({string.Join(",", region_ids.Select(r => $"'{r}'"))}) )" : "") +  // 多选条件
                (rank_id != "" ? $" AND {rank_id} = sup_rank" : "") +
                (sup_name != "" ? $" AND sup_name LIKE '%'||'{sup_name}'||'%'" : "")+
                (day_ids != null && day_ids.Count > 0 ? $" and ivdc.day_id IN ({string.Join(",", day_ids.Select(d => $"'{d}'"))})" : "");

            string sql = $@"
                select distinct i.supcust_id, i.sup_name, i.sup_addr, i.mobile, 
                       TO_CHAR(m.last_date, 'YYYY-MM-DD') as last_visit_date, 
                       COALESCE(ic.visit_cycle, 0) as visit_cycle, 
                       {seller_id} as seller_id, i.other_region 
                from info_supcust i
                left join (
                    select supcust_id, MAX(visit_date) as last_date 
                    from info_visit_schedule_plan 
                    where company_id = {companyID} 
                      and seller_id = {seller_id} 
                      and visit_date <= CURRENT_DATE 
                    group by supcust_id
                ) m ON m.supcust_id = i.supcust_id
                left join info_seller_supcust_cycle ic on i.supcust_id = ic.supcust_id and {seller_id} = ic.seller_id 
                join info_operator io on io.oper_id = {seller_id} 
                left join info_visit_day_client ivdc on ivdc.supcust_id = i.supcust_id
                where EXISTS (
                        SELECT 1
                        FROM json_array_elements_text(io.oper_regions) AS region
                        WHERE i.other_region LIKE '%/' || region || '/%'
                    )
                {condi}
                and i.status = 1 
                and i.company_id = {companyID}
                limit 1000;
            ";
            List<SupcustEInfo> list = await CDbDealer.GetRecordsFromSQLAsync<SupcustEInfo>(sql, cmd);

            // 对list进行分页与排序 传姓名的时候，以匹配度排序 无姓名时按cycle与last_visit_date 优先最久远 新增排序功能
            if (!string.IsNullOrEmpty(sup_name))
            {
                list = list.OrderBy(item => item.sup_name.IndexOf(sup_name, StringComparison.OrdinalIgnoreCase)).ToList();
            }
            else
            {
                list = list.OrderBy(item =>
                {
                    // 优先按 last_visit_date 排序，空值放在最前面
                    if (string.IsNullOrEmpty(item.last_visit_date))
                    {
                        return int.MinValue;
                    }
                    // 按 last_visit_date 与当前日期的差值排序，差值越大排在前
                    int daysDiff = (DateTime.Now - DateTime.Parse(item.last_visit_date)).Days;
                    // 如果 visit_cycle 为 0，将其放在最后
                    return item.visit_cycle == 0 ? int.MaxValue : daysDiff;
                }).ThenBy(item =>
                {
                    // 如果 visit_cycle 为 0，将其放在最后
                    return item.visit_cycle == 0 ? int.MaxValue : 0;
                }).ToList();
            }

            List<SupcustEInfo> data = list;

            result = "OK";
            return Json(new { result, data, msg });
        }

        /// <summary>
        /// 修改拜访计划
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SaveVisitSchedule([FromBody] dynamic info)
        {
            string operKey = info.operKey;
            string seller_id = info.seller_id;
            Security.GetInfoFromOperKey(operKey, out string companyID);

            string result = "OK";
            string data = null;
            string msg = "";

            string sql = "";

            DateTime now = DateTime.Now.Date;
            DateTime minDate = DateTime.Parse("2100-01-01"); ;
            DateTime maxDate = now;
            List<string> add_supcust_list = new List<string>();//
            
            foreach (var e in info.dateVisitInfos)
            {
                string supcust_id = e.supcust_id;
                string new_visit_date = e.new_visit_date;
                string old_visit_date = e.old_visit_date;

                if (new_visit_date != "")
                {
                    DateTime new_date = DateTime.Parse(new_visit_date);
                    minDate = minDate < new_date ? minDate : new_date;
                    if (new_date < now)
                    {
                        result = "error";
                        msg = "不允许调整以前的安排";
                        return Json(new { result, data, msg });
                    }
                    sql += $@"
INSERT INTO info_visit_schedule_plan (company_id, seller_id, supcust_id, visit_date)
VALUES ({companyID}, {seller_id}, {supcust_id}, '{new_visit_date}') ON CONFLICT (seller_id, supcust_id, visit_date) DO NOTHING;
";
                }
                if (old_visit_date != "")
                {
                    DateTime old_date = DateTime.Parse(old_visit_date);
                    maxDate = maxDate > old_date ? maxDate : old_date;
                    if (old_date < now)
                    {
                        result = "error";
                        msg = "不允许调整以前的安排";
                        return Json(new { result, data, msg });
                    }
                    sql += $@"
DELETE FROM info_visit_schedule_plan
WHERE supcust_id = {e.supcust_id} and visit_date = '{old_visit_date}' and seller_id = {seller_id} and company_id = {companyID};
";
                }
                add_supcust_list.Add(supcust_id);
            }
            if (sql != "") await saveSchedule(sql);

            return Json(new { result, data, msg });
        }

        /// <summary>
        /// 写入拜访周期
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SetSupcustVisitCycle([FromBody] dynamic info)
        {
            string seller_id = info.seller_id;
            string ss = info.supcust_id;
            string[] supcusts_ids = ss.Split(',');
            string visit_cycle = info.visit_cycle;
            string result = "OK";
            string msg = "";
            string data = null;

            // 插入或更新 visit_cycle
            string insertOrUpdateSql = $@"
INSERT INTO info_seller_supcust_cycle (seller_id, supcust_id, visit_cycle)
VALUES 
{string.Join(",\n", supcusts_ids.Select(supcust => $"({seller_id}, {supcust}, {visit_cycle})"))}
ON CONFLICT (seller_id, supcust_id)
DO UPDATE SET visit_cycle = EXCLUDED.visit_cycle;
";
            if (insertOrUpdateSql != "") await saveSchedule(insertOrUpdateSql);

            // 删除相关客户之后已生成的日程
            string deleteSql = $@"
DELETE FROM info_visit_schedule_plan
WHERE supcust_id IN ({string.Join(",", supcusts_ids.Select(supcust => $"'{supcust}'"))})
AND visit_date > CURRENT_DATE AND seller_id = {seller_id};
";

            //if (deleteSql != "") await saveSchedule(deleteSql);

            return Json(new { result, data, msg });
        }

        /// <summary>
        /// 查询业务员list
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> GetSellerList([FromBody] dynamic info)
        {
            string operKey = info.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string result = "OK";
            string msg = "";
            string sql = $@"select oper_id as seller_id,oper_name as seller_name,oper_regions as region_id,mobile from info_operator where is_seller = true and company_id = {companyID} and status!=0 and can_login!=false";
            dynamic data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            // 处理数据
            var sellers = new List<object>();
            foreach (dynamic row in data)
            {
                var seller = new
                {
                    seller_id = (dynamic)row.seller_id,
                    seller_name = (dynamic)row.seller_name,
                    mobile = (dynamic)row.mobile,
                    region_id = JArray.Parse((dynamic)row.region_id.ToString())
                };
                sellers.Add(seller);
            }

            // 构建最终的 JSON 结构
            var response = new
            {
                result = result,
                data = sellers,
                msg = msg
            };

            // 返回 JSON 结果
            return Json(response);
        }

        public async Task<List<DateVisitInfo>> getVisitSchedule(string operKey, DateTime start_visit_date,DateTime end_visit_date,string seller_id, List<SupcustBaseInfo> supcustData, bool assign)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where company_id = {companyID}";

            List<DateVisitInfo> data = new List<DateVisitInfo>();

            //获取历史数据
            Dictionary<string, List<SupcustInfo>> oldDateInfos = await getDeduceVisitDateFromDB(start_visit_date, end_visit_date, companyID, seller_id, supcustData);

            DateTime dateTime = DateTime.Now;
            //生成日程并填充进原日程and保存 仅生成当日以后
            if (start_visit_date > dateTime)
            {
                data = await getDeduceVisitDate(start_visit_date, end_visit_date, companyID, seller_id, supcustData, oldDateInfos, assign);
            }
            else
            {
                if (end_visit_date > dateTime)
                {
                    data = await getDeduceVisitDate(dateTime.AddDays(1), end_visit_date, companyID, seller_id, supcustData, oldDateInfos, assign);
                }
            }

            data = await replenishData(data,start_visit_date,supcustData);
            return data;
        }

        //生成推断日期
        public async Task<List<DateVisitInfo>> getDeduceVisitDate(DateTime this_date, DateTime last_date, string company_id, string seller_id, List<SupcustBaseInfo> supcustBaseInfos, Dictionary<string, List<SupcustInfo>> oldVisitData, bool assign)
        {
            Dictionary<string, List<SupcustInfo>> dateVisitInfoMap = oldVisitData;
            Dictionary<string, List<SupcustInfo>> newDateVisitInfoMap = new Dictionary<string, List<SupcustInfo>>(); //储存无拜访记录客户记录
            List<SupcustInfo> sp_supcusts = new List<SupcustInfo>(); //储存无拜访记录客户

            string sql = "";

            foreach (var e in supcustBaseInfos)
            {
                if (e.visit_cycle == 0) continue;
                DateTime i = new DateTime();
                if (e.last_visit_date != "")
                {
                    DateTime ldate = AddWorkDays(DateTime.Parse(e.last_visit_date), e.visit_cycle); //拟第一次拜访时间 上次拜访 + cycle,小于start_date 则加入sp_supcusts 让有last_date的排前面 
                    i = ldate >= this_date ? ldate : this_date;
                }
                else
                {
                    sp_supcusts.Add(new SupcustInfo(
                        e.supcust_id, e.sup_name, e.sup_addr, e.mobile, e.visit_cycle
                        ));
                    continue;
                }

                //保证第一次分配合理则后续合理
                if(assign)
                for (; i <= last_date; i = AddWorkDays(i, e.visit_cycle))
                {
                    if (!dateVisitInfoMap.ContainsKey(DateToString(i))) { dateVisitInfoMap[DateToString(i)] = new List<SupcustInfo>(); }
                    dateVisitInfoMap[DateToString(i)].Add(new SupcustInfo(
                        e.supcust_id, e.sup_name, e.sup_addr, e.mobile, e.visit_cycle
                        ));
                    string vd = DateToString(i);
                    sql += $@"
INSERT INTO info_visit_schedule_plan (company_id, seller_id, supcust_id, visit_date)
VALUES ({company_id}, {seller_id}, {e.supcust_id}, '{vd}');
";
                    
                }
            }

            sp_supcusts.Sort((x, y) => x.visit_cycle.CompareTo(y.visit_cycle));


            int total = 0;
            int key = 36288;
            //第一次遍历得出合理的每日拜访软上限 该上限仅针对无拜访记录客户
            foreach (var supcust in sp_supcusts)
            {
                if (supcust.visit_cycle != 0) total = total + key / supcust.visit_cycle;
            }
            total = (total % key == 0) ? total / key : total / key + 1;

            //填充无过往记录客户
            foreach (var e in sp_supcusts)
            {
                DateTime i = this_date;

                while(newDateVisitInfoMap.ContainsKey(DateToString(i)) && newDateVisitInfoMap[DateToString(i)].Count >= total)
                {
                    i = AddWorkDays(i, 1);
                }

                if(assign)
                for (; i <= last_date; i = AddWorkDays(i, e.visit_cycle))
                {
                    if (!newDateVisitInfoMap.ContainsKey(DateToString(i))) { newDateVisitInfoMap[DateToString(i)] = new List<SupcustInfo>(); }
                    newDateVisitInfoMap[DateToString(i)].Add(new SupcustInfo(
                        e.supcust_id, e.sup_name, e.sup_addr, e.mobile, e.visit_cycle
                        ));
                    string vd = DateToString(i);
                    sql += $@"
INSERT INTO info_visit_schedule_plan (company_id, seller_id, supcust_id, visit_date)
VALUES ({company_id}, {seller_id}, {e.supcust_id}, '{vd}');
";

                }
            }

            // 合并dateVisitInfoMap
            foreach (var kvp in newDateVisitInfoMap)
            {
                if (dateVisitInfoMap.ContainsKey(kvp.Key))
                    dateVisitInfoMap[kvp.Key].AddRange(kvp.Value);
                else
                    dateVisitInfoMap[kvp.Key] = kvp.Value;
            }

            // 将日期和对应的 VisitInfo 组装成 DateInfo 对象
            List<DateVisitInfo> data = new List<DateVisitInfo>();

            foreach (var kvp in dateVisitInfoMap)
            {
                string date = kvp.Key;
                List<SupcustInfo> supcustList = kvp.Value;
                data.Add(new DateVisitInfo(date, supcustList));
            }

            if(sql != "")
            {
                await saveSchedule(sql);
            }

            // 按日期排序
            data.Sort((dateInfo1, dateInfo2) => dateInfo1.date.CompareTo(dateInfo2.date));

            return data;
        }

        //从数据库获取计划
        public async Task<Dictionary<string, List<SupcustInfo>>> getDeduceVisitDateFromDB(DateTime this_date, DateTime last_date, string company_id, string seller_id, List<SupcustBaseInfo> supcustBaseInfos)
        {
            string sql = $@"
                SELECT v.supcust_id, v.visit_date 
                FROM info_visit_schedule_plan v
                JOIN (
                    SELECT DISTINCT supcust_id FROM info_supcust 
                    WHERE company_id = {company_id}
                ) s ON v.supcust_id = s.supcust_id
                WHERE v.company_id = {company_id} 
                AND v.seller_id = {seller_id} 
                AND v.visit_date BETWEEN '{this_date}' AND '{last_date}'
                ";

            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            Dictionary<string, List<SupcustInfo>> dateVisitInfoMap = new Dictionary<string, List<SupcustInfo>>();
            Dictionary<string, SupcustBaseInfo> supcustInfo = supcustBaseInfos.ToDictionary(s => s.supcust_id);

            foreach (dynamic dateVisitInfo in data)
            {
                string visit_date = ((dynamic)dateVisitInfo).visit_date;

                if (!dateVisitInfoMap.ContainsKey(visit_date))
                {
                    dateVisitInfoMap[visit_date] = new List<SupcustInfo>();
                }
                string supcustId = dateVisitInfo.supcust_id.ToString(); // 强制转换为字符串
                if (!supcustInfo.ContainsKey(supcustId))
                {
                    Console.WriteLine($"Warning: supcust_id {supcustId} not found in supcustBaseInfos.");
                    continue; // 跳过不存在的 ID，避免崩溃
                }
                SupcustBaseInfo s = supcustInfo[supcustId];

                dateVisitInfoMap[visit_date].Add(new SupcustInfo(s.supcust_id, s.sup_name, s.sup_addr, s.mobile, s.visit_cycle));
            }
/*            //并行计算优化 疑似有并发bug
            Parallel.ForEach(data, dateVisitInfo =>
            {
                string visit_date = ((dynamic)dateVisitInfo).visit_date; 

                lock (dateVisitInfoMap)
                {
                    if (!dateVisitInfoMap.ContainsKey(visit_date))
                    {
                        dateVisitInfoMap[visit_date] = new List<SupcustInfo>();
                    }
                }

                if (supcustInfo.TryGetValue(((dynamic)dateVisitInfo).supcust_id, out SupcustBaseInfo s))
                {
                    lock (dateVisitInfoMap[visit_date])
                    {
                        dateVisitInfoMap[visit_date].Add(new SupcustInfo(s.supcust_id, s.sup_name, s.sup_addr, s.mobile, s.visit_cycle));
                    }
                }
            });*/

            return dateVisitInfoMap;
        }

        //填充last_visit_date与next_visit_date
        public async Task<List<DateVisitInfo>> replenishData(List<DateVisitInfo> data,DateTime start_date, List<SupcustBaseInfo> supcustBaseInfos)
        {

            Dictionary<string, DateTime> lastVisitDateDict = new Dictionary<string, DateTime>();

            var supcust_ids = string.Join(",", supcustBaseInfos.Select(s => $"'{s.supcust_id}'"));

            string pre_sql = $@"
SELECT supcust_id, MAX(visit_date) AS max FROM info_visit_schedule_plan WHERE supcust_id IN ({supcust_ids}) AND visit_date < '{start_date}' GROUP BY supcust_id;
";
            dynamic pre_list = await CDbDealer.GetRecordsFromSQLAsync(pre_sql,cmd);

            foreach (var p in pre_list)
            {
                lastVisitDateDict[p.supcust_id] = DateTime.Parse(p.max);
            }

            // 填充 last_visit_date
            foreach (var dateVisitInfo in data)
            {
                foreach (var supcustInfo in dateVisitInfo.supcust_list)
                {
                    if (lastVisitDateDict.ContainsKey(supcustInfo.supcust_id))
                    {
                        supcustInfo.pre_visit_date = lastVisitDateDict[supcustInfo.supcust_id].ToString("yyyy-MM-dd");
                    }
                    else
                    {
                        supcustInfo.pre_visit_date = "";
                    }

                    lastVisitDateDict[supcustInfo.supcust_id] = DateTime.Parse(dateVisitInfo.date);
                }
            }

            return data;
        }



        public async Task<string> saveSchedule(string sql)
        {
            string msg = "";

            CMySbTransaction tran = cmd.Connection.BeginTransaction();//开启事务
            try
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
                tran.Commit();
                return msg;
            }
            catch (Exception ex)
            {
                tran.Rollback();
                Console.WriteLine(ex + "拜访计划数据生成/写入异常");
                msg = "error";
                return msg;
            }
        }

        //根据业务员、渠道、片区、等级筛选出负责的客户集合
        public async Task<dynamic> getSupcustList(string companyID, string seller_id, string group_id = "", string region_id = "", string rank_id = "", string sup_name = "")
        {
            string condi = 
                (group_id != "" ? $" AND {group_id} = sup_group" : "") +
                (region_id != "" ? $" AND EXISTS (SELECT 1 FROM unnest(string_to_array(other_region, '/')) AS region WHERE region = '{region_id}')" : "") +
                (rank_id != "" ? $" AND {rank_id} = sup_rank" : "") +
                (sup_name != "" ? $" AND sup_name LIKE '%' + {sup_name} + '%'" : "");
            //先查info_operator获取oper_regions 再根据此查info_supcust获取在该片区下的客户信息 再联info_seller_supcust表获取cycle(查不到置0)
            string sql = $@"
select i.supcust_id,i.sup_name,i.sup_addr,i.mobile,TO_CHAR(m.last_date, 'YYYY-MM-DD') as last_visit_date,COALESCE(ic.visit_cycle, 0) as visit_cycle,{seller_id} as seller_id,i.other_region from info_supcust i
left join (select supcust_id, MAX(visit_date) as last_date from info_visit_schedule_plan where company_id = {companyID} and seller_id = {seller_id} GROUP BY supcust_id) m ON m.supcust_id = i.supcust_id
left join info_seller_supcust_cycle ic on i.supcust_id = ic.supcust_id and {seller_id} = ic.seller_id 
join info_operator io on io.oper_id = {seller_id} 
where EXISTS (
        SELECT 1
        FROM json_array_elements_text(io.oper_regions) AS region
        WHERE i.other_region LIKE '%/' || region || '/%'
    )
{condi} and i.status = 1 and i.company_id = {companyID}; 
";
            dynamic data = await CDbDealer.GetRecordsFromSQLAsync<SupcustBaseInfo>(sql, cmd);

            return data;
        }

        public string DateToString(DateTime dateTime)
        {
            return dateTime.ToString("yyyy-MM-dd");
        }

        //判断是否为节假日 默认只跳过周日，后续在这里写根据设置/数据库获取详细放假安排
        public bool IsHoliday(DateTime date)
        {
            return false;
        }

        public DateTime AddWorkDays(DateTime startDate, int workDays)
        {
            DateTime result = startDate;
            int addedDays = 0;

            while (addedDays < workDays)
            {
                result = result.AddDays(1);
                if (!IsHoliday(result))
                {
                    addedDays++;
                }
            }

            return result;
        }
    }
}

