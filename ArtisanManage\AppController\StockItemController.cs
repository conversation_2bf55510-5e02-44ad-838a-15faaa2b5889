﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NPOI.HSSF.Record;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;
using static Org.BouncyCastle.Math.EC.ECCurve;

namespace ArtisanManage.AppController
{
    [Route("AppApi/[controller]/[action]")]
    [ApiController]
    public class StockItemController : QueryController
    {
        public StockItemController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpGet]
        public async Task<JsonResult> GetStockItem(string operKey, string itemID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $@"";
            SQLQueue QQ = new SQLQueue(cmd);
            sql = $@"select itp.item_name,itp.item_images,itp.barcode,iimu.*
from info_item_prop itp
left join
    (select item_id ,s->>'f1' as s_unit_no, s->>'f2' as s_unit_factor,m->>'f1' as m_unit_no, m->>'f2' as m_unit_factor,b->>'f1' as b_unit_no, b->>'f2' as b_unit_factor
     from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor)) as json from info_item_multi_unit where company_id = {companyID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)
     )iimu on iimu.item_id = itp.item_id
where itp. item_id = {itemID} and itp.company_id = {companyID};
";
            //s.stock_qty,s.sell_pend_qty as sell_pend_qty ,s.branch_id as id,ib.branch_name as name,COALESCE(produce_date,'') as produce_date,COALESCE(batch_no,'') as batch_no 
            QQ.Enqueue("getItemInfo", sql);
            sql = $@"select item_id,s.branch_id,branch_name,sum(s.stock_qty) as stock_qty,sum(s.sell_pend_qty) as sell_pend_qty,
json_agg(json_build_object('stock_qty',COALESCE(stock_qty,0),'sell_pend_qty',COALESCE(sell_pend_qty,0),'batch_id',itb.batch_id,'produce_date',itb.produce_date,'batch_no',COALESCE(itb.batch_no,''),'batch_id',s.batch_id
)) as batch_stock
from stock s
left join info_branch ib on ib.branch_id  = s.branch_id and ib.company_id = {companyID}
left join info_item_batch itb on itb.batch_id  = s.batch_id and itb.company_id = s.company_id
where s.item_id = {itemID} and s.company_id = {companyID} group by item_id,s.branch_id,branch_name";
            QQ.Enqueue("getBranchItemInfo", sql);
            sql = $@"select threshold_lack,threshold_overload,branch_id from info_stock_alert where item_id = {itemID} and company_id = {companyID}";
            QQ.Enqueue("getBranchItemAlertInfo", sql);
            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> itemInfo = null;
            List<ExpandoObject> branchItemInfo = null;
            List<ExpandoObject> branchItemAlertInfo = null;
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "getItemInfo")
                {
                    itemInfo = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "getBranchItemInfo")
                {
                    branchItemInfo = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "getBranchItemAlertInfo")
                {
                    branchItemAlertInfo = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            int code =0;
            string msg = "";
            return Json(new { code, msg, itemInfo, branchItemInfo, branchItemAlertInfo });
        }
        [HttpGet]
        public async Task<JsonResult> GetStockChangeDetail(string operKey,string itemID,string branchID,int pageNo,int pageSize, string startDate, string endDate,string sheetType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $@"";
            string condi2 = $@"";
            condi = (startDate==null || endDate==null) ? "": $@" and m.happen_time>='{startDate}' and m.happen_time<='{endDate}'";
            condi2 = sheetType==null ?"": $@" where sheet_type1 in ({sheetType})";
            string sql = $@"

select t.*,(stock_qty-COALESCE(sum(af.quantity*af.inout_flag*af.unit_factor),0)) as af_qty,count(0) over() as row_count
from (
-- 销售退货出库
select m.sheet_no,m.sheet_id,m.supcust_id,ifs.sup_name,m.happen_time,d.unit_no,d.inout_flag,d.item_id,m.sheet_type as sheet_type1,d.unit_factor,
(case when d.inout_flag < 0 then '销售单' else'销售退货单' end) as sheet_type,
-- <0 减少
-- >0 增加
d.inout_flag * d.quantity as qty,
null as from_branch_name,null as to_branch_name
from sheet_sale_detail d
left join sheet_sale_main m on m.company_id = {companyID} and d.sheet_id =m.sheet_id
left join info_supcust ifs on ifs.supcust_id = m.supcust_id and ifs.company_id = {companyID}
where d.company_id = {companyID} and d.item_id = {itemID} and m.branch_id = {branchID} and m.red_flag IS NULL
              AND m.approve_time IS NOT NULL
              and NOT COALESCE(m.is_imported, FALSE)  {condi}

union
-- 采购
select m.sheet_no,m.sheet_id,m.supcust_id,ifs.sup_name,m.happen_time,d.unit_no,d.inout_flag,d.item_id,m.sheet_type as sheet_type1,d.unit_factor,
(case when d.inout_flag < 0 then '采购退货单' else'采购单' end) as sheet_type,
-- <0 减少
-- >0 增加
d.inout_flag * d.quantity as qty,
null as from_branch_name,null as to_branch_name
from sheet_buy_detail d
left join sheet_buy_main m on m.company_id = {companyID} and d.sheet_id =m.sheet_id
left join info_supcust ifs on ifs.supcust_id = m.supcust_id and ifs.company_id = {companyID}
where d.company_id = {companyID} and d.item_id = {itemID} and m.branch_id = {branchID} and m.red_flag IS NULL
              AND m.approve_time IS NOT NULL
              and NOT COALESCE(m.is_imported, FALSE) {condi}
union
-- 调拨出库
select m.sheet_no,m.sheet_id,null as supcust_id,null as sup_name, m.happen_time,d.unit_no,d.inout_flag,d.item_id,'DB' as sheet_type1,d.unit_factor,
(case when m.from_branch_id = {branchID} then '调出单' else '调入单' end) as sheet_type,
(case when m.from_branch_id = {branchID} then (0-d.quantity) else d.quantity end) as qty,
ibf.branch_name as from_branch_name,ibt.branch_name as to_branch_name
from sheet_move_detail d
left join sheet_move_main m on m.sheet_id = d.sheet_id and m.company_id= {companyID}
left join info_branch ibf on ibf.company_id = {companyID} and ibf.branch_id = m.from_branch_id
left join info_branch ibt on ibt.company_id = {companyID} and ibt.branch_id = m.to_branch_id
where d.company_id = {companyID} and d.item_id = {itemID} and (m.from_branch_id = {branchID} or m.to_branch_id = {branchID}) and m.red_flag IS NULL
              AND m.approve_time IS NOT NULL
              and NOT COALESCE(m.is_imported, FALSE)  {condi}

union
-- 盘盈入库（盘点盈余）
select (case when m.inventory_sheet_id is null then  m.sheet_no else sim.sheet_no end) as sheet_no,
(case when m.inventory_sheet_id is null then m.sheet_id else  m.inventory_sheet_id end) as sheet_id,null as supcust_id,null as sup_name,m.happen_time,d.unit_no,d.inout_flag,d.item_id,
(case when m.inventory_sheet_id is null then m.sheet_type else sim.sheet_type end) as sheet_type1,
d.unit_factor,
(case when d.inout_flag >0 then '盘点盈亏单' else '报损单' end) as sheet_type,
-- >0 增加
-- <0 减少
(case when d.inout_flag >0 then d.quantity else -d.quantity end) as qty,
null as from_branch_name,null as to_branch_name
from sheet_invent_change_detail d
left join sheet_invent_change_main m on m.company_id = {companyID} and m.sheet_id = d.sheet_id
left join sheet_inventory_main sim on sim.company_id = {companyID} and sim.sheet_id = m.inventory_sheet_id
where d.company_id = {companyID} and d.item_id = {itemID}
and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null {condi}
     ) t


-- 查询>endDate的数量
left join (
    select 
        sum(quantity) quantity,unit_factor,inout_flag,item_id,m1.happen_time 
    from sheet_sale_detail d left join sheet_sale_main m1 on m1.sheet_id = d.sheet_id and m1.company_id = {companyID} 
    where d.company_id = {companyID} and d.item_id ={itemID} and m1.branch_id = {branchID} and m1.red_flag is null and m1.approve_time is not null and NOT COALESCE(m1.is_imported, FALSE)
     GROUP BY unit_factor,inout_flag,item_id,m1.happen_time
    union
    select 
        sum(quantity) quantity,unit_factor,inout_flag,item_id,m1.happen_time 
    from sheet_buy_detail d left join sheet_buy_main m1 on m1.sheet_id = d.sheet_id and m1.company_id = {companyID}
    where d.company_id = {companyID} and d.item_id ={itemID} and m1.branch_id = {branchID} and m1.red_flag is null and m1.approve_time is not null and NOT COALESCE(m1.is_imported, FALSE)
    GROUP BY unit_factor,inout_flag,item_id,m1.happen_time 
    union
    
    select sum(quantity) quantity,unit_factor,(case when m1.from_branch_id = {branchID} then -1 else 1 end) as inout_flag,item_id,m1.happen_time 
    from sheet_move_detail d left join sheet_move_main m1 on m1.sheet_id = d.sheet_id and m1.company_id = {companyID} 
    where d.company_id = {companyID} and d.item_id ={itemID} and (m1.from_branch_id = {branchID} or m1.to_branch_id = {branchID}) and m1.red_flag is null and m1.approve_time is not null and NOT COALESCE(m1.is_imported, FALSE) 
    group by unit_factor,inout_flag,item_id,m1.happen_time, m1.from_branch_id
    
    union
    select sum(quantity) quantity,unit_factor,inout_flag,item_id,m1.happen_time 
    from sheet_invent_change_detail d left join sheet_invent_change_main m1 on m1.sheet_id = d.sheet_id and m1.company_id = {companyID} 
    where d.company_id = {companyID} and d.item_id ={itemID} and m1.branch_id = {branchID} and m1.red_flag is null and m1.approve_time is not null 
    group by unit_factor,inout_flag,item_id,m1.happen_time 
)af on af.item_id = t.item_id and af.happen_time >t.happen_time
left join stock on stock.company_id = {companyID} and stock.branch_id = {branchID} and stock.item_id = {itemID}
{condi2}
group by t.sheet_no,t.sheet_id,t.supcust_id,t.sup_name,t.happen_time,t.qty,t.inout_flag,t.unit_factor,t.item_id,t.sheet_type1,t.unit_no,sheet_type,from_branch_name,to_branch_name,stock_qty
order by t.happen_time desc
limit {pageSize} offset {(pageNo - 1) * pageSize}
";

            dynamic list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            Dictionary<string, dynamic> data = new Dictionary<string, dynamic>();
            data.Add("result", list);
            int code = 0;
            string msg = "";
            return Json(new { code, msg,data});
        }
        [HttpGet]
        public async Task<JsonResult> GetStockLockChangeDetail(string operKey, string itemID, string branchID, int pageNo, int pageSize, string startDate, string endDate)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $@"
SELECT *,count(0) over() as row_count,sum(add_quantity_b) over() as sum_add_quantity_b,sum(add_quantity_m) over() as sum_add_quantity_m,sum(add_quantity_s) over() as sum_add_quantity_s,sum(reduce_quantity_b) over() as sum_reduce_quantity_b,sum(reduce_quantity_m) over() as sum_reduce_quantity_m,sum(reduce_quantity_s) over() as sum_reduce_quantity_s
FROM 
(
    SELECT sheet_no,sheet_id,from_branch_name,to_branch_name,inventory_sheet_id,sup_name,sheet_type as sheet_type1,
        sheet_type, case when red_flag='1' then '红冲单' when red_flag='2' then '红字单'  else '已审核' end as status,u.item_id as item_id,item_name,'' as before_quantity,
        unit_from_s_to_bms ((case when qty_change>0 then qty_change else 0 end)::float8,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)  as add_quantity,
        yj_get_unit_qty ('b',(CASE WHEN qty_change  > 0 THEN qty_change ELSE 0 END )::numeric,b_unit_factor,m_unit_factor,false) as add_quantity_b,yj_get_unit_qty ('m',(CASE WHEN qty_change  > 0 THEN qty_change ELSE 0 END )::numeric,b_unit_factor,m_unit_factor,false) as add_quantity_m,yj_get_unit_qty ('s',(CASE WHEN qty_change  > 0 THEN qty_change ELSE 0 END )::numeric,b_unit_factor,m_unit_factor,false) as add_quantity_s,
        unit_from_s_to_bms ((case when qty_change<0 then -1*qty_change else 0 end)::float8,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)  as reduce_quantity,
        yj_get_unit_qty ('b',(case when qty_change<0 then -1*qty_change else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as reduce_quantity_b,yj_get_unit_qty ('m',(case when qty_change<0 then -1*qty_change else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as reduce_quantity_m,yj_get_unit_qty ('s',(case when qty_change<0 then -1*qty_change else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as reduce_quantity_s,'' as after_quantity,qty_change as change_qty,happen_time,approve_time,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no 

    FROM
    (
        SELECT t.*,0 stock_qty,round(quantity::numeric,2) qty_change 
        FROM  
        (
            SELECT sup_name,item_id,m.sheet_id,'-1' inventory_sheet_id,sheet_no,sheet_type,sum(quantity*unit_factor) quantity,m.happen_time,m.approve_time,red_flag,'' as from_branch_name,'' as to_branch_name
            FROM sheet_sale_order_main m
            LEFT JOIN sheet_sale_order_detail d on m.company_id=d.company_id and m.sheet_id = d.sheet_id 
            LEFT JOIN sheet_status_order sso on m.company_id = sso.company_id and sso.sheet_id = m.sheet_id 
            LEFT JOIN info_supcust sc on m.company_id=sc.company_id and m.supcust_id = sc.supcust_id 
            WHERE m.company_id={companyID} AND not coalesce(M.is_imported,false) AND M.happen_time >= '{startDate}'  AND M.happen_time <= '{endDate}' and m.branch_id ={branchID} AND d.inout_flag<0
                and (sso.order_status <>'zd'or sso.order_status is null) and (trade_type NOT IN ( 'J', 'H' ) or trade_type is null) and (move_stock is null or move_stock ='f')
            GROUP by sup_name,item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,inventory_sheet_id
        ) t
        WHERE approve_time is not null  and red_flag is null order by happen_time,sheet_id 
    ) u
    LEFT JOIN info_item_prop i on i.company_id={companyID} and i.item_id = u.item_id 
    LEFT JOIN
    (
        SELECT item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no 
        FROM crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                as errr(item_id int, s jsonb,m jsonb,b jsonb)
    ) mu on mu.item_id = i.item_id
    WHERE i.company_id={companyID} and u.item_id={itemID}  order by happen_time 

) t limit {pageSize} offset {(pageNo - 1) * pageSize}";

            dynamic list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            Dictionary<string, dynamic> data = new Dictionary<string, dynamic>();
            data.Add("result", list);
            int code = 0;
            string msg = "";
            return Json(new { code, msg, data });
        }

        [HttpGet]
        public async Task<JsonResult> GetStockChangeSummary(string operKey, string itemID, string branchID,string startDate,string endDate)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $@" and m.happen_time>='{startDate}' and m.happen_time<='{endDate}'";
            string sql = $@"";
            SQLQueue QQ = new SQLQueue(cmd);
            sql = $@"select s->>'f1' as s_unit_no, s->>'f2' as s_unit_factor,m->>'f1' as m_unit_no, m->>'f2' as m_unit_factor,b->>'f1' as b_unit_no, b->>'f2' as b_unit_factor
from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor)) as json from info_item_multi_unit where company_id = {companyID} and item_id = {itemID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)";
            QQ.Enqueue("getUnit", sql);
            sql = $@"select sum(quantity*unit_factor*inout_flag) as sum,'X' as sheet_type,'销售单' as sheet_type_name from sheet_sale_detail d left join sheet_sale_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null and NOT COALESCE(m.is_imported, FALSE) and d.inout_flag < 0 {condi}
    union
    select sum(quantity*unit_factor*inout_flag) as sum,'T' as sheet_type,'销售退货单' as sheet_type_name from sheet_sale_detail d left join sheet_sale_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null and NOT COALESCE(m.is_imported, FALSE) and d.inout_flag > 0 {condi}
    union
    select sum(quantity*unit_factor*inout_flag) as sum,'CG' as sheet_type,'采购单' as sheet_type_name from sheet_buy_detail d left join sheet_buy_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null and NOT COALESCE(m.is_imported, FALSE) and d.inout_flag > 0 {condi}
    union
    select sum(quantity*unit_factor*inout_flag) as sum,'CT' as sheet_type,'采购退货单' as sheet_type_name from sheet_buy_detail d left join sheet_buy_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null and NOT COALESCE(m.is_imported, FALSE) and d.inout_flag < 0 {condi}
    union
    select sum(quantity*unit_factor*(case when m.from_branch_id = {branchID} then -1 else 1 end))as sum,'DB' as sheet_type,'调拨单' as sheet_type_name from sheet_move_detail d left join sheet_move_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and (m.from_branch_id = {branchID} or m.to_branch_id = {branchID}) and m.red_flag is null and m.approve_time is not null and NOT COALESCE(m.is_imported, FALSE) {condi}
    union
    select sum(quantity*unit_factor*inout_flag)as sum,'YK' as sheet_type,'盘点盈亏单' as sheet_type_name from sheet_invent_change_detail d left join sheet_invent_change_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null {condi}";
            QQ.Enqueue("getSummary", sql);
            sql = $@"
select (stock_qty - COALESCE(t1.qty,0)) as begin_qty,(stock_qty - COALESCE(t2.qty,0))  as end_qty  from stock st 
left join (
select sum(qty)as qty,item_id from (
		select sum(quantity*unit_factor*inout_flag)as qty,item_id from sheet_sale_detail d left join sheet_sale_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null and NOT COALESCE(m.is_imported, FALSE) and m.happen_time>='{startDate}' group by item_id 
    union
    select sum(quantity*unit_factor*inout_flag) as qty,item_id from sheet_buy_detail d left join sheet_buy_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null and NOT COALESCE(m.is_imported, FALSE) and m.happen_time>='{startDate}' group by item_id
    union
    select sum(quantity*unit_factor*(case when m.from_branch_id = {branchID} then -1 else 1 end))as qty,item_id from sheet_move_detail d left join sheet_move_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and (m.from_branch_id = {branchID} or m.to_branch_id = {branchID}) and m.red_flag is null and m.approve_time is not null and NOT COALESCE(m.is_imported, FALSE) and m.happen_time>='{startDate}' group by item_id
    union
    select sum(quantity*unit_factor*inout_flag)as qty,d.item_id from sheet_invent_change_detail d left join sheet_invent_change_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} 
		and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null and m.happen_time>='{startDate}' group by item_id
) t group by item_id
) t1 on t1.item_id=st.item_id
left join (
		select sum(inout_flag * unit_factor * quantity) as qty,item_id from (
				select quantity,unit_factor,inout_flag,d.item_id from sheet_sale_detail d left join sheet_sale_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null and NOT COALESCE(m.is_imported, FALSE) and m.happen_time>'{endDate}'
    union
    select quantity,unit_factor,inout_flag,d.item_id from sheet_buy_detail d left join sheet_buy_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null and NOT COALESCE(m.is_imported, FALSE) and m.happen_time>='{endDate}'
    union
    select quantity,unit_factor,(case when m.from_branch_id = {branchID} then -1 else 1 end) as inout_flag,d.item_id from sheet_move_detail d left join sheet_move_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and (m.from_branch_id = {branchID} or m.to_branch_id = {branchID}) and m.red_flag is null and m.approve_time is not null and NOT COALESCE(m.is_imported, FALSE) and m.happen_time>='{endDate}'
    union
    select quantity,unit_factor,inout_flag,d.item_id from sheet_invent_change_detail d left join sheet_invent_change_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID} where d.company_id = {companyID} and d.item_id ={itemID} and m.branch_id = {branchID} and m.red_flag is null and m.approve_time is not null and m.happen_time>='{endDate}' 
   ) t group by item_id
) t2 on t2.item_id =st.item_id
where st.company_id = {companyID} and st.branch_id = {branchID} and st.item_id = {itemID}
";
            QQ.Enqueue("getBeginEnd", sql);
            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> summaryInfo = null;
            List<ExpandoObject> beginEndInfo = null;
            dynamic unitInfo = null;
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "getSummary")
                {
                    summaryInfo = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "getBeginEnd")
                {
                    beginEndInfo = CDbDealer.GetRecordsFromDr(dr, false);
                } else if (sqlName == "getUnit")
                {
                    unitInfo = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            int code = 0;
            string msg = "";
            return Json(new { code, msg, summaryInfo, beginEndInfo, unitInfo });
        }


    }
}
