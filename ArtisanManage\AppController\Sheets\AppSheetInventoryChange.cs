﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.YingJiangBackstage.Pojo;
using Newtonsoft.Json;

namespace ArtisanManage.AppController.Sheets
{ 
    [Route("AppApi/[controller]/[action]")]
    public class AppSheetInventoryChange : BaseController
    { 
        public AppSheetInventoryChange(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        /// <summary>
        /// 加载盘点单--
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SheetInventChange sheet = new SheetInventChange(SheetInventChange.IS_REDUCE.IS_REDUCE, LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"
SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt 
left join info_attribute attr on opt.attr_id = attr.attr_id 
where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index";
            QQ.Enqueue("attr_options", sql);
            
            List<ExpandoObject> attrOptions = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "attr_options")
                {
                    attrOptions = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet, attrOptions});
        }
        
        /// <summary>
        /// 提交盘点单
        /// </summary>
        /// <param name="sheet">
        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic dSheet)
        {
            SheetInventChange sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetInventChange>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in SheetInventChange.Save:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            if (msg == "")
            {
                cmd.ActiveDatabase = "";
                sheet.Init();
                msg = await sheet.Save(cmd);
            }
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.make_time, sheet.happen_time, currentTime });
        }
        
        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="dSheet"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            SheetInventChange sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetInventChange>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Submit:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }

            sheet.Init();
            msg = await sheet.SaveAndApprove(cmd);
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no,sheet.approve_time, currentTime });
        }

        
        /// <summary>
        /// 红冲
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="sheetID"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string sheet_id = data.sheetID;
            string operKey = data.operKey;
            string redBrief = data.redBrief;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetInventChange sheet = new SheetInventChange(SheetInventChange.IS_REDUCE.IS_REDUCE, LOAD_PURPOSE.SHOW);
            string msg = "";
            dynamic sheet_ids = await CDbDealer.Get1RecordFromSQLAsync($"select inventory_sheet_id from sheet_invent_change_main where company_id = {companyID} and sheet_id= {sheet_id} and red_flag is null and approve_time is not null", cmd);
            if (sheet_ids.inventory_sheet_id != "") msg = "红冲失败，该单据由盘点单生成，请红冲对应盘点单";
            if (msg == "") msg = await sheet.Red(cmd, companyID, sheet_id, operID,redBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetInventChange sheet = new SheetInventChange(SheetInventChange.IS_REDUCE.IS_REDUCE, LOAD_PURPOSE.SHOW);
            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }
        
        [HttpGet]
        public async Task<JsonResult> GetItemList(string operKey, string searchStr, string brandIDs, string classID, int pageSize, int startRow, string branchID, bool showStockOnly, string addItemIds)
        { 
            bool firstRequest = false;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            
            string condi = $" where ip.company_id = {companyID} and (ip.status is null or ip.status='1')";

            if (string.IsNullOrEmpty(addItemIds))
            {
                condi += " and son_mum_item is NULL ";
            }
            else
            {
                condi += @$" and ip.item_id::text IN (SELECT unnest(string_to_array('{addItemIds}', ',')))";
            }
            if (searchStr != null)
            {
                string b = "%";
                if (searchStr.Length >= 6)
                {
                    b = "";
                }
                string flexStr = CPubVars.GetFlexLikeStr(searchStr);
                condi += $"and (ip.item_name ilike '%{flexStr}%' or ip.py_str ilike '%{searchStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%'  or ip.mum_attributes::text ilike '%{searchStr}%' or (t.s->>'f3') like '%{searchStr}{b}' or  (t.b->>'f3') like '%{searchStr}{b}' or (t.m->>'f3') like '%{searchStr}{b}')";
            }
            if (brandIDs != null && brandIDs != "") condi += $"and (ip.item_brand is null OR ip.item_brand in ({brandIDs})) ";
            if (classID != null) condi += $" and ip.other_class like '%/{classID}/%' ";
            if (showStockOnly) condi += $" and stock_qty > 0 ";
            string showStockOnlyCondi = "";
            if (showStockOnly) showStockOnlyCondi += $" and stock_qty>0 ";
            if (startRow == 0) firstRequest = true;
            SQLQueue QQ = new SQLQueue(cmd);
            string sql_noLimit = @$"
select 
       item_id,
       item_name,
       item_images,
       mum_attributes,
       stock_qty,
       b_wholesale_price,m_wholesale_price,s_wholesale_price,
        b_buy_price,m_buy_price,s_buy_price,
       cost_price_avg,
       cost_price_spec,
       s_buy_price cost_price_buy, 
       cost_price_recent,
        recentpricetime,

       item_order_index,
       batch_level,
       yj_get_bms_qty(stock_qty,bunit,b_unit_factor::float4,munit,m_unit_factor::float4,sunit) as current_qty,
       (case when m_unit_factor is null and b_unit_factor is not null then concat(s_unit_factor,bunit,'=',b_unit_factor,sunit)  
           when (b_unit_factor is not null) and (m_unit_factor is not null) then concat(s_unit_factor,bunit,'=',floor(b_unit_factor::numeric/m_unit_factor::numeric),munit,'=',b_unit_factor,sunit)
           when b_unit_factor is null then concat(s_unit_factor,sunit)  end ) as unit_conv,
       bUnit as b_unit_no,munit as m_unit_no,sunit as unit_no,branch_id,s_barcode,b_barcode,m_barcode,m_unit_factor,b_unit_factor,bstock,mstock,sstock  
from(
        SELECT ip.item_id, ip.item_name,mum_attributes,stock_qty,item_order_index,ip.wholesale_price,ip.cost_price_avg,ip.cost_price_spec,ip.cost_price_recent,
         s.setting->>'recentPriceTime' as recentpricetime,
                    stock.branch_id,item_images,ip.py_str,ip.py_str1,ip.item_no,ip.batch_level,
                        (case when b is not null then sign(COALESCE(stock.stock_qty,0))*floor(COALESCE(abs(stock.stock_qty),0) / (t.b->> 'f1')::numeric) else null end ) as bStock,
                        (CASE WHEN(t.m->>'f1') is null THEN null ELSE sign(COALESCE(stock.stock_qty,0))*floor((COALESCE(abs(stock.stock_qty),0)%(t.b->>'f1')::numeric)/(t.m->>'f1')::numeric) END) as mStock,
                        (CASE WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NOT NULL THEN sign(stock.stock_qty)*floor(COALESCE(abs(stock.stock_qty),0)%(t.b->>'f1')::numeric%(t.m->>'f1')::numeric)
			             WHEN(t.b->>'f1') is NOT NULL AND(t.m->>'f1') is NULL THEN round(COALESCE(stock.stock_qty,0) % (t.b->> 'f1')::numeric)
                         WHEN(t.b->>'f1') is NULL AND (t.m->>'f1') is NULL THEN round(COALESCE(stock.stock_qty,0)) END) sStock,
                        (t.s->>'f1') as s_unit_factor, (t.s->> 'f2') as sUnit, t.s->>'f3' s_barcode,s->>'f4' as s_wholesale_price,t.s->>'f6' s_buy_price,
                        (t.b->>'f1') as b_unit_factor, (t.b->> 'f2') as bUnit, t.b->>'f3' b_barcode,b->>'f4' as b_wholesale_price,t.b->>'f6' b_buy_price,
                        (t.m->>'f1') as m_unit_factor, (t.m->> 'f2') as mUnit, t.m->>'f3' m_barcode,m->>'f4' as m_wholesale_price,t.m->>'f6' m_buy_price
        FROM info_item_prop as ip
                LEFT JOIN
                (select item_id, s, m, b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,wholesale_price,retail_price,buy_price)) as json from info_item_multi_unit where company_id = {companyID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) 
                 as errr(item_id int, s jsonb,m jsonb, b jsonb)) t
                 on ip.item_id = t.item_id
        LEFT JOIN 
        (
                select s.company_id,case when son_mum_item is null then s.item_id else son_mum_item end item_id,branch_id,sum(stock_qty - COALESCE(sell_pend_qty,0)) as stock_qty from stock s
                LEFT JOIN info_item_prop ip on ip.company_id ={companyID} and s.item_id = ip.item_id
                 where s.company_id = {companyID} and branch_id = {branchID} {showStockOnlyCondi}
                 GROUP BY s.company_id ,case when son_mum_item is null then s.item_id else son_mum_item end,branch_id
    
        ) stock on t.item_id = stock.item_id
        left join company_setting s on s.company_id = {companyID}
        LEFT JOIN (select * from info_item_class where company_id = {companyID}) as ic on ip.item_class = ic.class_id {condi}
        
) tem ";
            
            var sql = sql_noLimit + $" order by item_order_index,item_name limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);
            if (firstRequest)
            {
                sql = $"select count(*) as itemCount from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
            }
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count"&&firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
            }
            foreach(dynamic rec in data)
            {
		 
				float cost_price_recent1;
				float cost_price_recent2;
				float cost_price_recent3;
				dynamic pStr = JsonConvert.DeserializeObject(rec.cost_price_recent);
				//Console.WriteLine("输出转换后的值：{0}" + "\n" + "转换后的类型：{1} " + "\n", pStr, pStr.GetType());
				if (pStr != null)
				{
					if (pStr.avg1 != null)
					{
						cost_price_recent1 = pStr.avg1;
					}
					else
					{
						cost_price_recent1 = 0;
					}
					if (pStr.avg2 != null)
					{
						cost_price_recent2 = pStr.avg2;
					}
					else
					{
						cost_price_recent2 = 0;
					}
					if (pStr.avg3 != null)
					{
						cost_price_recent3 = pStr.avg3;
					}
					else
					{
						cost_price_recent3 = 0;
					}
				}
				else
				{
					cost_price_recent1 = 0;
					cost_price_recent2 = 0;
					cost_price_recent3 = 0;
				}
				string recent_price_time;
				recent_price_time = rec.recentpricetime;
				if (recent_price_time == "1")
				{
					rec.cost_price_recent = CPubVars.ToDecimal(cost_price_recent1);
				}
				else if (recent_price_time == "2")
				{
					rec.cost_price_recent = CPubVars.ToDecimal(cost_price_recent2);
				}
				else if (recent_price_time == "3")
				{
					rec.cost_price_recent = CPubVars.ToDecimal(cost_price_recent3);
				}
				rec.cost_price_recent = rec.cost_price_recent.ToString();
		 
			}
			string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, itemCount });
        }
        
        
        [HttpPost]
        public async Task<JsonResult> GetFillItemsFromSale([FromBody] dynamic param)
        {
            string operKey = param.operKey;
            string startTime = param.startTime;
            string endTime = param.endTime;
            string sheetType = param.sheetType;
            string briefs = param.briefs;
            string branch_id = param.branch_id;

            string mainDB = "";
            string detailDB = "";
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string err = "";
            if (string.IsNullOrEmpty(startTime) || string.IsNullOrEmpty(endTime))
            {
                err = "时间未选择";
            }

            if (!string.IsNullOrEmpty(err))
            {
                return Json(new ResultUtil<dynamic>().CommonResult(-1, err, null));
            }
            string condi = "";
            
            if (!string.IsNullOrEmpty(briefs))
            {
                condi += @$" and EXISTS (SELECT 1 FROM unnest(string_to_array('{briefs}', ',')) AS brief  WHERE sd.remark LIKE '%' || brief || '%')";
            }
            if ("T".Equals(sheetType))
            {
                mainDB = "sheet_sale_main";
                detailDB = "sheet_sale_detail";
                condi += @$" and (sheet_type='T' or quantity<0) and sm.branch_id = {branch_id} ";
                
            } else if ("DB".Equals(sheetType))
            {
                mainDB = "sheet_move_main";
                detailDB = "sheet_move_detail";
                condi += @$" and sm.to_branch_id = {branch_id}  ";
            }
            string sql =  @$"
select 	tb.item_id,
        ip.item_name,
        ip.item_class as classId,
        s_qty,
        s->>'f1' as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_wholesale_price,s->>'f4' as s_barcode,
        m->>'f1' as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_wholesale_price,m->>'f4' as m_barcode,
        b->>'f1' as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_wholesale_price,b->>'f4' as b_barcode
from
(
   select 
       sd.item_id,
       sum(abs(sd.quantity * sd.unit_factor))as s_qty 
   from {detailDB} sd 
   left join {mainDB} sm on sd.sheet_id=sm.sheet_id and sd.company_id = sm.company_id
   where sd.company_id = {companyID} 
        {condi}
        and approve_time is not null 
        and red_flag is null 
        and sd.happen_time >= '{startTime}' 
        and sd.happen_time<='{endTime}' 
        and sm.happen_time >= '{startTime}' 
        and sm.happen_time<='{endTime}' 
   group by sd.item_id
) tb
left join info_item_prop ip on ip.company_id={companyID} and tb.item_id=ip.item_id
left join (select item_id,s,m,b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,barcode)) as json from info_item_multi_unit 
                                    where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)) unit on ip.item_id=unit.item_id
            ";
            try
            {
                List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                return Json(new ResultUtil<dynamic>().CommonResult(0, "success" ,data));
            }
            catch (Exception e)
            {
                return Json(new ResultUtil<dynamic>().CommonResult(-1, "获取商品失败", null));
            }
        }
    }
}

