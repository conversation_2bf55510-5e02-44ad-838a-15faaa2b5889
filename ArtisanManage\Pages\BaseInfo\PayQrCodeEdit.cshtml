@page
@model ArtisanManage.Pages.BaseInfo.PayQrCodeEditModel
@{
    Layout = null;
} 
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>PayQrCodeEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <style>
        #upload_img img {
            transition:all 0.5s; 
        }
        #upload_img img:hover{
            transform:scale(1.1);
        }
    </style>
    <script type="text/javascript">
        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
             @Html.Raw(Model.m_showFormScript)
             @Html.Raw(Model.m_createGridScript)

            $('#sub_id .row-oper').remove();
            $('#sub_code .row-oper').remove();
            $('#mother_code .row-oper').remove();
            if ($('#qrcode_id').val() == '') {
                $('#sub_id').attr("title", "清空可输入新账户名");
            }else{
                $('#mother_code').jqxInput({ disabled: true });
                $('#sub_id').jqxInput({ disabled: true });
            }

            $("#sub_id").on('change', function (event) {
                let args=event.args;
                if(args.type=="mouse"){
                    $('#sub_code').val(args.owner.selectedItem.c);
                    $('#status').val({ value: args.owner.selectedItem.s, label: (args.owner.selectedItem.s=='1'?'正常':'停用') });
                }else{
                    $('#sub_code').val('');
                    $('#status').val({value:'1', label:'正常'});
                }
            });

            var qrcode_uri = $('#qrcode_uri').val();
            if (qrcode_uri!=''){
                $('#photo_img').attr('src', `@Model.HWhref` + qrcode_uri);
                $('#upload_img').hide();
                $('#uploaded_img').show();
            }

            $("#upload-image").on('change',function (event) {
                var fileDom = document.getElementById('upload-image');
                var showDom = document.getElementById('photo_img');
                var reads = new FileReader();
                var file = fileDom.files[0];
                //console.log(file);
                reads.readAsDataURL(file);
                reads.onload = function (e) {
                    //console.log(e);
                    showDom.src = e.currentTarget.result;
                    $('#qrcode_uri').val(showDom.src);
                    $('#upload_img').hide();
                    $('#uploaded_img').show();
                    console.log('upload photo');
                }
            });
		
        });
       
        function addImage() {
            $("#upload-image").trigger("click");
        }

        function removePhoto(){
            $('#qrcode_uri').val('');
            $('#uploaded_img').hide();
            $('#upload_img').show();
        }

    </script>
</head>
<body>
    <div id="divHead" class="headtail"></div> 
    <div> 
        <div style="display:flex;">
            <div id="upload_img" onclick="addImage()" style="margin-top:5px; width:80px; height:80px; text-align: center; cursor: pointer; display:block; position:absolute; left:calc(50% - 40px);">
                <img id="add_img" src="/images/addPayQrcode.svg" style="width:80px; height:80px; display:inline-block;" >
                &nbsp; &nbsp;<span style="font-size:14px;position:absolute;left:calc(50% - 35px);color:darkgray;">添加二维码</span>
            </div>
            <div id="uploaded_img" style="margin-top:5px; width:110px; height:100px; text-align: center; cursor: pointer; display:none; position:absolute; left:calc(50% - 50px);">
                <img id="photo_img" style="width:100px; height:100px; display:inline-block;position:absolute;left:0;" />
                <span onclick="removePhoto()" style="font-size:16px;display:inline-block;float:right;">×</span>
            </div>
        </div>
    </div>
    <form class="input-block" id="formMainImage">
        <input id="upload-image" type="file" name="file" accept="image/png,image/bmp,image/jpeg" style=" display:none" />
    </form>

    <div style="text-align:center;position:absolute;bottom:20px;left:calc(50% - 100px);">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;">保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>
