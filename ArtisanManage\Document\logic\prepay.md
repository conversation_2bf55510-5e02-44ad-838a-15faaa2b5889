
## 预收款逻辑

  先通过预收款单收客户钱，在开销售单时支付方式可以选择预收款账户，预收款余额将会扣减
吧

### 涉及页面

1. 预收款单(WEB/APP)
   
2. 查看预收款单(WEB/APP)
  
4. 预收款余额(WEB/APP)

5. 客户往来张(WEB)

6. 客户往来账时间段汇总(WEB)


### 系统设置/权限相关

#### 1. 权限控制预收款是否可为负



### 表结构

 sheet_price_adjust_main/sheet_price_adjust_detail 

```sql
create table sheet_prepay(company_id integer not null,visit_id integer,sheet_id serial not null,red_sheet_id integer,sheet_no text,sheet_type text,supcust_id integer,getter_id integer,total_amount float,prepay_sub_id integer, approve_flag smallint,red_flag smallint,order_sheet_id integer, maker_id integer,make_time timestamp,approver_id integer,approve_time timestamp,happen_time timestamp,payway1_id integer,payway1_amount float8,payway2_id integer,payway2_amount float8,make_brief text,submit_time timestamp,disc_amount float,money_inout_flag int2,approve_brief text,now_pay_amount numeric,now_disc_amount,paid_amount numeric,order_adjust_sheet_id integer) partition by range(happen_time);
 
sheet_prepay：



create table prepay_balance(company_id integer not null,supcust_id integer,sub_id integer,balance float,init_balance float8,init_time timestamp,constraint pk_prepay_balance primary key(company_id,supcust_id,sub_id));
 
create table client_account_history(company_id integer not null,flow_id serial,supcust_id integer, happen_time timestamp,sheet_type text,sub_type text,sheet_id integer,sub_id integer,change_amount float4,now_balance float4,red_flag integer) partition by range(happen_time);


```
##### sheet_prepay
| 字段名     | 字段类型 | 是否可空                 | 字段注释     | 字段说明                                                     |
| ---------- | -------- | ------------------------ | ------------ | ------------------------------------------------------------ |
| visit_id | integer    |    | 拜访记录的ID     |   |
| supcust_id | integer  | 否 | 客户ID           |   |

### 版本记录

| 版本  | 时间             | 修改状态                                                     | 相关人员 |
| ----- | ---------------- | ------------------------------------------------------------ | -------- |
| 1.0.0 | 2021.12.29 16:50 | 初步完成，等待研发自我测试 :heavy_check_mark:                | 研发：刘 |
| 1.0.1 | 2021.12.29 晚上  | 研发自我测试，页面小bug完善:heavy_check_mark:                | 测试：刘 |
| 1.0.1 | 2021.12.30 9:34  | 逻辑bug调整优化，添加调整商品记录表优化查询效率，修复使用商品时存在逻辑bug:heavy_check_mark: | 测试：相 |
 

