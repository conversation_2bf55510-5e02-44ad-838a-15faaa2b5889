﻿@page
@model ArtisanManage.Pages.Report.AttendanceReportAllViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.addEventListener('message', function (rs) {
            console.log(rs)
            if (rs.data.msgHead === "AttendanceSettingEdit") {
                console.log($("#popItem"))
                $('#popItem').jqxWindow('close');
            }
        })
        $(document).ready(function () {
         
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
                QueryData();
        })
    </script>
</head>

<script type="text/javascript" src="../wwwroot/jqwidgets/jqwidgets/jqxtabs.js"></script>
<body class='default' style="overflow:hidden;">
    <style>
        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }
    </style>


    <div id="divHead" class="headtail" style="width:calc(100% - 110px);">

        <div style="float:none;height:0px; clear:both;"></div>

    </div>
    <div id="operbar">
        <button onclick="QueryData()" style="margin-left:20px;margin-bottom:10px;" class="main-button">查询</button>
        <button @@click="handleExportAttendance">导出</button>
    </div>
    
    <div style="flex-grow:1;display:flex;width:100%;height:100%;">
        <div style="width:calc(100% - 200px);height:100%; margin-left:10px;">
            <div><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>
            <div id="gridItems" style="margin-top:0px;margin-left:10px; margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 20px);"></div>
        </div>
    </div>
    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">客户信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
<script src="/js/Vue.js"></script>
<script src="/js/FileSaverVue.js"></script>
<script src="/js/Blob.js"></script>
<script src="/js/jszip.js"></script>
<script src="/js/ods.js"></script>
<script src="/js/cpexcel.js"></script>
<script src="/js/xlsx.full.min.js"></script>
<script src="/js/xlsx-style.js"></script>
<script src="/js/Export2Excel.js?v=9.559"></script>
<script src="/js/YJExportSheet.js?v=9.559"></script>
<script src="/js/site.js"></script>
<script>
    var app = new Vue({
        el: "#operbar",
        data() {
            return {
                msg: "123"
            }
        },
        mounted() {
            console.log("我加载了！")
        },
        methods: {
            // 导出考勤报表
            handleExportAttendance() {
                this.getAttendanceData((rows) => {
                    this.handleExportAttendanceExcel(rows)
                })
            },
            handleExportAttendanceExcel(attendanceRows) {
                // excel表头
                let header = ["业务员", "正常天数", "迟到天数", "早退天数", "请假天数", "休息天数", "缺勤天数", "未签退"]
                // 请求数据的字段
                const filterVal = ['oper_name', 'zc_count', 'cd_count', 'zt_count', 'qj_count', 'xx_count', 'qq_count', 'wqt_count']
                // 数据来源
                const list = attendanceRows
                console.log(attendanceRows)
                // 拼接数据
                debugger
                const data = this.formatJson(filterVal, attendanceRows)
                const selectStart = $('#start_date').jqxDateTimeInput('val');
                const selectEnd = $('#end_date').jqxDateTimeInput('val');

                // 将日期转换为 yyyy-mm-dd 格式
                const formatDate = (date) => {
                    date = new Date(date)
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要加 1
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                };

                const startDateFormatted = formatDate(selectStart);
                const endDateFormatted = formatDate(selectEnd);
                export_json_to_excel(header, data, startDateFormatted + '-' + endDateFormatted + '_' + '考勤报表')
            },
            // 拼接数据
            formatJson(filterVal, jsonData) {
                return jsonData.map(v => filterVal.map(j => {
                    return v[j]
                }))
            },
            // 获取考勤数据
            getAttendanceData(callBack) {
                debugger
                const selectStart = $('#start_date').jqxDateTimeInput('val');
                const selectEnd = $('#end_date').jqxDateTimeInput('val');
                $.ajax({
                    url: `/api/AttendanceReportAllView/GetQueryRecords?operKey=@Model.OperKey&start_date=${selectStart}&end_date=${selectEnd}`,
                    success: (res) => {
                        callBack(res.rows)
                    }
                })
            }

        }
    })
</script>
</html>