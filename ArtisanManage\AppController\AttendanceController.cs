﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    [Route("AppApi/[controller]/[action]")]
    public class AttendanceController : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public AttendanceController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }
        [HttpPost]
        public async Task<JsonResult> SaveSign([FromBody] dynamic param)
        {
            Security.GetInfoFromOperKey((string)param.operKey, out string companyID, out string operID);
            string flowId = param.flowId;
            if (flowId.IsValid())
            {
                await this.signOutAttendance(param, flowId, operID, companyID);
                return Json(new { });

            }
            else
            {
                var flowID = await this.signInAttendance(param, operID, companyID);
                return Json(new { flowID });
            }

        }
        [HttpPost]
        public async Task<JsonResult> GetAttendanceRecords([FromBody] dynamic param)
        {
            Security.GetInfoFromOperKey((string)param.operKey, out string companyID, out string operID);
            string staffID = param.operID.ToString();
            if (staffID.IsValid())
            {
                operID = param.operID.ToString();
            }
            SQLQueue QQ = new SQLQueue(cmd);
            int year = param.year;
            int month = param.month;

            string sql = $@"SELECT flow_id,oper_id,start_time,end_time,group_id,addr,longitude,latitude,in_pic,out_pic,status FROM attence_record where (start_time BETWEEN '{year}-{month}-01' AND '{year}-{month}-{DateTime.DaysInMonth(year, month)}') AND oper_id='{operID}' AND company_id='{companyID}'";
            QQ.Enqueue("attendanceRecords", sql);
            dynamic attendanceRecords = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "attendanceRecords")
                {
                    attendanceRecords = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            return Json(new { attendanceRecords });

        }
        public async Task<dynamic> signInAttendance(dynamic param, string operID, string companyID)
        {
            dynamic photos = param.photos;
            List<string> photosInDB = new List<string>();
            foreach (string photo in photos)
            {
                string photo_db_string = await process_sign_pic_return_dbstring(photo, operID, companyID, "in");
                photosInDB.Add($@"""{photo_db_string}""");
            }
            string photosInDBString = "[";
            photosInDBString += String.Join(",", photosInDB);
            photosInDBString += "]";
            string sql = @$"INSERT INTO attence_record(company_id,oper_id,start_time,group_id,addr,longitude,latitude,in_pic,status,schedule_id) VALUES({companyID},'{operID}','{DateTime.Now}','{param.groupId}','{param.addr}','{param.longitude}','{param.latitude}','{photosInDBString}','{param.status}','{param.schedule_id}')" + " returning flow_id";
            sql = $@"SELECT yj_exeSqlByInsertedRowID('{sql.Replace("'", "''")}','','@flow_id');";

            cmd.CommandText = sql;
            dynamic data = await cmd.ExecuteScalarAsync();
            return data;
        }
        public async Task signOutAttendance(dynamic param, string flowID, string operID, string companyID)
        {
            dynamic photos = param.photos;
            List<string> photosInDB = new List<string>();
            foreach (string photo in photos)
            {
                string photo_db_string = await process_sign_pic_return_dbstring(photo, operID, companyID, "out");
                photosInDB.Add($@"""{photo_db_string}""");
            }
            string photosInDBString = "[";
            photosInDBString += String.Join(",", photosInDB);
            photosInDBString += "]";
            string sql = @$"UPDATE attence_record SET out_pic='{photosInDBString}',status='{param.status}',end_time='{DateTime.Now}' WHERE flow_id='{flowID}' AND company_id='{companyID}' AND oper_id='{operID}'";
            cmd.CommandText = sql;
            await cmd.ExecuteScalarAsync();
        }
        public async Task<string> process_sign_pic_return_dbstring(string signPictureBase64, string operID, string companyID, string type)
        {
            if (signPictureBase64.Length == 0)
            {
                return String.Empty;
            }
            signPictureBase64 = signPictureBase64.Replace("data:image/jpeg;base64,", "");
            string folderPath = $"uploads/{DateTime.Today:yyyyMM}/";
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }
            string signPictureFileName = $"sign_{type}_{operID}_{companyID}_{CommonTool.GetTimeStamp()}";
            string fileExtension = ".jpeg";
            string signPictureIndb = $"/{DateTime.Today:yyyyMM}/{signPictureFileName}{fileExtension}";
            using (MemoryStream stream = new MemoryStream(Convert.FromBase64String(signPictureBase64)))
            {
                string path = folderPath + signPictureFileName + fileExtension;
                string err = await HuaWeiObs.Save(_httpClientFactory, stream, path);
                if (err != "")
                {
                    return "";
                }
            } 
            return signPictureIndb;
        }
        [HttpPost]
        public async Task<JsonResult> GetMyGroupInfo([FromBody] dynamic param)
        {
            Security.GetInfoFromOperKey((string)param.operKey, out string companyID, out string operID);
            string staffID = param.operID.ToString();
            if (staffID.IsValid())
            {
                operID = staffID;
            }
            Attendance attendance = new Attendance(cmd);
            dynamic groupInfoRes = await attendance.GetGroupInfo(param, operID, companyID);
            var groupInfoData = groupInfoRes.data;
            return Json(new { 
              data = groupInfoData
            });
        }
        [HttpPost]
        public async Task<JsonResult> GetTodayAttendanceInfos([FromBody] dynamic param)
        {
            Security.GetInfoFromOperKey((string)param.operKey, out string companyID, out string operID);
            string staffID = param.operID.ToString();
            if (staffID.IsValid())
            {
                operID = staffID;
            }
            Attendance attendance = new Attendance(cmd);
            dynamic todayAttendanceInfoRes = await attendance.GetTodayAttendanceInfos(operID, companyID);
            var todayAttendanceInfo = todayAttendanceInfoRes.data;
            return Json(new { 
               data = todayAttendanceInfo
            });
        }
        /**
         * 逐渐废弃
         */
        [HttpPost]
        public async Task<JsonResult> GetGroupInfo([FromBody] dynamic param)
        {
            Security.GetInfoFromOperKey((string)param.operKey, out string companyID, out string operID);
            string staffID = param.operID.ToString();
            if (staffID.IsValid())
            {
                operID = staffID;
            }
            Attendance attendance = new Attendance(cmd);
            dynamic res = await attendance.GetGroupInfoAndTodayAttendanceInfo(param, operID, companyID);
            var groupInfoData = res.groupInfoData;
            var todayAttendanceInfo = res.todayAttendanceInfo;
            return Json(new { groupInfoData, todayAttendanceInfo });
        }

    }
}
