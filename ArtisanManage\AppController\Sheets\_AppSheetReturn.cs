﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.AppController.Sheets
{

    /*
    [Route("AppApi/[controller]/[action]")]
    public class AppSheetReturn:Controller
    {
        CMySbCommand cmd;
        public AppSheetReturn(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }



        /// <summary>
        /// 加载单据--返回--支付方式,备注
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID"></param>
        /// <returns></returns>


        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SheetSale sheet = new SheetSale(SHEET_RETURN.IS_RETURN, LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            var sql = $"select sub_id,sub_name from info_pay_way where company_id = {companyID} order by payway_index;";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("subInfo", sql);
            sql = $@"select brief_id,brief_text from info_sheet_main_brief where company_id = {companyID}; ";
            QQ.Enqueue("briefInfo", sql);
            List<ExpandoObject> payways = null;
            List<ExpandoObject> brief = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "subInfo")
                {
                    payways = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "briefInfo")
                {
                    brief = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet, payways, brief });
        }


        /// <summary>
        /// 提交退货
        /// </summary>
        /// <param name="sheet">
        /// {"operKey":"wcAqiAdqGYG39sTafoxzNuV7gjl0d-zEX5Q5vIEsZ4CJBL8L71cPvCkNmSBpbvSukmnIwUZFvIg~", "sheet_no":"","sheet_id":"","supcust_id":"1","sup_name":"飞哥战队","sheetType":"T",
        /// "branch_id":"1","branch_name":"主仓库", "happen_time":"","make_brief":"","total_amount":"72","now_disc_amount":"0","payway1_id":"1","payway1_amount":"72","payway2_id":"","payway2_name":"","payway2_amount":"0",
        /// "left_amount":"0", "maker_id":"","maker_name":"","make_time":"","approver_id":"","approver_name":"","approve_time":"","now_pay_amount":72,"paid_amount":72,"shop_id":1,
        /// "SheetRows":[{"item_id":"6","item_name":"你好6","unit_no":"箱","real_price":"9","quantity":"8","unit_factor":"8","sub_amount":"72.00"}]}</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] SheetSale sheet)
        {
            var currentTime = DateTime.Now.ToText();
            sheet.Init();
            string msg = await sheet.SaveAndApprove(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no,currentTime });
        }






        /// <summary>
        /// 商品档案列表----返回商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="searchStr">商品名，助记码，商品编号，商品条码 模糊查询</param>
        /// <param name="brandID">品牌ID查询</param>
        /// <param name="classID">分类ID查询</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="branchID">仓库名 （1）</param>
        /// <returns>商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数</returns>


        [HttpGet]
        public async Task<JsonResult> GetItemList(string operKey, string searchStr, string brandID, string classID, int pageSize, int startRow,string branchID)
        { 
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $" where ip.company_id = {companyID} ";
            if (searchStr != null) condi += $"and (ip.item_name ilike '%{searchStr}%' or ip.py_str ilike '%{searchStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%' or ip.barcode ilike '%{searchStr}%') ";
            if (brandID != null) condi += $"and ip.item_brand = {brandID} ";
            if (classID != null) condi += $"and ip.other_class like '%/{classID}/%' ";
            if (branchID != null) condi += $"and stock.branch_id = {branchID} ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"SELECT ip.item_name,floor(stock.stock_qty/(t.b->>'f1')::numeric) bStock,(t.b->>'f2') as bUnit,
                         (CASE WHEN (t.m->>'f1') is null THEN null ELSE floor((stock.stock_qty%(t.b->>'f1')::numeric)/(t.m->>'f1')::numeric) END) as mStock,(t.m->>'f2') as mUnit,
                         (CASE WHEN (t.b->>'f1') is NOT NULL AND (t.m->>'f1') is NOT NULL THEN floor(stock.stock_qty%(t.b->>'f1')::numeric%(t.m->>'f1')::numeric)
			                   WHEN (t.b->>'f1') is NOT NULL AND (t.m->>'f1') is NULL THEN round(stock.stock_qty%(t.b->>'f1')::numeric) 
			                   WHEN (t.b->>'f1') is NULL AND (t.m->>'f1') is NULL AND stock.stock_qty>0 THEN round(stock.stock_qty) END) sStock,(t.s->>'f2') as sUnit,(t.s->>'f1') as sFactor,(t.m->>'f1') as mFactor,(t.b->>'f1') as bFactor,(t.s->>'f3') as spPrice,(t.s->>'f4') as slPrice,(t.m->>'f3') as mpPrice,(t.m->>'f4') as mlPrice,(t.b->>'f3') as bpPrice,(t.b->>'f4') as blPrice
                         FROM info_item_prop as ip 
                         LEFT JOIN 
                         (select item_id,s,m,b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,retail_price)) as json from info_item_multi_unit where company_id = {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                 as errr(item_id int, s jsonb,m jsonb, b jsonb)) t
                          on ip.item_id=t.item_id
                         LEFT JOIN stock on t.item_id = stock.item_id
                         LEFT JOIN (select * from info_item_class where company_id = {companyID}) as ic on ip.item_class = ic.class_id  {condi} limit {pageSize} offset {startRow}";
            QQ.Enqueue("data", sql);
            sql = $@"SELECT COUNT(ip.item_id) as itemCount FROM info_item_prop as ip  LEFT JOIN stock on ip.item_id = stock.item_id {condi} ";
            QQ.Enqueue("count", sql);
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data,itemCount });
        }




        /// <summary>
        /// 历史销售记录-- 返回{itemname,sheet_no,数量变化，单位，价格,日期，dateNum日期差,remark备注}
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="itemID">商品ID（1-48）</param>
        /// <param name="pageSize">可不传</param>
        /// <param name="startRow">可不传</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetSaleHistory(string operKey, string itemID,string pageSize,string startRow)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where m.company_id = {companyID} and item_id = {itemID} and approve_time is not null and red_flag is null ";
            var condi1 = $"";
            if (pageSize != null && startRow != null) condi1 += $" limit {pageSize} offset {startRow}"; 
            var sql = @$"select d.sheet_item_name,m.sheet_no,m.money_inout_flag*d.quantity quantity,d.unit_no,d.real_price,d.happen_time,(now()::DATE-d.happen_time::DATE) dateNum,d.remark from sheet_sale_main as m LEFT JOIN sheet_sale_detail as d on m.sheet_id = d.sheet_id
                       {condi} ORDER BY d.happen_time desc {condi1};";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
            sql = $"select count(m.sheet_no) as count from sheet_sale_main as m LEFT JOIN sheet_sale_detail as d on m.sheet_id = d.sheet_id {condi} ";
            QQ.Enqueue("count", sql);
            List<ExpandoObject> saleHistory = null;
            var dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    saleHistory = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    count = CPubVars.GetTextFromDr(dr, "count");
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, saleHistory,count });
        }


        /// <summary>
        /// 订单历史记录 -- 返回{itemname,sheet_no,数量变化，单位，价格,日期，dateNum日期差,remark备注}
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="itemID">（1）</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetSaleOrderHistory(string operKey, string itemID, string pageSize, string startRow)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where m.company_id = {companyID} and item_id = {itemID}";
            var condi1 = "";
            if (pageSize != null && startRow != null) condi1 += $" limit {pageSize} offset {startRow}";
            var sql = @$"select d.item_name,m.sheet_no,m.money_inout_flag*d.quantity quantity,d.unit_no,d.real_price,d.happen_time,(now()::DATE-d.happen_time::DATE) dateNum,d.remark from sheet_sale_order_main as m LEFT JOIN sheet_sale_order_detail as d on m.sheet_id = d.sheet_id
                       {condi} ORDER BY d.happen_time desc {condi1};";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
            sql = $"select count(m.sheet_no) as count from sheet_sale_order_main as m LEFT JOIN sheet_sale_order_detail as d on m.sheet_id = d.sheet_id {condi} ";
            QQ.Enqueue("count", sql);
            List<ExpandoObject> saleOrderHistory = null;
            var dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    saleOrderHistory = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    count = CPubVars.GetTextFromDr(dr, "count");
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, saleOrderHistory, count });
        }
    }
    */
}
