﻿using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class SellerRankModel : PageQueryModel
    { 
        public SellerRankModel(CMySbCommand cmd) : base(Services.MenuId.sellerRank)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead",  CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time",CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead",  CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time",CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"oper_name",    new DataItem(){Title="业务员",    Width="100" }},
                       {"x_amount",     new DataItem(){Title="销售金额",  Width="200",SqlFld="round(sum(case when (inout_flag*sd.quantity<=0) then inout_flag*(-1)*sub_amount else 0 end)::numeric,2)",ShowSum = true}},
                       {"t_amount",     new DataItem(){Title="退货金额",  Width="200",SqlFld="round(sum(case when (inout_flag*sd.quantity>0) then inout_flag*(-1)*sub_amount else 0 end)::numeric,2)",ShowSum = true}},
                       {"net_amount",   new DataItem(){Title="销售净额",  Width="200",SqlFld="round(sum(sub_amount*inout_flag*(-1))::numeric,2)",ShowSum = true}},
                     },
                     QueryFromSQL=@"from sheet_sale_main sm
		                left join sheet_sale_detail sd on sm.sheet_id = sd.sheet_id
		                left join info_operator o on sm.seller_id = o.oper_id where sm.company_id=~COMPANY_ID and coalesce(sm.red_flag,0)=0 and sm.approve_time is not null",
                     QueryGroupBySQL = " group by oper_name",
                     QueryOrderSQL=" order by net_amount desc"
                  }
                } 
            };             
        }
        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class SellerRankController : QueryController
    { 
        public SellerRankController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SellerRankModel model = new SellerRankModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            SellerRankModel model = new SellerRankModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

    }
}
