﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class RolesViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
 
        public RolesViewModel(CMySbCommand cmd) : base(Services.MenuId.infoRole)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                 {"searchString",new DataItem(){Title="搜索",PlaceHolder="名称",UseJQWidgets=false, SqlFld="role_name",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowContextMenu=true,
                     IdColumn="role_id",TableName="info_role",
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"role_id",new DataItem(){Title="id", Width="80",Hidden=true,HideOnLoad = true}},
                       {"role_name",new DataItem(){Title="角色名称", Width="40%",Linkable=true}},                       

                       {"remark",new DataItem(){Title="备注", ButtonUsage="list", Width=""}}
                     },
                     QueryFromSQL="from info_role" ,QueryOrderSQL="order by role_id"
                  }
                } 
            }; 
        }
        public async Task OnGet()
        {  
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            string err = "";
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = $"select * from info_operator where role_id in ({rowIDs}) and company_id={company_id} limit 1";
            QQ.Enqueue("order", sql);
            
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (dr.Read())
                { 
                    err = "该角色已经被员工使用,无法删除";
                    break;
                   
                }
            }
            QQ.Clear();
            return err;
        }
    }



    [Route("api/[controller]/[action]")]
    public class RolesViewController : QueryController
    { 
        public RolesViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            RolesViewModel model = new RolesViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        { 
            RolesViewModel model = new RolesViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }
        [HttpGet]
        public async Task<JsonResult> GetAvailRoleTemplates()
        {            
            string sql = @$"
select coalesce(t.templ_id,g.templ_id) templ_id,coalesce(t.templ_name,g.templ_name) templ_name,coalesce(t.templ_rights,g.templ_rights) templ_rights,coalesce(t.fee_discount,g.fee_discount) fee_discount 
from 
(
 select g.* from g_role_template g left join g_company c on c.company_id={Token.CompanyID} where g.business_type =c.business_id
) g 
full join 
(select * from info_role_template where company_id={Token.CompanyID}
) t on g.templ_id=t.templ_id";
            var data =await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new {result="OK",msg="",data});          
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            RolesViewModel model = new RolesViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd, "info_role");// gridID, startRow, endRow, bNewQuery);
            return records;
        }
    }
}
