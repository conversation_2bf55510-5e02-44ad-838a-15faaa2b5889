@page
@model ArtisanManage.Pages.Report.VipPointsSummaryModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow: hidden">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var newCount = 1;
        var itemSource = {};
        $(document).ready(function() {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)

            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                debugger
                var supcust_id = args.row.bounddata.supcust_id;
                var sup_name = args.row.bounddata.sup_name;
                if (args.datafield == "sup_name" && supcust_id) {
                    window.queryItems = funGetQueryValues();
                    window.queryItems['supcust_id'] = supcust_id;
                    window.queryItems['sup_name'] = sup_name;
                    var queryItemStr = queryItemsToString(window.queryItems)
                    window.parent.newTabPage('会员积分明细表', 'Report/VipPointsDetail' + queryItemStr, window);
                }
            });
            QueryData();
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            /*
            $('#supcust_id').jqxInput({
                onButtonClick: function (event) {
                    $('#popClient').jqxWindow('open');
                    $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&multiSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                }
            });
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            */

            QueryData();
        });
        function queryItemsToString(queryItems) {
            var query = "?"
            for (var i in queryItems) {
                if (i == 'operKey') {
                    continue;
                }
                query += "&"
                query += i
                query += '='
                query += queryItems[i]
            }
            return query;
        }
    </script>
</head>

<body style="overflow: hidden">
    <div style="display: flex; margin-top: 20px; align-items: center">
        <div id="divHead" class="headtail" style="width: calc(100% - 110px)">
            <div style="float: none; height: 0px; clear: both"></div>
        </div>

        <button
            onclick="QueryData()"
            style="margin-top: 30px;"
        >
            查询
        </button>
        <button
            id="btnExport"
            onclick="ExportExcel()"
            style="margin-left: 20px; margin-top: 30px"
        >
            导出
        </button>
    </div>

    <div id="gridItems"></div>
    <div id="divRowCount">
        <div>共<label id="rows_count">0</label>行</div>
    </div>

    <div id="popClient" style="display: none">
        <div id="clientCaption" style="height: 30px; background-color: #fff; text-align: center">
            <span style="font-size: 20px">选择客户</span>
        </div>
        <div style="overflow: hidden"></div>
    </div>
</body>
</html>
