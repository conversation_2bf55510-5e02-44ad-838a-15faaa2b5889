﻿@page
@model ArtisanManage.Pages.CwPages.CwVoucherViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>
    <style>
        .voucher_find_sheet_a {
            cursor: pointer !important;
        }

        .voucher_a:visited, .voucher_find_sheet_a:visited {
            color: #4499ff;
        }

        #jqxNoti {
            position: absolute;
            border: 1px solid dimgray;
            top: 5px;
            right: 5px;
            display: none;
        }

        #jqxNotificationDefaultContainer-top-right {
            z-index: 10000;
            max-height: calc(100% - 10px);
            overflow-y: scroll;
            direction: ltr;
        }

        #jqxNotificationDefaultContainer-top-right .jqx-notification {
            padding: 15px;
            border: 1px solid darkgrey;
        }

        #divOpreateGroup{
        }

        #btnBulkOpreate{
            width:100px;
            position:relative;
        }

        #btnBulkOpreate:hover #btnOpreateGroup {
            display:block;
        }

        #btnOpreateGroup{
            visibility:hidden;
            width:75px;
            height:fit-content;
            background-color:rgba(255,255,255,0.5);
            border:5px;
            margin:0;
            position:absolute;
            z-index:100;
        }

        #btnOpreateGroup button{
            cursor:pointer;
        }

        #importMask {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255,0.8);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            color: dimgrey;
            font-size: 18px;
        }

        #importProgress{
            width:220px;
            height:70px;
            transform:translate(calc(50vw - 50%), calc(50vh - 50%));
        }

        #importProgress_border{
            width:200px;
            height:30px;
            background-color:#fff;
            border: 2px solid FireBrick;
            display:inline-block;
        }

        #importProgress_content{
            height:100%;
            width:0%;
            background-color:FireBrick;
        }

        #resort_period, #resort_type{
            display:block;
            width:400px;
            height:30px;
            margin-top:30px;
        }

        #resort_footer{
            display: block;
            position:absolute;
            bottom:30px;
            width:fit-content;
            margin-left:100px;
        }

        #resort_period div, #resort_period select, #resort_type div, #resort_type select {
            display:inline-block;
        }

        #resort_period select, #resort_type select{
            width:250px;
        }

        #resort_footer button{
            display: inline-block;
            margin-right:30px;
        }

        #btnArrowDown{
            display:inline-block;
            top:10%;
        }

        /* 导入弹窗样式 */
        .import-dialog {
            padding: 20px;
        }
        .import-dialog p {
            margin: 10px 0;
            font-weight: bold;
        }
        .import-dialog a {
            color: #06c;
            text-decoration: underline;
            cursor: pointer;
        }
        .import-dialog input[type="file"] {
            margin: 10px 0;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .import-dialog-buttons {
            text-align: right;
            margin-top: 20px;
        }
        .import-dialog-buttons button {
            padding: 5px 15px;
            margin-left: 10px;
            cursor: pointer;
        }
    </style>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)'; 

    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
                
                // 初始化导入窗口
                $("#popImport").jqxWindow({ 
                    isModal: true, 
                    modalOpacity: 0.3, 
                    height: 300, 
                    width: 500, 
                    theme: 'summer', 
                    autoOpen: false, 
                    showCloseButton: true, 
                    closeButtonSize: 32, 
                    showAnimationDuration: 500, 
                    closeAnimationDuration: 500, 
                    animationType: 'fade' 
                });
                
                // 初始化导入进度窗口
                $("#popImportProgress").jqxWindow({ 
                    isModal: true, 
                    modalOpacity: 0.3, 
                    height: 150, 
                    width: 400, 
                    theme: 'summer', 
                    autoOpen: false, 
                    showCloseButton: false, 
                    closeButtonSize: 32, 
                    showAnimationDuration: 500, 
                    closeAnimationDuration: 500, 
                    animationType: 'fade' 
                });
                
                // 初始化导入结果窗口
                $("#popImportMessage").jqxWindow({ 
                    isModal: true, 
                    modalOpacity: 0.3, 
                    height: 500, 
                    width: 450, 
                    theme: 'summer', 
                    autoOpen: false, 
                    showCloseButton: true, 
                    closeButtonSize: 32, 
                    showAnimationDuration: 500, 
                    closeAnimationDuration: 500, 
                    animationType: 'fade' 
                });
                
                $("#gridItems").on("cellclick", function (event) {
                    // event arguments.
                    var args = event.args;
                    console.log(args.row);
                    if (args.datafield == "sheet_no") { 
                        var sheet_id = args.row.bounddata.sheet_id;
                        var startDay = $('#startDay').jqxDateTimeInput('val');
                        var endDay = $('#endDay').jqxDateTimeInput('val');
                       //  var sheet_type = args.row.bounddata.sheet_type;
                       // rowIndex = args.rowindex;
                        //var sheet_type=$('#gridItems').jqxGrid('getcellvalue', rowIndex, 'sheet_type');
                        window.parent.newTabPage('凭证', `CwPages/CwVoucher?sheet_id=${sheet_id}&startDay=${startDay}&endDay=${endDay}`,window);
                    } 
                });

                $("#gridItems").jqxGrid('beforeRowRender', function (divRow, rowData) {
                    if (rowData.sheet_status == '已红冲') 
                        divRow.style.color='#aaa' 
                    else if (rowData.sheet_status == '红字单')
                        divRow.style.color='#f00'                  
                    else
                        divRow.style.color='#000'

                })
                $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                var approve_time = $('#approve_time').text();
                if (approve_time) {
                    $('#btnApprove').attr('disabled', true);
                    $("#btnSave").attr('disabled', true);
                }
                QueryData();

        });

        function checkAndQuery() {
            $("#byAccountingPeriod").val(false);
            QueryData();
        }

        function bizSheetIdNoRender(row, column, value, p4, p5, rowData) {
            if(value=='') return p4;
            let biz_sheet_innerHTML = '<div class="jqx-grid-cell-left-align" style="margin-top: 6px;">';
            let biz_id_no_arr = value.split(';');
            if (biz_id_no_arr.length>0) {
                for (let i = 0; i < (biz_id_no_arr.length > 2 ? 2 : biz_id_no_arr.length); i++) {
                    let id_no_arr = biz_id_no_arr[i].split(',');
                    biz_sheet_innerHTML += `<a class="voucher_find_sheet_a" href="#" onmouseup="voucherClickSheet(event,'${rowData.business_sheet_type}','${id_no_arr[0]}','${id_no_arr[1]}')">${id_no_arr[1]}</a><span>,</span>`;
                }
                biz_sheet_innerHTML = biz_sheet_innerHTML.slice(0, -14);
                if (biz_id_no_arr.length > 2) {
                    biz_sheet_innerHTML += `<span style="cursor:pointer;" href="#"  onmouseup="showBizSheetAll(${row})">...</span>`;
                }
            }
            biz_sheet_innerHTML+='</div>';
            return biz_sheet_innerHTML;
        }

        function showBizSheetAll(rowindex) {
            let row = $('#gridItems').jqxGrid('getrowdatabyid', rowindex);
            let bizSheetHtmlAll = '<div>';
            let biz_id_no_arr = row.business_sheet_id_nos.split(';');
            let index = 1;
            biz_id_no_arr.forEach((id_no) => {
                let id_no_arr = id_no.split(',');
                bizSheetHtmlAll += `<a class="voucher_find_sheet_a" href="#" onmouseup="voucherClickSheet(event,'${row.business_sheet_type}','${id_no_arr[0]}','${id_no_arr[1]}')">${id_no_arr[1]}</a><span>,</span>`;
                if (index % 3 == 0) {
                    bizSheetHtmlAll += '<br>';
                }
                index++;
            });
            if (bizSheetHtmlAll.endsWith('<span>,</span>')) {
                bizSheetHtmlAll = bizSheetHtmlAll.substring(0, bizSheetHtmlAll.length - 14);
            }
            else if (bizSheetHtmlAll.endsWith('<span>,</span><br>')) {
                bizSheetHtmlAll = bizSheetHtmlAll.substring(0, bizSheetHtmlAll.length - 18);
            }
            $('#jqxNoti').html(bizSheetHtmlAll);
            $("#jqxNoti").jqxNotification({ width: "450", height: "auto", position: "top-right", opacity: 0.9, autoOpen: false, autoClose: false, template: null, theme: "summer", animationOpenDelay: "fast", animationCloseDelay: "fast", showCloseButton: true });
            $('#jqxNoti').jqxNotification('open');
        }

        function voucherClickSheet(e,biz_sheet_type,biz_sheet_id,biz_sheet_no){
            e.stopPropagation();
            let sheet_name='';
            let url='';
            switch (biz_sheet_type){
                case 'X':
                    sheet_name='销售单';
                    url = `Sheets/SaleSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'D':
                    sheet_name='销售单';
                    url = `Sheets/SaleSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'T':
                    sheet_name='退货单';
                    url = `Sheets/SaleSheet?forReturn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'CG':
                    sheet_name='采购单';
                    url = `Sheets/BuySheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'CT':
                    sheet_name='采购退货单';
                    url = `Sheets/BuySheet?forReturn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'SK':
                    sheet_name='收款单';
                    url = `Sheets/GetArrearsSheet?forPayOrGet=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'FK':
                    sheet_name = '付款单';
                    url = `Sheets/GetArrearsSheet?forPayOrGet=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'YS':
                    sheet_name = '预收款单';
                    url = `Sheets/PrepaySheet?forPayOrGet=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'YF':
                    sheet_name = '预付款单';
                    url = `Sheets/PrepaySheet?forPayOrGet=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'ZC':
                    sheet_name = '费用支出单';
                    url = `Sheets/FeeOutSheet?forOutOrIn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'SR':
                    sheet_name = '其他收入单';
                    url = `Sheets/FeeOutSheet?forOutOrIn=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'YK':
                    sheet_name = '盘点盈亏单';
                    url = `Sheets/InventChangeSheet?forReduce=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'BS':
                    sheet_name = '报损单';
                    url = `Sheets/InventChangeSheet?forReduce=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'DH':
                    sheet_name = '定货会';
                    url = `Sheets/OrderItemSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'DHTZ':
                    sheet_name = '定货会调整单';
                    url = `Sheets/OrderItemAdjustSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'CBTJ':
                    sheet_name = '成本调价单';
                    url = `Sheets/CostPriceAdjustSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'TR':
                    sheet_name = '转账单';
                    url = `CwPages/CashBankTransferSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'RK':
                    sheet_name = '其他入库单';
                    url = `Sheets/StockInOutSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'CK':
                    sheet_name = '其他出库单';
                    url = `Sheets/StockInOutSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'FYFT':
                    sheet_name = '采购费用分摊单';
                    url = `Sheets/FeeApportionSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'DK':
                    sheet_name = '贷款单';
                    url = `Sheets/LoanSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'HDK':
                    sheet_name = '还贷款单';
                    url = `Sheets/RepaySheet?sheet_id=${biz_sheet_id}`;
                    break;
            }
            window.parent.newTabPage(sheet_name, url, window);
            //window.parent.newTabPage('哈哈哈', `Sheets/SaleSheet?sheet_id=4403730`, window);
        }

        function btnBulkOperate_hover(){
            $("#btnOpreateGroup").css('visibility','inherit');
            $("#divOpreateGroup").on('mouseleave', (e) => {
                $("#btnOpreateGroup").css('visibility', 'hidden');              
            })
        }
        function btnBulkPrint() {
              var rows = window.g_checkedRows
            let sheetIDs = "";
            for (var id in rows) {
                let row = rows[id]; 
                sheetIDs += row.sheet_id + ',';
            }

            sheetIDs = sheetIDs.substring(0, sheetIDs.length - 1);


           $.ajax({
                url: '/api/CwVoucherView/GetMultiSheetsToPrint',
                type: 'GET',
                contentType: 'application/json',
                // data: JSON.stringify({sheet_id:'1',supcust_id:'2'}),
                data: {
                    operKey: g_operKey, sheetIDs: sheetIDs
                },
                success: function (data) {
                    console.log(data);
                    if (data.result === 'OK') { 
                        var container = window.parent.CefGlue
                        window.parent.g_SheetsWindowForPrint=window
                        //var sheets = data.printSheets;
                        var sheetGroup = data.sheetGroup;

                        
                        //var tmpSale = tmpsAndCprters.tmps.SaleList[tmp_sale];
                        //var tmpReturn = tmp_return == -1 ? null : tmpsAndCprters.tmps.ReturnList[tmp_return];

                         
                        sheetGroup.forEach(grp => {                            
                           
                                //grp.template=JSON.parse(tmpSale.template_content)
                                for (var i = 0; i < grp.sheets.length; i++){
                                    var sht = grp.sheets[i] 
                                       
                                }
                          
                        }) 
                         
                        console.log(sheetGroup)
                        if (!container || !container.printSheetByTemplate) {
                            bw.toast('在客户端程序中才可以打印',3000)
                            return
                        } 
                        
                        // Push cloud printers
                        let cloudPrinters = data.cloudPrinters;
                        var templVariables = data.templVariables;
                        var promise = container.printMultiSheets(sheetGroup, true, cloudPrinters, templVariables)
                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
               },
                error: function (xhr)
                {
                      console.log("返回响应信息：" + xhr.responseText);
                }
           })
 

        }
        function btnBulkOperate(type) {
            let typeArr=[
                { type: 'Approve', text: '审核' },
                { type: 'CancelApprove', text: '取消审核' },
                { type: 'Red', text: '红冲' },
                { type: 'Delete', text: '删除' }
            ];
            let thisType=typeArr.find(row=>row.type==type);

            var rows = window.g_checkedRows
            let ids = "";
            for (var id in rows) {
                let row = rows[id];
                if(row.sheet_status.indexOf("红")>-1){
                    bw.toast('请勿选择红冲凭证', 3000);
                    return;
                }
                ids += row.sheet_id + ',';
            }
            ids = ids.substring(0, ids.length - 1);
            jConfirm(`确定批量${thisType.text}凭证吗？`, function () {
                $('#mask').css('display', 'block');
                //每行230毫秒
                let progressWidth=0;
                let widthEachRow = 0.98 / ids.split(',').length;//进度条拉满是98%
                var timeEachRow=setInterval(function(){
                    progressWidth += widthEachRow;
                    //console.log(progressWidth);
                    if (progressWidth<0.98){
                        $('#progress_content').width(`${Math.round(progressWidth * 100)}%`);
                        $('#progress_text').text(`处理中...${Math.round(progressWidth / 0.98 * 100)}%`);
                    }else{
                        $('#progress_content').width('98%');
                        $('#progress_text').text(`处理中...100%`);
                        clearInterval(timeEachRow);
                    }
                },230);

                ajaxPost(`/api/CwVoucherView/Bulk${thisType.type}`, { ids }).then(data => {
                    if (data.result == "OK") {
                        clearInterval(timeEachRow);
                        $('#progress_content').width('98%');
                        $('#progress_text').text(`处理中...100%`);
                        setTimeout(()=>{
                            $('#mask').css('display', 'none');
                            $('#progress_content').width('0');
                            $('#progress_text').text(`处理中...0%`);

                            bw.toast(`凭证${thisType.text}成功`, 3000)
                            console.log(`凭证${thisType.text}成功`);
                            QueryData()
                        },500);
                    }else{
                        $('#mask').css('display', 'none');
                        $('#progress_content').width('0');
                        $('#progress_text').text(`处理中...0%`);
                        clearInterval(timeEachRow);

                        bw.toast(data.msg, 3000);
                        console.log(data.msg);
                    }
                }).catch(err => { 
                    clearInterval(timeEachRow);
                    $('#mask').css('display', 'none');
                    $('#progress_content').width('0');
                    $('#progress_text').text(`处理中...0%`);
                    console.log(err) ;
                    bw.toast('网络超时', 3000);
                    console.log('网络超时');
                });
            }, "");
        }

        function openResortWindow(){
            $('#list_resort_period').empty();
            $.ajax({
                url: '/api/CwVoucherView/GetCwPeriodsForResort',
                type: 'GET',
                contentType: 'application/json',
                async: false,
                data: { operKey: g_operKey },
                success: function (data) {
                    console.log(data);
                    if (data.result === 'OK') {
                        let html = '';
                        data.periods.forEach(period => html += `<option value="${period}">${period.substring(0,7)}</option>`);
                        $('#list_resort_period').append(html);

                        if ($("#jqxwindow_resort_sheetno")[0].classList.value.split(' ').find(a => a == 'jqx-window') == undefined) {
                            $("#jqxwindow_resort_sheetno").jqxWindow({ height: 300, width: 500, theme: 'summer', isModal: true });
                        }
                        $('#jqxwindow_resort_sheetno').jqxWindow('open');
                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }
        function resortSheetNo(){
            let period = $('#list_resort_period')[0].value;
            let type = $('#list_resort_type')[0].value;
            $.ajax({
                url: '/api/CwVoucherView/ResortSheetNo',
                type: 'POST',
                contentType: 'application/json',
                async: false,
                data: JSON.stringify({ operKey: g_operKey, period: period, type: type }),
                success: function (data) {
                    console.log(data);
                    if (data.result === 'OK') {
                        $('#jqxwindow_resort_sheetno').jqxWindow('close');
                        bw.toast('整理成功', 3000);
                        $('#startDay').val(data.periodOp);
                        $('#endDay').val(data.periodEd);
                        $('#status').val({value:'all', label:'所有'});
                        $('#sheet_no').val('');
                        $('#sub_id').val({value:'', label:''});
                        $("#byAccountingPeriod").val(false);
                        QueryData();
                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }
        
        // 打开导入弹窗
        function openImportWindow() {
            $("#popImport").jqxWindow('open');
        }
        
        // 下载模板
        function downloadTemplate() {
            window.open("https://yingjiang.obs.myhuaweicloud.com/download/凭证导入模板.xlsx");
        }

        // 下载模板
        function downloadFile(url, filename) {
            console.log("url:",url);
            console.log("fileName:",filename);

            getBlob(url, function (blob) {
                saveDownloadFile(blob, filename);
            })
        }
        function getBlob(url, cb) {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.responseType = 'blob';
            xhr.onload = function () {
                if (xhr.status === 200) {
                    cb(xhr.response);
                }
            };
            xhr.send();
        }
        
        // 保存
        function saveDownloadFile(blob, filename) {
            if (window.navigator.msSaveOrOpenBlob) {
                navigator.msSaveBlob(blob, filename);
            } else {
                var link = document.createElement('a');
                var body = document.querySelector('body');

                link.href = window.URL.createObjectURL(blob);
                link.download = filename;

                // fix Firefox
                link.style.display = 'none';
                body.appendChild(link);

                link.click();
                body.removeChild(link);

                window.URL.revokeObjectURL(link.href);
            }
        }
        // 上传文件
        function uploadFile() {
            if (!$('#import-file').val()) {
                bw.toast("请先选择要导入的文件");
                return;
            }

            var formData = new FormData();
            let file = $("#import-file")[0].files[0];
            console.log("fileName:",file.name);

            // if (file.name.indexOf('@(Model.companyName)')==-1) {
            //     bw.toast("模板文件名须包含商贸公司名称！")
            //     return
            // }
            
            // 处理文件名，添加公司ID和时间戳
            // let arr = file.name.split(".");
            // let tm = new Date().toISOString().replace(/[-:.TZ]/g, '');
            // file.name = `${arr[0]}_${g_company_id}_${tm}.${arr[arr.length-1]}`;
            
            formData.append('file', file, file.name);

            // 显示进度窗口
            $("#popImportProgress").jqxWindow('open');
            // $("#importProgressBar").jqxProgressBar({ value: 0 });
            console.log("显示进度窗口");
            
            $.ajax({
                url: `/api/CwVoucherView/ImportVouchers?operKey=${g_operKey}`,
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    var xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener("progress", function(evt) {
                        if (evt.lengthComputable) {
                            var percentComplete = Math.round((evt.loaded / evt.total) * 100);
                            $("#importProgressBar").jqxProgressBar('value', percentComplete);
                        }
                    }, false);
                    return xhr;
                },
                success: function (res) {
                    $("#popImportProgress").jqxWindow('close');
                    $("#popImport").jqxWindow('close');
                    
                    if (res.result === "OK") {
                        bw.toast("凭证导入成功", 3000);
                        QueryData(); // 刷新数据
                    } else {
                        $("#divImportMessage").html(res.msg);
                        $("#popImportMessage").jqxWindow('open');
                    }
                },
                error: function (err) {
                    $("#popImportProgress").jqxWindow('close');
                    bw.toast("导入失败: " + err.statusText, 3000);
                }
            });
        }
    </script>
</head>

<body>
  
    <div style="display:flex;padding-top:20px;">
       <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
       </div>
        <button onclick="checkAndQuery()" style="margin-left:20px;" class="main-button">查询</button>
        <div id="divOpreateGroup">
            <button id="btnBulkOpreate" onmouseenter="btnBulkOperate_hover()">批量操作 <div id="btnArrowDown" class="jqx-icon-arrow-down jqx-icon"></div></button>
            <div id="btnOpreateGroup">
                <button onclick="btnBulkPrint()">打印</button>
                <button onclick="btnBulkOperate('Approve')">审核</button>
                <button onclick="btnBulkOperate('CancelApprove')">取消审核</button> 
                <button onclick="btnBulkOperate('Red')">红冲</button>
                <button onclick="btnBulkOperate('Delete')">删除</button>
            </div>
        </div>
        <button onclick="openResortWindow()">整理</button>
        <button id="btnImport" onclick="openImportWindow()">导入</button>
        <button id="btnExport" onclick="ExportExcel()">导出</button>
     </div>
    
    <div id="gridItems" style="margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 95px);"></div>
    <div id="divRowCount"><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>
    

    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

    <div id="mask">
        <div id="progress">
            <div id="progress_border">
                <div id="progress_content"></div>
            </div>
            <div id="progress_text">处理中...</div>
        </div>
    </div>

    <div id='jqxwindow_resort_sheetno' style="display:none;">
        <div>整理凭证</div>@*window-head*@
        <div style="margin-top:7%;margin-left:10%;">@*window-body*@
            <div id='resort_period'>
                <div>会计期间：</div>
                <select id="list_resort_period"></select>
            </div>
            <div id='resort_type'>
                <div>整理方式：</div>
                <select id="list_resort_type">
                    <option value="1">按凭证号顺次前移重新编号</option>
                    <option value="2">按凭证日期顺次重新编号</option>
                    <option value="3">按凭证号顺次前移重新编号 且 跳过红冲凭证</option>
                    <option value="4">按凭证日期顺次重新编号 且 跳过红冲凭证</option>
                </select>
            </div>
            <div id="resort_footer">
                <button onclick="resortSheetNo()">确定</button>
                <button onclick="$('#jqxwindow_resort_sheetno').jqxWindow('close');">取消</button>
            </div>
        </div>
    </div>

    <div id="jqxNoti"></div>

    <!-- 导入凭证弹窗 -->
    <div id="popImport" style="display:none;">
        <div>导入凭证</div>
        <div class="import-dialog">
            <p>第一步 下载模板，填入数据：</p>
            @* <a href="javascript:void(0);" onclick="downloadTemplate()">下载模板</a>  *@
            <a id="downloadLink" href="javascript:void(0)" onclick="downloadFile('https://yingjiang.obs.myhuaweicloud.com/download/凭证导入模板.xlsx','@(Model.companyName)_导入模板.xlsx')">下载模板</a>
            <p>第二步 选择模板，导入：</p>
            <input type="file" id="import-file" name="file" accept=".xlsx" />
            <div class="import-dialog-buttons">
                <button onclick="uploadFile()">确定</button>
                <button onclick="$('#popImport').jqxWindow('close');">取消</button>
            </div>
        </div>
    </div>
    
    <!-- 导入进度弹窗 -->
    <div id="popImportProgress" style="display:none;">
        <div>导入进度</div>
        <div style="padding:20px;">
            <div id="importProgressBar"></div>
            <div style="text-align:center;margin-top:10px;">正在导入，请稍候...</div>
        </div>
    </div>
    
    <!-- 导入结果弹窗 -->
    <div id="popImportMessage" style="display:none;">
        <div>导入结果</div>
        <div style="overflow:auto;padding:20px;">
            <div id="divImportMessage"></div>
        </div>
    </div>

</body>
</html>