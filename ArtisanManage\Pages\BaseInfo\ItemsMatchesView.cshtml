﻿@page
@model ArtisanManage.Pages.BaseInfo.ItemsMatchesViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());

        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "ItemsMatchesEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var now_arr = new Array
                        var row = {
                            // TODO 
                            client_id: rs.data.record.client_id,
                            sup_name: rs.data.record.sup_name,
                            edit: "编辑"
                        }
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;


                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    QueryData();

                }
                $("#popItem").jqxWindow('close');
            };
        });

        var newCount = 1;

        function btnAddItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="ItemsMatchesEdit?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }

        function onGridRowEdit(rowIndex) {
            var client_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, "client_id");
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="ItemsMatchesEdit?operKey=' + g_operKey + '&client_id=' + client_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }

        var itemSource = {};
        $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)

                $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                if (args.datafield == "edit") {
                    if (args.originalEvent.button == 2) return;
                   if (ForSelect) {
                        var client_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "client_id");
                        
                        var msg = {
                            msgHead: 'ItemsMatchesView', action: 'select', client_id: client_id
                        };
                        window.parent.postMessage(msg, '*');
                    }
                    else {
                        onGridRowEdit(args.rowindex);
                        //$('#popItem').jqxWindow('open');
                        // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                    }
                }
            });


            $("#Cancel").on('click', function () {
                for (var i = 0; i < 10; i++) {
                    $('#jqxgrid').jqxGrid('deleterow', i);
                    $('#jqxgrid').jqxGrid('addrow', i, {})
                }
            });

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 500, width: 800, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            QueryData();
        });
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        .label_name {
            line-height: 32px;
            margin-left: 10px;
        }

        .label_content {
            width: 120px;
            height: 30px;
            margin-left: 10px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
    </style>
</head>

<body>

    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;">
        <div style="display:inherit">
            <div id="div_sup_name" style="display: flex; width: 300px;"><label class="sup_name">客户名称</label> <div id="supcust_id" style="width:200px;" class="label_content"></div></div>
            @*<input id="searchString" class="margin" placeholder="请输入客户/档案商品名称" />*@
            <button onclick="QueryData()" class="margin">查询</button>
        </div>

        <div><button onclick="btnAddItem_click()" class="margin">新增</button></div>
    </div>

    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:100%;margin-bottom:10px;"></div>


    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">客户限定商品匹配信息</span></div>
        <div style="overflow:hidden;"> 
        </div>
    </div>

</body>
</html>
