﻿@page
@model ArtisanManage.Pages.BaseInfo.PriceStrategyModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <link href="~/css/component.css" rel="stylesheet" />
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        //var rowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            /*if (rs.data.msgHead === "PlansSelect") {
                if (rs.data.action === "select") {
                    var plan_name = rs.data.plan_name;
                    var plan_id = rs.data.plan_id;
                    var index = rs.data.index;
                    if (index == "1") {
                        $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "price1", plan_id);
                        $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "price1_name", plan_name);
                    } else if (index == "2") {
                        $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "price2", plan_id);
                        $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "price2_name", plan_name);
                    } else if (index == "3") {
                        $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "price3", plan_id);
                        $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "price3_name", plan_name);
                    }
                }
                $('#popPlan').jqxWindow('close');
            } else if (rs.data.msgHead == "ClientClassSelect") {
                if (rs.data.action === "select") {
                    var region_id = rs.data.region_id;
                    var region_name = rs.data.region_name;
                    $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "region_id", region_id);
                    $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "region_name", region_name);
                }
                $('#popRegion').jqxWindow('close');
            } else if (rs.data.msgHead == "GroupsView") {
                if (rs.data.action === "select") {
                    var group_id = rs.data.group_id;
                    var group_name = rs.data.group_name;
                    $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "group_id", group_id);
                    $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "group_name", group_name);
                }
                $('#popGroup').jqxWindow('close');
            } else if (rs.data.msgHead == "RanksView") {
                if (rs.data.action === "select") {
                    var rank_id = rs.data.rank_id;
                    var rank_name = rs.data.rank_name;
                    $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "rank_id", rank_id);
                    $('#gridItems').jqxGrid('setcellvalue', window.rowIndex, "rank_name", rank_name);
                }
                $('#popRank').jqxWindow('close');
            }*/
        });
        @Html.Raw(Model.m_saveCloseScript)

        /*function onRowRemove(rowIndex,divGrid) {
            console.log('row removed')
            var id = $(divGrid).jqxGrid('getrowid', rowIndex);
            $("#gridItems").jqxGrid('deleterow', id); 
            window.operRowID = -1;
        }
        function onRowAdd(rowIndex,divGrid) {
            var row = {};
            debugger
            var columns=$(divGrid).jqxGrid('getcolumns').records
             columns.forEach(function (col) {
                if (col.datafield) row[col.datafield] = "";
                if (col.displayfield) row[col.displayfield] = "";
            })
            $("#gridItems").jqxGrid('addrow', null, row,rowIndex);
        }*/
       

        $(document).ready(function () {

            
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)

          
            //$('#gridItems').jqxGrid({cellhover:cellhover});
            window.operRowID = -1;
            window.g_gridID = 'gridItems'
            var divGrid = '#'+window.g_gridID
            $(divGrid).jqxGrid({
              cellhover:function(cellhtmlElement, x, y,rowIndex,gridID){
                if (cellhtmlElement) {
                  if (cellhtmlElement.className.indexOf('pinned') >= 0) {
                    var displayRows = $(divGrid).jqxGrid('getvisiblerows');
                     var allRows = $(divGrid).jqxGrid('getrows');
                    var arr = $('.row_operator')
                
                    for (var i = 0; i < arr.length; i++) {

                        var row_operator=arr[i]
                        if (row_operator.parentNode) {
                           // if (row_operator.parentNode.rowIndex != cellhtmlElement.rowIndex || row_operator.parentNode.rowIndex == undefined) {
                                var row = row_operator.parentNode.parentNode
                                var id = row.id
                                id = id.replace('row', '')
                                id = id.replace('gridItems', '')
                                var curRow = displayRows[id]
                                var showIndex=-1
                                for (var j = 0; j < allRows.length; j++) {
                                    var r=allRows[j]

                                    if (r ===curRow) {
                                        showIndex=j
                                    }
                            }
                            //if (showIndex != window.operRowID) {
                                  //  console.log('设置了setPinCell:', showIndex)
                                    var html = "<div style='height:100%;display:flex; justify-content:center;align-items:center;'>" + (showIndex + 1) + "</div>";
                                   row_operator.parentNode.innerHTML = html;// row_operator.parentNode.normalInnerHTML;
                               //}
                            //}
                        }
                    }

                    if (cellhtmlElement.innerHTML.indexOf('row_operator') == -1) {
                        if (!(window.g_dicNotDeleteRows && window.g_dicNotDeleteRows[rowIndex])) {
                              var pinText = $(cellhtmlElement).text()
                        

                           // window.g_hoveredRow
                            cellhtmlElement.innerHTML = `<div class="row_operator" style="height:100%;width:100%;display:flex;justify-content:space-around;align-items:center;">
                             <svg onclick="onRowAdd('${divGrid.replace('#','')}',${rowIndex})" class="row-oper" height="15" width="15" style="">
                                    <use xlink:href="/images/images.svg#add" />
                             </svg>
                             <svg onclick="onRowRemove('${divGrid.replace('#','')}',${rowIndex})" class="row-oper" height="15" width="15" style="">
                                    <use xlink:href="/images/images.svg#remove" />
                             </svg>
                            </div>`;
                            window.operHTML = cellhtmlElement.innerHTML;
                            window.operRowID = rowIndex;
                           // console.log('new rowIndex:',rowIndex)
                        }
                      
                    }
                }
              }
              
              }
              
              });
            
            /*
            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                if (args.datafield == "delete") {
                    if (args.originalEvent.button == 2) return;
                    rowIndex = args.rowindex;
                    if ($('#gridItems').jqxGrid('getcellvalue', rowIndex, 'delete') != null) {
                        var setting_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'setting_id');
                        if (setting_id) {
                            jConfirm(`确定要删除选中价格设置吗？`, function () {
                                $.ajax({
                                    url: '../api/PriceClassSetting/DeleteRecords',
                                    type: 'POST',
                                    contentType: 'application/json',
                                    data: JSON.stringify({ operKey: g_operKey, gridID: 'gridItems', rowIDs: setting_id }),
                                    success: function (data) {
                                        if (data.result == 'OK') {
                                            QueryData();
                                        }
                                        else {
                                            bw.toast(data.msg, 5000);
                                        }
                                    }
                                });
                            }, "");
                        } else {
                            bw.toast("无法删除空行", 5000);
                        }
                    }
                } else if (args.datafield == "add") {
                    if (args.originalEvent.button == 2) return;
                    rowIndex = args.rowindex;
                    var row = {};
                    if ($('#gridItems').jqxGrid('getcellvalue', rowIndex, 'add') != null) {
                        row["add"] = "<a>新增</a>";
                        row["delete"] = "<a>删除</a>";
                        var newIndex = rowIndex + 1;
                        $("#gridItems").jqxGrid('addrow', null, row, newIndex);
                    }
                } else if (args.datafield == "group_name") {
                    rowIndex = args.rowindex;
                    $('#popGroup').jqxWindow('open');
                    $("#popGroup").jqxWindow('setContent', `<iframe src="/BaseInfo/GroupsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    $("#popGroup").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 450, width: 675, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                } else if (args.datafield == "region_name") {
                    rowIndex = args.rowindex;
                    $('#popRegion').jqxWindow('open');
                    $("#popRegion").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientClassSelect?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    $("#popRegion").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 450, width: 675, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                } else if (args.datafield == "rank_name") {
                    rowIndex = args.rowindex;
                    $('#popRank').jqxWindow('open');
                    $("#popRank").jqxWindow('setContent', `<iframe src="/BaseInfo/RanksView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    $("#popRank").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 450, width: 675, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                }

                else if (args.datafield == "price1_name") {
                    rowIndex = args.rowindex;
                    $('#popPlan').jqxWindow('open');
                    $("#popPlan").jqxWindow('setContent', `<iframe src="/BaseInfo/PlansSelect?forSelect=1&operKey=${g_operKey}&index=1" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    $("#popPlan").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 450, width: 675, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                } else if (args.datafield == "price2_name") {
                    rowIndex = args.rowindex;
                    $('#popPlan').jqxWindow('open');
                    $("#popPlan").jqxWindow('setContent', `<iframe src="/BaseInfo/PlansSelect?forSelect=1&operKey=${g_operKey}&index=2" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    $("#popPlan").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 450, width: 675, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                } else if (args.datafield == "price3_name") {
                    rowIndex = args.rowindex;
                    $('#popPlan').jqxWindow('open');
                    $("#popPlan").jqxWindow('setContent', `<iframe src="/BaseInfo/PlansSelect?forSelect=1&operKey=${g_operKey}&index=3" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    $("#popPlan").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 450, width: 675, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                }
                else {
                    rowIndex = args.rowindex;
                }
            });
            */
           
            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

          //  QueryData();
          //$("#gridClients").hide();
          $(".Items").parent().addClass("active");
          
          $(".priceTab div").on("click", function() {
              
                var title = $(this).attr("class");
                if(title == "Clients") {
                    $("#gridClients").css("visibility","visible");
                    $("#gridItems").css("visibility","hidden");

                    $("#searchClients").show();
                    $("#searchItems").hide();
                    window.g_gridID = 'gridClients'
                }else{
                    $("#gridItems").css("visibility","visible");
                    $("#gridClients").css("visibility","hidden");

                    $("#searchClients").hide();
                    $("#searchItems").show();
                    window.g_gridID = 'gridItems'

                }

                $(this).parent().addClass("active");
                $(this).parent().siblings().removeClass("active");
                
            })
    	});

            
    </script>
</head>

<body style="overflow:hidden">


    <style>
        html, body {
            height: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        .dataArea {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            margin: 0;
            width: 100%;
            height: 100%;
        }

            .dataArea > div:first-child {
                width: 100%;
                height: 40px;
            }

            .dataArea > div:last-child {
                display: flex;
                align-items: stretch;
                align-content: stretch;
                width: 100%;
                flex-grow: 1;
            }

                .dataArea > div:last-child > div:first-child {
                    width: 200px;
                }

                .dataArea > div:last-child > div:last-child {
                    flex-grow: 1;
                }
        .display-none {
	        display:none;
        }
        .priceTab ul {
	        list-style-type:none;
	        overflow:hidden;
            margin-top:0px;
        }
        .priceTab ul li {
	        float:left;
        }

        .priceTab ul li {
	        padding:5px 0;
	        margin-right:5px;
	        width:150px;
	        text-align:center;	        
        }

        .priceTab ul li div {
	        cursor:pointer;
        }
        .priceTab ul li.active {
	        border-bottom:1px solid #e6214a
        }
        .priceTab ul li.active div {
	        color:#e6214a;
        }


        html, body {
            height: 100%;
            padding-left: 10px;
            margin: 0;
            overflow: hidden;
        }

        body {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        #gridItems {
            margin-bottom: 2px;
            width: calc(100% - 20px);
            flex:1;
        }

        #gridClients {
            margin-bottom: 2px;
            width: calc(100% - 20px);
            flex:1;
        }
    </style>




    <div style="display:inline-link;justify-content:space-around;margin-top:20px;">
        
        <div class="priceTab" id="priceTab" style="float:left;">
            <ul>
                <li>
                     <div class="Items"> 按客户类别指定 </div>
                </li>
                <li>
                     <div  class="Clients">按客户指定</div>
                </li>
            </ul>
        </div>
        <div id="searchClients" class="search-container" style="float:left;margin-left:100px;display:none;">
            <input type="text" id="search-supcust" placeholder="客户名称">
            <button id="search-button" onclick="performSearchClient()">搜索</button>
        </div>

        <div id="searchItems" class="search-container" style="float:left;margin-left:100px;">
            <input type="text" id="search-group" placeholder="渠道">
            <input type="text" id="search-region" placeholder="片区">
            <input type="text" id="search-rank" placeholder="等级">
            <button id="search-button" onclick="performSearchClass()">搜索</button>
        </div>


        <button id="btnSave" onclick="btnSave_Clicked();" style="float:right;margin-right:100px;">保存</button>
    </div> 

    <div id="gridItems" style="position:absolute;left:20px;width:calc(100% - 50px);top:70px;bottom:50px;" ></div>    
    <div id="gridClients" style="position:absolute;left:20px;width:calc(100% - 50px);top:70px;bottom:50px;visibility:hidden"></div>
    
   <!--
       <partial name="dialog" />
    -->
    <script type="text/javascript">

        function performSearchClient() {
            var rows = $("#gridClients").jqxGrid("getRows");
            var searchName = $('#search-supcust').val();

            for (var i = 0; i < rows.length; i++) {
                var row = rows[i].uid;
                var supName = rows[i].sup_name === undefined ? "" : rows[i].sup_name;

                if (supName.includes(searchName)) {
                    $("#gridClients").jqxGrid('showrow', row);// 如果包含，显示这一行
                } else {
                    $("#gridClients").jqxGrid('hiderow', row);// 如果不包含，隐藏这一行
                }
            }

        }

        function performSearchClass() {
            var rows = $("#gridItems").jqxGrid("getRows");
            console.log(rows)
            var searchGroup = $('#search-group').val();
            var searchRegion = $('#search-region').val();
            var searchRank = $('#search-rank').val();

            for (var i = 0; i < rows.length; i++) {
                var row = rows[i].uid;
                var groupName = rows[i].group_name === undefined ? "" : rows[i].group_name;
                var regionName = rows[i].region_name === undefined ? "" : rows[i].region_name;
                var rankName = rows[i].rank_name === undefined ? "" : rows[i].rank_name;

                if ((groupName.includes(searchGroup)) && (regionName.includes(searchRegion)) && (rankName.includes(searchRank))) {
                    $("#gridItems").jqxGrid('showrow', row);// 如果包含，显示这一行
                } else {
                    $("#gridItems").jqxGrid('hiderow', row);// 如果不包含，隐藏这一行
                }
            }
        }

        function btn_change(e) {
            window.parent.newTabPage("价格策略", `BaseInfo/PriceStrategy`, window);
        }
        function onFormSaved(msg) {
            bw.toast("保存成功");
        }
        /*function btnSave_Clicked() {
            var msg = 'ok';
            var rows = $("#gridItems").jqxGrid('getrows');
            console.log(rows);
            var sheetRows = new Array;
            for (var i = 0; i < rows.length; i++) {
                var sheetRow = {};
                var row = rows[i];
                if (!row.region_id && !row.rank_id && !row.group_id) {
                    msg = '请选定客户类别';
                    break;
                }
                else {
                    if (row.rank_id) sheetRow["rank_id"] = row.rank_id;
                    if (row.group_id) sheetRow["group_id"] = row.group_id;
                    if(row.region_id) sheetRow["region_id"] = row.region_id;
                    if (!row.price1 && !row.price2 && !row.price3) {
                        msg = '请设定价格方案'; break;
                    } else {
                        if (row.price1) { sheetRow["price1"] = row.price1; }
                        if (row.price2) { sheetRow["price2"] = row.price2; }
                        if (row.price3) { sheetRow["price3"] = row.price3; }
                    }
                    sheetRows[i] = sheetRow;
                }

            }
            if (sheetRows.length == 0) {
                msg = '请输入至少一行价格方案设置';
            }
            if (msg != 'ok') {
                bw.toast(msg, 5000);
            }
            else {
                var formData = {
                    sheetRows: sheetRows,
                    operKey: window.g_operKey
                };
                $.ajaxSetup({ contentType: "application/json" });
                $.post(`/api/PriceClassSetting/saveTable`, JSON.stringify(formData)).then(res => {
                    if (res) {
                        bw.toast("保存成功", 5000);
                    }
                    else bw.toast(data.msg, 5000);

                });

            }
        }*/


    </script>

</body>
</html>