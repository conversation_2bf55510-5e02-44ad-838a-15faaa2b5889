﻿@page
@model ArtisanManage.BMapModel
@{
    Layout = null;
}


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script type="text/javascript" src="//api.map.baidu.com/api?type=webgl&v=1.0&ak=@Html.Raw(Model.BaiduKey)"></script>
    <title>门店位置</title>
    <style>
        html, body, #allmap {
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div id='allmap'></div>
    <script>
        var lng = @Model.HttpContext.Request.Query["longitude"]?? 116.331398;
        var lat = @Model.HttpContext.Request.Query["latitude"]?? 39.897445;
        var map = new BMapGL.Map("allmap");
        var new_point = new BMapGL.Point(lng, lat);

        map.centerAndZoom(new_point, 15);
        map.enableScrollWheelZoom(true);



        window.onload = function () {
                map.clearOverlays();
                var marker = new BMapGL.Marker(new_point);  // 创建标注
                map.addOverlay(marker);              // 将标注添加到地图中
                map.panTo(new_point);
            
        }
    </script>
</body>
</html>