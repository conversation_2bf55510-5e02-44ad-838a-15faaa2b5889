using ArtisanManage.Models;
using ArtisanManage.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace ArtisanManage.Pages.BaseInfo
{
    public class CustomerLivenessModel : PageQueryModel
    { 
        public bool ForSelect = false;
        public bool MultiSelect = false;

        public CustomerLivenessModel(CMySbCommand cmd) : base(Services.MenuId.customerLiveness)
        {
            this.cmd = cmd;
            this.PageTitle = "客户活跃度";
            DataItems = new Dictionary<string, DataItem>()
            {
                //{"searchString",new DataItem(){Title="输入天数", SqlFld="s.sup_name",CompareOperator="like"}},

                {"seller_id",new DataItem()
                {
                    Title="业务员",FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="visit_seller,sale_seller",SqlForOptions=CommonTool.selectSellers,
                    //DealQueryItem = sellerId =>
                    //{
                    //    if (string.IsNullOrWhiteSpace(sellerId)) return "";
                    //    SQLVariable1 = $" and  = '{sellerId}' ";

                    //    return "";
                    //}
                } },
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="supcust_id",QueryByLabelLikeIfIdEmpty=true,
                            SqlForOptions=CommonTool.selectSupcust } },
                //{"status",new DataItem(){Title = "状态",FldArea="divHead",LabelFld = "cls_status_name", LabelInDB = false, Value = "normal", Label = "正常",ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "normal",

                //     Source = @"[{v:'normal',l:'正常',condition:""(status = '1' or status is null)""},
                //               {v:'stop',l:'停用',condition:""status = '0' ""},
                //               {v:'all',l:'所有',condition:""true""}]"


                // }},
                {"group_id",new DataItem(){Title="渠道",FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
                {
                    "unvisitDay", new DataItem(){ FldArea="divHead",Title = "未拜访天数大于",Width="100", DealQueryItem = day =>
                    {
                        SQLVariable2 = $" and (unvisitday = -1 or unvisitday >= {day}) ";
                        return "";
                    }}
                },
                {
                    "unsaleDay", new DataItem(){ FldArea="divHead",Title = "未销售天数大于", Width="100",DealQueryItem = day =>
                    {
                        SQLVariable3 = $" and (unsaleday = -1 or unsaleday >= {day}) ";
                        return "";
                    }}
                },
                {"orderByVisitTime",new DataItem(){FldArea="divHead",Title="按上次拜访排序",CtrlType="jqxCheckBox",CompareOperator="=",ForQuery=false}},
                {"has_arrears", new DataItem(){FldArea="divHead",Title="是否欠款",CtrlType="jqxCheckBox",CompareOperator="=",SqlFld="arrears_amount", DealQueryItem = val => {
                    if (val == "true") SQLVariable4 = " and arrears_amount > 0 ";
                    else if (val == "false") SQLVariable4 = " and (arrears_amount = 0 or arrears_amount is null) ";
                    return "";
                }}},
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                    "gridItems",new QueryGrid()
                    {
                       HasCheck=true,KeepCheckForQueries=false,
                        ContextMenuHTML="<ul><li id='BatchOperation'>批量操作</li></ul>",
                        IdColumn="supcust_id",TableName="info_supcust",
                        ShowContextMenu = true,
                        Columns = new Dictionary<string, DataItem>()
                        {
                            {"supcust_id",new DataItem(){Title="客户编号", Width="80",Hidden=true}},
                            {"seller_name",new DataItem(){Title="业务员",Width="200",
                                SqlFld=@"(case when visit_seller=sale_seller then visit_name 
                                               when visit_seller is null and sale_seller is not null then concat('销:',sale_name)
                                               when visit_seller is not null and sale_seller is null then concat('访:',visit_name)
                                               when visit_seller is null and sale_seller is null then '' 
                                                else concat('销:',sale_name,';','访:',visit_name) end )"} },
                            {"sup_name",new DataItem(){Title="客户名称", Width="200"}},
                            {"group_name",new DataItem(){Title="渠道", Width="150"}},
                            {"region_name",new DataItem(){Title="片区", Width="150"}},
                            {"lastsellday",new DataItem(){Title = "上次销售时间",Width = "200",Sortable = true}},
                            {"lastvisitday",new DataItem(){Title = "上次拜访时间",Width = "200"}},
                            {"total_amount",new DataItem(){Title = "上次销售金额",Width = "100",CellsAlign="right"}},
                            {"sheets",new DataItem(){Title = "查销售记录",Width = "150",Linkable=true,CellsAlign ="right",SqlFld="'销售记录'",SaveToDB=false}},
                            {"day_name",new DataItem(){Title = "日程",Width = "200"}},
                            {"last_gathering_time", new DataItem(){Title="上次收款时间", Width="150"}},
                            {"arrears_amount", new DataItem(){Title="尚欠金额", Width="120", CellsAlign="right"}},
                        },
                        QueryFromSQL=$@" from (SELECT s.company_id,s.supcust_id,sup_name,group_id,group_name,r.region_id,region_name,other_region,sheet_id,sheet_no,
                            (CASE WHEN happen_time IS NULL THEN '从未销售' ELSE (concat ( ( happen_time :: DATE ), ' (', ( DATE ( now( ) ) - DATE ( happen_time :: DATE ) ), '天前）' )  ) END) lastsellday,
			                ( happen_time :: DATE ) lastsaledate,
                            (CASE WHEN happen_time IS NULL THEN -1 ELSE ( DATE ( now( ) ) - DATE ( happen_time :: DATE )) END) unsaleday,
			                (CASE WHEN start_time IS NULL THEN '从未拜访' ELSE concat ( ( start_time :: DATE ), ' (', ( DATE ( now( ) ) - DATE ( start_time :: DATE ) ), '天前）' ) END ) lastvisitday,
				            ( start_time :: DATE ) lastvisitdate,
                            (CASE WHEN start_time IS NULL THEN -1 ELSE ( DATE ( now( ) ) - DATE ( start_time :: DATE ) ) END ) unvisitday,
				            sv.seller_id visit_seller,sm.seller_id sale_seller,visit_name,day_name,sale_name,COALESCE ( round( total_amount :: NUMERIC, 2 ), 0 ) AS total_amount,
                            gather.last_gathering_time, COALESCE(gather.arrears_amount,0) as arrears_amount
			                FROM
				            ( SELECT * FROM info_supcust WHERE company_id = ~COMPANY_ID ) s
				            LEFT JOIN (select mv.supcust_id,mv.start_time,seller_id 
                                    from (SELECT supcust_id, MAX(start_time) start_time FROM sheet_visit where company_id = ~COMPANY_ID GROUP BY supcust_id ) mv 
                                    left join sheet_visit v on v.supcust_id = mv.supcust_id and mv.start_time = v.start_time) sv ON s.supcust_id = sv.supcust_id
				            LEFT JOIN (SELECT ms.happen_time,seller_id,ms.supcust_id,money_inout_flag*total_amount total_amount,sm.sheet_id,sheet_no  
						                    from (select supcust_id,max(happen_time) happen_time,max(sheet_id) sheet_id from sheet_sale_main where company_id = ~COMPANY_ID AND red_flag IS NULL AND approve_time IS NOT NULL  group by supcust_id) ms
									        left join  sheet_sale_main sm on company_id = ~COMPANY_ID AND sm.supcust_id = ms.supcust_id  and ms.sheet_id = sm.sheet_id
                                        ) sm ON s.supcust_id = sm.supcust_id
				            LEFT JOIN ( SELECT region_id, region_name FROM info_region WHERE company_id = ~COMPANY_ID ) r ON s.region_id = r.region_id
				            LEFT JOIN ( SELECT group_id, group_name FROM info_supcust_group WHERE company_id = ~COMPANY_ID ) G ON G.group_id = s.sup_group
				            LEFT JOIN ( SELECT rank_id, rank_name FROM info_supcust_rank WHERE company_id = ~COMPANY_ID ) sr ON sr.rank_id = s.sup_rank 
				            LEFT JOIN ( SELECT oper_id, oper_name visit_name FROM info_operator WHERE company_id = ~COMPANY_ID ) vo ON vo.oper_id = sv.seller_id
				            LEFT JOIN ( SELECT oper_id, oper_name sale_name FROM info_operator WHERE company_id = ~COMPANY_ID ) so ON so.oper_id = sm.seller_id 
                            LEFT JOIN (
                                SELECT supcust_id, MAX(happen_time) AS last_gathering_time, SUM(left_amount) AS arrears_amount
                                FROM sheet_get_arrears_main
                                WHERE company_id = ~COMPANY_ID AND (red_flag IS NULL OR red_flag=0)
                                GROUP BY supcust_id
                            ) gather ON s.supcust_id = gather.supcust_id
	LEFT JOIN ( SELECT
	vc.supcust_id,
	
	string_agg(day_name ,',') day_name
FROM
	info_visit_day_client vc
	LEFT JOIN info_visit_day vd ON vc.day_id = vd.day_id 
	AND vd.company_id = ~COMPANY_ID 
WHERE
	vc.company_id = ~COMPANY_ID
	GROUP BY 
	vc.supcust_id ) ivc ON s.supcust_id = ivc .supcust_id 


			                WHERE s.company_id = ~COMPANY_ID  AND supcust_flag like '%C%' and (s.status is null or s.status='1')  ORDER BY s.supcust_id, lastsaledate DESC  ) T where  company_id= ~COMPANY_ID   ~SQL_VARIABLE2 ~SQL_VARIABLE3 ~SQL_VARIABLE4 " ,
                        QueryOrderSQL=" order by lastsellday,lastvisitday "
                    }
                }
            };
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            var orderByVisitTime = DataItems["orderByVisitTime"].Value;
            if (orderByVisitTime.ToLower() == "true") Grids["gridItems"].QueryOrderSQL = "order by lastvisitday,lastsellday ";
        }


        public async Task OnGet(string forSelect,string multiSelect)
        {
            await InitGet(cmd);
            ForSelect = forSelect == "1";
            MultiSelect = multiSelect == "1";
        } 


    }


    [Route("api/[controller]/[action]")]
    public class CustomerLivenessController : QueryController
    {  
        public CustomerLivenessController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            CustomerLivenessModel model = new CustomerLivenessModel(this.cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {// string gridID,int startRow,int endRow,bool bNewQuery){

            CustomerLivenessModel model = new CustomerLivenessModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            CustomerLivenessModel model = new CustomerLivenessModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
