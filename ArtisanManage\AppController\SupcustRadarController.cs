﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    [Route("AppApi/[controller]/[action]")]
    public class SupcustRadarController :QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public SupcustRadarController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }
        [HttpPost]
        public async Task<JsonResult> RadarLink([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID);

            SupcustRadarService supcustRadarService = new SupcustRadarService(this.cmd);
         string msg=  await supcustRadarService.LinkRadarName(companyID.ToString(), data.supcustID.ToString(), data.radarName.ToString());
            return Json(new
            {
                msg
            });
        }
        [HttpPost]
        public async Task<JsonResult> RadarSearch([FromBody]dynamic data)
        {

            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID);
            if (companyID.IsInvalid())
            {
                return Json(new { });
            }
            SupcustRadarService supcustRadarService = new SupcustRadarService(this.cmd);
            RadarQueryCondition radarQueryCondition = new RadarQueryCondition();
            radarQueryCondition.location = location(data.longitude.ToString(), data.latitude.ToString());
            radarQueryCondition.pageNum = data.pageNum;
            radarQueryCondition.pageSize = data.pageSize;
            radarQueryCondition.query = data.query;
            radarQueryCondition.radius = data.radius;
            string res=await supcustRadarService.SearchAll(_httpClientFactory, radarQueryCondition);
            dynamic radarSupNames = await supcustRadarService.GetNearRadarSupNames(companyID, data.longitude.ToString(), data.latitude.ToString(), Double.Parse(radarQueryCondition.radius));
            return Json(new { res,radarSupNames});
        }
        public string location(string longitude,string latitude)
        {
            return latitude + "," +longitude ;
        }
        [HttpGet]
        public async Task<JsonResult> GetSupcustTypes(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            if (companyID.IsInvalid())
            {
                return Json(new { });
            }
            SQLQueue QQ = new SQLQueue(cmd);
            var sql = $@"SELECT id,p_id,sup_type from info_supcust_type where status=1";
            QQ.Enqueue("getTypeSQL", sql);
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "getTypeSQL")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }

            }
            QQ.Clear();
            return Json(data);
        }
    }
}
