﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using static ArtisanManage.Pages.Report.VipPointsDetailModel;

namespace ArtisanManage.Pages.Report;

#pragma warning disable CS1998
public class VipPointsSummaryModel : PageQueryModel
{
    public VipPointsSummaryModel(CMySbCommand cmd) : base(MenuId.vipPointsSummary)
    {
        this.cmd = cmd;
        PageTitle = "会员积分汇总表";
        UsePostMethod = true;
        NotQueryHideColumn = false;

        DataItems = new Dictionary<string, DataItem>()
        {
            // 用于过滤会员卡/客户的查询条件
            { "supcust_id",   new DataItem(){ Title="客户", SqlFld="s.supcust_id",FldArea="divHead",LabelFld="sup_name",ButtonUsage="list",Checkboxes=true,QueryByLabelLikeIfIdEmpty=true, CompareOperator="=",
                SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID" }},
            { "vip_level_id", new DataItem(){ Title="会员等级", SqlFld="l.vip_level_id",FldArea="divHead",LabelFld="l.vip_level_name",ButtonUsage="list",Checkboxes=true,CompareOperator="=",
                SqlForOptions = "select vip_level_id as v,vip_level_name as l from vip_level where company_id = ~COMPANY_ID" }},
            { "other_region", new DataItem(){ Title="片区", FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                SqlForOptions = "select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index " }},
            { "group_id",     new DataItem(){ Title="渠道",FldArea="divHead", Checkboxes=true,LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                SqlForOptions = "select group_id as v,group_name as l from info_supcust_group" }},
            { "sup_rank",     new DataItem(){ Title="等级",FldArea="divHead",Checkboxes=true,LabelFld="rank_name",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",CompareOperator="=",
                SqlForOptions = "select rank_id as v,rank_name as l from info_supcust_rank" }},
            // 用于过滤积分变动的查询条件
            { "startDay",     new DataItem(){ Title="开始日期", ForQuery=false, FldArea="divHead", CtrlType="jqxDateTimeInput", Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
            { "endDay",       new DataItem(){ Title="结束日期", ForQuery=false, FldArea="divHead", CtrlType="jqxDateTimeInput", Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                JSDealItemOnSelect="var s=$('#endDay').jqxDateTimeInput('val').toString();if(s!=''){s=s.replace('00:00','23:59');$('#endDay').jqxDateTimeInput('val',s);}" }},
            { "rw_flag",      QueryOptionRwFlag }
        };

        Grids = new Dictionary<string, QueryGrid>()
        {{
            "gridItems", new QueryGrid()
            {
                ShowAggregates=true,
                Sortable=true,
                Columns = new Dictionary<string, DataItem>()
                {
                    { "supcust_id",     new() { Title="客户ID", SqlFld="s.supcust_id",Width="150",Hidden=true,HideOnLoad=true } },
                    { "sup_name",       new() { Title="客户名称", SqlFld="s.sup_name",Width="200",Linkable=true,Sortable=true,IsChinese=true,Pinned=true } },
                    { "vip_level_id",   new() { Title="会员卡等级ID", SqlFld="l.vip_level_id",Width="150",Hidden=true,HideOnLoad=true } },
                    { "vip_level_name", new() { Title="会员卡等级", SqlFld="l.vip_level_name",Width="100",Sortable=true,IsChinese=true } },
                    { "points_balance", new() { Title="会员卡剩余积分", SqlFld="c.points_balance",Width="120",Sortable=true,ShowSum=true} },
                    { "history_points", new() { Title="会员卡累计积分", SqlFld="c.history_points",Width="120",Sortable=true,ShowSum=true} },
                    { "total_sheet_points",    new() { Title="下单获取积分", SqlFld="log.total_sheet_points", Width="160", Sortable=true, ShowSum=true }},
                    { "total_redemption",      new() { Title="兑换花费积分", SqlFld="log.total_redemption", Width="160", Sortable=true, ShowSum=true }},
                    { "total_expired_points",  new() { Title="过期积分", SqlFld="log.total_expired_points", Width="160", Sortable=true, ShowSum=true }},
                    { "total_reded_points_x",  new() { Title="销售单据红冲（积分回收）", SqlFld="log.total_reded_points_x", Width="250", Sortable=true, ShowSum=true }},
                    { "total_reded_points_xd", new() { Title="兑换订单红冲（积分退回）", SqlFld="log.total_reded_points_xd", Width="250", Sortable=true, ShowSum=true }},
                },
                QueryFromSQL = $@"
                    FROM
                        vip_card c
                        left join (
                            with ranked_logs as (
                                select
                                    company_id,
                                    client_id,
                                    point_group_id,
                                    create_time,
                                    retail_whole_flag,
                                    point_change_type,
                                    sheet_type,
                                    point_group_total,
                                    ROW_NUMBER() OVER (PARTITION BY company_id, client_id, point_group_id ORDER BY create_time DESC) as rn
                                from
                                    vip_point_change_log
                                where
                                    company_id = ~COMPANY_ID
                                    and create_time > '~START_DAY'
                                    and create_time < '~END_DAY'
                            )
                            select
                                company_id,
                                client_id,
                                max(create_time) as create_time,
                                max(retail_whole_flag) as retail_whole_flag,
                                sum(case when point_change_type = 'sheet_points' then point_group_total else 0 end) as total_sheet_points,
                                sum(case when point_change_type = 'redemption' then point_group_total else 0 end) as total_redemption,
                                sum(case when point_change_type = 'point_expire' then point_group_total else 0 end) as total_expired_points,
                                sum(case when point_change_type = 'sheet_reded' and sheet_type = 'X' then point_group_total else 0 end) as total_reded_points_x,
                                sum(case when point_change_type = 'sheet_reded' and sheet_type = 'XD' then point_group_total else 0 end) as total_reded_points_xd
                            from
                                ranked_logs
                            where
                                rn = 1  -- 只选择每个 `point_group_id` 最新的一条记录
                            group by
                                company_id, client_id
                        ) log on c.company_id = log.company_id and c.client_id = log.client_id
                        left join info_supcust s on c.client_id = s.supcust_id and s.company_id = ~COMPANY_ID
                        left join vip_level l on c.vip_level_id = l.vip_level_id and l.company_id = ~COMPANY_ID
                    WHERE
                        c.company_id = ~COMPANY_ID
                ",
                QueryGroupBySQL = "",
                QueryOrderSQL = ""
            }
        }};
    }

    public override async Task OnQueryConditionGot(CMySbCommand cmd)
    {
        var startDay = DataItems["startDay"].Value;
        var endDay = DataItems["endDay"].Value;
        SQLVariables["startDay"] = startDay;
        SQLVariables["endDay"] = endDay;

        var sql = Grids["gridItems"].QueryFromSQL;
        sql = sql.Replace("~START_DAY", startDay);
        sql = sql.Replace("~END_DAY", endDay);
        Grids["gridItems"].QueryFromSQL = sql;
    }
    public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
    {
    }

    public async Task OnGet()
    {
        await InitGet(cmd);
    }
}

[Route("api/[controller]/[action]")]
public class VipPointsSummaryController : QueryController
{
    public VipPointsSummaryController(CMySbCommand cmd)
    {
        this.cmd = cmd;
    }

    [HttpGet]
    public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
    {
        var model = new VipPointsSummaryModel(cmd);
        var response = await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
        return response;
    }

    [HttpPost]
    public async Task<object> GetQueryRecords([FromBody] dynamic data)
    {
        var model = new VipPointsSummaryModel(cmd);
        object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
        return records;
    }

    [HttpPost]
    public async Task<ActionResult> ExportExcel()
    {
        string sParams = Request.Form["params"];
        sParams = System.Web.HttpUtility.UrlDecode(sParams);
        dynamic queryParams = JsonConvert.DeserializeObject(sParams);

        var model = new VipPointsSummaryModel(cmd);
        return await model.ExportExcel(Request, cmd, queryParams);
    }
}
#pragma warning restore CS1998
