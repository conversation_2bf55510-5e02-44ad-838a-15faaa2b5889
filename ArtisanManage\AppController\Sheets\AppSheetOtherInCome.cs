﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{



    [Route("AppApi/[controller]/[action]")]
    public class AppSheetOtherInCome : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public AppSheetOtherInCome(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }


        

        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID);
            SheetFeeOut sheet = new SheetFeeOut(SHEET_FEE_OUT.NOT_OUT, LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            var sql = @$"select sub_id,sub_name,py_str,sub_code from cw_subject where company_id = {companyID} and sub_type = 'ZC' and sub_code>66
            and (sub_id::text IN (
            SELECT 
                json_array_elements_text(avail_pay_ways) AS individual_value 
            FROM 
                info_operator 
            WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
            )
           OR
          (   SELECT 
                COUNT(*) 
            FROM 
                info_operator 
            WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
           );";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("feeOutInfo", sql);
            sql = @$"select sub_id,sub_name,py_str,sub_code from cw_subject where company_id = {companyID} and sub_type = 'QTSR' and sub_code>66
            and (sub_id::text IN (
                        SELECT 
                            json_array_elements_text(avail_pay_ways) AS individual_value 
                        FROM 
                            info_operator 
                        WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
                        )
                       OR
                      (   SELECT 
                            COUNT(*) 
                        FROM 
                            info_operator 
                        WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
                       )
            ;";
            QQ.Enqueue("feeInInfo", sql);
            sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id = {companyID} and sub_type in ('QT','YF') 
            and (sub_id::text IN (
                        SELECT 
                            json_array_elements_text(avail_pay_ways) AS individual_value 
                        FROM 
                            info_operator 
                        WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
                        )
                       OR
                      (   SELECT 
                            COUNT(*) 
                        FROM 
                            info_operator 
                        WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
                       )
            order by sub_type,order_index; ";
            QQ.Enqueue("payways", sql);
            List<ExpandoObject> feeOut = null;
            List<ExpandoObject> feeIn = null;
            List<ExpandoObject> payways = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "feeOutInfo")
                {
                    feeOut = CDbDealer.GetRecordsFromDr(dr, false);
                }
                if (sqlName == "feeInInfo")
                {
                    feeIn = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "payways")
                {
                    payways = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            string result = "OK";
            string msg = "";
            sheet.OperKey = null;
            return Json(new { result, msg, sheet, feeOut, feeIn, payways });
        }

        [HttpPost]
        public async Task<JsonResult> Save([FromBody] SheetFeeOut sheet)
        {
            var currentTime = DateTime.Now.ToText();
            sheet.Init();
            string msg = await sheet.Save(cmd);
            string result = msg == "" ? "OK" : "Error";

            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, currentTime });
        }

        public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64, string companyID)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, companyID);
            return result;
        }

        
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            string msg = "";
            SheetFeeOut sheet = null;  
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetFeeOut>(sSheet);
                if (dSheet.appendixPhotos != null)
                {
                    List<string> appendixBase64s = new List<string>();
                    foreach (string appendixPhoto in dSheet.appendixPhotos)
                    {
                        appendixBase64s.Add(appendixPhoto);
                    }
                    sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());

                }
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetFeeOut.Submit " + msg + " sSheet:" + sSheet);
                return new JsonResult(new { result = "Error", msg });
            }

            var currentTime = DateTime.Now.ToText();
            if (sheet != null)
            {
                sheet.Init();
                msg = await sheet.SaveAndApprove(cmd);
            }
            string result = msg == "" ? "OK" : "Error";

            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, currentTime });
        }

        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string result = "OK"; string msg = null;
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            if (sheetID == null || sheetID == "")
            {
                return new JsonResult(new { result = "Error", msg = "请指定单据号" });
            }

            var currentTime = DateTime.Now.ToText();
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetFeeOut sheet = new SheetFeeOut(SHEET_FEE_OUT.NOT_OUT, LOAD_PURPOSE.APPROVE);
            msg = await sheet.Red(cmd, companyID, sheetID, operID, "");
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });

        }
       
        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetFeeOut sheet = new SheetFeeOut(SHEET_FEE_OUT.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return Json(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }


    }
}