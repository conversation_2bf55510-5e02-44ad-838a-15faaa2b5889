using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class AccountHistoryModel : PageQueryModel
    {
        public AccountHistoryModel(CMySbCommand cmd) : base(MenuId.clientBusinessHistory)
        {
            this.cmd = cmd;
            this.PageTitle = "客户往来账";
           
            DataItems = new Dictionary<string, DataItem>()
            { 
                {"startDay",new DataItem(){Title="开始日期", ForQuery=false, FldArea = "divHead",CtrlType="jqxDateTimeInput", SqlFld="cah.approve_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", ForQuery=false, FldArea = "divHead", CtrlType="jqxDateTimeInput", SqlFld="cah.approve_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }"
                }},
                {"queryTimeAccord",new DataItem(){FldArea="divHead",Title="时间类型",LabelFld = "time_status_name",ButtonUsage = "list",CompareOperator="=",Value="byApproveTime",Label="审核时间",ForQuery=false, AutoRemember=true,
                    Hidden = true,
                    Source = @"[{v:'byApproveTime',l:'审核时间'},
                                   {v:'byHappenTime',l:'交易时间'},  
                                     ]"
                }},
                {"sub_type",new DataItem(){FldArea="divHead",Title="往来类型",ButtonUsage="list",LabelFld="sub_type_name",SqlFld="cah.sub_type",
                    Source = "[{v:'QK',l:'欠款'},{v:'YS',l:'预收款'},{v:'',l:'所有'}]",CompareOperator="="}},
                
                {"sheet_type",  new DataItem(){FldArea="divHead",Title="单据类型",ButtonUsage="list",LabelFld="sheet_type_name",Checkboxes = true,SqlFld="cah.sheet_type_query",
                    Source = "[{v:'X',l:'销售单'},{v:'XD',l:'销售订单'},{v:'T',l:'退货单'},{v:'TD',l:'退货订单'},{v:'DH',l:'定货单'},{v:'DHTZ',l:'定货调整单'},{v:'SK',l:'收款单'},{v:'YS',l:'预收款单'},{v:'ZC',l:'费用支出单'},{v:'',l:'所有'}]",CompareOperator="="}},
                    //Source = "[{v:'X',l:'销售单'},{v:'XT',l:'销售退货单'},{v:'XD',l:'销售订单'},{v:'TD',l:'退货订单'},{V:'DH',l:'定货单'},{v:'SK',l:'收款单'},{v:'YS',l:'预收款单'},{v:'',1:'所有'}]",CompareOperator="=" } },
                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",Checkboxes=true, LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},
                {"supcust_id",CommonTool.GetDataItem("supcust_id",new DataItemChange{ForQuery=false}) 
                },
                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
                {"group_id",new DataItem(){Title="渠道",FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                {"showImmediatePaySheet",new DataItem(){FldArea="divHead",Title="包含现结单据",CtrlType="jqxCheckBox",ForQuery=false,Value="false",
                    DealQueryItem=showImmediatePaySheet=>{
                        if(showImmediatePaySheet.ToLower()!="true")
                           Grids["gridItems"].QueryFromSQL2="";
                        return "";
                    }
                }},
            //{"sheet_type",new DataItem(){Title="单据类型",FldArea="divHead",LabelFld="sheet_type_name",ButtonUsage="list",Checkboxes = true,CompareOperator="=",
            //        Source = @"[{v:'DHTZ',l:'销售单'},{v:'T',l:'退货单'},{v:'YS',l:'预收款单'},{v:'DH',l:'定货单'},
            //                    {v:'DR',l:'定货调整单'},{v:'SK',l:'收款单'},{v:'XD',l:'销售订单'},{v:'TD',l:'退货订单'},{v:'',l:'所有'}]"}},
                 {"sub_id",new DataItem(){Title="科目",FldArea="divHead", LabelFld="sub_name",ButtonUsage="list",CompareOperator="=",SqlFld="payway1_id,payway2_id,payway3_id",ForQuery=false,
                SqlForOptions = "SELECT s.sub_id as v ,s.sub_name as l  FROM cw_subject s WHERE s.company_id =~COMPANY_ID AND sub_type IN ( 'YS' ) ORDER BY order_index"}},
             
             {"showRedSheet",new DataItem(){FldArea="divHead",Title="显示红冲单",CtrlType="jqxCheckBox",ForQuery=false,Value="true",AutoRemember=true}},
             {"showSaleSheetDetail",new DataItem(){FldArea="divHead",Title="显示销售明细",CtrlType="jqxCheckBox",QueryOnChange=true,ForQuery=false,Value="false",AutoRemember=true}},
             {"sys_hide_duplicate_cells",new DataItem(){
                    FldArea="divHead", Title="主表展示1行", LabelFld = "", CtrlType="jqxCheckBox",Hidden=true,
                     Value="false",Label="交易时间",ForQuery=false, AutoRemember=true
                }},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      Sortable=true,
                      ShowAggregates=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",new DataItem(){IsIDColumn = true,Title = "",Hidden = true,SqlFld = "sheet_id",HideOnLoad = true} },
                       {"row_index",new DataItem(){IsIDColumn = false,Hidden=true,HideOnLoad = true}},
                       {"sheet_no",   new DataItem(){MightDuplicate=true,HideDuplicateCells=true,Title="单据编号",    Width="200",Linkable = true, }},
                       {"sub_name",   new DataItem(){Title="科目名称",    Width="200"}},
                       {"make_brief", new DataItem() { Title = "备注", Width = "100", Hidden = false, SqlFld = "make_brief" }},
                       {"seller_name", new DataItem(){MightDuplicate=true,HideDuplicateCells=true,Title="业务员",  Width="80",Sortable=true}},
                       {"sup_name",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true,Title="客户名称",  Width="150" }},
                       {"sheet_type",   new DataItem(){MightDuplicate=true,HideDuplicateCells=true,Title="单据类型",    Width="100",
                           SqlFld="(CASE WHEN sheet_type='X' THEN '销售单' WHEN sheet_type='T' THEN '退货单' WHEN sheet_type='YS' THEN '预收款单' when sheet_type='DH' then '定货单' when sheet_type like 'DHTZ%' then '定货调整单' WHEN sheet_type='SK' THEN '收款单' when sheet_type='XD' then '销售订单' when sheet_type='TD' then '退货订单'when sheet_type='ZC' then '费用支出单' END)"}},
                        {"sheet_type_query", new DataItem(){Title="sheet_type_query", Hidden=true, HideOnLoad = true } },
                        { "item_name",     new DataItem() { Title = "商品名称", Hidden = true, HideOnLoad = true,Width = "100"  }},
                        { "real_price",     new DataItem() { Title = "单价", Hidden = true, HideOnLoad = true,Width = "60"  }},
                        { "orig_price",     new DataItem() { Title = "原价", Hidden = true, HideOnLoad = true,Width = "60" }},
                        { "quantity",     new DataItem(){Title="数量",  Hidden = true, HideOnLoad = true, Width = "60"}},
						{"qty_no_unit",     new DataItem() {Title="纯数量", Hidden=true,HideOnLoad = true,Width="70",
							FuncDealMe=(value)=>{return value=="0"?"":value; },
						   }},
						{ "unit_no",     new DataItem(){Title="单位",  Hidden = true, HideOnLoad = true, Width = "60"}},

						 {"left_amount", new DataItem(){MightDuplicate = true, HideDuplicateCells = true, Hidden=true,HideOnLoad = true,Title ="尚欠金额",  Width="100"}},
                       {"sheet_status", new DataItem(){Title="状态",  Width="55",SqlFld="(case when red_flag='2' then '红字单' when red_flag='1' then '已红冲'  END)"}},
                       {"happen_time", new DataItem(){MightDuplicate=true,HideDuplicateCells=true,Title="交易时间",    Width="200",SqlFld="cah.happen_time", Sortable = true}},
                       {"approve_time", new DataItem(){MightDuplicate=true,HideDuplicateCells=true,Title="审核时间",    Width="200",SqlFld="cah.approve_time",Sortable=true}},
                     
                         {"total_amount",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true,Title="单据金额",  Width="120",ShowSum=true,CellsAlign="right"}},
                       {"now_disc_amount", new DataItem(){MightDuplicate=true,HideDuplicateCells=true,Title="优惠",  Width="100",ShowSum=true,CellsAlign="right" }},
                       {"now_pay_amount",  new DataItem(){MightDuplicate=true,HideDuplicateCells=true,Title="收款",  Width="100",ShowSum=true ,SqlFld="now_pay_amount - COALESCE(prepay_amount,0) ",CellsAlign="right"}}, 
                       {"arrears_add",     new DataItem(){Title="应收款增",  Width="110",ShowSum=true,CellsAlign="right",
                           SqlFld=@"(CASE WHEN qk_amount>0 THEN qk_amount
                                          when sheet_type = 'X' and sk_amount>0 THEN sk_amount END)"}},
                       {"arrears_reduce",     new DataItem(){Title="应收款减",  Width="110",ShowSum=true,CellsAlign="right",
                           SqlFld=@"(CASE WHEN qk_amount<0 THEN (-qk_amount)
                                          when sheet_type = 'X' and sk_amount<0 THEN (-sk_amount) END)"}},
                       {"arrears_balance",     new DataItem(){Title="应收款余额",  Width="110",CellsAlign="right",
                           SqlFld="(CASE when qk_balance is not null then qk_balance when sk_balance is not null THEN sk_balance END)"}},
                       //{"net_receivable",     new DataItem(){Title="应收款余额",  Width="80",SqlFld="gad.left_amount"}},
                       {"prepay_add",     new DataItem(){Title="预收款增",  Width="110",ShowSum=true,CellsAlign="right",
                           SqlFld="(CASE WHEN ys_amount>0 THEN ys_amount END)"}},
                       {"prepay_reduce",     new DataItem(){Title="预收款减",  Width="110",ShowSum=true,CellsAlign="right",
                           SqlFld="(CASE WHEN ys_amount<0 THEN (-ys_amount) END)"}},
                       {"prepay_balance",     new DataItem(){Title="预收款余额",  Width="110",CellsAlign="right",
                           SqlFld="ys_balance::text "}},
                        {"prepay_total_balance",     new DataItem(){Title="预收款总余额",  Width="110",CellsAlign="right",
                           SqlFld="ys_total_balance::text "}},
                     },
                     QueryFromSQL=@"
FROM
(
    SELECT cah.*,left_amount,
    row_index,item_name, real_price, orig_price,quantity,unit_no, qty_no_unit,
    (case when cah.sheet_type like 'DHTZ%' then 'DHTZ' else cah.sheet_type end) as sheet_type_query,
    (
          case when sm.sheet_no is not null then sm.sheet_no when pp.sheet_no is not null then pp.sheet_no when gam.sheet_no is not null then gam.sheet_no when ad.sheet_no is not null then ad.sheet_no when ad_pw1.sheet_no is not null then ad_pw1.sheet_no when ad_pw2.sheet_no is not null then ad_pw2.sheet_no when fm.sheet_no is not null then fm.sheet_no else null end
    )  sheet_no,
(
          case when sm.make_brief is not null then sm.make_brief when pp.make_brief is not null then pp.make_brief when gam.make_brief is not null then gam.make_brief when ad.make_brief is not null then ad.make_brief when ad_pw1.make_brief is not null then ad_pw1.make_brief when ad_pw2.make_brief is not null then ad_pw2.make_brief when fm.make_brief is not null then fm.make_brief else null end
    )  make_brief,
    (
         case when sm.total_amount is not null then sm.total_amount when pp.total_amount is not null then pp.total_amount when gam.sheet_amount is not null then gam.sheet_amount when ad.total_amount is not null then ad.total_amount when ad_pw1.total_amount is not null then ad_pw1.total_amount when ad_pw2.total_amount is not null then ad_pw2.total_amount when ad_pw1.total_amount is not null then ad_pw1.total_amount when ad_pw2.total_amount is not null then ad_pw2.total_amount	WHEN fm.total_amount IS NOT NULL THEN  fm.total_amount  else null end
    ) total_amount,
    (
         case when sm.now_disc_amount is not null then sm.now_disc_amount when pp.now_disc_amount is not null then pp.now_disc_amount when gam.now_disc_amount is not null then gam.now_disc_amount else null end
    ) now_disc_amount,
    (
            case when sm.now_pay_amount is not null then sm.now_pay_amount when pp.now_pay_amount is not null then pp.now_pay_amount when gam.now_pay_amount is not null then gam.now_pay_amount  when fm.now_pay_amount is not null then fm.now_pay_amount else null end
    ) now_pay_amount,
    (
        case when sm.seller_id is not null then sm.seller_id when pp.seller_id is not null then pp.seller_id when gam.seller_id is not null then gam.seller_id when ad.seller_id is not null then ad.seller_id when ad_pw1.seller_id is not null then ad_pw1.seller_id when ad_pw2.seller_id is not null then ad_pw2.seller_id when fm.seller_id is not null then fm.seller_id else null end
    )  seller_id,
    (
        case when sm.prepay_amount is not null then sm.prepay_amount when pp.prepay_amount is not null then pp.prepay_amount when gam.prepay_amount is not null then gam.prepay_amount when ad.prepay_amount is not null then ad.prepay_amount when ad_pw1.prepay_amount is not null then ad_pw1.prepay_amount when ad_pw2.prepay_amount is not null then ad_pw2.prepay_amount when fm.prepay_amount is not null then fm.prepay_amount else null end
    )  prepay_amount

 
    FROM
    (
        SELECT (sheet->>'f1')::int sheet_id,sheet->>'f2' sheet_type,
             (sheet->>'f3')::int company_id,(sheet->>'f4')::TIMESTAMP happen_time,(sheet->>'f5')::TIMESTAMP approve_time,
             (sheet->>'f6')::int supcust_id,(sheet->>'f7') sub_type,(sheet->>'f8') red_flag,(sheet->>'f9') sub_id,sheet->>'f10' flow_id,
             (qk->>'f1')::real qk_amount,(qk->>'f2')::real qk_balance,(qk->>'f4')::real qk_balance_happen_time,
             (ys->>'f1')::real ys_amount,(ys->>'f2')::real ys_balance,(ys->>'f3')::real ys_total_balance,(ys->>'f4')::real ys_balance_happen_time,(ys->>'f5')::real ys_total_balance_happen_time,
             (sk->>'f1')::real sk_amount,(sk->>'f2')::real sk_balance,(sk->>'f4')::real sk_balance_happen_time
        FROM crosstab
        ( 'select row_to_json(row(sheet_id,sheet_type,company_id,happen_time,approve_time,supcust_id,sub_type,red_flag,sub_id,flow_id)) json1,sub_type ,row_to_json(row(change_amount,now_balance,now_prepay_balance,now_balance_happen_time,now_prepay_balance_happen_time,sheet_type)) as json 
	           from client_account_history 
                where company_id =~COMPANY_ID and  sheet_id is not null 
               
                and ~VAR_timeFld >= ''~VAR_startDay'' AND ~VAR_timeFld <= ''~VAR_endDay''   ~VAR_red_flag    ~VAR_sub_id  and supcust_id in (SELECT COALESCE(acct_cust_id,supcust_id) supcust_id  FROM info_supcust where company_id = ~COMPANY_ID ~VAR_supcust_id)     
                ORDER BY SHEET_ID desc
           ',  $$VALUES ('SK'),('YS'),('QK')$$
        ) AS errr(sheet jsonb,SK jsonb,YS jsonb,QK jsonb)
    ) cah
	LEFT JOIN 
    (
        select ssm.sheet_id,ssm.sheet_no,ssm.make_brief, ssm.seller_id,ssm.sheet_type,ssm.total_amount*ssm.money_inout_flag as total_amount,ssm.now_disc_amount*ssm.money_inout_flag as now_disc_amount,ssm.now_pay_amount*ssm.money_inout_flag as now_pay_amount,ssm.prepay_amount*ssm.money_inout_flag as  prepay_amount ,  ssm.payway1_id,ssm.payway2_id,
        ip.item_name as item_name,sd.row_index as row_index,sd.real_price as real_price,(ssm.total_amount - ssm.paid_amount - ssm.disc_amount) * ssm.money_inout_flag as left_amount,concat((case when ssm.sheet_type in ('X', 'XD') then sd.quantity else -sd.quantity end),sd.unit_no) as quantity,sd.orig_price,
        sd.quantity * sd.inout_flag *(-1) as qty_no_unit,sd.unit_no
        from sheet_sale_main ssm
        left join sheet_sale_detail sd on sd.company_id =  ~COMPANY_ID and sd.sheet_id = ssm.sheet_id 
        left join info_item_prop ip on ip.company_id =  ~COMPANY_ID  and ip.item_id = sd.item_id 
        where ssm.company_id = ~COMPANY_ID and sd.company_id =~COMPANY_ID and ip.company_id =  ~COMPANY_ID  ~VAR_row_index_condi
    ) sm ON sm.sheet_id = cah.sheet_id and sm.sheet_type = cah.sheet_type
	LEFT JOIN 
    (
        select sheet_id,make_brief,sheet_no,getter_id as seller_id,sheet_type,total_amount,now_disc_amount,now_pay_amount ,0 prepay_amount
        from sheet_prepay 
        where company_id = ~COMPANY_ID 
    ) pp ON pp.sheet_id = cah.sheet_id and pp.sheet_type = cah.sheet_type
	LEFT JOIN 
    (
        select sheet_id,sheet_no,make_brief,getter_id as seller_id,sheet_type,sheet_amount,now_disc_amount,now_pay_amount ,0 prepay_amount,   payway1_id,payway2_id  
        from sheet_get_arrears_main 
        where company_id = ~COMPANY_ID 
    ) gam ON gam.sheet_id = cah.sheet_id and 'SK' = cah.sheet_type
    LEFT JOIN 
    (
        select sheet_id,sheet_no, make_brief,seller_id,sheet_type,total_amount, 0 prepay_amount
       from sheet_item_ordered_adjust_main 
     where company_id = ~COMPANY_ID 
    ) ad on ad.sheet_id=cah.sheet_id and ad.sheet_type = substr(cah.sheet_type,1,4) 
    LEFT JOIN 
    (
        select sheet_id,sheet_no,make_brief, seller_id,sheet_type,total_amount, payway1_amount as prepay_amount
       from sheet_item_ordered_adjust_main 
     where company_id = ~COMPANY_ID and coalesce(payway1_amount,0)<>0 
    ) ad_pw1 on ad_pw1.sheet_id=cah.sheet_id and ad_pw1.sheet_type = substr(cah.sheet_type,1,4) 
    LEFT JOIN 
    (
        select sheet_id,sheet_no,make_brief, seller_id,sheet_type,total_amount, payway2_amount as  prepay_amount
       from sheet_item_ordered_adjust_main 
     where company_id = ~COMPANY_ID and coalesce(payway2_amount,0)<>0 
    ) ad_pw2 on ad_pw2.sheet_id=cah.sheet_id and ad_pw2.sheet_type = substr(cah.sheet_type,1,4) 
    LEFT JOIN 
    (
        select sheet_id,sheet_no,make_brief,getter_id as seller_id,sheet_type,total_amount,now_disc_amount*money_inout_flag as now_disc_amount,now_pay_amount*money_inout_flag as now_pay_amount, 0 prepay_amount,            payway1_id,payway2_id  
        from sheet_fee_out_main 
        where company_id = ~COMPANY_ID 
    ) fm on fm.sheet_id=cah.sheet_id and fm.sheet_type = cah.sheet_type 


    WHERE cah.company_id=~COMPANY_ID  
) cah 
LEFT JOIN info_supcust sc ON sc.supcust_id = cah.supcust_id and sc.company_id = ~COMPANY_ID
left join cw_subject cw on cah.sub_id=cw.sub_id::text and cw.company_id = ~COMPANY_ID
LEFT JOIN (select oper_id,oper_name as seller_name from info_operator where company_id= ~COMPANY_ID) seller on cah.seller_id = seller.oper_id
where cah.company_id=~COMPANY_ID and supcust_flag in ('C','CS')  and sheet_no is not null 

",

//单据类型移除，因为有同为客户和供应商的问题 AND SHEET_TYPE IN (''X'',''T'',''TD'',''XD'',''YS'',''DH'',''SK'',''DHTZ'',''DHTZ_zr'',''DHTZ_zc'',''ZC'')


                            QueryFromSQL2=@"
FROM
(
    SELECT  null flow_id,sheet_id,sheet_no,make_brief,sheet_type,sheet_type as sheet_type_query,sm.company_id,happen_time,sm.supcust_id, seller_name,sm.seller_id,  sup_name,null sub_type,null sub_name,null sub_id,null::float4 qk_amount,null::float4 qk_balance,null::float4 qk_balance_happen_time, null::float4 ys_amount,null::float4 ys_balance,null::float4 ys_balance_happen_time,null::float4 ys_total_balance,null::float4 ys_total_balance_happen_time,null::float4 sk_amount,null::float4 sk_balance,null::float4 sk_balance_happen_time,  money_inout_flag*total_amount total_amount,money_inout_flag*now_disc_amount now_disc_amount,money_inout_flag*now_pay_amount now_pay_amount , prepay_amount*money_inout_flag as prepay_amount,         red_flag ,approve_time,null::real left_amount
            ,null::integer row_index,null item_name,null::real real_price,null::real orig_price,null quantity,null::real qty_no_unit,null unit_no
    FROM sheet_sale_main sm 
    LEFT JOIN info_supcust sc ON sm.supcust_id = sc.supcust_id and sc.company_id=~COMPANY_ID 
    LEFT JOIN (select oper_id,oper_name as seller_name from info_operator where company_id= ~COMPANY_ID) seller on sm.seller_id = seller.oper_id
    where sm.company_id=~COMPANY_ID and coalesce(sm.prepay_amount,0) =0 and ABS(total_amount-now_disc_amount-now_pay_amount)<0.01 
        and  approve_time is not null and ~VAR_timeFld >= '~VAR_startDay' AND ~VAR_timeFld <= '~VAR_endDay'   ~VAR_red_flag
    UNION

    SELECT  null flow_id,sheet_id,make_brief,sheet_no,sheet_type,sheet_type as sheet_type_query,sm.company_id,happen_time,sm.supcust_id,  seller_name,sm.getter_id as seller_id, sup_name,null sub_type,null sub_name,null sub_id,null::float4 qk_amount,null::float4 qk_balance,null::float4 qk_balance_happen_time, null::float4 ys_amount,null::float4 ys_balance,null::float4 ys_balance_happen_time,null::float4 ys_total_balance,null::float4 ys_total_balance_happen_time,null::float4 sk_amount,null::float4 sk_balance,null::float4 sk_balance_happen_time,  money_inout_flag*total_amount total_amount,money_inout_flag*now_disc_amount now_disc_amount,money_inout_flag*now_pay_amount now_pay_amount , 0 prepay_amount,         red_flag ,approve_time,null::real left_amount
         ,null::integer row_index,null item_name,null::real real_price,null::real orig_price,null quantity,null::real qty_no_unit,null unit_no
    FROM sheet_fee_out_main sm 
    LEFT JOIN info_supcust sc ON sm.supcust_id = sc.supcust_id 
    LEFT JOIN (select oper_id,oper_name as seller_name from info_operator where company_id= ~COMPANY_ID) seller on sm.getter_id = seller.oper_id
        where sm.company_id=~COMPANY_ID and  ABS(total_amount-now_disc_amount-now_pay_amount)<0.01 
    and  approve_time is not null and ~VAR_timeFld >= '~VAR_startDay' AND ~VAR_timeFld <= '~VAR_endDay'   ~VAR_red_flag
    ) cah where cah.company_id=~COMPANY_ID  ~VAR_condi2_supcust_id 

",
                     //QueryOrderSQL=" order by ~VAR_forOrdeTimeFld ,sup_name"


             
                     QueryOrderSQL=" order by approve_time desc,sup_name,row_index"
                  }
                }
            };
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            //string now = CPubVars.GetDateText(DateTime.Now.Date) + " 00:00";
            //var supcust_id = CPubVars.RequestV(Request, "supcust_id");
            //var sub_type = CPubVars.RequestV(Request, "sub_type");
            //var sub_type_name = CPubVars.RequestV(Request, "sub_type_name");
            //if (supcust_id.IsValid() && sub_type.IsValid())
            //{
            //    string sql = $"select min(happen_time) v from client_account_history where company_id = {company_id} and supcust_id = {supcust_id} and sub_type = '{sub_type}'";
            //    dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            //    if (record != null) DataItems["startDay"].Value = record.v;
            //}
            //var q2q = DataItems["sub_id"].Value;

            if (DataItems["queryTimeAccord"].Value== "byHappenTime")
            {
                Grids["gridItems"].SortColumn = "happen_time";
				Grids["gridItems"].SortDirection = "desc";
			}
		}
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;
            string queryTimeAccord = DataItems["queryTimeAccord"].Value;
            SQLVariables["timeFld"] = "approve_time";
            SQLVariables["forOrdeTimeFld"] = "approve_time desc,flow_id desc";
            if (queryTimeAccord == "byHappenTime") {
                SQLVariables["timeFld"] = "happen_time";
                SQLVariables["forOrdeTimeFld"] = "happen_time desc,approve_time desc,flow_id desc";
                Grids["gridItems"].Columns["arrears_balance"].SqlFld = "(CASE when qk_balance_happen_time is not null then qk_balance_happen_time when sk_balance_happen_time is not null THEN sk_balance_happen_time END)";
                Grids["gridItems"].Columns["prepay_balance"].SqlFld = "ys_balance_happen_time::text";
                Grids["gridItems"].Columns["prepay_total_balance"].SqlFld = "ys_total_balance_happen_time::text";
            }
            
        
            if (DataItems["sub_id"].Value != "") 
            {
                var sub_id = DataItems["sub_id"].Value;
                SQLVariables["sub_id"] = @$"and sub_id={sub_id}";

            }
            else
            {
                SQLVariables["sub_id"] = "";
            }
 

            if (DataItems["showRedSheet"].Value.ToLower() == "true")
            {

                SQLVariables["red_flag"] = "  ";
            }
            else
            {
                SQLVariables["red_flag"] = "and red_flag is  null";
            }

            if (DataItems["supcust_id"].Value != "")
            {
                var supcustID = DataItems["supcust_id"].Value;
                SQLVariables["supcust_id"] = $" and supcust_id={supcustID} ";
                SQLVariables["condi2_supcust_id"] = $" and cah.supcust_id={supcustID}";

            }
            else
            {
                SQLVariables["supcust_id"] = "";
                SQLVariables["condi2_supcust_id"] = $"";
            }

            if (DataItems["showSaleSheetDetail"].Value.ToLower() == "true")
            {
                SQLVariables["row_index_condi"] = "";    //显示所有
                Grids["gridItems"].Columns["item_name"].Hidden = Grids["gridItems"].Columns["item_name"].HideOnLoad = false;
				Grids["gridItems"].Columns["real_price"].Hidden = Grids["gridItems"].Columns["real_price"].HideOnLoad = false;
			//	Grids["gridItems"].Columns["quantity"].Hidden = Grids["gridItems"].Columns["quantity"].HideOnLoad = false;
				Grids["gridItems"].Columns["qty_no_unit"].Hidden = Grids["gridItems"].Columns["qty_no_unit"].HideOnLoad = false;
				Grids["gridItems"].Columns["unit_no"].Hidden = Grids["gridItems"].Columns["unit_no"].HideOnLoad = false;

				Grids["gridItems"].Columns["left_amount"].Hidden = Grids["gridItems"].Columns["left_amount"].HideOnLoad = false;



			}
			else
            {
                SQLVariables["row_index_condi"] = " and coalesce(row_index,1)=1";     //只显示一行
            }
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class AccountHistoryController : QueryController
    {
        public AccountHistoryController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            AccountHistoryModel model = new AccountHistoryModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            AccountHistoryModel model = new AccountHistoryModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            string sParams = Request.Form["params"];
            sParams = System.Web.HttpUtility.UrlDecode(sParams);
            dynamic queryParams = JsonConvert.DeserializeObject(sParams);
          
            AccountHistoryModel model = new AccountHistoryModel(cmd);
            
            return await model.ExportExcel(Request, cmd,queryParams);
        }

    }
}
