﻿
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Jint.Native;
using Jint.Parser;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using NPOI.SS.Formula.Functions;
using static Antlr4.Runtime.Atn.SemanticContext;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class StoreStockReportModel : PageQueryModel
    {
        public StoreStockReportModel(CMySbCommand cmd, bool useMainDb = false) : base(Services.MenuId.storeStockReport)
        {
            if (useMainDb) this.Database = "";
            this.cmd = cmd;
            this.PageTitle = "门店库存上报表";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
            {"sup_name",new DataItem(){Title="客户检索",FldArea="divHead",SqlFld="client_name,py_str,mobile,supcust_no,sup_addr,boss_name",PlaceHolder="客户名/简拼/手机/编号/地址/老板姓名", QueryOnChange=true,CompareOperator="ilike",Width="250"}},
            {"item_id",new DataItem(){Title="商品名称",FldArea="divHead", LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="item_id",DropDownWidth="300",
              SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
            {"item_brand", new DataItem(){Title = "商品品牌",FldArea="divHead", LabelFld = "brand_name", ButtonUsage = "list", CompareOperator="=",SqlFld="item_brand",
                SqlForOptions = CommonTool.selectBrands}},
            {"item_class",new DataItem(){Title="商品类别",FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",MumSelectable=true,CompareOperator="like",SqlFld="other_class",
                SqlForOptions=CommonTool.selectClasses}},

             {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",Hidden = true,ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'2',l:'加权平均价'},{v:'3',l:'预设进价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价',c:'0'}]", CompareOperator="=" }},
            
            //{"produce_date",new DataItem(){Title="生产日期",FldArea="divHead",ForQuery=false,CtrlType="jqxDateTimeInput",ShowTime=false}},
            //{"batch_no",new DataItem(){Title="批次",FldArea="divHead",ForQuery=false}},
            {"status",new DataItem(){FldArea="divHead",Title = "状态",LabelFld = "cls_status_name", LabelInDB = false, Value = "all", Label = "所有",ButtonUsage = "list", QueryOnChange = false, Hidden=true, CompareOperator = "=", NullEqualValue = "all",
     Source = @"[{v:'normal',l:'正常',condition:"" COALESCE(ip.status,'1') ='1' ""},
               {v:'stop',l:'停用',condition:""ip.status = '0' ""},
               {v:'all',l:'所有',condition:""true""}]"
 }},
            {"bySmallUnit",new DataItem(){FldArea="divHead",Title="按小单位",CtrlType="jqxCheckBox",ForQuery=false,Value="false",AutoRemember=true}},
            {"byUnit",new DataItem(){Title="展示单位",FldArea="divHead", ButtonUsage="list",Hidden = true, ForQuery=false, Source = "[{v:'b',l:'大单位'},{v:'s',l:'小单位'},{v:'default',l:'混合单位'}]"}},
            {"byApproveTime",new DataItem(){FldArea="divHead",Title="按审核时间查",CtrlType="jqxCheckBox",ForQuery=false,Value="false",AutoRemember=true}},

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, 
                     Sortable=true,
                     //ColumnsHeight=15,
                      IdColumn="sheet_id",
                     //PageByOverAgg=false,
                     AllowAddRow=true,

                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"client_id",new DataItem(){Title="编号", Width="80",Hidden=true,HideOnLoad=true,SqlFld="clent_id"}},
                       //{"client_no",new DataItem(){Title="客户编号", Width="100",Hidden=true,Sortable=true,}},
                       {"client_name",new DataItem(){Title="客户名称", Width="200",Sortable=true}},
                       {"seller_name", new DataItem(){Title="业务员",  Width="80"}},
                       {"item_id",    new DataItem(){Title="商品编号",  Width="250",SqlFld="item_id",Hidden=true,HideOnLoad=true}},
                       {"item_name",    new DataItem(){Title="商品名称", Sortable=true,  Width="200",SqlFld="item_name"}},
                       //{"item_no",    new DataItem(){Title="商品编号", Width="100",SqlFld="ip.item_no",Hidden=true}},
                         {"s_barcode",    new DataItem(){Title="条码(小)", Sortable=true,  Width="100"}},
                       {"b_barcode",    new DataItem(){Title="条码(大)", Sortable=true,  Width="100",Hidden=true}},
                       {"unit_conv",new DataItem(){Title="单位换算",ButtonUsage="list", CellsAlign="center",Width="200",
                           SqlFld = @"yj_get_unit_relation(b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},
                       {"start_date",new DataItem(){Title="期初时间", Width="180",Sortable=true}},      
                       //{"item_spec",    new DataItem(){Title="规格", Width="50",Hidden=true}},
                       //{"unit_no",new DataItem(){Title="单位", Width="50", FuncGetSubColumns = async (col) =>
                       //     new ColumnsResult{
                       //         Columns=new Dictionary<string,DataItem>()
                       //         {
                       //           {"b_unit_no",   new DataItem(){Title="大",Width="50",Linkable=true,Hidden=true,HideOnLoad=true,
                       //           SqlFld="b_unit_no"}},
                       //           {"m_unit_no",   new DataItem(){Title="中",Width="50",Linkable=true,Hidden=true,HideOnLoad=true,
                       //           SqlFld="m_unit_no"}},
                       //           {"s_unit_no",   new DataItem(){Title="小",Width="50",Linkable=true,Hidden=true,HideOnLoad=true,
                       //           SqlFld="s_unit_no"}},

                       //         }
                       //     }
                       //} },
                       {"start_b_qty",   new DataItem(){Title="大",Width="10%",Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=" yj_getunitqty('b',start_stock,b_unit_factor,m_unit_factor) "
                       }},
                       {"start_m_qty",   new DataItem(){Title="中",Width="10%",Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=" yj_getunitqty('m',start_stock,b_unit_factor,m_unit_factor) "
                       }},
                       {"start_s_qty",   new DataItem(){Title="小",Width="50",Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=" yj_getunitqty('s',start_stock,b_unit_factor,m_unit_factor) "
                       }},
                       {"start_qty_unit",   new DataItem(){Title="期初库存",Width="100",
                           /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower() == "true"?"round(COALESCE (stock_Qty - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - COALESCE (qc_add_qty_period , 0 ) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 ) - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC"
                                                                        : "yj_get_bms_qty (round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - COALESCE (qc_add_qty_period , 0 ) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 )- COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC,b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )",
                           */
                           SqlFld = this.DataItems["byUnit"].Value.ToLower() == "b" ? "yj_get_bms_qty(round(start_stock)/ CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END)"
                                    : this.DataItems["byUnit"].Value.ToLower() == "s" ? "round(start_stock)"
                                    : "yj_get_bms_qty (round(start_stock),b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )",
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string qty = "",q="";
                               /*if (this.DataItems["bySmallUnit"].Value.ToLower() == "true")
                               {
                                    qty=sumColumnValues["start_stock"];
                                    return qty;
                               }
							   else
							   {
                                    q=sumColumnValues["start_b_qty"]; if(q!="") qty+=q+"大";
                                    q=sumColumnValues["start_m_qty"]; if(q!="") qty+=q+"中";
                                    q=sumColumnValues["start_s_qty"]; if(q!="") qty+=q+"小";
                                    return qty;
                               }*/
                                if (this.DataItems["byUnit"].Value.ToLower() == "b")
                                {
                                    qty=sumColumnValues["start_qty_b"];
                                    return qty;
                                }
                                else if (this.DataItems["byUnit"].Value.ToLower() == "s")
                                {
                                   qty=sumColumnValues["start_qty"];
                                    return qty;
                                }
                                else
                                {
                                    q=sumColumnValues["start_b_qty"]; if(q!="") qty+=q+"大";
                                    q=sumColumnValues["start_m_qty"]; if(q!="") qty+=q+"中";
                                    q=sumColumnValues["start_s_qty"]; if(q!="") qty+=q+"小";
                                    return qty;
                                }
                           }
                       }},
                       {"start_qty",   new DataItem(){Title="期初(小)",Width="10%",Hidden=true, HideOnLoad=true,ShowSum=true,
                           SqlFld="round(start_stock)"}},
                       {"start_qty_b",   new DataItem(){Title="期初(大)",Width="10%",Hidden=true, HideOnLoad=true,ShowSum=true,
                           SqlFld="round(start_stock) / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"}},

                       {"buy_qty_sum_show",new DataItem(){Title="期间补货量", Width="100",
                       SqlFld = this.DataItems["byUnit"].Value.ToLower() == "b" ? "yj_get_bms_qty(round(buy_qty_sum)/ CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END)"
                                    : this.DataItems["byUnit"].Value.ToLower() == "s" ? "round(buy_qty_sum)"
                                    : "yj_get_bms_qty (round(buy_qty_sum),b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )"} },
                       {"buy_qty_show",new DataItem(){Title="期末补货", Width="100",
                       SqlFld = this.DataItems["byUnit"].Value.ToLower() == "b" ? "yj_get_bms_qty(round(buy_qty)/ CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END)"
                                    : this.DataItems["byUnit"].Value.ToLower() == "s" ? "round(buy_qty)"
                                    : "yj_get_bms_qty (round(buy_qty),b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )"} },


                      {"end_qty_unit", new DataItem(){Title="期末库存",Width="80",
                           /*SqlFld=this.DataItems["bySmallUnit"].Value.ToLower()=="true" ? "COALESCE (stock_Qty  - coalesce(qc_add_qty,0)  + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC"
                                                                       : " yj_get_bms_qty (COALESCE (stock_Qty  - coalesce(qc_add_qty,0)  + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC, b_unit_no, b_unit_factor,m_unit_no,m_unit_factor,s_unit_no ) ",
                           */
                          SqlFld=this.DataItems["byUnit"].Value.ToLower()=="b" ? "yj_get_bms_qty(round(end_stock)) / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                          : this.DataItems["byUnit"].Value.ToLower()=="s" ? "round(end_stock)"
                          : " yj_get_bms_qty (round(end_stock), b_unit_no, b_unit_factor,m_unit_no,m_unit_factor,s_unit_no ) ",

                          FuncGetSumValue = (sumColumnValues) =>
                           {
                               string qty = "",q="";
							   /*if (this.DataItems["bySmallUnit"].Value.ToLower() == "true")
							   {
                                    qty=sumColumnValues["end_stock"];
                                    return qty;
                               }
							   else
							   {
                                   q=sumColumnValues["end_b_qty"]; if(q!="") qty+=q+"大";
                                   q=sumColumnValues["end_m_qty"]; if(q!="") qty+=q+"中";
                                   q=sumColumnValues["end_s_qty"]; if(q!="") qty+=q+"小";
                                   return qty;
                               }*/
                               if (this.DataItems["byUnit"].Value.ToLower() == "b")
                               {
                                    qty=sumColumnValues["end_qty_b"];
                                    return qty;
                               }
                               else if (this.DataItems["byUnit"].Value.ToLower() == "s")
                               {
                                    qty=sumColumnValues["end_qty"];
                                    return qty;
                               }
                               else
                               {
                                   q=sumColumnValues["end_b_qty"]; if(q!="") qty+=q+"大";
                                   q=sumColumnValues["end_m_qty"]; if(q!="") qty+=q+"中";
                                   q=sumColumnValues["end_s_qty"]; if(q!="") qty+=q+"小";
                                   return qty;
                               }
                           }
                       }},
                       {"end_b_qty",   new DataItem(){Title="大",Width="80",Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=" yj_getunitqty('b',end_stock,b_unit_factor,m_unit_factor) "
                       }},
                       {"end_m_qty",   new DataItem(){Title="中",Width="80",Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=" yj_getunitqty('m',end_stock,b_unit_factor,m_unit_factor) "
                       }},
                       {"end_s_qty",   new DataItem(){Title="小",Width="80",Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=" yj_getunitqty('s',end_stock,b_unit_factor,m_unit_factor) "
                       }},


                       {"end_qty",   new DataItem(){Title="期末(小)",Width="80",Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=" COALESCE (end_stock,0 ) :: NUMERIC "
                       }},
                       {"end_qty_b",   new DataItem(){Title="期末(大)",Width="80" ,Hidden=true,HideOnLoad=true,ShowSum=true,
                           SqlFld=" COALESCE (end_stock,0 ) :: NUMERIC  / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                       }},
                       //{"end_amount",   new DataItem(){Title="期末金额",Hidden=true, Width="10",ShowSum=true}},
                       {"end_date",new DataItem(){Title="期末时间", Width="180",Sortable=true}},
                       {"sale_qty_show",new DataItem(){Title="门店动销量", Width="100",
                       SqlFld = this.DataItems["byUnit"].Value.ToLower() == "b" ? "yj_get_bms_qty(round(sale_qty)/ CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END)"
                                    : this.DataItems["byUnit"].Value.ToLower() == "s" ? "round(sale_qty)"
                                    : "yj_get_bms_qty (round(sale_qty),b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )"} },
                       {"sale_qty_avg_show",new DataItem(){Title="日均动销量", Width="100",
                       SqlFld = this.DataItems["byUnit"].Value.ToLower() == "b" ? "yj_get_bms_qty(round(sale_qty_avg)/ CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END)"
                                    : this.DataItems["byUnit"].Value.ToLower() == "s" ? "round(sale_qty_avg)"
                                    : "yj_get_bms_qty (round(sale_qty_avg),b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )"} },
                     },

                     QueryFromSQL=@"
FROM
(
        with records as
               (select sj.company_id,
                       sj.client_id,
                       sj.sheet_no,
                       sj.item_id,
                       sj.sheet_item_name,
                       sj.client_stock_quantity,
                       sj.buy_quantity,
                       sj.happen_time,
                       sj.approve_time,
                       sj.seller_id,
                       qty.start_date,
                       qty.end_date
                from (select a.company_id,
                             a.client_id,
                             a.sheet_id,
                             a.sheet_no,
                             a.seller_id,
                             b.flow_id,
                             b.row_index,
                             b.item_id,
                             b.sheet_item_name,
                             b.client_stock_quantity,
                             b.buy_quantity,
                             b.happen_time,
                             a.approve_time
                      from sheet_client_stock_main a
                               left join sheet_client_stock_detail b on a.sheet_id = b.sheet_id
                      where a.~VAR_time >= '~VAR_startDay' and a.~VAR_time <= '~VAR_endDay') sj --sj是辅助作用，只是把必要的信息接到期初期末在的行
                         left join (select a.company_id,
                                           a.client_id,
                                           b.item_id,
                                           b.sheet_item_name,
                                           min(b.happen_time) start_date,
                                           max(b.happen_time) end_date
                                    from sheet_client_stock_main a
                                             left join sheet_client_stock_detail b on a.sheet_id = b.sheet_id
                      where a.~VAR_time >= '~VAR_startDay' and a.~VAR_time <= '~VAR_endDay'
                                    group by b.item_id, a.company_id, a.client_id, b.sheet_item_name) qty--qty是找出期初和期末的时间（这里还没有做筛选日期的限制）
                                   ON sj.company_id = qty.company_id
                                       AND sj.client_id = qty.client_id
                                       AND sj.item_id = qty.item_id
                                       AND sj.sheet_item_name = qty.sheet_item_name
                                       and (sj.happen_time = qty.end_date or sj.happen_time = qty.start_date)),

           start_records as (select records.company_id
                                  , records.client_id
                                  , records.seller_id
                                  , records.item_id
                                  , records.sheet_item_name
                                  , records.client_stock_quantity start_stock
                                  , records.start_date
                             from records
                             where happen_time = start_date),
           end_records as (select records.company_id
                                , records.client_id
                                , records.item_id
                                , records.sheet_item_name
                                , records.client_stock_quantity+records.buy_quantity end_stock
                                , records.buy_quantity          buy_qty
                                , records.end_date
                           from records
                           where happen_time = end_date),
           sum_buy_records as (select a.company_id,
                                      a.client_id,
                                      b.item_id,
                                      sum(b.buy_quantity) buy_qty_sum_all
                               from sheet_client_stock_main a
                                        left join sheet_client_stock_detail b on a.sheet_id = b.sheet_id
                               group by b.item_id, a.company_id, a.client_id)
      select start_records.company_id,
             start_records.client_id,
             client_records.sup_name                   client_name,
             supcust_id                                clent_id,
             py_str,
             mobile,
             supcust_no,
             sup_addr,
             boss_name,
             start_records.seller_id,
             oper_name                                 seller_name,
             start_records.item_id                     item_id,
             start_records.sheet_item_name             item_name,
             item_brand_records.item_brand item_brand,
             item_brand_records.other_class other_class,
             unit_conv,
             start_records.start_date,
             start_records.start_stock,
             (buy_qty_sum_all - buy_qty)               buy_qty_sum,
             buy_qty,
             end_stock,
             end_date,
             (start_stock + buy_qty_sum_all - buy_qty) sale_qty,
             CASE
                 WHEN end_date = start_date
                     THEN NULL
                 ELSE
                     CASE
                         WHEN DATE_PART('day', end_date - start_date) = 0 THEN (start_stock + buy_qty_sum_all - end_stock)
                         ELSE (start_stock + buy_qty_sum_all - end_stock) / DATE_PART('day', end_date - start_date)
                         END
                 END AS                                sale_qty_avg,
             s_unit_no,
             s_unit_factor,
             s_wholesale_price,
             s_retail_price,
             s_buy_price,
             s_barcode,
             m_unit_no,
             m_unit_factor,
             m_wholesale_price,
             m_retail_price,
             m_buy_price,
             m_barcode,
             b_unit_no,
             b_unit_factor,
             b_wholesale_price,
             b_retail_price,
             b_buy_price,
             b_barcode,
             unit_conv,
             stock_Qty
      from start_records
               left join end_records on
          start_records.company_id = end_records.company_id and
          start_records.client_id = end_records.client_id and
          start_records.item_id = end_records.item_id
               left join sum_buy_records on
          sum_buy_records.company_id = end_records.company_id and
          sum_buy_records.client_id = end_records.client_id and
          sum_buy_records.item_id = end_records.item_id
               left join (select    company_id, 
                                    supcust_id, 
                                    sup_name,
                                    py_str,
                                    mobile,
                                    supcust_no,
                                    sup_addr,
                                    boss_name
                          from info_supcust
                          where supcust_flag like '%C%') client_records on
          client_records.company_id = end_records.company_id and
          client_records.supcust_id = end_records.client_id
               LEFT JOIN

           (select item_id,
                   s_unit_no,
                   s_unit_factor,
                   s_wholesale_price,
                   s_retail_price,
                   s_buy_price,
                   s_barcode,
                   m_unit_no,
                   m_unit_factor,
                   m_wholesale_price,
                   m_retail_price,
                   m_buy_price,
                   m_barcode,
                   b_unit_no,
                   b_unit_factor,
                   b_wholesale_price,
                   b_retail_price,
                   b_buy_price,
                   b_barcode,
                   (
                       case
                           when b_unit_factor is not null and m_unit_factor is null
                               then concat(s_unit_factor, b_unit_no, '=', b_unit_factor, s_unit_no)
                           when b_unit_factor is not null and m_unit_factor is not null then concat(s_unit_factor,
                                                                                                    b_unit_no, '=',
                                                                                                    case
                                                                                                        when b_unit_factor::numeric % m_unit_factor::numeric = 0
                                                                                                            then round(b_unit_factor::numeric / m_unit_factor::numeric, 0)::text
                                                                                                        else round(b_unit_factor::numeric / m_unit_factor::numeric, 2)::text end,
                                                                                                    m_unit_no, '=',
                                                                                                    b_unit_factor,
                                                                                                    s_unit_no)
                           when b_unit_factor is null then concat(s_unit_factor, s_unit_no) end
                       ) as unit_conv
            from (select item_id,
                         (s ->> 'f1')::real as s_unit_factor,
                         s ->> 'f2'         as s_unit_no,
                         s ->> 'f3'         as s_wholesale_price,
                         s ->> 'f4'         as s_retail_price,
                         s ->> 'f5'            s_barcode,
                         s ->> 'f6'         as s_buy_price,
                         (m ->> 'f1')::real as m_unit_factor,
                         m ->> 'f2'         as m_unit_no,
                         m ->> 'f3'         as m_wholesale_price,
                         m ->> 'f4'         as m_retail_price,
                         m ->> 'f5'            m_barcode,
                         m ->> 'f6'         as m_buy_price,
                         (b ->> 'f1')::real as b_unit_factor,
                         b ->> 'f2'         as b_unit_no,
                         b ->> 'f3'         as b_wholesale_price,
                         b ->> 'f4'         as b_retail_price,
                         b ->> 'f5'            b_barcode,
                         b ->> 'f6'         as b_buy_price
                  from crosstab(
                               'select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,retail_price,barcode,buy_price)) as json from info_item_multi_unit where company_id = ~COMPANY_ID ORDER BY item_id',
                               $$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb, m jsonb, b jsonb)) t) item_info_records
           on item_info_records.item_id = end_records.item_id
               left join (select oper_id, company_id, oper_name
                          from g_operator) seller_name_records
                         on seller_name_records.company_id = end_records.company_id and
                            seller_name_records.oper_id = seller_id
               left join (SELECT item_id, company_id, sum(stock_qty) as stock_Qty
                          from stock m
                                   left join (select batch_id, produce_date, batch_no
                                              from info_item_batch
                                              where company_id = ~COMPANY_ID) itb on itb.batch_id = m.batch_id
                                   LEFT JOIN (select branch_position_name, branch_position branch_position_f
                                              from info_branch_position
                                              where company_id = ~COMPANY_ID) ibp on ibp.branch_position_f = m.branch_position
                          GROUP BY item_id, company_id) stock_qty_records
                         on stock_qty_records.item_id = start_records.item_id and
                            stock_qty_records.company_id = start_records.company_id
                         left join (select item_id, item_brand,other_class
                                from info_item_prop) item_brand_records
                               on item_brand_records.item_id = start_records.item_id) final_results
 where final_results.company_id=~COMPANY_ID
",

                     QueryGroupBySQL = "",
                     QueryOrderSQL=""
                  }
                }
            };
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {

            var costPrice = "mu.buy_price";//当前进价
            var cost_price_type = DataItems["cost_price_type"].Value;
            var recentPriceTime = "0";
            if (cost_price_type == "4")
            {
                string sql = $@"SELECT replace((setting->'recentPriceTime')::text,'""','') recentPriceTime FROM company_setting where company_id = {company_id}";

                dynamic re = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (re != null) recentPriceTime = re.recentpricetime;
            }
            switch (cost_price_type)
            {
                case "4"://最近平均进价
                    if (recentPriceTime != "0") costPrice = $"(ip.cost_price_recent->'avg{recentPriceTime}')::numeric";
                    break;
                case "3"://预设进价
                    costPrice = "mu.buy_price";
                    break;
                case "2"://加权价
                    costPrice = "ip.cost_price_avg";
                    break;
                case "1"://预设成本
                    costPrice = "mu.cost_price_spec";
                    break;
            }

            var columns = Grids.GetValueOrDefault("gridItems").Columns;

            //columns["end_amount"].SqlFld = $@" round((COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 ) - COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 ) - COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 )- COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 )*{costPrice})::numeric,2) ";


            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;

            SQLVariables["min_happen_time"] = DataItems["startDay"].Value;
            if (DataItems["byApproveTime"].Value.ToLower() == "true")
            {
                SQLVariables["time"] = "approve_time";
                string startDay = DataItems["startDay"].Value;
                SQLVariables["min_happen_time"] = CPubVars.GetDateText(Convert.ToDateTime(startDay).AddMonths(-3));
            }
            else
            {
                SQLVariables["time"] = "happen_time";

            }


            /*if (DataItems["bySmallUnit"].Value.ToLower() == "true")
            {
                this.Grids["gridItems"].Columns["start_stock_unit"].SqlFld = this.DataItems["bySmallUnit"].Value.ToLower() == "true" ? "round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - coalesce(qc_add_qty_period,0) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 )- COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 ) - COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC"
                                                 : "yj_get_bms_qty (round(COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE (xs_add_qty, 0 ) - COALESCE (xs_reduce_qty , 0 ) - COALESCE (cg_add_qty , 0 ) + COALESCE (cg_reduce_qty , 0 )- COALESCE (cr_add_qty , 0 ) + COALESCE (cr_reduce_qty , 0 )- COALESCE (zc_add_qty , 0 ) + COALESCE (zc_reduce_qty , 0 ) - COALESCE (yk_add_qty , 0 ) + COALESCE (   yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE (db_reduce_qty , 0 ) - coalesce(qc_add_qty_period,0) + COALESCE (xs_add_qty_period , 0 ) - COALESCE (xs_reduce_qty_period , 0 ) - COALESCE (cg_add_qty_period , 0 ) + COALESCE ( cg_reduce_qty_period , 0 ) - COALESCE (cr_add_qty_period , 0 ) + COALESCE ( cr_reduce_qty_period , 0 )- COALESCE (zc_add_qty_period , 0 ) + COALESCE ( zc_reduce_qty_period , 0 ) - COALESCE ( yk_add_qty_period , 0 ) + COALESCE ( yk_reduce_qty_period , 0 ) - COALESCE (db_add_qty_period , 0 ) + COALESCE (db_reduce_qty_period , 0 ),0 )::numeric, 3 ):: NUMERIC,b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )";
                
                this.Grids["gridItems"].Columns["end_stock_unit"].SqlFld = this.DataItems["bySmallUnit"].Value.ToLower() == "true" ? "COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 )- COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC"
                                                 : " yj_get_bms_qty (COALESCE (stock_Qty  - coalesce(qc_add_qty,0) + COALESCE ( xs_add_qty, 0 ) - COALESCE ( xs_reduce_qty , 0 ) - COALESCE ( cg_add_qty , 0 ) + COALESCE ( cg_reduce_qty , 0 ) - COALESCE ( cr_add_qty , 0 ) + COALESCE ( cr_reduce_qty , 0 )- COALESCE ( zc_add_qty , 0 ) + COALESCE ( zc_reduce_qty , 0 ) - COALESCE ( yk_add_qty , 0 ) + COALESCE ( yk_reduce_qty , 0 ) - COALESCE ( db_add_qty , 0 ) + COALESCE ( db_reduce_qty , 0 ),0 ) :: NUMERIC, b_unit_no, b_unit_factor,m_unit_no,m_unit_factor,s_unit_no ) ";
                 
            }*/
            if (DataItems["byUnit"].Value == "")
            {
                if (DataItems["bySmallUnit"].Value.ToLower() == "true")
                {
                    DataItems["byUnit"].Value = "s";
                    DataItems["byUnit"].Label = "小单位";
                }
                else
                {
                    DataItems["byUnit"].Value = "default";
                    DataItems["byUnit"].Label = "混合单位";
                }
            }
            this.Grids["gridItems"].Columns["start_qty_unit"].SqlFld = this.DataItems["byUnit"].Value.ToLower() == "b" ? "yj_get_bms_qty(round(start_stock))/ CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                : this.DataItems["byUnit"].Value.ToLower() == "s" ? "round(start_stock)"
                : "yj_get_bms_qty (round(start_stock),b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no )";

            this.Grids["gridItems"].Columns["end_qty_unit"].SqlFld = this.DataItems["byUnit"].Value.ToLower() == "b" ? "yj_get_bms_qty(round(end_stock))  / CASE WHEN b_unit_no IS NOT NULL AND b_unit_factor IS NOT NULL THEN b_unit_factor WHEN m_unit_no IS NOT NULL AND m_unit_factor IS NOT NULL THEN m_unit_factor ELSE 1 END"
                : this.DataItems["byUnit"].Value.ToLower() == "s" ? "round(end_stock)"
                : " yj_get_bms_qty (round(end_stock), b_unit_no, b_unit_factor,m_unit_no,m_unit_factor,s_unit_no ) ";


            string produceDateSql = "";
            string qcSql = "";
            string xsSql = "";
            string cgSql = "";
            string ykSql = "";
            string crSql = "";
            string zcSql = "";
            string drSql = "";
            string dcSql = "";

            SQLVariables["produceDateSql"] = produceDateSql;
            SQLVariables["qcSql"] = qcSql;
            SQLVariables["xsSql"] = xsSql;
            SQLVariables["cgSql"] = cgSql;
            SQLVariables["ykSql"] = ykSql;
            SQLVariables["crSql"] = crSql;
            SQLVariables["zcSql"] = zcSql;
            SQLVariables["drSql"] = drSql;
            SQLVariables["dcSql"] = dcSql;

        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            var costPriceType = "3";
            var costPriceTypeName = "预设进价";
            if (JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            var columns = Grids["gridItems"].Columns;
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                //columns["end_amount"].HideOnLoad = columns["end_amount"].Hidden = true;
                columns["buy_amount"].HideOnLoad = columns["buy_amount"].Hidden = true;
            }

            if (costPriceType == "1") costPriceTypeName = "预设成本";
            else if (costPriceType == "2") costPriceTypeName = "加权平均成本";
            else if (costPriceType == "4") costPriceTypeName = "最近平均进价";
            DataItems["cost_price_type"].Value = costPriceType;
            DataItems["cost_price_type"].Label = costPriceTypeName;
        }


    }



    [Route("api/[controller]/[action]")]
    public class StoreStockReportController : QueryController
    {
        public StoreStockReportController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            StoreStockReportModel model = new StoreStockReportModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            StoreStockReportModel model = new StoreStockReportModel(cmd);

            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            StoreStockReportModel model = new StoreStockReportModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
