﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using Newtonsoft.Json;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Org.BouncyCastle.Utilities.Collections;
using NPOI.OpenXmlFormats.Shared;

namespace ArtisanManage.CwPages.Report
{
    public class CashInOutModel : PageQueryModel
    {
        public CashInOutModel() : base(Services.MenuId.cashInOut)
        {

        }
        public void OnGet(string operKey)
        {
            OperKey = operKey;
        }
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class CashInOutController : QueryController
    { 
        public CashInOutController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpGet]
        public async Task<JsonResult> GetSubjectBanlance_old(string operKey, string startTime, string endTime, string operId, string timeType)
        {
            if (!startTime.Contains(":")) startTime += " 00:00";
            if (!endTime.Contains(":")) endTime += " 23:59";
            SQLQueue QQ = new SQLQueue(cmd);
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string happenTimeCondi = "";
            string checkedTimeCondi = "";
            string checkedLeftJoin = "";
            string accountHistoryLeftJoin = "";


            if (string.IsNullOrEmpty(timeType) || timeType == "byHappenTime")
            {
                happenTimeCondi = $" AND sm.happen_time >= '{startTime}'  AND sm.happen_time <= '{endTime}' ";
            }
            else
            {
                checkedLeftJoin =@$"	LEFT JOIN sheet_check_sheets_detail csd on sm.company_id=csd.company_id and sm.sheet_id=csd.business_sheet_id and business_sheet_type ~param
                                                    LEFT JOIN sheet_check_sheets_main csm on csd.company_id = csm.company_id and csd.sheet_id = csm.sheet_id";

                accountHistoryLeftJoin = @"LEFT JOIN sheet_check_sheets_detail csd on sm.company_id=csd.company_id and sm.sheet_id=csd.business_sheet_id and csd.business_sheet_type = sm.sheet_type
                                                    LEFT JOIN sheet_check_sheets_main csm on csd.company_id = csm.company_id and csd.sheet_id = csm.sheet_id";

                checkedTimeCondi = $@"
                    and csm.red_flag is null and csm.happen_time is not null and csm.happen_time >= '{startTime}'  
                    AND csm.happen_time <= '{endTime}' ";

            }

            string sellerIdCondition = "";
            string getterIdCondition = "";
            string getterIdCondition1 = "";
            if (operId != null)
            {
                sellerIdCondition = $"and seller_id= { operId }";
                getterIdCondition = $"and sm.getter_id= { operId }";
                getterIdCondition1 = $"and sp.getter_id= {operId}";
            }

          
            string sql = "";

            sql = @$"SELECT	
             round(SUM (money_inout_flag * ( 
                            (case when pw1.sub_type='QT' then coalesce(payway1_amount,0) else 0 end) 
                        + (case when pw2.sub_type='QT' then coalesce(payway2_amount,0) else 0 end) 
                        + (case when pw3.sub_type='QT' then coalesce(payway3_amount,0) else 0 end) 
             ) 	) :: NUMERIC,2 ) AS xs_in
             FROM	 sheet_sale_main sm
             LEFT JOIN ( SELECT sub_id, sub_name AS payway1_name, sub_type FROM cw_subject WHERE company_id ={companyID} ) pw1 ON sm.payway1_id = pw1.sub_id		
             LEFT JOIN ( SELECT sub_id, sub_name AS payway2_name, sub_type FROM cw_subject WHERE company_id ={companyID}) pw2 ON sm.payway2_id = pw2.sub_id 	
             LEFT JOIN ( SELECT sub_id, sub_name AS payway3_name, sub_type FROM cw_subject WHERE company_id ={companyID}) pw3 ON sm.payway3_id = pw3.sub_id
             {checkedLeftJoin.Replace("~param", "in ('X','T')")}           
            WHERE		sm.company_id ={companyID}  AND  sm.red_flag IS NULL	AND sm.approve_time IS NOT NULL	{happenTimeCondi} {checkedTimeCondi} {sellerIdCondition}";
            QQ.Enqueue("xs", sql);
            
            sql = @$"SELECT	
            round(SUM ((-money_inout_flag) * ( 
                        (case when pw1.sub_type='QT' then coalesce(payway1_amount,0) else 0 end) 
                    + (case when pw2.sub_type='QT' then coalesce(payway2_amount,0) else 0 end) 
                    + (case when pw3.sub_type='QT' then coalesce(payway3_amount,0) else 0 end) 
            ) 	) :: NUMERIC,2 ) AS cg_out 
            FROM		sheet_buy_main sm		
            LEFT JOIN ( SELECT sub_id, sub_name AS payway1_name, sub_type FROM cw_subject WHERE company_id ={companyID} ) pw1 ON sm.payway1_id = pw1.sub_id		
            LEFT JOIN ( SELECT sub_id, sub_name AS payway2_name, sub_type FROM cw_subject WHERE company_id ={companyID}) pw2 ON sm.payway2_id = pw2.sub_id 
            LEFT JOIN ( SELECT sub_id, sub_name AS payway3_name, sub_type FROM cw_subject WHERE company_id ={companyID}) pw3 ON sm.payway3_id = pw3.sub_id
            WHERE sm.company_id ={companyID} AND sm.red_flag IS NULL  AND sm.happen_time >= '{startTime}'  AND sm.happen_time <= '{endTime}' AND sm.approve_time IS NOT NULL  {sellerIdCondition}";
            QQ.Enqueue("cg", sql);

            sql = @$"SELECT	 round( SUM ( 
                                (case when cs1.sub_type='QT' then payway1_amount else 0 end)
                            + (case when cs2.sub_type='QT' then payway2_amount else 0 end)
                        ) :: NUMERIC, 2 ) AS zc_out 
            FROM sheet_fee_out_main sm 
            LEFT JOIN cw_subject cs1 ON cs1.company_id = sm.company_id 	AND sm.payway1_id = cs1.sub_id 
            LEFT JOIN cw_subject cs2 ON cs2.company_id = sm.company_id 	AND sm.payway2_id = cs2.sub_id 	
            {checkedLeftJoin.Replace("~param", "='ZC'")}
            WHERE	 sm.company_id = {companyID}  AND  sm.approve_time IS NOT NULL AND sm.red_flag IS NULL  and sm.sheet_type='ZC' {happenTimeCondi} {checkedTimeCondi} {getterIdCondition}	 ";
            QQ.Enqueue("fee_detail", sql);

            sql = @$"SELECT	 round( SUM ( 
                                (case when cs1.sub_type='QT' then payway1_amount else 0 end)
                            + (case when cs2.sub_type='QT' then payway2_amount else 0 end)
                        ) :: NUMERIC, 2 ) AS sr_in 
            FROM sheet_fee_out_main sm 
            LEFT JOIN cw_subject cs1 ON cs1.company_id = sm.company_id 	AND sm.payway1_id = cs1.sub_id 
            LEFT JOIN cw_subject cs2 ON cs2.company_id = sm.company_id 	AND sm.payway2_id = cs2.sub_id 	
            {checkedLeftJoin.Replace("~param", "='SR'")}
            WHERE	 sm.company_id = {companyID}  AND  sm.approve_time IS NOT NULL AND sm.red_flag IS NULL and sm.sheet_type='SR' {happenTimeCondi} {checkedTimeCondi} {getterIdCondition}	 ";
            QQ.Enqueue("Otherincome_detail", sql);

            sql = @$"select sum(case when sm.sheet_type in ('DH','YS') then sm.payway_amount else 0 end) as ys_add, 
                                        sum(case when sm.sheet_type ='YF' then sm.payway_amount else 0 end) as yf_reduce 
                from (
	                select *, payway1_id as payway_id, payway1_amount as payway_amount from sheet_prepay
	                union all 
	                select *, payway2_id as payway_id, payway2_amount as payway_amount from sheet_prepay
                    union all 
	                select *, payway3_id as payway_id, payway3_amount as payway_amount from sheet_prepay
                ) sm 
                left join cw_subject cs on sm.company_id=cs.company_id and sm.payway_id=cs.sub_id
                {accountHistoryLeftJoin}
            WHERE	 sm.company_id ={companyID} AND sm.red_flag IS NULL  and cs.sub_type='QT'  and sm.approve_time IS NOT NULL {happenTimeCondi} {checkedTimeCondi} {getterIdCondition}";
            QQ.Enqueue("AccountHistory", sql);//DH YS YF

            sql = @$"SELECT
            round(  SUM ( CASE WHEN sheet_type = 'SK'  THEN (
                    (case when p1.sub_type ='QT' then sm.payway1_amount else 0 end) 
                + (case when p2.sub_type = 'QT' then sm.payway2_amount else 0 end) 
                + (case when p3.sub_type = 'QT' then sm.payway3_amount else 0 end)
            ) else 0 end) :: NUMERIC,2 ) sk_in,
            round( 	SUM ( CASE WHEN sheet_type = 'FK'  THEN (
                    (case when p1.sub_type ='QT' then sm.payway1_amount else 0 end) 
                + (case when p2.sub_type = 'QT' then sm.payway2_amount else 0 end) 
                + (case when p3.sub_type = 'QT' then sm.payway3_amount else 0 end)
            ) ELSE 0 END ) :: NUMERIC,2 ) fk_out 
            from sheet_get_arrears_main sm
            LEFT JOIN cw_subject p1 on p1.company_id = sm.company_id and sm.payway1_id = p1.sub_id
			LEFT JOIN cw_subject p2 on p2.company_id = sm.company_id and sm.payway2_id = p2.sub_id
            LEFT JOIN cw_subject p3 on p3.company_id = sm.company_id and sm.payway3_id = p3.sub_id
            {checkedLeftJoin.Replace("~param", "in ('SK','FK')")}
            WHERE sm.company_id ={companyID} AND sm.red_flag IS NULL and sm.approve_time IS NOT NULL {happenTimeCondi} {checkedTimeCondi} {getterIdCondition}";
            QQ.Enqueue("arrears", sql);//SK FK

            decimal xs = 0, cg = 0, sk = 0, fk = 0, ys = 0, yf = 0, zc = 0, sr = 0;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "xs")
                {
                    dynamic xs_d = CDbDealer.Get1RecordFromDr(dr, false);
                    if (xs_d != null && xs_d.xs_in != "") xs = Convert.ToDecimal(xs_d.xs_in);
                }
                else if(sqlName == "cg")
                {
                    dynamic cg_d = CDbDealer.Get1RecordFromDr(dr, false);
                    if (cg_d != null && cg_d.cg_out != "") cg = Convert.ToDecimal(cg_d.cg_out);
                }
                else if (sqlName == "arrears")
                {
                    dynamic arrears = CDbDealer.Get1RecordFromDr(dr, false);
                    if (arrears != null && arrears.sk_in != "") sk = Convert.ToDecimal(arrears.sk_in);
                    if (arrears != null && arrears.fk_out != "") fk = Convert.ToDecimal(arrears.fk_out);
                }
                else if (sqlName == "AccountHistory")
                {
                    dynamic accountHistory = CDbDealer.Get1RecordFromDr(dr, false);
                    if (accountHistory != null && accountHistory.ys_add != "") ys = Convert.ToDecimal(accountHistory.ys_add);
                    if (accountHistory != null && accountHistory.yf_reduce != "") yf = Convert.ToDecimal(accountHistory.yf_reduce);
                }
                else if(sqlName == "fee_detail")
                {
                    dynamic fee_detail = CDbDealer.Get1RecordFromDr(dr, false);
                    if (fee_detail != null && fee_detail.zc_out != "") zc = Convert.ToDecimal(fee_detail.zc_out);
                }
                else if (sqlName == "Otherincome_detail")
                {
                    dynamic otherincome_detail = CDbDealer.Get1RecordFromDr(dr, false);
                    if (otherincome_detail != null && otherincome_detail.sr_in != "") sr = Convert.ToDecimal(otherincome_detail.sr_in);
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, xs, cg, sk, fk, ys, yf, zc, sr });

        }
       
       [HttpGet]
        public async Task<JsonResult> GetSubjectBanlance(string operKey, string startTime, string endTime, string operId, string timeType)
        {
            if (!startTime.Contains(":")) startTime += " 00:00";
            if (!endTime.Contains(":")) endTime += " 23:59";
            SQLQueue QQ = new SQLQueue(cmd);
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string happenTimeCondi = "";
            string checkedTimeCondi = "";
            string checkedLeftJoin = "";
            string accountHistoryLeftJoin = "";


            if (string.IsNullOrEmpty(timeType) || timeType == "byHappenTime")
            {
                happenTimeCondi = $" AND sm.happen_time >= '{startTime}'  AND sm.happen_time <= '{endTime}' ";
            }
            else
            {
                checkedLeftJoin =@$"	LEFT JOIN sheet_check_sheets_detail csd on sm.company_id=csd.company_id and sm.sheet_id=csd.business_sheet_id and business_sheet_type ~param
                                                    LEFT JOIN sheet_check_sheets_main csm on csd.company_id = csm.company_id and csd.sheet_id = csm.sheet_id";

                accountHistoryLeftJoin = @"LEFT JOIN sheet_check_sheets_detail csd on sm.company_id=csd.company_id and sm.sheet_id=csd.business_sheet_id and csd.business_sheet_type = sm.sheet_type
                                                    LEFT JOIN sheet_check_sheets_main csm on csd.company_id = csm.company_id and csd.sheet_id = csm.sheet_id";

                checkedTimeCondi = $@"
                    and csm.red_flag is null and csm.happen_time is not null and csm.happen_time >= '{startTime}'  
                    AND csm.happen_time <= '{endTime}' ";

            }

            string sellerIdCondition = "";
            string getterIdCondition = "";
            string getterIdCondition1 = "";
            if (operId != null)
            {
                sellerIdCondition = $"and seller_id= { operId }";
                getterIdCondition = $"and sm.getter_id= { operId }";
                getterIdCondition1 = $"and sp.getter_id= {operId}";
            }

          
            string sql = "";

            sql = @$"SELECT	
             round
             (
                SUM 
                (
                    money_inout_flag * 
                    ( 
                          (case when pw1.sub_type='QT' then coalesce(payway1_amount,0) else 0 end) 
                        + (case when pw2.sub_type='QT' then coalesce(payway2_amount,0) else 0 end) 
                        + (case when pw3.sub_type='QT' then coalesce(payway3_amount,0) else 0 end) 
                    ) 	
                ) :: NUMERIC,2 
             ) AS xs_in
             
             FROM sheet_sale_main sm
             LEFT JOIN ( SELECT sub_id, sub_name AS payway1_name, sub_type FROM cw_subject WHERE company_id ={companyID} ) pw1 ON sm.payway1_id = pw1.sub_id		
             LEFT JOIN ( SELECT sub_id, sub_name AS payway2_name, sub_type FROM cw_subject WHERE company_id ={companyID}) pw2 ON sm.payway2_id = pw2.sub_id 	
             LEFT JOIN ( SELECT sub_id, sub_name AS payway3_name, sub_type FROM cw_subject WHERE company_id ={companyID}) pw3 ON sm.payway3_id = pw3.sub_id
             {checkedLeftJoin.Replace("~param", "in ('X','T')")}           
            WHERE sm.company_id ={companyID}  AND  sm.red_flag IS NULL	AND sm.approve_time IS NOT NULL	{happenTimeCondi} {checkedTimeCondi} {sellerIdCondition}";
            QQ.Enqueue("xs", sql);
            
            sql = @$"SELECT	
            round(SUM ((-money_inout_flag) * ( 
                        (case when pw1.sub_type='QT' then coalesce(payway1_amount,0) else 0 end) 
                    + (case when pw2.sub_type='QT' then coalesce(payway2_amount,0) else 0 end) 
                    + (case when pw3.sub_type='QT' then coalesce(payway3_amount,0) else 0 end) 
            ) 	) :: NUMERIC,2 ) AS cg_out 
            FROM		sheet_buy_main sm		
            LEFT JOIN ( SELECT sub_id, sub_name AS payway1_name, sub_type FROM cw_subject WHERE company_id ={companyID} ) pw1 ON sm.payway1_id = pw1.sub_id		
            LEFT JOIN ( SELECT sub_id, sub_name AS payway2_name, sub_type FROM cw_subject WHERE company_id ={companyID}) pw2 ON sm.payway2_id = pw2.sub_id 
            LEFT JOIN ( SELECT sub_id, sub_name AS payway3_name, sub_type FROM cw_subject WHERE company_id ={companyID}) pw3 ON sm.payway3_id = pw3.sub_id
            WHERE sm.company_id ={companyID} AND sm.red_flag IS NULL  AND sm.happen_time >= '{startTime}'  AND sm.happen_time <= '{endTime}' AND sm.approve_time IS NOT NULL  {sellerIdCondition}";
            QQ.Enqueue("cg", sql);

            sql = @$"SELECT	 round( SUM ( 
                                (case when cs1.sub_type='QT' then payway1_amount else 0 end)
                            + (case when cs2.sub_type='QT' then payway2_amount else 0 end)
                        ) :: NUMERIC, 2 ) AS zc_out 
            FROM sheet_fee_out_main sm 
            LEFT JOIN cw_subject cs1 ON cs1.company_id = sm.company_id 	AND sm.payway1_id = cs1.sub_id 
            LEFT JOIN cw_subject cs2 ON cs2.company_id = sm.company_id 	AND sm.payway2_id = cs2.sub_id 	
            {checkedLeftJoin.Replace("~param", "='ZC'")}
            WHERE	 sm.company_id = {companyID}  AND  sm.approve_time IS NOT NULL AND sm.red_flag IS NULL  and sm.sheet_type='ZC' {happenTimeCondi} {checkedTimeCondi} {getterIdCondition}	 ";
            QQ.Enqueue("fee_detail", sql);

            sql = @$"SELECT	 round( SUM ( 
                                (case when cs1.sub_type='QT' then payway1_amount else 0 end)
                            + (case when cs2.sub_type='QT' then payway2_amount else 0 end)
                        ) :: NUMERIC, 2 ) AS sr_in 
            FROM sheet_fee_out_main sm 
            LEFT JOIN cw_subject cs1 ON cs1.company_id = sm.company_id 	AND sm.payway1_id = cs1.sub_id 
            LEFT JOIN cw_subject cs2 ON cs2.company_id = sm.company_id 	AND sm.payway2_id = cs2.sub_id 	
            {checkedLeftJoin.Replace("~param", "='SR'")}
            WHERE	 sm.company_id = {companyID}  AND  sm.approve_time IS NOT NULL AND sm.red_flag IS NULL and sm.sheet_type='SR' {happenTimeCondi} {checkedTimeCondi} {getterIdCondition}	 ";
            QQ.Enqueue("Otherincome_detail", sql);

            sql = @$"select sum(case when sm.sheet_type in ('DH','YS') then sm.payway_amount else 0 end) as ys_add, 
                                        sum(case when sm.sheet_type ='YF' then sm.payway_amount else 0 end) as yf_reduce 
                from (
	                select *, payway1_id as payway_id, payway1_amount as payway_amount from sheet_prepay
	                union all 
	                select *, payway2_id as payway_id, payway2_amount as payway_amount from sheet_prepay
                    union all 
	                select *, payway3_id as payway_id, payway3_amount as payway_amount from sheet_prepay
                ) sm 
                left join cw_subject cs on sm.company_id=cs.company_id and sm.payway_id=cs.sub_id
                {accountHistoryLeftJoin}
            WHERE	 sm.company_id ={companyID} AND sm.red_flag IS NULL  and cs.sub_type='QT'  and sm.approve_time IS NOT NULL {happenTimeCondi} {checkedTimeCondi} {getterIdCondition}";
            QQ.Enqueue("AccountHistory", sql);//DH YS YF

            sql = @$"SELECT
            round(  SUM ( CASE WHEN sheet_type = 'SK'  THEN (
                    (case when p1.sub_type ='QT' then sm.payway1_amount else 0 end) 
                + (case when p2.sub_type = 'QT' then sm.payway2_amount else 0 end) 
                + (case when p3.sub_type = 'QT' then sm.payway3_amount else 0 end)
            ) else 0 end) :: NUMERIC,2 ) sk_in,
            round( 	SUM ( CASE WHEN sheet_type = 'FK'  THEN (
                    (case when p1.sub_type ='QT' then sm.payway1_amount else 0 end) 
                + (case when p2.sub_type = 'QT' then sm.payway2_amount else 0 end) 
                + (case when p3.sub_type = 'QT' then sm.payway3_amount else 0 end)
            ) ELSE 0 END ) :: NUMERIC,2 ) fk_out 
            from sheet_get_arrears_main sm
            LEFT JOIN cw_subject p1 on p1.company_id = sm.company_id and sm.payway1_id = p1.sub_id
			LEFT JOIN cw_subject p2 on p2.company_id = sm.company_id and sm.payway2_id = p2.sub_id
            LEFT JOIN cw_subject p3 on p3.company_id = sm.company_id and sm.payway3_id = p3.sub_id
            {checkedLeftJoin.Replace("~param", "in ('SK','FK')")}
            WHERE sm.company_id ={companyID} AND sm.red_flag IS NULL and sm.approve_time IS NOT NULL {happenTimeCondi} {checkedTimeCondi} {getterIdCondition}";
            QQ.Enqueue("arrears", sql);//SK FK

            decimal xs = 0, cg = 0, sk = 0, fk = 0, ys = 0, yf = 0, zc = 0, sr = 0;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "xs")
                {
                    dynamic xs_d = CDbDealer.Get1RecordFromDr(dr, false);
                    if (xs_d != null && xs_d.xs_in != "") xs = Convert.ToDecimal(xs_d.xs_in);
                }
                else if(sqlName == "cg")
                {
                    dynamic cg_d = CDbDealer.Get1RecordFromDr(dr, false);
                    if (cg_d != null && cg_d.cg_out != "") cg = Convert.ToDecimal(cg_d.cg_out);
                }
                else if (sqlName == "arrears")
                {
                    dynamic arrears = CDbDealer.Get1RecordFromDr(dr, false);
                    if (arrears != null && arrears.sk_in != "") sk = Convert.ToDecimal(arrears.sk_in);
                    if (arrears != null && arrears.fk_out != "") fk = Convert.ToDecimal(arrears.fk_out);
                }
                else if (sqlName == "AccountHistory")
                {
                    dynamic accountHistory = CDbDealer.Get1RecordFromDr(dr, false);
                    if (accountHistory != null && accountHistory.ys_add != "") ys = Convert.ToDecimal(accountHistory.ys_add);
                    if (accountHistory != null && accountHistory.yf_reduce != "") yf = Convert.ToDecimal(accountHistory.yf_reduce);
                }
                else if(sqlName == "fee_detail")
                {
                    dynamic fee_detail = CDbDealer.Get1RecordFromDr(dr, false);
                    if (fee_detail != null && fee_detail.zc_out != "") zc = Convert.ToDecimal(fee_detail.zc_out);
                }
                else if (sqlName == "Otherincome_detail")
                {
                    dynamic otherincome_detail = CDbDealer.Get1RecordFromDr(dr, false);
                    if (otherincome_detail != null && otherincome_detail.sr_in != "") sr = Convert.ToDecimal(otherincome_detail.sr_in);
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, xs, cg, sk, fk, ys, yf, zc, sr });

        }
       
        [HttpGet]
        public async Task<JsonResult> GetPaymentMethods(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            
            SQLQueue QQ = new SQLQueue(cmd);
            var sql = $@"
                SELECT sub_id, sub_name 
                FROM cw_subject 
                WHERE company_id = {companyID} 
                AND sub_type = 'QT' 
                AND coalesce(status,'1') = '1'
                ORDER BY sub_code";
            
            QQ.Enqueue("methods", sql);
            
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "methods")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }
    }



}
