@page
@model ArtisanManage.Pages.BaseInfo.CheckSheetBySellerModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var m_db_id = "10";

    	    var newCount = 1;


    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
                
                //$("#gridItems").on("cellclick", function (event) {

                //    var args = event.args;
                //     var startDay = $('#startDay').jqxDateTimeInput('val');
                //    var endDay = $('#endDay').jqxDateTimeInput('val');
                //    var seller_id = args.row.bounddata.seller_id;
                //    var seller_name = args.row.bounddata.seller_name;
                //    var url = `WorkFlow/CheckAccount/CheckSheetsSheetHistory?&seller_id=${seller_id}&seller_name=${seller_name}}&startDay=${startDay}&endDay=${endDay}`;
                //    if (args.datafield == "seller_name" && seller_name) {
                        
                //        window.parent.newTabPage(`查交账单`, `${url}` );
                //    }


                //});


                QueryData();
            });


    </script>
</head>

<body style="overflow:hidden">
     <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail" style="width:calc(100% - 100px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>


    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>