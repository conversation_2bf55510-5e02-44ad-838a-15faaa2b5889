﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class BranchsViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
        
        public BranchsViewModel(CMySbCommand cmd):base(Services.MenuId.infoBranch)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"status",new DataItem(){Title = "状态", LabelFld = "cls_status_name", LabelInDB = false, Value = "normal", Label = "正常",ButtonUsage = "list", CompareOperator="=",
                   
                     Source = @"[{v:'normal',l:'正常',condition:""(status = '1' or status is null)""},
                               {v:'stop',l:'停用',condition:""status = '0' ""},
                               {v:'all',l:'所有',condition:""true""}]"


                }},
                 {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称/助记码",UseJQWidgets=false, SqlFld="branch_name,py_str",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     HasCheck=true,
                     IdColumn="branch_id",TableName="info_branch",
                     ShowContextMenu=true,
                     Sortable=true,
                     //ContextMenuHTML="<ul><li id='edit'>编辑</li><li id='remove'>删除</li><li id='BatchOperation'>批量操作</li></ul>",
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"branch_id",new DataItem(){Title="仓库编号", Width="80",SqlFld = "branch_id",Hidden=true,HideOnLoad = true}},
                       {"i",new DataItem(){Title="仓库编号", Width="80",SqlFld = "branch_id",Hidden=true,HideOnLoad = true}},
                       {"branch_name",new DataItem(){Title="仓库名称", Width="180",Linkable=true}},
                       {"branch_addr",new DataItem(){Title="仓库地址", Width="180"}},
                       {"branch_type",new DataItem(){Title="仓库类型",Width="180",SqlFld="(case WHEN branch_type='store' THEN '仓库' when branch_type='truck' then '车辆' ELSE '其他' END)"}},
                       {"status",new DataItem(){Title="状态",Width="180",SqlFld="(case WHEN status='0' THEN '停用' ELSE '正常' END)"}},
                       {"allow_negative_stock",new DataItem(){Title="允许负库存",Width="150",SqlFld ="(case allow_negative_stock when false then '否' else '是' end)", } },
                       {"allow_negative_stock_order",new DataItem(){Title="订单允许负可用库存",Width="150",SqlFld ="(case allow_negative_stock_order when false then '否' else '是' end)", } },
                       {"negative_stock_accordance",new DataItem(){Title="负库存依据",Width="150",SqlFld ="(case negative_stock_accordance when 'usable' then '可用库存' else '实际库存' end)",} },

                     },
                     QueryFromSQL="from info_branch where company_id=~COMPANY_ID ~SQL_VARIABLE1" ,QueryOrderSQL="order by order_index,branch_id"
                  }
                } 
            }; 
        }
        public async Task OnGet(string forSelect)
        {  
            await InitGet(cmd);
            ForSelect = forSelect == "1";
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }

        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            string err = "";
            //cmd.CommandText = $"select company_id from stock where branch_id in ({rowIDs}) and company_id={company_id} limit 1";

            cmd.CommandText = $@"
                select company_id from stock where branch_id in ({rowIDs}) and company_id={company_id}
                union select company_id from sheet_sale_main where branch_id in ({rowIDs}) and company_id={company_id} 
                union select company_id from sheet_sale_order_main where branch_id in ({rowIDs}) and company_id={company_id} 
                union select company_id from sheet_buy_main  where branch_id in ({rowIDs}) and company_id={company_id}
                union select company_id from sheet_buy_order_main where branch_id in ({rowIDs}) and company_id={company_id}
                union select company_id from sheet_move_main where (from_branch_id in ({rowIDs}) or to_branch_id in ({rowIDs})) and company_id={company_id} limit 1
             ";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                return "已产生库存记录,无法删除";
            } 
        
            return err;
        }
    }



    [Route("api/[controller]/[action]")]
    public class BranchsViewController : QueryController
    { 
        public BranchsViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BranchsViewModel model = new BranchsViewModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {// string gridID,int startRow,int endRow,bool bNewQuery){
            BranchsViewModel model = new BranchsViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            BranchsViewModel model = new BranchsViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd, "info_branch");// gridID, startRow, endRow, bNewQuery);
            return records;
        }
    }
}
