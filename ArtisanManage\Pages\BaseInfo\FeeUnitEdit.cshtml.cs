using ArtisanManage.Models;
using ArtisanManage.Pages.Sheets;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using Org.BouncyCastle.Asn1.X509;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System;

namespace ArtisanManage.Pages.BaseInfo
{
    public class FeeUnitEditModel : PageFormModel
    {
        public FeeUnitEditModel(CMySbCommand cmd) : base(Services.MenuId.infoFeeUnit)
        {
            this.cmd = cmd;
            this.PageTitle = "费用单位档案";
            this.DocType = "feeUnit";
            this.LogChange = true;
            string dateSource = @"[{v:'01',l:'每月1号'},{v:'02',l:'每月2号'},{v:'03',l:'每月3号'},{v:'04',l:'每月4号'},{v:'05',l:'每月5号'},{v:'06',l:'每月6号'},{v:'07',l:'每月7号'},{v:'08',l:'每月8号'},{v:'09',l:'每月9号'},{v:'10',l:'每月10号'},{v:'11',l:'每月11号'},
            {v:'12',l:'每月12号'},{v:'13',l:'每月13号'},{v:'14',l:'每月14号'},{v:'15',l:'每月15号'},{v:'16',l:'每月16号'},{v:'17',l:'每月17号'},{v:'18',l:'每月18号'},{v:'19',l:'每月19号'},{v:'20',l:'每月20号'},{v:'21',l:'每月21号'},{v:'22',l:'每月22号'},{v:'23',l:'每月23号'},{v:'24',l:'每月24号'},{v:'25',l:'每月25号'},{v:'26',l:'每月26号'},{v:'27',l:'每月27号'},{v:'28',l:'每月28号'},]";
            DataItems = new Dictionary<string, DataItem>()//这里配置了费用单位信息的iframe
            {
                {"supcust_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"sup_name",new DataItem(){Title="费用单位名称",Width="390",Necessary=true,FldArea="divHead"}},
                {"py_str",new DataItem(){Title="助记码",FldArea="divHead"}},
                {"supcust_no",new DataItem(){Title="费用单位编号",FldArea="divHead"}},
                {"sup_alias",new DataItem(){Title="别名",FldArea="divHead"}},
                {"boss_name",new DataItem(){Title="老板姓名",Necessary=true,FldArea="divHead"}},
                {"mobile",new DataItem(){Title="联系电话",Necessary=true,FldArea="divHead"}},
                {"sup_addr",new DataItem(){Title="费用单位地址",FldArea="divHead"}},
                {"addr_lat",new DataItem(){Title="纬度",FldArea="divHead",CtrlType="hidden"}},
                {"addr_lng",new DataItem(){Title="经度",FldArea="divHead",CtrlType="hidden"}},
                {"region_id",new DataItem(){Title="片区",Necessary=true,FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",MaxRecords="500", DropDownHeight="200",DropDownWidth="150", TreePathFld="other_region",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by order_index , region_id"
                }},
                {"sup_group",new DataItem(){Title="渠道",FldArea="divHead",LabelFld="group_name",CtrlType="jqxInput",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",
                    SqlForOptions="select group_id as v,group_name as l from info_supcust_group"
                }},
                {"sup_rank",new DataItem(){Title="等级",FldArea="divHead",LabelFld="rank_name",CtrlType="jqxInput",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",
                    SqlForOptions="select rank_id as v,rank_name as l from info_supcust_rank"
                }},

                {"supcust_flag",new DataItem(){Title="类型",FldArea="divHead",CtrlType="hidden", Value="W"}},
                {"status",new DataItem(){Title="状态",LabelFld="cls_status_name",FldArea="divHead",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", DropDownHeight="60",DropDownWidth="80",
                     Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}},
                {"create_time",new DataItem(){Title="添加时间",FldArea="divHead",SqlFld = "to_char(create_time,'yyyy-mm-dd HH:MM:SS')",Disabled=true} },
                {"retail_wholesale_flag",new DataItem(){Title="销售方式",FldArea="divHead",  Value="w",Label="批发", ButtonUsage="list", DropDownHeight="80",DropDownWidth="150",
                    Source = "[{v:'r',l:'零售'},{v:'w',l:'批发'}]"}},
                {"acct_cust_id",new DataItem(){FldArea="divHead",Title="结算单位",LabelFld="acct_cust_name",CtrlType="jqxInput", ButtonUsage="list",SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where acct_cust_id is null"}},
                {"sup_door_photo",new DataItem(){ FldArea="divHead",Hidden=true,
                FuncDealMe = doorPhotoUrl =>
                {
                     return HuaWeiObs.BucketLinkHref+"uploads/"+doorPhotoUrl;
                }
                }},
                {"sup_other_photo",new DataItem(){ FldArea="divHead",Hidden=true,
                FuncDealMe = otherPhotosUrl =>
                {
                    List<string> urls = (List<string>)otherPhotosUrl.Split(",").ToList().Select(url =>
                    {
                        return HuaWeiObs.BucketLinkHref +"uploads/"+ url;
                    });
                   return string.Join(",",urls);
                 }}},
                 {"acct_type",new DataItem(){Title="结算类型",SqlFld="info_supcust.acct_type", GetOptionsOnLoad=true, LabelFld="acct_type_name",FldArea="divHead",ButtonUsage="list",QueryOnChange = true,MaxRecords="500", LabelInDB = false,
                     Source = @"[{v:'pay',l:'现结',condition:""(s.acct_type = 'pay'or s.acct_type is null)""},
                               {v:'arrears',l:'欠款',condition:""s.acct_type = 'arrears' ""},]"
                    }},
                {"acct_way_id",new DataItem(){Title="结算方式",LabelFld="acct_way_name", FldArea="divHead",ButtonUsage="list",QueryOnChange = true,MaxRecords="500",
                    SqlForOptions="SELECT info_acct_way.acct_way_id AS v,info_acct_way.acct_way_name AS l FROM info_acct_way where info_acct_way.acct_type='~CONDI_DATA_ITEM'",CONDI_DATA_ITEM="acct_type"}},


                {"charge_seller",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="charge_name",CtrlType="jqxInput",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",
                    SqlForOptions=$"select oper_id as v,oper_name as l from info_operator where COALESCE(status,'1')='1' "
                }},
                {"creator_id",new DataItem(){Title="创建人",FldArea="divHead",LabelFld="creator_name",CtrlType="jqxInput",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",
                    SqlForOptions=$"select oper_id as v,oper_name as l from info_operator where COALESCE(status,'1')='1' "
                }},
                {"license_no",new DataItem(){FldArea="divHead",Title="营业执照" }},
                 {"max_arrears",new DataItem(){Title="欠款额度",FldArea="divHead"}},
                 {"max_arrears_days",new DataItem(){Title="欠款天数",FldArea="divHead"}},
                 {"sup_order_index",new DataItem(){Title="显示顺序",FldArea="divHead"}},
                 {"supcust_remark",new DataItem(){Title="备注",FldArea="divHead"}},
                 {"allow_change_price",new DataItem(){FldArea="divHead",CtrlType="jqxCheckBox",Title="app可以改价",Value="True" }},
                 {"approve_status",new DataItem(){Title="审核状态",CtrlType="hidden",HideOnLoad=true,FldArea="divHead"}},
                 {"arrears_order_start_date",new DataItem(){Title="对账起始日期",FldArea="divTestPage",
                 Source = dateSource,ButtonUsage="list",DropDownWidth="100"}},
                 {"arrears_order_end_date",new DataItem(){Title="对账截至日期",FldArea="divTestPage",
                 Source = dateSource,ButtonUsage="list",DropDownWidth="100"}},
                 {"arrears_get_start_date",new DataItem(){Title="收款起始日期",FldArea="divTestPage",
                 Source = dateSource,ButtonUsage="list",DropDownWidth="100"}},
                 {"arrears_get_end_date",new DataItem(){Title="收款截至日期",FldArea="divTestPage",
                 Source = dateSource,ButtonUsage="list",DropDownWidth="100"}},
            };


            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridAddress" ,new FormDataGrid(){

                   MinRows=3,AutoAddRow=true,
                   Height=300,
                  JSFixColumnCellRender = @"
function(row, column, value) { 
     return '<div style=""height:100%;display:flex;justify-content:center;align-items:center;"">' + (row+1) + '</div>';
}",
                  Columns = new Dictionary<string, DataItem>()
                   {
                       {"addr_id",new DataItem(){Title="id",Width="100",Hidden=true}},
                       {"addr_status",new DataItem(){Title="状态",Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]", ButtonUsage="list",DropDownHeight="80",DropDownWidth="80" }},

                       {"addr_order",new DataItem(){Title="顺序"}},
                       {"receiver_name",new DataItem(){Title="收货人"}},
                       {"receiver_mobile",new DataItem(){Title="收货人电话"}},
                       {"addr_desc",new DataItem(){Title="收货地址",Width="200",Necessary=true}},

                   },

                  GridIdFld="addr_id",
                  GridIdFldIsSerial=true,

                   TableName="info_client_address",
                   IdFld="client_id",
                   SelectFromSQL=@"from info_client_address where client_id='~ID' order by addr_order"
                }}

            };


            m_idFld = "supcust_id"; m_nameFld = "sup_name";
            m_tableName = "info_supcust";
            m_selectFromSQL = @"from info_supcust  
left join (select region_id,region_name from info_region where company_id=~COMPANY_ID) r on info_supcust.region_id = r.region_id         
left join (select supcust_id as s_id,sup_name as acct_cust_name from info_supcust where company_id=~COMPANY_ID ) ac on ac.s_id = info_supcust.acct_cust_id
left join (select oper_id  as charge_id,oper_name as charge_name from info_operator where company_id=~COMPANY_ID)charge on charge.charge_id = info_supcust.charge_seller
left join (select group_id,group_name from info_supcust_group where company_id=~COMPANY_ID) g on g.group_id = info_supcust.sup_group
left join (select oper_id, oper_name creator_name from info_operator where company_id=~COMPANY_ID) io on io.oper_id = info_supcust.creator_id and (status is null or status = 1)
left join (select rank_id,rank_name from info_supcust_rank where company_id=~COMPANY_ID) sr on sr.rank_id = info_supcust.sup_rank 

    LEFT JOIN  info_acct_way ON info_supcust.acct_way_id = info_acct_way.acct_way_id  where info_supcust.supcust_id='~ID'     ";

        }

        //left join (select supcust_id as s_id,sup_name as acct_cust_name from info_supcust where company_id=~COMPANY_ID ) ac on ac.s_id = info_supcust.acct_cust_id
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            if (DataItems["supcust_id"].Value == "")
            {
                DataItems["creator_id"].Value = OperID;
                DataItems["creator_id"].Label = OperName;
            }
            string supcustNoSerial = "";
            if (JsonCompanySetting != "")
            {
                dynamic set = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
                supcustNoSerial = set.supcustNoSerial ?? "false";
                string clientBossNecessary = set.clientBossNecessary ?? "true";
                if (clientBossNecessary.ToLower() == "false")
                {
                    DataItems["boss_name"].Necessary = false;
                }
                string clientMobileNecessary = set.clientMobileNecessary ?? "true";
                if (clientMobileNecessary.ToLower() == "false")
                {
                    DataItems["mobile"].Necessary = false;
                }

                string doorPicNecessary = set.doorPicNecessary ?? "true";
                if (doorPicNecessary.ToLower() == "false")
                {
                    DataItems["door_pic"].Necessary = false;
                }
                string clientLocationNecessary = set.clientLocationNecessary ?? "true";
                if (clientLocationNecessary.ToLower() == "false")
                {
                    DataItems["addr_desc"].Necessary = false;
                }

                string clientGroupNecessary = set.clientGroupNecessary ?? "false";
                if (clientGroupNecessary.ToLower() == "true")
                {
                    DataItems["sup_group"].Necessary = true;
                }
                string clientLevelNecessary = set.clientLevelNecessary ?? "false";
                if (clientLevelNecessary.ToLower() == "true")
                {
                    DataItems["sup_rank"].Necessary = true;
                }
                string clientRelateSeller = set.clientRelateSeller ?? "false";
                if (clientRelateSeller.ToLower() == "false")
                {
                    DataItems["charge_seller"].Hidden = true;
                }


            }
            string supcust_id = CPubVars.RequestV(Request, "supcust_id");
            if (supcust_id == "" && supcustNoSerial == "True")
            {

                string querySql = @$"select supcust_no from info_supcust where company_id={company_id} and supcust_flag = 'W' and supcust_no ~ '^\d+$' order by supcust_no::NUMERIC desc limit 1";


                dynamic queryResult = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);
                long supcust_no = 1;
                string s_supcust_no = supcust_no.ToString();
                if (queryResult != null && queryResult.supcust_no != "")
                {
                    s_supcust_no = queryResult.supcust_no;
                    supcust_no = Convert.ToInt64(s_supcust_no) + 1;
                    s_supcust_no = supcust_no.ToString().PadLeft(s_supcust_no.Length, '0');
                }
                DataItems["supcust_no"].Value = s_supcust_no;
            }

        }
        public async Task OnGet()
        {
            string now = CPubVars.GetDateText(DateTime.Now);
            string supcust_id = CPubVars.RequestV(Request, "supcust_id");
            if (supcust_id == "") DataItems["create_time"].Value = now;

            await InitGet(cmd);

            // DataItems["make_arrears_order_start_date"].Label = "每月" + DataItems["make_arrears_order_start_date"].Value + "号";
        }
        
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class FeeUnitEditController : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public FeeUnitEditController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }

        [HttpPost]
        public async Task<JsonResult> SaveSupcustPiaoZhengTong([FromBody] dynamic data)
        {
            TicketAccessService ticketAccessService = new TicketAccessService();


            var resp = await ticketAccessService.AddSupcust(_httpClientFactory, (string)data.token, (string)data.oentityCode, (string)data.bossName, (string)data.mobile, (string)data.supName, (string)data.address);
            return Json(new { result = "OK", resp });

        }
        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues, string condiDataItem)
        //这个玩意处理对popitem中所有下拉框的查询请求
        {
            FeeUnitEditModel model = new FeeUnitEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues, condiDataItem);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            FeeUnitEditModel model = new FeeUnitEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }

        [HttpPost]
        public async Task<JsonResult> CheckData([FromBody] dynamic request)
        {
            string mobile = request.mobile;
            var isNewRecord = request.isNewRecord.ToString();
            string supcust_id = request.supcust_id;
            Security.GetInfoFromOperKey((string)request.operKey, out string companyID, out string operID);

            if (!string.IsNullOrEmpty(mobile))
            {
                string condiOldCondi = "";
                if (isNewRecord != "True")
                {
                    condiOldCondi = $@"and supcust_id <> {supcust_id}";
                }
                string sql = $@"select supcust_flag from info_supcust where company_id={companyID} and TRIM(mobile)='{mobile}' {condiOldCondi} ";
                var rec = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (rec != null && rec.Count > 0)
                {
                    return new JsonResult(new { result = "Error", msg = "已存在相同手机号的客户、供应商或费用单位" });
                }
            }
            return new JsonResult(new { result = "OK", msg = "" });
        }
        [HttpPost]
        public async Task<JsonResult> GetDataSet([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            string acct_cust_id = data.acct_cust_id;
            string sql = @$"select arrears_order_start_date,arrears_order_end_date,arrears_get_start_date,arrears_get_end_date  from info_supcust where supcust_id = {acct_cust_id} and company_id = {companyID}";
            dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (ret != null)
            {
                if (string.IsNullOrEmpty((string)ret.arrears_order_start_date)
                    && string.IsNullOrEmpty((string)ret.arrears_order_start_date))
                {
                    return new JsonResult(new { result = "EMPTY", msg = "" });
                }
                return new JsonResult(new { result = "OK", msg = "", ret = ret });
            }
            else
            {
                return new JsonResult(new { result = "ERROR", msg = "" });
            }

        }

        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic request)
        //这里处理popitems的保存请求
        {
            string status = request.status;
            string supcust_id = request.supcust_id;
            string supcust_no = request.supcust_no;
            string mobile = request.mobile;

            string sup_name = request.sup_name;
            FeeUnitEditModel model = new FeeUnitEditModel(cmd);
            Security.GetInfoFromOperKey((string)request.operKey, out string companyID, out string operID);
            request.company_id = companyID;

            #region 对账日期设置
            // 对账起止日期编辑
            if (string.IsNullOrEmpty((string)request.arrears_order_start_date) ^ string.IsNullOrEmpty((string)request.arrears_order_end_date))
            {
                return new JsonResult(new { result = "Error", msg = "必须同时设置对账起止日期" });
            }
            if (!string.IsNullOrEmpty((string)request.arrears_order_start_date) && !string.IsNullOrEmpty((string)request.arrears_order_end_date))
            {
                if (int.Parse((string)request.arrears_order_start_date) > int.Parse((string)request.arrears_order_end_date))
                {
                    return new JsonResult(new { result = "Error", msg = "对账起始日期必须早于截止日期" });
                }

                // request.make_arrears_order_date = request.make_arrears_order_start_date + "," + request.make_arrears_order_end_date;
            }


            // 收款起止日期编辑
            if (string.IsNullOrEmpty((string)request.arrears_get_start_date) ^ string.IsNullOrEmpty((string)request.arrears_get_end_date))
            {
                return new JsonResult(new { result = "Error", msg = "必须同时设置收款起止日期" });
            }
            if (!string.IsNullOrEmpty((string)request.arrears_get_start_date) && !string.IsNullOrEmpty((string)request.arrears_get_end_date))
            {
                if (int.Parse((string)request.arrears_get_start_date) > int.Parse((string)request.arrears_get_end_date))
                {
                    return new JsonResult(new { result = "Error", msg = "收款起始日期必须早于截止日期" });
                }
                // request.get_arrears_order_date = request.get_arrears_order_start_date + "," + request.get_arrears_order_end_date;
            }

            // 检查是否为结算单位，下属若有其他门店，一并设置
            if (supcust_id != "")
            {
                string sup_ids = $"select string_agg(supcust_id::text, ',') as sub_ids from info_supcust where acct_cust_id = {supcust_id} and company_id = {companyID}";
                dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(sup_ids, cmd);
                if (ret != null && !string.IsNullOrEmpty((string)request.arrears_get_start_date) && !string.IsNullOrEmpty((string)request.arrears_order_start_date))
                {
                    // 结算单位找到下属门店
                    if (!string.IsNullOrEmpty((string)ret.sub_ids))
                    {
                        string updateDateSql = @$"update info_supcust set 
                        arrears_get_start_date = '{request.arrears_get_start_date}',
                        arrears_get_end_date = '{request.arrears_get_end_date}',
                        arrears_order_start_date = '{request.arrears_order_start_date}',
                        arrears_order_end_date = '{request.arrears_order_end_date}'
                        where company_id = {companyID} and supcust_id in ({ret.sub_ids})";

                        cmd.CommandText = updateDateSql;

                        await cmd.ExecuteNonQueryAsync();
                    }

                }

            }

            #endregion

            cmd.CommandText = $"select supcust_flag from info_supcust  where company_id={companyID}  and  sup_name='{request.sup_name}';";
            var supcust_flag = await cmd.ExecuteScalarAsync();
            var isNewRecord = request.isNewRecord.ToString();

            if (supcust_flag != null && isNewRecord == "True")
            {
                if (supcust_flag.ToString() == "C")
                {
                    return new JsonResult(new { result = "Error", msg = "客户档案已存在同名的记录" });
                }
                else if (supcust_flag.ToString() == "S")
                {
                    return new JsonResult(new { result = "Error", msg = "供应商档案已存在同名的记录" });
                }
                else if (supcust_flag.ToString() == "W")
                {
                    return new JsonResult(new { result = "Error", msg = "费用单位档案已存在同名的记录" });
                }
            }
            //if (!string.IsNullOrEmpty(mobile))
            //{
            //    string condiOldCondi = "";
            //    if (isNewRecord != "True")
            //    {
            //        condiOldCondi = $@"and supcust_id <> {supcust_id}";
            //    }
            //    string sql = $@"select supcust_flag from info_supcust where company_id={companyID} and TRIM(mobile)='{mobile}'{condiOldCondi} ";
            //    var rec = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //    if (rec != null && rec.Count > 0)
            //    {
            //        return new JsonResult(new { result = "Error", msg = "已存在相同手机号的客户或供应商" });
            //    }
            //}

            if (status == "0" && !string.IsNullOrEmpty(supcust_id))
            {
                string msg = "";
                List<ExpandoObject> list = await CDbDealer.GetRecordsFromSQLAsync($"select supcust_id from arrears_balance where company_id = {companyID} and supcust_id={supcust_id} and abs(balance)>0.01", cmd);
                if (list.Count() > 0)
                {
                    //result = "error";
                    msg = "该费用单位有欠款,不能停用";
                }
                list = await CDbDealer.GetRecordsFromSQLAsync($"select supcust_id from prepay_balance where company_id = {companyID} and supcust_id={supcust_id} and abs(balance)>0.01", cmd);
                if (list.Count() > 0)
                {
                    // result = "error";
                    msg = "该费用单位有预收款,不能停用";
                }
                if (msg != "")
                {
                    return new JsonResult(new { result = "Error", msg });
                }

            }
            string dt = $"{DateTime.Today:yyyyMM}";
            string folderPath = $"uploads/{dt}/";

            string sup_door_photo = request.sup_door_photo;
            //var ddd=model.DataItems["item_id"].Value;
            if (!HuaWeiObs.IsB64(sup_door_photo) && sup_door_photo.StartsWith(HuaWeiObs.BucketLinkHref))
            {
                sup_door_photo = sup_door_photo.Replace($"{HuaWeiObs.BucketLinkHref}/uploads", "");
                request.sup_door_photo = sup_door_photo;
            }
            else
            {
                sup_door_photo = sup_door_photo.Replace("data:image/jpeg;base64,", "");

                string doorPicName = $"sup_pic_{companyID}_{CommonTool.GetTimeStamp()}_{new Random().Next(1, 100)}";
                string fileExtension = ".jpeg";
                string path = folderPath + doorPicName + fileExtension;

                using (MemoryStream stream = new MemoryStream(Convert.FromBase64String(sup_door_photo)))
                {
                    try
                    {
                        await HuaWeiObs.Save(_httpClientFactory, stream, path);
                    }
                    finally
                    {
                        stream.Close();
                    }
                    string door_picture_indb = $"/{dt}/{doorPicName}{fileExtension}";
                    request.sup_door_photo = door_picture_indb;
                }
            }

            string sup_other_photo = request.sup_other_photo;
            if (sup_other_photo != null)
            {
                List<string> photos = sup_other_photo.Split("|").ToList();
                List<string> photos_in_db = new List<string>();
                foreach (string other_photo in photos)
                {
                    //var ddd=model.DataItems["item_id"].Value;
                    if (!HuaWeiObs.IsB64(other_photo) && other_photo.StartsWith(HuaWeiObs.BucketLinkHref))
                    {
                        string photo_url = other_photo.Replace($"{HuaWeiObs.BucketLinkHref}/uploads", "");
                        photos_in_db.Add(photo_url);
                    }
                    else
                    {
                        string other_photo_base64 = other_photo.Replace("data:image/jpeg;base64,", "");

                        string otherPicName = $"sup_pic_{companyID}_{CommonTool.GetTimeStamp()}_{new Random().Next(1, 100)}";
                        string fileExtension = ".jpeg";
                        string path = folderPath + otherPicName + fileExtension;

                        using (MemoryStream stream = new MemoryStream(Convert.FromBase64String(other_photo_base64)))
                        {
                            try
                            {
                                await HuaWeiObs.Save(_httpClientFactory, stream, path);
                            }
                            finally
                            {
                                stream.Close();
                            }
                            string other_picture_indb = $"/{dt}/{otherPicName}{fileExtension}";
                            photos_in_db.Add(other_picture_indb);
                        }
                    }
                }
                request.sup_other_photo = string.Join(",", photos_in_db);
            }



            if (request.supcust_id != "" && request.supcust_id == request.acct_cust_id) return new JsonResult(new { result = "Error", msg = "不能既是门店又是结算单位" });
            if (request.acct_cust_id != "" && request.supcust_id != "")
            {
                string querySql = $@"select * from info_supcust where company_id = {companyID} and acct_cust_id = {request.supcust_id}";
                List<ExpandoObject> lstSons = await CDbDealer.GetRecordsFromSQLAsync(querySql, cmd);
                if (lstSons != null && lstSons.Count > 0)
                {
                    string sons = "";
                    foreach (dynamic son in lstSons)
                    {
                        if (sons != "") sons += ",";
                        sons += son.sup_name;
                    }

                    return new JsonResult(new { result = "Error", msg = $@"已是其他门店的结算单位，不能再设置结算单位。门店:{sons}" });
                }
            }

            if (!string.IsNullOrEmpty(supcust_id)) //现在只能实现已创建的费用单位在保存的时候联动欠款策略表更新，新增费用单位没有supcust_id，无法联动
            {
                string max_arrears = request.max_arrears == "" ? "null" : request.max_arrears;
                string max_arrears_days = request.max_arrears_days == "" ? "null" : request.max_arrears_days;//没填写记作null
                string exsql = "";
                if (status == "1")
                {
                    if (max_arrears == "null" && max_arrears_days == "null")
                    {
                        //欠款额度和天数都是空，需要从arrears_strategy_client删除这个费用单位数据
                        exsql = $"delete from arrears_strategy_client where company_id={companyID} and supcust_id = {supcust_id}";
                    }
                    else
                    {
                        //费用单位状态正常，并且填写了欠款额度或天数，就要更新arrears_strategy_client表，更新旧数据或者插入新的
                        exsql = $@"INSERT INTO arrears_strategy_client (company_id,supcust_id, max_arrears, max_arrears_days)
                                VALUES ({companyID},{supcust_id}, {max_arrears}, {max_arrears_days})
                                ON CONFLICT(company_id,supcust_id) DO UPDATE SET max_arrears = {max_arrears},max_arrears_days = {max_arrears_days};";
                    }
                }
                else if (status == "0")
                {//如果停用费用单位的话，从arrears_strategy_client删除这条数据
                    exsql = $"delete from arrears_strategy_client where company_id={companyID} and supcust_id = {supcust_id}";
                }
                if (!string.IsNullOrEmpty(exsql))
                {
                    cmd.CommandText = exsql;
                    await cmd.ExecuteNonQueryAsync();
                }
            }


            /*
            //新增费用单位档案是，费用单位编号如果没有编辑，则公司范围内整数自增
            if (string.IsNullOrEmpty(supcust_id) && string.IsNullOrEmpty(supcust_no) ) 
            {
                string sql = $"select setting from company_setting where company_id={companyID}";
                dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                dynamic supcustNoSerial = JsonConvert.DeserializeObject(rec.setting).supcustNoSerial;
                if (supcustNoSerial!=null  && supcustNoSerial == "True") 
                {
                    string querySql = @$"select max(supcust_no::NUMERIC) supcust_no from info_supcust where company_id={companyID} and supcust_flag like '%C%' and supcust_no ~ '^\d+$' ";
                    dynamic queryResult = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);
                    request.supcust_no = Convert.ToInt64(queryResult.supcust_no) + 1;
                }
            }*/
            cmd.CommandText = $@"SELECT rights FROM info_operator o 
    left join info_role r on o.company_id = r.company_id and o.role_id = r.role_id
where o.company_id = {companyID} and o.oper_id = {operID}";
            dynamic jsonRights = await cmd.ExecuteScalarAsync();
            request.approve_brief = "";
            request.receiver_id = "";
            request.flow_id = "";
            request.msg_id = "";
            string approveFlag = "";
            dynamic rights = Newtonsoft.Json.JsonConvert.DeserializeObject(jsonRights);
            if (rights == null)
                return new JsonResult(new { result = "ERROR", msg = "获取权限失败" });

            if (rights.info.infoFeeUnit.approve == "false")
            {
                if (request.approve_status == "wait approve")
                {
                    return new JsonResult(new { result = "ERROR", msg = "该档案上次编辑未审核，请先审核再编辑" });
                }
                model.DataItems["approve_status"].Value = "wait approve";
                request.approve_status = "wait approve";
                if (request.supcust_id == "")
                {
                    if (rights.info.infoFeeUnit.create == "false")
                    {
                        return new JsonResult(new { result = "ERROR", msg = "暂无新建权限" });
                    }
                    approveFlag = "CREATE";
                }
                else
                {
                    if (rights.info.infoFeeUnit.edit == "false")
                    {
                        return new JsonResult(new { result = "ERROR", msg = "暂无编辑权限" });
                    }
                    approveFlag = "EDIT";
                }

            }
            else
            {
                dynamic docInfo = null;
                if (request.approve_status == "wait approve")
                {
                    string docSql = $@"SELECT flow_id,oper_action FROM document_change_log 
                where company_id = {companyID} and obj_name ='费用单位档案' and obj_id = {request.supcust_id}";
                    docInfo = await CDbDealer.Get1RecordFromSQLAsync(docSql, cmd);
                    if (docInfo != null)
                    {
                        if (docInfo.oper_action == "CREATE") approveFlag = "APPROVED_FROM_CREATE";
                        else approveFlag = "APPROVED_FROM_EDIT";
                        request.flow_id = docInfo.flow_id;
                    }
                    // else
                    // {
                    //   return new JsonResult(new { result = "ERROR", msg = "费用单位档案历史记录查询失败" });
                    // }
                }

                if (docInfo == null)
                {
                    if (request.supcust_id == "")
                    {
                        if (rights.info.infoFeeUnit.create == "false")
                        {
                            return new JsonResult(new { result = "ERROR", msg = "暂无新建权限" });
                        }
                        approveFlag = "CREATE_AND_APPROVED";
                    }
                    else
                    {
                        if (rights.info.infoFeeUnit.edit == "false")
                        {
                            return new JsonResult(new { result = "ERROR", msg = "暂无编辑权限" });
                        }
                        approveFlag = "EDIT_AND_APPROVED";
                    }
                }

                model.DataItems["approve_status"].Value = "";
                request.approve_status = "";


            }
            request.approve_flag = approveFlag;


            return await model.SaveTable(cmd, request, null);
        }
    }
}
