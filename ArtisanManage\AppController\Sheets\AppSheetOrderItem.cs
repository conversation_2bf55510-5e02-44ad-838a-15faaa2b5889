﻿using ArtisanManage.Enums;
using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.AppController.Sheets
{


    [Route("AppApi/[controller]/[action]")]
    public class AppSheetOrderItem : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public AppSheetOrderItem(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }



        /// <summary>
        /// 加载单据--返回--支付方式,备注
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID, string sheetType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetOrderItem sheet = new SheetOrderItem(LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);

            string option = "prepaySub";
            string sqlSetting = $@"SELECT setting->>'orderItemSheet' orderitemoption FROM company_setting where company_id ={companyID};";
            dynamic orderItemSetting = await CDbDealer.Get1RecordFromSQLAsync(sqlSetting, cmd);
            if (orderItemSetting != null)
            {
                option = orderItemSetting.orderitemoption;
                if (option.IsInvalid())
                {
                    option = "prepaySub";
                }
            }
            SQLQueue QQ = new SQLQueue(cmd);
            var sql = $"select sub_id,sub_name,sub_type payway_type from cw_subject where company_id = {companyID} and sub_type = 'QT' order by order_index;";
            QQ.Enqueue("subInfo", sql);
            sql = $@"select brief_id,brief_text from info_sheet_detail_brief where company_id = {companyID} and sheet_type = '{sheetType}';";
            QQ.Enqueue("briefInfo", sql);
            sql = $@"select * from (select sub_id,sub_name,py_str from cw_subject where company_id = {companyID} and sub_type = 'YS' and is_order = true UNION ALL select -1 as sub_id ,'未指定' as sub_name , 'wzd' as py_str) m
           where (sub_id::text IN (
                    SELECT 
                        json_array_elements_text(avail_pay_ways) AS individual_value 
                    FROM 
                        info_operator 
                    WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
                    )
                OR
                (   SELECT 
                        COUNT(*) 
                    FROM 
                        info_operator 
                    WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
             )
;";
            if (option == "prepaySub") sql = $@"select sub_id,sub_name,py_str from cw_subject where company_id = {companyID} and sub_type = 'YS' and is_order = true 
             and (sub_id::text IN (
                    SELECT 
                        json_array_elements_text(avail_pay_ways) AS individual_value 
                    FROM 
                        info_operator 
                    WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
                    )
                OR
                (   SELECT 
                        COUNT(*) 
                    FROM 
                        info_operator 
                    WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
             );";
            else if (option == "noPrepaySub") sql = $@"select -1 as sub_id ,'未指定' as sub_name , 'wzd' as py_str;";
            //sql = $@"select sub_id,sub_name,py_str from cw_subject where company_id = {companyID} and sub_type = 'YS' and is_order = true;";
            QQ.Enqueue("order_account", sql);
             
            List<ExpandoObject> payways = null;
            List<ExpandoObject> brief = null;
            List<ExpandoObject> order_account = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "subInfo")
                {
                    payways = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "briefInfo")
                {
                    brief = CDbDealer.GetRecordsFromDr(dr, false);
                }else if (sqlName == "order_account")
                {
                    order_account = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet, payways, brief, order_account });
        }

        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic dSheet)
        {
            SheetOrderItem sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);

            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetOrderItem>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Submit:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            if (msg == "")
            {
                sheet.Init();
                msg = await sheet.Save(cmd);
            }
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.make_time, sheet.happen_time, currentTime });
        }
        /// <summary>
        /// 提交销售单
        /// </summary>
        /// <param name="sheet">
        /// {"operKey":"wcAqiAdqGYG39sTafoxzNuV7gjl0d-zEX5Q5vIEsZ4CJBL8L71cPvCkNmSBpbvSukmnIwUZFvIg~","sheettype":"X","sheet_no":"","sheet_id":"","supcust_id":"1","branch_id":"1","branch_name":"主仓库", 
        /// "happen_time":"","make_brief":"","total_amount":"72","now_disc_amount":"0","payway1_id":"1","payway1_name":"","payway1_amount":"72","payway2_id":"","payway2_name":"","payway2_amount":"0",
        /// "left_amount":"0", "maker_id":"","maker_name":"","make_time":"","approver_id":"", "approver_name":"","approve_time":"","now_pay_amount":72,"paid_amount":72,"shop_id":1, 
        /// "SheetRows":[ {"item_id":"6","item_name":"你好6","unit_no":"箱","real_price":"9","quantity":"1","unit_factor":"8","sub_amount":"72.00","remark":"7"}, 
        /// {"item_id":"6","item_name":"你好6","unit_no":"瓶","real_price":"9","quantity":"1","unit_factor":"8","sub_amount":"9","remark":"7"}]}</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            SheetOrderItem sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetOrderItem>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Submit:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";

                return new JsonResult(new { result = "Error", msg });
            }
            if (msg == "")
            {
                sheet.Init();
                sheet._httpClientFactory = this._httpClientFactory;
                msg = await sheet.SaveAndApprove(cmd);
            }
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no,sheet.approve_time,sheet.happen_time, currentTime });
        }

		/// <summary>
		/// 删除未审核单据
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		public async Task<JsonResult> Delete([FromBody] dynamic data)
		{
			string sheet_id = data.sheet_id;
			string operKey = data.operKey;
			Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
			SheetOrderItem sheet = new SheetOrderItem(LOAD_PURPOSE.SHOW);

			string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
			string result = msg == "" ? "OK" : "Error";
			if (msg != "") result = "Error";
			return Json(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
		}

		[HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            string result = "OK";string msg = null;
            try
            {
                var currentTime = DateTime.Now.ToText();
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                SheetOrderItem sheet = new SheetOrderItem(LOAD_PURPOSE.SHOW);
                sheet._httpClientFactory = this._httpClientFactory;
                msg = await sheet.Red(cmd, companyID, sheetID, operID,"");
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });

            }
            catch(Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }
        }



        /// <summary>
        /// 商品档案列表----返回商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="searchStr">商品名，助记码，商品编号，商品条码 模糊查询</param>
        /// <param name="brandID">品牌ID查询</param>
        /// <param name="classID">分类ID查询</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="branchID">仓库名 （1）</param>
        /// <returns>商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数</returns>

        
        [HttpGet]
        public async Task<JsonResult> GetItemList(string operKey, string searchStr, string brandID, string classID, int pageSize, int startRow, string supcustID)
        {
            bool firstRequest = false;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $" and (ip.status is null or ip.status='1') ";
            string condiStock = "";
            string crosstabSql = "";

            
            if (searchStr.IsValid())
            {
                string b = "%";
                if (searchStr.Length >= 6) b = "";
                string flexStr = CPubVars.GetFlexLikeStr(searchStr);
                condi += $" and (ip.item_name ilike '%{flexStr}%' or ip.py_str ilike '%{searchStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%' or s_barcode like '%{searchStr}{b}' or b_barcode like '%{searchStr}{b}' or m_barcode like '%{searchStr}{b}' or ip.mum_attributes::text ilike '%{searchStr}%')";
            }

            if (brandID != null) condi += $"and ip.item_brand = {brandID} ";
            if (classID != null && classID != "-1") condi += $"and ip.other_class like '%/{classID}/%' ";
            

            if (startRow == 0) firstRequest = true;
            if (supcustID == null) supcustID = "-1";
            crosstabSql = @$"select mu.item_id,unit_type,row_to_json(row(mu.unit_no,unit_factor,barcode,wholesale_price,retail_price,recent_price,buy_price,lowest_price)) as json 
                                 from info_item_multi_unit mu left join (select item_id,unit_no,recent_price FROM client_recent_prices where company_id = {companyID} and supcust_id = {supcustID}) rp 
                                 on rp.item_id = mu.item_id and mu.unit_no = rp.unit_no where mu.company_id = {companyID} order by item_id";
            SQLQueue QQ = new SQLQueue(cmd);
            /*
            var sql_noLimit = $@"select mu.item_id,ip.item_name,item_class as class_id,valid_days,produce_date,item_spec,mu.unit_no,unit_type,unit_factor,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no,mu.barcode,mu.wholesale_price,s_wholesale_price,m_wholesale_price,b_wholesale_price,
                              (case when m_unit_factor is null and b_unit_factor is not null then concat(s_unit_factor, b_unit_no,'=',b_unit_factor,s_unit_no)  
			                              when(b_unit_factor is not null) and(m_unit_factor is not null) then concat(s_unit_factor, b_unit_no,'=',floor(b_unit_factor::numeric / m_unit_factor::numeric),m_unit_no,'=',b_unit_factor,s_unit_no)
										  when b_unit_factor is null then concat(s_unit_factor, s_unit_no)  end ) as unit_conv
                              from info_item_multi_unit mu
                              left
                              join (select item_id,(b->> 'f1')::real as b_unit_factor,(m->> 'f1')::real as m_unit_factor,(s->> 'f1')::real as s_unit_factor,
                                                         b->> 'f2' as b_unit_no,m->> 'f2' as m_unit_no,s->> 'f2' as s_unit_no,
                                                         b->> 'f3' as b_wholesale_price,m->> 'f3' as m_wholesale_price,s->> 'f3' as s_wholesale_price
                                                        from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price)) as json from info_item_multi_unit where company_id={companyID} order by item_id',$$values('s'::text),('m'::text),('b'::text)$$) 
                                                as errr(item_id int, s jsonb,m jsonb, b jsonb)) mu1 on mu.item_id = mu1.item_id
                              left join info_item_prop ip on mu.item_id = ip.item_id 
                              left join (select item_id,produce_date from item_recent_produce_date where company_id={companyID}) rpd on mu.item_id=rpd.item_id
                              LEFT JOIN (select * from info_item_class where company_id = {companyID}) as ic on ip.item_class = ic.class_id 
                              where mu.company_id ={ companyID} {condi}  ";
            */
            var sql_noLimit = @$"
SELECT ip.item_name,item_class as class_id,valid_days,produce_date,item_spec, t.*,t.s_unit_no sunit,t.m_unit_no munit,t.b_unit_no bunit
FROM info_item_prop as ip
LEFT JOIN 
(select item_id,(s->>'f1') as s_unit_no,(m->>'f1') as m_unit_no,(b->>'f1') as b_unit_no,
            (s->>'f2') as s_unit_factor,(m->>'f2') as m_unit_factor,(b->>'f2') as b_unit_factor,
            (s->>'f3') as s_barcode,(m->>'f3') as m_barcode,(b->>'f3') as b_barcode,
            (s->>'f4') as b_wholesale_price,(m->>'f4') as m_wholesale_price,(b->>'f4') as b_wholesale_price,
            (s->>'f5') as s_retail_price,(m->>'f5') as m_retail_price,(b->>'f5') as b_retail_price,
            (s->>'f6') as s_recent_price,(m->>'f6') as m_recent_price,(b->>'f6') as b_recent_price,
            (s->>'f7') as s_buy_price,(m->>'f7') as m_buy_price,(b->>'f7') as b_buy_price,
            (s->>'f8') as b_lowest_price,(m->>'f8') as m_lowest_price,(b->>'f8') as b_lowest_price
from crosstab('{crosstabSql}', $$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)) t
on ip.item_id=t.item_id                        
left join (select item_id,produce_date from item_recent_produce_date where company_id={companyID}) rpd on t.item_id=rpd.item_id
LEFT JOIN (select * from info_item_class where company_id = {companyID}) as ic on ip.item_class = ic.class_id where ip.company_id = {companyID} {condi} ";

            var sql = sql_noLimit + $" order by item_name limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);
            if (firstRequest)
            {
                sql = $"select count(*) as itemCount from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
            }
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count" && firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, itemCount });
        }


        /// <summary>
        /// 获得指定商品大中小单位
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="itemID"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetItemUnit(String operKey, string itemID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql = $@"select item_id,s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor)) as unit from info_item_multi_unit where company_id = {companyID} and item_id = {itemID} ',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                 as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)";
            ExpandoObject data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new {result,msg,data});
        }


      
        /// <summary>
        /// 获取定货会余额
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="itemID">（1）</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetItemOrderedSumByItem(string operKey, string itemName, string pageSize, string startRow,string supcustId)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql = @$"select iip.item_name,cws.sub_name,iob.s_quantity,round(iob.total_order_balance::numeric,2) as total_order_balance,mu.s_unit_no,mu.m_unit_no,mu.b_unit_no,mu.m_unit_factor,mu.b_unit_factor from 
         (select company_id,supcust_id,item_id,prepay_sub_id, order_price/unit_factor as s_order_price ,sum(unit_factor*quantity) as s_quantity,sum(quantity*order_price) as total_order_balance from items_ordered_balance where company_id={companyID}   GROUP BY company_id,supcust_id,item_id,prepay_sub_id,s_order_price) iob
         LEFT JOIN info_item_prop iip on iip.item_id = iob.item_id and iob.company_id = iip.company_id
         LEFT JOIN cw_subject cws on cws.company_id =iob.company_id and cws.sub_id = iob.prepay_sub_id and is_order = true
	     LEFT JOIN(
            SELECT item_id,
            MAX(CASE WHEN unit_type = 's' THEN unit_no END) AS s_unit_no,MAX(CASE WHEN unit_type = 'm' THEN unit_no END) AS m_unit_no,MAX(CASE WHEN unit_type = 'b' THEN unit_no END) AS b_unit_no,MAX(CASE WHEN unit_type = 'b' THEN unit_factor END) AS unit_factor,
            MAX(CASE WHEN unit_type = 'm' THEN unit_factor END) AS m_unit_factor,MAX(CASE WHEN unit_type = 'b' THEN unit_factor END) AS b_unit_factor
        FROM info_item_multi_unit WHERE company_id = {companyID} GROUP BY item_id) mu ON mu.item_id = iob.item_id 
         WHERE iob.company_id = {companyID}";
            var countSql = @$"select count(*) from ({sql}) t";
            if (itemName.IsValid())
            {
                var itemNameCondi = $" and iip.item_name like '%{itemName}%' ";
                sql += itemNameCondi;
                countSql = @$"select count(*) from ({sql}) t";
            }
            if (supcustId.IsValid())
            {
                var itemNameCondi = $" and iob.supcust_id = {supcustId} ";
                sql += itemNameCondi;
                countSql = @$"select count(*) from ({sql}) t";
            }
            if (pageSize != null && startRow != null)
            {
               var pageCondi = $" limit {pageSize} offset {startRow}";
                sql += pageCondi;
            }
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
            QQ.Enqueue("count", countSql);
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    count = CPubVars.GetTextFromDr(dr, "count");
                }
            }
            string result = "OK";
            string msg = "";

            //放sql可读性太差 在这里写
            foreach (var item in data)
            {
                var dynamicItem = item as IDictionary<string, object>;
                if (dynamicItem.ContainsKey("s_quantity") && dynamicItem.ContainsKey("s_unit_no") && dynamicItem.ContainsKey("m_unit_no") && dynamicItem.ContainsKey("b_unit_no") && dynamicItem.ContainsKey("m_unit_factor") && dynamicItem.ContainsKey("b_unit_factor"))
                {
                    var sQuantity = Convert.ToDouble(dynamicItem["s_quantity"]);
                    var sUnitNo = dynamicItem["s_unit_no"].ToString();
                    var mUnitNo = dynamicItem["m_unit_no"].ToString();
                    var bUnitNo = dynamicItem["b_unit_no"].ToString();
                    double mUnitFactor;
                    mUnitFactor = dynamicItem["m_unit_factor"].ToString() != "" ? Convert.ToDouble(dynamicItem["m_unit_factor"]) : 0;
                    double bUnitFactor;
                    bUnitFactor = dynamicItem["b_unit_factor"].ToString() != "" ? Convert.ToDouble(dynamicItem["b_unit_factor"]) : 0;

                    string qty = "";
                    if (bUnitNo != "" && Math.Floor(sQuantity / bUnitFactor) > 0)
                    {
                        qty += Math.Floor(sQuantity / bUnitFactor) + bUnitNo;
                        sQuantity = sQuantity % bUnitFactor;
                    }
                    if (mUnitNo != "" && Math.Floor(sQuantity / mUnitFactor) > 0)
                    {
                        qty += Math.Floor(sQuantity / mUnitFactor) + mUnitNo;
                        sQuantity = sQuantity % mUnitFactor;
                    }
                    if (sQuantity > 0)
                    {
                        qty += sQuantity + sUnitNo;
                    }

                    dynamicItem["qty"] = qty;
                }
            }

            return Json(new { result, msg, data, count });
        }
        /// <summary>
        /// 订单历史记录 -- 返回{itemname,sheet_no,数量变化，单位，价格,日期，dateNum日期差,remark备注}
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="itemID">（1）</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetSaleOrderHistory(string operKey, string itemID, string pageSize, string startRow)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where m.company_id = {companyID} and item_id = {itemID} and approve_time is not null and red_flag is null ";
            var condi1 = "";
            if (pageSize != null && startRow != null)  condi1 = $" limit {pageSize} offset {startRow}";
            var sql = @$"select d.sheet_item_name,m.sheet_no,m.money_inout_flag*d.quantity quantity,d.unit_no,d.real_price,d.happen_time,(now()::DATE-d.happen_time::DATE) dateNum,d.remark from sheet_sale_order_main as m LEFT JOIN sheet_sale_order_detail as d on m.sheet_id = d.sheet_id
                       {condi} ORDER BY d.happen_time desc {condi1};";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
            sql = $"select count(m.sheet_no) as count from sheet_sale_order_main as m LEFT JOIN sheet_sale_order_detail as d on m.sheet_id = d.sheet_id {condi} ";
            QQ.Enqueue("count", sql);
            List<ExpandoObject> saleOrderHistory = null;
            var dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    saleOrderHistory = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    count = CPubVars.GetTextFromDr(dr, "count");
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, saleOrderHistory, count });
        }
    
    
        

    }
}
