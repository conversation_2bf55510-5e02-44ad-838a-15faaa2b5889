﻿单据类接口规范
一、加载单据
Load(string sheet_id)

二、提交单据
Submit([FromBody] sheet)

sheet的JSON格式：
{
   operKey:'SKKDDFFIFI' //company_id 加密后的字符串
   OperKey:'SKKDDFFIFI'  //oper_id 加密后的字符串，和company_id加密方式一样
   sheet_id:'',//新建单据为空
   sheet_no:'',//新建单据为空
   sheet_type:'',//单据类型  销售:X,退货T,收款单SK,预收款单YS,调拨单DB
   happen_time:'',

   .....
   其他字段，和单据主表的字段名一致
   .....
   SheetRows:[
     {
        字段和单据详表的字段名一致
     } 
   ] 
}
返回:

 {result, msg, sheet_id, sheet_no}
 
 三、加载其他选择列表的API



 收款单API
 
一、加载单据
Load(string sheet_id)

二、提交单据
Submit([FromBody] sheet)

sheet的JSON格式：
{
   operKey:'SKKDDFFIFI' //company_id 加密后的字符串
   OperKey:'SKKDDFFIFI'  //oper_id 加密后的字符串，和company_id加密方式一样
   sheet_id:'',//新建单据为空
   sheet_no:'',//新建单据为空
   sheet_type:'',//单据类型  销售:X,退货T,收款单SK,预收款单YS,调拨单DB
   happen_time:'',

   .....
      supcust_id 
        sheet_amount 
        paid_amount  
         disc_amount  
        now_pay_amount 
        now_disc_amount 
         left_amount 
   .....
   SheetRows:[
     {
       sale_sheet_id  
       sheet_amount  
        paid_amount  
        disc_amount 
       now_pay_amount  
         now_disc_amount 
        left_amount 
     } 
   ] 
}
返回:

 {result, msg, sheet_id, sheet_no}
 

 
 
 
 sheet的JSON格式：
{"operKey":"Aa18nTx5omI=",
  "operKey":"Aa18nTx5omI=",
  "sheet_id": "",
  "sheet_no": "",
  "sheettype": "X",
  "branch_id": 1,
  "happen_time": "2020-11-20 16:58",
  "supcust_id": 1,
  "total_amount": 500,
  "payway1_id":1,
  "payway1_amount":500,
  "now_pay_amount": 500
  "SheetRows": [
    {
      "item_id": 1,
      "branch_id":1,
      "sheet_item_name":"服务器",
      "unit_no": "箱",
      "quantity": 10,
      "orig_price": 60
    },
    {
      "item_id": 3,
      "branch_id":1,
      "sheet_item_name":"旺旺大礼包",
      "unit_no": "排",
      "quantity": 10,
      "orig_price": 10
    }
  ]
}