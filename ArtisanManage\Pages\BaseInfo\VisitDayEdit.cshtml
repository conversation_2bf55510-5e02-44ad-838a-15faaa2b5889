﻿@page
@model ArtisanManage.Pages.BaseInfo.VisitDayEditModel
@{
    Layout = null;
}
<!DOCTYPE html> 
<html>
<head id="Head1" runat="server">
    <meta name="viewport" content="width=device-width" />

    <title>VisitDayEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <style>
        .gridPosition {
            position:absolute;
            top:70px;
            bottom:50px;
        }
    #region{
    position: absolute;
    top: 70px;
    z-index: 666;
    float: right;
    display:flex;
    flex-direction:column;
    align-items:center;
    height:500px;
    overflow:auto;
    left: 52%;
    background:#f0f0f0;
        }
        .region-item{
            margin:10px ;
            width:80px;
            padding:4px;
            border-bottom:1px solid #ccc;
        }
    </style>

</head>
<body>     
    <div id="divHead" class="headtail" style="margin:20px 0 0 20px">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-left:50px;">保存</button>
    </div>
    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="gridDay" class="gridPosition" style="left:20px; width:calc(50%);"> </div>
    
    <div id="region">
         <div style="position:fixed;background:#f0f0f0;width:86px;">筛选片区</div>
         <div style='padding-top:20px;' id="region-content"></div>
    </div>

    <div id="map" class="gridPosition" style="right: 20px;width: calc(50% - 50px);border:1px solid #c7c7c7;">

    </div>

    <script src="//api.map.baidu.com/api?type=webgl&v=1.0&ak=@Html.Raw(Model.BaiduKey)"></script>
        <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var mapOption = {}
            var wholeSupcusts = []
            var g_region=""
          var map = new BMapGL.Map('map'); // 创建Map实例
            map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
        map.addEventListener('dragend', function ({ type, target, pixel, point,latlng }) {
                console.log(latlng)
                const curzoom = map.getZoom()
                remeberOptions({
                    operKey: window.g_operKey,
                    zoom: curzoom,
                    lng: latlng.lng,
                    lat: latlng.lat
                })
            getAllSupcusts(latlng.lng,latlng.lat)
            //drawLushu()
        });

        $.ajax('/api/VisitDayEdit/GetRegions?operKey=' + window.g_operKey, {
                success: function (data) {
                    if (data.result === 'OK') {
                        const renderDiv=renderRegion(data.regionData)
                        $("#region-content").append(renderDiv)
                        const options = document.getElementsByClassName("region-item")
                        Array.from(options).map(element => {
                            element.addEventListener("mouseover", (e) => {
                            e.currentTarget.style.setProperty("background", "#ccc")
                        })
                         element.addEventListener("mouseout", (e) => {
                             var regionId = $(e.currentTarget).attr("data-region-id")
                             console.log({regionId,g_region})
                             if (regionId === g_region) { 
                                 e.currentTarget.style.setProperty("background", "#ccc")
                             } else { 
                                 e.currentTarget.style.setProperty("background", "#f0f0f0")
                             }
                        })
            })
            }
        }
        })
        map.addEventListener('zoomend', function (e) {
                const point = map.getCenter()
                const curzoom = map.getZoom()
                remeberOptions({
                    operKey: window.g_operKey,
                    zoom: curzoom,
                    lng: point.lng,
                    lat: point.lat
                })
        })
        
        function renderRegion(regions) {
            const regionDivs= regions.map(region => {
                return `<div id='region-item'  style='cursor:pointer' data-name='${region.region_name}'  data-region-id='/${region.mother_id}/${region.region_id}' class='region-item' onclick="confirmRegion('/${region.mother_id}/${region.region_id}')">${region.region_name}</div>`
            })
            //g_region = `/${regions[0].mother_id}`
            return `<div  id='region-item' class='region-item' data-name='全部' data-region-id='/${regions[0].mother_id}' onclick="confirmRegion('/${regions[0].mother_id}')">全部</div>`+
            regionDivs.join("")
        }
        function confirmRegion(region){
            g_region=region
            getAllSupcusts()
            $(`.region-item`).css("background","#f0f0f0")
            $(`#region-item[data-region-id='${region}']`).css("background","#ccc")
        }
        //getAllSupcusts()
        function getAllSupcusts(centerLng,centerLat,callback) {
            var positionIsValid = centerLat && centerLng
            $.ajax('/api/VisitDayEdit/GetSupcust?operKey=' + window.g_operKey, {
                data: {
                    centerLng: centerLng,
                    centerLat: centerLat,
                    radius:positionIsValid ? 5000:"",
                    region:g_region
                },
                success: function (data) {
                    if (data.result === 'OK') {
                        wholeSupcusts = data.records
                        drawMarkers()
                        drawLushu()
                    }
                }
            })
        }
        function remeberOptions(data) {
                $.ajax("/api/VisitDayEdit/RememberOption", {
                    data: JSON.stringify(data),
                    contentType: 'application/json',
                    method: "POST",
                    success: (e) => {
                        console.log(e)
                    }
                })
        }
        @Html.Raw(Model.m_saveCloseScript)
            $.ajax("/api/VisitDayEdit/GetRememberOption?operKey=" + window.g_operKey, {
                method: "GET",
                success: (res) => {
                    var options = JSON.parse(res.data.options)
                    mapOption = options.visitDay
                    map.centerAndZoom(new BMapGL.Point(mapOption.lng, mapOption.lat), mapOption.zoom);
                    getAllSupcusts(mapOption.lng, mapOption.lat)
                }
            })
        function createeditor_sup_name(row, cellvalue, editor, cellText, width, height) {
                var element = $('<div id="txtItemName"></div>');
                editor.append(element);
                var inputElement = editor.find('div')[0];

                var dataFields = new Array(
                    { datafield: "sup_name", text: "客户名称", width: 300 },
                    { datafield: "sup_addr", text: "客户地址", width: 150 }

                )

                $(inputElement).jqxInput({
                    placeHolder: "助记码/名称", height: height, width: width,
                    borderShape: "none",
                    buttonUsage: 'event',
                    showHeader: true,
                    dropDownHeight: 160,
                    displayMember: "sup_name",
                    valueMember: "supcust_id",
                    dataFields: dataFields,
                    searchFields: ["py_str", "sup_name"],
                    maxRecords: 9,
                    source: function (query, response) {
                        ajaxGet('/api/VisitDayEdit/GetSupcust',{query:query}).then(data=>{
                            if (data.result === 'OK') {
                                response(data.records)
                            }
                        }).catch(error=>{console.log(error)})
                    },
                    renderer: function (itemValue, inputValue) {
                        // debugger;
                        var terms = inputValue.split(/,\s*/);
                        // remove the current input
                        terms.pop();
                        // add the selected item
                        terms.push(itemValue);
                        // add placeholder to get the comma-and-space at the end
                        // terms.push("");
                        //var value = terms.join(", ");
                        //return terms;
                        return itemValue;
                    }, onButtonClick: function () {
                        window.curRowIndex = row;
                        $('#popClient').jqxWindow('open');
                        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&multiSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

                    }
                })

                $(inputElement).on('optionSelected', //点击客户信息行后
                    function (a, b) {
                        //debugger;
                        var value = $(inputElement).val();
                        var id = '';
                        console.log('value:' + JSON.stringify(value));

                        var cell = $('#gridDay').jqxGrid('getselectedcell');

                        var row = $('#gridDay').jqxGrid('getrowdata', cell.rowindex)

                        row={
                            supcust_id:value.value,
                            sup_name:value.label
                        }
                        let msg = '';
                        let gridRows = $('#gridDay').jqxGrid('getrows');
                        for (let i = 0; i < gridRows.length; i++) {
                            let gridRow = $('#gridDay').jqxGrid('getrowdata', i);
                            if (cell.rowindex !== i && gridRow.supcust_id === row.supcust_id) {
                                msg = '在第' + (i + 1) + '行已存在此客户'
                                bw.toast(msg, 5000);
                                break;
                            }
                        }
                        if (msg) return
                        console.log(gridRows)
                        addSupRows(cell.rowindex, [row])

                        // cellendedit({ args: {datafield:'item_id',rowindex:cell.rowindex,row:rowData,value:value,oldvalue:''} })
                    })

                // debugger;

         }

        function addSupRows(rowIndex,rows) {
            var supcusts_id = ''
          /**  rows.forEach(function (row) {
                if (supcusts_id != '')
                    supcusts_id += ','
                supcusts_id += row.supcust_id
            })*/
            supcusts_id = rows.map(row => {
                return row.supcust_id
            }).join(",")

            ajaxGet('/api/VisitDayEdit/GetSupcust',{supcustsID:supcusts_id}).then(data=>{
                if(data.result==='OK'){
                    var gridRows = $('#gridDay').jqxGrid('getrows');
                    rows.map(row => {
                        var gridRow = gridRows[rowIndex]
                        const supcust = data.records.filter(record => record.supcust_id == row.supcust_id)[0]
                        if (supcust) {
                            Object.assign(gridRow, supcust)
                        }
                        rowIndex++
                        if (gridRows.filter(gridrow => gridrow.supcust_id != '').length == gridRows.length) {
                            expandGridRows()
                        }
                    })

                    var selected_supcusts = gridRows.filter(row=>(row.addr_lng!=''&&row.addr_lat!=''))
                    console.log({ selected_supcusts })
                    drawMarkers()
                    drawLushu()
                    $('#gridDay').jqxGrid('updategrid')

                }
            }).catch(error=>{
                console.log(error)
            })
            }
function drawMarkers() {
    map.clearOverlays()
    let gridRows = $('#gridDay').jqxGrid('getrows');
    var selected_supcusts = gridRows.filter(gridRow => gridRow.supcust_id !== '')
    var wholepois = wholeSupcusts.filter(supcust=>supcust.addr_lng&&supcust.addr_lat).map(supcust => {
        var point = new BMapGL.Point(supcust.addr_lng, supcust.addr_lat)
        point.supcust = supcust
        var opts = {
            position: new BMapGL.Point(supcust.addr_lng, supcust.addr_lat), // 指定文本标注所在的地理位置
            offset: new BMapGL.Size(30, -30) // 设置文本偏移量
        };
        if (selected_supcusts.some(s_supcust => { return s_supcust.supcust_id === supcust.supcust_id })) {
            var myIcon = new BMapGL.Icon("/images/visitdayImage/red_flag.png", new BMapGL.Size(24, 24));
            var supMarker = new BMapGL.Marker(point, { icon: myIcon });
        } else {
            var myIcon = new BMapGL.Icon("/images/visitdayImage/grey_flag.png", new BMapGL.Size(24, 24));
            var supMarker = new BMapGL.Marker(point, { icon: myIcon });
        }
        supMarker.supcust = supcust
        map.addOverlay(supMarker);
        withClickEvent(supMarker)
        var label = new BMapGL.Label(supcust.sup_name, opts);
        //文本标注样式，transform为X轴平移，即文本居中显示
        label.setStyle({
            color: "#fff",
            backgroundColor: supcust.day_info_count === '0' ? "rgba(0, 0, 0, 0.5)" : "rgba(230,162,60,0.5)",
            borderRadius: "10px",
            padding: "0 10px",
            fontSize: "14px",
            border: "0",
            transform: 'translateX(-50%)'
        });
        map.addOverlay(label);
        return point
    })
    /** 
    var selectedpois = selected_supcusts.map(supcust => {
        var point = new BMapGL.Point(supcust.addr_lng, supcust.addr_lat)
        point.supcust = supcust
        var opts = {
            position: new BMapGL.Point(supcust.addr_lng, supcust.addr_lat), // 指定文本标注所在的地理位置
            offset: new BMapGL.Size(30, -30) // 设置文本偏移量
        };
        var label = new BMapGL.Label((Number(supcust.order_index) + 1) + " " + supcust.sup_name, opts);

        //文本标注样式，transform为X轴平移，即文本居中显示
        label.setStyle({
            color: "#fff",
            backgroundColor: "rgba(103,194,58,0.5)",
            borderRadius: "10px",
            padding: "0 10px",
            fontSize: "14px",
            border: "0",
            transform: 'translateX(-50%)'
        });
        map.addOverlay(label);
        return point
    })

    var options = {
        size: 5,
        shape: BMAP_POINT_SHAPE_STAR,
        color: '#888'
    }
    var selected_options = {
        size: 5,
        shape: BMAP_POINT_SHAPE_STAR,
        color: '#C40000'
    }

   
   var wholepoisCollection = new BMapGL.PointCollection(wholepois, options);  // 初始化PointCollection
   map.addOverlay(wholepoisCollection);  // 添加Overlay
   withClickEvent(wholepoisCollection)
   var selectedpoisCollections = new BMapGL.PointCollection(selectedpois, selected_options);  // 初始化PointCollection
   map.addOverlay(selectedpoisCollections);  // 添加Overlay
   **/
  
}
            function expandGridRows(){
                var lastIndex = getLastRowIndex()
                for (var i = 0; i < 10; i++) {
                    onRowAdd('gridDay', lastIndex+i)
                }
            }
            function getLastRowIndex() {
                    var gridRows = $('#gridDay').jqxGrid('getrows');
                    const lastIndex = gridRows.filter(gridRow => gridRow.supcust_id !== '').length
                    return lastIndex
                }
            function withClickEvent(marker) {
                marker.addEventListener("click", e => {
                    const {supcust } = marker
                    console.log(supcust)
                        var gridRows = $('#gridDay').jqxGrid('getrows');
                        if (gridRows.some(gridRow => {
                            return gridRow.supcust_id === supcust.supcust_id
                        })) {
                            bw.toast("该客户已存在列表中")
                            return
                        }
                        const lastRowIndex = getLastRowIndex()
                        var gridRow = gridRows[lastRowIndex]
                        Object.assign(gridRow, supcust)
                        $('#gridDay').jqxGrid('updategrid')
                        drawMarkers()
                        drawLushu()
                    console.log(gridRows.filter(gridrow => gridrow.supcust_id != '').length)
                        if (gridRows.filter(gridrow => gridrow.supcust_id != '').length == gridRows.length) {
                        expandGridRows()
                        }
                        })
                }

            function drawLushu() {
                var gridRows = $('#gridDay').jqxGrid('getrows');
                var selected_supcusts = gridRows.filter(gridRow => gridRow.supcust_id !== '')
                if (selected_supcusts.length == 0) { 
                    return
                }
                const { addr_lng, addr_lat } = selected_supcusts[selected_supcusts.length-1]
                console.log( addr_lng, addr_lat)
//                map.centerAndZoom(new BMap.Point(addr_lng, addr_lat),13)
                for (i = 0; i < selected_supcusts.length - 1; i++) {
                    // 实例化一个驾车导航用来生成路线
                    var start = new BMapGL.Point(selected_supcusts[i].addr_lng, selected_supcusts[i].addr_lat);
                    var end = new BMapGL.Point(selected_supcusts[i + 1].addr_lng, selected_supcusts[i + 1].addr_lat);
                    console.log({ start, end })
                    draw(start, end,"#529b2e")
                }
                function generateColorCode() {
                    var colorcode = ""
                    var str = "ABCDEF123456789"
                    for (var i = 0; i < 6; i++) {
                        colorcode += str.charAt(parseInt((Math.random() * 100)) % 15);
                    }
                    return "#" + colorcode;
                }
                function draw(start, end, polycolor) {
                    var drv = new BMapGL.DrivingRoute('北京', {
                        onSearchComplete: function (res) {
                            if (drv.getStatus() == BMAP_STATUS_SUCCESS) {
                                var plan = res.getPlan(0);
                                var arrPois = [];
                                for (var j = 0; j < plan.getNumRoutes(); j++) {
                                    var route = plan.getRoute(j);
                                    arrPois = arrPois.concat(route.getPath());
                                }
                                /** 
                                var sy = new BMapGL.Symbol(BMap_Symbol_SHAPE_BACKWARD_OPEN_ARROW, {
                                    scale: 0.6,//图标缩放大小
                                    strokeColor: '#fff',//设置矢量图标的线填充颜色
                                    strokeWeight: 2,//设置线宽
                                });
                                var icons = new BMapGL.IconSequence(sy, '100%', '10%', false);//设置为true，可以对轨迹进行编辑
                                */
                                map.addOverlay(new BMapGL.Polyline(arrPois, { strokeColor: polycolor }));

                            }
                        }
                    });
                    drv.search(start, end)
                }

            }
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "selectMulti" || rs.data.action === "select") {
                    var cell = $('#gridDay').jqxGrid('getselectedcell');
                    var rowIndex = cell.rowindex;
                    var editable = $("#gridDay").jqxGrid('endcelledit', rowIndex, "supcust_id", false);
                    $('#popClient').jqxWindow('close');

                    var gridRows = $('#gridDay').jqxGrid('getrows');
                    var checkRows = []
                    var index = 0
                    rs.data.checkedRows.forEach(row => {
                        for (let i = 0; i < gridRows.length; i++) {
                            let gridRow = $('#gridDay').jqxGrid('getrowdata', i);
                            if (rowIndex !== i && gridRow.supcust_id === row.supcust_id) {
                                msg = '在第' + (i + 1) + '行已存在客户-' + row.sup_name
                                bw.toast(msg, 5000);
                                return;
                            }
                        }
                        checkRows[index] = row
                        index ++
                    })
                    if (checkRows.length === 0) return

                    addSupRows(rowIndex,checkRows)
                }

            }
        });
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            var operateVal=$("#operate input").attr("data-value")
            if (operateVal === 'copy') {
               $('#day_name input').val("")
               $('#day_id input').val("")
               $('#py_str input').val("")
               $('#schedule_id input').val("")
               $('#day_id input').val("")
            }
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

             $('#day_name input').on('input', function () {
                $('#py_str input').val(this.value.ToPinYinCode());
            });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });
        });

        function dealFormData(formData) {
            for (let i = 0, len = formData.gridDay.length; i<len; i++) {
                var row = formData.gridDay[i]
                row.order_index = i
                formData.gridDay[i] = row;
            }
        }

        function onFormSaved(data) {
            bw.toast('保存成功',3000)
            $('#day_id').val(data.record.day_id)
        }

        </script>
</body>
</html>