using System;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;

namespace ArtisanManage.Cashier;

[Route("api/cashier/[action]")]
public class ShortCutController : BaseController
{
    public ShortCutController(CMySbCommand cmd)
    {
        this.cmd = cmd;
    }
    [HttpPost]
    public async Task<JsonResult> LoadShortcutSettings([FromBody] dynamic data)
    {
        try
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyId);
            string sql = $@"select 
    company_id, 
    key_price, 
    key_quantity, 
    key_submit, 
    key_hold_sheet, 
    key_disc_amt, 
    key_setting, 
    key_sheet_type,
    key_wholesale, 
    key_client, 
    key_use_vip_card, 
    key_sell_vip_card 
from info_shortcut_key where company_id = '{companyId}'";
            dynamic response = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            return J<PERSON>(new { result = "OK", msg = "", data = response });
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return Json(new { result = "Error", msg = ""});
        }
       
    }
    [HttpPost]
    public async Task<JsonResult> SaveShortcutSettings([FromBody] dynamic data)
    {
        CMySbTransaction tran = cmd.Connection.BeginTransaction();
        string operKey = data.operKey;
        Security.GetInfoFromOperKey(operKey, out string companyId);
        data.companyId = companyId;
        string key_price = data.key_price;  
        string key_quantity = data.key_quantity;  
        string key_submit = data.key_submit;  
        string key_hold_sheet = data.key_hold_sheet;  
        string key_disc_amt = data.key_disc_amt;  
        string key_setting = data.key_setting;  
        string key_sheet_type = data.key_sheet_type;
        string key_wholesale = data.key_wholesale;  
        string key_client = data.key_client;  
        string key_use_vip_card = data.key_use_vip_card;  
        string key_sell_vip_card = data.key_sell_vip_card;
        
        
        string sql = $@"INSERT INTO info_shortcut_key (
    company_id,
    key_price,
    key_quantity,
    key_submit,
    key_hold_sheet,
    key_disc_amt,
    key_setting,
    key_sheet_type,
    key_wholesale,
    key_client,
    key_use_vip_card,
    key_sell_vip_card
)
VALUES (
    '{companyId}',  -- company_id
    '{key_price}',
    '{key_quantity}',
    '{key_submit}',
    '{key_hold_sheet}',
    '{key_disc_amt}',
    '{key_setting}',
    '{key_sheet_type}',
    '{key_wholesale}',
    '{key_client}',
    '{key_use_vip_card}',
    '{key_sell_vip_card}'
)
ON CONFLICT (company_id)  -- 指定冲突的列
DO UPDATE SET
    key_price = '{key_price}',
    key_quantity = '{key_quantity}',
    key_submit = '{key_submit}',
    key_hold_sheet = '{key_hold_sheet}',
    key_disc_amt = '{key_disc_amt}',
    key_setting = '{key_setting}',
    key_sheet_type = '{key_sheet_type}',
    key_wholesale = '{key_wholesale}',
    key_client = '{key_client}',
    key_use_vip_card = '{key_use_vip_card}',
    key_sell_vip_card = '{key_sell_vip_card}';";
        try
        {
            dynamic result = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            tran.Commit();
            return Json(new { result = "OK", msg = "", data = result });
            
        }
        catch (Exception e)
        {
            tran.Rollback();
            Console.WriteLine(e);
            return Json(new { result = "Error", msg = ""});
        }
       
    }

}