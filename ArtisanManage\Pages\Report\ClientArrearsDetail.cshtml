@page
@model ArtisanManage.Pages.BaseInfo.ClientArrearsDetailModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head> 
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
           
    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                    var sheet_id = args.row.bounddata.sheet_id;
                    var sheet_no = args.row.bounddata.sheet_no;
                    var sheet_type = args.row.bounddata.sheet_type;
                    // var sheetType = sheet_no.replace(/[^a-zA-Z]/g, '');
                    var sheetType = args.row.bounddata.sheettype;
                    var supcust_id = args.row.bounddata.supcust_id;
                    var sup_name = args.row.bounddata.sup_name;
                    var seller_id = $('#seller_id').val().value;
                    var seller_name = $('#seller_id').val().label;

                    if (args.datafield == "sheet_no") {
                        if (sheetType == "X" || sheetType == "T" || sheetType == "XD" || sheetType == "TD") {
                            window.parent.newTabPage(sheet_type, `Sheets/SaleSheet?sheet_id=${sheet_id}`);
                        }
                        debugger
                        if (sheetType == "YS") {
                            window.parent.newTabPage(sheet_type, `Sheets/PrepaySheet?sheet_id=${sheet_id}`);
                        }
                        if (sheetType == "DH" ) {
                            window.parent.newTabPage(sheet_type, `Sheets/OrderItemSheet?sheet_id=${sheet_id}`);
                        }
                        debugger
                        if (sheetType == "ZC" || sheetType == "SR") {
                            sheet_type=(sheetType == "ZC"?"费用支出单":"其他收入单");
                            window.parent.newTabPage(sheet_type, `Sheets/FeeOutSheet?sheet_id=${sheet_id}`);
                        }

                    }
                    if (args.datafield == "getarrear") {

                        debugger
                        var url = `Sheets/GetArrearsSheet?supcust_id=${supcust_id}&sup_name=${encodeURIComponent(sup_name)}&sheetno=${sheet_no}`;

                        window.parent.newTabPage('收款单', url);
                    }


                } );
                QueryData();
                $('#supcust_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popClient').jqxWindow('open');
                        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });



    	 });
         window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                    //$.ajax({
                    //    url: '/api/SaleSheet/GetItemInfo',
                    //    type: 'GET',
                    //    contentType: 'application/json',
                    //    data: { operKey: g_operKey, item_id: null },
                    //    success: function(data) {
                    //        if (data.result === 'OK') {
                    //            if (!window.g_queriedItems) window.g_queriedItems = {};
                    //            window.g_queriedItems[item_id] = data.item;
                    //        }
                    //    }
                    //});

                }
                $('#popClient').jqxWindow('close');
            }
            

        });   

    </script>
</head>

<body>
  
    <div style="display:flex;margin-top:20px;align-items:center;">
         
        <div id="divHead" class="headtail" style="width: calc(100% - 110px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()">查询</button>
        <button id="btnExport" onclick="ExportExcel()">导出</button>
    </div>
    
     <div id="gridItems"></div>  
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div> 
        
    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
   

</body>
</html>