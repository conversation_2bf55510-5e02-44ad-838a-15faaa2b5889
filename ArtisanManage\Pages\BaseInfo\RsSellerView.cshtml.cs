﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NPOI.POIFS.Crypt.Dsig;
using NPOI.SS.Formula.Functions;

namespace ArtisanManage.Pages.BaseInfo
{
    public class RsSellerViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;

        public RsSellerViewModel(CMySbCommand cmd) : base(MenuId.rsSeller)
        {
            this.cmd = cmd;

            DataItems = new Dictionary<string, DataItem>()
            {
                 {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称",UseJQWidgets=false, SqlFld="reseller_name",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     IdColumn="reseller_id",TableName="rs_seller",
                     ShowContextMenu=true,
                     ContextMenuHTML="<ul><li id='edit'>编辑</li></ul>",
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"reseller_id",new DataItem(){Title="编号", Width="80",Hidden=true,HideOnLoad = true}},
                       {"reseller_name",new DataItem(){Title="分销商名称", Width="180",Linkable=true}},
                       {"reseller_mobile",new DataItem(){Title="分销商电话",Width="280"}},
                       {"reseller_count",new DataItem(){Title="端口数",Width="180"}},
                       {"plan_id",new DataItem(){Title="分销方案ID",Width="80",SqlFld="rs.plan_id",Hidden=true,HideOnLoad = true}},
                       {"plan_name",new DataItem(){Title="分销方案",Width="180",SqlFld="plan_name"}},
                     },
                     QueryFromSQL="from rs_seller rs left join rs_plan rp on rs.plan_id = rp.plan_id where rs.company_id = ~COMPANY_ID" ,QueryOrderSQL="order by reseller_id"
                  }
                }
            };
        }
        public async Task OnGet(string forSelect)
        {
            await InitGet(cmd);
            ForSelect = forSelect == "1";
        } 
    }




    [Route("api/[controller]/[action]")]
    public class RsSellerViewController : QueryController
    {
        public RsSellerViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            RsSellerViewModel model = new RsSellerViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            RsSellerViewModel model = new RsSellerViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            RsSellerViewModel model = new RsSellerViewModel(cmd);
            /*            cmd.CommandText = @$" delete from rs_view where company_id={Token.CompanyID} and reseller_id = ({data.rowIDs});
                                              delete from rs_seller where company_id={Token.CompanyID} and reseller_id = ({data.rowIDs})
                                            ";
                        await cmd.ExecuteNonQueryAsync();*/
            object records = await model.DeleteRecords(data, cmd, "rs_seller");
            // return new JsonResult(new { result = "OK", msg = "" });
            return records;
        }
    }
}
