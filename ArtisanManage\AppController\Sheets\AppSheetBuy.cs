﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.Operations;
using myJXC;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.AppController.Sheets
{
    [Route("AppApi/[controller]/[action]")]
    public class AppSheetBuy : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public AppSheetBuy(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }
        /// <summary>
        /// 选择客户时候，返回相关支付等信息
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="supcust_id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetSupplierAccountInfo(string operKey, string supcust_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            string custID = supcust_id;
            string acctSql = @$"
SELECT i.acct_cust_id,s.sup_name acct_cust_name 
FROM info_supcust i 
LEFT JOIN info_supcust s on i.acct_cust_id = s.supcust_id and s.company_id={companyID}
WHERE i.company_id = {companyID} and i.supcust_id = {supcust_id} and (s.status is null or s.status = '1');";
            dynamic acctInfo = await CDbDealer.Get1RecordFromSQLAsync(acctSql, cmd);
            if (acctInfo != null && acctInfo.acct_cust_id != "")
                custID = acctInfo.acct_cust_id;
             

            SQLQueue QQ = new SQLQueue(cmd);
            string sql = @$"
SELECT b.sub_id,sub_type payway_type,sub_name,is_order,round(balance::numeric,2) balance
FROM prepay_balance b 
LEFT JOIN cw_subject p on p.sub_id = b.sub_id and p.company_id={companyID}
WHERE b.company_id = {companyID} and supcust_id = {custID};";
            QQ.Enqueue("prepay", sql);
 
            sql = $"select round(balance::numeric,2)*(-1) balance from arrears_balance where company_id = {companyID} and supcust_id = {custID} and balance<>0;";
            QQ.Enqueue("arrears", sql);
            sql = $"select mobile,sup_addr,boss_name,sup_rank,sup_group,other_region as sup_regions from info_supcust where company_id = {companyID} and supcust_id = {supcust_id}";
            QQ.Enqueue("info", sql);
       
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            var prepay = new List<ExpandoObject>(); 
            var arrears = new List<ExpandoObject>();
            var subInfo = new List<ExpandoObject>();
      
            dynamic info = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "prepay")
                {
                    prepay = CDbDealer.GetRecordsFromDr(dr, false);
                }
                 
                else if (sqlName == "arrears")
                {
                    arrears = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "info")
                {
                    info = CDbDealer.Get1RecordFromDr(dr, false);
                }                
            }
            QQ.Clear();
            return Json(new { result = "OK", prepay, arrears, info.mobile, info.sup_addr, info.boss_name, info.sup_rank, subInfo});
        }
        /// <summary>
        /// 加载单据--返回--支付方式,备注
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID">(1-4)</param>
        /// <returns></returns>


        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID, string sheetType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID);
            var isReturn = sheetType == "CT" ? SHEET_RETURN.IS_RETURN : SHEET_RETURN.NOT_RETURN;
            SheetBuy sheet = new SheetBuy(isReturn, LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            
            var sql = @$"
select sub_id,sub_name,sub_type as payway_type from cw_subject 
where company_id = {companyID} and sub_type in ('QT','YF','QTSR') and (sub_id::text IN (
            SELECT 
                json_array_elements_text(avail_pay_ways) AS individual_value 
            FROM 
                info_operator 
            WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
            )
        OR
        (   SELECT 
                COUNT(*) 
            FROM 
                info_operator 
            WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
    )
order by case sub_type when 'QT' then 0 when 'YF' then 1 else 2 end, order_index;
";
            SQLQueue QQ = new SQLQueue(cmd);

            QQ.Enqueue("subInfo", sql);
            sql = $@"select brief_id,brief_text from info_sheet_detail_brief where company_id = {companyID} and sheet_type='CG';";
            QQ.Enqueue("briefInfo", sql);
            sql = @$"SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index";
            QQ.Enqueue("attr_options", sql);
            List<ExpandoObject> payways = null;
            List<ExpandoObject> brief = null;
            List<ExpandoObject> attrOptions = null;
            //Test SheetBuy
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "subInfo")
                {
                    payways = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "briefInfo")
                {
                    brief = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "attr_options")
                {
                    attrOptions = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet, payways, brief,attrOptions });
        }
        [HttpPost]
        public async Task<JsonResult> Save([FromBody] SheetBuy sheet)
        {
            var currentTime = DateTime.Now.ToText();
            string msg=sheet.Init();

            if (sheet.appendixPhotos != null)
            {
                List<string> appendixBase64s = new List<string>();
                foreach (string appendixPhoto in sheet.appendixPhotos)
                {
                    appendixBase64s.Add(appendixPhoto);
                }
                sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());
            }

            if(msg=="") msg = await sheet.Save(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.make_time, sheet.happen_time, sheet.sheet_no, currentTime });
        }

        /// <summary>
        /// 提交采购单
        /// </summary>
        /// <param name="sheet">
        /// {"operKey":"wcAqiAdqGYG39sTafoxzNuV7gjl0d-zEX5Q5vIEsZ4CJBL8L71cPvCkNmSBpbvSukmnIwUZFvIg~","sheet_no":"","sheet_id":"","sheettype":"CG","supcust_id":"1","branch_id":"1",
        /// "happen_time":"","make_brief":"","total_amount":"72","now_disc_amount":"0","payway1_id":"1","make_time":"","approve_time":"","payway1_amount":"72","payway2_id":"","payway2_name":"","payway2_amount":"0",
        /// "disc_amount":"0","now_pay_amount":72,"paid_amount":72,"prepayAmount":"0",
        /// "SheetRows":[{"item_id":"6","item_name":"你好6","unit_no":"箱","real_price":"9","quantity":"8","unit_factor":"8","sub_amount":"72.00","orig_price":"0","real_price":"10"}]}</param>
        /// <returns></returns>
        /// 
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            SheetBuy sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);

            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetBuy>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Submit:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            if (msg == "")
            { 
                msg=sheet.Init();

                if (dSheet.appendixPhotos != null)
                {
                    List<string> appendixBase64s = new List<string>();
                    foreach (string appendixPhoto in dSheet.appendixPhotos)
                    {
                        appendixBase64s.Add(appendixPhoto);
                    }
                    sheet.appendix_photos = await ProcessAppendixPicsRetDBStr (appendixBase64s, sheet.company_id.ToString());
                }

                if (sheet.isRedAndChange)
                {
                    if (!sheet.old_sheet_id.IsValid())
                    {
                        msg = "冲改时没有获取原单据的编号";
                    }
                    else
                    {
                        msg = await sheet.RedAndChange<SheetBuy>(cmd);
                    }
                }
                else
                {
                    msg = await sheet.SaveAndApprove(cmd);
                }
            }
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.approve_time, sheet.happen_time, currentTime });
        }
        public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64, string companyID)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, companyID);
            return result;
        }

        /*[HttpPost]
        public async Task<JsonResult> Submit([FromBody] SheetBuy sheet)
        {
            var currentTime = DateTime.Now.ToText();
            sheet.Init();
            string msg = await sheet.SaveAndApprove(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.approve_time, sheet.sheet_no, currentTime });
        }*/


        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            string result = "OK"; string msg = null;
            try
            {
                var currentTime = DateTime.Now.ToText();
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                SheetBuy sheet = new SheetBuy(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
                msg = await sheet.Red(cmd, companyID, sheetID, operID,"");
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }
        }


        /// <summary>
        /// 商品档案列表----返回商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="searchStr">商品名，助记码，商品编号，商品条码 模糊查询</param>
        /// <param name="brandID">品牌ID查询</param>
        /// <param name="classID">分类ID查询</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="branchID">仓库名 （1）</param>
        /// <returns>商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数</returns>


        [HttpGet]
        public async Task<JsonResult> GetItemList(string operKey, string searchStr, string brandIDs, string classID, int pageSize, int startRow, string branchID, bool showStockOnly, string supcustID, string depart_id, string oftenMonths, string sortType, string sortFld1, string sortFld2, string sortFld3)
        {
            bool firstRequest = false;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $" where ip.company_id = {companyID} and (ip.status is null or ip.status = 1) and son_mum_item is NULL ";
            string condi1 = "";
            string crosstabSql = "";
            string orderSql = "";
            string orderSelect = "";
            string borrowSql = "";
            string borrowSelect = "";
            string orderCondi = "";
            string oftenSql = "";

            // if (searchStr != null) condi += $"and (ip.item_name ilike '%{searchStr}%' or ip.py_str ilike '%{searchStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%' or (t.s->>'f3') like '%{searchStr}%' or  (t.b->>'f3') like '%{searchStr}%' or (t.m->>'f3') like '%{searchStr}%') ";
            
            if (searchStr.IsValid())
            {
                string b = "%";
                if (searchStr.Length >= 6) b = "";
                string flexStr = CPubVars.GetFlexLikeStr(searchStr);
                condi += $" and (ip.item_name ilike '%{flexStr}%' or ip.py_str ilike '%{searchStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%' or s_barcode  like '%{searchStr}{b}' or b_barcode  like '%{searchStr}{b}' or m_barcode  like '%{searchStr}{b}' or ip.mum_attributes::text ilike '%{searchStr}%')";
            }
            /*
            string sortSQL = @$"order by item_order_index,ip.item_id desc";
            if (firstSort == "item_name")
            {
                sortSQL = $@"order by ip.py_str asc";
            }
            if (firstSort == "order_index")
            {
                sortSQL = $@"order by item_order_index asc";
            }*/
            Dictionary<string, string> sortValues = new Dictionary<string, string>
            {
                { "item_name", "ip.py_str asc" },
                { "order_index", "item_order_index asc" },
                { "recent_create", "item_id desc" },
                { "stock", "case when stock.stock_qty > 0 then 0 else 1 end" },
                { "no_stock", "case when stock.stock_qty <= 0 then 0 else 1 end" },
                { "more_stock", "stock.stock_qty desc" },
                { "less_stock", "stock.stock_qty asc" },
                /*{ "higherPrice", "" },
                { "lowerPrice", "" },*/
                { "none", "" }
            };
             

            string sortFlds = "";
            if (sortFld1.IsValid() && sortValues.TryGetValue(sortFld1, out string firstSortField) && !string.IsNullOrWhiteSpace(firstSortField))
            {
                if (sortFlds.IsValid()) sortFlds += ",";
                sortFlds += firstSortField;
            }
            if (sortFld2.IsValid() && sortValues.TryGetValue(sortFld2, out string secondSortField) && !string.IsNullOrWhiteSpace(secondSortField))
            {
                if (sortFlds.IsValid()) sortFlds += ",";
                sortFlds += secondSortField;
            }
            if (sortFld3.IsValid() && sortValues.TryGetValue(sortFld3, out string thirdSortField) && !string.IsNullOrWhiteSpace(thirdSortField))
            {
                if (sortFlds.IsValid()) sortFlds += ",";
                sortFlds += thirdSortField;
            }

            //string sortSQL = "";
            if (sortFlds == "") sortFlds = "item_order_index,ip.item_id desc";

            // sortSQL = @$" order by {sortFlds}";
            //0:全部 -1:常用
            //-1:常用时使用查询。支持查出其他类
            bool bSearchStrInClass = false;
            if (!string.IsNullOrEmpty(searchStr) && classID != "0") bSearchStrInClass = true;

            if (brandIDs != null && brandIDs != "") condi += $"and (ip.item_brand is null OR ip.item_brand in ({brandIDs})) ";
            if (classID != null && classID != "0" && classID != "-1") condi += $"and ip.other_class like '%/{classID}/%' ";
            if (branchID != null)
            {
                condi1 = $" and branch_id = {branchID}";
            }
            else
            {
                branchID = "-1";
            }
            if (startRow == 0) firstRequest = true;
            if (supcustID == null) supcustID = "-1";

            crosstabSql = @$"select mu.item_id,unit_type,row_to_json(row(mu.unit_no,unit_factor,barcode,wholesale_price,retail_price,recent_price,buy_price,recent_orig_price,lowest_price)) as json 
                                 from info_item_multi_unit mu 
                                left join (select item_id,unit_no,recent_price,recent_orig_price FROM supplier_recent_prices where company_id = {companyID} and supcust_id = {supcustID}) rp 
                                 on rp.item_id = mu.item_id and mu.unit_no = rp.unit_no where mu.company_id = {companyID} order by item_id";

            if (showStockOnly) condi += $" and stock.stock_qty>0 ";
            string showStockOnlyCondi = "";
            if (showStockOnly) showStockOnlyCondi += $" and stock_qty>0 ";

            if (classID == "-1" && supcustID != "-1")   //查询常用商品
            {
                //关联还货表
                borrowSql = @$"left join (select null order_flow_id,  null disp_flow_id,null disp_sheet_id,null fee_sub_name,item_id,true as is_borrowed,null disp_items_id,null disp_items_name,null disp_left_qty,null disp_unit_no,to_char(now(),'MM')::int disp_month,to_char(now(),'YYYY')::int disp_year,null disp_month_id,null order_sub_id,null order_sub_name,null order_price,null order_unit_factor,null order_balance,null order_qty,null order_item_sheets_id,null order_item_sheets_no,borrowed_qty::text from borrowed_cust_items where company_id ={companyID} and borrowed_qty<>0 and cust_id={supcustID}";
                orderSql = $@"union select b.flow_id order_flow_id, null disp_flow_id,null disp_sheet_id,null fee_sub_name,item_id,null as is_borrowed,null disp_items_id,null disp_items_name,null disp_left_qty,null disp_unit_no,to_char(now(),'MM')::int disp_month,to_char(now(),'YYYY')::int disp_year,null disp_month_id,prepay_sub_id order_sub_id,COALESCE(sub_name,'未指定') as order_sub_name,order_price,b.unit_factor as order_unit_factor,b.quantity * b.order_price as order_balance,quantity*unit_factor order_qty, order_item_sheets_id,order_item_sheets_no, null borrowed_qty from items_ordered_balance b
                            left join prepay_balance pb on b.supcust_id = pb.supcust_id and b.prepay_sub_id = pb.sub_id
                            left join cw_subject pw on pw.sub_id = b.prepay_sub_id
                            where b.company_id={companyID} and b.supcust_id = {supcustID} and b.quantity!=0 and case when prepay_sub_id <>'-1' then  pw.sub_name is not null else true end) ob on ob.item_id = ip.item_id";

                if (!oftenMonths.IsValid()) oftenMonths = "3";
                if (Convert.ToInt32(oftenMonths) > 48) oftenMonths = "48";

                oftenSql = $@"left join
(
    select item_id,true as is_often
    from
    (
        select item_id,count,row_number() over(order by count desc) rn
        from
        (
            select s.item_id,count1+count2 count from
            (
                select item_id,count(item_id) count1 from sheet_buy_detail sd
                left join sheet_buy_main sm on sd.sheet_id = sm.sheet_id  and sd.company_id = sm.company_id
                where sd.company_id ={companyID} and supcust_id = {supcustID}   and (sm.happen_time::date >= (now()-interval'{oftenMonths} months') and sm.happen_time::date <=now()) group by item_id
            ) s
            left join
            (
                select item_id,count(item_id) count2 from sheet_buy_order_detail od left join sheet_buy_order_main om on od.sheet_id = om.sheet_id  and od.company_id = om.company_id
                where od.company_id = {companyID} and supcust_id = {supcustID} and (om.happen_time::date >= (now()-interval'{oftenMonths} months') and om.happen_time::date<= now())group by item_id
            ) o on o.item_id = s.item_id
        )  t
    ) tt where rn<=200
) often on often.item_id = ip.item_id";
                orderSelect = @",order_sub_id,order_sub_name,order_item_sheets_id,order_item_sheets_no,borrowed_qty,order_price,order_balance,ob.order_qty,ob.order_flow_id,
                               (case when order_qty=0 then '' when order_qty!=0 then unit_from_s_to_bms (order_qty::float4,bFactor::numeric,mFactor::numeric,sFactor::numeric,bUnit,mUnit,sUnit)
                                     when disp_flow_id is not null then concat(disp_left_qty,disp_unit_no)
                                     when borrowed_qty='0' then '' when borrowed_qty!='0' then unit_from_s_to_bms (borrowed_qty::float4,bFactor::numeric,mFactor::numeric,sFactor::numeric,bUnit,mUnit,sUnit)
                                     end) as order_qty_unit,is_often,case when order_sub_id is not null or is_often or is_borrowed then true else false end often_order";
                borrowSelect = @",is_borrowed,borrowed_qty";
                if (searchStr.IsValid()) orderCondi = "";
                else orderCondi = " and (order_sub_id is not null or is_often or is_borrowed)";
                sortFlds = @$" often_order desc,order_sub_name,is_borrowed, " + sortFlds;
            }
                SQLQueue QQ = new SQLQueue(cmd);
             
            var sql_noLimit = @$"
SELECT ip.item_id,ip.item_name,ip.mum_attributes,item_class as class_id,other_class,valid_days,item_spec,ip.item_images,ip.batch_level,virtual_produce_date,
    yj_get_unit_qty('b', stock.stock_qty, bFactor::NUMERIC, mFactor::NUMERIC, false) bStock,
	yj_get_unit_qty('m', stock.stock_qty, bFactor::NUMERIC, mFactor::NUMERIC, false) mstock,
	yj_get_unit_qty('s', stock.stock_qty, bFactor::NUMERIC, mFactor::NUMERIC, false) sstock,
    yj_get_bms_qty(stock.stock_qty,bunit,bFactor::float4,mUnit,mFactor::float4,sunit) as stock_qty_unit,
    sUnit, sFactor, s_barcode, spPrice, slPrice, s_recent_price, s_buy_price,case when s_recent_price is not null then s_recent_price else s_buy_price end s_price,
    mUnit, mFactor, m_barcode, mpPrice, mlprice, m_recent_price, m_buy_price,case when m_recent_price is not null then m_recent_price else m_buy_price end m_price,
    bUnit, bFactor, b_barcode, bpPrice, blPrice, b_recent_price, b_buy_price,case when b_recent_price is not null then b_recent_price else b_buy_price end b_price {orderSelect}{borrowSelect} 
FROM info_item_prop as ip {oftenSql} {borrowSql} {orderSql} 
LEFT JOIN 
(
    select item_id,s,m,b,
    (s->>'f1') sUnit,(s->>'f2') as sFactor,(s->>'f3') as s_barcode,(s->>'f4') as spPrice,(s->>'f5') as slPrice,s->>'f6' as s_recent_price,s->>'f7' as s_buy_price,s->>'f8' as s_recent_orig_price,s->>'f9' as s_lowest_price,
    (m->>'f1') as mUnit,(m->>'f2') as mFactor,(m->>'f3') as m_barcode,(m->>'f4') as mpPrice,(m->>'f5') as mlprice,m->>'f6' as m_recent_price,m->>'f7' as m_buy_price,m->>'f8' as m_recent_orig_price,m->>'f9' as m_lowest_price,
    (b->>'f1') as bUnit,(b->>'f2') as bFactor,(b->>'f3') as b_barcode,(b->>'f4') as bpPrice,(b->>'f5') as blPrice,b->>'f6' as b_recent_price,b->>'f7' as b_buy_price,b->>'f8' as b_recent_orig_price,b->>'f9' as b_lowest_price
    FROM crosstab('{crosstabSql}', $$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)) t
    on ip.item_id=t.item_id
    LEFT JOIN 
    (
        select item_id,sum(COALESCE(stock_qty,0)) stock_qty from stock where company_id = {companyID} {condi1} {showStockOnlyCondi}  group by item_id,branch_id
    ) stock on stock.item_id = t.item_id
    left join 
    (
        select item_id,produce_date  as virtual_produce_date from item_recent_produce_date where company_id={companyID} 
    ) rpd on t.item_id=rpd.item_id
LEFT JOIN 
(
    select * from info_item_class where company_id = {companyID}
) as ic on ip.item_class = ic.class_id  {condi} {orderCondi}";

            var sql = sql_noLimit + $" order by {sortFlds} limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);
            if (firstRequest)
            {
                sql = $"select count(*) as itemCount from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
            }
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count" && firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
            }
            dr.Close();

            // 处理在某类下搜索商品
            if (bSearchStrInClass)
            {
                bool foundInClass = false;
                foreach (dynamic item in data)
                {
                    string other_class = item.other_class;
                    string often_order = "";
                    if (classID == "-1" && supcustID != null) often_order = item.often_order;
                    if (other_class.Contains("/" + classID + "/") || often_order.ToLower() == "true")
                    {
                        foundInClass = true;
                    }
                    else
                    {
                        item.beforeInfo2 = "其他类";
                        if (foundInClass)
                        {
                        }
                        else
                        {
                            item.beforeInfo1 = "该类下木有哦~";
                        }

                        break;
                    }
                }
            }
            #region  采购调价单影响
            string items_id = "";
            foreach (dynamic item in data)
            {
                if (items_id == "") items_id = item.item_id;
                else items_id += "," + item.item_id; 

            }
            if (!string.IsNullOrWhiteSpace(items_id))
            {

                string adjustSql = @$"select sd.sheet_id,sd.item_id,sd.company_id,sd.s_price,sd.m_price,sd.b_price FROM
(
select DISTINCT ba.company_id,ba.sheet_id,ba.item_id,ba.last_adjust_time,sp.happen_time from buy_item_price_adjust ba
LEFT JOIN supplier_recent_prices sp on ba.item_id=sp.item_id and ba.company_id=sp.company_id and sp.supcust_id={supcustID}
where ba.company_id={companyID} and ba.item_id in ({items_id}) and (ba.last_adjust_time>sp.happen_time or sp.happen_time is null)

) t 
left JOIN
sheet_buy_price_adjust_detail sd on t.company_id=sd.company_id and t.sheet_id=sd.sheet_id and t.item_id=sd.item_id
where sd.company_id={companyID}";
                dynamic allAdjustItem = await CDbDealer.GetRecordsFromSQLAsync(adjustSql, cmd);
                if (allAdjustItem != null)
                {
                    foreach (dynamic item in allAdjustItem)
                    {
                        foreach (dynamic dataItem in data)
                        {
                            if (item.item_id == dataItem.item_id)
                            {
                                if (!string.IsNullOrEmpty(item.b_price))
                                {
                                    dataItem.b_price = item.b_price;
                                    dataItem.b_recent_price = item.b_price;
                                }

                                if (!string.IsNullOrEmpty(item.m_price))
                                {
                                    dataItem.m_price = item.m_price;
                                    dataItem.m_recent_price = item.m_price;
                                }

                                if (!string.IsNullOrEmpty(item.s_price))
                                {
                                    dataItem.s_price = item.s_price;
                                    dataItem.s_recent_price = item.s_price;
                                }

                            }
                        }
                    }
                }
            }
            
            #endregion
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, itemCount });
        }

        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetBuy sheet = new SheetBuy(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return Json(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        [HttpPost]
        public async Task<IActionResult> AppendBrief([FromBody] dynamic data)
        {
            string sheetID = data.sheetID;
            string newBrief = data.newBrief;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetBuy sheet = new SheetBuy(SHEET_RETURN.EMPTY, LOAD_PURPOSE.APPROVE);

            string msg = await sheet.AppendBrief(cmd, companyID, sheetID, newBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return Json(new { result, msg });
        }


    }
}
