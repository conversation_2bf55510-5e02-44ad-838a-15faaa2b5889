@page
@model ArtisanManage.Pages.BaseInfo.ItemsViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">

<head id="Head1"> 
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdropdownlist.js"></script>

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var RowIndex = -1;
        var g_allCheckFlag = false
        importPicInterval = null
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead == "ItemEdit") {
               // var newID = ""; var newName = "";
               // if (rs.data.record) { newID = rs.data.record.item_no; newName = rs.data.record.item_name; }
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()

                    }
                    else {
                        var gridUnit = rs.data.formData.gridUnit[0]
                        var rows = window.gridData_gridItems.localRows;
                    
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                   
                    
                        rows[0] = rs.data.record;
                        rows[0].i = rows[0].item_id;
                        rows[0].n = rows[0].item_name;
                        rows[0].status = rs.data.record.cls_status_name;
                        rows[0].mall_status = rows[0].mall_status === '0' ? '已下架' : '已上架';
                        rows[0].b = rs.data.record.brand_name;
                        if (gridUnit) {
                            rows[0].barcode = gridUnit.barcode;
                            rows[0].unit_no = gridUnit.unit_no;
                        }
                        if (rows[0].item_images.indexOf('.jpg') <= -1 && rows[0].item_images != "") {
                            rows[0].item_images = "";
                        }
                        window.source_gridItems.totalrecords++;

                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    var row=rs.data.record
                    var gridUnit = rs.data.formData.gridUnit
                    var sBarcode="",mBarcode="",bBarcode=""
                    if (gridUnit.length > 0) {
                        gridUnit.forEach(gd => {
                            if(gd.unit_type=="s")sBarcode = gd.barcode
                            if(gd.unit_type=="m")mBarcode = gd.barcode
                            if(gd.unit_type=="b")bBarcode = gd.barcode
                        })
                    }
                    let mallStatus = row.mall_status === '0' ? '已下架' : '已上架'
                    updateSingleGridRow("i", row.item_id, { n: row.item_name, item_no: row.item_no, item_spec: row.item_spec,brand_name:row.brand_name,s_barcode:sBarcode,m_barcode:mBarcode,b_barcode:bBarcode, mall_status: mallStatus  })       
                }
                if(!rs.data.bCopy) $("#popItem").jqxWindow('close')
            }
            else if (rs.data.msgHead == "ClassEdit") {
                var newID = "";  var newName = "";
                if (rs.data.record) { newID = rs.data.record.class_id; newName = rs.data.record.class_name; }
                if (rs.data.action == "add") {
                    var sltItem = $('#other_class').jqxTree('findItem', rs.data.record.mother_id);
                    $('#other_class').jqxTree('addTo', { value: newID, label: newName }, sltItem.element, false);
                    $('#other_class').jqxTree('render');   // update the tree.
                    attachContextMenu();
                }
                else if (rs.data.action == "update") {
                    var sltItem = $('#other_class').jqxTree('findItem', rs.data.record.class_id);
                    $('#other_class').jqxTree('updateItem', sltItem, { label: newName });
                    $('#other_class').jqxTree('render');
                    attachContextMenu();
                }
                $("#popClass").jqxWindow('close');
            }
            console.log(rs.data);
        });


    	function showLog(str) {
    	    if (!log) log = $("#log");
    	    log.append("<li class='" + className + "'>" + str + "</li>");
    	    if (log.children("li").length > 8) {
    	        log.get(0).removeChild(log.children("li")[0]);
    	    }
    	}
    	function getTime() {
    	    var now = new Date(),
		h = now.getHours(),
		m = now.getMinutes(),
		s = now.getSeconds(),
		ms = now.getMilliseconds();
    	    return (h + ":" + m + ":" + s + " " + ms);
    	}

    	var newCount = 1;
        function setBatchLevel() {
            let batchType = window.g_companySetting.batchType
            let batchLevel = ""
            switch(batchType){
                case '':
                    batchLevel = ""
                    break;
                case '1':
                    batchLevel = "1"
                    break;
                case '2':
                    batchLevel = "2"
                    break;

            }
            return batchLevel
        }
    	function btnAddClass_click(e)
        {
            debugger
            let batchLevel = setBatchLevel()
            console.log(batchLevel)
    	    var selectedItem = $('#other_class').jqxTree('selectedItem');
    	    if (!selectedItem) {
    	        bw.toast("请先选择一个类");
    	    }
            $('#popClass').jqxWindow('open');
            debugger
            $("#popClass").jqxWindow('setContent', `<iframe src="ClassEdit?operKey=${g_operKey}&mother_id=${selectedItem.value}&mother_name=${selectedItem.label}&batch_level=${batchLevel}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

    	};

        function btnEditClass_click() {

    	    var selectedItem = $('#other_class').jqxTree('selectedItem');
    	    if (!selectedItem) {
    	        bw.toast("请先选择一个类");
    	        return;
    	    }
    	    $('#popClass').jqxWindow('open')
            $("#popClass").jqxWindow('setContent', `<iframe src="ClassEdit?operKey=${g_operKey}&class_id=${selectedItem.value}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

    	};
        function myPreview(url) {
            $('#preview-image').jqxWindow({
                width: "600px",
                height: "600px",
                resizable: true,
                draggable: true,
                showCloseButton: true,
                autoOpen: false,
                zIndex: 9999,
            })

            $("#preview-image").jqxWindow('setContent', `
                                                <img style='width:100%;' src='${url}'/>
                                            `);
            $('#preview-image').jqxWindow('open');


        }
    	function btnRemoveClass_click(e) {
    	    var selectedItem = $('#other_class').jqxTree('selectedItem');
    	    if (!selectedItem) {
    	        bw.toast("请先选择一个类");
    	        return;
            }
            jConfirm('确定要删除' + selectedItem.label+'吗？', function () {
		        $.ajax({
    	            type: "POST",
    	            url: "../api/ItemsView/RemoveClass",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify({ operKey: g_operKey, class_id: selectedItem.value }),
    	            success: function (data) {
                        if (data.result == "OK") {
                            var sltItem = $('#other_class').jqxTree('findItem', data.class_id);
                            $('#other_class').jqxTree('removeItem', sltItem, false);
                            $('#other_class').jqxTree('render');
                            attachContextMenu();
    	                }
    	                else {
                            bw.toast(data.result);
    	                }
    	            }
    	        });
                }, "");


    	}
        function btnAddItem_click(e) {
            var selectedItem = $('#other_class').jqxTree('selectedItem');
            console.log(selectedItem)
            if (!selectedItem) {
                bw.toast("请先选择一个类");
                return;
            }
            //查询类别的batch_level
            $.ajax({
                url: '/api/ItemsView/GetClassInfo',
                type: 'GET',
                contentType: 'application/json',
                data: { operKey: g_operKey, classId: selectedItem.value },
                success: function (data) {
                    if (data.result === 'OK') {
                        var path = $('#other_class').jqxTree('getTreePath', selectedItem);
                        $('#popItem').jqxWindow('open');
                        $("#popItem").jqxWindow('setContent', `<iframe src="ItemEdit?operKey=${g_operKey}&item_class=${selectedItem.value}&class_name=${selectedItem.label}&other_class=${path}&batch_level=${data.data}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    } else {
                        bw.toast("获取信息失败");
                    }
                },
                error: function (xhr) {
                    bw.toast('请求信息失败')
                }
            });


        }


        function attachContextMenu() {
            if (!window.contextMenu) window.contextMenu = $("#jqxMenu_1").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            function isRightClick(event) {
                var rightclick;
                if (!event) var event = window.event;
                if (event.which) rightclick = (event.which == 3);
                else if (event.button) rightclick = (event.button == 2);
                return rightclick;
            }
            // open the context menu when the user presses the mouse right button.

            $("#other_class li").on('mousedown', function (event) {
                var target = $(event.target).parents('li:first')[0];
                var rightClick = isRightClick(event);
                var contextMenu = event.target.innerText == "全部" ? contextMenu0 : contextMenu1

                if (rightClick && target != null) {
                    $("#other_class").jqxTree('selectItem', target);
                    var scrollTop = $(window).scrollTop();
                    var scrollLeft = $(window).scrollLeft();
                    var y = event.clientY
                    var treeTop = $('#other_class').offset().top
                    var treeHeight = $('#other_class').height()
                    var menuHeight = $("#jqxMenu_1").height()
                    //console.log(event.offsetY)
                    if (event.clientY > treeTop + treeHeight - menuHeight -0) {
                        y=event.clientY - menuHeight -10
                    }
                    contextMenu.jqxMenu('open', parseInt(event.clientX) + 5 + scrollLeft, y + 5 + scrollTop);

                    return false;
                }
            });
        }

        function onGridRowEdit(rowIndex) {
            var item_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'i');
            if (item_id == null) return;
            // 显示加载提示
            //$('#loadingOverlay').show();
            $('#popItem').jqxWindow('open');
            
            $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }
        var itemSource = {};

        function btnSelectItems_click() {
            var rows = window.g_checkedRows
           //  var rows = window.g_arrCheckedRows
            var checkedRows=[]
            for (var id in rows) {
                var row=rows[id] 
                checkedRows.push(getSelectRow(row))
            }

            if (checkedRows.length == 0) {
                bw.toast('请至少选择一行商品')
                return
            }
            var msg = {
                msgHead: 'ItemsView', action: 'selectMulti', checkedRows: checkedRows
            }
            window.parent.postMessage(msg, '*');
        }
        function getSelectRow(row) {
             var newRow={
                    item_id: row.i, item_name: row.n,order_sub_id: row.order_sub_id, order_sub_name: row.order_sub_name,order_item_sheets_id:row.order_item_sheets_id,order_item_sheets_no:row.order_item_sheets_no, order_price: row.order_price, order_unit_factor: row.order_unit_factor, order_qty: row.order_qty, order_qty_unit: row.order_qty_unit,order_flow_id:row.order_flow_id, borrowed_qty: row.borrowed_qty, 
                    b_qty:row.b_qty,m_qty:row.m_qty,s_qty:row.s_qty,b_unit_no:row.b_unit_no,m_unit_no:row.m_unit_no,s_unit_no:row.s_unit_no,b_unit_factor:row.b_unit_factor,m_unit_factor:row.m_unit_factor,s_unit_factor:row.s_unit_factor,
                    disp_flow_id: row.disp_flow_id, disp_sheet_id: row.disp_sheet_id,fee_sub_name:row.fee_sub_name, disp_left_qty: row.disp_left_qty, disp_unit_no: row.disp_unit_no, disp_month: row.disp_month, disp_month_id: row.disp_month_id, disp_items_id: row.disp_items_id, disp_items_name: row.disp_items_name
            }
            return newRow
        }
        function onItemNameClick(rowIndex) {
             
            if (ForSelect) {

                //  var rows = $('#gridItems').jqxGrid('getrows')
                var checkedRows = []
                var rows = $('#gridItems').jqxGrid('getrows')
                var row = rows.find(r => r.uid == rowIndex)
                checkedRows.push(getSelectRow(row))

                var msg = {
                    msgHead: 'ItemsView', action: 'selectMulti', checkedRows: checkedRows
                }

                /*var msg = {
                    msgHead: 'ItemsView', action: 'select', item_id: item_id, item_name: item_name, order_sub_id: order_sub_id, order_sub_name: order_sub_name, order_price: order_price, unit_factor: unit_factor, order_qty:order_qty, order_qty_unit:order_qty_unit
                };*/
                var keys=Object.keys(window.g_checkedRows)
                if (keys.length > 0) {
                    window.onGridRowCheck(rowIndex)
                    return
                }
              
                window.parent.postMessage(msg, '*');
            }
            else {
                onGridRowEdit(rowIndex);
                //$('#popItem').jqxWindow('open');
                // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
            }

        }

        function onmouseleaveMultiItems() {
            if (window.tmShowDispItem) {
                clearTimeout(window.tmShowDispItem)
                window.tmShowDispItem = 0
            }
            $('#popMultiItems').hide();
        }
        window.onmousewheel = function (e) {
            onmouseleaveMultiItems;
        }


        function onMouseEnterItemName(event, rowIndex) {

            if (ForSelect) {

                var rows = $('#gridItems').jqxGrid('getrows')
                var row = rows[rowIndex]
                if (row.disp_items_id && row.disp_items_id.indexOf(',') > 0) {
                    if (window.tmShowDispItem) {
                        clearTimeout(window.tmShowSysPrice)
                        window.tmShowSysPrice = 0
                    }
                    window.tmShowDispItem = setTimeout(() => {
                    var items_id = row.disp_items_id.split(',');
                    var items_name = row.disp_items_name.split(',');
                    var pop = `<div id="popMultiItems" onmouseleave="onmouseleaveMultiItems()"  style="z-index:999999999;display:none;position:absolute; background-color:rgb(232 232 232);border-color:#999;border-width:1px;border-style:solid;border-radius:6px;overflow:auto "> </div> `

                    var popDiv = $('#popMultiItems');
                    if (popDiv.length == 0) {
                        $('body').append(pop)
                        popDiv = $('#popMultiItems');
                        //popDiv.on('onmouseleave', ()=>{
                        //    popDiv.hide()
                        //})
                    }
                    popDiv.empty()
                    var html = '<ul id = "ulItems" style="list-style-type:none;font-size:14px;padding-left:10px;display:flex;flex-direction: column;">';
                    for (var i = 0; i < items_id.length; i++) {
                        html += `<li style="height:30px;"> <span style = "cursor:pointer;" onclick='onDispItemliClick(${items_id[i]},"${items_name[i]}",${rowIndex})'>${items_name[i]}</span></li>`;
                    }
                    html += '</ul>';
                    popDiv.append(html);

                    var cx = event.clientX
                    var cy = event.clientY
                    var ht = 200, wd = 250;
                    popDiv.css('left', cx + 15)
                    popDiv.css('top', cy-15)
                    popDiv.css('width', wd)
                    popDiv.show()
                    //setTimeout(function () {

                    //    //popDiv.animate({
                    //    //     width: wd, left: cx - wd/2,top:cy-ht/2
                    //    //},500)
                    //}, 200);
                    }, 300)
                }


            }


        }

        function onDispItemliClick(item_id, item_name, rowIndex) {
            if (ForSelect) {
                var rows = $('#gridItems').jqxGrid('getrows')
                var row = rows[rowIndex]
                var checkedRows = []
                checkedRows.push({
                    item_id: item_id, item_name: item_name,
                    order_sub_id: row.order_sub_id, order_sub_name: row.order_sub_name, order_price: row.order_price, order_unit_factor: row.order_unit_factor, order_qty: row.order_qty, order_qty_unit: row.order_qty_unit,
                    disp_flow_id: row.disp_flow_id, disp_sheet_id: row.disp_sheet_id, disp_left_qty: row.disp_left_qty, disp_unit_no: row.disp_unit_no, disp_month: row.disp_month, disp_month_id: row.disp_month_id, disp_items_id: row.disp_items_id, disp_items_name: row.disp_items_name
                })

                var msg = {
                    msgHead: 'ItemsView', action: 'selectMulti', checkedRows: checkedRows
                }

                /*var msg = {
                    msgHead: 'ItemsView', action: 'select', item_id: item_id, item_name: item_name, order_sub_id: order_sub_id, order_sub_name: order_sub_name, order_price: order_price, unit_factor: unit_factor, order_qty:order_qty, order_qty_unit:order_qty_unit
                };*/
                window.parent.postMessage(msg, '*');
            }
        }

        function itemNameRenderer (row, column, value, p4, p5, rowData) {
            var disp_flow_id = rowData.disp_flow_id;
            var order_sub_id = rowData.order_sub_id;
            let borrowed_qty = rowData.borrowed_qty;
            var lab = rowData.disp_month + '月陈列';
            if (rowData.fee_sub_name) {
                lab+=" ("+rowData.fee_sub_name+")"
            }
            var dispLab = '', orderLab = '', hideItems = '', borrowLab = '',orderSheetsNoLab='';

            var labelColor = rowData.status === '停用' ? '#8fbbf9' : '#49f'
            
            if (rowData.disp_items_id&&rowData.disp_items_id.indexOf(',') > 0) {
                hideItems = `<label onmouseenter='onMouseEnterItemName(event,${row})'  style="cursor:pointer;margin-left:4px;color:${labelColor};margin-right:2px">${value}</label><label onmouseenter='onMouseEnterItemName(event,${row})'  style="margin-right:5px;cursor:pointer;color:#49f;">(等)</label>`;
            } else hideItems = `<label onmouseenter='onmouseleaveMultiItems(event,${row})'  style="cursor:pointer;margin-left:4px;color:${labelColor};margin-right:2px">${value}</label>`
            if (disp_flow_id) dispLab = `<label style="cursor:pointer;margin-left:3px;margin-right:3px;background:#e6214a;color:white;border-radius:3px;width:auto;font-size:10px;text-align:center;line-height:16px" >${lab}</label>`
            if (order_sub_id) orderLab = `<label style="cursor:pointer;margin-right:3px;background:#e6214a;color:white;border-radius:3px;width:50px;font-size:10px;text-align:center;line-height:16px" >定</label>`
            if (borrowed_qty) borrowLab = `<label style="cursor:pointer;margin-right:3px;background:#e6214a;color:white;border-radius:3px;width:50px;font-size:10px;text-align:center;line-height:16px" >借</label>`
            if (rowData.order_item_sheets_no) {
                var s = rowData.order_item_sheets_no
                if (s.indexOf(',') == 0) s = s.substr(1, s.length - 1)
                if (s.indexOf(',') ==  s.length - 1) s = s.substr(0, s.length - 1)

                orderSheetsNoLab = `<label style="cursor:pointer;margin-right:3px;margin-left:4px;padding:3px;padding-left:5px;padding-right:5px;border-radius:6px; background:#eeeeee;color:#66e;border-radius:3px;font-size:12px;text-align:center;line-height:16px" >${s}</label>`

            }
            return `<div onclick='onItemNameClick(${row})' style="height:100%;display:flex;align-items:center;justify-content:flex-start;" >${dispLab}${orderLab}${borrowLab}${hideItems}${orderSheetsNoLab}</div>`
        }
        function renderStockQty(row,stock_qty,son_stock_qty) {
            if(!stock_qty)return
            var stockLabel = `<label style="cursor:pointer;margin-left:4px;color:#000;margin-right:2px">${stock_qty}</label>`
             var sonStockLabel=''
             if (son_stock_qty) {
                  sonStockLabel = `<label style="cursor:pointer;margin-left:4px;color:#777;font-size:6px;margin-right:2px">${son_stock_qty}</label>`
             }
            return `<div onclick='onItemNameClick(${row})' style="height:100%;display:flex;align-items:center;justify-content:flex-start;" >${stockLabel}${sonStockLabel}</div>`
       
        }
        function stockQtyRenderer (row, column, value, p4, p5, rowData) { 
            return renderStockQty(row,rowData.stock_qty_unit,rowData.son_stock_qty)
        }
         function usableStockQtyRenderer (row, column, value, p4, p5, rowData) { 
            return renderStockQty(row,rowData.usable_stock_qty,rowData.son_usable_stock_qty)
        }
        function sellPendStockQtyRenderer (row, column, value, p4, p5, rowData) { 
            return renderStockQty(row,rowData.sell_pend_stock_qty,rowData.son_sell_pend_stock_qty)
        }
        $(document).ready(function () {

            window.contextMenu0 = $("#jqxMenu_0").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            window.contextMenu1 = $("#jqxMenu_1").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
       
            

            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)

            var source = $('#other_class').jqxTree('source')
            var topNode = source[0]
            var eleItems = $('#other_class').jqxTree('getItems')
            //debugger;
            function setStopItemVisible(node, visible) {
                if (node.status == '0') {
                    var item = eleItems.find(item => item.value == node.v)
                    if (item) {
                        item.element.style.display =visible ? "" : "none"
                    }
                }
                else if (node.items) {
                    node.items.forEach(son => {
                         setStopItemVisible(son,visible)
                    })                    
                }
            }
            setStopItemVisible(topNode,false)

            

            $('#status').on('change', function (event) {
                var status = $('#status').jqxInput('val')
                status = status.value
                setStopItemVisible(topNode,status == 'all' || status == 'stop'?true:false)                
            })
           

            $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

            if (!window.ForSelect) {
                var items = $('#other_class').jqxTree('getItems')
              
                $('#btnSelectItems').hide()
                $('#gridItems').jqxGrid('hidecolumn', 'sys_check')
            }
             
            $("#gridItems").jqxGrid('beforeRowRender', function (divRow, rowData) {
                if (rowData.status === '停用')
                    divRow.style.color = '#acacac'
                else
                    divRow.style.color = 'inherit' // 不如此设置的话,滚动换页时会把未停用商品也显示成淡灰色
            })

            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                if (args.datafield == "n") {
                    if (args.originalEvent.button == 2) return;
                  
                }
                else if (args.datafield == "approve_status") {
                    var item_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                    var item_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "n");
                    console.log(item_name)
                    url = `Report/DocChange?&obj_name=${'商品档案'}&item_id=${item_id}&item_name=${item_name}`;
                    window.parent.newTabPage('商品档案变化表', `${url}`);
                } 
                else if (args.datafield == "mall_status") {
                     var item_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                     var mall_status = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "mall_status");
                     var change_mall_status = mall_status === '已下架' ? 1 : 0
                     const params = {
                       operKey :  '@Model.OperKey',
                       rows: [item_id],
                       mall_status: change_mall_status
                     }
                     $.ajax({ 
                          url:'../api/itemsView/UpdateItemMallStatus',
                         type: "POST",
                         contentType: "application/json",
                         data: JSON.stringify(params)
                     }).then(res => {
                         if (res.result === "OK") {
                           bw.toast('修改成功')
                           $('#gridItems').jqxGrid('setcellvalue', args.rowindex, "mall_status", change_mall_status === 1 ? '已上架' : '已下架');
                         } else {
                             bw.toast(res.msg)
                         }
                     })
                  
                 }

            });
    	    $("#Cancel").on('click', function () {
    	        for (var i = 0; i < 10; i++) {
    	            $('#jqxgrid').jqxGrid('deleterow', i);
    	            $('#jqxgrid').jqxGrid('addrow', i, {})
    	        }
    	    });
            let itemMaxHeight = $("#popItem").parent().height()
            let itemMaxWidth = $("#popItem").parent().width()
            let classMaxHeight = $("#popClass").parent().height()
            let classMaxWidth = $("#popClass").parent().width()
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 650, width: 900, maxHeight: itemMaxHeight - 50, maxWidth: itemMaxWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            $("#popClass").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 320, width: 550, maxHeight: classMaxHeight - 50, maxWidth: classMaxWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });


            attachContextMenu();

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
            });

            $(document).on('click', function (e) {

                var contextMenu = e.target.innerText
                var ee = $('#popSet').css("display")
                if (contextMenu == "全部" && ee == "block") {
                    bw.toast("请勿将商品类别设置为'全部'");
                }

            });
            QueryData();
         
          


                //$("#btnAddClass").bind("click", { isParent: false }, btnAddClass_click);
                //$("#btnEditClass").bind("click", btnEditClass_click);
            //$("#btnRemoveClass").bind("click", btnRemoveClass_click);
       
        });
        function onGridRowContextMenuClick(gridID, menuID, rowIndex) {

            var rows = $('#gridItems').jqxGrid('getrows')
            var row = rows[rowIndex]
            if (menuID == 'BatchOperation') {
                $('#gridItems').jqxGrid('showcolumn', 'sys_check')
                //全选check
                
                   /* $("#sys_checkColumn").on("click", (e) => {
                        if (!g_allCheckFlag) {
                            $.ajax({
                            url: "/api/ItemsView/GetQueryRecords?gridID=gridItems&startRow=0&endRow=999999&GetRowsCount=true&operKey=@Model.OperKey&showAttrSonItems=&mall_status=all&cls_mall_status_name=%E6%89%80%E6%9C%89&status=normal&cls_status_name=%E6%AD%A3%E5%B8%B8&sortColumn=&sortDirection=",
                            success: (res) => {
                                window.g_checkedRows = res.rows
                                $("#lblCheckedCount").html(res.rowsCount)
                            }
                            })
                            g_allCheckFlag = true
                        } 
                        else {
                        $("#lblCheckedCount").html("0")
                            g_allCheckFlag = false
                        }
                     
                    })*/
           
                var a = $('#popBatchOperation')
                $('#popBatchOperation').css("display","block")

            }

        }
        
         window.g_batchSetFld=''
         function popBatchSetDlg(fld) { 
            window.g_batchSetFld=fld
             if (fld == "item_class") {
                 $('#set_head').append('类别设置');
                 $('#popBatchOperation').css("display", "none")
                 $('#popSet').css("display", "block")
                 $('#div_set').append('<label  style="line-height: 32px;">类别</label> ');
                 $('#div_set').append('<div id = "set" > </div>');
                 window.g_pageSetting.dataItems.push({ datafield: 'item_class', text: '类别', origText: '类别', alwaysShow: false, hidden: false, visible: true });
                 $('#set').jqxDropDownTree({ dropDownWidth: 200, dropDownHeight: 250, url: '../api/ItemEdit/GetDataItemOptions?operKey= ' + g_operKey + '&dataItemName=item_class', source: null, checkboxes: false, mumSelectable: true });
                 $('#set').jqxInput('val', { value: ``, label: `` });
             }
             else if (fld == "item_brand") {
                 $('#set_head').append('品牌设置');
                 $('#popBatchOperation').css("display", "none")
                 $('#popSet').css("display", "block")
                 $('#div_set').append('<label  style="line-height: 32px;">品牌</label> ');
                 $('#div_set').append('<div id = "set" > </div>');
                 window.g_pageSetting.dataItems.push({ datafield: 'item_brand', text: '品牌', origText: '品牌', alwaysShow: false, hidden: false, visible: true });
                 queryBrands((data) => {
                     console.log(data)
                     $('#set').jqxDropDownList({ selectedIndex: 0, width: '200px', height: '25px', displayMember: "l", valueMember: "v", placeHolder: "请选择", filterable: true, filterPlaceHolder: '按名称查询', source: data });
                     //$('#set').jqxDropDownList('val', { value: ``, label: `` });
                 })
             }
             else if (fld == "status") {
                 $('#set_head').append('状态设置');
                 $('#popBatchOperation').css("display", "none")
                 $('#popSet').css("display", "block")
                 $('#div_set').append('<label   style="line-height: 32px;position: relative;"  >状态</label>');
                 $('#div_set').append('<div> <select id="set" style="width: 200px;"><option >正常</option> <option >停用</option></select></div>');
             }
             else if (fld == "delete") {
                 $('#set_head').append('删除设置');
                 $('#popBatchOperation').css("display", "none")
                 $('#popSet').css("display", "block")
                 $('#div_set').append('<label   style="line-height: 32px;"  >范围</label>');
                 $('#div_set').append('<div > <select id="set" style="width: 200px;"><option >勾选</option> </select></div>');
             }
             else if (fld == "import_picture") {
                 $('#set_head').append('批量导入图片');
                 $('#popBatchOperation').css("display", "none")
                 $('#popSet').css("display", "block")
                 $('#div_set').append('<label id="importPicMessage"  style="line-height: 32px;"  >确定导入？</label>');
                 $('#div_set').append('<div ></div>');
             } else if (fld == "batch_level") {
                 $('#set_head').append('批量设置产期/批次');
                 $('#popBatchOperation').css("display", "none")
                 $('#popSet').css("display", "block")
                 $('#div_set').append('<label   style="line-height: 32px;position: relative;">开启产期</label>');
                 $('#div_set').append('<div> <select id="set" style="width: 200px;"><option value="produceDate">开启生产日期</option> <option value="batch" >开启批次</option><option value="none">不启用产期批次</option> </select></div>');
             }
             else if (fld == "set_supplier") {
                 $('#set_head').append('设置供应商');
                 $('#popBatchOperation').css("display", "none")
                 $('#popSet').css("display", "block")
                 window.g_pageSetting.dataItems.push({ datafield: 'supplier', text: '供应商', origText: '供应商', alwaysShow: false, hidden: false, visible: true });
                 $('#div_set').append('<div id = "set" > </div>');
                 $('#set').jqxDropDownTree({ dropDownWidth: 200, dropDownHeight: 250, url: '../api/BuySheetView/GetDataItemOptions?dataItemName=supcust_id&&operKey=' + g_operKey, source: null, checkboxes: false, mumSelectable: true });
                 $('#set').jqxInput('val', { value: ``, label: `` });
             }
             else if (fld == "mall_units") {
                $('#set_head').append('上架单位设置');
                $('#popBatchOperation').css("display", "none")
                $('#popSet').css("display", "block")
                $('#div_set').append('<label  style="line-height: 32px;">上架单位</label> ');
                $('#div_set').append('<div id = "set" > </div>');
                $('#set').jqxDropDownList({dropDownWidth: 200, dropDownHeight: 100, source: ["大","中","小"], checkboxes: true});
                //$('#set').jqxInput('val', { value: ``, label: `` });
             }
        }
    
    
         function btnClose_Clicked() {
            $('#popSet').css("display", "none")
            $('#div_set').empty()
            $('#set_head').empty()            
        }
        function btnSave_Clicked() {
            var rows = []
            for (var id in window.g_checkedRows) {
                rows.push(parseInt(window.g_checkedRows[id].i))
            }
            if (rows.length == 0) {
                bw.toast("未做勾选");
            }
            var url = ''
            var params = {}
           
            if (g_batchSetFld == 'item_class') { 
                item_class = $('#set').val().value        
                other_class = $('#set').jqxDropDownTree('treePath')
                params = {item_class:item_class, other_class:other_class}
                url='../api/itemsView/BatchSetClass'
            }
            else if (g_batchSetFld == 'item_brand') {

               var type_item = $("#set").jqxDropDownList('getSelectedItem');

                console.log(type_item)
                item_brand = type_item.value
                params = { item_brand: item_brand }
                url = '../api/itemsView/BatchSetBrand'


            }
            else if (g_batchSetFld == 'status') {
                status = $('#set').val()
                params = {status:status}
                url='../api/itemsView/BatchSetStatus'
            }
            else if (g_batchSetFld == 'batch_level') {
                batchLevel = $('#set').val()
            params = { batchLevel: batchLevel }
                url='../api/itemsView/BatchSetBatchLevel'
            }
            else if (g_batchSetFld == 'delete') {
                type = $('#set').val()
                params = {type:type}
                url='../api/itemsView/BatchDelete'
            }
            else if (g_batchSetFld == 'import_picture') {
                type = $('#set').val()
                params = {type:type}
                url='../AppApi/InfoItem/BatchImportPicture'
                importPicInterval = setInterval(() => {
                    $.ajax({
                        url:"../AppApi/InfoItem/GetBatchImportPictureProgress",
                        data: JSON.stringify({
                            operKey:"@Model.OperKey"
                        }),
                        method:"POST",
                        success: (res) => {
                            $("#importPicMessage").html(`正在导入中，请勿关闭...<div>${res.processedCount}/${res.total}</div>`)
                        }
                    })
                },6000)
            }
            else if (g_batchSetFld == 'set_supplier') {
                supplierID = $('#set').val().value
                params = {supplierID:supplierID}
                url='../api/itemsView/BatchSetSupplier'
            }
            else if (g_batchSetFld == 'mall_units'){
                mall_units_name = $('#set').jqxDropDownList('val');
                console.log(mall_units_name);
                params = { mall_units_name: mall_units_name }
                url='../api/itemsView/SetMallUnits'
            }

            params.operKey = '@Model.OperKey'
            params.rows = rows
            $.ajaxSetup({ contentType: "application/json" });
            $.post(url, JSON.stringify(params)).then(
                function (data) {
                 if (importPicInterval) {
                    clearInterval(importPicInterval)
                 }
                 if (data.result == 'OK') {

                     bw.toast(data.msg);
                     $('#popSet').css("display", "none")
                     $('#div_set').empty()
                     $('#set_head').empty()
                     QueryData() 
                 } else {
                     //bw.toast(data.msg);

                     $("#popMessage").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 500, width: 450, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                     $('#divMessage').html(data.msg)
                     $('#popMessage').jqxWindow('open');


                 }
             })
        }


        function queryBrands(cb){
            $.ajax({
                url:'../api/ItemEdit/GetDataItemOptions?operKey= ' + g_operKey + '&dataItemName=item_brand',
                method:"GET",
                success: (data) => {
                    cb(JSON.parse(data))
                }
            })
        }
        function beforeQuery() {
             
            var item = $('#other_class').jqxTree('getSelectedItem')
            var often = $('#other_class').find('li:first-child')
            var isOften = false
            if (item == null && window.ForSelect && getSupcustID()) {
                isOften = true
                $('#other_class').jqxTree('selectItem', often[1]);

            }
            if (item && item.value == 'often') {
                isOften = true
            }
            $('#btnAddItem').attr('disabled',isOften)
            $('#gridItems').jqxGrid('_getcolumn', 'order_sub_name').hidden = !isOften
            $('#gridItems').jqxGrid('_getcolumn', 'order_price').hidden = !isOften
            $('#gridItems').jqxGrid('_getcolumn', 'order_balance').hidden = !isOften
            $('#gridItems').jqxGrid('_getcolumn', 'order_qty_unit').hidden = !isOften


        }

        function getSupcustID() {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == "supcust_id") return true;
            }
            return (false);
        }

        function onSearchStrInput() {
            var value = $('#other_class').jqxTree('val')
            if (value && value.value == 'often') {
                var div = $('#other_class').find('div:contains(全部)');
                if(div.length){
                    var li=div[0].parentNode
                    $('#other_class').jqxTree('selectItem',li)
                }
            }
            
        }

        function qtyInputRender(row, column, value, p4, p5, rowData) {
            if(!rowData) return
            var s = ''
             var k=funcGetRowKeyForCheck(rowData)
            var checkRow=window.g_checkedRows[k] 
            if (checkRow) {
                rowData.b_qty = checkRow.b_qty
                rowData.m_qty = checkRow.m_qty
                rowData.s_qty = checkRow.s_qty 
            }
            if (rowData.b_qty) {
                s+=rowData.b_qty+rowData.b_unit_no
            }
            if (rowData.m_qty) {
                s+=rowData.m_qty+rowData.m_unit_no
            }
            if (rowData.s_qty) {
                s+=rowData.s_qty+rowData.s_unit_no
            }
            var html = `<div style="height:100%;display:flex;align-items:center;justify-content:flex-end;"><label style="margin-right:3px;">${s}</label></div>`
           
            //var html = `<div style="height:100%;display:flex;align-items:center;justify-content:flex-end;"><label>${value}</label><label style="margin-right:3px;">${unit_no || ''}</label></div>`
            return html;
        }
        function qtyInputCreateEditor(row, cellvalue, editor, cellText, width, height) {
            var rowData = $('#gridItems').jqxGrid('getrowdata', row)
            if(!rowData) return
            var s=''
            //if (rowData.b_unit_no) {
                //<label>${rowData.b_unit_no}
                //placeholder="${rowData.b_unit_no}"
                s += `<input id='b_qty'  onkeydown="onQtyInputKeyDown(event)" style="width:25px;border:none;outline:none;" autocomplete="off" /> </label>`
          //  }
           // if (rowData.m_unit_no) {
                s += `<input id='m_qty'  onkeydown="onQtyInputKeyDown(event)" style="width:25px;border:none;outline:none;margin-left:2px;" autocomplete="off"/>`
           // }
            s += `<input id='s_qty' style="width:25px;border:none;outline:none;margin-left:2px;" autocomplete="off"/>`
            s=`<div style="text-align:right;font-size:9px;width:100%;display:flex;justify-content:center;align-items:center;">
                    ${s}
               </div>`
             var element = $(s);
             editor.append(element);
        }
        function onQtyInputKeyDown(event) {
            
            if(event.keyCode!=13) return
            var input = event.target
            var nextInput=null
            if (input.id == 'b_qty') {
                nextInput = $(input.parentNode).find('#m_qty:visible') 
            }
            if (nextInput.length==0)
            { 
                nextInput = $(input.parentNode).find('#s_qty')  
            }
            if (nextInput.length) {
                nextInput.focus()
                event.stopPropagation()
            }
        }
        function qtyInputInitEditor(row, cellvalue, editor, celltext, pressedkey) {
            var rowData = $('#gridItems').jqxGrid('getrowdata', row)
            if(!rowData) return
            var b_input = editor.find('#b_qty');
            var m_input = editor.find('#m_qty');
            var s_input = editor.find('#s_qty');
            var attrs=rowData.mum_attributes
            if(attrs) attrs=JSON.parse(attrs)
            var stockAttr=attrs?attrs.find(attr=>attr.distinctStock):null
        
            b_input.css('display',rowData.b_unit_no && !stockAttr ?'block':'none')
            m_input.css('display',rowData.m_unit_no && !stockAttr ?'block':'none')
            s_input.css('display',rowData.s_unit_no && !stockAttr ?'block':'none')
            b_input.attr('placeholder',rowData.b_unit_no)
            m_input.attr('placeholder',rowData.m_unit_no)
            s_input.attr('placeholder',rowData.s_unit_no) 

            b_input.val(rowData.b_qty||'');
            m_input.val(rowData.m_qty||'');
            s_input.val(rowData.s_qty || '');
            if (pressedkey) {
                if (b_input && rowData.b_unit_no) {
                   b_input.val(pressedkey);
                }
                else{
                   s_input.val(pressedkey);
                }
            }
            
             
            setTimeout(function () {
                if (b_input.length && rowData.b_unit_no) b_input.focus()
                else  s_input.focus()
             }, 100)
        }
        function qtyInputGetEditorValue(row, cellvalue, editor) {
            var rowData = $('#gridItems').jqxGrid('getrowdata', row)
            if(!rowData) return
             var b_input = editor.find('#b_qty');
             var m_input = editor.find('#m_qty');
            var s_input = editor.find('#s_qty');
            if (b_input) rowData.b_qty = b_input.val()
            if (m_input) rowData.m_qty = m_input.val()
            if (s_input) rowData.s_qty = s_input.val()
            if (rowData.b_qty || rowData.m_qty || rowData.s_qty) {
                var k=funcGetRowKeyForCheck(rowData)
                window.g_checkedRows[k]=rowData
            }
            return '';
        }
       
    </script>
    <style>
        /* 添加一些基本的加载动画样式 */
        .loader {
            border: 8px solid #f3f3f3; /* Light grey */
            border-top: 8px solid #FFF0F5; /* Blue */
            border-radius: 50%;
            width: 100px;
            height: 100px;
            animation: spin 2s linear infinite;
        }
        .margin {
            margin-left: 0px;
        }
        .label_name {
            line-height: 32px;
            margin-left: 10px;
        }

        .label_content {
            width: 120px;
            height: 30px;
            margin-left: 10px;
        }
        #searchString {
            font-size: 14px; 
            border-color: #d2d2d2;
            border-width: 0.5px;
            border-style: none;
            border-bottom-style: solid;
            width: 120px;
            height: 25px;
            outline: none;
            margin-top:2px;
        }
        input::-webkit-input-placeholder { /* WebKit browsers */
            color: #ddd;
        }  
        #popMultiItems span:hover {
            cursor: pointer;
            color:#4499ff;
        }

        .shadow {
            position: relative;
            max-width: 270px;
            box-shadow: 0px 1px 4px rgba(0,0,0,0.3), 0px 0px 20px rgba(0,0,0,0.1) inset;
        }
        #popBatchOperation {
            width: 150px;
            height: 220px;
            position: fixed;
            top: 25%;
            left: 40%;
            z-index: 999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            display: none;
            text-align: center;
            cursor: pointer;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
        }



        #popSet {
            width: 500px;
            height: 300px;
            position: fixed;
            top: 27%;
            left: 30%;
            z-index: 999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            padding: 5px;
            display: none;
            text-align: center;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
        }
        #div_item_class {
            display: none;
            margin-left: 123px;
            margin-top: 70px;
        }
        #item_class {
            width: 200px;
            margin-left: 20px
        }
        
        #close{
            height: 16px;
            width: 20px; 
            cursor: pointer;
            position: relative;
            left: 43px;
            top: 0px;
        }
        #close:hover{
             background: #ddd;
        }
        .magic{
            width: 150px;
            height: 25px;
        }

        .magic:hover {
            background: #ddd;
        }  
        #div_close{
        height: 16px;
        width: 20px; 
        cursor: pointer;
        position: relative;
        left: 43px;
        top: 0px;
        }
        #div_close:hover{
             background: #ddd;
        }
        #div_set {
            display: none;
            margin-left: 123px;
            margin-top: 70px;
            display: flex;
            text-align: center;
        }

        .jqx-grid-cell-middle-align {
            margin-top: 0px !important;
        }
        
    </style>

</head>
 
<body style="overflow:hidden;">



    <div id="popMessage" style="display:none">
        <div style="height: 30px; background-color: #fff; text-align: center; border-bottom: solid 2px #D5D5D5 ">
            <span style="font-size:20px;">操作结果</span>
        </div>
        <div id="divMessage" style=" margin: 2px;overflow-y:scroll;">

        </div>
    </div>


    <div id="popBatchOperation">
        <svg id="div_close" onclick=" this.parentNode.style.display = 'none'">
            <use xlink:href="/images/images.svg?v=@Html.Raw(Model.Version)#close" />
        </svg>
        <div class="magic" onclick="popBatchSetDlg('item_class')">设置类别</div>
        <div class="magic" onclick="popBatchSetDlg('item_brand')">设置品牌</div>
        <div class="magic" onclick="popBatchSetDlg('status')">设置状态</div>
        <div class="magic" onclick="popBatchSetDlg('delete')">批量删除</div>
        <div class="magic" onclick="popBatchSetDlg('import_picture')">导入图片</div>
        <div class="magic" onclick="popBatchSetDlg('batch_level')">设置产期/批次</div>
        <div class="magic" onclick="popBatchSetDlg('set_supplier')">批量设置供应商</div>
        <div class="magic" onclick="popBatchSetDlg('mall_units')">设置商城上架单位</div>
    </div>
    <div id="popSet" style="display: none;">
        <div style="height:30px;background-color:#fff; text-align:center;">
            <span id="set_head" style="font-size:18px;"></span>
        </div>
        <div id="div_set"></div>
        <div style="overflow:hidden;">
            <button onclick="btnSave_Clicked()" style="align-content:center;margin-top:45px;margin-left:20px;">确认</button>
            <button onclick="btnClose_Clicked();" style="align-content:center;margin-top:45px;margin-left:75px">关闭</button>
        </div>
    </div>
    <div style="display:flex;justify-content:space-around;margin-top:20px;">
        <div id="divHead" class="headtail" style="display:inherit;width:calc(100% - 280px);">
            <div id="div_item_brand" style="display: flex; width: 150px;"><label class="label_name">品牌</label> <div id="item_brand" style="width:90px;" class="label_content"></div></div>
            <div id="div_status" style="display: flex;width:130px;"><label class="label_name">状态</label> <div id="status" style="width:70px;" class="label_content"></div></div>
            <div id="div_searchString"><input id="searchString" style="width:160px;" oninput="onSearchStrInput()" autocomplete="off" class="margin" placeholder="简拼/名称/编号" /></div>
            @if (Model.BranchID != "")
            {
                <div style="margin-left: 10px;font-size:13px;margin-top:4px; margin-right:20px;"><input class="magic-checkbox" type="checkbox" id="showHasStock" /><label for="showHasStock">有库存</label></div>
            }

        </div>

        <div style="z-index: 1;">
            <button onclick="QueryData()" style="width:60px;" class="margin">查询</button>
            <button id="btnSelectItems" style="width:60px;" class="margin" onclick="btnSelectItems_click()">选择</button>
            <button id="btnAddItem" style="width:60px;" onclick="btnAddItem_click()" class="margin">添加</button>
            @if (!Model.ForSelect)
            {
                <button id="btnExport" onclick="ExportExcel()" class="margin">导出</button>
            }
        </div>


    </div>
    <div style="display: flex;  flex: 1;">
        <!--如果不加height:100%在浏览器正常，客户端实际高度为0-->
        <div id='other_class' style="width: 200px; height: calc(100% - 20px); margin-top: 20px; margin-bottom: 2px; overflow-y: scroll">
        </div>

        <div style="width:calc(100% - 200px);height:auto; margin-left:10px;">

            <div>
                <div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div>
            </div>
            <div id="gridItems" style="margin-top:0px;margin-bottom:2px;width:calc(100% - 10px);height:calc(100% - 20px);"></div>

        </div>


    </div>
    <div style="display:flex;height:20px;width:100%;margin-bottom:0px;"></div>


    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">商品档案</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popClass" style="display: none;">
        <div style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">商品类别</span></div>
        <div style="overflow:hidden;"> </div>
    </div>


    <div id='jqxMenu_1' style="display:none;">
        <ul>
            <li id="mnuEditClass" onclick="btnEditClass_click()">编辑类</li>
            <li id="mnuAddClass" onclick="btnAddClass_click()">添加下级类</li>
            <li id="mnuRemoveClass" onclick="btnRemoveClass_click()">删除类</li>
        </ul>
    </div>

    <div id='jqxMenu_0' style="display:none;">
        <ul>
            <li id="mnuAddClass" onclick="btnAddClass_click()">添加下级类</li>
        </ul>
    </div>
    <div id="preview-image" style="display:none">
        <div id="itemCaption" style="padding:10px 0;background-color:#fff;display:flex;justify-content:center"><span style="font-size:20px;">照片</span></div>
        <div style="padding:10px;overflow:hidden;display:flex;justify-content:center"> </div>
    </div>
    </div>
    <!-- 加载提示的 HTML -->
    @* <div id="loadingOverlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(255,255,255,0.8); text-align:center; padding-top:20%;">
        <div style="display:inline-block; padding:20px;margin-left:50px; border:1px solid #ccc; border-radius:5px; background:#fff;">
            <p>正在加载中，请稍等...</p>
            <!-- 你可以添加一个旋转的加载图标 -->
            <div class="loader"></div>
        </div>
    </div> *@
</body>
</html>