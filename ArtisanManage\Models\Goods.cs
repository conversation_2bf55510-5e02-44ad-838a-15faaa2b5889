﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
//using Microsoft.EntityFrameworkCore;
//using MySql.Data;
//using MySql.Data.MySqlClient;
//using Pomelo.Data.MySql;
//using Pomelo.EntityFrameworkCore;
//using Pomelo.EntityFrameworkCore.MySql;
using Npgsql;
using System.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace ArtisanManage.Models
{
    public class WorldContext
    {
        public string ConnectionString { get; set; }

        public WorldContext(string connectionString)
        {
            this.ConnectionString = connectionString;
        }
       
        public List<Country> GetAllCountry()
        {
            List<Country> list = new List<Country>();
            //连接数据库
            /*
            using (NpgsqlConnection msconnection = GetConnection())
            {
                msconnection.Open();
                msconnection.ChangeDatabase("yuchen");
                //查找数据库里面的表
                
               
                NpgsqlCommand mscommand = new NpgsqlCommand("select * from info_item_prop", msconnection);
                using (NpgsqlDataReader reader = mscommand.ExecuteReader())
                {
                    
                    //读取数据
                    while (reader.Read())
                    {
                        list.Add(new Country()
                        {
                            // Code = reader.GetString("Code"),
                            Name = reader["item_name"].ToString()
                          //  Continent = reader.GetString("Continent"),
                           // Region = reader.GetString("Region")
                        });
                    }
                }
            }
            return list;
            */
            return null;
        } 
    }
    public class Country
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Continent { get; set; }
        public string Region { get; set; }
    }
    public class TestModel
    {
        public Guid Id { get; set; } 
        public string Text { get; set; }
    }
    public class TestContext : DbContext
    {
        public DbSet<TestModel> TestModels { get; set; } 
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<TestModel>(e =>
            {
               // e.HasIndex(x => x.Text).ForMySqlIsFullText(); // 添加全文索引
            });
        }
        public TestContext(DbContextOptions<TestContext> options) : base(options)
        {
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);

           // optionsBuilder.UseMySql("server=localhost;port=3306;database=t_saas_common1;user id=root;password=*********");
        }
    }
}
