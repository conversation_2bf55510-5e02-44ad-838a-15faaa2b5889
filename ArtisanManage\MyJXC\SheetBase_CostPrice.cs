﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
 
using System.Threading.Tasks;
 
using System.Dynamic;
using Org.BouncyCastle.Pqc.Crypto.Lms;

namespace ArtisanManage.MyJXC
{ 
    public partial class SheetBase<TROW> where TROW : SheetRowBase, new()
    { 
         
        private struct UpdateCostPriceKey
        { 
            public string sheet_type;
            public string sheet_id;
            public string item_id;
            public string cost_price_suspect;
            public string cost_price_avg;
            public int inout_flag;
            public override int GetHashCode()
            {
                // 您可以使用字段的哈希码进行组合，生成唯一的哈希码
                return sheet_type.GetHashCode() ^ sheet_id.GetHashCode() ^ item_id.GetHashCode() ^ cost_price_suspect.GetHashCode() ^ cost_price_avg.GetHashCode() ^ inout_flag.GetHashCode();
            }
            public override bool Equals(object obj)
            {
                if (obj is UpdateCostPriceKey)
                {
                    var other = (UpdateCostPriceKey)obj;
                    return this.sheet_type == other.sheet_type
                        && this.sheet_id == other.sheet_id
                        && this.item_id == other.item_id 
                        && this.cost_price_suspect == other.cost_price_suspect
                        && this.cost_price_avg == other.cost_price_avg
                        && this.inout_flag == other.inout_flag;
                }
                return false;
            }
        }
       
        class OrigSheetItem
        {
            public string sheet_id = "", item_id = "", sheet_type = "";
            public int inout_flag = 0;
            public decimal quantity = 0, unit_factor = 0, s_real_price = 0, sub_amount=0;
            public string cost_price_avg="",cost_price_avg_info = "";
            public string happen_time = "";
        }
        class SheetItemMergeKey
        {
            public string sheet_id = "", item_id = "", sheet_type = "";
            public int inout_flag = 0;
            public decimal quantity = 0;
            public string cost_price_avg="",cost_price_avg_info = "";
            public string happen_time = "";
 
        }
        class MergedSheetItem
        {
            public string sheet_id = "", item_id = "", sheet_type = "";
            public int inout_flag = 0;
            public decimal sheet_qty = 0, sheet_amount = 0, s_real_price = 0;
            public string cost_price_avg = "", cost_price_avg_info = "";
            public string happen_time = "";
            
            //public decimal CostPriceAvg = 0;
            public string RedFlag = "";
        }

        /*class DealSheetItem
        {
            public string sheet_id = "", item_id = "", sheet_type = "";//, red_flag="";
            public int inout_flag = 0;
            public decimal sheet_qty = 0, sheet_amount = 0, s_real_price = 0;
            public decimal cost_price_avg = 0;
            public string happen_time = "";
        }*/
        protected List<SheetRowCostPrice> MergeSheetRowsForAvg(List<SheetRowCostPrice> lstSheetRows)
        {
            Dictionary<string, SheetRowCostPrice> rowsDict = new Dictionary<string, SheetRowCostPrice>();
            foreach (SheetRowCostPrice sheetRow in lstSheetRows)
            {
                string skey = sheetRow.item_id;// +"_" + sheetRow.unit_factor.ToString();
                SheetRowCostPrice curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = new SheetRowCostPrice();
                    curRow.item_id = sheetRow.item_id;
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor * sheetRow.inout_flag;
                    //  curRow.old_cost_price_avg = sheetRow.old_cost_price_avg;
                    curRow.cost_price_avg = sheetRow.cost_price_avg;
                    curRow.item_cost_price_suspect = sheetRow.item_cost_price_suspect;
                    curRow.old_total_qty = sheetRow.old_total_qty;
                    curRow.inout_flag = sheetRow.inout_flag;
                    curRow.sub_amount = sheetRow.sub_amount * sheetRow.inout_flag;

                    curRow.real_price = sheetRow.real_price;
                    curRow.unit_factor = sheetRow.unit_factor;
                    rowsDict.Add(skey, curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor * sheetRow.inout_flag;
                    curRow.sub_amount += sheetRow.sub_amount * sheetRow.inout_flag;

                }
            }
            List<SheetRowCostPrice> newList = new List<SheetRowCostPrice>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }
        private async Task<List<MergedSheetItem>> GetHistorySheetItemsAfterMe_new(CMySbCommand cmd, string items_id, string dealing_happen_time)
        {
            string sql = "";
          
            SQLQueue QQ = new SQLQueue(cmd);
            var condi = $" and sd.sheet_id<>{this.sheet_id} ";

            // if (red_flag == "2") condi = $" and sd.sheet_id<>{this.red_sheet_id} ";
            //找出插入单据那一刻


			sql = @$" 
select        sheet_id,sheet_type,red_flag,money_inout_flag, t.happen_time, t.item_id,quantity,t.unit_factor,inout_flag,t.cost_price_avg,    cost_price_suspect,                        cost_price_buy,  sub_amount,    s_real_price,      ip.cost_price_avg as cost_price_avg_info from 
(
    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg ,   cost_price_suspect,                        cost_price_buy, sub_amount,real_price/unit_factor s_real_price , m.red_flag
    from sheet_sale_detail sd 
    left join sheet_sale_main m on m.sheet_id = sd.sheet_id and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
     
    UNION

    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg , false  cost_price_suspect,                        cost_price_buy, sub_amount,real_price/unit_factor s_real_price , m.red_flag
    from sheet_sale_order_detail sd 
    left join sheet_sale_order_main m on m.sheet_id = sd.sheet_id and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
     
    UNION

  select sd.sheet_id,sheet_type,0 as money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,cost_price_avg , false cost_price_suspect,buy_price as cost_price_buy, sd.cost_amount as sub_amount,(sd.cost_price/sd.unit_factor) as s_real_price,m.red_flag
    from sheet_combine_detail sd 
    left join sheet_combine_main m on m.sheet_id = sd.sheet_id and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
     
    UNION
    
    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg,false cost_price_suspect, real_price/unit_factor cost_price_buy, coalesce(sub_amount,0)+coalesce(allocate_amount,0) as sub_amount,real_price/unit_factor s_real_price  , m.red_flag
    from sheet_buy_detail sd 
    left join sheet_buy_main m on sd.sheet_id = m.sheet_id and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
     
    UNION
     
    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg,false cost_price_suspect,           sd.buy_price  cost_price_buy,0 sub_amount,0 s_real_price    , m.red_flag
    from sheet_invent_change_detail sd left join sheet_invent_change_main m on sd.sheet_id = m.sheet_id  and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
    
    UNION
     
    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,0 quantity,  unit_factor,inout_flag,old_avg_price/unit_factor cost_price_avg,false cost_price_suspect,   0 cost_price_buy,0 sub_amount, (case when red_flag is null then real_price/unit_factor else old_avg_price/unit_factor end) s_real_price   , m.red_flag
    from  sheet_cost_price_adjust_detail sd left join sheet_cost_price_adjust_main m on sd.sheet_id = m.sheet_id  and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
    
    UNION

    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg,false cost_price_suspect,   0 cost_price_buy,0 sub_amount, 0 s_real_price   , m.red_flag
    from  sheet_stock_in_out_detail sd left join sheet_stock_in_out_main m on sd.sheet_id = m.sheet_id  and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}

) t 
left join info_item_prop ip on t.item_id = ip.item_id and ip.company_id={this.company_id}
order by t.item_id,t.happen_time";
			List<OrigSheetItem> lstOrigSheetItems = await CDbDealer.GetRecordsFromSQLAsync<OrigSheetItem>(sql,cmd);

            List<MergedSheetItem> lstMergedSheetItems = lstOrigSheetItems.GroupBy(
                r => new SheetItemMergeKey() 
                {
                    sheet_id = r.sheet_id, sheet_type = r.sheet_type, inout_flag = r.inout_flag, item_id = r.item_id, happen_time = r.happen_time,
                    cost_price_avg = r.cost_price_avg, cost_price_avg_info = r.cost_price_avg_info 
                }
            )
            .Select(g =>
            {
                return new MergedSheetItem()
                {
                    sheet_id = (string)g.Key.sheet_id,               
                    inout_flag = g.Key.inout_flag,                   
                    item_id = (string)g.Key.item_id,
                    happen_time = (string)g.Key.happen_time,
                    sheet_type = (string)g.Key.sheet_type,
                    //  unit_factor = (string)g.Key.unit_factor,
                    cost_price_avg = g.Key.cost_price_avg,
                    cost_price_avg_info = (string)g.Key.cost_price_avg_info,
                    // s_real_price = (string)g.Key.s_real_price,
                    sheet_qty = g.Sum(c =>
                    {
                        return CPubVars.ToDecimal(c.quantity) * CPubVars.ToDecimal(c.unit_factor) * CPubVars.ToDecimal(c.inout_flag);
                    }),
                    sheet_amount = g.Sum(c =>
                    {
                        if (c.sheet_type == "CG" || c.sheet_type == "ZZ" || c.sheet_type == "CF")
                        {
                            return CPubVars.ToDecimal(c.sub_amount) * CPubVars.ToDecimal(c.inout_flag);
                        }
                        else
                        {
                            decimal d = CPubVars.ToDecimal(c.quantity) * CPubVars.ToDecimal(c.unit_factor) * CPubVars.ToDecimal(c.inout_flag);
                            if (c.cost_price_avg != "")
                                d *= CPubVars.ToDecimal((string)c.cost_price_avg);
                            else if (c.cost_price_avg_info != "")
                                d *= CPubVars.ToDecimal((string)c.cost_price_avg_info);
                            else d = 0;
                            return d;// CPubVars.ToDecimal(c.sub_amount) * CPubVars.ToDecimal(c.inout_flag);

                        }

                    })
                };
            }).OrderBy(g => g.happen_time).ThenBy(g => g.sheet_type).ThenBy(g => g.item_id).ToList();
         
            return lstMergedSheetItems;
        }

        public async Task UpdateCostPriceAvg_NEW(CMySbCommand cmd, List<SheetRowCostPrice> lstSheetRows, string dealing_happen_time)
        {
            string updateItemProp = "";
            string updateSaleSql = "";
            string updateCombineSql = "";
            string log_sql = "";
            Dictionary<UpdateCostPriceKey, List<string>> dicUpdateCostPriceItems = new Dictionary<UpdateCostPriceKey, List<string>>();
            void addUpdateCostPriceSQL(UpdateCostPriceKey key,string sheetID)
            {
                List<string> lstSheetID = null;
                if (dicUpdateCostPriceItems.ContainsKey(key))
                    lstSheetID = dicUpdateCostPriceItems[key];
                else lstSheetID = new List<string>();
                lstSheetID.Add((string) sheetID);
            }


			bool ignoreHistory = false;

			if (this.happen_time.IsValid())
			{
				if (this.sheet_type == SHEET_TYPE.SHEET_SALE || this.sheet_type == SHEET_TYPE.SHEET_SALE_RETURN || this.sheet_type == SHEET_TYPE.SHEET_BUY || this.sheet_type == SHEET_TYPE.SHEET_BUY_RETURN)
				{
					var tm = Convert.ToDateTime(this.happen_time);
					if (tm < DateTime.Now.AddDays(-30))
					{
						ignoreHistory = true;
					}
				}

				if (this.sheet_type == SHEET_TYPE.SHEET_SALE || this.sheet_type == SHEET_TYPE.SHEET_SALE_RETURN || this.sheet_type == SHEET_TYPE.SHEET_COMBINE_ITEMS || this.sheet_type == SHEET_TYPE.SHEET_SPLIT_ITEMS)
				{

					if (this.company_setting != null)
					{
						string useAccounting = this.company_setting.useAccounting;
						if (useAccounting != null && useAccounting.ToLower() == "true")
						{
							ignoreHistory = true;
						}
					}
				}

			}



			var mergeRowAvg = MergeSheetRowsForAvg(lstSheetRows);//单据里相同商品的合并，当前单据

            //获取从 单据发生时间的前一刻 至今 该单据商品的变化的数量和成本额（采购单，销售单，盘点盈亏单)

            List<MergedSheetItem> lstHistorySheetItems = new List<MergedSheetItem>();

            if (!HappenNow && !ignoreHistory)
            {
                string items_id = string.Join(",", mergeRowAvg.Select(r => r.item_id));
                lstHistorySheetItems = await GetHistorySheetItemsAfterMe_new(cmd, items_id, dealing_happen_time);  
            }


            foreach (SheetRowCostPrice sheetRow in mergeRowAvg)
            {
                decimal sum_qty_change = 0;

                decimal firstCostPriceAvg = -1;//从本单据向后 第一个 有效成本价
                if (lstHistorySheetItems.Count > 0)
                {
                    sum_qty_change = lstHistorySheetItems.Sum(i => {//计算红冲单据之后的商品变化总数
                        if (i.item_id == sheetRow.item_id && i.sheet_type != "XD" && i.sheet_type != "TD")
                        {
                            return CPubVars.ToDecimal(i.sheet_qty);
                        }
                        else return 0m;


                    });//红冲单据之后的所有单据 该商品的数量变化

                    foreach (var r in lstHistorySheetItems)
                    {
                        if (r.item_id == sheetRow.item_id && r.cost_price_avg != "" && r.sheet_type != "XD" && r.sheet_type != "TD")
                        {
                            firstCostPriceAvg = CPubVars.ToDecimal((string)r.cost_price_avg);
                            break;
                        }
                    }
                }
        

                decimal earliest_stock_qty = sheetRow.old_total_qty; //审核单据/红冲前的库存数
                decimal earliest_cost_amt = sheetRow.old_total_qty * sheetRow.cost_price_avg;  //sheetRow.old_cost_amt;//单据审核/红冲前的库存金额
                decimal earliest_cost_price_avg = sheetRow.cost_price_avg;

                //算出发生时间之前的平均价 
                if (firstCostPriceAvg != -1)
                {
                    earliest_cost_price_avg = firstCostPriceAvg;
                    earliest_stock_qty -= sum_qty_change;
                    earliest_cost_amt = earliest_cost_price_avg * earliest_stock_qty;
                }

                decimal now_qty = earliest_stock_qty;
                decimal now_cost_amt = earliest_cost_amt;
                decimal now_cost_price_avg = earliest_cost_price_avg;
                decimal pre_cost_price_avg = now_cost_price_avg;
                bool bSuspecting = false;
                now_qty = Math.Round(now_qty, 3);
                if (now_qty < 0)
                {
                    bSuspecting = true;
                }

                //遍历某个商品 从本单据插入前一刻 至今 的所有单据

                var lstDealSheetItems = new List<MergedSheetItem>();//当前商品在 当前单据 及 以后的单据中的列表

                //将该商品在 当前单据 的信息 加入 待处理列表
                var row = new MergedSheetItem()
                {
                    item_id = sheetRow.item_id,
                    sheet_id = this.sheet_id,
                    RedFlag = this.red_flag,
                    sheet_type = this.SheetType,
                    inout_flag = sheetRow.inout_flag,
                   // quantity = sheetRow.quantity,
                    happen_time = this.happen_time,
                    sheet_qty = sheetRow.quantity,
                    sheet_amount = sheetRow.sub_amount,
                   // CostPriceAvg = sheetRow.cost_price_avg,
                    cost_price_avg=sheetRow.cost_price_avg.ToString(),
                    // unit_factor = sheetRow.unit_factor,
                    s_real_price = sheetRow.real_price / sheetRow.unit_factor

                };
               
                // row.cost_price_buy=sheetRow.cost_price_buy
                lstDealSheetItems.Add(row);

                //将该商品在 当前单据以后的历史单据 的信息 加入 待处理列表
                if (firstCostPriceAvg != -1)
                {
                    foreach (MergedSheetItem item in lstHistorySheetItems)
                    {
                        if (sheetRow.item_id == item.item_id)
                        {
                            bool isRededSheet = false;
                            if (this.red_flag == "2" && this.red_sheet_id == item.sheet_id)
                            {
                                isRededSheet = true;
                            }
                            if (!isRededSheet)
                                lstDealSheetItems.Add(item);
                        }
                    }
                }

                foreach (MergedSheetItem item in lstDealSheetItems)
                {
                    decimal qty_change = CPubVars.ToDecimal(item.sheet_qty);
                    if (item.sheet_type == "XD" || item.sheet_type == "TD") qty_change = 0;
                    decimal amount_change = 0;
                    if (",X,T,YK,BS,CT,".Contains("," + item.sheet_type + ","))
                    {
                        amount_change = qty_change * now_cost_price_avg;
                    }
                    else if (item.sheet_type == "QCKC" || item.sheet_type == "CG" || item.sheet_type == "FYFT")
                    {
                        amount_change = CPubVars.ToDecimal(item.sheet_amount);
                    }
                    else if (item.sheet_type == "ZZ" || item.sheet_type == "CF")
                    {
                        //amount_change = CPubVars.ToDecimal(item.sheet_amount);
                        if (item.RedFlag == "" && item.sheet_amount < 0)
                        {
                            amount_change = 0;
                            qty_change = 0;
                        }
                        else
                        {
                            amount_change = CPubVars.ToDecimal(item.sheet_amount);
                        }
                    }
                    else if (item.sheet_type == "CBTJ") amount_change = (CPubVars.ToDecimal(item.s_real_price) - now_cost_price_avg) * now_qty;


                    decimal pre_qty = now_qty;
                    now_qty += qty_change;
                    now_cost_amt += amount_change;

                    now_qty = Math.Round(now_qty, 3);
                    if (now_qty < 0 || pre_qty < 0 || now_cost_amt < 0)
                    {
                        bSuspecting = true;

                        log_sql += @$"insert into cost_price_log (
          company_id,  sheet_id,       item_id,  happen_time,         sheet_type, infected_sheet_id,   suspect_status) 
 values ({company_id},{sheet_id},{item.item_id},'{happen_time}','{item.sheet_type}','{item.sheet_id}',             '1')
on conflict(company_id,sheet_id,       item_id,   happen_time,        sheet_type, infected_sheet_id) do update set suspect_status = '1';";

                    }

                    if (!bSuspecting)
                    {
                        //如果是采购单，需要重算加权平均价   
                        if (item.sheet_type == "QCKC" || item.sheet_type == "CG" || item.sheet_type == "ZZ" || item.sheet_type == "CF" || item.sheet_type == "FYFT")
                        {
                            //如果采购后导致货值或库存为0 取本次采购的进价,防止出现加权价为0的情况
                            if (now_qty > 0 && now_cost_amt >= 0)
                            {
                                //if (item.red_flag !="" || amount_change>0)
                                 
                                pre_cost_price_avg = now_cost_price_avg;
                                now_cost_price_avg = now_cost_amt / now_qty; 
                            }


                            /*
                            double buy_price = sheetRow.sub_amount / sheetRow.quantity;

                            //发现加权价可能计算错误，就取预设进价
                            if (now_cost_price_avg < 0 || (buy_price > 0 && now_cost_price_avg > 10 * buy_price))
                            {
                                now_cost_price_avg = buy_price;
                                now_cost_amt = now_qty * now_cost_price_avg;
                            }*/
                        }
                        else if (item.sheet_type == "CBTJ") now_cost_price_avg = CPubVars.ToDecimal(item.s_real_price);
                    }

                    if (!HappenNow && firstCostPriceAvg != -1 && !ignoreHistory)
                    {
                        if (item.sheet_type == "X" || item.sheet_type == "T")
                        {
                            CDbDealer db = new CDbDealer();
                            db.AddField("cost_price_suspect", bSuspecting.ToString());
                            db.AddField("cost_price_avg", now_cost_price_avg.ToString());
                            UpdateCostPriceKey ck = new UpdateCostPriceKey { 
                                cost_price_suspect= bSuspecting.ToString(),cost_price_avg= now_cost_price_avg.ToString(),
                                item_id=item.item_id,sheet_type=item.sheet_type                                
                            };
                            addUpdateCostPriceSQL(ck, item.sheet_id);

                           // updateSaleSql += db.GetUpdateSQL("sheet_sale_detail", $"company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id}") + ";";

                        }
                        else if (item.sheet_type == "XD" || item.sheet_type == "TD")
                        {
                            CDbDealer db = new CDbDealer();
                            db.AddField("cost_price_avg", now_cost_price_avg.ToString());
                            UpdateCostPriceKey ck = new UpdateCostPriceKey
                            {
                                cost_price_avg = now_cost_price_avg.ToString(),
                                item_id = item.item_id,
                                sheet_type = item.sheet_type
                            };
                            addUpdateCostPriceSQL(ck, item.sheet_id);
                        }
                        else if (item.sheet_type == "CG" || item.sheet_type == "CT")
                        {
                            if (!bSuspecting)
                            {
                                UpdateCostPriceKey ck = new UpdateCostPriceKey()
                                {
                                    cost_price_suspect = "",
                                    cost_price_avg = pre_cost_price_avg.ToString(),
                                    item_id = item.item_id,
                                    sheet_type = item.sheet_type
                                };
                                addUpdateCostPriceSQL(ck, item.sheet_id);

                                updateSaleSql += @$"update sheet_buy_detail set cost_price_avg = {pre_cost_price_avg} where company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id};";
                            }
                        }
                        else if (item.sheet_type == "QCKC")
                        {
                            if (!bSuspecting)
                            {
                                //宏宁: 需要斟酌先注释掉了，sheet_store_stock_detail表不存在，期初库存单的成本价是录入进去的，不应该被刷新,需要再测试期初库存单是否会正确影响加权成本价
                                //updateSaleSql += @$"update sheet_store_stock_detail set cost_price_avg = {pre_cost_price_avg} where company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id};";
                            }
                        }
                        else if (item.sheet_type == "ZZ" || item.sheet_type == "CF")
                        {
                            if (!bSuspecting)
                            {
                                // 如果出仓商品成本变化=》入仓产品及出仓产品更新cost_price cost_amount
                                if (item.inout_flag == -1 && item.RedFlag == "")
                                {
                                    //var costPrice = CPubVars.ToDecimal(item.unit_factor) * now_cost_price_avg;
                                    //var costAmount = costPrice * CPubVars.ToDecimal(item.quantity);
                                    //更新对应出仓商品数据

                                    UpdateCostPriceKey ck = new UpdateCostPriceKey()
                                    {
                                        cost_price_suspect = "",
                                        cost_price_avg = now_cost_price_avg.ToString(),
                                        item_id = item.item_id,
                                        sheet_type = item.sheet_type,
                                        inout_flag=-1
                                    };
                                    addUpdateCostPriceSQL(ck, item.sheet_id);
                                     
                                    updateSaleSql += @$"update sheet_combine_detail set cost_price_avg = {now_cost_price_avg},cost_price = unit_factor *{now_cost_price_avg},cost_amount =quantity*unit_factor*{now_cost_price_avg} where company_id = {company_id} and sheet_id={item.sheet_id} and item_id = {item.item_id} and inout_flag = -1;";
                                    SQLQueue QQ = new SQLQueue(cmd);
                                    QQ.Enqueue("sheetRowsData", $"select * from sheet_combine_detail where company_id ={this.company_id} and sheet_id = {item.sheet_id} ");
                                    List<ExpandoObject> sheetRowsData = null;
                                    var inRows = new List<ExpandoObject>();
                                    var outRows = new List<ExpandoObject>();
                                    var dr = await QQ.ExecuteReaderAsync();
                                    while (QQ.Count > 0)
                                    {
                                        var sqlName = QQ.Dequeue();
                                        if (sqlName == "sheetRowsData") sheetRowsData = CDbDealer.GetRecordsFromDr(dr, false);
                                    }
                                    QQ.Clear();
                                    foreach (dynamic rowdata in sheetRowsData)
                                    {
                                        if (rowdata.inout_flag == "1") inRows.Add(rowdata);
                                        if (rowdata.inout_flag == "-1") outRows.Add(rowdata);
                                    }
                                    decimal totalCost = 0;//差额
                                    foreach (dynamic rowdata in outRows)
                                    {
                                        if (item.item_id == rowdata.item_id)
                                        {
                                            if (rowdata.cost_price != "")
                                            {
                                                rowdata.cost_amount = CPubVars.ToDecimal(rowdata.cost_price) * CPubVars.ToDecimal(rowdata.quantity);
                                            }
                                            else rowdata.cost_amount = 0;

                                            totalCost += ((now_cost_price_avg * CPubVars.ToDecimal(rowdata.quantity) * CPubVars.ToDecimal(rowdata.unit_factor)) - CPubVars.ToDecimal(rowdata.cost_amount));
                                        }
                                    }
                                    decimal totalOldToBranchCost = 0;
                                    foreach (dynamic rowdata in inRows)
                                    {
                                        if (rowdata.cost_amount == "") rowdata.cost_amount = 0;
                                        totalOldToBranchCost += CPubVars.ToDecimal(rowdata.cost_amount);
                                    }
                                    foreach (dynamic rowdata in inRows)
                                    {
                                        decimal newCostAmount = 0;
                                        if (totalOldToBranchCost != 0) newCostAmount = CPubVars.ToDecimal(rowdata.cost_amount) / totalOldToBranchCost * totalCost;
                                        decimal newCostPrice = newCostAmount / CPubVars.ToDecimal(rowdata.quantity);
                                      
                                        //updateSaleSql += @$"update sheet_combine_detail set cost_price =cost_price + {newCostPrice},cost_amount =cost_amount + {newCostAmount}  where company_id = {company_id} and item_id = {rowdata.item_id} and sheet_id = {rowdata.sheet_id} and inout_flag = 1;";
                                        updateCombineSql += @$"update sheet_combine_detail set cost_price =cost_price + {newCostPrice},cost_amount =cost_amount + {newCostAmount}  where company_id = {company_id} and item_id = {rowdata.item_id} and sheet_id = {rowdata.sheet_id} and inout_flag = 1;";

                                    }
                                }
                                else
                                {
                                    UpdateCostPriceKey ck = new UpdateCostPriceKey()
                                    {
                                        cost_price_suspect = "",
                                        cost_price_avg = pre_cost_price_avg.ToString(),
                                        item_id = item.item_id,
                                        sheet_type = item.sheet_type
                                    };
                                    addUpdateCostPriceSQL(ck, item.sheet_id);
                                    //  updateSaleSql += @$"update sheet_combine_detail set cost_price_avg = {pre_cost_price_avg} where company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id};";
                                }
                            }
                        }
                        else if (item.sheet_type == "YK" || item.sheet_type == "BS")
                        {
                            if (!bSuspecting)
                            {
                                UpdateCostPriceKey ck = new UpdateCostPriceKey()
                                {
                                    cost_price_suspect = "",
                                    cost_price_avg = now_cost_price_avg.ToString(),
                                    item_id = item.item_id,
                                    sheet_type = item.sheet_type
                                };
                                addUpdateCostPriceSQL(ck, item.sheet_id);
                                updateSaleSql += @$"update sheet_invent_change_detail set cost_price_avg = {now_cost_price_avg} where company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id};";
                            }
                        }
                    }
                }//foreach (MergedSheetItem item in lstDealSheetItems)


                if (sheetRow.item_cost_price_suspect != bSuspecting || now_cost_price_avg != sheetRow.cost_price_avg)
                {
                    CDbDealer db = new CDbDealer();
                    if (sheetRow.item_cost_price_suspect != bSuspecting) db.AddField("item_cost_price_suspect", bSuspecting.ToString());
                    if (!bSuspecting && now_cost_price_avg != sheetRow.cost_price_avg) db.AddField("cost_price_avg", now_cost_price_avg.ToString());
                    if (db.FieldCount > 0)
                    {
                        updateItemProp += db.GetUpdateSQL("info_item_prop", $"company_id = {this.company_id} and item_id = {sheetRow.item_id}") + ";";

                        log_sql += @$"insert into cost_price_log (
          company_id,           sheet_id,       item_id,      happen_time,         sheet_type,        infected_sheet_id,   new_cost_price) 
 values ({this.company_id},{this.sheet_id},{sheetRow.item_id},'{this.happen_time}','{this.SheetType}','{this.sheet_id}',  {now_cost_price_avg.ToString()})
on conflict(company_id,sheet_id,       item_id,   happen_time,        sheet_type, infected_sheet_id) do nothing;";

                    }
                }
            }//foreach (SheetRowCostPrice sheetRow in mergeRowAvg)


            string updateSheetSql = "";
            foreach (var kp in dicUpdateCostPriceItems)
            {
                UpdateCostPriceKey ck = kp.Key;
                string sheetIDs = string.Join(",", kp.Value);
                if (ck.sheet_type == "X" || ck.sheet_type == "T")
                { 
                    CDbDealer db = new CDbDealer();
                    db.AddField("cost_price_suspect", ck.cost_price_suspect);
                    db.AddField("cost_price_avg", ck.cost_price_avg);
                    updateSheetSql += db.GetUpdateSQL("sheet_sale_detail", $"company_id = {company_id} and item_id = {ck.item_id} and sheet_id in ({sheetIDs})") + ";";

                }
                else if (ck.sheet_type == "XD" || ck.sheet_type == "TD")
                {
                    CDbDealer db = new CDbDealer();
                    db.AddField("cost_price_avg", ck.cost_price_avg);
                    updateSheetSql += db.GetUpdateSQL("sheet_sale_order_detail", $"company_id = {company_id} and item_id = {ck.item_id} and sheet_id in ({sheetIDs})") + ";";

                }
                else if (ck.sheet_type == "CG" || ck.sheet_type == "CT")
                {
                    updateSheetSql += @$"update sheet_buy_detail set cost_price_avg = {ck.cost_price_avg} where company_id = {company_id} and item_id = {ck.item_id} and sheet_id in ({sheetIDs});";
                }
                else if (ck.sheet_type == "ZZ" || ck.sheet_type == "CF")
                {
                    if (ck.inout_flag == -1)
                    {
                        updateSheetSql += @$"update sheet_combine_detail set cost_price_avg = {ck.cost_price_avg},cost_price = unit_factor *{ck.cost_price_avg},cost_amount =quantity*unit_factor*{ck.cost_price_avg} where company_id = {company_id} and sheet_id={ck.sheet_id} and item_id = {ck.item_id} and inout_flag = -1;";
                    }
                    else
                        updateSheetSql += @$"update sheet_combine_detail set cost_price_avg = {ck.cost_price_avg} where company_id = {company_id} and item_id = {ck.item_id} and sheet_id in ({sheetIDs});";
                }
                else if (ck.sheet_type == "YK" || ck.sheet_type == "BS")
                {
                    updateSheetSql += @$"update sheet_invent_change_detail set cost_price_avg = {ck.cost_price_avg} where company_id = {company_id} and item_id = {ck.item_id} and sheet_id in ({sheetIDs});";
                }
            }


            string sql = updateItemProp + updateSheetSql + updateCombineSql + log_sql;
            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
        }
      

    }
}
