﻿@page
@model ArtisanManage.Pages.BaseInfo.CustomerLivenessModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head> 
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>

    <script type="text/javascript">
    window.g_operKey = '@Html.Raw(Model.OperKey)';

    var newCount = 1;

    var itemSource = {};
    $(document).ready(function() {
        @Html.Raw(Model.m_showFormScript)
    @Html.Raw(Model.m_createGridScript)
            window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());

    window.MultiSelect = @Html.Raw(Model.MultiSelect.ToString().ToLower());

        $("#gridItems").on("cellclick", function (event) {
            var args = event.args;
            var supcust_id = args.row.bounddata.supcust_id;
            var sup_name = args.row.bounddata.sup_name;
            var dateObj = getLatestThreeMonth();
            var endDay = dateObj.end;
            var startDay = dateObj.start;
            if (args.datafield == "sheets") {

                window.parent.newTabPage('查销售单', `Sheets/SaleSheetView?supcust_id=${supcust_id}&sup_name=${encodeURIComponent(sup_name)}&startDay=${startDay}&endDay=${endDay}`);
            }

        });
        $('#btnSelectItems').hide()
        $('#gridItems').jqxGrid('hidecolumn', 'sys_check')
        if (window.MultiSelect) {
           $('#btnSelectItems').show()
           $('#gridItems').jqxGrid('showcolumn', 'sys_check')
        }
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
        $('#supcust_id').jqxInput({
            onButtonClick: function (event) {
                $('#popClient').jqxWindow('open');
                $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
            }
        });
        $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            QueryData();
        });

        $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
            console.log("禁用浏览器默认右键")
           return false;
        });
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                    //$.ajax({
                    //    url: '/api/SaleSheet/GetItemInfo',
                    //    type: 'GET',
                    //    contentType: 'application/json',
                    //    data: { operKey: g_operKey, item_id: item_id },
                    //    success: function (data) {
                    //        if (data.result === 'OK') {
                    //            if (!window.g_queriedItems) window.g_queriedItems = {};
                    //            window.g_queriedItems[item_id] = data.item;
                    //        }
                    //    }
                    //});

                }
                $('#popClient').jqxWindow('close');
            }
        });
        function btnSelectItems_click() {
            //var rows = window.g_checkedRows
            var rows = window.g_arrCheckedRows
            var checkedRows=[]
            for (var id in rows) {
                 var row=rows[id]
                 //checkedRows.push({item_id:row.i,item_name:row.n})


                checkedRows.push({
                    supcust_id: row.supcust_id, sup_name: row.sup_name,boss_name:row.boss_name,mobile:row.mobile,sup_addr:row.sup_addr,acct_type:row.acct_type
                })

            }
            var msg = {
                msgHead: 'CustomerLiveness', action: 'selectMulti', checkedRows: checkedRows
            }
            window.parent.postMessage(msg, '*');
        }

        function onGridRowContextMenuClick(gridID, menuID, rowIndex) {

            var rows = $('#gridItems').jqxGrid('getrows')
           console.log(menuID)
            if (menuID == 'BatchOperation') {
                $('#gridItems').jqxGrid('showcolumn', 'sys_check')
                var a = $('#popBatchOperation')
                $('#popBatchOperation').css("display", "block")

            }

        }
            window.g_batchSetFld=''
        function popBatchSetDlg(fld) {

             $('#popBatchOperation').css("display", "none")
             $('#popSet').css("display", "block")
             window.g_batchSetFld=fld
                if (fld == "status") {
                $('#set_head').append('状态设置');
                $('#popBatchOperation').css("display", "none")
                $('#popSet').css("display", "block")
                $('#div_set').append('<label   style="line-height: 32px;"  >状态</label>');
                $('#div_set').append('<div > <select id="set" style="width: 200px;"><option >正常</option> <option >停用</option></select></div>');
                }
    
        }
                function btnClose_Clicked() {
            $('#popSet').css("display", "none")
            $('#div_set').empty()
            $('#set_head').empty()            
        }
        
       function btnSave_Clicked() {
           debugger
            console.log(g_batchSetFld)
            var rows = []
            for (var id in window.g_checkedRows) { 
                rows.push( parseInt(window.g_checkedRows[id].supcust_id))
            }
            //window.g_checkedRows.forEach(function (a) {

            //    rows.push(parseInt(a));
            //});

             if (rows.length == 0)   bw.toast("未做勾选");
            //rows = rows.join(',')

            var url = ''
            var params = {}
             if (g_batchSetFld == 'status') {
                status = $('#set').val()
                params = { status: status }
                url = '../api/clientsView/BatchSetStatus'
                executeSubmit(url,params,rows)
            }
        }
        function executeSubmit(url,params,rows){
            console.log("执行")
            params.operKey = '@Model.OperKey'
            params.rows = rows
            $.ajaxSetup({ contentType: "application/json" });
            $.post(url, JSON.stringify(params)).then(
                function (data) {
                 if (data.result == 'OK') {
                     bw.toast('操作成功');
                     $('#popSet').css("display", "none")
                     $('#div_set').empty()
                     $('#set_head').empty()
                     QueryData() 
                 } else {
                     $("#popMessage").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 500, width: 450, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                     $('#divMessage').html(data.msg)
                     $('#popMessage').jqxWindow('open');
                 }
             })
        
        }
        function getLatestThreeMonth() {
            var end = new Date();
            var year = end.getFullYear();
            var month = end.getMonth() + 1;//0-11表示1-12月
            var day = end.getDate();
            var dateObj = {};
            dateObj.end = year + '-' + month + '-' + day;
            var endMonthDay = new Date(year, month, 0).getDate();    //当前月的总天数
            if (month - 3 <= 0) { //如果是1、2、3月，年数往前推一年
                var start3MonthDay = new Date((year - 1), (12 - (3 - parseInt(month))), 0).getDate();    //3个月前所在月的总天数
                if (start3MonthDay < day) {    //3个月前所在月的总天数小于现在的天日期
                    dateObj.start = (year - 1) + '-' + (12 - (3 - month)) + '-' + start3MonthDay;
                } else {
                    dateObj.start = (year - 1) + '-' + (12 - (3 - month)) + '-' + day;
                }
            } else {
                var start3MonthDay = new Date(year, (parseInt(month) - 3), 0).getDate();    //3个月前所在月的总天数
                if (start3MonthDay < day) {    //3个月前所在月的总天数小于现在的天日期
                    if (day < endMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.start = year + '-' + (month - 3) + '-' + (start3MonthDay - (endMonthDay - day));
                    } else {
                        dateObj.start = year + '-' + (month - 3) + '-' + start3MonthDay;
                    }
                } else {
                    dateObj.start = year + '-' + (month - 3) + '-' + day;
                }
            }
            return dateObj;
        }
    </script>
    <style type="text/css">
        #div_unvisitDay > div:first-child {
            text-align: right;
            width: 120px;
        }
        #div_unsaleDay > div:first-child {
            text-align: right;
            width: 120px;
        }
        #popBatchOperation {
            width: 100px;
            height: 200px;
            position: fixed;
            top: 25%;
            left: 40%;
            z-index: 999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            display: none;
            text-align: center;
            cursor: pointer;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
        }
                #popBatchOperation {
            width: 100px;
            height: 200px;
            position: fixed;
            top: 25%;
            left: 40%;
            z-index: 999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            display: none;
            text-align: center;
            cursor: pointer;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
        }

        #popSet {
            width: 500px;
            height: 300px;
            position: fixed;
            top: 27%;
            left: 30%;
            z-index: 999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            padding: 5px;
            display: none;
            text-align: center;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
        }
        .magic {
            width: 100px;
            height: 25px;
        }

        .magic:hover {
            background: #ddd;
        }
       #div_set {
            display: none;
            margin-left: 123px;
            margin-top: 70px;
            display: flex;
            text-align: center;
        }
        #div_close {
            height: 16px;
            width: 20px;
            cursor: pointer;
            position: relative;
            left: 43px;
            top: 0px;
        }
        #div_close:hover{
             background: #ddd;
        }
    </style>
</head>

<body style="overflow: hidden">
    <div style="display: flex; margin-top: 20px; align-items: center; justify-content: center">
        <div id="divHead" class="headtail" style="width: calc(100% - 110px);">
            <div style="float: none; height: 0px; clear: both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-right: 20px; margin-top: 30px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left: 20px; margin-top: 30px;">导出</button>
    </div>

<div id="gridItems"></div>
<div id="divRowCount">
    <div>共<label id="rows_count">0</label>行</div></div>

<div id="popClient" style="display: none;">
    <div id="clientCaption" style="height: 30px; background-color: #fff; text-align: center;"><span style="font-size: 20px;">选择客户</span></div>
    <div style="overflow: hidden"> </div>
</div>

    <div style="display:none;" id="popBatchOperation">
        <svg id="div_close" onclick=" this.parentNode.style.display = 'none'">
            <use xlink:href="/images/images.svg?v=@Html.Raw(Model.Version)#close" />
        </svg>


        <div class="magic " onclick="popBatchSetDlg('status')">设置状态</div>

    </div>
    
    <div id="popSet" style="display: none;">
        <div style="height:30px;background-color:#fff; text-align:center;">
            <span id="set_head" style="font-size:18px;"></span>
        </div>
        <div id="div_set">

        </div>

        <div style="overflow:hidden;">
            <button onclick="btnSave_Clicked()" style="align-content:center;margin-top:45px;margin-left:20px;">确认</button>
            <button onclick="btnClose_Clicked();" style="align-content:center;margin-top:45px;margin-left:75px">关闭</button>
        </div>
    </div>
</body>