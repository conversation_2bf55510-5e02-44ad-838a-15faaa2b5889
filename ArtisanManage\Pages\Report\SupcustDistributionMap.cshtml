@page
@model ArtisanManage.VisitPathModel
@{
    Layout = null;
}


<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.aggregates.js"></script>
    <link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)">
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>
    <script type="text/javascript" src="//api.map.baidu.com/api?v=2.0&&ak=@Html.Raw(Model.BaiduKey)"></script>
    <script type="text/javascript" src="//api.map.baidu.com/library/TextIconOverlay/1.2/src/TextIconOverlay_min.js"></script>
    <!-- <script type="text/javascript" src="~/uploads/templates/yingjiang_lushu.js"></script>-->
    <script type="text/javascript" src="~/js/yj_markercluster.js"></script>

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var map = {}
        var g_supcusts = []
        var g_region_dics=[]
        var g_groupType ='seller'
        function initMap({ supcusts }) {
            var map = new BMap.Map("map");
            var MAX = 10;
            var i = 0;
            if (supcusts.length != 0) {
                map.centerAndZoom(new BMap.Point(supcusts[0].addr_lng, supcusts[0].addr_lat), 10);
            }
            else {
               bw.toast("暂无有坐标点的客户")
               return
            }
            map.enableScrollWheelZoom();
            var markers = supcusts.filter(supcust => supcust.addr_lng && supcust.addr_lat).map((supcust,index) => {
                var point = new BMap.Point(supcust.addr_lng, supcust.addr_lat)
                var marker = new BMap.Marker(point)
                marker.addEventListener('click', e => {
                    $.ajax({
                        url: "/api/SupcustDistribution/GetSupcustInfo",
                        data: {
                            operKey: window.g_operKey,
                            supcustID: supcusts[index].supcust_id
                        },
                        success: (res) => {
                            const {sup_name,total_amount,happen_time,day_name}=res.data
                            // ������Ϣ����
                            var opts = {
                                width: 200,
                                height: 150,
                                title: ''
                            };
                            var routeTagDiv = day_name.split("$").map(name => {
                                return "<div style='background-color:#67C23A;border-radius:8px;color:#fff;margin-right:4px;padding:2px;'>"+name+"</div>"
                            }).join("")
                            var infoWindow = new BMap.InfoWindow(`<div>
                                                                    <div>客户店名：${sup_name}</div>
                                                                    <div>销售额：${total_amount}</div>
                                                                    <div>最后一次销售时间：${happen_time}</div>
                                                                    <div style='display:flex;flex-direction:row;'>${routeTagDiv}</div>
                                                                </div>`, opts);
                            map.openInfoWindow(infoWindow, point);
                        }
                    })
                })
                return  marker
            })
            var markerClusterer = new BMapLib.MarkerClusterer(map, { markers: markers });
        }
        function queryByType(key) {
            var results 
            if (g_groupType === 'sup_type') {
                results = g_supcusts.filter(supcust => supcust.group_name === key)
            }
            if (g_groupType === 'seller') {
                results = g_supcusts.filter(supcust => supcust.creator_name === key)
            }
            if (g_groupType === 'region') {
                const clickRegion = g_region_dics.find(region => region.region_name === key)
                console.log(clickRegion)
                results = g_supcusts.filter(supcust => supcust.other_region.startsWith("/" + clickRegion.mother_id + "/" + clickRegion.region_id + "/"))
            }
            const options = document.getElementsByClassName("option")
            Array.from(options).map(element => { 
                $(element).removeAttr("active")
                $(element).attr("style","background:#fff")
            })
            $(`[data-key='${key}']`).attr("style","background:#ccc")
            $(`[data-key='${key}']`).attr("active",true)

            //g_supcusts = results
            initMap({ supcusts: results })
        }
        function chooseType(type) {
            g_groupType = type
            const types = generateTypes({ supcusts: g_supcusts }, type)
            renderTypesOptionTemplates(types)

        }
        function generateTypes({ supcusts }, groupType = 'seller') {
            var typeCount = {}
            if (groupType === 'seller') {
                supcusts.map(supcust => {
                    if (supcust.creator_name && typeof typeCount[supcust.creator_name] !== 'undefined') {
                        typeCount[supcust.creator_name]++
                    } else {
                        typeCount[supcust.creator_name] = 1
                    }
                })
            }
            if (groupType === 'sup_type') {
                supcusts.map(supcust => {
                    if (supcust.group_name && typeof typeCount[supcust.group_name] !== 'undefined') {
                        typeCount[supcust.group_name]++
                    } else {
                        typeCount[supcust.group_name] = 1
                    }

                })
            }
            if (groupType === 'region') {
                supcusts.map(supcust => {
                    const region = matchRegion(supcust.other_region)
                    if (typeof region == 'undefined') {
                        return
                    }
                    if (typeof typeCount[region.region_name] === 'undefined') {
                        typeCount[region.region_name] = 1
                    }
                    typeCount[region.region_name]++

                })
            }
            return typeCount
        }
        function matchRegion(regionID) {
            console.log(g_region_dics)
            return g_region_dics.filter(region => regionID.startsWith("/" + region.mother_id + "/"+region.region_id +"/"))[0]
        }
        function renderTypesOptionTemplates(types) {
            document.getElementById("typeOptions").innerHTML = "<div>" + Object.keys(types).map(key => {
                return `<div data-key='${key}' onclick="queryByType('${key}')" class='option'>
                          <div>${key}</div>
                          <div>${types[key]}</div>
                          </div>`
            }).join("") + "</div>"
            const options = document.getElementsByClassName("option")
            Array.from(options).map(element => {
     
                    element.addEventListener("mouseover", (e) => {
                    if (!$(element).attr("active")) {
                        e.currentTarget.style.setProperty("background", "#eee")
                     }
                    })
                    element.addEventListener("mouseout", (e) => {
                     if (!$(element).attr("active")) {
                        e.currentTarget.style.setProperty("background", "#fff")
                     }
                 
                    })
                
            })
        }
        $(document).ready(function () {
            $.ajax("/api/SupcustDistribution/GetQueryRecords", {
                data: {
                    operKey: window.g_operKey
                },
                success: (res) => {
                    initMap({ supcusts: res.data })
                    g_supcusts = res.data
                    g_region_dics=res.regionData
                    const types = generateTypes({ supcusts: res.data })
                    renderTypesOptionTemplates(types)
                }
            })


     

        })
    </script>
    <style>
        #map {
            height: 900px;
            width: 100%;
        }
    </style>
</head>
<body>

    <div id="map">
    </div>
    <div id="groupTypeOptions">
        <div class="option" onclick="chooseType('seller')">业务员</div>
        <div class="option" onclick="chooseType('sup_type')">类别</div>
        <div class="option" onclick="chooseType('region')">片区</div>

    </div>
    <div id="typeOptions">
    </div>
</body>

</html>
<style scoped>
    #typeOptions{
        position:absolute;
        top:20px;
        left:80px;
        background:#fff;
        opacity:.8;
        height:900px;
        overflow-y:auto;
    }
    #groupTypeOptions > div {
        padding: 10px 5px;
        cursor:pointer;
    }
    #groupTypeOptions {
        position: absolute;
        top: 20px;
        left: 20px;
        background: #fff;
        opacity: .8;
        height: 140px;
        overflow-y: auto;
    }
    .option {
        padding: 10px 5px;
        border-bottom:1px solid #333;
        cursor:pointer;
    }
</style>
