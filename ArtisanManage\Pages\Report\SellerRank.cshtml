@page
@model ArtisanManage.Pages.BaseInfo.SellerRankModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel"/> 

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)'; 
    	    var newCount = 1; 

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                });
            
                $('.btnAct').on('click', function () {
                    var act = $(this).data('act');
                    window[act]();
                });
                QueryData();
    	    });
    </script>
</head>

<body>
    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
    </div>
  
    <div id="gridItems"></div>  
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div> 
        

    

</body>
</html>