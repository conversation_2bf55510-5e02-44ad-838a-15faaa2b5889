﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Pages.Sheets;
using ArtisanManage.Services;
using ArtisanManage.YingjiangMessage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Asn1.Ocsp;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using static ArtisanManage.AppController.SheetVisitController;
using static ArtisanManage.Services.CommonTool;

namespace ArtisanManage.AppController
{
    /// <summary>
    /// 客户
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class InfoClientController : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public InfoClientController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// 获取客户ID，客户NAME，客户TEL，客户地址
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="getTotal">客户数量</param>
        /// <param name="searchStr"></param>yo
        /// <param name="region_id">片区</param>
        /// <returns></returns>

        //[HttpGet]
        //public async Task<JsonResult> GetClientList(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string region_id, string currentLat, string currentLng, string supcustFlag)
        //{
        //    Security.GetInfoFromOperKey(operKey, out string companyID);
        //    if (supcustFlag.IsInvalid()) supcustFlag = "C";
        //    var condi = $"company_id = {companyID} and supcust_flag = '{supcustFlag}' and status = 1";
        //    var distanceSQL = "";
        //    var orderCondi = "";
        //    if (!String.IsNullOrEmpty(searchStr)) condi += $" and (sup_name ilike '%{searchStr}%' or py_str ilike '%{searchStr}%' ) ";
        //    if (!String.IsNullOrEmpty(region_id)) condi += $" and other_region ilike '%/{region_id}/%'";
        //    if (currentLng.IsValid() && currentLat.IsValid())
        //    {
        //        /*                distanceSQL += $", (ST_GeomFromText (st_astext(addr_lnglat), 4326 ) <-> ST_GeomFromText ( 'POINT({currentLng} {currentLat})', 4326 ))  as distance ";
        //        */
        //        distanceSQL += $",st_distance(ST_Transform(ST_SetSRID(addr_lnglat,4326)::geometry, 3857),ST_Transform('SRID=4326;POINT({currentLng} {currentLat})'::geometry, 3857)) as distance";
        //        orderCondi = "distance ASC,";
        //    }
        //    SQLQueue QQ = new SQLQueue(cmd);
        //    var sql = @$"select supcust_id,sup_name,acct_cust_id,acct_cust_name,mobile as sup_tel,sup_addr{distanceSQL},addr_lng,addr_lat,status from info_supcust s
        //                 left join (select supcust_id as a_supcust_id,sup_name as acct_cust_name from info_supcust where company_id = {companyID}) a on s.acct_cust_id = a.a_supcust_id
        //                 where {condi} order by {orderCondi}supcust_id desc limit {pageSize} offset {startRow};";
        //    QQ.Enqueue("data", sql);
        //    if (getTotal)
        //    {
        //        sql = $"select count(supcust_id) as total from info_supcust where {condi}";
        //        QQ.Enqueue("count", sql);
        //    }
        //    var data = new List<ExpandoObject>();
        //    var dr = await QQ.ExecuteReaderAsync();
        //    var total = "";
        //    while (QQ.Count > 0)
        //    {
        //        var sqlName = QQ.Dequeue();
        //        if (sqlName == "data")
        //        {
        //            data = CDbDealer.GetRecordsFromDr(dr, false);
        //        }
        //        else if (sqlName == "count")
        //        {
        //            dr.Read();
        //            total = CPubVars.GetTextFromDr(dr, "total");
        //        }
        //    }
        //    QQ.Clear();
        //    string result = "OK";
        //    string msg = "";
        //    return Json(new { result, msg, data, total });
        //}
        [HttpGet]

        public async Task<JsonResult> GetNewSupcustCount(string operKey, string operID, string operRegions)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            DateTime dt = DateTime.Now;
            string regionCondi = "";
            int[] regions = JsonConvert.DeserializeObject<int[]>(operRegions);
            if (regions.Length > 0)
                regionCondi += " and (" + string.Join(" or ", regions.Select(x => $"  other_region like '%/{x}/%'")) + ") ";
            string operIDCondition = @$" ";
            if (operID.IsValid())
            {
                operIDCondition += @$"and creator_id = {operID}";
            }
            string yearCondition = @$"{dt.Year}-01-01 00:00";
            string monthCondition = $@"{dt.Year}-{dt.Month.ToString().PadLeft(2, '0')}-01 00:00";
            string lastYearCondition = @$"create_time between '{dt.Year - 1}-01-01 00:00'  and  '{dt.Year - 1}-{dt.Month.ToString().PadLeft(2, '0')}-{dt.Day.ToString().PadLeft(2, '0')} 00:00'";
            string lastMonthCondition = $@"create_time between '{dt.Year - 1}-{dt.Month}-01 00:00'  and  '{dt.Year - 1}-{dt.Month.ToString().PadLeft(2, '0')}-{dt.Day.ToString().PadLeft(2, '0')} 00:00'";
            string weekCondition = $@"{dt.Year}-{dt.AddDays(-6).Month.ToString().PadLeft(2, '0')}-{dt.AddDays(-6).Day.ToString().PadLeft(2, '0')} 00:00";
            string lastDayCondition = $@"between '{dt.AddDays(-1).ToString("d")} 00:00' and  '{dt.AddDays(-1).ToString("d")} 23:59'";
            string threeDayCondition = $@"{dt.AddDays(-2).ToString("d")} 00:00";
            string todayCondition = $@"{dt.ToString("d")} 00:00";
            string sql = $@"
            SELECT 
            (SELECT count(1) AS year_count FROM info_supcust where create_time > '{yearCondition}' {operIDCondition} {regionCondi} and company_id = {companyID} and COALESCE(status,'1')='1' and other_region is not null) ,
            (SELECT count(1) AS month_count FROM info_supcust where create_time > '{monthCondition}' {operIDCondition} {regionCondi} and company_id = {companyID} and COALESCE(status,'1')='1' and other_region is not null) ,
            (SELECT count(1) AS week_count FROM info_supcust where create_time > '{weekCondition}' {operIDCondition} {regionCondi} and company_id = {companyID} and COALESCE(status,'1')='1' and other_region is not null)  ,
            (SELECT count(1) AS threeday_count FROM info_supcust where create_time > '{threeDayCondition}'  {operIDCondition} {regionCondi} and company_id = {companyID} and COALESCE(status,'1')='1'  and other_region is not null),
            (SELECT count(1) AS lastday_count FROM info_supcust where create_time  {lastDayCondition}  {operIDCondition} {regionCondi} and company_id = {companyID} and COALESCE(status,'1')='1'  and other_region is not null),
            (SELECT count(1) AS today_count FROM info_supcust where create_time >  '{todayCondition}'  {operIDCondition} {regionCondi} and company_id = {companyID} and COALESCE(status,'1')='1' and other_region is not null),
            (SELECT count(1) AS last_year_count FROM info_supcust where {lastYearCondition} {operIDCondition} {regionCondi} and company_id = {companyID} and COALESCE(status,'1')='1' and other_region is not null) ,
            (SELECT count(1) AS last_month_count FROM info_supcust where {lastMonthCondition} {operIDCondition} {regionCondi} and company_id = {companyID} and COALESCE(status,'1')='1' and other_region is not null)  
            ";
            cmd.CommandText = sql;
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            string result = "OK";
            return Json(new { result, data });
        }
        [HttpGet]

        public async Task<JsonResult> GetYearSupcustCountGroupBySeller(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            DateTime dt = DateTime.Now;


            string sql = $@"
	        SELECT  count(1),creator_id,oper_name from info_supcust isp
	        right JOIN info_operator iop on iop.oper_id = isp.creator_id and isp.company_id = iop.company_id and  COALESCE(iop.status,'1')='1'
	        WHERE  isp.company_id ={companyID} and  create_time BETWEEN '{dt.Year}-01-01 00:00' and '{dt.Year}-{dt.Month.ToString().PadLeft(2, '0')}-{dt.Day.ToString().PadLeft(2, '0')} 23:59' and  COALESCE(isp.status,'1')='1' GROUP BY creator_id,oper_name
            ";
            cmd.CommandText = sql;
            dynamic data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            return Json(new { result, data });
        }
        /// <summary>
        /// 获取客户ID，客户NAME，客户TEL，客户地址 --2021/3/8 新增查询条件：区域 --wyj
        /// </summary>
        /// <param name="operRegions">[145,146,142] </param>

        [HttpGet]
        public async Task<JsonResult> GetClientList(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string regionID, string currentLat, string currentLng, string supcustFlag, string operRegions, bool showAcctCusts, string startCreateTime, string endCreateTime, string creatorOperID, string orderBy, bool approveStatus, string supcustStatusType, string approveStatusType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            if (supcustFlag.IsInvalid()) supcustFlag = "C";
            var condi = $" and supcust_flag  like '%{supcustFlag}%' ";
            if (operRegions.IsValid())
            {
                var regions = JsonConvert.DeserializeObject<int[]>(operRegions);
                if (regions.Length > 0)
                    condi += " and (" + string.Join(" or ", regions.Select(x => $"  other_region like '%/{x}/%'or other_region is null")) + ") ";
            }
            var distanceSQL = "";
            var orderBySQL = "";
            var supcustStatusSQL = " and (s.status = '1' or s.status is null) ";
            if (supcustStatusType.IsValid())
            {
                if (supcustStatusType == "all")
                {
                    supcustStatusSQL = "";
                }
                if (supcustStatusType == "stop")
                {
                    supcustStatusSQL = " and (s.status = '0') ";
                }
            }
            condi += supcustStatusSQL;
            if (approveStatusType.IsValid())
            {
                if (approveStatusType == "all")
                {
                    condi += "";
                }
                if (approveStatusType == "approved")
                {
                    condi += " and approve_status is null";
                }
                if (approveStatusType == "unapproved")
                {
                    condi += " and approve_status = 'wait approve'";
                }
            }
            if (searchStr.IsValid()) condi += $" and (sup_name ilike '%{searchStr}%' or py_str ilike '%{searchStr}%'  or mobile ilike '%{searchStr}%' or supcust_no like '%{searchStr}%' or boss_name like '%{searchStr}%' ) ";
            if (regionID.IsValid()) condi += $" and other_region ilike '%/{regionID}/%'";
            if (showAcctCusts) condi += $" and (acct_cust_id is null)"; //显示结算单位
            if (startCreateTime.IsValid() && endCreateTime.IsValid()) condi += $" and create_time between '{startCreateTime}' and '{endCreateTime}'"; //创建时间筛选
            if (creatorOperID.IsValid()) condi += $" and creator_id = '{creatorOperID}'"; //创建人筛选
            if (approveStatus) condi += $" and approve_status = 'wait approve'";
            bool positionIsValid = currentLng.IsValid() && currentLat.IsValid();
            if (positionIsValid)
            {
                distanceSQL = $",st_distance(ST_Transform(ST_SetSRID(addr_lnglat,4326)::geometry, 3857),ST_Transform('SRID=4326;POINT({currentLng} {currentLat})'::geometry, 3857)) as distance";
            }
            if (orderBy == "orderIndex")
                orderBySQL = $"order by sup_order_index ASC,supcust_id DESC";
            else if (positionIsValid)
                orderBySQL = $"order by distance ASC,supcust_id DESC";
            else
                orderBySQL = $"order by supcust_id DESC";

            // var isVisitedSQL = $"SELECT 1 as is_isited FROM info_supcust ist LEFT JOIN sheet_visit sv ON ist.supcust_id=sv.supcust_id sv.start_time between '2021-3-29 00:00:00' AND '2021-3-29 23:59:59' AND ist.supcust_id=17045 LIMIT 1";
            SQLQueue QQ = new SQLQueue(cmd);
            var startTime = DateTime.Now.ToString("yyyy-MM-dd");
            //mobile as sup_tel, 待删
            var sql_NoLimit = @$"

select s.supcust_flag,s.supcust_id,sup_name,supcust_no,s.sup_door_picture,s.sup_door_photo,acct_cust_id,license_no,acct_cust_name,charge_seller, charge_seller_name,s.mobile,s.mobile as sup_tel,sup_addr{distanceSQL},addr_lng,addr_lat,s.status,s.acct_type,acct_way_name,realtime->>'lastSaleTime' as last_sale_time,realtime->>'lastVisitTime' as last_visit_time,s.sup_rank,s.sup_group,s.other_region as sup_regions,approve_status,visit_cycle
from info_supcust s
left join 
(
   select supcust_id as a_supcust_id,sup_name as acct_cust_name from info_supcust where company_id = {Token.CompanyID} limit 1
) a on s.acct_cust_id = a.a_supcust_id
left join info_acct_way aw on s.acct_way_id=aw.acct_way_id and s.company_id=aw.company_id
left join realtime_supcust r on s.supcust_id=r.supcust_id and r.company_id= {Token.CompanyID}
left join info_seller_supcust_cycle issc on issc.supcust_id=s.supcust_id and issc.seller_id = {operID}
left join
(
    select oper_id,oper_name charge_seller_name from info_operator where company_id={Token.CompanyID}
) op on s.charge_seller=op.oper_id
where s.company_id = {Token.CompanyID} {condi} ";
            var sql = $"{sql_NoLimit}  {orderBySQL} limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);
            //var getVisitedSupcustIdListSQL = $"SELECT distinct supcust_id FROM sheet_visit where start_time >= '{startTime}' AND company_id = {Token.CompanyID}";
            //QQ.Enqueue("visitedSupcustIdListData", getVisitedSupcustIdListSQL);


            if (getTotal)
            {
                sql = $"select count(*) as total from ({sql_NoLimit}) t";
                QQ.Enqueue("count", sql);
            }

            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> data = null;
            string total = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                /*if (sqlName == "visitedSupcustIdListData")
                {
                    dynamic dataList = CDbDealer.GetRecordsFromDr(dr, false);
                    string[] visitedSupcustIdList = new string[dataList.Count];
                    for(int i = 0; i < dataList.Count; i++)
                    {
                        visitedSupcustIdList[i] = dataList[i].supcust_id;
                    }
                    response.Add("visitedSupcustIdList", visitedSupcustIdList);
                }*/
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);

                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", msg = "", data, total });

        }

        /// <summary>
        /// 通过客户Id获得客户详情
        /// </summary>
        /// <param name="supcustId"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetClientById(string supcustId, string operKey)
        {
            string result = "OK";
            string msg = "";
            //Dictionary<string, dynamic> data = new Dictionary<string, dynamic>();
            Token.TryParse(operKey, out Token token);

            //SQLQueue QQ = new SQLQueue(cmd);
            var condi = $"sc.company_id = {token.CompanyID} and sc.supcust_id={supcustId} and sc.supcust_flag ilike '%C%' ";
            var clientInfoSql = @$"SELECT sc.status,sc.sup_alias,sc.addr_lng,sc.addr_lat,sc.supcust_id,sc.sup_name,sc.supcust_remark,sc.py_str,sc.sup_door_picture,acct_cust_id,acct_cust_name,sc.boss_name,sc.mobile,sc.supcust_no,sc.sup_group,sg.group_name,sc.sup_rank,rank_name,sc.region_id,r.region_name,sc.other_region,sc.sup_addr,sc.acct_type,sc.retail_wholesale_flag,sc.license_no,sc.sup_door_photo,total_wx_user,sc.approve_status,io.oper_name as charge_name,sc.acct_way_id,acct_way_name,other_region_name,m.mall_type,sup_other_photo,case when (relate_store is not null and ib.relate_client is not null and coalesce(ib.status, 1) = 1) then true else false end as relate_store,sc.mall_client_payment_type
                            FROM info_supcust sc
                            LEFT JOIN (select supcust_id as a_supcust_id,sup_name as acct_cust_name from info_supcust where company_id = {token.CompanyID}) a on sc.acct_cust_id = a.a_supcust_id
                            LEFT JOIN info_supcust_rank sr on sr.rank_id = sc.sup_rank
	                        LEFT JOIN info_supcust_group sg on sg.group_id = sc.sup_group
	                        LEFT JOIN info_region r on r.region_id = sc.region_id
	                        LEFT JOIN (select string_agg(region_name, '/' order by ir.region_id) as other_region_name,supcust_id
										from info_region ir
															left join 
														( select unnest(region_array[3:array_length(region_array, 1) - 1]) as region_id,
																								supcust_id
																				from (SELECT string_to_array(other_region, '/') AS region_array, supcust_id
																							FROM info_supcust
																							WHERE company_id = {token.CompanyID}
																								and supcust_id ={supcustId}) ss) iss
																																	
										on ir.region_id::text = iss.region_id
										where ir.company_id = {token.CompanyID}
											and supcust_id = {supcustId} group by supcust_id ) irr on irr.supcust_id = sc.supcust_id
                            LEFT JOIN info_operator io on io.oper_id = sc.charge_seller and io.company_id = {token.CompanyID}
                            left join (select status, relate_client from info_branch where company_id = {token.CompanyID}) ib on ib.relate_client = sc.supcust_id
                            LEFT JOIN ( SELECT COUNT ( * ) AS total_wx_user, company_id,supcust_id FROM info_cust_contact icc WHERE icc.company_id = {token.CompanyID} AND icc.supcust_id={supcustId} GROUP BY company_id,supcust_id) c on c.company_id =  sc.company_id and c.supcust_id=sc.supcust_id
                            LEFT JOIN ( SELECT mall_type, company_id FROM mall_setting ms WHERE ms.company_id = {token.CompanyID} GROUP BY company_id) m on m.company_id =  sc.company_id
                            LEFT JOIN (SELECT acct_way_id, acct_way_name from info_acct_way where company_id = {token.CompanyID}) iaw on iaw.acct_way_id = sc.acct_way_id
                        where {condi};";
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync(clientInfoSql, cmd);
            //data.Add("clientInfoData", clientInfoData);
            dynamic editLog = null;
            if (data.approve_status == "wait approve")
            {
                string logInfoSql = $@"select dcl.*,io.oper_name as edit_name from document_change_log dcl 
                                left join (select oper_name,oper_id from info_operator where company_id =  {token.CompanyID} ) io on io.oper_id = dcl.oper_id
                            where company_id = {token.CompanyID} and obj_id = {supcustId} order by happen_time desc limit 1;";
                editLog = await CDbDealer.Get1RecordFromSQLAsync(logInfoSql, cmd);
                // data.Add("logInfoData", logInfo);
            }

            return Json(new { result, msg, data, editLog });
        }

        [HttpGet]
        public async Task<JsonResult> GetSupplierById(string supcustId, string operKey)
        {
            string result = "OK";
            string msg = "";
            dynamic data = null;
            if (Token.TryParse(operKey, out Token token))
            {
                var condi = $"company_id = {token.CompanyID} and supcust_id={supcustId} and supcust_flag ilike '%S%' ";
                var sql = @$"SELECT supcust_id,sup_name,py_str,boss_name,mobile,status,sup_addr
                                FROM info_supcust 
                           where {condi};";
                data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            }
            else
            {
                result = "Failed";
                msg = "身份验证失败，请重新登录";
            }
            return Json(new { result, msg, data });
        }

        /// <summary>
        /// 供应商信息
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="sup_name">供应商名字</param>
        /// <param name="mobile">供应商电话</param>
        /// <returns></returns>


        [HttpGet]
        public async Task<JsonResult> GetSuplierInfo(string operKey, int pageSize, int startRow, string sup_name, string mobile)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"company_id = {companyID} and supcust_flag = 'S' ";
            if (!string.IsNullOrEmpty(sup_name)) condi += $"and (sup_name ilike '%{sup_name}%' or py_str ilike '%{sup_name}%' ";
            if (!string.IsNullOrEmpty(mobile)) condi += $"and mobile like '%{mobile}%'";
            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"select supcust_id,sup_name,mobile from info_supcust where {condi}  limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);
            sql = $"select count(supcust_id) as count from info_supcust where {condi}";
            QQ.Enqueue("count", sql);
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "count");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total });
        }


        /*
        /// <summary>
        /// 获取客户门店列表(开单据时使用)
        /// <param name="operKey">返回值中：客户门店ID，客户门店姓名 </param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="searchStr"></param>
        /// <param name="region_id"></param>
        /// <param name="currentLat"></param>
        /// <param name="currentLng"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetShopsList(string operKey, int pageSize, int startRow, string searchStr, string region_id, string currentLat, string currentLng)
        {
            bool firstRequest = false;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"s.company_id = {companyID} and supcust_flag ilike '%C%' ";
            var distanceSQL = "";
            var orderCondi = "";
            var sql = "";
            if (searchStr.IsValid()) condi += $" and (sup_name ilike '%{searchStr}%' or py_str ilike '%{searchStr}%' or shop_name ilike '%{searchStr}%' or shop_py_str ilike '%{searchStr}%' ) ";
            if (region_id.IsValid()) condi += $" and (other_region like '%/{region_id}/%' or shop_other_region like '%/{region_id}/%')";
            if (startRow == 0) firstRequest = true;
            if (currentLng.IsValid() && currentLat.IsValid())
            {
                distanceSQL += $", ST_GeomFromText (st_astext(addr_lnglat), 4326 ) <-> ST_GeomFromText ( 'POINT({currentLng} {currentLat})', 4326 )  as distance ";
                orderCondi = "distance ASC,";
            }
            SQLQueue QQ = new SQLQueue(cmd);
            var sql_noLimit = @$"select supcust_id,sup_name,sup_addr{distanceSQL} from (
                                    SELECT (case when shop_id is null then s.supcust_id::text else concat(s.supcust_id,',',shop_id) end) supcust_id,
                                           (case when shop_name is null then sup_name else concat(sup_name,',',shop_name) end) sup_name,
                                           (case when shop_addr is null then sup_addr else shop_addr end) sup_addr,
                                           (case when shop_addr_lng is null then addr_lnglat else shop_addr_lnglat end) addr_lnglat 
                                      FROM info_supcust s left join info_supcust_shop ss on s.supcust_id = ss.supcust_id where {condi} ) t order by {orderCondi}supcust_id desc ";
            sql = sql_noLimit + $"limit {pageSize} offset {startRow}";
            QQ.Enqueue("data", sql);
            if (firstRequest)
            {
                sql = $"select count(*) as total from ({sql_noLimit}) tt ";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count"&&firstRequest)
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total });
        }
        */
        /// <summary>
        /// 选择片区
        /// </summary>
        /// <param name="operKey"> </param>
        /// <returns></returns>
        [HttpGet]
        //public async Task<JsonResult> GetRegion(string operKey)
        //{
        //    Security.GetInfoFromOperKey(operKey, out string companyID);
        //    var sql = @$"select region_id as id,region_name as name,mother_id from info_region where company_id = {companyID};";
        //    var list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
        //    var arr = JsonConvert.DeserializeObject<List<Tree>>(JsonConvert.SerializeObject(list));
        //    var data = CommonTool.ToTree(arr);
        //    string result = "OK";
        //    string msg = "";
        //    return Json(new { result, msg, data });
        //}

        /// <summary>
        /// 增加查询条件：区域
        /// </summary>
        /// <param name="operRegions">[145,146,142] </param>
        /// <returns></returns>
        public async Task<JsonResult> GetRegion(string operRegions)
        {
            var regions = await cmd.QueryAsync<Region>(Token.CompanyID, null, "order_index,region_id");
            var region = regions.ToTree1();
            if (operRegions.IsValid())
            {
                var regionIds = JsonConvert.DeserializeObject<int[]>(operRegions);
                if (regionIds.Length > 0)
                    region.Filter(regionIds);
            }
            return new JsonResult(new { result = "OK", msg = "", data = region });
        }

        /// <summary>
        /// 此接口未测试，如果有问题，请改用上面的接口
        /// </summary>
        /// <returns></returns>
        /*[HttpGet]
        public async Task<Response> GetRegion(string operKey)
        {
            var list = await cmd.QueryWhereAsync<Region>($"where company_id = {Token.CompanyID}");
            var data = list.ToTree1();
            response.Add("data", data);
            return response;
        }
        */


        /// <summary>
        /// 获取客户渠道
        /// </summary>
        /// <param name="operKey"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetGroup(string operKey)
        {
            string result = "OK";
            string msg = "";
            List<ExpandoObject> data = null;
            if (Token.TryParse(operKey, out Token token))
            {
                var condi = $"sc.company_id = {token.CompanyID}";
                var sql = @$"SELECT sc.group_id,sc.group_name
                            FROM info_supcust_group sc
                           where {condi} ;";
                data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            }
            else
            {
                result = "Failed";
                msg = "身份验证失败，请重新登录";
            }
            return Json(new { result, msg, data });
        }

        /// <summary>
        /// 获取客户等级
        /// </summary>
        /// <param name="operKey"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetRank(string operKey)
        {
            string result = "OK";
            string msg = "";
            List<ExpandoObject> data = null;
            if (Token.TryParse(operKey, out Token token))
            {
                var condi = $"sc.company_id = {token.CompanyID}";
                var sql = @$"SELECT sc.rank_id,sc.rank_name
                            FROM info_supcust_rank sc
                           where {condi} ;";
                data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            }
            else
            {
                result = "Failed";
                msg = "身份验证失败，请重新登录";
            }
            return Json(new { result, msg, data });
        }


        [HttpPost]
        public async Task<string> GetMaxSupcustNum([FromBody] dynamic data)
        {

            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);


            string querySql = @$"select supcust_no from info_supcust where company_id={companyID} and supcust_flag like '%C%' and supcust_no ~ '^\d+$' order by supcust_no::NUMERIC desc limit 1";


            dynamic queryResult = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);
            long supcust_no = 1;
            string s_supcust_no = supcust_no.ToString();
            if (queryResult != null && queryResult.supcust_no != "")
            {
                s_supcust_no = queryResult.supcust_no;
                supcust_no = Convert.ToInt64(s_supcust_no) + 1;
                s_supcust_no = supcust_no.ToString().PadLeft(s_supcust_no.Length, '0');
            }
            return s_supcust_no;
        }

        /// <summary>
        /// 客户档案保存
        /// </summary>
        /// <param name="data">{"operKey":"bIuYnVotW7J33Q8abhxoBzLx-HlRyzltkJ10Eqfo87IWpKgtDVFJm9qYJRiorwSh1LkCfI9INRirWKR56w_1J2JZyekvje5E_U86_7ZiDkuzlbBZXb47aQ~~","sup_name":"好利来",
        /// "boss_name":"张三","mobile":"15896854588","sup_group":"1","sup_rank":"1","region_id":"2","sup_addr":"仙林大道","other_region":"0/1/2","addr_lat":"88.8888"(纬度),"addr_lng":"120.001"(经度)} (supcust_id)  </param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> checkData([FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            string condiOldSup = "";
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            string mobile = data.mobile;
            string supcust_id = data.supcust_id;
            if (supcust_id.IsValid())
            {
                condiOldSup = $" and supcust_id<>{supcust_id} ";
            }
            if (!string.IsNullOrEmpty(mobile))
            {
                string sql = $@"select supcust_id from info_supcust where company_id = {companyID} and mobile='{mobile}'{condiOldSup}";
                var rec = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (rec != null && rec.Count > 0)
                {
                    result = "error";
                    msg = "已存在相同手机号的客户或供应商";
                }
            }
            return Json(new { result, msg });

        }
        [HttpPost]
        public async Task<JsonResult> SaveSupcustsInfo([FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            cmd.oper_id = operID;
            string supcust_id = "";
            string sup_name = "";
            string supcust_no = "";
            string mobile = "";
            string addr_lat = "";
            string addr_lng = "";
            string new_id = "";
            string acct_cust_id = "";
            bool relate_store = false;
            string license_no = "";
            //string status = data.Property("status")!=null? data.status : "1";
            string status = data.status ?? "1";
            string retail_wholesale_flag = "";
            string sup_door_photo = "";
            string py_str = "";
            List<string> sup_other_photo = new List<string>();
            string now = CPubVars.GetDateText(DateTime.Now);
            supcust_id = data.supcust_id;
            sup_name = data.sup_name;
            supcust_no = data.supcust_no;
            mobile = data.mobile;
            addr_lat = data.addr_lat;
            addr_lng = data.addr_lng;
            acct_cust_id = data.acct_cust_id;
            if (data.relate_store is not null) relate_store = data.relate_store;
            license_no = data.license_no;
            py_str = data.py_str ?? "";
            if (data.sup_other_photo != null)
            {
                foreach (string one_other_photo_base64 in data.sup_other_photo)
                {
                    sup_other_photo.Add(one_other_photo_base64);
                }
            }
            retail_wholesale_flag = data.retail_wholesale_flag;
            string condiSameName = $" and sup_name = '{sup_name}' ";
            string condiOldSup = "";

            /* string supcustNoSerial = data.supcustNoSerial;
             if (!supcust_id.IsValid() && supcustNoSerial == "True" && string.IsNullOrEmpty(supcust_no))
             {

                 string querySql = @$"select max(supcust_no::NUMERIC) supcust_no from info_supcust where company_id={companyID} and supcust_flag like '%C%' and supcust_no ~ '^\d+$' ";
                 dynamic queryResult = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);
                 data.supcust_no = Convert.ToInt64(queryResult.supcust_no) + 1;

             }*/

            if (supcust_id.IsValid())
            {
                condiOldSup = $" and supcust_id<>{supcust_id} ";
            }

            List<ExpandoObject> list = await CDbDealer.GetRecordsFromSQLAsync($"select supcust_id from info_supcust where company_id = {companyID} {condiSameName} {condiOldSup}", cmd);
            if (list.Count() > 0)
            {
                result = "error";
                msg = "已存在同名的客户或供应商";
            }

            //if (!string.IsNullOrEmpty(mobile))
            //{
            //    string sql = $@"select supcust_id from info_supcust where company_id = {companyID} and mobile='{mobile}'{condiOldSup}";
            //    var rec = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //    if ( rec!=null && rec.Count > 0)
            //    {
            //        result = "error";
            //        msg = "已存在相同手机号的客户或供应商";
            //    }
            //}
            if (status == "0" && !string.IsNullOrEmpty(supcust_id))
            {
                list = await CDbDealer.GetRecordsFromSQLAsync($"select supcust_id from arrears_balance where company_id = {companyID} and supcust_id={supcust_id} and abs(balance)>0.01", cmd);
                if (list.Count() > 0)
                {
                    result = "error";
                    msg = "该客户有欠款,不能停用";
                }
                list = await CDbDealer.GetRecordsFromSQLAsync($"select supcust_id from prepay_balance where company_id = {companyID} and supcust_id={supcust_id} and abs(balance)>0.01", cmd);
                if (list.Count() > 0)
                {
                    result = "error";
                    msg = "该客户有预收款,不能停用";
                }
            }
            // 包场模式客户新增包场虚拟仓库
            string add_store_sql = "";
            string relate_branch_id = "";
            // 新添加客户
            if(!supcust_id.IsValid())
            {
                if(relate_store)
                {
                    // 新建关联仓库
                    add_store_sql = @$"insert into info_branch(company_id,       branch_name,  allow_negative_stock, allow_negative_stock_order, branch_type, py_str, status, negative_stock_accordance, for_contract_seller)  
                                                              values ({companyID}, '{sup_name+"(包场仓)"}',    false,                 false      , 'store' ,  '{py_str+"(bcc)"}', '1', 'usable', false         ) RETURNING branch_id;"; 
                }
            }else //编辑客户
            {
                string select_relate_store = $@"select relate_store from info_supcust where company_id = {companyID} and supcust_id = {supcust_id} ";
                dynamic relateStoreId = await CDbDealer.Get1RecordFromSQLAsync(select_relate_store, cmd);

                // 已经存在关联仓库
                if (relateStoreId.relate_store != "")
                {
                    int branch_status = 0;
                    string update_relate_sname = "";
                    string select_relate_client = $@"select branch_name from info_branch where company_id = {companyID} and relate_client = {supcust_id};";
                    dynamic relateStoreName = await CDbDealer.Get1RecordFromSQLAsync(select_relate_client, cmd);
                    // 是否包场
                    if(relate_store) branch_status = 1;
                    // 修改包场客户名称同步更新包场仓名称
                    if (relateStoreName != null && !relateStoreName.branch_name.Contains(sup_name)) update_relate_sname = $@", branch_name = '{sup_name+"包场仓"}'";
                    // 更新包场仓库
                    add_store_sql = $@"update info_branch set status = {branch_status} {update_relate_sname} where company_id = {companyID} and branch_id = '{relateStoreId.relate_store}'  RETURNING branch_id;";
                } else
                {
                    // 是否包场
                    if(relate_store)
                    {
                         // 新建关联仓库
                        add_store_sql = @$"insert into info_branch(company_id,       branch_name,  allow_negative_stock, allow_negative_stock_order, branch_type, py_str, status, negative_stock_accordance, for_contract_seller)  
                                                            values ({companyID}, '{sup_name+"(包场仓)"}',    false,                 false , 'store'   ,  '{py_str+"(bcc)"}', '1', 'usable', false              ) RETURNING branch_id;";
                        
                    }
                }
            }
            
            if(add_store_sql.IsValid())
            {
                dynamic new_branch_id = await CDbDealer.GetRecordsFromSQLAsync(add_store_sql, cmd);
                relate_branch_id = new_branch_id[0].branch_id;
            }else
            {
                relate_branch_id = "";  // 没有使用包场
            }
            if (result == "OK")
            {
                CDbDealer db = new CDbDealer();
                db.AddFields(data, "sup_name,boss_name,sup_alias,mobile,sup_group,sup_rank,region_id,sup_addr,supcust_remark,other_region,py_str,acct_type,license_no,supcust_no,approve_status,charge_seller,mall_client_payment_type");
                db.AddField("company_id", companyID);
                string door_picture = data.sup_door_photo;

                if (door_picture.IsValid() && HuaWeiObs.IsB64(door_picture))
                {
                    var res = await process_sup_pic_return_dbstring(door_picture, companyID);
                    if (res.result == "OK")
                    {
                        db.AddField("sup_door_photo", res.urls[0]);
                    }
                }
                else
                {
                    db.AddField("sup_door_photo", door_picture);
                }
                //sup_other_photo
                if (sup_other_photo.Count() != 0)
                {
                    List<string> urls = new List<string>();
                    for (int i = 0; i < sup_other_photo.Count; i++)
                    {
                        string photoUrl = sup_other_photo[i];
                        if (HuaWeiObs.IsB64(photoUrl))
                        {
                            var res = await process_sup_pic_return_dbstring(photoUrl, companyID);
                            urls.Add(res.urls[0]);
                        }
                        else
                        {
                            urls.Add(photoUrl);
                        }
                    }
                    db.AddField("sup_other_photo", string.Join(",", urls));
                }
                else
                {
                    db.AddField("sup_other_photo", "");
                }
                if(relate_branch_id.IsValid())
                {
                    db.AddField("relate_store", relate_branch_id);
                }
                if (!supcust_id.IsValid())
                {
                    db.AddField("create_time", now);
                    db.AddField("creator_id", operID);
                }
                db.AddField("supcust_flag", "C");
                db.AddField("status", status);
                if (retail_wholesale_flag.IsValid())
                {
                    db.AddField("retail_wholesale_flag", retail_wholesale_flag);
                }

                if (addr_lat.IsValid() && addr_lng.IsValid())
                {
                    db.AddField("addr_lat", addr_lat);
                    db.AddField("addr_lng", addr_lng);
                    string s = $"ST_MakePoint({addr_lng},{addr_lat})";
                    db.AddField_OrigValue("addr_lnglat", s);
                }

                if (acct_cust_id.IsValid()) db.AddField("acct_cust_id", acct_cust_id);
                string sql = "";
                if (supcust_id.IsValid())
                {
                    sql = db.GetUpdateSQL("info_supcust", $"supcust_id='{supcust_id}' and company_id = '{companyID}' returning supcust_id;");
                }
                else
                {
                    //新建档案
                    sql = db.GetInsertSQL("info_supcust") + " returning supcust_id;";
                }
                ClientEditModel model = new ClientEditModel(cmd);
                dynamic origInfo = await model.GetRecord(cmd, (string)data.operKey, supcust_id);
                cmd.CommandText = sql;
                object ov = await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value) { 
                    new_id = ov.ToString();
                    if (relate_branch_id.IsValid())
                    {
						string updateStoreSql = $"update info_branch set relate_client = {new_id} where company_id = {companyID} and branch_id = {relate_branch_id}";
						await CDbDealer.GetRecordsFromSQLAsync(updateStoreSql, cmd);
					}
                }

				//插入前查询旧数据：params:GetRecord(CMySbCommand cmd, string operKey, string IdValue)
				
				

				model.DataItems[model.m_idFld].Value = new_id;
                model.DataItems[model.m_nameFld].Value = sup_name;
                string approveBrief = data.approve_brief ?? "";
                string logFlowId = data.flow_id ?? "";
                string receiverId = data.receiverId ?? "";
                string msgId = data.msg_id ?? "";
                await model.SaveLog(cmd, (string)data.operKey, origInfo, data.approve_flag.ToString(), approveBrief, logFlowId, receiverId, msgId);

            }
            return Json(new { result, msg, new_id, supcust_id, sup_door_photo });
        }


        [HttpPost]
        public async Task<JsonResult> SaveSuppliersInfo([FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);

            string supcust_id = "";
            string sup_name = "";
            string mobile = "";
            string new_id = "";
            string status = data.status ?? "1";

            string now = CPubVars.GetDateText(DateTime.Now);
            supcust_id = data.supcust_id;
            sup_name = data.sup_name;
            mobile = data.mobile;
            string condiSameName = $" and sup_name = '{sup_name}' ";
            string condiOldSup = "";

            if (supcust_id.IsValid())
            {
                condiOldSup = $" and supcust_id<>{supcust_id} ";
            }
            var list = await CDbDealer.GetRecordsFromSQLAsync($"select supcust_id from info_supcust where company_id = {companyID} {condiSameName}{condiOldSup}", cmd);
            if (list.Count() > 0)
            {
                result = "error";
                msg = "已存在同名的客户或供应商";
            }

            //if (!string.IsNullOrEmpty(mobile))
            //{
            //    string sql = $@"select supcust_id from info_supcust where company_id = {companyID} and TRIM(mobile)='{mobile}'{condiOldSup}";
            //    var rec = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //    if (rec!=null && rec.Count > 0)
            //    {
            //        result = "error";
            //        msg = "已存在相同手机号的客户或供应商";
            //    }
            //}

            if (result == "OK")
            {
                CDbDealer db = new CDbDealer();
                db.AddFields(data, "sup_name,boss_name,mobile,sup_addr,py_str");
                db.AddField("company_id", companyID);


                if (!supcust_id.IsValid())
                {
                    db.AddField("create_time", now);
                    db.AddField("creator_id", operID);
                }
                db.AddField("supcust_flag", "S");
                db.AddField("status", status);


                string sql = "";

                if (supcust_id.IsValid())
                {
                    sql = db.GetUpdateSQL("info_supcust", $"supcust_id='{supcust_id}' and company_id = '{companyID}';");
                }
                else
                {
                    sql = db.GetInsertSQL("info_supcust") + " returning supcust_id;";
                }
                cmd.CommandText = sql;
                object ov = await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value) new_id = ov.ToString();
            }
            return Json(new { result, msg, new_id, supcust_id });
        }




        public async Task<UploadImageResult> process_sup_pic_return_dbstring(string pictureBase64, string companyID)
        {
            var res = new UploadImageResult
            {
                result = "OK"
            };

            NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();

            pictureBase64 = pictureBase64.Replace("data:image/jpeg;base64,", "");

            if (string.IsNullOrEmpty(pictureBase64))
            {
                return res;
            }

            string dt = $"{DateTime.Today:yyyyMM}"; // 预防23点59分保存的情况，dt设为固定值
            string folderPath = $"uploads/{dt}/";

            string doorPicName = $"sup_pic_{companyID}_{CommonTool.GetTimeStamp()}_{new Random().Next(1, 100)}";
            string fileExtension = ".jpeg";
            string path = folderPath + doorPicName + fileExtension;

            using (MemoryStream stream = new MemoryStream(Convert.FromBase64String(pictureBase64)))
            {
                try
                {
                    await HuaWeiObs.Save(_httpClientFactory, stream, path);
                    logger.Info("in process_door_pic_return_dbstring:saved");
                    string door_picture_indb = $"/{dt}/{doorPicName}{fileExtension}";
                    res.urls.Add(door_picture_indb);
                    return res;
                }
                catch (Exception err)
                {
                    stream.Close();
                    logger.Info($"in process_door_pic_return_dbstring,err:{err}:door_picture_base64:" + pictureBase64);
                    res.result = "Error";
                    res.msg = "传门头出错:" + err;
                    return res;
                }
                finally
                {

                }
            }
        }


        [HttpGet]
        public async Task<JsonResult> GetOrdersByClient(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string regionID, string currentLat, string currentLng, string supcustFlag, string operRegions, bool isReceipted, string startDate, string endDate, bool isBoss, bool isAssignVanNecessary)
        {
            if (supcustFlag.IsInvalid()) supcustFlag = "C";
            var condition = $" and supcust_flag = '{supcustFlag}' and (s.status = '1' or s.status is null) ";
            //if (operRegions.IsValid())
            //{
            //    var regions = JsonConvert.DeserializeObject<int[]>(operRegions);
            //    if (regions.Length > 0)
            //        condition += " and (" + string.Join(" or ", regions.Select(x => $"other_region  like '%/{x}/%'")) + ") ";
            //}

            var distanceSQL = "";
            var orderBy = "";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%'  or s.mobile ilike '%{searchStr}%' ) ";
            if (regionID.IsValid()) condition += $" and  other_region ilike '%/{regionID}/%'";
            if (!isReceipted && currentLng.IsValid() && currentLat.IsValid())
            {
                distanceSQL += $",st_distance(ST_Transform(ST_SetSRID(addr_lnglat,4326)::geometry, 3857),ST_Transform('SRID=4326;POINT({currentLng} {currentLat})'::geometry, 3857)) as distance";
                orderBy += "order by distance ASC, sm.happen_time desc";
            }
            else
            {
                orderBy += "order by sm.happen_time desc";
            }

            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);

            if (startDate.IsValid() && endDate.IsValid())
            {
                queryTime +=
                    $" and sm.happen_time >= '{startDate} 00:00:00' and sm.happen_time <= '{endDate} 23:59:59' ";
            }
            var temp = isReceipted ? "m" : "oss";
            var queryRole = isBoss ? "" : $@" and ({temp}.senders_id like '%{Token.OperID}%' or sm.senders_id like '%{Token.OperID}%')";
            var assignCondi = "";
            if (isAssignVanNecessary) assignCondi = " and oss.order_status = 'zc' ";

            SQLQueue QQ = new SQLQueue(cmd);



            string sql_NoLimit;
            if (!isReceipted)
            {
                sql_NoLimit =
@$"SELECT
    s.supcust_id,
    s.addr_lng,
    s.addr_lat,
    s.sup_addr,
    sup_name,
    s.addr_lng,
    s.addr_lat,
    s.sup_addr,
    acct_cust_id,
    acct_cust_name,
    sm.sheet_id AS order_sheet_id,
    sm.sheet_no AS order_sheet_no,
    m.sale_sheet_id,
    m.sale_sheet_no,
    sm.seller_id,
    sm.make_brief,sm.order_source,
    io.oper_name as seller_name,
    (case when {isReceipted} then m.senders_name else case when oss.senders_name is null then sm.senders_name else oss.senders_name end end) as senders_name,
    oss.receipt_status,
    s.mobile AS sup_tel,
    sm.happen_time,
    case when m.sale_sheet_id is not null then m.total_amount else  sm.total_amount end total_amount,
    round((sm.total_amount -sm.now_pay_amount - sm.now_disc_amount)::numeric,4) left_amount,
    realtime->>'lastSaleTime' as last_sale_time,realtime->>'lastVisitTime' as last_visit_time,
    sup_addr {distanceSQL}
FROM
	sheet_sale_order_main sm
LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id = {Token.CompanyID}
LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and io.company_id = {Token.CompanyID}
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = '{Token.CompanyID}'
) A ON s.acct_cust_id = A.a_supcust_id
LEFT JOIN sheet_status_order oss ON sm.sheet_id = oss.sheet_id and oss.company_id = {Token.CompanyID}
LEFT JOIN 
(
    select order_sheet_id, sheet_id as sale_sheet_id,red_flag, sheet_no as sale_sheet_no,senders_id,senders_name ,approve_time,total_amount
    from sheet_sale_main where red_flag IS NULL and company_id = '{Token.CompanyID}' and happen_time>='{startDate}'
) m on sm.sheet_id = m.order_sheet_id
left join realtime_supcust r on sm.supcust_id=r.supcust_id and r.company_id={Token.CompanyID}                      
LEFT JOIN
(
    select d.sale_order_sheet_id,m.approve_time FROM op_move_to_van_detail d 
    LEFT JOIN op_move_to_van_main m on d.company_id = m.company_id and d.op_id = m.op_id
    WHERE d.company_id = '{Token.CompanyID}' and m.red_flag is null
) otd on otd.sale_order_sheet_id = sm.sheet_id
where sm.company_id = '{Token.CompanyID}'
and sm.approve_time is not null 
and sm.red_flag is null 
and {(isReceipted ? " m.sale_sheet_id is not null " : " (m.sale_sheet_id is null or m.approve_time is null) and oss.receipt_status is null ")}  
and m.red_flag is null 
{assignCondi}
{queryRole} 
{condition} 
{queryTime}
";

            }
            else
            {
                sql_NoLimit = @$"

SELECT
    s.supcust_id,
    sup_name,
    s.addr_lng,
    s.addr_lat,
    s.sup_addr,
    acct_cust_id,
    acct_cust_name,
    order_sheet_id,
    order_sheet_no,
    sm.sheet_id  sale_sheet_id,
    sm.sheet_no  sale_sheet_no,
    sm.seller_id,
    sm.make_brief,
    sm.order_source,
    io.oper_name as seller_name,
    senders_name,
	receipt_status,
    s.mobile AS sup_tel,
    sm.happen_time,
    sm.total_amount,
    round((sm.total_amount -sm.now_pay_amount - sm.now_disc_amount)::numeric,4) left_amount,
    realtime->>'lastSaleTime' as last_sale_time,realtime->>'lastVisitTime' as last_visit_time,
    sup_addr {distanceSQL}
FROM
	  sheet_sale_main    sm  
LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id = {Token.CompanyID}
LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and io.company_id = {Token.CompanyID}
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = {Token.CompanyID}
) A ON s.acct_cust_id = A.a_supcust_id
LEFT JOIN 
(
    select  m.sheet_id , sheet_no as order_sheet_no, receipt_status,   red_flag, oss.senders_id  from  sheet_sale_order_main m
	LEFT JOIN sheet_status_order oss ON m.sheet_id = oss.sheet_id and oss.company_id = {Token.CompanyID}
	where red_flag IS NULL and m.company_id = {Token.CompanyID} and happen_time>='2022-09-20'
) m  on sm.order_sheet_id= m.sheet_id
left join realtime_supcust r on sm.supcust_id=r.supcust_id and r.company_id={Token.CompanyID}                    

where sm.company_id = {Token.CompanyID} 
and sm.approve_time is not null 
and sm.red_flag is null 
and  sm.order_sheet_id is not null  
and m.red_flag is null   
{queryRole} 
{condition} 
{queryTime}
";

            }

            var sql = $"{sql_NoLimit}  {orderBy} limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);


            if (getTotal)
            {
                sql = $"select count(*) as total from ({sql_NoLimit}) t";
                QQ.Enqueue("count", sql);
            }

            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> data = null;
            string total = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                /* if (sqlName == "visitedSupcustIdListData")
                 {
                     dynamic dataList = CDbDealer.GetRecordsFromDr(dr, false);
                     string[] visitedSupcustIdList = new string[dataList.Count];
                     for (int i = 0; i < dataList.Count; i++)
                     {
                         visitedSupcustIdList[i] = dataList[i].supcust_id;
                     }
                     response.Add("visitedSupcustIdList", visitedSupcustIdList);
                 }*/
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);

                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", msg = "", data, total });

        }

        /// <summary>
        /// 删除前检查
        /// </summary>
        /// <param name="rowIDs"></param>
        /// <returns></returns>
        public async Task<string> CheckBeforeDeleteRecords(string companyID, string rowIDs)
        {
            return await ClientsViewModel.CheckBeforeDeleteRecords_client(cmd, rowIDs, companyID);

        }


        [HttpPost]
        public async Task<object> DeleteSupcustRecord([FromBody] dynamic data)
        {
            Object operKey = data;
            string rowIDs = data.rowIDs;
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);

            string err = await ClientsViewModel.CheckBeforeDeleteRecords_client(cmd, rowIDs, companyID);
            if (err != "")
            {
                return new JsonResult(new { result = "Error", msg = err });
            }
            cmd.CommandText = $"delete from info_supcust where supcust_id in ({data.rowIDs}) and company_id={companyID};";
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new { result = "OK", msg = "" });

        }
        [HttpGet]
        public async Task<object> GetSupcustSummary(string operKey, string startDate, string endDate, int pageSize, int startRow, string sellerId)
        {

            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sellerCondition = "";
            if (sellerId.IsValid()) sellerCondition += @$"AND seller_id = {sellerId}";
            string sql = $@"

SELECT
	(case when  sum(tb_amount.sum_sale_amount) is not  null then sum(tb_amount.sum_sale_amount) else 0 end)  sum_sale_amount,
	(case when  sum(tb_amount.sum_t_amount) is not  null then sum(tb_amount.sum_t_amount) else 0 end)  sum_t_amount,
	tb_visit_count.count AS visit_count,
	isp.sup_name ,
	isp.supcust_id
FROM
	info_supcust isp
	left JOIN (
	SELECT
		company_id,
	( CASE WHEN ssm.sheet_type = 'X' THEN SUM ( ssm.total_amount ) ELSE 0 END ) sum_sale_amount,
	( CASE WHEN ssm.sheet_type = 'T' THEN SUM ( ssm.total_amount ) ELSE 0 END ) sum_t_amount,
	supcust_id
FROM
	sheet_sale_main ssm
WHERE
	company_id = {companyID}
	AND ssm.happen_time BETWEEN '{startDate} 00:00'
	AND '{endDate} 23:59:58'
    AND ssm.red_flag is null
    {sellerCondition}
GROUP BY
	ssm.sheet_type,
	ssm.supcust_id,
	ssm.company_id 
	) tb_amount ON tb_amount.supcust_id = isp.supcust_id 
	AND tb_amount.company_id = isp.company_id 
LEFT JOIN ( SELECT COUNT ( 1 ),supcust_id FROM sheet_visit sv WHERE start_time BETWEEN '{startDate} 00:00' AND '{endDate} 23:59:58' AND company_id = {companyID} GROUP BY sv.supcust_id )  tb_visit_count ON tb_visit_count.supcust_id = isp.supcust_id 
WHERE
	isp.company_id = {companyID} 
GROUP BY
	isp.supcust_id,
	isp.sup_name,
	tb_visit_count.count

ORDER BY
		sum_sale_amount DESC ,sum_t_amount DESC,visit_count DESC
 limit {pageSize} offset {startRow}";
            cmd.CommandText = sql;
            string totalSQL = @$"

SELECT
	 SUM ( sum_sale_amount ) as sum_sale_amount, SUM ( sum_t_amount ) as sum_t_amount,
 (select count(1) from sheet_visit where company_id ={companyID} and sheet_visit.start_time  BETWEEN '{startDate} 00:00' 
	AND '{endDate} 23:59:58'  ) as visit_count
FROM
	(
	SELECT
		company_id,
	( CASE WHEN ssm.sheet_type = 'X' THEN SUM ( ssm.total_amount ) ELSE 0 END ) sum_sale_amount,
	( CASE WHEN ssm.sheet_type = 'T' THEN SUM ( ssm.total_amount ) ELSE 0 END ) sum_t_amount,
	supcust_id 
FROM
	sheet_sale_main ssm 
WHERE
	company_id = {companyID}
	AND ssm.happen_time BETWEEN '{startDate} 00:00' 
	AND '{endDate} 23:59:58' 
    AND ssm.red_flag is null
    {sellerCondition}
GROUP BY
	ssm.sheet_type,
	ssm.supcust_id,
	ssm.company_id 
	) tmp_sum";
            dynamic res = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            dynamic totalRes = await CDbDealer.Get1RecordFromSQLAsync(totalSQL, cmd);
            return new JsonResult(new { result = "OK", msg = "", data = res, total = totalRes });
        }

        /// <summary>
        /// 根据关键词搜索客户列表
        /// </summary>
        /// <param name="operKey">操作密钥</param>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="pageSize">每页数量</param>
        /// <param name="startRow">起始行</param>
        /// <returns>客户列表</returns>
        [HttpGet]
        public async Task<JsonResult> SearchClientList(string operKey, string keyword, int pageSize = 20, int startRow = 0)
        {
            string result = "OK";
            string msg = "";
            List<ExpandoObject> data = null;
            
            try
            {
                Security.GetInfoFromOperKey(operKey, out string companyID);
                
                var condi = $"company_id = {companyID} and supcust_flag = 'C' and status = 1";
                if (!string.IsNullOrEmpty(keyword))
                {
                    condi += $" and (sup_name ilike '%{keyword}%' or py_str ilike '%{keyword}%' or mobile like '%{keyword}%')";
                }
                
                var sql = $@"
                    SELECT 
                        supcust_id, 
                        sup_name, 
                        mobile
                    FROM info_supcust 
                    WHERE {condi}
                    ORDER BY sup_name
                    LIMIT {pageSize} OFFSET {startRow}";
                
                data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            }
            catch (Exception e)
            {
                result = "Failed";
                msg = "获取客户列表失败: " + e.Message;
            }
            
            return Json(new { result, msg, data });
        }

        

    }
}
