using ArtisanManage.Models;
using ArtisanManage.Services;
using HuaWeiObsController;

using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using System.Data.SqlTypes;

namespace ArtisanManage.Pages.BaseInfo
{
    public class VisitRecordModel : PageQueryModel
    { 
        public VisitRecordModel(CMySbCommand cmd) : base(Services.MenuId.visitRecord)

        {
            this.cmd = cmd;
            this.PageTitle = "拜访记录";
            DataItems = new Dictionary<string, DataItem>()
            {
                //zxk sm表不存在?   改成 v  happen_time ==> 两个日期的字段 2021/1/22 11点38分
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="v.start_time",CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="v.start_time",CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客户名称",LabelFld="sup_name",ButtonUsage="list",CompareOperator="=",DropDownWidth = "200",SqlFld="ss.supcust_id",
                SqlForOptions=CommonTool.selectSupcust } },
                {"other_region",new DataItem(){Title="片区",FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by order_index , region_id "
                }},
                {"group_id",new DataItem(){Title="渠道",FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                {"rank_id",new DataItem(){Title="等级", FldArea="divHead",LabelFld="rank_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_rank",
                    SqlForOptions ="select rank_id as v,rank_name as l from info_supcust_rank"}},

                {"seller_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="oper_name",ButtonUsage="list",CompareOperator="=",SqlFld="v.seller_id",
                    SqlForOptions ="select oper_id as v,oper_name as l from info_operator where company_id=~COMPANY_ID and is_seller and (status=1 or status is null)"}},
                {"day_id",new DataItem(){Title="日程",FldArea="divHead",LabelFld="day_name",ButtonUsage="list",CompareOperator="=",SqlFld="vd.day_id",
                    SqlForOptions ="select day_id as v,day_name as l from info_visit_day where company_id = ~COMPANY_ID"}},
                {"schedule_id",new DataItem(){Title="行程",FldArea="divHead",LabelFld="schedule_name",ButtonUsage="list",CompareOperator="=",SqlFld="vs.schedule_id",
                    SqlForOptions ="select schedule_id as v,schedule_name as l from info_visit_schedule where company_id = ~COMPANY_ID"}},
                
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates=true,
                      IdColumn="visit_id",
                      RowsHeight=60,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"visit_id", new DataItem(){Title="visit_id", Hidden=true, HideOnLoad=true, Linkable=true, Width="80"}},
                       {"oper_name",    new DataItem(){Title="业务员"}},
                       {"sup_name",     new DataItem(){Title="门店", SqlFld="ss.sup_name"}},
                       {"start_time",     new DataItem(){Title="到店时间", SqlFld="v.start_time"}},
                       {"end_time",   new DataItem(){Title="离店时间", SqlFld="v.end_time"}},
                        {"day_name",   new DataItem(){Title="日程名称", SqlFld="vd.day_name"}},
                       {"schedule_name",   new DataItem(){Title="行程",   Width="100",}},
                         {"interval",new DataItem(){Title="拜访时长",SqlFld= "EXTRACT(epoch from age(v.end_time,v.start_time))/60",Width="100",
                         FuncDealMe = interval =>
                            {
                                if(interval != null&& interval !="")
                                {
                                  return Math.Ceiling(Convert.ToDouble(interval)).ToString()+"分钟";
                                }
                                return "";
                            }
                         }},
                        {
                        "showcase_pictures",   new DataItem()
                        {
                            Width = "300",
                            SqlFld="case when v.work_content is not null then v.work_content else jsonb_build_array(v.door_picture)||v.showcase_pictures end",
                            Title = "拜访照片",
                            CellsAlign ="center",
                            FuncDealMe = jarray =>
                            {
                                if (jarray == ""||jarray =="[]"||jarray =="[null]") return "暂无照片";
                                if(jarray.IndexOf("work_content")!=-1){
                                dynamic res = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(jarray);
                                List<string> picDoms = new List<string>();
                                if(Convert.ToString(res[0].work_content).IndexOf("mandatory") !=-1) {  // 没想到更好的判断方法，如果有请更正一下
                                    foreach(string url in res[0].work_content.mandatory)
                                    {
                                       var imageDom =  $"<img height='60px' src='{HuaWeiObs.BucketLinkHref}/{url}' style=\"color:#409EFF;cursor:pointer;\" onclick=\"previewImage\" />";
                                        picDoms.Add(imageDom);
                                    }
                                };
                                

                                return  string.Join("",picDoms);

                                }
                                else
                                {
                                    dynamic res = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(jarray);
                                    List<string> picDoms = new List<string>();
                                    foreach(string url in res)
                                    {
                                        var imageDom =  $"<img height='60px' src='{HuaWeiObs.BucketLinkHref}/uploads{url}' style=\"color:#409EFF;cursor:pointer;\" onclick=\"previewImage\" />";
                                        picDoms.Add(imageDom);
                                    }
                                    return  string.Join("",picDoms);
                                }

                            }
                        }},
                         {
                        "maintain_review",   new DataItem()
                        {
                            SqlFld=" json_agg(odm.work_content)",
                            Title = "维护复核",                            
                           CellsAlign ="center",
                            FuncDealMe = jarray =>
                            {
                                if (!(jarray != ""&&jarray!="[]"&&jarray!="[null]")) return "暂无维护信息";
                                return  $"<div style=\"color:#409EFF;cursor:pointer;\" onclick=\"previewMaintainInfo\" />";
                                var div = new List<String>();
                                if (jarray.Contains("work_content"))
                                {
                                    var actions = Newtonsoft.Json.JsonConvert.DeserializeObject<List<dynamic>>(jarray);
                                    actions.ForEach(action=>{
                                        if (action.action.type == "photo")
                                        {
                                            foreach (var photo in (JArray) action.work_content.mandatory)
                                            {
                                                div.Add($"{HuaWeiObs.BucketLinkHref+"/"+photo.ToString()}");
                                            }

                                            if (action.work_content.optional != null)
                                            {
                                                foreach (var photo in (JArray) action.work_content.optional)
                                                {
                                                    div.Add($"{HuaWeiObs.BucketLinkHref+"/"+photo.ToString()}");
                                                }
                                            }
                                        }

                                    });

                                }
                                else
                                {
                                    var urls = Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(jarray);

                                    urls.ForEach(url=>{
                                        div.Add($"{HuaWeiObs.BucketLinkHref+"/uploads"+url}");
                                    });

                                }
                                return  $"<div style=\"color:#409EFF;cursor:pointer;\" onclick=\"previewImage(\'{String.Join(",",div)}\')\">查看</a>";

                            }
                        }},

                       {
    "address",   new DataItem(){Title="签到地址",Width="180",
                         // SqlFld="CONCAT('<a target=\"_blank\" href=\"./bmap?latitude=',v.latitude, '&longitude=' ,longitude,'\">查看</a>')"}},
                         SqlFld="CONCAT('<a href=\"#\">查看</a>')"}},
                         { "end_address",   new DataItem(){Title="签退地址",Width="180",
                        SqlFld="CONCAT('<a href=\"#\">查看</a>')" }},
                         //  SqlFld="CONCAT('<a target=\"_blank\" href=\"./bmap?latitude=',v.end_latitude, '&end_longitude=' ,longitude,'\">查看</a>')"}},
                       {"order_amount",   new DataItem(){Title="订单金额", SqlFld="v.order_amount",ShowSum=true}},
                       {"sale_amount",   new DataItem(){Title="销售金额",SqlFld="v.sale_amount",ShowSum=true}},
                       {"remark",   new DataItem()
                       {
                           Title="备注", 
                           SqlFld= "case when v.work_content is not null and v.work_content::text like '%brief%' then v.work_content::text else v.remark end",
                           FuncDealMe = str =>
                           {
                               
                               if (string.IsNullOrWhiteSpace(str) || !str.Contains("brief"))
                               {
                                   return str;
                               }
                               try
                               {
                                   var res = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(str);
                                   var remarkTemplate = new List<string>();

                                   foreach (var item in res)
                                   {
                                       if ((string)item?.action?.type == "brief")
                                       {
                                           string name = item?.action?.name ?? "";
                                           string content = item?.work_content ?? "";
                                           remarkTemplate.Add($"{name}:{content}");
                                       }
                                   }

                                   return string.Join(";", remarkTemplate);
                               }
                               catch
                               {
                                   return str;
                               }
                           }
                       }},
                       {"sign_distance",   new DataItem(){Title="签到距离", SqlFld="v.sign_distance"}},
                       {"end_sign_distance",   new DataItem(){Title="签退距离", SqlFld="v.end_sign_distance"}},
                       {"time_on_road",   new DataItem(){Title="在途时间",JSCellRender="timeOnRoadRender",Width="120",SqlFld= "EXTRACT(epoch from age(v.start_time,v.prev_end_time))/60",
                       FuncDealMe = interval =>
                            {
                                if(interval != null&& interval !="")
                                {
                                  return Math.Ceiling(Convert.ToDouble(interval)).ToString()+"分钟";
                                }
                                return "";
                            }} },
                       {"visit_score",   new DataItem(){Title="得分",ShowSum=true}}
                     },
                     QueryFromSQL=@",v.door_picture as door_pic, v.showcase_pictures as showcase_pics ,v.latitude as latitude, v.longitude as longitude,v.end_latitude as end_latitude, v.end_longitude as end_longitude
from (select *,lag(end_time) over (partition by seller_id, DATE(start_time) order by start_time) as prev_end_time from sheet_visit where company_id = ~COMPANY_ID) v
left join (select visit_id as odm_visit_id,company_id,work_content from op_display_maintain) odm on odm.company_id = ~COMPANY_ID and odm.odm_visit_id = v.visit_id
left join info_visit_day vd on vd.day_id = v.day_id and vd.company_id = ~COMPANY_ID
left join info_visit_schedule vs on vd.schedule_id=vs.schedule_id and vs.company_id=~COMPANY_ID
left join info_supcust ss on v.supcust_id = ss.supcust_id and ss.company_id = ~COMPANY_ID
left join info_operator o on v.seller_id = o.oper_id and v.company_id = ~COMPANY_ID
where v.company_id=~COMPANY_ID",
                     QueryOrderSQL=" group by(visit_id,oper_name,sup_name,start_time,end_time,day_name,schedule_name,interval,showcase_pictures,address,order_amount,sale_amount,remark,visit_score,door_pic,showcase_pics,latitude,longitude,end_longitude,end_latitude,v.work_content,v.sign_distance,v.end_sign_distance,v.prev_end_time) order by start_time::text desc "//不知为何加上::text后速度成倍提升
                  }
                }
            };
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class VisitRecordController : QueryController
    { 
        public VisitRecordController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            VisitRecordModel model = new VisitRecordModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }

        [HttpGet]
        public async Task<object> GetDataForDownloadPics(string operKey,string startTime,string endTime,string seller_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condition = @$"";
            if (startTime.IsValid())
            {
                condition += @$" and start_time > '{startTime}' ";
            }
            if (endTime.IsValid())
            {
                condition += $@" and start_time < '{endTime}' ";
            }
            if (seller_id.IsValid())
            {
                condition += $@" and seller_id = '{seller_id}' ";
            }
            string sql = @$"SELECT isp.sup_name, work_content,iop.oper_name ,start_time,end_time,door_picture,showcase_pictures 
                        from sheet_visit sv 
                        LEFT join info_supcust isp on isp.company_id = sv.company_id and  isp.supcust_id = sv.supcust_id 
                        LEFT join info_operator iop on iop.company_id = sv.company_id and  iop.oper_id = sv.seller_id 
                        where sv.company_id = '{companyID}' " + condition;
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //支持拜访模板图片导出 2024/1/9
            foreach (dynamic record in records)
            {
                List<string> showcasePics = new List<string>();
                string workContent = record.work_content;
                //假如用了拜访模板
                if (workContent.IsValid())
                {
                    dynamic workContentArrays = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(workContent);
                    //workContentOuter 外部的一层work_content
                    foreach (dynamic workContentOuter in workContentArrays)
                    {
                        dynamic workContentObj = workContentOuter["work_content"];
                    
                    dynamic optionalList = workContentObj["optional"];
                    if (optionalList != null )
                    {
                       foreach (string optionPic in optionalList)
                       {
                          showcasePics.Add("\"" + optionPic.Replace("uploads", "") + "\"");
                       }
                     }
              
                    dynamic mandatoryList =  workContentObj.mandatory;
                    foreach (string mandatoryPic in mandatoryList)
                    {
                        showcasePics.Add("\""+ mandatoryPic.Replace("uploads", "") + "\"");
                    }
                    }
                }
                //假如没用拜访模板
                else
                {
                    List<string> originShowcasePics = Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(record.showcase_pictures);
                    if (originShowcasePics == null )
                    {
                        continue;
                    }
                    foreach (var url in originShowcasePics)
                    {
                        showcasePics.Add("\"" + url.Replace("uploads", "") + "\"");
                    }
                }
                
                record.showcase_pictures = $@"
                    [{string.Join(',', showcasePics)}]
                ";
            }
            return records;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            VisitRecordModel model = new VisitRecordModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }
        [HttpPost]
        public async Task<IActionResult> GetVisitPhoto([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string visitID = data.visitID;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string sql = @$"select door_picture,showcase_pictures,work_content from sheet_visit where company_id = '{companyID}' and visit_id = '{visitID}'";
            dynamic options = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", data = options });
        }
        [HttpPost]
        public async Task<IActionResult> getMaintainInfo([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string visitID = data.visitID;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string sql = @$"select visit_time,page_oper_name,json_agg(json_build_object('reviewer_name',reviewer_name,'disp_sheet_id',disp_sheet_id,'sheet_no',sheet_no,'disp_template_id',disp_template_id,'month_maintain_times',month_maintain_times,'client_id',client_id,'sum_maintain_id',sum_maintain_id,'maintain_id',maintain_id,'maintain_times',maintain_times,'maintain_interval_days',maintain_interval_days,'disp_template_name',disp_template_name,'sup_name',sup_name,'oper_name',oper_name,'happen_time',happen_time,'maintain_need_review',maintain_need_review,'reviewer',reviewer,'review_time',review_time,'review_comment',review_comment,'review_refused',review_refused,'maintain_work_content',maintain_work_content))  as maintain
                            from (
                                select reviewer_name,odm.disp_sheet_id as disp_sheet_id,sheet_no,idt.disp_template_id as disp_template_id,month_maintain_times,maintain_interval_days,v.start_time as visit_time,m.supcust_id as client_id,sum_maintain_id,maintain_id,maintain_times,disp_template_name,sup_name,oper_name,odm.happen_time as happen_time,maintain_need_review,odm.reviewer as reviewer,odm.review_time as review_time,odm.review_comment as review_comment,odm.review_refused as review_refused,odm.work_content as maintain_work_content,
                                 (select ioo.oper_name from info_operator ioo where ioo.company_id = {companyID} and ioo.oper_id = {operID}) as page_oper_name
                                from sheet_visit v
                                left join op_display_maintain odm on odm.visit_id = v.visit_id
                                left join (select oper_name as reviewer_name,company_id,oper_id from info_operator) io on  io.company_id ='{companyID}' and io.oper_id = odm.reviewer
                                left join sum_display_maintain sdm on sdm.company_id = '{companyID}' and sdm.disp_sheet_id = odm.disp_sheet_id and to_char(sdm.months,'yyyy')::int = to_char(now(),'yyyy')::int and to_char(sdm.months,'mm')::int = to_char(now(),'mm')::int
                                left join (select maintain_need_review,disp_template_id,company_id,disp_template_name,month_maintain_times,maintain_interval_days from info_display_template ) idt on odm.disp_temp_id = idt.disp_template_id
                                left join (select sup_name,company_id,supcust_id,supcust_flag from info_supcust ) s on s.supcust_id =v.supcust_id and s.supcust_flag = 'C'
                                left join (select oper_name,company_id,oper_id from info_operator) o on o.company_id ='{companyID}' and o.oper_id = odm.oper_id
                                left join display_agreement_main m on m.company_id = '{companyID}' and m.sheet_id = odm.disp_sheet_id
                                where v.company_id = '{companyID}' and v.visit_id = '{visitID}' order by disp_sheet_id
                            )t group by visit_time,page_oper_name";
            dynamic options = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", data = options });
        }
        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            VisitRecordModel model = new VisitRecordModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
