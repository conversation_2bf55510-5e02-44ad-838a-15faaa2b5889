﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Dynamic;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;

namespace ArtisanManage.AppController
{


    [Route("AppApi/[controller]/[action]")]
    public class LogUserActionController : QueryController
    {             

        public LogUserActionController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpPost]
        public async Task<JsonResult> LogUserAction([FromBody] dynamic data)
        {
            return Json(new { result="OK", msg = "" });
            string operKey = data.operKey;
            JArray actions = data.actions;

          

            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string logMsg = $"in LogUserAction,{Newtonsoft.Json.JsonConvert.SerializeObject(data)}";
            MyLogger.LogFileMsg(logMsg, companyID);
            var sql = "";
            //string now = CPubVars.GetDateText(DateTime.Now);
            foreach (JObject action in actions)
            {
                string sAction = Newtonsoft.Json.JsonConvert.SerializeObject(action);
                string tm = action.GetValue("tm").ToString();
                sql += @$"insert into log_user_action (company_id,oper_id, happen_time,  action  )
                                          values     ({companyID},{operID},'{tm}',     '{action}');                                                    
             ";
            }

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            string result = "OK";
            return Json(new { result, msg = "" });


        }
    }
}





