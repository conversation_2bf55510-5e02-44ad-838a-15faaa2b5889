using System;
using ArtisanManage.Services;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;
using ArtisanManage.Pages.Sheets;
using System.Dynamic;
using System.Net.Http;
using Newtonsoft.Json;
using NPOI.POIFS.Crypt.Dsig;
using ArtisanManage.YingjiangMessage.Services;
using HuaWeiObsController;
using System.IO;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using Org.BouncyCastle.Asn1.Ocsp;
using static System.Runtime.InteropServices.JavaScript.JSType;
using NPOI.HPSF;
using ArtisanManage.AppController;
using NPOI.SS.Formula.Functions;
using NuGet.Protocol.Plugins;
using Microsoft.AspNetCore.Components.Forms;

namespace ArtisanManage.Pages.BaseInfo 
{
    public class ClientEditModel : PageFormModel
    { 
        public ClientEditModel(CMySbCommand cmd) : base(Services.MenuId.infoClient)
        {
            this.cmd = cmd;
            this.PageTitle = "客户档案";
            this.DocType = "client";
            this.LogChange = true;
            string dateSource = @"[{v:'01',l:'每月1号'},{v:'02',l:'每月2号'},{v:'03',l:'每月3号'},{v:'04',l:'每月4号'},{v:'05',l:'每月5号'},{v:'06',l:'每月6号'},{v:'07',l:'每月7号'},{v:'08',l:'每月8号'},{v:'09',l:'每月9号'},{v:'10',l:'每月10号'},{v:'11',l:'每月11号'},
            {v:'12',l:'每月12号'},{v:'13',l:'每月13号'},{v:'14',l:'每月14号'},{v:'15',l:'每月15号'},{v:'16',l:'每月16号'},{v:'17',l:'每月17号'},{v:'18',l:'每月18号'},{v:'19',l:'每月19号'},{v:'20',l:'每月20号'},{v:'21',l:'每月21号'},{v:'22',l:'每月22号'},{v:'23',l:'每月23号'},{v:'24',l:'每月24号'},{v:'25',l:'每月25号'},{v:'26',l:'每月26号'},{v:'27',l:'每月27号'},{v:'28',l:'每月28号'},]";
            DataItems = new Dictionary<string, DataItem>()//这里配置了客户信息的iframe
            {
                {"supcust_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"sup_name",new DataItem(){Title="客户名称",Width="390",Necessary=true,FldArea="divHead"}},
                {"py_str",new DataItem(){Title="助记码",FldArea="divHead"}},
                {"supcust_no",new DataItem(){Title="客户编号",FldArea="divHead"}},
                {"sup_alias",new DataItem(){Title="别名",FldArea="divHead"}},
                {"boss_name",new DataItem(){Title="老板姓名",Necessary=true,FldArea="divHead"}},
                {"mobile",new DataItem(){Title="联系电话",Necessary=true,FldArea="divHead"}},
                {"sup_addr",new DataItem(){Title="客户地址",FldArea="divHead"}},
                {"addr_lat",new DataItem(){Title="纬度",FldArea="divHead",CtrlType="hidden"}},
                {"addr_lng",new DataItem(){Title="经度",FldArea="divHead",CtrlType="hidden"}},
                {"region_id",new DataItem(){Title="片区",Necessary=true,FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",MaxRecords="500", DropDownHeight="200",DropDownWidth="150", TreePathFld="other_region", 
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by order_index , region_id"
                }},
                {"sup_group",new DataItem(){Title="渠道",FldArea="divHead",LabelFld="group_name",CtrlType="jqxInput",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",
                    SqlForOptions="select group_id as v,group_name as l from info_supcust_group"
                }},
                {"sup_rank",new DataItem(){Title="等级",FldArea="divHead",LabelFld="rank_name",CtrlType="jqxInput",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",
                    SqlForOptions="select rank_id as v,rank_name as l from info_supcust_rank"
                }},

                //{"supcust_flag",new DataItem(){Title="类型",FldArea="divHead",CtrlType="hidden", Value="C"}},
                {"status",new DataItem(){Title="状态",LabelFld="cls_status_name",FldArea="divHead",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", DropDownHeight="60",DropDownWidth="80",
                     Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}},
                {"create_time",new DataItem(){Title="添加时间",FldArea="divHead",SqlFld = "to_char(create_time,'yyyy-mm-dd HH:MM:SS')",Disabled=true} },
                {"cust_type",new DataItem(){Title="类型",LabelFld="cust_type_name",FldArea="divHead",LabelInDB=false,SaveToDB=false, SqlFld="case when acct_cust_id is null then 'client' else 'shop' end ", Value="client",Label="客户", ButtonUsage="list", DropDownHeight="80",DropDownWidth="150",
                    Source = "[{v:'client',l:'客户'},{v:'shop',l:'门店'}]"}},
                {"retail_wholesale_flag",new DataItem(){Title="销售方式",FldArea="divHead",  Value="w",Label="批发", ButtonUsage="list", DropDownHeight="80",DropDownWidth="150",
                    Source = "[{v:'r',l:'零售'},{v:'w',l:'批发'}]"}},
                {"acct_cust_id",new DataItem(){FldArea="divHead",Title="结算单位",LabelFld="acct_cust_name",CtrlType="jqxInput", ButtonUsage="list",SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where acct_cust_id is null"}},
                {"sup_door_photo",new DataItem(){ FldArea="divHead",Hidden=true,
                FuncDealMe = doorPhotoUrl =>
                {
                     return HuaWeiObs.BucketLinkHref+"uploads/"+doorPhotoUrl;
                }
                }},
                {"sup_other_photo",new DataItem(){ FldArea="divHead",Hidden=true,
                FuncDealMe = otherPhotosUrl =>
                {
                    List<string> urls = (List<string>)otherPhotosUrl.Split(",").ToList().Select(url =>
                    {
                        return HuaWeiObs.BucketLinkHref +"uploads/"+ url;
                    });
                   return string.Join(",",urls);
                 }}},
                 {"acct_type",new DataItem(){Title="结算类型",SqlFld="info_supcust.acct_type", GetOptionsOnLoad=true, LabelFld="acct_type_name",FldArea="divHead",ButtonUsage="list",QueryOnChange = true,MaxRecords="500", LabelInDB = false,
                     Source = @"[{v:'pay',l:'现结',condition:""(s.acct_type = 'pay'or s.acct_type is null)""},
                               {v:'arrears',l:'欠款',condition:""s.acct_type = 'arrears' ""},]"
                    }},
                {"acct_way_id",new DataItem(){Title="结算方式",LabelFld="acct_way_name", FldArea="divHead",ButtonUsage="list",QueryOnChange = true,MaxRecords="500",
                    SqlForOptions="SELECT info_acct_way.acct_way_id AS v,info_acct_way.acct_way_name AS l FROM info_acct_way where info_acct_way.acct_type='~CONDI_DATA_ITEM'",CONDI_DATA_ITEM="acct_type"}},
               

                {"charge_seller",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="charge_name",CtrlType="jqxInput",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",
                    SqlForOptions=$"select oper_id as v,oper_name as l from info_operator where COALESCE(status,'1')='1' "
                }},
                {"creator_id",new DataItem(){Title="创建人",FldArea="divHead",LabelFld="creator_name",CtrlType="jqxInput",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",
                    SqlForOptions=$"select oper_id as v,oper_name as l from info_operator where COALESCE(status,'1')='1' "
                }},
                 {"supcust_flag",new DataItem(){Title="往来类型",FldArea="divHead",ButtonUsage="list", Necessary=true, Value="C",Label="客户",Source = "[{v:'C',l:'客户'},{v:'CS',l:'供应商/客户'}]"}},
                {"license_no",new DataItem(){FldArea="divHead",Title="营业执照" }},
                 {"max_arrears",new DataItem(){Title="欠款额度",FldArea="divHead"}},
                 {"max_arrears_days",new DataItem(){Title="欠款天数",FldArea="divHead"}},
                 {"sup_order_index",new DataItem(){Title="显示顺序",FldArea="divHead"}},
                 {"supcust_remark",new DataItem(){Title="备注",FldArea="divHead"}},
                 {"mall_client_payment_type",new DataItem(){Title="商城支付方式",FldArea="divHead", Checkboxes = true , ButtonUsage="list", Source = "[{v:'online',l:'在线支付'},{v:'offline',l:'货到付款'}]"}},
                 {"allow_change_price",new DataItem(){FldArea="divHead",CtrlType="jqxCheckBox",Title="app可以改价",Value="True" }},
                 {"ck_relate_store",new DataItem(){FldArea="divHead",CtrlType="jqxCheckBox", Title="是否包场",Value="false",SaveToDB=false, SqlFld="case when (relate_store is not null and ib.relate_client is not null and coalesce(ib.status, 1) = 1) then true else false end"}},
				 {"relate_store",new DataItem(){Title="包场仓库ID",Hidden=true, FldArea="divHead"}},
				 {"approve_status",new DataItem(){Title="审核状态",CtrlType="hidden",HideOnLoad=true,FldArea="divHead"}},
                 {"arrears_order_start_date",new DataItem(){Title="对账起始日期",FldArea="divTestPage",
                 Source = dateSource,ButtonUsage="list",DropDownWidth="100"}},
                 {"arrears_order_end_date",new DataItem(){Title="对账截至日期",FldArea="divTestPage",
                 Source = dateSource,ButtonUsage="list",DropDownWidth="100"}},
                 {"arrears_get_start_date",new DataItem(){Title="收款起始日期",FldArea="divTestPage",
                 Source = dateSource,ButtonUsage="list",DropDownWidth="100"}},
                 {"arrears_get_end_date",new DataItem(){Title="收款截至日期",FldArea="divTestPage",
                 Source = dateSource,ButtonUsage="list",DropDownWidth="100"}},
            };
            

            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridAddress" ,new FormDataGrid(){

                   MinRows=3,AutoAddRow=true,
                   Height=300,
                  JSFixColumnCellRender = @"
function(row, column, value) { 
     return '<div style=""height:100%;display:flex;justify-content:center;align-items:center;"">' + (row+1) + '</div>';
}",
                  Columns = new Dictionary<string, DataItem>()
                   {
                       {"addr_id",new DataItem(){Title="id",Width="100",Hidden=true}},
                       {"addr_status",new DataItem(){Title="状态",Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]", ButtonUsage="list",DropDownHeight="80",DropDownWidth="80" }},
                    
                       {"addr_order",new DataItem(){Title="顺序"}},
                       {"receiver_name",new DataItem(){Title="收货人"}},
                       {"receiver_mobile",new DataItem(){Title="收货人电话"}},
                       {"province",new DataItem(){Title="省",Hidden=true}},  // 目前由于底层框架问题-_-!!，无下拉联动属性框架，此处进行隐藏，不让用户进行编辑，防止小程序设置值之后，电脑端二次设置覆盖成null
                       {"city",new DataItem(){Title="市",Hidden=true}},
                       {"area",new DataItem(){Title="区",Hidden=true}},
                       {"addr_desc",new DataItem(){Title="收货地址",Width="200",Necessary=true}},
                        
                   },

                  GridIdFld="addr_id",
                  GridIdFldIsSerial=true,
                  
                   TableName="info_client_address",
                   IdFld="client_id",
                   SelectFromSQL=@"from info_client_address where client_id='~ID' order by addr_order"
                }}

            };

            
            m_idFld = "supcust_id"; m_nameFld = "sup_name";
            m_tableName = "info_supcust";
            m_selectFromSQL = @"from info_supcust  
left join (select region_id,region_name from info_region where company_id=~COMPANY_ID) r on info_supcust.region_id = r.region_id         
left join (select supcust_id as s_id,sup_name as acct_cust_name from info_supcust where company_id=~COMPANY_ID ) ac on ac.s_id = info_supcust.acct_cust_id
left join (select oper_id  as charge_id,oper_name as charge_name from info_operator where company_id=~COMPANY_ID)charge on charge.charge_id = info_supcust.charge_seller
left join (select group_id,group_name from info_supcust_group where company_id=~COMPANY_ID) g on g.group_id = info_supcust.sup_group
left join (select oper_id, oper_name creator_name from info_operator where company_id=~COMPANY_ID) io on io.oper_id = info_supcust.creator_id and (status is null or status = 1)
left join (select status, relate_client from info_branch where company_id = ~COMPANY_ID) ib on ib.relate_client = info_supcust.supcust_id
left join (select rank_id,rank_name from info_supcust_rank where company_id=~COMPANY_ID) sr on sr.rank_id = info_supcust.sup_rank 

    LEFT JOIN  info_acct_way ON info_supcust.acct_way_id = info_acct_way.acct_way_id  where info_supcust.supcust_id='~ID'     ";
                                
        }
		
		//left join (select supcust_id as s_id,sup_name as acct_cust_name from info_supcust where company_id=~COMPANY_ID ) ac on ac.s_id = info_supcust.acct_cust_id
		public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            if (DataItems["supcust_id"].Value == "")
            {
                DataItems["creator_id"].Value = OperID;
                DataItems["creator_id"].Label = OperName;
            }
            string supcustNoSerial = "";
            if (JsonCompanySetting != "")
            {
                dynamic set = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
                supcustNoSerial = set.supcustNoSerial ?? "false";
                string clientBossNecessary = set.clientBossNecessary ?? "true";
                if (clientBossNecessary.ToLower() == "false")
                {
                    DataItems["boss_name"].Necessary=false;
                }
                string clientMobileNecessary = set.clientMobileNecessary ?? "true";
                if (clientMobileNecessary.ToLower() == "false")
                {
                    DataItems["mobile"].Necessary = false;
                }

                string doorPicNecessary = set.doorPicNecessary ?? "true";
                if (doorPicNecessary.ToLower() == "false")
                {
                    DataItems["boss_name"].Necessary = false;
                }

                string clientLocationNecessary = set.clientLocationNecessary ?? "true";
                if (clientLocationNecessary.ToLower() == "false")
                {
                    DataItems["region_id"].Necessary = false;
                }

                string clientGroupNecessary = set.clientGroupNecessary ?? "false";
                if (clientGroupNecessary.ToLower() == "true")
                {
                    DataItems["sup_group"].Necessary = true;
                }
                string clientLevelNecessary = set.clientLevelNecessary ?? "false";
                if (clientLevelNecessary.ToLower() == "true")
                {
                    DataItems["sup_rank"].Necessary = true;
                }
                string clientRelateSeller = set.clientRelateSeller ?? "false";
                if (clientRelateSeller.ToLower() == "false")
                {
                    DataItems["charge_seller"].Hidden = true;
                }
                

            }
            string supcust_id = CPubVars.RequestV(Request, "supcust_id");
            if (supcust_id == "" && supcustNoSerial == "True")
            {

                string querySql = @$"select supcust_no from info_supcust where company_id={company_id} and supcust_flag like '%C%' and supcust_no ~ '^\d+$' order by supcust_no::NUMERIC desc limit 1";


                dynamic queryResult = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);
                long supcust_no = 1;
                string s_supcust_no = supcust_no.ToString();
                if (queryResult != null && queryResult.supcust_no != "") {
                    s_supcust_no = queryResult.supcust_no;
                    supcust_no = Convert.ToInt64(s_supcust_no) + 1;
                    s_supcust_no = supcust_no.ToString().PadLeft(s_supcust_no.Length,'0');
                }
                DataItems["supcust_no"].Value = s_supcust_no;
            }
            if (!string.IsNullOrEmpty(DataItems["mall_client_payment_type"].Value))
            {
                var label = DataItems["mall_client_payment_type"].Value.Replace("online", "在线支付").Replace("offline", "货到付款");
                DataItems["mall_client_payment_type"].Label = label;
            }

        }
        public async Task OnGet()
        {
            string now = CPubVars.GetDateText(DateTime.Now);
            string supcust_id = CPubVars.RequestV(Request,"supcust_id");
            if (supcust_id == "") DataItems["create_time"].Value = now;
            
            await InitGet(cmd);
            
            // DataItems["make_arrears_order_start_date"].Label = "每月" + DataItems["make_arrears_order_start_date"].Value + "号";
        }
        //public override async Task LogSave(CMySbCommand cmd, string operKey, string IdValue, dynamic origInfo, string approve_brief, string approve_status, string approve_flag, string obj_name, string receiver_id, string flow_id, string from_flag, string msg_id)
        //{
        //    await SaveLog(cmd,operKey, IdValue, origInfo, approve_brief, approve_status, approve_flag, obj_name, receiver_id, flow_id, from_flag, msg_id);
        //}
      /*  public override Dictionary<string, dynamic> GetGridVariances(Dictionary<string, List<ExpandoObject>> newGrids, Dictionary<string, List<ExpandoObject>> oldGrids)
        {
            Dictionary<string, dynamic> variances = new Dictionary<string, dynamic>();
            Dictionary<string,dynamic> oldGridDic= new Dictionary<string,dynamic>();
            Dictionary<string, dynamic> newGridDic= new Dictionary<string,dynamic>();
            foreach (dynamic oldGridItem in oldGrids)
            {
                string key = oldGridItem["addr_id"];
                oldGridDic.Add(key, oldGridItem );
                    
            }
            foreach (dynamic newGridItem in newGrids)
            {
                string key = newGridItem["addr_id"];
                newGridDic.Add(key, newGridItem);
            }
            foreach(KeyValuePair<string,dynamic> kv in oldGridDic)
            {
                string key = kv.Key;
                string oldAddr = kv.Value["addr_desc"];
                string variKey ="addr_desc" + kv.Value["addr_order"];
                if (newGridDic.ContainsKey(key))
                {
                    string newAddr = newGridDic[key]["addr_desc"];
                    if(newAddr != oldAddr)
                    {
                        Dictionary<string, string> variance = new Dictionary<string, string>();
                        variance.Add("newValue", newAddr);
                        variance.Add("oldValue", oldAddr);
                        variances.Add(variKey, variance);
                    }
                    newGridDic.Remove(key);
                }
                else
                {
                    Dictionary<string, string> variance = new Dictionary<string, string>();
                    variance.Add("newValue", "");
                    variance.Add("oldValue", kv.Value);
                    variances.Add(variKey, variance);
                }
            }
            if (newGridDic.Count > 0)
            {
                foreach(KeyValuePair<string,dynamic> kv in newGridDic)
                {
                    string newAddr = kv.Value["addr_desc"];
                    string variKey ="addr_desc" + kv.Value["addr_order"];
                    Dictionary<string, string> variance = new Dictionary<string, string>();
                    variance.Add("newValue", newAddr);
                    variance.Add("oldValue", "");
                    variances.Add(variKey, variance);
                }
            }
            return variances;
        }
    */
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class ClientEditController : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public ClientEditController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }

        [HttpPost]
        public async Task<JsonResult> SaveSupcustPiaoZhengTong([FromBody] dynamic data)
        {
            TicketAccessService ticketAccessService = new TicketAccessService();
            
            
            var resp = await ticketAccessService.AddSupcust(_httpClientFactory,(string)data.token, (string)data.oentityCode, (string)data.bossName, (string)data.mobile, (string)data.supName, (string)data.address);
            return Json(new { result = "OK", resp });

        }
        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues,string condiDataItem)
        //这个玩意处理对popitem中所有下拉框的查询请求
        {
            ClientEditModel model = new ClientEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues, condiDataItem);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            ClientEditModel model = new ClientEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }

        [HttpPost]
        public async Task<JsonResult> CheckData([FromBody] dynamic request)
        {
            string mobile = request.mobile;
            var isNewRecord = request.isNewRecord.ToString();
            string supcust_id = request.supcust_id;
            Security.GetInfoFromOperKey((string)request.operKey, out string companyID, out string operID);
            
            if (!string.IsNullOrEmpty(mobile))
            {
                string condiOldCondi = "";
                if (isNewRecord != "True")
                {
                    condiOldCondi = $@"and supcust_id <> {supcust_id}";
                }
                string sql = $@"select supcust_flag from info_supcust where company_id={companyID} and TRIM(mobile)='{mobile}' {condiOldCondi} ";
                var rec = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (rec != null && rec.Count > 0)
                {
                    return new JsonResult(new { result = "Error", msg = "已存在相同手机号的客户或供应商" });
                }
            }
            return new JsonResult(new { result = "OK", msg = "" });
        }
        [HttpPost]
        public async Task<JsonResult> GetDataSet([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            string acct_cust_id = data.acct_cust_id;
            string sql = @$"select arrears_order_start_date,arrears_order_end_date,arrears_get_start_date,arrears_get_end_date  from info_supcust where supcust_id = {acct_cust_id} and company_id = {companyID}";
            dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if( ret != null)
            {
                if(string.IsNullOrEmpty((string)ret.arrears_order_start_date)
                    && string.IsNullOrEmpty((string)ret.arrears_order_start_date))
                {
                    return new JsonResult(new { result = "EMPTY", msg = "" });
                }
                return new JsonResult(new { result = "OK", msg = "", ret = ret });
            }
            else
            {
                return new JsonResult(new { result = "ERROR", msg = "" });
            }
            
        }

        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic request) 
        {
            string status = request.status;
            string supcust_id = request.supcust_id;
            string supcust_no = request.supcust_no;
            string mobile = request.mobile;
            string py_str = request.py_str;

            string sup_name = request.sup_name;
            ClientEditModel model = new ClientEditModel(cmd);
            Security.GetInfoFromOperKey((string)request.operKey, out string companyID,out string operID);
            request.company_id = companyID;

            #region 对账日期设置
            // 对账起止日期编辑
            if (string.IsNullOrEmpty((string)request.arrears_order_start_date) ^ string.IsNullOrEmpty((string)request.arrears_order_end_date))
            {
                return new JsonResult(new { result = "Error", msg = "必须同时设置对账起止日期" });
            }
            if (!string.IsNullOrEmpty((string)request.arrears_order_start_date) && !string.IsNullOrEmpty((string)request.arrears_order_end_date))
            {
                if (int.Parse((string)request.arrears_order_start_date) > int.Parse((string)request.arrears_order_end_date))
                {
                    return new JsonResult(new { result = "Error", msg = "对账起始日期必须早于截止日期" });
                }

                // request.make_arrears_order_date = request.make_arrears_order_start_date + "," + request.make_arrears_order_end_date;
            }
            
            
            // 收款起止日期编辑
            if (string.IsNullOrEmpty((string)request.arrears_get_start_date) ^ string.IsNullOrEmpty((string)request.arrears_get_end_date))
            {
                return new JsonResult(new { result = "Error", msg = "必须同时设置收款起止日期" });
            }
            if(!string.IsNullOrEmpty((string)request.arrears_get_start_date) && !string.IsNullOrEmpty((string)request.arrears_get_end_date))
            {
                if (int.Parse((string)request.arrears_get_start_date) > int.Parse((string)request.arrears_get_end_date))
                {
                    return new JsonResult(new { result = "Error", msg = "收款起始日期必须早于截止日期" });
                }
                // request.get_arrears_order_date = request.get_arrears_order_start_date + "," + request.get_arrears_order_end_date;
            }

            // 检查是否为结算单位，下属若有其他门店，一并设置
            if(supcust_id != "")
            {
                string sup_ids = $"select string_agg(supcust_id::text, ',') as sub_ids from info_supcust where acct_cust_id = {supcust_id} and company_id = {companyID}";
                dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(sup_ids, cmd);
                if (ret != null && !string.IsNullOrEmpty((string)request.arrears_get_start_date) && !string.IsNullOrEmpty((string)request.arrears_order_start_date))
                {
                    // 结算单位找到下属门店
                    if (!string.IsNullOrEmpty((string)ret.sub_ids))
                    {
                        string updateDateSql = @$"update info_supcust set 
                        arrears_get_start_date = '{request.arrears_get_start_date}',
                        arrears_get_end_date = '{request.arrears_get_end_date}',
                        arrears_order_start_date = '{request.arrears_order_start_date}',
                        arrears_order_end_date = '{request.arrears_order_end_date}'
                        where company_id = {companyID} and supcust_id in ({ret.sub_ids})";

                        cmd.CommandText = updateDateSql;

                        await cmd.ExecuteNonQueryAsync();
                    }

                }

            }

            #endregion

            cmd.CommandText  = $"select supcust_flag from info_supcust  where company_id={companyID}  and  sup_name='{request.sup_name}';";
            var supcust_flag = await cmd.ExecuteScalarAsync();
            var isNewRecord = request.isNewRecord.ToString();
           
            if (supcust_flag!=null&&isNewRecord == "True")
            {
                if (supcust_flag.ToString() == "C")
                {
                    return new JsonResult(new {result = "Error", msg = "客户档案已存在同名的记录"});
                }
                else if (supcust_flag.ToString() == "S")
                {
                    return new JsonResult(new {result = "Error", msg = "供应商档案已存在同名的记录"});
                }
                else if (supcust_flag.ToString() == "W")
                {
                    return new JsonResult(new { result = "Error", msg = "费用单位档案已存在同名的记录" });
                }
            }
            if (sup_name.Contains(','))
            {
                return new JsonResult(new { result = "Error", msg = "【客户名称】请将英文逗号改为中文逗号" });
            }
            //if (!string.IsNullOrEmpty(mobile))
            //{
            //    string condiOldCondi = "";
            //    if (isNewRecord != "True")
            //    {
            //        condiOldCondi = $@"and supcust_id <> {supcust_id}";
            //    }
            //    string sql = $@"select supcust_flag from info_supcust where company_id={companyID} and TRIM(mobile)='{mobile}'{condiOldCondi} ";
            //    var rec = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            //    if (rec != null && rec.Count > 0)
            //    {
            //        return new JsonResult(new { result = "Error", msg = "已存在相同手机号的客户或供应商" });
            //    }
            //}

            if (!string.IsNullOrEmpty(supcust_id) 
                && ! ",4427,".Contains(","+companyID+",")  //江山客户提出异议，暂时放行
            )//原来没有结算单位，拦截了自身存在欠款还想加结算单位的情况（结算单位从A切到B，或结算单位从有然后删除，这两种情况还没考虑）
            {
                SQLQueue QQ = new SQLQueue(cmd);
                string sql = $@"(select supcust_id, '欠款' as type  from arrears_balance where company_id = {companyID} and supcust_id={supcust_id} and abs(balance)>0.01)
                            union all
                            (select supcust_id,'预收款' as type from prepay_balance where company_id = {companyID} and supcust_id={supcust_id} and abs(balance)>0.01)";
                QQ.Enqueue("balance", sql);
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                dynamic balance = null;
                while (QQ.Count > 0)
                {
                    string sqlName = QQ.Dequeue();
                    if (sqlName == "balance")
                    {
                        balance = CDbDealer.Get1RecordFromDr(dr, false);
                    }
                }
                QQ.Clear();


                if(balance != null)
                {
                    if (status == "0")
                    {
                        return new JsonResult(new { result = "Error", msg = $"该客户有{balance.type},不能停用" });
                    }
                    if (request.acct_cust_id != "")
                    {
                        return new JsonResult(new { result = "Error", msg = $"该客户有{balance.type},不能添加结算单位" });
                    }
                }
                
            }
            

            string dt = $"{DateTime.Today:yyyyMM}";
            string folderPath = $"uploads/{dt}/";

            string sup_door_photo = request.sup_door_photo;
            //var ddd=model.DataItems["item_id"].Value;
            if (!HuaWeiObs.IsB64(sup_door_photo) && sup_door_photo.StartsWith(HuaWeiObs.BucketLinkHref))
            {
                sup_door_photo = sup_door_photo.Replace($"{HuaWeiObs.BucketLinkHref}/uploads", "");
                request.sup_door_photo = sup_door_photo;
            }
            else
            {
                sup_door_photo = sup_door_photo.Replace("data:image/jpeg;base64,", "");

                string doorPicName = $"sup_pic_{companyID}_{CommonTool.GetTimeStamp()}_{new Random().Next(1, 100)}";
                string fileExtension = ".jpeg";
                string path = folderPath + doorPicName + fileExtension;

                using (MemoryStream stream = new MemoryStream(Convert.FromBase64String(sup_door_photo)))
                {
                    try
                    {
                        await HuaWeiObs.Save(_httpClientFactory, stream, path);
                    }
                    finally
                    {
                        stream.Close();
                    }
                    string door_picture_indb = $"/{dt}/{doorPicName}{fileExtension}";
                    request.sup_door_photo = door_picture_indb;
                }
            }

            string sup_other_photo = request.sup_other_photo;
            if(sup_other_photo != null)
            {
                List<string>photos = sup_other_photo.Split("|").ToList();
                List<string> photos_in_db = new List<string>();
                foreach(string other_photo in photos)
                {
                    //var ddd=model.DataItems["item_id"].Value;
                    if (!HuaWeiObs.IsB64(other_photo) && other_photo.StartsWith(HuaWeiObs.BucketLinkHref)){
                        string photo_url = other_photo.Replace($"{HuaWeiObs.BucketLinkHref}/uploads", "");
                        photos_in_db.Add(photo_url);
                    }
                    else{
                        string other_photo_base64 = other_photo.Replace("data:image/jpeg;base64,", "");

                        string otherPicName = $"sup_pic_{companyID}_{CommonTool.GetTimeStamp()}_{new Random().Next(1, 100)}";
                        string fileExtension = ".jpeg";
                        string path = folderPath + otherPicName + fileExtension;

                        using (MemoryStream stream = new MemoryStream(Convert.FromBase64String(other_photo_base64)))
                        {
                            try
                            {
                                await HuaWeiObs.Save(_httpClientFactory, stream, path);
                            }
                            finally
                            {
                                stream.Close();
                            }
                            string other_picture_indb = $"/{dt}/{otherPicName}{fileExtension}";
                            photos_in_db.Add(other_picture_indb);
                        }
                    }
                }
                request.sup_other_photo = string.Join(",", photos_in_db);
            }
      


            if (request.supcust_id!=""&& request.supcust_id == request.acct_cust_id) return new JsonResult(new { result = "Error", msg = "不能既是门店又是结算单位" });
            if(request.acct_cust_id != ""&&request.supcust_id!="")
            {
                string querySql = $@"select * from info_supcust where company_id = {companyID} and acct_cust_id = {request.supcust_id}";
                List<ExpandoObject> lstSons = await CDbDealer.GetRecordsFromSQLAsync(querySql, cmd);
                if(lstSons != null && lstSons.Count>0)
                {
                    string sons = "";
                    foreach(dynamic son in lstSons)
                    {
                        if(sons!="") sons += ",";
                        sons += son.sup_name;
                    }
                   
                    return new JsonResult(new { result = "Error", msg = $@"已是其他门店的结算单位，不能再设置结算单位。门店:{sons}" });
                }
            }

            if (!string.IsNullOrEmpty(supcust_id)) //现在只能实现已创建的客户在保存的时候联动欠款策略表更新，新增客户没有supcust_id，无法联动
            {
                string max_arrears = request.max_arrears == "" ? "null" : request.max_arrears;
                string max_arrears_days = request.max_arrears_days == "" ? "null" : request.max_arrears_days;//没填写记作null
                string exsql = "";
                if (status == "1")
                {
                    if (max_arrears == "null" && max_arrears_days == "null")
                    {
                        //欠款额度和天数都是空，需要从arrears_strategy_client删除这个客户数据
                        exsql = $"delete from arrears_strategy_client where company_id={companyID} and supcust_id = {supcust_id}";
                    }
                    else
                    {
                        //客户状态正常，并且填写了欠款额度或天数，就要更新arrears_strategy_client表，更新旧数据或者插入新的
                        exsql = $@"INSERT INTO arrears_strategy_client (company_id,supcust_id, max_arrears, max_arrears_days)
                                VALUES ({companyID},{supcust_id}, {max_arrears}, {max_arrears_days})
                                ON CONFLICT(company_id,supcust_id) DO UPDATE SET max_arrears = {max_arrears},max_arrears_days = {max_arrears_days};";
                    }
                }
                else if (status == "0")
                {//如果停用客户的话，从arrears_strategy_client删除这条数据
                    exsql = $"delete from arrears_strategy_client where company_id={companyID} and supcust_id = {supcust_id}";
                }
                if (!string.IsNullOrEmpty(exsql))
                {
                    cmd.CommandText = exsql;
                    await cmd.ExecuteNonQueryAsync();
                }
            }
            

            
            cmd.CommandText = $@"SELECT rights FROM info_operator o 
    left join info_role r on o.company_id = r.company_id and o.role_id = r.role_id
where o.company_id = {companyID} and o.oper_id = {operID}";
            dynamic jsonRights = await cmd.ExecuteScalarAsync();
            request.approve_brief = "";
            request.receiver_id = "";
            request.flow_id = "";
            request.msg_id = "";
            string approveFlag = "";
            dynamic rights = Newtonsoft.Json.JsonConvert.DeserializeObject(jsonRights);
            if (rights == null) 
                return new JsonResult(new { result = "ERROR", msg = "获取权限失败" });
            
            if(rights.info.infoClient.approve == "false")
            {
                if(request.approve_status == "wait approve")
                {
                    return new JsonResult(new { result = "ERROR", msg = "该档案上次编辑未审核，请先审核再编辑" });
                }
                model.DataItems["approve_status"].Value = "wait approve";
                request.approve_status = "wait approve";
                if (request.supcust_id =="")
                {
                    if(rights.info.infoClient.create == "false")
                    {
                        return new JsonResult(new { result = "ERROR", msg = "暂无新建权限" });
                    }
                    approveFlag = "CREATE";
                }
                else
                {
                    if (rights.info.infoClient.edit == "false")
                    {
                        return new JsonResult(new { result = "ERROR", msg = "暂无编辑权限" });
                    }
                    approveFlag = "EDIT";
                }
                    
            }
            else
            {
                dynamic docInfo = null;
                if (request.approve_status == "wait approve")
                {
                        string docSql = $@"SELECT flow_id,oper_action FROM document_change_log 
                where company_id = {companyID} and obj_name ='客户档案' and obj_id = {request.supcust_id}";
                    docInfo =await CDbDealer.Get1RecordFromSQLAsync(docSql, cmd);
                    if (docInfo != null)
                    {
                        if (docInfo.oper_action == "CREATE") approveFlag = "APPROVED_FROM_CREATE";
                        else approveFlag = "APPROVED_FROM_EDIT";
                        request.flow_id = docInfo.flow_id;
                    }
                   // else
                   // {
                   //   return new JsonResult(new { result = "ERROR", msg = "客户档案历史记录查询失败" });
                   // }
                }

                if(docInfo==null)
                {
                    if (request.supcust_id == "")
                    {
                        if (rights.info.infoClient.create == "false")
                        {
                            return new JsonResult(new { result = "ERROR", msg = "暂无新建权限" });
                        }
                        approveFlag = "CREATE_AND_APPROVED";
                    }
                    else
                    {
                        if (rights.info.infoClient.edit == "false")
                        {
                            return new JsonResult(new { result = "ERROR", msg = "暂无编辑权限" });
                        }
                        approveFlag = "EDIT_AND_APPROVED";
                    }
                }
                
                model.DataItems["approve_status"].Value = "";
                request.approve_status = "";
                     
            }

            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            try
            {
				// 包场模式客户新增包场虚拟仓库
				bool ck_relate_store = request.ck_relate_store;
				string add_store_sql = "";
				// 新添加客户
				if (!supcust_id.IsValid())
				{
					if (ck_relate_store)
					{
						// 新建关联仓库
						add_store_sql = @$"insert into info_branch(company_id,       branch_name,  allow_negative_stock, allow_negative_stock_order, branch_type, py_str, status, negative_stock_accordance, for_contract_seller)  
                                                              values ({companyID}, '{sup_name + "(包场仓)"}',    false,                 false   , 'store'   , '{py_str + "(bcc)"}' , '1', 'usable', false           ) RETURNING branch_id;";
					}
				}
				else //编辑客户
				{
					string select_relate_store = $@"select relate_store from info_supcust where company_id = {companyID} and supcust_id = {supcust_id}; ";
					dynamic relateStoreId = await CDbDealer.Get1RecordFromSQLAsync(select_relate_store, cmd);

					// 已经存在关联仓库
					if (relateStoreId.relate_store != "")
					{
						int branch_status = 0;
						string update_relate_sname = "";
						string select_relate_sup = $@"select branch_name from info_branch where company_id = {companyID} and relate_client = {supcust_id};";
						dynamic relateStoreName = await CDbDealer.Get1RecordFromSQLAsync(select_relate_sup, cmd);
						// 是否包场
						if (ck_relate_store) branch_status = 1;
						// 修改包场客户名称同步更新包场仓名称
						if (relateStoreName != null && !relateStoreName.branch_name.Contains(sup_name)) update_relate_sname = $@", branch_name = '{sup_name + "(包场仓)"}'";
						// 更新包场仓库
						add_store_sql = $@"update info_branch set status = {branch_status} {update_relate_sname} where company_id = {companyID} and branch_id = '{relateStoreId.relate_store}'  RETURNING branch_id;";
					}
					else
					{
						// 是否包场
						if (ck_relate_store)
						{
							// 新建关联仓库
							add_store_sql = @$"insert into info_branch(company_id,       branch_name,  allow_negative_stock, allow_negative_stock_order, branch_type, py_str, status, negative_stock_accordance, for_contract_seller)  
                                                            values ({companyID}, '{sup_name + "(包场仓)"}',    false,                 false     , 'store'   , '{py_str + "(bcc)"}', '1', 'usable', false     ) RETURNING branch_id;";

						}
					}
				}

                string relate_store = "";
				if (add_store_sql.IsValid())
				{
					dynamic new_branch_id = await CDbDealer.GetRecordsFromSQLAsync(add_store_sql, cmd);
					relate_store = request.relate_store = new_branch_id[0].branch_id;
				}
                else
				{
					relate_store = request.relate_store = "";  // 没有使用包场
				}

				request.approve_flag = approveFlag;
                 
				JsonResult jres = await model.SaveTable(cmd, request, tran);
                dynamic res = (dynamic)jres.Value;
                if (res.result != "OK")
                {
                    tran.Rollback();
                    return jres;
                }
                if(relate_store!="")
				{
                    supcust_id = model.DataItems["supcust_id"].Value;
					string sql = $"update info_branch set relate_client = {supcust_id} where company_id = {companyID} and branch_id = {relate_store}";
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
				}

				tran.Commit();
                return jres;
			}
            catch(Exception e)
            {
                tran.Rollback();
                
				return new JsonResult(new { result="Error", msg="保存发生错误" });
			}
        }

        [HttpPost]
        public async Task<JsonResult> Approve([FromBody] dynamic data)
        {
            string result = "OK";
            string supcust_id = (string)data.supcust_id;
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            // 判断通过还是不通过
            string logInfoSql = $@"select dcl.*,io.oper_name as edit_name from document_change_log dcl 
                                left join (select oper_name,oper_id from info_operator where company_id =  {companyID}) io on io.oper_id = dcl.oper_id
                            where company_id = {companyID} and obj_id = {supcust_id} order by happen_time desc limit 1;";
            dynamic editLog = await CDbDealer.Get1RecordFromSQLAsync(logInfoSql, cmd);
            var param = JsonConvert.DeserializeObject<Dictionary<string, string>>(JsonConvert.SerializeObject(data.param));
            string updateSql = "update info_supcust set approve_status = null,  ";
            foreach (var kvp in param)
            {
                if (kvp.Key == "supcust_id" || kvp.Key == "operKey" || kvp.Key == "approve_flag")
                {
                    continue;
                }
                //else if (kvp.Key == "approve_status")
                //{
                //    if (kvp.Value?.ToLower() == "true"){
                //        updateSql += $"approve_status = 'wait approve',";
                //    } 
                //    else
                //    {
                //        updateSql += $"approve_status = null,";
                //    }
                //    continue;   
                //} 
                updateSql += string.IsNullOrEmpty(kvp.Value) ? $"{kvp.Key} = null," : $"{kvp.Key} = '{kvp.Value}',";
                
            }
            updateSql = updateSql.Trim().TrimEnd(',');
            updateSql += $" where company_id = {companyID} and supcust_id = {supcust_id}";
            ClientEditModel model = new ClientEditModel(cmd);
            model.DataItems[model.m_idFld].Value = supcust_id;
            model.DataItems[model.m_nameFld].Value = (string)data.sup_name;
            dynamic origInfo = await model.GetRecord(cmd, (string)data.operKey, supcust_id);

            cmd.CommandText = updateSql;
            await cmd.ExecuteNonQueryAsync();
            string approveBrief = data.approve_brief ?? "";
            string logFlowId = editLog.flow_id ?? "";
            string receiverId = editLog.oper_id ?? "";
            string msgId = data.msg_id ?? "";
            await model.SaveLog(cmd, (string)data.operKey, origInfo, data.approve_flag.ToString(), approveBrief, logFlowId, receiverId, msgId);
            return Json(new { result});
        }
    }
}