using ArtisanManage.Models;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NPOI.POIFS.Crypt.Dsig;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.CwPages
{
    public class CashBankBalanceModel : PageQueryModel
    {
        public string BizStartPeriod = "";
        public CashBankBalanceModel(CMySbCommand cmd, bool useMainDb = false) : base(Services.MenuId.cashBankBalance)
        {
            if (useMainDb) this.Database = "";
            this.cmd = cmd;
            this.PageTitle = "现金银行余额表";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay", new DataItem(){Title="开始日期", FldArea="divHead", CtrlType="jqxDateTimeInput",  CompareOperator=">=", Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00", ForQuery=false}},
                {"endDay", new DataItem(){Title="结束日期", FldArea="divHead", CtrlType="jqxDateTimeInput", CompareOperator="<=", Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59", ForQuery=false,
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }"
                }},
                {"time_type",new DataItem(){FldArea="divHead",Title="时间类型",LabelFld = "timetype_name",ButtonUsage = "list",CompareOperator="=",Value="happen_time",Label="按交易时间", ForQuery=false,
                        Source = @"[{v:'happen_time', l:'按交易时间'},
                                           {v:'make_time', l:'按制单时间'},
                                           {v:'approve_time',l:'按审核时间'}]"
                }},
                {"sub_id",new DataItem(){Title="账户",FldArea="divHead", SqlFld="e.sub_id", LabelFld="sub_name",ButtonUsage="list",CompareOperator="=", Checkboxes = true,
                    SqlForOptions = "select q.sub_id as v, s.sub_name as l from info_pay_qrcode q left join cw_subject s on q.company_id=s.company_id and q.sub_id=s.sub_id where q.company_id =~COMPANY_ID and s.sub_type ='QT' order by order_index"}},
                {"showRed",new DataItem(){FldArea="divHead",Title="显示红冲单",CtrlType="jqxCheckBox",ForQuery=false,Value="false",AutoRemember=true}},
                {"status", new DataItem(){Title="状态", FldArea="divHead", Label="正常",SqlFld="cs.status", Width="100", LabelFld="status_name", ButtonUsage="list", CompareOperator="=", Value="1", Source=@"[{v:'0', l:'停用'}, {v:'1', l:'正常'}]", NullEqualValue = "1"}},
            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates = true,
                      IdColumn="sub_id",
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sub_id",new DataItem(){Title="sub_id", SqlFld="q.sub_id", Hidden=true, HideOnLoad=true }},
                       {"sub_name",new DataItem(){Title="账户", SqlFld="cs.sub_name", Linkable = true }},//, Linkable=true
                       {"from_amt",   new DataItem(){Title="期初余额", CellsAlign="right", SqlFld="cast(coalesce(e.balance,0)-(coalesce(s2.amt,0)+coalesce(b2.amt,0)+coalesce(a2.amt,0)+coalesce(p2.amt,0)+coalesce(z2.amt,0)+coalesce(t2.amt,0)+coalesce(l2.amt,0))-(coalesce(s1.in_amt,0)-coalesce(s1.out_amt,0)+coalesce(b1.in_amt,0)-coalesce(b1.out_amt,0)+coalesce(a1.in_amt,0)-coalesce(a1.out_amt,0)+coalesce(p1.in_amt,0)-coalesce(p1.out_amt,0)+coalesce(z1.in_amt,0)-coalesce(z1.out_amt,0)+coalesce(t1.in_amt,0)-coalesce(t1.out_amt,0)+coalesce(l1.in_amt,0)-coalesce(l1.out_amt,0)) as decimal(18,2))",ShowSum=true }},
                       {"in_amt",   new DataItem(){Title="收入", CellsAlign="right", SqlFld="cast((coalesce(s1.in_amt,0)+coalesce(b1.in_amt,0)+coalesce(a1.in_amt,0)+coalesce(p1.in_amt,0)+coalesce(z1.in_amt,0)+coalesce(t1.in_amt,0)+coalesce(l1.in_amt,0)) as decimal(18,2))",ShowSum=true}},
                       {"out_amt",   new DataItem(){Title="支出", CellsAlign="right", SqlFld="cast((coalesce(s1.out_amt,0)+coalesce(b1.out_amt,0)+coalesce(a1.out_amt,0)+coalesce(p1.out_amt,0)+coalesce(z1.out_amt,0)+coalesce(t1.out_amt,0)+coalesce(l1.out_amt,0)) as decimal(18,2))",ShowSum=true}},
                       {"to_amt",   new DataItem(){Title="期末余额", CellsAlign="right", SqlFld="cast(coalesce(e.balance,0)-(coalesce(s2.amt,0)+coalesce(b2.amt,0)+coalesce(a2.amt,0)+coalesce(p2.amt,0)+coalesce(z2.amt,0)+coalesce(t2.amt,0)+coalesce(l2.amt,0)) as decimal(18,2))",ShowSum=true}},

                     },
                     //期间余额=最终余额-(销售单收入+采购单支出(负值)+收付款单收支(正/负)+预收预付款单定货会收支(正/负)+支出收入单收支(正/负)+转账单收支(正/负)+贷款单收入+还贷款单支出(负值))
                     QueryFromSQL=@"
                from info_pay_qrcode q
                left join cashbank_balance e on e.company_id=~COMPANY_ID and q.sub_id=e.sub_id
                left join cw_subject cs on cs.company_id=~COMPANY_ID and cs.sub_id=e.sub_id 
                left join (select sub_id, sum(in_amt) as in_amt, sum(out_amt) as out_amt from (
                                select sub_id, sum(case when s.money_inout_flag=1 then coalesce(amt,0) else 0 end) as in_amt, sum(case when s.money_inout_flag = -1 then coalesce(amt, 0) else 0 end) as out_amt from(
                                    (select s.company_id, s.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_sale_main s
                                    left join cw_subject cs on s.company_id = cs.company_id and s.payway1_id = cs.sub_id where s.company_id=~COMPANY_ID and s.approve_time is not null and s.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(s.payway1_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select s.company_id, s.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_sale_main s
                                    left join cw_subject cs on s.company_id = cs.company_id and s.payway2_id = cs.sub_id where s.company_id=~COMPANY_ID and s.approve_time is not null and s.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(s.payway2_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select s.company_id, s.sheet_id, payway3_id as sub_id, payway3_amount as amt, money_inout_flag, sub_type, status from sheet_sale_main s
                                    left join cw_subject cs on s.company_id = cs.company_id and s.payway3_id = cs.sub_id where s.company_id=~COMPANY_ID and s.approve_time is not null and s.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(s.payway3_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) ss
                                left join sheet_sale_main s on s.company_id=~COMPANY_ID and ss.sheet_id = s.sheet_id
                                where ss.company_id = ~COMPANY_ID and s.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(s.is_imported,false)=false and s.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by ss.company_id, ss.sheet_id, ss.sub_id, ss.money_inout_flag
                        ) sss group by sub_id
                ) s1 on e.sub_id = s1.sub_id
                left join(select sub_id, sum(amt) as amt from(
                                select sub_id, sum(s.money_inout_flag * coalesce(amt, 0)) as amt from(
                                    (select s.company_id, s.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_sale_main s
                                    left join cw_subject cs on s.company_id = cs.company_id and s.payway1_id = cs.sub_id where s.company_id=~COMPANY_ID and s.approve_time is not null and s.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(s.payway1_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select s.company_id, s.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_sale_main s
                                    left join cw_subject cs on s.company_id = cs.company_id and s.payway2_id = cs.sub_id where s.company_id=~COMPANY_ID and s.approve_time is not null and s.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(s.payway2_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select s.company_id, s.sheet_id, payway3_id as sub_id, payway3_amount as amt, money_inout_flag, sub_type, status from sheet_sale_main s
                                    left join cw_subject cs on s.company_id = cs.company_id and s.payway3_id = cs.sub_id where s.company_id=~COMPANY_ID and s.approve_time is not null and s.~VAR_TIME_TYPE ~VAR_TIME_OVER   and coalesce(s.payway3_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) ss
                                left join sheet_sale_main s on s.company_id=~COMPANY_ID and ss.sheet_id = s.sheet_id
                                where ss.company_id = ~COMPANY_ID and s.~VAR_TIME_TYPE ~VAR_TIME_OVER and coalesce(s.is_imported,false)=false ~VAR_SHOW_REDSHEET
                                group by ss.company_id, ss.sheet_id, ss.sub_id, ss.money_inout_flag
                        ) sss group by sub_id
                ) s2 on e.sub_id = s2.sub_id

                left join(select sub_id, sum(in_amt) as in_amt, sum(out_amt) as out_amt from(
                                select sub_id, sum(case when b.money_inout_flag = 1 then coalesce(amt, 0) else 0 end) as in_amt, sum(case when b.money_inout_flag = -1 then coalesce(amt, 0) else 0 end) as out_amt from(
                                    (select b.company_id, b.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_buy_main b
                                    left join cw_subject cs on b.company_id = cs.company_id and b.payway1_id = cs.sub_id where b.company_id=~COMPANY_ID and b.approve_time is not null and b.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(b.payway1_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select b.company_id, b.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_buy_main b
                                    left join cw_subject cs on b.company_id = cs.company_id and b.payway2_id = cs.sub_id where b.company_id=~COMPANY_ID and b.approve_time is not null and b.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(b.payway2_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select b.company_id, b.sheet_id, payway3_id as sub_id, payway3_amount as amt, money_inout_flag, sub_type, status from sheet_buy_main b
                                    left join cw_subject cs on b.company_id = cs.company_id and b.payway3_id = cs.sub_id where b.company_id=~COMPANY_ID and b.approve_time is not null and b.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(b.payway3_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) bb
                                left join sheet_buy_main b on b.company_id=~COMPANY_ID and bb.sheet_id = b.sheet_id
                                where bb.company_id = ~COMPANY_ID and b.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN  and coalesce(b.is_imported,false)=false and b.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by bb.company_id, bb.sheet_id, bb.sub_id, bb.money_inout_flag
                            ) bbb group by sub_id
                ) b1 on e.sub_id = b1.sub_id
                left join(select sub_id, sum(amt) as amt from(
                                select sub_id, sum(b.money_inout_flag * coalesce(amt, 0)) as amt from(
                                    (select b.company_id, b.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_buy_main b
                                    left join cw_subject cs on b.company_id = cs.company_id and b.payway1_id = cs.sub_id where b.company_id=~COMPANY_ID and b.approve_time is not null and b.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(b.payway1_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select b.company_id, b.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_buy_main b
                                    left join cw_subject cs on b.company_id = cs.company_id and b.payway2_id = cs.sub_id where b.company_id=~COMPANY_ID and b.approve_time is not null and b.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(b.payway2_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select b.company_id, b.sheet_id, payway3_id as sub_id, payway3_amount as amt, money_inout_flag, sub_type, status from sheet_buy_main b
                                    left join cw_subject cs on b.company_id = cs.company_id and b.payway3_id = cs.sub_id where b.company_id=~COMPANY_ID and b.approve_time is not null and b.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(b.payway3_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) bb
                                left join sheet_buy_main b on b.company_id=~COMPANY_ID and bb.sheet_id = b.sheet_id
                                where bb.company_id = ~COMPANY_ID and b.~VAR_TIME_TYPE ~VAR_TIME_OVER and coalesce(b.is_imported,false)=false and b.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by bb.company_id, bb.sheet_id, bb.sub_id, bb.money_inout_flag
                            ) bbb group by sub_id
                ) b2 on e.sub_id = b2.sub_id

                left join(select sub_id, sum(in_amt) as in_amt, sum(out_amt) as out_amt from(
                                select sub_id, sum(case when a.money_inout_flag = 1 then coalesce(amt, 0) else 0 end) as in_amt, sum(case when a.money_inout_flag = -1 then coalesce(amt, 0) else 0 end) as out_amt from(
                                    (select a.company_id, a.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_get_arrears_main a
                                    left join cw_subject cs on a.company_id = cs.company_id and a.payway1_id = cs.sub_id where a.company_id=~COMPANY_ID and a.approve_time is not null and a.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(a.payway1_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select a.company_id, a.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_get_arrears_main a
                                    left join cw_subject cs on a.company_id = cs.company_id and a.payway2_id = cs.sub_id where a.company_id=~COMPANY_ID and a.approve_time is not null and a.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(a.payway2_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select a.company_id, a.sheet_id, payway3_id as sub_id, payway3_amount as amt, money_inout_flag, sub_type, status from sheet_get_arrears_main a
                                    left join cw_subject cs on a.company_id = cs.company_id and a.payway3_id = cs.sub_id where a.company_id=~COMPANY_ID and a.approve_time is not null and a.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(a.payway3_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) aa
                                left join sheet_get_arrears_main a on a.company_id=~COMPANY_ID and aa.sheet_id = a.sheet_id
                                where aa.company_id = ~COMPANY_ID and a.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(a.is_imported,false)=false and a.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by aa.company_id, aa.sheet_id, aa.sub_id, aa.money_inout_flag
                            ) aaa group by sub_id
                ) a1 on e.sub_id = a1.sub_id
                left join(select sub_id, sum(amt) as amt from(
                                select sub_id, sum(a.money_inout_flag * coalesce(amt, 0)) as amt from(
                                    (select a.company_id, a.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_get_arrears_main a
                                    left join cw_subject cs on a.company_id = cs.company_id and a.payway1_id = cs.sub_id where a.company_id=~COMPANY_ID and a.approve_time is not null and a.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(a.payway1_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select a.company_id, a.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_get_arrears_main a
                                    left join cw_subject cs on a.company_id = cs.company_id and a.payway2_id = cs.sub_id where a.company_id=~COMPANY_ID and a.approve_time is not null and a.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(a.payway2_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select a.company_id, a.sheet_id, payway3_id as sub_id, payway3_amount as amt, money_inout_flag, sub_type, status from sheet_get_arrears_main a
                                    left join cw_subject cs on a.company_id = cs.company_id and a.payway3_id = cs.sub_id where a.company_id=~COMPANY_ID and a.approve_time is not null and a.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(a.payway3_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) aa
                                left join sheet_get_arrears_main a on a.company_id=~COMPANY_ID and aa.sheet_id = a.sheet_id
                                where aa.company_id = ~COMPANY_ID and a.~VAR_TIME_TYPE ~VAR_TIME_OVER and coalesce(a.is_imported,false)=false and a.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by aa.company_id, aa.sheet_id, aa.sub_id, aa.money_inout_flag
                            ) aaa group by sub_id
                ) a2 on e.sub_id = a2.sub_id

                left join(select sub_id, sum(in_amt) as in_amt, sum(out_amt) as out_amt from(
                                select sub_id, sum(case when p.money_inout_flag = 1 then coalesce(amt, 0) else 0 end) as in_amt, sum(case when p.money_inout_flag = -1 then coalesce(amt, 0) else 0 end) as out_amt from(
                                    (select p.company_id, p.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_prepay p
                                    left join cw_subject cs on p.company_id = cs.company_id and p.payway1_id = cs.sub_id where p.company_id=~COMPANY_ID and p.approve_time is not null and p.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(p.payway1_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select p.company_id, p.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_prepay p
                                    left join cw_subject cs on p.company_id = cs.company_id and p.payway2_id = cs.sub_id where p.company_id=~COMPANY_ID and p.approve_time is not null and p.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(p.payway2_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select p.company_id, p.sheet_id, payway3_id as sub_id, payway3_amount as amt, money_inout_flag, sub_type, status from sheet_prepay p
                                    left join cw_subject cs on p.company_id = cs.company_id and p.payway3_id = cs.sub_id where p.company_id=~COMPANY_ID and p.approve_time is not null and p.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(p.payway3_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) pp
                                left join sheet_prepay p on p.company_id=~COMPANY_ID and pp.sheet_id = p.sheet_id
                                where pp.company_id = ~COMPANY_ID and p.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(p.is_imported,false)=false and p.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by pp.company_id, pp.sheet_id, pp.sub_id, pp.money_inout_flag
                            ) ppp group by sub_id
                ) p1 on e.sub_id = p1.sub_id
                left join(select sub_id, sum(amt) as amt from(
                                select sub_id, sum(p.money_inout_flag * coalesce(amt, 0)) as amt from(
                                    (select p.company_id, p.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_prepay p
                                    left join cw_subject cs on p.company_id = cs.company_id and p.payway1_id = cs.sub_id where p.company_id=~COMPANY_ID and p.approve_time is not null and p.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(p.payway1_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select p.company_id, p.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_prepay p
                                    left join cw_subject cs on p.company_id = cs.company_id and p.payway2_id = cs.sub_id where p.company_id=~COMPANY_ID and p.approve_time is not null and p.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(p.payway2_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select p.company_id, p.sheet_id, payway3_id as sub_id, payway3_amount as amt, money_inout_flag, sub_type, status from sheet_prepay p
                                    left join cw_subject cs on p.company_id = cs.company_id and p.payway3_id = cs.sub_id where p.company_id=~COMPANY_ID and p.approve_time is not null and p.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(p.payway3_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) pp
                                left join sheet_prepay p on p.company_id=~COMPANY_ID and pp.sheet_id = p.sheet_id
                                where pp.company_id = ~COMPANY_ID and p.~VAR_TIME_TYPE ~VAR_TIME_OVER and coalesce(p.is_imported,false)=false and p.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by pp.company_id, pp.sheet_id, pp.sub_id, pp.money_inout_flag
                            ) ppp group by sub_id
                ) p2 on e.sub_id = p2.sub_id

                left join(select sub_id, sum(in_amt) as in_amt, sum(out_amt) as out_amt from(
                                select sub_id, sum(case when z.money_inout_flag = 1 then coalesce(amt, 0) else 0 end) as in_amt, sum(case when z.money_inout_flag = -1 then coalesce(amt, 0) else 0 end) as out_amt from(
                                    (select z.company_id, z.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_fee_out_main z
                                    left join cw_subject cs on z.company_id = cs.company_id and z.payway1_id = cs.sub_id where z.company_id=~COMPANY_ID and z.approve_time is not null and z.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(z.payway1_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select z.company_id, z.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_fee_out_main z
                                    left join cw_subject cs on z.company_id = cs.company_id and z.payway2_id = cs.sub_id where z.company_id=~COMPANY_ID and z.approve_time is not null and z.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(z.payway2_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) zz
                                left join sheet_fee_out_main z on z.company_id=~COMPANY_ID and zz.sheet_id = z.sheet_id
                                where zz.company_id = ~COMPANY_ID and z.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and z.sheet_attribute->>'imported' is null and z.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by zz.company_id, zz.sheet_id, zz.sub_id, zz.money_inout_flag
                            ) zzz group by sub_id
                ) z1 on e.sub_id = z1.sub_id
                left join(select sub_id, sum(amt) as amt from(
                                select sub_id, sum(z.money_inout_flag * coalesce(amt, 0)) as amt from(
                                    (select z.company_id, z.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_fee_out_main z
                                    left join cw_subject cs on z.company_id = cs.company_id and z.payway1_id = cs.sub_id where z.company_id=~COMPANY_ID and z.approve_time is not null and z.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(z.payway1_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select z.company_id, z.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_fee_out_main z
                                    left join cw_subject cs on z.company_id = cs.company_id and z.payway2_id = cs.sub_id where z.company_id=~COMPANY_ID and z.approve_time is not null and z.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(z.payway2_amount, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) zz
                                left join sheet_fee_out_main z on z.company_id=~COMPANY_ID and zz.sheet_id = z.sheet_id
                                where zz.company_id = ~COMPANY_ID and z.~VAR_TIME_TYPE ~VAR_TIME_OVER and z.sheet_attribute->>'imported' is null and z.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by zz.company_id, zz.sheet_id, zz.sub_id, zz.money_inout_flag
                            ) zzz group by sub_id
                ) z2 on e.sub_id = z2.sub_id

                left join(select sub_id, sum(in_amt) as in_amt, sum(out_amt) as out_amt from(
                                select sub_id, sum(case when tt.money_inout_flag = 1 then coalesce(amt, 0) else 0 end) as in_amt, sum(case when tt.money_inout_flag = -1 then coalesce(amt, 0) else 0 end) as out_amt from(
                                    (select td.company_id, td.sheet_id, money_out_id as sub_id, out_amount_withfee as amt, (case when coalesce(tm.red_flag,0) in (0,1) then -1 else 1 end) as money_inout_flag, sub_type, status from sheet_cashbank_transfer_detail td
                                    left join sheet_cashbank_transfer_main tm on tm.company_id=~COMPANY_ID and td.sheet_id=tm.sheet_id
                                    left join cw_subject cs on cs.company_id=~COMPANY_ID and td.money_out_id = cs.sub_id where td.company_id=~COMPANY_ID and tm.approve_time is not null and tm.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(td.out_amount_withfee, 0)<>0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select td.company_id, td.sheet_id, money_in_id as sub_id, in_amount as amt, (case when coalesce(tm.red_flag,0) in (0,1) then 1 else -1 end) as money_inout_flag, sub_type, status from sheet_cashbank_transfer_detail td
                                    left join sheet_cashbank_transfer_main tm on tm.company_id=~COMPANY_ID and td.sheet_id=tm.sheet_id
                                    left join cw_subject cs on cs.company_id=~COMPANY_ID and td.money_in_id = cs.sub_id where td.company_id=~COMPANY_ID and tm.approve_time is not null and tm.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(td.in_amount, 0)<>0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) tt
                                left join sheet_cashbank_transfer_main t on t.company_id=~COMPANY_ID and tt.sheet_id = t.sheet_id
                                where tt.company_id = ~COMPANY_ID and t.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and t.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by tt.company_id, tt.sheet_id, tt.sub_id, tt.money_inout_flag
                            ) ttt group by sub_id
                ) t1 on e.sub_id = t1.sub_id
                left join(select sub_id, sum(amt) as amt from(
                                select sub_id, sum(tt.money_inout_flag * coalesce(amt, 0)) as amt from(
                                    (select td.company_id, td.sheet_id, money_out_id as sub_id, out_amount_withfee as amt, (case when coalesce(tm.red_flag,0) in (0,1) then -1 else 1 end) as money_inout_flag, sub_type, status from sheet_cashbank_transfer_detail td
                                    left join sheet_cashbank_transfer_main tm on tm.company_id=~COMPANY_ID and td.sheet_id=tm.sheet_id
                                    left join cw_subject cs on cs.company_id=~COMPANY_ID and td.money_out_id = cs.sub_id where td.company_id=~COMPANY_ID and tm.approve_time is not null and tm.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(td.out_amount_withfee, 0)<>0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select td.company_id, td.sheet_id, money_in_id as sub_id, in_amount as amt, (case when coalesce(tm.red_flag,0) in (0,1) then 1 else -1 end) as money_inout_flag, sub_type, status from sheet_cashbank_transfer_detail td
                                    left join sheet_cashbank_transfer_main tm on tm.company_id=~COMPANY_ID and td.sheet_id=tm.sheet_id
                                    left join cw_subject cs on cs.company_id=~COMPANY_ID and td.money_in_id = cs.sub_id where td.company_id=~COMPANY_ID and tm.approve_time is not null and tm.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(td.in_amount, 0)<>0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                ) tt
                                left join sheet_cashbank_transfer_main t on t.company_id=~COMPANY_ID and tt.sheet_id = t.sheet_id
                                where tt.company_id = ~COMPANY_ID and t.~VAR_TIME_TYPE ~VAR_TIME_OVER and t.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by tt.company_id, tt.sheet_id, tt.sub_id, tt.money_inout_flag) ttt group by sub_id
                ) t2 on e.sub_id = t2.sub_id
                left join(select sub_id, sum(in_amt) as in_amt, 0 as out_amt from(
                                select sub_id, sum(sl.money_inout_flag * coalesce(amt, 0)) as in_amt from (
                                    (select  sl.company_id, sl.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_loan sl
                                    left join cw_subject cs on cs.company_id=~COMPANY_ID and sl.payway1_id = cs.sub_id where sl.company_id=~COMPANY_ID and sl.approve_time is not null and sl.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(sl.payway1_id, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select  sl.company_id, sl.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_loan sl
                                    left join cw_subject cs on cs.company_id=~COMPANY_ID and sl.payway2_id = cs.sub_id where sl.company_id=~COMPANY_ID and sl.approve_time is not null and sl.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(sl.payway2_id, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select  sl.company_id, sl.sheet_id, payway3_id as sub_id, payway3_amount as amt, money_inout_flag, sub_type, status from sheet_loan sl
                                    left join cw_subject cs on cs.company_id=~COMPANY_ID and sl.payway3_id = cs.sub_id where sl.company_id=~COMPANY_ID and sl.approve_time is not null and sl.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(sl.payway3_id, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                )ll
                                left join sheet_loan sl on sl.company_id=~COMPANY_ID and ll.sheet_id = sl.sheet_id
                                where ll.company_id = ~COMPANY_ID and sl.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and sl.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by ll.company_id, ll.sheet_id, ll.sub_id, ll.money_inout_flag) lll group by sub_id
                ) l1 on e.sub_id = l1.sub_id
                left join(select sub_id, sum(amt) as amt from(
                                select sub_id, sum(sl.money_inout_flag * coalesce(amt, 0)) as amt from (
                                    (select  sl.company_id, sl.sheet_id, payway1_id as sub_id, payway1_amount as amt, money_inout_flag, sub_type, status from sheet_loan sl
                                    left join cw_subject cs on cs.company_id=~COMPANY_ID and sl.payway1_id = cs.sub_id where sl.company_id=~COMPANY_ID and sl.approve_time is not null and sl.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(sl.payway1_id, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select  sl.company_id, sl.sheet_id, payway2_id as sub_id, payway2_amount as amt, money_inout_flag, sub_type, status from sheet_loan sl
                                    left join cw_subject cs on cs.company_id=~COMPANY_ID and sl.payway2_id = cs.sub_id where sl.company_id=~COMPANY_ID and sl.approve_time is not null and sl.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(sl.payway2_id, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                    union all
                                    (select  sl.company_id, sl.sheet_id, payway3_id as sub_id, payway3_amount as amt, money_inout_flag, sub_type, status from sheet_loan sl
                                    left join cw_subject cs on cs.company_id=~COMPANY_ID and sl.payway3_id = cs.sub_id where sl.company_id=~COMPANY_ID and sl.approve_time is not null and sl.~VAR_TIME_TYPE ~VAR_TIME_OVER  and coalesce(sl.payway3_id, 0) <> 0 and cs.sub_type = 'QT' ~VAR_STATUS)
                                )ll
                                left join sheet_loan sl on sl.company_id=~COMPANY_ID and ll.sheet_id = sl.sheet_id
                                where ll.company_id = ~COMPANY_ID and sl.~VAR_TIME_TYPE ~VAR_TIME_OVER and sl.approve_time is not null ~VAR_SHOW_REDSHEET
                                group by ll.company_id, ll.sheet_id, ll.sub_id, ll.money_inout_flag) lll group by sub_id
                ) l2 on e.sub_id = l2.sub_id
                where e.company_id = ~COMPANY_ID  ~VAR_STATUS" //s1 b1 a1 p1 z1 t1所选时间发生额，s2 b2 a2 p2 z2 t2从toTime到最大approvetime发生额（即>toTime，不需要取出approvetime）
                  }
                }
            };
        }

        public async Task OnGet(string operKey)
        {
            await InitGet(cmd);
            dynamic g_co = await CDbDealer.Get1RecordFromSQLAsync($"select business_start_period from g_company where company_id={company_id}", cmd);
            BizStartPeriod = g_co.business_start_period;
        }

        public override async Task OnQueryConditionStrGot(string condi, CMySbCommand cmd)
        {
            this.SQLVariables["TIME_TYPE"] = DataItems["time_type"].Value;
            if (this.SQLVariables["TIME_TYPE"] == "") this.SQLVariables["TIME_TYPE"] = "happen_time";
            this.SQLVariables["TIME_BETWEEN"] = $"between '{DataItems["startDay"].Value}' and '{DataItems["endDay"].Value}' ";
            this.SQLVariables["TIME_OVER"] = $"> '{DataItems["endDay"].Value}' ";
            this.SQLVariables["SHOW_REDSHEET"] = Convert.ToBoolean(DataItems["showRed"].Value) ? "" : " and (red_flag is null)";
            //this.SQLVariables["STATUS"] = $"'{DataItems["status"].Value.ToString()}'";
            string statusValue = DataItems["status"].Value;
            if (string.IsNullOrEmpty(statusValue))
            {
                this.SQLVariables["STATUS"] = "";
                // 未指定状态，查询所有

            }
            else
            {
                switch (statusValue)
                {
                    case "1":
                        this.SQLVariables["STATUS"] = " and COALESCE(cs.status, '1') = '1'";
                        break;
                    case "0":
                        this.SQLVariables["STATUS"] = " and cs.status = '0'";
                        break;
                    default:
                        this.SQLVariables["STATUS"] = "";
                        break;
                }
            }
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class CashBankBalanceController : Controller
    {
        CMySbCommand cmd;
        public CashBankBalanceController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value)
        {
            CashBankBalanceModel model = new CashBankBalanceModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, null);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            CashBankBalanceModel model = new CashBankBalanceModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            CashBankBalanceModel model = new CashBankBalanceModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
