﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using NPOI.HSSF.Record;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class VisitSummaryBySellerModel : PageQueryModel
    { 
        public VisitSummaryBySellerModel(CMySbCommand cmd) : base(Services.MenuId.visitSummaryBySeller)
        {
            this.cmd = cmd;
            this.PageTitle = "拜访汇总";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="start_time",CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="start_time",CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},

                {"seller_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="sv.seller_id",SqlForOptions=CommonTool.selectSellers } },
                {"day_id",new DataItem(){Title="日程",FldArea="divHead",LabelFld="day_name",ButtonUsage="list",CompareOperator="=",SqlFld="vd.day_id",
                    SqlForOptions ="select day_id as v,day_name as l from info_visit_day where company_id = ~COMPANY_ID"}},
                {"schedule_id",new DataItem(){Title="行程",FldArea="divHead",LabelFld="schedule_name",ButtonUsage="list",CompareOperator="=",SqlFld="vs.schedule_id",
                    SqlForOptions ="select schedule_id as v,schedule_name as l from info_visit_schedule where company_id = ~COMPANY_ID"}},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                        {"oper_id",    new DataItem(){Title="业务员",  Width="150",Hidden=true}},
                       {"oper_name",    new DataItem(){Title="业务员名称", Linkable=true,  Width="300" }},
                       {"schedule_id",   new DataItem(){Title="行程id",   CellsAlign="right",  Width="100",SqlFld="vs.schedule_id ",Hidden=true}},
                       {"schedule_name",   new DataItem(){Title="行程",   Width="100",}},
                       {"day_id",   new DataItem(){Title="日程id",   CellsAlign="right",  Width="100",SqlFld="sv.day_id",Hidden=true}},
                       {"day_name",   new DataItem(){Title="日程", Linkable=true,   Width="100",}},
                       {"visit_time",   new DataItem(){Title="拜访日期",   CellsAlign="right",  Width="150",SqlFld="to_char(start_time,'YYYY-MM-DD') "}},
                       {"plan_num",   new DataItem(){Title="计划数量", Sortable=true,  CellsAlign="right",ShowSum=true,  Width="100",}},
                       {"real_num",   new DataItem(){Title="实际数量",Linkable=true, Sortable=true,  CellsAlign="right",ShowSum=true,  Width="100",SqlFld="count(*) ",}},
                       {"visit_score",   new DataItem(){Title="得分",SqlFld="sum(visit_score) ",ShowSum=true}},
                       {"sale_amount",new DataItem(){Title="销售额", SqlFld =@$"tb_sup_count.sum_sale_amount " ,Sortable=true,Width="100",ShowSum=true} },
                        {"order_amount",new DataItem(){Title="销售订单额", SqlFld =@$"tb_sup_count.sum_order_amount ",Sortable=true,Width="120" ,ShowSum=true} },
                        {"sup_count",new DataItem(){Title="拜访门店家数", SqlFld =@$"tb_sup_count.sup_count " ,CellsAlign="right", ShowSum=true} }
                     },
                     QueryFromSQL=@"
FROM sheet_visit sv

left join info_operator   o  on sv.seller_id = o.oper_id and o.company_id=~COMPANY_ID
left join info_visit_day vd  on sv.day_id=vd.day_id and vd.company_id=~COMPANY_ID
left join info_visit_schedule vs  on vd.schedule_id=vs.schedule_id and vs.company_id=~COMPANY_ID
LEFT JOIN (
					SELECT COUNT
			( DISTINCT supcust_id ) AS sup_count ,seller_id,concat ( to_char( start_time, 'YYYY-MM-DD' ) ) as dd_start_time,sum(sale_amount) as sum_sale_amount,sum(order_amount) as sum_order_amount
		FROM
			sheet_visit 
		WHERE
			company_id = ~COMPANY_ID 
			AND start_time::text >= concat ( to_char( start_time, 'YYYY-MM-DD 00:00' ) )  
			AND  start_time::text < concat ( to_char( start_time, 'YYYY-MM-DD 23:59' ) ) 
		GROUP BY seller_id,concat ( to_char( start_time, 'YYYY-MM-DD' ) ) 
			
			) tb_sup_count	ON o.oper_id = tb_sup_count.seller_id and  tb_sup_count.dd_start_time = to_char( start_time, 'YYYY-MM-DD' )
LEFT JOIN (
select 
day_id,
count(*) plan_num
from info_visit_day_client 
where company_id=~COMPANY_ID
GROUP BY day_id
)vc on sv.day_id=vc.day_id
where sv.company_id=~COMPANY_ID and COALESCE(o.status,'1')='1'
",
                     QueryGroupBySQL = " group by concat(to_char(start_time,'YYYY-MM-DD'),sv.day_id,sv.seller_id),visit_time,oper_name,day_name,plan_num,oper_id,sv.day_id,vs.schedule_id,tb_sup_count.sup_count,sum_order_amount,sum_sale_amount ",
                     QueryOrderSQL=" order by visit_time"
                  }
                } 
            };             
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            
        }


        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {

        }

        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class VisitSummaryBySellerController : QueryController
    { 
        public VisitSummaryBySellerController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value ,string availValues)
        {
            VisitSummaryBySellerModel model = new VisitSummaryBySellerModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpGet]
        public async Task<JsonResult> GetVisitDayDetail(string visitDay,string dayID,string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = @$"SELECT isc.sup_name,
                            (select count(1) from sheet_visit where  start_time between  '{visitDay} 00:00' and '{visitDay} 23:59' and ivdc.supcust_id = sheet_visit.supcust_id )
                            from info_visit_day_client ivdc
                            left join  info_supcust isc on ivdc.company_id = isc.company_id and isc.supcust_id = ivdc.supcust_id
                            where day_id = {dayID} and ivdc.company_id = {companyID}";
            dynamic data =await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", msg = "", rows = data });

        }
        [HttpGet]
        public async Task<object> GetQueryRecords(string operKey ,int seller_id, string startDay, string endDay)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            VisitSummaryBySellerModel model = new VisitSummaryBySellerModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            model.Grids["gridItems"].QueryFromSQL = sql;
            dynamic records = await model.GetRecordFromQuerySQL(Request, cmd);
            var totalVisitCount = "-1";
            if (seller_id!=null)
            {
                string totalSummarySQL = $@"SELECT  COUNT(distinct supcust_id) FROM sheet_visit where seller_id = {seller_id} and start_time between '{startDay}' and '{endDay}' and company_id = {companyID}";
                dynamic data = await CDbDealer.Get1RecordFromSQLAsync(totalSummarySQL, cmd);
                totalVisitCount = data.count;
            }
            return new JsonResult(new { result = "OK", msg = "", rows = records.Value.rows, rowsCount = records.Value.rowsCount, sumResult = records.Value.sumResult, totalVisitCount });

        }
        public async Task<dynamic> GetMonthSummary(string seller_id, string startDay, string endDay)
        {
            dynamic res = null;
      
            return Json(res);
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel(string sheetType)
        {
            VisitSummaryBySellerModel model = new VisitSummaryBySellerModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            model.Grids["gridItems"].QueryFromSQL = sql;
            return await model.ExportExcel(Request, cmd);
        }

    }
}
