﻿using ArtisanManage.Models;
using ArtisanManage.MyCW;
using ArtisanManage.Pages.CwPages;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using myJXC;
using Newtonsoft.Json;
using Renci.SshNet.Messages.Connection;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowBulkArrears : SheetRowBase
    {
        [SaveToDB][FromFld] public string supcust_id { get; set; }
        [SaveToDB] [FromFld] public string mm_sheet_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string mm_sheet_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string order_sheet_no { get; set; }
        [FromFld("s.order_sheet_id",LOAD_PURPOSE.SHOW)] public string order_sheet_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string mm_sheet_time { get; set; }
        public string mm_sheet_date
        {
            get
            {
                return CPubVars.GetDateTextNoTime(mm_sheet_time);
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public string mm_make_brief { get; set; }

        [FromFld("s.sup_name")] public string shop_name { get; set; }
        [SaveToDB][FromFld] public string sup_name { get; set; }
        [SaveToDB] [FromFld] public string m_sheet_type { get; set; }
        public string mm_sheet_type_name { get {return m_sheet_type.Replace("CT", "采购退货").Replace("X" , "销售").Replace("T","退货").Replace("DH","定货会").Replace("YS","预收").Replace("ZC","费用支出").Replace("YF","预付").Replace("CG","采购").Replace("SR","其他收入");} }
        [SaveToDB] [FromFld] public decimal sheet_amount { get; set; }
        [SaveToDB] [FromFld] public decimal paid_amount { get; set; }
        [SaveToDB] [FromFld] public decimal disc_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_disc_amount { get; set; }
        [SaveToDB] [FromFld] public decimal left_amount { get; set; }
        [SaveToDB] [FromFld] public override string remark { get; set; }


    }

    public enum SHEET_GET_BULK_ARREARS
    {
        EMPTY,
        IS_GET,
        NOT_GET

    }
    public class SheetGetBulkArrears : SheetBase<SheetRowBulkArrears>
    {
        public List<string> appendixPhotos { get; set; } = new List<string>();
        public string appendix_photos { get; set; } = "";
        [SaveToDB] [FromFld] public string visit_id { get; set; } = "";
        [SaveToDB] [FromFld] public string supcust_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_name { get; set; } = "";
        [SaveToDB] [FromFld] public override int money_inout_flag { get; set; }
        [SaveToDB] [FromFld] public decimal sheet_amount { get; set; }
        [SaveToDB] [FromFld] public decimal paid_amount { get; set; }
        [SaveToDB] [FromFld] public decimal disc_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_disc_amount { get; set; }
        [SaveToDB] [FromFld] public decimal left_amount { get; set; }

        [SaveToDB] [FromFld] public string payway1_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_name { get; set; } = "";
        [SaveToDB] [FromFld] public decimal payway1_amount { get; set; }
        [SaveToDB] [FromFld] public string payway2_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_name { get; set; } = "";
        [SaveToDB] [FromFld] public decimal payway2_amount { get; set; }
        [SaveToDB][FromFld] public string payway3_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway3_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway3_amount { get; set; }
       
        [SaveToDB] [FromFld] public string getter_id { get; set; } = "";
        [SaveToDB][FromFld] public string son_sheet_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string getter_name { get; set; } = "";
        [SaveToDB] [FromFld] public override SHEET_TYPE sheet_type { get; set; }
        internal string mmSheetTable = "";
        [SaveToDB]
        [FromFld]
        public virtual string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                if (appendix_photos != "" && appendix_photos != "[]")
                {
                    sheetAttribute.Add("appendixPhotos", appendix_photos);
                }


                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);

                return s;
            }
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);

                    if (sheetAttr.appendixPhotos != null)
                    {
                        this.appendix_photos = sheetAttr.appendixPhotos;
                    }
                }
            }
        }
        [SaveToDB] [FromFld] public override string is_imported { get; set; } = "";
        // 对账单要恢复
        [SaveToDB][FromFld] public string order_sheet_id { get; set; } = "";
        [SaveToDB][FromFld] public string order_sheet_no { get; set; } = "";
        /*
        public SheetGetArrears() : base("sheet_get_arrears_main", "sheet_get_arrears_detail", LOAD_PURPOSE.ALL)
        {

        }*/
        public SheetGetBulkArrears(SHEET_GET_BULK_ARREARS sheetGetBulkArrears, LOAD_PURPOSE loadPurpose) : base("sheet_get_bulk_arrears_main", "sheet_get_bulk_arrears_detail", loadPurpose)
        {
            sheet_type =SHEET_TYPE.SHEET_GET_BULK_MONEY;
            ConstructFun();
        }
        private void ConstructFun()
        {
            mmSheetTable = "sheet_sale_main";
            if (sheet_type == SHEET_TYPE.SHEET_PAY_MONEY) mmSheetTable = "sheet_buy_main";
            money_inout_flag = sheet_type == SHEET_TYPE.SHEET_PAY_MONEY ? -1 : 1;

            if (LoadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @" left join info_supcust c on t.supcust_id=c.supcust_id and c.company_id=~COMPANY_ID
                                  left join (select oper_id,oper_name as getter_name from info_operator where company_id=~COMPANY_ID) getter on t.getter_id=getter.oper_id
                                  left join (select oper_id,oper_name as maker_name from info_operator where company_id=~COMPANY_ID) maker on t.maker_id=maker.oper_id
                                  left join  (select oper_id,oper_name as approver_name from info_operator where company_id=~COMPANY_ID) approver on t.approver_id=approver.oper_id
                                  left join (select sub_id,sub_name as payway1_name from cw_subject where company_id=~COMPANY_ID) pw1 on t.payway1_id=pw1.sub_id
                                  left join (select sub_id,sub_name as payway2_name from cw_subject where company_id=~COMPANY_ID) pw2 on t.payway2_id=pw2.sub_id
                                  left join (select sub_id,sub_name as payway3_name from cw_subject where company_id=~COMPANY_ID) pw3 on t.payway3_id=pw3.sub_id
              ";
                DetailLeftJoin = @$"
left join
(
   select a.*,b.sup_name from 
            (
                        select supcust_id, sheet_id,sheet_no as mm_sheet_no,order_sheet_no,sm.order_sheet_id,supcust_id as mm_supcust_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,make_brief mm_make_brief,total_amount as mm_sheet_amount,money_inout_flag as mm_money_inout_flag 
                           from sheet_sale_main sm 
                           left join 
                           (
                              select sheet_id order_sheet_id,sheet_no order_sheet_no from sheet_sale_order_main 
                              where company_id=~COMPANY_ID
                           ) om on sm.order_sheet_id=om.order_sheet_id 
                           where sm.company_id=~COMPANY_ID and sm.sheet_id in (VAR_sale_sheets_id)
                           UNION 
                              select supcust_id,sheet_id,sheet_no as mm_sheet_no,order_sheet_no,sm.order_sheet_id,supcust_id as mm_supcust_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,make_brief mm_make_brief,total_amount as mm_sheet_amount,money_inout_flag as mm_money_inout_flag 
                            from sheet_buy_main sm
                             left join 
                           (
                              select sheet_id order_sheet_id,sheet_no order_sheet_no from sheet_buy_order_main 
                              where company_id=~COMPANY_ID
                           ) om on sm.order_sheet_id=om.order_sheet_id
                              where sm.company_id=~COMPANY_ID and sm.sheet_id in (VAR_buy_sheets_id)
                           UNION
                              select supcust_id,sheet_id,sheet_no as mm_sheet_no,null order_sheet_no,null order_sheet_id,supcust_id as mm_supcust_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,make_brief mm_make_brief,total_amount as mm_sheet_amount,money_inout_flag as mm_money_inout_flag from sheet_prepay
                              where company_id=~COMPANY_ID and sheet_id in (VAR_prepay_sheets_id)
                           UNION
                              select supcust_id,sheet_id,sheet_no as mm_sheet_no,null order_sheet_no,null order_sheet_id,supcust_id as mm_supcust_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,make_brief mm_make_brief,total_amount as mm_sheet_amount,money_inout_flag as mm_money_inout_flag from sheet_fee_out_main
                              where company_id=~company_id and sheet_id in (VAR_fee_sheets_id)
            ) a left join info_supcust b on a.supcust_id=b.supcust_id and b.company_id=~company_id
)
s on t.mm_sheet_id=s.sheet_id and t.m_sheet_type = s.m_sheet_type
                            
                    ";
            }
        }
        public override async Task BeforeLoad(CMySbCommand cmd, string companyID, string sheetID)
        {
            List<System.Dynamic.ExpandoObject> lstSheets = await CDbDealer.GetRecordsFromSQLAsync($"select mm_sheet_id,m_sheet_type from sheet_get_bulk_arrears_detail where company_id={companyID} and sheet_id in ({sheetID});", cmd);
            Variables = new Dictionary<string, string>() {
                { "sale_sheets_id","null" },{ "buy_sheets_id","null" },{ "prepay_sheets_id","null" },{ "fee_sheets_id","null" }

            };

            foreach (dynamic sht in lstSheets)
            {
                string mmSheetsID = "";
                string key = "";
                switch (sht.m_sheet_type)
                {
                    case "X":
                    case "T":
                        key = "sale_sheets_id"; break;
                    case "CG":
                    case "CT":
                        key = "buy_sheets_id"; break;
                    case "YS":
                    case "YF":
                    case "DH":
                        key = "prepay_sheets_id"; break;
                    case "SR":
                    case "ZC":
                        key = "fee_sheets_id"; break;
                }
                if (this.Variables.ContainsKey(key))
                {
                    mmSheetsID = this.Variables[key];
                }
                if (mmSheetsID != "") mmSheetsID += ",";
                mmSheetsID += sht.mm_sheet_id;
                this.Variables[key] = mmSheetsID;
            }
        }
        public SheetGetBulkArrears(LOAD_PURPOSE loadPurpose) : base("sheet_get_bulk_arrears_main", "sheet_get_bulk_arrears_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_GET_BULK_MONEY;
            ConstructFun();
        }
        public SheetGetBulkArrears() : base("sheet_get_bulk_arrears_main", "sheet_get_bulk_arrears_detail", LOAD_PURPOSE.SHOW)
        {
            sheet_type = SHEET_TYPE.SHEET_GET_BULK_MONEY;
            ConstructFun();
        }

        protected override void InitForSave()
        {
            base.InitForSave();
            if (getter_id == "") getter_id = OperID;
            if (approver_id == "") approver_id = OperID;

        }

        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            //if (supcust_id == "") return "必须指定客户";
            //if (getter_id == "" && IsFromWeb) return "必须指定业务员";
            if (payway1_id == "") return "必须指定支付方式";
            if (SheetRows.Count == 0) return "单据不存在明细行";
            if (Math.Abs(now_pay_amount -payway1_amount - payway2_amount -payway3_amount)>0.05m)
            {
                string payways = payway1_name;
                if (payway2_name != "")
                {
                    payways += ",";
                    payways += payway2_name;
                }
                if (payway3_name != "")
                {
                    payways += ",";
                    payways += payway3_name;
                }
                return payways + "支付的金额和本次应该支付的金额不一致";
            }
            decimal total_now_pay_amount = 0;
            decimal total_now_disc_amount = 0;
            decimal total_now_left_amount = 0;
            foreach (SheetRowBulkArrears row in SheetRows)
            {
                total_now_pay_amount += row.now_pay_amount;
                total_now_disc_amount += row.now_disc_amount;
                total_now_left_amount += row.left_amount;
                var row_left_amount = row.sheet_amount - row.paid_amount - row.disc_amount - row.now_disc_amount - row.now_pay_amount;
                if (Math.Abs(row_left_amount - row.left_amount) > 0.1m) 
                      return $"单据{row.mm_sheet_no}的尚欠金额与实际尚欠不等";
                //if (!IsImported && row.now_pay_amount / row.sheet_amount < 0 )
                //{
                //   if( row.sheet_amount > 0)  return $"单据{row.mm_sheet_no}的本次支付金额必须是正数";
                //   else return $"单据{row.mm_sheet_no}的本次支付金额必须是负数";
                //}
                /*if (!IsImported && row.now_disc_amount * row.sheet_amount < 0)
                {
                   if (row.sheet_amount > 0) return $"单据{row.mm_sheet_no}的本次优惠金额必须是正数"; 
                   else return $"单据{row.mm_sheet_no}的本次优惠金额必须是负数";
                }*/
            }

            if (Math.Abs(total_now_pay_amount - now_pay_amount) > 0.1m) return "明细行本次支付合计与总的本次支付不等";
          
            if (Math.Abs(total_now_disc_amount - now_disc_amount) > 0.1m) return "明细行本次优惠合计与总的本次优惠不等";

            if (Math.Abs(total_now_left_amount - left_amount) > 0.1m) return "明细行尚欠合计与总的尚欠不等";
  

            return "OK";
        }

        protected override async Task<string> CheckSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSheetValid(cmd);
            if (check != "OK") return check;


            List<SheetRowBulkArrears> lstToRemove = new List<SheetRowBulkArrears>();
            sheet_amount = 0;
            paid_amount = 0;
            disc_amount = 0;
            left_amount = 0;

            foreach (SheetRowBulkArrears row in SheetRows)
            {
                if (row.now_disc_amount == 0 && row.now_pay_amount == 0)
                {
                    lstToRemove.Add(row);
                }
                else
                {
                    paid_amount += row.paid_amount;
                    disc_amount += row.disc_amount;
                    left_amount += row.left_amount;
                    sheet_amount += row.sheet_amount;
                }
            }

            foreach (var row in lstToRemove)
            {
                SheetRows.Remove(row);
            }

            return "OK";
        }
        
        public override string GetSheetCharactor()
        {
            string res =  this.company_id + "_" + this.sheet_id + "_";
            foreach (var row in SheetRows)
            {
                res += row.mm_sheet_id+"_";
            }
            return res;
        }
        protected override async Task<string> CheckForRed(CMySbCommand cmd)
        {
            return await CheckForRed_MoneySheet(cmd);
        }
        class CInfoForApprove : CInfoForApproveBase
        {
            //public string ArrearBalance = "";
            public string subID = "";
            public float ChangeBal = 0;
            public float Balance = 0;
            public float paidAmount = 0;
            public float discAmount = 0;
            //public string ErrorMsg = "";//基类里已有ErrMsg,这里不需要再定义一个,两个就容易混淆，造成BUG
            //  public bool BalanceExist = false;

            public Dictionary<string, SellerArrearBalance> SellersBalance = new Dictionary<string, SellerArrearBalance>();
            public List<Subject> PrepaySubjects = new List<Subject>();
            public List<Subject> PaywaysInfo = new List<Subject>();

        }
        protected class Subject
        {
            public string sub_id { get; set; }
            public string sub_name { get; set; }
            public string balance { get; set; }
            public string sub_type { get; set; }
        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            string sql;
            //sql = GetSqlForArrearsQQ(supcust_id, getter_id);
            //QQ.Enqueue("arrear_balance", sql);
            //sql = $"select balance from arrears_balance where company_id={company_id} and supcust_id={supcust_id}";
            //QQ.Enqueue("arrears_balance", sql);
            sql = $"select sub_id from cw_subject where company_id={company_id} and sub_type='QK'";
            QQ.Enqueue("sub_id", sql);
            string sub_ids = payway1_id;
            if (this.payway1_name == "预收款")
            {

            }
            if (this.sheet_no == "SK230608197001")
            {

            }
           if (payway2_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway2_id;
            }
            if (payway3_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway3_id;
            }
            if (sub_ids != "")
            {
                //sql = $"select s.sub_id,sub_name,balance,sub_type from cw_subject s left join (select sub_id,balance from prepay_balance where supcust_id={supcust_id}) b on s.sub_id=b.sub_id  where s.sub_id in (" + sub_ids + ") and sub_type in ('YS','YF');";
                //QQ.Enqueue("prepay_sub", sql);
                sql = $"select sub_id, sub_type, sub_name from cw_subject where company_id={company_id} and sub_id in ({sub_ids});";
                QQ.Enqueue("payway_type", sql);
            }
        
           // var mmSheetIDs = "";
            sql = "";
			if (SheetRows.Count == 0)
			{
                throw new Exception("单据没有明细行");
			}
            foreach (var row in SheetRows)
            { 
                if (sql != "") sql += " union ";
                string mmSheetTable = GetTableFromSheetType(row.m_sheet_type);
                if(!mmSheetTable.IsValid())
				{
                    throw new Exception($"单据类型" + row.m_sheet_type+"是未知单据类型");
				}
                string flag = "";
                if (sheet_type == SHEET_TYPE.SHEET_PAY_MONEY) flag = "*(-1)";
                string sellerFld = "seller_id";
                if (",ZC,SR,DH,YS,YF,".Contains("," + row.m_sheet_type + ",")) sellerFld = "getter_id seller_id";
               
                sql += $"select sheet_id,sheet_no,{sellerFld},red_flag,paid_amount*money_inout_flag {flag} as paid_amount ,disc_amount * money_inout_flag {flag} as disc_amount from {mmSheetTable} where company_id = {company_id} and sheet_id = {row.mm_sheet_id}";
                //  mmSheetIDs += row.mm_sheet_id; * money_inout_flag as paid_amount;;* money_inout_flag as disc_amount 
            }
            sql = @$"
select tb.*,o.oper_name seller_name,o.seller_max_arrears,aba.auxiliary_balance seller_balance from ({sql}) tb 
left join info_operator o on tb.seller_id=o.oper_id and o.company_id={company_id}
left join arrears_balance_auxiliary aba on tb.seller_id=aba.auxiliary_id and aba.auxiliary_type='seller' and aba.company_id={company_id}
;";
   
          //  sql = $"selecst sheet_id,paid_amount,disc_amount from {mmSheetTable} where company_id = {company_id} and sheet_id in ({mmSheetIDs});";
            QQ.Enqueue("mm_sheet", sql);
   //         if (!IsImported)
			//{
   //             SetQQForWeChatInfo(QQ, supcust_id);
   //         }
            
        }
        class SellerArrearBalance
        {
            public string seller_name = "";
            public decimal max_arrears = 0m;
            public decimal balance = 0m;
            public decimal change_balance = 0m;
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            
            bool isRed = false;
            if (bForRed) isRed = true;
            if (sqlName == "arrears_balance")
            {
                //DealArrearReadQQ(dr, info, left_amount, supcust_id);
                dynamic record = CDbDealer.Get1RecordFromDr(dr, false);
                if (record != null)
                {
                   // info.BalanceExist = true;
                    info.ArrearsBalance = record.balance;
                }
            }
            else if (sqlName == "sub_id")
            {
                dynamic record = CDbDealer.Get1RecordFromDr(dr, false);

                if (record != null)
                {
                    info.subID = record.sub_id;
                }
            }
            else if (sqlName == "prepay_sub")
            {
                info.PrepaySubjects = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
            else if (sqlName == "mm_sheet")
            {
                List<System.Dynamic.ExpandoObject> sheetList = CDbDealer.GetRecordsFromDr(dr, false);
                if (sheetList != null  )
                {                   
                     
                    foreach (var row in SheetRows)
                    {
                        dynamic queryRow = sheetList.Find(queryRow => ((dynamic)queryRow).sheet_id == row.mm_sheet_id);
                        if (queryRow != null)
                        {
                            if (!isRed)
                            {
                                if (queryRow.red_flag != "")
                                {
                                    info.ErrMsg = $"单据{queryRow.sheet_no}已被红冲, 请删除此单";
                                    return;
                                }

                                decimal paid_amount = CPubVars.ToDecimal(queryRow.paid_amount);
                                decimal disc_amount = CPubVars.ToDecimal(queryRow.disc_amount);

                                decimal nDiff = 0.1m;
                                if(queryRow.sheet_no== "XS22042825008")
								{

								}
                                if (IsImported) nDiff = 3m; //导入舟谱数据时差异会比较大
                                if (Math.Abs(paid_amount - row.paid_amount) > nDiff)
                                {
                                    info.ErrMsg = $"单号  {queryRow.sheet_no}   的已支付金额已经由{row.paid_amount}变为{paid_amount},请重新开此收款单";
                                    if (SheetType == "FK") info.ErrMsg = $"单号{queryRow.sheet_no}的已支付金额已经{row.paid_amount}变为{paid_amount},请重新开此付款单";
                                    return;
                                }

                                if (Math.Abs(disc_amount - row.disc_amount) > nDiff)
                                {
                                    info.ErrMsg = "";
                                    info.ErrMsg = $"单{queryRow.sheet_no}的已优惠金额已由{row.disc_amount}变为{disc_amount},请重新开此收款单";
                                    if (SheetType == "FK") info.ErrMsg = info.ErrMsg.Replace("收款单", "付款单");
                                    return;
                                }
                            }

                            if (queryRow.seller_max_arrears != "")
                            {
                                SellerArrearBalance bal;
                                if (!info.SellersBalance.ContainsKey(queryRow.seller_id))
                                {
                                    bal = new SellerArrearBalance();
                                    info.SellersBalance.Add(queryRow.seller_id, bal);
                                    bal.seller_name = queryRow.seller_name;
                                    if (queryRow.seller_balance != "")
                                    {
                                        bal.balance = CPubVars.ToDecimal(queryRow.seller_balance); 
                                        bal.max_arrears = CPubVars.ToDecimal(queryRow.seller_max_arrears);
                                    }
                                }
                                else
                                {
                                    bal = info.SellersBalance[queryRow.seller_id]; 
                                }
                                bal.change_balance += -money_inout_flag * (row.now_pay_amount + row.now_disc_amount);
                               
                            }
                        }
                    }
                    if (red_flag == "2")
                    {
                        foreach (var kp in info.SellersBalance)
                        {
                            var bal = kp.Value;
                            var new_balance = bal.balance + bal.change_balance;
                            if (new_balance > bal.max_arrears)
                            {
                                info.ErrMsg = $"业务员{bal.seller_name}欠款超出了最大限额{bal.max_arrears}";
                            }
                        }
                    }
                   
                }
            }
            else if (sqlName == "payway_type")
            {
                info.PaywaysInfo = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }

            ReadQQDataForWeChatInfo(sqlName, dr, info);
        }
        private string GetTableFromSheetType(string sheetType)
        {
            string mmSheetTable = "";
            switch (sheetType)
            {
                case "ZC": case "SR": mmSheetTable = "sheet_fee_out_main"; break;
                case "X":case "T":  mmSheetTable = "sheet_sale_main"; break;
                case "CG": case "CT": mmSheetTable = "sheet_buy_main"; break;
                case "YS": case "YF": case "DH": mmSheetTable = "sheet_prepay"; break;

            }
            return mmSheetTable;
        }
       
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            string preSql = "";
            string redSql = "";
            string sql = "";
            string son_sheet_id = "";
            string sError = "";
            //CInfoForApprove info1 = (CInfoForApprove)info1;
            //info.ChangeBal *= (-1);getApproveSQL那已经计算好了，这里不必要再乘以-1了

            CInfoForApprove info = (CInfoForApprove)info1;

            //if (red_flag == "2")
            //{
            //    CMySbTransaction tran = null;
            //    sql = @$"select son_sheet_no from sheet_get_bulk_arrears_main where company_id = {company_id} and sheet_no = '{sheet_no}' and sheet_type = '{SheetType}';";
            //    cmd.CommandText = sql;
            //    try
            //    {
            //        object ov="";// = await cmd.ExecuteScalarAsync();

            //        CMySbDataReader dr = await cmd.ExecuteReaderAsync();
            //        if (dr.Read())
            //        {
            //            son_sheet_no=CPubVars.GetTextFromDr(dr, "son_sheet_no");
            //        }
            //        dr.Close();
            //        //if(ov!=null && ov != DBNull.Value)
            //        {
            //      //      string tt = ov.ToString();
            //        }
            //        using (CMySbDataReader reader = await cmd.ExecuteReaderAsync())
            //        {
            //            // 假设 resultList 是一个预定义的列表，用于存储查询结果
            //            string resultList = "";

            //            // 遍历 DbDataReader 中的记录
            //            while (await reader.ReadAsync())
            //            {
            //                // 假设 YourResultType 是一个类，包含了数据库表的字段作为属性
            //                string result = "";
            //                {
            //                    // 假设 PropertyName 是 YourResultType 的一个属性，对应数据库表的某个字段
            //                    result = reader[son_sheet_no"] ;// 请确保 "ColumnName" 是实际的列名
            //                };

            //                resultList+=result;
            //            }

            //            // 现在 resultList 包含了查询的结果，可以在程序中使用
            //        }

            //        string operKey = this.OperKey;
            //        string redBrief = "";
            //        Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            //        SheetGetArrears sheet = new SheetGetArrears(SHEET_GET_ARREARS.EMPTY, LOAD_PURPOSE.SHOW);
            //        if (sheet_no != "")
            //        {
            //            if (ov != null && ov != DBNull.Value)
            //            {
            //                string resultString = ov.ToString().Trim(); // 去除首尾空白字符
            //                if (!string.IsNullOrEmpty(resultString)) // 确保字符串不为空
            //                {
            //                    string[] splitResults = resultString.Split(','); // 分割字符串

            //                    // 过滤掉空字符串
            //                    string[] nonEmptyResults = splitResults.Where(s => !string.IsNullOrEmpty(s)).ToArray();

            //                    if (nonEmptyResults.Length > 0)
            //                    {
            //                        sheet_id = nonEmptyResults[0];
            //                    }
            //                    for (int i = 0; i < nonEmptyResults.Length; i++)
            //                    {
            //                        sheet_id = nonEmptyResults[i];
            //                        sheet._httpClientFactory = this._httpClientFactory;
            //                        string msg = await sheet.Red(cmd, companyID, sheet_id, operID, redBrief);
            //                        string result = msg == "" ? "OK" : "Error";
            //                        if (msg != "") result = "Error";
            //                        return;
            //                    }
            //                }
            //            }
            //        }

            //        if (tran != null) tran.Commit();
            //    }
            //    catch (Exception e)
            //    {
            //        if (tran != null) tran.Rollback();
            //        string errMsg = $"In Save,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}";
            //        NLogger.Error(errMsg);
            //        MyLogger.LogMsg(errMsg, company_id);
            //        sError = "红冲发生错误";
            //    }
            //}
            if (red_flag == "2")
            {

                if (!string.IsNullOrEmpty(this.son_sheet_id)) // 确保字符串不为空

                //CMySbTransaction tran = null;
                sql = @$"select son_sheet_no from sheet_get_bulk_arrears_main where company_id = {company_id} and sheet_no = '{sheet_no}' and sheet_type = '{SheetType}';";
                cmd.CommandText = sql;
                try

                {
                    string operKey = this.OperKey;
                    string redBrief = "";
                    Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                    SheetGetArrears sheet = new SheetGetArrears(SHEET_GET_ARREARS.EMPTY, LOAD_PURPOSE.SHOW);
                    string[] splitResults = this.son_sheet_id.Split(','); // 分割字符串

                    // 过滤掉空字符串
                    string[] nonEmptyResults = splitResults.Where(s => !string.IsNullOrEmpty(s)).ToArray();
                    for (var i = 0; i < nonEmptyResults.Length; i++)
                    {
                        sheet_id = nonEmptyResults[i];
                        sheet._httpClientFactory = this._httpClientFactory;
                        string msg = await sheet.Red(cmd, companyID, sheet_id, operID, redBrief,false);
                        string result = msg == "" ? "OK" : "Error";
                        if (msg != "")
                        {
                            if (msg != "该单据已被红冲，不能再次红冲")
                            {

                                result = "Error";
                                return;
                            }
                        }
                    }
                }


                    //if (tran != null) tran.Commit();

                catch (Exception e)
                {
                   // if (tran != null) tran.Rollback();
                    string errMsg = $"In Save,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}";
                    NLogger.Error(errMsg);
                    MyLogger.LogMsg(errMsg, company_id);
                    sError = "红冲发生错误";
                }

            }
            else
            {
                Dictionary<string, SheetGetArrears> dicSheets = new Dictionary<string, SheetGetArrears>();
                foreach (SheetRowBulkArrears row in this.SheetRows)
                {
                    SheetGetArrears sheet = null;
                    if (dicSheets.ContainsKey(row.supcust_id))
                    {
                        sheet = dicSheets[row.supcust_id];
                    }
                    else
                    {
                        sheet = new SheetGetArrears();
                        sheet.Init();
                        sheet.supcust_id = row.supcust_id;
                        sheet.sup_name = row.sup_name;
                        sheet.OperID = this.OperID;
                        sheet.OperKey = this.OperKey;
                        sheet.getter_id = this.getter_id;
                        sheet.getter_name = this.getter_name;
                        sheet.happen_time = this.happen_time;
                        sheet.company_id = this.company_id;
                        sheet.payway1_id = this.payway1_id;
                        sheet.payway1_name = this.payway1_name;
                        sheet.now_pay_amount = 0;
                        sheet.now_disc_amount = 0;
                        sheet.left_amount = 0;
                        sheet.payway2_id = this.payway2_id;
                        sheet.payway3_id = this.payway3_id;
                        sheet.payway1_amount = 0;
                        sheet.payway2_amount = 0;
                        sheet.payway3_amount = 0;
                        dicSheets.Add(row.supcust_id, sheet);
                    }
                    SheetRowArrears aRow = new SheetRowArrears();
                    aRow.disc_amount = row.disc_amount;
                    aRow.left_amount = row.left_amount;
                    aRow.paid_amount = row.paid_amount;
                    aRow.sheet_amount = row.sheet_amount;
                    aRow.mm_make_brief = row.mm_make_brief;
                    aRow.now_pay_amount = row.now_pay_amount;
                    aRow.now_disc_amount = row.now_disc_amount;
                    aRow.left_amount = row.left_amount;
                    aRow.m_sheet_type = row.m_sheet_type;
                    aRow.mm_sheet_id = row.mm_sheet_id;
                    sheet.now_pay_amount += row.now_pay_amount;
                    sheet.now_disc_amount += row.now_disc_amount;
                    sheet.left_amount += row.left_amount;
                    sheet.payway1_amount += this.payway1_amount * row.now_pay_amount / this.now_pay_amount;
                    sheet.payway2_amount += this.payway2_amount * row.now_pay_amount / this.now_pay_amount;
                    sheet.payway3_amount += this.payway3_amount * row.now_pay_amount / this.now_pay_amount;
                    sheet.SheetRows.Add(aRow);

                }

                foreach (var kp in dicSheets)
                {
                    SheetGetArrears sheet = kp.Value;
                    string msg = await sheet.SaveAndApprove(cmd, false);
                    if (msg != "")
                    {
                        info.ErrMsg = msg;
                        break;
                    }
                    son_sheet_id += sheet.sheet_id;
                    son_sheet_id += ",";
                }
                sql = @$"update sheet_get_bulk_arrears_main set son_sheet_id = '{son_sheet_id}' where company_id = {company_id} and sheet_no = '{this.sheet_no}' and sheet_type = '{SheetType}';";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
        
        }


        public override string GetWeChatMsgHead()
        {
            string sheetTypeName = "";
            string first = "";
            switch (this.SheetType) {
                case "SK" :  sheetTypeName = "收款单"; break;
            }
            switch (this.red_flag)
            {
                case "": first = $"您有新的【{sheetTypeName}】,来自【{this.company_name}】,请注意查收"; break;
                case "2": first = $"您的【{sheetTypeName}】【被红冲】,来自【{this.company_name}】,请注意查收"; break;
            }
            return first;
        }
        public override string GetWeChatMsgTail()
        {
            string remark = "";
            if (this.sheet_no != "") remark += "单据编号：" + this.sheet_no + "\n";
            return remark;
        }
        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting);

        }

        //public List<SheetRowBulkArrears> GetSheetDataBySheetNo(string sheetNo, CMySbCommand cmd)
        //{
        //    // 定义查询 SQL 语句
        //    string sql = @"
        //    SELECT 
        //        CustomerName, 
        //        OrderType, 
        //        OrderTime, 
        //        TotalAmount, 
        //        PaidAmount, 
        //        DiscountAmount 
        //    FROM 
        //        SalesOrder 
        //    WHERE 
        //        SheetNo = @SheetNo"; // 假设单号字段名为 SheetNo

        //    // 创建参数
        //    var parameters = new List<CMySbParameter>
        //{
        //    new CMySbParameter("@SheetNo", sheetNo)
        //};

        //    // 使用您的数据库访问代码执行查询
        //    // 这里假设您有一个方法叫做 ExecuteReaderAsync，它接受 SQL 语句和参数列表，并返回一个包含结果的列表
        //    var salesOrderList = await _cmd.ExecuteReaderAsync<List<SalesOrder>>(sql, parameters);

        //    return salesOrderList;
        //}

        /*public override async Task<JsonResult> ToVoucherRows(CMySbCommand cmd, string sheetID, SheetCwVoucher sheetCwVoucher, Dictionary<string, decimal> payways)
        {
            string subsID = "";
            string condi = $@"( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='1122' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='1122')  order by sub_code limit 1 )";//应收账款
            if (SheetType == "FK") condi = $@"( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='2202' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='2202')  order by sub_code limit 1 )";//应付账款
            int subLen = 1;
            if (payways.Count == 0)
            {
                if (payway1_id != "" && payway1_amount != 0) payways.Add(payway1_id, payway1_amount * money_inout_flag);
                if (payway2_id != "" && payway2_amount != 0) payways.Add(payway2_id, payway2_amount * money_inout_flag);
                if (payway3_id != "" && payway3_amount != 0) payways.Add(payway3_id, payway3_amount * money_inout_flag);
                if (now_disc_amount != 0) payways.Add("disc", now_disc_amount * money_inout_flag);
                if ((now_pay_amount + now_disc_amount) != 0) payways.Add("total", (now_pay_amount + now_disc_amount) * money_inout_flag);
            }
            if (payways == null || payways.Count == 0)
            {
                return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
            }
            foreach (var payway in payways)
            {
                if (payway.Key == "total") continue;
                else if (payway.Key == "left") continue;
                else if (payway.Key == "disc") condi += $@" union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_code=560304 )";
                else
                {
                    if (subsID != "") subsID = subsID + ",";
                    subsID += payway.Key;
                }
                subLen++;
            }
            string payCondi = "";
            if (subsID != "") payCondi += $"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_id in ({subsID}) )";
            
            string sql = $"{condi} {payCondi}";
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (records == null || records.Count < subLen) return new JsonResult(new { result = "Error", msg = "缺少生成凭证的相关科目，请添加" });
            if (records.Count > subLen) return new JsonResult(new { result = "Error", msg = "生成凭证的相关科目有重复科目代码，请修改" });

            foreach (var rec in records)
            {
                if (rec.status == null || rec.status == "") rec.status = 1;
                if (Convert.ToInt16(rec.status) == 0) return new JsonResult(new { result = "Error", msg = "相关科目已停用，请检查" });
                CwRowVoucher cwRow = new CwRowVoucher();
                cwRow.business_sheet_type = SheetType;
                cwRow.business_sheet_id = sheetID;
                cwRow.sub_id = rec.sub_id;
                cwRow.remark = SheetType == "SK" ? "收应收款" : "付应付款";
                if (red_flag == "2") cwRow.remark = SheetType == "SK" ? "红冲收应收款" : "红冲付应付款";
                decimal changeAmt = 0;
                foreach (var payway in payways)
                {
                    changeAmt = payway.Value;

                    if (payway.Key == rec.sub_id || (payway.Key == "disc" && rec.sub_code == "560304"))
                    {
                        if (changeAmt >= 0) cwRow.debit_amount = changeAmt.ToString();// SK正常开单: changeAmt>0  借payway，贷应收； FK开单: changeAmt<0  借应付账款，贷payway
                        else cwRow.credit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = changeAmt.ToString();
                        break;
                    }
                    else if ((rec.sub_code.ToString().Substring(0, 4) == "1122" || rec.sub_code.ToString().Substring(0, 4) == "2202") && payway.Key == "total") // 应收账款
                    {
                        if (changeAmt >= 0) cwRow.credit_amount = changeAmt.ToString();
                        else cwRow.debit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = (-1 * changeAmt).ToString();
                        break;
                    }
                }
                
                sheetCwVoucher.SheetRows.Add(cwRow);
            }

            return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
        }*/


    }
}
