﻿//using AspectCore.DynamicProxy;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.Models
{
    /*
    public class MyAspectAttribute : AbstractInterceptorAttribute
    {
        public override Task Invoke(AspectContext context, AspectDelegate next)
        {
            try
            {
                //方法调用之前
                Debug.WriteLine("Before");
                return context.Invoke(next);
            }
            catch (Exception)
            {
                //方法抛异常调用
                Debug.WriteLine("exception!");
                throw;
            }
            finally
            {
                //方法完成之后调用
                Debug.WriteLine("After");
            }
        }
      
    }
    public interface IMyAopTest
    {
        [MyAspect]
        int Add(int a, int b);
    }
    public class MyAopTest : IMyAopTest
    {
        public int Add(int a, int b)
        {
            return a + b;
        }
    }*/
}
