﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using NPOI.SS.Formula.Functions;
using ArtisanManage.Services.SheetService;
using ArtisanManage.MyCW;
using OBS.Model;
using ArtisanManage.Pages;
using ArtisanManage.WebAPI;
using ArtisanManage.YingJiangCommon.Controller.PromotionController;
using System.ComponentModel.Design;
using Org.BouncyCastle.Bcpg;

namespace ArtisanManage.MyJXC
{
    public class SheetRowPriceAllowance : SheetRowBase
    {
        public int uid { get; set; }
        public int boundindex { get; set; }
        public string uniqueid { get; set; }
        public int visibleindex { get; set; }
        public string batch_id { get; set; }
        public string produce_date { get; set; }
        public string batch_no { get; set; }
        public string branch_id { get; set; }
        public string branch_position { get; set; } // 注意类型可能需要根据实际情况调整为数字类型
        public decimal allocate_amount { get; set; }
        public int row_index { get; set; }
        [SaveToDB][FromFld] public string item_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_name { get; set; } = "";
        [SaveToDB][FromFld] public string unit_no { get; set; } = "";

        [SaveToDB][FromFld] public string unit_factor { get; set; } = "";

        [SaveToDB][FromFld] public string sale_qty { get; set; } = "";

        [SaveToDB][FromFld] public string quantity { get; set; } = "";
        //[SaveToDB][FromFld] public string subsidy_price { get; set; } = "";

        [SaveToDB][FromFld] public string remarks { get; set; } = "";
        //[SaveToDB][FromFld] public string sheet_rebate_price { get; set; } = "";
        [SaveToDB][FromFld] public string sheet_allowance_price { get; set; } = "";

        // [SaveToDB][FromFld] public string rebate_price { get; set; } = "";
        [SaveToDB][FromFld] public string allowance_price { get; set; } = "";

        [SaveToDB][FromFld] public string sub_amount { get; set; } = "";
        [SaveToDB][FromFld] public string sum_cost { get; set; } = "";
        [SaveToDB][FromFld] public string cost_amount { get; set; } = "";
        [SaveToDB][FromFld] public string cost_price { get; set; } = "";


        /* public string s_sheet_rebate_price { 
             get 
             {
                 if (sheet_rebate_price.IsValid() && unit_factor.IsValid())
                 {
                     return (Convert.ToDecimal(sheet_rebate_price) / Convert.ToDecimal(unit_factor)).ToString();
                 }
                 return "";
             } 

         }

         public string s_rebate_price { 
             get 
             {
                 if (rebate_price.IsValid() && unit_factor.IsValid())
                 {
                     return (Convert.ToDecimal(rebate_price) / Convert.ToDecimal(unit_factor)).ToString();
                 }
                 return "";
             } 
         }*/
        public string s_sheet_allowance_price
        {
            get
            {
                if (sheet_allowance_price.IsValid() && unit_factor.IsValid())
                {
                    return (Convert.ToDecimal(sheet_allowance_price) / Convert.ToDecimal(unit_factor)).ToString();
                }
                return "";
            }

        }

        public string s_allowance_price
        {
            get
            {
                if (allowance_price.IsValid() && unit_factor.IsValid())
                {
                    return (Convert.ToDecimal(allowance_price) / Convert.ToDecimal(unit_factor)).ToString();
                }
                return "";
            }
        }
        public string sale_sheet_nos { get; set; } = "";
        public string sale_sheet_ids { get; set; } = "";
        //public string quantity_unit_conv { get; set; } = "";
        public string sale_qty_unit { get; set; } = "";
        [SaveToDB][FromFld]public string other_info
        {
            get
            {
                IDictionary<string, object> d = new ExpandoObject();
                if (sale_sheet_nos != "") d["saleSheetNos"] = sale_sheet_nos;
                if (sale_sheet_ids != "") d["saleSheetIds"] = sale_sheet_ids;
                //if (quantity_unit_conv != "") d["quantity_unit_conv"] = quantity_unit_conv;
                if (sale_qty_unit != "") d["sale_qty_unit"] = sale_qty_unit;
                if (d.Count > 0)
                {
                    return JsonConvert.SerializeObject(d);
                }
                return "";
            }
            set
            {
                if (value.IsValid())
                {
                    dynamic d = JsonConvert.DeserializeObject(value);
                    if (d.saleSheetNos != null) sale_sheet_nos = d.saleSheetNos;
                    if (d.saleSheetIds != null) sale_sheet_ids = d.saleSheetIds;
                    //if (d.quantity_unit_conv != null) quantity_unit_conv = d.quantity_unit_conv;
                    if (d.sale_qty_unit != null) sale_qty_unit = d.sale_qty_unit;

                }
            }
        }



        
    }
    //public enum SHEET_RETURN
    //{
    //    EMPTY,
    //    NOT_RETURN,
    //    IS_RETURN
    //}

    public class SheetPriceAllowance : SheetBase<SheetRowPriceAllowance>
    {
        [SaveToDB][FromFld] public string supcust_id { get; set; } = "";
        [SaveToDB][FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_name { get; set; } = "";
        [SaveToDB][FromFld] public string start_time { get; set; } = "";
        [SaveToDB][FromFld] public string end_time { get; set; } = "";
        [SaveToDB][FromFld] public override int money_inout_flag { get; set; }
        [SaveToDB][FromFld] public override decimal total_amount { get; set; }


        [SaveToDB][FromFld] public string payway1_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway1_amount { get; set; }
        [SaveToDB][FromFld] public string payway2_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway2_amount { get; set; }

        [SaveToDB][FromFld] public string payway3_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway3_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway3_amount { get; set; }

        [SaveToDB][FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB][FromFld] public decimal now_disc_amount { get; set; }
        [SaveToDB][FromFld] public decimal paid_amount { get; set; }
        [SaveToDB][FromFld] public decimal disc_amount { get; set; }
        [SaveToDB][FromFld] public string seller_id { get; set; } = "";

        [SaveToDB][FromFld] public string total_quantity { get; set; } = "";

        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";

        [SaveToDB][FromFld] public string fee_sub_id { get; set; } = "";
        [FromFld("cw.sub_name", LOAD_PURPOSE.SHOW)] public string fee_sub_name { get; set; } = "";

        [FromFld(LOAD_PURPOSE.SHOW)] public string zc_sheet_id { get; set; } = "";

        [FromFld(LOAD_PURPOSE.SHOW)] public string zc_sheet_no { get; set; } = "";




        protected class CInfoForApprovePriceAllowance : CInfoForApproveBase
        {

        }

        [JsonConstructor]  //if there is more constructor, it is necessary to set on one of them
        public SheetPriceAllowance(SHEET_RETURN sheetReturn, LOAD_PURPOSE loadPurpose) : base("sheet_price_allowance_main", "sheet_price_allowance_detail", loadPurpose)
        {
            ConstructFun();
            sheet_type = SHEET_TYPE.SHEET_PRICE_ALLOWANCE;
        }
        private void ConstructFun()
        {
            MainLeftJoin += @$"
LEFT JOIN (
	SELECT
		sheet_attribute ->> 'related_sheet_id' bc_sheet_id,
		sheet_attribute ->> 'related_sheet_no' bc_sheet_no,
		sheet_id zc_sheet_id,
		sheet_no zc_sheet_no 
	FROM
		sheet_fee_out_main 
	WHERE
		company_id = ~COMPANY_ID 
		AND red_flag IS NULL 
		AND approve_time IS NOT NULL 
		AND sheet_attribute IS NOT NULL 
		AND sheet_attribute ->> 'related_sheet_id' IS NOT NULL 
	) so ON T.sheet_id :: TEXT = so.bc_sheet_id
LEFT JOIN ( SELECT oper_id, oper_name AS seller_name FROM info_operator WHERE company_id = ~COMPANY_ID ) seller ON T.seller_id = seller.oper_id
LEFT JOIN ( SELECT oper_id, oper_name AS maker_name FROM info_operator WHERE company_id = ~COMPANY_ID ) maker ON T.maker_id = maker.oper_id
LEFT JOIN ( SELECT oper_id, oper_name AS approver_name FROM info_operator WHERE company_id = ~COMPANY_ID ) approver ON T.approver_id = approver.oper_id
LEFT JOIN info_supcust sup ON sup.company_id = T.company_id and t.supcust_id = sup.supcust_id
left JOIN cw_subject cw on cw.company_id = t.company_id and cw.sub_id = t.fee_sub_id
left join (select sub_id,sub_name as payway1_name,sub_type payway1_type,is_order payway1_is_order,usage payway1_usage from cw_subject where company_id=~COMPANY_ID) pw1 on t.payway1_id=pw1.sub_id
left join (select sub_id,sub_name as payway2_name,sub_type payway2_type,is_order payway2_is_order,usage payway2_usage from cw_subject where company_id=~COMPANY_ID) pw2 on t.payway2_id=pw2.sub_id
left join (select sub_id,sub_name as payway3_name,sub_type payway3_type,is_order payway3_is_order,usage payway3_usage from cw_subject where company_id=~COMPANY_ID) pw3 on t.payway3_id=pw3.sub_id

";

            DetailLeftJoin += $@"
LEFT JOIN info_item_prop ip on ip.company_id = t.company_id and ip.item_id = t.item_id";
        }
        public SheetPriceAllowance(LOAD_PURPOSE loadPurpose) : base("sheet_price_allowance_main", "sheet_price_allowance_detail", loadPurpose)
        {
            ConstructFun();
            sheet_type = SHEET_TYPE.SHEET_PRICE_ALLOWANCE;
        }
        public SheetPriceAllowance() : base("sheet_price_allowance_main", "sheet_price_allowance_detail", LOAD_PURPOSE.SHOW)
        {
            ConstructFun();
            sheet_type = SHEET_TYPE.SHEET_PRICE_ALLOWANCE;
        }

        //public override string Init()
        //{
        //    base.Init();
        //    //foreach (var row in SheetRows)
        //    //{
        //    //    if (row.trade_type == null || row.trade_type == "") row.trade_type = SheetType;

        //    //    if (row.trade_type== "KS") row.inout_flag = 0;
        //    //}
        //}



        //销售过程中，主要影响：货值-----> 数量改变，货值就会改变
        //                  质疑----->  数量的变化，是否在库存的范围内，即当库存由非负->负的过程，或者库存由负->非负的过程，都会影响（包含销售退货红冲所有情况）
        //                  商品质疑表中  ----->  若商品质疑状态变化，则记录  若商品库存通过单据变化，即当库存由非负->负的过程，或者库存由负->非负的过程，便会插入一条记录




        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprovePriceAllowance();
            base.GetInfoForApprove_SetQQ(QQ);

        }


        // 第二步，销售单保存过程
        string NotRememberPriceBriefs = "";
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprovePriceAllowance();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprovePriceAllowance();
            CInfoForApprovePriceAllowance info = (CInfoForApprovePriceAllowance)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);


        }

  

        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {

            DateTime today = DateTime.Today;
            string nowDate = today.ToString("yyyy-MM-dd");
            if (start_time.IsInvalid())
            {
                start_time = nowDate + " 00:00:00";
            }
            if (end_time.IsInvalid())
            {
                end_time = nowDate + " 23:59:59";
            }
            return await base.CheckSheetValid(cmd);
        }
        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {

            DateTime today = DateTime.Today;
            string nowDate = today.ToString("yyyy-MM-dd");
            if (start_time.IsInvalid())
            {
                start_time = nowDate + " 00:00:00";
            }
            if (end_time.IsInvalid())
            {
                end_time = nowDate + " 23:59:59";
            }
            return await base.CheckSaveSheetValid(cmd);
        }

        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            CInfoForApprovePriceAllowance info = (CInfoForApprovePriceAllowance)info1;

            if (FIXING_ARREARS) return "";

            string sql = "";
            if (!IsImported)
            {
                sql = base.GetApproveSQL(info1);
            }


            //#region update borrowed items
            //var mergedTradeRows = MergeTradeSheetRows(info.SheetRows);
            //string borrowSql = "";
            //foreach (var row in mergedTradeRows)
            //{
            //    decimal borrowChangeQty = 0;
            //    var itemID = row.son_mum_item!="" ? row.son_mum_item : row.item_id;
            //    borrowChangeQty = CPubVars.ToDecimal(row.quantity);
            //    if (row.trade_type == "J" || row.trade_type == "H")
            //    {
            //        row.BorrowedQty += borrowChangeQty;
            //        if(row.trade_type=="H"&&row.BorrowedQty<0) info.ErrMsg = $"{row.item_name}使用的数量大于可还货库存";
            //        if (red_flag == "2" && row.BorrowedQty < 0) info.ErrMsg = $"{row.item_name}的剩余数量与当时借货数量不一致,红冲失败";
            //        //if (row.HasBorrowedQty) borrowSql += $"update borrowed_cust_items set borrowed_qty = borrowed_qty+({borrowChangeQty}) where company_id={company_id} and cust_id = {supcust_id} and item_id={itemID};";
            //        //else 
            //            borrowSql += @$"insert into borrowed_cust_items(company_id,cust_id,item_id,borrowed_qty) values({company_id},{supcust_id},{itemID},{borrowChangeQty})
            //                             on conflict(company_id,cust_id,item_id) do update set borrowed_qty = borrowed_cust_items.borrowed_qty + {borrowChangeQty};";
            //    }
            //}
            //sql += borrowSql;
            //#endregion

            //if (IsImported) return sql;

            //#region update recent price
            //if (red_flag != "2")
            //{
            //    if (info.UnitPriceRows.Count() > 0)
            //    {
            //        foreach (var row in info.UnitPriceRows)
            //        {
            //            string origPrice = row.orig_price.IsValid() ? row.orig_price : "null";

            //            string recent_price = row.real_price.ToString();
            //            string recent_orig_price = origPrice;
            //            sql += $@"insert into client_recent_prices (company_id,supcust_id,item_id,unit_no,recent_price,recent_orig_price,happen_time) values ({company_id},{supcust_id},{row.item_id},'{row.unit_no}',{recent_price},{recent_orig_price},'{happen_time}') 
            //        on conflict(company_id,supcust_id,item_id,unit_no) do update set recent_price={recent_price},recent_orig_price={recent_orig_price},happen_time = '{happen_time}' where (client_recent_prices.happen_time is null or client_recent_prices.happen_time <= '{happen_time}');";

            //        }
            //    }

            //}
            //else
            //{
            //    //DateTime tm = Convert.ToDateTime(happen_time);
            //    /* foreach(var row in this.SheetRows)
            //     {
            //         if (row.real_price != 0 && row.last_time_price.IsValid() && row.last_time_price!="0")
            //         {
            //             sql += $"update client_recent_prices set recent_price={row.last_time_price} where company_id={company_id} and client_id={this.supcust_id} and item_id={row.item_id} and happen_time='{this.happen_time}';";

            //         }
            //     }*/

            //}
            //#endregion

            //#region update recent produce date
            //string sqlUpdateRecentProduceDate = "";
            //string sNow = CPubVars.GetDateText(DateTime.Now);
            //foreach (var row in info.SheetRows)
            //{
            //    //if (!string.IsNullOrEmpty(row.virtual_produce_date))
            //    if (row.virtual_produce_date != null)
            //    {
            //        sqlUpdateRecentProduceDate += $"insert into item_recent_produce_date (company_id,item_id,produce_date,happen_time) values ({company_id},{row.item_id},'{row.virtual_produce_date}','{sNow}') on conflict(company_id,item_id) do update set produce_date='{row.virtual_produce_date}',happen_time='{sNow}';";
            //    }
            //}
            //sql += sqlUpdateRecentProduceDate;
            //#endregion


            //#region 扣减订单占用库存
            //if (!info.OrderSheetIsImported)
            //{
            //    if (sheet_type == SHEET_TYPE.SHEET_SALE && order_sheet_id != "" && info.OrderSheetRows != null)
            //    {
            //        var orderRows = (from row in info.OrderSheetRows where row.quantity > 0 select row).ToList();

            //        SheetSaleOrder orderSheet = new SheetSaleOrder();
            //        orderSheet.SheetRows = orderRows;

            //        //var mergedRows = orderSheet.MergeSheetRows(orderRows);
            //        var mergedRows = orderSheet.MergeSheetRowsByBatchAndItem(orderRows);

            //        foreach (dynamic row in mergedRows)
            //        {
            //            string s = "";
            //            string changeQty = "";

            //            //var qty = row.inout_flag * row.quantity1;
            //            var qty = -row.quantity;
            //            if (red_flag == "2") qty *= -1;
            //            changeQty = qty.ToString();

            //            if (changeQty == "-0") changeQty = "0";

            //            if (qty >= 0)
            //            {
            //                changeQty = "+" + qty.ToString();
            //            }
            //            else
            //            {
            //                changeQty = qty.ToString();
            //            }

            //            if (string.IsNullOrEmpty(row.batch_id)) row.batch_id = "0";
            //            if (string.IsNullOrEmpty(row.branch_position)) row.branch_position = "0";
            //            if (string.IsNullOrEmpty(row.branch_id)) row.branch_id = branch_id;

            //            if (row.move_stock.ToLower() != "true")
            //            {
            //                s = $"insert into stock(company_id,branch_id,item_id,stock_qty,sell_pend_qty,batch_id,branch_position) values ({company_id},{row.branch_id},{row.item_id},0,{qty},{row.batch_id},{row.branch_position}) on conflict (company_id,branch_id,item_id,batch_id,branch_position) do update set sell_pend_qty=stock.sell_pend_qty{changeQty};";
            //                sql += s;
            //                row.NewSellPendQty = row.SellPendQty + qty;
            //            }
            //        }
            //    }
            //}


            //#endregion



            //#region 使用陈列协议
            //var mergedDispRows = MergeDispSheetRows(info.SheetRows);
            //foreach (SheetRowSale row in mergedDispRows)
            //{
            //    if (row.disp_flow_id != null && row.disp_flow_id != "")
            //    {
            //        var month_given = "month" + row.disp_month_id + "_given";
            //        decimal qty = row.quantity * row.inout_flag*(-1);
            //        if (row.disp_month_left - qty < -0.01m) info.ErrMsg = $"{row.item_name}使用数量超过相关陈列协议的剩余数量，请重新输入";
            //        else
            //        {
            //            //sql += $"update display_agreement_detail set {month_given}=COALESCE({month_given},0)+({qty}) where company_id = {company_id} and sheet_id = {row.disp_sheet_id} and flow_id = {row.disp_flow_id} and items_id like '%{row.item_id}%' and unit_no = '{row.unit_no}';";
            //            sql += $"update display_agreement_detail set {month_given}=COALESCE({month_given},0)+({qty}) where company_id = {company_id} and sheet_id = {row.disp_sheet_id} and flow_id = {row.disp_flow_id};";
            //        }
            //    }
            //}

            //#endregion
            //if (this.sheet_type == SHEET_TYPE.SHEET_SALE)
            //{
            //    if (red_flag =="2"){
            //        string new_recent_sale_time = CPubVars.GetDateText(info.new_recent_sale_time); 
            //        string realTime = $"{{\"lastSaleTime\":\"{new_recent_sale_time}\"}}";
            //        string realtimeSql = $"insert into realtime_supcust(company_id,supcust_id,realtime) values ({company_id},{supcust_id},'{realTime}') on conflict(company_id,supcust_id) do update set realtime=realtime_supcust.realtime||'{realTime}'::jsonb;";
            //        sql += realtimeSql;
            //    }
            //    else
            //    {
            //        string realTime = $"{{\"lastSaleTime\":\"{happen_time}\"}}";
            //        string realtimeSql = $"insert into realtime_supcust(company_id,supcust_id,realtime) values ({company_id},{supcust_id},'{realTime}') on conflict(company_id,supcust_id) do update set realtime=realtime_supcust.realtime||'{realTime}'::jsonb;";
            //        sql += realtimeSql;
            //    }

            //}

            //if (visit_id.IsValid())
            //{
            //    decimal amount = this.total_amount * money_inout_flag;
            //    string s = $"update sheet_visit set sale_amount=coalesce(sale_amount,0)+{amount} where company_id={company_id} and visit_id={visit_id};";
            //    sql += s;
            //}


            return sql;
        }


        //商品质疑表新增记录
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            await base.OnSheetIDGot(cmd, sheetID, info1);
            string sql = "";
            foreach (SheetRowPriceAllowance row in SheetRows)
            {
                if (row.sale_sheet_ids.IsValid())
                {
                    var s_price = row.s_sheet_allowance_price;
                    var allowance_price = row.allowance_price;
                    var allowance_quantity = row.quantity;
                    if (red_flag == "2")
                    {
                        s_price = "null";
                        allowance_price = "null";
                        allowance_quantity = "null";
                    }
                    sql += @$"update supplier_fee_detail set allowance_price ={s_price},allowance_amount={allowance_price},allowance_quantity = {allowance_quantity} where company_id ={company_id} and sale_sheet_id in ({row.sale_sheet_ids} ) and item_id = {row.item_id};";
                }
            }
            cmd.CommandText = sql;
            if (cmd.CommandText != "")
            {
                await cmd.ExecuteNonQueryAsync();
            }

            if (red_flag != "2")
            {
                SheetFeeOut foSheet = new SheetFeeOut(SHEET_FEE_OUT.NOT_OUT, LOAD_PURPOSE.SHOW);
                foSheet.company_id = company_id;
                foSheet.OperID = OperID;
                foSheet.OperKey = OperKey;
                foSheet.supcust_id = supcust_id;
                foSheet.sup_name = sup_name;
                //foSheet.order_source = "BC";
                foSheet.total_amount = total_amount;
                foSheet.payway1_id = payway1_id;
                foSheet.payway1_amount = payway1_amount;
                foSheet.payway1_name = payway1_name;
                foSheet.payway2_id = payway2_id;
                foSheet.payway2_amount = payway2_amount;
                foSheet.payway2_name = payway2_name;
                foSheet.now_pay_amount = now_pay_amount;
                foSheet.paid_amount = paid_amount;
                foSheet.now_disc_amount = now_disc_amount;
                foSheet.disc_amount = disc_amount;
                foSheet.money_inout_flag = 1;
                //foSheet.left_amount = total_amount - now_pay_amount - now_disc_amount;
                foSheet.getter_id = seller_id;
                foSheet.getter_name = seller_name;
                foSheet.related_sheet_id = sheet_id;
                foSheet.related_sheet_no = sheet_no;
                foSheet.related_sheet_type = "BT";
                foSheet.make_brief = "补贴单";
                SheetRowFeeOut foRow = new SheetRowFeeOut();
                foRow.fee_sub_id = fee_sub_id;
                foRow.fee_sub_name = fee_sub_name;
                foRow.fee_sub_amount = total_amount;
                foRow.remark = "其它收入";
                foSheet.SheetRows.Add(foRow);
                string msg = await foSheet.SaveAndApprove(cmd, false);
                if (msg == "")
                {
                    zc_sheet_id = foSheet.sheet_id;
                    zc_sheet_no = foSheet.sheet_no;
                }
                info1.ErrMsg = msg;
            }
          
        }
        public override string GetWeChatMsgHead()
        {
            string sheetTypeName = "";
            string first = "";
            switch (this.SheetType)
            {
                case "X": sheetTypeName = "销售单"; break;
                case "T": sheetTypeName = "退货单"; break;
            }
            switch (this.red_flag)
            {
                case "": first = $"您有新的【{sheetTypeName}】,来自【{this.company_name}】,请注意查收"; break;
                case "2": first = $"您的【{sheetTypeName}】【被红冲】,来自【{this.company_name}】,请注意查收"; break;
            }
            return first;
        }
        public override string GetWeChatMsgTail()
        {
            string remark = "";
            if (this.sheet_no != "") remark += "单据编号：" + this.sheet_no + "\n";

            return remark;
        }
        /*
           protected List<SheetRowSale> MergeOrderedSheetRows(List<SheetRowSale> rows)
           {
               Dictionary<string, SheetRowSale> rowsDict = new Dictionary<string, SheetRowSale>();
               foreach (SheetRowSale sheetRow in SheetRows)
               {
                   if (sheetRow.order_sub_id == "") continue;
                   string skey = sheetRow.item_id + "_" +  sheetRow.OrderPrice.ToString() + "_" + sheetRow.order_sub_id.ToString();
                   SheetRowSale curRow = null;
                   rowsDict.TryGetValue(skey, out curRow);
                   if (curRow == null)
                   { 
                       curRow = JsonConvert.DeserializeObject<SheetRowSale>(JsonConvert.SerializeObject(sheetRow)); 
                       curRow.quantity = sheetRow.quantity * sheetRow.unit_factor / sheetRow.OrderUnitFactor;
                       curRow.OrderedBalance = sheetRow.OrderedBalance;
                       curRow.OrderedQty = sheetRow.OrderedQty;
                       curRow.OrderUnitNo = sheetRow.OrderUnitNo;
                       curRow.OrderUnitFactor = sheetRow.OrderUnitFactor;
                       curRow.OrderPrice = sheetRow.OrderPrice; 
                       rowsDict.Add(skey, curRow);
                   }
                   else
                   {
                       curRow.quantity += sheetRow.quantity * sheetRow.unit_factor / sheetRow.OrderUnitFactor;
                       curRow.sub_amount += sheetRow.sub_amount;
                   }
               }
               List<SheetRowSale> newList = new List<SheetRowSale>();
               foreach (var k in rowsDict)
               {
                   newList.Add(k.Value);
               }
               return newList;

           }

           */

        protected List<SheetRowSale> MergeOrderedSheetRows(List<SheetRowSale> rows)
        {
            //Dictionary<string, SheetRowSale> rowsDict = new Dictionary<string, SheetRowSale>();
            //foreach (SheetRowSale sheetRow in SheetRows)
            //{
            //    if (sheetRow.order_sub_id == "") continue;
            //    string unitPrice =CPubVars.FormatMoney(sheetRow.real_price / sheetRow.unit_factor,2);
            //    string skey = sheetRow.item_id + "_" + unitPrice + "_" + sheetRow.order_sub_id.ToString();
            //    SheetRowSale curRow = null;
            //    rowsDict.TryGetValue(skey, out curRow);
            //    if (curRow == null)
            //    {
            //        curRow = JsonConvert.DeserializeObject<SheetRowSale>(JsonConvert.SerializeObject(sheetRow));
            //        curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
            //        curRow.real_price = curRow.real_price / curRow.unit_factor;


            //        //curRow.OrderedBalance = sheetRow.OrderedBalance;
            //        //curRow.OrderedQty = sheetRow.OrderedQty;
            //        // curRow.OrderUnitNo = sheetRow.OrderUnitNo;
            //        // curRow.OrderUnitFactor = sheetRow.OrderUnitFactor;
            //        // curRow.OrderPrice = sheetRow.OrderPrice;
            //        rowsDict.Add(skey, curRow);
            //    }
            //    else
            //    {
            //        curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
            //        curRow.sub_amount += sheetRow.sub_amount;
            //    }
            //}
            List<SheetRowSale> newList = new List<SheetRowSale>();
            //foreach (var k in rowsDict)
            //{
            //    newList.Add(k.Value);
            //}
            return newList;

        }

        protected List<SheetRowSale> MergeTradeSheetRows(List<SheetRowSale> rows)
        {
            //Dictionary<string, SheetRowSale> rowsDict = new Dictionary<string, SheetRowSale>();
            //foreach (SheetRowSale sheetRow in SheetRows)
            //{
            //    if (sheetRow.trade_type == "X" || sheetRow.trade_type == "T") continue;
            //    string skey = "";
            //    if (sheetRow.son_mum_item != "")
            //    {
            //        skey = sheetRow.son_mum_item + "_" + sheetRow.trade_type.ToString();
            //    }
            //    else
            //    {
            //        skey = sheetRow.item_id + "_" + sheetRow.trade_type.ToString();
            //    }
            //    SheetRowSale curRow = null;
            //    rowsDict.TryGetValue(skey, out curRow);
            //    if (curRow == null)
            //    {
            //        curRow = new SheetRowSale();
            //        curRow.item_id = sheetRow.item_id;
            //        curRow.item_name = sheetRow.item_name;
            //        curRow.unit_factor = sheetRow.unit_factor;
            //        curRow.quantity = sheetRow.unit_factor * sheetRow.quantity * sheetRow.inout_flag; //转化成小单位
            //        curRow.trade_type = sheetRow.trade_type;
            //        curRow.inout_flag = sheetRow.inout_flag;
            //        curRow.HasBorrowedQty = sheetRow.HasBorrowedQty;
            //        curRow.BorrowedQty = sheetRow.BorrowedQty;
            //        if (sheetRow.son_mum_item !="")
            //        {
            //            curRow.son_mum_item = sheetRow.son_mum_item;
            //        }
            //        rowsDict.Add(skey, curRow);
            //    }
            //    else curRow.quantity += sheetRow.quantity * sheetRow.unit_factor * sheetRow.inout_flag;

            //}
            List<SheetRowSale> newList = new List<SheetRowSale>();
            //foreach (var k in rowsDict)
            //{
            //    newList.Add(k.Value);
            //}
            return newList;

        }


        protected List<SheetRowSale> MergeDispSheetRows(List<SheetRowSale> rows)
        {
            //Dictionary<string, SheetRowSale> rowsDict = new Dictionary<string, SheetRowSale>();
            //foreach (SheetRowSale sheetRow in SheetRows)
            //{
            //    if (sheetRow.disp_flow_id != null && sheetRow.disp_flow_id == "") continue;
            //    string skey = sheetRow.disp_flow_id+'_'+sheetRow.disp_month_id;
            //    SheetRowSale curRow = null;
            //    rowsDict.TryGetValue(skey, out curRow);
            //    if (curRow == null)
            //    {
            //        curRow = new SheetRowSale();
            //        curRow.item_id = sheetRow.item_id;
            //        curRow.item_name = sheetRow.item_name;
            //        curRow.unit_no = sheetRow.unit_no;
            //        curRow.quantity = sheetRow.quantity;
            //        curRow.inout_flag = sheetRow.inout_flag;
            //        curRow.trade_type = sheetRow.trade_type;
            //        curRow.disp_flow_id = sheetRow.disp_flow_id;
            //        curRow.disp_month_id = sheetRow.disp_month_id;
            //        curRow.disp_sheet_id = sheetRow.disp_sheet_id;
            //        curRow.disp_month_left = sheetRow.disp_month_left;
            //        rowsDict.Add(skey, curRow);
            //    }
            //    else curRow.quantity += sheetRow.quantity;
            //}
            List<SheetRowSale> newList = new List<SheetRowSale>();
            //foreach (var k in rowsDict)
            //{
            //    newList.Add(k.Value);
            //}
            return newList;

        }




        /*public override async Task<JsonResult> ToVoucherRows(CMySbCommand cmd, string sheetID, SheetCwVoucher sheetCwVoucher, Dictionary<string, decimal> payways)
        {
            string subsID = "";
            string condi = "";// 主营业务收入
            string payCondi = "";
            int subLen = 1;
            if (payways == null || payways.Count == 0) // 单张单据生成凭证
            {
                if (payway1_id != "" && payway1_amount != 0) payways.Add(payway1_id, payway1_amount * money_inout_flag);
                if (payway2_id != "" && payway2_amount != 0) payways.Add(payway2_id, payway2_amount * money_inout_flag);
                if (left_amount != 0) payways.Add("left", left_amount * money_inout_flag);
                if (now_disc_amount != 0) payways.Add("disc", now_disc_amount * money_inout_flag);
                if (total_amount != 0) payways.Add("total", total_amount * money_inout_flag);
            }
            if(payways == null || payways.Count == 0)
            {
                return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
            }
            foreach (var payway in payways) // 按业务批量生产凭证
            {
                if (payway.Key == "total" || payway.Key.StartsWith("gift")) continue;
                else if (payway.Key == "left") condi += $@"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='1122' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='1122')  order by sub_code limit 1 )"; // 应收账款第一个明细
                else if (payway.Key == "disc") condi += $@"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_code=560304 )"; //财务费用-现金折扣（不允许增加子科目）
                else
                {
                    if (subsID != "") subsID = subsID + ",";
                    subsID += payway.Key;
                }
                subLen++;
            }
            if (subsID != "") payCondi += $"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_id in ({subsID}) )";
            string sql = $@"( 
                select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='5001' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='5001')  order by sub_code limit 1 
                ) {condi} {payCondi} ";
            dynamic subs_biz = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (subs_biz == null || subs_biz.Count < subLen) return new JsonResult(new { result = "Error", msg = "缺少生成凭证的相关科目，请添加" });
            if (subs_biz.Count > subLen) return new JsonResult(new { result = "Error", msg = "生成凭证的相关科目有重复科目代码，请修改" });

            //赠品转凭证
            if (payways.ContainsKey("gift"))
            {
                string remark0 = "";
                sql = $"( select sub_id,sub_name,sub_code,direction,COALESCE(status,'1') as status,'赠品' as remark,'{payways["gift"]}' as change_amount from cw_subject where company_id = {company_id} and sub_code =1405 )";//库存商品不允许增加子科目
                List<string> paywayGiftToRemove = new List<string>();
                paywayGiftToRemove.Add("gift");
                foreach (KeyValuePair<string, decimal> payway in payways)
                {
                   if(payway.Key.StartsWith("gift") && payway.Key != "gift")
                    {
                        remark0 = payway.Key.Replace("gift", "").Split(" ")[1];
                        sql += $"union all ( select sub_id,sub_name,sub_code,direction,COALESCE(status,'1') as status,'赠品' as remark,'{payway.Value}' as change_amount from cw_subject where company_id = {company_id} and sub_id={payway.Key.Replace("gift", "").Split(" ")[0]} and sub_id not in (select mother_id from cw_subject where company_id= {company_id} ))";
                        paywayGiftToRemove.Add(payway.Key);
                    }
                }
                sql=sql.Replace("赠品", remark0);
                dynamic subs_gift = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (subs_gift == null || subs_gift.Count < paywayGiftToRemove.Count) return new JsonResult(new { result = "Error", msg = "赠品缺少生成凭证的相关科目，或不是明细科目" });
                foreach (dynamic sub in subs_gift)
                {
                    if (Convert.ToInt16(sub.status) == 0) return new JsonResult(new { result = "Error", msg = "相关科目已停用，请检查" });
                    CwRowVoucher cwRow = new CwRowVoucher();
                    cwRow.business_sheet_type = SheetType;
                    cwRow.business_sheet_id = sheetID;
                    cwRow.sub_id = sub.sub_id;
                    cwRow.remark = sub.remark;
                    decimal.TryParse(sub.change_amount, out decimal changeAmt);
                    if (sub.sub_code == "1405")
                    {
                        cwRow.credit_amount = (-changeAmt).ToString();
                    }
                    else
                    {
                        cwRow.debit_amount=sub.change_amount;
                    }
                    cwRow.change_amount = sub.change_amount;
                    sheetCwVoucher.SheetRows.Add(cwRow);
                }
                foreach(string paywayGift in paywayGiftToRemove)
                {
                    payways.Remove(paywayGift);
                }
            }
            //业务转凭证
            foreach (var sub in subs_biz) // 对于每个 sub ，都有对应借贷金额，加入到cwRow里
            {
                if (sub.status == null || sub.status == "") sub.status = 1;
                if(Convert.ToInt16(sub.status)==0) return new JsonResult(new { result = "Error", msg = "相关科目已停用，请检查" });
                CwRowVoucher cwRow = new CwRowVoucher();
                cwRow.business_sheet_type = SheetType;
                cwRow.business_sheet_id = sheetID;
                cwRow.sub_id = sub.sub_id;
                if (!payways.ContainsKey("total")) break;
                cwRow.remark = payways["total"] >= 0 ? "销售商品" : "退货";

                if(red_flag == "2") cwRow.remark = payways["total"] >= 0 ? "红冲销售商品" : "红冲退货";
                decimal changeAmt = 0;
                foreach(var payway in payways) 
                {
                    changeAmt = payway.Value;

                    if (payway.Key == sub.sub_id || (payway.Key == "left" && sub.sub_code.ToString().Substring(0,4) == "1122") || (payway.Key == "disc" && sub.sub_code == "560304"))
                    {
                        if (changeAmt >= 0) cwRow.debit_amount = changeAmt.ToString();
                        else cwRow.credit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = changeAmt.ToString();
                        break;
                    }
                    else if (sub.sub_code.ToString().Substring(0, 4) == "5001" && payway.Key == "total") // 主营业务收入
                    {
                        if (changeAmt >= 0) cwRow.credit_amount = changeAmt.ToString();
                        else cwRow.debit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = (-1 * changeAmt).ToString();
                        break;
                    }
                }
                
                sheetCwVoucher.SheetRows.Add(cwRow);
            }

            return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
        }*/

        public SheetSale Copy()//深拷贝
        {
            return (SheetSale)this.MemberwiseClone();
        }

		#region 重载SheetBase里的方法
		/* Note
         * 通过重载SheetBase的SaveAndApprove、Red等方法来为本类型单据的处理流程统一地添加逻辑
         * await base.xxx是原逻辑的调用节点，可以在此之前或之后视情况地插入代码
        */
		public override async Task<string> OnSheetSaved(CMySbCommand cmd, string sheetID)
		{
            string msg = ""; 
           
            msg = await DoPromotionLogOnSheetCreated(cmd);            
            return msg;
        }

		protected override async Task<string> BeforeRed(CMySbCommand cmd, string sheetID, string rederID, string redBrief, CInfoForApproveBase info)
		{
			SheetFeeOut foSheet = new SheetFeeOut(SHEET_FEE_OUT.NOT_OUT, LOAD_PURPOSE.SHOW);
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync($"SELECT  sheet_id,sheet_no FROM sheet_fee_out_main where company_id = {this.company_id} and sheet_attribute is not null and sheet_attribute->>'related_sheet_id' = '{sheetID}' and red_flag is null and approve_time is not null;", cmd);
            string feeOutSheetId = data.sheet_id;
            foSheet.red_from = "BT";
            string msg = await foSheet.Red(cmd, this.company_id, feeOutSheetId, rederID, redBrief, false);
            return msg;
        }
        public async override Task<string> Delete(CMySbCommand cmd, string companyID, string sheetID, string operID, bool bAutoCommit = true)
        {
            var baseDeleteResult = await base.Delete(cmd, companyID, sheetID, operID, bAutoCommit);
            if (baseDeleteResult.Length > 0)
                return baseDeleteResult;

            return string.Empty;
        }
        private async Task<string> DoPromotionLogOnSheetCreated(CMySbCommand cmd)
        {
            try
            { 

                // 再记录销售单消耗的限时特价商品
                var promotionItems = PromotionController.GetSeckillItems(this);
                var logSeckillItemsResult = await PromotionController.LogSeckillItems(cmd,
                    company_id, sheet_id, SheetType, supcust_id, promotionItems);
                Console.WriteLine($"logSeckillItemsResult.Result: '{logSeckillItemsResult.ToJsonText()}'");
            }
            catch (Exception ex)
            {
                NLogger.Error(ex.ToString());
            }
            return "";
        }
        
        #endregion
        //protected override async Task<string> CheckSheetRowValid(CMySbCommand cmd)
        //{
        //    foreach (dynamic row in this.SheetRows)
        //    {
        //        string branchID = row.branch_id;
        //        string branchName = row.branch_name;
        //        if (branchID.IsInvalid())
        //        {
        //            branchID = branch_id;
        //            branchName = branch_name;
        //        }
        //        if (branchID.IsInvalid()) branchID = branch_id;
        //        if (row.GetType().GetProperty("branch_position") == null || row.branch_position == "0" || row.branch_position == null || row.branch_position == "") continue;
        //        dynamic record = await CDbDealer.Get1RecordFromSQLAsync($"select flow_id from info_branch_position where company_id = {company_id} and branch_position = {row.branch_position} and branch_id = {branchID};", cmd);
        //        if (record == null)
        //        {
        //            return $"{branchName}不存在库位：{row.branch_position_name}";
        //        }
        //    }
        //    return "OK";

        //}
    }
}
