# 功能实现介绍

## 调价单

### 功能使用前提：

+  正常使用的商品
+ 默认客户有策略，策略不只有 最近售价

### 主要功能逻辑

1. 单据上方可选择调价对象(可以选择批发价和各种价格方案)，调价后对应调价对象会被修改
   + 调价类型：批发价、价格方案、零售价 
   + 调价：调整前后价格、显示大中小价格  
   + 调价过程：
     1. 生成调价单，记录前后价格过程
     2. 批发价：添加、修改商品档案的批发价
     3. 方案价格：修改方案中具体商品的价格，表 price_plan_item；如果该方案之前为空，则相当于新增一个方案

2. 调价后，取记忆价格之后，会再次判断后面的价格方案是否被修改过，如果修改过，就会取消修改过的价格
   + 调价后具体影响：
     1. 调批发价：
        + 当price1是`最近售价`有值，看price2批发价，此时price2有值
          + `注：` 若price2有调价单时间，且时间比记忆价格时间新，取批发价
          + 否则，取最近售价
        + 当price1是批发价，就取批发价
        + 当price1为其他，方案一 - `最近售价` - 批发价
          + 对于方案一中没有的商品，商品又改了price2，则应取批发价
     2. 调方案一：
        + 当price1是`最近售价且`有值，看price2方案一，此时price2有值
          + `注：`看调价单时间或者方案更新时间，若时间比记忆价格时间新，取方案一价格
          + 否则，取方案一
        + 当price1是方案一，就取方案一
        + 当price1为其他，方案二 - `最近售价` - 方案一
          + 对于方案二中没有的商品，商品又改了方案一，则应取方案一

### 设计表

 sheet_price_adjust_main/sheet_price_adjust_detail 

```sql
CREATE TABLE sheet_price_adjust_main(company_id integer NOT NULL,sheet_id serial,sheet_no text NOT NULL, sheet_type text,money_inout_flag smallint, seller_id integer, plans_id text NOT NULL, red_flag smallint,red_sheet_id integer, red_sheet_date timestamp,maker_id integer, make_time timestamp, make_brief text,approver_id integer,approve_time timestamp, approve_brief text, happen_time timestamp,submit_time timestamp) 
partition by range(happen_time);

create table sheet_price_adjust_main_20072201 partition of sheet_price_adjust_main for values from ('2020-07-01') to ('2022-01-01');
create table sheet_price_adjust_main_22012301 partition of sheet_price_adjust_main for values from ('2022-01-01') to ('2023-01-01');

CREATE TABLE sheet_price_adjust_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,sheet_id integer,row_index int,inout_flag integer, item_id integer not null,s_price float4,m_price float4,b_price float4,price_info Jsonb NOT NULL,combine_flag text, tax_amount float4,happen_time timestamp, remark text) 
partition by range(happen_time);

create table sheet_price_adjust_detail_20072201 partition of sheet_price_adjust_detail for values from ('2020-07-01') to ('2022-01-01');
create table sheet_price_adjust_detail_22012301 partition of sheet_price_adjust_detail for values from ('2022-01-01') to ('2023-01-01');
 
```

###### 添加表item_price_adjust--12.30 

```sql
CREATE TABLE item_price_adjust(company_id integer not null,plan_id integer not null,item_id integer not null,adjust_time timestamp,constraint pk_item_price_adjust primary key(company_id,plan_id,item_id));
```

其中新增字段：

sheet_price_adjust_detail：

| 字段名     | 字段类型 | 是否可空                 | 字段注释     | 字段说明                                                     |
| ---------- | -------- | ------------------------ | ------------ | ------------------------------------------------------------ |
| price_info | Jsonb    | :heavy_multiplication_x: | 选择调价类型 | 存储格式：[{"planID":"","sPrice":""},{"planID":"","bPrice":""}]  <br />例：[{ "planID":"333","sPrice":"50"},{"planID":"-1","sPrice":"35","bPrice":"70"} ]   <br />价格方案中的plan_id,   -1：wholesale_price、0：retail_price，可多选 |

### 版本记录

| 版本  | 时间             | 修改状态                                                     | 相关人员 |
| ----- | ---------------- | ------------------------------------------------------------ | -------- |
| 1.0.0 | 2021.12.29 16:50 | 初步完成，等待研发自我测试 :heavy_check_mark:                | 研发：刘 |
| 1.0.1 | 2021.12.29 晚上  | 研发自我测试，页面小bug完善:heavy_check_mark:                | 测试：刘 |
| 1.0.1 | 2021.12.30 9:34  | 逻辑bug调整优化，添加调整商品记录表优化查询效率，修复使用商品时存在逻辑bug:heavy_check_mark: | 测试：相 |
| 1.1.0 | 2021.12.30 14:31 | 优化调整完成，等待研发自我测试:heavy_check_mark:             | 研发：刘 |
|       | 2021.12.30 14:48 | 研发自我测试，暂未发现bug:heavy_check_mark:                  | 测试：刘 |
|       | 2021.12.30 15:28 | 数据存储优化 price_info的json优化                            | 测试：相 |
| 1.2.0 | 2021.12.31 14:07 | 重新修改选择商品、保存存储price_info、打开单据渲染旧价格的逻辑 | 研发：刘 |
|       | 2021.12.31       | 对1.2.0版本的研发自我测试:heavy_check_mark:                  | 研发：刘 |



## 成本调价单

### 功能使用前提

### 主要功能逻辑

1. 正常直接开单
   + 根据选择的交易时间，从交易时间开始，重置加权价，并影响该时刻后续的 单据 的加权价，即使用 计算加权平均价 代码
2. 红冲单据
   + 根据选择的交易时间，从交易时间开始，使用调价前价格，并影响该时刻后续的 单据 的加权价，即使用 计算加权平均价 代码，恢复加权价
3. 当勾选调整预设进价时，先考虑是否有查看进价权限，会修改交易时间至今的 单据 在数据库中(sheet_sale_detail, sheet_invent_change_detail)存储的 cost_price_buy/buy_price ，且修改商品档案(info_item_multi_unit) 的进价 buy_price

### 设计表

###### 	sheet_cost_price_adjust_main / sheet_cost_price_adjust_detail

```sql
CREATE TABLE sheet_cost_price_adjust_main (company_id integer NOT NULL, sheet_id serial, sheet_no text NOT NULL, sheet_type text, money_inout_flag smallint, seller_id integer, red_flag smallint, red_sheet_id integer, red_sheet_date date, maker_id integer, make_time timestamp, make_brief text, approver_id integer, approve_time timestamp, approve_brief text, happen_time timestamp, submit_time timestamp, buy_price_change bool) 
partition by range(happen_time);

create table sheet_cost_price_adjust_main_20072201 partition of sheet_cost_price_adjust_main  for values from ('2020-07-01') to ('2022-01-01');
create table sheet_cost_price_adjust_main_22012301 partition of sheet_cost_price_adjust_main  for values from ('2022-01-01') to ('2023-01-01');

CREATE TABLE sheet_cost_price_adjust_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,sheet_id integer,row_index int,inout_flag integer, item_id integer not null,unit_no text,unit_factor float4,real_price float4,old_avg_price float4,old_buy_price float4,combine_flag text, tax_amount float4,happen_time timestamp, remark text) 
partition by range(happen_time)

create table sheet_cost_price_adjust_detail_20072201 partition of sheet_cost_price_adjust_detail for values from ('2020-07-01') to ('2022-01-01');
create table sheet_cost_price_adjust_detail_22012301 partition of sheet_cost_price_adjust_detail for values from ('2022-01-01') to ('2023-01-01');

```

sheet_cost_price_adjust_main

| 字段名           | 字段类型 | 是否可空           | 字段注释             | 字段说明      |
| ---------------- | -------- | ------------------ | -------------------- | ------------- |
| buy_price_change | bool     | :heavy_check_mark: | 是否同时修改预设进价 | 存储格式：t,f |

sheet_cost_price_adjust_detail

| 字段名        | 字段类型 | 是否可空                 | 字段注释                   | 字段说明        |
| ------------- | -------- | ------------------------ | -------------------------- | --------------- |
| real_price    | float    | :heavy_multiplication_x: | 开单时调整的价格           | 存储格式：float |
| old_avg_price | float    | :heavy_check_mark:       | 开单时查询的当前加权成本价 | 存储格式：float |
| old_buy_price | float    | :heavy_check_mark:       | 开单时查询的当前进价       | 存储格式：float |

### 版本记录

| 版本  | 时间           | 修改状态                                                     | 相关人员 |
| ----- | -------------- | ------------------------------------------------------------ | -------- |
| 1.0.0 | 2022.1.5 19:11 | 初步完成，等待研发自我测试 :heavy_check_mark:                | 研发：刘 |
|       | 2022.1.5 19:11 | 初步测试完成                                                 | 测试：刘 |
| 1.0.1 | 2022.1.5 19:27 | 在修改成本价时，还需更改商品档案里的进价；打开单据时看不到进价，且应考虑权限 | 测试：刘 |
|       | 2022.1.5 20:08 | 修复上述bug                                                  | 研发：刘 |

# 财务需求

## 会计科目

会计科目明确科目的借贷方向，一级科目有严格的科目代码

### 必备科目

| 类别     | 科目编码 | 科目名称     | 方向 |
| -------- | -------- | ------------ | ---- |
|          | 1        | 资产类       | 借   |
| 资产类   | 10       | 流动资产     | 借   |
| 流动资产 | 1001     | 库存现金     | 借   |
| 流动资产 | 1002     | 银行存款     | 借   |
| 流动资产 | 1012     | 其他货币资金 | 借   |
| 流动资产 | 1122     | 应收账款     | 借   |
| 流动资产 | 1123     | 预收账款     | 借   |
| 流动资产 | 1405     | 库存商品     | 借   |
| 流动资产 | 1410     | 商品进销差价 | 贷   |
|          | 2        | 负债类       | 贷   |
| 负债类   | 20       | 流动负债     | 贷   |
| 流动负债 | 2202     | 应付账款     | 贷   |
| 流动负债 | 2203     | 预收账款     | 贷   |
|          | 3        | 所有者权益类 | 贷   |
|          | 4        | 成本费用     | 借   |
|          | 6        | 损益         |      |
| 损益     | 60       | 收入         | 贷   |
| 收入     | 6001     | 主营业务收入 | 贷   |
| 收入     | 6051     | 其他业务收入 | 贷   |
| 损益     | 64       | 费用         | 借   |
| 费用     | 6401     | 主营业务成本 | 借   |
| 费用     | 6601     | 销售费用     | 借   |
| 费用     | 6602     | 管理费用     | 借   |
| 费用     | 6603     | 财务费用     | 借   |

## 业务单据自动生成凭证

首先在公司设置里，选择是否自动生成凭证

### 一、 功能目的：

开完单据后，自动生成凭证

### 二、需求细节与难点：

不同业务单据生成的凭证，需要注意凭证备注、科目、科目金额、科目借贷方向

### 三、开发流程：

##### 1. 需求分析

不同业务单据的 凭证模板：

+ 正常销售单 / 红冲退货单 / 退货单开销售
  + 借 支付方式/财务费用(优惠)/应收账款(欠款)
  + 贷 主营业务收入
+ 正常退货单 / 红冲销售单 / 销售单开退货
  + 借 主营业务收入
  + 贷 支付方式/财务费用(优惠)/应收账款(欠款)
+ 正常采购单 / 红冲采购退货 / 采购退货开采购
  + 借 库存商品
  + 贷 支付方式/应付账款/财务费用
+ 正常采购退货单 / 红冲采购单 / 采购单开退货
  + 借 支付方式/应付账款/财务费用
  + 贷 库存商品
+ 预收 / 定货 / 定货调整
  + 借 支付方式/财务费用/应收账款
  + 贷 开单时选择的预收账户
+ 预付
  + 借 开单时选择的预付账户
  + 贷 支付方式/财务费用/应付账款
+ 收款
  + 借 支付方式/财务费用
  + 贷 应收账款
+ 付款
  + 借 应付账款
  + 贷 支付方式/财务费用
+ 费用支出
  + 借 选择的费用支出科目
  + 贷 支付方式
+ 其他收入
  + 借 支付方式
  + 贷 选择的收入科目
+ 盘点单 对应的盘盈 / 盘亏
  + 盘盈 借库存商品，贷 管理费用 -- 对应金额为 sum(数量差*加权价)
  + 盘亏 借管理费用，贷库存商品
+ 成本调价单 对应的调高 / 调低
  + 调高 借库存商品，贷 商品进销差价 -- 对应金额为 sum(总库存*差价)
  + 调低 借 商品进销差价，贷库存商品

##### 2. 创建相关表

凭证与业务单据对应表

```sql
create table cw_voucher_sheet_mapper(
 company_id integer,
 voucher_id integer,
 business_sheet_type text,
 business_sheet_id integer,constraint pk_cw_voucher_sheet_mapper primary key(company_id,voucher_id,business_sheet_type,business_sheet_id)
) partition by hash(company_id)
CREATE TABLE cw_voucher_sheet_mapper_0 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 0);
CREATE TABLE cw_voucher_sheet_mapper_1 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 1);
CREATE TABLE cw_voucher_sheet_mapper_2 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 2);
CREATE TABLE cw_voucher_sheet_mapper_3 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 3);
CREATE TABLE cw_voucher_sheet_mapper_4 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 4);
CREATE TABLE cw_voucher_sheet_mapper_5 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 5);
CREATE TABLE cw_voucher_sheet_mapper_6 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 6);
CREATE TABLE cw_voucher_sheet_mapper_7 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 7);
CREATE TABLE cw_voucher_sheet_mapper_8 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 8);
CREATE TABLE cw_voucher_sheet_mapper_9 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 9);


```

表：company_setting中的setting

| 字段名            | 字段类型  | 是否可空 | 字段注释             | 字段说明(存储格式) |
| ----------------- | --------- | -------- | -------------------- | ------------------ |
| useAccounting | bool      |          | 启用财务功能         |   false                 |
| autoCreateVoucher | bool      |          | 业务单据自动创建凭证 |   false                 |
| autoApproveVoucher | bool      |          | 业务单据生凭证自动审核 |                    |
| accountingPeriod  | timestamp |          | 当前会计账期         | 2021-09           |
| openAccountPeriod  | timestamp |          | 开账会计账期         | 2021-09           |
| accountingCodeLength  | text |          | 编码长度        | [4,2,2,2,2]           |
| saleIncomeAndCost  | bool |          | 销售收入和成本同时生成         | false           |
| cwPeriodFromTo  | text |          | 财务开账账期始末         | 2021-09-01 ~ 2021-09-30           |

##### 3. 页面

在公司设置页面添加两个勾选框

##### 4. 功能主要逻辑

在每个单据审核时，根据单据数据 生成 对应凭证模板 ToVoucherRows，然后在 OnSheetIDGot 后调用 toVoucher 方法，调用Voucher的审核方法

对于 生成 voucherRows ：根据单据信息，查找相关的subInfo，判断借、贷方金额，对应到凭证的 voucherRows 中



## 批量生成凭证

### 一、功能目的

在批量生产凭证页面中，通过勾选，进行多张单据生成凭证，其中可以选择是否按业务单据，进行多张合并，是同一类业务单据只生成一张单据，金额累加

### 二、需求细节与难点

通过多选，得到不同单据 的 单据类型和单据号，然后加载出单据对应数据，生成 voucherRows ，最终通过 sheetCwVoucher.SaveAndApprove 生成凭证

### 三、开发流程

1. 需求分析

2. 创建相关表

3. 页面

   批量生产凭证页面

   + 左边是写死 的列表
   + 右面是 根据经营历程改造的 查询所有未生成凭证的 业务单据

4. 功能主要逻辑

   1. 通过多选，得到不同单据 的 单据类型和单据号

   2. 生成凭证

      + 如果不需要按业务合并 -- 类似于自动生成凭证
        + 遍历多选的单据ID，对每个单据创建 TSheet对象，调用load方法，然后直接 调用 ToVoucherRows，最终通过 sheetCwVoucher.SaveAndApprove 生成凭证

      ```c#
      SheetSale sheetSale = new SheetSale(SHEET_RETURN.EMPTY, MyJXC.LOAD_PURPOSE.SHOW);
      await sheetSale.Load(cmd, companyID, sheetID);
      record = await sheetSale.ToVoucherRows(cmd, sheetID, sheetCwVoucher, payway);
      
      if (record != null && record.Value.msg != "")
      {
          msg = record.Value.msg;
          break;
      }
      msg = await record.Value.sheetCwVoucher.SaveAndApprove(cmd);
      ```

      + 按业务单据 合并
        + 首先对多选的信息，生成 Dictionary<string,string> sheetsDic ；其中 key 是 sheetType，value 是 逗号分割的 sheetIDs ---- 为了调用 LoadMultiSheets 方法，一次性获取 某一类单据 
        + 然后调用 LoadMultiSheets ，对结果集进行遍历，对金额进行加减，得到新的合并后的 单据总金额 即，payways
        + 调用 ToVoucherRows，最终通过 sheetCwVoucher.SaveAndApprove 生成凭证