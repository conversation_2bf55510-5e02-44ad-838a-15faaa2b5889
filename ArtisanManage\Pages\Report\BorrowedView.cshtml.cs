﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class BorrowedViewModel : PageQueryModel
    {  
        public BorrowedViewModel(CMySbCommand cmd):base(MenuId.borrowedView)
        {
            this.cmd = cmd;
            this.PageTitle = "借还货汇总";
            DataItems = new Dictionary<string, DataItem>()
            {

                {"startDay",new DataItem(){Title="开始日期",FldArea = "divHead",CtrlType="jqxDateTimeInput",ForQuery=false, SqlFld="sm.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea = "divHead", CtrlType="jqxDateTimeInput",ForQuery=false, SqlFld="sm.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }"
                }},
                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="sd.item_id",ForQuery=false,DropDownWidth="300",
                SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
                {"supcust_id",CommonTool.GetDataItem("supcust_id",new DataItemChange{Checkboxes = true,SqlFld = "sm.supcust_id",ForQuery=false,}) },
 
                 {"hh_status",new DataItem(){FldArea="divHead",Title="还货情况",LabelFld = "status_name",ForQuery=false,ButtonUsage = "list",CompareOperator="=",Value="all",Label="所有",
                    Source = @"[{v:'cleared',l:'已结清',condition:""bc.borrowed_qty=0""},
                                 {v:'uncleared',l:'未结清',condition:""bc.borrowed_qty!=0""},
                                 {v:'all',l:'所有',condition:""true""}]"
                }},

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"supcust_id",    new DataItem(){Title="客户编号",  Width="250",SqlFld="supcust_id",HideOnLoad=true,Hidden=true}},
                       {"sup_name",new DataItem(){Title="客户名称", CellsAlign="left",Width="200"} },
                       {"item_id",    new DataItem(){Title="商品",  Width="150",SqlFld="",Hidden=true}},
                       {"item_name",new DataItem(){Title="商品名称", CellsAlign="left",SqlFld="",Width="200"} },
                       {"j_qty",new DataItem(){Title="借货数量", CellsAlign="center", Width="100",
    SqlFld="unit_from_s_to_bms(sum(case when trade_type ='J' then -quantity*unit_factor else 0 end )::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
    Linkable=true,
    SortFld="sum(case when trade_type = 'J' then -quantity*unit_factor else 0 end )::numeric",
    Sortable=true,
    FuncDealMe=(value)=>{return value=="0"?"":value; },
    FuncGetSumValue = (sumColumnValues) =>
    {
        string sQty = "";
        if(sumColumnValues["j_qty_b"]!="") sQty+= sumColumnValues["j_qty_b"]+"大";
        if(sumColumnValues["j_qty_m"]!="") sQty+= sumColumnValues["j_qty_m"]+"中";
        if(sumColumnValues["j_qty_s"]!="") sQty+= sumColumnValues["j_qty_s"]+"小";
        return sQty;
    }
} },
                       {"j_qty_b",   new DataItem(){Title="借货数量(大)", CellsAlign="center",   Width="100",ShowSum=true,Hidden=true,HideOnLoad = true,
    SqlFld="yj_get_unit_qty('b',sum(case when trade_type = 'J' then -quantity*unit_factor else 0 end )::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe=(value)=>{return value=="0"?"":value; }
}},
                       {"j_qty_m",   new DataItem(){Title="借货数量(中)", CellsAlign="center",   Width="100",ShowSum=true,Hidden=true,HideOnLoad = true,
    SqlFld="yj_get_unit_qty('m',sum(case when trade_type = 'J' then -quantity*unit_factor else 0 end )::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe=(value)=>{return value=="0"?"":value; }
}},
                       {"j_qty_s",   new DataItem(){Title="借货数量(小)", CellsAlign="center",   Width="100",ShowSum=true,Hidden=true,HideOnLoad = true,
    SqlFld="yj_get_unit_qty('s',sum(case when trade_type = 'J' then -quantity*unit_factor else 0 end )::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe=(value)=>{return value=="0"?"":value; }
}},

                       {"h_qty", new DataItem()
{
    Title = "还货数量",
    CellsAlign = "center",
    Width = "100",
    SqlFld = "unit_from_s_to_bms(sum(case when trade_type ='H' then quantity*unit_factor else 0 end )::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
    Linkable = true,
    SortFld = "sum(case when trade_type = 'H' then quantity*unit_factor else 0 end )::numeric",
    Sortable = true,
    FuncDealMe = (value) => { return value == "0" ? "" : value; },
    FuncGetSumValue = (sumColumnValues) =>
    {
        string sQty = "";
        if (sumColumnValues["h_qty_b"] != "") sQty += sumColumnValues["h_qty_b"] + "大";
        if (sumColumnValues["h_qty_m"] != "") sQty += sumColumnValues["h_qty_m"] + "中";
        if (sumColumnValues["h_qty_s"] != "") sQty += sumColumnValues["h_qty_s"] + "小";
        return sQty;
    }
}},
{"h_qty_b", new DataItem()
{
    Title = "还货数量(大)",
    CellsAlign = "center",
    Width = "100",
    ShowSum = true,
    Hidden = true,
    HideOnLoad = true,
    SqlFld = "yj_get_unit_qty('b',sum(case when trade_type = 'H' then quantity*unit_factor else 0 end )::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe = (value) => { return value == "0" ? "" : value; }
}},
{"h_qty_m", new DataItem()
{
    Title = "还货数量(中)",
    CellsAlign = "center",
    Width = "100",
    ShowSum = true,
    Hidden = true,
    HideOnLoad = true,
    SqlFld = "yj_get_unit_qty('m',sum(case when trade_type = 'H' then quantity*unit_factor else 0 end )::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe = (value) => { return value == "0" ? "" : value; }
}},
{"h_qty_s", new DataItem()
{
    Title = "还货数量(小)",
    CellsAlign = "center",
    Width = "100",
    ShowSum = true,
    Hidden = true,
    HideOnLoad = true,
    SqlFld = "yj_get_unit_qty('s',sum(case when trade_type = 'H' then quantity*unit_factor else 0 end )::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe = (value) => { return value == "0" ? "" : value; }
}},
{"no_qty", new DataItem()
{
    Title = "未还数量",
    CellsAlign = "center",
    Width = "100",
    SqlFld = "unit_from_s_to_bms((sum(case when trade_type ='J' then -quantity*unit_factor else 0 end ) - sum(case when trade_type = 'H' then quantity * unit_factor else 0 end ))::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
    SortFld = "(sum(case when trade_type ='J' then -quantity*unit_factor else 0 end ) - sum(case when trade_type = 'H' then quantity * unit_factor else 0 end ))::numeric",
    Sortable = true,
    FuncDealMe = (value) => { return value == "0" ? "" : value; },
    FuncGetSumValue = (sumColumnValues) =>
    {
        string sQty = "";
        if (sumColumnValues["no_qty_b"] != "") sQty += sumColumnValues["no_qty_b"] + "大";
        if (sumColumnValues["no_qty_m"] != "") sQty += sumColumnValues["no_qty_m"] + "中";
        if (sumColumnValues["no_qty_s"] != "") sQty += sumColumnValues["no_qty_s"] + "小";
        return sQty;
    }
}},
{"no_qty_b", new DataItem()
{
    Title = "未还数量(大)",
    CellsAlign = "center",
    Width = "100",
    ShowSum = true,
    Hidden = true,
    HideOnLoad = true,
    SqlFld = "yj_get_unit_qty('b',(sum(case when trade_type ='J' then -quantity*unit_factor else 0 end ) - sum(case when trade_type = 'H' then quantity * unit_factor else 0 end ))::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe = (value) => { return value == "0" ? "" : value; }
}},
{"no_qty_m", new DataItem()
{
    Title = "未还数量(中)",
    CellsAlign = "center",
    Width = "100",
    ShowSum = true,
    Hidden = true,
    HideOnLoad = true,
    SqlFld = "yj_get_unit_qty('m',(sum(case when trade_type ='J' then -quantity*unit_factor else 0 end ) - sum(case when trade_type = 'H' then quantity * unit_factor else 0 end ))::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe = (value) => { return value == "0" ? "" : value; }
}},
{"no_qty_s", new DataItem()
{
    Title = "未还数量(小)",
    CellsAlign = "center",
    Width = "100",
    ShowSum = true,
    Hidden = true,
    HideOnLoad = true,
    SqlFld = "yj_get_unit_qty('s',(sum(case when trade_type ='J' then -quantity*unit_factor else 0 end ) - sum(case when trade_type = 'H' then quantity * unit_factor else 0 end ))::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe = (value) => { return value == "0" ? "" : value; }
}},
{"borrowed_qty", new DataItem()
{
    Title = "当前未还数量",
    CellsAlign = "center",
    Width = "100",
    SqlFld = "unit_from_s_to_bms(borrowed_qty::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
    SortFld = "borrowed_qty::numeric",
    Sortable = true,
    FuncDealMe = (value) => { return value == "0" ? "" : value; },
    FuncGetSumValue = (sumColumnValues) =>
    {
        string sQty = "";
        if (sumColumnValues["borrowed_qty_b"] != "") sQty += sumColumnValues["borrowed_qty_b"] + "大";
        if (sumColumnValues["borrowed_qty_m"] != "") sQty += sumColumnValues["borrowed_qty_m"] + "中";
        if (sumColumnValues["borrowed_qty_s"] != "") sQty += sumColumnValues["borrowed_qty_s"] + "小";
        return sQty;
    }
}},
{"borrowed_qty_b", new DataItem()
{
    Title = "当前未还数量(大)",
    CellsAlign = "center",
    Width = "100",
    ShowSum = true,
    Hidden = true,
    HideOnLoad = true,
    SqlFld = "yj_get_unit_qty('b',borrowed_qty::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe = (value) => { return value == "0" ? "" : value; }
}},
{"borrowed_qty_m", new DataItem()
{
    Title = "当前未还数量(中)",
    CellsAlign = "center",
    Width = "100",
    ShowSum = true,
    Hidden = true,
    HideOnLoad = true,
    SqlFld = "yj_get_unit_qty('m',borrowed_qty::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe = (value) => { return value == "0" ? "" : value; }
}},
{"borrowed_qty_s", new DataItem()
{
    Title = "当前未还数量(小)",
    CellsAlign = "center",
    Width = "100",
    ShowSum = true,
    Hidden = true,
    HideOnLoad = true,
    SqlFld = "yj_get_unit_qty('s',borrowed_qty::numeric,b_unit_factor,m_unit_factor,false)",
    FuncDealMe = (value) => { return value == "0" ? "" : value; }
}}

                     },
                    

                          QueryFromSQL=@"
from (select sup_name,
             case when son_mum_item is null then a.itemId else son_mum_item end as item_id,
             case
                 when son_mum_item_name is null then ip.item_name
                 else son_mum_item_name end                                      as item_name,
             b_unit_factor,
             m_unit_factor,
             s_unit_factor,
             b_unit_no,
             m_unit_no,
             s_unit_no,
             borrowed_qty,
             a.*
      from (select supcust_id, item_id as itemId, trade_type, quantity, unit_factor
            from sheet_sale_main sm
                     left join sheet_sale_detail sd
                               on sm.sheet_id = sd.sheet_id and trade_type in ('J', 'H') and sd.company_id = ~COMPANY_ID
            where sd.company_id = ~COMPANY_ID
              and red_flag is null
              and approve_time is not null
              and (sm.happen_time >= '~VAR_STARTDAY')
              and (sm.happen_time < '~VAR_ENDDAY')
              and item_id is not null
              ~VAR_ITEM_ID  
            union all
            select supcust_id, item_id as itemId, trade_type, quantity, unit_factor
            from borrow_item_main sm
                     left join borrow_item_detail sd
                               on sm.sheet_id = sd.sheet_id and trade_type in ('J', 'H') and sd.company_id = ~COMPANY_ID
            where sd.company_id = ~COMPANY_ID
              and red_flag is null
              and approve_time is not null
              and (sm.happen_time >= '~VAR_STARTDAY')
              and (sm.happen_time < '~VAR_ENDDAY')
              and item_id is not null
              ~VAR_ITEM_ID ) a
               left join info_supcust sc on a.supcust_id = sc.supcust_id and sc.company_id = ~COMPANY_ID
               left join (SELECT ip1.company_id,
                                 ip1.item_id,
                                 ip1.item_name,
                                 ip1.son_mum_item,
                                 ip2.item_name son_mum_item_name
                          FROM info_item_prop ip1
                                   LEFT JOIN info_item_prop ip2
                                             on ip1.company_id = ip2.company_id and ip1.son_mum_item = ip2.item_id
                          WHERE ip1.company_id = ~COMPANY_ID) ip on a.itemId = ip.item_id and ip.company_id = ~COMPANY_ID
               left join borrowed_cust_items bc
                         on bc.item_id = CASE WHEN son_mum_item IS NULL THEN a.itemId ELSE son_mum_item END and
                            a.supcust_id = bc.cust_id and bc.company_id = ~COMPANY_ID
               left join
           (select item_id,
                   (b ->> 'f1')::numeric as b_unit_factor,
                   (m ->> 'f1')::numeric as m_unit_factor,
                   (s ->> 'f1')::numeric as s_unit_factor,
                   b ->> 'f2'            as b_unit_no,
                   m ->> 'f2'            as m_unit_no,
                   s ->> 'f2'            as s_unit_no
            from crosstab(
                         'select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',
                         $$values ('s'::text),('m'::text),('b'::text)$$)
                     as errr(item_id int, s jsonb, m jsonb, b jsonb)) t
           on a.itemId = t.item_id) T where  true ~VAR_SUPCUST_ID ~VAR_HH_STATUS
",
                   QueryGroupBySQL = "GROUP BY sup_name, supcust_id,borrowed_qty, b_unit_factor, m_unit_factor, s_unit_factor, b_unit_no, m_unit_no, s_unit_no, item_name, item_id ",
                   QueryOrderSQL="order by sup_name"
                  }
                } 
            }; 
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
           string startDay = DataItems["startDay"].Value;
           string endDay = DataItems["endDay"].Value;
           string item_id = DataItems["item_id"].Value;
           string supcust_id = DataItems["supcust_id"].Value;
           string hh_status = DataItems["hh_status"].Value;
           this.SQLVariables["STARTDAY"] = @$"{startDay}";
           this.SQLVariables["ENDDAY"] = @$"{endDay}";
            this.SQLVariables["ITEM_ID"] = "";
            this.SQLVariables["SUPCUST_ID"] = "";
            this.SQLVariables["HH_STATUS"] = "";
           
            if(item_id.IsValid())
            {
                this.SQLVariables["ITEM_ID"] = @$"and item_id in ({item_id})";
            }
            if(supcust_id.IsValid())
            {
                this.SQLVariables["SUPCUST_ID"] = @$"and supcust_id ='{supcust_id}'";
            }
            if(hh_status == "cleared")
            {
               this.SQLVariables["HH_STATUS"] = @$"and  T.borrowed_qty=0";
            }else if(hh_status == "uncleared")
            {
                this.SQLVariables["HH_STATUS"] = @$"and  T.borrowed_qty!=0";  
            } else
            {
                this.SQLVariables["HH_STATUS"] = @$"";  
            }
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            

        }
        public async Task OnGet()
        {  
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
    }



    [Route("api/[controller]/[action]")]
    public class BorrowedViewController : QueryController
    { 
        public BorrowedViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BorrowedViewModel model = new BorrowedViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            BorrowedViewModel model = new BorrowedViewModel(cmd); 
            
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }
        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            BorrowedViewModel model = new BorrowedViewModel(cmd); 
            return await model.ExportExcel(Request, cmd); 
        }

    }
}
