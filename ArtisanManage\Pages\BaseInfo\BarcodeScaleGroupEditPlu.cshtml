﻿@page
@model ArtisanManage.Pages.BaseInfo.BarcodeScaleGroupEditPluModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head id="Head1" runat="server">
    <meta name="viewport" content="width=device-width" />

    <title>BarcodeScaleGroupEditPlu</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js?v=@Html.Raw(Model.Version)"></script>
    <style>
        .gridPosition {
            position: absolute;
            top: 70px;
            bottom: 50px;
        }

     </style >
    <script type="text/javascript">
        @Html.Raw(Model.m_saveCloseScript)
            window.ObjOperRights = @Html.Raw(Model.ObjOperRights);
        $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
                $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
                });
        });
    </script>
</head>
<body>
    <div id="divHead" class="headtail" style="margin:20px 0 0 20px">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-left:50px;">保存</button>
    </div>
    <div id="popItem" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择商品</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="gridBarcodeGroupPlu" class="gridPosition" style="left:20px; width:calc(90%);"> </div>


    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var wholeSupcusts = []

        // $.ajax('/api/BarcodeScaleGroupEditPlu/GetRegions?operKey=' + window.g_operKey, {
        //     success: function (data) {
        //         if (data.result === 'OK') {
                    // const renderDiv = renderRegion(data.regionData)
                    // $("#region-content").append(renderDiv)
                    // const options = document.getElementsByClassName("region-item")
                    // Array.from(options).map(element => {
                    //     element.addEventListener("mouseover", (e) => {
                    //         e.currentTarget.style.setProperty("background", "#ccc")
                    //     })
                    //     element.addEventListener("mouseout", (e) => {
                    //         var regionId = $(e.currentTarget).attr("data-region-id")
                    //         console.log({ regionId, g_region })
                    //         if (regionId === g_region) {
                    //             e.currentTarget.style.setProperty("background", "#ccc")
                    //         } else {
                    //             e.currentTarget.style.setProperty("background", "#f0f0f0")
                    //         }
                    //     })
                    // })
        //         }
        //     }
        // })
        function initeditor_item_name(row, cellvalue, editor, celltext, pressedkey) {

            var inputField = editor.find('input');

            if (pressedkey) {

                var container = window.parent.CefGlue
                var simulateInput = null
                if (container) simulateInput = container.simulateInput
                if (!simulateInput) {
                    inputField.val(pressedkey);
                    inputField.jqxInput('selectLast');
                }
                else {
                    inputField.focus()
                    setTimeout(() => {
                        inputField.val('')
                        simulateInput(pressedkey)
                        console.log('keycode:' + pressedkey)

                    }, 30)
                }


            }
            else {
                inputField.val({ value: cellvalue, label: celltext });
                inputField[0].value = celltext || '';
                inputField.jqxInput('selectAll');

            }
        }
        function createeditor_item_name(row, cellvalue, editor, cellText, width, height) {
            var element = $('<div id="txtItemName"></div>');
            editor.append(element);
            var inputElement = editor.find('div')[0];

            var dataFields = new Array(
                { datafield: "name", text: "商品名称", width: 360 },
                { datafield: "item_spec", text: "规格", width: 100 },
                { datafield: "code", text: "条码", width: 140 },
                { datafield: "stock", text: "库存", width: 100 }

            )

            $(inputElement).jqxInput({
                placeHolder: "助记码/名称", height: height, width: width,
                borderShape: "none",
                buttonUsage: 'event',
                showHeader: true,
                dropDownHeight: 160,
                displayMember: "name",
                valueMember: "id",
                dataFields: dataFields,
                searchFields: ["zjm", "name", "code", "item_no", "mum_attributes"],
                maxRecords: 9,
                source: function (query, response) {
                    ajaxGet('/api/BarcodeScaleGroupEditPlu/GetItemsInfo', { query: query }).then(data => {
                        if (data.result === 'OK') {
                            response(data.records)
                        }
                    }).catch(error => { console.log(error) })
                },
                renderer: function (itemValue, inputValue) {
                    // debugger;
                    var terms = inputValue.split(/,\s*/);
                    // remove the current input
                    terms.pop();
                    // add the selected item
                    terms.push(itemValue);
                    // add placeholder to get the comma-and-space at the end
                    // terms.push("");
                    //var value = terms.join(", ");
                    //return terms;
                    return itemValue;
                }, onButtonClick: function () {
                    window.curRowIndex = row;
                    var celltext = $('#gridBarcodeGroupPlu').jqxGrid('getcellvalue', row, 'item_name');
                    var params = ''
                    var showSonItems = ''
                    $(inputElement).val({ value: cellvalue, label: celltext });
                    if (inputElement) {
                        inputElement.value = celltext || '';
                        $(inputElement).jqxInput('selectAll');
                    }
                    $('#popItem').jqxWindow('open');
                    $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&operKey=${g_operKey}${showSonItems}&item_class=${3}&class_name=${'rrrr'}&${params}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    
                }
            })
            $(inputElement).val(cellvalue);
            $(inputElement).on('optionSelected', //点击客户信息行后
                function (a, b) {
                    //debugger;
                    var value = $(inputElement).val();
                    var id = '';
                    console.log('value:' + JSON.stringify(value));

                    var cell = $('#gridBarcodeGroupPlu').jqxGrid('getselectedcell');

                    var row = $('#gridBarcodeGroupPlu').jqxGrid('getrowdata', cell.rowindex)

                    row = {
                        item_id: value.value,
                        item_name: value.label
                    }
                    let msg = '';
                    let gridRows = $('#gridBarcodeGroupPlu').jqxGrid('getrows');
                    for (let i = 0; i < gridRows.length; i++) {
                        let gridRow = $('#gridBarcodeGroupPlu').jqxGrid('getrowdata', i);
                        if (cell.rowindex !== i && gridRow.item_id === row.item_id) {
                            msg = '在第' + (i + 1) + '行已存在此商品'
                            bw.toast(msg, 5000);
                            break;
                        }
                    }
                    if (msg) return
                    console.log(gridRows)
                    addItemRows(cell.rowindex, [row])

                    // cellendedit({ args: {datafield:'item_id',rowindex:cell.rowindex,row:rowData,value:value,oldvalue:''} })
                })

            // debugger;
            editor.on('close', function () {
                var value = $(inputElement).val();
                $('#gridBarcodeGroupPlu').jqxGrid('setcellvalue', row, 'item_name', value);
            });
        }
        function addItemRows(rowIndex, rows) {
            const item_id = rows.map(row => row.item_id).join(',');

            // 向后端请求商品信息
            ajaxGet('/api/BarcodeScaleGroupEditPlu/GetItemsInfo', { itemID: item_id })
                .then(data => {
                    if (data.result === 'OK') {
                        const gridRows = $('#gridBarcodeGroupPlu').jqxGrid('getrows');
                        const barcode_scale_group_id = $('#barcode_scale_group_id').val();

                        // 更新每一条记录到相应的行
                        data.records.forEach((item, index) => {
                            const currentRowIndex = rowIndex + index;
                            const gridRow = gridRows[currentRowIndex];
                            if (gridRow) {
                                // 遍历所有字段并逐个更新
                                Object.keys(item).forEach(key => {
                                    // 使用 setcellvalue 更新单元格值
                                    $('#gridBarcodeGroupPlu').jqxGrid('setcellvalue', currentRowIndex, key, item[key]);
                                    $('#gridBarcodeGroupPlu').jqxGrid('setcellvalue', currentRowIndex, 'barcode_scale_group_id', barcode_scale_group_id);
                                });
                            }
                        });
                        $('#gridBarcodeGroupPlu').jqxGrid('updategrid');
                    }
                })
                .catch(error => {
                    console.error('Error updating rows:', error);
                });
        }
        function expandGridRows() {
            var lastIndex = getLastRowIndex()
            for (var i = 0; i < 10; i++) {
                onRowAdd('gridBarcodeGroupPlu', lastIndex + i)
            }
        }
        function getLastRowIndex() {
            var gridRows = $('#gridBarcodeGroupPlu').jqxGrid('getrows');
            const lastIndex = gridRows.filter(gridRow => gridRow.supcust_id !== '').length
            return lastIndex
        }

        function withClickEvent(marker) {
            marker.addEventListener("click", e => {
                // const { supcust } = marker
                // console.log(supcust)
                // var gridRows = $('#gridBarcodeGroupPlu').jqxGrid('getrows');
                // if (gridRows.some(gridRow => {
                //     return gridRow.supcust_id === supcust.supcust_id
                // })) {
                //     bw.toast("该客户已存在列表中")
                //     return
                // }
                // const lastRowIndex = getLastRowIndex()
                // var gridRow = gridRows[lastRowIndex]
                // Object.assign(gridRow, supcust)
                // $('#gridBarcodeGroupPlu').jqxGrid('updategrid')
                // drawMarkers()
                // drawLushu()
                // console.log(gridRows.filter(gridrow => gridrow.supcust_id != '').length)
                // if (gridRows.filter(gridrow => gridrow.supcust_id != '').length == gridRows.length) {
                //     expandGridRows()
                // }
            })
        }
       
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "selectMulti" || rs.data.action === "select") {
                    var cell = $('#gridBarcodeGroupPlu').jqxGrid('getselectedcell');
                    var rowIndex = cell.rowindex;
                    var editable = $("#gridBarcodeGroupPlu").jqxGrid('endcelledit', rowIndex, "item_id", false);
                    $('#popItem').jqxWindow('close');

                    var gridRows = $('#gridBarcodeGroupPlu').jqxGrid('getrows');
                    var checkRows = []
                    var index = 0
                    rs.data.checkedRows.forEach(row => {
                        for (let i = 0; i < gridRows.length; i++) {
                            let gridRow = $('#gridBarcodeGroupPlu').jqxGrid('getrowdata', i);
                            if (rowIndex !== i && gridRow.item_id === row.item_id) {
                                msg = '在第' + (i + 1) + '行已存在商品-' + row.item_name
                                bw.toast(msg, 5000);
                                return;
                            }
                        }
                        checkRows[index] = row
                        index++
                    })
                    if (checkRows.length === 0) return

                    addItemRows(rowIndex, checkRows)
                }

            }
        });
        $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            var operateVal = $("#operate input").attr("data-value")

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            $('#item_name input').on('input', function () {
                $('#py_str input').val(this.value.ToPinYinCode());
            });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });
        });

        function dealFormData(formData) {
            for (let i = 0, len = formData.gridBarcodeGroupPlu.length; i < len; i++) {
                var row = formData.gridBarcodeGroupPlu[i]
                row.order_index = i
                formData.gridBarcodeGroupPlu[i] = row;
            }
        }

        function onFormSaved(data) {
            bw.toast('保存成功', 3000)
            $('#plu_id').val(data.record.plu_id)
        }

    </script>
</body>
</html>