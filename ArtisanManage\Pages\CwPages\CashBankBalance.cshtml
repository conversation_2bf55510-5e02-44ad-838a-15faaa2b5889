﻿@page
@model ArtisanManage.Pages.CwPages.CashBankBalanceModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var bizStartPeriod = '@Html.Raw(Model.BizStartPeriod)';
        bizStartPeriod = new Date(`${bizStartPeriod} 00:00:00`);
        var newCount = 1;
        var itemSource = {};
        $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
            if (bizStartPeriod && !isNaN(bizStartPeriod.getTime())) $("#startDay").jqxDateTimeInput('setMinDate', bizStartPeriod);

            $('#time_type .row-oper').remove();
            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                var sub_id = args.row.bounddata.sub_id;
                var sub_name = args.row.bounddata.sub_name;
                var startDay = $('#startDay').jqxDateTimeInput('val');
                var endDay = $('#endDay').jqxDateTimeInput('val');
                var showRedSheet = $('#showRed').val();
                /*if (args.datafield) {
                    var sub_id = args.row.bounddata.sub_id;
                    let startDay = $('#startDay').jqxDateTimeInput('val').replace(' ','+');
                    let now = new Date();
                    window.parent.newTabPage('现金银行明细账', `CwPages/CashBankDetail?sub_id=${sub_id}&startDay=${startDay}&endDay=${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}+23%3A59`);
                }*/
                var url = ""
                if (args.datafield == "sub_name") {
                    url = `CwPages/CashBankDetail?&sub_id=${sub_id}&sub_name=${sub_name}&startDay=${startDay}&endDay=${endDay}&showRed=${showRedSheet}`;
                    title = "现金银行明细账"
                    window.parent.newTabPage(title,`${url}`);
                }

            });
            QueryData();
        });


        function beforeQuery(){
            let startDay = $('#startDay').jqxDateTimeInput('getDate');
            if (new Date(startDay) < bizStartPeriod) {
                $('#startDay ').jqxDateTimeInput('setDate', bizStartPeriod);
            }
        }

    </script>
</head>

<body>

    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail" style="width: calc(100% - 220px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <div style="margin-left: auto; display: flex; align-items: center;">
            <button onclick="QueryData()" style="margin-right:10px;border-radius: 3px 0px 0px 3px">查询</button>
            <button id="btnExport" onclick="ExportExcel()" style="margin-right:10px;border-radius: 3px 0px 0px 3px">导出</button>
        </div>
    </div>


    <div id="gridItems" style="margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 95px);"></div>
    <div id="divRowCount"><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>



</body>
</html>