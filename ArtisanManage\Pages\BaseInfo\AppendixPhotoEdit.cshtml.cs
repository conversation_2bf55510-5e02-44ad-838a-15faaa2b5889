﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;
using ArtisanManage.Services;
namespace ArtisanManage.Pages.BaseInfo
{
    public class AppendixPhotoEditModel : PageFormModel
    {
        public AppendixPhotoEditModel(CMySbCommand cmd, string company_id = "", string oper_id = "") : base(Services.MenuId.Empty)
        {
            this.cmd = cmd;
            NoIDFld = true;
        }

        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }


    [ApiController]
    [Route("api/[controller]/[action]")]
    public class AppendixPhotoEditController : BaseController
    {
        public AppendixPhotoEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        private string GetTableFromSheetType(string sheetType)
        {
            string mmSheetTable = "";
            switch (sheetType)
            {
                case "ZC": case "SR": mmSheetTable = "sheet_fee_out_main"; break;
                case "X": case "T": mmSheetTable = "sheet_sale_main"; break;
                case "CG": case "CT": mmSheetTable = "sheet_buy_main"; break;
                case "YS": case "YF": case "DH": mmSheetTable = "sheet_prepay"; break;

            }
            return mmSheetTable;
        }
        [HttpGet]
        public async Task<IActionResult> GetSheetAppendixPhotos(string sheetType,string sheetId,string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string tabelName = GetTableFromSheetType(sheetType);
            string sql = @$"select sheet_attribute->>'appendixPhotos' as ap from {tabelName} where company_id = {companyID} and sheet_id = {sheetId} and sheet_type = '{sheetType}'";
            dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (ret != null)
            {
                return new JsonResult(new { result = "OK", photoStr = ret.ap });
            }
            return new JsonResult(new { result = "Empty" });
        }
    }
}
