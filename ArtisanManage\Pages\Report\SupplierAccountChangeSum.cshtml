@page
@model ArtisanManage.Pages.BaseInfo.SupplierAccountChangeSumModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var m_db_id = "10";

        var newCount = 1;

    	var itemSource = {};
    	   $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
                QueryData();
            $("#gridItems").on("cellclick", function (event) {

                    var args = event.args;
                    var supcust_id = args.row.bounddata.supcust_id;

                    var sup_name = args.row.bounddata.sup_name;

                    var startDay = $('#startDay').jqxDateTimeInput('val');
                    var endDay = $('#endDay').jqxDateTimeInput('val');
                    //var seller_id = $('#seller_id').val().value;
                    //var seller_name = $('#seller_id').val().label;
                    //var brand_id = $('#brand_id').val().value;

                    //var brand_name = $('#brand_id').val().label;
                    var url =""
                    var title =""
                    //if (args.datafield == "sup_name" && sup_name) {
                    //    url = `Report/AccountHistory?&supcust_id=${supcust_id}&sup_name=${sup_name}&startDay=${startDay}&endDay=${endDay}`;
                    //   title="客户往来账"
                    //    window.parent.newTabPage(title, `${url}`);
                    //}

                    let now = new Date();//供应商往来汇总点击链接定位到供应商往来账，结束时间应该为now，因为供应商往来账按照审核时间查询
                        
                    now = `${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}` + " 23:59";
                    if ((args.datafield == "prepay_add" || args.datafield == "prepay_reduce") && sup_name) {
                        url = `Report/SupplierAccountHistory?&supcust_id=${supcust_id}&sup_name=${sup_name}&startDay=${startDay}&endDay=${now}&sub_type=YF&sub_type_name=预付款`;
                        title="供应商往来账"
                        window.parent.newTabPage(title, `${url}`);
                        console.log('张三', url)
                    }
                     if ((args.datafield == "arrear_add" || args.datafield == "arrear_reduce") && sup_name) {
                        url = `Report/SupplierAccountHistory?&supcust_id=${supcust_id}&sup_name=${sup_name}&startDay=${startDay}&endDay=${now}&sub_type=QK&sub_type_name=欠款`;
                        title="供应商往来账"
                        window.parent.newTabPage(title, `${url}`);
                    }


                });
            $('#supcust_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popClient').jqxWindow('open');
                        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/SuppliersView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
           });


        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "SuppliersView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                    $.ajax({
                        url: '/api/BuySheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: null },
                        success: function(data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });

                }
                $('#popClient').jqxWindow('close');
            }
    });

        function beforeQuery() {
            let endDay = $('#endDay').jqxDateTimeInput('getDate');
            let endToday = new Date();
            endToday.setHours(23);
            endToday.setMinutes(59);
            endToday.setSeconds(59);
            if (new Date(endDay) > endToday) {
                $('#endDay').jqxDateTimeInput('setDate', endToday);
            }
        }

    </script>
</head>

<body style="overflow:hidden">

    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail" style="width:calc(100% - 100px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>


    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>


    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
</body>
</html>