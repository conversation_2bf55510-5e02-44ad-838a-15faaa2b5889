﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class SupplierArrearsModel : PageQueryModel
    { 
        public SupplierArrearsModel(CMySbCommand cmd) : base(Services.MenuId.supplierArrears)
        {
            this.cmd = cmd;
            this.PageTitle = "应付款";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"supcus_id",new DataItem(){Title="供应商",FldArea="divHead", LabelFld="sup_name",ButtonUsage="list",CompareOperator="=",SqlFld="sc.supcust_id",
                SqlForOptions=CommonTool.selectSuppliers } },
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                   "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"supcust_id",     new DataItem(){Title="供应商",SqlFld = "b.supcust_id",Hidden = true } },
                       {"sup_name",     new DataItem(){Title="供应商名称", Width="300",Linkable = true }},
                       {"balance",     new DataItem(){Title="应付款", Width="80",SqlFld="balance*(-1)",ShowSum=true,Linkable = true}},
                       {"net_amount",   new DataItem(){Title="采购净额", Width="100",SqlFld="sum(total_amount*money_inout_flag*(-1))",ShowSum=true}},
                       {"disc_amount",     new DataItem(){Title="优惠金额", Width="100",SqlFld="sum(sm.disc_amount*money_inout_flag*(-1))",ShowSum = true}},
                       {"paid_amount",     new DataItem(){Title="已收金额", Width="100",SqlFld="sum(sm.paid_amount*money_inout_flag*(-1))",ShowSum = true}},
                     },
                     QueryFromSQL=@"from arrears_balance b 
                                    left join client_account_history cah on b.supcust_id = cah.supcust_id and cah.company_id=~COMPANY_ID 
                                    left join (select * from sheet_buy_main where company_id =~COMPANY_ID and red_flag is null and approve_time is not null and total_amount<>(paid_amount+disc_amount)) sm on b.supcust_id = sm.supcust_id and cah.sheet_id = sm.sheet_id
                                    left join info_supcust sc on b.supcust_id = sc.supcust_id and sc.company_id = ~COMPANY_ID 
                                    left join info_operator op on sm.seller_id = op.oper_id and op.company_id = ~COMPANY_ID
                                    where b.company_id=~COMPANY_ID and balance<>0 and supcust_flag in ('S','CS')",
                     QueryGroupBySQL = " group by sup_name,b.supcust_id,b.balance",
                     QueryOrderSQL=" order by b.balance desc"
                  }

                } 
            };             
        }


        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class SupplierArrearsController : QueryController
    { 
        public SupplierArrearsController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SupplierArrearsModel model = new SupplierArrearsModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            SupplierArrearsModel model = new SupplierArrearsModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            SupplierArrearsModel model = new SupplierArrearsModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
