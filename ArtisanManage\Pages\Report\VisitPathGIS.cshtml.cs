using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage
{
    public class VisitPathGISModel : PageQueryModel
    { 
        public VisitPathGISModel(CMySbCommand cmd) : base(Services.MenuId.visitPath)
        {
            this.cmd = cmd;
            FuncDealCondition = (condi) =>
            {
                if (condi == "") return "";
                //"seller_id=12 and start_time='2001-12-1'"
                condi = condi.Replace("where", "");
                var arr = condi.Split(" and ");
                string newCondi = "";
                foreach (var s in arr)
                {
                    int n = s.IndexOf("happen_time");
                    if (n >= 0)
                    {
                        var ss = s.Split("=");
                        var sTime = ss[1]; 
                        sTime = sTime.Replace("'", "");
                        if (sTime == "")
                            return condi;
                        var time = CPubVars.GetDateText(Convert.ToDateTime(sTime).Date);
                        if (newCondi != "") newCondi += " and ";
                        newCondi += $" happen_time>'{ time}' and happen_time<'{time} 23:59:59'";
                    }
                    else
                    {
                        if (newCondi != "") newCondi += " and ";
                        newCondi += s;
                    }

                }
                newCondi = " where " + newCondi;
                return newCondi;

            };
            DataItems = new Dictionary<string, DataItem>()
            {
                {"seller_id",new DataItem(){Title="业务员",SqlFld="st.seller_id",LabelFld="oper_name",ButtonUsage="list",CompareOperator="=", Necessary=true,
                    SqlForOptions ="select oper_id as v,oper_name as l from info_operator where is_seller"}},
                {"startDay",new DataItem(){Title="日期", CtrlType="jqxDateTimeInput", SqlFld="happen_time",CompareOperator="=",Necessary=true,
                }},
                
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {

                {

                  "gridItems",  new QueryGrid()
                  {
                     //DbPageSize=10000,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"st.longitude",new DataItem(){Title="经度"}},
                       {"st.latitude", new DataItem(){Title="纬度"}},
                       {"st.visit_id", new DataItem(){Title="拜访流水号"}},
                       {"sv.door_picture", new DataItem(){Title="门店照片"} },
                       {"sv.start_time", new DataItem(){Title="到访时间"}},
                       {"sv.end_time", new DataItem(){Title="签退时间"}},
                       {"isc.boss_name", new DataItem(){Title="老板姓名"}},
                       {"isc.sup_name", new DataItem(){Title="店铺名字"} },
                       {"isc.mobile", new DataItem(){Title="手机号码"} },
                        {"isc.sup_addr", new DataItem(){Title="地址"} }
                     },
/*                                     $"JOIN sheet_visit sv ON sv.visit_id = st.visit_id " +
                $"JOIN info_suscust is ON is.supcust_id=sv.supcust_id" +*/
                     QueryFromSQL=
                     " FROM seller_trail st " +
                     " LEFT JOIN sheet_visit sv ON st.visit_id = sv.visit_id " +
                     " LEFT JOIN info_supcust isc ON sv.supcust_id=isc.supcust_id ",
                     QueryOrderSQL=" order by happen_time ASC "
                  }
                }
            };

        }

        public async Task OnGet(string operKey)
        {
            OperKey = operKey; 
            await InitGet(cmd);

        }
    }

    [Route("api/[controller]/[action]")]
    public class VisitPathGISController : QueryController
    { 
        public VisitPathGISController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpGet]
        public async Task<object> GetSellerVisitList(string operKey,string queryDate)
        {

            DateTime visitDay = DateTime.Today;
            if (queryDate.IsValid())
            {
               visitDay = Convert.ToDateTime(queryDate);

            }
            string visitDayStr = visitDay.ToString();
            string nextDayStr = visitDay.AddDays(1).ToString();
            Security.GetInfoFromOperKey(operKey, out string companyID);

            //string sql = @$"SELECT COUNT(1),sv.seller_id,op.oper_name 
            //            FROM sheet_visit sv
            //            LEFT JOIN g_operator op ON sv.seller_id = op.oper_id
            //            WHERE (start_time between '{visitDayStr}' and '{nextDayStr}')
            //            AND sv.company_id={companyID}
            //            GROUP BY sv.seller_id,op.oper_name";

            string sql = @$"SELECT iop.oper_id as seller_id,iop.oper_name as seller_name,sv.svcount 
                        FROM info_operator iop
                        LEFT JOIN 
                        (select seller_id,count(1) as svcount from sheet_visit   WHERE start_time between '{visitDayStr}' and '{nextDayStr}' and company_id ={companyID} group by seller_id) sv
                        on sv.seller_id=iop.oper_id
                        where iop.company_id={companyID} and COALESCE(iop.status,'1')='1'  ";
            object data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return Json(new { data });
        }
        [HttpGet]
        public async Task<object> GetSellerTrailPoints(string operKey, string queryDate,string querySellerID)
        {
            DateTime trailDay = DateTime.Today;
            if (queryDate.IsValid())
            {
                trailDay = Convert.ToDateTime(queryDate);

            }
            string trailDayStr = trailDay.ToString();
            string nextDayStr = trailDay.AddDays(1).ToString();
            Security.GetInfoFromOperKey(operKey, out string companyID);

            string sql = @$"SELECT
            st.longitude,st.latitude,st.happen_time,sv.seller_id,
            sv.visit_id,sv.start_time,sv.end_time,(select round(date_part('epoch', end_time::TIMESTAMP - start_time::TIMESTAMP)::NUMERIC / 60)) intervals,
            isp.boss_name,isp.sup_name,isp.mobile,isp.addr_lng as sup_addr_lng,isp.addr_lat as sup_addr_lat,
            ssm.sheet_id,ssm.total_amount as sale_total_amount,
            som.sheet_id as sale_order_sheet_id,som.total_amount as sale_order_total_amount
            from seller_trail st
            left join sheet_visit sv 
            on st.visit_id=sv.visit_id and st.company_id=sv.company_id
            left join sheet_sale_main ssm
            on ssm.visit_id=st.visit_id and st.company_id=ssm.company_id
            left join sheet_sale_order_main som 
            on som.visit_id=st.visit_id and st.company_id=som.company_id
            left join info_supcust isp 
            on isp.supcust_id=sv.supcust_id and st.company_id=isp.company_id
            where st.company_id={companyID}
            and st.seller_id={querySellerID}
            and (st.happen_time between '{trailDayStr}' and '{nextDayStr}') order by st.happen_time  ASC";
            object data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return Json(new { data });
        }
        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            VisitPathModel model = new VisitPathModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpGet]
        public async Task<object> GetSellerCurPosition(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string today = DateTime.Today.ToString();
            string nextDay = DateTime.Today.AddDays(1).ToString();
            string sql = $@"SELECT st.seller_id,iot.oper_name,st.happen_time,st.longitude,st.latitude 
                            FROM seller_trail st
                            RIGHT JOIN ( SELECT st.seller_id, max(st.happen_time) as happen_time FROM seller_trail st where  st.company_id = {companyID}  GROUP BY st.seller_id ) g_st
                            ON st.seller_id = g_st.seller_id AND st.happen_time = g_st.happen_time
                            LEFT JOIN info_operator iot
                            ON  iot.oper_id=st.seller_id and iot.company_id = {companyID} 
                            WHERE (st.happen_time BETWEEN '{today}' AND '{nextDay}' ) AND st.company_id={companyID}						 ";
            object data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return Json(new { data });
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            VisitPathModel model = new VisitPathModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

    }
}