﻿@page
@model ArtisanManage.Pages.Report.ItemsOrderedListModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
         window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
            var m_db_id = "10";

    	    var newCount = 1;

            var itemSource = {};
        function btnSelectItems_click() {
            var rows = $('#gridItems').jqxGrid('getrows')
            var checkedRows = []
            rows.forEach(function (row) {
                if (row.multi_selected) {
                    checkedRows.push({ item_id: row.item_id, item_name: row.item_name })
                }
            })
            var msg = {
                msgHead: 'ItemsOrderedList', action: 'selectMulti', checkedRows: checkedRows
            }
            window.parent.postMessage(msg, '*');
        }
    	$(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            if (!window.ForSelect) {
                $('#gridItems').jqxGrid('hidecolumn', 'multi_selected')
                $('#btnSelectItems').hide()
            }

                
            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                if (args.datafield == "item_name") {
                    if (args.originalEvent.button == 2) return;
                    var item_id = args.row.bounddata.item_id;
                    RowIndex = args.rowindex;

                    if (ForSelect) {
                        var item_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "item_id");
                        var item_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "item_name");
                        var msg = {
                            msgHead: 'ItemsOrderedList', action: 'select', item_id: item_id, item_name: item_name
                        };
                        window.parent.postMessage(msg, '*');

                    }

                }
            });
                
            $('#supcust_id').jqxInput({
                onButtonClick: function (event) {
                    $('#popClient').jqxWindow('open');
                    $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                }
            });
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

         
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
             
            QueryData();
        });

        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                    $.ajax({
                        url: '/api/ItemsOrderedList/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: item_id },
                        success: function (data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });

                }
                $('#popClient').jqxWindow('close');
            }
            else if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "select") {
                    var item_id = rs.data.item_id;
                    var item_name = rs.data.item_name;
                    $('#item_id').jqxInput('val', { value: item_id, label: item_name });

                    $.ajax({
                        url: '/api/SaleSheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: item_id },
                        success: function (data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });
                }
                $('#popItem').jqxWindow('close');
            }

        });
    </script>
</head>

<body style="overflow:hidden">
    <style>
    </style>
    <div style="display:flex;justify-content:space-around;margin-top:20px;">
        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <div><button id="btnSelectItems" onclick="btnSelectItems_click()">选择商品</button></div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        @*<div style="display: flex;"><label>订货款账户</label> <div id="prepay_sub_id" style="width:120px;height:20px; margin-left:20px;"></div></div>
            <div style="display: flex;"><label>客户</label> <div id="status" style="width:120px;height:20px; margin-left:20px;"></div></div>
            <div><input id="searchString" style="font-size:14px; border-radius:6px;border-color:#ddd;border-width:0.5px; width:200px;height:25px;" placeholder="请输入简拼/名称" /></div>
        *@

    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>


    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择定货会商品</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popItem" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择商品</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>