﻿@page
@model ArtisanManage.Pages.Report.BusinessHistoryListsModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var m_db_id = "10";

    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
                QueryData();
                
                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                    if (args.datafield == "sheet_no"  ) {
                        var sheet_id = args.row.bounddata.sheet_id;
                        var sheet_type = args.row.bounddata.sheet_type;
                        var sheet_type_name = args.row.bounddata.sheet_type_name;

                        var sheet_no = args.row.bounddata.sheet_no;
                        //var sheetType = sheet_no.replace(/[^a-zA-Z]/g, '');
                        var sheetType = sheet_type;
                        //window.parent.newTabPage(sheet_type, `Sheets/BuySheet?sheet_id=${sheet_id}`);
                        if (sheet_type == "CG" || sheet_type=="CT"){
                            window.parent.newTabPage(sheet_type_name, `Sheets/BuySheet?sheet_id=${sheet_id}`);
                        } else if (sheet_type == "XD" || sheet_type == "TD") {
                            window.parent.newTabPage(sheet_type_name, `Sheets/SaleOrderSheet?sheet_id=${sheet_id}`);
                        } else if (sheet_type == "X" || sheet_type == "T") {
                            window.parent.newTabPage(sheet_type_name, `Sheets/SaleSheet?sheet_id=${sheet_id}`);
                        } else if (sheet_type == "SK" || sheet_type == "FK") {
                            window.parent.newTabPage(sheet_type_name, `Sheets/GetArrearsSheet?sheet_id=${sheet_id}`);
                        } else if (sheet_type == "YS" || sheet_type == "YF") {
                            window.parent.newTabPage(sheet_type_name, `Sheets/PrepaySheet?sheet_id=${sheet_id}`);
                        } else if (sheet_type == "ZC" || sheet_type == "SR") {
                            window.parent.newTabPage(sheet_type_name, `Sheets/FeeOutSheet?sheet_id=${sheet_id}`);
                        } else if (sheet_type == "DB") {
                            window.parent.newTabPage(sheet_type_name, `Sheets/MoveSheet?sheet_id=${sheet_id}`);
                        } else if (sheet_type == "YK") {
                            window.parent.newTabPage(sheet_type_name, `Sheets/InventChangeSheet?sheet_id=${sheet_id}`);
                        } else if (sheet_type == "BS") {
                            window.parent.newTabPage(sheet_type_name, `Sheets/InventChangeSheet?sheet_id=${sheet_id}`);
                        } else if (sheet_type == "DH") {
                            window.parent.newTabPage(sheet_type_name, `Sheets/OrderItemSheet?sheet_id=${sheet_id}`);
                        } 
                        

                    }
                });

                //$('#supcust_id').jqxInput({
                //    onButtonClick: function (event) {
                //        $('#popClient').jqxWindow('open');
                //        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                //    }
                //});
                //$("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                
            });

        window.addEventListener('message', function (rs) {

            //if (rs.data.msgHead === "ClientsView") {
            //    if (rs.data.action === "select") {
            //        var supcust_id = rs.data.supcust_id;
            //        var sup_name = rs.data.sup_name;
            //        $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

            //    }
            //    $('#popClient').jqxWindow('close');
            //}
        });
    </script>
</head>

<body>

    <div style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>

        <button onclick="QueryData()" style="margin-right:20px;margin-top:30px;">查询</button>
        <button id="export" onclick="ExportExcel()" style="margin-left:20px;margin-top:30px;">导出</button>

    </div>


    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>


</body>
</html>