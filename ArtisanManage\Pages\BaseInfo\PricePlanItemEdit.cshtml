﻿@page
@model ArtisanManage.Pages.BaseInfo.PricePlanItemEditModel
@{
    Layout = null;
} 
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>PricePlanItemEdit</title>
    <!--
    <link rel="stylesheet" href="~/css/DataForm.css" type="text/css" />
    <script type="text/javascript" src="~/js/DataForm.js"></script>
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmyinput.js"></script>

    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxbuttons.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxscrollbar.js"></script>

    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdropdowntree.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxtree.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmenu.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmygrid.edit.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.selection.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.columnsresize.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.sort.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.aggregates.js"></script>
  -->
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
     
  
    <script type="text/javascript">
        @Html.Raw(Model.m_saveCloseScript)
        window.plan_id=@Html.Raw(Model.pid);
           var JsonCompanySetting = @Html.Raw(Model.JsonCompanySetting);
        
        
        $(document).ready(function () {
             @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            var seeInPrice = false;
            window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);
            if (window.g_operRights && window.g_operRights.delicacy
                && window.g_operRights.delicacy.seeInPrice
                && window.g_operRights.delicacy.seeInPrice.value) {
                seeInPrice = true;
            }
            if (seeInPrice == false) {
                $('#div_seeInPrice').hide();
            }
            if (!$('#m_unit_no').val()) {
                $('#div_m_unit_wholesale_price').hide()
                $('#div_m_unit_buy_price').hide()
            }
            if (!$('#b_unit_no').val()) {
                $('#div_b_unit_wholesale_price').hide()
                $('#div_b_unit_buy_price').hide()
            }
                
            var rows = $('#gridUnit').jqxGrid('getrows');
            var heightIndex = 0;
            var priceIndex = 0;
            for (var i = 0; i < rows.length; i++) {//1 row 30px
                var row = rows[i];
                if(row.plan_id){
                    priceIndex++;
                    if (row.plan_id == window.plan_id) {
                    heightIndex = i; break;
                }
                }
               
            } 
            setTimeout(() => {
                // $('#gridUnit').jqxGrid('scrolloffset', heightIndex*30, 0);
            },3000)
           


            $("#gridUnit").on('cellendedit', function (event) {

                // event arguments.
                var args = event.args;
                // column data field.
                var colName = event.args.datafield;
                // row's bound index.
                var rowIndex = event.args.rowindex;
                var cellValue = args.value;
                var oldValue = args.oldvalue;
              //   setTimeout(function () {

                    if(colName == "plan_id"){
                        var row = $('#gridUnit').jqxGrid('getrowdata',0);
                        $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "sunit", row.sunit);
                        $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "bunit", row.bunit);
                        $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "munit", row.munit);
                        $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "bfactor", row.bfactor);
                        $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "mfactor", row.mfactor);
                        $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "buy_price", row.buy_price);
                        $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "wholesale_price", row.wholesale_price);
                    }


                    if (colName == "s_price"||colName == "m_price"||colName == "b_price") {

                        if (cellValue && !isNaN(cellValue) && parseFloat(cellValue) >= 0) {
                            var unitType = colName.split('_')[0]

                            var s_unit_no = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "sunit");
                            var b_unit_no = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "bunit");
                            var m_unit_no = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "munit");

                            var currFactorFld = unitType+'factor'
                            var currFactor = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, currFactorFld);//当前编辑的包装率一定是存在的，否则不允许编辑
                            if(unitType=='s') currFactor=1

                            price_unit = parseFloat(cellValue)/parseFloat(currFactor);

                            var bfactor = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, 'bfactor');
                            var mfactor = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, 'mfactor');
                            debugger
                            if (JsonCompanySetting && JsonCompanySetting.unitPriceRelated != 'False') {
                                               if(currFactorFld!='sfactor') {
                                                    $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "s_price", toMoney(price_unit,4));
                                                }

                                              if(mfactor && currFactorFld!='mfactor') {
                                                     $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "m_price", toMoney(price_unit*parseFloat(mfactor),4));
                                                 }
                                             if(bfactor && currFactorFld!="bfactor") {
                                                     $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "b_price", toMoney(price_unit*parseFloat(bfactor)));
                                                    }
                            }
                       
                             /*
                            //处理折扣
                            var wholesale_price = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "s_wholesale_price")
                            var b_wholesale_price = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "b_wholesale_price")
                            var s_wholesale_price = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "s_wholesale_price")
                            s_wholesale_price=parseFloat(s_wholesale_price)
                            var b_unit_factor = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "bfactor")
                            if (b_wholesale_price) {
                                b_wholesale_price = parseFloat(b_wholesale_price)
                                s_wholesale_price=b_wholesale_price/parseFloat(b_unit_factor)
                            }
                           
                            if(s_wholesale_price){
                                var discount = toMoney(price_unit/s_wholesale_price*100)
                                if(discount) $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "discount", discount)
                            }*/
                            //如果有中单位，处理中单位单元格
                        } else if(cellValue){
                            var msg = '请输入正确的金额';
                            bw.toast(msg, 3000);
                            $('#jqxgrid').jqxGrid('setcellvalue', rowIndex, colName, ''); 
                        }   
                            
                    }
                             
                    else if (colName == "discount") { 
                       
                        //处理被修改单元格自身，如果是合法输入则改变
                        if (cellValue && !isNaN(cellValue) && parseFloat(cellValue) > 0) {
                            /*
                            var b_wholesale_price = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "b_wholesale_price");
                            var s_wholesale_price = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "s_wholesale_price");
                            s_wholesale_price=parseFloat(s_wholesale_price)
                            var b_unit_factor = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "bfactor");
                            if (b_wholesale_price) {
                                b_wholesale_price = parseFloat(b_wholesale_price)
                                s_wholesale_price=b_wholesale_price/parseFloat(b_unit_factor)
                            }
                        
                           if(!s_wholesale_price) return;
                            $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "discount", formatNum(cellValue,2));
                            var s_unit_no = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "sunit");
                            var b_unit_no = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "bunit");
                            var m_unit_no = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, "munit");
                            var bfactor = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, 'bfactor');
                            var mfactor = $('#gridUnit').jqxGrid('getcellvalue', rowIndex, 'mfactor');
                      
                            var price_unit = parseFloat(s_wholesale_price)*parseFloat(cellValue)*0.01;
                            $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "s_price", formatNum(price_unit,2));
                            if(mfactor)  $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "m_price", formatNum(price_unit*parseFloat(mfactor),2));
                            if(bfactor)  $('#gridUnit').jqxGrid('setcellvalue', rowIndex, "b_price", formatNum(price_unit*parseFloat(bfactor),2));
                            */
                        }
                        else if (cellValue) {
                            bw.toast('折扣应为数字且为正', 2000);
                            //如果折扣是无效输入，重置折扣
                            $('#gridUnit').jqxGrid('setcellvalue', row, "discount", '');
                        }
                    }
                    //}, 0);
            });             

            
        });
        function formatNum(m,n){
                m=parseFloat(parseFloat(m).toFixed(n));
                for(var i=0;i<=n;i++){
                    var t = parseFloat(parseFloat(m).toFixed(i));
                    if(m==t)  return t;
                }
        }

        function dealGridRowsOnLoad(rows){
            rows.forEach((row)=>{
                if(row.discount) row.discount = parseFloat(row.discount)*100

            })
            return rows
        }

        function dealFormData(formData) {
            for (var i = 0; i < formData.gridUnit.length; i++) {
                var row = formData.gridUnit[i];
                if(row.plan_id&&row.discount) {
                    row.discount = parseFloat(row.discount)/100
                }
                formData.gridUnit[i] = row;
            }
        }

        function checkDataValid(formData){
            var msg = ''
            for (var i = 0; i < formData.gridUnit.length; i++) {
                var row = formData.gridUnit[i];
                if(row.plan_id && !row.discount && !row.s_price && !row.m_price && !row.b_price) {
                    msg = '第'+i+1+'行'+row.plan_name+'的必须设置价格或折扣'
                    break;
                }
            }
            if (msg) {

                bw.toast(msg,2000)
                return false
            }
            return true;
        }
        function onPriceChange(event,unitType,priceType) { 
            let newValue = event.target.value
            let computedFlag = false
            if (JsonCompanySetting) { 
                if (JsonCompanySetting.unitPriceRelated != "False") computedFlag = true
            }
            if (computedFlag) {
                         let m_unit_factor =  $('#m_unit_factor').jqxInput('val')
                         let b_unit_factor = $('#b_unit_factor').jqxInput('val')
                         let bPrice = ''  
                         let mPrice = ''
                         let sPrice = ''
                switch (unitType) { 
                    case 'b':
                        bPrice = newValue
                        sPrice = toMoney(bPrice / b_unit_factor, 4)
                        mPrice = toMoney(sPrice * m_unit_factor, 4)
                        break;
                    case 'm':
                        mPrice = newValue
                        sPrice = toMoney(mPrice / m_unit_factor, 4)
                        bPrice = toMoney(sPrice * b_unit_factor, 2)
                        break;
                    case 's':
                        sPrice = newValue
                        mPrice = toMoney(sPrice * m_unit_factor, 4)
                        bPrice = toMoney(sPrice * b_unit_factor, 2)
                        break;
                  default :
                            return

                                        
                }
                if (priceType == 'sale') {
                    $('#s_wholesale_price').val(sPrice)
                    $('#m_wholesale_price').val(mPrice)
                    $('#b_wholesale_price').val(bPrice)
                } else {
                     $('#s_buy_price').val(sPrice)
                    $('#m_buy_price').val(mPrice)
                    $('#b_buy_price').val(bPrice)
                }
            }
           
        }
    </script>
</head>
<body>
    <style>
        .row{
            display:flex;
            height:20px;
            margin-top:20px;
        }
        .row>div:first-child{
            text-align:right;width:100px;
            justify-content:flex-end;
            padding-right:10px;
        }
        .row input{
            border:none;
            border-bottom:1px solid #bbb;
            outline:0px; 
        }
             
            .row > div {
                display: flex;
            }
            .row > div>div {
                margin-right:20px;
            }
        .price-input {
            width: 60px;
            text-align: center;
            
        }
    </style>
    <div id="divHead"  style="width:600px;padding-top:0px;padding-bottom:20px;">
        <input id="item_id" type="hidden"/> 
        <div class="row">
             <div>商品名称</div>
            <div><input id="item_name" readonly style="width:300px;" /></div>
            <div style="margin-left:30px;"><span id="units_relation"></span></div>
           
        </div>
        
        <div class="row">
            <div>批发价</div>
            <div>
                 <div id="div_b_unit_wholesale_price"><input id="b_wholesale_price" class="price-input" autocomplete ="off" onchange="onPriceChange(event,'b','sale')"/><span>元/</span><span id="b_unit_no"></span></div>
                 <div id="div_m_unit_wholesale_price"><input id="m_wholesale_price" class="price-input" autocomplete ="off" onchange="onPriceChange(event,'m','sale')"/><span>元/</span><span id="m_unit_no"></span></div>
                 <div><input id="s_wholesale_price" class="price-input" autocomplete ="off" onchange="onPriceChange(event,'s','sale')"/><span>元/</span><span id="s_unit_no"></span></div>
            </div>
        </div>  
         <div class="row" id="div_seeInPrice">
            <div>进价</div>
            <div>
                 <div id="div_b_unit_buy_price"><input id="b_buy_price" class="price-input" autocomplete ="off" onchange="onPriceChange(event,'b','buy')"/><span>元/</span><span id="b_unit_no_1"></span></div>
                 <div id="div_m_unit_buy_price"><input id="m_buy_price" class="price-input" autocomplete ="off" onchange="onPriceChange(event,'m','buy')"/><span>元/</span><span id="m_unit_no_1"></span></div>
                 <div><input id="s_buy_price" class="price-input" autocomplete ="off" onchange="onPriceChange(event,'s','buy') "/><span>元/</span><span id="s_unit_no_1"></span></div>
            </div>
        </div> 

    </div> 
    @*<div style="margin-top:10px; margin-left:40px;margin-bottom:10px;font-weight:bold;">价格方案设置</div>*@
    <div id="gridUnit" style="width:calc(100% - 20px);margin-left:10px;margin-right:10px;height:350px;"> </div>
    <div style="text-align:center;margin-top:20px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;">保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
    <div></div>   
    
</body>
</html>
