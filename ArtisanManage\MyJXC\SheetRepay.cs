﻿using ArtisanManage.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace ArtisanManage.MyJXC
{
    public class SheetRepay<TROW>: SheetBase<TROW> where TROW : SheetRowBase, new()
    { 
        [SaveToDB] [FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB][FromFld("t.loan_sheet_id")] public string loan_sheet_id { get; set; }//贷款单单号
        [FromFld("sl.loan_sheet_no")] public string loan_sheet_no { get; set; }//贷款单单号
        [SaveToDB][FromFld] public string installment_no { get; set; }
        [FromFld("pl.installment_count")] public string installment_count { get; set; }
        [SaveToDB][FromFld] public string partner_id { get; set; }//借贷款单位
        [FromFld(LOAD_PURPOSE.SHOW)] public string partner_name { get; set; }
        [SaveToDB][FromFld] public string loan_partner_id { get; set; }//sub_id 借款科目
        [FromFld(LOAD_PURPOSE.SHOW)] public string loan_partner_name { get; set; }
        [SaveToDB][FromFld] public string getter_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string getter_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public DateTime repay_date { get; set; }
        [FromFld("sl.repay_way")] public string repay_way { get; set; }
        [FromFld("(case sl.repay_way when 'EPI' then '等额本息' when 'IOP' then '先息后本' end)")] public string repay_way_name { get; set; }
        [FromFld("sl.repay_period")] public string repay_period { get; set; }
        [FromFld("(case sl.repay_period when 'M' then '按月还款' when 'Y' then '按年还款'  when 'Q' then '按季度还款'  when 'SY' then '按半年还款' end)")] public string repay_period_name { get; set; }
        [FromFld("sl.interest_rate_month*100")] public decimal interest_rate_month { get; set; } = 0;
        [FromFld("sl.interest_rate_year*100")] public decimal interest_rate_year { get; set; } = 0;
        [FromFld("pl.period_range")] public string period_range { get; set; }
        [SaveToDB][FromFld] public override int money_inout_flag { get; set; } = -1;

        [FromFld(LOAD_PURPOSE.SHOW)] public decimal principal_total { get; set; } = 0;//sheet_loan

        [FromFld(LOAD_PURPOSE.SHOW)] public decimal period_total_due { get; set; } = 0;//sheet_loan_repay_plan
        [FromFld(LOAD_PURPOSE.SHOW)] public decimal period_principal_due { get; set; } = 0;//sheet_loan_repay_plan
        [FromFld(LOAD_PURPOSE.SHOW)] public decimal period_interest_due { get; set; } = 0;//sheet_loan_repay_plan

        [FromFld("round((pl.period_total_due-t.period_total_to_pay)::numeric,2)")] public decimal period_paid_amount { get; set; } = 0;
        [FromFld("round((pl.period_principal_due-t.period_principal_to_pay)::numeric,2)")] public decimal period_principal_paid { get; set; } = 0;
        [FromFld("round((pl.period_interest_due-t.period_interest_to_pay)::numeric,2)")] public decimal period_interest_paid { get; set; } = 0;

        [SaveToDB][FromFld] public decimal period_total_to_pay { get; set; } = 0;
        [SaveToDB][FromFld] public decimal period_principal_to_pay { get; set; } = 0;
        [SaveToDB][FromFld] public decimal period_interest_to_pay { get; set; } = 0;

        [SaveToDB][FromFld] public override decimal total_amount { get; set; } = 0;
        [SaveToDB][FromFld] public decimal now_pay_amount { get; set; } = 0;
        [SaveToDB][FromFld] public decimal pay_principal_due { get; set; } = 0;
        [SaveToDB][FromFld] public decimal pay_interest_due { get; set; } = 0;

        [SaveToDB][FromFld] public string payway1_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway1_amount { get; set; } = 0;
        [SaveToDB][FromFld] public string payway2_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway2_amount { get; set; } = 0;


        public SheetRepay() : base("sheet_repay", "", LOAD_PURPOSE.APPROVE)
        {
            sheet_type = SHEET_TYPE.SHEET_REPAY_MONEY;
            ConstructFun();
        }

        public SheetRepay(LOAD_PURPOSE loadPurpose) : base("sheet_repay", "", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_REPAY_MONEY;
            ConstructFun();
        }

        private void ConstructFun()
        {
            MainLeftJoin = @$" 
left join (select sheet_id, sheet_no as loan_sheet_no, total_amount as principal_total, interest_rate_month, interest_rate_year, repay_way, repay_period from sheet_loan where company_id=~COMPANY_ID) sl on sl.sheet_id=t.loan_sheet_id
left join (select sheet_id as loan_sheet_id, coalesce(period_total_due,0) as period_total_due, coalesce(principal_due,0) as period_principal_due, coalesce(interest_due,0) as period_interest_due, repay_date, period_range, installment_no, installment_count from sheet_loan_repay_plan where company_id=~COMPANY_ID) pl on pl.loan_sheet_id=t.loan_sheet_id and pl.installment_no=t.installment_no
left join info_loan_partner p on p.company_id=~COMPANY_ID and t.partner_id=p.partner_id
left join (select oper_id,oper_name as getter_name from info_operator where company_id=~COMPANY_ID) getter on t.getter_id=getter.oper_id
left join (select oper_id,oper_name as maker_name from info_operator where company_id=~COMPANY_ID) maker on t.maker_id=maker.oper_id
left join  (select oper_id,oper_name as approver_name from info_operator where company_id=~COMPANY_ID) approver on t.approver_id=approver.oper_id
left join (select sub_id,sub_name as payway1_name from cw_subject where company_id=~COMPANY_ID) pw1 on t.payway1_id=pw1.sub_id
left join (select sub_id,sub_name as payway2_name from cw_subject where company_id=~COMPANY_ID) pw2 on t.payway2_id=pw2.sub_id
left join (select sub_id,sub_name as loan_partner_name from cw_subject where company_id=~COMPANY_ID) los on t.loan_partner_id=los.sub_id ";
        }

        public override string GetSheetCharactor()
        {
            string res = $"{this.company_id}_{this.OperID}_{this.partner_id}_{this.total_amount.ToString()}_{this.payway1_id}_{this.payway2_id}_{this.make_brief}";
            return res;
        }

        protected override void InitForSave()
        {
            base.InitForSave();
            if (getter_id == "") getter_id = OperID;
            if (approver_id == "") approver_id = OperID;
            money_inout_flag = red_flag == "2" ? 1 : -1;
        }

        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            if (partner_id == "") return "必须指定借贷款单位";
            if (getter_id == "" && IsFromWeb) return "必须指定业务员";
            if (loan_partner_id == "") return "必须指定账户";

            if (payway1_id == "" || payway1_amount == 0) return "必须指定支付方式";
            if (payway2_id == "" && payway2_amount != 0) return "请选择支付方式2";
            if (total_amount <= 0 || now_pay_amount <= 0) return "请输入正确的支付金额";
            if (total_amount != pay_principal_due + pay_interest_due) return "本次支付总额与本次支付利息+本次支付本金不一致";
            if (total_amount != payway1_amount + payway2_amount) return "本次支付总额与支付方式合计不一致";
            if (total_amount > period_total_due - period_paid_amount) return "本单还款总额大于本期剩余总额";
            if (pay_principal_due > period_principal_due - period_principal_paid) return "本单还款本金大于本期剩余本金";
            if (pay_interest_due > period_interest_due - period_interest_paid) return "本单还款利息大于本期剩余利息";

            return "OK";
        }

        public class CInfoForApprove : CInfoForApproveBase
        {
            public List<Subject> PaywaysInfo = new List<Subject>();
        }

        public class Subject
        {
            public string sub_id { get; set; }
            public string sub_name { get; set; }
            public string sub_type { get; set; }
        }

        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            string sql;

            string sub_ids = string.Join(',', new string[] { payway1_id, payway2_id }.Where(p => !string.IsNullOrEmpty(p)));
            if (sub_ids != "")
            {
                sql = $"select sub_id, sub_name, sub_type from cw_subject where company_id={company_id} and sub_id in ({sub_ids});";
                QQ.Enqueue("payway_type", sql);
            }

            sql = $"select red_flag, coalesce(paid_amount,0) as paid_amount, coalesce(principal_paid,0) as principal_paid, coalesce(interest_paid,0) as interest_paid from sheet_loan_repay_plan where company_id={company_id} and sheet_id={loan_sheet_id} and installment_no={installment_no}";
            QQ.Enqueue("loan_plan", sql);

            if (red_flag == "2")
            {
                //还贷款单类似收款单，每一张还贷款单上记录了当时的待还，红冲前面还的会导致后面还贷款单上记录的待还不对，必须按审核时间从后往前依次红冲
                sql = $"select sheet_id, sheet_no from sheet_repay where company_id={company_id} and loan_sheet_id={loan_sheet_id} and installment_no={installment_no} and approve_time>=(select approve_time from sheet_repay where company_id={company_id} and sheet_id={red_sheet_id}) and sheet_id<>'{red_sheet_id}' and red_flag is null";
                QQ.Enqueue("repay_after", sql);
            }
            
            
        }

        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);

            if (sqlName == "payway_type")
            {
                info.PaywaysInfo = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
            else if (sqlName == "loan_plan")
            {
                dynamic plan = CDbDealer.Get1RecordFromDr(dr, false);
                if (plan == null)
                {
                    info.ErrMsg = "还款计划不存在";
                }
                else
                {
                    if (red_flag=="" && (Math.Abs(Convert.ToDecimal(plan.paid_amount) - period_paid_amount) > 0.01m || Math.Abs(Convert.ToDecimal(plan.principal_paid) - period_principal_paid) > 0.01m || Math.Abs(Convert.ToDecimal(plan.interest_paid) - period_interest_paid) > 0.01m))
                    {
                        info.ErrMsg = "本期已还款金额发生了改变，请重新开单";
                    }
                    if (plan.red_flag != "")
                    {
                        info.ErrMsg = "贷款单已红冲，请检查";
                    }
                }
            }
            else if (sqlName == "repay_after")
            {
                dynamic repay_after = CDbDealer.Get1RecordFromDr(dr, false);
                if (repay_after != null) 
                {
                    info.ErrMsg = $"请先红冲本单之后审核的单据【{repay_after.sheet_no}】";
                }
            }

        }

        

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";

            #region 还贷款
            decimal nowPayTotal = Math.Round(now_pay_amount * money_inout_flag, 2);
            decimal nowPayPrincipal = Math.Round(pay_principal_due * money_inout_flag, 2);
            decimal nowPayInterest = Math.Round(pay_interest_due * money_inout_flag, 2);
            sql += $@"update sheet_loan_repay_plan set paid_amount=coalesce(paid_amount,0)-({nowPayTotal}), principal_paid=coalesce(principal_paid,0)-({nowPayPrincipal}), interest_paid=coalesce(interest_paid,0)-({nowPayInterest}) where company_id={company_id} and sheet_id={loan_sheet_id} and installment_no={installment_no}; 
                update sheet_loan_repay_plan set status='no' where company_id={company_id} and sheet_id={loan_sheet_id} and round(paid_amount::numeric,2)=0; 
                update sheet_loan_repay_plan set status='part' where company_id={company_id} and sheet_id={loan_sheet_id} and round(period_total_due::numeric,2)>round(paid_amount::numeric,2) and round(paid_amount::numeric,2)<>0 ;
                update sheet_loan_repay_plan set status='all' where company_id={company_id} and sheet_id={loan_sheet_id}  and round(period_total_due::numeric,2)=round(paid_amount::numeric,2); 
                update sheet_loan set paid_amount=coalesce(paid_amount,0)-({nowPayTotal}), paid_principal_total=coalesce(paid_principal_total,0)-({nowPayPrincipal}), paid_interest_total=coalesce(paid_interest_total,0)-({nowPayInterest}) where company_id={company_id} and sheet_id={loan_sheet_id}; ";//减法是因为带了money_input_flag
            #endregion

            #region 更新现金银行余额
            string sql_cb = "";
            if (info.BizStartPeriod != "" && info.PaywaysInfo != null && !IsImported)
            {
                Dictionary<string, decimal> pws = new Dictionary<string, decimal>();
                Subject pw1 = info.PaywaysInfo.Find(p => p.sub_id == payway1_id && p.sub_type == "QT");
                if (pw1 != null && payway1_amount != 0)
                {
                    if (!pws.ContainsKey(payway1_id)) pws.Add(payway1_id, payway1_amount);
                    else pws[payway1_id] += payway1_amount;
                }
                Subject pw2 = info.PaywaysInfo.Find(p => p.sub_id == payway2_id && p.sub_type == "QT");
                if (pw2 != null && payway2_amount != 0)
                {
                    if (!pws.ContainsKey(payway2_id)) pws.Add(payway2_id, payway2_amount);
                    else pws[payway2_id] += payway2_amount;
                }
                if (pws.Count() > 0)
                {
                    sql_cb = base.UpdateCashBankBalance(pws);
                }
            }
            #endregion

            cmd.CommandText = sql + sql_cb;
            if (cmd.CommandText != "")
            {
                await cmd.ExecuteNonQueryAsync();
            }
        }


    }
}
