﻿@page
@model ArtisanManage.Pages.BaseInfo.ItemsMatchesEditModel
@{
    Layout = null;
}
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>ItemsMatchesEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">


        @Html.Raw(Model.m_saveCloseScript)
            $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
                var clientId = getQueryStringParameter('client_id');
                var inputElement = $('#div_client_id input');

                // 检查client_id是否存在
                if (clientId) {
                    console.log(clientId)
                    // 如果client_id存在，禁用输入框
                    inputElement.prop('disabled', true);
                }

                    $('#div_client_id input').on('input', function () {
                        console.log("aa")
                    })
            });

        function getQueryStringParameter(name, url) {
            if (!url) url = window.location.href;
            name = name.replace(/[\[\]]/g, "\\$&");
            var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, " "));
        }
    </script>
</head>
<body>
    <div id="divHead" class="headtail" style="width:750px;"></div>
    <div id="gridItems" style="width:calc(650px);margin-top:10px;margin-left:10px;margin-right:0px;height:75%;"> </div>
    <div style="text-align:center;margin-top:20px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;">保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>
