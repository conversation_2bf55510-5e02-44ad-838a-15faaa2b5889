﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Services;
using ArtisanManage.YingjiangMessage.Services;
using Jint.Parser;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NPOI.POIFS.Crypt.Dsig;
using System;
using System.Collections.Generic;
using System.DrawingCore;
using System.DrawingCore.Imaging;
using System.ComponentModel.Design;
using System.Dynamic;
using System.Formats.Asn1;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;
using ArtisanManage.YingJiangMallMini.Services;
using Newtonsoft.Json.Linq;
using static ArtisanManage.Services.CommonTool;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Quartz.Util;

namespace ArtisanManage.AppController
{
    ///商品
    ///商品类别
    ///商品档案
    ///单个商品详情
    ///保存商品详情修改

    public class ProgressValueModel
    {
        public int  processedCount { get; set; }  
        public int total { get; set; }
    }
    /// <summary>
    /// 商品
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class InfoItemController : QueryController
    {
        public static  Dictionary<string, ProgressValueModel> importPictureProgress = new Dictionary<string, ProgressValueModel>();
        public static string ToFieldValue(dynamic d)
        {
            string fld = (string)d;
            return String.IsNullOrEmpty(fld) ? "null" : $"'{fld}'";
        }
        private readonly IHttpClientFactory _httpClientFactory;
        public InfoItemController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }


        /// <summary>
        /// 获取品牌ID,名称
        /// </summary>
        /// <param name="operKey"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetBrandList(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql = @$"select brand_id,brand_name from info_item_brand where company_id = {companyID};";
            var data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }
        [HttpGet]
        public async Task<JsonResult> GetItemStock(string operKey,string batchLevel,string itemId)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            cmd.ActiveDatabase = "";
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            //string sonSql = $@"";
            if (batchLevel.IsInvalid())
            {
                List<ExpandoObject> stockByItem = null;
                string stockByItemSql = $@"select * from stock where company_id = {companyID} and item_id ={itemId} and batch_id <>0 and stock_qty <>0
                                           union
                                           select * from stock s where company_id = {companyID} 
and item_id in (select item_id from info_item_prop where company_id = {companyID} and son_mum_item = {itemId})
and batch_id <>0 and stock_qty <>0";
                stockByItem = await CDbDealer.GetRecordsFromSQLAsync(stockByItemSql, cmd);
                if (stockByItem.Count != 0)
                {
                    tran.Rollback();
                    return new JsonResult(new { result = "ERROR", msg = "修改失败，存在严格产期/批次库存，建议使用盘点单先清除" });
                }
                else
                {
                    //清除stock里的batch_id <> 0 
                    cmd.CommandText = $@"delete from stock where company_id={companyID} and item_id={itemId} and  batch_id <> 0;
delete from stock where company_id={companyID} and item_id in (select item_id from info_item_prop where company_id = {companyID} and son_mum_item = {itemId}) and  batch_id <> 0;";
                    await cmd.ExecuteNonQueryAsync();
                }
            }
            else if (batchLevel == "1")
            {
                List<ExpandoObject> stockByItem = null;
                string stockByItemSql = $@"select stock.*,batch_no,produce_date from stock 
                                         left join info_item_batch itb on itb.batch_id = stock.batch_id and itb.company_id = stock.company_id
                                         where stock.company_id = {companyID} and item_id ={itemId} and stock.batch_id <> 0 and batch_no <> '' and stock_qty <>0
                                         union
select stock.*,batch_no,produce_date from stock 
left join info_item_batch itb on itb.batch_id = stock.batch_id and itb.company_id = stock.company_id
where stock.company_id = {companyID} and item_id in (select item_id from info_item_prop where company_id = {companyID} and son_mum_item = {itemId}) and stock.batch_id <> 0 and batch_no <> '' and stock_qty <>0
";
                stockByItem = await CDbDealer.GetRecordsFromSQLAsync(stockByItemSql, cmd);
                if (stockByItem.Count != 0)
                {
                    tran.Rollback();
                    return new JsonResult(new { result = "ERROR", msg = "修改失败，存在严格产期/批次库存，建议使用盘点单先清除" });
                }
                else
                {
                    //清除stock里的batch_no不为空的
                    cmd.CommandText = $@"delete from stock where company_id={companyID} and item_id={itemId} and  batch_id <> 0 
                                        and batch_id in (select batch_id from info_item_batch where  batch_no <> '' and company_id = {companyID});
delete from stock where company_id={companyID} and item_id in (select item_id from info_item_prop where company_id = {companyID} and son_mum_item = {itemId}) and  batch_id <> 0 
and batch_id in (select batch_id from info_item_batch where  batch_no <> '' and company_id = {companyID})";
                    await cmd.ExecuteNonQueryAsync();
                }
            }
            tran.Commit();
            return Json(new { result="OK", msg="" });
        }
        /// <summary>

        /// 分类
        /// </summary>
        /// <param name="operKey"></param>
        /// <returns></returns>
        /// 
        [HttpGet]
        public async Task<JsonResult> GetItemClass_old(string operKey, string brandIDs, string branchID, bool bGetClassStock)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $"";
            if (brandIDs != null && brandIDs != "")
            {
                condi += $" and (brand_id is null OR brand_id in ({brandIDs})) ";
            }
            //var sql = @$"select class_id as id,class_name as name,mother_id,brand_id from info_item_class where company_id = {company_id};";
            SQLQueue QQ = new SQLQueue(cmd);

            var sql = @$"
select class_id as id,class_name as name,mother_id from info_item_class where company_id = {companyID}  and (cls_status is null or cls_status = '1') {condi} order by order_index,class_name,class_id;";
            //var list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            QQ.Enqueue("class", sql);


            if (bGetClassStock && branchID != null)
            {
                sql = $@"
select distinct item_class class_id from stock
left join info_item_prop ip on stock.company_id = ip.company_id and stock.item_id = ip.item_id
where stock.company_id = {companyID} and stock.branch_id={branchID} and item_class is not null and (status= '1' or status is null) and stock_qty> 0;";
                QQ.Enqueue("hasStockClass", sql);
            }
            List<Tree> list = null;
            List<ExpandoObject> hasStockClass = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tb = QQ.Dequeue();
                if (tb == "class")
                {
                    list = CDbDealer.GetRecordsFromDr<Tree>(dr, false);
                }
                else if (tb == "hasStockClass")
                {
                    hasStockClass = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            if (hasStockClass != null)
            {
                foreach (dynamic cls in hasStockClass)
                {
                    Tree t = list.Find(row => cls.class_id == row.Id.ToString());
                    if (t != null)
                    {
                        t.hasStock = true;
                        for (int i = 0; i < 4; i++)
                        {
                            t = list.Find(row => t.Mother_Id == row.Id);
                            if (t == null) break;
                            t.hasStock = true;
                        }
                    }
                }
            }

            //var arr = JsonConvert.DeserializeObject<List<Tree>>(JsonConvert.SerializeObject(list));
            var data = CommonTool.ToTree(list);

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, hasStockClass });
        }        /*
        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string item_id = data.item_id;
            CDbDealer db = new CDbDealer();
            db.AddFields(data, "item_name,");
            db.AddField("company_id", companyID);
            string sql;
            if (item_id == "")
            {
                sql=db.GetInsertSQL("info_item_prop") + " returning item_id";
            }
            else
            {
                sql=db.GetUpdateSQL("info_item_prop",$"item_id='{item_id}'");
            }
            cmd.CommandText = sql;
            object ov=await cmd.ExecuteScalarAsync();
            string new_id = "";
            if(ov!=DBNull.Value && ov != null)
            {
                new_id = ov.ToString();
            }
            string result = "OK";
            return Json(new { result,msg = "", new_id });
        }
        */
        [HttpGet]
        public async Task<JsonResult> GetItemClass(string operKey, string brandIDs, string branchID, bool bGetClassStock)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $"";
            if (brandIDs != null && brandIDs != "")
            {
                condi += $" and (ib.brand_id is null OR ib.brand_id in ({brandIDs})) ";
            }
            //var sql = @$"select class_id as id,class_name as name,mother_id,brand_id from info_item_class where company_id = {company_id};";
            SQLQueue QQ = new SQLQueue(cmd);

            var sql = @$"
SELECT
	iic.class_id AS id,
	iic.class_name AS name,
	iic.mother_id,
	ib.brand_id,
	ib.brand_name 
FROM
	info_item_class iic
	LEFT JOIN info_item_brand ib ON ib.company_id = iic.company_id and iic.brand_id = ib.brand_id 
WHERE
	iic.company_id = {companyID}
	AND ( cls_status IS NULL OR cls_status = '1' ) 
    {condi}
ORDER BY
	order_index,
	class_name,
	class_id;";
            //var list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            QQ.Enqueue("class", sql);


            if (bGetClassStock && branchID != null)
            {
                sql = $@"
select distinct item_class class_id from stock
left join info_item_prop ip on stock.company_id = ip.company_id and stock.item_id = ip.item_id
where stock.company_id = {companyID} and stock.branch_id={branchID} and item_class is not null and (status= '1' or status is null) and stock_qty> 0;";
                QQ.Enqueue("hasStockClass", sql);
            }
            List<TreeGetItemClass> list = null;
            List<ExpandoObject> hasStockClass = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tb = QQ.Dequeue();
                if (tb == "class")
                {
                    list = CDbDealer.GetRecordsFromDr<TreeGetItemClass>(dr, false);
                }
                else if (tb == "hasStockClass")
                {
                    hasStockClass = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            if (hasStockClass != null)
            {
                foreach (dynamic cls in hasStockClass)
                {
                    TreeGetItemClass t = list.Find(row => cls.class_id == row.Id.ToString());
                    if (t != null)
                    {
                        t.hasStock = true;
                        for (int i = 0; i < 4; i++)
                        {
                            t = list.Find(row => t.Mother_Id == row.Id);
                            if (t == null) break;
                            t.hasStock = true;
                        }
                    }
                }
            }

            //var arr = JsonConvert.DeserializeObject<List<Tree>>(JsonConvert.SerializeObject(list));
            var data = CommonTool.ToTreeGetItemClass(list);

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, hasStockClass });
        }
        [HttpGet]
        public async Task<JsonResult> GetItemClassBrand(string operKey, string brandIDs, string branchID, bool bGetClassStock)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $"";
            if (brandIDs != null && brandIDs != "")
            {
                condi += $" and (ib.brand_id is null OR ib.brand_id in ({brandIDs})) ";
            }
            //var sql = @$"select class_id as id,class_name as name,mother_id,brand_id from info_item_class where company_id = {company_id};";
            SQLQueue QQ = new SQLQueue(cmd);

            var sql = @$"
SELECT
	iic.class_id AS id,
	iic.class_name AS name,
	iic.mother_id,
	ib.brand_id,
	ib.brand_name 
FROM
	info_item_class iic
	LEFT JOIN info_item_brand ib ON ib.company_id = iic.company_id and iic.brand_id = ib.brand_id 
WHERE
	iic.company_id = {companyID}
	AND ( cls_status IS NULL OR cls_status = '1' ) 
    {condi}
ORDER BY
	order_index,
	class_name,
	class_id;";
            //var list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            QQ.Enqueue("class", sql);


            if (bGetClassStock && branchID != null)
            {
                sql = $@"
select distinct item_class class_id from stock
left join info_item_prop ip on stock.company_id = ip.company_id and stock.item_id = ip.item_id
where stock.company_id = {companyID} and stock.branch_id={branchID} and item_class is not null and (status= '1' or status is null) and stock_qty> 0;";
                QQ.Enqueue("hasStockClass", sql);
            }
            List<TreeGetItemClass> list = null;
            List<ExpandoObject> hasStockClass = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tb = QQ.Dequeue();
                if (tb == "class")
                {
                    list = CDbDealer.GetRecordsFromDr<TreeGetItemClass>(dr, false);
                }
                else if (tb == "hasStockClass")
                {
                    hasStockClass = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            if (hasStockClass != null)
            {
                foreach (dynamic cls in hasStockClass)
                {
                    TreeGetItemClass t = list.Find(row => cls.class_id == row.Id.ToString());
                    if (t != null)
                    {
                        t.hasStock = true;
                        for (int i = 0; i < 4; i++)
                        {
                            t = list.Find(row => t.Mother_Id == row.Id);
                            if (t == null) break;
                            t.hasStock = true;
                        }
                    }
                }
            }

            //var arr = JsonConvert.DeserializeObject<List<Tree>>(JsonConvert.SerializeObject(list));
            var data = CommonTool.ToTreeGetItemClass(list);

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, hasStockClass });
        }
        public bool imageUrlIsValid(string img)
        {
            if (img.IsInvalid())
            {
                return false;
            }
            dynamic imgObj = JsonConvert.DeserializeObject(img);
            return imgObj!=null && imgObj.main != "" && imgObj.main != null;
        }
        public bool CheckItemWorldInfoIsComplete(dynamic itemWorldInfo)
        {
            string sbarcode = itemWorldInfo.sbarcode;
            string bbarcode = itemWorldInfo.bbarcode;
            string mbarcode = itemWorldInfo.mbarcode;
            string sunit = itemWorldInfo.sunit;
            string munit = itemWorldInfo.munit;
            string bunit = itemWorldInfo.bunit;
            string mfactor = itemWorldInfo.mfactor;
            string bfactor = itemWorldInfo.bfactor;
            string sfactor = itemWorldInfo.sfactor;
            string item_name = itemWorldInfo.item_name;
            return sbarcode.IsValid()
                && mbarcode.IsValid()
                && bbarcode.IsValid()
                && sunit.IsValid()
                && munit.IsValid()
                && bunit.IsValid()
                && mfactor.IsValid()
                && bfactor.IsValid()
                && sfactor.IsValid()
                && item_name.IsValid()
                && imageUrlIsValid(itemWorldInfo.item_images);
        }
        public ItemWorldModel FillNetWorkItemInfoToWorldItemData(dynamic itemWorldInfoDto, dynamic itemInfo)
        {
            ItemWorldModel itemWorldInfo = itemWorldInfoDto as ItemWorldModel;
            if (itemWorldInfo == null)
                itemWorldInfo = new ItemWorldModel();
            string goodsName = itemInfo?.goodsName.ToString();
            string img = itemInfo.img.ToString();
            var other = new string[] { };
            dynamic data  = new
            {
                main = "",
                tiny = "",
                other
            }; 
            if (itemWorldInfo.item_name == null && goodsName.IsValid())
                itemWorldInfo.item_name = itemInfo.goodsName;

            if (!imageUrlIsValid( itemWorldInfo.item_images)  && img.IsValid())
            {
                data = new
                {
                    main = img,
                    tiny = img,
                    other
                };
            }
            itemWorldInfo.item_images = JsonConvert.SerializeObject(data);

            return itemWorldInfo;
        }
        public dynamic FillSupcustItemToWorldItemData(dynamic itemWorldInfo, dynamic itemInfo)
        {

            var world_b_barcode = (String)itemWorldInfo.bbarcode;
            var item_b_barcode = (String)itemInfo.bbarcode;
            if (world_b_barcode.IsInvalid() && item_b_barcode.IsValid())
                itemWorldInfo.bbarcode = itemInfo.bbarcode;

            var world_m_barcode = (String)itemWorldInfo.mbarcode;
            var item_m_barcode = (String)itemInfo.mbarcode;
            if (world_m_barcode.IsInvalid() && item_m_barcode.IsValid())
                itemWorldInfo.mbarcode = itemInfo.mbarcode;

            var world_s_barcode = (String)itemWorldInfo.sbarcode;
            var item_s_barcode = (String)itemInfo.sbarcode;
            if (world_s_barcode.IsInvalid() && item_s_barcode.IsValid())
                itemWorldInfo.sbarcode = itemInfo.sbarcode;

            var world_b_factor = (String)itemWorldInfo.bfactor;
            var item_b_factor = (String)itemInfo.bfactor;
            if (world_b_factor.IsInvalid() && item_b_factor.IsValid())
                itemWorldInfo.bfactor = itemInfo.bfactor;

            var world_m_factor = (String)itemWorldInfo.mfactor;
            var item_m_factor = (String)itemInfo.mfactor;
            if (world_m_factor.IsInvalid() && item_m_factor.IsValid())
                itemWorldInfo.mfactor = itemInfo.mfactor;


            var world_s_factor = (String)itemWorldInfo.sfactor;
            var item_s_factor = (String)itemInfo.sfactor;
            if (world_s_factor.IsInvalid() && item_s_factor.IsValid())
                itemWorldInfo.sfactor = itemInfo.sfactor;


            var world_b_unit = (String)itemWorldInfo.bunit;
            var item_b_unit = (String)itemInfo.bunit;
            if (world_b_unit.IsInvalid() && item_b_unit.IsValid())
                itemWorldInfo.bunit = itemInfo.bunit;

            var world_m_unit = (String)itemWorldInfo.munit;
            var item_m_unit = (String)itemInfo.munit;
            if (world_m_unit.IsInvalid() && item_m_unit.IsValid())
                itemWorldInfo.munit = itemInfo.munit;

            var world_s_unit = (String)itemWorldInfo.sunit;
            var item_s_unit = (String)itemInfo.sunit;
            if (world_s_unit.IsInvalid() && item_s_unit.IsValid())
                itemWorldInfo.sunit = itemInfo.sunit;

            var world_item_name = (String)itemWorldInfo.item_name;
            var item_item_name = (String)itemInfo.item_name;
            if (world_item_name.IsInvalid() && item_item_name.IsValid())
                itemWorldInfo.item_name = item_item_name;

            var world_item_images = (String)itemWorldInfo.item_images;
            var item_item_images = (String)itemInfo.item_images;
            if (!imageUrlIsValid(world_item_images) && imageUrlIsValid(item_item_images))
                itemWorldInfo.item_images = item_item_images;
                
            return itemWorldInfo;
        }
        public string GetUpdateItemWorldSQL(dynamic itemWorldInfo)
        {
            var factorSQL = @$"";
            string sfactor = (string)itemWorldInfo.sfactor;
            if (sfactor.IsValid())
                factorSQL += $@"sfactor = {sfactor},";

            string mfactor = (string)itemWorldInfo.mfactor;
            if (mfactor.IsValid())
                factorSQL += $@"mfactor = {mfactor},";

            string bfactor = (string)itemWorldInfo.bfactor;
            if (bfactor.IsValid())
                factorSQL += $@"bfactor = {bfactor},";

            string item_images = itemWorldInfo.item_images;

            return $@"
                         UPDATE info_item_world SET sbarcode = '{itemWorldInfo.sbarcode}',bbarcode = '{itemWorldInfo.bbarcode}',mbarcode = '{itemWorldInfo.mbarcode}',
                                                    sunit = '{itemWorldInfo.sunit}',bunit = '{itemWorldInfo.bunit}',munit = '{itemWorldInfo.munit}',
                                                    {factorSQL}
                                                    item_name = '{itemWorldInfo.item_name}'  WHERE id = '{itemWorldInfo.id}';";
        }
        public string GetInsertItemWorldSQL(dynamic itemWorldInfo)
        {
            string sfactor = (string)itemWorldInfo.sfactor;
            List<string> list = new List<string>();
            if (sfactor.IsValid())
                //insertFactorValueSQL += $@"'{sfactor}'";
                list.Add($@"'{sfactor}'");
            else
                list.Add($@"null");
            string mfactor = (string)itemWorldInfo.mfactor;
            if (mfactor.IsValid())
                list.Add($@"'{mfactor}'");
            else
                list.Add($@"null");
            string bfactor = (string)itemWorldInfo.bfactor;
            if (bfactor.IsValid())
                list.Add($@"'{bfactor}'");
            else
                list.Add($@"null");
            string factorInsertValueSQL = string.Join(",", list);

            return $@"
                    INSERT INTO info_item_world(sbarcode,bbarcode,mbarcode,
                                                sfactor,bfactor,mfactor,
                                                sunit,bunit,munit,
                                                item_name)VALUES
                                                (
                                                '{itemWorldInfo.sbarcode}','{itemWorldInfo.bbarcode}','{itemWorldInfo.mbarcode}',
                                                {factorInsertValueSQL},
                                                '{itemWorldInfo.sunit}','{itemWorldInfo.bunit}','{itemWorldInfo.munit}',
                                                '{itemWorldInfo.item_name}'
                                                 );
                ";
        }

        [HttpGet]
        public async Task<JsonResult> GetItemInfoByBarCode(string operKey, string barCode)
        {

            return await GetItemInfoByBarCodeServ(barCode);
        }
        [HttpGet]
        public async Task<JsonResult> GetPicturesByItemWorld(string operKey, string itemName,string barCode)
        {
            string sql = $@"SELECT  word_similarity(item_name, '{itemName}') similarity,item_name,item_images FROM info_item_world
                WHERE word_similarity(item_name, '{itemName}') >0.1 and item_images::json->> 'main' <>'' ORDER BY word_similarity(item_name, '{itemName}')  desc";
            dynamic pics = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return Json(new { result = "OK", msg = "",data=pics });

        }
        [HttpPost]
        public JsonResult GetBatchImportPictureProgress([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            if (!importPictureProgress.ContainsKey(companyID + "_" + operID))
            {
                return Json(new ProgressValueModel { processedCount=0,total = 0});
            }
            return Json(importPictureProgress[companyID + "_" + operID]);

        }
        //批量导入图片
        [HttpPost]
        public async Task<JsonResult> BatchImportPicture([FromBody] dynamic data)
        {
            string eName = "";
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            string rowIDs = string.Join(",", data.rows);
            string sql = $@"SELECT info_item_prop.item_id,info_item_multi_unit.barcode from info_item_prop
            LEFT JOIN info_item_multi_unit on info_item_multi_unit.item_id = info_item_prop.item_id and info_item_multi_unit.unit_type ='s'
            where info_item_prop.item_id in ({rowIDs}) and info_item_prop.company_id = {companyID}";
            dynamic itemInfos = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            int index = 0;
            int errorCount = 0;
            Dictionary<string, string> picDic = new Dictionary<string, string>();
            var progressModel = new ProgressValueModel();
            progressModel.processedCount = 0;
            progressModel.total = itemInfos.Count;
            importPictureProgress.Add(companyID + "_" + operID, progressModel);
         
                foreach (var itemInfo in itemInfos)
                {

                    progressModel.processedCount = index;
                    importPictureProgress[companyID + "_" + operID] = progressModel;
                    index++;
                    if (index == progressModel.total)
                    {
                        importPictureProgress.Remove(companyID + "_" + operID);
                    }
                    eName = itemInfo.barcode;
                    dynamic res = await GetItemInfoByBarCodeServ(itemInfo.barcode);
                    if (res==null || res.Value==null || res.Value.result == "Error")
                    {
                        errorCount++;
                        continue;
                    }


                    var itemImagesObj = Newtonsoft.Json.JsonConvert.DeserializeObject(res.Value.productInfo.item_images);
                    string mainImageUrl = itemImagesObj?.main;
                    if (mainImageUrl.IsValid())
                    {
                        string base64 = toBase64(mainImageUrl);
                        if (base64.IsValid())
                        {
                            string item_images = await process_item_image_return_dbstring(base64, companyID);
                            picDic[itemInfo.item_id] = item_images;
                        }

                    }
                }
                List<string> updateSQLList = new List<string>();
                foreach (var item_id in picDic.Keys)
                {
                    updateSQLList.Add(@$"UPDATE info_item_prop SET item_images = '{picDic[item_id]}' WHERE item_id ={item_id} AND company_id = {companyID}");
                }
                if (updateSQLList.Count > 0)
                {
                    cmd.CommandText = string.Join(";", updateSQLList);
                    await cmd.ExecuteReaderAsync();
                }
                //AppApi/InfoItem/GetItemInfoByBarCode
                return Json(new { result = "OK", msg = @$"总计导入{index}条，失败{errorCount}条，失败原因：图片库中未录入" });
       
        }
        public async Task<string> process_item_image_return_dbstring(string picture_base64, string companyID)
        {
            var data = new HuaWeiObsController.HuaWeiObs.ItemImages();
            data.main = picture_base64;
            data.other = new List<string>();
            //   HuaWeiObsController.HuaWeiObs.ItemImages ii = JsonConvert.DeserializeObject<HuaWeiObsController.HuaWeiObs.ItemImages>(data);
            string dir = $"item-images/company_{companyID}/";
            var id = $"newIn{DateTime.Now:yyyy-MM-dd_HH-mm-ss-ffff}";
            string ite = $"item_{id}_";
            data.path = dir + ite;
            var item_images = await HuaWeiObsController.HuaWeiObs.WebItemImageSave(_httpClientFactory, data);
            return item_images;
        }
        public async Task<JsonResult> GetItemInfoByBarCodeServ(string barCode)
        {
            dynamic productInfo = null;
            string source = "";
            if (string.IsNullOrEmpty(barCode) || barCode.Length < 10)
            {
                return Json(new { result = "Error", msg = "条码长度必须大于10位" });
            }
            //Security.GetInfoFromOperKey(operKey, out string companyID);
            //先从 item_world里面取
            string item_world_sql = $@"
                        SELECT id,sbarcode,mbarcode,bbarcode,sunit,munit,bunit,bfactor,mfactor,sfactor,item_name,item_images as item_images,update_time,image_judge
                        from info_item_world iiw 
                        where iiw.sbarcode = '{barCode}' or iiw.bbarcode = '{barCode}' or  iiw.mbarcode = '{barCode}'";
            dynamic itemWorldInfo = await CDbDealer.Get1RecordFromSQLAsync(item_world_sql, cmd);

            if (itemWorldInfo == null)
            {
                itemWorldInfo = new ItemWorldModel();
            }
            if (this.CheckItemWorldInfoIsComplete(itemWorldInfo))
            {
                source = "self";
                productInfo = itemWorldInfo;
                return Json(new { source,result = "OK", productInfo }); ;
            }
            string count_unit_type_sql = $@"
                        SELECT item_id,count(imu.item_id) as unittype_count,imu.company_id
                        from info_item_multi_unit imu 
                        where imu.barcode = '{barCode}'  GROUP BY imu.item_id,imu.company_id ORDER BY unittype_count desc limit 1";
            dynamic count_data = await CDbDealer.Get1RecordFromSQLAsync(count_unit_type_sql, cmd);

            if (count_data != null)
            {
                string info_indb_sql = $@"
SELECT ip.item_name,ip.py_str,ip.item_no,ip.valid_days,ip.item_spec,ip.item_images,ib.brand_name,
       (t.b->>'f1') as bunit,(t.b->>'f2') as bfactor,(t.b->>'f3') as bbarcode,(t.b->>'f4') as bpprice, (t.b->>'f5') as blprice, (t.b->>'f6') as bbprice,
       (t.m->>'f1') as munit,(t.m->>'f2') as mfactor,(t.m->>'f3') as mbarcode,(t.m->>'f4') as mpprice, (t.m->>'f5') as mlprice, (t.m->>'f6') as mbprice, 
       (t.s->>'f1') as sunit,(t.s->>'f2') as sfactor,(t.s->>'f3') as sbarcode,(t.s->>'f4') as spprice, (t.s->>'f5') as slprice, (t.s->>'f6') as sbprice
      
FROM info_item_prop as ip
LEFT JOIN
(
    SELECT item_id,s,m,b FROM
    crosstab
    (
        'select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,wholesale_price,buy_price,retail_price)) as json 
                FROM info_item_multi_unit where company_id = {count_data.company_id} order by item_id
        ',$$values ('s'::text),('m'::text),('b'::text)$$
    )
    as errr(item_id int, s jsonb,m jsonb, b jsonb)
) t on ip.item_id=t.item_id
LEFT JOIN info_item_brand as ib on ip.company_id=ib.company_id and ib.brand_id = ip.item_brand
WHERE ip.item_id='{count_data.item_id}' and ip.company_id = '{count_data.company_id}'";
                productInfo = await CDbDealer.Get1RecordFromSQLAsync(info_indb_sql, cmd);
            }

            if (productInfo != null && productInfo.sbarcode !="")
            {
                try
                {
                    itemWorldInfo = FillSupcustItemToWorldItemData(itemWorldInfo, productInfo);
                    if (itemWorldInfo.id != null)
                    {
                        item_world_sql = GetUpdateItemWorldSQL(itemWorldInfo);
                    }
                    else
                    {
                        item_world_sql = GetInsertItemWorldSQL(itemWorldInfo);

                    }
                    cmd.CommandText = item_world_sql;
                    await cmd.ExecuteNonQueryAsync();
                }catch(Exception e)
                {
                    Console.WriteLine(productInfo.sbarcode);
                }

            }
            //7258
            if (itemWorldInfo.update_time == "" || itemWorldInfo.update_time == null)
            {
                itemWorldInfo.update_time = "2000-01-01 00:00";
            }
            bool itemWorldIsComplete = CheckItemWorldInfoIsComplete(itemWorldInfo);
            bool lastUpdateMore30Days = DateTime.Compare(DateTime.Now.AddDays(-30), DateTime.Parse(itemWorldInfo.update_time)) > 0;
            //&& (itemWorldIsComplete || lastUpdateMore30Days)
            if (itemWorldInfo != null && (itemWorldIsComplete || !lastUpdateMore30Days))
            {
                source = "self";
                productInfo = itemWorldInfo;
                return Json(new { result = "OK", source, productInfo }); ;
            }


            //string standard_sql = @$"SELECT barcode,product_name,item_name,produce_district,unit,size from info_item_standard where barcode='{barCode}' limit 1";
            dynamic res = CommonTool.GetItemInfoFromNetwork(barCode);
            if (res!=null )
            {
                var hasError = res.status == 201 || res.status == 202;
                if (hasError)
                {
                    productInfo = new ItemWorldModel();
                    productInfo.sbarcode = barCode;
                    return Json(new { result = "Error", msg = "未匹配到商品", productInfo });
                }
                itemWorldInfo = FillNetWorkItemInfoToWorldItemData(itemWorldInfo, res.result);
            }
            itemWorldInfo.item_name = itemWorldInfo?.item_name?.Replace("'", "");
            string item_images = itemWorldInfo.item_images;
            if (!imageUrlIsValid(item_images))
            {
                var other = new string[] { };
                var emptyImages = new
                {
                    main = "",
                    tiny = "",
                    other
                };
                itemWorldInfo.item_images = JsonConvert.SerializeObject(emptyImages);
            }
            item_world_sql = $@"
            INSERT INTO info_item_world (sbarcode,item_name, item_images,update_time)
            VALUES
            (
                '{barCode}',
                '{itemWorldInfo.item_name}',
                '{itemWorldInfo.item_images}',
                '{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}'
            ) 
            ON CONFLICT (sbarcode) 
            DO
                UPDATE
                SET item_name = '{itemWorldInfo.item_name}', item_images = '{itemWorldInfo.item_images}',update_time = '{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}'

            ";
            cmd.CommandText = item_world_sql;
            await cmd.ExecuteNonQueryAsync();
            productInfo = itemWorldInfo;
            return Json(new { result = "OK", msg = "", source = "self", productInfo });

        }

        public string toBase64(string url)
        {
            try
            {
                WebClient mywebclient = new WebClient();
                byte[] Bytes = mywebclient.DownloadData(url);
                using (MemoryStream ms = new MemoryStream(Bytes))
                {
                    Image outputImg = Image.FromStream(ms);
                    Bitmap bmp = new Bitmap(outputImg);
                    bmp.Save(ms, ImageFormat.Jpeg);
                    byte[] arr = new byte[ms.Length];
                    ms.Position = 0;
                    ms.Read(arr, 0, (int)ms.Length);
                    ms.Close();
                    String strbaser64 = Convert.ToBase64String(arr);
                    return strbaser64;
                }
            }
            catch (Exception e)
            {
                return "";
            }


        }


        /// <summary>
        /// 商品档案 （根据助记码，条码，编号,商品名，模糊查询）
        /// 显示：商品名，最小单位，单价
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="searchStr"></param>     
        /// /// <param name="sortFld1"></param>
        /// <param name="sortFld2"></param>
        /// <param name="sortFld3"></param>
        /// <param name="brandID"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetItemList(string operKey, string searchStr, string brandID, string classID, int pageSize, int startRow, string status, string sortFld1, string sortFld2, string sortFld3, bool queryByBrandId)

        {
            bool firstRequest = false;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $"";
            if (status == "0")
            {
                condi = $" and (status = '0') ";
            } 
            else if (status == "1")
            {
                condi = $" and (status = '1' or status is null) ";
            }
            if (searchStr.IsValid())
            {
                string b = "%";
                if (searchStr.Length >= 6) b = "";
                string flexStr = CPubVars.GetFlexLikeStr(searchStr);
                condi += $" and (ip.item_name ilike '%{flexStr}%' or ip.py_str ilike '%{searchStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%' or s_barcode like '%{searchStr}{b}' or b_barcode like '%{searchStr}{b}' or m_barcode like '%{searchStr}{b}' or ip.mum_attributes::text ilike '%{searchStr}%' or ip.avail_attr_combine::text ilike '%{searchStr}%')";
            }
            //if (brandID != null) condi += $" and ip.item_brand = '{brandID}' ";
            if (brandID != null)
            {
                if (queryByBrandId)
                {
                    condi += $" and ip.item_brand = '{brandID}'";
                }
                else {
                    condi += $"and (ip.item_brand is null OR ip.item_brand in ({brandID})) ";
                }
            }
            //if (classID != null) condi += $" and ip.other_class like '%/{classID}/%' ";
            if (classID != null && classID != "0" && classID != "-1") condi += $"and ip.other_class like '%/{classID}/%' ";
            Dictionary<string, string> sortValues = new Dictionary<string, string>
            {
                { "item_name", "ip.py_str asc" },
                { "order_index", "ip.item_order_index asc" },
                { "recent_create", "ip.item_id desc" },
                { "stock", "case when stock.stock_qty > 0 then 0 else 1 end" },
                { "no_stock", "case when stock.stock_qty <= 0 then 0 else 1 end" },
                { "more_stock", "stock.stock_qty desc" },
                { "less_stock", "stock.stock_qty asc" },
                { "none", "" }
            };

            List<string> sortFields = new List<string>();

            if (sortFld1.IsValid() && sortValues.TryGetValue(sortFld1, out string firstSortField) && !string.IsNullOrWhiteSpace(firstSortField))
            {
                sortFields.Add(firstSortField);
            }
            if (sortFld2.IsValid() && sortValues.TryGetValue(sortFld2, out string secondSortField) && !string.IsNullOrWhiteSpace(secondSortField))
            {
                sortFields.Add(secondSortField);
            }
            if (sortFld3.IsValid() && sortValues.TryGetValue(sortFld3, out string thirdSortField) && !string.IsNullOrWhiteSpace(thirdSortField))
            {
                sortFields.Add(thirdSortField);
            }

            // 默认排序逻辑
            if (sortFields.Count == 0)
            {
                sortFields.Add("ip.item_order_index");
                sortFields.Add("ip.item_id desc");
            }

            string sortSQL = "ORDER BY " + string.Join(", ", sortFields);

            if (startRow == 0) firstRequest = true;
            SQLQueue QQ = new SQLQueue(cmd);
            var sql_noLimit = @$"
SELECT ip.item_id,ip.item_name,unit_conv,coalesce(bmu.unit_no,smu.unit_no) unit_no,coalesce(bmu.wholesale_price,smu.wholesale_price) wholesale_price,ip.item_images,status, s_unit_no,s_unit_factor,s_wholesale_price,m_unit_no,m_unit_factor,m_wholesale_price,b_unit_no,b_unit_factor,b_wholesale_price,s_barcode,m_barcode, b_barcode
FROM info_item_prop as ip
LEFT JOIN 
(
    select item_id,unit_no,wholesale_price,barcode from info_item_multi_unit WHERE company_id = {companyID} and unit_type = 's'
)  smu on ip.item_id=smu.item_id 
LEFT JOIN 
(
   select item_id,unit_no,wholesale_price,barcode from info_item_multi_unit WHERE company_id = {companyID} and unit_type = 'b'
) bmu on ip.item_id=bmu.item_id
LEFT JOIN 
(
   select item_id,unit_no,wholesale_price,barcode from info_item_multi_unit WHERE company_id = {companyID} and unit_type = 'm'
) mmu on ip.item_id=mmu.item_id
LEFT JOIN info_item_brand as ib on ip.company_id=ib.company_id and ip.item_brand = ib.brand_id 
left join 
(
        select item_id,
            s_unit_no,s_unit_factor,s_wholesale_price,s_retail_price,s_buy_price,s_barcode,
            m_unit_no,m_unit_factor,m_wholesale_price,m_retail_price,m_buy_price,m_barcode,
            b_unit_no,b_unit_factor,b_wholesale_price,b_retail_price,b_buy_price,b_barcode,
            (
                case when b_unit_factor is not null and m_unit_factor is     null then concat(s_unit_factor,b_unit_no,'=',b_unit_factor,s_unit_no)  
			        when b_unit_factor is not null and m_unit_factor is not null then concat(s_unit_factor,b_unit_no,'=',
                    case  when b_unit_factor::numeric % m_unit_factor::numeric = 0 then round(b_unit_factor::numeric / m_unit_factor::numeric,0)::text
                          else round(b_unit_factor::numeric / m_unit_factor::numeric, 2)::text  end,
                    m_unit_no,'=',b_unit_factor,s_unit_no)
			        when b_unit_factor is null then concat(s_unit_factor,s_unit_no)  end
            ) as unit_conv    
        from 
        (
            select item_id,(s->>'f1')::real as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_wholesale_price,s->>'f4' as s_retail_price,s->>'f5' s_barcode,s->>'f6' as s_buy_price,
                            (m->>'f1')::real as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_wholesale_price,m->>'f4' as m_retail_price,m->>'f5' m_barcode,m->>'f6' as m_buy_price,
                            (b->>'f1')::real as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_wholesale_price,b->>'f4' as b_retail_price,b->>'f5' b_barcode,b->>'f6' as b_buy_price
            from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,retail_price,barcode,buy_price)) as json from info_item_multi_unit where company_id = {companyID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb) 
        ) t 
) d on d.item_id = ip.item_id					
WHERE ip.company_id = {companyID} and ip.son_mum_item is NULL {condi} ";
            var sql = sql_noLimit+ $" {sortSQL} limit {pageSize} offset {startRow}";
            QQ.Enqueue("data", sql);
            if (firstRequest)
            {
                sql = $"select count(*) as itemCount from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
            }
            sql = $"select * from info_attribute where company_id ={ companyID } order by order_index";
            QQ.Enqueue("attrs", sql);
            sql = $"select opt_id,opt_name,o.attr_id from info_attr_opt o left join info_attribute a on o.attr_id=a.attr_id where o.company_id={companyID} order by o.order_index;";
            QQ.Enqueue("attrOptions", sql);
            
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count"&&firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data,itemCount });
        }
        /*
        /// <summary>
        /// 品牌类别
        /// 查询条件： 品牌名
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="searchStr"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetBrandList1(string operKey, string searchStr)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $"where company_id = {companyID}";
            if (searchStr != null) condi += $"and brand_name ilike '%{searchStr}%'";
            var sql = @$"select brand_name from info_item_brand {condi}";
            var data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

        /// <summary>
        /// 查看单位
        /// </summary>
        /// <param name="operKey"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetUnitnoList1(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $"where company_id = {companyID}";
            var sql = @$"select unit_no from info_avail_unit {condi} order by order_index";
            var data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }
        */
        [HttpGet]
        public async Task<JsonResult> GetUnitnoList(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $"where company_id = {companyID}";
            var sql = @$"select unit_no from info_avail_unit {condi} order by order_index";
            var data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

        /// <summary>
        /// 查看商品详细档案 
        /// 包含：照片，基本信息，小中大单位
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="itemID">bUnit大单位单位名，bFactor 大单位包装率，bpprice 大单位批发价，bbprice大单位进价，bbarcode大单位条码</param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetItemInfoDetail(string operKey, string itemID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string checkSql = @$"select mum_attributes,avail_attr_combine from info_item_prop ip where ip.company_id = {companyID} And ip.item_id = {itemID}";
            dynamic checkData = await CDbDealer.Get1RecordFromSQLAsync(checkSql, cmd);
            // 处理avail_attr_combine情况
            if (checkData.mum_attributes !="" && checkData.mum_attributes !="[]" && checkData.avail_attr_combine == "")
            {
                // 只针对一维商品处理
                await MallMiniItemInfoService.UpdateAvailAttrCombin(cmd, companyID, itemID);
            }
            var condi = $"where ip.company_id = {companyID} And ip.item_id = {itemID}";
            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
SELECT ip.mall_units,ip.item_name,ip.py_str,ip.item_no,ip.valid_days,ip.valid_day_type,ip.item_spec,ip.item_provenance,ip.location,ip.item_class,ic.class_name,ip.other_class,ip.item_brand,ib.brand_name,ip.supplier_id,sup.sup_name supplier_name,(t.b->>'f2') as bUnit,(t.m->>'f2') as mUnit,(t.s->>'f2') as sUnit,(t.b->>'f1') as bFactor,(t.m->>'f1') as mFactor,(t.s->>'f1') as sFactor,
       ip.son_options_id,ip.avail_attr_combine,ip.mall_units_show_price,
       cost_price_avg,
       (t.s->>'f3') as spPrice,(t.m->>'f3') as mpPrice,(t.b->>'f3') as bpPrice,(t.s->>'f4') as sbPrice,(t.m->>'f4') as mbPrice,(t.b->>'f4') as bbPrice,(t.s->>'f5') as slPrice,(t.m->>'f5') as mlPrice,(t.b->>'f5') as blPrice,
       (t.s->>'f6') as sbarcode,(t.m->>'f6') as mbarcode,(t.b->>'f6') as bbarcode,photo1,photo2, item_images, ip.mum_attributes,ip.batch_level, ip.status,
       (t.s->>'f7') as scPrice,(t.m->>'f7') as mcPrice,(t.b->>'f7') as bcPrice,(t.s->>'f8') as slowPrice,(t.m->>'f8') as mlowPrice,(t.b->>'f8') as blowPrice,
       (t.s ->> 'f9') as sWeight,(t.m ->> 'f9') as mWeight,(t.b ->> 'f9') as bWeight,
       (t.s ->> 'f10') as mall_min_qty_s,(t.m ->> 'f10') as mall_min_qty_m,(t.b ->> 'f10') as mall_min_qty_b, 
       (t.s ->> 'f11') as mall_max_qty_s,(t.m ->> 'f11') as mall_max_qty_m,(t.b ->> 'f11') as mall_max_qty_b, 
       ip.approve_status,CASE WHEN (t.b->>'f1') IS NOT NULL THEN 1 ELSE 0 END +CASE WHEN (t.m->>'f1') IS NOT NULL THEN 1 ELSE 0 END+CASE WHEN (t.s->>'f1') IS NOT NULL THEN 1 ELSE 0 END as unitRows
from info_item_prop as ip
left join 
(
    select item_id,s,m,b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,buy_price,retail_price,barcode,contract_price,lowest_price,weight,mall_min_qty,mall_max_qty)) as json
    from info_item_multi_unit where company_id ={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$)  as errr(item_id int, s jsonb,m jsonb, b jsonb)
) t on ip.item_id=t.item_id
LEFT JOIN (select * from info_item_class where company_id = {companyID} ) as ic on ic.class_id = ip.item_class
LEFT JOIN info_supcust sup on ip.supplier_id=sup.supcust_id and ip.company_id=sup.company_id
LEFT JOIN info_item_brand as ib on ib.brand_id = ip.item_brand {condi}";
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

            string query_son_items = $@" select item_id,item_name from info_item_prop where company_id={companyID} and son_mum_item={itemID}";
            var sonItems = await CDbDealer.GetRecordsFromSQLAsync(query_son_items, cmd);
            string itemsId = itemID;
            if (sonItems != null)
            {
               foreach (dynamic item in sonItems)
                {
                    itemsId += "," + item.item_id;
                }
            }

            sql = $@"
select item_id from stock where item_id = {itemID} and company_id={companyID} 
union
select item_id from items_ordered_balance where item_id = {itemID} and company_id={companyID} 
union
select item_id from sheet_sale_order_detail where item_id = {itemID} and company_id={companyID} limit 1 
";

            sql = $@"
select item_id from stock where item_id in ( {itemsId}) and company_id={companyID} and stock_qty <>0
union
select item_id from items_ordered_balance where item_id in ( {itemsId}) and company_id={companyID} 
union
select item_id from sheet_move_detail where item_id in ( {itemsId}) and company_id={companyID} 
union
select item_id from sheet_buy_detail where item_id in ( {itemsId}) and company_id={companyID} 
union
select item_id from sheet_sale_detail where item_id in ( {itemsId}) and company_id={companyID}
union
select item_id from sheet_inventory_detail where item_id in ( {itemsId}) and company_id={companyID} 
union
select item_id from sheet_invent_change_detail where item_id in ( {itemsId}) and company_id={companyID} 
union
select item_id from sheet_sale_order_detail where item_id in ( {itemsId}) and company_id={companyID}
union
select item_id from sheet_buy_order_detail where item_id in ( {itemsId}) and company_id={companyID} 
limit 1 
";

            var infoUsed = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            
            sql = $"select * from info_attribute where company_id ={companyID} order by order_index";
            var availAttributes = await CDbDealer.GetRecordsFromSQLAsync<ItemEditModel.InfoAttribute>(sql, cmd);

            sql = $"select opt_id,opt_name,o.attr_id from info_attr_opt o left join info_attribute a on o.attr_id=a.attr_id where o.company_id={companyID} order by o.order_index;";
            var options =  await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            
            // var attrOptions = new Dictionary<string, dynamic>();
            var attrOptions = options.GroupBy(opt =>((dynamic)opt).attr_id).ToDictionary(g=>(string)g.Key,g=>g.ToList()); 
            
            var itemUsed = false;
            if (infoUsed.Count > 0) itemUsed = true;
            sql = $@"SELECT * from 
(
    select plan_id pid,item_id,s_price,m_price,discount,b_price from price_plan_item where company_id={companyID} and item_id='{itemID}'
) s left join price_plan_main plan on s.pid=plan.plan_id and plan.company_id={companyID}";
            var plans = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd); 
            dynamic editLog = null;
            if (data.approve_status == "wait approve")
            {
                string logInfoSql = $@"select dcl.*,io.oper_name as edit_name from document_change_log dcl
                                left join (select oper_name,oper_id from info_operator where company_id =  {companyID} ) io on io.oper_id = dcl.oper_id
                            where company_id = {companyID} and obj_id = {itemID} order by happen_time desc limit 1;";
                editLog = await CDbDealer.Get1RecordFromSQLAsync(logInfoSql, cmd);
                // data.Add("logInfoData", logInfo);
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data ,itemUsed, availAttributes, attrOptions, plans, editLog });
        }

  

        [HttpPost]
        public async Task<JsonResult> SaveItemInfo([FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID,out string operID);
            cmd.oper_id = operID;
            string new_id = "";
            string item_id = data.item_id??"";
            bool isNewRecord = item_id == "";
            string item_name = data.item_name ?? "";
            string py_str = data.py_str ?? "";
            string item_no = data.item_no ?? "";
            string item_class = data.item_class ?? "";
            string item_brand =data.item_brand ?? "";
            //string supplier_id = data.supplier_id ?? "";
            string valid_days =data.valid_days ?? "";
            string valid_day_type = data.valid_day_type ?? "";
            string location = data.location ?? "";
            string unit_no =data.unit_no ?? "";
            string unit_factor =data.unit_factor ?? "";
            string unit_weight = data.unit_weight ?? "";
            string big_unit_no =data.big_unit_no ?? "";
            string big_unit_factor =data.big_unit_factor ?? "";
            string big_unit_weight = data.big_unit_weight ?? "";
            string medium_unit_no =data.medium_unit_no ?? "";
            string medium_unit_factor =data.medium_unit_factor ?? "";
            string medium_unit_weight = data.medium_unit_weight ?? "";
            string mall_min_qty_s = data.mall_min_qty_s ?? "";
            string mall_min_qty_m = data.mall_min_qty_m ?? "";
            string mall_min_qty_b = data.mall_min_qty_b ?? "";
            string mall_max_qty_s = data.mall_max_qty_s ?? "";
            string mall_max_qty_m = data.mall_max_qty_m ?? "";
            string mall_max_qty_b = data.mall_max_qty_b ?? "";
            string wholesale_price =data.wholesale_price ?? "";           

            string big_wholesale_price =data.big_wholesale_price ?? "";
            string medium_wholesale_price =data.medium_wholesale_price ?? "";

            // 2024.03.06 add 承包价
            string contract_price = data.contract_price ?? "";
            string big_contract_price = data.big_contract_price ?? "";
            string medium_contract_price = data.medium_contract_price ?? "";

            string lowest_price = data.lowest_price ?? "";
            string big_lowest_price = data.big_lowest_price ?? "";
            string medium_lowest_price = data.medium_lowest_price ?? "";

            string retail_price =data.retail_price ?? "";
            string big_retail_price =data.big_retail_price ?? "";
            string medium_retail_price =data.medium_retail_price ?? "";

            string buy_price =data.buy_price ?? "";
            string big_buy_price =data.big_buy_price ?? "";
            string medium_buy_price =data.medium_buy_price ?? "";

          
            string big_barcode =data.big_barcode ?? "";
            big_barcode = big_barcode.Trim();
            string barcode =data.barcode ?? "";
            barcode = barcode.Trim();
            string medium_barcode =data.medium_barcode ?? "";
            medium_barcode = medium_barcode.Trim();
            string other_class =data.other_class ?? "";
            string item_spec =data.item_spec ?? "";
            string mum_attributes =data.mum_attributes ?? "";
            string status =data.status ?? "";
            string batch_level = data.batch_level ?? "";
            string item_provenance = data.item_provenance ?? "";
            string mall_units = data.mall_units ?? "";
            string mall_units_show_price = data.mall_units_show_price ?? "";
            
            string sql;
            string query_item_id = item_id;
            if (string.IsNullOrEmpty(query_item_id)) query_item_id = "'-1'";
            ItemEditModel model = new ItemEditModel(cmd);
            dynamic origInfo = await model.GetRecord(cmd,(string)data.operKey, item_id);
            if (item_name.IndexOf('\'') > -1) 
            {
                msg = "商品名请不要包含英文单引号";
                return Json(new { result = "Error", msg });
            }
            var list = await CDbDealer.GetRecordsFromSQLAsync($"select item_id from info_item_prop where company_id = {companyID} and item_name = '{item_name}' and item_id<>{query_item_id}", cmd);
            if (list.Count() > 0)
            {
                result = "error";
                msg = "存在同名的商品";
            }
            
            if (big_unit_no!="" && big_unit_factor=="")
            {
                msg = "请输入大单位的包装率";
            }
            else if (big_unit_no == "" && big_unit_factor != "")
            {
                msg = "请输入大单位的单位名称";
            }
            if (medium_unit_no != "" && medium_unit_factor == "")
            {
                msg = "请输入中单位的包装率";
            }
            else if (medium_unit_no == "" && medium_unit_factor != "")
            {
                msg = "请输入中单位的单位名称";
            }
            else if(big_unit_no!="" && big_unit_no == medium_unit_no)
            {
                msg = "中单位和大单位名称不能相同";
            }
            else if (big_unit_no != "" && big_unit_no == unit_no)
            {
                msg = "小单位和大单位名称不能相同";
            }
            else if (medium_unit_no != "" && medium_unit_no == unit_no)
            {
                msg = "小单位和中单位名称不能相同";
            }
            else if (big_unit_factor != "" && big_unit_factor == "'1'")
            {
                msg = "大单位包装率必须大于1";
            }
            else if (medium_unit_factor != "" && medium_unit_factor == "'1'")
            {
                msg = "中单位包装率必须大于1";
            }
            else if (medium_unit_factor != "" && medium_unit_factor == big_unit_factor)
            {
                msg = "中单位和大单位包装率不能相等";
            }
            else if (medium_unit_factor != "" &&  big_unit_factor!="")
            {
                var mf = CPubVars.ToDecimal(medium_unit_factor.Replace("'",""));
                var bf = CPubVars.ToDecimal(big_unit_factor.Replace("'", ""));
                var l = bf % mf;
                if (Math.Abs(l)> CPubVars.ToDecimal(0.1))  
                   msg = "大单位包装率必须能被中单位包装率整除";
            }
            object ov = null;
            /*
            #region 停用时检查是否有库存
            if (item_id != "" && status == "0")
            {
                cmd.CommandText = @$"select s.item_id from stock s left join info_item_prop ip on s.item_id=ip.item_id and ip.company_id={companyID} where s.company_id={companyID} and (s.item_id={item_id} or ip.son_mum_item={item_id}) and abs(stock_qty)>0.01;";
                ov = await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value)
                {
                    return Json(new { result = "Error", msg = "该商品还有库存，请盘0后再停用" });
                }
            }  
            #endregion
            */
            //手机端添加类别不能选择全部，暂不加校验
            /*else if (item_class != "")
            {
                dynamic classID = await CDbDealer.Get1RecordFromSQLAsync($"select class_id from info_item_class where company_id ={companyID} and class_id = {item_class} and class_name = '全部' ", cmd);
                if (classID != null)
                    msg = "请勿将商品类别设置为'全部'";
            }*/

            if (other_class.IsValid())
            {
				cmd.CommandText = $@"
SELECT string_agg ( class_name :: TEXT, '/' ) AS class_name 
FROM
(
	SELECT class_name 
	FROM
	(
	    WITH split_table AS 
        (
            SELECT regexp_split_to_table('{other_class}', '/') AS class_id
        )
        SELECT
            row_number() OVER () AS row_num,
            class_id
        FROM
            split_table
        WHERE
            class_id <> ''
	) t 
	LEFT JOIN info_item_class ic ON ic.company_id = {companyID} AND t.class_id::numeric = ic.class_id 
	WHERE ic.mother_id<>0
) t";
				 
				object ob = await cmd.ExecuteScalarAsync();
                if (ob != null && ob != DBNull.Value)
                {
					string other_class_name = (string)ob;
                    data.other_class_name = other_class_name;
                }
			}
            string item_images = data.item_images.ToString();
            if (item_images.IsValid())
            { 
                //try
                { 
                    HuaWeiObsController.HuaWeiObs.ItemImages ii = JsonConvert.DeserializeObject<HuaWeiObsController.HuaWeiObs.ItemImages>(item_images);
                    string dir = $"item-images/company_{companyID}/";
                    var id = data.item_id == "" ? $"newIn{DateTime.Now:yyyy-MM-dd_HH-mm-ss-ffff}" : data.item_id;
                    string ite = $"item_{id}_";
                    ii.path = dir + ite;
                    item_images = await HuaWeiObsController.HuaWeiObs.WebItemImageSave(_httpClientFactory, ii);
                }
               // catch
                {
               //     msg = "保存商品图片失败";
                }
               
            }
           // item_images = item_images;

            if (msg != "")
            {
                return Json(new { result="Error", msg});
            }
          
            string getInsertUnitSQL(string item_id, string unit_type, string unit_no, string unit_factor, 
                string wholesale_price,string buy_price,string retail_price,string barcode,
                string contract_price,string lowest_price,string weight,string mall_min_qty,string mall_max_qty) {
                CDbDealer db = new CDbDealer();
                if (item_id == "") item_id = "@item_id"; 
                db.AddField("company_id", companyID);
                db.AddField_OrigValue("item_id", item_id);
                db.AddField("unit_no", unit_no);
                db.AddField("unit_factor", unit_factor);
                db.AddField("wholesale_price", wholesale_price);
                db.AddField("buy_price", buy_price);
                db.AddField("contract_price", contract_price);
                db.AddField("lowest_price", lowest_price);
                db.AddField("retail_price", retail_price);
                db.AddField("barcode", barcode);
                db.AddField("unit_type", unit_type);
                db.AddField("weight", weight);
                db.AddField("mall_min_qty", mall_min_qty);
                db.AddField("mall_max_qty", mall_max_qty);
                string s = db.GetInsertSQL("info_item_multi_unit");
                if (!s.EndsWith(";")) s += ";";
                return s; 
            }

            string sqlUnit = getInsertUnitSQL(item_id, "s", unit_no, "1", wholesale_price, buy_price, retail_price, barcode,contract_price,lowest_price, unit_weight, mall_min_qty_s,mall_max_qty_s);


            if (big_unit_factor.IsValid() && big_unit_factor != "null")
            {
                sqlUnit += getInsertUnitSQL(item_id, "b",big_unit_no, big_unit_factor, big_wholesale_price, big_buy_price, big_retail_price, big_barcode,big_contract_price,big_lowest_price, big_unit_weight, mall_min_qty_b,mall_max_qty_b);

                //sqlUnit += $@"insert into info_item_multi_unit(company_id,item_id,unit_no,unit_factor,wholesale_price,buy_price,retail_price,barcode,unit_type) values({companyID},@item_id,{big_unit_no},{big_unit_factor},{big_wholesale_price},{big_buy_price},{big_retail_price},{big_barcode},'b');";
            }
            if (medium_unit_factor.IsValid() && medium_unit_factor != "null")
            {
                sqlUnit += getInsertUnitSQL(item_id, "m", medium_unit_no, medium_unit_factor, medium_wholesale_price, medium_buy_price, medium_retail_price, medium_barcode,medium_contract_price,medium_lowest_price, medium_unit_weight, mall_min_qty_m,mall_max_qty_m);
                     
                //sqlUnit += $@"insert into info_item_multi_unit(company_id,item_id,unit_no,unit_factor,wholesale_price,buy_price,retail_price,barcode,unit_type) values({companyID},@item_id,{medium_unit_no},{medium_unit_factor},{medium_wholesale_price},{medium_buy_price},{medium_retail_price},{medium_barcode},'m');";
            }

            // 同步子口味商品重量
            dynamic sonItems = null;
            if (!((string)data.item_id).IsNullOrWhiteSpace())
            {
                sonItems = await CDbDealer.GetRecordsFromSQLAsync($"select ip.item_id,mu.unit_type from info_item_prop ip left join info_item_multi_unit mu on mu.company_id = {companyID} and mu.item_id = ip.item_id where ip.company_id = {companyID} and son_mum_item = {data.item_id}", cmd);

                foreach (var sonItem in sonItems)
                {
                    string weight = sonItem.unit_type switch{ "s" => unit_weight,"m" => medium_unit_weight,"b" => big_unit_weight,_ => null};
                    if (weight != null && !weight.IsNullOrWhiteSpace())
                    {
                        sqlUnit += @$"UPDATE info_item_multi_unit  SET weight = {weight}  WHERE company_id = {companyID} AND item_id = {sonItem.item_id} AND unit_type = '{sonItem.unit_type}';";
                    }
                }
            }

            CDbDealer db = new CDbDealer();
            db.AddField("company_id", companyID);
            db.AddField("item_images", item_images);
            db.AddField("item_provenance", item_provenance);
            
            if (mum_attributes == "[]")
            {
                data.mum_attributes = "";
            }
            db.AddFields(data, "item_name,py_str,item_no,item_class,other_class,other_class_name,item_brand,supplier_id,status,item_spec,valid_days,valid_day_type,location,mum_attributes,batch_level,approve_status,mall_units,mall_units_show_price");
			


			if (item_id.IsValid())
            {
                string sqlItem = db.GetUpdateSQL("info_item_prop", $"company_id ={companyID} and item_id = {item_id}")+";";
                sql = $@"
{sqlItem}
delete from info_item_multi_unit where company_id={companyID} and item_id = {item_id}; 
{sqlUnit}
";
               
            }
            else
            {
                string  sqlItem = db.GetInsertSQL("info_item_prop") + " returning item_id;";                    
                sql = $@"
SELECT yj_exeSqlByInsertedRowID('{sqlItem.Replace("'", "''")}','{sqlUnit.Replace("'", "''")}','@item_id')";

            }
            sql = sql.Replace("'NaN'", "null");

			cmd.ActiveDatabase = "";
			CMySbTransaction tran = cmd.Connection.BeginTransaction();

			cmd.CommandText = sql;
			ov = await cmd.ExecuteScalarAsync();
			if (ov != null && ov != DBNull.Value) item_id = ov.ToString();
			item_id = item_id.Split(",")[0];

			
 
            // 分销同步查询旧类别
            dynamic oldClass = null;
            if (!((string)data.item_id).IsNullOrWhiteSpace())
            {
                oldClass = await CDbDealer.Get1RecordFromSQLAsync($"select item_class from info_item_prop where company_id = {companyID} and item_id = {data.item_id}", cmd);
            }

            
            
            
            #region 对于多属性进行处理
            try
            {
                string son_mum_item = data.son_mum_item;    // 判断是否是子商品
                if (string.IsNullOrEmpty(son_mum_item)) //不是子商品
                {

                    if (!isNewRecord && data.attrInfo != null)//新建商品的不用管
                    {
                        dynamic rec =await CDbDealer.Get1RecordFromSQLAsync($"select son_mum_item from info_item_prop where company_id={companyID} and item_id ={item_id}", cmd);
                        if(rec != null &rec.son_mum_item!="")
                        {
							tran.Rollback();
							return Json(new { result = "Error" , msg = "该商品本身就是属性子商品" });
						}
                        //更新子商品的item_class和other_class
                        string up_item_class = item_class == "" ? "null" : item_class; //item_class虽然是必填，但是这里防一手空值
                        string updateBatchLevel = "";
                        if (!string.IsNullOrEmpty(batch_level))
                        {
                            updateBatchLevel = $@",batch_level = '{batch_level}'";
                        }

                        string exsql = $"update info_item_prop set item_class = {up_item_class},other_class = '{other_class}' {updateBatchLevel} where company_id={companyID} and son_mum_item = {item_id}";
                        cmd.CommandText = exsql;
                        await cmd.ExecuteNonQueryAsync();


						//更新子商品名称

						// 使用正则表达式提取括号中的规格（如 '(芒果)'）
						string ExtractSpec(string itemName)
						{
							var match = Regex.Match(itemName, @"\(([^)]*)\)");
							return match.Success ? match.Value : "";
						}

						// 替换主名称，保留括号中的规格
						string ReplaceMainName(string itemName, string newMainName)
						{
							// 提取括号中的规格
							string spec = ExtractSpec(itemName);
							// 移除规格部分，保留主名称
							string mainNameWithoutSpec = itemName.Replace(spec, "").Trim();
							// 替换主名称
							string updatedMainName = mainNameWithoutSpec.Replace(mainNameWithoutSpec, newMainName);
							// 最后将新主名称和原规格部分重新拼接起来
							return updatedMainName + spec;
						}
						string queryOldItemName = $"SELECT item_name FROM info_item_prop WHERE company_id = {companyID} AND item_id = {item_id}";
						cmd.CommandText = queryOldItemName;
						string querySubItems = $@"SELECT item_id, item_name FROM info_item_prop WHERE company_id={companyID} AND son_mum_item = {item_id}";
						List<ExpandoObject> subItems = await CDbDealer.GetRecordsFromSQLAsync(querySubItems, cmd);
						foreach (dynamic subItem in subItems)
						{
							string subItemName = subItem.item_name;
							string new_item_name = data.item_name?.ToString() ?? "";
							// 替换主名称，保留括号中的规格
							string updatedSubItemName = ReplaceMainName(subItemName, new_item_name);
							//Console.WriteLine("updatedSubItemName: " + updatedSubItemName);
							// 更新子商品的名称
							string updateSubItemNameSql = $"UPDATE info_item_prop SET item_name = '{updatedSubItemName}' WHERE son_mum_item = {item_id} AND item_id={subItem.item_id}";
							cmd.CommandText = updateSubItemNameSql;
							await cmd.ExecuteNonQueryAsync();
						}
					}

					dynamic attrInfo = data.attrInfo;

                    if (attrInfo != null)
                    {
						string attrSql = "";

						bool distinctStock = attrInfo.distinctStock;

						JArray itemMumAttributes = attrInfo.itemMumAttributes;
						JArray availAttrCombine = attrInfo.availAttrCombine;
						JArray deleteItemList = attrInfo.deleteItemList;

						if (itemMumAttributes.Count > 0 || availAttrCombine.Count > 0 || deleteItemList.Count > 0)
						{
							if (distinctStock)
							{
								string itemInfoSql = @$"select ip.item_spec,ip.item_id,item_name,py_str,item_no,item_class,other_class,item_brand,mu.unit_no,unit_factor,unit_type,mu.barcode,mu.wholesale_price,mu.buy_price,mu.retail_price,mu.weight,mu.volume from info_item_prop ip left join info_item_multi_unit mu  on ip.item_id=mu.item_id  and ip.company_id=mu.company_id where ip.company_id={companyID} and ip.item_id={item_id}";
								List<ExpandoObject> itemUnitInfo = await CDbDealer.GetRecordsFromSQLAsync(itemInfoSql, cmd);

								SQLQueue QQ = new SQLQueue(cmd);
								//  区分库存：  如果id存在， 更新状态、批发价、条码  不存在：状态是true，创建对应的子商品，更新状态、批发价、条码
								string createItemSql;
								List<string> deleteSonItemIds = new List<string>();
								foreach (dynamic availItem in availAttrCombine)
								{
									string availItemStatus = availItem.status + "";
									string availItemId = availItem.item_id + "";
									string availItemBBarcode = availItem.bBarcode + "";
									string availItemMBarcode = availItem.mBarcode + "";
									string availItemSBarcode = availItem.sBarcode + "";
									string availItemBPrice = availItem.bPrice + "";
									string availItemMPrice = availItem.mPrice + "";
									string availItemSPrice = availItem.sPrice + "";
									string availItemSonOptionsId = availItem.son_options_id + "";
									db = new CDbDealer();
									if (availItemId.StartsWith("nanoid"))
									{
										db.AddField("company_id", companyID);
										db.AddField("item_name", $@"{availItem.item_name}");
										db.AddField("py_str", $@"{availItem.py_str}");
										db.AddField("son_mum_item", item_id);
										db.AddField("son_options_id", availItemSonOptionsId);
										db.AddField("item_class", item_class);
										db.AddField("item_spec", item_spec);
										db.AddField("item_no", item_no);
										db.AddField("other_class", other_class);
										db.AddField("item_brand", item_brand);
										db.AddField("status", availItemStatus);
										if (!string.IsNullOrEmpty(batch_level))
										{
											db.AddField("batch_level", batch_level);
										}
										string sqlItem = db.GetInsertSQL("info_item_prop") + " returning item_id";
										string sqlUnits = "";
										foreach (dynamic unit in itemUnitInfo)
										{
											db = new CDbDealer();
											db.AddField("company_id", companyID);
											db.AddField("item_id", "@ITEM_ID");
											db.AddField("unit_no", unit.unit_no);
											db.AddField("unit_factor", unit.unit_factor);
											db.AddField("unit_type", unit.unit_type);
											// db.AddField("wholesale_price", unit.wholesale_price);
											db.AddField("buy_price", unit.buy_price);
											db.AddField("retail_price", unit.retail_price);
											db.AddField("weight", unit.weight);
											db.AddField("volume", unit.volume);
											string unitBarcode = unit.barcode;
											string wholesalePrice = unit.wholesale_price;
											string sonBarcode = "";
											string sonWholesalePrice = "";
											if (unit.unit_type == "b")
											{
												sonBarcode = string.IsNullOrEmpty(availItemBBarcode) ? unitBarcode : availItemBBarcode;
												sonWholesalePrice = string.IsNullOrEmpty(availItemBPrice) ? wholesalePrice : availItemBPrice;
												db.AddField("barcode", sonBarcode);
												db.AddField("wholesale_price", sonWholesalePrice);
											}
											else if (unit.unit_type == "m")
											{
												sonBarcode = string.IsNullOrEmpty(availItemMBarcode) ? unitBarcode : availItemMBarcode;
												sonWholesalePrice = string.IsNullOrEmpty(availItemMPrice) ? wholesalePrice : availItemMPrice;
												db.AddField("barcode", sonBarcode);
												db.AddField("wholesale_price", sonWholesalePrice);
											}
											else if (unit.unit_type == "s")
											{
												sonBarcode = string.IsNullOrEmpty(availItemSBarcode) ? unitBarcode : availItemSBarcode;
												sonWholesalePrice = string.IsNullOrEmpty(availItemSPrice) ? wholesalePrice : availItemSPrice;
												db.AddField("barcode", sonBarcode);
												db.AddField("wholesale_price", sonWholesalePrice);
											}
											sqlUnits += db.GetInsertSQL("info_item_multi_unit") + ";";
										}
										createItemSql = $@"SELECT yj_exeSqlByInsertedRowID('{sqlItem.Replace("'", "''")}','{sqlUnits.Replace("'", "''")}','@ITEM_ID');";
										QQ.Enqueue(availItemSonOptionsId, createItemSql);
									}
									else
									{
										deleteSonItemIds.Add(availItemId);
										attrSql += $"update info_item_prop set status='{availItemStatus}' where company_id={companyID} and item_id={availItemId};";
										foreach (dynamic unit in itemUnitInfo)
										{
											db = new CDbDealer();
											db.AddField("company_id", companyID);
											db.AddField("item_id", availItemId);
											db.AddField("unit_no", unit.unit_no);
											db.AddField("unit_factor", unit.unit_factor);
											db.AddField("unit_type", unit.unit_type);
											// db.AddField("wholesale_price", unit.wholesale_price);
											db.AddField("buy_price", unit.buy_price);
											db.AddField("retail_price", unit.retail_price);
											db.AddField("weight", unit.weight);
											db.AddField("volume", unit.volume);
											string unitBarcode = unit.barcode;
											string wholesalePrice = unit.wholesale_price;
											string sonBarcode = "";
											string sonWholesalePrice = "";
											if (unit.unit_type == "b")
											{
												sonBarcode = string.IsNullOrEmpty(availItemBBarcode) ? unitBarcode : availItemBBarcode;
												sonWholesalePrice = string.IsNullOrEmpty(availItemBPrice) ? wholesalePrice : availItemBPrice;
												db.AddField("barcode", sonBarcode);
												db.AddField("wholesale_price", sonWholesalePrice);
											}
											else if (unit.unit_type == "m")
											{
												sonBarcode = string.IsNullOrEmpty(availItemMBarcode) ? unitBarcode : availItemMBarcode;
												sonWholesalePrice = string.IsNullOrEmpty(availItemMPrice) ? wholesalePrice : availItemMPrice;
												db.AddField("barcode", sonBarcode);
												db.AddField("wholesale_price", sonWholesalePrice);
											}
											else if (unit.unit_type == "s")
											{
												sonBarcode = string.IsNullOrEmpty(availItemSBarcode) ? unitBarcode : availItemSBarcode;
												sonWholesalePrice = string.IsNullOrEmpty(availItemSPrice) ? wholesalePrice : availItemSPrice;
												db.AddField("barcode", sonBarcode);
												db.AddField("wholesale_price", sonWholesalePrice);
											}
											attrSql += db.GetInsertSQL("info_item_multi_unit") + ";";

											// attrSql += db.GetUpdateSQL("info_item_multi_unit", $"company_id ={companyID} and item_id = {availItemId} and unit_type = '{unit.unit_type}'") + ";";
										}
									}
								}

								if (deleteSonItemIds.Count > 0)
								{
									string deleteSonItemSql = @$"
delete from info_item_multi_unit iimu where iimu.company_id = {companyID} and iimu.item_id in ({string.Join(", ", deleteSonItemIds.Select(id => $"'{id}'").ToArray())});";
									cmd.CommandText = deleteSonItemSql;
									await cmd.ExecuteNonQueryAsync();
								}

								if (QQ.Count > 0)
								{
									CMySbDataReader dr = await QQ.ExecuteReaderAsync();
									while (QQ.Count > 0)
									{
										string son_options_id = QQ.Dequeue();
										if (dr.Read())
										{
											object ovAttrOv = dr[0];
											if (ovAttrOv != DBNull.Value)
											{
												// 查找并更新
												foreach (JObject item in availAttrCombine.Children<JObject>())
												{
													if ((string)item["son_options_id"] == son_options_id)
													{
														string s = ovAttrOv.ToString();
														if (s.Contains(","))
														{
															s = s.Split(",")[0];
														}
														item["item_id"] = s; // 设置新的 item_id 值
														item["son_mum_item"] = item_id; // 设置新的 item_id 值
														break; // 如果只需要更新第一个匹配的元素，找到后即可退出循环
													}
												}
											}
										}
									}
								}
								QQ.Clear();
							}
							else
							{
								// 不区分库存的商品，如果第一次创建成区分库存，未使用的时候创建了子商品，后续改成了不区分库存的商品，应该进行删除已经创建的子商品
								var availAttrCombineIds = availAttrCombine
									.Where(item => !((string)item["item_id"]).StartsWith("nanoid"))
									.Select(item => $"'{(string)item["item_id"]}'")
									.Distinct(); // 去除重复的ID

								var deleteItemListIds = deleteItemList
									.Where(item => !((string)item["item_id"]).StartsWith("nanoid"))
									.Select(item => $"'{(string)item["item_id"]}'")
									.Distinct(); // 去除重复的ID

								var filteredIds = availAttrCombineIds.Concat(deleteItemListIds).Distinct(); // 合并并去除重复的ID
								string noDistinctStockId = string.Join(",", filteredIds); // 使用逗号连接字符串

								if (!string.IsNullOrEmpty(noDistinctStockId))
								{
									attrSql += @$"delete from info_item_prop where company_id={companyID} and item_id in ({noDistinctStockId});";
									attrSql += @$"delete from info_item_multi_unit where company_id={companyID} and item_id in ({noDistinctStockId});";
									foreach (dynamic availItem in availAttrCombine)
									{
										availItem.item_id = "nanoid" + Nanoid.Nanoid.Generate();
									}
								}


							}

							// 更新mum_attribute\avail_attr_combine
							string itemMumAttributesStr = itemMumAttributes.ToString();
							string availAttrCombineStr = availAttrCombine.ToString();
							attrSql += @$"update info_item_prop set mum_attributes='{itemMumAttributesStr}', avail_attr_combine = '{availAttrCombineStr}' where company_id={companyID} and item_id={item_id};";
							if ("0".Equals(status))
							{   // 父商品停用，才能全部停用
								attrSql += @$"update info_item_prop set status = '{status}' where company_id={companyID} and son_mum_item={item_id};";
							}
							// 处理删除问题 deleteItemList 删除创建的档案， id不为nanoid的情况
							string itemIds = string.Join(",",
								deleteItemList
									.Where(item => !((string)item["item_id"]).StartsWith("nanoid"))
									.Select(item => $"'{(string)item["item_id"]}'"));
							if (!string.IsNullOrEmpty(itemIds))
							{
								attrSql += @$"delete from info_item_prop where company_id={companyID} and item_id in ({itemIds});";
								attrSql += @$"delete from info_item_multi_unit where company_id={companyID} and item_id in ({itemIds});";
							}
							if (!string.IsNullOrEmpty(attrSql))
							{
								await CDbDealer.GetRecordsFromSQLAsync(attrSql, cmd);
							}
						}

					}
                    
                }
                else//是子商品
                {
                    string data_son_options_id = data.son_options_id;
                    // 同步更新父商品中avail_attr_combine
                    string itemFatherSQL = $"select avail_attr_combine from info_item_prop where  company_id={companyID} and item_id= {son_mum_item}";
                    dynamic availResult = await CDbDealer.Get1RecordFromSQLAsync(itemFatherSQL, cmd);
                    var availAttrCombine = ((IDictionary<string, object>)availResult)["avail_attr_combine"].ToString();
                    JArray availAttrCombineJArray = JArray.Parse(availAttrCombine);
                    JObject findAvailItem = availAttrCombineJArray.FirstOrDefault(availItem => availItem["son_options_id"].ToString().Equals(data_son_options_id)) as JObject;
                    if (findAvailItem != null)
                    {
                        findAvailItem["status"] = Int32.Parse(status);
                        JArray gridUnits = JArray.Parse(data["gridUnit"].ToString());
                        foreach (JObject gridUnit in gridUnits)
                        {
                            string unitType = gridUnit["unit_type"].ToString();
                            switch (unitType)
                            {
                                case "b":
                                    findAvailItem["bBarcode"] = gridUnit["barcode"];
                                    findAvailItem["bPrice"] = gridUnit["wholesale_price"];
                                    break;
                                case "m":
                                    findAvailItem["mBarcode"] = gridUnit["barcode"];
                                    findAvailItem["mPrice"] = gridUnit["wholesale_price"];
                                    break;
                                case "s":
                                    findAvailItem["sBarcode"] = gridUnit["barcode"];
                                    findAvailItem["sPrice"] = gridUnit["wholesale_price"];
                                    break;
                            }
                        }
                    }
                    string newAvail = availAttrCombineJArray.ToString();
                    itemFatherSQL = @$"update info_item_prop set avail_attr_combine = '{newAvail}' where company_id={companyID} and item_id={son_mum_item};";
                    await CDbDealer.Get1RecordFromSQLAsync(itemFatherSQL, cmd);
                }
            }
            catch (Exception e)
            {
                tran.Rollback();
                MyLogger.LogMsg("修改商品属性失败,msg:" + e.Message + e.StackTrace +" data:"+ data, companyID);
                return Json(new { result = "Error" , msg = "修改商品属性失败" });
            }
            
            #endregion


            // 添加分销同步逻辑
            bool changeClass = false;


            if (item_id != "" && !item_brand.IsNullOrWhiteSpace())
            {
                // 同步分销商商品档案

                if ((oldClass != null && (string)oldClass.item_class != item_class) || ((string)data.item_id).IsNullOrWhiteSpace()) changeClass = true;
                dynamic itemInfoSyncResult = await ResellerService.EditItemInfoSync(new
                {
                    itemId = item_id,
                    operKey = (string)data.operKey,
                    brandId = item_brand,
                    companyId = companyID,
                    changeClass = true

                }, cmd, tran);
                if (itemInfoSyncResult.result != "OK")
                {
                    return Json(new { result = "Error", msg = itemInfoSyncResult.msg, added = false, record = new { } });
                }
            }

            // 废弃，新版应该是用不上
            // string sqlAttr = await ItemEditController.GetAttributeSQL(companyID, data, cmd);
            string rollBackMsg = "";
            try
            {
                // if (sqlAttr != "")
                // {
                //     cmd.CommandText = sqlAttr;
                //     await cmd.ExecuteScalarAsync();
                // }
                //插入前查询旧数据：params:GetRecord(CMySbCommand cmd, string operKey, string IdValue)
                model.DataItems[model.m_idFld].Value = item_id;
                model.DataItems[model.m_nameFld].Value = item_name;
                string approveBrief = data.approve_brief ?? "";
                string logFlowId = data.flow_id ?? "";//
                string receiverId = data.receiverId ?? "";
                string msgId = data.msg_id ?? "";
                await model.SaveLog(cmd, (string)data.operKey, origInfo, data.approve_flag.ToString(), approveBrief, logFlowId, receiverId, msgId);
            }
            catch(Exception e)
            {
                rollBackMsg = "ERROR";
                tran.Rollback();
            }
            if(rollBackMsg=="" && tran!=null) tran.Commit();
          
            
           

            return Json(new { result, msg, item_id,new_id,item_images });
        }
        
        [HttpPost]

        public async Task<string> CheckBeforeDelete(string operKey, string rowIDs)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var msg = "";

                var id = rowIDs;
                cmd.CommandText = @$"select item_id from sheet_sale_detail where item_id = {id}  and company_id={companyID}  
                union all
                select item_id from sheet_sale_order_detail where item_id = {id}   and company_id = { companyID }
                    union all
               select item_id from sheet_order_item_detail where item_id = {id}   and company_id = { companyID }
                    union all
               select item_id from sheet_item_ordered_adjust_detail where item_id = {id}   and company_id = {companyID }
                     union all
               select item_id from sheet_buy_detail where item_id = {id}   and company_id = { companyID }
                    union all
              select item_id from sheet_move_detail where item_id = {id}   and company_id = { companyID }
                    union all
               select item_id from sheet_inventory_detail where item_id = {id}   and company_id = { companyID }
                    limit 1;";
                var item_id = await cmd.ExecuteScalarAsync();
                if (item_id != null)
                {
                    cmd.CommandText = $" select item_name from info_item_prop where  company_id ={companyID} AND item_id ={item_id} ";
                    var name = await cmd.ExecuteScalarAsync();
                    msg += $"{name} ";
                }

            if (msg != "") return $"{msg}已被使用，无法删除";
            else
            { 
                return "";
            }


        }
        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            
            string err = await CheckBeforeDelete((string)data.operKey, (string)data.rowIDs);
            if (err != "")
            {
                return new JsonResult(new { result = "Error", msg = err });
            }
            cmd.ActiveDatabase = "";
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            try
            {
                string delRecordSql = $@"delete from info_item_prop where  company_id={companyID} and item_id in ({data.rowIDs});";
                string nowTime = CPubVars.GetDateText(DateTime.Now);
                string approveStatus = (string)data.approveStatus;
                //if (approveStatus == "DELETE")
                //{
                //    delRecordSql += $@"insert into document_change_log(company_id,obj_id,obj_name,oper_id,happen_time,approve_status,oper_action) values
                //               ({companyID},{data.rowIDs},'商品档案',{operID},'{nowTime}','wait approve','DELETE');";
                //    //todo:发送审核消
                //    string msgTitle = @$"商品档案被删除了：{(string)data.itemName}，请审核";
                //    await MessageCreateServices.CreateMessageService(new
                //    {
                //        operKey = (string)data.operKey,
                //        createrId = operID,
                //        msgClass = "todo",
                //        msgType = "ItemApprove",
                //        msgSubType = "ItemDelete",
                //        obj_id = data.rowIDs,
                //        receiverId = "",
                //        msgTitle,
                //    }, cmd);

                //}
                //else if (approveStatus == "DELETE_AND_APPROVED")
                //{
                //    delRecordSql += $@"insert into document_change_log(company_id,obj_id,obj_name,oper_id,happen_time,approve_status,approver_id,approve_time,oper_action) values
                //               ({companyID},{data.rowIDs},'商品档案',{operID},'{nowTime}','approved',{operID},'{nowTime}','DELETE');";
                //}
                delRecordSql += $@"insert into document_change_log(company_id,obj_id,obj_name,oper_id,happen_time,approve_status,approver_id,approve_time,oper_action) values
                               ({companyID},{data.rowIDs},'商品档案',{operID},'{nowTime}','approved',{operID},'{nowTime}','DELETE');";
                cmd.CommandText = delRecordSql;
                await cmd.ExecuteNonQueryAsync();
                tran.Commit();
                return new JsonResult(new { result = "OK", msg = "" });
            }
            catch (Exception e)
            {
                tran.Rollback();
                return Json(new { result = "Error", msg = "删除失败" });
            }
        }

        [HttpPost]
        public async Task<JsonResult> CreateAttrName([FromBody] dynamic dataParams)
        {
            string result = "OK";
            string msg = "";
            Security.GetInfoFromOperKey((string)dataParams.operKey, out string companyID);
            int attr_id = dataParams.attr_id;
            string opt_name = dataParams.opt_name;
            string sql =  $@"insert into info_attr_opt(company_id,attr_id,opt_name) values ('{companyID}', '{attr_id}', '{opt_name}') on conflict(company_id,attr_id,opt_name) do update set company_id={companyID} returning attr_id,opt_id,opt_name";
            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            Dictionary<string, dynamic> data = new Dictionary<string, dynamic>();
            if (rec != null)
            {
                data.Add("attr_id", rec.attr_id);
                data.Add("opt_id", rec.opt_id);
                data.Add("opt_name", rec.opt_name);
            }
            return Json(new {result , msg, data});
        }

        [HttpGet]
        public async Task<JsonResult> GetAttrItemInfos(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql = $"select * from info_attribute where company_id ={companyID} order by order_index";
            //var availAttributes = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            var availAttributes = await CDbDealer.GetRecordsFromSQLAsync<ItemEditModel.InfoAttribute>(sql, cmd);
            sql = $"select opt_id,opt_name,o.attr_id from info_attr_opt o left join info_attribute a on a.company_id={companyID} and o.attr_id=a.attr_id where o.company_id={companyID} order by o.order_index;";
            var options =  await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            var attrOptions = options.GroupBy(opt =>((dynamic)opt).attr_id).ToDictionary(g=>(string)g.Key,g=>g.ToList()); 
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, availAttributes, attrOptions});
        }
    }
}
