### 生成创建dump文件
1.查看DotNET进程pid
`ps -ef | grep _`
2.执行DotNetCreateDump命令
`/usr/share/dotnet/shared/Microsoft.NETCore.App/3.1.28/createdump ${pid} #本文档将以29515号进程进行演示`
`执行结束后，在tmp文件夹生成coredump.${pid} 格式的文件`
### 下载工具
https://docs.microsoft.com/zh-cn/dotnet/core/diagnostics/dotnet-dump
https://aka.ms/dotnet-counters/linux-x64
#### 执行脚本
`wget https://docs.microsoft.com/zh-cn/dotnet/core/diagnostics/dotnet-dump #下载dotnetdump`
`wget https://aka.ms/dotnet-counters/linux-x64 #下载性能计数器`
### 调试堆栈命令

mkdir oom-tools #创建oom工具文件夹归纳oom工具

chmod 777 dotnet-dump #赋权限

./dotnet-dump analyze /tmp/coredump.29515 #执行命令分析该dump文件

dumpheap -stat 
作用：查看内存占用堆栈地址、大小和数量
示例：dumpheap -stat 

dumpheap -mt ==堆栈地址== 
作用：查看当前堆栈编号对象
示例：dumpheap -mt 00007fde732d14c0

gcroot -all ==对象地址== 
作用：查看当前对象的根信息
示例：gcroot -all  00007fdc4882e560

do ==对象地址== 
作用：查看当前对象的内容
示例： do 00007fdc4882e560

gcwhere ==对象地址== 
作用：查看当前对象的gc代数
示例： gcwhere 00007fdc4882e560
### 调试线程命令

clrthreads 
作用：查看所有线程，返回结果第一列为 线程号
示例： clrthreads

setthread ==线程号== 
作用：跳到指定线程
示例：setthread 17

clrstack 
作用：返回当前线程运行内容
示例：clrstack
