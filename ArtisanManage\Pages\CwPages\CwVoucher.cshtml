﻿@page
@model ArtisanManage.CwPages.CwVoucherModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="en">

<head>
    <partial name="_VoucherHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/CwSheet/Voucher.js?v=@Model.PartialViewModel.Version"></script>

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.g_operName = '@Html.Raw(Model.OperName)';
        window.g_companySetting = @Html.Raw(Model.JsonCompanySetting);
        window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);
        window.CoolieServerUri = '@Html.Raw(Model.CoolieServerUri)';
        //window.g_pageType = 'sheet';//回车站
        window.g_startDay = '@Model.PartialViewModel.StartDay';
        window.g_endDay = '@Model.PartialViewModel.EndDay';
    </script>
    <style>
        body {
            overflow: hidden;
        }

        #divTitle {
            margin-top: 5px;
            height: 45px;
            text-align: center;
        }

        #jqxgrid {
            position: static;
            margin-left: 10px;
            width: 100%;
            height: 100%;
            border-bottom-color: #dedede;
        }
        
        .money_display ul {
            margin:0;
            padding:0;
            height:40px;
        }

        .money_display ul li {
            float:left;
            display:inline-block;
            list-style:none;
            width:25px;
            height:40px;
            border-right:1px solid #aaa;
            text-align:center;
            line-height:40px;
            font-size:16px;
        }
        .money_display ul li:nth-child(3n) {
            border-right:1px solid #3E97EB;
        }
        .money_display ul li:nth-last-child(3){
            border-right:1px solid red;
        }
        .money_display ul li:last-child {
            border-right:0;
        }
        .title {
            text-align:center;
        }

        .title .top_title {
            height:55%;
            line-height:32px;
        }
        .title .bottom_unit ul{
            margin:0;
            padding:0;
        }

        .title .bottom_unit ul li {
            display:inline-block;
            list-style:none;
            width:25px;
            border-top:1px solid #aaa;
            border-right:1px solid #aaa;
            text-align:center;
            line-height:30px;
        }
        .bottom_unit ul li:nth-child(3n) {
            border-right:1px solid #3E97EB;
        }
        .bottom_unit ul li:nth-last-child(3){
            border-right:1px solid red;
        }

        .divButtons button{
            display:inline-block;
        }

        .makeInfo > div {
            text-align:left;
            width:210px;
        }

        .makeInfo > div > div:first-child, .makeInfo > div > div:last-child {
            text-align: left;
            width:fit-content;
        }

        #bizSheets{
            width:420px;
            display:none;
        }

        #jqxNotificationDefaultContainer-top-right{
            z-index: 10000;
        }
        #jqxNotificationDefaultContainer-top-right .jqx-notification {
            padding:15px;
            border: 1px solid darkgrey;
        }

    </style>

</head>
<body>
    <div id="divTitle">
        <label id="lblSheetTitle" style="font-weight:500;font-size:25px;">@Html.Raw(Model.SheetTitle)</label>
        <img id="imgState" style="display:none;position:fixed;top:0px;left:calc(50% - 150px);" src="" />
        <div class="makeInfo" style="position:absolute;top:5px; right:0px;">
            <div><div><label>制单:</label></div> <div><span id="make_time"></span><span id="maker_name"></span></div></div>
            <div><div><label>审核:</label></div> <div><span id="approve_time"></span><span id="approver_name"></span></div></div>
            <div id="bizSheets"><div><label>业务单据:</label></div> <div><a id="biz_id_nos"></a></div></div>
            <div id="business_sheet_type" style="display:none;"></div>
        </div>
    </div>

    <div id="divHead" class="headtail">
        <div style="float:none;height:0px; clear:both;"></div>
    </div>

    <div id="jqxgrid"></div>

    <div id="divTail" class="headtail">

        <div style="float:none;height:0px; clear:both;"></div>
    </div>

    <div id="divButtons" style="text-align:center;">
        <button id="btnSave" type="button" style="display:none;">保存</button>
        <button id="btnApprove" type="button" style="display:none;">审核</button>
        <button id="btnCancelApprove" type="button" style="display:none;">取消审核</button>
        <button id="btnRed" type="button" disabled style="display:none;">红冲</button>
        <button id="btnDelete" type="button" disabled style="display:none;">删除</button>
        <button id="btnCopy" type="button">复制</button>
        <button id="btnAdd" type="button">新增</button>
        <button id="btnPrint" type="button" style="display:none;"> 打印</button>
        <button id="btnSourceSearch" type="button" >源单</button>
        <button id="btnClose" type="button">关闭</button>

    </div>

    <div id="popSub" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择科目</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

    <div id="jqxNoti_allSheets"></div>

</body>

</html>