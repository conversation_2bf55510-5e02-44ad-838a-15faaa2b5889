@page
@model ArtisanManage.Pages.BaseInfo.SupplierAccountHistoryModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">

    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var m_db_id = "10";

    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $('#supcust_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popClient').jqxWindow('open');
                        $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/SuppliersView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });


                $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                if (args.datafield == "sheet_no") {
                    debugger
                    var sheet_id = args.row.bounddata.sheet_id;
                    var sheet_type = args.row.bounddata.sheet_type;
                    if (sheet_type == '采购单' || sheet_type == '采购退货单') window.parent.newTabPage(sheet_type, `Sheets/BuySheet?sheet_id=${sheet_id}`);
                    if (sheet_type == '预付款单') window.parent.newTabPage(sheet_type, `Sheets/PrepaySheet?sheet_id=${sheet_id}&forPayOrGet=true`);
                    if (sheet_type == '付款单') window.parent.newTabPage(sheet_type, `Sheets/GetArrearsSheet?sheet_id=${sheet_id}&forPayOrGet=true`);
                    if (sheet_type == '费用支出单') window.parent.newTabPage(sheet_type, `Sheets/FeeOutSheet?sheet_id=${sheet_id}&forPayOrGet=true`);
                    if (sheet_type == '其他收入单') window.parent.newTabPage(sheet_type, `Sheets/FeeOutSheet?sheet_id=${sheet_id}`);
                }
            });
            $("#gridItems").jqxGrid('beforeRowRender', function (divRow, rowData) {
                if (rowData.sheet_status == '已红冲')
                    divRow.style.color = '#888'
                else if (rowData.sheet_status == '红字单')
                    divRow.style.color = '#f00'
                else
                    divRow.style.color = '#000'

            })
                $('.btnAct').on('click', function () {
                    var act = $(this).data('act');
                    window[act]();
                });
                QueryData();
            });
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "SuppliersView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                    

                }
                $('#popClient').jqxWindow('close');
            }
        })
    </script>
</head>

<body>

    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>

    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择供应商</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
</body>
</html>