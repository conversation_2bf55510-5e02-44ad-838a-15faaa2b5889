﻿@page
@model ArtisanManage.Pages.CwPages.Report.DetailAccountModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <style>
        ::-webkit-scrollbar-corner {
            background-color: #fff;
        }

        #clearSvg{
            display:none;
        }

        #page_title{
            width: 100%;
            height: 40px;
            font-size: 30px;
            text-align: center;
            justify-content: center;
            padding-top: 5px;
            padding-bottom: 5px;
            display: flex;
        }

        .voucher_find_sheet_a{
            cursor:pointer !important;
        }
        .voucher_a:visited, .voucher_find_sheet_a:visited {
            color: #4499ff;
        }

        #jqxNoti{
            position: absolute;
            border:1px solid dimgray;
            top:5px;
            right:5px;
            display:none;
        }

        #jqxNotificationDefaultContainer-top-right {
            z-index: 10000;
            max-height: calc(100% - 10px);
            overflow-y: scroll;
            direction:ltr;
        }

        #jqxNotificationDefaultContainer-top-right .jqx-notification {
            padding: 15px;
            border: 1px solid darkgrey;
        }
    </style>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);
        window.ErrMsg = '@Html.Raw(Model.ErrMsg)';
        window.OpenDate = '@Html.Raw(Model.OpenDate)';
        window.MaxDate = '@Html.Raw(Model.MaxDate)';
        window.PeriodChoose = '@Html.Raw(Model.PeriodChoose)';
        window.SubIdChoose = '@Html.Raw(Model.SubIdChoose)';
        
        $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
            return false;
        });

        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript);
            @Html.Raw(Model.m_createGridScript);

            $('#timeType .row-oper').remove();
            if (PeriodChoose){
                let monthStart = `${PeriodChoose.split('~')[0]} 00:00:00`;
                $("#startDay").val(new Date(monthStart));
                let endDayInit = new Date(PeriodChoose.split('~')[1]);
                endDayInit = new Date(endDayInit.getFullYear(), endDayInit.getMonth()+1, 1);
                endDayInit = new Date(endDayInit.setDate(0));
                let monthEnd = `${endDayInit.getFullYear()}-${endDayInit.getMonth()+1}-${endDayInit.getDate()} 23:59:59`;
                $('#endDay').val(new Date(monthEnd));
            }else{
                $("#endDay").val(new Date(MaxDate));
                $("#startDay").val(new Date(OpenDate));
                if (new Date(MaxDate).getFullYear() != new Date(OpenDate).getFullYear() || new Date(MaxDate).getMonth() != new Date(OpenDate).getMonth()) {
                    let newStartDay = new Date(new Date(MaxDate).setDate(1));
                    $("#startDay").val(new Date(newStartDay.getFullYear(), newStartDay.getMonth(), newStartDay.getDate(), 0, 0, 0,));
                }
            }
            $("#startDay").jqxDateTimeInput('setMinDate', new Date(OpenDate));
            $("#startDay").jqxDateTimeInput('setMaxDate', new Date(MaxDate));
            $("#endDay").jqxDateTimeInput('setMinDate', new Date(OpenDate));
            $("#endDay").jqxDateTimeInput('setMaxDate', new Date(MaxDate));
            
            if (SubIdChoose) {
                if (!(window.g_operRights.cwReport && window.g_operRights.cwReport.detailBill && window.g_operRights.cwReport.detailBill.see)) {
                    bw.toast('没有明细账访问权限', 3000);
                    return;
                }

                var tree = $('#other_sub').jqxTree('getInstance')
                $('#other_sub').jqxTree('selectItem', tree.items.find(item => item.value == SubIdChoose));
            }
            let sub = $('#other_sub').jqxTree('getSelectedItem');
            if (sub) $('#sub_code').val(sub.label);
            $('#sub_code .row-oper').remove();

            if(ErrMsg!=''){
                bw.toast(ErrMsg, 3000);
                return;
            }

            if (!window.g_operRights.cwReport) {
                return;
            }
            if (!window.g_operRights.cwReport.detailBill) {
                return;
            } else {
                if (window.g_operRights.cwReport.detailBill.see) {
                    QueryData();
                    $('#btnQuery').attr('disabled', false);
                    $('#btnApproveVo').attr('disabled', false);
                }
                if (window.g_operRights.cwReport.detailBill.export) {
                    $('#btnExport').attr('disabled', false);
                }
            }


            $('#btnQuery').on('click', function () {
                QueryData();
            });

            $('#btnApproveVo').on('click', function () {
                if (!$('#startDay').val()) $('#startDay').val(new Date(OpenDate));
                if (!$('#endDay').val()) $('#endDay').val(new Date(OpenDate));
                if (new Date($('#startDay').val()) > new Date($('#endDay').val())) {
                    let tempDate = new Date($('#startDay').val());
                    $('#endDay').val(new Date($('#startDay').val()));
                    $('#startDay').val(tempDate);
                }

                let startDay = $('#startDay').val();
                let endDay = $('#endDay').val();
                window.parent.newTabPage('查凭证录入', `/CwPages/CwVoucherView?startDay=${startDay.split(' ')[0]}&endDay=${endDay}&status=unapproved`, window);
            });

            $('#btnExport').on('click', function () {
                ExportExcel();
            });

            $('#other_sub').on('select', function (e) {
                var item = $('#other_sub').jqxTree('getItem', e.args.element);
                $('#sub_code').val(item.label);
            });

            $('#timeType').on('select', function (e) {
                if(e.args.item.value=='m'){
                    typedTime();
                }
            });

            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                if (args.datafield == "sheet_no") {
                    var sheet_id = args.row.bounddata.sheet_id;
                    window.parent.newTabPage('凭证', `CwPages/CwVoucher?sheet_id=${sheet_id}`, window);
                }
            });

        });

        function beforeQuery(){
            let timeType = $('#timeType').val();
            if (!$('#startDay').val()) $('#startDay').val(new Date(OpenDate));
            if (!$('#endDay').val()) $('#endDay').val(new Date(OpenDate));
            if (new Date($('#startDay').val()) > new Date($('#endDay').val())){
                let tempDate = new Date($('#startDay').val());
                $('#endDay').val(new Date($('#startDay').val()));
                $('#startDay').val(tempDate);
            }
            if (timeType.value == 'm') {
                typedTime();
            }
        }

        function typedTime(){
            let startTime = new Date($('#startDay').val());
            if (startTime < new Date(OpenDate)){
                $('#startDay').val(new Date(OpenDate));
                startTime = $('#startDay').val();
            }
            if (new Date(startTime).getDate() != 1) {
                let monthStartDay = 1;
                if (new Date(OpenDate).getFullYear() == startTime.getFullYear() && new Date(OpenDate).getMonth() == startTime.getMonth() && new Date(OpenDate).getDate() > startTime.getDate()) {
                    monthStartDay = new Date(OpenDate).getDate();
                }
                $('#startDay').val(new Date(startTime.setDate(monthStartDay)));
            }

            let endTime = new Date($('#endDay').val());
            let year = endTime.getFullYear();
            let month = endTime.getMonth() + 1;
            endTime = new Date(year, month, 1);
            endTime = new Date(endTime.setDate(0));
            let monthEnd = `${year}-${month}-${endTime.getDate()} 23:59:59`;
            $('#endDay').val(new Date(monthEnd));
        }

        function biz_id_nosRender(row, column, value, p4, p5, rowData) {
            if (value == '') return p4;
            let biz_sheet_innerHTML = '<div class="jqx-grid-cell-left-align" style="margin-top: 2px; line-height: 28px;">';
            let biz_id_no_arr = value.split(';');

            if (biz_id_no_arr.length>0) {
                for (let i = 0; i < (biz_id_no_arr.length > 2 ? 2 : biz_id_no_arr.length); i++) {
                    let id_no_arr = biz_id_no_arr[i].split(',');
                    biz_sheet_innerHTML += `<a class="voucher_find_sheet_a" href="#" onmouseup="voucherClickSheet(event,'${rowData.business_sheet_type}','${id_no_arr[0]}','${id_no_arr[1]}')">${id_no_arr[1]}</a><span>,</span>`;
                }
                biz_sheet_innerHTML = biz_sheet_innerHTML.slice(0, -14);
                if (biz_id_no_arr.length > 2) {
                    biz_sheet_innerHTML += `<span style="cursor:pointer;" onclick="showBizSheetAll(${row})">...</span>`;
                }
            }
            
            biz_sheet_innerHTML += '</div>';
            return biz_sheet_innerHTML;
        }

        function showBizSheetAll(rowindex) { 
            let row = $('#gridItems').jqxGrid('getrowdatabyid', rowindex);
            let bizSheetHtmlAll = '<div>';
            let biz_id_no_arr = row.biz_id_nos.split(';');
            let index = 1;
            biz_id_no_arr.forEach((id_no) => {
                let id_no_arr = id_no.split(',');
                bizSheetHtmlAll += `<a class="voucher_find_sheet_a" href="#" onmouseup="voucherClickSheet(event,'${row.business_sheet_type}','${id_no_arr[0]}','${id_no_arr[1]}')">${id_no_arr[1]}</a><span>,</span>`;
                if (index % 3 == 0) {
                    bizSheetHtmlAll += '<br>';
                }
                index++;
            });
            if (bizSheetHtmlAll.endsWith('<span>,</span>')) {
                bizSheetHtmlAll = bizSheetHtmlAll.substring(0, bizSheetHtmlAll.length - 14);
            }
            else if (bizSheetHtmlAll.endsWith('<span>,</span><br>')) {
                bizSheetHtmlAll = bizSheetHtmlAll.substring(0, bizSheetHtmlAll.length - 18);
            }
            $('#jqxNoti').html(bizSheetHtmlAll);
            $("#jqxNoti").jqxNotification({ width: "450", height: "auto", position: "top-right", opacity: 0.9, autoOpen: false, autoClose: false, template: null, theme: "summer", animationOpenDelay: "fast", animationCloseDelay: "fast", showCloseButton: true });
            $('#jqxNoti').jqxNotification('open');
        }

        function voucherClickSheet(e, biz_sheet_type, biz_sheet_id, biz_sheet_no) {
            e.stopPropagation();
            let sheet_name = '';
            let url = '';
            switch (biz_sheet_type) {
                case 'X':
                    sheet_name = '销售单';
                    url = `Sheets/SaleSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'D':
                    sheet_name = '销售单';
                    url = `Sheets/SaleSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'T':
                    sheet_name = '退货单';
                    url = `Sheets/SaleSheet?forReturn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'CG':
                    sheet_name = '采购单';
                    url = `Sheets/BuySheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'CT':
                    sheet_name = '采购退货单';
                    url = `Sheets/BuySheet?forReturn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'SK':
                    sheet_name = '收款单';
                    url = `Sheets/GetArrearsSheet?forPayOrGet=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'FK':
                    sheet_name = '付款单';
                    url = `Sheets/GetArrearsSheet?forPayOrGet=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'YS':
                    sheet_name = '预收款单';
                    url = `Sheets/PrepaySheet?forPayOrGet=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'YF':
                    sheet_name = '预付款单';
                    url = `Sheets/PrepaySheet?forPayOrGet=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'ZC':
                    sheet_name = '费用支出单';
                    url = `Sheets/FeeOutSheet?forOutOrIn=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'SR':
                    sheet_name = '其他收入单';
                    url = `Sheets/FeeOutSheet?forOutOrIn=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'YK':
                    sheet_name = '盘点盈亏单';
                    url = `Sheets/InventChangeSheet?forReduce=false&sheet_id=${biz_sheet_id}`;
                    break;
                case 'BS':
                    sheet_name = '报损单';
                    url = `Sheets/InventChangeSheet?forReduce=true&sheet_id=${biz_sheet_id}`;
                    break;
                case 'DH':
                    sheet_name = '定货会';
                    url = `Sheets/OrderItemSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'DHTZ':
                    sheet_name = '定货会调整单';
                    url = `Sheets/OrderItemAdjustSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'CBTJ':
                    sheet_name = '成本调价单';
                    url = `Sheets/CostPriceAdjustSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'TR':
                    sheet_name = '转账单';
                    url = `CwPages/CashBankTransferSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'RK':
                    sheet_name = '其他入库单';
                    url = `Sheets/StockInOutSheet?sheet_id=${biz_sheet_id}`;
                    break;
                case 'CK':
                    sheet_name = '其他出库单';
                    url = `Sheets/StockInOutSheet?sheet_id=${biz_sheet_id}`;
                    break;
            }
            window.parent.newTabPage(sheet_name, url, window);
        }

        function biz_briefsRender(row, column, value, p4, p5, rowData) {
            if (value == '') return p4;
            let briefs_html = '<div class="jqx-grid-cell-left-align" style="margin-top: 2px; line-height: 28px;">';
            if (value.length > 15) {
                briefs_html += value.substring(0, 15);
                briefs_html += `<span style="cursor:pointer;" onclick="showBriefsAll(${row})">...</span>`;
            } else {
                briefs_html += value;
            }
            briefs_html += '</div>';

            return briefs_html;
        }
        
        function showBriefsAll(rowindex) {
            let row = $('#gridItems').jqxGrid('getrowdatabyid', rowindex);
            $('#jqxNoti').html(`<span>${row.briefs}</span>`);
            $("#jqxNoti").jqxNotification({ width: "450", height: "auto", position: "top-right", opacity: 0.9, autoOpen: false, autoClose: false, template: null, theme: "summer", animationOpenDelay: "fast", animationCloseDelay: "fast", showCloseButton: true });
            $('#jqxNoti').jqxNotification('open');
        }

        window.getLeftTopAreaSize = function () {
            let otherWidth = $('#other_sub').outerWidth();
            let otherHeight = $('#page_title').outerHeight();
            return [otherWidth, otherHeight];
        };
    </script>
</head>
<body>

    <div id="page_title">明细账</div>
    <div style="display:flex;margin-top:0px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 200px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>

        <button id="btnQuery" style="margin:5px 10px 10px 10px;" disabled="disabled">查询</button>
        <button id="btnApproveVo" style="width: 80px; margin:5px 10px 10px 10px;" disabled="disabled">审核凭证</button>
        <button id="btnExport" style="margin:5px 20px 10px 10px;" disabled="disabled">导出</button>

    </div>
    <div style="display:flex;width:100%;height:calc(100% - 110px) !important;margin-top:0;">
        <!--左侧树-->
        <div id='other_sub' style="width:250px;height:calc(100% - 20px);margin-bottom:2px;overflow:scroll"></div>
        <!--右侧主表格-->
        <div style="width:calc(100% - 200px);height:100% !important; margin-left:10px; ">
            <!--表格部分-->
            <div id="gridItems" style="margin-top:0px;margin-left:10px; margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 20px);"></div>
            <!--统计行-->
            <div style="margin-bottom:2px;"><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>
        </div>
    </div>

    <div id="jqxNoti"></div><!--biz_id_nos & briefs 共用-->

</body>
</html>

