﻿@page
@model ArtisanManage.Pages.BaseInfo.RsSellerEditModel
@{
    Layout = null;
}
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>RsSellerEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script src="~/js/Vue.js"></script>

    <script type="text/javascript">


        @Html.Raw(Model.m_saveCloseScript)
            $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
                        });
    </script>
</head>
<body>
    <div id="divHead" class="headtail" style="width:500px;"></div>
    <div style="text-align:center;margin-top:20px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;">保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>

