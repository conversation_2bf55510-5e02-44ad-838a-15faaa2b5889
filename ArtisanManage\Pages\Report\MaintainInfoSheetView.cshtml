﻿@page
@model ArtisanManage.Pages.Report.MaintainInfoSheetViewModel
@{
}
<!DOCTYPE html>
<html>
<head>
    <title></title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script src="~/js/axios.min.js"></script>
    <style>
    html,
    body {
    margin: 0;
    padding: 0;
    }

    [v-cloak] {
    display: none;
    }

    #visitMaintain {
        padding:0 20px;
        box-sizing: border-box;
        width: 100%;
    }
    .maintain-mod{
        width:100%;
    }
    .maintain-title{
        text-align:center;
    }
    .maintain-item{
        padding:20px 0;
        border-bottom:2px dotted #ccc;
    }
    .maintain-sheet{
        display:flex;
        flex-wrap:wrap;
        width:100%;
    }
    .maintain-sheet>span{
        padding:5px 0;
        max-width:50%;
        min-width:50%;
    }
    .maintain-photo-box{
       
    }
    .maintain-photo-name{
        padding:5px 0;
    }
    .maintain-photo{
        display:flex;
        margin-left:10px;
    }
    .maintain-photo-item{
        margin-right:5px;
        display:flex;
        flex-direction:column;
        align-items:center;
    }

        .maintain-photo-item>span{
            padding:5px 0;
        }
        .maintain-status{

        }
        .btn-box{
            padding: 5px 0;
            display:flex;
            justify-content:space-between;
            
        }
        .reviewed>div{
            padding:5px 0;
        }

    </style>
</head>
<body>
    <div id="visitMaintain">
        <div class="maintain-mod">
            <div class="maintain-item" v-for="(item,index) in maintainInfo " :key="index">
               <div class="maintain-sheet">
                    <span>模板：{{item.dispTemplateName}}</span>
                    <span>上次维护时间：{{item.happenTime.replace("T"," ")}}</span>
                    <span>供应商：{{item.supcustName}}</span>
                    <span>业务员：{{item.operName}}</span>
                    <span>维护次数：{{item.maintainTimes}}/{{item.MonthMaintainTimes}}</span>
                    <span>间隔：{{item.MaintainIntervalDays}}天/次</span>
               </div>
               <div class="maintain-photo-box">
                    <div class="maintain-photos" v-for="(citem,cindex) in item.maintainPhoto" :key="cindex">
                        <div class="maintain-photo-name">{{citem.name}}</div>
                        <div class="maintain-photo" v-if="citem.photos.length">
                            <div class="maintain-photo-item" v-for="(ccItem,ccindex) in citem.photos" :key="ccindex">
                                <el-image style="width: 100px; height: 100px"
                                          :src="ccItem.url"
                                          :preview-src-list="photos">
                                </el-image>
                                <span>{{ccItem.type}}</span>
                            </div>
                        </div>
                    </div>
               </div>
               <div class="maintain-brief-box">
                    <div class="maintain-briefs" v-for="(cbitem,cbindex) in item.maintainBrief" :key="cbindex" v-if="cbitem.brief !=''">
                        <div class="maintain-brief-name" style="padding:5px 0;">{{cbitem.name}}</div>
                        <div class="maintain-brief-content">
                            <el-input disabled type="textarea" :autosize="{ minRows: 2}"  v-model="cbitem.brief" />
                        </div>
                    </div>
               </div>
                <div class="maintain-status">
                    <div class="no-review" v-if="item.status=='未维护'">
                        <div class="btn-box" v-if=" maintainReviewRight">
                            <el-button type="danger" round size="small" v-on:click="setMaintainStatus(item,-1)">不通过</el-button>
                            <el-button type="success" round size="small" v-on:click="setMaintainStatus(item,1)">通过</el-button>
                        </div>
                         <div v-else>
                            您当前尚无权限复核，请联系管理员设置
                        </div>
                        <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="item.reviewComment" v-if="maintainReviewRight">
                        </el-input>
                    </div>
                    <div class="reviewed" v-if="item.status !='未维护' && item.status!='不需要维护'">
                        <div>
                            <span>{{item.reviewTime.replace("T"," ")}}</span>
                            <span style="padding:0 10px;color:#67c23a" v-if="item.status == '维护通过'">维护通过</span>
                            <span style="padding:0 10px;color:#f56c6c" v-else>维护不通过</span>
                        </div>
                        <div>复核人：{{item.reviewerName}}</div>
                        <div>复核内容：{{item.reviewComment}}</div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    <script>
        var app = new Vue({
            el: "#visitMaintain",
            data() {
                return {
                    operKey:'',
                    visitID: '',
                    huaWeiObs: '',
                    photos: [],
                    visitTime:'',
                    maintainInfo:[],
                    maintainReviewTime:'',
                    maintainReviewer:'',
                    page_oper_name:'',
                    maintainReviewRight: false,
                }
            },
            methods:{
                getQueryString(name) {
                    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
                    var r = window.location.search.substr(1).match(reg);
                    if (r != null) {
                        return decodeURI(r[2])
                    }
                    return null;
                },
                handleInitPageQueryParams() {
                    this.operKey = this.getQueryString('operKey')
                    this.visitID = this.getQueryString('visitID')
                    this.huaWeiObs = this.getQueryString('domain')
                    console.log(this.getQueryString('maintainReviewRight'))
                    this.maintainReviewRight = this.getQueryString('maintainReviewRight') === 'true'
                },
                getMaintainContent(params) {
                    $.ajax({
                        url: '/api/VisitRecord/getMaintainInfo',
                        type: 'POST',
                        data: JSON.stringify(params),
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(res => {
                        this.visitTime = res.data.visit_time
                        this.page_oper_name = res.data.page_oper_name
                        this.handleVisitInfo(JSON.parse(res.data.maintain))
                    })
                },
                handleVisitInfo(visitMaintainInfo) {
                    visitMaintainInfo.forEach(maintainItem=>{
                        let maintainInfoItem = {}
                        maintainInfoItem.MonthMaintainTimes = maintainItem.month_maintain_times
                        maintainInfoItem.MaintainIntervalDays = maintainItem.maintain_interval_days
                        maintainInfoItem.dispSheetID = maintainItem.disp_sheet_id
                        maintainInfoItem.dispSheetNo = maintainItem.sheet_no
                        maintainInfoItem.clientID = maintainItem.client_id
                        maintainInfoItem.sumMaintainID = maintainItem.sum_maintain_id//
                        maintainInfoItem.maintainTimes = maintainItem.maintain_times == null ? 0 : maintainItem.maintain_times
                        maintainInfoItem.dispTemplateID = maintainItem.disp_template_id
                        maintainInfoItem.dispTemplateName = maintainItem.disp_template_name//模板名
                        maintainInfoItem.supcustName = maintainItem.sup_name//供应商
                        maintainInfoItem.operName = maintainItem.oper_name//业务员
                        maintainInfoItem.happenTime = maintainItem.happen_time == null ? "" : maintainItem.happen_time
                        maintainInfoItem.maintainID = maintainItem.maintain_id//
                        maintainInfoItem.reviewComment = maintainItem.review_comment//维护复核内容
                        maintainInfoItem.reviewTime = maintainItem.review_time == null ? "" : maintainItem.review_time
                        maintainInfoItem.reviewer = maintainItem.reviewer
                        maintainInfoItem.reviewerName = maintainItem.reviewer_name
                        maintainInfoItem.reviewRefused = maintainItem.review_refused
                        if (maintainItem.maintain_need_review == true && maintainItem.reviewer == null){
                            maintainInfoItem.status = '未维护'//未维护
                        }else if(maintainItem.maintain_need_review == true && maintainItem.reviewer !=null && maintainItem.review_refused == true) {
                            maintainInfoItem.status = '维护不通过'//维护不通过
                        } else if (maintainItem.maintain_need_review == true && maintainItem.reviewer != null && (maintainItem.review_refused == false || maintainItem.review_refused ==null)) {
                            maintainInfoItem.status = '维护通过'//维护通过
                        }else{
                            maintainInfoItem.status = '不需要维护'//不需要维护
                        }
                        maintainInfoItem.maintainPhoto = []//维护照片
                        maintainInfoItem.maintainBrief = []//维护备注
                        if (maintainItem.maintain_work_content != undefined && maintainItem.maintain_work_content.length !=0){
                            maintainItem.maintain_work_content.forEach(maintainAction => {
                                if(maintainAction.action.type=="photo"){
                                    let maintainObj = {}
                                    maintainObj.name = maintainAction.action.name
                                    maintainObj.photos = []
                                    if (maintainAction.work_content.mandatory != undefined && maintainAction.work_content.mandatory.length != 0) {
                                        maintainAction.work_content.mandatory.forEach(e => {
                                            maintainObj.photos.push({ url: this.huaWeiObs + e, type: '必选' })
                                            this.photos.push(this.huaWeiObs + e)
                                        })
                                    }
                                    if (maintainAction.work_content.optional != undefined && maintainAction.work_content.optional.length != 0) {
                                        maintainAction.work_content.optional.forEach(e => {
                                            maintainObj.photos.push({ url: this.huaWeiObs + e, type: '可选' })
                                            this.photos.push(this.huaWeiObs + e)
                                        })
                                    }
                                    maintainInfoItem.maintainPhoto.push(maintainObj)
                                }else{
                                    let maintainObj = {}
                                    maintainObj.name = maintainAction.action.name
                                    maintainObj.brief = maintainAction.work_content
                                    maintainInfoItem.maintainBrief.push(maintainObj)

                                }
                            })
                        }
                        this.maintainInfo.push(maintainInfoItem)
                   
                   })
                },
                setMaintainStatus(item, status) {
                    let review_refused = ''
                    if (status == 1) {
                        
                        review_refused = false
                    } else {
                        review_refused = true
                    }
                    let params = {
                        operKey: this.operKey,
                        review_refused: review_refused,
                        review_comment: item.reviewComment,
                        maintain_id: item.maintainID.toString(),
                        client_id: item.clientID.toString(),
                        disp_temp_id: item.dispTemplateID.toString(),
                        disp_sheet_id: item.dispSheetID.toString(),
                        sum_maintain_id: item.sumMaintainID == null ? "" : item.sumMaintainID,
                        visit_time: this.visitTime,
                        visit_id:this.visitID,
                        maintain_times: item.maintainTimes == null ? 0 : item.maintainTimes,
                        disp_sheet_no: item.dispSheetNo,
                        supName: item.supcustName
                    }
                    console.log(params)
                    $.ajax({
                        url: '/AppApi/SheetVisit/UpdateDisplayMaintainActionReview',
                        type: 'POST',
                        data: JSON.stringify(params),
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(res => {
                        console.log(res)
                        if (res.result === 'OK') {
                            item.reviewer = res.operID
                            item.reviewTime = res.currentTime
                            item.reviewRefused = params.review_refused
                            item.status = params.review_refused ? "维护不通过" : "维护通过"
                            item.reviewerName = this.page_oper_name
                            item.maintainTimes = item.maintainTimes + 1
                            this.$message({
                                message: '复核完成',
                                type: 'success'
                            });
                        } else {
                            this.$message.error('复核失败，请重试。或者联系管理员');
                        }
                    }).catch(() => {
                        this.$message.error('复核失败，请重试。或者联系管理员');
                    })
                }
            },
            async created() {
                this.handleInitPageQueryParams()
                var params =  {operKey: this.operKey,visitID:this.visitID}
                await this.getMaintainContent(params)
            },

        })
    </script>
</body>
</html>