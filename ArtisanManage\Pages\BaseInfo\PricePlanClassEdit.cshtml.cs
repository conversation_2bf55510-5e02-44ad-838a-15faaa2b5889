﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class PricePlanClassEditModel : PageFormModel
    { 
        public PricePlanClassEditModel(CMySbCommand cmd) : base(Services.MenuId.infoPrice)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"class_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"class_name",new DataItem(){Title="类名",Necessary=true,FldArea="divHead",}},
                {"order_index",new DataItem(){Title="顺序号",FldArea="divHead",SaveToDB=false,Hidden = true}},
                {"coverAll",new DataItem(){FldArea="divHead",Title="同时更新单独设置的折扣",CtrlType="jqxCheckBox",GetFromDb=false,SaveToDB=false}},
            };
            m_idFld = "class_id"; m_nameFld = "class_name";
            m_tableName = "info_item_class"; SaveMainTable = false;
            m_selectFromSQL = "from info_item_class left join (select class_id as my_mother_id,class_name as mother_name from info_item_class where company_id =~COMPANY_ID) tb_mother on info_item_class.mother_id=tb_mother.my_mother_id left join info_item_brand on info_item_class.brand_id=info_item_brand.brand_id where class_id='~ID'";

            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridUnit" ,new FormDataGrid(){
                   AutoAddRow=false,
                   AllowDragRow=true,
                   AllowInsertRemoveRow=true,
                   Height=200,//MinRows=6,
                   Columns = new Dictionary<string, DataItem>()
                   {
                       //{"plan_id",new DataItem(){Title="价格方案编号",SqlFld="pi.plan_id",Hidden=true} },
                       //{"plan_name",new DataItem(){Title="价格方案",SqlFld="pi.plan_name", Width="180",SaveToDB=false,Necessary=true} },
                       {"plan_id",new DataItem(){Title="价格方案",SqlFld="price_plan_class.plan_id",Necessary=true,LabelFld = "plan_name",Width="120",
                         SqlForOptions=@"select plan_id::text as plan_id,plan_name as plan_name from price_plan_main where price_plan_main.company_id=~COMPANY_ID"
                       } },
                       {"discount",new DataItem(){Title="折扣%",SqlFld="discount", Width="180",}},

                   },
                   TableName="price_plan_class",
                   IdFld="class_id",
                   SelectFromSQL=@"from price_plan_class 
                                    left join price_plan_main plan on price_plan_class.plan_id=plan.plan_id and plan.company_id=~COMPANY_ID 
                                    where price_plan_class.company_id=~COMPANY_ID and class_id=~ID "
                   }}
            };
        }

        public async Task OnGet()
        {
            await InitGet(cmd);

        }
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class PricePlanClassEditController : BaseController
    {
        public PricePlanClassEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            PricePlanClassEditModel model = new PricePlanClassEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value,availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            PricePlanClassEditModel model = new PricePlanClassEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            PricePlanClassEditModel model = new PricePlanClassEditModel(cmd);
            JsonResult record = await model.SaveTable(cmd, request);
            var msg = (record.Value as dynamic).msg;
            if (msg != "")
            {
                return msg;
            }
            Security.GetInfoFromOperKey((string)request.operKey, out string companyID);
            var classID = request["class_id"];
            var coverAll = request["coverAll"];
            string sql = @$"select p.item_id,s_factor,m_factor,b_factor,s_wholesale_price,m_wholesale_price,b_wholesale_price from info_item_prop p 
                                left join (select item_id,(s->>'f1') as s_factor,(s->>'f2')::numeric as s_wholesale_price,(m->>'f1') as m_factor,(m->>'f2')::numeric as m_wholesale_price,(b->>'f1') as b_factor,(b->>'f2')::numeric as b_wholesale_price
                                     from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,wholesale_price)) as json from info_item_multi_unit where company_id = {companyID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) 
                                          as errr(item_id int, s jsonb,m jsonb, b jsonb) ) mu on mu.item_id=p.item_id  where p.company_id = {companyID} and other_class like '/%{classID}%/' and (status = '1' or status is null)";
            cmd.CommandText = sql;
            dynamic items = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (items != null)
            {
                var plans = request["gridUnit"];
                sql = "";
                foreach (var item in items)
                {
                    var itemID = item.item_id;
                    var s_price = item.s_wholesale_price == "" ? "null" : item.s_wholesale_price;
                    var m_price = item.m_wholesale_price == "" ? "null" : item.m_wholesale_price;
                    var b_price = item.b_wholesale_price == "" ? "null" : item.b_wholesale_price;
                    if (s_price == "null" && b_price != "null" && item.b_factor!="")
                    {
                        s_price = Convert.ToSingle(b_price) / Convert.ToSingle(item.b_factor);
                    }
                    foreach (var plan in plans)
                    {
                        var discount = plan["discount"];
                        var planID = plan["plan_id"];
                        var dealConflict = " nothing ";
                        if (discount != "")
                        {
                            double disc = Convert.ToSingle(discount);
                            if (coverAll == "true")
                            {
                                dealConflict = $" update set s_price = round({s_price}*{disc},4),m_price = round({m_price}*{disc},2),b_price = round({b_price}*{disc},2),discount={disc} ";
                            }
                            sql += $@"insert into price_plan_item(plan_id,company_id,item_id,s_price,m_price,b_price,discount) 
                            values ({planID},{companyID},{itemID},round({s_price}*{disc},4),round({m_price}*{disc}*0.01,2),round({b_price}*{disc},2),{disc})
                              on conflict (plan_id,company_id,item_id) do {dealConflict};";
                        }
                    }
                }


                if (sql != "")
                {
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                }

            }

            return new JsonResult(new { result = "OK", msg = "" });

        }
    }
}