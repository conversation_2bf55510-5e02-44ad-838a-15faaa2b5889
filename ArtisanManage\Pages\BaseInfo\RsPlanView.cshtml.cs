﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class RsPlanViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;

        public RsPlanViewModel(CMySbCommand cmd) : base(Services.MenuId.rsPlan)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                 {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称",UseJQWidgets=false, SqlFld="plan_name",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     IdColumn="i",TableName="rs_plan",
                     ShowContextMenu=true,
                     ContextMenuHTML="<ul><li id='edit'>编辑</li></ul>",
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"i",new DataItem(){Title="编号", Width="80",SqlFld = "plan_id",Hidden=true,HideOnLoad = true}},
                       {"plan_name",new DataItem(){Title="方案名称", Width="180",Linkable=true}},
                       {"brand_name",new DataItem(){Title="关联品牌",Width="180",Hidden=true}},
                       {"sheet_sync",new DataItem(){Title="是否同步票据",Width="180",SqlFld="(case sheet_sync when true then '是' else '否' end) " } },
                       {"share",new DataItem(){Title="是否同步数据",Width="180",SqlFld="(case share when true then '是' else '否' end) " } },
                       {"client_mapper",new DataItem(){Title="数据同步方式",Width="180",SqlFld="( CASE WHEN client_mapper = 'resellerAsClient' THEN '分销商对应客户'  WHEN client_mapper = 'sellerAsClient' THEN '业务员对应客户' end)" } }
                     },
                     QueryFromSQL="from rs_plan where company_id= ~COMPANY_ID" ,QueryOrderSQL="order by plan_id"
                  }
                }
            };
        }
        public async Task OnGet(string forSelect)
        {
            await InitGet(cmd);
            ForSelect = forSelect == "1";
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }

        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            cmd.CommandText = $"select company_id from rs_seller where plan_id in ({rowIDs}) and company_id={company_id} limit 1";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该方案在使用中,无法删除";
            return "";
        }
    }




    [Route("api/[controller]/[action]")]
    public class RsPlanViewController : QueryController
    {
        public RsPlanViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            RsPlanViewModel model = new RsPlanViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            RsPlanViewModel model = new RsPlanViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            RsPlanViewModel model = new RsPlanViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd, "rs_plan");
            return records;
        }
    }
}
