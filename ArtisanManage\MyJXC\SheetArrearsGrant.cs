﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using NPOI.SS.Formula.Functions;
using ArtisanManage.Services.SheetService;
using ArtisanManage.MyCW;
using OBS.Model;
using ArtisanManage.Pages;
using ArtisanManage.WebAPI;
using ArtisanManage.YingJiangCommon.Controller.PromotionController;
using NPOI.HPSF;
using System.ComponentModel.Design;

namespace ArtisanManage.MyJXC
{

    public class SheetRowArrearsBill : SheetRowBase
    {
        //[SaveToDB][FromFld] public string flow_id { get; set; }
        [SaveToDB][FromFld] public string business_sheet_id { get; set; }
        [SaveToDB][FromFld] public string business_sheet_no { get; set; }
        [SaveToDB][FromFld] public string business_sheet_type { get; set; }
        [SaveToDB][FromFld] public string bill_id { get; set; } = "";
        [SaveToDB][FromFld] public string keeper_id { get; set; } = "";
        [SaveToDB][FromFld] public string old_status { get; set; } = "";
        public string business_sheet_type_name { get { return business_sheet_type.Replace("X", "销售单").Replace("T", "销售退货单").Replace("YS", "预收款单").Replace("ZC", "费用支出单").Replace("YF", "预付"); } }
        // [SaveToDB][FromFld] public decimal total_amount { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string keeper_name { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string business_happen_time { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string business_approve_time { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string business_make_time { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string out_company { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string supcust_name { get; set; }
        [SaveToDB][FromFld] public decimal left_amount { get; set; }
		[SaveToDB][FromFld] public decimal orig_amount { get; set; }
        // [SaveToDB][FromFld] public decimal left_amount { get; set; }
    }

    public enum SHEET_ARREARS_GRANT
    {
        EMPTY,
        IS_GRANT,
        IS_REVOKE
    }
    public class SheetArrearsGrant : SheetBase<SheetRowArrearsBill>
    {
        public List<string> appendixPhotos { get; set; } = new List<string>();
        public string appendix_photos { get; set; } = "";

        // [FromFld(LOAD_PURPOSE.SHOW)] public string maker_name { get; set; } = "";
        // [SaveToDB][FromFld] public string maker_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string worker_name { get; set; } = "";
        [SaveToDB][FromFld] public string worker_id { get; set; } = "";
        [SaveToDB][FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB][FromFld] public override decimal total_amount { get; set; }
     

        // [FromFld(LOAD_PURPOSE.SHOW)] public override string print_count { get; set; } = "";

        [SaveToDB]
        [FromFld]
        public virtual string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                if (appendix_photos != "" && appendix_photos != "[]")
                {
                    sheetAttribute.Add("appendixPhotos", appendix_photos);
                }


                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);

                return s;
            }
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);

                    if (sheetAttr.appendixPhotos != null)
                    {
                        this.appendix_photos = sheetAttr.appendixPhotos;
                    }
                }
            }
        }
        public SheetArrearsGrant(SHEET_ARREARS_GRANT sheetArrearsGrant, LOAD_PURPOSE loadPurpose) : base("sheet_move_arrears_bill_main", "sheet_move_arrears_bill_detail", loadPurpose)
        {
            sheet_type = sheetArrearsGrant == SHEET_ARREARS_GRANT.IS_GRANT ? SHEET_TYPE.SHEET_ARREARS_GRANT : SHEET_TYPE.SHEET_ARREARS_REVOKE;
            ConstructFun();
        }
        private void ConstructFun()
        {

            MainLeftJoin += @$"
                               left join (select oper_id,oper_name as approver_name from info_operator where company_id=~COMPANY_ID) approver on t.approver_id=approver.oper_id
                               left join (select oper_id,oper_name as worker_name from info_operator where company_id=~COMPANY_ID) worker on t.worker_id=worker.oper_id
                               left join (select oper_id,oper_name as maker_name from info_operator where company_id=~COMPANY_ID) maker on t.maker_id=maker.oper_id";

            DetailLeftJoin = @$"left join(
select sheet_id ,sheet_type, company_id, total_amount, disc_amount, paid_amount,approve_time as business_approve_time,happen_time as business_happen_time,make_time as business_make_time, red_flag,money_inout_flag
from sheet_sale_main 
where 
company_id =  ~COMPANY_ID AND
red_flag is null AND
approve_time is not null
UNION
select sheet_id, sheet_type, company_id, total_amount, disc_amount, paid_amount,approve_time as business_approve_time,happen_time as business_happen_time,make_time as business_make_time, red_flag,money_inout_flag
from sheet_prepay
where 
sheet_type = 'YS' AND
company_id =  ~COMPANY_ID AND
red_flag is null AND
approve_time is not null
UNION
select sheet_id, sheet_type, company_id, total_amount, disc_amount, paid_amount,approve_time as business_approve_time, happen_time as business_happen_time,make_time as business_make_time,red_flag,money_inout_flag
from sheet_fee_out_main
where 
company_id =  ~COMPANY_ID AND
red_flag is null AND
approve_time is not null
)sm  on sm.sheet_id = t.business_sheet_id and sm.sheet_type = t.business_sheet_type 
                                left join (select oper_name as keeper_name, oper_id from info_operator) keeper on t.keeper_id = keeper.oper_id
                                left join (select supcust_name,out_company,bill_id from arrears_bill) ab on t.bill_id = ab.bill_id";

        }

        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            if (maker_id == "") return "必须指定发放人";
            //if (getter_id == "" && IsFromWeb) return "必须指定业务员";

            if (SheetRows.Count == 0) return "单据不存在明细行";

            decimal row_total_amount = 0;

            foreach (SheetRowArrearsBill row in SheetRows)
            {
                dynamic rowInfo = await CDbDealer.Get1RecordFromSQLAsync($"select out_company from arrears_bill where bill_id = {row.bill_id} and company_id = {company_id}", cmd);
                bool outCompany = ((string)row.out_company).ToLower() == "false" ? false : true;
                if (sheet_type == SHEET_TYPE.SHEET_ARREARS_GRANT && outCompany)
                {
                    return "无法发放不在公司内的欠条，请刷新欠条管理界面";
                }
                else if (sheet_type == SHEET_TYPE.SHEET_ARREARS_REVOKE && !outCompany)
                {
                    return "无法回收公司内部的欠条，请刷新欠条管理界面";
                }
                row_total_amount += row.left_amount;
                row.keeper_id = worker_id;

                // 审核时更新old_status
                if (red_flag.IsInvalid())
                {
                    row.old_status = row.out_company;
                }
                //if (!IsImported && row.now_pay_amount / row.sheet_amount < 0 )
                //{
                //   if( row.sheet_amount > 0)  return $"单据{row.mm_sheet_no}的本次支付金额必须是正数";
                //   else return $"单据{row.mm_sheet_no}的本次支付金额必须是负数";
                //}
                /*if (!IsImported && row.now_disc_amount * row.sheet_amount < 0)
                {
                   if (row.sheet_amount > 0) return $"单据{row.mm_sheet_no}的本次优惠金额必须是正数"; 
                   else return $"单据{row.mm_sheet_no}的本次优惠金额必须是负数";
                }*/
            }

            if (Math.Abs(total_amount - row_total_amount) > 0.1m) return "明细行合计与表单总计不等";
             

            return "OK";
        }

        protected override async Task<string> CheckSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSheetValid(cmd);
            if (check != "OK") return check;


            List<SheetRowArrearsBill> lstToRemove = new List<SheetRowArrearsBill>();



            foreach (var row in lstToRemove)
            {
                SheetRows.Remove(row);
            }

            return "OK";
        }
         
        class CInfoForApprove : CInfoForApproveBase
        {
            public string msg = "";
        }
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            string updateArrearsBillSql = "";
            string outCompany = sheet_type == SHEET_TYPE.SHEET_ARREARS_GRANT ? "true" : "false";
             
           // if (red_flag.IsValid())
           // {
           //     outCompany = outCompany == "true" ? "false" : "true";
           // }
            foreach (SheetRowArrearsBill row in SheetRows)
            {
                string billStatus = "";
                string setLastRevokeTime = "";
                string saleSheetUpdateSql = "";
                // 销售单的状态更新？？
                if (red_flag.IsValid())
                {
                    // 回退
                    if (sheet_type == SHEET_TYPE.SHEET_ARREARS_REVOKE)
                    {
                        string lastRevokeSheet = @$"select m.approve_time from sheet_move_arrears_bill_main m left join sheet_move_arrears_bill_detail d
                            on d.sheet_id = m.sheet_id and d.company_id = m.company_id 
                            where m.company_id = {company_id} and d.bill_id = {row.bill_id} and sheet_type = 'QTS'  and m.sheet_id not in ({red_sheet_id},{sheetID}) and m.red_flag is null and m.approve_time is not null
                            order by m.sheet_id desc ";
                        dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(lastRevokeSheet, cmd);
                        if (ret != null)
                        {
                            setLastRevokeTime = $",last_revoke_time = '{ret.approve_time}'";
                        }
                        else
                        {
                            setLastRevokeTime = $",last_revoke_time = null";
                        }
                        
                    }
                    if (string.IsNullOrEmpty(row.old_status))
                    {
                        billStatus = "null";
                        // 只有这种情况把salesheet的iou_get_time抹掉
                        saleSheetUpdateSql = @$"update sheet_sale_main set iou_get_time = null
                            where company_id = {company_id} and sheet_id = {row.business_sheet_id} and sheet_type = '{row.business_sheet_type}';";

                    }
                    else
                    {
                        billStatus = ((string)row.old_status).ToLower();
                    }

                    updateArrearsBillSql += $"update arrears_bill set out_company = {billStatus},keeper_id = {OperID}{setLastRevokeTime} where company_id = {company_id} and business_sheet_type = '{row.business_sheet_type}' and business_sheet_id={row.business_sheet_id};{saleSheetUpdateSql}";
                }
                else
                {
                    // 领用人转为持有人
                    
                    if (sheet_type == SHEET_TYPE.SHEET_ARREARS_REVOKE)
                    {
                        setLastRevokeTime =$",last_revoke_time = '{approve_time}'";
                    }
                    if (row.out_company == "")
                    {
                        saleSheetUpdateSql = $"update sheet_sale_main set iou_get_time = '{approve_time}' where company_id = {company_id} and sheet_id = {row.business_sheet_id} and sheet_type = '{row.business_sheet_type}';";
                    }
                    updateArrearsBillSql += @$"update arrears_bill set out_company = {outCompany},keeper_id = {worker_id} {setLastRevokeTime} where company_id = {company_id} and business_sheet_type = '{row.business_sheet_type}' and business_sheet_id={row.business_sheet_id};{saleSheetUpdateSql}";
                }
                
            }
            cmd.CommandText = updateArrearsBillSql;
            await cmd.ExecuteNonQueryAsync();
        }
        protected override async Task<string> CheckForRed(CMySbCommand cmd)
        {
            string msg = "";
            dynamic va = this;
            // 欠条的红冲涉及到欠条状态变化，红冲时检查本单据包含的明细有没有出现在时间更靠后的发放/回收单里
            #region 检查明细欠条最新的发放回收记录
            foreach (SheetRowArrearsBill row in SheetRows)
            {
                string querySql = @$"select m.sheet_id,m.sheet_no,m.approve_time from sheet_move_arrears_bill_detail d left join sheet_move_arrears_bill_main m
                        on d.sheet_id = m.sheet_id and d.company_id = m.company_id 
                        where d.bill_id = {row.bill_id} and m.red_flag is null and m.approve_time is not null 
                        and m.approve_time > '{approve_time}' and m.company_id = {company_id} order by m.sheet_id desc limit 1";
                dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);
                if (ret != null)
                {
                    msg = $"最新单据【{ret.sheet_no}】包含第{row.row_index}行明细对应的欠条，无法红冲当前单据，请先红冲最新单据";

                }
            }
            #endregion
            return msg;
        }
 
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.sheet_id + "_" + this.worker_id + "_";
            foreach (var row in SheetRows)
            {
                res += row.bill_id + "_";
            }
            return res;
        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
        }

        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting);

        }
    }
}
