﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class SalesSummaryBySenderModel : PageQueryModel
    { 
        public SalesSummaryBySenderModel(CMySbCommand cmd) : base(Services.MenuId.salesSummaryBySender)
        {
            //new added
           
            this.UsePostMethod = true;
            this.cmd = cmd;
           
            this.PageTitle = "销售汇总(送货员)";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time+sd.happen_time",CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time+sd.happen_time",CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"status",new DataItem(){FldArea="divHead",  Title="单据状态", ButtonUsage = "list",CompareOperator="=",Value="approved",Label="已审核",
                        Source = @"[{v:'normal',l:'所有',condition:""sm.red_flag is null""},
                                   {v:'unapproved',l:'未审核',condition:""sm.approve_time is null""},
                                   {v:'approved',l:'已审核',condition:""sm.approve_time is not null and sm.red_flag is null""}]"
                }},
                {"trade_type",new DataItem(){FldArea="divHead",Checkboxes=true, Title="交易类型",ButtonUsage = "list",CompareOperator="=",Value="",Label="",
                        Source = @"[{v:'ALL',l:'所有',condition:""true""},
                                    {v:'X',l:'销售',condition:""coalesce(trade_type,'X')='X' and sd.quantity*sd.inout_flag<0""},
                                   {v:'T',l:'退货',condition:""coalesce(trade_type,'X')='T' and sd.quantity*sd.inout_flag>0""},
                                   {v:'XT',l:'销退',condition:""coalesce(trade_type,'X') in ('X','T','XD','TD')""},
                                   {v:'DH',l:'定货还货',condition:""trade_type='DH'""},
                                   {v:'JH',l:'借还货',condition:""trade_type in ('J','H')""},
                                   {v:'CL',l:'陈列兑付',condition:""trade_type ='CL'""},
                                   {v:'HH',l:'换货',condition:""trade_type in ('HR','HC')""}]"
                }},
                {"arrears_status",new DataItem(){FldArea="divHead",Title="欠款情况",Hidden = true, Checkboxes=true, ButtonUsage = "list",CompareOperator="=",
                    Source = @"[{v:'cleared',l:'已结清',condition:""abs(total_amount-paid_amount-disc_amount)<0.1""},
                                 {v:'uncleared',l:'未结清',condition:""abs(total_amount-paid_amount-disc_amount)>0.1""},
                                 {v:'all',l:'所有',condition:""true""}]"
                }},
                 {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="list",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="sd.item_id",DropDownWidth="300",
              SearchFields=CommonTool.itemSearchFields,
				SqlForOptions =CommonTool.selectItemWithBarcode   }},
				 
				{"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){SqlFld="ip.item_brand"})},
				{"class_id",new DataItem(){Title="类别",FldArea="divHead",LabelFld="class_name",MaxRecords="1000",ButtonUsage="list",CompareOperator="like",SqlFld="ip.other_class",
                    SqlForOptions ="select class_id as v,class_name as l from info_item_class order by order_index,class_name,class_id"}},
                {"senders_id",new DataItem(){Title="送货员",FldArea="divHead",LabelFld="sender_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.senders_id",SqlForOptions=CommonTool.selectSenders } },
                
                {"seller_id",new DataItem(){FldArea="divHead",Title="管理员",Checkboxes=false, LabelFld="senders_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSenders,CompareOperator="="}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                {"depart_path",new DataItem(){Title="部门",FldArea="divHead",LabelFld="depart_path_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", TreePathFld="depart_path",CompareOperator="like",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                 {"department_id",new DataItem(){Title="所属部门",TreePathFld="department_path",Hidden=true, FldArea="divHead",LabelFld="department_id_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=false,DropDownWidth="150", CompareOperator="=",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'2',l:'加权平均价'},{v:'3',l:'预设进价'},{v:'1',l:'预设成本'}]", CompareOperator="=" }},
                {"showRebateProfit",new DataItem(){FldArea="divHead",Title="显示补差后利润",CtrlType="jqxCheckBox",Hidden=true,ForQuery=false,Value="false"}},
                {"sheetType",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,HideOnLoad = true} },
                //{"detail_table",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,} }
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, Sortable=true,PageByOverAgg=false,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       // {"oper_id",    new DataItem(){Title="送货员",  Width="150",SqlFld="sm.senders_id",Hidden=true}},
                       // {"oper_name",    new DataItem(){Title="送货员名称", Sortable=true,Linkable=true,   Width="300",SqlFld="sm.senders_name" }},
                       {"oper_id",    new DataItem(){Title="送货员",  Width="150",SqlFld="oper_id",Hidden=true}},
                       {"oper_name",    new DataItem(){Title="送货员名称", Pinned=true, Sortable=true, Linkable=true,   Width="300",SqlFld="oper_name" }},
                       {"x_quantity",   new DataItem(){Title="销售量", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="ROUND(SUM(x_quantity)::NUMERIC, 2)",ShowSum=true,Hidden=true}},
                       {"t_quantity",   new DataItem(){Title="退货量", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="ROUND(SUM(t_quantity)::NUMERIC, 2)",ShowSum=true,Hidden=true}},
                       {"net_quantity", new DataItem(){Title="净销量", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="ROUND(SUM(net_quantity)::NUMERIC, 2)",ShowSum=true,Hidden=true}},
                       {"x_quantity_b",   new DataItem(){Title="销售量(大)", Sortable=true, CellsAlign="right",  Width="150",SqlFld="ROUND(SUM(x_quantity_b)::NUMERIC, 2)",ShowSum=true,Hidden=true}},
                       {"t_quantity_b",   new DataItem(){Title="退货量(大)", Sortable=true, CellsAlign="right",  Width="150",SqlFld="ROUND(SUM(t_quantity_b)::NUMERIC, 2)",ShowSum=true,Hidden=true}},

                       {"net_quantity_b",   new DataItem(){Title="净销量(大)", Sortable=true, CellsAlign="right",  Width="150",SqlFld="ROUND(SUM(net_quantity_b)::NUMERIC, 2)",ShowSum=true,Hidden=true}},


                       {"free_quantity", new DataItem(){Title="赠品量", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="ROUND(SUM(free_quantity)::NUMERIC, 2)",ShowSum=true,Hidden=true}},
                       {"rebate_quantity",   new DataItem(){Title="补差数量", Sortable=true,  CellsAlign="right",  Width="100",SqlFld="ROUND(SUM(rebate_quantity)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"x_amount",     new DataItem(){Title="销售金额", Sortable=true, CellsAlign="right",  Width="100",SqlFld="ROUND(SUM(x_amount)::NUMERIC, 2)",ShowSum=true}},
                       {"t_amount",     new DataItem(){Title="退货金额", Sortable=true, CellsAlign="right",  Width="100",SqlFld="ROUND(SUM(t_amount)::NUMERIC, 2)",ShowSum=true}},
                       {"net_amount",   new DataItem(){Title="销售净额", Sortable=true, CellsAlign="right",  Width="100",SqlFld="ROUND(SUM(net_amount)::NUMERIC, 2)",ShowSum=true}},
                       {"net_rebate_amount",   new DataItem(){Title="销售净额(补差后)", Sortable=true,Hidden=true, CellsAlign="right",  Width="150",SqlFld="ROUND(SUM(net_rebate_amount)::NUMERIC, 2)",ShowSum=true}},
                       {"disc_amount",  new DataItem(){Title="优惠金额", Sortable=true, CellsAlign="right",     Width="100",SqlFld="ROUND(SUM(disc_amount)::NUMERIC, 2)",ShowSum=true}},
                       {"total_weight",  new DataItem(){Title="总重量", Sortable=true, CellsAlign="right",     Width="100",SqlFld="ROUND(SUM(total_weight)::NUMERIC, 2)",ShowSum=true}},

                     },

                                          QueryFromSQL=@"
FROM
(

	SELECT oper_id AS oper_id, CASE WHEN oper_name IS NULL THEN '未指定' ELSE oper_name END AS oper_name,
    ROUND
    (
        SUM(CASE WHEN quantity * money_inout_flag > 0 THEN quantity * sd.unit_factor ELSE 0 END ) :: NUMERIC
        /
        COALESCE(ARRAY_LENGTH( REGEXP_SPLIT_TO_ARRAY( sm.senders_id, ',' ), 1 ),1) :: NUMERIC
        ,2
    ) :: NUMERIC AS x_quantity,

 
    ROUND
    (
        SUM(CASE WHEN quantity * money_inout_flag > 0 THEN quantity * sd.unit_factor /t.b_unit_factor ELSE 0 END ) :: NUMERIC
        /
        COALESCE(ARRAY_LENGTH( REGEXP_SPLIT_TO_ARRAY( sm.senders_id, ',' ), 1 ),1) :: NUMERIC 
        ,2          

    ) :: NUMERIC AS x_quantity_b,

    round( SUM ( CASE WHEN quantity * money_inout_flag < 0 THEN quantity * sd.unit_factor ELSE 0 END ) :: NUMERIC, 2 ) AS t_quantity,


    round( SUM ( CASE WHEN quantity * money_inout_flag < 0 THEN quantity * sd.unit_factor  /t.b_unit_factor ELSE 0 END ) :: NUMERIC, 2 ) AS t_quantity_b,


    round( SUM ( quantity * sd.unit_factor * inout_flag * ( - 1 ) ) :: NUMERIC, 2 ) AS net_quantity,

     round( SUM ( quantity * sd.unit_factor * inout_flag * ( - 1 )/t.b_unit_factor ) :: NUMERIC, 2 ) AS net_quantity_b,

    round( SUM ( case when coalesce(rebate_price, 0) <> 0 then quantity*sd.unit_factor else 0 end)::numeric,2) as rebate_quantity,
    round
    (
        SUM 
        (
            CASE WHEN sd.sub_amount = 0 AND COALESCE ( trade_type, 'X' ) NOT IN ( 'CL', 'H', 'J' ) THEN
			        sd.quantity * sd.unit_factor * sd.inout_flag * ( - 1 ) ELSE 0
	        END
        ) :: NUMERIC,2
    ) AS free_quantity,
    ROUND
    (
        SUM ( CASE WHEN quantity * sd.inout_flag < 0 THEN inout_flag * ( - 1 ) * sd.sub_amount ELSE 0 END ) :: NUMERIC, 2
    ) / COALESCE(ARRAY_LENGTH( REGEXP_SPLIT_TO_ARRAY( sm.senders_id, ',' ), 1 ),1) :: NUMERIC AS x_amount,
    ROUND
    (
        SUM ( CASE WHEN sd.quantity * sd.inout_flag > 0 THEN sd.sub_amount * sd.inout_flag ELSE 0 END ) :: NUMERIC,2
    ) AS t_amount,
    ROUND ( SUM ( sub_amount * inout_flag * ( - 1 ) ) :: NUMERIC, 2 ) AS net_amount,
    ROUND( SUM ( sub_amount * inout_flag * ( - 1 ) - coalesce(rebate_price, 0)*quantity*sd.unit_factor ) :: NUMERIC, 2 ) AS net_rebate_amount,
    ROUND
    (
        SUM ( DISTINCT ( round( now_disc_amount :: NUMERIC, 6 ) :: TEXT || sm.sheet_id :: TEXT || '1' ) :: NUMERIC ), 2
    ) AS disc_amount,
    ROUND(SUM(quantity*sd.unit_factor * coalesce(sd.rebate_price,0) ) :: NUMERIC,2)  AS rebate_price,
	  
    ROUND
    (
	    SUM (CASE WHEN COALESCE ( trade_type, 'X' ) <> 'CL' THEN quantity * sd.unit_factor * inout_flag * ( - 1 ) * ~VAR_cost_price_fld ELSE 0 END) :: NUMERIC,2
    ) AS cost_amount_hasfree,
    ROUND
    (
        SUM( case when coalesce(trade_type,'X') <>'J' then  quantity*sd.unit_factor*inout_flag*(-1)*~VAR_cost_price_fld else 0 end ) :: NUMERIC, 2
    ) as cost_amount_hasfree_cl_jh,
    ROUND(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round( SUM ( case when coalesce(trade_type,'X') <>'J' then  quantity*sd.unit_factor*inout_flag*(-1)*~VAR_cost_price_fld else 0 end ) :: NUMERIC, 2 )
    as profit_hasfree_cl_jh,
    ROUND
    (
        (
            (ROUND(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round(sum( case when coalesce(trade_type,'X') <>'J' then quantity*sd.unit_factor*inout_flag*(-1)*~VAR_cost_price_fld else 0 end )::numeric,2) )*100
             /
            (
                case when ROUND(sum(inout_flag*(-1)*sub_amount)::numeric,2) <>0
                     then ROUND(sum(inout_flag*(-1)*sub_amount)::numeric,2)
                     else null
                end
            ) :: NUMERIC
        ):: NUMERIC, 2
    )  as profit_rate_hasfree_cl_jh,
    ROUND( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 )
    - round(
        SUM (CASE WHEN COALESCE ( trade_type, 'X' ) <> 'CL' THEN quantity * sd.unit_factor * inout_flag * ( - 1 ) * ~VAR_cost_price_fld ELSE 0 END) :: NUMERIC,2
    ) AS profit_hasfree,
    ROUND
    (
        (
            (
                (
                    round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 )
                  - round(SUM (CASE WHEN COALESCE ( trade_type, 'X' ) <> 'CL' THEN quantity * sd.unit_factor * inout_flag * ( - 1 ) * ~VAR_cost_price_fld ELSE 0 END) :: NUMERIC,2)
                ) * 100
            ) /
            (
                CASE WHEN round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) <> 0 THEN
                                round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) ELSE NULL END
            ) :: NUMERIC
        ) :: NUMERIC,2
    ) AS profit_rate_hasfree,
    ROUND( SUM ( quantity * sd.unit_factor * inout_flag * ( - 1 ) * ~VAR_cost_price_fld ) :: NUMERIC, 2 )
        AS cost_amount_free_cl,
    ROUND( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 )
    - ROUND( SUM ( quantity * sd.unit_factor * inout_flag * ( - 1 ) * ~VAR_cost_price_fld ) :: NUMERIC, 2 )
    AS profit_free_cl,
    ROUND
    (
	    (
		    (
		       round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) - round( SUM ( quantity * sd.unit_factor * inout_flag * ( - 1 ) * ~VAR_cost_price_fld ) :: NUMERIC, 2 )* 100
		    ) / 
		    (
		       CASE WHEN ROUND( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) <> 0 THEN round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) ELSE NULL END
		    ) :: NUMERIC
	    ) :: NUMERIC,2
    ) AS profit_rate_free_cl,
    ROUND(SUM ( ( CASE WHEN sub_amount = 0 THEN - quantity * sd.unit_factor * inout_flag * ~VAR_cost_price_fld ELSE 0 END ) ) :: NUMERIC,2) 
    AS free_cost_amount,

    ROUND(sum((case when sub_amount=0  and (sd.trade_type='CL' or sd.remark like '%陈列%') then -quantity*sd.unit_factor*inout_flag*~VAR_cost_price_fld else 0 end))::numeric,2)
    as cl_cost_amount,
    ROUND(SUM ( CASE WHEN sub_amount <> 0 THEN - inout_flag * quantity * sd.unit_factor * ~VAR_cost_price_fld ELSE 0 END ) :: NUMERIC,2)
    AS cost_amount,
    ROUND(SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) 
    - ROUND(SUM ( CASE WHEN sub_amount <> 0 THEN - inout_flag * quantity * sd.unit_factor * ~VAR_cost_price_fld ELSE 0 END ) :: NUMERIC,2)
    AS profit,
    ROUND
    (
	    (
		    100 * 
            (
                round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 )
            - round(SUM ( CASE WHEN sub_amount <> 0 THEN - inout_flag * quantity * sd.unit_factor * ~VAR_cost_price_fld ELSE 0 END ) :: NUMERIC,2)
           )
	       / 
           (
             CASE WHEN round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) <> 0 THEN round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) ELSE NULL END
	       ) :: NUMERIC
        ) :: NUMERIC,2
    ) AS profit_rate,        
    ROUND(
            SUM(CASE WHEN sd.item_id IS NOT NULL THEN sd.quantity * sd.unit_factor * t.weight ELSE 0 END) :: NUMERIC, 2
        ) AS total_weight
    FROM
	    ~detailTable sd
	LEFT JOIN ~mainTable sm ON sm.sheet_id = sd.sheet_id AND sd.company_id = ~COMPANY_ID
	LEFT JOIN info_operator o ON o.company_id = ~COMPANY_ID AND o.oper_id = ANY ( REGEXP_SPLIT_TO_ARRAY( senders_id, ',' ) :: INT [] )
	LEFT JOIN info_item_prop ip ON sd.item_id = ip.item_id AND ip.company_id = ~COMPANY_ID

    LEFT JOIN
    ( 

        SELECT us.item_id,us.unit_no s_unit_no, us.barcode s_barcode,
                            ub.unit_no b_unit_no, ub.barcode b_barcode,  ub.unit_factor b_unit_factor,
                            um.unit_no m_unit_no, um.barcode m_barcode,  um.unit_factor m_unit_factor,us.weight
        FROM      info_item_multi_unit us
        LEFT JOIN info_item_multi_unit ub on us.item_id=ub.item_id and ub.unit_type='b' and ub.company_id=~COMPANY_ID  
        LEFT JOIN info_item_multi_unit um on us.item_id=um.item_id and um.unit_type='m' and um.company_id=~COMPANY_ID  
        WHERE us.company_id=~COMPANY_ID  and us.unit_type='s' 
    ) t on sd.item_id=t.item_id  
    WHERE
	    sm.company_id = ~COMPANY_ID
	    AND sm.seller_id IS NOT NULL  ~VAR_IS_DEL
	    AND ~QUERY_CONDITION
    GROUP BY
	    oper_id,
	    oper_name,
	    sm.senders_id
) t
GROUP BY
oper_id,
oper_name
",
/*                     QueryGroupBySQL = " group by oper_id,oper_name,sm.senders_id",
                     QueryOrderSQL=" order by oper_id,oper_name"*/
                  }
                }
            };
			/* quest*/
			var origCols = Grids["gridItems"].Columns;
			var cols = SalesSummaryByItemModel.GetProfitColumns(origCols,false);

           
            foreach (var k in cols)
            {
                if(k.Key!= "return_cost_amount")
                   origCols.Add(k.Key, k.Value);
            }
        }

        /*
         
           ROUND(SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 )
	    - SUM ( CASE WHEN row_index = 1 THEN inout_flag * ( - 1 ) * COALESCE ( now_disc_amount, 0 ) ELSE 0 END )
	    - round( SUM ( quantity * sd.unit_factor * inout_flag * ( - 1 ) * ~VAR_cost_price_fld ) :: NUMERIC, 2 )
	    + round(SUM (  ( CASE WHEN sub_amount = 0 AND trade_type != 'J' THEN - quantity * sd.unit_factor * inout_flag *~VAR_cost_price_fld ELSE 0 END )) :: NUMERIC, 2 )
	    AS profit_cut_disc,
		ROUND
		(
			(
				(
				   (
                       round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) 
                       - SUM ( CASE WHEN row_index = 1 THEN inout_flag * ( - 1 ) * COALESCE ( now_disc_amount, 0 ) ELSE 0 END ) 
                       - round( SUM ( quantity * sd.unit_factor * inout_flag * ( - 1 ) * ~VAR_cost_price_fld ) :: NUMERIC, 2 )
                       + round(SUM (( CASE WHEN sub_amount = 0 AND trade_type != 'J' THEN - quantity * sd.unit_factor * inout_flag *~VAR_cost_price_fld ELSE 0 END )) :: NUMERIC,2)
                   ) * 100
                ) / 
				(
				    CASE WHEN round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) <> 0 THEN round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) ELSE NULL END
				) :: NUMERIC
			) :: NUMERIC, 2
		) AS profit_rate_cut_disc,

         */
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SalesSummaryByItemModel.SetProfitColumns(this);
            var cost_price_type = DataItems["cost_price_type"].Value;


            var costPrice = "sd.cost_price_buy";//当前进价
            switch (cost_price_type)
            {
                case "3"://当前进价
                    costPrice = "sd.cost_price_buy";
                    break;
                case "2"://加权价
                    costPrice = "sd.cost_price_avg";
                    break;
                case "1"://预设成本
                    costPrice = "sd.cost_price_prop";
                    break;
            }

            SQLVariables["cost_price_fld"] = costPrice;
            var columns = Grids.GetValueOrDefault("gridItems").Columns;

			//补差额
			if (columns.ContainsKey("rebate_amount")) columns["rebate_amount"].SqlFld = "ROUND( SUM ( rebate_price ) :: NUMERIC, 2 )";
            
            //columns["profit_cut_disc"].SqlFld = $"ROUND(SUM(T.profit_cut_disc) :: NUMERIC, 2)";
            //columns["profit_rate_cut_disc"].SqlFld = $"ROUND(AVG(T.profit_rate_cut_disc) :: NUMERIC, 2)";

            /* new added*/

            /* ROUND(SUM(cost_amount_hasfree) :: NUMERIC, 2)  AS sum_cost_amount_hasfree,*/
          
            columns["cost_amount_hasfree"].SqlFld = $"ROUND( SUM ( cost_amount_hasfree ) :: NUMERIC, 2 )";
            columns["cost_amount_hasfree_cl_jh"].SqlFld = $"ROUND( SUM ( cost_amount_hasfree_cl_jh ) :: NUMERIC, 2 )";

            /*ROUND( SUM ( profit_hasfree )  :: NUMERIC, 2 ) AS sum_profit_hasfree,*/

            columns["profit_hasfree"].SqlFld = $"ROUND( SUM ( profit_hasfree )  :: NUMERIC, 2 )";
            columns["profit_hasfree_cl_jh"].SqlFld = $"ROUND( SUM ( profit_hasfree_cl_jh )  :: NUMERIC, 2 )";

            /*ROUND( AVG ( profit_rate_hasfree )  :: NUMERIC, 2 ) AS avg_profit_rate_hasfree,*/

            columns["profit_rate_hasfree"].SqlFld = $"ROUND( AVG ( profit_rate_hasfree )  :: NUMERIC, 2 )";

            /*ROUND( SUM ( cost_amount_free_cl ) :: NUMERIC, 2 )  AS sum_cost_amount_free_cl,*/
           
            columns["cost_amount_free_cl"].SqlFld = $"ROUND( SUM ( cost_amount_free_cl ) :: NUMERIC, 2 )";

            /*ROUND( SUM ( profit_free_cl ) :: NUMERIC, 2 )  AS sum_profit_free_cl,*/
          
            columns["profit_free_cl"].SqlFld = $"ROUND( SUM ( profit_free_cl ) :: NUMERIC, 2 ) ";

            /*ROUND( AVG ( profit_rate_free_cl ) :: NUMERIC, 2 ) AS avg_profit_rate_free_cl,*/
           
            columns["profit_rate_free_cl"].SqlFld = $"ROUND( AVG ( profit_rate_free_cl ) :: NUMERIC, 2 )";
            columns["profit_rate_hasfree_cl_jh"].SqlFld = $"ROUND( AVG ( profit_rate_hasfree_cl_jh ) :: NUMERIC, 2 )";

            /*ROUND( SUM ( free_cost_amount ) :: NUMERIC, 2 )  AS sum_free_cost_amount,*/

            columns["free_cost_amount"].SqlFld = $"ROUND( SUM ( free_cost_amount ) :: NUMERIC, 2 )";

            columns["cl_cost_amount"].SqlFld = $"ROUND( SUM ( cl_cost_amount ) :: NUMERIC, 2 )";

            /*ROUND( SUM ( cost_amount ) :: NUMERIC, 2 )  AS sum_cost_amount,*/

            columns["cost_amount"].SqlFld = $"ROUND( SUM ( cost_amount ) :: NUMERIC, 2 )";

            /*ROUND( SUM ( profit ) :: NUMERIC, 2 )  AS sum_profit*/
           
            columns["profit"].SqlFld = $"ROUND( SUM ( profit ) :: NUMERIC, 2 )";

            /*ROUND( AVG ( profit_rate ) :: NUMERIC, 2 )  AS avg_profit_rate*/
           
            columns["profit_rate"].SqlFld = "ROUND( AVG ( profit_rate ) :: NUMERIC, 2 )";
            var sheetType = DataItems["sheetType"].Value;
            this.SQLVariables["IS_DEL"] = "";
            if(sheetType.ToLower() == "xd")
            {
                this.SQLVariables["IS_DEL"] = "and coalesce(sm.is_del, false) = false";
            }

            /*
            var cost_price_type = DataItems["cost_price_type"].Value;

            
            var costPrice = "sd.cost_price_buy";//当前进价
            switch (cost_price_type)
            {
                case "3"://当前进价
                    costPrice = "sd.cost_price_buy";
                    break;
                case "2"://加权价
                    costPrice = "sd.cost_price_avg";
                    break;
                case "1"://预设成本
                    costPrice = "sd.cost_price_prop";
                    break;
            }

            var columns = Grids.GetValueOrDefault("gridItems").Columns;

            columns["cost_amount_hasfree"].SqlFld = $"round( SUM ( CASE WHEN trade_type !='J' THEN quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ELSE 0 END ) :: NUMERIC, 2 ) ";
            columns["profit_hasfree"].SqlFld = $"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(now_disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)";
            columns["profit_rate_hasfree"].SqlFld = @$"
cast( 
    (
(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(now_disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)	)*100
)
/
	(
		case when round(sum(sub_amount*inout_flag*(-1))::numeric,2) <>0 
		     then round(sum(sub_amount*inout_flag*(-1))::numeric,2)
					else null 
		end
	) 

as numeric)";
            columns["free_cost_amount"].SqlFld = $"round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2) ";
            columns["cost_amount"].SqlFld = $"round( SUM ( CASE WHEN trade_type !='J' THEN quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ELSE 0 END ) :: NUMERIC, 2 )-round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2)";
            columns["profit"].SqlFld = $"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(now_disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)+round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2)";
            columns["profit_rate"].SqlFld = @$"
cast( 
    (
(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(now_disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)+round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2))*100
	) /
	(
		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) <>0 
		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2) 
					else null 
		end
	) 

as numeric)";*/
        }


        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            SalesSummaryByItemModel.SetCostInfo(this);
           

        }

        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class SalesSummaryBySenderController : QueryController
    { 
        public SalesSummaryBySenderController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SalesSummaryBySenderModel model = new SalesSummaryBySenderModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpPost]
        public async Task<object> GetQueryRecords([FromBody] dynamic data )
        {
            string sheetType = data.sheetType; 
            //string cost_price_type_name,
            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";
            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }
            SalesSummaryBySenderModel model = new SalesSummaryBySenderModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            // new added
            // sql = "SELECT oper_id,oper_name,SUM ( x_quantity ) AS sum_x_quantity,round( SUM ( x_amount ), 2 ) AS sum_x_amount FROM(" + sql + "AS tempt GROUP BY oper_id,oper_name;";

            model.Grids["gridItems"].QueryFromSQL = sql;
            object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            string sParams = Request.Form["params"];
            sParams = System.Web.HttpUtility.UrlDecode(sParams);
            dynamic queryParams = JsonConvert.DeserializeObject(sParams);
            string sheetType = queryParams.sheetType;

            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";
            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }
            SalesSummaryBySenderModel model = new SalesSummaryBySenderModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            model.Grids["gridItems"].QueryFromSQL = sql;
            return await model.ExportExcel(Request, cmd, queryParams);
        }

    }
}
