## 2021-8-20 自适应打印到A4就换页了

重装驱动搞定

## 2021-9-1  httpclient访问网址问题
程序启动一段时间后，httpclient访问网址会报如下错误
The handler does not support custom handling of certificates with this combination of libcurl (7.29.0) and its SSL backend ("OpenSSL/1.0.2k"). An SSL backend based on "OpenSSL/1.0.2u" is required. Consider using System.Net.Http.SocketsHttpHandler.
升级libcurl到7.58后仍然没解决。

按如下步骤升级成功

centos7 升级 openssl 到 openssl 1.1.1
 

从源码安装openssl
#### 1. 下载并解压:

cd ~
wget https://www.openssl.org/source/openssl-1.1.1g.tar.gz
tar -zxvf openssl-1.1.1g.tar.gz
cd openssl-1.1.1g

cd ~
wget https://www.openssl.org/source/openssl-1.1.1g.tar.gz
tar -zxvf openssl-1.1.1g.tar.gz
cd openssl-1.1.1g
#### 2. 编译并安装:

./Configure linux-x86_64
make
make test
make install

./Configure linux-x86_64
make
make test
make install

运行 openssl, 可能报如下错误:

/usr/local/bin/openssl version
/usr/local/bin/openssl: error while loading shared libraries: libcrypto.so.1.1: cannot open shared object file: No such file or directory

/usr/local/bin/openssl version
/usr/local/bin/openssl: error while loading shared libraries: libcrypto.so.1.1: cannot open shared object file: No such file or directory
解决办法见第三步。

#### 3. 建立软链接 libssl:

sudo ln -s /usr/local/lib64/libssl.so.1.1 /usr/lib64/
sudo ln -s /usr/local/lib64/libcrypto.so.1.1 /usr/lib64/

sudo ln -s /usr/local/lib64/libssl.so.1.1 /usr/lib64/
sudo ln -s /usr/local/lib64/libcrypto.so.1.1 /usr/lib64/

#### 4. 链接新的 openssl

sudo ln -s /usr/local/bin/openssl /usr/bin/openssl

sudo ln -s /usr/local/bin/openssl /usr/bin/openssl
#### 5. 检查新版本:

openssl_latest version
OpenSSL 1.1.1g 21 Apr 2020



##  盘点单保存时实际数量被清零的bug
 1. 复现
   只要第一行为空，保存后，再次保存，再打开，实际数量就为空了
   
 2. 原理 save的时候会把row中real_quantity为''的delete掉。jqxgrid在getrows的时候只要某一列第一行为undefined，下面所有行的该列的值都为undefined,对某一行的该列赋值,再updatebounddata也不会刷新该列表格数据
 3. 
所以不能delete row中的某个属性

 3. 解决
   save的时候getSheetData中先clone一个sheetRows，再对它进行处理，这样就不会影响grid了

 

## other_class other_region查询慢的问题

 发现other_class other_region 通过 like 查询时速度很慢，需要好几秒，使用一般的索引不行，后来通过以下语句创建索引之后解决了
CREATE INDEX idx_info_supcust_other_region ON info_supcust USING gin (other_region gin_trgm_ops);/*普通索引不支持%ab%这样的查询，导致数据量大时检索缓慢，gin_trgm_ops模式支持的，需要安装扩展 create extension pg_trgm; */



# 打印故障集锦

### 1、东北大连客户 本来打印正常，换了一台新的EPSON 615KII,后就不能打了。
   远程后，发现系统测试页能打。客户端打印销售单时，打印任务队列里也出现了该销售单，并很快消失，看起来正常，就
不实际打印。 选择打印到PDF，XPS都正常。
   重新启动print spool也不管。
   发现该驱动是从驱动天空下载的，怀疑是不是驱动问题。然后从官网下载了驱动，点setup安装后，检测不到打印机，拔插了USB也不行，
最后手动选择端口USB002安装成功。然后打印销售单时，打印队列里显示打印错误。把USB再次拔插后，打印出来了。然后再试就可以了。

### 2、自适应打印时打到A4高度就停下来了。
    
   尝试在打印服务属性里增加一个大于A4纸张的纸张尺寸，可以多设置几个尺寸。

### 云打印粗体时，除了微软雅黑，小数点都打不出来，在centos上的确是这样的


jexus重写URL功能

@老木目 还有一个办法：
把你那个文件放到 asp.net core 的wwwroot中，把扩展名改为.json，然后在jexus的网站配置中添加：
rewrite=^/apple-app-site-association$  /apple-app-site-association.json



2022-11-25 BIG BUG  页面的OnGet方法中不能传入cmySbCommand做依赖注入，因为他没法自动回收，connection会一直保持open,连接池会被用光 
OnGet(CMySbCommand cmd
依赖注入的变量必须放在构造函数里才能被自动回收

2022-12-23 jqxGrid 单元格弹出框超出表格区域时，一点滑块就触发cellendedit
  修正:mousedown事件里增加如下判断
  var popupMum = $(m.target).parent('.jqx-input-popup')
  if (popupMum.length) return//弹出框里点击不应该导致endcelledit

2023-8-6  客户端登录后没有反应，发现是电脑日期和实际相差超过一定天数就不行了。window.location.href =不起作用
代码里加了一个判断以提示用户



2024-6-9  客户鑫振鑫一台电脑客户端登录后白屏，远程，发现调用.setOperKey 后就进入白屏状态，断点也没法调试了。
     然后用EDGE浏览器登录也提示  无法安全地连接到此页面
这可能是因为该站点使用过期的或不安全的 TLS 安全设置。如果这种情况持续发生，请与网站的所有者联系。TLS 安全设置未设置为默认设置，这也可能导致此错误

然后，在IE浏览器里的安全设置