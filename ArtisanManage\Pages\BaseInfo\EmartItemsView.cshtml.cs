using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class EmartItemsViewModel : PageQueryModel 
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
        public string BranchID = "";

        public EmartItemsViewModel(CMySbCommand cmd) : base(MenuId.infoItem)
        {
            this.cmd = cmd;
            this.PageTitle = "商品档案";
            
            DataItems = new Dictionary<string, DataItem>()
            {
                //{"item_query",new DataItem(){title="编号"}}, 
                {"item_brand", new DataItem(){Title = "品牌", LabelFld = "brand_name", ButtonUsage = "list", QueryOnChange = true,MaxRecords="500",
                        CompareOperator = "=",SqlForOptions = CommonTool.selectBrands}},
                {"searchString",new DataItem(){Title = "检索字符串", PlaceHolder = "输入名称/助记码", UseJQWidgets = false,SqlFld = "item_name,py_str,s_barcode,m_barcode,b_barcode,item_no", LabelFld = "brand_name", QueryOnChange = true, CompareOperator = "like"}},
                {"status",new DataItem(){Title = "状态",LabelFld = "cls_status_name", LabelInDB = false, Value = "normal", Label = "正常",Width="50", ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "normal",
                     Source = @"[{v:'normal',l:'正常',condition:""(p.status = '1' or p.status is null)""},
                                 {v:'stop',l:'停用',condition:""p.status = '0' ""},
                                 {v:'all',l:'所有',condition:""true""}]" 
                }},
                {"showHasStock",new DataItem(){Title = "显示有库存", ForQuery = false, LabelInDB = false,UseJQWidgets = false}},
                {"branch_id",new DataItem(){Title = "仓库", FldArea = "divHead", Hidden = true, ForQuery = false, LabelInDB = false,HideOnLoad=true}},
                {"other_class", new DataItem(){Title = "父类", LikeWrapper = "/", CtrlType = "jqxTree", MumSelectable = true,GetOptionsOnLoad = true, QueryOnChange = true, CompareOperator = "like",
                    Value="often", MaxRecords = "1000",SqlForOptions ="select class_id as v,class_name as l,py_str as z,mother_id as pv,cls_status as status from info_item_class where class_id<>-1 order by order_index,class_name,class_id"}},
                {"supcust_id",new DataItem(){Title = "客户", FldArea = "divHead", Hidden = true, ForQuery = false, LabelInDB = false}},
                {"showSonItems",new DataItem(){Title = "显示子商品", FldArea = "divHead", Hidden = true, ForQuery = false, LabelInDB = false}},
                {"has_emart_id",new DataItem(){Title = "电商编号", LabelInDB = false, Value = "all", Label = "所有",
                     Width="250", ButtonUsage = "list", QueryOnChange = true, CompareOperator = "=", NullEqualValue = "all",
                     Source = @"[{v:'have',l:'只看已有',condition:""(d.have_emart = true) ""},
                                 {v:'nope',l:'只看没有',condition:""(d.have_emart = false OR d.have_emart is null) ""}, 
                                 {v:'all', l:'所有',condition:""true""}]"
                     // Why '= false OR is null'? Because somtimes this comes null value due to unfixable left-join.
                     // InfSein, 20221121.
                }},
              
                //{"brand_no",new DataItem(){title="品牌",labelFld="brand_name",buttonUsage="list",
                //  SqlForOptions ="select brand_no as v,brand_name as l from info_item_brand"}}
            };
            Func<string, Dictionary<string, string>, string> funcDealCellValue = (value, row) =>
            {
                string optQty = value;
                if (optQty == "") return "";

                var arr = optQty.Split(',');

                string res = "";
                foreach (var s in arr)
                {
                    var ss = s.Split(':');
                    string opt = ss[0];
                    string qty = ss[1];
                    decimal dQty = Decimal.Parse(qty, System.Globalization.NumberStyles.Float);
                    string unitQty = SheetBase<SheetRowBase>.GetUnitQty(dQty, row["b_unit_no"], row["m_unit_no"], row["s_unit_no"], row["b_unit_factor"], row["m_unit_factor"]);
                    if (res != "") res += ",";
                    res += opt + ":" + unitQty;
                }
                return res;
            };
            const string onEmartCellBeginEdit = @"
function(row, datafield, columntype, value, cellvalue) {
    var gridRow = $('#gridItems').jqxGrid('getrows')[row]
    var id = gridRow.i; // Item_id
    if (!id) { console.warn(rowdata); return false; }
    var b = gridRow.b_unit_no;
    var m = gridRow.m_unit_no;
    var s = gridRow.s_unit_no;
    if(datafield == 'b_emart_id') { if(!b) return false; }
    else if(datafield == 'm_emart_id') { if(!m) return false; }
    else if(datafield == 's_emart_id') { if(!s) return false; }
}
";
            const string onEmartCellEndEdit = @"
function(row, datafield, columntype, value, cellvalue){
    var gridRow = $('#gridItems').jqxGrid('getrows')[row]
    var id = gridRow.i; // Item_id
    if (!id) { console.log(rowdata); return; }
    var b = gridRow.b_emart_id;
    var m = gridRow.m_emart_id;
    var s = gridRow.s_emart_id;
    if(datafield == 'b_emart_id') { b = cellvalue; }
    else if(datafield == 'm_emart_id') { m = cellvalue; }
    else if(datafield == 's_emart_id') { s = cellvalue; }
    if(!b){b=''} if(!m){m=''} if(!s){s=''}
    var d = JSON.stringify({ operKey: g_operKey, id: id, b:b, m:m, s:s });
    $.ajax({
        url: '/api/EmartItemsView/SaveEmartIds',
        type: 'POST',
        contentType: 'application/json',
        data: d,
        success: function (data) {
            if (data.result === 'OK') {
                myConsoleLog(`${id} Saved: [b]'${b}'/[m]'${m}'/[s]'${s}'...`)
            }
            else {
                bw.toast('保存失败：'+data.msg)
                console.error('Save emart id failed! See more detail below:')
                console.log(data.detail)
            }
        },  
        error: function (xhr) {
            bw.toast('网络错误,保存失败',1000)
            console.error('Save emart id post-error! See more detail below:')
            console.log(xhr.responseText)
        }
    });
}
";
            const string initEditor = @"
function (row, cellvalue, editor, celltext, pressedkey) {
    // set the editor's current value. The callback is called each time the editor is displayed.
    var inputField = editor.find('input');
    if (pressedkey) {
        inputField.val(pressedkey);
        inputField.jqxInput('selectLast');
    }
    else {
        inputField.val({ value: cellvalue||'', label: celltext||'' });
        if (celltext != undefined) inputField[0].value = celltext;
        inputField.jqxInput('selectAll');
    }
    //  inputField.focus()
}
";
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     HasCheck=true,KeepCheckForQueries=false,                    
                     IdColumn="i",TableName="info_item_prop",
                     JsFuncGetRowKeyForCheck=@"
function(row){
   var key= row.i + '_'
   if(row.disp_flow_id){
        key+='_disp_' + row.disp_flow_id +'_' + row.disp_month_id
   }
   if(row.order_sub_id){
       key+='_order_' + row.order_sub_id +'_'+ row.order_unit_factor+'_' + row.order_price 
   }
   return key

}
",
                     ShowContextMenu=true,
                     ContextMenuHTML="<ul><li id='edit'>编辑</li><li id='remove'>删除</li><li id='BatchOperation'>批量操作</li></ul>",
                     Sortable=true,
                 
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"qtyInput",new DataItem(){Title="数量",GetFromDb=false,editable=true, Hidden=true,HideOnLoad=true, Width="76",Sortable=true,
                           JSCellRender=@"qtyInputRender",JSCreateEditor=@"qtyInputCreateEditor",JSInitEditor=@"qtyInputInitEditor",JSGetEditorValue=@"qtyInputGetEditorValue"

                       }},
                       {"i",new DataItem(){Title="商品编号",SqlFld="p.item_id", Width="200",Hidden=true,HideOnLoad = true}},
                       {"n",new DataItem(){Title="商品名称",SqlFld="item_name", Width="220",Linkable=true,Sortable=true,
                            JSInitEditor = initEditor, JSGetEditorValue = "function (row, cellvalue, editor) { var v = editor.find('input').val(); return v; }",
                            JSCellRender="itemNameRenderer"
                       }},

                       {"have_emart", new DataItem(){Title="已登记电商编号", Width="40", Hidden=true,HideOnLoad=true } },
                       {"emart_ids", new DataItem(){ Title="电商编号", Width="60",
                            SubColumns=new Dictionary<string,DataItem>() {
                                {"b_emart_id",new DataItem(){Title="大",Width="120",CellsAlign="right", editable = true, JSCellBeginEdit = onEmartCellBeginEdit, JSCellEndEdit = onEmartCellEndEdit }},
                                {"m_emart_id",new DataItem(){Title="中",Width="120",CellsAlign="right", editable = true, JSCellBeginEdit = onEmartCellBeginEdit, JSCellEndEdit = onEmartCellEndEdit }},
                                {"s_emart_id",new DataItem(){Title="小",Width="120",CellsAlign="right", editable = true, JSCellBeginEdit = onEmartCellBeginEdit, JSCellEndEdit = onEmartCellEndEdit }},
                            }
                       }},

                       #region other old grids, copied from ItemsView...
                       {"stock_qty_unit",new DataItem(){Title="库存",GetFromDb=false,Hidden=true,HideOnLoad=true, SqlFld="yj_get_bms_qty(stock_qty,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)", Width="70",Sortable=true,
                            JSCellRender="stockQtyRenderer"
                       }},
                       {"son_stock_qty",new DataItem(){Title="属性库存",GetFromDb=false,Hidden=true,HideOnLoad=true, Width="70",Sortable=true,
                            FuncDealCellValue=funcDealCellValue
                       }},
                       {"son_usable_stock_qty",new DataItem(){Title="属性可用库存",GetFromDb=false,Hidden=true,HideOnLoad=true, Width="70",Sortable=true,
                            FuncDealCellValue=funcDealCellValue
                       }},
                       {"son_sell_pend_qty",new DataItem(){Title="属性占用库存",GetFromDb=false,Hidden=true,HideOnLoad=true, Width="70",Sortable=true,
                          FuncDealCellValue=funcDealCellValue
                       }},
                       {"usable_stock_qty",new DataItem(){Title="可用库存",GetFromDb=false,Hidden=true,HideOnLoad=true, SqlFld="yj_get_bms_qty(stock_qty-sell_pend_qty,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)", Width="70",Sortable=true,
                        JSCellRender="usableStockQtyRenderer"
                       }},
                       {"sell_pend_stock_qty",new DataItem(){Title="占用库存",GetFromDb=false,Hidden=true,HideOnLoad=true, SqlFld="yj_get_bms_qty(sell_pend_qty,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)", Width="70",Sortable=true,
                        JSCellRender="sellPendStockQtyRenderer"
                       }},
                       {"disp_flow_id",new DataItem(){Title="陈列协议flow",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_sheet_id",new DataItem(){Title="陈列协议单号",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_left_qty",new DataItem(){Title="陈列协议剩余量",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_unit_no",new DataItem(){Title="陈列协议单位",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_month",new DataItem(){Title="陈列协议月份",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_month_id",new DataItem(){Title="陈列协议第几个月",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_items_id",new DataItem(){Title="陈列协议商品名称",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"disp_items_name",new DataItem(){Title="陈列协议商品",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"order_qty_unit",new DataItem(){Title="剩余数量",Width="150",GetFromDb = false,Hidden=true,HideOnLoad=true} },
                       {"order_price",new DataItem(){Title="定货价",GetFromDb = false,Width="100" ,Hidden=true,HideOnLoad=true} },
                       {"order_balance",new DataItem(){Title="定货款余额",Width = "150",GetFromDb = false,Hidden=true,HideOnLoad=true}},
                       {"order_qty", new DataItem(){Title="可用还货数量(无单位)",GetFromDb = false,Hidden=true,HideOnLoad=true}},
                       {"cost_amt",new DataItem(){Title = "货值",Width="100",Hidden=true,HideOnLoad=true} },
                       //{"quantity",new DataItem(){Title="订货价",GetFromDb = false } },
                       {"order_unit_factor",new DataItem(){Title="定货单位",GetFromDb = false,Hidden = true,HideOnLoad=true } },
                       {"order_sub_id",new DataItem(){Title="定货款账户",GetFromDb = false,Hidden=true,HideOnLoad=true} },
                       {"order_sub_name",new DataItem(){Title="定货款账户",Width = "150",GetFromDb = false,Hidden=true,HideOnLoad=true}},
                       {"borrowed_qty",new DataItem(){Title="借货数量(无单位)",Width = "150",GetFromDb = false,Hidden=true,HideOnLoad=true}},
                       {"item_no",new DataItem(){Title="商品编号",SqlFld="item_no", Width="150",Hidden=false,Sortable=true}},
                       {"item_spec",new DataItem(){Title="规格",Width="150"}},
                       {"valid_days",new DataItem(){Title="保质期",Hidden = true,Width="150"}},
                       {"barcode",new DataItem(){Title="小单位条码",SqlFld="s_barcode",Hidden=true, Width="200"}},
                       {"b_barcode",new DataItem(){Title="大单位条码",Hidden=true, Width="200"}},
                       {"s_unit_no",new DataItem(){Title="小单位",Hidden=true, Width="00"}},
                       {"b_unit_no",new DataItem(){Title="大单位",Hidden=true, Width="70"}},
                       {"m_unit_no",new DataItem(){Title="中单位",Hidden=true, Width="00"}},
                       {"m_unit_factor",new DataItem(){Title="中包装率",Hidden=true, Width="10"}},
                       {"b_unit_factor",new DataItem(){Title="大包装率",Hidden=true, Width="10"}},


                       {"b",new DataItem(){Title="品牌",SqlFld="brand_name",Hidden=true, Width="150"}},
                       {"mum_attributes",new DataItem(){Hidden=true,HideOnLoad=true}},
                       {"category",new DataItem(){Title="类别",SqlFld="concat(class_name,'/', mother_name)",Hidden=true, Width="150"}},
                       {"status",new DataItem(){Title="状态",Width="180",SqlFld="(case WHEN status='0' THEN '停用' ELSE '正常' END)"}},
                       {"c",new DataItem(){Title="单位换算",SqlFld="unit_conv", Width="200"}},
                       {"wholesale_price",new DataItem(){Title="批发价", Width="60",Hidden=true,
                            SubColumns=new Dictionary<string,DataItem>()
                            {
                                {"b_wholesale_price",new DataItem(){Title = "大",InnerTitle="批发价大",OrigTitle="批发价大",Hidden = true,Width = "70",CellsAlign="right",Sortable=true}},
                                {"m_wholesale_price",new DataItem(){Title="中",Hidden=true,Width="70",CellsAlign="right"} },
                                {"s_wholesale_price",new DataItem(){Title="小",Hidden=true,Width="70",CellsAlign="right",Sortable=true} },
                            }
                       }},
                       {"retail_price",new DataItem(){Title="零售价", Width="60",Hidden=true,  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"b_retail_price",new DataItem(){Title = "大",Hidden =true,Width = "70",CellsAlign="right",Sortable=true}},
                                    {"m_retail_price",new DataItem(){Title="中",Hidden=true,Width="70",CellsAlign="right"} },
                                    {"s_retail_price",new DataItem(){Title="小",Hidden=true,Width="70",CellsAlign="right",Sortable=true} },
                                }
                            }
                       } },
                       {"buy_price",new DataItem(){Title="进价", Width="60",Hidden=true,  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"b_buy_price",new DataItem(){Title = "大",Hidden = true,Width = "70",CellsAlign="right",Sortable=true}},
                                    {"m_buy_price",new DataItem(){Title="中",Hidden=true,Width="70",CellsAlign="right"} },
                                    {"s_buy_price",new DataItem(){Title="小",Hidden=true,Width="70",CellsAlign="right",Sortable=true} },
                                }
                            }
                       }},
                       {"cost_price_avg",new DataItem(){Title="加权价", Width="60",Hidden=true,  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"b_cost_price",new DataItem(){Title = "大",Hidden = true,Width = "70",CellsAlign="right",SqlFld="round((cost_price_avg*b_unit_factor)::numeric,2)", Sortable=true}},
                                    {"m_cost_price",new DataItem(){Title="中",Hidden=true,Width="70",SqlFld="round((cost_price_avg*m_unit_factor)::numeric,2)  ",CellsAlign="right"} },
                                    {"s_cost_price",new DataItem(){Title="小",Hidden=true,Width="70",SqlFld="round((cost_price_avg*s_unit_factor)::numeric,2)  ",CellsAlign="right",Sortable=true} },
                                }
                            }
                       }}
                       #endregion
                     },
                     QueryFromSQL=$@"
from info_item_prop p 
 ~SQL_VARIABLE1
left join
(
  select brand_id,brand_name from info_item_brand where company_id = ~COMPANY_ID
) b on p.item_brand = brand_id
LEFT JOIN 
( 
    SELECT class_id, class_name, 
	    ic.mother_id,
	    mother_name
    FROM info_item_class ic
	LEFT JOIN
    (
	    SELECT
	    class_id  mother_id,
	    class_name mother_name
        FROM
	        info_item_class 
        WHERE
	        class_id <>- 1 
	        AND ( cls_status IS NULL OR cls_status = '1' ) 
	        and company_id=~COMPANY_ID
        ORDER BY order_index, class_name, class_id
    ) t on ic.mother_id=t.mother_id
    WHERE company_id = ~COMPANY_ID 
) C ON P.item_class = class_id
LEFT JOIN
(
    SELECT item_id,
        s_unit_no,s_unit_factor,s_wholesale_price,s_retail_price,s_buy_price,s_barcode,
        m_unit_no,m_unit_factor,m_wholesale_price,m_retail_price,m_buy_price,m_barcode,
        b_unit_no,b_unit_factor,b_wholesale_price,b_retail_price,b_buy_price,b_barcode,
        (
         case when b_unit_factor is not null and m_unit_factor is     null then concat(s_unit_factor,b_unit_no,'=',b_unit_factor,s_unit_no)  
			  when b_unit_factor is not null and m_unit_factor is not null then concat(s_unit_factor,b_unit_no,'=',floor(b_unit_factor::numeric/m_unit_factor::numeric),m_unit_no,'=',b_unit_factor,s_unit_no)
			  when b_unit_factor is null then concat(s_unit_factor,s_unit_no)  end
        ) as unit_conv,
        es.src_item_id as s_emart_id, em.src_item_id as m_emart_id, eb.src_item_id as b_emart_id, 
        (CASE WHEN es.src_item_id is null AND em.src_item_id is null AND eb.src_item_id is null THEN false ELSE true END) as have_emart
    FROM 
    (
        SELECT item_id,(s->>'f1')::real as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_wholesale_price,s->>'f4' as s_retail_price,s->>'f5' s_barcode,s->>'f6' as s_buy_price,
                       (m->>'f1')::real as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_wholesale_price,m->>'f4' as m_retail_price,m->>'f5' m_barcode,m->>'f6' as m_buy_price,
                       (b->>'f1')::real as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_wholesale_price,b->>'f4' as b_retail_price,b->>'f5' b_barcode,b->>'f6' as b_buy_price
        FROM crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,retail_price,barcode,buy_price)) as json from info_item_multi_unit where company_id = ~COMPANY_ID ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb) 
    ) t 
    LEFT JOIN(
        select src_item_id, dest_item_id from emart_item_pair where company_id = ~COMPANY_ID and emart_type = 'all' and dest_item_unit = 's'
    ) es ON es.dest_item_id = t.item_id
    LEFT JOIN(
        select src_item_id, dest_item_id from emart_item_pair where company_id = ~COMPANY_ID and emart_type = 'all' and dest_item_unit = 'm'
    ) em ON em.dest_item_id = t.item_id
    LEFT JOIN(
        select src_item_id, dest_item_id from emart_item_pair where company_id = ~COMPANY_ID and emart_type = 'all' and dest_item_unit = 'b'
    ) eb ON eb.dest_item_id = t.item_id
) d on d.item_id = p.item_id
~VAR_stock_sql


where p.company_id=~COMPANY_ID ~SQL_VARIABLE2 ~SQL_VARIABLE3 ~VAR_son_items_condi ~VAR_stock_condi" ,
                     QueryOrderSQL="order by p.item_order_index,p.item_id desc"
                  }
                }
            };
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            string supcust_id = DataItems["supcust_id"].Value;//这个值不选的时候不提供常用选择功能
            string other_class = DataItems["other_class"].Value;
            var dispCondi = " and ((disp_year < (to_char(now(),'YYYY')::int)) OR (disp_month<=(to_char(now(),'MM')::int) and disp_year = (to_char(now(),'YYYY')::int))) ";
            // var dispCondi = " and disp_month<=(to_char(now(),'MM')::int) and disp_year=(to_char(now(),'YYYY')::int) ";
            //查询提前兑付权限
            string rightSql = $"select rights->'delicacy'->'allowAdvanceDisplayFee'->'value' allow_advance_disp_fee from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
            dynamic advanceDisp = await CDbDealer.Get1RecordFromSQLAsync(rightSql, cmd);
            if (advanceDisp != null && advanceDisp.allow_advance_disp_fee == "true")
                dispCondi = $" ";
            // DataItems["supcust_id"].Value = "";
            if (supcust_id != "" && other_class == "often")
            {
                var sup_select = !string.IsNullOrWhiteSpace(supcust_id);
                string supQuery = sup_select ? $" and b.supcust_id = {supcust_id} " : "";
                string supQuery1 = sup_select ? $" and supcust_id = {supcust_id} " : "";
                this.SQLVariable1 =
                    @$" 
left join 
(
    select item_id,true as is_often 
    from 
    (
        select item_id,count,row_number() over(order by count desc) rn 
        from 
        (
            select s.item_id,count1+count2 count from 
            (
                select item_id,count(item_id) count1 from sheet_sale_detail sd
                left join sheet_sale_main sm on sd.sheet_id = sm.sheet_id  and sd.company_id = sm.company_id
                where sd.company_id = {this.company_id} {supQuery1} and (sm.happen_time::date >= (now()-interval'3 months') and sm.happen_time::date <=now()) group by item_id
            ) s
            left join 
            (
                select item_id,count(item_id) count2 from sheet_sale_order_detail od left join sheet_sale_order_main om on od.sheet_id = om.sheet_id  and od.company_id = om.company_id
                where od.company_id = {company_id} {supQuery1} and (om.happen_time::date >= (now()-interval'3 months') and om.happen_time::date<= now())group by item_id
            ) o
            on o.item_id = s.item_id
        )  t 
    ) tt where rn<=30
) often on often.item_id = p.item_id

left join 
( 
    select flow_id disp_flow_id,sheet_id disp_sheet_id,split_part(items_id, ',',1)::int item_id,items_id disp_items_id,items_name disp_items_name,disp_left_qty,unit_no disp_unit_no,
    (case when start_month+months-1>12 then start_month+months-13 else start_month+months-1 end) disp_month,
    (case when start_month+months-1>12 then start_year+1 else start_year end) as disp_year,months as disp_month_id,null order_sub_id,null order_sub_name,null order_price,null order_unit_factor,null order_balance,null order_qty,null borrowed_qty
    
    from
    (
        select d.sheet_id,d.flow_id,items_id,items_name,unit_no,unnest(string_to_array((								
                COALESCE(month1_qty,0)-COALESCE(month1_given,0)||','||COALESCE(month2_qty,0)-COALESCE(month2_given,0)||','||
                COALESCE(month3_qty,0)-COALESCE(month3_given,0)||','||COALESCE(month4_qty,0)-COALESCE(month4_given,0)||','||
                COALESCE(month5_qty,0)-COALESCE(month5_given,0)||','||COALESCE(month6_qty,0)-COALESCE(month6_given,0)||','||
                COALESCE(month7_qty,0)-COALESCE(month7_given,0)||','||COALESCE(month8_qty,0)-COALESCE(month8_given,0)||','||
                COALESCE(month9_qty,0)-COALESCE(month9_given,0)||','||COALESCE(month10_qty,0)-COALESCE(month10_given,0)||','||
                COALESCE(month11_qty,0)-COALESCE(month11_given,0)||','||COALESCE(month12_qty,0)-COALESCE(month12_given,0)) ,','))::numeric disp_left_qty,
                unnest(string_to_array('01,02,03,04,05,06,07,08,09,10,11,12',','))::int months,to_char(m.start_time,'YYYY')::int start_year,to_char(m.start_time,'MM')::int start_month 
            from display_agreement_detail d left join display_agreement_main m on m.sheet_id = d.sheet_id and d.company_id = m.company_id
            where d.company_id = {company_id} and red_flag is null and approve_time is not null and supcust_id = {supcust_id} and items_id!='money' 
    ) t 
    where disp_left_qty > 0 GROUP BY items_id,items_name,flow_id,sheet_id,months,disp_left_qty,unit_no,start_year,start_month 

	union
		select null disp_flow_id,null disp_sheet_id,item_id,null disp_items_id,null disp_items_name,null disp_left_qty,null disp_unit_no,to_char(now(),'MM')::int disp_month,to_char(now(),'YYYY')::int disp_year,null disp_month_id,prepay_sub_id order_sub_id,sub_name as order_sub_name,order_price,b.unit_factor as order_unit_factor,b.quantity * b.order_price as order_balance,quantity*unit_factor order_qty,null borrowed_qty
        from items_ordered_balance b
        left join prepay_balance pb on b.supcust_id = pb.supcust_id and b.prepay_sub_id = pb.sub_id
        left join cw_subject pw on pw.sub_id = b.prepay_sub_id
        where b.company_id={company_id}  and b.supcust_id = {supcust_id} and b.quantity!=0

    union
        select null disp_flow_id,null disp_sheet_id,item_id,null disp_items_id,null disp_items_name,null disp_left_qty,null disp_unit_no,to_char(now(),'MM')::int disp_month,to_char(now(),'YYYY')::int disp_year,null disp_month_id,null order_sub_id,null order_sub_name,null order_price,null order_unit_factor,null order_balance,null order_qty,borrowed_qty::text
        from borrowed_cust_items 
        where company_id = {company_id} and borrowed_qty<>0 and cust_id={supcust_id} 

) tt on tt.item_id = p.item_id {dispCondi}";

                this.SQLVariable2 = @" and (order_sub_id is not null or is_often or disp_flow_id is not null or borrowed_qty is not null)";
                DataItems["other_class"].Value = "";
            }

            string branch_id = DataItems["branch_id"].Value;
            this.SQLVariables["stock_sql"] = "";
            this.SQLVariables["stock_condi"] = "";

            if (branch_id != "")
            {
                var col = Grids.First().Value.Columns["stock_qty_unit"];
                var son_col = Grids.First().Value.Columns["son_stock_qty"];
                son_col.GetFromDb = true;
                var son_usable_col = Grids.First().Value.Columns["son_usable_stock_qty"];
                son_usable_col.GetFromDb = true;
                var usable_col = Grids.First().Value.Columns["usable_stock_qty"];
                col.Hidden = false;
                col.GetFromDb = true;
               
                col.HideOnLoad = false;
                usable_col.Hidden = false;
                usable_col.GetFromDb = true;
                usable_col.HideOnLoad = false;
                /*this.SQLVariables["stock_sql"] = $@"  
LEFT JOIN stock on p.item_id=stock.item_id and p.company_id = stock.company_id and stock.branch_id={branch_id}
";
                */
                this.SQLVariables["stock_sql"] = $@"  
LEFT JOIN 
(
    select stock.company_id,case when son_mum_item is null then stock.item_id else son_mum_item end stock_item_id,sum(stock_qty) stock_qty,sum(sell_pend_qty) sell_pend_qty,
          string_agg(opt_name||':'|| stock_qty,',') son_stock_qty,
          string_agg(opt_name||':'|| sell_pend_qty,',') son_sell_pend_qty,
          string_agg(opt_name||':'|| (stock_qty-sell_pend_qty),',') son_usable_stock_qty
 
    FROM stock
    LEFT join info_item_prop ip on stock.company_id=ip.company_id and stock.item_id=ip.item_id
    LEFT JOIN info_attr_opt opt on ip.company_id=opt.company_id and (case when position ('_' in coalesce(ip.son_options_id,''))=0 then ip.son_options_id::integer else 0 end)=opt.opt_id 
    where stock.company_id={company_id} and stock.branch_id={branch_id}
    group by stock.company_id,stock_item_id
) stock
on p.item_id=stock.stock_item_id
";

                string showHasStock = DataItems["showHasStock"].Value;
                if(showHasStock.ToLower()=="true")
                   this.SQLVariables["stock_condi"] = " and stock_qty >0";
            }

            this.SQLVariables["son_items_condi"] = "";
            string showSonItems = DataItems["showSonItems"].Value;
            if (showSonItems == "1")
            {
                this.SQLVariables["son_items_condi"] = " and (mum_attributes is null or mum_attributes::text not like '%\"distinctStock\": true%')";
            }
            else
            {
                this.SQLVariables["son_items_condi"] = " and son_mum_item is null";
            }



        }
        public async Task OnGet(string forSelect,string branch_id)
        {
            ForSelect = forSelect == "1";
            if (ForSelect)
            {
                Grids["gridItems"].HasCheck = true;
                Grids["gridItems"].KeepCheckForQueries = true;

                var dataItem = DataItems["other_class"];
                dataItem.JSAfterCreate = @"
                  var items = $('#other_class').jqxTree('getItems')
                  if (items.length > 1) {
                        $('#other_class').jqxTree('addBefore', { value: 'often', label: '常用' }, items[1].element, false);
                        $('#other_class').jqxTree('render'); 
                        var div = $('#other_class').find('div:contains(常用)');
                        if(div.length){
                           var li=div[0].parentNode
                           $('#other_class').jqxTree('selectItem',li)
                        }
                  }

            
";
                var col = Grids.First().Value.Columns["qtyInput"];
                col.Hidden = false;
               
            }

            if (branch_id != null && branch_id != "")
            {
                var col = Grids.First().Value.Columns["stock_qty_unit"];
                var usable_col = Grids.First().Value.Columns["usable_stock_qty"];
                col.Hidden = false;
                usable_col.Hidden = false;
                col.GetFromDb = true;
                usable_col.GetFromDb = true;
                this.BranchID = branch_id;
               // col.HideOnLoad = false;
            }
            await InitGet(cmd);

            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        { 
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                var columns = await Grids["gridItems"].GetAllColumns();
                columns["s_buy_price"].HideOnLoad = columns["s_buy_price"].Hidden = true;
                columns["m_buy_price"].HideOnLoad = columns["m_buy_price"].Hidden = true;
                columns["b_buy_price"].HideOnLoad = columns["b_buy_price"].Hidden = true; 
            } 

        }
        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        { 
            cmd.CommandText = $"select item_id from stock where item_id in ({rowIDs}) and company_id={company_id} limit 1";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                return "商品已产生库存记录,无法删除";
            }

            cmd.CommandText = $"select item_id from sheet_order_item_detail where item_id in ({rowIDs}) and company_id={company_id} limit 1";
            ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                return "商品已在定货会中使用过,无法删除";
            }
            return "";
        }
    }

    [Route("api/[controller]/[action]")]
    public class EmartItemsViewController : BaseController
    { 
        public EmartItemsViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            EmartItemsViewModel model = new EmartItemsViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            EmartItemsViewModel model = new EmartItemsViewModel(cmd);
            var columns = model.Grids.GetValueOrDefault("gridItems").Columns;
            //选择常用后，部分列名参与查询 设置 GetFromDb = true; 
            string supcust_id = CPubVars.RequestV(Request, "supcust_id");
            string other_class = CPubVars.RequestV(Request, "other_class");
            if (supcust_id.IsValid() &&  other_class == "often")
            { 
                foreach (KeyValuePair<string, DataItem> col in columns)
                {
                    if (col.Key.IndexOf("order") >= 0 || col.Key.IndexOf("disp") >= 0 || col.Key.IndexOf("borrowed") >= 0) 
                    {
                        if(col.Key== "order_qty_unit") col.Value.SqlFld = @"(case when order_qty=0 then '' when order_qty!=0 then unit_from_s_to_bms (order_qty::float8,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) 
                                                                                  when disp_flow_id is not null then concat(disp_left_qty,disp_unit_no) 
                                                                                  when borrowed_qty='0' then '' when borrowed_qty!='0' then unit_from_s_to_bms (borrowed_qty::float8,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)
                                                                            end)";
                        col.Value.GetFromDb = true; 
                    }
                }
                model.Grids.GetValueOrDefault("gridItems").QueryOrderSQL = "order by disp_flow_id,disp_month_id,borrowed_qty,p.item_order_index,p.item_id";
                
            };
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<object> SaveEmartIds([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string company_id);
            string item_id = data.id;
            string b_eid = data.b;
            string m_eid = data.m;
            string s_eid = data.s;
            string sql = "", values = "";
            if (b_eid.IsValid())
            {
                b_eid = b_eid.Replace("'", ""); // Clean invalid char
                values += $"('{company_id}', 'all', '{b_eid}', '{item_id}', 'b'),"; 
            }
            if (m_eid.IsValid())
            {
                m_eid = m_eid.Replace("'", "");
                values += $"('{company_id}', 'all', '{m_eid}', '{item_id}', 'm'),"; 
            }
            if (s_eid.IsValid())
            {
                s_eid = s_eid.Replace("'", "");
                values += $"('{company_id}', 'all', '{s_eid}', '{item_id}', 's'),"; 
            }

            // 先删后加
            sql += $@"DELETE FROM emart_item_pair 
                      WHERE company_id = {company_id} AND dest_item_id = {item_id}; ";
            if (values != "")
            {
                values = values.Trim(',');
                sql += $@"
                      INSERT INTO emart_item_pair(company_id, emart_type, src_item_id, dest_item_id, dest_item_unit) 
                      VALUES {values} 
                      ON CONFLICT(company_id, emart_type, src_item_id) 
                      DO UPDATE SET dest_item_id = excluded.dest_item_id, dest_item_unit = excluded.dest_item_unit";
            }

            string msg = "", detail = "";
            if (sql != "")
            {
                // 由于含有DELETE语句，使用transaction来避免不可逆操作
                // 只有运行了所有语句未报错的情况下才会commit，否则回滚
                CMySbTransaction tran = cmd.Connection.BeginTransaction();
                try
                {
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                    tran.Commit();
                }
                catch(Exception ex)
                {
                    msg += ex.Message;
                    detail += ex.ToString();
                }
            }
            string result = msg=="" ? "OK" : "Error";
            return new { result, msg, detail };
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            EmartItemsViewModel model = new EmartItemsViewModel(cmd);
            dynamic records = await model.DeleteRecords(data, cmd, "info_item_prop");// gridID, startRow, endRow, bNewQuery);

            // Emart modify
            if(records.Value.result == "OK")
            {
                try
                {
                    var item_ids = (string)data.rowIDs;
                    Security.GetInfoFromOperKey(data.operKey, out string company_id);
                    if (item_ids.IsValid() && company_id.IsValid())
                    {
                        var sql = $@"DELETE FROM emart_item_pair WHERE company_id = {company_id} AND dest_item_id IN ({item_ids});";
                        cmd.CommandText = sql;
                        await cmd.ExecuteNonQueryAsync();
                    }
                }
                catch(Exception ex)
                {
                    NLogger.Error(ex.ToString());
                    NLogger.Info($"报错的Sql Command: {cmd.CommandText}");
                    return new JsonResult(new{ result="Error", msg=ex.Message });
                }
            }
            
            return records;
        }
     
        [HttpPost]
        public async Task<IActionResult> RemoveClass([FromBody] dynamic value)
        {
            ClassEditModel model = new ClassEditModel(cmd);
            Security.GetInfoFromOperKey((string)value.operKey, out string companyID);
            string id = value[model.m_idFld];
            string result = "OK";
            if (id == "")
            {
                result = "请传入类编号";
                goto end;
            }
            CDbDealer db = new CDbDealer();

            object o = null;

            cmd.CommandText = @$"select class_id from info_item_class where mother_id='{id} ' and company_id = {companyID}";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该类的子类后再删除该类"; goto end;
            }
            cmd.CommandText = @$"select item_name from info_item_prop where item_class=' {id}' and company_id = {companyID}";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该类的商品后再删除该类"; goto end;
            }
            string sql = @$"delete from info_item_class where class_id='{id}' and company_id = {companyID}";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();


        //  var tt = Convert.ToString(value.uid); 
        //var rr = new { UserID = value.UserID, UserName = value.UserName };
        //return value;
        end:
            return Json(new { result, class_id = id });
            //return JsonObject<object> (new { UserID = value.UserID, UserName = value.UserName });
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            EmartItemsViewModel model = new EmartItemsViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
        public async Task<JsonResult> BatchSetClass([FromBody] dynamic data)
        {
            EmartItemsViewModel model = new EmartItemsViewModel(cmd);
            
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);

            string item_class = data.item_class;
            string other_class = data.other_class;
            string sql = "";
            string rows = string.Join(",", data.rows);
            //cmd.CommandText = $" select brand_id  from info_item_class where  company_id ={companyID} AND class_id = {item_class}";
            //var item_brand = await cmd.ExecuteScalarAsync();             

            sql = $"UPDATE info_item_prop SET item_class = {item_class} ,other_class = '{other_class}' WHERE company_id ={companyID} AND item_id in  ({rows} )";

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new { result = "OK", msg = "" });
        }
        public async Task<JsonResult> BatchSetBrand([FromBody] dynamic data)
        {
            EmartItemsViewModel model = new EmartItemsViewModel(cmd);

            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);

            string item_brand = data.item_brand;
            string sql = "";
            string rows = string.Join(",", data.rows);
            //cmd.CommandText = $" select brand_id  from info_item_class where  company_id ={companyID} AND class_id = {item_class}";
            //var item_brand = await cmd.ExecuteScalarAsync();             

            sql = $"UPDATE info_item_prop SET item_brand = {item_brand}  WHERE company_id ={companyID} AND item_id in  ({rows} )";

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new { result = "OK", msg = "" });
        }

        [HttpPost]
        public async Task<JsonResult> BatchSetStatus([FromBody] dynamic data)
        {

            EmartItemsViewModel model = new EmartItemsViewModel(cmd);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string status = data.status;
            string rows = string.Join(",", data.rows);
            string sql = "";
              
            if (status == "正常")
                sql = $"UPDATE info_item_prop SET status = '1' WHERE company_id ={companyID} AND item_id in  ({rows} )";
            else if(status == "停用")
                sql = $"UPDATE info_item_prop SET status = '0' WHERE company_id ={companyID} AND item_id in  ({rows} )";

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new { result = "OK", msg = "" });
        }
        [HttpPost]
        public async Task<JsonResult> BatchDelete([FromBody] dynamic data)
        {
            EmartItemsViewModel model = new EmartItemsViewModel(cmd);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);

            var msg = "";
            //List<ExpandoObject> dat = null;
            if (data.type == "勾选")
            {
                foreach (var id in data.rows)
                {
                    cmd.CommandText = $@"
                    select item_id from stock where item_id = {id} and company_id={companyID} 
                    union
                    select item_id from items_ordered_balance where item_id = {id} and company_id={companyID} 
                    union
                    select item_id from sheet_sale_order_detail where item_id = {id} and company_id={companyID} limit 1 ";
                  var aa = await cmd.ExecuteScalarAsync();

                    if (aa != null)
                    {
                        cmd.CommandText = $" select item_name from info_item_prop where  company_id ={companyID} AND item_id ={id} ";
                        var name = await cmd.ExecuteScalarAsync();
                        msg += $"{name} ";
                    }
                   
                }
            }
            //else if (data.type == "全部")
            //{
            //    cmd.CommandText = $"delete from info_item where  company_id ={companyID} ";
            //    await cmd.ExecuteNonQueryAsync();
            //}

            if (msg != "") return Json(new { msg = $"{msg}已被使用，无法删除" });
            //{ throw new MyException($"{msg}已被使用，无法删除"); }
            else
            {
                string rows = string.Join(",", data.rows);
                cmd.CommandText = @$"
DELETE FROM info_item_prop WHERE company_id = {companyID} AND item_id IN ({rows}); 
DELETE FROM emart_item_pair WHERE company_id = {companyID} AND dest_item_id IN ({rows});";
                await cmd.ExecuteNonQueryAsync();
                return Json(new { result = "OK" });
            }
                
        }




    }


    //public class GoodClass
    //{
    //    public string id = "";
    //    public string name = "";
    //    public string owner = "";
    //    public bool hasSonClass = false;
    //    public int ClassIndex = -1;
    //    public string combine_sta = "";
    //    public string select_style = "";
    //}
}
