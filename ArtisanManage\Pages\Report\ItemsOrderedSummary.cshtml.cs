﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ItemsOrderedSummaryModel : PageQueryModel
    { 
        
        public ItemsOrderedSummaryModel(CMySbCommand cmd) : base(Services.MenuId.itemsOrderedSummary)
        {
            this.cmd = cmd;
            this.PageTitle = "定货汇总";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, Value=CPubVars.GetDateText(DateTime.Now.Year-1 + "-" + DateTime.Now.Month + "-01")  +" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="b.item_id,u.item_id",DropDownWidth="300",
                    SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
                //{"order_sub_id",new DataItem(){FldArea="divHead",Title="定货款账户",LabelFld="sub_name",ButtonUsage="list",CompareOperator="=",SqlFld="order_sub_id,u.prepay_sub_id",
                //    SqlForOptions ="select sub_id as v,sub_name as l from cw_subject where company_id=~COMPANY_ID and sub_type = 'YS' and is_order"}},
                {"order_sub_id",new DataItem(){FldArea="divHead",Title="定货款账户",LabelFld="sub_name",ButtonUsage="list",CompareOperator="=",SqlFld="order_sub_id,u.prepay_sub_id",SqlForOptions="select -1 as v ,'未指定' as l , 'wzd' as z union select sub_id as v,sub_name as l,py_str as z from cw_subject where sub_type = 'YS' and is_order = true and COALESCE(status,'1')='1'"}},

                {"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld = "b.supcust_id,u.supcust_id",
                    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID "}},

                {"getter_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="oper_name",ButtonUsage="list",CompareOperator="=",SqlFld="u.getter_id",
                    SqlForOptions="select oper_id as v,oper_name as l from info_operator where company_id=~COMPANY_ID  and is_seller  and COALESCE(status,'1')='1' order by oper_name" } },
                {"showZeroQty",new DataItem(){FldArea="divHead",Title="零剩余可用",LabelFld = "showZeroQtyStatus",ButtonUsage = "list",CompareOperator="=",Value="0",Label="不显示零剩余",
                       Source = @"[{v:'1',l:'显示零剩余',condition:""true""},
                                    {v:'0',l:'不显示零剩余',condition:""b.order_qty <> 0  ""}]"

                }},
                //{"showDiffOnly",new DataItem(){FldArea="divHead",Title="展示差异",AfterGroup=true, ButtonUsage = "list",CompareOperator="=",Value="0",Label="展示所有",
                //       Source = @"[{v:'0',l:'展示所有',condition:""true""},
                //                    {v:'1',l:'只展示有差异的',condition:""sum(order_quantity)+sum(change_quantity)+sum(sale_quantity)-coalesce(b.order_qty,0)<>0""}]"

                //}},
                {"remark", new DataItem(){FldArea="divHead",Title="备注",CompareOperator="like", Width="100"}},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates = true,
                   
                     Columns = new Dictionary<string, DataItem>()
                     {
                       //{"sheet_id",new DataItem(){SqlFld="sheet_id",Hidden=true }},
                       //{"sheet_no",     new DataItem(){Title="单据编号",    Width="180",SqlFld="sheet_no" ,Linkable = true}},
                       {"supcust_id",new DataItem(){Title = "客户",Hidden = true,SqlFld = "coalesce(b.supcust_id,u.supcust_id)",HideOnLoad= true } },
                       {"sup_name",new DataItem(){Title="客户", Width="180"}},
                       {"order_sub_id",new DataItem(){Title="定货款账户",Width = "180",Hidden = true,SqlFld = "coalesce(order_sub_id,u.prepay_sub_id)" ,HideOnLoad= true} },
                       {"getter_id",new DataItem(){Title="业务员",Width = "180",Hidden = true,SqlFld = "string_agg(DISTINCT u.getter_id::text, ',')" ,HideOnLoad= true} },
                       {"seller_id",new DataItem(){Title="",Width = "180",Hidden = true,SqlFld = "split_part(string_agg(case when sheet_type <>'销' then  u.getter_id::text end , '|' order by happen_time desc) ,'|', 1)" ,HideOnLoad= true} },
                       {"order_sub_name",new DataItem(){Title="定货款账户",Width = "150",SqlFld="COALESCE(sub_name,'未指定')" } },
                       {"getter_name",new DataItem(){Title="业务员",Width = "150",SqlFld="string_agg(DISTINCT concat(sheet_type,':',oper_name) , ';')" } },
                       {"seller_name",new DataItem(){Title="定货业务员",Width = "150",SqlFld="split_part(string_agg(case when sheet_type <>'销' then  oper_name::text end , '|' order by happen_time desc) ,'|', 1)" } },
                       //{"sheet_no",new DataItem(){Title="近期单号",Width = "150",Linkable = true,SqlFld=" split_part(string_agg(sheet_no , '|' order by happen_time desc) ,'|', 1)" } },
                       {"sheet_no",new DataItem(){Title="期间定货单号",Width = "150",Linkable = true,SqlFld=" string_agg(case when u.sheet_type <>'销' and  position( u.sheet_no in b.order_item_sheets_no)>0  and  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then concat(sheet_no,'_',sheet_id ) else null end ,',' ) ",
                       JSCellRender ="orderItemSheetRender" 
                       //JSCellRender=@"function (row, column, value,p4,p5,rowData) { 
                        //                var orderItemSheets=rowData.sheet_no;
                        //                var orderItemSheet_arr=orderItemSheets.split(',');
                        //                var html='';
                        //                if(orderItemSheet_arr.length>0){
                        //                    for(var index in orderItemSheet_arr){
                        //                        var sht = orderItemSheet_arr[index]
                        //                        var sht_arr = sht.split('_')
                        //                        html +=`<span style =""margin-left:20px"" >${sht_arr[0]}</span>`
                        //                    }
                        //                }
                        //                return `<div style = ""height:100%;display:flex;align-items:center;justify-content:flex-start;"" >${html}</div>`
                        //                }"
                       } },
                       {"happen_time",new DataItem(){Title="近期定货时间",Width = "150",SqlFld=" split_part(string_agg(case when u.sheet_type <>'销' and u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then   happen_time::text else null end  , '|' order by happen_time desc) ,'|', 1) " } },
                       {"sheet_id",new DataItem(){Title="单据ID", Width = "150",HideOnLoad=true, Hidden = true,SqlFld=" split_part(string_agg(case when sheet_type <>'销' then  sheet_id::text end , '|' order by happen_time desc) ,'|', 1) " } },

                       //{"sheet_no",new DataItem(){Title="单号",Width = "150",Linkable = true,SqlFld=" string_agg(sheet_no , '|' order by happen_time desc)" } },
                       //{"happen_time",new DataItem(){Title="时间",Width = "150",SqlFld=" string_agg(happen_time::text , '|' order by happen_time desc)  " } },
                       //{"sheet_id",new DataItem(){Title="间",Width = "150",SqlFld=" string_agg(sheet_id::text , '|' order by happen_time desc)  " } },

                       {"item_id",new DataItem(){Title="商品",Width="",Hidden=true,SqlFld = "coalesce(b.item_id,u.item_id)" } },
                       {"item_name",new DataItem(){Title="商品名称", Width="180",Linkable = true}},
						{"s_unit_no",new DataItem(){Title="小单位", Width="60",Hidden=true}},
                       //{"unit_no",new DataItem(){Title = "单位",Hidden = true ,SqlFld = "b.unit_no"} },
                       {"initial_quantity1",new DataItem(){Title = "期初数量(小)",Hidden = true,Width="120", SqlFld = "coalesce(b.order_qty,0) -(sum(order_quantity)+sum(change_quantity)+sum(sale_quantity))", ShowSum=true}},
                       {"initial_quantity_b",new DataItem(){Title = "期初数量B",Hidden = true, SqlFld = "yj_get_unit_qty('b',(coalesce(b.order_qty,0) -(sum(order_quantity)+sum(change_quantity)+sum(sale_quantity)))::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"initial_quantity_m",new DataItem(){Title = "期初数量m",Hidden = true, SqlFld = "yj_get_unit_qty('m',(coalesce(b.order_qty,0) -(sum(order_quantity)+sum(change_quantity)+sum(sale_quantity)))::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"initial_quantity_s",new DataItem(){Title = "期初数量s",Hidden = true, SqlFld = "yj_get_unit_qty('s',(coalesce(b.order_qty,0) -(sum(order_quantity)+sum(change_quantity)+sum(sale_quantity)))::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"initial_quantity", new DataItem(){Title="期初数量",CellsAlign="right",Width="120",FuncDealMe=value=>{return value=="0"?"":value; },
                           SqlFld="unit_from_s_to_bms ((coalesce(b.order_qty,0)-( sum(order_quantity)+sum(change_quantity)+sum(sale_quantity)))::float4,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",

                           FuncGetSumValue = ((sumValues) =>
                           {
                               string sum="";
                               string s=sumValues["initial_quantity_b"];
                               if(s!="") sum+=s+"大";
                               s=sumValues["initial_quantity_m"];
                               if(s!="") sum+=s+"中";
                               s=sumValues["initial_quantity_s"];
                               if(s!="") sum+=s+"小";
                               return sum;
                           })
                       }},

                       {"order",new DataItem(){Title="期间定货", Width="10%",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                  {"order_quantity", new DataItem(){Title="定货数量",CellsAlign="right",Width="120",FuncDealMe=value=>{return value=="0"?"":value; },
                                       SqlFld="unit_from_s_to_bms (sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  order_quantity else 0 end)::float4,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",

                                       FuncGetSumValue = ((sumValues) =>
                                       {
                                           string sum="";
                                           string s=sumValues["order_quantity_b"];
                                           if(s!="") sum+=s+"大";
                                           s=sumValues["order_quantity_m"];
                                           if(s!="") sum+=s+"中";
                                           s=sumValues["order_quantity_s"];
                                           if(s!="") sum+=s+"小";
                                           return sum;
                                       })
                                   }},

                                  {"change_quantity", new DataItem(){Title="调整数量",CellsAlign="right",Width="120",FuncDealMe=value=>{return value=="0"?"":value; },
                                       SqlFld="unit_from_s_to_bms (sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  change_quantity else 0 end)::float4,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                                       FuncGetSumValue = ((sumValues) =>
                                       {
                                           string sum="";
                                           string s=sumValues["change_quantity_b"];
                                           if(s!="") sum+=s+"大";
                                           s=sumValues["change_quantity_m"];
                                           if(s!="") sum+=s+"中";
                                           s=sumValues["change_quantity_s"];
                                           if(s!="") sum+=s+"小";
                                           return sum;
                                       })

                                   }},
                                  {"sale_quantity", new DataItem(){Title="已还数量",CellsAlign="right",Width="120",FuncDealMe=value=>{ return value=="0"?"":value; },
                                       SqlFld="unit_from_s_to_bms ((sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  sale_quantity else 0 end)*(-1))::float4,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                                       FuncGetSumValue = ((sumValues) =>
                                       {
                                           string sum="";
                                           string s=sumValues["sale_quantity_b"];
                                           if(s!="") sum+=s+"大";
                                           s=sumValues["sale_quantity_m"];
                                           if(s!="") sum+=s+"中";
                                           s=sumValues["sale_quantity_s"];
                                           if(s!="") sum+=s+"小";
                                           return sum;
                                       })
                                   }},
                                  {"left_quantity", new DataItem(){Title="剩余可用",CellsAlign="right", Width="120",FuncDealMe=value=>{return value=="0"?"":value; },
                                       SqlFld="unit_from_s_to_bms((coalesce(b.order_qty,0) -(sum(case when  u.happen_time >'~VAR_endDay' then  order_quantity else 0 end )+sum(case when  u.happen_time >'~VAR_endDay' then  change_quantity else 0 end)+sum(case when  u.happen_time >'~VAR_endDay' then  sale_quantity else 0 end)))::float4,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                                       FuncGetSumValue = ((sumValues) =>
                                       {
                                           string sum="";
                                           string s=sumValues["left_quantity_b"];
                                           if(s!="") sum+=s+"大";
                                           s=sumValues["left_quantity_m"];
                                           if(s!="") sum+=s+"中";
                                           s=sumValues["left_quantity_s"];
                                           if(s!="") sum+=s+"小";
                                           return sum;
                                       })
                                   }},

                                }
                            }
                       } },
                       //{"order_quantity1",new DataItem(){Title = "定货数量(小)",Hidden = true,Width="120", SqlFld = "sum(order_quantity)", ShowSum=true}},M.happen_time >= '~VAR_startDay' AND M.happen_time <= '~VAR_endDay'
                       {"order_quantity1",new DataItem(){Title = "定货数量(小)",Hidden = true,Width="120", SqlFld = "sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  order_quantity else 0 end)", ShowSum=true}},
                       {"order_quantity_b",new DataItem(){Title = "定货数量B",Hidden = true, SqlFld = "yj_get_unit_qty('b',sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  order_quantity else 0 end)::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"order_quantity_m",new DataItem(){Title = "定货数量m",Hidden = true, SqlFld = "yj_get_unit_qty('m',sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  order_quantity else 0 end)::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"order_quantity_s",new DataItem(){Title = "定货数量s",Hidden = true, SqlFld = "yj_get_unit_qty('s',sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  order_quantity else 0 end)::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},

                         
                       {"change_quantity1",new DataItem(){Title = "调整数量(小)",Hidden = true,Width="120",SqlFld = "sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  change_quantity else 0 end)" } },
                       {"change_quantity_b",new DataItem(){Title = "调整数量B",Hidden = true, SqlFld = "yj_get_unit_qty('b',sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  change_quantity else 0 end)::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"change_quantity_m",new DataItem(){Title = "调整数量m",Hidden = true, SqlFld = "yj_get_unit_qty('m',sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  change_quantity else 0 end)::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                        {"change_quantity_s",new DataItem(){Title = "调整数量s",Hidden = true, SqlFld = "yj_get_unit_qty('s',sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  change_quantity else 0 end)::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},

                         
                       {"sale_quantity1",new DataItem(){Title = "已还数量(小)",Width="120",Hidden = false,ShowSum=true,SqlFld = "sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  sale_quantity else 0 end)*(-1)"} },
                       {"sale_quantity_b",new DataItem(){Title = "已还数量b",Hidden = true, SqlFld = "yj_get_unit_qty('b',sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  sale_quantity else 0 end)*(-1)::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"sale_quantity_m",new DataItem(){Title = "已还数量m",Hidden = true, SqlFld = "yj_get_unit_qty('m',sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  sale_quantity else 0 end)*(-1)::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"sale_quantity_s",new DataItem(){Title = "已还数量s",Hidden = true, SqlFld = "yj_get_unit_qty('s',sum(case when  u.happen_time >= '~VAR_startDay' AND u.happen_time <= '~VAR_endDay' then  sale_quantity else 0 end)*(-1)::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},

                       
                       
                       {"left_quantity_b",new DataItem(){Title = "剩余可用b",Hidden = true, SqlFld = "yj_get_unit_qty('b',(coalesce(b.order_qty,0) -(sum(case when  u.happen_time >'~VAR_endDay' then  order_quantity else 0 end )+sum(case when  u.happen_time >'~VAR_endDay' then  change_quantity else 0 end)+sum(case when  u.happen_time >'~VAR_endDay' then  sale_quantity else 0 end)))::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"left_quantity_m",new DataItem(){Title = "剩余可用m",Hidden = true, SqlFld = "yj_get_unit_qty('m',(coalesce(b.order_qty,0) -(sum(case when  u.happen_time >'~VAR_endDay' then  order_quantity else 0 end )+sum(case when  u.happen_time >'~VAR_endDay' then  change_quantity else 0 end)+sum(case when  u.happen_time >'~VAR_endDay' then  sale_quantity else 0 end)))::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"left_quantity_s",new DataItem(){Title = "剩余可用s",Hidden = true, SqlFld = "yj_get_unit_qty('s',(coalesce(b.order_qty,0) -(sum(case when  u.happen_time >'~VAR_endDay' then  order_quantity else 0 end )+sum(case when  u.happen_time >'~VAR_endDay' then  change_quantity else 0 end)+sum(case when  u.happen_time >'~VAR_endDay' then  sale_quantity else 0 end)))::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       
                       {"left_quantity1",new DataItem(){Title = "剩余可用(小)",Hidden = false,Width="120",ShowSum=true,SqlFld = "coalesce(b.order_qty,0) -(sum(case when  u.happen_time >'~VAR_endDay' then  order_quantity else 0 end )+sum(case when  u.happen_time >'~VAR_endDay' then  change_quantity else 0 end)+sum(case when  u.happen_time >'~VAR_endDay' then  sale_quantity else 0 end))" } },
                       {"total_left",new DataItem(){Title = "总剩余可用",Hidden = false,Width="120",SqlFld = " unit_from_s_to_bms(b.order_qty::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) " } },
                      //{"ought_left_quantity", new DataItem(){Title="理论剩余可用",CellsAlign="right",Hidden = true,HideOnLoad= true, Width="120",FuncDealMe=value=>{return value=="0"?"":value; },
                      //     SqlFld="sum(order_quantity)+sum(change_quantity)+sum(sale_quantity)",
                            
                      // }},

                      // {"diff_qty", new DataItem(){Title="差异",CellsAlign="right",Hidden = true,HideOnLoad= true, Width="120",FuncDealMe=value=>{return value=="0"?"":value; },
                      //     SqlFld="sum(order_quantity)+sum(change_quantity)+sum(sale_quantity)-coalesce(b.order_qty,0)",
                      // }},

                       {"order_price",   new DataItem(){Title="小单位定货价格", CellsAlign="right", Width="120",SqlFld="(case when b.order_price is null and u.real_price is null then '' when b.order_price is null and u.real_price is not null then real_price::text else b.order_price::text end)"}},
                       {"real_price",   new DataItem(){Title="可用余额", CellsAlign="right", Width="120",ShowSum=true,SqlFld = "b.balance"}},
                       {"remark", new DataItem(){Title="备注",Width="100", SqlFld="string_agg(case when u.remark<>'' then u.remark else null end,', ')"}},
                     },
                     /*QueryFromSQL=@"
from 
(
    SELECT 
        company_id ,order_sub_id,supcust_id,item_id,order_price,sum(order_qty) order_qty,sum(balance) balance 
    from 
            (   select company_id, b.prepay_sub_id order_sub_id,supcust_id,item_id, round((b.order_price/b.unit_factor)::numeric,3) order_price,round(b.quantity*b.unit_factor::numeric,2) order_qty,round(b.quantity*b.order_price::numeric,2) as balance 
                from items_ordered_balance b where company_id=~COMPANY_ID  
            )t 
    GROUP BY order_sub_id,supcust_id,item_id,order_price,company_id
) b   
right join
(
        select 
            sheet_no,m.sheet_id, m.supcust_id,item_id,0 as sale_quantity,round(sum(quantity*unit_factor)::numeric,2) order_quantity,0 as change_quantity,sum(sub_amount) sub_amount,prepay_sub_id ,m.happen_time,round((real_price/unit_factor)::numeric,4) real_price,getter_id,'定' sheet_type
        from 
             sheet_prepay m 
        left join sheet_order_item_detail d  on    M.company_id= d.company_id  and  d.sheet_id = m.sheet_id 
        where m.company_id=~COMPANY_ID  and m.approve_time is not null and red_flag is null and sheet_type = 'DH'and M.happen_time >= '~VAR_startDay' AND M.happen_time <= '~VAR_endDay'
        group by sheet_no,m.supcust_id,item_id,prepay_sub_id,m.happen_time,real_price,getter_id,sheet_type,unit_factor,m.sheet_id
        
    UNION
        
        select 
          '' sheet_no, m.sheet_id, m.supcust_id,case when ip.son_mum_item is null then d.item_id else ip.son_mum_item end item_id,round(sum(inout_flag*quantity*unit_factor)::numeric,2) sale_quantity,0 as order_quantity,0 as change_quantity,sum(sub_amount) sub_amount,order_sub_id prepay_sub_id,null as happen_time,round((real_price/unit_factor)::numeric,4) real_price,seller_id as  getter_id,'销' sheet_type
        from 
             sheet_sale_main m 
        LEFT JOIN sheet_sale_detail d on   M.company_id= d.company_id  and d.sheet_id = m.sheet_id 
        LEFT JOIN info_item_prop ip on m.company_id = ip.company_id and ip.item_id=d.item_id
        where m.company_id=~COMPANY_ID and m.approve_time is not null and red_flag is null and order_sub_id is not null and M.happen_time >= '~VAR_startDay' AND M.happen_time <= '~VAR_endDay'
        group by m.seller_id,sheet_no, m.supcust_id,m.happen_time,m.approve_time,red_flag,d.order_sub_id,real_price,getter_id,sheet_type,d.unit_factor,case when ip.son_mum_item is null then d.item_id else ip.son_mum_item end,m.sheet_id
        
    UNION
        
        select 
            sheet_no,m.sheet_id,c.supcust_id,c.item_id,0 as sale_quantity,0 as order_quantity,round(sum((now_quantity-old_quantity)*unit_factor)::numeric,2) change_quantity,sum(now_sub_amount) sub_amount,c.prepay_sub_id,m.happen_time,round((real_price/unit_factor)::numeric,4) real_price,seller_id as  getter_id,'调' sheet_type
        from 
            items_ordered_change c 
        left join sheet_item_ordered_adjust_main m on  c.company_id= m.company_id and  m.sheet_id = c.sheet_id 
        left  join info_item_multi_unit mu on mu.item_id = c.item_id and mu.unit_no = c.unit_no and c.company_id= mu.company_id
        where m.company_id=~COMPANY_ID and oper_type = 'change' and M.happen_time >= '~VAR_startDay' AND M.happen_time <= '~VAR_endDay'
        group by sheet_no,c.supcust_id,c.item_id,sheet_type,c.prepay_sub_id,m.happen_time,real_price,getter_id,sheet_type,unit_factor,m.sheet_id
                                            
    UNION
                                            
        select 
            sheet_no,m.sheet_id,c.supcust_id,c.item_id,0 as sale_quantity,0 as order_quantity,round(sum((-1)*old_quantity*unit_factor)::numeric,2) change_quantity,sum(old_sub_amount) sub_amount,c.prepay_sub_id,m.happen_time,round((real_price/unit_factor)::numeric,4) real_price,seller_id as  getter_id,'调' sheet_type
        from items_ordered_change c 
        left join sheet_item_ordered_adjust_main m on c.company_id= m.company_id   and   m.sheet_id = c.sheet_id 
        left join info_item_multi_unit mu on  c.company_id= mu.company_id and mu.item_id = c.item_id and mu.unit_no = c.unit_no
        where m.company_id=~COMPANY_ID and  oper_type = 'delete' and M.happen_time >= '~VAR_startDay' AND M.happen_time <= '~VAR_endDay'
        group by sheet_no,c.supcust_id,c.item_id,sheet_type,c.prepay_sub_id,m.happen_time,real_price,getter_id,sheet_type,unit_factor,m.sheet_id
        
    UNION
                                            
        select 
              sheet_no,m.sheet_id,c.supcust_id,c.item_id,0 as sale_quantity,0 as order_quantity,round(sum(now_quantity*unit_factor)::numeric,2) change_quantity,sum(now_sub_amount) sub_amount,c.prepay_sub_id,m.happen_time,round((real_price/unit_factor)::numeric,4) real_price,seller_id as  getter_id,'调' sheet_type
        from items_ordered_change c 
        left join sheet_item_ordered_adjust_main m on  c.company_id= m.company_id   and  m.sheet_id = c.sheet_id left 
        join info_item_multi_unit mu on  c.company_id= mu.company_id and  mu.item_id = c.item_id and mu.unit_no = c.unit_no
        where m.company_id=~COMPANY_ID and oper_type = 'add' and M.happen_time >= '~VAR_startDay' AND M.happen_time <= '~VAR_endDay'
        group by sheet_no,c.supcust_id,c.item_id,sheet_type,c.prepay_sub_id,m.happen_time,real_price , getter_id,sheet_type,unit_factor ,m.sheet_id  
) u on b.item_id = u.item_id and u.prepay_sub_id = b.order_sub_id and u.supcust_id = b.supcust_id and abs(u.real_price - b.order_price )<0.001
left join info_item_prop i on b.company_id= i.company_id   and  i.item_id = b.item_id
left join info_supcust p on  b.company_id= p.company_id   and p.supcust_id = b.supcust_id
left join info_pay_way pw on b.company_id= pw.company_id   and   pw.sub_id = b.order_sub_id
LEFT JOIN info_operator io on b.company_id= io.company_id   and io.oper_id = u.getter_id
left join 
(select 
item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                as errr(item_id int, s jsonb,m jsonb,b jsonb)
) mu on     mu.item_id = i.item_id
where b.company_id=~COMPANY_ID",
                     */

                       QueryFromSQL=@"
from 
(
    select 
            sheet_no,m.sheet_id, m.supcust_id,item_id,0 as sale_quantity,round(sum(quantity*unit_factor)::numeric,2) order_quantity,0 as change_quantity,sum(sub_amount) sub_amount,prepay_sub_id ,m.happen_time,round((real_price/unit_factor)::numeric,4) real_price,getter_id,'定' sheet_type, STRING_AGG(COALESCE(d.remark, ''), ', ') AS remark
    from 
            sheet_prepay m 
    left join sheet_order_item_detail d  on    M.company_id= d.company_id  and  d.sheet_id = m.sheet_id 
    where m.company_id=~COMPANY_ID  and m.approve_time is not null and red_flag is null and sheet_type = 'DH' and M.happen_time >= '~VAR_startDay' --AND M.happen_time <= '~VAR_endDay'
    group by sheet_no,m.supcust_id,item_id,prepay_sub_id,m.happen_time,real_price,getter_id,sheet_type,unit_factor,m.sheet_id
        
    UNION
        
    select 
        '' sheet_no, m.sheet_id, m.supcust_id,case when ip.son_mum_item is null then d.item_id else ip.son_mum_item end item_id,round(sum(inout_flag*quantity*unit_factor)::numeric,2) sale_quantity,0 as order_quantity,0 as change_quantity,sum(sub_amount) sub_amount,order_sub_id prepay_sub_id,m.happen_time as happen_time,round((real_price/unit_factor)::numeric,4) real_price,seller_id as  getter_id,'销' sheet_type,'' remark
    from 
            sheet_sale_main m 
    LEFT JOIN sheet_sale_detail d on   M.company_id= d.company_id  and d.sheet_id = m.sheet_id 
    LEFT JOIN info_item_prop ip on m.company_id = ip.company_id and ip.item_id=d.item_id
    where m.company_id=~COMPANY_ID and m.approve_time is not null and red_flag is null and order_sub_id is not null and M.happen_time >= '~VAR_startDay' --AND M.happen_time <= '~VAR_endDay'
    group by m.seller_id,sheet_no, m.supcust_id,m.happen_time,m.approve_time,red_flag,d.order_sub_id,real_price,getter_id,sheet_type,d.unit_factor,case when ip.son_mum_item is null then d.item_id else ip.son_mum_item end,m.sheet_id
        
    UNION
        
    select 
        sheet_no,m.sheet_id,c.supcust_id,c.item_id,0 as sale_quantity,0 as order_quantity,round(sum((now_quantity-old_quantity)*unit_factor)::numeric,2) change_quantity,sum(now_sub_amount) sub_amount,c.prepay_sub_id,m.happen_time,round((real_price/unit_factor)::numeric,4) real_price,seller_id as  getter_id,'调' sheet_type,'' remark
    from 
        items_ordered_change c 
    left join sheet_item_ordered_adjust_main m on  c.company_id= m.company_id and  m.sheet_id = c.sheet_id 
    left  join info_item_multi_unit mu on mu.item_id = c.item_id and mu.unit_no = c.unit_no and c.company_id= mu.company_id
    where m.company_id=~COMPANY_ID and oper_type = 'change' and M.happen_time >= '~VAR_startDay' --AND M.happen_time <= '~VAR_endDay'
    group by sheet_no,c.supcust_id,c.item_id,sheet_type,c.prepay_sub_id,m.happen_time,real_price,getter_id,sheet_type,unit_factor,m.sheet_id
                                            
    UNION
                                            
    select 
        sheet_no,m.sheet_id,c.supcust_id,c.item_id,0 as sale_quantity,0 as order_quantity,round(sum((-1)*old_quantity*unit_factor)::numeric,2) change_quantity,sum(old_sub_amount) sub_amount,c.prepay_sub_id,m.happen_time,round((real_price/unit_factor)::numeric,4) real_price,seller_id as  getter_id,'调' sheet_type,'' remark
    from items_ordered_change c 
    left join sheet_item_ordered_adjust_main m on c.company_id= m.company_id   and   m.sheet_id = c.sheet_id 
    left join info_item_multi_unit mu on  c.company_id= mu.company_id and mu.item_id = c.item_id and mu.unit_no = c.unit_no
    where m.company_id=~COMPANY_ID and  oper_type = 'delete' and M.happen_time >= '~VAR_startDay' --AND M.happen_time <= '~VAR_endDay'
    group by sheet_no,c.supcust_id,c.item_id,sheet_type,c.prepay_sub_id,m.happen_time,real_price,getter_id,sheet_type,unit_factor,m.sheet_id
        
    UNION
                                            
    select sheet_no,m.sheet_id,c.supcust_id,c.item_id,0 as sale_quantity,0 as order_quantity,round(sum(now_quantity*unit_factor)::numeric,2) change_quantity,sum(now_sub_amount) sub_amount,c.prepay_sub_id,m.happen_time,round((real_price/unit_factor)::numeric,4) real_price,seller_id as  getter_id,'调' sheet_type,'' remark
    from items_ordered_change c
    left join sheet_item_ordered_adjust_main m on  c.company_id= m.company_id   and  m.sheet_id = c.sheet_id left 
    join info_item_multi_unit mu on  c.company_id= mu.company_id and  mu.item_id = c.item_id and mu.unit_no = c.unit_no
    where m.company_id=~COMPANY_ID and oper_type = 'add' and M.happen_time >= '~VAR_startDay' --AND M.happen_time <= '~VAR_endDay'
    group by sheet_no,c.supcust_id,c.item_id,sheet_type,c.prepay_sub_id,m.happen_time,real_price , getter_id,sheet_type,unit_factor ,m.sheet_id
) u
left join
(
    SELECT 
        company_id ,order_sub_id,supcust_id,item_id,order_price,sum(order_qty) order_qty,sum(balance) balance ,order_item_sheets_no
    from 
            (   select DISTINCT company_id, b.prepay_sub_id order_sub_id,supcust_id,item_id, round((b.order_price/b.unit_factor)::numeric,4) order_price,sum(round(b.quantity*b.unit_factor::numeric,2)) order_qty,sum(round(b.quantity*b.order_price::numeric,2)) as balance ,string_agg(order_item_sheets_no,',')  order_item_sheets_no 
                from items_ordered_balance b where company_id=~COMPANY_ID
                GROUP BY company_id, b.prepay_sub_id,supcust_id,item_id,round((b.order_price/b.unit_factor)::numeric,4)
            )t 
    GROUP BY order_sub_id,supcust_id,item_id,order_price,company_id,order_item_sheets_no
) b
on b.item_id = u.item_id and u.prepay_sub_id = b.order_sub_id and u.supcust_id = b.supcust_id and abs(u.real_price - b.order_price )<0.0001
left join info_item_prop i on i.company_id =~COMPANY_ID  and  i.item_id = u.item_id
left join info_supcust p on  p.company_id=~COMPANY_ID    and p.supcust_id = u.supcust_id
left join cw_subject pw on  pw.company_id=~COMPANY_ID  and  pw.sub_id = b.order_sub_id 
LEFT JOIN info_operator io on io.company_id=~COMPANY_ID  and io.oper_id = u.getter_id
left join 
(
    select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                    as errr(item_id int, s jsonb,m jsonb,b jsonb)
) mu on  mu.item_id = i.item_id
where 1=1
",
                            QueryGroupBySQL="group by order_sub_id,prepay_sub_id,sub_name,b.supcust_id,b.order_item_sheets_no,u.supcust_id,sup_name,b.item_id,u.item_id,item_name,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no,b.order_price,b.balance,u.real_price,b.order_qty",
                           
                      QueryOrderSQL=" order by happen_time desc , supcust_id,prepay_sub_id,u.item_id"
                  }
                } 
            }; 
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {

            //string sql = $"select min(happen_time) v from sheet_prepay p left join info_pay_way pw on pw.sub_id = p.prepay_sub_id where p.company_id = {company_id} and is_order";
            //dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            //if (record != null)
            //{
            //    if(record.v!="")
            //       DataItems["startDay"].Value = record.v;
            //}
            
            
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {

            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;
            //if (DataItems["showDiffOnly"].Value == "1")
            //{
            //    //SQLVariables["having"] =" having "
            //}


        }
        public async Task OnGet()
        {  
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
    }



    [Route("api/[controller]/[action]")]
    public class ItemsOrderedSummaryController : QueryController
    { 
        public ItemsOrderedSummaryController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ItemsOrderedSummaryModel model = new ItemsOrderedSummaryModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            ItemsOrderedSummaryModel model = new ItemsOrderedSummaryModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            ItemsOrderedSummaryModel model = new ItemsOrderedSummaryModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
