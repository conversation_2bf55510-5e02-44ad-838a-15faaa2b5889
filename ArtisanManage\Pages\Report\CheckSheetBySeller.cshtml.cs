﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class CheckSheetBySellerModel : PageQueryModel
    { 
        public CheckSheetBySellerModel(CMySbCommand cmd) : base(Services.MenuId.checkSheetBySeller)
        {
            this.cmd = cmd;
            this.PageTitle = "交账汇总";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="happen_time",CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="happen_time",CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"seller_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="getter_id",SqlForOptions=CommonTool.selectSellers } },
                  {"depart_path",new DataItem(){Title="部门",FldArea="divHead",LabelFld="depart_path_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", TreePathFld="depart_path",CompareOperator="like",LikeWrapper="",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
            };


            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"seller_id", new DataItem(){Title="业务员", SqlFld="getter_id", Width="5%",Hidden=true}},
                       {"seller_name",new DataItem(){Title="业务员名称", Sortable=true, Width="5%" }},
                       {"all_income",new DataItem(){Title="总收入",Width = "5%",Sortable=true,SqlFld="sum(sale_amount)+sum(get_preget)+sum(get_prepay)+sum(get_arrears)+sum(fee_out)+sum(income)",ShowSum=true}},
                           
                       {"sale",new DataItem(){Title="销售", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                    {"sale_total_amount",    new DataItem(){Title="销售", Sortable=true,  CellsAlign="right",  Width="5%",SqlFld="sum(sale_total_amount)",ShowSum=true,Hidden=true}},
                                    {"return_amount",        new DataItem(){Title="退货", Sortable=true,  CellsAlign="right",  Width="5%",SqlFld="sum(return_amount)",ShowSum=true,Hidden=true}},
                                    {"sale_disc_amount",     new DataItem(){Title="优惠", Sortable=true, CellsAlign="right",  Width="5%",SqlFld="sum(sale_disc_amount)",ShowSum=true}},
                                    {"sale_prepay_amount",     new DataItem(){Title="使用预收款", Sortable=true, CellsAlign="right",  Width="5%",SqlFld="sum(sale_prepay_amount)",ShowSum=true}},
                                    {"sale_left_amount",     new DataItem(){Title="欠款", Sortable=true, CellsAlign="right",  Width="5%",SqlFld="sum(sale_left_amount)",ShowSum=true}},
                                    {"sale_amount",          new DataItem(){Title="实收", Sortable=true,  CellsAlign="right",  Width="5%",SqlFld="sum(sale_amount)",ShowSum=true,Hidden=true}},
                                }
                            }
                       } },

                       {"get",new DataItem(){Title="预收款", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                               {"preget_total_amount",   new DataItem(){Title="总额", Sortable=true, CellsAlign="right",  Width="5%",SqlFld="sum(preget_total_amount)",ShowSum=true}},
                               {"preget_left_amount",   new DataItem(){Title="欠款", Sortable=true, CellsAlign="right",  Width="5%",SqlFld="sum(preget_left_amount)",ShowSum=true}},
                               {"preget_disc_amount",   new DataItem(){Title="优惠", Sortable=true, CellsAlign="right",  Width="5%",SqlFld="sum(preget_disc_amount)",ShowSum=true}},
                                  
                                {"get_preget",   new DataItem(){Title="实收", Sortable=true, CellsAlign="right",  Width="5%",SqlFld="sum(get_preget)",ShowSum=true}},
                                }
                            }
                       } },

                       {"pay",new DataItem(){Title="预付款", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                 {"prepay_total_amount",  new DataItem(){Title="总额", Sortable=true, CellsAlign="right",     Width="5%",SqlFld="sum(prepay_total_amount)",ShowSum=true}},
                                 {"prepay_left_amount",  new DataItem(){Title="欠款", Sortable=true, CellsAlign="right",     Width="5%",SqlFld="sum(prepay_left_amount)",ShowSum=true}},
                                 {"prepay_disc_amount",  new DataItem(){Title="优惠", Sortable=true, CellsAlign="right",     Width="5%",SqlFld="sum(prepay_disc_amount)",ShowSum=true}},
                                {"get_prepay",   new DataItem(){Title="实收", Sortable=true, CellsAlign="right",  Width="5%",SqlFld="sum(get_prepay)",ShowSum=true}},
                                }
                            }
                       } },
                       {"arrears",new DataItem(){Title=" 收款", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                {"arrears_total_amount",  new DataItem(){Title="总额", Sortable=true, CellsAlign="right",     Width="5%",SqlFld="sum(arrears_total_amount)",ShowSum=true}},
                                {"arrears_prepay_amount",  new DataItem(){Title="预收", Sortable=true, CellsAlign="right",     Width="5%",SqlFld="sum(arrears_prepay_amount)",ShowSum=true}},
                               {"arrears_disc_amount",  new DataItem(){Title="优惠", Sortable=true, CellsAlign="right",     Width="5%",SqlFld="sum(arrears_disc_amount)",ShowSum=true}},
                               {"get_arrears",      new DataItem(){Title="实收", Sortable=true, CellsAlign="right",  Width="5%",SqlFld="sum(get_arrears)",ShowSum=true}},
                                }
                            }
                       } },

                       {"other_feeout",new DataItem(){Title="费用支出", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                {"fee_out_total_amount", new DataItem(){Title="总额", CellsAlign="right",    Width="5%", Sortable=true,SqlFld="sum(fee_out_total_amount)",ShowSum=true}},
                                {"fee_out", new DataItem(){Title="实收", CellsAlign="right",    Width="5%", Sortable=true,SqlFld="sum(fee_out)",ShowSum=true}},
                                {"fee_out_left_amount", new DataItem(){ Title = "欠款", CellsAlign = "right",Width = "5%",Sortable=true,SqlFld="sum(fee_out_left_amount)",ShowSum=true}},
                                {"fee_out_disc_amount", new DataItem(){ Title = "优惠", CellsAlign = "right",Width = "5%",Sortable=true,SqlFld="sum(fee_out_disc_amount)",ShowSum=true}},
                                }
                            }
                       } },
                       {"other_income",new DataItem(){Title="其他收入", Width="60",  FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                                {"income_total_amount",new DataItem(){ Title = "总额", CellsAlign = "right",Width = "5%",Sortable=true,SqlFld="sum(income_total_amount)",ShowSum=true}},
                                {"income",new DataItem(){ Title = "实收", CellsAlign = "right",Width = "5%",Sortable=true,SqlFld="sum(income)",ShowSum=true}},
                                {"income_left_amount",new DataItem(){ Title = "欠款", CellsAlign = "right",Width = "5%",Sortable=true,SqlFld="sum(income_left_amount)",ShowSum=true}},
                                {"income_disc_amount",new DataItem(){ Title = "优惠", CellsAlign = "right",Width = "5%",Sortable=true,SqlFld="sum(income_disc_amount)",ShowSum=true}},
                                }
                            }
                       } }
                     },
                     QueryFromSQL=@" from sheet_check_sheets_main cm
	LEFT JOIN ( SELECT oper_id, oper_name AS seller_name, depart_path FROM info_operator WHERE company_id =~COMPANY_ID ) o ON o.oper_id = cm.getter_id
    where
	cm.company_id =~COMPANY_ID
	AND cm.approve_time IS NOT NULL 
	AND cm.red_flag IS NULL",
                     QueryGroupBySQL = "group by getter_id,seller_name",
                     QueryOrderSQL=""
                  }
                } 
            };             
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            
        }


        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
           

        }

        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class CheckSheetBySellerController : QueryController
    { 
        public CheckSheetBySellerController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            CheckSheetBySellerModel model = new CheckSheetBySellerModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords(string cost_price_type_name,string sheetType)
        {
           
            CheckSheetBySellerModel model = new CheckSheetBySellerModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }
        public async Task<ActionResult> ExportExcel()
        {
            CheckSheetBySellerModel model = new CheckSheetBySellerModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }



    }
}
