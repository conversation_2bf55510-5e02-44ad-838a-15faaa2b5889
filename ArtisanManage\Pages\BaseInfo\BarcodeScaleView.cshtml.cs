using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using System.IO;
using System.Text;
using System.Data;
using System.Globalization;


namespace ArtisanManage.Pages.BaseInfo
{
    public class BarcodeScaleViewModel : PageQueryModel
    {
        CMySbCommand cmd;
        public BarcodeScaleViewModel(CMySbCommand cmd) : base(MenuId.infoBarcodeScale)
        {
            this.cmd = cmd;
            this.PageTitle = "条码秤";
            DataItems = new Dictionary<string, DataItem>()
            {
                
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                    "gridItems", new QueryGrid()
                    {
                        HasCheck=true,
                        IdColumn="scale_id",TableName="info_barcode_scale",
                        ShowContextMenu=true,
                        ContextMenuHTML="<ul><li id='edit'>编辑</li><li id='remove'>删除</li>",
                        Sortable=true,
                        Columns = new Dictionary<string, DataItem>()
                        {
                            {"scale_id",new DataItem(){Title="条码秤id", Width="200",Hidden=true,HideOnLoad=true}},
                            {"scale_no",new DataItem(){Title="条码秤编号",Width="100",CellsAlign="center"}},
                            {"scale_name",new DataItem(){Title="条码秤名称", Width="200",Sortable=true,CellsAlign="center"}},
                            {"scale_model",new DataItem(){Title="条码秤类型", Width="100",Sortable=true,CellsAlign="center"}},
                            {"barcode_scale_group_id",new DataItem(){Title="分组id",SqlFld="ibs.barcode_scale_group_id",Width="200",Sortable=true,CellsAlign="center",Hidden=true,HideOnLoad=true}},
                            {"barcode_scale_group_name",new DataItem(){Title="分组",SqlFld="ibsg.barcode_scale_group_name",Width="200",Sortable=true,CellsAlign="center"}},
                            {"bar_ip",new DataItem(){Title="ip", Width="200",Sortable=true,CellsAlign="center"}},
                            {"barcode_head2",new DataItem(){Title="前两位码", Width="100",Sortable=true,CellsAlign="center"}},
                            {"owner_store",new DataItem(){Title="所属门店", Width="200",Sortable=true,CellsAlign="center"}},
                            {"upload_result",new DataItem(){Title="下传结果", Width="200",Sortable=true,CellsAlign="center",GetFromDb=false,SaveToDB=false,
                                JSCellBeginEdit = @"function(row, datafield, columntype, value) {return true; return false; }"}},
                        },
                        QueryFromSQL=$@"FROM info_barcode_scale ibs
                                        left join  info_barcode_scale_group ibsg on ibs.barcode_scale_group_id=ibsg.barcode_scale_group_id and ibsg.company_id=~COMPANY_ID
                                         where ibs.company_id=~COMPANY_ID",

                    }
                }
            };
        }
        public async Task OnGet()
        {
            await InitGet(cmd);

        }
        public dynamic AssembleScheduleOperatorInfo(ScheduleModel schedule,List<OperModel> operInfos)
        {

            var scheduleView = new ScheduleViewModel();
            return scheduleView;
        }
       
        

    }  
    public class SteelyardBrand
    {
        public string brand_type = "";
        public Dictionary<string, SteelyardGroup> dicGroup = new Dictionary<string, SteelyardGroup>();

    }
    public class SteelyardGroup
    {
        public string group_name = ""; public string group_id = "";
        public List<System.Data.DataTable> lstRow = new List<System.Data.DataTable>();

    }
    [Route("api/[controller]/[action]")]
    public class BarcodeScaleViewController : Controller
    {
        CMySbCommand cmd;
        public BarcodeScaleViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        //}
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {// string gridID,int startRow,int endRow,bool bNewQuery){

            BarcodeScaleViewModel model = new BarcodeScaleViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }
        [HttpGet]
        public async Task<IActionResult> GetOpers(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = @$"select oper_id,oper_name from info_operator where company_id={companyID} and COALESCE(status,'1')='1';";

            dynamic opers = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            return new JsonResult(new { result = "OK", opers });
        }


        [HttpPost]
        public async Task<IActionResult> RemoveSchedule([FromBody] dynamic value)
        {
            string operKey = value.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);

        string id = "";
        string result = "OK";

            object o = null;
            cmd.CommandText = $"select day_id from info_visit_day where company_id={companyID} and schedule_id='" + id + "'";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该行程的日程后，再删除该行程"; goto end;
            }
            string sql = $"delete from info_visit_schedule where company_id={companyID} and schedule_id='{id}'";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();

        end:
            return Json(new { result, schedule_id = id });
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            //VisitDayViewModel model = new VisitDayViewModel(cmd);
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            //object records = await model.DeleteRecords(data, cmd, "info_visit_day");// gridID, startRow, endRow, bNewQuery);
            var sql = $@"DELETE from info_barcode_scale where scale_id in ({data.rowIDs}) and company_id={companyID}";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new {result= "OK"});
        }
        [HttpPost]
        public async Task<IActionResult> DownloadGroupData([FromBody] dynamic data)
        {
            try
            {
                string operKey = data.operKey;
                Security.GetInfoFromOperKey(operKey, out string companyID);
                var resultLines = new List<dynamic>();

                // 将groupData转换为List并添加索引信息
                var groupDataWithIndex = ((IEnumerable<dynamic>)data.groupData)
                    .Select(item => new {
                        Item = item,
                        RowIndex = (int)item.boundindex
                    })
                    .ToList();

                // 按groupId分组
                var groupedData = groupDataWithIndex
                    .GroupBy(x => (string)x.Item.barcode_scale_group_id);

                foreach (var group in groupedData)
                {
                    string groupId = group.Key;
                    string barcodeHead2 = (string)group.First().Item.barcode_head2;
                    string line = "";

                    // 获取PLU数据
                    string query = $@"
            SELECT info_barcode_scale_group_plu.*
            FROM info_barcode_scale_group_plu
            LEFT JOIN info_barcode_scale_group 
                ON info_barcode_scale_group_plu.barcode_scale_group_id = info_barcode_scale_group.barcode_scale_group_id
            WHERE info_barcode_scale_group.barcode_scale_group_id = '{groupId}'
            AND info_barcode_scale_group.company_id = {companyID}";

                    cmd.CommandText = query;
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (reader.Read())
                        {
                            if (line != "")
                            {
                                line += "\r\n" + GenerateDataLine(reader, barcodeHead2);
                            }
                            else
                            {
                                line = GenerateDataLine(reader, barcodeHead2);
                            }
                        }

                        // 为该组的每个秤添加结果
                        foreach (var item in group)
                        {
                            resultLines.Add(new
                            {
                                content_wr = line,
                                bar_ip = (string)item.Item.bar_ip,
                                rowIndex = item.RowIndex,
                                groupId = groupId
                            });
                        }
                    }
                }

                return Json(new
                {
                    result = "OK",
                    lines = resultLines
                });
            }
            catch (Exception ex)
            {
                return Json(new { result = "Error", msg = ex.Message });
            }
        }
        private string GenerateDataLine(CMySbDataReader dr, string preCode)
        {
            string itemNo = CPubVars.GetTextFromDr(dr, "item_no");
            double price = Convert.ToDouble(dr["retail_price"] ?? 0) * 100;
            string priceStr = CPubVars.FormatMoney(price, 0);
            string itemName = CPubVars.GetTextFromDr(dr, "item_name");
            if (itemName.Length > 12) itemName = itemName.Substring(0, 12);
            string pluCode = CPubVars.GetTextFromDr(dr, "plu_no").PadLeft(4, '0');
            string unitNo = CPubVars.GetTextFromDr(dr, "unit_no");
            string unitInfo = (unitNo != "斤" && unitNo != "公斤" && unitNo != "kg" && unitNo != "") ? "1" : "0";

            return $"1P{pluCode}A{preCode}{itemNo.PadLeft(5, '0')}{priceStr.PadLeft(6, '0')}{unitInfo}000000008{preCode}000000000000000000000020106100712201318301924400##{itemName}##{itemName}##{itemName}";
        }

    }
}

