﻿@page
@model ArtisanManage.Pages.Mall.MallNoticeEditModel
@{
    Layout = null;
}
<!DOCTYPE html>

<head>
    <meta name="viewport" content="width=device-width" />
    <title>AppendixPhotoEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <style>
        .el-upload-dragger{
            width:148px;
            height:148px;
            border-width:2px

        }

        .el-upload--picture-card :hover {
            width: 148px;
            height: 148px;
            border-color: #87CEFA
        }

        .el-upload:focus .el-upload-dragger {
            border-color: #87CEFA
        }
        .el-upload--picture-card{
            border:none
        }
/*          #uploadArea{
             margin:10px
         } */
        #hintText{
            color: #787878;
            font-size: 14px;

        }
        #bottomArea {
            bottom: 15px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        #btnArea {
            display: flex;
            margin-bottom:15px
        }
        #saveBtn{
            background-color: #409eff;
            border-color: #409eff;
            margin-right:15px;
            color:#fff;
        }
        #saveBtn:hover{
            background-color: #66b1ff;
            border-color: #66b1ff;
        }

        #saveBtn:active{
            background-color: #3a8ee6;
            border-color: #3a8ee6;
        }
        #closeBtn{
           /* margin-left:15px*/
        }
        .myButton{
            display: flex;
            align-items: center; /* 垂直居中 */
            justify-content: center; /* 水平居中 */
            height: 36px; /* 按钮高度，可根据需要调整 */
            padding: 0 15px; /* 内边距，控制左右间距 */
            font-size: 14px; /* 字体大小 */
            border-radius: 4px; /* 圆角样式 */
            margin: 0px 15px
        }
        html, body {
            height: 100%; /* 确保根容器有高度 */
            margin: 0; /* 移除默认间距 */
        }

        #app {
            height: 100%; /* 确保子容器占满父容器 */
            overflow-y: auto;
        }
        

        .el-form-item {
            width: 90%;
            margin: 20px 5%
        }

        .el-switch.is-checked .el-switch__core {
            border-color: #409EFF;
            background-color: #409EFF;
        }
        /* 以下两条设置是为了覆盖引入的button样式对时间选择器引发的影响 */
        .el-picker-panel__icon-btn{
            margin-top: 0
        }

        .el-time-panel button, .el-picker-panel__content button { 
            width:auto;
            height:auto;
        }

        .el-date-table td.end-date span, .el-date-table td.start-date span {
            background-color: #409EFF
        }

        .el-checkbox__input.is-checked .el-checkbox__inner {
            background-color: #409EFF;
            border-color: #409EFF;
        }
        .scrolling-banner {
          width: 300px; /* 设置容器宽度 */
          height: 40px; /* 设置容器高度 */
          overflow: hidden; /* 隐藏超出容器的内容 */
          border: none; /* 可选：添加边框 */
          position: relative;
          display: flex;
          align-items: center
        }
        
        .banner-content {
          white-space: nowrap; /* 防止文字换行 */
          animation: scroll 10s linear infinite; /* 设置滚动动画 */
          width: auto;
          font-size: 18px;
        }

        .tree-select-dropdown {
            max-height: 240px;
            overflow-y: auto;
            padding: 8px;
            background: #fff;
        }

        .tree-select-dropdown::-webkit-scrollbar {
            width: 8px; /* 滚动条宽度 */
        }

        .tree-select-dropdown::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.3); /* 滚动条滑块颜色 */
            border-radius: 4px;
            opacity: 0; /* 初始隐藏 */
            transition: opacity 0.3s;
        }

        .tree-select-dropdown:hover::-webkit-scrollbar-thumb {
            opacity: 1; /* 悬浮时显示 */
        }

        .tree-select-dropdown::-webkit-scrollbar-track {
            background: transparent; /* 滑轨背景 */
        }

        /* 隐藏滚动条 */
         #app::-webkit-scrollbar {
             display: none; /* 针对 Webkit 浏览器 (Chrome, Edge, Safari) 隐藏滚动条 */
         }

         #app::-webkit-scrollbar {
             width: 16px; /* 滚动条宽度 */
         }

        .multi_select .el-select, .multi_select .el-input {
            display: inline-block;
            position: relative;
            width: 400px;
        }

        .single_select .el-select,  {
            display: inline-block;
            position: relative;
            width: 200px;
        }

       
        @@keyframes scroll {
          0% {
            transform: translateX(310px); /* 从容器右边开始,容器宽度是300px,多设置10px让滚动出现不突兀 */
          }
          100% {
            transform: translateX(-100%); /* 滚动到容器左边 */
          }
        }
    </style>
</head>

<body>
    <div id="app">
        @* submit.native.prevent="() => {}防止表单回车键自动提交 *@
        <el-form ref="form" :model="form" label-width="110px" @@submit.native.prevent="() => {}" :rules="rules" :disabled="!canEditNotice">
            <el-form-item label="公告说明" prop="remark" 
            >
                <el-input v-model="form.remark" placeholder="请输入公告说明，可用于检索公告"></el-input>
            </el-form-item>
            <el-form-item label="公告类型" prop="type" class="single_select">
                <el-select v-model="form.notice_type" placeholder="请选择公告类型" @@change="validateNoticeContent">
                    <el-option v-for="item in typeOptions"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value">
                    </el-option>
                    
                </el-select>
            </el-form-item>
            <el-form-item label="横幅文字" v-if="form.notice_type ==='banner'" prop="banner"
                          >
                <el-input v-model="banner" placeholder="请输公告横幅文字内容"></el-input>
            </el-form-item>
            <el-form-item label="公告背景颜色" v-if="form.notice_type ==='banner'" prop="background"
                          >
                <el-color-picker v-model="background"></el-color-picker>
            </el-form-item>
            <el-form-item label="公告文字颜色" v-if="form.notice_type ==='banner'" prop="color"
                          >
                <el-color-picker v-model="color"></el-color-picker>
            </el-form-item>
            <el-form-item label="公告效果预览" v-if="form.notice_type ==='banner'" >
                <div class="scrolling-banner" :style="containerStyle">
                    <div class="banner-content" :style="contentStyle" ref="bannerContent">
                        {{ banner? banner:"默认横幅内容" }}
                    </div>
                </div>
            </el-form-item>
            <el-form-item id="uploadArea" label="公告图片" v-if="form.notice_type ==='popupImg'" prop="image"
                          >
                <el-upload action="#"
                           list-type="picture-card"
                           drag
                           :disabled="disableUpload"
                           accept=".jpeg,.png,.jpg"
                           :limit="1"
                           :on-exceed="handleExceed"
                           :auto-upload="false"
                           :on-change="onFileChange"
                           :file-list="fileList">
                    <i class="el-icon-upload"></i>

                    <div slot="file" slot-scope="{file}">
                        <img class="el-upload-list__item-thumbnail"
                             :src="file.url" alt="">
                        <span class="el-upload-list__item-actions">
                            <span class="el-upload-list__item-preview"
                                  v-on:click="handlePreview(file)">
                                <i class="el-icon-zoom-in"></i>
                            </span>
                            @* <span v-if="!disabled"
                            class="el-upload-list__item-delete"
                            v-on:click="handleDownload(file)">
                            <i class="el-icon-download"></i>
                            </span> *@
                            <span v-if="!disableRemove"
                                  class="el-upload-list__item-delete"
                                  v-on:click="handleRemove(file)">
                                <i class="el-icon-delete"></i>
                            </span>
                        </span>
                    </div>
                </el-upload>
                  @* 图片预览弹出框 *@
                <el-dialog :visible.sync="dialogVisible" width="1000px">
                    <img width="950px" :src="dialogImageUrl" alt="">
                </el-dialog>
            </el-form-item>
            
@*  自定义的带下拉树的选择，可以过滤以及多选           
    <el-form-item label="片区" class="multi_select">
                <el-select v-model="form.regions"
                           placeholder="请选择节点"
                           :popper-class="'tree-select-dropdown'"
                           filterable
                           :filter-method="setFilter"
                           multiple
                @@visible-change="onDropdownVisibleChange">
                    <!-- 自定义下拉内容 -->
                    <template #empty>
                        <div class="tree-dropdown">
                            <el-tree :data="treeData"
                                     :props="defaultProps"
                                     ref="tree"
                                     node-key="id"
                                     show-checkbox
                                     highlight-current
                                     default-expand-all
                                     check-strictly
                                     :filter-node-method="filterTree"
                                     @@check-change="handleCheckChange"></el-tree>
                        </div>
                    </template>
                </el-select> 
            </el-form-item>*@
            <el-form-item label="跳转选项" class="single_select">
                <el-select v-model="redirectType" placeholder="请选择跳转对象" filterable clearable>
                    <el-option v-for="item in redirectOptions"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="跳转商品" v-if="redirectType === 'item'" prop="item" class="single_select">
                <el-select v-model="redirectItem" placeholder="请选择跳转的商品" filterable clearable>
                    <el-option v-for="item in itemOptions"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="公告优先级" 
                          prop="order" class="multi_select">
                <el-input v-model.number="form.order_index" autocomplete="off" placeholder="请输入一个正整数代表优先级，数字越小优先级越高"></el-input>
            </el-form-item>
            <el-form-item label="公告展示时间" prop="time">
                <el-date-picker v-model="form.time_range"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始时间"
                                end-placeholder="结束日时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="片区" class="multi_select">
                <el-select v-model="form.regions" placeholder="请选择展示片区" multiple filterable clearable>
                    <el-option v-for="item in regionOptions"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="渠道" class="multi_select">
                <el-select v-model="form.sup_group" placeholder="请选展示渠道" 
                multiple
                           filterable clearable>
                    <el-option v-for="item in groupOptions"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="等级" class="multi_select">
                <el-select v-model="form.sup_rank" placeholder="请选择展示等级" 
                multiple
                           filterable clearable>
                    <el-option v-for="item in rankOptions"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            
            <el-form-item label="阅后重复提醒">
                <el-switch v-model="form.repeat_remind"
                           active-text="开启"
                           inactive-text="关闭"></el-switch>
            </el-form-item>
            <el-form-item label="公告状态" class="single_select">
                <el-switch v-model="form.status"
                           active-text="正常"
                           inactive-text="停用">
                </el-switch>
            </el-form-item>
           @*  <el-form-item label="公告内容">
                <el-input type="textarea" v-model="form.content"></el-input>
            </el-form-item> *@
        </el-form>
        
        <div id="bottomArea">
            <div id="btnArea">
                @* 不指定type为button浏览器回车会触发按钮点击操作 *@
                <button id="saveBtn" class="myButton"  type="button" @@click="submitForm" :disabled="isSubmitting || (!canEditNotice)">保存</button>
                <button id="closeBtn" class="myButton" onclick="btnClose()" type="button" :disabled="isSubmitting">关闭</button>
            </div>
            
        </div>
    </div>


    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var Href = '@Html.Raw(Model.ObsBucketLinkHref)'
        window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);	//先配一下权限变量
        @Html.Raw(Model.m_saveCloseScript)
            $(document).ready(function () {

             @Html.Raw(Model.m_showFormScript);
             @Html.Raw(Model.m_createGridScript);
                    // if(editable === 'false') {
                    // // 如果指定了单据，要隐藏上传组件，上传组件在document加载好之后才能通过类名检索
                    //     $('.el-upload-dragger').css('display', 'none')
                    //     $('#btnArea').css('display','none')
                    // }

        })

        window.addEventListener('message', function (event) {
            console.log("wojieshoulexiaoxi")
            debugger
            // 确保消息来自父页面
            if (event.data.msg !== 'MallNoticeView') {
                return;
            } if (event.data.msg === 'MallNoticeView') {
                // debugger
                // $('.el-upload-dragger').css('display', 'none')
                // $('#btnArea').css('display', 'none')
                // vm.disableUpload = true
                // vm.disableRemove = true
            }
            // 获取发送的对象数据
            // let href = event.data.href
            // processLoadImg(event.data.photo,href)


        });
        var vm = new Vue({
            el: '#app',
            data(){
                return {
                    canEditNotice: true,
                    isSubmitting: false,
                    form: {
                        notice_id: '',
                        creator:'',
                        remark: '',
                        //region: '',
                        time_range: [],
                        regions: [],
                        sup_group: [],
                        sup_rank: [],
                        repeat_remind: false,
                        notice_type: '',
                        status: true,
                        order_index: null,
                        notice_type: '',
                        redirect_target: { type: '', item_id: '' },
                        notice_content: { 'banner': '', 'background': '', 'color': '' }
                    },
                    fileList: [],
                    banner: '',
                    color: '#409EFF',
                    background: '#D9ECFF',
                    scrollSpeed: 50,   // 固定的滚动速度（每秒移动的像素数）
                    animationDuration: 0, // 动态计算动画时长
                    disableUpload: false,
                    disableRemove: false,
                    dialogVisible: false,
                    dialogImageUrl: '',
                    disabled: false,
                    regionSearchWords: '',
                    origFileList: [],
                    rankOptions: [],
                    regionOptions: [],
                    groupOptions: [],
                    typeOptions: [{
                        value: 'banner',
                        label: '横幅'
                    }, {
                        value: 'popupImg',
                        label: '弹窗图片'
                    }],

                    treeData: [],
                    defaultProps: {
                        children: "children",
                        label: "label",
                    },
                    redirectItem: '',
                    redirectOptions: [{
                        value: 'item',
                        label: '商品'
                    },],
                    redirectType:'',

                    rules: {
                        image: [{
                            required: true, validator: this.imageValidate, trigger: 'change'
                        }],
                        color: [{
                            required: true, validator: this.colorValidate, trigger: 'change'
                        }],
                        background: [{
                            required: true, validator: this.backgroundValidate, trigger: 'change'
                        }],
                        banner: [{
                            required: true, validator: this.bannerValidate, trigger: 'change'
                        }],
                        type: [{
                            required: true, validator: this.typeValidate, trigger: 'change'
                        }],
                        remark: [{
                            required: true, validator: this.remarkValidate, trigger: 'blur'
                        }],
                        order: [{
                            required: true, validator: this.orderValidate, trigger: 'blur'
                        }],
                        item: [{
                            required: true, validator: this.itemValidate, trigger: 'change'
                        }],
                        time: [{
                            required: true, validator: this.timeValidate, trigger: 'change'
                        }],
                    },
                }
                
   
            },
            computed: {
                // 动态计算 inputStyle
                containerStyle() {
                    return {
                        color: this.color, // 文字颜色
                        backgroundColor: this.background, // 背景颜色
                        border: 'none',
                        height: '40px',
                        //padding: '5px 10px '
                    };
                },
                contentStyle() {
                    return {
                        animationDuration: `${this.animationDuration}s`, // 动态计算滚动速度
                    }
                },
               
                
            },
            watch: {
                // 监听 banner 内容的变化
                banner: function () {
                    this.updateAnimationDuration(); // 内容变化时更新动画时长
                },
                // 监听片区输入关键词的变化，触发下拉树的节点过滤
                regionSearchWords(val) {
                    this.$refs.tree.filter(val);
                },
                fileList(val) {
                    Vue.nextTick(() => { // 等待 DOM 更新完成后再执行，不然刚加载的时候找不到元素
                        console.log($('.el-upload.el-upload--picture-card'))
                        if (val.length === 1) {
                            this.disableUpload = true
                            $('.el-upload.el-upload--picture-card').hide();
                        } else {
                            this.disableUpload = false
                            $('.el-upload.el-upload--picture-card').show();
                        }
                    });
                }
            },
            mounted() {
                // 页面加载时立即计算动画时长
                this.updateAnimationDuration();
                // 初始化一个时间区间
                this.initNoticeTimeInterval();
            },
            methods: {
                // 提交
                submitForm(formName) {
                    let formValid = this.validateForm()
                    if (!formValid) {
                        return
                    }
                    
                    let notice = {
                        notice_id: this.form.notice_id,
                        notice_type: this.form.notice_type,
                        notice_content: this.getNoticeContent(),
                        show_after_read: this.form.repeat_remind,
                        order_index: this.form.order_index,
                        redirect_target: this.getRedirectTarget(),
                        show_start_time: this.getSqlTime(this.form.time_range[0]),
                        show_end_time: this.getSqlTime(this.form.time_range[1]),
                        status: this.form.status ? '1' :'0',
                        groups_id: this.form.sup_group.join(','),
                        ranks_id: this.form.sup_rank.join(','),
                        regions_id: this.form.regions.join(','),
                        creator: this.form.creator,
                        remark:this.form.remark,
                    }
                    let data = {
                        operKey: g_operKey,
                        notice: notice
                    }
                    this.isSubmitting = true
                    $.ajax({
                        url: '/api/MallNoticeEdit/Save',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(data),
                        success: function (data) {
                            if (data.result === 'OK') {
                                // this.isSubmitting = false
                                let action = notice.notice_id ? 'update':'add'
                                btnSaveNotice(data.record, action)
                            }
                            else {
                                

                                bw.toast(data.msg, 3000);
                            }
                        },
                        error: function (xhr) {
                            console.log("返回响应信息：" + xhr.responseText);
                        }
                    });

                },
                getNoticeContent(){
                    let content = {}
                    if (this.form.notice_type === 'banner') {
                        content.banner = this.banner
                        content.background = this.background
                        content.color = this.color
                    } else if (this.form.notice_type === 'popupImg') {
                        let file = this.fileList[0]
                        content.popupImg = {
                            src: file.src, // 如果没有直接的 src，通常用 url
                            name: file.name || "" //
                        };
                    }
                    // 拓展别的notice类型
                    return content
                },
                getRedirectTarget() {
                    let target = { item_id: '', type: '' }
                    if (this.redirectType === 'item') {
                        target.item_id = this.redirectItem,
                        target.type = 'item'
                    }
                    // 拓展别的notice类型
                    return target
                },
                getSqlTime (timeStr) {
                    const date = new Date(timeStr);

                    // 格式化为 PostgreSQL 时间戳格式
                    const pgTimestamp = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ` +
                        `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;

                    return pgTimestamp
                },
                // 整体校验
                validateForm() {
                    isValid = true
                    this.$refs['form'].validate((valid) => {
                        if (!valid) {
                           isValid = false
                        } 
                    });
                    return isValid
                },
                // 触发公告内容相关校验，在公告类型变化时触发
                validateNoticeContent() {
                    let propList = ['image','banner','background','color']
                    propList.forEach(prop => {
                        this.$refs.form.validateField(prop, (valid) => {
                        });
                    });
                },
                // 说明文字校验
                remarkValidate(rule, value, callback) {
                    if (!this.form.remark) {
                        callback(new Error('必须填写说明文字'));

                    } else {
                        callback(); // 校验通过
                    }
                },
                // 优先级校验
                orderValidate(rule, value, callback) {
                    if (this.form.order_index === undefined || this.form.order_index === null) {
                        callback(new Error('必须填写公告优先级'));

                    } // 检查 order_index 是否为正整数
                    else if (!Number.isInteger(Number(this.form.order_index)) || this.form.order_index <= 0) {
                        callback(new Error('公告优先级必须是正整数'));
                    } else {
                        callback(); // 校验通过
                    }
                },
                // 公告类型校验
                typeValidate(rule, value, callback) {
                    if (!this.form.notice_type) {
                        callback(new Error('必须选择公告类型'));

                    } else {
                        callback(); // 校验通过
                    }
                },
                // 图片文件的校验
                imageValidate(rule, value, callback) {
                    if (this.form.notice_type === 'popupImg' && this.fileList.length === 0) {
                        callback(new Error('必须上传弹窗图片'));
                    } else {
                        callback(); // 校验通过
                    }
                },
                // 横幅文字校验
                bannerValidate(rule, value, callback) {
                    if (this.form.notice_type === 'banner' && !this.banner) {
                        callback(new Error('必须填写横幅内容'));
                        
                    } else {
                        callback(); // 校验通过
                    }
                },
                // 背景颜色校验
                backgroundValidate(rule, value, callback) {
                    if (this.form.notice_type === 'banner' && !this.background) {
                        callback(new Error('必须选择横幅背景颜色'));

                    } else {
                        callback(); // 校验通过
                    }
                },
                // 文字颜色校验
                colorValidate(rule, value, callback) {
                    if (this.form.notice_type === 'banner' && !this.color) {
                        callback(new Error('必须选择横幅文字颜色'));

                    } else {
                        callback(); // 校验通过
                    }
                },
                // 跳转商品校验
                itemValidate(rule, value, callback) {
                    if (!this.redirectItem && this.redirectType === 'item') {
                        callback(new Error('必须选择跳转商品'));

                    } else {
                        callback(); // 校验通过
                    }
                },
                // 公告时间校验
                timeValidate(rule, value, callback) {
                    console.log('时间区间：' + this.form.time_range)
                    if (!this.form.time_range || this.form.time_range.length !== 2) {
                        callback(new Error('必须选择公告起止时间'));

                    } else {
                        callback(); // 校验通过
                    }
                },
                // 初始化时间
                initNoticeTimeInterval() {
                    const now = new Date(); // 当前时间
                    const oneWeekLater = new Date(now);
                    oneWeekLater.setDate(now.getDate() + 7); // 一周后
                    oneWeekLater.setHours(23, 59, 59, 0); // 设置为23:59:59

                    this.form.time_range = [now, oneWeekLater];
                    
                },
                handleCheckChange(node, checked, halfChecked) {
                    debugger
                    // 当树节点的选中状态发生变化时，更新 formData.region 数组
                    // checkedKeys 是所有被勾选的节点的 id 数组
                    if (checked) {
                        // 如果节点不在数组中，则添加
                        if (!this.form.regions.includes(node.label)) {
                            this.form.regions.push(node.label);  // 可以根据需求使用 node.id 或 node.label
                        }
                    } else {
                        // 如果当前节点取消选中，则从数组中移除
                        const index = this.form.regions.indexOf(node.label);
                        if (index !== -1) {
                            this.form.regions.splice(index, 1);
                        }
                    }
                },
                setFilter (value, data) {
                    // 设置片区输入的关键词，用来触发watch
                    if (!value) return true;
                    this.regionSearchWords = value
                },
                filterTree(value, data) {
                    // 过滤树节点
                    if (!value) return true;
                    return data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1;
                     
                    
                },
                handleNodeClick(node) {
                    // 在树节点点击时，将节点的 label 或 id 添加到 formData.region 数组中
                    if (!this.form.regions.includes(node.label)) {
                        this.form.regions.push(node.label);  // 以 label 为例，添加节点的 label
                    }

                    // 可选：如果希望选中后直接关闭下拉框
                   //  this.$refs.select.blur();
                },
                onDropdownVisibleChange(visible) {
                    if (!visible) {
                        // 下拉框关闭时清空搜索
                        this.regionSearchWords = "";
                        
                    }
                },
                // 计算动画时长
                updateAnimationDuration() {
                    const contentWidth = this.calculateContentWidth(); // 计算文本内容宽度
                    this.animationDuration = ( 300 + contentWidth) / this.scrollSpeed; // 根据内容宽度和速度计算动画持续时间
                },
                calculateContentWidth() {
                    const el = this.$refs.bannerContent; // 通过 ref 获取元素
                    if (el) {
                        return el.offsetWidth; // 获取元素的宽度
                    }
                    return 0;
                },
                handleRemove(file) {
                    console.log(this.disableUpload)
                    this.fileList.splice(index, 1);
                    if (this.fileList.length < 1) {
                        this.disableUpload = false
                        $('.el-upload.el-upload--picture-card').show();
                    }
                    this.$refs['form'].validateField('image'); // 手动触发文件上传框验证
                },

                handlePreview(file) {
                    this.dialogImageUrl = file.url;
                    this.dialogVisible = true;
                },
                handleDownload(file) {
                    console.log(file);
                },
                onFileChange(file, fileList) {
                    // 过滤非图片文件，好像accept可以直接过滤
                    // 不知道为什么删除文件没触发
                    let fileType = file.raw.type
                    if (fileType !== "image/jpeg" && fileType !== "image/jpg" && fileType !== "image/png") {
                        this.$message.error('不支持上传非图片文件');
                        // 移除文件
                        let index = fileList.findIndex(item => item.uid === file.uid);
                        this.fileList.splice(index, 1);
                        return false
                    }
                    let reader = new FileReader();
                    reader.onload = () => {
                        // 将文件的 base64 编码存储到 fileUrlList 中
                        // this.fileUrlList.push(reader.result);
                        file.raw.src = reader.result
                        console.log("get src")
                    };

                    reader.readAsDataURL(file.raw);

                    // 将文件对象存储到 fileList 中

                    let url = URL.createObjectURL(file.raw);
                    file.raw.url = url
                    this.fileList.push(file.raw)
                    console.log("push raw")
                    // if (this.fileList.length === 1) {
                    //     // 上传一张图片后禁用上传
                    //     this.disableUpload = true
                    //     // $('.el-upload-dragger').css('display', 'none')
                    //     $('.el-upload.el-upload--picture-card').hide();
                    // } else if (this.fileList.length < 1) {
                    //     // 删除图片解禁用
                    //     this.disableUpload = false
                    //     $('.el-upload.el-upload--picture-card').show();
                    // }
                    this.$refs['form'].validateField('image'); // 触发校验
                },
                beforeUpload(file) {
                    console.log(file.type)
                },
                handleExceed(files, fileList) {
                    this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
                },
                btnClose() {
                    debugger
                    // vm.fileList = [...vm.origFileList]; // 复制数组
                    // if (vm.fileList.length < 5) {
                    //     vm.disableUpload = false
                    // }
                    // let fileSrcList = vm.fileList.map(file => file.src).filter(src => src);
                    // var msg = { msgHead: 'AppendixPhotoEdit', action: 'close', appendix: vm.fileList, src: fileSrcList };
                    //
                    // window.parent.postMessage(msg, '*');
                }
            }



                    });
        // 检查有没有编辑权限
        if (window.getRightValue('setting.mallNotice.edit').toLowerCase() === 'false') {
            vm.canEditNotice = false
            const saveBtn = document.getElementById('saveBtn');
                if (saveBtn) {
                    // 隐藏或删除元素
                    saveBtn.style.display = 'none';
                }
        }
        let notice_id = getQueryVariable("notice_id")
        
        
        //指定了notice_id 查询公告详情
        $.ajax({
            url: '/api/MallNoticeEdit/GetMallNotice',
            type: 'GET',
            contentType: 'application/json',
            data: { operKey: g_operKey, notice_id: notice_id },
            success: function (ret) {
                // console.log(data)
                if (ret.result === "OK") {
                    debugger
                    let notice_info = ret.data
                    vm.groupOptions = ret.groups
                    vm.rankOptions = ret.ranks
                    vm.regionOptions = ret.regions.filter(region => region.pv !== '0'); // 过滤掉全部这个节点
                    vm.itemOptions = ret.items
                    // 第一版采用展平显示，不显示树结构
                    // vm.treeData = processRegionTree(ret.regions)
                    if (notice_id) {
                        // 加载notice信息
                        initNoticeInfo(ret.notice)
                    }
                }

            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText)
            }
        });

        // 加载公告时初始化获取的数据
        function initNoticeInfo(notice) {
            debugger
            vm.form.notice_id = notice.notice_id || '';
            vm.form.remark = notice.remark || '';
            vm.form.time_range = [new Date(notice.show_start_time.replace(" ", "T")), new Date(notice.show_end_time.replace(" ", "T"))] || [];  // 替换空格为T以符合ISO标准
            vm.form.regions = notice.regions_id ? notice.regions_id.split(",") : [];
            vm.form.sup_group = notice.groups_id ? notice.groups_id.split(",") : [];
            vm.form.sup_rank = notice.ranks_id ? notice.ranks_id.split(",") : [];
            vm.form.repeat_remind = notice.show_after_read.toLowerCase() === 'true' ? true : false;
            vm.form.notice_type = notice.notice_type || '';
            let content = JSON.parse(notice.notice_content)
            let href = Href + '/uploads'
            vm.fileList = notice.notice_type === 'popupImg' ? [{ src: href + content.popupImg, url: href + content.popupImg }] : [];
            vm.banner = notice.notice_type === 'banner' ? content.banner : '';
            vm.background = notice.notice_type === 'banner' ? content.background : '#D9ECFF';
            vm.color = notice.notice_type === 'banner' ? content.color : '#409EFF';
            vm.form.status = notice.status === '1' ? true : false;
            vm.form.order_index = parseInt(notice.order_index) || '';
            let target = JSON.parse(notice.redirect_target)
            vm.form.redirect_target = target || { type: '', item_id: '' };
            vm.redirectType = target.type ? target.type : '';
            vm.redirectItem = target.type === 'item' ? target.item_id : ''; 
            vm.form.notice_content = notice.notice_content || { banner: '', background: '', color: '' };
        }
          
        function processRegionTree(data) {
            // 找到最小的 id 作为根节点的父 id,可能总为0
            const tree = [];
            const map = {};
            let rootId = Math.min(...data.map((item) => parseInt(item.pv)))

            // 创建一个节点的映射
            data.forEach((item) => {
                map[item.value] = { id:item.value, label:item.label, children: [] };
            })

            // 构建树
            data.forEach((item) => {
                let node = map[item.value]
                if (parseInt(item.pv) === rootId) {
                    tree.push(node)
                } else if (map[item.pv]) {
                    map[item.pv].children.push(node)
                }
                
            })
            return tree
        }

        function processLoadImg(photoStr) {
            debugger
            let loadphoto = JSON.parse(photoStr)
            if (loadphoto !== '' && loadphoto !== null && loadphoto !== undefined) {
                for (let p of loadphoto) {
                    let photoUrl = href + p
                    vm.origFileList.push({ uid: uid, url: photoUrl, src: photoUrl })
                    uid++
                }
            }

            vm.fileList = [...vm.origFileList]; // 复制数组
            if (vm.fileList.length === 5) {
                vm.disableUpload = true
            }
            
        }
        function btnSaveNotice(notice, action) {
            // 把照片加入msg里
            // vm.origFileList = [...vm.fileList];
            // 
            // let fileSrcList = vm.fileList.map(file => file.src).filter(src => src);
            // 检查是否是鼠标点击触发的，防止连续回车触发提交或取消
            debugger
            if (event.detail === 0) {
                console.log('Triggered by keyboard, ignoring.');
                return;
            }
            vm.isSubmitting = false
            let notice_id = notice.notice_id
            
            var msg = { msgHead: 'NoticeEdit', action: action,notice:notice  };

            window.parent.postMessage(msg, '*');
        }

        function btnClose() {

            debugger
            console.log(window.g_operRights)
            // 检查是否是鼠标点击触发的，防止连续回车触发提交或取消
            if (event.detail === 0) {
                console.log('Triggered by keyboard, ignoring.');
                return;
            }
            // vm.fileList = [...vm.origFileList]; // 复制数组
            // if (vm.fileList.length < 5) {
            //     vm.disableUpload = false
            // }
            // let fileSrcList = vm.fileList.map(file => file.src).filter(src => src);
            var msg = { msgHead: 'NoticeEdit', action: 'close'};
            // 
            window.parent.postMessage(msg, '*');
        }

        function getQueryVariable(variable) {
            debugger
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) { return pair[1]; }
            }
            return '';
        }



    </script>


</body>


</html>