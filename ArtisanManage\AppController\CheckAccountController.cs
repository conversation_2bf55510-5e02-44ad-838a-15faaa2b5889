﻿//using ArtisanManage.Models;
//using ArtisanManage.Services;
//using Microsoft.AspNetCore.Mvc;
//using System;
//using System.Collections.Generic;
//using System.Dynamic;
//using System.Linq;
//using System.Threading.Tasks;


//namespace ArtisanManage.AppController
//{
//    [Route("AppApi/[controller]/[action]")]
//    public class CheckAccountController : QueryController
//    {


//        public CheckAccountController(CMySbCommand cmd)
//        {
//            this.cmd = cmd;
//        }

//        [HttpPost]
//        public async Task<JsonResult> CheckAccount(string oper_id, string company_id)
//        {
//            SQLQueue QQ = new SQLQueue(cmd);
//            var dr = await QQ.ExecuteReaderAsync();
//            string sql = $@"SELECT sheet_id approve_time and red_flag from sheet_check_sheets_main
//              where company_id={company_id} and getter_id={oper_id}
//                AND approve_time >= DATEADD(DAY, DATEDIFF(DAY, 0, GETDATE()), 0) 
//                AND approve_time < DATEADD(DAY, DATEDIFF(DAY, 0, GETDATE()) + 1, 0) 
//           ";

//            while (QQ.Count > 0)
//            {
//                var sqlName = QQ.Dequeue();
//                var sheet_id = new List<ExpandoObject>();
//                var red_flag = new List<ExpandoObject>();

//                if (sqlName == "sheet_id")
//                {
//                    sheet_id = CDbDealer.GetRecordsFromDr(dr, false);
//                }
//                else if (sqlName == "red_flag")
//                {
//                    red_flag = CDbDealer.GetRecordsFromDr(dr, false);
//                }
//                if (sheet_id != null && red_flag == null)
//                {
//                    return Json(new
//                    {
//                        cannewsheet = "OK"
//                    });
//                }
//            }
//        }
//    }
//}