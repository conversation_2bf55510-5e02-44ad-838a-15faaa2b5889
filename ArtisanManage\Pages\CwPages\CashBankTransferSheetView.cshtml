﻿@page
@model ArtisanManage.Pages.CwPages.CashBankTransferSheetViewModel
@{
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var newCount = 1;
        var itemSource = {};
        $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {
                    // event arguments.
                    var args = event.args;
                    console.log(args.row);
                    if (args.datafield == "sheet_no") {
                        var sheet_id = args.row.bounddata.sheet_id;
                        window.parent.newTabPage('转账单', `CwPages/CashBankTransferSheet?sheet_id=${sheet_id}`);
                    }
                });

            $("#gridItems").jqxGrid('beforeRowRender', function (divRow, rowData) {
                if (rowData.sheet_status == '已红冲')
                    divRow.style.color = '#aaa'
                else if (rowData.sheet_status == '红字单')
                    divRow.style.color = '#f00'
                else
                    divRow.style.color = '#000'
            })

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            QueryData();
        });

    </script>
</head>

<body>

    <div style="display:flex;padding-top:20px;">

        <div id="divHead" class="headtail">

            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px">导出</button>
    </div>


    <div id="gridItems" style="margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 95px);"></div>
    <div id="divRowCount"><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>


    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>