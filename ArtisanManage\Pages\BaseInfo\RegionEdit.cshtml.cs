using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Text.RegularExpressions;

namespace ArtisanManage.Pages.BaseInfo
{
    public class RegionEditModel :  PageFormModel
    {  
        public string m_sheet_no { get; set; } 
        public RegionEditModel(CMySbCommand cmd) : base(Services.MenuId.infoClient)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"region_id",new DataItem(){Title="编号",FldArea="divHead",CtrlType="hidden"}},
                {"region_name",new DataItem(){Title="片区名称",FldArea="divHead"}},
                {"mother_id",new DataItem(){Title="上级片区", LabelFld="mother_name",CtrlType="jqxDropDownTree",MumSelectable=true,Necessary=true, FldArea="divHead",TreePathFld="mother_region_path",TreePathFromDb=false,
                   SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region order by order_index , region_id"} },
                {"order_index",new DataItem(){Title="顺序号",FldArea="divHead"}},
                //{"other_region",new DataItem(){Title="片区路径",CtrlType="hidden"} },
            };
            m_idFld = "region_id"; m_nameFld = "region_name";
            m_tableName = "info_region";
            m_selectFromSQL = "from info_region left join (select region_id as my_mother_id,region_name as mother_name from info_region) tb_mother on info_region.mother_id=tb_mother.my_mother_id where region_id='~ID'";
        }
        public async Task OnGet()
        { 
            await InitGet(cmd);
        }    
    }


    [ApiController]
    [Route("api/[controller]/[action]")]
    public class RegionEditController : BaseController
    {
        public RegionEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic value)
        { 
            RegionEditModel model = new RegionEditModel(cmd);
            string operKey = value.operKey;
            string sql = "";
            string msg = "";
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string mother_region_path = value.mother_region_path;
            string region_id = value.region_id;
            if (region_id != "" && mother_region_path!="")
            {
                if (mother_region_path.Contains("/" + region_id + "/"))
                {
                    return new JsonResult(new { result = "Error", msg = "父类不能是该类自身或子类" });
                }
                
            }
            JsonResult record = await model.SaveTable(cmd, value);
            msg = (record.Value as dynamic).msg;

            var sql1 = @$"SELECT DISTINCT(other_region)  FROM info_supcust WHERE company_id={companyID} and other_region like '%/{region_id}/%'";

            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("itemOtherRegion", sql1);
            var dr = await QQ.ExecuteReaderAsync();
            List<System.Dynamic.ExpandoObject> itemOtherRegions = null;
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "itemOtherRegion")
                {
                    itemOtherRegions = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();




            if (msg == "")
            {
                foreach (dynamic item in itemOtherRegions)
                {
                    string itemOtherRegion = item.other_region;

                    //获取商品other_region 最后一个region_id（该商品的父片区）
                    var stack = new Stack<char>();

                    for (var i = itemOtherRegion.Length - 1; i >= 0; i--)
                    {
                        if (!char.IsNumber(itemOtherRegion[i]))
                        {
                            if (0 == stack.Count) continue;
                            else
                                break;
                        }

                        stack.Push(itemOtherRegion[i]);
                    }

                    var result = new string(stack.ToArray());

                    var regionList = itemOtherRegion.Split(region_id);
                    var val = regionList[1];

                    if (mother_region_path != "")
                    {
                        string region_path = mother_region_path + region_id + val;
                        sql += $"update info_supcust set other_region='{region_path}' where company_id={companyID} and other_region like '%/{region_id}/%' and region_id = {result};";
             
                    }





                }
                if (sql != "")
                {
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                }
                return record;
            }
            else return new JsonResult(new { result = "Error", msg });
        }
        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName,string flds,string value, string availValues)
        {
            RegionEditModel model = new RegionEditModel(cmd); 
            string data=await PageBaseModel.GetDataItemOptions(cmd,operKey,model.DataItems ,dataItemName, flds,value,availValues);
            return data; 
        } 
    }
}