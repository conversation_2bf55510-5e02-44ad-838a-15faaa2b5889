using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;

namespace ArtisanManage.Pages.BaseInfo 
{
    public class FeeOutEditModel : PageFormModel
    {
        public FeeOutEditModel(CMySbCommand cmd,string company_id="",string oper_id="") : base(Services.MenuId.paywaysView)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"sub_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"sub_name",new DataItem(){Title="费用名称",Necessary=true,FldArea="divHead"}},
            };
 
            m_idFld = "sub_id"; m_nameFld = "sub_name";
            m_tableName = "cw_subject";
            m_selectFromSQL = "from cw_subject where sub_id='~ID' and sub_type = 'ZC'";
        }

        public async Task OnGet()
        {  
            await InitGet(cmd);   
        } 
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class FeeOutEditController : BaseController
    { 
        public FeeOutEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            FeeOutEditModel model = new FeeOutEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            FeeOutEditModel model = new FeeOutEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {

            FeeOutEditModel model = new FeeOutEditModel(cmd);
            return await model.SaveTable(cmd, request);

        }
    }
}