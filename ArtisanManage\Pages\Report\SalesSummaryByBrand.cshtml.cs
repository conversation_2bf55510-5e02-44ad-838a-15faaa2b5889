﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class SalesSummaryByBrandModel : PageQueryModel
    {
        public SalesSummaryByBrandModel(CMySbCommand cmd) : base(Services.MenuId.salesSummaryByBrand)
        {
            this.cmd = cmd;
            this.PageTitle = "销售汇总(品牌)";
            this.NotQueryHideColumn = true;
            this.UsePostMethod = true;
            DataItems = new Dictionary<string, DataItem>()
            {   {"supcust_id",new DataItem(){FldArea="divHead",Title="客户名称",Checkboxes=true,LabelFld="sup_name",ButtonUsage="event",CompareOperator="=",DropDownWidth = "200",SqlFld="sm.supcust_id",
                    SqlForOptions=CommonTool.selectSupcust } },
                  
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sd.happen_time+sm.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sd.happen_time+sm.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    " 
                }},
                
				{"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){SqlFld="ip.item_brand"})},


				{"seller_id",new DataItem(){Title="业务员",Checkboxes=true,FldArea="divHead", LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="seller_id",SqlForOptions=CommonTool.selectSellers } },
                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
                {"group_id",new DataItem(){Title="渠道",Checkboxes=true,FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                {"depart_path",new DataItem(){Title="部门",FldArea="divHead",LabelFld="depart_path_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", TreePathFld="depart_path",CompareOperator="like",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                 {"department_id",new DataItem(){Title="所属部门",TreePathFld="department_path",Hidden=true, FldArea="divHead",LabelFld="department_id_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=false,DropDownWidth="150", CompareOperator="=",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                 {"arrears_status",new DataItem(){FldArea="divHead",Title="欠款情况",Hidden=true, Checkboxes=true, ButtonUsage = "list",CompareOperator="=",
                    Source = @"[{v:'cleared',l:'已结清',condition:""abs(total_amount-paid_amount-disc_amount)<0.1""},
                                 {v:'uncleared',l:'未结清',condition:""abs(total_amount-paid_amount-disc_amount)>0.1""},
                                 {v:'all',l:'所有',condition:""true""}]"
                }},
                {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'3',l:'预设进价'},{v:'2',l:'加权平均价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价'}]", CompareOperator="=" }},
                {"senders_id",new DataItem(){FldArea="divHead",Title="送货员",Checkboxes=true, LabelFld="senders_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSenders,CompareOperator="like"}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                {"branch_id",new DataItem(){Title="仓库",FldArea="divHead",Checkboxes=true, LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.branch_id",
                    SqlForOptions=CommonTool.selectBranch}},
                {"showRebateProfit",new DataItem(){FldArea="divHead",Title="显示补差后利润",CtrlType="jqxCheckBox",Hidden=true,ForQuery=false,Value="false"}},
                 {"sheetType",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,HideOnLoad = true} }
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"brand_id",    new DataItem(){Title="品牌",  Width="150",Hidden=true, HideOnLoad=true,IsIDColumn=true}},
                       {"brand_name",    new DataItem(){Title="品牌名称", Pinned=true, Linkable=true,  Width="200" ,  SqlFld="(case when brand_name is not null then brand_name else '无品牌商品' end )"   }},
                       {"x_quantity",   new DataItem(){Title="销售量", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="round(sum(case when inout_flag*quantity<0 then sd.inout_flag*(-1)*quantity*sd.unit_factor else 0 end)::numeric,2)",ShowSum = true,Hidden = true}},
                       {"x_big_quantity",   new DataItem(){Title="销售量(大)", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="round(sum(case when inout_flag*quantity<0 then sd.inout_flag*(-1)*quantity*sd.unit_factor/b_unit_factor else 0 end)::numeric,2)",ShowSum = true,Hidden = true}},
                       {"t_quantity",   new DataItem(){Title="退货量", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="round(sum(case when inout_flag*quantity>0 then sd.inout_flag*quantity*sd.unit_factor else 0 end)::numeric,2)",ShowSum = true,Hidden=true}},
                       {"t_big_quantity",   new DataItem(){Title="退货量(大)", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="round(sum(case when inout_flag*quantity>0 then sd.inout_flag*quantity*sd.unit_factor/b_unit_factor else 0 end)::numeric,2)",ShowSum = true,Hidden=true}},
                       {"net_quantity", new DataItem(){Title="净销量", Sortable=true,  CellsAlign="right",  Width="80",SqlFld="round(sum(quantity*sd.unit_factor*inout_flag*(-1))::numeric,2)",ShowSum = true,Hidden=true}},
                       {"rebate_quantity",   new DataItem(){Title="补差数量", Sortable=true,  CellsAlign="right",  Width="100",SqlFld="round(sum(case when coalesce(rebate_price, 0) <> 0 then quantity*sd.unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"x_amount",     new DataItem(){Title="销售金额", CellsAlign="right", Width="100", Sortable=true,SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.inout_flag*(-1)*sd.sub_amount else 0 end)::numeric,2)",
                       ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       {"t_amount",     new DataItem(){Title="退货金额", Sortable=true,  CellsAlign="right", Width="100",SqlFld="round(sum(case when sd.quantity*sd.inout_flag>0 then sd.sub_amount*sd.inout_flag else 0 end)::numeric,2)",ShowSum = true}},
                       {"net_amount",   new DataItem(){Title="销售净额", Sortable=true, CellsAlign="right",  Width="100",SqlFld="round(sum(sub_amount*money_inout_flag)::numeric,2)",ShowSum = true}},
                       {"net_rebate_amount",   new DataItem(){Title="销售净额(补差后)", Sortable=true, Hidden = true, CellsAlign="right",  Width="150",SqlFld="round(sum(sub_amount*money_inout_flag - coalesce(rebate_price, 0)*quantity*sd.unit_factor)::numeric,2)",ShowSum = true}},
                        {"weight", new DataItem(){Title="销售重量(kg)",  CellsAlign="center",  Width="100",SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.inout_flag*(-1)*mu.weight else 0 end)::numeric,3)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       }},
                        {"return_weight", new DataItem(){Title="退货重量(kg)",  CellsAlign="center",  Width="100",SqlFld="round(sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.inout_flag*mu.weight else 0 end)::numeric,3)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       }},
                        {"net_weight", new DataItem(){Title="净销重量(kg)",  CellsAlign="center",  Width="100",SqlFld="round(sum(sd.quantity*sd.inout_flag*(-1)*mu.weight)::numeric,3)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       }},
                       //{"disc_amount",  new DataItem(){Title="优惠", CellsAlign="right",     Width="10%",SqlFld="sum(disc_amount)",ShowSum = true}},
                      /* {"cost_amount_hasfree",  new DataItem(){Title="成本(含赠)", Sortable=true,  CellsAlign="right",  Width="15%",SqlFld="sum(disc_amount)",ShowSum=true}},
                       {"profit_hasfree",  new DataItem(){Title="利润(含赠)", Sortable=true,  CellsAlign="right",  Width="15%",ShowSum=true}},
                        {"profit_rate_hasfree",new DataItem(){Title = "利润率(%)(含赠)", Sortable=true,CellsAlign = "right",Width = "7%"  ,ShowAvg = true,
                        FuncGetSumValue = (sumColumnValues) =>
                           {
                               string s_profit_hasfree =sumColumnValues["profit_hasfree"];
                               string s_net_amount =sumColumnValues["net_amount"];

                               double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                               double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                               string rate="";
                               if (net_amount != 0)
                               {
                                   rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                               }
                               return rate;
                           }
                        }},
                        {"free_cost_amount",new DataItem(){Title="赠品成本",CellsAlign="right",Width="8%",SqlFld="",ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }} },
                       {"cost_amount",  new DataItem(){Title="成本", CellsAlign="right",    Width="8%", Sortable=true,SqlFld="Will be changed by condition",ShowSum=true}},
                       {"profit",       new DataItem(){Title="利润", CellsAlign="right",    Width="8%", Sortable=true,ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }}},
                       {"profit_rate",new DataItem(){ Title = "利润率(%)", Sortable=true,CellsAlign = "right",Width = "7%",ShowAvg = true,
                       FuncGetSumValue = (sumColumnValues) =>
                           {
                               string s_profit_hasfree =sumColumnValues["profit"];
                               string s_net_amount =sumColumnValues["net_amount"];

                               double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                               double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                               string rate="";
                               if (net_amount != 0)
                               {
                                   rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                               }
                               return rate;
                           }
                       }},*/

                     },
                     QueryFromSQL=@"
from  ~detailTable sd 
left join ~mainTable sm  on sm.sheet_id = sd.sheet_id and sm.company_id=~COMPANY_ID
left join info_item_prop ip on sd.item_id = ip.item_id and ip.company_id=~COMPANY_ID
left join info_item_brand ib on ip.item_brand = ib.brand_id and ib.company_id=~COMPANY_ID 
left join info_supcust sc on sm.supcust_id = sc.supcust_id and sc.company_id=~COMPANY_ID
left join info_operator io on sm.seller_id = io.oper_id and io.company_id=~COMPANY_ID
left join info_item_multi_unit mu on  sd.item_id = mu.item_id and sd.unit_factor = mu.unit_factor and mu.company_id = ~COMPANY_ID 
left join (select distinct on (item_id) item_id,unit_factor b_unit_factor from info_item_multi_unit where company_id=~COMPANY_ID order by item_id, unit_factor desc)bunit on sd.item_id=bunit.item_id

where sd.company_id= ~COMPANY_ID and sm.approve_time is not null and sm.red_flag is null ~VAR_IS_DEL and (trade_type NOT IN ( 'J', 'H' ) or trade_type is null)",
                     QueryGroupBySQL = " group by brand_id,brand_name",
                     QueryOrderSQL=" order by brand_name"
                  }
                }
            };
			var origCols = Grids["gridItems"].Columns;
			var cols = SalesSummaryByItemModel.GetProfitColumns(origCols,false);
            
            foreach (var k in cols)
            {
                origCols.Add(k.Key, k.Value);
            }
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            //Item页面中的SetProfitColumns方法
            SalesSummaryByItemModel.SetProfitColumns(this);
            var sheetType = DataItems["sheetType"].Value;
            this.SQLVariables["IS_DEL"] = "";
            if(sheetType.ToLower() == "xd")
            {
                this.SQLVariables["IS_DEL"] = "and coalesce(sm.is_del, false) = false";
            }
            /*
            var cost_price_type = DataItems["cost_price_type"].Value;


            var costPrice = "sd.cost_price_buy";//当前进价
            switch (cost_price_type)
            {
                case "3"://预设进价
                    costPrice = "sd.cost_price_buy";
                    break;
                case "2"://加权价
                    costPrice = "sd.cost_price_avg";
                    break;
                case "1"://预设成本
                    costPrice = "sd.cost_price_prop";
                    break;
                case "4"://最近平均进价
                    costPrice = "sd.cost_price_recent";
                    break;
            }

            var columns = Grids.GetValueOrDefault("gridItems").Columns;
            columns["cost_amount_hasfree"].SqlFld = $"round( SUM(case when coalesce(trade_type,'X')<>'CL' then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end) :: NUMERIC, 2 )";
            columns["profit_hasfree"].SqlFld = $@"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round( SUM ( case when coalesce(trade_type,'X')<>'CL' then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end ) :: NUMERIC, 2 )";
            columns["profit_rate_hasfree"].SqlFld = @$"
cast( 
    (
		(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round(sum(case when coalesce(trade_type,'X')<>'CL' then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2) )*100
	) /
	(
		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) <>0
		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2) 
		     else null
		end
	)

as numeric)";

            columns["cost_amount_free_cl"].SqlFld = $"round( SUM (quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ) :: NUMERIC, 2) ";
            columns["profit_free_cl"].SqlFld = $@"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round( SUM ( quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ) :: NUMERIC, 2 )";
            columns["profit_rate_free_cl"].SqlFld = @$"
cast( 
    (
		(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2))*100
	) /
	(
		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) <>0 
		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2) 
					else null 
		end
	)
as numeric)";



            columns["free_cost_amount"].SqlFld = $"round(sum((case when sub_amount=0  then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2)  ";

            columns["cost_amount"].SqlFld = $"round( SUM ( CASE WHEN sub_amount <> 0  THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 )";
            columns["profit"].SqlFld = $@"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)
-round( SUM ( CASE WHEN sub_amount <> 0   THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 )";
            columns["profit_rate"].SqlFld = @$"
            cast( 
                (
        100*(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round( SUM ( CASE WHEN sub_amount <> 0  THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 ))
            	) /
            	(
            		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) <>0 
            		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2)
            					else null 
            		end
            	) 

            as numeric)";


            columns["free_cost_amount"].SqlFld = $"round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2)  ";

            columns["cost_amount"].SqlFld = $"round( SUM ( CASE WHEN sub_amount <> 0 and trade_type !='J' THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 )";
            columns["profit"].SqlFld = $@"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)
-round( SUM ( CASE WHEN sub_amount <> 0 and (trade_type !='J' or trade_type is null)  THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 )";
            columns["profit_rate"].SqlFld = @$"
            cast( 
                (
        100*(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round( SUM ( CASE WHEN sub_amount <> 0 and (trade_type !='J' or trade_type is null)  THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 ))
            	) /
            	(
            		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) <>0 
            		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2)
            					else null 
            		end
            	) 

            as numeric)";*/

        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            SalesSummaryByItemModel.SetCostInfo(this);
            /*var costPriceType = "3";
            var costPriceTypeName = "预设进价";
            if (JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            if (costPriceType == "1") costPriceTypeName = "预设成本";
            else if (costPriceType == "2") costPriceTypeName = "加权平均成本";
            else if (costPriceType == "4") costPriceTypeName = "最近平均进价";
            DataItems["cost_price_type"].Value = costPriceType;
            DataItems["cost_price_type"].Label = costPriceTypeName;
            var columns = Grids["gridItems"].Columns;
            var sheetType = DataItems["sheetType"].Value;

            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights != null && operRights.delicacy != null) seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                columns["free_cost_amount"].HideOnLoad = columns["free_cost_amount"].Hidden = true;
                columns["cost_amount"].HideOnLoad = columns["cost_amount"].Hidden = true;
                columns["profit"].HideOnLoad = columns["profit"].Hidden = true;
                columns["profit_rate"].HideOnLoad = columns["profit_rate"].Hidden = true;
                columns["cost_amount_hasfree"].HideOnLoad = columns["cost_amount_hasfree"].Hidden = true;
                columns["profit_hasfree"].HideOnLoad = columns["profit_hasfree"].Hidden = true;
                columns["profit_rate_hasfree"].HideOnLoad = columns["profit_rate_hasfree"].Hidden = true;
            }*/

        }

        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class SalesSummaryByBrandController : QueryController
    {
        public SalesSummaryByBrandController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SalesSummaryByBrandModel model = new SalesSummaryByBrandModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
		[HttpPost]
		public async Task<object> GetQueryRecords([FromBody] dynamic data)
        {
            string sheetType = data.sheetType;

			var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";
            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }
            SalesSummaryByBrandModel model = new SalesSummaryByBrandModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            model.Grids["gridItems"].QueryFromSQL = sql;
            object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
            return records;
        }
        [HttpPost]
        public async Task<ActionResult> ExportExcel(string sheetType)
        {
            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";
            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }
            SalesSummaryByBrandModel model = new SalesSummaryByBrandModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            model.Grids["gridItems"].QueryFromSQL = sql;
            return await model.ExportExcel(Request, cmd);
        }
    }
}
