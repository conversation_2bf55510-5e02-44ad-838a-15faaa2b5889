﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ClientAddModel : PageQueryModel
    { 
        public ClientAddModel(CMySbCommand cmd) : base(Services.MenuId.clientAdd)
        {
            this.cmd = cmd;
            this.PageTitle = "新增客户报表";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sc.create_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sc.create_time",   CompareOperator="<=",Value = CPubVars.GetDateText(DateTime.Now.Date) + " 23:59",
                    JSDealItemOnSelect =@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                 {"sup_group",new DataItem(){Title="渠道",FldArea="divHead",LabelFld="group_name",ButtonUsage="list",QueryOnChange=true,CompareOperator="=",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="sc.supcust_id",
                            SqlForOptions=CommonTool.selectSupcust } },
                 {"status",new DataItem(){Title = "状态",FldArea="divHead",LabelFld = "cls_status_name", LabelInDB = false, Value = "normal", Label = "正常",ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "normal",
                     Source = @"[{v:'normal',l:'正常',condition:""(sc.status = '1' or sc.status is null)""},
                               {v:'stop',l:'停用',condition:""sc.status = '0' ""},
                               {v:'all',l:'所有',condition:""true""}]"
                 }},
                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
                 {"sup_rank",new DataItem(){Title="级别",FldArea="divHead",LikeWrapper="/", LabelFld="rank_name", QueryOnChange=true,CompareOperator="=",Checkboxes=true,ButtonUsage = "list",
                   SqlForOptions="select rank_id as v,rank_name as l  from info_supcust_rank"
                 }},
                {"balance",new DataItem(){Title = "欠款情况",FldArea="divHead",LabelFld = "balance_status", LabelInDB = false, Value = "noBalance", Label = "已结清",ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "noBalance",
                    Source = @"[{v:'noBalance',l:'已结清',condition:""(abs(balance) < 0.01 or balance is null)""},
                            {v:'hasBalance',l:'未结清',condition:""abs(balance) >= 0.01""},
                            {v:'all',l:'所有',condition:""true""}]"
                 }},

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates = true,
                      Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"supcust_id",new DataItem(){HideOnLoad =true,Hidden=true,SqlFld="sc.supcust_id"}},
                       {"sup_name",new DataItem(){Title="客户名称",Linkable=true}},
                       {"mobile",new DataItem(){Title="客户电话",SqlFld="sc.mobile"}},
                       {"sup_addr",new DataItem(){Title="客户地址",SqlFld="sc.sup_addr"}},
                       {"balance",new DataItem(){Title="客户欠款金额",Sortable=true} },
                       {"sale_amount",new DataItem(){Title="客户销售金额",SqlFld="t.total_sale_amount",Sortable=true} },
                       {"create_time",     new DataItem(){Title="创建时间", Sortable=true,    Width="150",SqlFld="sc.create_time", }},
                       {"oper_name",new DataItem(){Title="创建人",Linkable=true}},
                     },
                     QueryFromSQL=@"
                      from info_supcust sc
LEFT JOIN info_operator io on sc.creator_id=io.oper_id and io.company_id=~COMPANY_ID
left join arrears_balance ab on sc.supcust_id=ab.supcust_id and ab.company_id=~COMPANY_ID
LEFT JOIN (
    SELECT supcust_id, SUM(total_amount * money_inout_flag) AS total_sale_amount FROM sheet_sale_main 
    WHERE happen_time >= '~VAR_startDay' AND happen_time < '~VAR_endDay' AND approve_time IS NOT NULL AND company_id = ~COMPANY_ID AND sheet_type = 'X' AND red_flag IS NULL 
    GROUP BY supcust_id
) t ON sc.supcust_id = t.supcust_id
where sc.company_id=~COMPANY_ID
",
                     QueryGroupBySQL = " ",

                     QueryOrderSQL=" "
                  }
                }
            };
        }

        public async Task OnGet()
        {
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;
        }
    }



    [Route("api/[controller]/[action]")]
    public class ClientAddController : QueryController
    { 
        public ClientAddController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ClientAddModel model = new ClientAddModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {

            ClientAddModel model = new ClientAddModel(cmd);
            
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {

            ClientAddModel model = new ClientAddModel(cmd);
            
            return await model.ExportExcel(Request, cmd);
        }
    }
}
