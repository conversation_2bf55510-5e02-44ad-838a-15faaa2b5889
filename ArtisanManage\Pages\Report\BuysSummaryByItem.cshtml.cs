
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class BuysSummaryByItemModel : PageQueryModel
    {
     

        private string buildJsAggregatesComputer(string unit_no, string qty)
        {
            var JsAggregatesComputer = $@"[{{
                                     'qty':
                                          function (aggregatedValue, currentValue, column, record) {{
                                                  var unit_no = record.{unit_no};
                                                  var qty = record.{qty};
                                                  currentValue = parseFloat(toMoney(currentValue));
                                                  if (!currentValue) return aggregatedValue;
                                                  if (qty && unit_no) {{
                                                    if (!aggregatedValue) aggregatedValue = '';
                                                         var n = aggregatedValue.indexOf(unit_no);
                                                         if (n > 0) {{
                                                            var unitQty = 0;
                                                            for (i = n - 1; i >= 0; i--) {{
                                                                 var tmp = parseFloat(aggregatedValue.substring(i, n));
                                                                 if (tmp.toString() != 'NaN') {{
                                                                      unitQty = tmp;
                                                                 }}
                                                                 else break;
                                                             }}
                                                            
                                                             aggregatedValue = aggregatedValue.replace(unitQty + unit_no, toMoney(unitQty + currentValue) + unit_no)
                                                            
                                                        }}
                                                        else
                                                        {{
                                                            aggregatedValue = aggregatedValue + toMoney(currentValue).toString() + unit_no;
                                                        }}
                                                   }}
                                                return aggregatedValue;
                                                }}
                                        }}]
                                    ";

            return JsAggregatesComputer;
        }
        private string buildJsAggregatesRender()
        {
            return @" function aggregatesrenderer_quantity (aggregates, column, element, summaryData) {
                                               var renderstring = `<div class='jqx-widget-content style='float: left; width: 100%; height: 100%; '>`;
                                               $.each(aggregates, function (key, value) {
                                                    renderstring += '<div style=`position: relative; margin: 6px; text-align: right; overflow: hidden;`>' + value + '</div>';
                                               });
                                               renderstring +=`</div>`;
                                          return renderstring;
                                         } 
                     ";
        }
        public BuysSummaryByItemModel(CMySbCommand cmd) : base(Services.MenuId.buysSummaryByItem)
        {
            this.cmd = cmd;
            this.PageTitle = "采购汇总(商品)";
            string x_quantity = "sum(case when sheet_type='CG' then sd.tquantity::numeric else 0 end)::numeric";
            string x_quantity_rounded = $"round({x_quantity},4)";
            string t_quantity = "sum(case when sheet_type='CT' then sd.tquantity::numeric else 0 end)::numeric";
            string t_quantity_rounded = $"round({t_quantity},4)";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},

            {"item_id",new DataItem(){Title="商品名称", FldArea="divHead", LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="sd.item_id",DropDownWidth="300",
             SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
            
			     {"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){SqlFld="ip.item_brand"})},

				{"other_class",new DataItem(){Title="类别",FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",TreePathFld="other_class",MumSelectable=true,CompareOperator="like",
                   SqlForOptions=CommonTool.selectClasses}},
                {"seller_id",new DataItem(){FldArea="divHead",Title="经手人",LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},
                {"receivers_id",new DataItem(){FldArea="divHead",Title="收货员",Checkboxes=false, LabelFld="receivers_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSenders,CompareOperator="like"}},
                {"branch_id",new DataItem(){Title="仓库",FldArea="divHead",LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",SqlFld="(case when sd.branch_id is not null then sd.branch_id else sm.branch_id end)",
                SqlForOptions=CommonTool.selectBranch }},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="供应商",LabelFld="sup_name",ButtonUsage="list",CompareOperator="=",SqlFld="s.supcust_id",
                    SqlForOptions=CommonTool.selectSuppliers}},
                {"depart_path",new DataItem(){Title="部门",FldArea="divHead",LabelFld="depart_path_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", TreePathFld="depart_path",CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"make_brief",new DataItem(){Title="整单备注",FldArea="divHead",CompareOperator="ilike" } }, 
                {"remark",new DataItem(){Title="明细备注",FldArea="divHead",CompareOperator="ilike",SqlFld = "sd.remark"} },

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates=true, Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"item_id",    new DataItem(){Title="商品编号",  Width="250",SqlFld="sd.item_id",Hidden=true}},
                       {"item_name",    new DataItem(){Title="商品名称",Linkable = true,   Width="250",SqlFld="ip.item_name"}},
                       {"barcode",   new DataItem(){Title="商品条码", Sortable=true,    Width="200",SqlFld="b_barcode"}},
                       {"b_unit_no",   new DataItem(){Title="大单位名称",    Width="",SqlFld="b_unit_no",Hidden=true,HideOnLoad = true}},
                       /*{"x_quantity1",   new DataItem(){Title="采购量(无单位)", Hidden=true, HideOnLoad  = true,Width="250",SqlFld="round((sum(case when sheet_type='CG' then sd.tquantity else 0 end)::numeric/b_unit_factor::numeric),2)"}},
                       {"x_quantity",   new DataItem(){Title="采购量", Sortable=true,  CellsAlign="center",  Width="150",SqlFld="concat(round((sum(case when sheet_type='CG' then sd.tquantity else 0 end)::numeric/b_unit_factor::numeric),2),b_unit_no)",
                            JsAggregatesComputer = buildJsAggregatesComputer("b_unit_no","x_quantity1"),
                            JsAggregatesRender=buildJsAggregatesRender()
                       }},*/
                       {"s_unit_no",   new DataItem(){Title="小单位", Width="5%",SqlFld="s_unit_no",Hidden=false,CellsAlign="right"}},

                        {"s_x_quantity", new DataItem() {Title="采购量（小单位）",CellsAlign="right",Width="5%",
                            SqlFld=$"{x_quantity_rounded}"
                         } },



                        {"x_quantity",   new DataItem(){Title="采购量", CellsAlign="center", Sortable=true,   Width="50",
                           SqlFld = $"unit_from_s_to_bms ({x_quantity_rounded},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           SortFld=x_quantity,
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           RelyColumns="x_quantity_b,x_quantity_m,x_quantity_s",
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                                return CPubVars.GetBMSQty(sumColumnValues["x_quantity_b"],sumColumnValues["x_quantity_m"],sumColumnValues["x_quantity_s"]);
                           }
                       }},
                       {"x_quantity_b",   new DataItem(){Title="采购量(b)", CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('b',{x_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_m",   new DataItem(){Title="采购量(m)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('m',{x_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_s",   new DataItem(){Title="采购量(s)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('s',{x_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       /*{"t_quantity1",   new DataItem(){Title="退货量(无单位)",  Hidden=true,  Width="250",SqlFld="round((sum(case when sheet_type='CT' then sd.tquantity else 0 end)::numeric/b_unit_factor::numeric),2)"}},
                       {"t_quantity_old",   new DataItem(){Title="退货量", Sortable=true,  CellsAlign="center",  Width="150",SqlFld="concat(round((sum(case when sheet_type='CT' then sd.tquantity else 0 end)::numeric/b_unit_factor::numeric),2),b_unit_no)",
                            JsAggregatesComputer = buildJsAggregatesComputer("b_unit_no","t_quantity1"),
                            JsAggregatesRender=buildJsAggregatesRender() 
                       }},*/
                       {"t_quantity",   new DataItem(){Title="退货量", CellsAlign="center", Sortable=true,   Width="50",
                           SqlFld = $"unit_from_s_to_bms ({t_quantity_rounded},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           SortFld=t_quantity,
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           RelyColumns="t_quantity_b,t_quantity_m,t_quantity_s",
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                                return CPubVars.GetBMSQty(sumColumnValues["t_quantity_b"],sumColumnValues["t_quantity_m"],sumColumnValues["t_quantity_s"]);
                           }
                       }},
                       {"t_quantity_b",   new DataItem(){Title="退货量(b)", CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('b',{t_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"t_quantity_m",   new DataItem(){Title="退货量(m)",   CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('m',{t_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"t_quantity_s",   new DataItem(){Title="退货量(s)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('s',{t_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_amount",     new DataItem(){Title="采购金额", Sortable=true, CellsAlign="right", Width="150",SqlFld="sum(case when sheet_type='CG' then sub_amount*(-1)*money_inout_flag else 0 end)",ShowSum=true}},
                       {"t_amount",     new DataItem(){Title="退货金额", Sortable=true, CellsAlign="right", Width="150",SqlFld="sum(case when sheet_type='CT' then -sub_amount*(-1)*money_inout_flag else 0 end)",ShowSum=true}},
                       {"total_quantity",   new DataItem(){Title="数量小计", Sortable=true,  Hidden=true,  Width="250",SqlFld="round((sum(case when sheet_type='CT' then sd.tquantity else 0 end)::numeric/b_unit_factor::numeric),2)-round((sum(case when sheet_type='CT' then sd.tquantity else 0 end)::numeric/b_unit_factor::numeric),2)"}},
                       {"total_amount",     new DataItem(){Title="金额小计", Sortable=true, CellsAlign="right", Width="150",SqlFld="sum(case when sheet_type='CG' then sub_amount*(-1)*money_inout_flag else 0 end)+sum(case when sheet_type='CT' then sub_amount*(-1)*money_inout_flag else 0 end)",ShowSum=true}},
                       {"cashback_amount",     new DataItem(){Title="返利总额", Sortable=true, CellsAlign="right", Width="150",SqlFld="sum(case when sheet_type='CG' then (sd.price_cashback * sd.quantity) else 0 end)",ShowSum=true,Hidden=true}},
                     },
                     /*QueryFromSQL=@"from sheet_buy_main sm
		                left join sheet_buy_detail sd on sm.sheet_id = sd.sheet_id
		                left join info_item_prop ip on sd.item_id = ip.item_id 
                        left join info_operator io on sm.seller_id = io.oper_id
                        left join info_branch ib on ib.branch_id = sm.branch_id
                        left join info_supcust s on s.supcust_id = sm.supcust_id
                        left join info_item_multi_unit u on u.item_id = sd.item_id and u.unit_type = 's'",*/
                     QueryFromSQL=@"
from sheet_buy_main sm
right join 
(
    select d.*,(quantity*d.unit_factor) tquantity,
    b_unit_factor,
    b_unit_no,
    b_barcode,
    m_unit_factor,
    m_unit_no,
    m_barcode,
    s_unit_factor,
    s_unit_no,
    s_barcode
    from sheet_buy_detail d
   left join
 (SELECT s.item_id,
         b.unit_factor::numeric AS b_unit_factor,
         m.unit_factor::numeric AS m_unit_factor,
         s.unit_factor::numeric AS s_unit_factor,
         b.unit_no              AS b_unit_no,
         m.unit_no              AS m_unit_no,
         s.unit_no              AS s_unit_no,
         b.barcode              AS b_barcode,
         m.barcode              AS m_barcode,
         s.barcode              AS s_barcode
  FROM info_item_multi_unit s
           LEFT JOIN info_item_multi_unit b ON s.item_id = b.item_id AND b.unit_type = 'b' AND b.company_id = ~COMPANY_ID 
           LEFT JOIN info_item_multi_unit m  ON s.item_id = m.item_id AND m.unit_type = 'm' AND m.company_id = ~COMPANY_ID 
  WHERE s.unit_type = 's'  AND s.company_id = ~COMPANY_ID ) t
 on d.item_id = t.item_id

where d.company_id= ~COMPANY_ID 
                      


) sd on sd.sheet_id = sm.sheet_id
left join info_item_prop ip on sd.item_id = ip.item_id and ip.company_id= ~COMPANY_ID
left join info_operator io on sm.seller_id = io.oper_id and io.company_id= ~COMPANY_ID
left join info_branch ib on ib.branch_id = COALESCE(sd.branch_id,sm.branch_id) and ib.company_id= ~COMPANY_ID
left join info_supcust s on s.supcust_id = sm.supcust_id and s.company_id= ~COMPANY_ID
left join info_item_brand itb on ip.item_brand = itb.brand_id and itb.company_id=~COMPANY_ID 
where sm.company_id = ~COMPANY_ID and sd.company_id= ~COMPANY_ID and sm.approve_time is not null and sm.red_flag is null",
                      
                      QueryGroupBySQL = " group by sd.item_id, b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no,s_barcode,m_barcode,b_barcode,ip.item_name,sd.item_id",
                     QueryOrderSQL=" order by item_name"
                  }
                } 
            };             
        }
        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                var columns = await Grids["gridItems"].GetAllColumns();
                columns["x_amount"].HideOnLoad = columns["x_amount"].Hidden = true;
                columns["t_amount"].HideOnLoad = columns["t_amount"].Hidden = true;
                columns["total_amount"].HideOnLoad = columns["total_amount"].Hidden = true;
            }

        }
    }



    [Route("api/[controller]/[action]")]
    public class BuysSummaryByItemController : QueryController
    { 
        public BuysSummaryByItemController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BuysSummaryByItemModel model = new BuysSummaryByItemModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            BuysSummaryByItemModel model = new BuysSummaryByItemModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            BuysSummaryByItemModel model = new BuysSummaryByItemModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
