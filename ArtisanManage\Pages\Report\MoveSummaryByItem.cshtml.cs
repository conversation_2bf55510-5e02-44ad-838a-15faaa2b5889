﻿using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ArtisanManage.Services;
namespace ArtisanManage.Pages.BaseInfo
{
    public class MoveSummaryByItemModel : PageQueryModel
    { 
        public MoveSummaryByItemModel(CMySbCommand cmd) : base(Services.MenuId.moveSummary)
        {
            this.cmd = cmd;
            this.PageTitle = "调拨汇总(商品)";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期", FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="mm.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="mm.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"from_branch_id",new DataItem(){Title="出仓",Checkboxes=true, FldArea="divHead",ButtonUsage="list",CompareOperator="=",LabelFld="from_branch_name",
                SqlForOptions=CommonTool.selectBranch }},
                {"from_branch_position",new DataItem(){Title="出仓库位",Checkboxes=true, FldArea="divHead",ButtonUsage="list",CompareOperator="=",LabelFld="from_branch_position_name",ForQuery=false,
                SqlForOptions=CommonTool.selectBranchPosition }},
                {"to_branch_id",  new DataItem(){Title="入仓",Checkboxes=true,FldArea="divHead",ButtonUsage="list",CompareOperator="=",LabelFld="to_branch_name",
                SqlForOptions=CommonTool.selectBranch }},
                {"to_branch_position",  new DataItem(){Title="入仓库位",Checkboxes=true,FldArea="divHead",ButtonUsage="list",CompareOperator="=",LabelFld="to_branch_position_name",ForQuery=false,
                SqlForOptions=CommonTool.selectBranchPosition }},
                 {"item_brand", new DataItem(){Title = "品牌",Checkboxes=true, FldArea="divHead", LabelFld = "brand_name", ButtonUsage = "list", CompareOperator="=",SqlFld="ip.item_brand",
                    SqlForOptions = CommonTool.selectBrands}},
                 {"item_class",new DataItem(){Title="类别",FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",MumSelectable=true,CompareOperator="like",SqlFld="ip.other_class",
                   SqlForOptions=CommonTool.selectClasses,MaxRecords="500"}},

                //{"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Value="1",Label="加权平均价",Source = "[{v:'1',l:'加权平均价'},{v:'2',l:'批发价'}]", CompareOperator="=" }},
              //  {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="md.item_id",DropDownWidth="300",
              //SqlForOptions =CommonTool.selectItem  }},
                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="md.item_id",DropDownWidth="300",
                    Pinned=true, SearchFields=CommonTool.itemSearchFields, SqlForOptions =CommonTool.selectItemWithBarcode }},
                 {"make_brief",new DataItem(){Title="整单备注",FldArea="divHead",CompareOperator="ilike",SqlFld="mm.make_brief", } },
                {"remark",new DataItem(){Title="明细备注",SqlFld="md.remark", FldArea="divHead",CompareOperator="ilike" } },
                };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"item_name",    new DataItem(){Title="商品名称", Linkable=true, Sortable=true, Width="250",SqlFld="ip.item_name",Pinned=true}},
                       {"item_id",    new DataItem(){Title="商品id", HideOnLoad=true,Hidden=true,  SqlFld="md.item_id"}},
                       {"barcode",    new DataItem(){Title="商品条码",   Width="200",SqlFld="s_barcode"}},
                       {"b_unit_no",   new DataItem(){Title="大单位名称", Width="200",SqlFld="b_unit_no",Hidden=true}},
                       {"s_unit_no",   new DataItem(){Title="小单位", Width="5%",SqlFld="s_unit_no",Hidden=false,CellsAlign="right"}},
                       {"s_quantity",   new DataItem(){Title="数量(小单位)", CellsAlign="right",   Width="5%",ShowSum=true,Hidden=false,SqlFld = "(sum(md.quantity * md.unit_factor))::numeric"}},
                       {"from_branch_name",  new DataItem(){Title="出货仓库",   Width="200"}},
                       {"from_branch_position_name",  new DataItem(){Title="出货库位",   Width="200"}},
                       {"to_branch_name",    new DataItem(){Title="入货仓库",   Width="200"}},
                       {"to_branch_position_name",    new DataItem(){Title="入货库位",   Width="200"}},
                       {"wholesale_amount",   new DataItem(){Title="批发金额",Sortable=true,   Width="160",ShowSum=true,SqlFld="ROUND(SUM(md.quantity *  CASE WHEN md.unit_factor = 1 THEN (md.unit_factor * md.wholesale_price)::NUMERIC ELSE 0::NUMERIC END)::NUMERIC, 2) +ROUND(SUM(md.quantity * CASE WHEN md.unit_factor <> 1 THEN md.wholesale_price::NUMERIC ELSE 0::NUMERIC END)::NUMERIC, 2)"}},
                       {"cost_amount_avg",   new DataItem(){Title="成本金额", Sortable=true,  Width="160",ShowSum=true,SqlFld="round(sum(md.quantity * md.unit_factor  * md.cost_price_avg)::numeric, 2)"}},
                       {"contract_amount",   new DataItem(){Title="承包金额", Sortable=true,  Width="160",ShowSum=true,SqlFld="round(sum(md.quantity * md.contract_price)::numeric, 2)"}},
                         {"buy_amount",   new DataItem(){Title="进价金额",  Sortable=true, Width="160",ShowSum=true,SqlFld="round(sum(md.quantity * md.unit_factor  * md.buy_price)::numeric, 2)"}},
                       {"current_buy_amount",   new DataItem(){Title="当前进价金额",Sortable=true,Hidden=true, Width="160",ShowSum=true,SqlFld="round(sum(md.quantity * md.unit_factor  * mu.s_buy_price::numeric)::numeric, 2)"}},
                       
                       {"quantity_b",   new DataItem(){Title="数量(大)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty('b',sum(md.quantity * md.unit_factor )::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"quantity_m",   new DataItem(){Title="数量(中)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty('m',sum(md.quantity * md.unit_factor )::numeric,b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"quantity_s",   new DataItem(){Title="数量(小)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty('s',sum(md.quantity * md.unit_factor )::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                       {"quantity",     new DataItem(){Title="数量",   Sortable=true,    Width="200",SqlFld="unit_from_s_to_bms ((sum(md.quantity * md.unit_factor )::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty = sumColumnValues["quantity_b"]+"大";
                               if(sumColumnValues["quantity_m"]!="")
                                 sQty+= sumColumnValues["quantity_m"]+"中";
                               if(sumColumnValues["quantity_s"]!="")
                                 sQty+=sumColumnValues["quantity_s"]+"小";
                               return sQty;
                            }
                       } }
                     },

                     QueryFromSQL=@"

from 
sheet_move_detail md 
left join sheet_move_main mm on mm.company_id = ~COMPANY_ID and mm.sheet_id = md.sheet_id
left join info_item_prop ip on ip.company_id = ~COMPANY_ID and ip.item_id = md.item_id
LEFT JOIN 
(
    select branch_id,branch_name as from_branch_name from info_branch where company_id= ~COMPANY_ID
) fb ON mm.from_branch_id = fb.branch_id 
LEFT JOIN 
(
    select branch_position,branch_position_name as from_branch_position_name from info_branch_position where company_id= ~COMPANY_ID
) fbp ON md.from_branch_position = fbp.branch_position 
LEFT JOIN 
(
    select branch_id,branch_name as to_branch_name from info_branch where company_id= ~COMPANY_ID
) tb ON mm.to_branch_id = tb.branch_id 
LEFT JOIN 
(
    select branch_position,branch_position_name as to_branch_position_name from info_branch_position where company_id= ~COMPANY_ID
) tbp ON md.to_branch_position = tbp.branch_position 

LEFT JOIN
( 
    select item_id,
  (s->>'f1')::numeric as s_unit_factor,  s->>'f2' as s_unit_no, s->>'f3' as s_buy_price, s->>'f4' as s_wholesale_price,s->>'f5' s_barcode,
    (m->>'f1')::numeric as m_unit_factor, m->>'f2' as m_unit_no, m->>'f3' as m_buy_price, m->>'f4' as m_wholesale_price,m->>'f5' m_barcode,
    (b->>'f1')::numeric as b_unit_factor, b->>'f2' as b_unit_no, b->>'f3' as b_buy_price, b->>'f4' as b_wholesale_price,b->>'f5' b_barcode

 
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price,wholesale_price,barcode)) as json from info_item_multi_unit where company_id=~COMPANY_ID ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$)  as errr(item_id int, s jsonb,m jsonb, b jsonb) 
) mu on ip.item_id = mu.item_id


where md.company_id= ~COMPANY_ID and mm.approve_time is not null and mm.red_flag is null ~VAR_branch_position_sql",
                     QueryGroupBySQL=" group by md.item_id,b_unit_no,ip.item_name,from_branch_name,from_branch_position_name,to_branch_name,to_branch_position_name,s_barcode,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no",
                     QueryOrderSQL=" order by ip.item_name"
                  }
                } 
            }; 
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            //var columns = Grids.GetValueOrDefault("gridItems").Columns;
            //var costType = "";
            //var cost_price_type = DataItems["cost_price_type"].Value;
            //switch (cost_price_type)
            //{
            //    case "1"://预设成本价
            //        costType = "cost_amount_avg";
            //        break;
            //    case "2"://加权价
            //        costType = "wholesale_amount";
            //        break;
            //}

            //columns["total_amount"].SqlFld = $"round(sum({ costType})::numeric, 2) ";
            string from_branch_positions = DataItems["from_branch_position"].Label;
            string to_branch_positions = DataItems["to_branch_position"].Label;
            var fromBranchPositions = "";
            var toBranchPositions = "";

            if (from_branch_positions != "")
            {
                var fromBranchPositionList = from_branch_positions.Split(',');
                foreach (var fromBranchPosition in fromBranchPositionList)
                {
                    if (fromBranchPositions == "")
                    {
                        if (fromBranchPosition == "默认库位")
                        {
                            fromBranchPositions += $@" and (md.from_branch_position = 0";
                            continue;
                        }
                        fromBranchPositions = $@" and (from_branch_position_name ='{fromBranchPosition}'";
                    }
                    else
                    {
                        if (fromBranchPosition == "默认库位")
                        {
                            fromBranchPositions += $@" or md.from_branch_position = 0";
                            continue;
                        }
                        fromBranchPositions += $@" or from_branch_position_name ='{fromBranchPosition}'";
                    }
                }
                if (fromBranchPositions != "") fromBranchPositions += ")";

            }
            if (to_branch_positions != "")
            {
                var toBranchPositionList = to_branch_positions.Split(',');
                foreach (var toBranchPosition in toBranchPositionList)
                {
                    if (toBranchPositions == "")
                    {
                        if (toBranchPosition == "默认库位")
                        {
                            toBranchPositions += $@" and (md.to_branch_position = 0";
                            continue;
                        }
                        toBranchPositions = $@" and (to_branch_position_name ='{toBranchPosition}'";
                    }
                    else
                    {
                        if (toBranchPosition == "默认库位")
                        {
                            toBranchPositions += $@" or md.to_branch_position = 0";
                            continue;
                        }
                        toBranchPositions += $@" or to_branch_position_name ='{toBranchPosition}'";
                    }
                }
                if (toBranchPositions != "") toBranchPositions += ")";

            }
            string branchPositionSql = $@"{fromBranchPositions} {toBranchPositions}";
            this.SQLVariables["branch_position_sql"] = branchPositionSql;

        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
             
            var columns = Grids["gridItems"].Columns;
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                columns["cost_amount_avg"].HideOnLoad = columns["cost_amount_avg"].Hidden = true;
                columns["buy_amount"].HideOnLoad = columns["buy_amount"].Hidden = true;
                columns["current_buy_amount"].HideOnLoad = columns["current_buy_amount"].Hidden = true; 
            } 

        }

        public async Task OnGet()
        {  
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
    }



    [Route("api/[controller]/[action]")]
    public class MoveSummaryByItemController : QueryController
    { 
        public MoveSummaryByItemController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            MoveSummaryByItemModel model = new MoveSummaryByItemModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            MoveSummaryByItemModel model = new MoveSummaryByItemModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }
        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            MoveSummaryByItemModel model = new MoveSummaryByItemModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
