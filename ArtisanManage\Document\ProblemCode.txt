﻿1.减少代码行数,代码阅读更轻松，能一行写下就不要分多行
对于一个代码文件来说，阅读代码总是竖向翻阅的，代码行数越多，查看越吃力。
vue本身html和code,css都在一个文件里，纵向空间本就宝贵


  ：
  getRealDiscount(discountPercent) {
      return discountPercent > 10
        ? discountPercent / 100
        : discountPercent / 10;
    },


    getRealDiscount(discountPercent) {
       return discountPercent > 10 ? discountPercent / 100 : discountPercent / 10;
    },



   this.scrollInterval = window.addEventListener(
      "scroll",
      () => {
        this.lastScrollTime = new Date();
        const destroyInterval = new Date().getTime() - this.destroyTime.getTime();
        this.scrollInterval = setInterval(() => {
          if (this.hScrollInterval == 0 && destroyInterval > 300) {
            console.log(scrollIntervalTime);
            this.hScrollInterval = setInterval(this.scrollTopListen, 100);
          }
        });
      },
      true
    );



 <template #right>
    <div
    @click="
        clickHistorySheetBtn(item.supcust_id, item.sup_name)
    "
    class="history-btn"
    >
    历史单据
    </div>
</template>


<template #right>
        <div @click="clickHistorySheetBtn(item.supcust_id, item.sup_name)" class="history-btn">
            历史单据
        </div>
</template>

 

if(realDiscount<1){
   this.sheet.make_brief=this.keyboardText+'折优惠'
}

if(realDiscount<1) this.sheet.make_brief=this.keyboardText+'折优惠'



2.实现同一个功能的多个函数代码放在文件中的一个地方，紧挨着，用备注表示开始和结束

//begin:计算整单折扣或优惠金额
    btnCalcDiscount_click() {

   ........

    getRealDiscount(discountPercent) {
      return discountPercent > 10 ? discountPercent / 100 : discountPercent / 10;
    },
  //end:计算整单折扣或优惠金额


3.尽量不要使用watch  尤其是watch全局的store变量，
组件之间传参用事件，不要用watch


4.变量，函数名单词要完整，名字有意义，在不用注释的情况下就可以看明白是干什么的
   
   不要没有意义的加s，一般数组后面才加s
      var objs





       

  this.scrollInterval = window.addEventListener(
      "scroll",
      () => {
        this.lastScrollTime = new Date();
        const destroyInterval = new Date().getTime() - this.destroyTime.getTime();
        this.scrollInterval = setInterval(() => {
          if (this.hScrollInterval == 0 && destroyInterval > 300) {
            console.log(scrollIntervalTime);
            this.hScrollInterval = setInterval(this.scrollTopListen, 100);
          }
        });
      },
      true
    );

destroyTime ---> scrollFinishTime



activated(){
    this.show = false   //变量名太短了，不能准确表达意思，这里是显示什么？
    this.getOperRight()
    this.inithandleScroll()
    window.addEventListener('scroll', this.handleScroll, true)
  },


5.一个功能不重用，就不要单独写函数，可以直接就地写内置函数


6.用最简单的方式实现，不要绕弯子
原代码：
 <div style="margin-left:20px;margin-top:5px"> <button class="btnAct" data-act="QueryData">查询</button> </div>

 $('.btnAct').on('click', function () {
            var act = $(this).data('act');
            window[act]();
 }); 

优化后：
 <div style="margin-left:20px;margin-top:5px"> <button onclick="QueryData">查询</button> </div>



 SETQQ:

            if (seller_id != "")
            {
                sql = $"select rights->'delicacy'->'allowNegativeStock'->'value' role_allow_negative_stock from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={seller_id}";
                QQ.Enqueue("role_allow_negative_stock", sql);
            }

            if (branch_id != "")
            {
                sql = $"select allow_negative_stock branch_allow_negative_stock from info_branch where company_id = {company_id} and branch_id = {branch_id}";
                QQ.Enqueue("branch_allow_negative_stock", sql);

            }


READQQ

            else if (sqlName == "role_allow_negative_stock")
            {
                dynamic roleAllowNegativeStock = CDbDealer.Get1RecordFromDr(dr);
                string r = roleAllowNegativeStock.role_allow_negative_stock;
                if (roleAllowNegativeStock != null && r.ToLower() == "false") RoleAllowNegativeStock = false; 
            }
            else if (sqlName == "branch_allow_negative_stock" && RoleAllowNegativeStock)//这里依赖于执行顺序，如果后来修改了顺序，就会导致bug
            {
                dynamic branchAllowNegativeStock = CDbDealer.Get1RecordFromDr(dr);
                string b = branchAllowNegativeStock.branch_allow_negative_stock;
                if (branchAllowNegativeStock != null && b.ToLower() == "false") BranchAllowNegativeStock = false;
            }




用for 代替 while,否则容易造成死循环
while (startRow <= sheet.LastRowNum)
            {
                var row = sheet.GetRow(startRow);
                startRow++;
                if (row == null) continue;//
                if (filter != null && filter(row)) continue;
                var dr = new DataRow { Ordinal = startRow };
                dr.FillWith(row);
                dataRows.Add(dr);
                
            };
            return dataRows;