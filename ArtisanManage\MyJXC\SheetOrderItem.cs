﻿using ArtisanManage.Models;
using ArtisanManage.Pages.CwPages;
using ArtisanManage.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using HuaWeiObsController;

namespace ArtisanManage.MyJXC
{
    public class SheetRowOrderItem : SheetRowItem
    {
        
       // [SaveToDB] [FromFld] public string item_id { get; set; } = "";
       // [FromFld(LOAD_PURPOSE.SHOW)] public string item_name { get; set; } = "";
      //  [SaveToDB] [FromFld] public string unit_no { get; set; } = ""; 
      //  [SaveToDB] [FromFld] public float unit_factor { get; set; } = 1; 
     //   [SaveToDB] [FromFld] public float quantity { get; set; }
       // [SaveToDB] [FromFld] public float orig_price { get; set; }
         [SaveToDB] [FromFld] public decimal real_price { get; set; }
         [SaveToDB] [FromFld] public decimal sub_amount { get; set; }
        public string branch_position{ get; set; }
        public string branch_id { get; set; }
        public string branch_position_name { get; set; } = "";
        public string branch_name { get; set; } = "";
        public string sheet_no { get; set; } = "";
       // [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_no { get; set; }
       // [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_no { get; set; }
       // [FromFld(LOAD_PURPOSE.SHOW)] public string s_unit_no { get; set; }

       // [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_factor { get; set; }
       // [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_factor { get; set; }
      //  public string s_unit_price { get; set; }
       // public string m_unit_price { get; set; }
       // public string b_unit_price { get; set; }
        //  public string unit_conv { get; set; }

        // public string s_unit_qty { get; set; }
        //  public string m_unit_qty { get; set; }
        // public string b_unit_qty { get; set; }

        //public string b_real_price = "";
        //public string s_real_price = "";
        //public string m_real_price = "";

        internal decimal OrderedBalance = 0;
        internal decimal OrderedUnitFactor = 0;
        internal decimal OrderedQuantity = 0;
        internal string OrderedUnitNo = "";
        internal bool HasOrderedBalance = false;
        public string b_real_price
        {
            get
            {
                if (!b_unit_factor.IsValid()) return "";
                return CPubVars.FormatMoney(this.real_price / this.unit_factor * CPubVars.ToDecimal(this.b_unit_factor), 4);
            }
        }
        public string m_real_price
        {
            get
            {
                if (!m_unit_factor.IsValid()) return "";
                return CPubVars.FormatMoney(this.real_price / this.unit_factor * CPubVars.ToDecimal(this.m_unit_factor), 4);
            }
        }
        public string s_real_price
        {
            get
            {
                return CPubVars.FormatMoney(this.real_price / this.unit_factor, 6);
            }
        }
        public decimal QtyOfSmallUnit;
        //  internal float s_quantit

        public override void SetInfoForPrint(bool smallUnitBarcode)
        {
            base.SetInfoForPrint(smallUnitBarcode);

            /*var row = this;
            if (row.b_unit_factor.IsValid())
                row.b_real_price = CPubVars.FormatMoney(row.real_price / row.unit_factor * Convert.ToSingle(row.b_unit_factor), 2);
            if (row.m_unit_factor.IsValid())
                row.m_real_price = CPubVars.FormatMoney(row.real_price / row.unit_factor * Convert.ToSingle(row.m_unit_factor), 2);

            row.s_real_price = CPubVars.FormatMoney(row.real_price / row.unit_factor, 2);
            */

        }
    }
    public class SheetOrderItem : SheetPrepay<SheetRowOrderItem>
    {
        
        public string[] appendixPhotos { get; set; }
        [JsonConstructor]
        public SheetOrderItem(LOAD_PURPOSE loadPurpose) : base("sheet_prepay", "sheet_order_item_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_ORDER_ITEM;
            if (loadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @" left join info_supcust c on t.supcust_id=c.supcust_id
                                  left join (select oper_id,oper_name as getter_name from info_operator) seller on t.getter_id=seller.oper_id
                                  left join (select oper_id,oper_name as maker_name from info_operator) maker on t.maker_id=maker.oper_id
                                  left join (select oper_id,oper_name as approver_name from info_operator) approver on t.approver_id=approver.oper_id
                                  left join (select sub_id,sub_name as payway1_name from cw_subject) pw1 on t.payway1_id=pw1.sub_id
                                  left join (select sub_id,sub_name as payway2_name from cw_subject) pw2 on t.payway2_id=pw2.sub_id
                                  left join (select sub_id,sub_name as payway3_name from cw_subject) pw3 on t.payway3_id=pw3.sub_id
                                  left join (select sub_id,sub_name as prepay_sub_name from cw_subject) pre on t.prepay_sub_id=pre.sub_id
                                  left join (select sheet_id as order_adjust_sheet_id,sheet_no as order_adjust_sheet_no from sheet_item_ordered_adjust_main) om on om.order_adjust_sheet_id=t.order_adjust_sheet_id

";

                DetailLeftJoin = @"left join info_item_prop i on t.item_id=i.item_id  
                                   left join info_item_brand ib on i.item_brand=ib.brand_id
                                   left join (select class_id classId,class_name,order_index as class_order_index from info_item_class where company_id =~COMPANY_ID) ic on i.item_class=ic.classId 
                                   left join (select batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date from info_item_batch where company_id= ~COMPANY_ID) itb on itb.batch_id = t.batch_id
                                   left join (select item_id,s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,s_unit->>'f3' as s_barcode,s_unit->>'f4' as s_retail_price,s_unit->>'f5' as s_lowest_price,
                                                            m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,m_unit->>'f3' as m_barcode,m_unit->>'f4' as m_retail_price,m_unit->>'f5' as m_lowest_price,
                                                            b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor,b_unit->>'f3' as b_barcode,b_unit->>'f4' as b_retail_price,b_unit->>'f5' as b_lowest_price
                                                    from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,retail_price,lowest_price)) as json from info_item_multi_unit where company_id=~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)) unit_barcode on t.item_id=unit_barcode.item_id                     
    
                    ";

              /*  DetailLeftJoin = @"left join info_item_prop i on t.item_id=i.item_id 
                                       left join (select item_id,real_quantity,
                                                b_unit_no,m_unit_no,s_unit_no,m_unit_factor,b_unit_factor,s_unit_qty,m_unit_qty,b_unit_qty,b_unit_price,m_unit_price,s_unit_price,unit_conv   
                                        from (
                                               SELECT d.item_id,real_quantity,
                                                       (CASE WHEN b_unit_no is not null then sign(COALESCE(qty,0))*floor(COALESCE(abs(qty),0) / b_unit_factor) end) as b_unit_qty,b_unit_no,
                                                       (CASE WHEN m_unit_no is null THEN null ELSE sign(COALESCE(qty,0))*floor((COALESCE(abs(qty),0)%b_unit_factor)/m_unit_factor) END) as m_unit_qty,m_unit_no,
                                                       (CASE WHEN b_unit_no is NOT NULL AND m_unit_no is NOT NULL THEN sign(qty)*floor(COALESCE(abs(qty),0)%b_unit_factor%m_unit_factor)
                                                             WHEN b_unit_no is NOT NULL AND m_unit_no is NULL THEN round(cast(qty % b_unit_factor as numeric),2)
                                                             WHEN b_unit_no is NULL AND m_unit_no is NULL THEN round(qty,2) END) s_unit_qty,s_unit_no,
                                                       (CASE WHEN b_unit_no is not null then round(cast(price_unit*b_unit_factor as numeric),2) end) as b_unit_price,
                                                       (CASE WHEN m_unit_no is null THEN null ELSE round(cast(price_unit*m_unit_factor as numeric),2) END) as m_unit_price, 
                                                       round(price_unit,2) s_unit_price,m_unit_factor,b_unit_factor,
                                                       (case when m_unit_factor is null and b_unit_factor is not null then concat('1',b_unit_no,'=',b_unit_factor,s_unit_no)  
                                                             when (b_unit_factor is not null) and (m_unit_factor is not null) then concat('1',b_unit_no,'=',floor(b_unit_factor::numeric/m_unit_factor::numeric),m_unit_no,'=',b_unit_factor,s_unit_no)
                                                             when b_unit_factor is null then concat('1',s_unit_no)  end ) as unit_conv
                                     FROM (select item_id, (m->>'f1')::numeric m_unit_factor,(b->>'f1')::numeric b_unit_factor,
                                                           s->>'f2' s_unit_no,m->>'f2' m_unit_no,b->>'f2' b_unit_no,s->>'f3' s_barcode,m->>'f3' m_barcode,b->>'f3' b_barcode             
                                             from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode)) as json 
                                            from info_item_multi_unit where company_id=~company_id ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$)  as errr(item_id int, s jsonb,m jsonb, b jsonb)) t 
                                    right JOIN (select item_id,(quantity*unit_factor)::numeric qty,quantity real_quantity,real_price,(real_price/unit_factor)::numeric price_unit from sheet_order_item_detail where company_id=~company_id and sheet_id=~sheet_id) d on d.item_id = t.item_id
                                    ) tem) dd on dd.item_id = t.item_id and dd.real_quantity=t.quantity
    
                    ";*/
            }
            else if (loadPurpose == LOAD_PURPOSE.APPROVE)
                DetailLeftJoin = @$" ";
        }
        public SheetOrderItem() : base("sheet_prepay", "sheet_order_item_detail", LOAD_PURPOSE.APPROVE)
        {  
        }
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.sheet_id + "_" + this.supcust_id + "_" + this.OperID + "_" +  "_" + this.make_brief + "_" + this.payway1_id + "_" + this.payway1_amount + "_" + (this.payway2_amount != 0 ? this.payway2_id + "_" + this.payway2_amount + "_" : "") + (this.payway3_amount != 0 ? this.payway3_id + "_" + this.payway3_amount + "_" : "");
            foreach (var row in SheetRows)
            {
                res += row.item_id + "_" + row.item_name + "_" + row.quantity + row.real_price + row.remark;
            }
            return res;
        }
        public decimal total_qty_num
        {
            get
            {
                decimal d = 0m;
                foreach (var row in SheetRows)
                {
                    d += row.quantity;
                }
                return d;
            }
        }
        public string total_qty_unit
        {
            get
            { 
                Dictionary<string, decimal> dic = new Dictionary<string, decimal>(); 
                foreach (var row in SheetRows)
                {  
                    if (dic.ContainsKey(row.unit_no))
                    {
                        dic[row.unit_no] += row.quantity;
                    }
                    else
                    {
                        dic.Add(row.unit_no, row.quantity);
                    }
                }
                string s = "";
                foreach(var k in dic)
                {
                    s += CPubVars.FormatMoney(k.Value,3) + k.Key;
                }
                return s;
            }
        }
        protected override void InitForSave()
        {
            base.InitForSave();
            paid_amount = now_pay_amount;
            disc_amount = now_disc_amount;
            Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
            foreach (var row in SheetRows)
            {
                if (row.sub_amount == 0 && row.quantity != 0 && !sheetAttribute.ContainsKey("free")) sheetAttribute.Add("free", "true");
            }
            if ((total_amount - paid_amount - disc_amount >= 0.1m) && !sheetAttribute.ContainsKey("arrears")) sheetAttribute.Add("arrears", "true");
            if (disc_amount > 0 && !sheetAttribute.ContainsKey("disc")) sheetAttribute.Add("disc", "true");
            if (appendixPhotos!=null && appendixPhotos.Length > 0)
            {
                // main.Replace(BucketLinkHref + "/", "")
                for (int i = 0, appendixPhotosLength = appendixPhotos.Length; i < appendixPhotosLength; i++)
                {
                    appendixPhotos[i] = appendixPhotos[i].Replace(HuaWeiObs.BucketLinkHref + "/", "");
                }

                sheetAttribute.Add("appendixPhotos", JsonConvert.SerializeObject(appendixPhotos));
            }
            sheet_attribute = JsonConvert.SerializeObject(sheetAttribute);
        }
        [SaveToDB]
        [FromFld]
        public override string sheet_attribute
        {
            get
            { 
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                if (total_amount - paid_amount - disc_amount >= 0.1m) sheetAttribute.Add("arrears", "true");
                if (disc_amount > 0 && !sheetAttribute.ContainsKey("disc")) sheetAttribute.Add("disc", "true"); 
                if (OtherSheetAttributes != null)
                {
                    foreach (var k in OtherSheetAttributes)
                    {
                        if (!sheetAttribute.ContainsKey(k.Key))
                            sheetAttribute.Add(k.Key, k.Value);
                    }
                }
                if (appendix_photos != "" && appendix_photos != "[]")
                {
                    sheetAttribute.Add("appendixPhotos", appendix_photos);
                }

                foreach (var row in SheetRows)
                {
                    if (row.sub_amount == 0 && row.quantity != 0 && !sheetAttribute.ContainsKey("free")) sheetAttribute.Add("free", "true");
                }
                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.bindSheetInfo != null)
                    {
                        this.bindSheetInfo = sheetAttr.bindSheetInfo;
                    }
                    if (sheetAttr.appendixPhotos != null)
                    {
                        this.appendix_photos = sheetAttr.appendixPhotos;
                    }
                }
              
            }
        }
        public class OrderedItem
        {
            public string item_id = "";
            public string unit_no = "";
            public decimal unit_factor = 1;
            public decimal order_price = 0;
            public decimal s_order_price = 0;
            public decimal quantity = 0;
            public decimal s_quantity = 0;
            public decimal balance = 0;
         //   public decimal s_unit_order_price = 0;

        }
        protected class CInfoForApprove : SheetPrepay<SheetRowOrderItem>.CInfoForApprove
        {            
            public List<SheetRowOrderItem> SheetRows = null;
            //public List<OrderedItem> orderedItemsInfo = null;
            public List<OrderedItem> orderedItemsInfo = null;
            public string BizStartPeriod = "";
            public List<Subject> PaywaysInfo = new List<Subject>();
        }

        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql;
            base.GetInfoForApprove_SetQQ(QQ);
            
            if (SheetRows.Count > 0)
            {
                string items_id = "";
                string unit_nos = "";
                foreach (SheetRowOrderItem row in SheetRows)
                {
                    if (items_id != "") items_id += ",";
                    if (unit_nos != "") unit_nos += ",";
                    items_id += row.item_id;
                    unit_nos += "'"+row.unit_no+"'"; 
                }
                sql = $"select item_id,quantity,quantity*unit_factor s_quantity,order_price,order_price/unit_factor s_order_price,order_price * quantity as balance,unit_factor,unit_no from items_ordered_balance where company_id={company_id} and supcust_id={supcust_id}  and item_id in ({items_id}) and prepay_sub_id = {prepay_sub_id}";
                QQ.Enqueue("items_ordered_info", sql);
                sql = $"select item_id,quantity,order_price,order_price * quantity as balance,unit_factor,unit_no from items_ordered_balance where company_id={company_id} and supcust_id={supcust_id} and unit_no in ({unit_nos}) and item_id in ({items_id}) and prepay_sub_id = {prepay_sub_id}";
                QQ.Enqueue("items_ordered_balance", sql);
                sql = $"select item_id,unit_no s_unit_no from info_item_multi_unit where company_id = {company_id} and item_id in ({items_id}) and unit_factor = 1 ";
                QQ.Enqueue("s_unit", sql);
                if (red_flag != "")
                {
                    sql = $"select item_id,unit_no b_unit_no,unit_factor b_unit_factor from info_item_multi_unit where company_id = {company_id} and item_id in ({items_id}) and unit_type='b' ";
                    QQ.Enqueue("b_unit", sql);
                }
                
            }


            string sub_ids = payway1_id;
            if (payway2_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway2_id;
            }
            if (payway3_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway3_id;
            }
            if (sub_ids != "")
            {
                sql = $"select sub_id from cw_subject where company_id = {company_id} and sub_id in ({sub_ids}) and sub_type = 'YS'";
                QQ.Enqueue("prepaySub", sql);
            }
            // SetQQForWeChatInfo(QQ, supcust_id);

        }

        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            
            if (sqlName == "items_ordered_balance")
            {
                //List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
               
                //foreach (SheetRowOrderItem row in SheetRows)
                //{
                //    foreach (dynamic rec in records)
                //    {
                //        if (row.item_id == rec.item_id && row.unit_no==rec.unit_no)
                //        {
                //            decimal order_price = CPubVars.ToDecimal(rec.order_price);
                //            if (Math.Abs(order_price - row.real_price) < 0.0001m)
                //            { 
                //                row.HasOrderedBalance = true;
                //                row.OrderedBalance = CPubVars.ToDecimal(Decimal.Parse(rec.balance, System.Globalization.NumberStyles.Float));
                //                row.OrderedQuantity = CPubVars.ToDecimal(Decimal.Parse(rec.quantity, System.Globalization.NumberStyles.Float));
                //                row.OrderedUnitFactor = CPubVars.ToDecimal(Decimal.Parse(rec.unit_factor, System.Globalization.NumberStyles.Float));
                //                row.OrderedUnitNo = rec.unit_no;
                //                break;
                //            }
                //        }                       
                //    }
                //}
                //info.SheetRows = SheetRows;
            }
            else if (sqlName == "items_ordered_info")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                //info.orderedItemsInfo = records;
                List<OrderedItem> lstOrderedItem=new List<OrderedItem>();
                foreach (SheetRowOrderItem row in SheetRows)
                {
                    decimal orderedTotalQty = 0;
                    decimal orderedTotalBalance = 0;
                    foreach (dynamic rec in records)
                    {
                        decimal s_order_price = CPubVars.ToDecimal(Decimal.Parse(rec.s_order_price.ToString(), System.Globalization.NumberStyles.Float));
                        decimal s_real_price = CPubVars.ToDecimal(Decimal.Parse(row.s_real_price.ToString(), System.Globalization.NumberStyles.Float));
                        if (row.item_id == rec.item_id&& Math.Abs(s_order_price - s_real_price) < 0.001m)
                        {
                            orderedTotalQty += CPubVars.ToDecimal(Decimal.Parse(rec.s_quantity, System.Globalization.NumberStyles.Float)); 
                            orderedTotalBalance +=CPubVars.ToDecimal(Decimal.Parse(rec.balance, System.Globalization.NumberStyles.Float));
                            OrderedItem orderedItem = new OrderedItem();
                            orderedItem.unit_factor = CPubVars.ToDecimal(Decimal.Parse(rec.unit_factor, System.Globalization.NumberStyles.Float));
                            orderedItem.quantity = CPubVars.ToDecimal(Decimal.Parse(rec.quantity, System.Globalization.NumberStyles.Float));
                            orderedItem.s_quantity = CPubVars.ToDecimal(Decimal.Parse(rec.s_quantity, System.Globalization.NumberStyles.Float));
                            orderedItem.balance = CPubVars.ToDecimal(Decimal.Parse(rec.balance, System.Globalization.NumberStyles.Float));
                            orderedItem.order_price = CPubVars.ToDecimal(Decimal.Parse(rec.order_price, System.Globalization.NumberStyles.Float));
                            orderedItem.s_order_price = CPubVars.ToDecimal(Decimal.Parse(rec.s_order_price, System.Globalization.NumberStyles.Float));
                            orderedItem.unit_no = rec.unit_no;
                            orderedItem.item_id = rec.item_id;
                            lstOrderedItem.Add(orderedItem);

                            if (row.unit_no == rec.unit_no)
                            {
                                row.OrderedUnitFactor =  CPubVars.ToDecimal(Decimal.Parse(rec.unit_factor, System.Globalization.NumberStyles.Float));
                                row.OrderedUnitNo = rec.unit_no;
                            }
                            
                        }

                    }
                    if (row.OrderedUnitFactor == 0)
                    {
                        row.OrderedUnitFactor = row.unit_factor;
                        row.OrderedUnitNo = row.unit_no;
                    }
                    if (orderedTotalQty != 0)
                    {
                        row.HasOrderedBalance = true;
                        row.OrderedBalance = orderedTotalBalance;
                        row.OrderedQuantity = orderedTotalQty/ row.OrderedUnitFactor;
                    }

                }
                info.orderedItemsInfo = lstOrderedItem;

            }
            else if (sqlName == "s_unit")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
            }
            else if (sqlName == "prepaySub")
            {
                dynamic record = CDbDealer.Get1RecordFromDr(dr, false);
                if (record != null && record.sub_id != "")
                {
                    info.ErrMsg = "请勿使用预收款支付";
                }
            }
            else if(sqlName == "b_unit")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowOrderItem row in SheetRows)
                    {
                        if (row.item_id == rec.item_id)
                        {
                            row.b_unit_factor = rec.b_unit_factor;
                            row.b_unit_no = rec.b_unit_no;
                        }
                    }
                }
                info.SheetRows = SheetRows;
            }
            //ReadQQDataForWeChatInfo(sqlName, dr, info);

        }

        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
            if (supcust_id == "") return "必须指定客户";
            if (prepay_sub_id == "") return "必须指定预收账户";
            if (getter_id == "" && IsFromWeb) return "必须指定业务员";
            if (now_pay_amount>0 && payway1_id == "") return "必须指定支付方式";

            if (happen_time != "")
            {
                string now = CPubVars.GetDateText(DateTime.Now.Date) + " 23:59";
                string happenTime = happen_time;
                DateTime n = Convert.ToDateTime(now);
                DateTime ap = Convert.ToDateTime(happenTime);
                if (n < ap)
                {
                    return "交易时间请勿超过当日";
                }
            }

            decimal total_sub_amount = 0;
            Dictionary<string, SheetRowOrderItem> dic = new Dictionary<string, SheetRowOrderItem>();
            foreach (var row in SheetRows)
            {
                if (row.quantity <=0)
                {
                    return $"{row.item_name}单位{row.unit_no}价格{row.real_price}数量不能为0";
                }
                total_sub_amount += row.sub_amount;
                if (Math.Abs(row.sub_amount / row.quantity - row.real_price) > 0.02m) return $"{row.item_name}金额和数量*单价有差异";
                string key = row.item_id +"_"  + "_" + row.unit_no + "_" + row.real_price.ToString()+"_"+row.remark; 
                
                /*
                if (dic.ContainsKey(key))
                {
                    return $"{row.item_name}价格{row.real_price}存在重复行";
                }
                else
                   dic.Add(key, row);
                */

            }
            if (Math.Abs(total_sub_amount - total_amount) >= 1) return "明细行合计与总额不等，请检查";

            return "OK";
        }

        public override async Task<CInfoForApproveBase> Load(CMySbCommand cmd, string companyID, string sheetID,
            bool bForRed = false)
        {
            CInfoForApprove info = new CInfoForApprove();
            info=(CInfoForApprove) await base.Load(cmd, companyID, sheetID, bForRed);
            await LoadInfoForPrint(cmd, true, true);
            return info;
        }
        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true,dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting); 
            

			decimal b_qty = 0, m_qty = 0, s_qty = 0;
			 
			Dictionary<string, decimal> dicUnitQty = new Dictionary<string, decimal>(); 
			Dictionary<string, decimal> dicSUnitQty = new Dictionary<string, decimal>();
		 
			decimal l_sum_s_sub_qty = 0;
	 
			decimal l_sum_quantity = 0;
 
			foreach (var row in SheetRows)
			{
				row.SetInfoForPrint(smallUnitBarcode);
				//if (row.inout_flag == 0) continue;

				if (row.quantity > 0)
				{
					b_qty += row.b_quantity;
					m_qty += row.m_quantity;
					s_qty += row.s_quantity;
					if (row.s_sub_qty != "")
						l_sum_s_sub_qty += CPubVars.ToDecimal(row.s_sub_qty);


					if (dicUnitQty.ContainsKey(row.unit_no))
					{
						dicUnitQty[row.unit_no] += row.quantity;
					}
					else dicUnitQty.Add(row.unit_no, row.quantity);

					if (dicSUnitQty.ContainsKey(row.s_unit_no))
					{
						dicSUnitQty[row.s_unit_no] += row.quantity * row.unit_factor;
					}
					else dicSUnitQty.Add(row.s_unit_no, row.quantity * row.unit_factor);

					l_sum_quantity += row.quantity;

				}

			}
			string sumQty = "";
			if (b_qty != 0) sumQty += CPubVars.FormatMoney(b_qty, 2) + "大";
			if (m_qty != 0) sumQty += CPubVars.FormatMoney(m_qty, 2) + "中";
			if (s_qty != 0) sumQty += CPubVars.FormatMoney(s_qty, 2) + "小";

		 
			this.sum_quantity_unit_conv = sumQty;
			 
			//this.sum_item_name = "合计: " + MoneyToUpper(this.total_amount.ToString());

		}


        /*
        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;

            string sql = "";
            if (LeftAmount != 0)
            {
                string changeBal;
                float nChangeBal = LeftAmount * money_inout_flag;
                if (info.ArrearBalance != "")
                {
                    //double bal = Convert.ToDouble(info.ArrearBalance); 
                    changeBal = nChangeBal.ToString();
                    if (changeBal == "-0") changeBal = "0";
                    if (nChangeBal >= 0) changeBal = "+" + nChangeBal.ToString();
                    else changeBal = nChangeBal.ToString();
                    sql += $"update arrears_balance set balance=balance{changeBal} where company_id={company_id} and supcust_id={supcust_id};";
                }
                else
                {
                    sql += $"insert into arrears_balance (company_id,supcust_id,balance) values ({company_id},{supcust_id},{nChangeBal});";

                }
            }
            if (Math.Abs(total_amount) >= -0.01)
            {
                float nChangeBal = total_amount * money_inout_flag;
                float bal = 0;
                info.ChangeBal = nChangeBal;
                if (info.PrepayBalance != "")
                {
                    bal = Convert.ToSingle(info.PrepayBalance);
                    //var tmp = nChangeBal.ToString();
                    //if (tmp == "-0") tmp = "0";
                    //if (nChangeBal >= 0) changeBal = "+" + nChangeBal.ToString();
                    //else changeBal = nChangeBal.ToString();
                    sql += @$"update prepay_balance set balance=balance+({nChangeBal}) where company_id={company_id} and supcust_id={supcust_id} and sub_id = {prepay_sub_id};";

                }
                else
                {
                    sql += $"insert into prepay_balance (company_id,supcust_id,balance,sub_id) values ({company_id},{supcust_id},{nChangeBal},{prepay_sub_id}) " +
                        $"   on conflict (company_id,supcust_id,sub_id) do update set balance=prepay_balance.balance+({nChangeBal});";
                }
                bal += nChangeBal;
                info.Balance = bal;
                var mergedRows = MergeSheetRows(this.SheetRows);
                foreach (SheetRowOrderItem row in mergedRows)
                {
                    string s = "";
                    float qty;
                    float balance;
                    //compute from branch stock qty
                    qty = money_inout_flag * row.quantity;
                    balance = money_inout_flag * row.sub_amount;
                    s = $"insert into items_ordered_balance(company_id,supcust_id,prepay_sub_id,item_id,unit_no,unit_factor,quantity,order_price,balance) values ({company_id},{supcust_id},{prepay_sub_id},{row.item_id},'{row.unit_no}',{row.unit_factor},{qty},{row.real_price},{balance}) " +
                            $"on conflict (company_id,supcust_id,item_id,unit_no,prepay_sub_id,order_price) do update set quantity=items_ordered_balance.quantity+({qty}),balance=items_ordered_balance.balance+({balance});";
                    sql += s;
                }
            }
            
            
            return sql;
        }
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            string redSql = "";
            string sql = "";
            CInfoForApprove info = (CInfoForApprove)info1;
            float changeBal = 0;
            float arrears = 0;

            if (LeftAmount != 0)
            {
                changeBal = LeftAmount * money_inout_flag;
                if (info.ArrearBalance == "") arrears = 0;
                else arrears = Convert.ToSingle(info.ArrearBalance);
                arrears += changeBal;
                if (red_flag.IsValid())
                {
                    redSql += @$"update client_account_history set red_flag = '1' where company_id = {company_id} and sheet_id = {red_sheet_id} and sheet_type = 'DH' and sub_type = 'QK';";
                    sql += $"insert into client_account_history(company_id,happen_time,sheet_type,sheet_id,change_amount,now_balance,supcust_id,sub_type,red_flag) values ({company_id},'{CPubVars.GetDateText(approve_time)}','DH',{sheetID},{changeBal},{arrears},{supcust_id},'QK',{red_flag});";
                }
                if (red_flag.IsInvalid())
                {
                    sql += $"insert into client_account_history(company_id,happen_time,sheet_type,sheet_id,change_amount,now_balance,supcust_id,sub_type) values ({company_id},'{CPubVars.GetDateText(approve_time)}','DH',{sheetID},{changeBal},{arrears},{supcust_id},'QK');";
                }
            }
            if (Math.Abs(total_amount) >= 0)
            {
                if (red_flag.IsValid())
                {
                    redSql += @$"update client_account_history set red_flag = '1' where company_id = {company_id} and sheet_id = {red_sheet_id} and sheet_type = 'DH';";
                    sql += @$"insert into client_account_history(company_id, happen_time, sheet_type, sheet_id, change_amount, now_balance, supcust_id, sub_id, sub_type,red_flag)
                            values({ company_id},'{CPubVars.GetDateText(approve_time)}','DH',{ sheet_id},{ info.ChangeBal},{ info.Balance},{ supcust_id},{ prepay_sub_id},'YS',{red_flag});";
                }
                if (red_flag.IsInvalid())
                {
                    sql += @$"insert into client_account_history(company_id, happen_time, sheet_type, sheet_id, change_amount, now_balance, supcust_id, sub_id, sub_type)
                            values({ company_id},'{CPubVars.GetDateText(approve_time)}','DH',{ sheet_id},{ info.ChangeBal},{ info.Balance},{ supcust_id},{ prepay_sub_id},'YS');";
                }
            }
            
            cmd.CommandText = redSql + sql;
            await cmd.ExecuteNonQueryAsync();
            
        }
        */


        protected   string GetApproveSQL_old(CInfoForApproveBase info1)
        {
            string sql = "";
            CInfoForApprove info = (CInfoForApprove)info1;
              
            //if (Math.Abs(total_amount) >= -0.01m)
            //{ 
            var mergedRows = MergeSheetRows(this.SheetRows);
            foreach (SheetRowOrderItem row in mergedRows)
            {
                string s = "";
                decimal qty;
                decimal balance;
                //compute from branch stock qty
                qty = money_inout_flag * row.quantity;
                balance = money_inout_flag * row.sub_amount;
                var newQty = row.OrderedQuantity * row.OrderedUnitFactor + qty * row.unit_factor;
                if (newQty < -0.01m)
                {
                    info.ErrMsg = $"{row.item_name}剩余定货数量只有{row.OrderedQuantity}{row.OrderedUnitNo},不够";
                    return "";
                }
                if (red_flag == "2")
                {
                    var row_s_qty = qty * row.unit_factor;
                    
                    decimal row_s_price = CPubVars.ToDecimal(Decimal.Parse(row.s_real_price.ToString(), System.Globalization.NumberStyles.Float));
                    foreach (var orderedItem in info.orderedItemsInfo)
                    {
                        if(row.item_id == orderedItem.item_id && orderedItem.s_quantity>0 && Math.Abs(row_s_price- orderedItem.s_order_price)<0.01m)
                        {
                            if (row_s_qty + orderedItem.s_quantity >= 0)
                            {
                                var real_qty = row_s_qty / orderedItem.unit_factor;
                                row_s_qty = 0;
                                s += @$"insert into items_ordered_balance(company_id,supcust_id,prepay_sub_id,item_id,unit_no,unit_factor,quantity,order_price) values ({company_id},{supcust_id},{prepay_sub_id},{row.item_id},'{orderedItem.unit_no}',{orderedItem.unit_factor},{qty},{orderedItem.order_price})
                                on conflict (company_id,supcust_id,item_id,unit_no,prepay_sub_id,order_price) do update set quantity=items_ordered_balance.quantity+({real_qty});";
                                break;
                               
                            }
                            else
                            {
                                row_s_qty = row_s_qty + orderedItem.s_quantity;
                                s+= @$"insert into items_ordered_balance(company_id,supcust_id,prepay_sub_id,item_id,unit_no,unit_factor,quantity,order_price) values ({company_id},{supcust_id},{prepay_sub_id},{row.item_id},'{orderedItem.unit_no}',{orderedItem.unit_factor},{qty},{orderedItem.order_price})
                                on conflict (company_id,supcust_id,item_id,unit_no,prepay_sub_id,order_price) do update set quantity=0;";
                            }

                        }
                    }
					if (row_s_qty < -0.001m)
					{
                        info.ErrMsg = $"{row.item_name}剩余定货数量只有{row.OrderedQuantity}{row.OrderedUnitNo},不够红冲的";
                        return "";
                    }
                }
                else
                {
                    s = @$"insert into items_ordered_balance(company_id,supcust_id,prepay_sub_id,item_id,unit_no,unit_factor,quantity,order_price) values ({company_id},{supcust_id},{prepay_sub_id},{row.item_id},'{row.unit_no}',{row.unit_factor},{qty},{row.real_price})
                            on conflict (company_id,supcust_id,item_id,unit_no,prepay_sub_id,order_price) do update set quantity=items_ordered_balance.quantity+({qty});";

                }

                sql += s;
            }
            //}
            return sql;
        }

        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            string sql = "";
            CInfoForApprove info = (CInfoForApprove)info1;
             
            return sql;
        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            string sql = "";
            CInfoForApprove info =(CInfoForApprove) info1;
            await base.OnSheetIDGot(cmd, sheet_id, info);
            if (this.SheetType == "DH")
            {
                if (info.WeChatInfo != null && info.WeChatInfo.Count > 0)
                {
                    await SendSheetSimple(cmd,_httpClientFactory,
                    info.WeChatInfo,
                    this.SheetType,
                    this.now_disc_amount.ToString(),
                    this.left_amount.ToString(),
                    this.payway1_name,
                    this.payway1_amount.ToString(),
                    this.payway2_name,
                    this.payway2_amount.ToString(),
                    this.payway3_name,
                    this.payway3_amount.ToString(),
                    this.supcust_id);
                }
                foreach(var row in this.SheetRows)
				{
                   
				}
            }

            var mergedRows = MergeSheetRows(this.SheetRows);
            foreach (SheetRowOrderItem row in mergedRows)
            {
                string s = "";
                decimal qty;
                decimal balance;
                //compute from branch stock qty
                qty = money_inout_flag * row.quantity;
                balance = money_inout_flag * row.sub_amount;
                var newQty = row.OrderedQuantity * row.OrderedUnitFactor + qty * row.unit_factor;
                if (newQty < -0.01m)
                {
                    info.ErrMsg = $"{row.item_name}剩余定货数量只有{row.OrderedQuantity}{row.OrderedUnitNo},不够";
                    return;
                }
                if (red_flag == "2")
                {
                    var row_s_qty = row.quantity * row.unit_factor;

                    decimal row_s_price = CPubVars.ToDecimal(Decimal.Parse(row.s_real_price.ToString(), System.Globalization.NumberStyles.Float));
                    foreach (var orderedItem in info.orderedItemsInfo)
                    {
                        if (row.item_id == orderedItem.item_id  && Math.Abs(row_s_price - orderedItem.s_order_price) < 0.01m && orderedItem.quantity > 0)

                        {
                            var real_qty = row_s_qty / orderedItem.unit_factor;
                            string new_qty = "";
                            if (orderedItem.s_quantity - row_s_qty >= 0)
                            { 
                                row_s_qty = 0;
                                // s += @$"insert into items_ordered_balance(company_id,supcust_id,prepay_sub_id,item_id,unit_no,unit_factor,quantity,order_price) values ({company_id},{supcust_id},{prepay_sub_id},{row.item_id},'{orderedItem.unit_no}',{orderedItem.unit_factor},{real_qty},{orderedItem.order_price})
                                // on conflict (company_id,supcust_id,item_id,unit_no,prepay_sub_id,order_price) do update set quantity=items_ordered_balance.quantity+({real_qty});";
                                // break;
                                new_qty = $"items_ordered_balance.quantity-({real_qty})";
                            }
                            else
                            {
                                row_s_qty = row_s_qty - orderedItem.s_quantity;
                                new_qty = "0";
                            }

                            s += @$"insert into items_ordered_balance(company_id,supcust_id,prepay_sub_id,item_id,unit_no,unit_factor,quantity,order_price) values ({company_id},{supcust_id},{prepay_sub_id},{row.item_id},'{orderedItem.unit_no}',{orderedItem.unit_factor},{real_qty},{orderedItem.order_price})
                                on conflict (company_id,supcust_id,item_id,unit_no,prepay_sub_id,order_price) do update set quantity={new_qty}, order_item_sheets_id = replace(items_ordered_balance.order_item_sheets_id,',{red_sheet_id},',','), order_item_sheets_no = replace(items_ordered_balance.order_item_sheets_no,',{sheet_no},',',');";
                            if (row_s_qty <= 0) break;

                        }
                    }
                    if (row_s_qty > 0.001m)
                    {
                        info.ErrMsg = $"{row.item_name}剩余定货数量只有{row.OrderedQuantity}{row.OrderedUnitNo},不够红冲的";
                        return;
                    }
                }
                else
                {
                    s = @$"insert into items_ordered_balance(company_id,   supcust_id,   prepay_sub_id,item_id,        unit_no,       unit_factor,   quantity, order_price,  order_item_sheets_id,order_item_sheets_no) 
                                                     values ({company_id},{supcust_id},{prepay_sub_id},{row.item_id},'{row.unit_no}',{row.unit_factor},{qty},{row.real_price},',{sheet_id},',      ',{sheet_no},')
                            on conflict (company_id,supcust_id,item_id,unit_no,prepay_sub_id,order_price) do update set quantity=items_ordered_balance.quantity+({qty}),order_item_sheets_id=coalesce(items_ordered_balance.order_item_sheets_id,'')||',{sheet_id},',order_item_sheets_no=coalesce(items_ordered_balance.order_item_sheets_no,'')||',{sheet_no},';";

                }

                sql += s; 
            }


            #region 更新现金银行余额
            string sql_cb = "";
            if (info.BizStartPeriod != "" && info.PaywaysInfo != null && !IsImported)
            {
                Dictionary<string, decimal> pws = new Dictionary<string, decimal>();
                Subject pw1 = info.PaywaysInfo.Find(p => p.sub_id == payway1_id && p.sub_type == "QT");
                if (pw1 != null && payway1_amount != 0)
                {
                    if (!pws.ContainsKey(payway1_id)) pws.Add(payway1_id, payway1_amount);
                    else pws[payway1_id] += payway1_amount;
                }
                Subject pw2 = info.PaywaysInfo.Find(p => p.sub_id == payway2_id && p.sub_type == "QT");
                if (pw2 != null && payway2_amount != 0)
                {
                    if (!pws.ContainsKey(payway2_id)) pws.Add(payway2_id, payway2_amount);
                    else pws[payway2_id] += payway2_amount;
                }
                Subject pw3 = info.PaywaysInfo.Find(p => p.sub_id == payway3_id && p.sub_type == "QT");
                if (pw3 != null && payway3_amount != 0)
                {
                    if (!pws.ContainsKey(payway3_id)) pws.Add(payway3_id, payway3_amount);
                    else pws[payway3_id] += payway3_amount;
                }
                if (pws.Count() > 0)
                {
                    sql_cb = base.UpdateCashBankBalance(pws);
                    sql += sql_cb;
                }
            }
            #endregion


            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
        }
        public override string GetWeChatMsgHead()
        {
            string sheetTypeName = "";
            string first = "";
            switch (this.SheetType) {
                case "DH" :  sheetTypeName = "定货单"; break;
            }
            switch (this.red_flag)
            {
                case "": first = $"您有新的【{sheetTypeName}】,来自【{this.company_name}】,请注意查收"; break;
                case "2": first = $"您的【{sheetTypeName}】【被红冲】,来自【{this.company_name}】,请注意查收"; break;
            }
            return first;
        }
        public override string GetWeChatMsgTail()
        {
            string remark = "";
            if (this.sheet_no != "") remark += "单据编号：" + this.sheet_no + "\n";
            return remark;
        }

        protected List<SheetRowOrderItem> MergeSheetRows(List<SheetRowOrderItem> rows)
        {
            Dictionary<string, SheetRowOrderItem> rowsDict = new Dictionary<string, SheetRowOrderItem>();
            foreach (var sheetRow in rows)
            {
                decimal bUnitFactor = 1;
                if (sheetRow.b_unit_factor!=null && sheetRow.b_unit_factor != "") bUnitFactor = CPubVars.ToDecimal(sheetRow.b_unit_factor);
                decimal sUnitPrice = sheetRow.real_price / sheetRow.unit_factor;
                
             //   string skey = sheetRow.item_id +  "_" + sUnitPrice.ToString();
                string skey = sheetRow.item_id + "_" + CPubVars.FormatMoney(sUnitPrice,2);
                SheetRowOrderItem curRow = null;

                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = new SheetRowOrderItem();
                    curRow.item_id = sheetRow.item_id;
                    curRow.item_name = sheetRow.item_name;
                    curRow.quantity = sheetRow.quantity;//*sheetRow.unit_factor/ bUnitFactor;
                    curRow.unit_no = sheetRow.unit_no;
                    curRow.unit_factor = sheetRow.unit_factor;
                    curRow.real_price = sheetRow.real_price;
                    curRow.sub_amount = sheetRow.sub_amount;
                    curRow.inout_flag = sheetRow.inout_flag;
                    curRow.OrderedBalance = sheetRow.OrderedBalance;
                    curRow.OrderedQuantity = sheetRow.OrderedQuantity;
                    curRow.OrderedUnitFactor = sheetRow.OrderedUnitFactor;
                    curRow.OrderedUnitNo = sheetRow.OrderedUnitNo; 
                    curRow.HasOrderedBalance = sheetRow.HasOrderedBalance;
                    curRow.b_unit_no = sheetRow.b_unit_no;
                    curRow.b_unit_factor = bUnitFactor.ToString();
                    
                    rowsDict.Add(skey, curRow);
                    curRow.QtyOfSmallUnit += sheetRow.quantity * sheetRow.unit_factor;
                }
                else
                {
                   
                    curRow.QtyOfSmallUnit += sheetRow.quantity * sheetRow.unit_factor;
                    if (bUnitFactor >1)
                    {
                        curRow.unit_no = sheetRow.b_unit_no;
                        curRow.unit_factor =CPubVars.ToDecimal(sheetRow.b_unit_factor);
                        curRow.quantity = curRow.QtyOfSmallUnit / bUnitFactor;
                        curRow.real_price = sUnitPrice * bUnitFactor;
                    }
                    else
                    {
                        curRow.quantity = curRow.QtyOfSmallUnit;
                    }                   
                    curRow.sub_amount += sheetRow.sub_amount;                   
                }               
            }
            List<SheetRowOrderItem> newList = new List<SheetRowOrderItem>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }





    }

}
