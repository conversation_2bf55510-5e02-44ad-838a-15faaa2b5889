@page
@model ArtisanManage.Pages.BaseInfo.FeeOutsViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "FeeOutEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = rs.data.record;
                        rows[0].i = rows[0].sub_id;


                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "branch_name",rs.data.record.branch_name);
                }
                $("#popItem").jqxWindow('close');
            };
        });
          
    	var newCount = 1;

        function btnAddItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="FeeOutEdit?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                    if (args.datafield == "sub_name") {
                        var sub_id = args.row.bounddata.sub_id;
                        var sub_name = id = args.row.bounddata.sub_name;
                        var rowIndex = args.rowindex;
                        if (ForSelect) {

                            //  var rows = $('#gridItems').jqxGrid('getrows')
                            //var checkedRows = []
                            //var rows = $('#gridItems').jqxGrid('getrows')
                           // var row = rows.find(r => r.uid == rowIndex)
                            //checkedRows.push(getSelectRow(row))

                            var msg = {
                                msgHead: 'FeeOutView', action: 'select',sub_id:sub_id,sub_name:sub_name
                            }

                            /*var msg = {
                                msgHead: 'ItemsView', action: 'select', item_id: item_id, item_name: item_name, order_sub_id: order_sub_id, order_sub_name: order_sub_name, order_price: order_price, unit_factor: unit_factor, order_qty:order_qty, order_qty_unit:order_qty_unit
                            };*/
                            //var keys = Object.keys(window.g_checkedRows)
                            //if (keys.length > 0) {
                               // window.onGridRowCheck(rowIndex)
                               // return
                             

                            window.parent.postMessage(msg, '*');
                        }
                        else {
                             $('#popItem').jqxWindow('open');
                           
                             $("#popItem").jqxWindow('setContent', '<iframe src="FeeOutEdit?operKey=' + g_operKey + '&sub_id=' + id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');

                        }
                    }
                });
                $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                QueryData();
            });
    </script>
</head>

<body>
 
    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;">
        <div><input id="searchString" style="font-size:14px; border-radius:6px;border-color:#ddd;border-width:0.5px; width:200px;height:25px;" placeholder="请输入简拼/名称" /></div>
        <div><button onclick="btnAddItem_click()">新增支出</button></div>
    </div>
     
    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:100%;margin-bottom:10px;"></div>
     

    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">支出信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    
</body>
</html>