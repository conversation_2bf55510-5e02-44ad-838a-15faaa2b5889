using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NPOI.SS.Formula.Functions;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ClientArrearsModel : PageQueryModel
    { 
        public ClientArrearsModel(CMySbCommand cmd) : base(Services.MenuId.arrearsBlance)
        {
            this.cmd = cmd;
            this.PageTitle = "应收款";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddYears(-1))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客户",LabelFld="sup_name",Checkboxes=true, ButtonUsage="list",CompareOperator="=",DropDownWidth = "200",SqlFld="sc.supcust_id",QueryByLabelLikeIfIdEmpty=true,
                SqlForOptions=CommonTool.selectSupcust} },
                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",LabelFld="seller_name",ButtonUsage="list",ForQuery=false,SqlForOptions=CommonTool.selectSellers } },
                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
                {"day_id",new DataItem(){FldArea="divHead",SqlFld="ivdc.day_id",Title="所属日程",LabelFld="day_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150",CompareOperator="=",
                    SqlForOptions=@$"SELECT ivd.day_id AS v,ivd.day_name AS l,ivd.schedule_id AS pv FROM info_visit_day ivd WHERE company_id = ~COMPANY_ID UNION
                    (SELECT ivs.schedule_id AS v,ivs.schedule_name AS l,0 AS pv FROM info_visit_schedule ivs WHERE company_id= ~COMPANY_ID )"
                }},
                {
                    "senders_id",
                    new DataItem()
                    {
                        FldArea = "divHead", Title = "送货员",SqlFld = "senders_id", LabelFld = "senders_name", ButtonUsage = "list",ForQuery=false,
                        DealQueryItem = status => ""+status+"",
                        SqlForOptions=CommonTool.selectSenders,  //SqlForOptions = "select oper_id as v,oper_name as l,py_str as z from info_operator",
                        CompareOperator = "like"
                    }
                },
                
                 {"arrear_status",new DataItem(){FldArea="divHead",ForQuery=false, Title = "结清状态",LabelFld = "cls_arrear_status", LabelInDB = false, Value = "not_cleared", Label = "未结清",ButtonUsage = "list", QueryOnChange = false, Hidden=false, CompareOperator = "=", NullEqualValue = "all",
                     Source = @"[{v:'not_cleared',l:'未结清'},
                               {v:'period_not_cleared',l:'期间未结清'}, 
                               {v:'all',l:'所有'}]"
                 }},
                {"arrears_order_status",new DataItem(){ FldArea="divHead",Title="对账状态",LabelFld = "arrears_order_status_v",LabelInDB = false,ForQuery=false,ButtonUsage="list",Value = "all", Label = "所有",QueryOnChange = false, Hidden=false, CompareOperator = "=", NullEqualValue = "all",
                    Source = @"[{v:'none',l:'待对帐'},
                                {v:'unapproved',l:'对账中'},
                                {v:'approved',l:'待收款'},                                   
                                {v:'all',l:'所有'}]"
                }},
                {"arrearsAge",new DataItem(){FldArea="divHead",Title="账龄大于(天)",CtrlType="jqxInput",ForQuery=false, Width="150" }},
                //{"sup_group",new DataItem(){FldArea="divHead",Title="渠道",LabelFld="sup_group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sc.sup_group",
                //    SqlForOptions="select distinct sup_group as v, sup_group as l from info_supcust where company_id=~COMPANY_ID and sup_group is not null and sup_group != '' order by sup_group"
                //}},
                {"group_id",new DataItem(){FldArea="divHead",Title="渠道",Checkboxes=true,LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sc.sup_group",
                    SqlForOptions="select group_id as v,group_name as l from info_supcust_group"
                }},
                {"showNoProcess",new DataItem(){FldArea="divHead",Title="显示无发生额的客户",CtrlType="jqxCheckBox",ForQuery=false,Value="true"}},
                
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       
                       {"supcust_id",     new DataItem(){Title="supcust_id",Hidden = true,SqlFld="sc.supcust_id", HideOnLoad = true} },
                       {"sup_name",     new DataItem(){Title="客户", Width="300",Linkable = true ,Sortable=true}},
                       {"balance",     new DataItem(){Title="总欠款", Width="15%",ShowSum=true,Sortable=true, Linkable=true}},
                       {"supcust_no",   new DataItem(){Title="客户编号",  Width="100", Hidden=true,Sortable=true}},
                       {"region_name",new DataItem(){Title="片区", Width="100",Sortable=true}},
                       {"group_name", new DataItem(){Title="渠道", Width="100", SqlFld="group_name", Sortable=true}},
                       {"sum_x_qk_add",   new DataItem(){Title="销售欠款", Width="100",ShowSum=true,Sortable=true,Hidden = true,}},
                       {"sum_t_qk_add",   new DataItem(){Title="退货欠款", Width="100",ShowSum=true,Sortable=true,Hidden = true,}},
                       {"arrear",new DataItem(){Title="时间段", Width="60", Sortable=true, FuncGetSubColumns = async (col) =>
                            new ColumnsResult{

                            Columns=new Dictionary<string,DataItem>()
                              {
                       {"total_amount",   new DataItem(){Title="单据金额", Width="70",ShowSum=true,Sortable=true,Linkable=false}},
                       {"qk_add",   new DataItem(){Title="期间欠款", Width="70",ShowSum=true,Sortable=true,Linkable=true}},
                       {"sk_add",   new DataItem(){Title="期后收款", Width="70",ShowSum=true,Sortable=true,Linkable=true}},
                       {"disc_amount",   new DataItem(){Title="已优惠", Width="60",ShowSum=true}},
                       {"qk_reduce",   new DataItem(){Title="已收", Width="60",ShowSum=true}},
                       {"left_amount",     new DataItem(){Title="尚欠", Width="60",ShowSum = true,Sortable=true,Linkable=true}},
                       { "seller_qk",     new DataItem(){Title="尚欠业务员", Width="100",SqlFld="seller_name"}}
                                }
                            }
                       } },
                       {"day_id",     new DataItem(){Title="所属日程",Hidden = true,SqlFld="day_name"} },
                       //{"net_amount",   new DataItem(){Title="销售净额", Width="15%",SqlFld="sum(total_amount*money_inout_flag)",ShowSum=true}},
                       //{"disc_amount",     new DataItem(){Title="优惠金额", Width="15%",SqlFld="sum(disc_amount*money_inout_flag)",ShowSum = true}},
                     },
                     QueryFromSQL=@"
FROM 
(
    SELECT b.supcust_id, round((SUM(COALESCE ( sm.x_qk_add, 0 )))::numeric,2) sum_x_qk_add,
		 round( ( SUM(COALESCE ( sm.t_qk_add, 0 )) )::numeric,2) sum_t_qk_add,
         round( (SUM(COALESCE(sm.total_amount, 0)) )::numeric,2) as total_amount,
         round(( SUM (COALESCE ( sm.total_amount, 0 ) - COALESCE ( sm.now_disc_amount, 0 ) - COALESCE ( sm.now_pay_amount, 0 )))::numeric,2) qk_add, 
         SUM ( COALESCE (sm.disc_amount, 0 ) - COALESCE ( sm.now_disc_amount, 0 ) ) 
         disc_amount, SUM ( COALESCE (sm.paid_amount, 0 ) - COALESCE ( sm.now_pay_amount, 0 ) ) qk_reduce,
         round ( ( SUM ( COALESCE ( sm.total_amount, 0 ) - COALESCE ( sm.disc_amount, 0 ) - COALESCE ( sm.paid_amount, 0 )) )::numeric,2) left_amount,
         balance,STRING_AGG(DISTINCT case when COALESCE ( sm.total_amount, 0 ) - COALESCE ( sm.disc_amount, 0 ) - COALESCE ( sm.paid_amount, 0 )<>0 then oper_name else null end, ',' ) seller_name
    FROM arrears_balance b
    LEFT JOIN 
    (
        (
            SELECT CASE WHEN  sheet_type='X' THEN COALESCE ( total_amount, 0 ) - COALESCE ( now_disc_amount, 0 ) - COALESCE ( now_pay_amount, 0 )
			     else 0 end as x_qk_add,
			     case WHEN  sheet_type='T' THEN COALESCE ( total_amount, 0 ) - COALESCE ( now_disc_amount, 0 ) - COALESCE ( now_pay_amount, 0 )
			     else 0 end as t_qk_add,
                 sheet_id,supcust_id,seller_id,senders_id,money_inout_flag,total_amount*money_inout_flag total_amount,disc_amount*money_inout_flag disc_amount,
                 paid_amount*money_inout_flag paid_amount,now_pay_amount*money_inout_flag now_pay_amount,now_disc_amount*money_inout_flag now_disc_amount, arrears_order_sheet_id
            FROM sheet_sale_main 
            WHERE company_id =~COMPANY_ID  and happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay' ~VAR_seller_id  ~VAR_senders_id   and red_flag is null and approve_time is not null ~VAR_arrears_age_condi
    
            UNION ALL 
      
            SELECT 0 x_qk_add,0 t_qk_add ,sheet_id,supcust_id,getter_id seller_id,null senders_id,money_inout_flag,total_amount*money_inout_flag total_amount,disc_amount*money_inout_flag disc_amount,
                   paid_amount*money_inout_flag paid_amount,now_pay_amount*money_inout_flag now_pay_amount,now_disc_amount*money_inout_flag now_disc_amount , arrears_order_sheet_id
            FROM sheet_prepay
            WHERE company_id =~COMPANY_ID  and happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay' ~VAR_getter_id   and red_flag is null and prepay_sub_id <> '-1' and approve_time is not null ~VAR_arrears_age_condi 
    
            UNION ALL 

            SELECT 0 x_qk_add,0 t_qk_add ,sheet_id,supcust_id,getter_id seller_id,null senders_id,money_inout_flag,total_amount * money_inout_flag total_amount,disc_amount * money_inout_flag disc_amount,
                   paid_amount * money_inout_flag paid_amount,now_pay_amount * money_inout_flag now_pay_amount,now_disc_amount * money_inout_flag now_disc_amount , arrears_order_sheet_id
            FROM sheet_fee_out_main 
            WHERE company_id = ~COMPANY_ID  AND happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay'  ~VAR_getter_id  and red_flag is null and approve_time is not null ~VAR_arrears_age_condi 
        ) t 
        LEFT JOIN info_operator io on io.oper_id=t.seller_id
    ) sm ON b.supcust_id = sm.supcust_id and b.company_id = ~COMPANY_ID
    LEFT JOIN (select sheet_id ,approve_time ,red_flag , company_id FROM sheet_get_arrears_order_main WHERE company_id = ~COMPANY_ID and red_flag is null ) aom on  sm.arrears_order_sheet_id = aom.sheet_id
    WHERE b.company_id =~COMPANY_ID  ~VAR_not_cleared_condi ~VAR_tail_condi_seller_id ~VAR_arrears_order_condi

    GROUP BY b.supcust_id,b.balance ~VAR_period_not_cleared_condi
    ORDER BY left_amount desc
) t 
LEFT JOIN 
(
    SELECT sum(sgad.now_pay_amount+sgad.now_disc_amount) sk_add,sm.supcust_id  
    FROM 
    (
        select sheet_id, supcust_id,'X' m_sheet_type from sheet_sale_main WHERE company_id =~COMPANY_ID  and happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay' ~VAR_seller_id  ~VAR_senders_id   and red_flag is null and approve_time is not null ~VAR_arrears_age_condi
        UNION ALL
        select sheet_id, supcust_id,'YS' m_sheet_type from sheet_prepay WHERE company_id =~COMPANY_ID  and happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay' ~VAR_getter_id and red_flag is null and approve_time is not null ~VAR_arrears_age_condi
        UNION ALL
        select sheet_id, supcust_id,'ZC' m_sheet_type from sheet_fee_out_main WHERE company_id =~COMPANY_ID  and happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay' ~VAR_getter_id and red_flag is null and approve_time is not null ~VAR_arrears_age_condi
    )sm 
    left join sheet_get_arrears_detail sgad on sgad.COMPANY_ID= ~COMPANY_ID and sgad.m_sheet_type=sm.m_sheet_type and sgad.mm_sheet_id=sm.sheet_id
    left join sheet_get_arrears_main sgam on sgam.COMPANY_ID= ~COMPANY_ID and sgad.sheet_id=sgam.sheet_id 
    WHERE sgam.happen_time >= '~VAR_endDay'  ~VAR_getter_id  and red_flag is null and approve_time is not null 
    group by sm.supcust_id
) gam on t.supcust_id = gam.supcust_id
LEFT JOIN info_supcust sc ON t.supcust_id = sc.supcust_id AND sc.company_id =~COMPANY_ID  
LEFT JOIN (select group_id, group_name, company_id from info_supcust_group) isg on sc.sup_group = isg.group_id and isg.company_id = ~COMPANY_ID

LEFT JOIN 
(
    SELECT supcust_id, string_agg ( region_name :: TEXT, '/' ) AS region_name 
    FROM
	(
	    SELECT supcust_id, region_name 
	    FROM
		( 
            SELECT supcust_id, regexp_split_to_table( other_region, '/' ) region_id FROM info_supcust WHERE company_id = ~COMPANY_ID  
        ) T LEFT JOIN info_region re ON re.company_id = ~COMPANY_ID  AND re.region_id :: TEXT = T.region_id 
	    WHERE T.region_id <> '' 
	) tt 
    GROUP BY supcust_id
) r on t.supcust_id  = r.supcust_id
LEFT JOIN  
(
    SELECT DISTINCT on (supcust_id,company_id) supcust_id,company_id,day_id FROM info_visit_day_client where  company_id = ~COMPANY_ID ~VAR_day_id_condi
) ivdc on ivdc.supcust_id = sc.supcust_id and ivdc.company_id = ~COMPANY_ID 
LEFT JOIN  
(
    SELECT company_id,day_id,day_name FROM info_visit_day where  company_id = ~COMPANY_ID ~VAR_day_id_condi
) dayt on ivdc.day_id = dayt.day_id and dayt.company_id = ~COMPANY_ID 
WHERE sc.supcust_flag in ('C','CS')  ~VAR_showNoProcess
 
	 ", 

                          /*                QueryFromSQL=@"
FROM 
(
    select
        b.supcust_id,
        round( (SUM (CASE WHEN sub_type = 'QK' AND cah.sheet_type IN ( 'X', 'YS', 'DH', 'T','ZC' ) 	AND cah.happen_time >='~VAR_startDay ' 	AND cah.happen_time <='~VAR_endDay' THEN	COALESCE ( sm.total_amount, 0 ) -COALESCE ( sm.now_disc_amount, 0 )- COALESCE ( sm.now_pay_amount, 0 ) ELSE 0 	END ) + SUM (	CASE	WHEN sub_type = 'QK' 	AND cah.sheet_type IN ( 'X', 'YS', 'DH', 'T','ZC' ) 		AND cah.happen_time >= '~VAR_startDay '  	AND cah.happen_time <= '~VAR_endDay' THEN		COALESCE ( sp.total_amount, 0 ) -COALESCE ( sp.now_disc_amount, 0 )- COALESCE ( sp.now_pay_amount, 0 ) ELSE 0 END)+SUM (CASE WHEN sub_type = 'QK' AND cah.sheet_type IN ( 'X', 'YS', 'DH', 'T', 'ZC' ) AND cah.happen_time >= '~VAR_startDay ' AND cah.happen_time <=  '~VAR_endDay' THEN COALESCE ( sf.total_amount, 0 ) - COALESCE ( sf.now_disc_amount, 0 ) - COALESCE ( sf.now_pay_amount, 0 ) ELSE 0 END ) )::numeric,2) qk_add,
        SUM ( CASE WHEN sub_type = 'QK'AND cah.sheet_type in ('X','YS','DH','T','ZC') and cah.happen_time >= '~VAR_startDay' AND cah.happen_time <= '~VAR_endDay' THEN COALESCE (sp.disc_amount, 0 ) - COALESCE ( sp.now_disc_amount, 0 ) ELSE 0 END ) + SUM ( CASE WHEN sub_type = 'QK'AND cah.sheet_type in ('X','YS','DH','T','ZC') and cah.happen_time >= '~VAR_startDay' AND cah.happen_time <= '~VAR_endDay' THEN COALESCE (sm.disc_amount, 0 ) - COALESCE ( sm.now_disc_amount, 0 ) ELSE 0 END ) + SUM (CASE WHEN sub_type = 'QK' AND cah.sheet_type IN ( 'X', 'YS', 'DH', 'T', 'ZC' ) AND cah.happen_time >= '~VAR_startDay' AND cah.happen_time <=  '~VAR_endDay' THEN COALESCE ( sf.disc_amount, 0 ) - COALESCE ( sf.now_disc_amount, 0 )ELSE 0 END ) disc_amount,
        SUM ( CASE WHEN sub_type = 'QK'AND cah.sheet_type in ('X','YS','DH','T','ZC') and cah.happen_time >= '~VAR_startDay' AND cah.happen_time <= '~VAR_endDay' THEN COALESCE (sp.paid_amount, 0 ) - COALESCE ( sp.now_pay_amount, 0 ) ELSE 0 END ) + SUM ( CASE WHEN sub_type = 'QK'AND cah.sheet_type in ('X','YS','DH','T','ZC') and cah.happen_time >= '~VAR_startDay' AND cah.happen_time <= '~VAR_endDay' THEN COALESCE (sm.paid_amount, 0 ) - COALESCE ( sm.now_pay_amount, 0 ) ELSE 0 END ) +SUM (CASE WHEN sub_type = 'QK' AND cah.sheet_type IN ( 'X', 'YS', 'DH', 'T', 'ZC' ) AND cah.happen_time >= '~VAR_startDay' AND cah.happen_time <=  '~VAR_endDay' THEN COALESCE ( sf.paid_amount, 0 ) - COALESCE ( sf.now_pay_amount, 0 )  ELSE 0 END )      qk_reduce,
        round((SUM ( CASE WHEN sub_type = 'QK'AND cah.sheet_type in ('X','YS','DH','T','ZC') and cah.happen_time >= '~VAR_startDay' AND cah.happen_time <= '~VAR_endDay' THEN COALESCE ( sp.total_amount, 0 ) - COALESCE ( sp.disc_amount, 0 ) - COALESCE ( sp.paid_amount, 0 )ELSE 0 END ) + SUM ( CASE WHEN sub_type = 'QK'AND cah.sheet_type in ('X','YS','DH','T','ZC') and cah.happen_time >= '~VAR_startDay' AND cah.happen_time <= '~VAR_endDay' THEN ( COALESCE ( sm.total_amount, 0 ) - COALESCE ( sm.disc_amount, 0 ) - COALESCE ( sm.paid_amount, 0 )) ELSE 0 END) )::numeric,2) + SUM (CASE WHEN sub_type = 'QK' AND cah.sheet_type IN ( 'X', 'YS', 'DH', 'T', 'ZC' ) AND cah.happen_time >= '~VAR_startDay' AND cah.happen_time <=  '~VAR_endDay' THEN( COALESCE ( sf.total_amount, 0 ) - COALESCE ( sf.disc_amount, 0 ) - COALESCE ( sf.paid_amount, 0 ) ) ELSE 0 END )   paid_amount,
        balance
    from arrears_balance b
    left join
    (
          select sheet_id,supcust_id,seller_id,senders_id,money_inout_flag,total_amount*money_inout_flag total_amount,disc_amount*money_inout_flag disc_amount,
                        paid_amount*money_inout_flag paid_amount,now_pay_amount*money_inout_flag now_pay_amount,now_disc_amount*money_inout_flag now_disc_amount 
       from sheet_sale_main where company_id =~COMPANY_ID  and happen_time >= '~VAR_startDay' ~VAR_seller_id  ~VAR_senders_id 

        union

           select sheet_id,getter_id seller_id,supcust_id,money_inout_flag,total_amount*money_inout_flag total_amount,disc_amount*money_inout_flag disc_amount,
                        paid_amount*money_inout_flag paid_amount,now_pay_amount*money_inout_flag now_pay_amount,now_disc_amount*money_inout_flag now_disc_amount 
        from sheet_prepay where company_id =~COMPANY_ID  and happen_time >= '~VAR_startDay' ~VAR_getter_id 

        union

        SELECT sheet_id,supcust_id,getter_id seller_id,money_inout_flag,total_amount * money_inout_flag total_amount,disc_amount * money_inout_flag disc_amount,
                          paid_amount * money_inout_flag paid_amount,now_pay_amount * money_inout_flag now_pay_amount,now_disc_amount * money_inout_flag now_disc_amount 
        FROM sheet_fee_out_main WHERE company_id = ~COMPANY_ID  AND happen_time >= '~VAR_startDay'  ~VAR_getter_id

  
    )

    LEFT JOIN 
    (
       select * from client_account_history where company_id =~COMPANY_ID and happen_time >= '~VAR_startDay'  
    ) cah ON b.supcust_id = cah.supcust_id 
    LEFT JOIN 
    (
       select sheet_id,supcust_id,seller_id,senders_id,money_inout_flag,total_amount*money_inout_flag total_amount,disc_amount*money_inout_flag disc_amount,
                        paid_amount*money_inout_flag paid_amount,now_pay_amount*money_inout_flag now_pay_amount,now_disc_amount*money_inout_flag now_disc_amount 
       from sheet_sale_main where company_id =~COMPANY_ID  and happen_time >= '~VAR_startDay' ~VAR_seller_id  ~VAR_senders_id 
    ) sm ON b.supcust_id = sm.supcust_id AND cah.sheet_id = sm.sheet_id  
    LEFT JOIN 
    (
       select sheet_id,getter_id seller_id,supcust_id,money_inout_flag,total_amount*money_inout_flag total_amount,disc_amount*money_inout_flag disc_amount,
                        paid_amount*money_inout_flag paid_amount,now_pay_amount*money_inout_flag now_pay_amount,now_disc_amount*money_inout_flag now_disc_amount 
        from sheet_prepay where company_id =~COMPANY_ID  and happen_time >= '~VAR_startDay' ~VAR_getter_id 
    )  sp ON b.supcust_id = sp.supcust_id AND cah.sheet_id = sp.sheet_id 
    LEFT JOIN 
    (
        SELECT sheet_id,supcust_id,getter_id seller_id,money_inout_flag,total_amount * money_inout_flag total_amount,disc_amount * money_inout_flag disc_amount,
                          paid_amount * money_inout_flag paid_amount,now_pay_amount * money_inout_flag now_pay_amount,now_disc_amount * money_inout_flag now_disc_amount 
        FROM sheet_fee_out_main WHERE company_id = ~COMPANY_ID  AND happen_time >= '~VAR_startDay'  ~VAR_getter_id
    ) sf ON b.supcust_id = sf.supcust_id AND cah.sheet_id = sf.sheet_id	
    where b.company_id =~COMPANY_ID and cah.red_flag is null  ~VAR_show_no_arrears_condi

    group by b.supcust_id,b.balance
    order by paid_amount desc
) t 
LEFT JOIN info_supcust sc ON t.supcust_id = sc.supcust_id AND sc.company_id =~COMPANY_ID ~VAR_seller_id
where  sc.supcust_flag = 'C' 
~VAR_paid_amount
	 ",*/
                     QueryGroupBySQL = "",
                     QueryOrderSQL=""
                  }
                } 
            };             
        }


        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;
            SQLVariables["arrears_age_condi"] = "";
            SQLVariables["day_id_condi"] = "";
            if (DataItems["arrearsAge"].Value!="")
            {
                double arrearsAge = Double.Parse(DataItems["arrearsAge"].Value);
                SQLVariables["arrears_age_condi"] = @$"and ( happen_time <= '{DateTime.Today.AddDays(-arrearsAge).ToString("yyyy-MM-dd")} 23:59')";
            }


            if (DataItems["senders_id"].Value != "")
            {
                SQLVariables["senders_id"] = "and ( senders_id  ILIKE'%" +  DataItems["senders_id"].Value + "%')";
            }
            else
            {
                SQLVariables["senders_id"] = "";
            }


            if (DataItems["seller_id"].Value != ""){
                string sellerID = DataItems["seller_id"].Value;
                SQLVariables["seller_id"] = "and  seller_id =" + sellerID;
                SQLVariables["getter_id"] = "and  getter_id =" + sellerID;
                SQLVariables["tail_condi_seller_id"] = $" AND (sm.seller_id ={sellerID}  ) ";
                
            }
            else
            {
                SQLVariables["seller_id"] = " ";
                SQLVariables["getter_id"] = " ";
                SQLVariables["tail_condi_seller_id"] = " ";
                
            }
            SQLVariables["not_cleared_condi"] = "";
            SQLVariables["period_not_cleared_condi"] = "";
            //SQLVariables["show_no_arrears_condi"] = "";
            string arrear_status = DataItems["arrear_status"].Value;

            if (arrear_status=="not_cleared") 
            {
                SQLVariables["not_cleared_condi"] = " and abs(b.balance)>=0.01 "; 
            }
            else if (arrear_status == "period_not_cleared")
            {
                SQLVariables["period_not_cleared_condi"] = " having abs( round ( ( SUM ( COALESCE ( sm.total_amount, 0 ) - COALESCE ( sm.disc_amount, 0 ) - COALESCE ( sm.paid_amount, 0 )) )::numeric,2))>=0.01 ";
            }

            if (DataItems["showNoProcess"].Value.ToLower() == "true")
            {
                SQLVariables["showNoProcess"] = "  ";
            }
            else
            {
                SQLVariables["showNoProcess"] = " and (COALESCE(qk_add,0) !=0 or COALESCE(disc_amount,0) !=0 or COALESCE(qk_reduce,0) !=0 or COALESCE(left_amount,0)!=0) ";
            }

            // 对账状态产生的条件勾选
            string arrears_order_status = DataItems["arrears_order_status"].Value;
            SQLVariables["arrears_order_condi"] = " AND (sm.total_amount - sm.paid_amount - sm.disc_amount > 0.01 or sm.total_amount - sm.paid_amount - sm.disc_amount < -0.01)";
            
            if (arrears_order_status == "none")
            {
                // 待对帐
                SQLVariables["arrears_order_condi"] += " AND sm.arrears_order_sheet_id is null";
            }
            else if (arrears_order_status == "unapproved")
            {
                // 对账中
                SQLVariables["arrears_order_condi"] += " AND sm.arrears_order_sheet_id is not null AND aom.approve_time is null and red_flag is null";
            } 
            else if (arrears_order_status == "approved")
            {
                SQLVariables["arrears_order_condi"] += " AND sm.arrears_order_sheet_id is not null AND aom.approve_time is not null and red_flag is null";
            }
            else
            {
                SQLVariables["arrears_order_condi"] = "";
            }

            if (DataItems["day_id"].Value != "") {
                SQLVariables["day_id_condi"] = " and day_id='"+ DataItems["day_id"].Value + "'";

            }
        }
    }



    [Route("api/[controller]/[action]")]
    public class ClientArrearsController : QueryController
    { 
        public ClientArrearsController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ClientArrearsModel model = new ClientArrearsModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            ClientArrearsModel model = new ClientArrearsModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            ClientArrearsModel model = new ClientArrearsModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
