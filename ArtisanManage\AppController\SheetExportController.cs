﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Pages.Sheets;
using ArtisanManage.Services;
using ArtisanManage.Services.SheetService;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;
using NPOI.HSSF.Record;
using Newtonsoft.Json.Linq;
using System.Linq;
using System.Net.Http;
using System.Net;
using Microsoft.CodeAnalysis.Operations;
using NPOI.SS.Formula.Functions;
using NPOI.POIFS.Crypt.Dsig;
using System.ComponentModel.Design;
using Org.BouncyCastle.Asn1.X9;
using System.Threading.Tasks.Dataflow;
using Microsoft.VisualStudio.TextTemplating;
using Microsoft.AspNetCore.Http;
using NPOI.XSSF.UserModel;
using System.IO;
using System.Text;
using static ArtisanManage.Models.PageQueryModel;
using SixLabors.ImageSharp.Memory;
using System.Xml;
using NPOI.SS.UserModel;
using System.Text.RegularExpressions;
using NPOI.XSSF.Streaming.Values;
using NPOI.SS.Util;

namespace ArtisanManage.AppController
{
    [Route("AppApi/[controller]/[action]")]
    public class SheetExportController : QueryController
    {
        [HttpPost]
        public async Task<IActionResult> ExportExcel([FromBody] dynamic data)
        {
            // 提取传递的数据
            string supName = data.supName;
            string sellerName = data.sellerName;
            string sendersName = data.sendersName;
            string branchName = data.branchName;
            string remark = data.remark;
            string happenTime = data.happenTime;
            string sheetNo = data.sheetNo;
            string formData = data.formData;

            string supAddr = data.supAddr;
            string mobile = data.mobile;
            string receiveAddr = data.receiveAddr;
            string sendTime = data.sendTime;

            // 表头设置
            List<string[]> headers = new List<string[]>
    {
        new[] { "单号", sheetNo },
        new[] { "客户", supName },
        new[] { "销售员", sellerName },
        new[] { "送货员", sendersName },
        new[] { "仓库", branchName },
        new[] { "备注", remark },
        new[] { "交易时间", happenTime },
        new[] { "", "" },
        new[] { "客户地址", supAddr },
        new[] { "客户电话", mobile },
        new[] { "收货地址", receiveAddr },
        new[] { "送货时间", sendTime }
    };

            string fileName = "saleSheet.xlsx";
            int rowCount = 0;
            int colCount = 0;

            // 创建 Excel 工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet worksheet = (XSSFSheet)workbook.CreateSheet("Sheet1");

            // 设置表头字体样式
            IFont headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            ICellStyle headerItemStyle = workbook.CreateCellStyle();
            headerItemStyle.SetFont(headerFont);

            // 写入表头
            var headerRow = worksheet.CreateRow(rowCount);
            foreach (var h in headers)
            {
                ICell boldCell = headerRow.CreateCell(colCount);
                boldCell.SetCellValue(h[0]);
                boldCell.CellStyle = headerItemStyle;
                headerRow.CreateCell(colCount + 1).SetCellValue(h[1]);
                colCount += 2;
                if (colCount == 8)
                {
                    colCount = 0;
                    rowCount++;
                    headerRow = worksheet.CreateRow(rowCount);
                }
            }

            // 解析 formData
            JArray formDataArray = JArray.Parse(formData);

            // 创建 Excel 内容的表头行
            var contentHeaderRow = worksheet.CreateRow(rowCount + 1);
            int columnIndex = 0;

            // 设置表头单元格样式
            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(headerFont);
            headerStyle.BorderTop = BorderStyle.Thin;
            headerStyle.TopBorderColor = IndexedColors.Black.Index;
            headerStyle.BorderBottom = BorderStyle.Thin;
            headerStyle.BottomBorderColor = IndexedColors.Black.Index;
            headerStyle.BorderLeft = BorderStyle.Thin;
            headerStyle.LeftBorderColor = IndexedColors.Black.Index;
            headerStyle.BorderRight = BorderStyle.Thin;
            headerStyle.RightBorderColor = IndexedColors.Black.Index;

            // 设置内容单元格样式
            ICellStyle cellStyle = workbook.CreateCellStyle();
            cellStyle.BorderTop = BorderStyle.Thin;
            cellStyle.TopBorderColor = IndexedColors.Black.Index;
            cellStyle.BorderBottom = BorderStyle.Thin;
            cellStyle.BottomBorderColor = IndexedColors.Black.Index;
            cellStyle.BorderLeft = BorderStyle.Thin;
            cellStyle.LeftBorderColor = IndexedColors.Black.Index;
            cellStyle.BorderRight = BorderStyle.Thin;
            cellStyle.RightBorderColor = IndexedColors.Black.Index;

            // 写入内容表头
            formDataArray = new JArray(formDataArray.Where(item => item.HasValues));
            foreach (JProperty property in formDataArray.First.Children<JProperty>())
            {
                worksheet.SetColumnWidth(columnIndex, 20 * 256); // 设置列宽
                ICell headerCell = contentHeaderRow.CreateCell(columnIndex);
                headerCell.SetCellValue(property.Name);
                headerCell.CellStyle = headerStyle;
                columnIndex++;
            }

            // 处理表格中的数据
            int rowIndex = rowCount + 2;
            foreach (JObject item in formDataArray)
            {
                var dataRow = worksheet.CreateRow(rowIndex);
                columnIndex = 0;
                foreach (JProperty property in item.Properties())
                {
                    ICell cell = dataRow.CreateCell(columnIndex);
                    string cellValue = property.Value.ToString();

                    // 处理特殊字段：判断并格式化特殊字段
                    cellValue = ProcessSpecialField(property.Name, cellValue);

                    // 设置单元格值
                    cell.SetCellValue(cellValue);
                    cell.CellStyle = cellStyle;
                    columnIndex++;
                }
                rowIndex++;
            }

            // 将工作簿写入内存流并返回结果
            byte[] fileData;
            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                fileData = ms.ToArray();
                ms.Close();
            }

            return File(fileData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "export.xlsx");
        }

        // 处理特殊字段的方法：将数量字段和价格字段等合并成 "{字段名}合计：{数值}"
        public string ProcessSpecialField(string key, string value)
        {
            // 判断是否包含 "xj:" 或 "qty:" 的字段
            if (value.Contains("xj:"))
            {
                // 提取数字部分 (例如 "xj: 5400" -> "5400")
                string numericValue = value.Split(':')[1].Trim();
                // 格式化输出
                return $"{key}合计：{numericValue}";
            }
            else if (value.Contains("qty:"))
            {
                // 提取并返回数量描述 (例如 "qty: 30大12小" -> "30大12小")
                string numericValue = value.Split(':')[1].Trim();
                return $"{key}合计：{numericValue}";
            }
            return value; // 对于其他情况直接返回原始值
        }



        public async Task<IActionResult> ExportInventChangeExcel([FromBody] dynamic data)
        {
            // Console.WriteLine(data.ToString());


            string sellerName = data.sellerName;
            string branchName = data.branchName;
            string remark = data.remark;
            string happenTime = data.happenTime;
            string sheetNo = data.sheetNo;
            string defaultUnit=data.defaultUnit;
            string formData = data.formData;


            List<string[]> headers = [["单号", sheetNo],["销售员",sellerName],
                ["仓库", branchName],["备注",remark],["交易时间",happenTime],
                ["默认单位",defaultUnit]
            ];

            string fileName = "saleSheet.xlsx";

            int rowCount = 0;
            int colCount = 0;
            // 创建一个新的工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();

            // 添加一个工作表
            XSSFSheet worksheet = (XSSFSheet)workbook.CreateSheet("Sheet1");

            // 创建一个字体对象，设置为黑体
            IFont headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            // 创建单元格样式对象，并设置字体
            ICellStyle headerItemStyle = workbook.CreateCellStyle();
            headerItemStyle.SetFont(headerFont);
            // 写入表头
            var headerRow = worksheet.CreateRow(rowCount);
            foreach (var h in headers)
            {
                ICell boldCell = headerRow.CreateCell(colCount);
                boldCell.SetCellValue(h[0]);
                boldCell.CellStyle = headerItemStyle;
                headerRow.CreateCell(colCount + 1).SetCellValue(h[1]);
                colCount += 2;
                if (colCount == 8)
                {
                    colCount = 0;
                    rowCount++;
                    headerRow = worksheet.CreateRow(rowCount);
                }
            }

            JArray formDataArray = JArray.Parse(formData);

            // 创建 Excel 表头行
            // + 2 中间空一行
            var contentHeaderRow = worksheet.CreateRow(rowCount + 1);

            // 写入表头
            int columnIndex = 0;

            formDataArray = new JArray(formDataArray.Where(item => item.HasValues));

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(headerFont);
            headerStyle.BorderTop = BorderStyle.Thin;
            headerStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            headerStyle.BorderBottom = BorderStyle.Thin;
            headerStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            headerStyle.BorderLeft = BorderStyle.Thin;
            headerStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            headerStyle.BorderRight = BorderStyle.Thin;
            headerStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色

            ICellStyle cellStyle = workbook.CreateCellStyle();
            cellStyle.BorderTop = BorderStyle.Thin;
            cellStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            cellStyle.BorderBottom = BorderStyle.Thin;
            cellStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            cellStyle.BorderLeft = BorderStyle.Thin;
            cellStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            cellStyle.BorderRight = BorderStyle.Thin;
            cellStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色

            foreach (JProperty property in formDataArray.First.Children<JProperty>())
            {
                worksheet.SetColumnWidth(columnIndex, 20 * 256); // 第一个参数是列索引，第二个参数是宽度的单位，通常为 1/256 字符宽度
                ICell headerCell = contentHeaderRow.CreateCell(columnIndex);
                headerCell.SetCellValue(property.Name);
                headerCell.CellStyle = headerStyle;
                columnIndex++;
            }

            // 写入每个对象的属性值
            int rowIndex = rowCount + 2;
            foreach (JObject item in formDataArray)
            {
                var dataRow = worksheet.CreateRow(rowIndex);
                columnIndex = 0;
                foreach (JProperty property in item.Properties())
                {
                    ICell cell = dataRow.CreateCell(columnIndex);
                    cell.SetCellValue(property.Value.ToString());
                    cell.CellStyle = cellStyle;
                    columnIndex++;
                }
                rowIndex++;
            }

            // 将工作簿转换为内存流
            // 使用 NPOIMemoryStream 将 XSSFWorkbook 写入内存流
            byte[] fileData;
            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                //ms.Flush();
                //ms.Position=0;
                fileData = ms.ToArray();
                ms.Close();
            }
            // 返回 FileResult，提供内存流中的数据
            return File(fileData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "export.xlsx");
        }

        public async Task<IActionResult> ExportStockInOutExcel([FromBody] dynamic data)
        {
            // Console.WriteLine(data.ToString());
            //其他入库单和其他出库单的excel文件导出的代码，抄的盘点盈亏单的导出的代码

            string sellerName = data.sellerName;
            string branchName = data.branchName;
            string remark = data.remark;
            string happenTime = data.happenTime;
            string sheetNo = data.sheetNo;
            string defaultUnit = data.defaultUnit;
            string formData = data.formData;


            List<string[]> headers = [["单号", sheetNo],["销售员",sellerName],
                ["仓库", branchName],["备注",remark],["交易时间",happenTime],
                ["默认单位",defaultUnit]
            ];

            string fileName = "saleSheet.xlsx";

            int rowCount = 0;
            int colCount = 0;
            // 创建一个新的工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();

            // 添加一个工作表
            XSSFSheet worksheet = (XSSFSheet)workbook.CreateSheet("Sheet1");

            // 创建一个字体对象，设置为黑体
            IFont headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            // 创建单元格样式对象，并设置字体
            ICellStyle headerItemStyle = workbook.CreateCellStyle();
            headerItemStyle.SetFont(headerFont);
            // 写入表头
            var headerRow = worksheet.CreateRow(rowCount);
            foreach (var h in headers)
            {
                ICell boldCell = headerRow.CreateCell(colCount);
                boldCell.SetCellValue(h[0]);
                boldCell.CellStyle = headerItemStyle;
                headerRow.CreateCell(colCount + 1).SetCellValue(h[1]);
                colCount += 2;
                if (colCount == 8)
                {
                    colCount = 0;
                    rowCount++;
                    headerRow = worksheet.CreateRow(rowCount);
                }
            }

            JArray formDataArray = JArray.Parse(formData);

            // 创建 Excel 表头行
            // + 2 中间空一行
            var contentHeaderRow = worksheet.CreateRow(rowCount + 1);

            // 写入表头
            int columnIndex = 0;

            formDataArray = new JArray(formDataArray.Where(item => item.HasValues));

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(headerFont);
            headerStyle.BorderTop = BorderStyle.Thin;
            headerStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            headerStyle.BorderBottom = BorderStyle.Thin;
            headerStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            headerStyle.BorderLeft = BorderStyle.Thin;
            headerStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            headerStyle.BorderRight = BorderStyle.Thin;
            headerStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色

            ICellStyle cellStyle = workbook.CreateCellStyle();
            cellStyle.BorderTop = BorderStyle.Thin;
            cellStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            cellStyle.BorderBottom = BorderStyle.Thin;
            cellStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            cellStyle.BorderLeft = BorderStyle.Thin;
            cellStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            cellStyle.BorderRight = BorderStyle.Thin;
            cellStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色

            foreach (JProperty property in formDataArray.First.Children<JProperty>())
            {
                worksheet.SetColumnWidth(columnIndex, 20 * 256); // 第一个参数是列索引，第二个参数是宽度的单位，通常为 1/256 字符宽度
                ICell headerCell = contentHeaderRow.CreateCell(columnIndex);
                headerCell.SetCellValue(property.Name);
                headerCell.CellStyle = headerStyle;
                columnIndex++;
            }

            // 写入每个对象的属性值
            int rowIndex = rowCount + 2;
            foreach (JObject item in formDataArray)
            {
                var dataRow = worksheet.CreateRow(rowIndex);
                columnIndex = 0;
                foreach (JProperty property in item.Properties())
                {
                    ICell cell = dataRow.CreateCell(columnIndex);
                    cell.SetCellValue(property.Value.ToString());
                    cell.CellStyle = cellStyle;
                    columnIndex++;
                }
                rowIndex++;
            }

            // 将工作簿转换为内存流
            // 使用 NPOIMemoryStream 将 XSSFWorkbook 写入内存流
            byte[] fileData;
            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                //ms.Flush();
                //ms.Position=0;
                fileData = ms.ToArray();
                ms.Close();
            }
            // 返回 FileResult，提供内存流中的数据
            return File(fileData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "export.xlsx");
        }

        [HttpPost]
        public async Task<IActionResult> BuySheetExportExcel([FromBody] dynamic data)
        {
            // Console.WriteLine(data.ToString());
            //采购单和采购退货单的excel文件导出的代码，抄的销售单、退货单的导出的代码
            //送货员改成了收货员
            string supName = data.supName;//供应商
            string sellerName = data.sellerName;//经手人
            string receiversName = data.receiversName;//收货员
            string branchName = data.branchName;//仓库
            string remark = data.remark;//备注信息
            string happenTime = data.happenTime;//交易日期
            string sheetNo = data.sheetNo;//单号
            string formData = data.formData;//表格信息

            string supAddr = data.supAddr;
            string mobile = data.mobile;
            string receiveAddr = data.receiveAddr;
            string sendTime = data.sendTime;

            List<string[]> headers = [["单号", sheetNo],
                ["供应商", supName],["经手人",sellerName],["收货员",receiversName],
                ["仓库", branchName],["备注",remark],["交易时间",happenTime],["",""],
                ["客户地址",supAddr],["客户电话", mobile],["收货地址", receiveAddr],["送货时间", sendTime]];

            // bool hasOtherInfo = bool.Parse(data.hasOtherInfo);

            string fileName = "buySheet.xlsx";

            int rowCount = 0;
            int colCount = 0;
            // 创建一个新的工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();

            // 添加一个工作表
            XSSFSheet worksheet = (XSSFSheet)workbook.CreateSheet("Sheet1");

            // 创建一个字体对象，设置为黑体
            IFont headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            // 创建单元格样式对象，并设置字体
            ICellStyle headerItemStyle = workbook.CreateCellStyle();
            headerItemStyle.SetFont(headerFont);
            // 写入表头
            var headerRow = worksheet.CreateRow(rowCount);
            foreach (var h in headers)
            {
                ICell boldCell = headerRow.CreateCell(colCount);
                boldCell.SetCellValue(h[0]);
                boldCell.CellStyle = headerItemStyle;
                headerRow.CreateCell(colCount + 1).SetCellValue(h[1]);
                colCount += 2;
                if (colCount == 8)
                {
                    colCount = 0;
                    rowCount++;
                    headerRow = worksheet.CreateRow(rowCount);
                }
            }


            /*            

                        headerRow.CreateCell(1).SetCellValue(sellerName);

                        headerRow.CreateCell(2).SetCellValue(sendersName);

                        headerRow.CreateCell(3).SetCellValue(branchName);

                        headerRow.CreateCell(4).SetCellValue(remark);

                        headerRow.CreateCell(5).SetCellValue(happenTime);

                        headerRow.CreateCell(6).SetCellValue(sheetNo);
            */

            JArray formDataArray = JArray.Parse(formData);

            // 创建 Excel 表头行
            // + 2 中间空一行
            var contentHeaderRow = worksheet.CreateRow(rowCount + 1);

            // 写入表头
            int columnIndex = 0;

            formDataArray = new JArray(formDataArray.Where(item => item.HasValues));

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(headerFont);
            headerStyle.BorderTop = BorderStyle.Thin;
            headerStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            headerStyle.BorderBottom = BorderStyle.Thin;
            headerStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            headerStyle.BorderLeft = BorderStyle.Thin;
            headerStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            headerStyle.BorderRight = BorderStyle.Thin;
            headerStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色

            ICellStyle cellStyle = workbook.CreateCellStyle();
            cellStyle.BorderTop = BorderStyle.Thin;
            cellStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            cellStyle.BorderBottom = BorderStyle.Thin;
            cellStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            cellStyle.BorderLeft = BorderStyle.Thin;
            cellStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            cellStyle.BorderRight = BorderStyle.Thin;
            cellStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色

            foreach (JProperty property in formDataArray.First.Children<JProperty>())
            {
                worksheet.SetColumnWidth(columnIndex, 20 * 256); // 第一个参数是列索引，第二个参数是宽度的单位，通常为 1/256 字符宽度
                ICell headerCell = contentHeaderRow.CreateCell(columnIndex);
                headerCell.SetCellValue(property.Name);
                headerCell.CellStyle = headerStyle;
                columnIndex++;
            }

            // 写入每个对象的属性值
            int rowIndex = rowCount + 2;
            foreach (JObject item in formDataArray)
            {
                var dataRow = worksheet.CreateRow(rowIndex);
                columnIndex = 0;
                foreach (JProperty property in item.Properties())
                {
                    ICell cell = dataRow.CreateCell(columnIndex);
                    cell.SetCellValue(property.Value.ToString());
                    cell.CellStyle = cellStyle;
                    columnIndex++;
                }
                rowIndex++;
            }

            // 将工作簿转换为内存流
            // 使用 NPOIMemoryStream 将 XSSFWorkbook 写入内存流
            byte[] fileData;
            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                //ms.Flush();
                //ms.Position=0;
                fileData = ms.ToArray();
                ms.Close();
            }
            // 返回 FileResult，提供内存流中的数据
            return File(fileData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "export.xlsx");
        }

        [HttpPost]
        public async Task<IActionResult> BuyOrderSheetExportExcel([FromBody] dynamic data)
        {
            // Console.WriteLine(data.ToString());
            //采购订单的excel文件导出的代码，抄的销售单、退货单的导出的代码
            //比采购单和采购退货单少：收货员
            string supName = data.supName;//供应商
            string sellerName = data.sellerName;//经手人
            //string receiversName = data.receiversName;//收货员
            string branchName = data.branchName;//仓库
            string remark = data.remark;//备注信息
            string happenTime = data.happenTime;//交易日期
            string sheetNo = data.sheetNo;//单号
            string formData = data.formData;//表格信息

            string supAddr = data.supAddr;
            string mobile = data.mobile;
            string receiveAddr = data.receiveAddr;
            string sendTime = data.sendTime;

            List<string[]> headers = [["单号", sheetNo],
                ["供应商", supName],["经手人",sellerName],
                ["仓库", branchName],["备注",remark],["交易时间",happenTime],["",""],
                ["客户地址",supAddr],["客户电话", mobile],["收货地址", receiveAddr],["送货时间", sendTime]];

            // bool hasOtherInfo = bool.Parse(data.hasOtherInfo);

            string fileName = "buySheet.xlsx";

            int rowCount = 0;
            int colCount = 0;
            // 创建一个新的工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();

            // 添加一个工作表
            XSSFSheet worksheet = (XSSFSheet)workbook.CreateSheet("Sheet1");

            // 创建一个字体对象，设置为黑体
            IFont headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            // 创建单元格样式对象，并设置字体
            ICellStyle headerItemStyle = workbook.CreateCellStyle();
            headerItemStyle.SetFont(headerFont);
            // 写入表头
            var headerRow = worksheet.CreateRow(rowCount);
            foreach (var h in headers)
            {
                ICell boldCell = headerRow.CreateCell(colCount);
                boldCell.SetCellValue(h[0]);
                boldCell.CellStyle = headerItemStyle;
                headerRow.CreateCell(colCount + 1).SetCellValue(h[1]);
                colCount += 2;
                if (colCount == 8)
                {
                    colCount = 0;
                    rowCount++;
                    headerRow = worksheet.CreateRow(rowCount);
                }
            }


            /*            

                        headerRow.CreateCell(1).SetCellValue(sellerName);

                        headerRow.CreateCell(2).SetCellValue(sendersName);

                        headerRow.CreateCell(3).SetCellValue(branchName);

                        headerRow.CreateCell(4).SetCellValue(remark);

                        headerRow.CreateCell(5).SetCellValue(happenTime);

                        headerRow.CreateCell(6).SetCellValue(sheetNo);
            */

            JArray formDataArray = JArray.Parse(formData);

            // 创建 Excel 表头行
            // + 2 中间空一行
            var contentHeaderRow = worksheet.CreateRow(rowCount + 1);

            // 写入表头
            int columnIndex = 0;

            formDataArray = new JArray(formDataArray.Where(item => item.HasValues));

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(headerFont);
            headerStyle.BorderTop = BorderStyle.Thin;
            headerStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            headerStyle.BorderBottom = BorderStyle.Thin;
            headerStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            headerStyle.BorderLeft = BorderStyle.Thin;
            headerStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            headerStyle.BorderRight = BorderStyle.Thin;
            headerStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色

            ICellStyle cellStyle = workbook.CreateCellStyle();
            cellStyle.BorderTop = BorderStyle.Thin;
            cellStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            cellStyle.BorderBottom = BorderStyle.Thin;
            cellStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            cellStyle.BorderLeft = BorderStyle.Thin;
            cellStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            cellStyle.BorderRight = BorderStyle.Thin;
            cellStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色

            foreach (JProperty property in formDataArray.First.Children<JProperty>())
            {
                worksheet.SetColumnWidth(columnIndex, 20 * 256); // 第一个参数是列索引，第二个参数是宽度的单位，通常为 1/256 字符宽度
                ICell headerCell = contentHeaderRow.CreateCell(columnIndex);
                headerCell.SetCellValue(property.Name);
                headerCell.CellStyle = headerStyle;
                columnIndex++;
            }

            // 写入每个对象的属性值
            int rowIndex = rowCount + 2;
            foreach (JObject item in formDataArray)
            {
                var dataRow = worksheet.CreateRow(rowIndex);
                columnIndex = 0;
                foreach (JProperty property in item.Properties())
                {
                    ICell cell = dataRow.CreateCell(columnIndex);
                    cell.SetCellValue(property.Value.ToString());
                    cell.CellStyle = cellStyle;
                    columnIndex++;
                }
                rowIndex++;
            }

            // 将工作簿转换为内存流
            // 使用 NPOIMemoryStream 将 XSSFWorkbook 写入内存流
            byte[] fileData;
            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                //ms.Flush();
                //ms.Position=0;
                fileData = ms.ToArray();
                ms.Close();
            }
            // 返回 FileResult，提供内存流中的数据
            return File(fileData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "export.xlsx");
        }


        [HttpPost]
        public async Task<IActionResult> GetArrearsSheetExportExcel([FromBody] dynamic data)
        {
            // Console.WriteLine(data.ToString());

            // 收款单导出
            string supName = data.supName;
            string getterName = data.getterName;
            string makeBrief = data.makeBrief;
            string happenTime = data.happenTime;
            string sheetNo = data.sheetNo;
            string formData = data.formData;
            string sheetType = data.sheetType;
            string sheetNoType = sheetType == "SK" ? "收款单号" : "对账单号";

            List<string[]> headers = [[sheetNoType, sheetNo],
                ["客户", supName],
                ["收款人", getterName],
                ["交易时间", happenTime],
                ["备注", makeBrief]];

            // bool hasOtherInfo = bool.Parse(data.hasOtherInfo);

            string fileName = "getArrearsSheet.xlsx";

            int rowCount = 0;
            int colCount = 0;
            // 创建一个新的工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();

            // 添加一个工作表
            XSSFSheet worksheet = (XSSFSheet)workbook.CreateSheet("Sheet1");

            // 创建一个字体对象，设置为黑体
            IFont headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            // 创建单元格样式对象，并设置字体
            ICellStyle headerItemStyle = workbook.CreateCellStyle();
            headerItemStyle.SetFont(headerFont);
            // 写入表头
            var headerRow = worksheet.CreateRow(rowCount);
            foreach (var h in headers)
            {
                ICell boldCell = headerRow.CreateCell(colCount);
                boldCell.SetCellValue(h[0]);
                boldCell.CellStyle = headerItemStyle;
                headerRow.CreateCell(colCount + 1).SetCellValue(h[1]);
                colCount += 2;
                if (colCount == 8)
                {
                    colCount = 0;
                    rowCount++;
                    headerRow = worksheet.CreateRow(rowCount);
                }
            }


            /*            

                        headerRow.CreateCell(1).SetCellValue(sellerName);

                        headerRow.CreateCell(2).SetCellValue(sendersName);

                        headerRow.CreateCell(3).SetCellValue(branchName);

                        headerRow.CreateCell(4).SetCellValue(remark);

                        headerRow.CreateCell(5).SetCellValue(happenTime);

                        headerRow.CreateCell(6).SetCellValue(sheetNo);
            */

            JArray formDataArray = JArray.Parse(formData);

            // 创建 Excel 表头行
            // + 2 中间空一行
            var contentHeaderRow = worksheet.CreateRow(rowCount + 1);

            // 写入表头
            int columnIndex = 0;

            formDataArray = new JArray(formDataArray.Where(item => item.HasValues));

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(headerFont);
            headerStyle.BorderTop = BorderStyle.Thin;
            headerStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            headerStyle.BorderBottom = BorderStyle.Thin;
            headerStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            headerStyle.BorderLeft = BorderStyle.Thin;
            headerStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            headerStyle.BorderRight = BorderStyle.Thin;
            headerStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色

            ICellStyle cellStyle = workbook.CreateCellStyle();
            cellStyle.BorderTop = BorderStyle.Thin;
            cellStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            cellStyle.BorderBottom = BorderStyle.Thin;
            cellStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            cellStyle.BorderLeft = BorderStyle.Thin;
            cellStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            cellStyle.BorderRight = BorderStyle.Thin;
            cellStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色

            foreach (JProperty property in formDataArray.First.Children<JProperty>())
            {
                worksheet.SetColumnWidth(columnIndex, 20 * 256); // 第一个参数是列索引，第二个参数是宽度的单位，通常为 1/256 字符宽度
                ICell headerCell = contentHeaderRow.CreateCell(columnIndex);
                headerCell.SetCellValue(property.Name);
                headerCell.CellStyle = headerStyle;
                columnIndex++;
            }

            // 用来匹配明细行日期
            string pattern = @"^\d{1,2}/\d{1,2}/\d{4}$";
            Regex regex = new Regex(pattern);

            // 写入每个对象的属性值
            int rowIndex = rowCount + 2;
            foreach (JObject item in formDataArray)
            {
                var dataRow = worksheet.CreateRow(rowIndex);
                columnIndex = 0;
                foreach (JProperty property in item.Properties())
                {
                    ICell cell = dataRow.CreateCell(columnIndex);

                    if (regex.IsMatch(property.Value.ToString()))
                    {
                        DateTime date = DateTime.ParseExact(property.Value.ToString(), "M/d/yyyy", null);
                        cell.SetCellValue(date.ToString("yyyy-MM-dd"));
                    
                    } else
                    {
                        cell.SetCellValue(property.Value.ToString());
                    }
                    
                    cell.CellStyle = cellStyle;
                    columnIndex++;
                }
                rowIndex++;
            }

            // 将工作簿转换为内存流
            // 使用 NPOIMemoryStream 将 XSSFWorkbook 写入内存流
            byte[] fileData;
            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                //ms.Flush();
                //ms.Position=0;
                fileData = ms.ToArray();
                ms.Close();
            }
            // 返回 FileResult，提供内存流中的数据
            return File(fileData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "export.xlsx");
        }
        [HttpPost]
        public async Task<IActionResult> ExportPriceAdjustExcel([FromBody] dynamic data)
        {
            // Console.WriteLine(data.ToString());

            //调价单导出
            string plansName = data.plansName;
            string sellerName = data.sellerName;
            string makeBrief = data.remark;
            string happenTime = data.happenTime;
            string sheetNo = data.sheetNo;
            string formData = data.formData;
            string parentHeader = data.parentHeader;



            List<string[]> headers = [["调价单号", sheetNo],
                ["价格方案", plansName],
                ["业务员", sellerName],
                ["交易时间", happenTime],
                ["备注", makeBrief],
                ["默认为价格（调价后）", ""]];

            // bool hasOtherInfo = bool.Parse(data.hasOtherInfo);

            string fileName = "getAdjustSheet.xlsx";

            int rowCount = 0;
            int colCount = 0;
            // 创建一个新的工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();

            // 添加一个工作表
            XSSFSheet worksheet = (XSSFSheet)workbook.CreateSheet("Sheet1");

            // 创建一个字体对象，设置为黑体
            IFont headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            // 创建单元格样式对象，并设置字体
            ICellStyle headerItemStyle = workbook.CreateCellStyle();
            headerItemStyle.SetFont(headerFont);
            // 写入表头
            var headerRow = worksheet.CreateRow(rowCount);
            foreach (var h in headers)
            {
                ICell boldCell = headerRow.CreateCell(colCount);
                boldCell.SetCellValue(h[0]);
                boldCell.CellStyle = headerItemStyle;
                headerRow.CreateCell(colCount + 1).SetCellValue(h[1]);
                colCount += 2;
                if (colCount == 8)
                {
                    colCount = 0;
                    rowCount++;
                    headerRow = worksheet.CreateRow(rowCount);
                }
            }


            /*            

                        headerRow.CreateCell(1).SetCellValue(sellerName);

                        headerRow.CreateCell(2).SetCellValue(sendersName);

                        headerRow.CreateCell(3).SetCellValue(branchName);

                        headerRow.CreateCell(4).SetCellValue(remark);

                        headerRow.CreateCell(5).SetCellValue(happenTime);

                        headerRow.CreateCell(6).SetCellValue(sheetNo);
            */

            JArray formDataArray = JArray.Parse(formData);

            // 创建 Excel 表头行
            // + 2 中间空一行
            
            // 写入表头
            int columnIndex = 0;

            formDataArray = new JArray(formDataArray.Where(item => item.HasValues));

            ICellStyle headerStyle = workbook.CreateCellStyle();
            headerStyle.SetFont(headerFont);
            headerStyle.BorderTop = BorderStyle.Thin;
            headerStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            headerStyle.BorderBottom = BorderStyle.Thin;
            headerStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            headerStyle.BorderLeft = BorderStyle.Thin;
            headerStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            headerStyle.BorderRight = BorderStyle.Thin;
            headerStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色

            ICellStyle cellStyle = workbook.CreateCellStyle();
            cellStyle.BorderTop = BorderStyle.Thin;
            cellStyle.TopBorderColor = IndexedColors.Black.Index; // 设置顶部边框线的颜色
            cellStyle.BorderBottom = BorderStyle.Thin;
            cellStyle.BottomBorderColor = IndexedColors.Black.Index; // 设置底部边框线的颜色
            cellStyle.BorderLeft = BorderStyle.Thin;
            cellStyle.LeftBorderColor = IndexedColors.Black.Index; // 设置左侧边框线的颜色
            cellStyle.BorderRight = BorderStyle.Thin;
            cellStyle.RightBorderColor = IndexedColors.Black.Index; // 设置右侧边框线的颜色


            rowCount++;
            bool hasParentHeader = false;
            dynamic contentHeaderRow = null;
            dynamic parentHeaderRow = null;
            if (!string.IsNullOrEmpty(parentHeader))
            {
                hasParentHeader = true;
                parentHeaderRow = worksheet.CreateRow(rowCount);
                rowCount++;
            }
            contentHeaderRow = worksheet.CreateRow(rowCount);
            
            // 先写入子标题行
            foreach (JProperty property in formDataArray.First.Children<JProperty>())
            {
                worksheet.SetColumnWidth(columnIndex, 20 * 256); // 第一个参数是列索引，第二个参数是宽度的单位，通常为 1/256 字符宽度
                ICell headerCell = contentHeaderRow.CreateCell(columnIndex);
                string headerText = property.Name;
                if (hasParentHeader)
                {
                    string[] parts = headerText.Split('_');
                    headerText = parts[0];
                }
                headerCell.SetCellValue(headerText);
                headerCell.CellStyle = headerStyle;
                columnIndex++;
            }

            if (!string.IsNullOrEmpty(parentHeader))
            {
                // 写入父表头
                List<string> pList = JsonConvert.DeserializeObject<List<string>>(parentHeader);
                hasParentHeader = true;

                string oldPh = "";
                int mergedStartColumn = -1;  // 用于记录合并开始的列
                bool hasMerged = true;
                columnIndex = 0;
                for (int i = 0; i < pList.Count(); i++)
                {
                    string ph = pList[i];

                    worksheet.SetColumnWidth(columnIndex, 20 * 256); // 第一个参数是列索引，第二个参数是宽度的单位，通常为 1/256 字符宽度
                    ICell headerCell = parentHeaderRow.CreateCell(columnIndex);
                    headerCell.SetCellValue(ph);
                    headerCell.CellStyle = headerStyle;
                    columnIndex++;

                    if (string.IsNullOrEmpty(ph))
                    {
                        // 向下合并一行
                        string headerText = contentHeaderRow.GetCell(i).StringCellValue;
                        headerCell.SetCellValue(headerText);
                        worksheet.AddMergedRegion(new CellRangeAddress(rowCount -1, rowCount, i, i));
                        // mergedStartColumn = i;
                    } 
                    if (ph != oldPh)
                    {
                        oldPh = ph;
                        if (!hasMerged)
                        {
                            worksheet.AddMergedRegion(new CellRangeAddress(rowCount -1, rowCount - 1, mergedStartColumn, i -1));
                            if (string.IsNullOrEmpty(ph))
                            {
                                hasMerged = true;
                                // mergedStartColumn = i;
                            }else
                            {
                                hasMerged = false;
                                mergedStartColumn = i;
                            }
                           
                        } else
                        {
                            hasMerged = false;
                            mergedStartColumn = i;
                        }
                        
                    }
                    


                    if (!hasMerged && i == pList.Count() - 1)
                    {
                        worksheet.AddMergedRegion(new CellRangeAddress(rowCount - 1, rowCount - 1, mergedStartColumn, i));
                    }
                }


                columnIndex = 0;
            }

            // 用来匹配明细行日期
            string pattern = @"^\d{1,2}/\d{1,2}/\d{4}$";
            Regex regex = new Regex(pattern);

            // 写入每个对象的属性值
            int rowIndex = rowCount + 1;
            foreach (JObject item in formDataArray)
            {
                var dataRow = worksheet.CreateRow(rowIndex);
                columnIndex = 0;
                foreach (JProperty property in item.Properties())
                {
                    ICell cell = dataRow.CreateCell(columnIndex);

                    if (regex.IsMatch(property.Value.ToString()))
                    {
                        DateTime date = DateTime.ParseExact(property.Value.ToString(), "M/d/yyyy", null);
                        cell.SetCellValue(date.ToString("yyyy-MM-dd"));

                    }
                    else
                    {
                        cell.SetCellValue(property.Value.ToString());
                    }

                    cell.CellStyle = cellStyle;
                    columnIndex++;
                }
                rowIndex++;
            }

            // 将工作簿转换为内存流
            // 使用 NPOIMemoryStream 将 XSSFWorkbook 写入内存流
            byte[] fileData;
            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                //ms.Flush();
                //ms.Position=0;
                fileData = ms.ToArray();
                ms.Close();
            }
            // 返回 FileResult，提供内存流中的数据
            return File(fileData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "export.xlsx");
        }
        [HttpPost]
        public async Task<IActionResult> JqxExportExcel([FromForm] IFormCollection data)
        {
            Console.WriteLine(data.ToString());
            string contentXml = data["content"];
            string result = "OK";
            string msg = "";

            #region 返回格式漂亮的xls文件，jqx版本太低不支持返回xlsx文件
            // // 设置响应头
            // Response.Clear();
            // Response.ContentType = "application/vnd.ms-excel";
            // Response.Headers.Add("Content-Disposition", $"attachment; filename=SK.xls");
            // Response.Headers.Add("Cache-Control", "must-revalidate, post-check=0, pre-check=0, private");
            // Response.Headers.Add("Expires", "0");
            // Response.Headers.Add("Pragma", "public");
            // // 写入内存流
            // 
            // using (MemoryStream memoryStream = new MemoryStream())
            // {
            //     using (StreamWriter sw = new StreamWriter(memoryStream, Encoding.UTF8, leaveOpen: true))
            //     {
            //         await sw.WriteAsync(contentXml);
            //         await sw.FlushAsync();
            //     }
            // 
            //     // 将内存流的内容异步写入响应体
            //     memoryStream.Seek(0, SeekOrigin.Begin);
            //    await memoryStream.CopyToAsync(Response.Body);
            // 
            // 
            // }
            // return new EmptyResult();
            #endregion
            #region 返回一个没有样式的xlsx文件
            // 创建一个新的 SXSSFWorkbook 实例
            using (var workbook = new XSSFWorkbook())
            {
                // 添加一个工作表
                var sheet = workbook.CreateSheet("Sheet1");

                // 将 XML 内容加载到工作表中
                using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(contentXml)))
                {
                    var xmlDoc = new XmlDocument();
                    xmlDoc.Load(stream);

                    // 创建命名空间管理器
                    var nsManager = new XmlNamespaceManager(xmlDoc.NameTable);
                    nsManager.AddNamespace("ss", "urn:schemas-microsoft-com:office:spreadsheet");

                    // 使用带有命名空间的 XPath 获取所有行节点
                    var rows = xmlDoc.SelectNodes("//ss:Row", nsManager);
                    int rowIdx = 0;

                    foreach (XmlNode rowNode in rows)
                    {
                        var row = sheet.CreateRow(rowIdx++);
                        int colIdx = 0;

                        // 使用带有命名空间的 XPath 获取当前行中的所有单元格节点
                        var cells = rowNode.SelectNodes("ss:Cell", nsManager);

                        foreach (XmlNode cellNode in cells)
                        {
                            // 使用带有命名空间的 XPath 获取单元格中的数据
                            var dataNode = cellNode.SelectSingleNode("ss:Data", nsManager);
                            var value = dataNode.InnerText;

                            var cell = row.CreateCell(colIdx++);
                            cell.SetCellValue(value);
                        }
                    }
                }

                // 使用 NPOIMemoryStream 将 XSSFWorkbook 写入内存流
                byte[] fileData;
                using (var ms = new NPOIMemoryStream())
                {
                    workbook.Write(ms);
                    ms.Flush();
                    ms.Position = 0;
                    fileData = ms.ToArray();
                }

                // 返回 FileResult，提供内存流中的数据
                return File(fileData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "XS.xlsx");
            }
            #endregion

        }
    }
}
