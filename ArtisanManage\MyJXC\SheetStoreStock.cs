﻿using ArtisanManage.Models;
using ArtisanManage.Pages;
using ArtisanManage.Pages.WeChat.SheetPages;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using NuGet.Packaging.Signing;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Dynamic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowStoreStock : SheetRowBase
    {
        public SheetRowStoreStock()
        {

        }
        [SaveToDB][FromFld] public string item_id { get; set; } = "";
        [SaveToDB][FromFld] public string sheet_item_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string batch_level { get; set; } = "";
        [SaveToDB][FromFld] public string batch_id { get; set; } = "0";
        [FromFld(LOAD_PURPOSE.SHOW)] public string produce_date { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string batch_no { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_name { get; set; } = "";

        [SaveToDB] [FromFld] public decimal unit_factor { get; set; } = 1;
        [SaveToDB] [FromFld] public decimal sys_quantity { get; set; }
         public string branch_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string branch_name { get; set; } = "";
        [SaveToDB][FromFld] public string branch_position { get; set; } = "0";
        [FromFld(LOAD_PURPOSE.SHOW)] public string branch_position_name { get; set; } = "";
        [FromFld("dd.item_spec",LOAD_PURPOSE.SHOW)] public string item_spec { get; set; } = "";
        [FromFld("dd.item_no", LOAD_PURPOSE.SHOW)] public string item_no { get; set; } = "";
        public string inventory_qty
        { 
            get
            {
                string s = "";
                if (!string.IsNullOrEmpty(b_unit_qty) && b_unit_qty != "0") s += b_unit_qty + b_unit_no;
                if (!string.IsNullOrEmpty(m_unit_qty) && m_unit_qty != "0") s += m_unit_qty + m_unit_no;
                if (!string.IsNullOrEmpty(s_unit_qty) && s_unit_qty != "0") s += s_unit_qty + s_unit_no;
                return s;

            }
        }
        public string client_stock_qty
        {
            get
            {
                string s = "";
                if (!string.IsNullOrEmpty(b_unit_qty_storestock) && b_unit_qty_storestock != "0") s += b_unit_qty_storestock + b_unit_no;
                if (!string.IsNullOrEmpty(m_unit_qty_storestock) && m_unit_qty_storestock != "0") s += m_unit_qty_storestock + m_unit_no;
                if (!string.IsNullOrEmpty(s_unit_qty_storestock) && s_unit_qty_storestock != "0") s += s_unit_qty_storestock + s_unit_no;
                return s;

            }
        }
        public static string GetQtyUnit(decimal s_qty,string b_unit_no, decimal b_unit_factor,string m_unit_no, decimal m_unit_factor,string s_unit_no,ref decimal b_sum_quantity,ref decimal m_sum_quantity,ref decimal s_sum_quantity)
        {
            decimal leftQty = s_qty; 
            var absLeftQty = Math.Abs(leftQty);
            decimal flag = 1;
            if(absLeftQty!=0) flag = leftQty / absLeftQty;
            string sQty = "";
            if (b_unit_factor>0)
            {
               // row.unit_relation = "1*" + b_unit_factor;
               // row.unit_relation1 = $"1{row.b_unit_no}={row.b_unit_factor}{row.s_unit_no}";
                var qty = Math.Floor(absLeftQty / b_unit_factor);
                if (qty < 0.001m) qty = 0;
                qty = CPubVars.ToDecimal(CPubVars.FormatMoney(qty, 3));
                if (qty > 0)
                    sQty += (qty * flag).ToString() + b_unit_no;
                absLeftQty = absLeftQty % b_unit_factor;
                b_sum_quantity += qty * flag;
            }

            if (m_unit_factor>0)
            {
                var qty = Math.Floor(absLeftQty /m_unit_factor);
                if (qty < 0.001m) qty = 0;
                qty = CPubVars.ToDecimal(CPubVars.FormatMoney(qty, 3));
                if (qty > 0)
                    sQty += (qty * flag).ToString() + m_unit_no;
                absLeftQty = absLeftQty % CPubVars.ToDecimal(m_unit_factor);
                m_sum_quantity += qty * flag;
            }
            if (absLeftQty < 0.001m) absLeftQty = 0;

            absLeftQty = CPubVars.ToDecimal(CPubVars.FormatMoney(absLeftQty, 3));
            if (absLeftQty > 0)
            {
                s_sum_quantity += absLeftQty;
                sQty += (absLeftQty * flag).ToString() + s_unit_no;
            }
            return sQty;
        }
        public string add_qty
        {
            get
            {
                decimal qty = real_d_quantity - sys_quantity;
                decimal bf = 0, mf = 0;
                if (b_unit_factor.IsValid()) bf = CPubVars.ToDecimal(b_unit_factor);
                if (m_unit_factor.IsValid()) mf = CPubVars.ToDecimal(m_unit_factor);

                decimal b_sum_quantity = 0, m_sum_quantity = 0, s_sum_quantity=0;

                string s=GetQtyUnit(qty, b_unit_no, bf, m_unit_no, mf, s_unit_no, ref b_sum_quantity, ref m_sum_quantity, ref s_sum_quantity);

                return s;

            }
        }
        public string add_cost_amount
        {
            get
            {
                decimal qty = real_d_quantity - sys_quantity;
           
                decimal n = 0;
                if (cost_price_type == "2")
                {
                    if (cost_price_avg.IsValid())
                    {
                        n = CPubVars.ToDecimal(cost_price_avg) * unit_factor;
                    }
                   
                }
                else if (cost_price_type == "3")
                {
                    if (buy_price.IsValid())
                    {
                        n = CPubVars.ToDecimal(buy_price) * unit_factor;
                    }
                        }
                n = n * qty;
                n = Math.Round(n, 2);  
                return n.ToString(); 
            }
        }
        [SaveToDB] [FromFld] public string real_quantity { get; set; }
        internal decimal real_d_quantity { 
            get
            { 
                Decimal.TryParse(real_quantity, out decimal r);
                return r;
            }
            set {
                real_quantity=value.ToString();
            }
        }
        [SaveToDB][FromFld] public string client_stock_quantity { get; set; }
        [SaveToDB][FromFld] public string buy_quantity { get; set; }
        internal decimal buy_d_quantity
        {
            get
            {
                Decimal.TryParse(buy_quantity, out decimal r);
                return r;
            }
            set
            {
                buy_quantity = value.ToString();
            }
        }
        internal decimal client_stock_d_quantity
        {
            get
            {
                Decimal.TryParse(client_stock_quantity, out decimal r);
                return r;
            }
            set
            {
                client_stock_quantity = value.ToString();
            }
        }
        [SaveToDB] [FromFld] public string cost_price_avg { get; set; }
        [SaveToDB] [FromFld] public string cost_price_prop { get; set; }
        [SaveToDB] [FromFld] public string wholesale_price { get; set; }
        [SaveToDB] [FromFld] public string buy_price { get; set; }      
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_barcode { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_barcode { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_barcode { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string unit_conv { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string stock_qty { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string current_qty { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_unit_qty { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_qty { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_qty { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_unit_qty_storestock { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_qty_storestock { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_qty_storestock { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_images{ get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_factor { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_factor { get; set; }
        [FromFld("setting->>'costPriceType'", LOAD_PURPOSE.SHOW)] public string cost_price_type { get; set; }
      


        public bool HasStockQty = false;
        //public decimal StockQty = 0;
     
    }
    public class SheetStoreStock : SheetBase<SheetRowStoreStock>
    {
        [SaveToDB] [FromFld] public override SHEET_TYPE sheet_type { get; set; }
        public string branch_id { get; set; } = "";
        [SaveToDB][FromFld] public string client_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string client_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string branch_name { get; set; } = "";
        [SaveToDB] [FromFld] public string seller_id { get; set; } = "";
        [SaveToDB] [FromFld] public string wholesale_amount { get; set; } = "";
        [SaveToDB] [FromFld] public string cost_amount_avg { get; set; } = "";
        [SaveToDB] [FromFld] public string buy_amount { get; set; } = "";
        // [SaveToDB] [FromFld] public string loss_wholesale_amount { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";
        //[SaveToDB] [FromFld] public string inventory_type { get; set; } = "";
        public string branch_id_forsale { get; set; } = ""; 
        public string branch_name_forsale { get; set; } = "";
        public string itemImages { get; set; } = "";
        public SHEET_TYPE sheetTypeCreate { get; set; } 
        public string sum_add_qty
        {
            get
            {
                decimal b_sum_quantity = 0, m_sum_quantity = 0, s_sum_quantity = 0;
                foreach (var row in SheetRows)
                {
                    decimal qty = row.real_d_quantity - row.sys_quantity;
                    decimal bf = 0, mf = 0;
                    if (row.b_unit_factor.IsValid()) bf = CPubVars.ToDecimal(row.b_unit_factor);
                    if (row.m_unit_factor.IsValid()) mf = CPubVars.ToDecimal(row.m_unit_factor); 
                    string s = SheetRowStoreStock.GetQtyUnit(qty, row.b_unit_no, bf, row.m_unit_no, mf, row.s_unit_no, ref b_sum_quantity, ref m_sum_quantity, ref s_sum_quantity);
                }
                string sum = "";
                if (b_sum_quantity != 0) sum += CPubVars.FormatMoney(b_sum_quantity, 3) + "大";
                if (m_sum_quantity != 0) sum += CPubVars.FormatMoney(m_sum_quantity, 3) + "中";
                if (s_sum_quantity != 0) sum += CPubVars.FormatMoney(s_sum_quantity, 3) + "小";
                return sum; 
            }
        }
        public string sum_current_qty
        {
            get
            {
                decimal b_sum_quantity = 0, m_sum_quantity = 0, s_sum_quantity = 0;
                foreach (var row in SheetRows)
                {
                    decimal qty = row.sys_quantity;
                    decimal bf = 0, mf = 0;
                    if (row.b_unit_factor.IsValid()) bf = CPubVars.ToDecimal(row.b_unit_factor);
                    if (row.m_unit_factor.IsValid()) mf = CPubVars.ToDecimal(row.m_unit_factor);
                    string s = SheetRowStoreStock.GetQtyUnit(qty, row.b_unit_no, bf, row.m_unit_no, mf, row.s_unit_no, ref b_sum_quantity, ref m_sum_quantity, ref s_sum_quantity);
                }
                string sum = "";
                if (b_sum_quantity != 0) sum += CPubVars.FormatMoney(b_sum_quantity, 3) + "大";
                if (m_sum_quantity != 0) sum += CPubVars.FormatMoney(m_sum_quantity, 3) + "中";
                if (s_sum_quantity != 0) sum += CPubVars.FormatMoney(s_sum_quantity, 3) + "小";
                return sum;
            }
        }
        public string sum_inventory_qty
        {
            get
            {
                decimal b_sum_quantity = 0, m_sum_quantity = 0, s_sum_quantity = 0;
                foreach (var row in SheetRows)
                {
                    decimal qty = row.real_d_quantity;
                    decimal bf = 0, mf = 0;
                    if (row.b_unit_factor.IsValid()) bf = CPubVars.ToDecimal(row.b_unit_factor);
                    if (row.m_unit_factor.IsValid()) mf = CPubVars.ToDecimal(row.m_unit_factor);
                    string s = SheetRowStoreStock.GetQtyUnit(qty, row.b_unit_no, bf, row.m_unit_no, mf, row.s_unit_no, ref b_sum_quantity, ref m_sum_quantity, ref s_sum_quantity);
                }
                string sum = "";
                if (b_sum_quantity != 0) sum += CPubVars.FormatMoney(b_sum_quantity, 3) + "大";
                if (m_sum_quantity != 0) sum += CPubVars.FormatMoney(m_sum_quantity, 3) + "中";
                if (s_sum_quantity != 0) sum += CPubVars.FormatMoney(s_sum_quantity, 3) + "小";
                return sum;
            }
        }
        //[SaveToDB] [FromFld] public override int money_inout_flag { get; set; }
        [SaveToDB]
        [FromFld]
        public virtual string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>(); 
                if (!TempHappenTime)
                {
                    sheetAttribute.Add("tempHappenTime", "false");
                } 
                
                string s = "";
                if (sheetAttribute.Count > 0) s = JsonConvert.SerializeObject(sheetAttribute); 
                return s;
            }
            set
            {

                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);


                    if (sheetAttr.tempHappenTime != null)
                    {
                        this.TempHappenTime = sheetAttr.tempHappenTime != "false";
                    }


                }
            }
        }
        public SheetStoreStock(LOAD_PURPOSE loadPurpose) : base("sheet_client_stock_main", "sheet_client_stock_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_STORE_STOCK;
            if (loadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @" left join info_branch b on t.branch_id=b.branch_id  and b.company_id = ~COMPANY_ID                                
                                  left join (select oper_id,oper_name as seller_name from info_operator where company_id = ~COMPANY_ID) seller on t.seller_id=seller.oper_id
                                  left join (select supcust_id,sup_name as client_name  from info_supcust where company_id = ~COMPANY_ID) sup on t.client_id=sup.supcust_id
                                  left join (select oper_id,oper_name as maker_name from info_operator where company_id = ~COMPANY_ID) maker on t.maker_id=maker.oper_id
                                  left join  (select oper_id,oper_name as approver_name from info_operator where company_id = ~COMPANY_ID) approver on t.approver_id=approver.oper_id
                ";
                //  DetailLeftJoin = "left join info_item_prop i on t.item_id=i.item_id";
                DetailLeftJoin = $@" 
left join sheet_client_stock_main m on m.company_id = t.company_id and m.sheet_id = t.sheet_id
left join
(
    select item_id,item_name,tem.item_spec,item_images,tem.item_no,stock_qty,wholesale_price,cost_price_avg,cost_price_spec,yj_get_bms_qty(stock_qty,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no) as current_qty,batch_level,
         yj_get_unit_relation(b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no) as unit_conv,
         yj_get_unit_qty('b',real_qty,b_unit_factor,m_unit_factor,true) as b_unit_qty,
         yj_get_unit_qty('m',real_qty,b_unit_factor,m_unit_factor,true) as m_unit_qty,
         case when real_qty=0 then 0 else yj_get_unit_qty('s',real_qty,b_unit_factor,m_unit_factor,true) end as s_unit_qty,
        yj_get_unit_qty('b',client_stock_quantity,b_unit_factor,m_unit_factor,true) as b_unit_qty_storestock,
         yj_get_unit_qty('m',client_stock_quantity,b_unit_factor,m_unit_factor,true) as m_unit_qty_storestock,
         case when real_qty=0 then 0 else yj_get_unit_qty('s',client_stock_quantity,b_unit_factor,m_unit_factor,true) end as s_unit_qty_storestock,
         b_unit_no,m_unit_no,s_unit_no,s_barcode, b_barcode,m_barcode, m_unit_factor,b_unit_factor,batch_id,branch_position,produce_date,batch_no
    from 
    (
        SELECT ip.item_id,ip.item_images,ip.item_name,ip.item_spec,ip.item_no,branch_position,stock_qty,real_qty,buy_quantity,client_stock_quantity,ip.wholesale_price,ip.cost_price_avg,ip.cost_price_spec, batch_id,produce_date,batch_no,ip.batch_level,
		                                t.s->>'f2' as s_unit_no,t.s->>'f3' as spPrice,t.s->>'f4' as slPrice,t.s->>'f5' s_barcode,
            (t.m->>'f1')::numeric as m_unit_factor,t.m->>'f2' as m_unit_no,t.m->>'f3' as mpPrice,t.m->>'f4' as mlPrice,t.m->>'f5' m_barcode,
            (t.b->>'f1')::numeric as b_unit_factor,t.b->>'f2' as b_unit_no,t.b->>'f3' as bpPrice,t.b->>'f4' as blPrice,t.b->>'f5' b_barcode
                               
        FROM info_item_prop as ip
        LEFT JOIN
        (
            select item_id, s, m, b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,retail_price,barcode)) as json from info_item_multi_unit where company_id=~COMPANY_ID ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$)  as errr(item_id int, s jsonb,m jsonb, b jsonb) 
        ) t on ip.item_id = t.item_id
        right JOIN
        (
            select sys_quantity as stock_qty,item_id,branch_position,real_quantity as real_qty,buy_quantity,client_stock_quantity,d.batch_id,produce_date,batch_no from sheet_client_stock_detail d
		     left join (select batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date from info_item_batch where company_id= ~COMPANY_ID ) itb on itb.batch_id =COALESCE(d.batch_id,0) 
where company_id = ~COMPANY_ID and sheet_id = ~sheet_id
        ) stock on t.item_id = stock.item_id where ip.company_id=~COMPANY_ID
   ) tem
) dd on dd.item_id = t.item_id  and COALESCE(t.batch_id, 0) = COALESCE(dd.batch_id,0) and COALESCE(dd.branch_position,0) = COALESCE(t.branch_position, 0)
left join company_setting cs on t.company_id=cs.company_id
left join info_branch ibb on ibb.branch_id = m.branch_id
left join info_branch_position ibp on ibp.branch_id = m.branch_id and ibp.branch_position = COALESCE(t.branch_position,0)";

              

                   
            }
            else if (loadPurpose == LOAD_PURPOSE.APPROVE)
                DetailLeftJoin = $" left join stock on t.item_id=stock.item_id and t.company_id=stock.company_id and t.branch_id = stock.branch_id and t.batch_id = stock.batch_id and t.branch_position = stock.branch_position ";
        }
	 
        public override string GetDataLockKey()
        {
            return this.company_id + "_" + this.branch_id;
        }
        protected override void InitForSave()
        {
            base.InitForSave();
            if (seller_id == "") seller_id = OperID;
            if (approver_id == "") approver_id = OperID;

        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql;
            base.GetInfoForApprove_SetQQ(QQ);
            if (SheetRows.Count > 0)
            {
                string items_id = "";
                string batchs_id = "";
                string branchs_position = "";
                foreach (SheetRowStoreStock row in SheetRows)
                {
                    if (items_id != "") items_id += ",";
                    items_id += row.item_id;
                    if (batchs_id != "") batchs_id += ",";
                    batchs_id += row.batch_id;
                    if (branchs_position != "") branchs_position += ",";
                    branchs_position += row.branch_position;
                }
                if (batchs_id == "")
                {
                    batchs_id = "0";
                }

                sql = $"select item_id,stock_qty,batch_id,branch_position from stock where company_id={company_id} and item_id in ({items_id}) and batch_id in ({batchs_id})";
                QQ.Enqueue("stock", sql);
            }
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName,bForRed);
            if (sqlName == "stock")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowStoreStock row in SheetRows)
                    {
                        if (rec.item_id != "" && row.item_id == rec.item_id && row.batch_id == rec.batch_id && row.branch_position == rec.branch_position)
                        {
                            if (rec.stock_qty != "")
                            {
                                row.HasStockQty = true;
                               // row.StockQty = CPubVars.ToDecimal(rec.stock_qty);
                            }
                        }
                    }
                }
                info.SheetRows = SheetRows;

            }
        }
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
            //if (branch_id == "") return "请指定仓库";
            if (SheetRows.Count == 0) return "请指定至少一行商品";
            return "OK";
        }
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.sheet_id + "_"  + "_" + this.branch_id + "_" + this.OperID + "_" + this.seller_id + "_" + this.make_brief;
            foreach (var row in SheetRows)
            {
                res += row.item_id + "_" + row.item_name + "_" + row.sys_quantity+"_" +row.real_quantity +"_" + row.buy_quantity + "_" + row.client_stock_quantity + "_" +  row.remark;
            }
            return res;
        }
    
        
        protected class CInfoForApprove : CInfoForApproveBase
        {
            public string ArrearBalance = "", PrepayBalance = "";
            public List<SheetRowStoreStock> SheetRows = null;
        }
        /*protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";
            int inoutFlag = 1;
            if (sheet_type == SHEET_TYPE.SHEET_INVENT_INPUT)
            {
                inoutFlag = -1;
            }
            if (red_flag == "2") inoutFlag *= -1;

             
            //changeSheet.SaveAndApprove(cmd, false);
            

            return sql;
        }*/
         protected override async Task<string> BeforeRed(CMySbCommand cmd, string sheetID, string rederID,string redBrief,CInfoForApproveBase info)
         {  
            SheetInventChange changeSheet = new SheetInventChange(SheetInventChange.IS_REDUCE.NOT_REDUCE, LOAD_PURPOSE.SHOW);
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync($"select sheet_id,sheet_no from sheet_invent_change_main where inventory_sheet_id={sheetID}", cmd);
            string changeSheetID = data.sheet_id;
            string changeSheetNo = data.sheet_no;
            changeSheet.sheet_no = changeSheetNo;
 
            string err = await changeSheet.Red(cmd, this.company_id, changeSheetID, rederID, redBrief,false);
         
            return err;
        }
        
        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            foreach (dynamic row in this.SheetRows)
            {
                string branchID = row.branch_id;
                string branchName = row.branch_name;
                if (branchID.IsInvalid())
                {
                    branchID = branch_id;
                    branchName = branch_name;
                }
                if (row.branch_position == "0" || string.IsNullOrWhiteSpace(row.branch_position)) continue;
                dynamic record = await CDbDealer.Get1RecordFromSQLAsync($"select branch_position from info_branch_position where company_id = {company_id} and branch_position = {row.branch_position} and branch_id = {branchID};", cmd);
                if (record == null)
                {
                    return $"{branchName}不存在库位：{row.branch_position_name}";
                }
            }

            string msg=await CheckBatch(cmd);
            if (msg != "") return msg;
            return "OK";

        }
        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting); 
            
        }

        public async Task<string> CheckBatch(CMySbCommand cmd ,bool bAutoCommit = true)
        {
            string msg = "";
            string insertSql = "";
            string insertValue = "";
            string selectSql = "";
            string selectValue = "";
            Dictionary<string, dynamic> batchDic = new Dictionary<string, dynamic>();
            Dictionary<string, string> sheetRowBatch = new Dictionary<string, string>();
            try
            {

                foreach (SheetRowStoreStock row in SheetRows)
                {
                    if (row.produce_date.IsInvalid())
                    {
                        continue;
                    }
                    string key = row.produce_date + row.batch_no;
                    if (!batchDic.ContainsKey(key))
                    {
                        batchDic[key] = new { produce_date = row.produce_date, batch_no = row.batch_no };
                        if(selectValue!="")  selectValue += ",";
                        selectValue += $@"('{row.produce_date}','{row.batch_no}')";
                    }
                }
                if (selectValue != "") selectSql = $@"select * from info_item_batch where company_id = {company_id} and (substring(produce_date::text,1,10),batch_no) in ({selectValue});";
                if (selectSql != "")
                {
                    List<ExpandoObject> selectRec = await CDbDealer.GetRecordsFromSQLAsync(selectSql, cmd);
                    foreach (dynamic row in selectRec)
                    {
                        string produceDate = row.produce_date;
                        string batchNo = row.batch_no;
                        produceDate = produceDate.Substring(0, 10);
                        string key = produceDate + batchNo;
                        sheetRowBatch.Add(key, row.batch_id);
                        if (batchDic.ContainsKey(key)) batchDic.Remove(key);
                    }
                    foreach(KeyValuePair<string,dynamic> kv in batchDic)
                    {
                        string produceDate = kv.Value.produce_date;
                        string batchNo = kv.Value.batch_no;
                        if(insertValue!="")  insertValue += ",";
                        insertValue += $@"({company_id},'{produceDate}','{batchNo}')";
                    }
                    if (insertValue!="")//INSERT INTO
                    {
                        insertSql += $"insert into info_item_batch (company_id,produce_date,batch_no) values {insertValue} on CONFLICT(company_id,produce_date,batch_no) DO NOTHING RETURNING batch_id,produce_date,batch_no;";
                        List<ExpandoObject> insertRec = await CDbDealer.GetRecordsFromSQLAsync(insertSql, cmd);
                        foreach (dynamic row in insertRec)
                        {
                            string produceDate = row.produce_date;
                            string batchNo = row.batch_no;
                            produceDate = produceDate.Substring(0, 10);
                            string key = produceDate + batchNo;
                            sheetRowBatch.Add(key, row.batch_id);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                msg = "生产日期/批次错误";
                MyLogger.LogMsg(msg + e.Message + e.StackTrace + "SQL:" + cmd.CommandText, company_id, "produce_date");
            }
            if (msg == "")
            {
                foreach (SheetRowStoreStock row in SheetRows)
                {
                    if (row.produce_date.IsInvalid())
                    {
                        row.batch_id = "0";
                        continue;
                    }
                    string key = row.produce_date + row.batch_no;
                    if (sheetRowBatch.ContainsKey(key))
                    {
                        row.batch_id = sheetRowBatch[key];
                    }
                    else
                    {
                        msg = "生产日期/批次错误";
                    }
                }
            }
            return msg;
        }

    }
}
