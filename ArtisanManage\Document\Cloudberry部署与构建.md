# 拉取源码

`git clone https://github.com/cloudberrydb/cloudberrydb.git`

# 运行对应系统bash文件安装依赖

华为云debian可以使用README.Ubuntu.bash

`sudo ~/cloudberrydb/deploy/build/README.Ubuntu.bash`

`sudo apt install software-properties-common  
sudo add-apt-repository ppa:ubuntu-toolchain-r/test  
sudo apt install gcc-10 g++-10  
sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-10 100`

# 其余准备工作

`echo -e "/usr/local/lib \n/usr/local/lib64" >> /etc/ld.so.conf
ldconfig`

创建gpdamin用户，密码11035758Tao

# 构建cbdb

运行config脚本

`cd cloudberrydb
./configure --with-perl --with-python --with-libxml --with-gssapi --prefix=/usr/local/cloudberrydb`

编译并安装

`make -j8
make -j8 install`

引入greenplum环境

`cd ..
cp -r cloudberrydb/ /home/<USER>/
cd /home/<USER>/
chown -R gpadmin:gpadmin cloudberrydb/
su - gpadmin
cd cloudberrydb/
source /usr/local/cloudberrydb/greenplum_path.sh`

启动示例集群

make create-demo-cluster

![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-09-03-21-30-29-595b1ba31971fdbde250a1075121acac.png)

# 其余参考指南

通过bash安装依赖可能会出现类似问题

![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-09-03-21-33-23-image.png)

可以逐步排查，以下为当前服务器参考

`sudo pip3 install launchpadlib -i https://mirrors.aliyun.com/pypi/simple --break-system-packages`

并可参考命令日志
