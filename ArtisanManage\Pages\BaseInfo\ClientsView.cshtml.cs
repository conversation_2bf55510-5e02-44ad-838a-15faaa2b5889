﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Dynamic;
using System.Linq;
using System.Text;
using static ArtisanManage.Services.CommonTool;
using ArtisanManage.Pages.Sheets;
using System.Net.Http;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ClientsViewModel : PageQueryModel
    {
        public string m_classTreeStr = ""; 
        public bool ForSelect = false;
        public bool MultiSelect = false;
        /// <summary>
        /// 
        /// </summary>
        public ClientsViewModel(CMySbCommand cmd) : base(Services.MenuId.infoClient)
        {
            this.cmd = cmd;
            this.PageTitle = "客户档案";
            DataItems = new Dictionary<string, DataItem>()
            {                
              
                 {"sup_group",new DataItem(){Title="渠道",FldArea="divHead",LabelFld="group_name",ButtonUsage="list",QueryOnChange=true,CompareOperator="=",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                 {"sup_name",new DataItem(){Title="检索",FldArea="divHead",SqlFld="sup_name,s.py_str,s.mobile,s.supcust_no,sup_addr,s.boss_name,s.supcust_remark",PlaceHolder="客户名/简拼/手机/编号/地址/老板姓名/备注", QueryOnChange=true,CompareOperator="ilike",Width="250"}},
                 {"status",new DataItem(){Title = "状态",FldArea="divHead",LabelFld = "cls_status_name", LabelInDB = false, Value = "normal", Label = "正常",ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "normal",
                    
                     Source = @"[{v:'normal',l:'正常',condition:""(s.status = '1' or s.status is null)""},
                               {v:'stop',l:'停用',condition:""s.status = '0' ""},
                               {v:'all',l:'所有',condition:""true""}]"

                 }},
                {"acct_cust_id",new DataItem(){FldArea="divHead",Title="结算单位",LabelFld="acct_cust_name",Checkboxes=true,Hidden=true, ButtonUsage="list",CompareOperator="=",DropDownWidth = "200",SqlFld="acct_cust_id",
                SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where company_id= ~COMPANY_ID and supcust_flag ilike '%C%'and acct_cust_id is null order by supcust_id "} },
                 {"sup_rank",new DataItem(){Title="级别",FldArea="divHead",LikeWrapper="/", LabelFld="rank_name", QueryOnChange=true,CompareOperator="=",Checkboxes=true,ButtonUsage = "list",
                   SqlForOptions="select rank_id as v,rank_name as l  from info_supcust_rank"} },
                 {"acct_type",new DataItem(){Title = "结算类型",FldArea="divHead",LabelFld = "acct_type_name", LabelInDB = false, Value = "", Label = "",ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "",
                     Source = @"[{v:'normal',l:'现结',condition:""(s.acct_type = 'pay' or s.acct_type is null)""},
                               {v:'stop',l:'欠款',condition:""s.acct_type = 'arrears' ""},{v:'all',l:'所有',condition:""true""}]"
                 }},

                // {"other_region_head",new DataItem(){Title="片区",FldArea="divHead",LabelFld="region_name", QueryOnChange=true,CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",SqlFld="other_region",
                //    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region"
                //}},
                 {"seller_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="seller_name",Hidden=true, ButtonUsage="list",NullEqualValue="-1", QueryOnChange=true,CompareOperator="=",SqlFld="cs.oper_id",SqlForOptions=CommonTool.selectSellers } },
                {"creator_id",new DataItem(){Title="创建人",FldArea="divHead",LabelFld="creator_name",Hidden=true, ButtonUsage="list",QueryOnChange=true,CompareOperator="=",SqlFld="s.creator_id",SqlForOptions=CommonTool.selectSellers } },
                {"startDay",new DataItem(){Title="创建: 从",FldArea="divHead",CtrlType="jqxDateTimeInput",Hidden=true, SqlFld="virtual_create_time",   CompareOperator=">="}},
                 {"endDay"  ,new DataItem(){Title="到",FldArea="divHead",CtrlType="jqxDateTimeInput",Hidden=true, SqlFld="virtual_create_time",   CompareOperator="<",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},

                {"supcust_flag",new DataItem(){Title="",FldArea="divHead",LabelFld="",CtrlType="hidden" } },
                {"showAcctCusts",new DataItem(){Title = "显示结算门店", FldArea = "divHead", Hidden = true,HideOnLoad=true, ForQuery = false, LabelInDB = false}},

                
                {"other_region",new DataItem(){Title="片区",FldArea="divHead",LikeWrapper="/", CtrlType="jqxTree",MumSelectable=true,TopNodeValueRestricted=true, GetOptionsOnLoad=true,MaxRecords="500", QueryOnChange=true,CompareOperator="like",
                   SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by order_index , region_id",
                   JSBeforeCreate=@"
                        var treeHeight = $('#other_region').height() 
                        if (treeHeight < 50) {
                            treeHeight = $('body').height() - $('#divTop').height() - 30
                            $('#~ItemID').height(treeHeight) 
                        }
            "
                } },
                // {"approve_status",new DataItem(){Title = "审核状态",FldArea="divHead",LabelFld = "cls_approve_status_name", LabelInDB = false, Value = "normal", Label = "已审核",ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "normal",
                {"approve_status",new DataItem(){Title = "审核状态",FldArea="divHead",LabelFld = "cls_approve_status_name", LabelInDB = false, ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", 
                     Source = @"[{v:'normal',l:'已审核',condition:""s.approve_status is null""},
                               {v:'stop',l:'待审核',condition:""s.approve_status is not null""},]"

                 }},




            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {

                     HasCheck=true,KeepCheckForQueries=true,
                     IdColumn="supcust_id",TableName="info_supcust",
                     ContextMenuHTML="<ul><li id='edit'>编辑</li><li id='remove'>删除</li><li id='BatchOperation'>批量操作</li></ul>",
                     ShowContextMenu=true,
                     Sortable=true,
                     JSBeforeCreate=@"
var gridHeight= $('#gridItems').height()
if(gridHeight==0) { 
    var headHeight=$('#divTop').height()
    var bodyHeight=$('body').height()
    gridHeight=bodyHeight-headHeight-30
    $('#gridItems').height(gridHeight)
}",
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"supcust_id",new DataItem(){Title="编号", Width="80",Hidden=true,HideOnLoad=true,SqlFld="s.supcust_id"}},
                       {"supcust_no",new DataItem(){Title="客户编号", Width="100",Hidden=true,Sortable=true,SortFld="LPAD(supcust_no, 10, '0')"}},
                       {"sup_name",new DataItem(){Title="客户名称", Width="200",Linkable=true,Sortable=true,
                       JSCellRender=@"function (row, column, value,p4,p5,rowData) { 
                            var is_new=rowData.new;
                            var supcust_id=rowData.supcust_id;
                            var html='';
                            if(supcust_id && is_new) html='<label style = ""margin-right:3px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:16px"" >新</label>'
                             return `<div style = ""height:100%;display:flex;align-items:center;justify-content:flex-start;"" ><label style=""cursor:pointer;margin-left:4px;color:#49f;margin-right:2px"">${value}</label>${html}</div>`
                            }"
                       }},
                       {"approve_status",new DataItem(){Title="审核状态", Width="100",Hidden=true,Linkable=true,SqlFld="s.approve_status",
                        JSCellRender=@"function (row, column, value,p4,p5,rowData) { 
                            var supcust_id=rowData.supcust_id;
                            if(!supcust_id) return ''
                            let v = value?`<div style = ""height:100%;width:100%;display:flex;align-items:center;justify-content:center;color:red"" >待审核</div>`:`<div style = ""height:100%;width:100%;display:flex;align-items:center;justify-content:center;#49f"" >已审核</div>`
                            return v
                            }"
                        }},
                       {"diff_describe",new DataItem(){Title="修改记录", Width="180",JSCellRender="changeLogView",Hidden=true}},
                       {"boss_name",new DataItem(){Title="老板姓名", Width="100"}},
                       {"mobile",new DataItem(){Title="老板手机",SqlFld="s.mobile", Width="200"}},
                       {"seller_name",new DataItem(){Title="业务员",SqlFld="cs.oper_name",Hidden=true, Width="200"}},
                       {"sup_addr",new DataItem(){Title="地址", Width="200"}},
                       {"py_str",new DataItem(){Title="助记码",SqlFld="s.py_str",Hidden=true, Width="200"}},
                       {"location",new DataItem(){Title="定位",Width="180",SqlFld="case when addr_lat is not null and  addr_lng is not null then '是'else'否'end",Hidden = true}},
                       {"region_path",new DataItem(){Title="片区", Width="100",GetFromDb=false,  FuncGetValueFromRowData=(colName,row)=>
                           {
                               string s = "",q="";
                               q = row["region1_name"]; if (q != "") s += q;
                               q = row["region2_name"]; if (q != "") s += "/" + q;
                               q = row["region3_name"]; if (q != "") s += "/" + q;
                               q = row["region4_name"]; if (q != "") s += "/" + q;
                               return s;
                           }}}, 
                       {"region1_name",new DataItem(){Title="片区1", Width="100",Hidden=true}},
                       {"region2_name",new DataItem(){Title="片区2", Width="100",Hidden=true}},
                       {"region3_name",new DataItem(){Title="片区3", Width="100",Hidden=true}},
                       {"region4_name",new DataItem(){Title="片区4", Width="100",Hidden=true}},
                        {"supcust_flag",new DataItem(){Title="类型标志",Hidden = true,HideOnLoad=true}},
                       {"license_no",new DataItem(){Title="营业执照号",Hidden=true}},
                       {"group_name",new DataItem(){Title="渠道", Width="100"}},
                       {"new",new DataItem(){ Title="new",SaveToDB=false,Hidden=true,HideOnLoad=true, SqlFld="(CASE WHEN  ( DATE ( now( ) ) - DATE ( create_time :: DATE ) )<=3 THEN 'true' END )" } },
                       {"virtual_create_time",new DataItem(){Title="创建时间",Width = "180",FldArea="divHead",SqlFld = "virtual_create_time"} },
                       {"rank_name",new DataItem(){Title="级别", Width="80"}},
                       {"status",new DataItem(){Title="状态",Width="180",SqlFld="(case WHEN s.status='0' THEN '停用' ELSE '正常' END)"}},
                       {"acct_type",new DataItem(){Title="",Width="180",SqlFld="s.acct_type", HideOnLoad=true,Hidden = true}},
                       {"acct_type_name",new DataItem(){Title="结算类型",Width="180",SqlFld="(case WHEN s.acct_type='arrears' THEN '欠款' ELSE '现结' END)"}},
                       {"creator_name",new DataItem(){Title="创建人",Width="180",SqlFld="io.oper_name",Hidden = true}},
                       {"acct_name",new DataItem(){Title="结算单位",Width="180",Hidden = true}},
                       {"balance",new DataItem(){Title="欠款金额",Width="100",Hidden = true}},
                       {"balance_has_order",new DataItem(){Title="欠款金额(含订)",Width="100",Hidden = true,SqlFld="COALESCE(balance,0) + COALESCE(pend_amount,0)"}},
                       {"max_arrears",new DataItem(){Title="欠款额度",Width="100",Hidden = true}},
                       {"max_arrears_days",new DataItem(){Title="欠款天数",Width="100",Hidden = true}},
                       {"sup_order_index",new DataItem(){Title="显示顺序",Width="100",Hidden = false}},
                       {"addr_lng",new DataItem(){Title="经度",Width="10",Hidden = true,HideOnLoad=true}},
                       {"addr_lat",new DataItem(){Title="纬度",Width="10",Hidden = true,HideOnLoad=true}},
                       {"acct_way_name",new DataItem(){Title="结算方式", LabelFld="acct_way_name",Width="100"}},
                       {"addr_position",new DataItem(){Title="位置",Width="10",Hidden = true,GetFromDb=false,  FuncGetValueFromRowData = (colName,row) =>
                           {
                               string s = "",q="";
                               q = row["addr_lng"]; if (q != "") s += q;
                               q = row["addr_lat"]; if (q != "") s += "," + q;
                               if(s!="")
                                    s=Security.MyEncrypt(s);
                               return s;
                           }
                       }
                     },
                      {"transaction_type",new DataItem(){Title="销售方式",SqlFld="(case when s.retail_wholesale_flag = 'r' then '零售' when s.retail_wholesale_flag = 'w' then '批发' else '' end)", Width="80"}},
                     {"supcust_remark",new DataItem(){Title="备注",Width="100"}},
                  },
                     QueryFromSQL=@"
from
(
    select *,(case when create_time is null then '2020-01-01 00:00:00' else to_char(create_time,'yyyy-mm-dd HH24:MI:SS') end) as virtual_create_time
    from info_supcust where company_id= ~COMPANY_ID and supcust_flag like '%C%'
) s 
left join (select region_id,region_name region1_name from info_region where company_id= ~COMPANY_ID) r1 on split_part(s.other_region,'/',3) = r1.region_id::text
left join (select region_id,region_name region2_name from info_region where company_id= ~COMPANY_ID) r2 on split_part(s.other_region,'/',4) = r2.region_id::text
left join (select region_id,region_name region3_name from info_region where company_id= ~COMPANY_ID) r3 on split_part(s.other_region,'/',5) = r3.region_id::text
left join (select region_id,region_name region4_name from info_region where company_id= ~COMPANY_ID) r4 on split_part(s.other_region,'/',6) = r4.region_id::text

left join (select group_id,group_name from info_supcust_group where company_id= ~COMPANY_ID) g on g.group_id = s.sup_group
left join (select rank_id,rank_name from info_supcust_rank where company_id= ~COMPANY_ID) sr on sr.rank_id = s.sup_rank 
left join info_operator io on s.creator_id = io.oper_id and  io.company_id= ~COMPANY_ID
left join info_operator cs on s.charge_seller = cs.oper_id and  cs.company_id= ~COMPANY_ID
left join 
(
   select sup_name acct_name,supcust_id csupcust_id from info_supcust  where company_id= ~COMPANY_ID 
) acct on s.acct_cust_id=acct.csupcust_id
left join arrears_balance ab on s.supcust_id=ab.supcust_id and ab.company_id=~COMPANY_ID
LEFT JOIN  info_acct_way aw ON s.acct_way_id = aw.acct_way_id and aw.company_id=~COMPANY_ID
LEFT JOIN (
    SELECT DISTINCT ON (obj_id)
        diff_describe,
        obj_id,
        happen_time
    FROM document_change_log
    WHERE company_id = ~COMPANY_ID 
      AND approve_time IS NULL
      AND obj_name IN ('客户档案', 'infoClient')
    ORDER BY obj_id, happen_time DESC
) dcl ON s.supcust_id = dcl.obj_id
   AND s.approve_status IS NOT NULL
where s.company_id= ~COMPANY_ID ~SQL_VARIABLE1 ~VAR_acct_custs_condi" ,
                      QueryOrderSQL="order by sup_order_index,virtual_create_time desc,supcust_id desc "
                  }
                } 
            }; 
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
           
            //DataItems["startDay"].Value = "2020 - 01 - 01 00:00:00";
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            string showAcctCusts = DataItems["showAcctCusts"].Value;
            if (showAcctCusts == "1")
            {
                this.SQLVariables["acct_custs_condi"] = " and acct_cust_id is null";
            }
            else
            {
                this.SQLVariables["acct_custs_condi"] = " ";
            }
			if (DataItems["other_region"].Label == "全部")
			{
                DataItems["other_region"].Value = ""; 
            }
        }
        public async Task OnGet(string forSelect,string multiSelect)
        {
            await InitGet(cmd);
            ForSelect = forSelect == "1";
            MultiSelect = multiSelect == "1";

        }
        public static async Task<string> CheckBeforeDeleteRecords_client(CMySbCommand cmd, string rowIDs,string company_id)
        {
            cmd.CommandText = $@"
                select company_id from sheet_sale_main where (supcust_id in ({rowIDs}) or acct_supcust_id in ({rowIDs})) and company_id={company_id} 
                union  select company_id from sheet_fee_out_main where (supcust_id in ({rowIDs}) or acct_supcust_id in ({rowIDs})) and company_id={company_id} 
                union  select company_id from sheet_sale_order_main where supcust_id in ({rowIDs}) and company_id={company_id} 
                union  select company_id from sheet_get_arrears_main where supcust_id in ({rowIDs}) and company_id={company_id}
                union select company_id from sheet_prepay where supcust_id in ({rowIDs}) and company_id={company_id}
                union select company_id from sheet_visit where supcust_id in ({rowIDs}) and company_id={company_id} limit 1
             ";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该客户已使用,无法删除";

            return "";
        }
        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            return await CheckBeforeDeleteRecords_client(cmd,rowIDs, company_id);


        } 
         
    }



    [Route("api/[controller]/[action]")]
    public class ClientsViewController : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public ClientsViewController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }
 
        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ClientsViewModel model = new ClientsViewModel(this.cmd); 
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);           
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {// string gridID,int startRow,int endRow,bool bNewQuery){

            ClientsViewModel model = new ClientsViewModel(cmd); 
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<IActionResult> RemoveRegion([FromBody] dynamic value)
        {
            ClassEditModel model = new ClassEditModel(cmd);
            string operKey = value.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);

                string id = value[model.m_idFld];
            string result = "OK";
            if (id == "")
            {
                result = "请传入类编号";
                goto end;
            }

            object o = null;

            cmd.CommandText = $"select region_id from info_region where  company_id={companyID} and mother_id={id}";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该片区的子片区后再删除"; goto end;
            }
            cmd.CommandText = $"select sup_name from info_supcust where company_id={companyID} and COALESCE(status,'1')='1' and region_id={id}";
            o =await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该片区的客户后再删除该类"; goto end;
            }
            string sql = $"delete from info_region where company_id={companyID} and region_id='{id}'";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
             
            end:
            return Json(new { result, region_id = id });
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)//删除grid行请求
        {
            ClientsViewModel model = new ClientsViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd, "info_supcust");// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            ClientsViewModel model = new ClientsViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
        
        [HttpPost]
        public async Task<JsonResult> BatchSetRegion([FromBody] dynamic data)
        {
            string region_id = data.region_id;
            string other_region = data.other_region;
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string rows = string.Join(",", data.rows);
            string sql = $"UPDATE info_supcust SET region_id = {region_id},other_region ='{other_region}' WHERE company_id ={companyID} AND supcust_id in ({rows})";
           
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new { result = "OK", msg = "" });
        }
        [HttpPost]
        public async Task<JsonResult> BatchSetOperator([FromBody] dynamic data)
        {
            string operID = data.operID;
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string rows = string.Join(",", data.rows);
            string sql = $"UPDATE info_supcust SET charge_seller = {operID} WHERE company_id ={companyID} AND supcust_id in ({rows})";
            if (string.IsNullOrEmpty(operID))
            {
                sql = $"UPDATE info_supcust SET charge_seller = null WHERE company_id ={companyID} AND supcust_id in ({rows})";
            }
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new { result = "OK", msg = "" });
            
            
            
            
        }
        [HttpPost]
        public async Task<JsonResult> BatchSetGroup([FromBody] dynamic data)
        {
            string sup_group = data.sup_group;
            string rows = string.Join(",", data.rows);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string sql = $"UPDATE info_supcust SET sup_group = {sup_group} where company_id ={companyID} AND supcust_id in ({rows})";

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new { result = "OK", msg = "" });
        }
        public async Task<JsonResult> BatchSetAddress([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            dynamic updateRows = data.updateAddressRows;
            List<string> updateSQLs = new List<string>();
            foreach (var row in updateRows)
            {
                updateSQLs.Add(@$"UPDATE info_supcust SET sup_addr = '{row.sup_addr}' WHERE supcust_id = '{row.supcust_id}' AND company_id = '{companyID}';");

            }
            cmd.CommandText = string.Join("", updateSQLs);
            await cmd.ExecuteScalarAsync();
            return Json(new { result = "OK" });
        }
        [HttpPost]
        public async Task<JsonResult> BatchSetRank([FromBody] dynamic data)
        {
            string sup_rank = data.sup_rank;
            string rows = string.Join(",", data.rows);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string sql = $"UPDATE info_supcust SET sup_rank = {sup_rank} where company_id ={companyID} AND supcust_id in ({rows})";

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new { result = "OK", msg = "" });
        }
        [HttpPost]
        public async Task<JsonResult> BatchSetAcctType([FromBody] dynamic data)
        {
            string acct_type = data.acct_type;
            string rows = string.Join(",", data.rows);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string sql = $"UPDATE info_supcust SET acct_type = '{acct_type}' where company_id ={companyID} AND supcust_id in ({rows})";

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new { result = "OK", msg = "" });
        }
        public async Task<JsonResult> BatchSetLocation([FromBody] dynamic data)
        {
            
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string sql = "";
            var addr_lng = "";
            var addr_lat = "";
            var msg = "";
            //List<ExpandoObject> dat = null;
            if (data.type == "勾选")
            {
                foreach (var id in data.rows)
                {
                    cmd.CommandText = $" select sup_addr from info_supcust where  company_id ={companyID} AND supcust_id ={id} ";
                    var addr = await cmd.ExecuteScalarAsync();
                    cmd.CommandText = $" select sup_name from info_supcust where  company_id ={companyID} AND supcust_id ={id} ";
                    var name = await cmd.ExecuteScalarAsync();
                    if (addr == null || addr == DBNull.Value)
                    {
                        msg += $"{name} ";
                    }
                    else
                    {
                        var pos = await CommonTool.GetPositionByAddr(_httpClientFactory,addr.ToString()+name);
                        if (pos.result != null)
                        {
                            addr_lng = pos.result.location.lng;
                            addr_lat = pos.result.location.lat;
                            sql = $"UPDATE info_supcust SET addr_lng = '{addr_lng}' , addr_lat = '{addr_lat}',addr_lnglat=(select st_setsrid(st_makepoint(('{addr_lng}')::double precision, ('{addr_lat}')::double precision), 4326) ) where company_id ={companyID} AND supcust_id ={id}";
                            cmd.CommandText = sql;
                            await cmd.ExecuteNonQueryAsync();
                        }
                        else
                        {
                            msg += $"{name} ";

                        }
                    }
                }
            }
            else if (data.type == "全部")
            {
               
                sql = $" select supcust_id from info_supcust where  company_id ={companyID}  ";
                var supcust_id = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                foreach (dynamic i  in supcust_id)
                {
                    var id = i.supcust_id;
                    cmd.CommandText = $" select sup_addr from info_supcust where  company_id ={companyID} AND supcust_id ={id} ";
                    var addr = await cmd.ExecuteScalarAsync();
                    cmd.CommandText = $" select sup_name from info_supcust where  company_id ={companyID} AND supcust_id ={id} ";
                    var name = await cmd.ExecuteScalarAsync();
                    if (addr == null)
                    {
                        msg += $"{name}";
                    }
                    else
                    {
                        var pos = await CommonTool.GetPositionByAddr(_httpClientFactory,addr.ToString() + name);
                        if (pos.result != null)
                        {
                            addr_lng = pos.result.location.lng;
                            addr_lat = pos.result.location.lat;
                            sql = $"UPDATE info_supcust SET addr_lng = '{addr_lng}' , addr_lat = '{addr_lat}',addr_lnglat=(select st_setsrid(st_makepoint(('{addr_lng}')::double precision, ('{addr_lat}')::double precision), 4326) ) where company_id ={companyID} AND supcust_id ={id}";
                            cmd.CommandText = sql;
                            await cmd.ExecuteNonQueryAsync();
                        }
                        else
                        {
                            msg += $"{name}";

                        }
                    }
                }
            }

            if (msg != "") return Json(new { msg = $"{msg} 地址错误" });
            //if (msg != "") throw new MyException($"{msg}");
            else return Json(new { result = "OK" });
        }
        [HttpPost]

        public async Task<JsonResult> BatchSetStatus([FromBody] dynamic data)
        {

            ClientsViewModel model = new ClientsViewModel(cmd);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string status = data.status;
            string rows = string.Join(",", data.rows);
            string sql = "";

            if (status == "停用" )
            {
                string msg = "";
                List<ExpandoObject> list = await CDbDealer.GetRecordsFromSQLAsync($"select supcust_id from arrears_balance where company_id = {companyID} and supcust_id in ({rows} ) and abs(balance)>0.01", cmd);
                List<ExpandoObject> arrear_list = new List<ExpandoObject>();
                List<ExpandoObject> prepay_list = new List<ExpandoObject>();
                if (list.Count() > 0)
                {  
                    msg = "客户有欠款,不能停用\n";
                    string arrear_rows = list.Select(item => (item as IDictionary<string, object>)["supcust_id"]).OfType<string>().Aggregate((current, next) => current + "," + next);
                    //Console.WriteLine(arrear_rows);
                    arrear_list = await CDbDealer.GetRecordsFromSQLAsync($"select supcust_no,sup_name from info_supcust where company_id = {companyID} and supcust_id in ({arrear_rows} )", cmd);
                    //Console.WriteLine(arrear_list);
                    
                }
                list = await CDbDealer.GetRecordsFromSQLAsync($"select supcust_id from prepay_balance where company_id = {companyID} and supcust_id in ({rows}) and abs(balance)>0.01", cmd);
                if (list.Count() > 0)
                {
                    msg += "客户有预收款,不能停用\n";
                    string prepay_rows = list.Select(item => (item as IDictionary<string, object>)["supcust_id"]).OfType<string>().Aggregate((current, next) => current + "," + next);
                    prepay_list = await CDbDealer.GetRecordsFromSQLAsync($"select supcust_no,sup_name from info_supcust where company_id = {companyID} and supcust_id in ({prepay_rows} )", cmd); 
                }
                if (msg != "")
                {
                    return new JsonResult(new { result = "Error", msg, arrear_list , prepay_list });
                    //return new JsonResult(new { result = "Error", msg });
                }
            }


            if (status == "正常")
                sql = $"UPDATE info_supcust SET status = '1' WHERE company_id ={companyID} AND supcust_id in  ({rows} )";
            else if (status == "停用")
            {
                sql = $"UPDATE info_supcust SET status = '0' WHERE company_id ={companyID} AND supcust_id in  ({rows} )";
            }

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return Json(new { result = "OK", msg = "" });
        }


        public async Task<JsonResult> BatchDelete([FromBody] dynamic data)
        {
            ClientsViewModel model = new ClientsViewModel(cmd);
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            var msg = "";
            var sup_name = "";

            if (data.type == "勾选")
            {

                ////2.加载已使用客户
                #region
                string sql = "";
                Dictionary<string, string> dicUsedSupcust = new Dictionary<string, string>();
                CMySbDataReader dr;
                SQLQueue QQ = new SQLQueue(cmd);
                sql = @$"
	        select t.supcust_id,sup_name
	        from (
	        select supcust_id from sheet_sale_main where  company_id={Token.CompanyID}
	        union 
	        select supcust_id from sheet_get_arrears_main where  company_id = {Token.CompanyID}
			        union 
             select supcust_id from sheet_prepay where  company_id = {Token.CompanyID}
			             union 
             select supcust_id from sheet_buy_main where  company_id ={Token.CompanyID} 
			            union 
            select supcust_id from sheet_visit where  company_id = {Token.CompanyID} 
            ) t
             LEFT JOIN info_supcust sc on t.supcust_id =sc.supcust_id and sc.company_id ={Token.CompanyID}
         where sup_name is not null

        ;";
                QQ.Enqueue("usedSupcust", sql);
                dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    string sqlName = QQ.Dequeue();
                    if (sqlName == "usedSupcust")
                    {
                        var lstUsedSuocust = CDbDealer.GetRecordsFromDr(dr, false);
                        foreach (dynamic b in lstUsedSuocust)
                        {
                            dicUsedSupcust.Add(b.supcust_id, b.sup_name);
                        }

                    }

                }
                #endregion


                foreach (string id in data.rows)
                {

                    var supcust_id = id.ToString();
                    if (dicUsedSupcust.ContainsKey(supcust_id))
                    {
                        sup_name = dicUsedSupcust[supcust_id];
                        msg += $" {sup_name}  <br/>";
                    }



                }
            }

            if (msg != "") return Json(new { msg = $"无法删除 以下客户已使用： <br/>   {msg}" });
            else
            {
                string rows = string.Join(",", data.rows);
                cmd.CommandText = $"delete from info_supcust where  company_id ={companyID} AND supcust_id in  ({rows} )";
                await cmd.ExecuteNonQueryAsync();
                return Json(new { result = "OK" });
            }
        }

    }
}