﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.YingJiangBackstage.Pojo;
using ArtisanManage.YingjiangMessage.Pojo;
using ArtisanManage.YingjiangMessage.Services;

namespace ArtisanManage.AppController.Sheets
{
    [Route("AppApi/[controller]/[action]")]
    public class AppSheetDisplayAgreement : QueryController
    { 
        public AppSheetDisplayAgreement(CMySbCommand cmd)
        {
            this.cmd = cmd;

            
        }


        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SheetDisplayAgreement sheet = new SheetDisplayAgreement(LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet });
        }


        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic sheet)
        {
            var currentTime = DateTime.Now.ToText();
            sheet.sheet_type = SHEET_TYPE.SHEET_DISPLAY_AGREEMENT;
            sheet.Init();
            string msg = await sheet.SaveAndApprove(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, currentTime });
        }

        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string result = "OK"; string msg = null;
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            try
            {
                var currentTime = DateTime.Now.ToText();
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                SheetDisplayAgreement sheet = new SheetDisplayAgreement(LOAD_PURPOSE.SHOW);
                msg = await sheet.Red(cmd, companyID, sheetID, operID,"");
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }
        }
       
        [HttpGet]
        public async Task<JsonResult> GetFeeOutSubForKS(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $@"
select sub_id as v,sub_name as l,py_str as z 
from cw_subject 
where company_id = {companyID} 
  and sub_type = 'ZC' and sub_code > 100  
  and sub_id not in (select case when (s.setting->>'feeOutSubForKS')::int is null then -1 else (s.setting->>'feeOutSubForKS')::int end sub_id from  company_setting s where s.company_id = {companyID}) limit 100
";
            dynamic feeOutList = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, feeOutList });
        }
        [HttpPost]
        public async Task<JsonResult> UpdateDisplaySignActionReview([FromBody] dynamic data)
        {
            string result = "OK"; 
            string msg = "";
            try
            {
                var currentTime = DateTime.Now.ToText();
                string operKey = data.operKey;
                bool review_refused = data.review_refused;
                string review_comment = data.review_comment;
                string sheet_id = data.sheet_id;
                string msgId = data.msgId;
                string receiverId = data.receiverId;
                string supName = data.supName;
                string sheetNo = data.sheetNo;
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                string sql = @$"UPDATE display_agreement_main SET reviewer = {operID}, review_time = '{currentTime}', review_comment = '{review_comment}', review_refused = {review_refused} WHERE company_id = {companyID} AND sheet_id = '{sheet_id}' RETURNING sheet_id";
                dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                string sheetId = "";
                if (rec != null)
                {
                    sheetId = rec.sheet_id;
                }
               
                Dictionary<string, dynamic> messageResult = await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey, 
                    msgId,
                    sheetID = sheet_id,
                    msgClass = MessageType.ClassType.Todo,
                    msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                    msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplaySignSubType.SubTypeKey
                }, cmd);

                if (!messageResult.ContainsKey("errMessage"))
                {
                    if (review_refused)
                    {
                        // 拒绝
                        await MessageCreateServices.CreateMessageService(new
                        {
                            operKey = operKey,
                            createrId = operID,
                            msgClass = MessageType.ClassType.ProofPolish,
                            msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                            msgSubType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplaySignSubType.SubTypeKey,
                            sheetID = sheet_id,
                            receiverId,
                            msgTitle = @$"{supName} 陈列单据 {sheetNo} 签约复核未通过，请尽快重新上传",
                        }, cmd);
                    }
                    else
                    {
                        // 通知到当时的创建单据的业务员
                        await MessageCreateServices.CreateMessageService(new
                        {
                            operKey,
                            createrId = operID,
                            msgClass = MessageType.ClassType.Notice,
                            msgType = MessageType.NoticeMessageType.CommonNotice.NoticeType,
                            msgSubType =MessageType.NoticeMessageType.CommonNotice.NoticeSubType.CommonNoticeSubType.SubTypeKey,
                            receiverId,
                            msgTitle = @$"{supName} 陈列单据 {sheetNo} 签约复核已通过",
                        }, cmd);
                    }
                }
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, currentTime, sheetId, operID });
            }
            catch (Exception e)
            {
                result = "Error";
                msg = "复核失败";
                return new JsonResult(new { result, msg });
            }
        }


        /// <summary>
        /// 获取当前用户生效的陈列协议，前端筛选出模板进行限制
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> GetCurrentDisplayList([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            string supcust_id = data.supcust_id;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string sql = $@"
select
        idt.disp_template_id,
        dam.sheet_id,
       dam.sheet_no,
       dam.start_time,
       dam.end_time,
       dam.supcust_id,
       idt.disp_template_name
from display_agreement_main dam
left join info_display_template idt on idt.company_id = dam.company_id and idt.disp_template_id = dam.disp_template_id
where dam.company_id = {companyID} and supcust_id = '{supcust_id}' 
  and date_trunc('month', dam.end_time)::date >= date_trunc('month', now())::date and red_flag is null
";
            List<ExpandoObject> list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return Json(new ResultUtil<dynamic>().CommonResult(0, "success", list));
        }
    }
}
