﻿@page
@model ArtisanManage.Pages.BaseInfo.CommissionDetailModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <link href="~/css/component.css" rel="stylesheet" />

    <script src="~/js/commission.js?v=@Html.Raw(Model.Version)"></script>
    <script src="~/MiniJsLib/MiniJsLibPC.js"></script>
    <script type="text/javascript">
        var infos = [];
        function showDetail(i) {
            app.dialog.row = i;
            app.dialog.Show();
        }
    	var newCount = 1;
    	var itemSource = {};
        window.g_operKey = '@Html.Raw(Model.OperKey)';

        app = new App('@Model.OperKey', {
            setup() {
                this.addServices(['Table', 'Dialog', 'Validator', 'ProcessBar']);
            },
        })
        app.ready(function () {
            @Html.Raw(Model.m_showFormScript);
            @Html.Raw(Model.m_createGridScript);

            app.useDialog({
                 id: 'dialog',
                class: 'fix dialog',
                title: '提成细则',
                '.': {
                    click: {
                        itemName() {
                            var seller = $('#workerID').jqxInput('val');
                            var startDay = $('#startDay').jqxDateTimeInput('val');
                            var endDay = $('#endDay').jqxDateTimeInput('val');
                            var job = $('#job').jqxInput('val');
                            var job_id = '', job_name = '';
                            if (job && job.value == "sender") {
                                job_id = 'senders_id'
                                job_name = 'senders_name'
                            }
                            else {
                                job_id = 'seller_id'
                                job_name = 'seller_name'
                            }
                            var url = `/Report/SalesSummaryByItem?page=salesSummaryByItem&item_name=${this.textContent.trim()}&item_id=${this.id}
    &startDay=${startDay}&endDay=${endDay}&${job_id}=${seller.value}&${job_name}=${seller.label}`;
                            parent.newTabPage('销售汇总(按商品)', url);
                        }
                    }
                },
                beforeShow() {
                    var rowData = infos[this.row];
                    console.log(rowData);
                    app.link('.employeeList', '#employees', content => {
                        content = '`' + content + '`';
                        var trs = rowData.infos.map(data => {
                            //return new Array(10).join(eval(content));
                            return eval(content);
                        });
                        return trs.join('');
                    });
                    var tr = `<tr><td class='left'>${rowData.name}</td><td class='right'>${rowData.amount_x_str}</td><td class='right'>${rowData.quantity_x_str}</td><td class='two-words'></td><td class='right'>${rowData.amount_t_str}</td><td class='right'>${rowData.quantity_t_str}</td><td class='two-words'><td class='two-words'><td class='two-words'></tr>`;
                    $('#sum').html(tr);
                    $('#result').html(`计算结果：${rowData.commission_x_str || 0} - ${rowData.commission_t_str || 0} = ${rowData.commission_str || 0}`);
                    var formula = `<p>销:${rowData.formulaDisplay_x || ""}</p><p>退:${rowData.formulaDisplay_t||""}</p>`;
                    $('#formula').html(formula);
                }
            });

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            var workerID = $('#workerID').jqxInput('val');
            if (workerID) {
                QueryData(null,data => {
                    console.log(data);
                });
            }
        });

       function dealFormData(data) {
            var forGetter = false;
            if (window.g_companySetting && window.g_companySetting.commissionForMoneyGetter) {
                var forGetter = window.g_companySetting.commissionForMoneyGetter
                if (forGetter.toString().toLowerCase() == "true") {
                    forGetter = true;
                }
            }
           // data.forMoneyGetter=forGetter
        }
    </script>
    <style>
        table {
            border-collapse: collapse;
        }
        .dialog_container th, .dialog_container td {
            border: 1px #ddd solid;
            height: 30px;
            font-weight: normal;
        }
        .left{
            width:360px;
            padding-left:5px;
        }
        .right {
            text-align: right;
            width: 100px;
            padding-right:5px;
        }
        .two-words{
            width:60px;
        }
        .itemName{
            color:#49f;
            cursor:pointer;
        }
        th.right {
            text-align: center;
        }
        .tableContainer{
            overflow-y:auto;
            height:70%;
        }
        .dialog_body {
            display: flex;
            flex-flow: column;
            overflow-y: hidden !important;
        }
        .dialog_foot {
            display: none !important;
        }
        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;
        }
        .dialog{
            height:90% !important;
        }
        ::-webkit-scrollbar-track {
            background-color: #fff;
        }
        ::-webkit-scrollbar-thumb {
            border-radius: 7px;         
            -webkit-box-shadow: inset 0 0 0px rgba(0,0,0,.3);
            background-color: #ebebeb;
        }
        ::-webkit-scrollbar-corner {
            background-color: black;
        }
    </style>
</head>

<body style="overflow:hidden">
    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail">
            <div style="float:none;height:0px; clear:both;"></div>
            <div style="display: flex;width:260px;"><label class="label_name">商品</label> <div id="item_id" style="width:200px;" class="label_content"></div></div>
        </div>

        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

    <script type="text/template" id="dialogTmpl">
        <div style="flex:none">
            <p id="result"></p>
            <div id="formula"></div>
            <table>
                <thead>
                    <tr>
                        <th rowspan="2" class="left">商品名称</th>
                        <th colspan="3">销售</th>
                        <th colspan="3">退货</th>
                        <th class="two-words" rowspan="2">欠款</th>
                        <th class="two-words" rowspan="2">收欠</th>
                    </tr>
                    <tr>
                        <th class="right">金额</th>
                        <th class="right">数量</th>
                        <th  class="two-words">赠送</th> 
                        <th class="right">金额</th>
                        <th class="right">数量</th>
                        <th class="two-words">赠送</th>
                       
                    </tr>
                </thead>
            </table>
            <table id="sum"></table>
        </div>
        <div class="tableContainer">
            <table>
                <tbody class="employeeList"></tbody>
            </table>
        </div>
    </script>
    <script type="text/template" id="employees">
        <tr>
            <td class="left itemName" id="${data.item_id}">${data.item_name}</td>
            <td class="right">${toMoney(data.x_amount || "")}</td>
            <td class="right">${toMoney(data.x_quantity || data.x_gift_quantity|| '')}</td>
            <td  class="two-words">${data.x_gift_quantity?'是':''}</td> 
            <td class="right">${toMoney(data.t_amount || "")}</td>
            <td class="right">${toMoney(data.t_quantity || data.t_gift_quantity || '')}</td>
            <td  class="two-words">${data.t_gift_quantity?'是':''}</td>
            <th class="two-words" >${data.is_arrears?'是':''}</th> 
            <th class="two-words" >${data.fromGetArrears?'是':''}</th> 
        </tr>
    </script>
</body>
</html>