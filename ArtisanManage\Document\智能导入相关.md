## ArtisanManage.Services.Gpt4oService

一次请求，直接解析图片

### Class Gpt4o

apiKey为孙海军提供，api标准与openai一致

`_httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", "Bearer " + apiKey);`

### TableRecognization() 向gpt发送解析任务请求

请求地址

`string apiUrl = "https://rps.onedollargpt.com:18101/v1/chat/completions";`

requestContent参数可以参考openai的api标准



### ConvertContentJson(string jsonString)

用于将大模型返回的Json转换为华为ocr返回的格式，以兼容原有（kimi也会走该方法）



## ArtisanManage.Services.KimiService

目前逻辑并未调用kimi的大模型服务（效果不如gpt，可以修改）

FileExtract()  ——>  FileContent()  ——>  TableRecognization()

使用kimi的文件解析（ocr）服务以及gpt的大模型

所以有两个apiKey，kimi账号已更换为大相账号

`private const string apiKeyKimi`

`private const string apiKeyGpt`

### FileExtract()

是kimi的文件上传接口，只支持以multipart形式POST以及form-data编码传**文件**（所以移动端会走BuySheet.cshtml.cs中的Base64ToFormFile（）方法将Base64转文件上传），有空间限制和文件数限制，已在FileContent（）中加即用即删

`//DELETE
var deleteRequest = new HttpRequestMessage(HttpMethod.Delete, apiDeleteUrl);
deleteRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", apiKeyKimi);
HttpResponseMessage deleteResponse = await _httpClient.SendAsync(deleteRequest);`

返回FileID，通过fileId向FileContent()获取对应图片的ocr内容

### FileContent()

kimi的文件解析（表格ocr效果还可以）

返回string类型的表格内容，通过*/n*区分单元格（比较抽象的格式，但是大模型可读，由于单元格可能出现粘连，所以代码方式解析不太可行，抛给大模型处理会将没有/n的地方区分开）

### TableRecognization()

与Gpt4o.TableRecognization()类似，注释中有其余模型可选



kimi后续问题及更新可以参考官网文档





## 数据库SQL相关

### 涉及字段

company_setting.setting       (jsonb对象)

key：ocrEngine

无/h：华为ocr；g：gpt；k：kimi

添加：

`UPDATE company_setting SET setting = jsonb_set(setting::jsonb, '{ocrEngine}','"k"', true) WHERE company_id = 831;`

ture表示如没有则增加该属性

修改：

`UPDATE company_setting SET setting = jsonb_set(setting::jsonb, '{ocrEngine}','"g"') WHERE company_id = 831;`

移除：

`update company_setting set setting=setting::jsonb - 'ocrEngine' where company_id=831;`


