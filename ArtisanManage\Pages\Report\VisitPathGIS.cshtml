@page
@model ArtisanManage.VisitPathModel
@{
    Layout = null;
}


<!DOCTYPE html>
<html lang="en">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <!--  <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.aggregates.js"></script>-->
    <link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)">
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>
    <!-- <script type="text/javascript" src="https://api.map.baidu.com/api?v=1.0&type=webgl&ak=@Html.Raw(Model.BaiduKey)"></script>
    <script type="text/javascript" src="~/uploads/templates/yingjiang_lushu.js"></script>
    <script type="text/javascript" src="https://bj.bcebos.com/v1/mapopen/github/BMapGLLib/Lushu/src/Lushu.min.js"></script> -->
    <!--<title><%= htmlWebpackPlugin.options.title %></title>  -->
    <!-- <script src="https://mapv.baidu.com/build/mapv.js"></script>
    <script src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.min.js"></script>-->
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://js.arcgis.com/4.31/esri/themes/dark/main.css" />
    <script src="https://js.arcgis.com/4.31/"></script>
    <script type="module" src="https://js.arcgis.com/map-components/4.31/arcgis-map-components.esm.js"></script>

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        const mapElem = document.querySelector("arcgis-map");

        require(["esri/Map", "esri/views/MapView", "esri/layers/WebTileLayer", "esri/layers/GraphicsLayer", "esri/Graphic", "esri/geometry/Polyline", "esri/symbols/SimpleLineSymbol", "esri/symbols/PictureMarkerSymbol"], function (Map, MapView, WebTileLayer, GraphicsLayer, Graphic, Polyline, SimpleLineSymbol, PictureMarkerSymbol) {
            var that = this
            const tdtLayer = new WebTileLayer({
                urlTemplate: `https://t0.tianditu.gov.cn/DataServer?T=cva_w&x={col}&y={row}&l={level}&tk=b671eb25836b584b3239b754076a49e5`,
                subDomains: ["0", "1", "2", "3", "4", "5", "6", "7"]
            });
            // 天地图矢量底图
            const tdtVectorLayer = new WebTileLayer({
                urlTemplate: `https://t1.tianditu.gov.cn/DataServer?T=vec_w&x={col}&y={row}&l={level}&tk=b671eb25836b584b3239b754076a49e5`,
                subDomains: ["0", "1", "2", "3", "4", "5", "6", "7"]
            });

            const map = new Map({
                layers: [tdtVectorLayer,tdtLayer]
            });

            const view = new MapView({
                container: "viewDiv",
                map: map,
                center: [104.1954, 35.8617],
                zoom: 4
            });

            // 创建 GraphicsLayer 用于添加标记点
            const graphicsLayer = new GraphicsLayer();
            map.add(graphicsLayer);

            // 定义一个标记点
            const point = {
                type: "point", // 几何类型
                longitude: 104.1954, // 经度
                latitude: 35.8617 // 纬度
            };

            // 定义标记点的符号
            const markerSymbol = {
                type: "simple-marker", // 符号类型：简单标记
                color: "blue", // 标记点颜色
                size: "10px", // 标记点大小
                outline: {
                    color: "white", // 边框颜色
                    width: 1 // 边框宽度
                }
            };

            // 创建标记点的 Graphic 对象
            const pointGraphic = new Graphic({
                geometry: point,
                symbol: markerSymbol
            });

            // 将标记点添加到图层
            graphicsLayer.add(pointGraphic);
            $('#set').jqxDropDownTree({ dropDownWidth: 200, dropDownHeight: 250, url: '../api/ClientEdit/GetDataItemOptions?dataItemName=charge_seller&&operKey=' + g_operKey, source: null, checkboxes: false, mumSelectable: true });
            debugger
            $('#set').jqxInput('val', { value: ``, label: `` });
            window.query = function query() {
                GetTrailPoints($("#set").val().value, '2025-01-01', (trailPointVisitInfos) => {
                    trailPointVisitInfos.map(info => {
                        // 定义一个标记点
                        const point = {
                            type: "point", // 几何类型
                            longitude: info.longitude, // 经度
                            latitude: info.latitude // 纬度
                        };

                        // 定义标记点的符号
                        const markerSymbol = {
                            type: "simple-marker", // 符号类型：简单标记
                            color: "blue", // 标记点颜色
                            size: "10px", // 标记点大小
                            outline: {
                                color: "white", // 边框颜色
                                width: 1 // 边框宽度
                            }
                        };

                        // 创建标记点的 Graphic 对象
                        const pointGraphic = new Graphic({
                            geometry: point,
                            symbol: markerSymbol
                        });
                        graphicsLayer.add(pointGraphic);

                    })
                    var points = trailPointVisitInfos.map(info => {
                        return [info.longitude, info.latitude]
                    })
                    console.log(points)
                    const polyline = new Polyline({
                        paths: [
                            points
                        ]
                    });
                    const lineGraphic = new Graphic({
                        geometry: polyline
                        //symbol: arrowLineSymbol
                    });
                    graphicsLayer.add(lineGraphic)
                    // 创建箭头符号（箭头使用一个简单的图片标记符号）
                    const arrowSymbol = new PictureMarkerSymbol({
                        url: "../images/arrow.png", // 箭头图片
                        width: 36, // 箭头的宽度
                        height: 36 // 箭头的高度
                    });


                    points.map((p,index) => {
                     
                        if (index < points.length - 1) {
                            var symbol = new PictureMarkerSymbol({
                                url: "../images/arrow.png", // 箭头图片
                                width: 12, // 箭头的宽度
                                height: 12, // 箭头的高度
                                angle: calculateAngle(p, points[index + 1])
                            });
                            // 添加箭头标记到折线的终点
                            const arrowMarker = new Graphic({
                                geometry: {
                                    type: "point", // 点
                                    longitude: p[0], // 折线终点经度
                                    latitude: p[1] // 折线终点纬度
                                },
                                symbol
                            });
                            graphicsLayer.add(arrowMarker);
                        } 
                    })
                    // 计算箭头角度
                    function calculateAngle(start, end) {
                        const dx = end[0] - start[0];
                        const dy = end[1] - start[1];
                        return Math.atan2(dy, dx) * (180 / Math.PI);
                    }

                    var that = this
                    // 动态移动点
                    const pointSymbol = { type: "simple-marker", color: [0, 255, 0], size: "12px" };
                    const movingPoint = new Graphic({
                        geometry: { type: "point", longitude: points[0][0][0], latitude: points[0][0][1] },
                        symbol: pointSymbol
                    });
                    graphicsLayer.add(movingPoint);
                    const path = polyline.paths[0]; // 获取轨迹路径
                    let currentIndex = 0;
                    // 贝塞尔曲线插值：生成平滑的点
                    function getBezierPoints(p0, p1, p2, numPoints = 100) {
                        let points = [];
                        for (let t = 0; t <= 1; t += 1 / numPoints) {
                            const x = (1 - t) * (1 - t) * p0[0] + 2 * (1 - t) * t * p1[0] + t * t * p2[0];
                            const y = (1 - t) * (1 - t) * p0[1] + 2 * (1 - t) * t * p1[1] + t * t * p2[1];
                            points.push([x, y]);
                        }
                        return points;
                    }
                    var loopIndex = 0
                    let bezierPoints = []
                    var rounds = Number(path.length / 3).toFixed(0)
                    var leftPointsLength = (rounds + 1) * 3 - path.length
                    //1 2 3 4

                    while (loopIndex < path.length - 2 ) {
                        bezierPoints = bezierPoints.concat(getBezierPoints(path[loopIndex], path[loopIndex + 1], path[loopIndex + 2], 200)); // 生成200个平滑点
                        loopIndex += 2
                    }
                    console.log(bezierPoints)
                    /** 
                    var lastPoint = bezierPoints[bezierPoints.length -1]
                    for (i = 0; i < leftPointsLength; i++) {
                        bezierPoints.push(bezierPoints[lastPoint])
                    }*/
                    // 获取轨迹路径的平滑点
                    function moveMarker() {
                        if (currentIndex < bezierPoints.length - 1) {
                            currentIndex++; // 移动到下一个点
                            // 更新标记的位置
                            movingPoint.geometry = {
                                type: "point",
                                longitude: bezierPoints[currentIndex][0],
                                latitude: bezierPoints[currentIndex][1]
                            };

                            // 更新标记的位置（刷新图层）
                            //that.refresh();
                        } else {
                            clearInterval(moveInterval); // 动画结束
                        }
                    }

                    // 启动移动标记的定时器
                    const moveInterval = setInterval(moveMarker, 20);
                })
            }
            function GetTrailPoints(sellerID, calendarDateStr, cb) {
                $.ajax({
                    url: `/api/VisitPath/GetSellerTrailPoints`,
                    data: {
                        "querySellerID": sellerID,
                        "queryDate": calendarDateStr,
                        "operKey": "@Html.Raw(Model.OperKey)"
                    },
                    success: (res) => {
                        const trailPointVisitInfos = res.data
                        console.log(res.data)
                        if (trailPointVisitInfos.length === 0) {
                            return
                        } else {
                            cb(trailPointVisitInfos)
                        }
                    }
                })
            }
        });

        


    </script>
    <style>
        html,
        body {
            margin: 0;
        }

        #viewDiv{
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
            position:absolute;
            top:40px;
        }

        #set{
            z-index:1
        }
    </style>
</head>
<body>
    <div>
        <div id="set"> </div>
        <button  onclick="window.query()">查询</button>
    </div>

    <div id="viewDiv"></div>
</body>

</html>
