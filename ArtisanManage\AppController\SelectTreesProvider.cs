﻿using ArtisanManage.Models;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
 
using static ArtisanManage.Services.CommonTool;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace ArtisanManage.AppController
{
    ///商品
    ///商品类别
    ///商品档案
    ///单个商品详情
    ///保存商品详情修改



    /// <summary>
    /// 商品
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class SelectTreesProvider : QueryController
    {
        public static string ToFieldValue(dynamic d)
        {
            string fld = (string) d;
            return String.IsNullOrEmpty(fld) ? "null" : $"'{fld}'";
        }
        private readonly IHttpClientFactory _httpClientFactory;
        public SelectTreesProvider(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }


        /// <summary>
        /// 获取品牌ID,名称
        /// </summary>
        /// <param name="operKey"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetTreesForSelect(string operKey,string target, string departID, bool bViewSelf, string operRegions, string brandIDs, string branchID, bool bGetClassStock)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID);
            if (target == "region")
            {
                return await GetRegions(companyID, operID, departID, bViewSelf, operRegions);
            }
            else if (target == "class")
            {
                return await GetClasses(companyID, brandIDs, branchID, bGetClassStock);
            }
            else if (target == "department")
            {
                return await GetDepartment(companyID, departID);
            }
            //else if (target == "worker")
            //{
            //    return await GetWorkers(companyID, operID, departID, bViewSelf);
            //}
            else return null;          
            
        }

        public  async Task<JsonResult> GetDepartment(string companyID, string departID)
        {
            string departSql = $"select depart_id as id,depart_name as name,mother_id from info_department where company_id= {companyID}";
            List<Department> departments = await CDbDealer.GetRecordsFromSQLAsync<Department>(departSql, cmd);
            var department = departments.ToTree1();
            List<Department> data = new List<Department>();
            data.Add(department);
            if (departID.IsValid())
            {
                int departIdTemp = Convert.ToInt32(departID);
                int[] arr = new int[] { departIdTemp };
                if (arr.Length > 0)
                    department.Filter(arr);
            }
            string result = "OK";
            string msg = "";
            return new JsonResult(new { result, msg, data });
        }

        

        public async Task<dynamic> GetRegions(string companyID,string operID, string departID, bool bViewSelf, string operRegions)
        {
            var condi = "";
            if (bViewSelf) condi += $" and oper_id = {operID}";
            if (departID != null) condi += $" and depart_id  = {departID}";
            string regionsql = @$"
SELECT region_name as name,
       region_id as id,
       mother_id as mother_id 
FROM info_region
    where company_id={companyID} order by order_index , region_id";
            List<Region> regions = await CDbDealer.GetRecordsFromSQLAsync<Region>(regionsql, cmd);
            var region = regions.ToTree1();
            List<Region> data = new List<Region>();
            data.Add(region);
            if (operRegions.IsValid())
            {
                var regionIds = JsonConvert.DeserializeObject<int[]>(operRegions);
                if (regionIds.Length > 0)
                    region.Filter(regionIds);
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
            //return region;
        }
        public async Task<dynamic> GetClasses(string companyID, string brandIDs, string branchID, bool bGetClassStock)
        {
            string condi = $"";
            if (brandIDs != null && brandIDs != "")
            {
                condi += $" and (ib.brand_id is null OR ib.brand_id in ({brandIDs})) ";
            }
            //var sql = @$"select class_id as id,class_name as name,mother_id,brand_id from info_item_class where company_id = {company_id};";
            SQLQueue QQ = new SQLQueue(cmd);

            var sql = @$"
SELECT
	iic.class_id AS id,
	iic.class_name AS name,
	iic.mother_id,
	ib.brand_id,
	ib.brand_name 
FROM
	info_item_class iic
	LEFT JOIN info_item_brand ib ON ib.company_id = iic.company_id and iic.brand_id = ib.brand_id 
WHERE
	iic.company_id = {companyID}
	AND ( cls_status IS NULL OR cls_status = '1' ) 
    {condi}
ORDER BY
	order_index,
	class_name,
	class_id;";
            //var list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            QQ.Enqueue("class", sql);


            if (bGetClassStock && branchID != null)
            {
                sql = $@"
select distinct item_class class_id from stock
left join info_item_prop ip on stock.company_id = ip.company_id and stock.item_id = ip.item_id
where stock.company_id = {companyID} and stock.branch_id={branchID} and item_class is not null and (status= '1' or status is null) and stock_qty> 0;";
                QQ.Enqueue("hasStockClass", sql);
            }
            List<TreeGetItemClass> list = null;
            List<ExpandoObject> hasStockClass = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tb = QQ.Dequeue();
                if (tb == "class")
                {
                    list = CDbDealer.GetRecordsFromDr<TreeGetItemClass>(dr, false);
                }
                else if (tb == "hasStockClass")
                {
                    hasStockClass = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            if (hasStockClass != null)
            {
                foreach (dynamic cls in hasStockClass)
                {
                    TreeGetItemClass t = list.Find(row => cls.class_id == row.Id.ToString());
                    if (t != null)
                    {
                        t.hasStock = true;
                        for (int i = 0; i < 4; i++)
                        {
                            t = list.Find(row => t.Mother_Id == row.Id);
                            if (t == null) break;
                            t.hasStock = true;
                        }
                    }
                }
            }

            //var arr = JsonConvert.DeserializeObject<List<Tree>>(JsonConvert.SerializeObject(list));
            var data = CommonTool.ToTreeGetItemClass(list);

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, hasStockClass });
        }
        //public async Task<JsonResult> GetSellers(string companyID,string operID, string departID, bool bViewSelf)
        //{ 
        //    var condi = "";
        //    if (bViewSelf) condi += $" and oper_id = {operID}";
        //    if (departID != null) condi += $" and depart_id  = {departID}";
        //    var sql = $"SELECT oper_id id,oper_name as name FROM info_operator where company_id = {companyID} {condi} and is_seller and COALESCE(status,'1')='1'";
        //    List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
        //    string result = "OK";
        //    string msg = "";
        //    return Json(new { result, msg, data });
        //}

        //public async Task<JsonResult> GetWorkers(string companyID, string operID, string departID, bool bViewSelf)
        //{
        //    var condi = "";
        //    if (bViewSelf) condi += $" and oper_id = {operID}";
        //    if (departID != null) condi += $" and depart_id  = {departID}";
        //    var sql = $"SELECT oper_id id,oper_name as name FROM info_operator where company_id = {companyID} {condi} and COALESCE(status,'1')='1'";
        //    List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
        //    string result = "OK";
        //    string msg = "";
        //    return Json(new { result, msg, data });
        //}
    }
}
