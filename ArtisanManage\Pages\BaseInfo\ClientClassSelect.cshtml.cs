﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ClientClassSelectModel : PageQueryModel
    {
        public string m_classTreeStr = "";
 
        public ClientClassSelectModel(CMySbCommand cmd) : base(Services.MenuId.Empty)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                 {"other_region",new DataItem(){Title="片区", LikeWrapper="/", CtrlType="jqxTree",MumSelectable=true,GetOptionsOnLoad=true,QueryOnChange=true,CompareOperator="like",
                   SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region"} }
            };
           
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }

    }




    [Route("api/[controller]/[action]")]
    public class ClientClassSelectController : QueryController
    { 
        public ClientClassSelectController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ClientClassSelectModel model = new ClientClassSelectModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            ClientClassSelectModel model = new ClientClassSelectModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

       
    }
}
