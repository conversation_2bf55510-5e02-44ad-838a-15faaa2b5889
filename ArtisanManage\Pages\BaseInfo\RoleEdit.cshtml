@page
@model ArtisanManage.Pages.BaseInfo.RoleEditModel

@{
    Layout = null;

}
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>RoleEdit</title>
    <link href="~/css/flex.css" rel="stylesheet" />
    <script src="~/js/Vue.js"></script>

    <link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>

    <link rel="stylesheet" href="~/ChosenSelect/chosen.css" />
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <style>
        [v-cloak] {
            display: none;
        }
        body {
            font-size: 14px;
            overflow: hidden;
        }

        ul, li {
            list-style: none;
            padding: 0;
        }

        .left {
            width: 300px;
        }

        .table {
            border-width: 1px;
            border-left-width: 0px;
            border-top-width: 0px;
            border-style: solid;
            border-color: #eee;
            border-spacing: 0px 0px;
            width: 100%;
            margin: 0px;
        }

        th, td {
            margin: 0px;
            padding: 0px;
            margin-top: 0px;
            margin-bottom: 0px;
            height: 40px;
            border-width: 0.5px;
            border-style: solid;
            border-color: #eee;
            border-left: none;
            border-left-width: 0px;
            border-top-width: 0.5px;
            border-top: none;
            font-weight: normal;
        }

            td.left {
                padding: 0 1rem;
            }

        .right {
            width: calc((100vw * 0.9 - 300px) / 6);
        }

        .hidden {
            display: none;
        }

        .all:before {
            content: '\a0';
            display: inline-block;
            border: 1px solid silver;
            text-align: center;
            width: 20px;
            height: 20px;
            font-weight: bold;
        }

        .none:before {
            content: '\2713';
            color: #FA8E53;
            display: inline-block;
            border: 1px solid silver;
            text-align: center;
            width: 20px;
            height: 20px;
            font-weight: bold;
        }

        .part:before {
            content: '\1508';
            color: #FA8E53;
            display: inline-block;
            border: 1px solid silver;
            text-align: center;
            width: 20px;
            height: 20px;
            font-weight: bold;
        }

        #root {
            padding: 20px;
            overflow: hidden;
        }

        .ui-selectmenu-list {
            height: 100%;
            width: calc(100% - 70px);
            overflow: auto;
            margin-top: 0px;
        }

        .tab {
            width: 70px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            background-color: #eee;
            font-size: 16px;
            cursor: pointer;
            user-select: none;
        }
        /*
        .tab:hover {
                background-color: #efe;
        }*/

        .active-tab {
            background-color: #fff;
        }

        .magic-checkbox {
            margin-top: -10px;
        }

        .posirelative {
            position: relative;
        }

        .treeName {
            display: inline;
        }
    </style>
    <script>
        var g_operKey = '@Model.OperKey';
    </script>
</head>
<body>
    <div id="root" class="" v-cloak>
        <div class="row-between">
            <label>角色名称：<input v-model="record.role_name" class="magic-input"/></label>
            <!-- <label>描述：<input v-model="record.remark" /></label>--> 
            <button @@click='save' class="main-button" style="width:80px;" :disabled = "!canEdit">保存</button> 
        </div>
        <div style="display:flex;height:calc(100vh - 80px);background-color:#eee;margin-top:10px;padding-left:1px;padding-top:1px;">
            <div style="width:70px;height:100%;">
                <div v-for="tab in tabs" v-bind:class="{'active-tab':tab.id==curTab.id}" v-bind:id="tab.id" v-on:click="onTabClick(tab)" class="tab">
                    {{tab.title}}
                </div>

            </div>
            <ul class="ui-selectmenu-list" style="background-color:#fff;">


                <li v-for="(cls,clsKey) in rights" v-show="curTab.clsKeys.indexOf(clsKey)>=0">
                    <table class="table">
                        <thead>
                            <tr>
                                <th class="left" style="text-align:left;padding-left:10px;">
                                    <input type="checkbox" class="magic-checkbox" v-bind:id="'cls' + clsKey" v-bind:data-clsKey="clsKey" onclick="onClsClick(event)" />
                                    <label style="margin-top: 0px;" v-bind:for="'cls' + clsKey">{{store[clsKey].display}}</label>
                                </th>
                                <th v-if="store[clsKey].titles.length>1" class="right hidden" v-for="title in store[clsKey].titles">
                                    {{title}}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(module,moduleKey) in cls">
                                <td v-if="store[clsKey].subRightsBaseRightType[moduleKey]=='SELECT'" class="left" style="padding-left: 50px;">
                                    <label>{{store[clsKey].subRightsDisplay[moduleKey]}}</label>
                                    <select class="chosen-select-deselect" style="width:150px" data-placeholder="选择..." onchange="console.log('我被改变了')" v-bind:id="'mod_'+clsKey+'_' + moduleKey" v-bind:data-clsKey="clsKey" v-bind:data-moduleKey="moduleKey" v-model="module.value">
                                        <option v-for="(optionTitle,optionID) in store[clsKey].subRightsOptionJson[moduleKey]" :value="optionID">
                                            {{optionTitle}}
                                        </option>
                                    </select>
                                </td>
                               <td v-if="store[clsKey].subRightsBaseRightType[moduleKey]=='INPUT'" class="left" style="padding-left: 50px;">
                                   <label style="margin-top: 0px;margin-right:10px;">{{store[clsKey].subRightsDisplay[moduleKey]}}</label>
                                   <input type="text" v-bind:value="rights[clsKey][moduleKey].value" onchange='inputChange(event)'  v-bind:id="'mod_'+clsKey+'_' + moduleKey" v-bind:data-clsKey="clsKey" v-bind:data-moduleKey="moduleKey"  />
                                   <label style="margin-top: 0px; margin-right: 10px; color: #aaa;">{{store[clsKey].subRightsTips[moduleKey]}}</label>
                                </td>
                                <td v-if="store[clsKey].subRightsBaseRightType[moduleKey]=='CHECK'" class="left" style="padding-left: 50px;">
                                    <input type="checkbox" class="magic-checkbox"  v-bind:id="'mod_'+clsKey+'_' + moduleKey" v-bind:data-clsKey="clsKey" v-bind:data-moduleKey="moduleKey" onclick="onModuleCheckClick(event)" />
                                    <label style="margin-top: 0px;" v-bind:for="'mod_'+clsKey+'_' + moduleKey">{{store[clsKey].subRightsDisplay[moduleKey]}}</label>
                                </td>
                                <!-- <td style="text-align:center;" v-if="Object.keys(module).length>1" v-for="(right,rightKey) in  module"><input type="checkbox" class="magic-checkbox" v-model="module[rightKey]" @@click="onRightClick(cls,clsKey)" v-bind:id="'ck_'+rightKey+'_' + moduleKey" ><label style="margin-top: -10px;" v-bind:for="'ck_'+rightKey+'_' + moduleKey"></label></td>
                                 -->
                                <td
                                    style="text-align:center;" 
                                    v-if="(Object.keys(module).length>=1 && clsKey !== 'delicacy') && store[clsKey].subRightsBaseRightType[moduleKey]=='CHECK'"
                                    v-for="(right,rightKey) in rightsTemplate[clsKey][moduleKey]"
                                >
                                    <input type="checkbox" class="magic-checkbox" v-model="module[rightKey]" @@click="onRightClick(cls,clsKey)" v-bind:id="'ck_'+rightKey+'_' + moduleKey">
                                    <label style="margin-top: -10px;" v-bind:for="'ck_'+rightKey+'_' + moduleKey"> {{ right_titles[rightKey] }} </label>
                                </td>
                            </tr>
                        </tbody>
                    </table>


                </li>
            </ul>
        </div>



    </div>
    <script src="~/ChosenSelect/chosen.jquery.js" type="text/javascript"></script>
    <script>


    </script>
    <script type="text/javascript">
        debugger
        var store = @Html.Raw(Model.store);
        var rights = @Html.Raw(Model.rights);
        var rightsTemplate=@Html.Raw(Model.rightsTemplate);

        var record = {
            role_id:'@Html.Raw(Model.role_id)',
            role_name: '@Html.Raw(Model.DataItems["role_name"].Value)',
            templ_id: '@Html.Raw(Model.DataItems["templ_id"].Value)',
            templ_rights: `@Html.Raw(Model.DataItems["templ_rights"].Value)`,
            remark: '@Html.Raw(Model.remark)',
        };


        var vm = new Vue({
            el: "#root",
            data: {
                // 从`MenusSets.cs`的`enum MenuId`中迁移，添加新权限时更新此项
                right_titles: {
                    'see': '查看',
                    'create': '新建',
                    'make': '制单',
                    'print': '打印',
                    'export': '导出',
                    'edit': '编辑',
                    'red': '红冲',
                    'value': '取值',
                    'delete': '删除',
                    'approve': '审核',
                    'review': '复核',
                    'operate': '操作',
                },
                curTab:  {
                        id:"sheet",title:"单据",clsKeys:'sale,orderFlow,buy,stock,money'
                    },
                tabs: [
                    {
                        id:"sheet",title:"单据",clsKeys:'sale,orderFlow,buy,stock,money'
                    },
                    {
                        id:"info",title:"档案" ,clsKeys:'info'
                    },
                    {
                        id:"report",title:"报表" ,clsKeys:'report'
                    },
                    {
                        id:"accounting",title:"财务",clsKeys:'cwOperate,cwSheet,cwReport'
                    },
                     {
                        id:"setting",title:"设置" ,clsKeys:'setting'
                     },
                     {
                        id:"delicacy",title:"精细化" ,clsKeys:'delicacy'
                     }


                ],
               record: record,
                    rights: rights,rightsTemplate:rightsTemplate,
                    store: store,
                canEdit : false
            },
            mounted() { 
                window.g_operRights =@Html.Raw(Model.JsonOperRightsOrig);
           
                if (window.g_operRights.info.infoRole && window.g_operRights.info.infoRole.edit) { 
                    this.canEdit = true
                }
            },
            methods: {
                onLoad() {
                     for (var clsKey in rights) {
                         var cls = rights[clsKey];
                         for (var moduleKey in cls) {
                             var module = cls[moduleKey];
                             for (var k in module) {
                                 if (module[k] === "true" ) {
                                     module[k]=true
                                 }
                                 else if (module[k] === "false" ) {
                                     module[k]=false
                                 }
                             
                                
                             }
                         }

                    }
                    for (var clsKey in rights) {
                        var cls = rights[clsKey];

                        this.onRightClick(cls, clsKey);

                    }
                    console.log(rights)
                },
                onTabClick(tab) {
                    this.curTab = tab;
                },
                onRightClick(cls, clsKey) {
                    // var module = module[moduleKey];
                    var that = this;
                    setTimeout(function () {
                        that.judgeClassCheckState(cls, clsKey);
                       // onRightClickFunc(module, clsKey, moduleKey);
                    }, 30);
                },
                onRightClickFunc(module, clsKey, moduleKey) {
                        var haveChecked = false;
                        var haveUnchecked = false;
                        for (var k in module) {
                            var state = module[k];
                            if (!state || state == 0) {
                                haveUnchecked = true;
                            }
                            else if (state) haveChecked = true;

                        }
                       var ck = document.all['mod_' + clsKey + '_' + moduleKey];
                     //    var cls = document.all['cls' + clsKey ];
                    if (!ck) return;
                        if (haveChecked && !haveUnchecked) {
                            ck.checked = true;
                            state = "checked";
                            $(ck).prop('indeterminate', false)
                        }
                        else if (!haveChecked && haveUnchecked) {
                            ck.checked = false;
                             state = "unchecked";
                            $(ck).prop('indeterminate', false)
                        }
                        else {
                            //  ck.checked = 'indeterminate';
                            $(ck).prop('indeterminate', true)
                             state = "indeterminate";
                    }

                    return state;

                },
                judgeClassCheckState(cls, clsKey) {
                    var haveChecked = false;
                   var haveUnchecked = false;var haveIndeterminate = false;
                    for(moduleKey in cls){
                        var module = cls[moduleKey];
                        var state = this.onRightClickFunc(module, clsKey, moduleKey);
                        if (state == 'checked') {
                            haveChecked = true;
                        }
                        else if (state == 'unchecked') {
                            haveUnchecked = true;
                        }
                        else if (state == 'indeterminate') {
                            haveIndeterminate = true;
                        }
                    }
                    var ck = document.all['cls' + clsKey ];
                    if (haveIndeterminate) {
                         $(ck).prop('indeterminate', true)
                    }
                    else if (haveChecked && !haveUnchecked) {
                            ck.checked = true;
                            $(ck).prop('indeterminate', false)
                    }
                    else if (!haveChecked && haveUnchecked) {
                        ck.checked = false;
                        $(ck).prop('indeterminate', false)
                    }
                },
                save() {
                    
                    if (!record.role_name) {
                        bw.toast('请输入角色名称');
                        return;
                    }

                    function mergeDefaults(defaults, target) {
                        for (let key in defaults) {
                            if (defaults.hasOwnProperty(key)) {
                                // 如果 target 中不存在该键，直接赋值
                                if (!target.hasOwnProperty(key)) {
                                    target[key] = defaults[key];
                                } else {
                                    // 如果该键是对象，递归合并
                                    if (typeof defaults[key] === 'object' && typeof target[key] === 'object') {
                                        mergeDefaults(defaults[key], target[key]);
                                    }
                                }
                            }
                        }
                    }

                    var defaultRights={
                       "delicacy": {
                           "allowLoginApp": {
                              "value": false
                            }
                        }
                    }
                    mergeDefaults(defaultRights,rights)
                    var data = {
                        operKey: g_operKey,
                        isNewRecord:record.role_id?false:true,
                        role_id: record.role_id,
                        role_name: record.role_name,
                        templ_id:record.templ_id,
                        remark:record.remark,
                        rights: rights,
                    }
                    $.ajax({
                        url: '../api/roleedit/save?operKey=@Model.OperKey',
                        type: 'post',
                        data: JSON.stringify(data),
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        var message = {
                            msgHead: "RoleEdit",
                            action: record.role_id ? 'update' : 'add',
                            record: record
                        };
                        if (res.result == "OK") {
                            window.srcWindow.WaitToReQueryData(500);
                            window.parent.closeTab(window);
                           // window.parent.postMessage(message, '*');
                        } else {
                            bw.toast(res.msg)
                        }



                    });
                }
            }
        });
         $('.chosen-select-deselect').chosen({ allow_single_deselect: true });
        $(function () {
            $('thead').on('click', function () {
              //  $(this).next().toggle();
            });
        });
        vm.onLoad();
        function inputChange(e) {
            console.log(e)
            var clsKey = e.target.getAttribute('data-clsKey');
            var cls = rights[clsKey];
            var moduleKey = e.target.getAttribute('data-moduleKey');
           rights[clsKey][moduleKey].value= e.target.value
        }
        function onModuleCheckClick(e) {
            var state = e.target.checked;
            var clsKey = e.target.getAttribute('data-clsKey');
            console.log('clsKey:', clsKey)
            var cls = rights[clsKey];
            var moduleKey = e.target.getAttribute('data-moduleKey');
            var module = rights[clsKey][moduleKey];
            for (var k in module) {
                if (!state) state = false;
                module[k] = state;
            }
            if (moduleKey == 'seeInPrice' && module.value==false ) { 
                
                var seeProfit = rights[clsKey]['seeProfit']
                seeProfit.value = false
            }
            vm.judgeClassCheckState(cls, clsKey);
        }
        function onClsClick(e) {
            e.stopPropagation();
            var state = e.target.checked;
            var clsKey = e.target.getAttribute('data-clsKey');
            var cls = rights[clsKey];
            var clsRightsType = store[clsKey]["subRightsBaseRightType"];
            for (var ck in cls) {
                var rightType = clsRightsType[ck]
                var module = cls[ck];
                if (rightType == "CHECK") {
                    for (var k in module) {
                        module[k] = state;
                    }
                }
            }
            vm.judgeClassCheckState(cls, clsKey);
        }


    </script>

    <script type="text/javascript" src="~/NiceWidgets/NiceWidgets.js"></script>

</body>
</html>
