﻿using ArtisanManage.CwPages;
using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Pages.CwPages;
using ArtisanManage.Services;
using Microsoft.AspNetCore.SignalR;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.OpenXmlFormats;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.MyCW
{
    public class CwRowVoucher : CwRowBase
    {

        [SaveToDB] [FromFld] public string sub_id { get; set; }
        [FromFld("c.sub_code", (MyJXC.LOAD_PURPOSE)LOAD_PURPOSE.SHOW)] public string sub_code { get; set; }
        [FromFld("concat(sub_code,' ',substring((select string_agg(sub_name,'-' order by sub_code::text) as name from cw_subject where company_id= ~COMPANY_ID and sub_id::text in ( select * from REGEXP_SPLIT_TO_TABLE((select substring(concat(other_sub,sub_id),2) from cw_subject where company_id= ~COMPANY_ID and level>=1 and sub_id=t.sub_id), '/')  ) ),8)) as sub_name", (MyJXC.LOAD_PURPOSE)LOAD_PURPOSE.SHOW)] public string sub_name { get; set; }
        [FromFld((MyJXC.LOAD_PURPOSE)LOAD_PURPOSE.SHOW)] public string direction { get; set; }
        [FromFld("(case direction when 1 then '借' when -1 then '贷' end) as direction_label", (MyJXC.LOAD_PURPOSE)LOAD_PURPOSE.SHOW)] public string direction_label { get; set; }
        [SaveToDB] [FromFld] public string assister1_type { get; set; }
        [SaveToDB] [FromFld] public string assister1_id { get; set; }
        [SaveToDB] [FromFld] public string assister2_type { get; set; }
        [SaveToDB] [FromFld] public string assister2_id { get; set; }
        [FromFld(@"(case when (assister1_id is not null and assister2_id is null) then (case when assister1_type='C' then concat('【客户】',type_sc1.sup_name) when assister1_type='S' then concat('【供应商】',type_sc1.sup_name) when assister1_type='INV' then concat('【商品】',type_inv1.item_name) when assister1_type='DEP' then concat('【部门】',type_dep1.depart_name) when assister1_type='MAN' then concat('【业务员】',type_man1.oper_name) else null end) 
            when (assister1_id is not null and assister2_id is not null) then concat((case when assister1_type='C' then concat('【客户】',type_sc1.sup_name) when assister1_type='S' then concat('【供应商】',type_sc1.sup_name) when assister1_type='INV' then concat('【商品】',type_inv1.item_name) when assister1_type='DEP' then concat('【部门】',type_dep1.depart_name) when assister1_type='MAN' then concat('【业务员】',type_man1.oper_name) else null end),'_',(case when assister2_type='C' then concat('【客户】',type_sc2.sup_name)when assister2_type='S' then concat('【供应商】',type_sc2.sup_name) when assister2_type='INV' then concat('【商品】',type_inv2.item_name) when assister2_type='DEP' then concat('【部门】',type_dep2.depart_name) when assister2_type='MAN' then concat('【业务员】',type_man2.oper_name) else null end)) else '无' end) as assister_types_names", (MyJXC.LOAD_PURPOSE)LOAD_PURPOSE.SHOW)] public string assister_types_names { get; set; }

        public decimal change_amount = 0;
        [SaveToDB][FromFld] public decimal debit_amount { get; set; } = 0;
        [SaveToDB][FromFld] public decimal credit_amount { get; set; } = 0;
        public string business_sheet_id { get; set; }
        public string business_sheet_type { get; set; }


    }
    public class SheetCwVoucher:SheetCwBase<CwRowVoucher>
    {
        [SaveToDB] [FromFld("to_char(t.period,'yyyy-MM-dd') as period")] public string period { get; set; }

        [SaveToDB][FromFld] public bool auto_created { get; set; }
		[FromFld("(sheet_attribute->>'biz_info')::text", (MyJXC.LOAD_PURPOSE)LOAD_PURPOSE.SHOW)] public string biz_info { get; set; }
        [SaveToDB] [FromFld] public string sheet_attribute
        {
            get//保存
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                if (biz_info != null && biz_info!="")
                {
                    sheetAttribute.Add("biz_info", biz_info);
                }

                string s = "";
                if (sheetAttribute.Count > 0) s = JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set//读取
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.biz_info != null)
                    {
                        biz_info = sheetAttr.biz_info.ToString();
                    }
                }
            }
        }

        private void InitSheet()
		{
            if (this.LoadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @"left join (select oper_id,oper_name as maker_name from info_operator) maker on maker.oper_id = t.maker_id 
                                left join (select oper_id,oper_name as approver_name from info_operator) approver on approver.oper_id=t.approver_id
                                left join (
                                        select sheet_id, row->>'biz_sheet_type' as business_sheet_type,
                                            string_agg(concat(row->>'biz_sheet_id',',',row->>'biz_sheet_no'),';') as biz_id_nos,
                                            string_agg(row->>'biz_sheet_no',',') as biz_nos,
                                            string_agg(row->>'biz_make_brief','；') filter (where coalesce(row->>'biz_make_brief','')<>'') as briefs
                                        from (
                                            select sheet_id,(jsonb_array_elements_text((sheet_attribute->>'biz_info')::jsonb))::jsonb as row from cw_voucher_main
                                            where company_id= ~COMPANY_ID and sheet_attribute is not null 
                                        ) tt group by sheet_id,row->>'biz_sheet_type'
                                ) biz on biz.sheet_id=t.sheet_id  ";
                DetailLeftJoin = @"left join (select sub_id as cw_sub_id,sub_name,sub_code,direction from cw_subject where company_id=~company_id) c on c.cw_sub_id = t.sub_id
                                left join info_supcust type_sc1 on type_sc1.company_id=t.company_id and type_sc1.supcust_id=t.assister1_id
                                left join info_item_prop type_inv1 on type_inv1.company_id=t.company_id and type_inv1.item_id=t.assister1_id
                                left join (select ide.depart_id, ide.depart_name, ide.company_id  from info_department ide left join (select company_id,depart_id,count(oper_id) as c from info_operator where company_id= ~COMPANY_ID  and (status = '1' or status is null) group by company_id,depart_id) io on ide.company_id=io.company_id and ide.depart_id=io.depart_id where ide.company_id= ~COMPANY_ID  and io.c>0 ) type_dep1 on type_dep1.company_id=t.company_id and type_dep1.depart_id=t.assister1_id
                                left join info_operator type_man1 on type_man1.company_id=t.company_id and type_man1.oper_id=t.assister1_id

                                left join info_supcust type_sc2 on type_sc2.company_id=t.company_id and type_sc2.supcust_id=t.assister2_id
                                left join info_item_prop  type_inv2 on type_inv2.company_id=t.company_id and type_inv2.item_id=t.assister2_id
                                left join (select ide.depart_id, ide.depart_name, ide.company_id  from info_department ide left join (select company_id,depart_id,count(oper_id) as c from info_operator where company_id= ~COMPANY_ID  and (status = '1' or status is null) group by company_id,depart_id) io on ide.company_id=io.company_id and ide.depart_id=io.depart_id where ide.company_id= ~COMPANY_ID  and io.c>0 ) type_dep2 on type_dep2.company_id=t.company_id and type_dep2.depart_id=t.assister2_id
                                left join info_operator type_man2 on type_man2.company_id=t.company_id and type_man2.oper_id=t.assister2_id";
            }
        }
        public SheetCwVoucher(LOAD_PURPOSE loadPurpose) :base("cw_voucher_main", "cw_voucher_detail", loadPurpose)
        {
            InitSheet();
        }

        public SheetCwVoucher() : base("cw_voucher_main", "cw_voucher_detail", LOAD_PURPOSE.SHOW)
        {
            InitSheet();
        }

        protected class CInfoForApprove : CInfoForApproveBase
        {
            public List<SheetRowMove> SheetRows = null;
            public List<SubInfo> subInfo = null;
        }

        protected class SubInfo
        {
            public string sub_id { get; set; }
            public string sub_code { get; set; }
            public int sub_direction { get; set; }
            public string sub_balance { get; set; }
            public string other_sub { get; set; }
            public bool is_detail { get; set; } //是否是明细科目

        }
        public override void Init()
        {
            base.Init();
            money_inout_flag = 1;
        }

        protected override void InitForGetSaveSQL()
        {
            base.InitForGetSaveSQL();
            period = Convert.ToDateTime(this.happen_time).ToString("yyyy-MM-01");

        }
        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd,  CInfoForApproveBase info)
		{
            string sql = "";
            if (sheet_no != "")
            {
                sql = $"select sheet_no from cw_voucher_main " +
                        $"where company_id={company_id} and to_char(period, 'yyyymm')=to_char('{this.happen_time}'::date, 'yyyymm') and sheet_no={sheet_no} ";
                if (sheet_id != "") sql += $"and sheet_id!={sheet_id}";
                List<ExpandoObject> list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (list.Count > 0)
                    return "本会计期间内字号重复,请更改";
                sql = $"select next_voucher_no from cw_voucher_no where company_id={company_id} and to_char(period, 'yyyymm')=to_char('{this.happen_time}'::date, 'yyyymm');";
                list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (list.Count > 0)
                    sql = $"update cw_voucher_no set next_voucher_no=GREATEST(next_voucher_no, {sheet_no})+1 where company_id={company_id} and to_char(period, 'yyyymm')=to_char('{this.happen_time}'::date, 'yyyymm');";
                else sql = $"insert into cw_voucher_no (company_id,period,next_voucher_no) values ({company_id},to_char('{this.happen_time}'::date, 'yyyy-mm-01')::date,{sheet_no}+1);";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
            return "";
        
        }
        public override async Task<string> Save(CMySbCommand cmd, bool bAutoCommit = true)
        {
            string sql = "";
            if (sheet_no != "")
            {
                sql = $"select sheet_no from cw_voucher_main " +
                        $"where company_id={company_id} and to_char(period, 'yyyymm')=to_char('{this.happen_time}'::date, 'yyyymm') and sheet_no={sheet_no} ";
                if (sheet_id != "") sql += $"and sheet_id!={sheet_id}";
                List<ExpandoObject> list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (list.Count > 0)
                    return "本会计期间内字号重复,请更改";
                sql = $"select next_voucher_no from cw_voucher_no where company_id={company_id} and to_char(period, 'yyyymm')=to_char('{this.happen_time}'::date, 'yyyymm');";
                list = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (list.Count > 0)
                    sql = $"update cw_voucher_no set next_voucher_no=GREATEST(next_voucher_no, {sheet_no})+1 where company_id={company_id} and to_char(period, 'yyyymm')=to_char('{this.happen_time}'::date, 'yyyymm');";
                else sql = $"insert into cw_voucher_no (company_id,period,next_voucher_no) values ({company_id},to_char('{this.happen_time}'::date, 'yyyy-mm-01')::date,{sheet_no}+1);";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
                return await base.Save(cmd, bAutoCommit);
            }
            if (sheet_id=="")
            {
                sql = $"select yj_getnewvoucherno({company_id},'{this.happen_time}')";
                cmd.CommandText = sql;
                object ov = await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value)
                    sheet_no = ov.ToString();
            }
            else//会造成断号
            {
                dynamic vo = await CDbDealer.Get1RecordFromSQLAsync($"select happen_time from cw_voucher_main where sheet_id={sheet_id} and company_id={company_id}", cmd);
                DateTime newHappenTime = Convert.ToDateTime(happen_time);
                DateTime oldHappenTime = Convert.ToDateTime(vo.happen_time);
                if(!(newHappenTime.Year==oldHappenTime.Year && newHappenTime.Month == oldHappenTime.Month))
                {
                    sql = $"select yj_getnewvoucherno({company_id},'{this.happen_time}')";
                    cmd.CommandText = sql;
                    object ov = await cmd.ExecuteScalarAsync();
                    if (ov != null && ov != DBNull.Value)
                        sheet_no = ov.ToString();
                }
            }
            return await base.Save(cmd, bAutoCommit);
        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql;
            base.GetInfoForApprove_SetQQ(QQ);
            if (red_flag != "2" && sheet_id == "" && sheet_no== "") sheet_no = "";
            if (sheet_no == "")
            {
                sql = $"select yj_getnewvoucherno({company_id},'{happen_time}')";
                QQ.Enqueue("sheet_no", sql);
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
            }
            if (OperID != "")
            {
                //sql = $"select rights->'delicacy'->'allowNegativeStock'->'value' role_allow_negative_stock from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
                //QQ.Enqueue("role_allow_negative_stock", sql);
            }
            if (SheetRows.Count > 0)
            {
                var dt = Convert.ToDateTime(happen_time);
                var period = dt.Year.ToString() + '-' + dt.Month.ToString() + '-' + "01";
                string subs_id = "";
                foreach (CwRowVoucher row in SheetRows)
                {
                    if (subs_id != "") subs_id += ",";
                    subs_id += row.sub_id;
                }
                //sub_balance可能不需要？
                sql = $@"select cs.sub_id,cs.sub_code,cs.sub_name,cs.direction sub_direction,csb.balance sub_balance,concat(cs.other_sub,cs.sub_id) other_sub,(case when cs2.mother_id is null then true else false end) as is_detail from cw_subject cs
                    left join ( select sub_id,balance from cw_sub_balance where company_id = {company_id} and period = '{period}' and sub_id in ({subs_id}) ) csb on cs.sub_id=csb.sub_id
                    left join ( select company_id,mother_id,count(mother_id) as sons from cw_subject group by company_id,mother_id) cs2 on cs.company_id=cs2.company_id and cs.sub_id=cs2.mother_id
                    where cs.company_id={company_id} and cs.sub_code!=0 and 
                    (
	                    cs.sub_id in ({subs_id})  ";
                foreach (CwRowVoucher row in SheetRows)
                {
                    sql += $@"or cs.sub_id::text in ( select * from REGEXP_SPLIT_TO_TABLE((select substring(other_sub,2,length(other_sub)-2) from cw_subject where company_id={company_id} and sub_id={row.sub_id}), '/') )";
                }
                sql += ")";
                QQ.Enqueue("subInfo", sql);
                
            }

        }

        
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd=null)
        {
            await base.CheckSheetValid();
            decimal debitTotal = 0;
            decimal creditTotal = 0;
            foreach (var row in SheetRows) {
                debitTotal += row.debit_amount;
                creditTotal += row.credit_amount;
                if (row.debit_amount == 0 && row.credit_amount == 0) return "存在没有金额的行";
            }
            if (Math.Abs(debitTotal - creditTotal) >= (decimal)0.01) return "借贷不平衡";
            return "OK";
        }
   
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (sqlName == "sheet_no")
            {
                if (dr.Read())
                {
                    object ov = dr[0];
                    if (ov != DBNull.Value) sheet_no = ov.ToString();
                }
            }
            if (sqlName == "subInfo")
            {
                info.subInfo = CDbDealer.GetRecordsFromDr<SubInfo>(dr);
            }

        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1, VoucherOperType operType)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
            await base.OnSheetIDGot(cmd, sheetID, info, operType);
            string sql = "";
            string period = Convert.ToDateTime(happen_time).ToString("yyyy-MM-01");
            if (operType == VoucherOperType.SaveAndApprove || operType== VoucherOperType.Approve || operType== VoucherOperType.Red || operType== VoucherOperType.CancelApprove)
            {
                foreach (var row in SheetRows)
                {
                    List<SubInfo> thisSubInfo = info.subInfo.Where(x => x.sub_id.ToString() == row.sub_id).ToList();
                    if (thisSubInfo.Count() == 1)
                    {
                        string[] subs = thisSubInfo[0].other_sub.Split('/');
                        string[] newSubArr = subs.Skip(2).Take(subs.Length - 2).ToArray();//othersub 去除第一级全部，到当前级
                        List<SubInfo> setSubInfo = info.subInfo.Where(x => newSubArr.Contains(x.sub_id.ToString())).ToList();
                        foreach (SubInfo sub in setSubInfo)
                        {
                            /////decimal changeBal = CPubVars.ToDecimal(row.change_amount) * sub.sub_direction;
                            decimal changeBal= (row.debit_amount!=0? row.debit_amount: -row.credit_amount) * sub.sub_direction;
                            decimal debitBal = row.debit_amount;
                            decimal creditBal = row.credit_amount;
                            if (operType == VoucherOperType.CancelApprove)
                            {
                                changeBal = -changeBal;
                                debitBal = -debitBal;
                                creditBal = -creditBal;
                            }
                            sql += $"insert into cw_sub_balance(company_id,sub_id,period,balance,debit_amount,credit_amount) values ({company_id},{sub.sub_id},'{period}',{changeBal},{debitBal},{creditBal}) on conflict(company_id,sub_id,period) do update set balance = coalesce(cw_sub_balance.balance,0) + ({changeBal}),debit_amount=coalesce(cw_sub_balance.debit_amount,0)+({debitBal}),credit_amount=coalesce(cw_sub_balance.credit_amount,0)+({creditBal});";

                            //辅助核算保存余额：只保存明细
                            if (sub.is_detail)
                            {
                                if (row.assister1_type.IsValid() && row.assister1_id.IsValid())
                                {
                                    sql += $"insert into cw_sub_balance_assister(company_id,sub_id,period,assister_type,assister_id,balance,debit_amount,credit_amount) values ({company_id},{sub.sub_id},'{period}','{row.assister1_type}',{row.assister1_id},{changeBal},{debitBal},{creditBal}) on conflict(company_id,sub_id,period,assister_type,assister_id) do update set balance = coalesce(cw_sub_balance_assister.balance,0) + ({changeBal}),debit_amount=coalesce(cw_sub_balance_assister.debit_amount,0)+({debitBal}),credit_amount=coalesce(cw_sub_balance_assister.credit_amount,0)+({creditBal});";
                                }
                                if (row.assister2_type.IsValid() && row.assister2_id.IsValid())
                                {
                                    sql += $"insert into cw_sub_balance_assister(company_id,sub_id,period,assister_type,assister_id,balance,debit_amount,credit_amount) values ({company_id},{sub.sub_id},'{period}','{row.assister2_type}',{row.assister2_id},{changeBal},{debitBal},{creditBal}) on conflict(company_id,sub_id,period,assister_type,assister_id) do update set balance = coalesce(cw_sub_balance_assister.balance,0) + ({changeBal}),debit_amount=coalesce(cw_sub_balance_assister.debit_amount,0)+({debitBal}),credit_amount=coalesce(cw_sub_balance_assister.credit_amount,0)+({creditBal});";
                                }
                            }
                        }
                    }
                }
            }


            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("setting", $"select coalesce(setting ->>  'accountingPeriod','') accountingperiod from company_setting where company_id = {company_id}");
            QQ.Enqueue("cvm", $"select MAX(happen_time) from cw_voucher_main where company_id={company_id}");
            dynamic setting = null;
            dynamic cvm = null;
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "setting") setting = CDbDealer.Get1RecordFromDr(dr, false);
                if (sqlName == "cvm") cvm = CDbDealer.Get1RecordFromDr(dr, false);
            }
            QQ.Clear();
            
            if (operType== VoucherOperType.Save || operType == VoucherOperType.SaveAndApprove)
            {
                var row = SheetRows[0];//因为每行存的都一样
                if (row.business_sheet_id.IsValid())
                {
                    dynamic businessSheets = row.business_sheet_id.Split(',');
                    foreach (var bSheetID in businessSheets)
                    {
                        sql += $"insert into cw_voucher_sheet_mapper(company_id,voucher_id,business_sheet_type,business_sheet_id) values ({company_id},{sheetID},'{row.business_sheet_type}',{bSheetID}) on conflict(company_id,voucher_id,business_sheet_type,business_sheet_id) do nothing; ";
                        //新版本不用这个表了，是否废弃待定，现使用SheetCwBase中的sheet_attribute --zy2024.9.6
                        //不废弃，万一voucher表sheet_attribute丢失可用mapper表恢复 --zy2024.10.11
                    }
                }
            }

            sql += ClosingCarryForwardController.GetNextBalanceSql(setting.accountingperiod+"-01", Convert.ToDateTime(cvm.max).ToString("yyyy-MM-01"), company_id);

            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
                await CwLog.Save(company_id, OperID, sheetID, "OnSheetIDGot", sql, cmd);//记录余额过程
            }
        }
        protected string GetSQLForTemplates(string companyID, string mainTable, string sheetIDs)
        {
            string sql =
            @$"select m.sheet_id,st.setting->>'companyName' as company_name,st.setting->>'contactTel' as company_tel,st.setting->>'companyAddress' as company_address, t.template_id,t.template_name from {mainTable} m 
 left join print_template t on  t.sheet_type='FV' and t.company_id={companyID}  
left join print_template_avail_elements pa on pa.sheet_type='FV'
left join company_setting st on m.company_id=st.company_id
where m.company_id={companyID} and m.sheet_id in ({sheetIDs}) order by t.template_id;";

            return sql;
        }
        public static async Task<GetSheetsResult<SheetCwVoucher>> GetItemSheets(CMySbCommand cmd, string operKey, string sheetIDs, GetSheetsUsage usage)  
        {
            //  var clsType1 =  MethodBase.GetCurrentMethod().ReflectedType;
            //  System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
            //  string name = st.GetFrame(1).GetMethod().Name;
            //  var clsType=typeFromName(name);

            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            //TSheet sheet = (TSheet)Activator.CreateInstance(typeof(TSheet), new object[] { SHEET_RETURN.NOT_RETURN, LOAD_PURPOSE.SHOW });
            SheetCwVoucher sheet = (SheetCwVoucher)Activator.CreateInstance(typeof(SheetCwVoucher));

            List<SheetCwVoucher> sheets = null;
    

            sheets = (List<SheetCwVoucher>)await sheet.LoadMultiSheets<SheetCwVoucher>(cmd, companyID, sheetIDs, "", "");
            
            var firstSheet = sheets.First(); 
            GetSheetsResult<SheetCwVoucher> res = new GetSheetsResult<SheetCwVoucher>();
              
            string company_name = "", company_tel = "", company_address = "";
             
            dynamic printTemplate = null; 
            string sql = "";
            Dictionary<string, string> variables = new Dictionary<string, string>();
            if (usage.ForPrint)
            {
                sql = sheet.GetSQLForTemplates(companyID, sheet.MainTable, sheetIDs);
                SQLQueue QQ = new SQLQueue(cmd);
                QQ.Enqueue("map", sql);
                sql = $"select avail_elements from print_template_avail_elements where sheet_type='FV';";
                QQ.Enqueue("avail_elements", sql);
                sql = $"select template_id, template_content from print_template where company_id={companyID} and sheet_type in ('FV');";
                QQ.Enqueue("template", sql);
                sql = $"select oper_name from info_operator where oper_id='{operID}';";
                QQ.Enqueue("oper_name", sql);

                List<System.Dynamic.ExpandoObject> mapList = null;
                Dictionary<string, string> dicTemplate = new Dictionary<string, string>();
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    string tb = QQ.Dequeue();
                    if (tb == "map")
                    {
                        mapList = CDbDealer.GetRecordsFromDr(dr, false);
                    }
                    else if (tb == "template")
                    {
                        while (dr.Read())
                        {
                            string template_id = CPubVars.GetTextFromDr(dr, "template_id");
                            string template_content = CPubVars.GetTextFromDr(dr, "template_content");
                            dynamic c = JsonConvert.DeserializeObject(template_content);
                            c.variables = JsonConvert.DeserializeObject(JsonConvert.SerializeObject(variables));
                            template_content = JsonConvert.SerializeObject(c);
                            dicTemplate.Add(template_id, template_content);
                        }
                    }
                    else if (tb == "avail_elements")
                    {
                        if (dr.Read())
                        {
                            string avail_elements = CPubVars.GetTextFromDr(dr, "avail_elements");
                            dynamic elements = JsonConvert.DeserializeObject(avail_elements);

                            void putAreaVars(JArray eles, string prefix = "")
                            {
                                for (var i = 0; i < eles.Count; i++)
                                {
                                    dynamic obj = eles[i];
                                    string name = obj.name;
                                    string title = prefix + (string)obj.title;
                                    if (!variables.ContainsKey(title))
                                        variables.Add(title, name);
                                }
                            }

                            if (elements.pageHead != null) putAreaVars(elements.pageHead);
                            if (elements.tableHead != null) putAreaVars(elements.tableHead);
                            if (elements.table != null) putAreaVars(elements.table, "col_");
                            if (elements.tableTail != null) putAreaVars(elements.tableTail);
                            if (elements.pageTail != null) putAreaVars(elements.pageTail);
                        }
                    }
                    else if (tb == "oper_name")
                    {
                        if (dr.Read())
                        {
                            res.operName = CPubVars.GetTextFromDr(dr, "oper_name");
                        }
                    }

                }
                QQ.Clear();

                Dictionary<string, string> dicSheets = new Dictionary<string, string>();

                int index = 0;
                foreach (dynamic tmp in mapList)
                {
                    string template_content = "";
                    if (dicTemplate.ContainsKey(tmp.template_id))
                    {
                        template_content = dicTemplate[tmp.template_id];
                    }
                    if (template_content == "") continue;

                    if (!dicSheets.ContainsKey(tmp.sheet_id))
                    {
                        dicSheets.Add(tmp.sheet_id, tmp.sheet_id);
                        var sheet1 = sheets.Find((c) => c.sheet_id == tmp.sheet_id);
                        if (tmp.company_name != "")
                            company_name = sheet1.company_name = tmp.company_name;
                        if (tmp.company_tel != "")
                            company_tel = sheet1.company_tel = tmp.company_tel;
                        if (tmp.company_address != "")
                            company_address = sheet1.company_address = tmp.company_address;

                        printTemplate = JsonConvert.DeserializeObject(template_content);
                        
                    }
                    index++;
                } 
            }

             
 

            if (res.msg == "")
            {
                    
           //     if (usage.GetEachSheet)
                {
                    var grp = new GetSheetsResult<SheetCwVoucher>.SheetGroup();
                    grp.template = printTemplate;
                    grp.sheets = sheets;

                    foreach (var sheet1 in sheets)
                    {
                        await sheet1.LoadInfoForPrint(cmd, false, false);
                    }
                    res.sheetGroup.Add(grp);
                }
               
            }

            res.sheetIDs = sheetIDs;
            res.result = "OK";
            res.templVariables = variables;


            sql = @$"SELECT P.printer_id, P.printer_name, P.device_id, P.check_code, P.printer_brand
FROM info_cloud_printer P 
WHERE P.company_id = {companyID} AND P.status = 1
ORDER BY P.printer_id LIMIT 10;";
            res.cloudPrinters = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            if (res.msg != "")
            {
                res.result = "Error";
            }
            return res;

        }

    }



}
