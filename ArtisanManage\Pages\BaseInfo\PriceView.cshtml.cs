﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class PriceViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
 
        public PriceViewModel(CMySbCommand cmd) : base(Services.MenuId.infoPrice)
        {
            // Debug.WriteLine("OK");
            this.cmd = cmd;
            this.SQLVariables["PLAN_CONDI"] = "";
            this.SQLVariables["IN_PLAN"] = "zero,";
            DataItems = new Dictionary<string, DataItem>()
            {
                //{"item_query",new DataItem(){title="编号"}}, 
                 {"item_brand",new DataItem(){Title="品牌",LabelFld="brand_name",ButtonUsage="list",QueryOnChange=true,CompareOperator="=",
                    SqlForOptions = CommonTool.selectBrands}},
                // {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称/助记码",UseJQWidgets=false, SqlFld="item_name,py_str",LabelFld="brand_name",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
                 {"item_id",new DataItem(){Title="商品名称",LabelFld="item_name",SqlFld="tem.item_id", Width="300", ButtonUsage="list",CompareOperator="=",ShowDropDownColumnsHeader=true, datafields="[{datafield: 'l', text: '品名', width: 180},{datafield: 'b', text: '条码', width: 120}]", SearchFields=CommonTool.itemSearchFields, QueryOnChange=true,
                   QueryByLabelLikeIfIdEmpty=true, SqlForOptions =CommonTool.selectItemWithBarcode}},
                 {"price_plan",new DataItem(){Title="价格",ForQuery=false, LabelFld="plan_name",ButtonUsage="list",QueryOnChange=true, CompareOperator="=",
                    SqlForOptions="select -1 as v,'未设方案' as l,0 as order_index union select plan_id as v,plan_name as l,1 as order_index from price_plan_main where company_id=~COMPANY_ID order by order_index"}},
                 {"other_class",new DataItem(){Title="父类", LikeWrapper="/", CtrlType="jqxTree",MumSelectable=true,GetOptionsOnLoad=true,QueryOnChange=true,CompareOperator="like",   MaxRecords = "1000",
                   SqlForOptions=CommonTool.selectClasses} },
                //{"brand_no",new DataItem(){title="品牌",labelFld="brand_name",buttonUsage="list",
                  //  SqlForOptions ="select brand_no as v,brand_name as l from info_item_brand"}}
            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     IdColumn="i",TableName="info_item_prop",
                     ShowContextMenu=true, 
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"i",new DataItem(){Title="商品编号",SqlFld="distinct tem.item_id",Hidden=true}},
                       {"smi",new DataItem(){Title="商品编号",SqlFld="tem.son_mum_item",Hidden=true}},
                       {"n",new DataItem(){Title="商品名称",SqlFld="item_name",Width="155",Pinned=true}},
                       {"item_spec",new DataItem(){Title="规格",  Width="60",Hidden=true}},
                       {"item_no",new DataItem(){Title="商品编号",SqlFld="item_no", Width="60",Hidden=true}},
                       {"barcode",new DataItem(){Title="小单位条码",SqlFld="s_barcode", Width="120",Hidden=true}},
                       {"c",new DataItem(){Title="单位换算",SqlFld="unit_conv", Width="135",Hidden=true}},
                       {"retail_price",new DataItem(){Title="零售价",SqlFld="s_retail_price", Width="60",Hidden=true}},
                       {"buy_price",new DataItem(){Title="进价", Width="60",  FuncGetSubColumns = async (col) =>
                         {
                            ColumnsResult result=new ColumnsResult();
                            Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>(){
                               {"b_buy_price",new DataItem(){Title="大", Width="60"}},
                               {"m_buy_price",new DataItem(){Title="中", Width="60",Hidden=true}},
                               {"s_buy_price",new DataItem(){Title="小", Width="60"}},
                            };
                            result.Columns=subColumns;
                            return result;
                       }} },
                       {"wholesale_price",new DataItem(){Title="批发价", Width="60",  FuncGetSubColumns = async (col) =>
                        {
                            ColumnsResult result=new ColumnsResult(); 
                            Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>(){                                
                               {"b_wholesale_price",new DataItem(){Title="大", Width="60"}},
                               {"m_wholesale_price",new DataItem(){Title="中", Width="60",Hidden=true}},
                               {"s_wholesale_price",new DataItem(){Title="小", Width="60"}}
                            }; 
                            result.Columns=subColumns;
                            return result;
                       }} },  
                       {"total",new DataItem(){Title="",SqlFld="k.sp",Hidden=true,HideOnLoad=true}},
                       {"plan_price",new DataItem(){Title="方案价", Width="60",  FuncGetSubColumns = async (col) =>
                       {
                            ColumnsResult result=new ColumnsResult();
                            bool GetFromDb=false;
                            if(this.DataItems["price_plan"].Value!="") GetFromDb=true;
                            Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>(){
                               {"b_price",new DataItem(){Title="大",GetFromDb=GetFromDb, Width="60"}},
                               {"m_price",new DataItem(){Title="中",GetFromDb=GetFromDb, Width="60"}},
                               {"s_price",new DataItem(){Title="小",GetFromDb=GetFromDb, Width="60"}},
                               {"discount",new DataItem(){Title="折扣%",GetFromDb=GetFromDb,SqlFld="concat((case when discount*100=round(cast (discount*100 as numeric),2) then round(discount*100::numeric) else round(cast (discount*100 as numeric),2) end),'%')", Width="60"}},

                            };
                            result.Columns=subColumns;
                            return result;
                       }}}, 
                       {"zero",new DataItem(){SqlFld="0",Hidden=true, HideOnLoad=true}},
                       {"in_plan",new DataItem(){SqlFld="case when b_price is not null or m_price is not null or s_price is not null then 0 else 1 end",Hidden=true, HideOnLoad=true}},                       
                      
                       {"detail",new DataItem(){Title="价格方案", Width="60",SqlFld="'详情'",Linkable=true}}
                     },
                     QueryFromSQL=$@"
from (   
    SELECT ip.status as status ,ip.company_id,ip.item_id,item_name,item_spec,item_no,other_class,item_brand,(case when m_unit_factor is null and b_unit_factor is not null then concat('1',b_unit_no,'=',b_unit_factor,s_unit_no)  
	    when (b_unit_factor is not null) and (m_unit_factor is not null) then concat('1',b_unit_no,'=',m_unit_factor,m_unit_no,'=',b_unit_factor,s_unit_no) when b_unit_factor is null then concat('1',s_unit_no)  end ) as unit_conv,
        m_unit_factor,b_unit_factor,s_barcode,
        s_unit_no,s_wholesale_price,s_retail_price,s_buy_price,
        m_unit_no,m_wholesale_price,m_retail_price,m_buy_price,
        b_unit_no,b_wholesale_price,b_retail_price,b_buy_price,son_mum_item

    FROM info_item_prop as ip
    LEFT JOIN
    (
        select item_id, m->>'f1' m_unit_factor,b->>'f1' b_unit_factor,s->>'f3' s_barcode,               
            s->>'f2' s_unit_no,s->>'f4' s_wholesale_price,s->>'f5' s_retail_price,s->>'f6' s_buy_price,
            m->>'f2' m_unit_no,m->>'f4' m_wholesale_price,m->>'f5' m_retail_price,m->>'f6' m_buy_price,
            b->>'f2' b_unit_no,b->>'f4' b_wholesale_price,b->>'f5' b_retail_price,b->>'f6' b_buy_price
        from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,wholesale_price,retail_price,buy_price)) as json from info_item_multi_unit where company_id = ~COMPANY_ID ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)
    ) t
    on ip.item_id = t.item_id where company_id= ~COMPANY_ID
    
) tem
left join price_plan_item p on p.item_id = tem.item_id
left join 
(
    select item_id,array_to_string(array(select unnest(array_agg(concat_ws(':',plan_id,concat_ws(',',s_price,m_price,b_price))))),'; ') as sp from price_plan_item where company_id = ~COMPANY_ID group by item_id
) k on p.item_id=k.item_id  where tem.company_id=~COMPANY_ID and tem.status <>0 ~VAR_PLAN_CONDI
" ,

                     QueryOrderSQL="order by ~VAR_IN_PLAN item_name"
                  }
                } 
            }; 
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                var columns = await Grids["gridItems"].GetAllColumns();
                columns["s_buy_price"].HideOnLoad = columns["s_buy_price"].Hidden = true;
                columns["m_buy_price"].HideOnLoad = columns["m_buy_price"].Hidden = true;
                columns["b_buy_price"].HideOnLoad = columns["b_buy_price"].Hidden = true;
                 
            }


        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            var planID = DataItems["price_plan"].Value;
            //this.SQLVariable1 = "";
            if (planID != null && planID != "")
            {
              // this.SQLVariable1 = $" and (p.plan_id={planID} or p.plan_id is null) ";
              if(planID == "-1"){
                    this.SQLVariables["PLAN_CONDI"] = $" and p.plan_id is null ";
              }
              else{
                    //子商品跳过planid的筛选
                    this.SQLVariables["PLAN_CONDI"] = $"and (p.plan_id ={ planID} or tem.son_mum_item is not null)";
              }
               
               this.SQLVariables["IN_PLAN"] = "in_plan,";
            }
        }
        public async Task OnGet(string forSelect)
        {  
            await InitGet(cmd);
            ForSelect = forSelect == "1";
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
        /*
        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            string err = "";
            SQLQueue QQ = new SQLQueue(cmd);          
            string sql = $"select count(*) from sheet_sale_detail where item_id in ({rowIDs}) and company_id={company_id}";
            QQ.Enqueue("sale", sql);
            sql = $"select count(*) from sheet_buy_detail where item_id in ({rowIDs}) and company_id={company_id}";
            QQ.Enqueue("buy", sql);
            sql = $"select count(*) from sheet_move_detail where item_id in ({rowIDs}) and company_id={company_id}";
            QQ.Enqueue("move", sql);
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while(QQ.Count>0)
            {
                string tbl=QQ.Dequeue();
                if (dr.Read())
                {
                    object ov=dr[0];
                    if(ov!=null && ov != DBNull.Value)
                    {
                        int ct = Convert.ToInt32(ov);
                        if (ct > 0)
                        {
                            err = "商品已被使用过,无法删除";
                            break;
                        }
                    }
                }
            }
            QQ.Clear();
            return err;
        }*/
    }

    [Route("api/[controller]/[action]")]
    public class PriceViewController : QueryController
    { 
        public PriceViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            PriceViewModel model = new PriceViewModel(cmd);
            if (value != null)
            {

            }
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords(string price_plan)
        {// string gridID,int startRow,int endRow,bool bNewQuery){
            PriceViewModel model = new PriceViewModel(cmd);
            /*if(price_plan!=null && price_plan != "")
            {
               var gridItem = model.Grids["gridItems"].Columns;
                gridItem["s_price"].GetFromDb = true;
                gridItem["m_price"].GetFromDb = true;
                gridItem["b_price"].GetFromDb = true;
                gridItem["discount"].GetFromDb = true;
            }*/
            return await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            /*
            string json = JsonConvert.SerializeObject(records);
            JObject jo = (JObject)JsonConvert.DeserializeObject(json);
            JObject value = (JObject)jo["Value"];
            
            var rowString = value["rows"].ToString();
            var jar = JArray.Parse("[" + rowString + "]");

            int lines = rowString.Substring(1,rowString.Length-2).Split('{').Length-1;
            string spl = rowString.Split('{')[1];
            int start = int.Parse(spl.Split('"')[1]);
            for (int i=start; i < lines+start; i++)
            {
                string k=i.ToString();
                var s = jar[0][k];
                //return s;
                if (s != null)
                {
                    var p = s["total"];
                    if (p != null)
                    {
                        string total = p.ToString();
                        string[] arr = Regex.Split(total, "; ", RegexOptions.IgnoreCase);
                        var selectedPrice = "";
                        for (var j = 0; j < arr.Length; j++)
                        {
                            string[] c = arr[j].Split(':');
                            //return Json(new { c,price_plan });
                            if (c[0] == price_plan)
                            {
                                selectedPrice = c[1];
                                break;
                            }
                        }
                        string[] prices = selectedPrice.Split(',');
                        if (prices.Length == 2 || prices.Length == 3) {
                            jar[0][i.ToString()]["small_price"] = prices[0];
                            if ((jar[0][i.ToString()]["buy"] != null) && (jar[0][i.ToString()]["buy"].ToString()!= "")
                                && Convert.ToDouble(jar[0][i.ToString()]["buy"]) != 0.0)
                            {
                                jar[0][i.ToString()]["discount"] = Math.Round(Convert.ToDouble(prices[0]) / Convert.ToDouble(jar[0][i.ToString()]["buy"]), 2);
                            }
                        } 
                        if (prices.Length == 2)
                        {
                            jar[0][i.ToString()]["big_price"] = prices[1];
                        }
                        else if (prices.Length == 3)
                        {
                            jar[0][i.ToString()]["mid_price"] = prices[1];
                            jar[0][i.ToString()]["big_price"] = prices[2];
                        }
                        
                    }
                }
            }
            value["rows"] = jar[0];
            value["lines"] = lines;
            value["start"] = start;
            //value["pn"] = price_plan;
            return value;*/
        }
        /*
        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            PriceViewModel model = new PriceViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }*/
       
        [HttpPost]
        public async Task<IActionResult> RemoveClass([FromBody] dynamic value)
        {
            ClassEditModel model = new ClassEditModel(cmd);
            string id = value[model.m_idFld];
            string result = "OK";
            if (id == "")
            {
                result = "请传入类编号";
                goto end;
            }
            CDbDealer db = new CDbDealer();

            object o = null;

            cmd.CommandText = "select class_id from info_item_class where mother_id='" + id + "'";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该类的子类后再删除该类"; goto end;
            }
            cmd.CommandText = "select item_name from info_item_prop where item_class='" + id + "'";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该类的商品后再删除该类"; goto end;
            }
            string sql = "delete from info_item_class where class_id='" + id + "'";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync(); 

            //  var tt = Convert.ToString(value.uid); 
            //var rr = new { UserID = value.UserID, UserName = value.UserName };
            //return value;
            end:
            return Json(new { result, class_id = id });
            //return JsonObject<object> (new { UserID = value.UserID, UserName = value.UserName });
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            PriceViewModel model = new PriceViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }



    }

}
