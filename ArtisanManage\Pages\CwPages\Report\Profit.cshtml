﻿@page
@model ArtisanManage.CwPages.Report.ProfitModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Profit</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>

    <script src="~/js/FileSaverVue.js"></script>
    <script src="~/js/Blob.js"></script>
    <script src="~/js/jszip.js"></script>
    <script src="~/js/xlsx.full.min.js"></script>
    <script src="~/js/xlsx-style.js"></script>
    <script src="~/js/Export2Excel.js?v=@Html.Raw(Model.Version)"></script>

    <style>
        * {
            font-family: "微软雅黑"
        }
        [v-cloak] {
            display: none;
        }

        body {
        }

        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;

        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #dddddd;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }

        #pages {
            width: 100%;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            /*background-color: #f9f9f9;*/
        }

        .pages_title {
            width: 100%;
            height: 5vh;
            font-weight: 500;
            font-size: 25px;
            text-align: center;
            margin-top: 5px;
            padding-bottom:10px;
        }

        .pages_query {
            height: 5vh;
            display: block;
            margin-left: 20px;
            margin-right: 20px;
            flex-wrap: wrap;
            padding-bottom: 10px;
        }

        .query_item {
            display: inline-block;
            float:left;
            width:50%;
            margin-top: 3px;
        }

        .pages_buttons {
            width:49%;
            height: 100%;
            float:right;
            display:inline-block;
            justify-content:end;
            align-items:center;
        }

        .item_input {
            width: 149px;
            height: 100%;
            z-index: 0;
            position: relative;
            border-style: none none solid;
            border-bottom: 1px solid #c7c7c7;
            border-radius: 0px;
            margin-left: 10px;
        }

        .item_name {
            height: 20px;
            bottom: 1px;
            right: 1px;
            margin-bottom: 0px;
        }

        .pages_content {
            height: 100%;
            padding: 0 20px;
        }

        .level1 {
            font-weight: bold
        }
        .level2 {
            text-indent: 1em;
        }
        .level3 {
            text-indent: 2em;
        }


        .pages_content table {
            width: 100%;
            height:85vh;
            border: 0;
            border-collapse: collapse;
            border: 2px solid #ebeef5;
        }

            .pages_content table tbody {
                display: block;
                overflow: auto;
                overflow-x: hidden;
                height: calc(100% - 42px);
            }
        @@media(max-height:700px) {
            .pages_content table tbody {
                display: block;
                overflow: auto;
                overflow-x: hidden;
                height: 460px;
            }
         }
        .pages_content table thead, .pages_content tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .pages_content table thead {
            width: calc( 100% - 1em - 2px )
        }

        .pages_content table thead th{
            background-color:#f0f0f5;
        }

        .pages_content table thead th:nth-child(1), .pages_content table thead th:nth-child(5), .pages_content table tbody td:nth-child(1), .pages_content table tbody td:nth-child(5) {
            width: 20%
        }
        .pages_content table thead th:nth-child(2), .pages_content table thead th:nth-child(6), .pages_content table tbody td:nth-child(2), .pages_content table tbody td:nth-child(6) {
            width: 5%;
            text-align: center
        }
        .pages_content table thead th:nth-child(3), .pages_content table thead th:nth-child(7), .pages_content table tbody td:nth-child(3), .pages_content table tbody td:nth-child(7) {
            width: 12.5%
        }
        .pages_content table thead th:nth-child(4), .pages_content table thead th:nth-child(8), .pages_content table tbody td:nth-child(4), .pages_content table tbody td:nth-child(8) {
            width: 12.5%
        }
        .pages_content table tbody td:nth-child(3), .pages_content table tbody td:nth-child(7), .pages_content table tbody td:nth-child(4), .pages_content table tbody td:nth-child(8) {
            text-align: right;
        }
        @*高度*@
        .pages_content table thead th, .pages_content table tbody td {
            min-height: 40px;
            line-height: 40px;
        }
        @*边框*@
        .pages_content table thead th, .pages_content table tbody td {
            border-bottom: 2px solid #fff;
            border-right: 2px solid #fff;
        }
            .pages_content table thead th:last-child {
                border-right: 0;
            }
        @*背景*@
        .pages_content table tbody tr:nth-child(odd) {
            background: #fafafa;
        }
        .pages_content table tbody tr:hover {
            background-color: #f5f7fa;
        }

        .pages_content table thead th, .pages_content table tbody td {
            padding: 0 15px;
        }

        .showFormulaIcon{
            position:absolute;
            right:10px;
            top:50%;
            transform:translate(-50%,-50%);
        }
    </style>

</head>
<body>
    <div id="root" v-cloak>
        <div id="pages" class="" ref="pages">
            <div class="pages_title">利润表</div>
            <div class="pages_query">
                <div class="query_item">
                    <label class="item_name"></label>
                    <el-date-picker v-model="queryDate"
                                    type="month"
                                    value-format="yyyy-MM"
                                    :disabled="disabledDatePicker"
                                    @@change="changeDate"
                                    placeholder="选择月"
                                    :picker-options="pickerOptions">
                    </el-date-picker>
                </div>
                <div class="pages_buttons">
                    <el-button type="info" plain :disabled="disabledExportBtn" v-on:click="exportBtn()" style="position:absolute;right:20px;">导出</el-button>
                </div>
            </div>
            <div class="pages_content">
                <table>
                    <thead>
                        <tr>
                            <th>项目</th>
                            <th>行次</th>
                            <th>本年累计金额</th>
                            <th>本月金额</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="row in pnlData" :key="row.id">
                            <td :class="row.class" style="position:relative;">
                                {{row.name}}
                                <el-tooltip class="item" effect="dark" :content="row.formulaText" placement="right">
                                    <el-button icon="el-icon-more" circle class="showFormulaIcon" size="mini" v-if="row.showFormula"></el-button>
                                </el-tooltip>
                             </td>
                            <td>{{row.rowIndex}}</td>
                            <td>{{row.year_balance}}</td>
                            <td>{{row.balance}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script>
        var g_operKey = '@Model.OperKey';

        window.g_operRights =@Html.Raw(Model.JsonOperRightsOrig);
        function checkOperRight(vm){
            if(!window.g_operRights.cwReport){
                return false;
            }
            if (window.g_operRights.cwReport.profitSheet && window.g_operRights.cwReport.profitSheet.see) { 
                if(!window.g_operRights.cwReport.profitSheet.export) vm.disabledExportBtn=true;
                return true;
            }else{
                return false;
            }
        }
    </script>
    <script>

        var vm = new Vue({
            el: '#root',
            data() {
                return {
                    pnlData:[
                        {id:'01', name:'一、 营业收入', class:'level1', rowIndex:1, balance:0, year_balance:0, showFormula:true, formulaText:'',children: []},
                        {id:'02', name:'减：营业成本', class:'level2', rowIndex:2, balance:0, year_balance:0, showFormula:true, formulaText:'',children: []},
                        {id:'03', name:'税金及附加', class:'level2', rowIndex:3, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'04', name:'其中：消费税', class:'level3', rowIndex:4, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'05', name:'营业税', class:'level3', rowIndex:5, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'06', name:'城市维护建设税', class:'level3', rowIndex:6, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'07', name:'资源税', class:'level3', rowIndex:7, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'08', name:'土地增值税', class:'level3', rowIndex:8, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'09', name:'城镇土地使用税、 房产税、车船税、印花税', class:'level3', rowIndex:9, balance:0, year_balance:0, showFormula:true, formulaText:'',children: []},
                        {id:'10', name:'教育费附加、 矿产资源补偿费、排污费', class:'level3', rowIndex:10, balance:0, year_balance:0, showFormula:true, formulaText:'',children: []},
                        {id:'11', name:'销售费用', class:'level2', rowIndex:11, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'12', name:'其中：商品维修费', class:'level3', rowIndex:12, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'13', name:'广告费和业务宣传费', class:'level3', rowIndex:13, balance:0, year_balance:0, showFormula:true, formulaText:'',children: []},
                        {id:'14', name:'管理费用', class:'level2', rowIndex:14, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'15', name:'其中：开办费', class:'level3', rowIndex:15, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'16', name:'业务招待费', class:'level3', rowIndex:16, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'17', name:'研究费用', class:'level3', rowIndex:17, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'18', name:'财务费用', class:'level2', rowIndex:18, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'19', name:'其中：利息费用（收入以“-”号填列）', class:'level3', rowIndex:19, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'20', name:'加：投资收益（损失以“-”号填列）', class:'level2', rowIndex:20, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'21', name:'二、营业利润（亏损以“-”号填列）', class:'level1', rowIndex:21, balance:0, year_balance:0, showFormula:true, formulaText:'',children: []},
                        {id:'22', name:'加：营业外收入', class:'level2', rowIndex:22, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'23', name:'其中：政府补助', class:'level3', rowIndex:23, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'24', name:'减：营业外支出', class:'level2', rowIndex:24, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'25', name:'其中：坏账损失', class:'level3', rowIndex:25, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'26', name:'无法收回的长期债券投资损失', class:'level3', rowIndex:26, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'27', name:'无法收回的长期股权投资损失', class:'level3', rowIndex:27, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'28', name:'自然灾害等不可抗力因素造成的损失', class:'level3', rowIndex:28, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'29', name:'税收滞纳金', class:'level3', rowIndex:29, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'30', name:'三、利润总额（亏损总额以“-”号填列）', class:'level1', rowIndex:30, balance:0, year_balance:0, showFormula:true, formulaText:'',children: []},
                        {id:'31', name:'减：所得税费用', class:'level2', rowIndex:31, balance:0, year_balance:0, showFormula:false, formulaText:'',children: []},
                        {id:'32', name:'四、净利润（净亏损以“-”号填列）', class:'level1', rowIndex:32, balance:0, year_balance:0, showFormula:true, formulaText:'',children: []},
                    ],
                    queryDate: new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)),
                    pickerOptions:{
                        disabledDate(time) {
                            return time.getTime() > Date.now();
                        }
                    },
                    companyName:'',
                    disabledDatePicker:false,
                    disabledExportBtn:false
                }
            },
            created(){
                 if(!window.checkOperRight(this)){
                    this.disabledDatePicker=true;
                    this.disabledExportBtn=true;
                    return;
                }

                this.getData(this.queryDate+'-1');
            },
            mounted(){
            },
            methods: {
                getData(period){
                    $.ajax({
                        url: '/api/Profit/GetData',
                        type: 'get',
                        data: {
                            operKey: g_operKey,
                            period:period
                        },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then((res)=> {
                        if (res.result === 'OK') {
                            console.log('get pnl data');
                            this.companyName=res.companyName;
                            this.queryDate=res.pickPeriodOp;
                            this.pickerOptions={
                                disabledDate(time) {
                                    return (time.getTime()<new Date(res.openPeriod) || time.getTime() > new Date(res.maxPeriod));
                                }
                            };
                            // res.pnlReport.forEach(item=>{
                            //     let row=this.pnlData.filter(pd=>pd.id==item.id)[0];
                            //     row.balance=item.balance;
                            //     row.year_balance=item.year_balance;
                            //     row.formulaText=item.formulaText;
                            // });
                                    // 填充整个 pnlData
                             res.pnlReport.forEach(item => {
                               //let row = this.pnlData.find(pd => pd.id == item.id);
                               let row=this.pnlData.filter(pd=>pd.id==item.id)[0];
                               if (row) {
                                   row.balance = item.balance;
                                   row.year_balance = item.year_balance;
                                   row.formulaText = item.formulaText;
                                                           //如果该条目有子条目
                               if (item.children && item.children.length > 0) {
                          row.children = [];
                          item.children.forEach(childItem => {
                            //只填充 balance 或 year_balance 非零的子条目
                               if (childItem.balance !== 0 || childItem.year_balance !== 0) {
                                     let childRow = {
                                       id: childItem.id,
                                       name: childItem.name,
                                       class: childItem.class,
                                       rowIndex: childItem.rowIndex,
                                       balance: childItem.balance,
                                       year_balance: childItem.year_balance,
                                       showFormula: childItem.showFormula,
                                       formulaText: childItem.formulaText,
                                     };
                                     row.children.push(childRow);  添加子条目到父条目
                            }
                          });
                        }
                               }
                             });
                        } else {
                            this.$message({ showClose: true, message: res.msg, type: 'warning', offset: 20, duration: 2500 });
                        }
                    });
                },
                changeDate(){
                    this.getData(this.queryDate+'-1');
                },
                exportBtn(){
                    let data=[
                        ['利润表'], 
                        [`公司名称：${this.companyName}`,null, `会计期间：${this.queryDate.substr(0,7)}`,'单位：元'],
                        ['项目', '行次', '本年累计金额', '本月金额'],
                    ];
                    this.pnlData.forEach(row=>{
                        let excelRow=[row.name, row.rowIndex, row.year_balance, row.balance ];
                        data.push(excelRow);
                    });
                    let merges=['A1:D1','A2:B2'];
                    let bodyTitleName=[];                    
                    let  specialCellConfig=[
                        { 
                            "type": "s", 
                            "configObj": { 
                                "font": { "sz": 14, "bold": true },
                                "alignment": { "horizontal": "center" }
                            }, 
                            "controlScope": "col", 
                            "scope": [ 1, 1 ] 
                        },
                        { 
                            "type": "s", 
                            "configObj": { 
                                "border": { "top": { "style": "thin" }, "bottom": { "style": "thin" }, "left": { "style": "thin" }, "right": { "style": "thin" } },
                                "font": { "bold": true } 
                            }, 
                            "controlScope": "col", 
                            "scope": [ 3, 3 ] 
                        },
                        { 
                            "type": "s", 
                            "configObj": { "border": { "top": { "style": "thin" }, "bottom": { "style": "thin" }, "left": { "style": "thin" }, "right": { "style": "thin" } } }, 
                            "controlScope": "col", 
                            "scope": [ 4, data.length ] 
                        }, 
                    ];
                    window.webExportExcel(data,`利润表[${this.queryDate.substr(0,7)}]`, merges, bodyTitleName, specialCellConfig)
                }

            }
        })
    </script>


</body>
</html>