﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;

namespace ArtisanManage.MyJXC
{
    public enum SHEET_SALE_FEE_APPORTION
    {
        EMPTY,//0
        SAVE,//1
        APPROVE,//2
        RED1,//3
        RED2//4
    }

    public class SaleFeeItemInfo//用于获取当前加权价
    {
        public string item_id { get; set; } = "";
        public string item_name { get; set; } = "";
        public decimal cost_price_avg { get; set; } = 0;
        public decimal total_qty { get; set; } = 0;//加权价角度的总数量，不考虑仓库批次
    }

    public class SaleFeeSheetInfo//用于销售单审核前的分摊 和 销售单审核后已审核的分摊单
    {
        public List<SheetRowSaleFeeApportion> sheetRowSaleFeeAppoList { get; set; }
        public decimal total_fee_amount { get; set; }
        public string sale_fee_apportion_sheet_id { get; set; } = "";
        public string sale_fee_apportion_sheet_no { get; set; } = "";
        public SHEET_SALE_FEE_APPORTION status { get; set; } = SHEET_SALE_FEE_APPORTION.EMPTY;
        public string make_brief { get; set; } = "";
    }

    #region 费用信息（gridB）
    public class SheetRowSaleFeeApportion : SheetRowBase
    {
        public SheetRowSaleFeeApportion()
        {

        }

        [SaveToDB][FromFld] public string relate_sheet_type { get; set; }
        [SaveToDB][FromFld] public string relate_sheet_id { get; set; }
        [SaveToDB(false)][FromFld(false)] public override int row_index { get; set; }
        [SaveToDB(false)][FromFld(false)] public override int inout_flag { get; set; }
        [SaveToDB(false)][FromFld(false)] public override string remark { get; set; }

        public bool is_new { get; set; } = false;//通过sheet_attribute/SaleFeeSheetInfo/sheetRowSaleFeeAppoList保存
        public string sale_fee_sheet_id { get; set; }
        public string sale_fee_sheet_no { get; set; }
        public string supcust_id { get; set; } = "";
        public string sup_name { get; set; }
        public string fee_sub_id { get; set; }
        public string fee_sub_name { get; set; }
        public string fee_sub_amount { get; set; }
        public string payway1_id { get; set; }
        public string payway1_name { get; set; }
        public string payway1_amount { get; set; } = "0";
        public string left_amount { get; set; } = "0";
        public string fee_make_brief { get; set; } = "";
    }

    public class SheetSaleFeeApportion : SheetBase<SheetRowSaleFeeApportion>
    {
        public bool fromSaleSheet = false;
        public bool fromSaleFeeAppoSheet = false;
        public bool from_sale_sheet { get; set; } = false;//savetodb

        public SheetSaleFeeApportion(LOAD_PURPOSE loadPurpose) : base("sheet_sale_fee_apportion_main", "sheet_sale_fee_apportion_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_SALE_FEE_APPORTION;
            ConstructFun();
        }

        private void ConstructFun()
        {
            if (LoadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @"left join sheet_sale_fee_apportion_detail d on t.sheet_id=d.sheet_id and d.relate_sheet_type='X'
                                                left join (select sheet_id as sale_sheet_id, sheet_no as sale_sheet_no from sheet_sale_main where company_id=~COMPANY_ID) sm on t.sheet_attribute->>'sale_sheet_id'=sm.sale_sheet_id::text
                                                left join (select oper_id, oper_name as maker_name from info_operator where company_id=~COMPANY_ID) om on om.oper_id=t.maker_id
                                                left join (select oper_id, oper_name as seller_name from info_operator where company_id=~COMPANY_ID) os on os.oper_id=t.seller_id
                                                left join (select oper_id, oper_name as approver_name from info_operator where company_id=~COMPANY_ID) oa on oa.oper_id=t.approver_id";
                DetailLeftJoin = @"left join  (select sheet_id as sale_sheet_id, sheet_no as sale_sheet_no, supcust_id  from sheet_sale_main where company_id=~COMPANY_ID) sm on sm.sale_sheet_id=t.relate_sheet_id and t.relate_sheet_type='X'
                                                left join (select sheet_id as fee_sheet_id, sheet_no as fee_sheet_no, supcust_id, make_brief as fee_make_brief from sheet_fee_out_main where company_id=~COMPANY_ID) fm on t.relate_sheet_id=fm.fee_sheet_id and t.relate_sheet_type='ZC'
                                                left join (select sheet_id, fee_sub_id,  fee_sub_amount  from sheet_fee_out_detail where company_id=~COMPANY_ID) fd on fd.sheet_id=t.relate_sheet_id 
                                                left join (select sub_id,sub_name from cw_subject where company_id=~COMPANY_ID) s on fd.fee_sub_id=s.sub_id
                                                left join (select supcust_id, sup_name from info_supcust where company_id=~COMPANY_ID) c on c.supcust_id=sm.supcust_id";
            }

        }

        [SaveToDB][FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB][FromFld] public int seller_id { get; set; }
        [FromFld("os.seller_name", LOAD_PURPOSE.SHOW)] public string seller_name { get; set; }

        public List<SheetRowSale> SaleSheetRows { get; set; } = new List<SheetRowSale>();
        public List<SheetRowSaleFeeApportion> SaleFeeSheetRows { get; set; } = new List<SheetRowSaleFeeApportion>();

        [FromFld("sm.sale_sheet_id", LOAD_PURPOSE.SHOW)] public string sale_sheet_id { get; set; }
        [FromFld("sm.sale_sheet_no", LOAD_PURPOSE.SHOW)] public string sale_sheet_no { get; set; }
        [FromFld("sheet_attribute->>'total_fee_amount'", LOAD_PURPOSE.SHOW)] public string total_fee_amount { get; set; }//sheet_attribute存一份，单据合计如果和这个不一致就是bug

        [SaveToDB]
        [FromFld]
        public string sheet_attribute
        {
            get//从前端获取数据，保存数据库
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                if (!string.IsNullOrEmpty(sale_sheet_id))
                {
                    sheetAttribute.Add("sale_sheet_id", sale_sheet_id);
                }
                if (!string.IsNullOrEmpty(total_fee_amount))
                {
                    sheetAttribute.Add("total_fee_amount", total_fee_amount);
                }
                if (!string.IsNullOrEmpty(JsonConvert.SerializeObject(SaleSheetRows)))
                {
                    List<Dictionary<string, string>> saleSheetRowsList = new List<Dictionary<string, string>>();
                    foreach (SheetRowSale rowSale in SaleSheetRows)
                    {
                        Dictionary<string, string> rowStr = new Dictionary<string, string>();
                        rowStr.Add("row_index", rowSale.row_index.ToString());
                        rowStr.Add("item_id", rowSale.item_id.ToString());
                        rowStr.Add("allocate_amount", rowSale.allocate_amount.ToString());
                        saleSheetRowsList.Add(rowStr);
                    }
                    sheetAttribute.Add("SaleSheetRows", JsonConvert.SerializeObject(saleSheetRowsList));
                }
                if (!string.IsNullOrEmpty(JsonConvert.SerializeObject(SaleFeeSheetRows)))
                {
                    sheetAttribute.Add("SaleFeeSheetRows", JsonConvert.SerializeObject(SaleFeeSheetRows));
                }
                if (fromSaleSheet) from_sale_sheet = fromSaleSheet;
                if (!string.IsNullOrEmpty(from_sale_sheet.ToString().ToLower()))
                {
                    sheetAttribute.Add("from_sale_sheet", from_sale_sheet.ToString().ToLower());
                }

                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set//读取数据库，返回前端
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.sale_sheet_id != null)
                    {
                        this.sale_sheet_id = sheetAttr.sale_sheet_id;
                    }
                    if (sheetAttr.total_fee_amount != null)
                    {
                        this.total_fee_amount = sheetAttr.total_fee_amount;
                    }
                    if (sheetAttr.SaleSheetRows != null)
                    {
                        this.SaleSheetRows = JsonConvert.DeserializeObject<List<SheetRowSale>>(sheetAttr.SaleSheetRows.ToString());
                    }
                    if (sheetAttr.SaleFeeSheetRows != null)
                    {
                        this.SaleFeeSheetRows = JsonConvert.DeserializeObject<List<SheetRowSaleFeeApportion>>(sheetAttr.SaleFeeSheetRows.ToString());
                    }
                    if (sheetAttr.from_sale_sheet != null)
                    {
                        this.from_sale_sheet = Convert.ToBoolean(sheetAttr.from_sale_sheet);
                    }
                }
            }
        }


        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            //string sql = "";

        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);

        }

        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.sheet_id + "_" + this.OperID + "_" + this.make_brief + "_"  + this.total_fee_amount + "_" + this.sale_sheet_id + "_";
            foreach (var row in SaleFeeSheetRows)
            {
                res += row.fee_sub_id + "_" + row.fee_sub_amount + row.remark;
            }
            return res;
        }


        public async Task<string> CheckBeforeSaveOrApprove(CMySbCommand cmd)
        {
            //check1. 保存时销售单是否已红冲/已分摊过（分摊单已审核），分摊时间是否晚于销售时间
            if (!fromSaleSheet)
            {
                dynamic salesheet = await CDbDealer.Get1RecordFromSQLAsync($@"select sm.sheet_no, sm.approve_time, sm.red_flag, fm.sheet_id as fa_sheet_id, fm.sheet_no as fa_sheet_no, fm.approve_time as fa_approve_time from sheet_sale_main sm 
                    left join sheet_sale_fee_apportion_main fm on sm.company_id=fm.company_id and coalesce((sm.sheet_attribute->'saleFeeSheetInfo'->>'sale_fee_apportion_sheet_id')::numeric,0)=fm.sheet_id
                    where sm.company_id={company_id} and sm.sheet_id={sale_sheet_id};", cmd);
                if (salesheet.red_flag != "") return $"销售单【{salesheet.sheet_no}】已红冲，请重新选择";
                if (sheet_id != "" && salesheet.fa_approve_time != "") return $"销售单【{salesheet.sheet_no}】已分摊至销售费用分摊单【{salesheet.fa_sheet_no}】，请重新选择";//同一销售单可以在多个分摊单保存，但审核只能审核一次
                if ((happen_time == "" ? DateTime.Now : Convert.ToDateTime(happen_time)) < Convert.ToDateTime(salesheet.approve_time)) return "分摊单交易时间不能早于销售单审核时间";
            }

            //check2. 检查分摊金额是否不为负
            for (int i = 0; i < SaleSheetRows.Count(); i++)
            {
                if (SaleSheetRows[i].allocate_amount < 0) return $"销售单 第{i + 1}行 分摊金额不能为负";
            }

            //check3. 保存时费用支出单是否已红冲，是否已用于分摊，分摊时间是否晚于费用单时间
            if (SaleFeeSheetRows.Where(r => r.sale_fee_sheet_id.IsValid()).Count() > 0)
            {
                dynamic oldfeesheets = await CDbDealer.GetRecordsFromSQLAsync($"select sheet_no, sheet_id, approve_time, red_flag, sheet_attribute->>'sale_fee_apportion_sheet_id' as sale_fee_apportion_sheet_id, sheet_attribute->>'fee_apportion_sheet_no' as sale_fee_apportion_sheet_no from sheet_fee_out_main where company_id={company_id} and sheet_id in ( {String.Join(',', SaleFeeSheetRows.Where(r => r.sale_fee_sheet_id.IsValid()).Select(r => r.sale_fee_sheet_id).ToList())} )", cmd);
                foreach (dynamic row in oldfeesheets)
                {
                    if (row.red_flag != "") return $"销售费用单【{row.sheet_no}】已红冲，请重新选择";
                    if (row.sale_fee_apportion_sheet_id != "") return $"费用支出单【{row.sheet_no}】已分摊至销售费用分摊单【{row.sale_fee_apportion_sheet_no}】，请重新选择";
                    if (!fromSaleSheet)
                    {
                        if ((happen_time == "" ? DateTime.Now : Convert.ToDateTime(happen_time)) < Convert.ToDateTime(row.approve_time)) return "分摊单交易时间不能早于费用支出单审核时间";
                    }
                }
            }

            return "";
        }

		public override async Task<string> OnSheetBeforeSave(CMySbCommand cmd, CInfoForApproveBase info)
		{
            //保存：分摊单保存主表（销售单信息，分摊单信息都存在分摊单的sheet_attribute里），详表只存sale_sheet

            string msg = "";
        

            msg = await CheckBeforeSaveOrApprove(cmd);
            if (msg != "") return msg;

            //save1. 分摊单保存
            SheetRowSaleFeeApportion rowFA = new SheetRowSaleFeeApportion();
            rowFA.relate_sheet_type = "X";
            rowFA.relate_sheet_id = sale_sheet_id;//销售单必选才能保存
            rowFA.row_index = 0;
            SheetRows.Add(rowFA);
             
            return msg;
        }

        public override async Task<string> OnSheetBeforeApprove(CMySbCommand cmd, CInfoForApproveBase info)
        {
            string msg = "";
            string sql = "";
            
            msg = await CheckBeforeSaveOrApprove(cmd);
            if (msg != "") return msg;

            //approve1. 销售明细回写
            SheetRowSaleFeeApportion rowToDB = new SheetRowSaleFeeApportion();
            rowToDB.relate_sheet_type = "X";
            rowToDB.relate_sheet_id = sale_sheet_id;
            rowToDB.row_index = 0;
            SheetRows.Add(rowToDB);
            if (!fromSaleSheet)
            {
                foreach (SheetRowSale rowSale in SaleSheetRows)
                {
                    sql += $"update sheet_sale_detail set allocate_amount=({rowSale.allocate_amount}) where company_id={company_id} and sheet_id={sale_sheet_id} and row_index={rowSale.row_index} and item_id={rowSale.item_id};";
                }
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }

            //approve2. 新增费用单的审核
            //暂时无法合并生成新的费用单，因为每一行支付方式单独统计，如果超过了两种支付方式无法匹配费用单
            //两个两个合并不如直接一行做一个，逻辑简单清晰
            int rowIndex = 1;//X明细行是0，ZC行从1开始
            for (int i = 0; i < SaleFeeSheetRows.Count; i++)
            {
                SheetRowSaleFeeApportion rowFee = SaleFeeSheetRows[i];
                rowToDB = new SheetRowSaleFeeApportion();
                rowToDB.relate_sheet_type = "ZC";
                rowToDB.row_index = rowIndex;

                if (rowFee.sale_fee_sheet_id.IsInvalid())
                {
                    SheetFeeOut newfeesheet = new SheetFeeOut();
                    newfeesheet.SheetType = "ZC";
                    newfeesheet.money_inout_flag = -1;
                    newfeesheet.company_id = company_id;
                    newfeesheet.OperID = OperID;
                    newfeesheet.happen_time = happen_time;
                    newfeesheet.supcust_id = rowFee.supcust_id == null ? "" : rowFee.supcust_id;
                    newfeesheet.payway1_id = rowFee.payway1_id;
                    newfeesheet.getter_id = seller_id.ToString();
                    if (rowFee.payway1_id.IsInvalid())//如果走欠款，没选支付方式，就取一个现金银行类型的刻模具
                    {
                        cmd.CommandText = $"select v from ({CommonTool.SelectPayWayNormal.Replace("~COMPANY_ID", company_id).Replace("~OPER_ID", OperID)}) t limit 1";
                        newfeesheet.payway1_id = (await cmd.ExecuteScalarAsync()).ToString();
                    }
                    if (newfeesheet.payway1_id.IsInvalid() || newfeesheet.payway1_id == "-1") return $"第{i + 1}行 费用支出单请指定支付方式";
                    newfeesheet.payway1_amount = Convert.ToDecimal(rowFee.payway1_amount == "" ? "0" : rowFee.payway1_amount);
                    newfeesheet.now_pay_amount = Convert.ToDecimal(rowFee.payway1_amount == "" ? "0" : rowFee.payway1_amount);
                    newfeesheet.total_amount = Convert.ToDecimal(rowFee.fee_sub_amount);
                    newfeesheet.make_brief = rowFee.fee_make_brief;
                    SheetRowFeeOut newrow = new SheetRowFeeOut();
                    newrow.fee_sub_id = rowFee.fee_sub_id;
                    newrow.fee_sub_amount = Convert.ToDecimal(rowFee.fee_sub_amount);
                    newfeesheet.SheetRows.Add(newrow);

                    msg = await newfeesheet.SaveAndApprove(cmd, false);
                    if (msg != "") return msg;

                    SaleFeeSheetRows[i].sale_fee_sheet_id = newfeesheet.sheet_id;
                    SaleFeeSheetRows[i].sale_fee_sheet_no = newfeesheet.sheet_no;
                    rowToDB.relate_sheet_id = newfeesheet.sheet_id;
                    SheetRows.Add(rowToDB);
                }
                else
                {
                    if (SheetRows.Where(r => r.relate_sheet_id == rowFee.sale_fee_sheet_id).Count() == 0)
                    {
                        rowToDB.relate_sheet_id = rowFee.sale_fee_sheet_id;
                        SheetRows.Add(rowToDB);
                    }
                }
                rowIndex++;
            }
 
            return msg;
        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info)
        {
            await base.OnSheetIDGot(cmd, sheetID, info);
            cmd.ActiveDatabase = "";
            string sql = "";
            //CInfoForApprove info = (CInfoForApprove)info1;

            //红冲：
            //传入的sheetID是红冲单的sheet_id，info是原单的信息
            //1. 红冲分摊单（base已冲）
            //2. 撤销分摊单对销售单的影响
            //3. 撤销分摊单对旧的费用单的影响
            //4. 红冲新开的费用单



            //approve4/red2：重算成本价
            SheetSale sheetSale = new SheetSale();
            await sheetSale.Load(cmd, company_id, sale_sheet_id);
            string dealing_happen_time = happen_time;
            //红冲销售单时：
            //分摊单时间如果与销售单相同，销售单重算逻辑会带上分摊单，分摊单重算逻辑不带销售单，因为销售单先于分摊单被红冲，等分摊单跑到重算逻辑时已经查不到销售单了；
            //分摊单时间如果与销售单不同，销售单重算必定带上分摊单，分摊单重算必定不带销售单，因为分摊单时间必定晚于销售单。
            //红冲分摊单时：
            //分摊单时间如果与销售单相同，分摊单重算逻辑会带上销售单，但不影响结果；
            //分摊单时间如果与销售单不同，分摊单重算必定不带销售单。

            sql = "";
            SaleFeeSheetInfo saleFeeSheetInfo = new SaleFeeSheetInfo();
            saleFeeSheetInfo.sheetRowSaleFeeAppoList = SaleFeeSheetRows;
            saleFeeSheetInfo.total_fee_amount = Convert.ToDecimal(total_fee_amount);
            saleFeeSheetInfo.sale_fee_apportion_sheet_id = sheetID;
            saleFeeSheetInfo.sale_fee_apportion_sheet_no = sheet_no;
            saleFeeSheetInfo.status = SHEET_SALE_FEE_APPORTION.APPROVE;
            if (red_flag == "")//approve5：采购单回写sheet_attribute
            {
                sql += $"update sheet_sale_main set sheet_attribute = jsonb_set(COALESCE(sheet_attribute, '{{}}'::jsonb), '{{saleFeeSheetInfo}}','{JsonConvert.SerializeObject(saleFeeSheetInfo)}', true) where company_id = {company_id} and sheet_id={sale_sheet_id};";
            }
            else//red3：采购单主表移除sheet_attribute/feeSheetInfo，采购单详表移除分摊金额
            {
                sql += $@"update sheet_sale_main set sheet_attribute=sheet_attribute::jsonb - 'saleFeeSheetInfo' where company_id={company_id} and sheet_id={sale_sheet_id};
                            update sheet_sale_detail set allocate_amount=null where company_id={company_id} and sheet_id={sale_sheet_id};";
            }

            fromSaleFeeAppoSheet = true;
            foreach (SheetRowSaleFeeApportion feeFA in SaleFeeSheetRows)
            {
                if (red_flag == "")//approve6：费用单回写sheet_attribute
                {
                    sql += $@"update sheet_fee_out_main set sheet_attribute = jsonb_set(COALESCE(sheet_attribute, '{{}}'::jsonb), '{{sale_sheet_id}}','{sale_sheet_id}', true) where company_id = {company_id} and sheet_id={feeFA.sale_fee_sheet_id};
                                    update sheet_fee_out_main set sheet_attribute = jsonb_set(COALESCE(sheet_attribute, '{{}}'::jsonb), '{{sale_fee_apportion_sheet_id}}','{sheetID}', true) where company_id = {company_id} and sheet_id={feeFA.sale_fee_sheet_id};
                                    update sheet_fee_out_main set sheet_attribute = jsonb_set(COALESCE(sheet_attribute, '{{}}'::jsonb), '{{sale_fee_apportion_sheet_no}}','""{sheet_no}""', true) where company_id = {company_id} and sheet_id={feeFA.sale_fee_sheet_id};";
                }
                else//red4. 费用单移除sheet_attribute/sale_sheet_id，sale_fee_apportion_sheet_id，fee_apportion_sheet_no
                {
                    sql += $@"update sheet_fee_out_main set sheet_attribute=sheet_attribute::jsonb - 'sale_sheet_id' where company_id={company_id} and sheet_id={feeFA.sale_fee_sheet_id};
                                    update sheet_fee_out_main set sheet_attribute=sheet_attribute::jsonb - 'sale_fee_apportion_sheet_id' where company_id={company_id} and sheet_id={feeFA.sale_fee_sheet_id};
                                    update sheet_fee_out_main set sheet_attribute=sheet_attribute::jsonb - 'sale_fee_apportion_sheet_no' where company_id={company_id} and sheet_id={feeFA.sale_fee_sheet_id};";
                    if (feeFA.is_new)//red5. 红冲分摊单新开的费用单
                    {
                        SheetFeeOut feeSheet = new SheetFeeOut();
                        if (fromSaleFeeAppoSheet) feeSheet.allowRedByApportion = true;//fromSaleFeeAppoSheet=true表示从分摊单红冲过来的，可以红冲费用单
                        info.ErrMsg = await feeSheet.Red(cmd, company_id, feeFA.sale_fee_sheet_id, OperID, $"红冲分摊单【{sheet_no}】", false);
                        if (info.ErrMsg != "") return;
                    }
                }
            }

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
        }
    }


    #endregion
}
