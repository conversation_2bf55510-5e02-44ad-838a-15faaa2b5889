using ArtisanManage.Models;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Pages.Sheets;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using ArtisanManage.MyJXC;
using System.Text;
using System.Threading.Tasks;
using static ArtisanManage.AppController.SheetVisitController;
using static ArtisanManage.Services.CommonTool;
using System.ComponentModel.Design;

namespace ArtisanManage.AppController
{
    /// <summary>
    /// 客户
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class AppOrderManageController : BaseController
    { 

        public AppOrderManageController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        public async Task<JsonResult> GetAllOrders(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string searchStr2, string regionID, string currentLat, string currentLng, string supcustFlag, string operRegions, string tabName, string startDate, string endDate, string operID, string departID, string branchID, string senderID, string sellerID,string querySheetTypeValue,bool showRed)
        {
            if (supcustFlag.IsInvalid()) supcustFlag = "'C','CS'";
            var condition = $" and supcust_flag in ({supcustFlag}) and (s.status = '1' or s.status is null) ";


            if (operRegions.IsValid())
            {
                var regions = JsonConvert.DeserializeObject<int[]>(operRegions);

                //判断用户是否负责全部片区
                string regionSql = $@"SELECT DISTINCT ir.region_id FROM info_region ir WHERE ir.company_id = {Token.CompanyID} AND ir.mother_id = 0";
                dynamic root_region = await CDbDealer.Get1RecordFromSQLAsync(regionSql, cmd);
                int root = int.Parse(root_region.region_id);
                bool root_region_flag = regions.Contains(root);

                if (regions.Length > 0 && !root_region_flag)
                    //condition += " and (" + string.Join(" or ", regions.Select(x => $"other_region  like '%/{x}/%'")) + ") ";
                    condition += " and (" + string.Join(" or ", regions.Select(x => $"position('/{x}/' in other_region)>0")) + ") ";
            }
            if (querySheetTypeValue == "XD")
            {
                condition += "and sm.sheet_type='XD' ";
            }
            else if (querySheetTypeValue == "TD") 
            {
                condition += "and sm.sheet_type='TD' ";
            }

            var orderBy = "order by  sm.approve_time desc, sm.happen_time desc";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%'  or s.mobile ilike '%{searchStr}%' ) ";
            if (searchStr2.IsValid()) condition += $" and (sheet_no ilike '%{searchStr2}%' or sup_name ilike '%{searchStr2}%') ";//
            if (regionID.IsValid())
            {
                string sqlRegion = $@"WITH RECURSIVE SubRegions AS (
 
    SELECT
        region_id,
        mother_id,
        1 AS depth  
    FROM
        info_region
    WHERE
        region_id in ({regionID}) and company_id={Token.CompanyID}

    UNION ALL

 
    SELECT
        i.region_id,
        i.mother_id,
        sc.depth + 1  
    FROM
        info_region i
    INNER JOIN SubRegions sc ON i.mother_id = sc.region_id
    WHERE
        company_id={Token.CompanyID} and sc.depth < 5  
)
SELECT * FROM SubRegions;";
                var regions=await CDbDealer.GetRecordsFromSQLAsync(sqlRegion, cmd);
               // string regionCondi = "";
              
                string regionIds = String.Join(",", regions.Select(r => ((dynamic)r).region_id));
                if(regionIds!="")
                   condition += $" and s.region_id in ({regionIds})";

                /* var lstRegion = regionID.Split(',');

              string regionCondi = "";
               foreach (var re in lstRegion)
               {
                   string sql = $"select ";
                   if (regionCondi != "") regionCondi += " or ";
                   regionCondi += $" other_region ilike '%/{re}/%' ";
               }
               condition += $" and ({regionCondi})";
               */
            }

            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);

            queryTime += $" and sm.happen_time >= '{startDate}'";
            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and sm.happen_time <= '{endDate}' ";
            }

           
            if (operID.IsValid()) condition += $" and ( sm.seller_id = {operID} or coalesce(oss.senders_id,sm.senders_id) like '%{operID}%'  )";
            if (departID.IsValid()) condition += $" and io.depart_path like '%{"/" + departID + "/"}%'";
            if (sellerID.IsValid()) condition += $" and sm.seller_id = {sellerID}";
            if (senderID.IsValid()) condition += $" and coalesce(oss.senders_id,sm.senders_id) like '%{senderID}%'";
            if (branchID.IsValid()) condition += $" and sm.branch_id = {branchID}";
            if (!showRed)  condition  += $" and sm.red_flag is null";//未红冲
            var queryCondition = "";
            string sqlForSetting = $"select setting from company_setting where company_id = '{Token.CompanyID}'";
            cmd.CommandText = sqlForSetting;
            var jsonSetting = await cmd.ExecuteScalarAsync();
            dynamic companySetting=null;
            if (jsonSetting != null)
            {
                companySetting = JsonConvert.DeserializeObject(jsonSetting.ToString());
            }
            bool reviewOrderBeforeAssignVan = false;
            if (companySetting != null && companySetting.reviewOrderBeforeAssignVan != null) reviewOrderBeforeAssignVan = companySetting.reviewOrderBeforeAssignVan == "True"?true:false;
            if (tabName.IsValid())
            {
                if (tabName == "approve")
                {
                    queryCondition = "and sm.approve_time is  null";
                }
                else if(tabName == "print")
                {
                    queryCondition = "and sm.approve_time is not  null and (oss.sheet_print_count is null  or oss.sheet_print_count =0)  and (oss.order_status is null or oss.order_status in ('xd')) ";
                }
                 else if(tabName == "review")
                {
                    queryCondition = "and sm.review_time is null ";
                }
                else if (tabName == "assign")
                {
                    if (reviewOrderBeforeAssignVan)
                    {
                        queryCondition = "and sm.approve_time is not  null and sm.review_time is not null and op.sale_order_sheet_id is null  and (oss.order_status is null or oss.order_status in ('xd','dd'))";
                    }
                    else
                    {
                        queryCondition = "and sm.approve_time is not  null and op.sale_order_sheet_id is null  and (oss.order_status is null or oss.order_status in ('xd','dd'))";
                    }
                    
                }
                else if (tabName == "transfer")
                {
                    queryCondition = "and (m.sale_sheet_id is  null or m.approve_time is null ) and sm.approve_time is not  null and oss.receipt_status is null ";
                }
                else if (tabName == "transfered")
                {
                    //queryCondition = "and (m.sale_sheet_id is not null or m.approve_time is not null ) and sm.approve_time is not  null and oss.receipt_status is null ";
                    queryCondition = "and m.sale_sheet_id is not null ";
                }
                else if (tabName == "account")
                {
                    queryCondition = "and m.approve_time is not null and m.sale_sheet_id is not null and ca.sheet_id is null";
                }
                else if (tabName == "reject")
                {
                    queryCondition = "and oss.receipt_status in ('js','bf')";
                }

            }


            SQLQueue QQ = new SQLQueue(cmd);
            var sql_NoLimit =
@$"
SELECT
    s.supcust_id, s.addr_lng,s.addr_lat,s.sup_addr,
    sup_name, 
    sm.sheet_id AS order_sheet_id,
    sm.sheet_no AS order_sheet_no,
    sm.sheet_type as order_sheet_type,
    b.branch_name,
    m.sale_sheet_id,
    m.sale_sheet_no,
    ca.sheet_id account_sheet_id,
    m.approve_time sale_time,
    sm.seller_id,
    sm.make_brief,sm.order_source,
    io.oper_name as seller_name,
    (case when pb.bill_status = 'paid' then '线上支付' when pb.bill_status = 'return' then '线上退款' else '' end) as pay_status,
    (case when m.senders_name is not null then m.senders_name else case when oss.senders_name is null then sm.senders_name else oss.senders_name end end
    ) as senders_name,
    sm.approve_time,
	(case when sm.red_flag = '1' then 'reded' when sm.red_flag = '2' then 'red' else oss.order_status end) as order_status,
    oss.sheet_print_count,
    oss.receipt_status,
    case when oss.print_time is null then '未打单' else '已打单' end  print_status,	
    car.branch_name van_name,
    s.mobile AS sup_tel,
    sm.happen_time,
    sm.total_amount*sm.money_inout_flag total_amount, m.s_total_amount, m.s_now_pay_amount, m.s_left_amount
FROM
	sheet_sale_order_main sm
LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id = {Token.CompanyID}
LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and io.company_id = {Token.CompanyID}
LEFT JOIN sheet_status_order oss ON sm.sheet_id = oss.sheet_id and oss.company_id = {Token.CompanyID}
LEFT JOIN 
(
    select order_sheet_id, sheet_id as sale_sheet_id,red_flag, total_amount * money_inout_flag as s_total_amount,now_pay_amount * money_inout_flag as s_now_pay_amount, (total_amount-now_pay_amount-now_disc_amount)*money_inout_flag as s_left_amount,  sheet_no as sale_sheet_no,senders_id,senders_name,approve_time from sheet_sale_main where company_id = '{Token.CompanyID}' and happen_time>='{startDate}' and red_flag is null 
) m on sm.sheet_id = m.order_sheet_id               
LEFT JOIN info_branch b on b.company_id = {Token.CompanyID} and b.branch_id = sm.branch_id
LEFT JOIN info_branch car on car.company_id = {Token.CompanyID} and car.branch_id = oss.van_id 
LEFT JOIN op_move_to_van_detail op on op.company_id = {Token.CompanyID} and op.op_id = oss.assign_van_id and op.sale_order_sheet_id = oss.sheet_id
LEFT JOIN (
   SELECT cd.* FROM sheet_check_sheets_main cm 
   LEFT JOIN sheet_check_sheets_detail cd on cm.company_id = cd.company_id and cm.sheet_id = cd.sheet_id 
   WHERE cm.company_id = '{Token.CompanyID}' and  happen_time>='{startDate}' and red_flag is null
) ca on ca.business_sheet_id = m.sale_sheet_id
LEFT JOIN pay_bill pb on pb.company_id= '{Token.CompanyID}' and pb.bill_id = sm.pay_bill_id and pb.sheet_type = sm.sheet_type and pb.sheet_id = sm.sheet_id
where sm.company_id = '{Token.CompanyID}'
{queryCondition}
and coalesce(sm.is_del, false) = false

{condition} 
{queryTime}
";

            var sql = $"{sql_NoLimit}  {orderBy} limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);


            if (getTotal)
            {
                sql = $"select count(*) as total,sum(t.total_amount) as total_sale_amount,sum(t.s_total_amount) as sum_s_total_amount,sum(t.s_now_pay_amount) as sum_s_now_pay_amount,sum(t.s_left_amount) as sum_left_amount from ({sql_NoLimit}) t";
                QQ.Enqueue("count", sql);
            }

            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> data = null;
            string total = "";
            string totalSaleAmount = "";
            string sum_s_total_amount = "";
            string sum_s_now_pay_amount = "";
            string sum_total_sale_amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);

                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    totalSaleAmount = CPubVars.GetTextFromDr(dr, "total_sale_amount");
                    sum_s_total_amount = CPubVars.GetTextFromDr(dr, "sum_s_total_amount");
                    sum_s_now_pay_amount = CPubVars.GetTextFromDr(dr, "sum_s_now_pay_amount");
                    sum_total_sale_amount = CPubVars.GetTextFromDr(dr, "sum_total_sale_amount");

                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", msg = "", data, total, totalSaleAmount });
        }
        [HttpGet]
        public async Task<JsonResult> GetOrdersForApprove(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string regionID, string currentLat, string currentLng, string supcustFlag, string operRegions, bool isReceipted, string startDate, string endDate, string operID,string departID,string branchID,string senderID,string sellerID)
        {
            SqlResult sqlResult = GetOrdersForApprove_SQL(cmd, Token, getTotal, true, pageSize, startRow, searchStr, regionID, currentLat, currentLng, operRegions, isReceipted, startDate, endDate, operID, departID, branchID, senderID, sellerID);
            return await MiniSQLParser.RunSqlResult(cmd, sqlResult, getTotal);
        }

        public static SqlResult GetOrdersForApprove_SQL(CMySbCommand cmd, Token token, bool getCount, bool getRows, int pageSize, int startRow, string searchStr, string regionID, string currentLat, string currentLng, string operRegions, bool isReceipted, string startDate, string endDate,string operID, string departID, string branchID, string senderID, string sellerID)
        {        
            var condition = $"  and (s.status = '1' or s.status is null) ";
            if (operRegions.IsValid())
            {
                var regions = JsonConvert.DeserializeObject<int[]>(operRegions);
                if (regions.Length > 0)
                    condition += " and (" + string.Join(" or ", regions.Select(x => $"other_region  like '%/{x}/%'")) + ") ";
            }
 
            var orderBy = "";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%'  or s.mobile ilike '%{searchStr}%' ) ";
            if (regionID.IsValid())
            {
                var lstRegion = regionID.Split(',');
                string regionCondi = "";
                foreach (var re in lstRegion)
                {
                    if (regionCondi != "") regionCondi += " or ";
                    regionCondi += $" other_region ilike '%/{re}/%' ";
                }
                condition += $" and ({regionCondi})";
            }
          
            orderBy += "order by sm.happen_time desc";

            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);

            queryTime += $" and sm.happen_time >= '{startDate}'";
            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and sm.happen_time <= '{endDate}' ";
            }
            

            if (operID.IsValid()) condition += $" and  sm.seller_id = {operID}";
            if (departID.IsValid()) condition += $" and io.depart_path like '%{"/" + departID + "/"}%'";
            if (sellerID.IsValid()) condition += $" and sm.seller_id = {sellerID}";
            if (senderID.IsValid()) condition += $" and (oss.senders_id like '%{senderID}%' or sm.senders_id like '%{senderID}%')";
            if (branchID.IsValid()) condition += $" and sm.branch_id = {branchID}";

            var queryStatus = "and approve_time is null ";
            if (isReceipted) queryStatus = "and approve_time is not null ";
              
            var sql_NoLimit =
@$"
SELECT
    s.supcust_id,
    s.addr_lng,
    s.addr_lat,
    s.sup_addr,
    sup_name,
    acct_cust_id,
    acct_cust_name,
    sm.sheet_id AS order_sheet_id,
    sm.sheet_no AS order_sheet_no,
    sm.sheet_type as order_sheet_type,
    b.branch_name,
    m.sale_sheet_id,
    m.sale_sheet_no,
    sm.seller_id,
    sm.make_brief,sm.order_source,
    io.oper_name as seller_name,
    (case when m.senders_name is not null then m.senders_name else case when oss.senders_name is null then sm.senders_name else oss.senders_name end end) as senders_name,
    sm.approve_time,
	oss.order_status,    
    oss.receipt_status,
    s.mobile AS sup_tel,
    sm.happen_time,
    sm.total_amount,
    realtime->>'lastSaleTime' as last_sale_time,realtime->>'lastVisitTime' as last_visit_time
    
FROM
	sheet_sale_order_main sm
LEFT JOIN info_supcust s ON s.company_id = '{token.CompanyID}' and  sm.supcust_id = s.supcust_id
LEFT JOIN info_operator io ON io.company_id = '{token.CompanyID}' and  sm.seller_id = io.oper_id
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = '{token.CompanyID}'
) A ON s.acct_cust_id = A.a_supcust_id
LEFT JOIN sheet_status_order oss ON sm.sheet_id = oss.sheet_id and oss.company_id={token.CompanyID}
LEFT JOIN 
(
    select order_sheet_id, sheet_id as sale_sheet_id,red_flag, sheet_no as sale_sheet_no,senders_id,senders_name from sheet_sale_main where red_flag IS NULL and company_id = '{token.CompanyID}' and happen_time>='{startDate}'
) m on sm.sheet_id = m.order_sheet_id
left join realtime_supcust r on sm.supcust_id=r.supcust_id and r.company_id={token.CompanyID}                     
LEFT JOIN info_branch b on b.company_id = {token.CompanyID} and b.branch_id = sm.branch_id  
where sm.company_id = '{token.CompanyID}'
{queryStatus}
and sm.red_flag is null
and m.red_flag is null
and coalesce(sm.is_del, false) = false
{condition}
{queryTime}
";

            return MiniSQLParser.GetSQLResult(sql_NoLimit, getCount, getRows, orderBy, pageSize, startRow);

        }

        [HttpGet]
        public async Task<JsonResult> GetOrdersForReview(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string regionID, string currentLat, string currentLng, string supcustFlag, string operRegions, bool isReceipted, string startDate, string endDate, string operID, string departID, string branchID, string senderID, string sellerID)
        {
            SqlResult sqlResult = GetOrdersForReview_SQL(cmd, Token, getTotal, true, pageSize, startRow,  searchStr, regionID,  currentLat, currentLng, operRegions, isReceipted, startDate, endDate,  departID, branchID, senderID, sellerID);
            return await MiniSQLParser.RunSqlResult(cmd, sqlResult, getTotal);
        }

        public static SqlResult GetOrdersForReview_SQL(CMySbCommand cmd, Token token, bool getCount, bool getRows, int pageSize, int startRow, string searchStr, string regionID, string currentLat, string currentLng, string operRegions, bool isReceipted, string startDate, string endDate, string departID, string branchID, string senderID, string sellerID)
        { 
            var condition = $" and (s.status = '1' or s.status is null) ";
            if (operRegions.IsValid())
            {
                var regions = JsonConvert.DeserializeObject<int[]>(operRegions);
                if (regions.Length > 0)
                    condition += " and (" + string.Join(" or ", regions.Select(x => $"other_region  like '%/{x}/%'")) + ") ";
            }

            var orderBy = "";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%'  or s.mobile ilike '%{searchStr}%' ) ";
            if (regionID.IsValid())
            {
                var lstRegion = regionID.Split(',');
                string regionCondi = "";
                foreach (var re in lstRegion)
                {
                    if (regionCondi != "") regionCondi += " or ";
                    regionCondi += $" other_region ilike '%/{re}/%' ";
                }
                condition += $" and ({regionCondi})";
               // condition += $" and  other_region ilike '%/{regionID}/%'";
            }


            orderBy += "order by om.happen_time desc";

            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);

            queryTime += $" and om.happen_time >= '{startDate}'";
            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and om.happen_time <= '{endDate}' ";
            }

            if (departID.IsValid()) condition += $" and io.depart_path like '%{"/" + departID + "/"}%'";
            if (sellerID.IsValid()) condition += $" and om.seller_id = {sellerID}";
            if (senderID.IsValid()) condition += $" and (oss.senders_id like '%{senderID}%' or om.senders_id like '%{senderID}%')";
            if (branchID.IsValid()) condition += $" and om.branch_id = {branchID}";

            var queryStatus = "and review_time is null ";
            if (isReceipted) queryStatus = "and review_time is not null ";


            SQLQueue QQ = new SQLQueue(cmd);


            var sql_NoLimit =
@$"
SELECT
    s.supcust_id,
    s.addr_lng,
    s.addr_lat,
    s.sup_addr,
    sup_name,
    acct_cust_id,
    acct_cust_name,
    om.sheet_id AS order_sheet_id,
    om.sheet_no AS order_sheet_no,
    om.sheet_type as order_sheet_type,
    b.branch_name,
    sm.sale_sheet_id,
    sm.sale_sheet_no,
    om.seller_id,
    om.make_brief,om.order_source,
    io.oper_name as seller_name,
    (case when sm.senders_name is not null then sm.senders_name else case when oss.senders_name is null then om.senders_name else oss.senders_name end end) as senders_name,
    om.approve_time,
	oss.order_status,    
    oss.receipt_status,
    s.mobile AS sup_tel,
    om.happen_time,
    om.total_amount,
    realtime->>'lastSaleTime' as last_sale_time,realtime->>'lastVisitTime' as last_visit_time
 
FROM
	sheet_sale_order_main om
LEFT JOIN info_supcust s ON s.company_id={token.CompanyID} and om.supcust_id = s.supcust_id 
LEFT JOIN info_operator io ON io.company_id={token.CompanyID} and om.seller_id = io.oper_id
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = '{token.CompanyID}'
) A ON s.acct_cust_id = A.a_supcust_id
LEFT JOIN sheet_status_order oss ON om.sheet_id = oss.sheet_id
LEFT JOIN 
(
    select order_sheet_id, sheet_id as sale_sheet_id,red_flag, sheet_no as sale_sheet_no,senders_id,senders_name,approve_time from sheet_sale_main where red_flag IS NULL and company_id = '{token.CompanyID}' and happen_time>='{startDate}'
) sm on om.sheet_id = sm.order_sheet_id
LEFT JOIN realtime_supcust r on s.supcust_id=r.supcust_id and s.company_id=r.company_id                      
LEFT JOIN info_branch b on om.company_id=b.company_id and b.branch_id = om.branch_id  
where om.company_id = '{token.CompanyID}' and sm.approve_time is null and (oss.order_status is null or oss.order_status in ('xd','dd'))
{queryStatus}
and om.red_flag is null 
and sm.red_flag is null
and coalesce(om.is_del, false) = false
{condition} 
{queryTime}
";
            return MiniSQLParser.GetSQLResult(sql_NoLimit, getCount, getRows, orderBy, pageSize, startRow);
        }

        [HttpGet]
        public async Task<JsonResult> GetOrdersForPrint(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string regionID, string currentLat, string currentLng, string supcustFlag, string operRegions, bool isReceipted, string startDate, string endDate, string operID, string departID, string branchID, string senderID, string sellerID)
        {
            SqlResult sqlResult = GetOrdersForPrint_SQL(cmd, Token, getTotal, true, pageSize, startRow, searchStr, regionID, currentLat, currentLng, operRegions, isReceipted, startDate, endDate, operID, departID, branchID, senderID, sellerID);
            return await MiniSQLParser.RunSqlResult(cmd, sqlResult, getTotal); 
        }
        public static SqlResult GetOrdersForPrint_SQL(CMySbCommand cmd, Token token, bool getCount, bool getRows, int pageSize, int startRow, string searchStr, string regionID, string currentLat, string currentLng, string operRegions, bool isReceipted, string startDate, string endDate,string operID, string departID, string branchID, string senderID, string sellerID)
        {
            var condition = $" and (s.status = '1' or s.status is null) ";
            if (operRegions.IsValid())
            {
                var regions = JsonConvert.DeserializeObject<int[]>(operRegions);
                if (regions.Length > 0)
                    condition += " and (" + string.Join(" or ", regions.Select(x => $"other_region  like '%/{x}/%'")) + ") ";
            }

            // var distanceSQL = "";
            var orderBy = "";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%'  or s.mobile ilike '%{searchStr}%' ) ";
            if (regionID.IsValid())
            {
                var lstRegion = regionID.Split(',');
                string regionCondi = "";
                foreach (var re in lstRegion)
                {
                    if (regionCondi != "") regionCondi += " or ";
                    regionCondi += $" other_region ilike '%/{re}/%' ";
                }
                condition += $" and ({regionCondi})";
                //condition += $" and  other_region ilike '%/{regionID}/%'";
            }
            if (!isReceipted && currentLng.IsValid() && currentLat.IsValid())
            {
                //distanceSQL += $",st_distance(ST_Transform(ST_SetSRID(addr_lnglat,4326)::geometry, 3857),ST_Transform('SRID=4326;POINT({currentLng} {currentLat})'::geometry, 3857)) as distance";
                //  orderBy += "order by distance ASC, sm.happen_time desc";
            }
            else
            {
                // orderBy += "order by sm.happen_time desc";
            }
            orderBy += "order by sm.happen_time desc";
            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);

            queryTime += $" and happen_time >= '{startDate}'";
            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and happen_time <= '{endDate}' ";
            }
                    
            if (departID.IsValid()) condition += $" and io.depart_path like '%{"/" + departID + "/"}%'";
            if (operID.IsValid()) condition += $" and sm.seller_id = {operID}";
            if (sellerID.IsValid()) condition += $" and sm.seller_id = {sellerID}";
            if (senderID.IsValid()) condition += $" and (oss.senders_id like '%{senderID}%' or sm.senders_id like '%{senderID}%')";
            if (branchID.IsValid()) condition += $" and sm.branch_id = {branchID}";
            var queryStatus = " and (oss.sheet_print_count is null  or oss.sheet_print_count =0)  and (oss.order_status is null or oss.order_status in ('xd')) ";
            if (isReceipted) queryStatus = " and oss.sheet_print_count >0";


            SQLQueue QQ = new SQLQueue(cmd);
            var sql_NoLimit =
@$"SELECT
    s.supcust_id,
    s.addr_lng,s.addr_lat,s.sup_addr,sup_name,acct_cust_id,acct_cust_name,sm.sheet_id AS order_sheet_id,sm.sheet_no AS order_sheet_no,
    sm.sheet_type as order_sheet_type,b.branch_name,
    m.sale_sheet_id,m.sale_sheet_no,sm.seller_id,sm.make_brief,sm.order_source,
    io.oper_name as seller_name,
    (case when m.senders_name is not null then m.senders_name else case when oss.senders_name is null then sm.senders_name else oss.senders_name end end) as senders_name,
    sm.approve_time,
    oss.sheet_print_count,
	oss.order_status,    
    oss.receipt_status,
    case when oss.print_time is null then '未打单' else '已打单' end  print_status,	
    s.mobile AS sup_tel,
    sm.happen_time,
    sm.total_amount,
    realtime->>'lastSaleTime' as last_sale_time,realtime->>'lastVisitTime' as last_visit_time
    
FROM
	(SELECT * FROM sheet_sale_order_main WHERE company_id = {token.CompanyID} and red_flag is null and approve_time is not null  {queryTime}  order by happen_time desc ) sm
LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id = '{token.CompanyID}'
LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and io.company_id = sm.company_id and io.company_id = '{token.CompanyID}' and sm.company_id = '{token.CompanyID}'
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = '{token.CompanyID}'
) A ON s.acct_cust_id = A.a_supcust_id and s.company_id = '{token.CompanyID}'
LEFT JOIN sheet_status_order oss ON sm.sheet_id = oss.sheet_id and sm.company_id = '{token.CompanyID}' and sm.company_id = oss.company_id 
LEFT JOIN 
(
    select order_sheet_id, sheet_id as sale_sheet_id,red_flag, sheet_no as sale_sheet_no,senders_id,senders_name from sheet_sale_main where red_flag IS NULL and company_id = '{token.CompanyID}' and happen_time>='{startDate}'
) m on sm.sheet_id = m.order_sheet_id  and sm.company_id = '{token.CompanyID}'
left join realtime_supcust r on sm.supcust_id=r.supcust_id and r.company_id = '{token.CompanyID}'                        
LEFT JOIN info_branch b on b.branch_id = sm.branch_id and b.company_id = '{token.CompanyID}'
where sm.company_id = '{token.CompanyID}'
{queryStatus}
and sm.red_flag is null 
and coalesce(sm.is_del, false) = false
and approve_time is not null
and m.red_flag is null 
{condition} 

";
            return MiniSQLParser.GetSQLResult(sql_NoLimit, getCount, getRows, orderBy, pageSize, startRow);

        }

        [HttpGet]
        public async Task<JsonResult> GetOrdersForAssignVan(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string regionID, string currentLat, string currentLng, string operRegions, bool isDone, string startDate, string endDate, string operID, string departID, string branchID, string senderID, string sellerID,bool is_sender,string sheetViewRange, bool isBoss, bool needReview, bool ignorePayFailedSheetOnAssignVan, string sheetType)
        {
            SqlResult sqlResult = GetOrdersForAssignVan_SQL(cmd, Token, getTotal, true, pageSize, startRow, searchStr, regionID, currentLat, currentLng, operRegions, isDone, startDate, endDate, departID, branchID, senderID, sellerID, is_sender, sheetViewRange, isBoss, needReview, ignorePayFailedSheetOnAssignVan, sheetType);
            Action<dynamic> act = (res) =>
            {
                decimal qty_b = 0m, qty_m = 0m, qty_s = 0m;
                string total_qty = res.total_qty;
                foreach (var s in total_qty.Split(','))
                {
                    string ss = s.Replace(" ", "");
                    int n = ss.IndexOf("退:");
                    if (n >= 0)
                    {
                        ss = ss.Substring(0, n);
                    }
                    ss = ss.Replace("销:", "");
                    
                    string[] arr = ss.Split("大");
                    if (arr.Length == 2)
                    {
                        var q = CPubVars.ToDecimal(arr[0]);
                        qty_b += q;
                        ss = arr[1];
                    }
                    arr = ss.Split("中");
                    if (arr.Length == 2)
                    {
                        var q = CPubVars.ToDecimal(arr[0]);
                        qty_m += q;
                        ss = arr[1];
                    }
                    arr = ss.Split("小");
                    if (arr.Length == 2)
                    {
                        var q = CPubVars.ToDecimal(arr[0]);
                        qty_s += q;
                    }
                }
                total_qty = "";
                if (qty_b > 0) total_qty += CPubVars.FormatMoney(qty_b) + "大";
                if (qty_m > 0) total_qty += CPubVars.FormatMoney(qty_m) + "中";
                if (qty_s > 0.001m) total_qty += CPubVars.FormatMoney(qty_s) + "小";
                res.total_qty = total_qty;
            };

            return await MiniSQLParser.RunSqlResult(cmd, sqlResult, getTotal,"total_qty",act);

        }
        /*
        public static SqlResult GetOrdersForAssignVan_SQL_old(CMySbCommand cmd, Token token, bool getCount, bool getRows, int pageSize, int startRow, string searchStr, string regionID, string currentLat, string currentLng, string operRegions, bool isDone, string startDate, string endDate, string departID, string branchID, string senderID, string sellerID, bool is_sender,bool isBoss, bool needReview)
        { 
            var condition = $" and (s.status = '1' or s.status is null) "; 
            var orderBy = "";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%'  or s.mobile ilike '%{searchStr}%' ) ";
            if (regionID.IsValid()) condition += $" and  other_region ilike '%/{regionID}/%'";
            orderBy += "order by om.happen_time desc";
            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);

            queryTime += $" and om.happen_time >= '{startDate}'";
            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and om.happen_time <= '{endDate}' ";
            }
                       
            if(!isBoss)
            {
                if (is_sender)
                {
                    condition += $" and (oss.senders_id like '%{token.OperID}%' or om.senders_id like '%{token.OperID}%' or om.seller_id = {token.OperID}) ";
                }
                else
                {
                    condition += $" and om.seller_id = {token.OperID}";
                }
            }           
             
            if (needReview) condition += $" and om.review_time is not null ";
            if (departID.IsValid()) condition += $" and io.depart_path like '%{"/" + departID + "/"}%'";
            if (sellerID.IsValid()) condition += $" and om.seller_id = {sellerID}";
            if (senderID.IsValid()) condition += $" and (oss.senders_id like '%{senderID}%' or om.senders_id like '%{senderID}%')";
            if (branchID.IsValid()) condition += $" and om.branch_id = {branchID}";

            var queryStatus = " and op.sale_order_sheet_id is null  and (oss.order_status is null or oss.order_status in ('xd','dd'))";
            if (isDone) queryStatus = " and op.sale_order_sheet_id is not null ";


            SQLQueue QQ = new SQLQueue(cmd);
            var sql_NoLimit =
@$"
SELECT
    s.supcust_id, s.addr_lng,s.addr_lat,s.sup_addr,
    sup_name, acct_cust_id,
    acct_cust_name,
    om.sheet_id AS order_sheet_id,
    om.sheet_no AS order_sheet_no,
    om.sheet_type as order_sheet_type,
    b.branch_name,
    sm.sale_sheet_id,sm.sale_sheet_no,
    om.seller_id,om.make_brief,om.order_source,
    io.oper_name as seller_name,
    (case when sm.senders_name is not null then sm.senders_name else case when oss.senders_name is null then om.senders_name else oss.senders_name end end
    ) as senders_name,
    om.approve_time order_time,
	oss.order_status,    
    oss.receipt_status,
    case when oss.print_time is null then '未打单' else '已打单' end  print_status,	
    car.branch_name van_name,
    s.mobile AS sup_tel,
    om.happen_time,
    om.total_amount,
    op.op_id,op_no,op.approve_time,op.happen_time move_time,om.total_quantity sheet_qty
FROM
	sheet_sale_order_main om
LEFT JOIN info_supcust s ON om.supcust_id = s.supcust_id and om.company_id = s.company_id
LEFT JOIN info_operator io ON om.seller_id = io.oper_id and io.company_id = om.company_id
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = '{token.CompanyID}'
) A ON s.acct_cust_id = A.a_supcust_id
LEFT JOIN sheet_status_order oss ON om.sheet_id = oss.sheet_id and oss.company_id = om.company_id
LEFT JOIN 
(
    select order_sheet_id, sheet_id as sale_sheet_id,red_flag, sheet_no as sale_sheet_no,senders_id,senders_name from sheet_sale_main where red_flag IS NULL and company_id = '{token.CompanyID}' and happen_time>='{startDate}'
) sm on om.sheet_id = sm.order_sheet_id                   
LEFT JOIN info_branch b on b.company_id = om.company_id and b.branch_id = om.branch_id
LEFT JOIN info_branch car on car.company_id = oss.company_id and car.branch_id = oss.van_id 
LEFT JOIN
(
    SELECT d.*,m.happen_time,case when d.move_sheet_id is not null then m.approve_time else m.approve_time end,
           mm.sheet_no move_sheet_no,case when m.op_no is null  then 'ZC'|| m.op_id  else m.op_no end op_no 
    FROM op_move_to_van_main m  
    LEFT JOIN op_move_to_van_detail d on m.company_id = d.company_id and m.op_id=d.op_id and m.happen_time >= '{startDate}'
    LEFT JOIN sheet_move_main mm on m.company_id = mm.company_id and d.move_sheet_id = mm.sheet_id and mm.happen_time >= '{startDate}'
    WHERE m.company_id = '{token.CompanyID}' and m.red_flag is  null  
) op on om.sheet_id = op.sale_order_sheet_id
where om.company_id = '{token.CompanyID}'
{queryStatus}
and om.red_flag is null 
and om.approve_time is not null
and sm.red_flag is null
{condition} 
{queryTime}
"; 
            return MiniSQLParser.GetSQLResult(sql_NoLimit, getCount, getRows, orderBy, pageSize, startRow, "string_agg(om.total_quantity,',') as total_qty");
        }
       */
        public static SqlResult GetOrdersForAssignVan_SQL(CMySbCommand cmd, Token token, bool getCount, bool getRows, int pageSize, int startRow, string searchStr, string regionID, string currentLat, string currentLng, string operRegions, bool isDone, string startDate, string endDate, string departID, string branchID, string senderID, string sellerID, bool is_sender,string sheetViewRange, bool isBoss, bool needReview,  bool ignorePayFailedSheetOnAssignVan, string sheetType = "all")
        {
            var condition = "";// $" and (s.status = '1' or s.status is null) ";
            var orderBy = "";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%'  or s.mobile ilike '%{searchStr}%' ) ";
            if (regionID.IsValid())
            {
                var lstRegion = regionID.Split(',');
                string regionCondi = "";
                foreach(var re in lstRegion)
                {
                    if (regionCondi != "") regionCondi += " or ";
                    regionCondi += $" other_region ilike '%/{re}/%' ";
                }
                condition += $" and ({regionCondi})";
            }
            orderBy = "order by om.happen_time desc";
            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);

            queryTime += $" and om.happen_time >= '{startDate}'";
            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and om.happen_time <= '{endDate}' ";
            }
             
            if (sheetViewRange == null || sheetViewRange == "default")
                sheetViewRange = isBoss ? "all" : "self";

            if (sheetViewRange=="self")
            {
                if (is_sender)
                {
                    condition += $" and (oss.senders_id like '%{token.OperID}%' or om.senders_id like '%{token.OperID}%' or om.seller_id = {token.OperID}) ";
                }
                else
                {
                    condition += $" and om.seller_id = {token.OperID}";
                }
            }
            else if (sheetViewRange == "department")
            {
				if (departID.IsValid()) condition += $" and io.depart_path like '%{"/" + departID + "/"}%'";
			}

            if (needReview) condition += $" and om.review_time is not null ";
            
            if (ignorePayFailedSheetOnAssignVan) condition += $" and (pb.bill_status = 'paid' or pb.bill_status is null)";

            if (sellerID.IsValid()) condition += $" and om.seller_id = {sellerID}";
            if (senderID.IsValid()) condition += $" and (oss.senders_id like '%{senderID}%' or om.senders_id like '%{senderID}%')";
            if (branchID.IsValid()) condition += $" and om.branch_id = {branchID}";
            if(sheetType.IsValid() && sheetType != "all") condition += $" and om.sheet_type = '{sheetType}'";

            var queryStatus = " and (oss.order_status is null or oss.order_status in ('xd','dd'))";
         


            SQLQueue QQ = new SQLQueue(cmd);
            string sql_NoLimit;
            if(isDone)
            {
                queryStatus = "";
                //orderBy = "order by omm.happen_time desc";
                orderBy = "order by om.happen_time desc";
                sql_NoLimit = $@"SELECT om.supcust_id, s.addr_lng,s.addr_lat,s.sup_addr,s.sup_name,s.acct_cust_id,A.acct_cust_name,om.sheet_id order_sheet_id, om.sheet_no order_sheet_no,om.sheet_type order_sheet_type,b.branch_name,om.seller_id , io.oper_name seller_name, om.make_brief, om.order_source,  (case when oss.senders_name is null then om.senders_name else oss.senders_name end) as senders_name, 
om.approve_time order_time,oss.order_status,oss.receipt_status, case when oss.print_time is null then '未打单' else '已打单' end  print_status,	car.branch_name van_name,s.mobile sup_tel,
om.happen_time, om.total_amount,om.total_quantity sheet_qty,
case when oss.assign_van_id is null then optv.move_to_van_op_id else omm.op_id end op_id,
case when oss.assign_van_id is null then optv.move_to_van_op_no else omm.op_no end op_no,
case when oss.assign_van_id is null then optv.happen_time else omm.happen_time end move_time,
case when oss.assign_van_id is null then optv.approve_time else omm.approve_time end approve_time


FROM sheet_sale_order_main om 
LEFT JOIN sheet_status_order oss on oss.company_id = {token.CompanyID} and om.sheet_id = oss.sheet_id 
LEFT JOIN info_supcust s on s.company_id = {token.CompanyID} and s.supcust_id = om.supcust_id 
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = '{token.CompanyID}'
) A ON s.acct_cust_id = A.a_supcust_id

LEFT JOIN info_branch b on b.company_id = {token.CompanyID} and b.branch_id = om.branch_id
LEFT JOIN info_operator io on io.company_id = {token.CompanyID} and io.oper_id = om.seller_id
LEFT JOIN info_branch car on car.company_id = {token.CompanyID} and car.branch_id = oss.van_id
LEFT JOIN op_move_to_van_main omm on omm.company_id = oss.company_id and omm.op_id = oss.assign_van_id 
LEFT JOIN (
SELECT * fROM (
			SELECT od.sale_order_sheet_id,op_type,om.op_id as move_to_van_op_id ,case when om.op_no is null then 'ZC'||od.op_id else om.op_no end move_to_van_op_no,om.happen_time,om.approve_time, ROW_NUMBER() over(partition by od.sale_order_sheet_id ORDER BY om.happen_time DESC ) rn FROM op_move_to_van_detail od
			LEFT JOIN op_move_to_van_main om on od.company_id = om.company_id and od.op_id = om.op_id
			WHERE od.company_id =  '{token.CompanyID}' and om.red_flag is null  and om.happen_time >='{startDate}'
    )t
    WHERE t.rn = 1 and t.op_type in('2v','v2v') 
) 	optv on optv.sale_order_sheet_id = om.sheet_id 

LEFT JOIN pay_bill pb on pb.company_id= '{token.CompanyID}' and pb.bill_id = om.pay_bill_id and pb.sheet_type = om.sheet_type and pb.sheet_id = om.sheet_id

WHERE om.company_id = '{token.CompanyID}' and om.happen_time >='{startDate}'   and oss.order_status in('pzc','zc','zd','fh') 
and case when oss.assign_van_id is null then optv.move_to_van_op_id else omm.op_id end is not null 
{queryStatus}
{condition} 
";
//                sql_NoLimit =
//@$"
//SELECT
//    s.supcust_id, s.addr_lng,s.addr_lat,s.sup_addr,
//    sup_name, acct_cust_id,
//    acct_cust_name,
//    om.sheet_id AS order_sheet_id,
//    om.sheet_no AS order_sheet_no,
//    om.sheet_type as order_sheet_type,
//    b.branch_name, 
//    om.seller_id,om.make_brief,om.order_source,
//    io.oper_name as seller_name,
//    (case when oss.senders_name is null then om.senders_name else oss.senders_name end) as senders_name,
//    om.approve_time order_time,
//	oss.order_status,    
//    oss.receipt_status,
//    case when oss.print_time is null then '未打单' else '已打单' end  print_status,	
//    car.branch_name van_name,
//    s.mobile AS sup_tel,
//    om.happen_time,
//    om.total_amount,
//    omm.op_id,omm.op_no,omm.approve_time,omm.happen_time move_time,om.total_quantity sheet_qty
//FROM op_move_to_van_main omm 
//LEFT JOIN op_move_to_van_detail omd on omm.op_id=omd.op_id and omd.company_id={token.CompanyID} 
//LEFT JOIN sheet_sale_order_main om on omd.sale_order_sheet_id=om.sheet_id and om.company_id={token.CompanyID} and om.happen_time >= '{startDate}'

//LEFT JOIN info_supcust s ON om.supcust_id = s.supcust_id and om.company_id = s.company_id
//LEFT JOIN info_operator io ON om.seller_id = io.oper_id and io.company_id = om.company_id
//LEFT JOIN 
//( 
//    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = '{token.CompanyID}'
//) A ON s.acct_cust_id = A.a_supcust_id
//LEFT JOIN sheet_status_order oss ON om.sheet_id = oss.sheet_id and oss.company_id = om.company_id                
//LEFT JOIN info_branch b on b.company_id = om.company_id and b.branch_id = om.branch_id
//LEFT JOIN info_branch car on car.company_id = oss.company_id and car.branch_id = oss.van_id 
//where omm.company_id = '{token.CompanyID}' and omm.happen_time >= '{startDate}' 
//{queryStatus}
//{condition} 
 
//";
            }
            else
            {
                sql_NoLimit = $@"
SELECT
    s.supcust_id, s.addr_lng,s.addr_lat,s.sup_addr,
    sup_name, acct_cust_id,
    acct_cust_name,
    om.sheet_id AS order_sheet_id,
    om.sheet_no AS order_sheet_no,
    om.sheet_type as order_sheet_type,
    b.branch_name,
    om.seller_id,om.make_brief,om.order_source,
    io.oper_name as seller_name,
    ( case when oss.senders_name is null then om.senders_name else oss.senders_name end
    ) as senders_name,
    om.approve_time order_time,
	oss.order_status,    
    oss.receipt_status,
    case when oss.print_time is null then '未打单' else '已打单' end  print_status,	
    s.mobile AS sup_tel,
    om.happen_time,
    om.total_amount,
    om.total_quantity sheet_qty,
    sw.total_weight weight
FROM
	sheet_sale_order_main om
LEFT JOIN info_supcust s ON om.supcust_id = s.supcust_id and s.company_id= {token.CompanyID}
LEFT JOIN info_operator io ON om.seller_id = io.oper_id and io.company_id = {token.CompanyID}
LEFT JOIN (SELECT sd.sheet_id,sum(mu.weight*sd.quantity) total_weight FROM sheet_sale_order_detail sd
            LEFT JOIN info_item_multi_unit mu on sd.company_id = mu.company_id and sd.item_id = mu.item_id and sd.unit_factor = mu.unit_factor
            WHERE sd.company_id = {token.CompanyID}  and mu.weight is not null 
            GROUP BY sd.sheet_id 
) sw on sw.sheet_id = om.sheet_id 
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = '{token.CompanyID}'
) A ON s.acct_cust_id = A.a_supcust_id
LEFT JOIN sheet_status_order oss ON om.sheet_id = oss.sheet_id and oss.company_id = {token.CompanyID}      
LEFT JOIN info_branch b on b.company_id = {token.CompanyID} and b.branch_id = om.branch_id

LEFT JOIN pay_bill pb on pb.company_id= '{token.CompanyID}' and pb.bill_id = om.pay_bill_id and pb.sheet_type = om.sheet_type and pb.sheet_id = om.sheet_id

where om.company_id = '{token.CompanyID}' {queryStatus}
and om.red_flag is null 
and om.approve_time is not null
{condition} 
{queryTime}
";
//                sql_NoLimit =
//@$"
//SELECT
//    s.supcust_id, s.addr_lng,s.addr_lat,s.sup_addr,
//    sup_name, acct_cust_id,
//    acct_cust_name,
//    om.sheet_id AS order_sheet_id,
//    om.sheet_no AS order_sheet_no,
//    om.sheet_type as order_sheet_type,
//    b.branch_name,
//    sm.sale_sheet_id,sm.sale_sheet_no,
//    om.seller_id,om.make_brief,om.order_source,
//    io.oper_name as seller_name,
//    (case when sm.senders_name is not null then sm.senders_name else case when oss.senders_name is null then om.senders_name else oss.senders_name end end
//    ) as senders_name,
//    om.approve_time order_time,
//	oss.order_status,    
//    oss.receipt_status,
//    case when oss.print_time is null then '未打单' else '已打单' end  print_status,	
//    car.branch_name van_name,
//    s.mobile AS sup_tel,
//    om.happen_time,
//    om.total_amount,
//    op.op_id,op_no,op.approve_time,op.happen_time move_time,om.total_quantity sheet_qty
//FROM
//	sheet_sale_order_main om
//LEFT JOIN info_supcust s ON om.supcust_id = s.supcust_id and om.company_id = s.company_id
//LEFT JOIN info_operator io ON om.seller_id = io.oper_id and io.company_id = om.company_id
//LEFT JOIN 
//( 
//    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = '{token.CompanyID}'
//) A ON s.acct_cust_id = A.a_supcust_id
//LEFT JOIN sheet_status_order oss ON om.sheet_id = oss.sheet_id and oss.company_id = om.company_id
//LEFT JOIN 
//(
//    select order_sheet_id, sheet_id as sale_sheet_id,red_flag, sheet_no as sale_sheet_no,senders_id,senders_name from sheet_sale_main where red_flag IS NULL and company_id = '{token.CompanyID}' and happen_time>='{startDate}'
//) sm on om.sheet_id = sm.order_sheet_id                   
//LEFT JOIN info_branch b on b.company_id = om.company_id and b.branch_id = om.branch_id
//LEFT JOIN info_branch car on car.company_id = oss.company_id and car.branch_id = oss.van_id 
//LEFT JOIN
//(
//    SELECT d.*,m.happen_time,case when d.move_sheet_id is not null then m.approve_time else m.approve_time end,
//           mm.sheet_no move_sheet_no,case when m.op_no is null  then 'ZC'|| m.op_id  else m.op_no end op_no 
//    FROM op_move_to_van_main m  
//    LEFT JOIN op_move_to_van_detail d on m.company_id = d.company_id and m.op_id=d.op_id and m.happen_time >= '{startDate}'
//    LEFT JOIN sheet_move_main mm on m.company_id = mm.company_id and d.move_sheet_id = mm.sheet_id and mm.happen_time >= '{startDate}'
//    WHERE m.company_id = '{token.CompanyID}' and m.red_flag is  null  
//) op on om.sheet_id = op.sale_order_sheet_id
//where om.company_id = '{token.CompanyID}'
//{queryStatus}
//and om.red_flag is null 
//and om.approve_time is not null
//and sm.red_flag is null
//{condition} 
//{queryTime}
//";
            }



            return MiniSQLParser.GetSQLResult(sql_NoLimit, getCount, getRows, orderBy, pageSize, startRow, "string_agg(om.total_quantity,',') as total_qty");
        }

        [HttpGet]
        public async Task<JsonResult> GetAssignVanForApprove(string operKey, int pageSize, int startRow, bool getTotal,  string supcustFlag,  bool isDone, string startDate, string endDate,   string branchID, string senderID, string sellerID, string branches)
        {
            SqlResult sqlResult = GetAssignVanForApprove_SQL(cmd, Token, getTotal, true, pageSize, startRow, isDone, startDate, endDate, branchID, senderID, sellerID, branches);
            return await MiniSQLParser.RunSqlResult(cmd, sqlResult, getTotal);
        }

        public static SqlResult GetAssignVanForApprove_SQL(CMySbCommand cmd, Token token, bool getCount, bool getRows, int pageSize, int startRow, bool isDone, string startDate, string endDate, string branchID, string senderID, string sellerID, string branches)
        {
            var condition = $" otm.company_id = '{token.CompanyID}' ";
            var orderBy = "";
            orderBy += "order by otm.happen_time desc";
            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);
            queryTime += $" and otm.happen_time >= '{startDate}'";

            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and otm.happen_time <= '{endDate}' ";
            }

            if (branches == "") return new SqlResult(){ErrMsg= "无调出仓权限" };
            if (sellerID.IsValid()) condition += $" and otm.oper_id = {sellerID}";
            if (senderID.IsValid()) condition += $" and (otm.senders_id like '%{senderID}%')";
            if (branchID.IsValid()) condition += $" and (otm.from_branch ={branchID} or otm.to_van = {branchID}) ";
            string codi = "";
            var queryStatus = " and case when otm.move_sheet_id is not null then sm.approve_time else otm.approve_time end  is null ";
            if (isDone)
            {
                queryStatus = " and case when otm.move_sheet_id is not null then sm.approve_time else otm.approve_time end  is not null";
                codi = " and  od.sale_order_row_branch_id is not null ";
            }

            SQLQueue QQ = new SQLQueue(cmd);
            var sql_NoLimit =
@$"
SELECT otm.company_id,otm.op_id,otm.oper_id,io.oper_name,otm.happen_time,otm.senders_id,otm.senders_name,otd.count order_count,otd.sale_order_sheets_id,otm.from_branch from_branch_id,f.branch_name from_branch_name, otm.to_van to_van_id,t.branch_name to_van_name,otm.move_sheet_id, sm.sheet_no move_no,sm.approve_time move_time,sm.red_flag move_red_flag ,case when otm.move_sheet_id is not null then sm.approve_time else otm.approve_time end ,case when otm.move_stock is null then otd.move_stock else otm.move_stock end,case when otm.op_no is not null then otm.op_no else 'ZC'||otm.op_id end op_no,op_type
FROM op_move_to_van_main otm  
LEFT JOIN 
(
   SELECT DISTINCT od.op_id, case when sso.move_stock is null then om.move_stock else sso.move_stock end move_stock,count(DISTINCT sale_order_sheet_id),string_agg(DISTINCT sale_order_sheet_id::text,',') sale_order_sheets_id ,od.move_sheet_id,od.sale_order_row_branch_id AS from_branch
   FROM op_move_to_van_detail od 
   LEFT JOIN sheet_status_order sso on od.company_id = sso.company_id and od.sale_order_sheet_id = sso.sheet_id
   LEFT JOIN op_move_to_van_main om on om.company_id = od.company_id and om.op_id = od.op_id 
   WHERE od.company_id = '{token.CompanyID}'  {codi}  GROUP BY od.op_id,case when sso.move_stock is null then om.move_stock else sso.move_stock end,od.move_sheet_id,od.sale_order_row_branch_id
) otd on otd.op_id = otm.op_id
LEFT JOIN info_operator io on io.company_id = otm.company_id and io.oper_id = otm.oper_id
LEFT JOIN info_branch f on f.company_id = otm.company_id and f.branch_id = otm.from_branch
LEFT JOIN info_branch t on t.company_id = otm.company_id and t.branch_id = otm.to_van
LEFT JOIN sheet_move_main sm on sm.company_id = otm.company_id and sm.sheet_id =  coalesce(otm.move_sheet_id,otd.move_sheet_id)
WHERE  {condition} and (otm.from_branch in ({branches}) or otd.from_branch in ({branches})) and otm.red_flag is null {queryTime} {queryStatus}
";

            return MiniSQLParser.GetSQLResult(sql_NoLimit, getCount, getRows, orderBy, pageSize, startRow);

        }

        [HttpGet]
        public async Task<JsonResult> GetOrdersForBackBranch(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string regionID, string currentLat, string currentLng, string supcustFlag, string operRegions, bool isDone, string startDate, string endDate, string departID, string branchID, string senderID, string sellerID, bool is_sender, bool isBoss)
        {
            SqlResult sqlResult = GetOrdersForBackBranch_SQL(cmd, Token, getTotal, true, pageSize, startRow, searchStr, regionID, currentLat, currentLng, operRegions, isDone, startDate, endDate, departID, branchID, senderID, sellerID, is_sender,  isBoss);
            return await MiniSQLParser.RunSqlResult(cmd, sqlResult, getTotal);
        }

        public static SqlResult GetOrdersForBackBranch_SQL(CMySbCommand cmd, Token token, bool getCount, bool getRows, int pageSize, int startRow, string searchStr, string regionID, string currentLat, string currentLng, string operRegions, bool isDone, string startDate, string endDate, string departID, string branchID, string senderID, string sellerID, bool is_sender, bool isBoss)
        {
            var condition = $" and (s.status = '1' or s.status is null) ";

            var orderBy = "";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%'  or s.mobile ilike '%{searchStr}%' ) ";
            if (regionID.IsValid())
            {
                var lstRegion = regionID.Split(',');
                string regionCondi = "";
                foreach (var re in lstRegion)
                {
                    if (regionCondi != "") regionCondi += " or ";
                    regionCondi += $" other_region ilike '%/{re}/%' ";
                }
                condition += $" and ({regionCondi})";
            }
            orderBy += "order by sm.happen_time desc";
            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);

            queryTime += $" and sm.happen_time >= '{startDate}'";

            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and sm.happen_time <= '{endDate}' ";
            }
            if (!isBoss)
            {
                if (is_sender)
                {
                    condition += $" and (oss.senders_id like '%{token.OperID}%' or sm.senders_id like '%{token.OperID}%' or sm.seller_id = {token.OperID}) ";
                }
                else
                {
                    condition += $" and sm.seller_id = {token.OperID}";
                }

            }
       
          
            //if (operID.IsValid()) condition += $" and sm.seller_id = {operID}";
            if (departID.IsValid()) condition += $" and io.depart_path like '%{"/" + departID + "/"}%'";
            if (sellerID.IsValid()) condition += $" and sm.seller_id = {sellerID}";
            if (senderID.IsValid()) condition += $" and (oss.senders_id like '%{senderID}%' or sm.senders_id like '%{senderID}%')";
            if (branchID.IsValid()) condition += $" and sm.branch_id = {branchID}";
            var queryStatus = "and (back_branch_status is null  or back_branch_status ='bf')  and back_branch_done is null ";
            if (isDone) queryStatus = "and (back_branch_done = 't' or back.op_id is not null) ";

            string isDoneParameter = "back.approve_time back_approve_time ,";
            //            string isDoneLJ = $@"
            //SELECT DISTINCT ofr.sale_order_sheet_id,ofr.sale_sheet_id, string_agg(DISTINCT ofr.back_type, ',') back_types ,
            //    case when om.back_done is null then 
            //         case when sum(case when need_move_qty*unit_factor-move_qty*back_unit_factor-sale_qty*sale_unit_factor >0 then 1 else 0 end )>0 then false else true end
            //    else om.back_done end back_done
            //FROM op_move_from_van_row ofr
            //LEFT JOIN op_move_from_van_main om on om.company_id = ofr.company_id and om.op_id = ofr.op_id and om.happen_time >= '{startDate}'
            //WHERE ofr.company_id = '{token.CompanyID}' and om.red_flag is null and ofr.is_previous_move is null
            //GROUP BY ofr.op_id,ofr.sale_order_sheet_id,ofr.sale_sheet_id,back_done";
            string isDoneLJ = $@"  SELECT * FROM (
SELECT DISTINCT ofr.op_id, ofr.sale_order_sheet_id,ofr.sale_sheet_id, string_agg(DISTINCT ofr.back_type, ',') back_types ,
    case when om.back_done is null then 
         case when sum(case when need_move_qty*unit_factor-move_qty*back_unit_factor-sale_qty*sale_unit_factor >0 then 1 else 0 end )>0 then false else true end
    else om.back_done end back_done,om.happen_time,om.approve_time,row_number() over(partition by ofr.sale_order_sheet_id ORDER BY om.happen_time DESC) rn 
FROM op_move_from_van_row ofr
LEFT JOIN op_move_from_van_main om on om.company_id = ofr.company_id and om.op_id = ofr.op_id and om.happen_time >= '{startDate}'
WHERE ofr.company_id = '{token.CompanyID}' and om.red_flag is null and ofr.is_previous_move is null
GROUP BY ofr.op_id,ofr.sale_order_sheet_id,ofr.sale_sheet_id,back_done,om.happen_time,om.approve_time)t
WHERE t.rn=1";

            if (isDone)
            {
                isDoneParameter = @" back.approve_time  back_time, back.op_id ,oss.move_stock ,back.op_no,";
                isDoneLJ = $@"
SELECT DISTINCT ofr.op_id,case when om.op_no is not null then om.op_no else 'HK'||ofr.op_id end op_no ,ofr.sale_order_sheet_id,ofr.sale_sheet_id,om.approve_time,om.approver_id,string_agg(DISTINCT ofr.back_branch::text,',') back_branches,string_agg(DISTINCT ofr.move_sheet_id::text,',') move_sheets , string_agg(DISTINCT ofr.back_type, ',') back_types,
    case when back_done is null then 
         case when sum(case when need_move_qty*unit_factor-move_qty*back_unit_factor-sale_qty*sale_unit_factor >0 then 1 else 0 end  )>0 then false else true end 
    else back_done end back_done
FROM op_move_from_van_row ofr
LEFT JOIN op_move_from_van_main om on ofr.company_id = om.company_id and ofr.op_id = om.op_id and om.happen_time >= '{startDate}'
WHERE ofr.company_id = '{token.CompanyID}' and om.red_flag is null 
GROUP BY ofr.op_id,ofr.sale_order_sheet_id,ofr.sale_sheet_id,om.approve_time,om.approver_id,om.op_no,back_done";
            }


            SQLQueue QQ = new SQLQueue(cmd);
            var sql_NoLimit =
@$"
SELECT
    s.supcust_id, s.addr_lng,s.addr_lat,s.sup_addr,
    sup_name, sm.sheet_id AS order_sheet_id,sm.sheet_no AS order_sheet_no,sm.sheet_type as order_sheet_type,
    b.branch_name,m.sale_sheet_id,m.sale_sheet_no,sm.seller_id,sm.make_brief,sm.order_source,io.oper_name as seller_name,
    (case when m.senders_name is not null then m.senders_name else case when oss.senders_name is null then sm.senders_name else oss.senders_name end end
    ) as senders_name,sm.approve_time,oss.order_status,oss.receipt_status,oss.has_return,back_branch_done,case when oss.print_time is null then '未打单' else '已打单' end print_status,
    car.branch_name van_name,s.mobile AS sup_tel,sm.happen_time,sm.total_amount,{isDoneParameter} case when back_branch_status ='qb' then true else false end  back_done,oss.move_stock,case when back.back_done is null then false else true end have_previous_move
FROM sheet_sale_order_main sm
LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and  s.company_id = {token.CompanyID}
LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and  io.company_id = {token.CompanyID}
LEFT JOIN sheet_status_order oss ON sm.sheet_id = oss.sheet_id and oss.company_id = {token.CompanyID}
LEFT JOIN
(
    select order_sheet_id, sheet_id as sale_sheet_id,red_flag, sheet_no as sale_sheet_no,senders_id,senders_name from sheet_sale_main where red_flag IS NULL and company_id = '{token.CompanyID}' and happen_time >= '{startDate}' and approve_time is not null 
) m on sm.sheet_id = m.order_sheet_id              
LEFT JOIN info_branch b on b.company_id = {token.CompanyID} and b.branch_id = sm.branch_id
LEFT JOIN info_branch car on car.company_id = {token.CompanyID} and car.branch_id = oss.van_id 
LEFT JOIN (
  {isDoneLJ}
) 
back on back.sale_order_sheet_id=sm.sheet_id
where sm.company_id = '{token.CompanyID}'
and oss.order_status ='zd'
and (case when sm.sheet_type = 'TD' then oss.receipt_status in('zd','bf') else oss.receipt_status in ('bf','js') end or oss.has_return = 't')
and case when oss.receipt_status ='js' and sm.sheet_type ='XD' then sm.total_quantity !~'^(?!.*销:\S+\s*)(销:\s*)?(退:\S+)' else true end 
{queryStatus}
and sm.red_flag is null
and sm.approve_time is not null
and m.red_flag is null
{condition}
{queryTime}
";
           return MiniSQLParser.GetSQLResult(sql_NoLimit, getCount, getRows, orderBy, pageSize, startRow);
        }



        [HttpGet]
        public async Task<JsonResult> GetBackBranchSheetForApprove(string operKey, int pageSize, int startRow, bool getTotal, string supcustFlag, bool isDone, string startDate, string endDate, string branchID, string senderID, string sellerID, string branches)
        {
            SqlResult sqlResult = GetBackBranchSheetForApprove_SQL(cmd, Token, getTotal, true, pageSize, startRow, isDone, startDate, endDate, branchID, senderID, sellerID, branches);
            return await MiniSQLParser.RunSqlResult(cmd, sqlResult, getTotal);
        }

        public static SqlResult GetBackBranchSheetForApprove_SQL(CMySbCommand cmd, Token token, bool getCount, bool getRows, int pageSize, int startRow, bool isDone, string startDate, string endDate, string branchID, string senderID, string sellerID, string branches)
        {
            var condition = $" otm.company_id = '{token.CompanyID}' ";
            var orderBy = "";
            orderBy += "order by otm.happen_time desc";
            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);

            queryTime += $" and otm.happen_time >= '{startDate}'";

            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and otm.happen_time <= '{endDate}' ";
            }
            
            if (branches.IsInvalid()) return new SqlResult() { ErrMsg = "无调出仓权限" };
            if (sellerID.IsValid()) condition += $" and otm.oper_id = {sellerID}";
            if (senderID.IsValid()) condition += $" and (otm.senders_id like '%{senderID}%')";
            if (branchID.IsValid()) condition += $" and (otm.from_branch ={branchID} or otm.to_van = {branchID}) ";
            var queryStatus = " and case when otm.move_sheet_id is not null then sm.approve_time else case when rjt.sheet_id is not null then rjt.approve_time else case when rtn.sheet_id is not null then rtn.approve_time else otm.approve_time end end end   is null ";
            if (isDone) queryStatus = " and case when otm.move_sheet_id is not null then sm.approve_time else case when rjt.sheet_id is not null then rjt.approve_time else case when rtn.sheet_id is not null then rtn.approve_time else otm.approve_time end end end  is not null ";


            SQLQueue QQ = new SQLQueue(cmd);
            var sql_NoLimit =
@$"
SELECT otm.company_id,otm.op_id,case when otm.op_no is not null then otm.op_no else 'HK'||otm.op_id end op_no,otm.oper_id,io.oper_name,otm.happen_time,otm.senders_id,otm.senders_name,otd.count order_count,otd.sale_order_sheets_id,sm.from_branch_id from_branch_id,f.branch_name from_branch_name, sm.to_branch_id to_van_id,t.branch_name to_van_name,otm.move_sheet_id, sm.sheet_no move_no,sm.approve_time move_time,sm.red_flag move_red_flag ,assign_van_type,
case when rjt.sheet_id is not null then rjt.approve_time else case when rtn.sheet_id is not null then rtn.approve_time else otm.approve_time end end ,otm.from_branch,bb.branch_name branch_name, back.reject_branch,rtb.branch_name reject_branch_name, back.reject_move,rjt.sheet_no reject_no,back.return_branch,rnb.branch_name return_branch_name,back.return_move,rtn.sheet_no return_no ,otd.move_stock
FROM op_move_from_van_main otm  
LEFT JOIN 
( 
    SELECT op_id, count(op_id),string_agg(sale_order_sheet_id::text,',') sale_order_sheets_id,sso.move_stock  
    FROM op_move_from_van_detail od
	LEFT JOIN sheet_status_order sso on sso.company_id = od.company_id and sso.sheet_id = od.sale_order_sheet_id
	WHERE od.company_id = '{token.CompanyID}' 
    GROUP BY op_id,sso.move_stock 
) otd on otd.op_id = otm.op_id
LEFT JOIN sheet_move_main sm on sm.company_id = otm.company_id and sm.sheet_id =  otm.move_sheet_id
LEFT JOIN info_operator io on io.company_id = otm.company_id and io.oper_id = otm.oper_id
LEFT JOIN info_branch f on f.company_id = otm.company_id and f.branch_id = sm.from_branch_id
LEFT JOIN info_branch t on t.company_id = otm.company_id and t.branch_id = sm.to_branch_id
LEFT JOIN 
(
    SELECT DISTINCT op_id,assign_van_type FROM op_move_from_van_row WHERE company_id = '{token.CompanyID}'
) otr on otr.op_id  = otm.op_id
LEFT JOIN 
(
    SELECT op_id , max(reject_branch) reject_branch,max(return_branch) return_branch,max(reject_move) reject_move,max(return_move) return_move 
    FROM 
    (
        SELECT DISTINCT op_id ,
        case when back_type = 'reject' then back_branch   else null end reject_branch,
        case when back_type = 'return' then back_branch   else null end return_branch,
        case when back_type = 'reject' then move_sheet_id else null end reject_move,
        case when back_type = 'return' then move_sheet_id else null end return_move
        FROM op_move_from_van_row
        WHERE company_id = '{token.CompanyID}'
    ) t
    GROUP BY op_id 
) back on back.op_id=otm.op_id 
LEFT JOIN info_branch bb on bb.company_id = otm.company_id and bb.branch_id = otm.from_branch
LEFT JOIN sheet_move_main rjt on otm.company_id = rjt.company_id and back.reject_move = rjt.sheet_id 
LEFT JOIN sheet_move_main rtn on otm.company_id = rtn.company_id and back.return_move = rtn.sheet_id 
LEFT JOIN info_branch rtb on rtb.company_id = otm.company_id and rtb.branch_id = back.reject_branch
LEFT JOIN info_branch rnb on rnb.company_id = otm.company_id and rnb.branch_id = back.return_branch
WHERE  {condition} and otm.red_flag is null  and case when otm.move_stock='true' then  case when  otm.move_sheet_id is not null then  sm.from_branch_id else otm.from_branch end in ({branches}) else true end  {queryTime} {queryStatus}
";

            return MiniSQLParser.GetSQLResult(sql_NoLimit, getCount, getRows, orderBy, pageSize, startRow);

        }


  
        [HttpPost]
        public async Task<JsonResult> GetBackBranchRowInfo([FromQuery] string operKey, [FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey(operKey, out string companyId, out string operId);
            string op_id = data.op_id;
            string op_type = data.op_type;
            if (op_id == "") return new JsonResult(new { result = "error", msg = "装车单号为空" });

            SheetBackBranchVan backBranchSheet = new SheetBackBranchVan(LOAD_PURPOSE.SHOW);
            await backBranchSheet.Load(cmd, companyId, op_id);
            string moveSheetID = backBranchSheet.move_sheet_id;
            SheetMove moveSheet = null;
            if (moveSheetID != ""&& moveSheetID!=null)
            {
                SheetMove.GetSheetsUsage usage = new SheetMove.GetSheetsUsage();
                usage.GetSumSheet = true;
                usage.GetEachSheet = true;
                usage.SplitUnitRows = true;
                usage.OptionToRemember = data.optionToRemember;
                SheetMove.GetSheetsResult res = await SheetMove.GetItemSheets<SheetMove, SheetRowMove>(cmd, operKey, moveSheetID, usage, true, "0", "", "");
                moveSheet = res.sheetGroup[1].sheets[0];
            }
            return new JsonResult(new { result = "OK", msg = "", backBranchSheet, moveSheet });
        }
    
      
     /// <summary>
     /// 获取待转单的订单
     /// </summary>
     /// <param name="operKey"></param>
     /// <param name="pageSize"></param>
     /// <param name="startRow"></param>
     /// <param name="getTotal"></param>
     /// <param name="searchStr"></param>
     /// <param name="regionID"></param>
     /// <param name="currentLat"></param>
     /// <param name="currentLng"></param>
     /// <param name="supcustFlag"></param>
     /// <param name="operRegions"></param>
     /// <param name="startDate"></param>
     /// <param name="endDate"></param>
     /// <param name="isBoss"></param>
     /// <param name="isAssignVanNecessary"></param>
     /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetOrdersForSale(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string regionID, string currentLat, string currentLng, string operRegions, string startDate, string endDate, bool isBoss, bool isAssignVanNecessary,bool noVanOrderToSale,string sellerID,string senderID,string branchID,string carID, bool ignorePayFailedSheetOnOrderToSale)
        {
            
            SqlResult sqlResult = GetOrdersForSale_SQL(cmd,Token,getTotal,true, pageSize,  startRow, searchStr, regionID,  currentLat,  currentLng,  operRegions,  startDate,  endDate,  isBoss,  isAssignVanNecessary, noVanOrderToSale,  sellerID, senderID, branchID, carID, ignorePayFailedSheetOnOrderToSale);
                          
            if (sqlResult.ErrMsg != "")
            {
				return new JsonResult(new { result = "Error", msg = sqlResult.ErrMsg });

			}
			SQLQueue QQ = new SQLQueue(cmd);
            if (getTotal)
            {
                QQ.Enqueue("count", sqlResult.SqlCount);
            }
            QQ.Enqueue("data", sqlResult.SqlRows);

             
            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> data = null;
            string total = "";
            string totalAmount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
              
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);

                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    totalAmount = CPubVars.GetTextFromDr(dr, "total_amount");
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", msg = "", data, total, totalAmount });

        }
        /// <summary>
        /// 获取待转单的订单
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="getTotal"></param>
        /// <param name="searchStr"></param>
        /// <param name="regionID"></param>
        /// <param name="currentLat"></param>
        /// <param name="currentLng"></param>
        /// <param name="supcustFlag"></param>
        /// <param name="operRegions"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="isBoss"></param>
        /// <param name="isAssignVanNecessary"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetOrdersForGrab(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string regionID, string currentLat, string currentLng, string operRegions,string departID, string startDate, string endDate, string sellerID, string senderID, string branchID,string sheetNo)
        {
            //string operID=Token.OperID;
            SqlResult sqlResult = GetOrdersForGrab_SQL(cmd, Token, getTotal, true, pageSize, startRow, searchStr, regionID, currentLat, currentLng, operRegions, startDate, endDate, senderID, sellerID,branchID, departID,sheetNo);

            if (sqlResult.ErrMsg != "")
            {
                return new JsonResult(new { result = "Error", msg = sqlResult.ErrMsg });

            }
            SQLQueue QQ = new SQLQueue(cmd);
            if (getTotal)
            {
                QQ.Enqueue("count", sqlResult.SqlCount);
            }
            QQ.Enqueue("data", sqlResult.SqlRows);


            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> data = null;
            string total = "";
            string totalAmount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();

                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);

                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    totalAmount = CPubVars.GetTextFromDr(dr, "total_amount");
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", msg = "", data, total, totalAmount });

        }
        public static SqlResult GetOrdersForGrab_SQL(CMySbCommand cmd, Token token, bool getCount, bool getRows, int pageSize, int startRow, string searchStr, string regionID, string currentLat, string currentLng, string operRegions, string startDate, string endDate, string senderID, string sellerID, string branchID,string departID, string sheetNo)
        {
            var condition = $"";
            string condiRegions = "";
            if (operRegions.IsValid())
            {
                var regions = JsonConvert.DeserializeObject<int[]>(operRegions);
                if (regions.Length > 0)
                    // condiRegions = " and (" + string.Join(" or ", regions.Select(x => $"s.other_region  like '%/{x}/%'")) + ") ";
                    condiRegions = " and (" + string.Join(" or ", regions.Select(x => $"position( '/{x}/' in s.other_region) > 0")) + ") ";
            }
            if (sheetNo.IsValid())
            {
                condition += $" and om.sheet_no like '%{sheetNo}%'";

            }
            var distanceSQL = "";
            var orderBy = "";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%'  or s.mobile ilike '%{searchStr}%' or s.supcust_no ilike '%{searchStr}%' or om.sheet_no ilike '%{searchStr}%') ";
            if (regionID.IsValid())
            {
                var lstRegion = regionID.Split(',');
                string regionCondi = "";
                foreach (var re in lstRegion)
                {
                    if (regionCondi != "") regionCondi += " or ";
                    regionCondi += $" other_region ilike '%/{re}/%' ";
                }
                condition += $" and ({regionCondi})";
            }
            if (currentLng.IsValid() && currentLat.IsValid())
            {
                distanceSQL += $",st_distance(ST_Transform(ST_SetSRID(addr_lnglat,4326)::geometry, 3857),ST_Transform('SRID=4326;POINT({currentLng} {currentLat})'::geometry, 3857)) as distance";
                orderBy += "order by distance ASC";
            }

           // if (ignorePayFailedSheetOnOrderToSale) condition += $" and (pb.bill_status = 'paid' or pb.bill_status is null)";

            if (!startDate.IsValid())
            {
                return new SqlResult() { ErrMsg = "请指定起始日期" };
            }

            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);
            queryTime += $" and om.happen_time >= '{startDate}' ";
            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and om.happen_time <= '{endDate}' ";
            }
            //业务员可以在送货员签收的页面查看业务员为自己的订单，这个功能暂时去掉，张杰鹏那边发现业务员误审了单子，这个功能是某个代理商提的，忘记谁了，等他发现后再谈论
            //var queryRole = isBoss ? "" : $@" and (oss.senders_id like '%{token.OperID}%' or om.senders_id like '%{token.OperID}%' or om.seller_id = {token.OperID})";

            string queryRole;// = isBoss ? "" : $@" and (oss.senders_id like '%{token.OperID}%' or om.senders_id like '%{token.OperID}%')";
       
            queryRole = condiRegions;
          
           
            if (sellerID.IsValid()) { condition += $@" and om.seller_id = {sellerID}  "; }


         
            if (branchID.IsValid()) condition += $@" and om.branch_id = {branchID} ";
           

            string sql_NoLimit =
@$"
SELECT
       s.supcust_id,
    sup_name,s.addr_lng,s.addr_lat,s.sup_addr,acct_cust_id, acct_cust_name, om.sheet_id order_sheet_id,order_sheet_no,
    om.sheet_no  sale_sheet_no,om.seller_id,om.make_brief,om.order_source,om.sheet_type,io.oper_name as seller_name,om.senders_name,
    s.mobile AS sup_tel,om.happen_time,om.total_amount*om.money_inout_flag total_amount,round((om.total_amount -om.now_pay_amount - om.now_disc_amount)::numeric,4) left_amount,
    realtime->>'lastSaleTime' as last_sale_time,realtime->>'lastVisitTime' as last_visit_time,om.senders_id,
    sup_addr {distanceSQL}
FROM
(
        select  sheet_id,sheet_no as order_sheet_no,branch_id,senders_id,company_id,supcust_id,seller_id,happen_time,approve_time,senders_name,
            now_pay_amount,total_amount,order_source,make_brief,sheet_no,money_inout_flag,
            now_disc_amount,sheet_type,red_flag from  sheet_sale_order_main
	where red_flag IS NULL and company_id = {token.CompanyID} and happen_time>='{startDate}' and coalesce(is_del, false) = false
) om
LEFT JOIN sheet_status_order sso on om.sheet_id=sso.sheet_id and sso.company_id={token.CompanyID}
LEFT JOIN info_supcust s ON om.supcust_id = s.supcust_id and s.company_id = {token.CompanyID}
LEFT JOIN info_operator io ON om.seller_id = io.oper_id and io.company_id = {token.CompanyID}
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = {token.CompanyID}
) A ON s.acct_cust_id = A.a_supcust_id and s.company_id = {token.CompanyID}

left join realtime_supcust r on om.company_id=r.company_id and s.supcust_id=r.supcust_id   and s.company_id = {token.CompanyID}   and r.company_id = {token.CompanyID}        
where om.company_id = {token.CompanyID} 
and om.red_flag is null 
and om.senders_id is null
and sso.senders_id is null and coalesce(sso.order_status,'xd') in ('xd','dd')
{queryRole} 
{condition} 
{queryTime}


";

            return MiniSQLParser.GetSQLResult(sql_NoLimit, getCount, getRows, orderBy, pageSize, startRow, "SUM(om.total_amount) as total_amount");

        }
        [HttpPost]
        public async Task<JsonResult> GrabSheet(string operKey, [FromBody] dynamic data)
        {
            string sheetId = data.sheet_id;
            Security.GetInfoFromOperKey(operKey, out string companyId, out string operId);
            string sql = $"select oper_name from info_operator where company_id={companyId} and oper_id={operId};";
            cmd.CommandText = sql;
            object ov = await cmd.ExecuteScalarAsync();
            if(ov==null || ov == DBNull.Value)
            {
				return new JsonResult(new { result = "Error", msg = "该员工不存在" });
			}
            string sender_name = ov.ToString();
            string updateSQL = @$"update sheet_sale_order_main set senders_id = '{operId}',senders_name='{sender_name}' where company_id={companyId} and sheet_id = '{sheetId}' and senders_id is null;";
            cmd.CommandText = updateSQL;
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new { result = "OK", msg = "" });

        }
        [HttpGet]
        public async Task<JsonResult> GetOrdersForGrab_done(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string regionID, string currentLat, string currentLng, string supcustFlag, string operRegions, string startDate, string endDate, bool isBoss, string sellerID, string senderID, string branchID, string carID,string sheetNo)
        {
            Security.GetInfoFromOperKey(operKey, out string companyId, out string operId);
            if (supcustFlag.IsInvalid()) supcustFlag = "C";
            var condition = $" and supcust_flag = '{supcustFlag}' ";
            if (operRegions.IsValid())
            {
                var regions = JsonConvert.DeserializeObject<int[]>(operRegions);
                if (regions.Length > 0)
                    condition += " and (" + string.Join(" or ", regions.Select(x => $"other_region  like '%/{x}/%'")) + ") ";
            }

            var distanceSQL = "";
            var orderBy = "";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%' or s.mobile ilike '%{searchStr}%' or s.supcust_no ilike '%{searchStr}%' or om.order_sheet_no ilike '%{searchStr}%') ";
            if (regionID.IsValid())
            {
                var lstRegion = regionID.Split(',');
                string regionCondi = "";
                foreach (var re in lstRegion)
                {
                    if (regionCondi != "") regionCondi += " or ";
                    regionCondi += $" other_region ilike '%/{re}/%' ";
                }
                condition += $" and ({regionCondi})";
            }
            if (sheetNo.IsValid())
            {
                condition += $" and om.sheet_no like '%{sheetNo}%'";

            }

            orderBy += "order by om.approve_time desc";

            var queryTime = "";

            startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);

            DateTime dtMonthsAgo = Convert.ToDateTime(startDate).AddMonths(-3);


            queryTime += $" and om.happen_time >= '{startDate}'";
            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and om.happen_time <= '{endDate}' ";
            }
            var queryRole = isBoss ? "" : $@" and (om.senders_id like '%{Token.OperID}%' or om.seller_id = {Token.OperID})";
            var assignCondi = "";

            if (sellerID.IsValid()) { assignCondi += $@" and om.seller_id = {sellerID}  "; }
            // if (sellerID.IsValid()) { assignCondi += $@" and (sm.seller_id = {sellerID} or sm.seller_id = {operID}) "; }
            // else
            // {
            //     assignCondi += $@" and sm.seller_id = {operID} ";
            // }
            if (senderID.IsValid()) assignCondi += $@" and ( om.senders_id like '%{senderID}%') ";
            if (branchID.IsValid()) assignCondi += $@" and om.branch_id = {branchID} ";
            if (carID.IsValid()) assignCondi += $@" and oss.van_id ={carID} ";

            SQLQueue QQ = new SQLQueue(cmd);

            string sql_NoLimit = @$"
SELECT
       s.supcust_id,
    sup_name,s.addr_lng,s.addr_lat,s.sup_addr,acct_cust_id, acct_cust_name, om.sheet_id order_sheet_id,order_sheet_no,
    om.sheet_no  sale_sheet_no,om.seller_id,om.make_brief,om.order_source,om.sheet_type,io.oper_name as seller_name,om.senders_name,
    s.mobile AS sup_tel,om.happen_time,om.total_amount*om.money_inout_flag total_amount,round((om.total_amount -om.now_pay_amount - om.now_disc_amount)::numeric,4) left_amount,
    realtime->>'lastSaleTime' as last_sale_time,realtime->>'lastVisitTime' as last_visit_time,om.senders_id,
    sup_addr {distanceSQL}
FROM
	(
        select  sheet_id,sheet_no as order_sheet_no,branch_id,senders_id,company_id,supcust_id,seller_id,happen_time,approve_time,senders_name,
            now_pay_amount,total_amount,order_source,make_brief,sheet_no,money_inout_flag,
            now_disc_amount,sheet_type,red_flag from  sheet_sale_order_main
	where red_flag IS NULL and company_id = {Token.CompanyID} and happen_time>='{startDate}' and coalesce(is_del, false) = false
)om
LEFT JOIN info_supcust s ON om.supcust_id = s.supcust_id and s.company_id = {Token.CompanyID}
LEFT JOIN info_operator io ON om.seller_id = io.oper_id and io.company_id = {Token.CompanyID}
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = {Token.CompanyID}
) A ON s.acct_cust_id = A.a_supcust_id and s.company_id = {Token.CompanyID}

left join realtime_supcust r on om.company_id=r.company_id and s.supcust_id=r.supcust_id   and s.company_id = {Token.CompanyID}   and r.company_id = {Token.CompanyID}        
where om.company_id = {Token.CompanyID} 
and om.approve_time is not null 
and om.red_flag is null 
and om.senders_id like '%{operId}%'
{queryRole} 
{condition} 
{queryTime}
{assignCondi}


";



            var sql = $"{sql_NoLimit}  {orderBy} limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);


            if (getTotal)
            {
                sql = $"select count(*) as total,sum(t.total_amount) as total_amount from ({sql_NoLimit}) t";
                QQ.Enqueue("count", sql);
            }

            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> data = null;
            string total = "";
            string totalAmount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();

                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);

                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    totalAmount = CPubVars.GetTextFromDr(dr, "total_amount");
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", msg = "", data, total, totalAmount });

        }
        public static SqlResult GetOrdersForSale_SQL(CMySbCommand cmd,Token token, bool getCount, bool getRows, int pageSize, int startRow,string searchStr, string regionID, string currentLat, string currentLng,  string operRegions, string startDate, string endDate, bool isBoss, bool isAssignVanNecessary,bool noVanOrderToSale, string sellerID, string senderID, string branchID, string carID, bool ignorePayFailedSheetOnOrderToSale)
        {      
            var condition = $" and (s.status = '1' or s.status is null) ";
            string condiRegions = "";
            if (operRegions.IsValid())
            {
                var regions = JsonConvert.DeserializeObject<int[]>(operRegions);
                if (regions.Length > 0)
                    // condiRegions = " and (" + string.Join(" or ", regions.Select(x => $"s.other_region  like '%/{x}/%'")) + ") ";
                    condiRegions = " and (" + string.Join(" or ", regions.Select(x => $"position( '/{x}/' in s.other_region) > 0")) + ") ";
            }

            var distanceSQL = "";
            var orderBy = "";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%'  or s.mobile ilike '%{searchStr}%' or s.supcust_no ilike '%{searchStr}%' or om.sheet_no ilike '%{searchStr}%') ";
            if (regionID.IsValid())
            {
                var lstRegion = regionID.Split(',');
                string regionCondi = "";
                foreach (var re in lstRegion)
                {
                    if (regionCondi != "") regionCondi += " or ";
                    regionCondi += $" other_region ilike '%/{re}/%' ";
                }
                condition += $" and ({regionCondi})";
            }
            if (currentLng.IsValid() && currentLat.IsValid())
            {
                distanceSQL += $",st_distance(ST_Transform(ST_SetSRID(addr_lnglat,4326)::geometry, 3857),ST_Transform('SRID=4326;POINT({currentLng} {currentLat})'::geometry, 3857)) as distance";
                // 同时按路线顺序和距离排序，路线顺序优先
                orderBy += "order by CASE WHEN drp.route_order > 0 THEN 0 ELSE 1 END, drp.route_order ASC, distance ASC";
            }
            else
            {
                // 只按路线顺序排序
                orderBy += "order by CASE WHEN drp.route_order > 0 THEN 0 ELSE 1 END, drp.route_order ASC, om.happen_time DESC";
            }

            if (ignorePayFailedSheetOnOrderToSale) condition += $" and (pb.bill_status = 'paid' or pb.bill_status is null)";
            
            if (!startDate.IsValid())
            {
                return new SqlResult() {ErrMsg="请指定起始日期" };
            }

            var queryTime = "";
            if (startDate.IsValid()) startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);
            queryTime += $" and om.happen_time >= '{startDate}' ";
            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and om.happen_time <= '{endDate}' ";
            }
            //业务员可以在送货员签收的页面查看业务员为自己的订单，这个功能暂时去掉，张杰鹏那边发现业务员误审了单子，这个功能是某个代理商提的，忘记谁了，等他发现后再谈论
            //var queryRole = isBoss ? "" : $@" and (oss.senders_id like '%{token.OperID}%' or om.senders_id like '%{token.OperID}%' or om.seller_id = {token.OperID})";

            string queryRole;// = isBoss ? "" : $@" and (oss.senders_id like '%{token.OperID}%' or om.senders_id like '%{token.OperID}%')";
            if (isBoss)//当查看单据范围为全部时，可以查看所有的单据，受片区权限限制
            {
                queryRole = condiRegions; 
            }
            else//否则就只能查看送货员是自己的单据
            {
                queryRole = $@" and (case when oss.senders_id is null then om.senders_id else oss.senders_id end like '%{token.OperID}%')";

            }
            var assignCondi = "";
            if (isAssignVanNecessary && !noVanOrderToSale) assignCondi += " and oss.order_status in ('zc','fh') ";
            if (sellerID.IsValid()) { assignCondi += $@" and om.seller_id = {sellerID}  "; }
           
             
            if (senderID.IsValid()) assignCondi += $@" and ( case when oss.senders_id is null then om.senders_id else oss.senders_id end like '%{senderID}%') ";
            if (branchID.IsValid()) assignCondi += $@" and om.branch_id = {branchID} ";
            if (carID.IsValid()) assignCondi += $@" and oss.van_id ={carID} ";
            
            // 添加路线规划关联查询
            string routePlanJoin = $@"
LEFT JOIN (
    SELECT d.sheet_id, d.route_order
    FROM delivery_route_detail d
    JOIN delivery_route_plan p ON d.plan_id = p.plan_id AND d.company_id = p.company_id
    WHERE p.company_id = {token.CompanyID}
    AND p.sender_id = {token.OperID}
    AND p.plan_date = CURRENT_DATE
    AND p.plan_status = 'active'
) drp ON om.sheet_id = drp.sheet_id";

            string sql_NoLimit =
@$"
SELECT
    s.supcust_id,
    s.addr_lng,s.addr_lat,s.sup_addr,
    sup_name,s.addr_lng,s.addr_lat,s.sup_addr,acct_cust_id,acct_cust_name,om.sheet_id AS order_sheet_id,om.sheet_no AS order_sheet_no,
    sm.sale_sheet_id,sm.sale_sheet_no,om.seller_id,om.make_brief,om.order_source, om.sheet_type, io.oper_name as seller_name,
    (case when oss.senders_name is null then om.senders_name else oss.senders_name end) as senders_name,
    oss.receipt_status,s.mobile AS sup_tel,om.happen_time,om.total_amount,
    round((om.total_amount -om.now_pay_amount - om.now_disc_amount)::numeric,4) left_amount,
    realtime->>'lastSaleTime' as last_sale_time,realtime->>'lastVisitTime' as last_visit_time,
    COALESCE(drp.route_order, 0) as route_order,
    sup_addr {distanceSQL}
FROM
(
   SELECT sheet_id,sheet_no,seller_id,senders_id,make_brief,order_source,happen_time,total_amount,supcust_id,company_id,senders_name,now_pay_amount,now_disc_amount,approve_time,red_flag,sheet_type,branch_id,pay_bill_id FROM sheet_sale_order_main 
	WHERE
		happen_time >= '{startDate}' AND happen_time <= '{endDate}'
	AND	company_id = {token.CompanyID} 
	AND approve_time IS NOT NULL 
	AND red_flag IS NULL 
    and coalesce(is_del, false) = false
    and is_imported is null
	ORDER BY happen_time DESC

)  om

LEFT JOIN info_supcust s ON om.supcust_id = s.supcust_id and s.company_id = {token.CompanyID}
LEFT JOIN info_operator io ON om.seller_id = io.oper_id and io.company_id = {token.CompanyID}
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = '{token.CompanyID}'
) A ON s.acct_cust_id = A.a_supcust_id and om.company_id = {token.CompanyID}
LEFT JOIN sheet_status_order oss ON om.sheet_id = oss.sheet_id and oss.company_id = {token.CompanyID} 
LEFT JOIN 
(
    select order_sheet_id, sheet_id as sale_sheet_id,red_flag, sheet_no as sale_sheet_no,senders_id,senders_name ,approve_time
    from sheet_sale_main where red_flag IS NULL and company_id = {token.CompanyID} and happen_time>='{startDate}'
) sm on om.sheet_id = sm.order_sheet_id and om.company_id = {token.CompanyID}
left join realtime_supcust r on om.supcust_id=r.supcust_id and r.company_id = {token.CompanyID}          

LEFT JOIN pay_bill pb on pb.company_id= '{token.CompanyID}' and pb.bill_id = om.pay_bill_id and pb.sheet_type = om.sheet_type and pb.sheet_id = om.sheet_id

{routePlanJoin}

WHERE 
    om.approve_time is not null and om.red_flag is null 
    and sm.approve_time is null and oss.receipt_status is null 

{assignCondi}
{queryRole} 
{condition} 
{queryTime}
";
 
            return MiniSQLParser.GetSQLResult(sql_NoLimit, getCount,getRows, orderBy, pageSize, startRow, "SUM(om.total_amount) as total_amount");
             
        }

        [HttpGet]
        public async Task<JsonResult> GetOrdersForSale_done(string operKey, int pageSize, int startRow, bool getTotal, string searchStr, string regionID, string currentLat, string currentLng, string supcustFlag, string operRegions, string startDate, string endDate, bool isBoss, string sellerID, string senderID, string branchID, string carID)
        {
            if (supcustFlag.IsInvalid()) supcustFlag = "C";
            var condition = $" and supcust_flag = '{supcustFlag}' ";
            if (operRegions.IsValid())
            {
                var regions = JsonConvert.DeserializeObject<int[]>(operRegions);
                if (regions.Length > 0)
                    condition += " and (" + string.Join(" or ", regions.Select(x => $"other_region  like '%/{x}/%'")) + ") ";
            }

            var distanceSQL = "";
            var orderBy = "";
            if (searchStr.IsValid()) condition += $" and (sup_name ilike '%{searchStr}%' or s.py_str ilike '%{searchStr}%' or s.mobile ilike '%{searchStr}%' or s.supcust_no ilike '%{searchStr}%' or om.order_sheet_no ilike '%{searchStr}%') ";
            if (regionID.IsValid())
            {
                var lstRegion = regionID.Split(',');
                string regionCondi = "";
                foreach (var re in lstRegion)
                {
                    if (regionCondi != "") regionCondi += " or ";
                    regionCondi += $" other_region ilike '%/{re}/%' ";
                }
                condition += $" and ({regionCondi})";
            }
         
            orderBy += "order by sm.approve_time desc";
           
            var queryTime = "";
        
            startDate = CPubVars.GetDateText(startDate);
            if (endDate.IsValid()) endDate = CPubVars.GetDateText(endDate);

            DateTime dtMonthsAgo = Convert.ToDateTime(startDate).AddMonths(-3); 
             

            queryTime += $" and sm.happen_time >= '{startDate}'"; 
            if (endDate.IsValid())
            {
                if (!endDate.EndsWith("23:59:59")) endDate += " 23:59:59";
                queryTime += $" and sm.happen_time <= '{endDate}' ";
            }
            var queryRole = isBoss ? "" : $@" and (sm.senders_id like '%{Token.OperID}%' or sm.seller_id = {Token.OperID})";
            var assignCondi = "";

            if (sellerID.IsValid()) { assignCondi += $@" and sm.seller_id = {sellerID}  "; }
            // if (sellerID.IsValid()) { assignCondi += $@" and (sm.seller_id = {sellerID} or sm.seller_id = {operID}) "; }
            // else
            // {
            //     assignCondi += $@" and sm.seller_id = {operID} ";
            // }
            if (senderID.IsValid()) assignCondi += $@" and ( sm.senders_id like '%{senderID}%') ";
            if (branchID.IsValid()) assignCondi += $@" and om.branch_id = {branchID} ";
            if (carID.IsValid()) assignCondi += $@" and oss.van_id ={carID} ";

            SQLQueue QQ = new SQLQueue(cmd);
             
            string sql_NoLimit = @$"
SELECT
    s.supcust_id,
    sup_name,s.addr_lng,s.addr_lat,s.sup_addr,acct_cust_id, acct_cust_name, order_sheet_id,order_sheet_no,sm.sheet_id  sale_sheet_id,
    sm.sheet_no  sale_sheet_no,sm.seller_id,sm.make_brief,sm.order_source,sm.sheet_type,io.oper_name as seller_name,sm.senders_name,oss.receipt_status,
    s.mobile AS sup_tel,sm.happen_time,sm.total_amount*sm.money_inout_flag total_amount,round((sm.total_amount -sm.now_pay_amount - sm.now_disc_amount)::numeric,4) left_amount,
    realtime->>'lastSaleTime' as last_sale_time,realtime->>'lastVisitTime' as last_visit_time,
    sup_addr {distanceSQL}
FROM
	sheet_sale_main sm
LEFT JOIN info_supcust s ON sm.supcust_id = s.supcust_id and s.company_id = {Token.CompanyID}
LEFT JOIN info_operator io ON sm.seller_id = io.oper_id and io.company_id = {Token.CompanyID}
LEFT JOIN 
( 
    SELECT supcust_id AS a_supcust_id, sup_name AS acct_cust_name FROM info_supcust where company_id = {Token.CompanyID}
) A ON s.acct_cust_id = A.a_supcust_id and s.company_id = {Token.CompanyID}
LEFT JOIN 
(
    select  sheet_id,sheet_no as order_sheet_no,branch_id from  sheet_sale_order_main
	where red_flag IS NULL and company_id = {Token.CompanyID} and happen_time>='{CPubVars.GetDateText(dtMonthsAgo)}' and coalesce(is_del, false) = false
) om  on sm.order_sheet_id= om.sheet_id  and sm.company_id = {Token.CompanyID} 
LEFT JOIN sheet_status_order oss ON sm.company_id=oss.company_id and sm.order_sheet_id = oss.sheet_id  and oss.company_id = {Token.CompanyID} 
left join realtime_supcust r on sm.company_id=r.company_id and s.supcust_id=r.supcust_id   and s.company_id = {Token.CompanyID}   and r.company_id = {Token.CompanyID}        
where sm.company_id = {Token.CompanyID} 
and sm.approve_time is not null 
and sm.red_flag is null 
and sm.order_sheet_id is not null  
{queryRole} 
{condition} 
{queryTime}
{assignCondi}


";



            var sql = $"{sql_NoLimit}  {orderBy} limit {pageSize} offset {startRow};";
            QQ.Enqueue("data", sql);


            if (getTotal)
            {
                sql = $"select count(*) as total,sum(t.total_amount) as total_amount from ({sql_NoLimit}) t";
                QQ.Enqueue("count", sql);
            }

            var dr = await QQ.ExecuteReaderAsync();
            List<ExpandoObject> data = null;
            string total = "";
            string totalAmount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
            
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);

                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    totalAmount = CPubVars.GetTextFromDr(dr, "total_amount"); 
                }
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", msg = "", data, total,totalAmount });

        }

        /// <summary>
        /// 保存送货路线规划顺序
        /// </summary>
        /// <param name="data">包含路线规划数据的JSON对象</param>
        /// <returns>保存结果</returns>
        [HttpPost]
        public async Task<JsonResult> SaveDeliveryRouteOrder([FromBody] dynamic data)
        {
            try
            {
                // 从operKey获取公司ID和操作员ID
                Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
                
                // 获取路线数据
                var routeData = JsonConvert.DeserializeObject<List<DeliveryRouteItem>>(JsonConvert.SerializeObject(data.routeData));
                
                if (routeData == null || routeData.Count == 0)
                {
                    return new JsonResult(new { result = "Error", msg = "路线数据为空" });
                }

                // 开始事务
                var tran = await cmd.Connection.BeginTransactionAsync();
                try
                {
                    // 检查是否已有当天的路线规划
                    string checkSql = $@"
                        SELECT plan_id FROM delivery_route_plan 
                        WHERE company_id = {companyID} 
                        AND sender_id = {operID} 
                        AND plan_date = CURRENT_DATE
                        AND plan_status = 'active'";
                    
                    cmd.CommandText = checkSql;
                    var planId = await cmd.ExecuteScalarAsync();
                    
                    // 如果没有当天的路线规划，创建一个新的
                    if (planId == null)
                    {
                        string createPlanSql = $@"
                            INSERT INTO delivery_route_plan 
                            (company_id, sender_id, plan_date, plan_status, create_time) 
                            VALUES 
                            ({companyID}, {operID}, CURRENT_DATE, 'active', '{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}')
                            RETURNING plan_id";
                        
                        cmd.CommandText = createPlanSql;
                        planId = await cmd.ExecuteScalarAsync();
                    }
                    
                    // 删除现有的路线详情
                    string deleteSql = $@"
                        DELETE FROM delivery_route_detail 
                        WHERE company_id = {companyID} 
                        AND plan_id = {planId}";
                    
                    cmd.CommandText = deleteSql;
                    await cmd.ExecuteNonQueryAsync();
                    
                    // 插入新的路线详情
                    foreach (var item in routeData)
                    {
                        string insertSql = $@"
                            INSERT INTO delivery_route_detail 
                            (company_id, plan_id, sheet_id, route_order, addr_lng, addr_lat) 
                            VALUES 
                            ({companyID}, {planId}, '{item.order_sheet_id}', {item.route_order}, '{item.addr_lng}', '{item.addr_lat}')";
                        
                        cmd.CommandText = insertSql;
                        await cmd.ExecuteNonQueryAsync();
                    }
                    
                    // 提交事务
                    tran.Commit();
                    
                    return new JsonResult(new { result = "OK", msg = "路线规划保存成功" });
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    tran.Rollback();
                    
                    // 记录错误日志
                    NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                    logger.Error($"保存路线规划失败: {ex.Message}");
                    MyLogger.LogMsg($"保存路线规划失败: {ex.Message}", companyID);
                    
                    return new JsonResult(new { result = "Error", msg = "保存路线规划失败: " + ex.Message });
                }
            }
            catch (Exception ex)
            {
                // 记录错误日志
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error($"保存路线规划失败: {ex.Message}");
                
                return new JsonResult(new { result = "Error", msg = "保存路线规划失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 送货路线规划项目数据结构
        /// </summary>
        public class DeliveryRouteItem
        {
            /// <summary>
            /// 订单ID
            /// </summary>
            public string order_sheet_id { get; set; }
            
            /// <summary>
            /// 路线顺序
            /// </summary>
            public int route_order { get; set; }
            
            /// <summary>
            /// 经度
            /// </summary>
            public string addr_lng { get; set; }
            
            /// <summary>
            /// 纬度
            /// </summary>
            public string addr_lat { get; set; }
        }

        /// <summary>
        /// 撤销抢单
        /// </summary>
        /// <param name="data">包含sheet_id的JSON对象</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public async Task<JsonResult> CancelGrabSheet(string operKey,[FromBody] dynamic data)
        {
            try
            {
                string sheetId = data.sheet_id;
                Security.GetInfoFromOperKey(operKey, out string companyId, out string operId);
                
                // 验证单据是否存在并检查状态
                string checkSql = $@"
                    SELECT sm.sheet_id, sm.sheet_no, sm.senders_id, 
                           oss.order_status, 
                           CASE WHEN op.sale_order_sheet_id IS NOT NULL THEN true ELSE false END AS is_assigned_van,
                           CASE WHEN m.sale_sheet_id IS NOT NULL THEN true ELSE false END AS is_converted_to_sale
                    FROM sheet_sale_order_main sm
                    LEFT JOIN sheet_status_order oss ON sm.sheet_id = oss.sheet_id AND sm.company_id = oss.company_id
                    LEFT JOIN op_move_to_van_detail op ON sm.sheet_id = op.sale_order_sheet_id AND sm.company_id = op.company_id
                    LEFT JOIN (
                        SELECT order_sheet_id, sheet_id as sale_sheet_id 
                        FROM sheet_sale_main 
                        WHERE red_flag IS NULL AND company_id = {companyId}
                    ) m ON sm.sheet_id = m.order_sheet_id
                    WHERE sm.company_id = {companyId} 
                    AND sm.sheet_id = '{sheetId}'
                    AND coalesce(sm.is_del, false) = false";
                
                cmd.CommandText = checkSql;
                dynamic sheet = await CDbDealer.Get1RecordFromSQLAsync(checkSql, cmd);
                
                if (sheet == null)
                {
                    return new JsonResult(new { result = "Error", msg = "订单不存在或已被删除" });
                }
                
                if (sheet.senders_id == null || sheet.senders_id.ToString() != operId)
                {
                    return new JsonResult(new { result = "Error", msg = "只能撤销自己抢的单" });
                }
                
                // 检查是否已装车
                if (sheet.is_assigned_van=="True")
                {
                    return new JsonResult(new { result = "Error", msg = $"订单 {sheet.sheet_no} 已装车，无法撤销抢单" });
                }
                
                // 检查是否已转单
                if (sheet.is_converted_to_sale == "True")
                {
                    return new JsonResult(new { result = "Error", msg = $"订单 {sheet.sheet_no} 已转单，无法撤销抢单" });
                }
                
                // 检查订单状态
                if (sheet.order_status == "zd")
                {
                    return new JsonResult(new { result = "Error", msg = $"订单 {sheet.sheet_no} 已转单，无法撤销抢单" });
                }
                
                if (sheet.order_status == "pzc" || sheet.order_status == "zc")
                {
                    return new JsonResult(new { result = "Error", msg = $"订单 {sheet.sheet_no} 已装车，无法撤销抢单" });
                }
                
                // 更新订单，清除送货员信息
                string updateSql = $@"
                    UPDATE sheet_sale_order_main 
                    SET senders_id = NULL, senders_name = NULL 
                    WHERE company_id = {companyId} 
                    AND sheet_id = '{sheetId}'";
                
                cmd.CommandText = updateSql;
                await cmd.ExecuteNonQueryAsync();
                
                
                
                // 记录操作日志
                string logSql = $@"
                    INSERT INTO sheet_log (company_id, sheet_id, sheet_type, oper_id,  happen_time,                                           action)
                    VALUES              ({companyId}, '{sheetId}', 'SO',    {operId}, '{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}', 'cancel_grab')";
                
                cmd.CommandText = logSql;
                await cmd.ExecuteNonQueryAsync();
                
                return new JsonResult(new { result = "OK", msg = "" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { result = "Error", msg = "撤销抢单失败: " + ex.Message });
            }
        }
    }
}
