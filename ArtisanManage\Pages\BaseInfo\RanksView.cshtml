@page
@model ArtisanManage.Pages.BaseInfo.RanksViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">

    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>

    <script type="text/javascript">
        var frame = "RankEdit";

        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var RowIndex = -1;

        window.addEventListener('message', function (rs) {
            $("#popItem").jqxWindow('close');
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "RankEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var row = rs.data.record;
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;
                        rows[0].i = row.rank_id
                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "rank_name", rs.data.record.rank_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "rank_note", rs.data.record.rank_note);
                }
                $("#popItem").jqxWindow('close');
            };
            
        });
            var m_db_id = "10";

    	    var newCount = 1;

        function btnAddItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="${frame}?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

        }
        function onGridRowEdit(rowIndex) {
            var rank_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'i');
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="RankEdit?operKey=' + g_operKey + '&rank_id=' + rank_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }
    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                    console.log(args);
                    if (args.datafield == "rank_name") {
                        if (args.originalEvent.button == 2) return;
                        var rank_id = args.row.bounddata.i;
                        RowIndex = args.rowindex;
                        if (ForSelect) {
                            var rank_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                            var rank_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "rank_name");
                            var msg = {
                                msgHead: 'RanksView', action: 'select', rank_id: rank_id, rank_name: rank_name
                            };
                            window.parent.postMessage(msg, '*');
                        }
                        else {
                            onGridRowEdit(args.rowindex);
                        }
                    }
                });
                $("#Cancel").on('click', function () {
                    for (var i = 0; i < 10; i++) {
                        $('#jqxgrid').jqxGrid('deleterow', i);
                        $('#jqxgrid').jqxGrid('addrow', i, {})
                    }
                });

                $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                
                $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
                });
                QueryData();
    	    });
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
    </style>
</head>


<body>
  
    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;">
        <div><input id="searchString" class="margin" placeholder="请输入简拼/名称" /><button onclick="QueryData()" class="margin">查询</button></div>
        <div><button onclick="btnAddItem_click()" class="margin">新增等级</button></div>
    </div>
    
    <div id="gridItems" style="margin-top:10px;width:calc(100% - 20px);height:100%;margin-bottom:10px; "></div>
  

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">客户等级</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>