﻿using ArtisanManage.Models;
using ArtisanManage.MyCW;
using ArtisanManage.Services;
using ArtisanManage.Pages.CwPages;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using NPOI.XWPF.UserModel;
using Org.BouncyCastle.Pqc.Crypto.Saber;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Threading.Tasks;
using static ArtisanManage.Services.CommonTool;
using NPOI.SS.Formula.Functions;
using static System.Runtime.InteropServices.JavaScript.JSType;
using static ArtisanManage.Models.AttenceLeave;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using NPOI.XSSF.Streaming.Values;
using static Org.BouncyCastle.Bcpg.Attr.ImageAttrib;

namespace ArtisanManage.MyJXC
{
    public class SheetLoan<TROW> : SheetBase<TROW> where TROW : SheetRowBase, new()
    {
        [SaveToDB][FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB][FromFld] public string partner_id { get; set; }//借贷款单位
        [FromFld(LOAD_PURPOSE.SHOW)] public string partner_name { get; set; }
        public string loan_sub_id { get; set; }
        [SaveToDB][FromFld] public string loan_partner_id//sub_id 借款科目（业务中的贷款概念在财务里叫借款，即借入）
        {
            get
            {
                return loan_sub_id;
            }
            set
            {
                this.loan_sub_id = value;
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public string loan_partner_name { get; set; }
        [SaveToDB][FromFld] public string getter_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string getter_name { get; set; } = "";

        public decimal rate_m_show { get; set; } //e.g. 1333  （decimal自带默认值0，不用赋默认值）
        [SaveToDB][FromFld] public decimal interest_rate_month //月利率 e.g.  0.1333
        {
            get //从前端获取数据，保存数据库
            {
                decimal rm = Math.Round(rate_m_show / 100, 6);//前端带百分号保留4位小数，数据库没有百分号存入最多6位小数
                return rm;
            }
            set //读取数据库，返回前端
            {
                decimal rm = value * 100;
                rm = Convert.ToDecimal(rm.ToString("G29"));//去掉小数末尾0
                this.rate_m_show = rm;
            } 
        }
        public decimal rate_y_show { get; set; } 
        [SaveToDB][FromFld] public decimal interest_rate_year //年利率
        {
            get //从前端获取数据，保存数据库
            {
                decimal ry = Math.Round(rate_y_show / 100, 6);//前端带百分号保留4位小数，数据库没有百分号存入最多6位小数
                return ry;
            }
            set //读取数据库，返回前端
            {
                decimal ry = value * 100;
                ry = Convert.ToDecimal(ry.ToString("G29"));
                this.rate_y_show = ry;
            }
        }
        [SaveToDB][FromFld] public DateTime repay_date { get; set; } = DateTime.Now.Date.AddDays(1).AddSeconds(-1);//还款日期
        [SaveToDB][FromFld] public int installment_count { get; set; } = 0;//贷款期数（月）
        [SaveToDB][FromFld] public string repay_way { get; set; } = "";//利息结算方式（本息处理方式）
        [FromFld("(case t.repay_way when 'EPI' then '等额本息' when 'IOP' then '先息后本' else '' end)")] public string repay_way_name { get; set; }
        [SaveToDB][FromFld] public string repay_period { get; set; } = "";//还款方式（几期一还）
        [FromFld("(case t.repay_period when 'M' then '按月还款' when 'Y' then '按年还款' when 'SY' then '按半年还款' when 'Q' then '按季度还款' else '' end)")] public string repay_period_name { get; set; }

        [SaveToDB][FromFld] public override decimal total_amount { get; set; } = 0;
        [SaveToDB][FromFld] public decimal now_pay_amount { get; set; } = 0;//=total_amount;
        [SaveToDB][FromFld] public override int money_inout_flag { get; set; } = 1;
        [SaveToDB][FromFld] public string payway1_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway1_amount { get; set; } = 0;
        [SaveToDB][FromFld] public string payway2_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway2_amount { get; set; } = 0;
        

        public SheetLoan() : base("sheet_loan", "", LOAD_PURPOSE.SHOW)
        {
            sheet_type = SHEET_TYPE.SHEET_LOAN_MONEY;
            ConstructFun();
        }

        public SheetLoan(LOAD_PURPOSE loadPurpose) : base("sheet_loan", "", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_LOAN_MONEY;
            ConstructFun();
        }

        private void ConstructFun()
        {
            if (LoadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @" left join info_loan_partner c on c.company_id=~COMPANY_ID and t.partner_id=c.partner_id
                                  left join (select oper_id,oper_name as getter_name from info_operator where company_id=~COMPANY_ID) getter on t.getter_id=getter.oper_id
                                  left join (select oper_id,oper_name as maker_name from info_operator where company_id=~COMPANY_ID) maker on t.maker_id=maker.oper_id
                                  left join  (select oper_id,oper_name as approver_name from info_operator where company_id=~COMPANY_ID) approver on t.approver_id=approver.oper_id
                                  left join (select sub_id,sub_name as payway1_name from cw_subject where company_id=~COMPANY_ID) pw1 on t.payway1_id=pw1.sub_id
                                  left join (select sub_id,sub_name as payway2_name from cw_subject where company_id=~COMPANY_ID) pw2 on t.payway2_id=pw2.sub_id
                                  left join (select sub_id,sub_name as loan_partner_name from cw_subject where company_id=~COMPANY_ID) pre on t.loan_partner_id=pre.sub_id
              ";
            }
            DetailLeftJoin = "";
        }

        public override string GetSheetCharactor()
        {
            string res = $"{this.company_id}_{this.OperID}_{this.loan_partner_id}_{this.total_amount.ToString()}_{this.payway1_id}_{this.payway2_id}_{this.make_brief}";
            return res;
        }

        protected override void InitForSave()
        {
            base.InitForSave();
            if (getter_id == "") getter_id = OperID;
            if (maker_id == "") maker_id = OperID;
            if (approver_id == "") approver_id = OperID;
            money_inout_flag = red_flag == "2" ? -1 : 1;
        }

        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            if (partner_id == "") return "必须指定借贷款单位";
            if (getter_id == "" && IsFromWeb) return "必须指定业务员";
            if (loan_partner_id == "") return "必须指定账户";
            if (total_amount <= 0) return "贷款总额必须大于0";
            if (payway1_id == "" || payway1_amount == 0) return "必须指定支付方式";
            if (interest_rate_month < 0) return "利率不能小于0";
            if ((double)(interest_rate_year - interest_rate_month * 12) > 0.001) return "年利率与月利率换算错误";
            if (installment_count < 0) return "贷款期数不能小于0";
            if (installment_count >= 1000) return "贷款期数不能超过1000";
            if (installment_count != 0 && repay_way == "") return "必须指定利息结算方式";
            if (installment_count != 0 && repay_period == "") return "必须指定还款方式";
            if (Math.Abs(now_pay_amount - (payway1_amount + payway2_amount)) > 0.05m) return "支付方式合计与总支付金额不一致";
            if (installment_count == 0 && interest_rate_year != 0) return "贷款期数为0时，利率也为0，请修改利率";

            return "OK";
        }

        public class CInfoForApprove : CInfoForApproveBase
        {
            public List<Subject> PaywaysInfo = new List<Subject>();
        }

        public class Subject
        {
            public string sub_id { get; set; }
            public string sub_name { get; set; }
            public string sub_type { get; set; }
        }

        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            string sql;
            string sub_ids = string.Join(',', new string[] { payway1_id, payway2_id }.Where(p => p != ""));
            if (sub_ids != "")
            {
                sql = $"select sub_id, sub_name, sub_type from cw_subject where company_id={company_id} and sub_id in ({sub_ids});";
                QQ.Enqueue("payway_type", sql);
            }
            if (red_flag == "2") 
            {
                sql = $"select sheet_id, installment_no from sheet_loan_repay_plan where company_id={company_id} and sheet_id={red_sheet_id} and status in ('part', 'all')";
                QQ.Enqueue("loan_paid", sql);
            }
        }

        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }

        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);

            if (sqlName == "payway_type")
            {
                info.PaywaysInfo = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
            else if (sqlName == "loan_paid")
            {
                dynamic loanPaid = CDbDealer.Get1RecordFromDr(dr, false);
                if (loanPaid != null) 
                {
                    info.ErrMsg = $"该贷款单第{loanPaid.installment_no}期已有还款记录，请先红冲还贷款单";
                }
            }

        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";
            string redSql = "";

            if (red_flag == "2")
            {
                redSql += $"update sheet_loan_repay_plan set red_flag = '1' where company_id = {company_id} and sheet_id={red_sheet_id};";
            }
            else
            {
                if (total_amount != 0)
                {
                    int monthesPerPeriod;
                    switch (repay_period)
                    {
                        case "Y":// 每年还款一次
                            monthesPerPeriod = 12;
                            break;
                        case "SY":// 每半年还款一次
                            monthesPerPeriod = 6;
                            break;
                        case "Q"://按季度还
                            monthesPerPeriod = 3;
                            break;
                        default://默认按月还款
                            monthesPerPeriod = 1;
                            break;
                    }
                    if (installment_count != 0 && monthesPerPeriod != 1&& installment_count % monthesPerPeriod != 0)
                    {
                        info.ErrMsg = $"【贷款期数 {installment_count}】与【还款方式：{repay_period_name}】换算失败";
                        return;
                    }
					sql += CalculateRepaymentPlan(sheetID, total_amount, interest_rate_year, installment_count, monthesPerPeriod, repay_way);
                }
            }

            #region 更新现金银行余额
            string sql_cb = "";
            if (info.BizStartPeriod != "" && info.PaywaysInfo != null && !IsImported)
            {
                Dictionary<string, decimal> pws = new Dictionary<string, decimal>();
                Subject pw1 = info.PaywaysInfo.Find(p => p.sub_id == payway1_id && p.sub_type == "QT");
                if (pw1 != null && payway1_amount != 0)
                {
                    if (!pws.ContainsKey(payway1_id)) pws.Add(payway1_id, payway1_amount);
                    else pws[payway1_id] += payway1_amount;
                }
                Subject pw2 = info.PaywaysInfo.Find(p => p.sub_id == payway2_id && p.sub_type == "QT");
                if (pw2 != null && payway2_amount != 0)
                {
                    if (!pws.ContainsKey(payway2_id)) pws.Add(payway2_id, payway2_amount);
                    else pws[payway2_id] += payway2_amount;
                }
                if (pws.Count() > 0)
                {
                    sql_cb = base.UpdateCashBankBalance(pws);
                }
            }
            #endregion

            cmd.CommandText = redSql + sql + sql_cb;
            if (cmd.CommandText != "")
            {
                await cmd.ExecuteNonQueryAsync();
            }
        }

        /// <param name="sheetID">贷款单单号</param>
        /// <param name="principal">贷款单本金</param>
        /// <param name="R_year">年利率</param>
        /// <param name="loanTerm">还款总月数</param>
        /// <param name="monthsPerPeriod">还款频率</param>
        /// <param name="repaymentMethod">利息结算方式</param>
        public string CalculateRepaymentPlan(string sheetID, decimal principal, decimal R_year, int loanTerm, int monthsPerPeriod, string repaymentMethod)
        {
            string loanPlanSql = @"insert into sheet_loan_repay_plan (company_id, sheet_id, status, repay_way, repay_period, installment_count, installment_no, period_range, repay_date, period_total_due, principal_due, interest_due) values ";
            //公司ID，贷款单号，还款状态，利息结算方式，还款频率，总期数，当前期数，本期始末时间段，本期还款日，本期待还总额（本金+利息），本期待还利息，本期待还本金，本期还款后待还本金
            decimal R_period = R_year / 12 * monthsPerPeriod;//期间利息（比如：按季还款，则算三个月的利息）
            decimal totalPeriods = loanTerm / monthsPerPeriod;//还款总期数（次数，不是月数）
            string periodRange = $"{happen_time} ~ {repay_date.ToString("yyyy-MM-dd HH:mm:ss")}";//当前还款期间

            decimal instDue = 0;//本期待还利息
            decimal principalDue = 0;//本期待还本金
            decimal totalDue = 0;//本期待还本金+利息
            decimal remainingPrincipal = Math.Round(principal, 2);//本期还款后待还本金

            if (totalPeriods == 0)//一次全部还款，没有利息
            {
                instDue = 0;
                principalDue = Math.Round(principal, 2);
                totalDue = principalDue;

                loanPlanSql += $"({company_id}, {sheetID}, 'no', '{repay_way}', '{repay_period}', '1', '1', '{periodRange}', '{repay_date}', '{totalDue}', '{principalDue}', '{instDue}'),";
            }
            else
            {
                if (repaymentMethod == "EPI")//等额本息
                {
                    // 计算每月还款（如果没有利率就直接除总期数，有利率参考等额本息公式：https://baike.baidu.com/item/%E7%AD%89%E9%A2%9D%E6%9C%AC%E6%81%AF/3227456）
                    if (R_period == 0)
                    {
                        totalDue = Math.Round(principal / totalPeriods, 2);
                    }
                    else
                    {
                        totalDue = (decimal)((double)principal * ((double)R_period * Math.Pow(1 + (double)R_period, (double)totalPeriods)) / (Math.Pow(1 + (double)R_period, (double)totalPeriods) - 1));
                        totalDue = Math.Round(totalDue, 2);
                    }
                }
                else if (repaymentMethod == "IOP")//先息后本
                {
                    instDue = Math.Round(principal * R_period, 2);
                }

                DateTime periodStart = Convert.ToDateTime(happen_time);
                DateTime periodEnd = Convert.ToDateTime(repay_date);
                for (int period = 1; period <= totalPeriods; period++)
                {
                    periodStart = Convert.ToDateTime(happen_time).AddMonths((period - 1) * monthsPerPeriod);
                    periodEnd = Convert.ToDateTime(happen_time).AddMonths(period * monthsPerPeriod);
                    periodRange = $"{periodStart.ToString("yyyy-MM-dd HH:mm:ss")} ~ {periodEnd.ToString("yyyy-MM-dd HH:mm:ss")}";

                    if (repaymentMethod == "EPI")//等额本息：每期总还款不变，利息按剩余本金计算
                    {
                        if(period != totalPeriods)
                        {
                            instDue = Math.Round(remainingPrincipal * R_period, 2);
                            principalDue = Math.Round(totalDue - instDue, 2);
                        }
                        else// Last period
                        {
                            principalDue = remainingPrincipal; // All paid off
                            instDue = Math.Round(totalDue - principalDue, 2);
                        }
                            
                        loanPlanSql += $"({company_id}, {sheetID}, 'no', '{repay_way}', '{repay_period}', '{totalPeriods}', '{period}', '{periodRange}', '{periodEnd}', '{totalDue}', '{principalDue}', '{instDue}'),";
                    }
                    else if (repaymentMethod == "IOP")//先息后本：本金固定，每期利息固定，最后一期还本金
                    {
                        if (period != totalPeriods) 
                        {
                            totalDue = instDue;
                        }
                        else// Last period
                        {
                            totalDue = Math.Round(remainingPrincipal + instDue, 2);
                            principalDue = remainingPrincipal; // All paid off
                        }

                        loanPlanSql += $"({company_id}, {sheetID}, 'no', '{repay_way}', '{repay_period}', '{totalPeriods}', '{period}', '{periodRange}', '{periodEnd}', '{totalDue}', '{principalDue}', '{instDue}'),";
                    }
                    remainingPrincipal -= principalDue;//仅用于计算，不保存（对于EPI，最后一期还会有remainingPrincipal的余额，这不重要，因为totalDue是一开始就固定的）
                }
            }

            loanPlanSql = $"{loanPlanSql.TrimEnd(',')};";
            return loanPlanSql;
        }

    }

}

