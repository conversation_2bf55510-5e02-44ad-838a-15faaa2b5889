﻿@page
@model ArtisanManage.Pages.Report.ApproachingAlertModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
                $('#item_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popItem').jqxWindow('open');
                        $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            $("#popAlert").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 500, width: 700, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            QueryData();
        });
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "selectMulti") {
                    if (rs.data.checkedRows.length == 1) {
                        var item_id = rs.data.checkedRows[0].item_id;
                        var item_name = rs.data.checkedRows[0].item_name;
                    }
                    var rows = rs.data.checkedRows
                    var items_id = ''
                    rows.forEach(function (row) {
                        if (items_id != '') items_id += ','
                        items_id += row.item_id
                    })
                    $('#item_id').jqxInput('val', { value: item_id, label: item_name });
                    $.ajax({
                        url: '/api/SaleSheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: items_id },
                        success: function (data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });
                }
                $('#popItem').jqxWindow('close');
            }
        });
        function viewAlertDetail(row, column, value, p4, p5, rowData) {
            debugger
            return `<div onclick='showAlertDetail(${rowData.alert_detail})' style="height:100%;display:flex;align-items:center;justify-content:center;color:#4499ff;cursor:pointer;" >${value}</div>`
        }
        var cellsrenderer = function (row, columnfield, value, defaulthtml, columnproperties) {
            // return '<div style="height:100%;display:flex;align-items:center;justify-content:center;color: #4499ff;">' + value + '</div>';
            return '<div style="height:100%;display:flex;align-items:center;justify-content:center">' + value + '</div>';
        }
        function showAlertDetail(cellValue) {
            let gridData = []
            cellValue.forEach(cell => {
                if (!cell.stock_qty || !cell.sel_pend_qty) {
                    gridData.push({
                        produce_date: cell.produce_date ? cell.produce_date : "无产期",
                        batch_no: cell.produce_date ? "" : "无批次",
                        valid_days: cell.valid_days,
                        deadline:cell.deadline,
                        left_days:cell.left_days,
                        stock_qty_spec: cell.stock_qty_spec,
                    })
                }
            })
            var source =
            {
                localdata: gridData,
                datatype: "array"
            };

            let columns = [
                {
                    text: "生产日期",
                    datafield: "produce_date",
                    width: 100,
                },
                {
                    text: "批次",
                    datafield: "batch_no",
                    width: 100,
                },
                {
                    text: "保质期",
                    datafield: "valid_days",
                    width: 100,
                },
                {
                    text: "到期日期",
                    datafield: "deadline",
                    width: 100,
                },
                {
                    text: "剩余到期天数",
                    datafield: "left_days",
                    width: 100,
                },
                {
                    text: "预警库存数量（小单位）",
                    datafield: "stock_qty_spec",
                    width: 160,
                },
            ]
            var dataAdapter = new $.jqx.dataAdapter(source);
            $('#alerthDetailGrid').jqxGrid({
                source: dataAdapter,
                columns: columns,
                width: "100%",
                height: "100%"
            })
            $('#popAlert').jqxWindow('open');
        }
    </script>
</head>

<body>
    <div id="divTitle" style="text-align:center;height:45px;margin-top:5px;">
        <label id="lblSheetTitle" style="font-weight:500;font-size:25px;">@Html.Raw(Model.PageTitle)</label>
        <img id="imgState" style="display:none;position:fixed;top:0px;left:calc(50% - 150px);" src="" />

    </div>
    <div>
        <div id="divHead" class="headtail" style="margin-bottom:10px;margin-top:0px;">
            <div style="float:none;height:0px; clear:both;"></div>
            <button onclick="QueryData()" style="margin-left:20px;width:60px" class="main-button">查询</button>
        <button onclick="ExportExcel()" style="margin-left:20px;width:60px">导出</button>
        </div>
    </div>
    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>

    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择商品</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popAlert" style="display:none">
        <div id="alertCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">预警明细</span></div>
        <div id="alertBox" style="width:100%;height:calc(100%-30px);box-sizing:border-box;padding:20px;overflow:hidden;">
            <div id="alerthDetailGrid"></div>
        </div>
    </div>
</body>
</html>