﻿using System;
using System.Collections.Generic;
using System.Text;
using ArtisanManage.Models;
namespace myJXC
{
    public class CCode
    {
        public Dictionary<string, CFunction> m_dicCalledFun = new Dictionary<string, CFunction>();
        public Dictionary<string, CVariable> m_dicCalledVariable = new Dictionary<string, CVariable>();
        public bool m_bCollectingFunVar = false;
        public Dictionary<string, CVariable> m_dicPeriodSale = new Dictionary<string, CVariable>();
        public Dictionary<string, CVariable> m_dicClassSale = new Dictionary<string, CVariable>();
 
        public object getExpressionValue(bool bTest, string strExp, ref string strErr)
        {
            string funcName = "";
            return getExpressionValue(bTest, strExp, ref strErr, ref funcName);
        }
        public object getExpressionValue(bool bTest, string strExp, ref string strErr, ref string funcName)
        {
            if (strExp == "")
            {
                return "";
            }
            funcName = "";
            // If DEAL_ERROR Then On Error GoTo ErrDeal
            strErr = "";
            int n1=-1; int n2=-1; string c = ""; object s=null;  
            string sStartPos = "";
            bool bInYinHao = false;
            string strfun = "";
            int flg; int i;
            flg = 0;
          
            n1 = strExp.IndexOf("(");// InStr(strExp, "(")
            bool bN1InYinHao = false; bool bN2InYinHao = false;
            if (n1 >= 0)//there is a "()"
            {
                n1 = -1;
                for (i = 0; i < strExp.Length; i++)
                {
                    c = strExp.Substring(i, 1);
                    if (c == "\"")
                        bInYinHao = !bInYinHao;

                    if (!bInYinHao)
                    {
                        if (c == "(")
                        {
                            flg = flg + 1;

                            if (n1 == -1)
                            {
                                n1 = i;
                                bN1InYinHao = bInYinHao;
                            }
                        }
                        else if (c == ")")
                            flg = flg - 1;

                        if (flg == 0 && n1 != -1)
                        {
                            n2 = i;
                            bN2InYinHao = bInYinHao;
                            break;
                        }
                        else if (flg < 0)
                        {
                            strErr = "Too many )";
                            return "";
                        }
                    }

                }
                if (flg > 0)
                {
                    strErr = "lack ) ";
                    return "";
                }
            }


            string strExp1 = ""; object v=null;  
            //'test
            //'If bTest Then
            string strOp_L = ""; string strOp_R = "";
            int n_ExpStart; string sExpOrig = "";
            n_ExpStart = 0;
            if (n2 > 0 && n1 >=0)
            {
                bInYinHao = bN2InYinHao;
                for (i = n2 + 1; i < strExp.Length; i++)
                {
                    c = strExp.Substring(i, 1);
                    if (c == "\"") bInYinHao = !bInYinHao;
                    if (!bInYinHao)
                    {

                        if (c == " ")
                        {
                            if (strOp_R != "")
                                break;
                        }
                        else if ("*/+-\\&".Contains(c))
                        {
                            strOp_R = c;
                            break;
                        }
                        else if (c == ")")
                        {
                            break;
                        }
                        else if (c == "(")
                        {
                            break;
                        }
                        else
                            strOp_R = strOp_R + c;


                    }
                }
                strOp_R = strOp_R.ToLower().Trim();
                switch (strOp_R)
                {
                    case "":
                    case "*":
                    case "/":
                    case "+":
                    case "-":
                    case "\\":
                    case "mod":
                    case "&":
                        break;
                    default:
                        strErr = "Unknown Operator:" + strOp_R;
                        return "";
                }

                bInYinHao = bN1InYinHao;
                for (i = n1 - 1; i >= 0; i--) // To 1 Step (-1)
                {
                    c = strExp.Substring(i, 1);// Mid(strExp, i, 1)
                    if (c == "\"") bInYinHao = !bInYinHao;
                    if (!bInYinHao)
                    {
                        if ("*/+-\\&".Contains(c))
                        {
                            if (strfun == "")
                            {
                                strOp_L = c;
                            }
                            break;
                        }
                        else if (c == "(")
                        {
                            break;
                        }
                        else if (c == " ")
                        {
                            if (strfun != "")
                            {
                                if (strfun.ToLower() == "mod")
                                {
                                    strfun = "";
                                    strOp_L = "mod";
                                }
                                break;
                            }
                        }
                        else
                        {
                            strfun = c + strfun;
                            if (strfun.ToLower() == ")mod")
                            {
                                strfun = "";
                                strOp_L = "mod";
                            }
                        }

                    }
                }



                strfun = strfun.ToLower().Trim();
                if (strfun == "")
                    n_ExpStart = n1;
                else
                {
                    //if (i == -1) i = 0;
                    n_ExpStart = i+1;
                    if (n_ExpStart == -1) n_ExpStart = 0;
                }
                sExpOrig = strExp.Substring(n_ExpStart, n2 - n_ExpStart + 1);// Mid(strExp, n_ExpStart, n2 - n_ExpStart + 1)

                string strOrig = ""; 
                object strOrigV; 
                string sLen=""; object vLen; object vStartPos;

                string ExpectedType;

                ExpectedType = "Variant";

                switch (strfun)
                {
                    case "":
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);// Mid(strExp, n1 + 1, n2 - n1 - 1)
                        v = getExpressionValue(bTest, strExp1, ref strErr, ref funcName);
                        if (strErr != "")
                            return "";

                        break;
                    case "len":
                        ExpectedType = "Number";
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);
                        GetParameters(strExp1, "", ref  strOrig);
                        if (bTest)
                        {
                            funcName = "Len";
                            //if( GuideForm Is Nothing Then
                            //    'GuideForm.AddParam "Function", funcName, "String", "Get the length of the text"
                            //    ' GuideForm.AddParam "VariableReturn", NameOfVarToSet, "String", "The variable to store the returned value of the function", , , True
                            //    GuideForm.SetParam("VariableReturn", "Length of the string", "Number", True)
                            //    GuideForm.AddParam("Text", strOrig, "String", "The text whose length will be got")
                            //End If
                        }
                        strOrigV = this.getExpressionValue(bTest, strOrig, ref strErr);
                        if (strErr != "") return null;
                        if (!bTest) v = strOrigV.ToString().Length;
                        break;
                    case "lcase":
                        ExpectedType = "String";
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);
                        GetParameters(strExp1, "", ref strOrig);
                        if (bTest)
                        {
                            funcName = "LCase";
                            //If Not GuideForm Is Nothing Then
                            //    'GuideForm.AddParam "Function", funcName, "String", "Get the length of the text"
                            //    ' GuideForm.AddParam "VariableReturn", NameOfVarToSet, "String", "The variable to store the returned value of the function", , , True
                            //    GuideForm.SetParam("VariableReturn", "Lower case of the string", "String", True)
                            //    GuideForm.AddParam("Text", strOrig, "String", "The orignal string")
                            //End If
                        }
                        strOrigV = this.getExpressionValue(bTest, strOrig, ref strErr);
                        if (strErr != "") return null;

                        if (!bTest) v = strOrigV.ToString().ToLower();
                        break;
                    case "ucase":
                        ExpectedType = "String";
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);
                        GetParameters(strExp1, "", ref  strOrig);
                        if (bTest)
                        {
                            funcName = "UCase";
                            //If Not GuideForm Is Nothing Then
                            //    'GuideForm.AddParam "Function", funcName, "String", "Get the length of the text"
                            //    ' GuideForm.AddParam "VariableReturn", NameOfVarToSet, "String", "The variable to store the returned value of the function", , , True
                            //    GuideForm.SetParam("VariableReturn", "Upper case of the string", "String", True)
                            //    GuideForm.AddParam("Text", strOrig, "String", "The orignal string")
                            //End If
                        }
                        strOrigV = this.getExpressionValue(bTest, strOrig, ref strErr);
                        if (strErr != "") return null;
                        if (!bTest) v = strOrigV.ToString().ToUpper();// UCase(strOrigV)
                        break;
                    case "trim":
                        ExpectedType = "String";
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);
                        GetParameters(strExp1, "", ref  strOrig);
                        if (bTest)
                        {
                            funcName = "Trim";
                            //If Not GuideForm Is Nothing Then
                            //    'GuideForm.AddParam "Function", funcName, "String", "Get the length of the text"
                            //    ' GuideForm.AddParam "VariableReturn", NameOfVarToSet, "String", "The variable to store the returned value of the function", , , True
                            //    GuideForm.SetParam("VariableReturn", "Trimed string", "String", True)
                            //    GuideForm.AddParam("Text", strOrig, "String", "The orignal string")
                            //End If
                        }
                        strOrigV = this.getExpressionValue(bTest, strOrig, ref strErr);
                        if (strErr != "") return null;
                        if (!bTest) v = strOrigV.ToString().Trim();// Trim(strOrigV)
                        break;
                    case "mid":
                        ExpectedType = "String";
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);
                        GetParameters(strExp1, "", ref  strOrig, ref  sStartPos, ref  sLen);
                        if (bTest)
                        {
                            funcName = "Mid";
                            //If Not GuideForm Is Nothing Then
                            //    'GuideForm.AddParam "Function", funcName, "String", "Get a sub string from the text"
                            //    GuideForm.SetParam("VariableReturn", "The sub string returned", "String", True)
                            //    GuideForm.AddParam("Text", strOrig, "String", "The string from which the sub string will be got")
                            //    GuideForm.AddParam("StartPos", sStartPos, "Number", "The position of the sub string in the whole string. The min value is 1.")
                            //    GuideForm.AddParam("Length", sLen, "Number", "The length of the sub string")

                            //End If
                        }
                        strOrigV = this.getExpressionValue(bTest, strOrig, ref strErr);
                        if (strErr != "") return null;
                        vStartPos = this.getExpressionValue(bTest, sStartPos, ref strErr);
                        if (strErr != "") return null;
                        if (!bTest)
                        {
                            if (!CPubVars.IsNumeric(vStartPos))
                            {
                                strErr = "Length parameter in Mid function should be a number";
                                return null;
                            }

                            int nStartPos = Convert.ToInt32(vStartPos);
                            int nLen;
                            nLen = strOrigV.ToString().Length;
                            if (nStartPos > nLen)
                            {
                                v = "";
                            }
                            else
                            {
                                vLen = this.getExpressionValue(bTest, sLen, ref strErr);
                                if (strErr != "") return null;
                                if (!CPubVars.IsNumeric(vLen))
                                {
                                    strErr = "Length parameter in Mid function should be a number";
                                    return null;
                                }
                                int nvLen = Convert.ToInt32(vLen);

                                nLen = nLen - nStartPos + 1;
                                if (nvLen > nLen) nvLen = nLen;
                                v = strOrigV.ToString().Substring(nStartPos, nvLen);
                            }
                        }
                        break;
                    //case "instr":
                    //    ExpectedType = "Number";
                    //    strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);

                    //    string sFind = "";
                    //    sFind = "";
                    //    strOrig = "";
                    //    sStartPos = "";

                    //    GetParameters(strExp1, "", ref  sStartPos, ref  strOrig, ref  sFind);

                    //    if (sFind == "")
                    //    {
                    //        sFind = strOrig;
                    //        strOrig = sStartPos;
                    //        sStartPos = "";
                    //    }

                    //    if (bTest)
                    //    {
                    //        funcName = "InStr";
                    //        //If Not GuideForm Is Nothing Then
                    //        //    'GuideForm.AddParam "Function", funcName, "String", "Get the position of sub string in the text. If not found, it return 0."
                    //        //    GuideForm.SetParam("VariableReturn", "The position of sub string in the string", "Number", True)
                    //        //    GuideForm.AddParam("StartPos", sStartPos, "Number", "The position from which start to find the sub string. The min value is 1.")
                    //        //    GuideForm.AddParam("Text", strOrig, "String", "The string in which the sub string will be found")
                    //        //    GuideForm.AddParam("SubString", sFind, "String", "The sub string to find")

                    //        //End If
                    //    }

                    //    if (sStartPos != "")
                    //    {
                    //        sStartPos = this.getExpressionValue(bTest, sStartPos, ref strErr);
                    //        if (strErr != "") return null;
                    //    }
                    //    strOrig = this.getExpressionValue(bTest, strOrig, ref strErr);
                    //    if (strErr != "") return null;


                    //    sFind = this.getExpressionValue(bTest, sFind, ref strErr);
                    //    if (strErr != "") return null;
                    //    if (!bTest)
                    //    {
                    //        if (CPubVars.IsNumeric(sStartPos))
                    //            v = InStr(CLng(sStartPos), strOrig, sFind);
                    //        else
                    //            v = InStr(strOrig, sFind);


                    //    }
                    //    break;
                    case "left":
                        ExpectedType = "String";
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);
                        GetParameters(strExp1, "", ref  strOrig, ref  sLen);
                        if (bTest)
                        {
                            funcName = "Left";
                            //If Not GuideForm Is Nothing Then
                            //    'GuideForm.AddParam "Function", funcName, "String", "Get a sub string from the left side of the string"
                            //    GuideForm.SetParam("VariableReturn", "The sub string from the left side of the string", "String", True)
                            //    GuideForm.AddParam("Text", strOrig, "String", "The string whose left part will be got")
                            //    GuideForm.AddParam("Length", sLen, "Number", "The length of the left part")
                            //End If
                        }
                        strOrigV = this.getExpressionValue(bTest, strOrig, ref strErr);
                        if (strErr != "") return null;
                        vLen = this.getExpressionValue(bTest, sLen, ref strErr);
                        if (!CPubVars.IsNumeric(vLen))
                        {
                            strErr = "Length parameter in Left function should be a number";
                            return null;
                        }
                        int n_vLen = Convert.ToInt32(vLen);
                        if (n_vLen > strOrigV.ToString().Length)
                            n_vLen = strOrigV.ToString().Length;
                        if (n_vLen < 0) n_vLen = 0;
                        if (!bTest) v = strOrigV.ToString().Substring(0, n_vLen);// Left(strOrigV, vLen);
                        break;
                    case "right":
                        ExpectedType = "String";
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);
                        GetParameters(strExp1, "", ref  strOrig, ref  sLen);
                        if (bTest)
                        {
                            funcName = "Right";
                            //If Not GuideForm Is Nothing Then
                            //    'GuideForm.AddParam "Function", funcName, "String", "Get a sub string from the right side of the string"
                            //    GuideForm.SetParam("VariableReturn", "The sub string from the right side of the string", "String", True)
                            //    GuideForm.AddParam("Text", strOrig, "String", "The string whose right part will be got")
                            //    GuideForm.AddParam("Length", sLen, "Number", "The length of the right part")
                            //End If
                        }
                        strOrigV = this.getExpressionValue(bTest, strOrig, ref strErr);
                        if (strErr != "") return null;
                        vLen = this.getExpressionValue(bTest, sLen, ref strErr);
                        if (strErr != "") return null;
                        if (!CPubVars.IsNumeric(vLen))
                        {
                            strErr = "Length parameter in Left function should be a number";
                            return null;
                        }
                        n_vLen = Convert.ToInt32(vLen);
                        if (n_vLen > strOrigV.ToString().Length) n_vLen = strOrigV.ToString().Length;
                        if (!bTest) v = "";//strOrigV.ToString().Substring(strOrigV  Right(strOrigV, vLen)
                        break;
                   case "acct":
                        ExpectedType = "Number";
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);
                        string sub_code = ""; string data_item = ""; string currency_id = ""; string acc_year = ""; string acc_start_month = ""; string acc_end_month = ""; string acc_db_name = "";

                        GetParameters(strExp1, "", ref sub_code, ref data_item, ref currency_id, ref acc_year, ref acc_start_month, ref acc_end_month, ref acc_db_name);
                        
                        if (bTest)
                        {
                            funcName = "acct";
                            //If Not GuideForm Is Nothing Then
                            //    'GuideForm.AddParam "Function", funcName, "String", "Get a sub string from the right side of the string"
                            //    GuideForm.SetParam("VariableReturn", "The sub string from the right side of the string", "String", True)
                            //    GuideForm.AddParam("Text", strOrig, "String", "The string whose right part will be got")
                            //    GuideForm.AddParam("Length", sLen, "Number", "The length of the right part")
                            //End If
                            
                           // m_lstCalledFun.Add( 
                        }



                        object ov = null;
                        ov = this.getExpressionValue(bTest, sub_code, ref strErr);
                        if (strErr != "") return null;
                        if (ov != null) sub_code = ov.ToString();
                        

                        ov = this.getExpressionValue(bTest, data_item, ref strErr);
                        if (strErr != "") return null;
                        if (ov != null) data_item = ov.ToString();
                        

                        ov = this.getExpressionValue(bTest, currency_id, ref strErr);
                        if (strErr != "") return null;
                        if (ov != null) currency_id = ov.ToString();
                        if (currency_id == "") currency_id = "0";
                         
                        ov = this.getExpressionValue(bTest, acc_year, ref strErr);
                        if (strErr != "") return null;
                        if (ov != null) acc_year = ov.ToString();
                        if (acc_year == "") acc_year = "0";

                        ov = this.getExpressionValue(bTest, acc_start_month, ref strErr);
                        if (strErr != "") return null;
                        if (ov != null) acc_start_month = ov.ToString();
                        if (acc_start_month == "") acc_start_month = "0";

                        ov = this.getExpressionValue(bTest, acc_end_month, ref strErr);
                        if (strErr != "") return null;
                        if (ov != null) acc_end_month = ov.ToString();
                        if (acc_end_month == "") acc_end_month = "0";

                        ov = this.getExpressionValue(bTest, acc_db_name, ref strErr);
                        if (strErr != "") return null;
                        if (ov != null) acc_db_name = ov.ToString();
                        if (acc_db_name == "") acc_db_name = "";
                        if (!bTest)
                        {
                            CFunction fun = new CFunction();
                            fun.fun_name = "acct"; fun.param1 = sub_code; fun.param2 = data_item; fun.param3 = currency_id;
                            fun.param4 = acc_year; fun.param5 = acc_start_month; fun.param6 = acc_end_month; fun.param7 = acc_db_name;
                            string key = fun.fun_name + "_" + fun.param1 + "_" + fun.param2 + "_" + fun.param3 + "_" + fun.param4 + "_" + fun.param5 + "_" + fun.param6 + "_" + fun.param7;

                            if (m_bCollectingFunVar)
                            { 
                                if (!m_dicCalledFun.ContainsKey(key))
                                {
                                    m_dicCalledFun.Add(key, fun);
                                }
                                v = 0;
                            }
                            else
                            {
                                if (m_dicCalledFun.ContainsKey(key))
                                {
                                    v = m_dicCalledFun[key].fun_value;
                                }
                                else
                                    v = 0;
                            }
                        }        
                        break;
                    case "list":
                        ExpectedType = "String";
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);
                        string list_by = ""; string list_item = "";string list_by_item="";

                        GetParameters(strExp1, "", ref list_by, ref list_item, ref list_by_item);

                        if (bTest)
                        {
                            funcName = "list";
                            //If Not GuideForm Is Nothing Then
                            //    'GuideForm.AddParam "Function", funcName, "String", "Get a sub string from the right side of the string"
                            //    GuideForm.SetParam("VariableReturn", "The sub string from the right side of the string", "String", True)
                            //    GuideForm.AddParam("Text", strOrig, "String", "The string whose right part will be got")
                            //    GuideForm.AddParam("Length", sLen, "Number", "The length of the right part")
                            //End If

                            // m_lstCalledFun.Add( 
                        }



                        ov = null;
                        ov = this.getExpressionValue(bTest, list_by, ref strErr);
                        if (strErr != "") return null;
                        if (ov != null) list_by = ov.ToString();


                        ov = this.getExpressionValue(bTest, list_item, ref strErr);
                        if (strErr != "") return null;
                        if (ov != null) list_item = ov.ToString();
                         
                        
                        if (!bTest)
                        {
                            CFunction fun = new CFunction();
                            fun.fun_name = "list"; fun.param1 = list_by; fun.param2 = list_item; fun.param3 = list_by_item;
                            string key = fun.fun_name + "_" + fun.param1 + "_" + fun.param2;
                            if (m_bCollectingFunVar)
                            {
                                if (!m_dicCalledFun.ContainsKey(key))
                                {
                                    m_dicCalledFun.Add(key, fun);
                                }
                                v = 0;
                            }
                            else
                            {
                                if (m_dicCalledFun.ContainsKey(key))
                                {
                                    v = m_dicCalledFun[key].fun_value;
                                }
                                else
                                    v = 0;
                            }
                        }
                        break;
                    case "table":
                        ExpectedType = "String";
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);
                        //string list_by = ""; string list_item = "";

                        string Param1 = ""; string Param2=""; string Param3=""; string Param4=""; string Param5="";string Param6=""; string Param7=""; string Param8=""; 
                        GetParameters(strExp1, "", ref Param1 , ref Param2,ref Param3 ,ref Param4 ,ref Param5 ,ref Param6 ,ref Param7,ref Param8);

                        if (bTest)
                        {
                            funcName = "table";
                        } 

                        if (!bTest)
                        {
                            CFunction fun = new CFunction();
                            fun.fun_name = "table"; fun.param1 = Param1; fun.param2 = Param2; fun.param3 = Param3; fun.param4 = Param4; fun.param5 = Param5; fun.param6 = Param6; fun.param7 = Param7; fun.param8 = Param8;

                            string key = fun.fun_name + "_" + fun.param1 + "_" + fun.param2 + "_" + fun.param3 + "_" + fun.param4 + "_" + fun.param5 + "_" + fun.param6 + "_" + fun.param7 + "_" + fun.param8;
                            if (m_bCollectingFunVar)
                            {
                                if (!m_dicCalledFun.ContainsKey(key))
                                {
                                    m_dicCalledFun.Add(key, fun);
                                }
                                v = 0;
                            }
                            else
                            {
                                if (m_dicCalledFun.ContainsKey(key))
                                {
                                    v = m_dicCalledFun[key].fun_value;
                                }
                                else
                                    v = 0;
                            }
                        }
                        break; 
                    case "sum":
                        ExpectedType = "Number";
                        strExp1 = strExp.Substring(n1 + 1, n2 - n1 - 1);
                        string cell1 = ""; string cell2 = ""; string cell3 = ""; string cell4 = ""; string cell5 = ""; string cell6 = ""; string cell7 = ""; string cell8 = "";

                        GetParameters(strExp1, "", ref cell1, ref cell2, ref cell3, ref cell4, ref cell5, ref cell6, ref cell7,ref cell8);
                    
                        if (bTest)
                        {
                            funcName = "acct";
                            //If Not GuideForm Is Nothing Then
                            //    'GuideForm.AddParam "Function", funcName, "String", "Get a sub string from the right side of the string"
                            //    GuideForm.SetParam("VariableReturn", "The sub string from the right side of the string", "String", True)
                            //    GuideForm.AddParam("Text", strOrig, "String", "The string whose right part will be got")
                            //    GuideForm.AddParam("Length", sLen, "Number", "The length of the right part")
                            //End If

                            // m_lstCalledFun.Add( 
                        } 
                    
                        break;
                    //Case "replace"
                    //    ExpectedType = "String"
                    //     strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);

                    //    Dim n_p As Long, n_Pre As Long, n As Long, sCur As String
                    //    GetParameters(strExp1, "", strOrig, strFind, strReplace)
                    //    If bTest Then
                    //        funcName = "Replace"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Replace a part of the string. Return the changed string."
                    //            GuideForm.SetParam("VariableReturn", "The replaced string", "String", True)
                    //            GuideForm.AddParam("Text", strOrig, "String", "The string in which some sub string will be will be replaced")
                    //            GuideForm.AddParam("SubString", strFind, "String", "The sub string to be replaced")
                    //            GuideForm.AddParam("ReplaceString", strReplace, "String", "The string to replace the sub string")
                    //        End If
                    //    End If
                    //    strOrigV =this.getExpressionValue(runLine, bTest, strOrig, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    strFindV =this.getExpressionValue(runLine, bTest, strFind, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    strReplaceV =this.getExpressionValue(runLine, bTest, strReplace, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    If Not bTest Then v = Replace(strOrigV, strFindV, strReplaceV)
                    //Case "string"
                    //    ExpectedType = "String"
                    //    strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);

                    //    Dim strCount As String, strCharactor As String, vCharactor As Object
                    //    '  Dim n_p As Long, n_Pre As Long, n As Long, sCur As String
                    //    GetParameters(strExp1, "", strCount, strCharactor)
                    //    If bTest Then
                    //        funcName = "String"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get a string formed by certain number of same charactor"
                    //            GuideForm.SetParam("VariableReturn", "The formed string", "String", True)
                    //            GuideForm.AddParam("Count", strCount, "Number", "Count of charactor")
                    //            GuideForm.AddParam("Charactor", strCharactor, "String", "The repeated charactor which will form the whole string")
                    //        End If
                    //    End If
                    //    strCount =this.getExpressionValue(runLine, bTest, strCount, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    strCharactor =this.getExpressionValue(runLine, bTest, strCharactor, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    If Not bTest Then
                    //        If Not IsNumeric(strCount) Then
                    //            strErr = "First parameter of String function should be a number"
                    //            Exit Function
                    //        End If
                    //          v = String(CLng(strCount), strCharactor)
                    //    End If

                    //Case "GetItemsCountFromString"

                    //    ExpectedType = "Number"
                    //    strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    Dim sSeperator As String
                    //    GetParameters(strExp1, "", strOrig, sSeperator)
                    //    If bTest Then
                    //        funcName = "GetItemsCountFromString"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get a sub string from the text"
                    //            GuideForm.SetParam("VariableReturn", "The count of items seperated by a specified charactor in the string", "String", True)
                    //            GuideForm.AddParam("Text", strOrig, "String", "The string containing the seperated items")
                    //            GuideForm.AddParam("Seperator", sSeperator, "String", "The charator used to seperate the items in the string")

                    //        End If
                    //    End If
                    //    If strOrigV = "" Then
                    //        strErr = "The Text parameter should be specified"
                    //        Exit Function
                    //    End If
                    //    strOrigV =this.getExpressionValue(runLine, bTest, strOrig, strErr)
                    //    If strErr <> "" Then Exit Function

                    //    If sSeperator = "" Then
                    //        strErr = "The Seperator parameter should be specified"
                    //        Exit Function
                    //    End If

                    //    sSeperator =this.getExpressionValue(runLine, bTest, sSeperator, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    If Not bTest Then

                    //        If sSeperator = "" Then
                    //            strErr = "The seperator can not be empty string"
                    //        End If

                    //        Dim sCurC As String, nPreC As Long, nCurC As Long, bExit As Boolean, nItemCount As Long
                    //        Dim nSepLen As Integer
                    //        nSepLen = Len(sSeperator)
                    //        If Right(strOrigV, nSepLen) = sSeperator Then
                    //            strOrigV = Left(strOrigV, Len(strOrigV) - nSepLen)
                    //        End If

                    //        nPreC = 0
                    //        bExit = False
                    //        nItemCount = 0
                    //        Do
                    //            nCurC = InStr(nPreC + 1, strOrigV, sSeperator)
                    //            If nCurC = 0 Then
                    //                bExit = True
                    //                '  nCurC = Len(strOrigV) + 1
                    //            End If
                    //            nItemCount = nItemCount + 1
                    //            'sCurC = Mid(strOrigV, nPreC + 1, nCurC - nPreC - 1)
                    //            If bExit Then
                    //                Exit Do
                    //            End If


                    //            nPreC = n

                    //            '*********Prevent DeadLoop*******************************
                    //            Dim nMyLoopCount As Long
                    //            nMyLoopCount = nMyLoopCount + 1
                    //            If nMyLoopCount > 2000 Then
                    //                WriteLog("DeadLoop in GetExpressionValue A", True)
                    //                Exit Do
                    //            End If
                    //            '*********************************************************
                    //        Loop
                    //        v = nItemCount
                    //    End If

                    //Case "clng", "cint"
                    //    ExpectedType = "Number"
                    //    strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    Dim strNum As String
                    //    GetParameters(strExp1, "", strNum)
                    //    If bTest Then
                    //        funcName = "CLng"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Change the data type from string to long number"
                    //            GuideForm.SetParam("VariableReturn", "The converted number", "Number", True)
                    //            GuideForm.AddParam("strNumber", strNum, "Number", "Any number or string to be converted")
                    //        End If
                    //    End If
                    //    If strNum = "" Then
                    //        strErr = "Parameter not specified in CLng function"
                    //        Exit Function
                    //    End If
                    //    strNum =this.getExpressionValue(runLine, bTest, strNum, strErr)
                    //    If strErr <> "" Then
                    //        Exit Function
                    //    End If
                    //    If Not bTest Then

                    //        If Not IsNumeric(strNum) Then
                    //            strErr = "The parameter is not numeric"
                    //            Exit Function
                    //        End If
                    //        v = CLng(strNum)
                    //    End If

                    //Case "year"

                    //    ExpectedType = "Number"
                    //    Dim strDate As String
                    //    strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    GetParameters(strExp1, "", strDate)
                    //    If bTest Then
                    //        funcName = "Year"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get the year from a date"
                    //            GuideForm.SetParam("VariableReturn", "The year returned", "Number", True)
                    //            GuideForm.AddParam("Date", strDate, "Date", "")
                    //        End If
                    //    End If
                    //    strDate =this.getExpressionValue(runLine, bTest, strDate, strErr)
                    //    If Not bTest Then
                    //        If Not IsDate(strDate) Then
                    //            strErr = "Value in Year function is not of date type"
                    //            v = 0
                    //            Exit Function
                    //        Else
                    //            v = Year(strDate)
                    //        End If
                    //    End If

                    //Case "month"
                    //    ExpectedType = "Number"
                    //    strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    GetParameters(strExp1, "", strDate)
                    //    If bTest Then
                    //        funcName = "Month"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get the month from a date"
                    //            GuideForm.SetParam("VariableReturn", "The month returned", "Number", True)
                    //            GuideForm.AddParam("Date", strDate, "Date", "")
                    //        End If
                    //    End If
                    //    strDate =this.getExpressionValue(runLine, bTest, strDate, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    If Not bTest Then
                    //        If Not IsDate(strDate) Then
                    //            strErr = "Value in Year function is not of date type"
                    //            v = 0
                    //            Exit Function
                    //        Else
                    //            v = Month(strDate)
                    //        End If
                    //    End If

                    //Case "day"
                    //    ExpectedType = "Number"
                    //     strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    GetParameters(strExp1, "", strDate)
                    //    If bTest Then
                    //        funcName = "Day"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get the day of a month from a date"
                    //            GuideForm.SetParam("VariableReturn", "The day returned", "Number", True)
                    //            GuideForm.AddParam("Date", strDate, "Date", "")
                    //        End If
                    //    End If
                    //    strDate =this.getExpressionValue(runLine, bTest, strDate, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    If Not bTest Then
                    //        If Not IsDate(strDate) Then
                    //            strErr = "Value in Year function is not of date type"
                    //            v = 0
                    //            Exit Function
                    //        Else
                    //            v = Day(strDate)
                    //        End If
                    //    End If

                    //Case "hour"
                    //    ExpectedType = "Number"
                    //    strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    GetParameters(strExp1, "", strDate)
                    //    If bTest Then
                    //        funcName = "Hour"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get the hour from a date"
                    //            GuideForm.SetParam("VariableReturn", "The hour returned", "Number", True)
                    //            GuideForm.AddParam("Date", strDate, "Date", "")
                    //        End If
                    //    End If
                    //    strDate =this.getExpressionValue(runLine, bTest, strDate, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    If Not bTest Then
                    //        If Not IsDate(strDate) Then
                    //            strErr = "Value in Year function is not of date type"
                    //            v = 0
                    //            Exit Function
                    //        Else
                    //            v = Hour(strDate)
                    //        End If
                    //    End If

                    //Case "minute"
                    //    ExpectedType = "Number"
                    //     strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    GetParameters(strExp1, "", strDate)
                    //    If bTest Then
                    //        funcName = "Minute"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get the minute from a date"
                    //            GuideForm.SetParam("VariableReturn", "The minute returned", "Number", True)
                    //            GuideForm.AddParam("Date", strDate, "Date", "")
                    //        End If
                    //    End If
                    //    strDate =this.getExpressionValue(runLine, bTest, strDate, strErr)
                    //    If Not bTest Then
                    //        If Not IsDate(strDate) Then
                    //            strErr = "Value in Year function is not of date type"
                    //            v = 0
                    //            Exit Function
                    //        Else
                    //            v = Minute(strDate)
                    //        End If
                    //    End If

                    //Case "second"
                    //    ExpectedType = "Number"
                    //     strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    GetParameters(strExp1, "", strDate)
                    //    If bTest Then
                    //        funcName = "Second"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get the second from a date"
                    //            GuideForm.SetParam("VariableReturn", "The second returned", "Number", True)
                    //            GuideForm.AddParam("Date", strDate, "Date", "")
                    //        End If
                    //    End If
                    //    strDate =this.getExpressionValue(runLine, bTest, strDate, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    If Not bTest Then
                    //        If Not IsDate(strDate) Then
                    //            strErr = "Value in Year function is not of date type"
                    //            v = 0
                    //            Exit Function
                    //        Else
                    //            v = Second(strDate)
                    //        End If
                    //    End If

                    //Case "weekday"
                    //    ExpectedType = "Number"
                    //    strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    Dim sFirstDay As String
                    //    GetParameters(strExp1, "", strDate, sFirstDay)
                    //    If bTest Then
                    //        funcName = "WeekDay"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get the day of week from a date"
                    //            GuideForm.SetParam("VariableReturn", "The week day returned", "Number", True)
                    //            GuideForm.AddParam("Date", strDate, "Date", "")
                    //            GuideForm.AddParam("FirstDay", sFirstDay, "Number", "The first day of the week. Range from 0 to 7")

                    //        End If
                    //    End If
                    //    If Not bTest Then
                    //        strDate =this.getExpressionValue(runLine, bTest, strDate, strErr)
                    //        If strErr <> "" Then Exit Function
                    //        If Not IsDate(strDate) Then
                    //            strErr = "First parameter of weekday function should be a date"
                    //            Exit Function
                    //        End If
                    //        sFirstDay = Trim(Me.getExpressionValue(runLine, bTest, sFirstDay, strErr))
                    //        If strErr <> "" Then Exit Function
                    //        If sFirstDay = "" Then
                    //            sFirstDay = "0"
                    //        ElseIf Not IsNumeric(sFirstDay) Then
                    //            strErr = "Second parameter of weekday function should be a number between 0 and 7"
                    //            Exit Function
                    //        End If
                    //        If CLng(sFirstDay) < 0 Or CLng(sFirstDay) > 7 Then
                    //            strErr = "First parameter of weekday function should be a date"
                    //            Exit Function
                    //        End If
                    //        v = Weekday(strDate, sFirstDay)
                    //    End If
                    //Case "datediff"
                    //    ExpectedType = "Number"
                    //    strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    Dim sInterval As String, dt1 As String, dt2 As String
                    //    GetParameters(strExp1, "", sInterval, dt1, dt2)
                    //    If bTest Then
                    //        funcName = "DateDiff"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get the difference value of two date according to specified interval"
                    //            GuideForm.SetParam("VariableReturn", "The difference value of two date", "Number", True)
                    //            GuideForm.AddParam("Interval", sInterval, "String", "The interval value can be m(month),y(year),d(day),w(week),h(hour),n(minute),s(second)")
                    //            GuideForm.AddParam("Date1", dt1, "Date", "")
                    //            GuideForm.AddParam("Date2", dt2, "Date", "")

                    //        End If
                    //    End If
                    //    sInterval =this.getExpressionValue(runLine, bTest, sInterval, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    dt1 =this.getExpressionValue(runLine, bTest, dt1, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    dt2 =this.getExpressionValue(runLine, bTest, dt2, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    If Not bTest Then
                    //        Select Case sInterval
                    //            Case "yyyy", "q", "m", "y", "d", "w", "ww", "h", "n", "s"
                    //            Case Else
                    //                strErr = "Invalid interval parameter in DateDiff function"
                    //                Exit Function
                    //        End Select
                    //        If Not IsDate(dt1) Or Not IsDate(dt2) Then
                    //            strErr = "Value in dateDiff function is not of date type"
                    //            Exit Function
                    //        End If
                    //        v = DateDiff(sInterval, dt1, dt2)
                    //    End If
                    //Case "dateadd"
                    //    ExpectedType = "Date"
                    //    strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    Dim dtAdd As String
                    //    GetParameters(strExp1, "", sInterval, dtAdd, dt1)
                    //    If bTest Then
                    //        funcName = "DateAdd"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Change a date according to specified interval"
                    //            GuideForm.SetParam("VariableReturn", "The new date returned", "Date", True)
                    //            GuideForm.AddParam("Interval", sInterval, "String", "The interval value can be m(month),y(year),d(day),w(week),h(hour),n(minute),s(second)")
                    //            GuideForm.AddParam("AddValue", dtAdd, "Number", "The value to add to the date")
                    //            GuideForm.AddParam("Date", dt1, "Date", "The date to be changed")

                    //        End If
                    //    End If
                    //    sInterval =this.getExpressionValue(runLine, bTest, sInterval, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    dt1 =this.getExpressionValue(runLine, bTest, dt1, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    dtAdd =this.getExpressionValue(runLine, bTest, dtAdd, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    If Not bTest Then
                    //        Select Case sInterval
                    //            Case "yyyy", "q", "m", "y", "d", "w", "ww", "h", "n", "s"
                    //            Case Else
                    //                strErr = "Invalid interval parameter in DateAdd function"
                    //                Exit Function
                    //        End Select
                    //        If Not IsNumeric(dtAdd) Then
                    //            strErr = "Add value in DateAdd function should be a number"
                    //            Exit Function
                    //        End If

                    //        If Not IsDate(dt1) Then
                    //            strErr = "Value in DateAdd function is not of date type"
                    //            Exit Function
                    //        End If

                    //        v = DateAdd(sInterval, dtAdd, dt1)
                    //    End If
                    //Case "isdate"
                    //    ExpectedType = "Boolean"
                    //     strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    GetParameters(strExp1, "", strDate)
                    //    If bTest Then
                    //        funcName = "IsDate"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get the second from a date"
                    //            GuideForm.SetParam("VariableReturn", "If it is a date, return True", "Boolean", True)
                    //            GuideForm.AddParam("strDate", strDate, "String", "The text to judge if it is of date")
                    //        End If
                    //    End If
                    //    strDate =this.getExpressionValue(runLine, bTest, strDate, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    If Not bTest Then
                    //        v = IsDate(strDate)
                    //    End If
                    //Case "isnumeric"
                    //    ExpectedType = "Boolean"
                    //    strExp1 =strExp.Substring(n1+1, n2 - n1 - 1);
                    //    GetParameters(strExp1, "", strDate)
                    //    If bTest Then
                    //        funcName = "IsNumeric"
                    //        If Not GuideForm Is Nothing Then
                    //            'GuideForm.AddParam "Function", funcName, "String", "Get the second from a date"
                    //            GuideForm.SetParam("VariableReturn", "If it is numeric, return True", "Boolean", True)
                    //            GuideForm.AddParam("strNumber", strDate, "String", "The text to judge if it is numeric")
                    //        End If
                    //    End If
                    //    strDate =this.getExpressionValue(runLine, bTest, strDate, strErr)
                    //    If strErr <> "" Then Exit Function
                    //    If Not bTest Then
                    //        v = IsNumeric(strDate)
                    //    End If 

                    default:
                        strErr = "Unknown Function:" + strfun;
                        return null;
                       
                }
                if (!CPubVars.IsNumeric(v) || ExpectedType == "String")
                {
                    if (v == null) v = "";
                    v = "\"" + v.ToString().Trim() + "\"";
                }
                strExp = strExp.Replace(sExpOrig, " " + v.ToString() + " ");

                v = getExpressionValue(bTest, strExp, ref strErr, ref funcName);
                if (!bTest) return v;
                return null;

            }

            //finish testing


            object Ai=null; bool flgMulDiv=false; int expLen; object curV; string preMulDivOperater=""; string preAddSubOperater="";
            expLen = strExp.Length;
            n1 = -1;
            preAddSubOperater = strExp.Substring(0, 1);
            if (preAddSubOperater != "-")
                preAddSubOperater = "+";

            bInYinHao = false;
            string sCurItem;
            for (i = 0; i < expLen; i++)
            {
                c = strExp.Substring(i, 1);//  Mid(strExp, i, 1)
                if (c == " ")
                {
                    if (i + 5 < expLen)
                    {
                        c = strExp.Substring(i, 5);
                        c = c.ToLower();
                        if (c == " mod ")
                        {
                            c = c.Trim();
                            i = i + 4;
                        }
                    }
                }

                if (c == "\"")
                    bInYinHao = !bInYinHao;


                if (!bInYinHao)
                {

                    if (c == "*" || c == "/" || c == "\\" || c == "mod")
                    {
                        flgMulDiv = true;
                        if (c == "mod")
                        {
                            sCurItem = strExp.Substring(n1 + 1, i - 4 - n1).Trim();// Trim(Mid(strExp, n1 + 1, i - 4 - n1))
                        }
                        else
                        {
                            sCurItem = strExp.Substring(n1 + 1, i - 1 - n1).Trim(); //Trim(Mid(strExp, n1 + 1, i - 1 - n1))
                        }


                        n1 = i;
                        if (!bTest)
                        {
                            curV = GetValue(sCurItem, strErr);
                            if (strErr != "") return null;
                            if (!CPubVars.IsNumeric(curV))
                            {
                                strErr = "Operator *, / ,\\, mod - should be used to operator number";
                                return null;
                            }
                            // If curV = 0 Then curV = 1 'prevent the denominater from being 0 when testing

                            if (!(Ai == null))//  Not IsEmpty(Ai) Then
                            {
                                switch (preMulDivOperater)
                                {
                                    case "/":
                                    case "\\":
                                    case "mod":
                                        if (Convert.ToDouble(curV) == 0)
                                        {
                                            strErr = "Attempt to divide a number by 0";
                                            return null;
                                        }
                                        break;
                                }
                                switch (preMulDivOperater)
                                {
                                    case "*":
                                        Ai =Convert.ToDouble(Ai) * Convert.ToDouble(curV); break;
                                    case "/":
                                        Ai = Convert.ToDouble(Ai) / Convert.ToDouble(curV); break;
                                    case "\\":
                                        // Ai = Ai \ curV;
                                        break;
                                    case "mod":
                                        //  Ai = Ai Mod curV;
                                        break;
                                }
                            }
                            else
                            {
                                Ai = curV;
                            }

                        }
                        preMulDivOperater = c;
                    }
                    else if (c == "+" || c == "-" || c == "&" || i == expLen-1)
                    {
                        if (i == expLen-1)
                        {
                            sCurItem = strExp.Substring(n1 + 1, i - n1); //sCurItem = Trim(Mid(strExp, n1 + 1, i - n1));
                            curV = GetValue(sCurItem, strErr);
                        }
                        else if (i == 0)
                            curV = 0;
                        else
                        {
                            sCurItem = strExp.Substring(n1 + 1, i - 1 - n1).Trim();//   Trim(Mid(strExp, n1 + 1, i - 1 - n1))
                            curV = GetValue(sCurItem, strErr);

                        }
                        if (strErr != "") return null;
                        n1 = i;
                        if (!bTest)
                        {
                            if (flgMulDiv)
                            {
                                if (!(Ai == null))// IsEmpty(Ai) Then
                                {
                                    if (Convert.ToDouble(curV) == 0 && preMulDivOperater == "/")
                                    {
                                        strErr = "Attempt to divide a number by 0";
                                        return null;
                                    }
                                    switch (preMulDivOperater)
                                    {
                                        case "*":
                                            Ai = Convert.ToDouble(Ai) * Convert.ToDouble(curV); break;
                                        case "/":
                                            Ai = Convert.ToDouble(Ai) / Convert.ToDouble(curV); break;
                                        case "\\":
                                            //Ai = Ai \ curV;
                                            break;
                                        case "mod":
                                            //Ai = Ai Mod curV;
                                            break;
                                    }
                                }
                                else
                                {
                                    Ai = curV;
                                }
                            }
                            else
                            {
                                Ai = curV;
                            }

                            bool bBothNumber; bool bIsNumS; bool bIsNumAi;
                            bIsNumS = false;
                            bIsNumAi = false;
                            bIsNumS = CPubVars.IsNumeric(s);// IsEmpty(s) And IsNumeric(s)

                            bIsNumAi = CPubVars.IsNumeric(Ai);// IsNumeric(Ai)

                            if (bIsNumS && bIsNumAi)
                            {
                                bBothNumber = true;
                            }
                            else
                            {
                                // s = CStr(s)
                                if (Ai == null)
                                    Ai = "";
                                else
                                    Ai = Ai.ToString();

                                bBothNumber = false;

                            }


                            if (preAddSubOperater == "+")
                            {
                                if (bBothNumber)
                                {
                                  double ds = Convert.ToDouble(s);// CDbl(s)
                                   double dAi = Convert.ToDouble(Ai);
                                   s = ds + dAi;
                                }
                                else if (s == null)
                                    s = Ai;
                                else
                                    s = s.ToString() + Ai.ToString();
                            }
                            else if (preAddSubOperater == "&")
                            {
                                if (s == null)// IsEmpty(s) Then
                                    s = Ai;
                                else
                                    s = s.ToString() + Ai.ToString();// CStr(s) & CStr(Ai)
                            }
                            else
                            {
                                if (!bBothNumber)
                                {
                                    if ( s == null || s.ToString()=="")// IsEmpty(s) Then
                                    {
                                        try
                                        {
                                            s = -Convert.ToDouble(Ai);
                                        }
                                        catch (Exception)
                                        {
                                        }
                                    }
                                    else
                                    {
                                        strErr = "- operator can only be used to operate number";
                                        return null;
                                    }
                                }
                                else
                                    s =Convert.ToDouble(s) - Convert.ToDouble(Ai);

                            }
                            Ai = null; //Ai = Empty

                        } //'If Not bTest Then
                        flgMulDiv = false;
                        preAddSubOperater = c;
                    }//End If ' ElseIf c = "+" Or c = "-" Or i = expLen Then
                }//End If 'If Not bInYinHao Then
            }


            if (s != null)// If Not IsNull(s) Then
            {
                //if(c.ToString().Contains("{"))
                //{ 
                //     s = runLine.GetDestText(CStr(s), True);
                //}
                //If InStr(CStr(s), "<L") > 0 Then
                //    s = runLine.MotherMission.GetLanlibText(runLine, CStr(s), "L")
                //    s = runLine.MotherMission.GetLanlibText(runLine, CStr(s), "LM")
                //End If
                return s;
            }
            //ErrDeal:
            //        If err.Number <> 0 Then
            //            strErr = err.Description
            //        End If
            return null;
        }

        public void GetParameters(string sExpress, string SepStart)
        {
            string param5 = ""; string param4 = ""; string param3 = ""; string param2 = ""; string Param1 = "";
            GetParameters(sExpress, SepStart, ref Param1, ref param2, ref param3, ref param4, ref param5);

        }
        public void GetParameters(string sExpress, string SepStart, ref string Param1)
        {
            string param5 = ""; string param4 = ""; string param3 = ""; string param2 = "";
            GetParameters(sExpress, SepStart, ref Param1, ref param2, ref param3, ref param4, ref param5);

        }
        public void GetParameters(string sExpress, string SepStart, ref string Param1, ref string Param2)
        {
            string param5 = ""; string param4 = ""; string param3 = "";
            GetParameters(sExpress, SepStart, ref Param1, ref Param2, ref param3, ref param4, ref param5);

        }
        public void GetParameters(string sExpress, string SepStart, ref string Param1, ref string Param2, ref string param3)
        {
            string param5 = ""; string param4 = "";

            GetParameters(sExpress, SepStart, ref Param1, ref Param2, ref param3, ref param4, ref param5);

        }
        public void GetParameters(string sExpress, string SepStart, ref string Param1, ref string Param2, ref string param3, ref string param4)
        {
            string param5 = "";

            GetParameters(sExpress, SepStart, ref Param1, ref Param2, ref param3, ref param4, ref param5);

        }
        public void GetParameters(string sExpress, string SepStart, ref string Param1, ref string Param2, ref string param3, ref string param4, ref string param5)
        {
            string param6 = ""; string param7 = ""; string param8 = "";

            GetParameters(sExpress, SepStart, ref Param1, ref Param2, ref param3, ref param4, ref param5, ref param6, ref param7, ref param8);
        }
        public void GetParameters(string sExpress, string SepStart, ref string Param1, ref string Param2, ref string param3, ref string param4, ref string param5, ref string param6)
        {
            string param7 = ""; string param8 = ""; 

            GetParameters(sExpress, SepStart, ref Param1, ref Param2, ref param3, ref param4, ref param5, ref param6, ref param7, ref param8);
        }
        public void GetParameters(string sExpress, string SepStart, ref string Param1, ref string Param2, ref string param3, ref string param4, ref string param5, ref string param6, ref string param7)
        {
            string param8 = "";

            GetParameters(sExpress, SepStart, ref Param1, ref Param2, ref param3, ref param4, ref param5, ref param6, ref param7, ref param8);
        }
        public void GetParameters(string sExpress, string SepStart, ref string Param1, ref string Param2, ref string param3, ref string param4, ref string param5, ref string param6, ref string param7, ref string param8)
        {
            int n; int n_p=-1; int n_Pre=-1; int n_PreTmp=-1; string sCur; int n_YH=-1;

            Param1 = "";
            Param2 = "";
            param3 = "";
            param4 = "";
            param5 = "";

            n_p = 0;
            if (SepStart != "")
            {
                n_PreTmp = sExpress.IndexOf(SepStart) + 1;// InStr(sExpress, SepStart) + 1
                if (n_PreTmp == 1) return;
            }
            else
            {
                n_PreTmp = 0;
            }
            n_Pre = n_PreTmp;

            List<Int32> colYH = new List<int>();//  Dim colYH As New Collection
            n_YH = -1;
            while (true)
            {
                n_YH = sExpress.IndexOf("\"", n_YH + 1);//  n_YH = InStr(n_YH + 1, sExpress, Chr(34))
                if (n_YH >= 0)
                    colYH.Add(n_YH);
                else
                    break;
            }

            int YhCount; bool bInYH = false;
            YhCount = colYH.Count;


            while (true)
            {
                n = sExpress.IndexOf(",", n_PreTmp);//   n = InStr(n_PreTmp, sExpress, ",")
                bInYH = false;
                if (YhCount > 0)
                {
                    int v_n_YH1; int v_n_YH2;
                    int i;
                    for (i = 0; i < YhCount; i += 2) //;// To YhCount Step 2
                    {
                        v_n_YH1 = colYH[i];
                        if (i + 1 <= YhCount - 1)
                            v_n_YH2 = colYH[i + 1];
                        else
                            v_n_YH2 = 0;

                        if (n > v_n_YH1 && n < v_n_YH2)
                            bInYH = true;
                    }
                }


                if (n == -1)
                {
                    sCur = sExpress.Substring(n_Pre, sExpress.Length - n_Pre);//  sCur = Mid(sExpress, n_Pre, Len(sExpress) - n_Pre + 1);
                }
                else
                {
                    if (n == n_Pre)
                        sCur = "";
                    else
                        sCur = sExpress.Substring(n_Pre, n - n_Pre);//   sCur = Mid(sExpress, n_Pre, n - n_Pre)

                }

                n_PreTmp = n + 1;
                if (!bInYH)
                {
                    n_Pre = n_PreTmp;

                    n_p = n_p + 1;
                    sCur = sCur.Trim();
                    switch (n_p)
                    {
                        case 1:
                            Param1 = sCur; break;
                        case 2:
                            Param2 = sCur; break;
                        case 3:
                            param3 = sCur; break;
                        case 4:
                            param4 = sCur; break;
                        case 5:
                            param5 = sCur; break;
                    }
                }
                //      '*********Prevent DeadLoop*******************************
                //   Dim nMyLoopCount As Long
                //   nMyLoopCount = nMyLoopCount + 1
                //   If nMyLoopCount > 2000 Then
                //      WriteLog "DeadLoop in GetParameters", True
                //      Exit Do
                //   End If
                //   '*********************************************************
                if (n == -1)
                    break;
            }
        }
        public LinkedList<CVariable> m_lstVariables = new LinkedList<CVariable>();
        public object GetValue(string strV, string errDes)
        {
            
                errDes = "";
                strV = strV.Trim();
                if( strV == "" )
                {
                   return "";
                }
                 if( CPubVars.IsNumeric(strV))//'first see if it is a numeric
                 {
                    return  Convert.ToDouble(strV);
                 }
                 
                 bool fL=false; bool  fR=false; 


                if(strV.Substring(0,1)=="\"")// Left(strV, 1) = Chr(34) Then fL = True
                   fL = true;
                 if(strV.Substring(strV.Length-1,1)=="\"")// If Right(strV, 1) = Chr(34) Then fR = True
                   fR=true;
                string lStrV;
                lStrV = strV.ToLower();

                if( fL && fR )
                         return strV.Substring(1, strV.Length - 2);//  Mid(strV, 2, Len(strV) - 2)
                else if( fL || fR )
                {
                         errDes = "lacking \"" ;
                        return null;
                }
                else if(CPubVars.IsNumeric(strV))
                {
                    return strV;
                       
                }
                else if( lStrV == "true" )
                {
                       return true;
                }
                else if( lStrV == "false")
                {
                       return  false;
                }
                else if( lStrV == "sys_time" || lStrV == "{sys_time}" || lStrV == "now" || lStrV == "{now}" )
                {
                      
                     return CPubVars.GetDateText(DateTime.Now);
                       
                }  
                else 
                {

                        strV =strV.Replace("{", "");
                        strV =strV.Replace("}", ""); 
                        if(strV.Contains(" "))
                        {
                          errDes = "Invalid Variable Defination. Variable:" + strV;
                         return null;
                        }
                        if (m_bCollectingFunVar)
                        {
                            if (!m_dicCalledVariable.ContainsKey(strV))
                            {
                                CVariable called_var = new CVariable();
                                called_var.name = strV;
                                m_dicCalledVariable.Add(strV, called_var);
                            }
                        }
                        CVariable var;
                        
                        var = findVariable(strV);
                        if (var != null)
                        {
                            if (var.value == null)
                                return null;
                            else
                                return var.value;
                        } 
                        else
                        {
                            return "";// = Empty                        
                        }
                      
                }

        }
         
        public CVariable findVariable(string varname)
        {
            foreach (CVariable v in m_lstVariables)
            {
                if (v.name == varname || v.short_name ==varname)
                {
                    return v;
                }
            }
            return null;
        }
    }
    public class CFunction
    {
        public string fun_name = ""; public string fun_value = "";
        public string param1 = ""; public string param2 = "";
        public string param3 = "";
        public string param4 = "";
        public string param5 = "";
        public string param6 = "";
        public string param7 = ""; public string param8 = "";
        public bool HaveParam(string paramName)
        {
            Dictionary<string,string> dic=new Dictionary<string,string>(); 
            if (param1 == paramName) return true;
            if (param2 == paramName) return true;
            if (param3 == paramName) return true;
            if (param4 == paramName) return true;
            if (param5 == paramName) return true;
            if (param6 == paramName) return true;
            if (param7 == paramName) return true;
            if (param8 == paramName) return true; 
            return false; 
        }
        public string paramFldToSQLFld(string paramFld)
        { 
             string v=paramFld;
             v=v.Replace("仓库","branch");
             v=v.Replace("当前进价成本额","sum(money_inout_flag *price * quantity * unit_factor)");
             v=v.Replace("当时进价成本额","sum(money_inout_flag *cost_price_prop * quantity * unit_factor)");
             v=v.Replace("当时加权成本额","sum(money_inout_flag *cost_price * quantity * unit_factor)");
             v=v.Replace("现在加权成本额","sum(money_inout_flag * cost_price_avg * quantity * unit_factor)");
             v=v.Replace("销售额","sum(money_inout_flag *valid_price * quantity * unit_factor)");
             v = v.Replace("现金支付", "sum(money_inout_flag * sub_amount *cash_amount/total_amount)");
             v = v.Replace("银行卡支付", "sum(money_inout_flag * sub_amount *bank_amount/total_amount)");
             v = v.Replace("会员卡支付", "sum(money_inout_flag * sub_amount *card_amount/total_amount)");
             v = v.Replace("积分兑换支付", "sum(money_inout_flag * sub_amount *ticket_amount/total_amount)");
             return v;              
        }
        // cmd.CommandText = "select branch_name, sub(money_inout_flag *price * quantity * unit_factor) as in_amount,sub(money_inout_flag *cost_price_prop * quantity * unit_factor) as cost_prop_amount,sum(money_inout_flag * sub_amount) as total_sale,sum(money_inout_flag * sub_amount *other1_amount/total_amount) as other5_sale, sum(money_inout_flag * sub_amount *other1_amount/total_amount) as other1_sale,sum(money_inout_flag * sub_amount *other2_amount/total_amount) as other2_sale,sum(money_inout_flag * sub_amount *other3_amount/total_amount) as other3_sale,sum(money_inout_flag * sub_amount *other4_amount/total_amount) as other4_sale,sum(money_inout_flag * sub_amount *other1_amount/total_amount) as other5_sale,sum(money_inout_flag * sub_amount *other6_amount/total_amount) as other6_sale  from sheet_item_detail left join sheet_item_master on sheet_item_detail.sheet_no=sheet_item_master.sheet_no where approve_flag='1' and " + sTimeCondi + " and (red_flag is null or red_flag='0') group by sheet_item_detail.branch_name"; ;
                      

        //public string Get
        public string period_start = "";public string period_end = "";
        public List<string> lstSubCode = new List<string>();

    }
    public class CVariable
    {
        public string name = ""; public string short_name = ""; public string other_name = "";
        public object value = null;
        object defaultValue = null;
        public  string VarType = "";
       
        object GetValue() 
     {
        string  sName="";
        sName = name.ToLower();
        switch( sName)
        {
            case "sys_time":
            case "now":
              return DateTime.Now;
              
            default:
              return value;
              
        }
     }
        public void SetValue(object NewValue)
        {
            value = NewValue;
        }


        public CVariable ColonMe()
        {
            CVariable newVar = new CVariable();
            newVar.name = name;
            newVar.value = value;
            newVar.defaultValue = defaultValue;
            newVar.VarType = VarType;
           return newVar;
        }
    }
    public class CSubjectAmtRow
    {
        public string sub_id = ""; public string sub_code = "";
        public string period = ""; public double end_amount = 0; public double start_amount = 0;
        public double debit_amount = 0; public double credit_amount = 0; public double debit_amount_year = 0; public double credit_amount_year = 0;


    }
}
