﻿@page
@model ArtisanManage.Pages.BaseInfo.CommissionSummaryModel
@{
    var operKey = Request.Query["operKey"];
}
<link rel="stylesheet" href="/css/DataForm.css?v=3.32" />
<link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css" type="text/css" />
<link href="~/css/component.css" rel="stylesheet" asp-append-version="true" />
<style>
    * {
        margin: 0;
        padding: 0;
        font-weight: 400;
        font-family: 微软雅黑;
    }


        html, body {
            height: 100%;
        }

        .query-box {
            display: flex;
            flex-flow: row wrap;
            align-items: center;
            margin: 1rem 2rem;
        }

        #divList > header {
            flex: none;
            display: flex;
            justify-content: space-between;
            margin: 0 1rem;
            border-bottom: 1px solid #ddd;
            padding: 1rem 0;
        }

        #divList > main {
            margin: 0 1rem;
            
            /*margin-top:50px;*/
        }

        #divEdit > header {
            flex: none;
            margin: 2rem;
            border-bottom: 1px solid #ddd;
            padding-bottom: 1rem;
        }

        #divEdit > form {
            flex: auto;
            margin: 2rem;
            border-bottom: 1px solid #ddd;
        }

        #divEdit > form > label {
                display: flex;
                justify-content: space-between;
                margin: 1rem 0;
         }
    #gridScroller {
        position: static !important;
        right: 0 !important;
    }
    #divEdit > footer {
        flex: none;
        display: flex;
        justify-content: space-around;
    }

        #divView > header {
            flex: none;
            margin: 2rem;
            border-bottom: 1px solid #ddd;
            padding-bottom: 1rem;
        }

        .err {
            border: red 1px solid;
        }
        /*    .emptyRow{
            display:none;
        }

    */
        .btnView {
            color: blue;
            text-decoration: underline;
        }

    table {
        height: calc(100% - 200px);
        border-collapse: collapse;
    }

        th, td {
            border: 1px #ddd solid;
            text-align: center;
        }

        .showBy, [reset] {
            display: none;
        }

        [showChild~=divList] #divList,
        [showChild~=divEdit] #divEdit,
        [showChild~=divView] #divView {
            display: flex;
            height: 80%;
            flex-flow: column nowrap;
        }

        .form-row {
            line-height: 1.5rem;
            margin: 0.5rem;
        }

            .form-row label {
                display: inline-block;
                width: 30%;
                text-align: end;
                margin: 0 0.5rem;
            }

            .form-row select,
            .form-el {
                display: inline-block;
                width: 30%;
                text-align: start;
                outline: none;
                border: none;
                border-bottom: 1px groove;
            }

        .verification-failed {
            border: 1px red solid;
        }

        .idx, thead tr {
            background-color: #e8e8e8;
        }

        .commission_str {
            color: #5588f8;
            cursor: pointer;
        }

        tbody > tr {
            height: 25px;
        }

        th {
            height: 30px;
        }

        .searcher {
            height: 30px;
        }

        .searcher > input {
            margin-right: 0;
            padding-right: 0;
            width: 160px;
            border: none;
            border-bottom: 2px #ddd solid;
        }

        .main-button {
            margin-left: 40px;
        }
        tfoot td {
            height: 30px;
        }

        /*table th {
            height: 40px;
        }

        .col0, .idx {
            width: 60px;
        }*/
</style>

<div class="showBy" id="divList">
    <form id="queryBox" style="display: flex; align-items: center;">
        <div style="float:left">
            <div id="jqxdatetimeStartTime" bind="startDay" style="float:left"></div>
            <label style="float:left">~</label>
            <div id="jqxdatetimeEndTime" bind="endDay" style="float:left"></div>
            @*<input type="date" bind="startDay" placeholder="从" /> ~
            <input type="date" bind="endDay" placeholder="到" />*@
        <div style="display: flex; align-items: center; ">
            <input type="checkbox" id="queryByOrder" bind="queryByOrder" style="margin-left: 10px;margin-right: 5px; margin-top:5px;">
                <label for="queryByOrder" style="font-size: 0.9em; color: #444444; margin-top:5px;">根据订单时间查询</label>
        </div>
        </div>
        @* <select bind="seller_id"></select>   *@
    </form>
    <main id="grid"></main>
</div>
<script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
<script src="~/js/commission.js?v=@Html.Raw(Model.Version)" asp-append-version="true"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdatetimeinput.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcalendar.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxloader.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.js"></script>
<script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.culture.zh-CN.js"></script>
@* <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
<script src="https://cdn.jsdelivr.net/npm/toastify-js"></script> *@
<script src="~/js/toastify.js" asp-append-version="true"></script>
<script src="~/css/toastify.css" asp-append-version="true"></script>
<script type="text/javascript">
    
    app = new App('@operKey', {
        setup() {
            this
                .add('Fetch')
                .add('Grid')
                .add('QueryBox')
                .add('Dialog')
                .ready(() => {
                    //var today = new Date();
                    //var start = today.getDateString(0,0);
                    //var end = today.getDateString();
                    ////console.log(end);
                    //app.queryBox.$el.find('input[bind=startDay]').val(start).change();
                    //app.queryBox.$el.find('input[bind=endDay]').val(end).change();              
                    $("#jqxdatetimeStartTime").jqxDateTimeInput({ culture: 'zh-CN', width: '170px', height: '25px', formatString: "yyyy-MM-dd HH:mm:ss"});
                    $("#jqxdatetimeEndTime").jqxDateTimeInput({ culture: 'zh-CN', width: '170px', height: '25px', formatString: "yyyy-MM-dd HH:mm:ss" });
                    //$('#jqxdatetimeStartTime').jqxDateTimeInput('val', start);
                    app.queryBox.data.startDay = $('#jqxdatetimeStartTime').jqxDateTimeInput('val')
                    app.queryBox.data.queryByOrder = $('#queryByOrder').prop('checked')
                    var s = $('#jqxdatetimeEndTime').jqxDateTimeInput('val').toString();
                    if (s != '') {
                        s = s.replace('00:00:00', '23:59:59');
                        $('#jqxdatetimeEndTime').jqxDateTimeInput('val', s);
                    }
                    app.get('CommissionSummary/GetDataItemOptions', { dataItemName: 'seller_id' }).catch(employees => {
                        //console.log('qqq', employees);
                        var options = employees.map(employee => `<option value='${employee.v}'>${employee.l}</option>`);
                        options.unshift('<option value="-1">请选择员工</option>');
                        app.queryBox.$el.find('select').html(options);
                    });
                })
                .useKeyboard('keydown', {
                    Enter(e) {
                        setTimeout(function () {
                            app.Query();
                        }, 1);
                    },
                    PageUp(e) {
                        app.grid.go(-20);
                    },
                    PageDown() {
                        app.grid.go(20);
                    },
                    ArrowUp(e) {
                        var step = e.ctrlKey ? 20 : 1;
                        app.grid.go(-step);
                    },
                    ArrowDown(e) {
                        var step = e.ctrlKey ? 20 : 1;
                        app.grid.go(step);
                    },
                    End() {
                        app.grid.goto(10000000);
                    },
                    Home() {
                        app.grid.goto(0);
                    }
                })
        },
        '.': {
            click: {
                btnQuery() {
                    app.Query();
                },
            },
        },
        Query() {
            app.storeGrid(Object.values([]));
             // data.sumResult.idx = "合计";
            var tfoot = app.grid.table.tfoot;
            var btnQury=$('.btnQuery')
            showLoading(btnQury)
            tfoot.render({});
            app.queryBox.data.queryByOrder = $('#queryByOrder').prop('checked');
            this.queryBox.Query((data) => {
                app.storeGrid(Object.values(data.rows));
                //var testData = [];
                //Object.values(data.rows).forEach(x => {
                //    testData.push(x, x);
                //});
                //app.storeGrid(testData);
                data.sumResult.idx = "合计";
                tfoot = app.grid.table.tfoot;
                tfoot.render(data.sumResult);
                hideLoading(btnQury)
            }, (result) => { 
                var toast = Toastify({
                              text: result.msg,
                              duration: 3000, // 持续时间（毫秒）
                              close: false, // 是否显示关闭按钮
                              gravity: "top", // 弹出位置（top, bottom, left, right）
                              position: "center", // 对齐方式（left, right, center）
                              backgroundColor: "red"
                            });
                // 显示提示框
                toast.showToast();
                app.storeGrid(Object.values([]));
                hideLoading(btnQury)
            })
        }
    });
    window.g_companySetting =@Html.Raw(Model.JsonCompanySetting)
    var forGetter = false;
    if (window.g_companySetting && window.g_companySetting.commissionForMoneyGetter) {
        var forGetter = window.g_companySetting.commissionForMoneyGetter
        if (forGetter.toString().toLowerCase() == "true") {
            forGetter = true;
        }
    }
    app.useQueryBox('queryBox', {
        url: '/api/CommissionSummary/GetQueryRecords',
        args: {
            gridID: 'gridItems',
            startRow: 0,
            endRow: 100,
            GetRowsCount: true,
            forMoneyGetter:forGetter  
        },
    });
    app.useFetch({/**构思中*/
        api: 'OpenAccount',
        token: '',
        timeout: 0
    });
    app.ready(function () {
        app.useGrid({
            buildTable(table) {
                table.child('thead', {
                    cols: ['',   '员工', '合计提成',       '岗位',      '提成金额',       '销售金额',     '销售欠款',           '销售收欠款',                  '实销金额',                      '销售数量',  '销售净额',      '退货金额',      '退货欠款',           '退货收欠款',               '退货数量',      '销售提成', '退货扣减'],
                }).child('tbody', {
                    size: 16,
                    cols: ['idx', 'name', 'commission_sum', 'operType', 'commission_str', 'amount_x_str', 'amount_x_arrears_str',  'amount_x_from_arrears_str','amount_x_real_sale_str', 'quantity_x_str', 'net_amount_str','amount_t_str', 'amount_t_arrears_str','amount_t_from_arrears_str', 'quantity_t_str', 'commission_x_str', 'commission_t_str'],
                    render: {
                        idx(data, i) {
                            return ++i;
                        },
                        operType(data) {
                            return {seller:'业务员',sender:'送货员'}[data.operType];
                        },
                        commission_sum(data) {
                            return data.commission.toFixed(2);
                        }
                    }
                })
            },
            '.': {
                afterFill: {
                    name(e) {
                        console.log('合并单元格');
                        var tr = this.closestParent('tr').previousElementSibling;
                        if (!tr) return;
                        if (tr.cells[1].textContent === this.textContent) {
                            tr.cells[1].rowSpan = 2;
                            this.hidden = true;
                            tr.cells[2].textContent = (tr.cells[2].textContent * 1 + e.data.commission + 0.005).toFixed(2);
                            tr.cells[2].rowSpan = 2;
                            this.nextElementSibling.hidden = true;
                            return;
                        }
                        tr.cells[1].rowSpan = 1;
                        tr.cells[2].rowSpan = 1;
                        this.hidden = false;
                        this.nextElementSibling.hidden = false;
                    }
                },
                click: {
                    commission_str(e) {
                        var data = app.view(this);
                        //console.log(data); return;
                        var workerID = data.id, workerName = data.name;
                        //var startDay = app.queryBox.data.startDay;
                        var startDay = $('#jqxdatetimeStartTime').jqxDateTimeInput('val').toString();
                        //var endDay = app.queryBox.data.endDay;
                        var endDay = $('#jqxdatetimeEndTime').jqxDateTimeInput('val').toString();
                        //endDay=endDay+" 23:59";
                        var job = data.operType;
                        if ($('#queryByOrder').prop('checked'))
                            var queryByOrder = "True";
                        else { var queryByOrder = "False"; }
                        var url = `/Report/CommissionDetail?workerID=${workerID}&name=${workerName}&startDay=${startDay}&endDay=${endDay}&job=${job}&queryByOrder=${queryByOrder}`;
                        parent.newTabPage('提成明细', url);
                    }
                }
            }
        });
        app.useDialog({
            id: 'ResetKey',
            title: '重置员工密码',
            caller: '.btnResetKey',
            tmpl: 'ResetKeyTmpl',
            beforeShow(e, caller) {
                this.data = app.view(caller);
            },
            okEvent(self) {
                var data = app.validator.verify('#ResetKey');
                console.log(data);
                if (!data) return;
                data.company_Id = this.data.company_Id;
                app.fetch.post(data, '/api/OpenAccount/ResetPassword', function (x) {
                    app.showMessage(x);
                    self.Hide();
                })
            }
        });
        app.Query();
    });

        
        function dealFormData(data) {
            var forGetter = false;
            if (window.g_companySetting && window.g_companySetting.commissionForMoneyGetter) {
                var forGetter = window.g_companySetting.commissionForMoneyGetter
                if (forGetter.toString().toLowerCase() == "true") {
                    forGetter = true;
                }
            }
            data.forMoneyGetter=forGetter
        }
    function showLoading(btnQury) {
        btnQury.attr('disabled',true)
        if ($('#jqxLoader').length == 0) {
            $('body').append('<div id="jqxLoader"></div>')
            $("#jqxLoader").jqxLoader({ width: 100, height: 60, imagePosition: 'top' });
        }
        $('#jqxLoader').jqxLoader('open');        
    }
    function hideLoading(btnQury) {
         btnQury.attr('disabled',false)
        $('#jqxLoader').jqxLoader('close');
    }
</script>
   