﻿using ArtisanManage.Enums;
using ArtisanManage.Models;
using ArtisanManage.Services;
using MathNet.Numerics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static ArtisanManage.Models.DataItem;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ArtisanManage.Pages.BaseInfo
{
    public class CommissionDetailModel : PageQueryModel
    { 
        public CommissionDetailModel(CMySbCommand cmd) : base(Services.MenuId.commission)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ShowSeconds = true,Width="170", SqlFld="sm.happen_time",CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ShowSeconds = true,Width="170", SqlFld="sm.happen_time",CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00:00','23:59:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                 {"item_id",new DataItem(){Title="商品名称",LabelFld="item_name",SqlFld="tem.item_id",PlaceHolder="商品名称", Width="300", ButtonUsage="list",CompareOperator="=",ShowDropDownColumnsHeader=true, datafields="[{datafield: 'l', text: '品名', width: 180},{datafield: 'b', text: '条码', width: 120}]", SearchFields=CommonTool.itemSearchFields, QueryOnChange=true,
                   QueryByLabelLikeIfIdEmpty=true, SqlForOptions =CommonTool.selectItemWithBarcode}},
                {"workerID",new DataItem(){Title="员工",FldArea="divHead",LabelFld="name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.seller_id",
                    SqlForOptions ="select oper_id as v,oper_name as l from info_operator",Necessary=true}},
                {"depart_path",new DataItem(){Title="部门",FldArea="divHead",LabelFld="depart_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", TreePathFld="depart_path",CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"item_class",new DataItem(){Title="类别",FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",MumSelectable=true,CompareOperator="like",SqlFld="ip.other_class",
                   SqlForOptions=CommonTool.selectClasses,MaxRecords="500"}},
				{"forMoneyGetter",new DataItem(){Title="后续收款人享受提成",FldArea="divHead",CtrlType="jqxCheckBox",ForQuery=false}},
				{"queryByOrder",new DataItem(){Title="按照订单时间查询",FldArea="divHead",Hidden=true,HideOnLoad=true}},
                {
                    "job",
                    new DataItem()
                    {
                        Title = "岗位",FldArea="divHead", LabelFld = "job_name", LabelInDB = false, Value = "seller", Label = "业务员",
                        ButtonUsage = "list", Source = "[{v:'seller',l:'业务员'},{v:'sender',l:'送货员'}]",
                        CompareOperator = "=", NullEqualValue = "seller"
                    }
                },

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,                    
                     RowsHeight=40,
                     SubRowsNameInRow="Infos",
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"strategy_name",    new DataItem(){Title="策略", Width="80"}},
                       {"plan_name",    new DataItem(){Title="方案", Width="80"}},
                       {"plan_source",    new DataItem(){Title="方案来源", Width="80"}},
                       {"name",    new DataItem(){Title="品项",   Width="90",
                       SubRowsColumn="item_name",
                                  JSCellRender=@" 
         function (index, datafield, value, defaultvalue, column, rowdata) {
              window.infos[index] = rowdata;
              var  tmpDivs=`<div style='color:#5588f8;display:flex;align-items:center;padding:3px;height:1000;cursor:pointer;'  onclick='showDetail(${index})'>
                                         ${value}
                                   </div>`; 
               return tmpDivs;
        }"

                       }},
                       {"ruleDisplay",    new DataItem(){Title="规则", Width="120"}},
                        {"sale",   new DataItem(){Title="销售",
                           FuncGetSubColumns = (col) =>
                            {
                                var result = new ColumnsResult(){
                                     Columns=new Dictionary<string, DataItem>()
                                     {
                                           {"amount_x_str",   new DataItem(){Title="金额",  CellsAlign="right",  Width="70",ShowSum=true,
                                             JSCellRender=@"function (row, column, value,p4,p5,rowData) {  
                                                   var divArrears=''; 
                                                   if(rowData.amount_x_arrears_str)  divArrears=`<div style=""color:#aaa;font-size:11px;"">欠款${rowData.amount_x_arrears_str}</div>`
                   
                                                    var html = `<div style = ""height:100%;display:flex;align-items:center;justify-content:center;flex-direction:column;"" ><div>${value}</div>${divArrears}</div>`
                                                    return html;
                                            }",SubRowsColumn="x_amount_str"
                                           }},
                                           {"amount_x_arrears_str",   new DataItem(){Title="欠款",  CellsAlign="right",  Width="70",ShowSum=true,Hidden=true}},
                                           {"amount_x_from_arrears_str",   new DataItem(){Title="收欠款",  CellsAlign="right",  Width="100",ShowSum=true}},

                                           {"quantity_x_str",   new DataItem(){Title="数量",  CellsAlign="right",  Width="70",ShowSum=true,SubRowsColumn="x_quantity_str"}},
                                           {"quantity_unit_x",   new DataItem(){Title="数量(单位)",  CellsAlign="right",  Width="100",ShowSum=true,SubRowsColumn="quantity_unit_x"}}

                                     },
                                    
                                };
                                return Task.FromResult(result);
                            }
                        }},
                        {"saleReturn",   new DataItem(){Title="退货",
                           FuncGetSubColumns = (col) =>
                            {
                                var result = new ColumnsResult(){
                                     Columns=new Dictionary<string, DataItem>()
                                     {
                                           {"amount_t_str",   new DataItem(){Title="金额",  CellsAlign="right",  Width="70",ShowSum=true,  JSCellRender=@"function (row, column, value,p4,p5,rowData) {  
                                                   var divArrears=''; 
                                                   if(rowData.amount_t_arrears_str)  divArrears=`<div style=""color:#aaa;font-size:11px;"">欠款${rowData.amount_t_arrears_str}</div>`
                   
                                                    var html = `<div style = ""height:100%;display:flex;align-items:center;justify-content:flex-start;flex-direction:column;"" ><div>${value}</div>${divArrears}</div>`
                                                    return html;
                                            }",SubRowsColumn="t_amount_str"}},
                                           {"amount_t_arrears_str",   new DataItem(){Title="欠款",  CellsAlign="right",  Width="70",ShowSum=true,Hidden=true}},
                                           {"amount_t_from_arrears_str",   new DataItem(){Title="收欠款",  CellsAlign="right",  Width="100",ShowSum=true}},
                                           {"quantity_t_str",   new DataItem(){Title="数量",  CellsAlign="right",  Width="70",ShowSum=true,SubRowsColumn="t_quantity_str"}},
                                           {"quantity_unit_t",   new DataItem(){Title="数量(单位)",  CellsAlign="right",  Width="100",ShowSum=true,SubRowsColumn="quantity_unit_t"}}
                                     },
                                     FldsSQL="amount_t,amount_t_arrears,quantity_t"
                                };
                                return Task.FromResult(result);
                            }
                        }},
                        {"commission",   new DataItem(){Title="提成",
                           FuncGetSubColumns = (col) =>
                            {
                                var result = new ColumnsResult(){
                                     Columns=new Dictionary<string, DataItem>()
                                     {
                                          {"commission_x_str",   new DataItem(){Title="销售提成",  CellsAlign="right",  Width="100",ShowSum=true}},
                                          {"commission_t_str",   new DataItem(){Title="退货扣减",  CellsAlign="right",  Width="100",ShowSum=true}},
                                          {"commission_str",   new DataItem(){Title="提成金额",  CellsAlign="right",  Width="100",ShowSum=true }}
                                     },
                                     FldsSQL="commission_x,commission_t,commission"
                                };
                                return Task.FromResult(result);
                            }
                        }},
                        {"other_info",    new DataItem(){Title="其他信息", Width="100",SubRowsColumn="other_info"}},
                      }
                  }                    
               }               
            };             
        }

        public override async Task<JsonResult> GetRecordFromQuerySQL(HttpRequest request, CMySbCommand cmd,dynamic postData=null)
        {

            string startDay = GetParamFromRequest(request,postData, "startDay");
            string endDay = GetParamFromRequest(request, postData, "endDay");
            string workerID = GetParamFromRequest(request, postData, "workerID");
            string operKey = GetParamFromRequest(request, postData, "operKey");
            string operType= GetParamFromRequest(request, postData, "job");
            string itemId = GetParamFromRequest(request, postData, "item_id");
            string itemClass = GetParamFromRequest(request, postData, "item_class");
            string m = GetParamFromRequest(request, postData, "queryByOrder") ??"";

            string s = GetParamFromRequest(request, postData, "forMoneyGetter") ?? "";
            bool queryByOrder = m.ToLower() == "true";
            bool forMoneyGetter = s.ToLower() == "true";
            Security.GetInfoFromOperKey(operKey, out string companyID);
            
            string ignoreDataItems = "";
            DataItemGroup.FillDataItemsByRequest(DataItems, request,null, ref ignoreDataItems);
            var planMaps = await PlanWithMap.GetList(cmd, companyID);
            bool bHasDeductArrear = false;
            if (workerID == "")
            {
                return new JsonResult(new { result = "Error", msg = "请选择员工" });
            }
            string strategysql = $@"SELECT cc.flow_id,cm.company_id,cc.plan_id, plan.plan_name, plan.content,plan.type,cm.strategy_id id,'client' strategy_type,ct.strategy_name, cm.worker_id oper_id,op.oper_name ,cc.supcust_id,null group_id,null rank_id,null region_id  FROM commission_strategy_map cm 
LEFT JOIN commission_strategy_client cc on cm.company_id = cc.company_id and cm.strategy_id = cc.strategy_id
LEFT JOIN commission_plan plan on plan.company_id = cc.company_id and plan.plan_id = cc.plan_id
LEFT JOIN info_operator op on cm.company_id = op.company_id and cm.worker_id = op.oper_id
LEFT JOIN commission_strategy ct on ct.company_id = cm.company_id and ct.strategy_id = cm.strategy_id
WHERE cm.company_id = {companyID}  and cm.worker_id = {workerID}  and cc.plan_id is not null and plan.type = '{operType}'
union
SELECT cs.flow_id,cm.company_id,cs.plan_id, plan.plan_name, plan.content,plan.type,cm.strategy_id id,'class' strategy_type,ct.strategy_name, cm.worker_id oper_id,op.oper_name ,null supcust_id,cs.group_id,cs.rank_id,cs.region_id FROM commission_strategy_map cm 
LEFT JOIN commission_strategy_class cs on cm.company_id = cs.company_id and cm.strategy_id = cs.strategy_id
LEFT JOIN commission_plan plan on plan.company_id = cs.company_id and plan.plan_id = cs.plan_id
LEFT JOIN commission_strategy ct on ct.company_id = cm.company_id and ct.strategy_id = cm.strategy_id
LEFT JOIN info_operator op on cm.company_id = op.company_id and cm.worker_id = op.oper_id

WHERE cm.company_id = {companyID} and cm.worker_id = {workerID}  and cs.plan_id is not null and plan.type = '{operType}'
ORDER BY flow_id desc;";
            var list = await CDbDealer.GetRecordsFromSQLAsync(strategysql, cmd);
            List<CommissionStrategy> lstStrategies = new List<CommissionStrategy>();
            if (list.Count() > 0)
            {
                foreach (var item in list)
                {
                    var st = JsonConvert.DeserializeObject<CommissionStrategy>(JsonConvert.SerializeObject(item));
                    lstStrategies.Add(st);

                }
            }
            var lstStrategy_client = new List<CommissionStrategy>();
            var lstStrategy_class = new List<CommissionStrategy>();
            if (lstStrategies.Count() > 0)
            {
                foreach (var st in lstStrategies)
                {

                    //lstStrategy.Add(st);
                    if (st.strategy_type == "class") lstStrategy_class.Add(st);
                    if (st.strategy_type == "client") lstStrategy_client.Add(st);
                    if (!bHasDeductArrear)
                    {

                        dynamic planContent = JsonConvert.DeserializeObject(st.content);
                        foreach (var rule in planContent.rules)
                        {
                            if ((bool)rule.deductArrearages)
                            {
                                bHasDeductArrear = true;
                            }
                        }
                    }



                }
            }
            string planStr = "case ";
            var num_client = lstStrategy_client.Count();
            var num_class = lstStrategy_class.Count();
            if (num_client > 0)
            {
                foreach (var st in lstStrategy_client)     
                {
                    string orderSourceStr = "";
                    if (operType == "seller" && st.order_source == "线上")
                    {
                        orderSourceStr += $@"  and ssm.order_source = 'xcx' " ;
                    }
                    else if(operType =="seller"&& st.order_source == "线下")
                    {
                        orderSourceStr += $@" and (ssm.order_source !='xcx' or ssm.order_source is null) ";
                    }
                    
                        planStr += $@"  when ssm.supcust_id = {st.supcust_id} {orderSourceStr} then '{st.plan_id}'  ";                         
                }
            }
            if (num_class > 0)
            {
                foreach (var st in lstStrategy_class)
                {
                    string rankStr = "", regionStr = "", groupStr = "",orderSourceStr="";
                    if(operType=="seller"&&st.order_source == "线上")
                    {
                        orderSourceStr = $"and ssm.order_source = 'xcx' ";
                    }
                    else if (operType == "seller" && st.order_source == "线下")
                     {
                        orderSourceStr = $"and (ssm.order_source !='xcx' or ssm.order_source is null) ";
                    }
                    if (st.region_id != "") regionStr = $" sup.other_region like '%/'||{st.region_id}||'/%' ";
                    if (st.rank_id != "")
                    {
                        if (regionStr != "") rankStr = $" and sup.sup_rank= {st.rank_id} ";
                        else rankStr = $" sup.sup_rank= {st.rank_id} ";

                    }

                    if (st.group_id != "")
                    {
                        if (regionStr != "" || rankStr != "") groupStr = $" and sup.sup_group = {st.group_id} ";
                        else groupStr = $" sup.sup_group = {st.group_id} ";
                    }
                    planStr += $@"  when {regionStr} {rankStr} {groupStr}{orderSourceStr} then '{st.plan_id}' ";
                }
            }
            planStr += " end ";
           
            string queryBy = "";
            if (queryByOrder)
            {
                queryBy = "ssm.order_sheet_time";
            }
            else
            {
                queryBy = "ssd.happen_time";
            }
            var where = $"WHERE ssd.company_id = {companyID} AND ssm.company_id = {companyID} and {queryBy} <= '{endDay}' AND {queryBy} >= '{startDay}' and oper_type = '{operType}'";

            if (itemId.IsValid())
            {
                where += $" and ssd.item_id = '{itemId}' ";
            }
            if (itemClass.IsValid())
            {
                where += $" and ip.other_class like '%{itemClass}%' ";

            }
            string workerIdFld = " ssm.oper_id ";
            if (forMoneyGetter)
            {
                workerIdFld = "case when ga.getter_id is not null and oper_type = 'seller' then ga.getter_id else ssm.oper_id end";
            }

            if (workerID.IsValid()) where += $" AND {workerIdFld} = {workerID}";
            string result = "OK", msg = "";
            Dictionary<int, CommissionDetail> rows = null;
            CommissionDetail sumResult = null;
            int rowsCount = 0;
            if(lstStrategies.Count()==0) return new JsonResult(new { result = "error", msg = $"该员工所属策略没有设置提成方案", rowsCount, rows, sumResult });
            string saleSheetCondi = "";
            if (queryByOrder)
            { saleSheetCondi = $" and order_sheet_time>='{startDay}' and order_sheet_time<'{endDay}'"; }
            else
            { saleSheetCondi = $" and happen_time>='{startDay}' and happen_time<'{endDay}'"; }
            var commissionInfos = await CommissionInfo.GetList_newest(cmd, companyID,  where, startDay, endDay,forMoneyGetter, saleSheetCondi, workerID, planStr, lstStrategies, queryByOrder);
            var itemClasses = await cmd.QueryAsync<ItemClass>(companyID);
           

            List<CommissionInfo> lstGetArrearsItems = new List<CommissionInfo>();
            if (bHasDeductArrear)
            {
                string sql = @$"
select string_agg(mm_sheet_id::text,',') mm_sheet_ids,min(sm.happen_time) min_sale_time 
from sheet_get_arrears_detail d 
LEFT JOIN sheet_sale_main sm on d.company_id = sm.company_id and d.m_sheet_type in ('X','T') and d.mm_sheet_id = sm.sheet_id
LEFT JOIN sheet_get_arrears_main m on d.company_id = m.company_id and d.sheet_id = m.sheet_id and m.happen_time>='{startDay}' and m.happen_time<='{endDay}'
where d.company_id={companyID} and d.happen_time>='{startDay}' and d.happen_time<='{endDay}'  and m.red_flag is null and m.approve_time is not null and abs(d.left_amount)<0.01  and sm.sheet_id is not null ";

                if (workerID.IsValid())
                {
                    if (forMoneyGetter)
                        sql += $" AND (m.getter_id = {workerID} or EXISTS ( SELECT 1  FROM unnest(string_to_array(sm.senders_id, ',')) AS sender_id WHERE sender_id = '{workerID}'))";
                }
                cmd.CommandText = sql;
                string mm_sheet_ids = "", min_sale_time = "";
                CMySbDataReader dr = await cmd.ExecuteReaderAsync();
                if (dr.Read())
                {
                    mm_sheet_ids = CPubVars.GetTextFromDr(dr, "mm_sheet_ids");
                    min_sale_time = CPubVars.GetDateText(CPubVars.GetTextFromDr(dr, "min_sale_time"));
                }
                dr.Close();

                if (mm_sheet_ids != "")
                { 
                    where = $"WHERE ssd.company_id = {companyID} AND ssm.company_id = {companyID} and ssd.sheet_id in ({mm_sheet_ids}) and ssd.happen_time>='{min_sale_time}' and ssd.happen_time<'{startDay}'";
                    if (itemId.IsValid())
                    {
                        where += $" and ssd.item_id = '{itemId}' ";
                    }
                    if (itemClass.IsValid())
                    {
                        where += $" and ip.other_class like '%{itemClass}%' ";

                    }
                    if (forMoneyGetter)
                    {
                        where += $" and case when ga.getter_id is not null and oper_type = 'seller' then ga.getter_id else ssm.oper_id end in ({workerID}) ";
                    }
                    else
                    {
                        where += $" and {workerIdFld} in ({workerID}) ";
                    }
                    saleSheetCondi = $" and happen_time>='{min_sale_time}' and happen_time<'{startDay}' and sheet_id in ({mm_sheet_ids})"; 
                    lstGetArrearsItems = await CommissionInfo.GetList_newest(cmd, companyID, where, startDay, endDay,forMoneyGetter, saleSheetCondi, workerID, planStr, lstStrategies, queryByOrder);
                    lstGetArrearsItems.ForEach(item =>
                    {
                        item.FromGetArrears = true;
                    });
                }
            }

            if (commissionInfos.Count > 0 || lstGetArrearsItems.Count > 0)
            {
                var aa = commissionInfos.newGroupAndSumByEmployee(lstGetArrearsItems, planMaps,out msg, itemClasses);
                if (msg != "") { return new JsonResult(new { result = "error", msg }) ; }
				var sumAndDetails = aa.FirstOrDefault(x=>x.Key.OperType==operType);

                sumResult = sumAndDetails.Key;
                if (sumAndDetails.Value == null) return new JsonResult(new { result="error",msg = $"该员工作为{operType}没有提成方案",rowsCount,rows,sumResult });
                rows = sumAndDetails.Value.Select((x, i) => new KeyValuePair<int, CommissionDetail>(i, x)).ToDictionary(x => x.Key, x => x.Value);
                rowsCount = rows.Count;
            }
           

            return new JsonResult(new { result, msg, rowsCount, rows , sumResult });
        }
		public override Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
		{
            if (this.JsonCompanySetting.IsValid())
            {
                dynamic setting = JsonConvert.DeserializeObject(this.JsonCompanySetting);
				if (setting !=null && setting.commissionForMoneyGetter!=null)
				{
                    string forGetter = (string)setting.commissionForMoneyGetter;

				    if (forGetter.ToLower() == "true")
					{
                        this.DataItems["forMoneyGetter"].Value = "true";
					}
				}
			}
			return base.OnPageInitedWithDataAndRight(cmd);
		}
		public async Task OnGet()
        { 
            await InitGet(cmd);
        }
    }



    [Route("api/[controller]/[action]")]
    public class CommissionDetailController : YjController
    {
        CMySbCommand cmd;
        public CommissionDetailController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            CommissionDetailModel model = new CommissionDetailModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="startDay"></param>
        /// <param name="endDay"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetQueryRecords(DateTime startDay, DateTime endDay, int id)
        {
            CommissionDetailModel model = new CommissionDetailModel(cmd);
            return await model.GetRecordFromQuerySQL(Request, cmd);
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            CommissionDetailModel model = new CommissionDetailModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
