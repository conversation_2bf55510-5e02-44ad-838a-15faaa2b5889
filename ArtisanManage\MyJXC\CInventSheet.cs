﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data.SqlClient;
using System.Xml;
using System.Net;
using ArtisanManage.Models;
namespace myJXC
{
    public class CInventSheet : CSheet
    {
        public string m_out_branch_no = "";
        public string m_out_branch_name = "";
        public string m_in_branch_no = "";
        public string m_in_branch_name = "";
        public string m_out_branch_man_id = "";
        public string m_out_branch_man_name = "";
        public string m_in_branch_man_id = "";
        public string m_in_branch_man_name = "";

        public CInventSheet()
        {


        }
        public string SetGeneralInfo(string branch_no, string branch_name, string oper_id, string oper_name, string total_amount, string oper_date, string work_date)
        {

            //int n = 0; 
            string s = "";
            //s = CPubVars.GetItemID(branch_no);
            //if (s == "") return "请指定仓库";

            m_branch_no = branch_no;
            m_branch_name = branch_name;// CPubVars.GetItemName(branch_no);           

            m_oper_id = oper_id;
            m_oper_name = oper_name;

            //s = total_amount;
            //if (s == "") return "请指定总额";
            if (!CPubVars.IsNumeric(total_amount)) return "总额应为数字";
            m_total_amount = Convert.ToDouble(total_amount);

            s = oper_date;
            if (s == "") return "请指定操作日期";
            if (!CPubVars.IsDate(s)) return "操作日期格式不正确，不是合法的日期";
            m_oper_date = Convert.ToDateTime(oper_date);

            //s = work_date;
            //if (s == "") return "请指定工作日期";
            //if (!CPubVars.IsDate(s)) return "采购日期格式不正确，不是合法的日期";
            m_work_date = Convert.ToDateTime(work_date);

            base.SetGeneralInfo("", "", "", "", total_amount, "", "", oper_id, "", oper_date, work_date);
            return "";

        }
        public string AddRow(string item_no, string item_name, string item_subno, string unit_no, string unit_factor, string color, string size, string orig_price, string real_price, string quantity, string money)
        {
            string sRow = (SheetRows.Count + 1).ToString();
            //CPubVars moneyDeal = new CPubVars();
            string sRowPrompt = "第" + sRow + "行";

            SheetRow sheetRow = new SheetRow();

            if (item_no == "") return sRowPrompt + "未指定商品";
            sheetRow.item_no = item_no;
            sheetRow.item_name = item_name;
            sheetRow.item_subno = item_subno;
            sheetRow.unit_no = unit_no;

            if (unit_factor == "") return sRowPrompt + "未指定单位因子";
            if (!CPubVars.IsNumeric(unit_factor)) return sRowPrompt + "单位因子必须为数字";
            sheetRow.unit_factor = Convert.ToDouble(unit_factor);

            //if (orig_price == "") return sRowPrompt + "未指定原始价";
            //if (!CPubVars.IsNumeric(orig_price)) return sRowPrompt + "原始价必须为数字";
            if (CPubVars.IsNumeric(orig_price))
                sheetRow.orig_price = Convert.ToDouble(orig_price);

            //if (real_price == "") return sRowPrompt + "未指定实际价";
            //if (!CPubVars.IsNumeric(real_price)) return sRowPrompt + "实际价必须为数字";
            if (CPubVars.IsNumeric(real_price))
                sheetRow.real_price = Convert.ToDouble(real_price);

            if (quantity == "") return sRowPrompt + "未指定数量";
            if (!CPubVars.IsNumeric(quantity)) return sRowPrompt + "数量必须为数字";
            sheetRow.size_id = size;
            sheetRow.color_id = color;
            sheetRow.quantity = Convert.ToDouble(quantity);
            if (CPubVars.IsNumeric(money))
                sheetRow.money = Convert.ToDouble(money);
            SheetRows.Add(sheetRow);
            //base.AddRow(item_no, unit_no, unit_factor, orig_price, real_price, quantity, money);
            return "";

        }

        public async Task<string> LoadSheet(string Sheet_No)
        {
            string connString = CPubVars.ConnString;
            CMySbConnection conn = new CMySbConnection(connString);
            conn.Open();
            CMySbDataReader dr;
            CMySbCommand cmd = new CMySbCommand("", conn);

            cmd.CommandText = "select * from sheet_inventory_user_input left join info_item_color on sheet_inventory_user_input.color_id=info_item_color.color_id  left join info_item_size on sheet_inventory_user_input.size_id=info_item_size.size_id  where input_sheet_no='" + Sheet_No + "'";
            dr = await cmd.ExecuteReaderAsync();
            SheetRows.Clear();
            int nRow = 0;
            while (dr.Read())
            {
                if (nRow == 0)
                {
                    m_sheet_no = Sheet_No;

                    if (dr["branch_no"] != null) m_branch_no = dr["branch_no"].ToString();


                    if (dr["oper_id"] != null) m_oper_id = dr["oper_id"].ToString();

                    if (dr["oper_time"] != null)
                    {
                        DateTime dt = Convert.ToDateTime(dr["oper_time"]);
                        m_oper_date = dt;// dt.Year.ToString() + "-" + dt.Month.ToString() + "-" + dt.Day.ToString() + " " + dt.Hour.ToString().PadLeft(2, '0') + ":" + dt.Minute.ToString().PadLeft(2, '0') + ":" + dt.Second.ToString().PadLeft(2, '0');
                    }
                    //if (dr["work_date"] != null)
                    //{
                    //    DateTime dt = Convert.ToDateTime(dr["work_date"]);
                    //    m_work_date = dt;// dt.Year.ToString() + "-" + dt.Month.ToString() + "-" + dt.Day.ToString() + " " + dt.Hour.ToString().PadLeft(2, '0') + ":" + dt.Minute.ToString().PadLeft(2, '0') + ":" + dt.Second.ToString().PadLeft(2, '0');
                    //}

                    // if(dr["disc_amt"]!=null)  m_disc_amt =Convert.ToDouble(dr["disc_amt"]);                 
                    CMySbCommand cmd1 = new CMySbCommand("", conn);


                    cmd1.CommandText = "select oper_name from info_operator where oper_id='" + m_oper_id + "'";
                    object rr = cmd1.ExecuteScalar();
                    m_oper_name = "";
                    if (rr != null) m_oper_name = rr.ToString();

                }

                SheetRow sheetRow = new SheetRow();
                if (dr["item_no"] != DBNull.Value) sheetRow.item_no = dr["item_no"].ToString();
                if (dr["item_name"] != DBNull.Value) sheetRow.item_name = dr["item_name"].ToString().Trim();
                if (dr["item_subno"] != DBNull.Value) sheetRow.item_subno = dr["item_subno"].ToString().Trim();
                if (dr["unit_no"] != DBNull.Value) sheetRow.unit_no = CPubVars.GetTextFromDr(dr, "unit_no");
                if (dr["unit_factor"] != DBNull.Value) sheetRow.unit_factor = Convert.ToDouble(dr["unit_factor"]);
                //if (dr["orig_price"] != DBNull.Value) sheetRow.orig_price = Convert.ToDouble(dr["orig_price"]);
                //if (dr["valid_price"] != DBNull.Value) sheetRow.real_price = Convert.ToDouble(dr["valid_price"]);
                if (dr["real_quantity"] != DBNull.Value) sheetRow.quantity = Convert.ToDouble(dr["real_quantity"]);
                if (dr["color_id"] != DBNull.Value) sheetRow.color_id = CPubVars.GetTextFromDr(dr, "color_id");
                if (dr["size_id"] != DBNull.Value) sheetRow.size_id = CPubVars.GetTextFromDr(dr, "size_id"); //Convert.ToDouble(dr["size"]);
                if (sheetRow.color_id != "0")
                {
                    if (dr["color_name"] != DBNull.Value) sheetRow.color_name = CPubVars.GetTextFromDr(dr, "color_name");
                }
                if (dr["size_id"] != DBNull.Value) sheetRow.size_id = CPubVars.GetTextFromDr(dr, "size_id");
                if (sheetRow.size_id != "0")
                {
                    if (dr["size_name"] != DBNull.Value) sheetRow.size_name = CPubVars.GetTextFromDr(dr, "size_name");
                }
                //if (dr["sub_amount"] != DBNull.Value) sheetRow.money = Convert.ToDouble(dr["sub_amount"]);
                SheetRows.Add( sheetRow);
                nRow++;
            }

            dr.Close();
            conn.Close();
            return "";

        }

        public string SaveSheet(CMySbConnection conn, CMySbCommand cmd)
        {
            string connString = CPubVars.ConnString;
            //CMySbConnection conn = new CMySbConnection(connString);
            //conn.Open();
            CMySbDataReader dr;
            // CMySbCommand cmd = new CMySbCommand("", conn);
            DateTime dt1 = System.DateTime.Today;

            DateTime dt2 = dt1.AddDays(1);
            string sdt1 = CPubVars.GetDateText(dt1);
            string sdt2 = CPubVars.GetDateText(dt2);
            string sheet_no = m_sheet_no;
            string newPdaSheetNo = "";
            if (m_sheet_no == "")
                sheet_no = CSheetSale.GetNewSheetNo(SHEET_TYPE.SHEET_INVENT_INPUT,m_com_no, conn, cmd);
            else if (m_bPDASheet)
            { 
                    string cur_sheet_no = m_sheet_no;
                    for (int i = 1; i < 100; i++)
                    {
                        cmd.CommandText = "SELECT sheet_no from sheet_inventory_user_input where input_sheet_no ='" + cur_sheet_no + "'";
                        object ov = cmd.ExecuteScalar();
                        if (ov == null)
                        {
                            if (m_sheet_no != cur_sheet_no)
                            {
                                newPdaSheetNo = cur_sheet_no;
                            }
                            m_sheet_no = cur_sheet_no;
                            sheet_no = cur_sheet_no;
                            break;
                        }
                        cur_sheet_no = m_sheet_no + "_" + i.ToString();
                    }
            }
            else
            {
                cmd.CommandText = "SELECT * from sheet_inventory_user_input where input_sheet_no ='" + m_sheet_no + "'";
                dr = cmd.ExecuteReader();
                //string sID = "";
                string strErr = "";
                if (!dr.Read())
                {
                    dr.Close();
                    cmd.Dispose();
                    conn.Close();
                    strErr = "该单据不存在";
                    return strErr;
                }

                //string approve_flag = "0";
                //if (dr["approve_flag"] != DBNull.Value)
                //    approve_flag = dr["approve_flag"].ToString();
                //dr.Close();
                //dr.Dispose();

                //m_bApproved = (approve_flag == "1");
                //if (m_bApproved)
                //{
                //    conn.Close();
                //    return "该单据已经审批过，不能保存";
                //}
            }

            #region 获取总体信息

            CDbDealer dbDeal = new CDbDealer();


            cmd = new CMySbCommand("", conn);

            #endregion

            #region 获取详单信息
            // Dictionary<string, CDbDealer> rows = new Dictionary<string, CDbDealer>();
            cmd.CommandText = "delete from sheet_inventory_user_input where input_sheet_no='" + sheet_no + "'";
            cmd.ExecuteNonQuery();
            string sNow = CPubVars.GetDateText(DateTime.Now);
            foreach (var sheetRow in SheetRows)
            {
                
                dbDeal = new CDbDealer();
                dbDeal.AddField("input_sheet_no", sheet_no);
                dbDeal.AddField("com_no", m_com_no);
                dbDeal.AddField("branch_no", m_branch_no);
                dbDeal.AddField("oper_id", m_oper_id);
                dbDeal.AddField("oper_time", CPubVars.GetDateText(m_oper_date), "date");
                dbDeal.AddField("submit_time", sNow, "date");
                dbDeal.AddField("item_no", sheetRow.item_no);
                dbDeal.AddField("item_name", sheetRow.item_name);
                dbDeal.AddField("item_subno", sheetRow.item_subno);
                dbDeal.AddField("unit_no", sheetRow.unit_no.ToString());
                dbDeal.AddField("unit_factor", sheetRow.unit_factor.ToString());
                if (sheetRow.color_id == "") sheetRow.color_id = "0";
                if (sheetRow.size_id == "") sheetRow.size_id = "0";
                dbDeal.AddField("color_id", sheetRow.color_id.ToString());
                dbDeal.AddField("size_id", sheetRow.size_id.ToString());
                dbDeal.AddField("real_quantity", sheetRow.quantity.ToString());
                //dbDeal.AddField("sub_amount", sheetRow.money.ToString());
                //dbDeal.AddField("valid_price", sheetRow.real_price.ToString());              
                string sCmd = dbDeal.GetInsertSQL("sheet_inventory_user_input");
                cmd = new CMySbCommand(sCmd, conn);
                //Debug.WriteLine(sCmd);
                cmd.ExecuteNonQuery();
                cmd.Dispose();
            }

            #endregion
            // conn.Close();
            m_sheet_no = sheet_no;
            return "";

        }



    }
}
