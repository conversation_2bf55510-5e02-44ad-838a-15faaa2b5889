using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;

namespace ArtisanManage.Pages.BaseInfo 
{
    public class UnitEditModel : PageFormModel
    { 
        public UnitEditModel(CMySbCommand cmd, string company_id="",string oper_id="") : base(Services.MenuId.infoUnit)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"unit_id",new DataItem(){Title="编号",FldArea="divHead",Hidden = true}},
                {"unit_no",new DataItem(){Title="单位名称",Necessary=true,FldArea="divHead"}},
                {"is_big_unit",new DataItem(){FldArea="divHead",Title="大单位",CtrlType="jqxCheckBox"}},
                {"order_index",new DataItem(){Title="顺序号",FldArea="divHead"}},

            };

            m_idFld = "unit_id"; m_nameFld = "unit_no"; IdFldIsSerial = true;
            m_tableName = "info_avail_unit";
            m_selectFromSQL = "from info_avail_unit where unit_id='~ID'";
        }

        public async Task OnGet()
        {  
            await InitGet(cmd);   
        } 
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class UnitEditController : BaseController
    {
        public UnitEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues)
        {
            UnitEditModel model = new UnitEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            UnitEditModel model = new UnitEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            UnitEditModel model = new UnitEditModel(cmd);
            return await model.SaveTable(cmd, request);

        }
    }
}