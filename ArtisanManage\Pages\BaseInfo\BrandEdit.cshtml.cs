using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;

namespace ArtisanManage.Pages.BaseInfo 
{
    public class BrandEditModel : PageFormModel
    {
        public BrandEditModel(CMySbCommand cmd, string company_id="",string oper_id="") : base(Services.MenuId.infoBrand)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"brand_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"brand_name",new DataItem(){Title="品牌名称",Necessary=true,FldArea="divHead"}},                
                {"brand_status",new DataItem(){Title="状态",FldArea="divHead",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}},
                {"brand_order_index",new DataItem(){Title="顺序号",FldArea="divHead"}},
                {"remark",new DataItem(){Title="备注",FldArea="divHead"}},
            };

            m_idFld = "brand_id"; m_nameFld = "brand_name";
            m_tableName = "info_item_brand";
            m_selectFromSQL = "from info_item_brand where brand_id='~ID'";
        }

        public async Task OnGet()
        {  
            await InitGet(cmd);   
        } 
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class BrandEditController : BaseController
    { 
        public BrandEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName, string flds, string value,string availValues)
        {
            BrandEditModel model = new BrandEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
           // ,string availValues
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            BrandEditModel model = new BrandEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value,availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            BrandEditModel model = new BrandEditModel(cmd);
            return await model.SaveTable(cmd, request);
        }
    }
}