﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;
using ArtisanManage.Services.SheetService;
using Newtonsoft.Json;
using System.Linq;
using System.IO;
using HuaWeiObsController;
using System.Net.Http;
using ArtisanManage.WebAPI.MessageOSUtil;
using ArtisanManage.YingjiangMessage.Pojo;
using ArtisanManage.YingjiangMessage.Services;
using static Org.BouncyCastle.Math.EC.ECCurve;
using ArtisanManage.WebAPI;
using System.ComponentModel.Design;

namespace ArtisanManage.AppController.Sheets
{


    [Route("AppApi/[controller]/[action]")]
    public class AppSheetSaleOrder:BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public AppSheetSaleOrder(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }



        /// <summary>
        /// 加载单据--返回--支付方式,备注
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID"></param>
        /// <returns></returns>


        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID, string sheetType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID);
            var sheet_type = sheetType == "TD" ? SHEET_RETURN.IS_RETURN : SHEET_RETURN.NOT_RETURN;
            SheetSaleOrder orderSheet = new SheetSaleOrder(sheet_type, LOAD_PURPOSE.SHOW);
            await orderSheet.Load(cmd, companyID, sheetID);
            SheetSale saleSheet = new SheetSale(LOAD_PURPOSE.SHOW);
            if(sheetType=="X"||sheetType == "T")
            {
                saleSheet = orderSheet.ToSaleSheet(operKey);
            }
            SQLQueue QQ = new SQLQueue(cmd);
            //var sql = $"select sub_id,sub_name from info_pay_way where company_id = {companyID} order by payway_index;";
            //var sql = $"select sub_id,sub_name,payway_type from info_pay_way where company_id = {companyID} and is_order is not true and payway_type in ('QT','YS') order by payway_index;";
            var sql = @$"
            SELECT * FROM
            (
               
    select s.sub_id,sub_name,sub_type as payway_type,order_index as payway_index,'false' is_ks,qrcode_uri,q.pay_channel_id, pc.channel_name as pay_channel_name
    from cw_subject s
    left join info_pay_qrcode q on s.company_id = q.company_id and s.sub_id = q.sub_id
    left join pay_channel pc on pc.channel_id = q.pay_channel_id
    where s.company_id = {companyID} and s.is_order is not true and (sub_type in ('YS') or (sub_type='ZC' and for_pay) or (sub_type='QT' and coalesce(for_pay,true))) and coalesce(s.status, '1') = '1'
    union 
    select sub_id,sub_name,sub_type as payway_type,order_index as payway_index,'true' is_ks,'',null,''
    from company_setting c
    left join cw_subject s on c.company_id = s.company_id and c.setting->>'feeOutSubForKS' = s.sub_id::text where c.company_id = {companyID} and c.setting->>'feeOutSubForKS' is not null 

            ) t
            where (sub_id::text IN (
                        SELECT 
                            json_array_elements_text(avail_pay_ways) AS individual_value 
                        FROM 
                            info_operator 
                        WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
                        )
                    OR
                    (   SELECT 
                            COUNT(*) 
                        FROM 
                            info_operator 
                        WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
                )
            order by case payway_type when 'QT' then 0 when 'YS' then 1 else 2 end, payway_index;
                        ";

            QQ.Enqueue("subInfo", sql);
            sql = $@"select brief_id,brief_text from info_sheet_detail_brief where company_id = {companyID} and sheet_type = 'X';";
            QQ.Enqueue("briefInfo", sql);
              
            sql = $@"select oper_id as senders_id,oper_name as senders_name  from info_operator where company_id = {companyID} and is_sender  and COALESCE(status,'1')='1' order by oper_name";
            QQ.Enqueue("sendersInfo", sql);
            sql = @$"SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index";
            QQ.Enqueue("attr_options", sql);
            List<ExpandoObject> payways = null;
            List<ExpandoObject> brief = null;
            List<ExpandoObject> senders = null;
            List<ExpandoObject> attrOptions = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "subInfo")
                {
                    payways = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "briefInfo")
                {
                    brief = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "sendersInfo")
                {
                    senders = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "attr_options")
                {
                    attrOptions = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            dynamic sheet = orderSheet;
            if (sheetType == "X" || sheetType == "T")
            {
                saleSheet.sheet_id = saleSheet.order_sheet_id;
                saleSheet.sheet_no = saleSheet.order_sheet_no;
                sheet= saleSheet;
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet, payways, brief, senders, attrOptions });
        }
        /*
        [HttpGet]
        public async Task<JsonResult> GetItemStockQtys(string operKey, string itemIds, string branch_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql = $"select item_id,COALESCE(stock_qty,0) stock_qty,COALESCE(sell_pend_qty,0) sell_pend_qty, COALESCE(stock_qty,0)-COALESCE(sell_pend_qty,0) usable_stock_qty from stock where company_id = {companyID} and branch_id={branch_id} and item_id in ({itemIds});";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("stock_qtys", sql);
            List<ExpandoObject> stock_qtys = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "stock_qtys")
                {
                    stock_qtys = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, stock_qtys });
        }
        */
        [HttpGet]
        public async Task<JsonResult> GetItemStockQtys(string operKey, string itemIds, string branch_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql = $"select item_id,COALESCE(stock_qty,0) stock_qty,COALESCE(sell_pend_qty,0) sell_pend_qty, COALESCE(stock_qty,0)-COALESCE(sell_pend_qty,0) usable_stock_qty from stock where company_id = {companyID} and branch_id={branch_id} and item_id in ({itemIds});";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("stock_qtys", sql);
            List<ExpandoObject> stock_qtys = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "stock_qtys")
                {
                    stock_qtys = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, stock_qtys });
        }
        [HttpPost]
        public async Task<JsonResult> NoRedOrderSheetAfterPrint(string operKey, [FromBody] dynamic data)
        {
            string result = "OK";
            string msg = "";
            string sql = "";
            string sheet_id = data.sheetID;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            sql = $@"select sheet_print_count from sheet_status_order where company_id={companyID} and sheet_id={sheet_id}";
            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (rec != null && rec.sheet_print_count != "" && int.Parse(rec.sheet_print_count) > 0)
            {
                result = "false";
                msg = "单据已打印，无法红冲/冲改";
            }
            return new JsonResult(new { result, msg });
        }
        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic dSheet)
        {
            SheetSaleOrder sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);

            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetSaleOrder>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Submit:" + msg + sSheet);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "保存失败,请联系技术支持";
               
                return new JsonResult(new { result = "Error", msg });
            }
             
            sheet.Init();
            Security.GetInfoFromOperKey(sheet.OperKey, out string companyID, out string operID);

            if (sheet.appendixPhotos != null)
            {
                List<string> appendixBase64s = new List<string>();

                foreach (string appendixPhoto in sheet.appendixPhotos)
                {
                    appendixBase64s.Add(appendixPhoto);
                }
                sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());

            }
            if (sheet.displayGiveProofs != null)
            {
                string sheetType = "XD";
                string supcust_id = sheet.supcust_id;
                dynamic displayGiveProofs = sheet.displayGiveProofs;
                string subType  = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSenderSubType.SubTypeKey;
                foreach (dynamic displayGiveProofItem in displayGiveProofs)
                {
                    string disp_sheet_id = displayGiveProofItem.disp_sheet_id;
                    string uploadsMainPath = $@"{companyID}_{sheetType}_{subType}_{operID}_{supcust_id}_{disp_sheet_id}";
                    var workContent = displayGiveProofItem.work_content;
                    string work_content = JsonConvert.SerializeObject(workContent);
                    string work_content_result = await ActionsTemplateUtils.HandleActionTemplate(work_content, uploadsMainPath, _httpClientFactory);
                    dynamic work_content_obj = JsonConvert.DeserializeObject(work_content_result);
                    displayGiveProofItem.work_content = work_content_obj;
                }
                sheet.display_give_proofs =  JsonConvert.SerializeObject(sheet.displayGiveProofs);
            }

             msg = await sheet.Save(cmd);

            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, currentTime, sheet.make_time });
        }
        /// <summary>
        /// 提交销售订单
        /// </summary>
        /// <param name="sheet">
        /// {"operKey":"wcAqiAdqGYG39sTafoxzNuV7gjl0d-zEX5Q5vIEsZ4CJBL8L71cPvCkNmSBpbvSukmnIwUZFvIg~","sheettype":"XD","sheet_no":"","sheet_id":"","supcust_id":"1","branch_id":"1","branch_name":"主仓库", 
        /// "happen_time":"","make_brief":"","total_amount":"72","now_disc_amount":"0","payway1_id":"1","payway1_name":"","payway1_amount":"72","payway2_id":"","payway2_name":"","payway2_amount":"0",
        /// "left_amount":"0", "maker_id":"","maker_name":"","make_time":"","approver_id":"", "approver_name":"","approve_time":"","now_pay_amount":72,"paid_amount":72,"shop_id":1, 
        /// "SheetRows":[ {"item_id":"6","item_name":"你好6","unit_no":"箱","real_price":"9","quantity":"1","unit_factor":"8","sub_amount":"72.00","remark":"7"}, 
        /// {"item_id":"6","item_name":"你好6","unit_no":"瓶","real_price":"9","quantity":"1","unit_factor":"8","sub_amount":"9","remark":"7"}]}</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            SheetSaleOrder sheet = null;
            //  dSheet.appendixPhotos = this.ProcessAppendixPicsRetDBStr(dSheet.appendixPhotos,dS);
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetSaleOrder>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetSave.Submit:" + msg + sSheet);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet,Token.CompanyID);
                msg = "审核失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            
            sheet.Init();
            Security.GetInfoFromOperKey(sheet.OperKey, out string companyID, out string operID);

            if (sheet.bReview)
            {
                sheet.reviewer_id = sheet.OperID;
                sheet.review_time = CPubVars.GetDateText(DateTime.Now);
            }

            if (sheet.appendixPhotos != null)
            {
                List<string> appendixBase64s = new List<string>();

                foreach (string appendixPhoto in sheet.appendixPhotos)
                {
                    appendixBase64s.Add(appendixPhoto);
                }
                sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());

            }
            if (sheet.displayGiveProofs != null)
            {
                string sheetType = "XD";
                string supcust_id = sheet.supcust_id;
                dynamic displayGiveProofs = sheet.displayGiveProofs;
                string subType  = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSenderSubType.SubTypeKey;
                foreach (dynamic displayGiveProofItem in displayGiveProofs)
                {
                    string disp_sheet_id = displayGiveProofItem.disp_sheet_id;
                    string uploadsMainPath = $@"{companyID}_{sheetType}_{subType}_{operID}_{supcust_id}_{disp_sheet_id}";
                    var workContent = displayGiveProofItem.work_content;
                    string work_content = JsonConvert.SerializeObject(workContent);
                    string work_content_result = await ActionsTemplateUtils.HandleActionTemplate(work_content, uploadsMainPath, _httpClientFactory);
                    dynamic work_content_obj = JsonConvert.DeserializeObject(work_content_result);
                    displayGiveProofItem.work_content = work_content_obj;
                }
                sheet.display_give_proofs =  JsonConvert.SerializeObject(sheet.displayGiveProofs);
            }
            msg = await sheet.SaveAndApprove(cmd);
            // 创建消息
            if (msg == "" && sheet.displayGiveProofs != null)
            {
                dynamic displayGiveProofs = sheet.displayGiveProofs;
                string subType = "";
                string subTypeName = "";
                string sheetType = sheet.SheetType;
                string order_sheet_id = sheet.order_sheet_id;
                if (sheetType.Equals("XD"))
                {
                    subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSellerSubType.SubTypeKey;
                    subTypeName = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSellerSubType.SubTypeName;
                } else if (sheetType.Equals("X") && (!string.IsNullOrEmpty(order_sheet_id)))
                {
                    subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSenderSubType.SubTypeKey;
                    subTypeName = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayFdSenderSubType.SubTypeName;
                } else if (sheetType.Equals("X") && (string.IsNullOrEmpty(order_sheet_id)))
                {
                    subType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayCxGiveSubType.SubTypeKey;
                    subTypeName = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageSubType.DisplayCxGiveSubType.SubTypeName;
                }
                foreach (dynamic displayGiveProofItem in displayGiveProofs)
                {
                    // 多个单据下创建消息
                    bool needReview = displayGiveProofItem.need_review;
                    if (needReview)
                    {
                        string supName = sheet.sup_name;
                        string sheetNo = sheet.sheet_no;
                        string sheetId = sheet.sheet_id;
                        string dispSheetNo = displayGiveProofItem.disp_sheet_no;
                        string dispSheetId = displayGiveProofItem.disp_sheet_id;
                        await MessageCreateServices.CreateMessageService(new
                        {
                            operKey = sheet.OperKey,
                            createrId = operID,
                            msgClass = MessageType.ClassType.Todo,
                            msgType = MessageType.DisplayMessageType.DisplayReviewMessageType.MessageType,
                            msgSubType = subType,
                            receiverId = "",
                            msgTitle = @$"{supName} 陈列单据 {dispSheetNo} 进行了{subTypeName}，请尽快复核。来自单据{sheetNo}",
                            sheetID = sheetId,
                            sheetType,
                            dispSheetId
                        }, cmd);
                    }
                }
            }
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no,currentTime,sheet.approve_time});
        }

        /// <summary>
        /// 用户在小程序支付后,将订单的支付方式修改为登记为银行支付的支付方式。
        /// 需要在客户端手动获取到已支付和服务端接收到支付回调时都调用一次,以确保修改生效。
        /// </summary>
        [HttpPost]
        public async Task<CallResult> AlterSheetPaywayToBank([FromBody] dynamic data)
        {
            string operKey = data.operKey; 
            string sheetId = data.sheetId;
            string channelId = data.channelId;
            Security.GetInfoFromOperKey(operKey, out string companyId);
            int.TryParse(channelId, out int payChannel);

            var res = await AlterSheetPaywayToBankStatic(cmd, companyId, sheetId, payChannel);
            return res;
        }
        public static async Task<CallResult> AlterSheetPaywayToBankStatic(CMySbCommand cmd,
            string companyId, string sheetId, int payChannel)
        {
            var sheet = new SheetSaleOrder();
            await sheet.Load(cmd, companyId, sheetId);

            var res = await AlterSheetPaywayToBankStatic_v2(cmd, sheet, companyId, sheetId, payChannel);
            return res;
        }
        public static async Task<CallResult> AlterSheetPaywayToBankStatic_v2(CMySbCommand cmd,
            SheetSaleOrder sheetLoaded, // v2改动：允许直接传入Load后的单据来避免重复加载
            string companyId, string sheetId, int payChannel)
        {
            var _sql_queue = new SQLQueue(cmd);
            var _available_payways = new List<ExpandoObject>();
            var sql = $@"
                SELECT
                    s.sub_id, s.sub_name, s.sub_type payway_type, p.pay_channel_id 
                FROM
                    cw_subject s
                    left join info_pay_qrcode p on p.sub_id = s.sub_id
                WHERE 
                    s.company_id = {companyId} 
                    and p.pay_channel_id = {payChannel}
                    and s.sub_type in ('QT','YS')
                    and coalesce(is_order,false) is not true;
            ";
            _sql_queue.Enqueue("pay_payways", sql);
            try
            {
                var _dr = await _sql_queue.ExecuteReaderAsync();
                while (_sql_queue.Count > 0)
                {
                    var sqlName = _sql_queue.Dequeue();
                    if (sqlName == "pay_payways")
                        _available_payways = CDbDealer.GetRecordsFromDr(_dr, false);
                }
                _dr.Close();
                _sql_queue.Clear();
            }
            catch (Exception ex)
            {
                NLogger.Error("[AlterSheetPaywayToBankStatic] 数据库操作失败，语句为" + sql);
                NLogger.Error(ex.ToString());
                return new CallResult("Error", ex.Message);
            }

            if (_available_payways is null || _available_payways.Count == 0)
                return new CallResult("Failed", "商家没有设置登记为银行支付的支付方式");

            var valid_payway_id = string.Empty;
            foreach (dynamic valid_payway in _available_payways)
            {
                valid_payway_id = valid_payway?.sub_id ?? "";
                if (valid_payway_id.IsValid()) break;
            }
            if (valid_payway_id.IsInvalid())
                return new CallResult("Error", "绑定的支付方式损坏");

            if (sheetLoaded.payway1_id == valid_payway_id || sheetLoaded.payway2_id == valid_payway_id)
                return new CallResult("OK", "此订单已经登记成功");

            string target_payway = "payway1";
            if (sheetLoaded.payway2_amount > 0)
                target_payway = "payway2";

            var msg = string.Empty;
            try
            {
                sql = $@"
                    UPDATE 
                        sheet_sale_order_main 
                    SET
                        {target_payway}_id = '{valid_payway_id}'
                    WHERE
                        company_id = {companyId}
                        and sheet_id = {sheetId};
                ";
                cmd.CommandText = sql;
                cmd.company_id = companyId;
                await cmd.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                NLogger.Error("[AlterSheetPaywayToBankStatic] 数据库操作失败，语句为" + sql);
                NLogger.Error(ex.ToString());
                msg = ex.Message;
            }
            string result = msg.Length > 0 ? "Error" : "OK";
            return new CallResult(result, msg);
        }

        /// <summary>
        /// 提交销售订单
        /// </summary>
        /// <param name="sheet">
        /// {"operKey":"wcAqiAdqGYG39sTafoxzNuV7gjl0d-zEX5Q5vIEsZ4CJBL8L71cPvCkNmSBpbvSukmnIwUZFvIg~","sheettype":"XD","sheet_no":"","sheet_id":"","supcust_id":"1","branch_id":"1","branch_name":"主仓库", 
        /// "happen_time":"","make_brief":"","total_amount":"72","now_disc_amount":"0","payway1_id":"1","payway1_name":"","payway1_amount":"72","payway2_id":"","payway2_name":"","payway2_amount":"0",
        /// "left_amount":"0", "maker_id":"","maker_name":"","make_time":"","approver_id":"", "approver_name":"","approve_time":"","now_pay_amount":72,"paid_amount":72,"shop_id":1, 
        /// "SheetRows":[ {"item_id":"6","item_name":"你好6","unit_no":"箱","real_price":"9","quantity":"1","unit_factor":"8","sub_amount":"72.00","remark":"7"}, 
        /// {"item_id":"6","item_name":"你好6","unit_no":"瓶","real_price":"9","quantity":"1","unit_factor":"8","sub_amount":"9","remark":"7"}]}</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Review([FromBody] SheetSaleOrder sheet)
        {
            var currentTime = DateTime.Now.ToText();
            sheet.Init();

            if (sheet.appendixPhotos != null)
            {
                List<string> appendixBase64s = new List<string>();

                foreach (string appendixPhoto in sheet.appendixPhotos)
                {
                    appendixBase64s.Add(appendixPhoto);
                }
                sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());

            }
            string msg = await sheet.SaveAndApprove(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, currentTime, sheet.approve_time });
        }

        public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64, string companyID)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, companyID);
            return result;
        }


        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string result = "OK"; string msg = null;
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            try
            {
                var currentTime = DateTime.Now.ToText();
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                SheetSaleOrder sheet = new SheetSaleOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);
                msg = await sheet.Red(cmd, companyID, sheetID, operID,"");
                if (msg != "") result = "Error";
                if (result == "OK")
                {
                    //var billRefundResult = await PayBillController.TryRefundAssociatedBills(cmd, _httpClientFactory, companyID, sheetID, sheet.SheetType);
                    var returnRedPacketsResult = await RedPacketController.ReturnSheetRedPackets(cmd, sheetID, sheet.SheetType, companyID, sheet.supcust_id);
                }
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }
        }



        /// <summary>
        /// 商品档案列表----返回商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="searchStr">商品名，助记码，商品编号，商品条码 模糊查询</param>
        /// <param name="brandID">品牌ID查询</param>
        /// <param name="classID">分类ID查询</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="branchID">仓库名 （1）</param>
        /// <returns>商品详情{bstock--大单位库存，bunit--大单位名称，bfactor--大单位换算，bpprice--大单位批发价，blprice--大单位零售价 }，总条数</returns>


        [HttpGet]
        public async Task<JsonResult> GetItemList(string operKey, string searchStr, string brandID, string classID, int pageSize, int startRow,string branchID)
        { 
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $" where ip.company_id = {companyID} ";
            if (searchStr != null) condi += $"and (ip.item_name ilike '%{searchStr}%' or ip.py_str ilike '%{searchStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%' or ip.barcode like '%{searchStr}%') ";
            if (brandID != null) condi += $"and ip.item_brand = {brandID} ";
            if (classID != null) condi += $"and ip.other_class like '%/{classID}/%' ";
            if (branchID != null) condi += $"and stock.branch_id = {branchID} ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
SELECT ip.item_id,ip.item_name,floor(stock.stock_qty/(t.b->>'f1')::numeric) bStock,(t.b->>'f2') as bUnit,ip.batch_level,
    yj_get_unit_qty('b', stock.stock_qty, bFactor::NUMERIC, mFactor::NUMERIC, false) bstock,
	yj_get_unit_qty('m', stock.stock_qty, bFactor::NUMERIC, mFactor::NUMERIC, false) mstock,
	yj_get_unit_qty('s', stock.stock_qty, bFactor::NUMERIC, mFactor::NUMERIC, false) sstock,
    yj_get_bms_qty(stock.stock_qty,bunit,bFactor::float4,mUnit,mFactor::float4,sunit) as stock_qty_unit,
    (t.s->>'f2') as sUnit,(t.s->>'f1') as sFactor,(t.m->>'f1') as mFactor,(t.b->>'f1') as bFactor,(t.s->>'f3') as spPrice,(t.s->>'f4') as slPrice,(t.m->>'f3') as mpPrice,(t.m->>'f4') as mlPrice,(t.b->>'f3') as bpPrice,(t.b->>'f4') as blPrice
FROM info_item_prop as ip 
LEFT JOIN 
(select item_id,s,m,b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,retail_price)) as json from info_item_multi_unit where company_id = {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
        as errr(item_id int, s jsonb,m jsonb, b jsonb) ) t
on ip.item_id=t.item_id
LEFT JOIN stock on t.item_id = stock.item_id
LEFT JOIN (select * from info_item_class where company_id = {companyID} ) as ic on ip.item_class = ic.class_id  {condi} limit {pageSize} offset {startRow}";
            QQ.Enqueue("data", sql);
            sql = $@"SELECT COUNT(ip.item_id) as itemCount FROM info_item_prop as ip  LEFT JOIN stock on ip.item_id = stock.item_id {condi} ";
            QQ.Enqueue("count", sql);
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data,itemCount });
        }
         
 
         /// <summary>
        /// 删除未审核单据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetSaleOrder sheet = new SheetSaleOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            CallResult pay_bill_refund_response = new CallResult("OK", "未成功删除,操作取消");
            CallResult return_redpackets_result = new CallResult("OK", "未成功删除,操作取消");
            if (result == "OK")
            {
                //pay_bill_refund_response = await PayBillController.TryRefundAssociatedBills(cmd, _httpClientFactory,
                //    companyID, sheet_id, sheet.SheetType);
                return_redpackets_result = await RedPacketController.ReturnSheetRedPackets(cmd,
                    sheet_id, sheet.SheetType, companyID, sheet.supcust_id);
            }
            return Json(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        [HttpPost]
        public async Task<JsonResult> Reject([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            //var msg = await SaleOrderSheetService.RejectOrder(cmd, companyID, sheet_id);
            var msg = await SaleOrderSheetService.RejectOrder_new(cmd, operKey, companyID, sheet_id);
            var result = msg == "" ? "OK" : "Error";
            return Json(new { result, msg, sheet_id });
        }
        [HttpPost]
        public async Task<JsonResult> Recover([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            var msg = await SaleOrderSheetService.RecoverOrder(cmd, companyID, sheet_id);
            var result = msg == "" ? "OK" : "Error";
            return Json(new { result, msg, sheet_id });
        }
        [HttpPost]
        public async Task<IActionResult> AppendBrief([FromBody] dynamic data)
        {
            string sheetID = data.sheetID;
            string newBrief = data.newBrief;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetSaleOrder sheet = new SheetSaleOrder(SHEET_RETURN.EMPTY, LOAD_PURPOSE.APPROVE);

            string msg = await sheet.AppendBrief(cmd, companyID, sheetID, newBrief);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return Json(new { result, msg });
        }
    }
}
