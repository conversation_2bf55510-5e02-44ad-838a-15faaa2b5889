﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ArtisanManage.Services;


namespace ArtisanManage.Pages.BaseInfo
{
    public class SuppliersViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
 
        public SuppliersViewModel(CMySbCommand cmd) : base(Services.MenuId.infoSupplier)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"sup_name",new DataItem(){Title="检索",FldArea="divHead",SqlFld="sup_name,s.py_str,s.mobile,s.supcust_no",PlaceHolder="名称/简拼/手机/编号", QueryOnChange=true,CompareOperator="ilike"}},
               // {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称/助记码",UseJQWidgets=false, SqlFld="sup_name,py_str,mobile",ButtonUsage="list",QueryOnChange=true,CompareOperator="ilike"}},
                {"status",new DataItem(){Title = "状态",FldArea="divHead", LabelInDB = false, Value = "normal", Label = "正常",ButtonUsage = "list", QueryOnChange = true,  CompareOperator = "=", NullEqualValue = "normal",

                     Source = @"[{v:'normal',l:'正常',condition:""(s.status = '1' or s.status is null)""},
                               {v:'stop',l:'停用',condition:""s.status = '0' ""},
                               {v:'all',l:'所有',condition:""true""}]"
                }},
              //  {"forAll",new DataItem(){Title="forAll",FldArea="divHead", LabelInDB = false,ForQuery = false,CompareOperator="=",Hidden=true} },
          
            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     IdColumn="i",TableName="info_supcust",
                     ShowContextMenu=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"i",new DataItem(){Title="编号",SqlFld="supcust_id",Width="80",Hidden=true,HideOnLoad = true}},
                        {"supcust_flag",new DataItem(){Title="类型标志",Hidden = true,HideOnLoad=true}},
                       {"sup_name",new DataItem(){Title="名称",Width="150",Linkable=true}},
                       {"boss_name",new DataItem(){Title="联系人",Width="100"}},
                       {"mobile",new DataItem(){Title="联系电话",Width="100"}},
                       {"sup_addr",new DataItem(){Title="地址",Width="150"}},
                       {"status",new DataItem(){Title="状态",Width="50",SqlFld="(case WHEN status='0' THEN '停用' ELSE '正常' END)" }},
                       {"pay_info",new DataItem(){Title="支付信息",Width="100"}},
                     
                       
                     },
                     QueryFromSQL="from info_supcust s  where company_id=~COMPANY_ID and supcust_flag in ('S','CS')" ,QueryOrderSQL="order by supcust_id desc"
                  }
                } 
            }; 
        }
        //public bool ForAll = false;
        public async Task OnGet(string forSelect)
        {
            //ForAll = forAll == "true";
            ForSelect = forSelect == "1";
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
           // this.SQLVariable1 = "and supcust_flag in ('S','CS')";
            //string forAll = DataItems["forAll"].Value;
            //if (forAll=="true") this.SQLVariable1 = $" ";
        }
        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            cmd.CommandText = $"select company_id from sheet_buy_main where supcust_id in ({rowIDs}) and company_id={company_id} limit 1";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该供应商已销售过,无法删除";

            cmd.CommandText = $"select company_id from sheet_buy_main where supcust_id in ({rowIDs}) and company_id={company_id} limit 1";
            ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该供应商已做过订单,无法删除";

            cmd.CommandText = $"select company_id from sheet_get_arrears_main where supcust_id in ({rowIDs}) and company_id={company_id} limit 1";
            ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该供应商已做过付款单,无法删除";

            cmd.CommandText = $"select company_id from sheet_prepay where supcust_id in ({rowIDs}) and company_id={company_id} limit 1";
            ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该供应商做过预付款单,无法删除";

            cmd.CommandText = $"select company_id from sheet_sale_main where supcust_id in ({rowIDs}) and company_id={company_id} limit 1";
            ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该供应商已作为客户做过销售单,无法删除";

            cmd.CommandText = $"select company_id from sheet_sale_order_main where supcust_id in ({rowIDs}) and company_id={company_id} limit 1";
            ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该供应商已作为客户做过销售订单,无法删除";

            return "";
        }
    }



    [Route("api/[controller]/[action]")]
    public class SuppliersViewController : QueryController
    { 
        public SuppliersViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SuppliersViewModel model = new SuppliersViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            SuppliersViewModel model = new SuppliersViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            SuppliersViewModel model = new SuppliersViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd, "info_supcust");// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            SuppliersViewModel model = new SuppliersViewModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
