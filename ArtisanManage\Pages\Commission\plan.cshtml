﻿@page
@model ArtisanManage.Pages.CommisionPlansViewModel
@{
    Layout = "_Info";
    var operKey = Request.Query["operKey"];
}
@section head{
    <link rel="stylesheet" href="~/css/site.css?v=@Html.Raw(Model.Version)" />
    <link href="~/css/component.css?v=@Html.Raw(Model.Version)" rel="stylesheet" />
    <link href="~/NiceWidgets/NiceWidgets.css?v=@Html.Raw(Model.Version)" rel="stylesheet" />
    <script src="~/NiceWidgets/NiceWidgets.js?v=@Html.Raw(Model.Version)"></script>
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/MiniJsLib/jquery.dialog.js"></script>
    <link rel="stylesheet" href="~/MiniJsLib/jquery.dialog.css?v=@Html.Raw(Model.Version)">
        @*<script src="~/MiniJsLib/jquery.dialog.js?v=@Html.Raw(Model.Version)"></script>*@
    <link href="~/MiniJsLib/MiniJsLibPC.css" rel="stylesheet" />

    <style>
        body {
            overflow: hidden;
        }

        tfoot {
            display: none;
        }

        .dialog_container {
            width: 600px;
            height: 500px;
        }

        .row {
            flex-flow: row wrap;
            align-items: baseline;
        }

        .name a {
            color: #4488ee;
        }

        .dataIndex > svg {
            display: none
        }

        tr {
            height: 30px;
        }

            tr:hover {
                background: #fff1f1;
            }

                tr:hover > .dataIndex > svg {
                    display: inline-block
                }

        .dataIndex > p {
            display: inline
        }

        th {
            height: 30px;
        }

        tr:hover > .dataIndex > p {
            display: none;
        }

        .top {
            grid-area: top;
        }

        .row > li {
            list-style: none;
            border: 0px solid#ddd;
            line-height: 18px;
            background: #fdd;
            border-radius: 4px;
            margin-right: 0.25rem;
            margin-left: 10px;
            padding: 3px;
            padding-left: 5px;
            padding-right: 3px;
        }

            .row > li > .btnRemove {
                margin-left: 1px;
                color: #999;
                cursor: pointer;
            }

        body {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        * {
            font-family: 微软雅黑
        }
    </style>
}

<header>
    <button class="magic-button addPlan" style="width:100px">添加提成方案</button>
</header>
<main id="grid"></main>

@section Script
    {
    <script type="text/template" id="dialogTmpl">
        <table>
            <thead>
                <tr><th><a select="all">全选</a>  <a select="invert">反选</a></th><th>员工姓名</th></tr>
            </thead>
            <tbody class="employeeList"></tbody>
        </table>
    </script>
    <script type="text/template" id="employees">
        <tr>
            <td><input type="checkbox" style="width: 20px; height: 20px;" /></td>
            <td>{name}</td>
        </tr>
    </script>

    <script type="text/javascript">
        app = new App('@operKey', {
            setup() {
                this.addServices(['Table', 'Dialog', 'Validator', 'ProcessBar']);
            },
            ".": {
                click: {
                    addPlan() {
                        parent.newTabPage('添加提成方案', '/commission/index', window);
                    },
                }
            },
        });

        app.useDialog({
            id: 'dialog',
            title: '匹配员工',
            class: 'lg',
            '[]': {
                click: {
                    select(e) {
                        $(e.delegateTarget).find('input').each(function () { this.checked = e.prop === 'invert' ? !this.checked : e.prop === 'all' });
                    }
                }
            },
            okEvent(dialog) {
                var maps = [], names = [];
                dialog.$el.find('input:checked').each(function () {
                    var idx = this.getKey('rowIndex') - 1;
                    var employee = dialog.employees[idx];
                    maps.push({
                        strategy: dialog.plan.id,
                        employee: employee.userId,
                    });
                    names.push(employee.userName);
                });
                $.post({ url: '../Api/Commission/SaveStrategyMaps?operKey=' + app.token.operKey, contentType: 'text/json' }, JSON.stringify(maps)).then(result => {
                    if (['OK', 'Warn'].includes(result.result)) {
                        result.position.forEach((i, j) => {
                            dialog.plan.employeeMaps.push({
                                mapId: result.ids[j],
                                employeeId: maps[i].employee,
                                employeeName: names[i]
                            })
                        });
                        app.grid.table.updateCell(dialog.cell, dialog.plan);
                        dialog.Hide();
                    }
                    bw.toast(result.msg);
                });
            }
        });

        var pageSize = parseInt($('main').height() / 40);
        $(function () {//页面初始化
            app.useGrid({
                buildTable(table) {
                    table.child('thead', {
                        cols: ['序号', '提成方案']
                    }).child('tbody', {
                        size: pageSize,
                        cols: ['dataIndex', 'name'],
                        render: {
                            dataIndex(v, i) {
                                return `<p>${++i}</p>
                                        <svg width="15" height="15" style="cursor:pointer;margin-left:0px;fill:#999;" class="btnDelete">
                                            <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#remove'  />
                                        </svg>

                                    `;
                            },
                            name(data) {
                                return `<a class='btnEdit'>${data.name}</a>`
                            },
                            //type(data) {
                            //    return { seller: '业务员', sender: '送货员', sendhelper: '协送员' }[data.type];
                            //},
                            //employees(data) {
                            //    var cards = data.employeeMaps.map((x, i) => `<li id=${x.employeeId}>${x.employeeName}<span class='btnRemove' id=${x.mapId} title=${i}>
                            //                <svg width="15" height="15" style="cursor:pointer;">
                            //                    <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#close'  />
                            //                </svg>
                            //                </span></li>`)
                            //    return `<ul class='row'>${cards.join('')}

                            //        <svg width="18" height="18" style="cursor:pointer;margin-left:10px;margin-top:4px;"  fill="#999" class='addEmployees'>
                            //            <use xlink:href = '/images/images.svg?v=@Html.Raw(Model.Version)#thinAdd'  />
                            //        </svg>
                            //            </ul>`
                            //}
                        }
                    });
                },
                ".": {
                    click: {
                        btnDelete() {//删除方案
                            var plan = this.view('gridDatas');
                            $.post({ url: '../Api/Commission/CheckBeforeDeletePlan?id=' + plan.id, contentType: 'text/json' }, JSON.stringify(app.token)).then(msg => {
                                console.log(msg)
                                if (msg === "") {
                                    jConfirm(`确认要删除${plan.name}吗?`, () => {
                                        var idx = this.getKey();
                                        var rowIdx = this.getKey('rowIndex');
                                        $.post({ url: '../Api/Commission/DeletePlan?id=' + plan.id, contentType: 'text/json' }, JSON.stringify(app.token)).then(result => {
                                            if (result.result == 'OK') {
                                                app.grid.table.dataRows.splice(idx, 1);
                                                app.grid.table.refresh(rowIdx, idx);
                                                location.reload();
                                            } else {
                                                bw.toast(result.msg);
                                            }
                                        })
                                    })
                                } else {
                                    bw.toast(msg);
                                }
                            })
                        },
                        btnEdit() {
                            var plan = this.view('gridDatas');
                            parent.newTabPage('设置提成方案', `/commission/index?id=${plan.id}`, window);
                        },
                        //addEmployees() {添加员工
                        //    app.dialog.plan = this.view('gridDatas');  
                        //    app.dialog.employees = app.store("employees").filter(employee => !app.dialog.plan.employeeMaps.some(m => m.employeeId === employee.userId));
                        //    app.link('.employeeList', '#employees', content => {
                        //        var trs = app.dialog.employees.map(employee => {
                        //            return content.replace('{name}', employee.userName);
                        //        });
                        //        return trs.join('');
                        //    });
                        //    app.dialog.cell = this.closestParent('td');
                        //    app.dialog.$el.css('display', 'flex');
                        //},
                        btnRemove() {
                            var id = this.id, idx = this.title;
                            var plan = this.view('gridDatas');
                            var cell = this.closestParent('td');
                            jConfirm("确认要移除吗？", () => {
                                $.post({ url: '../Api/Commission/DeletePlanMap?id=' + id, contentType: 'text/json' }, JSON.stringify(app.token)).then(result => {
                                    if (result.result == 'OK') {
                                        plan.employeeMaps.splice(idx, 1);
                                        app.grid.table.updateCell(cell, plan);
                                    } else {
                                        bw.toast(result.msg);
                                    }
                                });
                            })
                        }
                    },
                }
            });

            $.get('../Api/Commission/GetPlanMaps', app.token).then(result => {
                if (result.result === 'OK') {
                    app.storeGrid(result.data);
                } else {
                    bw.toast(result.msg);
                }
            });
            $.get('../Api/Commission/GetEmployees', app.token).then(result => {
                if (result.result === 'OK') {
                    app.store("employees", result.data);
                } else {
                    bw.toast(result.msg);
                }
            });
        });
    </script>
}