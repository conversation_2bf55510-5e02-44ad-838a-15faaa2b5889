@page
@model ArtisanManage.Pages.BaseInfo.RolesViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "RoleEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = rs.data.record;
                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "role_name",rs.data.record.role_name);
                }
                $("#popItem").jqxWindow('close');
            };
        });


    	var newCount = 1;

        function btnAddItem_click(e) {
            if (!$('#popNewRole').length) {
                var popDlg = $(`
<div id="popNewRole" style="display: block;">
    <div style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">新建角色</span></div>
    <div style="overflow:hidden;padding:50px;padding-top:30px;">
        <div style="display:flex;margin-bottom:10px;">
            <div style="line-height:25px;">角色名称</div> <div id="role_name" style="width:200px;height:25px;margin-left:10px;"> </div>
        </div>
        <div style="display:flex;margin-bottom:10px;">
            <div style="line-height:25px;">角色模板</div> <div id="templ_id" style="width:200px;height:25px;margin-left:10px;"> </div>
        </div>
        <div style="text-align:center;margin-top:40px;">
           <button onclick="btnNewRoleOK_click()" style="margin-right:40px;">确定</button>
           <button onclick="$('#popNewRole').jqxWindow('close')">取消</button>
        </div>
    </div>
</div>
`)

                ajaxGet('/api/RolesView/GetAvailRoleTemplates',
                    {
                        operKey: g_operKey
                    }
                ).then(res => {

                    $('body').append(popDlg)
                     $('#role_name').jqxInput({
                       dropDownWidth: 200, dropDownHeight: 250, buttonUsage: 'none', checkboxes: false, borderShape: 'bottomLine', showHeader: false, displayMember: 'l',
                        valueMember: 'v', datafields: [{ datafield: 'l', text: '', width: 120 }], searchFields: ['l', 'z'], placeHolder: '', maxRecords: 300, url: '', source: null
                    });
                    let templArr = res.data
                    $('#templ_id').jqxInput({
                        dropDownWidth: 150, dropDownHeight: 80, buttonUsage: 'list', checkboxes: false, borderShape: 'bottomLine', showHeader: false, displayMember: 'templ_name',
                        valueMember: 'templ_id', datafields: [{ datafield: 'templ_name', text: '', width: 120 }], searchFields: ['templ_name'], placeHolder: '', maxRecords: 300, url: '', source: templArr
                    });
                    $('#templ_id').on('optionSelected', function (a, b) {
                        var templ = $('#templ_id').val()
                        templ = templArr.find(t => t.templ_id == templ.value)
                        if (templ && templ.fee_discount!=='' && parseFloat(templ.fee_discount)<1) {
                            $('#role_name').jqxInput('val', templ.templ_name)
                            $('#role_name').jqxInput({ disabled: true })
                        }
                        else {
                             $('#role_name').jqxInput({disabled:false})
                        }
                    })
                    $("#popNewRole").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 320, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

                   
                   openNewRoleDlg()
                }).catch(err => {

                })
            }
            else {
               openNewRoleDlg()
            } 
             

        }
        function openNewRoleDlg() {
            $('#role_name').jqxInput({disabled:false})
            $('#role_name').jqxInput('val', '')
            $('#templ_id').jqxInput('val', '')
            $('#popNewRole').jqxWindow('open')
        }
        function btnNewRoleOK_click() {
            var role_name = $('#role_name').val()
            var templ_id = $('#templ_id').val()
             if (!role_name) {
                bw.toast('请输入角色名称')
                return
            }
            if (!templ_id || !templ_id.value) {
                bw.toast('请选择一个角色模板')
                return
            }
            templ_id=templ_id.value
            window.parent.newTabPage('新建角色', `/BaseInfo/RoleEdit?role_name=${role_name}&templ_id=${templ_id}`, window);
            $('#popNewRole').jqxWindow('close')
        }

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                    console.log(args);
                    if (args.datafield == "role_name") {
                       // var id = args.row.bounddata.role_id;
                        onGridRowEdit(args.rowindex)
                      //  window.parent.newTabPage('编辑角色','/BaseInfo/RoleEdit?role_id=' + id, window);
 
                    }
                });
                $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
                });
              //  $("#popItem").jqxWindow({isModal: true, modalOpacity: 0.3, height: '800px', width: '800px', theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                QueryData();
            });
        function WaitToReQueryData(tm) {
            setTimeout(function () {
                QueryData();
            }, tm);
        }

        function onGridRowEdit(rowIndex) {
            var role_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, "role_id");
           // var id = args.row.bounddata.role_id;
            window.parent.newTabPage('编辑角色','/BaseInfo/RoleEdit?role_id=' + role_id, window);           
        }
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        #searchString {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
    </style>
</head>

<body>
 
    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;">
        <div><input id="searchString" class="margin" placeholder="请输入名称" /><button onclick="QueryData()" class="margin">查询</button></div>
        <div><input type="button" onclick="btnAddItem_click()" value="添加角色" class="margin" style="width:100px;" /></div>
    </div>
   
    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:calc(100% - 80px);margin-bottom:10px;"></div>
       
    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">角色信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>