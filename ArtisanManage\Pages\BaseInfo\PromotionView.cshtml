﻿@page
@model ArtisanManage.Pages.BaseInfo.PromotionView

@{
  Layout = null;
}

<!DOCTYPE html>
<html style="width: 100%;height: 100%;overflow: hidden">
<head>
  <title>促销活动</title>
  <script src="~/js/Vue.js"></script>
  <script>var commonVersion=@Model.Version</script>
  <script src="~/js/commonConfig.js?v=@Model.Version"></script>
  <style>
    iframe {
      width:100%;
      height:100%;
      margin-left:0;
      margin-right:0;
      border: 0;
      background-color: transparent;
    }
  </style>
</head>
<body style="width: 100%;height: 100%">
<div id="pages" style="width: 100%;height: 100%">
    <iframe :src="iframeSrc"></iframe>
</div>
<script>
    var g_operKey = '@Model.OperKey';
</script>
<script>
    const app = new Vue({
        el: "#pages",
        data() {
            return {
                iframeSrc: pageRouter.promotionView.router + g_operKey
            }
        },
    })
</script>
</body>
</html>