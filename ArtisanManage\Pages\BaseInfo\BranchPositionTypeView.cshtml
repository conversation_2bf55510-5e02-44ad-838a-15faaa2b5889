﻿@page
@model ArtisanManage.Pages.BaseInfo.BranchPositionTypeViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "BranchPositionTypeEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()

                    }
                    else {
                        var now_arr = new Array
                        var row = {
                            type_id: rs.data.record.type_id,
                            type_name: rs.data.record.type_name,
                            inner_type_name: rs.data.record.inner_type_name,
                            inner_type: rs.data.record.inner_type,
                            py_str: rs.data.record.py_str,
                        }
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;


                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    QueryData();

                }
                $("#popItem").jqxWindow('close');
            };
        });


        var newCount = 1;

        function btnAddItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="BranchPositionTypeEdit?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }
        var itemSource = {};
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                    console.log(event);
                if (args.originalEvent.button == 2) return
                if (args.datafield == "type_name") {
                    onGridRowEdit(args.rowindex)
                }
            });
            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });
            $("#popItem").jqxWindow({isModal: true, modalOpacity: 0.3, height: '400px', width: '500px', theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            QueryData();
        });

        function onGridRowEdit(rowIndex) {
            var type_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, "type_id");
            var inner_type = $('#gridItems').jqxGrid('getcellvalue', rowIndex, "inner_type");
            switch (inner_type) {
                case '正常':
                    inner_type_id = 1
                    break;
                case '临期':
                    inner_type_id = 0
                    break;
                case '过期':
                    inner_type_id = -1
                    break;
            }
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="BranchPositionTypeEdit?operKey=' + g_operKey + '&type_id=' + type_id + '&inner_type=' + inner_type_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        #searchString {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
    </style>
</head>

<body>

    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;">
        <div><input id="searchString" class="margin" placeholder="请输入名称" /><button onclick="QueryData()" class="margin">查询</button></div>
        <div><input type="button" onclick="btnAddItem_click()" value="添加库位类别" class="margin" style="width:100px;" /></div>
    </div>

    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:calc(100% - 80px);margin-bottom:10px;"></div>

    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">库位类型</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>