using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc; 
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using ArtisanManage.Services;
using HuaWeiObsController;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ClassEditModel :  PageFormModel
    {  
        public string m_sheet_no { get; set; }
       
        public ClassEditModel(CMySbCommand cmd) : base(MenuId.infoItem)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"class_id",new DataItem(){Title="编号"}},
                {"class_name",new DataItem(){Title="类名"}},
                {"mother_id",new DataItem(){Title="父类", LabelFld="mother_name",CtrlType="jqxDropDownTree",MumSelectable=true,DropDownHeight="200",TreePathFld="mother_class_path",TreePathFromDb=false,Necessary=true,
                MaxRecords="1000", 
                   SqlForOptions=CommonTool.selectClasses} },
                {"brand_id",new DataItem(){Title="品牌",LabelFld="brand_name",ButtonUsage="list",DropDownHeight="100",
                    SqlForOptions = CommonTool.selectBrands}},
                {"order_index",new DataItem(){Title="顺序号"}},              
                {"cls_status",new DataItem(){Title="状态",LabelFld="LabelFld",DropDownHeight="50",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}},
                {"general_class",new DataItem(){Title="统计类"}},
                {"batch_level",new DataItem(){Title="产期/批次",LabelFld="l",FldArea="divHead",LabelInDB=false,Value="",Label="", DropDownHeight="80",ButtonUsage="list", Source = "[{v:1,l:'开启生产日期'},{v:2,l:'开启批次'}]"}},
                {
                    "class_image",new DataItem()
                    { 
                        Title="图标",
                       
                        FuncDealMe = classImage =>
                        {
                            return HuaWeiObs.BucketLinkHref+"uploads/"+classImage;
                        }
                    }
                }
            }; 
            m_idFld = "class_id"; m_nameFld = "class_name"; 
            m_tableName = "info_item_class";
            AllowSameName = true;
            m_selectFromSQL = "from info_item_class left join (select class_id as my_mother_id,class_name as mother_name from info_item_class where company_id=~COMPANY_ID) tb_mother on info_item_class.mother_id=tb_mother.my_mother_id left join info_item_brand on info_item_class.brand_id=info_item_brand.brand_id where info_item_class.company_id=~COMPANY_ID and class_id=~ID";
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            string sql = $"select setting from company_setting where company_id = {company_id};";
            dynamic jsonSetting = await CDbDealer.Get1RecordFromSQLAsync(sql,cmd);
            if(jsonSetting != null)
            {
                dynamic setting = JsonConvert.DeserializeObject<dynamic>(jsonSetting.setting);
                string batch_type = "";
                if (setting != null && setting.batchType!=null) {
                    batch_type = setting.batchType;
                }
                if (batch_type=="")
                {
                    DataItems["batch_level"].Hidden = true;
                }
                else
                {
                    DataItems["batch_level"].Hidden = false;
                }
            }
            else
            {
                DataItems["batch_level"].Hidden = true;
            }
        }
        public async Task OnGet()
        {
           
            await InitGet(cmd);
        }  
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class ClassEditController : YjController
    { 
        CMySbCommand cmd;
        private readonly IHttpClientFactory _httpClientFactory;
        public ClassEditController(CMySbCommand cmd,  IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd;
            _httpClientFactory = httpClientFactory;
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic value)
        { 
            ClassEditModel model = new ClassEditModel(cmd);

            string mother_class_path = value.mother_class_path;
            string class_id = value.class_id;
            string class_name = value.class_name;
            string mother_id = value.mother_id;
            string cls_status = value.cls_status;
            if (class_id != "")
            {
                if(mother_class_path.Contains("/" + class_id + "/"))
                {
                    return new JsonResult(new { result = "Error", msg = "父类不能是该类自身或子类" });
                }
            }
            if (cls_status == "0")
            {
                dynamic item = await CDbDealer.Get1RecordFromSQLAsync($"select item_id from info_item_prop where company_id = {Token.CompanyID} and item_class={class_id} and (status = '1' or status is null) ", cmd);
                if (item != null)
                {
                    return new JsonResult(new { result = "Error", msg = "该类下存在正常商品，不能停用" });
                }
            }
            string sql = $"select class_name,class_id,mother_id from info_item_class where company_id= {Token.CompanyID}  and class_name='{class_name}' and mother_id={mother_id};";
            dynamic cls = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (cls != null && cls.class_id != class_id)
            {
                return new JsonResult(new { result = "Error", msg = "已存在同名类别，请勿重复添加" });
            }
            string class_image = value.class_image;
            //var ddd=model.DataItems["item_id"].Value;
            if (class_image.IsValid() && class_image.StartsWith("data:")) 
            {
                using (var image = HuaWeiObs.Base64ToImage(class_image))
                {
                    DateTime dt = DateTime.Now;
                    string mm = dt.ToString("yyyMM");
                    string ss = dt.ToString("yyyMMddHHmmss");
                    string path = $"class_image/{mm}/info_item_class_icon_{Token.CompanyID}_{class_id}_{ss}.png";
                    value.class_image = path;
                    await HuaWeiObs.Save(_httpClientFactory, image.Image, path);
                }
            }
            else if (class_image.IsValid() && class_image.StartsWith(HuaWeiObs.BucketLinkHref))
            {
                value.class_image = value.class_image.Replace(HuaWeiObs.BucketLinkHref, "");
            }

            var result = await model.SaveTable(cmd, value); 
            
            if (class_id != "" && mother_class_path!="")
            {
                var new_other_class = $"{mother_class_path}{class_id}/";
                //cmd.CommandText = $"UPDATE info_item_prop SET other_class='{new_other_class}' where company_id={Token.CompanyID} and item_class='{class_id}' and other_class<>'{new_other_class}'";
                 cmd.CommandText = $"UPDATE info_item_prop SET other_class='{new_other_class}'||split_part(other_class, '/{class_id}/', 2) where company_id={Token.CompanyID} and  other_class like '%/{class_id}/%'";
                await cmd.ExecuteNonQueryAsync();
            }
            return result;

            //var tt = Convert.ToString(value.uid); 
            //var rr = new { UserID = value.UserID, UserName = value.UserName };
            //return value;

            //return JsonObject<object> (new { UserID = value.UserID, UserName = value.UserName });
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName,string flds,string value, string availValues)
        {
            ClassEditModel model = new ClassEditModel(cmd); 
            string data=await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems ,dataItemName, flds,value,availValues);
            return data; 
        } 
    }
}