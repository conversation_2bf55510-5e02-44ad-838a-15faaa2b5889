
## 磁盘空间
centos 磁盘空间如果满了，打开页面会报错，APP打开也报错
### 查看文件夹内文件的大小
 du -sh * | sort -n

### 查看系统磁盘空间
 df -h


### 华为云挂载硬盘

  #### 1.华为云官网购买云硬盘，在网页上挂载到主机
#### 2.登录到主机上,执行fdisk -l可以看到/dev/vda  /dev/vdb 
#### 3.将vdb 分区格式化
   fdisk /dev/vdb 可以对磁盘进行分区操作，注意创建的是主分区，不要创建扩展分区，否则格式化会报错
   mkfs.ext4 /dev/vdb1


#### 4.挂载
   
    mount /dev/vdb /var/www/artisan/wwwroot/uploads
   这样挂载只是临时的，服务器一重启，就失去了挂载.
    blkid /dev/vdb  查看磁盘的uuid  /dev/vdb: UUID="c5839cf7-1757-4c78-8e63-d17c0cbb8274" TYPE="ext4" 
  
    vi /etc/fstab 编辑文件

![Fstab](MaintenancePic/fstab.png)  
把uuid加进去 ，保存，重启服务器后即可



  