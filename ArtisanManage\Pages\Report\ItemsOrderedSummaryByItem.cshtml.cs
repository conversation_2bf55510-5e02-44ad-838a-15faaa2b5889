﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NPOI.SS.Formula.Functions;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ItemsOrderedSummaryByItemModel : PageQueryModel
    {
        public string unionSql { get; set; } = "";

        public ItemsOrderedSummaryByItemModel(CMySbCommand cmd) : base(Services.MenuId.itemsOrderedSummaryByItem)
        {
            unionSql = @"(select m.supcust_id,case when ip.son_mum_item is null then d.item_id else ip.son_mum_item end item_id,m.sheet_id,sheet_no,sheet_type,round(sum(inout_flag*quantity*unit_factor)::numeric,2) quantity,m.happen_time,m.approve_time,order_sub_id,round((real_price/unit_factor)::numeric,4) real_price,d.unit_no,red_flag
                            from sheet_sale_main m
                            left join sheet_sale_detail d on d.sheet_id = m.sheet_id
                            LEFT JOIN info_item_prop ip on m.company_id = ip.company_id and ip.item_id=d.item_id
                            where m.company_id=~COMPANY_ID and m.approve_time is not null ~VAR_red_flag and order_sub_id is not null
                            group by m.supcust_id,case when ip.son_mum_item is null then d.item_id else ip.son_mum_item end,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,red_flag,d.order_sub_id,real_price,d.unit_factor,d.unit_no
                            UNION
                            select m.supcust_id,item_id,m.sheet_id,sheet_no,sheet_type,round(sum(case when m.red_flag ='2' then -1*quantity*unit_factor else quantity*unit_factor end)::numeric,2) quantity,m.happen_time,m.approve_time,prepay_sub_id order_sub_id, round((real_price/unit_factor)::numeric,4) real_price,d.unit_no,red_flag
                            from sheet_prepay m left join sheet_order_item_detail d  on d.sheet_id = m.sheet_id 
                            where m.company_id=~COMPANY_ID and m.approve_time is not null ~VAR_red_flag and sheet_type = 'DH'
                            group by m.supcust_id,item_id,m.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,prepay_sub_id,real_price,d.unit_factor,d.unit_no,red_flag
                            UNION
                            select c.supcust_id,c.item_id,c.sheet_id,sheet_no,sheet_type,round(sum((now_quantity-old_quantity)*unit_factor)::numeric,2) quantity,m.happen_time,m.approve_time,c.prepay_sub_id as order_sub_id,round((real_price/unit_factor)::numeric,4) real_price,c.unit_no,null red_flag
                            from items_ordered_change c left join sheet_item_ordered_adjust_main m on m.sheet_id = c.sheet_id left join info_item_multi_unit mu on mu.item_id = c.item_id and mu.unit_no = c.unit_no
                            where m.company_id=~COMPANY_ID and oper_type = 'change'
                            group by c.supcust_id,c.item_id,c.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,c.prepay_sub_id,real_price,unit_factor,c.unit_no
                            UNION
                            select c.supcust_id,c.item_id,c.sheet_id,sheet_no,sheet_type,round(sum((-1)*old_quantity*unit_factor)::numeric,2) quantity,m.happen_time,m.approve_time,c.prepay_sub_id as order_sub_id,round((real_price/unit_factor)::numeric,4) real_price,c.unit_no,null red_flag
                            from items_ordered_change c left join sheet_item_ordered_adjust_main m on m.sheet_id = c.sheet_id left join info_item_multi_unit mu on mu.item_id = c.item_id and mu.unit_no = c.unit_no
                            where m.company_id=~COMPANY_ID and oper_type = 'delete'
                            group by c.supcust_id,c.item_id,c.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,c.prepay_sub_id,real_price,unit_factor,c.unit_no
                            UNION
                            select c.supcust_id,c.item_id,c.sheet_id,sheet_no,sheet_type,round(sum(now_quantity*unit_factor)::numeric,2) quantity,m.happen_time,m.approve_time,c.prepay_sub_id as order_sub_id,round((real_price/unit_factor)::numeric,4) real_price,c.unit_no,null red_flag
                            from items_ordered_change c left join sheet_item_ordered_adjust_main m on m.sheet_id = c.sheet_id left join info_item_multi_unit mu on mu.item_id = c.item_id and mu.unit_no = c.unit_no
                            where m.company_id=~COMPANY_ID and oper_type = 'add'
                            group by c.supcust_id,c.item_id,c.sheet_id,sheet_no,sheet_type,m.happen_time,m.approve_time,c.prepay_sub_id,real_price,unit_factor,c.unit_no
                            
                            ) t
                        ";
            this.cmd = cmd;
            this.PageTitle = "定货变化明细";

            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},

                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="u.item_id",DropDownWidth="300",
                SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode   }},
                //{"sheet_type",new DataItem(){FldArea="divHead",Title="单据类型",LabelFld="sheet_type_name",ButtonUsage="list",Source = "[{v:'X',l:'销售单'},{v:'T',l:'退货单'},{v:'DH',l:'定货单'},{v:'DHTZ',l:'定货调整单'},{v:'',l:'所有'}]",CompareOperator="="}},
                {"order_sub_id",new DataItem(){FldArea="divHead",Title="定货款账户",LabelFld="sub_name",ButtonUsage="list",CompareOperator="=",Necessary=true,
                    SqlForOptions ="select -1 as v ,'未指定' as l , 'wzd' as z union select sub_id as v,sub_name as l,py_str as z from cw_subject where sub_type = 'YS' and is_order = true and COALESCE(status,'1')='1'"}},
                //{"order_sub_id",new DataItem(){FldArea="divHead",Title="定货款账户",LabelFld="sub_name",ButtonUsage="list",CompareOperator="=",Necessary=true,
                //    SqlForOptions ="select sub_id as v,sub_name as l from cw_subject where company_id=~COMPANY_ID and sub_type = 'YS' and is_order"}},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld = "u.supcust_id",Necessary=true,
                SqlForOptions=CommonTool.selectSupcust } },
                {"showRedSheet",new DataItem(){FldArea="divHead",Title="显示红冲单",CtrlType="jqxCheckBox",ForQuery=false,Value="false",AutoRemember=true}},
                {"timeChange",new DataItem(){FldArea="divHead",Title="",CtrlType="jqxCheckBox",Hidden= true,HideOnLoad = true,ForQuery=false,Value="true"}},

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_no",new DataItem(){Title="单据编号", Width="180",Linkable=true}},
                       {"sheet_id",new DataItem(){Title="单据", Width="180",Hidden=true}},
                       {"sheet_type1",new DataItem(){Title="类型",Hidden=true,SqlFld="sheet_type"} },
                       {"sheet_type",new DataItem(){Title="单据类型", Width="100",
                           SqlFld=@"(case when sheet_type='X' then '销售单' 
                                          when sheet_type='T' then '销售退货单' 
                                          when sheet_type = 'DH' then '定货单' 
                                          when sheet_type = 'DHTZ' then '定货调整单'
                                    end)"}},
                       {"red_flag", new DataItem(){Title="状态",  Width="55",Hidden = true,HideOnLoad = true}},
                       {"sheet_status", new DataItem(){Title="状态",  Width="55",SqlFld="(case when red_flag='2' then '红字单' when red_flag='1' then '已红冲'  END)"}},
                       {"supcust_id",new DataItem(){Title = "客户", SqlFld = "u.supcust_id" ,Necessary=true,Hidden=true} },
                       {"sup_name",new DataItem(){Title="客户", Width="180"}},
                       {"order_sub_id",new DataItem(){Title="定货款账户",Width = "180",Hidden = true,SqlFld = "u.order_sub_id" } },
                       {"happen_time",new DataItem(){Title="交易时间", Width="180"}},
                       {"approve_time",new DataItem(){Title="审核时间", Width="180"}},
                       {"item_id",new DataItem(){Title="商品",Width="",Hidden=true,SqlFld = "u.item_id" } },
                       {"item_name",new DataItem(){Title="商品名称", Width="180"}},
                       {"order_price",new DataItem(){Title="小单位定货价",Width="120" } },
                       {"current_quantity",new DataItem(){Title="开单前数量", Width="120",SqlFld="order_qty"}},
                       //{"unit_no",new DataItem(){Title="商品名称", Width="180",Hidden=true,SqlFld="mu.unit_no" } },
                       {"add_quantity",new DataItem(){Title="增加数量", Width="120",SqlFld="(select yj_get_bms_qty ((case when qty_change>0 then qty_change else 0 end)::float4,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no) )"}},
                       {"reduce_quantity",new DataItem(){Title="减少数量", Width="120",SqlFld="(select yj_get_bms_qty ((case when qty_change<0 then -1*qty_change else 0 end)::float4,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)) "}},
                       {"current_quantity1",new DataItem(){Title="开单后数量", Width="120",SqlFld="qty_change+order_qty"}},
                       {"change_qty",new DataItem(){Title="库存变化",Hidden=true,Width="120",SqlFld="qty_change"}},
                       {"b_unit_factor",new DataItem(){Title="大包装率",Hidden=true ,HideOnLoad = true} },
                       {"m_unit_factor",new DataItem(){Title="中包装率",Hidden=true ,HideOnLoad = true} },
                       {"s_unit_factor",new DataItem(){Title="小包装率",Hidden=true ,HideOnLoad = true} },
                       {"b_unit_no",new DataItem(){Title="大单位",Hidden=true,HideOnLoad = true } },
                       {"m_unit_no",new DataItem(){Title="中单位",Hidden=true,HideOnLoad = true } },
                       {"s_unit_no",new DataItem(){Title="小单位",Hidden=true ,HideOnLoad = true} },
                     },

                     QueryFromSQL=$@"from  (select t.*,coalesce(round((b.order_price/b.unit_factor)::numeric,4),t.real_price) order_price,round(b.quantity*b.unit_factor::numeric,2) order_qty,round(t.quantity::numeric,2) qty_change from  
                                        {unionSql}
                                        left join items_ordered_balance b on b.item_id = t.item_id and t.order_sub_id = b.prepay_sub_id and t.supcust_id = b.supcust_id and b.order_price = t.real_price and b.unit_no = t.unit_no order by happen_time,sheet_id ) u 
                                    left join  info_item_prop i on i.item_id = u.item_id
                                    left join info_supcust p on p.supcust_id = u.supcust_id
                                    left join (select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                                                as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu on mu.item_id = i.item_id
						            where i.company_id=~COMPANY_ID",
                     //QueryGroupBySQL = " group by u.sheet_no,u.sheet_type,sup_name,happen_time,approve_time,item_name,inout_flag,unit_factor,u.unit_no",
                      QueryOrderSQL="order by approve_time"
                  }
                }
            };
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            string now = CPubVars.GetDateText(DateTime.Now.Date) + " 00:00";
            
            var startDay = DataItems["startDay"].Value;
            string timeChange = DataItems["timeChange"].Value;
            var order_sub_id = CPubVars.RequestV(Request, "order_sub_id");
            var flag = true;
            if (timeChange.IsValid() && timeChange.ToLower() == "false") flag = false;
            if (order_sub_id.IsValid())
            {
                if (flag)
                {
                    string sql = $"select min(happen_time) v from sheet_prepay p left join cw_subject pw on pw.sub_id = p.prepay_sub_id where p.company_id = {company_id} and is_order and prepay_sub_id = {order_sub_id}";
                    dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    if (record != null) DataItems["startDay"].Value = record.v;
                }
                
            }
            else DataItems["startDay"].Value = now;

        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            if (DataItems["showRedSheet"].Value.ToLower() == "true")
            {
                SQLVariables["red_flag"] = "  ";
            }
            else
            {
                SQLVariables["red_flag"] = "and red_flag is  null";
            }
        }

        public async Task OnGet()
        {
            await InitGet(cmd);

        }
    }



    [Route("api/[controller]/[action]")]
    public class ItemsOrderedSummaryByItemController : QueryController
    {
        public ItemsOrderedSummaryByItemController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ItemsOrderedSummaryByItemModel model = new ItemsOrderedSummaryByItemModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            ItemsOrderedSummaryByItemModel model = new ItemsOrderedSummaryByItemModel(cmd);
            dynamic records = await model.GetRecordFromQuerySQL(Request, cmd);
            var rows = (Dictionary<int, Dictionary<string, string>>)records.Value.rows;

            //int startRowIndex = -1;
            //object o = Request.Query["startRow"]; if (o != null) startRowIndex = Convert.ToInt32(o.ToString());
            //int lastRowIndex = startRowIndex + rows.Count;
            //var lastRow = rows[lastRowIndex];
            if (rows.Count > 0)
            {
                float b_unit_factor = 0;
                if (rows[0].GetValueOrDefault("b_unit_factor").IsValid()) b_unit_factor = Convert.ToSingle(rows[0].GetValueOrDefault("b_unit_factor"));
                float m_unit_factor = 0;
                if (rows[0].GetValueOrDefault("m_unit_factor").IsValid()) m_unit_factor = Convert.ToSingle(rows[0].GetValueOrDefault("m_unit_factor"));
                float s_unit_factor = 0;
                if (rows[0].GetValueOrDefault("s_unit_factor").IsValid()) s_unit_factor = Convert.ToSingle(rows[0].GetValueOrDefault("s_unit_factor"));
                var b_unit_no = rows[0].GetValueOrDefault("b_unit_no");
                var m_unit_no = rows[0].GetValueOrDefault("m_unit_no");
                var s_unit_no = rows[0].GetValueOrDefault("s_unit_no");

                //var item_id = rows[0].GetValueOrDefault("item_id");
                var itemIds = new List<string>();
                for (int i = 0; i < rows.Count; i++)
                {
                    var item_id = rows[i].GetValueOrDefault("item_id");
                    if (!itemIds.Contains(item_id))
                    {
                        itemIds.Add(item_id);
                    }

                }
                var items_id = string.Join(',', itemIds);
                var supcust_id = CPubVars.RequestV(Request, "supcust_id");
                var order_sub_id = CPubVars.RequestV(Request, "order_sub_id");
                string endDay = CPubVars.RequestV(Request, "endDay");
                if(endDay.IsInvalid())endDay = CPubVars.GetDateText(DateTime.Now.Date) + " 23:59";
                var operKey = CPubVars.RequestV(Request, "operKey");
                Security.GetInfoFromOperKey(operKey, out string companyID);
                var lastRow = rows[rows.Keys.Last<int>()];
                //string sql = @$"SELECT item_id , order_price,sum(left_qty) left_qty FROM (select item_id,round((order_price/unit_factor)::numeric,4) order_price,round(COALESCE(b.quantity*b.unit_factor,0)::numeric,2) left_qty
                //                  from  items_ordered_balance b where item_id in ({items_id}) and prepay_sub_id = {order_sub_id} and supcust_id = {supcust_id} )b group by item_id,order_price ";
                string sql = @$"SELECT ordered.item_id , ordered.order_price, (COALESCE( ordered.left_qty,0) +COALESCE( sale.xs_add_qty,0) - COALESCE( sale.xs_reduce_qty,0) +COALESCE( dh.dh_add_qty,0) - COALESCE( dh.dh_reduce_qty,0) +COALESCE( change.adj_add_qty,0) - COALESCE( change.adj_reduce_qty,0)) as left_qty 	
FROM (
	SELECT item_id , order_price,sum(left_qty) left_qty
	FROM (
		select item_id,round((order_price/unit_factor)::numeric,4) order_price,round(COALESCE(b.quantity*b.unit_factor,0)::numeric,2) left_qty
		from  items_ordered_balance b 
		where company_id={companyID} and item_id in ({items_id}) and prepay_sub_id = {order_sub_id} and supcust_id = {supcust_id} 
	) b 
	group by item_id,order_price 							
) ordered 							
LEFT JOIN(	
	SELECT d.item_id AS item_id,round((d.real_price/d.unit_factor)::numeric,4) real_price,sum(CASE WHEN inout_flag*quantity < 0  THEN (inout_flag * quantity * unit_factor * (-1))::numeric ELSE 0 END ) AS xs_add_qty,sum(CASE WHEN inout_flag*quantity > 0 THEN (inout_flag * quantity * unit_factor)::numeric ELSE 0 END ) AS xs_reduce_qty 
	FROM sheet_sale_detail d
	LEFT JOIN sheet_sale_main M ON d.sheet_id = M.sheet_id and m.company_id={companyID}
	WHERE d.company_id={companyID}  and	red_flag IS NULL and d.happen_time>'{endDay}' and m.happen_time>'{endDay}' and d.trade_type ='DH'	AND M.approve_time IS NOT NULL  and  d.order_sub_id is not null    and d.item_id in ({items_id})  and d.order_sub_id = {order_sub_id} and m.supcust_id = {supcust_id}
	GROUP BY	d.item_id ,round((d.real_price/d.unit_factor)::numeric,4)
) sale on sale.item_id = ordered.item_id and abs(sale.real_price- ordered.order_price)<0.001 											
LEFT JOIN (				
	SELECT  d.item_id as item_id ,round((d.real_price/d.unit_factor)::numeric,4) real_price,sum(case when quantity < 0 then ((-1)*quantity*unit_factor)::numeric else 0 end ) as dh_add_qty,sum(case when quantity>0 then (quantity*unit_factor)::numeric else 0 end ) as dh_reduce_qty
	FROM sheet_order_item_detail d
	LEFT JOIN sheet_prepay p on p.company_id={companyID} and d.sheet_id = p.sheet_id 
	where d.company_id = {companyID}  and red_flag is null and d.happen_time>'{endDay}' and p.approve_time is not null and  d.item_id in ({items_id}) and prepay_sub_id = {order_sub_id} and p.supcust_id = {supcust_id}
	GROUP BY  d.item_id ,round((d.real_price/d.unit_factor)::numeric,4)
) dh on dh.item_id = ordered.item_id and  abs(dh.real_price- ordered.order_price)<0.001
LEFT JOIN
(
	select c.item_id,round((real_price/unit_factor)::numeric,4) real_price,sum(case when c.oper_type = 'delete' then (c.old_quantity*mu.unit_factor)::numeric when c.oper_type ='change' and c.old_quantity>c.now_quantity then ((now_quantity-old_quantity)*unit_factor)::numeric else 0 end )::numeric adj_add_qty,sum(case when c.oper_type = 'add' then (c.now_quantity*mu.unit_factor)::numeric when c.oper_type ='change' and c.now_quantity>c.old_quantity then ((now_quantity-old_quantity)*unit_factor)::numeric else 0 end )::numeric adj_reduce_qty
    from items_ordered_change c 
    left join sheet_item_ordered_adjust_main m on c.company_id= m.company_id   and   m.sheet_id = c.sheet_id 
    left join info_item_multi_unit mu on  c.company_id= mu.company_id and mu.item_id = c.item_id and mu.unit_no = c.unit_no
    where m.company_id={companyID} and   M.happen_time >= '{endDay}' and c.item_id in ({items_id}) and c.prepay_sub_id = {order_sub_id}  and m.supcust_id = {supcust_id}
    group by c.item_id,round((real_price/unit_factor)::numeric,4)
) change on change.item_id = ordered.item_id and abs(dh.real_price- ordered.order_price)<0.001 ";
                sql = sql.Replace("~COMPANY_ID", companyID, StringComparison.OrdinalIgnoreCase);
                Dictionary<string, float> dicCurrentQty = new Dictionary<string, float>();

                var lstQuantities = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                foreach (dynamic qty in lstQuantities)
                {
                    var key = qty.item_id + "_" + qty.order_price;
                    dicCurrentQty[key] = Convert.ToSingle(qty.left_qty);
                }
                var reversedKeys = rows.Keys.Reverse<int>();
                string before_quantity_unit_no = "";
                string after_quantity_unit_no = "";
                foreach (var k in reversedKeys)
                {

                    var row = rows[k];
                    b_unit_factor = Convert.ToSingle(row["b_unit_factor"] == "" ? "0" : row["b_unit_factor"]);
                    m_unit_factor = Convert.ToSingle(row["m_unit_factor"] == "" ? "0" : row["m_unit_factor"]);
                    b_unit_no = row["b_unit_no"];
                    m_unit_no = row["m_unit_no"];
                    s_unit_no = row["s_unit_no"];
                    var key = row["item_id"] + "_" + row["order_price"];
                    if (!dicCurrentQty.ContainsKey(key)) dicCurrentQty.Add(key, 0);
                    var currentQty = dicCurrentQty[key];
                    row.Remove("current_quantity1");
                    before_quantity_unit_no = CommonTool.units_from_s_to_bms(currentQty, b_unit_factor, m_unit_factor, s_unit_factor, b_unit_no, m_unit_no, s_unit_no);
                    row.Add("current_quantity1", before_quantity_unit_no);
                    float change_qty = Convert.ToSingle(row["change_qty"]);
                    currentQty -= change_qty;
                    dicCurrentQty[key] = currentQty;
                    row.Remove("current_quantity");
                    after_quantity_unit_no = CommonTool.units_from_s_to_bms(currentQty, b_unit_factor, m_unit_factor, s_unit_factor, b_unit_no, m_unit_no, s_unit_no);
                    row.Add("current_quantity", after_quantity_unit_no);
                }

            }

            // Dictionary<string, string> lastRow = rows.Last();

            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            ItemsOrderedSummaryByItemModel model = new ItemsOrderedSummaryByItemModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
