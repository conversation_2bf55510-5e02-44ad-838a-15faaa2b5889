@page
@model ArtisanManage.Pages.BaseInfo.ClassEditModel
@{
    Layout = null;
} 
<!DOCTYPE html> 
<html>
<head id="Head1" runat="server">
    <base target="_self">
    <title></title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
             @Html.Raw(Model.m_showFormScript)
              var  obsPrefix = "@Html.Raw(HuaWeiObsController.HuaWeiObs.BucketLinkHref)";
             let classIcon= $("#class_image").val()
             if (classIcon !== '') {
                $("#class_image").attr('src',obsPrefix + "/" + classIcon);
                $("#class_image").attr('height',"80");
             }
             updateUploadButtonVisibility()
             
             
            // 点击图片触发文件选择
            $("#class_image").click(function() {
                // 如果图片已有src，则触发input的点击事件
                if ($("#class_image").attr('src')) {
                    $("#upload-image").click();
                }
            });
             $("#upload-image").on("change", function(e) {
                 var file = $("#upload-image")[0].files[0]
                 var fr=new FileReader();
                fr.onload=function () {
                     $("#class_image").attr('src',this.result);
                     $("#class_image").attr('height',"80");
                    compressImage(this.result, (compressBase64Url) => {
                      $("#class_image").val(compressBase64Url)
                      updateUploadButtonVisibility()
                    })
                 };
                fr.readAsDataURL(file);
             })
             function compressImage(imageUrl, cb) {
                 // 创建 Image 对象
                 var img = new Image();
                 img.src = imageUrl;
                 img.onload = function() {
                     var _this = this;
                     // 获取 canvas 元素
                     var canvas = document.createElement('canvas');
                     canvas.id = 'canvas';
                     // 绘制图像到 canvas
                     canvas.width = img.width;
                     canvas.height = img.height;
                     var ctx = canvas.getContext("2d");
                     ctx.drawImage(_this, 0, 0, img.width, img.height);
                     // 使用 toDataURL 方法压缩图像
                     var dataUrl = canvas.toDataURL("image/jpeg", 0.5);
                     // 使用新的 Data URL 更新图像
                     cb(dataUrl)
                 }
             }
              // 函数：根据图片是否有src值，更新上传按钮的可见性
             function updateUploadButtonVisibility() {
                 if ($("#class_image").attr('src')) {
                     // 如果图片有src，则隐藏按钮
                     $(".upload-btn").hide();
                   
                 } else {
                     // 如果没有src，则显示按钮
                     $(".upload-btn").show();
                     $(".class_image").hide();
                 }
             }
        });
    </script>
    <style>
        .upload-wrapper:hover{
            cursor: pointer;
        }
    </style>
</head>
<body>     
        <div id="divHead" class="headtail" style="width:500px;">
            <div style="display:none;"><div><label>编号</label></div> <div><div id="class_id"></div></div></div>
            <div><div><label>名称</label></div> <div><div id="class_name"></div></div></div>
            <div><div><label>父类</label></div> <div><div id="mother_id"></div></div></div>
            <div><div><label>品牌</label></div> <div><div id="brand_id"></div></div></div>
            <div><div><label>序号</label></div> <div><div id="order_index"></div></div></div>
            <div><div><label>状态</label></div> <div><div id="cls_status"></div></div></div> 
            <div><div><label>统计类</label></div> <div><div id="general_class"></div></div></div> 
            <div style="float:none;height:0px; clear:both;"></div>
            <div class="upload-wrapper" style="height: 80px">
                <div style="display: flex; align-items: center"><label>图标</label></div>
                <div style="margin: 10px;height: 80px;">
                    <img id="class_image" style="width: 80px; height: 80px"/>
                    <label style="position: relative; " for="upload-image" class="upload-btn">上传图标</label>
                    <input id="upload-image" style="display: none;" type="file" name="file" accept="image/png,image/bmp,image/jpeg"/>
                </div>
            </div> 
           
        </div>

    
    <div style="text-align:center;margin-top:50px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;" >保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
@*$  <-- <label asp-for="Movie.ReleaseDate" class="control-label"></label>
                <input asp-for="Movie.ReleaseDate" class="form-control" />
                <span asp-validation-for="Movie.ReleaseDate" class="text-danger"></span>
        -->*@
    <!--   Html.DisplayFor(modelItem => item.Title)
        -->
</body>
</html>
