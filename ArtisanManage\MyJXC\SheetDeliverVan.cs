﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowDeliverVan:SheetRowBase
    {
        public SheetRowDeliverVan()
        {

        }
        [SaveToDB(false)][FromFld(false)] public override int inout_flag { get; set; } = 0;
        [SaveToDB(false)][FromFld(false)] public override int row_index { get; set; } = 0;
        [SaveToDB(false)][FromFld(false)] public override string remark { get => base.remark; set => base.remark = value; }

        [FromFld]public override string sheet_id { get; set; }
        [SaveToDB][FromFld] public string op_id { get; set; }

        [FromFld("om.op_no")]public string op_no { get; set; }
        [SaveToDB][FromFld] public string op_type { get; set; }

        [SaveToDB][FromFld] public string op_time { get; set; }

        [SaveToDB][FromFld] public string senders_id { get; set; }

        [SaveToDB][FromFld] public string senders_name { get; set; }

        [SaveToDB][FromFld] public string from_branch_id { get; set; }

        [FromFld("fb.branch_name")] public string from_branch_name { get; set; }

        [SaveToDB][FromFld] public string to_van_id { get; set; }

        [FromFld("tb.branch_name")] public string to_van_name { get; set; }

        [SaveToDB][FromFld] public string sale_order_sheet_ids { get; set; } = "";





    }
    public class SheetDeliverVan : SheetBase<SheetRowDeliverVan>
    {
        [SaveToDB][IDField][FromFld] public override string sheet_id { get; set; }
        [SaveToDB][FromFld] public override string sheet_no { get; set; } = "";
        [SaveToDB][FromFld] public string oper_id { get; set; }
        [FromFld("o.oper_name")] public string oper_name { get; set; }
        [SaveToDB(false)][FromFld(false)] public override string red_sheet_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string maker_id { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_time { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string make_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string approve_brief { get; set; } = "";
        [SaveToDB(false)][FromFld(false)] public override string submit_time { get; set; } = "";

        [SaveToDB(false)][FromFld(false)] public override string maker_name { get; set; } = "";

        [FromFld("b.branch_name")] public string van_name { get; set; }
        [SaveToDB][FromFld] public string van_id { get; set; }

        [SaveToDB][FromFld] public override string approver_id { get; set; } = "";
        [FromFld("ap.oper_name")] public override string approver_name { get; set; } = "";
        [SaveToDB][FromFld] public override string approve_time { get; set; } = "";

        public string sale_order_sheet_ids { get; set; } ="";

        public SheetDeliverVan(LOAD_PURPOSE loadPurpose) : base("sheet_delivery_main", "sheet_delivery_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_DELIVER_VAN;
            MainLeftJoin = @" 	
                        LEFT JOIN info_operator o on o.company_id = t.company_id and o.oper_id = t.oper_id
	                    LEFT JOIN info_branch b on b.company_id = t.company_id and b.branch_id = t.van_id
	                    LEFT JOIN info_operator ap on ap.company_id = t.company_id and ap.oper_id = t.approver_id
 ";
            DetailLeftJoin = $@"
	LEFT JOIN op_move_to_van_main om on om.company_id = t.company_id and om.op_id = t.op_id
	LEFT JOIN info_branch fb on fb.company_id = t.company_id and fb.branch_id = t.from_branch_id
	LEFT JOIN info_branch tb on tb.company_id = t.company_id and tb.branch_id = t.to_van_id
    LEFT JOIN (select op_id,string_agg(sale_order_sheet_id::text,',') sale_order_sheet_ids from op_move_to_van_detail where company_id = ~company_id group by op_id) od on od.op_id=om.op_id";
        }


        //protected override void InitForGetSaveSQL()
        //{
        //    base.InitForGetSaveSQL();

        //}
        public override string GetOtherSaveSQL()
        {
            string sqlDetail = "";
            return sqlDetail;
        }
        public override string GetSaveSQL(bool bForApproveOrRed, out string err)
        {
            err = "";
            string sql = base.GetSaveSQL(bForApproveOrRed, out err);
            return sql;
        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            string sql = "";
            string orderSheetIDs = "";
            string opIDs = "";
            foreach (var row in SheetRows)
            {
                if (orderSheetIDs != "") orderSheetIDs += ",";
                orderSheetIDs += row.sale_order_sheet_ids;
                if (opIDs != "") opIDs += ",";
                opIDs += row.op_id;
            }
            if (sale_order_sheet_ids == "") sale_order_sheet_ids = orderSheetIDs;
            sql = $@"SELECT sso.sheet_id,sm.sheet_no ,sso.sale_sheet_id,od.retreat_id FROM sheet_status_order sso 
LEFT JOIN sheet_sale_order_main sm on sm.company_id = sso.company_id and sm.sheet_id = sso.sheet_id
LEFT JOIN op_move_to_van_detail od on od.company_id = sso.company_id and sso.sheet_id = od.sale_order_sheet_id and od.op_id in ({opIDs})
WHERE sso.company_id = {company_id}  and sso.sheet_id in ({sale_order_sheet_ids});";
            QQ.Enqueue("check_order_sheets", sql);
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);

            if(sqlName == "check_order_sheets")
            {
                dynamic orderSheets = CDbDealer.GetRecordsFromDr(dr, false);
                string order_sheet_nos = "";
                foreach (dynamic sht in orderSheets)
                {
                    if (sht.sale_sheet_id != "")
                    {
                        if (order_sheet_nos != "") order_sheet_nos += ",";
                        order_sheet_nos += sht.sheet_no;
                    }

                }
                if (order_sheet_nos != "") info.ErrMsg = "订单" + order_sheet_nos + "已转单";

            }

        }
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
            return "OK";
        }
        protected override async Task<CInfoForApproveBase> GetInfoForApprove(CMySbCommand cmd)
        {

            SQLQueue QQ = new SQLQueue(cmd);
            if (sheet_id != "")
            {
                if (!FIXING_ARREARS)
                {
                    string check_sql = $"select approve_time from {MainTable} where op_id={sheet_id} and company_id = {company_id}";
                    QQ.Enqueue("check_sheet", check_sql);
                }
            }

            GetInfoForApprove_SetQQ(QQ);
            string errMsg = "";
            if (QQ.Count > 0)
            {
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                try
                {
                    while (QQ.Count > 0)
                    {
                        string tbl = QQ.Dequeue();
                        if (tbl == "check_sheet")
                        {
                            dynamic checkSheet = CDbDealer.Get1RecordFromDr(dr, false);
                            if (checkSheet != null && checkSheet.approve_time != "")
                            {
                                errMsg = "单据已审核过,不能再次审核";
                                break;
                            }
                        }
                        else
                        {
                            GetInfoForApprove_ReadData(dr, tbl,false);
                            if (InfoForApprove != null && InfoForApprove.ErrMsg != "")
                            {
                                errMsg = InfoForApprove.ErrMsg;
                                break;
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    errMsg = "读取数据失败";
                    MyLogger.LogMsg($"in GetInfoForApprove. error:${e.Message}, ${e.StackTrace}", company_id, "approve");
                }
                QQ.Clear();
            }
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            InfoForApprove.ErrMsg = errMsg;
            return InfoForApprove;
        }
        protected override string GetApproveSQL(CInfoForApproveBase info)
        {
            string sql = "";
            if (sale_order_sheet_ids != "")
            {
                sql = $@"UPDATE sheet_status_order 
                        set order_status = 'fh'
                        WHERE company_id = {company_id} and sheet_id in ({sale_order_sheet_ids});";
            }
            return sql;
        }
  
        
        protected class CInfoForApprove : CInfoForApproveBase
        {
            public string ArrearBalance = "", PrepayBalance = "";
            public List<SheetRowInventory> SheetRows = null;
        }
		protected override async Task<string> BeforeRed(CMySbCommand cmd, string sheetID, string rederID, string redBrief, CInfoForApproveBase info)
		{
			string err = ""; 
            string orderSheetIDs = "";
            string opIDs = "";
            foreach (var row in SheetRows)
            {
                if (orderSheetIDs != "") orderSheetIDs += ",";
                orderSheetIDs += row.sale_order_sheet_ids;
                if (opIDs != "") opIDs += ",";
                opIDs += row.op_id;
            }
            if (sale_order_sheet_ids == "") sale_order_sheet_ids = orderSheetIDs;
            string checkSql = $@"SELECT sso.sheet_id,sm.sheet_no ,sso.sale_sheet_id,od.retreat_id FROM sheet_status_order sso 
LEFT JOIN sheet_sale_order_main sm on sm.company_id = sso.company_id and sm.sheet_id = sso.sheet_id
LEFT JOIN op_move_to_van_detail od on od.company_id = sso.company_id and sso.sheet_id = od.sale_order_sheet_id and od.op_id in ({opIDs})
WHERE sso.company_id = {this.company_id}  and sso.sheet_id in ({sale_order_sheet_ids});";
            dynamic orderSheets = await CDbDealer.GetRecordsFromSQLAsync(checkSql, cmd);
            string order_sheet_nos = "";
            orderSheetIDs = "";
            foreach (dynamic sht in orderSheets)
            {
                if(sht.sale_sheet_id != "")
                {
                    if (order_sheet_nos != "") order_sheet_nos += ",";
                    order_sheet_nos += sht.sheet_no;
                }
                if(sht.retreat_id == "")
                {
                    if (orderSheetIDs != "") orderSheetIDs += ",";
                    orderSheetIDs += sht.sheet_id;
                }

            }
            if(order_sheet_nos!="")err = "订单"+ order_sheet_nos + "已转单";
            if (err == "")
            {
                string redSql = $@"update sheet_delivery_main set red_flag='1' where company_id = {this.company_id} and sheet_id ={sheetID};";
                string orderSheetSql = "";
                if (orderSheetIDs != "")orderSheetSql = $@"update sheet_status_order set order_status = 'zc' where company_id = {this.company_id} and sheet_id in ({sale_order_sheet_ids}) ";
                cmd.CommandText = redSql + orderSheetSql;
                try
                {
                    await cmd.ExecuteNonQueryAsync();
                }
                catch (Exception e)
                {
                    err = e.Message;

                }
             
            }

            return err;
        }
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.van_id ;
            foreach (var row in SheetRows)
            {
                res += row.op_id + "_" + row.op_type;
            }
            return res;
        }
      
 

    }
}
