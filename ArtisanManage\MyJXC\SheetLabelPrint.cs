﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using System;

namespace ArtisanManage.MyJXC
{
    public class SheetRowLabelPrint : SheetRowBase
    {
        #region 从SheetRowItem继承
        /**
         * 为什么这么做：
         * 避免后续在SheetRowItem添加字段可能导致的本表报错
         */
        [SaveToDB][FromFld] public string item_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_no { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_images { get; set; } = "";
        [SaveToDB][FromFld] public string branch_id { get; set; } = "";
        [SaveToDB][FromFld] public string unit_no { get; set; } = "";
        [SaveToDB][FromFld] public decimal unit_factor { get; set; } = 1;
        [SaveToDB][FromFld] public decimal quantity { get; set; }


        public string qty_unit
        {
            get
            {
                return CPubVars.FormatMoney(quantity, 3) + unit_no;
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_barcode { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_barcode { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_barcode { get; set; }
        [FromFld("t.happen_time", LOAD_PURPOSE.SHOW)] public string happen_time { get; set; }
        public string happen_date
        {
            get
            {
                return CPubVars.GetDateTextNoTime(happen_time);
            }
        }
        public string smb_barcode
        {
            get
            {
                return s_barcode.IsValid() ? s_barcode : m_barcode.IsValid() ? m_barcode : b_barcode;
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_unit_no { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_factor { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_factor { get; set; }


        [FromFld(LOAD_PURPOSE.SHOW)] public string brand_name { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string class_name { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string classId { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string other_class { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_order_index { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string class_order_index { get; set; }

        public string b_qty_dot
        {
            get
            {
                if (!b_unit_factor.IsValid()) return "";
                string qty = CPubVars.FormatMoney(unit_factor * quantity / CPubVars.ToDecimal(b_unit_factor), 3);
                return qty;
            }
        }
        public string b_quantity_show
        {
            get
            {
                if (b_quantity == 0) return "";
                return b_quantity.ToString();
            }
        }

        public string s_quantity_show
        {
            get
            {
                if (s_quantity == 0) return "";
                return s_quantity.ToString();
            }
        }
        public decimal b_quantity
        {
            get
            {
                return SheetBase<SheetRowItem>.GetQtyOfUnit(unit_factor * quantity, "b", b_unit_factor, m_unit_factor);
            }
        }
        public decimal m_quantity
        {
            get
            {
                return SheetBase<SheetRowItem>.GetQtyOfUnit(unit_factor * quantity, "m", b_unit_factor, m_unit_factor);
            }
        }
        public decimal s_quantity
        {
            get
            {
                return SheetBase<SheetRowItem>.GetQtyOfUnit(unit_factor * quantity, "s", b_unit_factor, m_unit_factor);
            }
        }

        public string b_quantity_unit
        {
            get
            {
                return CPubVars.FormatMoney(b_quantity, 0) + b_unit_no;
            }
        }
        public string m_quantity_unit
        {
            get
            {
                return CPubVars.FormatMoney(m_quantity, 0) + m_unit_no;
            }
        }
        public string s_quantity_unit
        {
            get
            {
                return CPubVars.FormatMoney(s_quantity, 3) + s_unit_no;
            }
        }

        public string quantity_unit_conv
        {
            get
            {
                return SheetBase<SheetRowItem>.GetUnitQty(quantity * unit_factor, b_unit_no, m_unit_no, s_unit_no, b_unit_factor, m_unit_factor);
            }
        }
        // public string quantity_unit_conv { get; set;}

        public string s_sub_qty_unit
        {
            get
            {
                return s_sub_qty + this.s_unit_no;
            }
        }
        public string s_sub_qty
        {
            get
            {
                return CPubVars.FormatMoney(this.quantity * this.unit_factor, 2);
            }
        }
        public string ms_sub_qty
        {
            get
            {
                decimal sQty = this.quantity * this.unit_factor;
                if (!string.IsNullOrEmpty(this.m_unit_factor))
                {
                    decimal mFactor = CPubVars.ToDecimal(this.m_unit_factor);
                    return CPubVars.FormatMoney(sQty / mFactor, 3);

                }
                else
                    return CPubVars.FormatMoney(sQty, 3);
            }
        }

        public string ms_sub_qty_unit
        {
            get
            {
                if (!string.IsNullOrEmpty(this.m_unit_factor))
                {
                    return ms_sub_qty + this.m_unit_no;

                }
                else
                    return ms_sub_qty + this.s_unit_no;
            }
        }

        // internal decimal s_sum_qty = 0f;//用于汇总单计算
        //汇总单
        public string sale_quantity_unit_conv { get; set; }
        public string give_quantity_unit_conv { get; set; }



        public string unit_relation
        {
            get
            {
                string r = "";
                if (m_unit_factor.IsValid())
                {
                    r = $"1*{CPubVars.FormatMoney(Convert.ToDouble(b_unit_factor) / Convert.ToDouble(m_unit_factor), 0)}*{m_unit_factor}";
                }
                else
                {
                    r = $"1*{b_unit_factor}";
                }
                return r;
            }
        }
        public string unit_relation1
        {
            get
            {
                string r = "";
                if (m_unit_factor.IsValid())
                {

                    r = $"1{b_unit_no}={CPubVars.FormatMoney(Convert.ToDouble(b_unit_factor) / Convert.ToDouble(m_unit_factor), 0)}{m_unit_no}={b_unit_factor}{s_unit_no}";
                }
                else if (b_unit_factor != "")
                {

                    r = $"1{b_unit_no}={b_unit_factor}{s_unit_no}";
                }
                return r;
            }
        }
        private string _barcode = "";
        public string barcode
        {
            get
            {
                if (_barcode != "") return _barcode;
                if (unit_no == s_unit_no) return s_barcode;
                if (unit_no == b_unit_no) return b_barcode;
                if (unit_no == m_unit_no) return m_barcode;
                return "";
            }
            set
            {
                _barcode = value;
            }
        }
        #endregion
        [SaveToDB][FromFld] public string orig_price { get; set; }
        [SaveToDB][FromFld] public decimal real_price { get; set; }
        [SaveToDB][FromFld] public string sys_price { get; set; }
        [SaveToDB][FromFld] public decimal sub_amount { get; set; }
        [SaveToDB][FromFld] public string remark_id { get; set; }

        // 2024.07.10
        // 增加存放位置（读取商品档案的字段）
        [FromFld(LOAD_PURPOSE.SHOW)] public string location { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string item_spec { get; set; }

        [SaveToDB][FromFld] public string virtual_produce_date { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_retail_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_retail_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_retail_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_lowest_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_lowest_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_lowest_price { get; set; }
        [FromFld("cs.setting->>'validDayType'", LOAD_PURPOSE.SHOW)] public string cs_valid_day_type { get; set; }
        [FromFld("valid_day_type", LOAD_PURPOSE.SHOW)] public string valid_day_type { get; set; }
        [FromFld("valid_days", LOAD_PURPOSE.SHOW)] public string _valid_days { get; set; }
        public string valid_days
        {
            get
            {
                if (_valid_days.IsValid())
                {
                    string vt = "d";
                    if (cs_valid_day_type.IsValid()) vt = cs_valid_day_type;
                    if (valid_day_type.IsValid()) vt = valid_day_type;
                    if (vt == "m") vt = "个月"; else if (vt == "d") vt = "天"; else if (vt == "y") vt = "年";
                    return _valid_days + vt;
                }
                else return "";
            }
        }
        public string valid_till_date
        {
            get
            {
                if (_valid_days.IsValid() && CPubVars.IsDate(virtual_produce_date))
                {
                    DateTime dt = Convert.ToDateTime(virtual_produce_date);
                    //CPubVars.IsNumeric(_valid_days))
                    int.TryParse(_valid_days, out int nValidDays);
                    string vt = "d";
                    if (cs_valid_day_type.IsValid()) vt = cs_valid_day_type;
                    if (valid_day_type.IsValid()) vt = valid_day_type;
                    string s = "";
                    if (vt == "m")
                    {
                        s = CPubVars.GetDateTextNoTime(dt.AddMonths(nValidDays));
                    }
                    else if (vt == "y")
                    {
                        s = CPubVars.GetDateTextNoTime(dt.AddYears(nValidDays));
                    }
                    else if (vt == "d")
                    {
                        s = CPubVars.GetDateTextNoTime(dt.AddDays(nValidDays));
                    }
                    return s;
                }
                else return "";

            }
        }
        [FromFld("t.retail_price", LOAD_PURPOSE.SHOW)] public string _retail_price { get; set; }
        [SaveToDB] public string retail_price
        {
            get
            {
                if (_retail_price.IsValid()) return _retail_price;
                if (unit_no == b_unit_no) return b_retail_price;
                else if (unit_no == m_unit_no) return m_retail_price;
                else if (unit_no == s_unit_no) return s_retail_price;
                return "";
            }
            set
            {
                _retail_price = value;
                if (unit_no == b_unit_no) b_retail_price = value;
                else if (unit_no == m_unit_no) m_retail_price = value;
                else if (unit_no == s_unit_no) s_retail_price = value;
            }
        }

        public string b_real_price
        {
            get
            {
                if (!b_unit_factor.IsValid()) return "";
                return CPubVars.FormatMoney(this.real_price / this.unit_factor * CPubVars.ToDecimal(this.b_unit_factor), 2);
            }
        }
        public string m_real_price
        {
            get
            {
                if (!m_unit_factor.IsValid()) return "";
                return CPubVars.FormatMoney(this.real_price / this.unit_factor * CPubVars.ToDecimal(this.m_unit_factor), 4);
            }
        }
        public string s_real_price
        {
            get
            {
                return CPubVars.FormatMoney(this.real_price / this.unit_factor, 4);
            }
        }
        public string s_real_price_unit
        {
            get
            {
                return CPubVars.FormatMoney(this.real_price / this.unit_factor, 4) + "/" + s_unit_no;
            }
        }
    }

    public class SheetLabelPrint : SheetBase<SheetRowLabelPrint>
    {
        private const string _MainTable = "sheet_label_print_main";
        private const string _DetailTable = "sheet_label_print_detail";

        [SaveToDB][FromFld] public override SHEET_TYPE sheet_type { get; set; }

        public string branch_id { get; set; } = "-1";
        public string supcust_id { get; set; } = "-1";

        public SheetLabelPrint() : base(_MainTable, _DetailTable, LOAD_PURPOSE.SHOW)
        {
            ConstructFun();
            sheet_type = SHEET_TYPE.SHEET_LABEL_PRINT;
        }
        private void ConstructFun()
        {
            MainLeftJoin += @"
                LEFT JOIN (
                    select oper_id,oper_name as maker_name
                    from info_operator
                    where company_id=~COMPANY_ID
                ) maker on t.maker_id=maker.oper_id
                LEFT JOIN (
                    select oper_id,oper_name as approver_name 
                    from info_operator 
                    where company_id=~COMPANY_ID
                ) approver on t.approver_id=approver.oper_id
            ";
            DetailLeftJoin += @$"
                left join {_MainTable} m on t.sheet_id=m.sheet_id and m.company_id=~COMPANY_ID
                left join info_item_prop i on t.item_id=i.item_id and i.company_id=~COMPANY_ID
                left join (select item_id,mum_attributes as son_mum_attributes from info_item_prop where company_id =~COMPANY_ID) son_attrs on case when i.son_mum_item is null then i.item_id else i.son_mum_item end=son_attrs.item_id 
                left join info_item_brand ib on i.item_brand=ib.brand_id and ib.company_id=~COMPANY_ID
                left join (select class_id classId,class_name,order_index as class_order_index,general_class as gen_class2 from info_item_class where company_id =~COMPANY_ID) ic on i.item_class=ic.classId 
                left join (select class_id classId1,class_name as class1_name,order_index as class1_order_index, general_class as gen_class1 from info_item_class where company_id =~COMPANY_ID) ic1 on text_to_int(split_part(i.other_class,'/', 3)) = ic1.classId1
                left join (
                    select item_id,s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,s_unit->>'f3' as s_barcode,s_unit->>'f4' as s_retail_price,s_unit->>'f5' as s_lowest_price,s_unit->>'f6' as s_weight,s_unit->>'f7' as s_volume,
                                   m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,m_unit->>'f3' as m_barcode,m_unit->>'f4' as m_retail_price,m_unit->>'f5' as m_lowest_price,m_unit->>'f6' as m_weight,m_unit->>'f7' as m_volume,
                                   b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor,b_unit->>'f3' as b_barcode,b_unit->>'f4' as b_retail_price,b_unit->>'f5' as b_lowest_price,b_unit->>'f6' as b_weight,s_unit->>'f7' as b_volume
                    from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,retail_price,lowest_price,weight,volume)) as json from info_item_multi_unit where company_id=~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)
                ) unit_barcode on t.item_id=unit_barcode.item_id     
                left join info_supcust sup on i.supplier_id=sup.supcust_id and sup.company_id=~COMPANY_ID
                left join info_manufactor manu on i.manufactor_id=manu.manufactor_id and manu.company_id=~COMPANY_ID
                left join company_setting cs on cs.company_id=~COMPANY_ID
            ";
        }
		protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
		{
			if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
			//CInfoForApprove info = (CInfoForApprove)InfoForApprove;

			base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
 
		}


		public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.make_brief;
            foreach (var row in SheetRows)
            {
                res += row.item_id + "_" + row.item_name + "_" + row.quantity;
            }
            return res;
        }
    }
}
