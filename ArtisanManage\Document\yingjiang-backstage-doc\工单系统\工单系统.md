# 1 工单系统概述

营匠工单系统主要为了帮助客户、代理商等上报系统运行时候的bug、新的需求等。后期拓展客服系统

# 2 数据需求分析

## 2.1  工单信息表 work_order_info

| 字段名           | 中文名               | 是否外键 | 类型 | 备注                                |
| ---------------- | -------------------- | -------- | ---- | ----------------------------------- |
| id               | 自增id               |          |      |                                     |
| parent_id        | 父id                 |          |      | 暂定，子需求                        |
| wx_user_id       | 微信用户id           |          |      | 用于推送、记录谁提交                |
| title            | 工单信息标题         |          |      |                                     |
| describe         | 工单信息主要描述内容 |          |      |                                     |
| open_mobile      | 开户手机号           |          |      |                                     |
|                  |                      |          |      |                                     |
|  |  | | | |
| src_worker | 来源提出者 | | | |
| company_id | 公司id | | | |
| client_contact | 客户联系方式 | | | |
|                  |                      |          |      |                                     |
|                  |                      |          |      |                                     |
| customer_name | 客户称呼 | | | 可以后续和wx_user表进行联动 |
| customer_contact | 客户联系方式 | | | 可以后续和wx_user表进行联动 |
| submit_time      | 用户提交时间         |          |      |                                     |
| need_days        | 预计时间             |          |      | 后台负责人填写                      |
| finish_time      | 结单时间             |          |      |                                     |
|                  |                      |          |      |                                     |
|                  |                      |          |      |                                     |
| work_type        | 工单类型             | 是       |      | work_order_type                     |
| status           | 状态                 | 是       |      | work_order_status                   |
| receiver_id      | 接单人id             | 是       |      |                                 |
| receive_time     | 接单时间             |          |      |                                 |
| manager_id       | 负责人id             | 是       |      | 任务负责人work_order_staff |
| tester_id        | 测试人id             | 是       |      | 测试人员                            |
| dealer_id        | 处理人员id           | 是       |      |                                     |
| remark           | 备注                 |          |      | 后台管理人员进行备注                |
| logs             | 日志                 |          |      | 自动记录一些如转单等情况操作情况    |
| media            | 图片列表地址         |          |      | 暂定                                |
| classify         | 所属分类             |          |      | 暂定：什么单据、什么功能 需要独立表 |

## 2.2 分类表 work_order_type

| 字段名    | 中文名   | 类型 | 备注        |
| --------- | -------- | ---- | ----------- |
| id        | 自增id   |      |             |
| type_key  | 类型key  |      | bug、demand |
| type_name | 类型name |      | 缺陷、需求  |

## 2.3 状态表 work_order_status

| 字段名      | 中文名  | 类型 | 备注                                                         |
| ----------- | ------- | ---- | ------------------------------------------------------------ |
| id          | 自增id  |      |                                                              |
| status_key  | 状态key |      | submit、received、confirm、 develop、test、 to_publish、cancel、 finish、reject |
| status_name | 状态名  |      | 已提交、已接单、 正在确认、 研发中、 测试中、待发布、已撤销、 已发布、待商榷 |

## 2.4 员工表 work_order_staff

| 字段名         | 中文名       | 类型 | 备注                     |
| -------------- | ------------ | ---- | ------------------------ |
| id             | 自增id       |      |                          |
| wx_user_id     | wx_user_id   |      | 用于推送，以及后台新建用 |
| staff_name     | 工单员工姓名 |      |                          |
| staff_mobile   | 手机号       |      | 暂定：用于后续登录       |
| staff_password | 密码         |      | 暂定：登录用             |
| staff_roles    | 员工角色     |      | admin、receiver、handler |
| staff_status   | 账号状态     |      | 是否禁用 true、false     |

## 2.5 员工角色表 work_order_role

| 字段名    | 中文名 | 类型 | 备注 |
| --------- | ------ | ---- | ---- |
| id        | 自增id |      |      |
| role_key  | key    |      |      |
| role_name | 角色名 |      |      |

## 2.6 部门表

| 字段名          | 中文名   | 类型 | 备注                       |
| --------------- | -------- | ---- | -------------------------- |
| id              | 自增id   |      |                            |
| parent_id       | 父级id   |      |                            |
| department_key  |          |      |                            |
| department_name |          |      |                            |
| handle_range    | 处理范围 |      | 保留，以供后续自动分配使用 |

## 2.7 工单分支表 work_order_branch









# 3 功能需求分析

## 3.1 客户端

> V 1.0 目前主要入口为微信公众号进行工单的提交

### 3.1.1 初始化接口

### 3.1.2 保存提交接口

### 3.1.3 消息模板提醒接口

## 3.2 后台系统

### 3.2.1 工单状态修改

| key        | name     | desc                                                         |
| ---------- | -------- | ------------------------------------------------------------ |
| submit     | 已提交   | 正常新建工单、其他状态不允许改成此状态submit_time;           |
|            |          | receiver_time;finish_time                                    |
|            |          |                                                              |
| received   | 已接单   | 1. 系统自动分配 2. 管理员进行分配 ， 有了receive_time才是已接单状态 |
|            |          |                                                              |
| confirm    | 正在确认 |                                                              |
| develop    | 研发中   |                                                              |
|            |          |                                                              |
| test       | 测试中   | bug类型或者demand类型的，只能修改到此状态，将此工单之后添加测试人员，由测试人员进行下一步的状态修改。由部门来决定情况， |
|            |          |                                                              |
| to_publish | 待发布   |                                                              |
|            |          |                                                              |
| cancel     | 已撤销   | finish_time                                                  |
| finish     | 已发布   | finish_time                                                  |
| reject     | 待商榷   | finish_time                                                  |





# 4 公众号提醒

1. 用户提交，状态是 已提交 
2. 技术develop研发中
3. 已发布
4. 待商榷
