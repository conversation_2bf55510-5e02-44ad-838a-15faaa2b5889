﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Dynamic;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{

    /// <summary>
    /// App端自定义桌面
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class AppDeskController : QueryController
    { 
        public AppDeskController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        /// 查看今日拜访次数及新增客户数（部门长）
        [HttpGet]
        public async Task<JsonResult> DepartManagerGetVisitorCountAndCreateCustomerCount(String operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            String todayStart = CommonTool.ToDayStartText(DateTime.Now);
            String todayEnd = CommonTool.ToDayEndText(DateTime.Now);
            var todayVisitCountSQL = $@"
                    SELECT COUNT( 1 ) AS VALUE FROM sheet_visit 
                    WHERE seller_id in (
	            		SELECT oper_id from info_operator where depart_id in (
				                SELECT depart_id from info_department where '{operID}' in ( SELECT regexp_split_to_table(managers_id ,',')) and company_id ={companyID}
		                    )
	                    ) 
                AND company_id = {companyID}  AND start_time BETWEEN '{todayStart}'  AND '{todayEnd}'";
            var todayCreateSupcustCountSQL = @$"SELECT COUNT(1) as value FROM info_supcust WHERE  company_id = {companyID} and creator_id in (
	           SELECT oper_id from info_operator where depart_id in (
				      SELECT depart_id from info_department where '{operID}' in ( SELECT regexp_split_to_table(managers_id ,',')) and company_id ={companyID}
		       )
            )            
            AND create_time between '{todayStart}' AND '{todayEnd}'";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("todayVisitCountSQL", todayVisitCountSQL);
            QQ.Enqueue("todayCreateSupcustCountSQL", todayCreateSupcustCountSQL);
            String visit_count = "";
            String create_supcust_count = "";
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "todayVisitCountSQL")
                {
                    dynamic visitCountData = CDbDealer.Get1RecordFromDr(dr, false);
                    if (visitCountData != null)
                    {
                        visit_count = visitCountData.value;
                    }
                }
                if (sqlName == "todayCreateSupcustCountSQL")
                {
                    dynamic createSupcustCountData = CDbDealer.Get1RecordFromDr(dr, false);
                    if (createSupcustCountData != null)
                    {
                        create_supcust_count = createSupcustCountData.value;
                    }
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, visit_count, create_supcust_count });
        }

        /// 查看今日拜访次数及新增客户数（业务员）
        [HttpGet]
        public async Task<JsonResult> SellerGetVisitorCountAndCreateCustomerCount(String operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            String todayStart = CommonTool.ToDayStartText(DateTime.Now);
            String todayEnd = CommonTool.ToDayEndText(DateTime.Now);
            var todayVisitCountSQL = $"SELECT COUNT(1) as value FROM sheet_visit WHERE seller_id={operID} and company_id = {companyID} AND start_time between '{todayStart}' AND '{todayEnd}'";
            var todayCreateSupcustCountSQL = $"SELECT COUNT(1) as value FROM info_supcust WHERE  company_id = {companyID} and creator_id={operID} AND create_time between '{todayStart}' AND '{todayEnd}'";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("todayVisitCountSQL", todayVisitCountSQL);
            QQ.Enqueue("todayCreateSupcustCountSQL", todayCreateSupcustCountSQL);
            String visit_count = "";
            String create_supcust_count = "";
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0) {
                var sqlName = QQ.Dequeue(); 
                if(sqlName== "todayVisitCountSQL")
                {
                    dynamic visitCountData =  CDbDealer.Get1RecordFromDr(dr,false);
                    if (visitCountData != null)
                    {
                        visit_count = visitCountData.value;
                    }
                    }
                if (sqlName == "todayCreateSupcustCountSQL")
                {
                    dynamic createSupcustCountData = CDbDealer.Get1RecordFromDr(dr, false);
                    if (createSupcustCountData != null)
                    {
                        create_supcust_count = createSupcustCountData.value;
                    }
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, visit_count, create_supcust_count });
        }
        /// 查看今日拜访次数及新增客户数（老板）
        [HttpGet]
        public async Task<JsonResult> BossGetVisitorCountAndCreateCustomerCount(String operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            String todayStart = CommonTool.ToDayStartText(DateTime.Now);
            String todayEnd = CommonTool.ToDayEndText(DateTime.Now);
            var todayVisitCountSQL = $"SELECT COUNT(1) as value FROM sheet_visit WHERE company_id={companyID} AND start_time between '{todayStart}' AND '{todayEnd}'";
            var todayCreateSupcustCountSQL = $"SELECT COUNT(1) as value FROM info_supcust WHERE company_id={companyID} AND create_time between '{todayStart}' AND '{todayEnd}'";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("todayVisitCountSQL", todayVisitCountSQL);
            QQ.Enqueue("todayCreateSupcustCountSQL", todayCreateSupcustCountSQL);
            String visit_count = "";
            String create_supcust_count = "";
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "todayVisitCountSQL")
                {
                    dynamic visitCountData = CDbDealer.Get1RecordFromDr(dr, false);
                    if (visitCountData != null)
                    {
                        visit_count = visitCountData.value;
                    }
                }
                if (sqlName == "todayCreateSupcustCountSQL")
                {
                    dynamic createSupcustCountData = CDbDealer.Get1RecordFromDr(dr, false);
                    if (createSupcustCountData != null)
                    {
                        create_supcust_count = createSupcustCountData.value;
                    }
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, visit_count, create_supcust_count });
        }
        /// <summary>
        /// 获取用户自定义桌面App
        /// </summary>
        /// <param name="operKey">用户秘钥</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetDesktop(string operKey,string appORpcFlag)
        { 
            Security.GetInfoFromOperKey(operKey,out string companyID,out string operID);
            var sql = "";
            if (appORpcFlag == "app")
            {
                sql = $"select io.app_desktop,os.app_setting from info_operator io left join oper_setting os on io.company_id=os.company_id and io.oper_id=os.oper_id where io.oper_id = {operID} and io.company_id = {companyID}";
            }
            else if (appORpcFlag == "pc") {
                sql = $"select io.pc_desktop,os.pc_setting from info_operator io left join oper_setting os on io.company_id=os.company_id and io.oper_id=os.oper_id where io.oper_id = {operID} and io.company_id = {companyID}";
            }
            
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync(sql,cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result,msg, data});
        }



        /// <summary>
        /// 保存用户自定义桌面
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> SaveDesktop([FromBody] dynamic data)
        { 
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            var sql = "";
            string appORpcFlag = data.appORpcFlag;
            if (appORpcFlag == "app")
            {
                string app_desktop = Newtonsoft.Json.JsonConvert.SerializeObject(data.app_desktop);
                sql = $"update info_operator set app_desktop ='{app_desktop}' where oper_id = {operID} and company_id = {companyID}";
            }
            else if (appORpcFlag == "pc")
            {
                string pc_desktop = Newtonsoft.Json.JsonConvert.SerializeObject(data.pc_desktop);
                sql = $"update info_operator set pc_desktop ='{pc_desktop}' where oper_id = {operID} and company_id = {companyID}";
            }
            cmd.CommandText = sql;
            await   cmd.ExecuteNonQueryAsync();
            string result = "OK";
            return Json(new { result,msg = "" });
        }
        /// 获取个性化设置
//        [HttpGet]
//        public async Task<JsonResult> GetUserPersonalSetting(string operKey,string type)
//        {
//            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
//            string sql = $@"";
//            if(type=="app")
//            {
//                sql = $@"
//SELECT app_setting FROM g_personal_setting WHERE oper_id={operID} and company_id={companyID}";
//            }
//            else if (type == "pc")
//            {
//                sql = $@"
//SELECT pc_setting FROM g_personal_setting WHERE oper_id={operID} and company_id={companyID}";
//            }
//            dynamic data = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
//            string result = "OK";
//            string msg = "";
//            return Json(new { result, msg, data });
//        }
        /// 插入或更新个性化设置
        [HttpPost]
        public async Task<JsonResult> InsertOrUpdateUserPersonalSetting([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string sql = $@"";
            string type = data.type;
            string personal_setting= Newtonsoft.Json.JsonConvert.SerializeObject(data.personal_setting);
            if (type=="app")
            {
                sql = $@"
INSERT INTO oper_setting(company_id,oper_id,app_setting)
VALUES ({companyID},{operID},'{personal_setting}')
ON conflict(company_id,oper_id)
DO UPDATE SET app_setting='{personal_setting}';";
            }
            else if(type=="pc")
            {
                sql=$@"
INSERT INTO oper_setting(company_id,oper_id,pc_setting)
VALUES ({companyID},{operID},'{personal_setting}')
ON conflict(company_id,oper_id)
DO UPDATE SET pc_setting='{personal_setting}';";
            }
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            string result = "OK";
            return Json(new { result, msg = "" });
        }

        [HttpPost]
        public async Task<JsonResult> UpdateOperSetting([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string sql = $@"";
            string type = data.type;
            string setting = Newtonsoft.Json.JsonConvert.SerializeObject(data.setting);
            if (type == "app")
            {
                sql = $@"
INSERT INTO oper_setting(company_id,oper_id,app_setting)
VALUES ({companyID},{operID},'{setting}')
ON conflict(oper_id)
DO UPDATE SET app_setting=oper_setting.app_setting||'{setting}'::jsonb;";
            }
            else if (type == "pc")
            {
                sql = $@"
INSERT INTO oper_setting(company_id,oper_id,pc_setting)
VALUES ({companyID},{operID},'{setting}')
ON conflict(oper_id)
DO UPDATE SET pc_setting=oper_setting.app_setting||'{setting}'::jsonb;";
            }
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            string result = "OK";
            return Json(new { result, msg = "" });
        }

    }
}
