@page
@model ArtisanManage.Pages.BaseInfo.SalesSummaryByBrandModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxpopover.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';

    	    var newCount = 1;
    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
                $("#popoverQuery").jqxPopover({ showArrow: false, autoClose: true, offset: { left: 0, top: -10 }, position: "bottom", title: "", showCloseButton: false, selector: $("#btnQueryChooseSheets") });


                $("#gridItems").on("cellclick", function (event) {

                    var args = event.args;
                    //品牌
                    var brand_id = args.row.bounddata.brand_id;
                    var brand_name = args.row.bounddata.brand_name;
                    var title = '销售明细表';
                    if (sheetType=="xd") title = '订单明细表'

                    if (args.datafield == "brand_name" && brand_name) {
                    window.queryItems = funGetQueryValues();
                    window.queryItems['brand_id'] = brand_id;
                    window.queryItems['brand_name'] = brand_name;

                    // 特殊处理一些字段
                    const regionName = $('#other_region').val().label;
                    const regionPath = $('#other_region').jqxDropDownTree('treePath')
                    if (regionName) {
                        window.queryItems['region_name'] = regionName;
                        window.queryItems['region_path'] = regionPath;
                        window.queryItems['other_region'] = $('#other_region').val().value
                        // fix #3471 (2024.08.16)
                        // 销售汇总中的片区值是树型(1234/5261),到了销售明细表就是值(5261),很酷
                    }

                    var queryItemStr = queryItemsToString(window.queryItems)
                    window.parent.newTabPage(title, 'Report/SalesDetail' + queryItemStr, window);
                    }
                });
                QueryData();
                let windowHeight = document.body.offsetHeight - 50
                let windowWidth = document.body.offsetWidth - 80
                    $('#supcust_id').jqxInput({
                        onButtonClick: function (event) {
                            $('#popClient').jqxWindow('open');
                            $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&multiSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                        }
                    });
                $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 900, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            });

        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });
                }
                else if (rs.data.action === "selectMulti") {
                    var rows = rs.data.checkedRows
                    var supcusts_id = '', supcusts_name = ''
                    rows.forEach(function (row) {
                        if (supcusts_id != '') supcusts_id += ','
                        supcusts_id += row.supcust_id
                        if (supcusts_name != '') supcusts_name += ','
                        supcusts_name += row.sup_name
                    })
                    $('#supcust_id').jqxInput('val', { value: supcusts_id, label: supcusts_name });
                }
                $('#popClient').jqxWindow('close');
            }

        });
        function btnQuerySaleSummaryBySeller_click() {
            debugger
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（业务员）", 'Report/SalesSummaryBySeller' + queryItemStr, window);
            var len = $('#jqxTabs').jqxTabs('length');
            var content = $('#jqxTabs').jqxTabs('getContentAt', len - 1);
            var frame = content.childNodes[0];
            var w = frame.contentWindow;
            w.g_bRelatedReport_sale = true
            window.g_bRelatedReport_sale = true

        }
        function btnQuerySaleSummaryByClient_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（客户）", 'Report/SalesSummaryByClient' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByItem_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（商品）", 'Report/SalesSummaryByItem' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByRegion_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（片区）", 'Report/SalesSummaryByRegion' + queryItemStr, window);
        }
        function btnQuerySaleSummaryByGroup_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（渠道）", 'Report/SalesSummaryByGroup' + queryItemStr, window);
        }
        function btnQuerySaleSummaryBySender_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售汇总（送货员）", 'Report/SalesSummaryBySender' + queryItemStr, window);
        }
        function btnQuerySaleDetail_click() {
            window.queryItems = funGetQueryValues();
            var queryItemStr = queryItemsToString(window.queryItems)
            window.parent.newTabPage("销售明细表", 'Report/SalesDetail' + queryItemStr, window);
        }
    </script>
</head>

<body>
    <style>
        .jqx-popover {
            border-color: #e2e2e2;
            border-radius: 20px;
            box-shadow: 20px 20px 50px 0px rgba(0, 0, 0, 0.25);
        }
    </style>
    <div style="display:flex;padding-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-top:30px;margin-right:0px;border-radius: 3px 0px 0px 3px">查询</button>

        <button id="btnQueryChooseSheets" class="btnright" style="width:30px;margin-right:20px;margin-top:30px;margin-left:0px;border-radius: 0px 3px 3px 0px">
            <img src="~/PrintTemplate/img/triangle.svg" style="margin-top: -1px; width: 14px; display: inline-block;vertical-align: middle;" />
        </button>
        <div id="popoverQuery" style="position:absolute;display:none;border-radius: 5px 5px 5px 5px">
            <div style="width:150px;height:200px;display:flex;flex-direction:column;justify-content:space-between;align-items:center;">
                <ul style="line-height: 26px;font-size:15px;padding:0px;">
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a id="btnshow" value="Show" onclick="btnQuerySaleSummaryBySeller_click();">
                            销售汇总（业务员）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByClient_click();">
                            销售汇总（客户）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByItem_click();">
                            销售汇总（商品）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByRegion_click();">
                            销售汇总（片区）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryByGroup_click();">
                            销售汇总（渠道）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleSummaryBySender_click();">
                            销售汇总（送货员）
                        </a>
                    </li>
                    <li style="list-style:none;cursor:pointer;" onmouseover="this.style.background='#DCDCDC'" onmouseout="this.style.background=''">
                        <a value="Show" onclick="btnQuerySaleDetail_click();">
                            销售明细表
                        </a>
                    </li>
                </ul>
                <div id="divClientVersion"></div>
            </div>
        </div>

        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;margin-top:30px;">导出</button>
    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>
     <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>