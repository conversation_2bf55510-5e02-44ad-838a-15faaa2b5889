﻿@page
@model ArtisanManage.Pages.BaseInfo.SaleContractorProfitModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var m_db_id = "10";

        var newCount = 1;

        var itemSource = {};
        $(document).ready(function () {
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)
                $("#gridItems").on("cellclick", function (event) {
                    // event arguments.
                    var args = event.args;
                    var item_id = args.row.bounddata.item_id;
                    var item_name = args.row.bounddata.item_name;
                    var startDay = $('#startDay').jqxDateTimeInput('val');
                    var endDay = $('#endDay').jqxDateTimeInput('val');
                    var branch_id = $('#branch_id').val().value;
                    var branch_name = $('#branch_id').val().label;
                    var supcust_id = $('#supcust_id').val().value;
                    var sup_name = $('#supcust_id').val().label;
                    debugger
                    var title = '销售明细表';
                    var url = `Report/SalesDetail?sheetType=X&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}`;
                    if (args.datafield == "item_name" && item_id) {

                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}`;
                        if (sup_name) url += `&supcust_id=${supcust_id}&sup_name=${sup_name}`;
                        window.parent.newTabPage(title, `${url}`);

                    }


                });

            QueryData();
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            $('#item_id').jqxInput({
                onButtonClick: function (event) {
                    $('#popItem').jqxWindow('open');
                    $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                }
            });
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
        });
        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "selectMulti") {
                    if (rs.data.checkedRows.length == 1) {
                        var item_id = rs.data.checkedRows[0].item_id;
                        var item_name = rs.data.checkedRows[0].item_name;
                    }

                    var rows = rs.data.checkedRows
                    var items_id = ''
                    var items_name = ''
                    rows.forEach(function (row) {
                        if (items_id != '') {
                            items_id += ',';
                        }

                        items_id += row.item_id;
                        items_name += row.item_name + ';';

                    })
                    $('#item_id').jqxInput('val', { value: items_id, label: items_name });

                    //$.ajax({
                    //    url: '/api/SaleSheet/GetItemInfo',
                    //    type: 'GET',
                    //    contentType: 'application/json',
                    //    data: { operKey: g_operKey, item_id: items_id },
                    //    success: function (data) {
                    //        if (data.result === 'OK') {
                    //            if (!window.g_queriedItems) window.g_queriedItems = {};
                    //            window.g_queriedItems[item_id] = data.item;
                    //        }
                    //    }
                    //});
                }

                $('#popItem').jqxWindow('close');
            }

        });
    </script>
</head>

<body style="overflow:hidden">

    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail" style="width:calc(100% - 100px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>


    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>


    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择商品</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
</body>
</html>