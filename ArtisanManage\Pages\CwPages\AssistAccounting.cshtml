﻿@page
@model ArtisanManage.Pages.CwPages.AssistAccountingModel
@{
}
<!DOCTYPE html>

<html>

<head>
    <meta name="viewport" content="width=device-width" />
    <title>AssistAccounting</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>

    <style>
        * {
            font-family: "微软雅黑"
        }
        [v-cloak] {
            display: none;
        }

        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;
        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #dddddd;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }

        #pages {
            width: 100%;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            /*background-color: #f9f9f9;*/
        }

        .pages_title {
            width: 100%;
            font-weight: 500;
            font-size: 25px;
            text-align: center;
            margin-top: 5px;
            padding-bottom: 10px;
        }

        .pages_query {
            display: inline-block;
            margin-left: 20px;
            margin-right: 20px;
            flex-wrap: wrap;
            padding-bottom: 10px;
            height: 35px !important;
        }

        .sub_select_input{
            width:200px;
        }

        .pages_content {
            width: 98%;
            height: 81%;
            margin: 10px 1%;
            display: block;
            overflow:hidden;
        }

        .pages_content table {
            width: 100%;
            height:100%;
            border: 0;
            border-collapse: collapse;
            border: 1px solid #ebeef5;
        }

        .pages_content table thead th{
            background-color:#f0f0f5;
        }

        .pages_content table tbody {
            display: block;
            overflow: auto;
            overflow-x: hidden;
            height: 95%;
        }

        @@media(max-height:700px) {
            .pages_content table tbody {
                display: block;
                overflow: auto;
                overflow-x: hidden;
                height: 460px;
            }
         }

         .pages_content table thead, .pages_content tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .pages_content table thead {
            width: calc( 100% - 1em );
        }

        @*高度*@
        .pages_content table thead th, .pages_content table tbody td {
            min-height: 40px;
            line-height: 40px;
        }
        @*边框*@
        .pages_content table thead th, .pages_content table tbody td {
            border-bottom: 1px solid #ebeef5;
            border-right: 2px solid #fff;
        }
        .pages_content table thead th:last-child {
            border-right: 0;
        }
        @*背景*@
        .pages_content table tbody tr:nth-child(odd) {
            background: #fafafa;
        }
        .pages_content table tbody tr:hover {
            background-color: #f5f7fa;
        }

        .pages_content table thead th:nth-child(1),  .pages_content table tbody td:nth-child(1) {
            width: 8%
        }
        .pages_content table thead th:nth-child(2),  .pages_content table tbody td:nth-child(2) {
            width: 25%
        }
        .pages_content table thead th:nth-child(3),  .pages_content table tbody td:nth-child(3) {
            width: 8%
        }
        .pages_content table thead th:nth-child(5),  .pages_content table tbody td:nth-child(5) {
            width: 8%
        }
        .pages_content table thead th, .pages_content table tbody td {
            padding: 0 15px;
        }

        .el-select{
            margin-right:20px;
        }

        .pages_tail{
            width: 98%;
            height: 3%;
            margin: 2px 1%;
            display: block;
            align-items:right;
        }

        .el-pagination{
            position:absolute;
            right:1%;
        }

        #iconInfo{
            position:absolute; 
            right:30px; 
            font-size:25px;
        }
        
    </style>
</head>

<body>
    <div id="root" v-cloak>
        <div id="pages">
            <div class="pages_title">辅助核算</div>
            <div class="pages_head">
                <div class="pages_query">
                    科目：
                    <el-select :value="subSelect.sub_full_name" placeholder="请选择科目" :disabled="subOptionDisabled" @@change="subOptionChange">
                        <el-option v-for="sub in assistSubs" :key="sub.sub_code" :label="sub.sub_full_name" :value="sub.sub_code"></el-option>
                    </el-select>
                    辅助项：
                    <el-select :value="typeSelect.name" placeholder="请选择科目" @@change="typeOptionChange">@*@@visible-change="typeSelVisibleChange"*@
                        <el-option v-for="t in subSelect.types" :key="t.id" :value="t.id" :disabled="t.disabled">{{t.name}}
                            <el-checkbox v-model="t.clickCheck" style="float:right;" :disabled="t.disabled" @@change="typeCheckChange(t)"></el-checkbox>
                        </el-option>
                        <el-button type="primary" size="mini" style="float:right;margin:10px;" @@click="saveTypes">保存</el-button>
                    </el-select>
                    @*<div id="iconInfo"  :style="iconStyle">
                        <el-tooltip class="item" effect="light" content="双击选择辅助科目" placement="left-start">
                            <i class="el-icon-warning-outline"></i>
                        </el-tooltip>
                    </div>*@
                </div>
            </div>
            <div class="pages_content">
                <table>
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>名称</th>
                            <th>助记码</th>
                            <th>科目</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="row in assist_details_page" :key="row.id" @*@@dblclick="rowForSelect(row)"*@>
                            <td>{{row.no}}</td>
                            <td>{{row.name}}</td>
                            <td>{{row.py_str}}</td>
                            <td>{{row.sub_full_name}}</td>
                            <td>{{row.status}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="pages_tail">
                <el-pagination
                    small
                    :background="true"
                    layout="prev, pager, next, total"
                    :page-size="pagination.pageSize"
                    :current-page="pagination.currentPage"
                    @@current-change="pageChange"
                    :total="pagination.total">
                </el-pagination>
            </div>
        </div>   
    </div>                      

    <script>
        var g_operKey = '@Model.OperKey';
        @*var ForSelect='@Model.ForSelect'.toLowerCase();
        var SubCode='@Model.SubCode';*@

        window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);
        function checkOperRight(vm) {
            if (window.g_operRights.cwOperate.assistAccounting) {
                if (!window.g_operRights.cwOperate.assistAccounting.operate) {
                    vm.disabledOperate = true;
                }
                return true;
            } else {
                return false;
            }
        }


    </script>
    <script>
        var vm = new Vue({
            el: '#root',
            data() {
                return {
                    disabledOperate: false,
                    assistSubs:[ 
                        { sub_code: '1122', sub_full_name: '1122 应收账款', 
                            types: [ 
                                { id: 'C', name: '客户', disabled: false, checked: false, clickCheck: false }, 
                                { id: 'S', name: '供应商', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'INV', name: '商品', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'DEP', name: '部门', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'MAN', name: '业务员', disabled: false, checked: false, clickCheck: false }
                            ] 
                        },
                        { sub_code: '2202', sub_full_name: '2202 应付账款', 
                            types: [ 
                                { id: 'C', name: '客户', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'S', name: '供应商', disabled: false, checked: false, clickCheck: false }, 
                                { id: 'INV', name: '商品', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'DEP', name: '部门', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'MAN', name: '业务员', disabled: false, checked: false, clickCheck: false }
                            ] 
                        },
                        { sub_code: '1405', sub_full_name: '1405 库存商品', 
                            types: [ 
                                { id: 'C', name: '客户', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'S', name: '供应商', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'INV', name: '商品', disabled: false, checked: false, clickCheck: false }, 
                                { id: 'DEP', name: '部门', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'MAN', name: '业务员', disabled: false, checked: false, clickCheck: false }
                            ] 
                        },
                        { sub_code: '5001', sub_full_name: '5001 主营业务收入', 
                            types: [ 
                                { id: 'C', name: '客户', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'S', name: '供应商', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'INV', name: '商品', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'DEP', name: '部门', disabled: false, checked: false, clickCheck: false }, 
                                { id: 'MAN', name: '业务员', disabled: false, checked: false, clickCheck: false }
                            ]  
                        },
                        { sub_code: '5051', sub_full_name: '5051 主营业务成本', 
                            types: [ 
                                { id: 'C', name: '客户', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'S', name: '供应商', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'INV', name: '商品', disabled: true, checked: false, clickCheck: false }, 
                                { id: 'DEP', name: '部门', disabled: false, checked: false, clickCheck: false }, 
                                { id: 'MAN', name: '业务员', disabled: false, checked: false, clickCheck: false }
                            ]  
                        },
                    ],
                    subSelect: { sub_code: '1122', sub_full_name: '1122 应收账款', 
                        types: [ 
                            { id: 'C', name: '客户', disabled: false, checked: false, clickCheck: false }, 
                            { id: 'S', name: '供应商', disabled: true, checked: false, clickCheck: false }, 
                            { id: 'INV', name: '商品', disabled: true, checked: false, clickCheck: false }, 
                            { id: 'DEP', name: '部门', disabled: true, checked: false, clickCheck: false }, 
                            { id: 'MAN', name: '业务员', disabled: false, checked: false, clickCheck: false }
                        ] 
                    },
                    typeSelect: { id: 'C', name: '客户', disabled: false, checked: false, clickCheck: false },
                    assist_details:[],
                    assist_details_page:[],
                    pagination: { pageSize:200, pages:1, currentPage:1, total:0 },
                    subOptionDisabled: false,
                    iconStyle:'display:none;'
                }
            },
            created() {
                //判断权限
                if (!window.checkOperRight(this)) {
                    this.disabledOperate = true;
                    return;
                }

                //初始化1
                this.getAssistSetting();

                //判断是否用于凭证选择
                //if(SubCode!=''){
                //    this.subOptionDisabled=true;
                //    this.iconStyle='display:inline-block; ';
                //    this.subOptionChange(SubCode);
                //    return;
                //}

                //初始化2
                this.subOptionChange(this.subSelect.sub_code);
            },
            methods: {
                getAssistSetting(){
                    $.ajax({
                        url: `/api/AssistAccounting/GetAssistSetting`,
                        type: 'get',
                        data: { operKey: g_operKey },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                        async: false
                    }).then(res => {
                        console.log('get assist type setting', res);
                        if (res.result === 'OK') {
                            this.assistSubs.forEach(subFront=>{
                                let subEnd1=res.cwAssistSetting.find(subEnd=>subEnd.sub_code==subFront.sub_code);
                                if(subEnd1!=null){
                                    subFront.types.forEach(typeFront=>{
                                        let typeEnd1=subEnd1.types.find(typeEnd=>typeEnd==typeFront.id);
                                        if(typeEnd1!=null){
                                            typeFront.checked=true;
                                            typeFront.clickCheck=true;
                                        }
                                    });
                                }
                            });
                        }else{
                            this.$alert(res.msg, '提示', {
                                confirmButtonText: '确定',
                                type: 'warning'
                            });
                        }
                    });
                },
                subOptionChange(sub_code){
                    let sub=this.assistSubs.find(s=>s.sub_code==sub_code);
                    this.subSelect=sub;
                    let type=this.subSelect.types.find(t=>t.disabled==false);
                    this.typeSelect=type;
                    this.typeOptionChange(type.id);
                },
                typeOptionChange(type_id){
                    let type=this.subSelect.types.find(t=>t.id==type_id);
                    if(type.checked==false){
                        this.assist_details=[];
                        this.assist_details_page=[];
                        return;
                    } 
                    this.typeSelect=type;
                    this.getData(this.subSelect.sub_code, type_id);
                    this.pageChange(this.pagination.currentPage);
                },
                //typeSelVisibleChange(visible){ //没办法用，选择其一另一个就会取消，因为点击就收起了一次
                //    if(visible==false){
                //        let types=this.subSelect.types.filter(t=>t.disabled==false && t.clicked!=t.clickCheck);
                //        types.forEach(t=>t.clickCheck=t.checked); //type select框隐藏，则清空未保存勾选
                //    }
                //},
                typeCheckChange(type){
                    $('.el-input__suffix')[1].click();//点击checkbox会自动收起下拉框，这里再点一次维持展开
                },
                saveTypes(){
                    let types=this.subSelect.types.filter(t=>t.disabled==false && t.checked!=t.clickCheck);
                    if(types.length==0){
                        this.$alert('该科目辅助项未修改', '提示', { confirmButtonText: '确定', type: 'warning' });
                        return;
                    } 
                    let sub=this.subSelect;
                    this.$confirm('科目开启辅助核算且使用过后不能取消，是否保存辅助核算设置？', '提示', {
                        confirmButtonText: '确定',cancelButtonText: '取消'
                    }).then(() => {
                        $('.el-input__suffix')[1].click();//收起下拉框
                        $.ajax({
                            url: `/api/AssistAccounting/Save?operKey=${g_operKey}`,
                            type: 'post',
                            data: JSON.stringify({ operKey: g_operKey, sub_code: sub.sub_code, types: types }),
                            contentType: "application/json;charset=UTF-8",
                            dataType: 'json',
                            async: false
                        }).then(res => {
                            console.log('save assist type setting', res);
                            if (res.result === 'OK') {
                                
                            }else{
                                this.$alert(res.msg, '提示', {
                                    confirmButtonText: '确定',
                                    type: 'warning'
                                });
                            }
                        });
                    });
                },
                getData(sub_code,type_id) {
                    $.ajax({
                        url: '/api/AssistAccounting/GetData',
                        type: 'get',
                        data: { operKey: g_operKey, sub_code: sub_code, type: type_id  },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                        async: false
                    }).then(res => {
                        //console.log('assist accounting get', res);
                        console.log('assist accounting get', res.result);
                        if (res.result === 'OK') {
                            this.assist_details=res.data.sort((a, b) => a.no - b.no);//res.data: oper_id,name,status,py_str,no,sub_full_name
                            this.pagination.currentPage=1;
                            this.pagination.total=this.assist_details.length;
                            this.pagination.pages=Math.floor(this.pagination.total/this.pagination.pageSize);
                        }
                    });
                },
                pageChange(page_no) {
                    console.log(`assist accounting, current page: ${page_no}`);
                    this.assist_details_page=[];
                    let start=(page_no-1)*this.pagination.pageSize;
                    let end=page_no*this.pagination.pageSize-1;
                    for(let i=0;i<this.assist_details.length;i++){
                        if( start<=i && i<=end ){
                            this.assist_details_page.push(this.assist_details[i]);
                        }
                    }
                    $('tbody')[0].scrollTo({ top: 0, left: 0, behavior: "smooth" });
                },
                //rowForSelect(row){
                //    if(ForSelect=="true"){
                        //console.log(row.id,row.name);
                //        console.log('select assist sub: '+row.name);
                //        let assist_sub={ assister_id: row.id, assister_name: row.name, assister_type_name: this.typeSelect.name, assister_type_id: this.typeSelect.type_id  };
                //        let msg = { msgHead: 'AssistAccounting', action: 'dblclick', assist_sub: assist_sub };
                //        window.parent.postMessage(msg, '*');
                //    }
                //}

            }
        });
    </script>
</body>

</html>