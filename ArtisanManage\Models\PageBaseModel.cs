using ArtisanManage.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Npgsql.Replication.PgOutput.Messages;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Threading.Tasks;
using System.Web;
using static ArtisanManage.Models.DataItem;

//using YuChenManage.Services;

namespace ArtisanManage.Models
{
    public class BaseDataGrid
    {
        public Dictionary<string, DataItem> Columns = new Dictionary<string, DataItem>();
        public List<Dictionary<string, DataItem>> Rows = new List<Dictionary<string, DataItem>>();
        public string TableName = ""; public string IdFld = "";
        public string SelectFromSQL = "";
        public int MinRows = 3;
    }
    public class PartialViewModelBase
    {
        public string Version = "";
        public string company_id = "";
        public string OperKey = "";
        public string OperName = "";
        public string BaiduKey =CPubVars.BaiduKey;
        public string OperDepartPath = "";
        public string JsonOperRights = "";
        public string JsonOperRightsOrig = "";
        public string JsonCompanySetting = "";
        public string JsonOptionsRemembered = "";
        public string JsonBranchRights = "";
        public string JsonPageSetting = "{}";
        public string JsonColumnsWidth = "{}";
        public string PageName = "";
        public string CoolieServerUri = "";
        public string ExportServerUri = "";
    }
    public class PageBaseModel : PageModel
    {
        /// <summary>
        ///add by wyj at 2020-12-24
        /// </summary>
        public Token Token => HttpContext!=null? HttpContext.Items["token"] as Token:null;
        public string PageId { get; set; }
        public MenuId PageMenuID { get; set; } = MenuId.Empty;
        public string PageTitle { get; set; } = "";
        public string PageName { get; set; } = "";

        //=====end========//
        private HttpRequest request;
        public new HttpRequest  Request {
            get {
                if (base.Request != null) return base.Request;
                else return request;
            }
            set { 
                request = value;
            } }
        //public static string DbType = "SQL Server";
        public string Version = CPubVars.g_Version;

        public string JsonOperRights = "";
        public string JsonOperRightsOrig = "";
        public string JsonCompanySetting = "";
        public string JsonBranchRights = "";
        public string DepartmentID = "";
        public string DepartmentName = "";
     
        public string AllRegionID = "";
        public string OperRegions = "", OperRegionsName="";
        public string OperBrandsID = "", OperBrandsName="";
        public dynamic ObjOperRights = null;
        public string SheetViewRange = "";
        public bool OperRestrictBranches = false;

        public List<System.Dynamic.ExpandoObject> BranchRights = null;

        public string JsonPageSetting = "{}";
        public string JsonColumnsWidth = "{}";
        public string JsonOptionsRemembered = "{}";
        public string CoolieServerUri = "";
        public string ExportServerUri = "";
        public string ServerUri = "";
        public static string DbType = "Postgresql";
        public string company_id = "";
        public Dictionary<string, string> m_dicFldAreaCtrls = new Dictionary<string, string>();
        public Dictionary<string, DataItem> DataItems = new Dictionary<string, DataItem>();
        public Dictionary<string,string> DuplicateLabels=new Dictionary<string, string>();
        public string ObsBucketLinkHref = HuaWeiObsController.HuaWeiObs.BucketLinkHref;
        //public dynamic D = new System.Dynamic.ExpandoObject();
        //public Dictionary<string, DataItem> DataItems = (IDictionary<string, object>)D;

        public PartialViewModelBase PartialViewModel = new PartialViewModelBase();//modifed by wyj 2020-12-24
        // public Dictionary<string, DataGrid> Grids  = new Dictionary<string, DataGrid>();
        // public string m_selectFromSQL = "";

        // public string m_idFld = "",m_nameFld="", m_tableName = "";
        // public JObject record = new JObject(); 

        public string m_showFormScript = "", m_getDataItemsScript = "", m_setDataItemsByJsonScript="";
        public string m_setDataItemsFuncScript = "";
        //   public string m_saveCloseScript = "";
        // public string m_createGridScript = "";public string m_saveGridScript = "";
        //public bool m_bNewRecord = false;
        public string Database = "";
        public CMySbCommand m_cmd = null;
        public CMySbCommand cmd
        {
            get
            {
                return m_cmd;
            }
            set
            {
                m_cmd = value; 
                if (m_cmd != null)
                {
                    m_cmd.ActiveDatabase = Database; 
                }
                
            }
        }
        public string sec_user = "";
        public string m_userID = "", agent_staff_id = "", agent_id = "", m_userMobile = "", headimgurl = "", nickname = "", sex = "";
        public string m_fromPage = "";
        public string OperID = ""; 
        public string OperName = "";
        public string OperDepartPath = "";
        public string OperKey = "";
        public string BaiduKey = CPubVars.BaiduKey;

		public PageBaseModel(MenuId pageMenuID)
        {
            PageMenuID = pageMenuID;
        }
	  
		public ActionResult GetResultPage()
		{
			/*string url = this.Request.GetDisplayUrl();
			if (this.ServerUri!="" && !url.Contains(this.ServerUri, StringComparison.OrdinalIgnoreCase))
			{
                //if (this.ServerUri!=null && this.ServerUri.Contains("s13"))
                //{
					var requestUrl = $"{this.ServerUri}{Request.PathBase}{Request.Path}{Request.QueryString}";
					return this.Redirect(requestUrl);
				//} 
			}*/
            
			return this.Page();
		}

		public void GetOperKey()
        {
            //add by wyj 2020-12-24
            var pageID = CPubVars.RequestV(Request, "page");
            if (pageID.IsValid())
                PageId = pageID;
            //====end====//


            OperKey = CPubVars.RequestV(Request, "operKey");
            if (OperKey == null || OperKey == "")
            {
                throw (new Exception("operKey not specified in request"));
            }
            Security.GetInfoFromOperKey(OperKey, out string companyID, out string operID);
            company_id = companyID;
            OperID = operID;
            if (this.cmd != null)
            {
                this.cmd.company_id = companyID;
                this.cmd.oper_id = operID;
            }
      
            PartialViewModel.Version = CPubVars.g_Version;//modifed by wyj 2020-12-24
            PartialViewModel.OperKey = OperKey;//modifed by wyj 2020-12-24
            PartialViewModel.company_id = companyID;

        }

        public string GetPageName()
		{
            if (this.PageName == "")
            {
                string cls = this.GetType().ToString();
                this.PageName = cls.Split(".").Last().Replace("Model", "");                 
            }
            return this.PageName;
        }
        //add by wyj 2020-12-24
        public async Task GetOperRights(CMySbCommand cmd)
        {
            bool bHaveRegion = false;
            foreach (var kp in this.DataItems)
            {
                if (kp.Key == "other_region")
                {
                    bHaveRegion = true;
                }
                
            }
            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
SELECT rights,setting,oper_name,opt.options,cs.server_uri as coolie_server_uri,g_server.server_uri,
         g_company.export_server,es.server_uri export_server_uri,
o.depart_path,o.restrict_branches,o.depart_id,dep.depart_name,dep.mother_id as mum_depart_id,dep.brands_id,dep.brands_name,o.avail_brands_id,o.avail_brands_name, oper_regions,info_region.region_id as all_region_id

FROM info_operator o left join info_role r on o.company_id = r.company_id and o.role_id = r.role_id 
LEFT JOIN company_setting on o.company_id=company_setting.company_id
LEFT JOIN options_remembered opt on o.company_id=opt.company_id and o.oper_id=opt.oper_id
LEFT JOIN g_company on o.company_id=g_company.company_id
LEFT JOIN g_server on g_company.server_id=g_server.server_id
LEFT JOIN g_server cs on g_server.coolie_server=cs.server_id
LEFT JOIN g_server es on g_company.export_server=es.server_id
LEFT JOIN info_region on info_region.company_id={company_id} and mother_id=0
LEFT JOIN info_department dep on dep.company_id={company_id} and o.depart_id = dep.depart_id
where o.company_id={company_id} and o.oper_id = {OperID}";
  
            QQ.Enqueue("oper_right",sql);


            this.GetPageName();

            bool bHasBranchRestrict = false;
            foreach(var kp in this.DataItems)
            {
                if (kp.Key.Contains("branch_id"))
                {
                    if (kp.Value.Restrict != "")
                    {
                        bHasBranchRestrict = true;
                        break;
                    }
                }
            }

            if (bHasBranchRestrict)
            {
                sql = $"select br.branch_id,b.branch_name,sheet_x,sheet_t,sheet_xd,sheet_td,sheet_dr,sheet_jh,sheet_hh,sheet_dc,sheet_cg,sheet_ct,sheet_pd,query_stock from oper_branch_rights br left join info_branch b on br.branch_id=b.branch_id and b.company_id={company_id} where br.company_id={company_id} and oper_id = {OperID}";
                QQ.Enqueue("branch_rights", sql);
            }

            if (bHaveRegion)
            {
                sql = $"select region_id,region_name from info_region where company_id={company_id}";
                QQ.Enqueue("regions", sql);
            }
           
            if (this.PageName != "")
            {
                sql = $"select oper_id,setting,columns_width from page_set where company_id={company_id} and page_name='{this.PageName}' and (oper_id=0 or oper_id={OperID}) order by oper_id";
                QQ.Enqueue("page_set", sql); 
            }
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            JsonPageSetting = "{}";
            while (QQ.Count > 0)
            {
                string sqlName=QQ.Dequeue();
                if (sqlName == "oper_right")
                {
                    dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                    OperName = rec.oper_name;
                    OperDepartPath = rec.depart_path;
                    JsonOperRights = JsonOperRightsOrig = rec.rights;
                    ObjOperRights = JsonConvert.DeserializeObject(rec.rights);
                    if (bHasBranchRestrict)
                    {
                        OperRestrictBranches = ((string)rec.restrict_branches).ToLower() == "true";
                    }

                    if(string.IsNullOrEmpty(rec.avail_brands_id))
                    {
                        this.OperBrandsID = rec.brands_id;
                        this.OperBrandsName = rec.brands_name;
                    }
                    else
                    {
                        this.OperBrandsID = rec.avail_brands_id;
                        this.OperBrandsName = rec.avail_brands_name;
                    }

                    if (rec.mum_depart_id != "0")
                    {
                        DepartmentID = rec.depart_id;
                        DepartmentName = rec.depart_name;
                    }

                    AllRegionID = rec.all_region_id;
                    OperRegions = rec.oper_regions;
                   
                    if (ObjOperRights.delicacy!=null && ObjOperRights.delicacy.sheetViewRange != null)
                    {
                        SheetViewRange=ObjOperRights.delicacy.sheetViewRange.value;
                    }
                    

                    JsonCompanySetting = rec.setting;
                    if (JsonCompanySetting == "") JsonCompanySetting = "{}";
                    JsonOptionsRemembered = rec.options;
                    if (JsonOptionsRemembered == "" || JsonOptionsRemembered == "null") JsonOptionsRemembered = "{}";
                    JObject jsonOptions = JsonConvert.DeserializeObject<JObject>(JsonOptionsRemembered);
                    JObject values = (JObject) jsonOptions["page_" + this.GetPageName()];
					if (values != null)
					{
                        foreach(var kp in values)
						{
                             this.DataItems.TryGetValue(kp.Key,out DataItem di);
							if (di != null)
							{
                                JObject value =(JObject) kp.Value;
                                if(value["value"]!=null)
                                    di.Value =(string) value["value"];
                                if (value["label"] != null)
                                    di.Label = (string) value["label"];

                            }
						}
					}

                    CoolieServerUri = rec.coolie_server_uri;
                    ExportServerUri = CoolieServerUri;
                    ServerUri = rec.server_uri;
                    if (rec.export_server_uri != "") ExportServerUri = rec.export_server_uri;
                    else if(rec.export_server=="0") ExportServerUri = "";
#if (DEBUG)
               //     CoolieServerUri = ""; ExportServerUri = "";
#endif
                }
                else if (sqlName == "regions")
                {
                    var regions = CDbDealer.GetRecordsFromDr(dr, false);
                    string regionsName = "";
                    if (OperRegions != "")
                    {
                        string regionsID = OperRegions.Replace("[", "").Replace("]", "").Replace(" ", "").Replace("\r", "").Replace("\n", "").Trim();
                        if (regionsID != "")
                        {
                            string[] arr = regionsID.Split(',');
                            foreach (var r in arr)
                            {
                                dynamic region=regions.Find((r1) => ((dynamic)r1).region_id == r);
                                if (region != null)
                                {
                                    if (regionsName != "") regionsName += ",";
                                    regionsName += region.region_name;
                                }
                            }
                        }
    
                    }
                    this.OperRegionsName= regionsName;
                }
                else if (sqlName == "branch_rights")
                {
                    if (JsonOperRights.IsValid())
                    {
                        dynamic jsonRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRights);
                        if (OperRestrictBranches)
                        {
                            BranchRights = CDbDealer.GetRecordsFromDr(dr, false);
                        }
                    }
                   
                   // JsonBranchRights = JsonConvert.SerializeObject(recs);
                   // BranchRights= JsonConvert.SerializeObject(recs);
                }
                else if (sqlName == "page_set")
                {
                    foreach (var kp in DataItems)
                    {
                        kp.Value.OrigTitle = kp.Value.Title;
                        if (kp.Value.InnerTitle != "") kp.Value.OrigTitle = kp.Value.InnerTitle;
                    }
                    List<System.Dynamic.ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                    if (records != null)
                    {
                        foreach (dynamic rec in records)
                        {
                            if (rec.setting != "")
                                JsonPageSetting = rec.setting;
                            if (rec.columns_width != "")
                                JsonColumnsWidth = rec.columns_width;


                        }
                        if (JsonPageSetting != "")
                        {
                            dynamic pageSet = JsonConvert.DeserializeObject(JsonPageSetting);
                            if (pageSet.dataItems != null)
                            {
                                int orderIndex = 0;
                                foreach (var item in pageSet.dataItems)
                                {
                                    foreach (var kp in DataItems)
                                    {
                                        if (item.datafield == kp.Key)
                                        {
                                            kp.Value.OrderIndex = orderIndex;
                                            if (item.hidden != null)
                                            {
                                                if (((string)item.hidden).ToLower() == "true")
                                                {
                                                    if(!kp.Value.ValueFilled)  
                                                       kp.Value.Hidden = true;
                                                } 
                                                else if (((string)item.hidden).ToLower() == "false")
                                                    kp.Value.Hidden = false;
                                            }
                                            if ((string)item.text != "")
                                            {
                                                if (kp.Value.Title != "")//没有标题的，不要变更标题，比如支付方式
                                                    kp.Value.Title = item.text;
                                            }
                                            else
                                            {
                                                item.text = kp.Value.Title;
                                            }
                                            break;
                                        }
                                    }
                                    orderIndex++;
                                }
                            }
                            var tmp = from objDic in DataItems orderby objDic.Value.OrderIndex select objDic;
                            DataItems = tmp.ToDictionary(pair => pair.Key, pair => pair.Value);
                        }
                    }
                }
            }
            QQ.Clear();

			string url = this.Request.GetDisplayUrl();
			if (
			  !url.Contains("localhost") && !url.Contains("127.0.0.1") && !url.Contains("192.168.") && this.ServerUri != "" &&

			  this.ServerUri!=null && this.ServerUri.Contains("s13") && !url.Contains(this.ServerUri, StringComparison.OrdinalIgnoreCase))
			{
				var requestUrl = $"{this.ServerUri}{Request.PathBase}{Request.Path}{Request.QueryString}";
				this.Response.Redirect(requestUrl);				 
			}

			if (PageMenuID!=MenuId.Empty)
            {
                //MenuId page = (MenuId)Enum.Parse(typeof(MenuId), PageId);
                var t = PageMenuID.GetType().GetField(PageMenuID.ToString());
                var g = t.GetCustomAttribute<EnumExtAttribute>();
                MenuId group = g.Group;
                JsonOperRights += $".{group}.{PageMenuID}";
            } 

            PartialViewModel.JsonOperRights = JsonOperRights;
            PartialViewModel.JsonOperRightsOrig = JsonOperRightsOrig;
            PartialViewModel.JsonCompanySetting = JsonCompanySetting;
            PartialViewModel.JsonOptionsRemembered = JsonOptionsRemembered;
            
            PartialViewModel.OperName = OperName;
            PartialViewModel.OperDepartPath = OperDepartPath;
            PartialViewModel.PageName = this.PageName;
            PartialViewModel.JsonPageSetting = JsonPageSetting;
            PartialViewModel.JsonColumnsWidth = JsonColumnsWidth;
#if (!DEBUG)
            PartialViewModel.CoolieServerUri = CoolieServerUri; 
            PartialViewModel.ExportServerUri = ExportServerUri; 
            
#endif



        }
        //==end==//
        public static string GetRealSQL(string sql, string company_id, string tableName = "", string idFldValue = "")
        {
            if (idFldValue != "") sql = sql.Replace("~ID", idFldValue);
            // sql = sql.Replace("~companyID", company_id);

            //if (!sql.Contains(" company_id=") && !sql.Contains(" company_id =") && !sql.Contains(".company_id=") && !sql.Contains(".company_id ="))
            if (!sql.Contains("~COMPANY_ID",StringComparison.OrdinalIgnoreCase))
            {
                if (tableName == "")
                {
                    int f = sql.IndexOf(" from ",0,StringComparison.OrdinalIgnoreCase);
                    if (f > 0)
                    {
                        int f1 = sql.IndexOf(" ", f + 7);
                        if (f1 > f)
                        {
                            tableName = sql.Substring(f + 6, f1 - f - 6).Trim();
                            if (f1 + 2 <= sql.Length)
                            {
                                int f2 = sql.IndexOf(" ", f1 + 2);
                                if (f2 > f1)
                                {
                                    string alis = sql.Substring(f1 + 1, f2 - f1 - 1).Trim();
                                    alis = alis.Trim().ToLower();
                                    if (alis == "where" || alis == "left" || alis == "order") alis = "";
                                    if (alis != "") tableName = alis;
                                }
                            }
                        }

                    }
                }
                string sqlNoOrder = sql, sqlOrder = "";
                int n = sql.LastIndexOf(" order by ");
                if (n > 0)
                {
                    sqlNoOrder = sql.Substring(0, n);
                    sqlOrder = sql.Substring(n, sql.Length - n);
                }
                else
                {
                    n = sql.LastIndexOf(" limit ");
                    int n1 = sql.LastIndexOf(" offset ");
                    if (n1 < n) n = n1;
                    if (n > 0)
                    {
                        sqlNoOrder = sql.Substring(0, n);
                        sqlOrder = sql.Substring(n, sql.Length - n);
                    }
                }
                if (tableName != "") tableName += ".";

                if (!sqlNoOrder.Contains(" where "))
                {
                    sqlNoOrder = sqlNoOrder + $" where {tableName} company_id={company_id}";
                }
                else
                    sqlNoOrder = sqlNoOrder + $" and {tableName} company_id={company_id}";
                sql = sqlNoOrder + sqlOrder;
            }
            return sql;
        }
        public virtual async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {

        }
        public virtual async Task SetPageCommonRights(CMySbCommand cmd)
        {

        }
        public async Task GetScriptsForDataItems(CMySbCommand cmd, bool bGetFldAreaCtrlsHtml = false, bool bPostEmptyFlds = true)
        {
            foreach(var kp in DataItems)
            {
                if(kp.Value.LabelFld!="" && kp.Key == kp.Value.LabelFld)
                {
                    throw new Exception("LabelFld should not be same as the key "+ kp.Key);
                }
            }

            await GetOperRights(cmd);//add by wyj 2020-12-24 


            await OnPageInitedWithDataAndRight(cmd);
            await SetPageCommonRights(cmd);


            m_getDataItemsScript += @"
var s_value=''; 
function getValidValue(v){
  v=v.replace('`','')
  v=v.replace(`'`,'')
  return v
}
";
            m_showFormScript += "if(!window.g_pageSetting) window.g_pageSetting={}; window.g_pageSetting.dataItems =[];";
            
            ScriptVar scriptVar = new ScriptVar();


            // foreach (KeyValuePair<string, DataItem> key in DataItems)
            m_showFormScript += @"window.g_initFormData={};";
           

            m_setDataItemsFuncScript += "window.setFormDataItems=function(formData){";

            #region Update dataItems for limiting value for branch ,region,department
            string[] keys = DataItems.Keys.ToArray();
            for (int i = 0; i < keys.Length; i++)
            {
                string key = keys[i];
                var dataItem = DataItems[key];
                string availValues = "";
                if (dataItem.SqlForOptions != "")
                {
                    if (BranchRights != null && key.Contains("branch_id"))
                    {
                        foreach (dynamic br in BranchRights)
                        {
                            JObject jbr = JObject.FromObject(br);
                            if (dataItem.Restrict != "")
                            {
                                //Restrict 可能为 sheet_x,sheet_t,sheet_xd,sheet_td,sheet_dr,sheet_dc,sheet_cg,sheet_ct,sheet_pd,query_stock
                                string[] arr = dataItem.Restrict.Split(',');
                                foreach(var ss in arr)
                                {
                                    string restrictValue = (string)jbr.GetValue(ss);
                                    if (restrictValue.IsValid() && restrictValue.ToLower() == "true")
                                    {
                                        if (!("," + availValues + ",").Contains("," + br.branch_id + ","))
                                        {
                                            if (availValues != "") availValues += ",";
                                            availValues += br.branch_id;
                                            if(dataItem.AvailLabels!="") dataItem.AvailLabels += ",";
                                            dataItem.AvailLabels += br.branch_name;
                                        }                                        
                                    }
                                }
                                
                            }
                        }
                        
                        dataItem.AvailValues = availValues;
                        //if (availValues != "") availValues = HttpUtility.UrlEncode(availValues);
                    }
                    else if (key == "depart_path")
                    {
                        if (DepartmentID != "")
                        {
                            if (SheetViewRange == "department")
                            {
                                //availValues = DepartmentID;
                                if(string.IsNullOrEmpty(dataItem.Value))
                                {
                                    dataItem.Value = DepartmentID;
                                    dataItem.Label = DepartmentName;
                                }
                            
                                dataItem.Disabled = true;
                                dataItem.Hidden = false;
                            }
                        }
                    }
                    else if (key == "department_id")
                    {
                        if (DepartmentID != "")
                        {
                            if (SheetViewRange == "department")
                            {
                                //availValues = DepartmentID;
                                if (string.IsNullOrEmpty(dataItem.Value))
                                {
                                    dataItem.Value = DepartmentID;
                                    dataItem.Label = DepartmentName;
                                }
                                
                                dataItem.Disabled = true;
                                dataItem.Hidden = false;
                            }
                        }
                    }
                    else if (key == "brand_id" || key == "item_brand")
                    {
                        if (OperBrandsID != "")
                        {
                            dataItem.AvailValues = OperBrandsID;
                            dataItem.AvailLabels = OperBrandsName;
                            if (dataItem.InQueryPage)
                            {
                                dataItem.Hidden = false;
                            }
                        }
                    }
                    else if (key == "other_region")
                    {
                        if (OperRegions != "" && OperRegions != "[]")
                        {
                            string regions = OperRegions.Replace("[", "").Replace("]", "").Replace(" ", "").Replace("\r", "").Replace("\n", "").Trim();
                            if (regions != "")
                            {
                                string[] arr = regions.Split(',');
                                bool bHaveAllRegion = false;
                                foreach (var r in arr)
                                {
                                    if (r.Trim() == AllRegionID)
                                    {
                                        bHaveAllRegion = true; break;
                                    }
                                }
                                if (!bHaveAllRegion)
                                {
                                    //availValues = Alvalue='{dataItem.AvailValues}';lRegionID + "," + regions;
                                    dataItem.AvailValues = regions;
                                    dataItem.AvailLabels = OperRegionsName;
                                 //   dataItem.AvailValues
                                    /*if (!this.DataItems.ContainsKey(key + "_hidden"))
                                    {
                                        DataItem regionHidden = new DataItem();
                                        regionHidden.HideOnLoad = true;
                                        regionHidden.Hidden = true;
                                        regionHidden.Value = regions;                                        
                                        regionHidden.FldArea = "divHead";
                                        this.DataItems.Add(key + "_hidden", regionHidden);
                                    }*/
                                }
                            }
                        }
                    }

                    
                    if (dataItem.AvailLabels != "")
                    {
                        if (dataItem.InQueryPage)
                        {
                            dataItem.PlaceHolder = dataItem.AvailLabels;
                        }
                       
                    }
                }

            }
            #endregion

            await GetScriptsForGroup(cmd, DataItems, bGetFldAreaCtrlsHtml, bPostEmptyFlds, scriptVar);
            m_setDataItemsFuncScript += @"}
window.resetFormData=function(){
  setFormDataItems(g_initFormData) 
}
resetFormData()
";
            m_showFormScript += m_setDataItemsFuncScript;
            m_getDataItemsScript = "clearInputError();" + m_getDataItemsScript;

            foreach (var kp in scriptVar.DicFldArea)
            {
                var divClearFloat = @"<div style = ""float:none;height:0px; clear:both;"" ></div>";
                m_showFormScript += $"$('#{kp.Value}').append('{divClearFloat}');";
            }

            m_showFormScript += $@"
window.onHideGroupBtnClicked=function(grp,cls,event){{
    var $grp=$('[hideGroup='+grp+']');
    $grp.css('display','none');
    for(var i=0;i<$grp.length;i++){{
        $($grp[i]).find('input').val('');
        $($grp[i]).find('input').trigger('input')
    }} 
    $('#btnGrpAdd_'+cls).css('display','block');
}};
window.onShowGroupBtnClicked=function(cls,event){{
    var $cls=$('[hideClass='+cls+']');
    var grpName='';var hiddenCount=0;
    for(var i=0;i<$cls.length;i++){{
        if($cls[i].style.display=='none'){{
            if(!grpName) grpName=$cls[i].getAttribute('hideGroup');
            var curGrpName=$cls[i].getAttribute('hideGroup');
            if(curGrpName==grpName){{
                $cls[i].style.display='block';
            }}
            else{{
                hiddenCount++;
            }}
        }}
    }} 
    if(hiddenCount==0) $('#btnGrpAdd_'+cls).css('display','none');
    window.onresize();
}}; 

document.body.onkeydown = function (e) {{
if(!e) return
if (e.keyCode == 13) {{
    var searchedEle = new Array();
    var arrIgnoredEle = new Array();
    arrIgnoredEle.push('jqxgrid');
    var eleInArray = function(arr, ele) {{
        var bExist = false;
        $.each(arr, function() {{
            if (this == ele || this == ele.id)
                bExist = true;
            return;
        }});
        return bExist;
    }};
    var focusNext = function(ele, bFromMe,count) {{
        if(count>50) 
            return;
        var next = ele.nextSibling;
        if (bFromMe) next = ele;
        searchedEle.push(next);
        if (next)
        {{
            if(next.id=='jqxgrid'){{
                var sheet_type = $('#sheet_type').val()
                if(sheet_type=='SHEET_FEE_OUT'|| sheet_type == 'SHEET_FEE_IN'){{
                    $('#jqxgrid').jqxGrid('selectcell',0,'fee_sub_id'); 
                    $('#jqxgrid').jqxGrid('begincelledit', 0, 'fee_sub_id');
                }}else{{
                    $('#jqxgrid').jqxGrid('selectcell',0,'item_id'); 
                    $('#jqxgrid').jqxGrid('begincelledit', 0, 'item_id');
                }}
                e.stopPropagation();
            }} 
            else if(next.style && next.style.display=='none'){{
                    return focusNext(next,false,count+1); 
            }} 
            else if(next.nodeName=='svg'){{
                    return focusNext(next,false,count+1); 
            }} 
            else if(next.nodeName=='barcode_input'){{
                    return focusNext(next,false,count+1); 
            }} 
            else if ('BUTTON'.indexOf(next.nodeName.toUpperCase())>=0 && next.focus && $(next).is (':visible') && !$(next).is (':disabled')) {{
                    setTimeout(function(){{
                        next.focus();
                    }},500);
                    e.stopPropagation();
                    return next;
            }}
            else if (next.childNodes.length == 0)
            {{
                if ('INPUT,BUTTON'.indexOf(next.nodeName.toUpperCase())>=0 && next.focus && $(next).is (':visible') && !$(next).is (':disabled')) {{
                    next.focus();
                    try{{
                       next.selectionStart = 0;
                    }}
                    catch(ee){{}}
                    return next;
                }}
                else {{
                    return focusNext(next,false,count+1);
                }}
            }}
            else
            {{ 
                var okele = null;
                var i;
                for (i = 0; i < next.childNodes.length; i++)
                {{
                    var curEle = next.childNodes[i];
                    if (!eleInArray(searchedEle, curEle) && !eleInArray(arrIgnoredEle, curEle))
                    {{
                        okele = focusNext(curEle, true,count+1);
                        if (okele)
                            break;
                    }}
                }}
                if (okele)
                {{
                    return okele;
                }}
                else
                {{
                    return focusNext(next,false,count+1);
                }}
            }}
        }}
        else if (!bFromMe)
        {{
            next = ele.parentElement;
            if (ele.parentElement == null)
                return null;
            if (next && next.tag == 'body')
            {{
                return null;
            }}
            var nxt = focusNext(next,false,count+1);
            return nxt;
        }}
    }}
    if(window.onEnterKeyDown){{
        window.onEnterKeyDown(document.activeElement)
                    
    }}
    var pNode=document.activeElement.parentNode
    if(pNode && pNode.id){{
        if('btnApprove,barcode_input'.indexOf(pNode.id)>=0) return
    }}
                    
		
    focusNext(document.activeElement,false,1); 
}}
}};

window.setPageReadonly=function(bReadonly){{ 
    {scriptVar.SetDataItemsReadOnlyScript} 
    if($('#jqxgrid').length){{
        $('#jqxgrid').jqxGrid({{editable:!bReadonly}})
    }}
    if($('#jqxgridB').length){{
        $('#jqxgridB').jqxGrid({{editable:!bReadonly}})
    }}
}}

             ";
        }
        
        class ScriptVar
        {
            public int CtrlID;
            public string SetDataItemsReadOnlyScript = "";
            public Dictionary<string, string>  DicFldArea = new Dictionary<string, string>();
            public int LoopTimes = 0;
        }
        public void UpdateDataItemsByDataLimit()
        {
          
        }
        private async Task GetScriptsForGroup(CMySbCommand cmd, Dictionary<string, DataItem> subDataItems, bool bGetFldAreaCtrlsHtml,bool bPostEmptyFlds, ScriptVar scriptVar)
        {
           
            Dictionary<string, string> dicHideGroup = new Dictionary<string, string>();
            Dictionary<string, string> dicHideGroup1 = new Dictionary<string, string>();
            
            string preHideGroup = "";
            string preHideClass = ""; string preFldArea = "";
            string[] keys = subDataItems.Keys.ToArray();
    
            Dictionary<string, string> vars = null;
            string laterScript = "";
            int n = 0;
            while (true)
            {
                scriptVar.LoopTimes++;
                if (scriptVar.LoopTimes > 1000)
                {
                    throw (new Exception("GetScriptsForGroup encounter dead loop"));
                }

                string key = ""; DataItem dataItem = null;
                if (n < keys.Length)
                {
                    key = keys[n]; 
                    dataItem = subDataItems[key];
                    n++;
                    if (dataItem.SubGroup != null)
                    {
                        await GetScriptsForGroup(cmd, dataItem.SubGroup, bGetFldAreaCtrlsHtml, bPostEmptyFlds, scriptVar);
                        continue;
                    } 
                    dataItem.CtrlID = scriptVar.CtrlID;
                }
              
                scriptVar.CtrlID++;

                string curHideGroup = ""; string curHideClass = "";
                if (dataItem != null)
                {
                    curHideGroup = dataItem.HideGroup;
                    curHideClass = dataItem.HideClass;
                }

                if (preHideGroup != "" && preHideGroup != curHideGroup)
                {
                    if (bGetFldAreaCtrlsHtml && preFldArea != "")
                    {
                        if (!dicHideGroup1.ContainsKey(preHideClass + "_" + preHideGroup))
                        {
                            dicHideGroup1.Add(preHideClass + "_" + preHideGroup, "");
                        }
                        if (dicHideGroup1.Count >= 2)
                        {
                            string curCtrl = $"<div style=\"display:none;\" hideGroup=\"{preHideGroup}\" hideClass=\"{preHideClass}\"><img onclick=\"onHideGroupBtnClicked('{preHideGroup}','{preHideClass}')\" src=\"/images/close.png\"/></div>";
                            m_showFormScript += $"$('#{preFldArea}').append(`{curCtrl}`);";
                        }
                    }
                }
                if (preHideClass != "" && preHideClass != curHideClass)
                {
                    if (bGetFldAreaCtrlsHtml && preFldArea != "" && !dataItem.HideOnLoad)
                    {
                        string curCtrl = $"<div id=\"btnGrpAdd_{preHideClass}\"><img onclick=\"onShowGroupBtnClicked('{preHideClass}')\" src=\"/images/add.png\"/></div>";
                        m_showFormScript += $"$('#{preFldArea}').append(`{curCtrl}`);";
                    }
                }

                if (dataItem != null)
                {

                    if (dataItem.Value.Contains("\\"))
                    {
                        dataItem.Value = dataItem.Value.Replace("\\", "/");
                    }
                    string labelFld = dataItem.GetJsLabelFld(key);

                    string v = dataItem.Value;

                    if (v.IndexOf("`") > -1) v = v.Replace("`", "\\`");
                    if (!v.StartsWith("'")) v = "`" + v + "`";

                    if (key == "depart_path")
                    {

                    }
                    #region  Init g_initFormData
                    if (v.Contains("2025-06-1"))
                    {

                    }
                    m_showFormScript += $"window.g_initFormData.{key}={v}||'';";
                    if (labelFld != "")
                    {
                        if (!DuplicateLabels.ContainsKey(labelFld))
                        {
                            DuplicateLabels.Add(labelFld, labelFld);
                        }
                        else
                        {
#if DEBUG
                            throw new Exception("数据项Label重复:" + labelFld);
#endif  
                        }
                        m_showFormScript += $"window.g_initFormData.{labelFld}='{dataItem.Label}';";
                    }
#endregion
                    if (key == "seller_id")
                    {

                    }
                    string ctrl = "jqxInput";
                    if (dataItem.CtrlType != "") ctrl = dataItem.CtrlType;
                    if (dataItem.FldArea != "" && (ctrl == "jqxInput" || ctrl == "jqxDropDownTree" || ctrl == "hidden" || ctrl == "jqxDateTimeInput" || ctrl == "jqxCheckBox" || dataItem.Hidden))
                    {
                        if (!scriptVar.DicFldArea.ContainsKey(dataItem.FldArea))
                        {
                            scriptVar.DicFldArea.Add(dataItem.FldArea, dataItem.FldArea);
                        }
                        //string areaCtrl = ""; 
                        string curCtrl;
                        if (ctrl == "hidden")
                        {
                            curCtrl = $"<input type=\"hidden\" id=\"{key}\"/>";
                        }
                        else if (!dataItem.UseJQWidgets)
                        {
                            curCtrl = $"<input id=\"{key}\"/>";
                        }
                        else
                        {
                            string lblStyle = ""; string txtStyle = "";
                            if (dataItem.Width != "")
                            {
                                string wd = dataItem.Width;
                                if (!wd.EndsWith("px")) wd += "px";
                                txtStyle = $"width:{wd};";
                            }

                            if (ctrl == "jqxCheckBox") txtStyle += "margin-top:10px;";
                            if (txtStyle != "") txtStyle = $" style=\"{txtStyle}\"";
                            if (dataItem.Title == "")
                            {
                                lblStyle = " style=\"width:20px;\" ";
                            }
                            else
                            {
                                if(dataItem.TitleWidth != "")
                                {
                                    string twd = dataItem.TitleWidth;
                                    if(!twd.EndsWith("px")) twd += "px";
                                    lblStyle = $"width:{twd};";
                                }
                                if (lblStyle != "") lblStyle = $" style=\"{lblStyle}\"";
                            }
                            string className = dataItem.ClassName;
                            string groupStyle = "";
                            string hideGroupAttr = "";
							if (key == "payway1_id")
							{

							}
                            if (dataItem.HideGroup != "" && dataItem.HideClass != "")
                            {
                                hideGroupAttr = $" hideGroup=\"{dataItem.HideGroup}\" hideClass=\"{dataItem.HideClass}\"";
                                if (!dicHideGroup.ContainsKey(dataItem.HideClass + "_" + dataItem.HideGroup))
                                {
                                    dicHideGroup.Add(dataItem.HideClass + "_" + dataItem.HideGroup, "");
                                }
                                if (dicHideGroup.Count >= 2 && !key.Contains("payway1"))
                                { 
                                    laterScript += $"$('#div_{key}').css('display','none');";
                                }

                                if (className != "") className += " ";
                                className += dataItem.ClassName;
                            }
                            if (dataItem.Hidden || (dataItem.HideIfEmpty && dataItem.Value == ""))
                            {
                                 laterScript += $"$('#div_{key}').css('display','none');";
                            }
                               
                            if (ctrl == "jqxCheckBox")
                                curCtrl = $"<div id=\"div_{key}\" {groupStyle} {hideGroupAttr} class=\"{className}\"><div style=\"width:30px;\"></div><div {txtStyle}><div  {txtStyle} id=\"{key}\"><label>{dataItem.Title}</label></div></div></div>";
                            else
                                curCtrl = $"<div id=\"div_{key}\" {groupStyle} {hideGroupAttr} class=\"{className}\"><div {lblStyle}><label>" + dataItem.Title + $"</label></div><div {txtStyle}><div {txtStyle} id=\"{key}\"></div></div></div>";

                        }

                        bool bAddToCustomForm = false;
                        if (bGetFldAreaCtrlsHtml && dataItem.FldArea != "")
                        {
                            m_showFormScript += $"$('#{dataItem.FldArea}').append('{curCtrl}');";
                        
                            bAddToCustomForm = true;
                        }
                        if (dataItem.CtrlType == "button") bAddToCustomForm = true;
                        string title = dataItem.Title; 
                        if (title == "") title = dataItem.InnerTitle;
                        if (title == "") title = key;
                        if (!dataItem.HideOnLoad && dataItem.CtrlType != "hidden" && title != "" && bAddToCustomForm)
                        {
                            m_showFormScript += $"window.g_pageSetting.dataItems.push({{datafield:'{key}',text:'{title}',origText:'{dataItem.OrigTitle}', alwaysShow:{dataItem.AlwaysShow.ToString().ToLower()}, hidden:{dataItem.Hidden.ToString().ToLower()},visible:{(!dataItem.Hidden).ToString().ToLower()}}});";
                        }

                    }
                    if (dataItem.AutoRemember)
                    {
                        m_showFormScript += @$"
$('#{key}').on(""change"",function(){{
   if(!window.changedItems) window.changedItems={{}}
   window.changedItems[{key}]=true

}})";


                    }
                    vars = new Dictionary<string, string>();
                    void SetVarsByDataItems(string flds)
                    {
                        Type type = typeof(DataItem);
                        string[] arr = flds.Split(",");
                        foreach (string s in arr)
                        {
                            string fld = s.Trim();
                            PropertyInfo prop = type.GetProperty(fld);
                            vars[fld] = prop.GetValue(dataItem).ToString();
                            if (vars[fld] == "True" || vars[fld] == "False")
                            {
                                vars[fld] = vars[fld].ToLower();
                            }
                        }
                    }

                    SetVarsByDataItems(@"ButtonUsage,BorderShape,datafields,SearchFields,MaxRecords,Url,Source,DropDownWidth,DropDownHeight,Checkboxes,MumSelectable,LabelFld,Value,Label,TreePathFld,TreePath");
                    vars["LabelFld"] = labelFld;
                    vars["JSBeforeCreate"] = dataItem.JSBeforeCreate;
                  
                    string showFormScript = ""; string getDataItemsScript = "";
                    string setDataItemsFuncScript = "";
                     
                    string controllerName = this.GetType().FullName.Replace("Model", "").Split(".").Last();
                    #region 初始化jqxInput组件
                  
                    showFormScript += @"~JSBeforeCreate "+ "\r\n";
                    if (dataItem.UseJQWidgets)
                    {
                        vars["disabled"] = ""; vars["readonly"] = "";
                        if (dataItem.Disabled)
                            vars["disabled"] = $",disabled:{(dataItem.Disabled ? "true" : "false")}";
                        if (dataItem.Readonly)
                            vars["readonly"] = $",readonly:{(dataItem.Readonly ? "true" : "false")}";

                        if (dataItem.SqlForOptions != "")
                        {     
                            vars["Url"] = $"../api/{controllerName}/GetDataItemOptions?operKey={this.OperKey}&dataItemName={key}";
                            if (dataItem.AvailValues != "") vars["Url"] += $"&availValues={dataItem.AvailValues}";

                            if (dataItem.OtherQueryStrForOptions != "")
                                vars["Url"] += "&" + dataItem.OtherQueryStrForOptions;
                        }

                        vars["condiDataItem"] = dataItem.CONDI_DATA_ITEM;
                        if (ctrl == "jqxInput" || ctrl == "jqxDropDownTree" || ctrl == "jqxTree")
                        {
							if (dataItem.Source.StartsWith("["))
							{
                               

                            }
                            if (dataItem.SqlForOptions != "" && dataItem.GetOptionsOnLoad)
                            {
                                string sqlOpt = dataItem.SqlForOptions;
								if (sqlOpt.Contains("~BRANDS_CONDI_CLASS"))
								{
                                    string brandCondi = "";
									if (this.OperBrandsID != "")
									{
									    brandCondi = $" and (brand_id in ({this.OperBrandsID}) ";
										if (this.OperBrandsID.Contains("-1")) brandCondi += " or brand_id is null";
										brandCondi += " or mother_id =0) ";
										dataItem.SqlForOptions = sqlOpt;
									}
									sqlOpt = sqlOpt.Replace("~BRANDS_CONDI_CLASS", brandCondi);
								}

                                dataItem.SqlForOptions = sqlOpt;

                                /*
								if (sqlOpt.Contains("[") && sqlOpt.Contains("~"))
                                {
                                    int n1=sqlOpt.IndexOf("[");
                                    if (n1 > 0)
                                    {
										int n2 = sqlOpt.IndexOf("]",n1+1);
                                        string sqlD = sqlOpt.Substring(n1 + 1, n2 - 1 - (n1 + 1) + 1);
                                    
                                        if (sqlD.Contains("~OPER_BRAND"))
                                        {
											string leftPart = sqlOpt.Substring(0,n1);
											string rightPart = sqlOpt.Substring(n2 + 1, sqlOpt.Length - (n2 + 1));
											if (this.OperBrandsID != "")
                                            {
                                                sqlD = sqlD.Replace("~OPER_BRANDS", this.OperBrandsID);
												sqlOpt = leftPart + sqlD + rightPart;
											}
                                            else
                                            { 
                                                sqlOpt = leftPart + rightPart;

											}
                                            dataItem.SqlForOptions = sqlOpt;
										}
                                    }
                                }
								*/

                                string source = await GetDataItemOptions(cmd,this.OperKey, DataItems, key, null, null,"");
                                JArray lstOptions = (JArray)JsonConvert.DeserializeObject(source);
                            //    dataItem.DropDownOptions = lstOptions;
                                if ((dataItem.FirstOptionAsDefault || dataItem.InitValue!="") && dataItem.Value == "")
                                {
                                    for (int i = 0; i < lstOptions.Count; i++)
                                    {
                                        dynamic opt = lstOptions[i];

                                        if (opt["hide"] == "t") continue;
                                        if(dataItem.InitValue != "" && dataItem.InitValue!=(string)opt.v)
                                        {
                                            continue;
                                        }
                                        dataItem.Value = opt.v;
                                        dataItem.Label = opt.l;
                                        vars["Value"] = dataItem.Value;
                                        vars["Label"] = dataItem.Label;
                                        m_showFormScript += $"\r\n window.g_initFormData['{key}'] = `{dataItem.Value}`;";
                                        m_showFormScript += $"window.g_initFormData['{dataItem.GetJsLabelFld(key)}'] = `{dataItem.Label}`;";
                                      
                                        break;
                                    }

                                }
                                else if (dataItem.Necessary && dataItem.Value == "")
                                {
                                    List<dynamic> validOptions = new List<dynamic>();
                                    for (int i = 0; i < lstOptions.Count; i++)
                                    {
                                        dynamic opt = lstOptions[i];
                                        if (opt["hide"] == "t") continue; 
                                        validOptions.Add(opt);
                                    }
                                    if (validOptions.Count == 1)
                                    {
                                        dynamic opt = validOptions[0];
                                        vars["Value"] = opt.v;
                                        vars["Label"] = opt.l;
                                        m_showFormScript += $"\r\n window.g_initFormData['{key}'] = `{opt.v}`;";
                                        m_showFormScript += $"window.g_initFormData['{dataItem.GetJsLabelFld(key)}'] = `{opt.l}`;";

                                    }
                                }

                                vars["Source"] = source;
                                vars["Url"] = "";

                            }
                        }
                        if (ctrl == "jqxInput")
                        {
                            vars["showHeader"] = dataItem.ShowDropDownColumnsHeader.ToString().ToLower();
                            vars["KeepNoValueLabel"] = dataItem.KeepNoValueLabel.ToString().ToLower();
                            showFormScript += $@"$('#~ItemID').jqxInput({{dropDownWidth:~DropDownWidth, dropDownHeight:~DropDownHeight,keepNoValueLabel:~KeepNoValueLabel,buttonUsage: '~ButtonUsage',checkboxes:~Checkboxes~disabled~readonly, borderShape: '~BorderShape', showHeader:~showHeader,displayMember: 'l',
valueMember: 'v',datafields: ~datafields, searchFields: ~SearchFields,placeHolder:'~PlaceHolder', maxRecords: ~MaxRecords, url: '~Url',condiDataItem:'~condiDataItem', source: ~Source}});" + "\r\n";
                            if (dataItem.ButtonUsage == "list")
                            {
                             //   showFormScript += $@"$('#~ItemID').find('input').on('focus',function(){{$('#~ItemID').jqxInput('suggest',true)}});";

                            }
                        }
                        else if (ctrl == "jqxDropDownTree")
                        {
                            showFormScript += @"$('#~ItemID').jqxDropDownTree({dropDownWidth:~DropDownWidth, dropDownHeight:~DropDownHeight, url:'~Url',source:~Source,checkboxes:~Checkboxes,mumSelectable:~MumSelectable~disabled~readonly, placeHolder:'~PlaceHolder'});" + "\r\n";
                        }
                        else if (ctrl == "jqxDateTimeInput")
                        {
                            string ss = "";
                            if (dataItem.ShowTime){
                                ss = " HH:mm";
                                if (dataItem.ShowSeconds) ss += ":ss";
                            }
                            showFormScript += $@"$('#~ItemID').jqxDateTimeInput({{culture: 'zh-CN',placeHolder:'~PlaceHolder', formatString: 'yyyy-MM-dd{ss}'~disabled~readonly}});" + "\r\n";
                        }
                        else if (ctrl == "jqxCheckBox")
                        {
                            showFormScript += $@"$('#~ItemID').jqxCheckBox({{locked:false~disabled}});" + "\r\n";
                        }
                        else if (ctrl == "jqxTree")
                        {
                            //if (dataItem.LabelFld == "") vars["LabelFld"] = key + "_LABEL";
                            vars["classNodeRecords"] = await GetDataItemOptions(cmd,this.OperKey, DataItems, key, null, null, dataItem.AvailValues);
                            showFormScript += @"var zNodes = ~classNodeRecords;
                        var treeSource =
                         {
                         datatype: 'json',
                         datafields: [
                             { name: 'v' },
                             { name: 'pv' },
                             { name: 'l' },
                             { name: 'z' },
                             { name: 'status' }
                         ],
                         id: 'id',
                         localdata: zNodes
                       }; 
                        var dataAdapter = new $.jqx.dataAdapter(treeSource); 
                        dataAdapter.dataBind(); 
                        var records = dataAdapter.getRecordsHierarchy('v', 'pv', 'items', [{ name: 'l', map: 'label' }, { name: 'v', map: 'value' }]);
                        $('#~ItemID').jqxTree({source:records});
                     
                        var margin=0
                      
                        var marginTop= $('#~ItemID').css('margin-top')
                        if(marginTop) marginTop=parseInt(marginTop.replace('px','')); else marginTop=margin;
                        var marginBottom= $('#~ItemID').css('margin-bottom')
                        if(marginBottom) marginBottom=parseInt(marginBottom.replace('px','')) ; else marginBottom=margin;
                        margin=marginTop+marginBottom
                         
                    

                        $('#~ItemID')[0].style.height = $('#~ItemID')[0].offsetHeight +'px';
                        $('#~ItemID').jqxTree('expandAll');
                         
                          " + "\r\n";

                        }
                        if ("jqxInput,jqxDropDownTree,jqxDateTimeInput".Contains(ctrl))
                            scriptVar.SetDataItemsReadOnlyScript += $"$('#{key}').{ctrl}({{readonly:bReadonly}});";
                        if (dataItem.JSAfterCreate != "")
                        {
                            showFormScript += dataItem.JSAfterCreate;
                        }

                    }

                    #endregion
                    if (key == "depart_path")
                    {

                    }
                    string ifCondi = "true";
                    if (dataItem.TextAsValue)
                    {
                        //showFormScript += @"$('#~ItemID').find('input').val(`~Value`);" + "\r\n";
                        setDataItemsFuncScript += @"$('#~ItemID').find('input').val(formData.~ItemID);" + "\r\n";
                        if (!bPostEmptyFlds) ifCondi = "s_value";
                        getDataItemsScript += @" 
var input=$('#~ItemID').find('input');
if(input.length>0) {
    input=input[0];     
    s_value=input.value;
    s_value=s_value.trim();
    if(s_value==',') s_value=''
}

//s_value = $('#~ItemID').find('input').val();
if (~ifCondi) {formFlds.~ItemID = s_value;}" + "\r\n";
                    }
                    else if (!dataItem.UseJQWidgets)
                    {
                        //showFormScript += @"$('#~ItemID').val(`~Value`);$('#~ItemID').text(`~Value`);" + "\r\n";
                        setDataItemsFuncScript += @"$('#~ItemID').val(formData.~ItemID);$('#~ItemID').text(formData.~ItemID);" + "\r\n";

                        
                        if (!bPostEmptyFlds) ifCondi = "s_value";
                        getDataItemsScript += @"s_value =$('#~ItemID').val()||'';
                         if(!s_value) s_value =getValidValue($('#~ItemID').text());
                         
                         if(!s_value && $('#~ItemID').attr('type')=='checkbox') s_value =$('#~ItemID').prop('checked')||'';
 
                        if (~ifCondi) { formFlds.~ItemID = s_value;}" + "\r\n";
                    }
                    else if (ctrl == "jqxCheckBox")
                    {
                        //showFormScript += "var value='~Value';if(value.toLowerCase()=='true') value=true;";
                        //showFormScript += @"$('#~ItemID').~Ctrl({checked:value});" + "\r\n";

                        setDataItemsFuncScript += "var value=formData.~ItemID;if(value && String(value).toLowerCase()=='true') value=true;";
                        setDataItemsFuncScript += @"$('#~ItemID').~Ctrl({checked:value});" + "\r\n";
                        getDataItemsScript += @"s_value =$('#~ItemID').~Ctrl('checked');
                        formFlds.~ItemID = s_value||false;" + "\r\n";

                    }
                    else if (ctrl == "hidden")
                    {
                        //showFormScript += @"$('#~ItemID').val(`~Value`);" + "\r\n";
                        setDataItemsFuncScript += @"$('#~ItemID').val(formData.~ItemID);" + "\r\n";
                        if (!bPostEmptyFlds) ifCondi = "s_value";
                        getDataItemsScript += @"s_value =$('#~ItemID').val();
                        if (~ifCondi) { formFlds.~ItemID = s_value;}" + "\r\n";
                    }
                    //else if (dataItem.LabelFld != "" || ctrl == "jqxTree")
                    else if (dataItem.LabelFld != "" || dataItem.ButtonUsage!="none" || (dataItem.Source.StartsWith("[") || dataItem.SqlForOptions != "") || ctrl == "jqxTree")//if it has a list options or it is tree
                    { 
                        if (labelFld != "")
                           setDataItemsFuncScript += @"
$('#~ItemID').~Ctrl('val',{value:formData.~ItemID,label:formData.~LabelFld||''}); 
";                        

                        if (!bPostEmptyFlds)
                            ifCondi = "s_value && (s_value.value || s_value.label || Array.isArray(s_value))";
                        string getDefaultValue = "";
                        if (dataItem.InQueryPage && dataItem.AvailValues != "")
                        {
                            getDefaultValue = @$"
if(!value){{
   value='{dataItem.AvailValues}';
    
   s_value={{value:'{dataItem.AvailValues}',label:'{dataItem.AvailLabels}'}};

}}
";
                        }
                        getDataItemsScript += @$"
s_value =$('#~ItemID').~Ctrl('val');      
var value='',label='';
if(Array.isArray(s_value)){{                           
    s_value.forEach((item)=>{{
        if(value!='') value+=',';
        value+=item.value;
        if(label!='') label+=',';
        label+=item.label;
    }})
}}
else if(s_value){{ value=s_value.value.trim(); label=s_value.label.trim();}}
{getDefaultValue}                     
if(~ifCondi){{
    formFlds.~ItemID =value||''; 
    var fldName='~LabelFld'
    if(fldName)
        formFlds[fldName]=label||'';
}}
" + "\r\n";
                        if (dataItem.QueryByLabelLikeIfIdEmpty)
                        {
                            getDataItemsScript+= @"
                            else
                            {
                              formFlds.~ItemID =''; 
                              var fldName='~LabelFld'
                              label=$('#~ItemID').~Ctrl('getText')
                              if(fldName)
                                 formFlds[fldName]=label||'';
                              }
                            ";
                        }
                        if (dataItem.Necessary)
                        {
                            getDataItemsScript += $"if(!s_value || (!s_value.value && !s_value.length)) {{showInputError('#~ItemID','请输入{dataItem.Title}');return {{errMsg:'请输入{dataItem.Title}'}};}}";
                        }
                    }
                    else
                    {
                        //showFormScript += @"$('#~ItemID').~Ctrl('val',`~Value`);" + "\r\n";
                        setDataItemsFuncScript += @"$('#~ItemID').~Ctrl('val',formData.~ItemID||'');" + "\r\n";
                        if (!bPostEmptyFlds) ifCondi = "s_value";
                        getDataItemsScript += @"s_value =getValidValue($('#~ItemID').~Ctrl('val').trim());
                        if (~ifCondi) { formFlds.~ItemID = s_value;}" + "\r\n";
                        if (dataItem.Necessary)
                        {
                            getDataItemsScript += $"if(!s_value || !s_value.trim()) {{showInputError('#~ItemID','请输入{dataItem.Title}'); return {{errMsg:'请输入{dataItem.Title}'}};}}";
                        }

                    }
                    

                    vars["ItemID"] = key;
                      
                    vars["Ctrl"] = ctrl;
                    vars["PlaceHolder"] = dataItem.PlaceHolder;
                    vars["ifCondi"] = ifCondi;

                    if (dataItem.CtrlType == "jqxDropDownTree" && dataItem.TreePathFld != "" )
                    {
                        if (dataItem.TreePathFld != key)//当dataItem.TreePathFld == key，formFlds.~TreePathFld= 会覆盖formFlds.key的值，所以加个判断让它不生效
						{
							showFormScript += @"$('#~ItemID').jqxDropDownTree('treePath','~TreePath');" + "\r\n";

							getDataItemsScript += @"formFlds.~TreePathFld=$('#~ItemID').jqxDropDownTree('treePath');" + "\r\n"; ;

						}

						// if(formFlds.~TreePathFld.split('/').filter(Boolean).length==1) formFlds.~TreePathFld='';
					}
                    showFormScript = GetStringWithVar(showFormScript, vars);
                    m_showFormScript += showFormScript;
                    m_showFormScript += laterScript;
                    setDataItemsFuncScript = GetStringWithVar(setDataItemsFuncScript, vars);
                    m_setDataItemsFuncScript += setDataItemsFuncScript;
                    getDataItemsScript = GetStringWithVar(getDataItemsScript, vars);

                    m_getDataItemsScript += getDataItemsScript;


                }

                if (dataItem == null)
                { 
                    break;
                }
                else
                {
                    preHideGroup = dataItem.HideGroup; if (preHideGroup == null) preHideGroup = "";

                    preHideClass = dataItem.HideClass; if (preHideClass == null) preHideClass = "";
                    preFldArea = dataItem.FldArea; if (preFldArea == null) preFldArea = "";
                } 
            }
            
        }

        public static string GetStringWithVar(string str, Dictionary<string, string> vars)
        {
            Dictionary<string, string> vars1 = vars.OrderByDescending(o => o.Key).ToDictionary(o => o.Key, p => p.Value);
            foreach (KeyValuePair<string, string> kp in vars1)
            {
                str = str.Replace("~" + kp.Key + "", kp.Value);
            }
            return str;
        }

        public virtual async Task GetJavaScripts(CMySbCommand cmd, bool bGetFldAreaCtrls = false)
        {

        }

        protected void InitDataItemsFromRequest()
        { 
            foreach (KeyValuePair<string, DataItem> kp in DataItems)
            {
                var dataItem = kp.Value;
                if (kp.Key == "branch_id")
                {

                }
                string v = CPubVars.RequestV(Request, kp.Key);
                if (v != null & v != "")
                {
                    dataItem.Value = v;
                    string label = "";
                    if (dataItem.LabelFld != "")
                    {
                        string l = CPubVars.RequestV(Request, dataItem.LabelFld);
                        if (l.Contains("%"))
                        {
                            // 如果字符串中包含 "%", 则说明是 encodeURIComponent 编码的，需要解码
                            l = WebUtility.UrlDecode(l);
                        }
                        if (l != null && l != "")
                        {
                            dataItem.Label = label=l;
                            dataItem.Hidden = false;
                            dataItem.ValueFilled = true;
                        }                       
                    }
                    if (label == "" && dataItem.Source != "" && dataItem.Source != "null")
                    {
                        JArray opts = (JArray)JsonConvert.DeserializeObject(dataItem.Source);
                        foreach (dynamic opt in opts)
                        {
                            if (dataItem.Value == (string)opt.v)
                            {
                                dataItem.Label = opt.l;
                                break;
                            }
                        }
                    }
                    if (dataItem.Value.IsValid() && dataItem.Value.Contains("'")) dataItem.Value = dataItem.Value.Replace("'", "");

                    if (dataItem.TreePathFld != "")
                    {
                        string l = CPubVars.RequestV(Request, dataItem.TreePathFld);
                        if (l != null) { dataItem.TreePath = l; }
                    }
                }
            }
            
        }
        public static async Task<string> GetJsonFromSQL(string sql, CMySbCommand cmd)
        {
            cmd.CommandText = sql;
            CMySbDataReader dr = await cmd.ExecuteReaderAsync();
            string json = "";
            while (dr.Read())
            {
                string row = "";
                for (int i = 0; i < dr.FieldCount; i++)
                {
                    string n = dr.GetName(i);
                    string v = CPubVars.GetTextFromDr(dr, n);
                    if (row != "") { row += ","; }
                    row += "\"" + n + "\":\"" + v + "\"";
                }
                row = "{" + row + "}";
                if (json != "") json += ",";
                json += row;
            }
            dr.Close();
            json = "[" + json + "]";
            return json;
        }
  
        /*
        public static async Task<Dictionary<int, Dictionary<string, string>>> GetRecordsFromSQL(string sql, CMySbCommand cmd, int startRow, QueryGrid grid = null)
        {
            Dictionary<string, DataItem> dicDealItems = new Dictionary<string, DataItem>();
            if (grid != null)
            {
                foreach (var col in grid.Columns)
                {
                    if (col.Value.FuncDealMe != null || col.Value.FuncDealCellValue != null)
                    {
                        var key = col.Key;// col.Value.SqlFld;  
                        // if (col.Value.SqlFld == "") key = col.Key;
                        dicDealItems.Add(key, col.Value);
                    }
                }
            }

            cmd.CommandText = sql;
            CMySbDataReader dr = await cmd.ExecuteReaderAsync();
            Dictionary<int, Dictionary<string, string>> dicRecords = new Dictionary<int, Dictionary<string, string>>();
            int r = startRow;
            while (dr.Read())
            {
                Dictionary<string, string> row = new Dictionary<string, string>();
                for (int i = 0; i < dr.FieldCount; i++)
                {
                    string n = dr.GetName(i);

                    string v = CPubVars.GetTextFromDr(dr, n);
                    if (dicDealItems.ContainsKey(n))
                    {
                        v = dicDealItems[n].FuncDealMe(v);
                    }

                    row[n] = v;
                }
                foreach(var kp in dicDealItems)
                {
                    kp.Value.FuncDealCellValue(row);
                }
                dicRecords.Add(r, row);
                r++;
            }
            dr.Close();
            return dicRecords;
        }
        */
        public static async Task<Dictionary<string, Dictionary<int, Dictionary<string, string>>>> GetRecordsFromQQ(SQLQueue QQ, CMySbCommand cmd, int startRow, QueryGrid grid = null)
        {
            /*
            Dictionary<string, DataItem> dicDealItems = new Dictionary<string, DataItem>();

            if (grid != null)
            {
                foreach (var col in grid.Columns)
                {
                    if (col.Value.FuncDealMe != null || col.Value.FuncDealCellValue != null)
                    {
                        var key = col.Key;// col.Value.SqlFld; 

                        // if (col.Value.SqlFld == "") key = col.Key;
                        dicDealItems.Add(key, col.Value);
                    }
                }
            }*/
            Dictionary<string, Dictionary<int, Dictionary<string, string>>> result = new Dictionary<string, Dictionary<int, Dictionary<string, string>>>();

            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            Dictionary<int, Dictionary<string, string>> dicRecords = new Dictionary<int, Dictionary<string, string>>();
            int r = startRow;
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                var records = GetRecordsFromDr(dr, startRow, grid);
                result.Add(tbl, records);
            }
            QQ.Clear();
            return result;
        }

        public static Dictionary<int, Dictionary<string, string>> GetRecordsFromDr(CMySbDataReader dr, int startRow, QueryGrid grid = null)
        {
            Dictionary<string, DataItem> dicDealItems = new Dictionary<string, DataItem>();
            if (grid != null)
            {
                foreach (var col in grid.Columns)
                {
                    if (col.Value.FuncDealMe != null || col.Value.FuncDealCellValue != null)
                    {
                        var key = col.Key;// col.Value.SqlFld; 
                        
                        // if (col.Value.SqlFld == "") key = col.Key;
                        dicDealItems.Add(key, col.Value);
                    }
                }
            }

            Dictionary<int, Dictionary<string, string>> dicRecords = new Dictionary<int, Dictionary<string, string>>();
            int r = startRow;
            while (dr.Read())
            {
                Dictionary<string, string> row = new Dictionary<string, string>();
                for (int i = 0; i < dr.FieldCount; i++)
                {
                    string n = dr.GetName(i);
                    object vv = dr[n];
                    if (n == "sum_pw_balance_46581")
                    {

                    }
                    string v = CPubVars.GetTextFromDr(dr, n);
                    
                 //   if (CPubVars.IsNumeric(v)) {
                        //v = CPubVars.FormatMoney(v, 6, false);
                    if (v == "-0") v = "0";
                //    }
                  
                    row[n] = v;
                }
                foreach (var kp in dicDealItems)
                {
                    if (row.ContainsKey(kp.Key))
                    {
                        if (kp.Value.FuncDealCellValue != null)
                            row[kp.Key] = kp.Value.FuncDealCellValue(row[kp.Key], row);
                        if (kp.Value.FuncDealMe != null)
                            row[kp.Key] = kp.Value.FuncDealMe(row[kp.Key]);
                    }
                    
                }

                dicRecords.Add(r, row);
                r++;
            }


            return dicRecords;
        }
        public static async Task<DataItem> FindDataItem(Dictionary<string,DataItem> dataItems,string key)
        {
            foreach(var kp in dataItems)
            {
                if (kp.Value.FuncGetSubColumns!=null)
                {
                    ColumnsResult colRes=await kp.Value.FuncGetSubColumns(kp.Value);
                    DataItem item = await FindDataItem(colRes.Columns,key);
                    if (item != null) return item;
                }
                else
                {
                    if(kp.Key== key)
                    {
                        return kp.Value;
                    }
                }
            }
            return null;
        }
        public static async Task<string> GetDataItemOptions(CMySbCommand cmd, string operKey, Dictionary<string, DataItem> myDataItems, string dataItemName, string flds, string value,string availValues, string condiDataItem="")
        {
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID );

            DataItem dataItem =await FindDataItem(myDataItems,dataItemName);
            if (dataItem.SqlForOptions == "")
            {
                throw new Exception("SqlForOptions was not specified");
            }
            string sql = dataItem.SqlForOptions;
            if (!condiDataItem.IsValid())
            {
                condiDataItem = "-1";
            }

            string valueFld = "";
            sql = sql.Replace("~CONDI_DATA_ITEM", condiDataItem, StringComparison.OrdinalIgnoreCase);
            if ((flds != null && value != null && value != "") || dataItem.AditionalCondiForOptions != "" ||availValues.IsValid())
            {
            
                string sqlTrim = sql.ToLower();
                string condi = "";
                sqlTrim = sqlTrim.Replace("\nfrom", " from");
                sqlTrim = sqlTrim.Replace("from\r", "from ");
                string before_from = sqlTrim.Split(" from ")[0];
                var arrUnions = before_from.Split("union");
                if (arrUnions.Length > 1)
                {
                    before_from = arrUnions[arrUnions.Length - 1];
                    before_from = before_from.Replace("\n", " ").Replace("\r", " ").Replace("union", "").Trim();
                
                }

                before_from = before_from.Substring(7, before_from.Length - 7);
                string[] sql_flds = before_from.Split(",");
                Dictionary<string, string> dicSqlFlds = new Dictionary<string, string>();
                foreach (string f in sql_flds)
                {
                    string ft = f.Trim().ToLower();
                    string[] arr_as = ft.Split(" as ");
                    if (arr_as.Length == 2) dicSqlFlds.Add(arr_as[1].Trim(), arr_as[0].Trim());
                    else dicSqlFlds.Add(arr_as[0].Trim(), arr_as[0].Trim());
                    if (valueFld == "") valueFld = arr_as[0].Trim();
                }
                if(flds != null && value != null && value != "")
                {
                    string[] arrFlds = flds.Split(",");
                    if (value != null && value != "")
                    {
                        foreach (string fld in arrFlds)
                        {
                            string fldLower = fld.ToLower();
                            if (dicSqlFlds.ContainsKey(fldLower))
                            {
                                string fldN = fld;
                                string fldOrig = dicSqlFlds[fldLower];
                                if (fldOrig.ToLower() != fldLower)
                                {
                                    fldN = fldOrig;
                                }
                                if (condi != "") condi += " or ";
								if (fldN == "item_name")
								{
                                    value = CPubVars.GetFlexLikeStr(value); 
                                }
                                condi += " " + fldN + " ilike '%" + value + "%'";
                            }
                        }
                        if (condi != "") condi = "(" + condi + ")";
                    }
                }
                  
                if (dataItem.AditionalCondiForOptions != "")
                {
                    if (condi != "") condi += " and ";
                    condi += "(" + dataItem.AditionalCondiForOptions + ")";
                }

                if (availValues.IsValid())
                {
                    if (condi != "") condi += " and ";
                    if (dataItemName == "other_region")
                    {
                        string subValues = "";
                        //List<string> arr = availValues.Split(',').ToList();
                        /*foreach(string region in arr)
                        {

                        }
                        if (arr.Count > 1)
                        {
                            arr.RemoveAt(0);
                            subValues=string.Join(',', arr);
                        }*/
                        string availCondi = $" ({valueFld} in ({availValues}) or mother_id in ({availValues})) ";

                        /*if (subValues != "")
                        {
                            availCondi += $" or mother_id in ({ subValues})";
                            availCondi = "(" + availCondi + ")";
                        }*/
                        condi += availCondi;
                    }
                    else
                       condi += $" {valueFld} in ({availValues})";
                }

                if (sql.IndexOf("~QUERY_CONDITION",StringComparison.OrdinalIgnoreCase) > 0)
                {
                    if (condi == "") condi = " true ";
                    sql = sql.Replace("~QUERY_CONDITION", condi, StringComparison.OrdinalIgnoreCase);
                    
                }
                else
                {
                    string[] arr_s = sqlTrim.Split(" where ");
                    string sql_w = arr_s[arr_s.Length - 1];
                    int l = sql_w.Split("(").Length;
                    int y = sql_w.Split(")").Length;
                    bool bHaveOuterWhere = false;
                    if (arr_s.Length > 1 && l == y) bHaveOuterWhere = true;
                    int n_insert = -1;
                    l = sqlTrim.LastIndexOf(" order by ");
                    if (l > 0) n_insert = l;       
                    else
                    {
                        l = sqlTrim.LastIndexOf(" limit ");
                        if (l > 0) n_insert = l;
                    }
                    if (condi != "")
                    {
                        if (bHaveOuterWhere) condi = " and " + condi; else condi = " where " + condi;
                        if (n_insert > 0)
                        {
                            string left = sql.Substring(0, n_insert);
                            string right = sql.Substring(n_insert, sql.Length - n_insert);
                            sql = left + condi + right;
                        }
                        else sql += condi;
                    }
                }              
            }
            else if (sql.IndexOf("~QUERY_CONDITION", StringComparison.OrdinalIgnoreCase) > 0)
            {
               sql = sql.Replace("~QUERY_CONDITION", " true ", StringComparison.OrdinalIgnoreCase);
			}

            sql = GetRealSQL(sql, companyID);
            sql = sql.Replace("~COMPANY_ID", companyID, StringComparison.OrdinalIgnoreCase);
            if (sql.Contains("~NOW", StringComparison.OrdinalIgnoreCase))
            {
                sql = sql.Replace("~NOW", CPubVars.GetDateText(DateTime.Now), StringComparison.OrdinalIgnoreCase);
            }
            if (sql.Contains("~OPER_ID", StringComparison.OrdinalIgnoreCase))
            {
                sql = sql.Replace("~OPER_ID", operID, StringComparison.OrdinalIgnoreCase);
            }
                if (dataItem.MaxRecords != "") sql += " limit " + dataItem.MaxRecords;
            string jsonData = "";
            try
            {
                jsonData=await GetJsonFromSQL(sql, cmd);
            }
            catch(Exception e)
            {

            }
            return jsonData;
        }

        public static async Task<JsonResult> GetDataItemOptionsJson(CMySbCommand cmd, string operKey, Dictionary<string, DataItem> myDataItems, string dataItemName, string flds, string value, string availValues, string condiDataItem = "")
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);

            DataItem dataItem = await FindDataItem(myDataItems, dataItemName);
            if (dataItem.SqlForOptions == "")
            {
                throw new Exception("SqlForOptions was not specified");
            }
            string sql = dataItem.SqlForOptions;
            if (!condiDataItem.IsValid())
            {
                condiDataItem = "-1";
            }

            string valueFld = "";
            sql = sql.Replace("~CONDI_DATA_ITEM", condiDataItem, StringComparison.OrdinalIgnoreCase);
            if ((flds != null && value != null && value != "") || dataItem.AditionalCondiForOptions != "" || availValues.IsValid())
            {

                string sqlTrim = sql.ToLower();
                string condi = "";
                sqlTrim = sqlTrim.Replace("\nfrom", " from");
                sqlTrim = sqlTrim.Replace("from\r", "from ");
                string before_from = sqlTrim.Split(" from ")[0];
                if(before_from.Contains(" union "))
                {
                    var arr=before_from.Split(" union ");
                    before_from = arr[arr.Length-1];
                }
                before_from = before_from.Substring(7, before_from.Length - 7);
                string[] sql_flds = before_from.Split(",");
                Dictionary<string, string> dicSqlFlds = new Dictionary<string, string>();
                foreach (string f in sql_flds)
                {
                    string ft = f.Trim().ToLower();
                    string[] arr_as = ft.Split(" as ");
                    if (arr_as.Length == 2) dicSqlFlds.Add(arr_as[1].Trim(), arr_as[0].Trim());
                    else dicSqlFlds.Add(arr_as[0].Trim(), arr_as[0].Trim());
                    if (valueFld == "") valueFld = arr_as[0].Trim();
                }
                if (flds != null && value != null && value != "")
                {
                    string[] arrFlds = flds.Split(",");
                    if (value != null && value != "")
                    {
                        value = value.Replace("'", "");
                        foreach (string fld in arrFlds)
                        {
                            string fldLower = fld.ToLower();
                            if (dicSqlFlds.ContainsKey(fldLower))
                            {
                                string fldN = fld;
                                string fldOrig = dicSqlFlds[fldLower];
                                if (fldOrig.ToLower() != fldLower)
                                {
                                    fldN = fldOrig;
                                }
                                if (condi != "") condi += " or ";
                                condi += " " + fldN + " ilike '%" + value + "%'";
                            }
                        }
                        if (condi != "") condi = "(" + condi + ")";
                    }
                }

                if (dataItem.AditionalCondiForOptions != "")
                {
                    if (condi != "") condi += " and ";
                    condi += "(" + dataItem.AditionalCondiForOptions + ")";
                }

                if (availValues.IsValid())
                {
                    if (condi != "") condi += " and ";
                    if (dataItemName == "other_region")
                    {
                        string subValues = "";
                        List<string> arr = availValues.Split(',').ToList();
                        if (arr.Count > 1)
                        {
                            arr.RemoveAt(0);
                            subValues = string.Join(',', arr);
                        }
                        string availCondi = $" {valueFld} in ({availValues}) ";
                        if (subValues != "")
                        {
                            availCondi += $" or mother_id in ({ subValues})";
                            availCondi = "(" + availCondi + ")";
                        }
                        condi += availCondi;
                    }
                    else
                        condi += $" {valueFld} in ({availValues})";
                }

                if (sql.IndexOf("~QUERY_CONDITION", StringComparison.OrdinalIgnoreCase) > 0)
                {
                    if (condi == "") condi = " true ";
                    sql = sql.Replace("~QUERY_CONDITION", condi, StringComparison.OrdinalIgnoreCase);

                }
                else
                {
                    string[] arr_s = sqlTrim.Split(" where ");
                    string sql_w = arr_s[arr_s.Length - 1];
                    int l = sql_w.Split("(").Length;
                    int y = sql_w.Split(")").Length;
                    bool bHaveOuterWhere = false;
                    if (arr_s.Length > 1 && l == y) bHaveOuterWhere = true;
                    int n_insert = -1;
                    l = sqlTrim.LastIndexOf(" order by ");
                    if (l > 0) n_insert = l;
                    else
                    {
                        l = sqlTrim.LastIndexOf(" limit ");
                        if (l > 0) n_insert = l;
                    }
                    if (condi != "")
                    {
                        if (bHaveOuterWhere) condi = " and " + condi; else condi = " where " + condi;
                        if (n_insert > 0)
                        {
                            string left = sql.Substring(0, n_insert);
                            string right = sql.Substring(n_insert, sql.Length - n_insert);
                            sql = left + condi + right;
                        }
                        else sql += condi;
                    }
                }
            }
            else
            {
                if (sql.IndexOf("~QUERY_CONDITION", StringComparison.OrdinalIgnoreCase) > 0)
                {     
                    sql = sql.Replace("~QUERY_CONDITION", " true ", StringComparison.OrdinalIgnoreCase);

                }
            }

            sql = GetRealSQL(sql, companyID);
            sql = sql.Replace("~COMPANY_ID", companyID, StringComparison.OrdinalIgnoreCase);
            if (sql.Contains("~NOW", StringComparison.OrdinalIgnoreCase))
            {
                sql = sql.Replace("~NOW", CPubVars.GetDateText(DateTime.Now), StringComparison.OrdinalIgnoreCase);
            }

            if (dataItem.MaxRecords != "") sql += " limit " + dataItem.MaxRecords;

            
            var records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return new JsonResult(new { result = "OK", data = records });
       
        }

        
    }
    public class DataItemGroup: Dictionary<string, DataItem>
    {
        /*
        public static void FillDataItemsByRequest_old(Dictionary<string, DataItem> dataItems, HttpRequest request,ref string ignoreDataItems )
        { 

            foreach (KeyValuePair<string, DataItem> kp in dataItems)
            {
                DataItem dataItem =  kp.Value;
                if (dataItem.SubGroup != null && dataItem.SubGroup.Count > 0)
                {
                    if (dataItem.SubGroup != dataItems)//防止死循环
                    {
                        FillDataItemsByRequest(dataItem.SubGroup, request,ref ignoreDataItems);
                    }
                    continue;
                }

                var fld = kp.Key;

                object rv = request.Query[fld];
                if (rv == null) rv = "";
                 
                dataItem.Value = rv.ToString().Trim();
                if (dataItem.Value.EndsWith(" 23:59"))
                {
                    dataItem.Value += ":59.999";
                }
               
                if (dataItem.LabelFld != "")
                {
                    rv = request.Query[dataItem.LabelFld];
                    if (rv == null) rv = "";
                    dataItem.Label = rv.ToString().Trim();
                }
                if (dataItem.IgnoreDataItemsIfNotEmpty != "" && dataItem.Value != "")
                {
                    ignoreDataItems += "," + dataItem.IgnoreDataItemsIfNotEmpty;
                }
            }

            if (ignoreDataItems != "")
            {
                ignoreDataItems += ",";

                foreach (KeyValuePair<string, DataItem> kp in dataItems)
                {
                    DataItem dataItem = kp.Value;
                    var fld = kp.Key;
                    if (ignoreDataItems.Contains("," + fld + ",") || ignoreDataItems.Contains(",all,"))
                    {
                        if (!(dataItem.IgnoreDataItemsIfNotEmpty != "" && dataItem.Value != ""))
                        {
                            dataItem.Value = "";
                        }
                    }
                }
            }
        }
        */
        public static void FillDataItemsByRequest(Dictionary<string, DataItem> dataItems, HttpRequest request,dynamic postData, ref string ignoreDataItems)
        {

            foreach (KeyValuePair<string, DataItem> kp in dataItems)
            {
                DataItem dataItem = kp.Value;
                if (dataItem.SubGroup != null && dataItem.SubGroup.Count > 0)
                {
                    if (dataItem.SubGroup != dataItems)//防止死循环
                    {
                        FillDataItemsByRequest(dataItem.SubGroup, request, postData, ref ignoreDataItems);
                    }
                    continue;
                }

                var fld = kp.Key;

                object rv = PageQueryModel.GetParamFromRequest(request,postData, fld);
                if (rv == null) rv = "";

                dataItem.Value = rv.ToString().Trim();
                if (dataItem.Value.EndsWith(" 23:59"))
                {
                    dataItem.Value += ":59.999";
                }

                if (dataItem.LabelFld != "")
                {
                    rv = PageQueryModel.GetParamFromRequest(request,postData,dataItem.LabelFld);
                    if (rv == null) rv = "";
                    dataItem.Label = rv.ToString().Trim();
                }
				else
				{
                    rv = PageQueryModel.GetParamFromRequest(request, postData, fld+"_LABEL");
                    if (rv == null) rv = "";
                    dataItem.Label = rv.ToString().Trim(); 

                }
                if (dataItem.IgnoreDataItemsIfNotEmpty != "" && dataItem.Value != "")
                {
                    ignoreDataItems += "," + dataItem.IgnoreDataItemsIfNotEmpty;
                }
            }

            if (ignoreDataItems != "")
            {
                ignoreDataItems += ",";

                foreach (KeyValuePair<string, DataItem> kp in dataItems)
                {
                    DataItem dataItem = kp.Value;
                    var fld = kp.Key;
                    if (ignoreDataItems.Contains("," + fld + ",") || ignoreDataItems.Contains(",all,"))
                    {
                        if (!(dataItem.IgnoreDataItemsIfNotEmpty != "" && dataItem.Value != ""))
                        {
                            dataItem.Value = "";
                        }
                    }
                }
            }
        }

        public static void ClearIgnoreDataItems(Dictionary<string, DataItem> dataItems,  string ignoreDataItems)
        { 
            if (ignoreDataItems != "")
            {
                ignoreDataItems += ",";

                foreach (KeyValuePair<string, DataItem> kp in dataItems)
                {
                    DataItem dataItem = kp.Value;
                    if (dataItem.SubGroup != null)
                    {
                        ClearIgnoreDataItems(dataItem.SubGroup, ignoreDataItems);
                    }
                    else
                    {
                        var fld = kp.Key;
                        if (ignoreDataItems.Contains("," + fld + ",") || ignoreDataItems.Contains(",all,"))
                        {
                            if (!(dataItem.IgnoreDataItemsIfNotEmpty != "" && dataItem.Value != ""))
                            {
                                dataItem.Value = "";
                            }
                        }
                    }
                  
                }
            }
        }
    }
    public class DataItemChange
    {
        public bool ForQuery = true;
       
        public string SqlFld = "";
        public bool Checkboxes = false;
        public bool AlwaysShow = false;
        public string ButtonUsage = "list";
        public string Width = "";
        public string SqlAreaToPlace = "";

	}
    public class DataItem
    {
        public class ColumnsResult
        {
            public Dictionary<string, DataItem> Columns = new Dictionary<string, DataItem>();
            public string FldsSQL = "";
        }
        public DataItemGroup SubGroup = null;
      
        //for grid column
        public string SubMumTitle = "";//二级类列名  如果此列在子列Columns中，且MumTitle不为空，则在子列和父列之间会多一级
        public bool HideTopGroupName = false;
        public bool GetFromDb = true;//设为false,查询时就不会取这一列
        public string SubRowsColumn = "";//该列对应要展示的subRows对应列的名称
        public Func<DataItem, Task<ColumnsResult>> FuncGetSubColumns;//定义合并列集合
		public string Restrict = "";
        public async Task TryGetSubColumns() {  
             if (SubColumns == null && FuncGetSubColumns != null)
             {
                 ColumnsResult result = await FuncGetSubColumns(this);
                 SubColumns = result.Columns;
                foreach(var kp in SubColumns)
                {
                    kp.Value.OrigTitle = kp.Value.Title;
                    if (kp.Value.InnerTitle != "") kp.Value.OrigTitle = kp.Value.InnerTitle; 
                }  
                SubColumnsFldsSQL = result.FldsSQL;
             } 
        }

        public Dictionary<string, string> OtherConditionForOptions = null;

        public Dictionary<string, DataItem> SubColumns = null;
        public string SubColumnsFldsSQL = "";
        public bool BuyPriceRelated = false;
		public bool ProfitRelated = false;
		public string JSCellRender = "", JSCellBeginEdit="", JSHeaderRender = "";
        public string JSCellEndEdit = ""; // InfSein, 20221122.
        public string JsAggregatesComputer = "", JsAggregatesRender = "";
        public string JSOther = "";
        public string JSCreateEditor = "", JSInitEditor="", JSGetEditorValue = "";
        public string JSBeforeCreate = "";
        public string JSAfterCreate = "";
        public bool ShowSum = false;
        public bool ShowRowPercent = false;
        public bool MightDuplicate = false;
        public bool IsIDColumn = false;
        public bool SelectFieldEvenHidden = false;
        public bool HideDuplicateCells = false;
        public bool ShowAvg = false;
        public Func<Dictionary<string, string>,string> FuncGetSumValue=null;
        public string RelyColumns = "";
        public string CellsAlign = "";
        public bool AutoRemember = false;
        public bool allowNullDate = false;
        public bool KeepNoValueLabel = false;
        public string EmptyConditionValue = "";
        
        //
        //public DataFld id = new DataFld();
        //public DataFld text = new DataFld() { bWrite = false };
        public Func<string, string> DealQueryItem;
        public Func<string, string> FuncDealMe;
        public Func<string,Dictionary<string, string>, string> FuncDealCellValue;

        public int OrderIndex = 100;
        public string JSDealItemOnSelect = "";
 
        public string CtrlType { get; set; } = "jqxInput";//can also be jqxDropDownTree  
        public bool ForQuery = true;
        public bool InQueryPage = false;
        //public bool SpecInSql = false;

		public string GetJsLabelFld(string key)
        {
            if (LabelFld != "")
            {
                if (LabelFld.Contains(".")) return LabelFld.Split(".")[1];
                else return LabelFld;
            }
            else
            {
                //if ((Source != "" && Source != "null") ||
               //     (SqlForOptions != "" && GetOptionsOnLoad)
               // ) return key + "_LABEL";
                if ((Source != "" && Source != "null") ||(SqlForOptions != ""))
                    return key + "_LABEL";
                else return "";
            }
        }
        public string LabelFld { get; set; } = "";//, textFld = "";
        public bool Necessary { get; set; } = false;
        public bool Disabled { get; set; } = false;
        public bool Readonly { get; set; } = false;
        public bool HideIfEmpty { get; set; } = false;
        public bool ShowSeconds { get; set; } = false;
        public bool ShowTime { get; set; } = true;
        public bool Hidden { get; set; } = false;  //for grid column
        /*public COLUMN_USAGE ColumnUsage { get; set; }
        public enum COLUMN_USAGE
        { 
            Show=1,
            ForCompute=2
		}*/
        public bool Sortable { get; set; } = false;
        public string ZeroAsEmpty { get; set; } = "";

        public Func<string,Dictionary<string,string>, string> FuncGetValueFromRowData;
        public bool AlwaysShow { get; set; } = false;
        public bool HideOnLoad { get; set; } = false;

        public bool Pinned { get; set; } = false;
        public string ClassName { get; set; }
        // public bool defaultHide { get; set; }

        public Dictionary<string, string> SqlFldOnHiddenRelateColumn = null;
        public int CtrlID { get; set; }
        public bool UseJQWidgets = true;
        public string HideGroup { get; set; } = "";
        public string HideClass { get; set; } = "";
        // public string valueFld="";
        public string Value { get; set; } = "";
        public string Label { get; set; } = "";
        public bool ValueFilled = false;
        public bool TextAsValue { get; set;}
        public bool SaveToDB = true;
        public string TreePath { get; set; } = "";
        public bool LabelInDB { get; set; } = true;
        public bool LabelInFormTable { get; set; } = false;
        public string FldArea { get; set; } = "";
        //for input and dropDownTree
        public string InnerTitle { get; set; } = "";
        public string OrigTitle { get; set; } = "";
        public string Title { get; set; } = "";
        public string Width { get; set; } = "";
        public string TitleWidth { get; set; } = "";
        public string TreePathFld { get; set; } = "";
		public string TreePathNameFld { get; set; } = "";
		public bool TreePathFromDb { get; set; } = true;
              
        public string PlaceHolder { get; set; } = "";
        public bool UseDefaultValue = false;
        public string BorderShape { get; set; } = "bottomLine";
        public string DisplayMember { get; set; } = "name";
        public string ValueMember { get; set; } = "id";
        public string Url { get; set; } = "";
       
        public string Source { get; set; } = "null";
        public string DropDownWidth { get; set; } = "200";
        public string DropDownHeight { get; set; } = "200";
        public bool Checkboxes { get; set; } = false;  
        public bool MumSelectable { get; set; } = false;//tree的父节点是否可选                           //for input
        
        public bool ShowDropDownColumnsHeader { get; set; }
        public string IgnoreDataItemsIfNotEmpty { get; set; } = "";
        public string datafields { get; set; } = "[{datafield: 'l', text: '', width: 120}]";
        public string SearchFields { get; set; } = "['l','z']"; 
        public string ButtonUsage { get; set; } = "none";

        public string SqlForOptions { get; set; } = "";
        public string AvailValues = "",AvailLabels="";
        public bool TopNodeValueRestricted;

        public string CONDI_DATA_ITEM = "";
        public string OtherQueryStrForOptions { get; set; } = "";
        public bool FirstOptionAsDefault = false;
        public string InitValue = "";
      //  public JArray DropDownOptions = null;
        public string MaxRecords { get; set; } = "200"; public bool GetOptionsOnLoad { get; set; } = false;
        public string AditionalCondiForOptions { get; set; } = "";
        //for query
        public string CompareOperator { get; set; } = "";
        public bool UseFlexLikeQuery = false;
		public string LikeString = "ilike";

		public string NullEqualValue = "";
        public bool ReverseCondition = false;
        public bool QueryByLabelLikeIfIdEmpty = false;
        public string LikeWrapper { get; set; } = ""; 
        public string SqlFld { get; set; } = "";

        public string SortFld { get; set; } = "";
        public bool IsChinese = false;
        public bool QueryOnChange { get; set; } = false;
        public bool Linkable { get; set; } = false;
        public bool AfterGroup = false;
        public string SqlCondition = "";
        public string SqlAreaToPlace = "";
		//public string DefaultValue { get; set; } = "";
		//for link button 
		public List<LinkButton> linkButtons = null;

        public bool threestatecheckbox { get; set; } = false;
        public string columntype { get; set; } = "";

        public bool editable = false;
        public bool EditableInFormGrid = true;
        // 用于展示鼠标 hover 时的提示信息
        public string Tooltip { get; set; } = "";
}
    public class LinkButton
    {
        public string id = "", text = "";

        public static implicit operator List<object>(LinkButton v)
        {
            throw new NotImplementedException();
        }
    }

    public static class MyStringHelper
    {
        public static string ToFuncSql(this string s)
        {
            return s.Replace("'", "''");
        }
        public static string ToShortSql(this string s)
        {
            return s.Replace("    ", " ").Replace("   ", " ").Replace("  ", " ").Replace("\r\n", "");
        }
    }

    public class CwLog
    {
        public static async Task Save(string companyID, string operID, dynamic sheet_id, string sheet_type, string log, CMySbCommand cmd)
        {
            if (sheet_id == null) sheet_id = "null";
            log = log.Replace("'", " ~ ").Replace('"', '~').Replace("\n","").Replace("\r","");
            string sql = $@"insert into cw_log(company_id,happen_time,oper_id,sheet_id,sheet_type,log) values 
                            ({companyID},'{DateTime.Now}',{operID},{sheet_id},'{sheet_type}','{log}')";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
        }
    }

    public class CashBankLog
    {
        public static async Task Save(string companyID, string operID, string sub_id, decimal amt, string sheet_type, dynamic sheet_id, string log, CMySbCommand cmd)
        {
            if (sheet_id == null) sheet_id = "null";
            log = log.Replace("'", " ~ ").Replace('"', '~').Replace("\n", "").Replace("\r", "");
            string sql2 = $@"insert into cashbank_balance_log(company_id, oper_id, sub_id, make_time, amount, sheet_type, sheet_id, log) values 
                            ({companyID}, {operID}, {sub_id}, '{DateTime.Now}', {amt}, '{sheet_type}', {sheet_id}, '{log}');";
            cmd.CommandText = sql2;
            await cmd.ExecuteNonQueryAsync();
        }
    }
}
