﻿@page
@model ArtisanManage.Pages.BaseInfo.InventoryAlertModel
@{
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <link href="~/css/flex.css" rel="stylesheet" />
    <link href="~/jqwidgets/jqwidgets/jqx-all.js" />
    <script src="~/js/jquery.min.js"></script>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <style>
        #tableContainer{
            height:100%;
            width:100%;
        }

        /deep/ .el-table thead.is-group th {
            background-color:#eee !important;
        }

        .btns{
            margin-top:5px;
            display:flex; 
            align-items:center;
            justify-content:center;
            gap:10px;
            position:relative;
        }

            .btns label {
                position: absolute;
                right: 10px; /* 右侧间距 */
                top: 10%; /* 垂直居中 */
                 
            }

        #btnSubmit, #btnClose {
            margin-left:20px;
            padding:5px 20px;
            text-align:center;
        }
    </style>
</head>
<body>
    <div class="wrapper" id="tableContainer">
        <el-table :data="tableData"
                  height="500"
                  border
                  style="width: 100%;"
                  :header-row-style="{'border-left':'0.5px solid #d6d6d6'}"
                  :row-style="{'border-left':'0.5px solid #d6d6d6','border-bottom':'0.5px solid #d6d6d6'}"
                  :header-cell-style="{'background-color':'#eee','color':'#000','border-right':'0.5px solid #d6d6d6','border-bottom':'0.5px solid #d6d6d6','font-weight':'normal'}"
                  :cell-style="{'background-color':'#fff','color':'#222','border-right':'0.5px solid #d6d6d6','border-bottom':'0.5px solid #d6d6d6'}">
            <el-table-column prop="item_name"
                             label="商品名称"
                             >
            </el-table-column>
            <el-table-column prop="s_barcode"
                             label="条码(小)"
                             width="180">
            </el-table-column>
            <el-table-column prop="b_barcode"
                             label="条码(大)"
                             width="180">
            </el-table-column>
             <el-table-column prop="unit_conv"
                             label="单位换算"
                             width="180">
            </el-table-column>
            <el-table-column prop="current_qty"
                             label="系统库存"
                             width="80">
            </el-table-column>
        </el-table>
        
            <div class="btns"  >
                <button id="btnSubmit" type="button" @@click="handleBtnSubmit">加入盘点</button>
                <button id="btnClose" type="button" @@click="handleBtnClose">不加入盘点</button>
                <label style="font-size:16px;">共{{tableData.length}}条</label>
            </div>
           
       
     
    </div>
    <script type="text/javascript">
        // 注册消息事件监听，接受子元素给的数据
        var app = new Vue({
            el: '#tableContainer',
            data() {
                return {
                  tableData:[],
                }
            },
            methods: {
                indexMethod(index) {
                    return index+1;
                },
                handleBtnSubmit(){
                    let msg = {
                        msgHead: 'InventoryAlert', action: 'submit', rows: this.tableData
                    };
                    window.parent.postMessage(msg, "*");
                },
                handleBtnClose(){
                    //window.parent.close()
                    let msg = {
                        msgHead: 'InventoryAlert', action: 'close'
                    };
                    window.parent.postMessage(msg, "*");
                },
            },
            mounted(){
                window.addEventListener('message', (e) => {
                    let data = JSON.parse(e.data)
                    this.tableData = data
                   
                }, false);
            }
        });
    </script>
</body>
</html>