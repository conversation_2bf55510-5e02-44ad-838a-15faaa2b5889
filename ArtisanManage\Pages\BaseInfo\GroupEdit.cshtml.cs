using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;

namespace ArtisanManage.Pages.BaseInfo 
{
    public class GroupEditModel : PageFormModel
    {
        public GroupEditModel(CMySbCommand cmd,string company_id="",string oper_id="") : base(Services.MenuId.infoClient)
        {
            this.cmd=cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"group_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"group_name",new DataItem(){Title="渠道名称",Necessary=true,FldArea="divHead"}},
                {"remark",new DataItem(){Title="备注",FldArea="divHead"}},
                {"order_index",new DataItem(){Title="显示顺序",FldArea="divHead"}},
            };
 
            m_idFld = "group_id"; m_nameFld = "group_name";
            m_tableName = "info_supcust_group";
            m_selectFromSQL = "from info_supcust_group where group_id='~ID'";
        }

        public async Task OnGet()
        {  
            await InitGet(cmd);   
        } 
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class GroupEditController : BaseController
    { 
        public GroupEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey,string dataItemName, string flds, string value, string availValues)
        {
            GroupEditModel model = new GroupEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            GroupEditModel model = new GroupEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            GroupEditModel model = new GroupEditModel(cmd);
            return await model.SaveTable(cmd, request);

        }
    }
}