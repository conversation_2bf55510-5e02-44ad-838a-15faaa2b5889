﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Microsoft.VisualBasic;
using Newtonsoft.Json;
using NPOI.SS.Formula.PTG;
using Org.BouncyCastle.Asn1.X509;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace ArtisanManage.Pages.BaseInfo
{
    public class CommissionSummaryModel : PageQueryModel
    {  
        public CommissionSummaryModel(CMySbCommand cmd) : base(Services.MenuId.commission)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ShowSeconds = true, SqlFld="sm.happen_time",CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ShowSeconds = true, SqlFld="sm.happen_time",CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59:599",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00:00','23:59:599');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"workerID",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="oper_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.seller_id",
                    SqlForOptions ="select oper_id as v,oper_name as l from info_operator where is_seller"}},
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"id",    new DataItem(){Title="业务员",   Width="" ,Hidden=true}},
                       {"name",    new DataItem(){Title="员工",   Width="" }},
                       {"amount_x_str",   new DataItem(){Title="销售金额",  CellsAlign="right",  Width="10%",ShowSum=true}},
                       {"amount_x_arrears_str",   new DataItem(){Title="销售欠款",  CellsAlign="right",  Width="10%",ShowSum=true}},
                       {"amount_x_from_arrears_str",   new DataItem(){Title="收欠款",  CellsAlign="right",  Width="10%",ShowSum=true}},
                       
                       {"quantity_x_str",   new DataItem(){Title="销售数量",  CellsAlign="right",  Width="10%",ShowSum=true}},
                       {"amount_t_str",   new DataItem(){Title="退货金额",  CellsAlign="right",  Width="10%",ShowSum=true}},
                       {"amount_t_arrears_str",   new DataItem(){Title="退货欠款",  CellsAlign="right",  Width="10%",ShowSum=true}},
                       {"amount_t_from_arrears_str",   new DataItem(){Title="收欠款",  CellsAlign="right",  Width="10%",ShowSum=true}},
                       {"quantity_t_str",   new DataItem(){Title="退货数量",  CellsAlign="right",  Width="10%",ShowSum=true}},
                       {"commission_x_str",   new DataItem(){Title="销售提成",  CellsAlign="right",  Width="10%",ShowSum=true}},
                       {"commission_t_str",   new DataItem(){Title="退货扣减",  CellsAlign="right",  Width="10%",ShowSum=true}},
                       { "net_amount_str",new DataItem(){ Title="销售净额",  CellsAlign="right",  Width="10%",ShowSum=true} },
                       {"commission_str",   new DataItem(){Title="提成金额",  CellsAlign="right",  Width="10%",ShowSum=true,
           JSCellRender=@" 
         function (index, datafield, value, defaultvalue, column, rowdata) {
              var workerID= rowdata.id; var workerName= rowdata.name; 
              var startDay=$('#startDay').jqxDateTimeInput('val');
              var endDay=$('#endDay').jqxDateTimeInput('val');

              var url=`/Report/CommissionDetail?id=${workerID}&name=${workerName}&startDay=${startDay}&endDay=${endDay}`;
              var  tmpDivs=`<div style='color:#5588f8;display:flex;align-items:center;justify-content:flex-end;padding:3px;height:100%;cursor:pointer;'  onclick=""parent.newTabPage('提成明细','${ url}')"">
                                         ${value}
                                   </div>`; 
               return tmpDivs;
        }"
                       }},
                     },
                  }
                } 
            };             
        }

        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
    }
    public class CommissionStrategy : BaseModel
    {
        public string flow_id { get; set; }
        public string company_id { get; set; }
        public string plan_id { get; set; }

        public string plan_name { get; set; }

        public string content { get; set; }
        public string type { get; set; }
        public string id { get; set; }
        public string strategy_type { get; set; }
        public string strategy_name { get; set; }
        public string oper_id { get; set; }
        public string oper_name { get; set; }
        public string supcust_id { get; set; }
        public string group_id { get; set; }
        public string rank_id { get; set; }
        public string region_id { get; set; }
        public string item_id { get; set; }
        public string order_source { get; set; }

    } 
    public class CommissionInfo : BaseModel
    {
        /// <summary>
        /// 员工
        /// </summary>
        [YjColumn]
        public int oper_id { get; set; }

        public string oper_name { get; set; }

        [YjColumn]
        public string oper_type { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        [YjColumn]
        public int item_id { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        [YjColumn]
        public string item_name { get; set; }

        /// <summary>
        /// 商品类别路径
        /// </summary>
        [YjColumn]
        public string other_class { get; set; }

        /// <summary>
        /// 大单位包装率
        /// </summary>
        [YjColumn(DbNull ="1")]
        public string b_unit_factor { get; set; }

        /// <summary>
        /// 大单位包装率
        /// </summary>
        public decimal factor_b => decimal.Parse(b_unit_factor.IsValid()?b_unit_factor:"1");

        /// <summary>
        /// 中单位包装率
        /// </summary>
        [YjColumn]
        public string m_unit_factor { get; set; }

        [YjColumn]
        public string b_unit_no { get; set; }

        [YjColumn]
        public string m_unit_no { get; set; }
        [YjColumn]
        public string s_unit_no { get; set; }

        /// <summary>
        /// 是否欠款
        /// </summary>
        [YjColumn]
        public bool is_arrears { get; set; }

        /// <summary>
        /// 销赠品量
        /// </summary>
        [YjColumn]
        public double x_gift_quantity { get; set; }
        /// <summary>
        /// 退赠品量
        /// </summary>
        [YjColumn]
        public double t_gift_quantity { get; set; }

        /// <summary>
        /// 销售额
        /// </summary>
        [YjColumn]
        public double x_amount { get; set; }

        public string x_amount_str => x_amount.ToMoneyStr();

        /// <summary>
        /// 退货额
        /// </summary>
        [YjColumn]
        public double t_amount { get; set; }

        public string t_amount_str => t_amount.ToMoneyStr();

        /// <summary>
        /// 总成本(按进价)
        /// </summary>
        [YjColumn]
        public double x_cost_buy_price { get; set; }
        /// <summary>
        /// 总成本(按进价)
        /// </summary>
        [YjColumn]
        public double t_cost_buy_price { get; set; }

        /// <summary>
        /// 总成本(按平均价)
        /// </summary>
        [YjColumn]
        public double x_cost_price_avg { get; set; }
        /// <summary>
        /// 总成本(按平均价)
        /// </summary>
        [YjColumn]
        public double t_cost_price_avg { get; set; }

        [YjColumn]
        public decimal real_price { get; set; }
        public decimal real_price_tmp { get; set; }
        public decimal b_real_price { get; set; }
        public decimal item_avg_price { get; set; }

        /// <summary>
        /// 销量
        /// </summary>
        [YjColumn]
        public double x_quantity { get; set; }
        public string x_quantity_str => x_quantity.ToMoneyStr(precision:4);

    
        public string quantity_unit_x { get; set; }

        /// <summary>
        /// 退货量
        /// </summary>
        [YjColumn]
        public double t_quantity { get; set; }
        public string t_quantity_str => t_quantity.ToMoneyStr(precision: 4);
        public string quantity_unit_t { get; set; }

        public List<CommissionRate> Rates { get; set; }
        public bool FromGetArrears = false;

        public string supcust_id { get; set; }
        public string plan_id { get; set; }

        public string strategy_type { get; set; }

        public string other_region { get; set; }
        public string sup_rank { get; set; }
        public string region_id { get; set; }
        public string sup_group { get; set; }

        public string strategy_id { get; set; }
        public string strategy_name { get; set; }

        public string plan_source { get; set; }
        public string plan_name { get; set; }

        public string other_info
        {
            get
            {
                return is_arrears ? "欠款" : (FromGetArrears ? "来自收欠款" : "");
            }
        }
        internal static async Task<List<CommissionInfo>> GetList_old(CMySbCommand cmd, string company_id, string where,string start_time,string end_time,bool forMoneyGetter)
        {
            string workerIdFld = " oper_id ";
            if (forMoneyGetter)
            {
                workerIdFld = "case when ga.getter_id is not null and oper_type = 'seller' then ga.getter_id else oper_id end";
            }
 
            cmd.CommandText = $@"
SELECT  {workerIdFld} oper_id,oper_type,iip.item_id,iip.item_name,iip.other_class,mu.b_unit_factor,mu.m_unit_factor,mu.b_unit_no,mu.m_unit_no,mu.s_unit_no, (real_price/unit_factor) as real_price,
case when ga.mm_sheet_id is not null then ssm.total_amount-ssm.paid_amount-ssm.disc_amount>=0.01 else ssm.total_amount-ssm.now_pay_amount-ssm.now_disc_amount>=0.01 END as is_arrears,

sum(case WHEN ssd.real_price=0 AND quantity*ssd.inout_flag<0 THEN -1*ssd.inout_flag*ssm.money_inout_flag*quantity*unit_factor else 0 end) AS x_gift_quantity,
sum(case WHEN ssd.real_price=0 AND quantity*ssd.inout_flag>0 THEN ssd.inout_flag*quantity*unit_factor else 0 end) AS t_gift_quantity,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*sub_amount*ssm.money_inout_flag else 0 end) AS x_amount,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*sub_amount*ssm.money_inout_flag*quantity/abs(quantity) else 0 end) AS t_amount,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag*(s_buy_price::numeric)*quantity*unit_factor else 0 end) AS x_cost_buy_price,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*ssm.money_inout_flag*(s_buy_price::numeric)*quantity*unit_factor else 0 end) AS t_cost_buy_price,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag*ssd.cost_price_avg*quantity*unit_factor else 0 end) AS x_cost_price_avg,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*ssm.money_inout_flag* ssd.cost_price_avg*quantity*unit_factor else 0 end) AS t_cost_price_avg,

sum(case when ssd.real_price >0 AND quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag*quantity*unit_factor else 0 end) AS x_quantity,
sum(case when ssd.real_price >0 AND quantity*ssd.inout_flag>0 then ssd.inout_flag*quantity*unit_factor else 0 end) AS t_quantity,
ssd.company_id
FROM sheet_sale_detail ssd
LEFT JOIN 
(    select seller_id oper_id,'seller' oper_type, * from sheet_sale_main WHERE company_id={company_id} AND seller_id is NOT NULL AND red_flag is null AND approve_time is not null
      union
     select regexp_split_to_table(senders_id, E',+')::int as oper_id,'sender' oper_type,* from sheet_sale_main WHERE company_id={company_id} AND senders_id is NOT
		 NULL AND red_flag is null AND approve_time is not null

 ) ssm ON ssm.sheet_id = ssd.sheet_id
LEFT JOIN info_item_prop iip ON ssd.item_id = iip.item_id
LEFT JOIN 
(
    select item_id,(b->>'f1') as b_unit_factor,(m->>'f1') as m_unit_factor,(s->>'f1') as s_unit_factor,(s->>'f3') as s_buy_price,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id= {company_id} order by item_id',$$values ('s'::TEXT),('m'::text),('b'::text)$$) 
    as errr(item_id int, s jsonb,m jsonb,b jsonb)
) mu on mu.item_id = ssd.item_id
left join
(
    select getter_id,mm_sheet_id,gad.happen_time from sheet_get_arrears_detail gad 
    left join sheet_get_arrears_main gam on gad.sheet_id=gam.sheet_id  and gad.company_id=gam.company_id where gam.red_flag is null and gam.happen_time >= '{start_time}' AND gam.happen_time <= '{end_time}' and gad.company_id={company_id} and abs(gad.left_amount)<0.01  
) ga  on ssd.sheet_id=ga.mm_sheet_id   
{where}
GROUP BY ( {workerIdFld},iip.item_id,iip.item_name,iip.other_class,mu.b_unit_factor,m_unit_factor,b_unit_no,m_unit_no,s_unit_no,is_arrears,ssd.company_id,real_price,unit_factor,oper_type,ga.getter_id)
ORDER BY  {workerIdFld},oper_type";

            var commissionInfos = new List<CommissionInfo>();
            var dr = await cmd.ExecuteReaderAsync();
            while (dr.Read())
            {
                commissionInfos.Add((CommissionInfo)new CommissionInfo().ReadFromDataReader(dr.dr_sql));
            }
            dr.Close();
            return commissionInfos;
        }

        internal static async Task<List<CommissionInfo>> GetList(CMySbCommand cmd, string company_id, string where, string start_time, string end_time, bool forMoneyGetter, string saleSheetCondi)
        {
            string workerIdFld = " oper_id ";

            if (forMoneyGetter)
            {
                workerIdFld = "case when ga.getter_id is not null and oper_type = 'seller' then ga.getter_id else oper_id end";

            }


            cmd.CommandText = $@"
SELECT  {workerIdFld} oper_id,oper_type,iip.item_id,iip.item_name,iip.other_class,mu.b_unit_factor,mu.m_unit_factor,mu.b_unit_no,mu.m_unit_no,mu.s_unit_no, (real_price/unit_factor) as real_price,  case when unit_factor>1  then case when unit_factor = mu.b_unit_factor::numeric then ssd.real_price::numeric else real_price::numeric *(mu.b_unit_factor::numeric / mu.m_unit_factor::numeric) end else real_price::numeric*mu.b_unit_factor::numeric end b_real_price,
case when ga.mm_sheet_id is not null then ssm.total_amount-ssm.paid_amount-ssm.disc_amount>=0.01 else ssm.total_amount-ssm.now_pay_amount-ssm.now_disc_amount>=0.01 END as is_arrears,

sum(case WHEN ssd.real_price=0 AND quantity*ssd.inout_flag<0 THEN -1*ssd.inout_flag*ssm.money_inout_flag*quantity*unit_factor else 0 end) AS x_gift_quantity,
sum(case WHEN ssd.real_price=0 AND quantity*ssd.inout_flag>0 THEN ssd.inout_flag*quantity*unit_factor else 0 end) AS t_gift_quantity,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*sub_amount*ssm.money_inout_flag else 0 end) AS x_amount,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*sub_amount*ssm.money_inout_flag*quantity/abs(quantity) else 0 end) AS t_amount,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag*(ssd.cost_price_buy::numeric)*quantity*unit_factor                         else 0 end) AS x_cost_buy_price,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*ssm.money_inout_flag*(ssd.cost_price_buy::numeric)*quantity*unit_factor * quantity/abs(quantity) else 0 end) AS t_cost_buy_price,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag * ssd.cost_price_avg*quantity*unit_factor                         else 0 end) AS x_cost_price_avg,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*ssm.money_inout_flag * ssd.cost_price_avg*quantity*unit_factor * quantity/abs(quantity) else 0 end) AS t_cost_price_avg,

sum(case when ssd.real_price >0 AND quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag*quantity*unit_factor else 0 end) AS x_quantity,
sum(case when ssd.real_price >0 AND quantity*ssd.inout_flag>0 then ssd.inout_flag*quantity*unit_factor else 0 end) AS t_quantity,
ssd.company_id
FROM sheet_sale_detail ssd
LEFT JOIN 
(    select seller_id oper_id,'seller' oper_type, * from sheet_sale_main WHERE company_id={company_id} AND seller_id is NOT NULL AND red_flag is null AND approve_time is not null {saleSheetCondi}
      union
     select regexp_split_to_table(senders_id, E',+')::int as oper_id,'sender' oper_type,* from sheet_sale_main WHERE company_id={company_id} AND senders_id is NOT
		 NULL AND red_flag is null AND approve_time is not null  {saleSheetCondi}

 ) ssm ON ssm.sheet_id = ssd.sheet_id
LEFT JOIN info_item_prop iip ON ssd.item_id = iip.item_id
LEFT JOIN 
(
    select item_id,(b->>'f1') as b_unit_factor,(m->>'f1') as m_unit_factor,(s->>'f1') as s_unit_factor,(s->>'f3') as s_buy_price,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id= {company_id} order by item_id',$$values ('s'::TEXT),('m'::text),('b'::text)$$) 
    as errr(item_id int, s jsonb,m jsonb,b jsonb)
) mu on mu.item_id = ssd.item_id
left join
(
    select getter_id,mm_sheet_id,gad.happen_time from sheet_get_arrears_detail gad 
    left join sheet_get_arrears_main gam on gad.sheet_id=gam.sheet_id  and gad.company_id=gam.company_id where gam.red_flag is null and gam.happen_time >= '{start_time}' AND gam.happen_time <= '{end_time}' and gad.company_id={company_id} and abs(gad.left_amount)<0.01  
) ga  on ssd.sheet_id=ga.mm_sheet_id   
{where}
GROUP BY ( {workerIdFld},iip.item_id,iip.item_name,iip.other_class,mu.b_unit_factor,m_unit_factor,b_unit_no,m_unit_no,s_unit_no,is_arrears,ssd.company_id,real_price,unit_factor,oper_type,ga.getter_id)
ORDER BY  {workerIdFld},oper_type";

            var commissionInfos = new List<CommissionInfo>();
            var dr = await cmd.ExecuteReaderAsync();
            while (dr.Read())
            {
                var info = new CommissionInfo();
                CDbDealer.SetObjectByDr(dr, info, false);
                commissionInfos.Add(info);// new CommissionInfo().ReadFromDataReader(dr.dr_sql));
            }
            dr.Close();
            return commissionInfos;
        }
        internal static async Task<List<CommissionInfo>> GetList_newest(CMySbCommand cmd, string company_id, string where, string start_time, string end_time, bool forMoneyGetter, string saleSheetCondi, string workerId, string planStr, List<CommissionStrategy> lstStrategy, bool querybyorder = false)
        {
            string giftAffectPricePlan = $@"";
            string sheetSellerCodi = @$" and t.oper_id ={workerId} ";
            string arrearsTimeCondi = $@"";
            if (querybyorder) { arrearsTimeCondi = ""; }
            List<CommissionInfo> lstInfos = new List<CommissionInfo>();
            string item_sql = $@"select item_id,other_class from info_item_prop where company_id={company_id}";
            dynamic infos = await CDbDealer.GetRecordsFromSQLAsync(item_sql, cmd);
            foreach (var lst in lstStrategy)
            {
                PlanContent content = JsonConvert.DeserializeObject<PlanContent>(lst.content);
                if (content.rules.Count() > 0)
                {
                    foreach (var rule in content.rules)
                    {
                        if (rule.GiftAffectAvgPrice)
                        {
                            dynamic infos_temp = infos;
                            foreach (var info in infos_temp)
                            {
                                CommissionInfo item = new CommissionInfo();
                                item.item_id = int.Parse(info.item_id);
                                item.other_class = info.other_class;
                                item.plan_id = lst.plan_id;
                                var ruleAndTarget = PlanContent.GetTargetFromItem(content.targets, item);
                                if (ruleAndTarget != null)
                                {
                                    ruleAndTarget.CommissionRule = content.rules.Find(x => x.Name == ruleAndTarget.Rule);
                                    if (ruleAndTarget.CommissionRule != null && ruleAndTarget.CommissionRule.GiftAffectAvgPrice)
                                    {
                                        lstInfos.Add(item);
                                    }
                                }
                            }
                            if (!giftAffectPricePlan.Contains(lst.plan_id))
                            {
                                if (!string.IsNullOrEmpty(giftAffectPricePlan))
                                {
                                    giftAffectPricePlan += ","; // 添加逗号分隔符
                                }
                                giftAffectPricePlan += lst.plan_id;
                            }
                        }
                    }
                }
            }

            string planAndItemSqlSum = @$"";
            if (giftAffectPricePlan.IsValid() && lstInfos.Count() != 0)
            {
                string planAndItem = $@"";
                var lstInfosGroups = lstInfos.GroupBy(x => x.plan_id);

                string total_condition = $@"";
                foreach (var lstInfosGroup in lstInfosGroups)
                {
                    string plan_id = "";
                    string planAndItemSql = @$"";
                    foreach (var lstInfoGroup in lstInfosGroup)
                    {
                        plan_id = lstInfoGroup.plan_id;
                        planAndItem += lstInfoGroup.item_id + ",";
                    }
                    if (planAndItem.EndsWith(","))
                    {
                        planAndItem = planAndItem.Substring(0, planAndItem.Length - 1);
                    }
                    if (plan_id != "")
                    { planAndItemSql += "(" + planStr + ")='" + plan_id + "' and iip.item_id in (" + planAndItem + ")"; }
                    if (planAndItemSql != "")
                    {
                        planAndItemSqlSum += "(" + planAndItemSql + ") or";
                    }
                }
                if (planAndItemSqlSum != "" && planAndItemSqlSum.EndsWith("or"))
                {
                    planAndItemSqlSum = planAndItemSqlSum.Substring(0, planAndItemSqlSum.Length - 2);
                    planAndItemSqlSum = "case when " + planAndItemSqlSum + " then ssd.item_avg_price else (real_price / unit_factor) end as real_price_tmp";
                }
                //planCondition = " where plan_id::int not in (" + giftAffectPricePlan + ")"; 
            }
            if (planAndItemSqlSum == "")
            {
                planAndItemSqlSum = "(real_price / unit_factor) AS real_price_tmp";

            }
            string operIdFld = "ssm.oper_id";
            string operNameFld = "ssm.oper_name";
            if (forMoneyGetter)
            {
                operIdFld = "case when ga.getter_id is not null and oper_type = 'seller' then ga.getter_id else ssm.oper_id end";
                operNameFld = " case when ga.getter_id is not null and oper_type = 'seller' then ga.getter_name else ssm.oper_name end";
            }
            cmd.CommandText = $@"
SELECT {planStr} plan_id, {operIdFld} oper_id,{operNameFld} oper_name,oper_type,iip.item_id,iip.item_name,iip.other_class,mu.b_unit_factor,mu.m_unit_factor,mu.b_unit_no,mu.m_unit_no,mu.s_unit_no, 
{planAndItemSqlSum},case when ga.mm_sheet_id is not null then sign(ssm.total_amount)*(ssm.total_amount-ssm.paid_amount-ssm.disc_amount)>=0.01 else sign(ssm.total_amount)*(ssm.total_amount-ssm.now_pay_amount-ssm.now_disc_amount)>=0.01 END as is_arrears,
sum(case WHEN ssd.real_price=0 AND quantity*ssd.inout_flag<0 THEN -1*ssd.inout_flag*ssm.money_inout_flag*quantity*unit_factor/ssm.oper_count else 0 end) AS x_gift_quantity,
sum(case WHEN ssd.real_price=0 AND quantity*ssd.inout_flag>0 THEN ssd.inout_flag*quantity*unit_factor/ssm.oper_count else 0 end) AS t_gift_quantity,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*sub_amount/ssm.oper_count else 0 end) AS x_amount,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*sub_amount*ssm.money_inout_flag*quantity/abs(quantity)/ssm.oper_count else 0 end) AS t_amount,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag*(ssd.cost_price_buy::numeric)*quantity*unit_factor /ssm.oper_count                        else 0 end) AS x_cost_buy_price,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*ssm.money_inout_flag*(ssd.cost_price_buy::numeric)*quantity*unit_factor * quantity/abs(quantity)/ssm.oper_count else 0 end) AS t_cost_buy_price,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag * ssd.cost_price_avg*quantity*unit_factor/ssm.oper_count                        else 0 end) AS x_cost_price_avg,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*ssm.money_inout_flag * ssd.cost_price_avg*quantity*unit_factor * quantity/abs(quantity)/ssm.oper_count else 0 end) AS t_cost_price_avg,

sum(case when ssd.real_price >0 AND quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag*quantity*unit_factor/ssm.oper_count else 0 end) AS x_quantity,
sum(case when ssd.real_price >0 AND quantity*ssd.inout_flag>0 then ssd.inout_flag*quantity*unit_factor/ssm.oper_count else 0 end) AS t_quantity
FROM sheet_sale_detail ssd
left join 
(    SELECT t.*,op.oper_name FROM 
    (
    select row_index,coalesce(d_seller_id,sm1.seller_id) oper_id,'seller' oper_type,sm1.*,1 as oper_count 
    from (
    select  d_seller_id,row_index,sheet_id sheet_id_tmp from sheet_sale_detail 
    where company_id={company_id}  {saleSheetCondi}    
    ) sd 
    left join 
    ( select * from sheet_sale_main sm where company_id={company_id} AND red_flag is null AND approve_time is not null {saleSheetCondi} 
    )sm1 on sm1.company_id={company_id} and sm1.sheet_id=sd.sheet_id_tmp
    where red_flag is null AND approve_time is not null
    union
    select sd.row_index,m.* from 
    (select row_index,sheet_id sheet_id_tmp from sheet_sale_detail WHERE company_id={company_id} {saleSheetCondi} )sd  left join 
    ( select regexp_split_to_table(senders_id, E',+')::int as oper_id,'sender' oper_type,*,array_length(string_to_array(senders_id, ','), 1) as oper_count from 
    sheet_sale_main WHERE company_id={company_id} AND approve_time is not null  {saleSheetCondi} )m on m.sheet_id=sd.sheet_id_tmp
    where red_flag is null AND approve_time is not null
    )t
	LEFT JOIN info_operator op on op.company_id = t.company_id and op.oper_id = t.oper_id
 ) ssm ON ssm.sheet_id = ssd.sheet_id and ssm.row_index=ssd.row_index
LEFT JOIN info_item_prop iip ON ssd.item_id = iip.item_id and iip.company_id = {company_id}
LEFT JOIN 
(
    SELECT us.item_id,us.unit_no s_unit_no, us.barcode s_barcode,1 s_unit_factor,us.retail_price s_retail_price, us.wholesale_price s_wholesale_price,
                                 ub.unit_no b_unit_no, ub.barcode b_barcode,  ub.unit_factor b_unit_factor,
                                 um.unit_no m_unit_no, um.barcode m_barcode,  um.unit_factor m_unit_factor
               FROM      info_item_multi_unit us
               LEFT JOIN info_item_multi_unit ub on us.item_id=ub.item_id and ub.unit_type='b' and ub.company_id={company_id}
               LEFT JOIN info_item_multi_unit um on us.item_id=um.item_id and um.unit_type='m' and um.company_id={company_id}  
               WHERE us.company_id={company_id} and us.unit_type='s'
) mu on mu.item_id = ssd.item_id
left join
(
    select getter_id,mm_sheet_id,gad.happen_time,oper_name getter_name from sheet_get_arrears_detail gad 
    left join sheet_get_arrears_main gam on gad.sheet_id=gam.sheet_id  and gad.company_id=gam.company_id 
    left join info_operator io on gam.getter_id=io.oper_id and io.company_id=gad.company_id
    where gam.red_flag is null and gam.approve_time is not null and gam.happen_time >= '{start_time}' AND gam.happen_time <= '{end_time}' and gad.happen_time >= '{start_time}' AND gad.happen_time <= '{end_time}' and gad.company_id={company_id} and abs(gad.left_amount)<0.01  
) ga  on ssd.sheet_id=ga.mm_sheet_id  
LEFT JOIN info_supcust sup on sup.company_id = ssm.company_id and sup.supcust_id = ssm.supcust_id
LEFT JOIN info_item_prop ip on ip.company_id = ssd.company_id and ip.item_id = ssd.item_id
{where}  and {planStr} is not null 
GROUP BY plan_id,{operIdFld} ,{operNameFld},iip.item_id,iip.item_name,iip.other_class,mu.b_unit_factor,m_unit_factor,b_unit_no,m_unit_no,s_unit_no,is_arrears,ssd.company_id,unit_factor,oper_type,real_price_tmp
ORDER BY  oper_id,oper_type";

            var commissionInfos = new List<CommissionInfo>();
            var dr = await cmd.ExecuteReaderAsync();
            while (dr.Read())
            {
                var info = new CommissionInfo();
                CDbDealer.SetObjectByDr(dr, info, false);
                foreach (var lst in lstStrategy)
                {
                    if (info.plan_id == lst.plan_id)
                    {
                        info.plan_name = lst.plan_name;
                        info.plan_source = lst.strategy_type;
                        info.strategy_id = lst.id;
                        info.strategy_name = lst.strategy_name;
                        info.strategy_type = lst.type;
                        info.real_price = info.real_price_tmp;
                        if (info.factor_b == 1)
                        { info.b_real_price = info.real_price; }
                        else
                        {
                            info.b_real_price = decimal.Round(info.real_price * decimal.Parse(info.b_unit_factor), 3);
                        }
                    }

                }
                commissionInfos.Add(info);// new CommissionInfo().ReadFromDataReader(dr.dr_sql));
            }
            dr.Close();
            return commissionInfos;
        }

        internal static async Task<List<CommissionInfo>> GetList_new(CMySbCommand cmd, string company_id, string where, string start_time, string end_time, bool forMoneyGetter,string saleSheetCondi,string workerId,string planStr, List<CommissionStrategy> lstStrategy, bool querybyorder=false)
        {
            string workerIdFld = " ssm.oper_id ";
            string sellerCodi = $@" and ssm.oper_id ={workerId} ";
            string sheetSellerCodi = @$" and t.oper_id ={workerId} ";
            //string planCondition = $@"";
            string giftAffectPricePlan = $@"";
            string arrearsTimeCondi = $@"";
            if (querybyorder) { arrearsTimeCondi = ""; }
            if (forMoneyGetter)
            {
                workerIdFld = "case when ga.getter_id is not null and oper_type = 'seller' then ga.getter_id else ssm.oper_id end";
                sellerCodi = "";
                sheetSellerCodi = "";

            }
            List<CommissionInfo> lstInfos=new List<CommissionInfo>();
            string item_sql = $@"select item_id,other_class from info_item_prop where company_id={company_id}";
            dynamic infos = await CDbDealer.GetRecordsFromSQLAsync(item_sql, cmd);
            foreach (var lst in lstStrategy)
            {
                PlanContent content = JsonConvert.DeserializeObject<PlanContent>(lst.content);
                if (content.rules.Count() > 0)
                {  
                    foreach (var rule in content.rules) 
                    {
                        if (rule.GiftAffectAvgPrice)
                        {
                            dynamic infos_temp=infos;
                            foreach (var info in infos_temp)
                            {
                                CommissionInfo item = new CommissionInfo();
                                item.item_id = int.Parse(info.item_id);
                                item.other_class = info.other_class;
                                item.plan_id = lst.plan_id;
                                var ruleAndTarget = PlanContent.GetTargetFromItem(content.targets, item);
                                if (ruleAndTarget != null)
                                {
                                    ruleAndTarget.CommissionRule = content.rules.Find(x => x.Name == ruleAndTarget.Rule);   
                                    if (ruleAndTarget.CommissionRule != null && ruleAndTarget.CommissionRule.GiftAffectAvgPrice)
                                    {
                                        lstInfos.Add(item);
                                    }
                                }
                            }
                            if (!giftAffectPricePlan.Contains(lst.plan_id))
                            {
                                if (!string.IsNullOrEmpty(giftAffectPricePlan))
                                {
                                    giftAffectPricePlan += ","; // 添加逗号分隔符
                                }
                                giftAffectPricePlan += lst.plan_id;
                            }
                        }        
                     }     
                }     
            }

            string planAndItemSqlSum = @$"";
            if (giftAffectPricePlan.IsValid() && lstInfos.Count() != 0)
            {
                string planAndItem = $@"";
                var lstInfosGroups = lstInfos.GroupBy(x => x.plan_id);
                
                string total_condition = $@"";
                foreach (var lstInfosGroup in lstInfosGroups)
                {
                    string plan_id = "";
                    string planAndItemSql = @$"";
                    foreach (var lstInfoGroup in lstInfosGroup)
                    {
                        plan_id = lstInfoGroup.plan_id;
                        planAndItem += lstInfoGroup.item_id + ",";
                    }
                    if (planAndItem.EndsWith(","))
                    {
                        planAndItem = planAndItem.Substring(0, planAndItem.Length - 1);
                    }
                    if (plan_id != "")
                    { planAndItemSql +="("+ planStr+")='" + plan_id + "' and iip.item_id in (" + planAndItem + ")"; }
                    if (planAndItemSql != "")
                    {
                        planAndItemSqlSum += "(" + planAndItemSql + ") or";
                    }
                }
                if (planAndItemSqlSum!=""&&planAndItemSqlSum.EndsWith("or"))      
                {
                    planAndItemSqlSum = planAndItemSqlSum.Substring(0, planAndItemSqlSum.Length - 2);
                    planAndItemSqlSum = "case when " + planAndItemSqlSum + " then ssd.item_avg_price else (real_price / unit_factor) end as real_price_tmp";
                }
                //planCondition = " where plan_id::int not in (" + giftAffectPricePlan + ")"; 
            }
            if (planAndItemSqlSum == "")
            {
                planAndItemSqlSum = "(real_price / unit_factor) AS real_price_tmp";

            }
            
            
            cmd.CommandText = $@"
SELECT {planStr} plan_id,{workerIdFld} oper_id,ssm.oper_name,oper_type,iip.item_id,iip.item_name,iip.other_class,mu.b_unit_factor,mu.m_unit_factor,mu.b_unit_no,mu.m_unit_no,mu.s_unit_no, 
{planAndItemSqlSum},case when ga.mm_sheet_id is not null then sign(ssm.total_amount)*(ssm.total_amount-ssm.paid_amount-ssm.disc_amount)>=0.01 else sign(ssm.total_amount)*(ssm.total_amount-ssm.now_pay_amount-ssm.now_disc_amount)>=0.01 END as is_arrears,
sum(case WHEN ssd.real_price=0 AND quantity*ssd.inout_flag<0 THEN -1*ssd.inout_flag*ssm.money_inout_flag*quantity*unit_factor/ssm.oper_count else 0 end) AS x_gift_quantity,
sum(case WHEN ssd.real_price=0 AND quantity*ssd.inout_flag>0 THEN ssd.inout_flag*quantity*unit_factor/ssm.oper_count else 0 end) AS t_gift_quantity,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*sub_amount/ssm.oper_count else 0 end) AS x_amount,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*sub_amount*ssm.money_inout_flag*quantity/abs(quantity)/ssm.oper_count else 0 end) AS t_amount,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag*(ssd.cost_price_buy::numeric)*quantity*unit_factor /ssm.oper_count                        else 0 end) AS x_cost_buy_price,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*ssm.money_inout_flag*(ssd.cost_price_buy::numeric)*quantity*unit_factor * quantity/abs(quantity)/ssm.oper_count else 0 end) AS t_cost_buy_price,

sum(case when quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag * ssd.cost_price_avg*quantity*unit_factor/ssm.oper_count                        else 0 end) AS x_cost_price_avg,
sum(case when quantity*ssd.inout_flag>0 then -1*ssd.inout_flag*ssm.money_inout_flag * ssd.cost_price_avg*quantity*unit_factor * quantity/abs(quantity)/ssm.oper_count else 0 end) AS t_cost_price_avg,

sum(case when ssd.real_price >0 AND quantity*ssd.inout_flag<0 then -1*ssd.inout_flag*ssm.money_inout_flag*quantity*unit_factor/ssm.oper_count else 0 end) AS x_quantity,
sum(case when ssd.real_price >0 AND quantity*ssd.inout_flag>0 then ssd.inout_flag*quantity*unit_factor/ssm.oper_count else 0 end) AS t_quantity
FROM sheet_sale_detail ssd
LEFT JOIN 
(    SELECT t.*,op.oper_name FROM (select seller_id oper_id,'seller' oper_type, *,1 as oper_count from sheet_sale_main WHERE company_id={company_id} AND seller_id is NOT NULL AND red_flag is null AND approve_time is not null {saleSheetCondi}
      union
     select regexp_split_to_table(senders_id, E',+')::int as oper_id,'sender' oper_type,*,array_length(string_to_array(senders_id, ','), 1) as oper_count from sheet_sale_main WHERE company_id={company_id} AND senders_id is NOT
		 NULL AND red_flag is null AND approve_time is not null  {saleSheetCondi} )t
		 LEFT JOIN info_operator op on op.oper_id = t.oper_id and op.company_id = t.company_id 
		 WHERE op.company_id ={company_id} {sheetSellerCodi}

 ) ssm ON ssm.sheet_id = ssd.sheet_id {sellerCodi}
LEFT JOIN info_item_prop iip ON ssd.item_id = iip.item_id and iip.company_id = {company_id}
LEFT JOIN 
(
    SELECT us.item_id,us.unit_no s_unit_no, us.barcode s_barcode,1 s_unit_factor,us.retail_price s_retail_price, us.wholesale_price s_wholesale_price,
                                 ub.unit_no b_unit_no, ub.barcode b_barcode,  ub.unit_factor b_unit_factor,
                                 um.unit_no m_unit_no, um.barcode m_barcode,  um.unit_factor m_unit_factor
               FROM      info_item_multi_unit us
               LEFT JOIN info_item_multi_unit ub on us.item_id=ub.item_id and ub.unit_type='b' and ub.company_id={company_id}
               LEFT JOIN info_item_multi_unit um on us.item_id=um.item_id and um.unit_type='m' and um.company_id={company_id}  
               WHERE us.company_id={company_id} and us.unit_type='s'
) mu on mu.item_id = ssd.item_id
left join
(
    select getter_id,mm_sheet_id,gad.happen_time from sheet_get_arrears_detail gad 
    left join sheet_get_arrears_main gam on gad.sheet_id=gam.sheet_id  and gad.company_id=gam.company_id where gam.red_flag is null and gam.approve_time is not null and gam.happen_time >= '{start_time}' AND gam.happen_time <= '{end_time}' and gad.happen_time >= '{start_time}' AND gad.happen_time <= '{end_time}' and gad.company_id={company_id} and abs(gad.left_amount)<0.01  
) ga  on ssd.sheet_id=ga.mm_sheet_id  
LEFT JOIN info_supcust sup on sup.company_id = ssm.company_id and sup.supcust_id = ssm.supcust_id
LEFT JOIN info_item_prop ip on ip.company_id = ssd.company_id and ip.item_id = ssd.item_id
{where}  and {planStr} is not null 
GROUP BY plan_id,{workerIdFld},ssm.oper_name,iip.item_id,iip.item_name,iip.other_class,mu.b_unit_factor,m_unit_factor,b_unit_no,m_unit_no,s_unit_no,is_arrears,ssd.company_id,unit_factor,oper_type,real_price_tmp
ORDER BY  {workerIdFld},oper_type";

            var commissionInfos = new List<CommissionInfo>();           
            var dr = await cmd.ExecuteReaderAsync();
            while (dr.Read())
            {
                var info = new CommissionInfo(); 
                CDbDealer.SetObjectByDr(dr, info, false);
                foreach(var lst in lstStrategy)
                {
                    if(info.plan_id == lst.plan_id)
                    {
                        info.plan_name = lst.plan_name;
                        info.plan_source = lst.strategy_type;
                        info.strategy_id = lst.id;
                        info.strategy_name = lst.strategy_name;
                        info.strategy_type = lst.type;
                        info.real_price = info.real_price_tmp;
                        if (info.factor_b == 1)
                        { info.b_real_price = info.real_price; }
                        else
                        {
                            info.b_real_price = decimal.Round(info.real_price * decimal.Parse(info.b_unit_factor), 3);
                        }
                    }

                }
                commissionInfos.Add(info);// new CommissionInfo().ReadFromDataReader(dr.dr_sql));
            }
            dr.Close();   
            return commissionInfos;
        }


        //        internal static async Task<List<CommissionInfo>> GetList(CMySbCommand cmd, string company_id, string operId, string where)
        //        {
        //            cmd.CommandText = $@"
        //SELECT ssm.seller_id as oper_id,senders_id,iip.item_id,iip.item_name,iip.other_class,mu.b_unit_factor,(real_price/unit_factor) as real_price,(case WHEN ssm.total_amount-ssm.paid_amount-ssm.disc_amount>0.01 THEN TRUE ELSE FALSE END) as is_arrears,

        //sum(case WHEN ssd.real_price=0 AND quantity*inout_flag<0 THEN -1*inout_flag*money_inout_flag*quantity*(mu.s_unit_factor::decimal) else 0 end) AS x_gift_quantity,
        //sum(case WHEN ssd.real_price=0 AND quantity*inout_flag>0 THEN -1*inout_flag*money_inout_flag*quantity*(mu.s_unit_factor::decimal) else 0 end) AS t_gift_quantity,

        //sum(case when quantity*inout_flag<0 then -1*inout_flag*sub_amount*money_inout_flag else 0 end) AS x_amount,
        //sum(case when quantity*inout_flag>0 then -1*inout_flag*sub_amount*money_inout_flag*quantity/abs(quantity) else 0 end) AS t_amount,

        //sum(case when quantity*inout_flag<0 then -1*inout_flag*money_inout_flag*(s_buy_price::numeric)*quantity*unit_factor else 0 end) AS x_cost_buy_price,
        //sum(case when quantity*inout_flag>0 then -1*inout_flag*money_inout_flag*(s_buy_price::numeric)*quantity*unit_factor else 0 end) AS t_cost_buy_price,

        //sum(case when quantity*inout_flag<0 then -1*inout_flag*money_inout_flag*ssd.cost_price_avg*quantity*unit_factor else 0 end) AS x_cost_price_avg,
        //sum(case when quantity*inout_flag>0 then-1*inout_flag*money_inout_flag* ssd.cost_price_avg*quantity*unit_factor else 0 end) AS t_cost_price_avg,

        //sum(case when ssd.real_price >0 AND quantity*inout_flag<0 then -1*inout_flag*money_inout_flag*quantity*unit_factor else 0 end) AS x_quantity,
        //sum(case when ssd.real_price >0 AND quantity*inout_flag>0 then -1*inout_flag*money_inout_flag*quantity*unit_factor else 0 end) AS t_quantity,


        //ssd.company_id
        //FROM sheet_sale_detail ssd
        //LEFT JOIN sheet_sale_main ssm ON ssm.sheet_id = ssd.sheet_id
        //LEFT JOIN info_item_prop iip ON ssd.item_id = iip.item_id
        //LEFT JOIN (select item_id,(b->>'f1') as b_unit_factor,(m->>'f1') as m_unit_factor,(s->>'f1') as s_unit_factor,(s->>'f3') as s_buy_price,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price)) as json from info_item_multi_unit where company_id= {company_id} order by item_id',$$values ('s'::TEXT),('m'::text),('b'::text)$$) 
        //                                        as errr(item_id int, s jsonb,m jsonb,b jsonb)) mu on mu.item_id = ssd.item_id
        //{where}
        //GROUP BY (oper_id,senders_id,iip.item_id,iip.item_name,iip.other_class,mu.b_unit_factor,is_arrears,ssd.company_id,real_price,unit_factor)
        //ORDER BY oper_id";

        //            var commissionInfos = new List<CommissionInfo>();
        //            var dr = await cmd.ExecuteReaderAsync();
        //            while (dr.Read()) { 
        //                commissionInfos.Add((CommissionInfo) new CommissionInfo().ReadFromDataReader(dr.dr_sql));
        //            }
        //            dr.Close();
        //            return commissionInfos;
        //        }
    };

    /// <summary>
    /// 提成明细
    /// </summary>
    public class CommissionDetail
    {
        public int Id { get; set; }

        /// <summary>
        /// 员工姓名、商品类别名称或商品名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string OperType { get; set; }
        /// <summary>
        /// 全部销售单据总额
        /// </summary>
        public double Amount_x { get; set; }
        public string Amount_x_str => Amount_x.ToMoneyStr();

        /// <summary>
        /// 欠款单据销售总额
        /// </summary>
        public double Amount_x_arrears { get; set; }
        public string Amount_x_arrears_str => Amount_x_arrears.ToMoneyStr();

        public double Amount_x_from_arrears { get; set; }
        public string Amount_x_from_arrears_str => Amount_x_from_arrears.ToMoneyStr();
        public string Amount_x_real_sale_str => (Amount_x + Amount_x_arrears - Amount_x_from_arrears).ToMoneyStr();

        /// <summary>
        /// 销售数量
        /// </summary>
        public double Quantity_x { get; set; }
        public string Quantity_x_str => Quantity_x.ToMoneyStr(precision: 4);
        public string Quantity_unit_x { get; set; }

        /// <summary>
        /// 退货额
        /// </summary>
        public double Amount_t { get; set; }
        public string Amount_t_str => Amount_t.ToMoneyStr();
        /// <summary>
        /// 销售净额
        /// </summary>
        public double Net_amount => Amount_x - Amount_t;
        public string Net_amount_str => Net_amount.ToMoneyStr();

        /// <summary>
        /// 欠款单据退货总额
        /// </summary>
        public double Amount_t_arrears { get; set; }
        public string Amount_t_arrears_str => Amount_t_arrears.ToMoneyStr();


        public double Amount_t_from_arrears { get; set; }
        public string Amount_t_from_arrears_str => Amount_t_from_arrears.ToMoneyStr();

        /// <summary>
        /// 退货量
        /// </summary>
        public double Quantity_t { get; set; }
        public string Quantity_t_str => Quantity_t.ToMoneyStr(precision: 4);
        public string Quantity_unit_t { get; set; }

        /// <summary>
        /// 销售提成
        /// </summary>
        public double Commission_x { get; set; }
        public string Commission_x_str => Commission_x.ToMoneyStr();

        /// <summary>
        /// 退货部分提成
        /// </summary>
        public double Commission_t { get; set; }
        public string Commission_t_str => Commission_t.ToMoneyStr();

        /// <summary>
        /// 合计提成
        /// </summary>
        public double Commission => Commission_x - Commission_t;
        public string Commission_str => Commission.ToMoneyStr();

        /// <summary>
        /// 销售成本
        /// </summary>
        public double Cost_x { get; set; }

        /// <summary>
        /// 退货成本
        /// </summary>
        public double Cost_t { get; set; }


        /// <summary>
        /// 销售利润
        /// </summary>
        public double Profi_x => Amount_x - Cost_x;

        /// <summary>
        /// 退货利润
        /// </summary>
        public double Profi_t => Amount_t - Cost_t;

        /// <summary>
        /// 合计利润
        /// </summary>
        public double Profi => Profi_x - Profi_t;

        public string RuleDisplay { get; set; }

        public string FormulaDisplay_x { get; set; }
        public string FormulaDisplay_t { get; set; }

        public List<CommissionInfo> Infos { get; set; }
        public string other_info { get; set; } = "";

        public string strategy_id { get; set; }
        public string strategy_name { get; set; }
        public string strategy_type { get; set; }

        public string plan_source { get; set; }
        public string plan_name { get; set; }

        public CommissionDetail(IGrouping<double,CommissionInfo> commissionInfos, CommissionRule commissionRule)
        {
            Infos = commissionInfos.ToList();
            Dictionary<string, decimal> dicUnitQty_x_b = new Dictionary<string, decimal>();
            Dictionary<string, decimal> dicUnitQty_x_m = new Dictionary<string, decimal>();
            Dictionary<string, decimal> dicUnitQty_x_s = new Dictionary<string, decimal>();

            Dictionary<string, decimal> dicUnitQty_t_b = new Dictionary<string, decimal>();
            Dictionary<string, decimal> dicUnitQty_t_m = new Dictionary<string, decimal>();
            Dictionary<string, decimal> dicUnitQty_t_s = new Dictionary<string, decimal>();
            commissionInfos.Aggregate(this, (result, Info) =>
            {
               
                if (commissionRule.DeductArrearages && Info.is_arrears)
                {
                    #region 统计总欠款
                    result.Amount_x_arrears += Info.x_amount;
                    result.Amount_t_arrears += Info.t_amount;

                    #endregion
                }  
                else
                {
                    #region 统计总销量             
                    var qty = Info.GetQty(commissionRule.ByBigUnit, commissionRule.IncludeGift, commissionRule.DeductArrearages);
                    result.Quantity_x += qty[0];
                    result.Quantity_t += qty[1];

                    var qty_s = Info.GetQty(false, commissionRule.IncludeGift, commissionRule.DeductArrearages);
                    SheetRowItem row = new SheetRowItem { b_unit_factor=Info.b_unit_factor, m_unit_factor = Info.m_unit_factor,
                        b_unit_no = Info.b_unit_no, m_unit_no = Info.m_unit_no, s_unit_no = Info.s_unit_no
                    };
          
                    Info.quantity_unit_x = SheetBase<SheetRowBase>.GetUnitQtyFromSmallUnitQty(row,(decimal) qty_s[0], out decimal b_qty,out decimal m_qty,out decimal s_qty);
                    if(b_qty != 0)
                    {
                        if (dicUnitQty_x_b.ContainsKey(Info.b_unit_no)) dicUnitQty_x_b[Info.b_unit_no] += b_qty;
                        else dicUnitQty_x_b.Add(Info.b_unit_no, b_qty);
                    }

                    if (m_qty != 0)
                    {
                        if (dicUnitQty_x_m.ContainsKey(Info.m_unit_no)) dicUnitQty_x_m[Info.m_unit_no] += m_qty;
                        else dicUnitQty_x_m.Add(Info.m_unit_no, m_qty);
                    }
                    if (s_qty != 0)
                    {
                        if (s_qty != 0 && dicUnitQty_x_s.ContainsKey(Info.s_unit_no)) dicUnitQty_x_s[Info.s_unit_no] += s_qty;
                        else dicUnitQty_x_s.Add(Info.s_unit_no, s_qty);
                    }

                    b_qty = 0; m_qty = 0; s_qty = 0;
                    Info.quantity_unit_t = SheetBase<SheetRowBase>.GetUnitQtyFromSmallUnitQty(row, (decimal)qty_s[1], out b_qty, out m_qty, out s_qty);
                    if (b_qty != 0)
                    {
                        if (b_qty != 0 && dicUnitQty_t_b.ContainsKey(Info.b_unit_no)) dicUnitQty_t_b[Info.b_unit_no] += b_qty;
                        else dicUnitQty_t_b.Add(Info.b_unit_no, b_qty);
                    }
                    if (m_qty != 0)
                    {
                        if (dicUnitQty_t_m.ContainsKey(Info.m_unit_no)) dicUnitQty_t_m[Info.m_unit_no] += m_qty;
                        else dicUnitQty_t_m.Add(Info.m_unit_no, m_qty);
                    }
                    if (s_qty != 0)
                    {
                        if (dicUnitQty_t_s.ContainsKey(Info.s_unit_no)) dicUnitQty_t_s[Info.s_unit_no] += s_qty;
                        else dicUnitQty_t_s.Add(Info.s_unit_no, s_qty);
                    }


                    #endregion
                    #region 统计总销售额
                    var amt = Info.GetAmt(commissionRule.DeductArrearages);
                    result.Amount_x += amt[0];
                    result.Amount_t += amt[1];
                    if (Info.FromGetArrears)
                    {
                        result.Amount_x_from_arrears += Info.x_amount;
                        result.Amount_t_from_arrears += Info.t_amount;
                    } 

                    #endregion
                    #region 统计总成本
                    var cost = Info.GetCost(commissionRule.DeductArrearages, commissionRule.CostType);
                    result.Cost_x += cost[0];
                    result.Cost_t += cost[1];
                    #endregion
                }
                return result;
            });

            Quantity_unit_x = "";
            foreach(var k in dicUnitQty_x_b) Quantity_unit_x += k.Value + k.Key; 
            foreach (var k in dicUnitQty_x_m) Quantity_unit_x += k.Value + k.Key;
            foreach (var k in dicUnitQty_x_s) Quantity_unit_x += k.Value + k.Key;

            Quantity_unit_t = "";
            foreach (var k in dicUnitQty_t_b) Quantity_unit_t += k.Value + k.Key;
            foreach (var k in dicUnitQty_t_m) Quantity_unit_t += k.Value + k.Key;
            foreach (var k in dicUnitQty_t_s) Quantity_unit_t += k.Value + k.Key; 

        }

        public CommissionDetail(string operType=null,string name = null, int id=0)
        {
            Name = name;
            Id = id;
            OperType = operType;
        }
    }

    [Route("api/[controller]/[action]")]
    public class CommissionSummaryController : QueryController
    { 
        public CommissionSummaryController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SalesSummaryBySellerModel model = new SalesSummaryBySellerModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        public string GetPlanSql(List<CommissionStrategy> lstStrategy_client, List<CommissionStrategy> lstStrategy_class)
        {
            string sellerPlanStr = "case";
            string senderPlanStr = "case";
            string planStr = "";
            var num_client = lstStrategy_client.Count();
            var num_class = lstStrategy_class.Count();
            if (num_client > 0)
            {
                foreach (var st in lstStrategy_client)
                {
                    if(st.type == "seller")
                    {
                        string orderSourceStr = "";
                        if (st.order_source == "线上")
                        {
                            orderSourceStr = $"and ssm.order_source = 'xcx' ";
                        }
                        else if (st.order_source == "线下")
                        {
                            orderSourceStr = $"and (ssm.order_source !='xcx' or ssm.order_source is null) ";
                        }
                        sellerPlanStr += $@" when ssm.supcust_id = {st.supcust_id} {orderSourceStr} then '{st.plan_id}'  ";             
                    }
                    else if(st.type == "sender")
                    {
                        senderPlanStr += $@" when ssm.supcust_id = {st.supcust_id} then '{st.plan_id}'  ";
                    }
                    //planStr += $@" when ssm.supcust_id = {st.supcust_id} then '{st.plan_id}'  ";
                }
            }
            if (num_class > 0)
            {
                foreach (var st in lstStrategy_class)
                {
                    string rankStr = "", regionStr = "", groupStr = "";
                    if (st.region_id != "") regionStr = $" sup.other_region like '%/'||{st.region_id}||'/%' ";
                    if (st.rank_id != "")
                    {
                        if (regionStr != "") rankStr = $" and sup.sup_rank= {st.rank_id} ";
                        else rankStr = $" sup.sup_rank= {st.rank_id} ";

                    }
                    if (st.group_id != "")
                    {
                        if (regionStr != "" || rankStr != "") groupStr = $" and sup.sup_group = {st.group_id} ";
                        else groupStr = $" sup.sup_group = {st.group_id} ";
                    }
                    if (st.type == "seller")
                    {
                        string orderSourceStr = "";
                        if (st.order_source == "线上")
                        {
                            orderSourceStr = $"and ssm.order_source = 'xcx' ";
                        }
                        else if (st.order_source == "线下")
                        {
                            orderSourceStr = $"and (ssm.order_source !='xcx' or ssm.order_source is null) ";
                        }
                            sellerPlanStr += $@"  when {regionStr} {rankStr} {groupStr}{orderSourceStr} then '{st.plan_id}'  ";
                       
                    }
                    else if(st.type == "sender")
                    {
                        senderPlanStr += $@"  when {regionStr} {rankStr} {groupStr} then '{st.plan_id}'  ";
                    }
                    //planStr += $@"  when {regionStr} {rankStr} {groupStr} then '{st.plan_id}'  ";
                }
            }
            if(sellerPlanStr !="case") sellerPlanStr += " end ";
            if (senderPlanStr != "case") senderPlanStr += " end ";

            if(sellerPlanStr == "case" && senderPlanStr != "case")
            {
                planStr = senderPlanStr;
            }else if(sellerPlanStr != "case" && senderPlanStr == "case")
            {
                planStr = sellerPlanStr;
            }else if(sellerPlanStr != "case" && senderPlanStr != "case")
            {
                planStr = $@"case when oper_type='seller' then {sellerPlanStr} else {senderPlanStr} end ";
            }
            //planStr += " end ";
            return planStr;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="startDay"></param>
        /// <param name="endDay"></param>
        /// <param name="seller_id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetOperInfo(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string result = "OK";
            string msg = "";
            string oper_name = "";
            try
            {
                string sql = $@"select is_seller,is_sender,is_admin,o.oper_name oper_name
                from info_operator o
                left join g_operator go on go.company_id=o.company_id and go.oper_id=o.oper_id
                where o.company_id={companyID} and o.oper_id={operID}";
                dynamic oper_info = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if(oper_info[0].is_sender!=null && oper_info[0].is_seller!=null )
                {
                    if ((oper_info[0].is_sender.ToString().ToLower() == "true" || oper_info[0].is_seller.ToString().ToLower() == "true")&& oper_info[0].is_admin.ToString().ToLower() == "false")
                    {
                        result = "true";
                        oper_name = oper_info[0].oper_name;
                    }
                    else
                    {
                        result = "false";
                    }
                }
            }
            catch
            {
                result = "error";
                msg = "出错了，请联系客服";
            }
            return new JsonResult(new { result, msg, operID,oper_name });
        }
        [HttpGet]
        public async Task<JsonResult> GetQueryRecords(string operKey,string startDay, string endDay, string workerID,bool forMoneyGetter,bool queryByOrder= false)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            

            
            List<System.Dynamic.ExpandoObject> lstWorkers = new List<System.Dynamic.ExpandoObject>();
            if (!workerID.IsValid())
            {
                string sql = $@"
SELECT distinct oper_id worker_id FROM info_operator o
left join commission_strategy_map m  on o.company_id=m.company_id and o.oper_id=m.worker_id
       WHERE o.company_id = {companyID} and m.worker_id is not null";
                //                string sql = @$"
                //select distinct worker_id from
                //(
                //    select seller_id worker_id from sheet_sale_main m where company_id={companyID} and happen_time>='{startDay}' and happen_time<'{endDay}'
                //    union 
                //    select regexp_split_to_table(senders_id, E',+')::int worker_id from sheet_sale_main m where company_id={companyID} and happen_time>='{startDay}' and happen_time<'{endDay}'
                //    union 
                //    select getter_id worker_id from sheet_get_arrears_main  where company_id={companyID} and happen_time>='{startDay}' and happen_time<'{endDay}'
                //              union
                //                SELECT seller_id worker_id FROM sheet_sale_main WHERE company_id = {companyID} and sheet_id in (SELECT mm_sheet_id FROM sheet_get_arrears_detail sd

                //      LEFT JOIN sheet_get_arrears_main sm on sd.company_id = sm.company_id and sm.sheet_id = sd.sheet_id

                //      WHERE sd.company_id = {companyID} and sm.happen_time >= '{startDay}' and sm.happen_time < '{endDay}' and sm.red_flag is null and sm.approve_time is not null
                //)
                //) t
                //";
                lstWorkers = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
             
            }
            else
            {
                dynamic worker = new System.Dynamic.ExpandoObject();
                worker.worker_id = workerID;
                lstWorkers.Add(worker);
            }



            var planMaps = await PlanWithMap.GetList(cmd, Token.CompanyID);
            bool bHasDeductArrear = false;
            string strategysql = $@"SELECT cc.flow_id,cm.company_id,cc.plan_id, plan.plan_name, plan.content,plan.type,cm.strategy_id id,'client' strategy_type,ct.strategy_name, cm.worker_id oper_id,op.oper_name ,cc.supcust_id,null group_id,null rank_id,null region_id,cc.order_source order_source  FROM commission_strategy_map cm 
LEFT JOIN commission_strategy_client cc on cm.company_id = cc.company_id and cm.strategy_id = cc.strategy_id
LEFT JOIN commission_plan plan on plan.company_id = cc.company_id and plan.plan_id = cc.plan_id
LEFT JOIN info_operator op on cm.company_id = op.company_id and cm.worker_id = op.oper_id
LEFT JOIN commission_strategy ct on ct.company_id = cm.company_id and ct.strategy_id = cm.strategy_id
WHERE cm.company_id = {companyID} 
union
SELECT cs.flow_id,cm.company_id,cs.plan_id, plan.plan_name, plan.content,plan.type,cm.strategy_id id,'class' strategy_type,ct.strategy_name, cm.worker_id oper_id,op.oper_name ,null supcust_id,cs.group_id,cs.rank_id,cs.region_id,cs.order_source order_source FROM commission_strategy_map cm 
LEFT JOIN commission_strategy_class cs on cm.company_id = cs.company_id and cm.strategy_id = cs.strategy_id
LEFT JOIN commission_plan plan on plan.company_id = cs.company_id and plan.plan_id = cs.plan_id
LEFT JOIN commission_strategy ct on ct.company_id = cm.company_id and ct.strategy_id = cm.strategy_id
LEFT JOIN info_operator op on cm.company_id = op.company_id and cm.worker_id = op.oper_id
WHERE cm.company_id = {companyID} 
ORDER BY flow_id desc;";
            var list = await CDbDealer.GetRecordsFromSQLAsync(strategysql, cmd);
            List<CommissionStrategy> lstStrategies = new List<CommissionStrategy>();
            List<CommissionStrategy> noPlanStrategies = new List<CommissionStrategy>();
            if (list.Count() > 0)
            {
                foreach(dynamic item in list)
                {
                    var st = JsonConvert.DeserializeObject<CommissionStrategy>(JsonConvert.SerializeObject(item));
                    if (item.plan_id != "")
                    {
                        lstStrategies.Add(st);
                    }
                    else
                    {
                        noPlanStrategies.Add(st);
                    }
                    

                }
            }
            var lstStName = new List<string>();
            if (noPlanStrategies.Count() > 0)
            {
                foreach(var nst in noPlanStrategies)
                {
                    var flag = true;
                    foreach(var st in lstStrategies)
                    {
                        if(st.strategy_name == nst.strategy_name)
                        {
                            flag = false;
                            break;
                        }
                    }
                    if (flag)
                    {
                        if (lstStName.Contains(nst.strategy_name))
                        {
                            continue;
                        }
                        else
                        {
                            lstStName.Add(nst.strategy_name);
                        }
                    }
                    
                }

            }
            //foreach(var name in lstStName)
            //{
            //    foreach (var st in lstStrategies)
            //    {
            //        if(st.strategy_name == name)
            //        {
            //            lstStName.Remove(name);
            //            break;
            //        }
            //    }
            //}
            
            if (lstStName.Count() > 0)
            {
                var errMsg = string.Join(',', lstStName);
                errMsg += " 策略没有设置方案";
                return new JsonResult(new { result = "error", msg = errMsg });

            }
            string msg = "";
            var operGroups = new List<KeyValuePair<CommissionDetail, List<CommissionDetail>>>();
            foreach (dynamic rec in lstWorkers)
            {
                string queryBy = "";
                if (queryByOrder) 
                { 
                    queryBy = "ssm.order_sheet_time"; 
                }
                else 
                {
                    queryBy = "ssd.happen_time";
                }
                var where = $"WHERE ssd.company_id = {companyID} AND ssm.company_id = {companyID} and {queryBy} <= '{endDay}' AND {queryBy} >= '{startDay}'";
                string workerIdFld = " ssm.oper_id ";
                if (forMoneyGetter)
                {
                    workerIdFld = "case when ga.getter_id is not null and oper_type = 'seller' then ga.getter_id else ssm.oper_id end";
                }
                if (workerID.IsValid()) where += $" AND {workerIdFld} IN ( {workerID})";
                string saleSheetCondi = "";
                if (queryByOrder) 
                {  saleSheetCondi = $" and order_sheet_time>='{startDay}' and order_sheet_time<'{endDay}'"; }
                else
                {  saleSheetCondi = $" and happen_time>='{startDay}' and happen_time<'{endDay}'"; }
                List<CommissionInfo> commissionInfos = new List<CommissionInfo>();
                List<CommissionInfo> lstGetArrearsItems = new List<CommissionInfo>();
                string worker_id = rec.worker_id;
                string newCondi = where;
                if(!workerID.IsValid())
                   newCondi += $" AND {workerIdFld} IN ( {worker_id})";
                var lstStrategy_client = new List<CommissionStrategy>();
                var lstStrategy_class = new List<CommissionStrategy>();
                var lstStrategy = new List<CommissionStrategy>();
                if (lstStrategies.Count() > 0)
                {
                    foreach (var st in lstStrategies)
                    {
                        if (st.oper_id == worker_id)
                        {
                            lstStrategy.Add(st);
                            if (st.strategy_type == "class") lstStrategy_class.Add(st);
                            if (st.strategy_type == "client") lstStrategy_client.Add(st);
                            if (!bHasDeductArrear)
                            {
                                
                                dynamic planContent = JsonConvert.DeserializeObject(st.content);
                                foreach (var rule in planContent.rules)
                                {
                                    if ((bool)rule.deductArrearages)
                                    {
                                        bHasDeductArrear = true;
                                    }
                                }
                            }


                        }

                    }
                }             
                string planStr = GetPlanSql(lstStrategy_client,lstStrategy_class);
                if (planStr=="")
                {
                    return new JsonResult(new { result = "Error", msg = "系统尚未设置提成策略" });
                }
                var lst = await CommissionInfo.GetList_newest(cmd, Token.CompanyID, newCondi, startDay, endDay, forMoneyGetter, saleSheetCondi, worker_id, planStr, lstStrategy, queryByOrder);
                commissionInfos.AddRange(lst);
                
                if (bHasDeductArrear)
                {
                    string sql = @$"
select string_agg(mm_sheet_id::text,',') mm_sheet_ids,min(sm.happen_time) min_sale_time 
from sheet_get_arrears_detail d 
LEFT JOIN sheet_sale_main sm on d.company_id = sm.company_id and d.m_sheet_type in ('X','T') and d.mm_sheet_id = sm.sheet_id
LEFT JOIN sheet_get_arrears_main m on d.company_id = m.company_id and d.sheet_id = m.sheet_id and m.happen_time>='{startDay}' and m.happen_time<='{endDay}'
where d.company_id={companyID} and d.happen_time>='{startDay}' and d.happen_time<='{endDay}'  and m.red_flag is null and m.approve_time is not null and abs(d.left_amount)<0.01 and sm.sheet_id is not null";

                    //      if (workerID.IsValid()) sql += $" AND sm.getter_id = {workerID}";
                    cmd.CommandText = sql;
                    string mm_sheet_ids = "", min_sale_time = "";
                    CMySbDataReader dr = await cmd.ExecuteReaderAsync();
                    if (dr.Read())
                    {
                        mm_sheet_ids = CPubVars.GetTextFromDr(dr, "mm_sheet_ids");
                        min_sale_time = CPubVars.GetDateText(CPubVars.GetTextFromDr(dr, "min_sale_time"));
                    }
                    dr.Close();
                    if (mm_sheet_ids != "")
                    {
                        where = $"WHERE ssd.company_id = {companyID} AND ssm.company_id = {companyID} and ssd.sheet_id in ({mm_sheet_ids}) and ssd.happen_time>='{min_sale_time}' and ssd.happen_time<'{startDay}' ";
                        if (forMoneyGetter)
                        { 
                            where += $" and case when ga.getter_id is not null and oper_type = 'seller' then ga.getter_id else ssm.oper_id end in ({worker_id}) "; 
                        }
                        else
                        {
                            where += $" and {workerIdFld} in ({worker_id}) ";
                        }
                        saleSheetCondi = $"and happen_time>='{min_sale_time}' and happen_time<'{startDay}' and sheet_id in ({mm_sheet_ids})";
                        lstGetArrearsItems = await CommissionInfo.GetList_newest(cmd, companyID, where, startDay, endDay, forMoneyGetter, saleSheetCondi, worker_id, planStr, lstStrategy, queryByOrder);
                        lstGetArrearsItems.ForEach(item =>
                        {
                            item.FromGetArrears = true;
                        });
                    }
                }

                var operGroup = commissionInfos.newGroupAndSumByEmployee(lstGetArrearsItems, planMaps,out msg);
                if (msg != "")
                {
                    return new JsonResult(new { result = "Error", msg });
                }
                operGroups.AddRange(operGroup);
            }

            //     var commissionInfos = await CommissionInfo.GetList(cmd, Token.CompanyID, where, startDay, endDay,forMoneyGetter);
            //if (commissionInfos.Count == 0) return new JsonResult(new { result = "OK", msg = "", rows = new List<dynamic>(), rowsCount = 0});



            //var operGroups = commissionInfos.GroupAndSumByEmployee(lstGetArrearsItems,planMaps);
            //    var temp_oper = lstGetArrearsItems.GroupAndSumByEmployee(commissionInfos, planMaps);
            string result = "OK";
            if (msg != "")
            {
                result = "error";
            }
       
            var recordsResult = operGroups.Select((x, i) => {  
                return new KeyValuePair<int, CommissionDetail>(i, x.Key); 
            }).ToDictionary(x => x.Key, x => x.Value);
          
         //   var recordsResult =slt.ToDictionary(x => x.Key, x => x.Value);
            return new JsonResult(new {result,msg, rows= recordsResult, rowsCount = recordsResult.Count,  sumResult = new CommissionDetail().Sum(operGroups.Select(x => x.Key)), });
              
        }
        [HttpGet]
        public async Task<JsonResult> GetQueryRecords_old_230608(string operKey, string startDay, string endDay, string workerID, bool forMoneyGetter)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var where = $"WHERE ssd.company_id = {companyID} AND ssm.company_id = {companyID} and ssd.happen_time <= '{endDay}' AND ssd.happen_time >= '{startDay}'";
            //  var where = $"WHERE ssd.company_id = {companyID} AND ssm.company_id = {companyID} and ssd.happen_time <= '{endDay}' AND ssd.happen_time >= '{startDay}'";
            //workerID = "1828";
            string workerIdFld = " oper_id ";
            if (forMoneyGetter)
            {
                workerIdFld = "case when ga.getter_id is not null and oper_type = 'seller' then ga.getter_id else oper_id end";
            }
            if (workerID.IsValid()) where += $" AND {workerIdFld} IN ( {workerID})";



            //    if (seller_id > 0) where += $" AND  ssm.seller_id = {seller_id}";
            List<CommissionInfo> commissionInfos = new List<CommissionInfo>();
            List<System.Dynamic.ExpandoObject> lstWorkers = new List<System.Dynamic.ExpandoObject>();
            if (!workerID.IsValid())
            {
                string sql = @$"
select distinct worker_id from
(
    select seller_id worker_id from sheet_sale_main m where company_id={companyID} and happen_time>='{startDay}' and happen_time<'{endDay}'
    union 
    select regexp_split_to_table(senders_id, E',+')::int worker_id from sheet_sale_main m where company_id={companyID} and happen_time>='{startDay}' and happen_time<'{endDay}'
    union 
    select getter_id worker_id from sheet_get_arrears_main  where company_id={companyID} and happen_time>='{startDay}' and happen_time<'{endDay}'
) t
";
                lstWorkers = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            }
            else
            {
                dynamic worker = new System.Dynamic.ExpandoObject();
                worker.worker_id = workerID;
                lstWorkers.Add(worker);
            }
            string saleSheetCondi = $" and happen_time>='{startDay}' and happen_time<'{endDay}'";
            foreach (dynamic rec in lstWorkers)
            {
                string worker_id = rec.worker_id;
                string newCondi = where;
                if (!workerID.IsValid())
                    newCondi += $" AND {workerIdFld} IN ( {worker_id})";

                var lst = await CommissionInfo.GetList(cmd, Token.CompanyID, newCondi, startDay, endDay, forMoneyGetter, saleSheetCondi);
                commissionInfos.AddRange(lst);
            }

            //     var commissionInfos = await CommissionInfo.GetList(cmd, Token.CompanyID, where, startDay, endDay,forMoneyGetter);
            //if (commissionInfos.Count == 0) return new JsonResult(new { result = "OK", msg = "", rows = new List<dynamic>(), rowsCount = 0});
            var planMaps = await PlanWithMap.GetList(cmd, Token.CompanyID);
            bool bHasDeductArrear = false;
            foreach (var pm in planMaps)
            {
                foreach (var rule in pm.PlanContent.rules)
                {
                    if (rule.DeductArrearages)
                    {
                        bHasDeductArrear = true;
                    }
                }
            }
            List<CommissionInfo> lstGetArrearsItems = new List<CommissionInfo>();
            if (bHasDeductArrear)
            {
                string sql = @$"
select string_agg(mm_sheet_id::text,',') mm_sheet_ids,min(sm.happen_time) min_sale_time 
from sheet_get_arrears_detail d 
LEFT JOIN sheet_sale_main sm on d.company_id = sm.company_id and d.m_sheet_type in ('X','T') and d.mm_sheet_id = sm.sheet_id
LEFT JOIN sheet_get_arrears_main m on d.company_id = m.company_id and d.sheet_id = m.sheet_id and m.happen_time>='{startDay}' and m.happen_time<='{endDay}'
where d.company_id={companyID} and d.happen_time>='{startDay}' and d.happen_time<='{endDay}'  and m.red_flag is null and abs(d.left_amount)<0.01";

                //      if (workerID.IsValid()) sql += $" AND sm.getter_id = {workerID}";
                cmd.CommandText = sql;
                string mm_sheet_ids = "", min_sale_time = "";
                CMySbDataReader dr = await cmd.ExecuteReaderAsync();
                if (dr.Read())
                {
                    mm_sheet_ids = CPubVars.GetTextFromDr(dr, "mm_sheet_ids");
                    min_sale_time = CPubVars.GetDateText(CPubVars.GetTextFromDr(dr, "min_sale_time"));
                }
                dr.Close();
                if (mm_sheet_ids != "")
                {
                    where = $"WHERE ssd.company_id = {companyID} AND ssm.company_id = {companyID} and ssd.sheet_id in ({mm_sheet_ids}) and ssd.happen_time>='{min_sale_time}' and ssd.happen_time<'{startDay}'";
                    saleSheetCondi = $"and happen_time>='{min_sale_time}' and happen_time<'{startDay}' and sheet_id in ({mm_sheet_ids})";
                    lstGetArrearsItems = await CommissionInfo.GetList(cmd, companyID, where, startDay, endDay, forMoneyGetter, saleSheetCondi);
                    lstGetArrearsItems.ForEach(item =>
                    {
                        item.FromGetArrears = true;
                    });
                }
            }

            var operGroups = commissionInfos.GroupAndSumByEmployee(lstGetArrearsItems, planMaps);
            //    var temp_oper = lstGetArrearsItems.GroupAndSumByEmployee(commissionInfos, planMaps);


            var recordsResult = operGroups.Select((x, i) => {
                return new KeyValuePair<int, CommissionDetail>(i, x.Key);
            }).ToDictionary(x => x.Key, x => x.Value);

            //   var recordsResult =slt.ToDictionary(x => x.Key, x => x.Value);
            return new JsonResult(new { result = "OK", msg = "", rows = recordsResult, rowsCount = recordsResult.Count, sumResult = new CommissionDetail().Sum(operGroups.Select(x => x.Key)), });

        }

        [HttpGet]
        public async Task<JsonResult> GetQueryRecords_old(string operKey, string startDay, string endDay, string workerID, bool forMoneyGetter)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var where = $"WHERE ssd.company_id = {companyID} AND ssm.company_id = {companyID} and ssd.happen_time <= '{endDay}' AND ssd.happen_time >= '{startDay}'";
            //  var where = $"WHERE ssd.company_id = {companyID} AND ssm.company_id = {companyID} and ssd.happen_time <= '{endDay}' AND ssd.happen_time >= '{startDay}'";
            //workerID = "1828";
            string workerIdFld = " oper_id ";
            if (forMoneyGetter)
            {
                workerIdFld = "case when ga.getter_id is not null and oper_type = 'seller' then ga.getter_id else oper_id end";
            }
            if (workerID.IsValid()) where += $" AND {workerIdFld} IN ( {workerID})";



            //    if (seller_id > 0) where += $" AND  ssm.seller_id = {seller_id}";

            var commissionInfos = await CommissionInfo.GetList(cmd, Token.CompanyID, where, startDay, endDay, forMoneyGetter,"");
            //if (commissionInfos.Count == 0) return new JsonResult(new { result = "OK", msg = "", rows = new List<dynamic>(), rowsCount = 0});
            var planMaps = await PlanWithMap.GetList(cmd, Token.CompanyID);
            bool bHasDeductArrear = false;
            foreach (var pm in planMaps)
            {
                foreach (var rule in pm.PlanContent.rules)
                {
                    if (rule.DeductArrearages)
                    {
                        bHasDeductArrear = true;
                    }
                }
            }
            List<CommissionInfo> lstGetArrearsItems = new List<CommissionInfo>();
            if (bHasDeductArrear)
            {
                string sql = @$"select string_agg(mm_sheet_id::text,',') from sheet_get_arrears_detail sd  
                                   LEFT JOIN sheet_get_arrears_main sm on sd.company_id = sm.company_id and sd.sheet_id = sm.sheet_id 
                                   where sd.company_id={companyID} and sd.happen_time>='{startDay}' and sd.happen_time<='{endDay}' and sm.red_flag is null and abs(sd.left_amount)<0.01";

                //      if (workerID.IsValid()) sql += $" AND sm.getter_id = {workerID}";
                cmd.CommandText = sql;
                object ov = await cmd.ExecuteScalarAsync();
                string mm_sheet_ids = "";
                if (ov != null && ov != DBNull.Value) mm_sheet_ids = ov.ToString();
                if (mm_sheet_ids != "")
                {
                    where = $"WHERE ssd.company_id = {companyID} AND ssm.company_id = {companyID} and ssd.sheet_id in ({mm_sheet_ids}) and ssd.happen_time<'{startDay}'";
                    lstGetArrearsItems = await CommissionInfo.GetList(cmd, companyID, where, startDay, endDay, forMoneyGetter,"");
                    lstGetArrearsItems.ForEach(item =>
                    {
                        item.FromGetArrears = true;
                    });
                }
            }

            var operGroups = commissionInfos.GroupAndSumByEmployee(lstGetArrearsItems, planMaps);
            //    var temp_oper = lstGetArrearsItems.GroupAndSumByEmployee(commissionInfos, planMaps);


            var recordsResult = operGroups.Select((x, i) => {
                return new KeyValuePair<int, CommissionDetail>(i, x.Key);
            }).ToDictionary(x => x.Key, x => x.Value);

            //   var recordsResult =slt.ToDictionary(x => x.Key, x => x.Value);
            return new JsonResult(new { result = "OK", msg = "", rows = recordsResult, rowsCount = recordsResult.Count, sumResult = new CommissionDetail().Sum(operGroups.Select(x => x.Key)), });

        }
    }
}
