﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Quartz.Impl.Calendar;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Globalization;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using static Org.BouncyCastle.Bcpg.Attr.ImageAttrib;

namespace ArtisanManage.AppController
{
    /// <summary>
    /// 查看单据
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class SheetAllSheetsController : QueryController
    {
        public SheetAllSheetsController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        /// <summary>
        /// 查看单据--销售单
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="getTotal"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetAllSaleSheets(string operKey, string sheetType, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID,string sellerID, string departID, string branchID, string senderID, string regionsID, string arrears, string approveStatus, string orderStatus, string saleTypes,string timeType, string searchStr, string makeBrief, string detailRemark, string sheetNo, string canSeeMallSheet,bool isSender, string operRegions, bool notLimitViewRangeOnClient=true)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            if (sheetType == null) sheetType = "X";

            string tb_main = "sheet_sale_main";
            string tb_status = "sheet_status_sale";
            string tb_detail = "sheet_sale_detail";
            string order_is_del = "";
            var sale_order_condi = "";
            var sale_order_condi_col = $"order_sheet_id,";
            var condi = $"";
            
            if (sheetType == "XD" || sheetType == "TD")
            {
                tb_main = "sheet_sale_order_main";
                tb_status = "sheet_status_order";
                tb_detail = "sheet_sale_order_detail";
                order_is_del = "and coalesce(m.is_del, false) = false";
                
                sale_order_condi = $"LEFT JOIN (SELECT order_sheet_id, sheet_id as sale_sheet_id,sheet_no as sale_sheet_no,senders_id,senders_name FROM sheet_sale_main WHERE red_flag IS NULL and company_id = '{companyID}' and make_time >= '{startDate}') sm on m.sheet_id = sm.order_sheet_id";
                sale_order_condi_col = "sm.sale_sheet_id,sm.sale_sheet_no,";

                if (orderStatus == "untransferred") condi += " and sm.sale_sheet_id is null ";
                else if (orderStatus == "transferred") condi += " and sm.sale_sheet_id is not null ";
            }
            

            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }

            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }

            if (!"happen_time".Equals(timeType)) {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }

            if (supcustID != null) condi += $" and m.supcust_id = {supcustID} ";
            if (branchID != null) condi += $" and m.branch_id = {branchID} ";

            if (sellerID != null) condi += $" and m.seller_id = {sellerID} ";
            if (operID != null)
            { 
                if (supcustID == null || !notLimitViewRangeOnClient )
                {
                    string senderCondi = " or false ";
                    if (isSender)
                    {
						if (sheetType == "XD" || sheetType == "TD")
						{
							senderCondi += $" or ( ','||m.senders_id||',' like  '%,{operID},%' or ','||st.senders_id||',' like '%,{operID},%' )  ";
						}
						else if (sheetType == "X" || sheetType == "T")
						{
							senderCondi += $" or ( ','||m.senders_id||',' like  '%,{operID},%' )  ";
						}
					}
					

					if (string.IsNullOrEmpty(canSeeMallSheet) || "False".Equals(canSeeMallSheet))
                    {
                        condi += $" and ( m.seller_id = {operID}  {senderCondi} ) ";
                    }
                    else
                    {
                        condi += $" and ( m.seller_id in ('{operID}','-1') {senderCondi} ) ";
                    }
                }
            }

             

            if (departID != null)
            {
				if (supcustID == null || !notLimitViewRangeOnClient)
                {
					condi += $" and o.depart_path like '%/{departID}/%' ";
				}
            }

            if (senderID != null)
            {
                if (sheetType == "XD" || sheetType == "TD")
                {
                    condi += $" and (','||m.senders_id||',' like  '%,{senderID},%' or ','||st.senders_id||',' like  '%,{senderID},%')  ";
                }
                else
                {
                    condi += $" and ','||m.senders_id||',' like  '%,{senderID},%' ";
                }
               
            }
            if (!showRed) condi += " and red_flag is null ";
            if (makeBrief != null) condi += $" and make_brief like '%{makeBrief}%'";
            string detailLeftJoin = "";
            if (detailRemark != null)
            {
                condi += $" and sd.sheet_id is not null";
                detailLeftJoin = $@"
 left join 
(
    select distinct sheet_id from {tb_detail} sd where company_id ={companyID} and happen_time >='{startDate}' and remark like '%{detailRemark}%'
) sd on m.sheet_id=sd.sheet_id
";
            }

            if (approveStatus == "unapproved") condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null ";

            if (saleTypes == "carsale") condi += " and order_sheet_id is null ";
            else if (saleTypes == "accesssale") condi += " and order_sheet_id is not null ";

            if (arrears != null)
            {
                if (arrears == "uncleared")
                {
                    condi += $" and abs(total_amount-disc_amount - paid_amount) >= 0.01";
                }
                else if (arrears == "cleared")
                {
                    condi += $" and abs(total_amount-disc_amount - paid_amount) < 0.01";
                }
            };
            if (operRegions.IsValid())
            {
                var operregions = JsonConvert.DeserializeObject<int[]>(operRegions);
                if (operregions.Length > 0)
                    condi += " and (" + string.Join(" or ", operregions.Select(x => $"'/'||other_region||'/'  like '%/{x}/%'")) + " or  s.supcust_id is null) ";
            }

            if (regionsID.IsValid())
            {
                var regions = JsonConvert.DeserializeObject<int[]>(regionsID);
                if (regions.Length > 0)
                    condi += " and (" + string.Join(" or ", regions.Select(x => $"'/'||other_region||'/'  like '%/{x}/%'")) + " or  s.supcust_id is null) ";
            }

            if (searchStr != null) condi += $" and (sheet_no like '%{searchStr}%' or sup_name like '%{searchStr}%' or s.py_str ilike '%{searchStr}%') ";
            
            SQLQueue QQ = new SQLQueue(cmd);
            var sqlNoLimit = @$"
select 

m.sheet_id,sheet_no,{sale_order_condi_col}m.supcust_id,sup_name,sheet_print_count print_count,m.visit_id,
    m.sheet_attribute->>'bj' as bj,case when sheet_attribute->>'returnAmt' is not null then 'true' end has_return,sheet_attribute->>'free' has_free, sheet_attribute->>'cl' cl, sheet_attribute->>'dh' dh, 
    sheet_attribute->>'j' j,sheet_attribute->>'h' h,sheet_attribute->>'hr' hr,sheet_attribute->>'hc' hc,sheet_attribute->>'ks' ks,
    m.sheet_attribute->>'display' as display,
    m.happen_time,m.make_time,m.approve_time, round(total_amount::numeric,2) total_amount,now_pay_amount,now_disc_amount,now_pay_amount-coalesce(prepay_amount,0)-((case when sub_type1='ZC' then m.payway1_amount else 0 end)+(case when sub_type2='ZC' then m.payway2_amount else 0 end)+(case when sub_type3='ZC' then m.payway3_amount else 0 end)) as real_pay_amount,coalesce(prepay_amount,0) prepay_amount,total_amount-now_pay_amount-now_disc_amount left_amount, total_quantity,m.seller_id,oper_name,maker_name, sheet_type,paid_amount,disc_amount,
(case when (red_flag is null and approve_time is not null) then 'approved' 
	when red_flag = '1' then 'reded' 
	when red_flag = '2' then 'red' 
    when approve_time is null then 'unapproved' end) as state,(case when (round(abs(total_amount-now_disc_amount-now_pay_amount)::numeric,2) > 0) then 'true' else 'false' end) has_arrears,
(case when now_disc_amount>0 then 'true' else 'false' end) has_disc,((case when sub_type1='ZC' then m.payway1_amount else 0 end)+(case when sub_type2='ZC' then m.payway2_amount else 0 end)+(case when sub_type3='ZC' then m.payway3_amount else 0 end)) as fee_out
from {tb_main} m
{detailLeftJoin}
LEFT JOIN {tb_status} st on st.company_id={companyID} and m.sheet_id=st.sheet_id
left join info_supcust s on s.company_id={companyID} and s.supcust_id = m.supcust_id
left join info_operator o on o.company_id={companyID} and o.oper_id = m.seller_id
left join (select oper_id maker_id,oper_name maker_name from info_operator where company_id={companyID}) maker on maker.maker_id = m.maker_id
left join (select sub_id,sub_type sub_type1 from cw_subject where company_id={companyID}) subject1 on subject1.sub_id = m.payway1_id
left join (select sub_id,sub_type sub_type2 from cw_subject where company_id={companyID}) subject2 on subject2.sub_id = m.payway2_id
left join (select sub_id,sub_type sub_type3 from cw_subject where company_id={companyID}) subject3 on subject3.sub_id = m.payway3_id
{sale_order_condi}
WHERE m.company_id = {companyID} {condi} and sheet_type = '{sheetType}' {order_is_del}";
            string sql = sqlNoLimit + $" order by case when approve_time is null then 0 else 1 end ,  {timeFld} desc  limit {pageSize} offset {startRow}";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                sql = $"select count(sheet_id) as total, sum(total_amount) as amount,sum(total_amount) as total_amount, sum(now_disc_amount) as now_disc_amount, sum(real_pay_amount) as real_pay_amount, sum(coalesce(prepay_amount,0)) as prepay_amount, sum(left_amount) as left_amount,sum(fee_out) as fee_out from ({sqlNoLimit}) t";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            var real_pay_amount = "";            
            var total_amount = "";
            var prepay_amount = "";
            var left_amount = "";
            var now_disc_amount = "";
            var fee_out = "";

            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                    real_pay_amount = CPubVars.GetTextFromDr(dr, "real_pay_amount");
                    total_amount = CPubVars.GetTextFromDr(dr, "total_amount");
                    now_disc_amount = CPubVars.GetTextFromDr(dr, "now_disc_amount");
                    prepay_amount = CPubVars.GetTextFromDr(dr, "prepay_amount");
                    left_amount = CPubVars.GetTextFromDr(dr, "left_amount");
                    fee_out= CPubVars.GetTextFromDr(dr, "fee_out");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount, real_pay_amount, now_disc_amount, total_amount, prepay_amount, left_amount, fee_out });
        }

        /// <summary>
        /// 查看单据--调拨单
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="getTotal"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetAllMoveSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID, string departID, string fromBranchID, string toBranchId, string roleFromBranchesId, string roleToBranchesId, string approveStatus,string timeType,string searchStr,bool isRestrictBranches)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID,out string realOperID);
            var condi = $"where m.company_id = {companyID}";
            
            // if (startDate != null && endDate != null) condi += $"and m.happen_time  >= '{startDate}' and m.happen_time <= '{CPubVars.PadDateWith2359(endDate)}' ";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }

            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }

            if (!"happen_time".Equals(timeType)) {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }
            
            if (supcustID != null) condi += $" and m.supcust_id = {supcustID} ";
            //fromBranchID  toBranchId 条件不能去掉，去掉后，仓管就可以看到其他仓库的单据了
            if (fromBranchID != null) condi += $" and m.from_branch_id in ({fromBranchID}) ";
            if (toBranchId != null) condi += $" and m.to_branch_id in ({toBranchId}) ";

            string roleCondi = "";
            if (isRestrictBranches && roleFromBranchesId.IsValid())
            {
                if (roleCondi != "") roleCondi += " and ";
                roleCondi += $" m.from_branch_id in ({roleFromBranchesId}) ";
            }

            if (isRestrictBranches && roleToBranchesId.IsValid())
            {
                if (roleCondi != "") roleCondi += " and ";
                roleCondi += $" m.to_branch_id in ({roleToBranchesId}) ";
            }

            if (roleCondi != "")
            {
                roleCondi = $"(({roleCondi}) or m.seller_id={realOperID})";
                condi += $" and {roleCondi} ";
            }
           

            if (operID != null) condi += $" and m.seller_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";

            if (!showRed) condi += " and red_flag is null ";
            condi += " and (sheet_attribute->>'assignVan' is null or sheet_attribute->>'assignVan'='')";
            if (approveStatus== "unapproved")      condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null ";

            if (searchStr != null) condi += $" and sheet_no like '%{searchStr}%' ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sqlNoLimit = @$"
select m.sheet_id,sheet_no,f.branch_name as f_branch,t.branch_name as t_branch,m.happen_time,m.make_time,m.approve_time,m.seller_id,oper_name, wholesale_amount,contract_amount,
(
    CASE WHEN ( red_flag IS NULL AND approve_time IS NOT NULL ) THEN
    'approved' 
    WHEN red_flag = '1' THEN
    'reded' 
    WHEN red_flag = '2' THEN
    'red' 

    WHEN approve_time IS NULL THEN
    'unapproved' 
    END 
    ) AS STATE          
    from sheet_move_main m
    left join info_branch as f on f.branch_id = m.from_branch_id and f.company_id ={companyID}
    left join info_branch as t on t.branch_id = m.to_branch_id and t.company_id ={companyID}
    left join info_operator o on o.company_id ={companyID} and o.oper_id = m.seller_id {condi} order by case when approve_time is null then 0 else 1 end ,{timeFld} desc limit {pageSize} offset {startRow}";
            QQ.Enqueue("sheets", sqlNoLimit);
            if (getTotal)
            {
                //sql = $"select count(sheet_id) as total from sheet_move_main m  {condi}";
             string   sql = $@"select count(m.sheet_id) as total, sum(wholesale_amount) as total_wholesale_amount, sum(contract_amount) as total_contract_amount from sheet_move_main m                 
                            left join info_operator o on o.company_id ={companyID} and o.oper_id = m.seller_id {condi}";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            string total_contract_amount = "";
            string total_wholesale_amount = "";

            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    total_wholesale_amount = CPubVars.GetTextFromDr(dr, "total_wholesale_amount");
                    total_contract_amount = CPubVars.GetTextFromDr(dr, "total_contract_amount");

                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, total_wholesale_amount, total_contract_amount, amount = total_wholesale_amount });
        }
        [HttpGet]
        public async Task<JsonResult> GetAllInventSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID,bool showRed, string operID, string departID, string branchID, string approveStatus, string timeType, string searchStr)
         {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where m.company_id = {companyID}";
            // if (startDate != null && endDate != null) condi += $"and m.happen_time  >= '{startDate}' and m.happen_time <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }

            if(!"happen_time".Equals(timeType)) {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }
            
            if (supcustID != null) condi += $" and m.supcust_id = {supcustID} ";
            if (branchID != null) condi += $" and m.branch_id = {branchID} ";
            if (operID != null) condi += $" and m.seller_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (!showRed) condi += " and red_flag is null ";
            if (approveStatus == "unapproved") condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null ";

            if (searchStr != null) condi += $" and sheet_no like '%{searchStr}%' ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
           select 
           m.sheet_id,
           sheet_no,
           branch_name,
           m.happen_time,
           m.make_time,
           m.approve_time,
           m.seller_id,
           oper_name,
           (case 
           when (red_flag is null and approve_time is not null) then 'approved' 
           when red_flag = '1' then 'reded' 
           when red_flag = '2' then 'red' 
           when approve_time is null then 'unapproved' end) as state 
            from sheet_inventory_main m 
            left join info_branch ib on m.branch_id = ib.branch_id
            left join info_operator o on o.oper_id = m.seller_id {condi} order by case when approve_time is null then 0 else 1 end ,  {timeFld} desc limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                //sql = $"select count(sheet_id) as total from sheet_move_main m  {condi}";
                sql = $@"select count(m.sheet_id) as total from sheet_inventory_main m 
                            left join (select sheet_id from sheet_inventory_main m where company_id = {companyID} group by sheet_id ) d on d.sheet_id = m.sheet_id 
                            left join info_operator o on o.oper_id = m.seller_id {condi}";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount });
        }
        
        
         [HttpGet]
        public async Task<JsonResult> GetAllInventReduceSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID,bool showRed, string operID, string departID, string branchID, string approveStatus, string timeType, string searchStr)
         {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where m.company_id = {companyID} and  sheet_type = 'BS' ";
            // if (startDate != null && endDate != null) condi += $"and m.happen_time  >= '{startDate}' and m.happen_time <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }
            
            if (!"happen_time".Equals(timeType)) {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }
            
            if (supcustID != null) condi += $" and m.supcust_id = {supcustID} ";
            if (branchID != null) condi += $" and m.branch_id = {branchID} ";
            if (operID != null) condi += $" and m.seller_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (!showRed) condi += " and red_flag is null ";
            if (approveStatus == "unapproved") condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null ";

            if (searchStr != null) condi += $" and sheet_no like '%{searchStr}%' ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
            select 
            m.sheet_id,
            sheet_no,
            branch_name,
            m.happen_time,
            m.make_time,
            m.approve_time,
            m.seller_id,
            oper_name,
            (case 
            when (red_flag is null and approve_time is not null) then 'approved' 
            when red_flag = '1' then 'reded' 
            when red_flag = '2' then 'red' 
            when approve_time is null then 'unapproved' end) as state 
            from sheet_invent_change_main m 
            left join info_branch ib on m.branch_id = ib.branch_id and ib.company_id ={companyID} 
            left join info_operator o on o.company_id ={companyID} and o.oper_id = m.seller_id {condi} order by case when approve_time is null then 0 else 1 end ,  {timeFld} desc limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                //sql = $"select count(sheet_id) as total from sheet_move_main m  {condi}";
                sql = $@"select count(m.sheet_id) as total from sheet_invent_change_main m 
                            left join (select sheet_id from sheet_invent_change_main m where company_id = {companyID} group by sheet_id ) d on d.sheet_id = m.sheet_id 
                            left join info_operator o on o.company_id ={companyID} and o.oper_id = m.seller_id {condi}";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount });
        }

        [HttpGet]
        public async Task<JsonResult> GetAllStoreStockSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID, string departID, string branchID, string approveStatus, string timeType, string searchStr)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where m.company_id = {companyID}";
            // if (startDate != null && endDate != null) condi += $"and m.happen_time  >= '{startDate}' and m.happen_time <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }

            if (!"happen_time".Equals(timeType))
            {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }

            if (supcustID != null) condi += $" and m.client_id = {supcustID} ";
            if (branchID != null) condi += $" and m.branch_id = {branchID} ";
            if (operID != null) condi += $" and m.seller_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (!showRed) condi += " and red_flag is null ";
            if (approveStatus == "unapproved") condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null ";

            if (searchStr != null) condi += $" and sheet_no like '%{searchStr}%' ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
           select 
           m.sheet_id,
           sheet_no,
           branch_name,
           m.happen_time,
           m.make_time,
           m.approve_time,
           m.seller_id,
           oper_name,
           (case 
           when (red_flag is null and approve_time is not null) then 'approved' 
           when red_flag = '1' then 'reded' 
           when red_flag = '2' then 'red' 
           when approve_time is null then 'unapproved' end) as state 
            from sheet_client_stock_main m 
            left join info_branch ib on m.branch_id = ib.branch_id
            left join info_operator o on o.oper_id = m.seller_id {condi} order by case when approve_time is null then 0 else 1 end ,  {timeFld} desc limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                //sql = $"select count(sheet_id) as total from sheet_move_main m  {condi}";
                sql = $@"select count(m.sheet_id) as total from sheet_client_stock_main m 
                            left join (select sheet_id from sheet_client_stock_main m where company_id = {companyID} group by sheet_id ) d on d.sheet_id = m.sheet_id 
                            left join info_operator o on o.oper_id = m.seller_id {condi}";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount });
        }
        [HttpGet]
        public async Task<JsonResult> GetAllBorrowedItemSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID, string departID, string branchID, string approveStatus, string timeType, string searchStr)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where m.company_id = {companyID} and sheet_type='JH'";
            // if (startDate != null && endDate != null) condi += $"and m.happen_time  >= '{startDate}' and m.happen_time <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }

            if (!"happen_time".Equals(timeType))
            {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }

            if (supcustID != null) condi += $" and m.client_id = {supcustID} ";
            if (branchID != null) condi += $" and m.branch_id = {branchID} ";
            if (operID != null) condi += $" and m.seller_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (!showRed) condi += " and red_flag is null ";
            if (approveStatus == "unapproved") condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null ";

            if (searchStr != null) condi += $" and sheet_no like '%{searchStr}%' ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
           select 
           m.sheet_id,
           sheet_no,
           branch_name,
           m.happen_time,
           m.make_time,
           m.approve_time,
           m.seller_id,
           oper_name,
           (case 
           when (red_flag is null and approve_time is not null) then 'approved' 
           when red_flag = '1' then 'reded' 
           when red_flag = '2' then 'red' 
           when approve_time is null then 'unapproved' end) as state 
            from borrow_item_main m 
            left join info_branch ib on m.branch_id = ib.branch_id
            left join info_operator o on o.oper_id = m.seller_id {condi} order by case when approve_time is null then 0 else 1 end ,  {timeFld} desc limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                //sql = $"select count(sheet_id) as total from sheet_move_main m  {condi}";
                sql = $@"select count(m.sheet_id) as total from borrow_item_main m 
                            left join (select sheet_id from borrow_item_main m where company_id = {companyID}  and sheet_type='JH' group by sheet_id ) d on d.sheet_id = m.sheet_id 
                            left join info_operator o on o.oper_id = m.seller_id {condi}";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount });
        }
        [HttpGet]
        public async Task<JsonResult> GetAllBorrowedReturnSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID, string departID, string branchID, string approveStatus, string timeType, string searchStr)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where m.company_id = {companyID} and sheet_type='HH'";
            // if (startDate != null && endDate != null) condi += $"and m.happen_time  >= '{startDate}' and m.happen_time <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }

            if (!"happen_time".Equals(timeType))
            {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }

            if (supcustID != null) condi += $" and m.client_id = {supcustID} ";
            if (branchID != null) condi += $" and m.branch_id = {branchID} ";
            if (operID != null) condi += $" and m.seller_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (!showRed) condi += " and red_flag is null ";
            if (approveStatus == "unapproved") condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null ";

            if (searchStr != null) condi += $" and sheet_no like '%{searchStr}%' ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
           select 
           m.sheet_id,
           sheet_no,
           branch_name,
           m.happen_time,
           m.make_time,
           m.approve_time,
           m.seller_id,
           oper_name,
           (case 
           when (red_flag is null and approve_time is not null) then 'approved' 
           when red_flag = '1' then 'reded' 
           when red_flag = '2' then 'red' 
           when approve_time is null then 'unapproved' end) as state 
            from borrow_item_main m 
            left join info_branch ib on m.branch_id = ib.branch_id
            left join info_operator o on o.oper_id = m.seller_id {condi} order by case when approve_time is null then 0 else 1 end ,  {timeFld} desc limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                //sql = $"select count(sheet_id) as total from sheet_move_main m  {condi}";
                sql = $@"select count(m.sheet_id) as total from borrow_item_main m 
                            left join (select sheet_id from borrow_item_main m where company_id = {companyID}  and sheet_type='HH' group by sheet_id ) d on d.sheet_id = m.sheet_id 
                            left join info_operator o on o.oper_id = m.seller_id {condi}";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount });
        }

        /// <summary>
        /// 查看单据--收款单
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="getTotal"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetAllArrearsSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID, string departID, string approveStatus,string timeType, string searchStr, string sheetType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            // if (startDate != null && endDate != null) condi += $"and m.happen_time  >= '{startDate}' and m.happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(sheetType))
            {
                sheetType = "SK";
            }
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }
            
            if (!"happen_time".Equals(timeType)) {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }
           
            if (supcustID != null) condi += $" and m.supcust_id = {supcustID} ";
            if (operID != null && supcustID == null) condi += $" and m.getter_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (!showRed) condi += " and red_flag is null ";
            if (approveStatus == "unapproved") condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null ";

            if (searchStr != null) condi += $" and (sheet_no like '%{searchStr}%' or sup_name like '%{searchStr}%') ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
select sheet_id,sheet_no,m.supcust_id,sup_name,happen_time,m.make_time,m.approve_time,m.getter_id,oper_name,sheet_type,(case when now_pay_amount=floor(now_pay_amount) then now_pay_amount else round(now_pay_amount::numeric,2) end) now_pay_amount,
(case when (red_flag is null and approve_time is not null) then 'approved' 
	when red_flag = '1' then 'reded' 
	when red_flag = '2' then 'red' 
    when approve_time is null then 'unapproved' end) as state,(case when now_disc_amount > 0 then 'true' else 'false' end) has_disc,
(case when left_amount > 0 then 'true' else 'false' end) has_arrears 
from sheet_get_arrears_main m
left join info_supcust s on s.supcust_id = m.supcust_id and s.company_id ={companyID}
left join info_operator o on o.oper_id = m.getter_id and o.company_id ={companyID} 
WHERE m.company_id = {companyID} {condi} and sheet_type = '{sheetType}' order by case when approve_time is null then 0 else 1 end ,  {timeFld} desc limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                sql = @$"
select count(sheet_id) as total, sum(now_pay_amount) as amount from sheet_get_arrears_main m 
left join info_supcust s on s.supcust_id = m.supcust_id and s.company_id = {companyID}
left join info_operator o on o.oper_id = m.getter_id and o.company_id={companyID}
where m.company_id = {companyID} {condi} and sheet_type = '{sheetType}'";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount });
        }

        /// <summary>
        /// 查看单据--费用支出单
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="getTotal"></param>
        /// <returns></returns>

        [HttpGet]

        public async Task<JsonResult> GetAllFeeOutSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID, string departID, string approveStatus,string timeType, string searchStr)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            // if (startDate != null && endDate != null) condi += $"and m.happen_time  >= '{startDate}' and m.happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }
            if (!"happen_time".Equals(timeType)) {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }
            if (supcustID != null) condi += $" and m.supcust_id = {supcustID} ";
            if (operID != null && supcustID == null) condi += $" and getter_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (!showRed) condi += " and red_flag is null ";
            if (approveStatus == "unapproved") condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null ";

            if (searchStr != null) condi += $" and (sheet_no like '%{searchStr}%' or sup_name like '%{searchStr}%') ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
select m.sheet_id,sheet_no,m.supcust_id,sup_name,m.happen_time,m.make_time,m.approve_time,(case when total_amount=floor(total_amount) then total_amount else round(total_amount::numeric,2) end) total_amount,getter_id,oper_name,sheet_type,
    fee_sub_id,sub_name,(case when fee_sub_amount=floor(fee_sub_amount) then fee_sub_amount else round(fee_sub_amount::numeric,2) end) fee_sub_amount,
(case when (red_flag is null and approve_time is not null) then 'approved' 
	when red_flag = '1' then 'reded' 
	when red_flag = '2' then 'red' 
    when approve_time is null then 'unapproved' end) as state 
from sheet_fee_out_main m
left join info_supcust s on s.supcust_id = m.supcust_id and s.company_id = {companyID}
left join sheet_fee_out_detail d on m.sheet_id = d.sheet_id and d.company_id = {companyID}
left join cw_subject c on d.fee_sub_id = c.sub_id and c.company_id = {companyID}
left join info_operator o on o.oper_id = m.getter_id and o.company_id = {companyID} 
where m.company_id = {companyID} and m.sheet_type = 'ZC' {condi} order by case when approve_time is null then 0 else 1 end ,  {timeFld}  desc limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                sql = @$"
select count(m.sheet_id) as total, sum(fee_sub_amount) as amount from sheet_fee_out_main m 
left join info_supcust s on s.supcust_id = m.supcust_id and s.company_id = {companyID} 
left join sheet_fee_out_detail d on m.sheet_id = d.sheet_id and d.company_id = {companyID} 
left join info_operator o on o.oper_id = m.getter_id and o.company_id = {companyID} 
WHERE m.company_id = {companyID} and m.sheet_type = 'ZC' {condi}";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount });
        }

        [HttpGet]
        public async Task<JsonResult> GetAllBuyOrderSheets(string operKey, int pageSize, int startRow, bool getTotal, 
            string startDate, string endDate, string supcustID, bool showRed, string operID, 
            string departID, string branchID, string approveStatus, string timeType, string searchStr)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"";
            
            // 处理时间类型
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if("approve_time".Equals(timeType))
            {    
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
            }
            
            // 处理日期范围
            if (startDate != null && endDate != null) 
                condi += $" and m.{timeType} >= '{startDate}' and m.{timeType} <= '{CPubVars.PadDateWith2359(endDate)}'";
            
            // 处理供应商
            if (supcustID != null) 
                condi += $" and m.supcust_id in ({supcustID})";
            
            // 处理红单显示
            if (!showRed) 
                condi += " and m.red_flag is null";
            
            // 处理操作员
            if (operID != null) 
                condi += $" and m.maker_id in ({operID})";
            
            // 处理部门
            if (departID != null) 
                condi += $" and o.depart_id in ({departID})";
            
            // 处理门店
            if (branchID != null) 
                condi += $" and m.branch_id in ({branchID})";
            
            // 处理审核状态
            if (approveStatus != null)
            {
                if (approveStatus == "approved")
                    condi += " and m.approve_time is not null";
                else if (approveStatus == "unapproved")
                    condi += " and m.approve_time is null";
            }
            
            // 处理搜索条件
            if (searchStr != null) 
                condi += $" and (m.sheet_no like '%{searchStr}%' or s.sup_name like '%{searchStr}%')";
            
            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
        select 
            m.sheet_id,
            m.sheet_no,
            m.supcust_id,
            s.sup_name,
            m.happen_time,
            m.make_time,
            m.approve_time,
            m.maker_id as seller_id,
            o.oper_name,
            b.branch_name,
            (case when m.total_amount=floor(m.total_amount) then m.total_amount else round(m.total_amount::numeric,2) end) total_amount,
            (case when m.approve_time is not null then 'approved' 
                when m.red_flag = 1 then 'red' 
                else 'unapproved' end) as state,
            m.buy_order_status
        from sheet_buy_order_main m
        left join info_supcust s on s.supcust_id = m.supcust_id and s.company_id = {companyID}
        left join info_operator o on o.oper_id = m.maker_id and o.company_id = {companyID}
        left join info_branch b on b.branch_id = m.branch_id and b.company_id = {companyID}
        WHERE m.company_id = {companyID} {condi} 
        order by case when m.approve_time is null then 0 else 1 end, {timeFld} desc 
        limit {pageSize} offset {startRow};";

            QQ.Enqueue("sheets", sql);
            
            if (getTotal)
            {
                sql = $@"
        select 
            count(m.sheet_id) as total,
            sum(case when m.total_amount=floor(m.total_amount) then m.total_amount else round(m.total_amount::numeric,2) end) as amount
        from sheet_buy_order_main m
        left join info_supcust s on s.supcust_id = m.supcust_id and s.company_id = {companyID}
        left join info_operator o on o.oper_id = m.maker_id and o.company_id = {companyID}
         WHERE m.company_id = {companyID} {condi}";
                QQ.Enqueue("count", sql);
            }
            
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount });
        }
        /// <summary>
        /// 查看单据--预收款单
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="getTotal"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetAllPrepaySheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID, string departID, string sheetType, string approveStatus,string timeType, string searchStr)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            // if (startDate != null && endDate != null) condi += $"and p.happen_time  >= '{startDate}' and p.happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if("approve_time".Equals(timeType))
            {    
                timeFld = "case when p.approve_time is not null then p.approve_time else p.make_time end";
            }
            else
            {
                timeFld = @$"p.{timeType}";
            }
            condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            if(!"happen_time".Equals(timeType)) {
                condi += @$" and p.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }
            
            if (supcustID != null) condi += $" and p.supcust_id = {supcustID} ";
            if (operID != null && supcustID == null) condi += $" and getter_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (!showRed) condi += " and red_flag is null ";
            if (approveStatus == "unapproved") condi += " and p.approve_time is null ";
            else if (approveStatus == "approved") condi += " and p.approve_time is not null ";

            if (searchStr != null) condi += $" and (sheet_no like '%{searchStr}%' or sup_name like '%{searchStr}%') ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
select sheet_id,sheet_no,p.supcust_id,sup_name,happen_time,make_time,approve_time,(case when total_amount=floor(total_amount) then total_amount else round(total_amount::numeric,2) end) total_amount,getter_id,oper_name,sheet_type,now_pay_amount,paid_amount,now_disc_amount,disc_amount,
total_amount-now_pay_amount-now_disc_amount left_amount,
(case when (red_flag is null and approve_time is not null) then 'approved' 
	when red_flag = '1' then 'reded' 
	when red_flag = '2' then 'red' 
    when approve_time is null then 'unapproved' end) as state ,
(case when (round(abs(total_amount-now_disc_amount-now_pay_amount)::numeric,2) > 0) then 'true' else 'false' end) has_arrears,
(case when now_disc_amount>0 then 'true' else 'false' end) has_disc
FROM sheet_prepay p
LEFT JOIN info_supcust s on s.supcust_id = p.supcust_id and s.company_id = {companyID} 
LEFT JOIN info_operator o on o.oper_id = p.getter_id and o.company_id = {companyID}
WHERE p.company_id = {companyID} {condi} and sheet_type = '{sheetType}' order by case when approve_time is null then 0 else 1 end ,  {timeFld} desc limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                sql = @$"
SELECT count(sheet_id) as total, sum(total_amount) as amount , sum(now_disc_amount) as now_disc_amount, sum(now_pay_amount) as now_pay_amount,sum(total_amount-now_pay_amount-now_disc_amount) as left_amount from sheet_prepay p
LEFT JOIN info_supcust s on s.supcust_id = p.supcust_id and s.company_id ={companyID}
LEFT JOIN info_operator o on o.oper_id = p.getter_id and o.company_id = {companyID}
WHERE p.company_id = {companyID} {condi} and sheet_type = '{sheetType}'";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            var total_now_disc_amount = "";
            var total_left_amount = "";
            var total_now_pay_amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                    total_now_disc_amount = CPubVars.GetTextFromDr(dr, "now_disc_amount");
                    total_left_amount = CPubVars.GetTextFromDr(dr, "left_amount");
                    total_now_pay_amount = CPubVars.GetTextFromDr(dr, "now_pay_amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount, total_now_disc_amount, total_left_amount, total_now_pay_amount });
        }

        /// <summary>
        /// 查看单据--采购单
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="getTotal"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetBuySheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID, string departID, string branchID, string approveStatus, string timeType,string searchStr, string sheetType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            // if (startDate != null && endDate != null) condi += $" and m.happen_time  >= '{startDate}' and m.happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }
            if (!"happen_time".Equals(timeType)) {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }
            
            if (supcustID != null) condi += $" and m.supcust_id = {supcustID} ";
            if (branchID != null) condi += $" and m.branch_id = {branchID} ";
            if (operID != null) condi += $" and m.seller_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (!showRed) condi += " and red_flag is null ";
            if (approveStatus == "unapproved") condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null ";

            if (searchStr != null) condi += $" and (sheet_no like '%{searchStr}%' or sup_name like '%{searchStr}%') ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
select sheet_id,sheet_no,sheet_type,sheet_attribute->>'forReturn' for_return,sheet_attribute->>'free' for_free,sheet_attribute->>'arrears' arrears,sheet_attribute->>'disc' disc,
    m.supcust_id,sup_name,happen_time,m.make_time,m.approve_time,(case when total_amount=floor(total_amount) then total_amount else round(total_amount::numeric,2) end) total_amount,total_quantity,m.seller_id,oper_name,sheet_type,
(case when (red_flag is null and approve_time is not null) then 'approved' 
	when red_flag = '1' then 'reded' 
	when red_flag = '2' then 'red' 
    when approve_time is null then 'unapproved' end) as state 
from sheet_buy_main m
left join info_supcust s on s.supcust_id = m.supcust_id and s.company_id = {companyID}
left join info_operator o on o.oper_id = m.seller_id and o.company_id = {companyID}
where m.company_id = {companyID} {condi} and sheet_type = '{sheetType}'
order by case when approve_time is null then 0 else 1 end ,  {timeFld} desc limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                sql = @$"
select count(sheet_id) as total, sum(total_amount) as amount from sheet_buy_main m 
left join info_supcust s on s.supcust_id = m.supcust_id and s.company_id =  {companyID}
left join info_operator o on o.oper_id = m.seller_id and o.company_id= {companyID}
WHERE m.company_id = {companyID} {condi} and sheet_type = '{sheetType}'";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount });
        }

        /// <summary>
        /// 查看单据--定货会
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="getTotal"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetAllOrderItemSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID, string departID, string approveStatus,string searchStr, string timeType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"";
            // if (startDate != null && endDate != null) condi += $"and p.happen_time >= '{startDate}' and p.happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if("approve_time".Equals(timeType))
            {    
                timeFld = "case when p.approve_time is not null then p.approve_time else p.make_time end";
            }
            else
            {
                timeFld = @$"p.{timeType}";
            }
            condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            if(!"happen_time".Equals(timeType)) {
                condi += @$" and p.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }
            
            
            if (supcustID != null) condi += $" and p.supcust_id = {supcustID} ";
            if (operID != null && supcustID == null) condi += $" and getter_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (!showRed) condi += " and red_flag is null ";
            if (approveStatus == "unapproved") condi += " and p.approve_time is null ";
            else if (approveStatus == "approved") condi += " and p.approve_time is not null ";

            if (searchStr != null) condi += $" and (sheet_no like '%{searchStr}%' or sup_name like '%{searchStr}%') ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
SELECT sheet_id,sheet_no,sheet_attribute->>'free' for_free,sheet_attribute->>'arrears' arrears,sheet_attribute->>'disc' disc,
    p.supcust_id,sup_name,happen_time,make_time,approve_time,(case when total_amount=floor(total_amount) then total_amount else round(total_amount::numeric,2) end) total_amount,getter_id,oper_name,sheet_type,now_pay_amount,paid_amount,now_disc_amount,disc_amount,
total_amount-now_pay_amount-now_disc_amount left_amount,
(case when (red_flag is null and approve_time is not null) then 'approved' 
	when red_flag = '1' then 'reded' 
	when red_flag = '2' then 'red' 
    when approve_time is null then 'unapproved' end) as state,
(case when (round(abs(total_amount-now_disc_amount-now_pay_amount)::numeric,2) > 0) then 'true' else 'false' end) has_arrears,
(case when now_disc_amount>0 then 'true' else 'false' end) has_disc
from sheet_prepay p
LEFT JOIN info_supcust s on s.supcust_id = p.supcust_id and s.company_id = {companyID} 
LEFT JOIN info_operator o on o.oper_id = p.getter_id and o.company_id =  {companyID} 
WHERE p.company_id = {companyID} {condi} and sheet_type = 'DH' order by case when approve_time is null then 0 else 1 end ,  {timeFld} desc limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                sql = @$"
SELECT count(sheet_id) as total, sum(total_amount) as amount, sum(now_disc_amount) as now_disc_amount, sum(now_pay_amount) as now_pay_amount,sum(total_amount-now_pay_amount-now_disc_amount) as left_amount from sheet_prepay p 
LEFT JOIN info_operator o on o.oper_id = p.getter_id and o.company_id = {companyID} 
LEFT JOIN info_supcust s on s.supcust_id = p.supcust_id and s.company_id = {companyID} 
WHERE p.company_id = {companyID} {condi} and sheet_type = 'DH'";
                
                QQ.Enqueue("count", sql);
                sql = @$"
select sum(b_qty) b_qty,sum(m_qty) m_qty,sum(s_qty) s_qty
from
(
    SELECT sd.item_id,yj_get_unit_qty('b',sum(sd.quantity*sd.unit_factor)::numeric,b_unit_factor,m_unit_factor,false) b_qty,
           yj_get_unit_qty('m',sum(sd.quantity*sd.unit_factor*sd.inout_flag*(-1))::numeric,m_unit_factor,m_unit_factor,false) m_qty,
           yj_get_unit_qty('m',sum(sd.quantity*sd.unit_factor*sd.inout_flag*(-1))::numeric,m_unit_factor,m_unit_factor,false) s_qty
    FROM sheet_prepay p 
    LEFT JOIN sheet_order_item_detail sd on p.sheet_id=sd.sheet_id and sd.company_id = {companyID} 
    LEFT JOIN 
    (
        select item_id,     (s->>'f1')::numeric as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_barcode,
                            (b->>'f1')::numeric as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_barcode,
                            (m->>'f1')::numeric as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_barcode
        from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,weight,volume)) as json from info_item_multi_unit where company_id= {companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
        as errr(item_id int, s jsonb,m jsonb,b jsonb) 
    ) t
    ON sd.item_id=t.item_id 
    LEFT JOIN info_operator o on o.oper_id = p.getter_id and o.company_id = {companyID} 
    LEFT JOIN info_supcust s on s.supcust_id = p.supcust_id and s.company_id = {companyID} 
    WHERE p.company_id = {companyID} {condi} and sheet_type = 'DH'
    GROUP BY sd.item_id,b_unit_factor,m_unit_factor
) t";
                QQ.Enqueue("itemSum", sql);


            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            string b_qty = "", m_qty = "", s_qty = "";
            string total_qty = "";
            var total_now_disc_amount = "";
            var total_left_amount = "";
            var total_now_pay_amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                    total_now_disc_amount = CPubVars.GetTextFromDr(dr, "now_disc_amount");
                    total_left_amount = CPubVars.GetTextFromDr(dr, "left_amount");
                    total_now_pay_amount = CPubVars.GetTextFromDr(dr, "now_pay_amount");
                }
                else if (sqlName == "itemSum")
                {
                    dr.Read();
                    b_qty = CPubVars.GetTextFromDr(dr, "b_qty");
                    m_qty = CPubVars.GetTextFromDr(dr, "m_qty");
                    s_qty = CPubVars.GetTextFromDr(dr, "s_qty");
                    if (b_qty != "" && b_qty != "0")
                        total_qty += b_qty + "大";
                    if (m_qty != "" && m_qty != "0")
                        total_qty += m_qty + "中";
                    if (s_qty != "" && s_qty != "0")
                        total_qty += s_qty + "小";
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount, total_qty, total_now_disc_amount, total_left_amount, total_now_pay_amount });
        }

        /// <summary>
        /// 查看单据--其他收入单
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="getTotal"></param>
        /// <returns></returns>

        [HttpGet]

        public async Task<JsonResult> GetAllOtherInComeSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID, string departID, string approveStatus, string timeType, string searchStr)
        {
            
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            // if (startDate != null && endDate != null) condi += $"and m.happen_time  >= '{startDate}' and m.happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }
            
            if (!"happen_time".Equals(timeType)) {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }
            
            if (supcustID != null) condi += $" and m.supcust_id = {supcustID} ";
            if (operID != null && supcustID == null) condi += $" and getter_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (!showRed) condi += " and red_flag is null ";
            if (approveStatus == "unapproved") condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null ";

            if (searchStr != null) condi += $" and (sheet_no like '%{searchStr}%' or sup_name like '%{searchStr}%') ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
select m.sheet_id,sheet_no,m.supcust_id,sup_name,m.happen_time,m.make_time,m.approve_time,(case when total_amount=floor(total_amount) then total_amount else round(total_amount::numeric,2) end) total_amount,getter_id,oper_name,sheet_type,
    fee_sub_id,sub_name,(case when fee_sub_amount=floor(fee_sub_amount) then fee_sub_amount else round(fee_sub_amount::numeric,2) end) fee_sub_amount,
(case when (red_flag is null and approve_time is not null) then 'approved' 
	when red_flag = '1' then 'reded' 
	when red_flag = '2' then 'red' 
    when approve_time is null then 'unapproved' end) as state 
from sheet_fee_out_main m
left join info_supcust s on s.supcust_id = m.supcust_id and s.company_id = {companyID}
left join sheet_fee_out_detail d on m.sheet_id = d.sheet_id and d.company_id = {companyID}
left join cw_subject c on d.fee_sub_id = c.sub_id and c.company_id = {companyID} 
left join info_operator o on o.oper_id = m.getter_id and o.company_id = {companyID}
WHERE m.company_id = {companyID} and m.sheet_type = 'SR' {condi} 
order by case when approve_time is null then 0 else 1 end ,  {timeFld} desc limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                sql = @$"
select count(m.sheet_id) as total, sum(fee_sub_amount) as amount from sheet_fee_out_main m 
left join info_supcust s on s.supcust_id = m.supcust_id and s.company_id = m.company_id 
left join sheet_fee_out_detail d on m.sheet_id = d.sheet_id left join info_operator o on o.oper_id = m.getter_id 
WHERE m.company_id = {companyID} and m.sheet_type = 'SR' {condi}";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount });
        }

        [HttpGet]
        public async Task<JsonResult> GetAllDisplayAgreementSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID,bool showRed, string operID, string departID, string branchID, string approveStatus,int displayTemplateID, string reviewerStatus,string pmDate, string timeType,string searchStr)
         {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            // if (startDate != null && endDate != null) condi += $" and m.happen_time  >= '{startDate}' and m.happen_time <= '{CPubVars.PadDateWith2359(endDate)}' ";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if ("approve_time".Equals(timeType))
            {
                timeFld = "case when m.approve_time is not null then m.approve_time else m.make_time end";
                condi += @$" and (m.approve_time  >= '{startDate}' or m.make_time >= '{startDate}') and (m.approve_time  <= '{CPubVars.PadDateWith2359(endDate)}' or m.make_time <= '{CPubVars.PadDateWith2359(endDate)}')";

            }
            else
            {
                timeFld = @$"m.{timeType}";
                condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            }

            if(!"happen_time".Equals(timeType)) {
                condi += @$" and m.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }
            
            if (supcustID != null) condi += $" and m.supcust_id = {supcustID} ";
            if (displayTemplateID != 0) condi += $" and m.disp_template_id = {displayTemplateID} ";
            //if (paymentYear.HasValue && paymentMonth.HasValue) condi += $" and aa.month = {paymentMonth} and aa.year = {paymentYear} and COALESCE(sdm.maintain_times,0) >= COALESCE(idt.month_maintain_times,0) and month_qty > month_given and (m.disp_template_id is null or (m.disp_template_id is not null and (idt.sign_need_review = false or (idt.sign_need_review = true and m.reviewer is not null and (m.review_refused is null or m.review_refused = false)) ) ))";
            //if (maintainYear.HasValue && maintainMonth.HasValue) condi += $" and aa.month = {maintainMonth} and aa.year = {maintainYear} and COALESCE(idt.month_maintain_times,0) > 0 and (m.disp_template_id is null or (m.disp_template_id is not null and (idt.sign_need_review = false or (idt.sign_need_review = true and m.reviewer is not null and (m.review_refused is null or m.review_refused = false)) )))";
            if (operID != null) condi += $" and m.seller_id = {operID} ";
            if (departID != null) condi += $" and o.depart_id = {departID} ";
            if (reviewerStatus != null)
            {
                if (reviewerStatus.Equals("noReviewer")) condi += " and m.reviewer is null and idt.sign_need_review = true ";
                if (reviewerStatus.Equals("reviewFalse")) condi += " and m.reviewer is not null and m.review_refused = true and idt.sign_need_review = true ";
            }
            
            if (approveStatus == "normal") condi += " and m.red_flag is null ";
            else if (approveStatus == "unapproved") condi += " and m.approve_time is null ";
            else if (approveStatus == "approved") condi += " and m.approve_time is not null and red_flag is null ";
            else if (approveStatus == "red") condi += " and m.red_flag in ('1','2') ";
            else if (approveStatus == "all") condi += " and true ";
            else if (approveStatus == "adjust") condi += " and ((red_sheet_id is not null and approve_time is not null) or adjust_sheet_id is not null) ";
            else if (approveStatus == "terminate") condi += " and terminate_time is not null ";
            else if (approveStatus == "payment") condi += $" and aa.month = {DateTime.ParseExact(pmDate, "ddd MMM dd yyyy HH:mm:ss 'GMT'zzz '(中国标准时间)'", CultureInfo.InvariantCulture).Month} and aa.year = {DateTime.ParseExact(pmDate, "ddd MMM dd yyyy HH:mm:ss 'GMT'zzz '(中国标准时间)'", CultureInfo.InvariantCulture).Year} and m.approve_time is not null and red_flag is null and month_qty > month_given and (m.disp_template_id is null or (m.disp_template_id is not null and (idt.sign_need_review = false or (idt.sign_need_review = true and m.reviewer is not null and (m.review_refused is null or m.review_refused = false)) ) and (idt.give_condition = 'after_sign' or (idt.give_condition = 'after_maintain' and COALESCE(sdm.maintain_times,0) >= COALESCE(idt.month_maintain_times,0))) ))";
            else if (approveStatus == "maintain") condi += $" and aa.month = {DateTime.ParseExact(pmDate, "ddd MMM dd yyyy HH:mm:ss 'GMT'zzz '(中国标准时间)'", CultureInfo.InvariantCulture).Month} and aa.year = {DateTime.ParseExact(pmDate, "ddd MMM dd yyyy HH:mm:ss 'GMT'zzz '(中国标准时间)'", CultureInfo.InvariantCulture).Year} and m.approve_time is not null and red_flag is null and COALESCE(idt.month_maintain_times,0) > 0 and COALESCE(month_qty,0) > 0 and (m.disp_template_id is null or (m.disp_template_id is not null and (idt.sign_need_review = false or (idt.sign_need_review = true and m.reviewer is not null and (m.review_refused is null or m.review_refused = false)) )))";
            
            if (!showRed) condi += " and m.red_flag is null ";

            if (searchStr != null) condi += $" and (sheet_no like '%{searchStr}%' or sup_name like '%{searchStr}%') ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
    select sheet_id,
       sheet_status,
       sheet_type,
       seller_name,
       sheet_no,
       adjust_sheet_id,
       adjust_sheet_no,
       red_sheet_id,
       red_flag,
       red_sheet_no,
       supcust_id,
       sup_name,
       fee_sub_id,
       sub_name,
       happen_time,
       start_time,
       end_time,
       approve_time,
       make_time,
       total_money,
       total_quantity,
       total_amount,
       make_brief,
       reviewer,
       review_refused,
       disp_template_id,
       sign_need_review,
       maintain_need_review,
       give_condition,
       month_maintain_times,
       maintain_interval_days,
       latest_sign_month_day,
    row_count,sum_total_money,sum_total_amount,last_maintain_time,
    json_agg(json_build_object( 'items_name',items_name,'unit_no',unit_no,'month_qty',month_qty,'month_given',month_given)) as payment_items
from
    (
        select *,
       count(0) over () as row_count,
       sum(total_money) over ()  as sum_total_money,
       sum(total_amount) over () as sum_total_amount
from (
select
    m.sheet_id  as sheet_id,
             (case
                  when red_sheet_id is not null then '调整单'
                  when adjust_sheet_id is not null then '已调整'
                  when terminate_time is not null then '已终止'
                  when m.approve_time is null then '未审'
                  when m.approve_time is not null and red_flag is null then '已审' END) as sheet_status,
             (case WHEN red_sheet_id is null THEN '陈列协议' else '陈列协议调整' END)           as sheet_type,
             seller_name,
             sheet_no,
             m.adjust_sheet_id                                                        as adjust_sheet_id,
             adjust_sheet_no,
             m.red_sheet_id                                                           as red_sheet_id,
             red_flag,
             red_sheet_no,
             supcust_id,
             sup_name,
             fee_sub_id,
             sub_name,
             m.happen_time,
             m.start_time,
             m.end_time,
             approve_time,
             make_time,
             total_money,
             total_quantity,
             total_amount                                                         as total_amount,
             make_brief,
             m.reviewer,
             m.review_refused,
             idt.disp_template_id,
			 idt.sign_need_review,
			 idt.maintain_need_review,
			 idt.give_condition,
			 idt.month_maintain_times,
             idt.maintain_interval_days,
             idt.latest_sign_month_day,
             todm.happen_time as last_maintain_time,
             items_name,month_qty,month_given,unit_no,month,year,
             COALESCE(sdm.maintain_times,0) as maintain_times
      from display_agreement_main m
		LEFT JOIN info_display_template idt on idt.company_id = {companyID} and idt.disp_template_id = m.disp_template_id
        LEFT JOIN (
            select sheet_id,items_name,month_qty,month_given,unit_no,month_maintain_times,
				(case when start_month+months-1>12 then start_month+months-13 else start_month+months-1 end) as month,
				(case when start_month+months-1>12 then start_year+1 else start_year end) as year,
				months as month_id
			from (select d.sheet_id,items_id,items_name,unit_no,month_maintain_times,
						unnest(string_to_array((
							COALESCE(month1_qty,0)||','||COALESCE(month2_qty,0)||','||
						COALESCE(month3_qty,0)||','||COALESCE(month4_qty,0)||','||
						COALESCE(month5_qty,0)||','||COALESCE(month6_qty,0)||','||
						COALESCE(month7_qty,0)||','||COALESCE(month8_qty,0)||','||
						COALESCE(month9_qty,0)||','||COALESCE(month10_qty,0)||','||
						COALESCE(month11_qty,0)||','||COALESCE(month12_qty,0)) ,','))::numeric month_qty,
						unnest(string_to_array((
						COALESCE(month1_given,0)||','||COALESCE(month2_given,0)||','||
						COALESCE(month3_given,0)||','||COALESCE(month4_given,0)||','||
						COALESCE(month5_given,0)||','||COALESCE(month6_given,0)||','||
						COALESCE(month7_given,0)||','||COALESCE(month8_given,0)||','||
						COALESCE(month9_given,0)||','||COALESCE(month10_given,0)||','||
						COALESCE(month11_given,0)||','||COALESCE(month12_given,0)) ,','))::numeric month_given,
						unnest(string_to_array('01,02,03,04,05,06,07,08,09,10,11,12',','))::int months,
							to_char(m.start_time,'YYYY')::int start_year,
            to_char(m.start_time,'MM')::int start_month
						from display_agreement_detail d
						left join display_agreement_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID}
                        left join info_display_template idt on idt.disp_template_id = m.disp_template_id and idt.company_id = {companyID}
						where d.company_id = {companyID}
						) as a
        ) aa on aa.sheet_id = m.sheet_id
        LEFT JOIN (select odm.company_id,max(happen_time) as happen_time,disp_sheet_id from op_display_maintain odm
                    left join info_display_template idt on idt.company_id = {companyID} and idt.disp_template_id = odm.disp_temp_id
                    where idt.maintain_need_review = true and odm.reviewer is not null and (odm.review_refused = false or odm.review_refused is null)
                    GROUP BY disp_sheet_id,to_char(happen_time,'YYYY')::int,to_char(happen_time,'MM')::int,odm.company_id
                    )todm on todm.company_id = {companyID} and todm.disp_sheet_id = m.sheet_id and to_char(todm.happen_time,'YYYY')::int=aa.year and to_char(todm.happen_time,'MM')::int=aa.month
        LEFT JOIN sum_display_maintain sdm on sdm.company_id = {companyID} and sdm.disp_sheet_id = m.sheet_id and to_char(sdm.months,'YYYY')::int = aa.year and to_char(sdm.months,'MM')::int  = aa.month
        LEFT JOIN (select sub_id, sub_name from cw_subject where company_id = {companyID}) c on c.sub_id = m.fee_sub_id
        LEFT JOIN (select supcust_id as sup_id, sup_name, py_str, region_id, other_region
                    from info_supcust
                    where company_id = {companyID}
                    and supcust_flag ilike '%C%') s on m.supcust_id = s.sup_id
        left join (select region_id, region_name from info_region where company_id = {companyID}) r
                    on s.region_id = r.region_id
        LEFT JOIN (select oper_id, oper_name as seller_name, depart_id from info_operator where company_id = {companyID}) o
                    on m.seller_id = o.oper_id
        LEFT JOIN (select sheet_id as adjust_sheet_id1, sheet_no as adjust_sheet_no
                    from display_agreement_main
                    where company_id = {companyID}) ad on ad.adjust_sheet_id1 = m.adjust_sheet_id
        LEFT JOIN 
        (
           select sheet_id as red_sheet_id1, sheet_no as red_sheet_no from display_agreement_main where company_id = {companyID}
        ) red on red.red_sheet_id1 = m.red_sheet_id
        where m.company_id = {companyID} {condi}
    )t 

    )tt
group by sheet_id, sheet_status, sheet_type, seller_name, sheet_no, adjust_sheet_id, adjust_sheet_no, red_sheet_id,red_flag,
         red_sheet_no,
         supcust_id,
         sup_name,
         fee_sub_id,
         sub_name, happen_time,
         start_time,
         end_time,
         approve_time,
         make_time,
         total_money,
         total_quantity, total_amount, make_brief,
         reviewer,
         review_refused,
         disp_template_id,
         sign_need_review,
         maintain_need_review,
         give_condition,
         month_maintain_times,
         maintain_interval_days,
         latest_sign_month_day,
         row_count, sum_total_money, sum_total_amount,last_maintain_time
order by (case when approve_time is null then make_time else approve_time end) desc 
limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                //sql = $"select count(sheet_id) as total from sheet_move_main m  {condi}";
                sql = $@"
select count(sheet_id) as total  from
(
    select m.sheet_id from display_agreement_main m 
    LEFT JOIN 
    (
        SELECT sheet_id,items_name,month_qty,month_given,unit_no,month_maintain_times,
			(case when start_month+months-1>12 then start_month+months-13 else start_month+months-1 end) as month,
			(case when start_month+months-1>12 then start_year+1 else start_year end) as year,months as month_id
	    FROM 
        (
            select d.sheet_id,items_id,items_name,unit_no,month_maintain_times,
					unnest(string_to_array((
						COALESCE(month1_qty,0)||','||COALESCE(month2_qty,0)||','||
					COALESCE(month3_qty,0)||','||COALESCE(month4_qty,0)||','||
					COALESCE(month5_qty,0)||','||COALESCE(month6_qty,0)||','||
					COALESCE(month7_qty,0)||','||COALESCE(month8_qty,0)||','||
					COALESCE(month9_qty,0)||','||COALESCE(month10_qty,0)||','||
					COALESCE(month11_qty,0)||','||COALESCE(month12_qty,0)) ,','))::numeric month_qty,
					unnest(string_to_array((
					COALESCE(month1_given,0)||','||COALESCE(month2_given,0)||','||
					COALESCE(month3_given,0)||','||COALESCE(month4_given,0)||','||
					COALESCE(month5_given,0)||','||COALESCE(month6_given,0)||','||
					COALESCE(month7_given,0)||','||COALESCE(month8_given,0)||','||
					COALESCE(month9_given,0)||','||COALESCE(month10_given,0)||','||
					COALESCE(month11_given,0)||','||COALESCE(month12_given,0)) ,','))::numeric month_given,
					unnest(string_to_array('01,02,03,04,05,06,07,08,09,10,11,12',','))::int months,
						to_char(m.start_time,'YYYY')::int start_year,
                    to_char(m.start_time,'MM')::int start_month
			FROM display_agreement_detail d
			left join display_agreement_main m on m.sheet_id = d.sheet_id and m.company_id = {companyID}
            left join info_display_template idt on idt.disp_template_id = m.disp_template_id and idt.company_id = {companyID}
			where d.company_id = {companyID}
		) as a
    ) aa on aa.sheet_id = m.sheet_id
    LEFT JOIN sum_display_maintain sdm on sdm.company_id = {companyID} and sdm.disp_sheet_id = m.sheet_id and to_char(sdm.months,'YYYY')::int = aa.year and to_char(sdm.months,'MM')::int  = aa.month
    left join (select sheet_id from display_agreement_main m where company_id = {companyID} group by sheet_id ) d on d.sheet_id = m.sheet_id 
    LEFT JOIN info_display_template idt on idt.company_id = {companyID} and idt.disp_template_id = m.disp_template_id
    LEFT JOIN 
    (
        select supcust_id as sup_id, sup_name from info_supcust where company_id = {companyID} and supcust_flag ilike '%C%'
    ) s on m.supcust_id = s.sup_id
    LEFT JOIN info_operator o on o.oper_id = m.seller_id AND o.company_id = {companyID}
    WHERE m.company_id = {companyID} {condi} 
    GROUP BY m.sheet_id
) aaa";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            var amount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                    amount = CPubVars.GetTextFromDr(dr, "amount");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total, amount });
        }

        [HttpGet]
        public async Task<JsonResult> GetAllSpecialPriceSheets(string operKey, int pageSize, int startRow, bool getTotal, string startDate, string endDate, string supcustID, bool showRed, string operID, string departID,string approveStatus, string timeType, string searchStr)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            // if (startDate != null && endDate != null) condi += $"and sm.happen_time  >= '{startDate}' and sm.happen_time <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (string.IsNullOrEmpty(timeType))
            {
                timeType = "happen_time";
            }
            string timeFld = timeType;
            if("approve_time".Equals(timeType))
            {    
                timeFld = "case when sm.approve_time is not null then sm.approve_time else sm.make_time end";
            }
            else
            {
                timeFld = @$"sm.{timeType}";
            }
            condi += @$" and {timeFld}  >= '{startDate}' and {timeFld}  <= '{CPubVars.PadDateWith2359(endDate)}'";
            if(!"happen_time".Equals(timeType)) {
                condi += @$" and sm.happen_time  >= '{CPubVars.GetDateText(Convert.ToDateTime(startDate).AddMonths(-3))}' ";
            }
            
            if (supcustID != null) condi += $" and sm.supcust_id = {supcustID} ";
            if (operID != null) condi += $" and sm.seller_id = {operID} ";
            if (departID != null) condi += $" and seller.depart_id = {departID} ";
            if (!showRed) condi += $" and sm.red_flag is null";
            if(approveStatus== "unapproved")
            {
                condi += $" and sm.approve_time is null";
            }else if (approveStatus== "approved")
            {
                condi += $" and sm.approve_time is not null";
            }

            if (searchStr != null) condi += $" and (sheet_no like '%{searchStr}%' or sup_name like '%{searchStr}%') ";

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
SELECT sm.*,sup.sup_name,seller.oper_name seller_name, maker.oper_name maker_name, approver.oper_name approver_name  ,(end_time::date-start_time::date)+1 as days ,(end_time::date-now()::date)+1 left_days,case when approve_time is null then 'unapproved' else case when red_flag is null then 'approved' else case when red_flag ='1' then 'reded' else 'red' end end end sheet_status 
FROM sheet_special_price_main sm 
LEFT JOIN info_supcust sup on sup.company_id ={companyID} and sup.supcust_id = sm.supcust_id  
LEFT JOIN info_operator seller on seller.company_id = {companyID}  and seller.oper_id = sm.seller_id
LEFT JOIN info_operator maker on maker.company_id = {companyID}  and maker.oper_id = sm.maker_id
LEFT JOIN info_operator approver on approver.oper_id = sm.approver_id and approver.company_id = {companyID}   
WHERE sm.company_id = {companyID} {condi} 
order by sm.happen_time desc
limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            if (getTotal)
            {
                sql = $@"
SELECT count(sm.sheet_id)  as total
FROM sheet_special_price_main sm 
LEFT JOIN info_supcust sup on sup.company_id = sm.company_id and sup.supcust_id ={companyID} 
LEFT JOIN info_operator seller on seller.company_id = {companyID}  and seller.oper_id = sm.seller_id
LEFT JOIN info_operator maker on maker.company_id = {companyID}  and maker.oper_id = sm.maker_id
LEFT JOIN info_operator approver on approver.oper_id = sm.approver_id and approver.company_id = {companyID}    
WHERE sm.company_id = {companyID} {condi}";
                QQ.Enqueue("count", sql);
            }
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                }
            }
            QQ.Clear();

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total });
        }
    }
}
