﻿@page
@model ArtisanManage.Pages.BaseInfo.VisitDayViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdropdownlist.js"></script>

    <style>
        .operdiv {
            width:100px;
            margin-left:35px;
            line-height: 22px;
            background-color:#ffd7d7;
            border-radius:5px;
            border: 1px solid #ffbdbd;
            padding: 0 4px 0 5px;
            white-space: nowrap;
            overflow:hidden;
            text-overflow:ellipsis;
        }
        .schedule-item{
            display:flex;
            color : #a0a0a0;
            flex-direction:row;
            justify-content:space-between;
            font-size:18px;
            padding:30px;
            cursor:pointer;
        }
        .day-item{
            padding:30px;
            font-size:18px;
            cursor:pointer;
        }
        #oprateBar{
            display:none;
        }
        
        #chooseCopyDay-container{
            display:flex;
            flex-direction:row;
            overflow:hidden;
        }
        #chooseCopyDay-left{
            font-size:16px;
            width:30%;
            overflow-y:scroll;
        }
        #chooseCopyDay-left>div{
            padding:10px;
        }
        #chooseCopyDay-right>div{
            padding:10px;
        }
        #chooseCopyDay-right{
            width:70%;
            margin-left:20px;
            font-size:16px;
            overflow-y:scroll;
        }
    </style>
</head>

<body>
    <div style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">

            <div style="float:none;height:0px; clear:both;"></div>

        </div>

        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button onclick="btnAddItem_click()" class="margin" style="width:80px;margin-right:20px;">添加日程</button>
        <div onmouseout="hideOprateBar()"  onmouseover="showOprateBar()" style="z-index:999999;width:100px;position:absolute;right:0px;top:56px;">
            <div id="oprateBar">
                <button style="width:96px" onclick="copyVisitDay()">复制</button>
            </div>
           </div>
           <button onmouseout="hideOprateBar()"  onmouseover="showOprateBar()"  id="operateBarBtn" class="btnright"  style="width: 28px; border-radius: 0 3px 3px 0; border-left: 1px solid #666;vertical-align: top; margin-left: -25px;border: #e2e2e2 2px solid;background:#e0e0e0;text-align:center;">
               <img  src="~/PrintTemplate/img/down-triangle.svg" style="margin-top: -1px; width: 14px; display: inline-block;vertical-align: middle;" />
        </button>
        </div>
        <div>

       

        @*<button id="btnExport" onclick="ExportExcel()" class="margin">导出</button>*@
        

    </div>
    <div style="flex-grow:1;display:flex;width:100%;height:100%;">
        <!--左侧行程-->
        <div id='schedule_id' style="width:250px;height:calc(100% - 20px);margin-top:20px;margin-bottom:2px;overflow:scroll">
        </div>
        <!--右侧主表格-->
        <div style="width:calc(100% - 200px);height:100%; margin-left:10px;">
            <!--上部 统计行-->
            <div><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>
            <!--表格部分-->
            <div id="gridItems" style="margin-top:0px;margin-left:10px; margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 20px);"></div>
        </div>
    </div>
    <div style="display:flex;height:30px;width:100%;margin-bottom:0px;">
    </div>

    
    <div id="popSchedule" style="display:none;">
        <div style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">行程</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    
    <div id="popAssignOper" style="display:none">
        <div style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">分配业务员</span></div>
        <div style="overflow:hidden;"> 
            <div id="div_oper" style="display:flex;margin:10px 0 0 20px;">
                <label style="line-height:25px"> 业务员 </label>
                <div id="jqxdropdownlist" style="margin-left:10px"> </div>
            </div>
            <div style="text-align:center;margin-top:50px;">
                <button id="btnSave" onclick="btnSaveOpers_Clicked();" style="margin-right:50px;" >保存</button> 
                <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
            </div>
        </div>
       
    </div>
    <div id='chooseCopyDay-container' class='chooseCopyDay-container'>
           <div style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择日程</span></div>
        <div   style="display:flex;flex-direction:row;padding:10px;">
            <div id='chooseCopyDay-left'></div>
            <div id='chooseCopyDay-right'></div>
        </div>
    </div>
    <div id='jqxMenu_1' style="display:none;">
        <ul>
            <li id="mnuEditSchedule" onclick="btnEditSchedule()">编辑行程</li>
            <li id="mnuRemoveSchedule" onclick="btnRemoveSchedule()">删除行程</li>
            <li id="AssignOper" onclick="btnAssignOper()">分配业务员</li>
        </ul>
    </div>
    <div id='jqxMenu_0'>
        <ul>
            <li id="mnuAddSchedule" onclick="btnAddSchedule()">添加行程</li>
        </ul>
    </div>



    <script>
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var g_operVisitInfo = @Html.Raw(Model.OperVisitInfo)
        var g_schedules=[]
        var g_rows=[]
        var RowIndex = -1;



        window.addEventListener('message', function (rs) {
            console.log(rs)
            if (rs.data.msgHead == "VisitDayEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var rows = window.gridData_gridItems.localRows
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = rs.data.record;
                        rows[0].i = rows[0].day_id;
                        rows[0].status = rows[0].cls_status_name

                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "day_name", rs.data.record.day_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "sups_name",rs.data.record.sups_name);
                }
            }
            else if (rs.data.msgHead == "VisitScheduleEdit") {
                var newID = ""; var newName = "";
                if (rs.data.record) { newID = rs.data.record.schedule_id; newName = rs.data.record.schedule_name; }
                if (rs.data.action == "add") {
                    var sltItem = $('#schedule_id').jqxTree('findItem', '0');
                    $('#schedule_id').jqxTree('addTo', { value: newID, label: newName }, sltItem.element, false);
                    $('#schedule_id').jqxTree('render');   // update the tree.
                }
                else if (rs.data.action == "update") {
                    var sltItem = $('#schedule_id').jqxTree('findItem', rs.data.record.schedule_id);
                    $('#schedule_id').jqxTree('updateItem', sltItem, { label: newName });
                    $('#schedule_id').jqxTree('render');

                }
                $("#popSchedule").jqxWindow('close');
            }
        });
    
        var newCount = 1;

        // 添加行程
        function btnAddSchedule(){
    	    var selectedItem = $('#schedule_id').jqxTree('selectedItem');
    	    if (!selectedItem) {
    	        bw.toast("请先选择一个行程",3000);
    	        return;
    	    }
            $('#popSchedule').jqxWindow('open');
            $("#popSchedule").jqxWindow('setContent', `<iframe src="VisitScheduleEdit?operKey=${g_operKey}&mother_id=${selectedItem.value}&mother_name=${selectedItem.label}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
    	};
        //编辑行程
        function btnEditSchedule() {
    	    var selectedItem = $('#schedule_id').jqxTree('selectedItem');
    	    if (!selectedItem) {
    	        bw.toast("请先选择一个行程");
    	        return;
    	    }
    	    $('#popSchedule').jqxWindow('open');
            $("#popSchedule").jqxWindow('setContent', `<iframe src="VisitScheduleEdit?operKey=${g_operKey}&schedule_id=${selectedItem.value}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
    	};

        //删除行程
        function btnRemoveSchedule() {
    	    var selectedItem = $('#schedule_id').jqxTree('selectedItem');
    	    if (!selectedItem) {
    	            bw.toast("请先选择一个行程",3000);
    	        return;
            }
            jConfirm('确定要删除' + selectedItem.label+'吗？', function () {

                ajaxPost('../api/VisitDayView/RemoveSchedule',{schedule_id: selectedItem.value }).then(data=>{
                    if (data.result == "OK") {
                            var sltItem = $('#schedule_id').jqxTree('findItem', data.schedule_id);
                            $('#schedule_id').jqxTree('removeItem', sltItem, false);
                            $('#schedule_id').jqxTree('render');
    	            }
    	            else {
                            bw.toast(data.result,3000);
    	            }
                }).catch(error=>{
                    console.log(error)
                })
		            
            }, "");
        };

        // 打开页面，渲染业务员列表
        function appendOperDiv() {
            var items = $('#schedule_id').jqxTree('getItems')
            
            items.forEach(item=>{
                for(var v in g_operVisitInfo) {
                    if(item.value === v) {
                        let operNames = '';
                        g_operVisitInfo[v].operInfos.forEach(oper=>{
                            let operArr = oper
                            if(operNames) operNames += ',';
                            operNames += operArr.oper_name
                        })
                        $(item.element).css('display','flex')
                        $(item.element).append(`<div class='operdiv'>${operNames}</div>`)
                    }
                }
                
                
            })
        }

        // 点击分配业务员 jqxdropdownlist
        function btnAssignOper() {
            var selectedItem = $('#schedule_id').jqxTree('selectedItem');
    	    if (!selectedItem) {
    	        bw.toast("请先选择一个行程");
    	        return;
    	    }
            
            $('#popAssignOper').jqxWindow('open');
            
            var source = {
                datatype: "json",
                datafields: [
                    { name: 'oper_id' },
                    { name: 'oper_name' }
                ],
                url: `../api/VisitDayView/GetOpers?operKey=${g_operKey}`
            };
            var dataAdapter = new $.jqx.dataAdapter(source);
            $("#jqxdropdownlist").jqxDropDownList({ 
                source: dataAdapter, displayMember: "oper_name", valueMember: "oper_id", width: 200, height: 25,
                checkboxes:true, placeHolder: "请选择业务员", searchMode:'contains'
            })

            // 在list显示已分配的业务员
            setTimeout(function(){
                let scheduleOperInfo = g_operVisitInfo[selectedItem.value]
                let checkedOper = {}
                scheduleOperInfo.operInfos.forEach(oper=>{
                    let operArr = JSON.parse(oper)
                    checkedOper = {
                        value : operArr.oper_id,
                        label : operArr.oper_name
                    }
                    $("#jqxdropdownlist").jqxDropDownList('checkItem', checkedOper)
                })
            },100)
        }

        // 保存分配业务员 并渲染到 tree
        function btnSaveOpers_Clicked() {
            let chooseOpers = $('#jqxdropdownlist').jqxDropDownList('getCheckedItems');
            let selectedItem = $('#schedule_id').jqxTree('selectedItem');
            let chooseOperIDs = '',chooseOperNames = '';
            chooseOpers.forEach(oper=>{
                if (chooseOperIDs) chooseOperIDs += ','
                if (chooseOperNames) chooseOperNames += ','
                chooseOperIDs += oper.value
                chooseOperNames += oper.label
            })
            ajaxPost('../api/VisitDayView/AssignOpers',{chooseOperIDs:chooseOperIDs,schedule_id:selectedItem.value}).then(data=>{
                if(data.result == "OK") {
                    bw.toast('保存成功')
                    $('#popAssignOper').jqxWindow('close');
                    $(selectedItem.element).css('display','flex')
                    if(selectedItem.element.childElementCount > 1) selectedItem.element.lastChild.remove()
                    $(selectedItem.element).append(`<div class='operdiv'>${chooseOperNames}</div>`)
                }else {
                    bw.toast(data.msg,3000)
                }
            }).catch(error => {
                console.log('分配业务员POST请求错误:'+ error)
            })

        }
       
        // 关闭分配业务员弹框
        function btnClose_Clicked() {
            $('#jqxdropdownlist').jqxDropDownList('clearSelection')
            $('#popAssignOper').jqxWindow('close');
        }

        //添加行程
        function btnAddItem_click(e) {
            let selectedItem = $('#schedule_id').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个行程",3000);
                return;
            }
            if(selectedItem.value == '0') {
                bw.toast("请选择其他行程",3000);
                return;
            } 
            let path = $('#schedule_id').jqxTree('getTreePath', selectedItem);

            window.parent.newTabPage('拜访日程', `BaseInfo/VisitDayEdit?schedule_id=${selectedItem.value}&schedule_name=${selectedItem.label}`,window);


        }

        

        function isRightClick(event) {
            var rightclick;
            if (!event) var event = window.event;
            if (event.which) rightclick = (event.which == 3);
            else if (event.button) rightclick = (event.button == 2);
            return rightclick;
        };
        function renderCopyDayLeftContainer(){
            $("#chooseCopyDay-left").html(getCopyScheduleLeftDocument())
            $(".schedule-item").map(key => { 
                var element=$(".schedule-item")[key]
                $(element).hover(e => {
                    $(element).css("color","#333")
                    $(element).css("background","#f0f0f0")
                },e => {
                    $(element).css("color","#a0a0a0")
                    $(element).css("background","#fff")
                })
            })
        }
       function renderCopyDayRightContainer(scheduleName){
            $("#chooseCopyDay-right").html(getCopyDayNameRightDocument(scheduleName))
                      $(".day-item").map(key => { 
                var element=$(".day-item")[key]
                $(element).hover(e => {
                    $(element).css("background","#f0f0f0")
                },e => {
                    $(element).css("background","#fff")
                })
            })
        }
        function getCopyScheduleLeftDocument(){
            const scheduleNames = this.getSchecduleNames(g_rows)
            var elements=scheduleNames.map(name=>{
                return `<div id='schedule-item' class='schedule-item' onmouseover=renderCopyDayRightContainer("${name}")><div>${name}</div><div> <img style="margin-top: -1px; width: 14px; display: inline-block;vertical-align: middle;"  src="/images/right.svg"/></div></div>`
            })
            return elements.join("")
        }
       function getCopyDayNameRightDocument(scheduleName){
            const days = this.getDayByScheduleName(g_rows,scheduleName)
            var elements=days.map(day=>{
                return `<div id='day-item' class='day-item' onclick='gotoCopyVisitDayPage("${day.day_id}")'>${day.day_name}</div>`
            })
            return elements.join("")
        }
        function attachContextMenu() {

            $("#schedule_id").on('mousedown', function (event) {
                if (event.target.tagName == "DIV" && event.target.parentNode.tagName== "LI") {
                    var target = event.target.parentNode;

                    var rightClick = isRightClick(event);
                    var contextMenu = event.target.innerText == "全部" ? contextMenu0 : contextMenu1
                    if (rightClick) {
                        $("#schedule_id").jqxTree('selectItem', target);
                        var scrollTop = $(window).scrollTop();
                        var scrollLeft = $(window).scrollLeft();
                        contextMenu.jqxMenu('open', parseInt(event.clientX) + 5 + scrollLeft, parseInt(event.clientY) + 5 + scrollTop);
                        return false;
                    }

                }

            });

        }
        function gotoCopyVisitDayPage(day_id) {
            window.parent.newTabPage('拜访日程', `BaseInfo/VisitDayEdit?day_id=${day_id}&operate=copy`,window);
        }
       //复制日程
        function copyVisitDay() {
            /** 
            var day_id = args.row.day_id;
             RowIndex = args.rowindex;
             onGridRowEdit(args.rowindex);
    	     var day_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, 'day_id');
            console.log({day_id})
            window.parent.newTabPage('拜访日程', `BaseInfo/VisitDayEdit?day_id=${day_id}&operate=copy`,window);
    	**/
        var that_=this
                  $.ajax({
                url:"/api/VisitDayView/GetQueryRecords",
                data: {
                    operKey:g_operKey,
                    gridID:"gridItems",
                    startRow:'0',
                    endRow:'499'
                },
                success: function(res) {
                    g_rows = res.rows;
                     $("#chooseCopyDay-container").jqxWindow({ isModal: true, modalOpacity:0.3, height: 500, width: 600, theme: 'summer', autoOpen: false, showCloseButton: true,closeButtonSize:32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                     $('#chooseCopyDay-container').jqxWindow('open');
                    that_.renderCopyDayLeftContainer()    
                }
            })

        };
        // 右击编辑日程
        function onGridRowEdit(rowIndex) {
            var day_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'day_id');
            window.parent.newTabPage('拜访日程', `BaseInfo/VisitDayEdit?day_id=${day_id}`,window);
        }
        //
        function showOprateBar() {
            $("#oprateBar").css('display','block')
        }
        function hideOprateBar() {
            $("#oprateBar").css('display','none')
        }
        function getSchecduleNames(g_rows){
            console.log(g_rows)
            console.log(new Set(Object.keys(g_rows).map(key=>{
                return g_rows[key]
            }).map(row=>{
                return row.schedule_name
            })))
            return Array.from(new Set(Object.keys(g_rows).map(key=>{
                return g_rows[key]
            }).map(row=>{
                return row.schedule_name
            })))
        }
        function getDayByScheduleName(g_rows,scheduleName){
            return Object.keys(g_rows).map(key=>{
                return g_rows[key]
            }).filter(row=> row.schedule_name==scheduleName)
        }
        var itemSource = {};

        $(document).ready(function () {
            window.contextMenu0 = $("#jqxMenu_0").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            window.contextMenu1 = $("#jqxMenu_1").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });

            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            QueryData("",res=>{
                console.log(res.rows)
                g_rows=res.rows
            });
  
  
            $("#btnAddSchedule").bind("click", { isParent: false }, btnAddSchedule);
            $("#btnEditSchedule").bind("click", btnEditSchedule);
            $("#btnRemoveSchedule").bind("click", btnRemoveSchedule);

            $("#gridItems").on("cellclick", function (event) {
                if (isRightClick(event.originalEvent)) return;
                // event arguments.
                var args = event.args;
                if (args.datafield == "day_name") {
                    var day_id = args.row.bounddata.day_id;
                    RowIndex = args.rowindex;

                    /*var id = args.row.bounddata.supcust_id;
                    RowIndex = args.rowindex;
                    $('#popItem').jqxWindow('open');
                    //supcust_id 需要跟后端定义字段一致
                    $("#popItem").jqxWindow('setContent', '<iframe src="ClientEdit?operKey=' + g_operKey + '&supcust_id=' + id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                    */
                    onGridRowEdit(args.rowindex);
                         
                }

            });
    	    $("#Cancel").on('click', function () {
    	        for (var i = 0; i < 10; i++) {
    	            $('#jqxgrid').jqxGrid('deleterow', i);
    	            $('#jqxgrid').jqxGrid('addrow', i, {})
    	        }
            });
            //if (!window.ForSelect) {

                $('#gridItems').jqxGrid('hidecolumn', 'sys_check')
            //}
      
            $("#popSchedule").jqxWindow({ isModal: true, modalOpacity:0.3, height: 350, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true,closeButtonSize:32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            $("#popAssignOper").jqxWindow({ isModal: true, modalOpacity:0.3, height: 200, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true,closeButtonSize:32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            attachContextMenu();

            $("#btnSave").on('click',function (e){
                QueryData();
            });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            $(document).on('click', function (e) {
                
                var contextMenu = e.target.innerText
                //var ee = $('#popSet').css("display")
                //if (contextMenu == "全部" && ee =="block") {
                //    bw.toast("请勿将客户片区设置为'全部'");
                //}
            });

            appendOperDiv()


        });

        

        
    </script>
</body>

</html>