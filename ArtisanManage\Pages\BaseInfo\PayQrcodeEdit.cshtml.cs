using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;
using ArtisanManage.Services;
using Newtonsoft.Json;
using HuaWeiObsController;
using System.Net.Http;
using ArtisanManage.Pages.Setting;
using System.Xml;
using NPOI.POIFS.Crypt.Dsig;

namespace ArtisanManage.Pages.BaseInfo
{
    public class PayQrCodeEditModel : PageFormModel
    {
        public string HWhref = HuaWeiObs.BucketLinkHref + "/";
        public PayQrCodeEditModel(CMySbCommand cmd, string company_id = "", string oper_id = "") : base(Services.MenuId.infoPayQrCode)
        {
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            this.cmd = cmd; 
            DataItems = new Dictionary<string, DataItem>()
            {
                {"qrcode_id",new DataItem(){Title="",FldArea="divHead", Hidden=true } },
                {"sub_code",new DataItem(){Title="账户编码", FldArea="divHead", SqlFld="s.sub_code", Disabled=true, SaveToDB=false}},
                {"sub_id",new DataItem(){Title="账户名称", FldArea="divHead", SqlFld="s.sub_id", LabelFld="sub_name", ButtonUsage="list", SqlForOptions ="select sub_id as v, sub_name as l, sub_code as c, coalesce(status,'1') as s from cw_subject where company_id =~COMPANY_ID and sub_type='QT'", DropDownWidth="150"}},
                {"mother_code",new DataItem(){Title="账户类型", FldArea="divHead", SqlFld="mother_id", LabelFld="mother_name", ButtonUsage="list",  SqlForOptions ="select sub_code as v, sub_name as l, sub_id as z from cw_subject where company_id =~COMPANY_ID and sub_code in (1001,1002) order by sub_code::text desc", Value="1002", Label="银行存款", DropDownWidth="100", DropDownHeight="100", Necessary=true, SaveToDB=false}},
                {"bank_name",new DataItem(){Title="开户银行", FldArea="divHead" }},
                {"bank_no",new DataItem(){Title="银行账号", FldArea="divHead", Width="390"}},
                {"pay_channel_id",new DataItem(){Title="支付通道",FldArea="divHead",LabelFld="channel_name",ButtonUsage="list", DropDownWidth="150", DropDownHeight="170", Necessary=false,
                    SqlForOptions ="select c.channel_id as v,c.channel_name as l from pay_merchant m left join pay_channel c on m.channel_id = c.channel_id"}},
                {"qrcode_uri",new DataItem(){ FldArea="divHead",Hidden=true}},
                {"order_index", new DataItem(){ Title="顺序号", SqlFld="s.order_index", FldArea="divHead", SaveToDB=false } },
                {"status",new DataItem(){Title="状态",LabelFld="cls_status_name",FldArea="divHead",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]", DropDownWidth="100", DropDownHeight="100", SaveToDB=false}},
                {"remark",   new DataItem(){ Title="备注", FldArea="divHead" }},
                {"shortcut", new DataItem(){ Title="快捷键", FldArea="divHead", Width="100", SaveToDB=true }}
            };

            m_idFld = "qrcode_id";
            m_tableName = "info_pay_qrcode";
            m_selectFromSQL = @"
                from info_pay_qrcode
                left join cw_subject s on info_pay_qrcode.company_id=s.company_id and info_pay_qrcode.sub_id=s.sub_id
                left join (select sub_id as mother_id1,sub_name as mother_name from cw_subject where company_id= ~COMPANY_ID ) sm on sm.mother_id1=s.mother_id
                left join pay_channel c on info_pay_qrcode.pay_channel_id=c.channel_id
                where info_pay_qrcode.company_id=~COMPANY_ID and info_pay_qrcode.qrcode_id='~ID' order by s.order_index";
        }


        public async Task OnGet()
        {
            await InitGet(cmd);
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class PayQrCodeEditController : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public PayQrCodeEditController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            PayQrCodeEditModel model = new PayQrCodeEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            PayQrCodeEditModel model = new PayQrCodeEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            Security.GetInfoFromOperKey(request.operKey.ToString(), out string companyID, out string operID);

            string qrcode_uri = request.qrcode_uri;
            if (!string.IsNullOrEmpty(qrcode_uri) && qrcode_uri.Contains("base64"))
            { 
                string dir = $"pay-code/company_{Token.CompanyID}/";
                Random rr = new Random();
                var id = $"{DateTime.Now:yyyy-MM-dd_HH-mm-ss-ffff}_{rr.Next(1000, 9999)}";
                string ite = $"qrcode_{id}.jpg";
                string path = dir + ite;
                string err=await HuaWeiObsController.HuaWeiObs.Save(_httpClientFactory,qrcode_uri, path);
                if(err!="") return Json(new { result = "Error", msg = err });
                request.qrcode_uri = path;
            }

            if (request.sub_name == "") return Json(new { result = "Error", msg = "请选择/输入账户科目" });
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select coalesce(setting ->>  'useAccounting','false') useaccounting from company_setting where company_id = {companyID}", cmd);
            if (setting == null || !Convert.ToBoolean(setting.useaccounting))
            {
                if (request.sub_name.ToString().Contains("-"))
                {
                    return Json(new { result="Error", msg= "名称不能包含“-”" });
                }
                if (request.sub_name.ToString().Contains(","))
                {
                    return Json(new { result = "Error", msg = "名称不能包含“,”" });
                }
            }

            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            if (request.qrcode_id == "") //新增
            {
                dynamic sub = await CDbDealer.Get1RecordFromSQLAsync($"select sub_id from cw_subject where company_id={companyID} and sub_name='{request.sub_name}'", cmd);
                if (sub == null)//输入科目
                {
                    dynamic sub_new = await CDbDealer.Get1RecordFromSQLAsync($"select substr(sub_code::text,1,4) as mother_code, max(sub_code)+1 as sub_code, level, mother_id, other_sub from cw_subject where company_id={companyID} and mother_id=(select sub_id from cw_subject where company_id={companyID} and sub_code={request.mother_code}) group by level, mother_id, other_sub, substr(sub_code::text,1,4)", cmd);
                    if ((sub_new.mother_code == "1001" && Convert.ToInt32(sub_new.sub_code) > 100199) || (sub_new.mother_code == "1002" && Convert.ToInt32(sub_new.sub_code) > 100299))
                    {
                        return Json(new { result = "Error", msg = $"账户编号超出最大值{(sub_new.mother_code == "1001" ? "100199" : "100299")}，请调整会计科目" });
                    }
                    cmd.CommandText = $@"insert into cw_subject (company_id, sub_code, sub_name, direction, sub_type, status, level, mother_id, other_sub, order_index) values ({companyID}, {sub_new.sub_code}, '{request.sub_name}', 1, 'QT', '{request.status}', {(sub_new.level==""?"null":sub_new.level)}, {sub_new.mother_id}, '{sub_new.other_sub}', {(request.order_index==""?"null":request.order_index)}) returning sub_id;";
                    string sub_id = (await cmd.ExecuteScalarAsync()).ToString();
                    request.sub_id= sub_id; 
                    if (setting != null && Convert.ToBoolean(setting.useaccounting))
                    {
                        cmd.CommandText = $@"insert into cw_op_sub_init_detail (company_id, sheet_id, sub_id) values ({companyID}, 1, {sub_id});
                            insert into cw_sub_balance select company_id, period, {sub_id} as sub_id from cw_sub_balance where company_id={companyID} and sub_id={sub_new.mother_id};";
                        await cmd.ExecuteNonQueryAsync();
                    }
                }
                else//选择科目
                {
                    if (request.sub_id == "")
                    {
                        return Json(new { result = "Error", msg = "已存在同名科目，该科目无法设为账户" });
                    }
                    dynamic qrcode = await CDbDealer.Get1RecordFromSQLAsync($"select qrcode_id from info_pay_qrcode where company_id = {companyID} and sub_id={request.sub_id}", cmd);
                    if (qrcode != null)
                    {
                        return Json(new { result = "Error", msg = "已存在同名账户" });
                    }
                    cmd.CommandText = $"update cw_subject set status={(request.status==""?"null":$"'{request.status}'")}, order_index={(request.order_index==""?"null": request.order_index)} where company_id={companyID} and sub_id={request.sub_id};";
                    await cmd.ExecuteNonQueryAsync();
                }
            }
            else//修改
            {
                cmd.CommandText = $"update cw_subject set status={(request.status == "" ? "null" : $"'{request.status}'")}, order_index={(request.order_index == "" ? "null" : request.order_index)} where company_id={companyID} and sub_id={request.sub_id};";
                await cmd.ExecuteScalarAsync();
            }

            PayQrCodeEditModel model = new PayQrCodeEditModel(cmd);
            JsonResult jr = await model.SaveTable(cmd, request, tran);
            dynamic jrd = jr.Value;
            if (jrd.result == "OK" && jrd.msg == "")
            {
                tran.Commit();
                await CwLog.Save(companyID, operID, null, "PayQrCodeEdit", $"OK; save/update sub as save cashbank account; qrcode_id: {jrd.record.qrcode_id}, sub_id: {jrd.record.sub_id}, sub_name: '{request.sub_name}';", cmd);
            }
            else
            { 
                tran.Rollback();
            }
            return jr;
        }
    }
}