﻿using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowCashBankTransfer : SheetRowBase
    {
        [SaveToDB][FromFld] public string money_out_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string money_out_name { get; set; }
        [SaveToDB][FromFld] public string money_in_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string money_in_name { get; set; }
        [SaveToDB][FromFld] public string fee_id { get; set; }
        [SaveToDB][FromFld] public decimal in_amount { get; set; }
        [SaveToDB][FromFld] public decimal fee_amount { get; set; } = 0;
        [SaveToDB][FromFld] public decimal out_amount_withfee { get; set; }

        //[SaveToDB][FromFld] public decimal out_amount_withoutfee { get; set; } = 0;
    }

    public class SheetCashBankTransfer : SheetBase<SheetRowCashBankTransfer>
    {
        [SaveToDB][FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB][FromFld] public string seller_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; }

        public SheetCashBankTransfer(LOAD_PURPOSE loadPurpose) : base("sheet_cashbank_transfer_main", "sheet_cashbank_transfer_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_CASH_BANK_TRANSFER;
            ConstructFun();
        }
        public SheetCashBankTransfer() : base("sheet_cashbank_transfer_main", "sheet_cashbank_transfer_detail", LOAD_PURPOSE.SHOW)
        {
            ConstructFun();
            sheet_type = SHEET_TYPE.SHEET_CASH_BANK_TRANSFER;
        }
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.seller_id + "_" + this.make_brief;
            foreach (var row in SheetRows)
            {
                res += row.in_amount + "_" + row.money_in_id + "_" + row.money_out_id + "_" + row.fee_amount;
            }
            return res;
        }
        
        private void ConstructFun()
        {
            MainLeftJoin = @"left join (select oper_id,oper_name as seller_name from info_operator where company_id=~COMPANY_ID) seller on t.seller_id=seller.oper_id
                                  left join (select oper_id,oper_name as maker_name from info_operator where company_id=~COMPANY_ID) maker on t.maker_id=maker.oper_id
                                  left join  (select oper_id,oper_name as approver_name from info_operator where company_id=~COMPANY_ID) approver on t.approver_id=approver.oper_id  ";
            DetailLeftJoin = @"left join (select sub_id,sub_name as money_out_name from cw_subject where company_id=~COMPANY_ID) pw1 on t.money_out_id=pw1.sub_id
                                  left join (select sub_id,sub_name as money_in_name from cw_subject where company_id=~COMPANY_ID) pw2 on t.money_in_id=pw2.sub_id ";
        }
 
        protected class Subject
        {
            public string sub_id { get; set; }
            public string sub_name { get; set; }
            public string sub_type { get; set; }
        }

        class CInfoForApprove : CInfoForApproveBase
        {
            public List<Subject> PaywaysInfo = new List<Subject>();
        }

        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            string sql = "";
            HashSet<string> sub_ids = new HashSet<string>();
            foreach (SheetRowCashBankTransfer row in SheetRows)
            {
                sub_ids.Add(row.money_out_id);
                sub_ids.Add(row.money_in_id);
            }
            if (sub_ids.Count != 0)
            {
                sql = $"select sub_id, sub_name, sub_type from cw_subject where company_id={company_id} and sub_id in ({String.Join(',',sub_ids)});";
                QQ.Enqueue("payway_type", sql);
            }

            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (sqlName == "payway_type")
            {
                info.PaywaysInfo = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
        }

        protected override async Task<string> CheckSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
            
            return "OK";
        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            await base.OnSheetIDGot(cmd, sheetID, info1);
            CInfoForApprove info = (CInfoForApprove)info1;

            #region 更新现金银行余额
            string sql = "";
            if (info1.BizStartPeriod != "" && info.PaywaysInfo != null)
            {
                if (red_flag == "2") money_inout_flag = -1;
                Dictionary<string, decimal> pws = new Dictionary<string, decimal>();
                foreach(SheetRowCashBankTransfer row in SheetRows)
                {
                    Subject pw_out = info.PaywaysInfo.Find(p => p.sub_id == row.money_out_id && p.sub_type == "QT");
                    if (pw_out != null && row.out_amount_withfee != 0)
                    {
                        if(!pws.ContainsKey(row.money_out_id)) pws.Add(row.money_out_id, -row.out_amount_withfee);
                        else pws[row.money_out_id] -= row.out_amount_withfee;
                    }
                    Subject pw_in = info.PaywaysInfo.Find(p => p.sub_id == row.money_in_id && p.sub_type == "QT");
                    if (pw_in != null && row.in_amount != 0)
                    {
                        if (!pws.ContainsKey(row.money_in_id)) pws.Add(row.money_in_id, row.in_amount);
                        else pws[row.money_in_id] += row.in_amount;
                    }
                }
                if (pws.Count > 0)
                {
                    sql = base.UpdateCashBankBalance(pws);
                }
            }
            #endregion

            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
        }
    }

    
}
