﻿using ArtisanManage.Models;
using myJXC;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.MyCW;
using Microsoft.AspNetCore.Mvc;
using ArtisanManage.Services;
using Org.BouncyCastle.Utilities;
using ArtisanManage.Pages.CwPages;
using NPOI.POIFS.Crypt.Dsig;
using System.ComponentModel.Design;
using ArtisanManage.Pages.BaseInfo;

namespace ArtisanManage.MyJXC
{

    public class SheetRowFeeOut : SheetRowBase
    {

        [SaveToDB] [FromFld] public string fee_sub_id { get; set; }
        //[SaveToDB] [FromFld] public string supcust_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string fee_sub_name { get; set; }
        [SaveToDB] [FromFld] public decimal fee_sub_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_disc_amount { get; set; }
        [SaveToDB] [FromFld] public string display_id { get; set; } = "";
        [SaveToDB] [FromFld] public string display_month { get; set; } = "";
        

    }
    public enum SHEET_FEE_OUT
    {
        EMPTY,
        IS_OUT,
        NOT_OUT

    }

    public class IncomeSheetInfo
    {
        public string supplier_id { get; set; } = "";
        public string supplier_name { get; set; } = "";
        public string income_sub_id { get; set; } = "";
        public string income_sub_name { get; set; } = "";
        public string yf_payway_id { get; set; } = "";
        public string yf_sub_name { get; set; } = "";
        public bool is_arrear { get; set; } = false;
        public string income_sheet_id { get; set; } = "";
        public string income_sheet_no { get; set; } = "";
        public bool is_same_time { get; set; } = false;//false:事后代收；true:事中代收
    }

    public class SheetFeeOut: SheetBase<SheetRowFeeOut>
    {
        public string appendix_photos { get; set; } = "";
        public Dictionary<string, string> OtherSheetAttributes = null;
        public string buy_sheet_id { get; set; } = "";//暂时没用，返回界面的是中间单据：采购费用分摊单
        [FromFld("sheet_attribute->>'fee_apportion_sheet_id'", LOAD_PURPOSE.SHOW)] public string fee_appo_sheet_id { get; set; } = "";
        [FromFld("sheet_attribute->>'fee_apportion_sheet_no'", LOAD_PURPOSE.SHOW)] public string fee_appo_sheet_no { get; set; } = "";
        [FromFld("supcust_remark", LOAD_PURPOSE.SHOW)] public string sup_remark { get; set; } = "";
        [FromFld("sup_addr", LOAD_PURPOSE.SHOW)] public string sup_addr { get; set; } = "";
        [FromFld("mobile", LOAD_PURPOSE.SHOW)] public string mobile { get; set; } = "";
        [FromFld("boss_name", LOAD_PURPOSE.SHOW)] public string boss_name { get; set; } = "";
        public bool allowRedByApportion = false;

        public string related_sheet_id { get; set; } = "";
        public string related_sheet_no { get; set; } = "";
        public string related_sheet_type { get; set; } = "";

        public string incomeSheetInfo { get; set; } = "";//ZC   不需要fromfld，{ get; set; }必须有，才能返回前端
        public string pay_for_supplier_fee_sheet_id { get; set; } = "";//SR
        public string pay_for_supplier_fee_sheet_no { get; set; } = "";//SR
        public bool allowRedBySupplierFee = false;//SR
												  //public bool allowRedBySupplierFeeAfter { get; set; } = false;//SR 事后代扣

		[SaveToDB][FromFld] public override string is_imported { get; set; } = "";

		[SaveToDB]
        [FromFld]
        public virtual string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                if (appendix_photos != "")
                {
                    // 附件添加
                    sheetAttribute.Add("appendixPhotos", appendix_photos);
                }
                if (related_sheet_id.IsValid())
                {
                    sheetAttribute.Add("related_sheet_id", related_sheet_id);
                    sheetAttribute.Add("related_sheet_no", related_sheet_no);
                }
                if (OtherSheetAttributes != null)
                {
                    foreach (var k in OtherSheetAttributes)
                    {
                        if (!sheetAttribute.ContainsKey(k.Key))
                            sheetAttribute.Add(k.Key, k.Value);
                    }
                }
                if (buy_sheet_id != "")
                {
                    sheetAttribute.Add("buy_sheet_id", buy_sheet_id);
                }
                if (incomeSheetInfo != "")//保存至费用支出单
                {
                    sheetAttribute.Add("incomeSheetInfo", incomeSheetInfo);
                }
                if (pay_for_supplier_fee_sheet_id != "")//保存在其他收入单
                {
                    sheetAttribute.Add("pay_for_supplier_fee_sheet_id", pay_for_supplier_fee_sheet_id);
                }
                if (pay_for_supplier_fee_sheet_no != "")//保存在其他收入单
                {
                    sheetAttribute.Add("pay_for_supplier_fee_sheet_no", pay_for_supplier_fee_sheet_no);
                }
                //if (allowRedBySupplierFeeAfter == true)//事后代扣，以后再做
                //{
                //    sheetAttribute.Add("allowRedBySupplierFeeAfter", allowRedBySupplierFeeAfter.ToString().ToLower());
                //}

                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set
            {

                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.appendixPhotos != null)
                    {
                        this.appendix_photos = sheetAttr.appendixPhotos;
                    }
                    if(sheetAttr.related_sheet_id  != null)
                    {
                        related_sheet_id = sheetAttr.related_sheet_id;
                        related_sheet_no = sheetAttr. related_sheet_no;
                        related_sheet_type = sheetAttr. related_sheet_type;
                    }
                    
                    if (sheetAttr.buy_sheet_id!=null)
                    {
                        this.buy_sheet_id=sheetAttr.buy_sheet_id.ToString();
                    }
                    if (sheetAttr.incomeSheetInfo != null)//取出至费用支出单
                    {
                        incomeSheetInfo = sheetAttr.incomeSheetInfo.ToString();
                    }
                    if (sheetAttr.pay_for_supplier_fee_sheet_id != null)//取出至其他收入单
                    {
                        pay_for_supplier_fee_sheet_id = sheetAttr.pay_for_supplier_fee_sheet_id;
                    }
                    if (sheetAttr.pay_for_supplier_fee_sheet_no != null)//取出至其他收入单
                    {
                        pay_for_supplier_fee_sheet_no = sheetAttr.pay_for_supplier_fee_sheet_no;
                    }
                    //if (sheetAttr.allowRedBySupplierFeeAfter != null)
                    //{
                    //    allowRedBySupplierFeeAfter = Convert.ToBoolean(sheetAttr.allowRedBySupplierFeeAfter);
                    //}
                }
                
            }
        }

        //public List<SheetBuyRow> SheetRows = new List<SheetBuyRow>();
        //public new List<SheetRowBase> SheetRows = new List<SheetRowBase>();
        // public string red_sheet_id = "";

        [SaveToDB][FromFld] public string order_source { get; set; } = "";
        [SaveToDB] [FromFld] public string visit_id { get; set; } = "";

        [SaveToDB] [FromFld] public override SHEET_TYPE sheet_type { get; set; }

        [SaveToDB] [FromFld] public string supcust_id { get; set; } = "";
        [SaveToDB][FromFld] public string acct_supcust_id { get; set; } = ""; 

        [FromFld("sheet_attribute->>'acctCustID' as acct_cust_id")] public string acct_cust_id { get; set; } = "";
        [SaveToDB] public string shop_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_name { get; set; } = "";
        //[FromFld(LOAD_PURPOSE.SHOW)] public string shop_name { get; set; } = "";
        [SaveToDB] [FromFld] public override int money_inout_flag { get; set; }
        [SaveToDB] [FromFld] public override decimal total_amount { get; set; } 

        [SaveToDB] [FromFld] public string payway1_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_type { get; set; } = "";
        [SaveToDB] [FromFld] public decimal payway1_amount { get; set; }
        [SaveToDB] [FromFld] public string payway2_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_type { get; set; } = "";
        [SaveToDB] [FromFld] public decimal payway2_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB][FromFld] public decimal paid_amount { get; set; }
        [SaveToDB][FromFld] public decimal disc_amount { get; set; }
        [SaveToDB][FromFld] public decimal now_disc_amount { get; set; }
        [SaveToDB(false)][FromFld(false)] public decimal prepay_amount { get; set; }

        public decimal left_amount { get { return total_amount - now_pay_amount - now_disc_amount; } }

        public decimal LeftAmount { get { return CPubVars.ToDecimal(CPubVars.FormatMoney(total_amount - paid_amount - disc_amount, 2)); } }
        [SaveToDB] [FromFld] public string getter_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string getter_name { get; set; } = "";

        public string red_from { get; set; } = "";


        //public SheetFeeOut() : base("sheet_fee_out", "", LOAD_PURPOSE.SHOW_OR_APPROVE)
        //{

        //}
        
        public SheetFeeOut(SHEET_FEE_OUT sheetFeeOut,LOAD_PURPOSE loadPurpose) : base("sheet_fee_out_main", "sheet_fee_out_detail",loadPurpose)
        {

            sheet_type = sheetFeeOut == SHEET_FEE_OUT.IS_OUT ? SHEET_TYPE.SHEET_FEE_OUT:SHEET_TYPE.SHEET_FEE_IN;

            if (loadPurpose == LOAD_PURPOSE.SHOW)
            {
                ConstructFun();
            }
        }
        private void ConstructFun()
        {
            MainLeftJoin = @" left join info_supcust c on t.supcust_id=c.supcust_id
                                  left join (select oper_id,oper_name as getter_name from info_operator where company_id=~COMPANY_ID) getter on t.getter_id=getter.oper_id
                                  left join (select oper_id,oper_name as maker_name from info_operator where company_id=~COMPANY_ID) maker on t.maker_id=maker.oper_id
                                  left join  (select oper_id,oper_name as approver_name from info_operator where company_id=~COMPANY_ID) approver on t.approver_id=approver.oper_id
                                  left join (select sub_id,sub_name as payway1_name,sub_type payway1_type from cw_subject where company_id=~COMPANY_ID) pw1 on t.payway1_id=pw1.sub_id
                                  left join (select sub_id,sub_name as payway2_name,sub_type payway2_type from cw_subject where company_id=~COMPANY_ID) pw2 on t.payway2_id=pw2.sub_id
              ";
            DetailLeftJoin = "left join (select sub_id,sub_name as fee_sub_name from cw_subject where company_id=~COMPANY_ID) cs on cs.sub_id = t.fee_sub_id ";
        }
        public SheetFeeOut(LOAD_PURPOSE loadPurpose) : base("sheet_fee_out_main", "sheet_fee_out_detail", loadPurpose)
        {
            ConstructFun();
        }
        public SheetFeeOut() : base("sheet_fee_out_main", "sheet_fee_out_detail", LOAD_PURPOSE.SHOW)
        {
            ConstructFun();
        }

        protected override void InitForSave()
        {
            base.InitForSave();
            if (getter_id == "") getter_id = OperID;
            if (approver_id == "") approver_id = OperID;
            paid_amount = now_pay_amount;
            disc_amount = now_disc_amount;

        }

        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            if (payway1_id == "") return "必须指定支付方式";
            if (getter_id == "" && IsFromWeb) return "必须指定业务员";

            if (Math.Abs(now_pay_amount - (payway1_amount + payway2_amount)) > 0.05m)
            {
                return "支付方式合计与总支付金额不一致";
            }

            if (Math.Abs(total_amount - now_pay_amount - left_amount - disc_amount) > 0.01m) return "支付金额与欠款金额的合计与总额不相等";
			if (SheetRows.Count == 0)
			{
                return "必须指定明细行";
            }
            decimal total_fee_sub_amount = 0;
            int rowIndex = 0;
            foreach (var row in SheetRows)
            {
                rowIndex++;

                if (!row.fee_sub_id.IsValid())
				{
                    return $"第{rowIndex}行没指定科目";
                }
                if (row.fee_sub_amount==0)
                {
                    return $"第{rowIndex}行费用金额不能为0";
                }
                total_fee_sub_amount += row.fee_sub_amount;
            }
            if (Math.Abs(total_fee_sub_amount - total_amount) >= 0.1m) return "明细行合计与总额不等，请检查";
            
            return "OK";
        }
        protected override async Task<string> CheckForRed(CMySbCommand cmd)
        {
            if (SheetType == "ZC")
            {
                // 检查欠条
                string billSheetSql = @$"select sheet_no from sheet_move_arrears_bill_main m left join
                    sheet_move_arrears_bill_detail d on d.company_id = m.company_id and d.sheet_id = m.sheet_id
                    where m.company_id = {company_id} and business_sheet_id = {sheet_id} and business_sheet_type = '{SheetType}'and approve_time is not null and red_flag is null
                    order by m.sheet_id desc nulls last";
                dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(billSheetSql,cmd);
                if (ret != null)
                {
                    return $"请红冲包含关联欠条的单据【{ret.sheet_no}】";
                }
            }
            if (buy_sheet_id != "" && !allowRedByApportion) return "请红冲关联采购费用分摊单";
            if (pay_for_supplier_fee_sheet_id != "" && SheetType == "SR" && !allowRedBySupplierFee) return "请红冲关联费用支出单";
            return await CheckForRed_MoneySheet(cmd);
        }
        class CInfoForApprove : CInfoForApproveBase
        {
            public List<Subject> PrepaySubjects = new List<Subject>();
            public List<Subject> FeeOutSubjects = null;
            public List<Subject> PaywaysInfo = new List<Subject>();
        }

        protected class Subject
        {
            public string sub_id { get; set; }
            public string balance { get; set; } = "";
            public string sub_name { get; set; }
            public string sub_type { get; set; }
        }
        protected override void NeedUpdateClientHistory(out string supcustID, out bool updateArrears, out string updatePrepaySubIDs)
        {
            supcustID = supcust_id;
            updateArrears = true;
            updatePrepaySubIDs = "";
            if (payway1_type == "YS" || payway1_type == "YF")
            {
                if (updatePrepaySubIDs != "") updatePrepaySubIDs += ',';
                updatePrepaySubIDs += payway1_id;
            }
            if (payway2_type == "YS" || payway2_type == "YF")
            {
                if (updatePrepaySubIDs != "") updatePrepaySubIDs += ',';
                updatePrepaySubIDs += payway2_id;
            }

        }

        protected override void GetInfoForSave_SetQQ(SQLQueue QQ)
        {
			if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
			CInfoForApprove info = (CInfoForApprove)InfoForApprove;
			string sql;
            base.GetInfoForSave_SetQQ(QQ);

            if (this.supcust_id != "" && this.supcust_id != "-1")
            {
                if (this.sheet_type==SHEET_TYPE.SHEET_FEE_IN)
                {
                    this.acct_supcust_id = this.supcust_id;
                }
                else
                {
                    sql = $@"select acct_cust_id from info_supcust where company_id={this.company_id} and supcust_id={this.supcust_id};";
                    QQ.Enqueue("supcust", sql);
                }
            }
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);

            if (sqlName == "supcust")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec != null)
                {
                    if (rec.acct_cust_id != "")
                    {
                        this.acct_supcust_id = rec.acct_cust_id;
                    }
                    else
                    {
                        this.acct_supcust_id = this.supcust_id;
                    }
                    /*if (this.acct_supcust_id != this.supcust_id && this.acct_supcust_id != rec.acct_cust_id)
                    {
                        info.ErrMsg = "该客户结算单位改变了,请重新选择客户";
                    }*/
                }
            }

        }



        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
			if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
			CInfoForApprove info = (CInfoForApprove)InfoForApprove;

			base.GetInfoForApprove_SetQQ(QQ);
            string sql;
            string sub_ids = payway1_id;
            if (payway2_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway2_id;
            }
            if (sub_ids != "")
            {
                sql = $"select sub_id,sub_name from cw_subject where company_id={company_id} and sub_id in (" + sub_ids + ") and sub_type in ('ZC','QTSR');";
                QQ.Enqueue("fee_sub", sql);
                sql = $"select sub_id, sub_name, sub_type from cw_subject where company_id={company_id} and sub_id in ({sub_ids});";
                QQ.Enqueue("payway_type", sql);
                if (supcust_id.IsValid())
                {
                    string custID = this.acct_supcust_id;
                    // 临时改动
                    if (custID.IsInvalid()) custID = this.supcust_id;
                    //if (acct_cust_id != "") custID = acct_cust_id;

                    sql = $"select s.sub_id,sub_name,balance,sub_type,s.is_order from cw_subject s left join (select sub_id,balance from prepay_balance where company_id={company_id} and supcust_id={custID}) b on s.sub_id=b.sub_id  where sub_type in ('YS','YF') and s.company_id ={company_id};";
                    QQ.Enqueue("prepay_sub", sql);
                }
            }
            if (Math.Abs( total_amount - paid_amount)  > 0.01m && supcust_id !="")
            {
                sql = GetSqlForArrearsQQ(supcust_id, getter_id);
                QQ.Enqueue("arrear_balance", sql);
            }

            if (sheet_id != "" && red_flag =="2")
            {
                sql = $@"SELECT sheet_attribute->>'related_sheet_id' related_sheet_id FROM sheet_fee_out_main where company_id = {company_id} and sheet_attribute is not null and sheet_id ={sheet_id}; ";
                QQ.Enqueue("bc_sheet_id", sql);
            }

            string displaySheetIDs = "";
            foreach(var row in SheetRows)
            {
                if(row.display_id!="")
                {
                    if (displaySheetIDs != "") displaySheetIDs += ",";
                    displaySheetIDs += row.display_id;
                }
            }
            if (displaySheetIDs != "") sql = $"select from display_agreement_detail where company_id={company_id} and sheet_id in ({displaySheetIDs}) and items_id = 'money' and all_given is not true";
        }
     
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);

            if (sqlName == "fee_sub")
            {
                info.FeeOutSubjects = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
            else if (sqlName == "arrear_balance")
            {
                DealArrearReadQQ(dr, info, left_amount, supcust_id);                
            }
            else if (sqlName == "payway_type")
            {
                info.PaywaysInfo = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
            else if(sqlName == "bc_sheet_id")
            {
                dynamic data = CDbDealer.Get1RecordFromDr(dr, false);
                if(data != null)
                {
                    string bc_sheet_id = data.related_sheet_id;
                    if (bc_sheet_id.IsValid() && red_from != "BC" && red_from != "BT")
                    {
                        if (sheet_type == SHEET_TYPE.SHEET_FEE_IN)
                        {
                            info.ErrMsg = "补贴生成的其它收入单无法直接红冲";
                        }
                        else
                        info.ErrMsg = "补差单生成的费用支出单无法直接红冲";
                    }
                }
            } 
            else if (sqlName == "prepay_sub")
            {
                info.PrepaySubjects = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
        }
 
        public async Task ProcessPcAppendix()
        {
			string images = appendix_photos;
			if (!string.IsNullOrEmpty(images) && images != "[]" && images.Contains("photos"))
			{
				// 使用 Newtonsoft.Json 解析 JSON 字符串
				var jsonObject = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(images);

				// 提取 photos 列表
				List<string> photos = jsonObject["photos"];
				appendix_photos = await ProcessAppendixPicsRetDBStr(photos);
			}
		}
        public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, company_id);
            return result;
        }
        
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            await base.OnSheetIDGot(cmd, sheetID, info1);

            #region 陈列协议兑付
            List<string> dispSubIDList = new List<string>();
            Dictionary<string, List<dynamic>> displaySub = new Dictionary<string, List<dynamic>>();
            foreach(var disp in SheetRows)
            {
                
                if (disp.display_id != "")
                {
                    dispSubIDList.Add(disp.fee_sub_id);
                    if(!displaySub.ContainsKey(disp.display_id))
                    {
                        displaySub.Add(disp.display_id, new List<dynamic>()); 
                    }
                    dynamic displayIdList = displaySub[disp.display_id];
                    displayIdList.Add(new {fee_sub_amount = disp.fee_sub_amount, display_month = disp.display_month});
                }
            }
            string dispSubIDs = string.Join(",", dispSubIDList);
            dispSubIDs = string.Join(",", dispSubIDs.Split(',').Distinct().ToArray());
            if (dispSubIDs != "")
            {
                string months = "";
                string monthCondi = "";
                string redCondi = " and red_flag is null  ";
                string allGivenCondi = " and all_given is not true";
                for (int i = 1; i <= 12; i++)
                {
                    months += $",coalesce(month{i}_qty,0) as month{i}_qty,coalesce(month{i}_given,0) as month{i}_given";
                    if (monthCondi != "") monthCondi += " or ";
                    monthCondi += $" coalesce(month{i}_qty,0) - coalesce(month{i}_given,0) >0.01 ";
                }
                monthCondi = "(" + monthCondi + ")";
                if (money_inout_flag == 1)
                {
                    allGivenCondi = " ";
                    redCondi = " ";
                    monthCondi = monthCondi.Replace("0.01", "-0.01");
                }

                string sqlQuery = @$"select 
       d.sheet_id,
       d.month1_given,d.month1_qty,
       d.month2_given,d.month2_qty,
       d.month3_given,d.month3_qty,
       d.month4_given,d.month4_qty,
       d.month5_given,d.month5_qty,
       d.month6_given,d.month6_qty,
       d.month7_given,d.month7_qty,
       d.month8_given,d.month8_qty,
       d.month9_given,d.month9_qty,
       d.month10_given,d.month10_qty,
       d.month11_given,d.month11_qty,
       d.month12_given,d.month12_qty,
       m.start_time,
       m.end_time,
       idt.disp_template_name,
       m.sheet_no,
       cw.sub_name
       {months} 
from display_agreement_detail d 
    left join display_agreement_main m on m.sheet_id = d.sheet_id   
    left join info_display_template idt on idt.company_id = {company_id} AND idt.disp_template_id = m.disp_template_id 
    left join cw_subject cw on cw.company_id = {company_id} AND cw.sub_id = m.fee_sub_id
where d.company_id = {company_id} 
  {redCondi}
  and approve_time is not null 
  and supcust_id = {supcust_id} 
  and items_id='money' 
  and m.fee_sub_id in ({dispSubIDs}) 
  and {monthCondi} {allGivenCondi};";

                List<ExpandoObject> lstFlow = await CDbDealer.GetRecordsFromSQLAsync(sqlQuery, cmd);

                foreach(var displaySubItem in displaySub)
                {
                    var dSheetID = displaySubItem.Key;
                    List<dynamic> displayMonthAndSubAmountArr = displaySubItem.Value;
                    string startTime = null;
                    DateTime startDateTime = DateTime.Now;
                    dynamic flowResult = null;
                    foreach (dynamic flow in lstFlow)
                    {
                        var dispSheetID = flow.sheet_id;
                        if (dispSheetID == dSheetID)
                        {
                            flowResult = flow;
                        }
                    }
                    startTime = flowResult.start_time;
                    startDateTime = DateTime.Parse(startTime);
                    string flowFields = "";
                    decimal sheetAllQty = 0;
                    decimal sheetAllGiven = 0;
                    decimal currentChangeGiven = 0;
                    // 判断数量是否有超兑
                    JObject flowResultJObject = JObject.FromObject(flowResult);
                    foreach (var displayMonthAndSubAmount in displayMonthAndSubAmountArr)
                    {
                        decimal sub_amount = displayMonthAndSubAmount.fee_sub_amount;
                        string displayMonth = displayMonthAndSubAmount.display_month;
                        DateTime displayMonthDateTime = DateTime.Parse(displayMonth);
                        if (sub_amount < 0)
                        {
                            info1.ErrMsg = "陈列协议金额不能为负";
                            return;
                        }
                        // money_inout_flag -1 是提交  1是红冲
                        sub_amount *= (money_inout_flag * -1); // sub_amount负数就是减少了兑付
                        currentChangeGiven += sub_amount;
                        int changeGivenIndex = ((displayMonthDateTime.Year - startDateTime.Year) * 12) + displayMonthDateTime.Month - startDateTime.Month + 1;
                        string fldName = $"month{changeGivenIndex}_given";
                        string fldNameQty = $"month{changeGivenIndex}_qty";
                        // 判断数量是否有超兑
                        string fldNameResult = flowResultJObject.GetValue(fldName).ToString();
                        string fldNameQtyResult = flowResultJObject.GetValue(fldNameQty).ToString();
                        int givenNum = 0;
                        int QtyNum = 0;
                        if (!fldNameResult.Equals(""))
                        {
                            givenNum =  Convert.ToInt32(flowResultJObject[fldName]) ;
                        }
                        if (!fldNameQtyResult.Equals(""))
                        {
                            QtyNum =  Convert.ToInt32(flowResultJObject[fldNameQty]) ;
                        }
                       
                       

                        if (money_inout_flag == -1)
                        {
                            if (sub_amount > QtyNum - givenNum)
                            {
                                string dispTemplateName = (string)flowResultJObject["disp_template_name"];
                                string sheet_no = (string)flowResultJObject["sheet_no"];
                                string sub_name = (string)flowResultJObject["sub_name"];
                                info1.ErrMsg = @$"陈列协议{sheet_no}金额超额兑付,{sub_name}, {dispTemplateName}";
                                return;
                            }
                        }
                        if (!flowFields.Equals(""))
                        {
                            flowFields += ",";
                        }
                        flowFields += $"{fldName} = coalesce({fldName},0) + ({sub_amount})";
                    }

                    for (int m = 1; m <= 12; m++)
                    {
                       string sQty = flowResultJObject[$"month{m}_qty"].ToString();
                       decimal qty = sQty.Equals("") ? 0 : CPubVars.ToDecimal(sQty);
                       string sGiven = flowResultJObject[$"month{m}_given"].ToString();
                       decimal given = sGiven.Equals("") ? 0 : CPubVars.ToDecimal(sGiven);
                       sheetAllQty += qty;
                       sheetAllGiven += given;
                    }
                    if (flowFields != "") 
                    {
                         string allGiven = "true";
                         if (sheetAllQty - sheetAllGiven - currentChangeGiven > 0.01m) allGiven = "false";
                         flowFields += $",all_given={allGiven}";
                         // 判断是否兑付完
                         AddExecSQL($"update display_agreement_detail set {flowFields} where company_id={company_id} and sheet_id = {dSheetID} and items_id = 'money';");
                         
                         //若兑付完就判断该陈列协议是否全部兑付完
                         if (allGiven == "true")
                         {
                             string isSettledSQL =
                                 $"select items_id,all_given from display_agreement_detail where company_id={company_id} and sheet_id = {dSheetID}";
                             List<ExpandoObject> allGivens = await CDbDealer.GetRecordsFromSQLAsync(isSettledSQL, cmd);
                             Boolean settledFlag = true;
                             foreach (dynamic allGivenJudge in allGivens)
                             {
                                 if (allGivenJudge.all_given.ToLower() != "true"&&allGivenJudge.items_id!="money")
                                 {
                                     settledFlag = false;
                                 }
                             }
                             if (settledFlag)
                             {
                                 string settleTime = CPubVars.GetDateText(DateTime.Now);
                                 AddExecSQL($"update display_agreement_main set settle_time='{settleTime}' where company_id={company_id} and sheet_id={dSheetID} and settle_time is null;");
                             }
                         }
                    }
                }
            }
            #endregion
            CInfoForApprove info = (CInfoForApprove)info1;
            string sRedFlag = "null";
            if (red_flag.IsValid()) sRedFlag = "'" + red_flag + "'";
            int flag = money_inout_flag;
            string sql = "";
            if (Math.Abs(left_amount) > 0.01m && supcust_id !="")
            {
               sql = await GetSqlForArrearsChange(cmd,info, left_amount, getter_id);
               AddExecSQL(sql);
            }

            #region 更新现金银行余额
            string sql_cb = "";
            if (info1.BizStartPeriod != "" && info.PaywaysInfo != null && !IsImported)
            {
                Dictionary<string, decimal> pws = new Dictionary<string, decimal>();
                Subject pw1 = info.PaywaysInfo.Find(p => p.sub_id == payway1_id && p.sub_type == "QT");
                if (pw1 != null && payway1_amount != 0)
                {
                    if (!pws.ContainsKey(payway1_id)) pws.Add(payway1_id, payway1_amount);
                    else pws[payway1_id] += payway1_amount;
                }
                Subject pw2 = info.PaywaysInfo.Find(p => p.sub_id == payway2_id && p.sub_type == "QT");
                if (pw2 != null && payway2_amount != 0)
                {
                    if (!pws.ContainsKey(payway2_id)) pws.Add(payway2_id, payway2_amount);
                    else pws[payway2_id] += payway2_amount;
                }
                if (pws.Count() > 0)
                {
                    sql_cb = base.UpdateCashBankBalance(pws);
                    AddExecSQL(sql_cb);
                }
            }
            #endregion
            #region 存欠款单或删除欠款单（红冲）
            // 费用支出单这里存的keeper_id是getter_id
            if (this.supcust_id != "")
            {
                if (Math.Abs(this.left_amount) > 0 && !red_flag.IsValid())
                {
                    string arrears_status = "no";
                    if (Math.Abs(this.left_amount) < Math.Abs(this.total_amount)) arrears_status = "part";
                    string insertArrearBillSql = $@"insert into arrears_bill 
    (company_id,business_sheet_type,business_sheet_id,business_sheet_no,supcust_id,supcust_name,orig_amount,left_amount,keeper_id,out_company,arrears_status)
values ({company_id},'{this.SheetType}',{this.sheet_id},'{this.sheet_no}',{this.supcust_id},'{this.sup_name}',{this.total_amount * this.money_inout_flag},{this.left_amount * this.money_inout_flag},{this.getter_id},true,'{arrears_status}')
returning bill_id";
                    dynamic insertRet = await CDbDealer.Get1RecordFromSQLAsync(insertArrearBillSql, cmd);

                }
                else if (this.left_amount > 0 && red_flag.IsValid())
                {
                    string billSql = @$"delete from arrears_bill where company_id = {company_id} and business_sheet_type = '{this.SheetType}'
            and business_sheet_id = {this.sheet_id} and business_sheet_no = '{this.sheet_no}' and supcust_id = {this.supcust_id}";
                    cmd.CommandText = billSql;
                    await cmd.ExecuteNonQueryAsync();
                }
            }
            #endregion
            sql = "";
            string redSql = "";
            if (info.PrepaySubjects != null)
            {
                //string custID = supcust_id;
                string custID = acct_supcust_id;

                decimal prepayTotalBalance = 0m;
                foreach (var sub in info.PrepaySubjects)
                {
                    if (sub.balance.IsValid())
                    {
                        prepayTotalBalance += CPubVars.ToDecimal(sub.balance);
                    }
                }

                foreach (var sub in info.PrepaySubjects)
                {
                    decimal prepayAmt = 0;
                    if (payway1_id == sub.sub_id)
                    {
                        prepayAmt = payway1_amount;
                    }
                    else if (payway2_id == sub.sub_id)
                    {
                        prepayAmt = payway2_amount;
                    }
                    if (Math.Abs(prepayAmt) > 0.001m)
                    {
                        flag = -1;
                        if (red_flag == "2") flag *= -1;
                        decimal changeBal = flag * prepayAmt * (-1);
                        decimal subBalance = 0m;
                        if (!string.IsNullOrEmpty(sub.balance)) subBalance = decimal.Parse(sub.balance, System.Globalization.NumberStyles.Float);
                        decimal preSubBalance = subBalance;
                        subBalance += changeBal;
                        prepayTotalBalance += changeBal;
                        

                        if (changeBal != 0)
                        {
                            if (sub.balance.IsValid())
                            {
                                sql += $"update prepay_balance set balance=balance+({changeBal}) where company_id={company_id} and supcust_id={custID} and sub_id={sub.sub_id};";
                            }
                            else
                            {
                                sql += $"insert into prepay_balance (company_id,supcust_id,sub_id,balance) values ({company_id},{custID},{sub.sub_id},{changeBal});";
                            }

                            if (red_flag == "2")
                            {
                                redSql += @$"update client_account_history set red_flag = '1' where company_id = {company_id} and sheet_id = {red_sheet_id} and sheet_type = '{SheetType}' and sub_type = '{sub.sub_type}';";
                            }
                            GetAccountHistoryHappenTimePrepayBalance(info, supcust_id, subBalance, prepayTotalBalance, sub.sub_id, out string balance, out string totalBalance);
                            sql += @$"
insert into client_account_history(company_id,   happen_time,                          approve_time,                          sheet_type  ,sheet_id ,change_amount  , now_balance  ,now_prepay_balance  ,now_balance_happen_time,now_prepay_balance_happen_time,supcust_id,sub_id      , sub_type       ,red_flag  ) 
                           values ({company_id},'{CPubVars.GetDateText(happen_time)}','{CPubVars.GetDateText(approve_time)}','{SheetType}',{sheetID},{changeBal}    , {subBalance} ,{prepayTotalBalance},{balance},{totalBalance},{custID}  ,{sub.sub_id},'{sub.sub_type}',{sRedFlag});";
                            if (!HappenNow)
                            {
                                sql += $"update client_account_history set now_balance_happen_time=now_balance_happen_time+{changeBal} where company_id={company_id} and supcust_id={custID} and sub_type='{sub.sub_type}' and sub_id={sub.sub_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                                sql += $"update client_account_history set now_prepay_balance_happen_time=now_prepay_balance_happen_time+{changeBal} where company_id={company_id} and supcust_id={custID} and sub_type='{sub.sub_type}' and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                            }
                                
                        }
                    }

                }
                if (sql != "") AddExecSQL(sql);
                if (redSql != "") AddExecSQL(redSql);
            }
            /*
            if (info.PaywaysInfo.Count>0)
            {
                foreach(var payway in info.PaywaysInfo)
                {
                    if (payway.sub_type == "YS")
                    {
                        int inoutFlag = 1;
                        if (red_flag == "2") inoutFlag = -1;
                        decimal prepayAmount = 0;
                        if (payway.sub_id == payway1_id) prepayAmount = payway1_amount;
                        else if(payway.sub_id ==payway2_id) prepayAmount = payway2_amount;

                    }
                }
            }*/
           // #endregion

            #region 代厂家支付生成收入单
            sql = "";
            if (incomeSheetInfo != "" && SheetType == "ZC")//费用支出单审核
            {
                IncomeSheetInfo incomeInfo = JsonConvert.DeserializeObject<IncomeSheetInfo>(incomeSheetInfo);
                if (red_flag == "")
                {
                    SheetFeeOut incomeSheet = new SheetFeeOut(SHEET_FEE_OUT.NOT_OUT, LOAD_PURPOSE.APPROVE);
                    incomeSheet.company_id = company_id;
                    incomeSheet.OperID = OperID;
                    incomeSheet.SheetType = "SR";
                    incomeSheet.money_inout_flag = 1;
                    incomeSheet.pay_for_supplier_fee_sheet_id = sheetID;
                    incomeSheet.pay_for_supplier_fee_sheet_no = sheet_no;
                    incomeSheet.happen_time = happen_time;
                    incomeSheet.supcust_id = incomeInfo.supplier_id;
                    if (incomeInfo.is_arrear)
                    {
                        incomeSheet.now_pay_amount = 0;
                        cmd.CommandText = $"select sub_id from cw_subject where company_id={company_id} and sub_type='QT' and coalesce(status,'1')='1' order by order_index limit 1";
                        object default_sub_id = await cmd.ExecuteNonQueryAsync();
                        if (default_sub_id == null)
                        {
                            info.ErrMsg = "请至少保留一个状态为正常的现金银行类科目";
                            return;
                        }
                        incomeSheet.payway1_id = default_sub_id.ToString();
                    }
                    else
                    {
                        incomeSheet.payway1_id = incomeInfo.yf_payway_id;
                        incomeSheet.payway1_amount = total_amount;
                        incomeSheet.now_pay_amount = total_amount;
                    }
                    incomeSheet.total_amount = total_amount;
                    incomeSheet.allowRedBySupplierFee = false;
                    SheetRowFeeOut row = new SheetRowFeeOut();
                    row.fee_sub_id = incomeInfo.income_sub_id;
                    row.fee_sub_amount = total_amount;
                    incomeSheet.SheetRows.Add(row);
                    info.ErrMsg = await incomeSheet.SaveAndApprove(cmd, false);
                    if (info.ErrMsg != "") return;

                    incomeInfo.income_sheet_id = incomeSheet.sheet_id;
                    incomeInfo.income_sheet_no = incomeSheet.sheet_no;
                    incomeInfo.is_same_time = true;
                    incomeSheetInfo = JsonConvert.SerializeObject(incomeInfo);
                    sql += $"update sheet_fee_out_main set sheet_attribute = jsonb_set(COALESCE(sheet_attribute, '{{}}'::jsonb), '{{incomeSheetInfo}}','{incomeSheetInfo}', true) where company_id = {company_id} and sheet_id={sheetID};";
                }
                else
                {
                    SheetFeeOut incomeSheet = new SheetFeeOut();
                    incomeSheet.allowRedBySupplierFee = true;
                    info.ErrMsg = await incomeSheet.Red(cmd, company_id, incomeInfo.income_sheet_id, OperID, "", false);
                    if (info.ErrMsg != "") return;
                }

            }

            if (pay_for_supplier_fee_sheet_id != "" && SheetType == "SR")
            {
                if (red_flag != "" && allowRedBySupplierFee)
                {
                    sql += $"update sheet_fee_out_main set sheet_attribute=sheet_attribute::jsonb - 'incomeSheetInfo' where company_id={company_id} and sheet_id={sheetID};";
                }
            }

            if (sql != "") AddExecSQL(sql);
            #endregion

            cmd.CommandText = GetExecSQL();
            if (cmd.CommandText != "")
            {
                await cmd.ExecuteNonQueryAsync();
            }
            
        }
        public async Task<string> SyncFeeOut(CMySbCommand cmd, dynamic oriSheet, string sheetType)
        {
            string err = "";
            SheetFeeOut sheet = JsonConvert.DeserializeObject<SheetFeeOut>(JsonConvert.SerializeObject(oriSheet));
            sheet.Init();
            string operKey = sheet.OperKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            sheet.OperID = "1";
            sheet.SYNCHRONIZE_SHEETS = true;
            sheet.order_source = "2fx";
            sheet.make_brief = "分销自动同步单据";
            sheet.money_inout_flag = sheet.money_inout_flag * -1;
            if (SheetType == "SR")
            {
                string resellerInfoSql = @$"
select * from rs_seller  rss  
left join (select plan_id,client_mapper from rs_plan) rsp  
on rss.plan_id = rsp.plan_id where reseller_company_id={companyID} and supplier_id = {oriSheet.supcust_id}";
                dynamic resellerInfo = await CDbDealer.Get1RecordFromSQLAsync(resellerInfoSql, cmd);

                string sellerId = sheet.getter_id;
                sheet.company_id = (string)resellerInfo.company_id;

                if ((string) resellerInfo.client_mapper == "sellerAsClient")
                {
                    string sellerClientSql = $"select rs_client_id from info_operator where oper_id = {sellerId}";
                    dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sellerClientSql, cmd);
                    // 第一种保护，业务员不在父账户客户列表，不允许开单
                    // if(record.rs_client_id == null || record.rs_client_id == "")
                    // {
                    //     err = "当前业务员不在父账户客户列表";
                    // }

                    if (record.rs_client_id == null || record.rs_client_id == "")
                    {
                        // 第二种，开到子公司客户上
                        sheet.supcust_id = (string)resellerInfo.client_id;
                    }
                    else
                    {
                        sheet.supcust_id = (string)record.rs_client_id;
                    }

                }
                else
                {
                    sheet.supcust_id = (string)resellerInfo.client_id;
                }
                sheet.company_id = (string)resellerInfo.company_id;
                sheet.sheet_type = SHEET_TYPE.SHEET_FEE_OUT;
                sheet.SheetType = "ZC";
                // 暂时填
                sheet.getter_id = "1";
                sheet.getter_name = "";
            }
            else
            {
                string rsCompanySql = $"select company_id,oper_id from info_operator where rs_client_id = {supcust_id}";
                dynamic rsCompanyInfo = await CDbDealer.Get1RecordFromSQLAsync(rsCompanySql, cmd);
                string resellerInfoSql = "";
                string operId = "";
                if (rsCompanyInfo != null)
                {
                    resellerInfoSql = @$"select * from rs_seller  rss 
                    left join (select plan_id,client_mapper from rs_plan) rsp on rss.plan_id = rsp.plan_id 
                    where company_id={companyID} and reseller_company_id = {rsCompanyInfo.company_id}";
                    operId = rsCompanyInfo.oper_id;

                }
                else
                {
                    resellerInfoSql = @$"select * from rs_seller  rss 
                    left join (select plan_id,client_mapper from rs_plan) rsp on rss.plan_id = rsp.plan_id 
                    where company_id={companyID} and client_id = {oriSheet.supcust_id}";

                }
                dynamic resellerInfo = await CDbDealer.Get1RecordFromSQLAsync(resellerInfoSql, cmd);
                // TODO 客户 此处为厂家 supplier
                if ((string)resellerInfo.client_mapper == "sellerAsClient")
                {
                    // string sellerClientSql = $"select rs_seller_id from info_supcust where oper_id = {clientId}";
                    // dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sellerClientSql, cmd);
                    sheet.getter_id = operId;

                }
                else
                {
                    sheet.getter_id = "1";
                }
                sheet.company_id = (string)resellerInfo.reseller_company_id;
                sheet.supcust_id = (string)resellerInfo.supplier_id;
                sheet.sheet_type = SHEET_TYPE.SHEET_FEE_IN;
                sheet.SheetType = "SR";
            }
            foreach (SheetRowFeeOut row in sheet.SheetRows)
            {
                // 费用类型如何对应？
                if (sheetType == "SR")
                {
                    // 供应商支出转为分销商收入
                    row.remark = $"供应商支出费用类型：{row.fee_sub_name}（编号{row.fee_sub_id}）";
                }
                else
                {
                    // 分销商收入转为供应商支出
                    row.remark = $"分销商收入费用类型：{row.fee_sub_name}（编号{row.fee_sub_id}）";
                }
                
                row.fee_sub_id = "";
                row.fee_sub_name = "";
                
            }
            sheet.sheet_id = "";
            sheet.sheet_no = "";
            sheet.maker_id = "1";
            sheet.maker_name = "";

            // sheet.payway1_id = "";
            // sheet.payway1_amount = 0;

            // 不清除的话同步单据也会被审核
            sheet.approver_id = "";
            sheet.approve_time = "";
            sheet.approve_brief = "";
            if (err == "")
                err = await sheet.Save(cmd, false);
            return err;
        }
        public override string GetSheetCharactor()
        {
            string res =  this.company_id + "_" + this.sheet_id + "_" + this.supcust_id + "_" + this.OperID + "_" +  this.make_brief + "_" + this.payway1_id + "_" + this.payway1_amount + "_" + (this.payway2_amount != 0 ? this.payway2_id + "_" + this.payway2_amount.ToString():"");
            foreach (var row in SheetRows)
            {
                res += row.fee_sub_id + "_" + row.fee_sub_amount + row.remark;
            }
            return res;
        }
        /* public override async Task<string> SaveAndApprove(CMySbCommand cmd, bool bAutoCommit = true)
         {
             string err = "";
             if (!IsImported)
             {
                 cmd.ActiveDatabase = "";
                 string rsSellerSql = "";
                 if (SheetType == "SR")
                 {
                     // 预付款以供应商查看rs_seller
                     rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync,rp.client_mapper FROM rs_seller rs
 LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
  WHERE rs.reseller_company_id = {company_id} and rs.supplier_id = {supcust_id};";
                 }
                 else
                 {
                     // SheetType = "ZC"
                     // 预收款以客户查看
                     string rsCompanySql = $"select company_id from info_operator where rs_client_id = {supcust_id}";
                     dynamic rsCompanyInfo = await CDbDealer.Get1RecordFromSQLAsync(rsCompanySql, cmd);
                     if (rsCompanyInfo != null)
                     {
                         rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync,rp.client_mapper FROM rs_seller rs
 LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
  WHERE rs.company_id = {company_id} and rs.reseller_company_id = {rsCompanyInfo.company_id};";
                     }
                     else
                     {
                         rsSellerSql = $@"SELECT rs.reseller_company_id, rs.plan_id,rp.sheet_sync,rp.client_mapper FROM rs_seller rs
 LEFT JOIN rs_plan rp on rp.company_id = rs.company_id and rp.plan_id = rs.plan_id
  WHERE rs.company_id = {company_id} and rs.client_id = {supcust_id};";
                     }
                 }
                 dynamic rsSeller = await CDbDealer.Get1RecordFromSQLAsync(rsSellerSql, cmd);
                 // string clientType = (string) rsSeller.client_mapper;
                 string orderSource = "";

                  if (sheet_id != "")
                  {
                      string orderSourceSql = $"select  * from sheet_prepay where company_id = {company_id} and supcust_id = {supcust_id} and sheet_id ={sheet_id};";
                      dynamic dbSheet = await CDbDealer.Get1RecordFromSQLAsync(orderSourceSql, cmd);
                      orderSource = dbSheet.order_source;
                  }
                  if (rsSeller != null && rsSeller.sheet_sync.ToLower() == "true" && orderSource != "2fx")
                  {
                      CMySbTransaction tran = cmd.Connection.BeginTransaction();
                      err = await base.SaveAndApprove(cmd, false);
                      if (err == "")
                      {
                          err = await SyncFeeOut(cmd, this, SheetType);
                      }

                      if (err == "")
                      {
                          tran.Commit();
                      }
                      else
                      {
                          tran.Rollback();
                      }
                  }
                  else
                  {
                      err = await base.SaveAndApprove(cmd, bAutoCommit);
                  }

              }
              else
              {
                  err = await base.SaveAndApprove(cmd, bAutoCommit);
              }
              return err;
              }*/
        /*
        protected List<SheetRowFeeOut> MergeDispSheetRows(List<SheetRowFeeOut> rows)
        {
            Dictionary<string, SheetRowFeeOut> rowsDict = new Dictionary<string, SheetRowFeeOut>();
            foreach (SheetRowFeeOut sheetRow in SheetRows)
            {
                if (sheetRow.display_id != null && sheetRow.display_id == "") continue;
                string skey = sheetRow.display_id;
                SheetRowFeeOut curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = new SheetRowFeeOut();
                    curRow.display_id = sheetRow.display_id;
                    curRow.fee_sub_id = sheetRow.fee_sub_id;
                    curRow.fee_sub_name = sheetRow.fee_sub_name;
                    curRow.fee_sub_amount = sheetRow.fee_sub_amount;
                    rowsDict.Add(skey, curRow);
                }
                else curRow.fee_sub_amount += sheetRow.fee_sub_amount;

            }
            List<SheetRowFeeOut> newList = new List<SheetRowFeeOut>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }
        */
        // public override async Task OnSheetIDGot_Copy(CMySbCommand cmd, string sheetID, CInfoForApproveBase info)
        //         {
        //             await base.OnSheetIDGot(cmd, sheetID, info);
        //
        //             #region 陈列协议兑付
        //             string dispSubIDs = "";
        //             Dictionary<string, decimal> displaySub = new Dictionary<string, decimal>();
        //             foreach(var disp in SheetRows)
        //             {
        //                 if (disp.display_id != "")
        //                 {
        //                     if (dispSubIDs != "") dispSubIDs += ",";
        //                     displaySub.Add(disp.display_id, disp.fee_sub_amount);
        //                     dispSubIDs += disp.fee_sub_id;
        //                 }
        //                 
        //             }
        //
        //             if (dispSubIDs != "")
        //             {
        //                 string months = "";
        //                 string monthCondi = "";
        //                 string allGivenCondi = " and all_given is not true";
        //                 for (int i = 1; i <= 12; i++)
        //                 {
        //                     months += $",coalesce(month{i}_qty,0) as month{i}_qty,coalesce(month{i}_given,0) as month{i}_given";
        //                     if (monthCondi != "") monthCondi += " or ";
        //                     monthCondi += $" coalesce(month{i}_qty,0) - coalesce(month{i}_given,0) >0.01 ";
        //                 }
        //                 monthCondi = "(" + monthCondi + ")";
        //                 if (money_inout_flag == 1)
        //                 {
        //                     allGivenCondi = " ";
        //                     monthCondi = monthCondi.Replace("0.01", "-0.01");
        //                 }
        //                 string sqlQuery = @$"select d.sheet_id,start_time,end_time {months} from display_agreement_detail d left join display_agreement_main m on m.sheet_id = d.sheet_id   where d.company_id = {company_id} and red_flag is null and approve_time is not null and supcust_id = {supcust_id} and items_id='money' and m.fee_sub_id in ({dispSubIDs}) and {monthCondi} {allGivenCondi};";
        //
        //                 List<ExpandoObject> lstFlow = await CDbDealer.GetRecordsFromSQLAsync(sqlQuery, cmd);
        //
        //                 foreach(var dSub in displaySub)
        //                 {
        //                     var dSheetID = dSub.Key;
        //                     decimal sub_amount = dSub.Value;
        //                      
        //                     if (sub_amount < 0)
        //                     {
        //                         info.ErrMsg = "陈列协议金额不能为负";
        //                         break;
        //                     }
        //                     decimal leftAmt1 = sub_amount;
        //                     if (money_inout_flag == -1)
        //                     {
        //                         foreach (dynamic flow in lstFlow)
        //                         {
        //                             decimal stillLeftAmt = 0;
        //                             string flowFields = "";
        //                             JObject jFlow = JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(flow));
        //                             for (int m = 1; m <= 12; m++)
        //                             {
        //                                 string sQty = jFlow[$"month{m}_qty"].ToString();
        //                                 decimal qty = CPubVars.ToDecimal(sQty);
        //                                 string sGiven = jFlow[$"month{m}_given"].ToString();
        //                                 decimal given = CPubVars.ToDecimal(sGiven);
        //                                 decimal monthLeft = qty - given;
        //                                 var dispSheetID = flow.sheet_id;
        //                                 if (dispSheetID == dSheetID )
        //                                 {
        //                                     decimal useAmt = 0;
        //                                     if (leftAmt1 >= monthLeft) useAmt = monthLeft; else useAmt = leftAmt1;
        //
        //                                     leftAmt1 -= useAmt;
        //                                     string fldName = $"month{m}_given";
        //                                     if (leftAmt1 >= 0 && useAmt != 0)
        //                                     {
        //                                         if (flowFields != "") flowFields += ",";
        //                                         flowFields += $"{fldName} =coalesce({fldName},0) +({useAmt})";
        //                                     }
        //                                     stillLeftAmt += qty - given - useAmt;
        //                                 }
        //                             }
        //                             if (flowFields != "")
        //                             {
        //                                 string allGiven = "true";
        //                                 if (stillLeftAmt > 0.01m) allGiven = "false";
        //                                 flowFields += $",all_given={allGiven}";
        //                                 AddExecSQL($"update display_agreement_detail set {flowFields} where company_id={company_id} and sheet_id={dSheetID};");
        //                             }
        //                         }
        //                     }
        //                     else if (money_inout_flag == 1)
        //                     {
        //
        //                         foreach (dynamic flow in lstFlow)
        //                         {
        //                             decimal stillLeftAmt = 0;
        //                             string flowFields = "";
        //                             JObject jFlow = JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(flow));
        //                             for (int m = 12; m >= 1; m--)
        //                             {
        //                                 string sQty = jFlow[$"month{m}_qty"].ToString();
        //                                 decimal qty = CPubVars.ToDecimal(sQty);
        //                                 string sGiven = jFlow[$"month{m}_given"].ToString();
        //                                 decimal given = CPubVars.ToDecimal(sGiven);
        //                                 decimal monthLeft = qty - given;
        //                                 var dispSheetID = flow.sheet_id;
        //                                 if (dispSheetID == dSheetID && given > 0)
        //                                 {
        //                                     decimal useAmt = 0;
        //                                     if (leftAmt1 >= given) useAmt = given; else useAmt = leftAmt1;
        //
        //                                     useAmt *= money_inout_flag;
        //                                     leftAmt1 -= useAmt;
        //                                     string fldName = $"month{m}_given";
        //                                     if (leftAmt1 >= 0 && useAmt != 0)
        //                                     {
        //                                         if (flowFields != "") flowFields += ",";
        //                                         flowFields += $"{fldName} =coalesce({fldName},0) - ({useAmt})";
        //                                     }
        //                                     stillLeftAmt += qty - given - (useAmt * (-1));
        //                                 }
        //                             }
        //                             if (flowFields != "")
        //                             {
        //                                 string allGiven = "false";
        //                                 if (stillLeftAmt < 0.01m) allGiven = "true";
        //                                 flowFields += $",all_given={allGiven}";
        //                                 AddExecSQL($"update display_agreement_detail set {flowFields} where company_id={company_id} and sheet_id={dSheetID};");
        //                             }
        //                         }
        //                     }
        //                 }
        //
        //             }
        //
        //             #endregion
        //             CInfoForApprove info1 = (CInfoForApprove)info;
        //             string sRedFlag = "null";
        //             if (red_flag.IsValid()) sRedFlag = "'" + red_flag + "'";
        //             int flag = money_inout_flag;
        //             if (left_amount != 0 && supcust_id !="")
        //             {
        //                 decimal arrearsBal = 0;
        //                 decimal changeBal = left_amount * flag;
        //                 if ( info1.ArrearBalance != "") arrearsBal = CPubVars.ToDecimal(info1.ArrearBalance);
        //                 arrearsBal += changeBal;
        //
        //                 if (info1.ArrearBalance != "")
        //                 {
        //                     AddExecSQL($"update arrears_balance set balance=balance+({changeBal}) where company_id={company_id} and supcust_id={supcust_id};");
        //                 }
        //                 else
        //                 {
        //                     AddExecSQL($"insert into arrears_balance (company_id,supcust_id,balance) values ({company_id},{supcust_id},{changeBal});");
        //                 }
        //                 if (red_flag == "2")
        //                 {
        //                     AddExecSQL(@$"update client_account_history set red_flag = '1' where company_id = {company_id} and sheet_id = {red_sheet_id} and sheet_type = '{SheetType}' and sub_type = 'QK';");
        //                 }
        //                 AddExecSQL($"insert into client_account_history(company_id,happen_time,approve_time, sheet_type,sheet_id,change_amount,now_balance,supcust_id,sub_type,red_flag) values ({company_id},'{CPubVars.GetDateText(happen_time)}','{CPubVars.GetDateText(approve_time)}', '{SheetType}',{sheetID},{changeBal},{arrearsBal},{supcust_id},'QK',{sRedFlag});");
        //             
        //
        //
        //             }
        //                 cmd.CommandText = GetExecSQL();
        //             if (cmd.CommandText != "")
        //             {
        //                 await cmd.ExecuteNonQueryAsync();
        //             }
        //         }
        //        

        /*public override async Task<JsonResult> ToVoucherRows(CMySbCommand cmd, string sheetID, SheetCwVoucher sheetCwVoucher, Dictionary<string, decimal> payways)
        {
            string subsID = ""; 
            int subLen = 0;
            string condi = "";
            if (payways.Count == 0)
            {
                if (payway1_id != "" && payway1_amount != 0) payways.Add(payway1_id, payway1_amount * money_inout_flag);
                if (payway2_id != "" && payway2_amount != 0) payways.Add(payway2_id, payway2_amount * money_inout_flag);
                if (left_amount != 0) payways.Add("left", left_amount * money_inout_flag);
                foreach (var row in SheetRows)
                {
                    if (row.fee_sub_id != "")
                    {
                        payways.Add("f" + row.fee_sub_id, row.fee_sub_amount * money_inout_flag);
                    }
                }
            }
            if (payways == null || payways.Count == 0)
            {
                return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
            }
            foreach (var payway in payways)
            {
                if (payway.Key == "left")
                {
                    condi += $@"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='2202' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='2202')  order by sub_code limit 1 )"; // 应付账款
                }
                else
                {
                    if (subsID != "") subsID = subsID + ",";
                    subsID += payway.Key.StartsWith("f") ? payway.Key.Substring(1) : payway.Key;
                }
                subLen++;
            }
            string payCondi = "";
            if (subsID != "") payCondi += $" and sub_id in ({subsID})";
            string sql = $"(select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} {payCondi}) {condi}";
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (records == null || records.Count < subLen) return new JsonResult(new { result = "Error", msg = "缺少生成凭证的相关科目，请添加" });
            if (records.Count > subLen) return new JsonResult(new { result = "Error", msg = "生成凭证的相关科目有重复科目代码，请修改" });

            foreach (var rec in records)
            {
                if (rec.status == null || rec.status == "") rec.status = 1;
                if (Convert.ToInt16(rec.status) == 0) return new JsonResult(new { result = "Error", msg = "相关科目已停用，请检查" });
                CwRowVoucher cwRow = new CwRowVoucher();
                cwRow.business_sheet_type = SheetType;
                cwRow.business_sheet_id = sheetID;
                cwRow.sub_id = rec.sub_id;
                cwRow.remark = SheetType == "ZC" ? "费用支出" : "其他收入";
                decimal changeAmt = 0;

                foreach (var payway in payways)
                {
                    changeAmt = payway.Value;

                    if (payway.Key == rec.sub_id )
                    {
                        if (changeAmt >= 0) cwRow.debit_amount = changeAmt.ToString();
                        else cwRow.credit_amount = Math.Abs(changeAmt).ToString();// ZC正常开单: changeAmt<0  贷payway，借费用科目； SR开单: changeAmt>0  借payway，贷收入科目
                        cwRow.change_amount = changeAmt.ToString();
                        break;
                    }
                    else if (payway.Key == "f" + rec.sub_id) 
                    {
                        if (changeAmt >= 0) cwRow.credit_amount = changeAmt.ToString();
                        else cwRow.debit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = (-1 * changeAmt).ToString();
                        break;
                    }
                    else if (payway.Key == "left" && rec.sub_code.ToString().Substring(0, 4) == "2202")
                    {
                        if (changeAmt >= 0) cwRow.debit_amount = changeAmt.ToString();
                        else cwRow.credit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = changeAmt.ToString();
                        break;
                    }
                }

                sheetCwVoucher.SheetRows.Add(cwRow);
            }
            return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
        }*/
    }
}
