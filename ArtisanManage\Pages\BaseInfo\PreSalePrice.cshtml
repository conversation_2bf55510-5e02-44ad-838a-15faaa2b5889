﻿@page
@model ArtisanManage.Pages.BaseInfo.PreSalePriceModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var RowIndex = -1;
        var newCount = 1;
        
        function btnAddItem_click(e) {
            var selectedItem = $('#other_class').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个类");
                return;
            }
            var path = $('#other_class').jqxTree('getTreePath', selectedItem);
            //$('#popItem').jqxWindow('open');
            //$("#popItem").jqxWindow('setContent', `<iframe src="ItemEdit?operKey=${g_operKey}&item_class=${selectedItem.value}&class_name=${selectedItem.label}&other_class=${path}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }
        /*function attachContextMenu() {
            if (!window.contextMenu) window.contextMenu = $("#jqxMenu").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            function isRightClick(event) {
                var rightclick;
                if (!event) var event = window.event;
                if (event.which) rightclick = (event.which == 3);
                else if (event.button) rightclick = (event.button == 2);
                return rightclick;
            }
            // open the context menu when the user presses the mouse right button.
            $("#other_class li").on('mousedown', function (event) {
                var target = $(event.target).parents('li:first')[0];
                var rightClick = isRightClick(event);


                if (rightClick && target != null) {
                    $("#other_class").jqxTree('selectItem', target);
                    var scrollTop = $(window).scrollTop();
                    var scrollLeft = $(window).scrollLeft();

                    contextMenu.jqxMenu('open', parseInt(event.clientX) + 5 + scrollLeft, parseInt(event.clientY) + 5 + scrollTop);

                    return false;
                }
            });

        }*/

        /*function onGridRowEdit(rowIndex) {
            var s = document.getElementById("price_plan").getElementsByTagName("input")[0].dataset;
            var label = s.value;
            var item_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'i');
            //$('#popItem').jqxWindow('open');
            //$("#popItem").jqxWindow('setContent', '<iframe src="PricePlanItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '&plan_id=' + label + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }*/
        var itemSource = {};


        function selectChange(e) {
            var select = document.getElementById("mySelect");
            var value = select.value;
            for (var rownum = 0; rownum < window.source_gridItems.totalrecords; rownum++) {
                var data = $('#gridItems').jqxGrid('getcellvalue', rownum, "total");
                if (data != null) {
                    var arr = data.split("; ");
                    var selectedPrice = "";
                    for (var i = 0; i < arr.length; i++) {
                        var c = arr[i].split(":");
                        if (c[0] == value) {
                            selectedPrice = c[1];
                            break;
                        }
                    }
                    var prices = selectedPrice.split(",");
                    if (prices.length == 2) {
                        $('#gridItems').jqxGrid('setcellvalue', rownum, "small_price", prices[0]);
                        $('#gridItems').jqxGrid('setcellvalue', rownum, "mid_price", "");
                        $('#gridItems').jqxGrid('setcellvalue', rownum, "big_price", prices[1]);
                    } else if (prices.length == 3) {
                        $('#gridItems').jqxGrid('setcellvalue', rownum, "small_price", prices[0]);
                        $('#gridItems').jqxGrid('setcellvalue', rownum, "mid_price", prices[1]);
                        $('#gridItems').jqxGrid('setcellvalue', rownum, "big_price", prices[2]);
                    }
                    else {
                        $('#gridItems').jqxGrid('setcellvalue', rownum, "small_price", "");
                        $('#gridItems').jqxGrid('setcellvalue', rownum, "mid_price", "");
                        $('#gridItems').jqxGrid('setcellvalue', rownum, "big_price", "");
                    }
                } else {
                    $('#gridItems').jqxGrid('setcellvalue', rownum, "small_price", "");
                    $('#gridItems').jqxGrid('setcellvalue', rownum, "mid_price", "");
                    $('#gridItems').jqxGrid('setcellvalue', rownum, "big_price", "");
                }
            }
        }

        $(document).ready(function () {
            window.contextMenu = $("#jqxMenu").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
        @Html.Raw(Model.m_showFormScript)
        @Html.Raw(Model.m_createGridScript)


                $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

            $("#gridItems").on("cellclick", function (event) {
                // event arguments.
                var args = event.args;
                if (args.datafield == "detail") {
                    if (args.originalEvent.button == 2) return;
                    var item_id = args.row.bounddata.i;
                    RowIndex = args.rowindex;
                    if (ForSelect) {
                        var item_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                        var item_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "n");
                        var msg = {
                            msgHead: 'PriceView', action: 'select', item_id: item_id, item_name: item_name
                        };
                        window.parent.postMessage(msg, '*');
                    }
                    else {
                        onGridRowEdit(args.rowindex);
                        //$('#popItem').jqxWindow('open');
                        // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                    }
                }
            });
            $("#Cancel").on('click', function () {
                for (var i = 0; i < 10; i++) {
                    $('#jqxgrid').jqxGrid('deleterow', i);
                    $('#jqxgrid').jqxGrid('addrow', i, {})
                }
            });



            //$("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 670, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            //$("#popClass").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 350, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            //attachContextMenu();

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            QueryData();


        });

    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        .label_name {
            line-height: 32px;
            margin-left: 10px;
        }

        .label_content {
            width: 120px;
            height: 30px;
            margin-left: 10px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
    </style>
</head>

<body style="overflow:hidden">

    <div id="divHead" style="display:flex;justify-content:flex-start;margin-top:20px;">
        <div style="display:inherit">
            <div style="display: flex;width:260px;"><label class="label_name">商品</label> <div id="item_id" style="width:200px;" class="label_content"></div></div>
            <div style="display: flex;"><label class="label_name">客户</label><div id="supcust_id" class="label_content"></div></div>
            <button onclick="QueryData()" class="margin">查询</button>
            <button id="btnExport" onclick="ExportExcel()" class="margin">导出</button>
        </div>
    </div>

    <div style="display:flex;height:100%;">

        <div id='other_class' style="width:0px;height:calc(100% - 90px);margin-top:20px;margin-bottom:2px;overflow-y:scroll">
        </div>

        <div style="width:100%;height:100%;margin-left:10px;">

            <div>
                <div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div>
            </div>

            <div id="gridItems" style="margin-top:0px;margin-bottom:2px;width:calc(100% - 10px);height:calc(100% - 90px);"></div>

        </div>

    </div>

    <div id='jqxMenu'>
    </div>

</body>
</html>