﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{

    /// <summary>
    /// 一、预收款方名称
    /// 二、应收款
    /// 三、预收款
    /// </summary>

    [Route("AppApi/[controller]/[action]")]
    public class SheetAcountController : QueryController
    { 
        public SheetAcountController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        /// <summary>
        /// 预收款方名称
        /// </summary>
        /// <param name="operKey"> </param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetSubName(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql = $"SELECT sub_id,sub_name FROM cw_subject where company_id = {companyID} AND sub_type = 'YS' and is_order is not true and COALESCE(status,'1')='1' ";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }
        /// <summary>
        /// 预付款
        /// </summary>
        /// <param name="operKey"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetSubNamePay(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql = $"SELECT sub_id,sub_name FROM cw_subject where company_id = {companyID} AND sub_type = 'YF' and is_order is not true and COALESCE(status,'1')='1'";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }




        /// <summary>
        /// 应收款界面
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="searchStr"></param>
        /// <returns></returns>
        /// 2021-3-31
        [HttpGet]
        public async Task<JsonResult> GetArrears(string operKey, string searchStr, string regionID, string sellerID, string senderID, string startTimeStr, string endTimeStr, bool showNoArrearsClients, int pageSize, int startRow, string dayID,string arrearsAge)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = "";
            string clientCondi = "";
            if (!string.IsNullOrWhiteSpace(searchStr)) clientCondi += $" and (sc.sup_name like '%{searchStr}%' )";
            
            if (!string.IsNullOrWhiteSpace(regionID))
            {
                string[] regionArr = regionID.Split(",");
                string regionCondi = "";
                foreach (var region in regionArr)
                {
                    if (regionCondi != "") regionCondi += " or ";
                    regionCondi += $"sc.other_region  like '%/{region}/%'";
                }
                if (regionCondi != "") regionCondi = " and (" + regionCondi + ")";
                clientCondi += regionCondi;
            }


            SQLQueue QQ = new SQLQueue(cmd);
            SQLVariables["arrears_age_condi"] = "";
            if (!string.IsNullOrWhiteSpace(arrearsAge))
                SQLVariables["arrears_age_condi"] += @$"and ( happen_time <= '{DateTime.Today.AddDays(-double.Parse(arrearsAge)).ToString("yyyy-MM-dd")} 23:59')";
            if (string.IsNullOrEmpty(startTimeStr))
            {
                startTimeStr = "2000-1-1";
            }
            if (string.IsNullOrEmpty(endTimeStr))
            {
                endTimeStr = CPubVars.GetDateText(DateTime.Now.AddDays(1));
            }
            SQLVariables["startDay"] = startTimeStr;
            SQLVariables["endDay"] = endTimeStr;

            if (!string.IsNullOrEmpty(senderID))
            {
                SQLVariables["senders_id"] = $" and senders_id LIKE '%{senderID}%'";
                SQLVariables["tail_condi_sender_id"] = $" and sm.senders_id LIKE '%{senderID}%'";

            }
            else
            {
                SQLVariables["senders_id"] = "";
                SQLVariables["tail_condi_sender_id"] = " ";
            } 
            if (!string.IsNullOrEmpty(sellerID))
            {
                SQLVariables["seller_id"] = "and  seller_id =" + sellerID;
                SQLVariables["getter_id"] = "and  getter_id =" + sellerID;
                SQLVariables["tail_condi_seller_id"] = $" AND (sm.seller_id ={sellerID} or sp.seller_id ={sellerID} or sf.seller_id ={sellerID} ) ";
            }
            else
            {
                SQLVariables["seller_id"] = " ";
                SQLVariables["getter_id"] = " ";
                SQLVariables["tail_condi_seller_id"] = " ";
            }
            SQLVariables["stock_qty_condi"] = "";
            if (!showNoArrearsClients)
            {
                SQLVariables["stock_qty_condi"] = " and abs(b.balance)>=0.01 ";
            }
            string sumFields = "";
            if (startRow == 0)
            {
                sumFields = ",count(0) over () as record_count,sum(balance) over() as sum_balance,sum(left_amount) over() as sum_period_left_amount,sum(total_amount) over () as sum_period_total_amount";
            }
            if (!string.IsNullOrEmpty(dayID))
            {

                SQLVariables["tail_visit_day_id"] = $" AND ivdc.day_id ={dayID} ";
                SQLVariables["join_info_visit_day"] = $" LEFT JOIN (select distinct supcust_id,day_id from info_visit_day_client where company_id={companyID}) ivdc on ivdc.supcust_id = T.supcust_id ";
            }
            else
            {
                SQLVariables["join_info_visit_day"] = $"";
                SQLVariables["tail_visit_day_id"] = $"";
            }


            string sql = $@"
select t.supcust_id,sup_name,qk_add ,disc_amount,qk_reduce,left_amount period_left_amount,total_amount,left_amount no_paid_amount,balance {sumFields}

FROM 
(
    select
        b.supcust_id,sc.sup_name,
        round(
        (
            SUM(COALESCE(sm.total_amount, 0))
                        + SUM(COALESCE(sp.total_amount, 0))
                        + SUM(COALESCE(sf.total_amount, 0))
                        + SUM(COALESCE(cg.total_amount, 0))
            )::numeric,2
        )  total_amount,
        round( 
          (
                SUM (COALESCE ( sm.total_amount, 0 ) - COALESCE ( sm.now_disc_amount, 0 ) - COALESCE ( sm.now_pay_amount, 0 )) 
              + SUM (COALESCE ( sp.total_amount, 0 ) - COALESCE ( sp.now_disc_amount, 0 ) - COALESCE ( sp.now_pay_amount, 0 ))
              + SUM (COALESCE ( sf.total_amount, 0 ) - COALESCE ( sf.now_disc_amount, 0 ) - COALESCE ( sf.now_pay_amount, 0 )) 
              + SUM (COALESCE ( cg.total_amount, 0 ) - COALESCE ( cg.now_disc_amount, 0 ) - COALESCE ( cg.now_pay_amount, 0 )) 
          )::numeric,2
        ) qk_add,
        SUM ( COALESCE (sp.disc_amount, 0 ) - COALESCE ( sp.now_disc_amount, 0 ) ) 
      + SUM ( COALESCE (sm.disc_amount, 0 ) - COALESCE ( sm.now_disc_amount, 0 ) ) 
      + SUM ( COALESCE (sf.disc_amount, 0 ) - COALESCE ( sf.now_disc_amount, 0 ) )
      + SUM ( COALESCE (cg.disc_amount, 0 ) - COALESCE ( cg.now_disc_amount, 0 ) )
        disc_amount,
        SUM ( COALESCE (sp.paid_amount, 0 ) - COALESCE ( sp.now_pay_amount, 0 ) ) 
      + SUM ( COALESCE (sm.paid_amount, 0 ) - COALESCE ( sm.now_pay_amount, 0 ) ) 
      + SUM ( COALESCE (sf.paid_amount, 0 ) - COALESCE ( sf.now_pay_amount, 0 ) )      
      + SUM ( COALESCE (cg.paid_amount, 0 ) - COALESCE ( cg.now_pay_amount, 0 ) )      
        qk_reduce,
        round
        (
            (
                  SUM ( COALESCE ( sm.total_amount, 0 ) - COALESCE ( sm.disc_amount, 0 ) - COALESCE ( sm.paid_amount, 0 ))   
                + SUM ( COALESCE ( sp.total_amount, 0 ) - COALESCE ( sp.disc_amount, 0 ) - COALESCE ( sp.paid_amount, 0 ))                 
                + SUM ( COALESCE ( sf.total_amount, 0 ) - COALESCE ( sf.disc_amount, 0 ) - COALESCE ( sf.paid_amount, 0 ))
                + SUM ( COALESCE ( cg.total_amount, 0 ) - COALESCE ( cg.disc_amount, 0 ) - COALESCE ( cg.paid_amount, 0 ))
            )::numeric,2
        )
        left_amount,
        balance
    from arrears_balance b
    LEFT JOIN info_supcust sc ON b.supcust_id = sc.supcust_id AND sc.company_id =~COMPANY_ID 
    LEFT JOIN 
    (
       select * from client_account_history where company_id =~COMPANY_ID and red_flag is null and happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay'  and sub_type = 'QK'
    ) cah ON b.company_id = cah.company_id and b.supcust_id = cah.supcust_id 
    LEFT JOIN 
    (
       select sheet_id,supcust_id,seller_id,senders_id,money_inout_flag,total_amount*money_inout_flag total_amount,disc_amount*money_inout_flag disc_amount,
                        paid_amount*money_inout_flag paid_amount,now_pay_amount*money_inout_flag now_pay_amount,now_disc_amount*money_inout_flag now_disc_amount 
       from sheet_sale_main where company_id =~COMPANY_ID  and happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay' ~VAR_seller_id  ~VAR_senders_id ~VAR_arrears_age_condi
    ) sm ON cah.sheet_id = sm.sheet_id  and cah.sheet_type in ('X','T')
    LEFT JOIN 
    (
       select sheet_id,getter_id seller_id,supcust_id,money_inout_flag,total_amount*money_inout_flag total_amount,disc_amount*money_inout_flag disc_amount,
                        paid_amount*money_inout_flag paid_amount,now_pay_amount*money_inout_flag now_pay_amount,now_disc_amount*money_inout_flag now_disc_amount 
        from sheet_prepay where company_id =~COMPANY_ID  and happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay' ~VAR_getter_id ~VAR_arrears_age_condi
    )  sp ON cah.sheet_id = sp.sheet_id  and cah.sheet_type in ('YS','YF','DH')
    LEFT JOIN 
    (
        SELECT sheet_id,supcust_id,getter_id seller_id,money_inout_flag,total_amount * money_inout_flag total_amount,disc_amount * money_inout_flag disc_amount,
                          paid_amount * money_inout_flag paid_amount,now_pay_amount * money_inout_flag now_pay_amount,now_disc_amount * money_inout_flag now_disc_amount 
        FROM sheet_fee_out_main WHERE company_id = ~COMPANY_ID  AND happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay'  ~VAR_getter_id ~VAR_arrears_age_condi
    ) sf ON cah.sheet_id = sf.sheet_id  and cah.sheet_type in ('ZC','SR')
    LEFT JOIN 
        (
            SELECT sheet_id,supcust_id,getter_id seller_id,money_inout_flag,total_amount * money_inout_flag total_amount,disc_amount * money_inout_flag disc_amount,
                              paid_amount * money_inout_flag paid_amount,now_pay_amount * money_inout_flag now_pay_amount,now_disc_amount * money_inout_flag now_disc_amount 
            FROM sheet_buy_main WHERE company_id = ~COMPANY_ID  AND happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay'  ~VAR_getter_id ~VAR_arrears_age_condi
        ) cg ON cah.sheet_id = cg.sheet_id  and cah.sheet_type in ('CG')

    where b.company_id =~COMPANY_ID and sc.supcust_flag in ('C','CS') {clientCondi}  ~VAR_stock_qty_condi ~VAR_tail_condi_seller_id ~VAR_tail_condi_sender_id

    group by b.supcust_id,sc.sup_name,b.balance
    
) t 
~VAR_join_info_visit_day 

where 1=1 {condi} ~VAR_tail_visit_day_id  order by balance desc,supcust_id asc";

            sql = GetRealSQL(sql, companyID);

            if (pageSize == 0) pageSize = 1000;
            string sqlLimit = $"{sql} limit {pageSize} offset {startRow}";
            QQ.Enqueue("data", sqlLimit);
            /*if (startRow == 0)
            {
                sql = @$"SELECT count(*) as record_count,sum(balance) as sum_balance,sum(period_left_amount) as sum_period_left_amount FROM ({sql} ) t";
                QQ.Enqueue("sum", sql);
            }*/

            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();

            string sum_balance = "";
            string sum_period_left_amount = "";
            string sum_period_total_amount = "";
            int recordCount = -1;
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                    if (startRow == 0 && data.Count > 0)
                    {
                        dynamic first = data.First();
                        recordCount = Convert.ToInt32(first.record_count);
                        sum_balance = first.sum_balance;
                        sum_period_left_amount = first.sum_period_left_amount;
                        sum_period_total_amount = first.sum_period_total_amount;
                    }
                }
                /*
                else if (sqlName == "sum")
                {
                    dr.Read();
                    sum_balance = CPubVars.GetTextFromDr(dr, "sum_balance");
                    sum_period_left_amount = CPubVars.GetTextFromDr(dr, "sum_period_left_amount");
                    string s = CPubVars.GetTextFromDr(dr, "record_count");
                    recordCount = Convert.ToInt32(s);
                }*/
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, sum_balance, total = sum_balance, sum_period_left_amount, sum_period_total_amount, recordCount });
        }


        /// <summary>
        /// 查询付款方式
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="subID"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetPayway(string operKey, string subID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where company_id = {companyID}";
            if (subID != null) condi += $"sub_id = {subID}";
            var sql = $"SELECT sub_id,sub_name FROM cw_subject {condi} order by order_index";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }

    }
}