﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class FeeOutsViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
        public FeeOutsViewModel(CMySbCommand cmd) : base(Services.MenuId.paywaysView)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                //{"sub_type",new DataItem(){Hidden=true,HideOnLoad=true}
               // },
                 {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称/助记码",UseJQWidgets=false, SqlFld="sub_name,py_str",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sub_id",new DataItem(){Title="支付序号", Width="80",Hidden=true}},
                       {"sub_code",new DataItem(){Title="支付编号", Width="80",Hidden=true}},
                       {"sub_name",new DataItem(){Title="费用支出名称", Width="180",Linkable=true}},
                     },
                     QueryFromSQL="from cw_subject where sub_type = 'ZC'" , QueryOrderSQL="order by sub_id"
                  }
                } 
            }; 
        }
        public async Task OnGet(string forSelect)
        {  
            await InitGet(cmd);
            ForSelect = forSelect == "1";
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }


        public async override Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            cmd.CommandText = $"select company_id from sheet_fee_out_detail where fee_sub_id in ({rowIDs}) and company_id={company_id} limit 1";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该费用类型已有被使用,请勿删除"; 

            return "";
        }
    }



    [Route("api/[controller]/[action]")]
    public class FeeOutsViewController : QueryController
    {  
        public FeeOutsViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            FeeOutsViewModel model = new FeeOutsViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {// string gridID,int startRow,int endRow,bool bNewQuery){
            FeeOutsViewModel model = new FeeOutsViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

    }
}
