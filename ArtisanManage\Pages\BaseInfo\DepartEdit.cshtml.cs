using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc; 
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using ArtisanManage.Services;

namespace ArtisanManage.Pages.BaseInfo
{
    public class DepartEditModel :  PageFormModel
    {  
        public string m_sheet_no { get; set; } 
        public DepartEditModel(CMySbCommand cmd) : base(Services.MenuId.infoOperator)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"depart_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"depart_name",new DataItem(){Title="部门名称",FldArea="divHead", Necessary=true}},
                {"mother_id",new DataItem(){Title="上级部门", LabelFld="mother_name",CtrlType="jqxDropDownTree",MumSelectable=true,FldArea="divHead",TreePathFld="mother_depart_path",TreePathFromDb=false,
                   SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"order_index",new DataItem(){Title="顺序号",FldArea="divHead"}},
                {"brands_id",new DataItem(){FldArea="divHead",Title="负责品牌",DropDownWidth="80", LabelFld="brands_name",LabelInFormTable=true, Checkboxes=true,ButtonUsage="list",SqlForOptions=CommonTool.selectBrands}},
                {"managers_id",new DataItem(){FldArea="divHead",Title="负责人",DropDownWidth="80", LabelFld="managers_name",LabelInFormTable=true, Checkboxes=true,ButtonUsage="list",SqlForOptions=CommonTool.selectSellers}},

            }; 
            m_idFld = "depart_id"; m_nameFld = "depart_name"; 
            m_tableName = "info_department";
            m_selectFromSQL = "from info_department " +
                "left join (select depart_id as my_mother_id,depart_name as mother_name from info_department) tb_mother on info_department.mother_id=tb_mother.my_mother_id  " +
                //"left join (select string_agg(brand_name::text,',') allowbrands_name from info_item_brand where company_id= ~COMPANY_ID and INSTR(info_department.allowbrands_id, brand_id)>0) brands" +
                "where depart_id='~ID'";
        }
        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
       
     
    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class DepartEditController : BaseController
    { 
        public DepartEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic value)
        { 
           // object o = null;
            DepartEditModel model = new DepartEditModel(cmd);


            string mother_depart_path = value.mother_depart_path;
            string depart_id = value.depart_id;
            if (depart_id != "")
            {
                if (mother_depart_path.Contains("/" + depart_id + "/"))
                {
                    return new JsonResult(new { result = "Error", msg = "父类不能是该类自身或子类" });
                }
            }

            return await model.SaveTable(cmd, value);



            //  var tt = Convert.ToString(value.uid); 
            //var rr = new { UserID = value.UserID, UserName = value.UserName };
            //return value;

            //return JsonObject<object> (new { UserID = value.UserID, UserName = value.UserName });
        }
        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            DepartEditModel model = new DepartEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        } 

    }
}