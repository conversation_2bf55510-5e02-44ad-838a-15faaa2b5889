using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.POIFS.Crypt.Dsig;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using NPOI.SS.Formula.Functions;
using NPOI.XSSF.Streaming.Values;
using static System.Runtime.InteropServices.JavaScript.JSType;
using ArtisanManage.AppController;
namespace ArtisanManage.Pages.BaseInfo
{
	public class SalesDetailModel : PageQueryModel
	{
		public SalesDetailModel(CMySbCommand cmd) : base(Services.MenuId.salesDetaill)
		{
			this.UsePostMethod = true;
			this.EnableBigDataMode = true;
			this.cmd = cmd;
			this.PageTitle = "销售明细表";
			this.NotQueryHideColumn = true;
			this.CoalesceDataItems = new List<string> { "depart_id", "depart_id,supcust_id" };
			this.CanQueryByApproveTime = false;
			DataItems = new Dictionary<string, DataItem>()
			{
				{"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
				{"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
					JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
				}},
				{"queryTimeAccord",new DataItem(){
					FldArea="divHead", Title="时间类型", LabelFld = "time_status_name", ButtonUsage = "list",
					CompareOperator="=",Value="byHappenTime",Label="交易时间",ForQuery=false, AutoRemember=true,
					Source = @"[
                                {v:'byHappenTime',l:'交易时间'},
                                {v:'byMakeTime',l:'制单时间'},
                                {v:'byApproveTime',l:'审核时间'},
                                {v:'byCheckedTime',l:'交账时间'},
                                {v:'bySendTime',l:'送货时间'},
                                {v:'byClearArrearsTime',l:'欠款结清时间'}
                               ]"
				}},
				{"clearArrearsStartDay",new DataItem(){Title="结欠开始",FldArea="divHead",Hidden=true, CtrlType="jqxDateTimeInput",ForQuery=false, Value=""}},
				{"clearArrearsEndDay",new DataItem(){Title="结欠结束",FldArea="divHead", Hidden=true, CtrlType="jqxDateTimeInput",ForQuery=false, Value="",
					JSDealItemOnSelect=@"                        
                            var s=$('#clearArrearsEndDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                                s=s.replace('00:00','23:59');           
                                $('#clearArrearsEndDay').jqxDateTimeInput('val',s);
                            }
                    "
				}},
				{"forItemHistory",new DataItem(){FldArea="divHead",Title="forItemHistory",CtrlType="jqxCheckBox",ForQuery=false,Hidden=true,HideOnLoad=true}},
				//{"brand_id",new DataItem(){SqlAreaToPlace="DETAIL",Title="品牌",Checkboxes=true, FldArea="divHead",LabelFld="brand_name",ButtonUsage="list",CompareOperator="=",SqlFld="item_brand",
				//	SqlForOptions = CommonTool.selectBrands}},

				   {"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){SqlAreaToPlace="DETAIL", SqlFld="item_brand"})},


				{"group_id",new DataItem(){SqlAreaToPlace="MAIN",Title="渠道",Checkboxes=true, FldArea="divHead",LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sc.sup_group",
					SqlForOptions = CommonTool.selectGroups}},
				{"item_id",new DataItem(){SqlAreaToPlace="DETAIL",Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",SqlFld="sd.item_id",DropDownWidth="300",QueryByLabelLikeIfIdEmpty=true,
				   SearchFields=CommonTool.itemSearchFields,
				SqlForOptions =CommonTool.selectItemWithBarcode  }},
				{"supcust_id",new DataItem(){SqlAreaToPlace="MAIN", FldArea="divHead",Title="客户",LabelFld="sup_name", Checkboxes=true,ButtonUsage="event",QueryByLabelLikeIfIdEmpty=true, CompareOperator="=",SqlFld="sm.supcust_id",
					SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID "}},
				{"acct_cust_id",new DataItem(){SqlAreaToPlace="MAIN", Hidden=true, FldArea="divHead",Title="结算单位",LabelFld="acct_name", Checkboxes=true,ButtonUsage="event",QueryByLabelLikeIfIdEmpty=false, CompareOperator="=",SqlFld="acct_cust_id",
					SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID "}},

				{"other_region",new DataItem(){SqlAreaToPlace="MAIN",Title="片区",MumSelectable=true, FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500", TreePathFld="region_path",CompareOperator="like",
					SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region order by order_index , region_id",UseFlexLikeQuery=true,LikeString="like"
					/*DealQueryItem = (value) =>
					{
					
						return "/"+value+"/";
					}*/
				}},
				 {"other_class",new DataItem(){SqlAreaToPlace="DETAIL",Title="类别",MumSelectable=true, FldArea="divHead",LabelFld="class_name",MaxRecords="1000",CtrlType="jqxDropDownTree",DropDownHeight="200", TreePathFld="class_path",CompareOperator="like",
					SqlForOptions="select class_id as v,class_name as l,mother_id as pv from info_item_class order by order_index , class_id",
					DealQueryItem = (value) =>
					{
						return $"/{value}/".Replace("//","/");
					}
				}},
               /*{"depart_path",new DataItem(){Title="部门",Hidden=true, FldArea="divHead",LabelFld="depart_path_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},*/
                {"depart_id",new DataItem(){SqlAreaToPlace="MAIN",Title="部门",Hidden=true, FldArea="divHead",LabelFld="depart_path_label",SqlFld="depart_id", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="=",
					SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
				}},
				 {"department_id",new DataItem(){SqlAreaToPlace="MAIN",Title="所属部门",Hidden=true,TreePathFld="department_path", FldArea="divHead",LabelFld="department_id_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=false,DropDownWidth="150", CompareOperator="=",
					SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
				}},
				{"seller_id",new DataItem(){Title="业务员",Checkboxes=true,  FldArea="divHead",ButtonUsage="list",CompareOperator="=",SqlFld="coalesce(d_seller_id,sm.seller_id)", LabelFld="seller_name",
					SqlForOptions ="select oper_id as v,oper_name as l from info_operator where company_id=~COMPANY_ID and is_seller and (status=1 or status is null)"}},
                //{"branch_id",new DataItem(){SqlAreaToPlace="MAIN",Title="仓库",  FldArea="divHead",LabelFld="branch_name",Checkboxes=true,ButtonUsage="list",CompareOperator="=",SqlFld="sm.branch_id",
                //    SqlForOptions ="select branch_id as v,branch_name as l from info_branch"}},
               {"branch_id",new DataItem(){Title="仓库",FldArea = "divHead",LabelFld="branch_name",Checkboxes=true, ForQuery=false,ButtonUsage="list",CompareOperator="=",
				SqlForOptions=CommonTool.selectBranch } },

			{"branch_position",new DataItem(){Title="库位",FldArea="divHead",LabelFld="branch_position_name",Checkboxes=true,ForQuery=false,ButtonUsage="list",CompareOperator="=",
				SqlForOptions=CommonTool.selectBranchPosition }},
			{"produce_date",new DataItem(){Title="生产日期",FldArea="divHead",ForQuery=false, CtrlType="jqxDateTimeInput",ShowTime=false}},
			{"batch_no",new DataItem(){Title="批次",FldArea="divHead",ForQuery=false}},
                //{"group_id",new DataItem(){Title="渠道",Checkboxes=true, FldArea="divHead",LabelFld="branch_name",ButtonUsage="list",Hidden=false,CompareOperator="=",SqlFld="sc.sup_group",
                    //SqlForOptions ="select group_id as v,group_name as l from info_supcust_group where company_id=~COMPANY_ID"}},

                {"make_brief",new DataItem(){FldArea="divHead",SqlFld="sm.make_brief",SqlAreaToPlace="MAIN",Title="整单备注",CompareOperator="like"} },
				{"remark",new DataItem(){SqlAreaToPlace="DETAIL",Title="行备注",FldArea="divHead",EmptyConditionValue="-1",LabelFld="remark_name",ButtonUsage="list",ForQuery=false,CompareOperator="like",Checkboxes=true,SqlFld="remark||''",
					SqlForOptions ="select -1 as v,'无备注' as l union select brief_id as v,brief_text as l from info_sheet_detail_brief where sheet_type='X' order by v"}},
				{"sheet_no",new DataItem(){Title="单号", FldArea="divHead", SqlFld="sm.sheet_no", SqlAreaToPlace="MAIN",CompareOperator="like" }},
				{"arrears_status",new DataItem(){FldArea="divHead",Hidden=true, SqlAreaToPlace="MAIN",Title="欠款情况",Checkboxes=true,ButtonUsage = "list",CompareOperator="=",
					Source = @"[{v:'cleared',l:'已结清',condition:""abs(total_amount-paid_amount-disc_amount)<0.1""},
                                 {v:'uncleared',l:'未结清',condition:""abs(total_amount-paid_amount-disc_amount)>0.1""},
                                 {v:'all',l:'所有',condition:""true""}]"
				}},
				{"sale_way",new DataItem(){SqlAreaToPlace="MAIN", FldArea="divHead",  Title="销售方式",LabelFld="sale_way_label", ButtonUsage = "list" ,Hidden=true, CompareOperator="=",Value="",Label="",
						Source = @"[{v:'all',l:'所有',condition:""1=1""},
                                   {v:'directSale',l:'车销',condition:""order_sheet_id is null""},
                                   {v:'byOrder',l:'访销',condition:""order_sheet_id is not null""}]"

				}},
				{"change_price",new DataItem(){SqlAreaToPlace="DETAIL",FldArea="divHead",   Title="变价",LabelFld="change_price_label", ButtonUsage = "list" ,Hidden=true, CompareOperator="=",Value="",Label="",
						Source = @"[{v:'all',l:'所有',condition:""1=1""},
                                   {v:'dowm',l:'变低',condition:""sys_price > real_price""},
                                   {v:'nochange',l:'不变',condition:""sys_price = real_price""},
                                   {v:'up',l:'变高',condition:""orig_price < real_price""}]"

				}},
				{"row_disc_amt",new DataItem(){SqlAreaToPlace="DETAIL",FldArea="divHead",   Title="单品优惠",LabelFld="row_disc_amt_label", ButtonUsage = "list" ,Hidden=true, CompareOperator="=",Value="",Label="",
						Source = @"[{v:'all',l:'所有',condition:""1=1""},
                                   {v:'dowm',l:'变低',condition:""orig_price > real_price""},
                                   {v:'nochange',l:'不变',condition:""orig_price = real_price""},
                                   {v:'up',l:'变高',condition:""orig_price < real_price""}]"

				}},


				{"disc_status",new DataItem(){FldArea="divHead", SqlAreaToPlace="MAIN",Hidden=true, Title="整单优惠",LabelFld = "disc_status_name",ButtonUsage = "list",CompareOperator="=",
					Source = @"[{v:'all',l:'所有',condition:""true""},
                                 {v:'yes',l:'有',condition:""abs(now_disc_amount)>=0.01""},
                                 {v:'no',l:'无',condition:""abs(now_disc_amount)<0.01""}]"
				}},
				{"transfer_status",new DataItem(){FldArea="divHead",Hidden=true, SqlAreaToPlace="MAIN",Title="转单情况",ButtonUsage = "list",CompareOperator="=",
						Source = @"[{v:'all',l:'所有',condition:""true""},
                                    {v:'transfered',l:'已转单',condition:""oss.order_status = 'zd' and oss.sale_sheet_id is not null""},
                                    {v:'untransfered',l:'未转单',condition:""COALESCE(oss.order_status,'xd') <>'zd' and oss.sale_sheet_id is null  ""}]"
				}},
				{"move_to_van",new DataItem(){FldArea="divHead",Hidden=true, SqlAreaToPlace="MAIN",Title="装车情况",ButtonUsage = "list",CompareOperator="=",
						Source = @"[{v:'all',l:'所有',condition:""true""},
                                    {v:'toVan',l:'已装车',condition:""oss.order_status ='zc'""},
                                    {v:'unToVan',l:'未装车',condition:""(COALESCE(oss.order_status,'xd')in ('dd','xd'))""}]"
				}},
				{"move_stock",new DataItem(){FldArea="divHead",Hidden=true, SqlAreaToPlace="MAIN",Title="库存情况",LabelFld="move_stock_name",ButtonUsage = "list",CompareOperator="=",
						Source = @"[{v:'all',l:'所有',condition:""true""},
                                    {v:'move_t',l:'转移库存',condition:""move_stock = 't'""},
                                    {v:'move_f',l:'未转移库存',condition:""(COALESCE(move_stock,'f')='f' or oss.order_status ='pzc')""}]"
				}},
				{"status",new DataItem(){FldArea="divHead", SqlAreaToPlace="MAIN",Title="单据状态",Checkboxes=true,ButtonUsage = "list",CompareOperator="=",Value="approved",Label="已审核",
						Source = @"[{v:'normal',l:'所有',condition:""sm.red_flag is null""},
                                   {v:'unapproved',l:'未审核',condition:""sm.approve_time is null""},
                                   {v:'approved',l:'已审核',condition:""sm.approve_time is not null and sm.red_flag is null""}]"

				}},

				{"trade_type",new DataItem(){SqlAreaToPlace="DETAIL",FldArea="divHead",Checkboxes=true, Title="交易类型",ButtonUsage = "list",CompareOperator="=",Value="",Label="",
						Source = @"[{v:'ALL',l:'所有',condition:""true""},
                                    {v:'X',l:'销售',condition:""coalesce(trade_type,'X')='X' and sd.quantity*sd.inout_flag<0""},
                                   {v:'T',l:'退货',condition:""coalesce(trade_type,'X') in ('T','TD','X') and sd.quantity*sd.inout_flag>0""},
                                   {v:'XT',l:'销退',condition:""coalesce(trade_type,'X') in ('X','T','XD','TD')""},
                                   {v:'DH',l:'定货还货',condition:""trade_type='DH'""},
                                   {v:'JH',l:'借还货',condition:""trade_type in ('J','H')""},
                                   {v:'CL',l:'陈列兑付',condition:""trade_type ='CL'""},
                                   {v:'HH',l:'换货',condition:""trade_type in ('HR','HC')""}]"
				}},

				 {"row_disc_status",new DataItem(){SqlAreaToPlace="DETAIL",FldArea="divHead",Title="折扣情况",Hidden=true, LabelFld = "row_disc_status_name",ButtonUsage = "list",CompareOperator="=",
						Source = @"[{v:'all',l:'所有',condition:""true""},
                                    {v:'yes',l:'有',condition:""round((orig_price-real_price)::numeric,2)<>0 ""},
                                   {v:'no',l:'无',condition:""round((orig_price-real_price)::numeric,2)=0""}]"
				}},

				{"sn_code",new DataItem(){SqlAreaToPlace="DETAIL",Title="序列号",FldArea="divHead",Hidden=true, TextAsValue=true,CompareOperator="like"}},
				{"item_no",new DataItem(){SqlAreaToPlace="DETAIL",Title="编号",FldArea="divHead",Hidden=true, CompareOperator="like"}},

				{"item_spec",new DataItem(){SqlAreaToPlace="DETAIL",Title="规格",FldArea="divHead",Hidden=true, CompareOperator="like"}},
				{"isConfinement",new DataItem(){SqlAreaToPlace="DETAIL",Title="产期",FldArea="divHead",Hidden=true, ForQuery = false}},

                //{"make_brief",new DataItem(){FldArea="divHead",SqlFld="sm.make_brief", SqlAreaToPlace="MAIN",Title="整单备注",CompareOperator="ilike"} },
                {"real_price_min",new DataItem(){SqlAreaToPlace="DETAIL",FldArea="divHead",Hidden=true, Title="单价&ge;",SqlFld = "real_price",CompareOperator=">="}},
				{"real_price_max",new DataItem(){SqlAreaToPlace="DETAIL",FldArea="divHead",Hidden=true, Title="单价&le;",SqlFld = "real_price",CompareOperator="<="}},
				{"b_price_min",new DataItem(){SqlAreaToPlace="DETAIL",FldArea="divHead",Hidden=true, Title="整件价&ge;",SqlFld = "real_price/sd.unit_factor * b_unit_factor",CompareOperator=">="}},
				{"b_price_max",new DataItem(){SqlAreaToPlace="DETAIL",FldArea="divHead",Hidden=true, Title="整件价&le;",SqlFld = "real_price/sd.unit_factor * b_unit_factor",CompareOperator="<="}},
				{"s_price_min",new DataItem(){SqlAreaToPlace="DETAIL",FldArea="divHead",Hidden=true, Title="小单位价&ge;",SqlFld = "real_price/sd.unit_factor",CompareOperator=">="}},
				{"s_price_max",new DataItem(){SqlAreaToPlace="DETAIL",FldArea="divHead",Hidden=true, Title="小单位价&le;",SqlFld = "real_price/sd.unit_factor",CompareOperator="<="}},

				{"saleWay",new DataItem(){SqlAreaToPlace="DETAIL",FldArea="divHead",Hidden=true, Title="销退赠", LabelFld="sale_ways",ButtonUsage="list",Value="all",Label="所有",
					Source = @"[{v:'sale',l:'销售',condition:""(quantity*inout_flag<0 and sub_amount != 0)""},
                                {v:'return',l:'退货',condition:""(quantity*inout_flag>0 and sub_amount != 0)""},
                                {v:'free',l:'赠品',condition:""(quantity*inout_flag!=0 and sub_amount = 0)""},
                                {v:'all',l:'所有',condition:""true""}]",

					CompareOperator="=",Checkboxes=true } },
				 {
					"senders_id",
					new DataItem()
					{
						FldArea = "divHead",SqlFld="','||sm.senders_id||','", SqlAreaToPlace="MAIN", Title = "送货员",Checkboxes = true, LabelFld = "sm.senders_name", ButtonUsage = "list",
						DealQueryItem = status => ","+status+",", Hidden=true,
						SqlForOptions=CommonTool.selectSenders,  //SqlForOptions = "select oper_id as v,oper_name as l,py_str as z from info_operator",
                        CompareOperator = "like"
					}
				},
				{"disp_sheet_no",new DataItem(){FldArea="divHead",Title="陈列协议单",Hidden=true,HideOnLoad=true, CompareOperator="like",SqlFld="da.sheet_no" } },
				{"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'2',l:'加权平均成本'},{v:'3',l:'预设进价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价'}]", CompareOperator="=" }},
				{"price_filter_type",new DataItem(){FldArea="divHead",Title="单价＜",ForQuery=false,LabelFld="price_filter_type_name",ButtonUsage="list",Source = "[{v:'belowLastSellingPrice',l:'上次售价'},{v:'belowWholesalePrice',l:'批发价'}]", CompareOperator="<" }},
				{"sheetType",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,HideOnLoad = true} },
				{"sys_hide_duplicate_cells",new DataItem(){
					FldArea="divHead", Title="主表展示1行", LabelFld = "", CtrlType="jqxCheckBox",Hidden=true,
					 Value="false",Label="",ForQuery=false, AutoRemember=true

				}},
				{"import_type", new DataItem(){FldArea="divHead", Title="导入类型", Hidden=true, ButtonUsage="list", CompareOperator="=",Checkboxes=true,
                    Source = @"[{v:'sale',l:'实际开单',condition:"" coalesce(sm.is_imported,false)=false and sm.sheet_attribute->>'imported' is null ""},
                                {v:'import',l:'导入且不影响库存',condition:"" coalesce(sm.is_imported,false)=true ""},
                                {v:'attr_import',l:'导入且影响库存',condition:"" (coalesce(sm.is_imported,false)=false and sm.sheet_attribute->>'imported' ='1') ""},
                                {v:'all',l:'所有',condition:""true""}]",
                }}
             
                //{"detail_table",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,} }
            
            };

			Grids = new Dictionary<string, QueryGrid>()
			{
				{
				  "gridItems",  new QueryGrid()
				  {
					  ShowAggregates = true,
					  Sortable=true,
					  PageByOverAgg=false,
					 Columns = new Dictionary<string, DataItem>()
					 {
					   {"sheet_id",new DataItem(){IsIDColumn = true,SqlFld = "sm.sheet_id",Hidden=true,HideOnLoad = true}},
					   {"row_index",new DataItem(){IsIDColumn = false,SqlFld = "sd.row_index",Hidden=true,HideOnLoad = true}},
                     //  {"placeholder_sheet_id",new DataItem(){SqlFld="placeholder_sheet_id",Hidden=true,HideOnLoad = true}},
                       {"sheet_no",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="单据编号", Sortable=true,    Width="100",SqlFld="sm.sheet_no" ,Linkable = true}},
                     //  {"placeholder_sheet_no",     new DataItem(){Title="占位单编号",Hidden=true,HideOnLoad = true, Sortable=true,    Width="100",SqlFld="placeholder_sheet_no" ,Linkable = true}},
                       {"sheet_type1",   new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="单据类型",     Width="80",SqlFld="(case WHEN sm.sheet_type='X' THEN '销售单' when sm.sheet_type='T' then '退货单' when sm.sheet_type='XD' then '销售订单' when sm.sheet_type='TD' then '退货订单' END)"}},
					   {"sheet_type",   new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="单据类型", Hidden=true,    Width="80",SqlFld="",HideOnLoad = true,SelectFieldEvenHidden=true}},
					   {"make_time",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="制单时间", Sortable=true, Width="150",SqlFld="sm.make_time"}},
					   {"happen_time",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="交易时间", Sortable=true, Width="150",SqlFld="sm.happen_time"}},
					   {"approve_time",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="审核时间", Sortable=true, Width="150",SqlFld="sm.approve_time"}},
					   {"sup_name",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="客户", Sortable=true,       Width="150",SqlFld="sup_name"}},
					   {"mobile",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="客户电话",  Sortable=true,       Width="90",SqlFld="mobile",Hidden = true}},
					   {"sup_addr",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="客户地址",         Width="150",SqlFld="sup_addr",Hidden = true}},
					   {"boss_name",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="老板姓名",         Width="150",SqlFld="boss_name",Hidden = true}},
					   {"trade_type", new DataItem(){Title="交易类型",Hidden=true, Width="70",SqlFld="case when sm.sheet_type='T' then '退货' else case coalesce(trade_type,'X') when 'J' then '借货' when 'H' then '还货' when 'DH' then '定货会' when 'CL' then '陈列' when 'HR' then '换入' when 'HC' then '换出' when 'T' then '退货' else '销售' end end"}},
                      
                       //{"supcust_no",     new DataItem(){Title="客户编号", Sortable=true,       Width="100",Hidden=true}},
					   {"region_name",     new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="片区",      Width="100",SqlFld="region_name",Hidden = true}},
					   {"oper_name",    new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="业务员",     Width="70",SqlFld="coalesce(d_seller_name,oper_name)"}},
                       {"senders_name",    new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="送货员",      Width="70",SqlFld="sm.senders_name"}},
					   {"maker_name", new DataItem(){MightDuplicate=true,HideDuplicateCells=true, Title="制单人",SqlFld ="sm.maker_name",  Width="80",Sortable=true}},
					   {"branch_name",    new DataItem(){Title="仓库",Sortable=true, Width="70",SqlFld="branch_name",Hidden=true}},
					   {"total_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true,  ShowSum=true, Title="总额",SqlFld="sm.total_amount * sm.money_inout_flag" , CellsAlign="right",Sortable=true, Width="70", Hidden=true}},
					   {"paid_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="已收金额", SqlFld="sm.paid_amount * sm.money_inout_flag" , CellsAlign="right",Sortable=true, Width="70",Hidden=true}},
					   {"disc_amount",    new DataItem(){ MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="已惠金额", SqlFld="sm.disc_amount::numeric * sm.money_inout_flag" , CellsAlign="right",Sortable=true, Width="70",Hidden=true}},
					   {"left_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="尚欠金额", SqlFld="(sm.total_amount -sm.paid_amount - sm.disc_amount) * sm.money_inout_flag" ,CellsAlign="right", Sortable=true, Width="70",Hidden=true}},
					   {"now_pay_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="本单收款", SqlFld="now_pay_amount * sm.money_inout_flag" ,CellsAlign="right", Sortable=true, Width="70",Hidden=true}},
					   {"now_disc_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="本单优惠", SqlFld="now_disc_amount::numeric * sm.money_inout_flag" ,CellsAlign="right", Sortable=true, Width="70",Hidden=true}},
					   {"now_left_amount",    new DataItem(){ MightDuplicate=true, HideDuplicateCells=true,ShowSum=true, Title="本单欠款", SqlFld="(sm.total_amount -sm.now_pay_amount - sm.now_disc_amount) * sm.money_inout_flag" ,CellsAlign="right", Sortable=true, Width="70",Hidden=true}},
					   {"later_paid_amount",    new DataItem(){MightDuplicate=true, HideDuplicateCells=true, ShowSum=true, Title="后续收款", SqlFld="(sm.paid_amount -sm.now_pay_amount) * sm.money_inout_flag" ,CellsAlign="right", Sortable=true, Width="70",Hidden=true}},

					   {"item_name",    new DataItem(){Title="商品名称",  Sortable=true, Width="200",SqlFld="sd.item_name"}},
					   {"rebate_price",    new DataItem(){Title="补差价",  Sortable=true, Width="200",SqlFld="rebate_price",GetFromDb=false,Hidden=true,HideOnLoad=true}},
					   {"unit_conv",    new DataItem(){Title="单位换算",    Width="70",SqlFld="yj_get_unit_relation(b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},
                       {"item_class",    new DataItem(){Title="类别",    Width="70",SqlFld="sd.other_class_name",Hidden = true}},
					   {"brand_name",    new DataItem(){Title="品牌",  Sortable=true, Width="200",SqlFld="sd.brand_name"}},
					   {"barcode",new DataItem(){Title = "条码",Width="90" ,SqlFld="itu.barcode",Hidden=true} },
					   {"s_barcode",new DataItem(){Title = "条码(小)",Width="90"} },
					   {"m_barcode",new DataItem(){Title = "条码(中)",Width="90",Hidden=true} },
					   {"b_barcode",new DataItem(){Title = "条码(大)",Width="90",Hidden=true} },
					   {"qty_no_unit",     new DataItem() {Title="纯数量", Width="70",Hidden=true,ShowSum=true,

						SqlFld=" sd.quantity * sd.inout_flag *(-1) ",CellsAlign="right",
						FuncDealMe=(value)=>{return value=="0"?"":value; },
					   }},
					   {"unit_no",     new DataItem() {Title="单位",Hidden=true, Width="70",SqlFld=" sd.unit_no ",CellsAlign="right",}},
					   {"quantity",     new DataItem() {Title="数量", Sortable=true, Width="70",
					   Tooltip="headertooltip",
						   SqlFld="concat((case when sm.sheet_type in ('X','XD') then sd.quantity else -sd.quantity end),sd.unit_no)",CellsAlign="right",
						   FuncDealMe=(value)=>{return value=="0"?"":value; },
						   RelyColumns="quantity_b,quantity_m,quantity_s",
						   FuncGetSumValue = (sumColumnValues) =>
						   {
							   string sQty ="";
							   if(sumColumnValues["quantity_b"]!="") sQty+= sumColumnValues["quantity_b"]+"大";
							   if(sumColumnValues["quantity_m"]!="") sQty+= sumColumnValues["quantity_m"]+"中";
							   if(sumColumnValues["quantity_s"]!="") sQty+= sumColumnValues["quantity_s"]+"小";
							   return sQty;
						   }
					   }},

					  {"unit_type",   new DataItem(){Title="大小", Hidden=true,HideOnLoad = true,Sortable=true,CellsAlign="right", Width="80",SqlFld="(case when itu.unit_type ='s'  then '小' else (case when itu.unit_type ='b' then '大' else '中' end )  end)"}},
					  {"quantity_b",   new DataItem(){Title="大数", Hidden=true,HideOnLoad = true,ShowSum=true,Sortable=true, CellsAlign="right", Width="80",
						  SqlFld="(case when itu.unit_type ='b' then(case when sm.sheet_type in ('X','XD')    then sd.quantity else -sd.quantity end) else null end)" }},
					  {"quantity_m",   new DataItem(){Title="中数",Hidden=true, HideOnLoad = true, ShowSum=true,Sortable=true, CellsAlign="right", Width="80",
						SqlFld="(case when itu.unit_type ='m' then(case when sm.sheet_type in ('X','XD')    then sd.quantity else -sd.quantity end) else null end)"}},
					  {"quantity_s",   new DataItem(){Title="小数", Hidden=true, HideOnLoad = true,ShowSum=true,Sortable=true, CellsAlign="right", Width="80",
						  SqlFld="(case when itu.unit_type ='s' then(case when sm.sheet_type in ('X','XD') then sd.quantity else -sd.quantity end) else null end)"
					  }},
					  {"b_pure_qty",   new DataItem(){Title="大单位数", Hidden=true, ShowSum=true,Sortable=true, CellsAlign="right", Width="50",
						  SqlFld="(case when b_unit_factor>0  then round((sd.quantity * sd.unit_factor * sd.inout_flag * (-1) /b_unit_factor)::numeric,3) else 0 end)"
					  }},
					  {"b_unit_no",     new DataItem() {Title="大单位",Hidden=true, Width="60", CellsAlign="right",}},

					  {"s_pure_qty",   new DataItem(){Title="小单位数", Hidden=true, ShowSum=true,Sortable=true, CellsAlign="right", Width="50",
						  SqlFld="round((sd.quantity * sd.unit_factor * sd.inout_flag * (-1))::numeric,3)"
					  }},
					  {"s_unit_no",     new DataItem() {Title="小单位",Hidden=true, Width="60", CellsAlign="right",}},

					  {"senders_qty", new DataItem(){Title="送货员数量",  CellsAlign="center",  Width="100",SqlFld="array_length(string_to_array(sm.senders_id,','),1)",
							FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=false, Hidden=true,
					  }},
					  {"net_quantity_per_sender_b", new DataItem(){Title="送货员人均数量(大单位)",  CellsAlign="center",  Width="100",SqlFld="round((sd.quantity*sd.unit_factor*sd.inout_flag*(-1)/array_length(string_to_array(sm.senders_id,','),1)/b_unit_factor)::numeric,2)",
							FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
					  }},
                       //{"type",   new DataItem(){Title="大小", Hidden=true,HideOnLoad = true,Sortable=true,CellsAlign="right", Width="7%",SqlFld="(case when itu.unit_type ='s'  then '小' else (case when itu.unit_type ='b' then '中' else '大' end )  end)"}},
                     
                       {"real_price", new DataItem(){Title="单价", Sortable=true, CellsAlign="right", Width="80",SqlFld="sd.real_price"}},
					   {"orig_price", new DataItem(){Title="原价", Sortable=true, CellsAlign="right", Width="80",SqlFld="sd.orig_price"}},
					   {"sum_amount", new DataItem(){Title = "金额", Sortable=true,CellsAlign = "right",Width = "80",SqlFld = " (case when sm.sheet_type in ('X' ,'XD') then (sd.sub_amount) else (-sd.sub_amount) end )  ",ShowSum = true}},

					   {"cost_amount_hasfree",  new DataItem(){ProfitRelated=true, Title="销售成本（含赠）", CellsAlign="right",   Width="150", Sortable=true,ShowSum=true,
					   FuncDealMe=(value)=>{
						   if (string.IsNullOrEmpty(value))
						   {
							   return "0";
						   }
							return Convert.ToString(Math.Round(CPubVars.ToDecimal(value),2));
					   }

					   }},
					   {"profit_hasfree",new DataItem(){ProfitRelated=true,Title = "销售利润（含赠）", Sortable=true,CellsAlign = "right",Width = "150",ShowSum = true}},
					   {"profit_rate_hasfree",new DataItem(){ProfitRelated=true,Title = "利润率(%)（含赠）", Sortable=true,CellsAlign = "right",Width = "150",ShowAvg = true,
						   RelyColumns="profit_hasfree,sum_amount",
						   FuncGetSumValue = (sumColumnValues) =>
						   {

							   string s_profit_hasfree =sumColumnValues["profit_hasfree"];
							   string s_net_amount =sumColumnValues["sum_amount"];

							   double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
							   double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
							   string rate="";
							   if (net_amount != 0)
							   {
								   rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
							   }
							   return rate;
						   }
					   }},
					   {"cost_amount_hasfree_cl_jh",  new DataItem(){ProfitRelated=true,Title="销售成本（含赠|陈列|借还）", CellsAlign="right",    Width="200", Sortable=true,ShowSum=true}},
					   {"profit_hasfree_cl_jh",new DataItem(){ProfitRelated=true,Title = "销售利润（含赠|陈列|借还）", Sortable=true,CellsAlign = "right",Width = "200",ShowSum = true}},
					   {"profit_rate_hasfree_cl_jh",new DataItem(){ProfitRelated=true,Title = "利润率(%)（含赠|陈列|借还）", Sortable=true,CellsAlign = "right",Width = "200",ShowAvg = true,
						   RelyColumns="profit_hasfree_cl_jh,sum_amount",
						   FuncGetSumValue = (sumColumnValues) =>
						   {
							   string s_profit_hasfree_cl_jh =sumColumnValues["profit_hasfree_cl_jh"];
							   string s_net_amount =sumColumnValues["sum_amount"];

							   double profit_hasfree_cl_jh=s_profit_hasfree_cl_jh!=""?Convert.ToDouble(s_profit_hasfree_cl_jh) : 0.0;
							   double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
							   string rate="";
							   if (net_amount != 0)
							   {
								   rate=CPubVars.FormatMoney(profit_hasfree_cl_jh/net_amount*100,1);
							   }
							   return rate;
						   }
					   }},
					   {"free_cost_amount",new DataItem(){ProfitRelated=true,Title="赠品成本",CellsAlign="right",Width="100",SqlFld="",ShowSum=true,Hidden=true,FuncDealMe=(value)=>{return value=="0"?"":value; }, } },
					   {"cost_amount",  new DataItem(){ProfitRelated=true,Title="销售成本", CellsAlign="right",    Width="100", Sortable=true,ShowSum=true}},
					   {"cost_price",  new DataItem(){ProfitRelated=true,Title="成本价", CellsAlign="right",    Width="100",Hidden=true, Sortable=true,ShowSum=true}},
					   {"wholesale_price",new DataItem(){Title="批发价", Width="60",Sortable=true,
							SubColumns=new Dictionary<string,DataItem>()
							{
								{"b_wholesale_price",new DataItem(){Title = "大",InnerTitle="批发价大",OrigTitle="批发价大",Width = "70",CellsAlign="right",Sortable=true,SqlFld="b_wholesale_price::numeric"}},
								{"m_wholesale_price",new DataItem(){Title="中",Width="70",CellsAlign="right"} },
								{"s_wholesale_price",new DataItem(){Title="小",Width="70",CellsAlign="right",Sortable=true,SqlFld="s_wholesale_price::numeric"} },
							}
					   }},
						 {"cost_price_detail",new DataItem(){Title="成本单价", Width="60",Sortable=true,ProfitRelated=true,
							SubColumns=new Dictionary<string,DataItem>()
							{
								{"b_cost_price",new DataItem(){ Title = "大", ProfitRelated=true,InnerTitle="成本价大",OrigTitle="成本价大",Width = "70",CellsAlign="right",Sortable=true}},
								{"m_cost_price",new DataItem(){ Title="中", ProfitRelated=true,Width="70",CellsAlign="right"} },
								{"s_cost_price",new DataItem(){ Title="小", ProfitRelated=true,Width="70",CellsAlign="right",Sortable=true} },
							}
					   }},
					   {"supplier_fee_cost_amount",new DataItem(){ProfitRelated=true,Title="厂家费用成本",CellsAlign="right",Width="100",SqlFld="",ShowSum=true,Hidden=true,FuncDealMe=(value)=>{return value=="0"?"":value; }, } },

					   {"profit",new DataItem(){ProfitRelated=true,Title = "销售利润", Sortable=true,CellsAlign = "right",Width = "100",ShowSum = true}},
					   {"profit_rate",new DataItem(){ProfitRelated=true,Title = "利润率(%)", Sortable=true,CellsAlign = "right",Width = "100",ShowAvg = true,
						RelyColumns="profit,sum_amount",
						   FuncGetSumValue = (sumColumnValues) =>
						   {
							   string s_profit_hasfree =sumColumnValues["profit"];
							   string s_net_amount =sumColumnValues["sum_amount"];

							   double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
							   double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
							   string rate="";
							   if (net_amount != 0)
							   {
								   rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
							   }
							   return rate;
						   }
					   }},

					   {"row_disc_amount", new DataItem(){Title="折扣金额",SqlFld="case when sd.trade_type !='J' then  round(((orig_price-real_price)*money_inout_flag*quantity)::numeric,2) else 0 end", Width="90",CellsAlign="right",ShowSum=true}},

					   {"sn_code", new DataItem(){Title="序列号", Sortable=true,Width="200"} },
					   {"make_brief",new DataItem(){Title="整单备注", Sortable=true,Width="100",JSCellRender="briefRender"} },
					   {"remark",   new DataItem(){Title="商品备注", Sortable=true, CellsAlign="left", Width="100",SqlFld="sd.remark",JSCellRender="remarkRender"}} ,
					   {"item_no",new DataItem(){Title="商品编号", Hidden=true, Width="100",}},
					   {"produce_date",new DataItem(){Title="生产日期",Hidden=true, Width="100",}},
					   {"valid_days",new DataItem(){Title="保质期",Hidden = true,Width="150"}},
					   {"item_spec",new DataItem(){Title="规格",Hidden=true, Width="100",}},
					   {"s_unit_price",   new DataItem(){Title="小单位价格", Hidden=true,Sortable=true, CellsAlign="right", Width="100",
						  SqlFld="case when (sd.unit_factor >0) then (real_price / sd.unit_factor)  else 0   end"
					  }},
                       
                       //{"cost_price_avg",   new DataItem(){Title="成本价", CellsAlign="right", Width="10%",SqlFld="sd.cost_price",ShowSum=true}},
                     },
					 QueryFromSQL=@"
FROM ~VAR_detail_table sd
LEFT JOIN 
( 
    select sm.*,region_name, maker_name,oper_name,sup_name ,sc.mobile ,sup_addr,boss_name ~VAR_other_field
    from ~VAR_main_table  sm
   ~VAR_other_leftJoin
    LEFT JOIN info_supcust sc ON sm.supcust_id = sc.supcust_id and sc.company_id =~COMPANY_ID 
    left join info_region re on re.region_id = sc.region_id and re.company_id = ~COMPANY_ID  
    ~VAR_STATUS_ORDER
    LEFT JOIN (select oper_id, oper_name as maker_name, depart_path from info_operator where company_id= ~COMPANY_ID ) maker on sm.maker_id = maker.oper_id
    LEFT JOIN info_operator io on io.oper_id = sm.seller_id and io.company_id = ~COMPANY_ID
    where sm.company_id=~COMPANY_ID and red_flag is null ~VAR_IS_DEL_CONDI and ~VAR_MAIN_HAPPEN_TIME_LIMIT and ~VAR_happen_time >= '~VAR_startDay' AND ~VAR_happen_time <='~VAR_endDay' ~AREA_MAIN

) sm ON sd.sheet_id = sm.sheet_id
LEFT JOIN info_item_multi_unit itu on sd.unit_no = itu.unit_no and itu.company_id = ~COMPANY_ID and sd.item_id = itu.item_id
~VAR_d_oper_name
LEFT JOIN 
(
    SELECT
  item_id,
                                 MAX(unit_no) FILTER (WHERE unit_type = 's') AS s_unit_no,
                                 MAX(barcode) FILTER (WHERE unit_type = 's') AS s_barcode,
                                 1 AS s_unit_factor,
                                 MAX(wholesale_price) FILTER (WHERE unit_type = 's') AS s_wholesale_price,
                                 MAX(retail_price) FILTER (WHERE unit_type = 's') AS s_retail_price,
                                 MAX(unit_no) FILTER (WHERE unit_type = 'b') AS b_unit_no,
                                 MAX(barcode) FILTER (WHERE unit_type = 'b') AS b_barcode,
                                 MAX(unit_factor) FILTER (WHERE unit_type = 'b') AS b_unit_factor,
                                 MAX(wholesale_price) FILTER (WHERE unit_type = 'b') AS b_wholesale_price,
                                 MAX(unit_no) FILTER (WHERE unit_type = 'm') AS m_unit_no,
                                 MAX(barcode) FILTER (WHERE unit_type = 'm') AS m_barcode,
                                 MAX(unit_factor) FILTER (WHERE unit_type = 'm') AS m_unit_factor,
                                 MAX(wholesale_price) FILTER (WHERE unit_type = 'm') AS m_wholesale_price
FROM info_item_multi_unit
WHERE company_id = ~COMPANY_ID
GROUP BY item_id
) d ON sd.item_id=d.item_id

left join 
(
    select sheet_id,sheet_no from display_agreement_main where company_id=~COMPANY_ID
) da on sd.disp_sheet_id = da.sheet_id 

left join info_item_batch itb on itb.batch_id = sd.batch_id_t and itb.company_id = ~COMPANY_ID
left join info_branch_position ibp on ibp.branch_position = sd.branch_position_t and ibp.company_id = ~COMPANY_ID

left join info_branch ib on coalesce(sd.branch_id_t,sm.branch_id)=ib.branch_id and ib.company_id = ~COMPANY_ID
where sm.sheet_id is not null  ~VAR_branch_id ~VAR_branch_position ~VAR_produce_date ~VAR_NO_PODUCEDATE ~VAR_batch_no ~VAR_remark ~VAR_price_filter",
                      //这个地方很困惑，指定客户筛选查询时很慢，) d on sd.item_id = d.item_id 后加了 and sd.company_id=~COMPANY_ID 速度就快了

                      
                     //LEFT JOIN info_item_prop ip on ip.item_id = sd.item_id and ip.company_id=~COMPANY_ID

					  QueryOrderSQL=" order by sd.happen_time desc,sd.sheet_id,sd.row_index"
				  }
				}
			};
		}

		public async Task OnGet(string sheetType)
		{


			await InitGet(cmd);


			//m_classTreeStr =ClassEditModel.getClassTreeStr(); 

			//string sheetType = DataItems["sheetType"].Value;

		}

		public override async Task OnQueryConditionGot(CMySbCommand cmd)
		{
			var cost_price_type = DataItems["cost_price_type"].Value;

			// 价格过滤
			var price_filter_type = DataItems["price_filter_type"].Value;
			Console.WriteLine($"price_filter_type: {price_filter_type}");

			if (price_filter_type == "belowLastSellingPrice")
			{
				SQLVariables["price_filter"] = " and sd.real_price < sd.last_time_price";
			}
			else if (price_filter_type == "belowWholesalePrice")
			{
				SQLVariables["price_filter"] = " and sd.real_price < itu.wholesale_price";
			}
			else
			{
				Console.WriteLine("Invalid price_filter_type: " + price_filter_type);
				SQLVariables["price_filter"] = "and true"; // 默认不过滤
			}

			var queryTimeAccord = DataItems["queryTimeAccord"].Value;
			var startDay = DataItems["startDay"].Value;
			var endDay = DataItems["endDay"].Value;

			if (endDay.IsValid() && !endDay.Contains(":")) endDay += " 23:59:59";
			DataItems["endDay"].Value = endDay;

			
			SQLVariables["detail_happen_time"] = "sd.happen_time";
			SQLVariables["STATUS_ORDER"] = "";

			// 处理欠款结清时间筛选条件
			var clearArrearsStartDay = DataItems["clearArrearsStartDay"].Value;
			var clearArrearsEndDay = DataItems["clearArrearsEndDay"].Value;

			string clearArrearsCondition = "";
			if (clearArrearsStartDay.IsValid() || clearArrearsEndDay.IsValid())
			{ 

				if (clearArrearsStartDay.IsValid())
					clearArrearsCondition += $" and sm.settle_time >= '{clearArrearsStartDay}'";

				if (clearArrearsEndDay.IsValid())
				{
					if (!clearArrearsEndDay.Contains(":"))
						clearArrearsEndDay += " 23:59:59";

					DataItems["clearArrearsEndDay"].Value = clearArrearsEndDay;
					clearArrearsCondition += $" and sm.settle_time <= '{clearArrearsEndDay}'";
				}

			}


			string sheetType = DataItems["sheetType"].Value;
			var main_table = "sheet_sale_main";
			var other_leftJoin = "";
			var other_field = " , sm.senders_name as real_senders_name";
			var other_condi = "sm.sheet_id";
			var detail_table = $@"
( 
SELECT ~VAR_select_items,sn_code,sd.branch_id branch_id_t,COALESCE(sd.branch_position,0) as branch_position_t,COALESCE(sd.batch_id,0) as batch_id_t,  case when (sd.unit_factor >0) then (real_price / sd.unit_factor)  else 0   end as s_unit_price,d_seller_id
FROM sheet_sale_detail sd
left join info_item_prop ip on sd.item_id=ip.item_id and ip.company_id=~COMPANY_ID
left join info_sheet_detail_brief sb on sb.brief_text=sd.remark and sb.company_id=~COMPANY_ID
left join info_item_brand iib on iib.company_id = ~COMPANY_ID and iib.brand_id = ip.item_brand
where sd.company_id=~COMPANY_ID and  ~VAR_DETAIL_HAPPEN_TIME_LIMIT  ~AREA_DETAIL
)";

			SQLVariables["IS_DEL_CONDI"] = "";
			SQLVariables["d_oper_name"] = "left join(select oper_id, oper_name as d_seller_name from info_operator where company_id = ~COMPANY_ID ) dsell on sd.d_seller_id = dsell.oper_id";

            if (sheetType == "xd")
			{
				main_table = "sheet_sale_order_main";
				//  other_leftJoin = "  left join  sheet_placeholder_order_main som on som.company_id = sm.company_id and  sm.sheet_id= som.sale_order_sheet_id ";
				//    other_field = "  ,som.sheet_id placeholder_order_id ";
				//    other_condi = " coalesce(sm.placeholder_order_id,sm.sheet_id) ";
				detail_table = $@"
(
select ~VAR_select_items,sn_code,sd.branch_id as branch_id_t,COALESCE(sd.branch_position,0) as branch_position_t,COALESCE(sd.batch_id,0) as batch_id_t,CAST(null as INTEGER) as d_seller_id,null d_seller_name
from sheet_sale_order_detail sd
left join info_item_prop ip on sd.item_id=ip.item_id and ip.company_id=~COMPANY_ID  
left join info_sheet_detail_brief sb on sb.brief_text=sd.remark and sb.company_id=~COMPANY_ID
left join info_item_brand iib on iib.company_id = ~COMPANY_ID and iib.brand_id = ip.item_brand
where sd.company_id= ~COMPANY_ID and ~VAR_DETAIL_HAPPEN_TIME_LIMIT ~AREA_DETAIL
     
)";
                other_field = " , coalesce(oss.senders_name, sm.senders_name) as real_senders_name";
				SQLVariables["IS_DEL_CONDI"] = " and coalesce(sm.is_del, false) = false ";
				SQLVariables["STATUS_ORDER"] = " LEFT JOIN sheet_status_order oss  on sm.sheet_id = oss.sheet_id and oss.company_id = ~COMPANY_ID ";
                SQLVariables["d_oper_name"] = "";
            }

			SQLVariables["main_table"] = main_table;
			SQLVariables["detail_table"] = detail_table;
			SQLVariables["other_field"] = other_field;
			SQLVariables["other_leftJoin"] = other_leftJoin;
			SQLVariables["other_condi"] = other_condi;
			

			this.SQLVariables["select_items"] = @"sd.last_time_price,sd.remark,sd.trade_type,sd.row_index,sd.orig_price,sd.real_price,sd.quantity,sd.cost_price_buy,sd.unit_factor,sd.inout_flag,sd.sub_amount,sd.unit_no,sd.company_id,sd.item_id,sd.sheet_id,sd.happen_time,sd.disp_sheet_id,sd.cost_price_avg,sd.cost_price_recent,sd.cost_price_prop,sd.branch_id,sb.relate_supplier_fee
,item_name,item_spec,valid_days,item_no,brand_name,other_class_name
";

			//默认happen_time提前三个月，利用分区表缩小查询范围，byClearArrearsTime三个月不够，需要现查最早的单据happen_time,
			//byHappenTime不需要限制MAIN_HAPPEN_TIME_LIMIT，因为SQL中已经有了
			DateTime tmStart = Convert.ToDateTime(startDay);
			tmStart = tmStart.AddMonths(-3);
			DateTime tmEnd = Convert.ToDateTime(endDay);
			tmEnd = tmEnd.AddMonths(3);
			SQLVariables["DETAIL_HAPPEN_TIME_LIMIT"] = @$" sd.happen_time >= '{CPubVars.GetDateText(tmStart)}' and sd.happen_time <= '{CPubVars.GetDateText(tmEnd)}'";
			SQLVariables["MAIN_HAPPEN_TIME_LIMIT"] = $" sm.happen_time >= '{CPubVars.GetDateText(tmStart)}' and sm.happen_time <= '{CPubVars.GetDateText(tmEnd)}'";

			if (queryTimeAccord == "byClearArrearsTime")
			{
				SQLVariables["happen_time"] = "sm.settle_time";
				string preSql = $"select min(happen_time) min_happen_time from sheet_sale_main where company_id={company_id} and red_flag is null and settle_time >= '{startDay}' AND settle_time <='{endDay}' and  approve_time is not null";
				dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(preSql, cmd);
				string min_happen_time = rec.min_happen_time;

				SQLVariables["DETAIL_HAPPEN_TIME_LIMIT"] = @$" sd.happen_time >= '{startDay}'";
				if (min_happen_time != "")
				{
					SQLVariables["DETAIL_HAPPEN_TIME_LIMIT"] = $" sd.happen_time >= '{min_happen_time}' ";
					SQLVariables["MAIN_HAPPEN_TIME_LIMIT"] = $" sm.happen_time >= '{min_happen_time}'";

				}
			}
			else if (queryTimeAccord == "byMakeTime")
			{

				SQLVariables["happen_time"] = "sm.make_time";
			}
			else if (queryTimeAccord == "byApproveTime")
			{
				SQLVariables["happen_time"] = "sm.approve_time";
			}
			else if (queryTimeAccord == "bySendTime")
			{
				SQLVariables["happen_time"] = "sm.send_time";
			}
			else if (queryTimeAccord == "byCheckedTime")
			{
				SQLVariables["happen_time"] = "sm.check_account_time";
			}
			else//HAPPEN_TIME
			{
				//默认happen_time提前三个月，利用分区表缩小查询范围
				SQLVariables["happen_time"] = "sm.happen_time";
				SQLVariables["DETAIL_HAPPEN_TIME_LIMIT"] = "  sd.happen_time >= '~VAR_startDay' AND sd.happen_time <='~VAR_endDay'";
				SQLVariables["MAIN_HAPPEN_TIME_LIMIT"] = $" true ";

			}

			if (clearArrearsCondition != "")
			{
				SQLVariables["MAIN_HAPPEN_TIME_LIMIT"] += clearArrearsCondition;
			}

			string remark = DataItems["remark"].Label;

			if (string.IsNullOrEmpty(remark) || remark == ",")
			{
				SQLVariables["remark"] = " and true ";
			}
			else
			{
				string[] keywords = remark.Split(','); // 使用逗号作为分隔符来分割字符串
				keywords = keywords.Where(k => !string.IsNullOrWhiteSpace(k) && k != "无备注").ToArray();
				string dynamicArray = $"ARRAY[{string.Join(", ", keywords.Select(k => $"'%{k}%'"))}]";
				if (remark.Equals("无备注,") || remark.Equals("无备注") || remark.Equals(",无备注,"))
				{
					SQLVariables["remark"] = @$" and (sd.remark= '' or sd.remark is null)";
				}
				else if (remark.Contains("无备注"))
				{
					SQLVariables["remark"] = @$" and (sd.remark||'' ILIKE ANY ({dynamicArray}) OR (sd.remark= '' or sd.remark is null))";
				}
				else
				{
					SQLVariables["remark"] = @$" and (sd.remark||'' ILIKE ANY ({dynamicArray})) ";
				}
			}

			SQLVariables["startDay"] = DataItems["startDay"].Value;
			SQLVariables["endDay"] = DataItems["endDay"].Value;

			var columns = Grids.GetValueOrDefault("gridItems").Columns;
			////含赠
			//var cost_amount_hasfree = columns.GetValueOrDefault("cost_amount_hasfree");
			//var profit_hasfree = columns.GetValueOrDefault("profit_hasfree");
			//var profit_rate_hasfree = columns.GetValueOrDefault("profit_rate_hasfree");
			////赠品
			//var cost_price = columns.GetValueOrDefault("cost_price");
			////去赠
			//var profit = columns.GetValueOrDefault("profit");
			//var profit_rate = columns.GetValueOrDefault("profit_rate");
			//var free_cost_amount = columns.GetValueOrDefault("free_cost_amount");

			//var cost_price = columns.GetValueOrDefault("cost_price");
			var costPrice = "sd.cost_price_buy";//当前进价
			switch (cost_price_type)
			{
				case "4"://最近平均进价
					costPrice = "sd.cost_price_recent";
					break;
				case "3"://当前进价
					costPrice = "sd.cost_price_buy";
					break;
				case "2"://加权价
					costPrice = "sd.cost_price_avg";
					break;
				case "1"://预设成本
					costPrice = "sd.cost_price_prop";
					break;
			}
			// var sheetType = DataItems["sheetType"].Value;


			//Console.WriteLine("Detail页面的OnQueryConditionGot方法获取的costPrice:" + costPrice);


			//var brand_id = DataItems["brand_id"].Value;
			//if (brand_id == "-1"){ 这里只针对只选择无品牌情况，如果是多个值检索还是不准
			//    DataItems["brand_id"].Value = "null";
			//}
			// 这里不明白一定要把值设成 -1 然后再在这里判断换成null
			//var brand_id = DataItems["brand_id"].Value;
			//if (brand_id.Contains("-1"))
			//{
			//	brand_id = brand_id.Replace("-1", "null");
			//	DataItems["brand_id"].Value = brand_id;
			//}
			var group_id = DataItems["group_id"].Value;
			if (group_id.Contains("-1"))
			{
				group_id = group_id.Replace("-1", "null");
				DataItems["group_id"].Value = group_id;
			}

			columns["cost_amount_hasfree_cl_jh"].SqlFld = $"round( (quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} )::numeric,2) ";
			columns["profit_hasfree_cl_jh"].SqlFld = $"round((case when sm.sheet_type in ('X' ,'XD') then (sd.quantity*sd.real_price) else (-sd.quantity*sd.real_price) end ):: NUMERIC, 2 )-round( ( quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric, 2)";
			columns["profit_rate_hasfree_cl_jh"].SqlFld = @$"
round ( (
	(
    100*(round((case when sm.sheet_type in ('X' ,'XD') then (sd.quantity*sd.real_price) else (-sd.quantity*sd.real_price) end ):: NUMERIC, 2 )-round((  quantity*sd.unit_factor*inout_flag*(-1)*{costPrice}  )::numeric,2))
)/
	(
		case when (case when sm.sheet_type in ('X' ,'XD') then (sd.quantity*sd.real_price) else (-sd.quantity*sd.real_price) end ) <>0 
		     then (case when sm.sheet_type in ('X' ,'XD') then (sd.quantity*sd.real_price) else (-sd.quantity*sd.real_price) end )
					else null 
		end
		) :: NUMERIC
	):: NUMERIC, 2)

";
			columns["cost_amount_hasfree"].SqlFld = $"(case when COALESCE(sd.trade_type,'') !='J' and COALESCE(sd.trade_type,'')  !='H' then  quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end) ";
			columns["profit_hasfree"].SqlFld = $"round((case when sm.sheet_type in ('X' ,'XD') then (sd.sub_amount) else (-sd.sub_amount) end ):: NUMERIC, 2 )-round((case when COALESCE(sd.trade_type,'')  !='J' and COALESCE(sd.trade_type,'')  !='H' and COALESCE(sd.trade_type,'')  !='CL' and not coalesce(sd.relate_supplier_fee,false) then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2)";
			columns["profit_rate_hasfree"].SqlFld = @$"
round ( (
	(
    100*(round((case when sm.sheet_type in ('X' ,'XD') then (sd.sub_amount) else (-sd.sub_amount) end ):: NUMERIC, 2 )-round((case when COALESCE(sd.trade_type,'')  !='J' and not coalesce(sd.relate_supplier_fee,false) then  quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2))
)/
	(
		case when (case when sm.sheet_type in ('X' ,'XD') then (sd.sub_amount) else (-sd.sub_amount) end ) <>0 
		     then (case when sm.sheet_type in ('X' ,'XD') then (sd.sub_amount) else (-sd.sub_amount) end )
					else null 
		end
		) :: NUMERIC
	):: NUMERIC, 2)

";
			columns["free_cost_amount"].SqlFld = $"round(((case when sd.quantity!=0 and sd.sub_amount=0 and COALESCE(sd.trade_type,'') !='J'  then sd.quantity*(-1)*sd.unit_factor*inout_flag else 0 end)*{costPrice})::numeric,2) ";
			columns["supplier_fee_cost_amount"].SqlFld = $"round(((case when sd.quantity!=0 and sd.sub_amount=0 and COALESCE(sd.trade_type,'') !='J' and sd.relate_supplier_fee  then sd.quantity*(-1)*sd.unit_factor*inout_flag else 0 end)*{costPrice})::numeric,2) ";

			columns["cost_price"].SqlFld = $"{costPrice}*sd.unit_factor";
			columns["cost_price_detail"].SubColumns["s_cost_price"].SqlFld = $"{costPrice}";
			columns["cost_price_detail"].SubColumns["m_cost_price"].SqlFld = $"{costPrice}*m_unit_factor";
			columns["cost_price_detail"].SubColumns["b_cost_price"].SqlFld = $"{costPrice}*b_unit_factor";
			columns["cost_amount"].SqlFld = $" round((case when COALESCE(sd.trade_type,'') !='J' then  quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2)-round(((case when sd.quantity!=0 and sd.sub_amount=0 and COALESCE(sd.trade_type,'')  !='J' then sd.quantity*(-1)*sd.unit_factor*inout_flag else 0 end)*{costPrice})::numeric,2)";
			columns["profit"].SqlFld = $"round((case when sm.sheet_type in ('X' ,'XD') then (sd.sub_amount) else (-sd.sub_amount) end ):: NUMERIC, 2 )-round((case when COALESCE(sd.trade_type,'')  !='J' and not coalesce(sd.relate_supplier_fee,false) then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2)+round(((case when sd.quantity!=0 and sd.sub_amount=0 and COALESCE(sd.trade_type,'')  !='J'  then sd.quantity*(-1)*sd.unit_factor*inout_flag else 0 end)*{costPrice})::numeric,2)";
			columns["profit_rate"].SqlFld = @$"
round ( (
	(
		100*(round((case when sm.sheet_type in ('X' ,'XD') then (sd.quantity*sd.real_price) else (-sd.quantity*sd.real_price) end ):: NUMERIC, 2 )-round((case when COALESCE(sd.trade_type,'')  !='J' then  quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2)+round(((case when sd.quantity!=0 and sd.sub_amount=0 and COALESCE(sd.trade_type,'')  !='J' and not coalesce(sd.relate_supplier_fee,false)  then sd.quantity*(-1)*sd.unit_factor*inout_flag else 0 end)*{costPrice})::numeric,2))
	)/
	(	case when (case when sm.sheet_type in ('X' ,'XD') then (sd.sub_amount) else (-sd.sub_amount) end ) <>0 
		     then (case when sm.sheet_type in ('X' ,'XD') then (sd.sub_amount) else (-sd.sub_amount) end )
					else null 
		end
		) :: NUMERIC
	):: NUMERIC, 2)

";

			if (DataItems["branch_id"].Value != "")
			{
				SQLVariables["branch_id"] = $" and COALESCE(sd.branch_id,sm.branch_id) in ( {DataItems["branch_id"].Value})";

			}
			else
			{
				SQLVariables["branch_id"] = " ";
			}
			string branchPositionStr = DataItems["branch_position"].Label;

			string branchs_position = "";
			if (branchPositionStr != "")
			{
				var branchPositionList = branchPositionStr.Split(',');
				foreach (var branchPosition in branchPositionList)
				{
					if (branchs_position == "")
					{
						if (branchPosition == "默认库位")
						{
							branchs_position += $@" and (branch_position = 0";
							continue;
						}
						branchs_position = $@" and (branch_position_name ='{branchPosition}'";
					}
					else
					{
						if (branchPosition == "默认库位")
						{
							branchs_position += $@" or branch_position = 0";
							continue;
						}
						branchs_position += $@" or branch_position_name ='{branchPosition}'";
					}
				}
				if (branchs_position != "") branchs_position += ")";
				SQLVariables["branch_position"] = branchs_position;
			}
			else
			{
				SQLVariables["branch_position"] = $@"";
			}
			//这边如果是从库存表跳转过来，需要限定一下条件。
			SQLVariables["NO_PODUCEDATE"] = "";
			var isConfinement = DataItems["isConfinement"].Value;
			if (DataItems["produce_date"].Value != "")
			{
				SQLVariables["produce_date"] = $@" and produce_date = '{DataItems["produce_date"].Value}'";
			}
			else if (DataItems["produce_date"].Value == "" && isConfinement == "true")
			{

				SQLVariables["produce_date"] = "";
				SQLVariables["NO_PODUCEDATE"] = "and coalesce(sm.is_imported,false)=false";
			}
			else
			{
				SQLVariables["produce_date"] = "";
			}
			if (DataItems["batch_no"].Value != "")
			{
				SQLVariables["batch_no"] = $@" and batch_no = '{DataItems["batch_no"].Value}'";
			}
			else
			{
				SQLVariables["batch_no"] = "";
			}
		}

		public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
		{
			string requestFlag = CPubVars.RequestV(Request, "queryTimeAccord");
			if (requestFlag == "")
			{
				JObject jsonOptions = JsonConvert.DeserializeObject<JObject>(JsonOptionsRemembered);
				JObject pageRemembered = (JObject)jsonOptions["page_SaleSheetView"];
				bool hasByHappenTimeOld = false;
				bool hasQueryTimeAccord = false;
				if (pageRemembered != null)
				{
					foreach (var item in pageRemembered)
					{
						if (item.Key == "byHappenTime") hasByHappenTimeOld = true;
						if (item.Key == "queryTimeAccord") hasQueryTimeAccord = true;
					}
				}
				if (hasByHappenTimeOld && !hasQueryTimeAccord)
				{
					string value = (string)pageRemembered["byHappenTime"]["value"];
					if (value == "true")
					{
						DataItems["queryTimeAccord"].Value = "byHappenTime";
						DataItems["queryTimeAccord"].Label = "交易时间";
					}
					else
					{
						DataItems["queryTimeAccord"].Value = "byApproveTime";
						DataItems["queryTimeAccord"].Label = "审核时间";
					}
				}
			}
			else
			{
				if (requestFlag == "byHappenTime")
				{
					DataItems["queryTimeAccord"].Value = "byHappenTime";
					DataItems["queryTimeAccord"].Label = "交易时间";
				}
				else if (requestFlag == "byMakeTime")
				{
					DataItems["queryTimeAccord"].Value = "byMakeTime";
					DataItems["queryTimeAccord"].Label = "制单时间";
				}
				else if (requestFlag == "byCheckedTime")
				{
					DataItems["queryTimeAccord"].Value = "byCheckedTime";
					DataItems["queryTimeAccord"].Label = "交账时间";
				}
			}

			var costPriceType = "3";
			var costPriceTypeName = "预设进价";
			if (JsonCompanySetting.IsValid())
			{
				dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
				if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
			}
			if (costPriceType == "1") costPriceTypeName = "预设成本";
			else if (costPriceType == "2") costPriceTypeName = "加权平均成本";
			else if (costPriceType == "4") costPriceTypeName = "最近平均进价";
			DataItems["cost_price_type"].Value = costPriceType;
			DataItems["cost_price_type"].Label = costPriceTypeName;

			//Console.WriteLine("Detail页面的OnPageInitedWithDataAndRight方法获取的costPriceType:" + costPriceType);
			var columns = Grids["gridItems"].Columns;
			var sheetType = DataItems["sheetType"].Value;
			if (sheetType == "xd")
			{
				DataItems["arrears_status"].Hidden = true;

				DataItems["sn_code"].Hidden = true;
				DataItems["sn_code"].HideOnLoad = true;
				columns.Remove("sn_code");
			}
			if (sheetType == "x")
			{
				DataItems["transfer_status"].Hidden = true;
				DataItems["move_stock"].Hidden = true;
				DataItems["move_to_van"].Hidden = true;

				DataItems["transfer_status"].HideOnLoad = true;
				DataItems["move_stock"].HideOnLoad = true;
				DataItems["move_to_van"].HideOnLoad = true;
			}

			bool seeInPrice = false;
			if (JsonOperRights.IsValid())
			{
				dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
				if (operRights?.delicacy?.seeInPrice?.value is not null)
					seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
			}

			if (!seeInPrice)
			{
				columns["cost_amount_hasfree"].HideOnLoad = columns["cost_amount_hasfree"].Hidden = true;
				columns["profit_hasfree"].HideOnLoad = columns["profit_hasfree"].Hidden = true;
				columns["profit_rate_hasfree"].HideOnLoad = columns["profit_rate_hasfree"].Hidden = true;
				columns["cost_amount_hasfree_cl_jh"].HideOnLoad = columns["cost_amount_hasfree_cl_jh"].Hidden = true;
				columns["profit_hasfree_cl_jh"].HideOnLoad = columns["profit_hasfree_cl_jh"].Hidden = true;
				columns["profit_rate_hasfree_cl_jh"].HideOnLoad = columns["profit_rate_hasfree_cl_jh"].Hidden = true;
				columns["free_cost_amount"].HideOnLoad = columns["free_cost_amount"].Hidden = true;
				columns["profit"].HideOnLoad = columns["profit"].Hidden = true;
				columns["profit_rate"].HideOnLoad = columns["profit_rate"].Hidden = true;
				columns["cost_amount"].HideOnLoad = columns["cost_amount"].Hidden = true;
				columns["cost_price"].HideOnLoad = columns["cost_price"].Hidden = true;

			}


			var forItemHistory = DataItems["forItemHistory"].Value;
			if (forItemHistory == "1")
			{
				DataItems["sheetType"].Value = "XSBC";
				foreach (var kp in this.DataItems)
				{
					if (!"startDay,endDay,supcust_id,item_id".Contains(kp.Key))
					{
						kp.Value.Hidden = kp.Value.HideOnLoad = true;
					}
				}
				foreach (var kp in this.Grids["gridItems"].Columns)
				{
					if (!"item_name,sheet_type1,sheet_no,sup_name,quantity,real_price,sub_amount,remark".Contains(kp.Key))
					{
						kp.Value.Hidden = kp.Value.HideOnLoad = true;
					}
					if ("rebate_price".Contains(kp.Key))
					{
						kp.Value.GetFromDb = true;
						kp.Value.Hidden = kp.Value.HideOnLoad = false;
					}
				}
			}

			if (sheetType.IsValid() && sheetType.ToLower() == "xd")
			{
				this.DataItems.Remove("sale_way");
			}
			//处理订单明细表与销售明细表的差异；订单不展示，销售明细隐藏
			string requestSheetType = "";
			if (CPubVars.RequestV(Request, "sheetType") != "")
			{
				requestSheetType = Convert.ToString(CPubVars.RequestV(Request, "sheetType"));
				if (requestSheetType == "x")
				{
					DataItems["queryTimeAccord"].Source = @"[{v:'byApproveTime',l:'审核时间'},
                                {v:'byHappenTime',l:'交易时间'},
                                {v:'byMakeTime',l:'制单时间'},
                                {v:'byCheckedTime',l:'交账时间'},
                                {v:'bySendTime',l:'送货时间'},
                                {v:'byClearArrearsTime',l:'欠款结清时间'}]";
				}
				else
				{
					DataItems["queryTimeAccord"].Source = @"[{v:'byApproveTime',l:'审核时间'},
                            {v:'byHappenTime',l:'交易时间'},
                            {v:'byMakeTime',l:'制单时间'},
                            {v:'bySendTime',l:'送货时间'}]";
				}

			}
		}

	}



	[Route("api/[controller]/[action]")]
	public class SalesDetailController : QueryController
	{
		public SalesDetailController(CMySbCommand cmd)
		{
			this.cmd = cmd;
		}


		[HttpGet]
		public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
		{
			SalesDetailModel model = new SalesDetailModel(cmd);
			return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

		}

		[HttpPost]
		public async Task<object> GetQueryRecords([FromBody] dynamic data)
		{
			string sheetType = data.sheetType;
			string endDay = data.endDay + "";
			// if(!Regex.IsMatch(endDay,"23:59:00"))
			// {
			//     data.endDay = data.endDay+ " 23:59:59";
			// }
			string item_id = data.item_id;
			string supcust_id = data.supcust_id;
			string startDay = data.startDay + "";
			SalesDetailModel model = new SalesDetailModel(cmd);

			object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
			return records;
		}

		[HttpPost]
		public async Task<ActionResult> ExportExcel()
		{
			string sParams = Request.Form["params"];
			sParams = System.Web.HttpUtility.UrlDecode(sParams);
			dynamic queryParams = JsonConvert.DeserializeObject(sParams);
			SalesDetailModel model = new SalesDetailModel(cmd);
			return await model.ExportExcel(Request, cmd, queryParams);
		}
	}
}
