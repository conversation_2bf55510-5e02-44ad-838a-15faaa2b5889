@page
@model ArtisanManage.Pages.Setting.ReceiptTemplateModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<title></title>
		<link rel="stylesheet" type="text/css" href="~/PrintTemplate/css/PrintTemplate.css?v=@Html.Raw(Model.Version)" />
		<link rel="stylesheet" href="~/PrintTemplate/font/iconfont.css">
		<link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)">
	    <script src="~/PrintTemplate/js/jquery-1.7.1.min.js"></script> 
		<script src="~/MiniJsLib/MiniJsLibPC.js"></script>

		<link rel="stylesheet" href="~/MiniJsLib/jquery.dialog.css?v=@Html.Raw(Model.Version)">
		<script src="~/MiniJsLib/jquery.dialog.js?v=@Html.Raw(Model.Version)"></script>
		<script type="text/javascript" src="~/tinymce_5.8.2/tinymce.min.js" ></script>
	    <script type="text/javascript" src="~/PrintTemplate/js/exif.min.js" ></script>
		<style>
            * {
                -webkit-user-select: none; /* Chrome/Safari/Opera */
                -moz-user-select: none; /* Firefox */
                -ms-user-select: none; /* Internet Explorer/Edge */
                user-select: none; /* Non-prefixed version, currently not supported by any browser */
                font-family:inherit;
			}

            ::-webkit-scrollbar {
                width: 16px;
                height: 16px;
                background-color: #888;
            }

            ::-webkit-scrollbar-track {
                background-color: #888;
            }


            ::-webkit-scrollbar-thumb {
                border-radius: 7px;
                -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
                background-color: #aaaaaa;
            }

            ::-webkit-scrollbar-corner {
                background-color: #888;
            }
            .mce-content-body{
				margin:0px;
            }

			svg {
                width: 30px;
                height: 30px;
                display: inline-block;
                margin: 8px; 
                cursor: pointer;
                fill:#ccc;
            }
            table td{
                padding: 0px;
                padding-bottom: 0px;
                padding-top: 0px;
                padding-left: 0px;
                padding-right: 0px;
            }
		</style>
	</head>
	<body>
		<div id="app">
			<!-- 左边栏 -->
			<div class="drag_navs" style=" padding:0px;margin-top:0px;top:0px;height:100%;">
				<div class="drag_navs_boxs" id="drag_navs_boxs"  style="padding:0px;margin-top:50px;top:0px;left:0px;height:100%;">
					<div class="drag_navs_boxs_m" id="drag_navs_boxs_m">
					</div>
				</div>
			</div>

			<!-- 中间栏 -->
			<div class="drag_navs_wid" id="drag_navs_wid" style="position:absolute; display:flex; padding-left:40px; width:100%; height:50px; margin-top:0px; top:0px; left:0px; margin-left:0px; z-index:99999;">	
				<div class="drag_wid_input drag_wid_input">
					<div class="drag_wid_input_name" style="width:50px;">
						名称:
					</div>
					<div class="drag_wid_input_input">
						<input type="text" placeholder="" id="templateName" value="">	 
					</div>
				</div>

				<div class="drag_wid_input" style="margin-left: 30px;">
					<div class="drag_wid_input_name" >
						票据宽度:
					</div>
					<select class="drag_wid_input_input" id="receipt_width" style="width:120px;margin-left:10px;">
						<option value="58">58mm</option>
						<option value="76">76mm</option>
						<option value="80">80mm</option>
						<option value="110">110mm</option>
					</select>
				</div>

				<div class="submit_btn" style="display:block;width: 300px;position: relative;margin-right:20px;">
				    <form id="formHeadImage">
                    <input id="upload-official-head" type="file" name="file" accept="image/png,image/bmp,image/jpeg" style=" display:none" />
					</form>
					<form id="formTailImage">
					<input id="upload-official-seal" type="file" name="file" accept="image/png,image/bmp,image/jpeg" style="display: none" />
					</form>
					<button id="btnSave" style="width: 60px;">保存</button>
					<button id="btnSaveAs" style="margin-left:10px;font-size:15px;">另存</button>
					<button id="btnReturn" style="margin-top:10px;">返回</button>
				</div>
			</div>
			<!-- 中间栏 -->
			<div class="whole-container" style="background-color:#999; position:absolute;display:block; overflow:auto;left:60px;top:50px; height:calc(100% - 59px);width:50%;" draggable="false">
				<div id="print_sheet_outer" style="background-image:url('/PrintTemplate/img/receipt.png');width:calc(100% -  100px);min-height:calc(100% - 100px);padding:50px; display:flex; justify-content:center;align-items:center;flex-direction:row;background:#888;">
					<div id="print_sheet" style="background-color:#fff;display:flex;flex-direction:column;">
						<div class="imgDiv" style="justify-content: flex-end;">
							<div id="uploadHeadText" style="font-family:monospace;width:100%;padding:2px 1px;text-align:center;color: #ddd">页眉图片</div>
							<svg id="uploadHead" style="margin:auto;padding: 1px 2px 2px;visibility:visible;display:flex;width:30px;height:30px;"><use xlink: href="/images/images.svg#thinAdd"></use></svg>
							<img class="HeaderFooter" id="print_pageHead" alt="页眉" style="object-fit:none;overflow:hidden;min-height:40px;" src=" " onerror="this.hidden='true'" />
							<div id="closeHead" style="position: absolute;visibility:hidden;top: 2px;z-index: 100;right: 2px;cursor: pointer;opacity: .2;font-size: 22px;padding: 5px 2px 2px;color: #000;font-weight: bold;line-height: 10px;text-align: center;">×</div>
						</div>
						<textarea class="print-area" id="print_tableHead" placeholder="表头" data="tableHead" draggable="false" style="margin:0px;font-family:monospace;height:80px;width:100%; overflow: hidden;padding: 5px 10px;box-sizing: border-box;transition: all 0.2s linear;"></textarea>
						<textarea class="print-area" id="print_table" placeholder="表格" data="table" draggable="false" style="margin:0px;font-family:monospace;height:80px;width:100%; overflow: hidden;padding: 5px 10px;box-sizing: border-box;transition: all 0.2s linear;"></textarea>
						<textarea class="print-area" id="print_tableTail" placeholder="表尾" data="tableTail" draggable="false" style="margin:0px;font-family:monospace;height:80px;width:100%; overflow: hidden;padding: 5px 10px;box-sizing: border-box;transition: all 0.2s linear;"></textarea>
						<div class="imgDiv" style="justify-content: flex-end;">
							<div id="uploadTailText" style="font-family:monospace;width:100%;padding:2px 1px;text-align:center;color: #ddd">页脚图片</div>
							<svg id="uploadTail" style="margin:auto;padding: 1px 2px 2px;visibility:visible;display:flex;width:30px;height:30px;"><use xlink: href="/images/images.svg#thinAdd"></use></svg>
							<img class="HeaderFooter" id="print_pageTail" alt="页脚" style="object-fit:none;overflow:hidden;min-height:40px;" src=" " onerror="this.hidden='true'" />
							<div id="closeTail" style="position: absolute;right:10px;float:right;visibility:hidden;top: 0px;z-index: 100;right: 2px;cursor: pointer;opacity: .2;font-size: 22px;padding: 0px 2px 2px;color: #000;font-weight: bold;line-height: 10px;">×</div>
						</div>
					</div>
					
				</div>
			</div>
			<!-- 中间栏 -->
			<!-- 右边栏-->
			<div class="preview_container" style="background-image:url('/PrintTemplate/img/desk2.jpg');background-position:center;background-size:100% 100%;z-index:100;position:absolute;overflow:auto;display:flex;justify-content:center;align-items:center;padding:30px;flex-direction:row;width:41%;right:10px;height:calc(100% - 120px);top:50px;" draggable="false">
				<div class="preview" style="min-width:219px;background-image:url('/PrintTemplate/img/receipt.png');background-position:center;background-size:250% 137%;white-space:break-spaces;word-break:break-all;display:flex;flex-direction:column;font-family:monospace;position:absolute;padding: 20px 0px 40px;" draggable="false">
				</div>
			</div>
			<!-- 右边栏 -->
		</div>
	</body>
</html>
<script src="~/PrintTemplate/js/print_json.js?v=@Html.Raw(Model.Version)" type="text/javascript" charset="utf-8"></script>
<script>
	/*
	注：58mm一行32个字符;76mm一行43个字符；80mm一行48个字符；110mm一行67个字符。如果要修改，在文件中搜索sheetSheetWidth，修改其中return的数字即可，并在setPreviewContent()中修改font-size和padding
	未解决的问题：1.当输入内容过多时，右侧预览区(id = 'preview')拉长后上下边距过窄
	2.上传页眉页脚图片部分，删除小标不能固定在图片的右上角，需要修改css样式
	3.预览区的加粗部分用了strong，显示出的效果无法对齐
	4.后面可能要更改设置为任意长度用的字符，目前用的是'#'，在该文件中中搜索rule_width、两处的rule1、addItem = addItem.replace(/#/, '') ，将其中的#替换掉即可
	*/
	var TemplateID = "@Html.Raw(Model.TemplateID)"; 
	var SheetType = "@Html.Raw(Model.SheetType)";	
	var g_operKey = "@Html.Raw(Model.OperKey)";
	$(function () {
		let PropertyOptions = {
			"sheetTypeList": [
				{
					"title": "销售单",
					"name": 'X'
				}, {
					"title": "退货单",
					"name": 'T'
				}
				, {
					"title": "销售订单",
					"name": 'XD'
				}
				, {
					"title": "退货订单",
					"name": 'TD'
				}
				, {
					"title": "采购单",
					"name": 'CG'
				}
				, {
					"title": "采购退货单",
					"name": 'CT'
				}
				, {
					"title": "采购订单",
					"name": 'CD'
				}
				, {
					"title": "调拨单",
					"name": 'DB'
				}
				, {
					"title": "盘点单",
					"name": 'PD'
				},
				, {
					"title": "盘点盈亏单",
					"name": 'YK'
				},
				{
					"title": "报损单",
					"name": 'BS'
				}, 
				{
					"title": "定货会",
					"name": 'DH'
				}, {
					"title": "收款单",
					"name": 'SK'
				}
				, {
					"title": "付款单",
					"name": 'FK'
				}, {
					"title": "预收款单",
					"name": 'YS'
				}
				, {
					"title": "预付款单",
					"name": 'YF'
				}
				, {
					"title": "销售商品汇总单",
					"name": "X_SUM"
				}
				, {
					"title": "客户汇总单",
					"name": "X_SHEETS_MAIN"
				}
				, {
					"title": "费用支出单",
					"name": "ZC"
				}
				, {
					"title": "其他收入单",
					"name": "SR"
				}
				, {
					"title": "贷款单",
					"name": "DK"
				}
				, {
					"title": "还贷款单",
					"name": "HDK"
				}
			], 
			"receiptTabAreas": [{
				"title": "表头",
				"icons": "&#xe67e;",
				"name": "tableHead",
				"id": "nav_tableHead"
			}, {
				"title": "表格",
				"icons": "&#xe686;",
				"name": "table",
				"id": "nav_table"
			}, {
				"title": "表尾",
				"icons": "&#xe68b;",
				"name": "tableTail",
				"id": "nav_tableTail"
			}
		]} ;
		let SheetTemplate = {
			name: "",
			title: "",
			width:"",
			pageHead:{
				name: "pageHead",
				title: "页眉区",
				htmlContent: "",
			},
			tableHead: {
				name: "tableHead",
				title: "表头区",
				htmlContent: ""
			},
			table: {
				name: "table",
				title: "表格区",
				htmlContent: ""
			},
			tableTail: {
				name: "tableTail",
				title: "表尾区",
				htmlContent: ""
			},
			pageTail: {
				name: "pageTail",
				title: "页尾区",
				htmlContent: ""
			}
		}
		var AvailElements = {}
		window.loadTemplate = function (sheetType, templateID) {
			TemplateID = templateID;
			$.ajax({
				url: "/api/ReceiptTemplate/LoadTemplate", //url地址
				dataType: "json", //返回的数据类型
				type: "get", //发起请求的方式
				data: {
					sheetType: sheetType,
					templateID: templateID,
					operKey: g_operKey
				},
				success: function (res) {
					if (res.result === "OK") {
						console.log("loadTemplate success!");
						AvailElements = res.avail_elements
						loadTabAreas(PropertyOptions.receiptTabAreas,res.avail_elements)
						$('#templateName').val(res.template_name)
						SheetTemplate = res.template
						renderTemplate(res.template)//渲染模板
					}
				},
				error: function () {
					console.log("loadTemplate failure!");
				}
			});
		}
		//加载左边栏
		function loadTabAreas(areas, availElements) {
			let htmls = '';
			areas.map((area) => {
				htmls += `<div class="drag_navs_more" style="z-index:10;">
					<div class="drag_navs_more_on" style="cursor:pointer;border-bottom-style:solid;border-color:#eee; border-bottom-width:1px;padding-top:10px;padding-left:12px;" id="${area.id}">
					  <span class="iconfont" style="margin:15px;margin-right:12px;"> ${area.icons}</span >`;
				htmls += '<i class="iconfont">&#xe621;</i>'

				htmls += `<div style="margin-left:14px;margin-bottom:10px;font-size:12px;color:#777;">` + area.title + "</div>"
				let cls = "toolbox-item"
				if (area.name && availElements[area.name]) {
					htmls += `<div class="toolbox ${cls}" id="toolBox_${area.id}">` +
						'<ul>';
					availElements[area.name].map((element) => {
						let data = {
							math: area.name,
							name: element.name,
							title: element.title,
							value: element.value
						}
						htmls += `<li class="toolbox_li"  id=${element.name} value="leftNav" data=${JSON.stringify(data)} >${element.title}</li>`
					})
					htmls += '</ul></div>';
				}
				htmls += '</div></div>'
			})
			$("#drag_navs_boxs_m").html(htmls)
			setTimeout(function () {
				initClickObjects();
			}, 500);
		}
		let headerContent = ''
		let headerImg = ''
		let headContent = ''
		let bodyContent = ''
		let tailContent = ''
		let footerContent = ''
		let footerImg = ''
		let previewContent = ''
		let previewPadding = 0
		initPage();
		function initPage(){
			//输入框自适应高度
			$("textarea").off("input")
			$("#print_tableHead").on("input", function (e) {
				this.style.height = `${this.scrollHeight}px`;
				setHtmlContentForArea('print_tableHead', 'tableHead','data')
				setPrevieContent()
			})
			$("#print_table").on("input", function (e) {
				this.style.height = `${this.scrollHeight}px`;
				setHtmlContentForArea('print_table', 'table','data')
				setPrevieContent()
			})
			$("#print_tableTail").on("input", function (e) {
				this.style.height = `${this.scrollHeight}px`;
				setHtmlContentForArea('print_tableTail', 'tableTail','data')
				setPrevieContent()
			})
			$("#closeHead").on("click", function () {
				$('#print_pageHead').src = ' '
				headerImg = ''
				$('#closeHead').css('visibility', 'hidden')
				$('#print_pageHead').attr('hidden','hidden')
				$('#uploadHead').css('visibility', 'visible')
				$('#uploadHeadText').removeAttr("hidden")
				resetPreviewImage("print_pageHead");
			});
			$("#closeTail").on("click", function () {
				$('#print_pageTail').src = 'url()' ;
				footerImg = ''
				$('#closeTail').css('visibility', 'hidden')
				$('#print_pageTail').attr('hidden', 'hidden')
				$('#uploadTail').css('visibility', 'visible')
				$('#uploadTailText').removeAttr("hidden")
				resetPreviewImage("print_pageTail");
			});
			$("#uploadHead").on("click", function () {
				$("#upload-official-head").trigger("click");
			})
			$("#uploadTail").on("click", function () {
				$("#upload-official-seal").trigger("click");
			})
			$("#upload-official-head").on('change', function (event) {
				let fileDom = document.getElementById('upload-official-head')
				let showDom = "print_pageHead"
				previewImage(fileDom, showDom)
				$('#uploadHead').css('visibility', 'hidden')
				$("#uploadHeadText").attr('hidden', 'hidden')
				$('#print_pageHead').removeAttr("hidden")
				$('#closeHead').css('visibility', 'visible')
			});

			$("#upload-official-seal").on('change', function (event) {
				let fileDom = document.getElementById('upload-official-seal')
				let showDom = "print_pageTail"
				previewImage(fileDom, showDom)
				$('#uploadTail').css('visibility', 'hidden')
				$('#uploadTailText').attr('hidden', 'hidden')
				$('#print_pageTail').removeAttr("hidden")
				$('#closeTail').css('visibility', 'visible')
			})
			$("#btnSave").on("click", function () {
				saveAndUpload();
			})

			$("#btnSaveAs").on("click", function () {
				let newName = window.prompt("请输入另存的名称", "")
				SheetTemplate.title = newName;
				$('#templateName').val(newName);
				TemplateID = "";
				saveAndUpload();
			})

			$("#btnReturn").on("click", function () {
				window.srcWindow.WaitToReQueryData(500);
				window.parent.closeTab(window);
			})
		}
		function resetPreviewImage(areaID){
			if (areaID === "print_pageHead") {
				headerContent = ' ';
			} else if (areaID === "print_pageTail") {
				footerContent = ' ';
			}
			setPrevieContent()
		}
		//让上传的图片显示在页眉页脚
		function previewImage(uploadDocument, showDom) {
			var showDocument = document.getElementById(showDom)
			var reads = new FileReader();
			var file = uploadDocument.files[0];
			reads.readAsDataURL(file)
			reads.onload = function (e) {
				showDocument.src = e.currentTarget.result
				 //console.log('base结果：' + e.currentTarget.result)//这个result就是base64编码的
				//实时刷新预览区
				 setHeaderFooterForArea(showDom,e.currentTarget.result)

				 if (showDom == "print_pageHead"){
					 headerImg =e.currentTarget.result 
				 }else if(showDom == "print_pageTail"){
					 footerImg =e.currentTarget.result
				 }
				 
				//页眉页脚图片宽高自适应
				//var width = $("#print_sheet").css("width")
				//$(showDocument).css('width',width)
				//$(showDocument).css('height','80px')
			}
		}
		function getSheetWidth(){
			var w = $('#receipt_width').val()
			if(w == 58) return 32
			else if(w == 76) return 43
			else if(w == 80) return 48
			else if(w == 110) return 67
		}
		function getFixedWidthPos(str, width){
			let j = 0 ;
			var rule = /[\u2E80-\u9FFF]/
			for (var i = 0; i < str.length; i++){
				if (rule.test(str[i])) j += 2
				else j += 1
				if(j > width)  return i
			}
			return str.length
		}
		function getRealWidth(str,pos){
			let j = 0;
			var rule = /[\u4e00-\u9fa5\·\！\￥\……\（\）\—\【\】\、\；\‘\’\：\“\”\《\》\？\，\。\、]/
			for (var i = 0; i < str.length; i++) {
				if (rule.test(str[i])) j += 2 
				else if(str[i] == '\n'){
					var width = getSheetWidth()
					j += width - pos%width - j
				}
				else j += 1
			}
			return j
		}
		//type='db'只要替换{名称}，type='data',需要替换{名称}和^[cd]^
		function setHtmlContentForArea(areaID, partName,type) {
			let textarea = $('#' + areaID);
			var text = textarea.val();
			var newText = ''
			var rule = /[\u4e00-\u9fa5\·\！\￥\……\（\）\—\【\】\、\；\‘\’\：\“\”\《\》\？\，\。\、]/g
			var rule_width = /{[\u2E80-\u9FFF]+#}/				//{汉字#}    TODO:如果后面要改指定随意长度的符号，把这里的'#'替换掉即可
			var rule1 = /{(\s)*[\u2E80-\u9FFF]+(\s)*#?}/     //{   汉字   }  TODO:如果后面要改指定随意长度的符号，把这里的'#'替换掉即可
			var rule2 = /[\u2E80-\u9FFF]+/				   //汉字
			var rule3 = /\^[cd].*\^/						//c是加粗，d是加大  /(?<=\^[cd]).*(?=\^)/
			var sheetWidth = getSheetWidth()
			var realPos = 0  //newText字符串实际长度(一个汉字记为2宽度)
			let widthList = []
			var row = 0
			var rowCount = 0
	
			while (rule1.test(text)) {//若文本中有可匹配项
				var pos = text.search(rule1)
				var lengthFlag = false //判断是否为固定长度元素
				//每到新的一行，就将list清空
				if ((Math.floor(realPos / sheetWidth) > row) && type == 'data') {
					row = Math.floor(realPos / sheetWidth)
					console.log("到了新的一行")
					while (widthList.length) {
						var element = widthList.shift()
						var e_addItem = element.item
						var e_realPos = element.realPos
						var e_itemWidth = element.itemWidth
						var e_length = e_realPos % sheetWidth
						var r_length = realPos % sheetWidth
						var acWidth = getFixedWidthPos(e_addItem, e_itemWidth)
						var addItem = e_addItem.substring(0, acWidth)
						var toComplete = 0
						console.log("e_realPos1:" + e_realPos + ",realPos1:" + realPos)
						console.log("e_length1:" + e_length + ",r_length1:" + r_length)
						 if(r_length > e_length) {
							//toComplete = realPos + sheetWidth - e_itemWidth
							toComplete = Math.ceil(realPos / sheetWidth) * sheetWidth + e_length
							if (rowCount > 0) toComplete = toComplete - rowCount
							console.log("补全的11:" + toComplete)
							newText = newText.padEnd(toComplete, ' ')
							realPos = toComplete + e_itemWidth + rowCount
						} else if (r_length < e_length) {
							toComplete = realPos + e_length - r_length  
							if (rowCount > 0) toComplete = toComplete - rowCount
							console.log("补全的12:" + toComplete)
							newText = newText.padEnd(toComplete, ' ')
							realPos = toComplete + e_itemWidth + rowCount
						} else realPos += e_itemWidth

						//将宽度不够的待添加字符补全
						element.item = e_addItem.substring(acWidth)
						var thisCharCount = addItem.match(rule) == null ? 0 : addItem.match(rule).length
						if (element.item.length == 0) {
							addItem = addItem.padEnd(e_itemWidth - thisCharCount, ' ')
						}

						newText += addItem
						rowCount += thisCharCount

						//若输入完成，从list中删除
						if (element.item.length <= 0) {
							var index = widthList.indexOf(element);
							if (index > -1) {
								widthList.splice(index, 1);
							}
						} else widthList.push(element)
						}
					//清空后置零
					rowCount = 0
				}

				if(pos == 0){//若为0，表明{汉字}在text最前面
					var reps = rule1.exec(text)[0] 
					var rep = rule2.exec(reps)[0]
					var flag = false
					var itemWidth = reps.length - rep.length + rep.length * 2  //固定的宽度 若长度随意则该变量无效
					//若当前扫描的这个不是长度随意的
					if (reps.search(rule_width) != 0)  lengthFlag = true

					AvailElements[partName].some((element) => {
						var word = element.title
						if (type == 'db') var showItem = element.name  //type设置为db，就会显示数据库中的名字
						else var showItem = element.value  //type设置为data，就会显示具体数值
						if(word == rep){
							var addItem = reps.replace(rep,showItem);
							if (type == 'data'){//还要把{}去掉
								addItem = addItem.replace(/{(\s)*/, '')
								addItem = addItem.replace(/(\s)*}/, '')

								if(lengthFlag){//固定长度替换
									var acWidth = getFixedWidthPos(addItem,itemWidth)
									var item = addItem.substring(acWidth)
									var charCount = addItem.substring(0, acWidth).match(rule) == null ? 0 : addItem.substring(0, acWidth).match(rule).length
									rowCount += charCount

									if(item != '') {//若具体宽度小于指定宽度，就不用加入list
										//widthList.push({ item, realPos, itemWidth ,charCount})
										widthList.push({ item, realPos, itemWidth,charCount})
										addItem = addItem.substring(0, acWidth)
									}else{
										addItem = addItem.padEnd(itemWidth - charCount, ' ')//宽度不够的要补全
									}
								}
								else {//随意长度替换
									addItem = addItem.replace(/#/, '')
									var charCount = addItem.match(rule) == null ? 0 : addItem.match(rule).length
									rowCount += charCount

									itemWidth = getRealWidth(addItem)
								}
								var setWidth = Math.ceil((realPos + 1) / sheetWidth) * sheetWidth
								console.log(realPos + ',' + itemWidth + ',' + setWidth)
								if (((realPos + itemWidth) > setWidth * sheetWidth) && widthList.length) {
									console.log("必须换行了11！")
									var lineBreakStr = ''
									lineBreakStr = lineBreakStr.padEnd(sheetWidth - realPos % sheetWidth, ' ')
									addItem = lineBreakStr
									itemWidth = getRealWidth(lineBreakStr, realPos)
									console.log("realPos=" + realPos + ",lineBreakWidth=" + itemWidth)
								}
								else text = text.substring(reps.length)
								realPos += itemWidth
							}
							else text = text.substring(reps.length)
							newText += addItem
							flag = true
							return true
						}
					})

					//如果遍历完没有找到匹配的
					if (!flag) {
						if (type == 'data'){
							pos = reps.length //代表字符个数
							itemWidth = getRealWidth(text.substring(0, pos), realPos)//代表字符宽度
							var cnstr = text.substring(0, pos).match(rule)
							var count = cnstr == null ? 0 : cnstr.length//其中中文字符个数
							if (count > 0) rowCount += count

							var setWidth = Math.ceil((realPos + 1) / sheetWidth) * sheetWidth
							console.log(realPos + ',' + itemWidth + ',' + count + ',' + setWidth)
							if (((realPos + itemWidth + count) > setWidth) && widthList.length) {
								console.log("必须换行了22！")
								debugger
								var lineBreakStr = ''
								lineBreakStr = lineBreakStr.padEnd(sheetWidth - realPos % sheetWidth, ' ')
								newText += lineBreakStr
								itemWidth = getRealWidth(lineBreakStr, realPos)
								realPos += itemWidth
								console.log("realPos=" + realPos + ",lineBreakWidth=" + itemWidth)
							}
							else {
								newText += text.substring(0, pos)
								itemWidth = getRealWidth(text.substring(0, pos), realPos)
								realPos += itemWidth
								text = text.substring(pos)
							}
						}
						else {
							newText += text.substring(0, pos)
							//itemWidth = getRealWidth(text.substring(0, pos), realPos)
							//realPos += itemWidth
							text = text.substring(pos)
						}
					}
				} 
				else {
					if (type == 'data'){
						var cnstr = text.substring(0, pos).match(rule)
						var count = cnstr == null ? 0 : cnstr.length //中文字符个数
						if (count > 0) rowCount += count
						var itemWidth = getRealWidth(text.substring(0, pos), realPos)
						//如果要加的字符串超过可加的宽度，就给他插入换行符，让字符串在输出完widthList后换行显示
						var setWidth = Math.ceil((realPos + 1) / sheetWidth) * sheetWidth
						console.log(realPos + ',' + itemWidth + ',' + count + ',' + setWidth)
						if (((realPos + itemWidth + count) > setWidth) && widthList.length) {
							console.log("必须换行了33！")
							var lineBreakStr = ''
							lineBreakStr = lineBreakStr.padEnd(sheetWidth - realPos % sheetWidth, ' ')
							newText += lineBreakStr
							itemWidth = getRealWidth(lineBreakStr, realPos)
							realPos += itemWidth
							console.log("realPos=" + realPos + ",lineBreakWidth=" + itemWidth)
						}
						else {
							newText += text.substring(0, pos)
							itemWidth = getRealWidth(text.substring(0, pos), realPos)
							realPos += itemWidth
							text = text.substring(pos)
						}
					}
					else{
						newText += text.substring(0, pos)
						//itemWidth = getRealWidth(text.substring(0, pos), realPos)
						//realPos += itemWidth
						text = text.substring(pos)
					}
				}
			} 
			//如果要匹配的分行元素在有{}的最后一行，就会进入这个while 
			while (widthList.length && type == 'data') {
				//forEach不是先进先出，而是数字优先,所以不能用forEach
				var element = widthList.shift()
				var e_addItem = element.item
				var e_realPos = element.realPos
				var e_itemWidth = element.itemWidth
				var e_length = e_realPos % sheetWidth
				var r_length = realPos % sheetWidth
				var acWidth = getFixedWidthPos(e_addItem, e_itemWidth)
				var addItem = e_addItem.substring(0, acWidth)
				var toComplete = 0
				console.log("e_realPos2:" + e_realPos + ",realPos2:" + realPos)
				console.log("e_length2:" + e_length + ",r_length2:" + r_length)
				//先补空格
				if(r_length > e_length) {
					toComplete = Math.ceil(realPos / sheetWidth) * sheetWidth + e_length
					if (rowCount > 0) toComplete = toComplete - rowCount
					console.log("补全的21:" + toComplete)
					realPos = toComplete + e_itemWidth + rowCount
				} else if (r_length < e_length) {
					toComplete = realPos + e_length - r_length
					if (rowCount > 0) toComplete = toComplete - rowCount
					console.log("补全的22:" + toComplete)
					realPos = toComplete + e_itemWidth + rowCount
				} else realPos += e_itemWidth

				newText = newText.padEnd(toComplete, ' ')
				//将宽度不够的待添加字符补全
				element.item = e_addItem.substring(acWidth)
				var thisCharCount = e_addItem.substring(0, acWidth).match(rule) == null ? 0 : e_addItem.substring(0, acWidth).match(rule).length
				if (element.item.length == 0) {
					addItem = addItem.padEnd(e_itemWidth -thisCharCount, ' ')
				}
				newText += addItem
				rowCount += thisCharCount

				if (element.item.length == 0) {
					const index = widthList.indexOf(element)
					if (index > -1) {
						widthList.splice(index, 1)
					}
				}
				else widthList.push(element)
			}
			
			newText += text
			text = newText
			newText = ''
			//对加粗，增大的字进行处理
			if (type == 'data'){
				while(rule3.test(text)){
					var ruleBold = /\^c.*\^/	
					var ruleSize = /\^d.*\^/	
					var posBold = text.search(ruleBold)
					var posSize = text.search(ruleSize)
					if(posBold == 0){//若为0，表明^c.....^在text最前面
						var rule = /(?<=\^c).*(?=\^)/
						var reps = rule.exec(text)[0] 
						newText = newText + '<strong>' + reps + '</strong>'  //TODO:这个用strong 没法儿对齐
						text = text.substring(reps.length+3)
					} else if (posSize == 0){//若为0，表明^d.....^在text最前面
						var rule = /(?<=\^d).*(?=\^)/
						var reps = rule.exec(text)[0] 
						newText = newText + '<div style="font-size:25px">' + reps + '</div>'
						text = text.substring(reps.length+3)
					}else {
						var pos = posBold > posSize ? posBold : posSize
						if (posBold > -1 && posSize > -1) {
							pos = posBold > posSize ? posSize : posBold
						}
						newText += text.substring(0,pos)
						text = text.substring(pos)
					}
				}
			}
			newText += text

			if (areaID === "print_tableHead") {
				headContent = newText;
			} else if (areaID === "print_table") {
				bodyContent = newText;
			} else if (areaID === "print_tableTail") {
				tailContent = newText;
			}

		}
		function setHeaderFooterForArea(areaID,result) {
			if (result != null) {
				var text = '<img style="object-fit:none;overflow:hidden;min-height:40px;" src="' + result + '" onerror="this.hidden=\'true\'"  />'
				if (areaID === "print_pageHead") {
					headerContent = text;
				} else if (areaID === "print_pageTail") {
					footerContent = text;
				}
			}
			setPrevieContent()
		}
		function initClickObjects(){
			//左边工具栏 li点击
			$(".toolbox_li").off("click")
			$(".toolbox_li").on("click", function (e) {
				let that_ = $(this).parent().parent().parent()[0].id;
				var textplace = ' ';
				if (that_ === "nav_tableHead") {
					textplace = "print_tableHead"
					partName = 'tableHead'
				} else if (that_ === "nav_table") {
					textplace = 'print_table'
					partName = 'table'
				} else if (that_ === "nav_tableTail") {
					textplace = 'print_tableTail'
					partName = 'tableTail'
				}
				
				let textarea = $('#' + textplace);
				// 获取光标位置
				var ctrl = document.getElementById(textplace)
				var cursorPos = getPositionForTextArea(ctrl)
				
				var value = $(this).text();
				var insertText = '{' + value + '}';
				var text = textarea.val();
				// 在当前光标后插入
				var newText = text.substring(0, cursorPos) + insertText + text.substring(cursorPos);
				textarea.val(newText);
				// 更新右侧预览区
				setHtmlContentForArea(textplace, partName,'data')
				setPrevieContent()
			})
			// 鼠标进入组件显示左边栏(不能用悬浮，不然会闪)
			$(".drag_navs_more_on").on("mouseenter", function (e) {
				e.preventDefault();
				e.stopPropagation();
				$('.drag_navs_more_on').removeClass('drag_navs_more_active')
				$(this).addClass('drag_navs_more_active')
				$('.toolbox').hide(100)
				$(this).parent().find('.toolbox').show(100)
			})
			$(".toolbox").on("mouseleave", function (e) {
				e.preventDefault();
				e.stopPropagation();
				$('.toolbox').hide(100)
			})
			//左边小勾 勾选显示区域
			$('.drag_navs_more_on i').toggle(function () {
				$(this).addClass('selectIs')
				$(this).html('&#xe6cc;')
				let that_ = $(this).parent()[0].id;

				if (that_ === "nav_tableHead") {
					$("#print_tableHead").show(100)
				} else if (that_ === "nav_table") {
					$("#print_table").show(100)
				} else if (that_ === "nav_tableTail") {
					$("#print_tableTail").show(100)
				}
			}, function () {
				$(this).removeClass('selectIs')
				$(this).html('&#xe621;')
				let that_ = $(this).parent()[0].id;
				if (that_ === "nav_tableHead") {
					$("#print_tableHead").hide(100)
				} else if (that_ === "nav_table") {
					$("#print_table").hide(100)
				} else if (that_ === "nav_tableTail") {
					$("#print_tableTail").hide(100)
				}
			})
		}
		//Textarea中获取光标位置
		function getPositionForTextArea(ctrl) {
			var CaretPos = 0;
			if (document.selection) {// IE Support
				ctrl.focus();
				var Sel = document.selection.createRange();
				var Sel2 = Sel.duplicate();
				Sel2.moveToElementText(ctrl);
				var CaretPos = -1;
				while (Sel2.inRange(Sel)) {
					Sel2.moveStart('character');
					CaretPos++;
				}
			} else if (ctrl.selectionStart || ctrl.selectionStart == '0') {// Firefox support
				CaretPos = ctrl.selectionStart;
			}
			return (CaretPos);
		}
		// 获取dpi
		let windowDips;
		windowDipsFun();
		function windowDipsFun() {
			let arrDPI = new Array;

			if (window.screen.deviceXDPI) {
				arrDPI[0] = window.screen.deviceXDPI;
				arrDPI[1] = window.screen.deviceYDPI;
			} else {
				var tmpNode = document.createElement("DIV");
				tmpNode.style.cssText = "width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden";
				document.body.appendChild(tmpNode);
				arrDPI[0] = parseInt(tmpNode.offsetWidth);
				arrDPI[1] = parseInt(tmpNode.offsetHeight);
				tmpNode.parentNode.removeChild(tmpNode);
			}
			if (window.parent.CefGlue && window.parent.CefGlue.getTrueDPI) {
				var scale = window.parent.CefGlue.getScreenScale()
				arrDPI[0] = parseFloat(window.parent.CefGlue.getTrueDPI()) / scale
				arrDPI[1] = arrDPI[0]
			}
			windowDips = arrDPI;
		}
		$('#receipt_width').on('change', function () {
			updatePageSize()
		})
		function setHeight(area) {
			var x = document.getElementById(area)
			x.style.height = `${x.scrollHeight}px`;
		}
		function im2px(val, contentIds, types) {
			let dpi = windowDips;
			let thats_ = $(contentIds);
			if (types === "w") {
				let resultNumber = Number(val) / 25.4 * Number(dpi[0]);
				resultNumber = val * 4;
				thats_.css('width', resultNumber + 'px');
			} else {
				let resultNumber = Number(val) / 25.4 * Number(dpi[1]);
				thats_.css('height', resultNumber + 'px');
			}
		}
		function mm2px(val, direction) {
			let dpi = windowDips;
			if (!val) return ''
			let resultNumber
			if (direction === "w") {
				//resultNumber = Number((Number(val) / 25.4 * Number(dpi[0])).toFixed(4));
				resultNumber = val* 4 ;
			} else {
				resultNumber = Number((Number(val) / 25.4 * Number(dpi[1])).toFixed(4));
			}
			return resultNumber;
		}
		//根据用户所选宽度进行更改--由receipt_width的改变触发
		function updatePageSize() {
			var w;
			w = $('#receipt_width').val()
			SheetTemplate.width = w
			im2px(w, '.preview', 'w')
			im2px(w, '#print_sheet', 'w')
			var width = $("#print_sheet").css("width")
			$('.print-area').css('width', width + 'px')
			setHeight('print_tableHead')
			setHeight('print_table')
			setHeight('print_tableTail')
		}
		console.log('before load template')
		window.loadTemplate(SheetType, TemplateID);
		//type='data'时，用作预览区文本获取 ;type='db'时，用作存储至数据库的htmlContent内容获取
		function save(type){
			setHtmlContentForArea('print_tableHead', 'tableHead',type)
			setHtmlContentForArea('print_table', 'table',type)
			setHtmlContentForArea('print_tableTail', 'tableTail',type)
		}

		//设置预览区内容
		function setPrevieContent(){
			previewContent = headerContent + '<div style="font-size:12px;white-space:break-spaces;word-break:break-all;padding:0px 12px;">' + headContent + '<br>' + bodyContent + '<br>' + tailContent + '</div>' + footerContent;
			$('.preview').css({ "box-shadow": "0 0 20px rgba(0, 0, 0, 0.2)" })
			$('.preview').html(previewContent);
		}

		//将模板保存至数据库
		function saveAndUpload() {
			var printSheet = $("#print_sheet");
			var templateName = $('#templateName').val();
			if (!templateName) {
				bw.toast("请输入模板名称", 5000); return;
			}
			save('db')
			SheetTemplate.tableHead.htmlContent= headContent
			SheetTemplate.table.htmlContent = bodyContent
			SheetTemplate.tableTail.htmlContent = tailContent

			SheetTemplate.pageHead.htmlContent = headerImg
			SheetTemplate.pageTail.htmlContent = footerImg

			var s = JSON.stringify(SheetTemplate)
			var json = JSON.parse(s)
			$.ajax({
				url: "/api/ReceiptTemplate/SaveTemplate", //url地址
				contentType: 'application/json',
				type: "post", //发起请求的方式
				data: JSON.stringify({
					operKey: g_operKey,
					sheet_type: SheetType,
					template_id: TemplateID,
					template_name: templateName,
					template_content: json
				}),
				success: function (res) {
					if (res.newTemplateID) {
						TemplateID = res.newTemplateID;
					}
					bw.toast("保存成功", 1000);
				},
				error: function (res) {
					console.log(res)
				}
			});
		}

		//渲染模板 Param obj:res.template   
		function renderTemplate(obj) {
			let widths = '';
			let sWidth = '';
			//print_sheet背景色白色
			$("#print_sheet").css('background-size', '100% 100%')
			$("#print_sheet").css('background-repeat', 'no-repeat')
			$("#print_sheet").css('background-color', `#fff`)

			// 渲染初始化元素
			if (obj.tableHead) {
				$('#nav_tableHead i').addClass('selectIs').html('&#xe6cc;')
				if(obj.tableHead.htmlContent){
					loadHtmlContentForArea('print_tableHead', 'tableHead',obj.tableHead.htmlContent)
				}
			}
			if (obj.table) {
				$('#nav_table i').addClass('selectIs').html('&#xe6cc;')
				if (obj.table.htmlContent) {
					loadHtmlContentForArea('print_table','table', obj.table.htmlContent)
				}
			}
			if (obj.tableTail) {
				$('#nav_tableTail i').addClass('selectIs').html('&#xe6cc;')
				if (obj.tableTail.htmlContent) {
					loadHtmlContentForArea('print_tableTail','tableTail', obj.tableTail.htmlContent)
				}
			}
			//这里要加载页眉页脚后再save
			if (obj.pageHead) {
				if (obj.pageHead.htmlContent) {
					setHeaderFooterForArea("print_pageHead", obj.pageHead.htmlContent)
					var area = document.getElementById('print_pageHead')
					area.src = obj.pageHead.htmlContent
					headerImg = obj.pageHead.htmlContent

					$('#uploadHead').css('visibility', 'hidden')
					$("#uploadHeadText").attr('hidden', 'hidden')

					$('#print_pageHead').removeAttr("hidden")
					$('#closeHead').css('visibility', 'visible')
				}
			}
			if (obj.pageTail) {
				if (obj.pageTail.htmlContent) {
					setHeaderFooterForArea("print_pageTail", obj.pageTail.htmlContent)
					var area = document.getElementById('print_pageTail')
					area.src = obj.pageTail.htmlContent
					footerImg = obj.pageTail.htmlContent

					$('#uploadTail').css('visibility', 'hidden')
					$("#uploadTailText").attr('hidden', 'hidden')

					$('#print_pageTail').removeAttr("hidden")
					$('#closeTail').css('visibility', 'visible')
				}
			}

			

			//将数据库中存储字段替换为汉字
			function loadHtmlContentForArea(areaID, partName, htmlContent) {
				var text = htmlContent
				var newText = ''
				var rule1 = /{(\s)*[a-zA-Z0-9_-]+(\s)*#?}/     //{  字母   }   TODO:如果后面要改指定随意长度的符号，把这里的'#'替换掉即可
				var rule2 = /[a-zA-Z0-9_-]+/				   //字母/数字_或-

				while (rule1.test(text)) {//若文本中有可匹配项
					var pos = text.search(rule1)
					if (pos == 0) {//若为0，表明{...}在text最前面
						var reps = rule1.exec(text)[0]
						var rep = rule2.exec(reps)[0]
						var flag = false

						AvailElements[partName].some((element) => {
							var word = element.name
							if (word == rep) {
								var addItem = reps.replace(rep, element.title);
								newText += addItem
								flag = true
								return true
							}
						})

						text = text.substring(reps.length)
						if (!flag) newText += text.substring(reps.length)
					} else {
						newText += text.substring(0, pos)
						text = text.substring(pos)
					}
				}
				newText += text
				$('#' + areaID).val(newText)
			}

			if (obj.width) {
				widths = mm2px(obj.width); //px
				sWidth = obj.width   //mm
			} else {
				widths = mm2px(58);
				sWidth = 58
			}
			$('#receipt_width').val(sWidth)
			updatePageSize()
			setHeight('print_tableHead')
			setHeight('print_table')
			setHeight('print_tableTail')
			
			//save 设置预览区
			save('data')
			setPrevieContent()
		}

	})
</script>
