﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.MyCW;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace ArtisanManage.MyJXC
{
    public class SheetRowDisplayAgreement : SheetRowBase
    {
        [SaveToDB] [FromFld] public string items_id { get; set; } = "";
        [SaveToDB] [FromFld] public string items_name { get; set; } = "";
        [SaveToDB] [FromFld] public string unit_no { get; set; } = "";
        [SaveToDB] [FromFld] public string month1_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month1_given { get; set; } = "";
        [SaveToDB] [FromFld] public string month2_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month2_given { get; set; } = "";
        [SaveToDB] [FromFld] public string month3_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month3_given { get; set; } = "";
        [SaveToDB] [FromFld] public string month4_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month4_given { get; set; } = "";
        [SaveToDB] [FromFld] public string month5_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month5_given { get; set; } = "";
        [SaveToDB] [FromFld] public string month6_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month6_given { get; set; } = "";
        [SaveToDB] [FromFld] public string month7_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month7_given { get; set; } = "";
        [SaveToDB] [FromFld] public string month8_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month8_given { get; set; } = "";
        [SaveToDB] [FromFld] public string month9_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month9_given { get; set; } = "";
        [SaveToDB] [FromFld] public string month10_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month10_given { get; set; } = "";
        [SaveToDB] [FromFld] public string month11_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month11_given { get; set; } = "";
        [SaveToDB] [FromFld] public string month12_qty { get; set; } = "";
        [SaveToDB] [FromFld] public string month12_given { get; set; } = "";
        [SaveToDB] [FromFld] public string sub_amount { get; set; }
        [SaveToDB] [FromFld] public string all_given { get; set; } = "";
    }
    public class SheetDisplayAgreement : SheetBase<SheetRowDisplayAgreement> 
    {
        [SaveToDB] [FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB] [FromFld] public string supcust_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string supcust_no { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_addr { get; set; } = "";
        [SaveToDB][FromFld] public string fee_sub_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string fee_sub_name { get; set; } = "";

        [SaveToDB] [FromFld] public override int money_inout_flag { get; set; }
        [SaveToDB] [FromFld] public override decimal total_amount { get; set; }

        [SaveToDB] [FromFld] public string seller_id { get; set; } = "";
        [SaveToDB] [FromFld] public string responsible_worker { get; set; } = "";

        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";
        [SaveToDB] [FromFld] public string start_time { get; set; } = "";
        [SaveToDB] [FromFld] public string end_time { get; set; } = "";
        [SaveToDB] [FromFld] public string adjust_sheet_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string adjust_sheet_no { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string red_sheet_no { get; set; } = "";
        [SaveToDB] [FromFld] public string orig_sheet_id { get; set; } = "";
        [SaveToDB] [FromFld] public  float total_money { get; set; }
        [SaveToDB] [FromFld] public string total_quantity { get; set; } = "";
        [SaveToDB] [FromFld] public string terminate_time { get; set; } = "";
        [SaveToDB] [FromFld] public string terminator_oper { get; set; } = "";
        
        [SaveToDB] [FromFld("t.disp_template_id")] public string disp_template_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string disp_template_name { get; set; } = "";
 
        [SaveToDB] [FromFld] public string sign_work_content { get; set; } = "";
        [SaveToDB] [FromFld] public string reviewer { get; set; } = "";
        [SaveToDB] [FromFld] public string review_time { get; set; } = "";
        [SaveToDB] [FromFld] public string review_comment { get; set; } = "";
        [SaveToDB] [FromFld] public string review_refused { get; set; } = "";
        [SaveToDB] [FromFld] 
        public virtual string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                if (appendix_photos != "")
                {
                    sheetAttribute.Add("appendixPhotos", appendix_photos);
                }

                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set
            {

                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.appendixPhotos != null)
                    {
                        this.appendix_photos = sheetAttr.appendixPhotos;
                    }
                    

                }

            }
        }
        public string appendix_photos { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string reviewerName { get; set; } = "";

        public bool signSendMessage = false;
        protected class CInfoForApprove : CInfoForApproveBase
        { 
           
        }
        public SheetDisplayAgreement(LOAD_PURPOSE loadPurpose) : base("display_agreement_main", "display_agreement_detail",loadPurpose)
        {
            sheet_type =SHEET_TYPE.SHEET_DISPLAY_AGREEMENT;
            if (loadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin =   @$"
left join info_supcust c on t.supcust_id=c.supcust_id
left join (select oper_id,oper_name as seller_name from info_operator) seller on t.seller_id=seller.oper_id
left join (select oper_id,oper_name as reviewerName from info_operator) reviewer on t.reviewer = reviewer.oper_id
left join (select oper_id,oper_name as maker_name from info_operator) maker on t.maker_id=maker.oper_id
left join (select oper_id,oper_name as approver_name from info_operator) approver on t.approver_id=approver.oper_id
left join (select oper_id,oper_name as terminator_oper from info_operator) terminator on t.terminator_oper=terminator.oper_id
left join (select sub_id,sub_name as fee_sub_name from cw_subject) fee on t.fee_sub_id=fee.sub_id
left join (select sheet_id as red_sheet_id1,sheet_no as red_sheet_no from display_agreement_main where company_id=~company_id ) a on a.red_sheet_id1 = t.red_sheet_id
left join (select sheet_id as adjust_sheet_id1,sheet_no as adjust_sheet_no from display_agreement_main where company_id=~company_id ) ad on ad.adjust_sheet_id1 = t.adjust_sheet_id
left join info_display_template tmp on t.company_id=tmp.company_id and t.disp_template_id=tmp.disp_template_id
            
";
                DetailLeftJoin = "";
            }
            else if (loadPurpose == LOAD_PURPOSE.APPROVE)
                DetailLeftJoin = @$"";
        }

        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.sheet_id + "_" + this.supcust_id + "_" + this.OperID + "_" + "_" + this.make_brief;
            foreach (var row in SheetRows)
            {
                res += row.items_id + "_" + row.month1_qty + "_" + row.month2_qty + row.month3_qty + row.month4_qty + row.month5_qty + row.month6_qty + row.month7_qty + row.month8_qty + row.month9_qty + row.month10_qty + row.month11_qty + row.month12_qty;
            }
            return res;
        }
        protected override void InitForSave()
        {
            base.InitForSave();
            if (seller_id == "") seller_id = OperID;
            if (responsible_worker == "") responsible_worker = seller_id;
            
        }

        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            string sql = "";
            base.GetInfoForApprove_SetQQ(QQ);
            if (red_sheet_id != "")
            {
                //找出orig_sheet_id
                sql = $"select * from display_agreement_main where company_id = {company_id} and sheet_id = {red_sheet_id}";
                QQ.Enqueue("origSheet", sql);
                sql = @$"select distinct d.item_id as given_item_id,item_name as given_item_name 
                            from sheet_sale_detail d 
                            left join sheet_sale_main m on m.sheet_id = d.sheet_id 
                            left join info_item_prop p on p.item_id = d.item_id and p.company_id = d.company_id
                            where d.company_id = {company_id} and disp_sheet_id = {red_sheet_id} and approve_time is not null and red_flag is null";
                QQ.Enqueue("dispGivenItems", sql);
            }
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
          

            if (sqlName == "origSheet")
            {
                dynamic record = CDbDealer.Get1RecordFromDr(dr, false);
                if (record != null)
                {
                    if(record.adjust_sheet_id!="")
                    {
                        info.ErrMsg = "被调整单据已作后续调整，请勿重复调整";
                        return;
                    }
                    orig_sheet_id = record.orig_sheet_id;
                    if (record.orig_sheet_id == "") orig_sheet_id = red_sheet_id;
                }
            }
            
            if(sqlName== "dispGivenItems")
            {
                dynamic record = CDbDealer.GetRecordsFromDr(dr, false);
                if (record != null)
                {
                    var itemsID = "";
                    foreach (SheetRowDisplayAgreement row in SheetRows)
                    {
                        if (itemsID != "") itemsID += ",";
                        itemsID += row.items_id;
                    }
                    foreach(var rec in record)
                    {
                        var givenItemID = rec.given_item_id;
                        var givenItemName = rec.given_item_name;
                        if (itemsID.IndexOf(givenItemID) == -1)
                        {
                            info.ErrMsg = $"商品{givenItemName}在销售单中已兑付，调整单中不能删除该商品";
                            return;
                        }
                    }
                }
            }
        }

		public async Task ProcessPcAppendix()
		{
			string images = appendix_photos;
			if (!string.IsNullOrEmpty(images) && images != "[]" && images.Contains("photos"))
			{
				// 使用 Newtonsoft.Json 解析 JSON 字符串
				var jsonObject = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(images);

				// 提取 photos 列表
				List<string> photos = jsonObject["photos"];
				appendix_photos = await ProcessAppendixPicsRetDBStr(photos);
			}
		}

		public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, company_id);
            return result;
        }
        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            if (supcust_id == "") return "必须指定客户";
            if (seller_id == "" && IsFromWeb) return "必须指定业务员";
            if (fee_sub_id == "") return "必须指定费用支出账户";
            List<string> qtyArrKey = new List<string>();

            Decimal calcQty(string qty)
            {
                if (string.IsNullOrEmpty(qty))
                {
                    return 0;
                }

                return Decimal.Parse(qty);
            }

            Decimal calcTotalAmount = 0;
            foreach(var row in SheetRows)
            {
                if (string.IsNullOrEmpty(row.unit_no))
                {
                    return  $"{row.items_name}必须指定单位";
                }
                if (!row.sub_amount.IsValid())
                {
                    row.sub_amount = "0";
                }

                Decimal subAmount = Decimal.Parse(row.sub_amount);
                Decimal sumSubAmout =
                    calcQty(row.month1_qty) + calcQty(row.month2_qty) + calcQty(row.month3_qty) +
                    calcQty(row.month4_qty) + calcQty(row.month5_qty) + calcQty(row.month6_qty) +
                    calcQty(row.month7_qty) + calcQty(row.month8_qty) + calcQty(row.month9_qty) +
                    calcQty(row.month10_qty) + calcQty(row.month11_qty) + calcQty(row.month12_qty);
                if (subAmount != sumSubAmout)
                {
                    return  $"{row.items_name}数量与小计不同";
                }

                if (sumSubAmout == 0)
                {
                    row.all_given = "true";
                }

                calcTotalAmount += sumSubAmout;
            }
            
            Boolean settleFlag = true;
            foreach (var row in SheetRows)
            {
                if (row.all_given != "true")
                {
                    settleFlag = false;
                }
            }

            if (settleFlag)
            {
                return  $"商品/钱的数量不能全部为0";
            }
            return "OK";
        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info)
        {
            await base.OnSheetIDGot(cmd, sheetID, info);
            string sql = "";
            if (red_sheet_id!="") sql = $"update display_agreement_main set adjust_sheet_id = {sheetID},red_flag = 1 where company_id = {company_id} and sheet_id = {red_sheet_id}";

            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
        }

        public async Task<string> Adjust(CMySbCommand cmd, string companyID, string sheetID, string rederID, bool bAutoCommit = true)
        {
            string sError = "";
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync($"select terminate_time from display_agreement_main where sheet_id={sheetID}", cmd);
            if (data!=null&&data.terminate_time != "")
            {
                sError = "该单据已被终止，请勿调整";
                goto err;
            }
            dynamic record = await CDbDealer.GetRecordsFromSQLAsync($"select * from display_agreement_detail where sheet_id={sheetID}", cmd);
            if (record != null && sError == "")
            {
                foreach (var rec in record)
                {
                    float given = 0;
                    foreach (KeyValuePair<string, object> col in rec)
                    {
                        if (col.Key.IndexOf("_given") >= 0)
                        {
                            if (!string.IsNullOrWhiteSpace(col.Value.ToString())) given = Convert.ToSingle(col.Value);
                            if (given > 0)
                            {
                                sError = $"商品{rec.items_name}已被使用，请勿红冲";
                                goto err;
                            }
                        }
                    }
                }
            }
        err:
            if (sError == "")
            {
                object ov = await cmd.ExecuteScalarAsync();
                string sql = $"update {MainTable} set red_flag = '1',adjust_sheet_id='{sheetID}' where  company_id={companyID} and sheet_id={red_sheet_id};";
                cmd.CommandText = sql;
                CMySbTransaction tran = null;
                try
                {
                    if (bAutoCommit)
                        tran = cmd.Connection.BeginTransaction();

                    ov = await cmd.ExecuteScalarAsync();

                    if (tran != null) tran.Commit();
                }
                catch (Exception e)
                {
                    if (tran != null)
                        tran.Rollback();
                    return e.Message;
                }
            }
            return sError;
        }

        public async virtual Task<string> Terminate(CMySbCommand cmd, string companyID, string sheetID, string terminator, bool bAutoCommit = true)
        {
            string sError = "";
            if (sheetID == "")
            {
                sError = "终止必须指定单据号"; return sError;
            }
            string sql = $"select sheet_id from {MainTable} where company_id={companyID} and sheet_id={sheetID} and approve_time is not null and red_flag is null";
            cmd.CommandText = sql;
            object ov = await cmd.ExecuteScalarAsync();
            if (ov == null)
            {
                return "该单据已被调整/终止,不能终止";
            }
            terminate_time = CPubVars.GetDateText(DateTime.Now);
            sql = $"update {MainTable} set red_flag = '1',terminate_time='{terminate_time}',terminator_oper={terminator} where  company_id={companyID} and sheet_id={sheetID};";
            cmd.CommandText = sql;
            CMySbTransaction tran = null;
            try
            {
                if (bAutoCommit)
                    tran = cmd.Connection.BeginTransaction();

                ov = await cmd.ExecuteScalarAsync();

                if (tran != null) tran.Commit();
            }
            catch (Exception e)
            {
                if (tran != null)
                    tran.Rollback();
                return e.Message;
            }
            return sError;
        }


		public override async Task<string> OnSheetBeforeSave(CMySbCommand cmd, CInfoForApproveBase info)
		{
            // 校验同一个模板同一个客户
            string templateGiveUniqueness = await ValidateTemplateGiveUniqueness(cmd);
            if (!string.IsNullOrEmpty(templateGiveUniqueness))
            {
                return templateGiveUniqueness;
            }
            return "";
           
        }

      

        /// <summary>
        /// 重复性兑付开单校验
        /// </summary>
        /// <param name="cmd"></param>
        /// <param name="companyID"></param>
        /// <param name="supcustID"></param>
        /// <param name="dispTemplateID"></param>
        /// <param name="sheetStartTime">yyyy-MM</param>
        /// <param name="sheetEndTime">yyyy-MM</param>
        /// <param name="originSheetID"></param>
        /// <returns></returns>
        public async Task<string> ValidateTemplateGiveUniqueness(CMySbCommand cmd)
        {
            string companyID = this.company_id; 
            string supcustID = this.supcust_id; 
            string dispTemplateID = this.disp_template_id; 
            string sheetStartMonth = this.start_time.Substring(0, 7); 
            string sheetEndMonth = this.end_time.Substring(0, 7); 
            // 无模板情况不进行处理
            if (dispTemplateID == "")
            {
                return "";
            }
            // 查询出所有终止和正常的历史单据。
            string condi = $@"";

            if (!string.IsNullOrEmpty(this.sheet_id))
            {
                // 二次保存审核或调整单二次保存审核
                condi = @$" AND dad.sheet_id != {this.sheet_id} ";
                if (!string.IsNullOrEmpty(this.red_sheet_id))
                {
                    // 调整单二次保存审核
                    condi += @$" AND dad.sheet_id != {this.red_sheet_id} ";
                }
            }
            else if (!string.IsNullOrEmpty(this.red_sheet_id))
            {
                // 调整单首次保存
                condi = @$" AND dad.sheet_id != {this.red_sheet_id} ";
            }

            string sql = @$"
select dam.sheet_id,
       dam.sheet_no,
       to_char(dam.start_time, 'YYYY-MM') AS start_time,
       to_char(dam.end_time, 'YYYY-MM') AS end_time,
       dam.red_flag,
       dam.red_sheet_id,
       sum(COALESCE(ABS(month1_given), 0))  as month1_given,
       sum(COALESCE(ABS(month2_given), 0))  as month2_given,
       sum(COALESCE(ABS(month3_given), 0))  as month3_given,
       sum(COALESCE(ABS(month4_given), 0))  as month4_given,
       sum(COALESCE(ABS(month5_given), 0))  as month5_given,
       sum(COALESCE(ABS(month6_given), 0))  as month6_given,
       sum(COALESCE(ABS(month7_given), 0))  as month7_given,
       sum(COALESCE(ABS(month8_given), 0))  as month8_given,
       sum(COALESCE(ABS(month9_given), 0))  as month9_given,
       sum(COALESCE(ABS(month10_given), 0)) as month10_given,
       sum(COALESCE(ABS(month11_given), 0)) as month11_given,
       sum(COALESCE(ABS(month12_given), 0)) as month12_given
from display_agreement_detail dad
         left join display_agreement_main dam on dam.company_id = {companyID} 
                                                     and dam.supcust_id = {supcustID} 
                                                     and dam.disp_template_id = {dispTemplateID} 
                                                     and dad.sheet_id = dam.sheet_id
where dad.company_id = {companyID}
  and dam.company_id = {companyID}
  and dam.supcust_id = {supcustID}
  and dam.disp_template_id = {dispTemplateID} 
  {condi}
group by dam.sheet_id, dam.sheet_no, dam.start_time, dam.end_time, dam.red_flag, dam.red_sheet_id
";
            List<ValidateDisplayAgreementTemplate> validateLst = await CDbDealer.GetRecordsFromSQLAsync<ValidateDisplayAgreementTemplate>(sql, cmd);
            if (validateLst == null)
            {
                return "";
            }
            // 获取不可用月份集合
            HashSet<string> unavailableMonths = new HashSet<string>();
            foreach (ValidateDisplayAgreementTemplate record in validateLst)
            {
                Type type = record.GetType();
                List<string> monthList = GetMonthRange(record.start_time, record.end_time);
                // 如果是终止单据，整个时间段的月份都不可用
                if (record.red_flag == 1)
                {
                    for (int i = 0; i <= monthList.Count; i++)
                    {
                        string fieldName = $"month{i + 1}_given";
                        PropertyInfo property = type.GetProperty(fieldName);
                        if (property != null)
                        {
                            decimal value = (decimal)property.GetValue(record);
                            if (value != 0)
                            {
                                unavailableMonths.Add(monthList[i]);
                            }
                        }
                    }
                }
                else
                {
                    unavailableMonths.UnionWith(monthList);
                }
            }
            // 获取开单要的集合
            HashSet<string> needMonths = new HashSet<string>(GetMonthRange(sheetStartMonth, sheetEndMonth));
            // 计算交集
            HashSet<string> intersection = new HashSet<string>(needMonths);
            intersection.IntersectWith(unavailableMonths);
            // 判断交集是否有元素
            if (intersection.Count > 0)
            {
                // 返回存在的元素
                return "该模板已有重复月份" + string.Join(", ", intersection);
            }
            // 返回空字符串
            return "";
        }
        /// <summary>
        /// yyyy-MM
        /// </summary>
        /// <param name="start">yyyy-MM</param>
        /// <param name="end">yyyy-MM</param>
        /// <returns></returns>
        public List<string> GetMonthRange(string start, string end)
        {
            List<string> monthList = new List<string>();
            DateTime startDate = DateTime.ParseExact(start, "yyyy-MM", null);
            DateTime endDate = DateTime.ParseExact(end, "yyyy-MM", null);

            while (startDate <= endDate)
            {
                monthList.Add(startDate.ToString("yyyy-MM"));
                startDate = startDate.AddMonths(1);
            }

            return monthList;
        }
    }
    public class ValidateDisplayAgreementTemplate
    {
        public int sheet_id { get; set; }        // 表单ID
        public string sheet_no { get; set; }     // 表单编号
        public string start_time { get; set; } // 开始时间
        public string end_time { get; set; }   // 结束时间
        public short? red_flag { get; set; }       // 红标志 (1表示终止)
        public int? red_sheet_id { get; set; }   // 终止的表单ID

        // 各个月的应兑和已兑数量
        // public decimal month1_qty { get; set; }    // 第一个月应兑数量
        public decimal month1_given { get; set; }  // 第一个月已兑数量
        // public decimal month2_qty { get; set; }    // 第二个月应兑数量
        public decimal month2_given { get; set; }  // 第二个月已兑数量
        // public decimal month3_qty { get; set; }    // 第三个月应兑数量
        public decimal month3_given { get; set; }  // 第三个月已兑数量
        // public decimal month4_qty { get; set; }    // 第四个月应兑数量
        public decimal month4_given { get; set; }  // 第四个月已兑数量
        // public decimal month5_qty { get; set; }    // 第五个月应兑数量
        public decimal month5_given { get; set; }  // 第五个月已兑数量
        // public decimal month6_qty { get; set; }    // 第六个月应兑数量
        public decimal month6_given { get; set; }  // 第六个月已兑数量
        // public decimal month7_qty { get; set; }    // 第七个月应兑数量
        public decimal month7_given { get; set; }  // 第七个月已兑数量
        // public decimal month8_qty { get; set; }    // 第八个月应兑数量
        public decimal month8_given { get; set; }  // 第八个月已兑数量
        // public decimal month9_qty { get; set; }    // 第九个月应兑数量
        public decimal month9_given { get; set; }  // 第九个月已兑数量
        // public decimal month10_qty { get; set; }   // 第十个月应兑数量
        public decimal month10_given { get; set; } // 第十个月已兑数量
        // public decimal month11_qty { get; set; }   // 第十一个月应兑数量
        public decimal month11_given { get; set; } // 第十一个月已兑数量
        // public decimal month12_qty { get; set; }   // 第十二个月应兑数量
        public decimal month12_given { get; set; } // 第十二个月已兑数量
    }


}
