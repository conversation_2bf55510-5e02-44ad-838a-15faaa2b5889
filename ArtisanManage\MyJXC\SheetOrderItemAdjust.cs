﻿using ArtisanManage.Models;
using ArtisanManage.MyCW;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetRowOrderItemAdjust:SheetRowBase
    {
        [SaveToDB] [FromFld] public string item_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_name { get; set; } = "";
        [SaveToDB] [FromFld] public string unit_no { get; set; } = "";
        [SaveToDB] [FromFld] public decimal unit_factor { get; set; } = 1;
        [SaveToDB] [FromFld] public decimal quantity { get; set; }
        [SaveToDB] [FromFld] public decimal real_price { get; set; }
        [SaveToDB] [FromFld] public decimal sub_amount { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_unit_no { get; set; }
       public string s_unit_qty { 
            get
            {
                var row = new SheetRowItem { item_id=item_id,  b_unit_factor=b_unit_factor,m_unit_factor=m_unit_factor};
                SheetBase<SheetRowBase>.GetUnitQtyFromSmallUnitQty(row, quantity * unit_factor,out decimal b_qty,out decimal m_qty,out decimal s_qty);
                return s_qty.ToString();
            }
        }
        
        public string m_unit_qty
        {
            get
            {
                var row = new SheetRowItem { item_id = item_id, b_unit_factor = b_unit_factor, m_unit_factor = m_unit_factor };
                SheetBase<SheetRowBase>.GetUnitQtyFromSmallUnitQty(row, quantity * unit_factor, out decimal b_qty, out decimal m_qty, out decimal s_qty);
                return m_qty.ToString();
            }
        }
 
        public string b_unit_qty
        {
            get
            {
                var row = new SheetRowItem { item_id = item_id, b_unit_factor = b_unit_factor, m_unit_factor = m_unit_factor };
                SheetBase<SheetRowBase>.GetUnitQtyFromSmallUnitQty(row, quantity * unit_factor, out decimal b_qty, out decimal m_qty, out decimal s_qty);
                return b_qty.ToString();
            }
        }
 
        public string unit_conv
        {
            get
            {
                string r = "";
                if (m_unit_factor.IsValid())
                {

                    r = $"1{b_unit_no}={CPubVars.FormatMoney(Convert.ToDouble(b_unit_factor) / Convert.ToDouble(m_unit_factor), 0)}{m_unit_no}={b_unit_factor}{s_unit_no}";
                }
                else
                {

                    r = $"1{b_unit_no}={b_unit_factor}{s_unit_no}";
                }
                return r;
            }
        }

        
        public string b_unit_price
        {
            get
            {
                if (b_unit_factor != "" && b_unit_factor != null )
                    return CPubVars.FormatMoney(real_price / unit_factor * CPubVars.ToDecimal(b_unit_factor), 2);
                else return "";
            }
        }
     
        public string m_unit_price
        {
            get
            {
                if (m_unit_factor != "" && m_unit_factor != null)
                    return CPubVars.FormatMoney(real_price / unit_factor * CPubVars.ToDecimal(m_unit_factor), 2);
                else return "";
            }
        }
 
        public string s_unit_price
        {
            get
            {
                 return CPubVars.FormatMoney(real_price / unit_factor, 4);
               
            }
        }


        //   [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_qty { get; set; }
        //   [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_qty { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_factor { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_factor { get; set; }
      //  [FromFld(LOAD_PURPOSE.SHOW)] public string s_unit_price { get; set; }
      //  [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_price { get; set; }
      //  [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_price { get; set; }
       // [FromFld(LOAD_PURPOSE.SHOW)] public string unit_conv { get; set; }
        public decimal old_quantity { get; set; }
        public decimal old_real_price { get; set; }
        public decimal old_sub_amount { get; set; }
        public string oper_type = "";
        public bool HasBalance = false;
        public bool OldItemHasBalance = false;
        public decimal balance = 0;
        public decimal OldItembalance = 0;

    }
    public class SheetOrderItemAdjust : SheetBase<SheetRowOrderItemAdjust>
    {
        [SaveToDB] [FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB] [FromFld] public string supcust_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_name { get; set; } = "";
        [SaveToDB] [FromFld] public string seller_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";
        [SaveToDB] [FromFld] public string prepay_sub_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string prepay_sub_name { get; set; } = ""; 
        [SaveToDB] [FromFld] public decimal prepay_balance { get; set; }
          
        public decimal diff_amount { get { return prepay_balance - total_amount; } }

        [SaveToDB] [FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB] [FromFld] public decimal now_disc_amount { get; set; }
        [SaveToDB] [FromFld] public override decimal total_amount { get; set; }
        [SaveToDB] [FromFld] public string payway1_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_name { get; set; } = "";
        [SaveToDB] [FromFld] public decimal payway1_amount { get; set; }
        [SaveToDB] [FromFld] public string payway2_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_name { get; set; } = "";
        [SaveToDB] [FromFld] public decimal payway2_amount { get; set; }
      

        public SheetOrderItemAdjust(LOAD_PURPOSE loadPurpose) : base("sheet_item_ordered_adjust_main", "sheet_item_ordered_adjust_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_ORDER_ITEM_ADJUST;
            if(loadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @"
left join info_supcust c on t.company_id = c.company_id and t.supcust_id=c.supcust_id "+
//left join (select supcust_id,prepay_sub_id,item_id as old_item_id,unit_no as old_unit_no,quantity as old_quantity,order_price as old_real_price,quantity * order_price as old_sub_amount from items_ordered_balance where company_id=~COMPANY_ID) ob on ob.supcust_id = t.supcust_id and ob.prepay_sub_id = ob.prepay_sub_id
@"left join (select oper_id,oper_name as seller_name from info_operator where company_id=~COMPANY_ID) seller on t.seller_id=seller.oper_id
left join (select oper_id,oper_name as maker_name from info_operator where company_id=~COMPANY_ID) maker on t.maker_id=maker.oper_id
left join (select oper_id,oper_name as approver_name from info_operator where company_id=~COMPANY_ID) approver on t.approver_id=approver.oper_id
left join (select sub_id,sub_name as prepay_sub_name from cw_subject where company_id=~COMPANY_ID) pre on t.prepay_sub_id=pre.sub_id
left join (select sub_id,sub_name as payway1_name from cw_subject where company_id=~COMPANY_ID) pw1 on t.payway1_id=pw1.sub_id
left join (select sub_id,sub_name as payway2_name from cw_subject where company_id=~COMPANY_ID) pw2 on t.payway2_id=pw2.sub_id
                                  
                ";

                /*DetailLeftJoin = @"left join info_item_prop i on t.item_id=i.item_id  
                                   left join (select item_id,real_quantity,
                                            b_unit_no,m_unit_no,s_unit_no,m_unit_factor,b_unit_factor,s_unit_qty,m_unit_qty,b_unit_qty,b_unit_price,m_unit_price,s_unit_price,unit_conv   
                                    from (
                                           SELECT d.item_id,real_quantity,
				                                   (CASE WHEN b_unit_no is not null then sign(COALESCE(qty,0))*floor(COALESCE(abs(qty),0) / b_unit_factor) end) as b_unit_qty,b_unit_no,
                                                   (CASE WHEN m_unit_no is null THEN null ELSE sign(COALESCE(qty,0))*floor((COALESCE(abs(qty),0)%b_unit_factor)/m_unit_factor) END) as m_unit_qty,m_unit_no,
                                                   (CASE WHEN b_unit_no is NOT NULL AND m_unit_no is NOT NULL THEN sign(qty)*floor(COALESCE(abs(qty),0)%b_unit_factor%m_unit_factor)
			                                             WHEN b_unit_no is NOT NULL AND m_unit_no is NULL THEN round(cast(qty % b_unit_factor as numeric),2)
                                                         WHEN b_unit_no is NULL AND m_unit_no is NULL THEN round(qty,2) END) s_unit_qty,s_unit_no,
                                                   (CASE WHEN b_unit_no is not null then real_price end) as b_unit_price,
                                                   (CASE WHEN m_unit_no is null THEN null ELSE round(cast(price_unit*m_unit_factor as numeric),2) END) as m_unit_price, 
                                                   round(price_unit,2) s_unit_price,m_unit_factor,b_unit_factor,
                                                   (case when m_unit_factor is null and b_unit_factor is not null then concat('1',b_unit_no,'=',b_unit_factor,s_unit_no)  
			                                             when (b_unit_factor is not null) and (m_unit_factor is not null) then concat('1',b_unit_no,'=',floor(b_unit_factor::numeric/m_unit_factor::numeric),m_unit_no,'=',b_unit_factor,s_unit_no)
							                             when b_unit_factor is null then concat('1',s_unit_no)  end ) as unit_conv
                                            FROM (select item_id, (m->>'f1')::numeric m_unit_factor,(b->>'f1')::numeric b_unit_factor,
                                                                s->>'f2' s_unit_no,m->>'f2' m_unit_no,b->>'f2' b_unit_no,s->>'f3' s_barcode,m->>'f3' m_barcode,b->>'f3' b_barcode             
                                                    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode)) as json 
                                                     from info_item_multi_unit ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$)  as errr(item_id int, s jsonb,m jsonb, b jsonb)) t 
                                    right JOIN (select item_id,(quantity*unit_factor)::numeric qty,quantity real_quantity,real_price,(real_price/unit_factor)::numeric price_unit from sheet_item_ordered_adjust_detail where company_id=~company_id and sheet_id=~sheet_id) d on d.item_id = t.item_id
                                ) tem) dd on dd.item_id = t.item_id and dd.real_quantity=t.quantity

                ";
                */

                DetailLeftJoin = @" 
left join info_item_prop i on t.company_id = i.company_id and t.item_id=i.item_id  
left join 
(  
    select item_id, (m->>'f1')::numeric m_unit_factor,(b->>'f1')::numeric b_unit_factor, s->>'f2' s_unit_no,m->>'f2' m_unit_no,b->>'f2' b_unit_no,s->>'f3' s_barcode,m->>'f3' m_barcode,b->>'f3' b_barcode             
    from crosstab(
        'select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode)) as json 
        from info_item_multi_unit where company_id=~COMPANY_ID ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$)  as errr(item_id int, s jsonb,m jsonb, b jsonb
    )
) dd on t.item_id = dd.item_id
";  
            }
           // else if (loadPurpose == LOAD_PURPOSE.APPROVE)
             //   DetailLeftJoin = @$" left join sheet_item_ordered_adjust_main m on  t.sheet_id=m.sheet_id ";
        }
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.sheet_id + "_" + this.supcust_id + "_" + this.OperID + "_" + "_" + this.make_brief + "_" + this.payway1_id + "_" + this.payway1_amount + "_" + (this.payway2_amount != 0 ? this.payway2_id + "_" + this.payway2_amount + "_" : "") ;
            foreach (var row in SheetRows)
            {
                res += row.item_id + "_" + row.item_name + "_" + row.quantity + row.real_price + row.remark;
            }
            return res;
        }
        protected override void InitForSave()
        {
            base.InitForSave();
            if (seller_id == "") seller_id = OperID;
            if (approver_id == "") approver_id = OperID;
            

        }

        public decimal DoNullString(dynamic x)
        {
            decimal y = 0;
        
            string v = (x== "" ? "0" : x);
            y=Decimal.Parse(v, System.Globalization.NumberStyles.Float); 

            return y;
        }

        protected class CInfoForApprove : CInfoForApproveBase
        {
            public List<SheetRowOrderItemAdjust> SheetRows = null;
            public decimal OrderSubBalance=0;
            public List<Subject> PaywaySubjects = new List<Subject>();
            public List<Subject> PrepaySubjects = new List<Subject>();
        }
        protected class Subject
        {
            public string sub_id { get; set; }
            public string sub_name { get; set; }
            public string balance { get; set; }
            public string sub_type { get; set; }
        }
        protected override void NeedUpdateClientHistory(out string supcustID, out bool updateArrears, out string updatePrepaySubIDs)
        {
            supcustID = supcust_id;
            updateArrears = true;
            updatePrepaySubIDs = "";
            if(prepay_sub_id!="")
            {
                if (updatePrepaySubIDs != "") updatePrepaySubIDs += ',';
                updatePrepaySubIDs += prepay_sub_id;
            }
            if (prepay_sub_id != "")
            {
                if (updatePrepaySubIDs != "") updatePrepaySubIDs += ',';
                updatePrepaySubIDs += payway1_id;
            }
            if (prepay_sub_id != "")
            {
                if (updatePrepaySubIDs != "") updatePrepaySubIDs += ',';
                updatePrepaySubIDs += payway2_id;
            }

        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql;
            base.GetInfoForApprove_SetQQ(QQ);
            sql = $"select c.sub_id,balance from cw_subject c left join prepay_balance b on c.sub_id = b.sub_id where c.company_id={company_id} and c.sub_id={prepay_sub_id} and supcust_id={supcust_id}";
            QQ.Enqueue("balance", sql);
            sql = $@"select item_id,unit_no,unit_factor,order_price,quantity as old_quantity,balance as old_sub_amount,'' oper_type 
                           from items_ordered_balance where supcust_id = {supcust_id} and prepay_sub_id = {prepay_sub_id}";
            QQ.Enqueue("old_items", sql);
           
            if (Math.Abs(now_pay_amount) >= 0.01m)
            {
                string sub_ids = payway1_id;
                if (payway2_id != "")
                {
                    if (sub_ids != "") sub_ids += ","; sub_ids += payway2_id;
                }
                sql = $@"select c.sub_id, c.sub_type, c.sub_name, coalesce(balance,0) balance from cw_subject c left join (select * from  prepay_balance where company_id={company_id} and supcust_Id={supcust_id}) b on c.sub_id=b.sub_id where c.sub_id in ({sub_ids}) and c.company_id = {company_id} and c.sub_type ='YS' ";
                QQ.Enqueue("payway_sub", sql);
            }
            sql = $"select s.sub_id,sub_name,balance,sub_type from cw_subject s left join (select sub_id,balance from prepay_balance where supcust_id={supcust_id}) b on s.sub_id=b.sub_id  where sub_type in ('YS','YF');";
            QQ.Enqueue("prepay_sub", sql);

        }

        public List<SheetRowOrderItemAdjust> MergedSheetRows = null;
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {

            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if(sqlName == "balance")
            {
                dynamic record = CDbDealer.Get1RecordFromDr(dr, false);
                if (record != null)
                {
                    decimal bal= CPubVars.ToDecimal(record.balance);
                    if (bal != prepay_balance)
                    {
                        info.ErrMsg = "定货款余额已更新，请重新打开页面";
                        return;
                    }
                   // if (record.balance != "") prepay_balance = Convert.ToSingle(record.balance);
                   // else prepay_balance = 0;
                }
                
            }
            var mergedRows = MergeSheetRows(this.SheetRows);
            if (sqlName == "old_items")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (SheetRowOrderItemAdjust row in mergedRows)
                {
                    if(row.item_id!= "")
                    {
                        dynamic itemInRec = null;
                        foreach(dynamic rec in records)
                        {
                            if(rec.item_id==row.item_id && rec.unit_factor==row.unit_factor.ToString() && CPubVars.FormatMoney(rec.order_price, 4) == CPubVars.FormatMoney(row.real_price, 4))
                            {
                                itemInRec = rec;  break;
                            }
                        }
                         
                        if (itemInRec == null) row.oper_type = "add";
                        else
                        {
                            row.oper_type = "same";
                            if (DoNullString(itemInRec.old_quantity) != row.quantity)
                            {
                                row.oper_type = "change";
                            }                            
                            row.old_quantity = DoNullString(itemInRec.old_quantity);
                            row.old_sub_amount = DoNullString(itemInRec.old_sub_amount);
                        }
                    }
                }


                foreach (dynamic rec in records)
                {
                    if(rec.item_id != "")
                    {
                        SheetRowOrderItemAdjust itemInRow = null;
                        foreach (var row in mergedRows)
                        {

                            if (rec.item_id == row.item_id && rec.unit_factor == row.unit_factor.ToString() && CPubVars.FormatMoney(rec.order_price, 4) == CPubVars.FormatMoney(row.real_price, 4)) 
                            {
                                itemInRow = row; break;
                            }
                        }

                        if (itemInRow == null)
                        {
                            SheetRowOrderItemAdjust addRow = new SheetRowOrderItemAdjust();
                            addRow.item_id = rec.item_id;
                            addRow.unit_no = rec.unit_no;
                            addRow.unit_factor = DoNullString(rec.unit_factor);
                            addRow.old_quantity = DoNullString(rec.old_quantity);
                            addRow.old_real_price = DoNullString(rec.order_price);
                            addRow.old_sub_amount = DoNullString(rec.old_sub_amount); 
                            addRow.oper_type = "delete";
                            mergedRows.Add(addRow);
                        }
                    }
                }
                info.SheetRows = mergedRows;
                MergedSheetRows = mergedRows;
            }
            else if (sqlName == "payway_sub")
            {
                info.PaywaySubjects = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
            else if (sqlName == "prepay_sub")
            {
                info.PrepaySubjects = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
            }
        }

        //判断是否包含重复商品
        protected bool HasDuplicateValues(dynamic arr)
        {
            ISet<string> set = new HashSet<string>();
            for (var i = 0; i < arr.Count; i++)
            {
                set.Add(arr[i]); 
            }
            return set.Count != arr.Count;
        }

        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            var check =await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
            if (seller_id == "" && IsFromWeb) return "必须指定业务员";
            if (SheetRows.Count == 0) return "必须指定商品行";
            //if (total_amount - prepay_balance>0 ) return "调整后的余额不能超过现有余额";
            decimal total_sub_amount = 0;
            Dictionary<string, SheetRowOrderItemAdjust> dic = new Dictionary<string, SheetRowOrderItemAdjust>();

            foreach (var row in SheetRows)
            {
                total_sub_amount += row.sub_amount; 

                string key = row.item_id + "_" + "_" + row.unit_no + "_" + row.real_price.ToString();

                if (dic.ContainsKey(key))
                {
                    if(row.quantity>=0.001m)
                      return $"{row.item_name}价格{row.real_price}存在重复行";
                }
                else
                    dic.Add(key, row);
            }

            if (prepay_sub_id!="-1" && Math.Abs(total_sub_amount - total_amount) >= 0.05m) return "明细行合计与总额不等，请检查";
            if(Math.Abs(diff_amount - now_disc_amount - now_pay_amount) > 0.05m)
            {
                return "差异金额 应该 = 忽略金额 + 转移金额";
            }
            if (now_pay_amount != 0 && Math.Abs(now_pay_amount - payway1_amount - payway2_amount) > 0.01m) return "转移金额与支付金额不等";
            if (payway1_amount!=0 && payway1_id == "") return "请指定转移金额的支付账户";
            if (payway1_id == payway2_id && payway2_amount != 0) return "请选择不同支付账户";
           
            //List<string> items_id = new List<string>();
            //foreach (SheetRowOrderItemAdjust row in SheetRows)
            //{
            //    items_id.Add(row.item_id+'_'+row.real_price);
            //}
            //if (HasDuplicateValues(items_id)) return "同一商品，请重新调整";


            return "OK";
        }

        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
            int inout_flag = -1;
            if (red_flag != "") inout_flag *= (-1);//定货会调整单不能红冲
            ClearExecSQL();
            AddExecSQL($"delete from items_ordered_balance where company_id={company_id} and supcust_id={supcust_id} and prepay_sub_id = {prepay_sub_id};");
           
            
            foreach (SheetRowOrderItemAdjust row in MergedSheetRows)
            {
                if (row.item_id.IsValid() && row.oper_type!="delete")
                {
                    if (red_flag.IsInvalid())
                    {
                        AddExecSQL(@$"
insert into items_ordered_balance
        (company_id, prepay_sub_id,   supcust_id,  item_id,       unit_no,       unit_factor,       quantity,    order_price) 
values ({company_id},{prepay_sub_id},{supcust_id},{row.item_id},'{row.unit_no}',{row.unit_factor},{row.quantity},{row.real_price})
on conflict (company_id,supcust_id,prepay_sub_id,item_id,unit_no,order_price) do update set quantity={row.quantity},balance={row.sub_amount};

".Trim().Replace("  "," ")); 
                    }
                }
            }


            if (Math.Abs(diff_amount)>0.01m)
            {
                decimal ChangeBal = diff_amount * inout_flag;
                info.OrderSubBalance = prepay_balance + ChangeBal;
                string sql = @$"insert into prepay_balance (company_id, supcust_id, sub_id, balance) values ({company_id},{supcust_id},{prepay_sub_id},{info.OrderSubBalance}) 
                                            on conflict (company_id,supcust_id, sub_id) do update set balance=({info.OrderSubBalance}); ";
                Subject pw1_sub = info.PaywaySubjects.Find(sub => sub.sub_id == payway1_id);
                if (payway1_id.IsValid() && payway1_amount != 0 && pw1_sub!=null && pw1_sub.sub_type=="YS")
                {
                    sql += $@"insert into prepay_balance (company_id, supcust_id, sub_id, balance) values ({company_id}, {supcust_id}, {payway1_id}, {payway1_amount}) 
                                        on conflict (company_id,supcust_id, sub_id) do update set balance=coalesce(prepay_balance.balance,0)+({payway1_amount});";
                }
                Subject pw2_sub = info.PaywaySubjects.Find(sub => sub.sub_id == payway2_id);
                if (payway2_id.IsValid() && payway2_amount != 0 && pw2_sub != null && pw2_sub.sub_type == "YS")
                {
                    sql += $@"insert into prepay_balance (company_id, supcust_id, sub_id, balance) values ({company_id}, {supcust_id}, {payway2_id}, {payway2_amount}) 
                                        on conflict (company_id,supcust_id, sub_id) do update set balance=coalesce(prepay_balance.balance,0)+({payway2_amount});";
                }

                AddExecSQL(sql);
            }

            return GetExecSQL();
        }


        

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info)
        {
            await base.OnSheetIDGot(cmd, sheetID, info);
            CInfoForApprove info1 = (CInfoForApprove)info;
            string sql = "";
            decimal inoutFlag = -1;
            foreach(SheetRowOrderItemAdjust row in MergedSheetRows)
            {
                if (row.oper_type == "add")
                {
                    sql += $@"insert into items_ordered_change
       (company_id,  prepay_sub_id,  supcust_id,  sheet_id,  item_id,       unit_no,       now_quantity,  real_price,      now_sub_amount,  happen_time,    oper_type)
values ({company_id},{prepay_sub_id},{supcust_id},{sheet_id},{row.item_id},'{row.unit_no}',{row.quantity},{row.real_price},{row.sub_amount},'{happen_time}','{row.oper_type}');";
                }
                else if (row.oper_type == "delete")
                {
                    sql += $@"
insert into items_ordered_change
       (company_id,  prepay_sub_id,  supcust_id,  sheet_id,  item_id,      unit_no,        old_quantity,      real_price,          old_sub_amount,      happen_time,    oper_type)
values ({company_id},{prepay_sub_id},{supcust_id},{sheet_id},{row.item_id},'{row.unit_no}',{row.old_quantity},{row.old_real_price},{row.old_sub_amount},'{happen_time}','{row.oper_type}');";

                }
                else 
                { 
                    sql += $@"
insert into items_ordered_change
       (company_id,  prepay_sub_id,  supcust_id,  sheet_id,  item_id,       unit_no,       old_quantity,      now_quantity,  real_price,      old_sub_amount,      now_sub_amount,   happen_time,oper_type)
values ({company_id},{prepay_sub_id},{supcust_id},{sheet_id},{row.item_id},'{row.unit_no}',{row.old_quantity},{row.quantity},{row.real_price},{row.old_sub_amount},{row.sub_amount},'{happen_time}','{row.oper_type}');";

                }
            }
            decimal prepayTotalBalance = 0m;
            if (info1.PrepaySubjects != null)
            {
                
                foreach (var sub in info1.PrepaySubjects)
                {
                    if (sub.balance.IsValid())
                    {
                        prepayTotalBalance += CPubVars.ToDecimal(sub.balance);
                    }
                }
            }
                if (Math.Abs(prepay_balance - total_amount) >= 0.01m)
            {
                decimal changeBal = inoutFlag * (prepay_balance-total_amount);
                prepayTotalBalance += changeBal;
                GetAccountHistoryHappenTimePrepayBalance(info, supcust_id, info1.OrderSubBalance, prepayTotalBalance, prepay_sub_id, out string balance, out string totalBalance);
                sql += @$"insert into client_account_history(company_id, happen_time,approve_time, sheet_type, sheet_id, change_amount, now_balance,now_prepay_balance,now_balance_happen_time, now_prepay_balance_happen_time,supcust_id, sub_id, sub_type)
                            values({ company_id},'{CPubVars.GetDateText(happen_time)}', '{CPubVars.GetDateText(approve_time)}',  'DHTZ_zc',{ sheet_id},{ changeBal},{info1.OrderSubBalance},{prepayTotalBalance},{balance},{totalBalance},{ supcust_id},{ prepay_sub_id},'YS'); ";//转出
                if (!HappenNow)
                {
                    sql += $"update client_account_history set now_balance_happen_time=now_balance_happen_time+{changeBal} where company_id={company_id} and sub_type='YS' and sub_id={prepay_sub_id} and supcust_id={supcust_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                    sql += $"update client_account_history set now_prepay_balance_happen_time=now_prepay_balance_happen_time+{changeBal} where company_id={company_id} and sub_type='YS' and supcust_id={supcust_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                }
                    
                Subject pw1_sub = info1.PaywaySubjects.Find(sub => sub.sub_id == payway1_id);
                if (payway1_id.IsValid() && payway1_amount != 0 && pw1_sub != null && pw1_sub.sub_type == "YS")
                {
                    prepayTotalBalance += payway1_amount;
                    decimal pw1_balance = CPubVars.ToDecimal(pw1_sub.balance) + payway1_amount;
                    GetAccountHistoryHappenTimePrepayBalance(info, supcust_id, pw1_balance, prepayTotalBalance, payway1_id, out string balance1, out string totalBalance1);
                    sql += @$"insert into client_account_history(company_id, happen_time,approve_time, sheet_type, sheet_id, change_amount, now_balance,now_prepay_balance,now_balance_happen_time,now_prepay_balance_happen_time, supcust_id, sub_id, sub_type)
                            values({company_id},'{CPubVars.GetDateText(happen_time)}', '{CPubVars.GetDateText(approve_time)}',  'DHTZ_zr',{sheet_id},{payway1_amount},{pw1_balance},{prepayTotalBalance},{balance1},{totalBalance1},{supcust_id},{payway1_id},'YS'); ";//转入
                    if (!HappenNow)
                    {
                        sql += $"update client_account_history set now_balance_happen_time=now_balance_happen_time+{payway1_amount} where company_id={company_id} and sub_type='YS' and sub_id={payway1_id} and supcust_id={supcust_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                        sql += $"update client_account_history set now_prepay_balance_happen_time=now_prepay_balance_happen_time+{payway1_amount} where company_id={company_id} and sub_type='YS' and supcust_id={supcust_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                    }
                        
                }
                Subject pw2_sub = info1.PaywaySubjects.Find(sub => sub.sub_id == payway2_id);
                if (payway2_id.IsValid() && payway2_amount != 0 && pw2_sub != null && pw2_sub.sub_type == "YS")
                {
                    prepayTotalBalance += payway2_amount;
                    decimal pw2_balance = CPubVars.ToDecimal(pw2_sub.balance) + payway2_amount;
                    GetAccountHistoryHappenTimePrepayBalance(info, supcust_id, pw2_balance, prepayTotalBalance, payway2_id, out string balance2, out string totalBalance2);
                    sql += @$"insert into client_account_history(company_id, happen_time,approve_time, sheet_type, sheet_id, change_amount, now_balance,now_prepay_balance,now_balance_happen_time,now_prepay_balance_happen_time, supcust_id, sub_id, sub_type)
                            values({company_id},'{CPubVars.GetDateText(happen_time)}', '{CPubVars.GetDateText(approve_time)}',  'DHTZ_zr',{sheet_id},{payway2_amount},{pw2_balance},{prepayTotalBalance},{balance2},{totalBalance2},{supcust_id},{payway2_id},'YS'); ";//转入
                    if (!HappenNow)
                    {
                        sql += $"update client_account_history set now_balance_happen_time=now_balance_happen_time+{payway2_amount} where company_id={company_id} and sub_type='YS' and sub_id={payway2_id} and supcust_id={supcust_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                        sql += $"update client_account_history set now_prepay_balance_happen_time=now_prepay_balance_happen_time+{payway2_amount} where company_id={company_id} and sub_type='YS' and supcust_id={supcust_id} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
                    }
                        
                }
            }

            if (sql != "")
            {
                sql = string.Join(";", sql.Split(';').Distinct().ToArray());
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
        }

        protected List<SheetRowOrderItemAdjust> MergeSheetRows(List<SheetRowOrderItemAdjust> rows)
        {
            Dictionary<string, SheetRowOrderItemAdjust> rowsDict = new Dictionary<string, SheetRowOrderItemAdjust>();
            foreach (SheetRowOrderItemAdjust sheetRow in rows)
            {
                if (sheetRow.quantity <= 0.001m) continue;//数量为0时，单位总是为小单位,所以后面readdata中比较差异会出错，干脆删除，后面会被标记成delete
                string skey = sheetRow.item_id+"_"+sheetRow.real_price +"_" + sheetRow.unit_factor.ToString();
                SheetRowOrderItemAdjust curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = new SheetRowOrderItemAdjust();
                    curRow.item_id = sheetRow.item_id;
                    curRow.item_name = sheetRow.item_name;
                    curRow.quantity = sheetRow.quantity;
                    curRow.unit_no = sheetRow.unit_no;
                    curRow.unit_factor = sheetRow.unit_factor;
                    curRow.real_price = sheetRow.real_price;
                    curRow.sub_amount = sheetRow.sub_amount;
                    curRow.inout_flag = sheetRow.inout_flag;
                    rowsDict.Add(skey, curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity;
                    curRow.sub_amount += sheetRow.sub_amount;
                }
            }
            List<SheetRowOrderItemAdjust> newList = new List<SheetRowOrderItemAdjust>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }

        /*public override async Task<JsonResult> ToVoucherRows(CMySbCommand cmd, string sheetID, SheetCwVoucher sheetCwVoucher, Dictionary<string, decimal> payways)
        {
            string subsID = "";
            string condi = "";
            string payCondi = "";
            int subLen = 0;
            if (payways == null || payways.Count == 0)
            {
                decimal money_inout_flag = 1;
                if (payway1_id != "" && payway1_amount != 0) payways.Add(payway1_id, payway1_amount * money_inout_flag);
                if (payway2_id != "" && payway2_amount != 0) payways.Add(payway2_id, payway2_amount * money_inout_flag);
                if (diff_amount != 0) payways.Add("p" + prepay_sub_id, diff_amount * money_inout_flag);
                if (now_disc_amount != 0) payways.Add("disc", now_disc_amount * money_inout_flag);
            }

            foreach (var payway in payways) // 按业务批量生产凭证
            {
                if (payway.Key == "disc") condi = "or sub_code = 560304";//涉及code_length
                else
                {
                    if (subsID != "") subsID += ",";
                    subsID += payway.Key.StartsWith("p") ? payway.Key.Substring(1) : payway.Key;
                }
                subLen++;
            }
            if (subsID != "") payCondi += $"sub_id in ({subsID})";

            string sql = $"select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and ( {payCondi} {condi} );";
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (records == null || records.Count < subLen) return new JsonResult(new { result = "Error", msg = "缺少生成凭证的相关科目，请添加" });
            if (records.Count > subLen) return new JsonResult(new { result = "Error", msg = "生成凭证的相关科目有重复科目代码，请修改" });

            foreach (var rec in records) // 对于每个 sub ，都有对应借贷金额，加入到cwRow里
            {
                if (rec.status == null || rec.status == "") rec.status = 1;
                if (Convert.ToInt16(rec.status) == 0) return new JsonResult(new { result = "Error", msg = "相关科目已停用，请检查" });
                CwRowVoucher cwRow = new CwRowVoucher();
                cwRow.business_sheet_type = SheetType;
                cwRow.business_sheet_id = sheetID;
                cwRow.sub_id = rec.sub_id;
                cwRow.remark = "定货调整";
                //if (red_flag == "2") cwRow.remark = payways["total"] >= 0 ? "红冲销售商品" : "红冲退货";
                decimal changeAmt = 0;
                foreach (var payway in payways)
                {
                    changeAmt = payway.Value;

                    if (payway.Key == rec.sub_id || (payway.Key == "disc" && rec.sub_code == "560304"))// 预收普通账户 || 财务费用-现金折扣
                    {
                        if (changeAmt >= 0) cwRow.debit_amount = changeAmt.ToString();//changeAmt>0 借：预收定货会账户 （贷：预收普通账户）
                        else cwRow.credit_amount = Math.Abs(changeAmt).ToString();//changeAmt<0 （借：预收普通账户） 贷：预收定货会账户
                        cwRow.change_amount = changeAmt.ToString();
                        break;
                    }
                    else if (payway.Key == "p" + rec.sub_id)//预收普通账户
                    {
                        if (changeAmt >= 0) cwRow.credit_amount = changeAmt.ToString();
                        else cwRow.debit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = (-1 * changeAmt).ToString();
                        break;
                    }

                }

                sheetCwVoucher.SheetRows.Add(cwRow);
            }

            return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
        }*/

    }


}
