﻿@page
@model ArtisanManage.Pages.CwPages.OpeningBalanceModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>OpeningBalance</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>

    <style>
        * {
            font-family: "微软雅黑";
        }

        [v-cloak] {
            display: none;
        }

        body {
        }

        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;
        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #dddddd;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }


        #pages {
            width: 100%;
            height: 100vh;
            overflow: hidden;
            display: block;
            flex-direction: column;
        }

        .pages_title{
            width: 100%;
            height:5vh;
            font-weight:500;
            font-size:25px;
            display:flex;
            justify-content:center;
            align-items:center;
        }

        .pages_subtitle{
            width: 100%;
            height:5vh;
            display:block;
        }

        .pages_subtitle .pages_openingdate {
            width:30%;
            height:100%;
            float:left;
            font-size:20px;
            display: flex;
            justify-content: left;
            align-items: center;
            padding-left: 20px;
        }

       .pages_subtitle .pages_buttons {
            height: 100%;
            float:right;
            display:flex;
            justify-content:end;
            align-items:center;
            padding-right:20px;
        }

        .pages_report{
            /*width: 100%;*/
            height: 88vh;
            padding:20px;
            padding-top:0;
        }

        .pages_report .pages_categories{
            width:100%;
            height:6vh;
            display:flex;
            justify-content:left;
            align-items:center;
        }

        .btnActive{
            background: #f56c6c !important;
            border-color: #f56c6c !important;
            color: #fff !important;
        }

        .pages_report .pages_table{
            width: 100%;
            height:calc(100% - 6vh);
        }

        .pages_report .pages_table table{
            width:100%;
            height:100%;
            border: 1px solid #ebeef5;
        }

        .pages_report table thead, .pages_report table tbody tr{
            display:table;
            table-layout: fixed;
        }
        .pages_report table thead {
            width: calc(100% - 17px);
        }

        .scrollTr{
            width: 100%;
        }

        .noScrollTr{
            width: calc(100% - 17px);
        }

        .pages_report table tbody {
            display: block;
            overflow-x: hidden;
            height: calc(100% - 41px);
        }

        .pages_report table thead th:first-of-type, .pages_report table tbody td:first-of-type {
            width:20%
        }
        .pages_report table thead th:nth-of-type(2), .pages_report table tbody td:nth-of-type(2) {
            width: 35%
        }
        .pages_report table thead th:nth-of-type(3), .pages_report table tbody td:nth-of-type(3) {
            width: 15%
        }
        .pages_report table thead th:nth-of-type(4), .pages_report table tbody td:nth-of-type(4) {
            width: 30%
        }

        .pages_report table tbody input, .assister_edit_input{
            width: 96%;
            height: 25px;
            text-align: end;
            border: 1px solid lightgrey;
            display: inline-block;
            margin: auto;
        }
        .pages_report table tbody input:focus, .assister_edit_input{
            border:1px solid darkgrey;
            outline:none;
        }
        input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 3px;
        }

        /*高度*/
        .pages_report table thead th {
            height: 40px;
        }
        .pages_report table tbody td {
            min-height: 35px;
            line-height: 35px;
        }
        /*边框*/
        .pages_report table thead th, .pages_report table tbody td {
            border-right: 1px solid #ebeef5;
        }
        .pages_report table thead th:last-child {
            border-right: 0;
        }
        /*背景*/
        .pages_report table thead th{
            background: #eeeeee;
        }
        .pages_report table tbody tr:nth-child(odd) {
            background: #fafafa;
        }
        .pages_report table tbody tr:hover {
            background-color: #f5f7fa; /*#fef0f0;*/
        }

        .el-message{
            z-index:3000 !important;/*el-message-box的遮罩层z-index从2000开始每次点击自增*/
        }
        .el-message-box table{
            border-spacing:0;
            width:90%;
            text-align:center;
        }
        .el-message-box table th, .el-message-box table td{
            border-right:1px solid #000;
            border-bottom: 1px solid #000;
        }

        .el-message-box table th:nth-child(1), .el-message-box table td:nth-child(1) {
            border-left: 1px solid #000;
        }
        .el-message-box table th{
            border-top: 1px solid #000;
        }


        .draw_bal{
            height: 380px;
            position:relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .draw_data {
            width: 60%;
            height: 60px;
            position: absolute;
            z-index:110;
            top:0;
            display:block;
            font-size:18px;
        }

        .draw_data_debit{
            width:30%;
            height:100%;
            margin:0;
            float: left;
        }

        .draw_data_sign{
            width: 40%;
            height: 100%;
            margin: 0;
            float: left;
            text-align:center;
        }

        .draw_data_credit {
            width: 30%;
            height: 100%;
            margin: 0;
            float:left;
        }

        .draw_data_diff{
            text-align:center;
            margin-bottom:10px;
        }

        .draw_data .row1, .draw_data .row2{
            width:100%;
            height:47%;
            text-align:center;
        }

        .draw_data .row1{
            margin-bottom:3px;
        }


        .draw_box{
            width:60%;
            height:calc(95% - 70px);
            position:absolute;
            display:block;
            bottom:0;
        }

        .draw_box1{
            z-index: 100;
        }

        .draw_box2{
            z-index: 110;
        }

        .draw_box1_top,.draw_box1_bottom{
            width: 100%;
            height: 10%;
        }

        .draw_box1_mid {
            width: 100%;
            height: 80%;   
        }

        .draw_ball{
            width:5%;
            height:100%;
            border-radius:50%;
            margin-left:auto;
            margin-right:auto;
            background: radial-gradient(circle at 25% 35%, #fff, lightgray, gray);
        }

        .draw_pole {
            width: 2%;
            height: 100%;
            margin-left: auto;
            margin-right: auto;
            background: linear-gradient(to right, #fff, lightgray, gray);
        }

        .draw_pan {
            width: 50%;
            height: 100%;
            border-radius:50% / 100% 100% 0 0 ;
            margin-left: auto;
            margin-right: auto;
            background: radial-gradient(circle at 25% 35%, #fff, lightgray, gray);
        }

        .draw_box2_top {
            width: 70%;
            height: 2%;
            border-radius:3px;
            margin-top:4.5%;
            margin-left: auto;
            margin-right: auto;
            background-color: rgba(136,127,122,80%);
            /*opacity:80%;*/
            transition:all 0.4s;
        }

        .draw_box2_mid{
            width: 100%;
            height: 70%;
            position: relative;
        }

        .draw_scale{
            width:30%;
            height:100%;
            position: absolute;
        }

        .draw_scale_left{
            left:0;
            transition: all 0.4s;
            /*top:-20px;*/
        }

        .draw_scale_right {
            right:0;
            transition: all 0.4s;
            /*top:20px;*/
        }

        .draw_cord{
            width:100%;
            height:80%;
            /*opacity:50%;*/
        }

        .draw_cord_left{
            width:50%;
            height:100%;
            float:left;
            background: linear-gradient(to bottom right,rgba(0,0,0,0)0%,rgba(0,0,0,0)calc(50% - 1.5px),rgba(0,0,0,0.5)50%,rgba(0,0,0,0)calc(50% + 1.5px),rgba(0,0,0,0)100%);
        }

        .draw_cord_right {
            width: 50%;
            height: 100%;
            float:right;
            background:linear-gradient(to top right,rgba(0,0,0,0)0%,rgba(0,0,0,0)calc(50% - 1.5px),rgba(0,0,0,0.5)50%,rgba(0,0,0,0)calc(50% + 1.5px),rgba(0,0,0,0)100%) ;
        }

        .draw_carrier{
            width:100%;
            height:20%;
            border-radius: 50% / 0 0 100% 100%;
            background: radial-gradient(circle at 25% 35%, rgba(211,211,211,0.5), rgba(128,128,128,0.5), rgba(105,105,105,0.5));
            /*opacity:50%;*/
            position:absolute;
            z-index:108; 
        }

        .draw_amt{
            width:66px;
            height:66px;
            border-radius:50%;
            position:absolute;
            bottom:13px;
            left:calc(50% - 33px);
            z-index:105;
            transition: all 0.4s;
        }
        
        .draw_scale_left .draw_amt{
            background: radial-gradient(circle at 25% 35%, #bbbcde, #4d5aaf, #4d4398 );
        }

        .draw_scale_right .draw_amt {
            background: radial-gradient(circle at 25% 35%, #f6bfbc, #f09199, #b7282e );
        }

        .dialog_assister{
            overflow: hidden;
        }
        .dialog_assister .el-dialog{
            margin-top: 8vh !important;
        }

        .dialog_assister_select{
            overflow:hidden;
        }

        .dialog_assister_select .el-dialog {
            margin-top: 8vh !important;
        }

        @@media(max-height:700px){
            .dialog_assister .el-dialog{
                width:80% !important;
                margin-top:0 !important;
            }
            .dialog_assister .el-table{
                height:400px !important;
            }
            .dialog_assister_select .el-dialog {
                margin-top: 0 !important;
            }
            .dialog_assister_select .el-table{
                height:360px !important;
            }
        }

        .el-icon-plus{
            position:absolute;
            right:20px;
            cursor:pointer;
            margin-top:6px;
            color:darkgray;
            font-size:larger;
            height:20px;
        }

        
        .assister_select_fullname{
            margin-top:12px;
        }
        .assister_select_fullname .assister_select_fullname_elinput{
            width:55%;
        }
        .assister_select_fullname .assister_select_balance_elinput{
            width:15%;
        }

        .dialog_assister .el-table__footer td:last-of-type{
            text-align: right;
            padding-right:15px;
        }
        .dialog_assister .el-table{
            overflow: visible !important;
        }
        .dialog_assister .el-dialog__body{
            padding:30px 30px;
            padding-bottom:1px;
        }
        .dialog_assister table, .dialog_assister .el-table__empty-block, .dialog_assister .el-table__footer {
            width:100% !important;
        }
        .dialog_assister .el-table__footer{
            margin-top:-40px;
            position:absolute;
            z-index:100;
        }
        .dialog_assister .el-table__body-wrapper{
            margin-bottom:40px !important;
        }

        .el-icon-refresh-right {
            display: inline-block;
            margin-left: 15px;
            color: darkgray;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.5s;
        }

            .el-icon-refresh-right:hover {
                color: lightgray;
            }

    </style>

</head>
<body>
    <div id="root" v-cloak>
        <div id="pages">
            <div class="pages_title">科目期初余额</div>
            <div class="pages_subtitle">
                <div class="pages_openingdate">
                    <span>开账时间：{{period}}</span><!--fromDB-->
                    <span style="margin-left:10px;color:darkgray;">{{periodStatus}}</span>
                </div>
                <div class="pages_buttons">
                    <el-button type="info" plain v-on:click="calcOpBalance()" :disabled="disabledSeeBtn">试算平衡</el-button>
                    <el-button type="info" plain v-on:click="chooseImportSub" :disabled="disabledMake">业务导入</el-button>
                    <el-button type="info" plain v-on:click="exportExcel()" :disabled="disabledSeeBtn">导出</el-button>

                    <el-dialog title="试算平衡" :visible.sync="dialogTrialBalanceVisible">
                        <div class="draw_bal">
                            <div class="draw_data">
                                <div class="draw_data_debit">
                                    <div class="row1">借方合计</div>
                                    <div class="row2" ref="draw_data_debit_row">0</div>
                                </div>
                                <div class="draw_data_sign" ref="draw_data_sign">=</div>
                                <div class="draw_data_credit">
                                    <div class="row1">贷方合计</div>
                                    <div class="row2" ref="draw_data_credit_row">0</div>
                                </div>
                                <div class="draw_data_diff" ref="draw_data_diff">差额：0</div>
                            </div>
                            <div class="draw_box draw_box1">
                                <div class="draw_box1_top">
                                    <div class="draw_ball"></div>
                                </div>
                                <div class="draw_box1_mid">
                                    <div class="draw_pole"></div>
                                </div>
                                <div class="draw_box1_bottom">
                                    <div class="draw_pan"></div>
                                </div>
                            </div>
                            <div class="draw_box draw_box2">
                                <div class="draw_box2_top" ref="draw_box2_top"></div>
                                <div class="draw_box2_mid">
                                    <div class="draw_scale draw_scale_left" ref="draw_scale_left">
                                        <div class="draw_cord">
                                            <div class="draw_cord_left"></div>
                                            <div class="draw_cord_right"></div>
                                        </div>
                                        <div class="draw_carrier"></div>
                                        <div class="draw_amt" ref="draw_amt_left"></div>
                                    </div>
                                    <div class="draw_scale draw_scale_right" ref="draw_scale_right">
                                        <div class="draw_cord">
                                            <div class="draw_cord_left"></div>
                                            <div class="draw_cord_right"></div>
                                        </div>
                                        <div class="draw_carrier"></div>
                                        <div class="draw_amt" ref="draw_amt_right"></div>
                                    </div>
                                </div>
                                <div class="draw_bal_pole"></div>
                            </div>
                        </div>
                        <div slot="footer" class="dialog-footer">
                            <el-button type="primary" @@click="dialogTrialBalanceVisible=false">确 定</el-button>
                        </div>
                    </el-dialog>

                    <el-dialog title="业务导入" :visible.sync="dialogImportVisible">
                        <el-checkbox v-for="im in importList" :key="im.id" v-model="im.checked">{{im.name}}</el-checkbox>
                        <br />
                        <el-tooltip class="item" effect="dark" placement="bottom-start">
                            <div slot="content">
                                库存商品：取自【仓库-期初库存表】成本金额总计；<br />
                                应收账款：取自【资金-客户往来汇总表】开账月月初日0点往来合计期初总余额（请选择“显示无发生额的客户”）；<br />
                                应付账款：取自【资金-供应商往来汇总表】开账月月初日0点往来合计期初总余额；<br />
                                预付账款：取自【资金-预付款余额】开账月月初日0点总计期初余额；<br />
                                预收账款：取自【资金-预收款余额】开账月月初日0点总计期初余额；<br />
                                现金银行：取自【资金-现金银行余额表】开账日0点各科目期末余额。
                            </div>
                            <i class="el-icon-info" style="margin-top: 20px;"></i>
                        </el-tooltip>
                        <div slot="footer" class="dialog-footer">
                            @*<el-button type="danger" plain @@click="clearBizSubBal">清 除</el-button>*@
                            <el-button @@click="dialogImportVisible = false">取 消</el-button>
                            <el-button type="primary" @@click="saveImport">确 定</el-button>
                        </div>
                    </el-dialog>
                </div>
            </div>
            <div class="pages_report">
                <div class="pages_categories">
                    <el-button-group >
                        <el-button type="danger" v-for="btn in sbjBtnGroup" :key="btn.id" :autofocus="btn.focus" plain v-on:click="chooseSbj(btn.id)">{{btn.name}}</el-button>
                    </el-button-group>
                    <i class="el-icon-refresh-right" title="刷新" @@click="refresh"></i>
                </div>
                <div class="pages_table">
                    <table>
                        <thead>
                            <tr>
                                <th>科目编码</th>
                                <th>科目名称</th>
                                <th>方向</th>
                                <th>期初余额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="op in balanceForShow" :key="op.id" class="scrollTr">
                                <td>{{op.id}}</td>
                                <td style="position:relative;">{{op.name}}<i class="el-icon-plus" v-if="op.assister_types!='' && op.id.charAt(0)!='5'" @@click="showAssisterDialog(op)"></i></td>
                                <td>{{op.dir}}</td>
                                <td style="position:relative;">
                                    <input v-model="op.balance" type="number" :readonly="input_readonly" @@focus="inputOldValue=op.balance" @@change="inputChangeSave(op)" title=""/>
                                    <transition name="el-zoom-in-top">
                                        <el-tag class="save_tag" v-if="op.saveTagVisible" type="success" style="display:inline-block;position:absolute;left:-67px;">已保存</el-tag>
                                    </transition>
                                </td>
                            </tr>
                            
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 辅助核算 -->
        <el-dialog title="添加辅助核算" :visible.sync="dialogAssisterVisible" width="70%"class ="dialog_assister" :close-on-click-modal="false" :before-close="assisterCancel">
            <el-table :data="assisterTable" height="430" width="100%" :show-summary="true" :summary-method="assisterSummary" :stripe="true" @@selection-change="assisterSelectionChange">
                <el-table-column type="selection" width="50"></el-table-column>
                <el-table-column property="row_index" label="行号" width="50"></el-table-column>
                <el-table-column property="sub_full_code_name" label="科目" width="300"></el-table-column>
                <el-table-column property="assister_types_names" label="辅助项" width="450"></el-table-column>
                <el-table-column  label="期初余额" width="200">
                    <template slot-scope="scope">
                        <input v-model="scope.row.balance" type="number" :readonly="input_readonly" @@change="editAssisterData(scope.row)" class="assister_edit_input" />
                    </template>
                </el-table-column>
                <div slot="append"></div>
            </el-table>
            <div style="margin-top: 60px">
                <el-button @@click="showAssisterSelectDialog" :disabled="disabledMake" v-loading.fullscreen.lock="fullscreenLoading">添加</el-button>
                <el-button @@click="deleteAssisterData" type="danger" plain :disabled="disabledMake">移除（{{assister_delete_data.count}}）</el-button>@*:disabled="disabledAssisterBtnForImportSub"*@
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @@click="assisterCancel">取 消</el-button>
                <el-button type="primary" @@click="saveAssisterData" :disabled="disabledMake">保 存</el-button>
            </span>
        </el-dialog>

        <!--选择辅助项-->
        <el-dialog title="选择辅助项" :visible.sync="dialogAssisterSelectVisible" width="50%" class="dialog_assister_select">
            <el-tabs v-model="assister_select_tab" type="border-card" @@tab-click="assister_select_changeTab">
                <el-tab-pane :label="tab.type_name" :name="tab.type_id" v-for="tab in assisterTabs" :key="tab.type_id">
                    <el-table :data="tab.assister_list.filter(data => !assister_select_search || data.name.toLowerCase().includes(assister_select_search.toLowerCase()))" height="400" :stripe="true">
                        <el-table-column label="选择" width="80">
                            <template slot-scope="scope">
                                <el-radio class="radio" :label="scope.row.id" v-model="tab.select_id" @@input="assisterSelectRadioClick(scope.row)">&nbsp;</el-radio>
                            </template>
                        </el-table-column>
                        <el-table-column property="name" label="辅助项"></el-table-column>
                        <el-table-column align="right">
                            <template slot="header" slot-scope="scope"><el-input v-model="assister_select_search" size="mini" placeholder="输入关键字搜索" /></template>
                        </el-table-column>
                    </el-table>
                    @*<el-pagination small
                        :background="true"
                        layout="prev, pager, next, total"
                        :page-size="assister_select_pagination.pageSize"
                        :current-page="assister_select_pagination.currentPage"
                        @@current-change="assisterSelectPageChange"
                                   :total="assister_select_pagination.total">
                    </el-pagination>*@
                </el-tab-pane>
            </el-tabs>
            <div class="assister_select_fullname">
                辅助核算科目：<el-input type="text" v-model="assister_select_data.fullname" class="assister_select_fullname_elinput" readonly></el-input>
                期初余额：<el-input type="text" v-model="assister_select_data.balance" class="assister_select_balance_elinput" @@keyup.enter.native="addAssisterRow"></el-input>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @@click="dialogAssisterSelectVisible = false">取 消</el-button>
                <el-button type="primary" @@click="addAssisterRow">确 认</el-button>
            </span>
        </el-dialog>

    </div>

    <script>
        var g_operKey = '@Model.OperKey';
        
        window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);
        function checkOperRight(vm) {
            if (window.g_operRights.cwSheet){
                if (window.g_operRights.cwSheet.openingBalance && window.g_operRights.cwSheet.openingBalance.see) {
                    if (!window.g_operRights.cwSheet.openingBalance.make) {
                        vm.input_readonly = true;
                        vm.disabledMake = true;
                    }
                    return true;
                } else {
                    return false;
                }
            }else{
                return false;
            }
        }

    </script>
    <script>
        var vm = new Vue({
            el: '#root',
            data() {
                return {
                    /***********************配置项********************/
                    codeLength: [4, 2, 2, 2, 2],
                    period: '',
                    periodStatus:'',
                    cwPeriodFromTo:'',
                    disabledMake: false,
                    disabledSeeBtn: false,
                    sbjBtnGroup: [//id: 1开头是资产，2开头是负债，3开头是权益，4开头是成本，5开头是损益
                        { id: '1', name: '资产' },
                        { id: '2', name: '负债' },
                        { id: '3', name: '权益' },
                        { id: '4', name: '成本' },
                        { id: '5', name: '损益' },
                    ],
                    fullscreenLoading:false,
                    costPriceType:2,

                    /***********************科目余额相关********************/
                    sbjOpBalanceTest:[
                        { id:'1001',name:'库存现金',dir:'借',balance:'2000',level:1, sub_id:'', mother_id:'', sub_type:'', saveTagVisible:false, assister_types:'', no: 1 },
                        { id: '110102', name: '债券', dir: '借', balance: '500', level: 2, sub_id: '2484', mother_id: '2482', sub_type: '', saveTagVisible: false, assister_types: '', no: 2 }
                    ],
                    sbjOpBalance:[],
                    balanceForShow: [],//当前大类的所有科目
                    inputOldValue: '',
                    input_readonly: false,

                     /***********************试算平衡相关********************/
                    dialogTrialBalanceVisible:false,
                    
                    /***********************业务导入相关********************/
                    importList: [
                        { id: 1, name: '商品期初', tag: 'inventory', checked: true, balance: 0, sub_code: 1405, sub_id: '', oldValue: 0 },
                        { id: 2, name: '应收账款', tag: 'receivable', checked: false, balance: 0, sub_code: 1122, sub_id: '', oldValue: 0 },
                        { id: 3, name: '应付账款', tag: 'payable', checked: false, balance: 0, sub_code: 2202, sub_id: '', oldValue: 0 },
                        { id: 4, name: '预付账款', tag: 'prepayable', checked: false, balance: 0, sub_list:[], sub_id: '', oldValue: 0 },
                        { id: 5, name: '预收账款', tag: 'receiptInAdvance', checked: false, balance: 0, sub_list:[], sub_id: '', oldValue: 0 },
                        { id: 6, name: '现金银行', tag: 'cashBank', checked: false, balance: 0, sub_list:[], sub_id: '', oldValue: 0 }
                    ],
                    dialogImportVisible:false,
                   
                    /***********************辅助核算相关********************/
                    /***********辅助dialog1*********/
                    dialogAssisterVisible: false,
                    mainSubRowForAssister: {},
                    assisterTable:[
                        //{ row_index: 0, sub_id: 0, sub_full_code_name: '560304财务费用-现金折扣', assister_types_names: '【部门】开发部_【业务员】粽子', assister1_type:'', assister1_id: 0, assister2_type: '', assiste2_id: 0, balance: 0 }
                    ],
                    //disabledAssisterInputForImportSub: false,
                    disabledAssisterBtnForImportSub: false,
                    assister_delete_data: { count: 0, list: [] },
                    /***********辅助dialog2*********/
                    dialogAssisterSelectVisible: false,
                    assister_select_tab: "",
                    assisterTabs: [
                        //{ type_id: 'INV', type_name: '商品', assister_list: [], select_id: 0 }  // assister_list_inPage: []               assister_list: [ id, name, pystr ]
                    ],
                    assister_select_search:'',
                    assister_select_data: { fullname: '', balance: 0 }
                    //assister_select_pagination: { pageSize: 200, pages: 1, currentPage: 1, total: 0 },
                }
            },
            computed: {
            },
            created(){
                if (!window.checkOperRight(this)) {
                    this.disabledMake = true;
                    this.disabledSeeBtn=true;
                    return;
                }

                this.getDataFromDB();
                
                this.updateTrStyle();

                let arr = this.sbjOpBalance.filter(item => item.id.charAt(0) == '1');
                arr = arr.sort(function (a, b) { return a.no - b.no });
                this.balanceForShow = arr;

                window.checkOperRight(this);//防止input_readonly被替换
            },
            mounted() {
                //首次加载默认选择第一个按钮
                document.querySelector('.el-button--danger').classList.add('btnActive');
            },
            methods: {
                //后端获取数据
                getDataFromDB() {
                    $.ajax({
                        url: '/api/OpeningBalance/GetOpeningBalance',
                        type: 'get',
                        data: {
                            operKey: g_operKey
                        },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                        async: false,
                        error: (xhr) => {
                            this.$message({ type: 'error', message: '网络错误' });
                        }
                    }).then(res => {
                        console.log('opening balance');
                        if (res.result === 'OK') {
                            this.codeLength = res.codeLength.substring(1, res.codeLength.length - 1).split(',').map(i=>parseInt(i));
                            this.period = res.cwPeriodFromTo == "" ? "" : `${res.cwPeriodFromTo.split(' ~ ')[0].split('-')[0]}年${parseInt(res.cwPeriodFromTo.split(' ~ ')[0].split('-')[1])}月${parseInt(res.cwPeriodFromTo.split(' ~ ')[0].split('-')[2])}日`;
                            this.cwPeriodFromTo = res.cwPeriodFromTo;
                            this.costPriceType=res.costPriceType;
                            let data=[];
                            res.data.forEach(item => {
                                data.push({ 
                                    id: item.sub_code, 
                                    name: item.sub_name, 
                                    dir: item.dir, 
                                    balance: item.balance, 
                                    level: item.level, 
                                    sub_id: item.sub_id, 
                                    mother_id: item.mother_id, 
                                    sub_type: item.sub_type, 
                                    saveTagVisible: false, 
                                    assister_types: item.assister_types,
                                    no: parseInt(item.no),
                                    sub_full_code_name: item.sub_full_code_name
                                });
                            });
                            this.sbjOpBalance=data.sort((a,b)=>a.no>b.no);

                            this.input_readonly=false;
                            if(res.close==true){
                                this.input_readonly='readonly';
                                this.disabledMake=true;
                                this.periodStatus = '[已结账]';
                            }
                        }
                    });
                },
                //更新行样式
                updateTrStyle() {
                    this.$nextTick(() => {
                        let rows = document.querySelectorAll('.pages_report table tbody tr');

                        this.scrollRowWidth(rows);

                        this.nameTdIndent(rows);

                        this.inputDisabled(rows);
                    });
                },
                //修改滚动后的行宽，保持五个table一致
                scrollRowWidth(rows){
                    for (let row of rows){
                        if (document.querySelector('tbody').scrollHeight <= 640) {
                            row.classList.remove('scrollTr');
                            row.classList.add('noScrollTr');
                        } else {
                            row.classList.remove('noScrollTr');
                            row.classList.add('scrollTr');
                        }
                    }
                },
                //单元格科目缩进
                nameTdIndent(rows) {
                    let codeArr = this.codeLength;
                    for (let i = 0; i < rows.length; i++) {
                        let nameTd = rows[i].children[1];
                        switch (rows[i].firstChild.innerText.length) {
                            case (codeArr[0] + codeArr[1])://4+2
                                nameTd.style.textIndent = "1.5em";
                                break;
                            case (codeArr[0] + codeArr[1] + codeArr[2])://4+2+2
                                nameTd.style.textIndent = "3em";
                                break;
                            case (codeArr[0] + codeArr[1] + codeArr[2] + codeArr[3])://4+2+2+2
                                nameTd.style.textIndent = "4.5em";
                                break;
                            case (codeArr[0] + codeArr[1] + codeArr[2] + codeArr[3] + codeArr[4])://4+2+2+2+2
                                nameTd.style.textIndent = "6em";
                                break;
                        }
                    }
                },
                //含下级科目不可修改
                inputDisabled(rows){
                    let disCodeList = [];
                    // let disCodeList=['1405','1122','2202'];
                    // this.sbjOpBalance.forEach(item=>{
                    //     if(item.sub_type=='YF' || item.sub_type=='YS'){
                    //         disCodeList.push(item.id);
                    //     }
                    // });
                    for (let row of rows) {
                        let opData = this.sbjOpBalance.find(item => item.id == row.querySelector('td').innerText);
                        if (this.sbjOpBalance.find(item => item.mother_id == opData.sub_id)
                            || opData.id.charAt(0)=='5'
                            || disCodeList.find(sub_code=>sub_code==opData.id)
                            || opData.assister_types != '') {
                            row.querySelector('input').disabled = true;
                        }
                    }
                },
                //选择大类后数据变化
                chooseSbj(btnid){
                    //切换按钮颜色常亮
                    let sbjBtns = document.querySelectorAll('.el-button--danger');
                    for(let i=0;i<sbjBtns.length;i++){
                        sbjBtns[i].classList.remove('btnActive');
                    }
                    event.currentTarget.classList.add('btnActive');

                    //筛选对应按钮的科目
                    let arr=this.sbjOpBalance.filter(item => item.id.charAt(0) == btnid);
                    arr = arr.sort(function (a, b) { return a.no-b.no });
                    this.balanceForShow = arr;

                    //修改行样式
                    this.updateTrStyle();
                },
                //修改数据保存数据库
                inputChangeSave(op){
                    let saveArr = [];
                    op.balance=Math.round(parseFloat(op.balance == '' ? 0 : op.balance)*100)/100;
                    let opToSave = { sub_code: parseFloat(op.id), sub_id: parseInt(op.sub_id), balance: op.balance, oldValue: this.inputOldValue };
                    saveArr.push(opToSave);
                    this.saveParents(op,saveArr,[]);

                    $.ajax({
                        url: '/api/OpeningBalance/Save?operKey=' + g_operKey,
                        type: 'post',
                        async:false,
                        data: JSON.stringify({
                            operKey:g_operKey,
                            sub_id: op.sub_id,
                            saveData: saveArr,
                            assisterData: []
                        }), 
                        contentType: "application/json;charset=UTF-8",
                        error: (xhr) => {
                            this.$message({ type: 'error', message: '网络错误' });
                        }
                    }).then(res => {
                        if (res.result === "OK") {
                            console.log("期初保存成功");
                            this.afterSave(saveArr);
                            this.inputOldValue = op.balance;
                        } else {
                            console.log("期初保存失败");
                            this.sbjOpBalance.find(row => row.sub_id == op.sub_id).balance=opToSave.oldValue;
                            this.$message({ showClose: true, message: `保存失败！${res.msg}`, type: 'error', offset: 20, duration: 2500 });
                        }
                    });
                },
                saveParents(op, saveArr, saveArrs) {//一次存一棵树
                    //op:{ id:'110102',name:'债券',dir:'借',balance:'500',level:2,sub_id:'2484',mother_id:'2482', saveTagVisible:false  }
                    if(op.level>1){
                        let opToSave=saveArr[saveArr.length-1];
                        let parent = this.sbjOpBalance.find(item => item.sub_id == op.mother_id);
                        let dir=op.dir!=parent.dir?-1:1;
                        let oldBalance = Math.round(parseFloat(parent.balance == '' ? 0 : parent.balance)*100)/100;
                        saveArrs.forEach(tree => {
                            tree.forEach(sub => {
                                if (sub.sub_id ==parent.sub_id){
                                    oldBalance=sub.balance;
                                }
                            });
                        });
                        let adjustedOldVal = Math.round((parseFloat(opToSave.oldValue == '' ? 0 : opToSave.oldValue) * dir)*100)/100;
                        let adjustedNewVal= Math.round((parseFloat((op.balance == '' || isNaN(op.balance)) ? 0 : op.balance)*dir)*100)/100;
                        let newBalance = Math.round((oldBalance - adjustedOldVal + adjustedNewVal)*100)/100;
                        saveArr.push({ sub_code: parseFloat(parent.id), sub_id: parseInt(parent.sub_id), balance: newBalance, oldValue: oldBalance });
                        let parentForSave={ mother_id:parent.mother_id, dir:parent.dir, balance:newBalance, id:parent.id, sub_id:parent.sub_id, level:parent.level };
                        this.saveParents(parentForSave, saveArr, saveArrs);
                    }
                },
                afterSave(saveArr){
                    saveArr.forEach(item => {
                        let op = this.sbjOpBalance.find(row => row.sub_id == item.sub_id);
                        op.balance = item.balance == 0 ? '' : item.balance;
                        this.sbjOpBalance.find(row => row.sub_id == item.sub_id).saveTagVisible = true;
                    });
                    setTimeout(() => {
                        saveArr.forEach(item => {
                            this.sbjOpBalance.find(row => row.sub_id == item.sub_id).saveTagVisible = false;
                        });
                    }, 2500);
                },
                //试算平衡按钮
                calcOpBalance(){
                    this.dialogTrialBalanceVisible=true;
                    //全部账户的借方期初余额合计数 = 全部账户的贷方期初余额合计数
                    //借方合计
                    let debit = this.sbjOpBalance.filter(item=>item.id.length == 4 && item.dir =='借').reduce(function(total,item){
                        total = (parseFloat(total).toFixed(2) * 100 + parseFloat(item.balance == '' ? 0 : item.balance).toFixed(2) * 100) / 100;
                        return total;
                    },0);
                    //贷方合计
                    let credit = this.sbjOpBalance.filter(item => item.id.length == 4 && item.dir == '贷').reduce(function (total, item) {
                        total = (parseFloat(total).toFixed(2) * 100 + parseFloat(item.balance == '' ? 0 : item.balance).toFixed(2) * 100) / 100;
                        return total;
                    },0);
                    this.$nextTick(()=>{
                        //setTimeout(() => {
                        //    let params = [
                        //        ['translateY(32px)', 'translateY(-32px)', '1.2', '0.85', '-10deg', '71%'],
                        //        ['translateY(-32px)', 'translateY(32px)', '0.85', '1.2', '10deg', '71%'],
                        //        ['translateY(0px)', 'translateY(0px)', '1', '1', '0deg', '70%']
                        //    ];
                        //    let param = params[2];
                        //    if (debit - credit > 0) {
                        //        param = params[0];
                        //        this.$message({
                        //            showClose: true,
                        //            message: '期初录入余额不平衡，请检查！',
                        //            type: 'warning',
                        //            offset: 20,
                        //            duration: 2500
                        //        });
                        //    } else if (debit - credit < 0) {
                        //        param = params[1];
                        //        this.$message({
                        //            showClose: true,
                        //            message: '期初录入余额不平衡，请检查！',
                        //            type: 'warning',
                        //            offset: 20,
                        //            duration: 2500
                        //        });
                        //    }else{
                        //        this.$message({
                        //            showClose: true,
                        //            message: '恭喜您，期初余额平衡！',
                        //            type: 'success',
                        //            offset: 20,
                        //            duration: 2500
                        //        });
                        //    }
                        //    this.$refs.draw_data_debit_row.innerText=debit;
                        //    this.$refs.draw_data_credit_row.innerText = credit;
                        //    this.$refs.draw_data_sign.innerText = debit - credit == 0 ? '=' : (debit - credit >0?'>':'<');
                        //    this.$refs.draw_data_diff.innerText = '差额：' + Math.round((debit - credit)*100)/100;
                        //    this.$refs.draw_scale_left.style.transform = param[0];
                        //    this.$refs.draw_scale_right.style.transform = param[1];
                        //    this.$refs.draw_amt_left.style.scale = param[2];
                        //    this.$refs.draw_amt_right.style.scale = param[3];
                        //    this.$refs.draw_box2_top.style.rotate = param[4];
                        //    this.$refs.draw_box2_top.style.width = param[5];
                        //}, 100);

                        //兼容老版本css
                        setTimeout(() => {
                            let params = [
                                ['translateY(32px)', 'translateY(-32px)', 'scale(1.2)', 'scale(0.85)', 'rotate(-10deg)', '71%'],
                                ['translateY(-32px)', 'translateY(32px)', 'scale(0.85)', 'scale(1.2)', 'rotate(10deg)', '71%'],
                                ['translateY(0px)', 'translateY(0px)', 'scale(1)', 'scale(1)', 'rotate(0deg)', '70%']
                            ];
                            let param = params[2];
                            if (debit - credit > 0) {
                                param = params[0];
                                this.$message({
                                    showClose: true,
                                    message: '期初录入余额不平衡，请检查！',
                                    type: 'warning',
                                    offset: 20,
                                    duration: 2500
                                });
                            } else if (debit - credit < 0) {
                                param = params[1];
                                this.$message({
                                    showClose: true,
                                    message: '期初录入余额不平衡，请检查！',
                                    type: 'warning',
                                    offset: 20,
                                    duration: 2500
                                });
                            } else {
                                this.$message({
                                    showClose: true,
                                    message: '恭喜您，期初余额平衡！',
                                    type: 'success',
                                    offset: 20,
                                    duration: 2500
                                });
                            }
                            this.$refs.draw_data_debit_row.innerText = debit;
                            this.$refs.draw_data_credit_row.innerText = credit;
                            this.$refs.draw_data_sign.innerText = debit - credit == 0 ? '=' : (debit - credit > 0 ? '>' : '<');
                            this.$refs.draw_data_diff.innerText = '差额：' + Math.round((debit - credit) * 100) / 100;
                            this.$refs.draw_scale_left.style.transform = param[0];
                            this.$refs.draw_scale_right.style.transform = param[1];
                            this.$refs.draw_amt_left.style.transform = param[2];
                            this.$refs.draw_amt_right.style.transform = param[3];
                            this.$refs.draw_box2_top.style.transform = param[4];
                            this.$refs.draw_box2_top.style.width = param[5];
                        }, 100);
                    });
                    

                    //弹出界面
                    /*this.$alert(`
                        <table>
                            <thead>
                                <th>借方金额</th>
                                <th>贷方金额</th>
                                <th>差额</th>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>${debit}</td>
                                    <td>${credit}</td>
                                    <td>${debit-credit}</td>
                                </tr>
                            </tbody>
                        </table>
                        `,'期初试算平衡',{
                            dangerouslyUseHTMLString: true
                    });
                    //弹出提示
                    if (debit - credit==0){
                        this.$message({
                            showClose: true,
                            message: '恭喜您，期初余额平衡！',
                            type: 'success',
                            offset: 280,
                            duration:2500
                        });
                    }else{
                        this.$message({
                            showClose: true,
                            message: '期初录入余额不平衡，请检查！',
                            type: 'warning',
                            offset: 280,
                            duration:2500
                        });
                    }*/
                },

                //导入按钮弹出显示
                chooseImportSub(){
                    if(this.input_readonly){
                        this.$alert('第一个会计期间已结账，不能导入期初数据！', '提示', {
                            confirmButtonText: '确定',
                            type: 'warning'
                        });
                        return;
                    }
                    this.importList.forEach(item => item.checked = false);
                    this.importList[0].checked=true;
                    this.dialogImportVisible = true;
                    //this.checkImport(true,1,this.importList[0].tag);
                    return;
                },
                //确认导入
                saveImport(){
                    let hasCheck = false;
                    this.importList.forEach(im => {
                        if (im.checked) hasCheck = true;
                    });
                    if (!hasCheck) {
                        this.$alert('请至少选择一项', '提示', { confirmButtonText: '确定', type: 'warning' });
                        return;
                    }

                    const loading = this.$loading({ lock: true, text: '导入中...', spinner: 'el-icon-loading', background: 'rgba(255, 255, 255, 0.7)' });
                    let _this=this;
                    let startDay = this.cwPeriodFromTo.split(' ~ ')[0];
                    let lastDay = new Date(new Date(startDay).setDate(new Date(startDay).getDate()-1));
                    //let now = new Date();
                    let costPriceType=this.costPriceType;
                    $.ajax({
                        url: `/api/OpeningBalance/SaveImport?operKey=${g_operKey}&startDay=1111-11-11+00%3A00&endDay=${lastDay.getFullYear()}-${lastDay.getMonth() + 1}-${lastDay.getDate()}+23%3A59&gridID=gridItems&startRow=0&endRow=2000&cost_price_type=${costPriceType}&GetRowsCount=true&showNoData=true&showRed=false`,
                        //url: `/api/OpeningBalance/SaveImport?operKey=${g_operKey}&startDay=${startDay}+00%3A00&endDay=${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}+23%3A59&gridID=gridItems&startRow=0&endRow=2000&cost_price_type=${costPriceType}&GetRowsCount=true&showNoData=true`,
                        type: 'post',
                        data: JSON.stringify({
                            operKey: g_operKey,
                            importList: _this.importList.filter(item=>item.checked==true).map(item=>item.tag)
                        }),
                        contentType: "application/json;charset=UTF-8",
                        error: (xhr) => {
                            loading.close();
                            _this.$message({ type: 'error', message: '导入失败 ' + xhr.responseText });
                        }
                    }).then(res => {
                        this.dialogImportVisible = false;
                        loading.close();
                        if (res.result === "OK") {
                            if(res.msg!=''){
                                _this.$message({ type: 'warning', message: res.msg });
                                return;
                            }
                            console.log("期初导入成功");
                            _this.$message({ type: 'success', message: '导入成功!' });
                            _this.afterSave(res.subs_balance);
                        } else {
                            console.log("期初导入失败");
                            _this.$message({ type: 'error', message: '导入失败 '+res.msg });
                        }
                    });
                },


                //点击行内+号弹出辅助核算展示表
                showAssisterDialog(row) {
                    if(row.assister_types=='' || row.id.charAt(0)=='5'){
                        console.log('cw opening balance assister 该科目没有辅助核算');
                        return;
                    }
                    //if (['1405', '1122', '2202', '1123', '2203'].indexOf(row.id.substring(0,4)) > -1) {
                    //    this.disabledAssisterInputForImportSub=true;
                    //    this.disabledAssisterBtnForImportSub=true;
                    //}else{
                    //    this.disabledAssisterInputForImportSub=false;
                    //    this.disabledAssisterBtnForImportSub=false;
                    //}
                    let _this=this;
                    $.ajax({
                        url: '/api/OpeningBalance/GetAssisterSubData',
                        type: 'get',
                        data: { operKey: g_operKey, sub_id: row.sub_id },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                        async: false,
                        error: function (xhr) {
                            _this.$message({ showClose: true, message: '网络错误', type: 'error', offset: 20, duration: 2500 });
                        }
                    }).then(res => {
                        console.log('get assister sub data in opening balance');
                        if (res.result === 'OK') {
                            _this.dialogAssisterVisible = true;
                            _this.assisterTable = res.data;
                            _this.mainSubRowForAssister = row;
                            _this.inputOldValue=parseFloat((isNaN(row.balance)||row.balance==''||row.balance==null)?0:row.balance);
                        } else {
                            _this.$message({ showClose: true, message: res.msg, type: 'error', offset: 20, duration: 2500 });
                        }
                    });
                },
                assisterSummary(param){
                    const { columns, data } = param;
                    const sums = [];
                    columns.forEach((column, index) => {
                        if (index === 2) {
                            sums[index] = this.mainSubRowForAssister.sub_full_code_name;
                            return;
                        }
                        if (index === 3) {
                            sums[index] = '合计';
                            return;
                        }
                        if(index === 4) {
                            const values = data.map(item => Number(item.balance));
                            if (!values.every(value => isNaN(value))) {
                                sums[index] = values.reduce((prev, curr) => {
                                    const value = Number(curr);
                                    if (!isNaN(value)) {
                                        return prev + curr;
                                    } else {
                                        return prev;
                                    }
                                }, 0);
                                sums[index]=Math.round(parseFloat(sums[index])*100)/100;
                            } else {
                                sums[index] = 'N/A';
                            }
                        }
                    });
                    this.mainSubRowForAssister.balance=sums[4];
                    return sums;
                },
                editAssisterData(row){
                    let assister_row=this.assisterTable.find(item=>item.row_index==row.row_index);
                    assister_row.balance=row.balance;
                },
                saveAssisterData(){
                    if (this.assisterTable.length == 0 && this.mainSubRowForAssister.balance == this.inputOldValue) {
                        this.$message({ showClose: true, message: '没有要保存的数据', type: 'warning', offset: 20, duration: 2500 });
                        return;
                    }

                    //保存主科目
                    let saveArr = [{ sub_code: parseFloat(this.mainSubRowForAssister.id), sub_id: parseInt(this.mainSubRowForAssister.sub_id), balance: ((this.mainSubRowForAssister.balance==null || isNaN(this.mainSubRowForAssister.balance))?0:this.mainSubRowForAssister.balance), oldValue: this.inputOldValue }];
                    this.saveParents(this.mainSubRowForAssister, saveArr, []);

                    //保存辅助科目
                    let assisterData=this.assisterTable;

                    let _this=this;
                    $.ajax({
                        url: '/api/OpeningBalance/Save?operKey=' + g_operKey,
                        type: 'post',
                        data: JSON.stringify({
                            operKey:g_operKey,
                            sub_id: saveArr[0].sub_id,
                            saveData: saveArr,
                            assisterData: assisterData
                        }), 
                        contentType: "application/json;charset=UTF-8",
                        error: function (xhr) {
                            _this.$message({ showClose: true, message: '保存失败（'+xhr.status+'）', type: 'error', offset: 20, duration: 2500 });
                        }
                    }).then(res => {
                        if (res.result === "OK") {
                            console.log("期初保存成功");
                            _this.dialogAssisterVisible = false;
                            _this.$message({ showClose: true, message: '保存成功！', type: 'success', offset: 20, duration: 2500 });
                            _this.afterSave(saveArr);
                        } else {
                            console.log("期初保存失败");
                            _this.$message({ showClose: true, message: `保存失败：${res.msg}`, type: 'error', offset: 20, duration: 2500 });
                        }
                    });
                },
                assisterSelectionChange(val){
                    this.assister_delete_data.count=val.length;
                    this.assister_delete_data.list=val;
                },
                assisterCancel(){
                    this.dialogAssisterVisible = false;
                    this.sbjOpBalance.find(row=>row.sub_id==this.mainSubRowForAssister.sub_id).balance=this.inputOldValue;
                },
                deleteAssisterData(){
                    if(this.assister_delete_data.count==0){
                        this.$message({ showClose: true, message: '请选择要删除的数据', type: 'warning', offset: 20, duration: 2500 });
                        return;
                    }
                    let _this=this;
                    this.assister_delete_data.list.forEach(row=>{
                        _this.assisterTable=_this.assisterTable.filter(item=>item.row_index!=row.row_index);
                    });
                    this.assister_delete_data.count=0;
                    let index=0;
                    this.assisterTable.forEach(item=>item.row_index=++index);
                },
                //辅助核算展示表点击添加弹出辅助核算选择框
                showAssisterSelectDialog() {
                    this.assister_select_data.fullname='';
                    this.assister_select_data.balance=0;
                    this.assisterTabs = [];
                    let _this=this;
                    $.ajax({
                        url: '/api/AssistAccounting/GetDataForVoucher',
                        type: 'get',
                        data: { operKey: g_operKey },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                        async: false,
                        error: function (xhr) {
                            _this.$message({ showClose: true, message: '网络错误', type: 'warning', offset: 20, duration: 2500 });
                        }
                    }).then(res => {
                        console.log('get assister select data in opening balance');
                        if (res.result === 'OK') {
                            let mainRow = _this.mainSubRowForAssister;
                            let assistRow = res.cwAssistSetting.find(a => a.sub_code == mainRow.id); //1405 INV,MAN 商品,业务员  or  ******** DEP 部门
                            if(assistRow==null){
                                _this.$message({ showClose: true, message: '该科目辅助核算已发生变动，请刷新页面！', type: 'warning', offset: 20, duration: 2500 });
                                return;
                            }
                            let assister_data = res.data;
                            let assistersArr = assistRow.assister_types.split(',');
                            for (let i = 0; i < assistersArr.length; i++) {
                                let tabInfo={ 
                                    type_id: assistersArr[i], 
                                    type_name: assistRow.assister_names.split(',')[i], 
                                    assister_list: assister_data[`data_${assistersArr[i].toLowerCase()}`] ,
                                    select_id: 0
                                };
                                _this.assisterTabs.push(tabInfo);
                            }
                            _this.assister_select_tab=_this.assisterTabs[0].type_id;
                            _this.dialogAssisterSelectVisible = true;
                        } else {
                            _this.$message({ showClose: true, message: `查询辅助项失败，请检查！${res.msg}`, type: 'error', offset: 20, duration: 2500 });
                        }
                    });
                },
                assisterSelectRadioClick(row){
                    let fullname='';
                    for (let i=0; i<this.assisterTabs.length; i++){
                        if(this.assisterTabs[i].select_id!=''){
                            let select_id=this.assisterTabs[i].select_id;
                            let name = this.assisterTabs[i].assister_list.find(item => item.id == select_id).name;
                            let type='';
                            switch(this.assisterTabs[i].type_id){
                                case 'C': type='【客户】'; break;
                                case 'S': type='【供应商】'; break;
                                case 'INV': type='【商品】'; break;
                                case 'DEP': type='【部门】'; break;
                                case 'MAN': type='【业务员】'; break;
                                default: break;
                            }
                            if(fullname!='') fullname += '_';
                            fullname += type+name;
                        }
                    }
                    this.assister_select_data.fullname=fullname;
                },
                addAssisterRow(){
                    for (let i = 0; i < this.assisterTabs.length; i++) {
                        if (this.assisterTabs[i].select_id == '') {
                            this.$message({ showClose: true, message: '辅助项未选择完整，请检查！', type: 'error', offset: 20, duration: 2500 });
                            return;
                        }
                    }
                    if (isNaN(parseFloat(this.assister_select_data.balance)) || parseFloat(this.assister_select_data.balance) == 0){
                        this.$message({ showClose: true, message: '请输入正确的期初余额！', type: 'error', offset: 20, duration: 2500 });
                        return;
                    }

                    //相同的type和id的行在前端合并，后端直接保存
                    let oldRow={};
                    if(this.assisterTabs.length == 1){
                        oldRow = this.assisterTable.find(item => item.assister1_type == this.assisterTabs[0].type_id && item.assister1_id == this.assisterTabs[0].select_id);
                    }
                    else if (this.assisterTabs.length == 2){
                        oldRow = this.assisterTable.find(item => (item.assister1_type == this.assisterTabs[0].type_id && item.assister1_id == this.assisterTabs[0].select_id && item.assister2_type == this.assisterTabs[1].type_id && item.assister2_id == this.assisterTabs[1].select_id) || (item.assister1_type == this.assisterTabs[1].type_id && item.assister1_id == this.assisterTabs[1].select_id && item.assister2_type == this.assisterTabs[0].type_id && item.assister2_id == this.assisterTabs[0].select_id));
                    }
                    if(oldRow!={} && oldRow != null){
                        oldRow.balance= Math.round(parseFloat(oldRow.balance)*100)/100 + Math.round(parseFloat(this.assister_select_data.balance)*100)/100;
                    }else{
                        let index = this.assisterTable.map(item => item.row_index);
                        if (index.length == 0) index = 0;
                        else index = Math.max(...this.assisterTable.map(item => item.row_index));
                        let newRow = {
                            row_index: index + 1,
                            sub_id: this.mainSubRowForAssister.sub_id,
                            sub_full_code_name: this.mainSubRowForAssister.sub_full_code_name,
                            assister_types_names: this.assister_select_data.fullname,
                            assister1_type: this.assisterTabs[0].type_id,
                            assister1_id: this.assisterTabs[0].select_id,
                            assister2_type: '',
                            assister2_id: 0,
                            balance: Math.round(parseFloat(this.assister_select_data.balance) * 100) / 100
                        };
                        if (this.assisterTabs.length == 2) {
                            newRow.assister2_type = this.assisterTabs[1].type_id;
                            newRow.assister2_id = this.assisterTabs[1].select_id;
                        }
                        this.assisterTable.push(newRow);
                    }
                    this.dialogAssisterSelectVisible = false;
                },
                assister_select_changeTab(){
                    this.assister_select_search='';
                },
                refresh() {
                    location.reload();
                },
                exportExcel() {
                    if (!this.period){
                        this.$message({ showClose: true, message: '请先开账', type: 'danger', offset: 20, duration: 2500 });
                        return;
                    }
                    $.ajax({
                        url: "/AppApi/Main/GetProgressToShow?operKey=" + g_operKey,
                        method: "GET",
                        contentType: "application/json;charset=UTF-8",
                        data: {},
                        success: (res) => {
                            if (res.result == 'OK') {
                                if (res.progress) {
                                    var pg = res.progress
                                    var arr = pg.split('|')
                                    var title = arr[0]
                                    var value = arr[1]
                                    this.$message({ showClose: true, message: '有一个正在进行的操作:' + title, type: 'warning', offset: 20, duration: 4000 });
                                    console.log(title, res.progress);
                                    return
                                }

                                var mainWin = window.parent
                                if (mainWin.parent) mainWin = mainWin.parent
                                mainWin.showProgress(true)

                                var postParams = {};
                                var url = `../api/OpeningBalance/ExportExcel?operKey=${g_operKey}`;
                                if (window.ExportServerUri) {
                                    url = `${window.ExportServerUri}/api/OpeningBalance/ExportExcel?operKey=${g_operKey}`;
                                }
                                var downLoadFile = function (url, postParams) {
                                    var $form = $('<form  method="post" />');
                                    $form.attr('action', url);
                                    $(document.body).append($form);
                                    $form[0].submit();
                                    $form[0].remove();
                                }
                                downLoadFile(url, postParams)
                            } else {
                                this.$message({ showClose: true, message: res.msg, type: 'danger', offset: 20, duration: 2500 });
                            }
                        },
                        error: (res) => {
                            this.$message({ showClose: true, message: '导出失败', type: 'danger', offset: 20, duration: 2500 });
                            console.log('导出失败', res);
                        }
                    });




                    // $.ajax({
                    //     url: '/api/OpeningBalance/ExportExcel',
                    //     type: 'get',
                    //     data: { operKey: g_operKey },
                    //     contentType: "application/json;charset=UTF-8",
                    //     async: false,
                    //     error: (xhr) => {
                    //         this.$message({ type: 'error', message: '导出失败 ' });
                    //     }
                    // }).then(res => {
                    //     if (res.result === "OK") {
                    //         console.log('export cw opening balance');
                    //     } else {
                    //         this.$message({ type: 'error', message: '导出失败' });
                    //     }
                    // });
                }
                /*clearBizSubBal(){
                    let hasCheck = false;
                    this.importList.forEach(im => {
                        if (im.checked) hasCheck = true;
                    });
                    if (!hasCheck) {
                        this.$alert('请至少选择一项', '提示', { confirmButtonText: '确定', type: 'warning' });
                        return;
                    }

                    this.$confirm('确认清除所选科目财务期初金额？', '提示', {
                        confirmButtonText: '确定',cancelButtonText: '取消'
                    }).then(() => {
                        const loading = this.$loading({ lock: true, text: '清除中...', spinner: 'el-icon-loading', background: 'rgba(255, 255, 255, 0.7)' });
                        let _this = this;
                        let period = this.period.replace('年', '-').replace('月', '');
                        let now = new Date();
                        $.ajax({
                            url: `/api/OpeningBalance/ClearImport?operKey=${g_operKey}`,
                            type: 'post',
                            data: JSON.stringify({
                                operKey: g_operKey,
                                importList: _this.importList.filter(item => item.checked == true).map(item => item.tag)
                            }),
                            contentType: "application/json;charset=UTF-8",
                            error: (xhr) => {
                                loading.close();
                                _this.$message({ type: 'error', message: '清除失败 ' + xhr.responseText });
                            }
                        }).then(res => {
                            this.dialogImportVisible = false;
                            loading.close();
                            if (res.result === "OK") {
                                if (res.msg != '') {
                                    _this.$message({ type: 'warning', message: res.msg });
                                    return;
                                }
                                console.log("业务科目清除期初成功");
                                _this.$message({ type: 'success', message: '已清除' });
                                _this.afterSave(res.subs_balance);
                            } else {
                                console.log("业务科目清除期初失败");
                                _this.$message({ type: 'error', message: '清除失败 ' + res.msg });
                            }
                        });
                    });

                }*/
                //assisterSelectPageChange(page_no){
                //    console.log(`opening balance assister select page change, current page: ${page_no}`);
                //    this.assister_list_inPage=[];
                //    let start=(page_no-1)*this.assister_select_pagination.pageSize;
                //    let end = page_no * this.assister_select_pagination.pageSize - 1;
                //    let tab_id=this.assister_select_tab;
                //    let thisTabList=this.assisterTabs.find(t=>t.type_id==tab_id).assister_list;
                //    let thisTabListInPage=this.assisterTabs.find(t=>t.type_id==tab_id).assister_list_inPage;
                //    for (let i = 0; i < thisTabList.length; i++) {
                //        if( start<=i && i<=end ){
                //            thisTabListInPage.push(this.assist_details[i]);
                //        }
                //    }
                //    $('tbody')[0].scrollTo({ top: 0, left: 0, behavior: "smooth" });
                //},



                
                /*saveImport_(){
                    let hasCheck=false;
                    this.importList.forEach(im=>{
                        if(im.checked){
                            hasCheck=true;
                        }
                    });
                    if(!hasCheck){
                        this.$alert('请至少选择一项', '提示', {
                            confirmButtonText: '确定',
                            type: 'warning'
                        });
                        return;
                    }

                    this.dialogImportVisible = false;

                    let saveArrs=[];
                    let err="";
                    this.importList.forEach(im=>{
                        if (im.checked) {
                            if (im.tag == 'inventory' || im.tag == 'receivable' || im.tag == 'payable'){
                                let op = this.sbjOpBalance.find(o => o.sub_id == im.sub_id);
                                let saveArr = [{ sub_code: parseFloat(op.id), sub_id: parseInt(op.sub_id), balance: parseFloat(im.balance == '' ? 0 : im.balance), oldValue:op.balance }];
                                let opForSave = { mother_id: op.mother_id, dir: op.dir, balance: im.balance, id: op.id, sub_id: op.sub_id, level: op.level };
                                this.saveParents(opForSave, saveArr, saveArrs);
                                saveArrs.push(saveArr);
                            }else{
                                Object.keys(im.sub_list).forEach(sub_id=>{
                                    let op = this.sbjOpBalance.find(o => o.sub_id == sub_id);
                                    if (im.tag == 'prepayable' && op.id.substring(0, 4) != "1123"){
                                        err += "预付账户类型请放在科目【1123 预付账款】下，请修改科目;";
                                    } else if (im.tag == 'receiptInAdvance' && op.id.substring(0, 4) != "2203"){
                                        err += "预收账户类型请放在科目【2203 预收账款】下，请修改科目;";
                                    }
                                    let balance=parseFloat(im.sub_list[sub_id] == '' ? 0 : im.sub_list[sub_id]);
                                    balance = Math.round(balance*100)/100;
                                    let saveArr = [{ sub_code: parseFloat(op.id), sub_id: parseInt(op.sub_id), balance: balance , oldValue: op.balance }];
                                    let opForSave = { mother_id: op.mother_id, dir: op.dir, balance: balance, id: op.id, sub_id: op.sub_id, level: op.level };
                                    this.saveParents(opForSave, saveArr, saveArrs);
                                    saveArrs.push(saveArr);
                                });
                            }
                        }
                    });
                    if(err!="") return err;

                    $.ajax({
                        url: '/api/OpeningBalance/SaveImport?operKey=' + g_operKey,
                        type: 'post',
                        data: JSON.stringify({
                            operKey: g_operKey,
                            importList: saveArrs
                        }),
                        contentType: "application/json;charset=UTF-8",
                        error: (xhr) => {
                            this.$message({ type: 'error', message: '导入失败 '+xhr.responseText });
                        }
                    }).then(res => {
                        if (res.result === "OK") {
                            console.log("期初导入成功");
                            saveArrs.forEach(saveArr=>{
                                this.afterSave(saveArr);
                            });
                            this.dialogImportVisible=false;
                            this.$message({ type: 'success', message: '导入成功!' });
                        } else {
                            console.log("期初导入失败");
                        }
                    });
                },

                checkImport(checked,id,tag){
                    if(checked==true){
                        let period = this.period.replace('年', '-').replace('月', '');
                        let now = new Date();
                        let url='';
                        switch(id){
                            case 1:
                                url=`/api/OpeningBalance/GetImportInventory?operKey=${g_operKey}&startDay=${period}-01+00%3A00&endDay=${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}+23%3A59&gridID=gridItems&startRow=0&endRow=200&cost_price_type=2&GetRowsCount=true`;//期初默认按加权平均计价
                                break;
                            case 2:
                                url=`/api/OpeningBalance/GetImportReceivableAndReceiptInAdvance?operKey=${g_operKey}&startDay=${period}-01+00%3A00&endDay=${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}+23%3A59&gridID=gridItems&startRow=0&endRow=200&GetRowsCount=true&showNoData=true`;
                                break;
                            case 3:
                                url = `/api/OpeningBalance/GetImportPayableAndPrepayable?operKey=${g_operKey}&startDay=${period}-01+00%3A00&endDay=${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}+23%3A59&gridID=gridItems&startRow=0&endRow=200&GetRowsCount=true`;
                                break;
                            case 4:
                                url = `/api/OpeningBalance/GetImportPayableAndPrepayable?operKey=${g_operKey}&startDay=${period}-01+00%3A00&endDay=${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}+23%3A59&gridID=gridItems&startRow=0&endRow=200&GetRowsCount=true`;
                                break;
                            case 5:
                                url=`/api/OpeningBalance/GetImportReceivableAndReceiptInAdvance?operKey=${g_operKey}&startDay=${period}-01+00%3A00&endDay=${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}+23%3A59&gridID=gridItems&startRow=0&endRow=200&GetRowsCount=true&showNoData=true`;
                                break;
                        }
                        this.getImportFromDB(url,id,tag);
                    }else{
                        let importItem=this.importList.filter(im=>im.id==id)[0];
                        importItem.balance=0;
                    }
                },
                getImportFromDB(url,id,tag) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: JSON.stringify({ operKey: g_operKey }),
                        contentType: "application/json;charset=UTF-8",
                        async: false
                    }).then(res => {
                        if (res.result === "OK") {
                            console.log('get import '+tag);
                            if (tag=='inventory' || tag == 'receivable' || tag == 'payable'){
                                this.importList[id-1].balance=res[tag];
                                this.importList[id-1].sub_id=res[tag+'_id'];
                            }else{
                                this.importList[id-1].sub_list=res[tag];
                            }

                        } else {
                            console.log('get import '+tag+'fail');
                        }
                    });
                },*/
            }
        })
    </script>


</body>
</html>