﻿using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ArtisanManage.Services;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;
using System;
using System.Linq;
using System.ComponentModel.Design;
using NPOI.SS.Formula.Functions;
using Newtonsoft.Json.Linq;
using Microsoft.AspNetCore.Razor.Language.Intermediate;
using ArtisanManage.Pages.Setting;
using Microsoft.CodeAnalysis.Elfie.Model.Strings;

namespace ArtisanManage.Pages.CwPages
{
    public class OpeningAccountModel : PageFormModel
    {
        public OpeningAccountModel(CMySbCommand cmd) : base(Services.MenuId.openingAccount)
        {
            this.cmd = cmd;
        }
        public async Task OnGet(string operKey)
        {
            OperKey = operKey;
            Security.GetInfoFromOperKey(operKey, out company_id, out OperID);
            await GetOperRights(cmd);
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class OpeningAccountController : Controller
    {
        CMySbCommand cmd;
        public OpeningAccountController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }
        public string companyName;
        public bool useAccounting;
        public List<int> accountingCodeLength;
        public string openAccountPeriod;//财务开账期 yyyy-MM
        public string cwPeriodFromTo;//财务开账账期始末 yyyy-MM-dd ~ yyyy-MM-dd
        public bool autoCreateVoucher;
        public bool autoApproveVoucher;
        public bool saleIncomeAndCost;
        //public bool cwBizPeriodSame;

        [HttpGet]
        public async Task<JsonResult> GetAccount(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($@"select 
                coalesce(setting ->>  'useAccounting','false') useaccounting, 
                coalesce(setting ->>  'accountingCodeLength','') accountingcodelength, 
                coalesce(setting ->>  'openAccountPeriod','false') openaccountperiod, 
                coalesce(setting ->>  'cwPeriodFromTo','') cwperiodfromto, 
                coalesce(setting ->>  'autoCreateVoucher','false') autocreatevoucher, 
                coalesce(setting ->>  'autoApproveVoucher','false') autoapprovevoucher, 
                coalesce(setting ->>  'autoApproveVoucher','false') autoapprovevoucher, 
                coalesce(setting ->>  'saleIncomeAndCost','false') saleincomeandcost, 
                setting ->>  'companyName' companyname, setting from company_setting where company_id = {companyID}", cmd);
            if (setting.setting == null) return Json(new { result = "Error", msg = "请先保存公司设置" });

            companyName = setting.companyname;
            useAccounting = Convert.ToBoolean(setting.useaccounting);
            if (useAccounting)
            {
                openAccountPeriod = setting.openaccountperiod.ToString();
                cwPeriodFromTo = setting.cwperiodfromto.ToString();
                if (cwPeriodFromTo == "") cwPeriodFromTo = $"{openAccountPeriod}-01 ~ {Convert.ToDateTime($"{openAccountPeriod}-01").AddMonths(1).AddDays(-1).ToString("yyyy-MM-dd")}";

                string codeLengthStr = setting.accountingcodelength.ToString();
                if (codeLengthStr != "")
                {
                    List<int> codeLengthList = codeLengthStr.Substring(1, codeLengthStr.Length - 2).Split(',').Select(Int32.Parse).ToList();
                    accountingCodeLength = codeLengthList;
                }
                autoCreateVoucher = Convert.ToBoolean(setting.autocreatevoucher);
                autoApproveVoucher = Convert.ToBoolean(setting.autoapprovevoucher);
                saleIncomeAndCost = Convert.ToBoolean(setting.saleincomeandcost);
                //cwBizPeriodSame= Convert.ToBoolean(setting.cwbizperiodsame);
            }

            dynamic g_co = await CDbDealer.Get1RecordFromSQLAsync($"select business_start_period from g_company where company_id={companyID}", cmd);
            string monthRangeFrom = "";
            if (g_co.business_start_period != "")
            {
                DateTime bizStartDate = Convert.ToDateTime(g_co.business_start_period);
                monthRangeFrom = bizStartDate.GetMonthStart().ToString("yyyy-MM-dd");
            }

            return Json(new { result="OK", msg="", companyName, useAccounting, openAccountPeriod, cwPeriodFromTo, accountingCodeLength, autoCreateVoucher, autoApproveVoucher, saleIncomeAndCost, monthRangeFrom });
        }

        /// <summary>
        /// 切换时间的change事件
        /// </summary>
        [HttpGet]
        public async Task<JsonResult> GetCwPeriodFromTo(string operKey, string openAccountPeriod)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            DateTime openAccountDay = Convert.ToDateTime($"{openAccountPeriod}-01");
            dynamic g_co = await CDbDealer.Get1RecordFromSQLAsync($"select business_start_period from g_company where company_id={companyID}", cmd);//只有第一个期间可能不完整
            string cwPeriodFromTo = GetCwPeriodFromToInside(openAccountDay, g_co);
            return Json(new { result = "OK", msg = "", cwPeriodFromTo });
        }

        public string GetCwPeriodFromToInside(DateTime openAccountDay, dynamic g_co)
        {
            string cwPeriodFromTo = "";
            if (g_co.business_start_period != "")//启用了业务期间
            {
                DateTime bizStartDay = Convert.ToDateTime(g_co.business_start_period);
                if (bizStartDay.Year == openAccountDay.Year && bizStartDay.Month == openAccountDay.Month && bizStartDay.Day != 1)
                {
                    DateTime bizEndDay = new DateTime(bizStartDay.Year, bizStartDay.Month, 1).AddMonths(1).AddDays(-1);
                    cwPeriodFromTo = $"{g_co.business_start_period} ~ {bizEndDay.ToString("yyyy-MM-dd")}";
                }
            }
            if (cwPeriodFromTo == "")
            {
                DateTime cwStartDay = openAccountDay;
                DateTime cwEndDay = cwStartDay.AddMonths(1).AddDays(-1);
                cwPeriodFromTo = $"{cwStartDay.ToString("yyyy-MM-dd")} ~ {cwEndDay.ToString("yyyy-MM-dd")}";
            }
            return cwPeriodFromTo;
        }

        [HttpPost]
        public async Task<JsonResult> SaveAccount([FromBody] dynamic jsonData)
        {
            string msg = "";
            string result = "OK";

            //get data from page
            dynamic data = JsonConvert.DeserializeObject(jsonData.ToString());
            if (data.companyName.ToString() == "") return Json(new { result = "Error", msg = "请在【设置-公司设置】中保存公司名称" });
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            companyName= data.companyName.ToString();
            useAccounting = Convert.ToBoolean(data.useAccounting);
            openAccountPeriod = data.openAccountPeriod;
            autoCreateVoucher = Convert.ToBoolean(data.autoCreateVoucher);
            autoApproveVoucher = Convert.ToBoolean(data.autoApproveVoucher);
            saleIncomeAndCost = Convert.ToBoolean(data.saleIncomeAndCost);
            //cwBizPeriodSame = Convert.ToBoolean(data.cwBizPeriodSame);
            string oldCLStr = "[4,2,2,2,2]";//默认编码长度
            string codeLengthStr=data.accountingCodeLength.ToString();//新的编码长度
            accountingCodeLength = JsonConvert.DeserializeObject<List<int>>(codeLengthStr);
            cmd.ActiveDatabase = "";
            cmd.company_id = companyID;

            //get data from db
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("company_setting", $"select setting from company_setting where company_id={companyID}");
            QQ.Enqueue("g_co", $"select business_start_period, business_period from g_company where company_id={companyID}");
            QQ.Enqueue("cw_subject", $@"
                select sub_code from cw_subject where company_id={companyID} and sub_code in (1,2,3,4,5) and level is null and direction is not null
                union all
                select sub_code from cw_subject where company_id={companyID} and sub_code not in (0,1,2,3,4,5) and (level is null or direction is null)
                union all
                select sub_code from cw_subject where company_id={companyID} and sub_name='本年利润' and direction=-1 and level=1 and sub_code=3103");
            QQ.Enqueue("vo_cost_month", $"select sheet_id from cw_voucher_main where company_id={companyID} and period=(select concat(coalesce(setting ->> 'accountingPeriod','9999-12'),'-01') as period from company_setting where company_id={companyID})::date and make_brief='结转销售成本' and red_flag is null");
            QQ.Enqueue("vo_cost_sheet", $@"select m.sheet_id from cw_voucher_main m 
                        left join cw_voucher_sheet_mapper map on m.sheet_id=map.voucher_id
                        left join cw_voucher_detail d on m.sheet_id=d.sheet_id 
                        left join cw_subject s on d.sub_id=s.sub_id
                        where m.company_id={companyID} and m.period=(select concat(coalesce(setting ->> 'accountingPeriod','9999-12'),'-01') as period from company_setting where company_id={companyID})::date and m.red_flag is null and map.business_sheet_id is not null and substr(s.sub_code::text,1,4)='5401' and d.remark not like '%赠品%'");
            QQ.Enqueue("cs_init0", $"select * from cw_subject where company_id={companyID} order by sub_id");
            QQ.Enqueue("cs_init", $"select * from cw_subject where company_id={companyID} order by sub_code::text");
            QQ.Enqueue("brief", $"select * from info_sheet_detail_brief where company_id={companyID} and brief_text='赠品'");
            QQ.Enqueue("op_bal", $@"select company_id from cw_op_sub_init_main where company_id={companyID} union all select company_id from cw_op_sub_init_detail where company_id={companyID}");
            QQ.Enqueue("cw_sub_balance", $@"select * from cw_sub_balance where company_id={companyID}");

            dynamic company_setting = null;
            dynamic g_co = null;
            dynamic cw_subject = null;
            dynamic vo_cost_month = null;//1
            dynamic vo_cost_sheet = null;//1
            dynamic cs_init0 = null;//2
            dynamic cs_init = null;//2
            dynamic brief = null;//2
            dynamic op_bal = null;//3
            dynamic cw_sub_balance = null;//4
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "company_setting") company_setting = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "g_co")
                {
                    g_co = CDbDealer.Get1RecordFromDr(dr, false);
                    if (g_co.business_start_period != "")
                    {
                        if (Convert.ToDateTime($"{openAccountPeriod}-01") < Convert.ToDateTime($"{g_co.business_start_period.Substring(0, 7)}-01"))
                        {
                            return Json(new { result = "Error", msg = "财务开账时间不能早于业务开账时间，请查阅【设置-业务结账】" });
                        }
                    }
                }
                else if (sqlName == "cw_subject") cw_subject = CDbDealer.GetRecordsFromDr(dr, false);
                else if (sqlName == "vo_cost_month") vo_cost_month = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "vo_cost_sheet") vo_cost_sheet = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "cs_init0") cs_init0 = CDbDealer.GetRecordsFromDr(dr, false);
                else if (sqlName == "cs_init") cs_init = CDbDealer.GetRecordsFromDr(dr, false);
                else if (sqlName == "brief") brief = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "op_bal") op_bal = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "cw_sub_balance") cw_sub_balance = CDbDealer.Get1RecordFromDr(dr, false);
            }
            QQ.Clear();
            cwPeriodFromTo = GetCwPeriodFromToInside(Convert.ToDateTime($"{openAccountPeriod}-01"), g_co);

            #region 待修改
            //dynamic totalCheck = await CDbDealer.Get1RecordFromSQLAsync($"select sub_id from cw_subject where company_id={companyID} and (level is null or direction is null)", cmd); // or level not between -1 and 3
            //if (totalCheck != null)
            //{
            //    return new JsonResult(new { result = "Error", msg = "科目逻辑错误，请联系管理员修改" });
            //}
            #endregion

            var tran = cmd.Connection.BeginTransaction();
            //1.保存公司设置
            dynamic setting = JsonConvert.DeserializeObject(company_setting.setting);
            if (setting.Property("accountingCodeLength") != null) oldCLStr = setting.accountingCodeLength.ToString();
            bool oldUseAc = Convert.ToBoolean(setting.useAccounting);
            SqlMsg sm1 = GetCompanySettingSaveSql(setting, oldUseAc, companyID, vo_cost_month, vo_cost_sheet);
            if (sm1.msg != "")
            {
                return Json(new { result = "Error", sm1.msg });
            }
            else
            {
                cmd.CommandText = sm1.sql;
                await cmd.ExecuteNonQueryAsync();
            }

            if (!oldUseAc)//没开账时的初始化
            {
                SqlMsg sm = new SqlMsg();
                //2.将专财科目更新过来
                if (cw_subject.Count !=6)
                {
                    SqlMsg sm2 = await GetInitUpdateSubjectSql(companyID, cs_init0, cs_init, brief);
                    if (sm2.msg != "")
                    {
                        tran.Rollback();
                        return Json(new { result = "Error", sm2.msg });
                    }
                    sm.sql += sm2.sql;
                }
                //3.初始化科目期初表
                if (op_bal != null) return Json(new { result = "Error", msg = "存在财务期初数据，请先清理！" });
                sm.sql = $"insert into cw_op_sub_init_main (company_id,sheet_id,happen_time,period) values ({companyID},1,'{DateTime.Now}','{openAccountPeriod}'); ";
                sm.sql += $"insert into cw_op_sub_init_detail (company_id, sheet_id, sub_id) select {companyID} as company_id, 1 as sheet_id, sub_id from cw_subject where company_id={companyID} and level<>0 and level is not null; ";
                //4.初始化科目余额表
                if (cw_sub_balance != null) return Json(new { result = "Error", msg = "存在财务余额数据，请先清理！" });
                sm.sql += $"insert into cw_sub_balance (company_id,period,sub_id) select {companyID} as company_id, '{openAccountPeriod}-01' as period, sub_id from cw_subject where company_id={companyID}; ";
                //5. 业务期间结账至会计初始期间（必须）
                if (g_co.business_start_period != "")
                {
                    SqlMsg sm5 = UpdateBizPeriod(companyID, operID, cwPeriodFromTo, g_co);
                    if (sm5.msg.ToString() != "")
                    {
                        tran.Rollback();
                        return Json(new { result = "Error", sm5.msg });
                    }
                    sm.sql += sm5.sql;
                }
                //6. 备注信息“赠品”添加默认主营业务成本科目
                sm.sql += $@"update info_sheet_detail_brief b set sub_id=cw.sub_id from cw_subject cw where b.company_id={companyID} and b.brief_text='赠品' and cw.company_id=b.company_id and cw.sub_code=5401 and b.sub_id is null;
                    INSERT INTO info_sheet_detail_brief (company_id, brief_text, sheet_type, is_price_remember, sub_id, default_for_give, relate_supplier_fee)
                    SELECT company_id, '赠品' AS brief_text, 'X' AS sheet_type, FALSE AS is_price_remember, sub_id, FALSE AS default_for_give, FALSE AS relate_supplier_fee
                    FROM cw_subject
                    WHERE company_id = {companyID} AND sub_code = 5401 AND NOT EXISTS ( SELECT 1 FROM info_sheet_detail_brief WHERE company_id = {companyID} AND brief_text = '赠品' );";

                cmd.CommandText = sm.sql;
                await cmd.ExecuteNonQueryAsync();
            }
            /*//6.根据accountingCodeLength修改科目编码长度
            dynamic r5 = (await SaveCodeLength(cw_subject, oldCLStr, companyID)).Value as dynamic;
            if (r5.msg.ToString() != "")
            {
                tran.Rollback();
                await CwLog.Save(companyID, operID, null, "OpeningAccount", $"Error: {r5.msg}; save opening account; save table company_setting: {log_properties}autoCreateVoucher{log_msg}; update code length, save table cw_subject: sub_code", cmd);
                return Json(new { result = "Error", r5.msg });
            }*/

            tran.Commit();
            await CwLog.Save(companyID, operID, null, "OpeningAccount", $"OK; {(oldUseAc ? "save" : "open")} account; OpenPeriod: {openAccountPeriod}; cwPeriodFromTo: {cwPeriodFromTo}; BizPeriod: {g_co.business_period}; AutoCreateVo: {autoCreateVoucher}; AntoApproveVo: {autoApproveVoucher}; saleIncomeAndCost: {saleIncomeAndCost}; ", cmd);

            return Json(new { result, msg });
        }

        public SqlMsg GetCompanySettingSaveSql([FromQuery]dynamic setting, bool oldUseAc, string companyID, [FromQuery] dynamic vo_cost_month, [FromQuery] dynamic vo_cost_sheet)
        {
            SqlMsg sm = new SqlMsg();
            /*if (setting.Property("companyName") != null)//公司名称
            {
                setting.companyName = companyName;
            }
            else
            {
                setting.Add("companyName", companyName);
            }*/
            if (setting.Property("useAccounting") != null)//是否开账
            {
                setting.useAccounting = useAccounting;
            }
            else
            {
                setting.Add("useAccounting", useAccounting);
            }
            if (setting.Property("openAccountPeriod") != null)//开账会计期间
            {
                setting.openAccountPeriod = openAccountPeriod;
            }
            else
            {
                setting.Add("openAccountPeriod", openAccountPeriod);
            }
            if (!oldUseAc)
            {
                if (setting.Property("accountingPeriod") != null)//当前会计期间
                {
                    setting.accountingPeriod = openAccountPeriod;
                }
                else
                {
                    setting.Add("accountingPeriod", openAccountPeriod);
                }
            }
            if (setting.Property("accountingCodeLength") != null)//编码长度
            { 
                setting.accountingCodeLength = $"[{string.Join(',', accountingCodeLength)}]";
            }
            else
            {
                setting.Add("accountingCodeLength", $"[{string.Join(',', accountingCodeLength)}]");
            }
            if (setting.Property("autoCreateVoucher") != null)//是否业务单据自动创建凭证
            {
                setting.autoCreateVoucher = autoCreateVoucher;
            }
            else
            {
                setting.Add("autoCreateVoucher", autoCreateVoucher);
            }
            if (setting.Property("autoApproveVoucher") != null)//是否单据转凭证自动审核
            {
                setting.autoApproveVoucher = autoApproveVoucher;
            }
            else
            {
                setting.Add("autoApproveVoucher", autoApproveVoucher);
            }
            //是否销售收入成本同时生成
            if (saleIncomeAndCost)
            {
                if (vo_cost_month != null)
                {
                    sm.msg = "本月已生成结转成本凭证，无法修改【销售收入成本同时生成】属性";
                    return sm;
                }
            }
            else
            {
                if (vo_cost_sheet != null)
                {
                    sm.msg = "本月已生成销售收入成本同步结转的凭证，无法修改【销售收入成本同时生成】属性";
                    return sm;
                }
            }
            if (setting.Property("saleIncomeAndCost") != null)
            {
                setting.saleIncomeAndCost = saleIncomeAndCost;
            }
            else
            {
                setting.Add("saleIncomeAndCost", saleIncomeAndCost);
            }
            /*if (setting.Property("cwBizPeriodSame") != null)//是否财务反结账/反开账期间与业务同步
            {
                setting.cwBizPeriodSame = cwBizPeriodSame;
            }
            else
            {
                setting.Add("cwBizPeriodSame", cwBizPeriodSame);
            }*/
            if (setting.Property("cwPeriodFromTo") != null)//财务期间始末
            {
                setting.cwPeriodFromTo = cwPeriodFromTo;
            }
            else
            {
                setting.Add("cwPeriodFromTo", cwPeriodFromTo);
            }

            sm.sql= $@"update company_setting set setting='{JsonConvert.SerializeObject(setting)}' where company_id={companyID}; ";
            return sm;
        }

        /*public async Task<JsonResult> SaveCodeLength(dynamic cw_subject, string oldCLStr, string companyID)
        {
            List<int> cl = oldCLStr.Substring(1, oldCLStr.Length - 2).Split(',').Select(Int32.Parse).ToList();
            for(int j=1; j < cl.Count(); j++)
            {
                cl[j] = cl[j - 1] + cl[j];
            }
            string sql = "";
            foreach (dynamic subject in cw_subject)
            {
                string newCL = "";
                int.TryParse(subject.level,out int level);
                if (level == 1 || level ==0) continue;
                for (int i=1;i<= level; i++)
                {
                    if (i == 1)
                    {
                        string sc1 = subject.sub_code.ToString().Substring(0, cl[i - 1]);
                        newCL += sc1;
                    }
                    else
                    {
                        int thisLenth = cl[i - 1] - cl[i - 2];
                        string sc2 = subject.sub_code.ToString().Substring(cl[i-2], thisLenth);
                        if (accountingCodeLength[i-1] > thisLenth)//新>旧
                        {
                            sc2 = $"{new string('0', accountingCodeLength[i-1] - thisLenth)}{sc2}";
                        }
                        else//旧>=新
                        {
                            sc2 = sc2.Substring(thisLenth - accountingCodeLength[i-1], sc2.Length - (thisLenth - accountingCodeLength[i - 1]));
                        }
                        newCL += sc2;
                    }
                }
                if (newCL == subject.sub_code.ToString()) continue;
                sql += $"update cw_subject set sub_code={newCL} where company_id={companyID} and sub_id={subject.sub_id.ToString()};";
            }
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg });
        }*/

        public SqlMsg UpdateBizPeriod(string companyID, string operID, string cwPeriodFromTo, dynamic g_co)
        {
            SqlMsg sm = new SqlMsg();
            DateTime bizPeriod = Convert.ToDateTime(g_co.business_period);
            bizPeriod = bizPeriod.GetMonthStart();
            DateTime cwPeriod = Convert.ToDateTime(cwPeriodFromTo.Split(" ~ ")[0]);
            cwPeriod = cwPeriod.GetMonthStart();
            //如果当前业务期(期初日)>=财务开账期(期初日)：什么也不做
            if (bizPeriod >= cwPeriod)
            {
                return sm;
            }
            //如果当前业务期早于财务开账期：将业务结账期结账至会计开账前一期，也就是使业务当前期=财务开账期
            sm.sql += $"update g_company set business_period='{cwPeriod}' where company_id={companyID}; ";
            while (bizPeriod < cwPeriod)
            {
                sm.sql += $"insert into business_monthly_closing (company_id, period, make_time, maker_id) values ({companyID}, '{bizPeriod}', '{DateTime.Now}', {operID}); ";
                bizPeriod = bizPeriod.AddMonths(1);
            }
            return sm;
        }

        public async Task<SqlMsg> GetInitUpdateSubjectSql(string  companyID, [FromQuery]dynamic cs_init0, [FromQuery]dynamic cs_init, [FromQuery]dynamic brief)
        {
            SqlMsg sm = new SqlMsg();
            if (cs_init.Count == 10)
            {
                if ((Int32.Parse(cs_init[0].sub_code) == 0 && cs_init[0].sub_name == "全部") &&
                    (Int32.Parse(cs_init[1].sub_code) == 10 && cs_init[1].sub_name == "资产类" && cs_init[1].mother_id == cs_init[0].sub_id) &&
                    (Int32.Parse(cs_init[2].sub_code) == 1001 && cs_init[2].sub_name == "现金" && cs_init[2].mother_id == cs_init[1].sub_id) &&
                    (Int32.Parse(cs_init[3].sub_code) == 1002 && cs_init[3].sub_name == "银行" && cs_init[3].mother_id == cs_init[1].sub_id) &&
                    (Int32.Parse(cs_init[4].sub_code) == 100201 && cs_init[4].sub_name == "微信支付" && cs_init[4].mother_id == cs_init[3].sub_id) &&
                    (Int32.Parse(cs_init[5].sub_code) == 100202 && cs_init[5].sub_name == "支付宝" && cs_init[5].mother_id == cs_init[3].sub_id) &&
                    (Int32.Parse(cs_init[6].sub_code) == 100203 && cs_init[6].sub_name == "银行卡" && cs_init[6].mother_id == cs_init[3].sub_id) &&
                    //(Int32.Parse(cs[7].sub_code) == 1123 && cs[7].sub_name == "预付款" && cs[7].mother_id == cs[1].sub_id) &&
                    //(Int32.Parse(cs[8].sub_code) == 22 && cs[8].sub_name == "负债类" && cs[8].mother_id == cs[0].sub_id) &&
                    //(Int32.Parse(cs[9].sub_code) == 2203 && cs[9].sub_name == "预收" && cs[9].mother_id == cs[8].sub_id) &&
                    //(Int32.Parse(cs[10].sub_code) == 220301 && cs[10].sub_name == "预收款" && cs[10].mother_id == cs[9].sub_id) &&
                    //(Int32.Parse(cs[11].sub_code) == 60 && cs[11].sub_name == "损益类（收入）" && cs[11].mother_id == cs[0].sub_id) &&
                    //(Int32.Parse(cs[12].sub_code) == 6051 && cs[12].sub_name == "其他收入" && cs[12].mother_id == cs[11].sub_id) &&
                    //(Int32.Parse(cs[13].sub_code) == 605101 && cs[13].sub_name == "厂家返点" && cs[13].mother_id == cs[12].sub_id) &&
                    (Int32.Parse(cs_init[14].sub_code) == 66 && cs_init[14].sub_name == "损益类(支出)" && cs_init[14].mother_id == cs_init[0].sub_id) &&
                    (Int32.Parse(cs_init[15].sub_code) == 6601 && cs_init[15].sub_name == "路费" && cs_init[15].mother_id == cs_init[14].sub_id) &&
                    (Int32.Parse(cs_init[16].sub_code) == 6602 && cs_init[16].sub_name == "餐补" && cs_init[16].mother_id == cs_init[14].sub_id))
                {
                    sm.sql = $"select yj_update_existing_subject({companyID},10);";
                }
            }
            if (cs_init.Count == 13)
            {
                if ((Int32.Parse(cs_init[0].sub_code) == 0 && cs_init[0].sub_name == "全部") &&
                    (Int32.Parse(cs_init[1].sub_code) == 10 && cs_init[1].sub_name == "资产类" && cs_init[1].mother_id == cs_init[0].sub_id) &&
                    (Int32.Parse(cs_init[2].sub_code) == 1001 && cs_init[2].sub_name == "现金" && cs_init[2].mother_id == cs_init[1].sub_id) &&
                    (Int32.Parse(cs_init[3].sub_code) == 1002 && cs_init[3].sub_name == "银行" && cs_init[3].mother_id == cs_init[1].sub_id) &&
                    (Int32.Parse(cs_init[4].sub_code) == 100201 && cs_init[4].sub_name == "微信支付" && cs_init[4].mother_id == cs_init[3].sub_id) &&
                    (Int32.Parse(cs_init[5].sub_code) == 100202 && cs_init[5].sub_name == "支付宝" && cs_init[5].mother_id == cs_init[3].sub_id) &&
                    (Int32.Parse(cs_init[6].sub_code) == 100203 && cs_init[6].sub_name == "银行卡" && cs_init[6].mother_id == cs_init[3].sub_id) &&
                    //(Int32.Parse(cs[7].sub_code) == 1123 && cs[7].sub_name == "预付款" && cs[7].mother_id == cs[1].sub_id) &&
                    (Int32.Parse(cs_init[8].sub_code) == 22 && cs_init[8].sub_name == "负债类" && cs_init[8].mother_id == cs_init[0].sub_id) &&
                    (Int32.Parse(cs_init[9].sub_code) == 2203 && cs_init[9].sub_name == "预收" && cs_init[9].mother_id == cs_init[8].sub_id) &&
                    (Int32.Parse(cs_init[10].sub_code) == 220301 && cs_init[10].sub_name == "预收款" && cs_init[10].mother_id == cs_init[9].sub_id) &&
                    //(Int32.Parse(cs[11].sub_code) == 60 && cs[11].sub_name == "损益类（收入）" && cs[11].mother_id == cs[0].sub_id) &&
                    //(Int32.Parse(cs[12].sub_code) == 6051 && cs[12].sub_name == "其他收入" && cs[12].mother_id == cs[11].sub_id) &&
                    //(Int32.Parse(cs[13].sub_code) == 605101 && cs[13].sub_name == "厂家返点" && cs[13].mother_id == cs[12].sub_id) &&
                    (Int32.Parse(cs_init[14].sub_code) == 66 && cs_init[14].sub_name == "损益类(支出)" && cs_init[14].mother_id == cs_init[0].sub_id) &&
                    (Int32.Parse(cs_init[15].sub_code) == 6601 && cs_init[15].sub_name == "路费" && cs_init[15].mother_id == cs_init[14].sub_id) &&
                    (Int32.Parse(cs_init[16].sub_code) == 6602 && cs_init[16].sub_name == "餐补" && cs_init[16].mother_id == cs_init[14].sub_id))
                {
                    sm.sql = $"select yj_update_existing_subject({companyID},13);";
                }
            }
            if (cs_init.Count == 17)
            {
                if ((Int32.Parse(cs_init[0].sub_code) == 0 && cs_init[0].sub_name == "全部") &&
                    (Int32.Parse(cs_init[1].sub_code) == 10 && cs_init[1].sub_name == "资产类" && cs_init[1].mother_id == cs_init[0].sub_id) &&
                    (Int32.Parse(cs_init[2].sub_code) == 1001 && cs_init[2].sub_name == "现金" && cs_init[2].mother_id == cs_init[1].sub_id) &&
                    (Int32.Parse(cs_init[3].sub_code) == 1002 && cs_init[3].sub_name == "银行" && cs_init[3].mother_id == cs_init[1].sub_id) &&
                    (Int32.Parse(cs_init[4].sub_code) == 100201 && cs_init[4].sub_name == "微信支付" && cs_init[4].mother_id == cs_init[3].sub_id) &&
                    (Int32.Parse(cs_init[5].sub_code) == 100202 && cs_init[5].sub_name == "支付宝" && cs_init[5].mother_id == cs_init[3].sub_id) &&
                    (Int32.Parse(cs_init[6].sub_code) == 100203 && cs_init[6].sub_name == "银行卡" && cs_init[6].mother_id == cs_init[3].sub_id) &&
                    (Int32.Parse(cs_init[7].sub_code) == 1123 && cs_init[7].sub_name == "预付款" && cs_init[7].mother_id == cs_init[1].sub_id) &&
                    (Int32.Parse(cs_init[8].sub_code) == 22 && cs_init[8].sub_name == "负债类" && cs_init[8].mother_id == cs_init[0].sub_id) &&
                    (Int32.Parse(cs_init[9].sub_code) == 2203 && cs_init[9].sub_name == "预收" && cs_init[9].mother_id == cs_init[8].sub_id) &&
                    (Int32.Parse(cs_init[10].sub_code) == 220301 && cs_init[10].sub_name == "预收款" && cs_init[10].mother_id == cs_init[9].sub_id) &&
                    (Int32.Parse(cs_init[11].sub_code) == 60 && cs_init[11].sub_name == "损益类（收入）" && cs_init[11].mother_id == cs_init[0].sub_id) &&
                    (Int32.Parse(cs_init[12].sub_code) == 6051 && cs_init[12].sub_name == "其他收入" && cs_init[12].mother_id == cs_init[11].sub_id) &&
                    (Int32.Parse(cs_init[13].sub_code) == 605101 && cs_init[13].sub_name == "厂家返点" && cs_init[13].mother_id == cs_init[12].sub_id) &&
                    (Int32.Parse(cs_init[14].sub_code) == 66 && cs_init[14].sub_name == "损益类(支出)" && cs_init[14].mother_id == cs_init[0].sub_id) &&
                    (Int32.Parse(cs_init[15].sub_code) == 6601 && cs_init[15].sub_name == "路费" && cs_init[15].mother_id == cs_init[14].sub_id) &&
                    (Int32.Parse(cs_init[16].sub_code) == 6602 && cs_init[16].sub_name == "餐补" && cs_init[16].mother_id == cs_init[14].sub_id))
                {
                    sm.sql = $"select yj_update_existing_subject({companyID},17);";
                }
            }
            if (cs_init.Count > 17)
            {
                foreach(dynamic sub in cs_init)
                {
                    if (Convert.ToInt32(sub.sub_id) > Convert.ToInt32(cs_init0[16].sub_id))
                    {
                        if(sub.sub_code=="22" || sub.sub_code == "60" || sub.sub_code.StartsWith("1001") || sub.sub_code.StartsWith("1002") || sub.sub_code.StartsWith("1123") || sub.sub_code.StartsWith("2203") || sub.sub_code.StartsWith("6051") || sub.sub_code.StartsWith("66"))
                        {
                            sm.sql = $"select yj_update_existing_subject({companyID},17);";
                        }
                        else
                        {
                            sm.sql = "";
                            break;
                        }
                    }
                }
            }
            if(sm.sql == "")
            {
                sm.msg = "科目不符合规则，请联系客服更新科目";
                return sm;
            }

            cmd.CommandText = sm.sql;
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                sm.sql = "";
                //更新助记码
                dynamic cs_afterInit = await CDbDealer.GetRecordsFromSQLAsync($"select sub_id,sub_name,sub_code from cw_subject where company_id={companyID} and level is not null;", cmd);
                foreach (dynamic sub in cs_afterInit)
                {
                    sm.sql += $"update cw_subject set py_str='{PinYinConverter.GetJP(sub.sub_name)}' where company_id={companyID} and sub_id={sub.sub_id};";
                }
                //更新赠品科目：默认主营业务成本
                if (brief == null)
                {
                    sm.sql += $"insert into info_sheet_detail_brief (company_id, brief_text, sub_id, sheet_type, is_price_remember) select {companyID} as company_id, '赠品' as brief_text, sub_id, 'X' as sheet_type,  false as is_price_remember from cw_subject where company_id={companyID} and substr(sub_code::text,1,4)='5401' and sub_id not in (select mother_id from cw_subject where company_id={companyID}) limit 1 ;";
                }
                else
                {
                    if (brief.sub_id == "")
                    {
                        sm.sql += $"update info_sheet_detail_brief set sub_id=(select sub_id from cw_subject where company_id={companyID} and substr(sub_code::text,1,4)='5401' and sub_id not in (select mother_id from cw_subject where company_id={companyID}) limit 1) where company_id={companyID} and brief_text='赠品' and sheet_type='X' and sub_id is null;";
                    }
                }
                return sm;
            }
            else
            {
                sm.msg = "生成科目有误";
                return sm;
            }

        }

        [HttpPost]
        public async Task<JsonResult> ClearAccount([FromBody] dynamic data)
        {
            string msg = "";
            Security.GetInfoFromOperKey(data.operKey.ToString(), out string companyID, out string operID);
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($@"select 
                    coalesce(setting ->>  'useAccounting','false') useaccounting, 
                    coalesce(setting ->>  'openAccountPeriod','') openaccountperiod, 
                    coalesce(setting ->>  'accountingPeriod','') accountingperiod  from company_setting where company_id = {companyID}", cmd);
            
            if (setting == null)
            {
                msg = "请先保存公司设置";
                await CwLog.Save(companyID, operID, null, "ClearAccount", $"Error; clear cw account; {msg}; ", cmd);
                return Json(new { result = "Error", msg });
            }

            if (!Convert.ToBoolean(setting.useaccounting))
            {
                msg = "当前未开账，请重新打开页面";
                await CwLog.Save(companyID, operID, null, "ClearAccount", $"Error; clear cw account; {msg}; ", cmd);
                return Json(new { result = "Error", msg });
            }

			cmd.CommandText = $"select company_name from g_company where company_id={companyID};";
			object ov = await cmd.ExecuteScalarAsync();
			string companyName = "";
			if (ov != null && ov != DBNull.Value) companyName = ov.ToString();

			CMySbTransaction tran = cmd.Connection.BeginTransaction();
           
            cmd.CommandText = $"select * from yj_clearcwaccount({companyID},'{companyName}','');";
            string res = (await cmd.ExecuteScalarAsync()).ToString();
            if (res!="OK")
            {
                tran.Rollback();
                msg = $"反开账失败 {res}";
                await CwLog.Save(companyID, operID, null, "ClearAccount", $"Error: {msg}; clear cw account; open period before clear: {setting.openaccountperiod}; period before clear: {setting.accountingperiod}; ", cmd);
                return Json(new { result = "Error", msg });
            }

            //反开账同步还原业务账期至开账月前一期，相当于业务反结账（暂不采用）
            /*if (Convert.ToBoolean(data.cwBizPeriodSame))
            {
                cmd.CommandText = $@"update business_monthly_closing set red_flag='1', red_time='{DateTime.Now}' where company_id={companyID} and period>='{setting.openaccountperiod}-01' and red_flag is null; 
                                                    update g_company set business_period='{setting.openaccountperiod}-01' where company_id={companyID};";
                await cmd.ExecuteScalarAsync();
            }*/

            tran.Commit();
            await CwLog.Save(companyID, operID, null, "ClearAccount", $"OK; clear cw account; open period before clear: {setting.openaccountperiod}; period before clear: {setting.accountingperiod}; ", cmd);
            return Json(new { result = "OK", msg = "" });
        }
    }

    public class SqlMsg
    {
        public string sql { get; set; } = "";
        public string msg { get; set; } = "";
    }

}
