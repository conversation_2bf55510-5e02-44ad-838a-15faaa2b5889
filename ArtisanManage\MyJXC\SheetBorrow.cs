﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using NPOI.SS.Formula.Functions;
using ArtisanManage.Services.SheetService;
using ArtisanManage.MyCW;
using OBS.Model;
using ArtisanManage.Pages;
using ArtisanManage.WebAPI;
using ArtisanManage.YingJiangCommon.Controller.PromotionController;
using System.ComponentModel.Design;
using static ICSharpCode.SharpZipLib.Zip.ExtendedUnixData;

namespace ArtisanManage.MyJXC
{
    public class SheetRowBorrowItem : SheetRowMM
    {
        [FromFld("case when t.unit_factor::text=m_unit_factor then m_wholesale_price when t.unit_factor::text=b_unit_factor then b_wholesale_price else  s_wholesale_price end")] public string wholesale_price { get; set; }
        [SaveToDB][FromFld] public string recent_retail_price { get; set; }
        [SaveToDB][FromFld] public string last_time_price { get; set; }
        [SaveToDB] public bool cost_price_suspect { get; set; }

        [SaveToDB][FromFld] public string sn_code { get; set; }

        [FromFld] public string flow_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_provenance { get; set; }
        [SaveToDB] [FromFld] public override string trade_type { get; set; } = "";
        [FromFld("(case trade_type when 'J' then '借货' when 'H' then '还货' when 'DH' then '定货' when 'CL'then '陈列'  end) trade_type_name", LOAD_PURPOSE.SHOW)] public string trade_type_name { get; set; } = "";

        private string _attr_qty = "";
        
        [SaveToDB] [FromFld] public override string attr_qty {
            get {
                return _attr_qty;
            }
            set {
                if (value == "[]") value = "";
                _attr_qty = value;
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public override string sale_print_combine_attr { get; set; } = "";//合并打印多口味

       

        internal JArray _mum_attributes = null;
        [FromFld("case when son_mum_attributes is not null then son_mum_attributes else mum_attributes end", LOAD_PURPOSE.SHOW)]
        public string mum_attributes //前端用来在quantity单元格的render函数cellsrenderer_quantity判断是否需要展示属性按钮
        {
            get
            {
                if (_mum_attributes == null) return "";
                
                if (son_mum_item != "")
                {
                    for (int i = _mum_attributes.Count - 1; i >= 0; i--)
                    {
                        JObject a = (JObject)_mum_attributes[i];
                        JValue s = (JValue)a.GetValue("distinctStock");
                        if (s != null && s.ToString().ToLower() == "true")
                        {
                            _mum_attributes.RemoveAt(i);
                        }
                    }
                }
                if (_mum_attributes.Count == 0) return "";
                return JsonConvert.SerializeObject(_mum_attributes);
            }

            set
            {
                if (value == null) _mum_attributes = null;
                else
                {
                    _mum_attributes = JsonConvert.DeserializeObject<JArray>(value);
                }
            }
        }
        internal float cost_price_recent1;
        internal float cost_price_recent2;
        internal float cost_price_recent3;
        [SaveToDB] [FromFld("t.cost_price_buy", LOAD_PURPOSE.SHOW)] public string cost_price_buy { get; set; }
        [SaveToDB] [FromFld("t.cost_price_prop", LOAD_PURPOSE.SHOW)] public string cost_price_prop { get; set; }
        [SaveToDB][FromFld("t.cost_price_recent", LOAD_PURPOSE.SHOW)] public string cost_price_recent { get; set; }

        
        [FromFld("setting->>'costPriceType'", LOAD_PURPOSE.SHOW)]public string cost_price_type { get; set; }
        [FromFld("setting->>'recentPriceTime'", LOAD_PURPOSE.SHOW)] public string recent_price_time { get; set; }
        public decimal cost_price
        {
            get
            {
                decimal n = 0;
                if (cost_price_type == "2")
                {//参考成本价是加权平均价
                    if (cost_price_avg.IsValid())
                       n =CPubVars.ToDecimal(cost_price_avg) * unit_factor;
                }
                else if (cost_price_type == "3")
                {//参考成本价是预设进价
                    if (cost_price_buy.IsValid())
                        n = CPubVars.ToDecimal(cost_price_buy) * unit_factor;
                }
                else if (cost_price_type == "1")
                {//参考成本价是预设成本
                    if (cost_price_prop.IsValid())
                        n = CPubVars.ToDecimal(cost_price_prop) * unit_factor;
                }
                else if (cost_price_type == "4")
                {//参考成本价是最近平均进价
                    if (recent_price_time == "1")
                    {
                        n = CPubVars.ToDecimal(cost_price_recent1) * unit_factor;
                    }
                    else if (recent_price_time == "2")
                    {
                        n = CPubVars.ToDecimal(cost_price_recent2) * unit_factor;
                    }
                    else if (recent_price_time == "3")
                    {
                        n = CPubVars.ToDecimal(cost_price_recent3) * unit_factor;
                    }
                }
                //Console.WriteLine("recent_price_time为" + recent_price_time);
                //Console.WriteLine("cost_price_type为" + cost_price_type);
                n = Math.Round(n, 4);
                //n = CPubVars.ToDecimal(CPubVars.FormatMoney(n, 4));
                return n;
            }
        }
        
        public decimal cost_amount
        {
            get
            {
                decimal n = cost_price * quantity;
                n = Math.Round(n, 2);
               // n = CPubVars.ToDecimal(CPubVars.FormatMoney(n, 2));
                return n;
            }
        }

        public decimal money_amount
        {
            get
            {
                decimal amt = sub_amount;
                if (this.order_sub_id.IsValid()) amt = 0m;
                return amt; 
            }
        }
        public decimal profit
        {
            get
            {
                return Math.Round(sub_amount - cost_amount, 2); 
            }
        }
        public string profit_rate
        {
            get
            {
                string s = "";
                if (sub_amount != 0)
                {
                    s = CPubVars.FormatMoney(profit / sub_amount * 100, 1);
                }
                return s;
            }
        }
        public string scanBarcode
        {
            get; set;
        } = "";

        public bool isSpecialPrice { get; set; } = false;
        public string special_price { get; set; } = "";
        public string promotion_id { get; set; }
        public string promotion_type { get; set; }
        public string NoStockAttrs = "";
        public string order_flow_id = "";
        [SaveToDB] [FromFld] public string other_info {
            get {
                IDictionary<string, object> d= new ExpandoObject();
                if (scanBarcode != "") d["scanBarcode"] = scanBarcode;
                if (disp_flow_id != "") d["dispFlowID"] = disp_flow_id;
                if (disp_month_id != "") d["dispMonthID"] = disp_month_id;
                if (disp_sheet_id != "") d["dispSheetID"] = disp_sheet_id;
                if (disp_template_id != "") d["dispTmpID"] = disp_template_id;
                if (NoStockAttrs != "") d["noStockAttrs"] = NoStockAttrs;
                 
                if (order_sub_id != "") d["orderSubID"] = order_sub_id;
                if (order_item_sheets_id != "") d["orderItemSheetsID"] = CPubVars.GetCleanSeperateIDs(order_item_sheets_id);
                if (order_item_sheets_no != "") d["orderItemSheetsNO"] = CPubVars.GetCleanSeperateIDs(order_item_sheets_no);
                if (isSpecialPrice) d["isSpecialPrice"] = isSpecialPrice;
                if (special_price != "") d["specialPrice"] = special_price;
                if (promotion_id != "") d["promotion_id"] = promotion_id;
                if (promotion_type != "") d["promotion_type"] = promotion_type;
                if (order_flow_id != "") d["order_flow_id"] = order_flow_id;
                if (d.Count > 0)
                {
                    return JsonConvert.SerializeObject(d);
                }
                return "";
            }
            set
            {
                if (value.IsValid())
                {
                    dynamic d = JsonConvert.DeserializeObject(value);
                    if (d.scanBarcode != null) scanBarcode = d.scanBarcode;
                    if (d.dispFlowID != null) disp_flow_id = d.dispFlowID;
                    if (d.dispMonthID != null) disp_month_id = d.dispMonthID;
                    if (d.dispSheetID != null) disp_sheet_id = d.dispSheetID;
                    if (d.noStockAttrs != null) NoStockAttrs = d.noStockAttrs;
                    if (d.dispTmpID != null) disp_template_id = d.dispTmpID;
                    if (d.isSpecialPrice != null) isSpecialPrice = d.isSpecialPrice;
                    if (d.specialPrice != null) special_price = d.specialPrice;


                    if (d.orderSubID != null) order_sub_id = d.orderSubID;
                    if (d.orderItemSheetsID != null) order_item_sheets_id = CPubVars.GetCleanSeperateIDs( Convert.ToString(d.orderItemSheetsID) );
                    if (d.orderItemSheetsNO != null) order_item_sheets_no = CPubVars.GetCleanSeperateIDs(Convert.ToString(d.orderItemSheetsNO) );
                    if (d.promotion_id != null) promotion_id = d.promotion_id;
                    if (d.promotion_type != null) promotion_type = d.promotion_type;
                    if (d.order_flow_id != null) order_flow_id = d.order_flow_id;
                }
            }
        }
        [SaveToDB] [FromFld] public string order_sub_id { get; set; } = "";
        public string order_price
        {
            get
            {
                if (order_sub_id.IsValid())
                {
                    return real_price.ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public string order_item_sheets_no { get; set; } = "";
        public string order_item_sheets_id { get; set; } = "";
        [SaveToDB] [FromFld] public string disp_sheet_id { get; set; } = ""; //使用的陈列协议单号
        
        public string disp_template_id { get; set; } = ""; // 陈列协议模板
        [SaveToDB] [FromFld] public string disp_flow_id { get; set; } = ""; 
        [SaveToDB] [FromFld] public string disp_month_id { get; set; } = ""; //使用的陈列协议 对应数据库month1、month2
   
        internal decimal disp_month_left { get; set; } = 0; 
        [FromFld("cw.sub_name")] public string order_sub_name { get; set; }
        internal decimal order_qty { get; set; }
       

        internal bool AfterSheetHasStockQty = true;
        internal decimal before_qty { get; set; }

 //#region 用于定货会还货扣减
        //internal decimal OrderedBalance;
        //internal decimal OrderedQty;
        //internal string OrderUnitNo = "";
        //internal decimal OrderUnitFactor;
      //  internal string OrderPrice;
 //#endregion
        internal bool is_recent = false;
        internal decimal BorrowedQty = 0;
        internal bool HasBorrowedQty = false;
    }
    //这边定义返回的单据类型（方便后续共用sheet）
    public enum SHEET_BORROW_RETURN
    {
        EMPTY,
        NOT_RETURN,
        IS_RETURN
    }

    public class SheetBorrowItem : SheetMM<SheetRowBorrowItem>
    {
        [SaveToDB] [FromFld] public override string order_sheet_id { get; set; } = "";
        [SaveToDB] [FromFld] public bool is_retail { get; set; }

        [SaveToDB] [FromFld] public string shop_id { get; set; } = "";
        [SaveToDB] [FromFld] public string senders_id { get; set; } = "";
        [SaveToDB] [FromFld] public string senders_name { get; set; } = "";
        [SaveToDB] [FromFld] public string getter_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string getter_name { get; set; } = ""; 
        [SaveToDB][FromFld] public string settle_time { get; set; } = "";

        [SaveToDB][FromFld] public string review_time { get; set; } = "";
        [SaveToDB][FromFld] public string reviewer_id { get; set; } = "";
        public bool bReview { get; set; } = false;

        [FromFld(LOAD_PURPOSE.SHOW)] public string reviewer_name { get; set; } = "";
        [SaveToDB]
        public string cost_amount_avg
        {
            get
            {
                decimal cost_amt = 0;
                foreach (var row in this.SheetRows)
                {
                    if (!string.IsNullOrEmpty(row.cost_price_avg))
                        cost_amt += row.quantity * row.unit_factor * CPubVars.ToDecimal(row.cost_price_avg);
                }
                return CPubVars.FormatMoney(cost_amt, 2);
            }
        }
        [SaveToDB]
        public string cost_amount_buy
        {
            get
            {
                decimal cost_amt = 0;
                foreach (var row in this.SheetRows)
                {
                    if (!string.IsNullOrEmpty(row.cost_price_buy))
                        cost_amt += row.quantity * row.unit_factor * CPubVars.ToDecimal(row.cost_price_buy);
                }
                return CPubVars.FormatMoney(cost_amt, 2);
            }
        }
        [SaveToDB]
        public string cost_amount_recent
        {
            get
            {
                decimal cost_amt = 0;
                foreach (var row in this.SheetRows)
                {
                    if (!string.IsNullOrEmpty(row.cost_price_recent))
                        cost_amt += row.quantity * row.unit_factor * CPubVars.ToDecimal(row.cost_price_recent);
                }
                return CPubVars.FormatMoney(cost_amt, 2);
            }
        }
        [SaveToDB]
        public string cost_amount_prop
        {
            get
            {
                decimal cost_amt = 0;
                foreach (var row in this.SheetRows)
                {
                    if (!string.IsNullOrEmpty(row.cost_price_prop))
                        cost_amt += row.quantity * row.unit_factor * CPubVars.ToDecimal(row.cost_price_prop);
                }
                return CPubVars.FormatMoney(cost_amt, 2);
            }
        }

        //public string total_profit_amount { get; set; } = "";
        [SaveToDB] [FromFld] public string receive_addr { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string receive_addr_desc { get; set; } = "";
        public string receive_addr_show { get
			{
                return receive_addr_desc.IsValid() ? receive_addr_desc : sup_addr;
			} 
        }
        [SaveToDB] [FromFld] public string visit_id { get; set; } = "";
        
        [SaveToDB][FromFld] public string order_source { get; set; } = ""; // 单据来源

        [FromFld("sheet_print_count",LOAD_PURPOSE.SHOW)] public  override string print_count { get; set; } = "";

        public string van_id { get { return send_van_id; } }
        public string van_name { get { return send_van_name; } }

        
        // public string appendix_photos = "";

     
        public string order_source_name
        {
            get
            {
                if (order_source == "xcx")
                    return "商城";
                else if (order_source == "cashier")
                    return "收银系统";
                else
                    return "";
            }

        }
        public string bindSheetInfo { get; set; } = "";
        [SaveToDB] [FromFld] public override string sheet_attribute { 
            get
            { 
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                string baseAttr = base.sheet_attribute;
                if (baseAttr != "") sheetAttribute=JsonConvert.DeserializeObject<Dictionary<string, string>>(baseAttr);
                if (!string.IsNullOrEmpty(bindSheetInfo))
                {
                    sheetAttribute.Add("bindSheetInfo", bindSheetInfo);
                }
                foreach (var row in SheetRows)
                { 
                    //陈列协议
                    if (row.disp_flow_id != "" && row.quantity != 0! && !sheetAttribute.ContainsKey("display")) sheetAttribute.Add("display", "true");

                    string tradeType = (row.trade_type).ToLower();
                    if (row.trade_type != "" && row.trade_type != "x" && row.trade_type != "t" && !sheetAttribute.ContainsKey(tradeType)) sheetAttribute.Add(tradeType, "true");

                }
                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);
                return s;
            }
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);
                    if (sheetAttr.bindSheetInfo != null)
                    {
                        this.bindSheetInfo = sheetAttr.bindSheetInfo;
                    }

                }
                base.sheet_attribute = value;
             
            }
        }


        [FromFld(LOAD_PURPOSE.SHOW)] public string sender_mobile { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public  string license_no { get; set; } = "";

        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string order_sheet_no { get; set; } = "";
        //[FromFld("sheet_attribute->>'returnAmt'", LOAD_PURPOSE.SHOW)] public decimal return_amount { get; set; } = 0;
        public decimal sale_amount
        { 
            get {  return total_amount + return_amount;}
        }

        #region 支付系统相关
        [SaveToDB][FromFld] public string pay_bill_id { get; set; } = "";

        /// <summary> 关联支付系统订单的订单状态 </summary>
        [FromFld(LOAD_PURPOSE.SHOW)] public string payb_status { get; set; } = "";

        /// <summary> 关联支付系统订单的订单单号 </summary>
        [FromFld(LOAD_PURPOSE.SHOW)] public string payb_trade_no { get; set; } = "";

        public string payb_status_name
        {
            get
            {
                if (this.order_source == "xcx")
                {
                    return payb_status switch
                    {
                        WebAPI.PayBillController.BillStatus.Paid => "在线支付",
                        WebAPI.PayBillController.BillStatus.Unpaid => "未支付",
                        WebAPI.PayBillController.BillStatus.Returned => "已退款",
                        WebAPI.PayBillController.BillStatus.Canceled => "已取消",
                        _ => "货到付款",
                    };
                }
                else return "";
            }
        }
        #endregion

        // public string order_sheet_no { get; set; } = "";//转销售单显示单号
        // [FromFld(LOAD_PURPOSE.SHOW)] public string sup_tel { get; set; } = "";

        public string suspect_status = "0";
        [SaveToDB] [FromFld("to_char(t.send_time,'yyyy-MM-dd hh24:mi:ss') as send_time")] public string send_time { get; set; } = "";
       
        protected class DisplayMonth
        {
            public string disp_sheet_id { get; set; }
            public string disp_flow_id { get; set; }
            public string disp_sub_id { get; set; }
            public decimal disp_left_amount { get; set; }
            public string disp_month_id { get; set; }
        }
        protected class CInfoForApproveSale : CInfoForApprove
        {
            //public List<SheetRowBorrowItemOrder> OrderSheetRows = null;
            //public string new_recent_sale_time = "";
            //public bool OrderSheetIsImported = false;

        }

        [JsonConstructor]  //if there is more constructor, it is necessary to set on one of them
        public SheetBorrowItem(SHEET_BORROW_RETURN sheetReturn, LOAD_PURPOSE loadPurpose) : base("borrow_item_main", "borrow_item_detail", loadPurpose)
        {
            ConstructFun();
            sheet_type = sheetReturn == SHEET_BORROW_RETURN.IS_RETURN ? SHEET_TYPE.SHEET_RETURN_ITEM : SHEET_TYPE.SHEET_BORROW_ITEM;
        }
        private void ConstructFun()
        {
            MainLeftJoin += @$"
left join (select sheet_id as order_sheet_id,sheet_no as order_sheet_no from sheet_sale_order_main where company_id=~COMPANY_ID) so on t.order_sheet_id = so.order_sheet_id
left join sheet_status_sale ss on ss.company_id=~COMPANY_ID and t.sheet_id=ss.sheet_id
left join (select oper_id,oper_name as sender_name,mobile as sender_mobile from info_operator where company_id=~COMPANY_ID) sender on split_part(t.senders_id,',',1)::integer=sender.oper_id 
left join (select oper_id,oper_name as getter_name from info_operator where company_id=~COMPANY_ID) getter on t.getter_id=getter.oper_id
left join (select oper_id,oper_name as reviewer_name,company_id from info_operator) reviewer on t.company_id=reviewer.company_id and t.reviewer_id=reviewer.oper_id         

left join (select bill_id as payb_flowid, bill_status as payb_status, trade_no as payb_trade_no from pay_bill where company_id=~COMPANY_ID) payb on t.pay_bill_id = payb.payb_flowid
left join (select addr_id, addr_desc receive_addr_desc from info_client_address where company_id=~COMPANY_ID) addr on t.receive_addr=addr.addr_id
";
     
            DetailLeftJoin += $@"
left join (select company_id,attr_id,sale_combine_print as sale_print_combine_attr from info_attribute where company_id=~COMPANY_ID) attr on (son_attrs.son_mum_attributes->0->>'attrID')::integer=attr.attr_id
left join cw_subject  cw on cw.company_id=~COMPANY_ID and cw.sub_id=t.order_sub_id";
        }
        public SheetBorrowItem(LOAD_PURPOSE loadPurpose) : base("borrow_item_main", "borrow_item_detail", loadPurpose)
        {
            ConstructFun();
            sheet_type = SHEET_TYPE.SHEET_BORROW_ITEM;
        }
        public SheetBorrowItem() : base("borrow_item_main", "borrow_item_detail", LOAD_PURPOSE.SHOW)
        {
            ConstructFun();
        }
         
        public override string Init()
        {
            string msg = base.Init();
            if (msg != "") return msg;
            foreach (var row in SheetRows)
            {
                if(row!= null)
                {
                    if (row.trade_type == null || row.trade_type == "") row.trade_type = SheetType;
                    if (row.trade_type == "J" || row.trade_type == "H") row.inout_flag = -1;
                    if (row.trade_type == "KS") row.inout_flag = 0;
                }
                
            }
            return "";
        }



        //销售过程中，主要影响：货值-----> 数量改变，货值就会改变
        //                  质疑----->  数量的变化，是否在库存的范围内，即当库存由非负->负的过程，或者库存由负->非负的过程，都会影响（包含销售退货红冲所有情况）
        //                  商品质疑表中  ----->  若商品质疑状态变化，则记录  若商品库存通过单据变化，即当库存由非负->负的过程，或者库存由负->非负的过程，便会插入一条记录


    
 
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveSale();
            string sql = "";
            if (order_sheet_id != "")
            { 
                //一定要放在 base.GetInfoForApprove_SetQQ(QQ); 前面，为了先获取订单里的欠款，好做判断
                string dt = CPubVars.GetDateText(DateTime.Now.AddDays(-90));
                sql = $@"select total_amount-now_pay_amount-now_disc_amount as now_left_amount,is_imported as order_sheet_is_imported,sso.order_status 
from sheet_sale_order_main m 
LEFT JOIN sheet_status_order sso on sso.company_id = m.company_id and sso.sheet_id = m.sheet_id 
where m.company_id = {company_id} and m.sheet_id={order_sheet_id}  and red_flag is null and approve_time is not null and happen_time>'{dt}'";
                QQ.Enqueue("order_sheet_info", sql);

                //一定要放在前面，否则sheetmm无法判断负库存
                 sql = $@"select item_id,branch_id,branch_position,batch_id,sum(qty) as qty from (
    select item_id,COALESCE(d.branch_id,m.branch_id) as branch_id,coalesce(branch_position,'0') as branch_position,COALESCE(batch_id,'0') as batch_id,sum(quantity*unit_factor) qty from sheet_sale_order_detail d 
    left join sheet_sale_order_main m on m.sheet_id = d.sheet_id and m.company_id = {company_id}
    left join sheet_placeholder_order_main pm on pm.sale_order_sheet_id = m.sheet_id and pm.company_id = {company_id}
    where d.company_id = {company_id} and pm.sale_order_sheet_id is null  and m.sheet_id in ({order_sheet_id}) and m.red_flag is null and m.approve_time is not null and quantity>0 group by item_id,m.branch_id,d.branch_id,branch_position,batch_id
    union all
    select item_id,COALESCE(d.branch_id,pm.branch_id) as branch_id,coalesce(branch_position,'0') as branch_position,COALESCE(batch_id,'0') as batch_id,sum(quantity*unit_factor) qty from sheet_placeholder_order_detail d 
    left join sheet_placeholder_order_main pm on pm.sheet_id = d.sheet_id and pm.company_id = {company_id}
    left join sheet_sale_order_main m on m.sheet_id = pm.sale_order_sheet_id and m.company_id = {company_id}
    where d.company_id = {company_id} and pm.sale_order_sheet_id is not null  and m.sheet_id in ({order_sheet_id}) and m.red_flag is null and m.approve_time is not null and pm.red_flag is null and pm.approve_time is not null and quantity>0 group by item_id,d.branch_id,branch_position,batch_id,pm.branch_id
)t
group by item_id,branch_id,branch_position,batch_id
";
                QQ.Enqueue("sale_order_qty", sql);

            }
            if (senders_id.IsInvalid() && senders_name.IsValid())
            {
                var arr_sender = senders_name.Split(",");
                string senderNameStr = "";
                if(arr_sender.Length > 0)
                {
                    foreach( var s in arr_sender)
                    {
                        string name = s.ToString();
                        if (name.IsValid())
                        {
                            if (senderNameStr != "") senderNameStr += ",";
                            senderNameStr += "'"+name+"'";
                        }
                    }
                }
                if (senderNameStr.IsValid())
                {
                    sql = $@"SELECT string_agg(oper_id::text,',') senders_id FROM info_operator where company_id ={company_id} and oper_name in({senderNameStr});";
                    QQ.Enqueue("senders_id", sql);
                }

            }

            base.GetInfoForApprove_SetQQ(QQ);

            if (FIXING_ARREARS) return;

            string origItemsID = "";
           // string items_id = "";
            string sonItemsID = "";
            string mumItemsID = "";
            
            /* if (Convert.ToBoolean(left_amount)) {
                 sql = @$"select auxiliary_balance,seller_max_arrears from arrears_balance_auxiliary aba LEFT JOIN info_operator io
                                     on aba.company_id=io.company_id and aba.auxiliary_id = io.oper_id 
                                     where io.company_id={company_id} and aba.auxiliary_id={seller_id} ";
                 QQ.Enqueue("seller_auxiliary", sql);
                 sql = @$"select ios.max_arrears,ab.balance from info_supcust ios left join arrears_balance ab 
                                     on ios.company_id=ab.company_id and ios.supcust_id=ab.supcust_id
                                     where ios.company_id={company_id} and ios.supcust_id={supcust_id}";
                 QQ.Enqueue("supcustauxiliary", sql);
             }*/

            string borrow_items_id = "";
            string ordered_items_id = "";
            if (SheetRows.Count > 0)
            {
                string items_id_stock = "";
                foreach (SheetRowMM row in SheetRows)
                {
                    if (origItemsID != "") origItemsID += ",";
                    origItemsID += row.item_id;
 

                    if (row.son_mum_item != "")
                    { 
                        if (mumItemsID != "") mumItemsID += ",";
                        mumItemsID += row.son_mum_item; 
                    }

                    string mainItemId = row.son_mum_item.IsValid() ? row.son_mum_item : row.item_id;
                    if (row.trade_type=="J" || row.trade_type == "H")
                    {
                        if (borrow_items_id != "") borrow_items_id += ","; borrow_items_id += mainItemId;
                    }
                    else if (row.trade_type == "DH")
                    {
                        if (ordered_items_id != "") ordered_items_id += ","; ordered_items_id += mainItemId;
                    }
                    
                    if (items_id_stock != "") items_id_stock += ",";
                    items_id_stock += row.item_id;
                }
                if (borrow_items_id != "")
                {
                    if (borrow_items_id.Contains("16667576") && supcust_id == "2051256")
                    {

                    }
                    sql = $@"select item_id,borrowed_qty from borrowed_cust_items where company_id = {company_id} and cust_id = {supcust_id} and item_id in (SELECT case when son_mum_item is null then item_id else son_mum_item end item_id  FROM info_item_prop WHERE case when son_mum_item is null then item_id else son_mum_item end in ({borrow_items_id}) and company_id = {company_id})";
                    QQ.Enqueue("borrow", sql);
                }

                if (IsImported) return;
                /*
                               sql = @$"
               select ip.item_name,ip.item_id, ip.son_mum_item, attr.remember_price 
               FROM info_item_prop ip
               LEFT JOIN info_item_prop mum on ip.son_mum_item = mum.item_id and mum.company_id = {company_id}
               LEFT JOIN info_attribute attr on (mum.mum_attributes->0->>'attrID')::integer=attr.attr_id and attr.company_id = {company_id}
               where company_id = {company_id} and item_id in ({origItemsID}) and son_mum_item is not null and mum.mum_attributes is not null ";
                               QQ.Enqueue("attribute_items", sql);

                               sql = $@"
               SELECT ip.item_id, ip.son_mum_item, attr.remember_price 
               FROM      info_item_prop ip
               LEFT JOIN info_item_prop mum on ip.son_mum_item = mum.item_id and mum.company_id = {company_id}
               LEFT JOIN info_attribute attr on (mum.mum_attributes->0->>'attrID')::integer=attr.attr_id and attr.company_id = {company_id}
               where ip.company_id = {company_id} and ip.item_id in( {origItemsID}) and mum.mum_attributes is not null ";
                               QQ.Enqueue("attr_remember_price", sql);
                               */
                sql = $"select string_agg(brief_text, ',') briefs from info_sheet_detail_brief where company_id = {company_id} and (is_price_remember is not true)";
                QQ.Enqueue("briefs", sql);

                sql = @$"
select p.item_id,stock_sum as total_qty,cost_price_recent,
  item_cost_price_suspect, cost_price_avg, cost_price_spec, stock_sum * cost_price_avg as cost_amt 
from info_item_prop p 
left join 
(
    select item_id,sum(COALESCE(stock_qty,0)) as stock_sum 
    from stock where company_id={company_id} and item_id in ({items_id_stock}) group by item_id
) stock on stock.item_id = p.item_id
where p.company_id = {company_id} and p.item_id in ({items_id_stock}) order by item_id";


                QQ.Enqueue("stock_sum", sql);

                string condi = $" mu.item_id in ({origItemsID}) ";
                if (mumItemsID != "") condi += $"  or mu.item_id in ({mumItemsID}) ";
                sql = $@"
select mu.item_id,mu.unit_no as item_unit_no,mu.unit_factor item_unit_factor,lowest_price item_lowest_price,buy_price,cost_price_spec,cs.setting->>'unitPriceRelated' unit_price_related
from info_item_multi_unit mu
left join company_setting cs on cs.company_id={company_id}
where mu.company_id = {company_id} and ({condi});
";
                QQ.Enqueue("unit_price", sql);

                /*   sql = $@"select b.item_id,(case when b_unit_no is null then s_unit_no else b_unit_no end) unit_no, (case when b_unit_factor is null then s_unit_factor else b_unit_factor end) unit_factor,quantity,balance,prepay_sub_id as order_sub_id,order_price
                                   from items_ordered_balance b 
                                   left join (select item_id,b->>'f1' as b_unit_factor,s->>'f1' as s_unit_factor,b->>'f2' as b_unit_no,s->>'f2' as s_unit_no
                                                      from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,wholesale_price)) as json from info_item_multi_unit where company_id= {company_id} order by item_id',$$values ('s'::text),('b'::text)$$) 
                                                   as errr(item_id int, s jsonb,b jsonb)) mu on mu.item_id = b.item_id where b.company_id = {company_id} and b.item_id in ({items_id}) and b.supcust_id = {supcust_id};";
                */
                if (ordered_items_id != "")
                {
                    sql = $@"
select b.flow_id,b.item_id,b.unit_no,b.unit_factor,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no,quantity,b.quantity *b.order_price as balance,prepay_sub_id as order_sub_id,order_price,order_item_sheets_id,order_item_sheets_no
from items_ordered_balance b 
left join 
(
   select item_id,b->>'f1' as b_unit_factor,m->>'f1' as m_unit_factor,s->>'f1' as s_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no
   from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,wholesale_price)) as json from info_item_multi_unit where company_id= {company_id} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
   as errr(item_id int, s jsonb, m jsonb,b jsonb)
) mu on mu.item_id = b.item_id 
where b.company_id = {company_id} and b.item_id in (SELECT case when son_mum_item is null then item_id else son_mum_item end item_id  FROM info_item_prop WHERE item_id in ({ordered_items_id}) and company_id = {company_id}) and b.supcust_id = {supcust_id};";
                    QQ.Enqueue("items_ordered", sql);//定货
					 
                    
                }
               
                if (order_sheet_id != "")//获取订单商品清单，以便于清除订单占用库存
                {
                    sql = @$"
select item_id,unit_factor,quantity,COALESCE(d.branch_id,m.branch_id) as branch_id,COALESCE(d.branch_position,0) as branch_position,inout_flag,move_stock,COALESCE(d.batch_id,0) as batch_id
from sheet_sale_order_detail d
left join sheet_sale_order_main m on d.sheet_id=m.sheet_id and d.company_id=m.company_id 
left join sheet_status_order s on d.sheet_id=s.sheet_id and d.company_id=s.company_id 
where d.company_id={company_id} and d.sheet_id={order_sheet_id} and m.red_flag is null;";
                    QQ.Enqueue("sale_order_sheet", sql);
                    if (red_flag == "")
                    { 
                        sql = $"select sheet_id from sheet_sale_main where company_id={company_id} and order_sheet_id={order_sheet_id} and red_flag is null;";
                        QQ.Enqueue("other_sale_sheet_for_order", sql);
                    }
                    else if (red_flag=="2")
                    {
                        sql = @$"
                                SELECT t.op_id fROM (
		                                SELECT od.sale_order_sheet_id,op_type,om.op_id ,case when om.op_no is null then 'ZC'||od.op_id else om.op_no end move_to_van_op_no,((now()::date - om.happen_time::date)+1) assign_van_days,ROW_NUMBER() over(partition by od.sale_order_sheet_id ORDER BY om.happen_time DESC ) rn FROM op_move_to_van_detail od
		                                LEFT JOIN op_move_to_van_main om on od.company_id = om.company_id and od.op_id = om.op_id
		                                WHERE od.company_id =  {company_id} and om.red_flag is null and om.approve_time is not null  and sale_order_sheet_id ={order_sheet_id} 
                                ) t
                                WHERE t.rn = 1  and t.op_type in('2v','v2v') ;";
                        QQ.Enqueue("move_to_van", sql);
                        sql = $"select * from sheet_status_order where company_id={company_id} and sheet_id={order_sheet_id}";
                        QQ.Enqueue("sheet_status_order", sql);
                    
                    }
                }
                if (red_flag == "2")
                {
                    sql = $"select max(happen_time) recent_sale_time from sheet_sale_main where company_id={company_id} and happen_time>'{CPubVars.GetDateText(DateTime.Now.AddDays(-360))}' and supcust_id={supcust_id} and sheet_id<>{red_sheet_id} and sheet_type='X' and approve_time is not null and red_flag is null";
                    QQ.Enqueue("new_recent_sale_time", sql);
                }
              
                sql = $"select setting from company_setting where company_id = '{company_id}'";
                        QQ.Enqueue("setting", sql);
        

                
               
                sql = "";
                foreach (SheetRowBorrowItem row in SheetRows)
                {
                    if (row.disp_sheet_id != null && row.disp_sheet_id != "" && row.disp_flow_id != null && row.disp_flow_id != "")
                    {
                        string item_id = row.item_id;
                        if (row.son_mum_item.IsValid()) item_id = row.son_mum_item;
                        if (sql != "") sql += " union ";
                        sql += $@"select d.* from display_agreement_detail d left join display_agreement_main m on m.sheet_id = d.sheet_id and d.company_id = m.company_id 
                                 where d.company_id = {company_id} and supcust_id = {supcust_id} and d.sheet_id = {row.disp_sheet_id} and items_id like '%{item_id}%' and unit_no = '{row.unit_no}' and flow_id = {row.disp_flow_id}";

                    }
                }
                if (sql != "")
                {
                    sql += ";";
                    QQ.Enqueue("display", sql);
                }
            }


            string sub_ids = payway1_id;
            if (payway2_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway2_id;
            }
            if (payway3_id != "")
            {
                if (sub_ids != "") sub_ids += ","; sub_ids += payway3_id;
            }
            if (sub_ids != "")
            {
                sql = $"select sub_id, sub_type, is_order from cw_subject where company_id={company_id} and sub_id in ({sub_ids}) and sub_id not in (select case when (s.setting->>'feeOutSubForKS')::int is null then -1 else (s.setting->>'feeOutSubForKS')::int end sub_id from  company_setting s where s.company_id = {company_id});";
                QQ.Enqueue("payway_type", sql); 
            }
			if (!IsImported)
			{
                SetQQForWeChatInfo(QQ, supcust_id);
            }
           

        }

                
        // 第二步，销售单保存过程
        string NotRememberPriceBriefs = "";
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApproveSale();
            CInfoForApproveSale info = (CInfoForApproveSale)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            //比较总库存时，需要判断单据总数量 
            if (sqlName == "briefs")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec.briefs != "") NotRememberPriceBriefs = "," + rec.briefs + ",";
            }
            else if (sqlName == "stock_sum")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                string recentPriceTime = "1";
                if (info.CompanySetting != null && info.CompanySetting.recentPriceTime != null)
                {
                      recentPriceTime = Convert.ToString(info.CompanySetting.recentPriceTime);
                 }
               
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowBorrowItem row in SheetRows)
                    {
                        if (row.item_id == rec.item_id)
                        {
                            row.old_total_qty = CPubVars.ToDecimal(rec.total_qty != "" ? rec.total_qty : 0);
                            // row.old_cost_price_avg = CPubVars.ToDecimal(rec.cost_price_avg != "" ? rec.cost_price_avg : 0);
                            // row.old_cost_amt = CPubVars.ToDecimal(rec.cost_amt != "" ? rec.cost_amt : 0);
                            row.item_cost_price_suspect = Convert.ToBoolean(rec.item_cost_price_suspect != "" ? rec.item_cost_price_suspect : false);
                            row.cost_price_avg = rec.cost_price_avg != "" ? rec.cost_price_avg : "0";
                            if (rec.cost_price_recent != "")
                            {
                                dynamic pStr = JsonConvert.DeserializeObject(rec.cost_price_recent);
                                //Console.WriteLine("输出转换后的值：{0}" + "\n" + "转换后的类型：{1} " + "\n", pStr, pStr.GetType());
                                row.cost_price_recent1 = pStr.avg1;
                                row.cost_price_recent2 = pStr.avg2;
                                row.cost_price_recent3 = pStr.avg3;
                            }
                            else
                            {
                                row.cost_price_recent1 = 0;
                                row.cost_price_recent2 = 0;
                                row.cost_price_recent3 = 0;
                            }
                            
                            if (recentPriceTime == "1") row.cost_price_recent = row.cost_price_recent1.ToString();
                            else if (recentPriceTime == "2") row.cost_price_recent = row.cost_price_recent2.ToString();
                            else if (recentPriceTime == "3") row.cost_price_recent = row.cost_price_recent3.ToString();
                        }
                    }
                }
            }
            /*
            else if (sqlName == "attr_remember_price")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                if (records != null)
                {
                    foreach (dynamic rec in records)
                    {
                        if (((string)rec.remember_price).ToLower() == "true")
                        {
                            var row = this.SheetRows.Find(row => row.item_id == rec.item_id);
                            if(row!=null) row.attrRememberPrice = true;
                        }
                    }
                }
            }*/
            else if (sqlName == "unit_price")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                if (records != null)
                {
                    #region 判断 最低售价校验  存入当前进价成本价
                    foreach (dynamic rec in records)
                    {
                        foreach (SheetRowBorrowItem row in SheetRows)
                        {
                            //decimal lowPrice = 0;
                            decimal buyPrice = 0, costPriceSpec = 0;
                            //if (row.order_sub_id != "") continue;
                            //if (rec.item_lowest_price != "") lowPrice = CPubVars.ToDecimal(rec.item_lowest_price);
                            if (rec.buy_price != "") buyPrice = CPubVars.ToDecimal(rec.buy_price);
                            if (rec.cost_price_spec != "") costPriceSpec = CPubVars.ToDecimal(rec.cost_price_spec);

                            if ((rec.item_id == row.item_id || rec.item_id == row.son_mum_item) && rec.item_unit_no == row.unit_no)
                            {
                                row.cost_price_buy = (buyPrice / row.unit_factor).ToString();
                                row.cost_price_prop = (costPriceSpec / row.unit_factor).ToString();
                                //if (row.real_price != 0 && !IsFromWeb && row.real_price < lowPrice)  info.ErrMsg = $"{row.item_name}的售价低于最低售价，请重新输入";

                                /*if (!IsFromWeb && red_flag != "2" && money_inout_flag == 1 && row.quantity > 0)
                                {
                                    decimal lowPrice = 0;
                                    if (rec.item_lowest_price != "") lowPrice = CPubVars.ToDecimal(rec.item_lowest_price);
                                    if (rec.item_unit_no == row.unit_no && row.real_price != 0 && row.real_price < lowPrice && row.trade_type != "H")
                                        info.ErrMsg = $"{row.item_name}的售价低于最低售价，请重新输入";
                                }*/
                            }

                            
                        }
                        if (info.ErrMsg != "") return;
                    }
                    #endregion

                    #region 根据单位关系更新各个单位的最近售价
                    {
                        Dictionary<string, string> dicUniqueItems = new Dictionary<string, string>();
                        foreach (SheetRowBorrowItem row in SheetRows)
                        {
                            if ("X,T".IndexOf(row.trade_type) == -1) continue;
                            if (NotRememberPriceBriefs != "" && NotRememberPriceBriefs.Contains("," + row.remark + ",")) continue;
                            if (row.order_sub_id != "") continue;
                            if (row.quantity * row.inout_flag > 0 && red_flag == "") continue;
                            if (row.quantity * row.inout_flag < 0 && red_flag == "2") continue;
                            if (row.real_price == 0) continue;
                            if (row.isSpecialPrice) continue;
                            if (dicUniqueItems.ContainsKey(row.item_id)) continue;
                            dicUniqueItems.Add(row.item_id, row.item_id);
                            foreach (dynamic rec in records)
                            {
                                var itemID = row.son_mum_item != "" && !row.attrRememberPrice ? row.son_mum_item : row.item_id;

                                if (rec.item_id == itemID)
                                {
                                    SheetRowBorrowItem saleRow = new SheetRowBorrowItem();
                                    saleRow.item_id = itemID;
                                    saleRow.unit_no = rec.item_unit_no;
                                    saleRow.sys_price = row.sys_price;
                                    //if (rec.item_unit_no == row.unit_no) saleRow.is_recent = true;
                                    string unit_price_related = rec.unit_price_related;

                                    if (unit_price_related.ToLower() != "false" || rec.item_unit_no == row.unit_no)
                                    {
                                        if (rec.item_unit_factor == "") continue;
                                        else
                                        {
                                            int roundNumber = 2;
                                            if (rec.item_unit_factor == "1")
                                            {
                                                roundNumber = 4;
                                            }

                                            saleRow.real_price = CPubVars.ToDecimal(Math.Round((row.real_price / row.unit_factor * CPubVars.ToDecimal(rec.item_unit_factor)), roundNumber));
                                            decimal orig_price = 0;
                                            decimal recent_retail_price;
                                            if (row.orig_price != null && row.orig_price != "")
                                            {
                                                orig_price = CPubVars.ToDecimal(row.orig_price);
                                                saleRow.orig_price = (Math.Round((orig_price / row.unit_factor * CPubVars.ToDecimal(rec.item_unit_factor)), roundNumber)).ToString();
                                            }
                                            if (row.recent_retail_price != null && row.recent_retail_price != "")
                                            {
                                                recent_retail_price = CPubVars.ToDecimal(row.recent_retail_price);
                                                saleRow.recent_retail_price = (Math.Round((recent_retail_price / row.unit_factor * CPubVars.ToDecimal(rec.item_unit_factor)), roundNumber)).ToString();
                                            }
                                        }
                                        saleRow.quantity = row.quantity;
                                        
                                        info.UnitPriceRows.Add(saleRow);
                                    }
                                }
                            }
                            if (info.ErrMsg != "") return;
                        }
                    }
                    #endregion
                }
            }
            else if (sqlName == "items_ordered") //定货会价格
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                /*
                if (records != null)
                {
                    foreach (SheetRowBorrowItem row in SheetRows)
                    {
                        if (row.order_sub_id == "") continue;
                        foreach (dynamic rec in records)
                        {
                            if (rec.order_price != "")
                            {
                                if (row.item_id == rec.item_id && row.order_sub_id == rec.order_sub_id)
                                {
                                   
                                    if (rec.order_price != "")
                                    {
                                        decimal unit_factor = CPubVars.ToDecimal(rec.unit_factor);
                                        var unitOrderPrice = CPubVars.ToDecimal(rec.order_price) / unit_factor;
                                        decimal rowUnitPrice = row.real_price / row.unit_factor;
                                        if (row.unit_no == rec.unit_no)
                                        { 
                                            if (Math.Abs(rowUnitPrice - unitOrderPrice) < 0.02)//解决误差问题
                                            { 
                                                row.OrderPrice = rec.order_price;
                                                if (rec.quantity != "" && rec.unit_factor != "")
                                                {
                                                    row.OrderedQty = CPubVars.ToDecimal(rec.quantity); //直接比较大单位
                                                }

                                                row.OrderUnitNo = rec.unit_no;
                                                row.OrderUnitFactor = CPubVars.ToDecimal(rec.unit_factor);
                                                if (rec.balance != "") row.OrderedBalance = CPubVars.ToDecimal(rec.balance);
                                            }
                                        }
                                        
                                    }
                                }
                               
                            }
                        }
                    }
                   
              

                } */
                info.SheetRows = SheetRows;
                info.ItemOrderedRows = records;
            }
            else if (sqlName == "sale_order_qty")  // 订单转单 的 销售单，找出原单据的数量，判断可用库存数量 
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowMM row in SheetRows)
                    {
                        string rowBranchId = row.branch_id.IsInvalid() ? branch_id:row.branch_id;
                        if (rec.item_id != "" && row.item_id == rec.item_id && rec.batch_id == row.batch_id && rec.branch_id == rowBranchId && rec.branch_position == row.branch_position)
                        {
                            row.SaleOrderQty = CPubVars.ToDecimal(rec.qty == "" ? 0 : rec.qty);
                        }
                    }
                }
            }
            else if (sqlName == "order_sheet_info")  // 订单转单 的 销售单，找出原单据的数量，判断可用库存数量 
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec != null)
                {
                    if (rec.now_left_amount != "")
                    {
                        info.OrderLeftAmount = CPubVars.ToDecimal(rec.now_left_amount);
                    }
                    //info.OrderSheetIsImported = rec.order_sheet_is_imported == "True";
                    if (!bForRed)
                    {
                        if(rec.order_status=="pzc") info.ErrMsg = "订单已申请装车,请先确认装车";
                    }
                }
            }
            else if (sqlName == "sale_order_sheet")
            {
                //List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                //info.OrderSheetRows = CDbDealer.GetRecordsFromDr<SheetRowBorrowItemOrder>(dr, false);
            }
            else if (sqlName == "new_recent_sale_time")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec != null)
                {
                    //info.new_recent_sale_time = rec.recent_sale_time;
                }
            }
            else if (sqlName == "other_sale_sheet_for_order")
            {
                if (!bForRed)
                {
                    dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                    if (rec != null)
                    {
                        if (sheet_id != rec.sheet_id)
                            info.ErrMsg = "订单已被转单,不能再次转单";
                    }
                }
            }
            else if (sqlName == "borrow")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowBorrowItem row in SheetRows)
                    {
                        var itemID = row.son_mum_item != "" ? row.son_mum_item : row.item_id;
                        if ((row.trade_type == "J" || row.trade_type == "H") && rec.item_id != "" && itemID == rec.item_id)
                        {
                            if (rec.borrowed_qty != "")
                            {
                                row.HasBorrowedQty = true;
                                row.BorrowedQty = CPubVars.ToDecimal(rec.borrowed_qty);
                            }
                        }
                    }
                }
                info.SheetRows = SheetRows;
            }
            else if (sqlName == "display")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowBorrowItem row in SheetRows)
                    {
                        string item_id = row.item_id;
                        if (row.son_mum_item.IsValid()) item_id = row.son_mum_item;
                        if (row.disp_flow_id != null && row.disp_sheet_id != null && row.disp_flow_id != "" && rec.flow_id == row.disp_flow_id && (rec.items_id.IndexOf(item_id) >= 0))
                        {
                            var month_qty = "month" + row.disp_month_id + "_qty";
                            var month_given = "month" + row.disp_month_id + "_given";
                            decimal qty = 0; decimal given = 0;
                            foreach (KeyValuePair<string, object> col in rec)
                            {
                                if (!col.Key.Contains("month" + row.disp_month_id)) continue;
                                if (month_qty == col.Key && !string.IsNullOrWhiteSpace(col.Value.ToString())) qty = CPubVars.ToDecimal(col.Value.ToString());
                                if (month_given == col.Key && !string.IsNullOrWhiteSpace(col.Value.ToString())) given = CPubVars.ToDecimal(col.Value.ToString());
                            }
                            row.disp_month_left = qty - given;
                        }
                    }
                }
                info.SheetRows = SheetRows;
            }
            else if (sqlName == "payway_type")
            {
                info.PaywaysInfo = CDbDealer.GetRecordsFromDr<Subject>(dr, false);
                //List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                var haveOrderAmount = false;
                decimal orderAmount = 0;
                foreach (var row in SheetRows)
                {
                    if (row.trade_type == "DH" && row.order_sub_id!="-1") orderAmount += row.sub_amount;
                }
                if (orderAmount > 0) haveOrderAmount = true;
                var haveOrderPayWay = false;
                foreach (dynamic payway in info.PaywaysInfo)
                {
                    if (payway.is_order != null && payway.is_order.ToLower() == "true") haveOrderPayWay = true;
                }
                if (haveOrderPayWay && !haveOrderAmount) info.ErrMsg = "没有定货会商品无法使用定货会账户支付";
                if (haveOrderAmount && !haveOrderPayWay) info.ErrMsg = "定货会商品需要用定货会账户支付";

            }
            else if (sqlName == "setting")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec != null && rec.setting != null)
                {
                    string s = rec.setting;
                    if (s != null)
                    {
                        info.CompanySetting = JsonConvert.DeserializeObject(rec.setting);
                        // fix: 订单转销售单必须指定送货员和权限挂钩， 夏京京
                        if (info.CompanySetting.saleSheetNeedSender == "True")
                        {
                            if (order_sheet_id.IsValid() && senders_id.IsInvalid()) info.ErrMsg = "订单转销售单必须指定送货员";
                        }
                    }

                }
            }
            else if (sqlName == "move_to_van")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec != null && rec.op_id != null)
                {
                    info.MoveToVanOpId = rec.op_id;
                }
            }
            else if (sqlName == "sheet_status_order")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec != null && rec.sheet_print_count != null)
                {
                    info.SheetPrintCount = rec.sheet_print_count;
                    string backStatus = rec.back_branch_status;
                    if (backStatus.IsValid()&&red_flag =="2") info.ErrMsg = "单据已回库,无法红冲";
                }
            }
           /* else if (sqlName == "attribute_items")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                if (records.Count > 0)
                {
                    foreach (dynamic rec in records)
                    {
                        foreach (SheetRowBorrowItem row in SheetRows)
                        {
                            if (rec.item_id == row.item_id)
                            {
                                if (row.item_name == "") row.item_name = rec.item_name;
                                row.son_mum_item = rec.son_mum_item;
                                if (((string)rec.remember_price).ToLower() == "true")
                                {
                                    row.attrRememberPrice = true;
                                }
                            }
                        }
                    }
                }
                info.SheetRows = SheetRows;
            }*/
            else if(sqlName == "senders_id")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec != null)
                {
                    senders_id = rec.senders_id;
                }
            }

            ReadQQDataForWeChatInfo(sqlName, dr, info);

        }
        public async Task<string> SaveAsBuySheet(CMySbCommand cmd, dynamic oriSheet)
        {
            string err = "";
            string sql = "";
            // 送货员
            oriSheet.senders_id = "";
            oriSheet.senders_name = "";
            var sheetType = oriSheet.SheetType;
            SheetBuy sheet = JsonConvert.DeserializeObject<SheetBuy>(JsonConvert.SerializeObject(oriSheet));
            // SheetBuyOrder sheet = new SheetBuyOrder();
            // sheet.SheetRows = oriSheet.SheetRows;
            // TODO 替换为client_id对应的company_id
            string operKey = sheet.OperKey;
            string operId = "";
            Security.GetInfoFromOperKey(operKey, out string supplierCompanyID);
            sheet.Init();
            sheet.SYNCHRONIZE_SHEETS = true;
            sheet.order_source = "2fx";
            sheet.make_brief = "分销自动同步单据";
            sheet.money_inout_flag = sheet.money_inout_flag * -1;
            // Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
            // sheetAttribute.Add("synchronize_sheet", "true");
            // sheet.sheet_attribute = JsonConvert.SerializeObject(sheetAttribute);
            sheet.OperID = "1";
            string rsCompanySql = $"select company_id,oper_id from info_operator where rs_client_id = {supcust_id}";//查找对应的业务员
            dynamic rsCompanyInfo = await CDbDealer.Get1RecordFromSQLAsync(rsCompanySql, cmd);
            string resellerInfoSql = "";
            if (rsCompanyInfo != null)//找到了对应业务员，说明这是一个按业务员对应客户的分销商
            {
                resellerInfoSql = @$"select * from rs_seller  rss 
                left join (select plan_id,brand_id,client_mapper,auto_approve_reseller_sheet from rs_plan) rsp on rss.plan_id = rsp.plan_id 
                where rss.company_id={supplierCompanyID} and rss.reseller_company_id = {rsCompanyInfo.company_id}";
                operId = rsCompanyInfo.oper_id;
            }
            else
            {
                resellerInfoSql = @$"select * from rs_seller  rss 
                left join (select plan_id,brand_id,client_mapper,auto_approve_reseller_sheet from rs_plan) rsp on rss.plan_id = rsp.plan_id 
                where company_id={supplierCompanyID} and rss.client_id = {oriSheet.supcust_id}";

            }

            dynamic resellerInfo = await CDbDealer.Get1RecordFromSQLAsync(resellerInfoSql, cmd);
            // TODO 客户 此处为厂家 supplier

            sheet.company_id = (string)resellerInfo.reseller_company_id;
            sheet.branch_id = "0";
            if ((string)resellerInfo.client_mapper == "sellerAsClient")
            {
                // string sellerClientSql = $"select rs_seller_id from info_supcust where oper_id = {clientId}";
                // dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sellerClientSql, cmd);
                sheet.seller_id = operId;
                sql = @$"select br.branch_id,ib.branch_name from info_operator io 
left join oper_branch_rights br on io.oper_id=br.oper_id and br.company_id={sheet.company_id}
left join info_branch ib on br.branch_id=ib.branch_id and ib.company_id={sheet.company_id}
where io.company_id={sheet.company_id} and io.oper_id={sheet.seller_id} and (not coalesce(io.restrict_branches,false) or br.sheet_cg='True')
AND br.branch_id IS NOT NULL  limit 2";
                var branches = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (branches.Count == 1)
                {
                    sheet.branch_id = ((dynamic)branches[0]).branch_id;
                    sheet.branch_name = ((dynamic)branches[0]).branch_name;
                }
            }
            else
            {
                sql = @$"select branch_id,branch_name,branch_type from info_branch ib
where ib.company_id={sheet.company_id} order by branch_type limit 2";
                var branches = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                int storeCount = 0;
                foreach(dynamic branch in branches)
                {
                    if (branch.branch_type == "store")
                    {
                        storeCount++;
                    }
                }
                if (branches.Count == 1 || storeCount==1)
                {
                    sheet.branch_id = ((dynamic)branches[0]).branch_id;
                    sheet.branch_name = ((dynamic)branches[0]).branch_name;
                }
            }

            sheet.supcust_id = (string)resellerInfo.supplier_id;
            // sheet.payway1_id = "0";
            // sheet.payway1_name = "";
            sheet.payway2_id = "";
            sheet.payway2_name = "";
            sheet.payway3_id = "";
            sheet.payway3_name = "";
            async Task<dynamic> getSonPayway(string payway_id,string payway_name)
            {
                if(payway_id=="") return new { sub_id = "", sub_name = "" };
                dynamic metSub = null;
                int metCount = 0;
                sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={supplierCompanyID} and sub_id={payway_id}";
                dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (rec != null)
                {
                    if (rec.sub_type == "YS")
                    {
                        sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={sheet.company_id} and sub_type ='YF'";
                        var prepaySubs = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);


                        foreach (dynamic sonSub in prepaySubs)
                        {
                            string subName = rec.sub_name;
                            string sonSubName = sonSub.sub_name;
                            // 要求主账号和分校账号设置一对命名相同的预收预付款如主账号：XX预收款 分销账号：XX预付款
                            if (subName.Replace("预收款", "").Replace("预收", "") == sonSubName.Replace("预付款", "").Replace("预付", ""))
                            // if (subName.Contains("预收") && sonSubName.Contains("预付"))
                            {
                                metSub = sonSub;
                                metCount++;
                            }
                        }
                        if (metCount != 1)
                        {
                            metSub = null;
                        }
                    }
                    else if (((string)rec.sub_name).Contains("现金"))
                    {
                        sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={sheet.company_id} and sub_name like '%现金%'";
                        metSub = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                         
                    }
                    else if (((string)rec.sub_name).Contains("微信支付"))
                    {
                        sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={sheet.company_id} and sub_name like '%微信支付%'";
                        metSub = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

                    }
                    else if (((string)rec.sub_name).Contains("支付宝"))
                    {
                        sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={sheet.company_id} and sub_name like '%支付宝%'";
                        metSub = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

                    }
                    else
                    {
                        sql = $@"select sub_id,sub_name,sub_type from cw_subject where company_id={sheet.company_id} and sub_name like '%{rec.sub_name}%'";
                        metSub = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    }
                }
                if (metSub == null) metSub = new { sub_id = "", sub_name = "" };
                return metSub;
            }

           
            dynamic payway=await getSonPayway(sheet.payway1_id, sheet.payway1_name);
            sheet.payway1_id = payway.sub_id; sheet.payway1_name = payway.sub_name;

            payway = await getSonPayway(sheet.payway2_id, sheet.payway2_name);
            sheet.payway2_id = payway.sub_id; sheet.payway2_name = payway.sub_name;

            payway = await getSonPayway(sheet.payway3_id, sheet.payway3_name);
            sheet.payway3_id = payway.sub_id; sheet.payway3_name = payway.sub_name;
             
            sheet.sheet_id = "";
            sheet.sheet_no = "";
            sheet.sheet_type = SHEET_TYPE.SHEET_BUY;
            if (sheetType == "T")
            {
                sheet.SheetType = "CT";
                sheet.sheet_type = SHEET_TYPE.SHEET_BUY_RETURN;
            }
            sheet.maker_id = "1";
            sheet.maker_name = "";
            sheet.approver_id = "";
            sheet.approve_time = "";
            sheet.approve_brief = "";

            // TODO delete

            foreach (SheetRowBuy row in sheet.SheetRows)
            {
                string itemId = row.item_id;
                string querySql = $"select item_id,item_class,other_class from info_item_prop where rs_mum_id = {itemId} and company_id ={sheet.company_id}";
                dynamic itemInfo = await CDbDealer.Get1RecordFromSQLAsync(querySql, cmd);

                dynamic fatherBrand = await CDbDealer.Get1RecordFromSQLAsync($"select item_brand from info_item_prop where item_id = {itemId}", cmd);
                // 查询品牌是否在分销方案里，确保方案修改之后，不包括该品牌商品无法开同步单据
                // 不包括的商品之后是否支持分销商自己开单？
                string planBrandSql = $"select * from rs_plan where plan_id = {resellerInfo.plan_id} AND ',' || brand_id || ',' LIKE '%,{fatherBrand.item_brand},%';";
                dynamic planBrandRet = await CDbDealer.Get1RecordFromSQLAsync(planBrandSql, cmd);
                if (planBrandRet == null)
                {
                    err = $"商品“{row.item_name}”品牌不在分销方案内";
                }

                if (itemInfo == null)
                {
                    err = $@"子公司档案没有商品:{row.item_name}";
                    break;
                }
                row.item_id = itemInfo.item_id;
                row.classId = itemInfo.item_class;
                row.other_class = itemInfo.other_class;
                // 自动审单时如果不转化inout_flag直接审核会导致库存信息错误
                row.inout_flag = row.inout_flag * (-1);

            }

            if (err == "")
            {
                if (((string)resellerInfo.auto_approve_reseller_sheet).ToLower() == "true")
                {
                    err = await sheet.SaveAndApprove(cmd, false);
                    if (err != "")
                    {
                        err = "审核分销商单据失败:" + err;
                    }
                }
                else
                { 
                    err = await sheet.Save(cmd, false);
                    if (err != "")
                    {
                        err = "保存分销商单据失败:" + err;
                    }
                }

                if (err != "") { return err; }
                var ret = new { bindSheetId = sheet.sheet_id, bindSheetNo = sheet.sheet_no, bindSheetType = sheet.SheetType, companyId =sheet.company_id };
                
                return JsonConvert.SerializeObject(ret);
               
                
            }
                
            return err;
        }
 
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            if (seller_id == "" && IsFromWeb && order_source=="") return "必须指定业务员";
            //if (order_sheet_id.IsValid() && senders_id.IsInvalid()) return "订单转销售单必须指定送货员";
            if (Math.Abs(total_amount - now_disc_amount - now_pay_amount) < 0.05m)
            {
                if (this.happen_time.IsValid())
                    settle_time = this.happen_time;
                else
                    settle_time = CPubVars.GetDateText(DateTime.Now);

            }
            else settle_time = "";

            if(!receive_addr.IsValid() && receive_addr_desc.IsValid())
			{
                cmd.CommandText = $"select addr_id from info_client_address where company_id={company_id} and addr_desc='{receive_addr_desc}'";
                object ov = await cmd.ExecuteScalarAsync();
                if(ov!=null && ov != DBNull.Value)
				{
                    receive_addr = ov.ToString();
				}
                else
				{
                    cmd.CommandText = $"insert into info_client_address (company_id,addr_desc) values ({company_id},'{receive_addr_desc}') returning addr_id";
                    ov = await cmd.ExecuteScalarAsync();
                    receive_addr = ov.ToString();
				}
			}

			return await base.CheckSheetValid(cmd);
        }
        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            if (seller_id == "" && IsFromWeb && order_source == "") return "必须指定业务员";

            if (!receive_addr.IsValid() && receive_addr_desc.IsValid())
            {
                cmd.CommandText = $"select addr_id from info_client_address where company_id={company_id} and client_id={supcust_id} and addr_desc='{receive_addr_desc}'";
                object ov = await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value)
                {
                    receive_addr = ov.ToString();
                }
                else
                {
                    cmd.CommandText = $"update info_client_address set addr_order=addr_order+1 where company_id={company_id} and client_id={supcust_id};insert into info_client_address (company_id,client_id,addr_desc,addr_order) values ({company_id},{supcust_id},'{receive_addr_desc}',1) returning addr_id";
                    ov = await cmd.ExecuteScalarAsync();
                    receive_addr = ov.ToString();
                }
            }
			if (!string.IsNullOrEmpty(order_sheet_id))
            {
                if (await SaleSheetService.CheckOrderBindRelation(cmd, order_sheet_id, sheet_id))
                {
                    return "该销售订单已转过单,不能再次转";
                }
            }
          
            // 2024.03.30
            // 何刚/绵阳友乐出现的问题，如果送货员以逗号结尾，会导致销售汇总查询时SQL报错
            if (senders_id.EndsWith(','))
            {
                senders_id = senders_id.TrimEnd(',');
            }
            if (senders_name.EndsWith(','))
            {
                senders_name = senders_name.TrimEnd(',');
            }

            if(senders_name!="" && senders_id == "")
            {
                return "送货员指定了名称但是未指定ID";
            }
            
            return await base.CheckSaveSheetValid(cmd);
        }

        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            CInfoForApproveSale info = (CInfoForApproveSale)info1;
          
            if (FIXING_ARREARS) return ""; 

            string sql = "";
            if (!IsImported)
            {
                sql = base.GetApproveSQL(info1);
            }
             

            #region update borrowed items
            var mergedTradeRows = MergeTradeSheetRows(info.SheetRows);
            string borrowSql = "";
            foreach (var row in mergedTradeRows)
            {
                decimal borrowChangeQty = 0;
                var itemID = row.son_mum_item!="" ? row.son_mum_item : row.item_id;
                borrowChangeQty = CPubVars.ToDecimal(row.quantity);
                if (row.trade_type == "J" || row.trade_type == "H")
                {
                    row.StockQty += borrowChangeQty;
                    if(red_flag != "2" && row.trade_type=="H"&&row.BorrowedQty<=0) info.ErrMsg = $"{row.item_name}使用的数量大于可还货库存";
                    if (red_flag == "2" && row.BorrowedQty <0) info.ErrMsg = $"{row.item_name}的剩余数量与当时借货数量不一致,红冲失败";
                    //if (row.HasBorrowedQty) borrowSql += $"update borrowed_cust_items set borrowed_qty = borrowed_qty+({borrowChangeQty}) where company_id={company_id} and cust_id = {supcust_id} and item_id={itemID};";
                    //else 
                        borrowSql += @$"insert into borrowed_cust_items(company_id,cust_id,item_id,borrowed_qty) values({company_id},{supcust_id},{itemID},{borrowChangeQty})
                                         on conflict(company_id,cust_id,item_id) do update set borrowed_qty = borrowed_cust_items.borrowed_qty + {borrowChangeQty};";
                }
            }
            sql += borrowSql;
            #endregion

            if (IsImported) return sql;

            #region update recent produce date
            string sqlUpdateRecentProduceDate = "";
            string sNow = CPubVars.GetDateText(DateTime.Now);
            foreach (var row in info.SheetRows)
            {
                //if (!string.IsNullOrEmpty(row.virtual_produce_date))
                if (row.virtual_produce_date != null)
                {
                    sqlUpdateRecentProduceDate += $"insert into item_recent_produce_date (company_id,item_id,produce_date,happen_time) values ({company_id},{row.item_id},'{row.virtual_produce_date}','{sNow}') on conflict(company_id,item_id) do update set produce_date='{row.virtual_produce_date}',happen_time='{sNow}';";
                }
            }
            sql += sqlUpdateRecentProduceDate;
            #endregion


            return sql;
        }


        //商品质疑表新增记录
        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            await base.OnSheetIDGot(cmd, sheetID, info1);
            if (FIXING_ARREARS) return;
            if (IsImported) return;

            CInfoForApproveSale info = (CInfoForApproveSale)info1;

            string dealing_happen_time = happen_time;
            if (red_flag == "2")
            {
                dynamic data = await CDbDealer.Get1RecordFromSQLAsync($"select happen_time from sheet_sale_main where company_id = {company_id} and sheet_id = {red_sheet_id}", cmd);
                if (data != null) dealing_happen_time = data.happen_time;
            }

            string origItemsID = "";
            //string son_items_id = "";
            List<SheetRowCostPrice> costPriceRows = new List<SheetRowCostPrice>();
            string sql = "";


            foreach (SheetRowBorrowItem row in SheetRows)
            {
                if (origItemsID != "") origItemsID += ",";
                origItemsID += row.item_id;
                //  items_id += row.son_mum_item.IsValid() ? row.son_mum_item : row.item_id;
               
                SheetRowCostPrice costRow = new SheetRowCostPrice();
                costRow.item_id = row.item_id;
                costRow.unit_no = row.unit_no;
                costRow.inout_flag = row.inout_flag;
                costRow.quantity = row.quantity * row.unit_factor;
                costRow.sub_amount = row.sub_amount;
                costRow.cost_price_avg = row.cost_price_avg.IsValid() ? CPubVars.ToDecimal(row.cost_price_avg) : 0;
                costRow.item_cost_price_suspect = row.item_cost_price_suspect;
                costRow.old_total_qty = row.old_total_qty;
                costPriceRows.Add(costRow);
                /*
                float cost_price_recent = 0;

                if (recentPriceTime == "1") cost_price_recent = row.cost_price_recent1;
                else if (recentPriceTime == "2") cost_price_recent = row.cost_price_recent2;
                else if (recentPriceTime == "3") cost_price_recent = row.cost_price_recent3;

                sql = @$"update sheet_sale_detail set cost_price_recent = {cost_price_recent}  where company_id = {company_id} and item_id = {row.item_id} and sheet_id = {sheet_id};";
                cmd.CommandText = sql;
                await cmd.ExecuteScalarAsync();*/
            }
            if (origItemsID != "")
            {
                //await UpdateCostPriceAvg(cmd, info1, dealing_happen_time);
                await UpdateCostPriceAvg(cmd, costPriceRows, dealing_happen_time);

            }

            ClearExecSQL();

            #region 存欠款单

            if (Math.Abs(this.left_amount) > 0 && !red_flag.IsValid() && sheet_type == SHEET_TYPE.SHEET_SALE)
            {
                string arrears_status = "no";
                if (Math.Abs(this.left_amount) < Math.Abs(this.total_amount)) arrears_status = "part";
                string insertArrearBillSql = $@"insert into arrears_bill 
    (company_id,business_sheet_type,business_sheet_id,business_sheet_no,supcust_id,supcust_name,orig_amount,left_amount,keeper_id,out_company,arrears_status)
values ({company_id},'{this.SheetType}',{this.sheet_id},'{this.sheet_no}',{this.supcust_id},'{this.sup_name}',{this.total_amount},{this.left_amount},{this.seller_id},true,'{arrears_status}')
returning bill_id";
                dynamic insertRet = await CDbDealer.Get1RecordFromSQLAsync(insertArrearBillSql, cmd);

            }
            else if (this.left_amount > 0 && red_flag.IsValid())
            {
                string billSql = @$"delete from arrears_bill where company_id = {company_id} and business_sheet_type = '{this.SheetType}'
            and business_sheet_id = {this.sheet_id} and business_sheet_no = '{this.sheet_no}' and supcust_id = {this.supcust_id}";
                cmd.CommandText = billSql;
                await cmd.ExecuteNonQueryAsync();
            }
            #endregion
//            #region 修改订单接收状态
//            if (order_sheet_id != "")
//            {
//                // string now = CPubVars.GetDateText(DateTime.Now);
//                if (red_flag == "2")
//                {
//                    var status = "xd";
//                    if (info.SheetPrintCount.IsValid() && info.SheetPrintCount != "0") status = "dd";
//                    if (info.CompanySetting != null && info.CompanySetting.flowExistMove != null)
//                    {
//                        bool flowExistMove = Convert.ToBoolean(info.CompanySetting.flowExistMove.ToString().ToLower());
//                        if (flowExistMove && info.MoveToVanOpId != "") status = "zc";
//                    }
//                    AddExecSQL($"update sheet_status_order set receipt_status = null, has_return = null, order_status = '{status}',sale_sheet_id=null where company_id = '{company_id}' and sheet_id = '{order_sheet_id}';");
//                }
//                //else if (info.OrderSheetRows != null)
//                //{
//                //    if (sheet_type == SHEET_TYPE.SHEET_SALE)
//                //    {
//                //        var hasReturn = false;
//                //        var receiptStatus = "zd";//整单签收
//                //        var orderStatus = "zd";//已转单
//                //        List<SheetRowBorrowItem> saleSheetRows = new List<SheetRowBorrowItem>();
//                //        List<SheetRowBorrowItem> saleSheetRows_return = new List<SheetRowBorrowItem>();
//                //        foreach (var row in SheetRows)
//                //        {
//                //            if (row.quantity > 0) saleSheetRows.Add(row);
//                //            if (row.quantity < 0) saleSheetRows_return.Add(row);
//                //        }

//                //        if (saleSheetRows.Count == 0)
//                //        {
//                //            if (saleSheetRows_return.Count == 0)
//                //                receiptStatus = "js";
//                //            else
//                //            {
//                //                hasReturn = true;
//                //                var mergedSheetRows_return = MergeSheetRows_return(saleSheetRows_return);
//                //                SheetSaleOrder orderSheet = new SheetSaleOrder();
//                //                orderSheet.SheetRows = info.OrderSheetRows;
//                //                var mergedOrderSheetRows_return = orderSheet.MergeSheetRows_return(info.OrderSheetRows);
//                //                foreach (var orderGood in mergedOrderSheetRows_return)
//                //                {
//                //                    var saleGood = mergedSheetRows_return.SingleOrDefault(a => a.item_id == orderGood.item_id);
//                //                    if (saleGood == null || orderGood.quantity < saleGood.quantity)
//                //                    {
//                //                        receiptStatus = "bf";
//                //                        break;
//                //                    }
//                //                }
//                //            }

//                //        }
//                //        else
//                //        {
//                //            var mergedSheetRows = MergeSheetRows(saleSheetRows, true);
//                //            SheetSaleOrder orderSheet = new SheetSaleOrder();
//                //            orderSheet.SheetRows = info.OrderSheetRows;
//                //            var mergedOrderSheetRows = orderSheet.MergeSheetRows(info.OrderSheetRows, true);

//                //            foreach (var saleOrderGood in mergedOrderSheetRows)
//                //            {
//                //                var saleGood = mergedSheetRows.SingleOrDefault(a => a.item_id == saleOrderGood.item_id);
//                //                if (saleGood == null || saleOrderGood.quantity > saleGood.quantity)
//                //                {
//                //                    receiptStatus = "bf";
//                //                    break;
//                //                }
//                //            }

//                //            foreach (var row in SheetRows)
//                //            {

//                //                if (row.quantity < 0)
//                //                {
//                //                    hasReturn = true;
//                //                    break;
//                //                }
//                //            }
//                //        }




//                        AddExecSQL(@$"
//insert into sheet_status_order
//          (company_id,    sheet_id,         sale_sheet_id, order_status,   receipt_status,      has_return, sheet_print_count, sum_print_count, open_stock_print_count) 
//values ('{company_id}', '{order_sheet_id}','{sheet_id}', '{orderStatus}', '{receiptStatus}',   '{hasReturn}', '0',              '0',             '0')
//on conflict(sheet_id) do update set order_status = '{orderStatus}', receipt_status = '{receiptStatus}', has_return = '{hasReturn}',sale_sheet_id={sheet_id};"
//);
//                    }
//                    else if (sheet_type == SHEET_TYPE.SHEET_SALE_RETURN)
//                    {
//                        string receiptStatus = "zd";
//                        var orderStatus = "zd";
//                        List<SheetRowBorrowItem> saleSheetRows = new List<SheetRowBorrowItem>();
//                        foreach (var row in SheetRows)
//                        {
//                            if (row.quantity > 0) saleSheetRows.Add(row);
//                        }

//                        if (saleSheetRows.Count == 0)
//                        {
//                            receiptStatus = "js";
//                        }
//                        else
//                        {
//                            var mergedSheetRows = MergeSheetRows(saleSheetRows, true);
//                            SheetSaleOrder orderSheet = new SheetSaleOrder();
//                            orderSheet.SheetRows = info.OrderSheetRows;
//                            var mergedOrderSheetRows = orderSheet.MergeSheetRows(info.OrderSheetRows, true);

//                            foreach (var saleOrderGood in mergedOrderSheetRows)
//                            {
//                                var saleGood = mergedSheetRows.SingleOrDefault(a => a.item_id == saleOrderGood.item_id);
//                                if (saleGood == null || saleOrderGood.quantity > saleGood.quantity)
//                                {
//                                    receiptStatus = "bf";
//                                    break;
//                                }
//                            }
//                        }
//                        AddExecSQL(@$"
//insert into sheet_status_order 
//       (company_id,      sheet_id,         sale_sheet_id,  order_status,    receipt_status,  has_return, sheet_print_count, sum_print_count, open_stock_print_count)
//values ('{company_id}', '{order_sheet_id}','{sheet_id}', '{orderStatus}', '{receiptStatus}', '{true}',  '0',                 '0',             '0') 
//on conflict(sheet_id) do update set order_status = '{orderStatus}', receipt_status = '{receiptStatus}',has_return = '{true}', sale_sheet_id={sheet_id};");
//                    }
//                }

//            }
            //#endregion

            #region reduce item ordered items

            var mergedOrderedRows = MergeOrderedSheetRows(info.SheetRows);

            sql = "";
            
            foreach (var row in mergedOrderedRows)
            {
                var errMsg = "";
                decimal qty = row.quantity * money_inout_flag;

                decimal leftQty = Math.Abs(qty);
                decimal flag = leftQty / qty;
                decimal availOrderQty_s = 0;
                var noMatch = true;

                /*
                foreach (dynamic rec in info.ItemOrderedRows)
                {
                     
                    //if ((row.item_id == rec.item_id || row.son_mum_item == rec.item_id) && row.order_sub_id == rec.order_sub_id && rec.flow_id == row.order_flow_id)
                    if ((row.item_id == rec.item_id || row.son_mum_item == rec.item_id) && row.order_sub_id == rec.order_sub_id)

                    {
                        noMatch = false;
                        decimal orderedUnitFactor = CPubVars.ToDecimal(rec.unit_factor);
                        decimal orderedPrice = CPubVars.ToDecimal(rec.order_price);
                        decimal orderedQty = Decimal.Parse(rec.quantity.ToString(), System.Globalization.NumberStyles.Float);//科学计数法转为正确的数字，再转成decimal类型

                        var unitOrderPrice = orderedPrice / orderedUnitFactor;
                         
                        bool bMet = false;
                        if (rec.flow_id == row.order_flow_id) bMet = true;
                        if (red_flag == "2"|| row.order_flow_id.IsInvalid()||!bMet)
                        {
                            if (Math.Abs(row.real_price - unitOrderPrice) < 0.001m)
                            {
                                bMet = true;
                            }
                        }
                       
                        if (bMet)
                        {
                            //row.OrderPrice = rec.order_price;
                            decimal orderQty_s = orderedUnitFactor * orderedQty;
                            decimal reduceQty_s = 0;
                            decimal changeQty = 0;
                            if (flag == 1)
                            {
                                if (orderQty_s > leftQty)
                                    reduceQty_s = leftQty;
                                else
                                    reduceQty_s = orderQty_s;

                                leftQty -= reduceQty_s;

                                changeQty = -reduceQty_s / orderedUnitFactor;

                                availOrderQty_s += reduceQty_s;
                            }
                            else
                            {
                                changeQty = leftQty / orderedUnitFactor;
                            }

                            //decimal newQty = Math.Round(orderedQty + changeQty);// CPubVars.ToDecimal(CPubVars.FormatMoney(orderedQty + changeQty,4));

                            //sql += $@"update items_ordered_balance set quantity=round(quantity+({changeQty}),5) where company_id = {company_id} and supcust_id={supcust_id} and item_id = {rec.item_id} and unit_factor = '{orderedUnitFactor}' and prepay_sub_id = {row.order_sub_id} and order_price = '{rec.order_price}' ;";//order_price=后面的单引号不能去掉，否则有小数点时检索不到
                            // sql += $@"update items_ordered_balance set quantity=round(quantity+({changeQty}),5) where company_id = {company_id} and supcust_id={supcust_id} and item_id = {rec.item_id} and unit_factor = '{orderedUnitFactor}' and prepay_sub_id = {row.order_sub_id} and flow_id = '{rec.flow_id}' ;";
                            string setSheetInfo = "";
                            if (Math.Abs(orderedQty + changeQty) < 0.02m)
                            {
                                setSheetInfo = ",order_item_sheets_id='',order_item_sheets_no=''";
                                
                            }
                            else
                            {
								if (red_flag == "2")
								{
									if (row.order_item_sheets_id!="")
									{
                                       setSheetInfo = $",order_item_sheets_id='{row.order_item_sheets_id}',order_item_sheets_no='{row.order_item_sheets_no}'";
                                        
                                    }
                                }
                            }
                            sql += $@"update items_ordered_balance set quantity=round(quantity+({changeQty}),5) {setSheetInfo} where company_id = {company_id} and supcust_id={supcust_id} and item_id = {rec.item_id} and flow_id = '{rec.flow_id}' ;";
                           
                            if (leftQty < 0.01m || flag == -1) break;
                        }
                    }
                }
                */

                foreach (dynamic rec in info.ItemOrderedRows)
                {
                    if ((row.item_id == rec.item_id || row.son_mum_item == rec.item_id) && row.order_sub_id == rec.order_sub_id && rec.flow_id == row.order_flow_id)
                    {
                        noMatch = false;
                        if (Math.Abs(row.real_price - (CPubVars.ToDecimal(rec.order_price) / CPubVars.ToDecimal(rec.unit_factor))) > 0.1m)
                        {
                            errMsg += $"定货价格为{row.real_price}/{row.unit_no}的商品{row.item_name}匹配的order_flow_id{row.order_flow_id}不对\n";
                            break;
                        }
                        sql += updateOrderItem(ref leftQty, ref flag, ref availOrderQty_s, row, rec);
                        if (leftQty < 0.01m || flag == -1) break;
                    }

                }

                if (noMatch)
                {
                    foreach (dynamic rec in info.ItemOrderedRows)
                    {
                        if ((row.item_id == rec.item_id || row.son_mum_item == rec.item_id) && row.order_sub_id == rec.order_sub_id && Math.Abs(row.real_price - (CPubVars.ToDecimal(rec.order_price) / CPubVars.ToDecimal(rec.unit_factor))) < 0.01m)
                        {
                            noMatch = false;
                            sql += updateOrderItem(ref leftQty, ref flag, ref availOrderQty_s, row, rec);
                            if (leftQty < 0.01m || flag == -1) break;
                        }
                    }
                }
                if (noMatch)
                {
                    info.ErrMsg = $"{row.item_name}的订货记录发生变动,请重新选择定货会商品";
                }
                if (flag == 1)
                {
                    string rowQtyUnit = GetUnitQty(row.quantity, row.b_unit_no, row.m_unit_no, row.s_unit_no, row.b_unit_factor, row.m_unit_factor);
                    string orderedQtyUnit = GetUnitQty(availOrderQty_s, row.b_unit_no, row.m_unit_no, row.s_unit_no, row.b_unit_factor, row.m_unit_factor);

                    if (leftQty > 0.01m) info.ErrMsg = $"{row.item_name}使用的数量{rowQtyUnit}({row.quantity}{row.s_unit_no})大于可还定货数量{orderedQtyUnit}({availOrderQty_s}{row.s_unit_no})";
                }
                if (errMsg != "") info.ErrMsg += errMsg;
            }
            string updateOrderItem(ref decimal leftQty, ref decimal flag, ref decimal availOrderQty_s, SheetRowBorrowItem row, dynamic rec)
            {
                string updateSql = "";
                decimal orderedUnitFactor = CPubVars.ToDecimal(rec.unit_factor);
                decimal orderedPrice = CPubVars.ToDecimal(rec.order_price);
                decimal orderedQty = Decimal.Parse(rec.quantity.ToString(), System.Globalization.NumberStyles.Float);//科学计数法转为正确的数字，再转成decimal类型

                var unitOrderPrice = orderedPrice / orderedUnitFactor;

                decimal orderQty_s = orderedUnitFactor * orderedQty;
                decimal reduceQty_s = 0;
                decimal changeQty = 0;

                if (flag == 1)
                {
                    if (orderQty_s > leftQty)
                        reduceQty_s = leftQty;
                    else
                        reduceQty_s = orderQty_s;

                    leftQty -= reduceQty_s;

                    changeQty = -reduceQty_s / orderedUnitFactor;

                    availOrderQty_s += reduceQty_s;
                }
                else
                {
                    changeQty = leftQty / orderedUnitFactor;
                }
                //decimal newQty = Math.Round(orderedQty + changeQty);// CPubVars.ToDecimal(CPubVars.FormatMoney(orderedQty + changeQty,4));

                //sql += $@"update items_ordered_balance set quantity=round(quantity+({changeQty}),5) where company_id = {company_id} and supcust_id={supcust_id} and item_id = {rec.item_id} and unit_factor = '{orderedUnitFactor}' and prepay_sub_id = {row.order_sub_id} and order_price = '{rec.order_price}' ;";//order_price=后面的单引号不能去掉，否则有小数点时检索不到
                // sql += $@"update items_ordered_balance set quantity=round(quantity+({changeQty}),5) where company_id = {company_id} and supcust_id={supcust_id} and item_id = {rec.item_id} and unit_factor = '{orderedUnitFactor}' and prepay_sub_id = {row.order_sub_id} and flow_id = '{rec.flow_id}' ;";
                string setSheetInfo = "";
                if (Math.Abs(orderedQty + changeQty) < 0.02m)
                {
                    setSheetInfo = ",order_item_sheets_id='',order_item_sheets_no=''";

                }
                else
                {
                    if (red_flag == "2")
                    {
                        if (row.order_item_sheets_id != "")
                        {
                            setSheetInfo = $",order_item_sheets_id='{row.order_item_sheets_id}',order_item_sheets_no='{row.order_item_sheets_no}'";

                        }
                    }
                }
                updateSql += $@"update items_ordered_balance set quantity=round(quantity+({changeQty}),5) {setSheetInfo} where company_id = {company_id} and supcust_id={supcust_id} and item_id = {rec.item_id} and flow_id = '{rec.flow_id}' ;";


                return updateSql;
            }
            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
            #endregion

            #region 陈列协议兑付
            if (info.PaywaysInfo != null && info.PaywaysInfo.Count > 0)
            {
                string zcSubIDs = "";
                foreach (var sub in info.PaywaysInfo)
                {
                    if (sub.sub_type == "ZC")
                    {
                        if (zcSubIDs != "") zcSubIDs += ",";
                        zcSubIDs += sub.sub_id;
                    }
                }
                if (zcSubIDs != "")
                {
                    string months = "";
                    string monthCondi = "";
                    string allGivenCondi = " and all_given is not true";
                    for (int i = 1; i <= 12; i++)
                    {
                        months += $",coalesce(month{i}_qty,0) as month{i}_qty,coalesce(month{i}_given,0) as month{i}_given";
                        if (monthCondi != "") monthCondi += " or ";
                        monthCondi += $" coalesce(month{i}_qty,0) - coalesce(month{i}_given,0) >0.01 ";
                    }
                    monthCondi = "(" + monthCondi + ")";
                    if (money_inout_flag == -1)
                    {
                        allGivenCondi = " ";
                        monthCondi = monthCondi.Replace("0.01", "-0.01");
                    }
                    string sqlQuery = @$"select d.sheet_id,start_time,end_time, fee_sub_id, flow_id {months} from display_agreement_detail d left join display_agreement_main m on m.sheet_id = d.sheet_id   where d.company_id = {company_id} and red_flag is null and approve_time is not null and supcust_id = {supcust_id} and items_id='money' and m.fee_sub_id in ({zcSubIDs}) and {monthCondi} {allGivenCondi};";

                    List<ExpandoObject> lstFlow = await CDbDealer.GetRecordsFromSQLAsync(sqlQuery, cmd);

                    for (int i = 1; i <= 3; i++)
                    {
                        string payway_id = ""; decimal payway_amount = 0;
                        if (i == 1) { payway_id = payway1_id; payway_amount = payway1_amount; }
                        else if (i == 2) { payway_id = payway2_id; payway_amount = payway2_amount; }
                        else if (i == 3) { payway_id = payway3_id; payway_amount = payway3_amount; }
                        if (payway_amount < 0)
                        {
                            info1.ErrMsg = "当支付金额小于零，不能用支出账户支付";
                            break;
                        }
                        decimal leftAmt1 = payway_amount;
                        if (money_inout_flag == 1)
                        {
                            foreach (dynamic flow in lstFlow)
                            {
                                decimal stillLeftAmt = 0;
                                string flowFields = "";
                                JObject jFlow = JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(flow));
                                for (int m = 1; m <= 12; m++)
                                {
                                    string sQty = jFlow[$"month{m}_qty"].ToString();
                                    decimal qty = CPubVars.ToDecimal(sQty);
                                    string sGiven = jFlow[$"month{m}_given"].ToString();
                                    decimal given = CPubVars.ToDecimal(sGiven);
                                    decimal monthLeft = qty - given;
                                    var subID = flow.fee_sub_id;

                                    var DispflowID = flow.flow_id;
                                    if (payway_id == subID)
                                    {
                                        decimal useAmt = 0;
                                        if (leftAmt1 >= monthLeft) useAmt = monthLeft; else useAmt = leftAmt1;

                                        useAmt *= money_inout_flag;
                                        leftAmt1 -= useAmt;
                                        string fldName = $"month{m}_given";
                                        if (leftAmt1 >= 0 && useAmt != 0)
                                        {
                                            if (flowFields != "") flowFields += ",";
                                            flowFields += $"{fldName} =coalesce({fldName},0) +({useAmt})";
                                        }
                                        stillLeftAmt += qty - given - useAmt;
                                    }
                                }
                                if (flowFields != "")
                                {
                                    string allGiven = "true";
                                    if (stillLeftAmt > 0.01m) allGiven = "false";
                                    flowFields += $",all_given={allGiven}";
                                    AddExecSQL($"update display_agreement_detail set {flowFields} where company_id={company_id} and flow_id={flow.flow_id};");
                                }
                            }
                        }
                        else if (money_inout_flag == -1)
                        {

                            foreach (dynamic flow in lstFlow)
                            {
                                decimal stillLeftAmt = 0m;
                                string flowFields = "";
                                JObject jFlow = JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(flow));
                                for (int m = 12; m >= 1; m--)
                                {
                                    string sQty = jFlow[$"month{m}_qty"].ToString();
                                    decimal qty = CPubVars.ToDecimal(sQty);
                                    string sGiven = jFlow[$"month{m}_given"].ToString();
                                    decimal given = CPubVars.ToDecimal(sGiven);
                                    decimal monthLeft = qty - given;
                                    var subID = flow.fee_sub_id;

                                    var DispflowID = flow.flow_id;
                                    if (payway_id == subID && given > 0)
                                    {
                                        decimal useAmt = 0;
                                        if (leftAmt1 >= given) useAmt = given; else useAmt = leftAmt1;

                                        //useAmt *= money_inout_flag;
                                        leftAmt1 -= useAmt;
                                        string fldName = $"month{m}_given";
                                        if (leftAmt1 >= 0 && useAmt != 0)
                                        {
                                            if (flowFields != "") flowFields += ",";
                                            flowFields += $"{fldName} =coalesce({fldName},0) - ({useAmt})";
                                        }
                                        stillLeftAmt += qty - given - (useAmt * (-1));
                                    }
                                }
                                if (flowFields != "")
                                {
                                    string allGiven = "false";
                                    if (stillLeftAmt < 0.01m) allGiven = "true";
                                    flowFields += $",all_given={allGiven}";
                                    AddExecSQL($"update display_agreement_detail set {flowFields} where company_id={company_id} and flow_id={flow.flow_id};");
                                }
                            }
                        }
                    }

                }
            }
            #endregion

            #region 更新现金银行余额
            if (info.BizStartPeriod != "" && info.PaywaysInfo != null && !IsImported)
            {
                Dictionary<string, decimal> pws = new Dictionary<string, decimal>();
                Subject pw1 = info.PaywaysInfo.Find(p => p.sub_id == payway1_id && p.sub_type == "QT");
                if (pw1 != null && payway1_amount != 0)
                {
                    if (!pws.ContainsKey(payway1_id)) pws.Add(payway1_id, payway1_amount);
                    else pws[payway1_id] += payway1_amount;
                }
                Subject pw2 = info.PaywaysInfo.Find(p => p.sub_id == payway2_id && p.sub_type == "QT");
                if (pw2 != null && payway2_amount != 0)
                {
                    if (!pws.ContainsKey(payway2_id)) pws.Add(payway2_id, payway2_amount);
                    else pws[payway2_id] += payway2_amount;
                }
                Subject pw3 = info.PaywaysInfo.Find(p => p.sub_id == payway3_id && p.sub_type == "QT");
                if (pw3 != null && payway3_amount != 0)
                {
                    if (!pws.ContainsKey(payway3_id)) pws.Add(payway3_id, payway3_amount);
                    else pws[payway3_id] += payway3_amount;
                }
                if (pws.Count() > 0)
                {
                    string sql_cb = base.UpdateCashBankBalance(pws);
                    if (sql_cb.Trim() != "") AddExecSQL(sql_cb);
                }
            }

            #endregion

            cmd.CommandText = GetExecSQL();
            if (cmd.CommandText != "")
            {
                await cmd.ExecuteNonQueryAsync();
            }

            if (this.SheetType == "X" || this.SheetType == "T")
            {
                if (info.WeChatInfo != null && info.WeChatInfo.Count > 0)
                {
                    try
                    {
                        await SendSheetSimple(cmd,_httpClientFactory, info.WeChatInfo, this.SheetType, this.now_disc_amount.ToString(), this.left_amount.ToString(), this.payway1_name, this.payway1_amount.ToString(), this.payway2_name, this.payway2_amount.ToString(),this.payway3_name,this.payway3_amount.ToString(),this.supcust_id);
                    }
                    catch (Exception ex)
                    {
                        NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                        MyLogger.LogMsg("in SheetSale.SendSheetSimple:发送单据信息到微信发生错误" + ex.StackTrace + "" + ex.Message, company_id);
                        logger.Error("in SheetSale.SendSheetSimple:发送单据信息到微信发生错误");
                    }
                }
            }
        }
        public override string GetWeChatMsgHead()
        {
            string sheetTypeName = "";
            string first = "";
            switch (this.SheetType) {
                case "X" :  sheetTypeName = "销售单"; break;
                case "T" :  sheetTypeName = "退货单"; break;
            }
            switch (this.red_flag)
            {
                case "": first = $"您有新的【{sheetTypeName}】,来自【{this.company_name}】,请注意查收"; break;
                case "2": first = $"您的【{sheetTypeName}】【被红冲】,来自【{this.company_name}】,请注意查收"; break;
            }
            return first;
        }
        public override string GetWeChatMsgTail()
        {
            string remark = "";
            if (this.sheet_no != "") remark += "单据编号：" + this.sheet_no + "\n";

            return remark;
        }
        /*
           protected List<SheetRowBorrowItem> MergeOrderedSheetRows(List<SheetRowBorrowItem> rows)
           {
               Dictionary<string, SheetRowBorrowItem> rowsDict = new Dictionary<string, SheetRowBorrowItem>();
               foreach (SheetRowBorrowItem sheetRow in SheetRows)
               {
                   if (sheetRow.order_sub_id == "") continue;
                   string skey = sheetRow.item_id + "_" +  sheetRow.OrderPrice.ToString() + "_" + sheetRow.order_sub_id.ToString();
                   SheetRowBorrowItem curRow = null;
                   rowsDict.TryGetValue(skey, out curRow);
                   if (curRow == null)
                   { 
                       curRow = JsonConvert.DeserializeObject<SheetRowBorrowItem>(JsonConvert.SerializeObject(sheetRow)); 
                       curRow.quantity = sheetRow.quantity * sheetRow.unit_factor / sheetRow.OrderUnitFactor;
                       curRow.OrderedBalance = sheetRow.OrderedBalance;
                       curRow.OrderedQty = sheetRow.OrderedQty;
                       curRow.OrderUnitNo = sheetRow.OrderUnitNo;
                       curRow.OrderUnitFactor = sheetRow.OrderUnitFactor;
                       curRow.OrderPrice = sheetRow.OrderPrice; 
                       rowsDict.Add(skey, curRow);
                   }
                   else
                   {
                       curRow.quantity += sheetRow.quantity * sheetRow.unit_factor / sheetRow.OrderUnitFactor;
                       curRow.sub_amount += sheetRow.sub_amount;
                   }
               }
               List<SheetRowBorrowItem> newList = new List<SheetRowBorrowItem>();
               foreach (var k in rowsDict)
               {
                   newList.Add(k.Value);
               }
               return newList;

           }

           */

        protected List<SheetRowBorrowItem> MergeOrderedSheetRows(List<SheetRowBorrowItem> rows)
        {
            Dictionary<string, SheetRowBorrowItem> rowsDict = new Dictionary<string, SheetRowBorrowItem>();
            foreach (SheetRowBorrowItem sheetRow in SheetRows)
            {
                if (sheetRow.order_sub_id == "") continue;
                if (sheetRow.quantity == 0) continue;
                string unitPrice =CPubVars.FormatMoney(sheetRow.real_price / sheetRow.unit_factor,2);
                string skey = sheetRow.item_id + "_" + unitPrice + "_" + sheetRow.order_sub_id.ToString()+"_"+sheetRow.order_flow_id;
                SheetRowBorrowItem curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = JsonConvert.DeserializeObject<SheetRowBorrowItem>(JsonConvert.SerializeObject(sheetRow));
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.real_price = curRow.real_price / curRow.unit_factor;


                    //curRow.OrderedBalance = sheetRow.OrderedBalance;
                    //curRow.OrderedQty = sheetRow.OrderedQty;
                    // curRow.OrderUnitNo = sheetRow.OrderUnitNo;
                    // curRow.OrderUnitFactor = sheetRow.OrderUnitFactor;
                    // curRow.OrderPrice = sheetRow.OrderPrice;
                    rowsDict.Add(skey, curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                    curRow.sub_amount += sheetRow.sub_amount;
                }
            }
            List<SheetRowBorrowItem> newList = new List<SheetRowBorrowItem>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }

        protected List<SheetRowBorrowItem> MergeTradeSheetRows(List<SheetRowBorrowItem> rows)
        {
            Dictionary<string, SheetRowBorrowItem> rowsDict = new Dictionary<string, SheetRowBorrowItem>();
            foreach (SheetRowBorrowItem sheetRow in SheetRows)
            {
                if (sheetRow.trade_type == "X" || sheetRow.trade_type == "T") continue;
                string skey = "";
                if (sheetRow.son_mum_item != "")
                {
                    skey = sheetRow.son_mum_item + "_" + sheetRow.trade_type.ToString();
                }
                else
                {
                    skey = sheetRow.item_id + "_" + sheetRow.trade_type.ToString();
                }
                SheetRowBorrowItem curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = new SheetRowBorrowItem();
                    curRow.item_id = sheetRow.item_id;
                    curRow.item_name = sheetRow.item_name;
                    curRow.unit_factor = sheetRow.unit_factor;
                    curRow.quantity = sheetRow.unit_factor * sheetRow.quantity * sheetRow.inout_flag; //转化成小单位
                    curRow.trade_type = sheetRow.trade_type;
                    curRow.inout_flag = sheetRow.inout_flag;
                    curRow.HasBorrowedQty = sheetRow.HasBorrowedQty;
                    curRow.BorrowedQty = sheetRow.BorrowedQty;
                    curRow.StockQty = sheetRow.StockQty;
                    if (sheetRow.son_mum_item !="")
                    {
                        curRow.son_mum_item = sheetRow.son_mum_item;
                    }
                    rowsDict.Add(skey, curRow);
                }
                else curRow.quantity += sheetRow.quantity * sheetRow.unit_factor * sheetRow.inout_flag;

            }
            List<SheetRowBorrowItem> newList = new List<SheetRowBorrowItem>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }


        protected List<SheetRowBorrowItem> MergeDispSheetRows(List<SheetRowBorrowItem> rows)
        {
            Dictionary<string, SheetRowBorrowItem> rowsDict = new Dictionary<string, SheetRowBorrowItem>();
            foreach (SheetRowBorrowItem sheetRow in SheetRows)
            {
                if (sheetRow.disp_flow_id != null && sheetRow.disp_flow_id == "") continue;
                string skey = sheetRow.disp_flow_id+'_'+sheetRow.disp_month_id;
                SheetRowBorrowItem curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = new SheetRowBorrowItem();
                    curRow.item_id = sheetRow.item_id;
                    curRow.item_name = sheetRow.item_name;
                    curRow.unit_no = sheetRow.unit_no;
                    curRow.quantity = sheetRow.quantity;
                    curRow.inout_flag = sheetRow.inout_flag;
                    curRow.trade_type = sheetRow.trade_type;
                    curRow.disp_flow_id = sheetRow.disp_flow_id;
                    curRow.disp_month_id = sheetRow.disp_month_id;
                    curRow.disp_sheet_id = sheetRow.disp_sheet_id;
                    curRow.disp_month_left = sheetRow.disp_month_left;
                    rowsDict.Add(skey, curRow);
                }
                else curRow.quantity += sheetRow.quantity;
            }
            List<SheetRowBorrowItem> newList = new List<SheetRowBorrowItem>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }
        //public override decimal setRowSaleOrderQty(SheetRowBorrowItem saleRow)
        //{
        //    return 1;
        //}



        /*public override async Task<JsonResult> ToVoucherRows(CMySbCommand cmd, string sheetID, SheetCwVoucher sheetCwVoucher, Dictionary<string, decimal> payways)
        {
            string subsID = "";
            string condi = "";// 主营业务收入
            string payCondi = "";
            int subLen = 1;
            if (payways == null || payways.Count == 0) // 单张单据生成凭证
            {
                if (payway1_id != "" && payway1_amount != 0) payways.Add(payway1_id, payway1_amount * money_inout_flag);
                if (payway2_id != "" && payway2_amount != 0) payways.Add(payway2_id, payway2_amount * money_inout_flag);
                if (left_amount != 0) payways.Add("left", left_amount * money_inout_flag);
                if (now_disc_amount != 0) payways.Add("disc", now_disc_amount * money_inout_flag);
                if (total_amount != 0) payways.Add("total", total_amount * money_inout_flag);
            }
            if(payways == null || payways.Count == 0)
            {
                return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
            }
            foreach (var payway in payways) // 按业务批量生产凭证
            {
                if (payway.Key == "total" || payway.Key.StartsWith("gift")) continue;
                else if (payway.Key == "left") condi += $@"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='1122' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='1122')  order by sub_code limit 1 )"; // 应收账款第一个明细
                else if (payway.Key == "disc") condi += $@"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_code=560304 )"; //财务费用-现金折扣（不允许增加子科目）
                else
                {
                    if (subsID != "") subsID = subsID + ",";
                    subsID += payway.Key;
                }
                subLen++;
            }
            if (subsID != "") payCondi += $"union all ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and sub_id in ({subsID}) )";
            string sql = $@"( 
                select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='5001' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='5001')  order by sub_code limit 1 
                ) {condi} {payCondi} ";
            dynamic subs_biz = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (subs_biz == null || subs_biz.Count < subLen) return new JsonResult(new { result = "Error", msg = "缺少生成凭证的相关科目，请添加" });
            if (subs_biz.Count > subLen) return new JsonResult(new { result = "Error", msg = "生成凭证的相关科目有重复科目代码，请修改" });

            //赠品转凭证
            if (payways.ContainsKey("gift"))
            {
                string remark0 = "";
                sql = $"( select sub_id,sub_name,sub_code,direction,COALESCE(status,'1') as status,'赠品' as remark,'{payways["gift"]}' as change_amount from cw_subject where company_id = {company_id} and sub_code =1405 )";//库存商品不允许增加子科目
                List<string> paywayGiftToRemove = new List<string>();
                paywayGiftToRemove.Add("gift");
                foreach (KeyValuePair<string, decimal> payway in payways)
                {
                   if(payway.Key.StartsWith("gift") && payway.Key != "gift")
                    {
                        remark0 = payway.Key.Replace("gift", "").Split(" ")[1];
                        sql += $"union all ( select sub_id,sub_name,sub_code,direction,COALESCE(status,'1') as status,'赠品' as remark,'{payway.Value}' as change_amount from cw_subject where company_id = {company_id} and sub_id={payway.Key.Replace("gift", "").Split(" ")[0]} and sub_id not in (select mother_id from cw_subject where company_id= {company_id} ))";
                        paywayGiftToRemove.Add(payway.Key);
                    }
                }
                sql=sql.Replace("赠品", remark0);
                dynamic subs_gift = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                if (subs_gift == null || subs_gift.Count < paywayGiftToRemove.Count) return new JsonResult(new { result = "Error", msg = "赠品缺少生成凭证的相关科目，或不是明细科目" });
                foreach (dynamic sub in subs_gift)
                {
                    if (Convert.ToInt16(sub.status) == 0) return new JsonResult(new { result = "Error", msg = "相关科目已停用，请检查" });
                    CwRowVoucher cwRow = new CwRowVoucher();
                    cwRow.business_sheet_type = SheetType;
                    cwRow.business_sheet_id = sheetID;
                    cwRow.sub_id = sub.sub_id;
                    cwRow.remark = sub.remark;
                    decimal.TryParse(sub.change_amount, out decimal changeAmt);
                    if (sub.sub_code == "1405")
                    {
                        cwRow.credit_amount = (-changeAmt).ToString();
                    }
                    else
                    {
                        cwRow.debit_amount=sub.change_amount;
                    }
                    cwRow.change_amount = sub.change_amount;
                    sheetCwVoucher.SheetRows.Add(cwRow);
                }
                foreach(string paywayGift in paywayGiftToRemove)
                {
                    payways.Remove(paywayGift);
                }
            }
            //业务转凭证
            foreach (var sub in subs_biz) // 对于每个 sub ，都有对应借贷金额，加入到cwRow里
            {
                if (sub.status == null || sub.status == "") sub.status = 1;
                if(Convert.ToInt16(sub.status)==0) return new JsonResult(new { result = "Error", msg = "相关科目已停用，请检查" });
                CwRowVoucher cwRow = new CwRowVoucher();
                cwRow.business_sheet_type = SheetType;
                cwRow.business_sheet_id = sheetID;
                cwRow.sub_id = sub.sub_id;
                if (!payways.ContainsKey("total")) break;
                cwRow.remark = payways["total"] >= 0 ? "销售商品" : "退货";

                if(red_flag == "2") cwRow.remark = payways["total"] >= 0 ? "红冲销售商品" : "红冲退货";
                decimal changeAmt = 0;
                foreach(var payway in payways) 
                {
                    changeAmt = payway.Value;

                    if (payway.Key == sub.sub_id || (payway.Key == "left" && sub.sub_code.ToString().Substring(0,4) == "1122") || (payway.Key == "disc" && sub.sub_code == "560304"))
                    {
                        if (changeAmt >= 0) cwRow.debit_amount = changeAmt.ToString();
                        else cwRow.credit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = changeAmt.ToString();
                        break;
                    }
                    else if (sub.sub_code.ToString().Substring(0, 4) == "5001" && payway.Key == "total") // 主营业务收入
                    {
                        if (changeAmt >= 0) cwRow.credit_amount = changeAmt.ToString();
                        else cwRow.debit_amount = Math.Abs(changeAmt).ToString();
                        cwRow.change_amount = (-1 * changeAmt).ToString();
                        break;
                    }
                }
                
                sheetCwVoucher.SheetRows.Add(cwRow);
            }

            return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
        }*/
        protected override async Task<string> CheckForRed(CMySbCommand cmd)
        {
            
            // 检查欠条
            string billSheetSql = @$"select sheet_no from sheet_move_arrears_bill_main m left join
                    sheet_move_arrears_bill_detail d on d.company_id = m.company_id and d.sheet_id = m.sheet_id
                    where m.company_id = {company_id} and business_sheet_id = {sheet_id} and business_sheet_type = '{SheetType}'and approve_time is not null and red_flag is null
                    order by m.sheet_id desc nulls last";
            dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(billSheetSql, cmd);
            if (ret != null)
            {
                return $"请红冲包含关联欠条的单据【{ret.sheet_no}】";
            };
            return "";
        }
            public SheetBorrowItem Copy()//深拷贝
        {
            return (SheetBorrowItem)this.MemberwiseClone();
        }

		#region 重载SheetBase里的方法
		/* Note
         * 通过重载SheetBase的SaveAndApprove、Red等方法来为本类型单据的处理流程统一地添加逻辑
         * await base.xxx是原逻辑的调用节点，可以在此之前或之后视情况地插入代码
        */
		public override async Task<string> OnSheetBeforeSave(CMySbCommand cmd, CInfoForApproveBase info)
		{
            string msg = "";

            //根据公司权限销售单单号与订单相同
            if (!string.IsNullOrWhiteSpace(order_sheet_id))
            {
                await LoadCompanySetting(cmd);
                if (company_setting != null && company_setting.saleSheetNoSameAsOrder  == "True")
                {
                    sheet_no = order_sheet_no;
                }
            }

            // 检查限时特价商品限购
            if (order_sheet_id.IsInvalid() || sheet_id.IsValid())
            {
                if (!IsImported)
                {
                    // 是从销售订单转单而来的销售单,仅限第二次及之后的保存执行校验
                    var checkPromotionSeckillResult = await PromotionController.CheckSeckillItems(cmd, this);
                    Console.WriteLine($"SaleSheet({sheet_id}).checkPromotionSeckillResult.Result: " +
                        checkPromotionSeckillResult.ToJsonText());
                    if (!checkPromotionSeckillResult.IsOK)
                        return checkPromotionSeckillResult.msg;
                }
            }
 
            return msg;
        } 
        
         
       #endregion
 
    }
}
