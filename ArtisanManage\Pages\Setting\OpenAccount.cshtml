@page
@model OpenAccount
@{
    var Businesses = await Model.cmd.QueryWhereAsync<Business>(null);
    
}
<link href="~/css/component.css" rel="stylesheet" asp-append-version="true"/>
<style>
    *{
        margin: 0;
        padding: 0;
        font-size:20px;
    }
    html, body {
        height: 100%;
    }
    .query-box{
        display:flex;
        flex-flow:row wrap;
        align-items:center;
        margin:0.5rem 1rem;
    }
    #divList > header {
        flex: none;
        display: flex;
        justify-content: space-between;
        margin: 0 1rem;
        border-bottom: 1px solid #ddd;
        padding: 1rem 0;
    }
    #divList > main {
        margin: 0 1rem;
        flex: auto;
        height: 100%;
    }

    #divEdit > header {
        flex: none;
        margin: 2rem;
        border-bottom: 1px solid #ddd;
        padding-bottom: 1rem;
    }
    #divEdit > form {
        flex: auto;
        margin: 2rem;
        border-bottom: 1px solid #ddd;
    }
        #divEdit > form > label {
            display: flex;
            justify-content: space-between;
            margin: 1rem 0;
        }
    #divEdit > footer {
        flex: none;
        display: flex;
        justify-content: space-around;
    }

    #divView > header {
        flex: none;
        margin: 2rem;
        border-bottom: 1px solid #ddd;
        padding-bottom: 1rem;
    }
    .err {
        border: red 1px solid;
    }
    .addr{
        width:30%;
        display:inline-flex;
        justify-content:space-between;
    }
    .btnView {
        color: blue;
        text-decoration:underline;
    }
    table {
        width: 100%;
        height: 100%;
        border-collapse: collapse;
    }

    th, td {
        border: 1px #ddd solid;
        text-align: center;
    }

    .showBy {
        display: none;
    }

    [showChild~=divList] #divList,
    [showChild~=divEdit] #divEdit,
    [showChild~=divView] #divView {
        display: flex;
        height: 98%;
        flex-flow: column nowrap;
    }
    .form-row{
        line-height:1.5rem;
        margin:0.5rem;
    }
    .form-row label{
        display:inline-block;
        width:30%;
        text-align:end;
        margin: 0 0.5rem;
    }
        .form-row select,
        .form-el {
            display: inline-block;
            width: 30%;
            text-align: start;
            outline: none;
            border: none;
            border-bottom: 1px groove;
        }

    .verification-failed {
        border: 1px red solid;
    }
</style>
<div class="showBy" id="divList">
    <header>
        <span>公司账户</span>
       <!-- <button class="btnAdd icon icon-plus">开户</button>-->
    </header>
    <form id="queryBox">
        <label class="searcher">
            <input type="text" bind="text" placeholder="请输入公司名称/老板名字/老板电话/代理姓名" keycode="13"/>
            <i class="btnQuery icon icon-search"></i>
        </label>
        <label>开户时间:
            <input type="date" bind="createTimeStart" placeholder="从" /> ~
            <input type="date" bind="createTimeEnd"  placeholder="到" />
        </label>
    </form>
    <main id ="grid" ></main>
</div>
<div class="showBy" id="divEdit">
    <header>
        <button class="btnBack icon icon-reply"> 返回</button><span></span>
    </header>
    <form id="root">
        <input hidden name="url" id="url" />
        <div class="form-row" hidden>
            <label>公司Id:</label><input class="form-el" name="@(nameof(Company.company_Id))" />
        </div>
        <div class="form-row">
            <label>公司名称*:</label><input class="form-el" validator="required" name="@(nameof(Company.companyName))" />
        </div>
        <div class="form-row">
            <label>行业:</label>
            <select id="business" class="form-el" validator="required" name="@(nameof(Company.businessId))">
                @foreach (var business in Businesses)
                {
                    <option title="@business.company_Id" value="@(business.Id)">@business.AlisaName</option>
                }
            </select>
        </div>
        <div class="form-row">
            <label>老板名字*:</label><input class="form-el" validator="required" name="@(nameof(Company.bossName))" />
        </div>
        <div class="form-row">
            <label>老板电话*:</label><input class="form-el" validator="mobile" name="@(nameof(Company.bossMobile))" />
        </div>
        <div class="form-row">
            <label>代理姓名*:</label><input class="form-el" validator="required" name="@(nameof(Company.agentName))" />
        </div>
        <div class="form-row">
            <label>端口数*:</label><input class="form-el" validator="positiveInt" name="@(nameof(Company.userCount))" />
        </div>
        <div class="form-row">
            <label>折扣1:</label>
             <select id="fee_discount1" class="form-el" validator="positiveFloat" name="@(nameof(Company.fee_discount1))">
                @foreach (dynamic disc in Model.AvailFeeDiscounts)
                {
                    <option title="@disc.fee_discount" value="@(disc.fee_discount)">@disc.fee_discount</option>
                }
            </select> 
          
        </div>
        <div class="form-row">
            <label>折扣1端口数:</label><input class="form-el"  name="@(nameof(Company.user_count_discount1))" />
        </div>
         <div class="form-row">
            <label>折扣2:</label>
              <select id="fee_discount2" class="form-el" validator="positiveFloat" name="@(nameof(Company.fee_discount2))">
                @foreach (dynamic disc in Model.AvailFeeDiscounts)
                {
                    <option title="@disc.fee_discount" value="@(disc.fee_discount)">@disc.fee_discount</option>
                }
            </select>  
             
        </div>
        <div class="form-row">
            <label>折扣2端口数:</label><input class="form-el"  name="@(nameof(Company.user_count_discount2))" />
        </div>
        <div class="form-row">
            <label>公司地址:</label>
            <div class="addr">
                <select name="province" id="province"></select>
                <select name="city" id="city"></select>
                <select name="county" id="county"></select>
            </div>
        </div>
         <div class="form-row">
            <label>开户时间:</label><input class="form-el"  name="@(nameof(Company.createTime))" />
        </div>

    </form>
    <footer>
            <button class="btnSave">保存</button>
    </footer>
</div>
<div class="showBy" id="divView">
    <header>
        <button class="btnBack icon icon-reply"> 返回</button><span></span>
    </header>
    <div>
        <div class="form-row">
            <label>公司名称*:</label><span title="@(nameof(Company.companyName))"></span>
        </div>
        <div class="form-row">
            <label>老板名字:</label><span title="@(nameof(Company.bossName))"></span>
        </div>
        <div class="form-row">
            <label>老板电话*:</label><span title="@(nameof(Company.bossMobile))"></span>
        </div>
        <div class="form-row">
            <label>代理姓名*:</label><span title="@(nameof(Company.agentName))"></span>
        </div>
        <div class="form-row">
            <label>端口数*:</label><span title="@(nameof(Company.userCount))"></span>
        </div>

         <div class="form-row">
            <label>折扣1:</label><span title="@(nameof(Company.fee_discount1))"></span>
        </div>
         <div class="form-row">
            <label>折扣1端口数:</label><span title="@(nameof(Company.user_count_discount1))"></span>
        </div>
        <div class="form-row">
            <label>折扣2:</label><span title="@(nameof(Company.fee_discount2))"></span>
        </div>
         <div class="form-row">
            <label>折扣2端口数:</label><span title="@(nameof(Company.user_count_discount2))"></span>
        </div>  


        <div class="form-row">
            <label>开户时间:</label><span title="@(nameof(Company.createTime))"></span>
        </div>
        <div class="form-row">
            <label>付费时间:</label><span title="@(nameof(Company.payTime))"></span>
        </div>
        <div class="form-row">
            <label>到期时间:</label><span title="@(nameof(Company.expireTime))"></span>
        </div>
        <div class="form-row">
            <label>公司地址:</label>
            <span title="province"></span>
            <span title="city"></span>
            <span title="county"></span>
        </div>
    </div>
</div>
<script type="text/template" id="ResetKeyTmpl">
    <div class="form-row">
        <label>员工手机:</label><input class="form-el" validator="mobile"  name="mobile"/>
    </div>
    <div class="form-row">
        <label>管理员密码:</label><input class="form-el" validator="password" name="password"/>
    </div>
</script>
<script src="~/js/commission.js" asp-append-version="true"></script>
<script type="text/javascript">
    app = new App(null, {
        setup() {
            this.add('Validator')
                .add('Fetch')
                .add('Grid')
                .add('MessageBox')
                .add('QueryBox')
                .add('Dialog')
                .useKeyboard('keydown',{
                    Enter(e) {
                        setTimeout(function () {
                            app.Query();
                        }, 1);
                    },
                    PageUp(e) {
                        app.grid.go(-20);
                    },
                    PageDown() {
                        app.grid.go(20);
                    },
                    ArrowUp(e) {
                        var step = e.ctrlKey ? 20 : 1;
                        app.grid.go(-step);
                    },
                    ArrowDown(e) {
                        var step = e.ctrlKey ? 20 : 1;
                        app.grid.go(step);
                    },
                    End() {
                        app.grid.goto(10000000);
                    },
                    Home() {
                        app.grid.goto(0);
                    }
                }).useDebug(1)
        },
        switchTab(tab = 'divList', tip = '公司账户') {
            $('header span').text(tip);
            document.body.setAttribute('showChild', tab);
        },
        '.': {
            click: {
                btnAdd() {
                    $('#root').find(':input').each(function () {
                        $(this).val('');
                    });
                    $('#city').empty();
                    $('#county').empty();
                    debugger
                    $('#url').val('@Startup.Localhost/api/openAccount/PrepareAccount?mode=0');
                    app.switchTab('divEdit', ' 添加账户');
                    $('#create-time').removeAttr('validator')
                },
                btnEdit() {
                    var data = app.view(this);
                    $('#root').find(':input').each(function () {
                        if (this.type == 'date') $(this).val(data[this.name].substring(0, 10));
                        else {
                            $(this).val(data[this.name]);
                            $(this).trigger('input');
                        };
                    });
                    $('#city').val(data.city);
                    $('#url').val('@Startup.Localhost/api/openAccount/UpdateAccount');

                    app.switchTab('divEdit', ' 编辑账户');
                    $('#create-time').attr("validator",'required')
                },
                btnView() {
                    var data = app.view(this);
                    $('#divView').find('span').each(function () {
                        $(this).text(data[this.title]);
                    });
                    app.switchTab('divView', ' 账户详情');
                },
                btnBack() {
                    app.switchTab();
                },
                btnSave() {
                  /*  var data = app.validator.verify('#root');
                    //console.log('现在测试中，已关闭数据通信：',data);return;
                    if (data) {
                        data.companyTmpl = $('#business :selected')[0].title;
                        data.company_Id = Number(data.company_Id);
                        app.fetch.post(data, data.url, function (x) {
                            if (x.result == 'OK') {
                                app.messageBox.success('操作成功', () => app.switchTab());
                            } else {
                                app.messageBox.error(x.msg);
                            }
                        });
                    }*/
                },
                btnQuery() {
                    app.Query();
                },
            },
        },
        '#': {
            input: {
                province() {
                    if (!this.value) return;
                    this.parentElement.province = this.value;
                    var cities = Object.keys(app.regions[this.value]).map(x => `<option value='${x}'>${x}</option>`);
                    cities.unshift('<option value="" selected>请选择市</option>');
                    $('#city').html(cities).trigger('input');
                },
                city() {
                    if (!this.value) return;
                    var province = this.getKey('province');
                    var counties = app.regions[province][this.value].map(x => `<option value='${x}'>${x}</option>`);
                    counties.unshift('<option value="" selected>请选择县区</option>');
                    $('#county').html(counties).trigger('input');
                }
            }
        },
        PrepareAccount(action, data) {
            app.fetch.post(data, '/api/OpenAccount/' + action, function (x) {
                if (x.result == 'OK') {
                    app.showSuccess('开户成功');
                    app.switchTab();
                } else {
                    app.showError(x.msg);
                }
            });
        },
        regions:@Html.Raw(await System.IO.File.ReadAllTextAsync(Environment.CurrentDirectory + "/JsonFiles/administrativeRegion.json")),
        Query() {
            this.queryBox.QueryData((data) => {
                app.storeGrid(data);
            })
        }
    });
    app.ready(function () {
        app.useMessageBox();
        app.useQueryBox('queryBox', {
            url: '/api/OpenAccount/getCompanies',
        });
        app.useFetch({/**构思中*/
            api: 'OpenAccount',
            token: '',
            timeout:0
        });
        app.useGrid({
            buildTable(table) {
                table.child('thead', {
                    cols: ['序号','ID', '公司名称', '	开户时间','老板名字','老板电话','代理姓名', '端口数', '操作'],
                }).child('tbody', {
                    size: parseInt($('#grid').height() / 40),
                    cols: ['idx','companyId', 'companyName', 'createTime', 'bossName', 'bossMobile','agentName','portQuantity', 'options'],
                    render: {
                        idx(data,i) {
                            return ++i;
                        },
                        companyId(data, i) {
                            return data.company_Id;
                        },
                        companyName(data, i) {
                            return `<a class='btnView' title='查看详情'>${data.companyName}</a>`;
                        },
                        portQuantity(data) {
                            return data.userCount;
                        },
                        options(data) {
                            var s = data.disable ? 'play btnPlay' : 'stop btnStop';
                            return `<a class='btnEdit icon icon-edit'></a>
                                       <a class='icon icon-retweet btnResetKey' title='重置员工密码'></a>
                                       <a class='icon icon-${s}'></a>`;
                        },
                    }
                })
            },
        });
        app.useDialog({
            id: 'ResetKey',
            title: '重置员工密码',
            caller: '.btnResetKey',
            tmpl: 'ResetKeyTmpl',
            beforeShow(e, caller) {
                this.data = app.view(caller);
            },
            okEvent(self) {
                var data = app.validator.verify('#ResetKey');
                console.log(data);
                if (!data) return;
                data.company_Id = this.data.company_Id;
                app.fetch.post(data, '/api/OpenAccount/ResetPassword', function (x) {
                    app.showMessage(x);
                    self.Hide();
                })
            }
        });
        app.useValidator().watch('#root').watch('#ResetKey');
        app.Query();

        var provinces = Object.keys(app.regions).map(x => `<option value='${x}'>${x}</option>`);
        provinces.unshift('<option value="" selected>请选择省</option>');
        $('#province').html(provinces);
    });
</script>