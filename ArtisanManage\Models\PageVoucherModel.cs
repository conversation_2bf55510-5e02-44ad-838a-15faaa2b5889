﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;
using ArtisanManage.MyCW;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
//using Newtonsoft.Json;
//using Newtonsoft.Json.Linq;

namespace ArtisanManage.Models
{
    public class VoucherPartialViewModel : PartialViewModelBase
    {
        public string m_saveCloseScript = "";
        public string m_createGridScript = "";
        public string m_saveGridScript = "";
        public string GetFormDataScript = "";
        public string SheetRowsJson = "";
        public string ItemsInfoJson = "{}";
        public string m_showFormScript = "";
    }
    public class VoucherGrid<CROW> where CROW : CwRowBase, new()
    {
        public Dictionary<string, DataItem> Columns = new Dictionary<string, DataItem>();
        public List<Dictionary<string, DataItem>> Rows = new List<Dictionary<string, DataItem>>();
        public string TableName = ""; public string IdFld = "";
        public string SelectFromSQL = "";
        public int MinRows = 3;
        public bool AllowAddRow = true;
        public string scriptCreateGrid, scriptSaveGrid;

        public async Task GetScript(CMySbCommand cmd, PageVoucherModel<CROW> pageForm, string gridID, SheetCwBase<CROW> voucher)
        {
            List<Dictionary<string, dynamic>> lstRows = new List<Dictionary<string, dynamic>>();
            PropertyInfo[] props = typeof(CROW).GetProperties();
            foreach (CROW sheetRow in voucher.SheetRows)
            {
                Dictionary<string, dynamic> row = new Dictionary<string, dynamic>();
                lstRows.Add(row);
                foreach (KeyValuePair<string, DataItem> key in Columns)
                {
                    DataItem dataItem = key.Value;

                    foreach (PropertyInfo prop in props)
                    {
                        if (prop.Name.ToLower() == key.Key.ToLower())
                        {
                            object p = prop.GetValue(voucher);
                            if (p != null)
                            {
                                dataItem.Value = p.ToString();
                            }
                        }
                        else if (prop.Name.ToLower() == dataItem.LabelFld.ToLower())
                        {
                            object p = prop.GetValue(voucher);
                            if (p != null)
                            {
                                dataItem.Value = p.ToString();
                            }
                        }
                    }
                }
            }

            int rowCount = MinRows;
            if (AllowAddRow && lstRows.Count >= MinRows) rowCount = lstRows.Count + 1;

            for (int i = lstRows.Count; i < rowCount; i++)
            {
                Dictionary<string, dynamic> row = new Dictionary<string, dynamic>();
                lstRows.Add(row);
                foreach (KeyValuePair<string, DataItem> key in Columns)
                {
                    DataItem dataItem = key.Value;
                    row[key.Key] = "";
                }
            }
            Dictionary<string, string> varsG = new Dictionary<string, string>();
            varsG["rows"] = JsonConvert.SerializeObject(lstRows);

            string sColumns = @"{
                            text: '', sortable: false, filterable: false, editable: false, pinned: true,
                            groupable: false, draggable: false, resizable: false,
                            datafield: '', columntype: 'number', width: 30,
                            cellclassname: fixColCss,
                            cellsrenderer: function(row, column, value) {
                                 return '<div style=""margin:4px;"">' + (value + 1) + '</div>';
                            } 
                           }";
            foreach (KeyValuePair<string, DataItem> key in Columns)
            {
                DataItem dataItem = key.Value;
                Dictionary<string, string> vars = new Dictionary<string, string>();
                vars["datafield"] = key.Key;
                vars["DisplayField"] = ""; vars["Title"] = dataItem.Title;
                if (dataItem.LabelFld != "") { vars["DisplayField"] = ", DisplayField: '" + dataItem.LabelFld + "'"; }
                vars["Width"] = "";
                if (dataItem.Width != "") { vars["Width"] = ", width: '" + dataItem.Width + "'"; }
                vars["columntype"] = ""; vars["createeditor"] = ""; vars["initeditor"] = "";

                string url = dataItem.Url; string source = dataItem.Source;
                if (dataItem.SqlForOptions != "")
                {
                    if (dataItem.GetOptionsOnLoad)
                        source = await PageBaseModel.GetDataItemOptions(cmd,pageForm.company_id, Columns, key.Key, null, null,null );
                    else
                    {
                        string controllerName = pageForm.GetType().FullName.Replace("Model", "").Split(".").Last();
                        url = "../api/" + controllerName + $"/GetColumnOptions?operKey={pageForm.OperKey}&gridID={gridID}&colName={key.Key}";
                    }
                }


                if (url != "" || source != "null")
                {
                    vars["columntype"] = ", columntype: 'template'";
                    Dictionary<string, string> varsE = new Dictionary<string, string>();
                    string ButtonUsage = "list";
                    if (dataItem.ButtonUsage == "event") ButtonUsage = "event";
                    varsE["ButtonUsage"] = ButtonUsage;
                    varsE["ValueFld"] = key.Key; varsE["LabelFld"] = key.Key;
                    if (dataItem.LabelFld != "") { varsE["LabelFld"] = dataItem.LabelFld; }
                    varsE["Url"] = url; varsE["Source"] = source;

                    vars["createeditor"] = @",createeditor:
                    function (row, cellvalue, editor, cellText, width, height) {
                        var element = $('<div></div >');
                        editor.append(element);
                        var inputElement = editor.find('div')[0]; 
                        var datafields = new Array({ datafield: '~LabelFld', text: '', width: 120 }); 
                        $(inputElement).jqxInput({
                            height: height, width: width,
                            borderShape: 'none',
                            buttonUsage: '~ButtonUsage', 
                            dropDownHeight: 160,
                            displayMember: '~LabelFld',
                            valueMember:  '~ValueFld',
                            datafields: datafields,
                            searchFields: ['~LabelFld'],
                            maxRecords: 9,
                            url:'~Url',
                            source:~Source
                        });
                     }";
                    vars["createeditor"] = PageBaseModel.GetStringWithVar(vars["createeditor"], varsE);
                    vars["initeditor"] = @",initeditor: function(row, cellvalue, editor, celltext, pressedkey) { 
                            var inputField = editor.find('input');
                            if (pressedkey)
                            {
                                inputField.val(pressedkey);
                                inputField.jqxInput('selectLast');
                            }
                            else
                            {
                                inputField.val({ value: cellvalue, label: celltext });
                                if(cellvalue=='') inputField.val('');
                                inputField.jqxInput('selectAll');
                            }
                           },
                        geteditorvalue: function(row, cellvalue, editor) {
                             var v = editor.find('input').val();
                              return v;}";
                }

                string col = @"{text: '~Title', datafield: '~datafield'~DisplayField~Width~columntype~createeditor,align:'center'~initeditor
                   }";
                col = PageBaseModel.GetStringWithVar(col, vars);
                if (sColumns != "") sColumns += ",";
                sColumns += col;
            }
            sColumns = "[" + sColumns + "]";

            varsG["columns"] = sColumns; varsG["rowCount"] = rowCount.ToString(); varsG["gridID"] = gridID;

            scriptCreateGrid = @"
            if(true){
            var theme = ''; var datafields = []; 
            var source =
            {
                localdata: ~rows,
                unboundmode: true,
                totalrecords: ~rowCount,
                datafields: datafields 
            };
            var dataAdapter = new $.jqx.dataAdapter(source);   
            var fixColCss = 'jqx-widget-header';
            if (theme != '') fixColCss += ' jqx-widget-header-' + theme;
               $('#~gridID').jqxGrid(
                {
                    height:150,
                    source:dataAdapter,
                    pageable:false, 
                    sortable:false,
                    editable:true,
                    columnsresize:true,
                    editmode:'selectedcell',
                    selectionmode:'singlerow',
                    theme: theme,
                    columns:~columns 
                });
            }

              $('#~gridID').on('contextmenu', function () {
                            return false;
                    });
            var divMenu=`<div id='gridMenu_~gridID'>
                    <ul>
                        <li>编辑</li>
                        <li>删除</li>
                    </ul>
                   </div>`;
              $('body').append(divMenu);
              var contextMenu = $('#gridMenu_~gridID').jqxMenu({ width: 200, height: 58, autoOpenPopup: false, mode: 'popup'});

              $('#~gridID').on('rowclick', function (event) {
                            if (event.args.rightclick) {
                                $('#~gridID').jqxGrid('selectrow', event.args.rowindex);
                    var scrollTop = $(window).scrollTop();
                    var scrollLeft = $(window).scrollLeft();
                    contextMenu.jqxMenu('open', parseInt(event.args.originalEvent.clientX) + 5 + scrollLeft, parseInt(event.args.originalEvent.clientY) + 5 + scrollTop);
                                return false;
                    }
                });

                $('#gridMenu_~gridID').on('itemclick', function (event) {
                            var args = event.args;
                    var rowindex = $('#~gridID').jqxGrid('getselectedrowindex');
                            if ($.trim($(args).text()) == '编辑') {
                                editrow = rowindex;
                                var offset = $('#~gridID').offset(); 
                            }
                            else if ($.trim($(args).text()) == '删除') {
                                var rowid = $('#~gridID').jqxGrid('getrowid', rowindex);
                                $('#~gridID').jqxGrid('deleterow', rowid);
                              }
                        });

             ";
            scriptCreateGrid = PageBaseModel.GetStringWithVar(scriptCreateGrid, varsG);

            string scriptSetFlds = ""; string scriptCheckRowEmpty = "";
            foreach (KeyValuePair<string, DataItem> key in Columns)
            {
                DataItem dataItem = key.Value;
                //if (dataItem.necessary)
                {
                    if (scriptCheckRowEmpty != "") scriptCheckRowEmpty += " && ";
                    {
                        scriptCheckRowEmpty += " row1." + key.Key + "==''";
                    }
                }

                scriptSetFlds += "row." + key.Key + "=row1." + key.Key + ";";
                if (dataItem.Label != "" && dataItem.LabelInDB)
                {
                    scriptSetFlds += "row." + dataItem.LabelFld + "=row1." + dataItem.LabelFld + ";";
                }
            }
            scriptSaveGrid = @" 
if(true){
var gridRows=$('#~gridID').jqxGrid('getrows');
var saveRows=new Array();
for(var i=0;i<gridRows.length;i++)
{
    var row1=gridRows[i];
    if(~scriptCheckRowEmpty){
       continue;
    }
   var row={};
   
   ~scriptSetFlds
    saveRows.push(row);
}
formFlds.~gridID=saveRows;

}

            ";
            varsG = new Dictionary<string, string>();
            varsG["gridID"] = gridID;
            varsG["scriptSetFlds"] = scriptSetFlds;
            varsG["scriptCheckRowEmpty"] = scriptCheckRowEmpty;
            scriptSaveGrid = PageBaseModel.GetStringWithVar(scriptSaveGrid, varsG);
        }
    }
    public class PageVoucherModel<CROW> : PageBaseModel where CROW : CwRowBase, new()
    {
        public string sheet_id = "";
        public string SheetRowsJson = "";
        public string ItemsInfoJson = "{}";
        public SheetCwBase<CROW> Sheet = null;
        public Dictionary<string, VoucherGrid<CROW>> Grids = new Dictionary<string, VoucherGrid<CROW>>();
        public string m_selectFromSQL = "";

        public string m_idFld = "", m_nameFld = "", m_tableName = "";
        public new SheetPartialViewModel PartialViewModel;

        public bool IdFldIsSerial = true;
        public JObject record = new JObject();

        // public string m_showFormScript = "", m_getDataItemsScript  = "";
        public string m_saveCloseScript = "";
        public string m_createGridScript = ""; public string m_saveGridScript = "";
        public string GetFormDataScript = "";
        public bool m_bNewRecord = false;
        public Dictionary<string, dynamic> ItemsInfo = new Dictionary<string, dynamic>();
        public PageVoucherModel(MenuId pageMenuID) : base(pageMenuID)
        {

        }
        // public virtual async Task OnDataItemsGotFromSheet(CMySbCommand cmd)
        //  {

        //  }
        public async Task GetJavaScripts(CMySbCommand cmd, SheetCwBase<CROW> sheet, bool bGetFldAreaCtrls = false)
        {

            GetDataItemsFromSheet(cmd, sheet);
            //   await OnDataItemsGotFromSheet(cmd);
            await GetScriptsForDataItems(cmd, bGetFldAreaCtrls);

            if (Grids != null)
            {
                foreach (KeyValuePair<string, VoucherGrid<CROW>> kp in Grids)
                {
                    await kp.Value.GetScript(cmd, this, kp.Key, sheet);
                    m_createGridScript += kp.Value.scriptCreateGrid;
                    m_saveGridScript += kp.Value.scriptSaveGrid;
                }
                m_getDataItemsScript += m_saveGridScript;
            }

            string msgHead = this.GetType().FullName.Replace("Model", "").Split(".").Last();

            GetFormDataScript = @$"
                function getFormData(){{
                   var formFlds = {{operKey:'{OperKey}',IsFromWeb:true}};                   
                   {m_getDataItemsScript}
                   return formFlds;
                }}
            ";

            var vars = new Dictionary<string, string>();
            vars["GetFormDataScript"] = GetFormDataScript;
            vars["m_bNewRecord"] = m_bNewRecord.ToString().ToLower(); vars["msgHead"] = msgHead; vars["m_getDataItemsScript "] = m_getDataItemsScript;
            vars["operKey"] = OperKey;
            m_saveCloseScript = PageBaseModel.GetStringWithVar(m_saveCloseScript, vars);
        }
        public bool GetDataItemsFromSheet(CMySbCommand cmd, SheetCwBase<CROW> sheet)
        {
            PropertyInfo[] props = sheet.GetType().GetProperties();
            foreach (KeyValuePair<string, DataItem> key in DataItems)
            {
                DataItem dataItem = key.Value;
                if (sheet.approve_time != "") dataItem.Readonly = true;
                foreach (PropertyInfo prop in props)
                {
                    if (prop.Name.ToLower() == key.Key.ToLower())
                    {
                        // metProp = prop;
                        object p = prop.GetValue(sheet);
                        string propValue = "";
                        if (p != null) propValue = p.ToString();

                        if (propValue != "" || sheet.sheet_id != "")
                        {
                            dataItem.Value = propValue;
                        }

                    }
                    else if (prop.Name.ToLower() == dataItem.LabelFld.ToLower())
                    {
                        // metProp = prop;
                        object p = prop.GetValue(sheet);
                        string propValue = "";
                        if (p != null) propValue = p.ToString();

                        if (propValue != "" || sheet.sheet_id != "")
                        {
                            dataItem.Label = propValue;
                        }
                    }
                    //dataItem.LabelFld
                }
            }
            return true;
        }
        //public virtual async Task<SheetCwBase<TROW>> GetSheetByOrder(CMySbCommand cmd, string operKey, string order_sheet_id)
        //{
        //    return null;
        //}SheetBase<TROW> sheet
        public async Task InitGet(CMySbCommand cmd, SheetCwBase<CROW> sheet)
        {
            GetOperKey();
            sheet_id = CPubVars.RequestV(Request, "sheet_id");
            var startDay = CPubVars.RequestV(Request, "startDay");
            var endDay = CPubVars.RequestV(Request, "endDay");

            if (sheet_id == "" || sheet_id == null)
            {
                InitDataItemsFromRequest();
            }

            if (this.Sheet == null)
            {
                this.Sheet = sheet;
                await Sheet.Load(cmd, company_id, sheet_id);
            }

            await GetJavaScripts(cmd, this.Sheet, true);
            SheetRowsJson = Newtonsoft.Json.JsonConvert.SerializeObject(this.Sheet.SheetRows);

            PartialViewModel = new SheetPartialViewModel
            {
                Version = this.Version,
                OperKey = this.OperKey,
                OperName = this.OperName,
                m_saveCloseScript = this.m_saveCloseScript,
                m_createGridScript = this.m_createGridScript,
                m_saveGridScript = this.m_saveGridScript,
                GetFormDataScript = this.GetFormDataScript,
                SheetRowsJson = this.SheetRowsJson,
                ItemsInfoJson = this.ItemsInfoJson,
                m_showFormScript = this.m_showFormScript,
                JsonOperRights = JsonOperRights,
                JsonCompanySetting = JsonCompanySetting,
                JsonPageSetting = JsonPageSetting,
                JsonColumnsWidth = JsonColumnsWidth,
                PageName = PageName,
                StartDay =startDay,
                EndDay = endDay
                
            };




        }
        /*
        public async Task GetItemsInfoBySheetInfo_old(List<string> item_ids, string supcust_id,string branch_id)
        {
            var items_id = "";

            foreach (string item_id in item_ids)
            {
                if (!("," + items_id + ",").Contains("," + item_id + ","))
                {
                    if (items_id != "") items_id += ",";
                    items_id += item_id;
                }
            }

            if (items_id != "")
            {
                //ItemsInfoJson 存储了单据商品列表中每一行商品的单位信息，用于在点击单位后展示对应的价格,在_SheetHead中有用到ItemsInfoJson
                var itemsInfo = await GetItemsInfo(company_id, items_id, supcust_id,branch_id);
                if (itemsInfo != null)
                {
                    dynamic d = itemsInfo.Value;
                    this.ItemsInfoJson = JsonConvert.SerializeObject(d.items);
                    PartialViewModel.ItemsInfoJson = this.ItemsInfoJson;
                }
            }
        }
        */
        /*
        public async Task GetItemsInfoBySheetInfo(string companyID, string items_id, dynamic otherInfo)
        {

            //ItemsInfoJson 存储了单据商品列表中每一行商品的单位信息，用于在点击单位后展示对应的价格,在_SheetHead中有用到ItemsInfoJson
            if (items_id.IsInvalid()) return;
            var itemsInfo = await GetItemsInfo(companyID, items_id, otherInfo);
            if (itemsInfo != null)
            {
                dynamic d = itemsInfo.Value;
                this.ItemsInfoJson = JsonConvert.SerializeObject(d.items);
                PartialViewModel.ItemsInfoJson = this.ItemsInfoJson;
            }

        }*/
        public string GetRealSQL(string sql)
        {
            sql = GetRealSQL(sql, company_id, m_tableName, DataItems[m_idFld].Value);
            return sql;
        }

        public virtual async Task<JsonResult> GetItemsInfo(string companyID, string items_id, dynamic otherInfo)
        {
            return null;
        }

    }
    /*
    static class StaticExtendClass
    {
        public static bool IsGenericSubclassOf(this Type type, Type superType)
        {
            if (type.BaseType != null
                && !type.BaseType.Equals(typeof(object))
                && type.BaseType.IsGenericType)
            {
                if (type.BaseType.GetGenericTypeDefinition().Equals(superType))
                {
                    return true;
                }
                return type.BaseType.IsGenericSubclassOf(superType);
            }

            return false;
        }
    }*/
}
