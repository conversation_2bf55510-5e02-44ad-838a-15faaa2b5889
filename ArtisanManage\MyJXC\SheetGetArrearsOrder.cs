﻿using ArtisanManage.Models;
using ArtisanManage.MyCW;
using ArtisanManage.Pages.CwPages;
using ArtisanManage.Pages.WeChat.SheetPages;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{

    public class SheetRowArrearsOrder : SheetRowBase
    {
        [SaveToDB][FromFld] public string mm_sheet_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string mm_sheet_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string order_sheet_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string mm_sheet_time { get; set; }
        public string mm_sheet_date
        {
            get
            {
                return CPubVars.GetDateTextNoTime(mm_sheet_time);
            }
        }
        [FromFld(LOAD_PURPOSE.SHOW)] public string mm_make_brief { get; set; }
        [SaveToDB][FromFld] public string m_sheet_type { get; set; }
        public string m_sheet_type_name { get { return m_sheet_type.Replace("CT", "采购退货单").Replace("X", "销售单").Replace("T", "退货单").Replace("DH", "定货会").Replace("YS", "预收款单").Replace("ZC", "支出单").Replace("YF", "预付").Replace("CG", "采购").Replace("SR", "其他收入"); } }
        [SaveToDB][FromFld] public decimal sheet_amount { get; set; }
        [SaveToDB][FromFld] public decimal paid_amount { get; set; }
        [SaveToDB][FromFld] public decimal disc_amount { get; set; }
        [SaveToDB][FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB][FromFld] public decimal now_disc_amount { get; set; }
        [SaveToDB][FromFld] public decimal left_amount { get; set; }
        [SaveToDB][FromFld] public override string remark { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string arrears_order_sheet_id { get; set; }

        // 门店ID supcust_id 门店名称 sup_name
        [FromFld(LOAD_PURPOSE.SHOW)] public string supcust_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_name { get; set; } = "";
        // 结算单位Id acct_id 结算单位名称 acct_sup_name
        [FromFld(LOAD_PURPOSE.SHOW)] public string acct_id { get; set; }
       // [FromFld(LOAD_PURPOSE.SHOW)] public string acct_sup_name { get; set; } = "";


    }

    public enum SHEET_GET_ARREARS_ORDER
    {
        EMPTY,
        NOT_APPROVED, //对账中
        IS_APPROVED,//待收款
        

    }
    public class SheetGetArrearsOrder : SheetBase<SheetRowArrearsOrder>
    {
        public List<string> appendixPhotos { get; set; } = new List<string>();
        public string appendix_photos { get; set; } = "";
        [SaveToDB][FromFld] public string visit_id { get; set; } = "";
        [SaveToDB][FromFld] public string supcust_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string sup_name { get; set; } = "";
        [SaveToDB][FromFld] public override int money_inout_flag { get; set; }
        [SaveToDB][FromFld] public decimal sheet_amount { get; set; }
        [SaveToDB][FromFld] public decimal paid_amount { get; set; }
        [SaveToDB][FromFld] public decimal disc_amount { get; set; }
        [SaveToDB][FromFld] public decimal now_pay_amount { get; set; }
        [SaveToDB][FromFld] public decimal now_disc_amount { get; set; }
        [SaveToDB][FromFld] public decimal left_amount { get; set; }
        // 申请标记位
        [SaveToDB][FromFld] public string apply_flag { get; set; } = "";

        // 指待付款的金额
        [SaveToDB][FromFld] public decimal real_left_amount { get; set; }
        [SaveToDB][FromFld] public string payway1_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway1_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway1_amount { get; set; }
        [SaveToDB][FromFld] public string payway2_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway2_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway2_amount { get; set; }
        [SaveToDB][FromFld] public string payway3_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string payway3_name { get; set; } = "";
        [SaveToDB][FromFld] public decimal payway3_amount { get; set; }

        // [SaveToDB][FromFld] public string order_status { get; set; } = "";

        [SaveToDB][FromFld] public string getter_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string getter_name { get; set; } = "";
        [SaveToDB][FromFld] public override SHEET_TYPE sheet_type { get; set; }
       
        internal string mmSheetTable = "";
        [SaveToDB]
        [FromFld]
        public virtual string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();
                if (appendix_photos != "" && appendix_photos != "[]")
                {
                    sheetAttribute.Add("appendixPhotos", appendix_photos);
                }


                string s = "";
                if (sheetAttribute.Count > 0) s = Newtonsoft.Json.JsonConvert.SerializeObject(sheetAttribute);

                return s;
            }
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    dynamic sheetAttr = JsonConvert.DeserializeObject(value);

                    if (sheetAttr.appendixPhotos != null)
                    {
                        this.appendix_photos = sheetAttr.appendixPhotos;
                    }
                }
            }
        }
        [SaveToDB][FromFld] public override string is_imported { get; set; } = "";
        public SheetGetArrearsOrder(SHEET_GET_ARREARS_ORDER sheetGetArrearsOrder, LOAD_PURPOSE loadPurpose) : base("sheet_get_arrears_order_main", "sheet_get_arrears_order_detail", loadPurpose)
        {
            // sheet_type = sheetGetArrearsOrder == SHEET_GET_ARREARS_ORDER.NOT_GET ? SHEET_TYPE.SHEET_PAY_MONEY : SHEET_TYPE.SHEET_GET_MONEY;
            
            sheet_type = SHEET_TYPE.SHEET_GET_MONEY_ORDER; // 待审核和待收款都是一种单据类型
            ConstructFun();
        }
        public SheetGetArrearsOrder(LOAD_PURPOSE loadPurpose) : base("sheet_get_arrears_order_main", "sheet_get_arrears_order_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_GET_MONEY_ORDER;
            ConstructFun();
        }

        public SheetGetArrearsOrder() : base("sheet_get_arrears_order_main", "sheet_get_arrears_order_detail", LOAD_PURPOSE.SHOW)
        {
            sheet_type = SHEET_TYPE.SHEET_GET_MONEY_ORDER;
            ConstructFun();
        }
        private void ConstructFun()
        {
            mmSheetTable = "sheet_sale_main";
            // if (sheet_type == SHEET_TYPE.SHEET_PAY_MONEY) mmSheetTable = "sheet_buy_main";
            // money_inout_flag = sheet_type == SHEET_TYPE.SHEET_PAY_MONEY ? -1 : 1;

            money_inout_flag =  1;
            if (LoadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @" left join info_supcust c on t.supcust_id=c.supcust_id and c.company_id=~COMPANY_ID
                                  left join (select oper_id,oper_name as getter_name from info_operator where company_id=~COMPANY_ID) getter on t.getter_id=getter.oper_id
                                  left join (select oper_id,oper_name as maker_name from info_operator where company_id=~COMPANY_ID) maker on t.maker_id=maker.oper_id
                                  left join  (select oper_id,oper_name as approver_name from info_operator where company_id=~COMPANY_ID) approver on t.approver_id=approver.oper_id
                                  left join (select sub_id,sub_name as payway1_name from cw_subject where company_id=~COMPANY_ID) pw1 on t.payway1_id=pw1.sub_id
                                  left join (select sub_id,sub_name as payway2_name from cw_subject where company_id=~COMPANY_ID) pw2 on t.payway2_id=pw2.sub_id
                                  left join (select sub_id,sub_name as payway3_name from cw_subject where company_id=~COMPANY_ID) pw3 on t.payway3_id=pw3.sub_id
              ";
                DetailLeftJoin = @$"
left join
(
   select a.*,b.sup_name,b.acct_id from 
            (
                        select supcust_id, sheet_id,sheet_no as mm_sheet_no,order_sheet_no,supcust_id as mm_supcust_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,make_brief mm_make_brief,total_amount as mm_sheet_amount,money_inout_flag as mm_money_inout_flag,arrears_order_sheet_id
                           from sheet_sale_main sm 
                           left join 
                           (
                              select sheet_id order_sheet_id,sheet_no order_sheet_no from sheet_sale_order_main 
                              where company_id=~COMPANY_ID 
                           ) om on sm.order_sheet_id=om.order_sheet_id 
                           where sm.company_id=~COMPANY_ID and sm.sheet_id in (VAR_sale_sheets_id) and (sheet_type = 'X' OR sheet_type = 'T')
                           UNION
                              select supcust_id,sheet_id,sheet_no as mm_sheet_no,null order_sheet_no,supcust_id as mm_supcust_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,make_brief mm_make_brief,total_amount as mm_sheet_amount,money_inout_flag as mm_money_inout_flag,arrears_order_sheet_id from sheet_prepay
                              where company_id=~COMPANY_ID and sheet_id in (VAR_prepay_sheets_id) and sheet_type = 'YS' 
                           UNION
                              select supcust_id,sheet_id,sheet_no as mm_sheet_no,null order_sheet_no,supcust_id as mm_supcust_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,make_brief mm_make_brief,total_amount as mm_sheet_amount,money_inout_flag as mm_money_inout_flag,arrears_order_sheet_id from sheet_fee_out_main
                              where company_id=~company_id and sheet_id in (VAR_fee_sheets_id) and sheet_type = 'ZC'
            ) a left join (select *,case when acct_cust_id is null then supcust_id else acct_cust_id end as acct_id from info_supcust) b on a.supcust_id=b.supcust_id and b.company_id=~company_id
)
s on t.mm_sheet_id=s.sheet_id and t.m_sheet_type = s.m_sheet_type
                            
                    ";
            }
        }

        public override async Task BeforeLoad(CMySbCommand cmd, string companyID, string sheetID)
        {
            List<System.Dynamic.ExpandoObject> lstSheets = await CDbDealer.GetRecordsFromSQLAsync($"select mm_sheet_id,m_sheet_type from sheet_get_arrears_order_detail where company_id={companyID} and sheet_id in ({sheetID});", cmd);
            Variables = new Dictionary<string, string>() {
                { "sale_sheets_id","null" },{ "prepay_sheets_id","null" },{ "fee_sheets_id","null" }

            };

            foreach (dynamic sht in lstSheets)
            {
                string mmSheetsID = "";
                string key = "";
                switch (sht.m_sheet_type)
                {
                    case "X":
                    case "T":
                        key = "sale_sheets_id"; break;
                    case "YS":
                        key = "prepay_sheets_id"; break;
                    case "SR":
                    case "ZC":
                        key = "fee_sheets_id"; break;
                }
                if (this.Variables.ContainsKey(key))
                {
                    mmSheetsID = this.Variables[key];
                }
                if (mmSheetsID != "") mmSheetsID += ",";
                mmSheetsID += sht.mm_sheet_id;
                this.Variables[key] = mmSheetsID;
            }
        }
        protected override void InitForSave()
        {
            base.InitForSave();
            if (getter_id == "") getter_id = OperID;
            // if (approver_id == "") approver_id = OperID;
            // 由于开启了对账单金额编辑，这里不能够继续这样写
            // real_left_amount = now_pay_amount < 0 ? now_pay_amount * -1 : now_pay_amount;

            real_left_amount = 0;
            foreach (SheetRowArrearsOrder row in SheetRows)
            {
                // 把每张业务单据的单据金额加起来得到审核时的对账单欠款总额
                real_left_amount += row.sheet_amount - row.paid_amount - row.disc_amount;
            }
            apply_flag = apply_flag ==  "1" ? "1" : "";
        }

        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            if (supcust_id == "") return "必须指定客户";
            //if (getter_id == "" && IsFromWeb) return "必须指定业务员";
            if (payway1_id == "") return "必须指定支付方式";
            if (SheetRows.Count == 0) return "单据不存在明细行";
            if (Math.Abs(now_pay_amount - payway1_amount - payway2_amount - payway3_amount) > 0.05m)
            {
                string payways = payway1_name;
                if (payway2_name != "")
                {
                    payways += ",";
                    payways += payway2_name;
                }
                if (payway3_name != "")
                {
                    payways += ",";
                    payways += payway3_name;
                }
                return payways + "支付的金额和本次应该支付的金额不一致";
            }
            decimal total_now_pay_amount = 0;
            decimal total_now_disc_amount = 0;
            decimal total_now_left_amount = 0;
            foreach (SheetRowArrearsOrder row in SheetRows)
            {
                total_now_pay_amount += row.now_pay_amount;
                total_now_disc_amount += row.now_disc_amount;
                total_now_left_amount += row.left_amount;
                var row_left_amount = row.sheet_amount - row.paid_amount - row.disc_amount - row.now_disc_amount - row.now_pay_amount;
                if (Math.Abs(row_left_amount - row.left_amount) > 0.1m)
                    return $"单据{row.mm_sheet_no}的尚欠金额与实际尚欠不等";
                //if (!IsImported && row.now_pay_amount / row.sheet_amount < 0 )
                //{
                //   if( row.sheet_amount > 0)  return $"单据{row.mm_sheet_no}的本次支付金额必须是正数";
                //   else return $"单据{row.mm_sheet_no}的本次支付金额必须是负数";
                //}
                /*if (!IsImported && row.now_disc_amount * row.sheet_amount < 0)
                {
                   if (row.sheet_amount > 0) return $"单据{row.mm_sheet_no}的本次优惠金额必须是正数"; 
                   else return $"单据{row.mm_sheet_no}的本次优惠金额必须是负数";
                }*/
            }

            if (Math.Abs(total_now_pay_amount - now_pay_amount) > 0.1m) return "明细行本次支付合计与总的本次支付不等";

            if (Math.Abs(total_now_disc_amount - now_disc_amount) > 0.1m) return "明细行本次优惠合计与总的本次优惠不等";

            if (Math.Abs(total_now_left_amount - left_amount) > 0.1m) return "明细行尚欠合计与总的尚欠不等";


            string acct_id = ""; 
            string mmSheetTable;
            string markSql;
            string mark;
            foreach (SheetRowArrearsOrder row in SheetRows)
            
            {   // 修改要求允许重复对账
                // mmSheetTable = GetTableFromSheetType(row.m_sheet_type);
                // markSql = $"select arrears_order_sheet_id from {mmSheetTable} where company_id={company_id} and sheet_id={row.mm_sheet_id}";
                // dynamic ret = await CDbDealer.Get1RecordFromSQLAsync(markSql, cmd);
                // mark = (string)ret.arrears_order_sheet_id;
                // if (sheet_id != "" && mark.IsValid() && mark != sheet_id)
                // {
                //     return $"第{row.row_index}行单据已被标记对账，不能重复对账";
                // }
                if (acct_id == "")
                {
                    acct_id = row.acct_id;
                }
                else
                {
                    if (acct_id != row.acct_id) return "不能对账来自多个结算单位的单据";
                }
                
            }
            return "OK";
           
        }

        protected override async Task<string> CheckSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSheetValid(cmd);
            if (check != "OK") return check;


            List<SheetRowArrearsOrder> lstToRemove = new List<SheetRowArrearsOrder>();
            sheet_amount = 0;
            paid_amount = 0;
            disc_amount = 0;
            left_amount = 0;

            foreach (SheetRowArrearsOrder row in SheetRows)
            {
                if (row.now_disc_amount == 0 && row.now_pay_amount == 0 && row.left_amount == 0)
                {
                    lstToRemove.Add(row);
                }
                else
                {
                    paid_amount += row.paid_amount;
                    disc_amount += row.disc_amount;
                    left_amount += row.left_amount;
                    sheet_amount += row.sheet_amount;
                }
            }

            foreach (var row in lstToRemove)
            {
                SheetRows.Remove(row);
            }

            return "OK";
        }

        protected override async Task<string> CheckForRed(CMySbCommand cmd)
        {
            // 新增逻辑：对账单已转收款单保存之后则不可红冲；对账单明细单据如果在对账审核之后被收款则对账单不可以红冲
            string sql = $"select sheet_id from sheet_get_arrears_main where company_id = {company_id} and order_sheet_id = {this.sheet_id} and red_flag is null";
            dynamic arrearsSheet = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (arrearsSheet != null) return "对账单已经转成收款单，如需红冲对账单请先删除或红冲关联收款单";
            foreach(SheetRowArrearsOrder row in SheetRows)
            {
                // 检查每个明细单据有无对账单审核后的收款记录
                sql = @$"select m.sheet_id from sheet_get_arrears_main m left join sheet_get_arrears_detail d on m.sheet_id = d.sheet_id and m.company_id = d.company_id
where d.mm_sheet_id = {row.mm_sheet_id} and d.m_sheet_type = '{row.m_sheet_type}' and m.company_id = {company_id} and m.red_flag is null and m.approve_time is not null
and m.approve_time > '{this.approve_time}'";
                arrearsSheet = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (arrearsSheet != null) return $"第{row.row_index}行单据【{row.mm_sheet_no}】在当前对账单审核后有收款记录，无法红冲当前对账单";
            }
            return await CheckForRed_MoneySheet(cmd);
        }

        class CInfoForApprove : CInfoForApproveBase
        {
            //public string ArrearBalance = "";
            public string subID = "";
            public float ChangeBal = 0;
            public float Balance = 0;
            public float paidAmount = 0;
            public float discAmount = 0;
            //public string ErrorMsg = "";//基类里已有ErrMsg,这里不需要再定义一个,两个就容易混淆，造成BUG
            //  public bool BalanceExist = false;

            public Dictionary<string, SellerArrearBalance> SellersBalance = new Dictionary<string, SellerArrearBalance>();
            public List<Subject> PrepaySubjects = new List<Subject>();


        }

        protected class Subject
        {
            public string sub_id { get; set; }
            public string sub_name { get; set; }
            public string balance { get; set; }
            public string sub_type { get; set; }
        }

        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            base.GetInfoForApprove_SetQQ(QQ);
            string sql;
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            //sql = GetSqlForArrearsQQ(supcust_id, getter_id);
            //QQ.Enqueue("arrear_balance", sql);

        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed )
        {
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
           
            
        }
        class SellerArrearBalance
        {
            public string seller_name = "";
            public decimal max_arrears = 0m;
            public decimal balance = 0m;
            public decimal change_balance = 0m;
        }

        private string GetTableFromSheetType(string sheetType)
        {
            string mmSheetTable = "";
            switch (sheetType)
            {
                case "ZC": mmSheetTable = "sheet_fee_out_main"; break;
                case "X": case "T": mmSheetTable = "sheet_sale_main"; break;
                case "YS": mmSheetTable = "sheet_prepay"; break;

            }
            return mmSheetTable;
        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";
            foreach (var row in SheetRows)
            {
                
                string mmSheetTable = GetTableFromSheetType(row.m_sheet_type);
                // 更新标记位
                if (red_flag.IsValid() || (apply_flag.IsInvalid() && approver_id == ""))
                {
                    // 红冲和撤销时取消标记 red_flag.isValid() = true || (apply_flag = null && approver_id = null )
                    sql += $"update {mmSheetTable} set arrears_order_sheet_id = null where company_id={company_id} and sheet_id={row.mm_sheet_id} ;";
                    row.arrears_order_sheet_id = null;
                }
                else
                {
                    // 申请和审核时添加标记 red_flag.isValid() = false || (apply_flag = 1 || approver_id != null )
                    sql += $"update {mmSheetTable} set arrears_order_sheet_id = {sheetID} where company_id={company_id} and sheet_id={row.mm_sheet_id} ;";
                    row.arrears_order_sheet_id = sheetID;
                }

                

                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();


            }
            if (approver_id != "" && red_flag.IsInvalid())
            {
                // 跳过审核直接申请了，就把申请标记位也修改为已标记“1”
                apply_flag = "1";
            }
        }
        
        public async Task<SheetGetArrears> ToGetArrearsSheet(string operKey, CMySbCommand cmd)
        {
            SheetGetArrears getArrearsSheet = JsonConvert.DeserializeObject<SheetGetArrears>(JsonConvert.SerializeObject(this));
            getArrearsSheet.sheet_type = SHEET_TYPE.SHEET_GET_MONEY;
            //对账单要恢复
            getArrearsSheet.order_sheet_id = this.sheet_id;
            getArrearsSheet.order_sheet_no = this.sheet_no;
            getArrearsSheet.OperID = OperID;
            getArrearsSheet.sheet_id = "";
            getArrearsSheet.sheet_no = "";
            getArrearsSheet.approver_id = "";
            getArrearsSheet.approver_name = "";
            getArrearsSheet.approve_time = "";
            getArrearsSheet.approve_brief = "";
            getArrearsSheet.make_time = "";
            getArrearsSheet.maker_id = "";
            getArrearsSheet.maker_name = "";
            getArrearsSheet.happen_time = "";
            getArrearsSheet.OperKey = operKey;
            getArrearsSheet.SheetRows = new List<SheetRowArrears>();
            decimal real_paid_amount = 0;
            decimal real_disc_amount = 0;
            decimal real_left_amount = 0;
            foreach(dynamic row in this.SheetRows)
            {
                //row.m_sheet_type = "";
                var orderRow = JsonConvert.DeserializeObject<dynamic>(JsonConvert.SerializeObject(row));
                SheetRowArrears newRow = JsonConvert.DeserializeObject<SheetRowArrears>(JsonConvert.SerializeObject(orderRow));
                string mmSheetTable = GetTableFromSheetType(row.m_sheet_type);
                string mmSheetInfoSql = $"select sheet_id,(money_inout_flag*total_amount) as total_amount,paid_amount* money_inout_flag as paid_amount,disc_amount* money_inout_flag as disc_amount from {mmSheetTable} where sheet_id = {row.mm_sheet_id} and company_id = {company_id}";
                dynamic mmSheetInfo = await CDbDealer.Get1RecordFromSQLAsync(mmSheetInfoSql,cmd);
                decimal paid_amount = decimal.Parse(mmSheetInfo.paid_amount);
                decimal disc_amount = decimal.Parse(mmSheetInfo.disc_amount);
                decimal total_amount = decimal.Parse(mmSheetInfo.total_amount);

                // 把对账单填写的东西带过来，如果不带过来就把这三行删了
                paid_amount = row.paid_amount;
                disc_amount = row.disc_amount;
                total_amount = row.sheet_amount;

                // newRow.now_pay_amount = 0;
                // newRow.now_disc_amount = 0;
                newRow.now_pay_amount = row.now_pay_amount;
                newRow.now_disc_amount = row.now_disc_amount;
                

                real_disc_amount += disc_amount;
                real_paid_amount += paid_amount;
                real_left_amount += total_amount - disc_amount - paid_amount;
                newRow.shop_name = row.sup_name;
                newRow.paid_amount = paid_amount;
                newRow.disc_amount = disc_amount;
                newRow.left_amount = total_amount - disc_amount - paid_amount - newRow.now_disc_amount - newRow.now_pay_amount;
                
                getArrearsSheet.SheetRows.Add(newRow);
            }
            getArrearsSheet.paid_amount = real_paid_amount;
            getArrearsSheet.disc_amount = real_disc_amount;
           //  getArrearsSheet.payway1_amount = 0;
            getArrearsSheet.payway2_amount = 0;
            getArrearsSheet.payway3_amount = 0;
            // getArrearsSheet.now_pay_amount = 0;
            // getArrearsSheet.now_disc_amount = 0;
            // getArrearsSheet.left_amount = real_left_amount;
            getArrearsSheet.Init();

            return getArrearsSheet;
        }
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.supcust_id + "_" + this.total_amount.ToString() + "_" + this.payway1_id + "_" + this.payway2_id + "_" + this.payway3_id + "_" + this.make_brief;
            return res;
        }
        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting);

        }
    }
}
