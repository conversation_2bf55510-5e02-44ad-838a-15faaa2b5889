﻿@page
@model ArtisanManage.Pages.CwPages.ClosingCarryForwardModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>ClosingCarryForward</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>

    <style>
        * {
            font-family: "微软雅黑"
        }

        [v-cloak] {
            display: none;
        }

        body {
        }

        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;
        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #dddddd;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }
        .tooltip_style {
            font-size: 18px;
        }

        #pages {
            width: 100%;
            height: 100vh;
            overflow-x: hidden;
            overflow-y: hidden;
            /*background-color: #f9f9f9;*/
        }
        
        .pages_head{
            width:100%;
            height:8vh;
        }

        .pages_period{
            width:30%;
            height:100%;
            float:left;
            font-size:18px;
            display:flex;
            justify-content:left;
            align-items:center;
            padding-left:20px;
        }

        .pages_operate{
            height:100%;
            float:right;
            display:flex;
            justify-content:end;
            align-items:center;
            padding-right:20px;
        }

        .pages_checkbox{
            padding-right:15px;
        }

        .pages_checkbox input{
            cursor: pointer;
        }

        .pages_main{
            width:100%;
            height:92vh;
            padding:20px;
        }

        .pages_box{
            width:280px;
            height:200px;
            border: 1px solid #c0c4cc;
            border-radius:10px;
            display:block;
            float:left;
            margin:15px;
            transition:all 0.3s;
        }

        .pages_box:hover{
            box-shadow: 1px 1px 5px rgba(0,0,0,0.3);
        }        
        
        .box_title{
            width:100%;
            height:50px;
            background-color: #dcdfe6;
            border-top-left-radius:10px;
            border-top-right-radius:10px;
        }

        .box_checkbox{
            width:60%;
            height:100%;
            float:left;
            display:flex;
            justify-content:left;
            align-items:center;
            padding-left:5px;
        }
        .box_checkbox input{
            margin-right:5px;
            cursor: pointer;
        }

        .box_edit{
            width:20%;
            height:100%;
            float:right;
            display:flex;
            justify-content:end;
            align-items:center;
            padding-right:10px;
        }

        .box_edit i{
            color:darkgray;
            transition:all 0.3s;
        }

        .box_edit i:hover{
            color:dimgray;
            cursor:pointer;
        }

        .box_body{
            width: 100%;
            height: 150px;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .box_body .box_balance{
            width:100%;
            height:50px;
            margin:0;
            padding:0;
            padding-top:25px;
        }

        .box_body .box_balance p{
            height:100%;
            margin:0;
            color: #293047;
            font-size: 40px;
            text-align: center;
        }

        .box_body .box_voucher{
            width:100%;
            height:25px;
            margin:0 auto;
        }

        .box_body .box_button{
            width:100%;
            height:40px;
            margin-bottom:10px;
        }

        .box_body .box_button button{
            display:flex;
            margin: 0 auto;
        }

        .pages_box:last-of-type{
            border: 2px dashed #c0c4cc;
            cursor:pointer;
            /*visibility:hidden;*/
        }

        .pages_box:last-of-type .box_title {
            background-color: #fff;
            color:darkgray;
        }

        .pages_box:last-of-type .box_checkbox input, .pages_box:last-of-type .box_edit{
            display:none;
        }

        .pages_box:last-of-type button{
            visibility:hidden;
        }

        .pages_box:last-of-type .box_checkbox label{
            padding-left:20px;
            color: darkgray;
        }

        .pages_box:last-child .box_body p{
            color: #c0c4cc;
            font-size:50px;
        }

        .el-dialog{
            margin-top:12vh !important;
        }

        .negaBalance{
            color:#ea5550 !important;
        }

        .voucher_a{
            width: fit-content;
            height: fit-content;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            margin:auto;
        }

        .icon-checked{
            color: #79c06e;
        }
        .icon-unchecked{
            color:#fdd35c;
        }

        .arrangeVoBtn{
            color:#006eb0;
            margin-left:20px;
        }
        .arrangeVoBtn:hover{
            color:#0095d9;
        }
        .arrangeVoLost{
            display:flex;
            margin-top:20px;
            margin-left:20px;
        }
        
        .el-message {
            z-index: 3000 !important; /*el-message-box的遮罩层z-index从2000开始每次点击自增*/
        }

        .amortizeInput{
            width:30%;
        }
    </style>

</head>
<body>
    <div id="root" v-cloak>
        <div id="pages">
            <div class="pages_head">
                <div class="pages_period">会计期间：{{period}}</div>
                <div class="pages_operate">
                    <div class="pages_checkbox">
                        <input type="checkbox" @@click="selectAll()" ref="selAll" :style=`pointer-events:${disabledOperate==true?'none':'auto'};` />
                        <label>全选</label>
                    </div>
                    <div class="pages_btns">
                        <el-button type="info" @@click="createVoucherAll()" :disabled="disabledOperate">生成凭证</el-button>
                        <el-button type="info" plain @@click="reload()" :disabled="disabledOperate">刷新</el-button>
                        <el-button type="danger" @@click="checkBeforeClose()" :disabled="disabledOperate">结账</el-button>
                        <el-button type="danger" plain @@click="unclose()" :disabled="disabledOperate">反结账</el-button>
                    </div>
                </div>
            </div>
            <div class="pages_main">
                <div class="pages_box" v-for="box in boxData" :key="box.id" ref="box" v-show="box.visible">
                    <div class="box_title">
                        <div class="box_checkbox">
                            <input type="checkbox" v-model="box.checked" @@change="checkAll()" :style=`pointer-events:${disabledOperate==true?'none':'auto'};` />
                            <label>{{box.name}}</label>
                        </div>
                        <div class="box_edit">
                            <i class="el-icon-edit" @@click="showTemplate(box)" :style=`pointer-events:${disabledOperate==true?'none':'auto'};` ></i>
                        </div>
                    </div>
                    <div class="box_body">
                        <div class="box_balance" ref="balance"><p>{{box.balance}}</p></div>
                        <div class="box_voucher"><a ref="voucher_a"></a></div>
                        <div class="box_button">
                            <el-button :plain="box.isBtnPlain" type="info" @@click="createVoucher(box)" :disabled="disabledOperate">{{box.btnText}}</el-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 结转销售成本模板 -->
            <el-dialog title="结转销售成本" :visible.sync="templateSaleCostVisible" width="60%">
                <el-table :data="templateTable">
                    <el-table-column property="remark" label="摘要" width="150"></el-table-column>
                    <el-table-column property="sbj_code" label="编码" width="150"></el-table-column>
                    <el-table-column property="sbj_name" label="科目" width="200"></el-table-column>
                    <el-table-column property="assisters_name" label="辅助核算" width="250"></el-table-column>
                    <el-table-column property="dir_debit" label="借方"></el-table-column>
                    <el-table-column property="dir_credit" label="贷方"></el-table-column>
                </el-table>
                <span style="padding-top:20px;display:block;">成本计价方式：{{boxData[0].costType}}</span>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @@click="templateSaleCostVisible = false">确 定</el-button>
                </span>
            </el-dialog>

            <!-- 结转摊销待摊费用模板 -->
            <el-dialog title="摊销待摊费用" :visible.sync="templateAmortizeVisible">
                <el-form label-width="180px" :model="templateForm" label-position="left">
                    <el-form-item label="长期待摊费用余额：">
                        <el-input v-model="templateForm.leftAmt" type="number" class="amortizeInput" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="上次摊销金额：">
                        <el-input v-model="templateForm.preAmt" type="number" class="amortizeInput" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="请输入本次摊销金额：">
                        <el-input v-model="templateForm.thisAmt" type="number" class="amortizeInput" min="0" @@change="setAmortizeAmount()"></el-input>
                    </el-form-item>
                </el-form>                
                <el-table :data="templateTable">
                    <el-table-column property="remark" label="摘要" width="150"></el-table-column>
                    <el-table-column property="sbj_code" label="编码" width="150"></el-table-column>
                    <el-table-column property="sbj_name" label="科目" width="200"></el-table-column>
                    <el-table-column property="assisters_name" label="辅助核算" width="200"></el-table-column>
                    <el-table-column property="dir_debit" label="借方"></el-table-column>
                    <el-table-column property="dir_credit" label="贷方"></el-table-column>
                </el-table>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @@click="templateAmortizeVisible = false;boxData[1].balance=templateForm.thisAmt;">确 定</el-button>
                </span>
            </el-dialog>

            <!-- 结转损益模板 -->
            <el-dialog title="结转损益" :visible.sync="templatePandLVisible" width="60%">
                <el-table :data="templateTable">
                    <el-table-column property="remark" label="摘要" width="150"></el-table-column>
                    <el-table-column property="sbj_code" label="编码" width="150"></el-table-column>
                    <el-table-column property="sbj_name" label="科目" width="200"></el-table-column>
                    <el-table-column property="assisters_name" label="辅助核算" width="250"></el-table-column>
                    <el-table-column property="dir_debit" label="借方"></el-table-column>
                    <el-table-column property="dir_credit" label="贷方"></el-table-column>
                </el-table>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @@click="templatePandLVisible = false">确 定</el-button>
                </span>
            </el-dialog>

            <!-- 年末结转利润模板 -->
            <el-dialog title="年末结转利润" :visible.sync="templatePandLYearEndVisible" width="60%">
                <el-table :data="templateTable">
                    <el-table-column property="remark" label="摘要" width="150"></el-table-column>
                    <el-table-column property="sbj_code" label="编码" width="150"></el-table-column>
                    <el-table-column property="sbj_name" label="科目" width="200"></el-table-column>
                    <el-table-column property="assisters_name" label="辅助核算" width="250"></el-table-column>
                    <el-table-column property="dir_debit" label="借方"></el-table-column>
                    <el-table-column property="dir_credit" label="贷方"></el-table-column>
                </el-table>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @@click="templatePandLYearEndVisible = false">确 定</el-button>
                </span>
            </el-dialog>

            <!-- 结账 -->
            <el-dialog title="结账前检查" :visible.sync="templateCloseVisible">
                <el-form>
                    <el-form-item v-for="cbc in checkItems" :key="cbc.id" :label="cbc.name" label-width="200px">
                        <i :class="(cbc.checked?'el-icon-circle-check':'el-icon-warning-outline')+' icon_i'" style="font-size:18px;"></i>
                        <el-button type="text" v-if="cbc.id==4" :disabled="cbc.checked" class="arrangeVoBtn" @@click="templateCloseVisible=false;templateVoucherNoVisible=true;">整理凭证</el-button>
                        <el-button type="text" v-if="cbc.id==5" :disabled="cbc.checked" class="arrangeVoBtn" @@click="templateCloseVisible=false;templateVoucherNoRepeatVisible=true;">整理凭证</el-button>
                        <el-button type="text" v-if="cbc.id==7" :disabled="cbc.checked" class="arrangeVoBtn" @@click="templateCloseVisible=false;templateAssistVoVisible=true;">检查辅助核算</el-button>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @@click="templateCloseVisible = false">取 消</el-button>
                    <el-button type="primary" @@click="closeMonth()">结 账</el-button>
                </span>
            </el-dialog>

            <!-- 整理凭证 -->
            <el-dialog title="整理凭证" :visible.sync="templateVoucherNoVisible">
                <el-radio-group  v-model="voucherOrderWays.selectId">
                    <el-radio v-for="w in voucherOrderWays.checkItems" :key="w.id" :label="w.id">{{w.name}}</el-radio>
                </el-radio-group>
                <br />
                <span v-if="voucherOrderWays.lostSheetNos!=''" class="arrangeVoLost">断号位：{{voucherOrderWays.lostSheetNos}}</span>
                <span slot="footer" class="dialog-footer">
                    <el-button @@click="templateVoucherNoVisible = false;checkBeforeClose()">取 消</el-button>
                    <el-button type="primary" @@click="putVoucherInOrder()">确 定</el-button>
                </span>
            </el-dialog>

            <!-- 整理凭证（重复） -->
            <el-dialog title="整理凭证" :visible.sync="templateVoucherNoRepeatVisible">
                <el-radio-group v-model="voucherOrderWays.selectId">
                    <el-radio v-for="w in voucherOrderWays.checkItems" :key="w.id" :label="w.id">{{w.name}}</el-radio>
                </el-radio-group>
                <br />
                <span v-if="voucherOrderWays.repeatSheetNos!=''" class="arrangeVoLost">重复位：{{voucherOrderWays.repeatSheetNos}}</span>
                <span slot="footer" class="dialog-footer">
                    <el-button @@click="templateVoucherNoRepeatVisible = false;checkBeforeClose()">取 消</el-button>
                    <el-button type="primary" @@click="putVoucherInOrder()">确 定</el-button>
                </span>
            </el-dialog>

            <!--检查未添加辅助项凭证-->
            <!--<el-dialog title="检查辅助核算" :visible.sync="templateAssistVoVisible">
                <div><span class="arrangeVoLost" style="display:inline-block;">缺少辅助核算的凭证：</span><div v-html="lostAssistVos" style="display:inline-block;"></div></div>
                <span slot="footer" class="dialog-footer">
                    <el-button @@click="templateAssistVoVisible = false;checkBeforeClose()">取 消</el-button>
                    <el-button type="primary" @@click="checkNoAssistVo()">批量查看</el-button>
                    <el-button type="primary" @@click="templateAssistVoVisible = false;checkBeforeClose()">确 定</el-button>
                </span>
            </el-dialog>-->
        </div>
    </div>
    <script type="text/javascript">
        var g_operKey = '@Model.OperKey';

        window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);
        function checkOperRight(vm) {
            if (window.g_operRights.cwOperate){
                if (window.g_operRights.cwOperate.ClosingCarryForward && window.g_operRights.cwOperate.ClosingCarryForward.see) {
                    if (!window.g_operRights.cwOperate.ClosingCarryForward.operate) vm.disabledOperate = true;
                    return true;
                } else {
                    return false;
                }
            }else{
                return false;
            }
        }
    </script>
    <script>
        var vm = new Vue({
            el: '#root',
            data() {
                return {
                    period:'',
                    cwPeriodFromTo:'',
                    openPeriod:'',
                    boxData:[
                        {
                            id: 1, name: '结转销售成本', balance: 0, checked: false, costType: '', sheet_id: 0, sheet_no:0, isBtnPlain: false, btnText: '生成凭证',visible:true,
                            table:[
                                { remark: '结转销售成本', dir: '借', sbj_name: '主营业务成本', sbj_code: '5401', sbj_id: '', assisters_name:'无', dir_debit: 0, dir_credit: '' },
                                { remark: '结转销售成本', dir: '借', sbj_name: '库存商品', sbj_code: '1405', sbj_id: '', assisters_name:'无', dir_debit: '', dir_credit: 0 }
                                //dir是科目方向
                            ]
                        },
                        {
                            id: 2, name: '摊销待摊费用', balance: 0, checked: false, sheet_id: 0, sheet_no: 0, isBtnPlain: false, btnText: '生成凭证', visible: true,
                            table: [
                                { remark: '摊销待摊费用', dir: 1, sbj_dir: '借', sbj_name: '长期待摊费用摊销', sbj_code: '560212', sbj_id: '', assisters_name:'无', dir_debit: 0, dir_credit: '' },
                                { remark: '摊销待摊费用', dir: 1, sbj_dir: '借', sbj_name: '长期待摊费用', sbj_code: '1801', sbj_id: '', assisters_name: '无', dir_debit: '', dir_credit: 0 }
                            ],
                            form: { preAmt:0, leftAmt:0, thisAmt:0 }
                        },
                        {
                            id: 97, name: '结转损益', balance: 0, checked: false, sheet_id: 0, sheet_no: 0, isBtnPlain: false, btnText: '生成凭证', visible:true,
                            table:[
                                { remark: '结转损益', dir: -1,sbj_dir:'贷', sbj_name: '主营业务收入', sbj_code: '5001', sbj_id: '', assisters_name:'无', dir_debit:0, dir_credit:'' },
                                { remark: '结转损益', dir: 1,sbj_dir:'借', sbj_name: '主营业务成本', sbj_code: '5401', sbj_id: '',  assisters_name:'无', dir_debit:'', dir_credit:0 },
                                { remark: '结转损益', dir: -1, sbj_dir: '贷', sbj_name: '本年利润', sbj_code: '3103', sbj_id: '', assisters_name: '无', dir_debit: '', dir_credit: 0 },
                            ],
                        },
                        {
                            id: 98, name: '年末结转利润', balance: 0, checked: false, sheet_id: 0, sheet_no: 0, isBtnPlain: false, btnText: '生成凭证', visible:false,
                            table: [
                                { remark: '年末结转利润', dir: -1, sbj_dir: '贷', sbj_name: '本年利润', sbj_code: '3103', sbj_id: '', assisters_name:'无', dir_debit: 0, dir_credit: '' },
                                { remark: '年末结转利润', dir: -1, sbj_dir: '贷', sbj_name: '利润分配-未分配利润', sbj_code: '310406', sbj_id: '', assisters_name: '无', dir_debit: '', dir_credit: 0 },
                            ],
                        },
                        { id: 99, name: '自定义模板', balance: '+', checked: false, sheet_id: 0, visible:false }
                    ],
                    templateSaleCostVisible: false,
                    templateAmortizeVisible:false,
                    templatePandLVisible:false,
                    templatePandLYearEndVisible: false,
                    templateCloseVisible: false,
                    templateVoucherNoVisible: false,
                    templateVoucherNoRepeatVisible: false,
                    templateTable:[],
                    templateForm:{},
                    fullscreenLoading: false,
                    checkItems:[
                        { id: 1, name: '财务初始数据是否平衡', checked: false },
                        { id: 2, name:'资产负债表是否平衡',checked:false },
                        { id: 3, name:'凭证是否审核',checked:false },
                        { id: 4, name:'凭证号是否连续',checked:false },
                        { id: 5, name: '凭证号是否重复', checked: false },
                        { id: 6, name:'是否结转损益',checked:false },
                        //{ id: 6, name:'辅助核算项是否填制完整',checked:false }
                    ],
                    voucherOrderWays: {
                        selectId:1,
                        lostSheetNos:'',
                        repeatSheetNos:'', 
                        checkItems:[
                            { id:1,name:'按凭证号顺次前移重新编号' },
                            { id:2,name:'按凭证日期顺次重新编号' }
                        ]
                    },
                    //templateAssistVoVisible:false,
                    //lostAssistVos: '',
                    //lostAssistVoNos:'',
                    disabledOperate:false
                }
            },
            created(){
                if (!window.checkOperRight(this)) {
                    this.disabledOperate=true;
                    return;
                }

                this.getData();
            },
            mounted() {
                //结转损益数字负数展示为红色
                if (this.boxData[this.boxData.length-3].balance < 0) {
                    this.$refs.balance[this.boxData.length-3].children[0].classList.add('negaBalance');
                }else{
                    this.$refs.balance[this.boxData.length - 3].children[0].classList.remove('negaBalance');
                }
                //年末结转损益数字负数展示为红色
                if (this.boxData[this.boxData.length-2].balance < 0) {
                    this.$refs.balance[this.boxData.length-2].children[0].classList.add('negaBalance');
                }else{
                    this.$refs.balance[this.boxData.length - 2].children[0].classList.remove('negaBalance');
                }

                //展示凭证号
                this.boxData.forEach((item,index)=>{
                    if(item.sheet_id>0){
                        this.showVoucher_a(index, item.sheet_id,item.sheet_no);
                    }
                });

                //自定义模板点击事件
                let diyBox=this.$refs.box[this.$refs.box.length-1];
                diyBox.addEventListener('click',()=>{
                    this.$alert('自定义模板', '提示', {
                        confirmButtonText: '确定'
                    });
                });
                
            },
            updated(){
                if (this.boxData[this.boxData.length-3].balance < 0) {
                    this.$refs.balance[this.boxData.length-3].children[0].classList.add('negaBalance');
                } else {
                    this.$refs.balance[this.boxData.length - 3].children[0].classList.remove('negaBalance');
                }
                if (this.boxData[this.boxData.length - 2].balance < 0) {
                    this.$refs.balance[this.boxData.length - 2].children[0].classList.add('negaBalance');
                } else {
                    this.$refs.balance[this.boxData.length - 2].children[0].classList.remove('negaBalance');
                }

                //结账修改dialog内icon颜色
                let icon_is = document.getElementsByClassName('icon_i');
                for (let i = 0; i < icon_is.length; i++) {
                    icon_is[i].classList.remove('icon-checked');
                    icon_is[i].classList.remove('icon-unchecked');
                    if (this.checkItems[i].checked == true) {
                        icon_is[i].classList.add('icon-checked');
                    } else {
                        icon_is[i].classList.add('icon-unchecked');
                    }
                }

                //展示凭证号
                this.boxData.forEach((item, index) => {
                    if (item.sheet_id > 0) {
                        this.showVoucher_a(index, item.sheet_id, item.sheet_no);
                    }
                });

                //12月展示年末box
                if (new Date(this.period.replace('年', '-').replace('月', '-') + '1').getMonth() + 1 == 12) {
                    this.$nextTick(() => {
                        let box_yearend = this.boxData.find(box => box.id == 98);
                        box_yearend.visible = true;
                    });
                }
            },
            methods: {
                getData() {
                    const loading = this.$loading({ lock: true, text: '加载中...', spinner: 'el-icon-loading', background: 'rgba(255, 255, 255, 1)' });
                    $.ajax({
                        url: '/api/ClosingCarryForward/GetData',
                        type: 'get',
                        data: { operKey: g_operKey },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                        //async: false,
                        error: (xhr) => {
                            loading.close();
                            this.$message({ type: 'error', message: '网络错误 ' + xhr.responseText });
                        }
                    }).then(res => {
                        console.log('closing data');
                        if (res.result === 'OK') {
                            //获取最早待结账会计期间(当前会计期间)
                            this.period=res.period;
                            this.cwPeriodFromTo = res.cwPeriodFromTo;
                            this.openPeriod=res.openPeriod;
                            let op=new Date(this.openPeriod+'-1');
                            this.openPeriod=op.getFullYear()+'年'+(op.getMonth()+1)+'月';

                            //获取结转销售成本相关数据
                            let sale_cost_obj=res.sc;
                            let jzSaleCost = sale_cost_obj.jzSaleCost;
                            if (parseFloat(jzSaleCost) > 0) {
                                this.boxData[0].balance = jzSaleCost;
                                let rows=[];
                                sale_cost_obj.sheetCwVoucher.sheetRows.forEach(row=>{
                                    //{ remark: '结转销售成本', dir: '借', sbj_name: '主营业务成本', sbj_code: '5401', sbj_id: '', assisters_name:'', dir_debit: 0, dir_credit: '' }
                                    let sub = res.subs.find(s => s.sub_id == row.sub_id);
                                    let newRow={};
                                    newRow.remark=row.remark;
                                    newRow.sbj_id=sub.sub_id;
                                    newRow.sbj_code = sub.sub_code;
                                    newRow.sbj_name = sub.sub_name;
                                    newRow.dir= sub.direction == 1 ? '借' : '贷';
                                    newRow.assisters_name = (row.assister_types_names == "" || row.assister_types_names == null) ? "无" : row.assister_types_names;
                                    newRow.dir_debit = row.debit_amount == 0 ? '' : row.debit_amount;
                                    newRow.dir_credit = row.credit_amount == 0 ? '' : row.credit_amount;
                                    rows.push(newRow);
                                });
                                this.boxData[0].table=rows;
                            }
                            else { //待结转销售成本为负，则无需结转
                                this.boxData[0].balance=0;
                            }                            
                            this.boxData[0].costType = sale_cost_obj.costType;//获取成本计价方式
                            if(sale_cost_obj.sheet_id_sc>0){
                                this.boxData[0].sheet_id = sale_cost_obj.sheet_id_sc;
                                this.boxData[0].sheet_no = sale_cost_obj.sheet_no_sc;
                                this.boxData[0].isBtnPlain = true;
                                this.boxData[0].btnText = '重新生成';
                            }

                            //获取结转损益相关数据
                            let pnl=res.pnl;
                            this.boxData[this.boxData.length-3].balance=pnl.pnl_all;
                            if (pnl.sheetCwVoucher.sheetRows.length>0){
                                let rows=[];
                                pnl.sheetCwVoucher.sheetRows.forEach(row=>{
                                    //{ remark: '结转损益', dir: '借', sbj_name: '本年利润', sbj_code: '3103', sbj_id: '', assisters_name:'', dir_debit: 0, dir_credit: '' }
                                    let newRow={};
                                    newRow.remark=row.remark;
                                    newRow.sbj_id=row.sub_id;
                                    newRow.sbj_code=row.sub_code;
                                    newRow.sbj_name=row.sub_name;
                                    newRow.dir=row.direction_label;
                                    newRow.dir_debit = row.debit_amount == 0 ? '' : row.debit_amount;
                                    newRow.dir_credit = row.credit_amount == 0 ? '' : row.credit_amount;
                                    newRow.assisters_name = (row.assister_types_names == "" || row.assister_types_names == null) ? "无" : row.assister_types_names;
                                    rows.push(newRow);
                                });
                                this.boxData[this.boxData.length-3].table = rows;
                            }
                            if(pnl.sheet_id_pnl>0){
                                this.boxData[this.boxData.length-3].sheet_id = pnl.sheet_id_pnl;
                                this.boxData[this.boxData.length-3].sheet_no = pnl.sheet_no_pnl;
                                this.boxData[this.boxData.length-3].isBtnPlain = true;
                                this.boxData[this.boxData.length - 3].btnText = '重新生成';
                            }

                            //获取年末结转利润相关数据
                            let box_pnlYE=this.boxData[this.boxData.length-2];
                            if (new Date(this.period.replace('年', '-').replace('月', '-') + '1').getMonth() + 1 == 12 && res.pnl_ye.amt_ye!=0) {
                                let sub_3103 = res.subs.find(ci => ci.sub_code.toString().substring(0, 4) == '3103');
                                let sub_310406 = res.subs.find(ci => ci.level>=2 && ci.sub_code.toString().substring(0, 6) == '310406');
                                box_pnlYE.balance = res.pnl_ye.amt_ye;
                                box_pnlYE.table=[];
                                res.pnl_ye.sheetCwVoucher.sheetRows.forEach(row=>{
                                    let newrow={ 
                                        remark: row.remark, 
                                        dir: row.direction, 
                                        sbj_dir: row.direction_label,
                                        sbj_name: row.sub_name,
                                        sbj_code: row.sub_code, 
                                        sbj_id: row.sub_id, 
                                        assisters_name: (row.assister_types_names == "" || row.assister_types_names == null) ? "无" : row.assister_types_names,
                                        dir_debit: row.debit_amount,
                                        dir_credit: row.credit_amount
                                    };
                                    box_pnlYE.table.push(newrow);
                                });
                            }
                            if (res.pnl_ye.sheet_id_yearend > 0) {
                                box_pnlYE.sheet_id = res.pnl_ye.sheet_id_yearend;
                                box_pnlYE.sheet_no = res.pnl_ye.sheet_no_yearend;
                                box_pnlYE.isBtnPlain = true;
                                box_pnlYE.btnText = '重新生成';
                            }
                            
                            //获取摊销数据
                            let amortizeBox = this.boxData[1];
                            if (parseFloat(res.amo.leftAmt)!=0 || parseFloat(res.amo.thisAmt)!=0){
                                amortizeBox.sheet_id = res.amo.sheet_id;
                                amortizeBox.sheet_no = res.amo.sheet_no;
                                amortizeBox.form.leftAmt = res.amo.leftAmt;
                                amortizeBox.form.preAmt = res.amo.preAmt;
                                amortizeBox.form.thisAmt = res.amo.thisAmt;
                                amortizeBox.balance = res.amo.thisAmt;
                                amortizeBox.table[0].dir_debit = res.amo.thisAmt;
                                amortizeBox.table[1].dir_credit = res.amo.thisAmt;
                                amortizeBox.table.forEach(row => {
                                    row.remark = this.period.replace('年', '-').split('-')[1] + row.remark;
                                });
                                amortizeBox.table[0].sbj_id = res.subs.find(s => s.sub_code.toString().substring(0, 6) == '560212').sub_id;
                                amortizeBox.table[1].sbj_id = res.subs.find(s => s.sub_code.toString().substring(0, 4) == '1801').sub_id;
                            }
                            if(res.amo.sheet_id>0){
                                amortizeBox.isBtnPlain = true;
                                amortizeBox.btnText = '重新生成';
                            }

                        }else{
                            this.$message({ type: 'warning', message: res.msg });
                        }
                        loading.close();
                    });
                },
                selectAll(){//选择“全选”后，box都选中
                    let ignoreBoxNum = 1;//自定义box
                    if (new Date(this.period.replace('年', '-').replace('月', '-') + '1').getMonth() + 1 != 12) {
                        ignoreBoxNum = 2;//自定义box+年末结转损益box
                    }
                    for (let i = 0; i < this.boxData.length - ignoreBoxNum; i++) {
                        this.boxData[i].checked=event.target.checked;
                    }
                },
                checkAll(){//把所有box都选中后，“全选”也选中
                    let addBox=0;
                    let removeBox=0;
                    let ignoreBoxNum = 1;//自定义box
                    if (new Date(this.period.replace('年', '-').replace('月', '-') + '1').getMonth() + 1 != 12) {
                        ignoreBoxNum = 2;//自定义box+年末结转损益box
                    }
                    for (let i = 0; i < this.boxData.length - ignoreBoxNum; i++) {
                        if(this.boxData[i].checked){
                            addBox++;
                        }else{
                            removeBox++;
                        }
                    }
                    if (addBox == this.boxData.length - ignoreBoxNum) {
                        this.$refs.selAll.checked=true;
                    }
                    if (removeBox >0){
                        this.$refs.selAll.checked = false;
                    }
                },
                showTemplate(box){
                    this.templateTable = box.table;
                    switch(box.id){
                        case 1:
                            this.templateSaleCostVisible = true;
                            break;
                        case 2:
                            this.templateForm = box.form; 
                            this.templateAmortizeVisible = true;
                            break;
                        case 97:
                            this.templatePandLVisible=true;
                            break;
                        case 98:
                            if(new Date(this.period.replace('年','-').replace('月','-')+'1').getMonth()+1 == 12){
                                this.templatePandLYearEndVisible=true;
                            }
                            break;
                    } 
                },
                reload(){
                    location.reload();
                },
                getVoucherData(box) {
                    let rows = box.table;
                    let edTime = new Date(this.period.replace('年', '-').replace('月', '-1'));
                    edTime = new Date(edTime.setMonth(edTime.getMonth() + 1));
                    edTime = new Date(edTime.setSeconds(edTime.getSeconds() - 1));;//待结转月的月末
                    edTime = new Date(edTime.setHours(new Date().getHours()));
                    edTime = new Date(edTime.setMinutes(new Date().getMinutes()));
                    edTime = new Date(edTime.setSeconds(new Date().getSeconds()));
                    let happenTime = edTime;//前端的时间基本不使用，被后端保存的时间替换
                    if (edTime.getFullYear() == new Date().getFullYear() && edTime.getMonth() == new Date().getMonth()) {//如果是当月结账，用当前时间
                        happenTime = new Date();
                    }
                    let sheet = {
                        operKey: g_operKey, IsFromWeb: true, happen_time: this.dateFormat(happenTime,'yyyy-MM-dd hh:mm:ss'),
                        sheet_id: "", sheet_no: "", make_brief: box.name, maker_id: "", maker_name: "",
                    };
                    //返回的direction_label表示科目的方向，change_amount凡是正的都是借方金额，负的都是贷方金额
                    if (box.name == '结转销售成本' || box.name == '摊销待摊费用') {
                        sheet.SheetRows = [
                            { remark: rows[0].remark, sub_id: rows[0].sbj_id, sub_name: rows[0].sbj_name, direction_label: rows[0].dir, debit_amount: box.balance, credit_amount: "", visibleindex: 0, change_amount: box.balance },
                            { remark: rows[1].remark, sub_id: rows[1].sbj_id, sub_name: rows[1].sbj_name, direction_label: rows[1].dir, debit_amount: "", credit_amount: box.balance, visibleindex: 1, change_amount: -parseFloat(box.balance) }
                        ];
                    }
                    else if (box.name == '结转损益' || box.name=='年末结转利润') {
                        sheet.SheetRows = [];
                        let index = 0;
                        rows.forEach(r => {
                            sheet.SheetRows.push({ remark: r.remark, sub_id: parseInt(r.sbj_id), sub_name: r.sbj_name, direction_label: r.dir, debit_amount: r.dir_debit == undefined ? '' : r.dir_debit, credit_amount: r.dir_credit == undefined ? '' : r.dir_credit, visibleindex: index, change_amount: r.dir_debit > 0 ? r.dir_debit : (-r.dir_credit) });
                            index++;
                        });
                    }
                    console.log('carry forward voucher to save before post');
                    return sheet;
                },
                createVoucher(box) {
                    //balance=0无需结转
                    if (box.balance==0){
                        this.$message({ type: 'warning',message: '无需生成凭证' });
                        return;
                    }

                    let sheet = this.getVoucherData(box);
                    let _this=this;
                    this.$confirm('确认生成凭证?', '提示', {
                        confirmButtonText: '确定',cancelButtonText: '取消'
                    }).then(() => {
                        const loading = _this.$loading({ lock: true, text: '生成中...', spinner: 'el-icon-loading', background: 'rgba(255, 255, 255, 0.7)' });
                        $.ajax({
                            url: `/api/ClosingCarryForward/SaveVouchers?operKey=${g_operKey}`,
                            type: 'post',
                            data: JSON.stringify({ operKey: g_operKey, sheets: [sheet] }),
                            contentType: "application/json;charset=UTF-8",
                            dataType: 'json',
                            //async: false,
                            error: (xhr) => {
                                loading.close();
                                this.$message({ type: 'error', message: '生成失败 ' + xhr.responseText });
                            }
                        }).then(res => {
                            console.log('save voucher');
                            loading.close();
                            if (res.result === 'OK') {
                                if (res.msg != "") {
                                    this.$message({ type: 'error', message: res.msg });
                                    return;
                                }

                                this.showVoucher_a(box.id - 1, res.vo_id_nos[box.id].split('-')[0], res.vo_id_nos[box.id].split('-')[1]);
                                //this.$set(box, 'isBtnPlain',true);
                                //this.$set(box, 'btnText', '重新生成');
                                this.$message({
                                    type: 'success',
                                    dangerouslyUseHTMLString: true,
                                    message: `生成凭证 <div style="color:blue;cursor:pointer;display:inline-block;" onclick="openVoucher(${res.vo_id_nos[box.id].split('-')[0]})">记-${res.vo_id_nos[box.id].split('-')[1]}</div>`
                                });
                            }else{
                                loading.close();
                                this.$message({ type: 'error', message: res.msg });
                            }
                        });
                    });
                },
                createVoucherAll(){
                    let count=0;
                    this.boxData.forEach(box=>{
                        if(!box.checked || box.balance==0){
                            count++;
                        }
                    });
                    if(count==this.boxData.length){
                        this.$alert('无需生成凭证', '提示', {
                            confirmButtonText: '确定',
                            type: 'warning'
                        });
                        return;
                    }

                    let _this=this;
                    var loading=null;
                    this.$confirm('确认生成凭证?', '提示', { 
                        confirmButtonText: '确定', cancelButtonText: '取消' 
                    }).then(() => {
                        loading = _this.$loading({ lock: true, text: '生成中...', spinner: 'el-icon-loading', background: 'rgba(255, 255, 255, 0.7)' });
                        let sheets=[];
                        let boxs=[];//生成凭证的box
                        _this.boxData.forEach(box => {
                            if (box.checked && box.balance != 0) {
                                let sheet = _this.getVoucherData(box);
                                sheets.push(sheet);
                                boxs.push(box);
                            }
                        });
                        let sheetData={ operKey: g_operKey, sheets:sheets };
                        $.ajax({
                            url: `/api/ClosingCarryForward/SaveVouchers?operKey=${g_operKey}`,
                            type: 'post',
                            data: JSON.stringify(sheetData),
                            contentType: "application/json;charset=UTF-8",
                            dataType: 'json',
                            //async: false,
                            error: (xhr) => {
                                loading.close();
                                _this.$message({ type: 'error', message: '生成失败 ' + xhr.responseText });
                            }
                        }).then(res => {
                            console.log('save vouchers');
                            loading.close();
                            if (res.result === 'OK') {
                                if (res.msg != "") {
                                    setTimeout(() => {
                                        _this.$message({ type: 'info', dangerouslyUseHTMLString: true, message: res.msg });
                                    }, 10);
                                    return;
                                }

                                boxs.forEach(box=>{
                                    _this.showVoucher_a(box.id - 1, res.vo_id_nos[box.id].split('-')[0], res.vo_id_nos[box.id].split('-')[1]);
                                    box.isBtnPlain = true;
                                    box.btnText = '重新生成';
                                    setTimeout(() => {
                                        _this.$message({ type: 'success', dangerouslyUseHTMLString: true, message: `生成凭证 <div style="color:blue;cursor:pointer;display:inline-block;" onclick="openVoucher(${res.vo_id_nos[box.id].split('-')[0]})">记-${res.vo_id_nos[box.id].split('-')[1]}</div>` });
                                    }, 10);
                                });
                                _this.getData();
                            }else{
                                _this.$message({ type: 'error', dangerouslyUseHTMLString: true, message: res.msg });
                            }
                        });
                    });
                },
                showVoucher_a(index,sheet_id,sheet_no){
                    this.$nextTick(()=>{
                        if (index == 97 - 1) index = this.$refs.voucher_a.length - 3;
                        if (index == 98 - 1) index = this.$refs.voucher_a.length - 2;
                        this.$refs.voucher_a[index].classList.add('voucher_a');
                        this.$refs.voucher_a[index].innerHTML = `记-${sheet_no}`;
                        this.$refs.voucher_a[index].href = 'javascript:void(0);';
                        this.$refs.voucher_a[index].addEventListener('click', () => { window.parent.newTabPage('凭证', `/CwPages/CwVoucher?sheet_id=${sheet_id}`, window); });
                    });
                },
                checkBeforeClose(){
                    this.templateCloseVisible = true;
                    let period=this.period.replace('年','-').replace('月','-1');
                    $.ajax({
                        url: `/api/ClosingCarryForward/CheckBeforeClose?operKey=${g_operKey}`,
                        type: 'post',
                        data: JSON.stringify({
                            operKey: g_operKey,
                            period:period
                        }),
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                        async: false
                    }).then(res => {
                        console.log('check before close');
                        if (res.result === 'OK') {
                            this.checkItems[0].checked = res.check1;
                            this.checkItems[1].checked = res.check2;
                            this.checkItems[2].checked = res.check3;
                            this.checkItems[3].checked = res.check4;
                            this.checkItems[4].checked = res.check5;
                            this.checkItems[5].checked = res.check6;
                            this.voucherOrderWays.lostSheetNos=res.lostSheetNos;
                            this.voucherOrderWays.repeatSheetNos = res.repeatSheetNos;

                            /*let lostAssistVosHtml='';
                            let lostAssistVoNosStr='';
                            if (res.lostAssistVos.length>0){
                                res.lostAssistVos.forEach(row=>{
                                    lostAssistVosHtml += `<div style="color:blue;cursor:pointer;display:inline-block;" onclick="openVoucher(${row.sheet_id})">记-${row.sheet_no}</div>, `;
                                    if (lostAssistVoNosStr.split(',').length < 50) {//一次最多加载50个凭证
                                        lostAssistVoNosStr += `${row.sheet_no},`;
                                    } 
                                });
                                this.lostAssistVos = lostAssistVosHtml.substring(0,lostAssistVosHtml.length-2);
                                if (lostAssistVoNosStr != '') lostAssistVoNosStr = lostAssistVoNosStr.substring(0, lostAssistVoNosStr.length - 1);
                                this.lostAssistVoNos = lostAssistVoNosStr;
                            }*/
                        }
                    });
                },
                /*checkNoAssistVo(){
                    let startDay = this.cwPeriodFromTo.split(' ~ ')[0];
                    let endDay = this.cwPeriodFromTo.split(' ~ ')[1];
                    window.parent.newTabPage('查凭证录入', `/CwPages/CwVoucherView?startDay=${startDay}&endDay=${endDay}+23%3A59&voucher_no=${this.lostAssistVoNos}`);
                },*/
                closeMonth(){
                    this.templateCloseVisible = false;
                    let period = this.period.replace('年', '-').replace('月', '-1');
                    let check=true;
                    this.checkItems.forEach(item=>{
                        if(item.checked==false){
                            check=false;
                            this.$alert('检查项未全部完成，不能结账！', '提示', {
                                confirmButtonText: '确定',
                                type: 'warning'
                            });
                            return;
                        }
                    });
                    if(check==false){
                        return;
                    }

                    $.ajax({
                        url: `/api/ClosingCarryForward/SaveClose?operKey=${g_operKey}`,
                        type: 'post',
                        data: JSON.stringify({
                            operKey: g_operKey,
                            period: period
                        }),
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                        async: false,
                        error: (xhr) => {
                            this.$message({ type: 'error', message: '结账失败 ' + xhr.responseText });
                        }
                    }).then(res => {
                        console.log('save close period');
                        if(res.result==='Error'){
                            this.$alert(res.msg, '提示', {
                                confirmButtonText: '确定',
                                type: 'error'
                            });
                        }
                        else if (res.result === 'OK') {
                            this.$alert('结账成功！', '提示', {
                                confirmButtonText: '确定',
                                type: 'success',
                                callback: action => {
                                    this.boxData[0].table[0].dir_debit = 0;
                                    this.boxData[0].table[1].dir_credit = 0;
                                    this.boxData[1].table = [
                                        { remark: '结转损益', dir: -1, sbj_dir: '贷', sbj_name: '主营业务收入', sbj_code: '5001', sbj_id: '', dir_debit: 0, dir_credit: '' },
                                        { remark: '结转损益', dir: 1, sbj_dir: '借', sbj_name: '主营业务成本', sbj_code: '5401', sbj_id: '', dir_debit: '', dir_credit: 0 },
                                        { remark: '结转损益', dir: -1, sbj_dir: '贷', sbj_name: '本年利润', sbj_code: '3103', sbj_id: '', dir_debit: '', dir_credit: 0 },
                                    ];
                                    this.reload();
                                }
                            });
                        }
                    });
                },
                unclose(){
                    if (this.period == this.openPeriod) {
                        this.$alert('当前会计期间为开账月，不能反结账！', '提示', {
                            confirmButtonText: '确定',
                            type: 'warning'
                        });
                        return;
                    }
                    let period = this.period.replace('年', '-').replace('月', '-1');
                    period = new Date(period);
                    period = new Date(period.setMonth(period.getMonth() - 1));
                    let period0 = period.getFullYear() + '-' + (period.getMonth() + 1)+'-1';
                    this.$confirm(`确认反结账至会计期间${period.getFullYear()}年${period.getMonth()+1}月？`, '提示', {
                        confirmButtonText: '确定', cancelButtonText: '取消',type:'warning'
                    }).then(() => {
                        $.ajax({
                            url: `/api/ClosingCarryForward/SaveUnclose?operKey=${g_operKey}`,
                            type: 'post',
                            data: JSON.stringify({
                                operKey: g_operKey,
                                period: period0
                            }),
                            contentType: "application/json;charset=UTF-8",
                            dataType: 'json',
                            async: false
                        }).then(res => {
                            console.log('save unclose');
                            if (res.result === 'OK') {
                                this.$alert('反结账成功', '提示', {
                                    confirmButtonText: '确定',
                                    type: 'success',
                                    callback: action => {
                                        this.reload();
                                    }
                                });
                            }
                        });
                    });
                },
                putVoucherInOrder(){
                    this.templateVoucherNoVisible = false;
                    let order_type = this.voucherOrderWays.selectId;//1 or 2
                    let _period = this.period.replace('年', '-').replace('月', '-01');
                    $.ajax({
                        url: `/api/ClosingCarryForward/PutVoucherInOrder?operKey=${g_operKey}`,
                        type: 'post',
                        data: JSON.stringify({
                            operKey: g_operKey,
                            orderType: order_type,
                            period: _period
                        }),
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                        async: false
                    }).then(res => {
                        console.log('put voucher in order before close');
                        if (res.result === 'OK') {
                            this.$message({ type: 'success', message: '整理凭证已完成' });
                            this.checkBeforeClose();
                        } else {
                            this.$message({ type: 'error', message: res.msg });
                        }
                    });
                },
                setAmortizeAmount(){
                    if (this.templateForm.thisAmt<0){
                        this.templateForm.thisAmt=0;
                        return;
                    }
                    if(parseFloat(this.templateForm.thisAmt)>parseFloat(this.templateForm.leftAmt)){
                        this.templateForm.thisAmt=this.templateForm.leftAmt;
                    }
                    this.boxData[1].table[0].dir_debit = this.templateForm.thisAmt;
                    this.boxData[1].table[1].dir_credit = this.templateForm.thisAmt;
                    this.templateTable = this.boxData[1].table;
                },
                dateFormat(date, fmt) {
                    var o = {
                        "M+": date.getMonth() + 1, //月份
                        "d+": date.getDate(), //日
                        "h+": date.getHours(), //小时
                        "m+": date.getMinutes(), //分
                        "s+": date.getSeconds(), //秒
                        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
                        "S": date.getMilliseconds() //毫秒
                    };
                    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
                    for (var k in o)
                        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                    return fmt;
                }
                
             }
        })
        function openVoucher(sheet_id){
            window.parent.newTabPage('凭证', `/CwPages/CwVoucher?sheet_id=${sheet_id}`, window);
        }
    </script>


</body>
</html>