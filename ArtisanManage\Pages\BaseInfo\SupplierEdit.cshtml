@page
@model ArtisanManage.Pages.BaseInfo.SupplierEditModel
@{
    Layout = null;
    ///jkkll
} 
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>SupplierEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    
    <script type="text/javascript">

        window.g_operKey = '@Html.Raw(Model.OperKey)';

        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            window.onresize();
            debugger
            $('#sup_name input').on('input', function () {
               
                $('#py_str input').val(this.value.ToPinYinCode());
            });
        });
        window.onresize = function () {
           // var windowWidth = window.innerWidth;
          //  $("#gridUnit").jqxGrid(
          //      {
          //          width: windowWidth - 20
           //     });
        };
    </script>
</head>
<body>
    <div id="divHead" class="headtail" style="width:500px;padding-right:0px;"></div> 
    <div style="padding-left:20px;padding-top:10px;">生产商</div>
    <div id="gridManufactor" style="width:calc(100% - 35px);margin-top:10px;margin-left:10px;margin-right:0px;height:50px;"> </div>
    <div style="text-align:center;margin-top:20px;">
        <button id="btnSave" onclick="btnSave_checkData();" style="margin-right:50px;">保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>
<script type="text/javascript">
    function btnSave_checkData(bCopy, callback) {
        debugger
        var formFlds = getFormData();
        if (formFlds.errMsg) {
            return;
        }
        try {
            if (typeof (eval(checkDataValid)) == 'function') {
                var bOK = checkDataValid(formFlds);
                if (!bOK) return;
            }
        } catch (e) { }

        if (window.dealFormData && typeof (eval(window.dealFormData)) == 'function') {
            dealFormData(formFlds);
        }
        $.ajax({
            url: '../api/ClientEdit/CheckData',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formFlds),
            success: function (data) {
                if (data.result == 'OK') {

                    btnSave_Clicked(bCopy, callback)
                }
                else {
                    jConfirm(data.msg + ",是否继续?", function () {
                        btnSave_Clicked(bCopy, callback)
                    }, "");
                    //bw.toast(data.msg, 5000);
                }
            },
            error: function (response, ajaxOptions, thrownError) {
                bw.toast('error' + response);
            }
        });
    }
</script>