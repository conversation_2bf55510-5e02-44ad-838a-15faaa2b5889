@page
@model ArtisanManage.Pages.BaseInfo.BranchEditModel
@{
    Layout = null;
} 
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>BranchEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">


        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
             @Html.Raw(Model.m_showFormScript)
             @Html.Raw(Model.m_createGridScript)
             $('#branch_name input').on('input', function () {
                $('#py_str input').val(this.value.ToPinYinCode());
            })
             //var rows = $('#gridPosition').jqxGrid('getrows');
             //let hasValueRows = rows.filter(r=>r.branch_id)
             //console.log(hasValueRows)
             //let newRow = {}
             //if(!hasValueRows.length){
             //       $('#gridPosition').jqxGrid('setcellvalue', 0, 'branch_id', $("#branch_id").val());
             //       $('#gridPosition').jqxGrid('setcellvalue', 0, 'branch_position_name', "默认仓库");
             //       $('#gridPosition').jqxGrid('setcellvalue', 0, 'branch_position',"0");
             //}
        });
    </script>
</head>
<body>
    <div id="divHead" class="headtail" style="width:500px;">
        
    </div> 
    <div id="gridPosition" style="width:calc(100% - 20px);margin-top:10px;margin-left:10px;margin-right:0px;height:50px;"> </div>
    <div style="text-align:center;margin-top:20px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;">保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>
