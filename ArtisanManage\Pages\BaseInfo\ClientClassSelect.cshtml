﻿@page
@model ArtisanManage.Pages.BaseInfo.ClientClassSelectModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <link href="~/css/component.css" rel="stylesheet" />
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';

    	$(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)

            $("#other_region").on('click', function (event) {
                var selectedItem = $('#other_region').jqxTree('selectedItem');
                if (selectedItem) {
                    console.log(selectedItem);
                    var region_id = selectedItem.value;
                    var region_name = selectedItem.label;
                    var msg = {
                        msgHead: 'ClientClassSelect', action: 'select', region_id: region_id, region_name: region_name
                    };
                    window.parent.postMessage(msg, '*');

                }
            });

            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            QueryData();
    	});
    </script>
</head>

<body style="overflow:hidden">
   

    <style>
        html, body {
            height: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        .dataArea {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            margin: 0;
            width: 100%;
            height: 100%;
        }

            .dataArea > div:first-child {
                width: 100%;
                height: 40px;
            }

            .dataArea > div:last-child {
                display: flex;
                align-items: stretch;
                align-content: stretch;
                width: 100%;
                flex-grow: 1;
            }

                .dataArea > div:last-child > div:first-child {
                    width: 200px;
                }

                .dataArea > div:last-child > div:last-child {
                    flex-grow: 1;
                }
    </style>




    <div style="display:flex;justify-content:space-around;margin-top:20px;">
        <div style="font:35px;font-weight:bold">价格方案列表</div>
    </div>

    <div class="dataArea">
        <div>
            <div>
                <div style="height:5px;">

                </div>
                <div style="width:100%;height:100%;margin-top:20px;">
                    <div id='other_region' style="width:100%;height:calc(100% - 90px);margin-top:10px;margin-bottom:2px;margin-left:10px;overflow-y:scroll">
                    </div>
                </div>
                <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:calc(100% - 80px);"></div>

            </div>
        </div>
    </div>

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

    <partial name="dialog" />


</body>
</html>