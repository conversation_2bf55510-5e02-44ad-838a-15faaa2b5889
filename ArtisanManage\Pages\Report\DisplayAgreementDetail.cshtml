﻿@page
@model ArtisanManage.Pages.Report.DisplayAgreementDetailModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var m_db_id = "10";

    	    var newCount = 1;

        var itemSource = {};

        function MonthRender(row, column, value, p4, p5, rowData) {
            var unit_no = rowData.unit_no;
            var i = p5.text.slice(0, -1);
            var givenCol = 'month' + i + '_given'
            var maintainTimesCol = "month" + i + "_maintain_times"
            var templateMaintainTimesCol = "month_maintain_times"
            var giveConditionCol = "give_condition"
            var templateNameCol = "disp_template_name"
            var year = rowData['month' + i + '_year']
            var doesNeedKeepCol = "month" + i + "_does_need_keep"
            var odkReviewerCol = "month" + i + "_odk_reviewer"
            var odkReviewRefusedCol = "month" + i + "_odk_review_refused"
            var given = $('#gridItems').jqxGrid('getcellvalue', row, givenCol);
            var maintainTimes = $('#gridItems').jqxGrid('getcellvalue', row, maintainTimesCol);
            var templateMaintainTimes = $('#gridItems').jqxGrid('getcellvalue', row, templateMaintainTimesCol);
            var giveCondition = $('#gridItems').jqxGrid('getcellvalue', row, giveConditionCol);
            var templateName = $('#gridItems').jqxGrid('getcellvalue', row, templateNameCol);
            var doesNeedKeep = $('#gridItems').jqxGrid('getcellvalue', row, doesNeedKeepCol);
            var odkReviewer = $('#gridItems').jqxGrid('getcellvalue', row, odkReviewerCol);
            var odkReviewRefused = $('#gridItems').jqxGrid('getcellvalue', row, odkReviewRefusedCol);
            if (templateMaintainTimes == "") {
                templateMaintainTimes = 0
            }
            if(maintainTimes ==""){
                maintainTimes = 0
            }

            if (value > 0) {
                var keepLabel = ''
                var maintainLabel = ''
                var canpaymentLabel = ''
                var paymentLabel = ''
                var div = ''
                //续签
                if ((doesNeedKeep == 'true' && odkReviewer != "" && odkReviewRefused == 'false') || doesNeedKeep == 'false' ) {
                    keepLabel = `<label style ="color:#3CB371;font-size:14px;text-align:center;position:absolute;top:2px;left:2px;">已续</label>`
                } else {
                    keepLabel = `<label style ="color:#e6214a;font-size:14px;text-align:center;position:absolute;top:2px;left:2px;">未续</label>`
                }
                //维护
                if (templateName != "" && templateMaintainTimes != 0) {//有模板
                    if (maintainTimes == templateMaintainTimes) {
                        maintainLabel = `<label style ="position:absolute;bottom:2px;right:2px;color:#e6214a;font-size:14px;">维护完成</label>`
                    } else {
                        maintainLabel = `<label style ="position:absolute;bottom:2px;right:2px;color:#aaa;font-size:14px;">维护${maintainTimes}/${templateMaintainTimes}次</label>`
                    }

                }
                //兑付
                if (templateName == "" || giveCondition == 'after_sign' || maintainTimes >= templateMaintainTimes) {//可兑
                    canpaymentLabel = `<label style ="color:#3CB371;font-size:14px;position:absolute;top:2px;right:2px;">可兑</label>`
                    if (value > given) {//还没兑完
                        if(given == 0){
                            paymentLabel = `<label style ="position:absolute;bottom:2px;left:2px;color:#aaa;font-size:14px;">未兑付</label>`
                        }else{
                            paymentLabel = `<label style ="position:absolute;bottom:2px;left:2px;color:#aaa;font-size:14px;">已兑${given}${unit_no}</label>`
                        }
                    } else {
                        paymentLabel = `<label style ="position:absolute;bottom:3px;left:3px;color:#e6214a;font-size:14px;">已兑完</label>`
                    }
                    div = `<div style ="height:100%;width:100%;display:flex;flex-direction: column;align-items:center;" >${keepLabel}${canpaymentLabel}<label style = "line-height:50px" >${value}</label>${paymentLabel}${maintainLabel}</div>`
                }
                div = `<div style ="height:100%;width:100%;display:flex;flex-direction:column;align-items:center;" >${keepLabel}${canpaymentLabel}<label style = "line-height:50px" >${value}</label><div style="display:flex">${paymentLabel}&nbsp;${maintainLabel}</div></div>`
                return div;
            }
        }

        function SubAmtRender(row, column, value, p4, p5, rowData) {
            var flow_id = rowData.flow_id;
            var sheet_no = rowData.sheet_no;
            var supcust_id = rowData.supcust_id;
            var items_id = rowData.items_id;
            var sup_name = rowData.sup_name;
            var sub_id = rowData.sub_id;
            var sub_name = rowData.sub_name;
            var unit_no = rowData.unit_no;
            var sub_amount_given = rowData.sub_amount_given;
            if (sub_amount_given > 0) {
                return `<div style ="display:flex; align-items:center; justify-content: center; flex-direction: column;margin-top:5px"><div>${value}</div><a onclick="TurnToSaleDetail(${flow_id},${supcust_id},'${sup_name}','${sheet_no}','${items_id}','${sub_id}','${sub_name}')" style = "color:#00f;font-size:11px;cursor:pointer"> 已兑${sub_amount_given}${unit_no}</div></div>`
            }
        }

        function TurnToSaleDetail(flowID, supcustID, supName,sheetNo,items_id,subID,subName) {
            var startDay = $('#startDay').val()
            var endDay = $('#endDay').val()
            if (items_id == "money")
                window.parent.newTabPage('查看销售单', `Sheets/SaleSheetView?supcust_id=${supcustID}&sup_name=${encodeURIComponent(supName)}&disp_sheet_no=${sheetNo}&startDay=${startDay}&endDay=${endDay}&payway_id=${subID}&sub_name=${encodeURIComponent(subName)}`);
            else window.parent.newTabPage('销售明细表', `Report/SalesDetail?supcust_id=${supcustID}&sup_name=${encodeURIComponent(supName)}&disp_sheet_no=${sheetNo}&startDay=${startDay}&endDay=${endDay}`);

        }

        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)


            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                if (args.datafield == "sheet_no") {
                    var sheet_id = args.row.bounddata.sheet_id;
                    window.parent.newTabPage('陈列协议', `Sheets/DisplayAgreementSheet?sheet_id=${sheet_id}`);
                }
            });
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
            $('#supcust_id').jqxInput({
                onButtonClick: function (event) {
                    $('#popClient').jqxWindow('open');
                    $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                }
            });
            $("#popClient").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            //start
            $('#disp_template_id').jqxInput({
                onButtonClick: function (event) {
                    $('#popDisplayTemplate').jqxWindow('open');
                    $("#popDisplayTemplate").jqxWindow('setContent', `<iframe src="/BaseInfo/SuppliersView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                }
            });
            $("#popSupplier").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            //end
            $('#item_id').jqxInput({
                onButtonClick: function (event) {
                    $('#popItem').jqxWindow('open');
                    $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                }
            });
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            QueryData();
        });

        window.addEventListener('message', function (rs) {
            if (rs.data.msgHead === "ClientsView") {
                if (rs.data.action === "select") {
                    var supcust_id = rs.data.supcust_id;
                    var sup_name = rs.data.sup_name;
                    $('#supcust_id').jqxInput('val', { value: supcust_id, label: sup_name });

                }
                $('#popClient').jqxWindow('close');
            }
            else if (rs.data.msgHead === "ItemsView") {
                if (rs.data.action === "selectMulti") {
                    if (rs.data.checkedRows.length == 1) {
                        var item_id = rs.data.checkedRows[0].item_id;
                        var item_name = rs.data.checkedRows[0].item_name;
                    }

                    var rows = rs.data.checkedRows
                    var items_id = ''
                    rows.forEach(function (row) {
                        if (items_id != '') items_id += ','
                        items_id += row.item_id
                    })
                    $('#item_id').jqxInput('val', { value: item_id, label: item_name });

                    $.ajax({
                        url: '/api/SaleSheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: items_id },
                        success: function (data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });
                }

                $('#popItem').jqxWindow('close');
            }

        });

    </script>
</head>

<body>

    <div style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">

            <div style="float:none;height:0px; clear:both;"></div>

        </div>

        <button onclick="QueryData()" style="margin-right:20px;margin-top:30px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;margin-top:30px;">导出</button>

    </div>

    <div id="gridItems"></div>
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div>
    @*start*@
    <div id="popSupplier" style="display:none">
        <div id="supplierCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择供应商</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    @*end*@
    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择商品</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>