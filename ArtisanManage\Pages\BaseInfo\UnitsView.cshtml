@page
@model ArtisanManage.Pages.BaseInfo.UnitsViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == "UnitEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var row_arr = new Array;
                        var row = {
                            i: rs.data.record.unit_id,
                            unit_id: rs.data.record.unit_id,
                            unit_no: rs.data.record.unit_no,
                            is_big_unit: rs.data.record.is_big_unit == "True" ? "是" : "否"
                        }

                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;
                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "unit_no",rs.data.record.unit_no);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "is_big_unit", rs.data.record.is_big_unit);
                    QueryData();

                }
                $("#popItem").jqxWindow('close');
            };
        });
            var m_db_id = "10";

    	    var newCount = 1;

        function btnAddItem_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="UnitEdit?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
        }

        function onGridRowEdit(rowIndex) {
            var unit_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'unit_id');
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe src="UnitEdit?operKey=' + g_operKey + '&unit_id=' + unit_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
        }

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)


                $("#btnAddItem").bind("click", { isParent: false }, btnAddItem_click);

                $("#gridItems").on("cellclick", function (event) {
                    // event arguments.
                    var args = event.args;
                    if (args.datafield == "unit_no") {
                        if (args.originalEvent.button == 2) return;
                        var unit_id = args.row.bounddata.unit_id;
                        RowIndex = args.rowindex;
                        if (ForSelect) {
                            var unit_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "unit_id");
                            var msg = {
                                msgHead: 'UnitView', action: 'select', unit_id: unit_id
                            };
                            window.parent.postMessage(msg, '*');
                        }
                        else {
                            onGridRowEdit(args.rowindex);

                            //$('#popItem').jqxWindow('open');
                            // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                        }
                    }
                });
                $("#Cancel").on('click', function () {
                    for (var i = 0; i < 10; i++) {
                        $('#jqxgrid').jqxGrid('deleterow', i);
                        $('#jqxgrid').jqxGrid('addrow', i, {})
                    }
                });

                $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });


                $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                    return false;
                });

                /*
                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                    if (args.datafield == "unit_no") {
                        var id = args.row.bounddata.unit_no;
                        RowIndex = args.rowindex;

                        $('#popItem').jqxWindow('open');
                        //unit_no 需要跟后端定义字段一致
                        $("#popItem").jqxWindow('setContent', '<iframe src="UnitEdit?operKey=' + g_operKey + '&unit_no=' + id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                    }

                });
                $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                */
               QueryData();
    	    });
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
    </style>
</head>

<body>

    <div id="divHead" style="display:flex;justify-content:space-around;margin-top:20px;">
        <div><input id="searchString" class="margin" placeholder="请输入简拼/名称" /><button onclick="QueryData()" class="margin">查询</button></div>

        <div><button onclick="btnAddItem_click()" class="margin">新增单位</button></div>
    </div>

    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:calc(100% - 80px);"></div>


    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>