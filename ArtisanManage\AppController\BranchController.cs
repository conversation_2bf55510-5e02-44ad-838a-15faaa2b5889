using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;
using System.Net.Http;
using ArtisanManage.Services;

namespace ArtisanManage.AppController
{
    [Route("common/[controller]/[action]")]
    public class BranchController : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        
        public BranchController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd;
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// 获取仓库列表
        /// </summary>
        /// <param name="operKey">操作密钥</param>
        /// <returns>仓库列表</returns>
        [HttpGet]
        public async Task<JsonResult> GetBranchList(string operKey)
        {
            string result = "OK";
            string msg = "";
            List<ExpandoObject> data = null;
            
            try
            {
                Security.GetInfoFromOperKey(operKey, out string companyID);
                
                var sql = $@"
                    SELECT 
                        branch_id, 
                        branch_name
                    FROM info_branch 
                    WHERE company_id = {companyID} 
                    AND status = 1
                    ORDER BY branch_name";
                
                data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            }
            catch (Exception e)
            {
                result = "Failed";
                msg = "获取仓库列表失败: " + e.Message;
            }
            
            return Json(new { result, msg, data });
        }
    }
}