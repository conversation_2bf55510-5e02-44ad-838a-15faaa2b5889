﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Math;
using static ArtisanManage.Models.DataItem;


namespace ArtisanManage.Pages.BaseInfo
{
    public class PriceStrategyModel : PageFormModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;


        public static string getPriceSql(string price)
        {
            return @$" select 'wholesale' {price},'批发价' {price}_name, 0 order_index
                        union select 'retail' {price},'零售价' {price}_name, 1 order_index
                        union select 'recent' {price},'最近售价' {price}_name, 2 order_index
                       union select plan_id::text as {price},plan_name as {price}_name, 3 order_index from price_plan_main where price_plan_main.company_id=~COMPANY_ID order by order_index
";
        }

       
        
        public PriceStrategyModel(CMySbCommand cmd) : base(Services.MenuId.priceStrategy)
        {
            this.cmd = cmd;
            this.NoIDFld = true;
            Grids = new Dictionary<string, FormDataGrid>()
            {
               {
                  "gridItems",  new FormDataGrid()
                  {
                     IdFld="",TableName="price_strategy_class",
                    // ShowContextMenu=true,
                     MinRows=20,
                     AutoAddRow=true,
                     AllowDragRow=true,
                     AllowInsertRemoveRow=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                      // {"setting_id",new DataItem(){Title="设置编号",SqlFld ="setting_id", Hidden=true}},
                       {"client_class",new DataItem(){Title="客户类别",
                       FuncGetSubColumns = async (col) =>
                        {
                            ColumnsResult result=new ColumnsResult();
                            //await CDbDealer.GetRecordsFromSQLAsync($"select * from plan_item;", cmd);
                            Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>(){
                                {"group_id",new DataItem(){Title="渠道",LabelFld="group_name",Width="160",SqlForOptions="select group_id,group_name from info_supcust_group",GetOptionsOnLoad=true,ButtonUsage="list"}},
 
                                {"region_id",new DataItem(){Title="片区",LabelFld="region_name",Width="160",SqlForOptions="select region_id,region_name from info_region",GetOptionsOnLoad=false,ButtonUsage="list",MaxRecords = "500"} },
                                
                                { "rank_id",new DataItem(){Title="等级",LabelFld="rank_name",Width="160",SqlForOptions="select rank_id,rank_name from info_supcust_rank",GetOptionsOnLoad=false,ButtonUsage="list"}},
 
                            };
                           // result.FldsSQL="group_id,group_name,region_id,region_name,rank_id,rank_name";
                            result.Columns=subColumns;
                            return result;
                           }} },
                       { "price",new DataItem(){Title="指定价格",Width="540" ,
                       FuncGetSubColumns = async (col) =>
                           {
                            ColumnsResult result=new ColumnsResult();
                            //await CDbDealer.GetRecordsFromSQLAsync($"select * from price_plan_main;", cmd);
                            Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>(){
                                { "price1",new DataItem(){Title="首选价格",LabelFld="price1_name", SqlFld="price_strategy_class.price1", Width="160",ButtonUsage="list",
                                    SqlForOptions=getPriceSql("price1"),SearchFields="['price1_name']"} },
                                { "price2",new DataItem(){Title="次选价格",LabelFld="price2_name", SqlFld="price_strategy_class.price2",Width="160",ButtonUsage="list",
                                    SqlForOptions=getPriceSql("price2"),SearchFields="['price2_name']"
                                } },
                                { "price3",new DataItem(){Title="末选价格",LabelFld="price3_name", SqlFld="price_strategy_class.price3",Width="160",ButtonUsage="list",
                                    SqlForOptions=getPriceSql("price3"),SearchFields="['price3_name']"
                                } }
                            };
                           // result.FldsSQL="price1,price2,price3";
                            result.Columns=subColumns;
                            return result;
                           }}
                         },
                       { "order_source",new DataItem(){Title="来源",LabelFld="order_source_name",Width="160",GetOptionsOnLoad=false,
                           //ValueMember="order_source",DisplayMember="order_source_name",
                           SqlForOptions="SELECT * FROM (VALUES ('xcx', '线上'), ('worker', '线下'),('all', '全部')) AS t(order_source, order_source_name);-- ",
                           //SearchFields="['order_source_name']",
                           //Source=@"[{order_source:'all',order_source_name:'全部'},
                           //              {order_source:'worker',order_source_name:'线下'},
                           //                {order_source:'xcx',order_source_name:'线上'}]",
                           ButtonUsage="list"}}
                     },
                     SelectFromSQL=$@"
from price_strategy_class
left join (select region_id reid,region_name from info_region where company_id= '~COMPANY_ID') r on price_strategy_class.region_id = r.reid
left join (select group_id gid,group_name from info_supcust_group where company_id= '~COMPANY_ID') g on g.gid = price_strategy_class.group_id
left join (select rank_id rid,rank_name from info_supcust_rank where company_id= '~COMPANY_ID') sr on sr.rid = price_strategy_class.rank_id
left join ({getPriceSql("price1")}) p1 on p1.price1 = price_strategy_class.price1
left join ({getPriceSql("price2")}) p2 on p2.price2 = price_strategy_class.price2
left join ({getPriceSql("price3")} ) p3 on p3.price3 = price_strategy_class.price3
left join (SELECT * FROM (VALUES ('xcx', '线上'), ('worker', '线下'),('all', '全部')) AS t(order_source, order_source_name)) os on os.order_source = price_strategy_class.order_source
where price_strategy_class.company_id='~COMPANY_ID' order by price_strategy_class.flow_id"

                  }
                }, 
                {
                  "gridClients",  new FormDataGrid()
                  {
                     IdFld="",TableName="price_strategy_client",
                     
                    // ShowContextMenu=true,
                     MinRows=20,
                     AutoAddRow=true,
                     AllowDragRow=true,
                     AllowInsertRemoveRow=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                        {"supcust_id",new DataItem(){Title="客户", LabelFld="sup_name",SearchFields="['sup_name','py_str']", SqlForOptions="select supcust_id,sup_name,py_str from info_supcust where supcust_flag in ('C','CS')", Width="180",GetOptionsOnLoad=false,ButtonUsage="list"}},
                        { "price",new DataItem(){Title="指定价格",Width="540" ,
                       FuncGetSubColumns = async (col) =>
                           {
                            ColumnsResult result=new ColumnsResult();
                            //await CDbDealer.GetRecordsFromSQLAsync($"select * from plan_item;", cmd);
                            Dictionary<string,DataItem> subColumns=new Dictionary<string,DataItem>(){
                                { "price1",new DataItem(){Title="首选价格",LabelFld="price1_name", SqlFld="price_strategy_client.price1", Width="160",ButtonUsage="list",
                                    SqlForOptions=getPriceSql("price1"),SearchFields="['price1_name']"
                                } },
                                { "price2",new DataItem(){Title="次选价格",LabelFld="price2_name", SqlFld="price_strategy_client.price2",Width="160",ButtonUsage="list",
                                    SqlForOptions=getPriceSql("price2"),SearchFields="['price2_name']"
                                } },
                                { "price3",new DataItem(){Title="末选价格",LabelFld="price3_name", SqlFld="price_strategy_client.price3",Width="160",ButtonUsage="list",
                                    SqlForOptions=getPriceSql("price3"),SearchFields="['price3_name']"
                                } }
                            };
                           // result.FldsSQL="price1,price2,price3";
                            result.Columns=subColumns;
                            return result;
                           }} },
                        { "order_source",new DataItem(){Title="来源",LabelFld="order_source_name",Width="160",GetOptionsOnLoad=false,
                            //ValueMember="order_source",DisplayMember="order_source_name",
                            SqlForOptions="SELECT * FROM (VALUES ('xcx', '线上'), ('worker', '线下'),('all', '全部')) AS t(order_source, order_source_name);--  ",
                            //SearchFields="['order_source_name']",
                            //Source=@"[{order_source:'all',order_source_name:'全部'},
                            //             {order_source:'worker',order_source_name:'线下'},
                            //               {order_source:'xcx',order_source_name:'线上'}]",
                            ButtonUsage="list"}}
                     },
                     SelectFromSQL=$@"from price_strategy_client
                                    left join (select supcust_id sid,sup_name from info_supcust where company_id='~COMPANY_ID') i  on i.sid=price_strategy_client.supcust_id
                                    left join ( {getPriceSql("price1")}) p1 on p1.price1 = price_strategy_client.price1::text
                                    left join ({getPriceSql("price2")}) p2 on p2.price2 = price_strategy_client.price2::text
                                    left join ({getPriceSql("price3")}) p3 on p3.price3 = price_strategy_client.price3::text
                                    left join (SELECT * FROM (VALUES ('xcx', '线上'), ('worker', '线下'),('all', '全部')) AS t(order_source, order_source_name)) os on os.order_source = price_strategy_client.order_source
                                    where price_strategy_client.company_id='~COMPANY_ID'",
                  }
                }
            };
        } 
      
        public async Task OnGet(string forSelect)
        {  
            await InitGet(cmd);
            ForSelect = forSelect == "1";
             
        }

    }




    [Route("api/[controller]/[action]")]
    public class PriceStrategyController : BaseController
    { 
        public PriceStrategyController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        /*
        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value)
        {
            PriceClassSettingModel model = new PriceClassSettingModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(operKey, model.DataItems, dataItemName, flds, value, cmd);
            return data;
        }*/

        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            PriceStrategyModel model = new PriceStrategyModel(cmd);
            if (value != null)
            {

            }
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value,availValues );
            return data;
        }

        // [HttpGet]
        //  public async Task<object> GetQueryRecords()
        // {
        //  PriceClassSettingModel model = new PriceClassSettingModel(cmd);
        //  object records = await model.GetRecordFromQuerySQL(Request, cmd);
        //  return records;
        // }

        //[HttpPost]
        //public async Task<object> DeleteRecords([FromBody] dynamic data)
        //{
        //    PriceClassSettingModel model = new PriceClassSettingModel(cmd);
        //    object records = await model.DeleteRecords(data, cmd);// gridID, startRow, endRow, bNewQuery);
        //    return records;
        //}

        [HttpPost]
        public async Task<object> Save([FromBody] dynamic data)
        {
          
            PriceStrategyModel model = new PriceStrategyModel(cmd);
            /*string item_class = data["item_class"];
            Security.GetInfoFromOperKey((string) data.operKey, out string companyID);
            dynamic res = await CDbDealer.Get1RecordFromSQLAsync($"select class_id from info_item_class where company_id ={companyID} and class_id = {item_class} and class_name = '全部' ", cmd);
            if (res != null)
            {
                error = "请勿将商品类别设置为'全部'";
                return new JsonResult(new { result = "Error", msg = error });
            } */
            return await model.SaveTable(cmd, data);

            /*
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            var delSql = $"delete price_class_setting where company_id = {companyID}";
            await CDbDealer.GetRecordsFromSQLAsync(delSql, cmd);
           
            var rowString = data.sheetRows;
            int lines =rowString.Count;
            for (int i =0;i<lines; i++)
            {
                string group_id="null";
                if (rowString[i]["group_id"] != null) group_id = rowString[i]["group_id"];

                string rank_id = "null";
                if (rowString[i]["rank_id"] != null) rank_id = rowString[i]["rank_id"];

                string region_id = "null";
                if (rowString[i]["region_id"] != null) region_id = rowString[i]["region_id"];

                string price1 = (string)rowString[i]["price1"];
                string price2 = (string)rowString[i]["price2"];
                string price3 = (string)rowString[i]["price3"];
                var sql = $"insert into price_class_setting(group_id,region_id,rank_id,price1,price2,price3,add_time,company_id) values ({group_id},{region_id},{rank_id},'{price1}','{price2}','{price3}',now(),'{companyID}');";
                await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            }
            return "ok";*/
        }
    }
}
