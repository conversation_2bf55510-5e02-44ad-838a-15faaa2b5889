﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.YingjiangMessage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HPSF;
using NPOI.POIFS.Crypt.Dsig;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Asn1.Ocsp;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using static ArtisanManage.Models.DataItem;
//using Newtonsoft.Json;
//using Newtonsoft.Json.Linq;

namespace ArtisanManage.Models
{
    public class FormDataGrid
    {
        public Dictionary<string, DataItem> Columns = new Dictionary<string, DataItem>();
        // public List<Dictionary<string, DataItem>> Rows = new List<Dictionary<string, DataItem>>();

        public string TableName = ""; public string IdFld = "";
        public string SelectFromSQL = "";
        public int MinRows = 3;
        public bool AllowDragRow = false;
        public bool AllowInsertRemoveRow = false;
        public bool AutoAddRow = true;
        public int MinEmptyRows = 1;//底部最少的空白行数
        public int AddRowsCount = 1;//检测到空白行不够时一次性添加的行数
        public bool HaveContextMenu = false;
        public string ContextMenuLiHtml = "<li id='remove'>删除</li>";//可以添加其他自定义li,定义 onGridRowContextMenuClick(gridID,menuID,rowID)即可接收菜单点击事件
        public int PinColumnWidth = 50;
        public string JSFixColumnCellRender = "";
        public string SelectionMode = "singlerow";
        public string EditMode = "selectedcell";
        public string scriptCreateGrid, scriptSaveGrid;
        public bool GridIdFldIsSerial = false;
        public string GridIdFld = "";
        public int Height = 0;
        public int Width = 0;
        public virtual void OnGridDataGot(List<Dictionary<string, dynamic>> lstRows)
        {

        }
        public class ColumnJsResult
        {
            public string sColumns = "";
            public bool bHaveLinkeBtn;
            public string columnGroups = "";
        }
      
        public async Task GetScript(CMySbCommand cmd, PageFormModel pageForm, string gridID, string ID, string companyID)
        {
            if (ID == "") SelectFromSQL = SelectFromSQL.Replace("~ID", "-1");
            else SelectFromSQL = SelectFromSQL.Replace("~ID", ID);
            string sql = await PageFormModel.GetSQLFromDataItems(companyID, Columns, TableName, SelectFromSQL);
            cmd.CommandText = sql;
            List<System.Dynamic.ExpandoObject> lstRecords = await CDbDealer.GetRecordsFromSQLAsync(sql,cmd);
            List<Dictionary<string, dynamic>> lstRows = new List<Dictionary<string, dynamic>>();
            
            foreach (dynamic record in lstRecords)
            {
                JObject jRecord =  JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(record));
                Dictionary<string, dynamic> row = new Dictionary<string, dynamic>();
                lstRows.Add(row);
                async Task SetRowByColumns(Dictionary<string,DataItem> columns)
                { 
                    foreach (KeyValuePair<string, DataItem> key in columns)
                    {
                        DataItem dataItem = key.Value;
                        if (!dataItem.GetFromDb) continue;
                        await dataItem.TryGetSubColumns();
                        if (dataItem.SubColumns != null)
                        {
                            await SetRowByColumns(dataItem.SubColumns); 
                        }
                        else
                        {
                           // object ov = jRecord[key.Key];
                            string value = jRecord[key.Key].ToString(); 
                            string label = "";
                            if (value != null) row[key.Key] = jRecord[key.Key].ToString();

                            if (dataItem.LabelFld != "")
                            {
                                if (dataItem.Source == "null")
                                {
                                    label = jRecord[dataItem.LabelFld].ToString();
                                   // if (ov != null) label = ov.ToString();
                                }
                                else
                                {
                                    //List<dynamic> lst = JsonSerializer.Deserialize<List<dynamic>>(dataItem.source);
                                    List<dynamic> lst = JsonConvert.DeserializeObject<List<dynamic>>(dataItem.Source);
                                    foreach (dynamic obj in lst)
                                    {
                                        if (obj.v == dataItem.Value)
                                        {
                                            label = obj.l; break;
                                        }
                                    }
                                }
                                row[dataItem.LabelFld] = label;
                            }
                        }
                     
                    }
                }
                await SetRowByColumns(Columns);
            }

            int rowCount = lstRows.Count;
            if (rowCount < MinRows) rowCount = MinRows;
        
            if (AutoAddRow && lstRows.Count >= MinRows) rowCount = lstRows.Count + 1;
            

            for (int i = lstRows.Count; i < rowCount; i++)
            {
                Dictionary<string, dynamic> row = new Dictionary<string, dynamic>();
                lstRows.Add(row);
                async Task SetRowByColumns(Dictionary<string, DataItem> _columns)
                {
                    foreach (KeyValuePair<string, DataItem> key in _columns)
                    {
                        DataItem dataItem = key.Value;
                        await dataItem.TryGetSubColumns();
                        if (dataItem.SubColumns != null)
                        { 
                            await SetRowByColumns(dataItem.SubColumns);
                        }
                        else
                        {
                            row[key.Key] = "";
                        }
                    }
                }
                await SetRowByColumns(Columns);

            }
            OnGridDataGot(lstRows);

            Dictionary<string, string> varsG = new Dictionary<string, string>();
            varsG["rows"] = JsonConvert.SerializeObject(lstRows);

            string cellRender = @" cellsrenderer: function(row, column, value) { 
                                 return '<div style=""height:100%;display:flex;justify-content:center;align-items:center;"">' + (value + 1) + '</div>';
                            } ";
            if (this.JSFixColumnCellRender != "")
            {
                cellRender = "cellsrenderer:" + this.JSFixColumnCellRender;
            }
            string sColumns = $@"{{
                            text: '', sortable: true, filterable: false, editable: false, pinned: true,
                            groupable: false, draggable: false, resizable: false,
                            datafield: '', columntype: 'number', width: {PinColumnWidth},
                            cellclassname: fixColCss,
                            {cellRender}
                           }}";

          
            //  bool bHaveLinkeBtn = false;
            //grid.Columns

            ColumnJsResult colRes = new ColumnJsResult() {sColumns=sColumns};
        
            async Task GetColumnsJS(Dictionary<string, DataItem> columns, string groupName, ColumnJsResult colRes, bool hideTopGroupName = false)
            {               
                foreach (KeyValuePair<string, DataItem> key in columns)
                {
                    DataItem dataItem = key.Value;
                    await dataItem .TryGetSubColumns();
                    if (dataItem.SubColumns != null) 
                    { 
                        await GetColumnsJS(dataItem.SubColumns, key.Key, colRes, dataItem.HideTopGroupName);
                        //if (sColumns != "") sColumns += ",";
                        // sColumns += cols;
                        if (!hideTopGroupName)
                        {
                            if (colRes.columnGroups != "") colRes.columnGroups += ",";
                            colRes.columnGroups += $"{{ text: '{dataItem.Title}', align: 'center', name: '{key.Key}'}}";
                        }
                        continue;
                    }



                    Dictionary<string, string> vars = new Dictionary<string, string>();
                    vars["datafield"] = key.Key;
                    vars["displayfield"] = ""; vars["title"] = dataItem.Title;
                    if (dataItem.LabelFld != "") { vars["displayfield"] = ", displayfield: '" + dataItem.LabelFld + "'"; }
                    vars["width"] = "";
                    if (dataItem.Width != "") { vars["width"] = ", width: '" + dataItem.Width + "'"; }
                    vars["cellsalign"] = "";
                    if (dataItem.CellsAlign != "") vars["cellsalign"] = $",cellsalign:'{dataItem.CellsAlign}'"; ;
                    vars["sortable"] = ",sortable:false";
                    if (dataItem.Sortable) vars["sortable"] = $",sortable:true"; ;


                    vars["gridID"] = gridID;
                    vars["cellsrenderer"] = "";
                    if (dataItem.Linkable)
                    {
                        vars["cellsrenderer"] =PageBaseModel.GetStringWithVar(@",cellsrenderer:function(row, columnfield, value, defaulthtml, columnproperties) { 
                        var height = $('#~gridID').jqxGrid('rowsheight');
                        return '<div style=""margin:4px;cursor:pointer; line-height:' + height + 'px;color: #0000ff;"">' + value + '</div>'; 
                       }", vars);
                    }
                    else if (dataItem.linkButtons != null && dataItem.linkButtons.Count > 0)
                    {
                        colRes.bHaveLinkeBtn = true;
                        var slbs = "";
                        foreach (var lb in dataItem.linkButtons)
                        {
                            var margin = "";
                            if (slbs != "") margin = "margin-left:10px;";
                            var slb = $@" <a style=""text-decoration:none; color:#555;{margin}"" href=""#"" onclick=""{gridID}_rowBtnClicked(${{row}},'{lb.id}');"">{lb.text}</a>";
                            slbs += slb;
                        }
                        string cellrender_orig = $@",cellsrenderer:function(row, columnfield, value, defaulthtml, columnproperties) {{ 
                          return `<div class=""linkBtn"" style=""margin:0px;display:none;width:100%;height:100%;text-align:center;align-items:center;justify-content:center;"">{slbs}</div>`;  
                        }}";
                        vars["cellsrenderer"] = PageBaseModel.GetStringWithVar(cellrender_orig, vars);
                    }


                    if (dataItem.JSCellRender != "")
                    {
                        vars["cellsrenderer"] = ",cellsrenderer:" + dataItem.JSCellRender;
                    }
                    vars["JSOther"] = "";
                    if (dataItem.JSOther != "")
                    {
                        vars["JSOther"] = dataItem.JSOther;
                    }

                    vars["columntype"] = "";
                    if (dataItem.columntype != "")
                    {
                        vars["columntype"] = ",columntype:'" + dataItem.columntype + "'";
                    }
                    vars["editable"] = "";
                    if (!dataItem.SaveToDB)
                    {
                       // vars["editable"] = ",editable:false";
                    }

                     vars["createeditor"] = ""; vars["initeditor"] = "";

                    string url = dataItem.Url; string source = dataItem.Source;
                    if (dataItem.SqlForOptions != "")
                    {
                        if (dataItem.GetOptionsOnLoad)
                            source = await PageBaseModel.GetDataItemOptions(cmd,pageForm.OperKey, Columns, key.Key, null, null,"");
                        else
                        {
                            string controllerName = pageForm.GetType().FullName.Replace("Model", "").Split(".").Last();
                            url = "../api/" + controllerName + $"/GetColumnOptions?operKey={pageForm.OperKey}&gridID={gridID}&colName={key.Key}";
                        }
                    }
                    vars["renderer"] = "";
                    if (dataItem.JSHeaderRender != "")
                    {
                        vars["renderer"] = ",renderer:" + dataItem.JSHeaderRender;
                    }
                    vars["cellsrenderer"] = "";
                    if (dataItem.JSCellRender != "")
                    {
                        vars["cellsrenderer"] = ",cellsrenderer:" + dataItem.JSCellRender;
                    }

                    vars["createeditor"] = "";
                    if (dataItem.JSCreateEditor != "")
                    {
                        vars["createeditor"] = ",createeditor:" + dataItem.JSCreateEditor;
                    }

                    vars["cellbeginedit"] = "";
                    if (dataItem.JSCellBeginEdit != "")
                    {
                        vars["cellbeginedit"] = ",cellbeginedit:" + dataItem.JSCellBeginEdit;
                    }
                    else if (!dataItem.EditableInFormGrid)
                    {
                        vars["cellbeginedit"] = @$",cellbeginedit:function(row, datafield, columntype, value) {{
                                      return false;
                                }}";
                    }

                    vars["cellendedit"] = "";
                    if (dataItem.JSCellEndEdit != "")
                    {
                        vars["cellendedit"] = ",cellendedit:" + dataItem.JSCellEndEdit;
                    }

                    vars["geteditorvalue"] = "";
                    if (url != "" || source != "null")
                    {
                        vars["columntype"] = ", columntype: 'template'";
                        Dictionary<string, string> varsE = new Dictionary<string, string>();
                        string ButtonUsage = "list";
                        string DropDownWidth = "160";                      
                        if (dataItem.DropDownWidth != "160") DropDownWidth = dataItem.DropDownWidth;
                        if (dataItem.ButtonUsage == "event") ButtonUsage = "event";                        

                        varsE["showHeader"] = dataItem.ShowDropDownColumnsHeader.ToString().ToLower();
                        varsE["DropDownWidth"] = DropDownWidth;
                        varsE["ButtonUsage"] = ButtonUsage;
                        varsE["ValueFld"] = key.Key; varsE["LabelFld"] = key.Key;
                        if (dataItem.LabelFld != "") { varsE["LabelFld"] = dataItem.LabelFld; }
                        if (dataItem.SearchFields != "") varsE["SearchFields"] = dataItem.SearchFields;
                        else varsE["SearchFields"] = "['" + dataItem.LabelFld + "']";
                        //varsE["SearchFields"] = dataItem.LabelFld;
                        varsE["Url"] = url; varsE["Source"] = source;
                        if (dataItem.JSCreateEditor == "")
                        {
                            vars["createeditor"] = @",createeditor:
                                function (row, cellvalue, editor, cellText, width, height) {
                                    var element = $('<div></div >');
                                    editor.append(element);
                                    var inputElement = editor.find('div')[0]; 
                                    var datafields = new Array({ datafield: '~LabelFld', text: '', width: 120 }); 
                                    $(inputElement).jqxInput({
                                        height: height, width: width,
                                        borderShape: 'none',
                                        buttonUsage: '~ButtonUsage', 
                                        dropDownHeight: 160,
                                        dropDownWidth:~DropDownWidth,
                                        displayMember: '~LabelFld',
                                        valueMember:  '~ValueFld',
                                        datafields: datafields,
                                        searchFields: ~SearchFields,
                                        maxRecords: 9,
                                        url:'~Url',
                                        source:~Source
                                    });
                                 }";
                            vars["createeditor"] = PageBaseModel.GetStringWithVar(vars["createeditor"], varsE);
                        }
                        vars["initeditor"] = @",initeditor: function(row, cellvalue, editor, celltext, pressedkey) { 
                            var inputField = editor.find('input'); 
                            if (pressedkey)
                            {
                                inputField.val(pressedkey);
                                inputField.jqxInput('selectLast');
                            }
                            else
                            { 
                                inputField.val({ value: cellvalue, label: celltext });
                                if(cellvalue=='') inputField.val('');
                                inputField.jqxInput('selectAll');
                            }
                           }";
                        if (dataItem.LabelFld != "")
                        {
                            vars["geteditorvalue"] = @",geteditorvalue: function(row, cellvalue, editor) {
                             var v = editor.find('input').val();
                              if(v==''){v={value:'',label:''}}
                             return v;}";
                        }
                        else
                        {
                            vars["geteditorvalue"] = @",geteditorvalue: function(row, cellvalue, editor) {
                             var v = editor.find('input').val(); 
                             return v;}";
                        }
                        
                    }


                    if (!dataItem.Hidden && (dataItem.ShowSum || dataItem.FuncGetSumValue != null))
                    {
                        if (dataItem.JsAggregatesRender == "")
                        {
                            dataItem.JsAggregatesRender = $@"
                                    function(aggregates, column, element, summaryData) {{
                                            var renderstring = `<div class='jqx-widget-content style='float: left; width: 100%; height: 100%; '>`;
                                            var sumValue=0;
                                            if(window.source_gridItems.sumResult) sumValue=window.source_gridItems.sumResult.{key.Key}||'0';
                            
                                            renderstring += `<div style='position: relative; margin: 6px; text-align: right; overflow: hidden;'>` +  sumValue + '</div>';
                                    
                                            renderstring += '</div>';
                                            return renderstring;
                                        }} 
                           ";
                        }
                    }

                    if (dataItem.JSCreateEditor != "")
                    {
                        vars["columntype"] = ",columntype:'template'";
                    }

                    if (dataItem.JSGetEditorValue != "")
                    {
                        vars["geteditorvalue"] = ",geteditorvalue:" + dataItem.JSGetEditorValue;
                    }
                
                    vars["aggregates_computer"] = "";
                    if (dataItem.JsAggregatesComputer != "")
                    {
                        vars["aggregates_computer"] = ",aggregates:" + dataItem.JsAggregatesComputer;
                    }
                    vars["aggregatesrenderer"] = "";
                    if (dataItem.JsAggregatesRender != "")
                    {
                        vars["aggregatesrenderer"] = ",aggregatesrenderer:" + dataItem.JsAggregatesRender;
                    }

                    vars["hidden"] = "";
                    if (dataItem.Hidden)
                    {
                        vars["hidden"] = ",hidden:true";
                    }

                    vars["hideOnLoad"] = "";
                    if (dataItem.HideOnLoad)
                    {
                        vars["hideOnLoad"] = ",hideOnLoad:true";
                    }

                    vars["alwaysShow"] = "";
                    if (dataItem.AlwaysShow)
                    {
                        vars["alwaysShow"] = ",alwaysShow:true";
                    }

                    vars["pinned"] = "";
                    if (dataItem.Pinned)
                    {
                        vars["pinned"] = ",pinned:true";
                    }

                    string columngroup = "";

                    string curGroupName = "";
                    if (groupName != "")
                    {
                        if (dataItem.SubMumTitle != "")//添加三级合并列的中级
                        {
                            curGroupName = groupName + "_" + dataItem.SubMumTitle;

                            if (!colRes.columnGroups.Contains(dataItem.SubMumTitle))
                            {
                                if (colRes.columnGroups != "") colRes.columnGroups += ",";
                                string mumGroup = "";
                                if (!hideTopGroupName) mumGroup = groupName;
                                colRes.columnGroups += $"{{ text: '{dataItem.SubMumTitle}', align: 'center', name: '{curGroupName}',parentgroup: '{mumGroup}'}}";
                            }
                        }
                        else curGroupName = groupName;
                    }

                    if (curGroupName != "") columngroup = $",columngroup:'{curGroupName}'";
                    vars["columngroup"] = columngroup;

                    string col = @"{text: '~title' ~columngroup~columntype~editable,datafield: '~datafield'~displayfield~width~cellsalign~sortable,align:'center'~hidden~hideOnLoad~alwaysShow~pinned~cellsrenderer~aggregates_computer~aggregatesrenderer~columntype~hidden~createeditor,align:'center'~initeditor~cellsrenderer~renderer~createeditor~cellbeginedit~geteditorvalue~JSOther}";
                  
                    col = PageBaseModel.GetStringWithVar(col, vars);
                    if (colRes.sColumns != "") colRes.sColumns += ",";
                    colRes.sColumns += col;
                }
            }
          
            await GetColumnsJS(Columns, "", colRes);
            /*foreach (KeyValuePair<string, DataItem> key in Columns)
            {
                DataItem dataItem = key.Value;
                Dictionary<string, string> vars = new Dictionary<string, string>();
                vars["datafield"] = key.Key;
                vars["DisplayField"] = ""; vars["Title"] = dataItem.Title;
                if (dataItem.LabelFld != "") { vars["DisplayField"] = ", DisplayField: '" + dataItem.LabelFld + "'"; }
                vars["Width"] = "";
                if (dataItem.Width != "") { vars["Width"] = ", width: '" + dataItem.Width + "'"; }
                
                vars["columntype"] = ""; vars["createeditor"] = ""; vars["initeditor"] = "";

                string url = dataItem.Url; string source = dataItem.Source;
                if (dataItem.SqlForOptions != "")
                {
                    if (dataItem.GetOptionsOnLoad)
                        source = await PageBaseModel.GetDataItemOptions(pageForm.OperKey, Columns, key.Key, null, null, cmd);
                    else
                    {
                        string controllerName = pageForm.GetType().FullName.Replace("Model", "").Split(".").Last();
                        url = "../api/" + controllerName + $"/GetColumnOptions?operKey={pageForm.OperKey}&gridID={gridID}&colName={key.Key}";
                    }
                }
                vars["renderer"] = "";
                if (dataItem.JSHeaderRender != "")
                {
                    vars["renderer"] = ",renderer:" + dataItem.JSHeaderRender;
                }
                vars["cellsrenderer"] = "";
                if (dataItem.JSCellRender != "")
                {
                    vars["cellsrenderer"] = ",cellsrenderer:" + dataItem.JSCellRender;
                }
                vars["cellbeginedit"] = "";
                if (dataItem.JSCellBeginEdit != "")
                {
                    vars["cellbeginedit"] = ",cellbeginedit:" + dataItem.JSCellBeginEdit;
                }
                if (url != "" || source != "null")
                {
                    vars["columntype"] = ", columntype: 'template'";
                    Dictionary<string, string> varsE = new Dictionary<string, string>();
                    string ButtonUsage = "list";
                    if (dataItem.ButtonUsage == "event") ButtonUsage = "event";
                    varsE["ButtonUsage"] = ButtonUsage;
                    varsE["ValueFld"] = key.Key; varsE["LabelFld"] = key.Key;
                    if (dataItem.LabelFld != "") { varsE["LabelFld"] = dataItem.LabelFld; }
                    varsE["Url"] = url; varsE["Source"] = source;

                    vars["createeditor"] = @",createeditor:
                    function (row, cellvalue, editor, cellText, width, height) {
                        var element = $('<div></div >');
                        editor.append(element);
                        var inputElement = editor.find('div')[0]; 
                        var datafields = new Array({ datafield: '~LabelFld', text: '', width: 120 }); 
                        $(inputElement).jqxInput({
                            height: height, width: width,
                            borderShape: 'none',
                            buttonUsage: '~ButtonUsage', 
                            dropDownHeight: 160,
                            displayMember: '~LabelFld',
                            valueMember:  '~ValueFld',
                            datafields: datafields,
                            searchFields: ['~LabelFld'],
                            maxRecords: 9,
                            url:'~Url',
                            source:~Source
                        });
                     }";
                    vars["createeditor"] = PageBaseModel.GetStringWithVar(vars["createeditor"], varsE);
                    vars["initeditor"] = @",initeditor: function(row, cellvalue, editor, celltext, pressedkey) { 
                            var inputField = editor.find('input');
                            if (pressedkey)
                            {
                                inputField.val(pressedkey);
                                inputField.jqxInput('selectLast');
                            }
                            else
                            {
                                inputField.val({ value: cellvalue, label: celltext });
                                if(cellvalue=='') inputField.val('');
                                inputField.jqxInput('selectAll');
                            }
                           },
                        geteditorvalue: function(row, cellvalue, editor) {
                             var v = editor.find('input').val();
                              return v;}";
                }
                vars["hidden"] = "";
                if (dataItem.Hidden)
                {
                    vars["hidden"] = ",hidden:true";
                }
                string col = @"{text: '~Title', datafield: '~datafield'~DisplayField~Width~columntype~hidden~createeditor,align:'center'~initeditor~cellsrenderer~renderer~cellbeginedit
                   }";
                col = PageBaseModel.GetStringWithVar(col, vars);
                if (sColumns != "") sColumns += ",";
                sColumns += col;
            }
            */

            sColumns = "[" + colRes.sColumns + "]";

            if (colRes.columnGroups != "") colRes.columnGroups = ",columngroups:[" + colRes.columnGroups + "]";
            varsG["columnGroups"] = colRes.columnGroups;

            varsG["columns"] = sColumns; varsG["rowCount"] = rowCount.ToString(); varsG["gridID"] = gridID;
            varsG["height"] = "";
            varsG["width"] = "";
            if (Width > 0)
                varsG["width"] = $"width:{Width},";
            else
                varsG["width"] = $"width:getWidth('~gridID'),";
            if (Height>0)
                varsG["height"] =$"height:{Height},";
             else
                varsG["height"] = $"height: getHeight('~gridID'),";
           varsG["AutoAddRow"] = this.AutoAddRow.ToString().ToLower();
           varsG["MinEmptyRows"] = this.MinEmptyRows.ToString();
           varsG["AddRowsCount"] = this.AddRowsCount.ToString();
           varsG["AllowInsertRemoveRow"] = this.AllowInsertRemoveRow.ToString().ToLower();
           varsG["AllowDragRow"] = this.AllowDragRow.ToString().ToLower();
           varsG["HaveContextMenu"] = this.HaveContextMenu.ToString().ToLower();
           varsG["ContextMenuLiHtml"] = this.ContextMenuLiHtml.ToString();
           varsG["SelectionMode"] = this.SelectionMode;
           varsG["EditMode"] = this.EditMode;

            
            scriptCreateGrid = @"
          window.getWidth=function(ctl){
             return document.getElementById(ctl).offsetWidth;
           };
           window.getHeight=function(ctl){
             var ht = document.getElementById(ctl).clientHeight;
             return ht;
           };
            if(true){
            var theme = ''; var datafields = []; 
            var rows=~rows;
            if(window.dealGridRowsOnLoad){
                 rows=window.dealGridRowsOnLoad(rows,~gridID)
           }
            var source =
            {
                sort: funcSortByColumn,
                localdata: rows,
                unboundmode: true,
                totalrecords: ~rowCount,
                datafields: datafields 
            };
            var dataAdapter = new $.jqx.dataAdapter(source);   
            var fixColCss = 'jqx-widget-header';
            if (theme != '') fixColCss += ' jqx-widget-header-' + theme;
             var columns=~columns 
               $('#~gridID').jqxGrid(
                {               
                    ~width
                    ~height                      
                    source:dataAdapter,
                    pageable:false, 
                    sortable:true,
                    editable:true,
                    columnsresize:true,
                    editmode:'~EditMode',
                    selectionmode:'~SelectionMode',
                    theme: theme,
                    columns:columns
                     ~columnGroups
                });
            }

              $('#~gridID').on('contextmenu', function () {
                            return false;
              });
              var contextMenu =null
              if(~HaveContextMenu){

                  var divMenu=`<div id='gridMenu_~gridID'>
                    <ul>
                        ~ContextMenuLiHtml 
                    </ul>
                   </div>`;
                   $('body').append(divMenu);
                   contextMenu = $('#gridMenu_~gridID').jqxMenu({ width: 200, height: 58, autoOpenPopup: false, mode: 'popup'});
            } 
            if(~AllowInsertRemoveRow){
                 $('#~gridID').jqxGrid({cellhover:cellhover});
              }

             if(~AllowDragRow){
                 enableDragRow('~gridID')
             } 

             $('#~gridID').on('rowclick', function (event) {
                 if (event.args.rightclick) {
                      if(~HaveContextMenu){

                         $('#~gridID').jqxGrid('selectrow', event.args.rowindex);
                         var scrollTop = $(window).scrollTop();
                         var scrollLeft = $(window).scrollLeft();

                         contextMenu.jqxMenu('open', parseInt(event.args.originalEvent.clientX) + 5 + scrollLeft, parseInt(event.args.originalEvent.clientY) + 5 + scrollTop);
                         return false;
                      }
                  }
                  else if(~AutoAddRow){
                        addEmptyRowsAtTail('~gridID',columns, ~MinEmptyRows, ~AddRowsCount)
                  }
                  
              });
             $('#~gridID').on('cellbeginedit', function (event) { 
                 var args = event.args;  
                 var rowIndex = args.rowindex;   
                 if(~AutoAddRow){
                    addEmptyRowsAtTail('~gridID',columns,rowIndex, ~MinEmptyRows, ~AddRowsCount)
                 }
                  
              });
                $('#gridMenu_~gridID').on('itemclick', function (event) {
                        var args = event.args;
                        var rowindex = $('#~gridID').jqxGrid('getselectedrowindex');
                        var menuItemID=$(args).attr('id')
                        if (menuItemID == 'remove') {
                                
                            var rowid = $('#~gridID').jqxGrid('getrowid', rowindex);
                            $('#~gridID').jqxGrid('deleterow', rowid);
                        }
                        else{                          
                                windw.onGridRowContextMenuClick('~gridID',menuItemID, rowindex)                           
                        }
                });

             ";

            if (colRes.bHaveLinkeBtn)
            {
                scriptCreateGrid += $@"
                      var $lastLinkBtn = null;
                $('#{gridID}').jqxGrid({{
                    cellhover: function(cellhtmlElement, x, y) {{
                        var ele = cellhtmlElement;
                        var $linkBtn = $(ele).find('.linkBtn');
                        if ($linkBtn.length > 0) {{ 
                        $linkBtn[0].style.display ='flex';
                            if ($lastLinkBtn != null)  $lastLinkBtn[0].style.display = 'none';
                        $lastLinkBtn = $linkBtn;
                        }}
                    }}
                }}); 
                ";
            }

            scriptCreateGrid = PageBaseModel.GetStringWithVar(scriptCreateGrid, varsG);

            string scriptSetFlds = ""; string scriptCheckRowEmpty = "";string wholeRowEmpty = "";
            bool bHaveNecessaryColumn = false;

            async Task setRowByCol(Dictionary<string,DataItem> _columns)
            {
                foreach (KeyValuePair<string, DataItem> key in _columns)
                {
                    DataItem dataItem = key.Value;
                    await dataItem .TryGetSubColumns();
                    if (dataItem.SubColumns != null)
                    { 
                        await setRowByCol(dataItem.SubColumns);
                    }
                    else
                    {
                        if (dataItem.Necessary)
                        {
                            bHaveNecessaryColumn = true;
                            if (scriptCheckRowEmpty != "") scriptCheckRowEmpty += " || ";
                            {
                                scriptCheckRowEmpty += " row1." + key.Key + ".trim()==''";
                               // scriptCheckRowEmpty += $" !row1.{key.Key} || row1.{key.Key}.trim()==''";

                            }
                        }
                        if (wholeRowEmpty != "") wholeRowEmpty += " && ";
                        {
                            wholeRowEmpty += " row1." + key.Key + ".trim()==''";
                        }
                        
                        scriptSetFlds += "row." + key.Key + "=row1." + key.Key + ";";
                        if (dataItem.LabelFld != "")//LabelFld
                        {
                            scriptSetFlds += "row." + dataItem.LabelFld + "=row1." + dataItem.LabelFld + ";";
                        }
                    }
                    
                }
            } 
            await setRowByCol(this.Columns);
            if (scriptCheckRowEmpty == "") scriptCheckRowEmpty = wholeRowEmpty;
            if (!bHaveNecessaryColumn)
            {
           //     throw (new Exception("表格必须至少指定一列必填的列"));
            }
            scriptSaveGrid = @" 
if(true){
var gridRows=$('#~gridID').jqxGrid('getrows');
var saveRows=new Array();
for(var i=0;i<gridRows.length;i++)
{
    var row1=gridRows[i];
    if(~scriptCheckRowEmpty){
       continue;
    }
   var row={};
   
   ~scriptSetFlds
    saveRows.push(row);
}
formFlds.~gridID=saveRows;

}

            ";
            varsG = new Dictionary<string, string>();
            varsG["gridID"] = gridID;
            varsG["scriptSetFlds"] = scriptSetFlds;
            varsG["scriptCheckRowEmpty"] = scriptCheckRowEmpty;

          
            scriptSaveGrid = PageBaseModel.GetStringWithVar(scriptSaveGrid, varsG);
        }
    }
    public class PageFormModel : PageBaseModel
    {
        // public Dictionary<string, string> m_dicFldAreaCtrls = new Dictionary<string, string>();
        // public Dictionary<string, DataItem> DataItems = new Dictionary<string, DataItem>();
        public PageFormModel(MenuId pageMenuID) : base(pageMenuID)
        {

        }
        public Dictionary<string, FormDataGrid> Grids = new Dictionary<string, FormDataGrid>();
        public string m_selectFromSQL = "";

        public string m_idFld = "", m_nameFld = "", m_codeFld = "", m_tableName = "";
        public bool AllowSameName = false;
        public bool SaveMainTable = true;
        public bool NoIDFld = false;//not't need IDFld,there is only a single record for each company. NoIDFld is here together with IDFld property to prevent programmers make mistakes

        public bool IdFldIsSerial = true;
        public JObject record = new JObject();

        public string m_saveCloseScript = "";
        public string m_createGridScript = ""; public string m_saveGridScript = "";
        public string GetFormDataScript = "";
        public bool m_bNewRecord = false;
        public bool LogChange = true;
        public string DocType = "";
        public enum APPROVE_STATUS
        {
            EMPTY,
            CREATE,
            EDIT,
            DELETE,
            CREATE_AND_APPROVED,
            EDIT_AND_APPROVED,
            DELETE_AND_APPROVED,
            APPROVED_FROM_CREATE,
            APPROVED_FROM_EDIT,
            APPROVED_FROM_DELETE,
            REFUSED_FROM_EDIT,
            REFUSED_FROM_CREATE,
            REFUSED_FROM_DELETE,
        }
        public override async Task GetJavaScripts(CMySbCommand cmd, bool bGetFldAreaCtrls = false)
        {
          //  await OnDataItemsGotFromSheet(cmd);

            await GetScriptsForDataItems(cmd, bGetFldAreaCtrls);
            Dictionary<string, string> vars = null;
            if (Grids != null)
            {
                foreach (KeyValuePair<string, FormDataGrid> kp in Grids)
                {
                    string idFldValue = "";
                    if(m_idFld!="")  idFldValue = DataItems[m_idFld].Value;
                    await kp.Value.GetScript(cmd, this, kp.Key, idFldValue, company_id); 
                    m_createGridScript += kp.Value.scriptCreateGrid;
                    m_saveGridScript += kp.Value.scriptSaveGrid; 
                }
                m_getDataItemsScript  += m_saveGridScript;
            }
           // await OnAllDataLoaded(cmd);

          //  m_getDataItemsScript  += "\r\n" + "var formFlds = {operKey:'~operKey'};";
          //  m_getDataItemsScript  += "\r\n" + "formFlds.isNewRecord = m_bNewRecord;";
            string msgHead = this.GetType().FullName.Replace("Model", "").Split(".").Last();
            
            GetFormDataScript = @$"
                function getFormData(){{
                   var formFlds = {{operKey:'{OperKey}'}};
                   formFlds.isNewRecord = m_bNewRecord;
                   {m_getDataItemsScript}
                   return formFlds;
                }}
            ";
            
            m_saveCloseScript = @" 
            var m_bNewRecord = ~m_bNewRecord;
            ~GetFormDataScript 
            function btnSave_Clicked(bCopy,callback)
            { 
                debugger
                var formFlds =getFormData();
                if(formFlds.errMsg){
                     return;
                }
                try{ 
                    if(typeof(eval(checkDataValid))=='function') {
                        var bOK = checkDataValid(formFlds);
                        if(!bOK) return;
                    }
                }catch(e){}

                if(window.dealFormData && typeof(eval(window.dealFormData))=='function') {
                      dealFormData(formFlds); 
                }
                $.ajax({
                    url: '../api/~msgHead/Save',
                type: 'POST',
                contentType: 'application/json', 
                data: JSON.stringify(formFlds), 
                success: function(data) {
                        if (data.result == 'OK')
                        {
                            var action = 'update';
                            if (m_bNewRecord)
                            {
                                action = 'add'; 
                                function setFld(obj1,obj2,fld){
                                     obj1[fld]=obj2[fld]
                                }
                                if('~idFld')
                                  setFld(formFlds,data,'~idFld')  
                            }
                            var msg = { msgHead: '~msgHead', action: action, record: data.record,bCopy:bCopy,formData:formFlds};
                            if(window.parent)
                               window.parent.postMessage(msg, '*'); 
                            if(window.onFormSaved) window.onFormSaved(msg)   
                            if(bCopy){
                               bw.toast('已复制') 
                            }
                            
                       } 
                        else{
                              bw.toast(data.msg,5000);
                        }
                        if(callback) callback(data)
                },
                 error: function (response, ajaxOptions, thrownError) {
                        bw.toast('error'+response);
                  }
    }); 
            } 
            function btnClose_Clicked()
            {
                var msg = { msgHead: '~msgHead', action: 'close'};
                window.parent.postMessage(msg, '*');
            }";
            vars = new Dictionary<string, string>();
            vars["idFld"] = m_idFld;
            vars["GetFormDataScript"] = GetFormDataScript;
            vars["m_bNewRecord"] = m_bNewRecord.ToString().ToLower(); vars["msgHead"] = msgHead; vars["m_getDataItemsScript "] = m_getDataItemsScript ;
            vars["operKey"] = OperKey; 
            m_saveCloseScript = PageBaseModel.GetStringWithVar(m_saveCloseScript, vars);
        }
        static async Task<string> GetFldsFromDataItems(Dictionary<string, DataItem> dicDataItems,string tableName)
        {
            string sql_flds = "";
            foreach (KeyValuePair<string, DataItem> key in dicDataItems)
            {
                DataItem dataItem = key.Value;
                if (dataItem != null)
                {
                    await dataItem.TryGetSubColumns();
                    if (dataItem.SubColumns != null)
                    { 
                        if (dataItem.SubColumns.Count>0)
                        {
                            string sql_flds1=await GetFldsFromDataItems(dataItem.SubColumns, tableName); 
                            if (sql_flds1 != "")
                            {
                                if (sql_flds != "") sql_flds += ",";
                                sql_flds += sql_flds1;
                            }
                        } 
                    }
                    else
                    {
                        if (!dataItem.GetFromDb) continue;
                        void getSqlFlds(string fld)
                        {
                            if (sql_flds != "") sql_flds += ",";
                            sql_flds += fld;
                        }
                        string valueFld = key.Key;
                        //valueFld = tableName + "." + valueFld;
                        //valueFld =  valueFld;
                        if(dataItem.SaveToDB && !valueFld.Contains(".")) valueFld = tableName + "." + valueFld;
                        if (dataItem.SqlFld != "") valueFld = dataItem.SqlFld + " as " + key.Key;
                        getSqlFlds(valueFld);
                        if (dataItem.LabelFld != "" && dataItem.LabelInDB)
                        {
                            getSqlFlds(dataItem.LabelFld);
                        }
                        if (dataItem.TreePathFld != "" && dataItem.TreePathFromDb)
                        {
                            getSqlFlds(dataItem.TreePathFld);
                        }
                    }
                   
                }
            }
            return sql_flds;

        }
        public static async Task<string> GetSQLFromDataItems(string companyID, Dictionary<string, DataItem> dicDataItems, string tableName, string sqlFrom)
        {
            string sql_flds = await GetFldsFromDataItems(dicDataItems,tableName);
             
            string sql;
            if (sqlFrom.StartsWith("select"))
                sql = sqlFrom.Replace("~SQL_FLDS", sql_flds);
            else 
                sql= "select " + sql_flds + " " + sqlFrom;
            sql = sql.Replace("~COMPANY_ID", companyID, StringComparison.OrdinalIgnoreCase);

            //如果检索条件中没有company_id就给它加上
            int n = sql.LastIndexOf(" where ");
            if(n>0)
            {
                sql = sql.Replace("company_id =", "company_id=");
                int n1=sql.IndexOf("company_id=",n);
                if (n1 == -1)
                {
                    string companyCondi = $" and {tableName}.company_id=" + companyID;
                    int n2 = sql.IndexOf(" order by ", n);
                    if (n2 > 0)
                    {
                        string left = sql.Substring(0, n2);
                        string right = sql.Substring(n2, sql.Length - n2);
                        sql = left + companyCondi + right;
                    }
                    else sql += companyCondi;
                 
                }
            } 
            return sql;
        }
        public async Task<bool> GetDataItemsFromDB(CMySbCommand cmd, string sqlFrom)
        {
            string sql =await GetSQLFromDataItems(company_id, DataItems, m_tableName, sqlFrom);
            cmd.CommandText = sql;
            CMySbDataReader dr = await cmd.ExecuteReaderAsync();
            if (dr.Read())
            {
                foreach (KeyValuePair<string, DataItem> key in DataItems)
                {
                    DataItem dataItem = key.Value;
                    object ov = dr[key.Key];
                    if (ov!=null && ov != DBNull.Value) dataItem.Value = ov.ToString().Trim();
                    string label = "";
                    if (dataItem.LabelFld != "" && dataItem.LabelInDB)
                    { 
                        ov = dr[dataItem.LabelFld];
                        if (ov != DBNull.Value && ov != null) dataItem.Label = label= ov.ToString(); 
                    }
                    if (label == "" && dataItem.Source != "" && dataItem.Source != "null")
                    {
                        JArray opts = (JArray)JsonConvert.DeserializeObject(dataItem.Source);
                        foreach (dynamic opt in opts)
                        {
                            if (dataItem.Value == (string)opt.v)
                            {
                                dataItem.Label = opt.l;
                                break;
                            }
                        }
                    }
                    if (dataItem.TreePathFld != "" && dataItem.TreePathFromDb)
                    {
                        ov = dr[dataItem.TreePathFld];
                        if (ov != DBNull.Value) dataItem.TreePath = ov.ToString();
                    }
                }
                dr.Close();
                return true;
            }
            else
            {
                dr.Close();
                return false;
            }
        }

        public class FormRecord
        {
            public dynamic Info;
            public Dictionary<string, List<ExpandoObject>> Grids=new Dictionary<string, List<ExpandoObject>>();
        }
        public async Task<FormRecord> GetRecord(CMySbCommand cmd, string operKey, string IdValue)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            //string sqlFlds = "";
            //dynamic info = null;
            FormRecord record = new FormRecord();
            if (IdValue != "" && m_tableName!="")
            {
                this.company_id = companyID; 
                string sql = await GetSQLFromDataItems(companyID, DataItems, m_tableName, this.m_selectFromSQL);
                sql = sql.Replace("~ID", IdValue);
                /*
                if (m_tableName != "")
                {
                    foreach (KeyValuePair<string, DataItem> d in DataItems)
                    {
                        DataItem dataItem = d.Value;
                        if (dataItem.SaveToDB && dataItem.CtrlType != "hidden")
                        {
                            if (sqlFlds == "")
                            {
                                sqlFlds += m_tableName + "." + d.Key;
                            }
                            else
                            {
                                sqlFlds += "," + m_tableName + "." + d.Key;
                            }
                            if (dataItem.LabelFld != "" && dataItem.LabelInDB)
                            {
                                if (sqlFlds == "")
                                {
                                    sqlFlds += dataItem.LabelFld;
                                }
                                else
                                {
                                    sqlFlds += "," + dataItem.LabelFld;
                                }
                            }
                            if (dataItem.TreePathFld != "" && dataItem.TreePathFromDb)
                            {
                                if (sqlFlds == "")
                                {
                                    sqlFlds += dataItem.TreePathFld;
                                }
                                else
                                {
                                    sqlFlds += "," + dataItem.TreePathFld;
                                }
                            }

                        }

                    }
                }
                string sql = $@"select {sqlFlds} {m_selectFromSQL};";
                if (IdValue == "") sql = sql.Replace("~ID", "-1");
                else sql = sql.Replace("~ID", IdValue);
                sql = sql.Replace("~COMPANY_ID", companyID); 
                */
                record.Info = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            }
            dynamic lstRecords = null;
            string gridSql = "";
            foreach (KeyValuePair<string, FormDataGrid> kg in Grids)
            {
                FormDataGrid grid = kg.Value;
                string SelectFromSQL = grid.SelectFromSQL;
                if (IdValue == "") SelectFromSQL = SelectFromSQL.Replace("~ID", "-1");
                else SelectFromSQL = SelectFromSQL.Replace("~ID", IdValue);
                Dictionary<string, DataItem> columns = grid.Columns;
                gridSql = await PageFormModel.GetSQLFromDataItems(companyID, columns, grid.TableName, SelectFromSQL);
                if (gridSql != "")
                {
                    cmd.CommandText = gridSql;
                    lstRecords = await CDbDealer.GetRecordsFromSQLAsync(gridSql, cmd);
                    record.Grids.Add(kg.Key, lstRecords);
                }
            }
           
            return record;// { info = info, gridInfo = lstRecords };
        }

        public async Task<dynamic> GetRecord_old(CMySbCommand cmd, string operKey, string IdValue)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sqlFlds = "";
            dynamic info = null;
            if (m_tableName != "")
            {
                foreach (KeyValuePair<string, DataItem> d in DataItems)
                {
                    DataItem dataItem = d.Value;
                    if (dataItem.SaveToDB && dataItem.CtrlType != "hidden")
                    {
                        if (sqlFlds == "")
                        {
                            sqlFlds += m_tableName + "." + d.Key;
                        }
                        else
                        {
                            sqlFlds += "," + m_tableName + "." + d.Key;
                        }
                        if (dataItem.LabelFld != "" && dataItem.LabelInDB)
                        {
                            if (sqlFlds == "")
                            {
                                sqlFlds += dataItem.LabelFld;
                            }
                            else
                            {
                                sqlFlds += "," + dataItem.LabelFld;
                            }
                        }
                        if (dataItem.TreePathFld != "" && dataItem.TreePathFromDb)
                        {
                            if (sqlFlds == "")
                            {
                                sqlFlds += dataItem.TreePathFld;
                            }
                            else
                            {
                                sqlFlds += "," + dataItem.TreePathFld;
                            }
                        }

                    }
                  
                }
            }
            string sql = $@"select {sqlFlds} {m_selectFromSQL};";
            if (IdValue == "") sql = sql.Replace("~ID", "-1");
            else sql = sql.Replace("~ID", IdValue);
            sql = sql.Replace("~COMPANY_ID", companyID);
            info = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            dynamic lstRecords = null;
            string gridSql = "";
            foreach (KeyValuePair<string, FormDataGrid> kg in Grids)
            {
                FormDataGrid grid = kg.Value;
                string SelectFromSQL = grid.SelectFromSQL;
                if (IdValue == "") SelectFromSQL = SelectFromSQL.Replace("~ID", "-1");
                else SelectFromSQL = SelectFromSQL.Replace("~ID", IdValue);
                Dictionary<string, DataItem> columns =grid.Columns;
                gridSql += await PageFormModel.GetSQLFromDataItems(companyID, columns, grid.TableName, SelectFromSQL);
            }
            if (gridSql != "")
            {
                cmd.CommandText = gridSql;
                lstRecords= await CDbDealer.GetRecordsFromSQLAsync(gridSql,cmd);
            }
            return new { info=info,gridInfo = lstRecords};
        }
        public APPROVE_STATUS SetApproveStatus(string approve_status)
        {
            switch (approve_status)
            {
                case "CREATE":
                   return APPROVE_STATUS.CREATE;
                case "EDIT":
                    return APPROVE_STATUS.EDIT;
                case "CREATE_AND_APPROVED":
                    return APPROVE_STATUS.CREATE_AND_APPROVED;
                case "EDIT_AND_APPROVED":
                    return APPROVE_STATUS.EDIT_AND_APPROVED;
                case "APPROVED_FROM_CREATE":
                    return APPROVE_STATUS.APPROVED_FROM_CREATE;
                case "APPROVED_FROM_EDIT":
                   return APPROVE_STATUS.APPROVED_FROM_EDIT;
                case "REFUSED_FROM_EDIT":
                    return APPROVE_STATUS.REFUSED_FROM_EDIT;
                case "REFUSED_FROM_CREATE":

                   return APPROVE_STATUS.REFUSED_FROM_CREATE;
                default:
                    return APPROVE_STATUS.EMPTY;

            }
        }
        public virtual Dictionary<string, dynamic> GetGridVariances(Dictionary<string, List<ExpandoObject>> newGrids, Dictionary<string, List<ExpandoObject>>  oldGrids)
        {
            string jsonNew = JsonConvert.SerializeObject(newGrids);
            string jsonOld = JsonConvert.SerializeObject(oldGrids);
            if (jsonNew != jsonOld)
            {
                var diff= new Dictionary<string, dynamic>();
                dynamic d = new {oldValue= oldGrids, newValue= newGrids };
                diff.Add("grid", d);
                return diff;
            }
            return new Dictionary<string, dynamic>() ;
        }
        public async Task SaveLog(CMySbCommand cmd, string operKey,FormRecord origInfo,string approve_flag, string approve_brief,string flow_id,string receiver_id,string msg_id)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            //todo:商品档案grid不能识别变化
            string idValue = "";
            if (m_idFld != "") idValue = DataItems[m_idFld].Value;
            var newInfo = await GetRecord(cmd, operKey, idValue);
            Dictionary<string, dynamic> variances = new Dictionary<string, dynamic>();
            CDbDealer dbLog = new CDbDealer();
            string msgTitle = @$"";
            string msgClass = "";
            string msgType = "";
            string msgSubType = "";
            string receiverId = "";
            dynamic baseInfo = origInfo.Info;
            dynamic newBaseInfo = newInfo.Info;
            Dictionary<string, List<ExpandoObject>>   gridInfo = new Dictionary<string, List<ExpandoObject>>();
            Dictionary<string, List<ExpandoObject>>   newGridInfo =new Dictionary<string, List<ExpandoObject>>();
            if(origInfo.Grids != null) gridInfo = JsonConvert.DeserializeObject<Dictionary<string, List<ExpandoObject>>>(JsonConvert.SerializeObject(origInfo.Grids));
            if(newInfo.Grids!=null) newGridInfo =JsonConvert.DeserializeObject<Dictionary<string, List<ExpandoObject>>>(JsonConvert.SerializeObject(newInfo.Grids));
            variances = GetGridVariances(newGridInfo, gridInfo);
            if (baseInfo!=null)
            { 
                
                foreach (dynamic origItem in baseInfo)
                {
                    string origKey = origItem.Key;
                    foreach (dynamic newItem in newBaseInfo)
                    {
                        string newKey = newItem.Key;
                        if (newKey == origKey)
                        {
                            if (origItem.Value != newItem.Value)
                            {
                                Dictionary<String, String> variance = new Dictionary<String, String>();
                                variance.Add("newValue", newItem.Value);
                                variance.Add("oldValue", origItem.Value);
                                variances.Add(origKey, variance);
                            }
                            break;
                        }
                    }
                }
            }
            APPROVE_STATUS approveStatus = SetApproveStatus(approve_flag);
            if (variances!=null && variances.Count != 0)
            {
                dbLog.AddField("diff_describe", Newtonsoft.Json.JsonConvert.SerializeObject(variances));
            }
            else if(approveStatus == APPROVE_STATUS.EDIT || approveStatus == APPROVE_STATUS.EDIT_AND_APPROVED)
            {
                cmd.CommandText = @$"update {m_tableName} set approve_status = null where company_id = {companyID} and {m_idFld} = {DataItems[m_idFld].Value}";
                await cmd.ExecuteNonQueryAsync();
                return;
            }
            if (approveStatus == APPROVE_STATUS.CREATE || approveStatus == APPROVE_STATUS.EDIT || approveStatus == APPROVE_STATUS.DELETE)
            {
                dbLog.AddField("approve_status", "wait approve");
                msgClass = "todo";
                msgType = DocType + "Approve";
                receiverId = "";
                if(approveStatus == APPROVE_STATUS.CREATE)
                {
                    dbLog.AddField("oper_action", "CREATE");
                    msgSubType = DocType + "Create";
                    msgTitle = @$"新增了一条{PageTitle}：{DataItems[m_nameFld].Value}，请审核";
                }
                else if(approveStatus == APPROVE_STATUS.EDIT)
                {
                    dbLog.AddField("oper_action", "EDIT");
                    msgSubType = DocType + "Edit";
                    msgTitle = @$"{PageTitle}被修改了：{DataItems[m_nameFld].Value}，请审核";
                }else if(approveStatus == APPROVE_STATUS.DELETE)
                {
                    dbLog.AddField("oper_action", "DELETE");
                    msgSubType = DocType + "DELETE";
                    msgTitle = @$"{PageTitle}被修改了：{DataItems[m_nameFld].Value}，请审核";
                }
            }
            else if (approveStatus == APPROVE_STATUS.CREATE_AND_APPROVED || approveStatus == APPROVE_STATUS.EDIT_AND_APPROVED || approveStatus == APPROVE_STATUS.DELETE_AND_APPROVED)
            {
                if(approveStatus == APPROVE_STATUS.CREATE_AND_APPROVED)
                {
                    dbLog.AddField("oper_action", "CREATE");
                }
                else if(approveStatus == APPROVE_STATUS.EDIT_AND_APPROVED)
                {
                    dbLog.AddField("oper_action", "EDIT");
                }
               
                dbLog.AddField("approve_status", "approved");
                dbLog.AddField("approve_time", CPubVars.GetDateText(DateTime.Now));
                dbLog.AddField("approve_brief", approve_brief);
                dbLog.AddField("approver_id", operID);
            }
            else if (approveStatus == APPROVE_STATUS.APPROVED_FROM_CREATE || approveStatus == APPROVE_STATUS.APPROVED_FROM_EDIT || approveStatus == APPROVE_STATUS.APPROVED_FROM_DELETE)
            {
                if (approveStatus == APPROVE_STATUS.APPROVED_FROM_CREATE)
                {
                    msgSubType = DocType + "Create";
                }
                else if(approveStatus == APPROVE_STATUS.APPROVED_FROM_EDIT)
                {
                    msgSubType = DocType + "Edit";
                }
                string updateOldLog = $@"update document_change_log set approve_status = 'approved',approver_id={operID},approve_time='{CPubVars.GetDateText(DateTime.Now)}',approve_brief = '{approve_brief}' where company_id = {companyID} and flow_id = {flow_id};";
                cmd.CommandText = updateOldLog;
                await cmd.ExecuteNonQueryAsync();
                Dictionary<string, dynamic> messageResult = await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey = operKey,
                    msgId = msg_id,
                    obj_id = DataItems[m_idFld].Value,
                    msgClass = "todo",
                    msgType = DocType + "Approve",
                    msgSubType,
                }, cmd);
                msgTitle = @$"{DataItems[m_nameFld].Value}的客户档案已通过审核";
                receiverId = receiver_id;
                msgClass = "notice";
                msgType ="";
                msgSubType = "";
                if (variances.Count != 0)
                {
                    dbLog.AddField("oper_action", "EDIT");
                    dbLog.AddField("approve_status", "approved");
                    dbLog.AddField("approve_time", CPubVars.GetDateText(DateTime.Now));
                    dbLog.AddField("approve_brief", approve_brief);
                    dbLog.AddField("approver_id", operID);
                }
            }
            else if (approveStatus == APPROVE_STATUS.REFUSED_FROM_CREATE || approveStatus == APPROVE_STATUS.REFUSED_FROM_EDIT)
            {
                if(approveStatus == APPROVE_STATUS.REFUSED_FROM_CREATE)
                {
                    msgSubType = DocType + "CREATE";
                }
                else
                {
                    msgSubType = DocType + "Edit";
                }
                string updateOldLog = $@"update document_change_log set approve_status = 'refused',approver_id={operID},approve_time='{CPubVars.GetDateText(DateTime.Now)}',approve_brief = '{approve_brief}' where company_id = {companyID} and flow_id = {flow_id};";
                cmd.CommandText = updateOldLog;
                await cmd.ExecuteNonQueryAsync();
                Dictionary<string, dynamic> messageResult = await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey = operKey,
                    msgId = msg_id,
                    obj_id = DataItems[m_idFld].Value,
                    msgClass = "todo",
                    msgType = DocType + "Approve",
                    msgSubType,
                }, cmd);
                msgTitle = @$"{DataItems[m_nameFld].Value}的客户档案未通过审核";
                receiverId = receiver_id;
                msgClass = "notice";
                msgType = "";
                msgSubType = "";
                if (variances.Count != 0)
                {
                    dbLog.AddField("oper_action", "EDIT");
                    dbLog.AddField("approve_status", "approved");
                    dbLog.AddField("approve_time", CPubVars.GetDateText(DateTime.Now));
                    dbLog.AddField("approve_brief", approve_brief);
                    dbLog.AddField("approver_id", operID);
                }
            }
           
            if (baseInfo == null || (variances!=null && variances.Count != 0))
            {
                //插入新档案
                dbLog.AddField("obj_name", PageMenuID.ToString());
                dbLog.AddField("company_id", companyID);
                dbLog.AddField("obj_id", idValue==""?"-1":idValue);
                dbLog.AddField("oper_id", operID);
                dbLog.AddField("happen_time", CPubVars.GetDateText(DateTime.Now));
                string sqlLog = dbLog.GetInsertSQL("document_change_log") + $";";
                cmd.CommandText = sqlLog;
                await cmd.ExecuteNonQueryAsync();
            }
            if (msgClass != "")
            {
                await MessageCreateServices.CreateMessageService(new
                {
                    operKey = operKey,
                    createrId = operID,
                    msgClass,
                    msgType,
                    msgSubType,
                    obj_id = DataItems[m_idFld].Value,
                    receiverId,
                    msgTitle,
                }, cmd);
            }
        }
        public async Task<JsonResult> SaveTable(CMySbCommand cmd, dynamic data,CMySbTransaction tran=null)
        {
            string sql = "";
            string sqlMain = "";
            cmd.ActiveDatabase = "";
            
            CDbDealer db = new CDbDealer();
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            cmd.company_id = companyID;
            cmd.oper_id = operID;
            if (m_tableName!="")
            {
                foreach (KeyValuePair<string, DataItem> key in DataItems)
                {
                    DataItem dataItem = key.Value;
                 
                    if (dataItem.SaveToDB)
                    {
                        object obj = data[key.Key];
                        string sValue = "";
                        if (obj != null) sValue = obj.ToString();
                        if (key.Key.Contains("mum_"))
                        {

                        }
                        sValue = sValue.Replace("\t", "");
                        if (sValue.Contains("\\")){
                            return new JsonResult(new { result = "Error", msg = $"{dataItem.Title}不能包含\\"});
                        }
                        if (sValue == "NaN") sValue = "";
                        if (key.Key != m_idFld || !IdFldIsSerial)
                        {
                            db.AddField(key.Key, sValue);
                        }
                        if (dataItem.LabelFld!="" && dataItem.LabelInFormTable)
                        {
                            obj = data[dataItem.LabelFld];
                            string label = "";
                            if (obj != null) label = obj.ToString();
                            db.AddField(dataItem.LabelFld, label);
                        }
                        dataItem.Value = sValue;
                        if (dataItem.TreePathFld != "" && dataItem.TreePathFromDb)
                        {
                            obj = data[dataItem.TreePathFld];
                            if (obj != null) dataItem.TreePath = obj.ToString();
                            db.AddField(dataItem.TreePathFld, dataItem.TreePath);
                        }
                    }
                }


                string id_value = "";
                string name_value = "";
                string code_value = "";
                if (m_nameFld != "")
                {
                    name_value = DataItems[m_nameFld].Value;
                }
                if (m_idFld != "")
                {
                    id_value = DataItems[m_idFld].Value;
                }
                if (m_codeFld != "")
                {
                    code_value = DataItems[m_codeFld].Value;
                }

                if (m_idFld != "")
                {
                    if (data.isNewRecord != null && data.isNewRecord.ToString() == "True")
                    {
                        if (id_value != "")
                        {
                            cmd.CommandText = $"select * from {m_tableName} where company_id={companyID}  and {m_idFld}='{id_value}';";
                            object ov = await cmd.ExecuteScalarAsync();
                            if (ov != null)
                            {
                                return new JsonResult(new { result = "Error", msg = "已存在相同的记录", record });
                            }
                        }

                        if (code_value != "")
                        {
                            cmd.CommandText = $"select * from {m_tableName} where company_id={companyID}  and {m_codeFld}='{code_value}';";
                            object ov = await cmd.ExecuteScalarAsync();
                            if (ov != null)
                            {
                                return new JsonResult(new { result = "Error", msg = "已存在编码相同的记录", record });
                            }
                        }


                        if (!AllowSameName && m_nameFld != "")
                        {
                            cmd.CommandText = $"select * from {m_tableName} where company_id={companyID}  and {m_nameFld}='{name_value}';";
                            object ov = await cmd.ExecuteScalarAsync();
                            if (ov != null)
                            {
                                return new JsonResult(new { result = "Error", msg = "已存在同名的记录", record });
                            }
                        }
                        // db.AddField(m_idFld, id_value);
                        db.AddField("company_id", companyID);
                        sqlMain = db.GetInsertSQL(m_tableName) + $" returning {m_idFld};";
                        m_bNewRecord = true;
                    }
                    else
                    {
                        if (!AllowSameName && m_idFld != "" && m_nameFld != "")
                        {
                            cmd.CommandText = $"select * from {m_tableName} where company_id={companyID} and {m_nameFld}='{name_value}' and {m_idFld}<>{id_value}";
                            object ov = await cmd.ExecuteScalarAsync();
                            if (ov != null)
                            {
                                return new JsonResult(new { result = "Error", msg = "已存在同名的记录", record });
                            }
                        }
                        sqlMain = db.GetUpdateSQL(m_tableName, $"company_id={companyID} and {m_idFld}={id_value};");

                        m_bNewRecord = false;
                    }
                }
                else//m_idFld=="" 说明这个主表的数据行对于每一个公司是唯一的，不需要idFld来区分
                {
                    m_bNewRecord = false;
                    sqlMain = db.GetUpdateSQL(m_tableName, $"company_id={companyID};");
                }
            }
            
           if(!SaveMainTable)
            {
                if (m_idFld != "" && DataItems[m_idFld].Value.IsInvalid())
                {
                    throw new Exception("SaveMainTable is false, id should not be empty for save");
                }
                sqlMain = "";
            }
           
          
            string gridSQL = "";

            foreach (KeyValuePair<string, FormDataGrid> kg in Grids)
            {
                dynamic d = data[kg.Key];
                if (d != null)
                {
                    JArray rows = d;

                    //List<Dictionary<string, string>> rows = (List<Dictionary<string, string>>)d;
                    FormDataGrid grid = kg.Value;
                    if (NoIDFld && m_idFld == "" && grid.IdFld == "")//be carefully to check all these varible to ensure that not delete records by mistake
                    {
                        gridSQL += $"delete from {grid.TableName} where company_id={companyID};";
                    } 
                    else 
                    {  
                        string idValue =  DataItems[m_idFld].Value;
                        if (idValue == "") idValue = "-1";
                        gridSQL += $"delete from {grid.TableName} where company_id={companyID} and {grid.IdFld}={idValue};"; 
                    }
                  
                      
                    foreach (JToken row in rows)
                    {
                        db = new CDbDealer();
                        db.AddField("company_id", companyID);
                        if (grid.IdFld != "")
                        { 
                            if (m_bNewRecord)
                            {
                                if (IdFldIsSerial)
                                    db.AddField(grid.IdFld, "@idFld");
                                else
                                    db.AddField(grid.IdFld, DataItems[m_idFld].Value);
                            }
                            else
                                db.AddField(grid.IdFld, DataItems[m_idFld].Value);
                        }

                        async Task AddFields(Dictionary<string,DataItem> columns)
                        {
                            foreach (KeyValuePair<string, DataItem> kp in columns)
                            {
                                await kp.Value.TryGetSubColumns();
                                if (kp.Value.SubColumns != null)
                                { 
                                    await AddFields(kp.Value.SubColumns);
                                }
                                else
                                {
                                    if (kp.Value.SaveToDB && kp.Key!=grid.IdFld)
                                    {
                                        var obj = row[kp.Key];
                                        string v = ""; if (obj != null) v = obj.ToString();
                                        if (v == "NaN") v = "";
                                        if(!(kp.Key==kg.Value.GridIdFld && kg.Value.GridIdFldIsSerial && v == ""))
                                          db.AddField(kp.Key, v);
                                    }
                                }                               
                            }
                        }
                        await AddFields(grid.Columns);
                        string sqlInsert = db.GetInsertSQL(grid.TableName) + ";";
                        gridSQL += sqlInsert;
                    }
                }
            }
            string IdValue = "";
            if (m_idFld != "")
            {
                IdValue = DataItems[m_idFld].Value;
            }
            dynamic origInfo = null;
            if (LogChange)
            {
                origInfo = await GetRecord(cmd, (string)data.operKey, IdValue);
            }
            
            string msg = "";
            bool tranWasNull = tran == null;
            if (tran == null)
            {
                tran = cmd.Connection.BeginTransaction();
            }
            try
            {

                if (m_bNewRecord)
                {
                    sql = sqlMain;
                    if (gridSQL != "")
                    {
                        if (IdFldIsSerial)
                            sql = $"SELECT yj_exeSqlByInsertedRowID('{sqlMain.ToFuncSql()}','{gridSQL.ToFuncSql()}','@idFld');";
                        else
                            sql = sqlMain + gridSQL;
                    }
                    cmd.CommandText = sql;
                    object ov = await cmd.ExecuteScalarAsync();
                    if (IdFldIsSerial)
                        DataItems[m_idFld].Value = ov.ToString().Split(',')[0];
                }
                else
                {
                    sql = sqlMain + gridSQL;
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                }
               
				if (tranWasNull)
                   tran.Commit();
            }
            catch(Exception e)
            {
                if (tranWasNull)
                    tran.Rollback();
                MyLogger.LogMsg($"In SaveTable,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);
                msg = "保存档案发生错误"; goto END; 
                
            }
            if (LogChange)
            {
                await SaveLog(cmd, (string)data.operKey, origInfo,CPubVars.GetStr(data.approve_flag), CPubVars.GetStr(data.approve_brief), CPubVars.GetStr(data.flow_id), CPubVars.GetStr(data.receiver_id), CPubVars.GetStr(data.msg_id));
            }
               
                //await SaveLog(cmd, (string)data.operKey, DataItems[m_idFld].Value, origInfo, DataItems);

            record = new JObject();
            foreach (KeyValuePair<string, DataItem> key in DataItems)
            {
                record[key.Key] = key.Value.Value;
                if (key.Value.LabelFld != "")
                {
                    object obj = data[key.Value.LabelFld];
                    if (obj != null)
                        record[key.Value.LabelFld] = obj.ToString();
                }
            }

        END:
            string result = "OK";
            if (msg != "") result = "Error";

			return new JsonResult(new { result, msg, added = m_bNewRecord, record });
           
        }
        /*public virtual async Task AdjustDataItems(CMySbCommand cmd) //根据某些数据项的值获取其他数据项的值
        {
            
        }*/
    
        public async Task InitGet(CMySbCommand cmd, Func<CMySbCommand, Task> adjustDataItems = null)
        {
            GetOperKey();
            #region check if the coder make wrong config
            if (NoIDFld != (m_idFld == ""))
            {
                throw (new Exception("if NoIDFld true,m_idFld should be '',else m_idFld should not be empty"));
            }
            if (m_idFld != "")
            {
                if (m_tableName == "")
                {
                    throw (new Exception("指定了m_idFld,必须同时指定m_tableName,指定了m_tableName不一定需要指定m_idFld"));
                }
            }
            string bHaveGridIdFld = "";
            foreach (var kp in this.Grids)
            {
                string bCurHaveIdFld = (kp.Value.IdFld != "").ToString();
                if (bHaveGridIdFld != bCurHaveIdFld && bHaveGridIdFld != "")
                {
                    throw (new Exception("所有表格中的IDFld必须同时指定或不指定"));
                }
                bHaveGridIdFld = bCurHaveIdFld;
            }
            if (this.Grids.Count > 0) { 
                if ((bHaveGridIdFld.ToLower() == "true") != (m_idFld != ""))
                {
                    throw (new Exception("m_idFld和表格中的IDFld必须同时指定或不指定"));
                }
            }
            if (NoIDFld)
            {
                if (m_selectFromSQL.Contains("~ID"))
                {
                    throw (new Exception("~ID should not exist in m_selectFromSQL if NoIDFld true"));
                }
                foreach (var kp in this.Grids)
                {
                    if (kp.Value.SelectFromSQL.Contains("~ID"))
                    {
                        throw (new Exception("~ID should not exist in grid's selectFromSQL if NoIDFld true"));
                    } 
                }
            }
            #endregion

            if (m_idFld != "")
            { 
                DataItems[m_idFld].Value = CPubVars.RequestV(Request, m_idFld);
                m_bNewRecord = (DataItems[m_idFld].Value == "");
            }


            InitDataItemsFromRequest();

            if (m_bNewRecord)
            {
                
                if(adjustDataItems!=null)
                   await adjustDataItems(cmd);
               /* if (adjustDataItems!=null)
                {
                   await Task.Run(()=>
                   {
                       adjustDataItems(cmd);
                   });
                }*/
                // await AdjustDataItems(cmd);
            }
            else
            {
                // m_postFormScript += "m_bNewRecord=false;";
                // string sql = m_selectFromSQL.Replace("~ID", DataItems[m_idFld].value);
                if (m_selectFromSQL != "")
                {
                    string sql = GetRealSQL(m_selectFromSQL);//这个方法查询sql
                    bool bExist = await GetDataItemsFromDB(cmd, sql);
                }
               
            }
            await GetJavaScripts(cmd,true);
        }
        public string GetRealSQL(string sql)
        {
            if(m_idFld == "")
            {
                // modified by hsj
                sql = GetRealSQL(sql, company_id, m_tableName,"");
            }
            else
            {
                sql = GetRealSQL(sql, company_id, m_tableName, DataItems[m_idFld].Value);
            }
            // sql = GetRealSQL(sql, company_id, m_tableName, DataItems[m_idFld].Value); 
            return sql;
        }
    } 
}
