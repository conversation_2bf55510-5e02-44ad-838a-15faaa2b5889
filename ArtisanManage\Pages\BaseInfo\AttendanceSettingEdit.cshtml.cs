﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;

namespace ArtisanManage.Pages.BaseInfo
{
    public class AttendanceSettingEditModel : PageFormModel
    {
       public AttendanceSettingEditModel(CMySbCommand cmd,string company_id = "", string oper_id = "") : base(Services.MenuId.infoBranch)
        {

            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"group_id",new DataItem(){Title="考勤组",FldArea="divHead",Hidden=true}},
                {"check_longitude",new DataItem(){Title="考勤经度",FldArea="divHead",Hidden=true}},
                {"check_latitude",new DataItem(){Title="考勤纬度",FldArea="divHead",Hidden=true}},
                {"group_name",new DataItem(){Title="名称",FldArea="divHead"}},
                {"members_id",new DataItem(){FldArea="divHead",Title="业务员",LabelFld="members_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers, Checkboxes = true,Hidden=true} },//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
               //    Source = @"[{v:'normal',l:'����',condition:""(s.status = '1' or s.status is null)""},
               //                {v:'stop',l:'ͣ��',condition:""s.status = '0' ""},
                //               {v:'all',l:'����',condition:""true""}]"
               {"check_days_id",new DataItem(){FldArea="divHead",Title="考勤日",LabelFld="check_days_name",ButtonUsage="list",Source= @"[{v:'1',l:'周一'}, {v:'2',l:'周二'},{v:'3',l:'周三'},{v:'4',l:'周四'},{v:'5',l:'周五'},{v:'6',l:'周六'},{v:'0',l:'周日'}]", Checkboxes = true} },//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
               {"check_start_time",new DataItem(){FldArea="divHead",Title="开始时间",Width="80",Value="9:00"}},
               {"check_end_time",new DataItem(){FldArea="divHead",Title="结束时间",Width="80",Value="17:00"}},
               {"fix_position",new DataItem(){FldArea="divHead",Title="固定位置",CtrlType="jqxCheckBox", Width="180"} },//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
               {"photo_necessary",new DataItem(){FldArea="divHead",Title="强制拍照",CtrlType="jqxCheckBox", Width="180"} },
                {"check_addr",new DataItem(){FldArea="divHead",Title="考勤地点",Width="240",Value=""}},
               {"check_distance",new DataItem(){FldArea="divHead",Title="考勤距离",Width="80",Value="0"}},

            };
            m_idFld = "group_id"; m_nameFld = "group_name";
            m_tableName = "info_attence_group";
            m_selectFromSQL = "from info_attence_group where company_id='~COMPANY_ID' and group_id='~ID'";
        }
        public async Task OnGet()
        { 
            await InitGet(cmd); 
        }

    }
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class AttendanceSettingEditController : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public AttendanceSettingEditController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }
        [HttpGet]
        public async Task<IActionResult>SearchPoi(string key)
        {
			// String url = $@"https://api.map.baidu.com/place/v2/suggestion?query={key}&&&output=json&ak=XUaaox2FCYKKhQrt8EXfS8SW2za8YbWS&&region=北京&city_limit=false";
			String url = $@"https://api.map.baidu.com/place/v2/suggestion?query={key}&&&output=json&ak={CPubVars.BaiduApiKey}&&region=北京&city_limit=false";

			HttpClient client = CPubVars.GetHttpClientFromFactory(_httpClientFactory);
             
            var response = await client.GetAsync(url);

            var res = await response.Content.ReadAsStringAsync();

            dynamic data = JsonConvert.DeserializeObject(res);

            return new JsonResult(data);
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic param)
        {
            Security.GetInfoFromOperKey(param.operKey.ToString(), out string companyID);

            Attendance attendance = new Attendance(cmd);
            string ret = await attendance.SaveGroup(param, companyID);
            string result = "OK";
            if (ret != "")
            {
                string msg = ret;
               
                return new JsonResult(new
                {
                    result,
                    msg
                });
            }
            return new JsonResult(new
            {
                result
            });
        }
        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value,string availValues)
        {
            var model = new AttendanceSettingEditModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
             
        }

    }
    }
