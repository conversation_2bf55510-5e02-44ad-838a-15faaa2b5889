﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ItemsOrderedSummaryBySheetModel : PageQueryModel
    { 
        
        public ItemsOrderedSummaryBySheetModel(CMySbCommand cmd) : base(Services.MenuId.itemsOrderedSummary)
        {
            this.cmd = cmd;
            this.PageTitle = "定货汇总(单据)";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",SqlFld="b.order_time",   CompareOperator=">=", Value=CPubVars.GetDateText(DateTime.Now.Year-1 + "-" + DateTime.Now.Month + "-01")  +" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",SqlFld="b.order_time",   CompareOperator="<=", Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="b.item_id",DropDownWidth="300",
                   SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
                //{"order_sub_id",new DataItem(){FldArea="divHead",Title="定货款账户",LabelFld="sub_name",ButtonUsage="list",CompareOperator="=",SqlFld="order_sub_id,u.prepay_sub_id",
                //    SqlForOptions ="select sub_id as v,sub_name as l from cw_subject where company_id=~COMPANY_ID and sub_type = 'YS' and is_order"}},
                {"order_sub_id",new DataItem(){FldArea="divHead",Title="定货款账户",LabelFld="sub_name",ButtonUsage="list",CompareOperator="=",SqlFld="order_sub_id",SqlForOptions="select -1 as v ,'未指定' as l , 'wzd' as z union select sub_id as v,sub_name as l,py_str as z from cw_subject where sub_type = 'YS' and is_order = true and COALESCE(status,'1')='1'"}},

                {"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld = "b.supcust_id",
                    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID "}},
                {"item_order_sheet_no",new DataItem(){FldArea="divHead",Title="单号", CompareOperator="like"}},
                {"getter_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="oper_name",ButtonUsage="list",CompareOperator="=",SqlFld="b.sheet_seller_id",
                    SqlForOptions="select oper_id as v,oper_name as l from info_operator where company_id=~COMPANY_ID  and is_seller  and COALESCE(status,'1')='1' order by oper_name" } },
                {"showZeroQty",new DataItem(){FldArea="divHead",Title="零剩余可用",LabelFld = "showZeroQtyStatus",ButtonUsage = "list",CompareOperator="=",Value="0",Label="不显示零剩余",
                       Source = @"[{v:'1',l:'显示零剩余',condition:""true""},
                                    {v:'0',l:'不显示零剩余',condition:""b.order_qty <> 0  ""}]"

                }},


            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates = true,
                   
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"supcust_id",new DataItem(){Title = "客户",Hidden = true,SqlFld = "b.supcust_id",HideOnLoad= true } },
                       {"sup_name",new DataItem(){Title="客户",Hidden = false, Width="180"}},
                       {"order_sub_id",new DataItem(){Title="定货款账户",Width = "180",Hidden = true,SqlFld = "order_sub_id" ,HideOnLoad= true} },
                       {"order_sub_name",new DataItem(){Title="定货款账户",Width = "150",SqlFld="COALESCE(sub_name,'未指定')" } },

                       {"item_id",new DataItem(){Title="商品",Width="",Hidden=true,SqlFld = "b.item_id" } },
                       {"item_name",new DataItem(){Title="商品名称", Width="180",Linkable = true}},
					   {"s_unit_no",new DataItem(){Title="小单位", Width="60",Hidden=true}},
                       
                       {"left_quantity_b",new DataItem(){Title = "总剩余可用b",Hidden = true, SqlFld = "yj_get_unit_qty('b',b.order_qty::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"left_quantity_m",new DataItem(){Title = "总剩余可用m",Hidden = true, SqlFld = "yj_get_unit_qty('m',b.order_qty::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"left_quantity_s",new DataItem(){Title = "总剩余可用s",Hidden = true, SqlFld = "yj_get_unit_qty('s',b.order_qty::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"left_quantity", new DataItem(){Title="总剩余可用",Hidden = true,HideOnLoad=true,CellsAlign="right", Width="120",FuncDealMe=value=>{return value=="0"?"":value; },
                           SqlFld="unit_from_s_to_bms(b.order_qty::float4,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           FuncGetSumValue = ((sumValues) =>
                           {
                               string sum="";
                               string s=sumValues["left_quantity_b"];
                               if(s!="") sum+=s+"大";
                               s=sumValues["left_quantity_m"];
                               if(s!="") sum+=s+"中";
                               s=sumValues["left_quantity_s"];
                               if(s!="") sum+=s+"小";
                               return sum;
                           })
                       }},
                       {"left_quantity1",new DataItem(){Title = "总剩余可用(小)",Hidden = true,HideOnLoad=true,Width="120",SqlFld = "b.order_qty" } },


                       {"order_price",   new DataItem(){Title="小单位定货价格", CellsAlign="right", Width="120",SqlFld=" b.order_price"}},
                       {"real_price",   new DataItem(){Title="可用余额", CellsAlign="right", Width="120",ShowSum=true,SqlFld = "b.balance"}},
                      {"item_order_sheet_id",new DataItem(){Title="单号",Hidden = true,HideOnLoad=true,Width = "150",SqlFld=" b.order_item_sheet_id" } },
                      {"item_order_sheet_no",new DataItem(){Title="单号",Hidden = false,Linkable = true,Width = "150",SqlFld=" b.item_order_sheet_no" } },
                      {"sheet_type1",new DataItem(){Title="单据类型",Hidden = false,Width = "150",SqlFld=" case when b.sheet_type ='DH' then '定货单' else '定货会调整单' end " } },
                      {"make_brief",new DataItem(){Title="单据备注",Hidden = false,Width = "150",SqlFld=" b.make_brief" } },
                       {"order_time",new DataItem(){Title="单据时间",Hidden = false,Width = "150",SqlFld=" order_time" } },
                       {"sheet_seller_id",new DataItem(){Title="单据业务员",Hidden = true,HideOnLoad = true,Width = "150",SqlFld=" b.sheet_seller_id" } },
                       {"sheet_seller_name",new DataItem(){Title="单据业务员",Width = "150",SqlFld=" b.sheet_seller_name" } },
                       {"order_sheet_qty_b",new DataItem(){Title = "单据数量b",Hidden = true, SqlFld = "yj_get_unit_qty('b',order_sheet_qty::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"order_sheet_qty_m",new DataItem(){Title = "单据数量m",Hidden = true, SqlFld = "yj_get_unit_qty('m',order_sheet_qty::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"order_sheet_qty_s",new DataItem(){Title = "单据数量s",Hidden = true, SqlFld = "yj_get_unit_qty('s',order_sheet_qty::numeric,b_unit_factor::numeric,m_unit_factor::numeric,true)", ShowSum=true,HideOnLoad= true}},
                       {"order_sheet_qty", new DataItem(){Title="单据数量",Hidden = false,CellsAlign="right", Width="120",FuncDealMe=value=>{return value=="0"?"":value; },
                           SqlFld="unit_from_s_to_bms(order_sheet_qty::float4,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           FuncGetSumValue = ((sumValues) =>
                           {
                               string sum="";
                               string s=sumValues["order_sheet_qty_b"];
                               if(s!="") sum+=s+"大";
                               s=sumValues["order_sheet_qty_m"];
                               if(s!="") sum+=s+"中";
                               s=sumValues["order_sheet_qty_s"];
                               if(s!="") sum+=s+"小";
                               return sum;
                           })
                       }},
                       {"order_sheet_qty1",new DataItem(){Title = "单据数量(小)",Hidden = false,Width="120",SqlFld = "order_sheet_qty" } },
                       {"sheet_sale_qty",new DataItem(){Title="单据已处理(还|调)",Hidden = false, Width="100",SqlFld="''"}},
                       {"sheet_left_qty",new DataItem(){Title="单据剩余数量",Hidden = false, Width="100",SqlFld="''"}},
                       {"remark",new DataItem(){Title = "商品备注",Hidden = false,Width="120",SqlFld = "b.remark" } },

                       {"rn",new DataItem(){Title = "",Hidden = true,HideOnLoad= true,Width="120",SqlFld = "b.rn" } },
                       {"sheet_type",new DataItem(){Title = "",Hidden = true,HideOnLoad= true,Width="120",SqlFld = "b.sheet_type" } },

                     },

                       QueryFromSQL=@"
from 
(
  
    SELECT 
        sd.company_id ,prepay_sub_id order_sub_id,sd.supcust_id,sd.item_id,case when t.order_price is null then sd.sheet_price else t.order_price end order_price ,sum(COALESCE(order_qty,0)) order_qty,sum(COALESCE(balance,0)) balance ,
				
				
				
			sd.sheet_id	order_item_sheet_id, sd.order_sheet_qty,sd.happen_time order_time ,sd.sheet_no item_order_sheet_no, row_number() over(partition by sd.supcust_id,prepay_sub_id,sd.item_id,sd.sheet_price ORDER BY sd.happen_time) rn ,sd.sheet_seller_id,sd.sheet_seller_name,sd.remark,sd.make_brief,sd.sheet_type
    from 
		(
		
		
		SELECT sp.sheet_no,sp.supcust_id,sp.prepay_sub_id,sp.getter_id sheet_seller_id,op.oper_name sheet_seller_name,sd.sheet_id,sd.company_id,sd.item_id,round((sd.real_price/sd.unit_factor)::numeric,3) sheet_price,sum(sd.quantity* sd.unit_factor) order_sheet_qty,sd.happen_time,sd.remark,sp.make_brief,sp.sheet_type FROM sheet_order_item_detail sd 
		LEFT JOIN sheet_prepay sp on sp.company_id = sd.company_id and sp.sheet_id = sd.sheet_id 
		LEFT JOIN info_operator op on op.company_id =  sp.company_id and sp.getter_id = op.oper_id
		where sd.company_id = ~COMPANY_ID and sp.red_flag is null and sp.approve_time is not null and sp.sheet_type ='DH' 
		GROUP BY sp.sheet_no,sd.sheet_id,sp.getter_id,op.oper_name,sd.company_id,sd.item_id,sheet_price,sd.happen_time ,sp.supcust_id,sp.prepay_sub_id,sd.remark,sp.make_brief,sp.sheet_type
        union all 
		SELECT sp.sheet_no,sp.supcust_id,sp.prepay_sub_id,sp.seller_id sheet_seller_id,op.oper_name sheet_seller_name,sd.sheet_id,sd.company_id,sd.item_id,round((sd.real_price/sd.unit_factor)::numeric,3) sheet_price,sum(sd.quantity* sd.unit_factor) order_sheet_qty,sd.happen_time,sd.remark,sp.make_brief,sp.sheet_type FROM sheet_item_ordered_adjust_detail sd 
		LEFT JOIN sheet_item_ordered_adjust_main sp on sp.company_id = sd.company_id and sp.sheet_id = sd.sheet_id
		LEFT JOIN info_operator op on op.company_id = sp.company_id and sp.seller_id = op.oper_id
		where sd.company_id = ~COMPANY_ID and sp.red_flag is null and sp.approve_time is not null 
		GROUP BY sp.sheet_no,sd.sheet_id,sp.seller_id,op.oper_name,sd.company_id,sd.item_id,sheet_price,sd.happen_time ,sp.supcust_id,sp.prepay_sub_id,sd.remark,sp.make_brief,sp.sheet_type
		)sd
		
            
    LEFT JOIN (
select DISTINCT company_id, b.prepay_sub_id order_sub_id,supcust_id,item_id, round((b.order_price/b.unit_factor)::numeric,3) order_price,sum(round(b.quantity*b.unit_factor::numeric,2)) order_qty,sum(round(b.quantity*b.order_price::numeric,2)) as balance 
                from items_ordered_balance b where company_id=~COMPANY_ID   
								GROUP BY company_id, b.prepay_sub_id,supcust_id,item_id,round((b.order_price/b.unit_factor)::numeric,3)
            )t  on sd.company_id = ~COMPANY_ID  and t.supcust_id = sd.supcust_id   and t.item_id = sd.item_id 
    where   case when t.order_price is not null then abs( t.order_price-sd.sheet_price)<0.001 else true end 
    GROUP BY prepay_sub_id,sd.supcust_id,sd.item_id,case when t.order_price is null then sd.sheet_price else t.order_price end,sd.company_id,order_item_sheet_id,order_sheet_qty,order_time,item_order_sheet_no,sd.sheet_seller_id,sd.sheet_seller_name,sd.sheet_price,sd.remark,sd.make_brief,sd.sheet_type
) b
left join info_item_prop i on i.company_id =~COMPANY_ID  and  i.item_id = b.item_id
left join info_supcust p on  p.company_id=~COMPANY_ID    and p.supcust_id = b.supcust_id
left join cw_subject pw on  pw.company_id=~COMPANY_ID  and  pw.sub_id = b.order_sub_id
left join 
(
    select item_id,(b->>'f1')::real as b_unit_factor,(m->>'f1')::real as m_unit_factor,(s->>'f1')::real as s_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
                    as errr(item_id int, s jsonb,m jsonb,b jsonb)
) mu on  mu.item_id = i.item_id
where 1=1
",
                            QueryGroupBySQL="group by order_sub_id,sub_name,b.supcust_id,sup_name,b.item_id,item_name,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no,b.order_price,b.balance,b.order_qty,order_sheet_qty,order_time,b.order_item_sheet_id,b.item_order_sheet_no,b.rn,b.sheet_seller_id,b.sheet_seller_name ,b.make_brief,b.remark,b.sheet_type",
                           
                      QueryOrderSQL=" order by supcust_id,order_sub_id,b.item_id,order_price, order_time desc  "
                  }
                } 
            }; 
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {

            //string sql = $"select min(happen_time) v from sheet_prepay p left join info_pay_way pw on pw.sub_id = p.prepay_sub_id where p.company_id = {company_id} and is_order";
            //dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            //if (record != null)
            //{
            //    if(record.v!="")
            //       DataItems["startDay"].Value = record.v;
            //}
            
            
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {

            //SQLVariables["startDay"] = DataItems["startDay"].Value;
            //SQLVariables["endDay"] = DataItems["endDay"].Value;



        }

        public override async Task<JsonResult> DealQueriedRecords(dynamic data, dynamic postParams)
        {
            var rows = (Dictionary<int, Dictionary<string, string>>)data.rows;
            //var rows =(List<dynamic>)data.rows;
            //var groups = rows.GroupBy(info => $"{info.supcust_id},{info.order_sub_id},{info.item_id},{info.order_price}");
            if (rows.Count > 0)
            {
                var keys = rows.Keys;
                dynamic preRow = null;
                int preRn = -1;
                decimal leftQty = 0;
                foreach (var key in keys)
                {
                    var row = rows[key];
                    var rn = row["rn"];
                    if (rn == "")
                    {

                    }
                    if (preRow != null)
                    {
                        if(row["sup_name"] != preRow["sup_name"]|| row["order_sub_id"] != preRow["order_sub_id"]|| row["item_id"] != preRow["item_id"] || row["order_price"] != preRow["order_price"])
                        {
                            preRow = null;
                            preRn = -1;
                            leftQty = 0;
                        }
                    }
                    var rn1 = Convert.ToInt32(rn);
                    var total_left_qty =CPubVars.ToDecimal( row["left_quantity1"]);
                    var order_sheet_qty = CPubVars.ToDecimal(row["order_sheet_qty1"]);

                    if (rn1 > 1)
                    {
                        if(preRow == null)
                        {
                            if(order_sheet_qty - total_left_qty > 0)
                            {
                                row["sheet_left_qty"] = total_left_qty.ToString();
                                row["sheet_sale_qty"] = (order_sheet_qty - total_left_qty).ToString();
                                leftQty = 0;
                            }
                            else
                            {
                                row["sheet_left_qty"] = row["order_sheet_qty1"];
                                leftQty = total_left_qty - order_sheet_qty;
                            }

                        }
                        else
                        {
                            if(order_sheet_qty- leftQty > 0)
                            {
                                row["sheet_left_qty"] = leftQty.ToString();
                                row["sheet_sale_qty"] = (order_sheet_qty - leftQty).ToString();
                                leftQty = 0;

                            }
                            else
                            {
                                row["sheet_left_qty"] = row["order_sheet_qty1"];
                                leftQty = leftQty - order_sheet_qty;
                            }
                        }
                        preRow = row;
                        preRn = rn1;
                    }
                    else
                    {
                        if (preRow == null)
                        {
                            row["sheet_sale_qty"] = (order_sheet_qty - total_left_qty).ToString();
                            row["sheet_left_qty"] = total_left_qty.ToString();

                        }
                        else
                        {
                            row["sheet_left_qty"] = leftQty.ToString();
                            row["sheet_sale_qty"] = (order_sheet_qty - leftQty).ToString();
                        }
                        preRow = null;
                        preRn = -1;
                        leftQty = 0;
                    }


                    if (row["sheet_left_qty"] == "0") row["sheet_left_qty"] = "";
                    if (row["sheet_sale_qty"] == "0") row["sheet_sale_qty"] = "";



                }
                
            }
            return new JsonResult(data);
        }
        public async Task OnGet()
        {  
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
    }



    [Route("api/[controller]/[action]")]
    public class ItemsOrderedSummaryBySheetController : QueryController
    { 
        public ItemsOrderedSummaryBySheetController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ItemsOrderedSummaryBySheetModel model = new ItemsOrderedSummaryBySheetModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            ItemsOrderedSummaryBySheetModel model = new ItemsOrderedSummaryBySheetModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            ItemsOrderedSummaryBySheetModel model = new ItemsOrderedSummaryBySheetModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
