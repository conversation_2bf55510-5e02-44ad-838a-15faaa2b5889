﻿
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class SupplierAccountChangeSumModel : PageQueryModel
    {
        public SupplierAccountChangeSumModel(CMySbCommand cmd, bool useMainDb = false) : base(Services.MenuId.supplierAccountChangeSum)
        {
            if (useMainDb) this.Database = "";
            this.cmd = cmd;
            this.PageTitle = "供应商往来汇总表";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, CompareOperator="=", QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-30))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, SqlFld="sm.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="供应商",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="sc.supcust_id",
                SqlForOptions=CommonTool.selectSuppliers } },


            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, Sortable=true,
                     ZeroAsEmpty = "true",


                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"supcust_id",    new DataItem(){Title="供应商编号",  Width="250",SqlFld="sc.supcust_id",HideOnLoad=true,Hidden=true}},
                       {"sup_name",    new DataItem(){Title="供应商名称", Sortable=true,  Width="100",SqlFld=""}},
                       {"prepay",new DataItem(){Title="预付款", Width="60", Sortable=true, FuncGetSubColumns = async (col) =>
                            new ColumnsResult{
                                Columns=new Dictionary<string,DataItem>()
                                {
                       {"prepay_strart",    new DataItem(){Title="初", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(YF_end,0) - (COALESCE(s_over.YF_add,0) - COALESCE(s_over.YF_reduce,0)) - (COALESCE(s_between.YF_add,0) - COALESCE(s_between.YF_reduce,0)) as decimal(18,2))",ShowSum=true}},
                       {"prepay_add",    new DataItem(){Title="增", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.YF_add,0) as decimal(18,2))",Linkable=true,ShowSum=true}},
                       {"prepay_reduce",    new DataItem(){Title="减", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.YF_reduce,0) as decimal(18,2))",Linkable=true,ShowSum=true}},
                       {"prepay_end",    new DataItem(){Title="末", Sortable=true,  Width="50",SqlFld="cast(COALESCE(YF_end,0) - (COALESCE(s_over.YF_add,0) - COALESCE(s_over.YF_reduce,0)) as decimal(18,2))",ShowSum=true}},
                                }
                            }
                       } },
                       {"arrear",new DataItem(){Title="应付款", Width="60", Sortable=true, FuncGetSubColumns = async (col) =>
                            new ColumnsResult{

                                Columns=new Dictionary<string,DataItem>()
                                {
                       {"arrear_strart",    new DataItem(){Title="初", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(QK_end,0) - (COALESCE(s_over.QK_add,0) - COALESCE(s_over.QK_reduce,0)) - (COALESCE(s_between.QK_add,0) - COALESCE(s_between.QK_reduce,0)) as decimal(18,2))",ShowSum=true}},
                       {"arrear_add",    new DataItem(){Title="增", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.QK_add,0) as decimal(18,2))",Linkable=true,ShowSum=true}},
                       {"arrear_reduce",    new DataItem(){Title="减", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.QK_reduce,0) as decimal(18,2))",Linkable=true,ShowSum=true}},
                       {"arrear_end",    new DataItem(){Title="末", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(QK_end,0) - (COALESCE(s_over.QK_add,0) - COALESCE(s_over.QK_reduce,0)) as decimal(18,2))",ShowSum=true}},

                                }
                            }
                       } },
                       {"total",new DataItem(){Title="往来合计（预付-应付）", Width="60", Sortable=true, FuncGetSubColumns = async (col) =>
                            new ColumnsResult{

                                Columns=new Dictionary<string,DataItem>()
                                {
                       {"total_strart",    new DataItem(){Title="初", Sortable=true,  Width="50",SqlFld=" cast((COALESCE(YF_end,0) - (COALESCE(s_over.YF_add,0) - COALESCE(s_over.YF_reduce,0)) - (COALESCE(s_between.YF_add,0) - COALESCE(s_between.YF_reduce,0))) - (COALESCE(QK_end,0) - (COALESCE(s_over.QK_add,0) - COALESCE(s_over.QK_reduce,0)) - (COALESCE(s_between.QK_add,0) - COALESCE(s_between.QK_reduce,0))) as decimal(18,2))",ShowSum=true}},
                       {"total_add",    new DataItem(){Title="增", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.YF_add,0)-COALESCE(s_between.QK_add,0) as decimal(18,2))",ShowSum=true}},
                       {"total_reduce",    new DataItem(){Title="减", Sortable=true,  Width="50",SqlFld=" cast(COALESCE(s_between.YF_reduce,0)-COALESCE(s_between.QK_reduce,0) as decimal(18,2))",ShowSum=true}},
                       {"total_end",    new DataItem(){Title="末", Sortable=true,  Width="50",SqlFld=" cast((COALESCE(YF_end,0) - (COALESCE(s_over.YF_add,0) - COALESCE(s_over.YF_reduce,0))) - (COALESCE(QK_end,0) - (COALESCE(s_over.QK_add,0) - COALESCE(s_over.QK_reduce,0))) as decimal(18,2))",ShowSum=true}},

                                }
                            }
                       } },


                     },

                     QueryFromSQL=@"
FROM
	( SELECT distinct supcust_id FROM prepay_balance pb WHERE company_id =~COMPANY_ID 
	union
	SELECT distinct supcust_id FROM arrears_balance pb WHERE company_id =~COMPANY_ID  ) t
	LEFT JOIN ( select supcust_id, sum(balance) YF_end from prepay_balance where  company_id =~COMPANY_ID  GROUP BY supcust_id ) yf ON yf.supcust_id = t.supcust_id
    LEFT JOIN ( select supcust_id, sum(-1*balance) QK_end from arrears_balance where  company_id =~COMPANY_ID  GROUP BY supcust_id ) qk ON qk.supcust_id = t.supcust_id
	LEFT JOIN (
        SELECT supcust_id,
            SUM ( CASE WHEN sub_type = 'YF'and sheet_type  IN('YF','SR','CT')  THEN change_amount ELSE 0 END ) AS YF_add,
            SUM ( CASE WHEN sub_type = 'YF'and sheet_type  in ('CG')   THEN -change_amount ELSE 0 END ) AS YF_reduce,
            SUM (  CASE WHEN sub_type = 'QK'AND case when sheet_type='FK' and change_amount<0 then sheet_type  in('CG','YF','ZC','FK') ELSE sheet_type  in('CG','YF','ZC') END THEN -change_amount ELSE 0 END ) AS QK_add,
            SUM (  CASE WHEN sub_type = 'QK'AND  case when sheet_type='FK' and change_amount>0 then sheet_type  in('FK','CT','SR') ELSE sheet_type  in('CT','SR') END THEN change_amount ELSE 0 END ) AS QK_reduce
        FROM client_account_history
        WHERE red_flag IS NULL AND company_id =~COMPANY_ID  AND happen_time >= '~VAR_startDay' AND happen_time <= '~VAR_endDay'
        GROUP BY supcust_id
	) s_between ON T.supcust_id = s_between.supcust_id
    LEFT JOIN (
        SELECT supcust_id,
            SUM ( CASE WHEN sub_type = 'YF'and sheet_type  ='YF'  THEN change_amount ELSE 0 END ) AS YF_add,
            SUM ( CASE WHEN sub_type = 'YF'and sheet_type  ='CG'  THEN -change_amount ELSE 0 END ) AS YF_reduce,
            SUM (  CASE WHEN sub_type = 'QK'AND case when sheet_type='FK' and change_amount<0 then sheet_type in ('CG','YF','ZC','FK') ELSE sheet_type in ('CG','YF','ZC') END THEN -change_amount ELSE 0 END ) AS QK_add,
            SUM (  CASE WHEN sub_type = 'QK'AND  case when sheet_type='FK' and change_amount>0 then sheet_type in ('FK','CT','SR') ELSE sheet_type  in('CT','SR') END THEN change_amount ELSE 0 END ) AS QK_reduce
        FROM client_account_history
        WHERE red_flag IS NULL AND company_id =~COMPANY_ID  AND happen_time > '~VAR_endDay' AND happen_time <= cast(concat(CURRENT_DATE::text,' 23:59:59.999') as timestamp) GROUP BY supcust_id
	) s_over ON T.supcust_id = s_over.supcust_id
left join info_supcust sc  on t.supcust_id=sc.supcust_id
where sc.company_id=~COMPANY_ID   and supcust_flag in ('S','CS')
",

                     QueryGroupBySQL = "",
                     QueryOrderSQL="ORDER BY supcust_id "
                  }
                }
            };
        }
        public async Task OnGet()
        {
            await InitGet(cmd);
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {

            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;


        }




    }



    [Route("api/[controller]/[action]")]
    public class SupplierAccountChangeSumController : QueryController
    {
        public SupplierAccountChangeSumController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SupplierAccountChangeSumModel model = new SupplierAccountChangeSumModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            SupplierAccountChangeSumModel model = new SupplierAccountChangeSumModel(cmd);

            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            SupplierAccountChangeSumModel model = new SupplierAccountChangeSumModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
