﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using Renci.SshNet;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{

   

    [Route("AppApi/[controller]/[action]")]
    public class AppSheetPrepay : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public AppSheetPrepay(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }


        [HttpGet]
        public async Task<JsonResult> GetSubName(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID);
            string sql = @$"select  sub_id,sub_name from cw_subject where company_id={companyID} and sub_type = 'YS' and is_order is not true 
            and (sub_id::text IN (
                        SELECT 
                            json_array_elements_text(avail_pay_ways) AS individual_value 
                        FROM 
                            info_operator 
                        WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
                        )
                       OR
                      (   SELECT 
                            COUNT(*) 
                        FROM 
                            info_operator 
                        WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
                       )
";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return Json(new { result = "OK",msg="",records });
        }


        [HttpGet]
        public async Task<JsonResult> GetPrepaySubjects(string operKey,string sheetType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            var sql = $"SELECT sub_id,sub_name FROM cw_subject where company_id = {companyID} AND sub_type = '{sheetType}' and is_order is not true and COALESCE(status,'1')='1' ";
            List<ExpandoObject> data = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });
        }


        /// <summary>
        /// 加载预收款单
        /// </summary>
        /// <param name="operKey"> </param>
        /// <returns>
        /// sheet  单据
        /// payways 支付方式
        /// </returns>

        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID,string sheetType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID);
            var sheet_type = sheetType == "YF" ? SHEET_PREPAY.IS_PREPAY : SHEET_PREPAY.NOT_PREPAY;
            SheetPrepay<SheetRowBase> sheet = new SheetPrepay<SheetRowBase>(sheet_type, LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            var intype = sheetType == "YF" ? "'QTSR'" : "'ZC'";
			var sql = @$"select sub_id,sub_name from cw_subject where company_id = {companyID} and  sub_type in ('QT',{intype}) and coalesce(status,'1')='1' 
            and (sub_id::text IN (
                        SELECT 
                            json_array_elements_text(avail_pay_ways) AS individual_value 
                        FROM 
                            info_operator 
                        WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
                        )
                       OR
                      (   SELECT 
                            COUNT(*) 
                        FROM 
                            info_operator 
                        WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
                       )
            order by order_index,case sub_type when 'QT' then 0  when 'ZC' then 1 else 2 end;";
            List<System.Dynamic.ExpandoObject> payways =await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            string result = "OK";
            string msg = "";
            return Json(new {result,msg, sheet,payways});
        }

        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic dsheet)
        {
            string result;
            string msg = "";
            string ssheet = JsonConvert.SerializeObject(dsheet);
            SheetPrepay<SheetRowBase> sheet = null;
            try
            {
                sheet = JsonConvert.DeserializeObject<SheetPrepay<SheetRowBase>>(ssheet);
                sheet.sheet_type = sheet.SheetType == "YF" ? SHEET_TYPE.SHEET_PRE_PAY_MONEY : SHEET_TYPE.SHEET_PRE_GET_MONEY;
            
                sheet.Init();
                sheet._httpClientFactory = this._httpClientFactory;
                if (dsheet.appendixPhotos != null)
                {
                    List<string> appendixBase64s = new List<string>();
                    foreach (string appendixPhoto in dsheet.appendixPhotos)
                    {
                        appendixBase64s.Add(appendixPhoto);
                    }
                    sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());
                }
                msg = await sheet.Save(cmd);
                result = msg == "" ? "OK" : "Error";
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no });
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetPrepay.Save:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Save:" + msg + sheet, Token.CompanyID);
                msg = "保存失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }
            

        }

        public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64, string companyID)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, companyID);
            return result;
        }
        /// <summary>
        /// 提交预收款单
        /// </summary>
        /// <param name="sheet">
        /// 预收款接口数据测试：
        /// {"operKey":"wcAqiAdqGYG39sTafoxzNuV7gjl0d-zEX5Q5vIEsZ4CJBL8L71cPvCkNmSBpbvSukmnIwUZFvIg~","sheet_id": "","sheet_no": "","sheettype": "YS", "happen_time": "2020-11-18 20:45:43","supcust_id": 1, 
        ///  "prepay_sub_id":5, "total_amount":100, "make_brief":"","payway1_id":"1","payway1_amount":100} </param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            SheetPrepay<SheetRowBase> sheet = null;
            //  dSheet.appendixPhotos = this.ProcessAppendixPicsRetDBStr(dSheet.appendixPhotos,dS);
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetPrepay<SheetRowBase>>(sSheet);
                if (dSheet.appendixPhotos != null)
                {
                    List<string> appendixBase64s = new List<string>();
                    foreach (string appendixPhoto in dSheet.appendixPhotos)
                    {
                        appendixBase64s.Add(appendixPhoto);
                    }
                    sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());
                }
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetPrepay.Submit:" + msg);
                MyLogger.LogMsg("in AppSheetSave.Submit:" + msg + sSheet, Token.CompanyID);
                msg = "提交失败,请联系技术支持";
                return new JsonResult(new { result = "Error", msg });
            }

             
            sheet.sheet_type = sheet.SheetType == "YF" ? SHEET_TYPE.SHEET_PRE_PAY_MONEY : SHEET_TYPE.SHEET_PRE_GET_MONEY;
            //sheet.sheet_type = SHEET_TYPE.SHEET_PRE_GET_MONEY;
            sheet.Init();
            sheet._httpClientFactory = this._httpClientFactory;
            msg = await sheet.SaveAndApprove(cmd);
            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no,currentTime});
        }


        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string result = "OK"; string msg = null;
            string operKey = data.operKey;
            string sheetID = data.sheetID;
  
            try
            {
                var currentTime = DateTime.Now.ToText();
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                SheetPrepay<SheetRowBase> sheet = new SheetPrepay<SheetRowBase>(SHEET_PREPAY.EMPTY,LOAD_PURPOSE.SHOW);
                sheet._httpClientFactory = this._httpClientFactory;
                msg = await sheet.Red(cmd, companyID, sheetID, operID,"");
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }
        }
        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        { 
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

            SheetPrepay<SheetRowBase> sheet = new SheetPrepay<SheetRowBase>(SHEET_PREPAY.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet_id });
        }




    }
}