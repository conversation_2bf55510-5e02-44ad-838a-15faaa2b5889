﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.Cashier;

[Route("api/cashier/[action]")]
public class LoginController : BaseController
{
    #region Predefs
    private readonly IHttpClientFactory _httpClientFactory;

    public LoginController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
    {
        this.cmd = cmd; _httpClientFactory = httpClientFactory;
    }
    #endregion

    [HttpPost]
    public async Task<CallResult<dynamic>> Login([FromBody] dynamic request)
    {
        string deviceMac = request.device_mac_address;
        if (deviceMac.IsInvalid()) return new("Error", "未获取到设备信息");

        var lc = new AppController.LoginController(cmd, _httpClientFactory);
        var login_response = (await lc.Login(request)).Value;
        if (login_response.result != "OK")
            return new("Failed", login_response.msg);

        string companyId = login_response.companyID;

        var _sql_queue = new SQLQueue(cmd);
        var poses = new List<ExpandoObject>();
        var sql = $@"
            SELECT
                pos_id, pos_name, branch_id, mac_addrs
            FROM
                info_pos
            WHERE
                company_id = '{companyId}'
            LIMIT 100;
        ";
        _sql_queue.Enqueue("load_poses", sql);
        var _dr = await _sql_queue.ExecuteReaderAsync();
        while (_sql_queue.Count > 0)
        {
            var sqlName = _sql_queue.Dequeue();
            if (sqlName == "load_poses")
                poses = CDbDealer.GetRecordsFromDr(_dr, false);
        }
        _dr.Close();
        _sql_queue.Clear();

        string pos_id = "", pos_name = "", branch_id = "";
        foreach (dynamic pos in poses)
        {
            string mac_addrs = pos.mac_addrs ?? "";
            pos.mac_addrs = ""; // 去除隐私内容以便返回前端
            var maces = mac_addrs.Split(',').ToList();
            foreach (var mac in maces)
            {
                if (mac == deviceMac)
                {
                    pos_id = pos.pos_id;
                    pos_name = pos.pos_name;
                    branch_id = pos.branch_id;
                    break;
                }
            }
        }

        return new("OK", login_response.msg, new
        {
            pos_info = new { pos_id, pos_name, branch_id, poses },
            oper_id = login_response.data.oper_id,
            oper_name = login_response.data.oper_name,
            mobile = login_response.data.mobile,
            operKey = login_response.operKey,
            companyID = login_response.companyID,
            companyName = login_response.companyName,
            newVersion = login_response.newVersion,
            updateUrl = login_response.updateUrl,
            serverUri = login_response.serverUri,
            coolieUri = login_response.coolieUri,
            serverTime = login_response.serverTime
        });
    }


    [HttpPost]
    public async Task<CallResult<dynamic>> RegisterPos([FromBody] dynamic request)
    {
        string operKey = request.operKey;
        Security.GetInfoFromOperKey(operKey, out string companyId);
        string deviceMac = request.device_mac_address;
        string pos_id = request.pos_id;
        string pos_name = request.pos_name;
        string branch_id = request.branch_id;

        if (deviceMac.IsInvalid())
            return new("Error", "未提供设备MAC地址");

        string msg = "", sql = "";
        if (pos_id.IsValid())
        {
            // 向已有的pos机记录中添加MAC地址内容
            sql = $@"
                UPDATE
                    info_pos
                SET
                    mac_addrs = COALESCE(mac_addrs || ',{deviceMac}', '{deviceMac}')
                WHERE
                    pos_id = {pos_id}
                    and (mac_addrs IS NULL OR mac_addrs NOT LIKE '%{deviceMac}%');
            ";
            try
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                NLogger.Error("[RegisterPos] 数据库操作失败，语句为" + sql);
                NLogger.Error(ex.ToString());
                msg = ex.Message;
            }
        }
        else
        {
            // 新增一条pos机记录
            if (pos_name.IsInvalid())
                return new("Error", "请为设备命名");
            if (branch_id.IsInvalid())
                return new("Error", "请选择绑定的仓库");
            sql = $@"
                INSERT INTO
                    info_pos
                    (company_id, pos_name, branch_id, mac_addrs, create_time)
                VALUES
                    ('{companyId}', '{pos_name}', '{branch_id}', '{deviceMac}', '{CPubVars.GetDateText(DateTime.Now)}')
                RETURNING
                    pos_id;
            ";
            try
            {
                CMySbDataReader dr;
                cmd.CommandText = sql;
                dr = await cmd.ExecuteReaderAsync();
                while (dr.Read())
                {
                    pos_id = CPubVars.GetTextFromDr(dr, "pos_id");
                }
                dr.Close();
                if (pos_id.IsInvalid())
                    msg = "获取返回数据失败,请刷新页面重新保存";
            }
            catch (Exception ex)
            {
                NLogger.Error("[RegisterPos] 数据库操作失败，语句为" + sql);
                NLogger.Error(ex.ToString());
                msg = ex.Message;
            }
        }

        string result = msg.Length > 0 ? "Error" : "OK";
        return new(result, msg, new {
            pos_id
        });
    }

    [HttpGet]
    public async Task<CallResult<dynamic>> LoadBranches(string operKey)
    {
        Security.GetInfoFromOperKey(operKey, out string companyId, out string operId);
        var _sql_queue = new SQLQueue(cmd);
        var branches = new List<ExpandoObject>();
        var sql = $@"
            SELECT
                ib.branch_id as value,
                ib.branch_name as label 
            FROM
                oper_branch_rights obr
                LEFT JOIN info_branch ib ON ib.branch_id = obr.branch_id AND ib.company_id = obr.company_id 
            WHERE
                ib.company_id = {companyId}
                and oper_id = {operId}
                and sheet_x = 'True';
        ";
        _sql_queue.Enqueue("load_branches", sql);
        var _dr = await _sql_queue.ExecuteReaderAsync();
        while (_sql_queue.Count > 0)
        {
            var sqlName = _sql_queue.Dequeue();
            if (sqlName == "load_branches")
                branches = CDbDealer.GetRecordsFromDr(_dr, false);
        }
        _dr.Close();
        _sql_queue.Clear();
        return new("OK", "", branches);
    }
}
