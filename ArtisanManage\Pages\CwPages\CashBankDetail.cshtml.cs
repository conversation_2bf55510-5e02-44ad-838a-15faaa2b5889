﻿using ArtisanManage.Models;
using ArtisanManage.Pages.BaseInfo;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NPOI.POIFS.Crypt.Dsig;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.CwPages
{
    public class CashBankDetailModel : PageQueryModel
    {
        public string BizStartPeriod = "";
        public string sub_id_req = "";
        public CashBankDetailModel(CMySbCommand cmd) : base(Services.MenuId.cashBankDetail)
        {
            this.cmd = cmd;
            this.PageTitle = "现金银行明细账";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay", new DataItem(){Title="开始日期", FldArea="divHead", CtrlType="jqxDateTimeInput",  CompareOperator=">=", Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00", ForQuery=false}},
                {"endDay", new DataItem(){Title="结束日期", FldArea="divHead", CtrlType="jqxDateTimeInput", CompareOperator="<=", Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59", ForQuery=false, 
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }"
                }},
                {"time_type",new DataItem(){FldArea="divHead",Title="时间类型",LabelFld = "timetype_name",ButtonUsage = "list",CompareOperator="=",Value="happen_time",Label="按交易时间", ForQuery=false,
                        Source = @"[{v:'happen_time', l:'按交易时间'},
                                           {v:'make_time', l:'按制单时间'},
                                           {v:'approve_time',l:'按审核时间'}]"
                }},
                {"sub_id",new DataItem(){Title="账户", FldArea="divHead", SqlFld="q.sub_id", LabelFld="sub_name", ButtonUsage="list", CompareOperator="=", Checkboxes = true, FirstOptionAsDefault=true, GetOptionsOnLoad=true,
                    SqlForOptions = "select q.sub_id as v, s.sub_name as l from info_pay_qrcode q left join cw_subject s on q.company_id=s.company_id and q.sub_id=s.sub_id where q.company_id =~COMPANY_ID and s.sub_type ='QT' order by order_index", ForQuery=false }},
                {"supcust_id", new DataItem(){Title="往来单位", FldArea="divHead", LabelFld="sup_name", ButtonUsage="list", CompareOperator="=", Checkboxes = true, ForQuery=false,  
                    SqlForOptions = @"select supcust_id as v,sup_name as l,py_str as z,supcust_no as n from info_supcust where company_id= ~COMPANY_ID  and (status = '1' or status is null) and ~QUERY_CONDITION
                                      union 
                                      select partner_id as v,partner_name as l,py_str as z,'' as n from (select *, partner_name as sup_name from info_loan_partner where company_id= ~COMPANY_ID and (status = '1' or status is null)) as loan_partners where ~QUERY_CONDITION" }},
                {"seller_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="seller_name",Hidden=true, ButtonUsage="list",CompareOperator="=",SqlFld="o.oper_id",GetOptionsOnLoad=false,SqlForOptions=CommonTool.selectSellers_without_no_specify, ForQuery=false } },
                {"showRed",new DataItem(){FldArea="divHead",Title="显示红冲单",CtrlType="jqxCheckBox",ForQuery=false,Value="false"}}
              
            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                    "gridItems",  new QueryGrid()
                    {
                        ShowAggregates = true,
                        IdColumn="row_index_all",
                        //AutoAppendQueryCondition = false,//true:条件以and形式，false:条件以where形式
                        Columns = new Dictionary<string, DataItem>()
                        {
                            {"row_index_all", new DataItem(){Title="row_index_all", Hidden=true, HideOnLoad=true } },
                            {"row_index", new DataItem(){Title="row_index", Hidden=true, HideOnLoad=true } },
                            {"sub_id",new DataItem(){Title="sub_id", Hidden=true, HideOnLoad=true }},
                            {"sub_name",new DataItem(){Title="账户", Width="200"}},
                            {"sup_name",new DataItem(){Title="往来单位", Width="200"}},
                            {"seller_name",new DataItem(){Title="业务员", Width="150"}},
                            {"time",new DataItem(){Title="时间", Width="200"}},
                            {"sheet_type",new DataItem(){Title="sheet_type", Hidden=true, HideOnLoad=true }},
                            {"sheet_type_name",new DataItem(){Title="单据类型" }},
                            {"sheet_id",new DataItem(){Title="sheet_id", Hidden=true, HideOnLoad=true }},
                            {"sheet_no",new DataItem(){Title="单据编号", Linkable=true }},
                            {"sheet_status", new DataItem(){Title="状态",  Width="100",SqlFld="(case red_flag when '2' then '红字单' when '1' then '已红冲' when '0' then '' else '已审核' end)"}},
                            {"make_brief",new DataItem(){Title="单据备注", Width="80"}},
                            {"in_amt",new DataItem(){Title="收入", CellsAlign="right", Width="200", ShowSum=true}},
                            {"out_amt",new DataItem(){Title="支出", CellsAlign="right", Width="200", ShowSum=true}},
                            {"balance_all",   new DataItem(){Title="余额", CellsAlign="right", Width="200" }},
                        },
                        QueryFromSQL=""
                    }
                }
            };
        }

        
        public async Task OnGet(string operKey)
        {
            await InitGet(cmd);
            dynamic g_co = await CDbDealer.Get1RecordFromSQLAsync($"select business_start_period from g_company where company_id={company_id}", cmd);
            BizStartPeriod = g_co.business_start_period;
        }

        public override async Task OnQueryConditionStrGot(string condi, CMySbCommand cmd)
        {
            this.SQLVariables["TIME_TYPE"] = DataItems["time_type"].Value;
            this.SQLVariables["TIME_BETWEEN"] = $"between '{DataItems["startDay"].Value}' and '{DataItems["endDay"].Value}' ";
            this.SQLVariables["TIME_START"] = $">= '{DataItems["startDay"].Value}' ";
            this.SQLVariables["SUB_CONDI"] = DataItems["sub_id"].Value == "" ? "" : $"and sub_id in ({DataItems["sub_id"].Value})";
            this.SQLVariables["SELLER_CONDI"] = DataItems["seller_id"].Value == "" ? "" : $"and seller_id in ({DataItems["seller_id"].Value})";
            this.SQLVariables["SUPCUST_CONDI"]= (DataItems["supcust_id"].Value == ""|| DataItems["supcust_id"].Value == ",") ? "" : $"and coalesce(supcust_id,0) in ({DataItems["supcust_id"].Value})";
            this.SQLVariables["SHOW_RED"] = Convert.ToBoolean(DataItems["showRed"].Value) ? "" : " and (red_flag is null)";

            #region QUERY_FROM_SQL
            string sql_between = @"select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, seller_id, sheet_type, (case sheet_type when 'X' then '销售单' when 'T' then '退货单' else '' end) as sheet_type_name, red_flag, payway1_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway1_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway1_amount else null end) as out_amt, make_brief from sheet_sale_main 
        where coalesce(payway1_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, seller_id, sheet_type, (case sheet_type when 'X' then '销售单' when 'T' then '退货单' else '' end) as sheet_type_name, red_flag, payway2_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway2_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway2_amount else null end) as out_amt, make_brief from sheet_sale_main 
        where coalesce(payway2_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, seller_id, sheet_type, (case sheet_type when 'X' then '销售单' when 'T' then '退货单' else '' end) as sheet_type_name, red_flag, payway3_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway3_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway3_amount else null end) as out_amt, make_brief from sheet_sale_main 
        where coalesce(payway3_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null

        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, seller_id, sheet_type, (case sheet_type when 'CG' then '采购单' when 'CT' then '采购退货单' else '' end) as sheet_type_name, red_flag, payway1_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway1_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway1_amount else null end) as out_amt, make_brief from sheet_buy_main 
        where coalesce(payway1_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, seller_id, sheet_type, (case sheet_type when 'CG' then '采购单' when 'CT' then '采购退货单' else '' end) as sheet_type_name, red_flag, payway2_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway2_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway2_amount else null end) as out_amt, make_brief from sheet_buy_main 
        where coalesce(payway2_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, seller_id, sheet_type, (case sheet_type when 'CG' then '采购单' when 'CT' then '采购退货单' else '' end) as sheet_type_name, red_flag, payway3_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway3_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway3_amount else null end) as out_amt, make_brief from sheet_buy_main 
        where coalesce(payway3_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, partner_id as supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'DK' then '贷款单' else '' end) as sheet_type_name, red_flag, payway1_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway1_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway1_amount else null end) as out_amt, make_brief from sheet_loan 
        where coalesce(payway1_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, partner_id as supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'DK' then '贷款单' else '' end) as sheet_type_name, red_flag, payway2_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway2_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway2_amount else null end) as out_amt, make_brief from sheet_loan 
        where coalesce(payway2_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, partner_id as supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'DK' then '贷款单' else '' end) as sheet_type_name, red_flag, payway3_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway3_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway3_amount else null end) as out_amt, make_brief from sheet_loan 
        where coalesce(payway3_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null

        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, partner_id as supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'HDK' then '还贷款单' else '' end) as sheet_type_name, red_flag, payway1_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway1_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway1_amount else null end) as out_amt, make_brief from sheet_repay 
        where coalesce(payway1_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, partner_id as supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'HDK' then '还贷款单' else '' end) as sheet_type_name, red_flag, payway2_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway2_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway2_amount else null end) as out_amt, make_brief from sheet_repay 
        where coalesce(payway2_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, partner_id as supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'HDK' then '还贷款单' else '' end) as sheet_type_name, red_flag, payway3_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway3_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway3_amount else null end) as out_amt, make_brief from sheet_repay 
        where coalesce(payway3_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null

        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'SK' then '收款单' when 'FK' then '付款单' else '' end) as sheet_type_name, red_flag, payway1_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway1_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway1_amount else null end) as out_amt, make_brief from sheet_get_arrears_main 
        where coalesce(payway1_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'SK' then '收款单' when 'FK' then '付款单' else '' end) as sheet_type_name, red_flag, payway2_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway2_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway2_amount else null end) as out_amt, make_brief from sheet_get_arrears_main 
        where coalesce(payway2_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'SK' then '收款单' when 'FK' then '付款单' else '' end) as sheet_type_name, red_flag, payway3_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway3_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway3_amount else null end) as out_amt, make_brief from sheet_get_arrears_main 
        where coalesce(payway3_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null

        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'DH' then '定货会' when 'YS' then '预收款单' when 'YF' then '预付款单' else '' end) as sheet_type_name, red_flag, payway1_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway1_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway1_amount else null end) as out_amt, make_brief from sheet_prepay 
        where coalesce(payway1_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'DH' then '定货会' when 'YS' then '预收款单' when 'YF' then '预付款单' else '' end) as sheet_type_name, red_flag, payway2_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway2_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway2_amount else null end) as out_amt, make_brief from sheet_prepay 
        where coalesce(payway2_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'DH' then '定货会' when 'YS' then '预收款单' when 'YF' then '预付款单' else '' end) as sheet_type_name, red_flag, payway3_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway3_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway3_amount else null end) as out_amt, make_brief from sheet_prepay 
        where coalesce(payway3_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and coalesce(is_imported,false)=false and approve_time is not null

        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'ZC' then '费用支出单' when 'SR' then '其他收入单' else '' end) as sheet_type_name, red_flag, payway1_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway1_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway1_amount else null end) as out_amt, make_brief from sheet_fee_out_main 
        where coalesce(payway1_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and sheet_attribute->>'imported' is null and approve_time is not null
        union all
        select company_id, sheet_id, sheet_no, happen_time, make_time, approve_time, supcust_id, getter_id as seller_id, sheet_type, (case sheet_type when 'ZC' then '费用支出单' when 'SR' then '其他收入单' else '' end) as sheet_type_name, red_flag, payway2_id as sub_id, 0 as row_index, (case when money_inout_flag=1 then payway2_amount else null end) as in_amt, (case when money_inout_flag=-1 then payway2_amount else null end) as out_amt, make_brief from sheet_fee_out_main 
        where coalesce(payway2_amount, 0) <> 0 and ~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and sheet_attribute->>'imported' is null and approve_time is not null

        union all 
        select td.company_id, td.sheet_id, tm.sheet_no, tm.happen_time, tm.make_time, tm.approve_time, 0 as supcust_id, maker_id as seller_id, tm.sheet_type, '转账单' as sheet_type_name, tm.red_flag, td.money_out_id as sub_id, td.row_index, null as in_amt, (case when coalesce(tm.red_flag,0)=2 then -td.out_amount_withfee else td.out_amount_withfee end) as out_amt, make_brief from sheet_cashbank_transfer_detail td
		left join sheet_cashbank_transfer_main tm on td.company_id=tm.company_id and td.sheet_id=tm.sheet_id
        where coalesce(td.out_amount_withfee,0)<>0 and tm.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and tm.approve_time is not null
        union all
        select td.company_id, td.sheet_id, tm.sheet_no, tm.happen_time, tm.make_time, tm.approve_time, 0 as supcust_id, maker_id as seller_id, sheet_type, '转账单' as sheet_type_name, tm.red_flag, td.money_in_id as sub_id, td.row_index, (case when coalesce(tm.red_flag,0)=2 then -td.in_amount else td.in_amount end) as in_amt, null as out_amt, make_brief from sheet_cashbank_transfer_detail td 
        left join sheet_cashbank_transfer_main tm on td.company_id=tm.company_id and td.sheet_id=tm.sheet_id
        where coalesce(td.in_amount,0)<>0 and tm.~VAR_TIME_TYPE ~VAR_TIME_BETWEEN and tm.approve_time is not null";

            string sql_op = $@"with detail_op as ( {sql_between.Replace("TIME_BETWEEN", "TIME_START")} )
                select -1 as row_index_all, 0 as row_index, q.sub_id, 0 as sheet_id, null as sheet_no, '{DataItems["startDay"].Value}' as time, '{DataItems["startDay"].Value}' as happen_time, '{DataItems["startDay"].Value}' as make_time, '{DataItems["startDay"].Value}' as approve_time, null as sheet_type, '期初' as sheet_type_name, '0' as red_flag, null as supcust_id, null as seller_id, null as order_index, null as in_amt, null as out_amt, null as make_brief, coalesce(e.balance,0)-(coalesce(t.in_all,0)-coalesce(t.out_all,0)) as balance from info_pay_qrcode q
                left join cashbank_balance e on q.company_id=e.company_id and q.sub_id=e.sub_id
                left join (select sub_id,sum(in_amt) as in_all, sum(out_amt) as out_all from detail_op where company_id= ~COMPANY_ID group by sub_id) t on  e.sub_id=t.sub_id
                where q.company_id= ~COMPANY_ID ";

            string sql = $@"
from (
    with detail_all as (
        select ttt.*, s.sub_name, c.sup_name, o.oper_name as seller_name from (
            ( {sql_op} )
            union all
            ( 
                select * from (		
                select (ROW_NUMBER() over (order by t.~VAR_TIME_TYPE, t.row_index, coalesce(t.red_flag,0))) as row_index_all, t.row_index, q.sub_id, t.sheet_id, t.sheet_no, t.~VAR_TIME_TYPE as time, t.happen_time, t.make_time, t.approve_time, t.sheet_type, t.sheet_type_name, t.red_flag, t.supcust_id, t.seller_id, s.order_index, t.in_amt, t.out_amt, t.make_brief, 0 as balance from info_pay_qrcode q
                left join cashbank_balance e on q.company_id=e.company_id and q.sub_id=e.sub_id
                left join ( {sql_between} ) t on q.company_id=t.company_id and q.sub_id=t.sub_id
                left join cw_subject s on q.company_id=t.company_id and s.sub_id=q.sub_id 
                where q.company_id = ~COMPANY_ID and s.sub_type='QT' and t.approve_time is not null
                ) tt  order by concat(order_index::text,'-',sub_id::text), row_index_all 
            )
            union all
            ( 
                select 1000000 as row_index_all, 1000000 as row_index, sub_id, 0 as sheet_id, null as sheet_no, '{DataItems["endDay"].Value}' as time, '{DataItems["endDay"].Value}' as happen_time, '{DataItems["endDay"].Value}' as make_time, '{DataItems["endDay"].Value}' as approve_time, null as sheet_type, '期末' as sheet_type_name, '0' as red_flag, null as supcust_id, null as seller_id, null as order_index, null as in_amt, null as out_amt, null as make_brief, 0 as balance from info_pay_qrcode where company_id=  ~COMPANY_ID 
            )
        ) ttt 
        left join cw_subject s on s.company_id=~COMPANY_ID and s.sub_id=ttt.sub_id 
        left join (select supcust_id, sup_name from info_supcust where company_id=~COMPANY_ID
                   union
                   select partner_id as supcust_id, partner_name as sup_name from info_loan_partner where company_id=~COMPANY_ID) c on c.supcust_id=ttt.supcust_id
        left join info_operator o on o.company_id=~COMPANY_ID and o.oper_id=ttt.seller_id
    ) 
    select * from (
        select *, cast(sum(coalesce(balance,0) + coalesce(in_amt,0) - coalesce(out_amt,0)) over (partition by sub_id order by row_index_all) as decimal(18,2)) as balance_all from detail_all where true ~VAR_SUB_CONDI ~VAR_SELLER_CONDI  order by sub_id,row_index_all 
    ) t_all where true ~VAR_SHOW_RED ~VAR_SUPCUST_CONDI
) tttt ";

            Grids["gridItems"].QueryFromSQL = sql;
            #endregion
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class CashBankDetailController : Controller
    {
        CMySbCommand cmd;
        public CashBankDetailController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value)
        {
            CashBankDetailModel model = new CashBankDetailModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, null);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            CashBankDetailModel model = new CashBankDetailModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            CashBankDetailModel model = new CashBankDetailModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}




