﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;

namespace ArtisanManage.Pages.Setting
{
    public class PaywaysQuickViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
   
        public PaywaysQuickViewModel(CMySbCommand cmd) : base(Services.MenuId.paywaysQuickView)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                 {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称/助记码",UseJQWidgets=false, SqlFld="sub_name,py_str",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
                 {"other_sub",new DataItem(){Title="父科目", LikeWrapper="/", CtrlType="jqxTree",MumSelectable=true,GetOptionsOnLoad=true,FirstOptionAsDefault=true, QueryOnChange=true,CompareOperator="like",
                   SqlForOptions="select sub_id as v,sub_name as l,py_str as z,mother_id as pv from cw_subject where company_id= ~COMPANY_ID order by mother_id"}},
            };
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     IdColumn="i",TableName="cw_subject",
                     ShowContextMenu=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"i",new DataItem(){Title="编号",SqlFld="sub_id",Width="80",Hidden=true,HideOnLoad = true}},
                       {"sub_name",new DataItem(){Title="名称",Width="150",Linkable=true}},
                       {"sub_code",new DataItem(){Title="科目代码",Width="150"}},
                       {"sub_type",new DataItem(){Title="类型",Width="100",SqlFld = "(case when sub_type = 'ZC' THEN '费用支出' when sub_type = 'YS' then '预收' when sub_type = 'QT' then '其他' when sub_type = 'YF' then '预付' when sub_type = 'QTSR' then '其他收入' end)"}},
                       {"status",new DataItem(){Title="状态",Width="50",SqlFld="(case WHEN status='0' THEN '停用' ELSE '正常' END)" }}
                     },
                     QueryFromSQL=@"from cw_subject 
                                    where company_id=~COMPANY_ID " ,
                     QueryOrderSQL="order by order_index,sub_id"
                  }
                } 
            }; 
        }
        public async Task OnGet(string forSelect)
        {  
            await InitGet(cmd);
            ForSelect = forSelect == "1";
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }

    }



    [Route("api/[controller]/[action]")]
    public class PaywaysQuickViewController : YjController
    {
        CMySbCommand cmd;
        public PaywaysQuickViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            var model = new PaywaysQuickViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            var model = new PaywaysQuickViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }

        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            var model = new PaywaysQuickViewModel(cmd);
            string sub_id = data.rowIDs;
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string result = "OK";
            string msg = "";

            msg = await CheckBeforeDeleteRecords(companyID, sub_id);
            if (msg != "") return new JsonResult(new { result = "Error", msg });

            string sql = $"delete from cw_subject where company_id = {companyID} and sub_id = {sub_id};" +
                         $"delete from info_pay_way where company_id = {companyID} and sub_id = {sub_id};";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();

            return Json(new { result, msg, sub_id });
        }

        public async Task<string> CheckBeforeDeleteRecords(string companyID, string rowIDs)
        {
            string err = "";
            SQLQueue QQ = new SQLQueue(cmd);
            string sql = $"select sub_code from cw_subject where mother_id in ({rowIDs}) and company_id = {companyID}";
            QQ.Enqueue("subject", sql);
            sql = $"select count(*) from sheet_sale_main where (payway1_id in ({rowIDs}) or payway2_id in ({rowIDs})) and company_id={companyID}";
            QQ.Enqueue("sale", sql);
            sql = $"select count(*) from sheet_get_arrears_main where (payway1_id in ({rowIDs}) or payway2_id in ({rowIDs})) and company_id={companyID}";
            QQ.Enqueue("get_arrears", sql);
            sql = $"select count(*) from sheet_prepay where prepay_sub_id in ({rowIDs}) and company_id={companyID}";
            QQ.Enqueue("prepay", sql);
            sql = $"select count(*) from prepay_balance where sub_id in ({rowIDs}) and company_id={companyID}";
            QQ.Enqueue("prepay_bal", sql);
            sql = $"select count(*) from sheet_fee_out_detail where fee_sub_id in ({rowIDs}) and company_id={companyID}";
            QQ.Enqueue("fee", sql);
            sql = $"select count(*) from display_agreement_main where fee_sub_id in ({rowIDs}) and company_id = {companyID}";
            QQ.Enqueue("order", sql);

            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "subject")
                {
                    dr.Read();
                    var subCode = CPubVars.GetTextFromDr(dr, "sub_code");  
                    if (subCode.IsValid())
                    {
                        err = "请先删除该科目类别的子科目类别";
                        break;
                    }
                }
                if (dr.Read())
                {
                    object ov = dr[0];
                    if (ov != null && ov != DBNull.Value)
                    {
                        int ct = Convert.ToInt32(ov);
                        if (ct > 0)
                        {
                            err = "该科目已被使用过,无法删除";
                            break;
                        }
                    }
                }
            }
            QQ.Clear();
            return err;
        }
        /*
        [HttpPost]
        public async Task<IActionResult> RemoveMumSub([FromBody] dynamic value)
        {
            var model = new PaywayQuickEditModel(cmd);
            string id = value[model.m_idFld];
            string result = "OK";
            if (id == "")
            {
                result = "请传入科目编号";
                goto end;
            }
            CDbDealer db = new CDbDealer();

            object o = null;
            
            cmd.CommandText = $"select sub_code from cw_subject where mother_id='{id}'";
            o = await cmd.ExecuteScalarAsync();
            if (o != null && o != DBNull.Value)
            {
                result = "请删除该科目类别的子科目类别后再删除该科目类别"; goto end;
            }
            //cmd.CommandText = $"select sub_code from cw_subject where sub_id='{id}'";
            //o = cmd.ExecuteScalar();
            //if (o != null && o != DBNull.Value)
            //{
            //    result = "请删除该科目类别的科目名称后再删除该科目类别"; goto end;
            //}
            string sql = $"delete from cw_subject where sub_id='{id}'";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();


        //  var tt = Convert.ToString(value.uid); 
        //var rr = new { UserID = value.UserID, UserName = value.UserName };
        //return value;
        end:
            return Json(new { result, sub_id = id });
            //return JsonObject<object> (new { UserID = value.UserID, UserName = value.UserName });
        }*/

    }
}
