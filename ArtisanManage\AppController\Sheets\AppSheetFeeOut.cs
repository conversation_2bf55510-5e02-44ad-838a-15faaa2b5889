﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using ArtisanManage.YingJiangBackstage.Pojo;

namespace ArtisanManage.AppController
{

   

    [Route("AppApi/[controller]/[action]")]
    public class AppSheetFeeOut : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public AppSheetFeeOut(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }


        /// <summary>
        /// 加载费用支出
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID);
            SheetFeeOut sheet = new SheetFeeOut(SHEET_FEE_OUT.IS_OUT,LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            string restrictPayWays = @$"and (sub_id::text IN (
            SELECT 
                json_array_elements_text(avail_pay_ways) AS individual_value 
            FROM 
                info_operator 
            WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
            )
           OR
          (   SELECT 
                COUNT(*) 
            FROM 
                info_operator 
            WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
           )";
            var sql = @$"select sub_id,sub_name,py_str,sub_code from cw_subject where company_id = {companyID} and sub_type = 'ZC' and sub_code>66 and coalesce(status,'1')='1' {restrictPayWays} ORDER BY order_index;";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("feeOutInfo", sql);
            sql = @$"select sub_id,sub_name,py_str,sub_code from cw_subject where company_id = {companyID} and sub_type = 'QTSR' and sub_code>66 and coalesce(status,'1')='1' {restrictPayWays};";
            QQ.Enqueue("feeInInfo", sql);
            sql = $@"select sub_id,sub_name, sub_code, sub_type payway_type from cw_subject where company_id = {companyID} and sub_type in ('QT','YS') and coalesce(status,'1')='1' {restrictPayWays} order by sub_type,order_index; ";
            QQ.Enqueue("payways", sql);
            sql = $"select supcust_id, sup_name, supcust_no, py_str from info_supcust where company_id = {companyID} and supcust_flag in ('C','CS') and coalesce(status,'1')='1' order by sup_order_index;";
            QQ.Enqueue("supcust", sql);
            List<ExpandoObject> feeOut = null;
            List<ExpandoObject> feeIn = null;
            List<ExpandoObject> payways = null;
            List<ExpandoObject> supcust = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "feeOutInfo")
                {
                    feeOut = CDbDealer.GetRecordsFromDr(dr, false);
                }
                if (sqlName == "feeInInfo")
                {
                    feeIn = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "payways")
                {
                    payways = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "supcust")
                {
                    supcust = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            string result = "OK";
            string msg = "";
            sheet.OperKey = null;
            return Json(new {result,msg, sheet, feeOut, feeIn, payways, supcust });
        }

        [HttpPost]
        public async Task<JsonResult> Save([FromBody] dynamic dSheet)
        {
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            string result;
            string msg;
            SheetFeeOut sheet = null;
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetFeeOut>(sSheet);
                if (dSheet.appendixPhotos != null)
                {
                    List<string> appendixBase64s = new List<string>();
                    foreach (string appendixPhoto in dSheet.appendixPhotos)
                    {
                        appendixBase64s.Add(appendixPhoto);
                    }
                    sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());
                }
            }
            catch (Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetFeeOut.Save " + msg + " sSheet:" + sSheet);
                return new JsonResult(new { result = "Error", msg });
            }

            var currentTime = DateTime.Now.ToText();
            sheet.Init();
            msg = await sheet.Save(cmd);
            result = msg == "" ? "OK" : "Error";

            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, currentTime });
        }

        public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64, string companyID)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, companyID);
            return result;
        }

        /// <summary>
        /// 提交费用支出单
        /// </summary>
        /// <param name="sheet">
        ///{"sheet_id": "","sheet_no": "","operKey": "bIuYnVotW7J33Q8abhxoBzLx-HlRyzltkJ10Eqfo87IWpKgtDVFJm9qYJRiorwSh1LkCfI9INRirWKR56w_1J2JZyekvje5E_U86_7ZiDkuzlbBZXb47aQ~~","sheettype": "ZC","maker_id": "",
        ///"maker_name": "","make_time": "","happen_time": "", "approve_time": "","make_brief": "","approve_brief": "","submit_time": "","supcust_id": "1",
        ///"shop_id": "11","total_amount": 300,"payway1_id":"1","payway1_amount": 200,"payway2_id": "2","payway2_amount": "100","make_brief":"",
        ///"SheetRows": [{"fee_sub_id": 9,"supcust_id": "1","fee_sub_amount": 250,"now_pay_amount": 200,"now_disc_amount": 50,"remark":""},{"fee_sub_id": 10,"supcust_id": "1",
        ///"fee_sub_amount": 250,"now_pay_amount": 200,"now_disc_amount": 50}]}
        /// </param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            string msg = "";
            SheetFeeOut sheet = null;
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetFeeOut>(sSheet);
                if (dSheet.appendixPhotos != null) 
                {
                    List<string> appendixBase64s = new List<string>();
                    foreach (string appendixPhoto in dSheet.appendixPhotos)
                    {
                        appendixBase64s.Add(appendixPhoto);
                    }
                    sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());
                }
            }
            catch(Exception e)
            {
                msg = e.Message;
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Error("in AppSheetFeeOut.Submit " + msg+ " sSheet:"+ sSheet);
                return new JsonResult(new { result = "Error", msg });
            }
             
            var currentTime = DateTime.Now.ToText();
            if (sheet != null)
            {
                sheet.Init();
                msg = await sheet.SaveAndApprove(cmd);
            } 
            string result = msg == "" ? "OK" : "Error";
          
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no,currentTime });
        } 

        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string result = "OK"; string msg = null;
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            if(sheetID==null || sheetID == "")
            {
                return new JsonResult(new { result="Error", msg="请指定单据号"});
            }
           /* try
            {*/
            var currentTime = DateTime.Now.ToText();
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetFeeOut sheet = new SheetFeeOut(SHEET_FEE_OUT.EMPTY, LOAD_PURPOSE.SHOW);
            msg = await sheet.Red(cmd, companyID, sheetID, operID,"");
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });
/*
            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }*/
        }
        /// <summary>
        /// 删除未审核单据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetFeeOut sheet = new SheetFeeOut(SHEET_FEE_OUT.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return Json(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }


        [HttpPost]
        public async Task<JsonResult> GetFeeOutDisplaySheetInfo([FromBody] dynamic paramers)
        {
            string operKey = paramers.operKey;
            string supcustId = paramers.supcustId;
            string deptPath = paramers.deptPath;
            bool canGiveDisplayCrossDept  = paramers.canGiveDisplayCrossDept ?? false;
            List<ExpandoObject> displaySheets = null;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string condi = $@" ";
            if (!canGiveDisplayCrossDept && deptPath.IsValid())
            {
                condi = $@" and  (io1.depart_path like '{deptPath}%' or io2.depart_path like '{deptPath}%') ";
            }

            string sql = @$"
select dam2.company_id,
       dam2.sheet_id,
       dam2.disp_template_id,
       dam2.sheet_no,
       dam2.seller_id,
       dam2.start_time,
       dam2.end_time,
       dam2.fee_sub_id,
       cw.sub_name,
       io1.oper_name as seller_name,
       dam2.responsible_worker,
       io2.oper_name as responsible_worker_name,
       idt.disp_template_name,
       idt.sup_id,
       idt.dept_path,
       idt.give_condition,
       idt.month_maintain_times,
       is_temp_sup.sup_name as disp_sup_name,
       sum_maintain.maintain_arr,
       dad.flow_id,
       dad.month1_qty,
       dad.month1_given,
       dad.month2_qty,
       dad.month2_given,
       dad.month3_qty,
       dad.month3_given,
       dad.month4_qty,
       dad.month4_given,
       dad.month5_qty,
       dad.month5_given,
       dad.month6_qty,
       dad.month6_given,
       dad.month7_qty,
       dad.month7_given,
       dad.month8_qty,
       dad.month8_given,
       dad.month9_qty,
       dad.month9_given,
       dad.month10_qty,
       dad.month10_given,
       dad.month11_qty,
       dad.month11_given,
       dad.month12_qty,
       dad.month12_given,
       dad.sub_amount,
       dad.all_given
from display_agreement_detail dad
         left join display_agreement_main dam2 on dad.company_id = dam2.company_id and dam2.sheet_id = dad.sheet_id
         left join info_display_template idt on idt.company_id = dad.company_id and idt.disp_template_id = dam2.disp_template_id
         left join info_supcust is_temp_sup on is_temp_sup.company_id = dad.company_id and is_temp_sup.supcust_id = idt.sup_id 
         left join info_operator io1 on io1.company_id = {companyID} and io1.oper_id = dam2.seller_id 
         left join info_operator io2 on io2.company_id = {companyID} and io2.oper_id = dam2.responsible_worker
            LEFT JOIN cw_subject cw on cw.company_id = {companyID} and cw.sub_id = dam2.fee_sub_id
         left join ( select t.disp_sheet_id, jsonb_agg(t) as maintain_arr
    from (
             select to_char(sdm.months, 'YYYY-MM') as maintain_month,
                    sdm.disp_sheet_id,
                    idt.month_maintain_times,
                    sdm.maintain_times,
                    idt.give_condition
             from sum_display_maintain sdm
                      left JOIN info_display_template idt
                                on sdm.company_id = {companyID} and idt.disp_template_id = sdm.disp_temp_id
             WHERE sdm.company_id = {companyID}
               and sdm.disp_sheet_id in (
                 select sheet_id
                 from display_agreement_main dam
                 where dam.company_id = {companyID}
                   and dam.supcust_id = {supcustId}
                   and dam.red_flag is NULL 
                   and dam.approve_time is NOT NULL
             )) t
    group by t.disp_sheet_id) sum_maintain on sum_maintain.disp_sheet_id = dad.sheet_id
where dad.company_id = {companyID}
  and dad.items_id = 'money'
  and (dad.all_given is NULL OR dad.all_given is false) 
  AND (
      COALESCE(dad.month1_qty, 0) +
      COALESCE(dad.month2_qty, 0) +
      COALESCE(dad.month3_qty, 0) +
      COALESCE(dad.month4_qty, 0) +
      COALESCE(dad.month5_qty, 0) +
      COALESCE(dad.month6_qty, 0) +
      COALESCE(dad.month7_qty, 0) +
      COALESCE(dad.month8_qty, 0) +
      COALESCE(dad.month9_qty, 0) +
      COALESCE(dad.month10_qty, 0) +
      COALESCE(dad.month11_qty, 0) +
      COALESCE(dad.month12_qty, 0)
  ) > (
      COALESCE(dad.month1_given, 0) +
      COALESCE(dad.month2_given, 0) +
      COALESCE(dad.month3_given, 0) +
      COALESCE(dad.month4_given, 0) +
      COALESCE(dad.month5_given, 0) +
      COALESCE(dad.month6_given, 0) +
      COALESCE(dad.month7_given, 0) +
      COALESCE(dad.month8_given, 0) +
      COALESCE(dad.month9_given, 0) +
      COALESCE(dad.month10_given, 0) +
      COALESCE(dad.month11_given, 0) +
      COALESCE(dad.month12_given, 0)
  )
  and dad.sub_amount > 0 
   and (dam2.disp_template_id is NULL OR (dam2.disp_template_id is NOT  NULL and idt.sign_need_review = false OR (idt.sign_need_review = true and dam2.reviewer is NOT NULL AND (dam2.review_refused = false OR dam2.review_refused is NULL))))
    {condi}
  and dad.sheet_id in (
    select sheet_id
    from display_agreement_main dam
    where dam.company_id = {companyID}
      and dam.supcust_id = {supcustId}
      and dam.red_flag is NULL 
      and dam.approve_time is NOT NULL
)
order by dad.sheet_id;
";
            try
            {
                displaySheets = await CDbDealer.GetRecordsFromSQLAsync(sql,cmd);
                return Json(new ResultUtil<dynamic>().CommonResult(0, "success", displaySheets));
            }
            catch
            {
                return Json(new ResultUtil<dynamic>().CommonResult(-1, "获取陈列费异常，请重试", null));
            }
        }


    }
}