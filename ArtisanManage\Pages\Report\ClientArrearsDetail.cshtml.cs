using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ClientArrearsDetailModel : PageQueryModel
    { 
        public ClientArrearsDetailModel(CMySbCommand cmd) : base(Services.MenuId.arrearsBlanceDetail)
        {
            this.cmd = cmd;
            this.PageTitle = "应收款明细";
            DataItems = new Dictionary<string, DataItem>()
            {
             {"startDay",new DataItem(){Title="开始日期",FldArea="divHead",ForQuery=false, CtrlType="jqxDateTimeInput", SqlFld="approve_time",CompareOperator=">=",QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddYears(-1))+" 00:00"}},
            {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", ForQuery=false, CtrlType="jqxDateTimeInput", SqlFld="approve_time", CompareOperator="<",QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
            }},
                 {"supcust_id",new DataItem(){FldArea="divHead",Title="客户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="sc.supcust_id",QueryByLabelLikeIfIdEmpty=true,
                   SqlForOptions=CommonTool.selectSupcust } },
                {"seller_id",new DataItem(){FldArea="divHead",Title="业务员",Checkboxes=true, LabelFld="seller_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},
                {"maker_id",new DataItem(){FldArea="divHead",Title="操作人",Checkboxes=true, LabelFld="maker_name",SqlFld="t.maker_id",ButtonUsage="list",SqlForOptions=CommonTool.selectSellers,CompareOperator="="}},

                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
            {"sheet_type",new DataItem(){Title="单据类型",FldArea="divHead",LabelFld="sheet_type_name",ButtonUsage="list",CompareOperator="=",
                    Source = @"[{v:'X',l:'销售单'},{v:'T',l:'销售退货单'},{v:'DH',l:'订货单'},{v:'ZC',l:'支出单'},{v:'YS',l:'预收款单'},
                                {v:'',l:'所有'}]"}},
            {"arrears_order_status",new DataItem(){ FldArea="divHead",Title="对账状态",LabelFld = "arrears_order_status_v",LabelInDB = false,ForQuery=false,ButtonUsage="list",Value = "all", Label = "所有",QueryOnChange = false, Hidden=false, CompareOperator = "=", NullEqualValue = "all",
                    Source = @"[{v:'none',l:'待对帐'},
                                {v:'unapproved',l:'对账中'},
                                {v:'approved',l:'待收款'},                                   
                                {v:'all',l:'所有'}]"
                }},
             {"sheet_no",new DataItem(){FldArea="divHead",Title="单号", CompareOperator="like"}},
                //{"sup_group",new DataItem(){FldArea="divHead",Title="渠道",LabelFld="sup_group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sc.sup_group",
                //    SqlForOptions="select distinct sup_group as v, sup_group as l from info_supcust where company_id=~COMPANY_ID and sup_group is not null and sup_group != '' order by sup_group"
                //}},
                {"group_id",new DataItem(){FldArea="divHead",Title="渠道",Checkboxes=true,LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sc.sup_group",
                    SqlForOptions="select group_id as v,group_name as l from info_supcust_group"
                }},
                {"make_brief",new DataItem(){FldArea="divHead",Title="备注", CompareOperator="like"}},

            };

            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",     new DataItem(){Hidden = true, HideOnLoad = true} },
                       {"sheet_no",     new DataItem(){Title="单号",      Width="100" ,Linkable = true}},
                       {"sup_name",   new DataItem(){Title="客户",  Width="200",Sortable=true}},
                       {"sup_addr",   new DataItem(){Title="地址",  Width="200"}},
                       {"supcust_id",   new DataItem(){ Hidden = true, HideOnLoad = true,SqlFld="sc.supcust_id"}},
                       {"seller_name", new DataItem(){Title="业务员",  Width="80",Sortable=true}},
                       {"maker_name", new DataItem(){Title="操作人",  Width="80",Sortable=true}},
                       //{"sup_group", new DataItem(){Title="渠道", Width="100", SqlFld="sc.sup_group", Sortable=true}},
                       {"group_name", new DataItem(){Title="渠道", Width="100", SqlFld="group_name", Sortable=true}},
                       {"sheet_type",new DataItem(){Title="单据类型", Width="100",Sortable=true,
                           SqlFld=@"(case 
                                          when sheet_type='X' then '销售单' 
                                          when sheet_type='T' then '销售退货单' 
                                          when sheet_type = 'YS' then '预收款单'
                                          when sheet_type = 'DH' then '订货单'
                                          when sheet_type = 'ZC' then '支出单'
                                    end)"}},
                       {"sheetType",new DataItem(){Hidden = true, HideOnLoad = true, SqlFld="sheet_type"}},
                       {"happen_time",     new DataItem(){Title="发生时间",Sortable=true} },
                       {"approve_time",     new DataItem(){Title="审核时间",Sortable=true} },
                       {"total_amount",     new DataItem(){Title="总金额",Sortable=true,ShowSum=true ,SqlFld="total_amount * money_inout_flag" ,} },
                       {"paid_amount",     new DataItem(){Title="已支付",Sortable=true,ShowSum=true ,SqlFld="paid_amount* money_inout_flag"} },
                       {"disc_amount",     new DataItem(){Title="已优惠",Sortable=true,ShowSum=true ,SqlFld="disc_amount* money_inout_flag"} },
                       {"left_amount",     new DataItem(){Title="尚欠",  SqlFld="(total_amount -paid_amount-disc_amount)* money_inout_flag" ,Sortable=true,ShowSum=true} },
                       {"make_brief",     new DataItem(){Title="备注",} },
                       {"getarrear",     new DataItem(){Title="操作", SqlFld=" ('收款')"   ,Linkable = true} },
                     },
                     QueryFromSQL=@"
from 
(
	SELECT
		sheet_id,sheet_no, supcust_id,seller_id, maker_id,happen_time, approve_time,sheet_type,make_brief ,money_inout_flag,total_amount,paid_amount,disc_amount ,arrears_order_sheet_id
	FROM
		sheet_sale_main 
where company_id = ~COMPANY_ID  
and happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay'
	AND approve_time IS NOT NULL 
	AND red_flag IS NULL 
	and 	ABS ( total_amount - paid_amount - disc_amount ) >= 0.01 

UNION
	SELECT
		sheet_id,sheet_no, supcust_id,getter_id as  seller_id, maker_id,happen_time, approve_time,sheet_type,make_brief ,money_inout_flag,total_amount,paid_amount,disc_amount ,arrears_order_sheet_id
	FROM
		sheet_prepay 
where company_id = ~COMPANY_ID  
and happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay'
		AND approve_time IS NOT NULL and prepay_sub_id <> '-1'
		AND red_flag IS NULL 
	and 	ABS ( total_amount - paid_amount - disc_amount ) >= 0.01 

UNION
	SELECT
		sheet_id,sheet_no, supcust_id, null seller_id, maker_id,happen_time, approve_time,sheet_type,make_brief ,money_inout_flag,total_amount,	paid_amount,disc_amount ,arrears_order_sheet_id
	FROM
		sheet_fee_out_main 
where company_id = ~COMPANY_ID  
and happen_time >= '~VAR_startDay' AND happen_time <='~VAR_endDay'
AND approve_time IS NOT NULL 
AND red_flag IS NULL 
and 	ABS ( total_amount - paid_amount - disc_amount ) >= 0.01 

	) T 
LEFT JOIN (select sheet_id as aom_sheet_id ,approve_time as aom_approve_time, company_id FROM sheet_get_arrears_order_main WHERE company_id = ~COMPANY_ID and red_flag is null) aom on  t.arrears_order_sheet_id = aom.aom_sheet_id
LEFT JOIN (select supcust_id ,sup_name,other_region,sup_addr, sup_group from info_supcust where company_id= ~COMPANY_ID and (supcust_flag = 'C' or supcust_flag = 'CS' )) sc on t.supcust_id = sc.supcust_id
LEFT JOIN (select group_id, group_name, company_id from info_supcust_group) isg on sc.sup_group = isg.group_id and isg.company_id = ~COMPANY_ID
LEFT JOIN (select oper_id,oper_name as seller_name from info_operator where company_id= ~COMPANY_ID) seller on t.seller_id = seller.oper_id
LEFT JOIN (select oper_id as  maker_id,oper_name as maker_name from info_operator where company_id= ~COMPANY_ID) maker on t.maker_id = maker.maker_id
where  happen_time is not null ~VAR_arrears_order_condi


", 

                     QueryGroupBySQL = "",
                     QueryOrderSQL="ORDER BY   happen_time  desc "
                  }
                } 
            };             
        }


        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;

            // 对账状态产生的条件勾选
            string arrears_order_status = DataItems["arrears_order_status"].Value;
            SQLVariables["arrears_order_condi"] = " AND (t.total_amount - t.paid_amount - t.disc_amount > 0.01 or t.total_amount - t.paid_amount - t.disc_amount < -0.01)";
            if (arrears_order_status == "none")
            {
                // 待对帐
                SQLVariables["arrears_order_condi"] += " AND t.arrears_order_sheet_id is null";
            }
            else if (arrears_order_status == "unapproved")
            {
                // 对账中
                SQLVariables["arrears_order_condi"] += " AND t.arrears_order_sheet_id is not null AND aom.aom_approve_time is null ";
            }
            else if (arrears_order_status == "approved")
            {
                SQLVariables["arrears_order_condi"] += " AND t.arrears_order_sheet_id is not null AND aom.aom_approve_time is not null";
            }
            else
            {
                SQLVariables["arrears_order_condi"] = "";
            }
        }
    }



    [Route("api/[controller]/[action]")]
    public class ClientArrearsDetailController : QueryController
    { 
        public ClientArrearsDetailController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ClientArrearsDetailModel model = new ClientArrearsDetailModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            ClientArrearsDetailModel model = new ClientArrearsDetailModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            ClientArrearsDetailModel model = new ClientArrearsDetailModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }

    }
}
