
using ArtisanManage.Enums;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace ArtisanManage.Pages
{
    public class OpenAccount : PageModel
    {
        public CMySbCommand cmd;

        internal static object[] printTmp;
        internal static Dictionary<string,Rights> roles;

        public string AdministrativeRegions { get; set; }
        public OpenAccount(CMySbCommand command)
        {
            cmd = command;
        }


        public List<Company> Companies { get; private set; }
        public List<Business> Businesses { get; private set; }
        public List<ExpandoObject> AvailFeeDiscounts = new List<ExpandoObject>();

        public async Task OnGet()
        {
            if (roles == null)
            {
                var s = await System.IO.File.ReadAllTextAsync(Environment.CurrentDirectory + "/JsonFiles/roles.json");
                roles = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, Rights>>(s);
            }
            if (printTmp==null)
            {
                var tmpls = await System.IO.File.ReadAllTextAsync(Environment.CurrentDirectory + "/JsonFiles/printTemplates.json");
                printTmp = Newtonsoft.Json.JsonConvert.DeserializeObject<object[]>(tmpls);
            }
            string sql = "select distinct fee_discount from g_role_template where fee_discount>0;";
            AvailFeeDiscounts = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

        }
    }

    [Route("api/[controller]/[action]")]
    public class OpenAccountController : YjController
    {
        CMySbCommand cmd;
        private readonly IHttpClientFactory _httpClientFactory;
        public OpenAccountController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }

        [HttpPost]
        public async Task<Response> PrepareAccount(OpenAccountMode mode,[FromBody] dynamic dCompany)
        {
            Company company = JsonConvert.DeserializeObject<Company>(JsonConvert.SerializeObject(dCompany));
            cmd.CommandText = $"select count(Company_Id) from g_company where company_name='{company.companyName}' and boss_mobile = '{company.bossMobile}'";
            Int64 b = (Int64)await cmd.ExecuteScalarAsync();
            if (b > 0) return Error("账户已存在");

            cmd.CommandText = @"select server_id,server_uri from g_setting 
                LEFT JOIN g_server on g_server.server_id = g_setting.set_value::int
                where set_name='activeServer'";
            var dr = await cmd.ExecuteReaderAsync();

          

            string url = "";
            while (dr.Read())
            {
                company.serverId = (int)dr[0];//
                url = (string)dr[1];
            }
            dr.Close();
#if (DEBUG)
            url = "127.0.0.1";
#endif
            if (!url.StartsWith("http")) url = "http://" + url;
            url = $"{url}/api/OpenAccount/CreateAccount";
            var records = new List<string>();
            try
            {
                company.createTime = DateTime.Now;
                var table = new List<Company> { company }.ToDataTable();
                var comanyId = (await table.SaveAsync(cmd, "company_id"))[0];
                records.Add($"DELETE FROM {table.Name} WHERE company_id = {comanyId}");
                var boss = new User_g
                {
                    company_Id = comanyId,
                    UserName = company.bossName,
                    Mobile = company.bossMobile,
                    IsAdmin = true
                };

                table = new List<User_g> { boss }.ToDataTable();
                var bossIds = await table.SaveAsync(cmd, "oper_id");
                records.Add($"DELETE FROM {table.Name} WHERE company_id = {comanyId}");
                boss.UserId = bossIds[0];
                await TokenChecker.UpdateOperPwd(boss.UserId.ToString(), boss.Password);
                AccountData data = mode switch
                {
                    OpenAccountMode.初始化模板 => AccountData.CreateTemplate(boss),
                    _ => await AccountData.GetTemplateAsync(boss, cmd, company.companyTmpl),
                };
                data.business_id = company.businessId.ToString();


                Response result = mode switch
                {
                    OpenAccountMode.开户 => await PostAsync(url, data),
                    _ => await CreateAccount(data)
                };
             

                if (result["result"]=="OK")
                {
                    cmd.CommandText = mode switch
                    {
                        OpenAccountMode.初始化模板 => $"update Business set company_id = {comanyId} where Business_Id = {company.businessId}",
                        OpenAccountMode.继承模板 => $"insert into Business (company_id,business_name) values({comanyId},'{company.companyName}')",
                        _ => null
                    };
                    if (cmd.CommandText.IsValid())
                    {
                        await cmd.ExecuteNonQueryAsync();
                    }
                }
                else throw new MyException(result["msg"]);

                //if (Startup.Localhost == "https://www.yingjiang168.com")
                // {
                dynamic postData = new { oper_id = boss.UserId, pwd=boss.Password};
              //  await CommonTool.PostJsonData("https://www.yingjiang.co/api/OpenAccount/UpdateOperInfo", postData);
               // }

            }
            catch (MyException ex)
            {
                if (records.Count == 0)
                {
                    Error(ex.Message);
                }
                cmd.CommandText = string.Join(';',records);
                await cmd.ExecuteNonQueryAsync();
                Error(ex.Message); 
            }
            return response;
        }

        private async Task<Response> PostAsync(string url, object data)
        {
            // var stream = Newtonsoft.Json.JsonConvert.SerializeObject(data);
            // byte[] bs = Encoding.UTF8.GetBytes(stream);

#if DEBUG
            url = "http://LOCALHOST/api/OpenAccount/CreateAccount";
#endif
           

            dynamic res = await CommonTool.PostJsonByFactory(_httpClientFactory, url, data);
             
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Response>(JsonConvert.SerializeObject(res));
        }

        [HttpPost]
        public async Task<Response> CreateAccount([FromBody] AccountData account)
        {
            try
            {
                await account.SaveAsync(cmd);
                await TokenChecker.UpdateOperPwd(account.Boss.UserId.ToString(),  account.Boss.Password);

                return response;

            }
            catch (Exception ex)
            {
                account.Abort(cmd);
                return Error("开户失败:" + ex.Message);
            }
        }


        [HttpPost]
        public async Task<Response> UpdateAccount([FromBody] Company company)
        {
            return await RespondAsync(async response =>
            {
               await  cmd.UpdateWhereAsync(company);
            });
        }

        [HttpPost]
        public async Task<Response> ResetPassword([FromBody] User_g  user)
        {
            return await RespondAsync(async response =>
            {
                if (user.Password != "yj168168")
                {
                    Error("密码错误");
                    return;
                }
                var employee = await cmd.QueryAsync<User_g>(user.company_Id.ToString(), $"mobile = '{user.Mobile}'");
                if (employee.Count == 0)
                {
                    Error("手机号码不存在");
                    return;
                }
                user.Password = "123456";

                await cmd.UpdateWhereAsync(user, $"WHERE company_id = {user.company_Id} and mobile = '{user.Mobile}'", "oper_pw");
                response["msg"] = "密码已重置";
                await TokenChecker.UpdateOperPwd(user.UserId.ToString(), (string)user.Password);
            });
        }

        [HttpPost]
        public async Task<Response> UpdateOperInfo([FromBody] dynamic data)
        {
            string oper_id = data.oper_id;
            string pwd = data.pwd;

            await TokenChecker.UpdateOperPwd(oper_id, pwd);

            return await RespondAsync(async response =>
            {                 
                response["msg"] = "OK";               
            });
        }

        [HttpGet]
        public async Task<Response> UpdateAllOperInfo()
        {  
            await TokenChecker.LoadAllUsers();

            return await RespondAsync(async response =>
            {
                response["msg"] = "OK";
            });
        }
        

        //[HttpGet]
        //public async Task<Response> GetList(int id, string mobile, [FromBody] Company company)
        //{
        //    return await RespondAsync(async response =>
        //    {
        //        response.Add("data", await cmd.QueryWhereAsync<Company>("order by company_id desc"));
        //    });
        //}

        [HttpGet]
        public async Task<Response> GetCompanies_abandoned(string text,DateTime? createTimeStart,DateTime? createTimeEnd)
        {
            var where = "where true";
            if (text.IsValid()) where += " and " + string.Join(" or ", new string[] { "company_name", "boss_name", "boss_mobile", "agent_name" }.Select(x=>$"{x} like '%{text}%'"));
            if(createTimeStart !=null && createTimeEnd != null && createTimeEnd > createTimeStart)
            {
                where += $" and create_time between '{createTimeStart}' and '{createTimeEnd}'";
            }
            where += " order by company_id desc";
            return await RespondAsync(async response =>
            {
                response.Add("data", await cmd.QueryWhereAsync<Company>(where));
            });
        }

        /// <summary>
        /// 慎重
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task DeleteAccount_Abandoned(int id)
        {
            var sqls = new string[]
            {
                //全删
                //"g_company","g_operator","info_operator","info_branch","info_supcust_rank","Company_Setting",
                //"info_department","info_item_class","info_pay_way","Print_Template","info_region","info_role",
                //"cw_subject","info_avail_unit"

                //只删除商品档案
                //"info_item_prop"
            }.Select(tableName => $"DELETE FROM {tableName} WHERE company_id = {id}");
            cmd.CommandText = string.Join(";", sqls);
            await cmd.ExecuteNonQueryAsync();
        }
    }
}
