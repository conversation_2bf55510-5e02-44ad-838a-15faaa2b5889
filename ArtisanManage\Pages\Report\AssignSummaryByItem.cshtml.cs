﻿
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;

namespace ArtisanManage.Pages.BaseInfo
{
    public class AssignSummaryByItemModel : PageQueryModel
    { 


        
        public AssignSummaryByItemModel(CMySbCommand cmd) : base(Services.MenuId.salesSummaryByItem)
        {
            this.UsePostMethod = true;
            this.cmd = cmd;
            this.PageTitle = "装车汇总(商品)";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="opr.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="opr.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
              //  {"depart_path",new DataItem(){Title="部门",Hidden=true, FldArea="divHead",LabelFld="depart_path_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="like",LikeWrapper="/",
              //      SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
              //  }},
              //  {"brand_id",new DataItem(){Title="品牌",Checkboxes=true, FldArea="divHead",LabelFld="brand_name",ButtonUsage="list",CompareOperator="=",SqlFld="ip.item_brand",
              //      SqlForOptions ="select brand_id as v,brand_name as l from info_item_brand"}},
              //  {"other_class",new DataItem(){Title="类别",FldArea="divHead",MaxRecords="1000", LabelFld="class_name",CtrlType="jqxDropDownTree",TreePathFld="other_class",MumSelectable=true,CompareOperator="like",
              //     SqlForOptions="select class_id as v,class_name as l,py_str as z,mother_id as pv from info_item_class"} },
              //  /*{"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",SqlFld="opr.item_id",DropDownWidth="300",
              //     QueryByLabelLikeIfIdEmpty=true, SqlForOptions ="select item_id as v,item_name as l,py_str as z from info_item_prop" }},*/
                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="opr.item_id",DropDownWidth="300",
                SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
              //  {"seller_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="seller_name",Checkboxes=true,ButtonUsage="list",CompareOperator="=",SqlFld="seller_id",SqlForOptions=CommonTool.selectSellers } },
              //      //SqlForOptions ="select oper_id as v,oper_name as l from info_operator"}},

                {"branch_id",new DataItem(){Title="配送车辆",FldArea="divHead",LabelFld="branch_name",Checkboxes=true,ButtonUsage="list",CompareOperator="=",SqlFld="opm.to_van",
                SqlForOptions=CommonTool.selectVan }},
              //  {"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name",ButtonUsage="event",Checkboxes=true,QueryByLabelLikeIfIdEmpty=true, CompareOperator="=",SqlFld="sm.supcust_id",
              //      SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID "}},
              //  {"group_id",new DataItem(){Title="渠道",FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
              //      SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
              //  {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
              //      SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
              //  }},
              //  //{"sup_rank",new DataItem(){Title="等级",FldArea="divHead",LabelFld="rank_name",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",CompareOperator="=",
              //  {"sup_rank",new DataItem(){Title="等级",FldArea="divHead",LabelFld="rank_name",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",CompareOperator="=",
              //      SqlForOptions="select rank_id as v,rank_name as l from info_supcust_rank"
              //  }},
       
              //  {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'2',l:'加权平均价'},{v:'3',l:'预设进价'},{v:'1',l:'预设成本'}]", CompareOperator="=" }},
                {
                    "senders_id",
                    new DataItem()
                    {
                        FldArea = "divHead", Title = "送货员",SqlFld = "','||opm.senders_id||','", ButtonUsage = "list",
                        Checkboxes=true,
                        DealQueryItem = status => ","+status+",",
                        SqlForOptions=CommonTool.selectSenders,  
                        CompareOperator = "like"
                    }
                },
                {"sheet_no",new DataItem(){Title="单号编号",FldArea="divHead",  SqlFld="sm.sheet_no",CompareOperator="like"}},
              //  {"make_brief",new DataItem(){Title="整单备注",FldArea="divHead",CompareOperator="ilike" } },
              //  {"remark",new DataItem(){Title="明细备注",FldArea="divHead",CompareOperator="ilike" } },
              //  {"status",new DataItem(){FldArea="divHead",  Hidden=true,   Checkboxes=true,Title="单据状态", ButtonUsage = "list",CompareOperator="=",Value="approved",Label="已审核",
              //          Source = @"[{v:'normal',l:'正常单据',condition:""sm.red_flag is null""},
              //                     {v:'unapproved',l:'未审核',condition:""sm.approve_time is null""},
              //                     {v:'approved',l:'已审核',condition:""sm.approve_time is not null and red_flag is null""}]"

              //  }},
              //   {"trade_type",new DataItem(){FldArea="divHead",Checkboxes=true, Title="交易类型",ButtonUsage = "list",CompareOperator="=",Value="",Label="",
              //          Source = @"[{v:'ALL',l:'所有',condition:""true""},
              //                      {v:'X',l:'销售',condition:""sd.quantity*sd.inout_flag<0""},
              //                     {v:'T',l:'退货',condition:""sd.quantity*sd.inout_flag>0""},
              //                     {v:'XT',l:'销退',condition:""coalesce(trade_type,'X') in ('X','T','XD','TD')""},
              //                     {v:'DH',l:'定货还货',condition:""trade_type='DH'""},
              //                     {v:'JH',l:'借还货',condition:""trade_type in ('J','H')""},
              //                     {v:'CL',l:'陈列兑付',condition:""trade_type ='CL'""},
              //                     {v:'HH',l:'换货',condition:""trade_type in ('HR','HC')""}]"
              //  }},
              //   {"arrears_status",new DataItem(){FldArea="divHead",Title="欠款情况", ButtonUsage = "list",CompareOperator="=",
              //      Source = @"[{v:'cleared',l:'已结清',condition:""abs(total_amount-paid_amount-disc_amount)<0.1""},
              //                   {v:'uncleared',l:'未结清',condition:""abs(total_amount-paid_amount-disc_amount)>0.1""},
              //                   {v:'all',l:'所有',condition:""true""}]"
              //  }},
              //  {"sheetType",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,HideOnLoad = true} }
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"item_id",    new DataItem(){Title="商品",  Width="150",SqlFld="opr.item_id",Hidden=true,HideOnLoad=true}},
                       {"item_name",    new DataItem(){Title="商品名称",  Width="150",SqlFld="ip.item_name",Sortable=true,IsChinese=true,Pinned=true}},
                       {"item_no",    new DataItem(){Title="商品编号",  Width="100",SqlFld="ip.item_no",Sortable=false}},
                       {"item_spec",    new DataItem(){Title="商品规格",  Width="100",SqlFld="ip.item_spec",Sortable=false}},
                       //{"brand_name",    new DataItem(){Title="品牌",  Width="10", Hidden=true, Sortable=true,IsChinese=true}},
                       {"s_barcode",    new DataItem(){Title="条码(小)",  Width="100",Hidden=true,SqlFld="t.s_barcode", Sortable=false}},
                       {"b_barcode",    new DataItem(){Title="条码(大)",  Width="100",Hidden=true,SqlFld="t.m_barcode", Sortable=false}},
                       {"m_barcode",    new DataItem(){Title="条码(中)",  Width="100",Hidden=true,SqlFld="t.b_barcode", Sortable=false}},
                       {"s_unit_no",    new DataItem(){Title="小单位",  Width="100",SqlFld="t.s_unit_no", Sortable=false}},
                       {"s_total_order_qty",    new DataItem(){Title="小单位订单数",  Width="100",SqlFld="sum((sheet_order_quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric", Sortable=true,ShowSum=true}},
                       //{"total_order_amount",    new DataItem(){Title="订单金额",  Width="100",SqlFld="sum(sd.sub_amount)::numeric", Sortable=true,ShowSum=true}},
                       //{"b_unit_no",   new DataItem(){Title="大单位名称", Width="",SqlFld="b_unit_no",Hidden=true,HideOnLoad = true}},
                       //{"x_quantity1",   new DataItem(){Title="销售量(无单位)", Width="10%",Hidden=true,HideOnLoad = true,SqlFld="round((sum(case when sd.quantity>0 and sd.sub_amount!=0 then sd.quantity*sd.unit_factor*(-1) else 0 end)::numeric/b_unit_factor::numeric),2)"}},
                       {"total_order_qty",   new DataItem(){Title="订单数量", CellsAlign="center", Sortable=true,   Width="80",
                           SqlFld="unit_from_s_to_bms ( sum((sheet_order_quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no ) ",
                           SortFld="sum((sheet_order_quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric/b_unit_factor", 
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty = "";
                               if(sumColumnValues["total_order_qty_b"]!="")  sQty+= sumColumnValues["total_order_qty_b"]+"大";
                               if(sumColumnValues["total_order_qty_m"]!="")  sQty+= sumColumnValues["total_order_qty_m"]+"中";
                               if(sumColumnValues["total_order_qty_s"]!="")  sQty+= sumColumnValues["total_order_qty_s"]+"小";
                               return sQty;
                           }
                       }},
                       {"total_order_qty_b",   new DataItem(){Title="订单数量(大)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,
                            SqlFld="yj_get_unit_qty('b',sum((sheet_order_quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"total_order_qty_m",   new DataItem(){Title="订单数量(中)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,
                            SqlFld="yj_get_unit_qty('m',sum((sheet_order_quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"total_order_qty_s",   new DataItem(){Title="订单数量(小)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,
                            SqlFld="yj_get_unit_qty('s',sum((sheet_order_quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"s_total_qty",    new DataItem(){Title="小单位装车数",  Width="100",SqlFld="sum((quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric", Sortable=true,ShowSum=true}},
                       {"total_qty",   new DataItem(){Title="装车数量", CellsAlign="center", Sortable=true,   Width="80",
                           SqlFld="unit_from_s_to_bms ( sum((quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no ) ",
                           SortFld="sum((quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric/b_unit_factor",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty = "";
                               if(sumColumnValues["total_qty_b"]!="")  sQty+= sumColumnValues["total_qty_b"]+"大";
                               if(sumColumnValues["total_qty_m"]!="")  sQty+= sumColumnValues["total_qty_m"]+"中";
                               if(sumColumnValues["total_qty_s"]!="")  sQty+= sumColumnValues["total_qty_s"]+"小";
                               return sQty;
                           }
                       }},
                       {"total_qty_b",   new DataItem(){Title="装车数量(大)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,
                            SqlFld="yj_get_unit_qty('b',sum((quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"total_qty_m",   new DataItem(){Title="装车数量(中)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,
                            SqlFld="yj_get_unit_qty('m',sum((quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"total_qty_s",   new DataItem(){Title="装车数量(小)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,
                            SqlFld="yj_get_unit_qty('s',sum((quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"s_total_retreat_qty",    new DataItem(){Title="小单位回撤数",  Width="100",SqlFld="sum(retreat_qty*unit_factor)::numeric", Sortable=true,ShowSum=true}},
                       {"total_retreat_qty",   new DataItem(){Title="回撤数量", CellsAlign="center", Sortable=true,   Width="80",
                           SqlFld="unit_from_s_to_bms ( sum(retreat_qty*unit_factor)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no ) ",
                           SortFld="sum(retreat_qty*unit_factor)::numeric/b_unit_factor",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty = "";
                               if(sumColumnValues["total_retreat_qty_b"]!="")  sQty+= sumColumnValues["total_retreat_qty_b"]+"大";
                               if(sumColumnValues["total_retreat_qty_m"]!="")  sQty+= sumColumnValues["total_retreat_qty_m"]+"中";
                               if(sumColumnValues["total_retreat_qty_s"]!="")  sQty+= sumColumnValues["total_retreat_qty_s"]+"小";
                               return sQty;
                           }
                       }},
                       {"total_retreat_qty_b",   new DataItem(){Title="回撤数量(大)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,
                            SqlFld="yj_get_unit_qty('b',sum(retreat_qty*unit_factor)::numeric,b_unit_factor,m_unit_factor,false) ",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"total_retreat_qty_m",   new DataItem(){Title="回撤数量(中)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,
                            SqlFld="yj_get_unit_qty('m',sum(retreat_qty*unit_factor)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"total_retreat_qty_s",   new DataItem(){Title="回撤数量(小)", CellsAlign="center",   Width="80",ShowSum=true,Hidden=true,
                            SqlFld="yj_get_unit_qty('s',sum(retreat_qty*unit_factor)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       //{"total_order_qty",   new DataItem(){Title="订单数量", CellsAlign="center", Sortable=true,   Width="8%",
                       //    SqlFld="unit_from_s_to_bms ( sum((sheet_order_quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no ) ",
                       //    SortFld="sum((sheet_order_quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric/b_unit_factor",
                       //    FuncDealMe=(value)=>{return value=="0"?"":value; },
                       //     FuncGetSumValue = (sumColumnValues) =>
                       //    {
                       //        string sQty = "";
                       //        if(sumColumnValues["total_order_qty_b"]!="")  sQty+= sumColumnValues["total_order_qty_b"]+"大";
                       //        if(sumColumnValues["total_order_qty_m"]!="")  sQty+= sumColumnValues["total_order_qty_m"]+"中";
                       //        if(sumColumnValues["total_order_qty_s"]!="")  sQty+= sumColumnValues["total_order_qty_s"]+"小";
                       //        return sQty;
                       //    }
                       //}},
                       //{"total_order_qty_b",   new DataItem(){Title="订单数量(大)", CellsAlign="center",   Width="8%",ShowSum=true,Hidden=true,
                       //     SqlFld="yj_get_unit_qty('b',sum((sheet_order_quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },
                       //}},
                       //{"total_order_qty_m",   new DataItem(){Title="订单数量(中)", CellsAlign="center",   Width="8%",ShowSum=true,Hidden=true,
                       //     SqlFld="yj_get_unit_qty('m',sum((sheet_order_quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },
                       //}},
                       //{"total_order_qty_s",   new DataItem(){Title="订单数量(小)", CellsAlign="center",   Width="8%",ShowSum=true,Hidden=true,
                       //     SqlFld="yj_get_unit_qty('s',sum((sheet_order_quantity-COALESCE(retreat_qty,0))*unit_factor)::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },
                       //}},
                        

                       //{"t_quantity1",   new DataItem(){Title="退货量(无单位)", Hidden=true, HideOnLoad = true, Width="8%",SqlFld="round((sum(case when sd.quantity<0 then sd.quantity*sd.unit_factor else 0 end)::numeric/b_unit_factor::numeric),2)"}},
                       //{"t_quantity",   new DataItem(){Title="退货量", CellsAlign="center",   Width="8%", Sortable=true,
                       //    SqlFld="unit_from_s_to_bms (sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) ",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },
                       //     FuncGetSumValue = (sumColumnValues) =>
                       //    {
                       //        string sQty = "";
                       //        if(sumColumnValues["t_quantity_b"]!="") sQty+= sumColumnValues["t_quantity_b"]+"大";
                       //        if(sumColumnValues["t_quantity_m"]!="") sQty+= sumColumnValues["t_quantity_m"]+"中";
                       //        if(sumColumnValues["t_quantity_s"]!="") sQty+= sumColumnValues["t_quantity_s"]+"小";
                       //        return sQty;
                       //    }
                       //}},
                       // {"t_quantity_b",   new DataItem(){Title="退货量(大)", CellsAlign="center",   Width="8%",ShowSum=true,Hidden=true,HideOnLoad = true,
                       //    SqlFld="yj_get_unit_qty('b',sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; }
                       //}},
                       // {"t_quantity_m",   new DataItem(){Title="退货量(中)", CellsAlign="center",   Width="8%",ShowSum=true,Hidden=true,HideOnLoad = true,
                       //    SqlFld="yj_get_unit_qty('m',sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; }
                       //}},
                       // {"t_quantity_s",   new DataItem(){Title="退货量(小)", CellsAlign="center",   Width="8%",ShowSum=true,Hidden=true,HideOnLoad = true,
                       //    SqlFld="yj_get_unit_qty('s',sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; }
                       //}},
                       //{"z_quantity1",   new DataItem(){Title="赠品量(无单位)", Width="10%",Hidden=true,HideOnLoad = true,SqlFld="round((sum(case when sd.quantity!=0 and sd.sub_amount=0 and  coalesce(trade_type,'X') not  in ('CL','H' ,'J')  then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric/b_unit_factor::numeric),2)"}},
                       //{"z_quantity",   new DataItem(){Title="赠品量", CellsAlign="center",Width="8%",Sortable=true,
                       //    SqlFld="unit_from_s_to_bms ((sum(case when sd.sub_amount=0 and  coalesce(trade_type,'X') not  in ('CL','H' ,'J')  then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                       //    FuncDealMe=(value)=>{return value=="0"?"":value; },
                       //    FuncGetSumValue = (sumColumnValues) =>
                       //    {
                       //        string sQty = "";
                       //         if(sumColumnValues["z_quantity_b"]!="") sQty+= sumColumnValues["z_quantity_b"]+"大";
                       //        if(sumColumnValues["z_quantity_m"]!="") sQty+= sumColumnValues["z_quantity_m"]+"中";
                       //        if(sumColumnValues["z_quantity_s"]!="") sQty+=sumColumnValues["z_quantity_s"]+"小";
                       //        return sQty;
                       //    }
                       //}},
                       // {"z_quantity_b",   new DataItem(){Title="赠品量(大)", CellsAlign="center",   Width="8%",ShowSum=true, Hidden=true,HideOnLoad = true,
                       //    SqlFld="yj_get_unit_qty('b',(sum(case when sd.sub_amount=0 and  coalesce(trade_type,'X') not  in ('CL','H' ,'J')  then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,false)",
                       //    FuncDealMe=(value)=>{return value=="0"?"":value; } 
                       //}},
                       // {"z_quantity_m",   new DataItem(){Title="赠品量(中)", CellsAlign="center",   Width="8%",ShowSum=true, Hidden=true,HideOnLoad = true,
                       //    SqlFld="yj_get_unit_qty('m',(sum(case when sd.sub_amount=0 and  coalesce(trade_type,'X') not  in ('CL','H' ,'J')  then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,false)",
                       //    FuncDealMe=(value)=>{return value=="0"?"":value; }
                       //}},
                       //  {"z_quantity_s",   new DataItem(){Title="赠品量(小)", CellsAlign="center",   Width="8%",ShowSum=true, Hidden=true,HideOnLoad = true,
                       //    SqlFld="yj_get_unit_qty('s',(sum(case when sd.sub_amount=0 and  coalesce(trade_type,'X') not  in ('CL','H' ,'J')  then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,false)",
                       //    FuncDealMe=(value)=>{return value=="0"?"":value; }
                       //}},
                       ////{"net_quantity1", new DataItem(){Title="净销量(无单位)",  Hidden=true,HideOnLoad = true,  Width="10%",SqlFld="round(sum(sd.quantity*sd.unit_factor*sd.inout_flag*(-1))::numeric/b_unit_factor,2)"}},
                       //{"net_quantity_hasfree", new DataItem(){Title="净销量（含赠）",  CellsAlign="center",  Width="8%",SqlFld="unit_from_s_to_bms(sum( sd.quantity*sd.unit_factor*sd.inout_flag*(-1) )::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },
                       //     FuncGetSumValue = (sumColumnValues) =>
                       //     {
                       //        string sQty = "";
                       //         if(sumColumnValues["net_quantity_b_hasfree"]!="") sQty+= sumColumnValues["net_quantity_b_hasfree"]+"大";
                       //        if(sumColumnValues["net_quantity_m_hasfree"]!="") sQty+= sumColumnValues["net_quantity_m_hasfree"]+"中";
                       //        if(sumColumnValues["net_quantity_s_hasfree"]!="") sQty+=sumColumnValues["net_quantity_s_hasfree"]+"小";
                       //        return sQty;
                       //     }
                       //}},
                       //{"net_quantity_b_hasfree", new DataItem(){Title="净销量（含赠）(大)",  CellsAlign="center",  Width="8%",SqlFld="yj_get_unit_qty ('b',sum( sd.quantity*sd.unit_factor*sd.inout_flag*(-1) )::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,

                       //}},
                       // {"net_quantity_m_hasfree", new DataItem(){Title="净销量（含赠）(中)",  CellsAlign="center",  Width="8%",SqlFld="yj_get_unit_qty ('m',sum( sd.quantity*sd.unit_factor*sd.inout_flag*(-1))::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       //}},
                       // {"net_quantity_s_hasfree", new DataItem(){Title="净销量（含赠）(小)",  CellsAlign="center",  Width="8%",SqlFld="yj_get_unit_qty ('s',sum( sd.quantity*sd.unit_factor*sd.inout_flag*(-1) )::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       //}},


                       //{"net_quantity", new DataItem(){Title="净销量",  CellsAlign="center", Sortable=true,  Width="8%",SqlFld="unit_from_s_to_bms(sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },
                       //     FuncGetSumValue = (sumColumnValues) =>
                       //     {
                       //        string sQty = "";
                       //         if(sumColumnValues["net_quantity_b"]!="") sQty+= sumColumnValues["net_quantity_b"]+"大";
                       //        if(sumColumnValues["net_quantity_m"]!="") sQty+= sumColumnValues["net_quantity_m"]+"中";
                       //        if(sumColumnValues["net_quantity_s"]!="") sQty+=sumColumnValues["net_quantity_s"]+"小";
                       //        return sQty;
                       //     }
                       //}},
                       //{"net_quantity_b", new DataItem(){Title="净销量(大)",  CellsAlign="center",  Width="8%",SqlFld="yj_get_unit_qty ('b',sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,

                       //}},
                       // {"net_quantity_m", new DataItem(){Title="净销量(中)",  CellsAlign="center",  Width="8%",SqlFld="yj_get_unit_qty ('m',sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       //}},
                       // {"net_quantity_s", new DataItem(){Title="净销量(小)",  CellsAlign="center",  Width="8%",SqlFld="yj_get_unit_qty ('s',sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       //}},
                       //{"net_quantity_per_sender_b", new DataItem(){Title="净销量送货员人均(大单位数)",  CellsAlign="center",  Width="8%",SqlFld="round(sum(sd.quantity*sd.unit_factor*sd.inout_flag*(-1)/array_length(string_to_array(sm.senders_id,','),1))::numeric/b_unit_factor,2)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       //}},
                       //{"x_amount",     new DataItem(){Title="销售金额", CellsAlign="right", Width="8%", Sortable=true,SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.inout_flag*(-1)*sd.sub_amount else 0 end)::numeric,2)",
                       //ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       //{"disc_amount",     new DataItem(){Title="价格优惠", CellsAlign="right", Width="8%", Sortable=true,SqlFld="round(sum((orig_price-real_price)*sd.quantity*sd.inout_flag*(-1))::numeric,2)",ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},

                       //{"t_amount",     new DataItem(){Title="退货金额", CellsAlign="right", Width="8%", Sortable=true,SqlFld="round(sum(case when sd.quantity*sd.inout_flag>0 then sd.sub_amount*sd.inout_flag else 0 end)::numeric,2)",ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       //{"net_amount",   new DataItem(){Title="销售净额", CellsAlign="right", Width="8%", Sortable=true,SqlFld="round(sum(inout_flag*(-1)*sub_amount)::numeric,2)",ShowSum=true,ShowRowPercent=true}},
                       //  //{"disc_amount",  new DataItem(){Title="优惠", CellsAlign="right",    Width="10%",SqlFld="sum(money_inout_flag*disc_amount)",ShowSum=true}},
                       //{"cost_amount_hasfree",  new DataItem(){Title="成本(含赠)", CellsAlign="right",    Width="8%", Sortable=true,SqlFld="Will be changed by condition",ShowSum=true}},
                       //{"profit_hasfree",       new DataItem(){Title="利润(含赠)", CellsAlign="right",    Width="8%", Sortable=true,ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }}},
                       //{"profit_rate_hasfree",new DataItem(){ Title = "利润率(%)(含赠)", Sortable=true,CellsAlign = "right",Width = "7%",ShowAvg = true,
                       // FuncDealMe=(value)=>{return value=="0"?"":value; },
                       //    FuncGetSumValue = (sumColumnValues) =>
                       //    {
                       //        string s_profit_hasfree =sumColumnValues["profit_hasfree"];
                       //        string s_net_amount =sumColumnValues["net_amount"];
                               
                       //        double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                       //        double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                       //        string rate="";
                       //        if (net_amount != 0)
                       //        {
                       //            rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                       //        }
                       //        return rate;
                       //    }
                       //}},
                       //{"cost_amount_free_cl",  new DataItem(){Title="成本(含赠|陈列)", CellsAlign="right",    Width="8%", Sortable=true,SqlFld="Will be changed by condition",ShowSum=true}},
                       //{"profit_free_cl",       new DataItem(){Title="利润(含赠|陈列)", CellsAlign="right",    Width="8%", Sortable=true,ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }}},
                       //{"profit_rate_free_cl",new DataItem(){ Title = "利润率(%)(含赠|陈列)", Sortable=true,CellsAlign = "right",Width = "7%",ShowAvg = true,
                       // FuncDealMe=(value)=>{return value=="0"?"":value; },
                       //    FuncGetSumValue = (sumColumnValues) =>
                       //    {
                       //        string s_profit_hasfree =sumColumnValues["profit_free_cl"];
                       //        string s_net_amount =sumColumnValues["net_amount"];

                       //        double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                       //        double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                       //        string rate="";
                       //        if (net_amount != 0)
                       //        {
                       //            rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                       //        }
                       //        return rate;
                       //    }
                       //}},

                       //{"free_cost_amount",new DataItem(){Title="赠品成本",CellsAlign="right",Width="8%",SqlFld="",ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }} },
                       //{"cost_amount",  new DataItem(){Title="成本", CellsAlign="right",    Width="8%", Sortable=true,SqlFld="Will be changed by condition",ShowSum=true}},
                       //{"profit",       new DataItem(){Title="利润", CellsAlign="right",    Width="8%", Sortable=true,ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }}},
                       //{"profit_rate",new DataItem(){ Title = "利润率(%)", Sortable=true,CellsAlign = "right",Width = "7%",ShowAvg = true,
                       // FuncGetSumValue = (sumColumnValues) =>
                       //    {
                       //        string s_profit_hasfree =sumColumnValues["profit"];
                       //        string s_net_amount =sumColumnValues["net_amount"];

                       //        double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                       //        double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                       //        string rate="";
                       //        if (net_amount != 0)
                       //        {
                       //            rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                       //        }
                       //        return rate;
                       //    }
                       //}},
                       //{"weight", new DataItem(){Title="出货重量(kg)",  CellsAlign="center",  Width="8%",SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.inout_flag*(-1)*mu2.weight else 0 end)::numeric,3)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       //}},
                       // {"volume", new DataItem(){Title="出货体积(m³)",  CellsAlign="center",  Width="8%",SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.inout_flag*(-1)*mu2.volume else 0 end)::numeric,3)",
                       //     FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       //}},
                     },
                     QueryFromSQL=$@"
from op_move_to_van_row opr
LEFT JOIN op_move_to_van_main opm on opm.company_id = opr.company_id and opm.op_id = opr.op_id
left join 
(
    select item_id,     (s->>'f1')::numeric as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_barcode,
                        (b->>'f1')::numeric as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_barcode,
                        (m->>'f1')::numeric as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_barcode
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,weight,volume)) as json from info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
    as errr(item_id int, s jsonb,m jsonb,b jsonb) 
) t
on opr.item_id=t.item_id  
left join sheet_sale_order_main sm on sm.company_id = opr.company_id and sm.sheet_id = opr.sale_order_sheet_id
left join info_item_prop ip on opr.item_id = ip.item_id and ip.company_id = ~COMPANY_ID
where opr.company_id = ~COMPANY_ID and opm.red_flag is null and opm.approve_time is not null",
                     QueryGroupBySQL = " GROUP BY opr.item_id,ip.item_name,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no,t.s_barcode,t.m_barcode,t.b_barcode,t.s_unit_no,ip.item_no,ip.item_spec",
                     QueryOrderSQL=" order by item_name"
                  }
                }
            };
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
           
//            var costPrice = "sd.cost_price_buy";//当前进价
//            var cost_price_type = DataItems["cost_price_type"].Value;
//            switch (cost_price_type)
//            {
//                case "3"://预设进价
//                    costPrice = "sd.cost_price_buy";
//                    break;
//                case "2"://加权价
//                    costPrice = "sd.cost_price_avg";
//                    break;
//                case "1"://预设成本
//                    costPrice = "sd.cost_price_prop";
//                    break;
//            }            

//            var columns = Grids.GetValueOrDefault("gridItems").Columns;         
//            columns["cost_amount_hasfree"].SqlFld = $"round( SUM(case when coalesce(trade_type,'X')<>'CL' then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end) :: NUMERIC, 2 )";
//            columns["profit_hasfree"].SqlFld = $@"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round( SUM ( case when coalesce(trade_type,'X')<>'CL' then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end ) :: NUMERIC, 2 )";
//            columns["profit_rate_hasfree"].SqlFld = @$"
//cast( 
//    (
//		(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round(sum(case when coalesce(trade_type,'X')<>'CL' then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2) )*100
//	) /
//	(
//		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) <>0
//		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2) 
//		     else null
//		end
//	)

//as numeric)";

//            columns["cost_amount_free_cl"].SqlFld = $"round( SUM (quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ) :: NUMERIC, 2) ";
//            columns["profit_free_cl"].SqlFld = $@"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round( SUM ( quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ) :: NUMERIC, 2 )";
//            columns["profit_rate_free_cl"].SqlFld = @$"
//cast( 
//    (
//		(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2))*100
//	) /
//	(
//		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) <>0 
//		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2) 
//					else null 
//		end
//	) 

//as numeric)";

//            columns["free_cost_amount"].SqlFld = $"round(sum((case when sub_amount=0  then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2)  ";

//            columns["cost_amount"].SqlFld = $"round( SUM ( CASE WHEN sub_amount <> 0  THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 )";
//            columns["profit"].SqlFld = $@"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)
//-round( SUM ( CASE WHEN sub_amount <> 0   THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 )";
//            columns["profit_rate"].SqlFld = @$"
//            cast( 
//                (
//        100*(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round( SUM ( CASE WHEN sub_amount <> 0  THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 ))
//            	) /
//            	(
//            		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) <>0 
//            		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2)
//            					else null 
//            		end
//            	) 

//            as numeric)";

        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            //var costPriceType = "3";
            //var costPriceTypeName = "预设进价";
            //if (JsonCompanySetting.IsValid())
            //{
            //    dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
            //    if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            //}
            //var columns = Grids["gridItems"].Columns;
            //bool seeInPrice = false;
            //if (JsonOperRights.IsValid())
            //{
            //    dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
            //    if (operRights != null && operRights.delicacy != null) seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower()=="true";
            //}
            //if (!seeInPrice)
            //{ 
            //    columns["free_cost_amount"].HideOnLoad = columns["free_cost_amount"].Hidden = true;
            //    columns["cost_amount"].HideOnLoad = columns["cost_amount"].Hidden = true;
            //    columns["profit"].HideOnLoad = columns["profit"].Hidden = true;
            //    columns["profit_rate"].HideOnLoad = columns["profit_rate"].Hidden = true;
            //    columns["cost_amount_hasfree"].HideOnLoad = columns["cost_amount_hasfree"].Hidden = true;
            //    columns["profit_hasfree"].HideOnLoad = columns["profit_hasfree"].Hidden = true;
            //    columns["profit_rate_hasfree"].HideOnLoad = columns["profit_rate_hasfree"].Hidden = true;

            //    columns["cost_amount_free_cl"].HideOnLoad = columns["cost_amount_free_cl"].Hidden = true;
            //    columns["profit_free_cl"].HideOnLoad = columns["profit_free_cl"].Hidden = true;
            //    columns["profit_rate_free_cl"].HideOnLoad = columns["profit_rate_free_cl"].Hidden = true;

            //}

            //if (costPriceType == "1") costPriceTypeName = "预设成本";
            //else if (costPriceType == "2") costPriceTypeName = "加权平均成本";
            //DataItems["cost_price_type"].Value = costPriceType;
            //DataItems["cost_price_type"].Label = costPriceTypeName;
          
            //var sheetType = DataItems["sheetType"].Value;

        }

        public async Task OnGet()
        {
            await InitGet(cmd);
        }

    }



    [Route("api/[controller]/[action]")]
    public class AssignSummaryByItemController : QueryController
    { 
        public AssignSummaryByItemController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            AssignSummaryByItemModel model = new AssignSummaryByItemModel(cmd);
            
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpPost]
        public async Task<object> GetQueryRecords([FromBody] dynamic data)
        {
           // string cost_price_type_name = data.cost_price_type_name;
            //string sheetType = data.sheetType;

            //var main_table = "sheet_sale_main";
            //var detail_table = "sheet_sale_detail";
            //if (sheetType == "xd")
            //{
            //    main_table = "sheet_sale_order_main";
            //    detail_table = "sheet_sale_order_detail";
            //}

            AssignSummaryByItemModel model = new AssignSummaryByItemModel(cmd);
            //var sql = model.Grids["gridItems"].QueryFromSQL;
            //sql = sql.Replace("~mainTable", main_table);
            //sql = sql.Replace("~detailTable", detail_table);
            //model.Grids["gridItems"].QueryFromSQL = sql;
            object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            string sParams = Request.Form["params"];
            sParams = System.Web.HttpUtility.UrlDecode(sParams);
            dynamic queryParams = JsonConvert.DeserializeObject(sParams);
            //string sheetType = queryParams.sheetType;

            //var main_table = "sheet_sale_main";
            //var detail_table = "sheet_sale_detail";
            //if (sheetType == "xd")
            //{
            //    main_table = "sheet_sale_order_main";
            //    detail_table = "sheet_sale_order_detail";
            //}

            AssignSummaryByItemModel model = new AssignSummaryByItemModel(cmd);
            //var sql = model.Grids["gridItems"].QueryFromSQL;
            //sql = sql.Replace("~mainTable", main_table);
            //sql = sql.Replace("~detailTable", detail_table);
            //model.Grids["gridItems"].QueryFromSQL = sql;
            return await model.ExportExcel(Request, cmd, queryParams);
        }

    }
}
