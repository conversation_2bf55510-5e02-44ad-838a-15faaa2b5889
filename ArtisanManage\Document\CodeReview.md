
## 2024-9-6
小米: 
调拨单可用库存展示好像没做好
 {"avail_b_qty",new DataItem(){Title="可用大库存",CellsAlign="right",Hidden=true,ShowSum=true,Width="150",HideOnLoad = true } },
                {"avail_m_qty",new DataItem(){Title="可用中库存",CellsAlign="right",Hidden=true,ShowSum=true,Width="150",HideOnLoad = true} },
                {"avail_s_qty",new DataItem(){Title="可用小库存",CellsAlign="right",Hidden=true,ShowSum=true,Width="150",HideOnLoad = true} }
 这些放在dataItems里没有意义


## 2024-9-5
小米:  0218919a2b5dcba8138c2eae9b773b9a584bcd10
movesheet.js
441: gridRow.from_branch_qty_unit = item.from_stock_qty_unit
 gridRow.to_branch_qty_unit = item.to_stock_qty_unit
可能是
from_branch_available_qty？

salesheet.cshtml.cs 
订单红冲校验，应该放在sheetsale.cs


## 小娄

```c#

sheetBase:

if (sheet_id != "")
            {
                string sheetIDField = this.GetSheetIDField();
                sql = $"select approve_time from {MainTable} where {sheetIDField}={sheet_id} and company_id = {company_id}";
                if(this.SheetType?.ToLower() == "xd" || this.SheetType?.ToLower() == "td")
                {
                    sql = $"select is_del from {MainTable} where {sheetIDField}={sheet_id} and company_id = {company_id}";
                }
                cmd.CommandText = sql;
                object ov = await cmd.ExecuteScalarAsync();
                if (ov == null)
                {
                    sError = "该单已删除, 不能保存"; goto END;
                } else if(ov.ToString().ToLower() == "true")
                {
                    sError = "该单已删除, 不能保存"; goto END;
                }
                   else if (ov != DBNull.Value)
                {
                    sError = "该单已审核,不能保存"; goto END;
                }
            }


```


