﻿@page
@model ArtisanManage.Pages.Report.ItemsOccupyReportModel

@{
  Layout = null;
}

<!DOCTYPE html>
<html style="width: 100%;height: 100%;overflow: hidden">
<head>
  <title>拜访规范</title>
  <script>var commonVersion=@Model.Version</script>
  <script src="~/js/commonConfig.js?v=@Model.Version"></script>
  <script src="~/js/Vue.js"></script>
   <script src="~/js/jquery-1.7.1.min.js"></script>
  <style>
    iframe{
      width:100%;
      height:100%;
      margin-left:0;
      margin-right:0;
      border: 0;
      background-color: transparent;
    }
  </style>
</head>
<body style="width: 100%;height: 100%">
<div id="pages"  style="width: 100%;height: 100%">
  <iframe name="iframe" :src="iframeSrc"></iframe>
</div>
<script>
    var g_operKey = '@Model.OperKey';
</script>
<script>
  const app = new Vue({
    el: "#pages",
     data() {
        return {
          iframeSrc : pageRouter.itemOccupyReport.router + g_operKey+"&&type=brand"
        }
      },
      mounted() {
          console.log(this.iframeSrc)
          window.addEventListener('message', function (msg) {
              console.log(`Report/SalesSummaryByClient?sheetType=xd&&item_id=${msg.data.params.itemId}&&item_name=${msg.data.params.itemName}`)
             window.parent.newTabPage("销售汇总（客户）",`Report/SalesSummaryByClient?sheetType=x&brand_id=${msg.data.params.brandId}&brand_name=${msg.data.params.brandName}&startDay=${msg.data.params.startTime} 00:00&endDay=${msg.data.params.endTime} 23:59`,window);
          })
      }
  })
</script>
</body>
</html>