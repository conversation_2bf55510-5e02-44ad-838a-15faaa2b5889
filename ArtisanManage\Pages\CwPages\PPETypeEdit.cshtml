﻿@page
@model ArtisanManage.Pages.CwPages.PPETypeEditModel
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>PaywayEdit</title>
    <link rel="stylesheet" href="~/css/DataForm.css" type="text/css" />
    <script type="text/javascript" src="~/js/DataForm.js"></script>
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdata.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmyinput.js"></script>

    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxbuttons.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxscrollbar.js"></script>

    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdropdowntree.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxtree.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmenu.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxmygrid.edit.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.selection.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.columnsresize.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.sort.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.aggregates.js"></script>

    <link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=2">
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=2"></script>

    <script type="text/javascript">
        @Html.Raw(Model.m_saveCloseScript)
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
        });
    </script>
</head>
<body>
    <div id="divHead" class="headtail" style="width:500px;"></div>
    <div style="text-align:center;margin-top:20px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;">保存</button>
        <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>