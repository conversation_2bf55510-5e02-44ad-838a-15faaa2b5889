﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using HuaWeiObsController;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{

    /// <summary>
    /// 一、预收款方名称
    /// 二、应收款
    /// 三、预收款
    /// </summary>

    [Route("AppApi/[controller]/[action]")]
    public class AppSheetGetArrearsController : QueryController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public AppSheetGetArrearsController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// 打开收款单
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID, string sheetType)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SHEET_GET_ARREARS sheetGetArrears = "SK".Equals(sheetType) ? SHEET_GET_ARREARS.IS_GET : SHEET_GET_ARREARS.NOT_GET;
            SheetGetArrears sheet = new SheetGetArrears(sheetGetArrears, LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            var sql = @$"select sub_id,sub_name,sub_type from cw_subject where company_id = {companyID} and ((sub_type='QT' and coalesce(for_pay,true)) or (sub_type='ZC' and for_pay)) 
            and (sub_id::text IN (
            SELECT 
                json_array_elements_text(avail_pay_ways) AS individual_value 
            FROM 
                info_operator 
            WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE 
             )
           OR
            (   SELECT 
                COUNT(*) 
            FROM 
                info_operator 
            WHERE  company_id = {companyID} and oper_id = {operID} AND restrict_pay_ways = TRUE ) = 0 
             ) 
             order by case when sub_type='QT' then 0 else 1 end, order_index;";
            List<ExpandoObject> payways = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet, payways });
        }
        


        /// <summary>
        /// 提交收款单
        /// </summary>
        /// <param name="sheet">{"operKey":"wcAqiAdqGYG39sTafoxzNuV7gjl0d-zEX5Q5vIEsZ4CJBL8L71cPvCkNmSBpbvSukmnIwUZFvIg~","sheet_id": "","sheet_no": "","sheettype": "SK","happen_time": "2020-11-18 16:58","supcust_id": 1,"sheet_amount": 500,
        /// "paid_amount": 340,"disc_amount": 0,"payway1_id":1,"payway1_amount":20,"payway2_id": 2,"payway2_amount":20,"now_pay_amount": 40,"now_disc_amount": 0,"left_amount": 120,"make_brief":"","getter_id":"",
        /// "SheetRows": [{"mm_sheet_id": 4,"sheet_amount": 250,"paid_amount": 170,"disc_amount": 0,"now_pay_amount": 20,"now_disc_amount": 0,"left_amount": 60},
        /// {"mm_sheet_id": 5,"sheet_amount": 250,"paid_amount": 170,"disc_amount": 0,"now_pay_amount": 20,"now_disc_amount": 0,"left_amount": 60}]}
        /// </param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Save([FromBody] SheetGetArrears sheet)
        {
            var currentTime = DateTime.Now.ToText();
            // sheet.sheet_type = SHEET_TYPE.SHEET_GET_MONEY;
            sheet.Init();
            if (sheet.appendixPhotos != null)
            {
                List<string> appendixBase64s = new List<string>();
                foreach (string appendixPhoto in sheet.appendixPhotos)
                {
                    appendixBase64s.Add(appendixPhoto);
                }
                sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());
            }
            string msg = await sheet.Save(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });
        }
        /// <summary>
        /// 提交收款单
        /// </summary>
        /// <param name="sheet">{"operKey":"wcAqiAdqGYG39sTafoxzNuV7gjl0d-zEX5Q5vIEsZ4CJBL8L71cPvCkNmSBpbvSukmnIwUZFvIg~","sheet_id": "","sheet_no": "","sheettype": "SK","happen_time": "2020-11-18 16:58","supcust_id": 1,"sheet_amount": 500,
        /// "paid_amount": 340,"disc_amount": 0,"payway1_id":1,"payway1_amount":20,"payway2_id": 2,"payway2_amount":20,"now_pay_amount": 40,"now_disc_amount": 0,"left_amount": 120,"make_brief":"","getter_id":"",
        /// "SheetRows": [{"mm_sheet_id": 4,"sheet_amount": 250,"paid_amount": 170,"disc_amount": 0,"now_pay_amount": 20,"now_disc_amount": 0,"left_amount": 60},
        /// {"mm_sheet_id": 5,"sheet_amount": 250,"paid_amount": 170,"disc_amount": 0,"now_pay_amount": 20,"now_disc_amount": 0,"left_amount": 60}]}
        /// </param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] SheetGetArrears sheet)
        {
            var currentTime = DateTime.Now.ToText();
            // sheet.sheet_type = SHEET_TYPE.SHEET_GET_MONEY;
            sheet.Init();
            if (sheet.appendixPhotos != null)
            {
                List<string> appendixBase64s = new List<string>();
                foreach (string appendixPhoto in sheet.appendixPhotos)
                {
                    appendixBase64s.Add(appendixPhoto);
                }
                sheet.appendix_photos = await ProcessAppendixPicsRetDBStr(appendixBase64s, sheet.company_id.ToString());
            }
            sheet._httpClientFactory = this._httpClientFactory;
            string msg = await sheet.SaveAndApprove(cmd);
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });
        }

        /// <summary>
        /// 红冲
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="
        /// sheetID"></param>
        /// <returns></returns>
        
        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string result = "OK"; string msg = null;
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            try
            {
                var currentTime = DateTime.Now.ToText();
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                SheetGetArrears sheet = new SheetGetArrears(SHEET_GET_ARREARS.EMPTY, LOAD_PURPOSE.SHOW);
                sheet._httpClientFactory = this._httpClientFactory;
                msg = await sheet.Red(cmd, companyID, sheetID, operID,"");
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });

            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }
        }

        public async Task<string> ProcessAppendixPicsRetDBStr(List<string> appendix_pictures_base64, string companyID)
        {
            var result = await CommonTool.ProcessAppendixPicsRetDBStr(_httpClientFactory, appendix_pictures_base64, companyID);
            return result;
        }
        /// <summary>
        /// 查询收款单关联的销售单
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="supcustID">1</param>
        /// <param name="sellerID">1-2</param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetSaleSheets(string operKey, string supcustID,string sellerID, string senderID, string sheetID,string startDate, string endDate , string sheetNo)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            try
            {                
                if (supcustID.IsInvalid()) throw new Exception("请先选择客户！");
                var condi = $" ";
                var sellerCondi = "";
                var getterCondi = "";
                var senderCondi = "";
                var falseCondi = "";
                var sheetNoCondi = "";
                if (sellerID != null)
                {
                    sellerCondi = $" and seller_id = {sellerID}";
                    getterCondi = $" and getter_id = {sellerID}";
                }
                if (senderID != null)
                {
                    senderCondi = $" and (senders_id ilike '%{senderID}%')";
                    falseCondi = $" and false";
                }
                if (sheetNo != null)
                {
                    sheetNoCondi = $" where sheet_no = '{sheetNo}'";
                }



                if (supcustID != null)
                {
                    // 找出结算单位下的门店相关销售单
                    var acctSql = $"select string_agg(supcust_id::text,',') supcust_ids from info_supcust where company_id = {companyID} and (acct_cust_id = {supcustID} or supcust_id = {supcustID})";
                    dynamic supcustIDs = await CDbDealer.Get1RecordFromSQLAsync(acctSql, cmd);
                    condi += $" and supcust_id in ({supcustIDs.supcust_ids})";
                }

                if (sheetID != null) condi += $" and sheet_id = {sheetID}";
                string orderCondi = "";
                if (startDate != null && endDate != null)
                {
                    condi += $" and happen_time  >= '{startDate}' and happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";
                    DateTime tm = Convert.ToDateTime(startDate).AddDays(-20);
                    orderCondi = $" and happen_time >= '{CPubVars.GetDateTextNoTime(tm)}' and happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";
                }
                var order_sql = " order by happen_time,sheet_id";
                SQLQueue QQ = new SQLQueue(cmd);
                /*var sql = $@"SELECT sheet_id as mm_sheet_id,sheet_no as mm_sheet_no,happen_time as mm_sheet_time,sheet_type as m_sheet_type,money_inout_flag,total_amount,
                         (case when money_inout_flag*total_amount=-0 then 0 else money_inout_flag*total_amount end) as sheet_amount,paid_amount,disc_amount,
                         (case when (money_inout_flag*total_amount - paid_amount-disc_amount)=-0 then 0 else (money_inout_flag*total_amount - paid_amount-disc_amount) end) left_amount 
                        from (select sheet_id,sheet_no,happen_time,sheet_type,money_inout_flag,total_amount,paid_amount,disc_amount
                                from sheet_sale_main
                               where (abs(money_inout_flag*total_amount-(paid_amount+disc_amount))>=0.01) and company_id = {companyID} and approve_time is not null and red_flag is null {sellerCondi} {condi}
                               UNION
                              select sheet_id,sheet_no,happen_time,sheet_type,money_inout_flag,total_amount,paid_amount,disc_amount
                                from sheet_prepay
                               where (abs(money_inout_flag*total_amount-(paid_amount+disc_amount))>=0.01) and company_id = {companyID} and approve_time is not null and red_flag is null {getterCondi} {condi} 
                           ) t {order_sql} ";
                    sql = $@"SELECT sum(money_inout_flag*paid_amount) totalPaid,sum(money_inout_flag*disc_amount) totalDisc,sum(money_inout_flag*total_amount - money_inout_flag*paid_amount - money_inout_flag*disc_amount) as totalLeft
                       from ({sql}) tt
                      ;";
                 */
                var sql = $@"
SELECT sheet_id as mm_sheet_id,sheet_no as mm_sheet_no,ifs.oper_name mm_seller_name,seller_id,order_sheet_no,happen_time as mm_sheet_time,sheet_type as m_sheet_type,REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(sheet_type,'X','销售'),'T','退货'),'DH','定货会'),'YS','预收'),'ZC','费用支出') as mm_sheet_type_name,
    mm_make_brief,money_inout_flag,total_amount,
    money_inout_flag*total_amount as sheet_amount, money_inout_flag* paid_amount as paid_amount,money_inout_flag*disc_amount as disc_amount,
    money_inout_flag*(total_amount - paid_amount - disc_amount) as left_amount 
FROM
(
    SELECT sheet_id,sheet_no,order_sheet_no,happen_time,sheet_type,make_brief as mm_make_brief,money_inout_flag,total_amount,paid_amount,disc_amount
    FROM sheet_sale_main sm
    LEFT JOIN 
    (
        select sheet_id order_sheet_id,sheet_no order_sheet_no from sheet_sale_order_main where company_id={companyID}  {orderCondi}
    ) om on sm.order_sheet_id=om.order_sheet_id
    
    WHERE abs(total_amount-paid_amount-disc_amount)>=0.01 and sm.company_id = {companyID} and sm.approve_time is not null and sm.red_flag is null {sellerCondi} {senderCondi}{condi}
    
    UNION
    
    SELECT sheet_id,sheet_no,null order_sheet_no,happen_time,sheet_type,make_brief as mm_make_brief,money_inout_flag,total_amount,paid_amount,disc_amount
    FROM sheet_prepay
    WHERE abs(total_amount-paid_amount-disc_amount)>=0.01 and company_id = {companyID} and approve_time is not null and red_flag is null {getterCondi} {falseCondi} {condi} 
    
    UNION 
    
    SELECT sheet_id,sheet_no,null order_sheet_no,happen_time,sheet_type,make_brief as mm_make_brief,money_inout_flag,total_amount,paid_amount,disc_amount
    FROM sheet_fee_out_main
    WHERE abs(total_amount-paid_amount-disc_amount)>=0.01 and company_id = {companyID} and approve_time is not null and red_flag is null  {getterCondi} {falseCondi} {condi} 
) t left join (select oper_id,oper_name,company_id from info_operator where company_id=~COMPANY_ID) ifs on t.seller_id=ifs.oper_id {sheetNoCondi}    {order_sql} ";
                QQ.Enqueue("receiptSheets", sql);
                sql = @$"SELECT c.sub_id,sub_name,sub_type,round(balance::NUMERIC, 2) balance 
                FROM cw_subject c 
                left JOIN prepay_balance pb on c.company_id = pb.company_id AND c.sub_id = pb.sub_id and supcust_id = {supcustID}
                WHERE c.company_id = {companyID} AND is_order IS NOT TRUE AND (sub_type IN( 'QT', 'YS' ) or (sub_type='ZC' and for_pay))
                ORDER BY case sub_type when 'QT' then 0 when 'YS' then 1 else 2 end, order_index;";
                QQ.Enqueue("prepay", sql);
                /* sql = $@"SELECT sum(paid_amount) totalPaid,sum(disc_amount) totalDisc,sum(total_amount - paid_amount - disc_amount) as totalLeft
                        from ({sql}) tt
                       ;";
                 QQ.Enqueue("receiptTotal", sql);*/
                List<ExpandoObject> data = null;
                var prepay = new List<ExpandoObject>();
                // List<ExpandoObject> total = null;
                var dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    var sqlName = QQ.Dequeue();
                    if (sqlName == "receiptSheets")
                    {
                        data = CDbDealer.GetRecordsFromDr(dr, false);
                    }
                    if(sqlName == "prepay")
                    {
                        prepay = CDbDealer.GetRecordsFromDr(dr, false);
                    }
                }
                QQ.Clear();
                string result = "OK";
                string msg = "";
                return Json(new { result, msg, data,prepay });
            }
            catch (Exception e)
            {
                string result = "ERROR";
                string msg = "获取单据失败";
                MyLogger.LogMsg($"In GetSaleSheets,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", companyID);

                return Json(new { result, msg });
            }
            
        }




        [HttpGet]
        public async Task<JsonResult> GetBuySheets(string operKey, string supcustID, string sellerID, string senderID, string sheetID, string startDate, string endDate)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $" ";
            var sellerCondi = "";
            var getterCondi = "";
            var senderCondi = "";
            var falseCondi = "";
            if (sellerID != null)
            {
                sellerCondi = $" and seller_id = {sellerID}";
                getterCondi = $" and getter_id = {sellerID}";
            }
            if (senderID != null)
            {
                senderCondi = $" and (receivers_id ilike '%{senderID}%')";
                falseCondi = $" and false";

            }
            if (supcustID != null) condi += $" and supcust_id = {supcustID}";
            if (sheetID != null) condi += $" and sheet_id = {sheetID}";
            if (startDate != null && endDate != null) condi += $" and happen_time  >= '{startDate}' and happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";
            var order_sql = " order by sheet_id";
            SQLQueue QQ = new SQLQueue(cmd);
            /*var sql = $@"SELECT sheet_id as mm_sheet_id,sheet_no as mm_sheet_no,happen_time as mm_sheet_time,sheet_type as m_sheet_type,
                          (case when (-1)*money_inout_flag*total_amount=-0 then 0 else (-1)*money_inout_flag*total_amount end) as sheet_amount,paid_amount,disc_amount,
                         (case when ((-1)*money_inout_flag*total_amount - paid_amount-disc_amount)=-0 then 0 else ((-1)*money_inout_flag*total_amount -paid_amount-disc_amount) end) left_amount 
                        from (select sheet_id,sheet_no,happen_time,sheet_type,money_inout_flag,total_amount,paid_amount,disc_amount
                                from sheet_buy_main
                               where (abs((-1)*money_inout_flag*total_amount-(paid_amount+disc_amount))>=0.01) and company_id = {companyID} and approve_time is not null and red_flag is null {sellerCondi} {condi}
                               UNION
                              select sheet_id,sheet_no,happen_time,sheet_type,money_inout_flag,total_amount,paid_amount,disc_amount
                                from sheet_prepay
                               where (abs((-1)*money_inout_flag*total_amount-(paid_amount+disc_amount))>=0.01) and company_id = {companyID} and approve_time is not null and red_flag is null {getterCondi} {condi} 
                           ) t  {order_sql};";
            QQ.Enqueue("receiptSheets", sql);
            sql = $@"SELECT sum((-1)*money_inout_flag*paid_amount) totalPaid,sum((-1)*money_inout_flag*disc_amount) totalDisc,sum((-1)*money_inout_flag*total_amount - (-1)*money_inout_flag*paid_amount - (-1)*money_inout_flag*disc_amount) as totalLeft from sheet_buy_main where (money_inout_flag*total_amount<>(money_inout_flag*(paid_amount+disc_amount)))  {condi};";
            QQ.Enqueue("receiptTotal", sql);*/
            var sql = $@"SELECT sheet_id as mm_sheet_id,sheet_no as mm_sheet_no,ifs.oper_name mm_seller_name,seller_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,REPLACE(REPLACE(sheet_type,'CG','采购'),'YF','预付款') as mm_sheet_type_name,mm_make_brief,
                          (-1)*money_inout_flag*total_amount as sheet_amount,(-1)*money_inout_flag * paid_amount as paid_amount,(-1)*money_inout_flag * disc_amount as disc_amount,
                          (-1)*money_inout_flag*(total_amount - paid_amount-disc_amount) as left_amount 
                        from (select sheet_id,sheet_no,happen_time,sheet_type,make_brief as mm_make_brief,money_inout_flag,total_amount,paid_amount,disc_amount
                                from sheet_buy_main 
                               where abs(total_amount-paid_amount-disc_amount)>=0.001 and company_id = {companyID} and approve_time is not null and red_flag is null {sellerCondi}  {senderCondi} {condi}
                               UNION
                              select sheet_id,sheet_no,happen_time,sheet_type,make_brief as mm_make_brief,money_inout_flag,total_amount,paid_amount,disc_amount
                                from sheet_prepay
                               where abs(total_amount-paid_amount-disc_amount)>=0.001 and company_id = {companyID} and approve_time is not null and red_flag is null {getterCondi}  {falseCondi}  {condi} 
                           ) t  left join (select oper_id,oper_name,company_id from info_operator where company_id=~COMPANY_ID) ifs on t.seller_id=ifs.oper_id {order_sql};";
            /*QQ.Enqueue("receiptSheets", sql);
            sql = $@"SELECT sum(paid_amount) totalPaid,sum(disc_amount) totalDisc,sum(total_amount - paid_amount - disc_amount) as totalLeft
                       from ({sql}) tt
                      ;";*/
            QQ.Enqueue("receiptTotal", sql);
            List<ExpandoObject> data = null;




            sql = @$"SELECT c.sub_id,sub_name,sub_type,round(balance::NUMERIC, 2) balance 
                FROM cw_subject c 
                left JOIN prepay_balance pb on c.company_id = pb.company_id AND c.sub_id = pb.sub_id and supcust_id = {supcustID}
                WHERE c.company_id = {companyID} AND is_order IS NOT TRUE AND (sub_type IN( 'QT', 'YF' ))
                ORDER BY case sub_type when 'QT' then 0 when 'YF' then 1 else 2 end, order_index;";
            QQ.Enqueue("prepay", sql);
            var prepay = new List<ExpandoObject>();
           // List<ExpandoObject> total = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "receiptTotal")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if(sqlName == "prepay")
                {
                    prepay = CDbDealer.GetRecordsFromDr(dr, false);
                }
                /*else if (sqlName == "receiptTotal")
                {
                    total = CDbDealer.GetRecordsFromDr(dr, false);
                }*/
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, prepay});
        }

        /// <summary>
        /// 删除未审核单据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetGetArrears sheet = new SheetGetArrears(SHEET_GET_ARREARS.EMPTY, LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            return Json(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }

        [HttpGet]
        public async Task<JsonResult> GetALLsheets(string operKey, string supcustID, string sellerID, string senderID, string sheetID, string startDate, string endDate, string sheetNo, string supcust_flag, string sheetType)
        {
            bool hasShopFlag = false;
            Security.GetInfoFromOperKey(operKey, out string companyID,out string operID);
            if (supcust_flag == null)
                supcust_flag = "C";
            try
            {
                if (supcustID.IsInvalid()) throw new Exception("请先选择客户！");
                var condi = $" ";
                var sellerCondi = "";
                var getterCondi = "";
                var senderCondi = "";
                var falseCondi = "";
                var sheetNoCondi = "";
                var receiverCondi = "";
                int sheetTypeFlag = 1;
                if (sheetType == "FK") {
                    sheetTypeFlag = -1;
                }
                if (sellerID != null)
                {
                    sellerCondi = $" and seller_id = {sellerID}";
                    getterCondi = $" and getter_id = {sellerID}";
                }
                if (senderID != null)
                {
                    senderCondi = $" and (senders_id ilike '%{senderID}%')";
                    receiverCondi = $" and (receivers_id ilike '%{senderID}%')";
                    falseCondi = $" and false";
                }
                if (sheetNo != null)
                {
                    sheetNoCondi = $" where sheet_no = '{sheetNo}'";
                }



                if (supcustID != null)
                {
                    if (supcust_flag!=null && supcust_flag.Contains("C"))
                    {
                        // 找出结算单位下的门店相关销售单
                        var acctSql = $"select string_agg(supcust_id::text,',') supcust_ids from info_supcust where company_id = {companyID} and (acct_cust_id = {supcustID} or supcust_id = {supcustID})";
                        dynamic supcustIDs = await CDbDealer.Get1RecordFromSQLAsync(acctSql, cmd);
                        string[] allSupcust = supcustIDs.supcust_ids.Split(",");
                        if (allSupcust.Count()> 1) hasShopFlag = true;
                        condi += $" and supcust_id in ({supcustIDs.supcust_ids})";
                    }
                    else 
                    {
                        condi = $"and supcust_id = {supcustID}";
                    }
           
                }

                if (sheetID != null) condi += $" and sheet_id = {sheetID}";
                string orderCondi = "";
                if (startDate != null && endDate != null)
                {
                    condi += $" and happen_time  >= '{startDate}' and happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";
                    DateTime tm = Convert.ToDateTime(startDate).AddDays(-20);
                    if(supcust_flag.Contains("C"))
                        orderCondi = $" and happen_time >= '{CPubVars.GetDateTextNoTime(tm)}' and happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";

                }
                var order_sql = " order by happen_time,sheet_id";
                SQLQueue QQ = new SQLQueue(cmd);

                string sqlByClient = $@"SELECT supcust_id,sheet_id,sheet_no,seller_id,order_sheet_no,sm.order_sheet_id,happen_time,sheet_type,make_brief as mm_make_brief,money_inout_flag,total_amount,paid_amount,disc_amount
    FROM sheet_sale_main sm
    LEFT JOIN 
    (
        select sheet_id order_sheet_id,sheet_no order_sheet_no from sheet_sale_order_main where company_id={companyID}  {orderCondi}
    ) om on sm.order_sheet_id=om.order_sheet_id
    WHERE abs((total_amount)::numeric - (paid_amount)::numeric - (disc_amount)::numeric)>=0.01 and sm.company_id = {companyID} and sm.approve_time is not null and sm.red_flag is null {sellerCondi} {senderCondi}{condi}
    UNION 
    SELECT supcust_id,sheet_id,sheet_no,null seller_id,null order_sheet_no,null order_sheet_id,happen_time,sheet_type,make_brief as mm_make_brief,money_inout_flag,total_amount,paid_amount,disc_amount
    FROM sheet_fee_out_main
    WHERE abs((total_amount)::numeric - (paid_amount)::numeric - (disc_amount)::numeric)>=0.01 and company_id = {companyID} and approve_time is not null and red_flag is null  {getterCondi} {falseCondi} {condi} ";

                string sqlBySupplier = $@"select supcust_id, sheet_id,sheet_no,seller_id,order_sheet_no,bm.order_sheet_id,happen_time,sheet_type,make_brief as mm_make_brief,money_inout_flag,total_amount,paid_amount,disc_amount
      from sheet_buy_main bm
      LEFT JOIN 
    (
        select sheet_id order_sheet_id,sheet_no order_sheet_no from sheet_buy_order_main where company_id={companyID}  {orderCondi}
    ) om on bm.order_sheet_id=om.order_sheet_id
      where abs((total_amount)::numeric - (paid_amount)::numeric - (disc_amount)::numeric)>=0.001 and company_id = {companyID} and approve_time is not null and red_flag is null {sellerCondi}  {receiverCondi} {condi}
        UNION 
    SELECT supcust_id,sheet_id,sheet_no,null seller_id,null order_sheet_no,null order_sheet_id,happen_time,sheet_type,make_brief as mm_make_brief,money_inout_flag,total_amount,paid_amount,disc_amount
    FROM sheet_fee_out_main
    WHERE abs((total_amount)::numeric - (paid_amount)::numeric - (disc_amount)::numeric)>=0.01 and company_id = {companyID} and approve_time is not null and red_flag is null  {getterCondi} {falseCondi} {condi} 
                ";
                string combineSQL = "";
                string subCondiByWhere = "";
                string subCondiByOrder = "";
                if (supcust_flag == "C" || supcust_flag == "W")
                {
                    combineSQL = sqlByClient;
                    subCondiByWhere = "(sub_type IN( 'QT', 'YS' ) or (sub_type='ZC' and for_pay))";
                    subCondiByOrder = "YS";
                }
                else if (supcust_flag == "S")
                {
                    combineSQL = sqlBySupplier;
                    subCondiByWhere = "(sub_type IN( 'QT', 'YF' ))";
                    subCondiByOrder = "YF";
                }
                else if (supcust_flag == "CS") 
                {
                    combineSQL = sqlByClient + "UNION " + sqlBySupplier;
                    subCondiByWhere = "(sub_type IN( 'QT', 'YS' ,'YF') or (sub_type='ZC' and for_pay))";
                    subCondiByOrder = "YF";
                }
                
                
                var sql = $@"
SELECT sup_name as shop_name, sheet_id as mm_sheet_id,sheet_no as mm_sheet_no,ifs.oper_name mm_seller_name,seller_id,order_sheet_no,order_sheet_id,happen_time as mm_sheet_time,sheet_type as m_sheet_type,REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(sheet_type,'CT','采购退货'),'X','销售'),'T','退货'),'DH','定货会'),'YS','预收'),'ZC','费用支出'),'CG','采购') , 'YF', '预付'), 'SR', '其他收入') as mm_sheet_type_name,
    mm_make_brief,money_inout_flag,total_amount,
    {sheetTypeFlag}*money_inout_flag*total_amount as sheet_amount, {sheetTypeFlag}*money_inout_flag* paid_amount as paid_amount,{sheetTypeFlag}*money_inout_flag*disc_amount as disc_amount,
    {sheetTypeFlag}*money_inout_flag*(total_amount - paid_amount - disc_amount) as left_amount 
FROM
(
    select a.*,b.sup_name from
            (
                    
                {combineSQL}
                UNION
                SELECT supcust_id,sheet_id,sheet_no,null seller_id,null order_sheet_no,null order_sheet_id,happen_time,sheet_type,make_brief as mm_make_brief,money_inout_flag,total_amount,paid_amount,disc_amount
                FROM sheet_prepay
                WHERE abs((total_amount)::numeric - (paid_amount)::numeric - (disc_amount)::numeric)>=0.01 and company_id = {companyID} and approve_time is not null and red_flag is null and prepay_sub_id <> '-1' {getterCondi} {falseCondi} {condi} 
            ) a LEFT JOIN info_supcust b on a.supcust_id = b.supcust_id and b.company_id={companyID} 
                
) t left join (select oper_id,oper_name,company_id from info_operator where company_id={companyID}) ifs on t.seller_id=ifs.oper_id {sheetNoCondi}    {order_sql} ";
                QQ.Enqueue("receiptSheets", sql);
                sql = @$"SELECT c.sub_id,sub_name,sub_type,round(balance::NUMERIC, 2) balance 
                FROM cw_subject c 
                left JOIN prepay_balance pb on c.company_id = pb.company_id AND c.sub_id = pb.sub_id and supcust_id = {supcustID}
                WHERE c.company_id = {companyID} AND is_order IS NOT TRUE and coalesce(status,'1')!='0' AND {subCondiByWhere} and (c.sub_id::text IN (
                SELECT 
                    json_array_elements_text(avail_pay_ways) AS individual_value 
                FROM 
                    info_operator 
                WHERE  company_id = {companyID} AND oper_id = {operID} AND restrict_pay_ways = TRUE 
                )
                OR
                (   SELECT 
                        COUNT(*) 
                    FROM 
                        info_operator 
                    WHERE  company_id = {companyID} AND oper_id = {operID}  AND restrict_pay_ways = TRUE ) = 0 
                )
                ORDER BY case sub_type when 'QT' then 0 when '{subCondiByOrder}' then 1 else 2 end, order_index;";
                QQ.Enqueue("prepay", sql);

                List<ExpandoObject> data = null;
                var prepay = new List<ExpandoObject>();
           
                var dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    var sqlName = QQ.Dequeue();
                    if (sqlName == "receiptSheets")
                    {
                        data = CDbDealer.GetRecordsFromDr(dr, false);
                    }
                    if (sqlName == "prepay")
                    {
                        prepay = CDbDealer.GetRecordsFromDr(dr, false);
                    }
                }
                QQ.Clear();
                string result = "OK";
                string msg = "";
                return Json(new { result, msg, data, prepay, hasShopFlag });
            }
            catch (Exception e)
            {
                string result = "ERROR";
                string msg = "获取单据失败";
                MyLogger.LogMsg($"In GetSaleSheets,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", companyID);

                return Json(new { result, msg });
            }
        }

        [HttpGet]
        public async Task<JsonResult> GetSheetsByIDs(string operKey, string sheetIDs, string sheetType)
        {
            try
            {
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

                if (string.IsNullOrEmpty(sheetIDs))
                {
                    return Json(new { result = "Error", msg = "销售单ID不能为空" });
                }

                // 根据销售单ID加载对应的欠款数据
                string sql = $@"
                    SELECT
                        sm.sheet_id as mm_sheet_id,
                        sm.sheet_no as mm_sheet_no,
                        COALESCE(ifs.oper_name, '') as mm_seller_name,
                        sm.sheet_type as mm_sheet_type,
                        CASE
                            WHEN sm.sheet_type = 'X' THEN '销售单'
                            WHEN sm.sheet_type = 'T' THEN '退货单'
                            ELSE sm.sheet_type
                        END as mm_sheet_type_name,
                        sm.happen_time as mm_sheet_time,
                        sm.total_amount as sheet_amount,
                        COALESCE(sm.paid_amount, 0) as paid_amount,
                        COALESCE(sm.disc_amount, 0) as disc_amount,
                        (sm.total_amount - COALESCE(sm.paid_amount, 0) - COALESCE(sm.disc_amount, 0)) as left_amount,
                        0 as now_pay_amount,
                        0 as now_disc_amount,
                        sm.make_brief as mm_make_brief,
                        '' as order_sheet_id,
                        '' as order_sheet_no,
                        '' as shop_name
                    FROM sheet_sale_main sm
                    LEFT JOIN (select oper_id,oper_name,company_id from info_operator where company_id={companyID}) ifs on sm.seller_id=ifs.oper_id
                    WHERE sm.company_id = {companyID}
                        AND sm.sheet_id IN ({sheetIDs})
                        AND sm.approve_time IS NOT NULL
                        AND sm.red_flag IS NULL
                        AND (sm.total_amount - COALESCE(sm.paid_amount, 0) - COALESCE(sm.disc_amount, 0)) > 0
                    ORDER BY sm.happen_time DESC";

                var records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

                return Json(new { result = "OK", data = records });
            }
            catch (Exception ex)
            {
                return Json(new { result = "Error", msg = $"加载销售单数据失败: {ex.Message}" });
            }
        }

        [HttpGet]
        public async Task<JsonResult> GetCurrentOperatorInfo(string operKey)
        {
            try
            {
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);

                string sql = $@"
                    SELECT
                        oper_id,
                        oper_name
                    FROM info_operator
                    WHERE company_id = {companyID} AND oper_id = {operID}";

                var operInfo = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

                if (operInfo != null)
                {
                    return Json(new { result = "OK", operInfo = operInfo });
                }
                else
                {
                    return Json(new { result = "Error", msg = "未找到操作人信息" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { result = "Error", msg = $"获取操作人信息失败: {ex.Message}" });
            }
        }


    }
}