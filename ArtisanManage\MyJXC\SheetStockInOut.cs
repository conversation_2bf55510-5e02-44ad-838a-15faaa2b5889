﻿using ArtisanManage.Models;
using ArtisanManage.Pages;
using ArtisanManage.MyCW;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using NPOI.SS.Formula.Functions;
using static NPOI.HSSF.Util.HSSFColor;
using Npgsql;
using _YingJiang.Print;

namespace ArtisanManage.MyJXC
{
    public class SheetRowStockInOut : SheetRowItem
    {
        public SheetRowStockInOut()
        {
           
        }
        [SaveToDB] public string sheet_item_name { get; set; } = "";
        [SaveToDB][FromFld] public string cost_price_avg { get; set; }
        [SaveToDB][FromFld] public string cost_price_prop { get; set; }
        [SaveToDB][FromFld] public string cost_price_buy { get; set; }
        [SaveToDB][FromFld] public string wholesale_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_buy_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_spec { get; set; }
        public string cost_price
        {
            get
            {
                string s = "";
                if (costPriceType == "2")
                {
                    if (cost_price_avg.IsValid())
                    {
                        s = CPubVars.FormatMoney(CPubVars.ToDecimal(cost_price_avg));
                    }
                }
                else if (cost_price_buy.IsValid())
                {
                    s = CPubVars.FormatMoney(CPubVars.ToDecimal(cost_price_buy));
                }
                return s;
            }
        }
        public string cost_amount
        {
            get
            {
                string s = "";
                if (cost_price.IsValid())
                {
                    s = CPubVars.FormatMoney(CPubVars.ToDecimal(cost_price) * quantity);
                }
                return s;
            }
        }

        public string cost_amount_avg
        {
            get
            {
                string s = "";
                if (cost_price_avg.IsValid())
                {
                    s = CPubVars.FormatMoney(CPubVars.ToDecimal(cost_price_avg) * unit_factor * quantity);
                }
                return s;
            }
        }
        public string wholesale_amount
        {
            get
            {
                string s = "";
                if (wholesale_price.IsValid()) s = CPubVars.FormatMoney(CPubVars.ToDecimal(wholesale_price) * quantity);
                return s;
            }
        }
        public string buy_amount
        {
            get
            {
                string s = "";
                if (cost_price_buy.IsValid()) s = CPubVars.FormatMoney(CPubVars.ToDecimal(cost_price_buy) * quantity);
                return s;
            }
        }

        public string costPriceType = "";
    }
    public class SheetStockInOut : SheetBase<SheetRowStockInOut>
    {
        [SaveToDB][FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB][FromFld] public string branch_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string branch_name { get; set; } = "";
        [SaveToDB][FromFld] public string seller_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";
        [SaveToDB][FromFld] public override int money_inout_flag { get; set; }
        [SaveToDB][FromFld] public string wholesale_amount { get; set; } = "";
        [SaveToDB][FromFld] public string cost_amount_avg { get; set; } = "";
        [SaveToDB][FromFld] public string buy_amount { get; set; } = "";
        public new string sum_quantity_unit_conv
        {
            get
            {
                decimal b_sum_quantity = 0, m_sum_quantity = 0, s_sum_quantity = 0;
                foreach (var row in SheetRows)
                {
                    decimal qty = row.unit_factor * row.quantity;
                    decimal bf = 0, mf = 0;
                    if (row.b_unit_factor.IsValid()) bf = CPubVars.ToDecimal(row.b_unit_factor);
                    if (row.m_unit_factor.IsValid()) mf = CPubVars.ToDecimal(row.m_unit_factor);
                    string s = SheetRowInventory.GetQtyUnit(qty, row.b_unit_no, bf, row.m_unit_no, mf, row.unit_no, ref b_sum_quantity, ref m_sum_quantity, ref s_sum_quantity);
                }
                string sum = "";
                if (b_sum_quantity != 0) sum += CPubVars.FormatMoney(b_sum_quantity, 3) + "大";
                if (m_sum_quantity != 0) sum += CPubVars.FormatMoney(m_sum_quantity, 3) + "中";
                if (s_sum_quantity != 0) sum += CPubVars.FormatMoney(s_sum_quantity, 3) + "小";
                return sum;
            }
        }
        public enum IS_REDUCE
        {
            EMPTY,
            IN,
            OUT,
        }
    
        public class CInfoForApprove : CInfoForApproveBase
        {
            public string ArrearBalance = "", PrepayBalance = "";
            public List<SheetRowStockInOut> SheetRows = null;
            public bool RoleAllowNegativeStock = true;
            public bool BranchAllowNegativeStock = true;
            public string NegativeStockAccordance = "real";
        }

        public SheetStockInOut(IS_REDUCE reduce, LOAD_PURPOSE loadPurpose) : base("sheet_stock_in_out_main", "sheet_stock_in_out_detail", loadPurpose)
        {
            sheet_type = reduce == IS_REDUCE.IN ? SHEET_TYPE.SHEET_STOCK_IN : SHEET_TYPE.SHEET_STOCK_OUT;
            if (loadPurpose == LOAD_PURPOSE.SHOW)
            {
                ConstructFun();
            }
            else if (loadPurpose == LOAD_PURPOSE.APPROVE)
                DetailLeftJoin = $" left join stock on t.item_id=stock.item_id and t.company_id=stock.company_id and COALESCE(t.branch_id,m.branch_id)=stock.branch_id and COALESCE(t.branch_position,0) = stock.branch_position and COALESCE(t.batch_id,0) = stock.batch_id ";
        }
        public SheetStockInOut(LOAD_PURPOSE loadPurpose) : base("sheet_stock_in_out_main", "sheet_stock_in_out_detail", loadPurpose)
        {
            ConstructFun();
        }
        public SheetStockInOut() : base("sheet_stock_in_out_main", "sheet_stock_in_out_detail", LOAD_PURPOSE.SHOW)
        {
            ConstructFun();
        }
        private void ConstructFun()
        {
            MainLeftJoin = @" left join info_branch b on t.branch_id=b.branch_id and b.company_id = ~COMPANY_ID                                 
                                  left join (select oper_id,oper_name as seller_name from info_operator where company_id = ~COMPANY_ID) seller on t.seller_id=seller.oper_id
                                  left join (select oper_id,oper_name as maker_name from info_operator where company_id = ~COMPANY_ID) maker on t.maker_id=maker.oper_id
                                  left join  (select oper_id,oper_name as approver_name from info_operator where company_id = ~COMPANY_ID) approver on t.approver_id=approver.oper_id
                ";
            DetailLeftJoin = @"
left join sheet_stock_in_out_main m on m.sheet_id = t.sheet_id and m.company_id = t.company_id
left join (select batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date from info_item_batch where company_id= ~COMPANY_ID) itb on itb.batch_id = t.batch_id
left join info_item_prop ip on t.company_id=ip.company_id and t.item_id=ip.item_id
left join info_item_brand ib on ip.company_id=ib.company_id and ip.item_brand=ib.brand_id
left join (select class_id classId,class_name,order_index as class_order_index from info_item_class where company_id =~COMPANY_ID) ic on ip.item_class=ic.classId 
LEFT JOIN
( 
    select item_id,
                                          s->>'f2' as s_unit_no, s->>'f3' as s_buy_price, s->>'f4' as s_wholesale_price,s->>'f5' s_barcode,
    (m->>'f1')::numeric as m_unit_factor, m->>'f2' as m_unit_no, m->>'f3' as m_buy_price, m->>'f4' as m_wholesale_price,m->>'f5' m_barcode,
    (b->>'f1')::numeric as b_unit_factor, b->>'f2' as b_unit_no, b->>'f3' as b_buy_price, b->>'f4' as b_wholesale_price,b->>'f5' b_barcode
 
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,buy_price,wholesale_price,barcode)) as json from info_item_multi_unit where company_id=~COMPANY_ID ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$)  as errr(item_id int, s jsonb,m jsonb, b jsonb) 
) mu on ip.item_id = mu.item_id
  left join info_branch ibb on ibb.branch_id = t.branch_id
  left join info_branch_position ibp on ibp.branch_id =COALESCE(t.branch_id,m.branch_id) and ibp.branch_position = COALESCE(t.branch_position,0)

";
        }
        public async Task<string> CheckBatch(CMySbCommand cmd ,bool bAutoCommit = true)
        {
            string msg = "";
            string insertSql = "";
            string insertValue = "";
            string selectSql = "";
            string selectValue = "";
            Dictionary<string, dynamic> batchDic = new Dictionary<string, dynamic>();
            Dictionary<string, string> sheetRowBatch = new Dictionary<string, string>();
            try
            {
                 
                foreach (SheetRowStockInOut row in SheetRows)
                { 
                    if (row.produce_date.IsInvalid() || row.produce_date == "无产期")
                    {
                        continue;
                    }
                    string key = row.produce_date + row.batch_no;
                    if (!batchDic.ContainsKey(key))
                    {
                        batchDic[key] = new { produce_date = row.produce_date, batch_no = row.batch_no };
                        if(selectValue!="")  selectValue += ",";
                        selectValue += $@"('{row.produce_date}','{row.batch_no}')";
                    }
                }
                if (selectValue != "") selectSql = $@"select * from info_item_batch where company_id = {company_id} and (substring(produce_date::text,1,10),batch_no) in ({selectValue});";
                if (selectSql != "")
                {
                    List<ExpandoObject> selectRec = await CDbDealer.GetRecordsFromSQLAsync(selectSql, cmd);
                    foreach (dynamic row in selectRec)
                    {
                        string produceDate = row.produce_date;
                        string batchNo = row.batch_no;
                        produceDate = produceDate.Substring(0, 10);
                        string key = produceDate + batchNo;
                        sheetRowBatch.Add(key, row.batch_id);
                        if (batchDic.ContainsKey(key)) batchDic.Remove(key);
                    }
                    foreach(KeyValuePair<string,dynamic> kv in batchDic)
                    {
                        string produceDate = kv.Value.produce_date;
                        string batchNo = kv.Value.batch_no;
                        if(insertValue!="")  insertValue += ",";
                        insertValue += $@"({company_id},'{produceDate}','{batchNo}')";
                    }
                    if (insertValue!="")//INSERT INTO
                    {
                        insertSql += $"insert into info_item_batch (company_id,produce_date,batch_no) values {insertValue} on CONFLICT(company_id,produce_date,batch_no) DO NOTHING RETURNING batch_id,produce_date,batch_no;";
                        List<ExpandoObject> insertRec = await CDbDealer.GetRecordsFromSQLAsync(insertSql, cmd);
                        foreach (dynamic row in insertRec)
                        {
                            string produceDate = row.produce_date;
                            string batchNo = row.batch_no;
                            produceDate = produceDate.Substring(0, 10);
                            string key = produceDate + batchNo;
                            sheetRowBatch.Add(key, row.batch_id);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                msg = "生产日期/批次错误";
                MyLogger.LogMsg(msg + e.Message + e.StackTrace + "SQL:" + cmd.CommandText, company_id, "produce_date");
            }
            if (msg == "")
            {
                foreach (SheetRowStockInOut row in SheetRows)
                {
                    if (row.produce_date.IsInvalid() || row.produce_date == "无产期")
                    {
                        row.batch_id = "0";
                        continue;
                    }
                    string key = row.produce_date + row.batch_no;
                    if (sheetRowBatch.ContainsKey(key))
                    {
                        row.batch_id = sheetRowBatch[key];
                    }
                    else
                    {
                        msg = "生产日期/批次错误";
                    }
                }
            }
            return msg;
        }
	 
     
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            var check = await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
            if (branch_id == "") return "请指定仓库";
            if (SheetRows.Count == 0) return "请指定至少一行商品";
            return "OK";
        }
        public override string GetSheetCharactor()
        {
            string res = this.company_id + "_" + this.OperID + "_" + this.branch_id + "_" + this.make_brief;
            foreach (var row in SheetRows)
            {
                res += row.item_id + "_" + row.quantity;
            }
            return res;
        }
        public override string GetDataLockKey()
        {
            return this.company_id + "_" + this.branch_id;
        }
        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";

            foreach (SheetRowStockInOut row in info.SheetRows)
            {
                string s = "";
                string changeQty = "";

                decimal qty, amount, cost_price_avg;
                qty = row.inout_flag * row.quantity * row.unit_factor;
                changeQty = qty.ToString();
                if (row.cost_price_avg.IsValid())
                {
                    if (CPubVars.ToDecimal(row.cost_price_avg) > 0)
                    {
                        cost_price_avg = CPubVars.ToDecimal(row.cost_price_avg);
                        amount = qty * cost_price_avg;
                       // sql += $"update info_item_prop set cost_price_avg = {cost_price_avg} where company_id = {company_id} and item_id = {row.item_id} and coalesce(cost_price_avg,0)=0;";
                    }
                }


                if (qty >= 0)
                {
                    changeQty = "+" + qty.ToString();
                }

                if (changeQty == "+-0") changeQty = "+0";

                if (string.IsNullOrEmpty(row.batch_id)) row.batch_id = "0";

                if (row.HasStockQty)
                {
                    s = $"update stock set stock_qty=stock_qty{changeQty} where company_id={company_id} and branch_id={branch_id} and item_id={row.item_id} and batch_id = {row.batch_id} and branch_position = {row.branch_position};";
                }
                else
                {
                    s = $"insert into stock(company_id,branch_id,item_id,stock_qty,batch_id,branch_position) values ({company_id},{branch_id},{row.item_id},{qty},{row.batch_id},{row.branch_position}) on conflict (company_id,branch_id,item_id,batch_id,branch_position) do update set stock_qty=stock.stock_qty{changeQty};";
                }
                sql += s;
                row.NewStockQty = row.StockQty + qty;
            }
            return sql;
        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            string sql = "";
            foreach (var row in MergedSheetRowByBatchAndItem)
            {
                string batchId = row.batch_id == null ? "0" : row.batch_id;
                string s = $@"insert into stock_change_log 
        (company_id,  approve_time,   branch_id,    item_id,    sheet_id,  pre_stock_qty, new_stock_qty,batch_id,branch_position ) 
values ({company_id},'{approve_time}',{branch_id},{row.item_id},{sheet_id},{row.StockQty},{row.NewStockQty},{batchId},{row.branch_position});";
                sql += s;
            }

            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql;
            base.GetInfoForApprove_SetQQ(QQ);
            if (OperID != "")
            {
                sql = $"select rights->'delicacy'->'allowNegativeStock'->'value' role_allow_negative_stock from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
                QQ.Enqueue("role_allow_negative_stock", sql);
            }

            if (branch_id != "")
            {
                sql = $"select allow_negative_stock branch_allow_negative_stock,negative_stock_accordance from info_branch where company_id = {company_id} and branch_id = {branch_id}";
                QQ.Enqueue("branch_allow_negative_stock", sql);

            }
            if (SheetRows.Count > 0)
            {
                string items_id = "";
                string batchs_id = "";
                string branchs_position = "";
                foreach (SheetRowStockInOut row in SheetRows)
                {
                    if (items_id != "") items_id += ",";
                    items_id += row.item_id;
                    if (batchs_id != "") batchs_id += ",";
                    batchs_id += row.batch_id;
                    if (branchs_position != "") branchs_position += ",";
                    branchs_position += row.branch_position;
                }
                if(batchs_id == "")
                {
                    batchs_id = "0";
                }
                //string.Join(',', SheetRows.Select(row => {return row.item_id; }));
                sql = $"select branch_id,item_id,stock_qty,batch_id,branch_position from stock where company_id={company_id} and branch_id={branch_id} and branch_position in ({branchs_position}) and item_id in ({items_id}) and batch_id in ({batchs_id})";
                QQ.Enqueue("stock", sql);
                sql = @$"
select ip.item_id,cost_price_avg,mu.buy_price s_buy_price from info_item_prop ip
left join info_item_multi_unit mu on ip.item_id=mu.item_id and ip.company_id=mu.company_id and mu.unit_factor=1
where ip.company_id={company_id} and ip.item_id in ({items_id})";
                QQ.Enqueue("avg", sql);
            }
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (sqlName == "role_allow_negative_stock")
            {
                dynamic roleAllowNegativeStock = CDbDealer.Get1RecordFromDr(dr);
                if (roleAllowNegativeStock != null)
                {
                    string r = roleAllowNegativeStock.role_allow_negative_stock;
                    if (r.ToLower() == "false") info.RoleAllowNegativeStock = false;
                }
            }
            else if (sqlName == "branch_allow_negative_stock")
            {
                dynamic branchInfo = CDbDealer.Get1RecordFromDr(dr);
                if (branchInfo != null)
                {
                    string b = branchInfo.branch_allow_negative_stock;
                    if (b.ToLower() == "false") info.BranchAllowNegativeStock = false;
                    info.NegativeStockAccordance = branchInfo.negative_stock_accordance;
                }
            }
            else if (sqlName == "stock")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowStockInOut row in SheetRows)
                    {
                        if (rec.item_id != "" && row.item_id == rec.item_id && row.batch_id == rec.batch_id && row.branch_position == rec.branch_position)
                        {
                            if (rec.stock_qty != "")
                            {
                                row.HasStockQty = true;
                                row.StockQty = CPubVars.ToDecimal(rec.stock_qty);
                            }
                        }
                    }
                }
                info.SheetRows = SheetRows;
                this.MergedSheetRowByBatchAndItem = MergeSheetRowsByBatchAndItem(info.SheetRows);
                if (!info.RoleAllowNegativeStock || !info.BranchAllowNegativeStock)
                {
                    int rowIndex = 1;
                    foreach (var row in this.MergedSheetRowByBatchAndItem)
                    {
                        decimal newStockQty = newStockQty = row.StockQty + row.quantity * row.inout_flag;
                        if (newStockQty < -0.01m)
                        {
                            info.ErrMsg = $"{row.item_name}出现负库存，审核失败";
                            if (red_flag == "2") info.ErrMsg = $"{row.item_name}出现负库存，红冲失败";
                            break;
                        }
                        rowIndex++;

                    }
                }
            }
        }
        protected List<SheetRowStockInOut> MergeSheetRowsByBatchAndItem(List<SheetRowStockInOut> rows)
        {
            List<SheetRowStockInOut> rowsDict = new List<SheetRowStockInOut>();

            foreach (SheetRowStockInOut sheetRow in rows)
            {
                SheetRowStockInOut curRow = null;
                foreach (SheetRowStockInOut rowDic in rowsDict)
                {
                    if (sheetRow.item_id == rowDic.item_id && sheetRow.batch_id == rowDic.batch_id && sheetRow.branch_position == rowDic.branch_position)
                    {
                        curRow = rowDic;
                        break;
                    }
                }
                if (curRow == null)
                {
                    string s = JsonConvert.SerializeObject(sheetRow);
                    curRow = JsonConvert.DeserializeObject<SheetRowStockInOut>(s);
                    curRow.item_id = sheetRow.item_id;
                    curRow.item_name = sheetRow.item_name;
                    curRow.sheet_item_name = sheetRow.sheet_item_name;
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_no = sheetRow.unit_no;
                    curRow.unit_factor = sheetRow.quantity;
                    curRow.inout_flag = sheetRow.inout_flag;
                    curRow.batch_id = sheetRow.batch_id;
                    curRow.branch_position = sheetRow.branch_position;
                    curRow.HasStockQty = sheetRow.HasStockQty;
                    curRow.StockQty = sheetRow.StockQty;
                    rowsDict.Add(curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                }
            }
            return rowsDict;

        }
        protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            foreach (dynamic row in this.SheetRows)
            {
                string branchID = row.branch_id;
                string branchName = row.branch_name;
                if (branchID.IsInvalid())
                {
                    branchID = branch_id;
                    branchName = branch_name;
                }
                if (row.branch_position == "0" || string.IsNullOrWhiteSpace(row.branch_position)) continue;
                dynamic record = await CDbDealer.Get1RecordFromSQLAsync($"select branch_position from info_branch_position where company_id = {company_id} and branch_position = {row.branch_position} and branch_id = {branchID};", cmd);
                if (record == null)
                {
                    return $"{branchName}不存在库位：{row.branch_position_name}";
                }
            }

            string msg=await CheckBatch(cmd);
            if (msg != "") return msg;
            return "OK";

        }
    }
}
