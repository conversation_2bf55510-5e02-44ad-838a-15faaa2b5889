﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class InventorySummaryModel : PageQueryModel
    { 
        public InventorySummaryModel(CMySbCommand cmd) : base(Services.MenuId.inventorySummary)
        {
            this.UsePostMethod = true;
            this.cmd = cmd;
            this.PageTitle = "盘点汇总";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sd.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sd.happen_time",   CompareOperator="<=",Value = CPubVars.GetDateText(DateTime.Now.Date) + " 23:59",
                    JSDealItemOnSelect =@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"sheet_type",new DataItem(){FldArea="divHead",Title="单据类型",LabelFld="sheet_type_name",ButtonUsage="list",Source = "[{v:'YK',l:'盈亏'},{v:'BS',l:'报损'},{v:'',l:'所有'}]",CompareOperator="="}},


				{"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){SqlFld="ip.item_brand"})},

				{"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",SqlFld="sd.item_id",DropDownWidth="300",QueryByLabelLikeIfIdEmpty=true,
                   SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
                {"branch_id",new DataItem(){Title="仓库",FldArea="divHead",LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.branch_id",
                    SqlForOptions ="select branch_id as v,branch_name as l from info_branch"}},
                {"seller_id",new DataItem(){Title="业务员", FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="seller_id",
                    SqlForOptions ="select oper_id as v,oper_name as l from info_operator where company_id=~COMPANY_ID and is_seller and (status=1 or status is null)"}},
                {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'3',l:'预设进价'},{v:'2',l:'加权平均价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价'}]", CompareOperator="=" }},
                {"byHappenTime",new DataItem(){FldArea="divHead",Title="按交易时间查询",CtrlType="jqxCheckBox",ForQuery=false}}

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates = true,
                      Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"item_id",new DataItem(){SqlFld = "sd.item_id",HideOnLoad =true,Hidden=true}},
                       {"item_name",new DataItem(){Title="商品名称",Linkable=true,Pinned=true}},

                       {"quantity",     new DataItem(){Title="数量", Sortable=true,    Width="50",
                         SqlFld="unit_from_s_to_bms ((sum( quantity*sd.unit_factor*inout_flag)::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)", }},

                       {"wholesale_amount",   new DataItem(){Title="批发金额",   Width="160" ,ShowSum=true,
                           SqlFld=" round( SUM (  sd.wholesale_price * quantity * sd.unit_factor*inout_flag )  :: NUMERIC, 2 )", }},
                       {"cost_amount",   new DataItem(){Title="成本金额",   Width="160" , ShowSum=true,
                           SqlFld="	round(SUM ( sd.cost_price_avg * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2)",  }},
                       {"buy_amount",   new DataItem(){Title="进价金额",   Width="160"  , ShowSum=true,
                           SqlFld="	round(  SUM  ( sd.cost_price_buy * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )",}},
                       {"current_amount", new DataItem(){ Title="当前金额",Width="160", ShowSum=true,
                            SqlFld="round(  sum( itu2.buy_price * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )"} },//盘点盈亏明细的数量是小数量
                     },
                     QueryFromSQL=@"
                      FROM sheet_invent_change_detail sd
left join sheet_invent_change_main sm on sm.sheet_id=sd.sheet_id and sm.company_id=~COMPANY_ID
left join (select *, cost_price_recent->>'avg1' as recent_price1, cost_price_recent->>'avg2' as recent_price2, cost_price_recent->>'avg3' as recent_price3 from info_item_prop where company_id=~COMPANY_ID) ip on ip.item_id=sd.item_id 
left join 
(
    select item_id,(b->>'f1')::numeric as b_unit_factor,(m->>'f1')::numeric as m_unit_factor,(s->>'f1')::numeric as s_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
    as errr(item_id int, s jsonb,m jsonb,b jsonb) 
) t on sd.item_id=t.item_id 
LEFT JOIN (select item_id, buy_price from info_item_multi_unit where company_id = ~COMPANY_ID and unit_type='s') itu2 on  itu2.item_id = sd.item_id
where sd.company_id=~COMPANY_ID
",
                     QueryGroupBySQL = " GROUP BY  sd.item_id,item_name,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no",

                     QueryOrderSQL=" "
                  }
                }
            };
        }

        public async Task OnGet()
        {
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                var columns = await Grids["gridItems"].GetAllColumns();
                columns["buy_amount"].HideOnLoad = columns["buy_amount"].Hidden = true;
                columns["cost_amount"].HideOnLoad = columns["cost_amount"].Hidden = true;
            }

            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select setting->>'costPriceType' as costpricetype, coalesce(setting->>'recentPriceTime','3') as recentpricetime from company_setting where company_id = {company_id}", cmd);//初始化时从设置取
            string costPriceType = "3";
            string recentPriceTime = "3";
            if (setting != null && setting.costpricetype != "")
            {
                costPriceType = setting.costpricetype;
                if (setting.costpricetype == "4")
                {
                    recentPriceTime = setting.recentpricetime;
                }
                DataItems["cost_price_type"].Value = setting.costpricetype;
            }
            switch (costPriceType)
            {
                case "3"://预设进价
                    DataItems["cost_price_type"].Label = "预设进价";
                    Grids["gridItems"].Columns["current_amount"].SqlFld = "round(  sum( itu2.buy_price * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )";
                    break;
                case "2"://加权价
                    DataItems["cost_price_type"].Label = "加权平均价";
                    Grids["gridItems"].Columns["current_amount"].SqlFld = "round(  sum( ip.cost_price_avg * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )";
                    break;
                case "1"://预设成本
                    DataItems["cost_price_type"].Label = "预设成本";
                    Grids["gridItems"].Columns["current_amount"].SqlFld = "round(  sum( ip.cost_price_spec * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )";
                    break;
                case "4"://最近平均进价
                    DataItems["cost_price_type"].Label = "最近平均进价";
                    Grids["gridItems"].Columns["current_amount"].SqlFld = $"round(  sum( ip.recent_price{recentPriceTime} * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )";
                    break;
            }
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select setting->>'costPriceType' as costpricetype, coalesce(setting->>'recentPriceTime','3') as recentpricetime from company_setting where company_id = {company_id}", cmd);
            string costPriceType = DataItems["cost_price_type"].Value;
            string recentPriceTime = "3";
            if (costPriceType == "4" && setting!=null)
            {
                recentPriceTime = setting.recentpricetime;
            }
            switch (costPriceType)
            {
                case "3"://预设进价
                    DataItems["cost_price_type"].Label = "预设进价";
                    Grids["gridItems"].Columns["current_amount"].SqlFld = "round(  sum( itu2.buy_price * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )";
                    break;
                case "2"://加权价
                    DataItems["cost_price_type"].Label = "加权平均价";
                    Grids["gridItems"].Columns["current_amount"].SqlFld = "round(  sum( ip.cost_price_avg * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )";
                    break;
                case "1"://预设成本
                    DataItems["cost_price_type"].Label = "预设成本";
                    Grids["gridItems"].Columns["current_amount"].SqlFld = "round(  sum( ip.cost_price_spec * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )";
                    break;
                case "4"://最近平均进价
                    DataItems["cost_price_type"].Label = "最近平均进价";
                    Grids["gridItems"].Columns["current_amount"].SqlFld = $"round(  sum( ip.recent_price{recentPriceTime}::numeric * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )";
                    break;
            }
        }
    }



    [Route("api/[controller]/[action]")]
    public class InventorySummaryController : QueryController
    { 
        public InventorySummaryController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            InventorySummaryModel model = new InventorySummaryModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpPost]
        public async Task<object> GetQueryRecords([FromBody] dynamic data)
        {
            InventorySummaryModel model = new InventorySummaryModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
            return records;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {

            InventorySummaryModel model = new InventorySummaryModel(cmd);
            
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {

            InventorySummaryModel model = new InventorySummaryModel(cmd);
            
            return await model.ExportExcel(Request, cmd);
        }
    }
}
