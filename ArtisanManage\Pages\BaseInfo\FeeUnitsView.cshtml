﻿@page
@model ArtisanManage.Pages.BaseInfo.FeeUnitsViewModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <!--
                <script  type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=@Html.Raw(Model.BaiduKey)"></script>

    -->


    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.ForSelect = @Html.Raw(Model.ForSelect.ToString().ToLower());
        window.MultiSelect = @Html.Raw(Model.MultiSelect.ToString().ToLower());
        window.WithClients = @Html.Raw(Model.WithClients.ToString().ToLower());
        window.ObjOperRights = @Html.Raw(Model.ObjOperRights);
        var g_loadBaiduMap = false
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {  //监听message事件
            debugger
            if (rs.data.msgHead == "FeeUnitEdit") {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = rs.data.record;
                        rows[0].i = rows[0].supcust_id;
                        rows[0].status = rows[0].cls_status_name

                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    var row = rs.data.record
                    updateSingleGridRow("supcust_id", row.supcust_id, { sup_name: row.sup_name, supcust_no: row.supcust_no, boss_name: row.boss_name, mobile: row.mobile, sup_addr: row.sup_addr })

                }
                $("#popItem").jqxWindow('close');//最后关掉popitem
            }
            else if (rs.data.msgHead == "RegionEdit") {
                var newID = ""; var newName = "";
                //注意修改‘region_id’
                if (rs.data.record) { newID = rs.data.record.region_id; newName = rs.data.record.region_name; }
                if (rs.data.action == "add") {
                    var sltItem = $('#other_region').jqxTree('findItem', rs.data.record.mother_id);
                    $('#other_region').jqxTree('addTo', { value: newID, label: newName }, sltItem.element, false);
                    $('#other_region').jqxTree('render');   // update the tree.
                }
                else if (rs.data.action == "update") {
                    var sltItem = $('#other_region').jqxTree('findItem', rs.data.record.region_id);
                    $('#other_region').jqxTree('updateItem', sltItem, { label: newName });
                    $('#other_region').jqxTree('render');

                }
                $("#popClass").jqxWindow('close');
            }
        });

        var newCount = 1;
        function loadBaiduScript(cb) {
            if (g_loadBaiduMap) {
                cb()
                return
            }
            var script = document.createElement('script');
            script.src = 'https://api.map.baidu.com/api?v=2.0&ak=@Html.Raw(Model.BaiduKey)&callback=commonMapInit';
            script.type = 'text/javascript';
            document.head.appendChild(script);
            console.log(document)
            window.commonMapInit = function () {
                g_loadBaiduMap = true
                cb()
            }
        }
        function btnAddRegion(e) {
            var selectedItem = $('#other_region').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个片区", 3000);
                return;
            }
            $('#popClass').jqxWindow('open');
            $("#popClass").jqxWindow('setContent', `<iframe src="RegionEdit?operKey=${g_operKey}&mother_id=${selectedItem.value}&mother_name=${selectedItem.label}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

        };
        function btnEditRegion() {
            var selectedItem = $('#other_region').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个片区", 3000);
                return;
            }
            $('#popClass').jqxWindow('open');
            //注意修改‘region_id’
            $("#popClass").jqxWindow('setContent', `<iframe src="RegionEdit?operKey=${g_operKey}&region_id=${selectedItem.value}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

        };
        function btnRemoveRegion(e) {
            var selectedItem = $('#other_region').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个片区", 3000);
                return;
            }
            jConfirm('确定要删除' + selectedItem.label + '吗？', function () {
                $.ajax({
                    type: "POST",
                    url: "../api/FeeUnitsView/RemoveRegion",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify({ operKey: g_operKey, class_id: selectedItem.value }),
                    success: function (data) {
                        if (data.result == "OK") {
                            var sltItem = $('#other_region').jqxTree('findItem', data.region_id);
                            $('#other_region').jqxTree('removeItem', sltItem, false);
                            $('#other_region').jqxTree('render');
                        }
                        else {
                            bw.toast(data.result, 3000);
                        }
                    }
                });
            }, "");
        }

        function btnAddItem_click(e) {

            if (ObjOperRights.info.infoFeeUnit.create === false) {
                bw.toast("您没有新建费用单位的权限")
                return
            }
            var selectedItem = $('#other_region').jqxTree('selectedItem');
            if (!selectedItem) {
                bw.toast("请先选择一个片区", 3000);//先选择侧边栏地区，然后在添加新费用单位
                return;
            }
            var path = $('#other_region').jqxTree('getTreePath', selectedItem);
            $('#popItem').jqxWindow('open');//打开popitem
            $("#popItem").jqxWindow('setContent', `<iframe src="FeeUnitEdit?operKey=${g_operKey}&region_id=${selectedItem.value}&region_name=${selectedItem.label}&other_region=${path}" width="100%" height="100%" scrolling="auto" frameborder="no"></iframe>`);
        }

        function isRightClick(event) {
            var rightclick;
            if (!event) var event = window.event;
            if (event.which) rightclick = (event.which == 3);
            else if (event.button) rightclick = (event.button == 2);
            return rightclick;
        };
        function attachContextMenu() {

            $("#other_region").on('mousedown', function (event) {
                if (event.target.tagName == "DIV" && event.target.parentNode.tagName == "LI") {
                    var target = event.target.parentNode;

                    var rightClick = isRightClick(event);
                    var contextMenu = event.target.innerText == "全部" ? contextMenu0 : contextMenu1
                    if (rightClick && target != null) {
                        $("#other_region").jqxTree('selectItem', target);
                        var scrollTop = $(window).scrollTop();
                        var scrollLeft = $(window).scrollLeft();
                        var y = event.clientY
                        var treeTop = $('#other_region').offset().top
                        var treeHeight = $('#other_region').height()
                        var menuHeight = $("#jqxMenu_1").height()
                        //console.log(event.offsetY)
                        if (event.clientY > treeTop + treeHeight - menuHeight - 0) {
                            y = event.clientY - menuHeight - 10
                        }
                        contextMenu.jqxMenu('open', parseInt(event.clientX) + 5 + scrollLeft, y + 5 + scrollTop);

                        return false;
                    }



                }

            });

        }

        function onGridRowEdit(rowIndex) {  //用点击费用单位单元格的方法打开了popitem费用单位信息栏，通过$("#gridItems").on("cellclick", function (event)进行监听
            var supcust_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'supcust_id');
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', '<iframe id="FeeUnitEdit" src="FeeUnitEdit?operKey=' + g_operKey + '&supcust_id=' + supcust_id + '" width="100%" height="100%"  frameborder="no"></iframe>');
            //zxk 临时更新 分辨率问题 2022/7/27
            if (screen.width === 1366 && screen.height === 768) {
                $("#popItem").css("height", screen.height - 100 + "px")
            }
        }
        var itemSource = {};
        $(document).ready(function () {
            window.contextMenu0 = $("#jqxMenu_0").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });
            window.contextMenu1 = $("#jqxMenu_1").jqxMenu({ width: '120px', autoOpenPopup: false, mode: 'popup' });



        @Html.Raw(Model.m_showFormScript)

        @Html.Raw(Model.m_createGridScript)
                /*
                var treeHeight = $('#other_region').height()
                if (treeHeight == 0) {
                    treeHeight = $('body').height() - $('#divTop').height() - 55
                    $('#other_region').height(treeHeight)
                }*/
                QueryData();
            $("#btnAddClass").bind("click", { isParent: false }, btnAddRegion);
            $("#btnEditClass").bind("click", btnEditRegion);
            $("#btnRemoveClass").bind("click", btnRemoveRegion);

            $("#gridItems").on("cellclick", function (event) {//这里是点击费用单位名称表格单元，griditems是费用单位的明细表，这里再对点击费用单位名称进行监听
                //debugger;
                if (isRightClick(event.originalEvent)) return;
                // event arguments.
                var args = event.args;
                if (args.datafield == "sup_name") {
                    var supcust_id = args.row.bounddata.supcust_id;
                    RowIndex = args.rowindex;//args包含被点击单元格和参数
                    if (ForSelect) {//似乎是变成小写
                        var supcust_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "supcust_id");//获得args.rowindex行和supcust_id列的值
                        var supcust_flag = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "supcust_flag");
                        var sup_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "sup_name");
                        var acct_type = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "acct_type");
                        var boss_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "boss_name");
                        var mobile = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "mobile");
                        var sup_addr = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "sup_addr");

                        var checkedRows = []
                        checkedRows.push({
                            supcust_id: supcust_id, sup_name: sup_name, acct_type: acct_type,
                            boss_name: boss_name, mobile: mobile, sup_addr: sup_addr
                        })


                        var msg = {
                            msgHead: 'FeeUnitsView', action: 'select', supcust_id: supcust_id, sup_name: sup_name, acct_type: acct_type, checkedRows: checkedRows, supcust_flag: supcust_flag
                        };
                        debugger
                        window.parent.postMessage(msg, '*');//这里负责iframe与父元素通信

                    }
                    else {
                        onGridRowEdit(args.rowindex);

                    }

                }

                else if (args.datafield == "approve_status") {
                    var sup_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "supcust_id");//获得args.rowindex行和supcust_id列的值
                    var sup_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "sup_name");
                    url = `Report/DocChange?&obj_name=${'费用单位档案'}&sup_id=${sup_id}&sup_name=${sup_name}`;
                    window.parent.newTabPage('费用单位档案变化表', `${url}`);
                }



            });
            $("#Cancel").on('click', function () {
                for (var i = 0; i < 10; i++) {
                    $('#jqxgrid').jqxGrid('deleterow', i);
                    $('#jqxgrid').jqxGrid('addrow', i, {})
                }
            });

            $('#btnSelectItems').hide()
            $('#gridItems').jqxGrid('hidecolumn', 'sys_check')
            if (window.MultiSelect) {
                $('#btnSelectItems').show()
                $('#gridItems').jqxGrid('showcolumn', 'sys_check')
            }
            //console.log("获取高度", $("#popItem").parent().height())
            let itemMaxHeight = $("#popItem").parent().height()
            let itemMaxWidth = $("#popItem").parent().width()
            let classMaxHeight = $("#popClass").parent().height()
            let classMaxWidth = $("#popClass").parent().width()
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 580, width: 600, maxHeight: itemMaxHeight - 50, maxWidth: itemMaxWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            $("#popClass").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 350, width: 600, maxHeight: classMaxHeight - 50, maxWidth: classMaxWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

            attachContextMenu();



            //$("#btnSave",document.frames("frame的name")
            $("#btnSave").on('click', function (e) {
                QueryData();
            });


            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });

            $(document).on('click', function (e) {

                var contextMenu = e.target.innerText
                var ee = $('#popSet').css("display")
                if (contextMenu == "全部" && ee == "block") {
                    bw.toast("请勿将费用单位片区设置为'全部'");
                }
            });
        });


        function btnSelectItems_click() {
            var rows = window.g_checkedRows
            //var rows = window.g_arrCheckedRows
            var checkedRows = []
            for (var id in rows) {
                var row = rows[id]
                //checkedRows.push({item_id:row.i,item_name:row.n})


                checkedRows.push({
                    supcust_id: row.supcust_id, sup_name: row.sup_name, boss_name: row.boss_name, mobile: row.mobile, sup_addr: row.sup_addr, acct_type: row.acct_type
                })

            }
            var msg = {
                msgHead: 'FeeUnitsView', action: 'selectMulti', checkedRows: checkedRows
            }
            window.parent.postMessage(msg, '*');
        }


        function onGridRowContextMenuClick(gridID, menuID, rowIndex) {

            var rows = $('#gridItems').jqxGrid('getrows')

            if (menuID == 'BatchOperation') {
                $('#gridItems').jqxGrid('showcolumn', 'sys_check')
                var a = $('#popBatchOperation')
                $('#popBatchOperation').css("display", "block")

            }

        }

        window.g_batchSetFld = ''
        function popBatchSetDlg(fld) {

            $('#popBatchOperation').css("display", "none")
            $('#popSet').css("display", "block")
            window.g_batchSetFld = fld
            if (fld == "other_region") {
                $('#set_head').append('片区设置');
                $('#div_set').append('<label  id="name" style="line-height: 32px;">片区</label> ');
                $('#div_set').append('<div id = "set" > </div>');
                $('#set').jqxDropDownTree({ dropDownWidth: 200, dropDownHeight: 250, url: '../api/FeeUnitsView/GetDataItemOptions?operKey= ' + g_operKey + '&dataItemName=other_region', source: null, checkboxes: false, mumSelectable: true });
                $('#set').jqxInput('val', { value: ``, label: `` });
            } else if (fld == "sup_group") {
                $('#set_head').append('渠道设置');
                $('#div_set').append('<label  id="name" style="line-height: 32px;">渠道</label> ');
                $('#div_set').append('<div id = "set" > </div>');
                $('#set').jqxDropDownTree({ dropDownWidth: 200, dropDownHeight: 250, url: '../api/FeeUnitsView/GetDataItemOptions?operKey= ' + g_operKey + '&dataItemName=sup_group', source: null, checkboxes: false, mumSelectable: true });
                $('#set').jqxInput('val', { value: ``, label: `` });
            } else if (fld == "location") {
                $('#set_head').append('定位设置');
                $('#div_set').append('<label   style="line-height: 32px;"  >范围</label>');
                $('#div_set').append('<div > <select id="set" style="width: 200px;"><option >勾选</option> <option >全部</option></select></div>');
            }
            else if (fld == "status") {
                $('#set_head').append('状态设置');
                $('#popBatchOperation').css("display", "none")
                $('#popSet').css("display", "block")
                $('#div_set').append('<label   style="line-height: 32px;"  >状态</label>');
                $('#div_set').append('<div > <select id="set" style="width: 200px;"><option >正常</option> <option >停用</option></select></div>');
            }
            else if (fld == "delete") {
                $('#set_head').append('删除设置');
                $('#popBatchOperation').css("display", "none")
                $('#popSet').css("display", "block")
                $('#div_set').append('<label   style="line-height: 32px;"  >范围</label>');
                $('#div_set').append('<div > <select id="set" style="width: 200px;"><option >勾选</option> </select></div>');
            }
            else if (fld == "rank") {
                $('#set_head').append('等级设置');
                $('#div_set').append('<label  id="name" style="line-height: 32px;">等级</label> ');
                $('#div_set').append('<div id = "set" > </div>');
                $('#set').jqxDropDownTree({ dropDownWidth: 200, dropDownHeight: 250, url: '../api/FeeUnitsView/GetDataItemOptions?operKey= ' + g_operKey + '&dataItemName=sup_rank', source: null, checkboxes: false, mumSelectable: true });
                $('#set').jqxInput('val', { value: ``, label: `` });
            }
            else if (fld == "acctType") {
                $('#set_head').append('结算方式');
                $('#div_set').append('<label  id="name" style="line-height: 32px;">结算方式</label> ');
                $('#div_set').append('<div id = "set" > </div>');
                $('#set').jqxDropDownTree({ dropDownWidth: 200, dropDownHeight: 250, source: [{ v: 'pay', l: '现结' }, { v: 'arrears', l: '欠款' }], checkboxes: false, mumSelectable: true });
                $('#set').jqxInput('val', { value: ``, label: `` });
            }
            else if (fld == "address") {
                $('#set_head').append('地址设置');
                $('#popBatchOperation').css("display", "none")
                $('#popSet').css("display", "block")
                $('#div_set').append('<label   style="line-height: 32px;"  >范围</label>');
            }
            else if (fld == "set_operator") {
                $('#set_head').append('设置业务员');
                $('#popBatchOperation').css("display", "none")
                $('#popSet').css("display", "block")
                window.g_pageSetting.dataItems.push({ datafield: 'operator', text: '业务员', origText: '业务员', alwaysShow: false, hidden: false, visible: true });
                $('#div_set').append('<div id = "set" > </div>');
                $('#set').jqxDropDownTree({ dropDownWidth: 200, dropDownHeight: 250, url: '../api/FeeUnitEdit/GetDataItemOptions?dataItemName=charge_seller&&operKey=' + g_operKey, source: null, checkboxes: false, mumSelectable: true });
                $('#set').jqxInput('val', { value: ``, label: `` });
            }
        }





        function btnClose_Clicked() {
            $('#popSet').css("display", "none")
            $('#div_set').empty()
            $('#set_head').empty()
        }

        function btnSave_Clicked() {
            debugger
            console.log(g_batchSetFld)
            var rows = []
            for (var id in window.g_checkedRows) {
                rows.push(parseInt(window.g_checkedRows[id].supcust_id))
            }
            //window.g_checkedRows.forEach(function (a) {

            //    rows.push(parseInt(a));
            //});

            if (rows.length == 0) bw.toast("未做勾选");
            //rows = rows.join(',')

            var url = ''
            var params = {}
            if (g_batchSetFld == 'other_region') {
                var region_id = $('#set').val().value
                var other_region = $('#set').jqxDropDownTree('treePath')//.slice(1)
                params = { region_id: region_id, other_region: other_region }
                url = '../api/feeUnitsView/BatchSetRegion'
                executeSubmit(url, params, rows)
            }
            else if (g_batchSetFld == 'sup_group') {
                var sup_group = $('#set').val().value
                params = { sup_group: sup_group }
                url = '../api/feeUnitsView/BatchSetGroup'
                executeSubmit(url, params, rows)
            }
            else if (g_batchSetFld == 'location') {
                var type = $('#set').val()
                params = { type: type }
                url = '../api/feeUnitsView/BatchSetLocation'
                executeSubmit(url, params, rows)
            }
            else if (g_batchSetFld == 'status') {
                status = $('#set').val()
                params = { status: status }
                url = '../api/feeUnitsView/BatchSetStatus'
                executeSubmit(url, params, rows)
            }
            else if (g_batchSetFld == 'delete') {
                type = $('#set').val()
                params = { type: type }
                url = '../api/feeUnitsView/BatchDelete'
                executeSubmit(url, params, rows)
            }
            else if (g_batchSetFld == 'rank') {
                var sup_rank = $('#set').val().value
                params = { sup_rank: sup_rank }
                url = '../api/feeUnitsView/BatchSetRank'
                executeSubmit(url, params, rows)
            }
            else if (g_batchSetFld == 'acctType') {
                var acct_type = $('#set').val().value
                params = { acct_type: acct_type }
                url = '../api/feeUnitsView/BatchSetAcctType'
                executeSubmit(url, params, rows)
            }
            else if (g_batchSetFld == 'address') {
                type = $('#set').val()
                params = { type: type }
                loadBaiduScript(() => {
                    console.log("加载成功")
                    getRealAddressByRows(g_checkedRows, res => {
                        params.updateAddressRows = res
                        if (res.length == 0) {
                            bw.toast('没有可导入的定位信息');
                            $('#popSet').css("display", "none")
                            $('#div_set').empty()
                            $('#set_head').empty()
                        }
                        url = '../api/feeUnitsView/BatchSetAddress'
                        executeSubmit(url, params, rows)
                    })
                })

            }
            else if (g_batchSetFld == 'set_operator') {
                var operID = $('#set').val().value
                params = { operID: operID }
                url = '../api/feeUnitsView/BatchSetOperator'
                executeSubmit(url, params, rows)
            }
        }
        function executeSubmit(url, params, rows) {
            console.log("在费用单位档案批量操作时执行")
            params.operKey = '@Model.OperKey'
            params.rows = rows
            $.ajaxSetup({ contentType: "application/json" });
            $.post(url, JSON.stringify(params)).then(
                function (data) {
                    if (data.result == 'OK') {
                        bw.toast('操作成功');
                        $('#popSet').css("display", "none")
                        $('#div_set').empty()
                        $('#set_head').empty()

                        if (url.indexOf('BatchSetRegion') >= 0) {
                            window.g_checkedRows = {}
                            window.g_arrCheckedRows = []
                            window.checkedIds = []
                        }
                        QueryData(undefined, clearInvisibleSelections)
                    } else {
                        $("#popMessage").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 500, width: 1000, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
                        $('#divMessage').html(data.msg);
                        if (g_batchSetFld == 'status') {
                            $('#jqxWindows_body').append('<div id="divTable" style="margin-top:2px"></div>');
                            $('#divTable').append('<table id="tableLeft" style="border:1px solid black;float:left;width:49%;border-collapse:collapse;"><thead><tr> <th>欠款费用单位编号 </th><th> 欠款费用单位名称 </th></tr> </thead><tbody id = "tableLeftBody"></tbody></table>');
                            $('#divTable').append('<table id="tableRight" style="border:1px solid black;float:right;width:49%;border-collapse:collapse;"><thead> <tr> <th>预收款费用单位编号 </th><th> 预收款费用单位名称 </th></tr></thead><tbody id = "tableRightBody"></tbody></table>');
                            if (data.arrear_list.length) {
                                for (let i = 0; i < data.arrear_list.length; i++) {
                                    $('#tableLeftBody').append('<tr><td>' + data.arrear_list[i].supcust_no + '</td><td>' + data.arrear_list[i].sup_name + '</td></tr>');
                                }
                            }
                            if (data.prepay_list.length) {
                                for (let i = 0; i < data.prepay_list.length; i++) {
                                    $('#tableRightBody').append('<tr><td>' + data.prepay_list[i].supcust_no + '</td><td>' + data.prepay_list[i].sup_name + '</td></tr>');
                                }
                            }
                        }
                        $('#popMessage').jqxWindow('open');
                        $('#popMessage').on('close', function (event) {
                            $('#divTable').remove();//窗口关闭的时候，移除窗口内的表格元素
                            console.log("jqxwindows窗口关闭了");
                        });
                    }
                })
        }
        function clearInvisibleSelections(res) {
            if (!res || res.result !== 'OK') return
            const rows = res.rows
            const selected = window.g_checkedRows
            if (!rows || !selected) return
            const visible_supcusts = []
            console.warn('[clearInvisibleSelections] rows:', rows)
            for (const row_index in rows) {
                const row = rows[row_index]
                visible_supcusts.push(row.supcust_id)
            }
            console.warn('[clearInvisibleSelections] visible_supcusts:', visible_supcusts)
            console.warn('[clearInvisibleSelections] selected:', selected)
            for (const checked_sup in selected) {
                if (!visible_supcusts.includes(checked_sup))
                    delete window.g_checkedRows[checked_sup]
            }
            console.warn('[clearInvisibleSelections] done! now window.g_checkedRows:', window.g_checkedRows)
        }
        function getRealAddressByRows(rows, cb) {
            var updateRows = []
            var validRows = Object.keys(rows).map((key, index) => {
                return rows[key]
            }).filter(row => !row.sup_addr && row.addr_lng && row.addr_lat)
            if (validRows.length == 0) {
                cb(updateRows)
                return
            }
            validRows.map((row, index) => {
                getAddress(row.addr_lng, row.addr_lat, (addr) => {
                    updateRows.push({
                        sup_addr: addr,
                        supcust_id: row.supcust_id
                    })
                    if (index == validRows.length - 1) {
                        cb(updateRows)
                    }

                })
            })
        }
        function getAddress(longitude, latitude, callback) {
            var geoc = new BMap.Geocoder()
            var addr = ""
            geoc.getLocation(new BMap.Point(longitude, latitude), (result) => {
                if (result.surroundingPois.length != 0) {
                    addr = result.address + result.surroundingPois[0].title + "附近"
                    callback(addr)
                } else {
                    addr = result.address
                    callback(addr)
                }
            })
            //callback(addr)
        }
        function beforeQuery() {
            /*$("#div_startDay").show()
            $("#div_endDay").show()
            $("#div_seller_id").show()
            $("#div_sup_rank").show()
            $("#div_sup_group").show()
            $("#div_other_region_head").show()
            if (window.ForSelect) {
                $("#div_startDay").hide()
                $("#div_endDay").hide()
                $("#div_seller_id").hide()
                $("#div_sup_rank").hide()
                $("#div_sup_group").hide()
                $("#div_other_region_head").hide()

            }*/

        }
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        .label_name {
            line-height: 22px;
            margin-left: 10px;
        }

        .label_content {
            width: 150px;
            height: 25px;
            margin-left: 10px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 20px;
        }

        #popBatchOperation {
            width: 100px;
            height: 260px;
            position: fixed;
            top: 25%;
            left: 40%;
            z-index: 999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            display: none;
            text-align: center;
            cursor: pointer;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
        }

        #popSet {
            width: 500px;
            height: 300px;
            position: fixed;
            top: 27%;
            left: 30%;
            z-index: 999;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: rgb(255, 255, 255);
            padding: 5px;
            display: none;
            text-align: center;
            box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.25);
        }

        .magic {
            width: 100px;
            height: 25px;
        }

            .magic:hover {
                background: #ddd;
            }

        #div_set {
            display: none;
            margin-left: 123px;
            margin-top: 70px;
            display: flex;
            text-align: center;
        }

        #div_close {
            height: 16px;
            width: 20px;
            cursor: pointer;
            position: relative;
            left: 43px;
            top: 0px;
        }

            #div_close:hover {
                background: #ddd;
            }

        #divTable table th, td { /*这里是设置状态为停用时，显示哪些费用单位不能停用的表格的样式*/
            border: 1px solid grey;
            padding: 1px;
            text-align: center; /*水平居中*/
            vertical-align: middle; /*垂直居中*/
        }

    </style>
</head>

<body style="overflow:hidden">

    <div id="popMessage" style="display:none">
        <div style="height: 30px; background-color: #fff; text-align: center; border-bottom: solid 2px #D5D5D5 ">
            <span style="font-size: 20px;  ; ">操作结果</span>
        </div>
        <div id="jqxWindows_body" style=" margin: 2px;overflow-y:scroll;">
            <div id="divMessage">         </div>

        </div>
    </div>



    <div id="popBatchOperation">
        <svg id="div_close" onclick=" this.parentNode.style.display = 'none'">
            <use xlink:href="/images/images.svg?v=@Html.Raw(Model.Version)#close" />
        </svg>

        <div class="magic " onclick="popBatchSetDlg('other_region')">设置片区</div>
        <div class="magic " onclick="popBatchSetDlg('sup_group')">设置渠道</div>
        <div class="magic " onclick="popBatchSetDlg('status')">设置状态</div>
        <div class="magic " onclick="popBatchSetDlg('location')">设置定位</div>
        <div class="magic " onclick="popBatchSetDlg('address')">设置地址</div>
        <div class="magic " onclick="popBatchSetDlg('rank')">设置等级</div>
        <div class="magic " onclick="popBatchSetDlg('acctType')">设置收款方式</div>
        <div class="magic " onclick="popBatchSetDlg('delete')">批量删除</div>
        <div class="magic " onclick="popBatchSetDlg('set_operator')">设置业务员</div>


    </div>

    <div id="popSet" style="display: none;">
        <div style="height:30px;background-color:#fff; text-align:center;">
            <span id="set_head" style="font-size:18px;"></span>
        </div>
        <div id="div_set">
        </div>

        <div style="overflow:hidden;">
            <button onclick="btnSave_Clicked()" style="align-content:center;margin-top:45px;margin-left:20px;">确认</button>
            <button onclick="btnClose_Clicked();" style="align-content:center;margin-top:45px;margin-left:75px">关闭</button>
        </div>
    </div>



    <div id="divTop" style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">

            <div style="float:none;height:0px; clear:both;"></div>

        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnSelectItems" class="margin" style="width:100px" onclick="btnSelectItems_click()">选择费用单位</button>

        @if (!Model.WithClients)
        {
            <button onclick="btnAddItem_click()" class="margin" style="width:100px">新增单位</button>
        }
        
        @if (!Model.ForSelect)
        {
            <button id="btnExport" onclick="ExportExcel()" class="margin">导出</button>
        }

        @*<button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;margin-top:30px;">导出</button>*@

    </div>

    @*<div id="divHead" style="display:flex; justify-content:space-around;margin-top:20px;">
    <div style="display:flex">
    <input id="supcust_flag" type="hidden" />
    <div id="div_startDay"><label class="label_name">开始日期</label> <div id="startDay" class="label_content"></div></div>
    <div id="div_endDay"><label class="label_name">结束日期</label> <div id="endDay" class="label_content"></div></div>
    <div id="div_sup_name"><label class="label_name">客户</label> <div id="sup_name" class="label_content"></div></div>
    <div id="div_seller_id"><label class="label_name">业务员</label> <div id="seller_id" class="label_content"></div></div>
    <div id="div_sup_group"><label class="label_name">渠道</label> <div id="sup_group" style="width:120px;height:25px; margin-left:10px;"></div></div>
    <div><label class="label_name">状态</label> <div id="status" style="width:120px;height:25px; margin-left:10px;"></div></div>
    <div id="div_sup_rank"><label class="label_name">级别</label> <div id="sup_rank" style="width:120px;height:25px; margin-left:10px;"></div></div>
    <button onclick="QueryData()" class="margin">查询</button>
    </div>
    <div>
    <button onclick="btnAddItem_click()" class="margin">新增客户</button>
    @if (!Model.ForSelect)
    {
    <button id="btnExport" onclick="ExportExcel()" class="margin">导出</button>
    }
    </div>

    </div>*@
    <div style="flex:1;display:flex;width:100%;">

        <div id='other_region' style="width: 200px; height: calc(100% - 20px); margin-top: 20px; margin-bottom: 2px; overflow-y: scroll">
        </div>

        <div style="width:calc(100% - 200px);height:100%; margin-left:10px;">

            <div><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>

            <div id="gridItems" style="margin-top:0px;margin-left:10px; margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 20px);"></div>

        </div>
    </div>
    <div style="display:flex;height:30px;width:100%;margin-bottom:0px;">
    </div>

    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">费用单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popClass" style="display:none;">
        <div style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">片区</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

    <div id='jqxMenu_1' style="display:none;">
        <ul>
            <li id="mnuEditClass" onclick="btnEditRegion()">编辑片区</li>
            <li id="mnuAddClass" onclick="btnAddRegion()">添加下级片区</li>
            <li id="mnuRemoveClass" onclick="btnRemoveRegion()">删除片区</li>
        </ul>
    </div>
    <div id='jqxMenu_0'>
        <ul>
            <li id="mnuAddClass" onclick="btnAddRegion()">添加下级片区</li>
        </ul>
    </div>

</body>
</html>
