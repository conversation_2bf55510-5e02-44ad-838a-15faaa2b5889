@page
@model ArtisanManage.Pages.BaseInfo.StockChangeSumModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel"/>
    
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var m_db_id = "10";

        var newCount = 1;

    	    var itemSource = {};
    	   $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
            $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                    var item_id = args.row.bounddata.item_id;
                    var item_name = args.row.bounddata.item_name;
                    var startDay = $('#startDay').jqxDateTimeInput('val');
                    var endDay = $('#endDay').jqxDateTimeInput('val');
                    var branch_id = $('#branch_id').val().value;
                    var branch_name = $('#branch_id').val().label;
                    var branch_position = $('#branch_position').val().value;
                    var branch_position_name = $('#branch_position').val().label;
                    var produce_date = args.row.bounddata.produce_date ? args.row.bounddata.produce_date : "";
                    var batch_no = args.row.bounddata.batch_no ? args.row.bounddata.batch_no : "";
                    if (!produce_date){
                        produce_date = $('#produce_date').val() ? $('#produce_date').val() :"";
                    }
                    if(!batch_no){
                        batch_no = $('#batch_no').val() ? $('#batch_no').val() : "";
                    }
                    var onlyNoBatch = $('#onlyNoBatch').jqxCheckBox('checked');
                    if (!branch_position) {
                        branch_position = "0"
                        branch_position_name = ""
                    }
                    if (!branch_id) {
                        branch_id = ""
                        branch_name = ""
                    }
                    var url="";
                    if (onlyNoBatch.toString() == "false") {
                        onlyNoBatch = false
                    }else{
                        onlyNoBatch = true
                    }
                    if (args.datafield == "item_name" && item_id) {
                         
                       url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${encodeURIComponent(item_name)}&startDay=${startDay}&endDay=${endDay}`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        console.log(url)
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }
                    if (args.datafield == "cg_add_qty_unit"  && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=CG&sheet_type_name=采购单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }

                     else if (args.datafield == "cg_reduce_qty_unit" && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=CT&sheet_type_name=采购退货单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                     }
                    else if (args.datafield == "xs_add_qty_unit"  && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=X&sheet_type_name=销售单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }
                    else if (args.datafield == "xs_reduce_qty_unit" && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=T&sheet_type_name=销售退货单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }
                    else if (args.datafield == "jh_add_qty_unit" && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=JH&sheet_type_name=借货单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }
                    else if (args.datafield == "jh_reduce_qty_unit" && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=HH&sheet_type_name=还货单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }
                    else if (args.datafield == "db_add_qty_unit"  && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=DR&sheet_type_name=调入单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    } 
                    else if (args.datafield == "db_reduce_qty_unit" && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=DC&sheet_type_name=调出单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    } 
                    else if ((args.datafield == "yk_add_qty_unit" ||args.datafield == "yk_reduce_qty_unit") && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=YK &sheet_type_name=盘点盈亏单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }
                    else if ( args.datafield == "yk_reduce_qty_unit" && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=YK,BS&sheet_type_name=报损单,盘点盈亏单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }
                    else if (args.datafield == "cr_add_qty_unit" && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=RK&sheet_type_name=其他入库单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }
                    else if (args.datafield == "cr_reduce_qty_unit" && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=CK&sheet_type_name=其他出库单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }
                    else if (args.datafield == "zc_add_qty_unit" && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=ZCR&sheet_type_name=组拆入仓单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }
                    else if (args.datafield == "zc_reduce_qty_unit" && item_id) {
                        url = `Report/StocksChangeByOrder?&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}&sheet_type=ZCC&sheet_type_name=组拆出仓单`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}&branch_position=${branch_position}&branch_position_name=${branch_position_name}`;
                        if (onlyNoBatch) {
                            url += `&onlyNoBatch=${onlyNoBatch}`;
                        } else {
                            url += `&produce_date=${produce_date}&batch_no=${batch_no}`;
                        }
                        window.parent.newTabPage('库存变化明细表', `${url}`);
                    }







                    else if (args.datafield == "end_qty_unit" && item_id) {
                        url = `Report/StocksView?&item_id=${item_id}&item_name=${item_name}`;
                        if (branch_name) url += `&branch_id=${branch_id}&branch_name=${branch_name}`;
                        window.parent.newTabPage('库存表', `${url}`);
                    }

                    });
                
                QueryData();
            let windowHeight = document.body.offsetHeight - 50
            let windowWidth = document.body.offsetWidth - 80
                 $('#item_id').jqxInput({
                    onButtonClick: function (event) {
                        $('#popItem').jqxWindow('open');
                        $("#popItem").jqxWindow('setContent', `<iframe src="/BaseInfo/ItemsView?forSelect=1&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
                    }
                });
            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 600, width: 1000, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

           });


        window.addEventListener('message', function (rs) {
            
             if (rs.data.msgHead === "ItemsView") {
                 if (rs.data.action === "selectMulti") {
                     if (rs.data.checkedRows.length == 1) {
                         var item_id = rs.data.checkedRows[0].item_id;
                         var item_name = rs.data.checkedRows[0].item_name;
                     }

                     var rows = rs.data.checkedRows
                     var items_id = ''
                    var items_name = ''
                     rows.forEach(function (row) {
                         if (items_id != '') items_id += ','
                        if (items_name != '') items_name += ','
                         items_id += row.item_id
                        items_name += row.item_name
                     })
                    $('#item_id').jqxInput('val', { value: items_id, label: items_name  });

                    $.ajax({
                        url: '/api/SaleSheet/GetItemInfo',
                        type: 'GET',
                        contentType: 'application/json',
                        data: { operKey: g_operKey, item_id: items_id },
                        success: function (data) {
                            if (data.result === 'OK') {
                                if (!window.g_queriedItems) window.g_queriedItems = {};
                                window.g_queriedItems[item_id] = data.item;
                            }
                        }
                    });
                }

                $('#popItem').jqxWindow('close');
             }

        });
        function viewProduceDate(row, column, value, p4, p5, rowData) {
            let showBatch = $("#showBatch").val().toString().toLowerCase() == "true" ? true : false;
            if (showBatch) {
                let cellValue = value
                return `<div style="height:100%;width:100%;display: flex;align-items:center;justify-content:center;">${cellValue ? cellValue : '无产期'}</div>`
            } else {
                let cellValue = value ? JSON.parse(JSON.stringify([value])) : []
                if (cellValue.length == 0) return ""
                if (cellValue.length == 1) {
                    let e = cellValue[0]
                    if (!e.produce_date) return `<div style="height:100%;width:100%;display: flex;align-items:center;justify-content:center;">无产期</div>`
                    else {
                        return `<div style="height:100%;width:100%;display: flex;align-items:center;justify-content:center;">${e.produce_date}</div>`
                    }
                }
                // if (cellValue.length > 1) return `<div onclick='showBatchDetail(${value})' style="height:100%;display:flex;align-items:center;justify-content:center;color:#4499ff;" >查看</div>`
            }
        }
    </script>
</head>

<body style="overflow:hidden">
   
    <div style="display:flex;padding-top:20px;">
        <div id="divHead" class="headtail" style="width:calc(100% - 100px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <button onclick="QueryData()" style="margin-left:20px;">查询</button>
        <button id="btnExport" onclick="ExportExcel()" style="margin-left:20px;">导出</button>
    </div>
     
     
    <div id="gridItems"></div>  
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div> 
           

    <div id="popItem" style="display:none">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">单位信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>

</body>
</html>