@page
@model ArtisanManage.Pages.BaseInfo.SalesTrendModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel"/> 

    @*<script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.aggregates.js"></script>*@
    <script src="~/js/echarts.common.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
            var m_db_id = "10";
            var newCount = 1;
    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)

                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                });
              
                $('.btnAct').on('click', function () {
                    var act = $(this).data('act');
                    window[act]();
                    updateCharts();
                });
                btnQuery_click() 
                
            });
        function btnQuery_click() {
            QueryData(null, updateCharts);
 
        }
    </script>

</head>

<body style="overflow:hidden">
  
    <div style="display:flex;margin-top:20px;margin-left:20px"; id="divHead" class="headtail">

        <div style="display: flex;">
            @*<label>交易日期：</label><input id="startDay" type="date" />~<input id="endDay" type="date" />*@
            <div style="height:30px"><label>开始日期:</label></div><input id="startDay" />
            <div style="height:30px;width:100px"><label>结束日期：</label></div><input id="endDay" />
            <div style="margin-left:20px;margin-top:5px"> <button onclick="btnQuery_click()">查询</button> </div>
        </div>

    </div>


    
      <div id="main" style="height:50%;padding: 2rem 0;"></div>
        
     <div id="gridItems" style="margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 95px);"></div>
          
    

     
    <script type="text/javascript">
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('main'));

        // 指定图表的配置项和数据
        var option = {
            tooltip: {},
            legend: {
                data: ['销量走势图']
            },
            xAxis: {
                data: [],
                axisLabel: {
                    interval: 0,
                    rotate: 40
                }
            },
            yAxis: {},
            series: [{
                name: '销量走势图',
                type: 'line',
                smooth:true,
                data: [],
                itemStyle: {
                    color:'#cc163a'
                },
                symbol: 'emptyCircle',
                symbolSize: 10,
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: '#CC5358'
                            },
                            {
                                offset: 1,
                                color: '#f9f3f3' // 100% 处的颜色
                            }
                        ],
                        global:false
                    }
                },
                xAxisIndex:0
            }]
        };

        function updateCharts() {
            
            var rows = $('#gridItems').jqxGrid('getrows');
            option.xAxis.data = rows.map(row => row.interval).reverse();
            option.series[0].data = rows.map(row => row.net_amount).reverse();
            myChart.setOption(option);
          
        };


                // 使用刚指定的配置项和数据显示图表。
    </script>

</body>
</html>