﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    [Route("AppApi/[controller]/[action]")]

    public class SheetMoveController:Controller
    {
        CMySbCommand cmd;


        public SheetMoveController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        /// <summary>
        /// 调拨单总表
        /// </summary>
        /// <param name="operKey"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="toBranch"></param>
        /// <param name="fromBranch"></param>
        /// <param name="itemName"></param>
        /// <param name="brandName"></param>
        /// <returns></returns>


        [HttpGet]
        public async Task<JsonResult> MoveList(string operKey, string startDate,string endDate,string toBranch,string fromBranch,string itemName,string brandID,string classID, int pageSize, int startRow,string sellerID,string departID)
        { 
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where mm.company_id = {companyID}  and approve_time is not null and red_flag is null ";
            if (startDate != null && endDate != null ) condi += $" and mm.happen_time  >= '{startDate}' and mm.happen_time  <= '{CPubVars.PadDateWith2359(endDate)}'";
            if (toBranch != null) condi += $" and mm.to_branch_id = {toBranch} ";
            if (fromBranch != null) condi += $" and mm.from_branch_id = {fromBranch} ";
            if (itemName != null) condi += $" and (ip.item_name ilike '%{itemName}%' or ip.py_str ilike '%{itemName}%') ";
            if (brandID != null) condi += $" and ip.item_brand  = {brandID} ";
            if (classID != null) condi += $" and ip.other_class like  '%/{classID}/%' ";
            if (sellerID.IsValid()) condi += $" and (mm.maker_id = '{sellerID}' or mm.seller_id = '{sellerID}' ";
            if (departID.IsValid()) condi += @$" AND mm.maker_id in (
            SELECT oper_id FROM info_operator where depart_id = '{departID}' and company_id ='{companyID}'
	         ) ";
            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"select to_branch_name,from_branch_name,ip.item_name,concat(round(sum(qty::numeric/b_unit_factor::numeric),2),b_unit_no) as amount,
                  round((COALESCE(ip.cost_price_avg,0)* SUM ( qty)):: NUMERIC,2) cost_amout ,
                   unit_from_s_to_bms (round( SUM ( qty :: NUMERIC  ), 2 ),b_unit_factor,m_unit_factor::numeric,1,b_unit_no,m_unit_no,s_unit_no) qty_unit,
                     yj_get_unit_qty ('b',round(sum(qty::numeric::numeric),2),b_unit_factor:: NUMERIC,m_unit_factor:: NUMERIC,false) b_qty,
                     yj_get_unit_qty ('m',round(sum(qty::numeric::numeric),2),b_unit_factor:: NUMERIC,m_unit_factor:: NUMERIC,false) m_qty,
                     yj_get_unit_qty ('s',round(sum(qty::numeric::numeric),2),b_unit_factor:: NUMERIC,m_unit_factor:: NUMERIC,false) s_qty
                    from sheet_move_main mm
                        right join
			                (SELECT md.item_id,sheet_id,(md.quantity*md.unit_factor) qty,(t.b1->>'f1')::numeric as b_unit_factor,(t.b1->>'f2') as b_unit_no,(t.b->> 'f2') as bUnit,(t.b->>'f1') as bFactor,
                                    (t.s->> 'f2') as sUnit,(t.s->>'f1') as sFactor ,( T.s ->> 'f2' ) AS s_unit_no,
		                        ( T.s ->> 'f1' ) AS s_unit_factor ,
		                        ( T.m ->> 'f2' ) AS m_unit_no,
		                        ( T.m ->> 'f1' ) AS m_unit_factor  from sheet_move_detail md 
								left join 
                            (select item_id, s, m, b, (case when b->>'f1' is null then s ELSE b end) b1 from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id={companyID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) 
                                                    as errr(item_id int, s jsonb,m jsonb, b jsonb)) t on t.item_id = md.item_id where md.company_id= {companyID} ORDER BY item_id) md on mm.sheet_id = md.sheet_id
                       left join info_item_prop ip on ip.item_id = md.item_id
                       LEFT JOIN (select branch_id,branch_name as from_branch_name from info_branch where company_id= {companyID}) fb ON mm.from_branch_id = fb.branch_id 
                       LEFT JOIN (select branch_id,branch_name as to_branch_name from info_branch where company_id= {companyID}) tb ON mm.to_branch_id = tb.branch_id 
                       LEFT JOIN info_item_brand as iib on iib.brand_id = ip.item_brand 
                       LEFT JOIN (select * from info_item_class where company_id = {companyID}) as ic on ic.class_id = ip.item_class  {condi} GROUP BY to_branch_name,from_branch_name,ip.item_name,b_unit_no,ip.cost_price_avg  ,b_unit_factor,m_unit_factor,b_unit_no,m_unit_no,s_unit_no limit {pageSize} offset {startRow};";
            QQ.Enqueue("sheets", sql);
            sql = $@"select concat(round(sum(qty::numeric/b_unit_factor::numeric),2),b_unit_no) as totalAmount
                     from sheet_move_main mm
                     right join
			                (SELECT md.item_id,sheet_id,(md.quantity*md.unit_factor) qty,(t.b1->>'f1')::numeric as b_unit_factor,(t.b1->>'f2') as b_unit_no,(t.b->> 'f2') as bUnit,(t.b->>'f1') as bFactor,
                                                    (t.s->> 'f2') as sUnit,(t.s->>'f1') as sFactor from sheet_move_detail md 
								left join 
                                (select item_id, s, m, b, (case when b->>'f1' is null then s ELSE b end) b1 from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id={companyID} ORDER BY item_id',$$values('s'::text),('m'::text),('b'::text)$$) 
                                                    as errr(item_id int, s jsonb,m jsonb, b jsonb)) t on t.item_id = md.item_id where md.company_id= {companyID} ORDER BY item_id) md on mm.sheet_id = md.sheet_id
                    left join info_item_prop ip on ip.item_id = md.item_id
                    LEFT JOIN (select branch_id,branch_name as from_branch_name from info_branch where company_id= {companyID}) fb ON mm.from_branch_id = fb.branch_id 
                    LEFT JOIN (select branch_id,branch_name as to_branch_name from info_branch where company_id= {companyID}) tb ON mm.to_branch_id = tb.branch_id 
                    LEFT JOIN info_item_brand as iib on iib.brand_id = ip.item_brand 
                    LEFT JOIN (select * from info_item_class where company_id = {companyID}) as ic on ic.class_id = ip.item_class {condi}  GROUP BY b_unit_no;";
            QQ.Enqueue("total", sql);
            sql = @$"SELECT count(sheet_no) as count FROM sheet_move_main as mm
                         LEFT JOIN info_branch as ib on ib.branch_id = mm.from_branch_id and ib.company_id ={companyID} 
                         LEFT JOIN info_branch as ib1 on ib1.branch_id = mm.to_branch_id and ib1.company_id ={companyID} 
                         LEFT JOIN sheet_move_detail as md on md.sheet_id = mm.sheet_id and md.company_id ={companyID}
                         LEFT JOIN info_item_prop as ip on ip.item_id = md.item_id and ip.company_id = {companyID}
                         LEFT JOIN info_item_brand as iib on iib.brand_id = ip.item_brand  and iib.company_id = {companyID}
                         LEFT JOIN (select * from info_item_class where company_id = {companyID}) as ic on ic.class_id = ip.item_class {condi} ";
            QQ.Enqueue("count", sql);
            List<ExpandoObject> data = null;

            List<ExpandoObject> total = null;
            var count = "";
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "sheets")
                {
                     data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "total")
                {
                    total = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    count = CPubVars.GetTextFromDr(dr, "count");
              
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, total,count });
        }

    }
}
