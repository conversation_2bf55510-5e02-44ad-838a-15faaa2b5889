﻿using ArtisanManage.Models;
using ArtisanManage.MyCW;
using ArtisanManage.Services;
using Org.BouncyCastle.Bcpg;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{

    public class SheetRowCostPriceAdjustItem : SheetRowBase
    {
        public SheetRowCostPriceAdjustItem()
        {

        }
        [SaveToDB] [FromFld] public string item_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_name { get; set; } = "";
        [SaveToDB] [FromFld] public string unit_no { get; set; } = "";
        [SaveToDB] [FromFld] public decimal unit_factor { get; set; } = 1;
        [FromFld("yj_get_unit_relation(b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no) as unit_conv", LOAD_PURPOSE.SHOW)] public string unit_conv { get; set; }

        [FromFld("mu.barcode")] public string barcode { get; set; } = "";
        [SaveToDB] [FromFld] public decimal real_price { get; set; } 
        [SaveToDB] [FromFld] public string old_avg_price { get; set; }
        [SaveToDB] [FromFld] public string old_buy_price { get; set; }
        [SaveToDB] [FromFld] public decimal quantity { get; set; }
        [SaveToDB] [FromFld] public decimal sub_amount { get; set; }
    }

    public class SheetRowCostPrice : SheetRowCostPriceAdjustItem
    {
        public decimal cost_price_avg { get; set; }
        internal decimal old_total_qty = 0;
        internal bool item_cost_price_suspect = false;
    }
    public class SheetCostPriceAdjust : SheetBase<SheetRowCostPrice>
    {
        [SaveToDB] [FromFld] public string seller_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";
        [SaveToDB] [FromFld] public bool buy_price_change { get; set; } = false;
        [SaveToDB] [FromFld] public override SHEET_TYPE sheet_type { get; set; }
        [SaveToDB] [FromFld] public override decimal total_amount { get; set; }

        public SheetCostPriceAdjust(LOAD_PURPOSE loadPurpose) : base("sheet_cost_price_adjust_main ", "sheet_cost_price_adjust_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_COST_PRICE_ADJUST;
            if (loadPurpose == LOAD_PURPOSE.SHOW)
            {
                ConstructFun();
            }
            else if (loadPurpose == LOAD_PURPOSE.APPROVE)
                DetailLeftJoin = @"left join sheet_cost_price_adjust_main m on t.sheet_id=m.sheet_id
                                   left join info_item_multi_unit mu on mu.item_id = t.item_id and mu.unit_factor = t.unit_factor
                                  ";
        }
        private void ConstructFun()
        {
            MainLeftJoin = @"left join (select oper_id,oper_name as seller_name,mobile as seller_mobile from info_operator) seller on t.seller_id=seller.oper_id
                                 left join (select oper_id,oper_name as maker_name from info_operator) maker on t.maker_id=maker.oper_id
                                 left join (select oper_id,oper_name as approver_name from info_operator) approver on t.approver_id=approver.oper_id
                            
                                ";
            DetailLeftJoin = @"left join sheet_cost_price_adjust_main m on t.sheet_id=m.sheet_id
                                   left join info_item_prop ip on t.item_id=ip.item_id 
                                   left join info_item_multi_unit mu on mu.item_id = t.item_id and mu.unit_factor = t.unit_factor
                                   left join (select item_id,s_unit->>'f1' as s_unit_no,m_unit->>'f1' as m_unit_no,(m_unit->>'f2')::real as m_unit_factor,
                                                     b_unit->>'f1' as b_unit_no,(b_unit->>'f2')::real as b_unit_factor
                                              from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor)) as json from info_item_multi_unit where company_id=~company_id order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)) unit_barcode on t.item_id=unit_barcode.item_id    
                                   

                ";
        }
        public SheetCostPriceAdjust() : base("sheet_cost_price_adjust_main", "sheet_cost_price_adjust_detail", LOAD_PURPOSE.SHOW)
        {
            ConstructFun();
        }

        protected class CInfoForApprove : CInfoForApproveBase
        {
            public List<SheetRowCostPriceAdjustItem> SheetRows = null;
            public List<SheetRowCostPrice> CostSheetRows = null;
        }

        protected override void InitForSave()
        {
            base.InitForSave();
            if (seller_id == "") seller_id = OperID;
        }

        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql = "";
            base.GetInfoForApprove_SetQQ(QQ);
            if(SheetRows.Count>0)
            {
                string items_id = string.Join(",", SheetRows.Select(r => r.item_id));
                sql = @$"select p.item_id,stock_sum as total_qty,
                    item_cost_price_suspect, cost_price_avg
                    from info_item_prop p 
                    left join 
                    ( select item_id,sum(COALESCE(stock_qty,0)) as stock_sum 
                        from stock where company_id={company_id} and item_id in ({items_id}) group by item_id
                    ) stock on stock.item_id = p.item_id
                    where p.company_id = {company_id} and p.item_id in ({items_id}) order by item_id";
                QQ.Enqueue("stock_sum", sql);
            }

            
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            if (sqlName == "stock_sum")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowCostPrice row in SheetRows)
                    {
                        if (row.item_id == rec.item_id)
                        {
                            row.old_total_qty = CPubVars.ToDecimal(rec.total_qty != "" ? rec.total_qty : 0);
                            row.item_cost_price_suspect = Convert.ToBoolean(rec.item_cost_price_suspect != "" ? rec.item_cost_price_suspect : false);
                            row.cost_price_avg = CPubVars.ToDecimal(rec.cost_price_avg != "" ? rec.cost_price_avg : "0");

                            row.quantity = CPubVars.ToDecimal(rec.total_qty != "" ? rec.total_qty : "0");
                            row.sub_amount = row.quantity * (row.real_price - CPubVars.ToDecimal(row.old_avg_price.IsValid() ? row.old_avg_price : "0"));
                            total_amount += row.sub_amount;
                        }
                    }
                }
                
            }
        }

        protected override async Task<string> CheckSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSheetValid(cmd);
            if (seller_id == "") return "必须指定业务员";
            return "OK";
        }

        protected override string GetApproveSQL(CInfoForApproveBase info)
        {
            return base.GetApproveSQL(info);
        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            await base.OnSheetIDGot(cmd, sheetID, info1);
            CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";

            List<SheetRowCostPrice> costPriceRows = new List<SheetRowCostPrice>();

            foreach (var row in SheetRows)
            {
                SheetRowCostPrice costRow = row;
                if (red_flag != "") costRow.real_price = CPubVars.ToDecimal(row.old_buy_price == ""||row.old_buy_price==null ? "0" : row.old_buy_price);
                 
                costRow.cost_price_avg = row.cost_price_avg;
                costPriceRows.Add(costRow);
                decimal sAvg = row.real_price / row.unit_factor;
                if (buy_price_change)
                {
                    string buyPrice = sAvg.ToString();
                    if (red_flag != "") buyPrice = !row.old_buy_price.IsValid() ? "null": row.old_buy_price; 
                    sql += @$"update sheet_sale_detail set cost_price_buy = {buyPrice} where company_id = {company_id} and item_id = {row.item_id} and happen_time>= '{happen_time}';";
                    sql += @$"update sheet_sale_order_detail set cost_price_buy = {buyPrice} where company_id = {company_id} and item_id = {row.item_id} and happen_time>= '{happen_time}';";
                    sql += @$"update sheet_invent_change_detail set buy_price = {buyPrice} where company_id = {company_id} and item_id = {row.item_id} and happen_time>= '{happen_time}';";
                    sql += @$"update info_item_multi_unit set buy_price = {buyPrice} * unit_factor where company_id = {company_id} and item_id = {row.item_id};";

                }

            }
            
            
            string dealing_happen_time = happen_time;
            
            if (red_flag == "2")
            {
                dynamic data = await CDbDealer.Get1RecordFromSQLAsync($"select happen_time from sheet_cost_price_adjust_main  where company_id = {company_id} and sheet_id = {red_sheet_id}", cmd);
                if (data != null) dealing_happen_time = data.happen_time;

            }
            dealing_happen_time = CPubVars.GetDateTextWithZeroTime(dealing_happen_time);
            await UpdateCostPriceAvg(cmd, costPriceRows, dealing_happen_time);
            

            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
        }

		public override string GetSheetCharactor()
		{
			string res = this.company_id + "_" + this.OperID + "_" ;
			foreach (var row in SheetRows)
			{
				res += row.item_id + "_" + row.quantity;
			}
			return res;
		}


		/*public override async Task<JsonResult> ToVoucherRows(CMySbCommand cmd, string sheetID, SheetCwVoucher sheetCwVoucher, Dictionary<string, decimal> payways)
        {
            decimal changeAmt = 0;
            if (payways.Count == 0)
            {
                if(total_amount!=0) payways.Add("total", total_amount);
            }
            if (payways == null || payways.Count == 0)
            {
                return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
            }
            changeAmt = payways["total"];
            
            string sql = $@"( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='1405' and level=  (select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='1405') order by sub_code limit 1 ) 
                union all 
                ( select sub_id,sub_name,sub_code,direction,status from cw_subject where company_id = {company_id} and substr(sub_code::text,1,4)='1407' and level=(select Max(level) from cw_subject where company_id={company_id} and substr(sub_code::text,1,4)='1407') order by sub_code limit 1 )"; // 库存商品、商品进销差价
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (records == null || records.Count < 2) return new JsonResult(new { result = "Error", msg = "缺少生成凭证的相关科目，请添加" });
            if (records.Count > 2) return new JsonResult(new { result = "Error", msg = "生成凭证的相关科目有重复科目代码，请修改" });

            foreach (var rec in records)
            {
                if (rec.status == null || rec.status == "") rec.status = 1;
                if (Convert.ToInt16(rec.status) == 0) return new JsonResult(new { result = "Error", msg = "相关科目已停用，请检查" });
                CwRowVoucher cwRow = new CwRowVoucher();
                cwRow.business_sheet_type = SheetType;
                cwRow.business_sheet_id = sheetID;
                cwRow.sub_id = rec.sub_id;
                if (changeAmt == 0) break;
                cwRow.remark = changeAmt >= 0 ? "成本调高" : "成本调低";
                    
                if (rec.sub_code.ToString().Substring(0, 4) == "1405") // 调高 借库存商品，贷 商品进销差价
                {
                    if (changeAmt >= 0) cwRow.debit_amount = changeAmt.ToString();
                    else cwRow.credit_amount = Math.Abs(changeAmt).ToString();
                    cwRow.change_amount = changeAmt.ToString();
                }
                if (rec.sub_code.ToString().Substring(0, 4) == "1407")
                {
                    if (changeAmt >= 0) cwRow.credit_amount = changeAmt.ToString();
                    else cwRow.debit_amount = Math.Abs(changeAmt).ToString();
                    cwRow.change_amount = (-1 * changeAmt).ToString();
                }
                sheetCwVoucher.SheetRows.Add(cwRow);
            }

            return new JsonResult(new { result = "OK", msg = "", sheetCwVoucher });
        }*/

	}
}
