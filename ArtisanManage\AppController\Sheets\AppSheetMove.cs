﻿using ArtisanManage.Models;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using myJXC;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.YingjiangMessage.Pojo;
using ArtisanManage.YingjiangMessage.Services;
using System.IO;
using static ArtisanManage.AppController.VisitStrategyController;


namespace ArtisanManage.AppController.Sheets
{ 
    [Route("AppApi/[controller]/[action]")]
    public class AppSheetMove : BaseController
    { 
        public AppSheetMove(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }



        /// <summary>
        /// 加载单据--返回--支付方式,备注
        /// </summary>
        /// <param name="operKey"> </param>
        /// <param name="sheetID"></param>
        /// <returns></returns>


        [HttpGet]
        public async Task<JsonResult> Load(string operKey, string sheetID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SheetMove sheet = new SheetMove(LOAD_PURPOSE.SHOW);
            await sheet.Load(cmd, companyID, sheetID);
            var sql = $"select sub_id,sub_name from cw_subject where company_id = {companyID} and sub_type in ('QT') order by order_index;";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("subInfo", sql);
            sql = $@"select brief_id,brief_text from info_sheet_detail_brief where company_id = {companyID} and sheet_type ='DB'; ";
            QQ.Enqueue("briefInfo", sql);
            sql = @$"SELECT opt_id, opt_name, attr.attr_id FROM info_attr_opt opt left join info_attribute attr on opt.attr_id = attr.attr_id where opt.company_id ={ companyID} and not attr.spec_opt_in_item order by opt.order_index
                   ";
            QQ.Enqueue("attr_options", sql);
            List<ExpandoObject> payways = null;
            List<ExpandoObject> brief = null;
            List<ExpandoObject> attrOptions = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "subInfo")
                {
                    payways = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "briefInfo")
                {
                    brief = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "attr_options")
                {
                    attrOptions = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }

            string result = "OK";
            string msg = "";
            return Json(new { result, msg, sheet, payways, brief, attrOptions });
        }


        /// <summary>
        /// 提交调拨单
        /// </summary>
        /// <param name="sheet">
        /// {"operKey":"wcAqiAdqGYG39sTafoxzNuV7gjl0d-zEX5Q5vIEsZ4CJBL8L71cPvCkNmSBpbvSukmnIwUZFvIg~","sheet_no":"","sheet_id":"","sheettype":"DB","from_branch_id":1,"to_branch_id":7,
        /// "happen_time":"","make_brief":"","total_amount":"72","now_disc_amount":"0","payway1_id":"","payway1_name":"","payway1_amount":"72","payway2_id":"","payway2_name":"","payway2_amount":"0",
        /// "maker_name":"","make_time":"","approver_id":"", "approver_name":"","approve_time":"",
        /// "SheetRows":[ {"item_id":"6","item_name":"你好6","unit_no":"箱","wholesale_price":"9","quantity":"10","unit_factor":"8","remark":"1"},
        /// {"item_id":"18","item_name":"你还有","unit_no":"包","wholesale_price":"48","quantity":"10","unit_factor":"1","remark":"1"}]} </param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Submit([FromBody] dynamic dSheet)
        {
            string operKey = dSheet.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetMove sheet = null;
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            var currentTime = DateTime.Now.ToText();
            string result;
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetMove>(sSheet);
            }
            catch (Exception e)
            {
                msg = e.Message;

                MyLogger.LogMsg("in AppSheetMove.Submit:" + msg, companyID);

                msg = "提交失败," + msg;
                return new JsonResult(new { result = "Error", msg });
            }
            string from_branch_name = dSheet.from_branch_name;
            string to_branch_name = dSheet.to_branch_name;
            string sheetNo = sheet.sheet_no;
            sheet.Init();
            if (sheet.isRedAndChange)
            {
                if (!sheet.old_sheet_id.IsValid())
                {
                    msg = "冲改时没有获取原单据的编号";
                }
                else
                {
                    msg = await sheet.RedAndChange<SheetMove>(cmd);
                }
            }
            else
            {
                msg = await sheet.SaveAndApprove(cmd);
            }
            string receiverId = sheet.maker_id;
            if (msg == "")
            {
                string  sheetId = sheet.sheet_id;
                //  更新消息
                Dictionary<string, dynamic> messageResult = await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey, 
                    msgId = "", 
                    sheetID = sheetId,
                    msgClass = MessageType.ClassType.Todo,
                    msgType = MessageType.MoveSheetMessageType.MessageType,
                    msgSubType = MessageType.MoveSheetMessageType.MessageSubType.MoveSheetApproveSubType.SubTypeKey
                }, cmd);
                // if (!messageResult.ContainsKey("errMessage"))
                // {
                //     // 通知到当时的创建单据的业务员
                //     await MessageCreateServices.CreateMessageService(new
                //     {
                //         operKey,
                //         createrId = operID,
                //         msgClass = MessageType.ClassType.Notice,
                //         msgType = MessageType.NoticeMessageType.CommonNotice.NoticeType,
                //         msgSubType =MessageType.NoticeMessageType.CommonNotice.NoticeSubType.CommonNoticeSubType.SubTypeKey,
                //         receiverId,
                //         msgTitle = @$"{from_branch_name} 到 {to_branch_name} 调拨申请已通过。 单号: {sheetNo}",
                //     }, cmd);
                // }
            }

            result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no,sheet.approve_time, currentTime });
        }

        [HttpPost]
        //public async Task<JsonResult> Save([FromBody] SheetMove sheet)
        public async Task<JsonResult> Save([FromBody] dynamic dSheet)
        {
            string operKey = dSheet.operKey;
            string maker_name = dSheet.maker_name;
            string from_branch_name = dSheet.from_branch_name;
            string from_branch_id = dSheet.from_branch_id;
            string to_branch_name = dSheet.to_branch_name;
            string to_branch_id = dSheet.to_branch_id;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            string sSheet = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            SheetMove sheet = null;
            var currentTime = DateTime.Now.ToText();
            string msg = "";
            try
            {
                sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetMove>(sSheet);
                sheet.Init();
                msg = await sheet.Save(cmd);
            }
            catch(Exception e)
            {
                msg = e.Message;
                MyLogger.LogMsg("in AppSheetMove.save:" + msg, companyID); 
                msg = "保存失败," + msg;
                return new JsonResult(new { result="Error", msg});
            }
            //更新消息的方法移动到了sheetmove.cs的onsheetsaved
            /*if (msg == "" && false)
            {
                string sheetNo = sheet.sheet_no;
                string sheetId = sheet.sheet_id;
                await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey, 
                    msgId = "", 
                    sheetID = sheetId,
                    msgClass = MessageType.ClassType.Todo,
                    msgType = MessageType.MoveSheetMessageType.MessageType,
                    msgSubType = MessageType.MoveSheetMessageType.MessageSubType.MoveSheetApproveSubType.SubTypeKey
                }, cmd);
                await MessageCreateServices.CreateMessageService(new
                {
                    operKey,
                    createrId = operID,
                    msgClass = MessageType.ClassType.Todo,
                    msgType = MessageType.MoveSheetMessageType.MessageType,
                    msgSubType = MessageType.MoveSheetMessageType.MessageSubType.MoveSheetApproveSubType.SubTypeKey,
                    receiverId = "",
                    sheetID = sheetId,
                    from_branch_id,to_branch_id,
                    msgTitle = @$"{maker_name} 申请 {from_branch_name} 到 {to_branch_name} 调拨。 单号: {sheetNo}， 请尽快处理",
                }, cmd);
            }*/
            string result = msg == "" ? "OK" : "Error";
            return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.make_time, currentTime });
        }

     
        [HttpPost]
        public async Task<JsonResult> Red([FromBody] dynamic data)
        {
            string result = "OK"; string msg = null;
            string operKey = data.operKey;
            string sheetID = data.sheetID;
            try
            {
                var currentTime = DateTime.Now.ToText();
                Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
                SheetMove sheet = new SheetMove(LOAD_PURPOSE.SHOW);
                msg = await sheet.Red(cmd, companyID, sheetID, operID,"");
                if (msg != "") result = "Error";
                return new JsonResult(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time, currentTime });
            }
            catch (Exception e)
            {
                result = "Error";
                msg = e.Message;
                return new JsonResult(new { result, msg });
            }
        }

        [HttpGet]
        public async Task<JsonResult> GetFillItemsFromSale(string operKey,string timeRange, string fromBanchID, string toBranchID,string branchPositions, bool isFromReturn, bool forDisplayItems,string startTime,string endTime,string? classes,string? brandsID)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string result = "OK";
            string msg = "";
            string branchID;
            string branchFld; 
            string condi;
            string condi2="";
            var searchTime = "";
            string condi3 = "";
            string condi4 = "";
            if (isFromReturn)
            {
                branchID = fromBanchID;                
                branchFld = "from_branch_id";
                condi = " and (sheet_type='T' or quantity<0) ";
                condi3 = "sd.branch_position as from_branch_position,COALESCE(branch_position_name,'') as from_branch_position_name ";
                condi4 = "from_branch_position,from_branch_position_name, ";
            }
            else
            {
                branchID = toBranchID; 
                branchFld = "to_branch_id";
                condi = " and (sheet_type='X' and quantity>0) ";
                condi3 = "sd.branch_position as to_branch_position,COALESCE(branch_position_name,'') as to_branch_position_name ";
                 condi4 = "to_branch_position,to_branch_position_name, ";
            }
            if (forDisplayItems)
            {
                condi += " and (sd.remark like '%陈列%') ";
            }
            if (branchPositions != "" && branchPositions != null)
            {
               condi += $@" and sd.branch_position in ({branchPositions}) ";
            }
            string sinceTime = "";
            if (startTime.IsValid() && endTime.IsValid())
            {
                searchTime = $" sd.happen_time >= '{startTime}' and sd.happen_time<='{endTime} 23:59:59' and sm.happen_time >= '{startTime}' and sm.happen_time<='{endTime} 23:59:59'"; 
            }
            else if (timeRange == "today")
            {
                sinceTime = CPubVars.GetDateText(DateTime.Now.Date.ToString("yyyy-MM-dd 00:00"));
                searchTime = $" sd.happen_time > '{sinceTime}' ";
            }
            else if (timeRange == "yesterday")
            {
                sinceTime = CPubVars.GetDateText(DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd 00:00"));
                searchTime = $" sd.happen_time > '{sinceTime}' ";
            }
            else if (timeRange == "sinceLastMove")
            {
                string timeLimit = CPubVars.GetDateText(DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd 00:00"));
                cmd.CommandText = $"select to_char(max(happen_time),'YYYY-MM-DD HH24:MI:SS') from sheet_move_main where {branchFld}={branchID} and company_id={companyID} and happen_time>'{timeLimit}'";
                object ov = await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value)
                {
                    sinceTime = ov.ToString();
                    searchTime = $" sd.happen_time > '{sinceTime}' ";
                }
                else msg = "7日之内没有做调拨单";
            }
            if (!String.IsNullOrEmpty(classes))
            {
                condi2 = $" and other_class ilike '%{classes}%' ";
            }
            if (!String.IsNullOrEmpty(brandsID))
            {
                condi2 = $" and item_brand in({brandsID}) ";
            }

            List<ExpandoObject> data = null;
            if (msg == "")
            {
                SQLQueue QQ = new SQLQueue(cmd);
                var sql = @$"
select 	tb.item_id,ip.item_name,ip.item_class as classId,s_qty,from_stock_qty,to_stock_qty,batch_no,produce_date,batch_id,{condi4}
        s->>'f1' as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_wholesale_price,s->>'f4' as s_barcode,
        m->>'f1' as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_wholesale_price,m->>'f4' as m_barcode,
        b->>'f1' as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_wholesale_price,b->>'f4' as b_barcode,
        s->>'f5' as s_contract_price,
        m->>'f5' as m_contract_price,
        b->>'f5' as b_contract_price
from
(
   select sd.item_id,sum(abs(sd.quantity * sd.unit_factor)) as s_qty,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date,sd.batch_id,{condi3} from sheet_sale_detail sd 
   left join sheet_sale_main sm on sd.sheet_id=sm.sheet_id and sd.company_id=sm.company_id
left join info_item_batch itb on itb.batch_id = sd.batch_id and itb.company_id = {companyID}
left join info_branch_position ibp on ibp.branch_id = coalesce(sd.branch_id,sm.branch_id) and ibp.branch_position = sd.branch_position
   where sd.company_id={companyID} {condi} and approve_time is not null and red_flag is null and (sm.branch_id={branchID} or sd.branch_id = {branchID}) and {searchTime} group by sd.item_id,sd.batch_id,itb.batch_no,itb.produce_date,sd.branch_position,branch_position_name
) tb
left join info_item_prop ip on ip.company_id={companyID} and tb.item_id=ip.item_id
     left join (
select case when son_mum_item is null then s.item_id else son_mum_item end item_id,branch_id as t_branch_id,sum(stock_qty - COALESCE(sell_pend_qty,0)) as to_stock_qty from stock s
        LEFT JOIN info_item_prop ip on ip.company_id ={companyID} and s.item_id = ip.item_id
         where s.company_id = {companyID} and branch_id = {toBranchID}
         GROUP BY case when son_mum_item is null then s.item_id else son_mum_item end,branch_id

) as tstock on ip.item_id = tstock.item_id 
         left join (
select case when son_mum_item is null then s.item_id else son_mum_item end item_id,branch_id as t_branch_id,sum(stock_qty - COALESCE(sell_pend_qty,0)) as from_stock_qty from stock s
        LEFT JOIN info_item_prop ip on ip.company_id ={companyID} and s.item_id = ip.item_id
         where s.company_id = {companyID} and branch_id = {fromBanchID}
         GROUP BY case when son_mum_item is null then s.item_id else son_mum_item end,branch_id

) as fstock on ip.item_id = fstock.item_id 
    
left join (select item_id,s,m,b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,barcode,contract_price)) as json from info_item_multi_unit 
                                    where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)) unit on ip.item_id=unit.item_id
where company_id = {companyID}{condi2}  
ORDER BY item_class::text desc ,item_id desc

            ";

                QQ.Enqueue("data", sql);
                
                var dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    var sqlName = QQ.Dequeue();
                    if (sqlName == "data")
                    {
                        data = CDbDealer.GetRecordsFromDr(dr, false);
                    }
                }
                QQ.Clear();
            }
            else result = "Error";
            return Json(new { result, msg, data });            
        }
        [HttpGet]
        public async Task<JsonResult> GetFillItemsFromStock(string operKey, string fromBanchID, string toBranchID,string fromBranchPosition,string toBranchPosition)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string msg = "";
            string fromCndi = "";
            string toCondi = "";
            if (fromBranchPosition != "" && fromBranchPosition !=null)
            {
                fromCndi += $@" and s.branch_position in ({fromBranchPosition}) ";
            }
            if (toBranchPosition != "" && toBranchPosition != null)
            {
                toCondi += $@" and s.branch_position in ({toBranchPosition}) ";
            }

            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"
                select
                    tb.item_id,ip.item_name,ip.item_class as classId,s_qty,s->>'f1' as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_wholesale_price, s->>'f4' as s_barcode,from_stock_qty,to_stock_qty,
                    COALESCE(from_branch_position,0) as from_branch_position ,from_branch_position_name, COALESCE(to_branch_position,0) as to_branch_position,to_branch_position_name,batch_no,produce_date,tb.batch_id,
                    b->>'f1' as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_wholesale_price,b->>'f4' as b_barcode,
                    m->>'f1' as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_wholesale_price,m->>'f4' as m_barcode
                from
                    (
                        select
                            item_id,stock_qty as s_qty,s.batch_id,branch_position,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date
                        from
                            stock s
                            left join info_item_batch itb on itb.company_id = {companyID} and itb.batch_id = s.batch_id
                        where branch_id={fromBanchID} and stock_qty>0 {fromCndi}
                    ) tb
                    left join info_item_prop ip on tb.item_id=ip.item_id 
                    left join (
                        select
                            case when son_mum_item is null then s.item_id else son_mum_item end item_id,s.branch_id as t_branch_id,batch_id,s.branch_position as to_branch_position,COALESCE(branch_position_name,'') as to_branch_position_name,COALESCE(stock_qty,0) - COALESCE(sell_pend_qty,0) as to_stock_qty
                        from
                            stock s
                            LEFT JOIN info_item_prop ip on ip.company_id ={companyID} and s.item_id = ip.item_id
                            left join info_branch_position ibp on ibp.branch_id = s.branch_id and ibp.branch_position = s.branch_position
                        where s.company_id = {companyID} and s.branch_id = {toBranchID} {toCondi}
                    ) as tstock on ip.item_id = tstock.item_id and tstock.batch_id = tb.batch_id and tstock.to_branch_position = tb.branch_position
                    left join (
                        select
                            case when son_mum_item is null then s.item_id else son_mum_item end item_id,s.branch_id as t_branch_id,batch_id,s.branch_position as from_branch_position,COALESCE(branch_position_name,'') as from_branch_position_name,COALESCE(stock_qty,0) - COALESCE(sell_pend_qty,0) as from_stock_qty
                        from
                            stock s
                            LEFT JOIN info_item_prop ip on {companyID} = ip.company_id and s.item_id = ip.item_id
                            left join info_branch_position ibp on ibp.branch_id = s.branch_id and ibp.branch_position = s.branch_position 
                         where s.company_id = {companyID} and s.branch_id = {fromBanchID} {fromCndi}
                    ) as fstock on ip.item_id = fstock.item_id  and fstock.batch_id = tb.batch_id and fstock.from_branch_position = tb.branch_position
                    left join (
                        select
                            item_id,s,m,b from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,barcode)) as json
                        from
                            info_item_multi_unit 
                        where company_id={companyID} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb, b jsonb)
                    ) unit on ip.item_id=unit.item_id
                where not ip.item_name is null
            ";

            QQ.Enqueue("data", sql);
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            QQ.Clear();
            string result = "OK";
            if (msg != "") result = "Error";
            return Json(new { result, msg, data });
        }

        /// <summary>
        /// 调拨款商品库存
        /// </summary>
        ///<param name="operKey"></param>
        /// <param name="searchStr">商品名，助记码，商品编号，商品条码 模糊查询</param>
        /// <param name="brandID">品牌ID查询</param>
        /// <param name="classID">分类ID查询</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <param name="fromBranchID"></param>
        /// <param name="toBranchID"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetItemList(string operKey, string searchStr,string sortType, string brandIDs, string classID, int pageSize, int startRow, string fromBranchID,string toBranchID, bool showStockOnly,bool isContractSeller)
        {
            string showStockOnlyCondi = "";
            bool firstRequest = false;
            string supcustID = "-1";
            Security.GetInfoFromOperKey(operKey, out string companyID);
             dynamic supId = null;
             // 这里增加一个对出仓是特殊仓库的判断
            if (fromBranchID.IsValid())
            {
				string sup_sql = $@"select relate_client from info_branch where company_id = {companyID} and branch_id = {fromBranchID};";
			    supId = await CDbDealer.Get1RecordFromSQLAsync(sup_sql, cmd);
				
				if (supId != null && supId.relate_client != "")
				{
					supcustID = supId.relate_client;
				}
			}
            // 以入仓为首要仓库，如果都为特殊仓库以入仓为准
			if (toBranchID.IsValid())
            {
				string sup_sql = $@"select relate_client from info_branch where company_id = {companyID} and branch_id = {toBranchID};";
			    supId = await CDbDealer.Get1RecordFromSQLAsync(sup_sql, cmd);
				
				if (supId != null && supId.relate_client != "")
				{
					supcustID = supId.relate_client;
				}
			}
            // string condi = $" where ip.company_id = {companyID} and (ip.status is null or ip.status='1') and (mum_attributes is null or mum_attributes::text not like '%\"distinctStock\": true%') ";
            
            string condi = $" where ip.company_id = {companyID} and (ip.status is null or ip.status='1') and son_mum_item is NULL ";
            string sortSQL = "";
            if (sortType == "item_name")
            {
                sortSQL = "ip.py_str asc";
            }
            else if (sortType == "order_index")
            {
                sortSQL = " ip.item_order_index asc";
            }
            else
            {
                sortSQL = "item_order_index,ip.item_id desc";
            }

            if (searchStr.IsValid())
            {
                string b = "%";
                if (searchStr.Length >= 6) b = "";
                string flexStr = CPubVars.GetFlexLikeStr(searchStr);
                condi += $" and (ip.item_name ilike '%{flexStr}%' or ip.py_str ilike '%{searchStr}%' or ip.py_str1 ilike '%{searchStr}%' or ip.item_no ilike '%{searchStr}%' or s_barcode like '%{searchStr}{b}' or b_barcode like '%{searchStr}{b}' or m_barcode like '%{searchStr}{b}' or ip.mum_attributes::text ilike '%{searchStr}%')";
            }

            string ORDER_BY = "order by ";
            bool bSearchStrInClass = false;
            if (classID != "0" && classID != "-1" && classID != "borrowItemClass" && classID != "orderItemClass" && classID != "displayItemClass" && !string.IsNullOrEmpty(searchStr) && !string.IsNullOrEmpty(classID))
            {
                bSearchStrInClass = true;
                ORDER_BY += $" case when ip.other_class like '%/{classID}/%' then 0 else 1 end, ";
            }
            ORDER_BY += @$" {sortSQL} ";

            if (brandIDs != null && brandIDs != "") condi += $"and (ip.item_brand is null OR ip.item_brand in ({brandIDs})) ";
            if (!bSearchStrInClass && classID != null && classID != "-1" && classID != "0" && classID != "orderItemClass" && classID != "borrowItemClass") condi += $" and ip.other_class like '%/{classID}/%' ";
           
            if (showStockOnly) condi += $" and fstock.from_stock_qty>0 ";
            if (showStockOnly) showStockOnlyCondi += $" and stock_qty>0 ";
            if (startRow == 0) firstRequest = true;
            SQLQueue QQ = new SQLQueue(cmd);
            var sql_noLimit = @$"
select ip.item_id,ip.item_name,ip.mum_attributes,other_class,ip.son_mum_item,barcode,ip.item_class as class_id,f_branch_id,t_branch_id,from_stock_qty,to_stock_qty,(t.b_unit_no) as bUnit, ip.item_images,ip.batch_level,
						 (t.m_unit_no) as mUnit,
                         t.s_unit_no as sUnit,t.s_unit_factor as sFactor,
                         t.m_unit_factor as mFactor,t.b_unit_factor as bFactor,s_wholesale_price,m_wholesale_price,b_wholesale_price,
                         s_weight, m_weight,b_weight,
                         s_contract_price,m_contract_price,b_contract_price,
                         t.s_barcode,t.m_barcode,t.b_barcode,s_retail_price,m_retail_price,b_retail_price,s_recent_price,m_recent_price,b_recent_price,s_buy_price,m_buy_price,b_buy_price,s_recent_orig_price,m_recent_orig_price,b_recent_orig_price,s_lowest_price,
                         m_lowest_price,b_lowest_price,b_cost_price_spec,s_cost_price_spec,m_cost_price_spec
FROM 
 (select case when son_mum_item is null then s.item_id else son_mum_item end item_id,branch_id as f_branch_id,sum(stock_qty - COALESCE(sell_pend_qty,0)) as from_stock_qty from stock s
        LEFT JOIN info_item_prop ip on {companyID} = ip.company_id and s.item_id = ip.item_id
         where s.company_id = {companyID} and branch_id = {fromBranchID} {showStockOnlyCondi}
         GROUP BY case when son_mum_item is null then s.item_id else son_mum_item end,branch_id
) as fstock 
right join info_item_prop as ip  on fstock.item_id = ip.item_id
left join
(
    select item_id,s->>'f1' as s_unit_no,s->>'f2' as s_unit_factor,s->>'f3' as s_wholesale_price,s->>'f4' as s_retail_price,s->>'f5' as s_contract_price,s->>'f6' as s_barcode,s ->> 'f7' as s_weight,s ->> 'f8' as s_recent_price,b ->> 'f9' as s_buy_price,s ->> 'f10' as s_recent_orig_price,s ->> 'f11' as s_lowest_price,s ->> 'f12' as s_cost_price_spec,   
                   m->>'f1' as m_unit_no,m->>'f2' as m_unit_factor,m->>'f3' as m_wholesale_price,m->>'f4' as m_retail_price,m->>'f5' as m_contract_price,m->>'f6' as m_barcode,m ->> 'f7' as m_weight,b ->> 'f8' as b_recent_price,b ->> 'f9' as b_buy_price,b ->> 'f10' as b_recent_orig_price,b ->> 'f11' as b_lowest_price,b ->> 'f12' as b_cost_price_spec, 
                   b->>'f1' as b_unit_no,b->>'f2' as b_unit_factor,b->>'f3' as b_wholesale_price,b->>'f4' as b_retail_price,b->>'f5' as b_contract_price,b->>'f6' as b_barcode,b ->> 'f7' as b_weight,m ->> 'f8' as m_recent_price,b ->> 'f9' as m_buy_price,m ->> 'f10' as m_recent_orig_price,m ->> 'f11' as m_lowest_price,m ->> 'f12' as m_cost_price_spec
    from crosstab('select iimu.item_id,iimu.unit_type,row_to_json(row (iimu.unit_no,unit_factor,wholesale_price,iimu.retail_price,contract_price,barcode,weight, recent_price, buy_price, recent_orig_price, lowest_price, cost_price_spec)) as json from info_item_multi_unit iimu
         left join(select item_id, unit_no,recent_price,recent_orig_price from client_recent_prices where supcust_id ={supcustID}) crp on iimu.item_id = crp.item_id and iimu.unit_no = crp.unit_no
    where iimu.company_id = {companyID} order by iimu.item_id',$$values ('s'::text),('m'::text),('b'::text)$$)  as errr(item_id int, s jsonb,m jsonb, b jsonb)
) t on ip.item_id=t.item_id
 left join (
select case when son_mum_item is null then s.item_id else son_mum_item end item_id,branch_id as t_branch_id,sum(stock_qty - COALESCE(sell_pend_qty,0)) as to_stock_qty from stock s
        LEFT JOIN info_item_prop ip on {companyID} = ip.company_id and s.item_id = ip.item_id
         where s.company_id = {companyID} and branch_id = {toBranchID}
         GROUP BY case when son_mum_item is null then s.item_id else son_mum_item end,branch_id

) as tstock on ip.item_id = tstock.item_id 
LEFT JOIN (select * from info_item_class where company_id = {companyID}
) ic on ip.item_class = ic.class_id {condi} {ORDER_BY}  " ;          

            var sql = sql_noLimit + $" limit {pageSize} offset {startRow};";

            QQ.Enqueue("data", sql);
            if(fromBranchID.IsValid())
            {
                sql = $@"select relate_client from info_branch where company_id = {companyID} and branch_id = {fromBranchID};";
                QQ.Enqueue("from_relate_client", sql);
            }
            if(toBranchID.IsValid())
            {
                sql = $@"select relate_client from info_branch where company_id = {companyID} and branch_id = {toBranchID};";
                QQ.Enqueue("to_relate_client", sql);
            }
            if (firstRequest)
            {
                sql = $"select count(*) as itemCount from ({sql_noLimit}) tt";
                QQ.Enqueue("count", sql);
            }
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            var itemCount = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count"&&firstRequest)
                {
                    dr.Read();
                    itemCount = CPubVars.GetTextFromDr(dr, "itemCount");
                }
                else if(sqlName == "from_relate_client")
                {
                    dynamic records = CDbDealer.GetRecordsFromDr(dr, false);
                    if(records.Count > 0 && records[0].relate_client != "")
                    {
                        supcustID  = records[0].relate_client;
                    }
                }
                else if(sqlName == "to_relate_client")
                {
                    dynamic records = CDbDealer.GetRecordsFromDr(dr, false);
                    if(records.Count > 0 && records[0].relate_client != "")
                    {
                        supcustID  = records[0].relate_client;
                    }
                }
            }
            QQ.Clear();
            // 处理在某类下搜索商品
            if (bSearchStrInClass)
            {
                bool foundInClass = false;
                foreach (dynamic item in data)
                {
                    string other_class = item.other_class;
                    if (other_class.Contains("/" + classID + "/"))
                    {
                        foundInClass = true;
                    }
                    else
                    {
                        item.beforeInfo2 = "其他类";
                        if (foundInClass)
                        {
                        }
                        else
                        {
                            item.beforeInfo1 = "该类下木有哦~";
                        }

                        break;
                    }
                }
            }
            #region 获取价格策略
            bool doPricePlan = false;
            Dictionary<string, string> planIDs = new Dictionary<string, string>();
            if (supcustID != "-1" && supcustID != "" && supcustID != "0" && data.Count > 0)
            {
                string supSql = $"select region_id,other_region,sup_rank,sup_group from info_supcust where company_id = {companyID} and supcust_id={supcustID}";
                dynamic supInfo = await CDbDealer.Get1RecordFromSQLAsync(supSql, cmd);
                var groupID = supInfo.sup_group == "" ? "null" : supInfo.sup_group;
                var otherRegion = supInfo.other_region;
                var rankID = supInfo.sup_rank == "" ? "null" : supInfo.sup_rank;
                string planSql = $@"
  select null flow_id,      supcust_id,null group_id,null region_id,null rank_id,price1,price2,price3 from price_strategy_client where company_id = {companyID} and supcust_id={supcustID} and coalesce(order_source,'all')!='xcx'
  union
  select      flow_id, null supcust_id,     group_id,     region_id,     rank_id,price1,price2,price3 from price_strategy_class  where company_id={companyID} and (position(concat('/',region_id,'/') in '{otherRegion}')>0   or region_id is null) and (group_id::text = '{groupID}' or group_id is null) and (rank_id is null or rank_id::text = '{rankID}') and coalesce(order_source,'all')!='xcx'   order by supcust_id,flow_id desc";
                List<ExpandoObject> supPlans = await CDbDealer.GetRecordsFromSQLAsync(planSql, cmd);
                if (supPlans.Count > 0)
                {
                    foreach (dynamic plan in supPlans)
                    {
                        if (plan.supcust_id != "") doPricePlan = true;
                        else if (plan.supcust_id == "" && plan.flow_id != "") doPricePlan = true;

                        if (doPricePlan)
                        {
                            if (plan.price1 != "") planIDs.Add("1", plan.price1);
                            if (plan.price2 != "") planIDs.Add("2", plan.price2);
                            if (plan.price3 != "") planIDs.Add("3", plan.price3);
                            break;
                        }
                    }
                }
                else
                {
                    doPricePlan = true;
                    planIDs.Add("1", "recent");
                    if (isContractSeller)
						planIDs.Add("2", "contract");
                    else
                        planIDs.Add("2", "wholesale");
                }
                
            }
            #endregion
            var itemIDs = string.Join(",", data.Select(d => (d as dynamic).item_id));
            #region 使用价格策略
            if (doPricePlan && planIDs.Count > 0)
            {
                List<ExpandoObject> plans = null;

                #region 获取所有商品的所有方案
                var fld = ""; var orderCondi1 = "";
                string priceSql = "";
                foreach (var p in planIDs)
                {
                    if (priceSql != "") priceSql += " union ";
                    if (p.Key != null && p.Key != "")
                    {
                        fld = $"{p.Key} as priority,'{p.Value}' plan_id,";
                        orderCondi1 = $" order by priority,price_item ";
                    }

                    priceSql += @$"
select p.item_id,p.other_class,pi.item_id price_item,{fld}plan_name,pi.class_id,s_price,m_price,b_price,class_discount,item_discount
from info_item_prop p 
left join 
(
    select null item_id,null s_price,null m_price,null b_price,     class_id,discount class_discount,null     item_discount from price_plan_class where company_id = {companyID} and plan_id::text = '{p.Value}' and class_id is not null
    union
    select      item_id,     s_price,     m_price,     b_price,null class_id,    null class_discount,discount item_discount from price_plan_item  where company_id = {companyID} and plan_id::text = '{p.Value}' and item_id in ({itemIDs})
) 
pi on (pi.item_id = p.item_id or pi.item_id is null) and (position(concat('/',pi.class_id,'/') in other_class)>0 or pi.class_id is null)
left join price_plan_main m on m.plan_id::text = '{p.Value}'
where p.company_id = {companyID} and p.item_id in ({itemIDs}) ";

                }

                priceSql += orderCondi1;
                plans = await CDbDealer.GetRecordsFromSQLAsync(priceSql, cmd);
                #endregion

                #region 修改结果集  { priority, { plan_id, item_id, priceSetting{price_item,class_id.....}}}
                Dictionary<string, dynamic> prices = new Dictionary<string, dynamic>();
                foreach (dynamic plan in plans)
                {
                    dynamic pr = null;
                    if (prices.ContainsKey(plan.priority + '_' + plan.item_id)) pr = prices[plan.priority + '_' + plan.item_id];
                    else
                    {
                        pr = new ExpandoObject();
                        pr.priority = plan.priority;
                        pr.plan_id = plan.plan_id;
                        pr.plan_name = plan.plan_name;
                        pr.item_id = plan.item_id;
                        pr.other_class = plan.other_class;
                        prices.Add((string)plan.priority + '_' + plan.item_id, pr);
                        pr.priceSetting = new List<dynamic>();//priceSetting 可能会包含一个类别设定和品项设定
                    }
                    List<dynamic> prSetting = pr.priceSetting;
                    if (plan.price_item != "" || plan.class_id != "") prSetting.Add(new { plan.price_item, plan.class_id, plan.class_discount, plan.item_discount, plan.s_price, plan.m_price, plan.b_price });
                }
                #endregion

                #region 调价单的影响 --- 在使用价格策略时，需要多进行一次比较
                // 对于首选方案为最近售价的价格策略，需要考虑调价单的影响
                dynamic adjustItems = null;
                if (planIDs.Count > 1 && planIDs["1"] == "recent") // 当 priority为1 && plan = "recent_price"时，
                {
                    string adjustPlan = planIDs["2"];
                    if (planIDs["2"] == "wholesale") adjustPlan = "-1";
                    if (planIDs["2"] == "retail") adjustPlan = "0";
                    //需要比较 client_recent_prices 表中 happen_time 与 sheet_price_adjust_detail 中 happen_time 的时间早晚
                    string selAdjustSql = @$"select string_agg(distinct d.item_id::text,',') items_id from sheet_price_adjust_detail d 
                                    left join sheet_price_adjust_main m on m.sheet_id = d.sheet_id 
                                    left join (select item_id,max(happen_time) happen_time,supcust_id from client_recent_prices where company_id={companyID} GROUP BY item_id,supcust_id ) r on r.item_id = d.item_id
                                    where m.company_id = {companyID} and supcust_id = {supcustID} and  m.red_flag is null and m.approve_time is not null and m.plans_id like '%{adjustPlan}%' and m.happen_time>r.happen_time and d.item_id in ({itemIDs})
                ";
                    adjustItems = await CDbDealer.Get1RecordFromSQLAsync(selAdjustSql, cmd); // 找出需要使用 新价格的 商品
                }
                #endregion

                #region 遍历商品集 给每个商品添加 大中小价格 setPrice=true，说明已经取到价格，getPlanPrice=true 表示data中添加价格方案价格
                foreach (dynamic unit in data)
                {
                    var s_price = ""; var m_price = ""; var b_price = "";
                    bool setPrice = false;
                    bool bOrigPriceDiffWithPrice = false;
                    unit.s_plan_price = "";
                    unit.m_plan_price = "";
                    unit.b_plan_price = "";
                    unit.plan_id = "";
                    unit.plan_name = "";
                    unit.unitPrice = new Dictionary<string, dynamic>();
                    foreach (var p in prices)
                    {
                        if (unit.item_id == p.Value.item_id)
                        {
                            var plan = p.Value.plan_id;
                            var name = unit.item_name;
                            Boolean state = false;
                            if (adjustItems != null) state = adjustItems.items_id.Contains(unit.item_id);
                            if (plan == "") continue;
                            else if (plan == "wholesale" && !setPrice)
                            {
                                s_price = unit.s_wholesale_price;
                                m_price = unit.m_wholesale_price;
                                b_price = unit.b_wholesale_price;
                                unit.plan_name = "批发";
                                if (s_price != "" || m_price != "" || b_price != "") setPrice = true;
                            }
							else if (plan == "contract" && !setPrice)
							{
								s_price = unit.s_contract_price;
								m_price = unit.m_contract_price;
								b_price = unit.b_contract_price;
								unit.plan_name = "承包";
								if (s_price != "" || m_price != "" || b_price != "") setPrice = true;
							}
							else if (plan == "recent" && !setPrice)
                            {
                                if (p.Value.priority == "1" && adjustItems != null && adjustItems.items_id.Contains(unit.item_id)) // 如果需要改价，则取第二次的方案价格
                                    continue;
                                s_price = unit.s_recent_price;
                                m_price = unit.m_recent_price;
                                b_price = unit.b_recent_price;
                                if (s_price != "" || m_price != "" || b_price != "")
                                {
                                    unit.b_orig_price = unit.b_recent_orig_price;
                                    unit.m_orig_price = unit.m_recent_orig_price;
                                    unit.s_orig_price = unit.s_recent_orig_price;
                                    setPrice = true;
                                    bOrigPriceDiffWithPrice = true;
                                }
                                unit.plan_name = "上次";
                            }
                            else if (plan == "retail" && !setPrice)
                            {
                                s_price = unit.s_retail_price;
                                m_price = unit.m_retail_price;
                                b_price = unit.b_retail_price;
                                if (s_price != "" || m_price != "" || b_price != "") setPrice = true;
                                unit.plan_name = "零售";
                            }
                            else//价格方案
                            {
                                string otherClass = p.Value.other_class;
                                var selectClass = ""; var selectDisc = ""; var classIndex = -1; bool getPlanPrice = false;
                                foreach (dynamic s in p.Value.priceSetting)
                                {
                                    if (s.price_item != "")
                                    {
                                        unit.s_plan_price = s.s_price;  //unit中添加价格方案
                                        unit.m_plan_price = s.m_price;
                                        unit.b_plan_price = s.b_price;
                                        if (!setPrice)
                                        {
                                            s_price = s.s_price;
                                            m_price = s.m_price;
                                            b_price = s.b_price;
                                        }
                                        if (s.s_price != "" || s.m_price != "" || s.b_price != "") //可能存在 只有 某一单位有价格，
                                        {
                                            setPrice = true; getPlanPrice = true;
                                            break;
                                        }
                                        else selectDisc = s.item_discount;
                                    }
                                    else if (s.class_id != "")
                                    {
                                        // var classArr = otherClass.Split(otherClass, '/');
                                        var classArr = otherClass.Split('/');
                                        if (Array.IndexOf(classArr, s.class_id) > classIndex)
                                        {
                                            classIndex = Array.IndexOf(classArr, s.class_id);
                                            selectClass = s.class_id;
                                            selectDisc = s.class_discount;
                                            getPlanPrice = true;
                                        }
                                    }
                                    if (getPlanPrice)
                                    {
                                        unit.plan_id = plan;
                                        unit.plan_name = p.Value.plan_name;
                                        break;
                                    }
                                }
                                if (!getPlanPrice && selectDisc != "") // 如果是给 商品类别指定折扣 的价格方案，就取批发价*折扣
                                {
                                    unit.b_orig_price = unit.bpprice;
                                    unit.m_orig_price = unit.mpprice;
                                    unit.s_orig_price = unit.spprice;
                                    bOrigPriceDiffWithPrice = true;
                                    if (unit.spprice != "") {
                                        if (!setPrice) s_price = (Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.spprice)).ToString();
                                        unit.s_plan_price = s_price;
                                    }
                                   
                                    if (unit.mpprice != "")
                                    {
                                        if (!setPrice) m_price = (Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.mpprice)).ToString();
                                        unit.m_plan_price = m_price;
                                    }
                                    if (unit.bpprice != "")
                                    {
                                        if (!setPrice) b_price = (Convert.ToSingle(selectDisc) * Convert.ToSingle(unit.bpprice)).ToString();
                                        unit.b_plan_price = b_price;
                                    }
                                    getPlanPrice = true;
                                }
                                if (getPlanPrice) break;
                            }
                           

                        }
                        

                    }


                    if (!bOrigPriceDiffWithPrice)
                    {
                        unit.b_orig_price = b_price;
                        unit.m_orig_price = m_price;
                        unit.s_orig_price = s_price;
                    }

                    unit.s_price = s_price;
                    unit.m_price = m_price;
                    unit.b_price = b_price;

                    unit.s_wholesale_price = s_price;
                    unit.m_wholesale_price = m_price;
                    unit.b_wholesale_price = b_price;
                    
                    // 重新封装价格便于手机端展示                  
                     if (unit.sunit != "")
                     {
                        List<dynamic> saction = new List<dynamic>();
                        saction.Add(new { text = "价格方案：", price = unit.s_plan_price });
                        saction.Add(new { text = "最近售价：", price = unit.s_recent_price });
                        saction.Add(new { text = "零售价：", price = unit.s_retail_price });
                        saction.Add(new { text = "批发价：", price = unit.s_wholesale_price });
                        unit.unitPrice.Add(unit.sunit,new {showPopoverFlag = false, actions = saction});
                        
                     }
                    if (unit.munit != "")
                    {
                        List<dynamic> maction = new List<dynamic>();
                        maction.Add(new { text = "价格方案：", price = unit.m_plan_price });
                        maction.Add(new { text = "最近售价：", price = unit.m_recent_price });
                        maction.Add(new { text = "零售价：", price = unit.m_retail_price });
                        maction.Add(new { text = "批发价：", price = unit.m_wholesale_price });
                        unit.unitPrice.Add(unit.munit,new {showPopoverFlag = false, actions = maction});
                    }
                    if (unit.bunit != "")
                    {
                        List<dynamic> baction = new List<dynamic>();
                        baction.Add(new { text = "价格方案：", price = unit.b_plan_price });
                        baction.Add(new { text = "最近售价：", price = unit.b_recent_price });
                        baction.Add(new { text = "零售价：", price = unit.b_retail_price });
                        baction.Add(new { text = "批发价：", price = unit.b_wholesale_price });
                        unit.unitPrice.Add(unit.bunit,new {showPopoverFlag = false, actions = baction});

                    }
                   
                    

                }
                #endregion

            }
            
            else
            {
                foreach (dynamic unit in data)
                {
                    unit.s_price = unit.s_recent_price;
                    unit.m_price = unit.m_recent_price;
                    unit.b_price = unit.b_recent_price;
                    unit.b_orig_price = unit.b_recent_orig_price;
                    unit.m_orig_price = unit.m_recent_orig_price;
                    unit.s_orig_price = unit.s_recent_orig_price;
                    if (unit.s_price == "") unit.s_price = unit.s_orig_price = unit.s_wholesale_price;
                    if (unit.m_price == "") unit.m_price = unit.m_orig_price = unit.m_wholesale_price;
                    if (unit.b_price == "") unit.b_price = unit.b_orig_price = unit.b_wholesale_price;
                    if (isContractSeller)
                    {
						unit.b_wholesale_price = unit.b_contract_price;
						unit.m_wholesale_price = unit.m_contract_price;
						unit.s_wholesale_price = unit.s_contract_price;
					}
                    unit.s_plan_price = "";
                    unit.m_plan_price = "";
                    unit.b_plan_price = "";
                    unit.plan_id = "";
                    unit.plan_name = "";
                    unit.unitPrice = new Dictionary<string, dynamic>();
                    // 重新封装价格便于手机端展示                  
                     if (unit.sunit != "")
                     {
                        List<dynamic> saction = new List<dynamic>();
                        saction.Add(new { text = "价格方案：", price = unit.s_plan_price });
                        saction.Add(new { text = "最近售价：", price = unit.s_recent_price });
                        saction.Add(new { text = "零售价：", price = unit.s_retail_price });
                        saction.Add(new { text = "批发价：", price = unit.s_wholesale_price });
                        unit.unitPrice.Add(unit.sunit,new {showPopoverFlag = false, actions = saction});
                        
                     }
                    if (unit.munit != "")
                    {
                        List<dynamic> maction = new List<dynamic>();
                        maction.Add(new { text = "价格方案：", price = unit.m_plan_price });
                        maction.Add(new { text = "最近售价：", price = unit.m_recent_price });
                        maction.Add(new { text = "零售价：", price = unit.m_retail_price });
                        maction.Add(new { text = "批发价：", price = unit.m_wholesale_price });
                        unit.unitPrice.Add(unit.munit,new {showPopoverFlag = false, actions = maction});
                    }
                    if (unit.bunit != "")
                    {
                        List<dynamic> baction = new List<dynamic>();
                        baction.Add(new { text = "价格方案：", price = unit.b_plan_price });
                        baction.Add(new { text = "最近售价：", price = unit.b_recent_price });
                        baction.Add(new { text = "零售价：", price = unit.b_retail_price });
                        baction.Add(new { text = "批发价：", price = unit.b_wholesale_price });
                        try
                        {
							unit.unitPrice.Add(unit.bunit, new { showPopoverFlag = false, actions = baction });

						}
                        catch(Exception ee)
                        {
							return Json(new {result= "Error", msg = $"商品{unit.item_name}大小单位名重复" });
						}


					}
                    if (unit.s_recent_price != "") unit.price_type = "上次";
                    else unit.price_type = "批发";
                }

            }
            #endregion
            foreach (dynamic item in data)
            {
                if (item.s_wholesale_price != ""|| item.m_wholesale_price != "" || item.b_wholesale_price != "") item.price_type = "批发";
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data, itemCount });
        }

        //查询出仓的库存
        [HttpGet]
        public async Task<JsonResult> GetFromBranchItemList(string operKey, string branchId, string itemIds)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);

            var condi = "";
            if (branchId != null ) condi += $"and branch_id = {branchId} ";

            if (itemIds != null) condi += $"and item_id in ({itemIds} )";

            var sql = $@"
select 
		item_id,
		item_name,
	concat(bStock,bUnit,mStock,mUnit,sStock,sUnit) BranchIDqty
from(

	SELECT
		ip.item_id,
		ip.item_name,
		ip.company_id,
		branch_id,
		stock_qty,
		(CASE WHEN b IS NOT NULL THEN sign( stock.stock_qty ) * FLOOR ( COALESCE ( ABS ( stock.stock_qty ), 0 ) / ( T.b ->> 'f1' ) :: NUMERIC ) ELSE NULL END ) AS bStock,
   ( CASE WHEN ( T.M ->> 'f1' ) IS NULL THEN NULL ELSE sign( stock.stock_qty ) * FLOOR (( COALESCE ( ABS ( stock.stock_qty ), 0 ) % ( T.b ->> 'f1' ) :: NUMERIC ) / ( T.M ->> 'f1' ) :: NUMERIC ) END ) AS mStock,
(
CASE	WHEN ( T.b ->> 'f1' ) IS NOT NULL	AND ( T.M ->> 'f1' ) IS NOT NULL THEN	round(	COALESCE ( stock.stock_qty, 0 ) % ( T.b ->> 'f1' ) :: NUMERIC % ( T.M ->> 'f1' ) :: NUMERIC,	0	) 	WHEN ( T.b ->> 'f1' ) IS NOT NULL 	AND ( T.M ->> 'f1' ) IS NULL THEN	round( COALESCE ( stock.stock_qty, 0 ) % ( T.b ->> 'f1' ) :: NUMERIC, 0 ) 	WHEN ( T.b ->> 'f1' ) IS NULL 	AND ( T.M ->> 'f1' ) IS NULL THEN	round( COALESCE ( stock.stock_qty, 0 ), 0 ) 	END 	) AS sStock,
	T.b ->> 'f2' AS bUnit,
	T.M ->> 'f2' AS mUnit,
	( T.s ->> 'f2' ) AS sUnit
FROM
( SELECT item_id,   stock_qty,branch_id FROM stock  WHERE company_id = {companyID} {condi}  ) stock
LEFT JOIN  info_item_prop AS ip  ON stock.item_id = ip.item_id 
LEFT JOIN (SELECT item_id,s,M,b FROM
	crosstab ( 'select item_id,unit_type,row_to_json(row(unit_factor,unit_no,wholesale_price,retail_price,barcode,buy_price)) as json from info_item_multi_unit where company_id= {companyID} ORDER BY item_id',$$ VALUES ( 's' :: TEXT ), ( 'm' :: TEXT ), ( 'b' :: TEXT ) $$ ) AS errr ( item_id INT, s jsonb, M jsonb, b jsonb ) 
	) t ON ip.item_id = t.item_id) t";

            QQ.Enqueue("data", sql);
            List<ExpandoObject> data = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, data });

        }


      

        /// <summary>
        /// 订单历史记录 -- 返回{itemname,sheet_no,数量变化，单位，价格,日期，dateNum日期差,remark备注}
        /// </summary>
        /// <param name="operKey">Aa18nTx5omI=</param>
        /// <param name="itemID">（1）</param>
        /// <param name="pageSize"></param>
        /// <param name="startRow"></param>
        /// <returns></returns>

        [HttpGet]
        public async Task<JsonResult> GetSaleOrderHistory(string operKey, string itemID, string pageSize, string startRow)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var condi = $"where m.company_id = {companyID} and item_id = {itemID} and approve_time is not null and red_flag is null ";
            if (pageSize != null && startRow != null) condi += $" limit {pageSize} offset {startRow}";
            var sql = @$"select d.item_name,m.sheet_no,m.money_inout_flag*d.quantity quantity,d.unit_no,d.real_price,d.happen_time,(now()::DATE-d.happen_time::DATE) dateNum,d.remark from sheet_sale_order_main as m LEFT JOIN sheet_sale_order_detail as d on m.sheet_id = d.sheet_id
                       {condi} ORDER BY d.happen_time desc;";
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("data", sql);
            sql = $"select count(m.sheet_no) as count from sheet_sale_order_main as m LEFT JOIN sheet_sale_order_detail as d on m.sheet_id = d.sheet_id {condi} ";
            QQ.Enqueue("count", sql);
            List<ExpandoObject> saleOrderHistory = null;
            var dr = await QQ.ExecuteReaderAsync();
            var count = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    saleOrderHistory = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    count = CPubVars.GetTextFromDr(dr, "count");
                }
            }
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, saleOrderHistory, count });
        }

        [HttpPost]
        public async Task<JsonResult> Delete([FromBody] dynamic data)
        {
            string sheet_id = data.sheet_id;
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            SheetMove sheet = new SheetMove(LOAD_PURPOSE.SHOW);

            string msg = await sheet.Delete(cmd, companyID, sheet_id, operID);
            string receiverId = sheet.maker_id;
            string sheetNo = sheet.sheet_no;
            string from_branch_name = sheet.from_branch_name;
            string to_branch_name = sheet.to_branch_name;
            string result = msg == "" ? "OK" : "Error";
            if (msg != "") result = "Error";
            if (msg == "")
            {
                //  更新消息
                Dictionary<string, dynamic> messageResult = await MessageUpdateServices.UpdateDealMessageService(new
                {
                    operKey, 
                    msgId = "", 
                    sheetID = sheet_id,
                    msgClass = MessageType.ClassType.Todo,
                    msgType = MessageType.MoveSheetMessageType.MessageType,
                    msgSubType = MessageType.MoveSheetMessageType.MessageSubType.MoveSheetApproveSubType.SubTypeKey
                }, cmd);
                // if (!messageResult.ContainsKey("errMessage"))
                // {
                //     // 通知到当时的创建单据的业务员
                //     await MessageCreateServices.CreateMessageService(new
                //     {
                //         operKey,
                //         createrId = operID,
                //         msgClass = MessageType.ClassType.Notice,
                //         msgType = MessageType.NoticeMessageType.CommonNotice.NoticeType,
                //         msgSubType =MessageType.NoticeMessageType.CommonNotice.NoticeSubType.CommonNoticeSubType.SubTypeKey,
                //         receiverId,
                //         msgTitle = @$"{from_branch_name} 到 {to_branch_name} 调拨申请已删除。 单号: {sheetNo}",
                //     }, cmd);
                // }
            }

            return Json(new { result, msg, sheet.sheet_id, sheet.sheet_no, sheet.happen_time });
        }
        [HttpGet]
        public async Task<JsonResult> GetBranchPosition(string operKey,string branch_id)
        {
            branch_id = branch_id.IsInvalid() ? "-1" : branch_id;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = $@"select branch_id,json_agg(json_build_object('v',branch_position,'l',branch_position_name)) as branch_position from info_branch_position where branch_id in ({branch_id}) and company_id = {companyID} and COALESCE(position_status,'1') = '1' group by branch_id;";
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string result = "OK";
            string msg = "";
            return Json(new { result, msg, records });
        }

        [HttpPost]
        public async Task<JsonResult> CheckVanStockOverLoad([FromBody] dynamic dSheet)
        {
            string s = Newtonsoft.Json.JsonConvert.SerializeObject(dSheet);
            SheetMove sheet = Newtonsoft.Json.JsonConvert.DeserializeObject<SheetMove>(s);
            Security.GetInfoFromOperKey(sheet.OperKey, out string companyID);
            string sql;
            string msg = "";
            
           
            if (sheet.to_branch_id.IsValid())
            {
                sql = $@"select isa.item_id,item_name,stock_qty,threshold_overload,branch_type from info_stock_alert as isa
                      left join( select item_id, sum(stock_qty) stock_qty from stock where company_id = {companyID} and branch_id={sheet.to_branch_id} group by item_id ) stock on isa.item_id = stock.item_id 
                      left join (select item_id,item_name from info_item_prop where company_id = {companyID})ip on ip.item_id=isa.item_id 
                      LEFT JOIN (select branch_id,branch_type from info_branch where company_id ={companyID})b on  b.branch_id = isa.branch_id
                      where company_id = {companyID} and isa.branch_id={sheet.to_branch_id} and b.branch_type='truck'";
                
                List<ExpandoObject> records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                
                  
                if (records.Count() > 0)
                { 
                    var sheetRows = sheet.MergeSheetRows(sheet.SheetRows);
                    string overloadItems = "";
                    foreach (dynamic rec in records)
                    {
                        var row=sheetRows.Find(row=>row.item_id==rec.item_id);
                        if (row != null)
                        {
                            decimal quantity = row.quantity;
                            decimal stockQty = string.IsNullOrEmpty(rec.stock_qty) ? 0 : CPubVars.ToDecimal(rec.stock_qty);
                            decimal thresholdQty = CPubVars.ToDecimal(rec.threshold_overload);
                            if (stockQty + quantity > thresholdQty + 0.01m)
                            {
                                decimal overloadQty = stockQty + quantity - thresholdQty;                               
                                overloadItems = $"{rec.item_name} 超{CPubVars.FormatMoney(overloadQty / row.unit_factor, 2) + row.unit_no}\r\n"; 
                            }
                        }
                    }
                    if(overloadItems!="")
                       msg = $"以下商品超车辆库存上限,请及时结转车辆库存\r\n: {overloadItems}";
                }
            }

            string result = "OK";
            if (msg != "") result = "Error";
            return new JsonResult(new { result,msg });
        }
    }
}

