﻿@page
@model ArtisanManage.CwPages.Report.CashInOutModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>CashInOut</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>
    <script src="~/js/jQuery.print.js"></script>
    <script src="~/js/jQuery.print.js"></script>
    <script src="~/js/jszip.min.js"></script>
    <script src="~/js/FileSaver.js"></script>
    <script src="~/js/excel-gen.js"></script>
    <script src="~/js/demo.page.js"></script>


    <style>
        * {
            font-family: "微软雅黑"
        }

        body {

        }
         [v-cloak] {
          display: none;
        }
        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;

        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #dddddd;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }

        #pages {
            width: 90%;
            display: flex;
            flex-direction: column;
            height: 90vh;
            cursor:pointer;
        }

        .pages_title {
            width: 100%;
            font-weight: 500;
            font-size: 25px;
            text-align: center;
            margin-top: 5px;
            padding-bottom:10px;
        }

        .pages_query {
            display: flex;
            width: 750px;
            margin: 0 auto;
        }


        .query_item {
            display: flex;
            width: 150px;
        }


            .query_item input {
                padding-top: 15px;
                padding-right: 0px;
                height: 35px;
                width: 179px !important;
                border: none;
                border-bottom: 1px #ddd solid;
                border-radius: 0;
                text-align: center;
            }
        .query_item input:active {

            border-bottom: 1px #ddd solid ;

        }

        .item_input {
            width: 149px;
            height: 50%;
            z-index: 0;
            position: relative;
            border-style: none none solid;
            border-bottom: 1px solid #c7c7c7;
            border-radius: 0px;
            margin-left: 10px;


        }


        .pages_content {
            padding: 0 20px;
            margin:20px auto;
        }

        .level_1 {
            font-weight: bold
        }
         .level_2 {
            text-indent: 1em;
            text-align:left;
        }
        .level_3 {
            text-indent: 1em;
            text-align:center;
        }
        .level_4 {
            text-indent: 2em;
            text-align: right;

        }
        .link {
           color:blue;
        }

        .pages_content table {
            width: 600px;
            border-width: 0;
            border-collapse: collapse;
            border: 1px solid #ebeef5;
            margin: 0 auto;
            display: block;

            max-height:600px;
        }
        @@media(max-height:700px) {
        .pages_content table tbody {
            display: block;
            height: 350px;
        }
         }
        .pages_content table thead, .pages_content tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .pages_content table thead {
            width: 100%;
        }

        .pages_content table thead th:nth-child(1), .pages_content table thead th:nth-child(5), .pages_content table tbody td:nth-child(1), .pages_content table tbody td:nth-child(5) {
            width: 50%
        }
        .pages_content table thead th:nth-child(2), .pages_content table thead th:nth-child(6), .pages_content table tbody td:nth-child(2), .pages_content table tbody td:nth-child(6) {
            width: 50%;
            text-align: center
        }
        .pages_content table thead th:nth-child(3), .pages_content table thead th:nth-child(7), .pages_content table tbody td:nth-child(3), .pages_content table tbody td:nth-child(7) {
            width: 12.5%
        }
        .pages_content table thead th:nth-child(4), .pages_content table thead th:nth-child(8), .pages_content table tbody td:nth-child(4), .pages_content table tbody td:nth-child(8) {
            width: 12.5%
        }
        .pages_content table tbody td:nth-child(3), .pages_content table tbody td:nth-child(7), .pages_content table tbody td:nth-child(4), .pages_content table tbody td:nth-child(8) {
            text-align: right;
        }
        @*高度*@
        .pages_content table thead th, .pages_content table tbody td {
            min-height: 40px;
            line-height: 40px;
        }
        @*边框*@
        .pages_content table thead th, .pages_content table tbody td {
            border-bottom: 1px solid #ebeef5;
            border-right: 1px solid #ebeef5;
        }
            .pages_content table thead th:last-child {
                border-right: 0;
            }
        @*背景*@
        .pages_content table tbody tr:nth-child(odd) {
            background: #fafafa;
        }
        .pages_content table tbody tr:hover {
            background-color: #f5f7fa;
        }

        .pages_content table thead th, .pages_content table tbody td {
            padding: 0 2px;
        }

        .main-button {
            margin-left: 30px;
            margin-top: 7px;
            background-color: #f2f2f2;
            border-style: solid;
            border-color: #e6e6e6;
            border-width: 1px;
            border-radius: 5px;
            width: 72px;
            height: 32px;
            background-color: #ffcccc;
            border-color: #ffcccc;
        }
        .main-button:hover {
            background-color: #eebbbb;
            border-color: #eebbbb;
        }

        .main-button:active {
            background-color: #ddaaaa;
        }
        .el-input__icon {
            height: 125%;
        }

    </style>

</head>
<body>
    <div id="root">
        <div id="pages" class="" ref="pages" v-cloak>
            <div class="pages_title">现金收支表</div>
            <div class="pages_query">
                <div class="query_item">

                    <el-date-picker v-model="StartDay"
                                    type="datetime"
                                    :clearable="false"
                                    value-format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>
                </div>
                <label style="line-height:50px">~</label>
                <div class="query_item">

                    <el-date-picker v-model="EndDay"
                                    type="datetime" :clearable="false"
                                    value-format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>

                </div>
                <div class="query_item">
                    <template>
                        <el-select v-model="selectId" clearable placeholder="业务员">
                            <el-option v-for="item in seller" :key="item.id" :label="item.value" :value="item.id"> </el-option>
                        </el-select>
                    </template>
                </div>
                     <div class="query_item" style="margin-left:20px">
                    <template>
                        <el-select v-model="timeType" clearable placeholder="时间查询依据">
                            <el-option v-for="(item,index) in queryTimeTypeArray" :key="index" :label="item.name" :value="item.value"> </el-option>
                        </el-select>
                    </template>
                </div>
                <div style="display:flex;justify-content:space-between;flex-wrap:nowrap;margin-left:30px;">
                    <button class="main-button" @@click="queryData" style="margin-left:20px">查询</button>
                    <button class="main-button" @@click="print()" style="margin-left:20px;background:#eee;border-color: #eee;">导出</button>
                    <button class="main-button" @@click="printPage()" style="margin-left:20px;background:#eee;border-color: #eee;">打印</button>
                </div>
            </div>
           

            <div class="pages_content" >
                <table id="test_table">
                   <thead >
                        <tr>
                            <th class="level_1">项目</th>
                            <th class="level_1">金额</th>
                            
                        </tr>
                    </thead>
                        
                    <tbody ref="tbodyRef">
                        <tr>
                            <th class="level_1">收入</th>
                            <td class="level_1"></td>
                            
                        </tr>
                        <tr>
                            <td class="level_3">销售收入</td>
                            <td class="link" @@click="toSalesSummary(xs_in)" ref="closingBalance_1">{{xs_in}}</td>
                        </tr>
                         <tr >
                            <td class="level_3" ref="closingBalance_1">收欠款</td>
                            <td class="link" @@click="toDetail('SK',sk_in)" >{{sk_in}} </td>
                        </tr>
                         <tr >
                            <td class="level_3" ref="closingBalance_1">收预收款</td>
                            <td class="link" @@click="toDetail('YS', ys_in)"  >{{ys_in}} </td>
                        </tr>
                        <tr>
                            <td class="level_3">其他收入合计</td>
                            <td class="link" @@click= "toDetail('SR',sr_in)" ref="closingBalance_1">{{sr_in}}</td>
                        </tr>
             
                         <tr>
                            <td class="level_3">合计</td>
                            <td  ref="closingBalance_1">{{toMoney(xs_in + ys_in + sk_in+ sr_in )}}</td>
                        </tr>                  

                        <tr>
                            <th class="level_1">支出</th>
                            <td class="level_1"></td>
                            
                        </tr> 
                        <tr>
                            <td class="level_3">采购金额</td>
                            <td class="link" @@click="toBuysSummaryByItem(cg_out)" ref="closingBalance_1">{{cg_out}}</td>
                        </tr> 

                        <tr >
                            <td class="level_3" ref="closingBalance_1">还欠款</td>
                            <td class="link" @@click="toDetail('FK',fk_out)" >{{fk_out}} </td>
                        </tr>

                        <tr >
                            <td class="level_3" ref="closingBalance_1">充预付款</td>
                            <td class="link"  @@click="toDetail('YF',yf_out)"> {{yf_out}}</td>
                        </tr>
                       
                        <tr>
                            <td class="level_3">费用合计</td>
                            <td class="link" @@click= "toDetail('ZC',zc_out)" ref="closingBalance_1">{{zc_out}}</td>
                        </tr>

                        <tr>
                            <td class="level_3">合计</td>
                            <td ref="closingBalance_1">{{toMoney(cg_out + yf_out + fk_out + zc_out )}}</td>
                        </tr>  

                         <tr>
                            <td class="level_1">收支差额</td>
                            <td ref="closingBalance_1">{{toMoney(xs_in + ys_in + sk_in+ sr_in -cg_out - yf_out - fk_out - zc_out )}}</td>
                        </tr> 
                    </tbody>

                </table>
            </div>
        </div>
    </div>
    <script>
        var g_operKey = '@Model.OperKey';
    </script>
    <script>
        var vm = new Vue({
            el: '#root',
            data() {
                return {
                    xs_in: 0,
                    sk_in: 0,
                    ys_in: 0,
                    sr_in: 0,
                    cg_out: 0,
                    fk_out: 0,
                    yf_out: 0,
                    zc_out: 0,

                    seller: [],
                    selectId: "",
                    StartDay: new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)) + '-' + new Date().getDate() +" 00:00:00",
                    EndDay: new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1))   + '-'  + new Date().getDate()+" 23:59:59",
                    queryTimeTypeArray: [{ name: '按交易日期查询', value: 'byHappenTime' }, {name: '按交账日期查询', value: 'byCheckedTime' }],
                    timeType:''
                }
            },
            computed: {
                EndDayTime() {
                    var t = this.EndDay
                    if (t.indexOf(":") == -1) t = t + ' 23:59:59'
                    return t
                }
            },
            mounted() {
                this.$nextTick(() => {
                    this.timeType = this.queryTimeTypeArray[0].value;
                    this.queryData();
                    this.getSellerId()
                })
            },
            methods: {
                queryData() {
                    var param = {
                        operKey: g_operKey,
                        startTime: this.StartDay ,
                        endTime: this.EndDayTime ,
                        operId: this.selectId,
                        timeType:this.timeType
                    }
                    this.GetSubjectBanlance(param)
                },
                toMoney(m) {
                    return toMoney(m)
                },
                GetSubjectBanlance(param) {
                    var that = this
                    $.ajax({
                        url: '/api/CashInOut/GetSubjectBanlance',
                        type: 'get',
                        data: param,
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        if (res.result === 'OK') {
                            that.xs_in = res.xs;
                            that.sk_in = res.sk;
                            that.ys_in = res.ys;
                            that.sr_in = res.sr;
                            that.cg_out = res.cg;
                            that.fk_out = res.fk;
                            that.yf_out = res.yf;
                            that.zc_out = res.zc;
                        }
                    });
                },
                toDetail(type,amt){
                    if (amt) {
                        let typeInfo = '';
                        switch (type) { 
                            case 'YS':
                                typeInfo = `&sheet_type=YS,DH&sheet_type_name=预收款单,定货会&sub_type=QT&sub_type_label=现金银行`;
                                break;
                            case 'YF':
                                typeInfo = `&sheet_type=YF&sheet_type_name=预付款单&sub_type=QT&sub_type_label=现金银行`;
                                break;
                            case 'SK':
                                typeInfo = `&sheet_type=SK&sheet_type_name=收款单&sub_type=QT&sub_type_label=现金银行`;
                                break;
                            case 'FK':
                                typeInfo = `&sheet_type=FK&sheet_type_name=付款单&sub_type=QT&sub_type_label=现金银行`;
                                break;
                            case 'ZC':
                                typeInfo = `&sheet_type=ZC&sheet_type_name=费用支出单&sub_type=QT&sub_type_label=现金银行`;
                                break;
                            case 'SR':
                                typeInfo = `&sheet_type=SR&sheet_type_name=其他收入单&sub_type=QT&sub_type_label=现金银行`;
                                break;
                        }

                        let sellerInfo = this.getThisSeller();
                        window.parent.newTabPage("收入支出明细表", `Report/FeeOutDetail?&startDay=${this.StartDay}&endDay=${this.EndDayTime}${typeInfo}${sellerInfo}`);
                    }
                },
                toSalesSummary(amt) { 
                    if (amt) {
                        let sellerInfo = this.getThisSeller();
                        window.parent.newTabPage("查销售单", `Sheets/SaleSheetView?sheetType=x&startDay=${this.StartDay}&endDay=${this.EndDayTime}&queryTimeAccord=${this.timeType ? this.timeType : 'byHappenTime'}&status=approved&${sellerInfo}`);
                    }
                },
                 toBuysSummaryByItem(amt) { 
                     if (amt) {
                        let sellerInfo = this.getThisSeller();
                         window.parent.newTabPage("查采购单", `Sheets/BuySheetView?startDay=${this.StartDay}&endDay=${this.EndDayTime}&byHappenTime=true${sellerInfo}`);
                     }
                },
                getThisSeller(){
                    if (this.selectId) {
                        let sellerRow = this.seller.find(row => row.id == this.selectId);
                        if(sellerRow){
                             return `&seller_id=${this.selectId}&seller_name=${sellerRow.value}`;
                        }
                    }
                    return '';
                },
                printPage(){


                    $("#pages").print({
                         globalStyles: true,//是否包含父文档的样式，默认为true
                        mediaPrint: false,//是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                        stylesheet: null,//外部样式表的URL地址，默认为null
                        noPrintSelector: ".no-print",//不想打印的元素的jQuery选择器，默认为".no-print"
                        iframe: true,//是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                        append: null,//将内容添加到打印内容的后面
                        prepend: null,//将内容添加到打印内容的前面，可以用来作为要打印内容
                        deferred: $.Deferred()//回调函数
                    });


                },
                getSellerId() {
                    var that = this
                    $.ajax({
                        url: '/api/BusinessProfit/GetSellerId',
                        type: 'get',
                        data: {
                            operKey: g_operKey,
                        },
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        if (res.result === 'OK') {
                            that.seller = res.seller
                        }
                    });
                }, print() {

                    excel = new ExcelGen({
                        "src_id": "test_table",
                        "show_header": true
                    });
                    excel.generate("现金收支表.xlsx");
                }
            }
        })
    </script>
    

</body>
</html>