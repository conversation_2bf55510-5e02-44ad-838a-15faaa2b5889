using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class PrintTemplateViewModel : PageQueryModel
    { 
        public PrintTemplateViewModel(CMySbCommand cmd):base(MenuId.infoPrintTemplate)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            { 
               // {"sheet_type",new DataItem(){FldArea="divHead",Title="单据类型",LabelFld="sheet_type_name",ButtonUsage="list",Source = "[{v:'X',l:'销售单'},{v:'T',l:'退货单'},{v:'',l:'所有'}]",CompareOperator="="}}
           
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_type", new DataItem(){Title="单据类型", SqlFld="st.sheet_type",Hidden=true, Width="100"}},
                       {"sheet_type_name", new DataItem(){Title="单据类型", Width="200"}},
                       {"default_template", new DataItem(){Title="默认模板",GetFromDb=false,  Width="200",
                /*      JSCellRender=@" 
 function (index, datafield, value, defaultvalue, column, rowdata) {   
   var allTmp= rowdata.all_template;
   if(allTmp) allTmp=JSON.parse(allTmp);
   var default_template='';
   if(allTmp){
      allTmp.some(function(tmp){
         if(tmp[2]==1) {default_template=tmp[1];return true;}
       
      }); 
   }
   return `<div style='margin:4px;'>${default_template}</div>`;
}"*/
                JSCellRender="default_template_Render"
                       }},
                       {"all_template_show", new DataItem(){Title="所有模板",GetFromDb=false,  Width="",
                        JSCellRender=@" 
 function (index, datafield, value, defaultvalue, column, rowdata) {   
   var allTmp= rowdata.all_template;
   if(allTmp) allTmp=JSON.parse(allTmp);
   var default_template='';
   var tmpDivs='';
if(allTmp){
   allTmp.forEach(function(tmp){
         var curTmp=`<div id='${tmp[0]}'>${tmp[1]}</div>`;
         tmpDivs+=curTmp;       
   });   
}
//tmpDivs+='<image src=""/images/add.svg""/>';
 tmpDivs+=`<svg>
       <use xlink: href = '/images/images.svg#thinAdd' />
   </svg>`;
 
   return `<div class='tmp-list'>${tmpDivs}</div>`;
}"

                       }},
                       {"all_template", new DataItem(){Title="所有模板",Hidden=true,HideOnLoad = true,Width=""}}                                     
                     },
                     QueryFromSQL=@"
from  json_to_recordset('[
{""sheet_type"":""X"",""sheet_type_name"":""销售单""}
,{""sheet_type"":""T"",""sheet_type_name"":""退货单""}
,{""sheet_type"":""XD"",""sheet_type_name"":""销售订单""}
,{""sheet_type"":""TD"",""sheet_type_name"":""退货订单""}

,{""sheet_type"":""CG"",""sheet_type_name"":""采购单""}
,{""sheet_type"":""CT"",""sheet_type_name"":""采购退货单""}
,{""sheet_type"":""CD"",""sheet_type_name"":""采购订单""}
,{""sheet_type"":""DB"",""sheet_type_name"":""调拨""}
,{""sheet_type"":""PD"",""sheet_type_name"":""盘点单""}
,{""sheet_type"":""SS"",""sheet_type_name"":""门店库存上报单""}
,{""sheet_type"":""YK"",""sheet_type_name"":""盘点盈亏单""}
,{""sheet_type"":""BS"",""sheet_type_name"":""报损单""}
,{""sheet_type"":""ZZ"",""sheet_type_name"":""组装单""}
,{""sheet_type"":""CF"",""sheet_type_name"":""拆分单""}
,{""sheet_type"":""RK"",""sheet_type_name"":""其他入库单""}
,{""sheet_type"":""CK"",""sheet_type_name"":""其他出库单""}
,{""sheet_type"":""DH"",""sheet_type_name"":""定货会""}
,{""sheet_type"":""SK"",""sheet_type_name"":""收款单""}
,{""sheet_type"":""FK"",""sheet_type_name"":""付款单""}
,{""sheet_type"":""YS"",""sheet_type_name"":""预收款单""}
,{""sheet_type"":""YF"",""sheet_type_name"":""预付款单""}
,{""sheet_type"":""X_SUM"",""sheet_type_name"":""销售商品汇总单""}
,{""sheet_type"":""X_SHEETS_MAIN"",""sheet_type_name"":""客户汇总单""}
,{""sheet_type"":""ZC"",""sheet_type_name"":""费用支出单""}
,{""sheet_type"":""SR"",""sheet_type_name"":""其他收入单""}
,{""sheet_type"":""FV"",""sheet_type_name"":""财务凭证""}
,{""sheet_type"":""TR"",""sheet_type_name"":""转账单""}
,{""sheet_type"":""JZ_SUMMARY"",""sheet_type_name"":""交账单收款来源""}
,{""sheet_type"":""JZ_PAYWAY"",""sheet_type_name"":""交账单支付账户""}
,{""sheet_type"":""JZ_SHEETLIST"",""sheet_type_name"":""交账单单据""}
,{""sheet_type"":""JZ_ITEMLIST"",""sheet_type_name"":""交账单商品""}
,{""sheet_type"":""BQ"",""sheet_type_name"":""标签打印单""}
,{""sheet_type"":""QTF"",""sheet_type_name"":""欠条发放单""}
,{""sheet_type"":""QTS"",""sheet_type_name"":""欠条回收单""}
,{""sheet_type"":""DZ"",""sheet_type_name"":""对账单""}
,{""sheet_type"":""HK"",""sheet_type_name"":""回库单""}
,{""sheet_type"":""DK"",""sheet_type_name"":""贷款单""}
,{""sheet_type"":""HDK"",""sheet_type_name"":""还贷款单""}
,{""sheet_type"":""JH"",""sheet_type_name"":""借货单""}
,{""sheet_type"":""HH"",""sheet_type_name"":""还货单""}
,{""sheet_type"":""ZWD"",""sheet_type_name"":""占位单""}

]'::json) as st(sheet_type text,sheet_type_name text) left join 
(
  select pt.sheet_type,array_agg((pt.template_id,pt.template_name,case when  tc.sheet_type is not null and tc.client_group_id =0 and tc.client_id =0 then 1 else 0 end)) as all_template from print_template pt left join print_template_choose tc on pt.sheet_type=tc.sheet_type and pt.template_id=tc.template_id where pt.company_id=~COMPANY_ID
  group by pt.sheet_type

) tmp on st.sheet_type=tmp.sheet_type  
",
                     
                     QueryOrderSQL=""
                  }
                } 
            }; 
        }
        public async Task OnGet()
        {  
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
    }



    [Route("api/[controller]/[action]")]
    public class PrintTemplateViewController : QueryController
    { 
        public PrintTemplateViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            PrintTemplateViewModel model = new PrintTemplateViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            PrintTemplateViewModel model = new PrintTemplateViewModel(cmd);           
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<JsonResult> DeleteTemplate([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string templateID = data.templateID;
            cmd.CommandText = $"delete from print_template where company_id={companyID} and template_id={templateID}";
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new {result="OK",msg=""});
        }

        [HttpPost]
        public async Task<JsonResult> SetDefaultTemplate([FromBody] dynamic data)
        {
            string operKey = data.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string templateID = data.templateID;
            string sheet_type = data.sheet_type;
            string template_id = data.template_id;
            
            cmd.CommandText = $"delete from print_template_choose where company_id={companyID} and sheet_type='{sheet_type}' and client_id =0 and client_group_id =0;";
            cmd.CommandText += $"insert into print_template_choose (company_id,template_id,sheet_type,client_id,client_group_id) values ({companyID},{template_id},'{sheet_type}',0,0);";
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new { result = "OK", msg = "" });
        }
    }
}
