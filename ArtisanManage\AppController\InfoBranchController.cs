﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ArtisanManage.AppController
{
    /// <summary>
    /// 获取仓库列表
    /// </summary>
    [Route("AppApi/[controller]/[action]")]
    public class InfoBranchController : QueryController
    { 
        public InfoBranchController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }



        /// <summary>
        /// 仓库列表
        /// </summary>
        /// <param name="operKey"> </param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetBranchList(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            SQLQueue QQ = new SQLQueue(cmd);
            var sql = @$"select ib.branch_id,branch_name,branch_type,negative_stock_accordance,json_agg(json_build_object('branch_position',COALESCE(branch_position::text,'0'),'branch_position_name',COALESCE(branch_position_name,''),'type_id',type_id)) as branch_position from info_branch  ib
left join info_branch_position ibp on ibp.company_id = {companyID} and ibp.branch_id = ib.branch_id and COALESCE(position_status,'1')='1'
where  ib.company_id = {companyID} and COALESCE(ib.status,'1')='1' 
group by ib.branch_id,branch_name,branch_type,negative_stock_accordance,ib.order_index
order by ib.order_index,branch_name;";
            QQ.Enqueue("data", sql);
                sql = $"select count(branch_id) as total from info_branch where company_id = {companyID} and COALESCE(status,'1')='1';";
                QQ.Enqueue("count", sql);
            
            var data = new List<ExpandoObject>();
            var dr = await QQ.ExecuteReaderAsync();
            var total = "";
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data")
                {
                    data = CDbDealer.GetRecordsFromDr(dr, false);
                }
                else if (sqlName == "count")
                {
                    dr.Read();
                    total = CPubVars.GetTextFromDr(dr, "total");
                }
            }
            QQ.Clear();
            string result = "OK";
            string msg = "";
            return Json(new { result,msg, data, total });
        }


    }
}
