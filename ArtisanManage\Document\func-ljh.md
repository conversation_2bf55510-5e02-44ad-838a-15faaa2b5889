# Bug模板

## bug名称/时间

### 需求细节：

+ 使用场景：
  1. 相关变化

### 衍生：

+ 优化

- Bug：

# 需求模板

## 需求名称/完成时间

### 一、 功能目的：

初步估计场景



### 二、需求细节与难点：

### 三、开发流程：

##### 1. 需求分析

##### 2. 创建相关表

新增字段：

表：

| 字段名 | 字段类型 | 是否可空 | 字段注释 | 字段说明(存储格式) |
| ------ | -------- | -------- | -------- | ------------------ |
|        |          |          |          |                    |

##### 3. 页面

##### 4. 功能主要逻辑

##### 5. 研发团队内部优化修改  

### 四、开发笔记：

### 五、研发自我测试

| 版本 | 时间 | 测试功能 | 功能场景 | 测试结果 | 预期结果 |
| ---- | ---- | -------- | -------- | -------- | -------- |
|      |      |          |          |          |          |

### 六、 版本记录

| 版本 | 时间 | 修改状态 | 相关人员 |
| ---- | ---- | -------- | -------- |
|      |      |          |          |





# Bug

## 开单中更改客户(Web)/12.21-12.22

### 需求细节：

+ 开单：
  1. 客户的账户余额刷新
  2. 商品行价格等刷新
  3. 陈列、定货会商品删除
  4. 支付方式中支付方式一改为默认支付方式、支付方式二清空，以修改定货会、预收、陈列方式

+ 复制单据
  1. 陈列、定货会商品删除
  2. 支付方式中支付方式一改为默认支付方式、支付方式二清空，以修改定货会、预收、陈列方式

### 衍生：

- 优化：
  1. 优化 addItemsRows 中的渲染，加快商品渲染速度
- Bug：
  1. 订货会支付方式与金额不根据数量修改
  2. 当存在支付方式二，支付金额不能修改
  3. 复制有变价商品单据时，修改单位后价格更改，折扣未能更改
  3. 复制单据后，客户业务员仓库Id未获取到，导致不能直接保存 :negative_squared_cross_mark:  `2021.12.24 转移给大相`



## 盘点盈亏单显示库存(Web)/2.8

### 需求细节：

+ 开单：
  1. 选择仓库后刷新库存列

### 衍生：

- Bug：
  1. 商品多选失效

## 会计科目删除科目(Web)/2.8

### 需求细节：

+ 删除会计科目时，两个表内的科目同时删除
  1. 单独写delete方法



## 销售单(Web)/2.10

### 使用定货会商品选择单位问题 2.10

### 需求细节：

+ 选择单位时，应该选择多个单位

  bug原因：window.g_queriedItems[key] 的 key需要修改

# 需求

## 调价单(Web)/12.23-12.29

### 一、 功能目的：

+ 供应商想为某些商品进行统一调价，在改价后的第一次开单时使用该调整价

#### 修改开单价格

场景：不同客户的价格策略不同，调价单的作用

| 价格策略顺序                 | 调价前展现的价格   | 当调价单选择类型为 | 调价单的意义                   | 开单时的理想值 |
| ---------------------------- | ------------------ | ------------------ | ------------------------------ | -------------- |
| `批发价` - 方案一            | 批发价             | 批发价             | 修改商品档案的批发价，直接取出 | 批发价         |
| `批发价` - 方案一            | 方案一(无批发价)   | 批发价             | 添加了商品批发价，直接使用     | 批发价         |
| `方案一` - 批发价            | 方案一             | 方案一             | 修改方案一价格                 | 方案一         |
| 最近售价 - `批发价` - 方案一 | 最近售价           | 批发价             | 修改商品档案的批发价，取批发价 | 批发价         |
| 最近售价 - `批发价` - 方案一 | 批发价(无最近售价) | 批发价             | 修改商品档案的批发价，取批发价 | 批发价         |
| 最近售价 - `批发价` - 方案一 | 方案一(无其他价格) | 批发价             | 添加了商品批发价，直接使用     | 批发价         |
| 最近售价 - `方案二`          | 方案二             | 方案二             | 修改方案二价格                 | 方案二         |
| 方案一 - 最近售价 -` 批发价` | 最近售价(无方案一) | 批发价             | 修改批发价，取批发价           | 批发价         |
| `方案一` - 最近售价 - 批发价 | 方案一             | 方案一             | 修改方案一价格                 | 方案一         |
| 批发价 - 最近售价 - `方案一` | 最近售价(无批发价) | 方案一             | 修改方案一价格, 取方案一       | 方案一         |

由此可得：调价单的`使用`中，应该选择价格策略中的`第一策略`和`最近售价的后一策略`，若无价格策略，默认选择最近售价，需添加策略？---- 默认有策略

这个意味着，调价单前，需根据知道本次调价商品的策略❓ ---- 默认已清楚

### 二、需求细节与难点：

1. 单据上方可选择调价对象(可以选择批发价和各种价格方案)，调价后对应调价对象会被修改
   + 调价类型：批发价、价格方案、零售价 
   + 调价：调整前后价格、显示大中小价格  
   + 调价过程：
     1. 生成调价单，记录前后价格过程
     2. 批发价：添加、修改商品档案的批发价 ❓是否意味着，批发价不能自己修改，只能通过调价单修改？  ---- 否
     3. 方案价格：修改方案中具体商品的价格，表 price_plan_item；如果该方案之前为空，则相当于新增一个方案
   
2. 调价后，取记忆价格之后，会再次判断后面的价格方案是否被修改过，如果修改过，就会取消修改过的价格
   + 调价后具体影响：
     1. 调批发价：
        + 当price1是`最近售价`有值，看price2批发价，此时price2有值
          + `注：` 若price2有调价单时间，且时间比记忆价格时间新，取批发价
          + 否则，取最近售价，即原逻辑不变
        + 当price1是批发价，就取批发价，即原逻辑不变
        + 当price1为其他，方案一 - `最近售价` - 批发价，`存在逻辑漏洞`:x:
          + 对于方案一中没有的商品，商品又改了price2，则应取批发价
     2. 调方案一：
        + 当price1是`最近售价且`有值，看price2方案一，此时price2有值
          + `注：`看调价单时间或者方案更新时间，若时间比记忆价格时间新，取方案一价格
          + 否则，取方案一，即原逻辑不变
        + 当price1是方案一，就取方案一，即原逻辑不变 
        + 当price1为其他，方案二 - `最近售价` - 方案一，`存在逻辑漏洞`:x:
          + 对于方案二中没有的商品，商品又改了方案一，则应取方案一
     
   + `合并`：
     
     + 当取的价格是 最近售价，看后面一个方案为调价方案有值，若该商品该价格类型有新的调价单，取调价单价格
     + 剩下的情况为原逻辑，即默认价格策略
     
     

### 三、开发流程：

##### 1. 需求分析 -- 12.23 初步完成

##### 2. 创建调价表-- 12.23完成 

###### 12.23 初步设计 -- 12.23

###### 设计 sheet_price_adjust_main/sheet_price_adjust_detail 

```sql
CREATE TABLE sheet_price_adjust_main(company_id integer NOT NULL,sheet_id serial,sheet_no text NOT NULL, sheet_type text,money_inout_flag smallint, seller_id integer, plans_id text NOT NULL, red_flag smallint,red_sheet_id integer, red_sheet_date timestamp,maker_id integer, make_time timestamp, make_brief text,approver_id integer,approve_time timestamp, approve_brief text, happen_time timestamp,submit_time timestamp) 
partition by range(happen_time);

create table sheet_price_adjust_main_20072201 partition of sheet_price_adjust_main for values from ('2020-07-01') to ('2022-01-01');
create table sheet_price_adjust_main_22012301 partition of sheet_price_adjust_main for values from ('2022-01-01') to ('2023-01-01');

CREATE TABLE sheet_price_adjust_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,sheet_id integer,row_index int,inout_flag integer, item_id integer not null,s_price float4,m_price float4,b_price float4,price_info Jsonb NOT NULL,combine_flag text, tax_amount float4,happen_time timestamp, remark text) 
partition by range(happen_time);

create table sheet_price_adjust_detail_20072201 partition of sheet_price_adjust_detail for values from ('2020-07-01') to ('2022-01-01');
create table sheet_price_adjust_detail_22012301 partition of sheet_price_adjust_detail for values from ('2022-01-01') to ('2023-01-01');

create unique index idx_info_item_multi_unit_unique on info_item_multi_unit(company_id,item_id,unit_type);

```

###### 添加表item_price_adjust--12.30 

```sql
CREATE TABLE item_price_adjust(company_id integer not null,plan_id integer not null,item_id integer not null,adjust_time timestamp,constraint pk_item_price_adjust primary key(company_id,plan_id,item_id)) partition by range(adjust_time);
```

其中新增字段：

sheet_price_adjust_main：

| 字段名     | 字段类型 | 是否可空                 | 字段注释     | 字段说明                                                     |
| ---------- | -------- | ------------------------ | ------------ | ------------------------------------------------------------ |
| price_info | Jsonb    | :heavy_multiplication_x: | 选择调价类型 | 存储格式：[{"planID":"","sPrice":""},{"planID":"","bPrice":""}]  <br />例：[{ "planID":"333","sPrice":"50"},{"planID":"-1","sPrice":"35","bPrice":"70"} ]   <br />价格方案中的plan_id, -1：wholesale_price、0：retail_price，可多选 |

##### 3. 页面：12.23 ~ 12.28

+ 开单页面
+ 查看单据页面

##### 4. 功能主要逻辑：12.24 ~ 12.28

##### 5. 开单商品价格修改的逻辑：12.29

##### 6. 研发团队内部优化修改   12.30 

### 四、开发笔记：

##### 对于json中plan_id，查询对应逗号分隔的价格

数据：{ "plan_id":"24,-1","old_price":"50,55"}

```sql
SELECT g.plans_id,string_agg(s.plan_name,',') plans_name
FROM (select t.data->>'plan_id'  plans_id from (select '{"plan_id":"24,-1","old_price":"50,55"}'::json as data ) t) g 
LEFT JOIN (select plan_id,plan_name from price_plan_main where company_id = 51 union select '-1','批发价'
) s 
ON s.plan_id::text = ANY(STRING_TO_ARRAY(g.plans_id, ',')) 
GROUP BY g.plans_id
```

`ANY(STRING_TO_ARRAY(g.plans_id, ',')) `[left join 逗号分隔字段查询](https://blog.csdn.net/AdminPwd/article/details/********* )

##### 对于选择商品得到的商品信息结构：

实现效果：当多选 价格类型 时，商品的原来价格应该带出来，显示：批发价：5，方案一：6

### 五、研发自我测试

| 版本  | 时间       | 测试功能                  | 功能场景                                       | 测试结果           | 预期结果  |
| ----- | ---------- | ------------------------- | ---------------------------------------------- | ------------------ | --------- |
| 1.0.0 | 2021.12.29 | 调价单-调批发价           |                                                | :heavy_check_mark: | 新增/修改 |
|       |            | 调价单-调零售价           |                                                | :heavy_check_mark: | 新增/修改 |
|       |            | 调价单-调整方案           |                                                | :heavy_check_mark: | 新增/修改 |
|       |            | 调价单-多商品复合调整     |                                                | :heavy_check_mark: | 新增/修改 |
|       |            | 销售单-正常使用价格方案   |                                                | :heavy_check_mark: | 价格方案  |
|       |            | 销售单-使用调价后的批发价 | 原方案：记忆价格-批发价-方案一                 | :heavy_check_mark: | 批发价    |
|       |            | 销售单-使用调价后的方案一 | 原方案：记忆价格-方案一-批发价                 | :heavy_check_mark: | 方案一    |
| 1.1.0 | 2021.12.30 | 销售单-使用调价后的批发价 | 原方案：记忆价格(有价格)-批发价                | :heavy_check_mark: | 批发价    |
|       |            | 销售单-使用调价后的批发价 | 原方案：方案一(无价格)-记忆价格(有价格)-批发价 | :heavy_check_mark: | 批发价    |

### 六、 版本记录

| 版本  | 时间             | 修改状态                                                     | 相关人员 |
| ----- | ---------------- | ------------------------------------------------------------ | -------- |
| 1.0.0 | 2021.12.29 16:50 | 初步完成，等待研发自我测试 :heavy_check_mark:                | 研发：刘 |
| 1.0.1 | 2021.12.29 晚上  | 研发自我测试，页面小bug完善:heavy_check_mark:                | 测试：刘 |
| 1.0.1 | 2021.12.30 9:34  | 逻辑bug调整优化，添加调整商品记录表优化查询效率，修复使用商品时存在逻辑bug:heavy_check_mark: | 测试：相 |
| 1.1.0 | 2021.12.30 14:31 | 优化调整完成，等待研发自我测试:heavy_check_mark:             | 研发：刘 |
|       | 2021.12.30 14:48 | 研发自我测试，暂未发现bug:heavy_check_mark:                  | 测试：刘 |
|       | 2021.12.30 15:28 | 数据存储优化 price_info的json优化                            | 测试：相 |
| 1.2.0 | 2021.12.31 14:07 | 重新修改选择商品、保存存储price_info、打开单据渲染旧价格的逻辑 | 研发：刘 |
|       | 2021.12.31       | 对1.2.0版本的研发自我测试:heavy_check_mark:                  | 测试：刘 |
| 1.2.1 | 2021.1.11        | 发现调价与最近售价比对时逻辑有问题，进行修复                 | 研发：刘 |





## 成本调价单(Web) /12.29 - 1.5

### 一、 功能目的：

+ 客户可以通过成本调价单，多个商品重算从调价时间至今的加权价

+ 可以勾选更改预设进价，如果公司设置中默认为预设进价，就默认勾选

+ 选择调价日期

  + 如果选择调价日期为当天

    + 仅仅修改商品档案中的 商品cost_price_avg
    
  + 如果选择调价时间为以往某一天
  
    + 将调用 更新加权价程序，相当于已知插入单据时刻前的加权价
    
    

### 二、 需求细节与难点

+ 调价过程，选择商品某个单位进行调价，最终都会转成小单位调价，存储时存小单位成本价，:question:保留四位 
+ 根据选择时间
  + 如果为当前时间，调价后，更新商品档案中的加权价
  + 如果为之前某一个时间，调价后，需要调用更新加权价的代码
+ 根据勾选调整预设进价
  + 如果勾选，需要根据时间，刷新销售单、盘点单中的cost_price_buy
  + 动态显示旧的进价
  + 默认更新加权价

### 三、 开发流程：

##### 1. 需求分析 12.29

##### 2. 创建成本调价单对应表格 12.30

###### 	sheet_cost_price_adjust_main / sheet_cost_price_adjust_detail

```html
CREATE TABLE sheet_cost_price_adjust_main (company_id integer NOT NULL, sheet_id serial, sheet_no text NOT NULL, sheet_type text, money_inout_flag smallint, seller_id integer, red_flag smallint, red_sheet_id integer, red_sheet_date date, maker_id integer, make_time timestamp, make_brief text, approver_id integer, approve_time timestamp, approve_brief text, happen_time timestamp, submit_time timestamp, buy_price_change bool) 
partition by range(happen_time);

create table sheet_cost_price_adjust_main_20072201 partition of sheet_cost_price_adjust_main  for values from ('2020-07-01') to ('2022-01-01');
create table sheet_cost_price_adjust_main_22012301 partition of sheet_cost_price_adjust_main  for values from ('2022-01-01') to ('2023-01-01');

CREATE TABLE sheet_cost_price_adjust_detail(company_id int4 NOT NULL,flow_id serial NOT NULL,sheet_id integer,row_index int,inout_flag integer, item_id integer not null,unit_no text,unit_factor float4,real_price float4,old_avg_price float4,old_buy_price float4,combine_flag text, tax_amount float4,happen_time timestamp, remark text) 
partition by range(happen_time)

create table sheet_cost_price_adjust_detail_20072201 partition of sheet_cost_price_adjust_detail for values from ('2020-07-01') to ('2022-01-01');
create table sheet_cost_price_adjust_detail_22012301 partition of sheet_cost_price_adjust_detail for values from ('2022-01-01') to ('2023-01-01');
```

sheet_cost_price_adjust_main

| 字段名           | 字段类型 | 是否可空           | 字段注释             | 字段说明      |
| ---------------- | -------- | ------------------ | -------------------- | ------------- |
| buy_price_change | bool     | :heavy_check_mark: | 是否同时修改预设进价 | 存储格式：t,f |

sheet_cost_price_adjust_detail

| 字段名        | 字段类型 | 是否可空                 | 字段注释                   | 字段说明        |
| ------------- | -------- | ------------------------ | -------------------------- | --------------- |
| real_price    | float    | :heavy_multiplication_x: | 开单时调整的价格           | 存储格式：float |
| old_avg_price | float    | :heavy_check_mark:       | 开单时查询的当前加权成本价 | 存储格式：float |
| old_buy_price | float    | :heavy_check_mark:       | 开单时查询的当前进价       | 存储格式：float |

##### 3. 页面 12.31

+ 开单
+ 查看单据页面

##### 4. 功能主要逻辑 12.31 - 1.5

成本调整单，如果选择时间，相当于，插入单据时，插入时修改加权价，货值变化为 当前库存*调价单价格

红冲时，相当于开了一张价格为 old_price_avg 的成本调价单，如果更新了进价，还需恢复进价

+ 梳理原加权平均价逻辑

  1. MergeSheetRowsForAvg -- 当前单据里相同商品的合并

  2. 主要逻辑

     + !HappenNow

       1. GetHistorySheetItemsAfterMe -- 通过聚合处理 得到历史单据结果集 `lstHistorySheetItems` (包括单据中数量变化、avg)

       2. 查找每个商品的 `本单据向后` 的第一个不为空的成本价 `earliest_cost_price_avg`，且计算 `本单据向后` 的商品数量变化`N`

       3. 得到 happen_time 前一刻信息 now_qty、now_cost_amt、now_cost_price_avg 

          + now_qty = 本单据审核前(现在)的库存 old_total_qty + N
          + now_cost_price_avg = earliest_cost_price_avg
          + now_cost_price_avg = happen_time 前一刻的库存 * earliest_cost_price_avg 

       4. 现在对 lstDealSheetItems 所有包含本单据及之后的单子  计算开不通单据后的货值与库存

          + 开完X,T,YK,BS， 货值 = 开单时的加权价 * 数量 now_cost_amt += qty_change * now_cost_price_avg

          + 开完CG、CT， 货值 = 单据金额 now_cost_amt += sheet_amount;
          + 开完`CBTJ`，货值 now_cost_amt += s_real_price * now_qty ;

          + 数量都是 库存 + 变化量  now_qty += qty_change;

       5. 根据货值/总数量 进行`重算成本价`

          + now_qty < 0 || pre_qty < 0 质疑，本单据加权价 修改 为当前取得的价格  不影响商品档案加权价

          + 否则
            + 如果是采购单且变化量为正数，now_cost_price_avg = now_cost_amt / now_qty
            + 或者是`成本调价单`，刷新加权价 now_cost_price_avg = s_real_price 

     + HappenNow

       1. now_qty、now_cost_amt、now_cost_price_avg 为均已知

       2. 对 lstDealSheetItems 即当前本单据，根据货值/总数量 进行重算成本价

          + X,T,YK,BS 货值 = 开单时的加权价 * 数量

          + CG、CT 货值 = 单据金额

          + 数量都是 库存 + 变化量

       3. now_qty < 0 || pre_qty < 0 质疑，本单据加权价 修改 为当前取得的价格  不影响商品档案加权价

       4. 否则，如果是采购单且变化量为正数

     + 对以下情况不处理

       1. 采购数量为负数

+ 成本调价单逻辑

  1. 首先修改 lstHistorySheetItems ，在成本调价单红冲时，需注意会修改 s_real_price
  2. 会修改货值 、 加权价 


### 四、研发自我测试

| 版本  | 时间     | 测试功能                  | 功能场景 | 测试结果           | 预期结果   |
| ----- | -------- | ------------------------- | -------- | ------------------ | ---------- |
| 1.0.0 | 2022.1.5 | 成本调价单-重算加权价     |          | :heavy_check_mark: | 更改加权价 |
|       |          | 红冲成本调价单-恢复加权价 |          | :heavy_check_mark: | 恢复加权价 |

### 五、 版本记录

| 版本  | 时间           | 修改状态                                                     | 相关人员 |
| ----- | -------------- | ------------------------------------------------------------ | -------- |
| 1.0.0 | 2022.1.5 19:11 | 初步完成，等待研发自我测试 :heavy_check_mark:                | 研发：刘 |
|       | 2022.1.5 19:11 | 初步测试完成                                                 | 测试：刘 |
| 1.0.1 | 2022.1.5 19:27 | 在修改成本价时，还需更改商品档案里的进价；打开单据时看不到进价，且应考虑权限 | 测试：刘 |
|       | 2022.1.5 20:00 | 修复上述bug                                                  | 研发：刘 |



## 拜访计划  1.6 - 

使用前提：

### 前期：舟谱功能分析 1.6 

相关页面：[拜访任务](#拜访任务(查看)) [创建拜访任务](#创建拜访任务) [策略列表](#策略列表) [业务员拜访记录](#业务员拜访记录)

### 一、 功能目的

业务员在拜访客户时，需要对客户列表进行分组，根据`拜访日程`，按顺序一家一家拜访

拜访日程----根据客户某些特征手动，将客户进行分组

最终，业务员使用软件时，在  拜访  页面，选择 按行程/附近，然后根据显示的日程列表，按序拜访，也可以点击跳过，或临时拜访

### 二、需求细节与难点 1.6 - 1.7

+ 编辑创建拜访行程

  行程中包含很多日程安排，每个日程在修改编辑时，可以根据选择客户进行组合

  + 客户
  + 日程

  | 行程  | 业务员 | 日程名称 | 日程                    | 详情                                                         |
  | ----- | ------ | -------- | ----------------------- | ------------------------------------------------------------ |
  | 行程1 |        | 1        | 1. 客户1、客户2、客户3  | 图标--搜索，图标--地图，图标--查看(查看里面包含，顺序号、客户名称、客户其他信息) |
  | 行程2 |        | 2        | 2. 客户A、客户B、客户C  |                                                              |
  | 行程3 |        | 3        | 3. 客户1、客户A、客户一 |                                                              |

  + 业务员 -- 业务员只能选择任一线路

  | 业务员 | 日程     |
  | ------ | -------- |
  | 老吴   | 周一日程 |

  

+ 拜访界面按日程显示客户
  + 页面会根据业务员以及日程完成序号，按顺序循环显示日程 ---- 选择业务员

### 三、开发流程 1.6

#### 1. 需求分析

#### 2. 行程对应表格

##### 创建 oper_info -- 记录业务员当前拜访状态

```sql
create table oper_info(company_id integer not null, oper_id integer not null, visit_info jsonb, primary key(company_id,oper_id));
```

##### 创建 info_visit_schedule

```sql
create table info_visit_schedule(company_id integer not null, schedule_id serial primary key,schedule_name text,py_str text, status int2) ;
```

##### 创建 info_visit_day/info_visit_day_client 

```sql
create table info_visit_day(company_id integer not null,day_id serial,schedule_id integer not null,day_name text,py_str text,status int2);

create table info_visit_day_client(company_id integer not null,order_index integer,day_id integer not null,supcust_id integer not null);

```

##### 创建info_visit_task -- 拜访任务表 之后可用

```sql
create table info_visit_task_main(company_id integer not null, task_id serial, seller_id integer,task_finished bool, make_time timestamp,brief text);

create table info_visit_task_detail(company_id integer not null, task_id integer, order_index integer, supcust_id integer not null, finished bool );
```

字段

| 字段名     | 字段类型 | 是否可空                 | 字段注释                                                     | 字段说明                               |
| ---------- | -------- | ------------------------ | ------------------------------------------------------------ | -------------------------------------- |
| visit_info | jsonb    | :heavy_multiplication_x: | 存储业务员当前的拜访状态，拜访到哪个日程哪个客户(下一个即将拜访) | 存储格式：[{"dayID":1,"clientID":555}] |

需要呈现数据 {"schedule_id,":55,"oper_id":"10,25,55"}

##### 修改 sheet_visit

```sql
alter table sheet_visit add column is_skip bool default false;
alter table sheet_visit add column day_id integer;
```

字段

| 字段名  | 字段类型 | 是否可空           | 字段注释                                   | 字段说明      |
| ------- | -------- | ------------------ | ------------------------------------------ | ------------- |
| is_skip | bool     | :heavy_check_mark: | 存储业务员 是否将当前客户跳过拜访          | 存储格式：t/f |
| day_id  | integer  | :heavy_check_mark: | 记录当前的日程id，从而判断是否是计划外客户 | 存储格式：int |

##### 修改 info_operator

```sql
alter table info_operator add column visit_schedule_id text;
```

字段

| 字段名            | 字段类型 | 是否可空           | 字段注释                 | 字段说明 |
| ----------------- | -------- | ------------------ | ------------------------ | -------- |
| visit_schedule_id | text     | :heavy_check_mark: | 存储 分配给业务员的 行程 |          |

#### 3. 页面 

+ 行程页面 : 行程 -- 业务员 -- 日程编辑
  + 行程编辑 -- 行程名称、状态
+ 制作日程页面 

#### 4. 功能主要逻辑 

+ 首选侧边栏显示 行程
  + mother_id 为0，名称为全部
  + 右击添加、编辑、删除
  + 点击行程，可以选择业务员
+ 选中行程，右边点击添加，可以添加对应日程，弹出 类似于价格方案页面

#### 5. 后续由团队其他成员研发

### 四、研发自我测试

| 版本  | 时间      | 测试功能       | 功能场景 | 测试结果           | 预期结果         |
| ----- | --------- | -------------- | -------- | ------------------ | ---------------- |
| 1.0.0 | 2022.1.17 | 制作行程、日程 |          | :heavy_check_mark: | 添加、修改、删除 |
|       |           |                |          |                    |                  |





## 负库存改造

使用前提：

调拨单需要输数据 sheet_atrribute

仓库档案刷数据 negative_stock_accordance =’real‘



### 一、功能目的

限制负库存开单的权限 库存依据-可用库存

### 二、需求细节与难点

才仓库档案里，勾选负库存限制

+ 仅不允许负库存 

  + 开其他单据 

    比较 当前` 单据变化数量`和当前`可用库存 `

  + 开销售单

    + 正常审核

      + 普通销售单：比较 当前`销售单数量`和当前`可用库存 `

      + 转单销售单：当前销售单可以修改数量，比较`当前销售单数量`和 `可用库存+订单数量` 
    + 红冲单

      + 普通销售单：比较 当前`销售单数量`和当前`可用库存 `
      + 转单销售单：红冲时，占用库存恢复，比较当前单据数量 和 可用库存-订单数量

  + 开调拨单

    + 正常审核
      + 普通调拨单： 比较 当前`出入仓数量`和当前`可用库存 `
      + 装车调拨： 

+ 仅不允许订单负库存 

  + 开销售订单
    + 正常情况：比较销售订单数量和当前可用库存
    + 红冲： 比较销售订单数量

  根据可用库存，直接判断开订单数量与 可用库存数量

### 三、研发流程：

#### 1. 需求分析 1.17

#### 2. 修改对应数据库表格

```sql
alter table info_branch add column allow_negative_stock_order bool
alter table info_branch add column negative_stock_accordance text
```

字段

| 字段名                     | 字段类型 | 是否可空           | 字段注释              | 字段说明    |
| -------------------------- | -------- | ------------------ | --------------------- | ----------- |
| allow_negative_stock_order | bool     | :heavy_check_mark: | 允许订单负库存        |             |
| negative_stock_accordance  | text     | :heavy_check_mark: | 负库存依据: 实际/可用 | real/usable |

```sql
alter table sheet_move_main add column sheet_attribute json
```

字段

| 字段名          | 字段类型 | 是否可空           | 字段注释           | 字段说明                                                     |
| --------------- | -------- | ------------------ | ------------------ | ------------------------------------------------------------ |
| sheet_attribute | json     | :heavy_check_mark: | 存储调拨单相关信息 | {"assignVan":"assign/back","saleOrderSheetIDs":"","saleOrderSheetNos":""} |

#### 3. 页面

在仓库档案中，添加允许订单负库存

#### 4. 主要逻辑

##### 1. 仓库档案正常保存

##### 2. 修改 允许负库存 逻辑

1. 抓住重点 负库存条件 

   + => 出现负的可用库存 => 本单据结束后的可用库存 == 库存 - 开单后的占用库存 + 本单据的变化量 < 0

     即  `库存 - 开单后的占用库存 + 本单据的变化量 < 0 `

   + => 出现负的实际库存 => 本单据结束后的可用库存 == 库存 + 本单据的变化量 < 0

     即  `库存 + 本单据的变化量 < 0 `

2. 开单后的占用库存 

   + 销售订单 
     + 占用库存 => 仓库占用库存 + 订单变化量 
     + 红冲时，占用库存增加 => 仓库占用库存 - 订单变化量 

   + 销售订单 转单后的销售单 
     + 占用库存减少 => 仓库占用库存 + 订单变化量 
     + 红冲时，占用库存增加 => 仓库占用库存 - 订单变化量 
   + 销售订单 装车调拨且需转移库存后的 调拨单 
     + 出仓占用库存减少 => 出仓占用库存 + 订单变化量 
     + 红冲时，出仓占用库存增加 => 出仓占用库存 - 订单变化量 
   + 其他单据 占用库存不影响 

3. 本单据的变化量 quantity * unit_factor * inout_flag

##### 3. 修改 订单允许负库存 逻辑

当单据类型为销售订单时，单据的 仓库允许负库存--改为 订单允许负库存，逻辑不变

### 四、研发测试

| 版本  | 时间      | 测试功能 | 功能场景 | 测试结果           | 预期结果 |
| ----- | --------- | -------- | -------- | ------------------ | -------- |
| 1.0.0 | 2022.1.20 | 负库存   |          | :heavy_check_mark: |          |
|       |           |          |          |                    |          |
|       |           |          |          |                    |          |
|       |           |          |          |                    |          |

疑问场景：  :question:

+ 开单前是负库存，开单后库存变正，若红冲使库存变负，将不允许。 如：库存-1，开退货-5，再红冲将不允许
+ 销售订单开负的，不影响占用库存
+ 当订单允许负库存时，会出现负可用库存
+ 盘点单 只参考实际库存





## 电脑端借货、还货/2022-1-25

### 一、 功能目的：

电脑端商品行选择交易方式--借货，之后在商品档案里显示还货商品

### 二、需求细节与难点：

在销售单、销售订单开单页面商品行，添加交易方式

+ 选择交易方式为借货

  + 商品价格 带出为0
  + 备注为借货
  + 查看单据页面，显示借/还

+ 当商品档案里有可用还货数量

  + 商品档案选择商品改造

  + 商品价格 带出为0
  + 备注为还货
  + 查看单据页面，显示借/还

### 三、开发流程：

##### 1. 需求分析 2022.1.25

##### 2. 创建相关表

无

##### 3. 页面 

+ 销售单、销售订单开单页面商品行，添加交易方式
  + 交易方式里，自定义
+ 查看单据页面，显示借/还
+ 选择交易方式为借货
  + 商品价格 带出为0
  + 备注为借货
  + 查看单据页面，显示借/还
+ 当商品档案里有可用还货数量
  + 商品价格 带出为0
  + 备注为还货
  + 查看单据页面，显示借/还

##### 4. 功能主要逻辑

无需修改核心逻辑

修改商品档案选择商品、带出价格问题

##### 5. 研发团队内部优化修改  

### 四、开发笔记：

### 五、研发自我测试

| 版本  | 时间      | 测试功能         | 功能场景   | 测试结果           | 预期结果 |
| ----- | --------- | ---------------- | ---------- | ------------------ | -------- |
| 1.0.0 | 2022.1.25 | 页面使用借货还货 | 销售单开单 | :heavy_check_mark: |          |

### 六、 版本记录

| 版本  | 时间      | 修改状态 | 相关人员 |
| ----- | --------- | -------- | -------- |
| 1.0.0 | 2022.1.25 |          |          |





## 统一收欠款、用预收款/2022.1.26

### 一、 功能目的：

在使用软件时，新建客户，选择类型、结算单位

在开销售单时，会加载结算单位，开单时产生的欠款，将记在结算单位上；开单时使用的预收款余额，也是结算单位的预收款余额

开预收款单、定货会、定货会调整单时，选择的是结算单位与客户(无门店)，产生的余额都记在结算单位的预收款余额上

开收款单，选择结算单位，展示结算单位下门店的所有欠款单据

预收、往来账报表只能查询，结算单位与客户(无门店)

### 二、需求细节与难点：

+ 欠款逻辑
  + 产生欠款的单据，查出开单客户的是否有结算单位 cust_id
    + 有的话，arrears_balance 的  supcust_id = cust_id；client_account_history 的 supcust_id  = cust_id
  + 收欠款单，选择的客户 -- 结算单位
    + 查询单据时 in supcusts
+ 预收款逻辑
  + 产生预收款的单据，选择客户 -- 结算单位 cust_id
    + 预收款单/定货会
  + 使用预收款的单据，开单客户的是否有结算单位 cust_id
    + 有的话，prepay_balance supcust_id = cust_id， client_account_history 的 supcust_id 

### 三、开发流程：

##### 1. 需求分析

##### 2. 创建相关表

##### 3. 页面

+ 选择客户，显示欠款和预收款余额时的逻辑
  + 需要显示结算单位
  + 开单时在sheet_attribute中，保存acct_cust_id
+ 预收款余额报表：显示客户
+ 往来账中显示的客户：

##### 4. 功能主要逻辑

电脑端：

+ X、XD、
  + 开单时选择客户 -- 客户/结算单位  得到custID，保存sheet_attribute
  + 产生欠款/使用预收款  -- custID
+ 预收款单、定货会、定货会调整单
  + 选择客户 -- 选择的是结算单位  即 supcust_id
  + 产生欠款/使用预收款  -- supcust_id
+ 收款单
  + 选择单据时，选择结算单位
  + 接口：找出单位下门店的单据

手机端：

+ X、XD
  + 选择客户接口，拿到 acct_cust_id，acct_cust_name    :heavy_check_mark:
  + 显示客户账户信息接口，拿到 acct_cust_id，acct_cust_name :heavy_check_mark:
  + 保存单据，传acct_cust_id
+ 预收款单或者定货会
  + 选择客户 -- 选择的是结算单位  即 supcust_id   当传参`showAcctCusts=true` :heavy_check_mark:
+ 收款单
  + 选择单据时，选择结算单位    当传参`showAcctCusts=true` :heavy_check_mark:
  + 接口：找出单位下门店的单据 :heavy_check_mark:

##### 5. 研发团队内部优化修改  

### 四、开发笔记：

### 五、研发自我测试

| 版本  | 时间      | 测试功能                     | 功能场景 | 测试结果 | 预期结果 |
| ----- | --------- | ---------------------------- | -------- | -------- | -------- |
| 1.0.0 | 2022.1.26 | 电脑端统一收欠款、使用预收款 |          |          |          |

### 六、 版本记录

| 版本  | 时间 | 修改状态 | 相关人员 |
| ----- | ---- | -------- | -------- |
| 1.0.0 |      |          |          |



## 红冲时记忆价格恢复/2022-2-9

### 一、 功能目的：

在销售单、销售订单、采购单红冲时，记忆价格应该恢复

### 二、需求细节与难点：

首先对于记忆价格，红冲后，记忆价格是该单据的系统推荐价格

记忆原价应该是销售单的原价

### 三、开发流程：

##### 功能主要逻辑

+ 首先修改记忆价格商品行信息，对于红冲单，也需要获取 记忆价格商品行
+ 红冲时，记忆价格是该单据的`系统推荐价格`

### 五、研发自我测试

| 版本   | 时间     | 测试功能 | 功能场景 | 测试结果 | 预期结果 |
| ------ | -------- | -------- | -------- | -------- | -------- |
| v1.0.0 | 2022-2-9 |          |          |          |          |



## 交账单接口 添加预付款单/ 2022-2-9

注意事项：需要修改 sheet_check_sheets_main 刷数据

### 一、 功能目的：

在交账时，添加预付款单

### 二、需求细节与难点：

接口修改

### 三、开发流程：

##### 创建相关表 

```sql
alter table sheet_check_sheets_main RENAME COLUMN get_prepay TO get_preget;
alter table sheet_check_sheets_main RENAME COLUMN prepay_total_amount TO preget_total_amount;
alter table sheet_check_sheets_main RENAME COLUMN prepay_disc_amount TO preget_disc_amount;
alter table sheet_check_sheets_main RENAME COLUMN prepay_left_amount TO preget_left_amount;
alter table sheet_check_sheets_main add get_prepay float8;
alter table sheet_check_sheets_main add prepay_total_amount float8;
alter table sheet_check_sheets_main add prepay_disc_amount float8;
alter table sheet_check_sheets_main add prepay_left_amount float8;
```

##### 4. 功能主要逻辑

+ 在sheet_prepay表中查出数据，根据sheet_type，拆分成两个list返回

### 五、研发自我测试

| 版本   | 时间      | 测试功能                       | 功能场景 | 测试结果                 | 预期结果                 |
| ------ | --------- | ------------------------------ | -------- | ------------------------ | ------------------------ |
| v1.0.0 | 2022-2-9  | 交账单返回相关数据             |          | 数据返回正常             | 数据返回正常             |
| v1.0.1 | 2022-2-10 | 交账单预付款欠款存在正负号问题 |          | 交账单预付款欠款返回负数 | 交账单预付款欠款返回负数 |





# 财务需求

## 会计科目

会计科目明确科目的借贷方向，一级科目有严格的科目代码

### 必备科目

| 类别     | 科目编码 | 科目名称     | 方向 |
| -------- | -------- | ------------ | ---- |
|          | 1        | 资产类       | 借   |
| 资产类   | 10       | 流动资产     | 借   |
| 流动资产 | 1001     | 库存现金     | 借   |
| 流动资产 | 1002     | 银行存款     | 借   |
| 流动资产 | 1012     | 其他货币资金 | 借   |
| 流动资产 | 1122     | 应收账款     | 借   |
| 流动资产 | 1123     | 预收账款     | 借   |
| 流动资产 | 1405     | 库存商品     | 借   |
| 流动资产 | 1410     | 商品进销差价 | 贷   |
|          | 2        | 负债类       | 贷   |
| 负债类   | 20       | 流动负债     | 贷   |
| 流动负债 | 2202     | 应付账款     | 贷   |
| 流动负债 | 2203     | 预收账款     | 贷   |
|          | 3        | 所有者权益类 | 贷   |
|          | 4        | 成本费用     | 借   |
|          | 6        | 损益         |      |
| 损益     | 60       | 收入         | 贷   |
| 收入     | 6001     | 主营业务收入 | 贷   |
| 收入     | 6051     | 其他业务收入 | 贷   |
| 损益     | 64       | 费用         | 借   |
| 费用     | 6401     | 主营业务成本 | 借   |
| 费用     | 6601     | 销售费用     | 借   |
| 费用     | 6602     | 管理费用     | 借   |
| 费用     | 6603     | 财务费用     | 借   |

## 业务单据自动生成凭证

首先在公司设置里，选择是否自动生成凭证

### 一、 功能目的：

开完单据后，自动生成凭证

### 二、需求细节与难点：

不同业务单据生成的凭证，需要注意凭证备注、科目、科目金额、科目借贷方向

### 三、开发流程：

##### 1. 需求分析

不同业务单据的 凭证模板：

+ 正常销售单 / 红冲退货单 / 退货单开销售
  + 借 支付方式/财务费用(优惠)/应收账款(欠款)
  + 贷 主营业务收入
+ 正常退货单 / 红冲销售单 / 销售单开退货
  + 借 主营业务收入
  + 贷 支付方式/财务费用(优惠)/应收账款(欠款)
+ 正常采购单 / 红冲采购退货 / 采购退货开采购
  + 借 库存商品
  + 贷 支付方式/应付账款/财务费用
+ 正常采购退货单 / 红冲采购单 / 采购单开退货
  + 借 支付方式/应付账款/财务费用
  + 贷 库存商品
+ 预收 / 定货 / 定货调整
  + 借 支付方式/财务费用/应收账款
  + 贷 开单时选择的预收账户
+ 预付
  + 借 开单时选择的预付账户
  + 贷 支付方式/财务费用/应付账款
+ 收款
  + 借 支付方式/财务费用
  + 贷 应收账款
+ 付款
  + 借 应付账款
  + 贷 支付方式/财务费用
+ 费用支出
  + 借 选择的费用支出科目
  + 贷 支付方式
+ 其他收入
  + 借 支付方式
  + 贷 选择的收入科目
+ 盘点单 对应的盘盈 / 盘亏
  + 盘盈 借库存商品，贷 管理费用 -- 对应金额为 sum(数量差*加权价)
  + 盘亏 借管理费用，贷库存商品
+ 成本调价单 对应的调高 / 调低
  + 调高 借库存商品，贷 商品进销差价 -- 对应金额为 sum(总库存*差价)
  + 调低 借 商品进销差价，贷库存商品

##### 2. 创建相关表

凭证与业务单据对应表

```sql
create table cw_voucher_sheet_mapper(
 company_id integer,
 voucher_id integer,
 business_sheet_type text,
 business_sheet_id integer,constraint pk_cw_voucher_sheet_mapper primary key(company_id,voucher_id,business_sheet_type,business_sheet_id)
) partition by hash(company_id)
CREATE TABLE cw_voucher_sheet_mapper_0 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 0);
CREATE TABLE cw_voucher_sheet_mapper_1 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 1);
CREATE TABLE cw_voucher_sheet_mapper_2 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 2);
CREATE TABLE cw_voucher_sheet_mapper_3 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 3);
CREATE TABLE cw_voucher_sheet_mapper_4 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 4);
CREATE TABLE cw_voucher_sheet_mapper_5 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 5);
CREATE TABLE cw_voucher_sheet_mapper_6 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 6);
CREATE TABLE cw_voucher_sheet_mapper_7 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 7);
CREATE TABLE cw_voucher_sheet_mapper_8 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 8);
CREATE TABLE cw_voucher_sheet_mapper_9 PARTITION OF cw_voucher_sheet_mapper FOR VALUES WITH(MODULUS 10, REMAINDER 9);


```

表：company_setting中的setting

| 字段名            | 字段类型  | 是否可空 | 字段注释             | 字段说明(存储格式) |
| ----------------- | --------- | -------- | -------------------- | ------------------ |
| useAccounting | bool      |          | 启用财务功能         |   false                 |
| autoCreateVoucher | bool      |          | 业务单据自动创建凭证 |   false                 |
| autoApproveVoucher | bool      |          | 业务单据生凭证自动审核 |                    |
| accountingPeriod  | timestamp |          | 当前会计账期         | 2021-09           |
| openAccountPeriod  | timestamp |          | 开账会计账期         | 2021-09           |
| accountingCodeLength  | text |          | 编码长度        | [4,2,2,2,2]           |
| saleIncomeAndCost  | bool |          | 销售收入和成本同时生成         | false           |
| cwPeriodFromTo  | text |          | 财务开账账期始末         | 2021-09-01 ~ 2021-09-30           |

##### 3. 页面

在公司设置页面添加两个勾选框

##### 4. 功能主要逻辑

在每个单据审核时，根据单据数据 生成 对应凭证模板 ToVoucherRows，然后在 OnSheetIDGot 后调用 toVoucher 方法，调用Voucher的审核方法

对于 生成 voucherRows ：根据单据信息，查找相关的subInfo，判断借、贷方金额，对应到凭证的 voucherRows 中



## 批量生成凭证

### 一、功能目的

在批量生产凭证页面中，通过勾选，进行多张单据生成凭证，其中可以选择是否按业务单据，进行多张合并，是同一类业务单据只生成一张单据，金额累加

### 二、需求细节与难点

通过多选，得到不同单据 的 单据类型和单据号，然后加载出单据对应数据，生成 voucherRows ，最终通过 sheetCwVoucher.SaveAndApprove 生成凭证

### 三、开发流程

1. 需求分析

2. 创建相关表

3. 页面

   批量生产凭证页面

   + 左边是写死 的列表
   + 右面是 根据经营历程改造的 查询所有未生成凭证的 业务单据

4. 功能主要逻辑

   1. 通过多选，得到不同单据 的 单据类型和单据号

   2. 生成凭证

      + 如果不需要按业务合并 -- 类似于自动生成凭证
        + 遍历多选的单据ID，对每个单据创建 TSheet对象，调用load方法，然后直接 调用 ToVoucherRows，最终通过 sheetCwVoucher.SaveAndApprove 生成凭证

      ```c#
      SheetSale sheetSale = new SheetSale(SHEET_RETURN.EMPTY, MyJXC.LOAD_PURPOSE.SHOW);
      await sheetSale.Load(cmd, companyID, sheetID);
      record = await sheetSale.ToVoucherRows(cmd, sheetID, sheetCwVoucher, payway);
      
      if (record != null && record.Value.msg != "")
      {
          msg = record.Value.msg;
          break;
      }
      msg = await record.Value.sheetCwVoucher.SaveAndApprove(cmd);
      ```

      + 按业务单据 合并
        + 首先对多选的信息，生成 Dictionary<string,string> sheetsDic ；其中 key 是 sheetType，value 是 逗号分割的 sheetIDs ---- 为了调用 LoadMultiSheets 方法，一次性获取 某一类单据 
        + 然后调用 LoadMultiSheets ，对结果集进行遍历，对金额进行加减，得到新的合并后的 单据总金额 即，payways
        + 调用 ToVoucherRows，最终通过 sheetCwVoucher.SaveAndApprove 生成凭证



# 优化

## jquery 使用 promise 封装 ajax

```javascript
$(function () {
    let request = function (url, method, params, body = false) {
        
        return new Promise((resolve, reject) => {
            let aj = {
                url: url,
                type: method,
                data: params,
                success(res) {
                    resolve(res)
                },
                error(error) {
                    reject(error)
                }
            }
            
            if (params && body) {
                aj.contentType = 'application/json'
                aj.data = JSON.stringify(params)
            }

            $.ajax(aj);

        })
    }

    window.myAjax = {
        get(url, params) {
            return request(url, 'GET', params)
        },

        post(url, data) {
            return request(url, 'POST', data, true)
        },
        
        put(url, data) {
            return request(url, 'PUT', data, true)
        },
        
        delete(url, data) {
            return request(url, 'DELETE', data, true)
        }
    }
})
```
