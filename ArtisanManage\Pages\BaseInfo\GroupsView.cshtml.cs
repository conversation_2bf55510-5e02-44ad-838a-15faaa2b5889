﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class GroupsViewModel : PageQueryModel
    {
        public string m_classTreeStr = "";
        public bool ForSelect = false;
       
        /// <summary>
        /// 43165qgreqfgreg
        /// </summary>
        public GroupsViewModel(CMySbCommand cmd) : base(Services.MenuId.infoClient)
        {
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                 {"searchString",new DataItem(){Title="检索字符串",PlaceHolder="输入名称",UseJQWidgets=false, SqlFld="group_name",ButtonUsage="list",QueryOnChange=true,CompareOperator="like"}},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      IdColumn="i",TableName="info_supcust_group",
                     ShowContextMenu=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"i",new DataItem(){Title="编号",SqlFld="group_id", Width="80",Hidden=true,HideOnLoad = true}},
                       {"group_name",new DataItem(){Title="名称",SqlFld="group_name", Width="180",Linkable=true}},
                       {"remark",new DataItem(){Title="备注",SqlFld="remark", Width="180"}},
                       {"order_index",new DataItem(){Title="显示顺序",SqlFld="order_index", Width="180"}}
                     },
                     QueryFromSQL="from info_supcust_group" ,QueryOrderSQL="order by order_index asc"
                  }
                } 
            }; 
        }
        public async Task OnGet(string forSelect)
        {  
            await InitGet(cmd);
            ForSelect = forSelect == "1";
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
        public override async Task<string> CheckBeforeDeleteRecords(string rowIDs)
        {
            cmd.CommandText = $"select company_id from info_supcust where sup_group in ({rowIDs}) and company_id={company_id} limit 1";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) return "该渠道正在使用,无法删除";

            return "";
        }
    }



    [Route("api/[controller]/[action]")]
    public class GroupsViewController : QueryController
    { 
        public GroupsViewController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            GroupsViewModel model = new GroupsViewModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {// string gridID,int startRow,int endRow,bool bNewQuery){
            GroupsViewModel model = new GroupsViewModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);// gridID, startRow, endRow, bNewQuery);
            return records;
        }
        [HttpPost]
        public async Task<object> DeleteRecords([FromBody] dynamic data)
        {
            GroupsViewModel model = new GroupsViewModel(cmd);
            object records = await model.DeleteRecords(data, cmd, "info_supcust_group");// gridID, startRow, endRow, bNewQuery);
            return records;
        }
    }
}
