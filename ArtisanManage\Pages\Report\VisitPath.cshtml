@page
@model ArtisanManage.VisitPathModel
@{
    Layout = null;
}


<!DOCTYPE html>
<html lang="en">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxgrid.aggregates.js"></script>
    <link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)">
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=1.0&type=webgl&ak=@Html.Raw(Model.BaiduKey)"></script>
    <!-- <script type="text/javascript" src="~/uploads/templates/yingjiang_lushu.js"></script>-->
  <script type="text/javascript" src="https://bj.bcebos.com/v1/mapopen/github/BMapGLLib/Lushu/src/Lushu.min.js"></script>  <!--<title><%= htmlWebpackPlugin.options.title %></title>  -->
   <script src="https://mapv.baidu.com/build/mapv.js"></script>
    <script src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.159/dist/mapvgl.min.js"></script>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.onload = function () {
            lookSellerCurPosition();
        }
        var LeftMoving = false;

        var RightMoving = 1;
        var t = null;
        var xl = -1;
        var xr = -1;
        $(document).mousemove(function (e) {
            e = e || window.event;
            xl = e.clientX;
            xr = parseInt($('#root').css('width')) - xl;
            var left = parseInt($('.left-container').css('left'));
           
            if (xl > 0 && xl < 200 && !LeftMoving && !t && left == -200) {
                console.log('x2 :', xl, 't :', t);
                t = setTimeout(function () {
                    if (xl > 40 && xl < 200 && !LeftMoving && left == -200) {
                        console.log('x3 :', xl, 't :', t);
                            $('.arrowleft').css("display", "none");
                            $('.left-container').css("left", "-200px");
                            $('.left-container').animate({ left: '0px' }, 500);

                            LeftMoving = true;

                    }
                    t = null;
                }, 300);
            }
            var right = parseInt($('.right-container').css('right'));
            if (xr > 0 && xr < 200 && !RightMoving && !t && right == -200 ) {
                t = setTimeout(function () {
                    if (xr > 0 && xr < 200 && !RightMoving && right == -200) {
                            $('.arrowright').css("display", "none");
                            $('.right-container').css("right", "-200px").show();
                            $('.right-container').animate({ right: '0px' }, 500);
                            RightMoving = true;

                    }
                    t = null;
                }, 300);
            }

        })

        $(document).mouseleave(function (e) {
            console.log('leave ');
            xl = -1;
            xr = -1;
        })


        function onMouseLeavePannelLeft(e) {
            var left = $('.left-container').offset().left;
            if (left == 0 && LeftMoving) {

                $('.left-container').animate({ left: '-200px' }, 1000, function () {
                    $('.arrowleft').css("display", "block")
                })

                app.showCalendar = false;

                LeftMoving = false;

            }
        }


        function onMouseLeavePannelRight(e) {
            var right = parseInt($('.right-container').css('right'));
            if (right == 0 && RightMoving ) {
                $('.right-container').animate({ right: '-200px' }, 1000, function () {
                    $('.arrowright').css("display", "block")
                })
                RightMoving = false;

            }
         }

        function rightshow() {
            $('.arrowright').css("display", "none");
            $('.right-container').animate({ right: '-200px' }, 500);
            $('.right-container').animate({ right: '0px' }, 500);
            RightMoving = true;

        }
        $(document).ready(function () {

            $(document).click(function (e) {


                e = e || window.event;
                var x = e.clientX; y = e.clientY;
                var right = parseInt($('.right-container').css('right'));
                if (x > 400 && x < 1500) {
                    app.pause();
                    if (right == 0 && RightMoving) {
                        $('.right-container').animate({ right: '-200px' }, 1000, function () {
                            $('.arrowright').css("display", "block")
                        })
                        RightMoving = false;;
                    }
                   
                  
                }
            })

        })
		// 全屏事件
        function btnFullScreen_click() {
            let element = document.documentElement;
            if (window.isFullScreen) {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitCancelFullScreen) {
                    document.webkitCancelFullScreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                window.isFullScreen=false
            } else {
                if (element.requestFullscreen) {
                    element.requestFullscreen();
                } else if (element.webkitRequestFullScreen) {
                    element.webkitRequestFullScreen();
                } else if (element.mozRequestFullScreen) {
                    element.mozRequestFullScreen();
                } else if (element.msRequestFullscreen) {
                    // IE11
                    element.msRequestFullscreen();
                }
                window.isFullScreen=true

            }

        }
        //关闭屏幕
        function closeScreen() {
            let element = document.documentElement;
            if (window.isFullScreen) {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitCancelFullScreen) {
                    document.webkitCancelFullScreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                window.isFullScreen = false
            }
        }


    </script>
    <style>
        ::-webkit-scrollbar {
            display: none;
        }

        html, body, #allmap {
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        [v-cloak] {
            display: none;
        }
        .BMap_noprint img :last-of-type {
            display: none !important;
        }

        .BMap_Marker .BMap_noprint {
            background: none !important;
        }

        .marker-board-container {
            width: 30rem;
            height: 15rem;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .supcust-info-container {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        #imgDemo {
            height: 100px;
            width: 100px;
        }

        .supcust-info-text-container {
            padding-left: 2rem;
            padding-right: 2rem;
            font-size: 1rem;
        }

        .choose-date-btn {
            /*margin-bottom: 20px;*/
            width: 150px;
            margin-left: 25px;
            padding: 8px;
            border-radius: 12px;
            background: #000;
            font-size: 20px;
        }

        .right-seller-container {
            width: 150px;
            height: 100%;
            right: 0px;
            top: 60px;
            background: white;
            position: absolute;
        }

        .el-calendar {
            height: 250px;
            width: 350px;
            z-index: 9;
            background: #000;
            color: #fff;
            /*border: 2px solid yellow;*/
        }

        .el-calendar-table .el-calendar-day {
            height: 20px;
        }

        .el-button el-button--plain el-button--mini {
            height: 20px;
            width: 30px;
            background: #000000;
            color: #fff;
        }

        .el-button-group > .el-button:hover {
            background-color: #c52d2d;
            color: #fff;
        }

        .el-calendar-table thead th {
            color: #fff;
        }

        .el-calendar__header {
            width: 310px;
            font-size: 10px;
            background-color: #000;
            display: flex;
            padding: 5px;
            align-items: center;
            color: #fff;
            margin-left: 15px;
        }

        .el-calendar__title {
            color: #fff;
            align-self: center;
            font-size: 14px;
        }

        .el-button {
            background: #000;
            color: #fff;
        }

        .el-calendar-day {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .el-calendar-table .el-calendar-day:hover {
            background-color: #c52d2d;
        }

        .el-calendar-table td.is-selected {
            background-color: #bb5858;
        }

        .el-calendar-table td.is-today {
            color: #f19409;
        }

        .el-calendar__body {
            width: 300px;
            font-size: 10px;
        }

        .seller-container_body {
            overflow-y: auto;
            overflow-x: hidden;
            width: 200px;
            height: calc( 100vh - 150px);
        }

        .seller-container_header {
            display: flex;
            flex-direction: row;
        }

        .seller-container_header_icon {
            width: 4px;
            height: 18px;
            background-color: #c40000;
        }

        .seller-container_body_item:hover {
            background-color: #bb5858;
        }

        .seller-container_body_item {
            margin-top: 2px;
            font-size: 18px;
            margin-left: 2px;
            border-radius: 2px;
        }

        .seller-container_header_text {
            margin-left: 8px;
            font-size: 14px;
        }

        .seller-container_body_item_title {
            margin-left: 10px;
            padding: 8px;
            display: inline-block;
            width: 90px;
        }

        .seller-container_body_item_content {
            padding: 8px;
            display: inline-block;
            width: 30px;
            margin-left: 12px;
            height: 30px;
            width: 50px;
            padding-right: 0;
        }

        .seller-container_body_item_divider {
            width: 100%;
            height: 2px;
            background-color: #eee;
        }



        .seller-selected {
            border-left: 4px solid #c40000;
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: row;
        }

        #allmap {
            width: 100%;
            height: 100%;
            position: absolute;
        }

        .info-header {
            float: left;
            position: absolute;
            display:flex;
            flex-direction:row;
            z-index: 5;
            left: 330px;
        }

        .info-header-item {
            width: 260px;
            font-size: 14px;
            height: 80px;
            border-right: 1px solid #eee;
            position:relative;
            background: #e4e7ed;
            padding: 8px;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
        }

            .info-header-item:hover {
                background-color: #ccc;
            }

        .timeline-selected {
            border: 2px solid #c40000;
        }

        .left-container {
            position: fixed;
            z-index: 9;
            left: -200px;
            width: 200px;
            height: 100%;
            background: #000;
            color: #fff;
            opacity: 0.75;
            /* display: none;*/
        }

        .box span {
            font-size: 16px;
            opacity: 0;
            transition: all 1s ease 0s;
        }

        .box:hover span {
            opacity: 1;
        }

        .middle-container {
            width: 100%;
        }

        .right-container {
            width: 200px;
            position: fixed;
            overflow-y: auto;
            height: 100%;
            /* right:0;*/
            right: -275px;
            /* display: none;*/
            opacity: 0.75;
            /* background-color:#aaa;*/
            background: #000;
            z-index: 9;
        }

        .visit-cell {
            display: flex;
            flex-direction: column;
            width: 200px
        }

        .el-timeline {
            width: 200px;
            padding-left: 15px;
            height: 100%;
            /*  padding-top:30px;  */
            border: 1px solid black;
            /*margin-top: 25px;*/
        }


        .el-timeline-item__wrapper {
            position: relative;
            padding-left: 20px;
        }

        .el-timeline-item__content {
            border-radius: 2px;
            width: 180px;
            color: #fff;
            font-size: 16px;
        }



        .visit-cell-bottom {
            display: flex;
            flex-direction: row;
            margin-top: 10px;
        }

        .visit-sale-amount {
            padding: 6px;
            margin-left: 4px;
            border-radius: 12px;
            background: #303133;
        }

        .visit-sale-amount:hover {
                color: #c40000;
         }
        .el-message--info {
            background: #000;
            opacity: 0.75;
            z-index: 9;
            
        }
        .el-message__content {
            background: #000 !important;
            opacity: 0.75;
            color: #fff !important;
        }
    </style>
</head>
<body>

    <div id="root">
        <div style="position:absolute;z-index:9; background: rgba(0,0,0,0.7); color: white; padding: 5px; display: none; border-radius: 4px;" id="tooltip"></div>
        <div class="container" v-vloak>
            <svg class="arrowleft" style="top: calc(50% - 50px); width:16px;height:100px;z-index: 9; position: fixed; display: block; ">
                <path d="M0 0  C0 20 ,16 30,16 50 C16 70,0 80,0 100 " fill="#000" style="stroke-width: 2px; opacity: 0.75;" />

            </svg>

               <div class="left-container" onmouseleave="onMouseLeavePannelLeft(event)" onmouseover="this.style.cursor='pointer'">
                <div style="display:flex;align-items:center;height:60px;margin-top:20px">
                    <div class="box" style="margin-left: 15px; width: 30px">
                        <svg @@click="lookSellerCurPosition()" style="height: 30px; width: 30px; cursor: pointer; margin-top: 5px " fill="#f55">
                            <use xlink:href="/images/images.svg?v=@Html.Raw(Model.Version)#bird" />
                        </svg>
                        <div style="width: 60px; height: 25.33px; display: block; margin-top: -6px ">
                            <span>鸟瞰</span>
                        </div>

                    </div>

                    <div class="box" style="margin-left: 37px; width: 30px;">
                        <svg @@click="runTrailClick()" style="height: 30px; width: 30px;  cursor: pointer; margin-top: 0px; " fill="#f33">
                            <use xlink:href="/images/images.svg?v=@Html.Raw(Model.Version)#trail" />
                        </svg>
                        <div style="width:60px;height:25.33px;display:block">
                            <span>轨迹</span>
                        </div>

                    </div>
                    <div class="box" style="margin-left: 42px; width: 30px">
                        <svg onclick="btnFullScreen_click()" style="height: 30px; width: 30px;  cursor: pointer; margin-top: 0px; " fill="#f33">
                            <use xlink:href="/images/images.svg?v=@Html.Raw(Model.Version)#fullscreen" />
                        </svg>
                        <div style="width:60px;height:25.33px;display:block">
                            <span>全屏</span>
                        </div>

                    </div>

                </div>


                <div  v-if="showCalendar" @@click="onCalendarClick" @@mouseleave="showCalendar=false"  class="calendar-container" style="position: fixed; top:0;left:200px; z-index:9">
                    <el-calendar v-model="calendarDate"></el-calendar>
                </div>
                <div @@click="showCalendar=true" class="choose-date-btn">{{convertDateStrToDay(calendarDate)}}</div>

                <div class="seller-container">
                    <div class="seller-container_body" >
                        <div class="seller-container_body_item" v-for="seller,index in sellerList" :index="index" @@click="sellerItemClick(seller)" onclick="rightshow()">
                            <div  class="seller-container_body_item_title">
                                {{seller.seller_name}}
                            </div>
                            <div class="seller-container_body_item_content">
                                {{seller.svcount===''?'0':seller.svcount}}次
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="middle-container">
                <div class="info-header">
                    <div @@click="visitInfoClick(item)" class="info-header-item" v-for="item,index in selectedVisitInfos" :index="index">
                        <div>店铺名：{{item.sup_name}}</div>
                        <div>在店：{{item.intervals}}分钟</div>
                        <div>拜访开始：{{item.start_time}}</div>
                        <div>拜访结束：{{item.end_time}}</div>
                    </div>
                </div>
                <div  id="allmap"></div>

            </div>
            <svg class="arrowright" style="right: 0px ;top: calc(50% - 50px); width: 16px; height: 100px; z-index: 9; position: fixed; display: none; transform: rotate(180deg)">
                <path d="M0 0  C0 20 ,16 30,16 50 C16 70,0 80,0 100 " fill="#000" style="stroke-width: 2px; opacity: 0.75;" />

            </svg>
           
            <div class="right-container" onmouseleave="onMouseLeavePannelRight(event)" onmouseover="this.style.cursor='pointer'">
                <div style="color:#fff;height:100px;padding-left:30px;margin-top:10px;">
                    <div @@click="clear()" >业务员：&nbsp&nbsp&nbsp{{SallerName}}</div>
                    <div>在店时长：{{Total_Interval||'0'}}分钟</div>
                    <div>销售金额：{{Total_sale||'0'}}元</div>
                    <div>订单金额：{{Total_sale_order||'0'}}元</div>
                    
                </div>
                

                <el-timeline>
                    <el-timeline-item  :color="visitPoint.timelineSelected?'#c40000':''" v-for="(visitPoint, index) in visitPoints " :key="index">
                        <div @@click="visitInfoClick(visitPoint)" class="visit-cell">
                            <div class="visit-cell-top">
                                {{visitPoint.sup_name}}
                                @*{{visitPoint.boss_name}}
                                {{visitPoint.mobile}}*@

                            </div>
                            <div>
                                拜访时间：{{visitPoint.happen_time.slice(-8)}}
                            </div>
                            <div>
                                签到距离:&nbsp&nbsp<span v-bind:style="{'color':visitPoint.distanceOutrange ? 'red':''}">{{format(visitPoint.distance,0)}}米</span>
                            </div>

                            <div>停留时间：{{visitPoint.intervals||'0'}}分钟</div>
                            <div class="visit-cell-bottom">
                                <div class="visit-sale-amount" onclick="closeScreen()" @@click="toSheet(visitPoint.sheet_id,'销售单')">销：{{visitPoint.sale_total_amount||'0'}}</div>
                                <div class="visit-sale-amount" onclick="closeScreen()" @@click="toSheet(visitPoint.sale_order_sheet_id,'销售订单')"> 订：{{visitPoint.sale_order_total_amount||'0'}}</div>
                            </div>
                        </div>
                    </el-timeline-item>
                </el-timeline>

            </div>
        </div>
    </div>
</body>
<script>
    var app = new Vue({
        el: '#root',
        data: {
            calendarDate: new Date(),
            sellerList: [],
            trailPointVisitInfos: [],
            map: {},
            trailRunPois: [],
            visitPoints: [],
            selectedVisitInfos: [],
            sellerNameDic: {},
            lushu: null,
            showCalendar: false,
            SallerName: null,
            Total_Interval: 0,
            Total_sale: 0,
            Total_sale_order: 0,
            p : true,
            mouseX:0,
            mouseY:0
        },
        mounted() {
            window.lookSellerCurPosition = this.lookSellerCurPosition;
            this.getSellers(this.calendarDate)
            this.initNewMap()
            document.addEventListener('mousemove', (event) => {
            this.mouseX= event.clientX
            this.mouseY = event.clientY
        })
        },
        watch: {
            calendarDate(newVal, oldVal) {
                this.lushu = null
                this.getSellers(newVal)
            }
        },
        methods: {
            toSheet(sheet_id, sheetType) {
                if (sheetType === '销售单') {
                    window.parent.newTabPage("销售单", `Sheets/SaleSheet?sheet_id=${sheet_id}`)
                }
                if (sheetType === '销售订单') {
                    window.parent.newTabPage("销售订单", `Sheets/SaleOrderSheet?sheet_id=${sheet_id}`)
                }
            },
            onCalendarClick(e) {
                if (!isNaN(e.target.innerText))
                   this.showCalendar = false;

            },
             format(num, cent) {
               return(num.toFixed(cent) + '').replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
             },
            initNewMap(centerPoint) {
                if(this.view){
                    this.view.destroy()
                }
                console.log(this.map)
                if (!this.map.areaCustomLoaded) {
                    var map = new BMapGL.Map("allmap");
                    this.map = map
                }
                if (!centerPoint) {
                    centerPoint = new BMapGL.Point(116.23123, 39.54123213)
                }
                this.map.centerAndZoom(centerPoint, 15);
                this.map.enableScrollWheelZoom(true);
                this.view = new mapvgl.View({
                    map: this.map
                });
            },
            centerBMapPointFrom(point) {
              return new BMapGL.Point(point.longitude, point.latitude);
            },
            sellerNameDicFrom(sellerList) {
                let res = {}
                sellerList.forEach(seller => {
                    res[seller_id] = seller.oper_name
                })
                console.log(res)
                return res
            },
            visitInfoClick(item) {
                    var point = new BMapGL.Point(item.longitude, item.latitude);
                this.map.setCenter(point)
                this.map.setZoom(18)
                this.visitPoints.map(aVisitPoint => {
                    if (item.visit_id === aVisitPoint.visit_id) {
                        aVisitPoint.timelineSelected = true
                    } else {
                        aVisitPoint.timelineSelected = false

                    }
                })
            },
            move (prvePoint, newPoint, effect, setRotation) {
                    var me = this,
                           //当前帧数
                            currentCount = 0,
                           //初始坐标
                           _prvePoint = me._projection.lngLatToPoint(prvePoint),//将球面坐标转换为平面坐标
                           //获取结束点的(x,y)坐标
                           _newPoint = me._projection.lngLatToPoint(newPoint),
                           //两点之间要循环定位的次数
                           count = me._runTime / me._intervalTimer;
                            //两点之间匀速移动
                           me._intervalFlag = setInterval(function () {
                              //两点之间当前帧数大于总帧数的时候，则说明已经完成移动
                              if (currentCount >= count) {
                              clearInterval(me._intervalFlag);
                              } else {
                            //动画移动
                            currentCount++;//计数
                                  var x = effect(_prvePoint.x, _newPoint.x, currentCount, count),
                                        y = effect(_prvePoint.y, _newPoint.y, currentCount, count);
                                  //根据平面坐标转化为球面坐标
                            var pos = map.getMapType().getProjection().pointToLngLat(new BMapGL.Pixel(x, y));
                            //设置marker角度(两点之间的距离车的角度保持一致)
                              if (currentCount == 1) {
                                  //转换角度
                                  setRotation(prvePoint,newPoint, me._em);
                                }
                                  //正在移动

                            me._em._newPointMark.setPosition(pos);
                        }
                           }, me._intervalTimer);
                           me._em._prvePoint = newPoint;
            },
            setRotation(curPos, targetPos, em){
                  var me = this;
                  var deg = 0;
                   curPos = map.pointToPixel(curPos);
                    targetPos = map.pointToPixel(targetPos);
                  if (targetPos.x != curPos.x) {
                      var tan = (targetPos.y - curPos.y) / (targetPos.x - curPos.x),
                      atan = Math.atan(tan);
                      deg = atan * 360 / (2 * Math.PI);
                      if (targetPos.x < curPos.x) {
                          deg = -deg + 90 + 90;
                      } else {
                          deg = -deg;
                      }
                      em._newPointMark.setRotation(-deg);

                  } else {
                      var disy = targetPos.y - curPos.y;
                      var bias = 0;
                      if (disy > 0)
                          bias = -1
                      else
                          bias = 1
                      em._newPointMark.setRotation(-bias * 90);
                  }
                  return;
              },
            lookSellerCurPosition() {
                $.ajax({
                    url: "/api/VisitPath/GetSellerCurPosition?operKey=@Html.Raw(Model.OperKey)",
                    success:  (res)=> {
                        const data = res.data
                        if (data.length == 0) {
                            this.$message("暂无业务员开工")
                            return
                        }
                        this.map.clearOverlays()
                        this.map.reset()
                        var map = this.map
                        map.enableScrollWheelZoom(true)
                        const firstVisitInfo = data[0]
                        var centerPoint = this.centerBMapPointFrom(firstVisitInfo)
                        map.centerAndZoom(centerPoint, 13);
                        data.forEach(e => {
                            var point = new BMapGL.Point(e.longitude, e.latitude);
                            var myIcon = new BMapGL.Icon("../images/seller_cur_icon.png", new BMapGL.Size(32, 32));
                            var marker = new BMapGL.Marker(point, {
                                icon: myIcon
                            });  // 创建标注
                            var label = new BMapGL.Label(e.oper_name + " " + e.happen_time.split(" ")[1], { offset: new BMapGL.Size(-5, 30) });
                            label.setStyle({
                                color: 'blue',
                                borderRadius: '5px',
                                borderColor: '#b85',
                                fontSize: '12px',
                                height: '15px',
                                lineHeight: '15px',
                                fontFamily: '微软雅黑'
                            });
                            marker.setLabel(label);
                            map.addOverlay(marker);              // 将标注添加到地图中
                        })
                    }
                })
            },
            convertDateStrToDay(date) {
                const y = date.getFullYear()
                const m = (date.getMonth() + 1 + '').myPadLeft(2, '0')
                const d = (date.getDate() + '').myPadLeft(2, '0')
                return `${y}-${m}-${d}`
            },

            getGreatCircleDistance(lat1, lng1, lat2, lng2) {
                var EARTH_RADIUS = 6378137.0; //单位M
                var radLat1 = this.getRad(lat1);
                var radLat2 = this.getRad(lat2);
                var a = radLat1 - radLat2;
                var radLng1 = this.getRad(lng1);
                var radLng2 = this.getRad(lng2);
                var b = radLng1 - radLng2;
                var s =
                    2 *
                    Math.asin(
                        Math.sqrt(
                            Math.pow(Math.sin(a / 2), 2) +
                            Math.cos(radLat1) *
                            Math.cos(radLat2) *
                            Math.pow(Math.sin(b / 2), 2)
                        )
                    );
                s = s * EARTH_RADIUS;
                s = Math.round(s * 10000) / 10000.0;
                return s;
            },
            getRad(d) {
                var PI = Math.PI;
                return (d * PI) / 180.0;
            },

            runTrailClick() {
                console.log("g_arrPois", this.trailRunPois )
                if (this.trailRunPois.length === 0) {
                    this.$message("正在为您处理动画中，请稍后。")
                    return
                }
                var that=this
        
                if (!this.lushu) {
                    var lushu = new BMapGLLib.LuShu(this.map, this.trailRunPois, {
                        defaultContent: "",//"从天安门到百度大厦"
                        autoView: true,//是否开启自动视野调整，如果开启那么路书在运动过程中会根据视野自动调整
                        icon: new BMapGL.Icon('../images/car5.jpg', new BMapGL.Size(32, 32), { anchor: new BMapGL.Size(13, 13) }),
                        speed: 1250,
                        enableRotation: false,//是否设置marker随着道路的走向进行旋转
                        landmarkPois: [
                        ]
                    });
                    this.lushu = lushu
                }
                console.log("this.lushu",this.lushu)
                this.lushu.start();

            },
            pause() {
                if (this.lushu) {
                    this.lushu.pause();
                }
            },
            clear() {
                this.lushu = null;
            },
            /**
            createInfoWindow(visitPoints) {
                totalContent = "<div style='overflow:scroll; width: auto; height: 40rem;'>"
                theSameDayPositionVisitorInfoList.forEach(visitPoints => {
                    var sContent = generateSupCustContent(visitPoints);
                    totalContent += sContent
                })
                totalContent += generateVisitCountContent(visitPoints.length)
                totalContent += "</div>"
                var infoWindow = new BMapGL.InfoWindow(totalContent);
                return infoWindow
            },
           
            createVisitMarker(map, visitPoints) {
                var that = this
                createIconMarker(this.view, visitPoints, "../images/journey_shopicon.png", that)
                function createIconMarker(view, points, iconUrl) {
                    var icon_layer = new mapvgl.IconLayer({
                        width: 40,
                        height: 40,
                        offset: [0, -153 / 12],
                        opacity: 1,
                        icon: iconUrl,
                        renderOrder:2,
                        enablePicked: true, // 是否可以拾取
                        //selectedIndex: -1, // 选中项
                        //selectedColor: '#ff0000', // 选中项颜色
                        //autoSelect: true, // 根据鼠标位置来自动设置选中项
                        onClick: (e) => { // 点击事件
                            if (e.dataItem && e.dataItem.geometry.visitPoint) {

                                var aVisitPoint = e.dataItem.geometry.visitPoint
                                console.log(aVisitPoint)
                                const visitInfos = visitPoints.filter(point => point.sup_name === aVisitPoint.sup_name && point.seller_id === aVisitPoint.seller_id)
                                that.selectedVisitInfos = visitInfos
                            }
                        },
                        onDblClick: e => {
                            console.log('double click', e);
                        },
                        onRightClick: e => {
                            console.log('right click', e);
                        }
                    });
                    view.addLayer(icon_layer);
                    var layerPoints = visitPoints.map(point => {
                        return {
                            geometry: {
                                visitPoint:point,
                                type: 'Point',
                                coordinates: [Number(point.longitude), Number(point.latitude)]
                            }
                        }
                    })
                    console.log({ layerPoints })
                    icon_layer.setData(layerPoints);
                }
            },
            **/
            getArrPoisPromise(map, start, end) {
            //绘制折线以及样式
                const distance = this.getGreatCircleDistance(start.lat, start.lng, end.lat, end.lng)
                const promise = new Promise((resolve, reject) => {
                    var that = this
                    const searchCompleteFunc = function (res) {
                        console.log(res)
                        if (drv.getStatus() == BMAP_STATUS_SUCCESS) {
                            var plan = res.getPlan(0);
                            var arrPois = [];
                            for (var j = 0; j < plan.getNumRoutes(); j++) {
                                var route = plan.getRoute(j);
                                arrPois = arrPois.concat(route.getPath());
                            }
                            // g_arrPois = g_arrPois.concat(arrPois)
                            //map.addOverlay(new BMapGL.Polyline(arrPois, {
                            //    strokeColor: "#67C23A",
                            //    strokeWeight:"6",
                            //    strokeStyle: distance >= 5000 ? 'dashed' : "solid",
                            //}));
                            //   map.setViewport(arrPois);
                            console.log(arrPois)
                            if (arrPois.length > 0) {
                                resolve(arrPois)
                            } else {
                                resolve([])
                            }
                        } else {
                               resolve([])
                        }
                    }
                    var drv

                    // if (distance<=2000) {
                    //   drv = new BMap.WalkingRoute("北京", {
                    //   onSearchComplete: onSearchCompleteFunc
                    //   });
                    // }
                    // else{
                    // drv=new BMap.DrivingRoute("北京", {
                    //   onSearchComplete: onSearchCompleteFunc
                    //   });
                    // }
                    // var start = new BMap.Point(startPoint.longitude, startPoint.latitude);
                    // var end = new BMap.Point(endPoint.longitude, endPoint.latitude);
                    // drv.search(start, end);
                    if (distance <= 5000) {
                        drv = new BMapGL.WalkingRoute("北京", {
                            onSearchComplete: searchCompleteFunc
                        });
                        drv.search(start, end);
                    } else {
                        resolve([start , end])
                    }

                })
                return promise
                },
            getVisitPointsFrom(trailPoints) {

                return trailPoints
                    .filter(e => e.visit_id)
                    .map(aVisitPoint => {
                        aVisitPoint.distance = this.getGreatCircleDistance(aVisitPoint.sup_addr_lat, aVisitPoint.sup_addr_lng, aVisitPoint.latitude, aVisitPoint.longitude)
                        aVisitPoint.distanceOutrange=  this.signOutRange(aVisitPoint.distance)
                        return aVisitPoint
                    }).sort((aVisitPoint1,aVisitPoint2)=>new Date(aVisitPoint2.start_time).getTime()-new Date(aVisitPoint1.start_time).getTime())
            },
            signOutRange(distance) {
                return distance > 500;
            },
            clickStyleChange(sellerItem) {
                this.sellerList.map(seller => {
                    seller.seller_id === sellerItem.seller_id ? seller.isSelected = true : seller.isSelected = false
                })
            },
            createStartEndPoint(map, trailPoints) {
                const startTrailPoint = trailPoints[0]
                const endTrailPoint = trailPoints[trailPoints.length - 1]
                createIconMarker(this.view,startTrailPoint, "../images/startpoint_icon.png")
                createIconMarker(this.view, endTrailPoint, "../images/endpoint_icon.png")
                function createIconMarker(view,point, iconUrl) {
                    var icon_layer = new mapvgl.IconLayer({
                        width: 50,
                        height: 50,
                        //offset: [0, -153 / 12],
                        opacity: 1,
                        icon: iconUrl,
                        renderOrder:1,
                        //enablePicked: true, // 是否可以拾取
                        //selectedIndex: -1, // 选中项
                        //selectedColor: '#ff0000', // 选中项颜色
                        //autoSelect: true, // 根据鼠标位置来自动设置选中项
                        onClick: (e) => { // 点击事件
                            console.log('click', e);
                        },
                        onDblClick: e => {
                            console.log('double click', e);
                        },
                        onRightClick: e => {
                            console.log('right click', e);
                        }
                    });
                    view.addLayer(icon_layer);
                    var layerPoint = [{
                        geometry: {
                            type: 'Point',
                            coordinates: [point.lng, point.lat]
                        }
                    }];
                    icon_layer.setData(layerPoint);
                }
            },
            createTrailPointIcon(trailPoints) {
   
                var icon_layer = new mapvgl.IconLayer({
                    width: 30,
                    height: 30,
                    //offset: [0, -153 / 12],
                    opacity: 1,
                    icon: "../images/seller_stay_point.png",
                    renderOrder: 3,
                    enablePicked: true, // 是否可以拾取
                    //selectedIndex: -1, // 选中项
                    //selectedColor: '#ff0000', // 选中项颜色
                    //autoSelect: true, // 根据鼠标位置来自动设置选中项
                    onClick: (e) => { // 点击事件
                        if (e.dataItem && e.dataItem.geometry.visitPoint) {
                            alert("到达时间:" + e.dataItem.geometry.visitPoint.happen_time)
                        }
                    },
                    onDblClick: e => {
                    },
                    onRightClick: e => {
                        console.log('right click', e);
                    },
                   onMousemove: e => {
                    
                       if (e.dataItem && e.dataItem.geometry.visitPoint) {
                           console.log($("#allmap"))
                           $("#tooltip").css("display", "block")
                           $("#tooltip").css("left", this.mouseX + "px")
                           $("#tooltip").css("top", this.mouseY + "px")
                           $("#tooltip").html("到达时间:" + e.dataItem.geometry.visitPoint.happen_time)
                       } else {
                            $("#tooltip").css("display", "none")

                       }
                    }
                });
                
                this.view.addLayer(icon_layer);
                var layerPoints = trailPoints.filter(point=>!point.visit_id).map(point => {
                    return {
                        geometry: {
                            visitPoint: point,
                            type: 'Point',
                            coordinates: [Number(point.longitude), Number(point.latitude)]
                        }
                    }
                })
                console.log({"trailPoints": layerPoints })
                icon_layer.setData(layerPoints);
            },
            sellerItemClick(sellerItem) {
                this.SallerName = sellerItem.seller_name
                var date = new Date(this.calendarDate)
                var calendarDateStr = this.convertDateStrToDay(date)
                this.clickStyleChange(sellerItem)
                this.$forceUpdate()
                var that = this
                that.visitPoints = []
                this.lushu=null
                $.ajax({
                    url: `/api/VisitPath/GetSellerTrailPoints`,
                    data: {
                        "querySellerID": sellerItem.seller_id,
                        "queryDate": calendarDateStr,
                        "operKey": "@Html.Raw(Model.OperKey)"
                    },
                    success: (res) => {
                        const trailPoints = res.data
                        this.trailPointVisitInfos = trailPoints
                        if (this.trailPointVisitInfos.length === 0) {
                            this.$message("该日无轨迹点")
                            return
                        }

                        const visitPoints = this.getVisitPointsFrom(trailPoints);
                        var i = 0;
                        var cc = visitPoints.length;
                        while (i < cc) {
                            this.Total_Interval += Number(visitPoints[i].intervals);
                            this.Total_sale += Number(visitPoints[i].sale_total_amount);
                            this.Total_sale_order += Number(visitPoints[i].sale_order_total_amount);
                            i++;
                        }
                        this.$message("正在为您处理轨迹中，请稍后。")

                        that.visitPoints = visitPoints;
                        const firstPoint = {
                            longitude: trailPoints[0].longitude,
                            latitude: trailPoints[0].latitude
                        }
                        const centerPoint = this.centerBMapPointFrom(firstPoint)
                        this.initNewMap(centerPoint)
                        var trailPointLines = []
                        const trailPointsPromises = trailPointsPromisesFrom(trailPoints)
                        this.trailRunPois=[]
                        //解决偶尔轨迹运动无法播放问题 假如轨迹信息发生错乱 换回Promise.all(promiseTasks)
                        Promise.all(trailPointsPromises).then((promiseArrPois) => {

                            //promiseArrPois.forEach(e => {
                            //    this.trailRunPois = this.trailRunPois.concat(e)
                            //})
                            promiseArrPois.map(lines => {
                                var linesString =  lines.map(point => {
                                    this.trailRunPois.push(point)
                                    return [point.lng, point.lat]
                                })
                                trailPointLines.push({
                                    geometry: {
                                        coordinates: linesString,
                                        type: 'LineString',


                                    },
                                    style: {
                                        color: '#0000FF',  // 蓝色
                                        dashArray: [10, 5], // 虚线
                                        width: 3           // 线宽
                                    }
                                })
                                
                            })

                
                            console.log({ trailPointLines })

                            //var line_datapoint = this.trailRunPois.map(e=>{
                            //    return [e.lng,e.lat]
                            //})
                            //var line_datapoint = []
                            //var linePointCache = []
                            var lineLayer = new mapvgl.LineLayer({
                                width: 10,
                                color: 'rgba(55, 71, 226, 0.9)',
                                style: 'road',
                                dashArray: item => {
                                    return item.style.dashArray
                                },
                                enablePicked: true,
                                onClick: e => {
                                    console.log(e);
                                }
                            });

                                    /** 
                                    if (linePointCache.length!=0) {
                                        var lineLayer = new mapvgl.LineLayer({
                                            width: 10,
                                            color: '#337ecc',
                                            style: 'road',
                                            enablePicked: true
                                        });
                                        this.view.addLayer(lineLayer);
                                        lineLayer.setData([{
                                            'geometry': {
                                                'coordinates': linePointCache,
                                                'type': 'LineString'
                                            }
                                        }])
                                        console.log("清除积压的缓存:", linePointCache)
                                        linePointCache = []
                                    }
                                    var dashLineLayer = new mapvgl.LineLayer({
                                        width: 10,
                                        dashArray: [20, 40],
                                        dashOffset: 10,
                                        color: '#c45656'
                                    });
                                    this.view.addLayer(dashLineLayer);
                                    dashLineLayer.setData([{
                                        'geometry': {
                                            'coordinates': [[prePoint.lng, prePoint.lat], [curPoint.lng, curPoint.lat]],
                                            'type': 'LineString'
                                        }
                                    }])
                                }
                                if (i == this.trailRunPois.length - 1) {
                                    var lineLayer = new mapvgl.LineLayer({
                                        width: 10,
                                        color: '#337ecc',
                                        style: 'road',
                                        enablePicked: true
                                    });
                                    this.view.addLayer(lineLayer);
                                    lineLayer.setData([{
                                        'geometry': {
                                            'coordinates': linePointCache,
                                            'type': 'LineString'
                                        }
                                    }])
                                    linePointCache = []
                                }
                                console.log("积压的缓存:", linePointCache)
                                
                            }**/
                            const dataSet = new mapv.DataSet(trailPointLines);
                            console.log(dataSet)

                            this.view.addLayer(lineLayer);
                            lineLayer.setData(dataSet.get());
                            //this.createStartEndPoint(this.map, this.trailRunPois)
                           // this.createVisitMarker(this.map, visitPoints)
                            this.createTrailPointIcon(trailPoints)
                        })
                        function trailPointsPromisesFrom(trailPointVisitInfos) {
                            let res = []
                            for (var i = 0; i < trailPointVisitInfos.length - 1; i++) {
                                const startPoint = trailPointVisitInfos[i]
                                const endPoint = trailPointVisitInfos[i + 1]
                                const startBMapPoint = that.getBMapPoint(startPoint.longitude, startPoint.latitude)
                                const endBMapPoint = that.getBMapPoint(endPoint.longitude, endPoint.latitude)
                                const arrPoisPromiseTask = that.getArrPoisPromise(that.map, startBMapPoint, endBMapPoint)
                                res.push(arrPoisPromiseTask)
                            }
                            return res
                        }
                    }
                }

                )
            },
                getBMapPoint(longitude, latitude) {
                    return new BMapGL.Point(longitude, latitude)
                },
                getSellers(calendarDate) {
                    var date = new Date(calendarDate)
                    var calendarDateStr = this.convertDateStrToDay(date)
                    $.ajax({
                        url: `/api/VisitPath/GetSellerVisitList?queryDate=${calendarDateStr}&&operKey=@Html.Raw(Model.OperKey)`, success: (res) => {
                            this.sellerList = res.data
                            this.sellerList.map(seller=>{
                                seller.isSelected= false
                            })
                        }
                    })
                }
            }
        })
</script>
</html>
