﻿@page
@model ArtisanManage.Pages.BaseInfo.BarcodeScaleGroupViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />


    <script type="text/javascript">
        var frame = "BarcodeScaleGroupEdit";
 
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        var RowIndex = -1;
        window.addEventListener('message', function (rs) {
            $("#popItem").jqxWindow('close');
            this.console.warn('请根据record对象属性修改对应字段：', rs.data);
            if (rs.data.msgHead == frame) {
                if (rs.data.action == "add") {
                    if (window.source_gridItems.totalrecords == 0) {
                        if (window.QueryData) window.QueryData()
                    }
                    else {
                        var row = rs.data.record;
                        var rows = window.gridData_gridItems.localRows;
                        for (var i = window.source_gridItems.totalrecords - 1; i >= 0; i--) {
                            if (rows[i]) {
                                rows[i + 1] = rows[i];
                            }
                        }
                        rows[0] = row;
                        rows[0].i = row.group_id;

                        window.source_gridItems.totalrecords++;
                        $('#gridItems').jqxGrid('clear');
                        $('#gridItems').jqxGrid('updatebounddata');
                    }
                }
                else if (rs.data.action == "update") {
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "group_name", rs.data.record.group_name);
                    $('#gridItems').jqxGrid('setcellvalue', RowIndex, "remark", rs.data.record.remark);
                }
            };
        });
        var m_db_id = "10";
        var newCount = 1;

        function btnAddBarcodeScaleGroup_click(e) {
            $('#popItem').jqxWindow('open');
            $("#popItem").jqxWindow('setContent', `<iframe src="${frame}?operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);

        }
        function btnAddBarcodeScale_click(e) {
           window.parent.newTabPage('条码秤', `BaseInfo/BarcodeScaleView`, window);
        }
        function onGridRowEdit(rowIndex) {
            var barcode_scale_group_id = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'barcode_scale_group_id');
            // $('#popItem').jqxWindow('open');
            // $("#popItem").jqxWindow('setContent', '<iframe src="GroupEdit?operKey=' + g_operKey + '&group_id=' + group_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
            if (!barcode_scale_group_id || barcode_scale_group_id === 'null') {
                console.error('Invalid barcode_scale_group_id:', barcode_scale_group_id);
                return;
            }

            window.parent.newTabPage('条码秤组', `BaseInfo/BarcodeScaleGroupEditPlu?barcode_scale_group_id=${barcode_scale_group_id}`, window);
        }
        var itemSource = {};
        $(document).ready(function () {
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)

            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                console.log(args);
                if (args.datafield == "barcode_scale_group_name") {
                    if (args.originalEvent.button == 2) return;
                        var barcode_scale_group_id = args.row.bounddata.barcode_scale_group_id;
                        RowIndex = args.rowindex;
                        // if (ForSelect) {
                        //     var group_id = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "i");
                        //     var group_name = $('#gridItems').jqxGrid('getcellvalue', args.rowindex, "group_name");
                        //     var msg = {
                        //         msgHead: 'GroupsView', action: 'select', group_id: group_id, group_name: group_name
                        //     };
                        //     window.parent.postMessage(msg, '*');
                        // }
                        // else {
                        // var barcode_scale_group_id = $('#gridItems').jqxGrid('getcellvalue', "i");
                        // window.parent.newTabPage('条码秤组', `BaseInfo/BarcodeScaleGroupEditPlu?barcode_scale_group_id=${barcode_scale_group_id}`, window);
                            onGridRowEdit(args.rowindex);
                            //$('#popItem').jqxWindow('open');
                            // $("#popItem").jqxWindow('setContent', '<iframe src="ItemEdit?operKey=' + g_operKey + '&item_id=' + item_id + '" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>');
                   // }
               }

         });


            $("#Cancel").on('click', function () {
                for (var i = 0; i < 10; i++) {
                    $('#jqxgrid').jqxGrid('deleterow', i);
                    $('#jqxgrid').jqxGrid('addrow', i, {})
                }
            });

            $("#popItem").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 500, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
            $(document).on('contextmenu', function (e) {//禁用浏览器默认右键
                return false;
            });
             
            QueryData();
        });
    </script>

    <style>
        .margin {
            margin-left: 20px;
        }

        input {
            font-size: 14px;
            border-radius: 6px;
            border-color: #ddd;
            border-width: 0.5px;
            width: 200px;
            height: 25px;
        }
    </style>
</head>

<body>

    <div id="divHead" style="display:flex;justify-content:flex-start; margin-top:20px;">
        @* <div><button onclick="QueryData()" class="margin">查询</button></div> *@
        <div><button onclick="btnAddBarcodeScale_click()" class="margin" style="width:100px;">条码秤信息</button></div>
        <div style="margin-left: auto;">
            <div style="display:flex;margin-right:40px">
                <div><button onclick="btnAddBarcodeScaleGroup_click()" class="margin" style="width:100px;">添加条码秤组</button></div>
            </div>
        </div>
    </div>

    <div id="gridItems" style="margin-top:10px;width:calc(100% - 10px);height:100%;margin-bottom:10px;"></div>


    <div id="popItem" style="display:none;">
        <div id="itemCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">新增条码秤组信息</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
</body>
</html>