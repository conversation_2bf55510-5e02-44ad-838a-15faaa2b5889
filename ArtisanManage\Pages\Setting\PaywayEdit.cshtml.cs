using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;
using ArtisanManage.Services;
using System.Dynamic;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using NPOI.POIFS.Crypt.Dsig;
using Microsoft.AspNetCore.SignalR;
using System.Text;
using static NPOI.HSSF.Util.HSSFColor;
using NPOI.HSSF.Record;
using NPOI.POIFS.Properties;
using System.Security.Policy;
using System.ComponentModel.Design;
using myJXC;

namespace ArtisanManage.Pages.Setting
{
    public class PaywayEditModel : PageFormModel
    { 
        public PaywayEditModel(CMySbCommand cmd, string company_id = "", string oper_id = "") : base(Services.MenuId.paywaysView)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"sub_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"sub_name",new DataItem(){Title="科目名称",Necessary=true,FldArea="divHead"}},
                {"sub_code",new DataItem(){Title="科目代码",Necessary=true,FldArea="divHead"}},
                {"order_index",new DataItem(){Title="顺序号",FldArea="divHead" }},
                {"mother_id",new DataItem(){Title="上级科目",Disabled=true, LabelFld="mother_name",CtrlType="jqxDropDownTree",MumSelectable=true,FldArea="divHead",TreePathFld="other_sub",
                   SqlForOptions="select sub_id as v,sub_name as l,mother_id as pv from cw_subject where company_id= ~COMPANY_ID ", MaxRecords="500"
                }},
                {"level",new DataItem(){Title="层级",FldArea="divHead",Disabled=true }},
                {"sub_type",new DataItem(){Title="类型",FldArea="divHead",LabelFld="sub_type_name",LabelInDB=false,ButtonUsage="list",Source = "[{v:'QT',l:'现金银行'},{v:'YS',l:'预收'},{v:'YF',l:'预付'},{v:'ZC',l:'费用支出'},{v:'QTSR',l:'其他收入'},{v:'JK',l:'借款'}]",CompareOperator="="}},
                {"status",new DataItem(){Title="状态",LabelFld="cls_status_name",FldArea="divHead",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}},
                {"direction",new DataItem(){Title="借贷",LabelFld="direction_name",FldArea="divHead",LabelInDB=false,Value="1",Label="借", ButtonUsage="list", Source = "[{v:1,l:'借'},{v:-1,l:'贷'}]"}},
                {"assister_types",new DataItem(){Title="辅助核算",LabelFld="assister_names",FldArea="divHead",LabelInDB=true, Checkboxes=true, Value="",Label="", ButtonUsage="list",DropDownWidth="150", DropDownHeight="150", MaxRecords="2",
                    Source = @"[{v:'C',l:'客户',condition:""assister_types like '%C%'""},
                                   {v:'S',l:'供应商',condition:""assister_types like '%S%'""},
                                   {v:'INV',l:'商品',condition:""assister_types like '%INV%'""},
                                   {v:'DEP',l:'部门',condition:""assister_types like '%DEP%'""},
                                   {v:'MAN',l:'业务员',condition:""assister_types like '%MAN%'""}]"
                }},
                {"is_order",new DataItem(){FldArea="divHead",Title="定货会账户",CtrlType="jqxCheckBox"}},
                {"for_pay",new DataItem(){FldArea="divHead",Title="可用于支付",CtrlType="jqxCheckBox"}},//用于销售单，客户端+app
                {"subUsed",new DataItem(){Title="已使用",FldArea="divHead",GetFromDb=false,SaveToDB=false,Hidden=true}},
            };

            m_idFld = "sub_id"; m_nameFld = "sub_name"; m_codeFld = "sub_code";
            m_tableName = "cw_subject";
            m_selectFromSQL = @"from (select *,replace(replace(replace(replace(replace(assister_types,'C','客户'),'S','供应商'),'INV','商品'),'DEP','部门'),'MAN','业务员') as assister_names from cw_subject) cw_subject left join (select sub_id as mother_id1,sub_name as mother_name from cw_subject where company_id= ~COMPANY_ID ) mc on mc.mother_id1=cw_subject.mother_id
                                where sub_id='~ID' and company_id= ~COMPANY_ID";
            this.AllowSameName = true;
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            string mother_id = Request.Query["mother_id"];
            string sql = "";
            if (DataItems["sub_code"].Value == "" &&!string.IsNullOrEmpty(mother_id))
            {
                sql = $"select  max(sub_code) v from cw_subject where company_id={company_id} and mother_id = {mother_id}";
                dynamic record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                if (record.v != "")
                {
                    int new_sub_code = Convert.ToInt32(record.v) + 1;
                    DataItems["sub_code"].Value = new_sub_code.ToString();

                }
                else
                {
                    sql = $"select  sub_code v from cw_subject where company_id={company_id} and sub_id = {mother_id}";
                    record = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                    DataItems["sub_code"].Value =(String)record.v + "01";
                }
            }
            var hasBalance = false;
            var sub_id = DataItems["sub_id"].Value;
            var sub_type = DataItems["sub_type"].Value;
            if(sub_id != "" && (sub_type=="YS"||sub_type=="YF"))
            {
                sql = $"SELECT sub_id FROM prepay_balance WHERE company_id = {company_id} and sub_id = {sub_id} limit 1";
                var record = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
                
                if (record.Count>0) hasBalance = true;

            }

            if (hasBalance)
            {
                DataItems["sub_type"].Disabled = true;
                DataItems["subUsed"].Value = "true";
            }

            if (sub_type != "" && sub_type != "ZC" && sub_type != "QTSR" && DataItems["for_pay"].Value=="")
            {
                DataItems["for_pay"].Value = "true";
            }
            if (sub_type == "YS" || sub_type == "YF")
            {
                DataItems["for_pay"].Value = "true";
                DataItems["for_pay"].Disabled = true;
            }


            //暂时不能放开（基本没有放开的可能，这边要改的更多，目前仅用于测试）
            //dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select coalesce(setting ->>  'useAccounting','false') useaccounting from company_setting where company_id = {company_id}", cmd);
            //if (Convert.ToBoolean(setting.useaccounting))
            //{
            //    DataItems["mother_id"].Disabled = true;
            //}
        }
        public async Task OnGet()
        { 
            await InitGet(cmd); 
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class PaywayEditController : BaseController
    {
        public PaywayEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            PaywayEditModel model = new PaywayEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            PaywayEditModel model = new PaywayEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        
        public async Task<string> AddAvailPayways(string companyID, string mother_id, string sub_id)
        {
            string msg = "";
            try
            {
                //下面的作用是在新增会计科目后，对员工档案里的支付方式进行更新的操作
                //从员工档案里筛选出限制使用支付方式，并且这个新建费用科目的父类被勾选的的员工
                if (!string.IsNullOrEmpty(mother_id))//只有这个新增科目有mother的时候才更新
                {
                    string operatorSql = $@"select oper_id, avail_pay_ways
                                    from info_operator
                                    where company_id = {companyID}
                                      and restrict_pay_ways = true
                                      and avail_pay_ways is not null
                                      and json_array_length(avail_pay_ways) > 0
                                      AND avail_pay_ways::jsonb @> '[{mother_id}]';";
                    dynamic opertorlist = await CDbDealer.GetRecordsFromSQLAsync(operatorSql, cmd);

                    string updatesql = "";
                    if (opertorlist.Count > 0 && sub_id != "")
                    {
                        int new_sub_id = Convert.ToInt32(sub_id);
                        foreach (dynamic opertor in opertorlist)
                        {
                            JArray availPayWays = JArray.Parse(opertor.avail_pay_ways);
                            if (!availPayWays.Any(t => t.Value<int>() == new_sub_id))//检查sub_id是否存在avail_pay_ways中
                            {
                                availPayWays.Add(new_sub_id);//父类存在但是自己不在的话，就要把自己添加进avail_pay_ways
                                string newAvailPayWays = availPayWays.ToString();
                                string oper_id = opertor.oper_id;
                                updatesql += $"update info_operator set avail_pay_ways = '{newAvailPayWays}' where company_id = {companyID} and oper_id = {oper_id};";

                            }
                        }
                    }
                    if (updatesql != "")//对要执行的sql需要进行判空操作
                    {
                        cmd.CommandText = updatesql;
                        await cmd.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception e)
            {
                msg = "创建科目出现问题，请联系客服";
            }
            return msg;
        }

            [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic data)
        {
            string msg = "";
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            string sql = "";
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            #region  开账前增改科目
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select coalesce(setting ->>  'useAccounting','false') useaccounting, setting from company_setting where company_id = {companyID}", cmd);
            dynamic subsForCheck = await CDbDealer.GetRecordsFromSQLAsync($@"
                select sub_code from cw_subject where company_id={companyID} and sub_code in (1,2,3,4,5) and level is null and direction is not null
                union all
                select sub_code from cw_subject where company_id={companyID} and sub_code not in (0,1,2,3,4,5) and (level is null or direction is null)
                union all
                select sub_code from cw_subject where company_id={companyID} and sub_name='本年利润' and direction=-1 and level=1 and sub_code=3103", cmd);
            if (setting == null || !Convert.ToBoolean(setting.useaccounting))
            {
                // 这里2836公司因为财务修改并且删除过一些科目，所以排除在下面代码之外
                if (subsForCheck.Count != 6 && companyID != "2836")
                {
                    msg = await CheckSubBeforeUseAccounting(data, companyID);
                    if (msg != "") return new JsonResult(new { result = "Error", msg });

                    PaywayEditModel model0 = new PaywayEditModel(cmd);
                    JsonResult record0 = await model0.SaveTable(cmd, data,tran);
                    string sub_id0 = (record0.Value as dynamic).record.sub_id;
                    string mother_id0 = (record0.Value as dynamic).record.mother_id;
                    string result0 = (record0.Value as dynamic).result;
                    msg = (record0.Value as dynamic).msg;
                    await CwLog.Save(companyID, operID, null, "CwSubject", $"{result0}: {msg}; save before use accounting; save table cw_subject, sub_code:  {data.sub_code}, sub_id: {sub_id0}", cmd);
                    if (msg == "") 
                    {
                        msg += await AddAvailPayways(companyID, mother_id0, sub_id0);
                        if (msg != "") result0 = "Error";
                    }
                    if (msg != "") tran.Rollback();
                    else tran.Commit();

                    return new JsonResult(new { result = result0, msg, record = record0 });
                }
                //如果subsForCheck.Count=6又没开账，说明是反开账后改科目的情况，按照开账标准增改科目
            }
            #endregion

            //dynamic totalCheck = await CDbDealer.Get1RecordFromSQLAsync($"select sub_id from cw_subject where company_id={companyID} and (level is null or direction is null )", cmd);
            //if (totalCheck != null)
            //{
            //    return new JsonResult(new { result = "Error", msg="科目逻辑错误，请联系管理员修改" });
            //}



            //保存前的判断
            msg = await CheckNewSubBeforeSave(data, companyID);//新增和修改都走这里
            if (msg != "")
            {
                await CwLog.Save(companyID, operID, null, "CwSubject", $"Error: {msg}; save table cw_subject, sub_code: {data.sub_code}", cmd);
                return new JsonResult(new { result = "Error", msg });
            }
            if (data.sub_id != "")//修改走这里
            {
                msg=await CheckSubBeforeSave(data, companyID);
                if (msg != "")
                {
                    await CwLog.Save(companyID, operID, null, "CwSubject", $"Error: {msg}; save table cw_subject, sub_code: {data.sub_code}", cmd);
                    return new JsonResult(new { result = "Error", msg });
                }
            }

            //保存
            
            PaywayEditModel model = new PaywayEditModel(cmd);

            JsonResult record = await model.SaveTable(cmd, data, tran);
            string sub_id = (record.Value as dynamic).record.sub_id;
            string mother_id = (record.Value as dynamic).record.mother_id;
            string result = (record.Value as dynamic).result; 
            msg = (record.Value as dynamic).msg;
            if (msg == "")
            {
                msg += await AddAvailPayways(companyID, mother_id, sub_id);
                if (msg != "") result = "Error";
            }   
            if (result != "OK")
            {
                tran.Rollback();
                await CwLog.Save(companyID, operID, null, "CwSubject", $"Error: {msg}; save table cw_subject, sub_code:  {data.sub_code}", cmd);              
                return Json(new { result = "Error", msg, added = model.m_bNewRecord, record });
            }

            if (data.sub_id != "")//修改科目
            {
                string py_str = PinYinConverter.GetJP((record.Value as dynamic).record.sub_name.ToString());
                sql = $@"update cw_subject set py_str='{py_str}' where company_id={companyID} and sub_id={sub_id};";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
                tran.Commit();
                await CwLog.Save(companyID, operID, null, "CwSubject", $"OK; edit, sub_id: {sub_id}, save table cw_subject, sub_code: {data.sub_code}", cmd);
                return new JsonResult(new { result, msg, added = model.m_bNewRecord, record });
            }

            if (Convert.ToBoolean(setting.useaccounting))//开账才保存cw_sub_balance
            {
                dynamic jr2v = (await SaveBalance((record.Value as dynamic).record, data, companyID, cmd)).Value as dynamic;
                if (jr2v.result != "OK")
                {
                    tran.Rollback();
                    await CwLog.Save(companyID, operID, null, "CwSubject", $"Error: {jr2v.msg}; add, save table cw_subject, sub_code: {data.sub_code}, cw_op_init_detail, cw_sub_balance", cmd);
                    return Json(new { result = "Error", msg = jr2v.msg, added = model.m_bNewRecord, record });
                }
            }

            tran.Commit();
            await CwLog.Save(companyID, operID, null, "CwSubject", $"OK; add, save subject, sub_code: {data.sub_code}", cmd);
            return new JsonResult(new { result, msg, added = model.m_bNewRecord, record });
        }


        public async Task<string> CheckSubBeforeUseAccounting(dynamic data, string companyID)
        {
            if (data.assister_types.ToString() != "")
            {
                return "如需使用辅助核算，请先开账";
            }
            if (((string)data.sub_name).Contains("-"))
            {
                return "科目名称不能包含“-”";
            }
            if (((string)data.sub_name).Contains(","))
            {
                return "科目名称不能包含“,”";
            }
            if (((string)data.sub_name).Contains(" "))
            {
                return "科目名称不能包含空格";
            }
            //sub_type
            if (data.sub_type == "QT" && (((string)data.sub_code).Substring(0, 4) != "1001" && ((string)data.sub_code).Substring(0, 4) != "1002"))
            {
                return "现金银行类型请添加在【1001现金】 或 【1002银行】科目下";
            }
            if (data.sub_type == "QTSR" && (!data.sub_code.ToString().StartsWith('5') && !data.sub_code.ToString().StartsWith('6')))
            {
                return "其他收入类型请添加在【6051其他收入】科目下";
            }
            if (data.sub_type == "ZC" && (!data.sub_code.ToString().StartsWith('5') && !data.sub_code.ToString().StartsWith('6')))
            {
                return "费用支出类型请添加在【损益类(支出)】科目下";
            }
            if (data.sub_type == "YS" && ((string)data.sub_code).Substring(0, 4) != "2203")
            {
                return "预收类型请添加在【2203预收】科目下";
            }
            if (data.sub_type == "YF" && ((string)data.sub_code).Substring(0, 4) != "1123")
            {
                return "预付类型请添加在【1123预付款】科目下";
            }
            if (data.other_sub.ToString().Trim('/').Split('/').Length > 4)
            {
                return "最多添加三级科目";
            }

            string sql = $@"
                (select qrcode_id as sheet_id , '现金银行账户' as sheet_type from info_pay_qrcode where company_id={companyID} and sub_id in ( ~VAR_SUB_ID ))
                union all
                ( select sheet_id, '销售单' as sheet_type from sheet_sale_main where (payway1_id in ( ~VAR_SUB_ID ) or payway2_id in ( ~VAR_SUB_ID ) or payway3_id in ( ~VAR_SUB_ID )) and company_id={companyID} )
                union all
                ( select sheet_id, '采购单' as sheet_type from sheet_buy_main where (payway1_id in ( ~VAR_SUB_ID ) or payway2_id in ( ~VAR_SUB_ID ) ) and company_id={companyID} )
                union all
                ( select sheet_id, (case when sheet_type='SK' then '收款单' else '付款单' end) as sheet_type from sheet_get_arrears_main where (payway1_id in ( ~VAR_SUB_ID )  or payway2_id in ( ~VAR_SUB_ID )) and company_id={companyID} )
                union all
                ( select sheet_id, (case when sheet_type='YS' then '预收款单' when sheet_type='YF' then '预付款单' else '定货会' end) as sheet_type from sheet_prepay where prepay_sub_id in ( ~VAR_SUB_ID ) and company_id={companyID} )
                union all
                ( select sub_id as sheet_id, '预收款/预付款余额' as sheet_type from prepay_balance where sub_id in ( ~VAR_SUB_ID ) and company_id={companyID} )
                union all
                ( select sheet_id, '定货调整单' as sheet_type from sheet_item_ordered_adjust_main where (prepay_sub_id in ( ~VAR_SUB_ID ) or payway1_id in ( ~VAR_SUB_ID ) or payway2_id in ( ~VAR_SUB_ID )) and company_id={companyID} )
                union all
                ( select prepay_sub_id as sheet_id, '定货会余额' as sheet_type from items_ordered_balance where prepay_sub_id in ( ~VAR_SUB_ID ) and company_id={companyID} )
                union all
                ( select sheet_id, (case when sheet_type='ZC' then '费用支出单' else '其他收入单' end) as sheet_type from sheet_fee_out_main where (payway1_id in ( ~VAR_SUB_ID ) or payway2_id in ( ~VAR_SUB_ID )) and company_id={companyID} )
                union all
                ( select sheet_id, '转账单' as sheet_type from sheet_cashbank_transfer_detail where (money_out_id in ( ~VAR_SUB_ID ) or money_in_id in ( ~VAR_SUB_ID )) and company_id={companyID} )
                union all
                ( select d.sheet_id, (case when m.sheet_type='ZC' then '费用支出单' else '其他收入单' end) as sheet_type from sheet_fee_out_detail d left join sheet_fee_out_main m on d.company_id=m.company_id and d.sheet_id=m.sheet_id where d.fee_sub_id in ( ~VAR_SUB_ID ) and d.company_id={companyID} )
                union all
                ( select sheet_id, '陈列协议' as sheet_type from display_agreement_main where fee_sub_id in ( ~VAR_SUB_ID ) and company_id = {companyID} )";
            SQLQueue QQ = new SQLQueue(cmd);
            //旧科目
            if (data.sub_id != "")
            {
                QQ.Enqueue("sub_17", $"select max(sub_id) as max_sub_id from (select sub_id from cw_subject where company_id={companyID} order by sub_id limit 17) t");
                QQ.Enqueue("sub", $"select s.*, q.qrcode_id from cw_subject s left join info_pay_qrcode q on s.sub_id=q.sub_id where s.company_id={companyID} and s.sub_id={data.sub_id}");
                QQ.Enqueue("qrcode", $"select qrcode_id from info_pay_qrcode where company_id={companyID} and sub_id={data.sub_id}");
                QQ.Enqueue("biz_sub", sql.Replace("~VAR_SUB_ID", $"{data.sub_id}"));
                QQ.Enqueue("sub_children", $"select sub_id, sub_code, sub_type from cw_subject where company_id={companyID} and mother_id={data.sub_id}");
            }
            //新科目
            QQ.Enqueue("sub_samecode", $"select sub_code, sub_id from cw_subject where company_id={companyID} and sub_code={data.sub_code}");
            //新旧都判断
            QQ.Enqueue("sub_mother", $"select sub_code, sub_type from cw_subject where company_id={companyID} and sub_id={data.mother_id}");
            QQ.Enqueue("biz_fa", sql.Replace("~VAR_SUB_ID", $"{data.mother_id}"));
            QQ.Enqueue("sub_samename", $"select sub_name, sub_code, sub_id, sub_type from cw_subject where company_id={companyID} and sub_name='{data.sub_name}' and sub_type in ('YS','YF','QT')");
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            dynamic sub_17 = null;
            dynamic sub = null;
            dynamic qrcode = null;
            dynamic biz_sub = null;
            dynamic sub_children = null;
            dynamic sub_samecode = null;
            dynamic sub_mother = null;
            dynamic biz_fa = null;
            dynamic sub_samename = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if(data.sub_id != "")
                {
                    if (sqlName == "sub_17") sub_17 = CDbDealer.Get1RecordFromDr(dr, false);
                    else if (sqlName == "sub") sub = CDbDealer.Get1RecordFromDr(dr, false);
                    else if (sqlName == "qrcode") qrcode = CDbDealer.Get1RecordFromDr(dr, false);
                    else if (sqlName == "biz_sub") biz_sub = CDbDealer.Get1RecordFromDr(dr, false);
                    else if (sqlName == "sub_children") sub_children = CDbDealer.GetRecordsFromDr(dr, false);
                }
                if (sqlName == "sub_samecode") sub_samecode = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "sub_mother") sub_mother = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "biz_fa") biz_fa = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "sub_samename") sub_samename = CDbDealer.GetRecordsFromDr(dr, false);
            }
            QQ.Clear();

            if (data.sub_id != "")//编辑旧科目
            {
                //if (sub.sub_code == "1003" && sub.sub_name == "预付款" && data.sub_code == "1123" && data.sub_name == "预付款") return "";
                if(Array.IndexOf(new string[] { "0", "10", "1001", "1002", "100201", "100202", "100203", "1123", "22", "2203", "220301", "60", "6051", "605101", "66", "6601", "6602" }, sub.sub_code.ToString()) > -1 && Convert.ToInt64(sub.sub_id)<=Convert.ToInt64(sub_17.max_sub_id))
                {
                    if(sub.sub_code.ToString()!=data.sub_code.ToString())
                    {
                        return "初始科目请勿修改科目代码";
                    }
                }
                if(sub.sub_type=="QT" && data.sub_type != "QT" && qrcode!=null)
                {
                    return "该科目已添加至【账户管理】，无法修改类型";
                }
                if (biz_sub != null)
                {
                    //if (data.status == "0") return $"该科目已被【{biz_sub.sheet_type}】使用过, 无法停用"; //使用过后停用是合理的逻辑--zy2024.6.20 
                    if (data.sub_type != sub.sub_type) return $"该科目已被【{biz_sub.sheet_type}】使用过, 无法修改类型";
                }
                if (sub_children.Count > 0)
                {
                    if (data.sub_type != "") return "父级科目无法添加类型，请为子科目添加类型";
                    if (data.sub_code != sub.sub_code) return "父级科目无法修改科目代码";
                }

                if (sub_samename.Count > 1)
                {
                    foreach(dynamic subN in sub_samename)
                    {
                        if (subN.sub_id != data.sub_id.ToString())
                        {
                            string type = subN.sub_type;
                            string type_label = "";
                            switch (type)
                            {
                                case "QT":
                                    type_label = "现金银行";
                                    break;
                                case "YS":
                                    type_label = "预收";
                                    break;
                                case "YF":
                                    type_label = "预付";
                                    break;
                            }
                            return $"存在相同名称的【{type_label}】类型科目";
                        }
                    }
                    
                }
            }
            else//仅针对新科目的判断
            {
                if (sub_samecode != null) return "存在相同科目代码的记录";
                if (biz_fa != null)
                {
                    return $"父级科目已被【{biz_fa.sheet_type}】使用过, 无法添加子科目";
                }
                if (new List<string>("1001,1002,1123,2203,6051,66".Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries)).Find(code => sub_mother.sub_code.ToString().StartsWith(code)) == null)
                {
                    return $"请在科目【1001现金, 1002银行, 1123预付款, 2203预收, 6051其他收入, 66损益类(支出)】下添加子科目";
                }
                if (sub_samename.Count > 0)
                {
                    string type = sub_samename[0].sub_type;
                    string type_label = "";
                    switch (type)
                    {
                        case "QT":
                            type_label = "现金银行";
                            break;
                        case "YS":
                            type_label = "预收";
                            break;
                        case "YF":
                            type_label = "预付";
                            break;
                    }
                    return $"存在相同名称的【{type_label}】类型科目";
                }
            }

            //新旧科目都判断
            if (data.sub_code!="1123" && !data.sub_code.ToString().StartsWith(sub_mother.sub_code))
            {
                return "科目代码不符合规范，请以父级科目代码开头";
            }
            if (data.sub_code == "1123" && data.sub_type!="")
            {
                return "预付类型请在科目【1123预付款】下添加子科目";
            }
            if (sub_mother.sub_type != "")
            {
                return "父级科目存在类型，请先调整父级科目";
            }
            
            return "";
        }

        public async Task<string> CheckNewSubBeforeSave(dynamic data, string companyID)
        {
            if (data.level.ToString() != "")
            {
                if (Convert.ToInt16(data.level) > 3)
                {
                    return "暂不支持层级为4及以上的科目";
                }
            }
            else
            {
                return "请勿新增或修改大类";
            }
            if (data.direction == "")
            {
                return "借贷不能为空";
            }
            if (((string)data.sub_name).Contains("-"))
            {
                return "科目名称不能包含-";
            }
            if (((string)data.sub_name).Contains(" "))
            {
                return "科目名称不能包含空格";
            }
            string[] assister_types=data.assister_types.ToString().Split(',');
            if(assister_types.Length > 2)
            {
                return "辅助核算最多选择两个核算项";
            }
            //sub_type
            //QT
            if (data.sub_type == "QT" && (((string)data.sub_code).Substring(0, 4) != "1001" && ((string)data.sub_code).Substring(0, 4) != "1002"))
            {
                return "现金银行类型请添加在1001库存现金 或 1002银行存款科目下";
            }
            if (data.sub_type == "QT" && data.direction != "1")
            {
                return "现金银行类型请选择借贷为“借”";
            }
            //QTSR
            if (data.sub_type == "QTSR" && !data.sub_code.ToString().StartsWith('5'))
            {
                return "其他收入类型请添加在损益类科目下";
            }
            if (data.sub_type == "QTSR" && data.direction != "-1")
            {
                return "其他收入类型请选择借贷为“贷”";
            }
            //ZC
            if (data.sub_type == "ZC" && !data.sub_code.ToString().StartsWith('5'))
            {
                return "费用支出类型请添加在损益类科目下";
            }
            if (data.sub_type == "ZC" && data.direction != "1")
            {
                return "费用支出类型请选择借贷为“借”";
            }
            //YS
            if (data.sub_type == "YS" && ((string)data.sub_code).Substring(0, 4) != "2203")
            {
                return "预收类型请添加在2203预收账款科目下";
            }
            if (data.sub_type == "YS" && data.direction != "-1")
            {
                return "预收类型请选择借贷为“贷”";
            }
            //YF
            if (data.sub_type == "YF" && ((string)data.sub_code).Substring(0, 4) != "1123")
            {
                return "预付类型请添加在1123预付账款科目下";
            }
            if (data.sub_type == "YF" && data.direction != "1")
            {
                return "预付类型请选择借贷为“借”";
            }
            if (data.sub_type == "JK" && !data.sub_code.ToString().StartsWith('2'))
            {
                return "借款类型只能在负债类下选择";
            }
            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("subMother", $"select sub_code,sub_name,direction,status,COALESCE(is_order,false) as is_order,assister_types from cw_subject where sub_id={data.mother_id} and company_id = {companyID}");
            QQ.Enqueue("subChildDirection", $"select sub_code from cw_subject where mother_id={data.mother_id} and company_id = {companyID} order by sub_code::text");
            QQ.Enqueue("subChildrenName", $"select sub_code from cw_subject where company_id = {companyID} and mother_id={data.mother_id} and  sub_name='{data.sub_name}'");
            QQ.Enqueue("setting", $"select coalesce(setting ->>  'accountingCodeLength','') accountingcodelength from company_setting where company_id = {companyID} ");
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            dynamic subMother = null;
            dynamic subChildDirection = null;
            dynamic subChildrenName = null;
            dynamic setting = null;
            while (QQ.Count > 0)
            {
                string sqlName=QQ.Dequeue();
                if (sqlName == "subMother") subMother = CDbDealer.Get1RecordFromDr(dr, false);
                else if(sqlName== "subChildDirection") subChildDirection= CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "subChildrenName") subChildrenName = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "setting") setting = CDbDealer.Get1RecordFromDr(dr, false);
            }
            QQ.Clear();

            if (subMother.status == "0")
            {
                return "父级已停用";
            }
            if (Convert.ToBoolean(subMother.is_order) == true)
            {
                return "父级为定货会账户，无法添加子科目";
            }
            if (!data.sub_code.ToString().StartsWith(subMother.sub_code))
            {
                return  "科目代码不符合规范，请以父级科目编码开头";
            }
            if (subMother.sub_code == "5" && data.sub_id == "")
            {
                return "请勿在【损益类】下添加其他一级科目，此科目余额将无法体现在利润表";
            }
            if (!data.sub_code.ToString().StartsWith("56") && !data.sub_code.ToString().StartsWith("57") && !data.sub_code.ToString().StartsWith("58") && data.sub_type == "ZC")
            {
                return "该科目无法设置为费用支出类型";
            }
            //还有几个类型要添加限制
            if (Array.IndexOf(new Int32[] { 112301, 220301, 1901, 560213, 560304 }, Convert.ToInt32(subMother.sub_code)) > -1)
            {
                return $"模板科目【{subMother.sub_name}】不允许增加子科目";
            }
            if (Array.IndexOf(new Int16[] { 1122, 2202, 5001, 5401, 1405 }, Convert.ToInt32(subMother.sub_code)) > -1)
            {
                return $"模板科目【{subMother.sub_name}】不允许增加子科目，请查询辅助核算";
            }
            if(data.sub_type == "YF" && !data.sub_code.ToString().StartsWith("1123"))
            {
                return $"预付账户类型请放在科目【1123 预付账款】下";
            }
            else if(data.sub_type == "YS" && !data.sub_code.ToString().StartsWith("2203"))
            {
                return $"预收账户类型请放在科目【2203 预收账款】下";
            }
            if(data.assister_types.ToString() != "")
            {
                if ((data.sub_code.ToString()=="1122" || data.sub_code.ToString().StartsWith("2203")) && (assister_types.Contains("INV") || assister_types.Contains("S")))
                {
                    return "该科目无法添加 商品/供应商 作为辅助核算项";
                }
                if ((data.sub_code.ToString() == "2202" || data.sub_code.ToString().StartsWith("1123")) && (assister_types.Contains("INV") || assister_types.Contains("C")))
                {
                    return "该科目无法添加 商品/客户 作为辅助核算项";
                }
                if ((data.sub_code.ToString().StartsWith("1221") || data.sub_code.ToString().StartsWith("2241")) && (assister_types.Contains("INV")))
                {
                    return "该科目无法添加 商品 作为辅助核算项";
                }
                if ((data.sub_code.ToString() == "1405" || data.sub_code.ToString() == "5401") && (assister_types.Contains("C") || assister_types.Contains("S")))
                {
                    return "该科目无法添加 客户/供应商 作为辅助核算项";
                }
                if (data.sub_code.ToString() == "5001" && assister_types.Contains("S"))
                {
                    return "该科目无法添加 供应商 作为辅助核算项";
                }
                //任何科目都可以选择 业务员/部门 作为辅助项
                if (Array.IndexOf(new string[] { "1122", "2202", "5001", "5401", "1405" }, data.sub_code.ToString()) <= -1 
                    && !data.sub_code.ToString().StartsWith("1123")
                    && !data.sub_code.ToString().StartsWith("1221")
                    && !data.sub_code.ToString().StartsWith("2203")
                    && !data.sub_code.ToString().StartsWith("2241")
                    && (assister_types.Contains("INV") || assister_types.Contains("C") || assister_types.Contains("S")))
                {
                    return "该科目无法添加 商品/客户/供应商 作为辅助核算项";
                }
            }
            if (subMother.assister_types.ToString() != "")
            {
                return "请先清空父级辅助核算";
            }
            
            if (subChildDirection != null && subChildDirection.sub_code.ToString() == data.sub_code.ToString() && subMother.direction.ToString() != data.direction.ToString())//已有子科目
            {
                return "第一个子科目借贷方向需与父级相同";
            }

            if (subChildrenName != null && data.sub_id=="")
            {
                return "同一科目下不能创建同名的子科目";
            }

            string codeLengthStr = "";
            if (setting.accountingcodelength.ToString()=="")
            {
                codeLengthStr = "[4,2,2,2,2]";
            }
            else
            {
                codeLengthStr = setting.accountingcodelength.ToString();
            }
            List<int> cl = codeLengthStr.Substring(1, codeLengthStr.Length - 2).Split(',').Select(Int32.Parse).ToList();
            for (int j = 1; j < cl.Count(); j++)
            {
                cl[j] = cl[j - 1] + cl[j];
            }
            int level = Convert.ToInt16(data.level);
            if (data.sub_code.ToString().Length != cl[level - 1])
            {
                return "科目代码长度不符合规范";
            }

            return "";
        }

        public async Task<string> CheckSubBeforeSave(dynamic data, string companyID)
        {
            bool is_order = false;
            if (data.is_order != null && data.is_order.ToString()!="") is_order = Convert.ToBoolean(data.is_order);
            bool for_pay = false;
            if (data.for_pay != null && data.for_pay.ToString() != "") for_pay = Convert.ToBoolean(data.for_pay);
            string status = "1";
            if (data.status != null && data.status.ToString() != "") status = data.status.ToString();

            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("sub", $"select sub_id, sub_code, sub_name, coalesce(sub_type,'') as sub_type,direction, COALESCE(is_order,false) is_order, coalesce(status,'1') as status, assister_types from cw_subject where sub_id={data.sub_id} and company_id = {companyID}");
            QQ.Enqueue("subChildren", $"select sub_code from cw_subject where mother_id={data.sub_id} and company_id = {companyID} and coalesce(status,'1')='1'");
            QQ.Enqueue("subThisName", $"select sub_code,sub_name from cw_subject where company_id = {companyID} and sub_name='{data.sub_name}' and sub_id<>{data.sub_id};");
            QQ.Enqueue("biz", $@"( select sheet_id as c from sheet_sale_main where (payway1_id={data.sub_id} or payway2_id={data.sub_id} or payway3_id={data.sub_id}) and company_id={companyID} )
                union all
                ( select sheet_id from sheet_buy_main where (payway1_id={data.sub_id} or payway2_id={data.sub_id} or payway3_id={data.sub_id}) and company_id={companyID} )
                union all
                ( select sheet_id from sheet_get_arrears_main where (payway1_id={data.sub_id} or payway2_id={data.sub_id} or payway3_id={data.sub_id}) and company_id={companyID} )
                union all
                ( select sheet_id from sheet_prepay where (prepay_sub_id={data.sub_id} or payway1_id={data.sub_id} or payway2_id={data.sub_id}) and company_id={companyID} )
                union all
                ( select sub_id as sheet_id from prepay_balance where sub_id={data.sub_id} and company_id={companyID} )
                union all
                ( select sheet_id from sheet_item_ordered_adjust_main where (prepay_sub_id={data.sub_id} or payway1_id={data.sub_id} or payway2_id={data.sub_id}) and company_id={companyID} )
                union all
                ( select prepay_sub_id as sheet_id from items_ordered_balance where prepay_sub_id={data.sub_id} and company_id={companyID} )
                union all
                ( select sheet_id from sheet_fee_out_main where (payway1_id={data.sub_id} or payway2_id={data.sub_id}) and company_id={companyID} )
                union all
                ( select sheet_id from sheet_fee_out_detail where fee_sub_id={data.sub_id} and company_id={companyID} )
                union all
                ( select sheet_id from display_agreement_main where fee_sub_id={data.sub_id} and company_id = {companyID} )
                union all
                ( select sheet_id from sheet_cashbank_transfer_detail where (money_out_id={data.sub_id} or money_in_id={data.sub_id}) and company_id = {companyID} )
                ");
            QQ.Enqueue("vo", $"select sheet_id from cw_voucher_detail where sub_id={data.sub_id} and company_id = {companyID}");
            QQ.Enqueue("init", $"select sheet_id from cw_op_sub_init_detail where balance is not null and sub_id={data.sub_id} and company_id={companyID}");
            QQ.Enqueue("assister", $@"
                ( select assister1_type,assister2_type from cw_voucher_detail where  sub_id={data.sub_id} and company_id={companyID} and    (assister1_type is not null or assister2_type is not null) and coalesce(change_amount,0)<>0 )
                union all 
                ( select assister1_type, assister2_type from cw_op_sub_init_assister_detail where company_id={companyID} and sub_id={data.sub_id} and (assister1_type is not null or assister2_type is not null) and coalesce(balance,0)<>0 )");
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            dynamic sub = null;
            dynamic subChildren = null;
            dynamic subThisName = null;
            dynamic biz = null;
            dynamic vo = null;
            dynamic init = null;
            dynamic assister = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "sub") sub = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "subChildren") subChildren = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "subThisName") subThisName = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "biz") biz = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "vo") vo = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "init") init = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "assister") assister = CDbDealer.Get1RecordFromDr(dr, false);
            }
            QQ.Clear();

            Int64 sub_code = Convert.ToInt64(sub.sub_code);
            Int64 data_code = Convert.ToInt64(data.sub_code);
            if(Array.IndexOf(new Int64[] { 1122, 1123, 112301, 112302, 2202, 2203, 220301, 220302, 1405, 1901, 3103, 3104, 310406, 5001, 5051, 5401, 5602, 560212, 560213, 5603, 560304, 1221, 2241, 560302 }, sub_code) > -1)//涉及code_length
            {
                if (data_code != sub_code) return "业务关联科目不能修改编码";
                //if (data.sub_name != sub.sub_name) return "业务关联科目不能修改名称";
                if (data.direction != sub.direction) return "业务关联科目不能修改方向";
                if (data.status=="0") return "业务关联科目不能停用";
                //if (sub_code == 112301 && (data.sub_type != null && data.sub_type != "")) return $"为保留至少一个不含类型的预付科目，该科目不允许修改类型";
                //if (sub_code == 220301 && (data.sub_type != "YS" || Convert.ToBoolean(data.is_order))) return $"为保留至少一个不为定货会账户的预收科目，该科目不允许修改类型";
            }
            if (subChildren!=null)
            {
                if (status == "0") return "请先停用该科目类别的明细科目";
                if (data.sub_type != "") return "请为明细科目设置类型";
                if (is_order == true) return "请为明细科目设置定货会账户";
                if (for_pay) return "请为明细科目添加可用于支付";
                if (data.assister_types != "") return "请为明细科目设置辅助核算";
            }
            if (subThisName != null && Array.IndexOf(new Int16[] { 1123, 2203 }, Convert.ToInt16(subThisName.sub_code.ToString().Substring(0, 4))) > -1)
            {
                return "【1123 预付账款】/【2203 预收账款】明细科目不允许重名";
            }
            if(biz != null || vo != null || init != null)
            {
                if (data.direction != sub.direction) return "该科目已被使用过,无法修改借贷";
                if (is_order != Convert.ToBoolean(sub.is_order)) return "该科目已被使用过,无法修改是否定货会账户";
            }
            string[] sub_assister = sub.assister_types.Split(',');
            List<string> sub_assisterList = sub_assister.ToList<string>();
            sub_assisterList.Sort();
            string[] data_assister = data.assister_types.ToString().Split(",");
            List<string> data_assisterList = data_assister.ToList<string>();
            data_assisterList.Sort();
            if (assister!=null && (assister.assister1_type != null || assister.assister2_type !=null) && (sub.assister_types.ToString().IndexOf(assister.assister1_type)>-1 || sub.assister_types.ToString().IndexOf(assister.assister2_type) > -1) && String.Join(',', sub_assisterList) != String.Join(',', data_assisterList))
            {
                return "该科目辅助项已被使用过,无法修改辅助核算";
            }
            return "";
        }

        /// <param name="sub">只有sub_id</param>
        /// <param name="data">前端传入的待保存信息</param>
        public async static  Task<JsonResult> SaveBalance(dynamic sub, dynamic data, string companyID, CMySbCommand cmd)
        {
            cmd.ActiveDatabase = "";
            //py_str：新增科目时的的保存
            string py_str = PinYinConverter.GetJP(sub.sub_name.ToString());
            string sql = $@"update cw_subject set py_str='{py_str}' where company_id={companyID} and sub_id={sub.sub_id};";
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();

            SQLQueue QQ = new SQLQueue(cmd);
            QQ.Enqueue("subChild", $@"select sub_id,sub_code from cw_subject where company_id={companyID} and mother_id={sub.mother_id}");
            QQ.Enqueue("subMother", $@"select sub_id,sub_code,sub_type,COALESCE(is_order,false) as is_order from cw_subject where company_id={companyID} and sub_id={sub.mother_id}");
            QQ.Enqueue("setting", $"select coalesce(setting ->>  'useAccounting','false') useaccounting from company_setting where company_id = {companyID}");
            CMySbDataReader dr = await QQ.ExecuteReaderAsync();
            dynamic subChild = null;
            dynamic subMother = null;
            dynamic setting = null;
            while (QQ.Count > 0)
            {
                string sqlName = QQ.Dequeue();
                if (sqlName == "subChild") subChild = CDbDealer.GetRecordsFromDr(dr, false);
                else if (sqlName == "subMother") subMother = CDbDealer.Get1RecordFromDr(dr, false);
                else if (sqlName == "setting") setting = CDbDealer.Get1RecordFromDr(dr, false);
            }
            QQ.Clear();

            if (Convert.ToBoolean(subMother.is_order) == true )
            {
                return new JsonResult(new { result = "Error", msg = "定货会账户请勿增加子科目" });
            }
            if (subMother.sub_type.ToString() != "")
            {
                sql = $@"update cw_subject set sub_type=null where company_id={companyID} and sub_id={subMother.sub_id};";
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
            //检查是否开账
            if (!Convert.ToBoolean(setting.useaccounting))
            {
                return new JsonResult(new { result = "OK", msg = "" });//没开账则不需要保存以下列表
            }

            //检查是否是第一个子科目
            dynamic checkSubConfirm = (await PaywaysViewController.CheckFirstChild(companyID, data, subMother, cmd) as dynamic).Value;
            if (checkSubConfirm.result != "OK")
            {
                return new JsonResult(new { result = "Error", msg = checkSubConfirm.msg });
            }
            if (subChild.Count == 1 && checkSubConfirm.result.ToString() == "OK" && checkSubConfirm.needConfirm.ToString().ToLower() == "true")//开账后新增第一个子科目
            {
                //init（新增一行）
                sql = $@"insert into cw_op_sub_init_detail (company_id,sheet_id,sub_id,balance) select ci3.company_id,1 as sheet_id,{sub.sub_id} as sub_id,ci3.balance from cw_op_sub_init_detail ci3 left join cw_subject cs on ci3.company_id=cs.company_id and ci3.sub_id=cs.sub_id where ci3.company_id={companyID} and cs.sub_id={sub.mother_id};";
                //sub_balance（每个period新增一行）
                sql += $@"insert into cw_sub_balance (company_id,period,sub_id,month_start_balance,year_start_balance,balance,debit_amount,credit_amount) select csb3.company_id,period, {sub.sub_id} as sub_id,month_start_balance,year_start_balance,balance,debit_amount,credit_amount from cw_sub_balance csb3 left join cw_subject cs on csb3.company_id=cs.company_id and csb3.sub_id=cs.sub_id where csb3.company_id={companyID} and cs.sub_id={sub.mother_id};";
                //cw_voucher（每个period使用过父级则都修改）
                sql += $@"update cw_voucher_detail set sub_id={sub.sub_id} where company_id={companyID} and sub_id={sub.mother_id};";
                //assister init
                sql += $"update cw_op_sub_init_assister_detail set sub_id={sub.sub_id} where company_id={companyID} and sub_id={sub.mother_id};";
                //assister（辅助只记录了明细）
                sql += $@"update cw_sub_balance_assister set sub_id={sub.sub_id} where company_id={companyID} and sub_id={sub.mother_id};";

                //业务单据（筛选时已禁止添加子科目）
                //sql += $@"update sheet_sale_main set payway1_id={sub.sub_id} where company_id={companyID} and payway1_id={sub.mother_id} ;";
                //sql += $@"update sheet_sale_main set payway2_id={sub.sub_id} where company_id={companyID} and payway2_id={sub.mother_id} ;";
                //sql += $@"update sheet_sale_main set payway3_id={sub.sub_id} where company_id={companyID} and payway3_id={sub.mother_id} ;";
                //sql += $@"update sheet_buy_main set payway1_id={sub.sub_id} where company_id={companyID} and payway1_id={sub.mother_id} ;";
                //sql += $@"update sheet_buy_main set payway2_id={sub.sub_id} where company_id={companyID} and payway2_id={sub.mother_id} ;";
                //sql += $@"update sheet_get_arrears_main set payway1_id={sub.sub_id} where company_id={companyID} and payway1_id={sub.mother_id} ;";
                //sql += $@"update sheet_get_arrears_main set payway2_id={sub.sub_id} where company_id={companyID} and payway2_id={sub.mother_id} ;";
                //sql += $@"update sheet_prepay set prepay_sub_id={sub.sub_id} where company_id={companyID} and prepay_sub_id={sub.mother_id} ;";
                //sql += $@"update prepay_balance set sub_id={sub.sub_id} where company_id={companyID} and sub_id={sub.mother_id} ;";
                //sql += $@"update sheet_item_ordered_adjust_main set prepay_sub_id={sub.sub_id} where company_id={companyID} and prepay_sub_id={sub.mother_id} ;";
                //sql += $@"update sheet_item_ordered_adjust_main set payway1_id={sub.sub_id} where company_id={companyID} and payway1_id={sub.mother_id} ;";
                //sql += $@"update sheet_item_ordered_adjust_main set payway2_id={sub.sub_id} where company_id={companyID} and payway2_id={sub.mother_id} ;";
                //sql += $@"update items_ordered_balance set prepay_sub_id={sub.sub_id} where company_id={companyID} and prepay_sub_id={sub.mother_id} ;";
                //sql += $@"update sheet_fee_out_main set payway1_id={sub.sub_id} where company_id={companyID} and payway1_id={sub.mother_id} ;";
                //sql += $@"update sheet_fee_out_main set payway2_id={sub.sub_id} where company_id={companyID} and payway2_id={sub.mother_id} ;";
                //sql += $@"update sheet_fee_out_detail set fee_sub_id={sub.sub_id} where company_id={companyID} and fee_sub_id={sub.mother_id} ;";
                //sql += $@"update display_agreement_main set fee_sub_id={sub.sub_id} where company_id={companyID} and fee_sub_id={sub.mother_id} ;";
            }
            else//开账后新增一级科目 || 开账后新增子科目但不是第一个 || 开账后新增第一个子科目但父级科目还未使用过
            {
                sql = $@"insert into cw_op_sub_init_detail (company_id,sheet_id,sub_id) values ({companyID},1,{sub.sub_id});";
                sql += $@"insert into cw_sub_balance (company_id,period,sub_id) select company_id,period,{sub.sub_id} as sub_id from cw_sub_balance where company_id={companyID} group by company_id,period;";
            }
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
            return new JsonResult(new { result = "OK", msg = "" });
        }

        /// <summary>
        /// 获取中文字符串的首字母
        /// </summary>
        /// <param name="strText">中文字符串</param>
        /// <returns></returns>
       

    }
}