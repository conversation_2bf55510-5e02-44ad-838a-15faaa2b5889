﻿[
  {
    "id": 0,
    "title": "单据类",
    "subsetAttr": [
      {
        "sonId": 0,
        "MenuId": 100,
        "title": "销售订单",
        "icons": "icon-tianjia",
        "iSactive": false,
        "goUrl": "/SaleSheet?sheetType=XD"
      },
      {
        "sonId": 1,
        "MenuId": 102,
        "title": "销售单",
        "iSactive": false,
        "icons": "icon-xiaosh<PERSON>",
        "goUrl": "/SaleSheet?sheetType=X"
      },
      {
        "sonId": 2,
        "MenuId": 101,
        "title": "退货订单",
        "iSactive": false,
        "icons": "icon-tuihuo1",
        "goUrl": "/SaleSheet?sheetType=TD"
      },
      {
        "sonId": 3,
        "MenuId": 103,
        "title": "退货单",
        "iSactive": false,
        "icons": "icon-tuihuo",
        "goUrl": "/SaleSheet?sheetType=T"
      },
      {
        "sonId": 4,
        "MenuId": 300,
        "title": "调拨单",
        "iSactive": false,
        "icons": "icon-tiaobo",
        "goUrl": "/TransferOrder"
      },
      {
        "sonId": 5,
        "MenuId": 400,
        "title": "收款单",
        "iSactive": false,
        "icons": "icon-shoukuan1",
        "goUrl": "/Collection"
      },
      {
        "sonId": 6,
        "MenuId": 303,
        "title": "盘点单",
        "iSactive": false,
        "icons": "icon-pandian1",
        "goUrl": "/InventoryList"
      },
      {
        "sonId": 7,
        "MenuId": 402,
        "title": "预收款单",
        "icons": "icon-yushou",
        "iSactive": false,
        "goUrl": "/PrepayReceipt"
      },
      {
        "sonId": 8,
        "MenuId": 404,
        "title": "费用支出",
        "icons": "icon-zhichu",
        "iSactive": false,
        "goUrl": "/FeeOut"
      },
      {
        "sonId": 9,
        "MenuId": 0,
        "title": "查看单据",
        "icons": "icon-chakan",
        "iSactive": false,
        "goUrl": "/ViewDocuments"
      }
    ]
  },
  {
    "id": 1,
    "title": "报表类",
    "subsetAttr": [
      {
        "sonId": 10,
        "MenuId": 599,
        "title": "销量走势图",
        "icons": "icon-huizong",
        "iSactive": false,
        "pattern": [
          {
            "ids": 0,
            "titles": "chart",
            "names": "图表"
          },
          {
            "ids": 1,
            "titles": "overview",
            "names": "概览"
          }
        ],
        "goUrl": "/CustomerRanking?types=salesTrend&names=销量走势图"
      },
      {
        "sonId": 11,
        "MenuId": 506,
        "title": "业务销售排行",
        "icons": "icon-paihang1",
        "iSactive": false,
        "pattern": [
          {
            "ids": 0,
            "titles": "chart",
            "names": "图表"
          },
          {
            "ids": 1,
            "titles": "overview",
            "names": "概览"
          }
        ],
        "goUrl": "/CustomerRanking?types=salesmanTrend&names=业务销量排行"
      },
      {
        "sonId": 12,
        "MenuId": 508,
        "title": "客户排行榜",
        "icons": "icon-paihang",
        "iSactive": false,
        "pattern": [
          {
            "ids": 0,
            "titles": "chart",
            "names": "图表"
          },
          {
            "ids": 1,
            "titles": "overview",
            "names": "概览"
          }
        ],
        "goUrl": "/CustomerRanking?types=customerRanking&names=客户排行榜"
      },
      {
        "sonId": 13,
        "MenuId": 509,
        "title": "品牌销量汇总",
        "icons": "icon-xingzhuang873kaobei",
        "iSactive": false,
        "pattern": [
          {
            "ids": 0,
            "titles": "chart",
            "names": "图表"
          },
          {
            "ids": 1,
            "titles": "overview",
            "names": "概览"
          }
        ],
        "goUrl": "/CustomerRanking?types=brandSalesSum&names=品牌销量汇总"
      },
      {
        "sonId": 14,
        "MenuId": 404,
        "title": "库存查询",
        "iSactive": false,
        "icons": "icon-chaxun",
        "goUrl": "/Stock"
      },
      {
        "sonId": 15,
        "MenuId": 500,
        "title": "应收款",
        "iSactive": false,
        "icons": "icon-shoukuan1",
        "goUrl": "/Receivables"
      },
      {
        "sonId": 16,
        "MenuId": 104,
        "title": "收款对账",
        "iSactive": false,
        "icons": "icon-huizong1",
        "goUrl": "/CheckAccount"
      },
      {
        "sonId": 17,
        "MenuId": 503,
        "title": "外勤轨迹",
        "iSactive": false,
        "icons": "icon-baifang3",
        "goUrl": "/journey"
      },
      {
        "sonId": 18,
        "MenuId": 511,
        "title": "调拨汇总表",
        "icons": "icon-pandian1",
        "iSactive": false,
        "goUrl": "/AllocationSummary"
      },
      {
        "sonId": 19,
        "MenuId": 402,
        "title": "预收款余额",
        "icons": "icon-pandian1",
        "iSactive": false,
        "goUrl": "/advancePayment"
      }
    ]
  },
  {
    "id": 2,
    "title": "档案类",
    "subsetAttr": [
      {
        "sonId": 20,
        "MenuId": 600,
        "title": "商品档案",
        "iSactive": false,
        "icons": "icon-dangan1",
        "goUrl": "/GoodsArchives"
      },
      {
        "sonId": 21,
        "MenuId": 603,
        "title": "客户档案",
        "iSactive": false,
        "icons": "icon-dangan",
        "goUrl": "/CustomerArchives"
      }
    ]
  },
  {
    "id": 3,
    "title": "市场管理类",
    "subsetAttr": [
      {
        "sonId": 30,
        "MenuId": 505,
        "title": "拜访门店",
        "iSactive": false,
        "icons": "icon-baifang3",
        "goUrl": "/visitUser"
      }
    ]
  }
]