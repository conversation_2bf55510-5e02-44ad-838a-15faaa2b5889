﻿@page
@model ArtisanManage.Pages.BaseInfo.ReceiptTemplateViewModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>
    <title></title>
    <partial name="_QueryPageHead" model="Model.PartialViewModel"/> 
    
    <script type="text/javascript">
           window.g_operKey = '@Html.Raw(Model.OperKey)'; 

    	    var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
                @Html.Raw(Model.m_createGridScript)
                $('#gridItems').jqxGrid({ columnsheight: 36 });; 
               $('#gridItems').jqxGrid({ autorowheight: true, autoheight: true }); 
                $("#gridItems").on("cellclick", function (event) {
                    var args = event.args;
                });
                 setTimeout(function () {
                     ReQueryData();
                },400)
            });
        var elementTypeWithMouse = ''; var rowPosition;
        function queryDone() {
            window.operPannel = null;
            $('.tmp-list>div').hover(function () {
                   
              rowPosition= $('body').offset();
              var pos = $(this).offset();
              pos.left -= rowPosition.left;
              pos.top -= rowPosition.top;

                
                var duration = 0;
                if (elementTypeWithMouse == "") duration = 500;
                window.operPannel.stop(true);
                if ($('#oper_pannel').css('display') == "none") {
                    $('#oper_pannel').css({"left": pos.left + 'px', 'top': pos.top - 30 + 'px' });
                }
                window.operPannel.css({ 'display': 'flex' ,'z-index':9999999});
                window.operPannel.animate({ 'opacity': '1',"left":pos.left + 'px','top': pos.top-32 + 'px' }, duration, null, function () {
                      
                });
                elementTypeWithMouse = 'template';
                window.curTemplateID = this.id;
                var rowIndex = parseInt($(this.parentNode.parentNode.parentNode).find('.jqx-grid-cell-pinned>div').text())-1;
                window.curTemplateSheetType =$('#gridItems').jqxGrid('getcellvalue', rowIndex, 'sheet_type'); 
                
            }, function () { 
                    setTimeout(function () {
                        if (elementTypeWithMouse == '') {
                            window.operPannel.animate({ 'opacity': '0' }, 500, null, function () {
                                //if (elementTypeWithMouse == '') {
                                window.operPannel.css({ 'display': 'none' });
                                // }
                            });
                        } 
                   }, 200);
                   elementTypeWithMouse = '';     
                              
            })
            window.operPannel = $('#oper_pannel');
            if (!window.operPannel || window.operPannel.length == 0) {
                window.operPannel = $('<div id="oper_pannel"><div id="setDefault">设为默认</div><div id="delete">删除</div></div>');
                $('body').append(window.operPannel);
                $('#oper_pannel').hide();
                $('#oper_pannel').hover(function () {
                    setTimeout(function () {
                        elementTypeWithMouse = 'oper_pannel'
                    }, 50);
                } , function () {
                    elementTypeWithMouse = '';
                });
                $('#oper_pannel>div').on('click', function () {
                     $('#oper_pannel').hide();
                    if (this.id == "delete") {
                        jConfirm('确定要删除吗?', function () {

                              $.ajax({
				                url: "/api/ReceiptTemplateView/DeleteTemplate", //url地址
				                 contentType: 'application/json',
                                 type: "post", //发起请求的方式
                                 data: JSON.stringify({
                                            operKey:g_operKey,
                                            templateID: curTemplateID,
					                     }),
				                success: function(res) {
                                    if (res.result === "OK") {  
                                       
                                        ReQueryData(); 
					                }
				                },
				                error: function() {

				                }
			               });

                        })
                         
                     }
                     else if (this.id == "setDefault") {

                            $.ajax({
				                url: "/api/ReceiptTemplateView/SetDefaultTemplate", //url地址
				                 contentType: 'application/json',
                                        type: "post", //发起请求的方式
                                        data: JSON.stringify({
                                            operKey:g_operKey,
                                            template_id: curTemplateID,
                                            sheet_type: curTemplateSheetType
					                     }),
				                success: function(res) {
                                    if (res.result === "OK") {
                                        ReQueryData(); 
					                }
				                },
				                error: function() {

				                }
			               })
                
                        }
              
                    })
                } 
               
       
    
            var aa = $('.tmp-list>div')

          $('.tmp-list>div').on('click', function () {
                var templateID = this.id;
                var rowIndex = parseInt($(this.parentNode.parentNode.parentNode).find('.jqx-grid-cell-pinned>div').text())-1;
                var sheetType = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'sheet_type');
                var templateName = $(this).text();
                window.parent.newTabPage("模板" + templateName, `/Setting/ReceiptTemplate?sheetType=${sheetType}&templateID=${templateID}`, window);
            });

            $('.tmp-list>svg').on('click', function (e) {     
                 var rowIndex = parseInt($(this.parentNode.parentNode.parentNode).find('.jqx-grid-cell-pinned>div').text())-1;
                var sheetType = $('#gridItems').jqxGrid('getcellvalue', rowIndex, 'sheet_type'); 
                window.parent.newTabPage("新建模板", `/Setting/ReceiptTemplate?sheetType=${sheetType}&templateID=`, window);
            });
           
        }
        function WaitToReQueryData(wait) {
            setTimeout(function () {
                ReQueryData();
            }, wait);
           
        }
        function ReQueryData() {
            QueryData(null, queryDone);
        }

 
    function default_template_Render (index, datafield, value, defaultvalue, column, rowdata) {   
           var allTmp= rowdata.all_template;
           if(allTmp) allTmp=JSON.parse(allTmp);
        var default_template = '';
        var id = "";
       if(allTmp){
              allTmp.some(function(tmp){
                  if (tmp[2] == 1) { default_template = tmp[1]; id = tmp[0]; return true;}
       
              }); 
        }
        var innerDiv = "";
        if (id) {
            innerDiv=`<div id=${id}>${default_template}</div>`;
        }
           return `<div class="default-template">${innerDiv}</div>`;
    }
       
    </script>

    <style>
        .jqx-fill-state-hover{
            background-color:transparent;
        }
        .jqx-fill-state-pressed {
           background-color: transparent;
        }
        .jqx-grid-cell-pinned {
            background-color: #fbfbfb;
            display:flex;
            align-items:center;
            justify-content:center;

        }
        .jqx-grid-cell-left-align{
            margin-left:10px;font-size:15px;
        }
        .jqx-grid-column-header { 
            font-size: 15px;
        }
        #oper_pannel {
            position: fixed;
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 9999999;
            background-color: #888;
            color: #fff;
            width: 200px;
            border-radius: 5px;
        }
        #oper_pannel>div{
            width:70px;height:30px;cursor:pointer;
            text-align:center;line-height:30px;
        }
        .tmp-list {
            display: flex;
            flex-wrap: wrap;
            align-items:center;
            padding-bottom:-10px;
        }
            .tmp-list > div {
                width: 120px;
                height: 50px;
                display: block;
                margin: 8px;
                background-color: #f2d8e0;
                line-height: 50px;
                text-align: center;
                flex-direction: row;
                cursor: pointer;
            }

            .tmp-list > svg {
                width: 30px;
                height: 30px;
                display: inline-block;
                margin: 8px; 
                cursor: pointer;
                fill:#ccc;
            }


        .default-template {
            height:100%;
            display: flex;
            justify-content:center;
            align-items: center;
        }

            .default-template > div {
                width: 120px;
                height: 50px;
                display: block;
                margin: 8px;
                background-color: #f2d8e0;
                line-height: 50px;
                text-align: center;
                flex-direction: row;
                cursor: pointer;
            }

       
    </style>
</head>

<body>
   
    <div style="display:flex;padding-top:20px;">
         
       <div id="divHead" class="headtail">
          <!--<div><div><label>客户</label></div> <div><div id="txtCust"></div></div></div>
            <div><div><label>业务员</label></div> <div><div id="cmbSeller"></div></div></div>
            <div><div><label>仓库</label></div> <div><div id="cmbBranch"></div></div></div>
            <div><div><label>发生时间</label></div> <div><div id="cmbDate"></div></div></div>
            <div><div><label>备注</label></div> <div><div id="txtBrief"></div></div></div>
            -->
            <div style="float:none;height:0px; clear:both;"></div>
        </div>
        <!-- <button onclick="ReQueryData()" style="margin-left:20px;">查询</button>-->
     </div>
   <div style="height:100%;overflow-y:scroll;">  
        <div id="gridItems" style="height:100%;"></div>   
        <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div> 
     
    </div>    

    

</body>
</html>