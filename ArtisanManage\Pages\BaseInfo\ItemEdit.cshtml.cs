using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;
using ArtisanManage.Services;
using Newtonsoft.Json;
using static ArtisanManage.Pages.BaseInfo.ItemEditModel;
using System.Net.Http;
using System.Dynamic;
using System.Text;
using Microsoft.CodeAnalysis.Operations;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Numerics;
using System.Runtime.InteropServices.JavaScript;
using Quartz.Util;
using System.Text.RegularExpressions;
using ArtisanManage.MyJXC;
using Org.BouncyCastle.Asn1.Ocsp;
using ArtisanManage.YingJiangBackstage.Pojo;
using System.Diagnostics;
using static HuaWeiObsController.HuaWeiObs;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ItemUnitGrid : FormDataGrid
    {
        public override void OnGridDataGot(List<Dictionary<string, dynamic>> lstRows)
        {

            foreach (var row in lstRows)
            {
                Dictionary<string, string> newValues = new Dictionary<string, string>();
                foreach (var cell in row)
                {
                    if ("wholesale_price,retail_price,buy_price,cost_price_spec,cost_price_avg".Contains(cell.Key))
                    {
                        string s = cell.Value;
                        if (s.EndsWith(".00"))
                            s = s.Replace(".00", "");
                        newValues.Add(cell.Key, s);
                    }
                }
                foreach (var cell in newValues)
                {
                    row[cell.Key] = cell.Value;
                }
            }


            lstRows.Sort((a, b) =>
            {
                double au = 0, bu = 0;
                if (a["unit_factor"] != "") au = Convert.ToDouble(a["unit_factor"]);
                if (b["unit_factor"] != "") bu = Convert.ToDouble(b["unit_factor"]);
                if (au == 1) au = 100000000000;
                if (bu == 1) bu = 100000000000;
                return (int)(bu - au);

            });
        }
    }
    public class ItemEditModel : PageFormModel
    {
        public List<InfoAttribute> AvailAttributes = new List<InfoAttribute>();
        public List<AttributeInItem> AttributesInItem = new List<AttributeInItem>();
        public string AttributeInitScript = "";
        public string UnitPriceRelated = "";
        public string AvaliPricePlan = "";
        public string SonItemList = "";
        public ItemEditModel(CMySbCommand cmd, string company_id = "", string oper_id = "") : base(Services.MenuId.infoItem)
        {
            this.cmd = cmd;
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            this.AllowSameName = false;
            this.DocType = "item";
            this.LogChange = true;
            this.PageTitle = "商品档案";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"item_id",new DataItem(){Title="编号",FldArea="divHead", Hidden=true } },
                {"item_name",new DataItem(){Title="名称",Width="385", Necessary=true,FldArea="divHead"}},
                {"item_alias",new DataItem(){Title="别名",FldArea="divHead"}},

                {"item_no",new DataItem(){Title="编号",FldArea="divHead"}},
                {"py_str",new DataItem(){Title="助记名",FldArea="divHead"}},
                {"location",new DataItem(){Title="存放位置",FldArea="divHead"}},
                {"cost_price_spec",new DataItem(){Title="预设成本价",FldArea="divHead",Hidden=true}},
                {"item_class",new DataItem(){Title="类别",Necessary=true,MaxRecords="1000", FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",TreePathFld="other_class",MumSelectable=true,
                   SqlForOptions=CommonTool.selectClasses} },
                {"item_brand",new DataItem(){Title="品牌",LabelFld="brand_name",FldArea="divHead",ButtonUsage="list",QueryOnChange = true,MaxRecords="500",
                    SqlForOptions = CommonTool.selectExistBrands}},
                {"item_order_index",new DataItem(){Title="顺序号",FldArea="divHead"}},
                {"valid_days",new DataItem(){Title="保质期",FldArea="divHead",Width="70"}},
                {"valid_day_type",new DataItem(){Title="",FldArea="divHead", Width="60",ButtonUsage="list", Source = "[{v:'d',l:'天'},{v:'m',l:'月'},{v:'y',l:'年'}]"}},
                {"approaching_days",new DataItem(){Title="临期节点",FldArea="divHead",Width="70"}},
                {"approaching_day_type",new DataItem(){Title="",FldArea="divHead",Width="60",Disabled=true,SaveToDB=false,Value="d",SqlFld="valid_day_type",Source="[{v:'d',l:'天'},{v:'m',l:'月'},{v:'y',l:'年'}]" } },
                {"item_spec",new DataItem(){Title="规格",FldArea="divHead"}},
                {"item_provenance",new DataItem(){Title="原产地",FldArea="divHead"}},
                {"status",new DataItem(){Title="状态",FldArea="divHead",LabelInDB=false,Value="1",Label="正常", ButtonUsage="list", Source = "[{v:1,l:'正常'},{v:0,l:'停用'}]"}},
                {"supplier_id",new DataItem(){Title="主供应商",LabelFld="sup_name",FldArea="divHead",ButtonUsage="list",QueryOnChange = true,MaxRecords="500",
                    SqlForOptions ="select supcust_id as v,sup_name as l from info_supcust where supcust_flag in ('S','CS') and status = 1"}},
                {"manufactor_id",new DataItem(){Title="生产商",LabelFld="manufactor_name",FldArea="divHead",ButtonUsage="list",QueryOnChange = true,MaxRecords="500",
                    SqlForOptions ="select manufactor_id as v,manufactor_name as l from info_manufactor where supplier_id=~CONDI_DATA_ITEM",CONDI_DATA_ITEM="supplier_id"}},
                //直接带出来的实现方法
                {"item_remark",new DataItem(){Title="备注",FldArea="divHead"}},
				{"itemUsed",new DataItem(){Title="已使用",FldArea="divHead",GetFromDb=false,SaveToDB=false,Hidden=true}},
                {"unitRows",new DataItem(){Title="单位行数",FldArea="divHead",GetFromDb=false,SaveToDB=false,Hidden=true}},
                {"mum_attributes",new DataItem(){FldArea="divHead",Hidden=true}},
                {"avail_attr_combine",new DataItem(){FldArea="divHead",Hidden=true}},
                {"son_options_id",new DataItem(){FldArea="divHead",Hidden=true}},
                {"son_mum_item",new DataItem(){FldArea="divHead",Hidden=true}},
                {"item_images",new DataItem(){ FldArea="divHead",Hidden=true}},
                {"item_videos",new DataItem(){ FldArea="divHead",Hidden=true}},
              //临时注释
                {"item_desc",new DataItem(){ FldArea="divHead",Hidden=true}},
                {"batch_level",new DataItem(){Title="产期/批次",LabelFld="l",FldArea="divHead",LabelInDB=false,Value="",Label="", ButtonUsage="list",DropDownHeight="80", Source = "[{v:1,l:'开启生产日期'},{v:2,l:'开启批次'}]"}},
                {"approve_status",new DataItem(){Title="审核状态",CtrlType="hidden",HideOnLoad=true,FldArea="divHead"}},

                //让前端拿到该商品的已添加价格方案
                {"price_plan_item",new DataItem(){ FldArea="divHead",Hidden=true,SqlFld="spp.price_plan_item",SaveToDB=false}},
                {"mall_status",new DataItem(){Title="商城状态",FldArea="divHead",LabelInDB=false,Value="1",Label="已上架", ButtonUsage="list", Source = "[{v:1,l:'已上架'},{v:0,l:'已下架'}]"}},
                {"mall_units",new DataItem(){Title="商城上架单位",FldArea="divHead", Checkboxes = true , ButtonUsage="list", Source = "[{v:'b',l:'大'},{v:'m',l:'中'},{v:'s',l:'小'}]"}},
                {"mall_units_show_price",new DataItem(){Title="商城价格展示",FldArea="divHead", Checkboxes = true , ButtonUsage="list", Source = "[{v:'b',l:'大'},{v:'m',l:'中'},{v:'s',l:'小'}]"}},
                {"allowChangeUnitFactor",new DataItem(){Title="允许改变已开单商品包装率",FldArea="divHead",GetFromDb=false,SaveToDB=false,Hidden=true}},


            };
            m_idFld = "item_id"; m_nameFld = "item_name";
            m_tableName = "info_item_prop";
            m_selectFromSQL = @"
from info_item_prop 
left join 
(
   select * from info_item_class where company_id =~COMPANY_ID
) c on info_item_prop.item_class=c.class_id 
left join info_item_brand on info_item_prop.company_id=info_item_brand.company_id and info_item_prop.item_brand=info_item_brand.brand_id 
left join info_supcust on info_item_prop.company_id=info_supcust.company_id and info_item_prop.supplier_id=info_supcust.supcust_id 
left join info_manufactor on info_item_prop.company_id=info_manufactor.company_id and info_item_prop.manufactor_id=info_manufactor.manufactor_id 
left join 
(
 SELECT  pi.company_id,
  pi.item_id, json_agg(jsonb_build_object(
             'price_plan_item', pi.*,
             'plan_name', pm.plan_name
           )) AS price_plan_item
FROM price_plan_item pi
LEFT JOIN price_plan_main pm ON pi.company_id = pm.company_id AND pi.plan_id = pm.plan_id
WHERE pi.company_id = ~COMPANY_ID AND item_id = ~ID
GROUP BY pi.company_id, pi.item_id


) spp on info_item_prop.company_id = spp.company_id and info_item_prop.item_id=spp.item_id
where info_item_prop.company_id=~COMPANY_ID and info_item_prop.item_id=~ID";

            Grids = new Dictionary<string, FormDataGrid>()
            {
                {"gridUnit" ,new ItemUnitGrid(){

                   MinRows=3,AutoAddRow=false,
                   Height=150,
                  JSFixColumnCellRender = @"
function(row, column, value) { 
     return '<div style=""height:100%;display:flex;justify-content:center;align-items:center;"">' + (value==0?'小':value==1?'大':value==2?'中':'') + '</div>';
}",
                  Columns = new Dictionary<string, DataItem>()
                   {
                       //{"unit_no",new DataItem(){title="单位",width="100",url="../api/ItemEdit/GetUnits"}},
                       {"unit_no",new DataItem(){Title="单位",Width="50",Necessary=true, SqlForOptions="select unit_no from info_avail_unit order by order_index",GetOptionsOnLoad=true,ButtonUsage="list"}},
                       {"unit_factor",new DataItem(){Title="包装率",Width="50",Necessary=true}},
                       {"unit_type",new DataItem(){Title="包装类型",Width="50",Hidden=true}},
                       {"wholesale_price",new DataItem(){Title="批发价",Width="60"}},
                       {"retail_price",new DataItem(){Title="零售价",Width="60"}},
                       {"buy_price",new DataItem(){Title="进价",Width="50"}},
                       {"profit_rate",new DataItem(){Title="利润率",Width="65",
                           SqlFld="case when wholesale_price>0 then CAST(ROUND(((wholesale_price - buy_price) / wholesale_price)*100,2) AS text)||'%' else '' end",SaveToDB=false, // 读取计算已有数据
                           JSCellBeginEdit = @"function(row, datafield, columntype, value) {return true; return false; }", // 禁止编辑
                       }},


                       {"cost_price_spec",new DataItem(){Title="预设成本",Width="67"}},
                       {"lowest_price",new DataItem(){Title="最低售价",Width="67"} },
                       {"contract_price",new DataItem(){Title="承包价",Width="60"}},

                       {"cost_price_avg",new DataItem(){Title="加权价",Width="60",SqlFld="case when unit_factor=1 then p.cost_price_avg else round((p.cost_price_avg*unit_factor)::numeric,2) end",SaveToDB=false,
                       JSCellBeginEdit =  @"
function(row, datafield, columntype, value) { 
    if(value=='' ||window.costPriceEmpty){ 
        window.costPriceEmpty=true
        return true
    }
    return false
}"
                       }},
                       {"cost_price_recent",new DataItem(){Title="最近平均进价",SqlFld="",SaveToDB=false}},
                       {"barcode",new DataItem(){Title="条码",Width="120"}},
                       {"weight",new DataItem(){Title="重量kg",Width="55"}},
                       {"volume",new DataItem(){Title="体积m³",Width="55"}},
                       {"mall_min_qty",new DataItem(){Title="商城起订量",Width="120"}},
                       {"mall_max_qty",new DataItem(){Title="商城每单限购量",Width="120"}},

                   },
                   TableName="info_item_multi_unit",
                   IdFld="item_id",
                   SelectFromSQL=@"from info_item_multi_unit left join (select item_id as item_id1,cost_price_avg,cost_price_recent from info_item_prop where company_id = ~COMPANY_ID) p on p.item_id1 = info_item_multi_unit.item_id where info_item_multi_unit.item_id='~ID' order by case unit_type when 's' then 0 when 'b' then 1 when 'u' then 2 end"
                }}

            };


        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {

            SQLQueue QQ = new SQLQueue(cmd);
            string sql = $"select rights->'delicacy'->'seeInPrice'->'value' see_cost_right,replace((rights->'delicacy'->'changeUnitfactorAfterNewSheet'->'value')::text,'\"','') allow_change_unit_factor from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
            QQ.Enqueue("right", sql);     
            sql = $"SELECT replace((setting->'recentPriceTime')::text,'\"','') recentPriceTime FROM company_setting where company_id = {company_id} ";
            QQ.Enqueue("recentPriceTime", sql);
            var item_id = DataItems["item_id"].Value;
            if (item_id != "")
            {
                var itemsID = $@"SELECT item_id FROM info_item_prop WHERE company_id = {company_id} and(item_id = {item_id} or son_mum_item = {item_id})";
                sql = $@"
select item_id from stock where item_id in ( {itemsID}) and company_id={company_id} and stock_qty <>0
union
select item_id from items_ordered_balance where item_id in ( {itemsID}) and company_id={company_id} 
union
select item_id from sheet_move_detail where item_id in ( {itemsID}) and company_id={company_id} 
union
select item_id from sheet_buy_detail where item_id in ( {itemsID}) and company_id={company_id} 
union
select item_id from sheet_sale_detail where item_id in ( {itemsID}) and company_id={company_id}
union
select item_id from sheet_inventory_detail where item_id in ( {itemsID}) and company_id={company_id} 
union
select item_id from sheet_invent_change_detail where item_id in ( {itemsID}) and company_id={company_id} 
union
select item_id from sheet_sale_order_detail where item_id in ( {itemsID}) and company_id={company_id}
union
select item_id from sheet_buy_order_detail where item_id in ( {itemsID}) and company_id={company_id} 
limit 1 
";
                QQ.Enqueue("itemUsed", sql);

            }
            

            sql = $"select * from info_attribute where company_id ={company_id} order by order_index, attr_id";
            QQ.Enqueue("attrs", sql);

            sql = $"select plan_id,plan_name from price_plan_main where company_id={company_id}";
            QQ.Enqueue("price_plan", sql);
            sql = $"select opt_id,opt_name,o.attr_id from info_attr_opt o left join info_attribute a on a.company_id ={company_id} and o.attr_id=a.attr_id where o.company_id={company_id} order by o.order_index, o.opt_id;";
            QQ.Enqueue("attrOptions", sql);
            sql = $"select setting from company_setting where company_id = {company_id};";
            QQ.Enqueue("batchType", sql);
            sql = $@" select iip.company_id,iip.item_id,iip.item_name,iip.son_options_id,iip.status,json_agg( json_build_object('barcode', iimu.barcode, 'wholesale_price', iimu.wholesale_price,'unit_type', iimu.unit_type)) AS other_info
from info_item_prop iip
left join info_item_multi_unit iimu on iimu.company_id = {company_id} and iimu.item_id = iip.item_id
where iip.company_id = {company_id} and  iip.son_mum_item = {item_id}
GROUP BY iip.company_id, iip.item_id, iip.item_name, iip.son_options_id, iip.status;
";
            if (!String.IsNullOrEmpty(item_id))
            {
                QQ.Enqueue("sonItemList", sql);
            }



            CMySbDataReader dr = await QQ.ExecuteReaderAsync();

            var unitColumns = Grids["gridUnit"].Columns;
            while (QQ.Count > 0)
            {
                string tbl = QQ.Dequeue();
                if (tbl == "right")
                {
                    dynamic right = CDbDealer.Get1RecordFromDr(dr, false);
                    unitColumns["buy_price"].Hidden = true;
                    unitColumns["cost_price_avg"].Hidden = true;
                    unitColumns["cost_price_spec"].Hidden = true;
                    unitColumns["profit_rate"].Hidden = true;
                    unitColumns["cost_price_recent"].Hidden = true;

                    if (right != null && right.see_cost_right == "true")
                    {
                        unitColumns["buy_price"].Hidden = false;
                        unitColumns["cost_price_avg"].Hidden = false;
                        unitColumns["cost_price_spec"].Hidden = false;
                        unitColumns["profit_rate"].Hidden = false;
                        unitColumns["cost_price_recent"].Hidden = false;
                    }
                    if (right.allow_change_unit_factor == "allow") { DataItems["allowChangeUnitFactor"].Value = "true"; }
                    else
                    {
                        DataItems["allowChangeUnitFactor"].Value = "false";
                    }
                }

                else if (tbl == "recentPriceTime")
                {
                    dynamic recentPriceTime = CDbDealer.Get1RecordFromDr(dr, false);
                    string times = "";
                    if (recentPriceTime != null) times = recentPriceTime.recentpricetime;


                    if (times.IsValid())
                    {
                        unitColumns["cost_price_recent"].SqlFld = @$"case when p.cost_price_recent is not null then  case when unit_factor=1 then (p.cost_price_recent->'avg{times}')::numeric else round(((p.cost_price_recent->'avg{times}')::numeric *unit_factor)::numeric,2) end   else null end";
                    }
                    else
                    {
                        unitColumns["cost_price_recent"].GetFromDb = false;
                        unitColumns["cost_price_recent"].Hidden = unitColumns["cost_price_recent"].HideOnLoad = true;

                    }
                    unitColumns["cost_price_recent"].JSCellBeginEdit = @"function(row, datafield, columntype, value) {
                                                 return false;
                                            }";
                }
                else if (tbl == "itemUsed")
                {
                    if (dr.Read())
                    {
                        var temp_function= @"function(row, datafield, columntype, value) {          
                                   if($('#itemUsed').val()=='false'||$('#allowChangeUnitFactor').val()=='true'||($('#itemUsed').val()=='true' && row>=$('#unitRows').val()))
                                                 return true;
                                                 else return false;
                                            }";
                        unitColumns["unit_factor"].JSCellBeginEdit = temp_function;
                        unitColumns["unit_no"].JSCellBeginEdit = temp_function;//单位和包装率的逻辑统一，如果允许修改，则都允许修改，反之亦然。
                        DataItems["itemUsed"].Value = "true";

                    }
                }
                else if (tbl == "attrs")
                {
                    AvailAttributes = CDbDealer.GetRecordsFromDr<InfoAttribute>(dr, false);
                    if (AvailAttributes != null && AvailAttributes.Count > 0)
                    {
                        AttributeInitScript += $"window.availAttributes={JsonConvert.SerializeObject(AvailAttributes)};";

                    }
                    else
                    {
                        AttributeInitScript += "window.availAttributes=[];";
                    }
                    string mum_attributes = DataItems["mum_attributes"].Value;
                    string avail_attr_combine = DataItems["avail_attr_combine"].Value;

                    if (mum_attributes.IsValid())
                    {
                        AttributeInitScript += $"window.mumAttributes={mum_attributes};";
                        AttributesInItem = JsonConvert.DeserializeObject<List<AttributeInItem>>(mum_attributes);
                    }
                    else AttributeInitScript += $"window.mumAttributes=[];";

                    if (avail_attr_combine.IsValid())
                    {
                        AttributeInitScript += $"window.availAttrCombine={avail_attr_combine};";
                    }
                    else
                    {
                        AttributeInitScript += $"window.availAttrCombine=null;";
                    }

                }
                else if (tbl == "attrOptions")
                {
                    var options = CDbDealer.GetRecordsFromDr(dr, false);
                    // var attrOptions = new Dictionary<string, dynamic>();
                    var attrOptions = options.GroupBy(opt => ((dynamic)opt).attr_id).ToDictionary(g => (string)g.Key, g => g.ToList());

                    string s = JsonConvert.SerializeObject(attrOptions);
                    AttributeInitScript += $"window.attrOptions={s};";
                }
                else if (tbl == "sonItemList")
                {
                    var sonItemList = CDbDealer.GetRecordsFromDr(dr, false);
                    if (sonItemList != null && sonItemList.Count > 0)
                    {
                        string s = JsonConvert.SerializeObject(sonItemList);
                        SonItemList += $"window.SonItemList={s};";
                    }
                    else
                    {
                        SonItemList += $"window.SonItemList=[];";
                    }
                }
                else if (tbl == "price_plan")
                {
                    var availPricePlan = CDbDealer.GetRecordsFromDr(dr, false);
                    string s = JsonConvert.SerializeObject(availPricePlan);
                    //前端页面初始化时，让前端通过model拿到该公司的所有价格方案
                    AvaliPricePlan += $"window.availPricePlan={s}";
                }
                else if (tbl == "batchType")
                {
                    dynamic settingJson = CDbDealer.Get1RecordFromDr(dr, false);
                    if (settingJson != null)
                    {
                        dynamic setting = JsonConvert.DeserializeObject<dynamic>(settingJson.setting);
                        string batch_type = "";
                        if (setting != null && setting.batchType != null)
                        {
                            batch_type = setting.batchType;
                        }
                        if (batch_type != "1" && batch_type != "2" && DataItems["batch_level"].Value.IsInvalid())
                        {
                            DataItems["batch_level"].Hidden = true;
                        }
                        else
                        {
                            DataItems["batch_level"].Hidden = false;
                        }
                    }
                    else
                    {
                        DataItems["batch_level"].Hidden = true;
                    }

                }
            }
            QQ.Clear();

            if (JsonCompanySetting.IsValid())
            {
                dynamic setting = JsonConvert.DeserializeObject(JsonCompanySetting);
                if (!DataItems["valid_day_type"].Value.IsValid())
                {

                    if (setting.validDayType != null)
                    {
                        DataItems["approaching_day_type"].Value = DataItems["valid_day_type"].Value = setting.validDayType;
                        DataItems["approaching_day_type"].Label = DataItems["valid_day_type"].Label = setting.validDayType == "m" ? "月" : setting.validDayType == "y" ? "年" : "天";

                    }
                }
                if (setting.unitPriceRelated != null)
                {
                    this.UnitPriceRelated = ((string)setting.unitPriceRelated).ToLower();
                }
            }
            if (DataItems["mall_units"].Value != null)
            {
                var arr_mall_units = DataItems["mall_units"].Value.Split(',');
                string mall_units_name = "";
                if (arr_mall_units.Length > 0)
                {
                    foreach (var m in arr_mall_units)
                    {
                        if ("b".Equals(m)) mall_units_name += "大,";
                        if ("m".Equals(m)) mall_units_name += "中,";
                        if ("s".Equals(m)) mall_units_name += "小,";
                    }
                }
                DataItems["mall_units"].Label = mall_units_name;
            }
            if (!string.IsNullOrEmpty(DataItems["mall_units_show_price"].Value))
            {
                var label = DataItems["mall_units_show_price"].Value.Replace("b", "大").Replace("m", "中").Replace("s", "小");
                DataItems["mall_units_show_price"].Label = label;
            }
        }


        public async Task OnGet()
        {
            async Task adjust(CMySbCommand cmd)
            {
                string item_class = DataItems["item_class"].Value;
                string item_brand = DataItems["item_brand"].Value;
                if (item_brand == "")
                {
                    dynamic brand = await CDbDealer.Get1RecordFromSQLAsync($"select info_item_brand.brand_id,brand_name from info_item_class left join info_item_brand on info_item_class.brand_id=info_item_brand.brand_id where class_id={item_class} and info_item_class.company_id = {company_id} ", cmd);
                    if (brand != null && brand.brand_id != "")
                    {
                        DataItems["item_brand"].Value = brand.brand_id;
                        DataItems["item_brand"].Label = brand.brand_name;
                    }
                }
            }
            await InitGet(cmd, adjust);
            //  string item_id = DataItems["item_id"].Value;
            //  if (!item_id.IsValid()) { item_id = "-1"; }





        }
        /*
          public override Dictionary<string, dynamic> GetGridVariances(Dictionary<string, List<ExpandoObject>> newGrids, Dictionary<string, List<ExpandoObject>> oldGrids)
        {
            Dictionary<string, dynamic> variances = new Dictionary<string, dynamic>();
            Dictionary<string, Dictionary<string,string>> unitsDic= new Dictionary<string,  Dictionary<string,string>>();
            Dictionary<string, Dictionary<string,string>> newUnitsDic= new Dictionary<string,  Dictionary<string,string>>();
            unitsDic.Add("s", null);
            unitsDic.Add("m", null);
            unitsDic.Add("b",null);
            newUnitsDic.Add("s", null);
            newUnitsDic.Add("m", null);
            newUnitsDic.Add("b",null);
            foreach (dynamic oldUnitItem in oldGrids)
            {
                Dictionary<string,string> oldUnitItemDic=JsonConvert.DeserializeObject<Dictionary<string,string>>(JsonConvert.SerializeObject(oldUnitItem));
                if (oldUnitItem.unit_type == "s") unitsDic["s"] = oldUnitItemDic;
                if (oldUnitItem.unit_type == "m") unitsDic["m"] = oldUnitItemDic;
                if (oldUnitItem.unit_type == "b") unitsDic["b"] = oldUnitItemDic;
            }
            foreach (dynamic newUnitItem in newGrids)
            {
                Dictionary<string,string> newUnitItemDic=JsonConvert.DeserializeObject<Dictionary<string,string>>(JsonConvert.SerializeObject(newUnitItem));
                if (newUnitItem.unit_type == "s") newUnitsDic["s"] = newUnitItemDic;
                if (newUnitItem.unit_type == "m") newUnitsDic["m"] = newUnitItemDic;
                if (newUnitItem.unit_type == "b") newUnitsDic["b"] = newUnitItemDic;
            }
            foreach (KeyValuePair<string,Dictionary<string,string> > kv in unitsDic)
            {
                string unitKey = kv.Key;
                if (kv.Value != null)
                {
                    foreach (KeyValuePair<string,string> kv2 in kv.Value)
                    {
                        string propertyKey = kv2.Key;
                        string oldValue = kv2.Value;
                        string newValue = newUnitsDic[unitKey] == null ? "" : newUnitsDic[unitKey][propertyKey];
                        if (oldValue != newValue)
                        {
                            Dictionary<string,string> variance = new Dictionary<string,string>();
                            variance.Add("newValue", newValue);
                            variance.Add("oldValue", oldValue);
                            variances.Add(unitKey+"_"+propertyKey, variance);
                        }
                    }
                }
                else
                {
                    if (newUnitsDic[unitKey] != null)
                    {
                        foreach (KeyValuePair<string,string> kv2 in newUnitsDic[unitKey])
                        {
                            string propertyKey = kv2.Key;
                            string oldValue = "";
                            string newValue =newUnitsDic[unitKey][propertyKey];
                            if (oldValue != newValue)
                            {
                                Dictionary<String, String> variance = new Dictionary<String, String>();
                                variance.Add("newValue", newValue);
                                variance.Add("oldValue", oldValue);
                                variances.Add(unitKey+"_"+propertyKey, variance);
                            }
                        }
                    }
                }
                        
            }
            return variances;
        }
        */
        public class InfoAttribute
        {
            public string attr_id { get; set; } = "";
            public string attr_name { get; set; } = "";
            public bool spec_opt_in_item { get; set; } = false;
            public bool use_opt_group { get; set; } = false;
            public bool distinct_stock { get; set; } = false;
            public bool distinct_stock_editable { get; set; } = false;
        }

        public class AttributeInItem
        {
            public string attrID = "", attrName = "", groupID = "";
            public bool distinctStock = false, specOptInItem = false; //remberSonItemPrice = false;
            public class AttributeOption
            {
                public string optID { get; set; } = "";
                public string optName { get; set; } = "";
                public string bBarcode { get; set; } = "";
                public string mBarcode { get; set; } = "";
                public string sBarcode { get; set; } = "";
                public string bPrice { get; set; } = "";
                public string sPrice { get; set; } = "";
            }
            public List<AttributeOption> options = new List<AttributeOption>();
        }
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class ItemEditController : BaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public ItemEditController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            this.cmd = cmd; _httpClientFactory = httpClientFactory;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string condiDataItem, string availValues)
        {
            ItemEditModel model = new ItemEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues, condiDataItem);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey, string gridID, string colName, string flds, string value, string availValues)
        {
            ItemEditModel model = new ItemEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        static string ToFieldValue(dynamic d)
        {
            string fld = (string)d;
            return String.IsNullOrEmpty(fld) ? "null" : $"'{fld}'";
        }
        public static async Task<string> GetAttributeSQL(string companyID, dynamic data, CMySbCommand cmd)
        {
            var attrs = data.mum_attributes;
            if (attrs == null)
            {
                return "";
            }

            string sqlUpdateSonUnits = "";

            string item_id = data.item_id;

            string sAttrs = "";

            if (attrs.GetType() == typeof(JValue))
            {
                sAttrs = attrs;
            }
            else
            {
                sAttrs = JsonConvert.SerializeObject(attrs);
            }
            sAttrs = sAttrs.Replace("\\t", "");
            List<AttributeInItem> lst = JsonConvert.DeserializeObject<List<AttributeInItem>>(sAttrs);
            if (lst == null)
            {
                return "";
            }

            string rows = "";

            foreach (var attr in lst)
            {
                if (attr.options != null && attr.options.Count > 0)
                {
                    foreach (var opt in attr.options)
                    {
                        if (opt.optID == "")
                        {
                            if (rows != "") rows += ",";
                            rows += $"({companyID},{attr.attrID},'{opt.optName}')";

                        }
                        else if (item_id != "")
                        {
                            string getUnitSql(string unitType, string barcode)
                            {
                                string s = @$"
update info_item_multi_unit mu set barcode='{barcode}' 
from info_item_prop ip
where mu.company_id=ip.company_id and mu.item_id=ip.item_id 
and mu.company_id={companyID} and ip.son_mum_item={item_id} and ip.son_options_id::text ='{opt.optID}' and unit_type='{unitType}';";
                                return s;
                            }

                            if (opt.sBarcode.IsValid()) sqlUpdateSonUnits += getUnitSql("s", opt.sBarcode);
                            if (opt.mBarcode.IsValid()) sqlUpdateSonUnits += getUnitSql("m", opt.mBarcode);
                            if (opt.bBarcode.IsValid()) sqlUpdateSonUnits += getUnitSql("b", opt.bBarcode);

                        }
                    }
                }
            }

            if (rows != "")
            {
                string s = $"insert into info_attr_opt(company_id,attr_id,opt_name) values {rows} on conflict(company_id,attr_id,opt_name) do update set company_id={companyID} returning attr_id,opt_id,opt_name;";
                var records = await CDbDealer.GetRecordsFromSQLAsync(s, cmd);
                foreach (dynamic rec in records)
                {
                    foreach (var attr in lst)
                    {
                        if (attr.options != null && attr.options.Count > 0)
                        {
                            foreach (var opt in attr.options)
                            {
                                if (opt.optName == rec.opt_name)
                                {
                                    opt.optID = rec.opt_id;
                                }
                            }
                        }
                    }
                }
            }

            string sql = "";
            if (item_id != "")
            {
                /*// 获取商品旧名称
                string queryOldItemName = $"SELECT item_name FROM info_item_prop WHERE company_id = {companyID} AND item_id = {item_id}";
                Console.WriteLine("quertON: "+queryOldItemName);
                cmd.CommandText = queryOldItemName;
                string old_item_name = (string)await cmd.ExecuteScalarAsync();
                // 更新子商品名称
                string querySubItems = $@"SELECT item_id, item_name FROM info_item_prop WHERE company_id={companyID} AND son_mum_item = {item_id}";
                List<ExpandoObject> subItems = await CDbDealer.GetRecordsFromSQLAsync(querySubItems, cmd);

                foreach (dynamic subItem in subItems)
                {
                    string subItemName = subItem.item_name;
                    Console.WriteLine("subItemName: " + subItemName);
                    // 替换子商品名称中的旧规格部分，例如将20g换为25g
                    string updatedSubItemName = subItemName.Replace(old_item_name, data.item_name);
                    // 更新子商品的名称
                    string updateSubItemNameSql = $"UPDATE info_item_prop SET item_name = '{updatedSubItemName}' WHERE son_mum_item = {item_id} AND item_id={subItem.item_id}";
                    cmd.CommandText = updateSubItemNameSql;
                    await cmd.ExecuteNonQueryAsync();
                }*/
                string sqlUpdateSonItems = $@"
update info_item_prop ii set item_name='{data.item_name}'|| '(' || t.opt_name ||  ')',item_brand={ToFieldValue(data.item_brand)},item_class={ToFieldValue(data.item_class)},other_class={ToFieldValue(data.other_class)},item_spec={ToFieldValue(data.item_spec)},batch_level = {ToFieldValue(data.batch_level)}
from
(
select * from
(
    select company_id, item_id, item_name, son_mum_item, son_options_id 
    from info_item_prop 
    where company_id = {companyID} and son_mum_item ={item_id}
) ip
left join
(
    select * from info_attr_opt  where company_id = {companyID} 
) io
on ip.company_id = io.company_id and ip.son_options_id::NUMERIC = io.opt_id
) t
where ii.item_id = t.item_id and ii.company_id = {companyID} and ii.son_mum_item = {item_id};";
                

                sql = sqlUpdateSonItems + sqlUpdateSonUnits;
            }

            if (lst.Count > 0)
                data.mum_attributes = JsonConvert.SerializeObject(lst);
            else data.mum_attributes = "";

            return sql;

        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic data)
        {

            string error = "";
            ItemEditModel model = new ItemEditModel(cmd);
            string item_class = data["item_class"];
            string other_class = data["other_class"];
            string item_brand = data["item_brand"];
            // string son_mum_item = data["item_id"];
            string item_name = data["item_name"];
            string item_no = data["item_no"];
            string item_spec = data["item_spec"];
            string status = data["status"];
            string son_mum_item = data["son_mum_item"];
            string data_son_options_id = data["son_options_id"];
            string valid_days = data["valid_days"];
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            cmd.ActiveDatabase = "";

            #region 商品档案log
            dynamic jsonRights = await CDbDealer.Get1RecordFromSQLAsync($@"SELECT rights FROM info_operator o 
    left join info_role r on o.company_id = r.company_id and o.role_id = r.role_id
where o.company_id = {companyID} and o.oper_id = {operID}", cmd);
            data.approve_brief = "";
            data.receiver_id = "";
            data.flow_id = "";
            data.msg_id = "";
            string approveFlag = "";
            dynamic rights = Newtonsoft.Json.JsonConvert.DeserializeObject(jsonRights.rights);
            if (rights != null)
            {
                if (rights.info.infoItem.approve == "false")
                {
                    if (data.approve_status == "wait approve")
                    {
                        return new JsonResult(new { result = "ERROR", msg = "该档案上次编辑未审核，请先审核再编辑" });
                    }
                    model.DataItems["approve_status"].Value = "wait approve";
                    data.approve_status = "wait approve";
                    if (data.item_id == "")
                    {
                        if (rights.info.infoItem.create == "false")
                        {

                            return new JsonResult(new { result = "ERROR", msg = "暂无新建权限" });
                        }
                        approveFlag = "CREATE";
                    }
                    else
                    {
                        if (rights.info.infoItem.edit == "false")
                        {

                            return new JsonResult(new { result = "ERROR", msg = "暂无编辑权限" });
                        }
                        approveFlag = "EDIT";
                    }

                }
                else
                {
                    if (data.approve_status == "wait approve")
                    {
                        string docSql = $@"SELECT flow_id,oper_action FROM document_change_log 
                    where company_id = {companyID} and obj_name ='商品档案' and obj_id = {data.item_id}";
                        dynamic docInfo = await CDbDealer.Get1RecordFromSQLAsync(docSql, cmd);
                        if (docInfo != null)
                        {
                            if (docInfo.oper_action == "CREATE") approveFlag = "APPROVED_FROM_CREATE";
                            else approveFlag = "APPROVED_FROM_EDIT";
                            data.flow_id = docInfo.flow_id;
                        }
                        // 2024.09.13 - #3952
                        // 这种地方有必要报错吗？
                        //else
                        //{
                        //     return new JsonResult(new { result = "ERROR", msg = "商品档案历史记录查询失败" });
                        //}
                    }
                    else
                    {
                        if (data.item_id == "")
                        {
                            if (rights.info.infoItem.create == "false")
                            {

                                return new JsonResult(new { result = "ERROR", msg = "暂无新建权限" });
                            }
                            approveFlag = "CREATE_AND_APPROVED";
                        }
                        else
                        {
                            if (rights.info.infoItem.edit == "false")
                            {

                                return new JsonResult(new { result = "ERROR", msg = "暂无编辑权限" });
                            }
                            approveFlag = "EDIT_AND_APPROVED";
                        }
                    }
                    model.DataItems["approve_status"].Value = "";
                    data.approve_status = "";


                }
                data.approve_flag = approveFlag;
            }
            else
            {

                return new JsonResult(new { result = "ERROR", msg = "获取权限失败" });
            }
            #endregion
            # region 检查item_name是否含特殊字符:英文逗号
            if (item_name.IndexOf(",") > -1)
            {
                return new JsonResult(new { result = "Error", msg = "商品名请不要包含英文逗号" });
            }
            /*if (item_name.IndexOf('"') > -1)
            {
                error = "商品名请不要包含英文单引号";
                return new JsonResult(new { result = "Error", msg = error });
            }*/
            if (item_name.IndexOf('\'') > -1)
            {
                return Json(new { result = "Error", msg = "商品名请不要包含英文单引号" });
            }
            #endregion
            #region 检查保质期是否有时间限制
            if (valid_days!=""&&!valid_days.IsNumeric())
            {
                return Json(new { result = "Error", msg = "保质期请填写数字" });
            }
            #endregion
            #region 检查条码是否是否含特殊字符:单引号
            dynamic grid_units = JArray.Parse(data["gridUnit"].ToString());
            foreach (dynamic unit in grid_units)
            {
                if (unit["barcode"].ToString().IndexOf("\'") > -1)
                {
                    return Json(new { result = "Error", msg = "商品条码请不要包含英文单引号" });
                }
                // 检查商城起订量是否为正整数或空值
                string mallMinQty = unit["mall_min_qty"]?.ToString();
                if (!string.IsNullOrEmpty(mallMinQty) && !System.Text.RegularExpressions.Regex.IsMatch(mallMinQty, @"^[1-9]\d*$"))
                {
                    return Json(new { result = "Error", msg = "商城起订量应该为正整数或为空值" });
                }
                string mallMaxQty = unit["mall_max_qty"]?.ToString();
                if (!string.IsNullOrEmpty(mallMaxQty) && !System.Text.RegularExpressions.Regex.IsMatch(mallMaxQty, @"^[1-9]\d*$"))
                {
                    return Json(new { result = "Error", msg = "商城每单限购量应该为正整数或为空值" });
                }
            }
            #endregion

            object ov = null;
            string item_id = data.item_id;
            /*
            #region 停用时检查是否有库存
            if (item_id!="" && status == "0")
            {
                cmd.CommandText = @$"select s.item_id from stock s left join info_item_prop ip on s.item_id=ip.item_id and ip.company_id={companyID} where s.company_id={companyID} and (s.item_id={item_id} or ip.son_mum_item={item_id}) and abs(stock_qty)>0.01;";
                ov = await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value)
                {
                    return Json(new { result = "Error", msg = "该商品还有库存，请盘0后再停用" });
                }
            }
            

            #endregion
            */
            #region 检查批次库存
            string batch_level = data["batch_level"];

            if (batch_level == "" && item_id != "")
            {
                List<ExpandoObject> stockByItem = null;
                string stockByItemSql = $@"select * from stock where company_id = {companyID} and item_id ={item_id} and batch_id <>0 and stock_qty <>0
                                           union
                                           select * from stock s where company_id = {companyID} 
and item_id in (select item_id from info_item_prop where company_id = {companyID} and son_mum_item = {item_id})
and batch_id <>0 and stock_qty <>0";
                stockByItem = await CDbDealer.GetRecordsFromSQLAsync(stockByItemSql, cmd);
                if (stockByItem.Count != 0)
                {
                    return new JsonResult(new { result = "ERROR", msg = "修改失败，存在严格产期/批次库存，建议使用盘点单先清除" });
                }
                else
                {
                    //清除stock里的batch_id <> 0 
                    cmd.CommandText = $@"delete from stock where company_id={companyID} and item_id={item_id} and  batch_id <> 0;
delete from stock where company_id={companyID} and item_id in(select item_id from info_item_prop where company_id = {companyID} and son_mum_item = {item_id}) and  batch_id <> 0;";
                    await cmd.ExecuteNonQueryAsync();
                }
            }
            else if (batch_level == "1" && item_id != "")
            {
                List<ExpandoObject> stockByItem = null;
                //string stockByItemSql = $@"select stock.*,batch_no,produce_date from stock 
                //                         left join info_item_batch itb on itb.batch_id = stock.batch_id and itb.company_id = stock.batch_id
                //                         where stock.company_id = {companyID} and item_id ={item_id} and stock.batch_id <> 0 and batch_no <> '' and stock_qty <>0";
                string stockByItemSql = $@"select stock.*,batch_no,produce_date from stock 
                                         left join info_item_batch itb on itb.batch_id = stock.batch_id and itb.company_id = stock.company_id
                                         where stock.company_id = {companyID} and item_id ={item_id} and stock.batch_id <> 0 and batch_no <> '' and stock_qty <>0
                                         union
select stock.*,batch_no,produce_date from stock 
left join info_item_batch itb on itb.batch_id = stock.batch_id and itb.company_id = stock.company_id
where stock.company_id = {companyID} and item_id in (select item_id from info_item_prop where company_id = {companyID} and son_mum_item = {item_id}) and stock.batch_id <> 0 and batch_no <> '' and stock_qty <>0
";
                stockByItem = await CDbDealer.GetRecordsFromSQLAsync(stockByItemSql, cmd);
                if (stockByItem.Count != 0)
                {

                    return new JsonResult(new { result = "ERROR", msg = "修改失败，存在严格产期/批次库存，建议使用盘点单先清除" });
                }
                else
                {
                    //清除stock里的batch_no不为空的
                    cmd.CommandText = $@"delete from stock where company_id={companyID} and item_id={item_id} and  batch_id <> 0 
                                        and batch_id in (select batch_id from info_item_batch where  batch_no <> '' and company_id = {companyID});
delete from stock where company_id={companyID} and item_id in(select item_id from info_item_prop where company_id = {companyID} and son_mum_item = {item_id}) and  batch_id <> 0 
and batch_id in (select batch_id from info_item_batch where  batch_no <> '' and company_id = {companyID})";
                    await cmd.ExecuteNonQueryAsync();
                }
            }
            #endregion


            bool changeClass = false;
            dynamic rs = await CDbDealer.Get1RecordFromSQLAsync($"select class_id from info_item_class where company_id ={companyID} and class_id = {item_class} and class_name = '全部' ", cmd);
            if (rs != null)
            {
                error = "请勿将商品类别设置为'全部'";
                return new JsonResult(new { result = "Error", msg = error });
            }
            cmd.CommandText = $@"
SELECT string_agg ( class_name :: TEXT, '/' ) AS class_name 
FROM
(
	SELECT class_name 
	FROM
	(
	    WITH split_table AS 
        (
            SELECT regexp_split_to_table('{other_class}', '/') AS class_id
        )
        SELECT
            row_number() OVER () AS row_num,
            class_id
        FROM
            split_table
        WHERE
            class_id <> ''
	) t 
	LEFT JOIN info_item_class ic ON ic.company_id = {companyID} AND t.class_id::numeric = ic.class_id 
	WHERE ic.mother_id<>0
) t";
            string other_class_name = "";
            ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value) other_class_name = (string)ov;
            model.DataItems.Add("other_class_name", new DataItem { });
            data.other_class_name = other_class_name;



            dynamic oldClass = null;
            if (!((string)data.item_id).IsNullOrWhiteSpace())
            {
                oldClass = await CDbDealer.Get1RecordFromSQLAsync($"select item_class from info_item_prop where company_id = {companyID} and item_id = {data.item_id}", cmd);
            }




            string images = data.item_images;
            
            string videos = data.item_videos;
            string item_desc = data.item_desc;
            

            if (!string.IsNullOrEmpty(images))
            {
                HuaWeiObsController.HuaWeiObs.ItemImages ii = JsonConvert.DeserializeObject<HuaWeiObsController.HuaWeiObs.ItemImages>(images);
                string dir = $"item-images/company_{companyID}/";
                var id = data.item_id == "" ? $"newIn{DateTime.Now:yyyy-MM-dd_HH-mm-ss-ffff}" : data.item_id;
                string ite = $"item_{id}_";
                ii.path = dir + ite;
                images = await HuaWeiObsController.HuaWeiObs.WebItemImageSave(_httpClientFactory, ii);
                data.item_images = images;
            }
            if (!string.IsNullOrEmpty(videos))
            {
                // 解析 JSON 为 JObject
                var jsonObject = JObject.Parse(videos);
                //string[] videos_delete = jsonObject["videos_delete"].ToObject<string[]>();
                // 移除 'videos_delete' 字段
                jsonObject.Remove("videos_delete");
                HuaWeiObsController.HuaWeiObs.ItemVideos iv = jsonObject.ToObject<HuaWeiObsController.HuaWeiObs.ItemVideos>();
                // 正则表达式：匹配 "path" 值中从开头到 "item-videos" 的部分
                string pattern = @".*?(?=item-videos)";

                // 递归方法来遍历并更新 JSON 中的 `path` 字段
                void CleanPath(JToken token)
                {
                    if (token.Type == JTokenType.Object)
                    {
                        foreach (var property in (JObject)token)
                        {
                            // 找到 `path` 字段并进行替换
                            if (property.Key == "path" && property.Value.Type == JTokenType.String)
                            {
                                string pathValue = property.Value.ToString();
                                string cleanedPath = Regex.Replace(pathValue, pattern, "");
                                ((JObject)token)[property.Key] = cleanedPath;
                            }
                            else
                            {
                                // 递归处理嵌套对象
                                CleanPath(property.Value);
                            }
                        }
                    }
                    else if (token.Type == JTokenType.Array)
                    {
                        foreach (var item in token)
                        {
                            // 递归处理数组中的每个对象
                            CleanPath(item);
                        }
                    }
                }

                // 执行 `path` 字段的清理操作
                CleanPath(jsonObject);

                // 输出更新后的 JSON
                string updatedvideos = jsonObject.ToString(Newtonsoft.Json.Formatting.None);
                // 检查是否有至少一个 Path 属性不为空
                bool HasPath(ItemVideos itemVideos)
                {
                    return !string.IsNullOrEmpty(itemVideos.main?.path) ||
                           !string.IsNullOrEmpty(itemVideos.tiny?.path) ||
                           itemVideos.other?.Any(v => !string.IsNullOrEmpty(v.path)) == true;
                }
                if (HasPath(iv))
                {
                    data.item_videos = updatedvideos;
                }
                else
                {
                    data.item_videos = null;
                }
            }
           

            //return await model.SaveTable(cmd, data);
            // var attrCount = data.mum_attributes.Count;
            // if (attrCount>0)
            // {
            //     string sqlUpdateSonItems = await GetAttributeSQL(companyID, data, cmd);
            //     if (sqlUpdateSonItems != "")
            //     {
            //         cmd.CommandText = sqlUpdateSonItems;
            //         await cmd.ExecuteNonQueryAsync();
            //     }
            //     
            // }
            CMySbTransaction tran = cmd.Connection.BeginTransaction();

            var sign = await model.SaveTable(cmd, data, tran);
            var result = ((dynamic)sign.Value).result;
            var msg = ((dynamic)sign.Value).msg;
            item_id = model.DataItems["item_id"].Value;

            if (result != "OK")
            {
                tran.Rollback();
                return new JsonResult(new { result, msg });
            }
            /*
            if (item_id.IsInvalid())
            {
                string selectItemName = $"SELECT COUNT(*) rn FROM info_item_prop where company_id = {companyID} and item_name = '{item_name}' ";
                dynamic selectItemNameResult = await CDbDealer.Get1RecordFromSQLAsync(selectItemName, cmd);
                if (selectItemNameResult != null)
                {
                    Console.WriteLine(selectItemNameResult.rn);
                    int n = Convert.ToInt32(selectItemNameResult.rn);
                    if (n > 0)
                    {
                        return new JsonResult(new { result = "ERROR", msg = "商品名称已存在" });
                    }
                }
            }*/

         

            // 对于多属性进行处理
            try
            {
                if (string.IsNullOrEmpty(son_mum_item)) //不是子商品
                {

                    if (!string.IsNullOrEmpty(item_id) && data.attrInfo!=null)//有item_id说明是保存的，新建商品的不用管
                    {
                        dynamic rec =await CDbDealer.Get1RecordFromSQLAsync($"select son_mum_item from info_item_prop where company_id={companyID} and item_id ={item_id}", cmd);
                        if(rec != null &rec.son_mum_item!="")
                        {
							tran.Rollback();
                            msg = "该商品本身就是属性子商品,不能再创建子商品";
							return new JsonResult(new { result, msg });
						}
                        //更新子商品的item_class和other_class
                        string up_item_class = item_class == "" ? "null" : item_class; //item_class虽然是必填，但是这里防一手空值
                        string updateBatchLevel = "";
                        if (!string.IsNullOrEmpty(batch_level))
                        {
                            updateBatchLevel = $@",batch_level = '{batch_level}'";
                        }

                        string exsql = $"update info_item_prop set item_class = {up_item_class},other_class = '{other_class}' {updateBatchLevel} where company_id={companyID} and son_mum_item = {item_id}";
                        cmd.CommandText = exsql;
                        await cmd.ExecuteNonQueryAsync();
                    }

                    //更新子商品名称

                    // 使用正则表达式提取括号中的规格（如 '(芒果)'）
                    string ExtractSpec(string itemName)
                    {
                        var match = Regex.Match(itemName, @"\(([^)]*)\)");
                        return match.Success ? match.Value : "";
                    }

                    // 替换主名称，保留括号中的规格
                    string ReplaceMainName(string itemName, string newMainName)
                    {                       
                        // 提取括号中的规格
                        string spec = ExtractSpec(itemName);
                        if (string.IsNullOrEmpty(spec))
                        {
                            return $@"{itemName}";
                        }

                        // 移除规格部分，保留主名称
                        string mainNameWithoutSpec = itemName.Replace(spec, "").Trim();
                        // 替换主名称
                        string updatedMainName = mainNameWithoutSpec.Replace(mainNameWithoutSpec, newMainName);
                        // 最后将新主名称和原规格部分重新拼接起来
                        return updatedMainName + spec;
                    }
                    string queryOldItemName = $"SELECT item_name FROM info_item_prop WHERE company_id = {companyID} AND item_id = {item_id}";
                    cmd.CommandText = queryOldItemName;
                    string old_item_name = (string)await cmd.ExecuteScalarAsync();
                    string querySubItems = $@"SELECT item_id, item_name FROM info_item_prop WHERE company_id={companyID} AND son_mum_item = {item_id}";
                    List<ExpandoObject> subItems = await CDbDealer.GetRecordsFromSQLAsync(querySubItems, cmd);
                    foreach (dynamic subItem in subItems)
                    {
                        string subItemName = subItem.item_name;
                        string new_item_name = data.item_name?.ToString() ?? "";
                        // 替换主名称，保留括号中的规格
                        string updatedSubItemName = ReplaceMainName(subItemName, new_item_name);
                        //Console.WriteLine("updatedSubItemName: " + updatedSubItemName);
                        // 更新子商品的名称
                        string updateSubItemNameSql = $"UPDATE info_item_prop SET item_name = '{updatedSubItemName}' WHERE son_mum_item = {item_id} AND item_id={subItem.item_id}";
                        cmd.CommandText = updateSubItemNameSql;
                        await cmd.ExecuteNonQueryAsync();
                    }

                    string attrSql = "";
                    dynamic attrInfo = data.attrInfo;
                    bool distinctStock = attrInfo.distinctStock;



                    JArray itemMumAttributes = attrInfo.itemMumAttributes;
                    JArray availAttrCombine = attrInfo.availAttrCombine;
                    JArray deleteItemList = attrInfo.deleteItemList;
                    string itemInfoSql = @$"select ip.item_spec,ip.item_id,item_name,py_str,item_no,item_class,other_class,item_brand,mu.unit_no,unit_factor,unit_type,mu.barcode,mu.wholesale_price,mu.buy_price,mu.retail_price,mu.weight,mu.volume from info_item_prop ip left join info_item_multi_unit mu  on ip.item_id=mu.item_id  and ip.company_id=mu.company_id where ip.company_id={companyID} and ip.item_id={item_id}";
                    List<ExpandoObject> itemUnitInfo = await CDbDealer.GetRecordsFromSQLAsync(itemInfoSql, cmd);
                    if (itemMumAttributes.Count > 0 || availAttrCombine.Count > 0 || deleteItemList.Count > 0)
                    {
                        if (distinctStock)
                        {
                            SQLQueue QQ = new SQLQueue(cmd);
                            CDbDealer db;
                            //  区分库存：  如果id存在， 更新状态、批发价、条码  不存在：状态是true，创建对应的子商品，更新状态、批发价、条码
                            string createItemSql;
                            List<string> deleteSonItemIds = new List<string>();
                            foreach (dynamic availItem in availAttrCombine)
                            {
                                string availItemStatus = availItem.status + "";
                                string availItemId = availItem.item_id + "";
                                string availItemBBarcode = availItem.bBarcode + "";
                                string availItemMBarcode = availItem.mBarcode + "";
                                string availItemSBarcode = availItem.sBarcode + "";
                                string availItemBPrice = availItem.bPrice + "";
                                string availItemMPrice = availItem.mPrice + "";
                                string availItemSPrice = availItem.sPrice + "";
                                string availItemSonOptionsId = availItem.son_options_id + "";
                                db = new CDbDealer();
                                if (availItemId.StartsWith("nanoid"))
                                {
                                    db.AddField("company_id", companyID);
                                    db.AddField("item_name", $@"{availItem.item_name}");
                                    db.AddField("py_str", $@"{availItem.py_str}");
                                    db.AddField("son_mum_item", item_id);
                                    db.AddField("son_options_id", availItemSonOptionsId);
                                    db.AddField("item_class", item_class);
                                    db.AddField("item_spec", item_spec);
                                    db.AddField("item_no", item_no);
                                    db.AddField("other_class", other_class);
                                    db.AddField("item_brand", item_brand);
                                    db.AddField("status", availItemStatus);
                                    if (!string.IsNullOrEmpty(batch_level))
                                    {
                                        db.AddField("batch_level", batch_level);
                                    }
                                    string sqlItem = db.GetInsertSQL("info_item_prop") + " returning item_id";
                                    string sqlUnits = "";
                                    foreach (dynamic unit in itemUnitInfo)
                                    {
                                        db = new CDbDealer();
                                        db.AddField("company_id", companyID);
                                        db.AddField("item_id", "@ITEM_ID");
                                        db.AddField("unit_no", unit.unit_no);
                                        db.AddField("unit_factor", unit.unit_factor);
                                        db.AddField("unit_type", unit.unit_type);
                                        // db.AddField("wholesale_price", unit.wholesale_price);
                                        db.AddField("buy_price", unit.buy_price);
                                        db.AddField("retail_price", unit.retail_price);
                                        db.AddField("weight", unit.weight);
                                        db.AddField("volume", unit.volume);
                                        string barcode = unit.barcode;
                                        string wholesalePrice = unit.wholesale_price;
                                        string sonBarcode = "";
                                        string sonWholesalePrice = "";
                                        if (unit.unit_type == "b")
                                        {
                                            sonBarcode = string.IsNullOrEmpty(availItemBBarcode) ? barcode : availItemBBarcode;
                                            sonWholesalePrice = string.IsNullOrEmpty(availItemBPrice) ? wholesalePrice : availItemBPrice;
                                            db.AddField("barcode", sonBarcode);
                                            db.AddField("wholesale_price", sonWholesalePrice);
                                        }
                                        else if (unit.unit_type == "m")
                                        {
                                            sonBarcode = string.IsNullOrEmpty(availItemMBarcode) ? barcode : availItemMBarcode;
                                            sonWholesalePrice = string.IsNullOrEmpty(availItemMPrice) ? wholesalePrice : availItemMPrice;
                                            db.AddField("barcode", sonBarcode);
                                            db.AddField("wholesale_price", sonWholesalePrice);
                                        }
                                        else if (unit.unit_type == "s")
                                        {
                                            sonBarcode = string.IsNullOrEmpty(availItemSBarcode) ? barcode : availItemSBarcode;
                                            sonWholesalePrice = string.IsNullOrEmpty(availItemSPrice) ? wholesalePrice : availItemSPrice;
                                            db.AddField("barcode", sonBarcode);
                                            db.AddField("wholesale_price", sonWholesalePrice);
                                        }
                                        sqlUnits += db.GetInsertSQL("info_item_multi_unit") + ";";
                                    }
                                    createItemSql = $@"SELECT yj_exeSqlByInsertedRowID('{sqlItem.Replace("'", "''")}','{sqlUnits.Replace("'", "''")}','@ITEM_ID');";
                                    QQ.Enqueue(availItemSonOptionsId, createItemSql);
                                }
                                else
                                {
                                    deleteSonItemIds.Add(availItemId);
                                    attrSql += $"update info_item_prop set status='{availItemStatus}' where company_id={companyID} and item_id={availItemId};";
                                    foreach (dynamic unit in itemUnitInfo)
                                    {
                                        db = new CDbDealer();
                                        db.AddField("company_id", companyID);
                                        db.AddField("item_id", availItemId);
                                        db.AddField("unit_no", unit.unit_no);
                                        db.AddField("unit_factor", unit.unit_factor);
                                        db.AddField("unit_type", unit.unit_type);
                                        // db.AddField("wholesale_price", unit.wholesale_price);
                                        db.AddField("buy_price", unit.buy_price);
                                        db.AddField("retail_price", unit.retail_price);
                                        db.AddField("weight", unit.weight);
                                        db.AddField("volume", unit.volume);
                                        string barcode = unit.barcode;
                                        string wholesalePrice = unit.wholesale_price;
                                        string sonBarcode = "";
                                        string sonWholesalePrice = "";
                                        if (unit.unit_type == "b")
                                        {
                                            sonBarcode = string.IsNullOrEmpty(availItemBBarcode) ? barcode : availItemBBarcode;
                                            sonWholesalePrice = string.IsNullOrEmpty(availItemBPrice) ? wholesalePrice : availItemBPrice;
                                            db.AddField("barcode", sonBarcode);
                                            db.AddField("wholesale_price", sonWholesalePrice);
                                        }
                                        else if (unit.unit_type == "m")
                                        {
                                            sonBarcode = string.IsNullOrEmpty(availItemMBarcode) ? barcode : availItemMBarcode;
                                            sonWholesalePrice = string.IsNullOrEmpty(availItemMPrice) ? wholesalePrice : availItemMPrice;
                                            db.AddField("barcode", sonBarcode);
                                            db.AddField("wholesale_price", sonWholesalePrice);
                                        }
                                        else if (unit.unit_type == "s")
                                        {
                                            sonBarcode = string.IsNullOrEmpty(availItemSBarcode) ? barcode : availItemSBarcode;
                                            sonWholesalePrice = string.IsNullOrEmpty(availItemSPrice) ? wholesalePrice : availItemSPrice;
                                            db.AddField("barcode", sonBarcode);
                                            db.AddField("wholesale_price", sonWholesalePrice);
                                        }
                                        attrSql += db.GetInsertSQL("info_item_multi_unit") + ";";

                                        // attrSql += db.GetUpdateSQL("info_item_multi_unit", $"company_id ={companyID} and item_id = {availItemId} and unit_type = '{unit.unit_type}'") + ";";
                                    }
                                }
                            }

                            if (deleteSonItemIds.Count > 0)
                            {
                                string deleteSonItemSql = @$"
delete from info_item_multi_unit iimu where iimu.company_id = {companyID} and iimu.item_id in ({string.Join(", ", deleteSonItemIds.Select(id => $"'{id}'").ToArray())});";
                                cmd.CommandText = deleteSonItemSql;
                                await cmd.ExecuteNonQueryAsync();
                            }

                            if (QQ.Count > 0)
                            {
                                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                                while (QQ.Count > 0)
                                {
                                    string son_options_id = QQ.Dequeue();
                                    if (dr.Read())
                                    {
                                        object ovAttrOv = dr[0];
                                        if (ovAttrOv != DBNull.Value)
                                        {
                                            // 查找并更新
                                            foreach (JObject item in availAttrCombine.Children<JObject>())
                                            {
                                                if ((string)item["son_options_id"] == son_options_id)
                                                {
                                                    string s = ovAttrOv.ToString();
                                                    if (s.Contains(","))
                                                    {
                                                        s = s.Split(",")[0];
                                                    }
                                                    item["item_id"] = s; // 设置新的 item_id 值
                                                    item["son_mum_item"] = item_id; // 设置新的 item_id 值
                                                    break; // 如果只需要更新第一个匹配的元素，找到后即可退出循环
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            QQ.Clear();
                        }
                        else
                        {
                            // 不区分库存的商品，如果第一次创建成区分库存，未使用的时候创建了子商品，后续改成了不区分库存的商品，应该进行删除已经创建的子商品
                            var availAttrCombineIds = availAttrCombine
                                .Where(item => !((string)item["item_id"]).StartsWith("nanoid"))
                                .Select(item => $"'{(string)item["item_id"]}'")
                                .Distinct(); // 去除重复的ID

                            var deleteItemListIds = deleteItemList
                                .Where(item => !((string)item["item_id"]).StartsWith("nanoid"))
                                .Select(item => $"'{(string)item["item_id"]}'")
                                .Distinct(); // 去除重复的ID

                            var filteredIds = availAttrCombineIds.Concat(deleteItemListIds).Distinct(); // 合并并去除重复的ID
                            string noDistinctStockId = string.Join(",", filteredIds); // 使用逗号连接字符串

                            if (!string.IsNullOrEmpty(noDistinctStockId))
                            {
                                attrSql += @$"delete from info_item_prop where company_id={companyID} and item_id in ({noDistinctStockId});";
                                attrSql += @$"delete from info_item_multi_unit where company_id={companyID} and item_id in ({noDistinctStockId});";
                                foreach (dynamic availItem in availAttrCombine)
                                {
                                    availItem.item_id = "nanoid" + Nanoid.Nanoid.Generate();
                                }
                            }


                        }

                        // 更新mum_attribute\avail_attr_combine
                        string itemMumAttributesStr = itemMumAttributes.ToString();
                        string availAttrCombineStr = availAttrCombine.ToString();
                        attrSql += @$"update info_item_prop set mum_attributes='{itemMumAttributesStr}', avail_attr_combine = '{availAttrCombineStr}' where company_id={companyID} and item_id={item_id};";
                        if ("0".Equals(status))
                        {   // 父商品停用，才能全部停用
                            attrSql += @$"update info_item_prop set status = '{status}' where company_id={companyID} and son_mum_item={item_id};";
                        }
                        // 处理删除问题 deleteItemList 删除创建的档案， id不为nanoid的情况
                        string itemIds = string.Join(",", deleteItemList.Select(item => $"'{(string)item["item_id"]}'"));
                        if (!string.IsNullOrEmpty(itemIds))
                        {
                            attrSql += @$"delete from info_item_prop where company_id={companyID} and item_id in ({itemIds});";
                            attrSql += @$"delete from info_item_multi_unit where company_id={companyID} and item_id in ({itemIds});";
                        }
                        if (!string.IsNullOrEmpty(attrSql))
                        {
                            await CDbDealer.GetRecordsFromSQLAsync(attrSql, cmd);
                        }
                    }
                }
                else//是子商品
                {
                    // 同步更新父商品中avail_attr_combine
                    string itemFatherSQL = $"select avail_attr_combine from info_item_prop where  company_id={companyID} and item_id= {son_mum_item}";
                    dynamic availResult = await CDbDealer.Get1RecordFromSQLAsync(itemFatherSQL, cmd);
                    var availAttrCombine = ((IDictionary<string, object>)availResult)["avail_attr_combine"].ToString();
                    JArray availAttrCombineJArray = JArray.Parse(availAttrCombine);
                    JObject findAvailItem = availAttrCombineJArray.FirstOrDefault(availItem => availItem["son_options_id"].ToString().Equals(data_son_options_id)) as JObject;
                    if (findAvailItem != null)
                    {
                        findAvailItem["status"] = Int32.Parse(status);
                        JArray gridUnits = JArray.Parse(data["gridUnit"].ToString());
                        foreach (JObject gridUnit in gridUnits)
                        {
                            string unitType = gridUnit["unit_type"].ToString();
                            switch (unitType)
                            {
                                case "b":
                                    findAvailItem["bBarcode"] = gridUnit["barcode"];
                                    findAvailItem["bPrice"] = gridUnit["wholesale_price"];
                                    break;
                                case "m":
                                    findAvailItem["mBarcode"] = gridUnit["barcode"];
                                    findAvailItem["mPrice"] = gridUnit["wholesale_price"];
                                    break;
                                case "s":
                                    findAvailItem["sBarcode"] = gridUnit["barcode"];
                                    findAvailItem["sPrice"] = gridUnit["wholesale_price"];
                                    break;
                            }
                        }
                    }
                    string newAvail = availAttrCombineJArray.ToString();
                    itemFatherSQL = @$"update info_item_prop set avail_attr_combine = '{newAvail}' where company_id={companyID} and item_id={son_mum_item};";
                    await CDbDealer.Get1RecordFromSQLAsync(itemFatherSQL, cmd);
                }
            }
            catch (Exception e)
            {
                tran.Rollback();
                Console.WriteLine(e);
                return new JsonResult(new { result = "ERROR", msg = "修改商品属性失败" });
            }
            // 

            if (item_id != "" && !item_brand.IsNullOrWhiteSpace())
            {
                // 同步分销商商品档案

                if ((oldClass != null && (string)oldClass.item_class != item_class) || ((string)data.item_id).IsNullOrWhiteSpace()) changeClass = true;
                dynamic itemInfoSyncResult = await ResellerService.EditItemInfoSync(new
                {
                    itemId = item_id,
                    operKey = (string)data.operKey,
                    brandId = item_brand,
                    companyId = companyID,
                    changeClass = true

                }, cmd, tran);
                if (itemInfoSyncResult.result != "OK")
                {
                    return Json(new { result = "Error", msg = itemInfoSyncResult.msg, added = false, record = new { } });
                }
            }


            if (result != "OK")
            {
                tran.Rollback();
                return sign;
            }
            dynamic d = data["gridUnit"];
            if (data.pricePlanSubmitInfo != null && data.pricePlanSubmitInfo.Count > 0)
            {
                string sql = "";
                //string item_id = model.DataItems["item_id"].Value;
                //先将这个商品的价格方案删除
                string deleteSql = $@"delete  from price_plan_item where company_id={companyID} and item_id={item_id}";
                cmd.CommandText = deleteSql;
                await cmd.ExecuteNonQueryAsync();

                foreach (dynamic plan in data.pricePlanSubmitInfo)
                {
                    if (plan.s_price == null || plan.s_price.ToString() == "") plan.s_price = "null";
                    if (plan.m_price == null || plan.m_price.ToString() == "") plan.m_price = "null";
                    if (plan.b_price == null || plan.b_price.ToString() == "") plan.b_price = "null";
                    if (plan.discount == null || plan.discount.ToString() == "") plan.discount = "null";
                    sql += $@"
                               insert into price_plan_item (company_id,plan_id,item_id,s_price,m_price,b_price,discount) values  ({companyID},{plan.plan_id},{item_id},{plan.s_price},{plan.m_price},{plan.b_price},{plan.discount})
                                on conflict (company_id,item_id,plan_id) do update set plan_id={plan.plan_id},s_price={plan.s_price},m_price={plan.m_price},b_price={plan.b_price},discount={plan.discount};
                        ";
                }
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();

            }
            if (d != null)
            {
                JArray rows = d;
                dynamic unit = rows[0];
                string cost_price_avg = unit.cost_price_avg;
                if (cost_price_avg != "")
                {
                    //string item_id=model.DataItems["item_id"].Value;
                    cmd.CommandText = $"update info_item_prop set cost_price_avg='{cost_price_avg}' where company_id={companyID} and item_id={item_id} and cost_price_avg is null;";
                    await cmd.ExecuteNonQueryAsync();
                }
            }


            string mallStatus = data["mall_status"];
            if ("0".Equals((mallStatus)) || "0".Equals(status))
            {
                cmd.CommandText = $"UPDATE mall_items SET on_sale = false where company_id={companyID} and item_id={item_id}";
                await cmd.ExecuteNonQueryAsync();
            }
            tran.Commit();
            return sign;
        }
        
        
        
        
        
        [HttpPost]
        public async Task<IActionResult> uploadVideo([FromBody] dynamic data)
        {

            string error = "";
            ItemEditModel model = new ItemEditModel(cmd);
            string item_class = data["item_class"];
            string other_class = data["other_class"];
            string item_brand = data["item_brand"];
            // string son_mum_item = data["item_id"];
            string item_name = data["item_name"];
            string item_no = data["item_no"];
            string item_spec = data["item_spec"];
            string status = data["status"];
            string son_mum_item = data["son_mum_item"];
            string data_son_options_id = data["son_options_id"];

            Security.GetInfoFromOperKey((string)data.operKey, out string companyID, out string operID);
            cmd.ActiveDatabase = "";

            dynamic jsonRights = await CDbDealer.Get1RecordFromSQLAsync($@"SELECT rights FROM info_operator o 
    left join info_role r on o.company_id = r.company_id and o.role_id = r.role_id
where o.company_id = {companyID} and o.oper_id = {operID}", cmd);
            data.approve_brief = "";
            data.receiver_id = "";
            data.flow_id = "";
            data.msg_id = "";
            string approveFlag = "";
            dynamic rights = Newtonsoft.Json.JsonConvert.DeserializeObject(jsonRights.rights);
            if (rights != null)
            {
                if (rights.info.infoItem.approve == "false")
                {
                    if (data.approve_status == "wait approve")
                    {
                        return new JsonResult(new { result = "ERROR", msg = "该档案上次编辑未审核，请先审核再编辑" });
                    }
                    model.DataItems["approve_status"].Value = "wait approve";
                    data.approve_status = "wait approve";
                    if (data.item_id == "")
                    {
                        if (rights.info.infoItem.create == "false")
                        {

                            return new JsonResult(new { result = "ERROR", msg = "暂无新建权限" });
                        }
                        approveFlag = "CREATE";
                    }
                    else
                    {
                        if (rights.info.infoItem.edit == "false")
                        {

                            return new JsonResult(new { result = "ERROR", msg = "暂无编辑权限" });
                        }
                        approveFlag = "EDIT";
                    }

                }
                else
                {
                    if (data.approve_status == "wait approve")
                    {
                        string docSql = $@"SELECT flow_id,oper_action FROM document_change_log 
                    where company_id = {companyID} and obj_name ='商品档案' and obj_id = {data.item_id}";
                        dynamic docInfo = await CDbDealer.Get1RecordFromSQLAsync(docSql, cmd);
                        if (docInfo != null)
                        {
                            if (docInfo.oper_action == "CREATE") approveFlag = "APPROVED_FROM_CREATE";
                            else approveFlag = "APPROVED_FROM_EDIT";
                            data.flow_id = docInfo.flow_id;
                        }
                        else
                        {
                            return new JsonResult(new { result = "ERROR", msg = "商品档案历史记录查询失败" });
                        }
                    }
                    else
                    {
                        if (data.item_id == "")
                        {
                            if (rights.info.infoItem.create == "false")
                            {

                                return new JsonResult(new { result = "ERROR", msg = "暂无新建权限" });
                            }
                            approveFlag = "CREATE_AND_APPROVED";
                        }
                        else
                        {
                            if (rights.info.infoItem.edit == "false")
                            {

                                return new JsonResult(new { result = "ERROR", msg = "暂无编辑权限" });
                            }
                            approveFlag = "EDIT_AND_APPROVED";
                        }
                    }
                    model.DataItems["approve_status"].Value = "";
                    data.approve_status = "";


                }
                data.approve_flag = approveFlag;
            }
            else
            {

                return new JsonResult(new { result = "ERROR", msg = "获取权限失败" });
            }

            string msg = "";
            string newPath = "";
            string uploadVideosrc = "";
            string uploadDescsrc = "";
            string uploadDesc = data.uploadDesc;
            try
            {
                if (uploadDesc== "False")
                {
                    string item_id = data.item_id;
                    int uploadFlag = data.uploadVideoFlag;
                    if (uploadFlag == 2)
                    {
                        uploadVideosrc = data.uploadVideosrc?.ToString();
                    }
                    else if (uploadFlag == 0 || uploadFlag == 1) uploadVideosrc = data.uploadVideosrc;
                    int? othCount = data.othCount != null ? (int?)data.othCount : null;
                    if (!string.IsNullOrEmpty(uploadVideosrc))
                    {
                        string dir = $"item-videos/company_{companyID}/";
                        var id = data.item_id == "" ? $"newIn{DateTime.Now:yyyy-MM-dd_HH-mm-ss-ffff}" : data.item_id;
                        string ite = $"item_{id}_";
                        string pathBase = dir + ite;
                        newPath = await HuaWeiObsController.HuaWeiObs.WebItemVideoSave(_httpClientFactory, uploadVideosrc, uploadFlag, pathBase, othCount);
                    }
                }
                else
                {
                    string item_id = data.item_id;
                    int uploadFlag = data.uploadDescFlag;
                    if (uploadFlag == 2)
                    {
                        uploadDescsrc = data.uploadDescsrc?.ToString();
                    }
                    else if (uploadFlag == 3) uploadDescsrc = data.uploadDescsrc.ToString();
                    int othCount = data.othCount;
                    if (!string.IsNullOrEmpty(uploadDescsrc))
                    {
                        string dir = $"item-desc/images/company_{companyID}/";
                        var id = data.item_id == "" ? $"newIn{DateTime.Now:yyyy-MM-dd_HH-mm-ss-ffff}" : data.item_id;
                        string ite = $"item_{id}_";
                        string pathBase = dir + ite;
                        newPath = await HuaWeiObsController.HuaWeiObs.WebItemImageSaveforDesc(_httpClientFactory, uploadDescsrc, uploadFlag, pathBase, othCount);
                    }
                }
            }
            catch (Exception ex)
            {
                msg = "上传发生错误";
                NLogger.Error("上传视频发生错误：" + ex.ToString());
                return new JsonResult(new { result = "ERROR", msg });
            }
            string result = "OK";
            if (msg != "") result = "Error";
            return new JsonResult(new { result, msg, newPath }); 
        }
        [HttpPost]
        public async Task<dynamic> CreateAttrOptItem([FromBody] dynamic data)
        {
            Security.GetInfoFromOperKey((string)data.operKey, out string companyID);
            string attrId = data.attr_id;
            string optName = data.opt_name;
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            string sql = @$"
insert into info_attr_opt(company_id,attr_id,opt_name) 
values ({companyID}, {attrId}, '{optName}') 
on conflict(company_id,attr_id,opt_name) 
    do update set company_id={companyID} returning attr_id,opt_id,opt_name;";
            dynamic result = null;
            try
            {
                cmd.CommandText = sql;
                result = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
                tran.Commit();
                return Json(new ResultUtil<dynamic>().CommonResult(0, "success", result));
            }
            catch (Exception e)
            {
                tran.Rollback();
                return Json(new ResultUtil<dynamic>().CommonResult(-1, e.Message, null));
            }


        }
    }
}