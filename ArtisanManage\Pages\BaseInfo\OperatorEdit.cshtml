@page
@model ArtisanManage.Pages.BaseInfo.OperatorEditModel
@{
    Layout = null;
} 
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>OperatorEdit</title>
    <partial name="_FormPageHead" model="Model.PartialViewModel" />
    <script src="~/js/Vue.js"></script>
     <style>
        
        #header {
            display: flex;
            justify-content: space-around;
            width:70%;
            margin-left:auto;
            margin-right:auto;
        }

        #paywayboxTop {
            margin-bottom: 10px;
        }
         #rightboxTop{
             margin-bottom:10px;
         }
      
         .info {
             margin: 1rem auto;
         }

        .box {
            display: none;
            margin-top: 10px;
             
        }

        .active {
            display: block;
        }

        .tab-content {
            text-align: center;
            flex: auto;
            background-color:#f6f6f6;
            cursor: pointer;
            padding: 0.5rem;
            font-size: 1rem;
        }

        .tab-active {
            background: #ffffff;
            font-size: 1rem;
            padding: 0.5rem;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        label[role=checkbox] {
            display: inline-block;
            text-align: center;
            width: 100%;
            height: 100%;
            padding-top:0.25rem;
            font-weight: bold;
            color: red;
            cursor:pointer;
        }

            label[role=checkbox].True:after {
                content: '\2713';
            }

            label[role=checkbox].unchecked::after {
                content: '\a0';
            }

            label[role=checkbox].part::after {
                content: '\1508';
                color: #FA8E53;
            }

        .buttons {
            display: flex;
            justify-content: center;
        }

        ul,li{
            list-style:none;
            padding-left:0.5rem;
 
        }
         .selectall, .selectrow {
             width: 18px;
             height: 18px;
             cursor:pointer;
             margin-top:3px;
         }
    </style>
     <script type="text/javascript">

         var tree;
         var treePay;
         var selectAll = false;
         $.prototype.listen = function (customEvent, selectorType = '.') {
             Object.keys(customEvent).forEach(eventType => {
                 Object.keys(customEvent[eventType]).forEach(action => {
                     var act = customEvent[eventType][action];
                     this.on(eventType, selectorType + action, function (e) {
                             e.stopPropagation();
                             act.call(this, e)
                     });
                 });
             });
         };
         function restrictBranches() {
             if ($('#restrict_branches').jqxCheckBox('checked')===true) $('#rightbox').show()
             else $('#rightbox').hide();
         }
         function restrictPayWays() {
             if ($('#restrict_pay_ways').jqxCheckBox('checked')===true) { $('#restrict').css('display', 'block'); }
             else { $('#restrict').css('display', 'none'); }
         } 
        @Html.Raw(Model.m_saveCloseScript)
            $(document).ready(function () {

                //  var gridWidth=parseInt($('#rightbox').css('width'))
                @Html.Raw(Model.m_showFormScript)
                    @Html.Raw(Model.m_createGridScript)
                    $('#oper_name input').on('input', function () {
                        $('#py_str input').val(this.value.ToPinYinCode());
                    })
                $('#header').on('click', 'span', function (e) {
                    var id = $('#header span.tab-active').toggleClass("tab-active").data("box");
                    $(id).toggle();
                    $(this).toggleClass("tab-active");
                    id = $(this).data("box");
                    $(id).toggle()
                });

                var oper_regions = $('#oper_regions').val()
                if (oper_regions) oper_regions = JSON.parse(oper_regions)
                tree = plantTree({ checkedList: oper_regions }, '.rootNode', tree)
                var gridWidth = window.innerWidth - 50;
                $('#rightbox').jqxGrid({ width: gridWidth, height: window.innerHeight - 155 });
                restrictBranches();

                $('#rightbox').listen({
                    click: {
                        cell(e) {
                            var value = $(this).hasClass('True') ? '' : 'True';
                            var row = this.getAttribute('index');
                            var colfield = this.getAttribute('field');
                            $('#rightbox').jqxGrid('setcellvalue', row, colfield, value);
                        },
                        selectrow() {
                            var rowIndex = this.getAttribute('index');
                            var rows = $('#rightbox').jqxGrid('getrows')
                            var row = rows[rowIndex]
                            var checked = this.checked ? 'True' : '';

                            var columns = $('#rightbox').jqxGrid('columns').records;

                            for (var i = 3; i < columns.length; i++) {
                                var col = columns[i];
                                row[col.datafield] = checked
                                //$('#rightbox').jqxGrid('setcellvalue', row, col.datafield, checked)
                            }
                            $('#rightbox').jqxGrid('updategrid')
                        },
                        selectall() {
                            selectAll = !selectAll
                            $('#rightbox').jqxGrid('updatebounddata')
                            var columns = $('#rightbox').jqxGrid('columns').records
                            var rows = $('#rightbox').jqxGrid('getrows')
                            var n = rows.length
                            for (var i = 0; i < n; i++) {
                                var row = rows[i]
                                for (var j = 3; j < columns.length; j++) {
                                    var col = columns[j];
                                    row[col.datafield] = selectAll ? "True" : ""
                                    // $('#rightbox').jqxGrid('setcellvalue', i, col.datafield, selectAll ? "True" : "");
                                }
                            }
                            $('#rightbox').jqxGrid('updategrid')
                        }
                    }
                });
                $('#rightboxTop').listen({
                    click: {
                        restrict_branches() {
                            restrictBranches();
                        }
                    }
                }, '#');


                restrictPayWays();
                var avail_pay_ways = $('#avail_pay_ways').val()
                if (avail_pay_ways) avail_pay_ways = JSON.parse(avail_pay_ways)
                treePay = plantTree({ checkedList: avail_pay_ways }, '.rootNodePay')
                //  $('#restrict_pay_ways').listen({
                //     click: {
                //         restrict_payways(){ 
                //             restrictpayways();
                //         }
                //     }
                // }, '#');

                 $('#paywayboxTop').on('change', restrictPayWays);
                //$('#paywayboxTop').on('click', '.restrict_payways', function () {
                //  restrictPayWays();
                // });

            });

        function dealFormData(formData) {
            formData.oper_regions = tree.view();
            formData.avail_pay_ways = treePay.view();
        }
     </script>

 
   
</head>
<body style="background-color:#f4f4f4;margin:0px;padding-top:10px;">
    <div id="header" >
        <span class="tab-content tab-active" data-box="#infobox">基础信息</span>
        <span class="tab-content" data-box="#branchRights">仓库权限</span>
        <span class="tab-content" data-box="#regionbox">负责区域</span>
        <span class="tab-content" data-box="#paywaybox">支付方式</span>
    </div>
    <div style="height:calc(100% - 120px);width:97%;border:1px solid #fff;background-color:#fff; margin-left:auto;margin-right:auto;">

            <div id="infobox" class="box active" style="height:calc(100% - 190px);width:80%;margin-left:auto;margin-right:auto;">
                <div id="divHead" class="headtail info"></div>
                <div style="text-align:center;margin-top:20px;">
                </div>
            </div>
            <div id="branchRights" class="box" style="height: calc(100% - 20px); width:96%;margin-left:auto;margin-right:auto;">
                <div id="rightboxTop"> </div> <!--   <input type="checkbox" id="restrict_branches"/>限制使用仓库-->
                <div id="rightbox" style="height:100%;width:100%;" hidden></div>
            </div>
     
            <ul id="regionbox" class="box rootNode" style="background:#fff;height:calc(100% - 30px);overflow-y:scroll;padding-left:10px;margin-top:20px;" >
                <partial name="tree" model=@Model.Region/>
            </ul>
            <ul id="paywaybox" class="box rootNodePay" style="background:#fff;height:calc(100% - 30px);overflow-y:scroll;padding-left:10px;margin-top:20px;">
                <div id="paywayboxTop"> </div> 
                <div id="restrict">
                <partial name="tree" model=@Model.PayWayNew/>
                </div>
            </ul>
    </div>
    <div class="buttons" style="margin-top:30px;">
        <button id="btnSave" onclick="btnSave_Clicked();" style="margin-right:50px;">保存</button> <button id="btnClose" onclick="btnClose_Clicked();">关闭</button>
    </div>
</body>
</html>
