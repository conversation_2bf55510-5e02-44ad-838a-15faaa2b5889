﻿using ArtisanManage.Models;
using System.Threading.Tasks;

namespace ArtisanManage.MyJXC
{
    public class SheetBuyRowPriceAdjust: SheetRowBase
    {
        [SaveToDB][FromFld] public string item_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string item_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_no { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string s_unit_no { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string b_unit_factor { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string m_unit_factor { get; set; }
        [SaveToDB][FromFld] public string s_price { get; set; }
        [SaveToDB][FromFld] public string m_price { get; set; }
        [SaveToDB][FromFld] public string b_price { get; set; }
        [FromFld("yj_get_unit_relation(b_unit_no, b_unit_factor, m_unit_no, m_unit_factor, s_unit_no) as unit_conv", LOAD_PURPOSE.SHOW)] public string unit_conv { get; set; }
        [SaveToDB][FromFld] public string s_old_price { get; set; }
        [SaveToDB][FromFld] public string m_old_price { get; set; }
        [SaveToDB][FromFld] public string b_old_price { get; set; }

    }

    public class SheetBuyPriceAdjust : SheetBase<SheetBuyRowPriceAdjust>
    {
        [SaveToDB][FromFld] public string seller_id { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";
        [SaveToDB][FromFld] public override SHEET_TYPE sheet_type { get; set; }

        public SheetBuyPriceAdjust(LOAD_PURPOSE loadPurpose) : base("sheet_buy_price_adjust_main", "sheet_buy_price_adjust_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.BUY_SHEET_PRICE_ADJUST;
            MainLeftJoin = @"
                                        left join (select oper_id,oper_name seller_name from info_operator) seller on t.seller_id=seller.oper_id
                                        left join (select oper_id,oper_name as maker_name from info_operator) maker on t.maker_id=maker.oper_id
                                        left join (select oper_id,oper_name as approver_name from info_operator) approver on t.approver_id=approver.oper_id
            ";
            DetailLeftJoin = @"left join info_item_prop i on t.item_id=i.item_id
                                   left join (select item_id,s_unit->>'f1' as s_unit_no,(s_unit->>'f2')::real as s_unit_factor,m_unit->>'f1' as m_unit_no,(m_unit->>'f2')::real as m_unit_factor,
                                                     b_unit->>'f1' as b_unit_no,(b_unit->>'f2')::real as b_unit_factor
                                              from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor)) as json from info_item_multi_unit where company_id=~company_id order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)) unit_barcode on t.item_id=unit_barcode.item_id    
                                   left join info_item_brand ib on i.item_brand=ib.brand_id
                                  ";
        }

        protected class CInfoForApprove : CInfoForApproveBase
        {
          
        }
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
        }
        protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;
            base.GetInfoForApprove_SetQQ(QQ);
        }
        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
         

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            string sql = "";
            string timeSql = "";
            string buySql = "";
            foreach (var sheetRow in SheetRows)
            {
                //更新采购商品最新时间调价表
                timeSql += $@"insert into buy_item_price_adjust(company_id,sheet_id,item_id,last_adjust_time) values ({company_id},{sheetID},{sheetRow.item_id},'{happen_time}')
                                on conflict (item_id) do update set last_adjust_time='{happen_time}',sheet_id={sheetID};                   
                                ";

                //更新商品档案里面的进价
                if (!string.IsNullOrEmpty(sheetRow.s_price))
                {
                    buySql += @$"insert into info_item_multi_unit(company_id,item_id,unit_no,unit_factor,buy_price,unit_type) values ({company_id},{sheetRow.item_id},'{sheetRow.s_unit_no}',1,{sheetRow.s_price},'s')
                            on conflict (company_id,item_id,unit_type) do update set buy_price={sheetRow.s_price};";
                }

                if (!string.IsNullOrEmpty(sheetRow.m_price))
                {
                    buySql += @$"insert into info_item_multi_unit(company_id,item_id,unit_no,unit_factor,buy_price,unit_type) values ({company_id},{sheetRow.item_id},'{sheetRow.m_unit_no}',{sheetRow.m_unit_factor},{sheetRow.m_price},'m')
                            on conflict (company_id,item_id,unit_type) do update set buy_price={sheetRow.m_price};";
                }

                if (!string.IsNullOrEmpty(sheetRow.b_price))
                {
                    buySql += @$"insert into info_item_multi_unit(company_id,item_id,unit_no,unit_factor,buy_price,unit_type) values ({company_id},{sheetRow.item_id},'{sheetRow.b_unit_no}',{sheetRow.b_unit_factor},{sheetRow.b_price},'b')
                            on conflict (company_id,item_id,unit_type) do update set buy_price={sheetRow.b_price};";
                }

            }
              sql = timeSql + buySql;
            if (sql != "") 
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
            
        
        
        }
    }
}
