﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using ArtisanManage.YingJiangBackstage.Pojo;
using Microsoft.AspNetCore.Mvc;

namespace ArtisanManage.AppController
{
    [Route("AppApi/[controller]/[action]")]
    public class InfoBriefController : QueryController
    {
        public InfoBriefController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpPost]
        public async Task<JsonResult> GetBriefListForInfoSheetDetailBrief([FromBody] dynamic param)
        {
            string operKey = param.operKey;
            Security.GetInfoFromOperKey(operKey, out string companyID);
            string sql = @$"select brief_id, brief_text from info_sheet_detail_brief where company_id = {companyID}";
            try
            {
                List<InfoSheetDetailBriefModel> infoSheetDetailBriefModels = await CDbDealer.GetRecordsFromSQLAsync<InfoSheetDetailBriefModel>(sql, cmd);
                return Json(new ResultUtil<dynamic>().CommonResult(0, "success" ,infoSheetDetailBriefModels));
            }
            catch (Exception e)
            {
                return Json(new ResultUtil<dynamic>().CommonResult(-1, "获取备注列表失败", null));
            }
        }
    }


    class InfoSheetDetailBriefModel
    {
        public int brief_id { get; set; }
        public string brief_text { get; set; }
    }
}