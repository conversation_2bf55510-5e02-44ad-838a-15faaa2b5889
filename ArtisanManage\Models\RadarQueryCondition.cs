﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.Models
{
    public class RadarQueryCondition
    {
        public string query
        {
            get;
            set;
        }
        public string location
        {
            get;
            set;
        }
        public string output
        {
            get;
            set;
        } = "json";
        public string radius
        {
            set;
            get;
        } = "2000";
        public string radiusLimit
        {
            set;
            get;
        } = "false";
        //
        public string scope
        {
            get;
            set;
        } = "2";
        public int coordType
        {
            get;
            set;
        } = 2;
        public int pageNum
        {
            get;
            set;
        } = 0;
        public int pageSize
        {
            get;
            set;
        } = 20;
        public string ak
        {
            get;
            set;
        } = "Qa6pXVRjlsnQrI8mNyNxHvTck4E3gndl";
    }
}
