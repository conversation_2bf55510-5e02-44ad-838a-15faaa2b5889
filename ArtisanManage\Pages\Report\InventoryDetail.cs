﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class InventoryDetailModel : PageQueryModel
    { 
        public InventoryDetailModel(CMySbCommand cmd) : base(Services.MenuId.inventoryDetail)
        {
            this.cmd = cmd;
            this.PageTitle = "商品盘点明细表";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time",   CompareOperator="<",Value = CPubVars.GetDateText(DateTime.Now.Date) + " 23:59",
                    JSDealItemOnSelect =@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
          
				 {"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){SqlFld="ip.item_brand"})},

				{"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",SqlFld="ip.item_id",DropDownWidth="300",QueryByLabelLikeIfIdEmpty=true,
                   SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
                {"seller_id",new DataItem(){Title="业务员", FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="seller_id",
                    SqlForOptions ="select oper_id as v,oper_name as l from info_operator where company_id=~COMPANY_ID and is_seller and (status=1 or status is null)"}},
                {"branch_id",new DataItem(){Title="仓库",FldArea="divHead",LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.branch_id",
                    SqlForOptions ="select branch_id as v,branch_name as l from info_branch"}},
                 

                {"remark",new DataItem(){Title="行备注",FldArea="divHead",TextAsValue=true, LabelFld="remark_name",ButtonUsage="list",CompareOperator="like",
                    SqlForOptions ="select brief_id as v,brief_text as l from info_sheet_detail_brief where sheet_type='X'"}},
                {"sheet_no",new DataItem(){Title="单号", FldArea="divHead",CompareOperator="like" }},
                {"sheet_type",new DataItem(){FldArea="divHead",Title="单据类型",LabelFld="sheet_type_name",ButtonUsage="list",Value="YK",Label="盘亏单",Source = "[{v:'YK',l:'盘亏单'},{v:'BS',l:'报损单'},{v:'',l:'所有'}]",CompareOperator="="}},
                {"byHappenTime",new DataItem(){FldArea="divHead",Title="按交易时间查询",CtrlType="jqxCheckBox",ForQuery=false}}

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                      ShowAggregates = true,
                      Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",new DataItem(){SqlFld="sm.sheet_id",Hidden=true,HideOnLoad = true}},
                       {"sheet_no",     new DataItem(){Title="单据编号", Sortable=true,    Width="100",SqlFld="sm.sheet_no" ,Linkable = true}},
                       {"sheet_type", new DataItem(){Title="单据类型",  Width="80",SqlFld="(case WHEN  sm.sheet_type='YK' THEN '盈亏单'  else '报损单' END)"}},
                       {"happen_time",     new DataItem(){Title="交易时间", Sortable=true, Width="150",SqlFld="sm.happen_time"}},
                       {"approve_time",     new DataItem(){Title="审核时间", Sortable=true, Width="150",SqlFld="sm.approve_time"}},
                       {"oper_name",    new DataItem(){Title="业务员",     Width="80",SqlFld="io.oper_name"}},
                       {"branch_name",    new DataItem(){Title="仓库",    Width="100"}},
                       {"item_name",    new DataItem(){Title="商品名称",    Width="100",SqlFld="ip.item_name"}},
                       {"unit_no",     new DataItem() {Title="单位",Hidden=true, Width="50",SqlFld=" sd.unit_no ",CellsAlign="right",}},
                       //{"quantity",     new DataItem(){Title="数量", Sortable=true,    Width="10%",SqlFld="concat(sd.quantity*inout_flag,sd.unit_no)" }},


                       {"quantity",     new DataItem() {Title="数量",  Width="50",
                           SqlFld="concat(sd.quantity ,sd.unit_no)",CellsAlign="right",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty ="";
                               if(sumColumnValues["quantity_b"]!="") sQty+= sumColumnValues["quantity_b"]+"大";
                               if(sumColumnValues["quantity_m"]!="") sQty+= sumColumnValues["quantity_m"]+"中";
                               if(sumColumnValues["quantity_s"]!="") sQty+= sumColumnValues["quantity_s"]+"小";
                               return sQty;
                           }

                       }},

                      {"quantity_b",   new DataItem(){Title="大数", Hidden=true,HideOnLoad = true,ShowSum=true, CellsAlign="right", Width="70",
                          SqlFld="case when itu.unit_type ='b' then sd.quantity  else null end"
                      }},
                      {"quantity_m",   new DataItem(){Title="中数",Hidden=true,HideOnLoad = true, ShowSum=true, CellsAlign="right", Width="70",
                          SqlFld="case when itu.unit_type ='m' then sd.quantity   else null end"
                      }},
                      {"quantity_s",   new DataItem(){Title="小数", Hidden=true,HideOnLoad = true,ShowSum=true, CellsAlign="right", Width="70",
                          SqlFld="case when itu.unit_type ='s' then sd.quantity  else null end"
                      }},






                        {"wholesale_amount",   new DataItem(){Title="批发金额",   Width="160" , ShowSum=true,
                           SqlFld=" round(  (  sd.wholesale_price * quantity * sd.unit_factor*inout_flag )  :: NUMERIC, 2 )", }},
                        {"cost_amount",   new DataItem(){Title="成本金额",   Width="160" , ShowSum=true,
                           SqlFld="	round( ( sd.cost_price_avg * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )",  }},
                        {"buy_amount",   new DataItem(){Title="进价金额",   Width="160"  ,ShowSum=true,
                           SqlFld="	round(    ( sd.cost_price_buy * quantity * sd.unit_factor*inout_flag)  :: NUMERIC, 2 )",}},
                        
                         {"remark",   new DataItem(){Title="商品备注", Sortable=true, CellsAlign="left", Width="150",SqlFld="sd.remark"}} 
                     },
                     QueryFromSQL=@"
			FROM sheet_invent_change_detail sd
				LEFT JOIN sheet_invent_change_main sm ON sd.sheet_id = sm.sheet_id 	AND sm.company_id =~COMPANY_ID 
				LEFT JOIN info_operator io ON io.oper_id = sm.seller_id AND io.company_id =~COMPANY_ID
				LEFT JOIN info_branch ib ON ib.branch_id = sm.branch_id AND ib.company_id =~COMPANY_ID
				LEFT JOIN info_item_prop ip ON ip.item_id = sd.item_id AND ip.company_id =~COMPANY_ID
                LEFT JOIN info_item_multi_unit itu on itu.unit_no = sd.unit_no and itu.company_id = ~COMPANY_ID and itu.item_id = sd.item_id
			where 
				 sd.company_id =~COMPANY_ID 
				AND red_flag IS NULL 
				AND approve_time IS NOT NULL
                
",
                     QueryOrderSQL=" order by approve_time desc"
                  }
                }
            };
        }

       
        public async Task OnGet()
        {
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                var columns = await Grids["gridItems"].GetAllColumns();
                columns["buy_amount"].HideOnLoad = columns["buy_amount"].Hidden = true;
                columns["cost_amount"].HideOnLoad = columns["cost_amount"].Hidden = true;
            }

        }
    }



    [Route("api/[controller]/[action]")]
    public class InventoryDetailController : QueryController
    { 
        public InventoryDetailController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            InventoryDetailModel model = new InventoryDetailModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {

            InventoryDetailModel model = new InventoryDetailModel(cmd);
            
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {

            InventoryDetailModel model = new InventoryDetailModel(cmd);
            
            return await model.ExportExcel(Request, cmd);
        }
    }
}
