﻿@page
@model ArtisanManage.Pages.CwPages.CwVoucherListsModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head id="Head1" runat="server">
    <partial name="_QueryPageHead" model="Model.PartialViewModel" />
    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';
        window.g_operRights = @Html.Raw(Model.JsonOperRightsOrig);
        
        var m_db_id = "10";
        var newCount = 1;        

    	var itemSource = {};
    	$(document).ready(function () {
            if (window.g_operRights.cwOperate) {
                if (window.g_operRights.cwOperate.sheetVoucherLists) {
                    if (window.g_operRights.cwOperate.sheetVoucherLists.operate) {
                        $('#SheetsToVoucher').attr('disabled',false);
                    }
                }
            }
            @Html.Raw(Model.m_showFormScript)
            @Html.Raw(Model.m_createGridScript)
            QueryData();
                
            $("#gridItems").on("cellclick", function (event) {
                var args = event.args;
                if (args.datafield == "sheet_no"  ) {
                        var sheet_id = args.row.bounddata.sheet_id;
                        var sheet_type = args.row.bounddata.sheet_type;
                        var sheet_no = args.row.bounddata.sheet_no;
                        var sheetType = args.row.bounddata.sheettype;
                        //window.parent.newTabPage(sheet_type, `Sheets/BuySheet?sheet_id=${sheet_id}`);
                        if (sheetType == "CG" || sheetType=="CT"){
                            window.parent.newTabPage(sheet_type, `Sheets/BuySheet?sheet_id=${sheet_id}`,window);
                        } else if (sheetType == "X" || sheetType == "T") {
                            window.parent.newTabPage(sheet_type, `Sheets/SaleSheet?sheet_id=${sheet_id}`,window);
                        } else if (sheetType == "SK") {
                            window.parent.newTabPage(sheet_type, `Sheets/GetArrearsSheet?sheet_id=${sheet_id}`,window);
                        } else if ( sheetType == "FK") {
                            window.parent.newTabPage(sheet_type, `Sheets/GetArrearsSheet?sheet_id=${sheet_id}&forPayOrGet=true`, window);
                        }else if (sheetType == "YS" ) {
                            window.parent.newTabPage(sheet_type, `Sheets/PrepaySheet?sheet_id=${sheet_id}`,window);
                        } else if (sheetType == "YF") {
                            window.parent.newTabPage(sheet_type, `Sheets/PrepaySheet?sheet_id=${sheet_id}&forPayOrGet=true`, window);
                        }else if (sheetType == "ZC" ) {
                           window.parent.newTabPage(sheet_type, `Sheets/FeeOutSheet?sheet_id=${sheet_id}&forOutOrIn=true`, window);
                        }else if (sheetType == "SR") {
                            window.parent.newTabPage(sheet_type, `Sheets/FeeOutSheet?sheet_id=${sheet_id}`, window);
                        }else if (sheetType == "BS") {
                            window.parent.newTabPage(sheet_type, `Sheets/InventChangeSheet?sheet_id=${sheet_id}&forReduce=true`, window);
                        } else if (sheetType == "YK") {
                            window.parent.newTabPage(sheet_type, `Sheets/InventChangeSheet?sheet_id=${sheet_id}`, window);
                        } else if (sheetType == "DH") {
                            window.parent.newTabPage(sheet_type, `Sheets/OrderItemSheet?sheet_id=${sheet_id}`,window);
                        } else if (sheetType == "DHTZ") {
                            window.parent.newTabPage(sheet_type, `Sheets/OrderItemAdjustSheet?sheet_id=${sheet_id}`,window);
                        } else if (sheetType == "CBTJ") {
                            window.parent.newTabPage(sheet_type, `Sheets/CostPriceAdjustSheet?sheet_id=${sheet_id}`,window);
                        } else if (sheetType == "TR") {
                            window.parent.newTabPage(sheet_type, `CwPages/CashBankTransferSheet?sheet_id=${sheet_id}`,window);
                        } else if (sheetType == "RK" || sheetType == "CK") {
                            window.parent.newTabPage(sheet_type, `Sheets/StockInOutSheet?sheet_id=${sheet_id}`, window);
                        } else if (sheetType == "FYFT") {
                            window.parent.newTabPage(sheet_type, `Sheets/FeeApportionSheet?sheet_id=${sheet_id}`, window);
                        } else if (sheetType == "DK") {
                            window.parent.newTabPage(sheet_type, `Sheets/LoanSheet?sheet_id=${sheet_id}`, window);
                        } else if (sheetType == "HDK") {
                            window.parent.newTabPage(sheet_type, `Sheets/RepaySheet?sheet_id=${sheet_id}`, window);
                        }
                }
            });
        });

        function btnSelectItems_click() {
            var rows = window.g_checkedRows
            //var rows = window.g_arrCheckedRows
            var checkedRows=[]
            for (var id in rows) {
                 var row=rows[id]
                 //checkedRows.push({item_id:row.i,item_name:row.n})
                checkedRows.push({ sheet_id:row.sheet_id, sheet_no:row.sheet_no, sheet_type:row.sheettype, oper_id:row.oper_id, happen_time:row.happen_time, make_brief:row.make_brief });
            }
            if(checkedRows.length==0){
                bw.toast("请选择单据", 3000);
                return;
            }
            let sheetsInfo = JSON.stringify(checkedRows)
            let bySheetType = false;
            if ($('#ToVoucherbySheetsType').length == 1)  bySheetType = $('#ToVoucherbySheetsType').jqxCheckBox('checked')
            let byOper = false;
            if ($('#ToVoucherbyOper').length == 1) byOper = $('#ToVoucherbyOper').jqxCheckBox('checked')

            jConfirm('确定生成凭证吗？', function () {
                $('#mask').css('display', 'block');
                //每行350毫秒
                let progressWidth=0;
                let widthEachRow = 0.98 / checkedRows.length;//进度条拉满是98%
                var timeEachRow=setInterval(function(){
                    progressWidth += widthEachRow;
                    //console.log(progressWidth);
                    if (progressWidth<0.98){
                        $('#progress_content').width(`${Math.round(progressWidth * 100)}%`);
                        $('#progress_text').text(`处理中...${Math.round(progressWidth / 0.98 * 100)}%`);
                    }else{
                        $('#progress_content').width('98%');
                        $('#progress_text').text(`处理中...100%`);
                        clearInterval(timeEachRow);
                    }
                },350);


                ajaxPost('/api/CwVoucher/ToVoucherListFromSheetsCheck', { sheetsInfo, bySheetType, byOper }).then(data => {
                    if (data.result == "OK") {
                        clearInterval(timeEachRow);
                        $('#progress_content').width('98%');
                        $('#progress_text').text(`处理中...100%`);
                        setTimeout(()=>{
                            $('#mask').css('display', 'none');
                            $('#progress_content').width('0');
                            $('#progress_text').text(`处理中...0%`);

                            bw.toast("凭证生成成功", 3000)
                            console.log("凭证生成成功");
                            QueryData()
                        },500);
                    }else{
                        $('#mask').css('display', 'none');
                        $('#progress_content').width('0');
                        $('#progress_text').text(`处理中...0%`);
                        clearInterval(timeEachRow);

                        bw.toast(data.msg, 5000)
                        console.log(data.msg);
                    }
                }).catch(err => { 
                    clearInterval(timeEachRow);
                    $('#mask').css('display', 'none');
                    $('#progress_content').width('0');
                    $('#progress_text').text(`处理中...0%`);
                    console.log(err) ;
                    bw.toast('网络超时', 3000);
                    console.log('网络超时');
                })

            }, "");


            
        }

        window.getLeftTopAreaSize = function () {
            let otherWidth = $('#sheetsTypes').outerWidth();
            return [otherWidth, 0];
        };
        
    </script>
    <style>
        ::-webkit-scrollbar-corner{
            background-color:#fff;
        }

        #div_supcust_id{
            margin-left:20px;
        }

        #mask {
            position: fixed; /*设置为固定定位*/
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255,0.8); /*使用半透明的白色作为背景色*/
            z-index: 9999; /*设置遮罩层的层级*/
            display: none;
            align-items: center;
            justify-content: center;
            color: dimgrey;
            font-size: 18px;
        }

        #progress{
            width:220px;
            height:70px;
            transform:translate(calc(50vw - 50%), calc(50vh - 50%));
        }

        #progress_border{
            width:200px;
            height:30px;
            background-color:#fff;
            border: 2px solid FireBrick;
            display:inline-block;
        }

        #progress_content{
            width:0;
            height:88%;
            margin:2px 2px 2px 2px;
            background-color: FireBrick;
        }

        #progress_text{
            width: 200px;
            height: 30px;
            display: inline-block;
            color: FireBrick;
        }
    </style>
</head>

<body>

    <div style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 200px);">
            <div style="float:none;height:0px; clear:both;"></div>
        </div>

        <button onclick="QueryData()" style="margin:5px 10px auto 10px;">查询</button>
        <button id="SheetsToVoucher" style="margin:5px 20px auto 10px;width:85px" onclick="btnSelectItems_click()" disabled="disabled">生成凭证</button>
        <button id="btnExport" style="margin:5px 20px auto 0;" onclick="ExportExcel()">导出</button>

    </div>
    <div style="flex-grow:1;display:flex;width:100%;height:calc(100% - 50px) !important;">
        <!--左侧行程-->
        <div id='sheetsTypes' style="width:250px;height:calc(100% - 30px);margin-top:20px;margin-bottom:2px;overflow:scroll">
        </div>
        <!--右侧主表格-->
        <div style="width:calc(100% - 200px);height:100%; margin-left:10px;">
            <!--上部 统计行-->
            <div><div style="float:right;margin-right:50px;height:20px;font-size:12px;color:#999;">共<label id="rows_count">0</label>行</div></div>
            <!--表格部分-->
            <div id="gridItems" style="margin-top:0px;margin-left:10px; margin-bottom:2px;width:calc(100% - 20px);height:calc(100% - 30px);"></div>
        </div>
    </div>

    <div id="mask">
        <div id="progress">
            <div id="progress_border">
                <div id="progress_content"></div>
            </div>
            <div id="progress_text">处理中...</div>
        </div>
    </div>

</body>
</html>