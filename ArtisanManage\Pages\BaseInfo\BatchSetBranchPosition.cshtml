﻿@page
@model BatchSetBranchPositionModel
@{
    //ViewData["Title"] = "Error";
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <link href="~/css/flex.css" rel="stylesheet" />
    <link href="~/jqwidgets/jqwidgets/jqx-all.js" />
    <script src="~/js/jquery.min.js"></script>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <style>
        #container{
            font-size:16px;
        }

        .cell-branch{
            padding:10px;
            border-bottom:1px solid #eee;
        }
        .row-item{
            width:100%;
            display:flex;
            flex-direction:row;
            justify-content:flex-start;
            align-items:center;
        }

        .branch-name {
            padding: 10px;
            width:100px;
        }
        .start-code{
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
        }

        .serial-mod{
            width: 100%;
            display: flex;
            flex-wrap:wrap;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
        }
        .cell-serial{
            margin-right:10px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div v-for="(bItem,index) in branchArr" :key="bItem.id" class="cell-branch">
            <div class="row-item">
                <div class="branch-name">{{bItem.branch_name}}</div>
                <div class="start-code">
                    <div style="width:80px">开始编号:</div>
                    <el-input style="width:220px" v-model="startCode" placeholder="请使用-分割，不要出现中文" @@blur="handleStartCodeBlur"> </el-input>
                </div>
            </div>
            <div class="" v-if="showList">共检测到{{serialArr.length}}个递增序列，请分别输入每个递增序列的最大值</div>
            <div class="serial-mod" v-if="showList">
                <div v-for="(item,index) in serialArr" :key="index" class="cell-serial">
                    <el-input style="width:220px" v-model="item.endCode" :placeholder="'开始序列为'+item.startCode"></el-input>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        // 注册消息事件监听，接受子元素给的数据
        var app = new Vue({
            el: '#container',
            data() {
                return {
                    branchArr: [],
                    startCode:'',
                    serialArr:[],
                    showList:false
                }
            },
            methods: {
                handleStartCodeBlur() {
                    let inputStartCode = this.startCode.replace(' ', '')
                    if (inputStartCode.length){
                        const parts = this.startCode.split('-');
                        if (parts.length) {
                            this.showList = true
                            parts.forEach(p => {
                                this.serialArr.push({
                                    startCode: p,
                                    endCode: ''
                                })
                            })
                        }
                    }
                    
                }
            },
            mounted(){
                window.addEventListener('message', (e) => {
                    let branchData = JSON.parse(e.data)
                    console.log(branchData)
                    branchData.forEach(e=>{
                        this.branchArr.push(e)
                    })
                }, true);
            }
        });
    </script>
</body>
</html>